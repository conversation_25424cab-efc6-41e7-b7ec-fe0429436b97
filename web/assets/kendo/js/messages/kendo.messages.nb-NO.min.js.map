{"version": 3, "sources": ["messages/kendo.messages.nb-NO.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "enums", "FilterMenu", "ColumnMenu", "messages", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "clear", "filter", "isFalse", "isTrue", "operator", "and", "info", "title", "or", "selectValue", "cancel", "value", "FilterMultiCheck", "search", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "confirmation", "cancelDelete", "confirmDelete", "Groupable", "empty", "Pager", "allPages", "display", "itemsPerPage", "next", "page", "previous", "refresh", "morePages", "TreeListPager", "Upload", "localization", "retry", "remove", "uploadSelectedFiles", "dropFilesHere", "statusFailed", "statusUploaded", "statusUploading", "headerStatusUploaded", "headerStatusUploading", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "deleteFile", "directoryNotFound", "emptyFolder", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "backColor", "foreColor", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "imageWebAddress", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "formatting", "viewHtml", "dialogUpdate", "insertFile", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "showFullDay", "showWorkDay", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACTF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACjDE,MACIC,GAAM,aACNC,GAAM,gBACNC,IAAO,0BACPC,GAAM,mBACNC,IAAO,6BACPC,IAAO,mBAEXC,QACIN,GAAM,aACNC,GAAM,gBACNC,IAAO,0BACPC,GAAM,gBACNC,IAAO,0BACPC,IAAO,mBAEXE,QACIC,SAAY,aACZC,eAAkB,kBAClBC,SAAY,cACZV,GAAM,aACNK,IAAO,kBACPM,WAAc,eAElBC,OACIZ,GAAM,aACNK,IAAO,sBAOfb,MAAMC,GAAGoB,aACTrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACjDE,MACIC,GAAM,aACNC,GAAM,gBACNC,IAAO,0BACPC,GAAM,mBACNC,IAAO,4BACPC,IAAO,mBAEXC,QACIN,GAAM,aACNC,GAAM,gBACNC,IAAO,0BACPC,GAAM,gBACNC,IAAO,0BACPC,IAAO,mBAEXE,QACIC,SAAY,aACZC,eAAkB,kBAClBC,SAAY,cACZV,GAAM,aACNK,IAAO,kBACPM,WAAc,eAElBC,OACIZ,GAAM,aACNK,IAAO,sBAOfb,MAAMC,GAAGqB,aACTtB,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,UACjDC,QAAW,WACXC,cAAiB,mBACjBC,eAAkB,mBAClBC,SAAY,sBACZC,KAAQ,OACRC,KAAQ,MACRC,OAAU,aAMd9B,MAAMC,GAAG8B,mBACT/B,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,UACvDS,OACIC,SAAY,UACZC,YAAe,gBAEnBC,KACIC,MAAS,QACTC,WAAc,gBACdC,MAAS,SACTC,MAAS,QACTC,GAAM,KACNC,YAAe,WAEnBC,aACIV,MAAS,SACTW,QAAW,WACXJ,MAAS,QACTK,OAAU,WACVC,OAAU,SAEdF,SACIG,IAAO,MACPb,SAAY,YACZC,YAAe,eACfa,SAAY,cAEhBC,iBACIC,MAAS,SACTC,OAAU,SACVC,KAAQ,QACRC,OAAU,QACVC,MAAS,UAEbT,QACIV,YAAe,eACfa,SAAY,aACZd,SAAY,UAEhBY,QACIS,GAAM,KACNpB,YAAe,eACfa,SAAY,aACZd,SAAY,MAEhBsB,UACIT,IAAO,MACPU,QAAW,SACXC,QAAW,eAOnBzD,MAAMC,GAAGC,aACTF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACjDmC,MAAS,QACTC,OAAU,WACVC,QAAW,WACXC,OAAU,UACVC,SAAY,cAMhB9D,MAAMC,GAAGoB,aACTrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACjDwC,IAAO,KACPL,MAAS,QACTC,OAAU,WACVK,KAAQ,wBACRC,MAAS,uBACTL,QAAW,WACXC,OAAU,UACVK,GAAM,QACNC,YAAe,SACfC,OAAU,SACVN,SAAY,WACZO,MAAS,WAMbrE,MAAMC,GAAGqE,mBACbtE,MAAMC,GAAGqE,iBAAiBnE,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqE,iBAAiBnE,UAAUC,QAAQmB,UACzDgD,OAAU,SAMRvE,MAAMC,GAAGuE,OACTxE,MAAMC,GAAGuE,KAAKrE,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuE,KAAKrE,UAAUC,QAAQmB,UAC3CkD,UACIC,WAAc,SACdN,OAAU,mBACVO,OAAU,gBACVC,QAAW,QACXC,KAAQ,QACRC,MAAS,kBACTC,IAAO,gBACPC,KAAQ,QACRC,OAAU,OACVC,OAAU,YAEdC,UACIC,aAAgB,iDAChBC,aAAgB,SAChBC,cAAiB,YAOzBtF,MAAMC,GAAGsF,YACTvF,MAAMC,GAAGsF,UAAUpF,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsF,UAAUpF,UAAUC,QAAQmB,UAChDiE,MAAS,sDAMbxF,MAAMC,GAAGwF,QACTzF,MAAMC,GAAGwF,MAAMtF,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwF,MAAMtF,UAAUC,QAAQmB,UAC5CmE,SAAY,OACZC,QAAW,0BACXH,MAAS,0BACTvC,MAAS,qBACT2C,aAAgB,kBAChBzC,KAAQ,qBACR0C,KAAQ,oBACRvC,GAAM,SACNwC,KAAQ,OACRC,SAAY,sBACZC,QAAW,YACXC,UAAa,iBAMjBjG,MAAMC,GAAGiG,gBACTlG,MAAMC,GAAGiG,cAAc/F,UAAUC,QAAQmB,SACzCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiG,cAAc/F,UAAUC,QAAQmB,UACpDmE,SAAY,OACZC,QAAW,0BACXH,MAAS,0BACTvC,MAAS,qBACT2C,aAAgB,kBAChBzC,KAAQ,qBACR0C,KAAQ,oBACRvC,GAAM,SACNwC,KAAQ,OACRC,SAAY,sBACZC,QAAW,YACXC,UAAa,iBAMjBjG,MAAMC,GAAGkG,SACTnG,MAAMC,GAAGkG,OAAOhG,UAAUC,QAAQgG,aAClCtG,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkG,OAAOhG,UAAUC,QAAQgG,cAC7ChC,OAAU,SACViC,MAAS,eACTpB,OAAU,UACVqB,OAAU,QACVC,oBAAuB,iBACvBC,cAAiB,kCACjBC,aAAgB,aAChBC,eAAkB,YAClBC,gBAAmB,aACnBC,qBAAwB,SACxBC,sBAAyB,mBAM7B7G,MAAMC,GAAG6G,SACT9G,MAAMC,GAAG6G,OAAO3G,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6G,OAAO3G,UAAUC,QAAQmB,UAC7CwF,KAAQ,UACRC,WAAc,gBACdC,SAAY,gBACZC,gBAAmB,mBACnBC,SAAY,iBACZC,gBAAmB,oBACnBC,YAAe,cACfC,OAAU,iBACVC,WAAc,gBACdC,YAAe,iBACfC,kBAAqB,0BACrBC,oBAAuB,sBACvBC,OAAU,SACVC,cAAiB,iBACjBC,YAAe,wBACfC,YAAe,uBACfC,aAAgB,qBAChBC,QAAW,oBACXC,cAAiB,iBACjBC,OAAU,OACVC,UAAa,YACbC,YAAe,UACfC,UAAa,eACbC,OAAU,cACVC,WAAc,0CACdC,kBAAqB,yCACrBC,YAAe,YACfC,gBAAmB,uDACnBC,QAAW,cACXC,YAAe,OACfC,YAAe,YACfC,cAAiB,mFACjBC,WAAc,WACdC,UAAa,iBACbC,UAAa,QACbzC,cAAiB,kCACjB0C,sBAAyB,QACzBC,aAAgB,SAChBC,aAAgB,WAChBC,aAAgB,mBAChBC,gBAAmB,cACnBC,oBAAuB,0BACvBC,SAAY,QACZC,YAAe,aACfC,eAAkB,cAClBnF,OAAU,MACVoF,YAAe,aACfC,cAAiB,8BACjBC,eAAkB,4BAClBC,YAAe,oBACfC,YAAe,qBACfC,aAAgB,gBAChBC,UAAa,YACbC,WAAc,SACdC,SAAY,WACZC,aAAgB,WAChBC,WAAc,kBAMlBrK,MAAMC,GAAGqK,YACTtK,MAAMC,GAAGqK,UAAUnK,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqK,UAAUnK,UAAUC,QAAQmB,UAChDgJ,OAAU,aACVnG,OAAU,SACVe,UACIC,aAAgB,oDAEpB7E,KAAQ,OACRqE,QAAW,QACX4F,QACIC,YAAe,kBACfC,YAAe,cACfC,YAAe,UACfxI,IAAO,QACPyI,YAAe,iBACfC,OAAU,SACVC,kBAAqB,4CACrBC,MAAS,QACTC,cAAiB,iBACjBC,SAAY,WACZC,qBAAwB,WACxBC,oBAAuB,YACvBlH,MAAS,SACTmH,WAAc,kBAElBC,MAAS,UACTC,oBACIC,gBAAmB,iFACnBC,uBAA0B,0BAC1BC,mBAAsB,yBACtBC,kBAAqB,8BACrBC,cAAiB,mFACjBC,qBAAwB,4BACxBC,iBAAoB,2BACpBC,gBAAmB,+BAEvB9G,KAAQ,QACR+G,KAAQ,MACRC,MAAS,QACTC,OACIC,OAAU,SACVpJ,IAAO,MACPqJ,MAAS,QACTC,KAAQ,MACRC,SAAY,cAEhBX,kBAAqB,gBACrBY,YAAe,eACfC,YAAe,sBAMnBvM,MAAMC,GAAGuM,SACbxM,MAAMC,GAAGuM,OAAOrM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuM,OAAOrM,UAAUC,QAAQgG,cAC/CqG,MAAS,WAMPzM,MAAMC,GAAGyM,QACb1M,MAAMC,GAAGyM,MAAMvM,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyM,MAAMvM,UAAUC,QAAQgG,cAC9CuG,OAAU,QAMR3M,MAAMC,GAAG2M,UACb5M,MAAMC,GAAG2M,QAAQzM,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,QAAQzM,UAAUC,QAAQgG,cAChDuG,OAAU,KACVvI,OAAU,YAKRpE,MAAMC,GAAG4M,SACb7M,MAAMC,GAAG4M,OAAO1M,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4M,OAAO1M,UAAUC,QAAQgG,cAC/CuG,OAAU,KACVvI,OAAU,aAIT0I,OAAO9M,MAAM+M", "file": "kendo.messages.nb-NO.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\n    kendo.ui.FilterCell.prototype.options.operators =\n    $.extend(true, kendo.ui.FilterCell.prototype.options.operators, {\n        \"date\": {\n            \"eq\": \"Er lik med\",\n            \"gt\": \"Er senere enn\",\n            \"gte\": \"Er lik eller senere enn\",\n            \"lt\": \"Er tidligere enn\",\n            \"lte\": \"Er lik eller tidligere enn\",\n            \"neq\": \"Er ikke lik med\"\n        },\n        \"number\": {\n            \"eq\": \"Er lik med\",\n            \"gt\": \"Er større enn\",\n            \"gte\": \"Er lik eller større enn\",\n            \"lt\": \"Er mindre enn\",\n            \"lte\": \"Er lik eller mindre enn\",\n            \"neq\": \"Er ikke lik med\"\n        },\n        \"string\": {\n            \"contains\": \"Inneholder\",\n            \"doesnotcontain\": \"Inneholder ikke\",\n            \"endswith\": \"Slutter med\",\n            \"eq\": \"Er lik med\",\n            \"neq\": \"Er ikke lik med\",\n            \"startswith\": \"Starter med\"\n        },\n        \"enums\": {\n            \"eq\": \"Er lik med\",\n            \"neq\": \"Er ikke lik med\"\n        }\n    });\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\n    kendo.ui.FilterMenu.prototype.options.operators =\n    $.extend(true, kendo.ui.FilterMenu.prototype.options.operators, {\n        \"date\": {\n            \"eq\": \"Er lik med\",\n            \"gt\": \"Er senere enn\",\n            \"gte\": \"Er lik eller senere enn\",\n            \"lt\": \"Er tidligere enn\",\n            \"lte\": \"Er lik eller tidigere enn\",\n            \"neq\": \"Er ikke lik med\"\n        },\n        \"number\": {\n            \"eq\": \"Er lik med\",\n            \"gt\": \"Er større enn\",\n            \"gte\": \"Er lik eller større enn\",\n            \"lt\": \"Er mindre enn\",\n            \"lte\": \"Er lik eller mindre enn\",\n            \"neq\": \"Er ikke lik med\"\n        },\n        \"string\": {\n            \"contains\": \"Inneholder\",\n            \"doesnotcontain\": \"Inneholder ikke\",\n            \"endswith\": \"Slutter med\",\n            \"eq\": \"Er lik med\",\n            \"neq\": \"Er ikke lik med\",\n            \"startswith\": \"Starter med\"\n        },\n        \"enums\": {\n            \"eq\": \"Er lik med\",\n            \"neq\": \"Er ikke lik med\"\n        }\n    });\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\n    kendo.ui.ColumnMenu.prototype.options.messages =\n    $.extend(true, kendo.ui.ColumnMenu.prototype.options.messages, {\n        \"columns\": \"Kolonner\",\n        \"sortAscending\": \"Sortere fallende\",\n        \"sortDescending\": \"Sortere stigende\",\n        \"settings\": \"Kolonneinstillinger\",\n        \"done\": \"Klar\",\n        \"lock\": \"Lås\",\n        \"unlock\": \"Lås opp\"\n    });\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\n    kendo.ui.RecurrenceEditor.prototype.options.messages =\n    $.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages, {\n        \"daily\": {\n            \"interval\": \"dag(er)\",\n            \"repeatEvery\": \"Gjenta hver:\"\n        },\n        \"end\": {\n            \"after\": \"Etter\",\n            \"occurrence\": \"forekomst(er)\",\n            \"label\": \"Slutt:\",\n            \"never\": \"Aldri\",\n            \"on\": \"På\",\n            \"mobileLabel\": \"Slutter\"\n        },\n        \"frequencies\": {\n            \"daily\": \"Daglig\",\n            \"monthly\": \"Månedlig\",\n            \"never\": \"Aldri\",\n            \"weekly\": \"Ukentlig\",\n            \"yearly\": \"Årlig\"\n        },\n        \"monthly\": {\n            \"day\": \"Dag\",\n            \"interval\": \"måned(er)\",\n            \"repeatEvery\": \"Gjenta hver:\",\n            \"repeatOn\": \"Gjenta på:\"\n        },\n        \"offsetPositions\": {\n            \"first\": \"første\",\n            \"fourth\": \"fjerde\",\n            \"last\": \"siste\",\n            \"second\": \"andre\",\n            \"third\": \"tredje\"\n        },\n        \"weekly\": {\n            \"repeatEvery\": \"Gjenta hver:\",\n            \"repeatOn\": \"Gjenta på:\",\n            \"interval\": \"uke(r)\"\n        },\n        \"yearly\": {\n            \"of\": \"av\",\n            \"repeatEvery\": \"Gjenta hver:\",\n            \"repeatOn\": \"Gjenta på:\",\n            \"interval\": \"år\"\n        },\n        \"weekdays\": {\n            \"day\": \"dag\",\n            \"weekday\": \"ukedag\",\n            \"weekend\": \"helgedag\"\n        }\n    });\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\n    kendo.ui.FilterCell.prototype.options.messages =\n    $.extend(true, kendo.ui.FilterCell.prototype.options.messages, {\n        \"clear\": \"Fjern\",\n        \"filter\": \"Filtrere\",\n        \"isFalse\": \"Er usann\",\n        \"isTrue\": \"Er sant\",\n        \"operator\": \"Operator\"\n    });\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\n    kendo.ui.FilterMenu.prototype.options.messages =\n    $.extend(true, kendo.ui.FilterMenu.prototype.options.messages, {\n        \"and\": \"Og\",\n        \"clear\": \"Fjern\",\n        \"filter\": \"Filtrere\",\n        \"info\": \"Vis poster med verdi:\",\n        \"title\": \"Vis poster med verdi\",\n        \"isFalse\": \"Er usann\",\n        \"isTrue\": \"Er sant\",\n        \"or\": \"Eller\",\n        \"selectValue\": \"-Velg-\",\n        \"cancel\": \"Avbryt\",\n        \"operator\": \"Operator\",\n        \"value\": \"Verdi\"\n    });\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"Søk\"\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\n    kendo.ui.Grid.prototype.options.messages =\n    $.extend(true, kendo.ui.Grid.prototype.options.messages, {\n        \"commands\": {\n            \"canceledit\": \"Avbryt\",\n            \"cancel\": \"Avbryt endringer\",\n            \"create\": \"Legg til post\",\n            \"destroy\": \"Slett\",\n            \"edit\": \"Endre\",\n            \"excel\": \"Export to Excel\",\n            \"pdf\": \"Export to PDF\",\n            \"save\": \"Lagre\",\n            \"select\": \"Velg\",\n            \"update\": \"Oppdater\"\n        },\n        \"editable\": {\n            \"confirmation\": \"Er du sikker på at du vil slette denna posten?\",\n            \"cancelDelete\": \"Avbryt\",\n            \"confirmDelete\": \"Slett\"\n        }\n    });\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\n    kendo.ui.Groupable.prototype.options.messages =\n    $.extend(true, kendo.ui.Groupable.prototype.options.messages, {\n        \"empty\": \"Dra en kolonne hit for å sortere på den kolonnen\"\n    });\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\n    kendo.ui.Pager.prototype.options.messages =\n    $.extend(true, kendo.ui.Pager.prototype.options.messages, {\n        \"allPages\": \"Alle\",\n        \"display\": \"{0} - {1} av {2} poster\",\n        \"empty\": \"Det finnes ingen poster\",\n        \"first\": \"Gå til første side\",\n        \"itemsPerPage\": \"poster per side\",\n        \"last\": \"Gå til siste siden\",\n        \"next\": \"Gå til neste side\",\n        \"of\": \"av {0}\",\n        \"page\": \"Side\",\n        \"previous\": \"Gå til forrige side\",\n        \"refresh\": \"Oppdatere\",\n        \"morePages\": \"Flere sider\"\n    });\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\n    kendo.ui.TreeListPager.prototype.options.messages =\n    $.extend(true, kendo.ui.TreeListPager.prototype.options.messages, {\n        \"allPages\": \"Alle\",\n        \"display\": \"{0} - {1} av {2} poster\",\n        \"empty\": \"Det finnes ingen poster\",\n        \"first\": \"Gå til første side\",\n        \"itemsPerPage\": \"poster per side\",\n        \"last\": \"Gå til siste siden\",\n        \"next\": \"Gå til neste side\",\n        \"of\": \"av {0}\",\n        \"page\": \"Side\",\n        \"previous\": \"Gå til forrige side\",\n        \"refresh\": \"Oppdatere\",\n        \"morePages\": \"Flere sider\"\n    });\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\n    kendo.ui.Upload.prototype.options.localization =\n    $.extend(true, kendo.ui.Upload.prototype.options.localization, {\n        \"cancel\": \"Avbryt\",\n        \"retry\": \"Forsøk igjen\",\n        \"select\": \"Velg...\",\n        \"remove\": \"Fjern\",\n        \"uploadSelectedFiles\": \"Last opp filer\",\n        \"dropFilesHere\": \"slipp filer her for å laste opp\",\n        \"statusFailed\": \"misslyktes\",\n        \"statusUploaded\": \"opplastet\",\n        \"statusUploading\": \"laster opp\",\n        \"headerStatusUploaded\": \"Ferdig\",\n        \"headerStatusUploading\": \"Laster opp...\"\n    });\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\n    kendo.ui.Editor.prototype.options.messages =\n    $.extend(true, kendo.ui.Editor.prototype.options.messages, {\n        \"bold\": \"Uthevet\",\n        \"createLink\": \"Legg til link\",\n        \"fontName\": \"Velg fontnavn\",\n        \"fontNameInherit\": \"(arvet fontnavn)\",\n        \"fontSize\": \"Velg størrelse\",\n        \"fontSizeInherit\": \"(arvet størrelse)\",\n        \"formatBlock\": \"Formatering\",\n        \"indent\": \"Øk indentasjon\",\n        \"insertHtml\": \"Legg til HTML\",\n        \"insertImage\": \"Legg til bilde\",\n        \"insertOrderedList\": \"Legg til numerert liste\",\n        \"insertUnorderedList\": \"Legg til punktliste\",\n        \"italic\": \"Kursiv\",\n        \"justifyCenter\": \"Sentrert tekst\",\n        \"justifyFull\": \"Marginaljustert tekst\",\n        \"justifyLeft\": \"Venstrejustert tekst\",\n        \"justifyRight\": \"Høyrejustert tekst\",\n        \"outdent\": \"Minsk indentasjon\",\n        \"strikethrough\": \"Gjennomstreket\",\n        \"styles\": \"Stil\",\n        \"subscript\": \"Nedsenket\",\n        \"superscript\": \"Opphøyd\",\n        \"underline\": \"Understreket\",\n        \"unlink\": \"Fjern lenke\",\n        \"deleteFile\": \"Er du sikker på at du vil slette \\\"{0}\\\"?\",\n        \"directoryNotFound\": \"En mappe med dette navnet finnes ikke.\",\n        \"emptyFolder\": \"Tom mappe\",\n        \"invalidFileType\": \"Filen \\\"{0}\\\" er ikke gyldig. Tilatte filtyper er {1}.\",\n        \"orderBy\": \"Sortere på:\",\n        \"orderByName\": \"Navn\",\n        \"orderBySize\": \"Størrelse\",\n        \"overwriteFile\": \"'En fil med navn \\\"{0}\\\" finnes allerede i aktuell mappe. Vil du skrive over den?\",\n        \"uploadFile\": \"Last opp\",\n        \"backColor\": \"Bakgrunnsfarge\",\n        \"foreColor\": \"Farge\",\n        \"dropFilesHere\": \"slipp filer her for å laste opp\",\n        \"dialogButtonSeparator\": \"eller\",\n        \"dialogCancel\": \"Avbryt\",\n        \"dialogInsert\": \"Legg til\",\n        \"imageAltText\": \"Alternativ tekst\",\n        \"imageWebAddress\": \"Webbadresse\",\n        \"linkOpenInNewWindow\": \"Åpne lenke i nytt vindu\",\n        \"linkText\": \"Tekst\",\n        \"linkToolTip\": \"Skjermtips\",\n        \"linkWebAddress\": \"Webbadresse\",\n        \"search\": \"Søk\",\n        \"createTable\": \"Lag tabell\",\n        \"addColumnLeft\": \"Legg til kolonne på venstre\",\n        \"addColumnRight\": \"Legg til kolonne på høyre\",\n        \"addRowAbove\": \"Legg til rad over\",\n        \"addRowBelow\": \"Legg til rad under\",\n        \"deleteColumn\": \"Slett kolonne\",\n        \"deleteRow\": \"Slett rad\",\n        \"formatting\": \"Format\",\n        \"viewHtml\": \"Vis HTML\",\n        \"dialogUpdate\": \"Oppdater\",\n        \"insertFile\": \"Sett inn fil\"\n    });\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\n    kendo.ui.Scheduler.prototype.options.messages =\n    $.extend(true, kendo.ui.Scheduler.prototype.options.messages, {\n        \"allDay\": \"hele dagen\",\n        \"cancel\": \"Avbryt\",\n        \"editable\": {\n            \"confirmation\": \"Er du sikker på at du vil slette denne oppgaven?\"\n        },\n        \"date\": \"Dato\",\n        \"destroy\": \"Slett\",\n        \"editor\": {\n            \"allDayEvent\": \"Heldags oppgave\",\n            \"description\": \"Beskrivelse\",\n            \"editorTitle\": \"Oppgave\",\n            \"end\": \"Slutt\",\n            \"endTimezone\": \"Slutt tidssone\",\n            \"repeat\": \"Gjenta\",\n            \"separateTimezones\": \"Bruk forskjellig start og slutt tidssoner\",\n            \"start\": \"Start\",\n            \"startTimezone\": \"Start tidssone\",\n            \"timezone\": \"tidssone\",\n            \"timezoneEditorButton\": \"Tidssone\",\n            \"timezoneEditorTitle\": \"Tidssoner\",\n            \"title\": \"Tittel\",\n            \"noTimezone\": \"Ingen tidssone\"\n        },\n        \"event\": \"Oppgave\",\n        \"recurrenceMessages\": {\n            \"deleteRecurring\": \"Vil du slette bare denne forekomsten eller alle forekomster av denne oppgaven?\",\n            \"deleteWindowOccurrence\": \"Slett denne forekomsten\",\n            \"deleteWindowSeries\": \"Slett alle forekomster\",\n            \"deleteWindowTitle\": \"Slett gjentagende forekomst\",\n            \"editRecurring\": \"Vil du redigere bare denne forekomsten eller alle forekomster av denne oppgaven?\",\n            \"editWindowOccurrence\": \"Rediger denne forekomsten\",\n            \"editWindowSeries\": \"Rediger alle forekomster\",\n            \"editWindowTitle\": \"Rediger gjentagende oppgave\"\n        },\n        \"save\": \"Lagre\",\n        \"time\": \"Tid\",\n        \"today\": \"I dag\",\n        \"views\": {\n            \"agenda\": \"Agenda\",\n            \"day\": \"Dag\",\n            \"month\": \"Måned\",\n            \"week\": \"Uke\",\n            \"workWeek\": \"Arbeidsuke\"\n        },\n        \"deleteWindowTitle\": \"Slett oppgave\",\n        \"showFullDay\": \"Vis full dag\",\n        \"showWorkDay\": \"Vis arbeidstimer\"\n    });\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Lukke\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Avbryt\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Avbryt\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}