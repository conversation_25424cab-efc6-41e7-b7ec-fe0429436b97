{"version": 3, "sources": ["messages/kendo.messages.es-ES.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "Groupable", "empty", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "noTimezone", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "retry", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "text", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "toolbar", "alignment", "alignmentButtons", "backgroundColor", "borders", "colorPicker", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "TreeList", "noRows", "loading", "requestFailed", "createchild", "TreeView", "Upload", "localization", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "Dialog", "close", "<PERSON><PERSON>", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGRC,MAAMC,GAAGC,kBACXF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,UACTC,OAAU,cAMZR,MAAMC,GAAGQ,cACXT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,UACTC,OAAU,cAMZR,MAAMC,GAAGS,aACXV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,mBACjBC,eAAkB,oBAClBC,OAAU,UACVC,QAAW,WACXC,KAAQ,QACRC,SAAY,4BACZC,KAAQ,WACRC,OAAU,iBAMZlB,MAAMC,GAAGkB,SACXnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,UACRC,OAAU,UACVC,UAAa,YACbC,cAAiB,UACjBC,YAAe,cACfC,UAAa,YACbC,cAAiB,iBACjBC,YAAe,+BACfC,aAAgB,6BAChBC,YAAe,aACfC,oBAAuB,6BACvBC,kBAAqB,0BACrBC,OAAU,gBACVC,QAAW,iBACXC,WAAc,gBACdC,OAAU,kBACVC,YAAe,kBACfC,WAAc,mBACdC,WAAc,gBACdC,SAAY,WACZC,SAAY,iCACZC,gBAAmB,oBACnBC,SAAY,+BACZC,gBAAmB,oBACnBC,YAAe,UACfC,WAAc,UACdC,UAAa,QACbC,UAAa,iBACbC,MAAS,UACTC,YAAe,gBACfC,WAAc,QACdC,QAAW,iBACXC,YAAe,SACfC,YAAe,SACfC,gBAAmB,wFACnBC,WAAc,0CACdC,cAAiB,yFACjBC,kBAAqB,mDACrBC,gBAAmB,gBACnBC,aAAgB,oBAChBC,WAAc,aACdC,YAAe,YACfC,eAAkB,gBAClBC,UAAa,SACbC,eAAkB,gBAClBC,SAAY,QACZC,YAAe,UACfC,oBAAuB,gCACvBC,aAAgB,aAChBC,aAAgB,WAChBC,sBAAyB,IACzBC,aAAgB,WAChBC,YAAe,cACfC,cAAiB,iCACjBC,eAAkB,+BAClBC,YAAe,sBACfC,YAAe,qBACfC,UAAa,cACbC,aAAgB,oBAMlB9E,MAAMC,GAAG8E,cACX/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpD6C,WAAc,gBACdC,QAAW,cACXE,YAAe,SACfD,YAAe,SACfK,kBAAqB,mDACrBR,YAAe,gBACfM,WAAc,0CACdD,gBAAmB,wFACnBE,cAAiB,yFACjBwB,cAAiB,sCACjBC,OAAU,YAMZjF,MAAMC,GAAGiF,aACXlF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACnD8E,OAAU,KACVC,QAAW,KACXvE,OAAU,UACVwE,MAAS,iBACTC,SAAY,cAMdtF,MAAMC,GAAGiF,aACXlF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,UACpCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,aACNC,IAAO,gBACPC,WAAc,eACdC,SAAY,WACZC,eAAkB,cAClBC,SAAY,aACZC,OAAU,UACVC,UAAa,aACbC,QAAW,aACXC,WAAc,iBAEhBC,QACEV,GAAM,aACNC,IAAO,gBACPU,IAAO,uBACPC,GAAM,eACNC,IAAO,uBACPC,GAAM,eACNR,OAAU,UACVC,UAAa,cAEfQ,MACEf,GAAM,aACNC,IAAO,gBACPU,IAAO,yBACPC,GAAM,eACNC,IAAO,wBACPC,GAAM,cACNR,OAAU,UACVC,UAAa,cAEfS,OACEhB,GAAM,aACNC,IAAO,gBACPK,OAAU,UACVC,UAAa,iBAOjBhG,MAAMC,GAAGyG,aACX1G,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQC,UACnDsG,KAAQ,+BACRC,MAAS,8BACTzB,OAAU,KACVC,QAAW,KACXvE,OAAU,UACVwE,MAAS,kBACTwB,IAAO,IACPC,GAAM,IACNC,YAAe,uBACfzB,SAAY,WACZ0B,MAAS,QACTxG,OAAU,cAMZR,MAAMC,GAAGyG,aACX1G,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQmF,UACpCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,aACNC,IAAO,gBACPC,WAAc,eACdC,SAAY,WACZC,eAAkB,cAClBC,SAAY,aACZC,OAAU,UACVC,UAAa,aACbC,QAAW,aACXC,WAAc,iBAEhBC,QACEV,GAAM,aACNC,IAAO,gBACPU,IAAO,uBACPC,GAAM,eACNC,IAAO,uBACPC,GAAM,eACNR,OAAU,UACVC,UAAa,cAEfQ,MACEf,GAAM,aACNC,IAAO,iBACPU,IAAO,yBACPC,GAAM,eACNC,IAAO,wBACPC,GAAM,cACNR,OAAU,UACVC,UAAa,cAEfS,OACEhB,GAAM,aACNC,IAAO,gBACPK,OAAU,UACVC,UAAa,iBAOjBhG,MAAMC,GAAGgH,mBACXjH,MAAMC,GAAGgH,iBAAiB9G,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgH,iBAAiB9G,UAAUC,QAAQC,UACzD6G,SAAY,mBACZ7B,MAAS,kBACTxE,OAAU,UACVoE,OAAU,YAMZjF,MAAMC,GAAGkH,QACXnH,MAAMC,GAAGkH,MAAMhH,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkH,MAAMhH,UAAUC,QAAQC,UAC9C+G,SACEC,SAAY,oBACZC,OAAU,gBACVC,YAAe,iBACfC,aAAgB,kBAChBC,IAAO,kBAETjH,OAAU,WACVkH,4BAA+B,qBAC/BC,sBAAyB,eACzBC,QAAW,SACXC,QACEC,aAAgB,UAChBC,YAAe,QACfC,IAAO,MACPC,gBAAmB,WACnBC,UAAa,WACbC,qBAAwB,WACxBC,gBAAmB,WACnBC,MAAS,WACTzB,MAAS,SACT0B,YAAe,YAEjBC,KAAQ,UACRC,OACEC,IAAO,MACPT,IAAO,MACPU,MAAS,MACTL,MAAS,WACTM,KAAQ,SACRC,KAAQ,UAOZ5I,MAAMC,GAAG4I,OACX7I,MAAMC,GAAG4I,KAAK1I,UAAUC,QAAQC,SAC9BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4I,KAAK1I,UAAUC,QAAQC,UAC7CyI,UACEtI,OAAU,mBACVuI,WAAc,WACdC,OAAU,UACVpB,QAAW,WACXqB,KAAQ,SACRC,MAAS,mBACTzB,IAAO,iBACPc,KAAQ,kBACRY,OAAU,cACVC,OAAU,cAEZC,UACEC,aAAgB,WAChBC,aAAgB,6CAChBC,cAAiB,YAEnBC,UAAa,+BAMfzJ,MAAMC,GAAGyJ,YACX1J,MAAMC,GAAGyJ,UAAUvJ,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyJ,UAAUvJ,UAAUC,QAAQC,UAClDsJ,MAAS,qFAMX3J,MAAMC,GAAG2J,cACX5J,MAAMC,GAAG2J,YAAYzJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,YAAYzJ,UAAUC,QAAQC,UACpDwJ,MAAS,SACTC,KAAQ,UACRC,KAAQ,YACRC,OAAU,QACVC,QAAW,UACXC,WAAc,uBAMhBlK,MAAMC,GAAGkK,iBACXnK,MAAMC,GAAGkK,eAAehK,UAAUC,QAChCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkK,eAAehK,UAAUC,SAC/CgK,YAAe,oBACfC,cAAiB,qBAMnBrK,MAAMC,GAAGqK,QACXtK,MAAMC,GAAGqK,MAAMnK,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqK,MAAMnK,UAAUC,QAAQC,UAC9CkK,SAAY,QACZC,QAAW,uCACXb,MAAS,oBACTc,KAAQ,SACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,yBACTC,SAAY,0BACZC,KAAQ,2BACRC,KAAQ,wBACRC,QAAW,aACXC,UAAa,iBAMfjL,MAAMC,GAAGiL,gBACXlL,MAAMC,GAAGiL,cAAc/K,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiL,cAAc/K,UAAUC,QAAQC,UACtDkK,SAAY,QACZC,QAAW,uCACXb,MAAS,oBACTc,KAAQ,SACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,yBACTC,SAAY,0BACZC,KAAQ,2BACRC,KAAQ,wBACRC,QAAW,aACXC,UAAa,iBAMfjL,MAAMC,GAAGkL,YACXnL,MAAMC,GAAGkL,UAAUhL,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkL,UAAUhL,UAAUC,QAAQC,UAClD+K,cAAiB,+BACjBC,aAAgB,iCAChBC,UAAa,kCAMftL,MAAMC,GAAGsL,iBACXvL,MAAMC,GAAGsL,eAAepL,UAAUC,QAAQC,SACxCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsL,eAAepL,UAAUC,QAAQC,UACvDsG,KAAQ,mCACR6E,aAAgB,mBAChB3K,OAAU,UACV4K,QAAW,oBACX7E,MAAS,mBACTvB,MAAS,UACTqG,GAAM,KACNlL,OAAU,WACV+E,WACEK,SAAY,WACZC,eAAkB,cAClBF,WAAc,eACdG,SAAY,cACZL,GAAM,aACNC,IAAO,oBAOX1F,MAAMC,GAAG0L,mBACX3L,MAAMC,GAAG0L,iBAAiBxL,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0L,iBAAiBxL,UAAUC,QAAQC,UACzDuL,aACEC,MAAS,QACTC,OAAU,WACVC,MAAS,cACTC,OAAU,eACVC,QAAW,eACXC,OAAU,cAEZJ,QACEK,YAAe,iBACfC,SAAY,YAEdL,OACEI,YAAe,iBACfC,SAAY,WAEdJ,QACEI,SAAY,aACZD,YAAe,iBACfE,SAAY,gBAEdJ,SACEE,YAAe,iBACfE,SAAY,eACZD,SAAY,WACZ3D,IAAO,QAETyD,QACEC,YAAe,iBACfE,SAAY,eACZD,SAAY,UACZ1B,GAAM,QAER1C,KACEsE,MAAS,OACTC,YAAe,MACfV,MAAS,QACTW,MAAS,UACTC,WAAc,iBACdC,GAAM,OAERC,iBACE/B,MAAS,UACTgC,OAAU,UACVC,MAAS,UACTC,OAAU,SACV/B,KAAQ,UAEVgC,UACEtE,IAAO,MACPuE,QAAW,gBACXC,QAAW,2BAOfjN,MAAMC,GAAGiN,YACXlN,MAAMC,GAAGiN,UAAU/M,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiN,UAAU/M,UAAUC,QAAQC,UAClD8M,OAAU,cACV3G,KAAQ,QACR4G,MAAS,SACTC,KAAQ,OACRC,YAAe,uBACfC,YAAe,2BACfC,MAAS,MACTjF,KAAQ,UACR/H,OAAU,WACVoH,QAAW,WACX6F,kBAAqB,kBACrBC,cAAiB,uCACjBC,eAAkB,wBAClBtE,UACEE,aAAgB,iDAElBf,OACEC,IAAO,MACPE,KAAQ,SACRiF,SAAY,iBACZC,OAAU,SACVnF,MAAS,OAEXoF,oBACEL,kBAAqB,+BACrBM,uBAA0B,6BAC1BC,mBAAsB,oBACtBC,gBAAmB,6BACnBC,qBAAwB,2BACxBC,iBAAoB,kBACpBC,gBAAmB,mEACnBC,cAAiB,kEAEnBxG,QACEjB,MAAS,SACTyB,MAAS,SACTL,IAAO,MACPsG,YAAe,cACfC,YAAe,cACfC,OAAU,UACVC,SAAY,IACZC,cAAiB,yBACjBC,YAAe,sBACfC,kBAAqB,wDACrBC,oBAAuB,iBACvBC,qBAAwB,eACxBC,WAAc,mBACdhH,YAAe,aAOnB/H,MAAMC,GAAG+O,SACXhP,MAAMC,GAAG+O,OAAO7O,UAAUC,QACxBN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+O,OAAO7O,UAAUC,SACvC6O,oBAAuB,WACvBC,oBAAuB,eAOzBlP,MAAMmP,aAAenP,MAAMmP,YAAY9O,SAAS+O,gBAClDpP,MAAMmP,YAAY9O,SAAS+O,cACzBtP,EAAEQ,QAAO,EAAMN,MAAMmP,YAAY9O,SAAS+O,eACxCC,WAAc,mBACdC,cAAiB,uBACjBC,wBAA2B,oCAC3BC,sBAAyB,kCACzBC,eAAkB,sBAClBC,WAAc,kBACdC,UAAa,iBACbC,YAAe,gBACfC,aAAgB,iBAChBC,UAAa,YACbC,MAAS,eACTC,YAAe,sBACfzP,MAAS,UACTC,OAAU,cAIZR,MAAMmP,aAAenP,MAAMmP,YAAY9O,SAAS4P,UAClDjQ,MAAMmP,YAAY9O,SAAS4P,QACzBnQ,EAAEQ,QAAO,EAAMN,MAAMmP,YAAY9O,SAAS4P,SACxC1P,MAAS,UACTgI,KAAQ,UACR/H,OAAU,WACV0P,OAAU,WACVC,MAAS,aACTC,OAAU,WACVC,OAAU,KACVC,mBACE1J,MAAS,UACT2J,YACEpK,OAAU,SACVqK,SAAY,SACZhK,KAAQ,UAGZiK,kBACE7J,MAAS,UAEX8J,gBACE9J,MAAS,oBAEX+J,eACE/J,MAAS,UAEXgK,iBACEhK,MAAS,aACTiK,SACEC,aAAgB,yBAChBpP,cAAiB,oBACjBE,aAAgB,yBAChBC,YAAe,aACfkP,SAAY,iBACZC,YAAe,eACfC,YAAe,iBAGnBC,aACEtK,MAAS,kBACTiK,SACEM,WAAc,gBACdC,kBAAqB,2BACrBC,gBAAmB,yBACnBC,QAAW,gBAGfC,cACE3K,MAAS,mBACTiK,SACEW,YAAe,mBACfC,WAAc,iBACdC,cAAiB,oBACjBC,SAAY,uBAGhBC,kBACEhL,MAAS,sBACTiL,YAAe,8CACfC,UAAa,iBACbC,UACEC,IAAO,iBACP7L,OAAU,SACV8L,KAAQ,QACRzL,KAAQ,QACR0L,OAAU,wBACVC,KAAQ,SAEVC,WACEC,YAAe,YACfC,SAAY,YACZC,QAAW,QACXC,WAAc,QACdC,QAAW,UACXC,WAAc,eACdC,qBAAwB,oBACxBC,kBAAqB,qBAEvBC,kBACER,YAAe,gBACfC,SAAY,gBACZC,QAAW,kBACXC,WAAc,qBACdC,QAAW,cACXC,WAAc,iBACdC,qBAAwB,yBACxBC,kBAAqB,sBACrBV,OAAU,iCAEZY,QACEf,SAAY,YACZgB,SAAY,WACZC,IAAO,MACPC,IAAO,MACPjM,MAAS,QACTqB,MAAS,SACTL,IAAO,MACPkL,cAAiB,sBACjBC,YAAe,mBACfC,YAAe,sBACfC,SAAY,qBACZvB,UAAa,qBACbD,YAAe,yBACfyB,YAAe,qBAEjBC,cACEC,UAAa,iBACbC,YAAe,qBAGnBC,gBACE9M,MAAS,cACTkM,QACEa,SAAY,oBACZC,WAAc,oBACdC,WAAc,WACdC,UAAa,mBACbC,QAAW,WACXC,YAAe,cACfC,MAAS,WACTC,WAAc,cACdC,OAAU,SACVC,aAAgB,kBAChBC,WAAc,kBAGlBC,oBACEC,aAAgB,sDAElBC,mBACE5N,MAAS,iBACT2N,aAAgB,gHAChBzB,QACE2B,QAAW,cACXC,OAAU,cACVC,SAAY,eAGhBC,4BACEL,aAAgB,gEAKpBvU,MAAMmP,aAAenP,MAAMmP,YAAY9O,SAASwU,aAClD7U,MAAMmP,YAAY9O,SAASwU,WACzB/U,EAAEQ,QAAO,EAAMN,MAAMmP,YAAY9O,SAASwU,YACxClU,cAAiB,yBACjBC,eAAkB,yBAClBkU,cAAiB,kBACjBC,kBAAqB,oBACrBxU,MAAS,UACT0E,OAAU,SACV+P,aAAgB,+BAChB3P,MAAS,SACT4P,OAAU,uBACVC,aAAgB,UAChBrO,IAAO,IACPC,GAAM,IACNvB,WACEC,QACEI,SAAY,oBACZC,eAAkB,uBAClBF,WAAc,wBACdG,SAAY,wBAEdU,MACEf,GAAM,WACNC,IAAO,cACPa,GAAM,uBACNF,GAAM,4BAERF,QACEV,GAAM,aACNC,IAAO,gBACPU,IAAO,uBACPC,GAAM,eACNC,IAAO,uBACPC,GAAM,oBAMZvG,MAAMmP,aAAenP,MAAMmP,YAAY9O,SAAS8U,UAClDnV,MAAMmP,YAAY9O,SAAS8U,QACzBrV,EAAEQ,QAAO,EAAMN,MAAMmP,YAAY9O,SAAS8U,SACxC1Q,cAAiB,2BACjBC,eAAkB,yBAClBC,YAAe,0BACfC,YAAe,sBACfwQ,UAAa,aACbC,kBACEvE,aAAgB,yBAChBpP,cAAiB,mBACjBE,aAAgB,uBAChBC,YAAe,aACfkP,SAAY,iBACZC,YAAe,gBACfC,YAAe,iBAEjBqE,gBAAmB,QACnBlU,KAAQ,UACRmU,QAAW,SACXC,aACEzF,MAAS,qBACTC,YAAe,0BAEjByF,KAAQ,SACRC,IAAO,QACP5Q,aAAgB,mBAChBD,UAAa,gBACb8Q,YAAe,0BACf9U,OAAU,SACV+U,WAAc,SACdlT,SAAY,mBACZmT,OAAU,0BACVC,aACEC,UAAa,aACb5P,OAAU,UACV6P,QAAW,aACXC,UAAa,aACbzF,SAAY,SACZhK,KAAQ,QACR6G,KAAQ,SACR6I,SAAY,mBACZC,SAAY,WACZC,YAAe,mBAEjBC,sBAAyB,oBACzBC,sBAAyB,mBACzBC,OAAU,mBACVC,eACEhF,YAAe,mBACfC,WAAc,iBACdC,cAAiB,oBACjBC,SAAY,uBAEdtQ,OAAU,SACVoV,MAAS,kBACTC,cACEvF,WAAc,gBACdC,kBAAqB,4BACrBC,gBAAmB,yBACnBC,QAAW,eAEbqF,KAAQ,WACRC,MAAS,QACTC,aACEC,KAAQ,UACRC,KAAQ,YAEVC,OAAU,kBACVC,QAAW,qBACXC,SAAY,sBACZC,aACEC,aAAgB,qBAChBC,cAAiB,qBACjBC,aAAgB,yBAChBC,cAAiB,0BAEnBC,UAAa,iBACbC,SAAY,gBACZnW,UAAa,YACboW,WAAc,4BAIhB1X,MAAMmP,aAAenP,MAAMmP,YAAY9O,SAASsX,OAClD3X,MAAMmP,YAAY9O,SAASsX,KACzB7X,EAAEQ,QAAO,EAAMN,MAAMmP,YAAY9O,SAASsX,MACxCC,QACEC,sBAAyB,0KACzBC,4BAA+B,2EAC/BC,gBAAmB,mFAErBC,MACEC,KAAQ,OACRC,OAAU,WACVC,KAAQ,YAOZnY,MAAMC,GAAGmY,WACXpY,MAAMC,GAAGmY,SAASjY,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmY,SAASjY,UAAUC,QAAQC,UACjDgY,OAAU,wBACVC,QAAW,cACXC,cAAiB,yBACjBpI,MAAS,aACTrH,UACEG,KAAQ,SACRG,OAAU,aACVL,WAAc,WACdC,OAAU,wBACVwP,YAAe,uBACf5Q,QAAW,SACXsB,MAAS,mBACTzB,IAAO,qBAOXzH,MAAMC,GAAGwY,WACXzY,MAAMC,GAAGwY,SAAStY,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwY,SAAStY,UAAUC,QAAQC,UACjDiY,QAAW,cACXC,cAAiB,sBACjBpI,MAAS,gBAMXnQ,MAAMC,GAAGyY,SACX1Y,MAAMC,GAAGyY,OAAOvY,UAAUC,QAAQuY,aAChC7Y,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyY,OAAOvY,UAAUC,QAAQuY,cAC/CxP,OAAU,gBACV3I,OAAU,WACV2P,MAAS,aACTD,OAAU,SACV0I,oBAAuB,iBACvB5T,cAAiB,2CACjB6T,gBAAmB,WACnBC,eAAkB,aAClBC,cAAiB,cACjBC,aAAgB,QAChBC,sBAAyB,cACzBC,qBAAwB,gBAM1BlZ,MAAMC,GAAGkZ,YACXnZ,MAAMC,GAAGkZ,UAAUhZ,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkZ,UAAUhZ,UAAUC,QAAQC,UAClD+Y,SAAY,mBACZC,QAAW,mBACXrG,IAAO,mCACPC,IAAO,mCACPqG,KAAQ,mBACRC,MAAS,yCACTC,IAAO,2BACPhT,KAAQ,6BACRiT,YAAe,wDAMjBzZ,MAAMC,GAAGyZ,SACX1Z,MAAMC,GAAGyZ,OAAOvZ,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyZ,OAAOvZ,UAAUC,QAAQuY,cAC/CgB,MAAS,WAMX3Z,MAAMC,GAAG2Z,QACX5Z,MAAMC,GAAG2Z,MAAMzZ,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2Z,MAAMzZ,UAAUC,QAAQuY,cAC9CtI,OAAU,QAMZrQ,MAAMC,GAAG4Z,UACX7Z,MAAMC,GAAG4Z,QAAQ1Z,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4Z,QAAQ1Z,UAAUC,QAAQuY,cAChDtI,OAAU,KACV7P,OAAU,cAKZR,MAAMC,GAAG6Z,SACX9Z,MAAMC,GAAG6Z,OAAO3Z,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6Z,OAAO3Z,UAAUC,QAAQuY,cAC/CtI,OAAU,KACV7P,OAAU,eAIfuZ,OAAO/Z,MAAMga", "file": "kendo.messages.es-ES.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n  /* FlatColorPicker messages */\n\n  if (kendo.ui.FlatColorPicker) {\n    kendo.ui.FlatColorPicker.prototype.options.messages =\n      $.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages, {\n        \"apply\": \"Aplicar\",\n        \"cancel\": \"Cancelar\"\n      });\n  }\n\n  /* ColorPicker messages */\n\n  if (kendo.ui.ColorPicker) {\n    kendo.ui.ColorPicker.prototype.options.messages =\n      $.extend(true, kendo.ui.ColorPicker.prototype.options.messages, {\n        \"apply\": \"Aplicar\",\n        \"cancel\": \"Cancelar\"\n      });\n  }\n\n  /* ColumnMenu messages */\n\n  if (kendo.ui.ColumnMenu) {\n    kendo.ui.ColumnMenu.prototype.options.messages =\n      $.extend(true, kendo.ui.ColumnMenu.prototype.options.messages, {\n        \"sortAscending\": \"Orden ascendente\",\n        \"sortDescending\": \"Orden descendente\",\n        \"filter\": \"Filtros\",\n        \"columns\": \"Columnas\",\n        \"done\": \"Hecho\",\n        \"settings\": \"Configuración de columnas\",\n        \"lock\": \"Bloquear\",\n        \"unlock\": \"Desbloquear\"\n      });\n  }\n\n  /* Editor messages */\n\n  if (kendo.ui.Editor) {\n    kendo.ui.Editor.prototype.options.messages =\n      $.extend(true, kendo.ui.Editor.prototype.options.messages, {\n        \"bold\": \"Negrita\",\n        \"italic\": \"Itálica\",\n        \"underline\": \"Subrayado\",\n        \"strikethrough\": \"Tachado\",\n        \"superscript\": \"Superíndice\",\n        \"subscript\": \"Subíndice\",\n        \"justifyCenter\": \"Texto centrado\",\n        \"justifyLeft\": \"Alinear texto a la izquierda\",\n        \"justifyRight\": \"Alinear texto a la derecha\",\n        \"justifyFull\": \"Justificar\",\n        \"insertUnorderedList\": \"Insertar lista desordenada\",\n        \"insertOrderedList\": \"Insertar lista ordenada\",\n        \"indent\": \"Poner sangría\",\n        \"outdent\": \"Quitar sangría\",\n        \"createLink\": \"Insert enlace\",\n        \"unlink\": \"Eliminar enlace\",\n        \"insertImage\": \"Insertar imagen\",\n        \"insertFile\": \"Insertar fichero\",\n        \"insertHtml\": \"Insertar HTML\",\n        \"viewHtml\": \"Ver HTML\",\n        \"fontName\": \"Seleccionar familia de fuentes\",\n        \"fontNameInherit\": \"(fuente heredada)\",\n        \"fontSize\": \"Seleccionar tamaño de fuente\",\n        \"fontSizeInherit\": \"(tamaño heredado)\",\n        \"formatBlock\": \"Formato\",\n        \"formatting\": \"Formato\",\n        \"foreColor\": \"Color\",\n        \"backColor\": \"Color de fondo\",\n        \"style\": \"Estilos\",\n        \"emptyFolder\": \"Carpeta vacía\",\n        \"uploadFile\": \"Subir\",\n        \"orderBy\": \"Ordenados por:\",\n        \"orderBySize\": \"Tamaño\",\n        \"orderByName\": \"Nombre\",\n        \"invalidFileType\": \"El fichero seleccionado \\\"{0}\\\" no es válido. Los tipos de ficheros soportados son {1}.\",\n        \"deleteFile\": '¿Está seguro que quiere eliminar \"{0}\"?',\n        \"overwriteFile\": 'Un fichero con el nombre \"{0}\" ya existe en el directorio actual. ¿Desea reemplazarlo?',\n        \"directoryNotFound\": \"Un directorio con este nombre no fue encontrado.\",\n        \"imageWebAddress\": \"Dirección Web\",\n        \"imageAltText\": \"Texto alternativo\",\n        \"imageWidth\": \"Ancho (px)\",\n        \"imageHeight\": \"Alto (px)\",\n        \"fileWebAddress\": \"Dirección Web\",\n        \"fileTitle\": \"Título\",\n        \"linkWebAddress\": \"Dirección Web\",\n        \"linkText\": \"Texto\",\n        \"linkToolTip\": \"ToolTip\",\n        \"linkOpenInNewWindow\": \"Abrir enlace en nueva ventana\",\n        \"dialogUpdate\": \"Actualizar\",\n        \"dialogInsert\": \"Insertar\",\n        \"dialogButtonSeparator\": \"o\",\n        \"dialogCancel\": \"Cancelar\",\n        \"createTable\": \"Crear tabla\",\n        \"addColumnLeft\": \"Agregar columna a la izquierda\",\n        \"addColumnRight\": \"Agregar columna a la derecha\",\n        \"addRowAbove\": \"Agregar fila arriba\",\n        \"addRowBelow\": \"Agregar fila abajo\",\n        \"deleteRow\": \"Borrar fila\",\n        \"deleteColumn\": \"Borrar columna\"\n      });\n  }\n\n  /* FileBrowser messages */\n\n  if (kendo.ui.FileBrowser) {\n    kendo.ui.FileBrowser.prototype.options.messages =\n      $.extend(true, kendo.ui.FileBrowser.prototype.options.messages, {\n        \"uploadFile\": \"Subir fichero\",\n        \"orderBy\": \"Ordenar por\",\n        \"orderByName\": \"Nombre\",\n        \"orderBySize\": \"Tamaño\",\n        \"directoryNotFound\": \"Un directorio con este nombre no fue encontrado.\",\n        \"emptyFolder\": \"Carpeta vacía\",\n        \"deleteFile\": '¿Está seguro que quiere eliminar \"{0}\"?',\n        \"invalidFileType\": \"El fichero seleccionado \\\"{0}\\\" no es válido. Los tipos de ficheros soportados son {1}.\",\n        \"overwriteFile\": \"Un fichero con el nombre \\\"{0}\\\" ya existe en el directorio actual. ¿Desea reemplazarlo?\",\n        \"dropFilesHere\": \"arrastre un fichero aquí para subir\",\n        \"search\": \"Buscar\"\n      });\n  }\n\n  /* FilterCell messages */\n\n  if (kendo.ui.FilterCell) {\n    kendo.ui.FilterCell.prototype.options.messages =\n      $.extend(true, kendo.ui.FilterCell.prototype.options.messages, {\n        \"isTrue\": \"Sí\",\n        \"isFalse\": \"No\",\n        \"filter\": \"Filtrar\",\n        \"clear\": \"Limpiar filtro\",\n        \"operator\": \"Operador\"\n      });\n  }\n\n  /* FilterCell operators */\n\n  if (kendo.ui.FilterCell) {\n    kendo.ui.FilterCell.prototype.options.operators =\n      $.extend(true, kendo.ui.FilterCell.prototype.options.operators, {\n        \"string\": {\n          \"eq\": \"Es igual a\",\n          \"neq\": \"No es igual a\",\n          \"startswith\": \"Comienza con\",\n          \"contains\": \"Contiene\",\n          \"doesnotcontain\": \"No contiene\",\n          \"endswith\": \"Termina en\",\n          \"isnull\": \"Es nulo\",\n          \"isnotnull\": \"No es nulo\",\n          \"isempty\": \"Está vacío\",\n          \"isnotempty\": \"No está vacío\"\n        },\n        \"number\": {\n          \"eq\": \"Es igual a\",\n          \"neq\": \"No es igual a\",\n          \"gte\": \"Es mayor o igual que\",\n          \"gt\": \"Es mayor que\",\n          \"lte\": \"Es menor o igual que\",\n          \"lt\": \"Es menor que\",\n          \"isnull\": \"Es nulo\",\n          \"isnotnull\": \"No es nulo\"\n        },\n        \"date\": {\n          \"eq\": \"Es igual a\",\n          \"neq\": \"No es igual a\",\n          \"gte\": \"Es posterior o igual a\",\n          \"gt\": \"Es posterior\",\n          \"lte\": \"Es anterior o igual a\",\n          \"lt\": \"Es anterior\",\n          \"isnull\": \"Es nulo\",\n          \"isnotnull\": \"No es nulo\"\n        },\n        \"enums\": {\n          \"eq\": \"Es igual a\",\n          \"neq\": \"No es igual a\",\n          \"isnull\": \"Es nulo\",\n          \"isnotnull\": \"No es nulo\"\n        }\n      });\n  }\n\n  /* FilterMenu messages */\n\n  if (kendo.ui.FilterMenu) {\n    kendo.ui.FilterMenu.prototype.options.messages =\n      $.extend(true, kendo.ui.FilterMenu.prototype.options.messages, {\n        \"info\": \"Mostrar filas con valor que:\",\n        \"title\": \"Mostrar filas con valor que\",\n        \"isTrue\": \"Sí\",\n        \"isFalse\": \"No\",\n        \"filter\": \"Filtrar\",\n        \"clear\": \"Limpiar filtros\",\n        \"and\": \"Y\",\n        \"or\": \"O\",\n        \"selectValue\": \"-Seleccionar valor -\",\n        \"operator\": \"Operador\",\n        \"value\": \"Valor\",\n        \"cancel\": \"Cancelar\"\n      });\n  }\n\n  /* FilterMenu operator messages */\n\n  if (kendo.ui.FilterMenu) {\n    kendo.ui.FilterMenu.prototype.options.operators =\n      $.extend(true, kendo.ui.FilterMenu.prototype.options.operators, {\n        \"string\": {\n          \"eq\": \"Es igual a\",\n          \"neq\": \"No es igual a\",\n          \"startswith\": \"Comienza con\",\n          \"contains\": \"Contiene\",\n          \"doesnotcontain\": \"No contiene\",\n          \"endswith\": \"Termina en\",\n          \"isnull\": \"Es nulo\",\n          \"isnotnull\": \"No es nulo\",\n          \"isempty\": \"Está vacío\",\n          \"isnotempty\": \"No está vacío\"\n        },\n        \"number\": {\n          \"eq\": \"Es igual a\",\n          \"neq\": \"No es igual a\",\n          \"gte\": \"Es mayor o igual que\",\n          \"gt\": \"Es mayor que\",\n          \"lte\": \"Es menor o igual que\",\n          \"lt\": \"Es menor que\",\n          \"isnull\": \"Es nulo\",\n          \"isnotnull\": \"No es nulo\"\n        },\n        \"date\": {\n          \"eq\": \"Es igual a\",\n          \"neq\": \"Es diferente a\",\n          \"gte\": \"Es posterior o igual a\",\n          \"gt\": \"Es posterior\",\n          \"lte\": \"Es anterior o igual a\",\n          \"lt\": \"Es anterior\",\n          \"isnull\": \"Es nulo\",\n          \"isnotnull\": \"No es nulo\"\n        },\n        \"enums\": {\n          \"eq\": \"Es igual a\",\n          \"neq\": \"No es igual a\",\n          \"isnull\": \"Es nulo\",\n          \"isnotnull\": \"No es nulo\"\n        }\n      });\n  }\n\n  /* FilterMultiCheck messages */\n\n  if (kendo.ui.FilterMultiCheck) {\n    kendo.ui.FilterMultiCheck.prototype.options.messages =\n      $.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages, {\n        \"checkAll\": \"Seleccionar todo\",\n        \"clear\": \"Limpiar filtros\",\n        \"filter\": \"Filtrar\",\n        \"search\": \"Buscar\"\n      });\n  }\n\n  /* Gantt messages */\n\n  if (kendo.ui.Gantt) {\n    kendo.ui.Gantt.prototype.options.messages =\n      $.extend(true, kendo.ui.Gantt.prototype.options.messages, {\n        \"actions\": {\n          \"addChild\": \"Agregar sub-tarea\",\n          \"append\": \"Agregar tarea\",\n          \"insertAfter\": \"Insertar abajo\",\n          \"insertBefore\": \"Insertar arriba\",\n          \"pdf\": \"Exportar a PDF\"\n        },\n        \"cancel\": \"Cancelar\",\n        \"deleteDependencyWindowTitle\": \"Borrar dependencia\",\n        \"deleteTaskWindowTitle\": \"Borrar tarea\",\n        \"destroy\": \"Borrar\",\n        \"editor\": {\n          \"assingButton\": \"Asignar\",\n          \"editorTitle\": \"Tarea\",\n          \"end\": \"Fin\",\n          \"percentComplete\": \"Completa\",\n          \"resources\": \"Recursos\",\n          \"resourcesEditorTitle\": \"Recursos\",\n          \"resourcesHeader\": \"Recursos\",\n          \"start\": \"Comienzo\",\n          \"title\": \"Título\",\n          \"unitsHeader\": \"Unidades\"\n        },\n        \"save\": \"Guardar\",\n        \"views\": {\n          \"day\": \"Día\",\n          \"end\": \"Fin\",\n          \"month\": \"Mes\",\n          \"start\": \"Comienzo\",\n          \"week\": \"Semana\",\n          \"year\": \"Año\"\n        }\n      });\n  }\n\n  /* Grid messages */\n\n  if (kendo.ui.Grid) {\n    kendo.ui.Grid.prototype.options.messages =\n      $.extend(true, kendo.ui.Grid.prototype.options.messages, {\n        \"commands\": {\n          \"cancel\": \"Cancelar Cambios\",\n          \"canceledit\": \"Cancelar\",\n          \"create\": \"Agregar\",\n          \"destroy\": \"Eliminar\",\n          \"edit\": \"Editar\",\n          \"excel\": \"Exportar a Excel\",\n          \"pdf\": \"Exportar a PDF\",\n          \"save\": \"Guardar Cambios\",\n          \"select\": \"Seleccionar\",\n          \"update\": \"Actualizar\"\n        },\n        \"editable\": {\n          \"cancelDelete\": \"Cancelar\",\n          \"confirmation\": \"¿Confirma la eliminación de este registro?\",\n          \"confirmDelete\": \"Eliminar\"\n        },\n        \"noRecords\": \"No hay datos disponibles.\"\n      });\n  }\n\n  /* Groupable messages */\n\n  if (kendo.ui.Groupable) {\n    kendo.ui.Groupable.prototype.options.messages =\n      $.extend(true, kendo.ui.Groupable.prototype.options.messages, {\n        \"empty\": \"Arrastre el título de una columna y suéltelo aquí para agrupar por ese criterio\"\n      });\n  }\n\n  /* MediaPlayer messages */\n\n  if (kendo.ui.MediaPlayer) {\n    kendo.ui.MediaPlayer.prototype.options.messages =\n      $.extend(true, kendo.ui.MediaPlayer.prototype.options.messages, {\n        \"pause\": \"Pausar\",\n        \"play\": \"Comezar\",\n        \"mute\": \"Silenciar\",\n        \"unmute\": \"Tocar\",\n        \"quality\": \"Calidad\",\n        \"fullscreen\": \"Pentalla completa\"\n      });\n  }\n\n  /* NumericTextBox messages */\n\n  if (kendo.ui.NumericTextBox) {\n    kendo.ui.NumericTextBox.prototype.options =\n      $.extend(true, kendo.ui.NumericTextBox.prototype.options, {\n        \"upArrowText\": \"Incrementar valor\",\n        \"downArrowText\": \"Disminuir valor\"\n      });\n  }\n\n  /* Pager messages */\n\n  if (kendo.ui.Pager) {\n    kendo.ui.Pager.prototype.options.messages =\n      $.extend(true, kendo.ui.Pager.prototype.options.messages, {\n        \"allPages\": \"Todas\",\n        \"display\": \"Elementos mostrados {0} - {1} de {2}\",\n        \"empty\": \"No hay registros.\",\n        \"page\": \"Página\",\n        \"of\": \"de {0}\",\n        \"itemsPerPage\": \"ítems por página\",\n        \"first\": \"Ir a la primera página\",\n        \"previous\": \"Ir a la página anterior\",\n        \"next\": \"Ir a la página siguiente\",\n        \"last\": \"Ir a la última página\",\n        \"refresh\": \"Actualizar\",\n        \"morePages\": \"Más paginas\"\n      });\n  }\n\n  /* TreeListPager messages */\n\n  if (kendo.ui.TreeListPager) {\n    kendo.ui.TreeListPager.prototype.options.messages =\n      $.extend(true, kendo.ui.TreeListPager.prototype.options.messages, {\n        \"allPages\": \"Todas\",\n        \"display\": \"Elementos mostrados {0} - {1} de {2}\",\n        \"empty\": \"No hay registros.\",\n        \"page\": \"Página\",\n        \"of\": \"de {0}\",\n        \"itemsPerPage\": \"ítems por página\",\n        \"first\": \"Ir a la primera página\",\n        \"previous\": \"Ir a la página anterior\",\n        \"next\": \"Ir a la página siguiente\",\n        \"last\": \"Ir a la última página\",\n        \"refresh\": \"Actualizar\",\n        \"morePages\": \"Más paginas\"\n      });\n  }\n\n  /* PivotGrid messages */\n\n  if (kendo.ui.PivotGrid) {\n    kendo.ui.PivotGrid.prototype.options.messages =\n      $.extend(true, kendo.ui.PivotGrid.prototype.options.messages, {\n        \"measureFields\": \"Colocar campos de datos aquí\",\n        \"columnFields\": \"Colocar campos de columna aquí\",\n        \"rowFields\": \"Colocar campos de filas aquí\"\n      });\n  }\n\n  /* PivotFieldMenu messages */\n\n  if (kendo.ui.PivotFieldMenu) {\n    kendo.ui.PivotFieldMenu.prototype.options.messages =\n      $.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages, {\n        \"info\": \"Mostrar elementos con valor que:\",\n        \"filterFields\": \"Filtro de campos\",\n        \"filter\": \"Filtrar\",\n        \"include\": \"Incluir campos...\",\n        \"title\": \"Campos a incluir\",\n        \"clear\": \"Limpiar\",\n        \"ok\": \"Ok\",\n        \"cancel\": \"Cancelar\",\n        \"operators\": {\n          \"contains\": \"Contiene\",\n          \"doesnotcontain\": \"No contiene\",\n          \"startswith\": \"Comienza con\",\n          \"endswith\": \"Termina con\",\n          \"eq\": \"Es ugual a\",\n          \"neq\": \"No es igual a\"\n        }\n      });\n  }\n\n  /* RecurrenceEditor messages */\n\n  if (kendo.ui.RecurrenceEditor) {\n    kendo.ui.RecurrenceEditor.prototype.options.messages =\n      $.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages, {\n        \"frequencies\": {\n          \"never\": \"Nunca\",\n          \"hourly\": \"Por hora\",\n          \"daily\": \"Diariamente\",\n          \"weekly\": \"Semanalmente\",\n          \"monthly\": \"Mensualmente\",\n          \"yearly\": \"Anualmente\"\n        },\n        \"hourly\": {\n          \"repeatEvery\": \"Repetir cada: \",\n          \"interval\": \" hora(s)\"\n        },\n        \"daily\": {\n          \"repeatEvery\": \"Repetir cada: \",\n          \"interval\": \" día(s)\"\n        },\n        \"weekly\": {\n          \"interval\": \" semana(s)\",\n          \"repeatEvery\": \"Repetir cada: \",\n          \"repeatOn\": \"Repetir en: \"\n        },\n        \"monthly\": {\n          \"repeatEvery\": \"Repetir cada: \",\n          \"repeatOn\": \"Repetir en: \",\n          \"interval\": \" mes(es)\",\n          \"day\": \"Día \"\n        },\n        \"yearly\": {\n          \"repeatEvery\": \"Repetir cada: \",\n          \"repeatOn\": \"Repetir en: \",\n          \"interval\": \" año(s)\",\n          \"of\": \" de \"\n        },\n        \"end\": {\n          \"label\": \"Fin:\",\n          \"mobileLabel\": \"Fin\",\n          \"never\": \"Nunca\",\n          \"after\": \"Después\",\n          \"occurrence\": \" ocurrencia(s)\",\n          \"on\": \"En \"\n        },\n        \"offsetPositions\": {\n          \"first\": \"primero\",\n          \"second\": \"segundo\",\n          \"third\": \"tercero\",\n          \"fourth\": \"cuarto\",\n          \"last\": \"último\"\n        },\n        \"weekdays\": {\n          \"day\": \"día\",\n          \"weekday\": \"día de semana\",\n          \"weekend\": \"día de fin de semana\"\n        }\n      });\n  }\n\n  /* Scheduler messages */\n\n  if (kendo.ui.Scheduler) {\n    kendo.ui.Scheduler.prototype.options.messages =\n      $.extend(true, kendo.ui.Scheduler.prototype.options.messages, {\n        \"allDay\": \"todo el día\",\n        \"date\": \"Fecha\",\n        \"event\": \"Evento\",\n        \"time\": \"Hora\",\n        \"showFullDay\": \"Mostrar día completo\",\n        \"showWorkDay\": \"Mostrar horas laborables\",\n        \"today\": \"Hoy\",\n        \"save\": \"Guardar\",\n        \"cancel\": \"Cancelar\",\n        \"destroy\": \"Eliminar\",\n        \"deleteWindowTitle\": \"Eliminar evento\",\n        \"ariaSlotLabel\": \"Seleccionado desde {0:t} hasta {1:t}\",\n        \"ariaEventLabel\": \"{0} en {1:D} al {2:t}\",\n        \"editable\": {\n          \"confirmation\": \"¿Está seguro que quiere eliminar este evento?\"\n        },\n        \"views\": {\n          \"day\": \"Día\",\n          \"week\": \"Semana\",\n          \"workWeek\": \"Semana laboral\",\n          \"agenda\": \"Agenda\",\n          \"month\": \"Mes\"\n        },\n        \"recurrenceMessages\": {\n          \"deleteWindowTitle\": \"Eliminar elemento recurrente\",\n          \"deleteWindowOccurrence\": \"Eliminar ocurrencia actual\",\n          \"deleteWindowSeries\": \"Eliminar la serie\",\n          \"editWindowTitle\": \"Editar elemento recurrente\",\n          \"editWindowOccurrence\": \"Editar ocurrencia actual\",\n          \"editWindowSeries\": \"Editar la serie\",\n          \"deleteRecurring\": \"¿Quiere eliminar esta ocurrencia del evento o la serie completa?\",\n          \"editRecurring\": \"¿Quiere editar esta ocurrencia del evento o la serie completa?\"\n        },\n        \"editor\": {\n          \"title\": \"Título\",\n          \"start\": \"Inicio\",\n          \"end\": \"Fin\",\n          \"allDayEvent\": \"Todo el día\",\n          \"description\": \"Descripción\",\n          \"repeat\": \"Repetir\",\n          \"timezone\": \" \",\n          \"startTimezone\": \"Zona horaria de inicio\",\n          \"endTimezone\": \"Zona horaria de fin\",\n          \"separateTimezones\": \"Usar zonas horarias separadas para el inicio y el fin\",\n          \"timezoneEditorTitle\": \"Zonas horarias\",\n          \"timezoneEditorButton\": \"Zona horaria\",\n          \"noTimezone\": \"Sin zona horaria\",\n          \"editorTitle\": \"Evento\"\n        }\n      });\n  }\n\n  /* Slider messages */\n\n  if (kendo.ui.Slider) {\n    kendo.ui.Slider.prototype.options =\n      $.extend(true, kendo.ui.Slider.prototype.options, {\n        \"increaseButtonTitle\": \"Aumentar\",\n        \"decreaseButtonTitle\": \"Disminuir\"\n      });\n  }\n\n\n  /* Spreadsheet messages */\n\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\n    kendo.spreadsheet.messages.borderPalette =\n      $.extend(true, kendo.spreadsheet.messages.borderPalette, {\n        \"allBorders\": \"Todos los bordes\",\n        \"insideBorders\": \"Dentro de los bordes\",\n        \"insideHorizontalBorders\": \"Dentro de los bordes horizontales\",\n        \"insideVerticalBorders\": \"Dentro de los bordes verticales\",\n        \"outsideBorders\": \"Fuera de los bordes\",\n        \"leftBorder\": \"Borde izquierdo\",\n        \"topBorder\": \"Borde superior\",\n        \"rightBorder\": \"Borde derecho\",\n        \"bottomBorder\": \"Borde inferior\",\n        \"noBorders\": \"Sin borde\",\n        \"reset\": \"Restabelecer\",\n        \"customColor\": \"Color personalizado\",\n        \"apply\": \"Aplicar\",\n        \"cancel\": \"Cancelar\"\n      });\n  }\n\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\n    kendo.spreadsheet.messages.dialogs =\n      $.extend(true, kendo.spreadsheet.messages.dialogs, {\n        \"apply\": \"Aplicar\",\n        \"save\": \"Guardar\",\n        \"cancel\": \"Cancelar\",\n        \"remove\": \"Eliminar\",\n        \"retry\": \"Reintentar\",\n        \"revert\": \"Revertir\",\n        \"okText\": \"OK\",\n        \"formatCellsDialog\": {\n          \"title\": \"Formato\",\n          \"categories\": {\n            \"number\": \"Número\",\n            \"currency\": \"Moneda\",\n            \"date\": \"Fecha\"\n          }\n        },\n        \"fontFamilyDialog\": {\n          \"title\": \"Fuente\"\n        },\n        \"fontSizeDialog\": {\n          \"title\": \"Tamaño de fuente\"\n        },\n        \"bordersDialog\": {\n          \"title\": \"Bordes\"\n        },\n        \"alignmentDialog\": {\n          \"title\": \"Alineación\",\n          \"buttons\": {\n            \"justtifyLeft\": \"Alinear a la izquierda\",\n            \"justifyCenter\": \"Alinear al centro\",\n            \"justifyRight\": \"Alinear a la derecha\t\",\n            \"justifyFull\": \"Justificar\",\n            \"alignTop\": \"Alinear arriba\",\n            \"alignMiddle\": \"linear medio\",\n            \"alignBottom\": \"linear fondo\"\n          }\n        },\n        \"mergeDialog\": {\n          \"title\": \"Combinar celdas\",\n          \"buttons\": {\n            \"mergeCells\": \"Combinar todo\",\n            \"mergeHorizontally\": \"Combinar horizontalmente\",\n            \"mergeVertically\": \"Combinar verticalmente\",\n            \"unmerge\": \"Descombinar\"\n          }\n        },\n        \"freezeDialog\": {\n          \"title\": \"Congelar paneles\",\n          \"buttons\": {\n            \"freezePanes\": \"Congelar paneles\",\n            \"freezeRows\": \"Congelar filas\",\n            \"freezeColumns\": \"Congelar columnas\",\n            \"unfreeze\": \"Desongelar paneles\"\n          }\n        },\n        \"validationDialog\": {\n          \"title\": \"Validación de datos\",\n          \"hintMessage\": \"Por favor, ingrese un {0} valor {1} válido.\",\n          \"hintTitle\": \"Validación {0}\",\n          \"criteria\": {\n            \"any\": \"Cualquer valor\",\n            \"number\": \"Número\",\n            \"text\": \"Texto\",\n            \"date\": \"Fecha\",\n            \"custom\": \"Fórmula personalizada\",\n            \"list\": \"Lista\"\n          },\n          \"comparers\": {\n            \"greaterThan\": \"mayor que\",\n            \"lessThan\": \"menos que\",\n            \"between\": \"entre\",\n            \"notBetween\": \"fuera\",\n            \"equalTo\": \"igual a\",\n            \"notEqualTo\": \"diferente de\",\n            \"greaterThanOrEqualTo\": \"mayor o igual que\",\n            \"lessThanOrEqualTo\": \"menor o igual que\"\n          },\n          \"comparerMessages\": {\n            \"greaterThan\": \"mayor que {0}\",\n            \"lessThan\": \"menos que {0}\",\n            \"between\": \"entre {0} y {1}\",\n            \"notBetween\": \"no entre {0} y {1}\",\n            \"equalTo\": \"igual a {0}\",\n            \"notEqualTo\": \"no igual a {0}\",\n            \"greaterThanOrEqualTo\": \"mayor que o igual a{0}\",\n            \"lessThanOrEqualTo\": \"menor o igual a {0}\",\n            \"custom\": \"que satisface la fórmula: {0}\"\n          },\n          \"labels\": {\n            \"criteria\": \"Criterios\",\n            \"comparer\": \"Comparer\",\n            \"min\": \"Min\",\n            \"max\": \"Max\",\n            \"value\": \"Valor\",\n            \"start\": \"Inicio\",\n            \"end\": \"Fin\",\n            \"onInvalidData\": \"En datos no válidos\",\n            \"rejectInput\": \"Rechazar entrada\",\n            \"showWarning\": \"Mostrar advertencia\",\n            \"showHint\": \"Mostrar sugerencia\",\n            \"hintTitle\": \"Título de la pista\",\n            \"hintMessage\": \"Mensagen de sugerencia\",\n            \"ignoreBlank\": \"Ignorar en blanco\"\n          },\n          \"placeholders\": {\n            \"typeTitle\": \"Tipo de título\",\n            \"typeMessage\": \"Escribir mensaje\"\n          }\n        },\n        \"exportAsDialog\": {\n          \"title\": \"Exportar...\",\n          \"labels\": {\n            \"fileName\": \"Nombre de archivo\",\n            \"saveAsType\": \"Guardar como tipo\",\n            \"exportArea\": \"Exportar\",\n            \"paperSize\": \"Tamaño del papel\",\n            \"margins\": \"Márgenes\",\n            \"orientation\": \"Orientación\",\n            \"print\": \"Imprimir\",\n            \"guidelines\": \"Directrices\",\n            \"center\": \"Centro\",\n            \"horizontally\": \"Horizontalmente\",\n            \"vertically\": \"Verticalmente\"\n          }\n        },\n        \"modifyMergedDialog\": {\n          \"errorMessage\": \"No se puede cambiar parte de una celda fusionada. \"\n        },\n        \"useKeyboardDialog\": {\n          \"title\": \"Copiar y pegar\",\n          \"errorMessage\": \"Estas acciones no se pueden invocar a través del menú. Utilice los métodos abreviados de teclado en su lugar:\",\n          \"labels\": {\n            \"forCopy\": \"para copiar\",\n            \"forCut\": \"para cortar\",\n            \"forPaste\": \"para pegar\"\n          }\n        },\n        \"unsupportedSelectionDialog\": {\n          \"errorMessage\": \"Esa acción no se puede realizar en la selección múltiple.\"\n        }\n      });\n  }\n\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\n    kendo.spreadsheet.messages.filterMenu =\n      $.extend(true, kendo.spreadsheet.messages.filterMenu, {\n        \"sortAscending\": \"Clasificar rango A a Z\",\n        \"sortDescending\": \"Clasificar rango Z a A\",\n        \"filterByValue\": \"Filter by value\",\n        \"filterByCondition\": \"Filtrar por valor\",\n        \"apply\": \"Aplicar\",\n        \"search\": \"Buscar\",\n        \"addToCurrent\": \"Añadir a la selección actual\",\n        \"clear\": \"Limpar\",\n        \"blanks\": \"(espacios en blanco)\",\n        \"operatorNone\": \"Ninguno\",\n        \"and\": \"Y\",\n        \"or\": \"O\",\n        \"operators\": {\n          \"string\": {\n            \"contains\": \"El texto contiene\",\n            \"doesnotcontain\": \"El texto no contiene\",\n            \"startswith\": \"El texto comienza con\",\n            \"endswith\": \"El texto termina con\"\n          },\n          \"date\": {\n            \"eq\": \"Fecha es\",\n            \"neq\": \"Fecha no es\",\n            \"lt\": \"La fecha es anterior\",\n            \"gt\": \"La fecha es posterior al\"\n          },\n          \"number\": {\n            \"eq\": \"Es igual a\",\n            \"neq\": \"No es igual a\",\n            \"gte\": \"Es mayor o igual que\",\n            \"gt\": \"Es mayor que\",\n            \"lte\": \"Es menor o igual que\",\n            \"lt\": \"Es menor que\"\n          }\n        }\n      });\n  }\n\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\n    kendo.spreadsheet.messages.toolbar =\n      $.extend(true, kendo.spreadsheet.messages.toolbar, {\n        \"addColumnLeft\": \"Añadir columna izquierda\",\n        \"addColumnRight\": \"Añadir columna derecha\",\n        \"addRowAbove\": \"Agregar fila por encima\",\n        \"addRowBelow\": \"Agregar fila debajo\",\n        \"alignment\": \"Alineación\",\n        \"alignmentButtons\": {\n          \"justtifyLeft\": \"Alinear a la izquierda\",\n          \"justifyCenter\": \"Alinear al cenro\",\n          \"justifyRight\": \"Alinear a la derecha\",\n          \"justifyFull\": \"Justificar\",\n          \"alignTop\": \"Alinear arriba\",\n          \"alignMiddle\": \"Alinear medio\",\n          \"alignBottom\": \"Alinear fondo\"\n        },\n        \"backgroundColor\": \"Fondo\",\n        \"bold\": \"Negrita\",\n        \"borders\": \"Bordes\",\n        \"colorPicker\": {\n          \"reset\": \"Restabelecer color\",\n          \"customColor\": \"Color personalizado...\"\n        },\n        \"copy\": \"Copiar\",\n        \"cut\": \"Corte\",\n        \"deleteColumn\": \"Eliminar columna\",\n        \"deleteRow\": \"Eliminar fila\",\n        \"excelImport\": \"Importar desde Excel...\",\n        \"filter\": \"Filtro\",\n        \"fontFamily\": \"Fuente\",\n        \"fontSize\": \"Tamaño de fuente\",\n        \"format\": \"Personalizar formato...\",\n        \"formatTypes\": {\n          \"automatic\": \"Automático\",\n          \"number\": \"Númbero\",\n          \"percent\": \"Percentaje\",\n          \"financial\": \"Financiero\",\n          \"currency\": \"Moneda\",\n          \"date\": \"Fecha\",\n          \"time\": \"Tiempo\",\n          \"dateTime\": \"Hora de la fecha\",\n          \"duration\": \"Duración\",\n          \"moreFormats\": \"Más formatos...\"\n        },\n        \"formatDecreaseDecimal\": \"Disminuir decimal\",\n        \"formatIncreaseDecimal\": \"Aumentar decimal\",\n        \"freeze\": \"Congelar paneles\",\n        \"freezeButtons\": {\n          \"freezePanes\": \"Congelar paneles\",\n          \"freezeRows\": \"Congelar filas\",\n          \"freezeColumns\": \"Congelar columnas\",\n          \"unfreeze\": \"Descongelar paneles\"\n        },\n        \"italic\": \"Italic\",\n        \"merge\": \"Combinar celdas\",\n        \"mergeButtons\": {\n          \"mergeCells\": \"Combinar todo\",\n          \"mergeHorizontally\": \"Combinar horizontalmente'\",\n          \"mergeVertically\": \"Combinar verticalmente\",\n          \"unmerge\": \"Descombinar\"\n        },\n        \"open\": \"Abrir...\",\n        \"paste\": \"Pegar\",\n        \"quickAccess\": {\n          \"redo\": \"Refacer\",\n          \"undo\": \"Desfacer\"\n        },\n        \"saveAs\": \"Guardar como...\",\n        \"sortAsc\": \"Ordenar ascendente\",\n        \"sortDesc\": \"Ordenar descendente\",\n        \"sortButtons\": {\n          \"sortSheetAsc\": \"Ordenar hoja A a Z\",\n          \"sortSheetDesc\": \"Ordenar hoja Z a A\",\n          \"sortRangeAsc\": \"Ordenar rango de A a Z\",\n          \"sortRangeDesc\": \"Ordenar rango de Z a A\"\n        },\n        \"textColor\": \"Color de texto\",\n        \"textWrap\": \"Ajustar texto\",\n        \"underline\": \"Subrayado\",\n        \"validation\": \"Validación de datos...\"\n      });\n  }\n\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\n    kendo.spreadsheet.messages.view =\n      $.extend(true, kendo.spreadsheet.messages.view, {\n        \"errors\": {\n          \"shiftingNonblankCells\": \"No se pueden insertar las celdas debido a la posibilidad de pérdida de datos. Seleccione otra ubicación de inserción o borre los datos del final de su hoja de trabajo.\",\n          \"filterRangeContainingMerges\": \"No se puede crear un filtro dentro de un intervalo que contenga fusiones\",\n          \"validationError\": \"El valor que introdujo viola las reglas de validación establecidas en la celda.\"\n        },\n        \"tabs\": {\n          \"home\": \"Casa\",\n          \"insert\": \"Insertar\",\n          \"data\": \"Datos\"\n        }\n      });\n  }\n\n  /* TreeList messages */\n\n  if (kendo.ui.TreeList) {\n    kendo.ui.TreeList.prototype.options.messages =\n      $.extend(true, kendo.ui.TreeList.prototype.options.messages, {\n        \"noRows\": \"No records to display\",\n        \"loading\": \"Cargando...\",\n        \"requestFailed\": \"Error de la solicitud.\",\n        \"retry\": \"Reintentar\",\n        \"commands\": {\n          \"edit\": \"Editar\",\n          \"update\": \"Actualizar\",\n          \"canceledit\": \"Cancelar\",\n          \"create\": \"Añadir nuevo registro\",\n          \"createchild\": \"Añadir registro hijo\",\n          \"destroy\": \"Borrar\",\n          \"excel\": \"Exportar a Excel\",\n          \"pdf\": \"Exportar a PDF\"\n        }\n      });\n  }\n\n  /* TreeView messages */\n\n  if (kendo.ui.TreeView) {\n    kendo.ui.TreeView.prototype.options.messages =\n      $.extend(true, kendo.ui.TreeView.prototype.options.messages, {\n        \"loading\": \"Cargando...\",\n        \"requestFailed\": \"Fallo en solicitud.\",\n        \"retry\": \"Reintentar\"\n      });\n  }\n\n  /* Upload messages */\n\n  if (kendo.ui.Upload) {\n    kendo.ui.Upload.prototype.options.localization =\n      $.extend(true, kendo.ui.Upload.prototype.options.localization, {\n        \"select\": \"Seleccione...\",\n        \"cancel\": \"Cancelar\",\n        \"retry\": \"Reintentar\",\n        \"remove\": \"Quitar\",\n        \"uploadSelectedFiles\": \"Subir archivos\",\n        \"dropFilesHere\": \"Arrastre los archivos aquí para subirlos\",\n        \"statusUploading\": \"subiendo\",\n        \"statusUploaded\": \"Completado\",\n        \"statusWarning\": \"advertencia\",\n        \"statusFailed\": \"Error\",\n        \"headerStatusUploading\": \"Subiendo...\",\n        \"headerStatusUploaded\": \"Completado\"\n      });\n  }\n\n  /* Validator messages */\n\n  if (kendo.ui.Validator) {\n    kendo.ui.Validator.prototype.options.messages =\n      $.extend(true, kendo.ui.Validator.prototype.options.messages, {\n        \"required\": \"{0} es requerido\",\n        \"pattern\": \"{0} no es válido\",\n        \"min\": \"{0} debe ser mayor o igual a {1}\",\n        \"max\": \"{0} debe ser menor o igual a {1}\",\n        \"step\": \"{0} no es válido\",\n        \"email\": \"{0} no es un correo electrónico válido\",\n        \"url\": \"{0} no es una URL válida\",\n        \"date\": \"{0} no es una fecha válida\",\n        \"dateCompare\": \"Fecha final debe ser mayor o igual a fecha inicial\"\n      });\n  }\n\n  /* Dialog */\n\n  if (kendo.ui.Dialog) {\n    kendo.ui.Dialog.prototype.options.messages =\n      $.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n        \"close\": \"Cerca\"\n      });\n  }\n\n  /* Alert */\n\n  if (kendo.ui.Alert) {\n    kendo.ui.Alert.prototype.options.messages =\n      $.extend(true, kendo.ui.Alert.prototype.options.localization, {\n        \"okText\": \"OK\"\n      });\n  }\n\n  /* Confirm */\n\n  if (kendo.ui.Confirm) {\n    kendo.ui.Confirm.prototype.options.messages =\n      $.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n        \"okText\": \"OK\",\n        \"cancel\": \"Cancelar\"\n      });\n  }\n\n  /* Prompt */\n  if (kendo.ui.Prompt) {\n    kendo.ui.Prompt.prototype.options.messages =\n      $.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n        \"okText\": \"OK\",\n        \"cancel\": \"Cancelar\"\n      });\n  }\n\n})(window.kendo.jQuery);\n}));"]}