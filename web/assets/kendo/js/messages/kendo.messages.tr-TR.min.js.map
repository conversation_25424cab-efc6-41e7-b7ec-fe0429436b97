{"version": 3, "sources": ["messages/kendo.messages.tr-TR.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "isnull", "isnotnull", "enums", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "isempty", "isnotempty", "isnullorempty", "isnotnullorempty", "FilterMenu", "ColumnMenu", "messages", "columns", "settings", "done", "lock", "sortAscending", "sortDescending", "unlock", "filter", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "label", "mobileLabel", "never", "occurrence", "on", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "weekdays", "weekday", "weekend", "of", "Editor", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "backColor", "bold", "createLink", "createTable", "deleteColumn", "deleteFile", "deleteRow", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "dialogUpdate", "directoryNotFound", "dropFilesHere", "emptyFolder", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "foreColor", "formatBlock", "formatting", "imageAltText", "imageWebAddress", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "invalidFileType", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "orderBy", "orderByName", "orderBySize", "outdent", "overwriteFile", "search", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "uploadFile", "viewHtml", "insertFile", "clear", "isFalse", "isTrue", "operator", "and", "cancel", "info", "title", "additionalOperator", "or", "selectValue", "value", "additionalValue", "logic", "FilterMultiCheck", "checkAll", "selectedItemsFormat", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "Groupable", "empty", "Pager", "allPages", "display", "itemsPerPage", "morePages", "next", "page", "previous", "refresh", "TreeListPager", "Scheduler", "allDay", "deleteWindowTitle", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "noTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "showFullDay", "showWorkDay", "time", "today", "views", "agenda", "month", "week", "workWeek", "Upload", "localization", "headerStatusUploaded", "headerStatusUploading", "remove", "retry", "statusFailed", "statusUploaded", "statusUploading", "uploadSelectedFiles", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,OACNC,GAAM,QACNC,IAAO,kBACPC,GAAM,OACNC,IAAO,iBACPC,IAAO,aACPC,OAAU,OACVC,UAAa,cAEfC,OACER,GAAM,OACNK,IAAO,aACPC,OAAU,OACVC,UAAa,cAEfE,QACET,GAAM,OACNC,GAAM,QACNC,IAAO,kBACPC,GAAM,QACNC,IAAO,kBACPC,IAAO,aACPC,OAAU,OACVC,UAAa,cAEfG,QACEC,SAAY,WACZC,eAAkB,YAClBC,SAAY,YACZb,GAAM,OACNK,IAAO,aACPS,WAAc,aACdR,OAAU,OACVC,UAAa,aACbQ,QAAW,MACXC,WAAc,YACdC,cAAiB,kBACjBC,iBAAoB,qBAOpB1B,MAAMC,GAAG0B,aACb3B,MAAMC,GAAG0B,WAAWxB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0B,WAAWxB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,OACNC,GAAM,QACNC,IAAO,kBACPC,GAAM,OACNC,IAAO,iBACPC,IAAO,aACPC,OAAU,OACVC,UAAa,cAEfC,OACER,GAAM,OACNK,IAAO,aACPC,OAAU,OACVC,UAAa,cAEfE,QACET,GAAM,OACNC,GAAM,QACNC,IAAO,kBACPC,GAAM,QACNC,IAAO,kBACPC,IAAO,aACPC,OAAU,OACVC,UAAa,cAEfG,QACEC,SAAY,WACZC,eAAkB,YAClBC,SAAY,YACZb,GAAM,OACNK,IAAO,aACPS,WAAc,aACdR,OAAU,OACVC,UAAa,aACbQ,QAAW,MACXC,WAAc,YACdC,cAAiB,iBACjBC,iBAAoB,sBAOpB1B,MAAMC,GAAG2B,aACb5B,MAAMC,GAAG2B,WAAWzB,UAAUC,QAAQyB,SACtC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2B,WAAWzB,UAAUC,QAAQyB,UACnDC,QAAW,WACXC,SAAY,iBACZC,KAAQ,QACRC,KAAQ,UACRC,cAAiB,iBACjBC,eAAkB,kBAClBC,OAAU,cACVC,OAAU,cAMRrC,MAAMC,GAAGqC,mBACbtC,MAAMC,GAAGqC,iBAAiBnC,UAAUC,QAAQyB,SAC5C/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqC,iBAAiBnC,UAAUC,QAAQyB,UACzDU,OACEC,SAAY,SACZC,YAAe,oBAEjBC,KACEC,MAAS,QACTC,MAAS,QACTC,YAAe,QACfC,MAAS,WACTC,WAAc,OACdC,GAAM,SAERC,aACEV,MAAS,SACTW,QAAW,QACXJ,MAAS,WACTK,OAAU,WACVC,OAAU,UAEZF,SACEG,IAAO,MACPb,SAAY,QACZC,YAAe,kBACfa,SAAY,YAEdC,iBACEC,MAAS,MACTC,OAAU,WACVC,KAAQ,MACRC,OAAU,SACVC,MAAS,UAEXC,UACER,IAAO,MACPS,QAAW,UACXC,QAAW,aAEbZ,QACEX,SAAY,WACZC,YAAe,qBACfa,SAAY,YAEdF,QACEZ,SAAY,SACZwB,GAAM,WACNvB,YAAe,mBACfa,SAAY,eAOZtD,MAAMC,GAAGgE,SACbjE,MAAMC,GAAGgE,OAAO9D,UAAUC,QAAQyB,SAClC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgE,OAAO9D,UAAUC,QAAQyB,UAC/CqC,cAAiB,kBACjBC,eAAkB,kBAClBC,YAAe,sBACfC,YAAe,qBACfC,UAAa,kBACbC,KAAQ,SACRC,WAAc,eACdC,YAAe,gBACfC,aAAgB,cAChBC,WAAc,sCACdC,UAAa,YACbC,sBAAyB,QACzBC,aAAgB,QAChBC,aAAgB,OAChBC,aAAgB,WAChBC,kBAAqB,kCACrBC,cAAiB,yCACjBC,YAAe,aACfC,SAAY,sBACZC,gBAAmB,sBACnBC,SAAY,sBACZC,gBAAmB,mBACnBC,UAAa,OACbC,YAAe,QACfC,WAAc,gBACdC,aAAgB,mBAChBC,gBAAmB,aACnBC,OAAU,YACVC,WAAc,YACdC,YAAe,aACfC,kBAAqB,oBACrBC,oBAAuB,qBACvBC,gBAAmB,qEACnBC,OAAU,kBACVC,cAAiB,gBACjBC,YAAe,YACfC,YAAe,mBACfC,aAAgB,mBAChBC,oBAAuB,oBACvBC,SAAY,QACZC,YAAe,aACfC,eAAkB,aAClBC,QAAW,oBACXC,YAAe,OACfC,YAAe,QACfC,QAAW,UACXC,cAAiB,+EACjBC,OAAU,QACVC,cAAiB,cACjBC,OAAU,UACVC,UAAa,QACbC,YAAe,UACfC,UAAa,aACbC,OAAU,iBACVC,WAAc,QACdC,SAAY,iBACZC,WAAc,gBAMZ1H,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQyB,SACtC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQyB,UACnD8F,MAAS,UACTtF,OAAU,SACVuF,QAAW,SACXC,OAAU,QACVC,SAAY,cAMV9H,MAAMC,GAAG0B,aACb3B,MAAMC,GAAG0B,WAAWxB,UAAUC,QAAQyB,SACtC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0B,WAAWxB,UAAUC,QAAQyB,UACnDkG,IAAO,KACPC,OAAU,QACVL,MAAS,UACTtF,OAAU,WACV4F,KAAQ,gCACRC,MAAS,+BACTN,QAAW,SACXC,OAAU,QACVC,SAAY,WACZK,mBAAsB,cACtBC,GAAM,OACNC,YAAe,gBACfC,MAAS,QACTC,gBAAmB,WACnBC,MAAS,aAMPxI,MAAMC,GAAGwI,mBACbzI,MAAMC,GAAGwI,iBAAiBtI,UAAUC,QAAQyB,SAC5C/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwI,iBAAiBtI,UAAUC,QAAQyB,UACzDoF,OAAU,QACVyB,SAAY,kBACZf,MAAS,UACTtF,OAAU,WACVsG,oBAAuB,6BAMrB3I,MAAMC,GAAG2I,OACb5I,MAAMC,GAAG2I,KAAKzI,UAAUC,QAAQyB,SAChC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2I,KAAKzI,UAAUC,QAAQyB,UAC7CgH,UACEC,WAAc,QACdd,OAAU,0BACVe,OAAU,kBACVC,QAAW,MACXC,KAAQ,UACRC,MAAS,eACTC,IAAO,aACPC,KAAQ,wBACRC,OAAU,MACVC,OAAU,YAEZC,UACEC,aAAgB,QAChBC,aAAgB,iDAChBC,cAAiB,UAOjB1J,MAAMC,GAAG0J,YACb3J,MAAMC,GAAG0J,UAAUxJ,UAAUC,QAAQyB,SACrC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0J,UAAUxJ,UAAUC,QAAQyB,UAClD+H,MAAS,wFAMP5J,MAAMC,GAAG4J,QACb7J,MAAMC,GAAG4J,MAAM1J,UAAUC,QAAQyB,SACjC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4J,MAAM1J,UAAUC,QAAQyB,UAC9CiI,SAAY,OACZC,QAAW,qDACXH,MAAS,yBACTpG,MAAS,kBACTwG,aAAgB,oBAChBtG,KAAQ,kBACRuG,UAAa,mBACbC,KAAQ,0BACRlG,GAAM,MACNmG,KAAQ,QACRC,SAAY,yBACZC,QAAW,cAMTrK,MAAMC,GAAGqK,gBACbtK,MAAMC,GAAGqK,cAAcnK,UAAUC,QAAQyB,SACzC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqK,cAAcnK,UAAUC,QAAQyB,UACtDiI,SAAY,OACZC,QAAW,qDACXH,MAAS,yBACTpG,MAAS,kBACTwG,aAAgB,oBAChBtG,KAAQ,kBACRuG,UAAa,mBACbC,KAAQ,0BACRlG,GAAM,MACNmG,KAAQ,QACRC,SAAY,yBACZC,QAAW,cAMTrK,MAAMC,GAAGsK,YACbvK,MAAMC,GAAGsK,UAAUpK,UAAUC,QAAQyB,SACrC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsK,UAAUpK,UAAUC,QAAQyB,UAClD2I,OAAU,UACVxC,OAAU,WACVuB,UACEE,aAAgB,oDAElBlJ,KAAQ,QACRkK,kBAAqB,gBACrBzB,QAAW,MACX0B,QACEC,YAAe,qBACfC,YAAe,QACfC,YAAe,OACfnI,IAAO,QACPoI,YAAe,cACfC,WAAc,8BACdC,OAAU,SACVC,kBAAqB,mDACrBC,MAAS,YACTC,cAAiB,kBACjBC,SAAY,GACZC,qBAAwB,gBACxBC,oBAAuB,gBACvBpD,MAAS,SAEXqD,MAAS,OACTC,oBACEC,gBAAmB,gEACnBC,uBAA0B,yBAC1BC,mBAAsB,aACtBlB,kBAAqB,wBACrBmB,cAAiB,oEACjBC,qBAAwB,wBACxBC,iBAAoB,iBACpBC,gBAAmB,6BAErB3C,KAAQ,SACR4C,YAAe,iBACfC,YAAe,uBACfC,KAAQ,QACRC,MAAS,QACTC,OACEC,OAAU,SACVhJ,IAAO,MACPiJ,MAAS,KACTC,KAAQ,QACRC,SAAY,sBAOZxM,MAAMC,GAAGwM,SACbzM,MAAMC,GAAGwM,OAAOtM,UAAUC,QAAQsM,aAClC5M,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwM,OAAOtM,UAAUC,QAAQsM,cAC/C1E,OAAU,WACV9C,cAAiB,yCACjByH,qBAAwB,aACxBC,sBAAyB,aACzBC,OAAU,SACVC,MAAS,cACTzD,OAAU,UACV0D,aAAgB,iBAChBC,eAAkB,WAClBC,gBAAmB,aACnBC,oBAAuB,6BAMrBlN,MAAMC,GAAGkN,SACbnN,MAAMC,GAAGkN,OAAOhN,UAAUC,QAAQyB,SAClC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkN,OAAOhN,UAAUC,QAAQsM,cAC/CU,MAAS,WAMPpN,MAAMC,GAAGoN,QACbrN,MAAMC,GAAGoN,MAAMlN,UAAUC,QAAQyB,SACjC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoN,MAAMlN,UAAUC,QAAQsM,cAC9CY,OAAU,WAMRtN,MAAMC,GAAGsN,UACbvN,MAAMC,GAAGsN,QAAQpN,UAAUC,QAAQyB,SACnC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsN,QAAQpN,UAAUC,QAAQsM,cAChDY,OAAU,QACVtF,OAAU,WAKRhI,MAAMC,GAAGuN,SACbxN,MAAMC,GAAGuN,OAAOrN,UAAUC,QAAQyB,SAClC/B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuN,OAAOrN,UAAUC,QAAQsM,cAC/CY,OAAU,QACVtF,OAAU,YAITyF,OAAOzN,MAAM0N", "file": "kendo.messages.tr-TR.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Eşit\",\n    \"gt\": \"Sonra\",\n    \"gte\": \"Sonra veya eşit\",\n    \"lt\": \"Önce\",\n    \"lte\": \"Önce veya eşit\",\n    \"neq\": \"Eşit değil\",\n    \"isnull\": \"Null\",\n    \"isnotnull\": \"Null değil\"\n  },\n  \"enums\": {\n    \"eq\": \"Eşit\",\n    \"neq\": \"Eşit değil\",\n    \"isnull\": \"Null\",\n    \"isnotnull\": \"Null değil\"\n  },\n  \"number\": {\n    \"eq\": \"<PERSON>şit\",\n    \"gt\": \"<PERSON>üyük\",\n    \"gte\": \"Büyük veya eşit\",\n    \"lt\": \"Küçük\",\n    \"lte\": \"Küçük veya eşit\",\n    \"neq\": \"Eşit değil\",\n    \"isnull\": \"Null\",\n    \"isnotnull\": \"Null değil\"\n  },\n  \"string\": {\n    \"contains\": \"İçeriyor\",\n    \"doesnotcontain\": \"İçermiyor\",\n    \"endswith\": \"İle biter\",\n    \"eq\": \"Eşit\",\n    \"neq\": \"Eşit değil\",\n    \"startswith\": \"İle başlar\",\n    \"isnull\": \"Null\",\n    \"isnotnull\": \"Null değil\",\n    \"isempty\": \"Boş\",\n    \"isnotempty\": \"Boş değil\",\n    \"isnullorempty\": \"Değer içermiyor\",\n    \"isnotnullorempty\": \"Değer içeriyor\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Eşit\",\n    \"gt\": \"Sonra\",\n    \"gte\": \"Sonra veya eşit\",\n    \"lt\": \"Önce\",\n    \"lte\": \"Önce veya eşit\",\n    \"neq\": \"Eşit değil\",\n    \"isnull\": \"Null\",\n    \"isnotnull\": \"Null değil\"\n  },\n  \"enums\": {\n    \"eq\": \"Eşit\",\n    \"neq\": \"Eşit değil\",\n    \"isnull\": \"Null\",\n    \"isnotnull\": \"Null değil\"\n  },\n  \"number\": {\n    \"eq\": \"Eşit\",\n    \"gt\": \"Büyük\",\n    \"gte\": \"Büyük veya eşit\",\n    \"lt\": \"Küçük\",\n    \"lte\": \"Küçük veya eşit\",\n    \"neq\": \"Eşit değil\",\n    \"isnull\": \"Null\",\n    \"isnotnull\": \"Null değil\"\n  },\n  \"string\": {\n    \"contains\": \"İçeriyor\",\n    \"doesnotcontain\": \"İçermiyor\",\n    \"endswith\": \"İle biter\",\n    \"eq\": \"Eşit\",\n    \"neq\": \"Eşit değil\",\n    \"startswith\": \"İle başlar\",\n    \"isnull\": \"Null\",\n    \"isnotnull\": \"Null değil\",\n    \"isempty\": \"Boş\",\n    \"isnotempty\": \"Boş değil\",\n    \"isnullorempty\": \"Değer içeriyor\",\n    \"isnotnullorempty\": \"Değer içermiyor\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Sütunlar\",\n  \"settings\": \"Sütun ayarları\",\n  \"done\": \"Tamam\",\n  \"lock\": \"Kilitle\",\n  \"sortAscending\": \"Artan Sıralama\",\n  \"sortDescending\": \"Azalan Sıralama\",\n  \"unlock\": \"Kilidini Aç\",\n  \"filter\": \"Filtrele\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"Günler\",\n    \"repeatEvery\": \"Her gün tekrarla\"\n  },\n  \"end\": {\n    \"after\": \"Sonra\",\n    \"label\": \"Bitiş\",\n    \"mobileLabel\": \"Bitiş\",\n    \"never\": \"Asla/Hiç\",\n    \"occurrence\": \"Olay\",\n    \"on\": \"Anlık\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Günlük\",\n    \"monthly\": \"Aylık\",\n    \"never\": \"Asla/Hiç\",\n    \"weekly\": \"Haftalık\",\n    \"yearly\": \"Yıllık\"\n  },\n  \"monthly\": {\n    \"day\": \"Gün\",\n    \"interval\": \"Aylar\",\n    \"repeatEvery\": \"Her ay tekrarla\",\n    \"repeatOn\": \"Tekrarla\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"İlk\",\n    \"fourth\": \"Dördüncü\",\n    \"last\": \"Son\",\n    \"second\": \"İkinci\",\n    \"third\": \"Üçüncü\"\n  },\n  \"weekdays\": {\n    \"day\": \"Gün\",\n    \"weekday\": \"İş günü\",\n    \"weekend\": \"Haftasonu\"\n  },\n  \"weekly\": {\n    \"interval\": \"Haftalar\",\n    \"repeatEvery\": \"Her hafta tekrarla\",\n    \"repeatOn\": \"Tekrarla\"\n  },\n  \"yearly\": {\n    \"interval\": \"Yıllar\",\n    \"of\": \"Arasında\",\n    \"repeatEvery\": \"Her Yıl Tekrarla\",\n    \"repeatOn\": \"Tekrarla\"\n  }\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"addColumnLeft\": \"Sola kolon ekle\",\n  \"addColumnRight\": \"Sağa kolon ekle\",\n  \"addRowAbove\": \"Yukarıya satır ekle\",\n  \"addRowBelow\": \"Aşağıya satır ekle\",\n  \"backColor\": \"Arka plan rengi\",\n  \"bold\": \"Kalın \",\n  \"createLink\": \"Köprü ekleme\",\n  \"createTable\": \"Tablo oluştur\",\n  \"deleteColumn\": \"Sütun silme\",\n  \"deleteFile\": \"Silmek istediğinizden emin misiniz?\",\n  \"deleteRow\": \"Satır sil\",\n  \"dialogButtonSeparator\": \"ya da\",\n  \"dialogCancel\": \"İptal\",\n  \"dialogInsert\": \"Ekle\",\n  \"dialogUpdate\": \"Güncelle\",\n  \"directoryNotFound\": \"Bu isimde bir dizin bulunamadı.\",\n  \"dropFilesHere\": \"Yüklemek için dosyaları buraya bırakın\",\n  \"emptyFolder\": \"Boş klasör\",\n  \"fontName\": \"Font ailesi Seçiniz\",\n  \"fontNameInherit\": \"Devralınan Karakter\",\n  \"fontSize\": \"Font boyutu Seçiniz\",\n  \"fontSizeInherit\": \"Devralınan Boyut\",\n  \"foreColor\": \"Renk\",\n  \"formatBlock\": \"Biçim\",\n  \"formatting\": \"Biçimlendirme\",\n  \"imageAltText\": \"Alternatif metin\",\n  \"imageWebAddress\": \"Web adresi\",\n  \"indent\": \"Satırbaşı\",\n  \"insertHtml\": \"HTML ekle\",\n  \"insertImage\": \"Resim ekle\",\n  \"insertOrderedList\": \"Sıralı liste ekle\",\n  \"insertUnorderedList\": \"Sırasız liste ekle\",\n  \"invalidFileType\": \"Seçilen dosya \\\"{0}\\\" geçerli değil. Desteklenen dosya türleri: {1}.\",\n  \"italic\": \"İtalik karakter\",\n  \"justifyCenter\": \"Merkezi metin\",\n  \"justifyFull\": \"Doğrulama\",\n  \"justifyLeft\": \"Metni sola yasla\",\n  \"justifyRight\": \"Metni sağa yasla\",\n  \"linkOpenInNewWindow\": \"Yeni pencerede aç\",\n  \"linkText\": \"Metin\",\n  \"linkToolTip\": \"Araç İpucu\",\n  \"linkWebAddress\": \"Web adresi\",\n  \"orderBy\": \"Düzenleme ölçütü:\",\n  \"orderByName\": \"İsim\",\n  \"orderBySize\": \"Boyut\",\n  \"outdent\": \"Çıkıntı\",\n  \"overwriteFile\": \"Dizinde \\\"{0}\\\" isimli bir dosya zaten mevcut. Üzerine yazmak istiyor musunuz?\",\n  \"search\": \"Arama\",\n  \"strikethrough\": \"Üstü çizili\",\n  \"styles\": \"Stiller\",\n  \"subscript\": \"İndis\",\n  \"superscript\": \"Üstyazı\",\n  \"underline\": \"Altını çiz\",\n  \"unlink\": \"Köprüyü Kaldır\",\n  \"uploadFile\": \"Yükle\",\n  \"viewHtml\": \"HTML Görünümü \",\n  \"insertFile\": \"Dosya Ekle\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"clear\": \"Temizle\",\n  \"filter\": \"Filtre\",\n  \"isFalse\": \"Yanlış\",\n  \"isTrue\": \"Doğru\",\n  \"operator\": \"Operatör\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"and\": \"Ve\",\n  \"cancel\": \"İptal\",\n  \"clear\": \"Temizle\",\n  \"filter\": \"Filtrele\",\n  \"info\": \"Tanıma uyan kayıtları göster:\",\n  \"title\": \"Tanıma uyan kayıtları göster\",\n  \"isFalse\": \"Yanlış\",\n  \"isTrue\": \"Doğru\",\n  \"operator\": \"Operatör\",\n  \"additionalOperator\": \"Ek Operatör\",\n  \"or\": \"Veya\",\n  \"selectValue\": \"Değer Seçiniz\",\n  \"value\": \"Değer\",\n  \"additionalValue\": \"Ek Değer\",\n  \"logic\": \"Bağıntı\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"Arama\",\n  \"checkAll\": \"Tümünü İşaretle\",\n  \"clear\": \"Temizle\",\n  \"filter\": \"Filtrele\",\n  \"selectedItemsFormat\": \"{0} seçenek işaretlendi\"\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"canceledit\": \"İptal\",\n    \"cancel\": \"Değişiklikleri iptal et\",\n    \"create\": \"Yeni Kayıt Ekle\",\n    \"destroy\": \"Sil\",\n    \"edit\": \"Düzenle\",\n    \"excel\": \"Excel Kaydet\",\n    \"pdf\": \"PDF Kaydet\",\n    \"save\": \"Değişiklikleri Kaydet\",\n    \"select\": \"Seç\",\n    \"update\": \"Güncelle\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"İptal\",\n    \"confirmation\": \"Kayıtları silmek istediğinizden emin misiniz ?\",\n    \"confirmDelete\": \"Sil\"\n  }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Bir sütun başlığını sürükleyin ve bu sütuna göre gruplandırmak için buraya bırakın\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Tümü\",\n  \"display\": \"{0} - {1} aralığı gösteriliyor. Toplam {2} öğe var\",\n  \"empty\": \"Görüntülenecek öğe yok\",\n  \"first\": \"İlk sayfaya git\",\n  \"itemsPerPage\": \"Sayfa başına ürün\",\n  \"last\": \"Son sayfaya git\",\n  \"morePages\": \"Daha fazla sayfa\",\n  \"next\": \"Bir sonraki sayfaya git\",\n  \"of\": \"{0}\",\n  \"page\": \"Sayfa\",\n  \"previous\": \"Bir önceki sayfaya git\",\n  \"refresh\": \"Güncelle\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"Tümü\",\n  \"display\": \"{0} - {1} aralığı gösteriliyor. Toplam {2} öğe var\",\n  \"empty\": \"Görüntülenecek öğe yok\",\n  \"first\": \"İlk sayfaya git\",\n  \"itemsPerPage\": \"Sayfa başına ürün\",\n  \"last\": \"Son sayfaya git\",\n  \"morePages\": \"Daha fazla sayfa\",\n  \"next\": \"Bir sonraki sayfaya git\",\n  \"of\": \"{0}\",\n  \"page\": \"Sayfa\",\n  \"previous\": \"Bir önceki sayfaya git\",\n  \"refresh\": \"Güncelle\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"Tüm gün\",\n  \"cancel\": \"İptal Et\",\n  \"editable\": {\n    \"confirmation\": \"Bu etkinliği silmek istediğinizden emin misiniz?\"\n  },\n  \"date\": \"Tarih\",\n  \"deleteWindowTitle\": \"Etkinliği sil\",\n  \"destroy\": \"Sil\",\n  \"editor\": {\n    \"allDayEvent\": \"Tüm gün süren olay\",\n    \"description\": \"Tanım\",\n    \"editorTitle\": \"Olay\",\n    \"end\": \"Bitiş\",\n    \"endTimezone\": \"Bitiş saati\",\n    \"noTimezone\": \"Zaman Aralığı belirtilmemiş\",\n    \"repeat\": \"Tekrar\",\n    \"separateTimezones\": \"Ayrı bir başlangıç ve bitiş Zaman aralığı kullan\",\n    \"start\": \"Başlangıç\",\n    \"startTimezone\": \"Başlangıç Saati\",\n    \"timezone\": \"\",\n    \"timezoneEditorButton\": \"Zaman Aralığı\",\n    \"timezoneEditorTitle\": \"Zaman Aralığı\",\n    \"title\": \"Tanım\"\n  },\n  \"event\": \"Olay\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Sadece bu olayı mı yoksa bütün seriyi mi silmek istiyorsunuz?\",\n    \"deleteWindowOccurrence\": \"Geçerli yinelemeyi Sil\",\n    \"deleteWindowSeries\": \"Seriyi Sil\",\n    \"deleteWindowTitle\": \"Tekrarlanan Öğeyi Sil\",\n    \"editRecurring\": \"Sadece bu olayı mı yoksa bütün seriyi mi düzenlemek istiyorsunuz?\",\n    \"editWindowOccurrence\": \"Geçerli Olayı Düzenle\",\n    \"editWindowSeries\": \"Seriyi düzenle\",\n    \"editWindowTitle\": \"Tekrarlanan Öğeyi Düzenle\"\n  },\n  \"save\": \"Kaydet\",\n  \"showFullDay\": \"Tüm gün göster\",\n  \"showWorkDay\": \"İş saatlerini göster\",\n  \"time\": \"Zaman\",\n  \"today\": \"Bugün\",\n  \"views\": {\n    \"agenda\": \"Gündem\",\n    \"day\": \"Gün\",\n    \"month\": \"Ay\",\n    \"week\": \"Hafta\",\n    \"workWeek\": \"Çalışma Haftası\"\n  }\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"İptal Et\",\n  \"dropFilesHere\": \"Yüklemek için dosyaları buraya bırakın\",\n  \"headerStatusUploaded\": \"Tamamlandı\",\n  \"headerStatusUploading\": \"Yükleniyor\",\n  \"remove\": \"Kaldır\",\n  \"retry\": \"Tekrar Dene\",\n  \"select\": \"Seçiniz\",\n  \"statusFailed\": \"Başarısız Oldu\",\n  \"statusUploaded\": \"Yüklendi\",\n  \"statusUploading\": \"Yükleniyor\",\n  \"uploadSelectedFiles\": \"Seçilen dosyaları Yükle\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Kapat\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"Tamam\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"Tamam\",\n  \"cancel\": \"İptal\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"Tamam\",\n  \"cancel\": \"İptal\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}