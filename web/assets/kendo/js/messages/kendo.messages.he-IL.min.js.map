{"version": 3, "sources": ["messages/kendo.messages.he-IL.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "Validator", "prototype", "options", "messages", "extend", "required", "pattern", "min", "max", "step", "email", "url", "date", "dateCompare", "TreeView", "loading", "requestFailed", "retry", "Upload", "localization", "select", "cancel", "remove", "uploadSelectedFiles", "dropFilesHere", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "NumericTextBox", "upArrowText", "downArrowText", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "title", "unitsHeader", "save", "views", "day", "month", "week", "year", "FileBrowser", "uploadFile", "orderBy", "orderByName", "orderBySize", "directoryNotFound", "emptyFolder", "deleteFile", "invalidFileType", "overwriteFile", "search", "FlatColorPicker", "apply", "ColorPicker", "FilterMenu", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "number", "gte", "gt", "lte", "lt", "enums", "<PERSON><PERSON><PERSON>ell", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "Grid", "commands", "canceledit", "create", "edit", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "Pager", "allPages", "page", "display", "empty", "refresh", "itemsPerPage", "next", "previous", "morePages", "TreeListPager", "and", "clear", "info", "selectValue", "isFalse", "isTrue", "or", "operator", "value", "FilterMultiCheck", "checkAll", "Groupable", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "style", "subscript", "superscript", "underline", "unlink", "backColor", "foreColor", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "imageWebAddress", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "styles", "formatting", "viewHtml", "dialogUpdate", "insertFile", "Scheduler", "allDay", "allDayEvent", "description", "endTimezone", "repeat", "separateTimezones", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "agenda", "workWeek", "showFullDay", "showWorkDay", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAEVC,MAAMC,GAAGC,YACbF,MAAMC,GAAGC,UAAUC,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,UAAUC,UAAUC,QAAQC,UAClDE,SAAY,WACZC,QAAW,cACXC,IAAO,oCACPC,IAAO,mCACPC,KAAQ,cACRC,MAAS,sBACTC,IAAO,kBACPC,KAAQ,oBACRC,YAAe,kBAMbf,MAAMC,GAAGe,WACbhB,MAAMC,GAAGe,SAASb,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGe,SAASb,UAAUC,QAAQC,UACjDY,QAAW,cACXC,cAAiB,cACjBC,MAAS,cAMPnB,MAAMC,GAAGmB,SACbpB,MAAMC,GAAGmB,OAAOjB,UAAUC,QAAQiB,aAClCvB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmB,OAAOjB,UAAUC,QAAQiB,cAC/CC,OAAU,SACVC,OAAU,MACVJ,MAAS,WACTK,OAAU,MACVC,oBAAuB,wBACvBC,cAAiB,sBACjBC,gBAAmB,YACnBC,eAAkB,sBAClBC,cAAiB,QACjBC,aAAgB,OAChBC,sBAAyB,UACzBC,qBAAwB,iBAMtBhC,MAAMC,GAAGgC,SACbjC,MAAMC,GAAGgC,OAAO9B,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgC,OAAO9B,UAAUC,SACvC8B,oBAAuB,OACvBC,oBAAuB,UAMrBnC,MAAMC,GAAGmC,iBACbpC,MAAMC,GAAGmC,eAAejC,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmC,eAAejC,UAAUC,SAC/CiC,YAAe,QACfC,cAAiB,UAMftC,MAAMC,GAAGsC,QACbvC,MAAMC,GAAGsC,MAAMpC,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsC,MAAMpC,UAAUC,QAAQC,UAC9CmC,SACEC,SAAY,WACZC,OAAU,OACVC,YAAe,YACfC,aAAgB,YAChBC,IAAO,YAETtB,OAAU,MACVuB,4BAA+B,QAC/BC,sBAAyB,QACzBC,QAAW,SACXC,QACEC,aAAgB,OAChBC,YAAe,QACfC,IAAO,OACPC,gBAAmB,cACnBC,UAAa,SACbC,qBAAwB,aACxBC,gBAAmB,cACnBC,MAAS,OACTC,MAAS,QACTC,YAAe,QAEjBC,KAAQ,OACRC,OACEC,IAAO,MACPV,IAAO,MACPW,MAAS,OACTN,MAAS,QACTO,KAAQ,OACRC,KAAQ,UAORjE,MAAMC,GAAGiE,cACblE,MAAMC,GAAGiE,YAAY/D,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiE,YAAY/D,UAAUC,QAAQC,UACpD8D,WAAc,QACdC,QAAW,UACXC,YAAe,KACfC,YAAe,OACfC,kBAAqB,yBACrBC,YAAe,cACfC,WAAc,mBACdC,gBAAmB,mDACnBC,cAAiB,mDACjBjD,cAAiB,6BACjBkD,OAAU,SAOR5E,MAAMC,GAAG4E,kBACb7E,MAAMC,GAAG4E,gBAAgB1E,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4E,gBAAgB1E,UAAUC,QAAQC,UACxDyE,MAAS,OACTvD,OAAU,SAMRvB,MAAMC,GAAG8E,cACb/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpDyE,MAAS,OACTvD,OAAU,SAMRvB,MAAMC,GAAG+E,aACbhF,MAAMC,GAAG+E,WAAW7E,UAAUC,QAAQ6E,UACtCnF,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+E,WAAW7E,UAAUC,QAAQ6E,WACnDC,QACIC,GAAM,SACNC,IAAO,YACPC,WAAc,WACdC,SAAY,OACZC,eAAkB,UAClBC,SAAY,UAEhBC,QACIN,GAAM,SACNC,IAAO,YACPM,IAAO,iBACPC,GAAM,SACNC,IAAO,gBACPC,GAAM,SAEV/E,MACIqE,GAAM,SACNC,IAAO,YACPM,IAAO,iBACPC,GAAM,OACNC,IAAO,iBACPC,GAAM,QAEVC,OACIX,GAAM,OACNC,IAAO,cAOTpF,MAAMC,GAAG8F,aACb/F,MAAMC,GAAG8F,WAAW5F,UAAUC,QAAQ6E,UACtCnF,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8F,WAAW5F,UAAUC,QAAQ6E,WACnDC,QACIC,GAAM,SACNC,IAAO,YACPC,WAAc,WACdC,SAAY,OACZC,eAAkB,UAClBC,SAAY,UAEhBC,QACIN,GAAM,SACNC,IAAO,YACPM,IAAO,iBACPC,GAAM,SACNC,IAAO,gBACPC,GAAM,SAEV/E,MACIqE,GAAM,SACNC,IAAO,YACPM,IAAO,iBACPC,GAAM,OACNC,IAAO,iBACPC,GAAM,QAEVC,OACIX,GAAM,OACNC,IAAO,cAOTpF,MAAMC,GAAG+F,aACbhG,MAAMC,GAAG+F,WAAW7F,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+F,WAAW7F,UAAUC,QAAQC,UACnD4F,cAAiB,YACjBC,eAAkB,YAClBC,OAAU,QACVC,QAAW,SACXC,KAAQ,MACRC,SAAY,SACZC,KAAQ,MACRC,OAAU,UAMRxG,MAAMC,GAAGwG,mBACbzG,MAAMC,GAAGwG,iBAAiBtG,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwG,iBAAiBtG,UAAUC,QAAQC,UACzDqG,OACEC,SAAY,WACZC,YAAe,aAEjBxD,KACEyD,MAAS,QACTC,WAAc,WACdC,MAAS,QACTC,MAAS,SACTC,GAAM,KACNC,YAAe,QAEjBC,aACET,MAAS,OACTU,QAAW,QACXJ,MAAS,SACTK,OAAU,QACVC,OAAU,QAEZF,SACEtD,IAAO,OACP6C,SAAY,YACZC,YAAe,YACfW,SAAY,YAEdC,iBACEC,MAAS,QACTC,OAAS,QACTC,KAAQ,QACRC,OAAU,MACVC,MAAS,SAEXR,QACET,YAAe,YACfW,SAAY,WACZZ,SAAY,YAEdW,QACEQ,GAAM,MACNlB,YAAe,YACfW,SAAY,WACZZ,SAAY,YAEdoB,UACEjE,IAAO,MACPkE,QAAW,QACXC,QAAW,gBAOXjI,MAAMC,GAAGiI,OACblI,MAAMC,GAAGiI,KAAK/H,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiI,KAAK/H,UAAUC,QAAQC,UAC7C8H,UACE5G,OAAU,MACV6G,WAAc,YACdC,OAAU,UACVrF,QAAW,MACXsF,KAAQ,QACR1E,KAAQ,OACRtC,OAAU,MACViH,OAAU,QAEZC,UACEC,aAAgB,YAChBC,aAAgB,gCAChBC,cAAiB,gBAOjB3I,MAAMC,GAAG2I,QACb5I,MAAMC,GAAG2I,MAAMzI,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2I,MAAMzI,UAAUC,QAAQC,UAC9CwI,SAAY,MACZC,KAAQ,OACRC,QAAW,4BACXjB,GAAM,WACNkB,MAAS,mBACTC,QAAW,OACXxB,MAAS,eACTyB,aAAgB,eAChBvB,KAAQ,eACRwB,KAAQ,YACRC,SAAY,cACZC,UAAa,mBAMXrJ,MAAMC,GAAGqJ,gBACbtJ,MAAMC,GAAGqJ,cAAcnJ,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqJ,cAAcnJ,UAAUC,QAAQC,UACtDwI,SAAY,MACZC,KAAQ,OACRC,QAAW,4BACXjB,GAAM,WACNkB,MAAS,mBACTC,QAAW,OACXxB,MAAS,eACTyB,aAAgB,eAChBvB,KAAQ,eACRwB,KAAQ,YACRC,SAAY,cACZC,UAAa,mBAMXrJ,MAAMC,GAAG+E,aACbhF,MAAMC,GAAG+E,WAAW7E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+E,WAAW7E,UAAUC,QAAQC,UACnD8F,OAAU,MACVoD,IAAO,MACPC,MAAS,MACTC,KAAQ,uBACR/F,MAAS,uBACTgG,YAAe,YACfC,QAAU,KACVC,OAAU,KACVC,GAAM,KACNtI,OAAU,MACVuI,SAAY,UACZC,MAAS,SAMP/J,MAAMC,GAAG8F,aACb/F,MAAMC,GAAG8F,WAAW5F,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8F,WAAW5F,UAAUC,QAAQC,UACnD8F,OAAU,MACVqD,MAAS,MACTG,QAAW,KACXC,OAAU,KACVE,SAAY,aAMV9J,MAAMC,GAAG+J,mBACbhK,MAAMC,GAAG+J,iBAAiB7J,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+J,iBAAiB7J,UAAUC,QAAQC,UACzD4J,SAAY,UACZT,MAAS,MACTrD,OAAU,MACVvB,OAAU,SAMR5E,MAAMC,GAAGiK,YACblK,MAAMC,GAAGiK,UAAU/J,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiK,UAAU/J,UAAUC,QAAQC,UAChD2I,MAAS,gDAMThJ,MAAMC,GAAGkK,SACbnK,MAAMC,GAAGkK,OAAOhK,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkK,OAAOhK,UAAUC,QAAQC,UAC7C+J,KAAQ,QACRC,WAAc,cACdC,SAAY,qBACZC,gBAAmB,gBACnBC,SAAY,gBACZC,gBAAmB,gBACnBC,YAAe,QACfC,OAAU,SACVC,WAAc,aACdC,YAAe,cACfC,kBAAqB,sBACvBC,oBAAuB,wBACvBC,OAAU,OACVC,cAAiB,SACjBC,YAAe,QACfC,YAAe,aACfC,aAAgB,aAChBC,QAAW,OACXC,cAAiB,UACjBC,MAAS,QACTC,UAAa,YACbC,YAAe,YACfC,UAAa,WACbC,OAAU,aACVlH,WAAc,kCACdF,kBAAqB,2BACrBC,YAAe,cACfE,gBAAmB,sDACnBN,QAAW,YACXC,YAAe,KACfC,YAAe,OACfK,cAAiB,6DACjBR,WAAc,OACdyH,UAAa,UACbC,UAAa,MACbC,sBAAyB,KACzBC,aAAgB,QAChBC,aAAgB,QAChBC,aAAgB,iBAChBC,gBAAmB,gBACnBC,oBAAuB,0BACvBC,SAAY,OACZC,YAAe,UACfC,eAAkB,cAClB1H,OAAU,MACV2H,YAAe,WACf7K,cAAiB,kBACjB8K,cAAiB,mBACjBC,eAAkB,mBAClBC,YAAe,gBACfC,YAAe,iBACfC,aAAgB,YAChBC,UAAa,WACbC,OAAU,UACVC,WAAc,QACdC,SAAY,WACZC,aAAgB,OAChBC,WAAc,cAMZlN,MAAMC,GAAGmB,SACbpB,MAAMC,GAAGmB,OAAOjB,UAAUC,QAAQiB,aAClCvB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmB,OAAOjB,UAAUC,QAAQiB,cAC/CE,OAAU,MACVG,cAAiB,6BACjBF,OAAU,MACVL,MAAS,qBACTG,OAAU,QACVQ,aAAgB,cAChBF,eAAkB,gBAClBD,gBAAmB,iBACnBF,oBAAuB,mBACvBO,qBAAwB,OACxBD,sBAAyB,gBAMvB/B,MAAMC,GAAGkN,YACbnN,MAAMC,GAAGkN,UAAUhN,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkN,UAAUhN,UAAUC,QAAQC,UAClD+M,OAAU,SACV7L,OAAU,MACVT,KAAQ,QACRkC,QAAW,MACXwF,UACEE,aAAgB,2CAElBzF,QACEoK,YAAe,mBACfC,YAAe,QACfnK,YAAe,QACfC,IAAO,OACPmK,YAAe,iBACfC,OAAU,YACVC,kBAAqB,yCACrBhK,MAAS,QACTiK,cAAiB,YACjBC,SAAY,IACZC,qBAAwB,eACxBC,oBAAuB,eACvBnK,MAAS,QACToK,WAAc,iBAEhBC,MAAS,QACTC,oBACIC,gBAAmB,yDACrBC,uBAA0B,mBAC1BC,mBAAsB,kBACtBC,kBAAqB,mBACrBC,cAAiB,yDACjBC,qBAAwB,mBACxBC,iBAAoB,qBACpBC,gBAAmB,sBAErB5K,KAAQ,OACR6K,KAAQ,MACRC,MAAS,OACT7K,OACE8K,OAAU,UACV7K,IAAO,MACPC,MAAS,OACTC,KAAQ,OACR4K,SAAY,cAEdR,kBAAqB,QACrBS,YAAe,cACfC,YAAe,mBAMb9O,MAAMC,GAAG8O,SACb/O,MAAMC,GAAG8O,OAAO5O,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8O,OAAO5O,UAAUC,QAAQiB,cAC/C2N,MAAS,UAMPhP,MAAMC,GAAGgP,QACbjP,MAAMC,GAAGgP,MAAM9O,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgP,MAAM9O,UAAUC,QAAQiB,cAC9C6N,OAAU,UAMRlP,MAAMC,GAAGkP,UACbnP,MAAMC,GAAGkP,QAAQhP,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkP,QAAQhP,UAAUC,QAAQiB,cAChD6N,OAAU,OACV3N,OAAU,SAKRvB,MAAMC,GAAGmP,SACbpP,MAAMC,GAAGmP,OAAOjP,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmP,OAAOjP,UAAUC,QAAQiB,cAC/C6N,OAAU,OACV3N,OAAU,UAIT8N,OAAOrP,MAAMsP", "file": "kendo.messages.he-IL.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Validator */\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} נדרש\",\n  \"pattern\": \"{0} לא תקין\",\n  \"min\": \"{0} אמור להיות גדול או שווה ל {1}\",\n  \"max\": \"{0} אמור להיות קטן או שווה ל {1}\",\n  \"step\": \"{0} לא תקין\",\n  \"email\": \"{0}  אימייל לא תקין\",\n  \"url\": \"{0} לא תקין URL\",\n  \"date\": \"{0} תאריך לא תקין\",\n  \"dateCompare\": \"משווה נתונים\"\n});\n}\n\n/* TreeView */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"אנא המתן...\",\n  \"requestFailed\": \"הבקשה נכשלה\",\n  \"retry\": \"נסה שנית\"\n});\n}\n\n/* Upload */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"בחר...\",\n  \"cancel\": \"בטל\",\n  \"retry\": \"נסה שנית\",\n  \"remove\": \"הסר\",\n  \"uploadSelectedFiles\": \"בחר קבצים להעלות לשרת\",\n  \"dropFilesHere\": \"גרור לכאן את הקבצים\",\n  \"statusUploading\": \"מעלה לשרת\",\n  \"statusUploaded\": \"הקבצים הועלו בהצלחה\",\n  \"statusWarning\": \"אזהרה\",\n  \"statusFailed\": \"נכשל\",\n  \"headerStatusUploading\": \"מעלה...\",\n  \"headerStatusUploaded\": \"העלאת קבצים\"\n});\n}\n\n/* Slider */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"יותר\",\n  \"decreaseButtonTitle\": \"פחות\"\n});\n}\n\n/* Numeric text box */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"למעלה\",\n  \"downArrowText\": \"למטה\"\n});\n}\n\n/* Gantt */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"הוסף ילד\",\n    \"append\": \"הוסף\",\n    \"insertAfter\": \"הכנס אחרי\",\n    \"insertBefore\": \"הכנס לפני\",\n    \"pdf\": \"קובץ PDF\"\n  },\n  \"cancel\": \"בטל\",\n  \"deleteDependencyWindowTitle\": \"מחיקה\",\n  \"deleteTaskWindowTitle\": \"מחיקה\",\n  \"destroy\": \"Delete\",\n  \"editor\": {\n    \"assingButton\": \"שייך\",\n    \"editorTitle\": \"כותרת\",\n    \"end\": \"סיים\",\n    \"percentComplete\": \"אחוז שהושלם\",\n    \"resources\": \"משאבים\",\n    \"resourcesEditorTitle\": \"כותרת עורך\",\n    \"resourcesHeader\": \"כותרת כללית\",\n    \"start\": \"התחל\",\n    \"title\": \"כותרת\",\n    \"unitsHeader\": \"מידה\"\n  },\n  \"save\": \"שמור\",\n  \"views\": {\n    \"day\": \"יום\",\n    \"end\": \"סוף\",\n    \"month\": \"חודש\",\n    \"start\": \"התחלה\",\n    \"week\": \"שבוע\",\n    \"year\": \"שנה\"\n  }\n});\n}\n\n/* File browser */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"העלאה\",\n  \"orderBy\": \"סדר לפי\",\n  \"orderByName\": \"שם\",\n  \"orderBySize\": \"גודל\",\n  \"directoryNotFound\": \"תיקייה בשם זה לא נמצאה\",\n  \"emptyFolder\": \"תיקייה ריקה\",\n  \"deleteFile\": 'האם למחוק \"{0}\"?',\n  \"invalidFileType\": \"הקובץ הנבחר \\\"{0}\\\"לא חוקי, הקבצים הנתמכים הם {1}.\",\n  \"overwriteFile\": \"קובץ בשם \\\"{0}\\\" כבר קיים בתיקייה. האם לדרוס אותו?\",\n  \"dropFilesHere\": \"גרור קבצים לכאן כדי להעלות\",\n  \"search\": \"חפש\"\n});\n}\n\n\n/* Flat color picker */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"עדכן\",\n  \"cancel\": \"בטל\"\n});\n}\n\n/* Color picker */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"עדכן\",\n  \"cancel\": \"בטל\"\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n      \"eq\": \"שווה ל\",\n      \"neq\": \"לא שווה ל\",\n      \"startswith\": \"מתחיל עם\",\n      \"contains\": \"מכיל\",\n      \"doesnotcontain\": \"לא מכיל\",\n      \"endswith\": \"נגמר ב\"\n  },\n  \"number\": {\n      \"eq\": \"שווה ל\",\n      \"neq\": \"לא שווה ל\",\n      \"gte\": \"גדול או שווה מ\",\n      \"gt\": \"גדול מ\",\n      \"lte\": \"קטן או שווה ל\",\n      \"lt\": \"קטן מ\"\n  },\n  \"date\": {\n      \"eq\": \"שןןה ל\",\n      \"neq\": \"לא שווה ל\",\n      \"gte\": \"אחרי או שווה ל\",\n      \"gt\": \"אחרי\",\n      \"lte\": \"לפני או שווה ל\",\n      \"lt\": \"לפני\"\n  },\n  \"enums\": {\n      \"eq\": \"שווה\",\n      \"neq\": \"לא שווה\"\n  }\n});\n}\n\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n      \"eq\": \"שווה ל\",\n      \"neq\": \"לא שווה ל\",\n      \"startswith\": \"מתחיל עם\",\n      \"contains\": \"מכיל\",\n      \"doesnotcontain\": \"לא מכיל\",\n      \"endswith\": \"נגמר ב\"\n  },\n  \"number\": {\n      \"eq\": \"שווה ל\",\n      \"neq\": \"לא שווה ל\",\n      \"gte\": \"גדול או שווה מ\",\n      \"gt\": \"גדול מ\",\n      \"lte\": \"קטן או שווה ל\",\n      \"lt\": \"קטן מ\"\n  },\n  \"date\": {\n      \"eq\": \"שןןה ל\",\n      \"neq\": \"לא שווה ל\",\n      \"gte\": \"אחרי או שווה ל\",\n      \"gt\": \"אחרי\",\n      \"lte\": \"לפני או שווה ל\",\n      \"lt\": \"לפני\"\n  },\n  \"enums\": {\n      \"eq\": \"שווה\",\n      \"neq\": \"לא שווה\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"מיון עולה\",\n  \"sortDescending\": \"מיון יורד\",\n  \"filter\": \"סינון\",\n  \"columns\": \"עמודות\",\n  \"done\": \"בצע\",\n  \"settings\": \"הגדרות\",\n  \"lock\": \"קבע\",\n  \"unlock\": \"שחרר\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"מחזוריות\",\n    \"repeatEvery\": \"חזור כל: \"\n  },\n  \"end\": {\n    \"after\": \"לאחר \",\n    \"occurrence\": \"מחזוריות\",\n    \"label\": \"תגית:\",\n    \"never\": \"אף פעם\",\n    \"on\": \"ב \",\n    \"mobileLabel\": \"נייד\"\n  },\n  \"frequencies\": {\n    \"daily\": \"יומי\",\n    \"monthly\": \"חודשי\",\n    \"never\": \"אף פעם\",\n    \"weekly\": \"שבועי\",\n    \"yearly\": \"שנתי\"\n  },\n  \"monthly\": {\n    \"day\": \"יום \",\n    \"interval\": \" מחזוריות\",\n    \"repeatEvery\": \"חזרה כל: \",\n    \"repeatOn\": \"חזור ב: \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"ראשון\",\n    \"fourth\":\"רביעי\",\n    \"last\": \"אחרון\",\n    \"second\": \"שני\",\n    \"third\": \"שלישי\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"חזור כל: \",\n    \"repeatOn\": \"חזור ב: \",\n    \"interval\": \"מחזוריות\"\n  },\n  \"yearly\": {\n    \"of\": \" ב \",\n    \"repeatEvery\": \"חזור כל: \",\n    \"repeatOn\": \"חזור ב: \",\n    \"interval\": \"מחזוריות\"\n  },\n  \"weekdays\": {\n    \"day\": \"יום\",\n    \"weekday\": \"בשבוע\",\n    \"weekend\": \"בסוף שבוע\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"בטל\",\n    \"canceledit\": \"בטל עריכה\",\n    \"create\": \"צור חדש\",\n    \"destroy\": \"מחק\",\n    \"edit\": \"עריכה\",\n    \"save\": \"שמור\",\n    \"select\": \"בחר\",\n    \"update\": \"עדכן\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"בטל מחיקה\",\n    \"confirmation\": \"האם הנך בטוח שברונך לבצע זאת?\",\n    \"confirmDelete\": \"אשר מחיקה\"\n  }\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"page\": \"עמוד\",\n  \"display\": \"{0} - {1} מתוך {2} פריטים\",\n  \"of\": \"מתוך {0}\",\n  \"empty\": \"אין פריטים להצגה\",\n  \"refresh\": \"רענן\",\n  \"first\": \"לעמוד הראשון\",\n  \"itemsPerPage\": \"פריטים בעמוד\",\n  \"last\": \"לעמוד האחרון\",\n  \"next\": \"לעמוד הבא\",\n  \"previous\": \"לעמוד הקודם\",\n  \"morePages\": \"עמודים נוספים\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"page\": \"עמוד\",\n  \"display\": \"{0} - {1} מתוך {2} פריטים\",\n  \"of\": \"מתוך {0}\",\n  \"empty\": \"אין פריטים להצגה\",\n  \"refresh\": \"רענן\",\n  \"first\": \"לעמוד הראשון\",\n  \"itemsPerPage\": \"פריטים בעמוד\",\n  \"last\": \"לעמוד האחרון\",\n  \"next\": \"לעמוד הבא\",\n  \"previous\": \"לעמוד הקודם\",\n  \"morePages\": \"עמודים נוספים\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"filter\": \"סנן\",\n  \"and\": \"וגם\",\n  \"clear\": \"נקה\",\n  \"info\": \"הראה פריטים עם ערך ש\",\n  \"title\": \"הראה פריטים עם ערך ש\",\n  \"selectValue\": \"-בחר ערך-\",\n  \"isFalse\":\"לא\",\n  \"isTrue\": \"כן\",\n  \"or\": \"או\",\n  \"cancel\": \"בטל\",\n  \"operator\": \"אופרטור\",\n  \"value\": \"ערך\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"filter\": \"סנן\",\n  \"clear\": \"נקה\",\n  \"isFalse\": \"לא\",\n  \"isTrue\": \"כן\",\n  \"operator\": \"אופרטור\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"בחר הכל\",\n  \"clear\": \"נקה\",\n  \"filter\": \"סנן\",\n  \"search\": \"חפש\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n    \"empty\": \"כדי לקבץ לפי העמודה גרור כותרת העמודה לכאן\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n    \"bold\": \"מודגש\",\n    \"createLink\": \"הכנסת קישור\",\n    \"fontName\": \"בחירת משפחת גופנים\",\n    \"fontNameInherit\": \"(גופן בירושה)\",\n    \"fontSize\": \"בחר גודל פונט\",\n    \"fontSizeInherit\": \"(גודל בירושה)\",\n    \"formatBlock\": \"פורמט\",\n    \"indent\": \"Indent\",\n    \"insertHtml\": \"הכנסת HTML\",\n    \"insertImage\": \"הכנסת תמונה\",\n    \"insertOrderedList\": \"Insert ordered list\",\n  \"insertUnorderedList\": \"Insert unordered list\",\n  \"italic\": \"נטוי\",\n  \"justifyCenter\": \"ממורכז\",\n  \"justifyFull\": \"מיושר\",\n  \"justifyLeft\": \"ישור לשמאל\",\n  \"justifyRight\": \"ישור לימין\",\n  \"outdent\": \"הזחה\",\n  \"strikethrough\": \"קו חוצה\",\n  \"style\": \"סגנון\",\n  \"subscript\": \"ציון תחתי\",\n  \"superscript\": \"ציון עילי\",\n  \"underline\": \"קו תחתון\",\n  \"unlink\": \"הסרת קישור\",\n  \"deleteFile\": 'האם בוודאות ברצונך למחוק \"{0}\"?',\n  \"directoryNotFound\": \"תיקייה בשם זה כבר קיימת.\",\n  \"emptyFolder\": \"תיקייה ריקה\",\n  \"invalidFileType\": \"הקובץ הנבחר \\\"{0}\\\" אינו חוקי. סוגי קבצים נתמכים {1}.\",\n  \"orderBy\": \"מיון לפי:\",\n  \"orderByName\": \"שם\",\n  \"orderBySize\": \"גודל\",\n  \"overwriteFile\": 'קובץ עם השם \"{0}\" כבר קיים במחיצה הנוכחית. האם לדרוס אותו?',\n  \"uploadFile\": \"העלה\",\n  \"backColor\": \"צבע רקע\",\n  \"foreColor\": \"צבע\",\n  \"dialogButtonSeparator\": \"או\",\n  \"dialogCancel\": \"ביטול\",\n  \"dialogInsert\": \"הכנסה\",\n  \"imageAltText\": \"Alternate text\",\n  \"imageWebAddress\": \"כתובת אינטרנט\",\n  \"linkOpenInNewWindow\": \"Open link in new window\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkWebAddress\": \"Web address\",\n  \"search\": \"חפש\",\n  \"createTable\": \"צור טבלה\",\n  \"dropFilesHere\": \"גרור לכאן קבצים\",\n  \"addColumnLeft\": \"הוסף עמודה לשמאל\",\n  \"addColumnRight\": \"הוסף עמודה לימין\",\n  \"addRowAbove\": \"הוסף שורה מעל\",\n  \"addRowBelow\": \"הוסף שורה מתחת\",\n  \"deleteColumn\": \"מחק עמודה\",\n  \"deleteRow\": \"מחק שורה\",\n  \"styles\": \"סגנונות\",\n  \"formatting\": \"פורמט\",\n  \"viewHtml\": \"ראה HTML\",\n  \"dialogUpdate\": \"עדכן\",\n  \"insertFile\": \"צרף קובץ\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"בטל\",\n  \"dropFilesHere\": \"גרור קבצים לכאן כדי להעלות\",\n  \"remove\": \"הסר\",\n  \"retry\": \"חזור על הפעולה שוב\",\n  \"select\": \"העלאה\",\n  \"statusFailed\": \"הפעולה כשלה\",\n  \"statusUploaded\": \"הפעולה הצליחה\",\n  \"statusUploading\": \"מעלה את הקבצים\",\n  \"uploadSelectedFiles\": \"העלה קבצים שבחרת\",\n  \"headerStatusUploaded\": \"בוצע\",\n  \"headerStatusUploading\": \"מעלה קבצים\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"כל יום\",\n  \"cancel\": \"בטל\",\n  \"date\": \"תאריך\",\n  \"destroy\": \"מחק\",\n  \"editable\": {\n    \"confirmation\": \"האם הנך בטוח כי ברצונך למחוק אירוע זה ?\"\n  },\n  \"editor\": {\n    \"allDayEvent\": \"אירוע של יום שלם\",\n    \"description\": \"תיאור\",\n    \"editorTitle\": \"אירוע\",\n    \"end\": \"סיום\",\n    \"endTimezone\": \"After timezone\",\n    \"repeat\": \"ארוע חוזר\",\n    \"separateTimezones\": \"Use different time zones start and end\",\n    \"start\": \"התחלה\",\n    \"startTimezone\": \"זמן מקומי\",\n    \"timezone\": \" \",\n    \"timezoneEditorButton\": \"Classes zone\",\n    \"timezoneEditorTitle\": \"Classes zone\",\n    \"title\": \"כותרת\",\n    \"noTimezone\": \"No hours zone\"\n  },\n  \"event\": \"אירוע\",\n  \"recurrenceMessages\": {\n      \"deleteRecurring\": \"האם הנך בטוח כי ברצונך למחוק אירוע זה או את כל הסדרה ?\",\n    \"deleteWindowOccurrence\": \"מחיקת אירוע בודד\",\n    \"deleteWindowSeries\": \"מחק סדרת ארועים\",\n    \"deleteWindowTitle\": \"מחק אירוע מחזורי\",\n    \"editRecurring\": \"האם הנך בטוח כי ברצונך לשנות אירוע זה או את כל הסדרה ?\",\n    \"editWindowOccurrence\": \"שינוי אירוע בודד\",\n    \"editWindowSeries\": \"שינוי סדרת אירועים\",\n    \"editWindowTitle\": \"שינוי אירוע מחזורי\"\n  },\n  \"save\": \"שמור\",\n  \"time\": \"שעה\",\n  \"today\": \"היום\",\n  \"views\": {\n    \"agenda\": \"סדר יום\",\n    \"day\": \"יום\",\n    \"month\": \"חודש\",\n    \"week\": \"שבוע\",\n    \"workWeek\": \"שבוע עבודה\"\n  },\n  \"deleteWindowTitle\": \"מחיקה\",\n  \"showFullDay\": \"הצג יום מלא\",\n  \"showWorkDay\": \"הצג יום עבודה\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"קרוב\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"בסדר\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"בסדר\",\n  \"cancel\": \"בטל\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"בסדר\",\n  \"cancel\": \"בטל\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}