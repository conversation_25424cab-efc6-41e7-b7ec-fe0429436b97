{"version": 3, "sources": ["messages/kendo.messages.it-CH.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "enums", "FilterMenu", "ColumnMenu", "messages", "columns", "filter", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "clear", "isFalse", "isTrue", "operator", "and", "info", "title", "or", "selectValue", "cancel", "value", "FilterMultiCheck", "search", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "confirmation", "cancelDelete", "confirmDelete", "Groupable", "empty", "Pager", "allPages", "display", "itemsPerPage", "next", "page", "previous", "refresh", "TreeListPager", "Upload", "localization", "retry", "remove", "uploadSelectedFiles", "dropFilesHere", "statusFailed", "statusUploaded", "statusUploading", "headerStatusUploaded", "headerStatusUploading", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "deleteFile", "directoryNotFound", "emptyFolder", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "backColor", "foreColor", "imageWebAddress", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "viewHtml", "dialogUpdate", "insertFile", "insertFile1", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "noTimezone", "event", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "deleteRecurring", "editR<PERSON><PERSON>ring", "time", "today", "views", "agenda", "month", "week", "workWeek", "showFullDay", "showWorkDay", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,aACNC,GAAM,SACNC,IAAO,oBACPC,GAAM,UACNC,IAAO,qBACPC,IAAO,kBAETC,QACEN,GAAM,aACNC,GAAM,kBACNC,IAAO,0BACPC,GAAM,mBACNC,IAAO,2BACPC,IAAO,kBAETE,QACEC,SAAY,WACZC,eAAkB,eAClBC,SAAY,cACZV,GAAM,aACNK,IAAO,iBACPM,WAAc,cAEhBC,OACEZ,GAAM,aACNK,IAAO,qBAOPb,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,aACNC,GAAM,SACNC,IAAO,oBACPC,GAAM,UACNC,IAAO,qBACPC,IAAO,kBAETC,QACEN,GAAM,aACNC,GAAM,kBACNC,IAAO,0BACPC,GAAM,mBACNC,IAAO,2BACPC,IAAO,kBAETE,QACEC,SAAY,WACZC,eAAkB,eAClBC,SAAY,cACZV,GAAM,aACNK,IAAO,iBACPM,WAAc,cAEhBC,OACEZ,GAAM,aACNK,IAAO,qBAOPb,MAAMC,GAAGqB,aACbtB,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,UACnDC,QAAW,UACXC,OAAU,SACVC,cAAiB,sBACjBC,eAAkB,wBAClBC,SAAY,uBACZC,KAAQ,QACRC,KAAQ,WACRC,OAAU,eAMR/B,MAAMC,GAAG+B,mBACbhC,MAAMC,GAAG+B,iBAAiB7B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+B,iBAAiB7B,UAAUC,QAAQmB,UACzDU,OACEC,SAAY,YACZC,YAAe,iBAEjBC,KACEC,MAAS,OACTC,WAAc,gBACdC,MAAS,QACTC,MAAS,MACTC,GAAM,KACNC,YAAe,QAEjBC,aACEV,MAAS,cACTW,QAAW,YACXJ,MAAS,MACTK,OAAU,iBACVC,OAAU,aAEZF,SACEG,IAAO,SACPb,SAAY,UACZC,YAAe,gBACfa,SAAY,oBAEdC,iBACEC,MAAS,QACTC,OAAU,SACVC,KAAQ,SACRC,OAAU,UACVC,MAAS,SAEXT,QACEV,YAAe,eACfa,SAAY,iBACZd,SAAY,gBAEdY,QACES,GAAM,KACNpB,YAAe,eACfa,SAAY,iBACZd,SAAY,WAEdsB,UACET,IAAO,SACPU,QAAW,yBACXC,QAAW,2BAOX1D,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACnDoC,MAAS,UACTlC,OAAU,SACVmC,QAAW,UACXC,OAAU,SACVC,SAAY,eAMV9D,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACnDwC,IAAO,IACPJ,MAAS,UACTlC,OAAU,SACVuC,KAAQ,iCACRC,MAAS,iCACTL,QAAW,UACXC,OAAU,SACVK,GAAM,IACNC,YAAe,qBACfC,OAAU,UACVN,SAAY,YACZO,MAAS,YAMPrE,MAAMC,GAAGqE,mBACbtE,MAAMC,GAAGqE,iBAAiBnE,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqE,iBAAiBnE,UAAUC,QAAQmB,UACzDgD,OAAU,WAMRvE,MAAMC,GAAGuE,OACbxE,MAAMC,GAAGuE,KAAKrE,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuE,KAAKrE,UAAUC,QAAQmB,UAC7CkD,UACEC,WAAc,UACdN,OAAU,oBACVO,OAAU,0BACVC,QAAW,UACXC,KAAQ,OACRC,MAAS,kBACTC,IAAO,gBACPC,KAAQ,qBACRC,OAAU,YACVC,OAAU,YAEZC,UACEC,aAAgB,6CAChBC,aAAgB,UAChBC,cAAiB,cAOjBtF,MAAMC,GAAGsF,YACbvF,MAAMC,GAAGsF,UAAUpF,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsF,UAAUpF,UAAUC,QAAQmB,UAClDiE,MAAS,4FAMPxF,MAAMC,GAAGwF,QACbzF,MAAMC,GAAGwF,MAAMtF,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwF,MAAMtF,UAAUC,QAAQmB,UAC9CmE,SAAY,MACZC,QAAW,4BACXH,MAAS,kCACTtC,MAAS,wBACT0C,aAAgB,sBAChBxC,KAAQ,wBACRyC,KAAQ,2BACRtC,GAAM,SACNuC,KAAQ,SACRC,SAAY,6BACZC,QAAW,cAMThG,MAAMC,GAAGgG,gBACbjG,MAAMC,GAAGgG,cAAc9F,UAAUC,QAAQmB,SACzCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgG,cAAc9F,UAAUC,QAAQmB,UACtDmE,SAAY,MACZC,QAAW,4BACXH,MAAS,kCACTtC,MAAS,wBACT0C,aAAgB,sBAChBxC,KAAQ,wBACRyC,KAAQ,2BACRtC,GAAM,SACNuC,KAAQ,SACRC,SAAY,6BACZC,QAAW,cAMThG,MAAMC,GAAGiG,SACblG,MAAMC,GAAGiG,OAAO/F,UAAUC,QAAQ+F,aAClCrG,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiG,OAAO/F,UAAUC,QAAQ+F,cAC/C/B,OAAU,UACVgC,MAAS,UACTnB,OAAU,eACVoB,OAAU,UACVC,oBAAuB,8BACvBC,cAAiB,mCACjBC,aAAgB,UAChBC,eAAkB,oBAClBC,gBAAmB,kBACnBC,qBAAwB,QACxBC,sBAAyB,wBAMvB5G,MAAMC,GAAG4G,SACb7G,MAAMC,GAAG4G,OAAO1G,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4G,OAAO1G,UAAUC,QAAQmB,UAC/CuF,KAAQ,YACRC,WAAc,sBACdC,SAAY,iCACZC,gBAAmB,mBACnBC,SAAY,mCACZC,gBAAmB,yBACnBC,YAAe,kBACfC,OAAU,kBACVC,WAAc,iBACdC,YAAe,qBACfC,kBAAqB,2BACrBC,oBAAuB,+BACvBC,OAAU,SACVC,cAAiB,eACjBC,YAAe,mBACfC,YAAe,8BACfC,aAAgB,4BAChBC,QAAW,iBACXC,cAAiB,UACjBC,OAAU,QACVC,UAAa,WACbC,YAAe,WACfC,UAAa,eACbC,OAAU,oBACVC,WAAc,mCACdC,kBAAqB,wDACrBC,YAAe,iBACfC,gBAAmB,8EACnBC,QAAW,cACXC,YAAe,OACfC,YAAe,aACfC,cAAiB,qFACjBC,WAAc,SACdC,UAAa,gBACbC,UAAa,SACbzC,cAAiB,oCACjB0C,gBAAmB,gBACnBC,sBAAyB,IACzBC,aAAgB,UAChBC,aAAgB,YAChBC,aAAgB,oBAChBC,oBAAuB,kCACvBC,SAAY,QACZC,YAAe,UACfC,eAAkB,gBAClBlF,OAAU,QACVmF,YAAe,eACfC,cAAiB,8BACjBC,eAAkB,4BAClBC,YAAe,sBACfC,YAAe,sBACfC,aAAgB,kBAChBC,UAAa,eACbC,SAAY,YACZC,aAAgB,SAChBC,WAAc,cACdC,YAAe,iBAMbpK,MAAMC,GAAGoK,YACbrK,MAAMC,GAAGoK,UAAUlK,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoK,UAAUlK,UAAUC,QAAQmB,UAClD+I,OAAU,kBACVlG,OAAU,UACVe,UACEC,aAAgB,4CAElB7E,KAAQ,OACRqE,QAAW,UACX2F,QACEC,YAAe,kBACfC,YAAe,cACfC,YAAe,SACftI,IAAO,OACPuI,YAAe,qBACfC,OAAU,SACVC,kBAAqB,mDACrBC,MAAS,SACTC,cAAiB,uBACjBC,SAAY,uBACZC,qBAAwB,cACxBC,oBAAuB,aACvBjH,MAAS,SACTkH,WAAc,eAEhBC,MAAS,SACTC,oBACEC,uBAA0B,4BAC1BC,mBAAsB,mBACtBC,kBAAqB,8BACrBC,qBAAwB,6BACxBC,iBAAoB,oBACpBC,gBAAmB,+BACnBC,gBAAmB,yDACnBC,cAAiB,0DAEnB7G,KAAQ,QACR8G,KAAQ,QACRC,MAAS,OACTC,OACEC,OAAU,SACVlJ,IAAO,SACPmJ,MAAS,OACTC,KAAQ,YACRC,SAAY,aAEdZ,kBAAqB,iBACrBa,YAAe,4BACfC,YAAe,mCAMbtM,MAAMC,GAAGsM,SACbvM,MAAMC,GAAGsM,OAAOpM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsM,OAAOpM,UAAUC,QAAQ+F,cAC/CqG,MAAS,YAMPxM,MAAMC,GAAGwM,QACbzM,MAAMC,GAAGwM,MAAMtM,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwM,MAAMtM,UAAUC,QAAQ+F,cAC9CuG,OAAU,QAMR1M,MAAMC,GAAG0M,UACb3M,MAAMC,GAAG0M,QAAQxM,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0M,QAAQxM,UAAUC,QAAQ+F,cAChDuG,OAAU,KACVtI,OAAU,aAKRpE,MAAMC,GAAG2M,SACb5M,MAAMC,GAAG2M,OAAOzM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,OAAOzM,UAAUC,QAAQ+F,cAC/CuG,OAAU,KACVtI,OAAU,cAITyI,OAAO7M,MAAM8M", "file": "kendo.messages.it-CH.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"È uguale a\",\n    \"gt\": \"È dopo\",\n    \"gte\": \"È dopo o uguale a\",\n    \"lt\": \"È prima\",\n    \"lte\": \"È prima o uguale a\",\n    \"neq\": \"Non è uguale a\"\n  },\n  \"number\": {\n    \"eq\": \"È uguale a\",\n    \"gt\": \"È più grande di\",\n    \"gte\": \"È più grande o uguale a\",\n    \"lt\": \"È più piccolo di\",\n    \"lte\": \"È più piccolo o uguale a\",\n    \"neq\": \"Non è uguale a\"\n  },\n  \"string\": {\n    \"contains\": \"Contiene\",\n    \"doesnotcontain\": \"Non contiene\",\n    \"endswith\": \"Finisce con\",\n    \"eq\": \"È uguale a\",\n    \"neq\": \"Non è uguale a\",\n    \"startswith\": \"Inizia con\"\n  },\n  \"enums\": {\n    \"eq\": \"È uguale a\",\n    \"neq\": \"Non è uguale a\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"È uguale a\",\n    \"gt\": \"È dopo\",\n    \"gte\": \"È dopo o uguale a\",\n    \"lt\": \"È prima\",\n    \"lte\": \"È prima o uguale a\",\n    \"neq\": \"Non è uguale a\"\n  },\n  \"number\": {\n    \"eq\": \"È uguale a\",\n    \"gt\": \"È più grande di\",\n    \"gte\": \"È più grande o uguale a\",\n    \"lt\": \"È più piccolo di\",\n    \"lte\": \"È più piccolo o uguale a\",\n    \"neq\": \"Non è uguale a\"\n  },\n  \"string\": {\n    \"contains\": \"Contiene\",\n    \"doesnotcontain\": \"Non contiene\",\n    \"endswith\": \"Finisce con\",\n    \"eq\": \"È uguale a\",\n    \"neq\": \"Non è uguale a\",\n    \"startswith\": \"Inizia con\"\n  },\n  \"enums\": {\n    \"eq\": \"È uguale a\",\n    \"neq\": \"Non è uguale a\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Colonne\",\n  \"filter\": \"Filtro\",\n  \"sortAscending\": \"In ordine crescente\",\n  \"sortDescending\": \"In ordine decrescente\",\n  \"settings\": \"Impostazioni colonna\",\n  \"done\": \"Fatto\",\n  \"lock\": \"Bloccare\",\n  \"unlock\": \"Sbloccare\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"giorno(i)\",\n    \"repeatEvery\": \"Ripeti ogni: \"\n  },\n  \"end\": {\n    \"after\": \"Dopo\",\n    \"occurrence\": \"Occorrenza(e)\",\n    \"label\": \"Fine:\",\n    \"never\": \"Mai\",\n    \"on\": \"Il\",\n    \"mobileLabel\": \"Ends\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Ogni giorno\",\n    \"monthly\": \"Ogni mese\",\n    \"never\": \"Mai\",\n    \"weekly\": \"Ogni settimana\",\n    \"yearly\": \"Ogni anno\"\n  },\n  \"monthly\": {\n    \"day\": \"Giorno\",\n    \"interval\": \"mese(i)\",\n    \"repeatEvery\": \"Ripeti ogni: \",\n    \"repeatOn\": \"Repeti quando:: \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"primo\",\n    \"fourth\": \"quarto\",\n    \"last\": \"ultimo\",\n    \"second\": \"secondo\",\n    \"third\": \"terzo\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Ripeti ogni:\",\n    \"repeatOn\": \"Ripeti quando:\",\n    \"interval\": \"settimana(e)\"\n  },\n  \"yearly\": {\n    \"of\": \"di\",\n    \"repeatEvery\": \"Ripeti ogni:\",\n    \"repeatOn\": \"Ripeti quando:\",\n    \"interval\": \"anno(i)\"\n  },\n  \"weekdays\": {\n    \"day\": \"giorno\",\n    \"weekday\": \"giorno della settimana\",\n    \"weekend\": \"giorno finesettimana\"\n  }\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"clear\": \"Rimuovi\",\n  \"filter\": \"Filtro\",\n  \"isFalse\": \"è falso\",\n  \"isTrue\": \"è vero\",\n  \"operator\": \"Operatore\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"and\": \"E\",\n  \"clear\": \"Rimuovi\",\n  \"filter\": \"Filtro\",\n  \"info\": \"Mostra elementi il cui valore:\",\n  \"title\": \"Mostra elementi il cui valore:\",\n  \"isFalse\": \"è falso\",\n  \"isTrue\": \"è vero\",\n  \"or\": \"O\",\n  \"selectValue\": \"-Seleziona valore-\",\n  \"cancel\": \"Annulla\",\n  \"operator\": \"Operatore\",\n  \"value\": \"Valore\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"Cerca\"\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"canceledit\": \"Annulla\",\n    \"cancel\": \"Annulla modifiche\",\n    \"create\": \"Aggiungi nuovo elemento\",\n    \"destroy\": \"Rimuovi\",\n    \"edit\": \"Edit\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"save\": \"Salva le modifiche\",\n    \"select\": \"Seleziona\",\n    \"update\": \"Aggiorna\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Sicuro di voler rimuovere questo elemento?\",\n    \"cancelDelete\": \"Annulla\",\n    \"confirmDelete\": \"Rimuovi\"\n  }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Trascina l'header di una colonna e rilascialo qui per raggruppare secondo tale colonna\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} di {2} elementi\",\n  \"empty\": \"Nessun elemento da visualizzare\",\n  \"first\": \"Vai alla prima pagina\",\n  \"itemsPerPage\": \"elementi per pagina\",\n  \"last\": \"Vai all'ultima pagina\",\n  \"next\": \"Vai alla prossima pagina\",\n  \"of\": \"di {0}\",\n  \"page\": \"Pagina\",\n  \"previous\": \"Vai alla pagina precedente\",\n  \"refresh\": \"Aggiorna\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} di {2} elementi\",\n  \"empty\": \"Nessun elemento da visualizzare\",\n  \"first\": \"Vai alla prima pagina\",\n  \"itemsPerPage\": \"elementi per pagina\",\n  \"last\": \"Vai all'ultima pagina\",\n  \"next\": \"Vai alla prossima pagina\",\n  \"of\": \"di {0}\",\n  \"page\": \"Pagina\",\n  \"previous\": \"Vai alla pagina precedente\",\n  \"refresh\": \"Aggiorna\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Annulla\",\n  \"retry\": \"Riprova\",\n  \"select\": \"Seleziona...\",\n  \"remove\": \"Rimuovi\",\n  \"uploadSelectedFiles\": \"Upload dei file selezionati\",\n  \"dropFilesHere\": \"rilascia qui i file per l'upload\",\n  \"statusFailed\": \"fallito\",\n  \"statusUploaded\": \"upload effettuato\",\n  \"statusUploading\": \"upload in corso\",\n  \"headerStatusUploaded\": \"Fatto\",\n  \"headerStatusUploading\": \"Upload in corso...\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Grassetto\",\n  \"createLink\": \"Inserisci hyperlink\",\n  \"fontName\": \"Seleziona una famiglia di font\",\n  \"fontNameInherit\": \"(font ereditato)\",\n  \"fontSize\": \"Seleziona la dimensione del font\",\n  \"fontSizeInherit\": \"(dimensione ereditata)\",\n  \"formatBlock\": \"Formatta blocco\",\n  \"indent\": \"Aumenta rientro\",\n  \"insertHtml\": \"Inserisci HTML\",\n  \"insertImage\": \"Inserisci immagine\",\n  \"insertOrderedList\": \"Inserisci lista ordinata\",\n  \"insertUnorderedList\": \"Inserisci lista non ordinata\",\n  \"italic\": \"Italic\",\n  \"justifyCenter\": \"Centra testo\",\n  \"justifyFull\": \"Giustifica testo\",\n  \"justifyLeft\": \"Allinea il testo a sinistra\",\n  \"justifyRight\": \"Allinea il testo a destra\",\n  \"outdent\": \"Riduci rientro\",\n  \"strikethrough\": \"Barrato\",\n  \"styles\": \"Stili\",\n  \"subscript\": \"A pedice\",\n  \"superscript\": \"In apice\",\n  \"underline\": \"Sottolineato\",\n  \"unlink\": \"Rimuovi hyperlink\",\n  \"deleteFile\": \"Sicuro di voler rimuovere \\\"{0}\\\"?\",\n  \"directoryNotFound\": \"Non è stata trovata alcuna directory con questo nome.\",\n  \"emptyFolder\": \"Cartella vuota\",\n  \"invalidFileType\": \"Il file selezionato \\\"{0}\\\" non è valido. I tipi di file supportati sono {1}.\",\n  \"orderBy\": \"Ordina per:\",\n  \"orderByName\": \"Nome\",\n  \"orderBySize\": \"Dimensione\",\n  \"overwriteFile\": \"'Un file con nome \\\"{0}\\\" esiste già nella directory corrente. Vuoi sovrascriverlo?\",\n  \"uploadFile\": \"Upload\",\n  \"backColor\": \"Colore sfondo\",\n  \"foreColor\": \"Colore\",\n  \"dropFilesHere\": \"rilascia qui i files per l'upload\",\n  \"imageWebAddress\": \"Indirizzo Web\",\n  \"dialogButtonSeparator\": \"o\",\n  \"dialogCancel\": \"Annulla\",\n  \"dialogInsert\": \"Inserisci\",\n  \"imageAltText\": \"Testo alternativo\",\n  \"linkOpenInNewWindow\": \"Apri link in una nuova finestra\",\n  \"linkText\": \"Testo\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkWebAddress\": \"Indirizzo Web\",\n  \"search\": \"Cerca\",\n  \"createTable\": \"Crea tabella\",\n  \"addColumnLeft\": \"Aggiungi colonna a sinistra\",\n  \"addColumnRight\": \"Aggiungi colonna a destra\",\n  \"addRowAbove\": \"Aggiungi riga sopra\",\n  \"addRowBelow\": \"Aggiungi riga sotto\",\n  \"deleteColumn\": \"Rimuovi colonna\",\n  \"deleteRow\": \"Rimuovi riga\",\n  \"viewHtml\": \"View HTML\",\n  \"dialogUpdate\": \"Update\",\n  \"insertFile\": \"Insert file\",\n  \"insertFile1\": \"Insert file\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"tutto il giorno\",\n  \"cancel\": \"Annulla\",\n  \"editable\": {\n    \"confirmation\": \"Sicuro di voler rimuovere questo evento?\"\n  },\n  \"date\": \"Data\",\n  \"destroy\": \"Rimuovi\",\n  \"editor\": {\n    \"allDayEvent\": \"Giornata intera\",\n    \"description\": \"Descrizione\",\n    \"editorTitle\": \"Evento\",\n    \"end\": \"Fine\",\n    \"endTimezone\": \"Fuso orario finale\",\n    \"repeat\": \"Ripeti\",\n    \"separateTimezones\": \"Usa differenti fusi orari per l'inizio e la fine\",\n    \"start\": \"Inizio\",\n    \"startTimezone\": \"Fuso orario iniziale\",\n    \"timezone\": \"Modifica fuso orario\",\n    \"timezoneEditorButton\": \"Fuso orario\",\n    \"timezoneEditorTitle\": \"Fusi orari\",\n    \"title\": \"Titolo\",\n    \"noTimezone\": \"No timezone\"\n  },\n  \"event\": \"Evento\",\n  \"recurrenceMessages\": {\n    \"deleteWindowOccurrence\": \"Rimuovi questa accorrenza\",\n    \"deleteWindowSeries\": \"Rimuovi la serie\",\n    \"deleteWindowTitle\": \"Rimuovi elemento ricorrente\",\n    \"editWindowOccurrence\": \"Modifica l'evento corrente\",\n    \"editWindowSeries\": \"Modifica la serie\",\n    \"editWindowTitle\": \"Modifica elemento ricorrente\",\n    \"deleteRecurring\": \"Vuoi rimuovere solo questo evento o la serie completa?\",\n    \"editRecurring\": \"Vuoi modifcare solo questo evento o la serie completa?\"\n  },\n  \"save\": \"Salva\",\n  \"time\": \"Tempo\",\n  \"today\": \"Oggi\",\n  \"views\": {\n    \"agenda\": \"Agenda\",\n    \"day\": \"Giorno\",\n    \"month\": \"Mese\",\n    \"week\": \"Settimana\",\n    \"workWeek\": \"Work Week\"\n  },\n  \"deleteWindowTitle\": \"Rimuovi evento\",\n  \"showFullDay\": \"Mostra il giorno completo\",\n  \"showWorkDay\": \"Mostra solo le ore lavorative\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Vicino\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Annulla\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Annulla\"\n});\n}\n\n})(window.kendo.jQuery);}));"]}