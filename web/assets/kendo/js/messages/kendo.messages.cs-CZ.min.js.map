{"version": 3, "sources": ["messages/kendo.messages.cs-CZ.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "noColor", "clearColor", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "overflowAnchor", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "cleanFormatting", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "units", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "isnullorempty", "isnotnullorempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "expandCollapseColumnHeader", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "confirmationDialog", "text", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "saveAsDialog", "fileName", "saveAsType", "exportAsDialog", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "toolbar", "alignmentButtons", "backgroundColor", "borders", "colorPicker", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "ListBox", "tools", "moveUp", "moveDown", "transferTo", "transferFrom", "transferAllTo", "transferAllFrom", "TreeView", "Upload", "localization", "clearSelectedFiles", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "progress", "Dialog", "close", "Calendar", "weekColumnHeader", "<PERSON><PERSON>", "Confirm", "Prompt", "DateInput", "hour", "minute", "dayperiod", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,WACTC,OAAU,SACVC,QAAW,YACXC,WAAc,kBAMZV,MAAMC,GAAGU,cACbX,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,UACpDE,MAAS,WACTC,OAAU,SACVC,QAAW,YACXC,WAAc,kBAMZV,MAAMC,GAAGW,aACbZ,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,UACnDQ,cAAiB,mBACjBC,eAAkB,kBAClBC,OAAU,QACVC,QAAW,UACXC,KAAQ,SACRC,SAAY,oBACZC,KAAQ,WACRC,OAAU,eAMRpB,MAAMC,GAAGoB,SACbrB,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,UAC/CiB,KAAQ,QACRC,OAAU,UACVC,UAAa,YACbC,cAAiB,cACjBC,YAAe,cACfC,UAAa,cACbC,cAAiB,oBACjBC,YAAe,iBACfC,aAAgB,kBAChBC,YAAe,oBACfC,oBAAuB,0BACvBC,kBAAqB,0BACrBC,OAAU,mBACVC,QAAW,mBACXC,WAAc,eACdC,OAAU,eACVC,YAAe,iBACfC,WAAc,gBACdC,WAAc,cACdC,SAAY,gBACZC,SAAY,gBACZC,gBAAmB,kBACnBC,SAAY,yBACZC,gBAAmB,qBACnBC,YAAe,SACfC,WAAc,cACdC,UAAa,QACbC,UAAa,eACbC,MAAS,QACTC,YAAe,iBACfC,WAAc,SACdC,eAAkB,iBAClBC,QAAW,eACXC,YAAe,YACfC,YAAe,QACfC,gBAAmB,kFACnBC,WAAc,+BACdC,cAAiB,qFACjBC,kBAAqB,wCACrBC,gBAAmB,QACnBC,aAAgB,oBAChBC,WAAc,aACdC,YAAe,aACfC,eAAkB,aAClBC,UAAa,QACbC,eAAkB,QAClBC,SAAY,OACZC,YAAe,iBACfC,oBAAuB,6BACvBC,aAAgB,eAChBC,aAAgB,SAChBC,sBAAyB,OACzBC,aAAgB,SAChBC,gBAAmB,sBACnBC,YAAe,iBACfC,cAAiB,uBACjBC,eAAkB,wBAClBC,YAAe,mBACfC,YAAe,mBACfC,UAAa,eACbC,aAAgB,gBAChBC,SAAY,KACZC,YAAe,oBACfC,SAAY,UACZC,QAAW,QACXC,iBAAoB,YACpBC,QAAW,UACXC,QAAW,SACXC,MAAS,QACTC,OAAU,QACVC,MAAS,WACTC,YAAe,sBACfC,YAAe,wBACfC,WAAc,eACdC,UAAa,YACbC,WAAc,SACdC,SAAY,YACZC,GAAM,KACNC,OAAU,aACVC,YAAe,kBACfC,gBAAmB,oBACnBC,SAAY,iBACZC,0BAA6B,4BAC7BC,UAAa,kBACbC,YAAe,oBACfC,WAAc,mBACdC,aAAgB,yBAChBC,eAAkB,2BAClBC,cAAiB,0BACjBC,gBAAmB,2BACnBC,kBAAqB,oBACrBC,iBAAoB,4BACpBC,gBAAmB,uBACnBC,kBAAqB,yBACrBC,iBAAoB,wBACpBC,YAAe,sBACfrG,QAAW,UACXsG,KAAQ,QACRC,eAAkB,0BAMhBvH,MAAMC,GAAGuH,cACbxH,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,UACpD+C,WAAc,SACdE,QAAW,gBACXE,YAAe,QACfD,YAAe,WACfK,kBAAqB,wCACrBT,YAAe,iBACfO,WAAc,yCACdD,gBAAmB,yDACnBE,cAAiB,0EACjB8D,cAAiB,iCACjBC,OAAU,YAMR1H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,UACnDuH,OAAU,YACVC,QAAW,cACX9G,OAAU,YACV+G,MAAS,SACTC,SAAY,cAMV/H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,cACNC,IAAO,cACPC,WAAc,YACdC,SAAY,WACZC,eAAkB,aAClBC,SAAY,WACZC,OAAU,UACVC,UAAa,YACbC,QAAW,aACXC,WAAc,eACdC,cAAiB,eACjBC,iBAAoB,cAEtBC,QACEZ,GAAM,WACNC,IAAO,cACPY,IAAO,sBACPC,GAAM,eACNC,IAAO,sBACPC,GAAM,eACNV,OAAU,UACVC,UAAa,aAEfU,MACEjB,GAAM,WACNC,IAAO,cACPY,IAAO,YACPC,GAAM,YACNC,IAAO,WACPC,GAAM,WACNV,OAAU,UACVC,UAAa,aAEfW,OACElB,GAAM,WACNC,IAAO,cACPK,OAAU,UACVC,UAAa,gBAObzI,MAAMC,GAAGoJ,aACbrJ,MAAMC,GAAGoJ,WAAWlJ,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoJ,WAAWlJ,UAAUC,QAAQC,UACnDiJ,KAAQ,sCACRC,MAAS,qCACT3B,OAAU,YACVC,QAAW,cACX9G,OAAU,YACV+G,MAAS,SACT0B,IAAO,YACPC,GAAM,OACNC,YAAe,oBACf3B,SAAY,WACZ4B,MAAS,UACTnJ,OAAU,YAMRR,MAAMC,GAAGoJ,aACbrJ,MAAMC,GAAGoJ,WAAWlJ,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoJ,WAAWlJ,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,cACNC,IAAO,cACPC,WAAc,YACdC,SAAY,WACZC,eAAkB,aAClBC,SAAY,WACZC,OAAU,UACVC,UAAa,YACbC,QAAW,aACXC,WAAc,eACdC,cAAiB,eACjBC,iBAAoB,cAEtBC,QACEZ,GAAM,WACNC,IAAO,cACPY,IAAO,sBACPC,GAAM,eACNC,IAAO,sBACPC,GAAM,eACNV,OAAU,UACVC,UAAa,aAEfU,MACEjB,GAAM,WACNC,IAAO,cACPY,IAAO,YACPC,GAAM,YACNC,IAAO,WACPC,GAAM,WACNV,OAAU,UACVC,UAAa,aAEfW,OACElB,GAAM,WACNC,IAAO,cACPK,OAAU,UACVC,UAAa,gBAObzI,MAAMC,GAAG2J,mBACb5J,MAAMC,GAAG2J,iBAAiBzJ,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,iBAAiBzJ,UAAUC,QAAQC,UACzDwJ,SAAY,aACZ/B,MAAS,UACT/G,OAAU,QACV2G,OAAU,YAMR1H,MAAMC,GAAG6J,QACb9J,MAAMC,GAAG6J,MAAM3J,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6J,MAAM3J,UAAUC,QAAQC,UAC9C0J,SACEC,SAAY,iBACZC,OAAU,cACVC,YAAe,aACfC,aAAgB,aAChBC,IAAO,iBAET5J,OAAU,SACV6J,4BAA+B,mBAC/BC,sBAAyB,cACzBC,QAAW,SACXC,QACEC,aAAgB,WAChBC,YAAe,OACfC,IAAO,QACPC,gBAAmB,SACnBC,UAAa,SACbC,qBAAwB,SACxBC,gBAAmB,SACnBC,MAAS,UACTzB,MAAS,QACT0B,YAAe,YAEjBC,KAAQ,SACRC,OACEC,IAAO,MACPT,IAAO,QACPU,MAAS,QACTL,MAAS,UACTM,KAAQ,QACRC,KAAQ,UAORvL,MAAMC,GAAGuL,OACbxL,MAAMC,GAAGuL,KAAKrL,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuL,KAAKrL,UAAUC,QAAQC,UAC7CoL,UACEjL,OAAU,SACVkL,WAAc,SACdC,OAAU,qBACVpB,QAAW,SACXqB,KAAQ,UACRC,MAAS,eACTzB,IAAO,aACPc,KAAQ,eACRY,OAAU,SACVC,OAAU,UAEZC,UACEC,aAAgB,SAChBC,aAAgB,sCAChBC,cAAiB,UAEnBC,UAAa,0BACbC,2BAA8B,MAM5BrM,MAAMC,GAAGqM,WACbtM,MAAMC,GAAGqM,SAASnM,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqM,SAASnM,UAAUC,QAAQC,UAC/CkM,OAAU,4BACVC,QAAW,aACXC,cAAiB,oBACjBC,MAAS,eACTjB,UACIG,KAAQ,UACRG,OAAU,eACVL,WAAc,SACdC,OAAU,qBACVgB,YAAe,qBACfpC,QAAW,SACXsB,MAAS,eACTzB,IAAO,iBAOXpK,MAAMC,GAAG2M,YACb5M,MAAMC,GAAG2M,UAAUzM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,UAAUzM,UAAUC,QAAQC,UAClDwM,MAAS,yEAMP7M,MAAMC,GAAG6M,iBACb9M,MAAMC,GAAG6M,eAAe3M,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6M,eAAe3M,UAAUC,SAC/C2M,YAAe,UACfC,cAAiB,aAMfhN,MAAMC,GAAGgN,cACbjN,MAAMC,GAAGgN,YAAY9M,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgN,YAAY9M,UAAUC,QAAQC,UACpD6M,MAAS,QACTC,KAAQ,UACRC,KAAQ,UACRC,OAAU,kBACVC,QAAW,UACXC,WAAc,wBAMZvN,MAAMC,GAAGuN,QACbxN,MAAMC,GAAGuN,MAAMrN,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuN,MAAMrN,UAAUC,QAAQC,UAC9CoN,SAAY,MACZC,QAAW,yBACXb,MAAS,yBACTc,KAAQ,SACRC,GAAM,QACNC,aAAgB,qBAChBC,MAAS,mBACTC,SAAY,uBACZC,KAAQ,mBACRC,KAAQ,sBACRC,QAAW,UACXC,UAAa,mBAMXnO,MAAMC,GAAGmO,gBACTpO,MAAMC,GAAGmO,cAAcjO,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmO,cAAcjO,UAAUC,QAAQC,UACtDoN,SAAY,MACZC,QAAW,yBACXb,MAAS,yBACTc,KAAQ,SACRC,GAAM,QACNC,aAAgB,qBAChBC,MAAS,mBACTC,SAAY,uBACZC,KAAQ,mBACRC,KAAQ,sBACRC,QAAW,UACXC,UAAa,mBAMfnO,MAAMC,GAAGoO,YACbrO,MAAMC,GAAGoO,UAAUlO,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoO,UAAUlO,UAAUC,QAAQC,UAClDiO,cAAiB,sBACjBC,aAAgB,yBAChBC,UAAa,0BAMXxO,MAAMC,GAAGwO,iBACbzO,MAAMC,GAAGwO,eAAetO,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwO,eAAetO,UAAUC,QAAQC,UACvDiJ,KAAQ,+BACRoF,aAAgB,QAChB3N,OAAU,QACV4N,QAAW,mBACXpF,MAAS,kBACTzB,MAAS,WACT8G,GAAM,KACNpO,OAAU,SACVwH,WACEK,SAAY,WACZC,eAAkB,aAClBF,WAAc,YACdG,SAAY,WACZL,GAAM,WACNC,IAAO,iBAOPnI,MAAMC,GAAG4O,mBACb7O,MAAMC,GAAG4O,iBAAiB1O,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4O,iBAAiB1O,UAAUC,QAAQC,UACzDyO,aACEC,MAAS,QACTC,OAAU,gBACVC,MAAS,QACTC,OAAU,QACVC,QAAW,UACXC,OAAU,SAEZJ,QACEK,YAAe,qBACfC,SAAY,UAEdL,OACEI,YAAe,kBACfC,SAAY,OAEdJ,QACEI,SAAY,YACZD,YAAe,kBACfE,SAAY,eAEdJ,SACEE,YAAe,kBACfE,SAAY,cACZD,SAAY,WACZlE,IAAO,OAETgE,QACEC,YAAe,kBACfE,SAAY,cACZD,SAAY,SACZ1B,GAAM,OAERjD,KACE6E,MAAS,SACTC,YAAe,QACfV,MAAS,QACTW,MAAS,WACTC,WAAc,YACdC,GAAM,OAERC,iBACE/B,MAAS,QACTgC,OAAU,QACVC,MAAS,QACTC,OAAU,SACV/B,KAAQ,YAGVgC,UACE7E,IAAO,MACP8E,QAAW,eACXC,QAAW,aAOXnQ,MAAMC,GAAGmQ,YACbpQ,MAAMC,GAAGmQ,UAAUjQ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmQ,UAAUjQ,UAAUC,QAAQC,UAClDgQ,OAAU,WACVlH,KAAQ,QACRmH,MAAS,UACTC,KAAQ,MACRC,YAAe,oBACfC,YAAe,yBACfC,MAAS,OACTxF,KAAQ,SACR1K,OAAU,SACV+J,QAAW,SACXoG,kBAAqB,iBACrBC,cAAiB,4BACjBC,eAAkB,wBAClB7E,UACEE,aAAgB,uCAElBf,OACEC,IAAO,MACPE,KAAQ,QACRwF,SAAY,iBACZC,OAAU,SACV1F,MAAS,SAEX2F,oBACEL,kBAAqB,4BACrBM,uBAA0B,0BAC1BC,mBAAsB,aACtBC,gBAAmB,+BACnBC,qBAAwB,+BACxBC,iBAAoB,cACpBC,gBAAmB,gEACnBC,cAAiB,kEAEnB/G,QACEjB,MAAS,SACTyB,MAAS,UACTL,IAAO,QACP6G,YAAe,YACfC,YAAe,QACfC,OAAU,YACVC,SAAY,IACZC,cAAiB,uBACjBC,YAAe,qBACfC,kBAAqB,yCACrBC,oBAAuB,eACvBC,qBAAwB,eACxBC,cAAiB,eACjBC,WAAc,qBACdxH,YAAe,cAOf1K,MAAMmS,aAAenS,MAAMmS,YAAY9R,SAAS+R,gBACpDpS,MAAMmS,YAAY9R,SAAS+R,cAC3BtS,EAAEQ,QAAO,EAAMN,MAAMmS,YAAY9R,SAAS+R,eACxCC,WAAc,qBACdC,cAAiB,qBACjBC,wBAA2B,+BAC3BC,sBAAyB,4BACzBC,eAAkB,oBAClBC,WAAc,kBACdC,UAAa,mBACbC,YAAe,mBACfC,aAAgB,mBAChBC,UAAa,iBACbC,MAAS,gBACTC,YAAe,mBACfzS,MAAS,WACTC,OAAU,YAIRR,MAAMmS,aAAenS,MAAMmS,YAAY9R,SAAS4S,UACpDjT,MAAMmS,YAAY9R,SAAS4S,QAC3BnT,EAAEQ,QAAO,EAAMN,MAAMmS,YAAY9R,SAAS4S,SACxC1S,MAAS,WACT2K,KAAQ,SACR1K,OAAU,SACV0S,OAAU,YACVxG,MAAS,WACTyG,OAAU,UACVC,OAAU,KACVC,mBACE9J,MAAS,SACT+J,YACExK,OAAU,QACVyK,SAAY,OACZpK,KAAQ,UAGZqK,kBACEjK,MAAS,SAEXkK,gBACElK,MAAS,kBAEXmK,eACEnK,MAAS,cAEXoK,iBACEpK,MAAS,YACTqK,SACCC,aAAgB,kBAChBjS,cAAiB,oBACjBE,aAAgB,mBAChBC,YAAe,WACf+R,SAAY,kBACZC,YAAe,sBACfC,YAAe,kBAGlBC,aACE1K,MAAS,gBACTqK,SACEM,WAAc,cACdC,kBAAqB,oBACrBC,gBAAmB,iBACnBC,QAAW,YAGfC,cACE/K,MAAS,iBACTqK,SACEW,YAAe,iBACfC,WAAc,gBACdC,cAAiB,kBACjBC,SAAY,mBAGhBC,oBACEC,KAAQ,uCACRrL,MAAS,kBAEXsL,kBACEtL,MAAS,eACTuL,YAAe,yCACfC,UAAa,eACbC,UACEC,IAAO,oBACPnM,OAAU,QACV8L,KAAQ,OACRzL,KAAQ,QACR+L,OAAU,mBACVC,KAAQ,QAEVC,WACEC,YAAe,YACfC,SAAY,YACZC,QAAW,OACXC,WAAc,YACdC,QAAW,cACXC,WAAc,gBACdC,qBAAwB,uBACxBC,kBAAqB,wBAEvBC,kBACER,YAAe,gBACfC,SAAY,gBACZC,QAAW,iBACXC,WAAc,sBACdC,QAAW,YACXC,WAAc,iBACdC,qBAAwB,2BACxBC,kBAAqB,2BACrBV,OAAU,0BAEZY,QACEd,SAAY,WACZe,SAAY,gBACZC,IAAO,MACPC,IAAO,MACPtM,MAAS,UACTqB,MAAS,UACTL,IAAO,QACPuL,cAAiB,gBACjBC,YAAe,kBACfC,YAAe,oBACfC,SAAY,oBACZtB,UAAa,mBACbD,YAAe,kBACfwB,YAAe,6BAEjBC,cACEC,UAAa,iBACbC,YAAe,kBAGnBC,cACEnN,MAAS,iBACTuM,QACEa,SAAY,gBACZC,WAAc,oBAGlBC,gBACEtN,MAAS,YACTuM,QACEa,SAAY,gBACZC,WAAc,kBACdE,WAAc,SACdC,UAAa,kBACbC,QAAW,SACXC,YAAe,YACfC,MAAS,OACTC,WAAc,UACdC,OAAU,WACVC,aAAgB,YAChBC,WAAc,WAGlBC,oBACEC,aAAgB,qCAElBC,mBACElO,MAAS,wBACTiO,aAAgB,uEAChB1B,QACE4B,QAAW,kBACXC,OAAU,cACVC,SAAY,gBAGhBC,4BACEL,aAAgB,mDAKhBxX,MAAMmS,aAAenS,MAAMmS,YAAY9R,SAASyX,aACpD9X,MAAMmS,YAAY9R,SAASyX,WAC3BhY,EAAEQ,QAAO,EAAMN,MAAMmS,YAAY9R,SAASyX,YACxCjX,cAAiB,0BACjBC,eAAkB,0BAClBiX,cAAiB,wBACjBC,kBAAqB,yBACrBzX,MAAS,SACTmH,OAAU,SACVuQ,aAAgB,6BAChBnQ,MAAS,SACToQ,OAAU,YACVC,aAAgB,OAChB3O,IAAO,MACPC,GAAM,KACNzB,WACEC,QACEI,SAAY,gBACZC,eAAkB,kBAClBF,WAAc,iBACdG,SAAY,iBAEdY,MACEjB,GAAO,WACPC,IAAO,aACPe,GAAO,gBACPF,GAAO,eAETF,QACEZ,GAAM,WACNC,IAAO,aACPY,IAAO,0BACPC,GAAM,eACNC,IAAO,0BACPC,GAAM,oBAMRlJ,MAAMmS,aAAenS,MAAMmS,YAAY9R,SAAS+X,UACpDpY,MAAMmS,YAAY9R,SAAS+X,QAC3BtY,EAAEQ,QAAO,EAAMN,MAAMmS,YAAY9R,SAAS+X,SACxCvT,cAAiB,uBACjBC,eAAkB,wBAClBC,YAAe,mBACfC,YAAe,mBACfgB,UAAa,YACbqS,kBACExE,aAAgB,kBAChBjS,cAAiB,oBACjBE,aAAgB,mBAChBC,YAAe,oBACf+R,SAAY,kBACZC,YAAe,sBACfC,YAAe,iBAEjBsE,gBAAmB,SACnBhX,KAAQ,QACRiX,QAAW,aACXC,aACEzF,MAAS,gBACTC,YAAe,oBAEjByF,KAAQ,YACRC,IAAO,UACPxT,aAAgB,iBAChBD,UAAa,eACb0T,YAAe,qBACf5X,OAAU,YACV6X,WAAc,QACdhW,SAAY,iBACZiW,OAAU,oBACVC,aACEC,UAAa,cACbjQ,OAAU,QACVkQ,QAAW,WACXC,UAAa,YACb1F,SAAY,OACZpK,KAAQ,QACRoH,KAAQ,MACR2I,SAAY,cACZC,SAAY,cACZC,YAAe,oBAEjBC,sBAAyB,0BACzBC,sBAAyB,yBACzBC,OAAU,iBACVC,eACEjF,YAAe,iBACfC,WAAc,gBACdC,cAAiB,kBACjBC,SAAY,kBAEdnT,OAAU,SACVkY,MAAS,gBACTC,cACExF,WAAc,cACdC,kBAAqB,oBACrBC,gBAAmB,iBACnBC,QAAW,WAEbsF,KAAQ,aACRC,MAAS,SACTC,aACEC,KAAQ,QACRC,KAAQ,QAEVC,OAAU,iBACVC,QAAW,oBACXC,SAAY,mBACZC,aACEC,aAAgB,wBAChBC,cAAiB,wBACjBC,aAAgB,0BAChBC,cAAiB,2BAEnBC,UAAa,cACbC,SAAY,eACZjZ,UAAa,YACbkZ,WAAc,qBAIZ1a,MAAMmS,aAAenS,MAAMmS,YAAY9R,SAASsa,OACpD3a,MAAMmS,YAAY9R,SAASsa,KAC3B7a,EAAEQ,QAAO,EAAMN,MAAMmS,YAAY9R,SAASsa,MACxCC,QACEC,sBAAyB,iHACzBC,4BAA+B,kDAC/BC,gBAAmB,8EAErBC,MACEC,KAAQ,OACRC,OAAU,SACVC,KAAQ,WAORnb,MAAMC,GAAGmb,SACbpb,MAAMC,GAAGmb,OAAOjb,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmb,OAAOjb,UAAUC,SACvCib,oBAAuB,SACvBC,oBAAuB,YAMrBtb,MAAMC,GAAGsb,UACbvb,MAAMC,GAAGsb,QAAQpb,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsb,QAAQpb,UAAUC,QAAQC,UAChDmb,OACEtI,OAAU,SACVuI,OAAU,kBACVC,SAAY,gBACZC,WAAc,eACdC,aAAgB,cAChBC,cAAiB,mBACjBC,gBAAmB,sBAOnB9b,MAAMC,GAAGqM,WACbtM,MAAMC,GAAGqM,SAASnM,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqM,SAASnM,UAAUC,QAAQC,UACjDkM,OAAU,4BACVC,QAAW,aACXC,cAAiB,oBACjBC,MAAS,eACTjB,UACIG,KAAQ,UACRG,OAAU,eACVL,WAAc,SACdC,OAAU,qBACVgB,YAAe,qBACfpC,QAAW,SACXsB,MAAS,eACTzB,IAAO,iBAOTpK,MAAMC,GAAG8b,WACb/b,MAAMC,GAAG8b,SAAS5b,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8b,SAAS5b,UAAUC,QAAQC,UACjDmM,QAAW,aACXC,cAAiB,oBACjBC,MAAS,kBAMP1M,MAAMC,GAAG+b,SACbhc,MAAMC,GAAG+b,OAAO7b,UAAUC,QAAQ6b,aAClCnc,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+b,OAAO7b,UAAUC,QAAQ6b,cAC/CnQ,OAAU,aACVtL,OAAU,SACVkM,MAAS,eACTwG,OAAU,SACVgJ,mBAAsB,SACtBC,oBAAuB,iBACvB1U,cAAiB,qCACjB2U,gBAAmB,WACnBC,eAAkB,UAClBC,cAAiB,WACjBC,aAAgB,QAChBC,sBAAyB,cACzBC,qBAAwB,SACxBC,mBAAsB,0BACtBC,mBAAsB,yBACtBC,qBAAwB,qCAMtB5c,MAAMC,GAAG4c,YACb7c,MAAMC,GAAG4c,UAAU1c,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4c,UAAU1c,UAAUC,QAAQC,UAClDyc,SAAY,iBACZC,QAAW,kBACX/G,IAAO,mCACPC,IAAO,oCACP+G,KAAQ,kBACRC,MAAS,mCACTC,IAAO,gCACP/T,KAAQ,wBACRgU,YAAe,8DAKbnd,MAAMC,GAAGmd,WACbpd,MAAMC,GAAGmd,SAAS/c,SAClBP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmd,SAAS/c,UAC7BmM,QAAS,kBAMTxM,MAAMC,GAAGod,SACbrd,MAAMC,GAAGod,OAAOld,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGod,OAAOld,UAAUC,QAAQ6b,cAC/CqB,MAAS,YAKPtd,MAAMC,GAAGsd,WACbvd,MAAMC,GAAGsd,SAASpd,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsd,SAASpd,UAAUC,QAAQC,UACjDmd,iBAAoB,MAMlBxd,MAAMC,GAAGwd,QACbzd,MAAMC,GAAGwd,MAAMtd,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwd,MAAMtd,UAAUC,QAAQ6b,cAC9C7I,OAAU,QAMRpT,MAAMC,GAAGyd,UACb1d,MAAMC,GAAGyd,QAAQvd,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyd,QAAQvd,UAAUC,QAAQ6b,cAChD7I,OAAU,KACV5S,OAAU,YAKRR,MAAMC,GAAG0d,SACb3d,MAAMC,GAAG0d,OAAOxd,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0d,OAAOxd,UAAUC,QAAQ6b,cAC/C7I,OAAU,KACV5S,OAAU,YAKRR,MAAMC,GAAG2d,YACX5d,MAAMC,GAAG2d,UAAUzd,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2d,UAAUzd,UAAUC,QAAQC,UAClDkL,KAAQ,MACRF,MAAS,QACTD,IAAO,MACP8E,QAAW,cACX2N,KAAQ,SACRC,OAAU,SACVhO,OAAU,UACViO,UAAa,gBAIhBC,OAAOhe,MAAMie", "file": "kendo.messages.cs-CZ.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Potvrdit\",\n  \"cancel\": \"Z<PERSON>šit\",\n  \"noColor\": \"bez barvy\",\n  \"clearColor\": \"Smazat barvu\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"Potvrdit\",\n  \"cancel\": \"Zrušit\",\n  \"noColor\": \"bez barvy\",\n  \"clearColor\": \"<PERSON>mazat barvu\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Třídit vzestupně\",\n  \"sortDescending\": \"Třídit sestupně\",\n  \"filter\": \"Filtr\",\n  \"columns\": \"Sloupce\",\n  \"done\": \"Hotovo\",\n  \"settings\": \"Nastavení sloupců\",\n  \"lock\": \"Zamknout\",\n  \"unlock\": \"Odemknout\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Tučně\",\n  \"italic\": \"Kurzíva\",\n  \"underline\": \"Podtržené\",\n  \"strikethrough\": \"Přeškrtnuté\",\n  \"superscript\": \"Horní index\",\n  \"subscript\": \"Dolní index\",\n  \"justifyCenter\": \"Zarovnat na střed\",\n  \"justifyLeft\": \"Zarovnat vlevo\",\n  \"justifyRight\": \"Zarovnat vpravo\",\n  \"justifyFull\": \"Zarovnat do bloku\",\n  \"insertUnorderedList\": \"Vložit odrážkový seznam\",\n  \"insertOrderedList\": \"Vložit číslovaný seznam\",\n  \"indent\": \"Zvětšit odsazení\",\n  \"outdent\": \"Zmenšit odsazení\",\n  \"createLink\": \"Vložit odkaz\",\n  \"unlink\": \"Zrušit odkaz\",\n  \"insertImage\": \"Vložit obrázek\",\n  \"insertFile\": \"Vložit soubor\",\n  \"insertHtml\": \"Vložit HTML\",\n  \"viewHtml\": \"Zobrazit HTML\",\n  \"fontName\": \"Vyberte písmo\",\n  \"fontNameInherit\": \"(výchozí písmo)\",\n  \"fontSize\": \"Vyberte velikost písma\",\n  \"fontSizeInherit\": \"(výchozí velikost)\",\n  \"formatBlock\": \"Formát\",\n  \"formatting\": \"Formátování\",\n  \"foreColor\": \"Barva\",\n  \"backColor\": \"Barva pozadí\",\n  \"style\": \"Styly\",\n  \"emptyFolder\": \"Prázný adresář\",\n  \"uploadFile\": \"Nahrát\",\n  \"overflowAnchor\": \"Další nástroje\",\n  \"orderBy\": \"Seřadit dle:\",\n  \"orderBySize\": \"Velikosti\",\n  \"orderByName\": \"Jména\",\n  \"invalidFileType\": \"Vybraný soubor s příponou \\\"{0}\\\" není podporovaný. Podporované soubory jsou {1}.\",\n  \"deleteFile\": \"Opravdu chcete smazat \\\"{0}\\\"?\",\n  \"overwriteFile\": \"'Soubor s názvem \\\"{0}\\\" již ve vybraném adresáři existuje. Přejete si jej přepsat?\",\n  \"directoryNotFound\": \"Adresář zadaného názvu nebyl nalezen.\",\n  \"imageWebAddress\": \"Odkaz\",\n  \"imageAltText\": \"Alternativní text\",\n  \"imageWidth\": \"Šířka (px)\",\n  \"imageHeight\": \"Výška (px)\",\n  \"fileWebAddress\": \"Web adresa\",\n  \"fileTitle\": \"Název\",\n  \"linkWebAddress\": \"Odkaz\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"Text po najetí\",\n  \"linkOpenInNewWindow\": \"Otevřít odkaz v novém okně\",\n  \"dialogUpdate\": \"Aktualizovat\",\n  \"dialogInsert\": \"Vložit\",\n  \"dialogButtonSeparator\": \"nebo\",\n  \"dialogCancel\": \"Zrušit\",\n  \"cleanFormatting\": \"Vymazat formátování\",\n  \"createTable\": \"Vložit tabulku\",\n  \"addColumnLeft\": \"Přidat sloupec vlevo\",\n  \"addColumnRight\": \"Přidat sloupec vpravo\",\n  \"addRowAbove\": \"Přidat řádek nad\",\n  \"addRowBelow\": \"Přidat řádek pod\",\n  \"deleteRow\": \"Smazat řádek\",\n  \"deleteColumn\": \"Smazat soupec\",\n  \"dialogOk\": \"Ok\",\n  \"tableWizard\": \"Průvodce tabulkou\",\n  \"tableTab\": \"Tabulka\",\n  \"cellTab\": \"Buňka\",\n  \"accessibilityTab\": \"Usnadnění\",\n  \"caption\": \"Titulek\",\n  \"summary\": \"Souhrn\",\n  \"width\": \"Šířka\",\n  \"height\": \"Výška\",\n  \"units\": \"Jednotky\",\n  \"cellSpacing\": \"Mezery mezi buňkami\",\n  \"cellPadding\": \"Odsazení obsahu buněk\",\n  \"cellMargin\": \"Okraje buňky\",\n  \"alignment\": \"Zarovnání\",\n  \"background\": \"Pozadí\",\n  \"cssClass\": \"CSS třída\",\n  \"id\": \"ID\",\n  \"border\": \"Ohraničení\",\n  \"borderStyle\": \"Styl ohraničení\",\n  \"collapseBorders\": \"Sbalit ohraničení\",\n  \"wrapText\": \"Zalamovat text\",\n  \"associateCellsWithHeaders\": \"Přidružit bunky k záhlaví\",\n  \"alignLeft\": \"Zarovnat doleva\",\n  \"alignCenter\": \"Zarovnat na střed\",\n  \"alignRight\": \"Zarovnat doprava\",\n  \"alignLeftTop\": \"Zarovnat doleva nahoru\",\n  \"alignCenterTop\": \"Zarovnat nahoru na střed\",\n  \"alignRightTop\": \"Zarovnat nahoru doprava\",\n  \"alignLeftMiddle\": \"Zarovnat doleva na střed\",\n  \"alignCenterMiddle\": \"Zarovnat na střed\",\n  \"alignRightMiddle\": \"Zarovnat doprava na střed\",\n  \"alignLeftBottom\": \"Zarovnat doleva dolů\",\n  \"alignCenterBottom\": \"Zarovnat dolů na střed\",\n  \"alignRightBottom\": \"Zarovnat dolů doprava\",\n  \"alignRemove\": \"Odstranit zarovnání\",\n  \"columns\": \"Sloupce\",\n  \"rows\": \"Řádky\",\n  \"selectAllCells\": \"Vybrat všechny buňky\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Nahrát\",\n  \"orderBy\": \"Seřadit podle\",\n  \"orderByName\": \"Název\",\n  \"orderBySize\": \"Velikost\",\n  \"directoryNotFound\": \"Adresář s tímto názvem nebyl nalezen.\",\n  \"emptyFolder\": \"Prázdná složka\",\n  \"deleteFile\": 'Jste si jistí, že chcete smazat \"{0}\"?',\n  \"invalidFileType\": \"Soubor \\\"{0}\\\" není platný. Použitelné typy souborů {1}.\",\n  \"overwriteFile\": \"Soubor \\\"{0}\\\" již v aktuálním adresáři existuje. Přejete si jej přepsat?\",\n  \"dropFilesHere\": \"přetáhněte soubory pro nahrání\",\n  \"search\": \"Hledat\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"je pravda\",\n  \"isFalse\": \"není pravda\",\n  \"filter\": \"Filtrovat\",\n  \"clear\": \"Zrušit\",\n  \"operator\": \"Operátor\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Je shodná s\",\n    \"neq\": \"Je různá od\",\n    \"startswith\": \"Začíná na\",\n    \"contains\": \"Obsahuje\",\n    \"doesnotcontain\": \"Neobsahuje\",\n    \"endswith\": \"Končí na\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Není null\",\n    \"isempty\": \"Je prázdná\",\n    \"isnotempty\": \"Není prázdná\",\n    \"isnullorempty\": \"Nemá hodnotu\",\n    \"isnotnullorempty\": \"Má hodnotu\"\n  },\n  \"number\": {\n    \"eq\": \"Je rovno\",\n    \"neq\": \"Je různá od\",\n    \"gte\": \"Je větší nebo rovno\",\n    \"gt\": \"Je větší než\",\n    \"lte\": \"Je menší nebo rovno\",\n    \"lt\": \"Je menší než\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Není null\"\n  },\n  \"date\": {\n    \"eq\": \"Je rovno\",\n    \"neq\": \"Je různá od\",\n    \"gte\": \"Začíná od\",\n    \"gt\": \"Začíná po\",\n    \"lte\": \"Končí do\",\n    \"lt\": \"Končí po\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Není null\"\n  },\n  \"enums\": {\n    \"eq\": \"Je rovno\",\n    \"neq\": \"Je různá od\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Není null\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Zobrazit položky s hodnotou, která:\",\n  \"title\": \"Zobrazit položky s hodnotou, která\",\n  \"isTrue\": \"je pravda\",\n  \"isFalse\": \"není pravda\",\n  \"filter\": \"Filtrovat\",\n  \"clear\": \"Zrušit\",\n  \"and\": \"A zároveň\",\n  \"or\": \"Nebo\",\n  \"selectValue\": \"-Vyberte hodnotu-\",\n  \"operator\": \"Operátor\",\n  \"value\": \"Hodnota\",\n  \"cancel\": \"Zrušit\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Je shodná s\",\n    \"neq\": \"Je různá od\",\n    \"startswith\": \"Začíná na\",\n    \"contains\": \"Obsahuje\",\n    \"doesnotcontain\": \"Neobsahuje\",\n    \"endswith\": \"Končí na\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Není null\",\n    \"isempty\": \"Je prázdná\",\n    \"isnotempty\": \"Není prázdná\",\n    \"isnullorempty\": \"Nemá hodnotu\",\n    \"isnotnullorempty\": \"Má hodnotu\"\n  },\n  \"number\": {\n    \"eq\": \"Je rovno\",\n    \"neq\": \"Je různá od\",\n    \"gte\": \"Je větší nebo rovno\",\n    \"gt\": \"Je větší než\",\n    \"lte\": \"Je menší nebo rovno\",\n    \"lt\": \"Je menší než\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Není null\"\n  },\n  \"date\": {\n    \"eq\": \"Je rovno\",\n    \"neq\": \"Je různá od\",\n    \"gte\": \"Začíná od\",\n    \"gt\": \"Začíná po\",\n    \"lte\": \"Končí do\",\n    \"lt\": \"Končí po\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Není null\"\n  },\n  \"enums\": {\n    \"eq\": \"Je rovno\",\n    \"neq\": \"Je různá od\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Není null\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Zvolit vše\",\n  \"clear\": \"Vymazat\",\n  \"filter\": \"Filtr\",\n  \"search\": \"Hledat\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Přidat potomka\",\n    \"append\": \"Přidat úkol\",\n    \"insertAfter\": \"Přidat pod\",\n    \"insertBefore\": \"Přidat nad\",\n    \"pdf\": \"Export do PDF\"\n  },\n  \"cancel\": \"Zrušit\",\n  \"deleteDependencyWindowTitle\": \"Smazat závislost\",\n  \"deleteTaskWindowTitle\": \"Smazat úkol\",\n  \"destroy\": \"Smazat\",\n  \"editor\": {\n    \"assingButton\": \"Přiřadit\",\n    \"editorTitle\": \"Úkol\",\n    \"end\": \"Konec\",\n    \"percentComplete\": \"Hotovo\",\n    \"resources\": \"Zdroje\",\n    \"resourcesEditorTitle\": \"Zdroje\",\n    \"resourcesHeader\": \"Zdroje\",\n    \"start\": \"Začátek\",\n    \"title\": \"Název\",\n    \"unitsHeader\": \"Jednotky\"\n  },\n  \"save\": \"Uložit\",\n  \"views\": {\n    \"day\": \"Den\",\n    \"end\": \"Konec\",\n    \"month\": \"Měsíc\",\n    \"start\": \"Začátek\",\n    \"week\": \"Týden\",\n    \"year\": \"Rok\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Zrušit\",\n    \"canceledit\": \"Zrušit\",\n    \"create\": \"Přidat nový záznam\",\n    \"destroy\": \"Smazat\",\n    \"edit\": \"Upravit\",\n    \"excel\": \"Excel export\",\n    \"pdf\": \"PDF export\",\n    \"save\": \"Uložit změny\",\n    \"select\": \"Vybrat\",\n    \"update\": \"Uložit\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Zrušit\",\n    \"confirmation\": \"Opravdu chcete smazat tento záznam?\",\n    \"confirmDelete\": \"Smazat\"\n  },\n  \"noRecords\": \"Žádný záznam nenalezen.\",\n  \"expandCollapseColumnHeader\": \"\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n    \"noRows\": \"Žádné záznamy k zobrazení\",\n    \"loading\": \"Načítám...\",\n    \"requestFailed\": \"Požadavek selhal.\",\n    \"retry\": \"Zkusit znovu\",\n    \"commands\": {\n        \"edit\": \"Upravit\",\n        \"update\": \"Aktualizovat\",\n        \"canceledit\": \"Zrušit\",\n        \"create\": \"Přidat nový záznam\",\n        \"createchild\": \"Přidat nový záznam\",\n        \"destroy\": \"Smazat\",\n        \"excel\": \"Excel export\",\n        \"pdf\": \"PDF export\"\n    }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Přetáhněte sem záhlaví sloupce pro seskupení dle vybraného sloupce.\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Zvětšit\",\n  \"downArrowText\": \"Zmenšit\"\n});\n}\n\n/* MediaPlayer messages */\n\nif (kendo.ui.MediaPlayer) {\nkendo.ui.MediaPlayer.prototype.options.messages =\n$.extend(true, kendo.ui.MediaPlayer.prototype.options.messages,{\n  \"pause\": \"Pauza\",\n  \"play\": \"Přehrát\",\n  \"mute\": \"Ztlumit\",\n  \"unmute\": \"Zrušit ztlumení\",\n  \"quality\": \"Kvalita\",\n  \"fullscreen\": \"Na celou obrazovku\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} z {2} celkem\",\n  \"empty\": \"Žádný záznam nenalezen\",\n  \"page\": \"Strana\",\n  \"of\": \"z {0}\",\n  \"itemsPerPage\": \"záznamů na stránku\",\n  \"first\": \"Na první stránku\",\n  \"previous\": \"Na předchozí stránku\",\n  \"next\": \"Na další stránku\",\n  \"last\": \"Na poslední stránku\",\n  \"refresh\": \"Obnovit\",\n  \"morePages\": \"Další stránky\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\n    kendo.ui.TreeListPager.prototype.options.messages =\n    $.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n      \"allPages\": \"All\",\n      \"display\": \"{0} - {1} z {2} celkem\",\n      \"empty\": \"Žádný záznam nenalezen\",\n      \"page\": \"Strana\",\n      \"of\": \"z {0}\",\n      \"itemsPerPage\": \"záznamů na stránku\",\n      \"first\": \"Na první stránku\",\n      \"previous\": \"Na předchozí stránku\",\n      \"next\": \"Na další stránku\",\n      \"last\": \"Na poslední stránku\",\n      \"refresh\": \"Obnovit\",\n      \"morePages\": \"Další stránky\"\n    });\n    }\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"Sem přetáhněte pole\",\n  \"columnFields\": \"Sem přetáhněte sloupce\",\n  \"rowFields\": \"Sem přetáhněte řádky\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"Zobrazit položky s hodnotou:\",\n  \"filterFields\": \"Filtr\",\n  \"filter\": \"Filtr\",\n  \"include\": \"Zahrnout pole...\",\n  \"title\": \"Pole k zahrnutí\",\n  \"clear\": \"Vyčistit\",\n  \"ok\": \"Ok\",\n  \"cancel\": \"Zrušit\",\n  \"operators\": {\n    \"contains\": \"Obsahuje\",\n    \"doesnotcontain\": \"Neobsahuje\",\n    \"startswith\": \"Začína na\",\n    \"endswith\": \"Končí na\",\n    \"eq\": \"Je rovno\",\n    \"neq\": \"Není rovno\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Nikdy\",\n    \"hourly\": \"Každou hodinu\",\n    \"daily\": \"Denně\",\n    \"weekly\": \"Týdně\",\n    \"monthly\": \"Měsíčně\",\n    \"yearly\": \"Ročně\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Opakovat každých: \",\n    \"interval\": \" hodin\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Opakovat každý:\",\n    \"interval\": \"dní\"\n  },\n  \"weekly\": {\n    \"interval\": \"týden(ny)\",\n    \"repeatEvery\": \"Opakovat každý:\",\n    \"repeatOn\": \"Opakovat v:\"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Opakovat každý:\",\n    \"repeatOn\": \"Opakovat v:\",\n    \"interval\": \"měsíc(e)\",\n    \"day\": \"Den\"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Opakovat každý:\",\n    \"repeatOn\": \"Opakovat v:\",\n    \"interval\": \"rok(y)\",\n    \"of\": \" z \"\n  },\n  \"end\": {\n    \"label\": \"Konec:\",\n    \"mobileLabel\": \"Končí\",\n    \"never\": \"Nikdy\",\n    \"after\": \"Konec po\",\n    \"occurrence\": \"opakování\",\n    \"on\": \"Dne\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"první\",\n    \"second\": \"druhý\",\n    \"third\": \"třetí\",\n    \"fourth\": \"čtvrtý\",\n    \"last\": \"poslední\"\n  },\n\n  \"weekdays\": {\n    \"day\": \"den\",\n    \"weekday\": \"pracovní den\",\n    \"weekend\": \"víkend\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"celý den\",\n  \"date\": \"Datum\",\n  \"event\": \"Událost\",\n  \"time\": \"Čas\",\n  \"showFullDay\": \"Zobrazit celý den\",\n  \"showWorkDay\": \"Zobrazit pracovní dobu\",\n  \"today\": \"Dnes\",\n  \"save\": \"Uložit\",\n  \"cancel\": \"Zrušit\",\n  \"destroy\": \"Smazat\",\n  \"deleteWindowTitle\": \"Smazat událost\",\n  \"ariaSlotLabel\": \"Zvoleno od {0:t} do {1:t}\",\n  \"ariaEventLabel\": \"{0} dne {1:D} v {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"Opravdu chcete smazat tuto událost?\"\n  },\n  \"views\": {\n    \"day\": \"Den\",\n    \"week\": \"Týden\",\n    \"workWeek\": \"Pracovní týden\",\n    \"agenda\": \"Agenda\",\n    \"month\": \"Měsíc\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Smazat opakovanou událost\",\n    \"deleteWindowOccurrence\": \"Smazat vybranou událost\",\n    \"deleteWindowSeries\": \"Smazat vše\",\n    \"editWindowTitle\": \"Upravit opakující se událost\",\n    \"editWindowOccurrence\": \"Upravit jen vybranou událost\",\n    \"editWindowSeries\": \"Upravit vše\",\n    \"deleteRecurring\": \"Chcete smazat jen vybranou událost, nebo i všechna opakování?\",\n    \"editRecurring\": \"Chcete upravit jen vybranou událost, nebo i všechna opakování?\"\n  },\n  \"editor\": {\n    \"title\": \"Nadpis\",\n    \"start\": \"Začátek\",\n    \"end\": \"Konec\",\n    \"allDayEvent\": \"Celodenní\",\n    \"description\": \"Popis\",\n    \"repeat\": \"Opakování\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"Časové pásmo začátku\",\n    \"endTimezone\": \"Časové pásmo konce\",\n    \"separateTimezones\": \"Různá časové pásma pro začátek a konec\",\n    \"timezoneEditorTitle\": \"Časová pásma\",\n    \"timezoneEditorButton\": \"Časové pásmo\",\n    \"timezoneTitle\": \"Časová pásma\",\n    \"noTimezone\": \"Žádné časová pásmo\",\n    \"editorTitle\": \"Událost\"\n  }\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"Všechno ohraničení\",\n  \"insideBorders\": \"Vnitřní ohraničení\",\n  \"insideHorizontalBorders\": \"Vnitřní vodorovné ohraničení\",\n  \"insideVerticalBorders\": \"Vnitřní svislé ohraničení\",\n  \"outsideBorders\": \"Vnější ohraničení\",\n  \"leftBorder\": \"Levé ohraničení\",\n  \"topBorder\": \"Horní ohraničení\",\n  \"rightBorder\": \"Pravé ohraničení\",\n  \"bottomBorder\": \"Dolní ohraničení\",\n  \"noBorders\": \"Bez ohraničení\",\n  \"reset\": \"Obnovit barvu\",\n  \"customColor\": \"Vlastní barva...\",\n  \"apply\": \"Potvrdit\",\n  \"cancel\": \"Zrušit\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Potvrdit\",\n  \"save\": \"Uložit\",\n  \"cancel\": \"Zrušit\",\n  \"remove\": \"Odstranit\",\n  \"retry\": \"Opakovat\",\n  \"revert\": \"Původní\",\n  \"okText\": \"OK\",\n  \"formatCellsDialog\": {\n    \"title\": \"Formát\",\n    \"categories\": {\n      \"number\": \"Číslo\",\n      \"currency\": \"Měna\",\n      \"date\": \"Datum\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Písmo\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Velikost písma\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Ohraničení\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Zarovnání\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Zarovnat doleva\",\n     \"justifyCenter\": \"Zarovnat na střed\",\n     \"justifyRight\": \"Zarovnat doprava\",\n     \"justifyFull\": \"Do bloku\",\n     \"alignTop\": \"Zarovnat nahoru\",\n     \"alignMiddle\": \"Zarovnat doprostřed\",\n     \"alignBottom\": \"Zarovnat dolů\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Sloučit buňky\",\n    \"buttons\": {\n      \"mergeCells\": \"Sloučit vše\",\n      \"mergeHorizontally\": \"Sloučit vodorovně\",\n      \"mergeVertically\": \"Sloučit svisle\",\n      \"unmerge\": \"Oddělit\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Ukotvit příčky\",\n    \"buttons\": {\n      \"freezePanes\": \"Ukotvit příčky\",\n      \"freezeRows\": \"Ukotvit řádky\",\n      \"freezeColumns\": \"Ukotvit sloupce\",\n      \"unfreeze\": \"Uvolnit příčky\"\n    }\n  },\n  \"confirmationDialog\": {\n    \"text\": \"Opravdu chcete odstranit tento list?\",\n    \"title\": \"Odstranit list\"\n  },\n  \"validationDialog\": {\n    \"title\": \"Validace dat\",\n    \"hintMessage\": \"Prosím vložte platnou {0} hodnotu {1}.\",\n    \"hintTitle\": \"Validace {0}\",\n    \"criteria\": {\n      \"any\": \"Jakákoliv hodnota\",\n      \"number\": \"Číslo\",\n      \"text\": \"Text\",\n      \"date\": \"Datum\",\n      \"custom\": \"Vlastní podmínka\",\n      \"list\": \"List\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"větší než\",\n      \"lessThan\": \"menší než\",\n      \"between\": \"mezi\",\n      \"notBetween\": \"není mezi\",\n      \"equalTo\": \"je shodná s\",\n      \"notEqualTo\": \"není shodná s\",\n      \"greaterThanOrEqualTo\": \"větší než nebo rovno\",\n      \"lessThanOrEqualTo\": \"menší než nebo rovno\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"větší než {0}\",\n      \"lessThan\": \"menší než {0}\",\n      \"between\": \"mezi {0} a {1}\",\n      \"notBetween\": \"není mezi {0} a {1}\",\n      \"equalTo\": \"rovno {0}\",\n      \"notEqualTo\": \"není rovno {0}\",\n      \"greaterThanOrEqualTo\": \"vetší než nebo rovno {0}\",\n      \"lessThanOrEqualTo\": \"menší než nebo rovno {0}\",\n      \"custom\": \"vyhovuje podmínce: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Podmínky\",\n      \"comparer\": \"Typ porovnání\",\n      \"min\": \"Min\",\n      \"max\": \"Max\",\n      \"value\": \"Hodnota\",\n      \"start\": \"Začátek\",\n      \"end\": \"Konec\",\n      \"onInvalidData\": \"Neplatná data\",\n      \"rejectInput\": \"Zamítnout vstup\",\n      \"showWarning\": \"Zobrazit varování\",\n      \"showHint\": \"Zobrazit nápovědu\",\n      \"hintTitle\": \"Titulek nápovědy\",\n      \"hintMessage\": \"Zpráva nápovědy\",\n      \"ignoreBlank\": \"Ignorovat prázdné hodnoty\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Vložte titulek\",\n      \"typeMessage\": \"Vložte zprávu\"\n    }\n  },\n  \"saveAsDialog\": {\n    \"title\": \"Uložit jako...\",\n    \"labels\": {\n      \"fileName\": \"Jméno souboru\",\n      \"saveAsType\": \"Uložit jako typ\"\n    }\n  },\n  \"exportAsDialog\": {\n    \"title\": \"Export...\",\n    \"labels\": {\n      \"fileName\": \"Jméno souboru\",\n      \"saveAsType\": \"Uložit jako typ\",\n      \"exportArea\": \"Export\",\n      \"paperSize\": \"Velikost papíru\",\n      \"margins\": \"Okraje\",\n      \"orientation\": \"Orientace\",\n      \"print\": \"Tisk\",\n      \"guidelines\": \"Vodítka\",\n      \"center\": \"Na střed\",\n      \"horizontally\": \"Vodorovně\",\n      \"vertically\": \"Svisle\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Nelze změnit část sloučené buňky.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Kopírování a vkládání\",\n    \"errorMessage\": \"Tyto akce nelze vyvolat z menu. Prosím, použijte klávesovou zkratku:\",\n    \"labels\": {\n      \"forCopy\": \"pro zkopírování\",\n      \"forCut\": \"pro vyjmutí\",\n      \"forPaste\": \"pro vložení\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"Tuto akci nelze použít na vícenásobný výběr.\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Seřadit rozsah od A k Z\",\n  \"sortDescending\": \"Seřadit rozsah od Z k A\",\n  \"filterByValue\": \"Filtrovat dle hodnoty\",\n  \"filterByCondition\": \"Filtrovat dle podmínky\",\n  \"apply\": \"Použít\",\n  \"search\": \"Hledat\",\n  \"addToCurrent\": \"Přidat k současnému výběru\",\n  \"clear\": \"Smazat\",\n  \"blanks\": \"(Prázdné)\",\n  \"operatorNone\": \"Není\",\n  \"and\": \"AND\",\n  \"or\": \"OR\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"Text obsahuje\",\n      \"doesnotcontain\": \"Text neobsahuje\",\n      \"startswith\": \"Text začíná na\",\n      \"endswith\": \"Text končí na\"\n    },\n    \"date\": {\n      \"eq\":  \"Datum je\",\n      \"neq\": \"Datum není\",\n      \"lt\":  \"Datum je před\",\n      \"gt\":  \"Datum je po\"\n    },\n    \"number\": {\n      \"eq\": \"Je rovno\",\n      \"neq\": \"Není rovno\",\n      \"gte\": \"Je větší než nebo rovno\",\n      \"gt\": \"Je větší než\",\n      \"lte\": \"Je menší než nebo rovno\",\n      \"lt\": \"Je menší než\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Vložit sloupec vlevo\",\n  \"addColumnRight\": \"Vložit sloupec vpravo\",\n  \"addRowAbove\": \"Vložit řádek nad\",\n  \"addRowBelow\": \"Vložit řádek pod\",\n  \"alignment\": \"Zarovnání\",\n  \"alignmentButtons\": {\n    \"justtifyLeft\": \"Zarovnat doleva\",\n    \"justifyCenter\": \"Zarovnat na střed\",\n    \"justifyRight\": \"Zarovnat doprava\",\n    \"justifyFull\": \"Zarovnat do bloku\",\n    \"alignTop\": \"Zarovnat nahoru\",\n    \"alignMiddle\": \"Zarovnat doprostřed\",\n    \"alignBottom\": \"Zarovnat dolů\"\n  },\n  \"backgroundColor\": \"Pozadí\",\n  \"bold\": \"Tučně\",\n  \"borders\": \"Ohraničení\",\n  \"colorPicker\": {\n    \"reset\": \"Obnovit barvu\",\n    \"customColor\": \"Vlastní barva...\"\n  },\n  \"copy\": \"Kopírovat\",\n  \"cut\": \"Vyjmout\",\n  \"deleteColumn\": \"Smazat sloupec\",\n  \"deleteRow\": \"Smazat řádek\",\n  \"excelImport\": \"Načíst z Excelu...\",\n  \"filter\": \"Filtrovat\",\n  \"fontFamily\": \"Písmo\",\n  \"fontSize\": \"Velikost písma\",\n  \"format\": \"Vlastní formát...\",\n  \"formatTypes\": {\n    \"automatic\": \"Automaticky\",\n    \"number\": \"Číslo\",\n    \"percent\": \"procenta\",\n    \"financial\": \"Účetnický\",\n    \"currency\": \"Měna\",\n    \"date\": \"Datum\",\n    \"time\": \"Čas\",\n    \"dateTime\": \"Datum a čas\",\n    \"duration\": \"Doba trvání\",\n    \"moreFormats\": \"Další formáty...\"\n  },\n  \"formatDecreaseDecimal\": \"Odebrat desetinné místo\",\n  \"formatIncreaseDecimal\": \"Přidat desetinné místo\",\n  \"freeze\": \"Ukotvit příčky\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Ukotvit příčky\",\n    \"freezeRows\": \"Ukotvit řádky\",\n    \"freezeColumns\": \"Ukotvit sloupce\",\n    \"unfreeze\": \"Uvolnit příčky\"\n  },\n  \"italic\": \"Italic\",\n  \"merge\": \"Sloučit buňky\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Sloučit vše\",\n    \"mergeHorizontally\": \"Sloučit vodorovně\",\n    \"mergeVertically\": \"Sloučit svisle\",\n    \"unmerge\": \"Oddělit\"\n  },\n  \"open\": \"Otevřít...\",\n  \"paste\": \"Vložit\",\n  \"quickAccess\": {\n    \"redo\": \"Znovu\",\n    \"undo\": \"Zpět\"\n  },\n  \"saveAs\": \"Uložit jako...\",\n  \"sortAsc\": \"Seřadit vzestupně\",\n  \"sortDesc\": \"Seřadit sestupně\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Seřadit list od A k Z\",\n    \"sortSheetDesc\": \"Seřadit list od Z k A\",\n    \"sortRangeAsc\": \"Seřadit rozsah od A k Z\",\n    \"sortRangeDesc\": \"Seřadit rozsah od Z k A\"\n  },\n  \"textColor\": \"Barva textu\",\n  \"textWrap\": \"Zalomit text\",\n  \"underline\": \"Podtržení\",\n  \"validation\": \"Validace dat...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Nelze vložit buňky z důvodu možné ztráty dat. Zvolte jiné místo pro vložení nebo odstraňte data z konce listu.\",\n    \"filterRangeContainingMerges\": \"Nelze vytvořit filtr v rozsahu sloučených buněk\",\n    \"validationError\": \"Vložená hodnota nevyhovuje validačním pravidlům nastaveným pro tuto buňku.\"\n  },\n  \"tabs\": {\n    \"home\": \"Home\",\n    \"insert\": \"Insert\",\n    \"data\": \"Data\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Zvýšit\",\n  \"decreaseButtonTitle\": \"Snížit\"\n});\n}\n\n/* ListBox messaages */\n\nif (kendo.ui.ListBox) {\nkendo.ui.ListBox.prototype.options.messages =\n$.extend(true, kendo.ui.ListBox.prototype.options.messages,{\n  \"tools\": {\n    \"remove\": \"Smazat\",\n    \"moveUp\": \"Posunout nahoru\",\n    \"moveDown\": \"Posunout dolů\",\n    \"transferTo\": \"Přesunout do\",\n    \"transferFrom\": \"Přesunout z\",\n    \"transferAllTo\": \"Přesunout vše do\",\n    \"transferAllFrom\": \"Přesunout vše z\"\n  }\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n  \"noRows\": \"Žádné záznamy k zobrazení\",\n  \"loading\": \"Načítám...\",\n  \"requestFailed\": \"Požadavek selhal.\",\n  \"retry\": \"Zkusit znovu\",\n  \"commands\": {\n      \"edit\": \"Upravit\",\n      \"update\": \"Aktualizovat\",\n      \"canceledit\": \"Zrušit\",\n      \"create\": \"Přidat nový záznam\",\n      \"createchild\": \"Přidat nový záznam\",\n      \"destroy\": \"Smazat\",\n      \"excel\": \"Excel export\",\n      \"pdf\": \"PDF export\"\n  }\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Načítám...\",\n  \"requestFailed\": \"Požadavek selhal.\",\n  \"retry\": \"Zkusit znovu\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Vyberte...\",\n  \"cancel\": \"Zrušit\",\n  \"retry\": \"Zkusit znova\",\n  \"remove\": \"Smazat\",\n  \"clearSelectedFiles\": \"Smazat\",\n  \"uploadSelectedFiles\": \"Nahrát soubory\",\n  \"dropFilesHere\": \"Pro nahrání přetáhněte soubory sem\",\n  \"statusUploading\": \"nahrávám\",\n  \"statusUploaded\": \"nahráno\",\n  \"statusWarning\": \"varování\",\n  \"statusFailed\": \"chyba\",\n  \"headerStatusUploading\": \"Nahrávám...\",\n  \"headerStatusUploaded\": \"Hotovo\",\n  \"invalidMaxFileSize\": \"Soubor je příliš velký.\",\n  \"invalidMinFileSize\": \"Soubor je příliš malý.\",\n  \"invalidFileExtension\": \"tento typ souboru není povolen.\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} je povinné\",\n  \"pattern\": \"{0} není platné\",\n  \"min\": \"{0} musí být větší než rovno {1}\",\n  \"max\": \"{0} musí být menší nebo rovno {1}\",\n  \"step\": \"{0} není platné\",\n  \"email\": \"{0} není platná e-mailová adresa\",\n  \"url\": \"{0} není platná webová adresa\",\n  \"date\": \"{0} není platné datum\",\n  \"dateCompare\": \"Datum konce musí být vyšší než nebo rovno datumu začátku\"\n});\n}\n\n/* kendo.ui.progress method */\nif (kendo.ui.progress) {\nkendo.ui.progress.messages =\n$.extend(true, kendo.ui.progress.messages, {\n    loading: \"Načítá se...\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization,{\n  \"close\": \"Zavřít\"\n});\n}\n\n/* Calendar */\nif (kendo.ui.Calendar) {\nkendo.ui.Calendar.prototype.options.messages =\n$.extend(true, kendo.ui.Calendar.prototype.options.messages, {\n  \"weekColumnHeader\": \"\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization,{\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization,{\n  \"okText\": \"OK\",\n  \"cancel\": \"Zrušit\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization,{\n  \"okText\": \"OK\",\n  \"cancel\": \"Zrušit\"\n});\n}\n\n/* DateInput */\nif (kendo.ui.DateInput) {\n  kendo.ui.DateInput.prototype.options.messages =\n    $.extend(true, kendo.ui.DateInput.prototype.options.messages, {\n      \"year\": \"rok\",\n      \"month\": \"měsíc\",\n      \"day\": \"den\",\n      \"weekday\": \"den v týdnu\",\n      \"hour\": \"hodiny\",\n      \"minute\": \"minuty\",\n      \"second\": \"sekundy\",\n      \"dayperiod\": \"dop./odp.\"\n    });\n}\n\n})(window.kendo.jQuery);\n}));"]}