{"version": 3, "sources": ["messages/kendo.messages.nl-BE.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "enums", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "FilterMenu", "ColumnMenu", "messages", "columns", "settings", "done", "sortAscending", "sortDescending", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "label", "never", "occurrence", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "weekdays", "weekday", "weekend", "of", "Editor", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "backColor", "bold", "createLink", "createTable", "deleteColumn", "deleteFile", "deleteRow", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "directoryNotFound", "dropFilesHere", "emptyFolder", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "foreColor", "formatBlock", "formatting", "imageAltText", "imageWebAddress", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "invalidFileType", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "orderBy", "orderByName", "orderBySize", "outdent", "overwriteFile", "search", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "uploadFile", "viewHtml", "dialogUpdate", "insertFile", "clear", "filter", "isFalse", "isTrue", "operator", "and", "cancel", "info", "title", "or", "selectValue", "value", "FilterMultiCheck", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "Groupable", "empty", "Pager", "allPages", "display", "itemsPerPage", "next", "page", "previous", "refresh", "morePages", "TreeListPager", "Scheduler", "allDay", "deleteWindowTitle", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "showFullDay", "showWorkDay", "time", "today", "views", "agenda", "month", "week", "workWeek", "timeline", "Upload", "localization", "headerStatusUploaded", "headerStatusUploading", "remove", "retry", "statusFailed", "statusUploaded", "statusUploading", "uploadSelectedFiles", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,gBACNC,GAAM,QACNC,IAAO,cACPC,GAAM,UACNC,IAAO,gBACPC,IAAO,mBAETC,OACEN,GAAM,gBACNK,IAAO,mBAETE,QACEP,GAAM,gBACNC,GAAM,gBACNC,IAAO,0BACPC,GAAM,iBACNC,IAAO,2BACPC,IAAO,mBAETG,QACEC,SAAY,QACZC,eAAkB,aAClBC,SAAY,aACZX,GAAM,gBACNK,IAAO,kBACPO,WAAc,iBAOdpB,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,gBACNC,GAAM,QACNC,IAAO,cACPC,GAAM,UACNC,IAAO,gBACPC,IAAO,mBAETC,OACEN,GAAM,gBACNK,IAAO,mBAETE,QACEP,GAAM,gBACNC,GAAM,gBACNC,IAAO,0BACPC,GAAM,iBACNC,IAAO,2BACPC,IAAO,mBAETG,QACEC,SAAY,QACZC,eAAkB,aAClBC,SAAY,aACZX,GAAM,gBACNK,IAAO,kBACPO,WAAc,iBAOdpB,MAAMC,GAAGqB,aACbtB,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,UACnDC,QAAW,WACXC,SAAY,qBACZC,KAAQ,SACRC,cAAiB,mBACjBC,eAAkB,mBAClBC,KAAQ,OACRC,OAAU,gBAMR9B,MAAMC,GAAG8B,mBACb/B,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,UACzDS,OACEC,SAAY,UACZC,YAAe,gBAEjBC,KACEC,MAAS,KACTC,MAAS,QACTC,MAAS,QACTC,WAAc,mBACdC,GAAM,KACNC,YAAe,QAEjBC,aACEV,MAAS,YACTW,QAAW,cACXL,MAAS,QACTM,OAAU,YACVC,OAAU,aAEZF,SACEG,IAAO,MACPb,SAAY,YACZC,YAAe,gBACfa,SAAY,eAEdC,iBACEC,MAAS,SACTC,OAAU,SACVC,KAAQ,UACRC,OAAU,SACVC,MAAS,SAEXC,UACER,IAAO,MACPS,QAAW,mBACXC,QAAW,eAEbZ,QACEX,SAAY,aACZC,YAAe,gBACfa,SAAY,eAEdF,QACEZ,SAAY,aACZwB,GAAM,MACNvB,YAAe,gBACfa,SAAY,kBAOZ/C,MAAMC,GAAGyD,SACb1D,MAAMC,GAAGyD,OAAOvD,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyD,OAAOvD,UAAUC,QAAQmB,UAC/CoC,cAAiB,uBACjBC,eAAkB,wBAClBC,YAAe,sBACfC,YAAe,sBACfC,UAAa,mBACbC,KAAQ,MACRC,WAAc,qBACdC,YAAe,cACfC,aAAgB,oBAChBC,WAAc,yDACdC,UAAa,kBACbC,sBAAyB,KACzBC,aAAgB,YAChBC,aAAgB,WAChBC,kBAAqB,yCACrBC,cAAiB,+CACjBC,YAAe,WACfC,SAAY,uBACZC,gBAAmB,sBACnBC,SAAY,0BACZC,gBAAmB,0BACnBC,UAAa,QACbC,YAAe,UACfC,WAAc,UACdC,aAAgB,oBAChBC,gBAAmB,WACnBC,OAAU,aACVC,WAAc,iBACdC,YAAe,uBACfC,kBAAqB,4BACrBC,oBAAuB,8BACvBC,gBAAmB,qGACnBC,OAAU,UACVC,cAAiB,kBACjBC,YAAe,YACfC,YAAe,wBACfC,aAAgB,yBAChBC,oBAAuB,6BACvBC,SAAY,QACZC,YAAe,UACfC,eAAkB,WAClBC,QAAW,cACXC,YAAe,OACfC,YAAe,UACfC,QAAW,sBACXC,cAAiB,0FACjBC,OAAU,OACVC,cAAiB,YACjBC,OAAU,UACVC,UAAa,YACbC,YAAe,cACfC,UAAa,eACbC,OAAU,wBACVC,WAAc,iBACdC,SAAY,YACZC,aAAgB,SAChBC,WAAc,iBAMZnH,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACnD6F,MAAS,gBACTC,OAAU,SACVC,QAAW,eACXC,OAAU,UACVC,SAAY,cAMVxH,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACnDkG,IAAO,KACPC,OAAU,YACVN,MAAS,gBACTC,OAAU,SACVM,KAAQ,yBACRC,MAAS,wBACTN,QAAW,eACXC,OAAU,UACVC,SAAY,WACZK,GAAM,KACNC,YAAe,qBACfC,MAAS,YAMP/H,MAAMC,GAAG+H,mBACbhI,MAAMC,GAAG+H,iBAAiB7H,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+H,iBAAiB7H,UAAUC,QAAQmB,UACzDkF,OAAU,UAMRzG,MAAMC,GAAGgI,OACbjI,MAAMC,GAAGgI,KAAK9H,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgI,KAAK9H,UAAUC,QAAQmB,UAC7C2G,UACEC,WAAc,YACdT,OAAU,wBACVU,OAAU,iBACVC,QAAW,cACXC,KAAQ,WACRC,MAAS,oBACTC,IAAO,kBACPC,KAAQ,sBACRC,OAAU,aACVC,OAAU,aAEZC,UACEC,aAAgB,YAChBC,aAAgB,gDAChBC,cAAiB,kBAOjB/I,MAAMC,GAAG+I,YACbhJ,MAAMC,GAAG+I,UAAU7I,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+I,UAAU7I,UAAUC,QAAQmB,UAClD0H,MAAS,+DAMPjJ,MAAMC,GAAGiJ,QACblJ,MAAMC,GAAGiJ,MAAM/I,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiJ,MAAM/I,UAAUC,QAAQmB,UAC9C4H,SAAY,MACZC,QAAW,0BACXH,MAAS,yBACThG,MAAS,wBACToG,aAAgB,mBAChBlG,KAAQ,yBACRmG,KAAQ,0BACR7F,GAAM,UACN8F,KAAQ,SACRC,SAAY,wBACZC,QAAW,YACXC,UAAa,iBAMX1J,MAAMC,GAAG0J,gBACb3J,MAAMC,GAAG0J,cAAcxJ,UAAUC,QAAQmB,SACzCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0J,cAAcxJ,UAAUC,QAAQmB,UACtD4H,SAAY,MACZC,QAAW,0BACXH,MAAS,yBACThG,MAAS,wBACToG,aAAgB,mBAChBlG,KAAQ,yBACRmG,KAAQ,0BACR7F,GAAM,UACN8F,KAAQ,SACRC,SAAY,wBACZC,QAAW,YACXC,UAAa,iBAMX1J,MAAMC,GAAG2J,YACb5J,MAAMC,GAAG2J,UAAUzJ,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,UAAUzJ,UAAUC,QAAQmB,UAClDsI,OAAU,gBACVnC,OAAU,YACVkB,UACEE,aAAgB,sDAElBvI,KAAQ,QACRuJ,kBAAqB,uBACrBzB,QAAW,cACX0B,QACEC,YAAe,iBACfC,YAAe,eACfC,YAAe,WACf/H,IAAO,OACPgI,YAAe,WACfC,OAAU,mBACVC,kBAAqB,2CACrBC,MAAS,QACTC,cAAiB,YACjBC,SAAY,qBACZC,qBAAwB,aACxBC,oBAAuB,eACvB9C,MAAS,YACT+C,WAAc,eAEhBC,MAAS,WACTC,oBACEC,gBAAmB,4FACnBC,uBAA0B,sBAC1BC,mBAAsB,kBACtBlB,kBAAqB,6BACrBmB,cAAiB,sFACjBC,qBAAwB,qBACxBC,iBAAoB,iBACpBC,gBAAmB,6BAErB3C,KAAQ,UACR4C,YAAe,gBACfC,YAAe,kBACfC,KAAQ,OACRC,MAAS,UACTC,OACEC,OAAU,SACV5I,IAAO,MACP6I,MAAS,QACTC,KAAQ,OACRC,SAAY,YACZC,SAAY,eAOZ9L,MAAMC,GAAG8L,SACb/L,MAAMC,GAAG8L,OAAO5L,UAAUC,QAAQ4L,aAClClM,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8L,OAAO5L,UAAUC,QAAQ4L,cAC/CtE,OAAU,YACVhD,cAAiB,+BACjBuH,qBAAwB,SACxBC,sBAAyB,cACzBC,OAAU,cACVC,MAAS,UACT1D,OAAU,YACV2D,aAAgB,UAChBC,eAAkB,SAClBC,gBAAmB,qBACnBC,oBAAuB,wBAMrBxM,MAAMC,GAAGwM,SACbzM,MAAMC,GAAGwM,OAAOtM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwM,OAAOtM,UAAUC,QAAQ4L,cAC/CU,MAAS,aAMP1M,MAAMC,GAAG0M,QACb3M,MAAMC,GAAG0M,MAAMxM,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0M,MAAMxM,UAAUC,QAAQ4L,cAC9CY,OAAU,QAMR5M,MAAMC,GAAG4M,UACb7M,MAAMC,GAAG4M,QAAQ1M,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4M,QAAQ1M,UAAUC,QAAQ4L,cAChDY,OAAU,KACVlF,OAAU,eAKR1H,MAAMC,GAAG6M,SACb9M,MAAMC,GAAG6M,OAAO3M,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6M,OAAO3M,UAAUC,QAAQ4L,cAC/CY,OAAU,KACVlF,OAAU,gBAITqF,OAAO/M,MAAMgN", "file": "kendo.messages.nl-BE.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Is gelijk aan\",\n    \"gt\": \"Is na\",\n    \"gte\": \"Is op of na\",\n    \"lt\": \"Is voor\",\n    \"lte\": \"Is op of voor\",\n    \"neq\": \"Is ongelijk aan\"\n  },\n  \"enums\": {\n    \"eq\": \"Is gelijk aan\",\n    \"neq\": \"Is ongelijk aan\"\n  },\n  \"number\": {\n    \"eq\": \"Is gelijk aan\",\n    \"gt\": \"Is groter dan\",\n    \"gte\": \"Is groter of gelijk aan\",\n    \"lt\": \"Is kleiner dan\",\n    \"lte\": \"<PERSON> kleiner of gelijk aan\",\n    \"neq\": \"Is ongelijk aan\"\n  },\n  \"string\": {\n    \"contains\": \"Bevat\",\n    \"doesnotcontain\": \"Bevat niet\",\n    \"endswith\": \"Eindigt op\",\n    \"eq\": \"Is gelijk aan\",\n    \"neq\": \"Is ongelijk aan\",\n    \"startswith\": \"Begint met\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Is gelijk aan\",\n    \"gt\": \"Is na\",\n    \"gte\": \"Is op of na\",\n    \"lt\": \"Is voor\",\n    \"lte\": \"Is op of voor\",\n    \"neq\": \"Is ongelijk aan\"\n  },\n  \"enums\": {\n    \"eq\": \"Is gelijk aan\",\n    \"neq\": \"Is ongelijk aan\"\n  },\n  \"number\": {\n    \"eq\": \"Is gelijk aan\",\n    \"gt\": \"Is groter dan\",\n    \"gte\": \"Is groter of gelijk aan\",\n    \"lt\": \"Is kleiner dan\",\n    \"lte\": \"Is kleiner of gelijk aan\",\n    \"neq\": \"Is ongelijk aan\"\n  },\n  \"string\": {\n    \"contains\": \"Bevat\",\n    \"doesnotcontain\": \"Bevat niet\",\n    \"endswith\": \"Eindigt op\",\n    \"eq\": \"Is gelijk aan\",\n    \"neq\": \"Is ongelijk aan\",\n    \"startswith\": \"Begint met\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Kolommen\",\n  \"settings\": \"Kolom instellingen\",\n  \"done\": \"Gereed\",\n  \"sortAscending\": \"Sorteer Oplopend\",\n  \"sortDescending\": \"Sorteer Aflopend\",\n  \"lock\": \"Slot\",\n  \"unlock\": \"Ontsluiten\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"dag(en)\",\n    \"repeatEvery\": \"Herhaal elke\"\n  },\n  \"end\": {\n    \"after\": \"Na\",\n    \"label\": \"Eind:\",\n    \"never\": \"Nooit\",\n    \"occurrence\": \"gebeurtenis(sen)\",\n    \"on\": \"Op\",\n    \"mobileLabel\": \"Ends\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Dagelijks\",\n    \"monthly\": \"Maandelijks\",\n    \"never\": \"Nooit\",\n    \"weekly\": \"Wekelijks\",\n    \"yearly\": \"Jaarlijks\"\n  },\n  \"monthly\": {\n    \"day\": \"Dag\",\n    \"interval\": \"maand(en)\",\n    \"repeatEvery\": \"Herhaal elke:\",\n    \"repeatOn\": \"Herhaal op:\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"eerste\",\n    \"fourth\": \"vierde\",\n    \"last\": \"laatste\",\n    \"second\": \"tweede\",\n    \"third\": \"derde\"\n  },\n  \"weekdays\": {\n    \"day\": \"dag\",\n    \"weekday\": \"doordeweekse dag\",\n    \"weekend\": \"weekend dag\"\n  },\n  \"weekly\": {\n    \"interval\": \"week/weken\",\n    \"repeatEvery\": \"Herhaal elke:\",\n    \"repeatOn\": \"Herhaal op:\"\n  },\n  \"yearly\": {\n    \"interval\": \"jaar/jaren\",\n    \"of\": \"van\",\n    \"repeatEvery\": \"Herhaal elke:\",\n    \"repeatOn\": \"Herhaal op:\"\n  }\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"addColumnLeft\": \"Voeg links kolom toe\",\n  \"addColumnRight\": \"Voeg rechts kolom toe\",\n  \"addRowAbove\": \"Rij boven toevoegen\",\n  \"addRowBelow\": \"Rij onder toevoegen\",\n  \"backColor\": \"Achtergrondkleur\",\n  \"bold\": \"Vet\",\n  \"createLink\": \"Hyperlink invoegen\",\n  \"createTable\": \"Tabel maken\",\n  \"deleteColumn\": \"Kolom verwijderen\",\n  \"deleteFile\": \"Weet u zeker dat u het bestand \\\"{0}\\\" wilt verwijderen?\",\n  \"deleteRow\": \"Rij verwijderen\",\n  \"dialogButtonSeparator\": \"of\",\n  \"dialogCancel\": \"Annuleren\",\n  \"dialogInsert\": \"Invoegen\",\n  \"directoryNotFound\": \"De map met deze naam is niet gevonden.\",\n  \"dropFilesHere\": \"Sleep bestanden hier naar toe om te uploaden\",\n  \"emptyFolder\": \"Lege map\",\n  \"fontName\": \"Selecteer lettertype\",\n  \"fontNameInherit\": \"(geërfd lettertype)\",\n  \"fontSize\": \"Selecteer lettergrootte\",\n  \"fontSizeInherit\": \"(geërfde lettergrootte)\",\n  \"foreColor\": \"Kleur\",\n  \"formatBlock\": \"Formaat\",\n  \"formatting\": \"Formaat\",\n  \"imageAltText\": \"Vervangende tekst\",\n  \"imageWebAddress\": \"Webadres\",\n  \"indent\": \"Inspringen\",\n  \"insertHtml\": \"HTML toevoegen\",\n  \"insertImage\": \"Afbeelding toevoegen\",\n  \"insertOrderedList\": \"Georderde lijst toevoegen\",\n  \"insertUnorderedList\": \"Ongeorderde lijst toevoegen\",\n  \"invalidFileType\": \"Het geseleteceerde bestand \\\"{0}\\\" is niet geldig. De volgende bestandstypen worden ondersteund {1}.\",\n  \"italic\": \"Cursief\",\n  \"justifyCenter\": \"Tekst centreren\",\n  \"justifyFull\": \"Uitvullen\",\n  \"justifyLeft\": \"Tekst links uitlijnen\",\n  \"justifyRight\": \"Tekst rechts uitlijnen\",\n  \"linkOpenInNewWindow\": \"Open link in nieuw venster\",\n  \"linkText\": \"Tekst\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkWebAddress\": \"Webadres\",\n  \"orderBy\": \"Sorteer op:\",\n  \"orderByName\": \"Naam\",\n  \"orderBySize\": \"Grootte\",\n  \"outdent\": \"Negatief inspringen\",\n  \"overwriteFile\": \"Het bestand met naam \\\"{0}\\\" bestaat reeds in deze map. Wilt u het bestand overschrijven?\",\n  \"search\": \"Zoek\",\n  \"strikethrough\": \"Doorhalen\",\n  \"styles\": \"Stijlen\",\n  \"subscript\": \"Subscript\",\n  \"superscript\": \"Superscript\",\n  \"underline\": \"Onderstrepen\",\n  \"unlink\": \"Hyperlink verwijderen\",\n  \"uploadFile\": \"Upload bestand\",\n  \"viewHtml\": \"View HTML\",\n  \"dialogUpdate\": \"Update\",\n  \"insertFile\": \"Insert file\"\n});\n}\n\n/* Filter cell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"clear\": \"Filter wissen\",\n  \"filter\": \"Filter\",\n  \"isFalse\": \"is niet waar\",\n  \"isTrue\": \"is waar\",\n  \"operator\": \"Operator\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"and\": \"En\",\n  \"cancel\": \"Annuleren\",\n  \"clear\": \"Filter wissen\",\n  \"filter\": \"Filter\",\n  \"info\": \"Toon items met waarde:\",\n  \"title\": \"Toon items met waarde\",\n  \"isFalse\": \"is niet waar\",\n  \"isTrue\": \"is waar\",\n  \"operator\": \"Operator\",\n  \"or\": \"Of\",\n  \"selectValue\": \"-Selecteer waarde-\",\n  \"value\": \"Waarde\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"Zoek\"\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"canceledit\": \"Annuleren\",\n    \"cancel\": \"Wijzigingen annuleren\",\n    \"create\": \"Item toevoegen\",\n    \"destroy\": \"Verwijderen\",\n    \"edit\": \"Bewerken\",\n    \"excel\": \"Export naar Excel\",\n    \"pdf\": \"Export naar PDF\",\n    \"save\": \"Wijzigingen opslaan\",\n    \"select\": \"Selecteren\",\n    \"update\": \"Bijwerken\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Annuleren\",\n    \"confirmation\": \"Weet u zeker dat u dit item wilt verwijderen?\",\n    \"confirmDelete\": \"Verwijderen\"\n  }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Sleep een kolomtitel in dit vak om de kolom te groeperen.\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"items {0} - {1} van {2}\",\n  \"empty\": \"Geen items om te tonen\",\n  \"first\": \"Ga naar eerste pagina\",\n  \"itemsPerPage\": \"items per pagina\",\n  \"last\": \"Ga naar laatste pagina\",\n  \"next\": \"Ga naar volgende pagina\",\n  \"of\": \"van {0}\",\n  \"page\": \"Pagina\",\n  \"previous\": \"Ga naar vorige pagina\",\n  \"refresh\": \"Verversen\",\n  \"morePages\": \"Meer pagina\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"items {0} - {1} van {2}\",\n  \"empty\": \"Geen items om te tonen\",\n  \"first\": \"Ga naar eerste pagina\",\n  \"itemsPerPage\": \"items per pagina\",\n  \"last\": \"Ga naar laatste pagina\",\n  \"next\": \"Ga naar volgende pagina\",\n  \"of\": \"van {0}\",\n  \"page\": \"Pagina\",\n  \"previous\": \"Ga naar vorige pagina\",\n  \"refresh\": \"Verversen\",\n  \"morePages\": \"Meer pagina\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"Toon hele dag\",\n  \"cancel\": \"Annuleren\",\n  \"editable\": {\n    \"confirmation\": \"Weet u zeker dat u deze afspraak wilt verwijderen?\"\n  },\n  \"date\": \"Datum\",\n  \"deleteWindowTitle\": \"Afspraak verwijderen\",\n  \"destroy\": \"Verwijderen\",\n  \"editor\": {\n    \"allDayEvent\": \"Duurt hele dag\",\n    \"description\": \"Omschrijving\",\n    \"editorTitle\": \"Afspraak\",\n    \"end\": \"Eind\",\n    \"endTimezone\": \"Eindtijd\",\n    \"repeat\": \"Terugkeerpatroon\",\n    \"separateTimezones\": \"Gebruik verschillende begin- en eindtijd\",\n    \"start\": \"Start\",\n    \"startTimezone\": \"Begintijd\",\n    \"timezone\": \"Pas tijdschema aan\",\n    \"timezoneEditorButton\": \"Tijdschema\",\n    \"timezoneEditorTitle\": \"Tijdschema's\",\n    \"title\": \"Onderwerp\",\n    \"noTimezone\": \"No timezone\"\n  },\n  \"event\": \"Afspraak\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Wilt u alleen dit exemplaar uit de reeks verwijderen of wilt u de hele reeks verwijderen?\",\n    \"deleteWindowOccurrence\": \"Verwijder exemplaar\",\n    \"deleteWindowSeries\": \"Verwijder reeks\",\n    \"deleteWindowTitle\": \"Verwijder terugkeerpatroon\",\n    \"editRecurring\": \"Wilt u alleen dit exemplaar uit de reeks bewerken of wilt u de hele reeks bewerken?\",\n    \"editWindowOccurrence\": \"Bewerken exemplaar\",\n    \"editWindowSeries\": \"Bewerken reeks\",\n    \"editWindowTitle\": \"Bewerken terugkeerpatroon\"\n  },\n  \"save\": \"Bewaren\",\n  \"showFullDay\": \"Toon hele dag\",\n  \"showWorkDay\": \"Toon werktijden\",\n  \"time\": \"Tijd\",\n  \"today\": \"Vandaag\",\n  \"views\": {\n    \"agenda\": \"Agenda\",\n    \"day\": \"Dag\",\n    \"month\": \"Maand\",\n    \"week\": \"Week\",\n    \"workWeek\": \"Work Week\",\n    \"timeline\": \"Tijdlijn\"\n  }\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Annuleren\",\n  \"dropFilesHere\": \"Sleep bestanden hier naartoe\",\n  \"headerStatusUploaded\": \"Gereed\",\n  \"headerStatusUploading\": \"Uploaden...\",\n  \"remove\": \"Verwijderen\",\n  \"retry\": \"Opnieuw\",\n  \"select\": \"Selecteer\",\n  \"statusFailed\": \"mislukt\",\n  \"statusUploaded\": \"gelukt\",\n  \"statusUploading\": \"bezig met uploaden\",\n  \"uploadSelectedFiles\": \"Bestanden uploaden\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Sluiten\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Annuleren\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Annuleren\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}