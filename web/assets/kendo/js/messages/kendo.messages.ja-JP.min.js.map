{"version": 3, "sources": ["messages/kendo.messages.ja-JP.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeView", "loading", "requestFailed", "retry", "Upload", "localization", "remove", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,KACTC,OAAU,WAMRR,MAAMC,GAAGQ,cACbT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,KACTC,OAAU,WAMRR,MAAMC,GAAGS,aACbV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,UACjBC,eAAkB,UAClBC,OAAU,OACVC,QAAW,IACXC,KAAQ,KACRC,SAAY,OACZC,KAAQ,MACRC,OAAU,WAMRlB,MAAMC,GAAGkB,SACbnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,KACRC,OAAU,KACVC,UAAa,KACbC,cAAiB,QACjBC,YAAe,QACfC,UAAa,QACbC,cAAiB,YACjBC,YAAe,WACfC,aAAgB,WAChBC,YAAe,OACfC,oBAAuB,mBACvBC,kBAAqB,gBACrBC,OAAU,QACVC,QAAW,UACXC,WAAc,aACdC,OAAU,aACVC,YAAe,UACfC,WAAc,UACdC,WAAc,WACdC,SAAY,WACZC,SAAY,eACZC,gBAAmB,cACnBC,SAAY,cACZC,gBAAmB,aACnBC,YAAe,SACfC,WAAc,SACdC,UAAa,MACbC,UAAa,MACbC,MAAS,OACTC,YAAe,SACfC,WAAc,SACdC,QAAW,aACXC,YAAe,MACfC,YAAe,IACfC,gBAAmB,iDACnBC,WAAc,kBACdC,cAAiB,8CACjBC,kBAAqB,0BACrBC,gBAAmB,WACnBC,aAAgB,SAChBC,WAAc,SACdC,YAAe,UACfC,eAAkB,WAClBC,UAAa,OACbC,eAAkB,WAClBC,SAAY,OACZC,YAAe,SACfC,oBAAuB,iBACvBC,aAAgB,KAChBC,aAAgB,KAChBC,sBAAyB,MACzBC,aAAgB,QAChBC,YAAe,UACfC,cAAiB,SACjBC,eAAkB,SAClBC,YAAe,SACfC,YAAe,SACfC,UAAa,OACbC,aAAgB,UAMd9E,MAAMC,GAAG8E,cACb/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpD6C,WAAc,SACdC,QAAW,YACXE,YAAe,IACfD,YAAe,MACfK,kBAAqB,0BACrBR,YAAe,SACfM,WAAc,kBACdD,gBAAmB,iDACnBE,cAAiB,8CACjBwB,cAAiB,uBACjBC,OAAU,QAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACnD8E,OAAU,OACVC,QAAW,OACXvE,OAAU,OACVwE,MAAS,MACTC,SAAY,SAMVtF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,UACNC,IAAO,WACPC,WAAc,SACdC,SAAY,QACZC,eAAkB,UAClBC,SAAY,UAEdC,QACEN,GAAM,UACNC,IAAO,WACPM,IAAO,SACPC,GAAM,YACNC,IAAO,SACPC,GAAM,aAERC,MACEX,GAAM,UACNC,IAAO,WACPM,IAAO,eACPC,GAAM,SACNC,IAAO,eACPC,GAAM,UAERE,OACEZ,GAAM,UACNC,IAAO,eAOP1F,MAAMC,GAAGqG,aACbtG,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQC,UACnDkG,KAAQ,eACRC,MAAS,cACTrB,OAAU,OACVC,QAAW,OACXvE,OAAU,OACVwE,MAAS,MACToB,IAAO,MACPC,GAAM,KACNC,YAAe,SACfrB,SAAY,MACZsB,MAAS,IACTpG,OAAU,WAMRR,MAAMC,GAAGqG,aACbtG,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,UACNC,IAAO,WACPC,WAAc,SACdC,SAAY,QACZC,eAAkB,UAClBC,SAAY,UAEdC,QACEN,GAAM,UACNC,IAAO,WACPM,IAAO,SACPC,GAAM,YACNC,IAAO,SACPC,GAAM,aAERC,MACEX,GAAM,UACNC,IAAO,WACPM,IAAO,eACPC,GAAM,SACNC,IAAO,eACPC,GAAM,UAERE,OACEZ,GAAM,UACNC,IAAO,eAOP1F,MAAMC,GAAG4G,mBACb7G,MAAMC,GAAG4G,iBAAiB1G,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4G,iBAAiB1G,UAAUC,QAAQC,UACzD4E,OAAU,QAMRjF,MAAMC,GAAG6G,QACb9G,MAAMC,GAAG6G,MAAM3G,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6G,MAAM3G,UAAUC,QAAQC,UAC9C0G,SACEC,SAAY,OACZC,OAAU,SACVC,YAAe,OACfC,aAAgB,OAChBC,IAAO,eAET5G,OAAU,QACV6G,4BAA+B,UAC/BC,sBAAyB,SACzBC,QAAW,KACXC,QACEC,aAAgB,OAChBC,YAAe,MACfC,IAAO,KACPC,gBAAmB,KACnBC,UAAa,OACbC,qBAAwB,OACxBC,gBAAmB,OACnBC,MAAS,KACTxB,MAAS,OACTyB,YAAe,MAEjBC,KAAQ,KACRC,OACEC,IAAO,IACPT,IAAO,KACPU,MAAS,IACTL,MAAS,KACTM,KAAQ,IACRC,KAAQ,QAORvI,MAAMC,GAAGuI,OACbxI,MAAMC,GAAGuI,KAAKrI,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuI,KAAKrI,UAAUC,QAAQC,UAC7CoI,UACEjI,OAAU,WACVkI,WAAc,QACdC,OAAU,YACVpB,QAAW,KACXqB,KAAQ,KACRC,MAAS,kBACTzB,IAAO,gBACPc,KAAQ,QACRY,OAAU,KACVC,OAAU,MAEZC,UACEC,aAAgB,QAChBC,aAAgB,oBAChBC,cAAiB,SAOjBnJ,MAAMC,GAAGmJ,YACbpJ,MAAMC,GAAGmJ,UAAUjJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmJ,UAAUjJ,UAAUC,QAAQC,UAClDgJ,MAAS,wCAMPrJ,MAAMC,GAAGqJ,iBACbtJ,MAAMC,GAAGqJ,eAAenJ,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqJ,eAAenJ,UAAUC,SAC/CmJ,YAAe,QACfC,cAAiB,WAMfxJ,MAAMC,GAAGwJ,QACbzJ,MAAMC,GAAGwJ,MAAMtJ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwJ,MAAMtJ,UAAUC,QAAQC,UAC9CqJ,SAAY,MACZC,QAAW,sBACXN,MAAS,eACTO,KAAQ,MACRC,GAAM,QACNC,aAAgB,gBAChBC,MAAS,YACTC,SAAY,WACZC,KAAQ,WACRC,KAAQ,YACRC,QAAW,KACXC,UAAa,aAMXpK,MAAMC,GAAGoK,gBACbrK,MAAMC,GAAGoK,cAAclK,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoK,cAAclK,UAAUC,QAAQC,UACtDqJ,SAAY,MACZC,QAAW,sBACXN,MAAS,eACTO,KAAQ,MACRC,GAAM,QACNC,aAAgB,gBAChBC,MAAS,YACTC,SAAY,WACZC,KAAQ,WACRC,KAAQ,YACRC,QAAW,KACXC,UAAa,aAMXpK,MAAMC,GAAGqK,YACbtK,MAAMC,GAAGqK,UAAUnK,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqK,UAAUnK,UAAUC,QAAQC,UAClDkK,cAAiB,oBACjBC,aAAgB,iBAChBC,UAAa,oBAMXzK,MAAMC,GAAGyK,iBACb1K,MAAMC,GAAGyK,eAAevK,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyK,eAAevK,UAAUC,QAAQC,UACvDkG,KAAQ,eACRoE,aAAgB,aAChB9J,OAAU,OACV+J,QAAW,kBACXpE,MAAS,gBACTnB,MAAS,MACTwF,GAAM,KACNrK,OAAU,QACV+E,WACEK,SAAY,QACZC,eAAkB,UAClBF,WAAc,SACdG,SAAY,SACZL,GAAM,UACNC,IAAO,eAOP1F,MAAMC,GAAG6K,mBACb9K,MAAMC,GAAG6K,iBAAiB3K,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6K,iBAAiB3K,UAAUC,QAAQC,UACzD0K,aACEC,MAAS,KACTC,OAAU,KACVC,MAAS,KACTC,OAAU,KACVC,QAAW,KACXC,OAAU,MAEZJ,QACEK,YAAe,cACfC,SAAY,OAEdL,OACEI,YAAe,cACfC,SAAY,MAEdJ,QACEI,SAAY,MACZD,YAAe,cACfE,SAAY,eAEdJ,SACEE,YAAe,cACfE,SAAY,cACZD,SAAY,MACZnD,IAAO,MAETiD,QACEC,YAAe,cACfE,SAAY,cACZD,SAAY,KACZ1B,GAAM,OAERlC,KACE8D,MAAS,MACTC,YAAe,KACfV,MAAS,KACTW,MAAS,KACTC,WAAc,KACdC,GAAM,OAERC,iBACE/B,MAAS,KACTgC,OAAU,OACVC,MAAS,OACTC,OAAU,OACV/B,KAAQ,MAEVgC,UACE9D,IAAO,IACP+D,QAAW,KACXC,QAAW,SAOXpM,MAAMC,GAAGoM,YACbrM,MAAMC,GAAGoM,UAAUlM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoM,UAAUlM,UAAUC,QAAQC,UAClDiM,OAAU,QACVlG,KAAQ,KACRmG,MAAS,OACTC,KAAQ,KACRC,YAAe,UACfC,YAAe,UACfC,MAAS,KACTzE,KAAQ,KACR1H,OAAU,QACV+G,QAAW,KACXqF,kBAAqB,UACrBC,cAAiB,yBACjBC,eAAkB,yBAClB5D,aAAgB,oBAChBf,OACEC,IAAO,IACPE,KAAQ,IACRyE,SAAY,MACZC,OAAU,KACV3E,MAAS,KAEX4E,oBACEL,kBAAqB,YACrBM,uBAA0B,UAC1BC,mBAAsB,QACtBC,gBAAmB,YACnBC,qBAAwB,UACxBC,iBAAoB,QACpBC,gBAAmB,qCACnBC,cAAiB,sCAEnBhG,QACEhB,MAAS,OACTwB,MAAS,KACTL,IAAO,KACP8F,YAAe,UACfC,YAAe,KACfC,OAAU,OACVC,SAAY,IACZC,cAAiB,WACjBC,YAAe,WACfC,kBAAqB,sBACrBC,oBAAuB,SACvBC,qBAAwB,UACxBC,cAAiB,UACjBC,WAAc,eACdzG,YAAe,WAOf1H,MAAMC,GAAGmO,SACbpO,MAAMC,GAAGmO,OAAOjO,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmO,OAAOjO,UAAUC,SACvCiO,oBAAuB,MACvBC,oBAAuB,SAMrBtO,MAAMC,GAAGsO,WACbvO,MAAMC,GAAGsO,SAASpO,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsO,SAASpO,UAAUC,QAAQC,UACjDmO,QAAW,cACXC,cAAiB,iBACjBC,MAAS,SAMP1O,MAAMC,GAAG0O,SACb3O,MAAMC,GAAG0O,OAAOxO,UAAUC,QAAQwO,aAClC9O,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0O,OAAOxO,UAAUC,QAAQwO,cAC/C9F,OAAU,aACVtI,OAAU,QACVkO,MAAS,MACTG,OAAU,KACVC,oBAAuB,cACvB9J,cAAiB,uBACjB+J,gBAAmB,UACnBC,eAAkB,WAClBC,cAAiB,KACjBC,aAAgB,KAChBC,sBAAyB,aACzBC,qBAAwB,QAMtBpP,MAAMC,GAAGoP,YACbrP,MAAMC,GAAGoP,UAAUlP,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoP,UAAUlP,UAAUC,QAAQC,UAClDiP,SAAY,YACZC,QAAW,YACXC,IAAO,6BACPC,IAAO,6BACPC,KAAQ,YACRC,MAAS,kBACTC,IAAO,kBACPxJ,KAAQ,kBAMNpG,MAAMC,GAAG4P,SACb7P,MAAMC,GAAG4P,OAAO1P,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4P,OAAO1P,UAAUC,QAAQwO,cAC/CkB,MAAS,SAMP9P,MAAMC,GAAG8P,QACb/P,MAAMC,GAAG8P,MAAM5P,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8P,MAAM5P,UAAUC,QAAQwO,cAC9CoB,OAAU,UAMRhQ,MAAMC,GAAGgQ,UACbjQ,MAAMC,GAAGgQ,QAAQ9P,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgQ,QAAQ9P,UAAUC,QAAQwO,cAChDoB,OAAU,OACVxP,OAAU,WAKRR,MAAMC,GAAGiQ,SACblQ,MAAMC,GAAGiQ,OAAO/P,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiQ,OAAO/P,UAAUC,QAAQwO,cAC/CoB,OAAU,OACVxP,OAAU,YAIT2P,OAAOnQ,MAAMoQ", "file": "kendo.messages.ja-JP.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"適用\",\n  \"cancel\": \"キャンセル\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"適用\",\n  \"cancel\": \"キャンセル\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"昇順に並べ替え\",\n  \"sortDescending\": \"降順に並べ替え\",\n  \"filter\": \"フィルタ\",\n  \"columns\": \"列\",\n  \"done\": \"完了\",\n  \"settings\": \"列の設定\",\n  \"lock\": \"ロック\",\n  \"unlock\": \"ロック解除\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"太字\",\n  \"italic\": \"斜体\",\n  \"underline\": \"下線\",\n  \"strikethrough\": \"取り消し線\",\n  \"superscript\": \"上付き文字\",\n  \"subscript\": \"下付き文字\",\n  \"justifyCenter\": \"テキストを中央揃え\",\n  \"justifyLeft\": \"テキストを左揃え\",\n  \"justifyRight\": \"テキストを右揃え\",\n  \"justifyFull\": \"両端揃え\",\n  \"insertUnorderedList\": \"順序付けされていないリストを挿入\",\n  \"insertOrderedList\": \"順序付けされたリストを挿入\",\n  \"indent\": \"インデント\",\n  \"outdent\": \"インデント解除\",\n  \"createLink\": \"ハイパーリンクを挿入\",\n  \"unlink\": \"ハイパーリンクを削除\",\n  \"insertImage\": \"イメージを挿入\",\n  \"insertFile\": \"ファイルを挿入\",\n  \"insertHtml\": \"HTML を挿入\",\n  \"viewHtml\": \"HTML を表示\",\n  \"fontName\": \"フォント ファミリを選択\",\n  \"fontNameInherit\": \"(継承されたフォント)\",\n  \"fontSize\": \"フォント サイズを選択\",\n  \"fontSizeInherit\": \"(継承されたサイズ)\",\n  \"formatBlock\": \"フォーマット\",\n  \"formatting\": \"フォーマット\",\n  \"foreColor\": \"カラー\",\n  \"backColor\": \"背景色\",\n  \"style\": \"スタイル\",\n  \"emptyFolder\": \"空のフォルダ\",\n  \"uploadFile\": \"アップロード\",\n  \"orderBy\": \"次の要素による整列:\",\n  \"orderBySize\": \"サイズ\",\n  \"orderByName\": \"名\",\n  \"invalidFileType\": \"選択されたファイル {0} は無効です。サポートされているファイル タイプは {1} です。\",\n  \"deleteFile\": \"{0} を本当に削除しますか?\",\n  \"overwriteFile\": \"{0} という名前のファイルは既にカレント ディレクトリに存在します。上書きしますか?\",\n  \"directoryNotFound\": \"この名前のディレクトリが見つかりませんでした。\",\n  \"imageWebAddress\": \"Web アドレス\",\n  \"imageAltText\": \"代替テキスト\",\n  \"imageWidth\": \"幅 (px)\",\n  \"imageHeight\": \"高さ (px)\",\n  \"fileWebAddress\": \"Web アドレス\",\n  \"fileTitle\": \"タイトル\",\n  \"linkWebAddress\": \"Web アドレス\",\n  \"linkText\": \"テキスト\",\n  \"linkToolTip\": \"ツールチップ\",\n  \"linkOpenInNewWindow\": \"リンクを新規ウィンドウで開く\",\n  \"dialogUpdate\": \"更新\",\n  \"dialogInsert\": \"挿入\",\n  \"dialogButtonSeparator\": \"または\",\n  \"dialogCancel\": \"キャンセル\",\n  \"createTable\": \"テーブルの作成\",\n  \"addColumnLeft\": \"左に列を追加\",\n  \"addColumnRight\": \"右に列を追加\",\n  \"addRowAbove\": \"上に行を追加\",\n  \"addRowBelow\": \"下に行を追加\",\n  \"deleteRow\": \"行を削除\",\n  \"deleteColumn\": \"列を削除\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"アップロード\",\n  \"orderBy\": \"次の要素による整列\",\n  \"orderByName\": \"名\",\n  \"orderBySize\": \"サイズ\",\n  \"directoryNotFound\": \"この名前のディレクトリは見つかりませんでした。\",\n  \"emptyFolder\": \"空のフォルダ\",\n  \"deleteFile\": \"{0} を本当に削除しますか?\",\n  \"invalidFileType\": \"選択されたファイル {0} は無効です。サポートされているファイル タイプは {1} です。\",\n  \"overwriteFile\": \"{0} という名前のファイルは既にカレント ディレクトリに存在します。上書きしますか?\",\n  \"dropFilesHere\": \"ここにファイルをドロップしてアップロード\",\n  \"search\": \"検索\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"真である\",\n  \"isFalse\": \"偽である\",\n  \"filter\": \"フィルタ\",\n  \"clear\": \"クリア\",\n  \"operator\": \"演算子\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"が次の値と同じ\",\n    \"neq\": \"が次の値と異なる\",\n    \"startswith\": \"が次で始まる\",\n    \"contains\": \"が次を含む\",\n    \"doesnotcontain\": \"が次を含まない\",\n    \"endswith\": \"が次で終わる\"\n  },\n  \"number\": {\n    \"eq\": \"が次の値と同じ\",\n    \"neq\": \"が次の値と異なる\",\n    \"gte\": \"が次の値以上\",\n    \"gt\": \"が次の値より大きい\",\n    \"lte\": \"が次の値以下\",\n    \"lt\": \"が次の値より小さい\"\n  },\n  \"date\": {\n    \"eq\": \"が次の値と同じ\",\n    \"neq\": \"が次の値と異なる\",\n    \"gte\": \"次の値と同じかそれより後\",\n    \"gt\": \"次の値より後\",\n    \"lte\": \"次の値と同じかそれより前\",\n    \"lt\": \"次の値より前\"\n  },\n  \"enums\": {\n    \"eq\": \"が次の値と同じ\",\n    \"neq\": \"が次の値と異なる\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"次の値がある項目を表示:\",\n  \"title\": \"次の値がある項目を表示\",\n  \"isTrue\": \"真である\",\n  \"isFalse\": \"偽である\",\n  \"filter\": \"フィルタ\",\n  \"clear\": \"クリア\",\n  \"and\": \"And\",\n  \"or\": \"Or\",\n  \"selectValue\": \"-値を選択-\",\n  \"operator\": \"演算子\",\n  \"value\": \"値\",\n  \"cancel\": \"キャンセル\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"が次の値と同じ\",\n    \"neq\": \"が次の値と異なる\",\n    \"startswith\": \"が次で始まる\",\n    \"contains\": \"が次を含む\",\n    \"doesnotcontain\": \"が次を含まない\",\n    \"endswith\": \"が次で終わる\"\n  },\n  \"number\": {\n    \"eq\": \"が次の値と同じ\",\n    \"neq\": \"が次の値と異なる\",\n    \"gte\": \"が次の値以上\",\n    \"gt\": \"が次の値より大きい\",\n    \"lte\": \"が次の値以下\",\n    \"lt\": \"が次の値より小さい\"\n  },\n  \"date\": {\n    \"eq\": \"が次の値と同じ\",\n    \"neq\": \"が次の値と異なる\",\n    \"gte\": \"次の値と同じかそれより後\",\n    \"gt\": \"次の値より後\",\n    \"lte\": \"次の値と同じかそれより前\",\n    \"lt\": \"次の値より前\"\n  },\n  \"enums\": {\n    \"eq\": \"が次の値と同じ\",\n    \"neq\": \"が次の値と異なる\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"検索\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"子の追加\",\n    \"append\": \"タスクの追加\",\n    \"insertAfter\": \"下に追加\",\n    \"insertBefore\": \"上に追加\",\n    \"pdf\": \"PDF にエクスポート\"\n  },\n  \"cancel\": \"キャンセル\",\n  \"deleteDependencyWindowTitle\": \"依存関係を削除\",\n  \"deleteTaskWindowTitle\": \"タスクを削除\",\n  \"destroy\": \"削除\",\n  \"editor\": {\n    \"assingButton\": \"割り当て\",\n    \"editorTitle\": \"タスク\",\n    \"end\": \"終点\",\n    \"percentComplete\": \"完了\",\n    \"resources\": \"リソース\",\n    \"resourcesEditorTitle\": \"リソース\",\n    \"resourcesHeader\": \"リソース\",\n    \"start\": \"始点\",\n    \"title\": \"タイトル\",\n    \"unitsHeader\": \"単位\"\n  },\n  \"save\": \"保存\",\n  \"views\": {\n    \"day\": \"日\",\n    \"end\": \"終点\",\n    \"month\": \"月\",\n    \"start\": \"始点\",\n    \"week\": \"週\",\n    \"year\": \"年\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"変更のキャンセル\",\n    \"canceledit\": \"キャンセル\",\n    \"create\": \"新規レコードを追加\",\n    \"destroy\": \"削除\",\n    \"edit\": \"編集\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"save\": \"変更の保存\",\n    \"select\": \"選択\",\n    \"update\": \"更新\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"キャンセル\",\n    \"confirmation\": \"このレコードを本当に削除しますか?\",\n    \"confirmDelete\": \"削除\"\n  }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"列ヘッダーをここにドラッグ アンド ドロップして、その列でグループ化\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"値を増やす\",\n  \"downArrowText\": \"値を減らす\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} ({2} 項目中)\",\n  \"empty\": \"表示する項目がありません\",\n  \"page\": \"ページ\",\n  \"of\": \"/ {0}\",\n  \"itemsPerPage\": \"項目 (1 ページあたり)\",\n  \"first\": \"最初のページに移動\",\n  \"previous\": \"前のページに移動\",\n  \"next\": \"次のページに移動\",\n  \"last\": \"最後のページに移動\",\n  \"refresh\": \"更新\",\n  \"morePages\": \"その他のページ\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} ({2} 項目中)\",\n  \"empty\": \"表示する項目がありません\",\n  \"page\": \"ページ\",\n  \"of\": \"/ {0}\",\n  \"itemsPerPage\": \"項目 (1 ページあたり)\",\n  \"first\": \"最初のページに移動\",\n  \"previous\": \"前のページに移動\",\n  \"next\": \"次のページに移動\",\n  \"last\": \"最後のページに移動\",\n  \"refresh\": \"更新\",\n  \"morePages\": \"その他のページ\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"ここにデータ フィールドをドロップ\",\n  \"columnFields\": \"ここに列フィールドをドロップ\",\n  \"rowFields\": \"ここに行フィールドをドロップ\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"次の値がある項目を表示:\",\n  \"filterFields\": \"フィールド フィルタ\",\n  \"filter\": \"フィルタ\",\n  \"include\": \"フィールドをインクルード...\",\n  \"title\": \"インクルードするフィールド\",\n  \"clear\": \"クリア\",\n  \"ok\": \"OK\",\n  \"cancel\": \"キャンセル\",\n  \"operators\": {\n    \"contains\": \"が次を含む\",\n    \"doesnotcontain\": \"が次を含まない\",\n    \"startswith\": \"が次で始まる\",\n    \"endswith\": \"が次で終わる\",\n    \"eq\": \"が次の値と同じ\",\n    \"neq\": \"が次の値と異なる\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"なし\",\n    \"hourly\": \"毎時\",\n    \"daily\": \"毎日\",\n    \"weekly\": \"毎週\",\n    \"monthly\": \"毎月\",\n    \"yearly\": \"毎年\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"次の間隔で繰り返す: \",\n    \"interval\": \" 時間\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"次の間隔で繰り返す: \",\n    \"interval\": \" 日\"\n  },\n  \"weekly\": {\n    \"interval\": \" 週間\",\n    \"repeatEvery\": \"次の間隔で繰り返す: \",\n    \"repeatOn\": \"次のときに繰り返す: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"次の間隔で繰り返す: \",\n    \"repeatOn\": \"次のときに繰り返す: \",\n    \"interval\": \" か月\",\n    \"day\": \"日 \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"次の間隔で繰り返す: \",\n    \"repeatOn\": \"次のときに繰り返す: \",\n    \"interval\": \" 年\",\n    \"of\": \" の \"\n  },\n  \"end\": {\n    \"label\": \"終了:\",\n    \"mobileLabel\": \"終了\",\n    \"never\": \"なし\",\n    \"after\": \"後 \",\n    \"occurrence\": \" 回\",\n    \"on\": \"オン \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"最初\",\n    \"second\": \"2 番目\",\n    \"third\": \"3 番目\",\n    \"fourth\": \"4 番目\",\n    \"last\": \"最後\"\n  },\n  \"weekdays\": {\n    \"day\": \"日\",\n    \"weekday\": \"平日\",\n    \"weekend\": \"週末\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"すべての日\",\n  \"date\": \"日付\",\n  \"event\": \"イベント\",\n  \"time\": \"時間\",\n  \"showFullDay\": \"24 時間表示\",\n  \"showWorkDay\": \"営業時間を表示\",\n  \"today\": \"今日\",\n  \"save\": \"保存\",\n  \"cancel\": \"キャンセル\",\n  \"destroy\": \"削除\",\n  \"deleteWindowTitle\": \"イベントを削除\",\n  \"ariaSlotLabel\": \"{0:t} ～ {1:t} 時の範囲から選択\",\n  \"ariaEventLabel\": \"{0} ({1:D} 日の {2:t} 時)\",\n  \"confirmation\": \"このイベントを本当に削除しますか?\",\n  \"views\": {\n    \"day\": \"日\",\n    \"week\": \"週\",\n    \"workWeek\": \"稼働日\",\n    \"agenda\": \"予定\",\n    \"month\": \"月\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"定期的な項目を削除\",\n    \"deleteWindowOccurrence\": \"現在の回を削除\",\n    \"deleteWindowSeries\": \"系列を削除\",\n    \"editWindowTitle\": \"定期的な項目を編集\",\n    \"editWindowOccurrence\": \"現在の回を編集\",\n    \"editWindowSeries\": \"系列を編集\",\n    \"deleteRecurring\": \"このイベントの回のみを削除しますか、それとも系列全体を削除しますか?\",\n    \"editRecurring\": \"このイベントの回のみを編集しますか、それとも系列全体を編集しますか?\"\n  },\n  \"editor\": {\n    \"title\": \"タイトル\",\n    \"start\": \"始点\",\n    \"end\": \"終点\",\n    \"allDayEvent\": \"終日のイベント\",\n    \"description\": \"説明\",\n    \"repeat\": \"繰り返す\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"開始タイムゾーン\",\n    \"endTimezone\": \"終了タイムゾーン\",\n    \"separateTimezones\": \"開始と終了で別のタイムゾーンを使用する\",\n    \"timezoneEditorTitle\": \"タイムゾーン\",\n    \"timezoneEditorButton\": \"タイム ゾーン\",\n    \"timezoneTitle\": \"タイム ゾーン\",\n    \"noTimezone\": \"タイムゾーンがありません\",\n    \"editorTitle\": \"イベント\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"増やす\",\n  \"decreaseButtonTitle\": \"減らす\"\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"ロードしています...\",\n  \"requestFailed\": \"要求を実行できませんでした。\",\n  \"retry\": \"再試行\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"ファイルを選択...\",\n  \"cancel\": \"キャンセル\",\n  \"retry\": \"再試行\",\n  \"remove\": \"削除\",\n  \"uploadSelectedFiles\": \"ファイルをアップロード\",\n  \"dropFilesHere\": \"ここにファイルをドロップしてアップロード\",\n  \"statusUploading\": \"アップロード中\",\n  \"statusUploaded\": \"アップロード済み\",\n  \"statusWarning\": \"警告\",\n  \"statusFailed\": \"失敗\",\n  \"headerStatusUploading\": \"アップロード中...\",\n  \"headerStatusUploaded\": \"完了\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} が必要です\",\n  \"pattern\": \"{0} は無効です\",\n  \"min\": \"{0} は {1} より大きいか同じ値にしてください\",\n  \"max\": \"{0} は {1} より小さいか同じ値にしてください\",\n  \"step\": \"{0} は無効です\",\n  \"email\": \"{0} は無効な電子メールです\",\n  \"url\": \"{0} は無効な URL です\",\n  \"date\": \"{0} は無効な日付です\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"閉じる\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"オーケー\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"オーケー\",\n  \"cancel\": \"キャンセル\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"オーケー\",\n  \"cancel\": \"キャンセル\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}