{"version": 3, "sources": ["messages/kendo.messages.bg-BG.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "Validator", "prototype", "options", "messages", "extend", "required", "pattern", "min", "max", "step", "email", "url", "date", "dateCompare", "TreeView", "loading", "requestFailed", "retry", "Upload", "localization", "select", "cancel", "remove", "uploadSelectedFiles", "dropFilesHere", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "dialogs", "apply", "save", "okText", "exportAsDialog", "title", "labels", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "formatCellsDialog", "categories", "number", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "justifyCenter", "justifyRight", "justifyFull", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "text", "custom", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "comparer", "value", "start", "end", "onInvalidData", "rejectInput", "showWarning", "showHint", "placeholders", "typeTitle", "typeMessage", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "sortAscending", "sortDescending", "filterByValue", "filterByCondition", "search", "clear", "blanks", "operatorNone", "and", "or", "operators", "string", "contains", "doesnotcontain", "startswith", "endswith", "eq", "neq", "lt", "gt", "gte", "lte", "colorPicker", "reset", "customColor", "toolbar", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "alignment", "alignmentButtons", "backgroundColor", "bold", "borders", "copy", "cut", "deleteColumn", "deleteRow", "excelImport", "filter", "fontFamily", "fontSize", "format", "formatTypes", "automatic", "percent", "financial", "time", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "italic", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sort", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "underline", "validation", "view", "nameBox", "errors", "shiftingNonblankCells", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "unitsHeader", "views", "day", "month", "week", "year", "FileBrowser", "uploadFile", "orderBy", "orderByName", "orderBySize", "directoryNotFound", "emptyFolder", "deleteFile", "invalidFileType", "overwriteFile", "FlatColorPicker", "noColor", "clearColor", "ColorPicker", "DateRangePicker", "startLabel", "endLabel", "FilterMenu", "enums", "<PERSON><PERSON><PERSON>ell", "ColumnMenu", "column", "columns", "done", "settings", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "Grid", "commands", "canceledit", "create", "edit", "excel", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "groupHeader", "ungroup<PERSON>eader", "ListBox", "tools", "moveUp", "moveDown", "transferTo", "transferFrom", "transferAllTo", "transferAllFrom", "TreeList", "noRows", "createchild", "Pager", "allPages", "page", "display", "empty", "refresh", "itemsPerPage", "next", "previous", "morePages", "TreeListPager", "info", "selectValue", "isFalse", "isTrue", "operator", "additionalOperator", "additionalValue", "logic", "into", "FilterMultiCheck", "checkAll", "clearAll", "selectedItemsFormat", "Groupable", "Editor", "createLink", "fontName", "fontNameInherit", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "justifyLeft", "outdent", "strikethrough", "style", "subscript", "superscript", "unlink", "backColor", "foreColor", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "dialogOk", "imageAltText", "imageWebAddress", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "createTable", "styles", "formatting", "viewHtml", "dialogUpdate", "insertFile", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "cellSpacing", "cellPadding", "cellMargin", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "clearSelectedFiles", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "Scheduler", "allDay", "allDayEvent", "description", "endTimezone", "repeat", "separateTimezones", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "today", "agenda", "workWeek", "showFullDay", "showWorkDay", "Dialog", "close", "<PERSON><PERSON>", "Confirm", "Prompt", "progress", "DateInput", "hour", "minute", "dayperiod", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAEVC,MAAMC,GAAGC,YACbF,MAAMC,GAAGC,UAAUC,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,UAAUC,UAAUC,QAAQC,UAClDE,SAAY,0BACZC,QAAW,mBACXC,IAAO,gDACPC,IAAO,+CACPC,KAAQ,mBACRC,MAAS,yBACTC,IAAO,uBACPC,KAAQ,wBACRC,YAAe,8DAMbf,MAAMC,GAAGe,WACbhB,MAAMC,GAAGe,SAASb,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGe,SAASb,UAAUC,QAAQC,UACjDY,QAAW,eACXC,cAAiB,oBACjBC,MAAS,mBAMPnB,MAAMC,GAAGmB,SACbpB,MAAMC,GAAGmB,OAAOjB,UAAUC,QAAQiB,aAClCvB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmB,OAAOjB,UAAUC,QAAQiB,cAC/CC,OAAU,oBACVC,OAAU,QACVJ,MAAS,gBACTK,OAAU,WACVC,oBAAuB,iBACvBC,cAAiB,4BACjBC,gBAAmB,YACnBC,eAAkB,WAClBC,cAAiB,UACjBC,aAAgB,SAChBC,sBAAyB,eACzBC,qBAAwB,gBAMtBhC,MAAMiC,aAAejC,MAAMiC,YAAY5B,SAAS6B,gBACpDlC,MAAMiC,YAAY5B,SAAS6B,cAC3BpC,EAAEQ,QAAO,EAAMN,MAAMiC,YAAY5B,SAAS6B,eACxCC,WAAc,iBACdC,cAAiB,mBACjBC,wBAA2B,gCAC3BC,sBAAyB,8BACzBC,eAAkB,iBAClBC,WAAc,eACdC,UAAa,gBACbC,YAAe,gBACfC,aAAgB,gBAChBC,UAAa,iBAIX5C,MAAMiC,aAAejC,MAAMiC,YAAY5B,SAASwC,UACpD7C,MAAMiC,YAAY5B,SAASwC,QAC3B/C,EAAEQ,QAAO,EAAMN,MAAMiC,YAAY5B,SAASwC,SACxCC,MAAS,UACTC,KAAQ,SACRxB,OAAU,SACVC,OAAU,WACVwB,OAAU,KACVC,gBACEC,MAAS,iBACTC,QACEC,SAAY,cACZC,WAAc,cACdC,WAAc,cACdC,UAAa,iBACbC,QAAW,YACXC,YAAe,aACfC,MAAS,YACTC,WAAc,aACdC,OAAU,YACVC,aAAgB,eAChBC,WAAc,eAGlBC,mBACEb,MAAS,cACTc,YACEC,OAAU,QACVC,SAAY,SACZpD,KAAQ,SAGZqD,kBACEjB,MAAS,SAEXkB,gBACElB,MAAS,oBAEXmB,eACEnB,MAAS,WAEXoB,iBACEpB,MAAS,eACTqB,SACCC,aAAgB,sBAChBC,cAAiB,aACjBC,aAAgB,uBAChBC,YAAe,0BACfC,SAAY,qBACZC,YAAe,yBACfC,YAAe,uBAGlBC,aACE7B,MAAS,wBACTqB,SACES,WAAc,iBACdC,kBAAqB,yBACrBC,gBAAmB,uBACnBC,QAAW,sBAGfC,cACElC,MAAS,sBACTqB,SACEc,YAAe,sBACfC,WAAc,kBACdC,cAAiB,kBACjBC,SAAY,wBAGhBC,kBACEvC,MAAS,oBACTwC,YAAe,qCACfC,UAAa,eACbC,UACEC,IAAO,iBACP5B,OAAU,QACV6B,KAAQ,QACRhF,KAAQ,OACRiF,OAAU,YAEZC,WACEC,YAAe,eACfC,SAAY,cACZC,QAAW,QACXC,WAAc,aACdC,QAAW,WACXC,WAAc,gBACdC,qBAAwB,yBACxBC,kBAAqB,yBAEvBC,kBACER,YAAe,mBACfC,SAAY,kBACZC,QAAW,kBACXC,WAAc,uBACdC,QAAW,eACXC,WAAc,oBACdC,qBAAwB,6BACxBC,kBAAqB,4BACrBT,OAAU,sCAEZ5C,QACEyC,SAAY,WACZc,SAAY,WACZjG,IAAO,MACPC,IAAO,OACPiG,MAAS,WACTC,MAAS,SACTC,IAAO,OACPC,cAAiB,sBACjBC,YAAe,mBACfC,YAAe,wBACfC,SAAY,0BACZtB,UAAa,WACbD,YAAe,aAEjBwB,cACEC,UAAa,oBACbC,YAAe,uBAGnBC,oBACEC,aAAgB,mDAElBC,mBACErE,MAAS,uBACToE,aAAgB,iGAChBnE,QACEqE,QAAW,cACXC,OAAU,eACVC,SAAY,iBAGhBC,4BACEL,aAAgB,mFAKhBtH,MAAMiC,aAAejC,MAAMiC,YAAY5B,SAASuH,aACpD5H,MAAMiC,YAAY5B,SAASuH,WAC3B9H,EAAEQ,QAAO,EAAMN,MAAMiC,YAAY5B,SAASuH,YACxCC,cAAiB,qBACjBC,eAAkB,qBAClBC,cAAiB,wBACjBC,kBAAqB,wBACrBlF,MAAS,UACTmF,OAAU,QACVC,MAAS,WACTC,OAAU,iBACVC,aAAgB,eAChBC,IAAO,IACPC,GAAM,MACNC,WACEC,QACEC,SAAY,UACZC,eAAkB,aAClBC,WAAc,YACdC,SAAY,eAEd9H,MACE+H,GAAM,aACNC,IAAO,gBACPC,GAAM,UACNC,GAAM,UAER/E,QACE4E,GAAM,aACNC,IAAO,gBACPG,IAAO,2BACPD,GAAM,iBACNE,IAAO,0BACPH,GAAM,qBAMR/I,MAAMiC,aAAejC,MAAMiC,YAAY5B,SAAS8I,cACpDnJ,MAAMiC,YAAY5B,SAAS8I,YAC3BrJ,EAAEQ,QAAO,EAAMN,MAAMiC,YAAY5B,SAAS8I,aACxCC,MAAS,cACTC,YAAe,iBACfvG,MAAS,UACTvB,OAAU,YAIRvB,MAAMiC,aAAejC,MAAMiC,YAAY5B,SAASiJ,UACpDtJ,MAAMiC,YAAY5B,SAASiJ,QAC3BxJ,EAAEQ,QAAO,EAAMN,MAAMiC,YAAY5B,SAASiJ,SACxCC,cAAiB,uBACjBC,eAAkB,wBAClBC,YAAe,oBACfC,YAAe,oBACfC,UAAa,eACbC,kBACEpF,aAAgB,sBAChBC,cAAiB,aACjBC,aAAgB,uBAChBC,YAAe,0BACfC,SAAY,qBACZC,YAAe,yBACfC,YAAe,yBAEjB+E,gBAAmB,MACnBC,KAAQ,cACRC,QAAW,UACXC,KAAQ,UACRC,IAAO,SACPC,aAAgB,gBAChBC,UAAa,aACbC,YAAe,0BACfC,OAAU,aACVC,WAAc,QACdC,SAAY,kBACZC,OAAU,qBACVC,aACEC,UAAa,cACbzG,OAAU,QACV0G,QAAW,UACXC,UAAa,WACb1G,SAAY,SACZpD,KAAQ,OACR+J,KAAQ,MACRC,SAAY,aACZC,SAAY,SACZC,YAAe,kBAEjBC,sBAAyB,kBACzBC,sBAAyB,mBACzBC,OAAU,sBACVC,eACE/F,YAAe,sBACfC,WAAc,kBACdC,cAAiB,kBACjBC,SAAY,uBAEd6F,OAAU,SACVC,MAAS,wBACTC,cACEvG,WAAc,wBACdC,kBAAqB,wBACrBC,gBAAmB,wBACnBC,QAAW,uBAEbqG,KAAQ,YACRC,MAAS,YACTC,aACEC,KAAQ,gBACRC,KAAQ,gBAEVC,OAAU,iBACVC,KAAQ,WACRC,QAAW,qBACXC,SAAY,qBACZC,aACEC,aAAgB,0BAChBC,cAAiB,0BACjBC,aAAgB,8BAChBC,cAAiB,+BAEnBC,UAAa,iBACbC,SAAY,sBACZC,UAAa,aACbC,WAAc,0BAIZzM,MAAMiC,aAAejC,MAAMiC,YAAY5B,SAASqM,OACpD1M,MAAMiC,YAAY5B,SAASqM,KAC3B5M,EAAEQ,QAAO,EAAMN,MAAMiC,YAAY5B,SAASqM,MACxCC,QAAW,gBACXC,QACEC,sBAAyB,gKAE3BC,MACEC,KAAQ,SACRC,OAAU,WACVC,KAAQ,YAORjN,MAAMC,GAAGiN,SACblN,MAAMC,GAAGiN,OAAO/M,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiN,OAAO/M,UAAUC,SACvC+M,oBAAuB,UACvBC,oBAAuB,YAMrBpN,MAAMC,GAAGoN,iBACbrN,MAAMC,GAAGoN,eAAelN,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoN,eAAelN,UAAUC,SAC/CkN,YAAe,qBACfC,cAAiB,uBAMfvN,MAAMC,GAAGuN,cACbxN,MAAMC,GAAGuN,YAAYrN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuN,YAAYrN,UAAUC,QAAQC,UACpDoN,MAAS,QACTC,KAAQ,kBACRC,KAAQ,uBACRC,OAAU,qBACVC,QAAW,wBACXC,WAAc,eAMZ9N,MAAMC,GAAG8N,QACb/N,MAAMC,GAAG8N,MAAM5N,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8N,MAAM5N,UAAUC,QAAQC,UAC9C2N,SACEC,SAAY,cACZC,OAAU,gBACVC,YAAe,gBACfC,aAAgB,gBAChBC,IAAO,mBAET9M,OAAU,SACV+M,4BAA+B,gBAC/BC,sBAAyB,gBACzBC,QAAW,SACXC,QACEC,aAAgB,QAChBC,YAAe,SACf9H,IAAO,OACP+H,gBAAmB,YACnBC,UAAa,UACbC,qBAAwB,UACxBC,gBAAmB,UACnBnI,MAAS,SACT1D,MAAS,WACT8L,YAAe,WAEjBjM,KAAQ,SACRkM,OACEC,IAAO,MACPrI,IAAO,OACPsI,MAAS,QACTvI,MAAS,SACTwI,KAAQ,UACRC,KAAQ,aAORrP,MAAMC,GAAGqP,cACbtP,MAAMC,GAAGqP,YAAYnP,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqP,YAAYnP,UAAUC,QAAQC,UACpDkP,WAAc,YACdC,QAAW,aACXC,YAAe,MACfC,YAAe,SACfC,kBAAqB,oCACrBC,YAAe,eACfC,WAAc,8CACdC,gBAAmB,0EACnBC,cAAiB,kFACjBrO,cAAiB,mCACjBuG,OAAU,WAORjI,MAAMC,GAAG+P,kBACbhQ,MAAMC,GAAG+P,gBAAgB7P,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+P,gBAAgB7P,UAAUC,QAAQC,UACxDyC,MAAS,UACTvB,OAAU,SACV0O,QAAW,WACXC,WAAc,iBAMZlQ,MAAMC,GAAGkQ,cACbnQ,MAAMC,GAAGkQ,YAAYhQ,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkQ,YAAYhQ,UAAUC,QAAQC,UACpDyC,MAAS,UACTvB,OAAU,SACV0O,QAAW,WACXC,WAAc,iBAMZlQ,MAAMC,GAAGmQ,kBACbpQ,MAAMC,GAAGmQ,gBAAgBjQ,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmQ,gBAAgBjQ,UAAUC,QAAQC,UACxDgQ,WAAc,SACdC,SAAY,UAMVtQ,MAAMC,GAAGsQ,aACbvQ,MAAMC,GAAGsQ,WAAWpQ,UAAUC,QAAQmI,UACtCzI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsQ,WAAWpQ,UAAUC,QAAQmI,WACnDC,QACEK,GAAM,aACNC,IAAO,gBACPH,WAAc,YACdF,SAAY,UACZC,eAAkB,aAClBE,SAAY,eAEd3E,QACE4E,GAAM,aACNC,IAAO,gBACPG,IAAO,2BACPD,GAAM,iBACNE,IAAO,0BACPH,GAAM,iBAERjI,MACE+H,GAAM,aACNC,IAAO,gBACPG,IAAO,sBACPD,GAAM,SACNE,IAAO,uBACPH,GAAM,WAERyH,OACE3H,GAAM,aACNC,IAAO,oBAOP9I,MAAMC,GAAGwQ,aACbzQ,MAAMC,GAAGwQ,WAAWtQ,UAAUC,QAAQmI,UACtCzI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwQ,WAAWtQ,UAAUC,QAAQmI,WACnDC,QACEK,GAAM,aACNC,IAAO,gBACPH,WAAc,YACdF,SAAY,UACZC,eAAkB,aAClBE,SAAY,eAEd3E,QACE4E,GAAM,aACNC,IAAO,gBACPG,IAAO,2BACPD,GAAM,iBACNE,IAAO,0BACPH,GAAM,iBAERjI,MACE+H,GAAM,aACNC,IAAO,gBACPG,IAAO,sBACPD,GAAM,SACNE,IAAO,uBACPH,GAAM,WAERyH,OACE3H,GAAM,aACNC,IAAO,oBAOP9I,MAAMC,GAAGyQ,aACb1Q,MAAMC,GAAGyQ,WAAWvQ,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyQ,WAAWvQ,UAAUC,QAAQC,UACnDwH,cAAiB,qBACjBC,eAAkB,qBAClBuC,OAAU,YACVsG,OAAU,SACVC,QAAW,SACX1I,MAAS,WACT3G,OAAU,SACVsP,KAAQ,SACRC,SAAY,wBACZC,KAAQ,UACRC,OAAU,aAMRhR,MAAMC,GAAGgR,mBACbjR,MAAMC,GAAGgR,iBAAiB9Q,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgR,iBAAiB9Q,UAAUC,QAAQC,UACzD6Q,OACEC,SAAY,YACZC,YAAe,oBAEjBvK,KACEwK,MAAS,QACTC,WAAc,iBACdC,MAAS,QACTC,MAAS,SACTC,GAAM,MACNC,YAAe,QAEjBC,aACET,MAAS,YACTU,QAAW,UACXJ,MAAS,SACTK,OAAU,WACVC,OAAU,WAEZF,SACE1C,IAAO,OACPiC,SAAY,YACZC,YAAe,mBACfW,SAAY,iBAEdC,iBACEC,MAAS,WACTC,OAAU,eACVC,KAAQ,eACRC,OAAU,WACVC,MAAS,aAEXR,QACET,YAAe,gBACfW,SAAY,mBACZZ,SAAY,eAEdW,QACEQ,GAAM,OACNlB,YAAe,mBACfW,SAAY,gBACZZ,SAAY,eAEdoB,UACErD,IAAO,MACPsD,QAAW,SACXC,QAAW,kBAOXzS,MAAMC,GAAGyS,OACb1S,MAAMC,GAAGyS,KAAKvS,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyS,KAAKvS,UAAUC,QAAQC,UAC7CsS,UACEpR,OAAU,mBACVqR,WAAc,QACdC,OAAU,SACVrE,QAAW,YACXsE,KAAQ,cACRC,MAAS,oBACT1E,IAAO,kBACPtL,KAAQ,mBACRzB,OAAU,SACV0R,OAAU,UAEZC,UACEC,aAAgB,SAChBC,aAAgB,gDAChBC,cAAiB,UAEnBC,UAAa,uBACbC,YAAe,yCACfC,cAAiB,+CAMfvT,MAAMC,GAAGuT,UACbxT,MAAMC,GAAGuT,QAAQrT,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuT,QAAQrT,UAAUC,QAAQC,UAChDoT,OACEjS,OAAU,WACVkS,OAAU,kBACVC,SAAY,kBACZC,WAAc,gBACdC,aAAgB,eAChBC,cAAiB,mBACjBC,gBAAmB,sBAOnB/T,MAAMC,GAAG+T,WACbhU,MAAMC,GAAG+T,SAAS7T,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+T,SAAS7T,UAAUC,QAAQC,UAC/C4T,OAAU,uBACVhT,QAAW,eACXC,cAAiB,oBACjBC,MAAS,gBACTwR,UACIG,KAAQ,cACRE,OAAU,SACVJ,WAAc,QACdC,OAAU,SACVqB,YAAe,kBACf1F,QAAW,YACXuE,MAAS,oBACT1E,IAAO,sBAOXrO,MAAMC,GAAGkU,QACbnU,MAAMC,GAAGkU,MAAMhU,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkU,MAAMhU,UAAUC,QAAQC,UAC9C+T,SAAY,SACZC,KAAQ,WACRC,QAAW,0BACXhC,GAAM,SACNiC,MAAS,4BACTC,QAAW,UACXvC,MAAS,uBACTwC,aAAgB,qBAChBtC,KAAQ,0BACRuC,KAAQ,0BACRC,SAAY,0BACZC,UAAa,kBAMX5U,MAAMC,GAAG4U,gBACT7U,MAAMC,GAAG4U,cAAc1U,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4U,cAAc1U,UAAUC,QAAQC,UACtD+T,SAAY,SACZC,KAAQ,WACRC,QAAW,0BACXhC,GAAM,SACNiC,MAAS,4BACTC,QAAW,UACXvC,MAAS,uBACTwC,aAAgB,qBAChBtC,KAAQ,0BACRuC,KAAQ,0BACRC,SAAY,0BACZC,UAAa,kBAMf5U,MAAMC,GAAGsQ,aACbvQ,MAAMC,GAAGsQ,WAAWpQ,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsQ,WAAWpQ,UAAUC,QAAQC,UACnDgK,OAAU,YACVhC,IAAO,IACPH,MAAS,WACT4M,KAAQ,oCACR5R,MAAS,oCACT6R,YAAe,oBACfC,QAAW,aACXC,OAAU,UACV3M,GAAM,MACN/G,OAAU,SACV2T,SAAY,WACZC,mBAAsB,wBACtBxO,MAAS,WACTyO,gBAAmB,wBACnBC,MAAS,SACTxE,KAAQ,SACRyE,KAAQ,OAMNtV,MAAMC,GAAGwQ,aACbzQ,MAAMC,GAAGwQ,WAAWtQ,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwQ,WAAWtQ,UAAUC,QAAQC,UACnDgK,OAAU,YACVnC,MAAS,WACT8M,QAAW,aACXC,OAAU,UACVC,SAAY,cAMVlV,MAAMC,GAAGsV,mBACbvV,MAAMC,GAAGsV,iBAAiBpV,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsV,iBAAiBpV,UAAUC,QAAQC,UACzDmV,SAAY,gBACZC,SAAY,kBACZvN,MAAS,WACTmC,OAAU,YACVpC,OAAU,QACV1G,OAAU,SACVmU,oBAAuB,yBACvB7E,KAAQ,SACRyE,KAAQ,OAMNtV,MAAMC,GAAG0V,YACb3V,MAAMC,GAAG0V,UAAUxV,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0V,UAAUxV,UAAUC,QAAQC,UAClDkU,MAAS,iDAMPvU,MAAMC,GAAG2V,SACb5V,MAAMC,GAAG2V,OAAOzV,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2V,OAAOzV,UAAUC,QAAQC,UAC/CyJ,KAAQ,UACR+L,WAAc,oBACdC,SAAY,QACZC,gBAAmB,mBACnBxL,SAAY,mBACZyL,gBAAmB,oBACnBC,YAAe,gBACfC,OAAU,gBACVC,WAAc,cACdC,YAAe,iBACfC,kBAAqB,0BACrBC,oBAAuB,gBACvBjL,OAAU,SACV5G,cAAiB,YACjBE,YAAe,WACf4R,YAAe,kBACf7R,aAAgB,mBAChB8R,QAAW,kBACXC,cAAiB,WACjBC,MAAS,UACTC,UAAa,eACbC,YAAe,eACfpK,UAAa,YACbqK,OAAU,qBACVhH,WAAc,8CACdF,kBAAqB,6CACrBC,YAAe,eACfE,gBAAmB,0EACnBN,QAAW,cACXC,YAAe,MACfC,YAAe,WACfK,cAAiB,8EACjBR,WAAc,YACduH,UAAa,eACbC,UAAa,OACbC,sBAAyB,KACzBC,aAAgB,SAChBC,aAAgB,SAChBC,SAAY,KACZC,aAAgB,iBAChBC,gBAAmB,cACnBC,oBAAuB,0BACvBC,SAAY,OACZC,YAAe,UACfC,eAAkB,cAClBxP,OAAU,QACVyP,YAAe,iBACfhW,cAAiB,mDACjB6H,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,gBACfC,YAAe,gBACfQ,aAAgB,gBAChBC,UAAa,aACbwN,OAAU,UACVC,WAAc,SACdC,SAAY,aACZC,aAAgB,SAChBC,WAAc,cACdC,YAAe,sBACfC,SAAY,UACZC,QAAW,SACXC,iBAAoB,aACpBC,QAAW,SACXC,QAAW,YACXC,MAAS,SACTC,OAAU,WACVC,YAAe,4BACfC,YAAe,wBACfC,WAAc,yBACd/O,UAAa,eACbgP,WAAc,MACdC,SAAY,WACZC,GAAM,KACNC,OAAU,QACVC,YAAe,kBACfC,gBAAmB,qBACnBC,SAAY,qBACZC,0BAA6B,8CAC7BC,UAAa,gBACbC,YAAe,kBACfC,WAAc,iBACdC,aAAgB,uBAChBC,eAAkB,yBAClBC,cAAiB,wBACjBC,gBAAmB,wBACnBC,kBAAqB,0BACrBC,iBAAoB,yBACpBC,gBAAmB,uBACnBC,kBAAqB,yBACrBC,iBAAoB,wBACpBC,YAAe,yBACfnJ,QAAW,SACXoJ,KAAQ,SACRC,eAAkB,0BAMhBja,MAAMC,GAAGmB,SACbpB,MAAMC,GAAGmB,OAAOjB,UAAUC,QAAQiB,aAClCvB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmB,OAAOjB,UAAUC,QAAQiB,cAC/CE,OAAU,OACV2Y,mBAAsB,oBACtBxY,cAAiB,mDACjByY,mBAAsB,oCACtBC,mBAAsB,oCACtBC,qBAAwB,+BACxB7Y,OAAU,WACVL,MAAS,gBACTG,OAAU,YACVQ,aAAgB,SAChBF,eAAkB,QAClBD,gBAAmB,WACnBF,oBAAuB,iBACvBO,qBAAwB,SACxBD,sBAAyB,gBAMvB/B,MAAMC,GAAGqa,YACbta,MAAMC,GAAGqa,UAAUna,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqa,UAAUna,UAAUC,QAAQC,UAClDka,OAAU,UACVhZ,OAAU,SACVT,KAAQ,OACR0N,QAAW,SACXyE,UACEE,aAAgB,sDAElB1E,QACE+L,YAAe,qBACfC,YAAe,WACf9L,YAAe,UACf9H,IAAO,OACP6T,YAAe,qBACfC,OAAU,aACVC,kBAAqB,kDACrBhU,MAAS,SACTiU,cAAiB,sBACjBC,SAAY,IACZC,qBAAwB,cACxBC,oBAAuB,cACvB9X,MAAS,WACT+X,WAAc,mBAEhBC,MAAS,UACTC,oBACEC,gBAAmB,sEACnBC,uBAA0B,0BAC1BC,mBAAsB,iBACtBC,kBAAqB,oCACrBC,cAAiB,uEACjBC,qBAAwB,2BACxBC,iBAAoB,kBACpBC,gBAAmB,oCAErB5Y,KAAQ,SACR8H,KAAQ,QACR+Q,MAAS,OACT3M,OACE4M,OAAU,aACV3M,IAAO,MACPC,MAAS,QACTC,KAAQ,UACR0M,SAAY,mBAEdP,kBAAqB,iBACrBQ,YAAe,iBACfC,YAAe,2BAMbhc,MAAMC,GAAGgc,SACbjc,MAAMC,GAAGgc,OAAO9b,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgc,OAAO9b,UAAUC,QAAQiB,cAC/C6a,MAAS,aAMPlc,MAAMC,GAAGkc,QACbnc,MAAMC,GAAGkc,MAAMhc,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkc,MAAMhc,UAAUC,QAAQiB,cAC9C2B,OAAU,QAMRhD,MAAMC,GAAGmc,UACbpc,MAAMC,GAAGmc,QAAQjc,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmc,QAAQjc,UAAUC,QAAQiB,cAChD2B,OAAU,KACVzB,OAAU,WAMRvB,MAAMC,GAAGoc,SACbrc,MAAMC,GAAGoc,OAAOlc,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoc,OAAOlc,UAAUC,QAAQiB,cAC/C2B,OAAU,KACVzB,OAAU,WAKRvB,MAAMC,GAAGqc,WACbtc,MAAMC,GAAGqc,SAASjc,SAClBP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqc,SAASjc,UAC7BY,QAAS,kBAKTjB,MAAMC,GAAGsc,YACXvc,MAAMC,GAAGsc,UAAUpc,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsc,UAAUpc,UAAUC,QAAQC,UAClDgP,KAAQ,SACRF,MAAS,QACTD,IAAO,MACPsD,QAAW,mBACXgK,KAAQ,OACRC,OAAU,SACVrK,OAAU,UACVsK,UAAa,YAIhBC,OAAO3c,MAAM4c", "file": "kendo.messages.bg-BG.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Validator */\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} е задължително поле\",\n  \"pattern\": \"{0} не е валидно\",\n  \"min\": \"{0} трябва да бъде по-голямо или равно на {1}\",\n  \"max\": \"{0} трябва да бъде по-малко или равно на {1}\",\n  \"step\": \"{0} не е валидно\",\n  \"email\": \"{0} не е валиден email\",\n  \"url\": \"{0} не е валиден URL\",\n  \"date\": \"{0} не е валидна дата\",\n  \"dateCompare\": \"Крайната дата трябва да бъде по-голяма от началната дата\"\n});\n}\n\n/* TreeView */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Зареждане...\",\n  \"requestFailed\": \"Грешка при заявка\",\n  \"retry\": \"Опитай отново\"\n});\n}\n\n/* Upload */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Избери файлове...\",\n  \"cancel\": \"Отказ\",\n  \"retry\": \"Опитай отново\",\n  \"remove\": \"Премахни\",\n  \"uploadSelectedFiles\": \"Качи файловете\",\n  \"dropFilesHere\": \"drop files here to upload\",\n  \"statusUploading\": \"uploading\",\n  \"statusUploaded\": \"uploaded\",\n  \"statusWarning\": \"warning\",\n  \"statusFailed\": \"failed\",\n  \"headerStatusUploading\": \"Uploading...\",\n  \"headerStatusUploaded\": \"Приключено\"\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"Всички граници\",\n  \"insideBorders\": \"Вътрешни граници\",\n  \"insideHorizontalBorders\": \"Вътрешни хоризонтални граници\",\n  \"insideVerticalBorders\": \"Вътрешни вертикални граници\",\n  \"outsideBorders\": \"Външни граници\",\n  \"leftBorder\": \"Лява граница\",\n  \"topBorder\": \"Горна граница\",\n  \"rightBorder\": \"Дясна граница\",\n  \"bottomBorder\": \"Долна граница\",\n  \"noBorders\": \"Без граница\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Приложи\",\n  \"save\": \"Запази\",\n  \"cancel\": \"Откажи\",\n  \"remove\": \"Премахни\",\n  \"okText\": \"OK\",\n  \"exportAsDialog\": {\n    \"title\": \"Експортирай...\",\n    \"labels\": {\n      \"fileName\": \"Име на файл\",\n      \"saveAsType\": \"Запази като\",\n      \"exportArea\": \"Експортирай\",\n      \"paperSize\": \"Размер на лист\",\n      \"margins\": \"Отстояние\",\n      \"orientation\": \"Ориентация\",\n      \"print\": \"Отпечатай\",\n      \"guidelines\": \"Guidelines\",\n      \"center\": \"Центрирай\",\n      \"horizontally\": \"Хоризонтално\",\n      \"vertically\": \"Вертикално\"\n    }\n  },\n  \"formatCellsDialog\": {\n    \"title\": \"Форматиране\",\n    \"categories\": {\n      \"number\": \"Число\",\n      \"currency\": \"Валута\",\n      \"date\": \"Дата\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Шрифт\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Размер на шрифта\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Граници\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Подравняване\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Подравняване отляво\",\n     \"justifyCenter\": \"Центриране\",\n     \"justifyRight\": \"Подравняване отдясно\",\n     \"justifyFull\": \"Двустранно подравняване\",\n     \"alignTop\": \"Горно подравняване\",\n     \"alignMiddle\": \"Подравняване в средата\",\n     \"alignBottom\": \"Долно подравняване\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Обединяване на клетки\",\n    \"buttons\": {\n      \"mergeCells\": \"Обедини всичко\",\n      \"mergeHorizontally\": \"Обедини по хоризонтала\",\n      \"mergeVertically\": \"Обедини по вертикала\",\n      \"unmerge\": \"Раздели на клетки\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Фиксирай прозорците\",\n    \"buttons\": {\n      \"freezePanes\": \"Фиксирай прозорците\",\n      \"freezeRows\": \"Фиксирай редове\",\n      \"freezeColumns\": \"Фиксирай колони\",\n      \"unfreeze\": \"Освободи прозорците\"\n    }\n  },\n  \"validationDialog\": {\n    \"title\": \"Проверка на данни\",\n    \"hintMessage\": \"Въведете валидна {0} стойност {1}.\",\n    \"hintTitle\": \"Проверка {0}\",\n    \"criteria\": {\n      \"any\": \"Всяка стойност\",\n      \"number\": \"Число\",\n      \"text\": \"Текст\",\n      \"date\": \"Дата\",\n      \"custom\": \"По избор\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"по-голямо от\",\n      \"lessThan\": \"по-малко от\",\n      \"between\": \"между\",\n      \"notBetween\": \"не е между\",\n      \"equalTo\": \"равно на\",\n      \"notEqualTo\": \"не е равно на\",\n      \"greaterThanOrEqualTo\": \"по-голямо или равно на\",\n      \"lessThanOrEqualTo\": \"по-малко или равно на\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"по-голямо от {0}\",\n      \"lessThan\": \"по-малко от {0}\",\n      \"between\": \"между {0} и {1}\",\n      \"notBetween\": \"не е между {0} и {1}\",\n      \"equalTo\": \"равно на {0}\",\n      \"notEqualTo\": \"не е равно на {0}\",\n      \"greaterThanOrEqualTo\": \"по-голямо или равно на {0}\",\n      \"lessThanOrEqualTo\": \"по-малко или равно на {0}\",\n      \"custom\": \"което удовлетворява формулата: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Критерии\",\n      \"comparer\": \"Comparer\",\n      \"min\": \"Мин\",\n      \"max\": \"Макс\",\n      \"value\": \"Стойност\",\n      \"start\": \"Начало\",\n      \"end\": \"Край\",\n      \"onInvalidData\": \"При невалидни данни\",\n      \"rejectInput\": \"Откажи въвеждане\",\n      \"showWarning\": \"Покажи предупреждение\",\n      \"showHint\": \"Покажи входно съобщение\",\n      \"hintTitle\": \"Заглавие\",\n      \"hintMessage\": \"Съобщение\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Напишете заглавие\",\n      \"typeMessage\": \"Напишете съобщение\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Не може да се промени част от обединена клетка.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Копиране и поставяне\",\n    \"errorMessage\": \"Тези команди не могат да бъдат избрани от менюто. Вместо това използвайте клавишна комбинация:\",\n    \"labels\": {\n      \"forCopy\": \"за копиране\",\n      \"forCut\": \"за изрязване\",\n      \"forPaste\": \"за поставяне\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"Командата, която сте избрали, не може да бъде използвана с няколко селекции.\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Сортирай възходящо\",\n  \"sortDescending\": \"Сортирай низходящо\",\n  \"filterByValue\": \"Филтрирай по стойност\",\n  \"filterByCondition\": \"Филтрирай по критерий\",\n  \"apply\": \"Приложи\",\n  \"search\": \"Търси\",\n  \"clear\": \"Премахни\",\n  \"blanks\": \"(Празни места)\",\n  \"operatorNone\": \"Без критерии\",\n  \"and\": \"И\",\n  \"or\": \"ИЛИ\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"Съдържа\",\n      \"doesnotcontain\": \"Не съдържа\",\n      \"startswith\": \"Започва с\",\n      \"endswith\": \"Завършва на\"\n    },\n    \"date\": {\n      \"eq\": \"Е равно на\",\n      \"neq\": \"Не е равно на\",\n      \"lt\": \"Е преди\",\n      \"gt\": \"Е след\"\n    },\n    \"number\": {\n      \"eq\": \"Е равно на\",\n      \"neq\": \"Не е равно на\",\n      \"gte\": \"Е по-голяма или равно на\",\n      \"gt\": \"Е по-голямо от\",\n      \"lte\": \"Е по-малко или равно на\",\n      \"lt\": \"Е по-малко от\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.colorPicker) {\nkendo.spreadsheet.messages.colorPicker =\n$.extend(true, kendo.spreadsheet.messages.colorPicker,{\n  \"reset\": \"Махни цвета\",\n  \"customColor\": \"Избери цвят...\",\n  \"apply\": \"Приложи\",\n  \"cancel\": \"Откажи\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Добави колона отляво\",\n  \"addColumnRight\": \"Добави колона отдясно\",\n  \"addRowAbove\": \"Добави ред отгоре\",\n  \"addRowBelow\": \"Добави ред отдолу\",\n  \"alignment\": \"Подравняване\",\n  \"alignmentButtons\": {\n    \"justtifyLeft\": \"Подравняване отляво\",\n    \"justifyCenter\": \"Центрирано\",\n    \"justifyRight\": \"Подравняване отдясно\",\n    \"justifyFull\": \"Двустранно подравняване\",\n    \"alignTop\": \"Горно подравняване\",\n    \"alignMiddle\": \"Подравняване в средата\",\n    \"alignBottom\": \"Подравняване в дъното\"\n  },\n  \"backgroundColor\": \"Фон\",\n  \"bold\": \"Удебеляване\",\n  \"borders\": \"Граници\",\n  \"copy\": \"Копирай\",\n  \"cut\": \"Изрежи\",\n  \"deleteColumn\": \"Изтрий колона\",\n  \"deleteRow\": \"Изтрии ред\",\n  \"excelImport\": \"Импортиране от Excel...\",\n  \"filter\": \"Филтриране\",\n  \"fontFamily\": \"Шрифт\",\n  \"fontSize\": \"Размер на шрифт\",\n  \"format\": \"Формат по избор...\",\n  \"formatTypes\": {\n    \"automatic\": \"Автоматичен\",\n    \"number\": \"Число\",\n    \"percent\": \"Процент\",\n    \"financial\": \"Финансов\",\n    \"currency\": \"Валута\",\n    \"date\": \"Дата\",\n    \"time\": \"Час\",\n    \"dateTime\": \"Дата и час\",\n    \"duration\": \"Период\",\n    \"moreFormats\": \"Още формати...\"\n  },\n  \"formatDecreaseDecimal\": \"Намали порядъка\",\n  \"formatIncreaseDecimal\": \"Увеличи порядъка\",\n  \"freeze\": \"Фиксирай прозорците\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Фиксирай прозорците\",\n    \"freezeRows\": \"Фиксирай редове\",\n    \"freezeColumns\": \"фиксирай колони\",\n    \"unfreeze\": \"Освободи прозорците\"\n  },\n  \"italic\": \"Курсив\",\n  \"merge\": \"Обединяване на клетки\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Обединяване на всичко\",\n    \"mergeHorizontally\": \"Обединяване по редове\",\n    \"mergeVertically\": \"Обединяване по колони\",\n    \"unmerge\": \"Разделяне на клетки\"\n  },\n  \"open\": \"Отвори...\",\n  \"paste\": \"Поставяне\",\n  \"quickAccess\": {\n    \"redo\": \"Стъпка напред\",\n    \"undo\": \"Стъпка назад\"\n  },\n  \"saveAs\": \"Запиши като...\",\n  \"sort\": \"Сортирай\",\n  \"sortAsc\": \"Сортирай възходящо\",\n  \"sortDesc\": \"Сортирай низходящо\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Сортирай лист от A до Z\",\n    \"sortSheetDesc\": \"Сортирай лист от Z до A\",\n    \"sortRangeAsc\": \"Сортирай селекция от A до Z\",\n    \"sortRangeDesc\": \"Сортирай селекция от Z до A\"\n  },\n  \"textColor\": \"Цвят на текста\",\n  \"textWrap\": \"Пренасяне на текста\",\n  \"underline\": \"Подчертано\",\n  \"validation\": \"Проверка на данни...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"nameBox\": \"Име на клетка\",\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Не може да се вмъкнат клетки поради възможност от загуба на данни. Изберете друга локация за вмъкване на клетки или изтрийте данни от края на работния лист.\"\n  },\n  \"tabs\": {\n    \"home\": \"Начало\",\n    \"insert\": \"Вмъкване\",\n    \"data\": \"Данни\"\n  }\n});\n}\n\n/* Slider */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Увеличи\",\n  \"decreaseButtonTitle\": \"Намали\"\n});\n}\n\n/* Numeric text box */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Увеличи стойността\",\n  \"downArrowText\": \"Намали стойността\"\n});\n}\n\n/* MediaPlayer messages */\n\nif (kendo.ui.MediaPlayer) {\nkendo.ui.MediaPlayer.prototype.options.messages =\n$.extend(true, kendo.ui.MediaPlayer.prototype.options.messages,{\n  \"pause\": \"Пауза\",\n  \"play\": \"Възпроизвеждане\",\n  \"mute\": \"Заглушаване на звука\",\n  \"unmute\": \"Включване на звука\",\n  \"quality\": \"Промяна на качеството\",\n  \"fullscreen\": \"Цял екран\"\n});\n}\n\n/* Gantt */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Добави дете\",\n    \"append\": \"Добави задача\",\n    \"insertAfter\": \"Добави отдолу\",\n    \"insertBefore\": \"Добави отгоре\",\n    \"pdf\": \"Експорт към PDF\"\n  },\n  \"cancel\": \"Откажи\",\n  \"deleteDependencyWindowTitle\": \"Изтрий връзка\",\n  \"deleteTaskWindowTitle\": \"Изтрий задача\",\n  \"destroy\": \"Изтрий\",\n  \"editor\": {\n    \"assingButton\": \"Задай\",\n    \"editorTitle\": \"Задача\",\n    \"end\": \"Край\",\n    \"percentComplete\": \"Завършено\",\n    \"resources\": \"Ресурси\",\n    \"resourcesEditorTitle\": \"Ресурси\",\n    \"resourcesHeader\": \"Ресурси\",\n    \"start\": \"Начало\",\n    \"title\": \"Заглавие\",\n    \"unitsHeader\": \"Единици\"\n  },\n  \"save\": \"Запази\",\n  \"views\": {\n    \"day\": \"Ден\",\n    \"end\": \"Край\",\n    \"month\": \"Месец\",\n    \"start\": \"Начало\",\n    \"week\": \"Седмица\",\n    \"year\": \"Година\"\n  }\n});\n}\n\n/* File browser */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Качи файл\",\n  \"orderBy\": \"Подреди по\",\n  \"orderByName\": \"Име\",\n  \"orderBySize\": \"Размер\",\n  \"directoryNotFound\": \"Папка с това име не беше намерена\",\n  \"emptyFolder\": \"Празна папка\",\n  \"deleteFile\": 'Сигурни ли сте че искате да изтриете \"{0}\"?',\n  \"invalidFileType\": \"Избраният файл \\\"{0}\\\" не е валиден. Поддържаните файлови формати са {1}.\",\n  \"overwriteFile\": \"Файл с име \\\"{0}\\\" вече съществува в текущата папка. Искате ли да го презапишете?\",\n  \"dropFilesHere\": \"Пуснете файл тук за да го качите\",\n  \"search\": \"Търси\"\n});\n}\n\n\n/* Flat color picker */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Приложи\",\n  \"cancel\": \"Откажи\",\n  \"noColor\": \"без цвят\",\n  \"clearColor\": \"Махни цвета\"\n});\n}\n\n/* Color picker */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"Приложи\",\n  \"cancel\": \"Откажи\",\n  \"noColor\": \"без цвят\",\n  \"clearColor\": \"Махни цвета\"\n});\n}\n\n/* DateRangePicker messages */\n\nif (kendo.ui.DateRangePicker) {\nkendo.ui.DateRangePicker.prototype.options.messages =\n$.extend(true, kendo.ui.DateRangePicker.prototype.options.messages,{\n  \"startLabel\": \"Начало\",\n  \"endLabel\": \"Край\"\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Е равно на\",\n    \"neq\": \"Не е равно на\",\n    \"startswith\": \"Започва с\",\n    \"contains\": \"Съдържа\",\n    \"doesnotcontain\": \"Не съдържа\",\n    \"endswith\": \"Завършва на\"\n  },\n  \"number\": {\n    \"eq\": \"Е равно на\",\n    \"neq\": \"Не е равно на\",\n    \"gte\": \"Е по-голяма или равно на\",\n    \"gt\": \"Е по-голямо от\",\n    \"lte\": \"Е по-малко или равно на\",\n    \"lt\": \"Е по-малко от\"\n  },\n  \"date\": {\n    \"eq\": \"Е равно на\",\n    \"neq\": \"Не е равно на\",\n    \"gte\": \"Е след или равно на\",\n    \"gt\": \"Е след\",\n    \"lte\": \"Е преди или равно на\",\n    \"lt\": \"Е преди\"\n  },\n  \"enums\": {\n    \"eq\": \"E равно на\",\n    \"neq\": \"Не е равно на\"\n  }\n});\n}\n\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Е равно на\",\n    \"neq\": \"Не е равно на\",\n    \"startswith\": \"Започва с\",\n    \"contains\": \"Съдържа\",\n    \"doesnotcontain\": \"Не съдържа\",\n    \"endswith\": \"Завършва на\"\n  },\n  \"number\": {\n    \"eq\": \"Е равно на\",\n    \"neq\": \"Не е равно на\",\n    \"gte\": \"Е по-голяма или равно на\",\n    \"gt\": \"Е по-голямо от\",\n    \"lte\": \"Е по-малко или равно на\",\n    \"lt\": \"Е по-малко от\"\n  },\n  \"date\": {\n    \"eq\": \"Е равно на\",\n    \"neq\": \"Не е равно на\",\n    \"gte\": \"Е след или равно на\",\n    \"gt\": \"Е след\",\n    \"lte\": \"Е преди или равно на\",\n    \"lt\": \"Е преди\"\n  },\n  \"enums\": {\n    \"eq\": \"E равно на\",\n    \"neq\": \"Не е равно на\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Сортирай възходящо\",\n  \"sortDescending\": \"Сортирай низходящо\",\n  \"filter\": \"Филтрирай\",\n  \"column\": \"Колона\",\n  \"columns\": \"Колони\",\n  \"clear\": \"Премахни\",\n  \"cancel\": \"Откажи\",\n  \"done\": \"Готово\",\n  \"settings\": \"Настройки на колоната\",\n  \"lock\": \"Заключи\",\n  \"unlock\": \"Отключи\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \" ден(дни)\",\n    \"repeatEvery\": \"Повтаряй всеки: \"\n  },\n  \"end\": {\n    \"after\": \"След \",\n    \"occurrence\": \" повторение(я)\",\n    \"label\": \"Край:\",\n    \"never\": \"Никога\",\n    \"on\": \"На \",\n    \"mobileLabel\": \"Край\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Ежедневно\",\n    \"monthly\": \"Месечно\",\n    \"never\": \"Никога\",\n    \"weekly\": \"Седмично\",\n    \"yearly\": \"Годишно\"\n  },\n  \"monthly\": {\n    \"day\": \"Ден \",\n    \"interval\": \" месец(и)\",\n    \"repeatEvery\": \"Повтаряй всеки: \",\n    \"repeatOn\": \"Повтаряй на: \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"първи(а)\",\n    \"fourth\": \"четвърти(та)\",\n    \"last\": \"последен(на)\",\n    \"second\": \"втори(а)\",\n    \"third\": \"трети(та)\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Повтаряй на: \",\n    \"repeatOn\": \"Повтаряй всеки: \",\n    \"interval\": \" седмица(и)\"\n  },\n  \"yearly\": {\n    \"of\": \" от \",\n    \"repeatEvery\": \"Повтаряй всеки: \",\n    \"repeatOn\": \"Повтаряй на: \",\n    \"interval\": \" година(ни)\"\n  },\n  \"weekdays\": {\n    \"day\": \"ден\",\n    \"weekday\": \"делник\",\n    \"weekend\": \"почивен ден\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Откажи промените\",\n    \"canceledit\": \"Отказ\",\n    \"create\": \"Добави\",\n    \"destroy\": \"Изтриване\",\n    \"edit\": \"Редактиране\",\n    \"excel\": \"Експорт към Excel\",\n    \"pdf\": \"Експорт към PDF\",\n    \"save\": \"Запази промените\",\n    \"select\": \"Избери\",\n    \"update\": \"Обнови\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Откажи\",\n    \"confirmation\": \"Сигурни ли сте, че искате да изтриете записа?\",\n    \"confirmDelete\": \"Изтрий\"\n  },\n  \"noRecords\": \"Няма налични записи.\",\n  \"groupHeader\": \"Натиснете ctrl + space за да групирате\",\n  \"ungroupHeader\": \"Натиснете ctrl + space за да разгрупирате\"\n});\n}\n\n/* ListBox messaages */\n\nif (kendo.ui.ListBox) {\nkendo.ui.ListBox.prototype.options.messages =\n$.extend(true, kendo.ui.ListBox.prototype.options.messages,{\n  \"tools\": {\n    \"remove\": \"Премахни\",\n    \"moveUp\": \"Премести нагоре\",\n    \"moveDown\": \"Премести надолу\",\n    \"transferTo\": \"Прехвърли към\",\n    \"transferFrom\": \"Прехвърли от\",\n    \"transferAllTo\": \"Премести вс. към\",\n    \"transferAllFrom\": \"Премести вс. от\"\n  }\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n    \"noRows\": \"Няма налични записи.\",\n    \"loading\": \"Зареждане...\",\n    \"requestFailed\": \"Грешка при заявка\",\n    \"retry\": \"Опитай отново\",\n    \"commands\": {\n        \"edit\": \"Редактиране\",\n        \"update\": \"Обнови\",\n        \"canceledit\": \"Отказ\",\n        \"create\": \"Добави\",\n        \"createchild\": \"Добави подзапис\",\n        \"destroy\": \"Изтриване\",\n        \"excel\": \"Експорт към Excel\",\n        \"pdf\": \"Експорт към PDF\"\n    }\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Всички\",\n  \"page\": \"Страница\",\n  \"display\": \"{0} - {1} от {2} записи\",\n  \"of\": \"от {0}\",\n  \"empty\": \"Няма записи за показване!\",\n  \"refresh\": \"Опресни\",\n  \"first\": \"Към първата страница\",\n  \"itemsPerPage\": \"записи на страница\",\n  \"last\": \"Към последната страница\",\n  \"next\": \"Към следващата страница\",\n  \"previous\": \"Към предишната страница\",\n  \"morePages\": \"Още страници\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\n    kendo.ui.TreeListPager.prototype.options.messages =\n    $.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n      \"allPages\": \"Всички\",\n      \"page\": \"Страница\",\n      \"display\": \"{0} - {1} от {2} записи\",\n      \"of\": \"от {0}\",\n      \"empty\": \"Няма записи за показване!\",\n      \"refresh\": \"Опресни\",\n      \"first\": \"Към първата страница\",\n      \"itemsPerPage\": \"записи на страница\",\n      \"last\": \"Към последната страница\",\n      \"next\": \"Към следващата страница\",\n      \"previous\": \"Към предишната страница\",\n      \"morePages\": \"Още страници\"\n    });\n    }\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"filter\": \"Филтрирай\",\n  \"and\": \"и\",\n  \"clear\": \"Премахни\",\n  \"info\": \"Покажи записи със стойност, която\",\n  \"title\": \"Покажи записи със стойност, която\",\n  \"selectValue\": \"-Избери стойност-\",\n  \"isFalse\": \"не е вярно\",\n  \"isTrue\": \"е вярно\",\n  \"or\": \"или\",\n  \"cancel\": \"Откажи\",\n  \"operator\": \"Оператор\",\n  \"additionalOperator\": \"Допълнителен оператор\",\n  \"value\": \"Стойност\",\n  \"additionalValue\": \"Допълнителна стойност\",\n  \"logic\": \"Логика\",\n  \"done\": \"Готово\",\n  \"into\": \"в\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"filter\": \"Филтрирай\",\n  \"clear\": \"Премахни\",\n  \"isFalse\": \"не е вярно\",\n  \"isTrue\": \"е вярно\",\n  \"operator\": \"Оператор\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Избери всички\",\n  \"clearAll\": \"Премахни всички\",\n  \"clear\": \"Премахни\",\n  \"filter\": \"Филтрирай\",\n  \"search\": \"Търси\",\n  \"cancel\": \"Откажи\",\n  \"selectedItemsFormat\": \"{0} селектирани записи\",\n  \"done\": \"Готово\",\n  \"into\": \"в\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Дръпни колона и я пусни тук, за да групираш\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Получер\",\n  \"createLink\": \"Направи препратка\",\n  \"fontName\": \"Шрифт\",\n  \"fontNameInherit\": \"(наследен шрифт)\",\n  \"fontSize\": \"Размер на шрифта\",\n  \"fontSizeInherit\": \"(наследен размер)\",\n  \"formatBlock\": \"Избери формат\",\n  \"indent\": \"Добави отстъп\",\n  \"insertHtml\": \"Вмъкни HTML\",\n  \"insertImage\": \"Вмъкни картина\",\n  \"insertOrderedList\": \"Вмъкни номериран списък\",\n  \"insertUnorderedList\": \"Вмъкни списък\",\n  \"italic\": \"Курсив\",\n  \"justifyCenter\": \"Центрирай\",\n  \"justifyFull\": \"Подравни\",\n  \"justifyLeft\": \"Подравни отляво\",\n  \"justifyRight\": \"Подравни отдясно\",\n  \"outdent\": \"Премахни отстъп\",\n  \"strikethrough\": \"Зачертай\",\n  \"style\": \"Стилове\",\n  \"subscript\": \"Долен индекс\",\n  \"superscript\": \"Горен индекс\",\n  \"underline\": \"Подчертай\",\n  \"unlink\": \"Премахни препратка\",\n  \"deleteFile\": \"Сигурни ли сте че искате да изтриете \\\"{0}\\\"?\",\n  \"directoryNotFound\": \"Директория с посоченото име не бе открита.\",\n  \"emptyFolder\": \"Празна папка\",\n  \"invalidFileType\": \"Избраният файл \\\"{0}\\\" не е валиден. Поддържаните файлови формати са {1}.\",\n  \"orderBy\": \"Подреди по:\",\n  \"orderByName\": \"Име\",\n  \"orderBySize\": \"Големина\",\n  \"overwriteFile\": \"Файл с име \\\"{0}\\\" вече съществува в тази папка. Искате ли да го презапишете?\",\n  \"uploadFile\": \"Качи файл\",\n  \"backColor\": \"Цвят на фона\",\n  \"foreColor\": \"Цвят\",\n  \"dialogButtonSeparator\": \"or\",\n  \"dialogCancel\": \"Cancel\",\n  \"dialogInsert\": \"Insert\",\n  \"dialogOk\": \"Ok\",\n  \"imageAltText\": \"Alternate text\",\n  \"imageWebAddress\": \"Web address\",\n  \"linkOpenInNewWindow\": \"Open link in new window\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkWebAddress\": \"Web address\",\n  \"search\": \"Търси\",\n  \"createTable\": \"Създай таблица\",\n  \"dropFilesHere\": \"преместете с мишката файлове тук за да ги качите\",\n  \"addColumnLeft\": \"Add column on the left\",\n  \"addColumnRight\": \"Add column on the right\",\n  \"addRowAbove\": \"Add row above\",\n  \"addRowBelow\": \"Add row below\",\n  \"deleteColumn\": \"Delete column\",\n  \"deleteRow\": \"Delete row\",\n  \"styles\": \"Стилове\",\n  \"formatting\": \"Format\",\n  \"viewHtml\": \"Виж HTML-а\",\n  \"dialogUpdate\": \"Обнови\",\n  \"insertFile\": \"Вмъкни файл\",\n  \"tableWizard\": \"Асистент за Таблици\",\n  \"tableTab\": \"Таблица\",\n  \"cellTab\": \"Клетка\",\n  \"accessibilityTab\": \"Достъпност\",\n  \"caption\": \"Надпис\",\n  \"summary\": \"Обобщение\",\n  \"width\": \"Ширина\",\n  \"height\": \"Височина\",\n  \"cellSpacing\": \"Разстояние между клетките\",\n  \"cellPadding\": \"Отстояние на клетките\",\n  \"cellMargin\": \"Разстояние от клетките\",\n  \"alignment\": \"Подравняване\",\n  \"background\": \"Фон\",\n  \"cssClass\": \"CSS клас\",\n  \"id\": \"ID\",\n  \"border\": \"Рамка\",\n  \"borderStyle\": \"Стил на рамката\",\n  \"collapseBorders\": \"Свиване на рамката\",\n  \"wrapText\": \"Събиране на текста\",\n  \"associateCellsWithHeaders\": \"Асоциирай клетките с заглавията на колоните\",\n  \"alignLeft\": \"Подравни ляво\",\n  \"alignCenter\": \"Подравни център\",\n  \"alignRight\": \"Подравни дясно\",\n  \"alignLeftTop\": \"Подравни ляво и горе\",\n  \"alignCenterTop\": \"Подравни център и горе\",\n  \"alignRightTop\": \"Подравни дясно и горе\",\n  \"alignLeftMiddle\": \"Подравни ляво и среда\",\n  \"alignCenterMiddle\": \"Подравни център и среда\",\n  \"alignRightMiddle\": \"Подравни дясно и среда\",\n  \"alignLeftBottom\": \"Подравни ляво и долу\",\n  \"alignCenterBottom\": \"Подравни център и горе\",\n  \"alignRightBottom\": \"Подравни дясно и долу\",\n  \"alignRemove\": \"RПремахни подравняване\",\n  \"columns\": \"Колони\",\n  \"rows\": \"Редове\",\n  \"selectAllCells\": \"Избери всички клетки\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Спри\",\n  \"clearSelectedFiles\": \"Изчисти файловете\",\n  \"dropFilesHere\": \"Преместете с мишката файлове тук за да ги качите\",\n  \"invalidMaxFileSize\": \"Размерът на файла е твърде голям.\",\n  \"invalidMinFileSize\": \"Размерът на файла е твърде малък.\",\n  \"invalidFileExtension\": \"Този тип файл не е разрешен.\",\n  \"remove\": \"Премахни\",\n  \"retry\": \"Опитай отново\",\n  \"select\": \"Избери...\",\n  \"statusFailed\": \"грешка\",\n  \"statusUploaded\": \"качен\",\n  \"statusUploading\": \"качва се\",\n  \"uploadSelectedFiles\": \"Качи файловете\",\n  \"headerStatusUploaded\": \"Готово\",\n  \"headerStatusUploading\": \"Качване...\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"цял ден\",\n  \"cancel\": \"Откажи\",\n  \"date\": \"Дата\",\n  \"destroy\": \"Изтрий\",\n  \"editable\": {\n    \"confirmation\": \"Сигурен ли сте че искате да изтриете това събитие?\"\n  },\n  \"editor\": {\n    \"allDayEvent\": \"Целодневно събитие\",\n    \"description\": \"Описание\",\n    \"editorTitle\": \"Събитие\",\n    \"end\": \"Край\",\n    \"endTimezone\": \"Крайна часова зона\",\n    \"repeat\": \"Повторение\",\n    \"separateTimezones\": \"Използвай различни часови зони за начало и край\",\n    \"start\": \"Начало\",\n    \"startTimezone\": \"Начална часова зона\",\n    \"timezone\": \" \",\n    \"timezoneEditorButton\": \"Часовa зона\",\n    \"timezoneEditorTitle\": \"Часови зони\",\n    \"title\": \"Заглавие\",\n    \"noTimezone\": \"Без часова зона\"\n  },\n  \"event\": \"Събитие\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Сигурен ли сте че искате да изтриете това събитие или цялата серия?\",\n    \"deleteWindowOccurrence\": \"Изтрий единично събитие\",\n    \"deleteWindowSeries\": \"Изтрий серията\",\n    \"deleteWindowTitle\": \"Изтриване на повтарящо се събитие\",\n    \"editRecurring\": \"Сигурен ли сте че искате да промените това събитие или цялата серия?\",\n    \"editWindowOccurrence\": \"Промени единично събитие\",\n    \"editWindowSeries\": \"Промени серията\",\n    \"editWindowTitle\": \"Промяна на повтарящо се действие\"\n  },\n  \"save\": \"Запази\",\n  \"time\": \"Време\",\n  \"today\": \"Днес\",\n  \"views\": {\n    \"agenda\": \"Дневен ред\",\n    \"day\": \"Ден\",\n    \"month\": \"Месец\",\n    \"week\": \"Седмица\",\n    \"workWeek\": \"Работна седмица\"\n  },\n  \"deleteWindowTitle\": \"Изтрий събитие\",\n  \"showFullDay\": \"Покажи цял ден\",\n  \"showWorkDay\": \"Покажи работни часове\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization,{\n  \"close\": \"Затвори\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization,{\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization,{\n  \"okText\": \"OK\",\n  \"cancel\": \"Отказ\"\n});\n}\n\n/* Prompt */\n\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization,{\n  \"okText\": \"OK\",\n  \"cancel\": \"Отказ\"\n});\n}\n\n/* kendo.ui.progress method */\nif (kendo.ui.progress) {\nkendo.ui.progress.messages =\n$.extend(true, kendo.ui.progress.messages, {\n    loading: \"Зареждане...\"\n});\n}\n\n/* DateInput */\nif (kendo.ui.DateInput) {\n  kendo.ui.DateInput.prototype.options.messages =\n    $.extend(true, kendo.ui.DateInput.prototype.options.messages, {\n      \"year\": \"година\",\n      \"month\": \"месец\",\n      \"day\": \"ден\",\n      \"weekday\": \"ден от седмицата\",\n      \"hour\": \"часа\",\n      \"minute\": \"минути\",\n      \"second\": \"секунди\",\n      \"dayperiod\": \"AM/PM\"\n    });\n}\n\n})(window.kendo.jQuery);\n}));"]}