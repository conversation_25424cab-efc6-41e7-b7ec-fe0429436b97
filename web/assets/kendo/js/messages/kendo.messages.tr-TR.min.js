/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e){"function"==typeof define&&define.amd?define(["kendo.core.min"],e):e()}(function(){!function(e,i){kendo.ui.FilterCell&&(kendo.ui.FilterCell.prototype.options.operators=e.extend(!0,kendo.ui.FilterCell.prototype.options.operators,{date:{eq:"Eşit",gt:"Sonra",gte:"Sonra veya eşit",lt:"Önce",lte:"Önce veya eşit",neq:"Eşit değil",isnull:"Null",isnotnull:"Null değil"},enums:{eq:"Eşit",neq:"Eşit değil",isnull:"Null",isnotnull:"Null değil"},number:{eq:"Eşit",gt:"Büyük",gte:"Büyük veya eşit",lt:"Küçük",lte:"Küçük veya eşit",neq:"Eşit değil",isnull:"Null",isnotnull:"Null değil"},string:{contains:"İçeriyor",doesnotcontain:"İçermiyor",endswith:"İle biter",eq:"Eşit",neq:"Eşit değil",startswith:"İle başlar",isnull:"Null",isnotnull:"Null değil",isempty:"Boş",isnotempty:"Boş değil",isnullorempty:"Değer içermiyor",isnotnullorempty:"Değer içeriyor"}})),kendo.ui.FilterMenu&&(kendo.ui.FilterMenu.prototype.options.operators=e.extend(!0,kendo.ui.FilterMenu.prototype.options.operators,{date:{eq:"Eşit",gt:"Sonra",gte:"Sonra veya eşit",lt:"Önce",lte:"Önce veya eşit",neq:"Eşit değil",isnull:"Null",isnotnull:"Null değil"},enums:{eq:"Eşit",neq:"Eşit değil",isnull:"Null",isnotnull:"Null değil"},number:{eq:"Eşit",gt:"Büyük",gte:"Büyük veya eşit",lt:"Küçük",lte:"Küçük veya eşit",neq:"Eşit değil",isnull:"Null",isnotnull:"Null değil"},string:{contains:"İçeriyor",doesnotcontain:"İçermiyor",endswith:"İle biter",eq:"Eşit",neq:"Eşit değil",startswith:"İle başlar",isnull:"Null",isnotnull:"Null değil",isempty:"Boş",isnotempty:"Boş değil",isnullorempty:"Değer içeriyor",isnotnullorempty:"Değer içermiyor"}})),kendo.ui.ColumnMenu&&(kendo.ui.ColumnMenu.prototype.options.messages=e.extend(!0,kendo.ui.ColumnMenu.prototype.options.messages,{columns:"Sütunlar",settings:"Sütun ayarları",done:"Tamam",lock:"Kilitle",sortAscending:"Artan Sıralama",sortDescending:"Azalan Sıralama",unlock:"Kilidini Aç",filter:"Filtrele"})),kendo.ui.RecurrenceEditor&&(kendo.ui.RecurrenceEditor.prototype.options.messages=e.extend(!0,kendo.ui.RecurrenceEditor.prototype.options.messages,{daily:{interval:"Günler",repeatEvery:"Her gün tekrarla"},end:{after:"Sonra",label:"Bitiş",mobileLabel:"Bitiş",never:"Asla/Hiç",occurrence:"Olay",on:"Anlık"},frequencies:{daily:"Günlük",monthly:"Aylık",never:"Asla/Hiç",weekly:"Haftalık",yearly:"Yıllık"},monthly:{day:"Gün",interval:"Aylar",repeatEvery:"Her ay tekrarla",repeatOn:"Tekrarla"},offsetPositions:{first:"İlk",fourth:"Dördüncü",last:"Son",second:"İkinci",third:"Üçüncü"},weekdays:{day:"Gün",weekday:"İş günü",weekend:"Haftasonu"},weekly:{interval:"Haftalar",repeatEvery:"Her hafta tekrarla",repeatOn:"Tekrarla"},yearly:{interval:"Yıllar",of:"Arasında",repeatEvery:"Her Yıl Tekrarla",repeatOn:"Tekrarla"}})),kendo.ui.Editor&&(kendo.ui.Editor.prototype.options.messages=e.extend(!0,kendo.ui.Editor.prototype.options.messages,{addColumnLeft:"Sola kolon ekle",addColumnRight:"Sağa kolon ekle",addRowAbove:"Yukarıya satır ekle",addRowBelow:"Aşağıya satır ekle",backColor:"Arka plan rengi",bold:"Kalın ",createLink:"Köprü ekleme",createTable:"Tablo oluştur",deleteColumn:"Sütun silme",deleteFile:"Silmek istediğinizden emin misiniz?",deleteRow:"Satır sil",dialogButtonSeparator:"ya da",dialogCancel:"İptal",dialogInsert:"Ekle",dialogUpdate:"Güncelle",directoryNotFound:"Bu isimde bir dizin bulunamadı.",dropFilesHere:"Yüklemek için dosyaları buraya bırakın",emptyFolder:"Boş klasör",fontName:"Font ailesi Seçiniz",fontNameInherit:"Devralınan Karakter",fontSize:"Font boyutu Seçiniz",fontSizeInherit:"Devralınan Boyut",foreColor:"Renk",formatBlock:"Biçim",formatting:"Biçimlendirme",imageAltText:"Alternatif metin",imageWebAddress:"Web adresi",indent:"Satırbaşı",insertHtml:"HTML ekle",insertImage:"Resim ekle",insertOrderedList:"Sıralı liste ekle",insertUnorderedList:"Sırasız liste ekle",invalidFileType:'Seçilen dosya "{0}" geçerli değil. Desteklenen dosya türleri: {1}.',italic:"İtalik karakter",justifyCenter:"Merkezi metin",justifyFull:"Doğrulama",justifyLeft:"Metni sola yasla",justifyRight:"Metni sağa yasla",linkOpenInNewWindow:"Yeni pencerede aç",linkText:"Metin",linkToolTip:"Araç İpucu",linkWebAddress:"Web adresi",orderBy:"Düzenleme ölçütü:",orderByName:"İsim",orderBySize:"Boyut",outdent:"Çıkıntı",overwriteFile:'Dizinde "{0}" isimli bir dosya zaten mevcut. Üzerine yazmak istiyor musunuz?',search:"Arama",strikethrough:"Üstü çizili",styles:"Stiller",subscript:"İndis",superscript:"Üstyazı",underline:"Altını çiz",unlink:"Köprüyü Kaldır",uploadFile:"Yükle",viewHtml:"HTML Görünümü ",insertFile:"Dosya Ekle"})),kendo.ui.FilterCell&&(kendo.ui.FilterCell.prototype.options.messages=e.extend(!0,kendo.ui.FilterCell.prototype.options.messages,{clear:"Temizle",filter:"Filtre",isFalse:"Yanlış",isTrue:"Doğru",operator:"Operatör"})),kendo.ui.FilterMenu&&(kendo.ui.FilterMenu.prototype.options.messages=e.extend(!0,kendo.ui.FilterMenu.prototype.options.messages,{and:"Ve",cancel:"İptal",clear:"Temizle",filter:"Filtrele",info:"Tanıma uyan kayıtları göster:",title:"Tanıma uyan kayıtları göster",isFalse:"Yanlış",isTrue:"Doğru",operator:"Operatör",additionalOperator:"Ek Operatör",or:"Veya",selectValue:"Değer Seçiniz",value:"Değer",additionalValue:"Ek Değer",logic:"Bağıntı"})),kendo.ui.FilterMultiCheck&&(kendo.ui.FilterMultiCheck.prototype.options.messages=e.extend(!0,kendo.ui.FilterMultiCheck.prototype.options.messages,{search:"Arama",checkAll:"Tümünü İşaretle",clear:"Temizle",filter:"Filtrele",selectedItemsFormat:"{0} seçenek işaretlendi"})),kendo.ui.Grid&&(kendo.ui.Grid.prototype.options.messages=e.extend(!0,kendo.ui.Grid.prototype.options.messages,{commands:{canceledit:"İptal",cancel:"Değişiklikleri iptal et",create:"Yeni Kayıt Ekle",destroy:"Sil",edit:"Düzenle",excel:"Excel Kaydet",pdf:"PDF Kaydet",save:"Değişiklikleri Kaydet",select:"Seç",update:"Güncelle"},editable:{cancelDelete:"İptal",confirmation:"Kayıtları silmek istediğinizden emin misiniz ?",confirmDelete:"Sil"}})),kendo.ui.Groupable&&(kendo.ui.Groupable.prototype.options.messages=e.extend(!0,kendo.ui.Groupable.prototype.options.messages,{empty:"Bir sütun başlığını sürükleyin ve bu sütuna göre gruplandırmak için buraya bırakın"})),kendo.ui.Pager&&(kendo.ui.Pager.prototype.options.messages=e.extend(!0,kendo.ui.Pager.prototype.options.messages,{allPages:"Tümü",display:"{0} - {1} aralığı gösteriliyor. Toplam {2} öğe var",empty:"Görüntülenecek öğe yok",first:"İlk sayfaya git",itemsPerPage:"Sayfa başına ürün",last:"Son sayfaya git",morePages:"Daha fazla sayfa",next:"Bir sonraki sayfaya git",of:"{0}",page:"Sayfa",previous:"Bir önceki sayfaya git",refresh:"Güncelle"})),kendo.ui.TreeListPager&&(kendo.ui.TreeListPager.prototype.options.messages=e.extend(!0,kendo.ui.TreeListPager.prototype.options.messages,{allPages:"Tümü",display:"{0} - {1} aralığı gösteriliyor. Toplam {2} öğe var",empty:"Görüntülenecek öğe yok",first:"İlk sayfaya git",itemsPerPage:"Sayfa başına ürün",last:"Son sayfaya git",morePages:"Daha fazla sayfa",next:"Bir sonraki sayfaya git",of:"{0}",page:"Sayfa",previous:"Bir önceki sayfaya git",refresh:"Güncelle"})),kendo.ui.Scheduler&&(kendo.ui.Scheduler.prototype.options.messages=e.extend(!0,kendo.ui.Scheduler.prototype.options.messages,{allDay:"Tüm gün",cancel:"İptal Et",editable:{confirmation:"Bu etkinliği silmek istediğinizden emin misiniz?"},date:"Tarih",deleteWindowTitle:"Etkinliği sil",destroy:"Sil",editor:{allDayEvent:"Tüm gün süren olay",description:"Tanım",editorTitle:"Olay",end:"Bitiş",endTimezone:"Bitiş saati",noTimezone:"Zaman Aralığı belirtilmemiş",repeat:"Tekrar",separateTimezones:"Ayrı bir başlangıç ve bitiş Zaman aralığı kullan",start:"Başlangıç",startTimezone:"Başlangıç Saati",timezone:"",timezoneEditorButton:"Zaman Aralığı",timezoneEditorTitle:"Zaman Aralığı",title:"Tanım"},event:"Olay",recurrenceMessages:{deleteRecurring:"Sadece bu olayı mı yoksa bütün seriyi mi silmek istiyorsunuz?",deleteWindowOccurrence:"Geçerli yinelemeyi Sil",deleteWindowSeries:"Seriyi Sil",deleteWindowTitle:"Tekrarlanan Öğeyi Sil",editRecurring:"Sadece bu olayı mı yoksa bütün seriyi mi düzenlemek istiyorsunuz?",editWindowOccurrence:"Geçerli Olayı Düzenle",editWindowSeries:"Seriyi düzenle",editWindowTitle:"Tekrarlanan Öğeyi Düzenle"},save:"Kaydet",showFullDay:"Tüm gün göster",showWorkDay:"İş saatlerini göster",time:"Zaman",today:"Bugün",views:{agenda:"Gündem",day:"Gün",month:"Ay",week:"Hafta",workWeek:"Çalışma Haftası"}})),kendo.ui.Upload&&(kendo.ui.Upload.prototype.options.localization=e.extend(!0,kendo.ui.Upload.prototype.options.localization,{cancel:"İptal Et",dropFilesHere:"Yüklemek için dosyaları buraya bırakın",headerStatusUploaded:"Tamamlandı",headerStatusUploading:"Yükleniyor",remove:"Kaldır",retry:"Tekrar Dene",select:"Seçiniz",statusFailed:"Başarısız Oldu",statusUploaded:"Yüklendi",statusUploading:"Yükleniyor",uploadSelectedFiles:"Seçilen dosyaları Yükle"})),kendo.ui.Dialog&&(kendo.ui.Dialog.prototype.options.messages=e.extend(!0,kendo.ui.Dialog.prototype.options.localization,{close:"Kapat"})),kendo.ui.Alert&&(kendo.ui.Alert.prototype.options.messages=e.extend(!0,kendo.ui.Alert.prototype.options.localization,{okText:"Tamam"})),kendo.ui.Confirm&&(kendo.ui.Confirm.prototype.options.messages=e.extend(!0,kendo.ui.Confirm.prototype.options.localization,{okText:"Tamam",cancel:"İptal"})),kendo.ui.Prompt&&(kendo.ui.Prompt.prototype.options.messages=e.extend(!0,kendo.ui.Prompt.prototype.options.localization,{okText:"Tamam",cancel:"İptal"}))}(window.kendo.jQuery)});
//# sourceMappingURL=kendo.messages.tr-TR.min.js.map
