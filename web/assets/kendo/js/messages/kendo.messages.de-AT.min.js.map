{"version": 3, "sources": ["messages/kendo.messages.de-AT.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "isnull", "isnotnull", "enums", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "isempty", "isnotempty", "FilterMenu", "messages", "clear", "filter", "isFalse", "isTrue", "operator", "and", "info", "title", "or", "selectValue", "cancel", "value", "FilterMultiCheck", "checkAll", "search", "selectedItemsFormat", "ColumnMenu", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "Editor", "backColor", "bold", "createLink", "deleteFile", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "directoryNotFound", "emptyFolder", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "foreColor", "formatBlock", "imageAltText", "imageWebAddress", "imageWidth", "imageHeight", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "invalidFileType", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "orderBy", "orderByName", "orderBySize", "outdent", "overwriteFile", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "uploadFile", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "dropFilesHere", "formatting", "viewHtml", "dialogUpdate", "insertFile", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "browserMessages", "FileBrowser", "ImageBrowser", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "confirmation", "cancelDelete", "confirmDelete", "noRecords", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "itemsPerPage", "next", "page", "previous", "refresh", "morePages", "TreeListPager", "Upload", "localization", "remove", "statusFailed", "statusWarning", "statusUploaded", "statusUploading", "uploadSelectedFiles", "headerStatusUploaded", "headerStatusUploading", "Scheduler", "allDay", "allDayEvent", "description", "timezoneTitle", "endTimezone", "repeat", "separateTimezones", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "agenda", "workWeek", "timeline", "timelineWeek", "timelineWorkWeek", "timelineMonth", "defaultRowText", "showFullDay", "showWorkDay", "ariaSlotLabel", "ariaEventLabel", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGRC,MAAMC,GAAGC,aACXF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,aACNC,GAAM,iBACNC,IAAO,6BACPC,GAAM,iBACNC,IAAO,6BACPC,IAAO,mBACPC,OAAU,WACVC,UAAa,kBAEfC,OACER,GAAM,aACNK,IAAO,mBACPC,OAAU,WACVC,UAAa,kBAEfE,QACET,GAAM,aACNC,GAAM,iBACNC,IAAO,6BACPC,GAAM,cACNC,IAAO,8BACPC,IAAO,mBACPC,OAAU,WACVC,UAAa,kBAEfG,QACEC,SAAY,aACZC,eAAkB,mBAClBC,SAAY,YACZb,GAAM,aACNK,IAAO,mBACPS,WAAc,cACdR,OAAU,WACVC,UAAa,iBACbQ,QAAW,WACXC,WAAc,qBAOlBxB,MAAMC,GAAGwB,aACXzB,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQC,UACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,aACNC,GAAM,iBACNC,IAAO,6BACPC,GAAM,iBACNC,IAAO,yBACPC,IAAO,mBACPC,OAAU,WACVC,UAAa,kBAEfC,OACER,GAAM,aACNK,IAAO,mBACPC,OAAU,WACVC,UAAa,kBAEfE,QACET,GAAM,aACNC,GAAM,iBACNC,IAAO,6BACPC,GAAM,kBACNC,IAAO,8BACPC,IAAO,mBACPC,OAAU,WACVC,UAAa,kBAEfG,QACEC,SAAY,aACZC,eAAkB,mBAClBC,SAAY,YACZb,GAAM,aACNK,IAAO,mBACPS,WAAc,cACdR,OAAU,WACVC,UAAa,iBACbQ,QAAW,WACXC,WAAc,qBAOlBxB,MAAMC,GAAGC,aACXF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQsB,SACpC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQsB,UACnDC,MAAS,UACTC,OAAU,SACVC,QAAW,aACXC,OAAU,cACVC,SAAY,cAMd/B,MAAMC,GAAGwB,aACXzB,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQsB,SACpC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQsB,UACnDM,IAAO,MACPL,MAAS,UACTC,OAAU,UACVK,KAAQ,gCACRC,MAAS,gCACTL,QAAW,SACXC,OAAU,UACVK,GAAM,OACNC,YAAe,eACfC,OAAU,YACVN,SAAY,WACZO,MAAS,UAMXtC,MAAMC,GAAGsC,mBACXvC,MAAMC,GAAGsC,iBAAiBpC,UAAUC,QAAQsB,SAC1C5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsC,iBAAiBpC,UAAUC,QAAQsB,UACzDc,SAAY,iBACZb,MAAS,UACTC,OAAU,UACVa,OAAU,SACVC,oBAAuB,+BAMzB1C,MAAMC,GAAG0C,aACX3C,MAAMC,GAAG0C,WAAWxC,UAAUC,QAAQsB,SACpC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0C,WAAWxC,UAAUC,QAAQsB,UACnDkB,QAAW,UACXC,cAAiB,wBACjBC,eAAkB,uBAClBC,SAAY,uBACZC,KAAQ,WACRC,KAAQ,UACRC,OAAU,aACVtB,OAAU,aAMZ5B,MAAMC,GAAGkD,mBACXnD,MAAMC,GAAGkD,iBAAiBhD,UAAUC,QAAQsB,SAC1C5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkD,iBAAiBhD,UAAUC,QAAQsB,UACzD0B,OACEC,SAAY,SACZC,YAAe,yBAEjBC,KACEC,MAAS,OACTC,WAAc,wBACdC,MAAS,WACTC,MAAS,MACTC,GAAM,KACNC,YAAe,SAEjBC,aACEV,MAAS,UACTW,QAAW,YACXJ,MAAS,MACTK,OAAU,cACVC,OAAU,YAEZF,SACEG,IAAO,MACPb,SAAY,WACZC,YAAe,wBACfa,SAAY,mBAEdC,iBACEC,MAAS,SACTC,OAAU,UACVC,KAAQ,UACRC,OAAU,UACVC,MAAS,WAEXT,QACEV,YAAe,wBACfa,SAAY,kBACZd,SAAY,YAEdY,QACES,GAAM,MACNpB,YAAe,wBACfa,SAAY,kBACZd,SAAY,WAEdsB,UACET,IAAO,MACPU,QAAW,YACXC,QAAW,wBAOf7E,MAAMC,GAAG6E,SACX9E,MAAMC,GAAG6E,OAAO3E,UAAUC,QAAQsB,SAChC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6E,OAAO3E,UAAUC,QAAQsB,UAC/CqD,UAAa,mBACbC,KAAQ,OACRC,WAAc,qBACdC,WAAc,kDACdC,sBAAyB,OACzBC,aAAgB,YAChBC,aAAgB,WAChBC,kBAAqB,6CACrBC,YAAe,qBACfC,SAAY,iBACZC,gBAAmB,uBACnBC,SAAY,QACZC,gBAAmB,qBACnBC,UAAa,QACbC,YAAe,aACfC,aAAgB,qBAChBC,gBAAmB,cACnBC,WAAc,cACdC,YAAe,YACfC,OAAU,oBACVC,WAAc,gBACdC,YAAe,gBACfC,kBAAqB,mBACrBC,oBAAuB,eACvBC,gBAAmB,8EACnBC,OAAU,SACVC,cAAiB,YACjBC,YAAe,aACfC,YAAe,cACfC,aAAgB,eAChBC,oBAAuB,qCACvBC,SAAY,OACZC,YAAe,UACfC,eAAkB,cAClBC,QAAW,iBACXC,YAAe,OACfC,YAAe,QACfC,QAAW,qBACXC,cAAiB,6GACjB5E,OAAU,SACV6E,cAAiB,kBACjBC,OAAU,OACVC,UAAa,eACbC,YAAe,eACfC,UAAa,gBACbC,OAAU,sBACVC,WAAc,YACdC,YAAe,mBACfC,cAAiB,wBACjBC,eAAkB,yBAClBC,YAAe,0BACfC,YAAe,2BACfC,aAAgB,iBAChBC,UAAa,gBACbC,cAAiB,uCACjBC,WAAc,SACdC,SAAY,YACZC,aAAgB,gBAChBC,WAAc,iBACdC,SAAY,KACZC,YAAe,qBACfC,SAAY,UACZC,QAAW,gBACXC,iBAAoB,iBACpBC,QAAW,YACXC,QAAW,kBACXC,MAAS,SACTC,OAAU,OACVC,YAAe,cACfC,YAAe,iBACfC,WAAc,aACdC,UAAa,cACbC,WAAc,cACdC,SAAY,aACZC,GAAM,KACNC,OAAU,SACVC,YAAe,aACfC,gBAAmB,kBACnBC,SAAY,YACZC,0BAA6B,8BAC7BC,UAAa,mBACbC,YAAe,uBACfC,WAAc,oBACdC,aAAgB,4BAChBC,eAAkB,gCAClBC,cAAiB,6BACjBC,gBAAmB,6BACnBC,kBAAqB,iCACrBC,iBAAoB,8BACpBC,gBAAmB,6BACnBC,kBAAqB,iCACrBC,iBAAoB,8BACpBC,YAAe,wBACf9H,QAAW,UACX+H,KAAQ,SACRC,eAAkB,kCAMxB,IAAIC,IACFjD,WAAc,YACdX,QAAW,iBACXC,YAAe,OACfC,YAAe,QACf7B,kBAAqB,wCACrBC,YAAe,qBACfL,WAAc,2DACdqB,gBAAmB,8EACnBc,cAAiB,gFACjBe,cAAiB,uCACjB3F,OAAU,SAGRzC,OAAMC,GAAG6K,cACX9K,MAAMC,GAAG6K,YAAY3K,UAAUC,QAAQsB,SACrC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6K,YAAY3K,UAAUC,QAAQsB,SAAUmJ,IAGhE7K,MAAMC,GAAG8K,eACX/K,MAAMC,GAAG8K,aAAa5K,UAAUC,QAAQsB,SACtC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8K,aAAa5K,UAAUC,QAAQsB,SAAUmJ,IAKjE7K,MAAMC,GAAG+K,QACXhL,MAAMC,GAAG+K,MAAM7K,UAAUC,QAAQsB,SAC/B5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+K,MAAM7K,UAAUC,QAAQsB,UAC9CuJ,SACEC,SAAY,0BACZC,OAAU,qBACVC,YAAe,iBACfC,aAAgB,gBAChBC,IAAO,uBAETjJ,OAAU,YACVkJ,4BAA+B,oBAC/BC,sBAAyB,kBACzBC,QAAW,UACXC,QACEC,aAAgB,WAChBC,YAAe,UACfrI,IAAO,OACPsI,gBAAmB,gBACnBC,UAAa,aACbC,qBAAwB,aACxBC,gBAAmB,aACnBC,MAAS,QACT/J,MAAS,QACTgK,YAAe,aAEjBC,KAAQ,YACRC,OACElI,IAAO,MACPX,IAAO,OACP8I,MAAS,QACTJ,MAAS,QACTK,KAAQ,QACRC,KAAQ,WAOZvM,MAAMC,GAAGuM,OACXxM,MAAMC,GAAGuM,KAAKrM,UAAUC,QAAQsB,SAC9B5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuM,KAAKrM,UAAUC,QAAQsB,UAC7C+K,UACEC,WAAc,YACdrK,OAAU,uBACVsK,OAAU,6BACVlB,QAAW,UACXmB,KAAQ,aACRC,MAAS,wBACTvB,IAAO,sBACPa,KAAQ,uBACRW,OAAU,QACVC,OAAU,iBAEZC,UACEC,aAAgB,6DAChBC,aAAgB,YAChBC,cAAiB,WAEnBC,UAAa,iCAMfpN,MAAMC,GAAGoN,WACXrN,MAAMC,GAAGoN,SAASlN,UAAUC,QAAQsB,SAClC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoN,SAASlN,UAAUC,QAAQsB,UACjD4L,OAAU,8BACVC,QAAW,gBACXC,cAAiB,uBACjBC,MAAS,YACThB,UACEG,KAAQ,aACRG,OAAU,gBACVL,WAAc,YACdC,OAAU,6BACVe,YAAe,4BACfjC,QAAW,UACXoB,MAAS,wBACTvB,IAAO,0BAOXtL,MAAMC,GAAG0N,YACX3N,MAAMC,GAAG0N,UAAUxN,UAAUC,QAAQsB,SACnC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0N,UAAUxN,UAAUC,QAAQsB,UAClDkM,MAAS,qFAMX5N,MAAMC,GAAG4N,iBACX7N,MAAMC,GAAG4N,eAAe1N,UAAUC,QAChCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4N,eAAe1N,UAAUC,SAC/C0N,YAAe,eACfC,cAAiB,qBAMnB/N,MAAMC,GAAG+N,QACXhO,MAAMC,GAAG+N,MAAM7N,UAAUC,QAAQsB,SAC/B5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+N,MAAM7N,UAAUC,QAAQsB,UAC9CuM,SAAY,OACZC,QAAW,6BACXN,MAAS,cACTvJ,MAAS,mBACT8J,aAAgB,qBAChB5J,KAAQ,oBACR6J,KAAQ,qBACR1J,GAAM,UACN2J,KAAQ,QACRC,SAAY,uBACZC,QAAW,gBACXC,UAAa,oBAOfxO,MAAMC,GAAGwO,gBACXzO,MAAMC,GAAGwO,cAActO,UAAUC,QAAQsB,SACvC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwO,cAActO,UAAUC,QAAQsB,UACtDuM,SAAY,OACZC,QAAW,6BACXN,MAAS,cACTvJ,MAAS,mBACT8J,aAAgB,qBAChB5J,KAAQ,oBACR6J,KAAQ,qBACR1J,GAAM,UACN2J,KAAQ,QACRC,SAAY,uBACZC,QAAW,gBACXC,UAAa,oBAMfxO,MAAMC,GAAGyO,SACX1O,MAAMC,GAAGyO,OAAOvO,UAAUC,QAAQuO,aAChC7O,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyO,OAAOvO,UAAUC,QAAQuO,cAC/CtM,OAAU,UACV+F,cAAiB,uCACjBwG,OAAU,UACVnB,MAAS,cACTX,OAAU,gBACV+B,aAAgB,oBAChBC,cAAiB,UACjBC,eAAkB,cAClBC,gBAAmB,YACnBC,oBAAuB,oBACvBC,qBAAwB,cACxBC,sBAAyB,kBAM3BnP,MAAMC,GAAGmP,YACXpP,MAAMC,GAAGmP,UAAUjP,UAAUC,QAAQsB,SACnC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmP,UAAUjP,UAAUC,QAAQsB,UAClD2N,OAAU,aACVhN,OAAU,YACV9B,KAAQ,QACRkL,QAAW,UACXH,IAAO,sBACP0B,UACEC,aAAgB,+CAElBvB,QACE4D,YAAe,qBACfC,YAAe,eACf3D,YAAe,SACfrI,IAAO,UACPiM,cAAiB,WACjBC,YAAe,gBACfC,OAAU,cACVC,kBAAqB,oDACrB1D,MAAS,UACT2D,cAAiB,iBACjBC,SAAY,uBACZC,qBAAwB,WACxBC,oBAAuB,YACvB7N,MAAS,QACT8N,WAAc,kBAEhBC,MAAS,SACTC,oBACEC,gBAAmB,kEACnBC,uBAA0B,wBAC1BC,mBAAsB,0CACtBC,kBAAqB,gDACrBC,cAAiB,qEACjBC,qBAAwB,gCACxBC,iBAAoB,mBACpBC,gBAAmB,mCAErBvE,KAAQ,YACRwE,KAAQ,OACRC,MAAS,QACTxE,OACEyE,OAAU,SACV3M,IAAO,MACPmI,MAAS,QACTC,KAAQ,QACRwE,SAAY,eACZC,SAAY,aACZC,aAAgB,mBAChBC,iBAAoB,0BACpBC,cAAiB,oBAEnBZ,kBAAqB,iBACrBa,eAAkB,eAClBC,YAAe,sBACfC,YAAe,2BACfC,cAAiB,iCACjBC,eAAkB,2BAMpBvR,MAAMC,GAAGuR,YACXxR,MAAMC,GAAGuR,UAAUrR,UAAUC,QAAQsB,SACnC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuR,UAAUrR,UAAUC,QAAQsB,UAClD+P,SAAY,oBACZC,QAAW,mBACXC,IAAO,2CACPC,IAAO,4CACPC,KAAQ,mBACRC,MAAS,+BACTC,IAAO,4BACPxR,KAAQ,iCAMVP,MAAMC,GAAG+R,SACXhS,MAAMC,GAAG+R,OAAO7R,UAAUC,QAAQsB,SAChC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+R,OAAO7R,UAAUC,QAAQuO,cAC/CsD,MAAS,eAMXjS,MAAMC,GAAGiS,QACXlS,MAAMC,GAAGiS,MAAM/R,UAAUC,QAAQsB,SAC/B5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiS,MAAM/R,UAAUC,QAAQuO,cAC9CwD,OAAU,QAMZnS,MAAMC,GAAGmS,UACXpS,MAAMC,GAAGmS,QAAQjS,UAAUC,QAAQsB,SACjC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmS,QAAQjS,UAAUC,QAAQuO,cAChDwD,OAAU,KACV9P,OAAU,eAMZrC,MAAMC,GAAGoS,SACXrS,MAAMC,GAAGoS,OAAOlS,UAAUC,QAAQsB,SAChC5B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoS,OAAOlS,UAAUC,QAAQuO,cAC/CwD,OAAU,KACV9P,OAAU,gBAIfiQ,OAAOtS,MAAMuS", "file": "kendo.messages.de-AT.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n  /* Filter cell operator messages */\n\n  if (kendo.ui.FilterCell) {\n    kendo.ui.FilterCell.prototype.options.operators =\n      $.extend(true, kendo.ui.FilterCell.prototype.options.operators, {\n        \"date\": {\n          \"eq\": \"gleich ist\",\n          \"gt\": \"später ist als\",\n          \"gte\": \"gleich oder später ist als\",\n          \"lt\": \"früher ist als\",\n          \"lte\": \"früher oder gleich ist als\",\n          \"neq\": \"nicht gleich ist\",\n          \"isnull\": \"Null ist\",\n          \"isnotnull\": \"nicht Null ist\"\n        },\n        \"enums\": {\n          \"eq\": \"gleich ist\",\n          \"neq\": \"nicht gleich ist\",\n          \"isnull\": \"Null ist\",\n          \"isnotnull\": \"nicht Null ist\"\n        },\n        \"number\": {\n          \"eq\": \"gleich ist\",\n          \"gt\": \"größer ist als\",\n          \"gte\": \"größer als oder gleich ist\",\n          \"lt\": \"kleiner ist\",\n          \"lte\": \"kleiner als oder gleich ist\",\n          \"neq\": \"nicht gleich ist\",\n          \"isnull\": \"Null ist\",\n          \"isnotnull\": \"nicht Null ist\"\n        },\n        \"string\": {\n          \"contains\": \"beinhaltet\",\n          \"doesnotcontain\": \"nicht beinhaltet\",\n          \"endswith\": \"endet mit\",\n          \"eq\": \"gleich ist\",\n          \"neq\": \"nicht gleich ist\",\n          \"startswith\": \"beginnt mit\",\n          \"isnull\": \"Null ist\",\n          \"isnotnull\": \"nicht Null ist\",\n          \"isempty\": \"leer ist\",\n          \"isnotempty\": \"nicht leer ist\"\n        }\n      });\n  }\n\n  /* Filter menu operator messages */\n\n  if (kendo.ui.FilterMenu) {\n    kendo.ui.FilterMenu.prototype.options.operators =\n      $.extend(true, kendo.ui.FilterMenu.prototype.options.operators, {\n        \"date\": {\n          \"eq\": \"gleich ist\",\n          \"gt\": \"später ist als\",\n          \"gte\": \"gleich oder später ist als\",\n          \"lt\": \"früher ist als\",\n          \"lte\": \"früher oder gleich ist\",\n          \"neq\": \"nicht gleich ist\",\n          \"isnull\": \"Null ist\",\n          \"isnotnull\": \"nicht Null ist\"\n        },\n        \"enums\": {\n          \"eq\": \"gleich ist\",\n          \"neq\": \"nicht gleich ist\",\n          \"isnull\": \"Null ist\",\n          \"isnotnull\": \"nicht Null ist\"\n        },\n        \"number\": {\n          \"eq\": \"gleich ist\",\n          \"gt\": \"größer ist als\",\n          \"gte\": \"größer als oder gleich ist\",\n          \"lt\": \"kleiner ist als\",\n          \"lte\": \"kleiner als oder gleich ist\",\n          \"neq\": \"nicht gleich ist\",\n          \"isnull\": \"Null ist\",\n          \"isnotnull\": \"nicht Null ist\"\n        },\n        \"string\": {\n          \"contains\": \"beinhaltet\",\n          \"doesnotcontain\": \"nicht beinhaltet\",\n          \"endswith\": \"endet mit\",\n          \"eq\": \"gleich ist\",\n          \"neq\": \"nicht gleich ist\",\n          \"startswith\": \"beginnt mit\",\n          \"isnull\": \"Null ist\",\n          \"isnotnull\": \"nicht Null ist\",\n          \"isempty\": \"leer ist\",\n          \"isnotempty\": \"nicht leer ist\"\n        }\n      });\n  }\n\n  /* FilterCell messages */\n\n  if (kendo.ui.FilterCell) {\n    kendo.ui.FilterCell.prototype.options.messages =\n      $.extend(true, kendo.ui.FilterCell.prototype.options.messages, {\n        \"clear\": \"Löschen\",\n        \"filter\": \"Filter\",\n        \"isFalse\": \"ist falsch\",\n        \"isTrue\": \"ist richtig\",\n        \"operator\": \"Operator\"\n      });\n  }\n\n  /* FilterMenu messages */\n\n  if (kendo.ui.FilterMenu) {\n    kendo.ui.FilterMenu.prototype.options.messages =\n      $.extend(true, kendo.ui.FilterMenu.prototype.options.messages, {\n        \"and\": \"und\",\n        \"clear\": \"Löschen\",\n        \"filter\": \"Filtern\",\n        \"info\": \"Zeilen mit Wert anzeigen, der\",\n        \"title\": \"Zeilen mit Wert anzeigen, der\",\n        \"isFalse\": \"falsch\",\n        \"isTrue\": \"richtig\",\n        \"or\": \"oder\",\n        \"selectValue\": \"-Wählen Sie-\",\n        \"cancel\": \"Abbrechen\",\n        \"operator\": \"Operator\",\n        \"value\": \"Wert\"\n      });\n  }\n\n  /* FilterMultiCheck messages */\n\n  if (kendo.ui.FilterMultiCheck) {\n    kendo.ui.FilterMultiCheck.prototype.options.messages =\n      $.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages, {\n        \"checkAll\": \"Alle auswählen\",\n        \"clear\": \"Löschen\",\n        \"filter\": \"Filtern\",\n        \"search\": \"Suchen\",\n        \"selectedItemsFormat\": \"{0} Element(e) ausgewählt\"\n      });\n  }\n\n  /* ColumnMenu messages */\n\n  if (kendo.ui.ColumnMenu) {\n    kendo.ui.ColumnMenu.prototype.options.messages =\n      $.extend(true, kendo.ui.ColumnMenu.prototype.options.messages, {\n        \"columns\": \"Spalten\",\n        \"sortAscending\": \"Aufsteigend sortieren\",\n        \"sortDescending\": \"Absteigend sortieren\",\n        \"settings\": \"Spalteneinstellungen\",\n        \"done\": \"Erledigt\",\n        \"lock\": \"Sperren\",\n        \"unlock\": \"Entsperren\",\n        \"filter\": \"Filtern\"\n      });\n  }\n\n  /* RecurrenceEditor messages */\n\n  if (kendo.ui.RecurrenceEditor) {\n    kendo.ui.RecurrenceEditor.prototype.options.messages =\n      $.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages, {\n        \"daily\": {\n          \"interval\": \"Tag(e)\",\n          \"repeatEvery\": \"Wiederholen an jedem:\"\n        },\n        \"end\": {\n          \"after\": \"Nach\",\n          \"occurrence\": \"Anzahl Wiederholungen\",\n          \"label\": \"Beenden:\",\n          \"never\": \"Nie\",\n          \"on\": \"Am\",\n          \"mobileLabel\": \"Endet\"\n        },\n        \"frequencies\": {\n          \"daily\": \"Täglich\",\n          \"monthly\": \"Monatlich\",\n          \"never\": \"Nie\",\n          \"weekly\": \"Wöchentlich\",\n          \"yearly\": \"Jährlich\"\n        },\n        \"monthly\": {\n          \"day\": \"Tag\",\n          \"interval\": \"Monat(e)\",\n          \"repeatEvery\": \"Wiederholen an jedem:\",\n          \"repeatOn\": \"Wiederholen am:\"\n        },\n        \"offsetPositions\": {\n          \"first\": \"ersten\",\n          \"fourth\": \"vierten\",\n          \"last\": \"letzten\",\n          \"second\": \"zweiten\",\n          \"third\": \"dritten\"\n        },\n        \"weekly\": {\n          \"repeatEvery\": \"Wiederholen an jedem:\",\n          \"repeatOn\": \"Wiederholen am:\",\n          \"interval\": \"Woche(n)\"\n        },\n        \"yearly\": {\n          \"of\": \"von\",\n          \"repeatEvery\": \"Wiederholen an jedem:\",\n          \"repeatOn\": \"Wiederholen am:\",\n          \"interval\": \"Jahr(e)\"\n        },\n        \"weekdays\": {\n          \"day\": \"Tag\",\n          \"weekday\": \"Wochentag\",\n          \"weekend\": \"Tag am Wochenende\"\n        }\n      });\n  }\n\n  /* Editor messages */\n\n  if (kendo.ui.Editor) {\n    kendo.ui.Editor.prototype.options.messages =\n      $.extend(true, kendo.ui.Editor.prototype.options.messages, {\n        \"backColor\": \"Hintergrundfarbe\",\n        \"bold\": \"Fett\",\n        \"createLink\": \"Hyperlink einfügen\",\n        \"deleteFile\": \"Sind Sie sicher, dass Sie \\\"{0}\\\" löschen wollen?\",\n        \"dialogButtonSeparator\": \"oder\",\n        \"dialogCancel\": \"Abbrechen\",\n        \"dialogInsert\": \"Einfügen\",\n        \"directoryNotFound\": \"Kein Verzeichnis mit diesem Namen gefunden\",\n        \"emptyFolder\": \"Leeres Verzeichnis\",\n        \"fontName\": \"Schriftfamilie\",\n        \"fontNameInherit\": \"(Schrift übernehmen)\",\n        \"fontSize\": \"Größe\",\n        \"fontSizeInherit\": \"(Größe übernehmen)\",\n        \"foreColor\": \"Farbe\",\n        \"formatBlock\": \"Absatzstil\",\n        \"imageAltText\": \"Abwechselnder Text\",\n        \"imageWebAddress\": \"Web-Adresse\",\n        \"imageWidth\": \"Breite (px)\",\n        \"imageHeight\": \"Höhe (px)\",\n        \"indent\": \"Einzug vergrößern\",\n        \"insertHtml\": \"HTML einfügen\",\n        \"insertImage\": \"Bild einfügen\",\n        \"insertOrderedList\": \"Numerierte Liste\",\n        \"insertUnorderedList\": \"Aufzählliste\",\n        \"invalidFileType\": \"Die ausgewählte Datei \\\"{0}\\\" ist ungültig. Unterstützte Dateitypen sind {1}.\",\n        \"italic\": \"Kursiv\",\n        \"justifyCenter\": \"Zentriert\",\n        \"justifyFull\": \"Ausrichten\",\n        \"justifyLeft\": \"Linksbündig\",\n        \"justifyRight\": \"Rechtsbündig\",\n        \"linkOpenInNewWindow\": \"Link in einem neuen Fenster öffnen\",\n        \"linkText\": \"Text\",\n        \"linkToolTip\": \"ToolTip\",\n        \"linkWebAddress\": \"Web-Adresse\",\n        \"orderBy\": \"Sortiert nach:\",\n        \"orderByName\": \"Name\",\n        \"orderBySize\": \"Größe\",\n        \"outdent\": \"Einzug verkleinern\",\n        \"overwriteFile\": \"Eine Datei mit dem Namen \\\"{0}\\\" existiert bereits im aktuellen Verzeichnis. Wollen Sie diese überschreiben?\",\n        \"search\": \"Suchen\",\n        \"strikethrough\": \"Durchgestrichen\",\n        \"styles\": \"Stil\",\n        \"subscript\": \"Tiefgestellt\",\n        \"superscript\": \"Hochgestellt\",\n        \"underline\": \"Unterstrichen\",\n        \"unlink\": \"Hyperlink entfernen\",\n        \"uploadFile\": \"Hochladen\",\n        \"createTable\": \"Tabelle einfügen\",\n        \"addColumnLeft\": \"Spalte links einfügen\",\n        \"addColumnRight\": \"Spalte rechts einfügen\",\n        \"addRowAbove\": \"Zeile oberhalb einfügen\",\n        \"addRowBelow\": \"Zeile unterhalb einfügen\",\n        \"deleteColumn\": \"Spalte löschen\",\n        \"deleteRow\": \"Zeile löschen\",\n        \"dropFilesHere\": \"Dateien hierhin ziehen zum Hochladen\",\n        \"formatting\": \"Format\",\n        \"viewHtml\": \"View HTML\",\n        \"dialogUpdate\": \"Aktualisieren\",\n        \"insertFile\": \"Datei einfügen\",\n        \"dialogOk\": \"OK\",\n        \"tableWizard\": \"Tabellen-Assistent\",\n        \"tableTab\": \"Tabelle\",\n        \"cellTab\": \"Tabellenzelle\",\n        \"accessibilityTab\": \"Zugänglichkeit\",\n        \"caption\": \"Erklärung\",\n        \"summary\": \"Zusammenfassung\",\n        \"width\": \"Breite\",\n        \"height\": \"Höhe\",\n        \"cellSpacing\": \"Zellabstand\",\n        \"cellPadding\": \"Zellauffüllung\",\n        \"cellMargin\": \"Zellenrand\",\n        \"alignment\": \"Ausrichtung\",\n        \"background\": \"Hintergrund\",\n        \"cssClass\": \"CSS Klasse\",\n        \"id\": \"Id\",\n        \"border\": \"Rahmen\",\n        \"borderStyle\": \"Rahmenstil\",\n        \"collapseBorders\": \"Collapse rahmen\",\n        \"wrapText\": \"Texthülle\",\n        \"associateCellsWithHeaders\": \"Zellen mit header verbinden\",\n        \"alignLeft\": \"Ausrichten links\",\n        \"alignCenter\": \"Ausrichten zentriert\",\n        \"alignRight\": \"Ausrichten rechts\",\n        \"alignLeftTop\": \"Ausrichten links und oben\",\n        \"alignCenterTop\": \"Ausrichten zentriert und oben\",\n        \"alignRightTop\": \"Ausrichten rechts und oben\",\n        \"alignLeftMiddle\": \"Ausrichten links und mitte\",\n        \"alignCenterMiddle\": \"Ausrichten zentriert und mitte\",\n        \"alignRightMiddle\": \"Ausrichten rechts und mitte\",\n        \"alignLeftBottom\": \"Ausrichten links und unten\",\n        \"alignCenterBottom\": \"Ausrichten zentriert und unten\",\n        \"alignRightBottom\": \"Ausrichten rechts und unten\",\n        \"alignRemove\": \"Ausrichtung entfernen\",\n        \"columns\": \"Spalten\",\n        \"rows\": \"Reihen\",\n        \"selectAllCells\": \"Alle Tabellenzellen auswählen\"\n      });\n  }\n\n  /* FileBrowser and ImageBrowser messages */\n\n  var browserMessages = {\n    \"uploadFile\": \"Hochladen\",\n    \"orderBy\": \"Sortieren nach\",\n    \"orderByName\": \"Name\",\n    \"orderBySize\": \"Größe\",\n    \"directoryNotFound\": \"Das Verzeichnis wurde nicht gefunden.\",\n    \"emptyFolder\": \"Leeres Verzeichnis\",\n    \"deleteFile\": 'Sind Sie sicher, dass Sie \"{0}\" wirklich löschen wollen?',\n    \"invalidFileType\": \"Die ausgewählte Datei \\\"{0}\\\" ist ungültig. Unterstützte Dateitypen sind {1}.\",\n    \"overwriteFile\": \"Eine Datei namens \\\"{0}\\\" existiert bereits im aktuellen Ordner. Überschreiben?\",\n    \"dropFilesHere\": \"Dateien hierhin ziehen zum Hochladen\",\n    \"search\": \"Suchen\"\n  };\n\n  if (kendo.ui.FileBrowser) {\n    kendo.ui.FileBrowser.prototype.options.messages =\n      $.extend(true, kendo.ui.FileBrowser.prototype.options.messages, browserMessages);\n  }\n\n  if (kendo.ui.ImageBrowser) {\n    kendo.ui.ImageBrowser.prototype.options.messages =\n      $.extend(true, kendo.ui.ImageBrowser.prototype.options.messages, browserMessages);\n  }\n\n  /* Gantt messages */\n\n  if (kendo.ui.Gantt) {\n    kendo.ui.Gantt.prototype.options.messages =\n      $.extend(true, kendo.ui.Gantt.prototype.options.messages, {\n        \"actions\": {\n          \"addChild\": \"Unteraufgabe hinzufügen\",\n          \"append\": \"Aufgabe hinzufügen\",\n          \"insertAfter\": \"Unter einfügen\",\n          \"insertBefore\": \"Über einfügen\",\n          \"pdf\": \"Exportieren als PDF\"\n        },\n        \"cancel\": \"Abbrechen\",\n        \"deleteDependencyWindowTitle\": \"Beziehung löschen\",\n        \"deleteTaskWindowTitle\": \"Aufgabe löschen\",\n        \"destroy\": \"Löschen\",\n        \"editor\": {\n          \"assingButton\": \"Zuweisen\",\n          \"editorTitle\": \"Aufgabe\",\n          \"end\": \"Ende\",\n          \"percentComplete\": \"abgeschlossen\",\n          \"resources\": \"Ressourcen\",\n          \"resourcesEditorTitle\": \"Ressourcen\",\n          \"resourcesHeader\": \"Ressourcen\",\n          \"start\": \"Start\",\n          \"title\": \"Titel\",\n          \"unitsHeader\": \"Einheiten\"\n        },\n        \"save\": \"Speichern\",\n        \"views\": {\n          \"day\": \"Tag\",\n          \"end\": \"Ende\",\n          \"month\": \"Monat\",\n          \"start\": \"Start\",\n          \"week\": \"Woche\",\n          \"year\": \"Jahr\"\n        }\n      });\n  }\n\n  /* Grid messages */\n\n  if (kendo.ui.Grid) {\n    kendo.ui.Grid.prototype.options.messages =\n      $.extend(true, kendo.ui.Grid.prototype.options.messages, {\n        \"commands\": {\n          \"canceledit\": \"Abbrechen\",\n          \"cancel\": \"Änderungen verwerfen\",\n          \"create\": \"Neuen Datensatz hinzufügen\",\n          \"destroy\": \"Löschen\",\n          \"edit\": \"Bearbeiten\",\n          \"excel\": \"Exportieren als Excel\",\n          \"pdf\": \"Exportieren als PDF\",\n          \"save\": \"Änderungen speichern\",\n          \"select\": \"Wähle\",\n          \"update\": \"Aktualisieren\"\n        },\n        \"editable\": {\n          \"confirmation\": \"Sind Sie sicher, dass Sie diesen Datensatz löschen wollen?\",\n          \"cancelDelete\": \"Abbrechen\",\n          \"confirmDelete\": \"Löschen\"\n        },\n        \"noRecords\": \"Keine Datensätze verfügbar.\"\n      });\n  }\n\n  /* TreeList messages */\n\n  if (kendo.ui.TreeList) {\n    kendo.ui.TreeList.prototype.options.messages =\n      $.extend(true, kendo.ui.TreeList.prototype.options.messages, {\n        \"noRows\": \"Keine Datensätze verfügbar.\",\n        \"loading\": \"Lade Daten...\",\n        \"requestFailed\": \"Laden fehlgeschlagen\",\n        \"retry\": \"Neu laden\",\n        \"commands\": {\n          \"edit\": \"Bearbeiten\",\n          \"update\": \"Aktualisieren\",\n          \"canceledit\": \"Abbrechen\",\n          \"create\": \"Neuen Datensatz hinzufügen\",\n          \"createchild\": \"Kind-Datensatz hinzufügen\",\n          \"destroy\": \"Löschen\",\n          \"excel\": \"Exportieren als Excel\",\n          \"pdf\": \"Exportieren als PDF\"\n        }\n      });\n  }\n\n  /* Groupable messages */\n\n  if (kendo.ui.Groupable) {\n    kendo.ui.Groupable.prototype.options.messages =\n      $.extend(true, kendo.ui.Groupable.prototype.options.messages, {\n        \"empty\": \"Ziehen Sie eine Spaltenüberschrift hierher, um nach dieser Spalte zu gruppieren\"\n      });\n  }\n\n  /* NumericTextBox messages */\n\n  if (kendo.ui.NumericTextBox) {\n    kendo.ui.NumericTextBox.prototype.options =\n      $.extend(true, kendo.ui.NumericTextBox.prototype.options, {\n        \"upArrowText\": \"Wert erhöhen\",\n        \"downArrowText\": \"Wert verringern\"\n      });\n  }\n\n  /* Pager messages */\n\n  if (kendo.ui.Pager) {\n    kendo.ui.Pager.prototype.options.messages =\n      $.extend(true, kendo.ui.Pager.prototype.options.messages, {\n        \"allPages\": \"Alle\",\n        \"display\": \"Einträge {0} - {1} von {2}\",\n        \"empty\": \"keine Daten\",\n        \"first\": \"Zur ersten Seite\",\n        \"itemsPerPage\": \"Elemente pro Seite\",\n        \"last\": \"Zur letzten Seite\",\n        \"next\": \"Zur nächsten Seite\",\n        \"of\": \"von {0}\",\n        \"page\": \"Seite\",\n        \"previous\": \"Zur vorherigen Seite\",\n        \"refresh\": \"Aktualisieren\",\n        \"morePages\": \"Weitere Seiten\"\n      });\n  }\n\n\n  /* TreeListPager messages */\n\n  if (kendo.ui.TreeListPager) {\n    kendo.ui.TreeListPager.prototype.options.messages =\n      $.extend(true, kendo.ui.TreeListPager.prototype.options.messages, {\n        \"allPages\": \"Alle\",\n        \"display\": \"Einträge {0} - {1} von {2}\",\n        \"empty\": \"keine Daten\",\n        \"first\": \"Zur ersten Seite\",\n        \"itemsPerPage\": \"Elemente pro Seite\",\n        \"last\": \"Zur letzten Seite\",\n        \"next\": \"Zur nächsten Seite\",\n        \"of\": \"von {0}\",\n        \"page\": \"Seite\",\n        \"previous\": \"Zur vorherigen Seite\",\n        \"refresh\": \"Aktualisieren\",\n        \"morePages\": \"Weitere Seiten\"\n      });\n  }\n\n  /* Upload messages */\n\n  if (kendo.ui.Upload) {\n    kendo.ui.Upload.prototype.options.localization =\n      $.extend(true, kendo.ui.Upload.prototype.options.localization, {\n        \"cancel\": \"Beenden\",\n        \"dropFilesHere\": \"Dateien hierhin ziehen zum Hochladen\",\n        \"remove\": \"Löschen\",\n        \"retry\": \"Wiederholen\",\n        \"select\": \"Wählen Sie...\",\n        \"statusFailed\": \"nicht erfolgreich\",\n        \"statusWarning\": \"Warnung\",\n        \"statusUploaded\": \"hochgeladen\",\n        \"statusUploading\": \"hochladen\",\n        \"uploadSelectedFiles\": \"Dateien hochladen\",\n        \"headerStatusUploaded\": \"Hochgeladen\",\n        \"headerStatusUploading\": \"Hochladen...\"\n      });\n  }\n\n  /* Scheduler messages */\n\n  if (kendo.ui.Scheduler) {\n    kendo.ui.Scheduler.prototype.options.messages =\n      $.extend(true, kendo.ui.Scheduler.prototype.options.messages, {\n        \"allDay\": \"Ganzer Tag\",\n        \"cancel\": \"Abbrechen\",\n        \"date\": \"Datum\",\n        \"destroy\": \"Löschen\",\n        \"pdf\": \"Exportieren als PDF\",\n        \"editable\": {\n          \"confirmation\": \"Möchten Sie diesen Termin wirklich löschen?\"\n        },\n        \"editor\": {\n          \"allDayEvent\": \"Ganztägiger Termin\",\n          \"description\": \"Beschreibung\",\n          \"editorTitle\": \"Termin\",\n          \"end\": \"Beenden\",\n          \"timezoneTitle\": \"Zeitzone\",\n          \"endTimezone\": \"Zeitzone Ende\",\n          \"repeat\": \"Wiederholen\",\n          \"separateTimezones\": \"Unterschiedliche Start- und Endzeitzonen benutzen\",\n          \"start\": \"Starten\",\n          \"startTimezone\": \"Zeitzone Start\",\n          \"timezone\": \"Zeitzonen bearbeiten\",\n          \"timezoneEditorButton\": \"Zeitzone\",\n          \"timezoneEditorTitle\": \"Zeitzonen\",\n          \"title\": \"Titel\",\n          \"noTimezone\": \"Keine Zeitzone\"\n        },\n        \"event\": \"Termin\",\n        \"recurrenceMessages\": {\n          \"deleteRecurring\": \"Möchten Sie nur diesen Termin oder alle Wiederholungen löschen?\",\n          \"deleteWindowOccurrence\": \"Diesen Termin löschen\",\n          \"deleteWindowSeries\": \"Alle Wiederholungen des Termins löschen\",\n          \"deleteWindowTitle\": \"Diesen Termin und alle Wiederholungen löschen\",\n          \"editRecurring\": \"Möchten Sie nur diesen Termin oder alle Wiederholungen bearbeiten?\",\n          \"editWindowOccurrence\": \"Aktuelles Ereignis bearbeiten\",\n          \"editWindowSeries\": \"Serie bearbeiten\",\n          \"editWindowTitle\": \"Wiederholungseintrag bearbeiten\"\n        },\n        \"save\": \"Speichern\",\n        \"time\": \"Zeit\",\n        \"today\": \"Heute\",\n        \"views\": {\n          \"agenda\": \"Agenda\",\n          \"day\": \"Tag\",\n          \"month\": \"Monat\",\n          \"week\": \"Woche\",\n          \"workWeek\": \"Arbeitswoche\",\n          \"timeline\": \"Zeitstrahl\",\n          \"timelineWeek\": \"Zeitstrahl Woche\",\n          \"timelineWorkWeek\": \"Zeitstrahl Arbeitswoche\",\n          \"timelineMonth\": \"Zeitstrahl Monat\"\n        },\n        \"deleteWindowTitle\": \"Termin löschen\",\n        \"defaultRowText\": \"Alle Termine\",\n        \"showFullDay\": \"Ganzen Tag anzeigen\",\n        \"showWorkDay\": \"Geschäftszeiten anzeigen\",\n        \"ariaSlotLabel\": \"Ausgewählt von {0:t} bis {1:t}\",\n        \"ariaEventLabel\": \"{0} am {1:D} um {2:t}\"\n      });\n  }\n\n  /* Validator messages */\n\n  if (kendo.ui.Validator) {\n    kendo.ui.Validator.prototype.options.messages =\n      $.extend(true, kendo.ui.Validator.prototype.options.messages, {\n        \"required\": \"{0} ist notwendig\",\n        \"pattern\": \"{0} ist ungültig\",\n        \"min\": \"{0} muss größer oder gleich sein als {1}\",\n        \"max\": \"{0} muss kleiner oder gleich sein als {1}\",\n        \"step\": \"{0} ist ungültig\",\n        \"email\": \"{0} ist keine gültige E-Mail\",\n        \"url\": \"{0} ist keine gültige URL\",\n        \"date\": \"{0} ist kein gültiges Datum\"\n      });\n  }\n\n  /* Dialog */\n\n  if (kendo.ui.Dialog) {\n    kendo.ui.Dialog.prototype.options.messages =\n      $.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n        \"close\": \"Schließen\"\n      });\n  }\n\n  /* Alert */\n\n  if (kendo.ui.Alert) {\n    kendo.ui.Alert.prototype.options.messages =\n      $.extend(true, kendo.ui.Alert.prototype.options.localization, {\n        \"okText\": \"OK\"\n      });\n  }\n\n  /* Confirm */\n\n  if (kendo.ui.Confirm) {\n    kendo.ui.Confirm.prototype.options.messages =\n      $.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n        \"okText\": \"OK\",\n        \"cancel\": \"Abbrechen\"\n      });\n  }\n\n  /* Prompt */\n\n  if (kendo.ui.Prompt) {\n    kendo.ui.Prompt.prototype.options.messages =\n      $.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n        \"okText\": \"OK\",\n        \"cancel\": \"Abbrechen\"\n      });\n  }\n\n})(window.kendo.jQuery);}));"]}