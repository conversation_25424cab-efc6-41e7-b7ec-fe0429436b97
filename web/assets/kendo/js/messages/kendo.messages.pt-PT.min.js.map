{"version": 3, "sources": ["messages/kendo.messages.pt-PT.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "retry", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "text", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "toolbar", "alignment", "alignmentButtons", "backgroundColor", "borders", "colorPicker", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeList", "noRows", "loading", "requestFailed", "createchild", "TreeView", "Upload", "localization", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "Dialog", "close", "<PERSON><PERSON>", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,UACTC,OAAU,cAMRR,MAAMC,GAAGQ,cACbT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,UACTC,OAAU,cAMRR,MAAMC,GAAGS,aACbV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,uBACjBC,eAAkB,wBAClBC,OAAU,SACVC,QAAW,UACXC,KAAQ,QACRC,SAAY,uBACZC,KAAQ,UACRC,OAAU,gBAMRlB,MAAMC,GAAGkB,SACbnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,UACRC,OAAU,UACVC,UAAa,aACbC,cAAiB,WACjBC,YAAe,mBACfC,UAAa,mBACbC,cAAiB,gBACjBC,YAAe,2BACfC,aAAgB,0BAChBC,YAAe,aACfC,oBAAuB,0BACvBC,kBAAqB,yBACrBC,OAAU,kBACVC,QAAW,kBACXC,WAAc,kBACdC,OAAU,kBACVC,YAAe,iBACfC,WAAc,mBACdC,WAAc,eACdC,SAAY,WACZC,SAAY,2BACZC,gBAAmB,0BACnBC,SAAY,8BACZC,gBAAmB,oBACnBC,YAAe,WACfC,WAAc,WACdC,UAAa,MACbC,UAAa,eACbC,MAAS,UACTC,YAAe,cACfC,WAAc,WACdC,QAAW,eACXC,YAAe,UACfC,YAAe,OACfC,gBAAmB,uEACnBC,WAAc,yCACdC,cAAiB,4EACjBC,kBAAqB,kDACrBC,gBAAmB,eACnBC,aAAgB,oBAChBC,WAAc,eACdC,YAAe,cACfC,eAAkB,eAClBC,UAAa,SACbC,eAAkB,eAClBC,SAAY,QACZC,YAAe,UACfC,oBAAuB,iCACvBC,aAAgB,YAChBC,aAAgB,UAChBC,sBAAyB,KACzBC,aAAgB,WAChBC,YAAe,eACfC,cAAiB,8BACjBC,eAAkB,6BAClBC,YAAe,wBACfC,YAAe,yBACfC,UAAa,iBACbC,aAAgB,qBAMd9E,MAAMC,GAAG8E,cACb/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpD6C,WAAc,WACdC,QAAW,cACXE,YAAe,OACfD,YAAe,UACfK,kBAAqB,kDACrBR,YAAe,cACfM,WAAc,yCACdD,gBAAmB,uEACnBE,cAAiB,yEACjBwB,cAAiB,0CACjBC,OAAU,eAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACnD8E,OAAU,eACVC,QAAW,UACXvE,OAAU,UACVwE,MAAS,SACTC,SAAY,cAMVtF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,YACNC,IAAO,gBACPC,WAAc,aACdC,SAAY,SACZC,eAAkB,aAClBC,SAAY,cACZC,OAAU,SACVC,UAAa,aACbC,QAAW,UACXC,WAAc,eAEhBC,QACEV,GAAM,YACNC,IAAO,gBACPU,IAAO,qBACPC,GAAM,cACNC,IAAO,qBACPC,GAAM,cACNR,OAAU,SACVC,UAAa,cAEfQ,MACEf,GAAM,YACNC,IAAO,gBACPU,IAAO,yBACPC,GAAM,gBACNC,IAAO,wBACPC,GAAM,eACNR,OAAU,SACVC,UAAa,cAEfS,OACEhB,GAAM,YACNC,IAAO,gBACPK,OAAU,SACVC,UAAa,iBAObhG,MAAMC,GAAGyG,aACb1G,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQC,UACnDsG,KAAQ,+BACRC,MAAS,8BACTzB,OAAU,eACVC,QAAW,UACXvE,OAAU,UACVwE,MAAS,SACTwB,IAAO,IACPC,GAAM,KACNC,YAAe,qBACfzB,SAAY,WACZ0B,MAAS,QACTxG,OAAU,cAMRR,MAAMC,GAAGyG,aACb1G,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,YACNC,IAAO,gBACPC,WAAc,aACdC,SAAY,SACZC,eAAkB,aAClBC,SAAY,cACZC,OAAU,SACVC,UAAa,aACbC,QAAW,UACXC,WAAc,eAEhBC,QACEV,GAAM,YACNC,IAAO,gBACPU,IAAO,qBACPC,GAAM,cACNC,IAAO,qBACPC,GAAM,cACNR,OAAU,SACVC,UAAa,cAEfQ,MACEf,GAAM,YACNC,IAAO,gBACPU,IAAO,yBACPC,GAAM,gBACNC,IAAO,wBACPC,GAAM,eACNR,OAAU,SACVC,UAAa,cAEfS,OACEhB,GAAM,YACNC,IAAO,gBACPK,OAAU,SACVC,UAAa,iBAObhG,MAAMC,GAAGgH,mBACbjH,MAAMC,GAAGgH,iBAAiB9G,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgH,iBAAiB9G,UAAUC,QAAQC,UACzD6G,SAAY,kBACZ7B,MAAS,SACTxE,OAAU,UACVoE,OAAU,eAMRjF,MAAMC,GAAGkH,QACbnH,MAAMC,GAAGkH,MAAMhH,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkH,MAAMhH,UAAUC,QAAQC,UAC9C+G,SACEC,SAAY,wBACZC,OAAU,mBACVC,YAAe,mBACfC,aAAgB,kBAChBC,IAAO,qBAETjH,OAAU,WACVkH,4BAA+B,uBAC/BC,sBAAyB,kBACzBC,QAAW,WACXC,QACEC,aAAgB,WAChBC,YAAe,SACfC,IAAO,MACPC,gBAAmB,WACnBC,UAAa,WACbC,qBAAwB,WACxBC,gBAAmB,WACnBC,MAAS,SACTzB,MAAS,SACT0B,YAAe,YAEjBC,KAAQ,UACRC,OACEC,IAAO,MACPT,IAAO,MACPU,MAAS,MACTL,MAAS,SACTM,KAAQ,SACRC,KAAQ,UAOR5I,MAAMC,GAAG4I,OACb7I,MAAMC,GAAG4I,KAAK1I,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4I,KAAK1I,UAAUC,QAAQC,UAC7CyI,UACEtI,OAAU,sBACVuI,WAAc,WACdC,OAAU,yBACVpB,QAAW,WACXqB,KAAQ,SACRC,MAAS,sBACTzB,IAAO,oBACPc,KAAQ,qBACRY,OAAU,aACVC,OAAU,aAEZC,UACEC,aAAgB,WAChBC,aAAgB,oDAChBC,cAAiB,YAEnBC,UAAa,gCAMXzJ,MAAMC,GAAGyJ,YACb1J,MAAMC,GAAGyJ,UAAUvJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyJ,UAAUvJ,UAAUC,QAAQC,UAClDsJ,MAAS,0EAMP3J,MAAMC,GAAG2J,iBACb5J,MAAMC,GAAG2J,eAAezJ,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,eAAezJ,UAAUC,SAC/CyJ,YAAe,iBACfC,cAAiB,oBAMf9J,MAAMC,GAAG8J,QACb/J,MAAMC,GAAG8J,MAAM5J,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8J,MAAM5J,UAAUC,QAAQC,UAC9C2J,SAAY,OACZC,QAAW,4BACXN,MAAS,gCACTO,KAAQ,SACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,4BACTC,SAAY,4BACZC,KAAQ,2BACRC,KAAQ,0BACRC,QAAW,YACXC,UAAa,kBAMX1K,MAAMC,GAAG0K,gBACb3K,MAAMC,GAAG0K,cAAcxK,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0K,cAAcxK,UAAUC,QAAQC,UACtD2J,SAAY,OACZC,QAAW,4BACXN,MAAS,gCACTO,KAAQ,SACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,4BACTC,SAAY,4BACZC,KAAQ,2BACRC,KAAQ,0BACRC,QAAW,YACXC,UAAa,kBAMX1K,MAAMC,GAAG2K,YACb5K,MAAMC,GAAG2K,UAAUzK,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2K,UAAUzK,UAAUC,QAAQC,UAClDwK,cAAiB,8BACjBC,aAAgB,gCAChBC,UAAa,kCAMX/K,MAAMC,GAAG+K,iBACbhL,MAAMC,GAAG+K,eAAe7K,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+K,eAAe7K,UAAUC,QAAQC,UACvDsG,KAAQ,+BACRsE,aAAgB,mBAChBpK,OAAU,UACVqK,QAAW,oBACXtE,MAAS,mBACTvB,MAAS,SACT8F,GAAM,KACN3K,OAAU,WACV+E,WACEK,SAAY,SACZC,eAAkB,aAClBF,WAAc,aACdG,SAAY,cACZL,GAAM,YACNC,IAAO,oBAOP1F,MAAMC,GAAGmL,mBACbpL,MAAMC,GAAGmL,iBAAiBjL,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmL,iBAAiBjL,UAAUC,QAAQC,UACzDgL,aACEC,MAAS,QACTC,OAAU,cACVC,MAAS,cACTC,OAAU,eACVC,QAAW,cACXC,OAAU,cAEZJ,QACEK,YAAe,mBACfC,SAAY,YAEdL,OACEI,YAAe,mBACfC,SAAY,WAEdJ,QACEI,SAAY,aACZD,YAAe,mBACfE,SAAY,gBAEdJ,SACEE,YAAe,mBACfE,SAAY,eACZD,SAAY,WACZpD,IAAO,QAETkD,QACEC,YAAe,mBACfE,SAAY,eACZD,SAAY,UACZ1B,GAAM,QAERnC,KACE+D,MAAS,OACTC,YAAe,MACfV,MAAS,QACTW,MAAS,SACTC,WAAc,iBACdC,GAAM,OAERC,iBACE/B,MAAS,WACTgC,OAAU,UACVC,MAAS,WACTC,OAAU,SACV/B,KAAQ,UAEVgC,UACE/D,IAAO,MACPgE,QAAW,SACXC,QAAW,oBAOX1M,MAAMC,GAAG0M,YACb3M,MAAMC,GAAG0M,UAAUxM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0M,UAAUxM,UAAUC,QAAQC,UAClDuM,OAAU,aACVpG,KAAQ,OACRqG,MAAS,SACTC,KAAQ,OACRC,YAAe,uBACfC,YAAe,8BACfC,MAAS,OACT1E,KAAQ,UACR/H,OAAU,WACVoH,QAAW,WACXsF,kBAAqB,kBACrBC,cAAiB,kCACjBC,eAAkB,wBAClB/D,UACEE,aAAgB,gDAElBf,OACEC,IAAO,MACPE,KAAQ,SACR0E,SAAY,qBACZC,OAAU,SACV5E,MAAS,OAEX6E,oBACEL,kBAAqB,2BACrBM,uBAA0B,4BAC1BC,mBAAsB,gCACtBC,gBAAmB,yBACnBC,qBAAwB,0BACxBC,iBAAoB,8BACpBC,gBAAmB,mEACnBC,cAAiB,kEAEnBjG,QACEjB,MAAS,SACTyB,MAAS,SACTL,IAAO,MACP+F,YAAe,oBACfC,YAAe,YACfC,OAAU,YACVC,SAAY,IACZC,cAAiB,yBACjBC,YAAe,sBACfC,kBAAqB,gDACrBC,oBAAuB,iBACvBC,qBAAwB,eACxBC,cAAiB,iBACjBC,WAAc,mBACd1G,YAAe,aAOf/H,MAAM0O,aAAe1O,MAAM0O,YAAYrO,SAASsO,gBACpD3O,MAAM0O,YAAYrO,SAASsO,cAC3B7O,EAAEQ,QAAO,EAAMN,MAAM0O,YAAYrO,SAASsO,eACxCC,WAAc,kBACdC,cAAiB,oBACjBC,wBAA2B,gCAC3BC,sBAAyB,8BACzBC,eAAkB,oBAClBC,WAAc,iBACdC,UAAa,iBACbC,YAAe,gBACfC,aAAgB,iBAChBC,UAAa,aACbC,MAAS,gBACTC,YAAe,sBACfhP,MAAS,UACTC,OAAU,cAIRR,MAAM0O,aAAe1O,MAAM0O,YAAYrO,SAASmP,UACpDxP,MAAM0O,YAAYrO,SAASmP,QAC3B1P,EAAEQ,QAAO,EAAMN,MAAM0O,YAAYrO,SAASmP,SACxCjP,MAAS,UACTgI,KAAQ,UACR/H,OAAU,WACViP,OAAU,UACVC,MAAS,mBACTC,OAAU,WACVC,OAAU,KACVC,mBACEjJ,MAAS,WACTkJ,YACE3J,OAAU,SACV4J,SAAY,QACZvJ,KAAQ,SAGZwJ,kBACEpJ,MAAS,iBAEXqJ,gBACErJ,MAAS,oBAEXsJ,eACEtJ,MAAS,UAEXuJ,iBACEvJ,MAAS,uBACTwJ,SACCC,aAAgB,qBAChB3O,cAAiB,SACjBE,aAAgB,oBAChBC,YAAe,aACfyO,SAAY,kBACZC,YAAe,oBACfC,YAAe,qBAGlBC,aACE7J,MAAS,eACTwJ,SACEM,WAAc,YACdC,kBAAqB,uBACrBC,gBAAmB,qBACnBC,QAAW,YAGfC,cACElK,MAAS,mBACTwJ,SACEW,YAAe,mBACfC,WAAc,kBACdC,cAAiB,mBACjBC,SAAY,wBAGhBC,kBACEvK,MAAS,qBACTwK,YAAe,6CACfC,UAAa,gBACbC,UACEC,IAAO,iBACPpL,OAAU,SACVqL,KAAQ,QACRhL,KAAQ,OACRiL,OAAU,uBACVC,KAAQ,SAEVC,WACEC,YAAe,YACfC,SAAY,YACZC,QAAW,QACXC,WAAc,YACdC,QAAW,UACXC,WAAc,cACdC,qBAAwB,uBACxBC,kBAAqB,wBAEvBC,kBACER,YAAe,gBACfC,SAAY,gBACZC,QAAW,kBACXC,WAAc,sBACdC,QAAW,cACXC,WAAc,kBACdC,qBAAwB,2BACxBC,kBAAqB,2BACrBV,OAAU,gCAEZY,QACEf,SAAY,WACZgB,SAAY,aACZC,IAAO,MACPC,IAAO,MACPxL,MAAS,QACTqB,MAAS,SACTL,IAAO,MACPyK,cAAiB,yBACjBC,YAAe,mBACfC,YAAe,gBACfC,SAAY,eACZvB,UAAa,iBACbD,YAAe,mBACfyB,YAAe,mBAEjBC,cACEC,UAAa,kBACbC,YAAe,sBAGnBC,gBACErM,MAAS,cACTyL,QACEa,SAAY,mBACZC,WAAc,oBACdC,WAAc,WACdC,UAAa,oBACbC,QAAW,UACXC,YAAe,aACfC,MAAS,WACTC,WAAc,QACdC,OAAU,UACVC,aAAgB,kBAChBC,WAAc,kBAGlBC,oBACEC,aAAgB,6CAElBC,mBACEnN,MAAS,iBACTkN,aAAgB,4FAChBzB,QACE2B,QAAW,cACXC,OAAU,cACVC,SAAY,eAGhBC,4BACEL,aAAgB,8DAKhB9T,MAAM0O,aAAe1O,MAAM0O,YAAYrO,SAAS+T,aACpDpU,MAAM0O,YAAYrO,SAAS+T,WAC3BtU,EAAEQ,QAAO,EAAMN,MAAM0O,YAAYrO,SAAS+T,YACxCzT,cAAiB,6BACjBC,eAAkB,6BAClByT,cAAiB,oBACjBC,kBAAqB,uBACrB/T,MAAS,UACT0E,OAAU,YACVsP,aAAgB,4BAChBlP,MAAS,SACTmP,OAAU,YACVC,aAAgB,SAChB5N,IAAO,IACPC,GAAM,KACNvB,WACEC,QACEI,SAAY,eACZC,eAAkB,mBAClBF,WAAc,mBACdG,SAAY,qBAEdU,MACEf,GAAO,SACPC,IAAO,aACPa,GAAO,kBACPF,GAAO,oBAETF,QACEV,GAAM,YACNC,IAAO,gBACPU,IAAO,yBACPC,GAAM,cACNC,IAAO,yBACPC,GAAM,mBAMRvG,MAAM0O,aAAe1O,MAAM0O,YAAYrO,SAASqU,UACpD1U,MAAM0O,YAAYrO,SAASqU,QAC3B5U,EAAEQ,QAAO,EAAMN,MAAM0O,YAAYrO,SAASqU,SACxCjQ,cAAiB,8BACjBC,eAAkB,6BAClBC,YAAe,wBACfC,YAAe,yBACf+P,UAAa,aACbC,kBACEvE,aAAgB,qBAChB3O,cAAiB,UACjBE,aAAgB,oBAChBC,YAAe,aACfyO,SAAY,kBACZC,YAAe,kBACfC,YAAe,oBAEjBqE,gBAAmB,QACnBzT,KAAQ,UACR0T,QAAW,SACXC,aACEzF,MAAS,YACTC,YAAe,uBAEjByF,KAAQ,SACRC,IAAO,SACPnQ,aAAgB,kBAChBD,UAAa,iBACbqQ,YAAe,uBACfrU,OAAU,UACVsU,WAAc,gBACdzS,SAAY,mBACZ0S,OAAU,6BACVC,aACEC,UAAa,aACbnP,OAAU,SACVoP,QAAW,cACXC,UAAa,aACbzF,SAAY,QACZvJ,KAAQ,OACRsG,KAAQ,OACR2I,SAAY,cACZC,SAAY,UACZC,YAAe,oBAEjBC,sBAAyB,uBACzBC,sBAAyB,uBACzBC,OAAU,mBACVC,eACEhF,YAAe,mBACfC,WAAc,kBACdC,cAAiB,mBACjBC,SAAY,uBAEd7P,OAAU,UACV2U,MAAS,eACTC,cACEvF,WAAc,YACdC,kBAAqB,uBACrBC,gBAAmB,qBACnBC,QAAW,WAEbqF,KAAQ,WACRC,MAAS,QACTC,aACEC,KAAQ,UACRC,KAAQ,YAEVC,OAAU,kBACVC,QAAW,uBACXC,SAAY,wBACZC,aACEC,aAAgB,yBAChBC,cAAiB,yBACjBC,aAAgB,yBAChBC,cAAiB,0BAEnBC,UAAa,eACbC,SAAY,gBACZ1V,UAAa,aACb2V,WAAc,2BAIZjX,MAAM0O,aAAe1O,MAAM0O,YAAYrO,SAAS6W,OACpDlX,MAAM0O,YAAYrO,SAAS6W,KAC3BpX,EAAEQ,QAAO,EAAMN,MAAM0O,YAAYrO,SAAS6W,MACxCC,QACEC,sBAAyB,8JACzBC,4BAA+B,wEAC/BC,gBAAmB,yEAErBC,MACEC,KAAQ,SACRC,OAAU,UACVC,KAAQ,YAOR1X,MAAMC,GAAG0X,SACb3X,MAAMC,GAAG0X,OAAOxX,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0X,OAAOxX,UAAUC,SACvCwX,oBAAuB,cACvBC,oBAAuB,iBAMrB7X,MAAMC,GAAG6X,WACb9X,MAAMC,GAAG6X,SAAS3X,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6X,SAAS3X,UAAUC,QAAQC,UACjD0X,OAAU,4BACVC,QAAW,gBACXC,cAAiB,kBACjBvI,MAAS,UACT5G,UACIG,KAAQ,SACRG,OAAU,YACVL,WAAc,WACdC,OAAU,yBACVkP,YAAe,gCACftQ,QAAW,WACXsB,MAAS,sBACTzB,IAAO,wBAOTzH,MAAMC,GAAGkY,WACbnY,MAAMC,GAAGkY,SAAShY,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkY,SAAShY,UAAUC,QAAQC,UACjD2X,QAAW,gBACXC,cAAiB,mBACjBvI,MAAS,aAMP1P,MAAMC,GAAGmY,SACbpY,MAAMC,GAAGmY,OAAOjY,UAAUC,QAAQiY,aAClCvY,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmY,OAAOjY,UAAUC,QAAQiY,cAC/ClP,OAAU,0BACV3I,OAAU,WACVkP,MAAS,UACTD,OAAU,UACV6I,oBAAuB,qBACvBtT,cAAiB,sCACjBuT,gBAAmB,WACnBC,eAAkB,YAClBC,cAAiB,QACjBC,aAAgB,SAChBC,sBAAyB,cACzBC,qBAAwB,WAMtB5Y,MAAMC,GAAG4Y,YACb7Y,MAAMC,GAAG4Y,UAAU1Y,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4Y,UAAU1Y,UAAUC,QAAQC,UAClDyY,SAAY,oBACZC,QAAW,mBACXxG,IAAO,oCACPC,IAAO,oCACPwG,KAAQ,mBACRC,MAAS,4BACTC,IAAO,mCACP1S,KAAQ,4BACR2S,YAAe,oDAMbnZ,MAAMC,GAAGmZ,SACbpZ,MAAMC,GAAGmZ,OAAOjZ,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmZ,OAAOjZ,UAAUC,QAAQiY,cAC/CgB,MAAS,YAMPrZ,MAAMC,GAAGqZ,QACbtZ,MAAMC,GAAGqZ,MAAMnZ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqZ,MAAMnZ,UAAUC,QAAQiY,cAC9CzI,OAAU,QAMR5P,MAAMC,GAAGsZ,UACbvZ,MAAMC,GAAGsZ,QAAQpZ,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsZ,QAAQpZ,UAAUC,QAAQiY,cAChDzI,OAAU,KACVpP,OAAU,cAKRR,MAAMC,GAAGuZ,SACbxZ,MAAMC,GAAGuZ,OAAOrZ,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuZ,OAAOrZ,UAAUC,QAAQiY,cAC/CzI,OAAU,KACVpP,OAAU,eAITiZ,OAAOzZ,MAAM0Z", "file": "kendo.messages.pt-PT.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Aplicar\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"Aplicar\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Ordenação Ascendente\",\n  \"sortDescending\": \"Ordenação Descendente\",\n  \"filter\": \"Filtro\",\n  \"columns\": \"Colunas\",\n  \"done\": \"Feito\",\n  \"settings\": \"Definições da Coluna\",\n  \"lock\": \"Trancar\",\n  \"unlock\": \"Destrancar\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Negrito\",\n  \"italic\": \"Itálico\",\n  \"underline\": \"Sublinhado\",\n  \"strikethrough\": \"Rasurado\",\n  \"superscript\": \"Superior à linha\",\n  \"subscript\": \"Inferior à linha\",\n  \"justifyCenter\": \"Centrar texto\",\n  \"justifyLeft\": \"Alinhar texto à esquerda\",\n  \"justifyRight\": \"Alinhar texto à direita\",\n  \"justifyFull\": \"Justificar\",\n  \"insertUnorderedList\": \"Inserir lista de marcas\",\n  \"insertOrderedList\": \"Inserir lista numerada\",\n  \"indent\": \"Aumentar avanço\",\n  \"outdent\": \"Diminuir avanço\",\n  \"createLink\": \"Inserir ligação\",\n  \"unlink\": \"Remover ligação\",\n  \"insertImage\": \"Inserir imagem\",\n  \"insertFile\": \"Inserir ficheiro\",\n  \"insertHtml\": \"Inserir HTML\",\n  \"viewHtml\": \"Ver HTML\",\n  \"fontName\": \"Selecionar tipo de letra\",\n  \"fontNameInherit\": \"(tipo de letra herdado)\",\n  \"fontSize\": \"Selecionar tamanho da letra\",\n  \"fontSizeInherit\": \"(tamanho herdado)\",\n  \"formatBlock\": \"Formatar\",\n  \"formatting\": \"Formatar\",\n  \"foreColor\": \"Cor\",\n  \"backColor\": \"Cor do fundo\",\n  \"style\": \"Estilos\",\n  \"emptyFolder\": \"Pasta Vazia\",\n  \"uploadFile\": \"Submeter\",\n  \"orderBy\": \"Ordenar por:\",\n  \"orderBySize\": \"Tamanho\",\n  \"orderByName\": \"Nome\",\n  \"invalidFileType\": \"O ficheiro selecionado \\\"{0}\\\" não é válido. Tipos suportados são {1}.\",\n  \"deleteFile\": 'Tem a certeza que quer eliminar \"{0}\"?',\n  \"overwriteFile\": 'Um ficheiro com o nome \"{0}\" já existe na pasta atual. Quer substituí-lo?',\n  \"directoryNotFound\": \"Não foi encontrada nenhuma pasta com este nome.\",\n  \"imageWebAddress\": \"Endereço Web\",\n  \"imageAltText\": \"Texto alternativo\",\n  \"imageWidth\": \"Largura (px)\",\n  \"imageHeight\": \"Altura (px)\",\n  \"fileWebAddress\": \"Endereço Web\",\n  \"fileTitle\": \"Título\",\n  \"linkWebAddress\": \"Endereço Web\",\n  \"linkText\": \"Texto\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkOpenInNewWindow\": \"Abrir ligação numa nova janela\",\n  \"dialogUpdate\": \"Atualizar\",\n  \"dialogInsert\": \"Inserir\",\n  \"dialogButtonSeparator\": \"ou\",\n  \"dialogCancel\": \"Cancelar\",\n  \"createTable\": \"Criar tabela\",\n  \"addColumnLeft\": \"Adicionar coluna à esquerda\",\n  \"addColumnRight\": \"Adicionar coluna à direita\",\n  \"addRowAbove\": \"Adicionar linha acima\",\n  \"addRowBelow\": \"Adicionar linha abaixo\",\n  \"deleteRow\": \"Eliminar linha\",\n  \"deleteColumn\": \"Eliminar coluna\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Submeter\",\n  \"orderBy\": \"Ordenar por\",\n  \"orderByName\": \"Nome\",\n  \"orderBySize\": \"Tamanho\",\n  \"directoryNotFound\": \"Não foi encontrada nenhuma pasta com este nome.\",\n  \"emptyFolder\": \"Pasta Vazia\",\n  \"deleteFile\": 'Tem a certeza que quer eliminar \"{0}\"?',\n  \"invalidFileType\": \"O ficheiro selecionado \\\"{0}\\\" não é válido. Tipos suportados são {1}.\",\n  \"overwriteFile\": \"Um ficheiro com o nome \\\"{0}\\\" já existe nesta pasta. Quer substituí-lo?\",\n  \"dropFilesHere\": \"Largue um ficheiro aqui para o submeter\",\n  \"search\": \"Pesquisar\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"é verdadeiro\",\n  \"isFalse\": \"é falso\",\n  \"filter\": \"Filtrar\",\n  \"clear\": \"Limpar\",\n  \"operator\": \"Operador\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"startswith\": \"Começa com\",\n    \"contains\": \"Contém\",\n    \"doesnotcontain\": \"Não contém\",\n    \"endswith\": \"Termina com\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\",\n    \"isempty\": \"É vazio\",\n    \"isnotempty\": \"É não vazio\"\n  },\n  \"number\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"gte\": \"É maior ou igual a\",\n    \"gt\": \"É maior que\",\n    \"lte\": \"É menor ou igual a\",\n    \"lt\": \"É menor que\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  },\n  \"date\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"gte\": \"É posterior ou igual a\",\n    \"gt\": \"É posterior a\",\n    \"lte\": \"É anterior ou igual a\",\n    \"lt\": \"É anterior a\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  },\n  \"enums\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Mostrar itens com valor que:\",\n  \"title\": \"Mostrar itens com valor que\",\n  \"isTrue\": \"é verdadeiro\",\n  \"isFalse\": \"é falso\",\n  \"filter\": \"Filtrar\",\n  \"clear\": \"Limpar\",\n  \"and\": \"E\",\n  \"or\": \"OU\",\n  \"selectValue\": \"-Selecionar valor-\",\n  \"operator\": \"Operador\",\n  \"value\": \"Valor\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"startswith\": \"Começa com\",\n    \"contains\": \"Contém\",\n    \"doesnotcontain\": \"Não contém\",\n    \"endswith\": \"Termina com\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\",\n    \"isempty\": \"É vazio\",\n    \"isnotempty\": \"É não vazio\"\n  },\n  \"number\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"gte\": \"É maior ou igual a\",\n    \"gt\": \"É maior que\",\n    \"lte\": \"É menor ou igual a\",\n    \"lt\": \"É menor que\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  },\n  \"date\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"gte\": \"É posterior ou igual a\",\n    \"gt\": \"É posterior a\",\n    \"lte\": \"É anterior ou igual a\",\n    \"lt\": \"É anterior a\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  },\n  \"enums\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Selecionar Tudo\",\n  \"clear\": \"Limpar\",\n  \"filter\": \"Filtrar\",\n  \"search\": \"Pesquisar\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Adicionar Descendente\",\n    \"append\": \"Adicionar Tarefa\",\n    \"insertAfter\": \"Adicionar Abaixo\",\n    \"insertBefore\": \"Adicionar Acima\",\n    \"pdf\": \"Exportar para PDF\"\n  },\n  \"cancel\": \"Cancelar\",\n  \"deleteDependencyWindowTitle\": \"Eliminar dependência\",\n  \"deleteTaskWindowTitle\": \"Eliminar tarefa\",\n  \"destroy\": \"Eliminar\",\n  \"editor\": {\n    \"assingButton\": \"Atribuir\",\n    \"editorTitle\": \"Tarefa\",\n    \"end\": \"Fim\",\n    \"percentComplete\": \"Completo\",\n    \"resources\": \"Recursos\",\n    \"resourcesEditorTitle\": \"Recursos\",\n    \"resourcesHeader\": \"Recursos\",\n    \"start\": \"Início\",\n    \"title\": \"Título\",\n    \"unitsHeader\": \"Unidades\"\n  },\n  \"save\": \"Guardar\",\n  \"views\": {\n    \"day\": \"Dia\",\n    \"end\": \"Fim\",\n    \"month\": \"Mês\",\n    \"start\": \"Início\",\n    \"week\": \"Semana\",\n    \"year\": \"Ano\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Cancelar alterações\",\n    \"canceledit\": \"Cancelar\",\n    \"create\": \"Adicionar novo registo\",\n    \"destroy\": \"Eliminar\",\n    \"edit\": \"Editar\",\n    \"excel\": \"Exportar para Excel\",\n    \"pdf\": \"Exportar para PDF\",\n    \"save\": \"Guardar alterações\",\n    \"select\": \"Selecionar\",\n    \"update\": \"Atualizar\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Cancelar\",\n    \"confirmation\": \"Tem a certeza que pretende eliminar este registo?\",\n    \"confirmDelete\": \"Eliminar\"\n  },\n  \"noRecords\": \"Nenhum registo disponível.\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Arraste uma coluna para este espaço para agrupar pelo valor da mesma\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Aumentar valor\",\n  \"downArrowText\": \"Diminuir valor\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Tudo\",\n  \"display\": \"Registos {0} - {1} de {2}\",\n  \"empty\": \"Sem registos para apresentar.\",\n  \"page\": \"Página\",\n  \"of\": \"de {0}\",\n  \"itemsPerPage\": \"itens por página\",\n  \"first\": \"Ir para a primeira página\",\n  \"previous\": \"Ir para a página anterior\",\n  \"next\": \"Ir para a próxima página\",\n  \"last\": \"Ir para a última página\",\n  \"refresh\": \"Atualizar\",\n  \"morePages\": \"Mais páginas\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"Tudo\",\n  \"display\": \"Registos {0} - {1} de {2}\",\n  \"empty\": \"Sem registos para apresentar.\",\n  \"page\": \"Página\",\n  \"of\": \"de {0}\",\n  \"itemsPerPage\": \"itens por página\",\n  \"first\": \"Ir para a primeira página\",\n  \"previous\": \"Ir para a página anterior\",\n  \"next\": \"Ir para a próxima página\",\n  \"last\": \"Ir para a última página\",\n  \"refresh\": \"Atualizar\",\n  \"morePages\": \"Mais páginas\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"Largue campos de dados aqui\",\n  \"columnFields\": \"Largue campos de colunas aqui\",\n  \"rowFields\": \"Largue campos de linhas aqui\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"mostrar itens com valor que:\",\n  \"filterFields\": \"Filtro de Campos\",\n  \"filter\": \"Filtrar\",\n  \"include\": \"Incluir Campos...\",\n  \"title\": \"Campos a incluir\",\n  \"clear\": \"Limpar\",\n  \"ok\": \"Ok\",\n  \"cancel\": \"Cancelar\",\n  \"operators\": {\n    \"contains\": \"Contém\",\n    \"doesnotcontain\": \"Não contém\",\n    \"startswith\": \"Começa com\",\n    \"endswith\": \"Termina com\",\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Nunca\",\n    \"hourly\": \"A cada hora\",\n    \"daily\": \"Diariamente\",\n    \"weekly\": \"Semanalmente\",\n    \"monthly\": \"Mensalmente\",\n    \"yearly\": \"Anualmente\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Repetir a cada: \",\n    \"interval\": \" hora(s)\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Repetir a cada: \",\n    \"interval\": \" dia(s)\"\n  },\n  \"weekly\": {\n    \"interval\": \" semana(s)\",\n    \"repeatEvery\": \"Repetir a cada: \",\n    \"repeatOn\": \"Repetir em: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Repetir a cada: \",\n    \"repeatOn\": \"Repetir em: \",\n    \"interval\": \" mês(es)\",\n    \"day\": \"Dia \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Repetir a cada: \",\n    \"repeatOn\": \"Repetir em: \",\n    \"interval\": \" ano(s)\",\n    \"of\": \" de \"\n  },\n  \"end\": {\n    \"label\": \"Fim:\",\n    \"mobileLabel\": \"Fim\",\n    \"never\": \"Nunca\",\n    \"after\": \"Depois\",\n    \"occurrence\": \" ocorrência(s)\",\n    \"on\": \"Em \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"primeiro\",\n    \"second\": \"segundo\",\n    \"third\": \"terceiro\",\n    \"fourth\": \"quarto\",\n    \"last\": \"último\"\n  },\n  \"weekdays\": {\n    \"day\": \"dia\",\n    \"weekday\": \"semana\",\n    \"weekend\": \"fim de semana\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"todo o dia\",\n  \"date\": \"Data\",\n  \"event\": \"Evento\",\n  \"time\": \"Hora\",\n  \"showFullDay\": \"Mostrar dia completo\",\n  \"showWorkDay\": \"Mostrar horário de trabalho\",\n  \"today\": \"Hoje\",\n  \"save\": \"Guardar\",\n  \"cancel\": \"Cancelar\",\n  \"destroy\": \"Eliminar\",\n  \"deleteWindowTitle\": \"Eliminar evento\",\n  \"ariaSlotLabel\": \"Selecionado entre {0:t} e {1:t}\",\n  \"ariaEventLabel\": \"{0} em {1:D} às {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"Tem a certeza que quer eliminar este evento?\"\n  },\n  \"views\": {\n    \"day\": \"Dia\",\n    \"week\": \"Semana\",\n    \"workWeek\": \"Semana de Trabalho\",\n    \"agenda\": \"Agenda\",\n    \"month\": \"Mês\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Eliminar item recorrente\",\n    \"deleteWindowOccurrence\": \"Eliminar ocorrência atual\",\n    \"deleteWindowSeries\": \"Eliminar série de ocorrências\",\n    \"editWindowTitle\": \"Editar Item Recorrente\",\n    \"editWindowOccurrence\": \"Editar ocorrência atual\",\n    \"editWindowSeries\": \"Editar série de ocorrências\",\n    \"deleteRecurring\": \"Quer eliminar só esta ocorrência ou toda a série de ocorrências?\",\n    \"editRecurring\": \"Quer editar só esta ocorrência ou toda a série de ocorrências?\"\n  },\n  \"editor\": {\n    \"title\": \"Titulo\",\n    \"start\": \"Início\",\n    \"end\": \"Fim\",\n    \"allDayEvent\": \"Evento todo o dia\",\n    \"description\": \"Descrição\",\n    \"repeat\": \"Repetição\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"Início do fuso horário\",\n    \"endTimezone\": \"Fim do fuso horário\",\n    \"separateTimezones\": \"Usar fusos horários de início e fim separados\",\n    \"timezoneEditorTitle\": \"Fusos Horários\",\n    \"timezoneEditorButton\": \"Fuso horário\",\n    \"timezoneTitle\": \"Fusos Horários\",\n    \"noTimezone\": \"Sem fuso horário\",\n    \"editorTitle\": \"Evento\"\n  }\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"Todas as bordas\",\n  \"insideBorders\": \"Bordas interiores\",\n  \"insideHorizontalBorders\": \"Bordas interiores horizontais\",\n  \"insideVerticalBorders\": \"Bordas interiores verticais\",\n  \"outsideBorders\": \"Bordas exteriores\",\n  \"leftBorder\": \"Borda esquerda\",\n  \"topBorder\": \"Borda superior\",\n  \"rightBorder\": \"Borda direita\",\n  \"bottomBorder\": \"Borda inferior\",\n  \"noBorders\": \"Sem bordas\",\n  \"reset\": \"Reiniciar cor\",\n  \"customColor\": \"Personalizar cor...\",\n  \"apply\": \"Aplicar\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Aplicar\",\n  \"save\": \"Guardar\",\n  \"cancel\": \"Cancelar\",\n  \"remove\": \"Remover\",\n  \"retry\": \"Tentar Novamente\",\n  \"revert\": \"Reverter\",\n  \"okText\": \"OK\",\n  \"formatCellsDialog\": {\n    \"title\": \"Formatar\",\n    \"categories\": {\n      \"number\": \"Número\",\n      \"currency\": \"Moeda\",\n      \"date\": \"Data\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Tipo de Letra\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Tamanho da Letra\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Bordas\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Alinhamento de texto\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Alinhar à esquerda\",\n     \"justifyCenter\": \"Centro\",\n     \"justifyRight\": \"Alinhar à direita\",\n     \"justifyFull\": \"Justificar\",\n     \"alignTop\": \"Alinhar ao topo\",\n     \"alignMiddle\": \"Alinhar ao centro\",\n     \"alignBottom\": \"Alinhar ao fundo\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Unir Células\",\n    \"buttons\": {\n      \"mergeCells\": \"Unir tudo\",\n      \"mergeHorizontally\": \"Unir horizontalmente\",\n      \"mergeVertically\": \"Unir verticalmente\",\n      \"unmerge\": \"Desunir\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Congelar painéis\",\n    \"buttons\": {\n      \"freezePanes\": \"Congelar painéis\",\n      \"freezeRows\": \"Congelar linhas\",\n      \"freezeColumns\": \"Congelar colunas\",\n      \"unfreeze\": \"Descongelar painéis\"\n    }\n  },\n  \"validationDialog\": {\n    \"title\": \"Validação de Dados\",\n    \"hintMessage\": \"Por favor indique um valor {1} válido {0}.\",\n    \"hintTitle\": \"Validação {0}\",\n    \"criteria\": {\n      \"any\": \"Qualquer valor\",\n      \"number\": \"Número\",\n      \"text\": \"Texto\",\n      \"date\": \"Data\",\n      \"custom\": \"Personalizar Fórmula\",\n      \"list\": \"Lista\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"maior que\",\n      \"lessThan\": \"menor que\",\n      \"between\": \"entre\",\n      \"notBetween\": \"não entre\",\n      \"equalTo\": \"igual a\",\n      \"notEqualTo\": \"não igual a\",\n      \"greaterThanOrEqualTo\": \"maior que ou igual a\",\n      \"lessThanOrEqualTo\": \"menor que ou igual a\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"maior que {0}\",\n      \"lessThan\": \"menor que {0}\",\n      \"between\": \"entre {0} e {1}\",\n      \"notBetween\": \"não entre {0} e {1}\",\n      \"equalTo\": \"igual a {0}\",\n      \"notEqualTo\": \"não igual a {0}\",\n      \"greaterThanOrEqualTo\": \"maior que ou igual a {0}\",\n      \"lessThanOrEqualTo\": \"menor que ou igual a {0}\",\n      \"custom\": \"que satisfaça a fórmula: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Critério\",\n      \"comparer\": \"Comparador\",\n      \"min\": \"Min\",\n      \"max\": \"Max\",\n      \"value\": \"Valor\",\n      \"start\": \"Inicio\",\n      \"end\": \"Fim\",\n      \"onInvalidData\": \"Quando dados inválidos\",\n      \"rejectInput\": \"Rejeitar entrada\",\n      \"showWarning\": \"Mostrar aviso\",\n      \"showHint\": \"Mostrar dica\",\n      \"hintTitle\": \"Título da dica\",\n      \"hintMessage\": \"Mensagem da dica\",\n      \"ignoreBlank\": \"Ignorar brancos\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Digite o título\",\n      \"typeMessage\": \"Digite a mensagem\"\n    }\n  },\n  \"exportAsDialog\": {\n    \"title\": \"Exportar...\",\n    \"labels\": {\n      \"fileName\": \"Nome do ficheiro\",\n      \"saveAsType\": \"Guardar como tipo\",\n      \"exportArea\": \"Exportar\",\n      \"paperSize\": \"Dimensão do papel\",\n      \"margins\": \"Margens\",\n      \"orientation\": \"Orientação\",\n      \"print\": \"Imprimir\",\n      \"guidelines\": \"Guias\",\n      \"center\": \"Centrar\",\n      \"horizontally\": \"Horizontalmente\",\n      \"vertically\": \"Verticalmente\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Não pode fazer parte de uma célula unida.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Copiar e colar\",\n    \"errorMessage\": \"Estas ações não podem ser invocadas pelo menu. Por favor use antes os atalhos do teclado:\",\n    \"labels\": {\n      \"forCopy\": \"para copiar\",\n      \"forCut\": \"para cortar\",\n      \"forPaste\": \"para colar\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"Esta ação não pode ser efectuada em seleções múltiplas.\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Ordenar intervalo de A a Z\",\n  \"sortDescending\": \"Ordenar intervalo de Z a A\",\n  \"filterByValue\": \"Filtrar por valor\",\n  \"filterByCondition\": \"Filtrar por condição\",\n  \"apply\": \"Aplicar\",\n  \"search\": \"Pesquisar\",\n  \"addToCurrent\": \"Adicionar à seleção atual\",\n  \"clear\": \"Limpar\",\n  \"blanks\": \"(Brancos)\",\n  \"operatorNone\": \"Nenhum\",\n  \"and\": \"E\",\n  \"or\": \"OU\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"Texto contém\",\n      \"doesnotcontain\": \"Texto não contém\",\n      \"startswith\": \"Texto começa com\",\n      \"endswith\": \"Texto termina com\"\n    },\n    \"date\": {\n      \"eq\":  \"Data é\",\n      \"neq\": \"Data não é\",\n      \"lt\":  \"Data é anterior\",\n      \"gt\":  \"Data é posterior\"\n    },\n    \"number\": {\n      \"eq\": \"É igual a\",\n      \"neq\": \"Não é igual a\",\n      \"gte\": \"É maior que ou igual a\",\n      \"gt\": \"É maior que\",\n      \"lte\": \"É menor que ou igual a\",\n      \"lt\": \"É menor que\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Adicionar coluna à esquerda\",\n  \"addColumnRight\": \"Adicionar coluna à direita\",\n  \"addRowAbove\": \"Adicionar linha acima\",\n  \"addRowBelow\": \"Adicionar linha abaixo\",\n  \"alignment\": \"Alinhmento\",\n  \"alignmentButtons\": {\n    \"justtifyLeft\": \"Alinhar à esquerda\",\n    \"justifyCenter\": \"Centrar\",\n    \"justifyRight\": \"Alinhar à direita\",\n    \"justifyFull\": \"Justificar\",\n    \"alignTop\": \"Alinhar ao topo\",\n    \"alignMiddle\": \"Alinhar ao meio\",\n    \"alignBottom\": \"Alinhar ao fundo\"\n  },\n  \"backgroundColor\": \"Fundo\",\n  \"bold\": \"Negrito\",\n  \"borders\": \"Bordas\",\n  \"colorPicker\": {\n    \"reset\": \"Repôr cor\",\n    \"customColor\": \"Personalizar cor...\"\n  },\n  \"copy\": \"Copiar\",\n  \"cut\": \"Cortar\",\n  \"deleteColumn\": \"Eliminar coluna\",\n  \"deleteRow\": \"Eliminar linha\",\n  \"excelImport\": \"Importar de Excel...\",\n  \"filter\": \"Filtrar\",\n  \"fontFamily\": \"Tipo de letra\",\n  \"fontSize\": \"Tamanho da letra\",\n  \"format\": \"Personalizar formatação...\",\n  \"formatTypes\": {\n    \"automatic\": \"Automático\",\n    \"number\": \"Número\",\n    \"percent\": \"Percentagem\",\n    \"financial\": \"Financeiro\",\n    \"currency\": \"Moeda\",\n    \"date\": \"Data\",\n    \"time\": \"Hora\",\n    \"dateTime\": \"Data e hora\",\n    \"duration\": \"Duração\",\n    \"moreFormats\": \"Mais formatos...\"\n  },\n  \"formatDecreaseDecimal\": \"Decrementar decimais\",\n  \"formatIncreaseDecimal\": \"Incrementar decimais\",\n  \"freeze\": \"Congelar painéis\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Congelar paineis\",\n    \"freezeRows\": \"Congelar linhas\",\n    \"freezeColumns\": \"Congelar colunas\",\n    \"unfreeze\": \"Descongelar painéis\"\n  },\n  \"italic\": \"Itálico\",\n  \"merge\": \"Unir células\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Unir tudo\",\n    \"mergeHorizontally\": \"Unir horizontalmente\",\n    \"mergeVertically\": \"Unir verticalmente\",\n    \"unmerge\": \"Desunir\"\n  },\n  \"open\": \"Abrir...\",\n  \"paste\": \"Colar\",\n  \"quickAccess\": {\n    \"redo\": \"Refazer\",\n    \"undo\": \"Desfazer\"\n  },\n  \"saveAs\": \"Guardar como...\",\n  \"sortAsc\": \"Ordenação ascendente\",\n  \"sortDesc\": \"Ordenação descendente\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Ordenar folha de A a Z\",\n    \"sortSheetDesc\": \"Ordenar folha de Z a A\",\n    \"sortRangeAsc\": \"Ordenar folha de A a Z\",\n    \"sortRangeDesc\": \"Ordenar folha de Z a A\"\n  },\n  \"textColor\": \"Cor do Texto\",\n  \"textWrap\": \"Quebrar texto\",\n  \"underline\": \"Sublinhado\",\n  \"validation\": \"Validação de dados...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Não é possível inserir células devido a possível perda de dados. Escolha outra localização para inserir ou elimine os dados do final da sua folha de dados.\",\n    \"filterRangeContainingMerges\": \"Não é possível criar um filtro dentro de um intervalo contendo uniões\",\n    \"validationError\": \"O valor que indicou viola as regras de validação definidas na célula.\"\n  },\n  \"tabs\": {\n    \"home\": \"Início\",\n    \"insert\": \"Inserir\",\n    \"data\": \"Dados\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Incrementar\",\n  \"decreaseButtonTitle\": \"Decrementar\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n  \"noRows\": \"Sem registos para mostrar\",\n  \"loading\": \"A carregar...\",\n  \"requestFailed\": \"Pedido falhado.\",\n  \"retry\": \"Repetir\",\n  \"commands\": {\n      \"edit\": \"Editar\",\n      \"update\": \"Atualizar\",\n      \"canceledit\": \"Cancelar\",\n      \"create\": \"Adicionar novo registo\",\n      \"createchild\": \"Adicionar registo descendente\",\n      \"destroy\": \"Eliminar\",\n      \"excel\": \"Exportar para Excel\",\n      \"pdf\": \"Exportar para PDF\"\n  }\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"A carregar...\",\n  \"requestFailed\": \"O pedido falhou.\",\n  \"retry\": \"Repetir\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Selecionar ficheiros...\",\n  \"cancel\": \"Cancelar\",\n  \"retry\": \"Repetir\",\n  \"remove\": \"Remover\",\n  \"uploadSelectedFiles\": \"Submeter ficheiros\",\n  \"dropFilesHere\": \"largue ficheiros aqui para submeter\",\n  \"statusUploading\": \"a enviar\",\n  \"statusUploaded\": \"submetido\",\n  \"statusWarning\": \"aviso\",\n  \"statusFailed\": \"falhou\",\n  \"headerStatusUploading\": \"Enviando...\",\n  \"headerStatusUploaded\": \"Feito\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} é obrigatório\",\n  \"pattern\": \"{0} não é válido\",\n  \"min\": \"{0} deve ser maior ou igual a {1}\",\n  \"max\": \"{0} deve ser menor ou igual a {1}\",\n  \"step\": \"{0} não é válido\",\n  \"email\": \"{0} não é um email válido\",\n  \"url\": \"{0} não é um endereço web válido\",\n  \"date\": \"{0} não é uma data válida\",\n  \"dateCompare\": \"A data final deve ser posterior à data inicial\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Fechar\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}