{"version": 3, "sources": ["messages/kendo.messages.da-DK.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gte", "gt", "lte", "lt", "neq", "number", "string", "endswith", "startswith", "contains", "doesnotcontain", "enums", "FilterMenu", "FilterMultiCheck", "messages", "search", "ColumnMenu", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "Grid", "commands", "create", "destroy", "canceledit", "update", "edit", "excel", "pdf", "select", "cancel", "save", "editable", "confirmation", "cancelDelete", "confirmDelete", "Pager", "allPages", "page", "display", "empty", "refresh", "itemsPerPage", "next", "previous", "morePages", "TreeListPager", "filter", "clear", "isFalse", "isTrue", "operator", "and", "info", "title", "selectValue", "value", "or", "Groupable", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "deleteFile", "directoryNotFound", "emptyFolder", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "backColor", "foreColor", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "imageWebAddress", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "createTable", "dropFilesHere", "formatting", "viewHtml", "dialogUpdate", "insertFile", "Upload", "localization", "remove", "retry", "statusFailed", "statusUploaded", "statusUploading", "uploadSelectedFiles", "headerStatusUploaded", "headerStatusUploading", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "timeline", "showFullDay", "showWorkDay", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,aACNC,IAAO,8BACPC,GAAM,gBACNC,IAAO,uBACPC,GAAM,SACNC,IAAO,mBAETC,QACEN,GAAM,aACNC,IAAO,8BACPC,GAAM,gBACNC,IAAO,8BACPC,GAAM,gBACNC,IAAO,qBAETE,QACEC,SAAY,cACZR,GAAM,aACNK,IAAO,oBACPI,WAAc,eACdC,SAAY,aACZC,eAAkB,mBAEpBC,OACEZ,GAAM,aACNK,IAAO,sBAOPb,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,aACNC,IAAO,8BACPC,GAAM,gBACNC,IAAO,uBACPC,GAAM,SACNC,IAAO,qBAETC,QACEN,GAAM,aACNC,IAAO,8BACPC,GAAM,gBACNC,IAAO,8BACPC,GAAM,gBACNC,IAAO,qBAETE,QACEC,SAAY,cACZR,GAAM,aACNK,IAAO,oBACPI,WAAc,eACdC,SAAY,aACZC,eAAkB,mBAEpBC,OACEZ,GAAM,aACNK,IAAO,sBAOPb,MAAMC,GAAGqB,mBACbtB,MAAMC,GAAGqB,iBAAiBnB,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,iBAAiBnB,UAAUC,QAAQmB,UACzDC,OAAU,SAMRxB,MAAMC,GAAGwB,aACbzB,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQmB,UACnDG,QAAW,WACXC,cAAiB,kBACjBC,eAAkB,kBAClBC,SAAY,uBACZC,KAAQ,SACRC,KAAQ,MACRC,OAAU,YAMRhC,MAAMC,GAAGgC,mBACbjC,MAAMC,GAAGgC,iBAAiB9B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgC,iBAAiB9B,UAAUC,QAAQmB,UACzDW,OACEC,SAAY,UACZC,YAAe,gBAEjBC,KACEC,MAAS,QACTC,WAAc,gBACdC,MAAS,QACTC,MAAS,SACTC,GAAM,MACNC,YAAe,WAEjBC,aACEV,MAAS,SACTW,QAAW,WACXJ,MAAS,SACTK,OAAU,WACVC,OAAU,SAEZF,SACEG,IAAO,MACPb,SAAY,YACZC,YAAe,eACfa,SAAY,eAEdC,iBACEC,MAAS,SACTC,OAAU,SACVC,KAAQ,SACRC,OAAU,QACVC,MAAS,UAEXT,QACEV,YAAe,eACfa,SAAY,cACZd,SAAY,UAEdY,QACES,GAAM,KACNpB,YAAe,gBACfa,SAAY,cACZd,SAAY,MAEdsB,UACET,IAAO,MACPU,QAAW,SACXC,QAAW,kBAOX3D,MAAMC,GAAG2D,OACb5D,MAAMC,GAAG2D,KAAKzD,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2D,KAAKzD,UAAUC,QAAQmB,UAC7CsC,UACEC,OAAU,SACVC,QAAW,OACXC,WAAc,UACdC,OAAU,UACVC,KAAQ,UACRC,MAAS,sBACTC,IAAO,oBACPC,OAAU,OACVC,OAAU,oBACVC,KAAQ,iBAEVC,UACEC,aAAgB,uDAChBC,aAAgB,WAChBC,cAAiB,WAOjB3E,MAAMC,GAAG2E,QACb5E,MAAMC,GAAG2E,MAAMzE,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2E,MAAMzE,UAAUC,QAAQmB,UAC9CsD,SAAY,MACZC,KAAQ,OACRC,QAAW,gCACXvB,GAAM,SACNwB,MAAS,wBACTC,QAAW,UACX9B,MAAS,qBACT+B,aAAgB,gBAChB7B,KAAQ,qBACR8B,KAAQ,oBACRC,SAAY,sBACZC,UAAa,iBAMXrF,MAAMC,GAAGqF,gBACTtF,MAAMC,GAAGqF,cAAcnF,UAAUC,QAAQmB,SACzCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqF,cAAcnF,UAAUC,QAAQmB,UACtDsD,SAAY,MACZC,KAAQ,OACRC,QAAW,gCACXvB,GAAM,SACNwB,MAAS,wBACTC,QAAW,UACX9B,MAAS,qBACT+B,aAAgB,gBAChB7B,KAAQ,qBACR8B,KAAQ,oBACRC,SAAY,sBACZC,UAAa,iBAMfrF,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACnDgE,OAAU,SACVC,MAAS,UACTC,QAAW,YACXC,OAAU,WACVC,SAAY,cAMV3F,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACnDgE,OAAU,SACVK,IAAO,KACPJ,MAAS,UACTK,KAAQ,iBACRC,MAAS,iBACTC,YAAe,eACfN,QAAW,YACXC,OAAU,WACVpB,OAAU,WACVqB,SAAY,WACZK,MAAS,QACTC,GAAM,WAMJjG,MAAMC,GAAGiG,YACblG,MAAMC,GAAGiG,UAAU/F,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiG,UAAU/F,UAAUC,QAAQmB,UAClDyD,MAAS,sEAMPhF,MAAMC,GAAGkG,SACbnG,MAAMC,GAAGkG,OAAOhG,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkG,OAAOhG,UAAUC,QAAQmB,UAC/C6E,KAAQ,MACRC,WAAc,cACdC,SAAY,YACZC,gBAAmB,kBACnBC,SAAY,sBACZC,gBAAmB,uBACnBC,YAAe,iBACfC,OAAU,SACVC,WAAc,cACdC,YAAe,iBACfC,kBAAqB,sBACrBC,oBAAuB,uBACvBC,OAAU,SACVC,cAAiB,gBACjBC,YAAe,SACfC,YAAe,sBACfC,aAAgB,oBAChBC,QAAW,SACXC,cAAiB,gBACjBC,OAAU,YACVC,UAAa,gBACbC,YAAe,eACfC,UAAa,eACbC,OAAU,aACVC,WAAc,iDACdC,kBAAqB,2CACrBC,YAAe,YACfC,gBAAmB,kEACnBC,QAAW,kBACXC,YAAe,OACfC,YAAe,YACfC,cAAiB,iGACjBC,WAAc,SACdC,UAAa,iBACbC,UAAa,QACbC,sBAAyB,QACzBC,aAAgB,UAChBC,aAAgB,QAChBC,aAAgB,mBAChBC,gBAAmB,cACnBC,oBAAuB,yBACvBC,SAAY,QACZC,YAAe,UACfC,eAAkB,cAClBvH,OAAU,MACVwH,cAAiB,6BACjBC,eAAkB,2BAClBC,YAAe,sBACfC,YAAe,uBACfC,aAAgB,eAChBC,UAAa,aACbC,YAAe,cACfC,cAAiB,oCACjBC,WAAc,WACdC,SAAY,WACZC,aAAgB,UAChBC,WAAc,gBAMZ3J,MAAMC,GAAG2J,SACb5J,MAAMC,GAAG2J,OAAOzJ,UAAUC,QAAQyJ,aAClC/J,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,OAAOzJ,UAAUC,QAAQyJ,cAC/CvF,OAAU,UACViF,cAAiB,wCACjBO,OAAU,QACVC,MAAS,cACT1F,OAAU,UACV2F,aAAgB,SAChBC,eAAkB,WAClBC,gBAAmB,WACnBC,oBAAuB,eACvBC,qBAAwB,SACxBC,sBAAyB,iBAMvBrK,MAAMC,GAAGqK,YACbtK,MAAMC,GAAGqK,UAAUnK,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqK,UAAUnK,UAAUC,QAAQmB,UAClDgJ,OAAU,aACVjG,OAAU,UACVE,UACEC,aAAgB,sDAElBlE,KAAQ,OACRwD,QAAW,OACXyG,QACEC,YAAe,aACfC,YAAe,cACfC,YAAe,aACftI,IAAO,OACPuI,YAAe,gBACfC,OAAU,SACVC,kBAAqB,2CACrBC,MAAS,QACTC,cAAiB,iBACjBC,SAAY,IACZC,qBAAwB,WACxBC,oBAAuB,YACvBrF,MAAS,QACTsF,WAAc,kBAEhBC,MAAS,aACTC,oBACEC,gBAAmB,sDACnBC,uBAA0B,sBAC1BC,mBAAsB,mBACtBC,kBAAqB,gCACrBC,cAAiB,wDACjBC,qBAAwB,yBACxBC,iBAAoB,sBACpBC,gBAAmB,oCAErBvH,KAAQ,MACRwH,KAAQ,MACRC,MAAS,QACTC,OACEC,OAAU,SACVlJ,IAAO,MACPmJ,MAAS,QACTC,KAAQ,MACRC,SAAY,aACZC,SAAY,aAEdZ,kBAAqB,kBACrBa,YAAe,cACfC,YAAe,oBAMbxM,MAAMC,GAAGwM,SACbzM,MAAMC,GAAGwM,OAAOtM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwM,OAAOtM,UAAUC,QAAQyJ,cAC/C6C,MAAS,WAMP1M,MAAMC,GAAG0M,QACb3M,MAAMC,GAAG0M,MAAMxM,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0M,MAAMxM,UAAUC,QAAQyJ,cAC9C+C,OAAU,UAMR5M,MAAMC,GAAG4M,UACb7M,MAAMC,GAAG4M,QAAQ1M,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4M,QAAQ1M,UAAUC,QAAQyJ,cAChD+C,OAAU,OACVtI,OAAU,cAKRtE,MAAMC,GAAG6M,SACb9M,MAAMC,GAAG6M,OAAO3M,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6M,OAAO3M,UAAUC,QAAQyJ,cAC/C+C,OAAU,OACVtI,OAAU,eAITyI,OAAO/M,MAAMgN", "file": "kendo.messages.da-DK.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Er lig med\",\n    \"gte\": \"Er senere end eller lig med\",\n    \"gt\": \"Er senere end\",\n    \"lte\": \"Er før eller lig med\",\n    \"lt\": \"Er før\",\n    \"neq\": \"Er ikke lig med\"\n  },\n  \"number\": {\n    \"eq\": \"Er lig med\",\n    \"gte\": \"Er større end eller lig med\",\n    \"gt\": \"Er større end\",\n    \"lte\": \"Er mindre end eller lig med\",\n    \"lt\": \"Er mindre end\",\n    \"neq\": \"Er forskellig fra\"\n  },\n  \"string\": {\n    \"endswith\": \"Slutter med\",\n    \"eq\": \"Er lig med\",\n    \"neq\": \"Er forskellig fra\",\n    \"startswith\": \"Begynder med\",\n    \"contains\": \"Indeholder\",\n    \"doesnotcontain\": \"Ikke indeholder\"\n  },\n  \"enums\": {\n    \"eq\": \"Er lig med\",\n    \"neq\": \"Er ikke lig med\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Er lig med\",\n    \"gte\": \"Er senere end eller lig med\",\n    \"gt\": \"Er senere end\",\n    \"lte\": \"Er før eller lig med\",\n    \"lt\": \"Er før\",\n    \"neq\": \"Er forskellig fra\"\n  },\n  \"number\": {\n    \"eq\": \"Er lig med\",\n    \"gte\": \"Er større end eller lig med\",\n    \"gt\": \"Er større end\",\n    \"lte\": \"Er mindre end eller lig med\",\n    \"lt\": \"Er mindre end\",\n    \"neq\": \"Er forskellig fra\"\n  },\n  \"string\": {\n    \"endswith\": \"Slutter med\",\n    \"eq\": \"Er lig med\",\n    \"neq\": \"Er forskellig fra\",\n    \"startswith\": \"Begynder med\",\n    \"contains\": \"Indeholder\",\n    \"doesnotcontain\": \"Ikke indeholder\"\n  },\n  \"enums\": {\n    \"eq\": \"Er lig med\",\n    \"neq\": \"Er ikke lig med\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"Søg\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Кolonner\",\n  \"sortAscending\": \"Sorter Stigende\",\n  \"sortDescending\": \"Sorter Faldende\",\n  \"settings\": \"Kolonneindstillinger\",\n  \"done\": \"Udført\",\n  \"lock\": \"Lås\",\n  \"unlock\": \"Lås op\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"days(s)\",\n    \"repeatEvery\": \"Gentag hver:\"\n  },\n  \"end\": {\n    \"after\": \"Efter\",\n    \"occurrence\": \"forekomst(er)\",\n    \"label\": \"Slut:\",\n    \"never\": \"Aldrig\",\n    \"on\": \"Den\",\n    \"mobileLabel\": \"Slutter\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Daglig\",\n    \"monthly\": \"Månedlig\",\n    \"never\": \"Aldrig\",\n    \"weekly\": \"Ugentlig\",\n    \"yearly\": \"Årlig\"\n  },\n  \"monthly\": {\n    \"day\": \"Dag\",\n    \"interval\": \"måned(er)\",\n    \"repeatEvery\": \"Gentag hver:\",\n    \"repeatOn\": \"Gentag den:\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"første\",\n    \"fourth\": \"fjerde\",\n    \"last\": \"sidste\",\n    \"second\": \"anden\",\n    \"third\": \"tredje\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Gentag hver:\",\n    \"repeatOn\": \"Gentag den:\",\n    \"interval\": \"uge(r)\"\n  },\n  \"yearly\": {\n    \"of\": \"of\",\n    \"repeatEvery\": \"Gentag hvert:\",\n    \"repeatOn\": \"Gentag den:\",\n    \"interval\": \"år\"\n  },\n  \"weekdays\": {\n    \"day\": \"dag\",\n    \"weekday\": \"ugedag\",\n    \"weekend\": \"weekend dag\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"create\": \"Indsæt\",\n    \"destroy\": \"Slet\",\n    \"canceledit\": \"Fortryd\",\n    \"update\": \"Opdatér\",\n    \"edit\": \"Redigér\",\n    \"excel\": \"Eksportér til Excel\",\n    \"pdf\": \"Eksportér til PDF\",\n    \"select\": \"Vælg\",\n    \"cancel\": \"Fortryd ændringer\",\n    \"save\": \"Gem ændringer\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Er du sikker på, at du ønsker at slette denne række?\",\n    \"cancelDelete\": \"Annullér\",\n    \"confirmDelete\": \"Slet\"\n  }\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"page\": \"Side\",\n  \"display\": \"Viser rækker {0} - {1} af {2}\",\n  \"of\": \"af {0}\",\n  \"empty\": \"Ingen rækker at vise.\",\n  \"refresh\": \"Opdatér\",\n  \"first\": \"Gå til første side\",\n  \"itemsPerPage\": \"emner pr side\",\n  \"last\": \"Gå til sidste side\",\n  \"next\": \"Gå til næste side\",\n  \"previous\": \"Gå til forrige side\",\n  \"morePages\": \"Flere sider\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\n    kendo.ui.TreeListPager.prototype.options.messages =\n    $.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n      \"allPages\": \"All\",\n      \"page\": \"Side\",\n      \"display\": \"Viser rækker {0} - {1} af {2}\",\n      \"of\": \"af {0}\",\n      \"empty\": \"Ingen rækker at vise.\",\n      \"refresh\": \"Opdatér\",\n      \"first\": \"Gå til første side\",\n      \"itemsPerPage\": \"emner pr side\",\n      \"last\": \"Gå til sidste side\",\n      \"next\": \"Gå til næste side\",\n      \"previous\": \"Gå til forrige side\",\n      \"morePages\": \"Flere sider\"\n    });\n    }\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"filter\": \"Filter\",\n  \"clear\": \"Nulstil\",\n  \"isFalse\": \"er falskt\",\n  \"isTrue\": \"er sandt\",\n  \"operator\": \"Operatør\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"filter\": \"Filter\",\n  \"and\": \"Og\",\n  \"clear\": \"Nulstil\",\n  \"info\": \"Vis rækker som\",\n  \"title\": \"Vis rækker som\",\n  \"selectValue\": \"-Vælg værdi-\",\n  \"isFalse\": \"er falskt\",\n  \"isTrue\": \"er sandt\",\n  \"cancel\": \"Annuller\",\n  \"operator\": \"Operator\",\n  \"value\": \"Value\",\n  \"or\": \"Eller\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Træk en kolonneoverskrift herover for at gruppére på den kolonne\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Fed\",\n  \"createLink\": \"Indsæt link\",\n  \"fontName\": \"Vælg font\",\n  \"fontNameInherit\": \"(nedarvet font)\",\n  \"fontSize\": \"Vælg font størrelse\",\n  \"fontSizeInherit\": \"(nedarvet størrelse)\",\n  \"formatBlock\": \"Vælg blok type\",\n  \"indent\": \"Indryk\",\n  \"insertHtml\": \"Indsæt HTML\",\n  \"insertImage\": \"Indsæt billede\",\n  \"insertOrderedList\": \"Indsæt ordnet liste\",\n  \"insertUnorderedList\": \"Indsæt uordnet liste\",\n  \"italic\": \"Kursiv\",\n  \"justifyCenter\": \"Centrér tekst\",\n  \"justifyFull\": \"Justér\",\n  \"justifyLeft\": \"Venstrejustér tekst\",\n  \"justifyRight\": \"Højrejustér tekst\",\n  \"outdent\": \"Ryk ud\",\n  \"strikethrough\": \"Gennemstreget\",\n  \"styles\": \"Stilarter\",\n  \"subscript\": \"Sænket skrift\",\n  \"superscript\": \"Hævet skrift\",\n  \"underline\": \"Understreget\",\n  \"unlink\": \"Fjern link\",\n  \"deleteFile\": \"Er du sikker på, at du ønsker at slette \\\"{0}\\\"?\",\n  \"directoryNotFound\": \"En mappe med dette navn blev ikke fundet\",\n  \"emptyFolder\": \"Tom mappe\",\n  \"invalidFileType\": \"Den valgte fil \\\"{0}\\\" er ugyldig. Understøttede filtyper er {1}.\",\n  \"orderBy\": \"Arrangér efter:\",\n  \"orderByName\": \"Navn\",\n  \"orderBySize\": \"Størrelse\",\n  \"overwriteFile\": \"'En fil ved navn \\\"{0}\\\" eksisterer allerede i den aktuelle mappe. Ønsker du at overskrive den?\",\n  \"uploadFile\": \"Upload\",\n  \"backColor\": \"Baggrundsfarve\",\n  \"foreColor\": \"Farve\",\n  \"dialogButtonSeparator\": \"eller\",\n  \"dialogCancel\": \"Fortryd\",\n  \"dialogInsert\": \"Insæt\",\n  \"imageAltText\": \"Alternativ tekst\",\n  \"imageWebAddress\": \"Web adresse\",\n  \"linkOpenInNewWindow\": \"Åben link i nyt vindue\",\n  \"linkText\": \"Tekst\",\n  \"linkToolTip\": \"Tooltip\",\n  \"linkWebAddress\": \"Web adresse\",\n  \"search\": \"Søg\",\n  \"addColumnLeft\": \"Tilføj kolonne til venstre\",\n  \"addColumnRight\": \"Tilføj kolonne til højre\",\n  \"addRowAbove\": \"Tilføj kolonne over\",\n  \"addRowBelow\": \"Tilføj kolonne under\",\n  \"deleteColumn\": \"Slet kolonne\",\n  \"deleteRow\": \"Slet række\",\n  \"createTable\": \"Opret tabel\",\n  \"dropFilesHere\": \"træk og slip filer for at uploade\",\n  \"formatting\": \"Formatér\",\n  \"viewHtml\": \"Vis HTML\",\n  \"dialogUpdate\": \"Opdater\",\n  \"insertFile\": \"Indsæt fil\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Fortryd\",\n  \"dropFilesHere\": \"Træk filer herover for at uploade dem\",\n  \"remove\": \"Fjern\",\n  \"retry\": \"Forsøg igen\",\n  \"select\": \"Vælg...\",\n  \"statusFailed\": \"fejlet\",\n  \"statusUploaded\": \"uploadet\",\n  \"statusUploading\": \"uploader\",\n  \"uploadSelectedFiles\": \"Upload filer\",\n  \"headerStatusUploaded\": \"Færdig\",\n  \"headerStatusUploading\": \"Uploader...\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"hele dagen\",\n  \"cancel\": \"Fortryd\",\n  \"editable\": {\n    \"confirmation\": \"Er du sikker på at du vil slette denne begivenhed?\"\n  },\n  \"date\": \"Dato\",\n  \"destroy\": \"Slet\",\n  \"editor\": {\n    \"allDayEvent\": \"Hele dagen\",\n    \"description\": \"Beskrivelse\",\n    \"editorTitle\": \"Begivenhed\",\n    \"end\": \"Slut\",\n    \"endTimezone\": \"Slut tidszone\",\n    \"repeat\": \"Gentag\",\n    \"separateTimezones\": \"Brug forskellige start og slut tidszoner\",\n    \"start\": \"Start\",\n    \"startTimezone\": \"Start tidszone\",\n    \"timezone\": \" \",\n    \"timezoneEditorButton\": \"Tidszone\",\n    \"timezoneEditorTitle\": \"Tidszoner\",\n    \"title\": \"Titel\",\n    \"noTimezone\": \"Ingen tidszone\"\n  },\n  \"event\": \"Begivenhed\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Vil du kun slette denne hændelse eller hele serien?\",\n    \"deleteWindowOccurrence\": \"Slet denne hændelse\",\n    \"deleteWindowSeries\": \"Slet hele serien\",\n    \"deleteWindowTitle\": \"Slet tilbagevendende hændelse\",\n    \"editRecurring\": \"Vil du kun redigere denne hændelse eller hele serien?\",\n    \"editWindowOccurrence\": \"Rediger denne hændelse\",\n    \"editWindowSeries\": \"Rediger hele serien\",\n    \"editWindowTitle\": \"Rediger tilbagevendende hændelse\"\n  },\n  \"save\": \"Gem\",\n  \"time\": \"Tid\",\n  \"today\": \"I dag\",\n  \"views\": {\n    \"agenda\": \"Agenda\",\n    \"day\": \"Dag\",\n    \"month\": \"Måned\",\n    \"week\": \"Uge\",\n    \"workWeek\": \"Arbejdsuge\",\n    \"timeline\": \"Tidslinie\"\n  },\n  \"deleteWindowTitle\": \"Slet begivenhed\",\n  \"showFullDay\": \"Vis hel dag\",\n  \"showWorkDay\": \"Vis arbejdsdag\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization,{\n  \"close\": \"Lukke\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization,{\n  \"okText\": \"Okay\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization,{\n  \"okText\": \"Okay\",\n  \"cancel\": \"Annuller\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization,{\n  \"okText\": \"Okay\",\n  \"cancel\": \"Annuller\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}