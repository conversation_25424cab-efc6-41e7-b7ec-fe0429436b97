{"version": 3, "sources": ["messages/kendo.messages.hu-HU.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "noColor", "clearColor", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "overflowAnchor", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "cleanFormatting", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "units", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "isnullorempty", "isnotnullorempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "expandCollapseColumnHeader", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "deleteRecurringConfirmation", "deleteSeriesConfirmation", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "confirmationDialog", "text", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "colorPicker", "toolbar", "alignmentButtons", "backgroundColor", "borders", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "ListBox", "tools", "moveUp", "moveDown", "transferTo", "transferFrom", "transferAllTo", "transferAllFrom", "TreeView", "Upload", "localization", "clearSelectedFiles", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "progress", "Dialog", "close", "Calendar", "weekColumnHeader", "<PERSON><PERSON>", "Confirm", "Prompt", "DateInput", "hour", "minute", "dayperiod", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,WACTC,OAAU,QACVC,QAAW,aACXC,WAAc,iBAMZV,MAAMC,GAAGU,cACbX,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,UACpDE,MAAS,WACTC,OAAU,QACVC,QAAW,aACXC,WAAc,iBAMZV,MAAMC,GAAGW,aACbZ,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,UACnDQ,cAAiB,mBACjBC,eAAkB,oBAClBC,OAAU,SACVC,QAAW,WACXC,KAAQ,OACRC,SAAY,oBACZC,KAAQ,SACRC,OAAU,YAMRpB,MAAMC,GAAGoB,SACbrB,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,UAC/CiB,KAAQ,WACRC,OAAU,OACVC,UAAa,YACbC,cAAiB,WACjBC,YAAe,cACfC,UAAa,aACbC,cAAiB,mBACjBC,YAAe,iBACfC,aAAgB,kBAChBC,YAAe,aACfC,oBAAuB,8BACvBC,kBAAqB,4BACrBC,OAAU,mBACVC,QAAW,sBACXC,WAAc,uBACdC,OAAU,0BACVC,YAAe,gBACfC,WAAc,iBACdC,WAAc,iBACdC,SAAY,oBACZC,SAAY,sBACZC,gBAAmB,sBACnBC,SAAY,sBACZC,gBAAmB,kBACnBC,YAAe,WACfC,WAAc,WACdC,UAAa,YACbC,UAAa,aACbC,MAAS,WACTC,YAAe,aACfC,WAAc,YACdC,eAAkB,mBAClBC,QAAW,YACXC,YAAe,QACfC,YAAe,MACfC,gBAAmB,kEACnBC,WAAc,qCACdC,cAAiB,6DACjBC,kBAAqB,6BACrBC,gBAAmB,SACnBC,aAAgB,oBAChBC,WAAc,iBACdC,YAAe,gBACfC,eAAkB,SAClBC,UAAa,MACbC,eAAkB,SAClBC,SAAY,SACZC,YAAe,aACfC,oBAAuB,wBACvBC,aAAgB,YAChBC,aAAgB,WAChBC,sBAAyB,OACzBC,aAAgB,QAChBC,gBAAmB,mBACnBC,YAAe,uBACfC,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,qBACfC,YAAe,oBACfC,UAAa,cACbC,aAAgB,iBAChBC,SAAY,KACZC,YAAe,iBACfC,SAAY,QACZC,QAAW,QACXC,iBAAoB,kBACpBC,QAAW,SACXC,QAAW,YACXC,MAAS,YACTC,OAAU,WACVC,MAAS,iBACTC,YAAe,eACfC,YAAe,iBACfC,WAAc,cACdC,UAAa,WACbC,WAAc,SACdC,SAAY,cACZC,GAAM,KACNC,OAAU,UACVC,YAAe,kBACfC,gBAAmB,0BACnBC,SAAY,cACZC,0BAA6B,iCAC7BC,UAAa,iBACbC,YAAe,mBACfC,WAAc,kBACdC,aAAgB,4BAChBC,eAAkB,2CAClBC,cAAiB,6BACjBC,gBAAmB,yCACnBC,kBAAqB,gDACrBC,iBAAoB,0CACpBC,gBAAmB,2BACnBC,kBAAqB,0CACrBC,iBAAoB,4BACpBC,YAAe,mBACfrG,QAAW,WACXsG,KAAQ,QACRC,eAAkB,+BAMhBvH,MAAMC,GAAGuH,cACbxH,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,UACpD+C,WAAc,YACdE,QAAW,WACXE,YAAe,MACfD,YAAe,QACfK,kBAAqB,6BACrBT,YAAe,gBACfO,WAAc,qCACdD,gBAAmB,kEACnBE,cAAiB,6DACjB8D,cAAiB,mCACjBC,OAAU,aAMR1H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,UACnDuH,OAAU,OACVC,QAAW,QACX9G,OAAU,SACV+G,MAAS,SACTC,SAAY,aAMV/H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,UACNC,IAAO,cACPC,WAAc,WACdC,SAAY,cACZC,eAAkB,kBAClBC,SAAY,WACZC,OAAU,OACVC,UAAa,WACbC,QAAW,OACXC,WAAc,WACdC,cAAiB,eACjBC,iBAAoB,cAEtBC,QACEZ,GAAM,UACNC,IAAO,cACPY,IAAO,uBACPC,GAAM,UACNC,IAAO,sBACPC,GAAM,SACNV,OAAU,OACVC,UAAa,YAEfU,MACEjB,GAAM,UACNC,IAAO,cACPY,IAAO,mBACPC,GAAM,QACNC,IAAO,oBACPC,GAAM,SACNV,OAAU,OACVC,UAAa,YAEfW,OACElB,GAAM,UACNC,IAAO,cACPK,OAAU,OACVC,UAAa,eAObzI,MAAMC,GAAGoJ,aACbrJ,MAAMC,GAAGoJ,WAAWlJ,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoJ,WAAWlJ,UAAUC,QAAQC,UACnDiJ,KAAQ,gCACRC,MAAS,+BACT3B,OAAU,OACVC,QAAW,QACX9G,OAAU,SACV+G,MAAS,SACT0B,IAAO,KACPC,GAAM,OACNC,YAAe,cACf3B,SAAY,UACZ4B,MAAS,QACTnJ,OAAU,WAMRR,MAAMC,GAAGoJ,aACbrJ,MAAMC,GAAGoJ,WAAWlJ,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoJ,WAAWlJ,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,UACNC,IAAO,cACPC,WAAc,WACdC,SAAY,cACZC,eAAkB,kBAClBC,SAAY,WACZC,OAAU,OACVC,UAAa,WACbC,QAAW,OACXC,WAAc,WACdC,cAAiB,eACjBC,iBAAoB,cAEtBC,QACEZ,GAAM,UACNC,IAAO,cACPY,IAAO,uBACPC,GAAM,UACNC,IAAO,sBACPC,GAAM,SACNV,OAAU,OACVC,UAAa,YAEfU,MACEjB,GAAM,UACNC,IAAO,cACPY,IAAO,mBACPC,GAAM,QACNC,IAAO,oBACPC,GAAM,SACNV,OAAU,OACVC,UAAa,YAEfW,OACElB,GAAM,UACNC,IAAO,cACPK,OAAU,OACVC,UAAa,eAObzI,MAAMC,GAAG2J,mBACb5J,MAAMC,GAAG2J,iBAAiBzJ,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,iBAAiBzJ,UAAUC,QAAQC,UACzDwJ,SAAY,oBACZ/B,MAAS,SACT/G,OAAU,SACV2G,OAAU,aAMR1H,MAAMC,GAAG6J,QACb9J,MAAMC,GAAG6J,MAAM3J,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6J,MAAM3J,UAAUC,QAAQC,UAC9C0J,SACEC,SAAY,yBACZC,OAAU,qBACVC,YAAe,gBACfC,aAAgB,iBAChBC,IAAO,qBAET5J,OAAU,QACV6J,4BAA+B,mBAC/BC,sBAAyB,kBACzBC,QAAW,SACXC,QACEC,aAAgB,gBAChBC,YAAe,UACfC,IAAO,OACPC,gBAAmB,YACnBC,UAAa,cACbC,qBAAwB,cACxBC,gBAAmB,cACnBC,MAAS,UACTzB,MAAS,MACT0B,YAAe,kBAEjBC,KAAQ,SACRC,OACEC,IAAO,MACPT,IAAO,OACPU,MAAS,QACTL,MAAS,QACTM,KAAQ,MACRC,KAAQ,SAORvL,MAAMC,GAAGuL,OACbxL,MAAMC,GAAGuL,KAAKrL,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuL,KAAKrL,UAAUC,QAAQC,UAC7CoL,UACEjL,OAAU,uBACVkL,WAAc,QACdC,OAAU,UACVpB,QAAW,SACXqB,KAAQ,cACRC,MAAS,sBACTzB,IAAO,oBACPc,KAAQ,sBACRY,OAAU,YACVC,OAAU,aAEZC,UACEC,aAAgB,QAChBC,aAAgB,gCAChBC,cAAiB,UAEnBC,UAAa,6BACbC,2BAA8B,MAM5BrM,MAAMC,GAAGqM,WACbtM,MAAMC,GAAGqM,SAASnM,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqM,SAASnM,UAAUC,QAAQC,UAC/CkM,OAAU,kCACVC,QAAW,cACXC,cAAiB,sBACjBC,MAAS,OACTjB,UACIG,KAAQ,cACRG,OAAU,YACVL,WAAc,QACdC,OAAU,UACVgB,YAAe,yBACfpC,QAAW,SACXsB,MAAS,sBACTzB,IAAO,wBAOXpK,MAAMC,GAAG2M,YACb5M,MAAMC,GAAG2M,UAAUzM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,UAAUzM,UAAUC,QAAQC,UAClDwM,MAAS,oDAMP7M,MAAMC,GAAG6M,iBACb9M,MAAMC,GAAG6M,eAAe3M,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6M,eAAe3M,UAAUC,SAC/C2M,YAAe,iBACfC,cAAiB,uBAMfhN,MAAMC,GAAGgN,cACbjN,MAAMC,GAAGgN,YAAY9M,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgN,YAAY9M,UAAUC,QAAQC,UACpD6M,MAAS,SACTC,KAAQ,YACRC,KAAQ,UACRC,OAAU,oBACVC,QAAW,UACXC,WAAc,qBAMZvN,MAAMC,GAAGuN,QACbxN,MAAMC,GAAGuN,MAAMrN,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuN,MAAMrN,UAAUC,QAAQC,UAC9CoN,SAAY,SACZC,QAAW,2BACXb,MAAS,kCACTc,KAAQ,QACRC,GAAM,WACNC,aAAgB,eAChBC,MAAS,wBACTC,SAAY,yBACZC,KAAQ,4BACRC,KAAQ,0BACRC,QAAW,YACXC,UAAa,qBAMXnO,MAAMC,GAAGmO,gBACbpO,MAAMC,GAAGmO,cAAcjO,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmO,cAAcjO,UAAUC,QAAQC,UACtDoN,SAAY,SACZC,QAAW,2BACXb,MAAS,kCACTc,KAAQ,QACRC,GAAM,WACNC,aAAgB,eAChBC,MAAS,wBACTC,SAAY,yBACZC,KAAQ,4BACRC,KAAQ,0BACRC,QAAW,YACXC,UAAa,qBAMXnO,MAAMC,GAAGoO,YACbrO,MAAMC,GAAGoO,UAAUlO,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoO,UAAUlO,UAAUC,QAAQC,UAClDiO,cAAiB,4BACjBC,aAAgB,8BAChBC,UAAa,6BAMXxO,MAAMC,GAAGwO,iBACbzO,MAAMC,GAAGwO,eAAetO,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwO,eAAetO,UAAUC,QAAQC,UACvDiJ,KAAQ,gCACRoF,aAAgB,YAChB3N,OAAU,SACV4N,QAAW,uBACXpF,MAAS,qBACTzB,MAAS,SACT8G,GAAM,KACNpO,OAAU,QACVwH,WACEK,SAAY,cACZC,eAAkB,kBAClBF,WAAc,WACdG,SAAY,WACZL,GAAM,UACNC,IAAO,kBAOPnI,MAAMC,GAAG4O,mBACb7O,MAAMC,GAAG4O,iBAAiB1O,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4O,iBAAiB1O,UAAUC,QAAQC,UACzDyO,aACEC,MAAS,OACTC,OAAU,WACVC,MAAS,UACTC,OAAU,UACVC,QAAW,UACXC,OAAU,UAEZJ,QACEK,YAAe,qBACfC,SAAY,WAEdL,OACEI,YAAe,qBACfC,SAAY,YAEdJ,QACEI,SAAY,WACZD,YAAe,qBACfE,SAAY,eAEdJ,SACEE,YAAe,qBACfE,SAAY,cACZD,SAAY,aACZlE,IAAO,QAETgE,QACEC,YAAe,qBACfE,SAAY,cACZD,SAAY,UACZ1B,GAAM,UAERjD,KACE6E,MAAS,QACTC,YAAe,WACfV,MAAS,OACTW,MAAS,QACTC,WAAc,mBACdC,GAAM,WAERC,iBACE/B,MAAS,OACTgC,OAAU,UACVC,MAAS,WACTC,OAAU,WACV/B,KAAQ,UAEVgC,UACE7E,IAAO,MACP8E,QAAW,WACXC,QAAW,gBAOXnQ,MAAMC,GAAGmQ,YACbpQ,MAAMC,GAAGmQ,UAAUjQ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmQ,UAAUjQ,UAAUC,QAAQC,UAClDgQ,OAAU,YACVlH,KAAQ,QACRmH,MAAS,UACTC,KAAQ,MACRC,YAAe,sBACfC,YAAe,qBACfC,MAAS,KACTxF,KAAQ,SACR1K,OAAU,QACV+J,QAAW,SACXoG,kBAAqB,kBACrBC,cAAiB,iCACjBC,eAAkB,kBAClB7E,UACEE,aAAgB,4CAElBf,OACEC,IAAO,MACPE,KAAQ,MACRwF,SAAY,WACZC,OAAU,SACV1F,MAAS,SAEX2F,oBACEL,kBAAqB,yBACrBM,uBAA0B,kBAC1BC,mBAAsB,4BACtBC,4BAA+B,gDAC/BC,yBAA4B,qDAC5BC,gBAAmB,8BACnBC,qBAAwB,uBACxBC,iBAAoB,iCACpBC,gBAAmB,qEACnBC,cAAiB,2EAEnBjH,QACEjB,MAAS,MACTyB,MAAS,SACTL,IAAO,YACP+G,YAAe,sBACfC,YAAe,SACfC,OAAU,aACVC,SAAY,IACZC,cAAiB,mBACjBC,YAAe,sBACfC,kBAAqB,2CACrBC,oBAAuB,WACvBC,qBAAwB,UACxBC,cAAiB,WACjBC,WAAc,gBACd1H,YAAe,cAOf1K,MAAMqS,aAAerS,MAAMqS,YAAYhS,SAASiS,gBACpDtS,MAAMqS,YAAYhS,SAASiS,cAC3BxS,EAAEQ,QAAO,EAAMN,MAAMqS,YAAYhS,SAASiS,eACxCC,WAAc,iBACdC,cAAiB,kBACjBC,wBAA2B,6BAC3BC,sBAAyB,6BACzBC,eAAkB,kBAClBC,WAAc,cACdC,UAAa,gBACbC,YAAe,eACfC,aAAgB,eAChBC,UAAa,gBACbC,MAAS,uBACTC,YAAe,iBACf3S,MAAS,WACTC,OAAU,WAIRR,MAAMqS,aAAerS,MAAMqS,YAAYhS,SAAS8S,UACpDnT,MAAMqS,YAAYhS,SAAS8S,QAC3BrT,EAAEQ,QAAO,EAAMN,MAAMqS,YAAYhS,SAAS8S,SACxC5S,MAAS,WACT2K,KAAQ,SACR1K,OAAU,QACV4S,OAAU,cACV1G,MAAS,OACT2G,OAAU,gBACVC,OAAU,KACVC,mBACEhK,MAAS,WACTiK,YACE1K,OAAU,OACV2K,SAAY,UACZtK,KAAQ,UAGZuK,kBACEnK,MAAS,aAEXoK,gBACEpK,MAAS,aAEXqK,eACErK,MAAS,aAEXsK,iBACEtK,MAAS,WACTuK,SACCC,aAAgB,iBAChBnS,cAAiB,mBACjBE,aAAgB,kBAChBC,YAAe,aACfiS,SAAY,8BACZC,YAAe,8BACfC,YAAe,+BAGlBC,aACE5K,MAAS,iBACTuK,SACEM,WAAc,oBACdC,kBAAqB,yBACrBC,gBAAmB,yBACnBC,QAAW,kBAGfC,cACEjL,MAAS,uBACTuK,SACEW,YAAe,uBACfC,WAAc,kBACdC,cAAiB,qBACjBC,SAAY,uBAGhBC,oBACEC,KAAQ,uCACRvL,MAAS,oBAEXwL,kBACExL,MAAS,iBACTyL,YAAe,gDACfC,UAAa,iBACbC,UACEC,IAAO,gBACPrM,OAAU,OACVgM,KAAQ,SACR3L,KAAQ,QACRiM,OAAU,iBACVC,KAAQ,SAEVC,WACEC,YAAe,gBACfC,SAAY,eACZC,QAAW,yBACXC,WAAc,6BACdC,QAAW,UACXC,WAAc,cACdC,qBAAwB,uBACxBC,kBAAqB,uBAEvBC,kBACER,YAAe,oBACfC,SAAY,mBACZC,QAAW,uBACXC,WAAc,2BACdC,QAAW,cACXC,WAAc,kBACdC,qBAAwB,2BACxBC,kBAAqB,0BACrBV,OAAU,6BAEZY,QACEd,SAAY,WACZe,SAAY,iBACZC,IAAO,UACPC,IAAO,UACPxM,MAAS,QACTqB,MAAS,SACTL,IAAO,YACPyL,cAAiB,8BACjBC,YAAe,0BACfC,YAAe,+BACfC,SAAY,yBACZtB,UAAa,gBACbD,YAAe,mBACfwB,YAAe,yBAEjBC,cACEC,UAAa,YACbC,YAAe,iBAGnBC,gBACErN,MAAS,gBACTyM,QACEa,SAAY,UACZC,WAAc,cACdC,WAAc,aACdC,UAAa,aACbC,QAAW,SACXC,YAAe,UACfC,MAAS,YACTC,WAAc,eACdC,OAAU,UACVC,aAAgB,eAChBC,WAAc,iBAGlBC,oBACEC,aAAgB,wDAElBC,mBACEnO,MAAS,yBACTkO,aAAgB,kGAChBzB,QACE2B,QAAW,aACXC,OAAU,aACVC,SAAY,mBAGhBC,4BACEL,aAAgB,uDAKhBzX,MAAMqS,aAAerS,MAAMqS,YAAYhS,SAAS0X,aACpD/X,MAAMqS,YAAYhS,SAAS0X,WAC3BjY,EAAEQ,QAAO,EAAMN,MAAMqS,YAAYhS,SAAS0X,YACxClX,cAAiB,0BACjBC,eAAkB,0BAClBkX,cAAiB,uBACjBC,kBAAqB,0BACrB1X,MAAS,WACTmH,OAAU,UACVwQ,aAAgB,qCAChBpQ,MAAS,SACTqQ,OAAU,WACVC,aAAgB,YAChB5O,IAAO,KACPC,GAAM,OACNzB,WACEC,QACEI,SAAY,uBACZC,eAAkB,2BAClBF,WAAc,iBACdG,SAAY,iBAEdY,MACEjB,GAAO,UACPC,IAAO,cACPe,GAAO,mBACPF,GAAO,mBAETF,QACEZ,GAAM,UACNC,IAAO,cACPY,IAAO,uBACPC,GAAM,UACNC,IAAO,sBACPC,GAAM,cAMRlJ,MAAMqS,aAAerS,MAAMqS,YAAYhS,SAASgY,cACpDrY,MAAMqS,YAAYhS,SAASgY,YAC3BvY,EAAEQ,QAAO,EAAMN,MAAMqS,YAAYhS,SAASgY,aACxCpF,MAAS,uBACTC,YAAe,iBACf3S,MAAS,WACTC,OAAU,WAIRR,MAAMqS,aAAerS,MAAMqS,YAAYhS,SAASiY,UACpDtY,MAAMqS,YAAYhS,SAASiY,QAC3BxY,EAAEQ,QAAO,EAAMN,MAAMqS,YAAYhS,SAASiY,SACxCzT,cAAiB,0BACjBC,eAAkB,2BAClBC,YAAe,sBACfC,YAAe,qBACfgB,UAAa,WACbuS,kBACExE,aAAgB,iBAChBnS,cAAiB,mBACjBE,aAAgB,kBAChBC,YAAe,aACfiS,SAAY,8BACZC,YAAe,8BACfC,YAAe,8BAEjBsE,gBAAmB,cACnBlX,KAAQ,WACRmX,QAAW,YACXJ,aACEpF,MAAS,uBACTC,YAAe,kBAEjBwF,KAAQ,UACRC,IAAO,UACPzT,aAAgB,iBAChBD,UAAa,cACb2T,YAAe,0BACf7X,OAAU,SACV8X,WAAc,YACdjW,SAAY,YACZkW,OAAU,qBACVC,aACEC,UAAa,cACblQ,OAAU,OACVmQ,QAAW,WACXC,UAAa,aACbzF,SAAY,UACZtK,KAAQ,QACRoH,KAAQ,MACR4I,SAAY,YACZC,SAAY,YACZC,YAAe,qBAEjBC,sBAAyB,4BACzBC,sBAAyB,yBACzBC,OAAU,uBACVC,eACEhF,YAAe,uBACfC,WAAc,kBACdC,cAAiB,qBACjBC,SAAY,sBAEdrT,OAAU,OACVmY,MAAS,iBACTC,cACEvF,WAAc,oBACdC,kBAAqB,yBACrBC,gBAAmB,yBACnBC,QAAW,iBAEbqF,KAAQ,eACRC,MAAS,cACTC,aACEC,KAAQ,QACRC,KAAQ,eAEVC,OAAU,oBACVC,QAAW,mBACXC,SAAY,oBACZC,aACEC,aAAgB,yBAChBC,cAAiB,yBACjBC,aAAgB,0BAChBC,cAAiB,2BAEnBC,UAAa,YACbC,SAAY,2BACZlZ,UAAa,YACbmZ,WAAc,uBAIZ3a,MAAMqS,aAAerS,MAAMqS,YAAYhS,SAASua,OACpD5a,MAAMqS,YAAYhS,SAASua,KAC3B9a,EAAEQ,QAAO,EAAMN,MAAMqS,YAAYhS,SAASua,MACxCC,QACEC,sBAAyB,qIACzBC,4BAA+B,yEAC/BC,gBAAmB,4EAErBC,MACEC,KAAQ,UACRC,OAAU,WACVC,KAAQ,WAORpb,MAAMC,GAAGob,SACbrb,MAAMC,GAAGob,OAAOlb,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGob,OAAOlb,UAAUC,SACvCkb,oBAAuB,UACvBC,oBAAuB,gBAMrBvb,MAAMC,GAAGub,UACbxb,MAAMC,GAAGub,QAAQrb,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGub,QAAQrb,UAAUC,QAAQC,UAChDob,OACErI,OAAU,SACVsI,OAAU,mBACVC,SAAY,kBACZC,WAAc,WACdC,aAAgB,cAChBC,cAAiB,mBACjBC,gBAAmB,0BAOnB/b,MAAMC,GAAGqM,WACbtM,MAAMC,GAAGqM,SAASnM,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqM,SAASnM,UAAUC,QAAQC,UACjDkM,OAAU,kCACVC,QAAW,cACXC,cAAiB,qBACjBC,MAAS,OACTjB,UACIG,KAAQ,cACRG,OAAU,YACVL,WAAc,QACdC,OAAU,UACVgB,YAAe,yBACfpC,QAAW,SACXsB,MAAS,sBACTzB,IAAO,wBAOTpK,MAAMC,GAAG+b,WACbhc,MAAMC,GAAG+b,SAAS7b,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+b,SAAS7b,UAAUC,QAAQC,UACjDmM,QAAW,cACXC,cAAiB,qBACjBC,MAAS,UAMP1M,MAAMC,GAAGgc,SACbjc,MAAMC,GAAGgc,OAAO9b,UAAUC,QAAQ8b,aAClCpc,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgc,OAAO9b,UAAUC,QAAQ8b,cAC/CpQ,OAAU,eACVtL,OAAU,QACVkM,MAAS,OACT0G,OAAU,cACV+I,mBAAsB,SACtBC,oBAAuB,oBACvB3U,cAAiB,mCACjB4U,gBAAmB,YACnBC,eAAkB,YAClBC,cAAiB,WACjBC,aAAgB,aAChBC,sBAAyB,eACzBC,qBAAwB,OACxBC,mBAAsB,mBACtBC,mBAAsB,oBACtBC,qBAAwB,oCAMtB7c,MAAMC,GAAG6c,YACb9c,MAAMC,GAAG6c,UAAU3c,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6c,UAAU3c,UAAUC,QAAQC,UAClD0c,SAAY,gBACZC,QAAW,kBACX9G,IAAO,sDACPC,IAAO,qDACP8G,KAAQ,kBACRC,MAAS,wBACTC,IAAO,sBACPhU,KAAQ,wBACRiU,YAAe,yEAKbpd,MAAMC,GAAGod,WACbrd,MAAMC,GAAGod,SAAShd,SAClBP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGod,SAAShd,UAC7BmM,QAAS,iBAMTxM,MAAMC,GAAGqd,SACbtd,MAAMC,GAAGqd,OAAOnd,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqd,OAAOnd,UAAUC,QAAQ8b,cAC/CqB,MAAS,aAKPvd,MAAMC,GAAGud,WACbxd,MAAMC,GAAGud,SAASrd,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGud,SAASrd,UAAUC,QAAQC,UACjDod,iBAAoB,MAMlBzd,MAAMC,GAAGyd,QACb1d,MAAMC,GAAGyd,MAAMvd,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyd,MAAMvd,UAAUC,QAAQ8b,cAC9C5I,OAAU,QAMRtT,MAAMC,GAAG0d,UACb3d,MAAMC,GAAG0d,QAAQxd,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0d,QAAQxd,UAAUC,QAAQ8b,cAChD5I,OAAU,KACV9S,OAAU,WAKRR,MAAMC,GAAG2d,SACb5d,MAAMC,GAAG2d,OAAOzd,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2d,OAAOzd,UAAUC,QAAQ8b,cAC/C5I,OAAU,KACV9S,OAAU,WAKRR,MAAMC,GAAG4d,YACX7d,MAAMC,GAAG4d,UAAU1d,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4d,UAAU1d,UAAUC,QAAQC,UAClDkL,KAAQ,KACRF,MAAS,QACTD,IAAO,MACP8E,QAAW,cACX4N,KAAQ,OACRC,OAAU,QACVjO,OAAU,cACVkO,UAAa,YAIhBC,OAAOje,MAAMke", "file": "kendo.messages.hu-HU.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Alkalmaz\",\n  \"cancel\": \"Mégse\",\n  \"noColor\": \"nincs szín\",\n  \"clearColor\": \"Tiszta szín\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"Alkalmaz\",\n  \"cancel\": \"Mégse\",\n  \"noColor\": \"nincs sz<PERSON>\",\n  \"clearColor\": \"<PERSON><PERSON>z<PERSON> s<PERSON>\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Rendezés növekvő\",\n  \"sortDescending\": \"Rendezés csökkenő\",\n  \"filter\": \"Szűrés\",\n  \"columns\": \"Oszlopok\",\n  \"done\": \"Kész\",\n  \"settings\": \"Oszlopbeállítások\",\n  \"lock\": \"Rögzít\",\n  \"unlock\": \"Felold\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Félkövér\",\n  \"italic\": \"Dőlt\",\n  \"underline\": \"Aláhúzott\",\n  \"strikethrough\": \"Áthúzott\",\n  \"superscript\": \"Felső index\",\n  \"subscript\": \"Alsó index\",\n  \"justifyCenter\": \"Középre igazítás\",\n  \"justifyLeft\": \"Balra igazítás\",\n  \"justifyRight\": \"Jobbra igazítás\",\n  \"justifyFull\": \"Sorkizárás\",\n  \"insertUnorderedList\": \"Számozatlan lista beszúrása\",\n  \"insertOrderedList\": \"Számozott lista beszúrása\",\n  \"indent\": \"Behúzás növelése\",\n  \"outdent\": \"Behúzás csökkentése\",\n  \"createLink\": \"Hivatkozás beszúrása\",\n  \"unlink\": \"Hivatkozás eltávolítása\",\n  \"insertImage\": \"Kép beszúrása\",\n  \"insertFile\": \"Fájl beszúrása\",\n  \"insertHtml\": \"HTML beszúrása\",\n  \"viewHtml\": \"HTML megtekintése\",\n  \"fontName\": \"Betűtípus választás\",\n  \"fontNameInherit\": \"(örökölt betűtípus)\",\n  \"fontSize\": \"Betűméret választás\",\n  \"fontSizeInherit\": \"(örökölt méret)\",\n  \"formatBlock\": \"Formátum\",\n  \"formatting\": \"Formázás\",\n  \"foreColor\": \"Tintaszín\",\n  \"backColor\": \"Háttérszín\",\n  \"style\": \"Stílusok\",\n  \"emptyFolder\": \"Üres mappa\",\n  \"uploadFile\": \"Feltöltés\",\n  \"overflowAnchor\": \"További eszközök\",\n  \"orderBy\": \"Rendezés:\",\n  \"orderBySize\": \"Méret\",\n  \"orderByName\": \"Név\",\n  \"invalidFileType\": \"A fájl \\\"{0}\\\" nem megfelelő típusú. Támogatott fájltípusok: {1}.\",\n  \"deleteFile\": \"Biztos, hogy törli a fájlt: \\\"{0}\\\"?\",\n  \"overwriteFile\": \"A mappában már létezik \\\"{0}\\\" nevű fájl. Kívánja felülírni?\",\n  \"directoryNotFound\": \"Nincs ilyen nevű könyvtár.\",\n  \"imageWebAddress\": \"Webcím\",\n  \"imageAltText\": \"Alternatív szöveg\",\n  \"imageWidth\": \"Szélesség (px)\",\n  \"imageHeight\": \"Magasság (px)\",\n  \"fileWebAddress\": \"Webcím\",\n  \"fileTitle\": \"Cím\",\n  \"linkWebAddress\": \"Webcím\",\n  \"linkText\": \"Szöveg\",\n  \"linkToolTip\": \"Helyi súgó\",\n  \"linkOpenInNewWindow\": \"Megnyitás új ablakban\",\n  \"dialogUpdate\": \"Frissítés\",\n  \"dialogInsert\": \"Beszúrás\",\n  \"dialogButtonSeparator\": \"vagy\",\n  \"dialogCancel\": \"Mégse\",\n  \"cleanFormatting\": \"Formázás törlése\",\n  \"createTable\": \"Táblázat létrehozása\",\n  \"addColumnLeft\": \"Oszlop beszúrása balra\",\n  \"addColumnRight\": \"Oszlop beszúrása jobbra\",\n  \"addRowAbove\": \"Sor beszúrása fölé\",\n  \"addRowBelow\": \"Sor beszúrása alá\",\n  \"deleteRow\": \"Sor törlése\",\n  \"deleteColumn\": \"Oszlop törlése\",\n  \"dialogOk\": \"OK\",\n  \"tableWizard\": \"Tábla varázsló\",\n  \"tableTab\": \"Tábla\",\n  \"cellTab\": \"Cella\",\n  \"accessibilityTab\": \"Hozzáférhetőség\",\n  \"caption\": \"Szöveg\",\n  \"summary\": \"Összegzés\",\n  \"width\": \"Szélesség\",\n  \"height\": \"Magasság\",\n  \"units\": \"Mértékegységek\",\n  \"cellSpacing\": \"Cella térköz\",\n  \"cellPadding\": \"Cella párnázás\",\n  \"cellMargin\": \"Cella margó\",\n  \"alignment\": \"Igazítás\",\n  \"background\": \"Háttér\",\n  \"cssClass\": \"CSS osztály\",\n  \"id\": \"ID\",\n  \"border\": \"Szegély\",\n  \"borderStyle\": \"Szegély stílusa\",\n  \"collapseBorders\": \"Keretek összeomlasztása\",\n  \"wrapText\": \"Szövegtörés\",\n  \"associateCellsWithHeaders\": \"Cellák társítása a fejlécekkel\",\n  \"alignLeft\": \"Balra igazítás\",\n  \"alignCenter\": \"Középre igazítás\",\n  \"alignRight\": \"Jobbra igazítás\",\n  \"alignLeftTop\": \"Igazítás balra és felülre\",\n  \"alignCenterTop\": \"Igazítás vízszintesen középre és felülre\",\n  \"alignRightTop\": \"Igazítás jobbra és felülre\",\n  \"alignLeftMiddle\": \"Igazítás balra és függőlegesen középre\",\n  \"alignCenterMiddle\": \"Igazítás vízszintesen és függőlegesen középre\",\n  \"alignRightMiddle\": \"Igazítás jobbra és függőlegesen középre\",\n  \"alignLeftBottom\": \"Igazítás balra és alulra\",\n  \"alignCenterBottom\": \"Igazítás vízszintesen középre és alulra\",\n  \"alignRightBottom\": \"Igazítás jobbra és alulra\",\n  \"alignRemove\": \"Igazítás törlése\",\n  \"columns\": \"Oszlopok\",\n  \"rows\": \"Sorok\",\n  \"selectAllCells\": \"Összes cella kiválasztása\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Feltöltés\",\n  \"orderBy\": \"Rendezés\",\n  \"orderByName\": \"Név\",\n  \"orderBySize\": \"Méret\",\n  \"directoryNotFound\": \"Nincs ilyen nevű könyvtár.\",\n  \"emptyFolder\": \"Üres könyvtár\",\n  \"deleteFile\": \"Biztos, hogy törli a fájlt: \\\"{0}\\\"?\",\n  \"invalidFileType\": \"A fájl \\\"{0}\\\" nem megfelelő típusú. Támogatott fájltípusok: {1}.\",\n  \"overwriteFile\": \"A mappában már létezik \\\"{0}\\\" nevű fájl. Kívánja felülírni?\",\n  \"dropFilesHere\": \"Húzza ide a feltöltendő fájlokat\",\n  \"search\": \"Keresés\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"igaz\",\n  \"isFalse\": \"hamis\",\n  \"filter\": \"Szűrés\",\n  \"clear\": \"Törlés\",\n  \"operator\": \"Művelet\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"egyenlő\",\n    \"neq\": \"nem egyenlő\",\n    \"startswith\": \"kezdődik\",\n    \"contains\": \"tartalmazza\",\n    \"doesnotcontain\": \"nem tartalmazza\",\n    \"endswith\": \"végződik\",\n    \"isnull\": \"null\",\n    \"isnotnull\": \"nem null\",\n    \"isempty\": \"üres\",\n    \"isnotempty\": \"nem üres\",\n    \"isnullorempty\": \"nincs értéke\",\n    \"isnotnullorempty\": \"van értéke\"\n  },\n  \"number\": {\n    \"eq\": \"egyenlő\",\n    \"neq\": \"nem egyenlő\",\n    \"gte\": \"nagyobb vagy egyenlő\",\n    \"gt\": \"nagyobb\",\n    \"lte\": \"kisebb vagy egyenlő\",\n    \"lt\": \"kisebb\",\n    \"isnull\": \"null\",\n    \"isnotnull\": \"nem null\"\n  },\n  \"date\": {\n    \"eq\": \"egyenlő\",\n    \"neq\": \"nem egyenlő\",\n    \"gte\": \"utána vagy ekkor\",\n    \"gt\": \"utána\",\n    \"lte\": \"előtte vagy ekkor\",\n    \"lt\": \"előtte\",\n    \"isnull\": \"null\",\n    \"isnotnull\": \"nem null\"\n  },\n  \"enums\": {\n    \"eq\": \"egyenlő\",\n    \"neq\": \"nem egyenlő\",\n    \"isnull\": \"null\",\n    \"isnotnull\": \"nem null\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Megjelenítendő elemek értéke:\",\n  \"title\": \"Megjelenítendő elemek értéke\",\n  \"isTrue\": \"igaz\",\n  \"isFalse\": \"hamis\",\n  \"filter\": \"Szűrés\",\n  \"clear\": \"Törlés\",\n  \"and\": \"és\",\n  \"or\": \"vagy\",\n  \"selectValue\": \"-Válasszon-\",\n  \"operator\": \"Művelet\",\n  \"value\": \"Érték\",\n  \"cancel\": \"Mégse\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"egyenlő\",\n    \"neq\": \"nem egyenlő\",\n    \"startswith\": \"kezdődik\",\n    \"contains\": \"tartalmazza\",\n    \"doesnotcontain\": \"nem tartalmazza\",\n    \"endswith\": \"végződik\",\n    \"isnull\": \"null\",\n    \"isnotnull\": \"nem null\",\n    \"isempty\": \"üres\",\n    \"isnotempty\": \"nem üres\",\n    \"isnullorempty\": \"nincs értéke\",\n    \"isnotnullorempty\": \"van értéke\"\n  },\n  \"number\": {\n    \"eq\": \"egyenlő\",\n    \"neq\": \"nem egyenlő\",\n    \"gte\": \"nagyobb vagy egyenlő\",\n    \"gt\": \"nagyobb\",\n    \"lte\": \"kisebb vagy egyenlő\",\n    \"lt\": \"kisebb\",\n    \"isnull\": \"null\",\n    \"isnotnull\": \"nem null\"\n  },\n  \"date\": {\n    \"eq\": \"egyenlő\",\n    \"neq\": \"nem egyenlő\",\n    \"gte\": \"utána vagy ekkor\",\n    \"gt\": \"utána\",\n    \"lte\": \"előtte vagy ekkor\",\n    \"lt\": \"előtte\",\n    \"isnull\": \"null\",\n    \"isnotnull\": \"nem null\"\n  },\n  \"enums\": {\n    \"eq\": \"egyenlő\",\n    \"neq\": \"nem egyenlő\",\n    \"isnull\": \"null\",\n    \"isnotnull\": \"nem null\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Összes kijelölése\",\n  \"clear\": \"Törlés\",\n  \"filter\": \"Szűrés\",\n  \"search\": \"Keresés\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Gyermekelem hozzáadása\",\n    \"append\": \"Feladat hozzáadása\",\n    \"insertAfter\": \"Hozzáadás alá\",\n    \"insertBefore\": \"Hozzáadás fölé\",\n    \"pdf\": \"Exportálás PDF-be\"\n  },\n  \"cancel\": \"Mégse\",\n  \"deleteDependencyWindowTitle\": \"Függőség törlése\",\n  \"deleteTaskWindowTitle\": \"Feladat törlése\",\n  \"destroy\": \"Törlés\",\n  \"editor\": {\n    \"assingButton\": \"Hozzárendelés\",\n    \"editorTitle\": \"Feladat\",\n    \"end\": \"Vége\",\n    \"percentComplete\": \"Befejezés\",\n    \"resources\": \"Erőforrások\",\n    \"resourcesEditorTitle\": \"Erőforrások\",\n    \"resourcesHeader\": \"Erőforrások\",\n    \"start\": \"Indítás\",\n    \"title\": \"Cím\",\n    \"unitsHeader\": \"Mértékegységek\"\n  },\n  \"save\": \"Mentés\",\n  \"views\": {\n    \"day\": \"nap\",\n    \"end\": \"vége\",\n    \"month\": \"hónap\",\n    \"start\": \"eleje\",\n    \"week\": \"hét\",\n    \"year\": \"év\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Módosítások elvetése\",\n    \"canceledit\": \"Mégse\",\n    \"create\": \"Új elem\",\n    \"destroy\": \"Törlés\",\n    \"edit\": \"Szerkesztés\",\n    \"excel\": \"Exportálás Excel-be\",\n    \"pdf\": \"Exportálás PDF-be\",\n    \"save\": \"Módosítások mentése\",\n    \"select\": \"Választás\",\n    \"update\": \"Frissítés\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Mégse\",\n    \"confirmation\": \"Biztos, hogy törli az elemet?\",\n    \"confirmDelete\": \"Törlés\"\n  },\n  \"noRecords\": \"Nincsenek elérhető elemek.\",\n  \"expandCollapseColumnHeader\": \"\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n    \"noRows\": \"Nincsenek megjeleníthető elemek\",\n    \"loading\": \"Betöltés...\",\n    \"requestFailed\": \"A kérés sikertelen.\",\n    \"retry\": \"Újra\",\n    \"commands\": {\n        \"edit\": \"Szerkesztés\",\n        \"update\": \"Frissítés\",\n        \"canceledit\": \"Mégse\",\n        \"create\": \"Új elem\",\n        \"createchild\": \"Gyermekelem hozzáadása\",\n        \"destroy\": \"Törlés\",\n        \"excel\": \"Exportálás Excel-be\",\n        \"pdf\": \"Exportálás PDF-be\"\n    }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Húzza ide az oszlopfejlécet a csoportosításhoz\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Érték növelése\",\n  \"downArrowText\": \"Érték csökkentése\"\n});\n}\n\n/* MediaPlayer messages */\n\nif (kendo.ui.MediaPlayer) {\nkendo.ui.MediaPlayer.prototype.options.messages =\n$.extend(true, kendo.ui.MediaPlayer.prototype.options.messages,{\n  \"pause\": \"Szünet\",\n  \"play\": \"Lejátszás\",\n  \"mute\": \"Némítás\",\n  \"unmute\": \"Némítás feloldása\",\n  \"quality\": \"Minőség\",\n  \"fullscreen\": \"Teljes képernyő\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Összes\",\n  \"display\": \"{0}-{1} a(z) {2} elemből\",\n  \"empty\": \"Nincsenek megjeleníthető elemek\",\n  \"page\": \"Oldal\",\n  \"of\": \"a(z) {0}\",\n  \"itemsPerPage\": \"elem / oldal\",\n  \"first\": \"Ugrás az első oldalra\",\n  \"previous\": \"Ugrás az előző oldalra\",\n  \"next\": \"Ugrás a következő oldalra\",\n  \"last\": \"Ugrás az utolsó oldalra\",\n  \"refresh\": \"Frissítés\",\n  \"morePages\": \"További oldalak\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"Összes\",\n  \"display\": \"{0}-{1} a(z) {2} elemből\",\n  \"empty\": \"Nincsenek megjeleníthető elemek\",\n  \"page\": \"Oldal\",\n  \"of\": \"a(z) {0}\",\n  \"itemsPerPage\": \"elem / oldal\",\n  \"first\": \"Ugrás az első oldalra\",\n  \"previous\": \"Ugrás az előző oldalra\",\n  \"next\": \"Ugrás a következő oldalra\",\n  \"last\": \"Ugrás az utolsó oldalra\",\n  \"refresh\": \"Frissítés\",\n  \"morePages\": \"További oldalak\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"Húzza az adatelemeket ide\",\n  \"columnFields\": \"Húzza az oszlopelemeket ide\",\n  \"rowFields\": \"Húzza a sorelemeket ide\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"Megjelenítendő elemek értéke:\",\n  \"filterFields\": \"Mezőszűrő\",\n  \"filter\": \"Szűrés\",\n  \"include\": \"Mezők befoglalása...\",\n  \"title\": \"Befoglalandó mezők\",\n  \"clear\": \"Törlés\",\n  \"ok\": \"OK\",\n  \"cancel\": \"Mégse\",\n  \"operators\": {\n    \"contains\": \"tartalmazza\",\n    \"doesnotcontain\": \"nem tartalmazza\",\n    \"startswith\": \"kezdődik\",\n    \"endswith\": \"végződik\",\n    \"eq\": \"egyenlő\",\n    \"neq\": \"nem egyenlő\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Soha\",\n    \"hourly\": \"Óránként\",\n    \"daily\": \"Naponta\",\n    \"weekly\": \"Hetente\",\n    \"monthly\": \"Havonta\",\n    \"yearly\": \"Évente\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Ismételje minden: \",\n    \"interval\": \" óra(k)\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Ismételje minden: \",\n    \"interval\": \" nap(ok)\"\n  },\n  \"weekly\": {\n    \"interval\": \" hét(ek)\",\n    \"repeatEvery\": \"Ismételje minden: \",\n    \"repeatOn\": \"Ismételje: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Ismételje minden: \",\n    \"repeatOn\": \"Ismételje: \",\n    \"interval\": \" hónap(ok)\",\n    \"day\": \"nap \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Ismételje minden: \",\n    \"repeatOn\": \"Ismételje: \",\n    \"interval\": \" év(ek)\",\n    \"of\": \" a(z) \"\n  },\n  \"end\": {\n    \"label\": \"Vége:\",\n    \"mobileLabel\": \"Végződik\",\n    \"never\": \"Soha\",\n    \"after\": \"Után \",\n    \"occurrence\": \" előfordulás(ok)\",\n    \"on\": \"Ekkor: \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"első\",\n    \"second\": \"második\",\n    \"third\": \"harmadik\",\n    \"fourth\": \"negyedik\",\n    \"last\": \"utolsó\"\n  },\n  \"weekdays\": {\n    \"day\": \"nap\",\n    \"weekday\": \"munkanap\",\n    \"weekend\": \"pihenőnap\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"egész nap\",\n  \"date\": \"Dátum\",\n  \"event\": \"Esemény\",\n  \"time\": \"Idő\",\n  \"showFullDay\": \"Teljes nap mutatása\",\n  \"showWorkDay\": \"Munkaórák mutatása\",\n  \"today\": \"Ma\",\n  \"save\": \"Mentés\",\n  \"cancel\": \"Mégse\",\n  \"destroy\": \"Törlés\",\n  \"deleteWindowTitle\": \"Esemény törlése\",\n  \"ariaSlotLabel\": \"Kiválasztva {0:t}-tól {1:t}-ig\",\n  \"ariaEventLabel\": \"{0} {1:D} {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"Biztos, hogy törölni akarja az eseményt?\"\n  },\n  \"views\": {\n    \"day\": \"nap\",\n    \"week\": \"Hét\",\n    \"workWeek\": \"Munkahét\",\n    \"agenda\": \"Naptár\",\n    \"month\": \"Hónap\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Ismétlődő elem törlése\",\n    \"deleteWindowOccurrence\": \"Alkalom törlése\",\n    \"deleteWindowSeries\": \"Összes ismétlődés törlése\",\n    \"deleteRecurringConfirmation\": \"Biztos, hogy törölni akarja ezt az alakalmat?\",\n    \"deleteSeriesConfirmation\": \"Biztos, hogy törölni akarja az összes ismétlődést?\",\n    \"editWindowTitle\": \"Ismétlődő elem szerkesztése\",\n    \"editWindowOccurrence\": \"Alkalom szerkesztése\",\n    \"editWindowSeries\": \"Összes ismétlődés szerkesztése\",\n    \"deleteRecurring\": \"Csak ezt az alkalmat szeretné törölni, vagy az összes ismétlődést?\",\n    \"editRecurring\": \"Csak ezt az alkalmat szeretné szerkeszteni, vagy az összes ismétlődést?\"\n  },\n  \"editor\": {\n    \"title\": \"Cím\",\n    \"start\": \"Kezdés\",\n    \"end\": \"Befejezés\",\n    \"allDayEvent\": \"Egész napos esemény\",\n    \"description\": \"Leírás\",\n    \"repeat\": \"Ismétlődés\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"Kezdés időzónája\",\n    \"endTimezone\": \"Befejezés időzónája\",\n    \"separateTimezones\": \"A kezdés és a befejezés időzónája eltérő\",\n    \"timezoneEditorTitle\": \"Időzónák\",\n    \"timezoneEditorButton\": \"Időzóna\",\n    \"timezoneTitle\": \"Időzónák\",\n    \"noTimezone\": \"Nincs időzóna\",\n    \"editorTitle\": \"Esemény\"\n  }\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"Minden szegély\",\n  \"insideBorders\": \"Belső szegélyek\",\n  \"insideHorizontalBorders\": \"Belső vízszintes szegélyek\",\n  \"insideVerticalBorders\": \"Belső függőleges szegélyek\",\n  \"outsideBorders\": \"Külső szegélyek\",\n  \"leftBorder\": \"Bal szegély\",\n  \"topBorder\": \"Felső szegély\",\n  \"rightBorder\": \"Jobb szegély\",\n  \"bottomBorder\": \"Alsó szegély\",\n  \"noBorders\": \"Nincs szegély\",\n  \"reset\": \"Alapértelmezett szín\",\n  \"customColor\": \"Egyéni szín...\",\n  \"apply\": \"Alkalmaz\",\n  \"cancel\": \"Mégse\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Alkalmaz\",\n  \"save\": \"Mentés\",\n  \"cancel\": \"Mégse\",\n  \"remove\": \"Eltávolítás\",\n  \"retry\": \"Újra\",\n  \"revert\": \"Visszaállítás\",\n  \"okText\": \"OK\",\n  \"formatCellsDialog\": {\n    \"title\": \"Formázás\",\n    \"categories\": {\n      \"number\": \"Szám\",\n      \"currency\": \"Pénznem\",\n      \"date\": \"Dátum\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Betűtípus\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Betűméret\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Szegélyek\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Igazítás\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Balra igazítás\",\n     \"justifyCenter\": \"Középre igazítás\",\n     \"justifyRight\": \"Jobbra igazítás\",\n     \"justifyFull\": \"Sorkizárás\",\n     \"alignTop\": \"Függőleges igazítás felülre\",\n     \"alignMiddle\": \"Függőleges igazítás középre\",\n     \"alignBottom\": \"Függőleges igazítás alulra\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Cellaegyesítés\",\n    \"buttons\": {\n      \"mergeCells\": \"Összes egyesítése\",\n      \"mergeHorizontally\": \"Egyesítés vízszintesen\",\n      \"mergeVertically\": \"Egyesítés függőlegesen\",\n      \"unmerge\": \"Szétválasztás\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Ablaktábla rögzítése\",\n    \"buttons\": {\n      \"freezePanes\": \"Ablaktábla rögzítése\",\n      \"freezeRows\": \"Sorok rögzítése\",\n      \"freezeColumns\": \"Oszlopok rögzítése\",\n      \"unfreeze\": \"Rögzítés feloldása\"\n    }\n  },\n  \"confirmationDialog\": {\n    \"text\": \"Biztos, hogy törli ezt a munkalapot?\",\n    \"title\": \"Munkalap törlése\"\n  },\n  \"validationDialog\": {\n    \"title\": \"Adatellenőrzés\",\n    \"hintMessage\": \"Kérem, írjon be egy érvényes {0} értéket {1}.\",\n    \"hintTitle\": \"Ellenőrzés {0}\",\n    \"criteria\": {\n      \"any\": \"Bármely érték\",\n      \"number\": \"Szám\",\n      \"text\": \"Szöveg\",\n      \"date\": \"Dátum\",\n      \"custom\": \"Egyéni szabály\",\n      \"list\": \"Lista\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"nagyobb, mint\",\n      \"lessThan\": \"kisebb, mint\",\n      \"between\": \"a következők közé esik\",\n      \"notBetween\": \"nem esik a következők közé\",\n      \"equalTo\": \"egyenlő\",\n      \"notEqualTo\": \"nem egyenlő\",\n      \"greaterThanOrEqualTo\": \"nagyobb vagy egyenlő\",\n      \"lessThanOrEqualTo\": \"kisebb vagy egyenlő\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"nagyobb, mint {0}\",\n      \"lessThan\": \"kisebb, mint {0}\",\n      \"between\": \"{0} és {1} közé esik\",\n      \"notBetween\": \"nem esik {0} és {1} közé\",\n      \"equalTo\": \"egyenlő {0}\",\n      \"notEqualTo\": \"nem egyenlő {0}\",\n      \"greaterThanOrEqualTo\": \"nagyobb vagy egyenlő {0}\",\n      \"lessThanOrEqualTo\": \"kisebb vagy egyenlő {0}\",\n      \"custom\": \"megfelel a képletnek: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Feltétel\",\n      \"comparer\": \"Összehasonlító\",\n      \"min\": \"Minimum\",\n      \"max\": \"Maximum\",\n      \"value\": \"Érték\",\n      \"start\": \"Kezdés\",\n      \"end\": \"Befejezés\",\n      \"onInvalidData\": \"Érvénytelen adat beírásakor\",\n      \"rejectInput\": \"Bevitel visszautasítása\",\n      \"showWarning\": \"Figyelmeztetés megjelenítése\",\n      \"showHint\": \"Javaslat megjelenítése\",\n      \"hintTitle\": \"Javaslat címe\",\n      \"hintMessage\": \"Javaslat szövege\",\n      \"ignoreBlank\": \"Üres cellák mellőzése\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Típus cím\",\n      \"typeMessage\": \"Típus üzenet\"\n    }\n  },\n  \"exportAsDialog\": {\n    \"title\": \"Exportálás...\",\n    \"labels\": {\n      \"fileName\": \"Fájlnév\",\n      \"saveAsType\": \"Fájl típusa\",\n      \"exportArea\": \"Exportálás\",\n      \"paperSize\": \"Papírméret\",\n      \"margins\": \"Margók\",\n      \"orientation\": \"Tájolás\",\n      \"print\": \"Nyomtatás\",\n      \"guidelines\": \"Segédvonalak\",\n      \"center\": \"Középre\",\n      \"horizontally\": \"Vízszintesen\",\n      \"vertically\": \"Függőlegesen\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Nem lehet módosítani az egyesített cella egy részét.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Másolás és beillesztés\",\n    \"errorMessage\": \"Ezek a műveletek nem érhetők el a menüből. Kérem, használja a következő billentyűkombinációkat:\",\n    \"labels\": {\n      \"forCopy\": \"másoláshoz\",\n      \"forCut\": \"kivágáshoz\",\n      \"forPaste\": \"beillesztéshez\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"A művelet nem hajtható végre többes kijelölésen.\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Tartomány rendezése A-Z\",\n  \"sortDescending\": \"Tartomány rendezése Z-A\",\n  \"filterByValue\": \"Szűrés érték szerint\",\n  \"filterByCondition\": \"Szűrés feltétel alapján\",\n  \"apply\": \"Alkalmaz\",\n  \"search\": \"Keresés\",\n  \"addToCurrent\": \"Hozzáadás az aktuális kijelöléshez\",\n  \"clear\": \"Törlés\",\n  \"blanks\": \"(Üresek)\",\n  \"operatorNone\": \"Egyik sem\",\n  \"and\": \"és\",\n  \"or\": \"vagy\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"A szöveg tartalmazza\",\n      \"doesnotcontain\": \"A szöveg nem tartalmazza\",\n      \"startswith\": \"A szöveg eleje\",\n      \"endswith\": \"A szöveg vége\"\n    },\n    \"date\": {\n      \"eq\":  \"A dátum\",\n      \"neq\": \"A dátum nem\",\n      \"lt\":  \"Ezen dátum előtt\",\n      \"gt\":  \"Ezen dátum után\"\n    },\n    \"number\": {\n      \"eq\": \"egyenlő\",\n      \"neq\": \"nem egyenlő\",\n      \"gte\": \"nagyobb vagy egyenlő\",\n      \"gt\": \"nagyobb\",\n      \"lte\": \"kisebb vagy egyenlő\",\n      \"lt\": \"kisebb\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.colorPicker) {\nkendo.spreadsheet.messages.colorPicker =\n$.extend(true, kendo.spreadsheet.messages.colorPicker,{\n  \"reset\": \"Alapértelmezett szín\",\n  \"customColor\": \"Egyéni szín...\",\n  \"apply\": \"Alkalmaz\",\n  \"cancel\": \"Mégse\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Oszlop hozzáadása balra\",\n  \"addColumnRight\": \"Oszlop hozzáadása jobbra\",\n  \"addRowAbove\": \"Sor hozzáadása fölé\",\n  \"addRowBelow\": \"Sor hozzáadása alá\",\n  \"alignment\": \"Igazítás\",\n  \"alignmentButtons\": {\n    \"justtifyLeft\": \"Balra igazítás\",\n    \"justifyCenter\": \"Középre igazítás\",\n    \"justifyRight\": \"Jobbra igazítás\",\n    \"justifyFull\": \"Sorkizárás\",\n    \"alignTop\": \"Függőleges igazítás felülre\",\n    \"alignMiddle\": \"Függőleges igazítás középre\",\n    \"alignBottom\": \"Függőleges igazítás alulra\"\n  },\n  \"backgroundColor\": \"Kitöltőszín\",\n  \"bold\": \"Félkövér\",\n  \"borders\": \"Szegélyek\",\n  \"colorPicker\": {\n    \"reset\": \"Alapértelmezett szín\",\n    \"customColor\": \"Egyéni szín...\"\n  },\n  \"copy\": \"Másolás\",\n  \"cut\": \"Kivágás\",\n  \"deleteColumn\": \"Oszlop törlése\",\n  \"deleteRow\": \"Sor törlése\",\n  \"excelImport\": \"Importálás Excel-ből...\",\n  \"filter\": \"Szűrés\",\n  \"fontFamily\": \"Betűtípus\",\n  \"fontSize\": \"Betűméret\",\n  \"format\": \"Egyéni formátum...\",\n  \"formatTypes\": {\n    \"automatic\": \"Automatikus\",\n    \"number\": \"Szám\",\n    \"percent\": \"Százalék\",\n    \"financial\": \"Könyvelési\",\n    \"currency\": \"Pénznem\",\n    \"date\": \"Dátum\",\n    \"time\": \"Idő\",\n    \"dateTime\": \"Dátum-idő\",\n    \"duration\": \"Időtartam\",\n    \"moreFormats\": \"Egyéb formátum...\"\n  },\n  \"formatDecreaseDecimal\": \"Tizedeshelyek csökkentése\",\n  \"formatIncreaseDecimal\": \"Tizedeshelyek növelése\",\n  \"freeze\": \"Ablaktábla rögzítése\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Ablaktábla rögzítése\",\n    \"freezeRows\": \"Sorok rögzítése\",\n    \"freezeColumns\": \"Oszlopok rögzítése\",\n    \"unfreeze\": \"Rögzítés feloldása\"\n  },\n  \"italic\": \"Dőlt\",\n  \"merge\": \"Cellaegyesítés\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Összes egyesítése\",\n    \"mergeHorizontally\": \"Egyesítés vízszintesen\",\n    \"mergeVertically\": \"Egyesítés függőlegesen\",\n    \"unmerge\": \"Szétválasztás\"\n  },\n  \"open\": \"Megnyitás...\",\n  \"paste\": \"Beillesztés\",\n  \"quickAccess\": {\n    \"redo\": \"Mégis\",\n    \"undo\": \"Visszavonás\"\n  },\n  \"saveAs\": \"Mentés másként...\",\n  \"sortAsc\": \"Rendezés növekvő\",\n  \"sortDesc\": \"Rendezés csökkenő\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Munkalap rendezése A-Z\",\n    \"sortSheetDesc\": \"Munkalap rendezése Z-A\",\n    \"sortRangeAsc\": \"Tartomány rendezése A-Z\",\n    \"sortRangeDesc\": \"Tartomány rendezése Z-A\"\n  },\n  \"textColor\": \"Tintaszín\",\n  \"textWrap\": \"Szöveg törése több sorba\",\n  \"underline\": \"Aláhúzott\",\n  \"validation\": \"Adatellenőrzés...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Adatvesztés nélkül nem lehet a cellákat beszúrni. Válasszon másik beszúrási pozíciót, vagy törölje az adatokat a munkalap végéről.\",\n    \"filterRangeContainingMerges\": \"Nem lehet szűrőt készíteni összevonásokat tartalmazó tartományon belül\",\n    \"validationError\": \"A beírt érték megsérti a cellára beállított adatellenőrzési szabályokat.\"\n  },\n  \"tabs\": {\n    \"home\": \"Elejére\",\n    \"insert\": \"Beszúrás\",\n    \"data\": \"Adat\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Növelés\",\n  \"decreaseButtonTitle\": \"Csökkentés\"\n});\n}\n\n/* ListBox messaages */\n\nif (kendo.ui.ListBox) {\nkendo.ui.ListBox.prototype.options.messages =\n$.extend(true, kendo.ui.ListBox.prototype.options.messages,{\n  \"tools\": {\n    \"remove\": \"Törlés\",\n    \"moveUp\": \"Mozgatás felfelé\",\n    \"moveDown\": \"Mozgatás lefelé\",\n    \"transferTo\": \"Felvétel\",\n    \"transferFrom\": \"Eltávolítás\",\n    \"transferAllTo\": \"Összes felvétele\",\n    \"transferAllFrom\": \"Összes eltávolítása\"\n  }\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n  \"noRows\": \"Nincsenek megjeleníthető elemek\",\n  \"loading\": \"Betöltés...\",\n  \"requestFailed\": \"A kérés sikertelen\",\n  \"retry\": \"Újra\",\n  \"commands\": {\n      \"edit\": \"Szerkesztés\",\n      \"update\": \"Frissítés\",\n      \"canceledit\": \"Mégse\",\n      \"create\": \"Új elem\",\n      \"createchild\": \"Gyermekelem hozzáadása\",\n      \"destroy\": \"Törlés\",\n      \"excel\": \"Exportálás Excel-be\",\n      \"pdf\": \"Exportálás PDF-be\"\n  }\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Betöltés...\",\n  \"requestFailed\": \"A kérés sikertelen\",\n  \"retry\": \"Újra\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Választás...\",\n  \"cancel\": \"Mégse\",\n  \"retry\": \"Újra\",\n  \"remove\": \"Eltávolítás\",\n  \"clearSelectedFiles\": \"Törlés\",\n  \"uploadSelectedFiles\": \"Fájlok feltöltése\",\n  \"dropFilesHere\": \"Húzza ide a feltöltendő fájlokat\",\n  \"statusUploading\": \"feltöltés\",\n  \"statusUploaded\": \"feltöltve\",\n  \"statusWarning\": \"figyelem\",\n  \"statusFailed\": \"sikertelen\",\n  \"headerStatusUploading\": \"Feltöltés...\",\n  \"headerStatusUploaded\": \"Kész\",\n  \"invalidMaxFileSize\": \"A fájl túl nagy.\",\n  \"invalidMinFileSize\": \"A fájl túl kicsi.\",\n  \"invalidFileExtension\": \"A fájltípus nem engedélyezett.\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} szükséges\",\n  \"pattern\": \"{0} érvénytelen\",\n  \"min\": \"{0} nagyobb vagy egyenlő kell hogy legyen, mint {1}\",\n  \"max\": \"{0} kisebb vagy egyenlő kell hogy legyen, mint {1}\",\n  \"step\": \"{0} érvénytelen\",\n  \"email\": \"{0} érvénytelen email\",\n  \"url\": \"{0} érvénytelen URL\",\n  \"date\": \"{0} érvénytelen dátum\",\n  \"dateCompare\": \"A végdátum nagyobb vagy egyenlő kell hogy legyen, mint a kezdődátum\"\n});\n}\n\n/* kendo.ui.progress method */\nif (kendo.ui.progress) {\nkendo.ui.progress.messages =\n$.extend(true, kendo.ui.progress.messages, {\n    loading: \"Betöltés...\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Bezárás\"\n});\n}\n\n/* Calendar */\nif (kendo.ui.Calendar) {\nkendo.ui.Calendar.prototype.options.messages =\n$.extend(true, kendo.ui.Calendar.prototype.options.messages, {\n  \"weekColumnHeader\": \"\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Mégse\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Mégse\"\n});\n}\n\n/* DateInput */\nif (kendo.ui.DateInput) {\n  kendo.ui.DateInput.prototype.options.messages =\n    $.extend(true, kendo.ui.DateInput.prototype.options.messages, {\n      \"year\": \"év\",\n      \"month\": \"hónap\",\n      \"day\": \"nap\",\n      \"weekday\": \"a hét napja\",\n      \"hour\": \"órák\",\n      \"minute\": \"prcek\",\n      \"second\": \"másodpercek\",\n      \"dayperiod\": \"DE/DU\"\n    });\n}\n\n})(window.kendo.jQuery);\n}));"]}