{"version": 3, "sources": ["messages/kendo.messages.zh-TW.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "noColor", "clearColor", "previewInput", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "DateRangePicker", "startLabel", "endLabel", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "edit<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadFile", "overflowAnchor", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogOk", "dialogCancel", "cleanFormatting", "createTable", "createTableHint", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "units", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "exportAs", "import", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "isnullorempty", "isnotnullorempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "additionalValue", "additionalOperator", "logic", "FilterMultiCheck", "checkAll", "selectedItemsFormat", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteDependencyConfirmation", "deleteTaskWindowTitle", "deleteTaskConfirmation", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "GanttTimeline", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "expandCollapseColumnHeader", "groupHeader", "ungroup<PERSON>eader", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "PivotSettingTarget", "PivotConfigurator", "measures", "measures<PERSON>abel", "columnsLabel", "rowsLabel", "fieldsLabel", "RecurrenceEditor", "recurrenceEditorTitle", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "MobileRecurrenceEditor", "endTitle", "repeatTitle", "headerTitle", "patterns", "repeatBy", "dayOfMonth", "dayOfWeek", "every", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "resetSeries", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "timeline", "timelineWeek", "timelineWorkWeek", "timelineMonth", "recurrenceMessages", "resetSeriesWindowTitle", "deleteWindowOccurrence", "deleteWindowSeries", "deleteRecurringConfirmation", "deleteSeriesConfirmation", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "retry", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "confirmationDialog", "text", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "showList<PERSON><PERSON>on", "showCalendarButton", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "scale", "fit", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "rangeDisabledDialog", "incompatibleRangesDialog", "noFillDirectionDialog", "duplicateSheetNameDialog", "overflowDialog", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "linkDialog", "url", "removeLink", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "matches", "doesnotmatch", "colorPicker", "toolbar", "alignmentButtons", "backgroundColor", "borders", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "toggleGridlines", "saveAs", "sort", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "hyperlink", "view", "errors", "openUnsupported", "shiftingNonblankCells", "insertColumnWhenRowIsSelected", "insertRowWhenColumnIsSelected", "filterRangeContainingMerges", "sortRangeContainingMerges", "cantSortMultipleSelection", "cantSortNullRef", "cantSortMixedCells", "validationError", "cannotModifyDisabled", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "dragHandleTitle", "ListBox", "tools", "moveUp", "moveDown", "transferTo", "transferFrom", "transferAllTo", "transferAllFrom", "TreeList", "noRows", "loading", "requestFailed", "createchild", "TreeView", "PanelBar", "Upload", "localization", "resume", "clearSelectedFiles", "uploadSelectedFiles", "invalidFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusPaused", "headerStatusUploaded", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "Validator", "required", "pattern", "step", "email", "dateCompare", "progress", "Dialog", "close", "Calendar", "weekColumnHeader", "<PERSON><PERSON>", "Confirm", "Prompt", "DateInput", "hour", "minute", "dayperiod", "DropDownTree", "deleteTag", "singleTag", "Cha<PERSON>", "placeholder", "Switch", "checked", "unchecked", "mobile", "<PERSON><PERSON><PERSON>", "pullTemplate", "releaseTemplate", "refreshTemplate", "ListView", "loadMoreText", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YAEF,SAAWG,EAAGC,GAGNC,MAAMC,GAAGC,kBACTF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACtDE,MAAS,KACTC,OAAU,KACVC,QAAW,MACXC,WAAc,OACdC,aAAgB,cAKxBX,MAAMC,GAAGW,cACTZ,MAAMC,GAAGW,YAAYT,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGW,YAAYT,UAAUC,QAAQC,UAClDE,MAAS,KACTC,OAAU,KACVC,QAAW,MACXC,WAAc,OACdC,aAAgB,cAKxBX,MAAMC,GAAGY,aACTb,MAAMC,GAAGY,WAAWV,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGY,WAAWV,UAAUC,QAAQC,UACjDS,cAAiB,OACjBC,eAAkB,OAClBC,OAAU,KACVC,QAAW,MACXC,KAAQ,KACRC,SAAY,MACZC,KAAQ,KACRC,OAAU,QAKlBrB,MAAMC,GAAGqB,kBACTtB,MAAMC,GAAGqB,gBAAgBnB,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,gBAAgBnB,UAAUC,QAAQC,UACtDkB,WAAc,KACdC,SAAY,QAKpBxB,MAAMC,GAAGwB,SACTzB,MAAMC,GAAGwB,OAAOtB,UAAUC,QAAQC,SAC9BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwB,OAAOtB,UAAUC,QAAQC,UAC7CqB,KAAQ,KACRC,OAAU,KACVC,UAAa,KACbC,cAAiB,MACjBC,YAAe,KACfC,UAAa,KACbC,cAAiB,OACjBC,YAAe,MACfC,aAAgB,MAChBC,YAAe,OACfC,oBAAuB,SACvBC,kBAAqB,SACrBC,OAAU,OACVC,QAAW,OACXC,WAAc,OACdC,OAAU,OACVC,YAAe,OACfC,WAAc,OACdC,WAAc,SACdC,SAAY,UACZC,SAAY,QACZC,gBAAmB,SACnBC,SAAY,UACZC,gBAAmB,WACnBC,YAAe,KACfC,WAAc,KACdC,UAAa,OACbC,UAAa,QACbC,MAAS,KACTC,YAAe,QACfC,cAAiB,sBACjBC,WAAc,OACdC,eAAkB,OAClBC,QAAW,QACXC,YAAe,QACfC,YAAe,QACfC,gBAAmB,iCACnBC,WAAc,mBACdC,cAAiB,4BACjBC,kBAAqB,SACrBC,gBAAmB,SACnBC,aAAgB,SAChBC,WAAc,aACdC,YAAe,aACfC,eAAkB,QAClBC,UAAa,OACbC,eAAkB,OAClBC,SAAY,OACZC,YAAe,OACfC,oBAAuB,YACvBC,aAAgB,KAChBC,aAAgB,KAChBC,sBAAyB,IACzBC,SAAY,KACZC,aAAgB,KAChBC,gBAAmB,OACnBC,YAAe,OACfC,gBAAmB,sBACnBC,cAAiB,SACjBC,eAAkB,SAClBC,YAAe,SACfC,YAAe,SACfC,UAAa,MACbC,aAAgB,MAChBC,YAAe,OACfC,SAAY,KACZC,QAAW,MACXC,iBAAoB,OACpBC,QAAW,KACXC,QAAW,KACXC,MAAS,IACTC,OAAU,IACVC,MAAS,KACTC,YAAe,QACfC,YAAe,SACfC,WAAc,SACdC,UAAa,KACbC,WAAc,MACdC,SAAY,MACZC,GAAM,KACNC,OAAU,KACVC,YAAe,OACfC,gBAAmB,OACnBC,SAAY,OACZC,0BAA6B,WAC7BC,UAAa,MACbC,YAAe,OACfC,WAAc,MACdC,aAAgB,OAChBC,eAAkB,OAClBC,cAAiB,OACjBC,gBAAmB,OACnBC,kBAAqB,OACrBC,iBAAoB,OACpBC,gBAAmB,OACnBC,kBAAqB,OACrBC,iBAAoB,OACpBC,YAAe,OACf1G,QAAW,IACX2G,KAAQ,IACRC,eAAkB,UAClBC,SAAY,KACZC,SAAU,QAKlB/H,MAAMC,GAAG+H,cACThI,MAAMC,GAAG+H,YAAY7H,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+H,YAAY7H,UAAUC,QAAQC,UAClDoD,WAAc,OACdE,QAAW,OACXE,YAAe,QACfD,YAAe,QACfK,kBAAqB,SACrBV,YAAe,QACfQ,WAAc,mBACdD,gBAAmB,iCACnBE,cAAiB,4BACjBiE,cAAiB,aACjBC,OAAU,QAKlBlI,MAAMC,GAAGkI,aACTnI,MAAMC,GAAGkI,WAAWhI,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkI,WAAWhI,UAAUC,QAAQC,UACjD+H,OAAU,IACVC,QAAW,IACXrH,OAAU,KACVsH,MAAS,KACTC,SAAY,SAKpBvI,MAAMC,GAAGkI,aACTnI,MAAMC,GAAGkI,WAAWhI,UAAUC,QAAQoI,UAClC1I,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkI,WAAWhI,UAAUC,QAAQoI,WACjDC,QACIC,GAAM,KACNC,IAAO,MACPC,WAAc,MACdC,SAAY,KACZC,eAAkB,KAClBC,SAAY,MACZC,OAAU,KACVC,UAAa,KACbC,QAAW,MACXC,WAAc,OACdC,cAAiB,KACjBC,iBAAoB,MAExBC,QACIZ,GAAM,KACNC,IAAO,MACPY,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,KACNV,OAAU,KACVC,UAAa,MAEjBU,MACIjB,GAAM,KACNC,IAAO,MACPY,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,KACNV,OAAU,KACVC,UAAa,MAEjBW,OACIlB,GAAM,KACNC,IAAO,MACPK,OAAU,KACVC,UAAa,SAMzBjJ,MAAMC,GAAG4J,aACT7J,MAAMC,GAAG4J,WAAW1J,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4J,WAAW1J,UAAUC,QAAQC,UACjDyJ,KAAQ,QACRC,MAAS,QACT3B,OAAU,IACVC,QAAW,IACXrH,OAAU,KACVsH,MAAS,KACT0B,IAAO,KACPC,GAAM,KACNC,YAAe,YACf3B,SAAY,MACZ4B,MAAS,IACTC,gBAAmB,MACnBC,mBAAsB,OACtBC,MAAS,OACT9J,OAAU,QAKlBR,MAAMC,GAAG4J,aACT7J,MAAMC,GAAG4J,WAAW1J,UAAUC,QAAQoI,UAClC1I,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4J,WAAW1J,UAAUC,QAAQoI,WACjDC,QACIC,GAAM,KACNC,IAAO,MACPC,WAAc,MACdC,SAAY,KACZC,eAAkB,KAClBC,SAAY,MACZC,OAAU,KACVC,UAAa,KACbC,QAAW,MACXC,WAAc,OACdC,cAAiB,KACjBC,iBAAoB,MAExBC,QACIZ,GAAM,KACNC,IAAO,MACPY,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,KACNV,OAAU,KACVC,UAAa,MAEjBU,MACIjB,GAAM,KACNC,IAAO,MACPY,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,KACNV,OAAU,KACVC,UAAa,MAEjBW,OACIlB,GAAM,KACNC,IAAO,MACPK,OAAU,KACVC,UAAa,SAMzBjJ,MAAMC,GAAGsK,mBACTvK,MAAMC,GAAGsK,iBAAiBpK,UAAUC,QAAQC,SACxCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsK,iBAAiBpK,UAAUC,QAAQC,UACvDmK,SAAY,KACZlC,MAAS,KACTtH,OAAU,KACVkH,OAAU,KACV1H,OAAU,KACViK,oBAAuB,iBAK/BzK,MAAMC,GAAGyK,QACT1K,MAAMC,GAAGyK,MAAMvK,UAAUC,QAAQC,SAC7BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyK,MAAMvK,UAAUC,QAAQC,UAC5CsK,SACIC,SAAY,QACZC,OAAU,OACVC,YAAe,QACfC,aAAgB,QAChBC,IAAO,UAEXxK,OAAU,KACVyK,4BAA+B,SAC/BC,6BAAgC,iBAChCC,sBAAyB,OACzBC,uBAA0B,eAC1BC,QAAW,KACXC,QACIC,aAAgB,OAChBC,YAAe,OACfC,IAAO,OACPC,gBAAmB,OACnBC,UAAa,KACbC,qBAAwB,OACxBC,gBAAmB,OACnBC,MAAS,OACT/B,MAAS,OACTgC,YAAe,OAEnBC,KAAQ,KACRC,OACIC,IAAO,MACPT,IAAO,OACPU,MAAS,MACTL,MAAS,OACTM,KAAQ,MACRC,KAAQ,UAMpBrM,MAAMC,GAAGqM,gBACTtM,MAAMC,GAAGqM,cAAcnM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqM,cAAcnM,UAAUC,QAAQC,UACpD4L,OACIC,IAAO,MACPE,KAAQ,MACRD,MAAS,MACTE,KAAQ,MACRP,MAAS,OACTL,IAAO,WAMnBzL,MAAMC,GAAGsM,OACTvM,MAAMC,GAAGsM,KAAKpM,UAAUC,QAAQC,SAC5BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsM,KAAKpM,UAAUC,QAAQC,UAC3CmM,UACIhM,OAAU,KACViM,WAAc,KACdC,OAAU,KACVrB,QAAW,KACXsB,KAAQ,KACRC,MAAS,WACT5B,IAAO,SACPgB,KAAQ,KACRa,OAAU,KACVC,OAAU,MAEdC,UACIC,aAAgB,OAChBC,aAAgB,eAChBC,cAAiB,QAErBC,UAAa,QACbC,2BAA8B,GAC9BC,YAAe,mBACfC,cAAiB,sBAKzBtN,MAAMC,GAAGsN,YACTvN,MAAMC,GAAGsN,UAAUpN,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsN,UAAUpN,UAAUC,QAAQC,UAChDmN,MAAS,2BAKjBxN,MAAMC,GAAGwN,iBACTzN,MAAMC,GAAGwN,eAAetN,UAAUC,QAC9BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwN,eAAetN,UAAUC,SAC7CsN,YAAe,KACfC,cAAiB,QAKzB3N,MAAMC,GAAG2N,cACT5N,MAAMC,GAAG2N,YAAYzN,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2N,YAAYzN,UAAUC,QAAQC,UAClDwN,MAAS,KACTC,KAAQ,KACRC,KAAQ,KACRC,OAAU,OACVC,QAAW,KACXC,WAAc,QAKtBlO,MAAMC,GAAGkO,QACTnO,MAAMC,GAAGkO,MAAMhO,UAAUC,QAAQC,SAC7BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkO,MAAMhO,UAAUC,QAAQC,UAC5C+N,SAAY,KACZC,QAAW,wBACXb,MAAS,QACTc,KAAQ,MACRC,GAAM,YACNC,aAAgB,MAChBC,MAAS,KACTC,SAAY,MACZC,KAAQ,MACRC,KAAQ,KACRC,QAAW,KACXC,UAAa,WAKrB9O,MAAMC,GAAG8O,gBACT/O,MAAMC,GAAG8O,cAAc5O,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8O,cAAc5O,UAAUC,QAAQC,UACpD+N,SAAY,KACZC,QAAW,wBACXb,MAAS,QACTc,KAAQ,MACRC,GAAM,YACNC,aAAgB,MAChBC,MAAS,KACTC,SAAY,MACZC,KAAQ,MACRC,KAAQ,KACRC,QAAW,KACXC,UAAa,WAKrB9O,MAAMC,GAAG+O,YACThP,MAAMC,GAAG+O,UAAU7O,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+O,UAAU7O,UAAUC,QAAQC,UAChD4O,cAAiB,YACjBC,aAAgB,WAChBC,UAAa,cAKrBnP,MAAMC,GAAGmP,iBACTpP,MAAMC,GAAGmP,eAAejP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmP,eAAejP,UAAUC,QAAQC,UACrDyJ,KAAQ,QACRhJ,cAAiB,OACjBC,eAAkB,OAClBsO,aAAgB,OAChBrO,OAAU,KACVsO,QAAW,UACXvF,MAAS,QACTzB,MAAS,KACTiH,GAAM,KACN/O,OAAU,KACVgI,WACIK,SAAY,KACZC,eAAkB,KAClBF,WAAc,MACdG,SAAY,MACZL,GAAM,KACNC,IAAO,UAMnB3I,MAAMC,GAAGuP,qBACTxP,MAAMC,GAAGuP,mBAAmBrP,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuP,mBAAmBrP,UAAUC,QAAQC,UACzDmN,MAAS,aAKjBxN,MAAMC,GAAGwP,oBACTzP,MAAMC,GAAGwP,kBAAkBtP,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwP,kBAAkBtP,UAAUC,QAAQC,UACxDqP,SAAY,YACZzO,QAAW,WACX2G,KAAQ,WACR+H,cAAiB,IACjBC,aAAgB,IAChBC,UAAa,IACbC,YAAe,QAKvB9P,MAAMC,GAAG8P,mBACT/P,MAAMC,GAAG8P,iBAAiB5P,UAAUC,QAAQC,SACxCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8P,iBAAiB5P,UAAUC,QAAQC,UACvD2P,sBAAyB,WACzBC,aACIC,MAAS,KACTC,OAAU,MACVC,MAAS,KACTC,OAAU,KACVC,QAAW,KACXC,OAAU,MAEdJ,QACIK,YAAe,KACfC,SAAY,OAEhBL,OACII,YAAe,KACfC,SAAY,MAEhBJ,QACIG,YAAe,KACfE,SAAY,MACZD,SAAY,MAEhBH,SACIE,YAAe,KACfE,SAAY,MACZD,SAAY,KACZvE,IAAO,OAEXqE,QACIC,YAAe,KACfE,SAAY,MACZD,SAAY,KACZlC,GAAM,OAEV9C,KACIkF,MAAS,KACTC,YAAe,KACfV,MAAS,KACTW,MAAS,MACTC,WAAc,MACdC,GAAM,OAEVC,iBACIvC,MAAS,KACTwC,OAAU,KACVC,MAAS,KACTC,OAAU,KACVvC,KAAQ,OAEZwC,UACIlF,IAAO,IACPmF,QAAW,MACXC,QAAW,SAMvBtR,MAAMC,GAAGsR,yBACTvR,MAAMC,GAAGsR,uBAAuBpR,UAAUC,QAAQC,SAC9CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsR,uBAAuBpR,UAAUC,QAAQC,UAC7DG,OAAU,KACVsM,OAAU,KACV0E,SAAY,OACZC,YAAe,OACfC,YAAe,OACfjG,KACIkG,UACIzB,MAAS,KACTW,MAAS,QACTE,GAAM,SAEVb,MAAS,KACTW,MAAS,QACTE,GAAM,SAEVX,OACIK,SAAY,IAEhBN,QACIM,SAAY,IAEhBJ,QACII,SAAY,IAEhBH,SACIG,SAAY,GACZmB,SAAY,OACZC,WAAc,KACdC,UAAa,KACbtB,YAAe,OACfuB,MAAS,IACT7F,IAAO,KAEXqE,QACIE,SAAY,GACZmB,SAAY,OACZC,WAAc,KACdC,UAAa,KACbtB,YAAe,OACfuB,MAAS,IACT5F,MAAS,IACTD,IAAO,QAMnBlM,MAAMC,GAAG+R,YACThS,MAAMC,GAAG+R,UAAU7R,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+R,UAAU7R,UAAUC,QAAQC,UAChD4R,OAAU,KACVtI,KAAQ,KACRuI,MAAS,KACTC,KAAQ,KACRC,YAAe,OACfC,YAAe,SACfC,MAAS,KACTtH,IAAO,SACPgB,KAAQ,KACRxL,OAAU,KACV6K,QAAW,KACXkH,YAAe,SACfC,kBAAqB,OACrB7D,KAAQ,KACRD,SAAY,KACZ+D,cAAiB,sBACjBC,eAAkB,sBAClB3F,UACIE,aAAgB,gBAEpBhB,OACIC,IAAO,MACPE,KAAQ,MACRuG,SAAY,QACZC,OAAU,OACVzG,MAAS,MACT0G,SAAY,MACZC,aAAgB,SAChBC,iBAAoB,WACpBC,cAAiB,UAErBC,oBACIT,kBAAqB,WACrBU,uBAA0B,SAC1BC,uBAA0B,SAC1BC,mBAAsB,WACtBC,4BAA+B,cAC/BC,yBAA4B,gBAC5BC,gBAAmB,WACnBC,qBAAwB,SACxBC,iBAAoB,WACpBC,gBAAmB,qBACnBC,cAAiB,sBAErBrI,QACIvB,MAAS,OACT+B,MAAS,OACTL,IAAO,OACPmI,YAAe,OACfC,YAAe,KACfC,OAAU,KACVC,SAAY,KACZC,cAAiB,OACjBC,YAAe,OACfC,kBAAqB,eACrBC,oBAAuB,OACvBC,qBAAwB,OACxBC,cAAiB,OACjBC,WAAc,MACd9I,YAAe,SAM3BxL,MAAMuU,aAAevU,MAAMuU,YAAYlU,SAASmU,gBAChDxU,MAAMuU,YAAYlU,SAASmU,cACvB1U,EAAEQ,QAAO,EAAMN,MAAMuU,YAAYlU,SAASmU,eACtCC,WAAc,OACdC,cAAiB,MACjBC,wBAA2B,QAC3BC,sBAAyB,QACzBC,eAAkB,MAClBC,WAAc,MACdC,UAAa,MACbC,YAAe,MACfC,aAAgB,MAChBC,UAAa,MACbC,MAAS,QACTC,YAAe,UACf7U,MAAS,KACTC,OAAU,QAIlBR,MAAMuU,aAAevU,MAAMuU,YAAYlU,SAASgV,UAChDrV,MAAMuU,YAAYlU,SAASgV,QACvBvV,EAAEQ,QAAO,EAAMN,MAAMuU,YAAYlU,SAASgV,SACtC9U,MAAS,KACTyL,KAAQ,KACRxL,OAAU,KACV8U,OAAU,KACVC,MAAS,KACTC,OAAU,KACVC,OAAU,KACVC,mBACI3L,MAAS,QACT4L,YACIrM,OAAU,KACVsM,SAAY,KACZjM,KAAQ,OAGhBkM,kBACI9L,MAAS,MAEb+L,gBACI/L,MAAS,QAEbgM,eACIhM,MAAS,MAEbiM,iBACIjM,MAAS,KACTkM,SACIC,aAAgB,MAChBlU,cAAiB,OACjBE,aAAgB,MAChBC,YAAe,OACfgU,SAAY,OACZC,YAAe,OACfC,YAAe,SAGvBC,aACIvM,MAAS,QACTkM,SACIM,WAAc,OACdC,kBAAqB,OACrBC,gBAAmB,OACnBC,QAAW,SAGnBC,cACI5M,MAAS,OACTkM,SACIW,YAAe,SACfC,WAAc,QACdC,cAAiB,QACjBC,SAAY,SAGpBC,oBACIC,KAAQ,gBACRlN,MAAS,SAEbmN,kBACInN,MAAS,OACToN,YAAe,gBACfC,UAAa,QACbC,UACIC,IAAO,MACPhO,OAAU,KACV2N,KAAQ,KACRtN,KAAQ,KACR4N,OAAU,OACVC,KAAQ,MAEZC,WACIC,YAAe,KACfC,SAAY,KACZC,QAAW,KACXC,WAAc,MACdC,QAAW,KACXC,WAAc,MACdC,qBAAwB,OACxBC,kBAAqB,QAEzBC,kBACIR,YAAe,QACfC,SAAY,QACZC,QAAW,cACXC,WAAc,eACdC,QAAW,QACXC,WAAc,SACdC,qBAAwB,UACxBC,kBAAqB,UACrBV,OAAU,WAEdY,QACId,SAAY,KACZe,SAAY,KACZC,IAAO,MACPC,IAAO,MACPnO,MAAS,IACT2B,MAAS,KACTL,IAAO,KACP8M,cAAiB,SACjBC,YAAe,OACfC,YAAe,OACfC,SAAY,WACZtB,UAAa,SACbD,YAAe,SACfwB,YAAe,OACfC,eAAkB,SAClBC,mBAAsB,UAE1BC,cACIC,UAAa,QACbC,YAAe,UAGvBC,gBACIlP,MAAS,QACToO,QACIe,MAAS,KACTC,IAAO,QACPC,SAAY,MACZC,WAAc,OACdC,WAAc,OACdC,UAAa,OACbC,QAAW,MACXC,YAAe,OACfC,MAAS,KACTC,WAAc,KACdC,OAAU,OACVC,aAAgB,KAChBC,WAAc,OAGtBC,oBACIC,aAAgB,kBAEpBC,qBACID,aAAgB,gBAEpBE,0BACIF,aAAgB,SAEpBG,uBACIH,aAAgB,YAEpBI,0BACIJ,aAAgB,WAEpBK,gBACIL,aAAgB,8BAEpBM,mBACIvQ,MAAS,QACTiQ,aAAgB,4BAChB7B,QACIoC,QAAW,MACXC,OAAU,MACVC,SAAY,QAGpBC,4BACIV,aAAgB,mBAEpBW,YACI5Q,MAAS,KACToO,QACIlB,KAAQ,OACR2D,IAAO,OACPC,WAAc,YAM9B7a,MAAMuU,aAAevU,MAAMuU,YAAYlU,SAASya,aAChD9a,MAAMuU,YAAYlU,SAASya,WACvBhb,EAAEQ,QAAO,EAAMN,MAAMuU,YAAYlU,SAASya,YACtCha,cAAiB,WACjBC,eAAkB,WAClBga,cAAiB,OACjBC,kBAAqB,OACrBza,MAAS,KACT2H,OAAU,KACV+S,aAAgB,UAChB3S,MAAS,KACT4S,OAAU,MACVC,aAAgB,IAChBnR,IAAO,KACPC,GAAM,KACNzB,WACIC,QACII,SAAY,OACZC,eAAkB,QAClBF,WAAc,QACdG,SAAY,QACZqS,QAAW,OACXC,aAAgB,SAEpB1R,MACIjB,GAAO,OACPC,IAAO,QACPe,GAAO,OACPF,GAAO,QAEXF,QACIZ,GAAM,OACNC,IAAO,QACPY,IAAO,SACPC,GAAM,OACNC,IAAO,SACPC,GAAM,YAMtB1J,MAAMuU,aAAevU,MAAMuU,YAAYlU,SAASib,cAChDtb,MAAMuU,YAAYlU,SAASib,YACvBxb,EAAEQ,QAAO,EAAMN,MAAMuU,YAAYlU,SAASib,aACtCnG,MAAS,QACTC,YAAe,UACf7U,MAAS,KACTC,OAAU,QAIlBR,MAAMuU,aAAevU,MAAMuU,YAAYlU,SAASkb,UAChDvb,MAAMuU,YAAYlU,SAASkb,QACvBzb,EAAEQ,QAAO,EAAMN,MAAMuU,YAAYlU,SAASkb,SACtCnW,cAAiB,SACjBC,eAAkB,SAClBC,YAAe,SACfC,YAAe,SACfe,UAAa,KACbkV,kBACItF,aAAgB,MAChBlU,cAAiB,OACjBE,aAAgB,MAChBC,YAAe,OACfgU,SAAY,OACZC,YAAe,OACfC,YAAe,QAEnBoF,gBAAmB,OACnB/Z,KAAQ,KACRga,QAAW,KACXJ,aACInG,MAAS,QACTC,YAAe,WAEnBuG,KAAQ,KACRC,IAAO,KACPnW,aAAgB,OAChBD,UAAa,OACbqW,YAAe,gBACf/T,SAAY,SACZ9G,OAAU,KACV8a,WAAc,KACd9Y,SAAY,OACZ+Y,OAAU,UACVC,aACIC,UAAa,KACbhF,KAAQ,KACR3N,OAAU,KACV4S,QAAW,MACXC,UAAa,KACbvG,SAAY,KACZjM,KAAQ,KACRwI,KAAQ,KACRiK,SAAY,OACZC,SAAY,MACZC,YAAe,WAEnBC,sBAAyB,SACzBC,sBAAyB,SACzBC,OAAU,OACVC,eACI9F,YAAe,SACfC,WAAc,QACdC,cAAiB,QACjBC,SAAY,QAEhBpV,OAAU,KACVgb,MAAS,QACTC,cACIrG,WAAc,OACdC,kBAAqB,OACrBC,gBAAmB,OACnBC,QAAW,QAEfmG,KAAQ,QACRC,MAAS,KACTC,aACIC,KAAQ,KACRC,KAAQ,MAEZC,gBAAmB,OACnBC,OAAU,SACVC,KAAQ,KACRC,QAAW,OACXC,SAAY,OACZC,aACIC,aAAgB,aAChBC,cAAiB,aACjBC,aAAgB,eAChBC,cAAiB,gBAErBC,UAAa,OACbC,SAAY,OACZjc,UAAa,KACbkc,WAAc,UACdC,UAAa,QAIrB/d,MAAMuU,aAAevU,MAAMuU,YAAYlU,SAAS2d,OAChDhe,MAAMuU,YAAYlU,SAAS2d,KACvBle,EAAEQ,QAAO,EAAMN,MAAMuU,YAAYlU,SAAS2d,MACtCC,QACIC,gBAAmB,8BACnBC,sBAAyB,yCACzBC,8BAAiC,gBACjCC,8BAAiC,gBACjCC,4BAA+B,qBAC/BC,0BAA6B,mBAC7BC,0BAA6B,cAC7BC,gBAAmB,gBACnBC,mBAAsB,sBACtBC,gBAAmB,uBACnBC,qBAAwB,gBAE5BC,MACIC,KAAQ,KACRC,OAAU,KACVC,KAAQ,SAMpBhf,MAAMC,GAAGgf,SACTjf,MAAMC,GAAGgf,OAAO9e,UAAUC,QACtBN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgf,OAAO9e,UAAUC,SACrC8e,oBAAuB,KACvBC,oBAAuB,KACvBC,gBAAmB,OAK3Bpf,MAAMC,GAAGof,UACTrf,MAAMC,GAAGof,QAAQlf,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGof,QAAQlf,UAAUC,QAAQC,UAC9Cif,OACIhK,OAAU,KACViK,OAAU,KACVC,SAAY,KACZC,WAAc,OACdC,aAAgB,OAChBC,cAAiB,SACjBC,gBAAmB,aAM/B5f,MAAMC,GAAG4f,WACT7f,MAAMC,GAAG4f,SAAS1f,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4f,SAAS1f,UAAUC,QAAQC,UAC/Cyf,OAAU,QACVC,QAAW,SACXC,cAAiB,QACjBzK,MAAS,KACT/I,UACIG,KAAQ,KACRG,OAAU,KACVL,WAAc,KACdC,OAAU,KACVuT,YAAe,OACf5U,QAAW,KACXuB,MAAS,WACT5B,IAAO,aAMnBhL,MAAMC,GAAGigB,WACTlgB,MAAMC,GAAGigB,SAAS/f,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGigB,SAAS/f,UAAUC,QAAQC,UAC/C0f,QAAW,SACXC,cAAiB,QACjBzK,MAAS,QAKjBvV,MAAMC,GAAGkgB,WACTngB,MAAMC,GAAGkgB,SAAShgB,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkgB,SAAShgB,UAAUC,QAAQC,UAC/C0f,QAAW,SACXC,cAAiB,QACjBzK,MAAS,QAKjBvV,MAAMC,GAAGmgB,SACTpgB,MAAMC,GAAGmgB,OAAOjgB,UAAUC,QAAQigB,aAC9BvgB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmgB,OAAOjgB,UAAUC,QAAQigB,cAC7CxT,OAAU,MACVrM,OAAU,KACV+U,MAAS,KACTD,OAAU,KACVzH,MAAS,KACTyS,OAAU,KACVC,mBAAsB,KACtBC,oBAAuB,KACvBvY,cAAiB,aACjBwY,aAAgB,UAChBC,gBAAmB,SACnBC,eAAkB,QAClBC,cAAiB,QACjBC,aAAgB,QAChBC,sBAAyB,SACzBC,mBAAsB,OACtBC,qBAAwB,QACxBC,mBAAsB,QACtBC,mBAAsB,QACtBC,qBAAwB,eAKhCnhB,MAAMC,GAAGmhB,YACTphB,MAAMC,GAAGmhB,UAAUjhB,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmhB,UAAUjhB,UAAUC,QAAQC,UAChDghB,SAAY,YACZC,QAAW,cACXjJ,IAAO,oBACPC,IAAO,oBACPiJ,KAAQ,gBACRC,MAAS,mBACT5G,IAAO,iBACPjR,KAAQ,iBACR8X,YAAe,sBAKvBzhB,MAAMC,GAAGyhB,WACT1hB,MAAMC,GAAGyhB,SAASrhB,SACdP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyhB,SAASrhB,UAC7B0f,QAAW,YAKnB/f,MAAMC,GAAG0hB,SACT3hB,MAAMC,GAAG0hB,OAAOxhB,UAAUC,QAAQC,SAC9BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0hB,OAAOxhB,UAAUC,QAAQigB,cAC7CuB,MAAS,QAKjB5hB,MAAMC,GAAG4hB,WACT7hB,MAAMC,GAAG4hB,SAAS1hB,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4hB,SAAS1hB,UAAUC,QAAQC,UAC/CyhB,iBAAoB,MAK5B9hB,MAAMC,GAAG8hB,QACT/hB,MAAMC,GAAG8hB,MAAM5hB,UAAUC,QAAQC,SAC7BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8hB,MAAM5hB,UAAUC,QAAQigB,cAC5C5K,OAAU,QAKlBzV,MAAMC,GAAG+hB,UACThiB,MAAMC,GAAG+hB,QAAQ7hB,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+hB,QAAQ7hB,UAAUC,QAAQigB,cAC9C5K,OAAU,KACVjV,OAAU,QAKlBR,MAAMC,GAAGgiB,SACTjiB,MAAMC,GAAGgiB,OAAO9hB,UAAUC,QAAQC,SAC9BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgiB,OAAO9hB,UAAUC,QAAQigB,cAC7C5K,OAAU,KACVjV,OAAU,QAKlBR,MAAMC,GAAGiiB,YACTliB,MAAMC,GAAGiiB,UAAU/hB,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiiB,UAAU/hB,UAAUC,QAAQC,UAChDgM,KAAQ,IACRF,MAAS,IACTD,IAAO,IACPmF,QAAW,IACX8Q,KAAQ,IACRC,OAAU,IACVnR,OAAU,IACVoR,UAAa,WAKrBriB,MAAMC,GAAGqiB,eACTtiB,MAAMC,GAAGqiB,aAAaniB,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqiB,aAAaniB,UAAUC,QAAQC,UACnDiI,MAAO,KACPia,UAAW,KACXC,UAAW,UAKnBxiB,MAAMC,GAAGwiB,OACTziB,MAAMC,GAAGwiB,KAAKtiB,UAAUC,QAAQC,SAC5BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwiB,KAAKtiB,UAAUC,QAAQC,UAC3CqiB,YAAa,YAKrB1iB,MAAMC,GAAG0iB,SACT3iB,MAAMC,GAAG0iB,OAAOxiB,UAAUC,QAAQC,SAC9BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0iB,OAAOxiB,UAAUC,QAAQC,UAC7CuiB,QAAW,IACXC,UAAa,OAOrB7iB,MAAM8iB,OAAO7iB,GAAG8iB,WAChB/iB,MAAM8iB,OAAO7iB,GAAG8iB,SAAS5iB,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAM8iB,OAAO7iB,GAAG8iB,SAAS5iB,UAAUC,QAAQC,UACtD2iB,aAAgB,OAChBC,gBAAmB,OACnBC,gBAAmB,YAK3BljB,MAAM8iB,OAAO7iB,GAAGkjB,WAChBnjB,MAAM8iB,OAAO7iB,GAAGkjB,SAAShjB,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAM8iB,OAAO7iB,GAAGkjB,SAAShjB,UAAUC,QAAQC,UACtD+iB,aAAgB,SAChBJ,aAAgB,OAChBC,gBAAmB,OACnBC,gBAAmB,aAIhCG,OAAOrjB,MAAMsjB", "file": "kendo.messages.zh-TW.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n/* JS for All Kendo UI Components Traditional Chinese (zh-TW) Language Pack | Written by IKKI | 2018-02-22 */\n(function ($, undefined) {\n\n    /* FlatColorPicker messages */\n    if (kendo.ui.FlatColorPicker) {\n        kendo.ui.FlatColorPicker.prototype.options.messages =\n            $.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n                \"apply\": \"確定\",\n                \"cancel\": \"取消\",\n                \"noColor\": \"無顏色\",\n                \"clearColor\": \"清除顏色\",\n                \"previewInput\": \"顏色十六進位代碼\"\n            });\n    }\n\n    /* ColorPicker messages */\n    if (kendo.ui.ColorPicker) {\n        kendo.ui.ColorPicker.prototype.options.messages =\n            $.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n                \"apply\": \"確定\",\n                \"cancel\": \"取消\",\n                \"noColor\": \"無顏色\",\n                \"clearColor\": \"清除顏色\",\n                \"previewInput\": \"顏色十六進位代碼\"\n            });\n    }\n\n    /* ColumnMenu messages */\n    if (kendo.ui.ColumnMenu) {\n        kendo.ui.ColumnMenu.prototype.options.messages =\n            $.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n                \"sortAscending\": \"昇冪排列\",\n                \"sortDescending\": \"降冪排列\",\n                \"filter\": \"篩選\",\n                \"columns\": \"欄位列\",\n                \"done\": \"完成\",\n                \"settings\": \"列設置\",\n                \"lock\": \"鎖定\",\n                \"unlock\": \"解鎖\"\n            });\n    }\n\n    /* DateRangePicker messages */\n    if (kendo.ui.DateRangePicker) {\n        kendo.ui.DateRangePicker.prototype.options.messages =\n            $.extend(true, kendo.ui.DateRangePicker.prototype.options.messages,{\n                \"startLabel\": \"開始\",\n                \"endLabel\": \"結束\"\n            });\n    }\n\n    /* Editor messages */\n    if (kendo.ui.Editor) {\n        kendo.ui.Editor.prototype.options.messages =\n            $.extend(true, kendo.ui.Editor.prototype.options.messages,{\n                \"bold\": \"粗體\",\n                \"italic\": \"斜體\",\n                \"underline\": \"底線\",\n                \"strikethrough\": \"刪除線\",\n                \"superscript\": \"上標\",\n                \"subscript\": \"下標\",\n                \"justifyCenter\": \"水準居中\",\n                \"justifyLeft\": \"左對齊\",\n                \"justifyRight\": \"右對齊\",\n                \"justifyFull\": \"兩端對齊\",\n                \"insertUnorderedList\": \"插入無序列表\",\n                \"insertOrderedList\": \"插入有序列表\",\n                \"indent\": \"增加縮進\",\n                \"outdent\": \"減少縮進\",\n                \"createLink\": \"插入連結\",\n                \"unlink\": \"刪除連結\",\n                \"insertImage\": \"插入圖片\",\n                \"insertFile\": \"插入文件\",\n                \"insertHtml\": \"插入HTML\",\n                \"viewHtml\": \"原始程式碼編輯\",\n                \"fontName\": \"請選擇字體\",\n                \"fontNameInherit\": \"（預設字體）\",\n                \"fontSize\": \"請選擇字型大小\",\n                \"fontSizeInherit\": \"（默認字型大小）\",\n                \"formatBlock\": \"格式\",\n                \"formatting\": \"格式\",\n                \"foreColor\": \"文字顏色\",\n                \"backColor\": \"文字背景色\",\n                \"style\": \"樣式\",\n                \"emptyFolder\": \"資料夾為空\",\n                \"editAreaTitle\": \"在可編輯區域可按 F10 跳轉工具列。\",\n                \"uploadFile\": \"上傳文件\",\n                \"overflowAnchor\": \"更多功能\",\n                \"orderBy\": \"排序方式：\",\n                \"orderBySize\": \"按大小排序\",\n                \"orderByName\": \"按名稱排序\",\n                \"invalidFileType\": \"你上傳的檔案格式 {0} 是無效的，支持的檔案類型為：{1}\",\n                \"deleteFile\": \"你確定要刪除【{0}】這個檔嗎？\",\n                \"overwriteFile\": \"當前資料夾已存在檔案名為【{0}】的檔，是否覆蓋？\",\n                \"directoryNotFound\": \"資料夾未找到\",\n                \"imageWebAddress\": \"圖片連結位址\",\n                \"imageAltText\": \"圖片預留位置\",\n                \"imageWidth\": \"圖片寬度（單位px）\",\n                \"imageHeight\": \"圖片高度（單位px）\",\n                \"fileWebAddress\": \"檔連結位址\",\n                \"fileTitle\": \"文件標題\",\n                \"linkWebAddress\": \"連結位址\",\n                \"linkText\": \"連結文字\",\n                \"linkToolTip\": \"連結提示\",\n                \"linkOpenInNewWindow\": \"在新視窗中打開連結\",\n                \"dialogUpdate\": \"更新\",\n                \"dialogInsert\": \"插入\",\n                \"dialogButtonSeparator\": \"或\",\n                \"dialogOk\": \"確定\",\n                \"dialogCancel\": \"取消\",\n                \"cleanFormatting\": \"清除格式\",\n                \"createTable\": \"創建表格\",\n                \"createTableHint\": \"創建一個 {0} 行 {1} 列的表格\",\n                \"addColumnLeft\": \"在左側插入列\",\n                \"addColumnRight\": \"在右側插入列\",\n                \"addRowAbove\": \"在上方插入行\",\n                \"addRowBelow\": \"在下方插入行\",\n                \"deleteRow\": \"刪除行\",\n                \"deleteColumn\": \"刪除列\",\n                \"tableWizard\": \"表格嚮導\",\n                \"tableTab\": \"表格\",\n                \"cellTab\": \"儲存格\",\n                \"accessibilityTab\": \"可訪問性\",\n                \"caption\": \"標題\",\n                \"summary\": \"摘要\",\n                \"width\": \"寬\",\n                \"height\": \"高\",\n                \"units\": \"單位\",\n                \"cellSpacing\": \"儲存格間距\",\n                \"cellPadding\": \"儲存格內邊距\",\n                \"cellMargin\": \"單元格外邊距\",\n                \"alignment\": \"對齊\",\n                \"background\": \"背景色\",\n                \"cssClass\": \"樣式表\",\n                \"id\": \"ID\",\n                \"border\": \"邊框\",\n                \"borderStyle\": \"邊框樣式\",\n                \"collapseBorders\": \"合併邊框\",\n                \"wrapText\": \"文字換行\",\n                \"associateCellsWithHeaders\": \"關聯表頭與儲存格\",\n                \"alignLeft\": \"左對齊\",\n                \"alignCenter\": \"居中對齊\",\n                \"alignRight\": \"右對齊\",\n                \"alignLeftTop\": \"左上對齊\",\n                \"alignCenterTop\": \"中上對齊\",\n                \"alignRightTop\": \"右上對齊\",\n                \"alignLeftMiddle\": \"左中對齊\",\n                \"alignCenterMiddle\": \"居中對齊\",\n                \"alignRightMiddle\": \"右中對齊\",\n                \"alignLeftBottom\": \"左下對齊\",\n                \"alignCenterBottom\": \"中下對齊\",\n                \"alignRightBottom\": \"右下對齊\",\n                \"alignRemove\": \"移除對齊\",\n                \"columns\": \"列\",\n                \"rows\": \"行\",\n                \"selectAllCells\": \"選擇所有儲存格\",\n                \"exportAs\": \"匯出\",\n                \"import\": \"導入\"\n            });\n    }\n\n    /* FileBrowser messages */\n    if (kendo.ui.FileBrowser) {\n        kendo.ui.FileBrowser.prototype.options.messages =\n            $.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n                \"uploadFile\": \"上傳文件\",\n                \"orderBy\": \"排序方式\",\n                \"orderByName\": \"按名稱排序\",\n                \"orderBySize\": \"按大小排序\",\n                \"directoryNotFound\": \"資料夾未找到\",\n                \"emptyFolder\": \"資料夾為空\",\n                \"deleteFile\": \"你確定要刪除【{0}】這個檔嗎？\",\n                \"invalidFileType\": \"你上傳的檔案格式 {0} 是無效的，支持的檔案類型為：{1}\",\n                \"overwriteFile\": \"當前資料夾已存在檔案名為【{0}】的檔，是否覆蓋？\",\n                \"dropFilesHere\": \"將文件拖拽到此處上傳\",\n                \"search\": \"搜索\"\n            });\n    }\n\n    /* FilterCell messages */\n    if (kendo.ui.FilterCell) {\n        kendo.ui.FilterCell.prototype.options.messages =\n            $.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n                \"isTrue\": \"是\",\n                \"isFalse\": \"否\",\n                \"filter\": \"篩選\",\n                \"clear\": \"清空\",\n                \"operator\": \"運算子\"\n            });\n    }\n\n    /* FilterCell operators */\n    if (kendo.ui.FilterCell) {\n        kendo.ui.FilterCell.prototype.options.operators =\n            $.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n                \"string\": {\n                    \"eq\": \"等於\",\n                    \"neq\": \"不等於\",\n                    \"startswith\": \"開頭是\",\n                    \"contains\": \"包含\",\n                    \"doesnotcontain\": \"不含\",\n                    \"endswith\": \"結尾是\",\n                    \"isnull\": \"為空\",\n                    \"isnotnull\": \"非空\",\n                    \"isempty\": \"空字串\",\n                    \"isnotempty\": \"非空字串\",\n                    \"isnullorempty\": \"無值\",\n                    \"isnotnullorempty\": \"有值\"\n                },\n                \"number\": {\n                    \"eq\": \"等於\",\n                    \"neq\": \"不等於\",\n                    \"gte\": \"大於等於\",\n                    \"gt\": \"大於\",\n                    \"lte\": \"小於等於\",\n                    \"lt\": \"小於\",\n                    \"isnull\": \"為空\",\n                    \"isnotnull\": \"非空\"\n                },\n                \"date\": {\n                    \"eq\": \"等於\",\n                    \"neq\": \"不等於\",\n                    \"gte\": \"晚於等於\",\n                    \"gt\": \"晚於\",\n                    \"lte\": \"早於等於\",\n                    \"lt\": \"早於\",\n                    \"isnull\": \"為空\",\n                    \"isnotnull\": \"非空\"\n                },\n                \"enums\": {\n                    \"eq\": \"等於\",\n                    \"neq\": \"不等於\",\n                    \"isnull\": \"為空\",\n                    \"isnotnull\": \"非空\"\n                }\n            });\n    }\n\n    /* FilterMenu messages */\n    if (kendo.ui.FilterMenu) {\n        kendo.ui.FilterMenu.prototype.options.messages =\n            $.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n                \"info\": \"篩選條件：\",\n                \"title\": \"篩選條件：\",\n                \"isTrue\": \"是\",\n                \"isFalse\": \"否\",\n                \"filter\": \"篩選\",\n                \"clear\": \"清空\",\n                \"and\": \"並且\",\n                \"or\": \"或者\",\n                \"selectValue\": \"-= 請選擇 =-\",\n                \"operator\": \"運算子\",\n                \"value\": \"值\",\n                \"additionalValue\": \"附加值\",\n                \"additionalOperator\": \"附加運算\",\n                \"logic\": \"篩選邏輯\",\n                \"cancel\": \"取消\"\n            });\n    }\n\n    /* FilterMenu operator messages */\n    if (kendo.ui.FilterMenu) {\n        kendo.ui.FilterMenu.prototype.options.operators =\n            $.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n                \"string\": {\n                    \"eq\": \"等於\",\n                    \"neq\": \"不等於\",\n                    \"startswith\": \"開頭是\",\n                    \"contains\": \"包含\",\n                    \"doesnotcontain\": \"不含\",\n                    \"endswith\": \"結尾是\",\n                    \"isnull\": \"為空\",\n                    \"isnotnull\": \"非空\",\n                    \"isempty\": \"空字串\",\n                    \"isnotempty\": \"非空字串\",\n                    \"isnullorempty\": \"無值\",\n                    \"isnotnullorempty\": \"有值\"\n                },\n                \"number\": {\n                    \"eq\": \"等於\",\n                    \"neq\": \"不等於\",\n                    \"gte\": \"大於等於\",\n                    \"gt\": \"大於\",\n                    \"lte\": \"小於等於\",\n                    \"lt\": \"小於\",\n                    \"isnull\": \"為空\",\n                    \"isnotnull\": \"非空\"\n                },\n                \"date\": {\n                    \"eq\": \"等於\",\n                    \"neq\": \"不等於\",\n                    \"gte\": \"晚於等於\",\n                    \"gt\": \"晚於\",\n                    \"lte\": \"早於等於\",\n                    \"lt\": \"早於\",\n                    \"isnull\": \"為空\",\n                    \"isnotnull\": \"非空\"\n                },\n                \"enums\": {\n                    \"eq\": \"等於\",\n                    \"neq\": \"不等於\",\n                    \"isnull\": \"為空\",\n                    \"isnotnull\": \"非空\"\n                }\n            });\n    }\n\n    /* FilterMultiCheck messages */\n    if (kendo.ui.FilterMultiCheck) {\n        kendo.ui.FilterMultiCheck.prototype.options.messages =\n            $.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n                \"checkAll\": \"全選\",\n                \"clear\": \"清空\",\n                \"filter\": \"篩選\",\n                \"search\": \"搜索\",\n                \"cancel\": \"取消\",\n                \"selectedItemsFormat\": \"已選擇 {0} 條資料\"\n            });\n    }\n\n    /* Gantt messages */\n    if (kendo.ui.Gantt) {\n        kendo.ui.Gantt.prototype.options.messages =\n            $.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n                \"actions\": {\n                    \"addChild\": \"新增子任務\",\n                    \"append\": \"新增任務\",\n                    \"insertAfter\": \"插入到後面\",\n                    \"insertBefore\": \"插入到前面\",\n                    \"pdf\": \"匯出 PDF\"\n                },\n                \"cancel\": \"取消\",\n                \"deleteDependencyWindowTitle\": \"刪除從屬任務\",\n                \"deleteDependencyConfirmation\": \"你確定要刪除這項從屬任務嗎？\",\n                \"deleteTaskWindowTitle\": \"刪除任務\",\n                \"deleteTaskConfirmation\": \"你確定要刪除這項任務嗎？\",\n                \"destroy\": \"刪除\",\n                \"editor\": {\n                    \"assingButton\": \"資源配置\",\n                    \"editorTitle\": \"編輯任務\",\n                    \"end\": \"結束時間\",\n                    \"percentComplete\": \"完成進度\",\n                    \"resources\": \"資源\",\n                    \"resourcesEditorTitle\": \"資源編輯\",\n                    \"resourcesHeader\": \"資源名稱\",\n                    \"start\": \"開始時間\",\n                    \"title\": \"任務標題\",\n                    \"unitsHeader\": \"百分比\"\n                },\n                \"save\": \"保存\",\n                \"views\": {\n                    \"day\": \"日視圖\",\n                    \"end\": \"任務結束\",\n                    \"month\": \"月視圖\",\n                    \"start\": \"任務開始\",\n                    \"week\": \"周視圖\",\n                    \"year\": \"年視圖\"\n                }\n            });\n    }\n\n    /* GanttTimeline messages */\n    if (kendo.ui.GanttTimeline) {\n        kendo.ui.GanttTimeline.prototype.options.messages =\n            $.extend(true, kendo.ui.GanttTimeline.prototype.options.messages,{\n                \"views\": {\n                    \"day\": \"日視圖\",\n                    \"week\": \"周視圖\",\n                    \"month\": \"月視圖\",\n                    \"year\": \"年視圖\",\n                    \"start\": \"任務開始\",\n                    \"end\": \"任務結束\"\n                }\n            });\n    }\n\n    /* Grid messages */\n    if (kendo.ui.Grid) {\n        kendo.ui.Grid.prototype.options.messages =\n            $.extend(true, kendo.ui.Grid.prototype.options.messages,{\n                \"commands\": {\n                    \"cancel\": \"取消\",\n                    \"canceledit\": \"取消\",\n                    \"create\": \"新增\",\n                    \"destroy\": \"刪除\",\n                    \"edit\": \"編輯\",\n                    \"excel\": \"匯出 Excel\",\n                    \"pdf\": \"匯出 PDF\",\n                    \"save\": \"保存\",\n                    \"select\": \"選擇\",\n                    \"update\": \"更新\"\n                },\n                \"editable\": {\n                    \"cancelDelete\": \"取消刪除\",\n                    \"confirmation\": \"你確定要刪除這條資料嗎？\",\n                    \"confirmDelete\": \"確定刪除\"\n                },\n                \"noRecords\": \"無相關資料\",\n                \"expandCollapseColumnHeader\": \"\",\n                \"groupHeader\": \"按 Ctrl + 空格 進行分組\",\n                \"ungroupHeader\": \"按 Ctrl + 空格 取消分組\"\n            });\n    }\n\n    /* Groupable messages */\n    if (kendo.ui.Groupable) {\n        kendo.ui.Groupable.prototype.options.messages =\n            $.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n                \"empty\": \"將欄位列名稱拖拽到此處可進行該列的分組顯示\"\n            });\n    }\n\n    /* NumericTextBox messages */\n    if (kendo.ui.NumericTextBox) {\n        kendo.ui.NumericTextBox.prototype.options =\n            $.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n                \"upArrowText\": \"增加\",\n                \"downArrowText\": \"減少\"\n            });\n    }\n\n    /* MediaPlayer messages */\n    if (kendo.ui.MediaPlayer) {\n        kendo.ui.MediaPlayer.prototype.options.messages =\n            $.extend(true, kendo.ui.MediaPlayer.prototype.options.messages,{\n                \"pause\": \"暫停\",\n                \"play\": \"播放\",\n                \"mute\": \"靜音\",\n                \"unmute\": \"取消靜音\",\n                \"quality\": \"畫質\",\n                \"fullscreen\": \"全屏\"\n            });\n    }\n\n    /* Pager messages */\n    if (kendo.ui.Pager) {\n        kendo.ui.Pager.prototype.options.messages =\n            $.extend(true, kendo.ui.Pager.prototype.options.messages,{\n                \"allPages\": \"全部\",\n                \"display\": \"{0} - {1} 條　共 {2} 條數據\",\n                \"empty\": \"無相關資料\",\n                \"page\": \"轉到第\",\n                \"of\": \"頁　共 {0} 頁\",\n                \"itemsPerPage\": \"條每頁\",\n                \"first\": \"首頁\",\n                \"previous\": \"上一頁\",\n                \"next\": \"下一頁\",\n                \"last\": \"末頁\",\n                \"refresh\": \"刷新\",\n                \"morePages\": \"更多...\"\n            });\n    }\n\n    /* TreeListPager messages */\n    if (kendo.ui.TreeListPager) {\n        kendo.ui.TreeListPager.prototype.options.messages =\n            $.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n                \"allPages\": \"全部\",\n                \"display\": \"{0} - {1} 條　共 {2} 條數據\",\n                \"empty\": \"無相關資料\",\n                \"page\": \"轉到第\",\n                \"of\": \"頁　共 {0} 頁\",\n                \"itemsPerPage\": \"條每頁\",\n                \"first\": \"首頁\",\n                \"previous\": \"上一頁\",\n                \"next\": \"下一頁\",\n                \"last\": \"末頁\",\n                \"refresh\": \"刷新\",\n                \"morePages\": \"更多...\"\n            });\n    }\n\n    /* PivotGrid messages */\n    if (kendo.ui.PivotGrid) {\n        kendo.ui.PivotGrid.prototype.options.messages =\n            $.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n                \"measureFields\": \"拖拽資料欄位到此處\",\n                \"columnFields\": \"拖拽列欄位到此處\",\n                \"rowFields\": \"拖拽行欄位到此處\"\n            });\n    }\n\n    /* PivotFieldMenu messages */\n    if (kendo.ui.PivotFieldMenu) {\n        kendo.ui.PivotFieldMenu.prototype.options.messages =\n            $.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n                \"info\": \"篩選條件：\",\n                \"sortAscending\": \"昇冪排列\",\n                \"sortDescending\": \"降冪排列\",\n                \"filterFields\": \"欄位篩選\",\n                \"filter\": \"篩選\",\n                \"include\": \"包含欄位...\",\n                \"title\": \"包含的欄位\",\n                \"clear\": \"清空\",\n                \"ok\": \"確定\",\n                \"cancel\": \"取消\",\n                \"operators\": {\n                    \"contains\": \"包含\",\n                    \"doesnotcontain\": \"不含\",\n                    \"startswith\": \"開頭是\",\n                    \"endswith\": \"結尾是\",\n                    \"eq\": \"等於\",\n                    \"neq\": \"不等於\"\n                }\n            });\n    }\n\n    /* PivotSettingTarget messages */\n    if (kendo.ui.PivotSettingTarget) {\n        kendo.ui.PivotSettingTarget.prototype.options.messages =\n            $.extend(true, kendo.ui.PivotSettingTarget.prototype.options.messages,{\n                \"empty\": \"拖拽欄位到此處\"\n            });\n    }\n\n    /* PivotConfigurator messages */\n    if (kendo.ui.PivotConfigurator) {\n        kendo.ui.PivotConfigurator.prototype.options.messages =\n            $.extend(true, kendo.ui.PivotConfigurator.prototype.options.messages,{\n                \"measures\": \"拖拽資料欄位到此處\",\n                \"columns\": \"拖拽列欄位到此處\",\n                \"rows\": \"拖拽行欄位到此處\",\n                \"measuresLabel\": \"量\",\n                \"columnsLabel\": \"列\",\n                \"rowsLabel\": \"行\",\n                \"fieldsLabel\": \"欄位\"\n            });\n    }\n\n    /* RecurrenceEditor messages */\n    if (kendo.ui.RecurrenceEditor) {\n        kendo.ui.RecurrenceEditor.prototype.options.messages =\n            $.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n                \"recurrenceEditorTitle\": \"週期類型事件編輯\",\n                \"frequencies\": {\n                    \"never\": \"從不\",\n                    \"hourly\": \"每小時\",\n                    \"daily\": \"每天\",\n                    \"weekly\": \"每週\",\n                    \"monthly\": \"每月\",\n                    \"yearly\": \"每年\"\n                },\n                \"hourly\": {\n                    \"repeatEvery\": \"週期\",\n                    \"interval\": \" 小時\"\n                },\n                \"daily\": {\n                    \"repeatEvery\": \"週期\",\n                    \"interval\": \" 天\"\n                },\n                \"weekly\": {\n                    \"repeatEvery\": \"週期\",\n                    \"repeatOn\": \"重複於\",\n                    \"interval\": \" 周\"\n                },\n                \"monthly\": {\n                    \"repeatEvery\": \"週期\",\n                    \"repeatOn\": \"重複於\",\n                    \"interval\": \" 月\",\n                    \"day\": \"幾號 \"\n                },\n                \"yearly\": {\n                    \"repeatEvery\": \"週期\",\n                    \"repeatOn\": \"重複於\",\n                    \"interval\": \" 年\",\n                    \"of\": \" 在 \"\n                },\n                \"end\": {\n                    \"label\": \"截止\",\n                    \"mobileLabel\": \"截止\",\n                    \"never\": \"從不\",\n                    \"after\": \"重複 \",\n                    \"occurrence\": \" 次後\",\n                    \"on\": \"止於 \"\n                },\n                \"offsetPositions\": {\n                    \"first\": \"第一\",\n                    \"second\": \"第二\",\n                    \"third\": \"第三\",\n                    \"fourth\": \"第四\",\n                    \"last\": \"最後一\"\n                },\n                \"weekdays\": {\n                    \"day\": \"天\",\n                    \"weekday\": \"工作日\",\n                    \"weekend\": \"週末\"\n                }\n            });\n    }\n\n    /* MobileRecurrenceEditor messages */\n    if (kendo.ui.MobileRecurrenceEditor) {\n        kendo.ui.MobileRecurrenceEditor.prototype.options.messages =\n            $.extend(true, kendo.ui.MobileRecurrenceEditor.prototype.options.messages,{\n                \"cancel\": \"取消\",\n                \"update\": \"保存\",\n                \"endTitle\": \"週期截止\",\n                \"repeatTitle\": \"週期模式\",\n                \"headerTitle\": \"週期事件\",\n                \"end\": {\n                    \"patterns\": {\n                        \"never\": \"從不\",\n                        \"after\": \"重複...\",\n                        \"on\": \"止於...\"\n                    },\n                    \"never\": \"從不\",\n                    \"after\": \"週期後截止\",\n                    \"on\": \"週期時截止\"\n                },\n                \"daily\": {\n                    \"interval\": \"\"\n                },\n                \"hourly\": {\n                    \"interval\": \"\"\n                },\n                \"weekly\": {\n                    \"interval\": \"\"\n                },\n                \"monthly\": {\n                    \"interval\": \"\",\n                    \"repeatBy\": \"重複到：\",\n                    \"dayOfMonth\": \"幾號\",\n                    \"dayOfWeek\": \"周幾\",\n                    \"repeatEvery\": \"全部重複\",\n                    \"every\": \"每\",\n                    \"day\": \"天\"\n                },\n                \"yearly\": {\n                    \"interval\": \"\",\n                    \"repeatBy\": \"重複到：\",\n                    \"dayOfMonth\": \"幾號\",\n                    \"dayOfWeek\": \"周幾\",\n                    \"repeatEvery\": \"全部重複\",\n                    \"every\": \"每\",\n                    \"month\": \"月\",\n                    \"day\": \"天\"\n                }\n            });\n    }\n\n    /* Scheduler messages */\n    if (kendo.ui.Scheduler) {\n        kendo.ui.Scheduler.prototype.options.messages =\n            $.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n                \"allDay\": \"全天\",\n                \"date\": \"日期\",\n                \"event\": \"事件\",\n                \"time\": \"時間\",\n                \"showFullDay\": \"顯示全天\",\n                \"showWorkDay\": \"顯示工作時間\",\n                \"today\": \"今天\",\n                \"pdf\": \"匯出 PDF\",\n                \"save\": \"保存\",\n                \"cancel\": \"取消\",\n                \"destroy\": \"刪除\",\n                \"resetSeries\": \"重置週期事件\",\n                \"deleteWindowTitle\": \"刪除事件\",\n                \"next\": \"往後\",\n                \"previous\": \"往前\",\n                \"ariaSlotLabel\": \"從 {0:t} 到 {1:t} 的選擇\",\n                \"ariaEventLabel\": \"在 {1:D} {2:t} 的 {0}\",\n                \"editable\": {\n                    \"confirmation\": \"你確定要刪除這個事件嗎？\"\n                },\n                \"views\": {\n                    \"day\": \"日視圖\",\n                    \"week\": \"周視圖\",\n                    \"workWeek\": \"工作日視圖\",\n                    \"agenda\": \"列表視圖\",\n                    \"month\": \"月視圖\",\n                    \"timeline\": \"時間線\",\n                    \"timelineWeek\": \"時間線周視圖\",\n                    \"timelineWorkWeek\": \"時間線工作日視圖\",\n                    \"timelineMonth\": \"時間線月視圖\"\n                },\n                \"recurrenceMessages\": {\n                    \"deleteWindowTitle\": \"刪除週期類型事件\",\n                    \"resetSeriesWindowTitle\": \"重置週期事件\",\n                    \"deleteWindowOccurrence\": \"刪除當前事件\",\n                    \"deleteWindowSeries\": \"刪除整個週期事件\",\n                    \"deleteRecurringConfirmation\": \"你確定要刪除當前事件？\",\n                    \"deleteSeriesConfirmation\": \"你確定要刪除整個週期事件？\",\n                    \"editWindowTitle\": \"編輯週期類型事件\",\n                    \"editWindowOccurrence\": \"編輯當前事件\",\n                    \"editWindowSeries\": \"編輯整個週期事件\",\n                    \"deleteRecurring\": \"你想僅刪除當前事件還是整個週期事件？\",\n                    \"editRecurring\": \"你想僅編輯當前事件還是整個週期事件？\"\n                },\n                \"editor\": {\n                    \"title\": \"事件標題\",\n                    \"start\": \"開始時間\",\n                    \"end\": \"結束時間\",\n                    \"allDayEvent\": \"全天事件\",\n                    \"description\": \"描述\",\n                    \"repeat\": \"重複\",\n                    \"timezone\": \"時區\",\n                    \"startTimezone\": \"開始時區\",\n                    \"endTimezone\": \"結束時區\",\n                    \"separateTimezones\": \"使用獨立的開始和結束時區\",\n                    \"timezoneEditorTitle\": \"時區設置\",\n                    \"timezoneEditorButton\": \"時區選擇\",\n                    \"timezoneTitle\": \"選擇時區\",\n                    \"noTimezone\": \"無時區\",\n                    \"editorTitle\": \"事件\"\n                }\n            });\n    }\n\n    /* Spreadsheet messages */\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\n        kendo.spreadsheet.messages.borderPalette =\n            $.extend(true, kendo.spreadsheet.messages.borderPalette,{\n                \"allBorders\": \"內外框線\",\n                \"insideBorders\": \"內框線\",\n                \"insideHorizontalBorders\": \"橫向內框線\",\n                \"insideVerticalBorders\": \"縱向內框線\",\n                \"outsideBorders\": \"外框線\",\n                \"leftBorder\": \"左框線\",\n                \"topBorder\": \"上框線\",\n                \"rightBorder\": \"右框線\",\n                \"bottomBorder\": \"下框線\",\n                \"noBorders\": \"無框線\",\n                \"reset\": \"無填充顏色\",\n                \"customColor\": \"其他顏色...\",\n                \"apply\": \"確定\",\n                \"cancel\": \"取消\"\n            });\n    }\n\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\n        kendo.spreadsheet.messages.dialogs =\n            $.extend(true, kendo.spreadsheet.messages.dialogs,{\n                \"apply\": \"確定\",\n                \"save\": \"保存\",\n                \"cancel\": \"取消\",\n                \"remove\": \"移除\",\n                \"retry\": \"重試\",\n                \"revert\": \"復原\",\n                \"okText\": \"確定\",\n                \"formatCellsDialog\": {\n                    \"title\": \"單元格格式\",\n                    \"categories\": {\n                        \"number\": \"數字\",\n                        \"currency\": \"貨幣\",\n                        \"date\": \"日期\"\n                    }\n                },\n                \"fontFamilyDialog\": {\n                    \"title\": \"字體\"\n                },\n                \"fontSizeDialog\": {\n                    \"title\": \"字型大小\"\n                },\n                \"bordersDialog\": {\n                    \"title\": \"邊框\"\n                },\n                \"alignmentDialog\": {\n                    \"title\": \"對齊\",\n                    \"buttons\": {\n                        \"justtifyLeft\": \"左對齊\",\n                        \"justifyCenter\": \"水準居中\",\n                        \"justifyRight\": \"右對齊\",\n                        \"justifyFull\": \"兩端對齊\",\n                        \"alignTop\": \"頂端對齊\",\n                        \"alignMiddle\": \"垂直居中\",\n                        \"alignBottom\": \"底端對齊\"\n                    }\n                },\n                \"mergeDialog\": {\n                    \"title\": \"合併儲存格\",\n                    \"buttons\": {\n                        \"mergeCells\": \"全部合併\",\n                        \"mergeHorizontally\": \"水準合併\",\n                        \"mergeVertically\": \"垂直合併\",\n                        \"unmerge\": \"取消合併\"\n                    }\n                },\n                \"freezeDialog\": {\n                    \"title\": \"凍結窗格\",\n                    \"buttons\": {\n                        \"freezePanes\": \"凍結上行左列\",\n                        \"freezeRows\": \"凍結上部行\",\n                        \"freezeColumns\": \"凍結左側列\",\n                        \"unfreeze\": \"取消凍結\"\n                    }\n                },\n                \"confirmationDialog\": {\n                    \"text\": \"你確定要刪除這張工作表嗎？\",\n                    \"title\": \"刪除工作表\"\n                },\n                \"validationDialog\": {\n                    \"title\": \"資料驗證\",\n                    \"hintMessage\": \"請輸入一個{1}的{0}值\",\n                    \"hintTitle\": \"驗證{0}\",\n                    \"criteria\": {\n                        \"any\": \"任何值\",\n                        \"number\": \"數字\",\n                        \"text\": \"文本\",\n                        \"date\": \"日期\",\n                        \"custom\": \"自訂公式\",\n                        \"list\": \"序列\"\n                    },\n                    \"comparers\": {\n                        \"greaterThan\": \"大於\",\n                        \"lessThan\": \"小於\",\n                        \"between\": \"介於\",\n                        \"notBetween\": \"不介於\",\n                        \"equalTo\": \"等於\",\n                        \"notEqualTo\": \"不等於\",\n                        \"greaterThanOrEqualTo\": \"大於等於\",\n                        \"lessThanOrEqualTo\": \"小於等於\"\n                    },\n                    \"comparerMessages\": {\n                        \"greaterThan\": \"大於{0}\",\n                        \"lessThan\": \"小於{0}\",\n                        \"between\": \"介於{0}和{1}之間\",\n                        \"notBetween\": \"不介於{0}和{1}之間\",\n                        \"equalTo\": \"等於{0}\",\n                        \"notEqualTo\": \"不等於{0}\",\n                        \"greaterThanOrEqualTo\": \"大於等於{0}\",\n                        \"lessThanOrEqualTo\": \"小於等於{0}\",\n                        \"custom\": \"滿足公式{0}\"\n                    },\n                    \"labels\": {\n                        \"criteria\": \"允許\",\n                        \"comparer\": \"比較\",\n                        \"min\": \"最小值\",\n                        \"max\": \"最大值\",\n                        \"value\": \"值\",\n                        \"start\": \"開始\",\n                        \"end\": \"結束\",\n                        \"onInvalidData\": \"對無效的數據\",\n                        \"rejectInput\": \"拒絕輸入\",\n                        \"showWarning\": \"顯示警告\",\n                        \"showHint\": \"選定儲存格時顯示\",\n                        \"hintTitle\": \"選定時的標題\",\n                        \"hintMessage\": \"選定時的信息\",\n                        \"ignoreBlank\": \"忽略空值\",\n                        \"showListButton\": \"顯示序列按鈕\",\n                        \"showCalendarButton\": \"顯示日曆按鈕\"\n                    },\n                    \"placeholders\": {\n                        \"typeTitle\": \"請輸入標題\",\n                        \"typeMessage\": \"請輸入資訊\"\n                    }\n                },\n                \"exportAsDialog\": {\n                    \"title\": \"匯出...\",\n                    \"labels\": {\n                        \"scale\": \"縮放\",\n                        \"fit\": \"調整為一頁\",\n                        \"fileName\": \"檔案名\",\n                        \"saveAsType\": \"保存類型\",\n                        \"exportArea\": \"匯出範圍\",\n                        \"paperSize\": \"紙張大小\",\n                        \"margins\": \"頁邊距\",\n                        \"orientation\": \"紙張方向\",\n                        \"print\": \"列印\",\n                        \"guidelines\": \"格線\",\n                        \"center\": \"居中方式\",\n                        \"horizontally\": \"水準\",\n                        \"vertically\": \"垂直\"\n                    }\n                },\n                \"modifyMergedDialog\": {\n                    \"errorMessage\": \"不能更改已合併儲存格的局部。\"\n                },\n                \"rangeDisabledDialog\": {\n                    \"errorMessage\": \"指定範圍包含禁用儲存格。\"\n                },\n                \"incompatibleRangesDialog\": {\n                    \"errorMessage\": \"範圍不匹配\"\n                },\n                \"noFillDirectionDialog\": {\n                    \"errorMessage\": \"無法判斷填充方向\"\n                },\n                \"duplicateSheetNameDialog\": {\n                    \"errorMessage\": \"工作表名稱重複\"\n                },\n                \"overflowDialog\": {\n                    \"errorMessage\": \"不能粘貼，因為複製區域和粘貼區域的大小和形狀不一樣。\"\n                },\n                \"useKeyboardDialog\": {\n                    \"title\": \"複製和粘貼\",\n                    \"errorMessage\": \"不能通過功能表調用這些操作，請使用鍵盤快速鍵代替：\",\n                    \"labels\": {\n                        \"forCopy\": \"為複製\",\n                        \"forCut\": \"為剪切\",\n                        \"forPaste\": \"為粘貼\"\n                    }\n                },\n                \"unsupportedSelectionDialog\": {\n                    \"errorMessage\": \"不能在多選的情況下執行該操作。\"\n                },\n                \"linkDialog\": {\n                    \"title\": \"連結\",\n                    \"labels\": {\n                        \"text\": \"連結文字\",\n                        \"url\": \"連結位址\",\n                        \"removeLink\": \"移除連結\"\n                    }\n                }\n            });\n    }\n\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\n        kendo.spreadsheet.messages.filterMenu =\n            $.extend(true, kendo.spreadsheet.messages.filterMenu,{\n                \"sortAscending\": \"按A到Z昇冪排列\",\n                \"sortDescending\": \"按Z到A降冪排列\",\n                \"filterByValue\": \"內容篩選\",\n                \"filterByCondition\": \"條件篩選\",\n                \"apply\": \"確定\",\n                \"search\": \"搜索\",\n                \"addToCurrent\": \"添加到當前選擇\",\n                \"clear\": \"清空\",\n                \"blanks\": \"（無）\",\n                \"operatorNone\": \"無\",\n                \"and\": \"並且\",\n                \"or\": \"或者\",\n                \"operators\": {\n                    \"string\": {\n                        \"contains\": \"文本包含\",\n                        \"doesnotcontain\": \"文本不包含\",\n                        \"startswith\": \"文本開頭是\",\n                        \"endswith\": \"文本結尾是\",\n                        \"matches\": \"文本等於\",\n                        \"doesnotmatch\": \"文本不等於\"\n                    },\n                    \"date\": {\n                        \"eq\":  \"日期等於\",\n                        \"neq\": \"日期不等於\",\n                        \"lt\":  \"日期早於\",\n                        \"gt\":  \"日期晚於\"\n                    },\n                    \"number\": {\n                        \"eq\": \"數字等於\",\n                        \"neq\": \"數字不等於\",\n                        \"gte\": \"數字大於等於\",\n                        \"gt\": \"數字大於\",\n                        \"lte\": \"數字小於等於\",\n                        \"lt\": \"數字小於\"\n                    }\n                }\n            });\n    }\n\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.colorPicker) {\n        kendo.spreadsheet.messages.colorPicker =\n            $.extend(true, kendo.spreadsheet.messages.colorPicker,{\n                \"reset\": \"無填充顏色\",\n                \"customColor\": \"其他顏色...\",\n                \"apply\": \"確定\",\n                \"cancel\": \"取消\"\n            });\n    }\n\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\n        kendo.spreadsheet.messages.toolbar =\n            $.extend(true, kendo.spreadsheet.messages.toolbar,{\n                \"addColumnLeft\": \"在左側插入列\",\n                \"addColumnRight\": \"在右側插入列\",\n                \"addRowAbove\": \"在上面插入行\",\n                \"addRowBelow\": \"在下麵插入行\",\n                \"alignment\": \"對齊\",\n                \"alignmentButtons\": {\n                    \"justtifyLeft\": \"左對齊\",\n                    \"justifyCenter\": \"水準居中\",\n                    \"justifyRight\": \"右對齊\",\n                    \"justifyFull\": \"兩端對齊\",\n                    \"alignTop\": \"頂端對齊\",\n                    \"alignMiddle\": \"垂直居中\",\n                    \"alignBottom\": \"底端對齊\"\n                },\n                \"backgroundColor\": \"背景顏色\",\n                \"bold\": \"粗體\",\n                \"borders\": \"邊框\",\n                \"colorPicker\": {\n                    \"reset\": \"無填充顏色\",\n                    \"customColor\": \"其他顏色...\"\n                },\n                \"copy\": \"複製\",\n                \"cut\": \"剪切\",\n                \"deleteColumn\": \"刪除整列\",\n                \"deleteRow\": \"刪除整行\",\n                \"excelImport\": \"從 Excel 導入...\",\n                \"exportAs\": \"匯出為...\",\n                \"filter\": \"篩選\",\n                \"fontFamily\": \"字體\",\n                \"fontSize\": \"字型大小\",\n                \"format\": \"定制格式...\",\n                \"formatTypes\": {\n                    \"automatic\": \"自動\",\n                    \"text\": \"文本\",\n                    \"number\": \"數字\",\n                    \"percent\": \"百分比\",\n                    \"financial\": \"財務\",\n                    \"currency\": \"貨幣\",\n                    \"date\": \"日期\",\n                    \"time\": \"時間\",\n                    \"dateTime\": \"日期時間\",\n                    \"duration\": \"時分秒\",\n                    \"moreFormats\": \"更多格式...\"\n                },\n                \"formatDecreaseDecimal\": \"減少小數位數\",\n                \"formatIncreaseDecimal\": \"增加小數位數\",\n                \"freeze\": \"凍結窗格\",\n                \"freezeButtons\": {\n                    \"freezePanes\": \"凍結上行左列\",\n                    \"freezeRows\": \"凍結上部行\",\n                    \"freezeColumns\": \"凍結左側列\",\n                    \"unfreeze\": \"取消凍結\"\n                },\n                \"italic\": \"斜體\",\n                \"merge\": \"合併儲存格\",\n                \"mergeButtons\": {\n                    \"mergeCells\": \"全部合併\",\n                    \"mergeHorizontally\": \"水準合併\",\n                    \"mergeVertically\": \"垂直合併\",\n                    \"unmerge\": \"取消合併\"\n                },\n                \"open\": \"打開...\",\n                \"paste\": \"粘貼\",\n                \"quickAccess\": {\n                    \"redo\": \"重做\",\n                    \"undo\": \"撤銷\"\n                },\n                \"toggleGridlines\": \"切換格線\",\n                \"saveAs\": \"另存為...\",\n                \"sort\": \"排序\",\n                \"sortAsc\": \"昇冪排列\",\n                \"sortDesc\": \"降冪排列\",\n                \"sortButtons\": {\n                    \"sortSheetAsc\": \"表格按A到Z昇冪排列\",\n                    \"sortSheetDesc\": \"表格按Z到A降冪排列\",\n                    \"sortRangeAsc\": \"選定範圍按A到Z昇冪排列\",\n                    \"sortRangeDesc\": \"選定範圍按Z到A降冪排列\"\n                },\n                \"textColor\": \"文字顏色\",\n                \"textWrap\": \"文字換行\",\n                \"underline\": \"底線\",\n                \"validation\": \"資料驗證...\",\n                \"hyperlink\": \"連結\"\n            });\n    }\n\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\n        kendo.spreadsheet.messages.view =\n            $.extend(true, kendo.spreadsheet.messages.view,{\n                \"errors\": {\n                    \"openUnsupported\": \"不支持的格式，請選擇一個尾碼名為 .xlsx 的文件。\",\n                    \"shiftingNonblankCells\": \"由於資料可能丟失無法插入儲存格，請選擇另一處插入或者從工作表的尾部刪除資料。\",\n                    \"insertColumnWhenRowIsSelected\": \"在選擇所有列時不能插入列。\",\n                    \"insertRowWhenColumnIsSelected\": \"在選擇所有行時不能插入行。\",\n                    \"filterRangeContainingMerges\": \"無法在包含合併儲存格的情況下進行篩選\",\n                    \"sortRangeContainingMerges\": \"無法在包含合併儲存格的情況下排序\",\n                    \"cantSortMultipleSelection\": \"無法在多選的情況下排序\",\n                    \"cantSortNullRef\": \"無法在沒有選擇的情況下排序\",\n                    \"cantSortMixedCells\": \"無法在包含有混合形狀儲存格的情況下排序\",\n                    \"validationError\": \"你輸入的值不符合目前的儲存格的驗證規則。\",\n                    \"cannotModifyDisabled\": \"不能修改被禁用的儲存格。\"\n                },\n                \"tabs\": {\n                    \"home\": \"開始\",\n                    \"insert\": \"插入\",\n                    \"data\": \"數據\"\n                }\n            });\n    }\n\n    /* Slider messages */\n    if (kendo.ui.Slider) {\n        kendo.ui.Slider.prototype.options =\n            $.extend(true, kendo.ui.Slider.prototype.options,{\n                \"increaseButtonTitle\": \"增加\",\n                \"decreaseButtonTitle\": \"減少\",\n                \"dragHandleTitle\": \"拖\"\n            });\n    }\n\n    /* ListBox messages */\n    if (kendo.ui.ListBox) {\n        kendo.ui.ListBox.prototype.options.messages =\n            $.extend(true, kendo.ui.ListBox.prototype.options.messages,{\n                \"tools\": {\n                    \"remove\": \"刪除\",\n                    \"moveUp\": \"上移\",\n                    \"moveDown\": \"下移\",\n                    \"transferTo\": \"轉移過去\",\n                    \"transferFrom\": \"轉移回來\",\n                    \"transferAllTo\": \"全部轉移過去\",\n                    \"transferAllFrom\": \"全部轉移回來\"\n                }\n            });\n    }\n\n    /* TreeList messages */\n    if (kendo.ui.TreeList) {\n        kendo.ui.TreeList.prototype.options.messages =\n            $.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n                \"noRows\": \"無相關資料\",\n                \"loading\": \"載入中...\",\n                \"requestFailed\": \"請求失敗！\",\n                \"retry\": \"重試\",\n                \"commands\": {\n                    \"edit\": \"編輯\",\n                    \"update\": \"更新\",\n                    \"canceledit\": \"取消\",\n                    \"create\": \"新增\",\n                    \"createchild\": \"新增子項\",\n                    \"destroy\": \"刪除\",\n                    \"excel\": \"匯出 Excel\",\n                    \"pdf\": \"匯出 PDF\"\n                }\n            });\n    }\n\n    /* TreeView messages */\n    if (kendo.ui.TreeView) {\n        kendo.ui.TreeView.prototype.options.messages =\n            $.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n                \"loading\": \"載入中...\",\n                \"requestFailed\": \"請求失敗！\",\n                \"retry\": \"重試\"\n            });\n    }\n\n    /* PanelBar messages */\n    if (kendo.ui.PanelBar) {\n        kendo.ui.PanelBar.prototype.options.messages =\n            $.extend(true, kendo.ui.PanelBar.prototype.options.messages,{\n                \"loading\": \"載入中...\",\n                \"requestFailed\": \"請求失敗！\",\n                \"retry\": \"重試\"\n            });\n    }\n\n    /* Upload messages */\n    if (kendo.ui.Upload) {\n        kendo.ui.Upload.prototype.options.localization=\n            $.extend(true, kendo.ui.Upload.prototype.options.localization,{\n                \"select\": \"選擇檔\",\n                \"cancel\": \"取消\",\n                \"retry\": \"重試\",\n                \"remove\": \"移除\",\n                \"pause\": \"暫停\",\n                \"resume\": \"恢復\",\n                \"clearSelectedFiles\": \"清空\",\n                \"uploadSelectedFiles\": \"上傳\",\n                \"dropFilesHere\": \"將文件拖拽到此處上傳\",\n                \"invalidFiles\": \"檔不符合要求！\",\n                \"statusUploading\": \"上傳中...\",\n                \"statusUploaded\": \"上傳成功！\",\n                \"statusWarning\": \"上傳警告！\",\n                \"statusFailed\": \"上傳失敗！\",\n                \"headerStatusUploading\": \"上傳中...\",\n                \"headerStatusPaused\": \"上傳暫停\",\n                \"headerStatusUploaded\": \"上傳完成！\",\n                \"invalidMaxFileSize\": \"文件太大！\",\n                \"invalidMinFileSize\": \"文件太小！\",\n                \"invalidFileExtension\": \"不支持的檔案格式！\"\n            });\n    }\n\n    /* Validator messages */\n    if (kendo.ui.Validator) {\n        kendo.ui.Validator.prototype.options.messages =\n            $.extend(true, kendo.ui.Validator.prototype.options.messages,{\n                \"required\": \"{0} 是必填項！\",\n                \"pattern\": \"{0} 的格式不正確！\",\n                \"min\": \"{0} 必須大於或等於 {1} ！\",\n                \"max\": \"{0} 必須小於或等於 {1} ！\",\n                \"step\": \"{0} 不是正確的步進值！\",\n                \"email\": \"{0} 不是正確的電子郵件格式！\",\n                \"url\": \"{0} 不是正確的網址格式！\",\n                \"date\": \"{0} 不是正確的日期格式！\",\n                \"dateCompare\": \"結束日期必須晚於或等於開始日期！\"\n            });\n    }\n\n    /* Progress messages */\n    if (kendo.ui.progress) {\n        kendo.ui.progress.messages =\n            $.extend(true, kendo.ui.progress.messages, {\n                \"loading\": \"載入中...\"\n            });\n    }\n\n    /* Dialog messages */\n    if (kendo.ui.Dialog) {\n        kendo.ui.Dialog.prototype.options.messages =\n            $.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n                \"close\": \"關閉\"\n            });\n    }\n\n    /* Calendar messages */\n    if (kendo.ui.Calendar) {\n        kendo.ui.Calendar.prototype.options.messages =\n            $.extend(true, kendo.ui.Calendar.prototype.options.messages, {\n                \"weekColumnHeader\": \"\"\n            });\n    }\n\n    /* Alert messages */\n    if (kendo.ui.Alert) {\n        kendo.ui.Alert.prototype.options.messages =\n            $.extend(true, kendo.ui.Alert.prototype.options.localization, {\n                \"okText\": \"確定\"\n            });\n    }\n\n    /* Confirm messages */\n    if (kendo.ui.Confirm) {\n        kendo.ui.Confirm.prototype.options.messages =\n            $.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n                \"okText\": \"確定\",\n                \"cancel\": \"取消\"\n            });\n    }\n\n    /* Prompt messages */\n    if (kendo.ui.Prompt) {\n        kendo.ui.Prompt.prototype.options.messages =\n            $.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n                \"okText\": \"確定\",\n                \"cancel\": \"取消\"\n            });\n    }\n\n    /* DateInput messages */\n    if (kendo.ui.DateInput) {\n        kendo.ui.DateInput.prototype.options.messages =\n            $.extend(true, kendo.ui.DateInput.prototype.options.messages, {\n                \"year\": \"年\",\n                \"month\": \"月\",\n                \"day\": \"日\",\n                \"weekday\": \"周\",\n                \"hour\": \"時\",\n                \"minute\": \"分\",\n                \"second\": \"秒\",\n                \"dayperiod\": \"上午/下午\"\n            });\n    }\n\n    /* DropDownTree messages */\n    if (kendo.ui.DropDownTree) {\n        kendo.ui.DropDownTree.prototype.options.messages =\n            $.extend(true, kendo.ui.DropDownTree.prototype.options.messages, {\n                clear: \"清空\",\n                deleteTag: \"刪除\",\n                singleTag: \"項已選擇\"\n            });\n    }\n\n    /* Chat messages */\n    if (kendo.ui.Chat) {\n        kendo.ui.Chat.prototype.options.messages =\n            $.extend(true, kendo.ui.Chat.prototype.options.messages, {\n                placeholder: \"請輸入...\"\n            });\n    }\n\n    /* Switch messages */\n    if (kendo.ui.Switch) {\n        kendo.ui.Switch.prototype.options.messages =\n            $.extend(true, kendo.ui.Switch.prototype.options.messages,{\n                \"checked\": \"開\",\n                \"unchecked\": \"關\"\n            });\n    }\n\n    /* Mobile Messages ------------------------------ */\n\n    /* Mobile Scroller messages */\n    if (kendo.mobile.ui.Scroller) {\n        kendo.mobile.ui.Scroller.prototype.options.messages =\n            $.extend(true, kendo.mobile.ui.Scroller.prototype.options.messages, {\n                \"pullTemplate\": \"下拉刷新\",\n                \"releaseTemplate\": \"釋放刷新\",\n                \"refreshTemplate\": \"刷新中...\"\n            });\n    }\n\n    /* Mobile ListView messages */\n    if (kendo.mobile.ui.ListView) {\n        kendo.mobile.ui.ListView.prototype.options.messages =\n            $.extend(true, kendo.mobile.ui.ListView.prototype.options.messages, {\n                \"loadMoreText\": \"點擊載入更多\",\n                \"pullTemplate\": \"下拉刷新\",\n                \"releaseTemplate\": \"釋放刷新\",\n                \"refreshTemplate\": \"刷新中...\"\n            });\n    }\n\n})(window.kendo.jQuery);\n}));"]}