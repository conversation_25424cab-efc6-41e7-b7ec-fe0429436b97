{"version": 3, "sources": ["messages/kendo.messages.sk-SK.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "isnullorempty", "isnotnullorempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "text", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "toolbar", "alignment", "alignmentButtons", "backgroundColor", "borders", "colorPicker", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "TreeView", "Upload", "localization", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "Dialog", "close", "<PERSON><PERSON>", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,SACTC,OAAU,YAMRR,MAAMC,GAAGQ,cACbT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,SACTC,OAAU,YAMRR,MAAMC,GAAGS,aACbV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,uBACjBC,eAAkB,sBAClBC,OAAU,SACVC,QAAW,SACXC,KAAQ,SACRC,SAAY,oBACZC,KAAQ,UACRC,OAAU,cAMRlB,MAAMC,GAAGkB,SACbnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,QACRC,OAAU,UACVC,UAAa,eACbC,cAAiB,cACjBC,YAAe,cACfC,UAAa,cACbC,cAAiB,oBACjBC,YAAe,iBACfC,aAAgB,kBAChBC,YAAe,oBACfC,oBAAuB,0BACvBC,kBAAqB,0BACrBC,OAAU,oBACVC,QAAW,oBACXC,WAAc,eACdC,OAAU,kBACVC,YAAe,iBACfC,WAAc,eACdC,WAAc,cACdC,SAAY,YACZC,SAAY,gBACZC,gBAAmB,qBACnBC,SAAY,wBACZC,gBAAmB,uBACnBC,YAAe,SACfC,WAAc,eACdC,UAAa,QACbC,UAAa,gBACbC,MAAS,QACTC,YAAe,oBACfC,WAAc,SACdC,QAAW,oBACXC,YAAe,WACfC,YAAe,QACfC,gBAAmB,qEACnBC,WAAc,iCACdC,cAAiB,8EACjBC,kBAAqB,wCACrBC,gBAAmB,QACnBC,aAAgB,YAChBC,WAAc,aACdC,YAAe,aACfC,eAAkB,QAClBC,UAAa,QACbC,eAAkB,QAClBC,SAAY,OACZC,YAAe,MACfC,oBAAuB,6BACvBC,aAAgB,SAChBC,aAAgB,SAChBC,sBAAyB,QACzBC,aAAgB,SAChBC,YAAe,iBACfC,cAAiB,sBACjBC,eAAkB,uBAClBC,YAAe,oBACfC,YAAe,oBACfC,UAAa,mBACbC,aAAgB,sBAMd9E,MAAMC,GAAG8E,cACb/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpD6C,WAAc,UACdC,QAAW,mBACXE,YAAe,QACfD,YAAe,WACfK,kBAAqB,wCACrBR,YAAe,oBACfM,WAAc,iCACdD,gBAAmB,qEACnBE,cAAiB,8EACjBwB,cAAiB,8CACjBC,OAAU,YAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACnD8E,OAAU,YACVC,QAAW,gBACXvE,OAAU,YACVwE,MAAS,WACTC,SAAY,cAMVtF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,KACNC,IAAO,SACPC,WAAc,WACdC,SAAY,WACZC,eAAkB,aAClBC,SAAY,UACZC,OAAU,UACVC,UAAa,cACbC,QAAW,aACXC,WAAc,iBACdC,cAAiB,eACjBC,iBAAoB,cAEtBC,QACEZ,GAAM,WACNC,IAAO,aACPY,IAAO,2BACPC,GAAM,gBACNC,IAAO,2BACPC,GAAM,gBACNV,OAAU,UACVC,UAAa,eAEfU,MACEjB,GAAM,KACNC,IAAO,SACPY,IAAO,qBACPC,GAAM,YACNC,IAAO,sBACPC,GAAM,aACNV,OAAU,UACVC,UAAa,eAEfW,OACElB,GAAM,KACNC,IAAO,SACPK,OAAU,UACVC,UAAa,kBAObhG,MAAMC,GAAG2G,aACb5G,MAAMC,GAAG2G,WAAWzG,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2G,WAAWzG,UAAUC,QAAQC,UACnDwG,KAAQ,sCACRC,MAAS,qCACT3B,OAAU,YACVC,QAAW,gBACXvE,OAAU,YACVwE,MAAS,WACT0B,IAAO,YACPC,GAAM,QACNC,YAAe,oBACf3B,SAAY,WACZ4B,MAAS,UACT1G,OAAU,YAMRR,MAAMC,GAAG2G,aACb5G,MAAMC,GAAG2G,WAAWzG,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2G,WAAWzG,UAAUC,QAAQmF,WACnDC,QACIC,GAAM,KACNC,IAAO,SACPC,WAAc,WACdC,SAAY,WACZC,eAAkB,aAClBC,SAAY,UACZC,OAAU,UACVC,UAAa,cACbC,QAAW,aACXC,WAAc,iBACdC,cAAiB,eACjBC,iBAAoB,cAExBC,QACIZ,GAAM,WACNC,IAAO,aACPY,IAAO,2BACPC,GAAM,gBACNC,IAAO,2BACPC,GAAM,gBACNV,OAAU,UACVC,UAAa,eAEjBU,MACIjB,GAAM,KACNC,IAAO,SACPY,IAAO,qBACPC,GAAM,YACNC,IAAO,sBACPC,GAAM,aACNV,OAAU,UACVC,UAAa,eAEjBW,OACIlB,GAAM,KACNC,IAAO,SACPK,OAAU,UACVC,UAAa,kBAOfhG,MAAMC,GAAGkH,mBACTnH,MAAMC,GAAGkH,iBAAiBhH,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkH,iBAAiBhH,UAAUC,QAAQC,UACvD+G,SAAY,SACZ/B,MAAS,WACTxE,OAAU,YACVoE,OAAU,YAMdjF,MAAMC,GAAGoH,QACbrH,MAAMC,GAAGoH,MAAMlH,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoH,MAAMlH,UAAUC,QAAQC,UAC9CiH,SACEC,SAAY,kBACZC,OAAU,eACVC,YAAe,YACfC,aAAgB,cAChBC,IAAO,qBAETnH,OAAU,SACVoH,4BAA+B,yBAC/BC,sBAAyB,oBACzBC,QAAW,YACXC,QACEC,aAAgB,WAChBC,YAAe,QACfC,IAAO,SACPC,gBAAmB,SACnBC,UAAa,SACbC,qBAAwB,SACxBC,gBAAmB,SACnBC,MAAS,WACTzB,MAAS,QACT0B,YAAe,YAEjBC,KAAQ,SACRC,OACEC,IAAO,MACPT,IAAO,SACPU,MAAS,SACTL,MAAS,WACTM,KAAQ,SACRC,KAAQ,UAOR9I,MAAMC,GAAG8I,OACb/I,MAAMC,GAAG8I,KAAK5I,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8I,KAAK5I,UAAUC,QAAQC,UAC7C2I,UACExI,OAAU,gBACVyI,WAAc,SACdC,OAAU,qBACVpB,QAAW,YACXqB,KAAQ,UACRC,MAAS,uBACTzB,IAAO,oBACPc,KAAQ,eACRY,OAAU,SACVC,OAAU,UAEZC,UACEC,aAAgB,SAChBC,aAAgB,wCAChBC,cAAiB,aAEnBC,UAAa,qBAMX3J,MAAMC,GAAG2J,YACb5J,MAAMC,GAAG2J,UAAUzJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,UAAUzJ,UAAUC,QAAQC,UAChDwJ,MAAS,6DAMT7J,MAAMC,GAAG6J,iBACb9J,MAAMC,GAAG6J,eAAe3J,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6J,eAAe3J,UAAUC,SAC/C2J,YAAe,iBACfC,cAAiB,oBAMfhK,MAAMC,GAAGgK,QACbjK,MAAMC,GAAGgK,MAAM9J,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgK,MAAM9J,UAAUC,QAAQC,UAC9C6J,SAAY,SACZC,QAAW,2BACXN,MAAS,8BACTO,KAAQ,SACRC,GAAM,QACNC,aAAgB,qBAChBC,MAAS,wBACTC,SAAY,4BACZC,KAAQ,0BACRC,KAAQ,4BACRC,QAAW,UACXC,UAAa,mBAMX5K,MAAMC,GAAG4K,gBACb7K,MAAMC,GAAG4K,cAAc1K,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4K,cAAc1K,UAAUC,QAAQC,UACtD6J,SAAY,SACZC,QAAW,2BACXN,MAAS,8BACTO,KAAQ,SACRC,GAAM,QACNC,aAAgB,qBAChBC,MAAS,wBACTC,SAAY,4BACZC,KAAQ,0BACRC,KAAQ,4BACRC,QAAW,UACXC,UAAa,mBAMX5K,MAAMC,GAAG6K,YACb9K,MAAMC,GAAG6K,UAAU3K,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6K,UAAU3K,UAAUC,QAAQC,UAClD0K,cAAiB,8BACjBC,aAAgB,+BAChBC,UAAa,kCAMXjL,MAAMC,GAAGiL,iBACblL,MAAMC,GAAGiL,eAAe/K,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiL,eAAe/K,UAAUC,QAAQC,UACvDwG,KAAQ,sCACRsE,aAAgB,cAChBtK,OAAU,SACVuK,QAAW,mBACXtE,MAAS,qBACTzB,MAAS,WACTgG,GAAM,KACN7K,OAAU,SACV+E,WACEK,SAAY,WACZC,eAAkB,aAClBF,WAAc,WACdG,SAAY,UACZL,GAAM,WACNC,IAAO,iBAOP1F,MAAMC,GAAGqL,mBACbtL,MAAMC,GAAGqL,iBAAiBnL,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqL,iBAAiBnL,UAAUC,QAAQC,UACzDkL,aACEC,MAAS,QACTC,OAAU,eACVC,MAAS,QACTC,OAAU,WACVC,QAAW,UACXC,OAAU,SAEZJ,QACEK,YAAe,mBACfC,SAAY,kBAEdL,OACEI,YAAe,mBACfC,SAAY,aAEdJ,QACEI,SAAY,mBACZD,YAAe,mBACfE,SAAY,cAEdJ,SACEE,YAAe,mBACfE,SAAY,aACZD,SAAY,oBACZpD,IAAO,QAETkD,QACEC,YAAe,mBACfE,SAAY,aACZD,SAAY,cACZ1B,GAAM,OAERnC,KACE+D,MAAS,UACTC,YAAe,UACfV,MAAS,QACTW,MAAS,MACTC,WAAc,oBACdC,GAAM,OAERC,iBACE/B,MAAS,OACTgC,OAAU,QACVC,MAAS,QACTC,OAAU,SACV/B,KAAQ,YAEVgC,UACE/D,IAAO,MACPgE,QAAW,eACXC,QAAW,aAOX5M,MAAMC,GAAG4M,YACb7M,MAAMC,GAAG4M,UAAU1M,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4M,UAAU1M,UAAUC,QAAQC,UAClDyM,OAAU,WACVpG,KAAQ,QACRqG,MAAS,UACTC,KAAQ,MACRC,YAAe,oBACfC,YAAe,wBACfC,MAAS,OACT1E,KAAQ,SACRjI,OAAU,SACVsH,QAAW,YACXsF,kBAAqB,uBACrBC,cAAiB,4BACjBC,eAAkB,wBAClB/D,UACEE,aAAgB,yCAElBf,OACEC,IAAO,MACPE,KAAQ,SACR0E,SAAY,kBACZC,OAAU,SACV5E,MAAS,UAEX6E,oBACEL,kBAAqB,kCACrBM,uBAA0B,6BAC1BC,mBAAsB,mBACtBC,gBAAmB,6BACnBC,qBAAwB,2BACxBC,iBAAoB,iBACpBC,gBAAmB,oEACnBC,cAAiB,mEAEnBjG,QACEjB,MAAS,QACTyB,MAAS,WACTL,IAAO,SACP+F,YAAe,YACfC,YAAe,QACfC,OAAU,WACVC,SAAY,IACZC,cAAiB,wBACjBC,YAAe,qBACfC,kBAAqB,2CACrBC,oBAAuB,eACvBC,qBAAwB,eACxBC,cAAiB,eACjBC,WAAc,qBACd1G,YAAe,cAOfjI,MAAM4O,aAAe5O,MAAM4O,YAAYvO,SAASwO,gBACpD7O,MAAM4O,YAAYvO,SAASwO,cAC3B/O,EAAEQ,QAAO,EAAMN,MAAM4O,YAAYvO,SAASwO,eACtCC,WAAc,oBACdC,cAAiB,sBACjBC,wBAA2B,gCAC3BC,sBAAyB,6BACzBC,eAAkB,uBAClBC,WAAc,kBACdC,UAAa,mBACbC,YAAe,mBACfC,aAAgB,mBAChBC,UAAa,iBACbC,MAAS,gBACTC,YAAe,mBACflP,MAAS,SACTC,OAAU,YAIVR,MAAM4O,aAAe5O,MAAM4O,YAAYvO,SAASqP,UACpD1P,MAAM4O,YAAYvO,SAASqP,QAC3B5P,EAAEQ,QAAO,EAAMN,MAAM4O,YAAYvO,SAASqP,SACtCnP,MAAS,SACTkI,KAAQ,SACRjI,OAAU,SACVmP,OAAU,YACVC,OAAU,KACVC,mBACI/I,MAAS,eACTgJ,YACIzJ,OAAU,QACV0J,SAAY,OACZrJ,KAAQ,UAGhBsJ,kBACIlJ,MAAS,SAEbmJ,gBACInJ,MAAS,iBAEboJ,eACIpJ,MAAS,cAEbqJ,iBACIrJ,MAAS,aACTsJ,SACIC,aAAgB,iBAChB3O,cAAiB,YACjBE,aAAgB,kBAChBC,YAAe,oBACfyO,SAAY,iBACZC,YAAe,oBACfC,YAAe,mBAGvBC,aACI3J,MAAS,kBACTsJ,SACIM,WAAc,gBACdC,kBAAqB,mBACrBC,gBAAmB,gBACnBC,QAAW,aAGnBC,cACIhK,MAAS,oBACTsJ,SACIW,YAAe,iBACfC,WAAc,iBACdC,cAAiB,iBACjBC,SAAY,qBAGpBC,kBACIrK,MAAS,kBACTsK,YAAe,yCACfC,UAAa,eACbC,UACIC,IAAO,oBACPlL,OAAU,QACVmL,KAAQ,OACR9K,KAAQ,QACR+K,OAAU,iBACVC,KAAQ,UAEZC,WACIC,YAAe,aACfC,SAAY,aACZC,QAAW,QACXC,WAAc,YACdC,QAAW,WACXC,WAAc,aACdC,qBAAwB,wBACxBC,kBAAqB,yBAEzBC,kBACIR,YAAe,iBACfC,SAAY,iBACZC,QAAW,kBACXC,WAAc,sBACdC,QAAW,eACXC,WAAc,iBACdC,qBAAwB,4BACxBC,kBAAqB,4BACrBV,OAAU,uBAEdY,QACIf,SAAY,WACZgB,SAAY,aACZC,IAAO,MACPC,IAAO,MACPtL,MAAS,UACTqB,MAAS,WACTL,IAAO,SACPuK,cAAiB,uBACjBC,YAAe,kBACfC,YAAe,qBACfC,SAAY,mBACZvB,UAAa,kBACbD,YAAe,iBACfyB,YAAe,qBAEnBC,cACIC,UAAa,eACbC,YAAe,gBAGvBC,gBACInM,MAAS,gBACTuL,QACIa,SAAY,cACZC,WAAc,iBACdC,WAAc,aACdC,UAAa,kBACbC,QAAW,SACXC,YAAe,aACfC,MAAS,SACTC,WAAc,gBACdC,OAAU,YACVC,aAAgB,YAChBC,WAAc,WAGtBC,oBACIC,aAAgB,uCAEpBC,mBACIjN,MAAS,0BACTgN,aAAgB,uFAChBzB,QACI2B,QAAW,iBACXC,OAAU,kBACVC,SAAY,gBAGpBC,4BACIL,aAAgB,6DAKpB9T,MAAM4O,aAAe5O,MAAM4O,YAAYvO,SAAS+T,aACpDpU,MAAM4O,YAAYvO,SAAS+T,WAC3BtU,EAAEQ,QAAO,EAAMN,MAAM4O,YAAYvO,SAAS+T,YACtCzT,cAAiB,6BACjBC,eAAkB,6BAClByT,cAAiB,0BACjBC,kBAAqB,4BACrB/T,MAAS,SACT0E,OAAU,SACVsP,aAAgB,8BAChBlP,MAAS,WACTmP,OAAU,YACVC,aAAgB,SAChB1N,IAAO,IACPC,GAAM,QACNzB,WACIC,QACII,SAAY,gBACZC,eAAkB,kBAClBF,WAAc,gBACdG,SAAY,gBAEhBY,MACIjB,GAAM,WACNC,IAAO,eACPe,GAAM,gBACNF,GAAM,eAEVF,QACIZ,GAAM,WACNC,IAAO,aACPY,IAAO,2BACPC,GAAM,gBACNC,IAAO,2BACPC,GAAM,qBAMdzG,MAAM4O,aAAe5O,MAAM4O,YAAYvO,SAASqU,UACpD1U,MAAM4O,YAAYvO,SAASqU,QAC3B5U,EAAEQ,QAAO,EAAMN,MAAM4O,YAAYvO,SAASqU,SACtCjQ,cAAiB,sBACjBC,eAAkB,uBAClBC,YAAe,oBACfC,YAAe,oBACf+P,UAAa,aACbC,kBACIvE,aAAgB,iBAChB3O,cAAiB,YACjBE,aAAgB,kBAChBC,YAAe,oBACfyO,SAAY,iBACZC,YAAe,oBACfC,YAAe,kBAEnBqE,gBAAmB,UACnBzT,KAAQ,QACR0T,QAAW,aACXC,aACIvF,MAAS,gBACTC,YAAe,oBAEnBuF,KAAQ,YACRC,IAAO,aACPnQ,aAAgB,mBAChBD,UAAa,mBACbqQ,YAAe,yBACfrU,OAAU,YACVsU,WAAc,QACdzS,SAAY,gBACZ0S,OAAU,oBACVC,aACIC,UAAa,cACbjP,OAAU,QACVkP,QAAW,WACXC,UAAa,WACbzF,SAAY,OACZrJ,KAAQ,QACRsG,KAAQ,MACRyI,SAAY,cACZC,SAAY,cACZC,YAAe,oBAEnBC,sBAAyB,yBACzBC,sBAAyB,0BACzBC,OAAU,iBACVC,eACIhF,YAAe,iBACfC,WAAc,iBACdC,cAAiB,iBACjBC,SAAY,oBAEhB7P,OAAU,QACV2U,MAAS,eACTC,cACIvF,WAAc,gBACdC,kBAAqB,mBACrBC,gBAAmB,gBACnBC,QAAW,YAEfqF,KAAQ,aACRC,MAAS,SACTC,aACIC,KAAQ,QACRC,KAAQ,QAEZC,OAAU,gBACVC,QAAW,uBACXC,SAAY,sBACZC,aACIC,aAAgB,4BAChBC,cAAiB,4BACjBC,aAAgB,6BAChBC,cAAiB,gCAErBC,UAAa,cACbC,SAAY,eACZ1V,UAAa,eACb2V,WAAc,wBAIdjX,MAAM4O,aAAe5O,MAAM4O,YAAYvO,SAAS6W,OACpDlX,MAAM4O,YAAYvO,SAAS6W,KAC3BpX,EAAEQ,QAAO,EAAMN,MAAM4O,YAAYvO,SAAS6W,MACtCC,QACIC,sBAAyB,sHACzBC,4BAA+B,6DAC/BC,gBAAmB,8EAEvBC,MACIC,KAAQ,QACRC,OAAU,SACVC,KAAQ,YAOZ1X,MAAMC,GAAG0X,SACb3X,MAAMC,GAAG0X,OAAOxX,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0X,OAAOxX,UAAUC,SACvCwX,oBAAuB,SACvBC,oBAAuB,YAMrB7X,MAAMC,GAAG6X,WACb9X,MAAMC,GAAG6X,SAAS3X,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6X,SAAS3X,UAAUC,QAAQC,UAC/C0X,OAAU,+BACVC,QAAW,gBACXC,cAAiB,sBACjBC,MAAS,QACTlP,UACIG,KAAQ,UACRG,OAAU,SACVL,WAAc,SACdC,OAAU,qBACViP,YAAe,mBACfrQ,QAAW,YACXsB,MAAS,uBACTzB,IAAO,wBAOX3H,MAAMC,GAAGmY,WACbpY,MAAMC,GAAGmY,SAASjY,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmY,SAASjY,UAAUC,QAAQC,UACjD2X,QAAW,gBACXC,cAAiB,sBACjBC,MAAS,WAMPlY,MAAMC,GAAGoY,SACbrY,MAAMC,GAAGoY,OAAOlY,UAAUC,QAAQkY,aAClCxY,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoY,OAAOlY,UAAUC,QAAQkY,cAC/CjP,OAAU,oBACV7I,OAAU,SACV0X,MAAS,QACTvI,OAAU,YACV4I,oBAAuB,iBACvBvT,cAAiB,8CACjBwT,gBAAmB,cACnBC,eAAkB,SAClBC,cAAiB,YACjBC,aAAgB,WAChBC,sBAAyB,iBACzBC,qBAAwB,YAMtB7Y,MAAMC,GAAG6Y,YACb9Y,MAAMC,GAAG6Y,UAAU3Y,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6Y,UAAU3Y,UAAUC,QAAQC,UAClD0Y,SAAY,yBACZC,QAAW,yBACXzG,IAAO,yBACPC,IAAO,2BACPyG,KAAQ,yBACRC,MAAS,0BACTC,IAAO,+BACPzS,KAAQ,0BACR0S,YAAe,6DAMbpZ,MAAMC,GAAGoZ,SACbrZ,MAAMC,GAAGoZ,OAAOlZ,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoZ,OAAOlZ,UAAUC,QAAQkY,cAC/CgB,MAAS,aAMPtZ,MAAMC,GAAGsZ,QACbvZ,MAAMC,GAAGsZ,MAAMpZ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsZ,MAAMpZ,UAAUC,QAAQkY,cAC9C1I,OAAU,QAMR5P,MAAMC,GAAGuZ,UACbxZ,MAAMC,GAAGuZ,QAAQrZ,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuZ,QAAQrZ,UAAUC,QAAQkY,cAChD1I,OAAU,KACVpP,OAAU,YAKRR,MAAMC,GAAGwZ,SACbzZ,MAAMC,GAAGwZ,OAAOtZ,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwZ,OAAOtZ,UAAUC,QAAQkY,cAC/C1I,OAAU,KACVpP,OAAU,aAITkZ,OAAO1Z,MAAM2Z", "file": "kendo.messages.sk-SK.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Použiť\",\n  \"cancel\": \"Storno\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"Použiť\",\n  \"cancel\": \"Storno\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Usporiadať vzostupne\",\n  \"sortDescending\": \"Usporiadať zostupne\",\n  \"filter\": \"Filter\",\n  \"columns\": \"Stĺpce\",\n  \"done\": \"Hotovo\",\n  \"settings\": \"Nastavenia stĺpca\",\n  \"lock\": \"Zamknúť\",\n  \"unlock\": \"Odomknúť\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Tučné\",\n  \"italic\": \"Kurzíva\",\n  \"underline\": \"Podčiarknuté\",\n  \"strikethrough\": \"Preškrtnuté\",\n  \"superscript\": \"Horný index\",\n  \"subscript\": \"Dolný index\",\n  \"justifyCenter\": \"Zarovnať na stred\",\n  \"justifyLeft\": \"Zarovnať vľavo\",\n  \"justifyRight\": \"Zarovnať vpravo\",\n  \"justifyFull\": \"Zarovnať do bloku\",\n  \"insertUnorderedList\": \"Vložiť odrážkový zoznam\",\n  \"insertOrderedList\": \"Vložiť číslovaný zoznam\",\n  \"indent\": \"Zväčšiť odsadenie\",\n  \"outdent\": \"Zmenšiť odsadenie\",\n  \"createLink\": \"Vložiť odkaz\",\n  \"unlink\": \"Odstrániť odkaz\",\n  \"insertImage\": \"Vložiť obrázok\",\n  \"insertFile\": \"Vložiť súbor\",\n  \"insertHtml\": \"Vložiť HTML\",\n  \"viewHtml\": \"View HTML\",\n  \"fontName\": \"Vyberte písmo\",\n  \"fontNameInherit\": \"(predvolené písmo)\",\n  \"fontSize\": \"Vyberte veľkosť písma\",\n  \"fontSizeInherit\": \"(predvolená veľkosť)\",\n  \"formatBlock\": \"Formát\",\n  \"formatting\": \"Formátovanie\",\n  \"foreColor\": \"Farba\",\n  \"backColor\": \"Farba pozadia\",\n  \"style\": \"Štýly\",\n  \"emptyFolder\": \"Prázdny priečinok\",\n  \"uploadFile\": \"Nahrať\",\n  \"orderBy\": \"Usporiadať podľa:\",\n  \"orderBySize\": \"Veľkosti\",\n  \"orderByName\": \"Názvu\",\n  \"invalidFileType\": \"Vybraný súbor \\\"{0}\\\" nie je podporovaný. Podporované súbory sú {1}.\",\n  \"deleteFile\": 'Naozaj chcete odstrániť \"{0}\"?',\n  \"overwriteFile\": 'Súbor s názvom \"{0}\" už vo vybratom priečinku existuje. Chcete ho nahradiť?',\n  \"directoryNotFound\": \"Priečinok s týmto názvom sa nenašiel.\",\n  \"imageWebAddress\": \"Odkaz\",\n  \"imageAltText\": \"Alt. text\",\n  \"imageWidth\": \"Šírka (px)\",\n  \"imageHeight\": \"Výška (px)\",\n  \"fileWebAddress\": \"Odkaz\",\n  \"fileTitle\": \"Názov\",\n  \"linkWebAddress\": \"Odkaz\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"Tip\",\n  \"linkOpenInNewWindow\": \"Otvoriť odkaz v novom okne\",\n  \"dialogUpdate\": \"Uložiť\",\n  \"dialogInsert\": \"Vložiť\",\n  \"dialogButtonSeparator\": \"alebo\",\n  \"dialogCancel\": \"Storno\",\n  \"createTable\": \"Vložiť tabuľku\",\n  \"addColumnLeft\": \"Pridať stĺpec vľavo\",\n  \"addColumnRight\": \"Pridať stĺpec vpravo\",\n  \"addRowAbove\": \"Pridať riadok nad\",\n  \"addRowBelow\": \"Pridať riadok pod\",\n  \"deleteRow\": \"Odstrániť riadok\",\n  \"deleteColumn\": \"Odstrániť stĺpec\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Odoslať\",\n  \"orderBy\": \"Usporiadať podľa\",\n  \"orderByName\": \"Názvu\",\n  \"orderBySize\": \"Veľkosti\",\n  \"directoryNotFound\": \"Priečinok s týmto názvom sa nenašiel.\",\n  \"emptyFolder\": \"Prázdny priečinok\",\n  \"deleteFile\": 'Naozaj chcete odstrániť \"{0}\"?',\n  \"invalidFileType\": \"Vybraný súbor \\\"{0}\\\" nie je podporovaný. Podporované súbory sú {1}.\",\n  \"overwriteFile\": \"Súbor s názvom \\\"{0}\\\" už vo vybratom priečinku existuje. Chcete ho nahradiť?\",\n  \"dropFilesHere\": \"Potiahnite sem súbory, ktoré chcete odoslať\",\n  \"search\": \"Hľadať\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"je pravda\",\n  \"isFalse\": \"nie je pravda\",\n  \"filter\": \"Filtrovať\",\n  \"clear\": \"Vyčistiť\",\n  \"operator\": \"Operátor\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Je\",\n    \"neq\": \"Nie je\",\n    \"startswith\": \"Začína s\",\n    \"contains\": \"Obsahuje\",\n    \"doesnotcontain\": \"Neobsahuje\",\n    \"endswith\": \"Končí s\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Nie je null\",\n    \"isempty\": \"Je prázdne\",\n    \"isnotempty\": \"Nie je prázdne\",\n    \"isnullorempty\": \"Nemá hodnotu\",\n    \"isnotnullorempty\": \"Má hodnotu\"\n  },\n  \"number\": {\n    \"eq\": \"Rovná sa\",\n    \"neq\": \"Nerovná sa\",\n    \"gte\": \"Je väčšie alebo sa rovná\",\n    \"gt\": \"Je väčšie ako\",\n    \"lte\": \"Je menšie alebo sa rovná\",\n    \"lt\": \"Je menšie ako\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Nie je null\"\n  },\n  \"date\": {\n    \"eq\": \"Je\",\n    \"neq\": \"Nie je\",\n    \"gte\": \"Nasleduje alebo je\",\n    \"gt\": \"Nasleduje\",\n    \"lte\": \"Predchádza alebo je\",\n    \"lt\": \"Predchádza\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Nie je null\"\n  },\n  \"enums\": {\n    \"eq\": \"Je\",\n    \"neq\": \"Nie je\",\n    \"isnull\": \"Je null\",\n    \"isnotnull\": \"Nie je null\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Zobraziť záznamy s hodnotou, ktorá:\",\n  \"title\": \"Zobraziť záznamy s hodnotou, ktorá\",\n  \"isTrue\": \"je pravda\",\n  \"isFalse\": \"nie je pravda\",\n  \"filter\": \"Filtrovať\",\n  \"clear\": \"Vyčistiť\",\n  \"and\": \"A zároveň\",\n  \"or\": \"Alebo\",\n  \"selectValue\": \"-Vyberte hodnotu-\",\n  \"operator\": \"Operátor\",\n  \"value\": \"Hodnota\",\n  \"cancel\": \"Storno\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n      \"eq\": \"Je\",\n      \"neq\": \"Nie je\",\n      \"startswith\": \"Začína s\",\n      \"contains\": \"Obsahuje\",\n      \"doesnotcontain\": \"Neobsahuje\",\n      \"endswith\": \"Končí s\",\n      \"isnull\": \"Je null\",\n      \"isnotnull\": \"Nie je null\",\n      \"isempty\": \"Je prázdne\",\n      \"isnotempty\": \"Nie je prázdne\",\n      \"isnullorempty\": \"Nemá hodnotu\",\n      \"isnotnullorempty\": \"Má hodnotu\"\n  },\n  \"number\": {\n      \"eq\": \"Rovná sa\",\n      \"neq\": \"Nerovná sa\",\n      \"gte\": \"Je väčšie alebo sa rovná\",\n      \"gt\": \"Je väčšie ako\",\n      \"lte\": \"Je menšie alebo sa rovná\",\n      \"lt\": \"Je menšie ako\",\n      \"isnull\": \"Je null\",\n      \"isnotnull\": \"Nie je null\"\n  },\n  \"date\": {\n      \"eq\": \"Je\",\n      \"neq\": \"Nie je\",\n      \"gte\": \"Nasleduje alebo je\",\n      \"gt\": \"Nasleduje\",\n      \"lte\": \"Predchádza alebo je\",\n      \"lt\": \"Predchádza\",\n      \"isnull\": \"Je null\",\n      \"isnotnull\": \"Nie je null\"\n  },\n  \"enums\": {\n      \"eq\": \"Je\",\n      \"neq\": \"Nie je\",\n      \"isnull\": \"Je null\",\n      \"isnotnull\": \"Nie je null\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\n    kendo.ui.FilterMultiCheck.prototype.options.messages =\n    $.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n        \"checkAll\": \"Všetky\",\n        \"clear\": \"Vyčistiť\",\n        \"filter\": \"Filtrovať\",\n        \"search\": \"Hľadať\"\n    });\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Pridať podúlohu\",\n    \"append\": \"Pridať úlohu\",\n    \"insertAfter\": \"Vložiť za\",\n    \"insertBefore\": \"Vložiť pred\",\n    \"pdf\": \"Exportovať do PDF\"\n  },\n  \"cancel\": \"Storno\",\n  \"deleteDependencyWindowTitle\": \"Odstránenie závislosti\",\n  \"deleteTaskWindowTitle\": \"Odstránenie úlohy\",\n  \"destroy\": \"Odstrániť\",\n  \"editor\": {\n    \"assingButton\": \"Priradiť\",\n    \"editorTitle\": \"Úloha\",\n    \"end\": \"Koniec\",\n    \"percentComplete\": \"Hotovo\",\n    \"resources\": \"Zdroje\",\n    \"resourcesEditorTitle\": \"Zdroje\",\n    \"resourcesHeader\": \"Zdroje\",\n    \"start\": \"Začiatok\",\n    \"title\": \"Názov\",\n    \"unitsHeader\": \"Jednotky\"\n  },\n  \"save\": \"Uložiť\",\n  \"views\": {\n    \"day\": \"Deň\",\n    \"end\": \"Koniec\",\n    \"month\": \"Mesiac\",\n    \"start\": \"Začiatok\",\n    \"week\": \"Týždeň\",\n    \"year\": \"Rok\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Zahodiť zmeny\",\n    \"canceledit\": \"Storno\",\n    \"create\": \"Pridať nový záznam\",\n    \"destroy\": \"Odstrániť\",\n    \"edit\": \"Upraviť\",\n    \"excel\": \"Exportovať do Excelu\",\n    \"pdf\": \"Exportovať do PDF\",\n    \"save\": \"Uložiť zmeny\",\n    \"select\": \"Vybrať\",\n    \"update\": \"Uložiť\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Storno\",\n    \"confirmation\": \"Naozaj chcete odstrániť tento záznam?\",\n    \"confirmDelete\": \"Odstrániť\"\n  },\n  \"noRecords\": \"Žiadne záznamy.\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n    \"empty\": \"Potiahnite sem záhlavie stĺpca na zoskupenie podľa neho\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Zvýšiť hodnotu\",\n  \"downArrowText\": \"Znížiť hodnotu\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Všetko\",\n  \"display\": \"{0} - {1} z {2} záznamov\",\n  \"empty\": \"Žiadny záznam na zobrazenie\",\n  \"page\": \"Strana\",\n  \"of\": \"z {0}\",\n  \"itemsPerPage\": \"záznamov na stranu\",\n  \"first\": \"Prejsť na prvú stranu\",\n  \"previous\": \"Prejsť na predošlú stranu\",\n  \"next\": \"Prejsť na ďalšiu stranu\",\n  \"last\": \"Prejsť na poslednú stranu\",\n  \"refresh\": \"Obnoviť\",\n  \"morePages\": \"Ďalšie strany\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"Všetko\",\n  \"display\": \"{0} - {1} z {2} záznamov\",\n  \"empty\": \"Žiadny záznam na zobrazenie\",\n  \"page\": \"Strana\",\n  \"of\": \"z {0}\",\n  \"itemsPerPage\": \"záznamov na stranu\",\n  \"first\": \"Prejsť na prvú stranu\",\n  \"previous\": \"Prejsť na predošlú stranu\",\n  \"next\": \"Prejsť na ďalšiu stranu\",\n  \"last\": \"Prejsť na poslednú stranu\",\n  \"refresh\": \"Obnoviť\",\n  \"morePages\": \"Ďalšie strany\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"Potiahnite sem polia údajov\",\n  \"columnFields\": \"Potiahnite sem polia stĺpcov\",\n  \"rowFields\": \"Potiahnite sem polia riadkov\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"Zobraziť záznamy s hodnotou, ktorá:\",\n  \"filterFields\": \"Filter polí\",\n  \"filter\": \"Filter\",\n  \"include\": \"Zahrnúť polia...\",\n  \"title\": \"Polia na zahrnutie\",\n  \"clear\": \"Vyčistiť\",\n  \"ok\": \"Ok\",\n  \"cancel\": \"Storno\",\n  \"operators\": {\n    \"contains\": \"Obsahuje\",\n    \"doesnotcontain\": \"Neobsahuje\",\n    \"startswith\": \"Začína s\",\n    \"endswith\": \"Končí s\",\n    \"eq\": \"Rovná sa\",\n    \"neq\": \"Nerovná sa\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Nikdy\",\n    \"hourly\": \"Každú hodinu\",\n    \"daily\": \"Denne\",\n    \"weekly\": \"Týždenne\",\n    \"monthly\": \"Mesačne\",\n    \"yearly\": \"Ročne\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Opakovať každú: \",\n    \"interval\": \" hodinu(hodín)\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Opakovať každý: \",\n    \"interval\": \" deň(dní)\"\n  },\n  \"weekly\": {\n    \"interval\": \" týždeň(týždňov)\",\n    \"repeatEvery\": \"Opakovať každý: \",\n    \"repeatOn\": \"Opakovať: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Opakovať každý: \",\n    \"repeatOn\": \"Opakovať: \",\n    \"interval\": \" mesiac(mesiacov)\",\n    \"day\": \"Deň \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Opakovať každý: \",\n    \"repeatOn\": \"Opakovať: \",\n    \"interval\": \" rok(rokov)\",\n    \"of\": \" z \"\n  },\n  \"end\": {\n    \"label\": \"Koniec:\",\n    \"mobileLabel\": \"Ukončiť\",\n    \"never\": \"Nikdy\",\n    \"after\": \"Po \",\n    \"occurrence\": \" opakovaní(-iach)\",\n    \"on\": \"On \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"prvý\",\n    \"second\": \"druhý\",\n    \"third\": \"tretí\",\n    \"fourth\": \"štvrtý\",\n    \"last\": \"posledný\"\n  },\n  \"weekdays\": {\n    \"day\": \"deň\",\n    \"weekday\": \"pracovný deň\",\n    \"weekend\": \"víkend\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"celý deň\",\n  \"date\": \"Dátum\",\n  \"event\": \"Udalosť\",\n  \"time\": \"Čas\",\n  \"showFullDay\": \"Zobraziť celý deň\",\n  \"showWorkDay\": \"Zobraziť pracovný čas\",\n  \"today\": \"Dnes\",\n  \"save\": \"Uložiť\",\n  \"cancel\": \"Storno\",\n  \"destroy\": \"Odstrániť\",\n  \"deleteWindowTitle\": \"Odstránenie udalosti\",\n  \"ariaSlotLabel\": \"Vybraté od {0:t} do {1:t}\",\n  \"ariaEventLabel\": \"{0} dňa {1:D} o {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"Naozaj chcete odstrániť túto udalosť?\"\n  },\n  \"views\": {\n    \"day\": \"Deň\",\n    \"week\": \"Týždeň\",\n    \"workWeek\": \"Pracovný týždeň\",\n    \"agenda\": \"Agenda\",\n    \"month\": \"Mesiac\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Odstránenie opakovanej udalosti\",\n    \"deleteWindowOccurrence\": \"Odstrániť aktuálnu udalosť\",\n    \"deleteWindowSeries\": \"Odstrániť všetko\",\n    \"editWindowTitle\": \"Úprava opakovanej udalosti\",\n    \"editWindowOccurrence\": \"Upraviť aktuálnu udalosť\",\n    \"editWindowSeries\": \"Upraviť všetko\",\n    \"deleteRecurring\": \"Chcete odstrániť len túto udalosť alebo aj všetky jej opakovania?\",\n    \"editRecurring\": \"Chcete upraviť len túto udalosť alebo aj všetky jej opakovania?\"\n  },\n  \"editor\": {\n    \"title\": \"Názov\",\n    \"start\": \"Začiatok\",\n    \"end\": \"Koniec\",\n    \"allDayEvent\": \"Celodenný\",\n    \"description\": \"Popis\",\n    \"repeat\": \"Opakovať\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"Časové pásmo začiatku\",\n    \"endTimezone\": \"Časové pásmo konca\",\n    \"separateTimezones\": \"Rôzne časové pásma pre začiatok a koniec\",\n    \"timezoneEditorTitle\": \"Časové pásma\",\n    \"timezoneEditorButton\": \"Časové pásmo\",\n    \"timezoneTitle\": \"Časové pásma\",\n    \"noTimezone\": \"Bez časového pásma\",\n    \"editorTitle\": \"Udalosť\"\n  }\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette, {\n    \"allBorders\": \"Všetky orámovania\",\n    \"insideBorders\": \"Vnútorné orámovania\",\n    \"insideHorizontalBorders\": \"Vnútorné vodorovné orámovania\",\n    \"insideVerticalBorders\": \"Vnútorné zvislé orámovania\",\n    \"outsideBorders\": \"Vonkajšie orámovania\",\n    \"leftBorder\": \"Ľavé orámovanie\",\n    \"topBorder\": \"Horné orámovanie\",\n    \"rightBorder\": \"Pravé orámovanie\",\n    \"bottomBorder\": \"Dolné orámovanie\",\n    \"noBorders\": \"Bez orámovania\",\n    \"reset\": \"Nulovať farbu\",\n    \"customColor\": \"Vlastná farba...\",\n    \"apply\": \"Použiť\",\n    \"cancel\": \"Storno\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs, {\n    \"apply\": \"Použiť\",\n    \"save\": \"Uložiť\",\n    \"cancel\": \"Storno\",\n    \"remove\": \"Odstrániť\",\n    \"okText\": \"OK\",\n    \"formatCellsDialog\": {\n        \"title\": \"Formátovanie\",\n        \"categories\": {\n            \"number\": \"Číslo\",\n            \"currency\": \"Mena\",\n            \"date\": \"Dátum\"\n        }\n    },\n    \"fontFamilyDialog\": {\n        \"title\": \"Písmo\"\n    },\n    \"fontSizeDialog\": {\n        \"title\": \"Veľkosť písma\"\n    },\n    \"bordersDialog\": {\n        \"title\": \"Orámovania\"\n    },\n    \"alignmentDialog\": {\n        \"title\": \"Zarovnanie\",\n        \"buttons\": {\n            \"justtifyLeft\": \"Zarovnať vľavo\",\n            \"justifyCenter\": \"Centrovať\",\n            \"justifyRight\": \"Zarovnať vpravo\",\n            \"justifyFull\": \"Zarovnať do bloku\",\n            \"alignTop\": \"Zarovnať nahor\",\n            \"alignMiddle\": \"Zarovnať na stred\",\n            \"alignBottom\": \"Zarovnať nadol\"\n        }\n    },\n    \"mergeDialog\": {\n        \"title\": \"Spájanie buniek\",\n        \"buttons\": {\n            \"mergeCells\": \"Spojiť všetko\",\n            \"mergeHorizontally\": \"Spojiť vodorovne\",\n            \"mergeVertically\": \"Spojiť zvisle\",\n            \"unmerge\": \"Rozpojiť\"\n        }\n    },\n    \"freezeDialog\": {\n        \"title\": \"Zmrazenie panelov\",\n        \"buttons\": {\n            \"freezePanes\": \"Zmraziť panely\",\n            \"freezeRows\": \"Zmraziť riadky\",\n            \"freezeColumns\": \"Zmraziť stĺpce\",\n            \"unfreeze\": \"Rozmraziť panely\"\n        }\n    },\n    \"validationDialog\": {\n        \"title\": \"Overenie údajom\",\n        \"hintMessage\": \"Prosím zadajte platnú {0} hodnotu {1}.\",\n        \"hintTitle\": \"Overenie {0}\",\n        \"criteria\": {\n            \"any\": \"Akákoľvek hodnota\",\n            \"number\": \"Číslo\",\n            \"text\": \"Text\",\n            \"date\": \"Dátum\",\n            \"custom\": \"Vlastný vzorec\",\n            \"list\": \"Zoznam\"\n        },\n        \"comparers\": {\n            \"greaterThan\": \"väčšie ako\",\n            \"lessThan\": \"menšie ako\",\n            \"between\": \"medzi\",\n            \"notBetween\": \"nie medzi\",\n            \"equalTo\": \"rovná sa\",\n            \"notEqualTo\": \"nerovná sa\",\n            \"greaterThanOrEqualTo\": \"väčšie alebo sa rovná\",\n            \"lessThanOrEqualTo\": \"menšie alebo sa rovná\"\n        },\n        \"comparerMessages\": {\n            \"greaterThan\": \"väčšie ako {0}\",\n            \"lessThan\": \"menšie ako {0}\",\n            \"between\": \"medzi {0} a {1}\",\n            \"notBetween\": \"nie medzi {0} a {1}\",\n            \"equalTo\": \"rovná sa {0}\",\n            \"notEqualTo\": \"nerovná sa {0}\",\n            \"greaterThanOrEqualTo\": \"väčšie alebo rovná sa {0}\",\n            \"lessThanOrEqualTo\": \"menšie alebo rovná sa {0}\",\n            \"custom\": \"ktoré vyhovuje: {0}\"\n        },\n        \"labels\": {\n            \"criteria\": \"Kritériá\",\n            \"comparer\": \"Porovnávač\",\n            \"min\": \"Min\",\n            \"max\": \"Max\",\n            \"value\": \"Hodnota\",\n            \"start\": \"Začiatok\",\n            \"end\": \"Koniec\",\n            \"onInvalidData\": \"Pri platných údajoch\",\n            \"rejectInput\": \"Odmietnuť vstup\",\n            \"showWarning\": \"Zobraziť varovanie\",\n            \"showHint\": \"Zobraziť pomôcku\",\n            \"hintTitle\": \"Titulok pomôcky\",\n            \"hintMessage\": \"Správa pomôcky\",\n            \"ignoreBlank\": \"Ignorovať prázdne\"\n        },\n        \"placeholders\": {\n            \"typeTitle\": \"Titulok typu\",\n            \"typeMessage\": \"Správa typu\"\n        }\n    },\n    \"exportAsDialog\": {\n        \"title\": \"Exportovať...\",\n        \"labels\": {\n            \"fileName\": \"Meno súboru\",\n            \"saveAsType\": \"Uložiť ako typ\",\n            \"exportArea\": \"Exportovať\",\n            \"paperSize\": \"Veľkosť papiera\",\n            \"margins\": \"Okraje\",\n            \"orientation\": \"Orientácia\",\n            \"print\": \"Tlačiť\",\n            \"guidelines\": \"Pomocné čiary\",\n            \"center\": \"Centrovať\",\n            \"horizontally\": \"Vodorovne\",\n            \"vertically\": \"Zvisle\"\n        }\n    },\n    \"modifyMergedDialog\": {\n        \"errorMessage\": \"Nemožno zmeniť časť spojenej bunky.\"\n    },\n    \"useKeyboardDialog\": {\n        \"title\": \"Kopírovanie a vkladanie\",\n        \"errorMessage\": \"Tieto akcie sa nemožno ovládať cez menu. Prosím, použite klávesové skratky namiesto:\",\n        \"labels\": {\n            \"forCopy\": \"na kopírovanie\",\n            \"forCut\": \"na vystrihnutie\",\n            \"forPaste\": \"na vloženie\"\n        }\n    },\n    \"unsupportedSelectionDialog\": {\n        \"errorMessage\": \"Táto akcia nemôže byť vykonaná na viacnásobnom výbere.\"\n    }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu, {\n    \"sortAscending\": \"Usporiadať výber od A do Z\",\n    \"sortDescending\": \"Usporiadať výber od Z do A\",\n    \"filterByValue\": \"Filtrovať podľa hodnoty\",\n    \"filterByCondition\": \"Filtrovať podľa podmienky\",\n    \"apply\": \"Použiť\",\n    \"search\": \"Hľadať\",\n    \"addToCurrent\": \"Pridať do aktuálneho výberu\",\n    \"clear\": \"Vyčistiť\",\n    \"blanks\": \"(Prázdne)\",\n    \"operatorNone\": \"Žiadne\",\n    \"and\": \"A\",\n    \"or\": \"ALEBO\",\n    \"operators\": {\n        \"string\": {\n            \"contains\": \"Text obsahuje\",\n            \"doesnotcontain\": \"Text neobsahuje\",\n            \"startswith\": \"Text začína s\",\n            \"endswith\": \"Text končí s\"\n        },\n        \"date\": {\n            \"eq\": \"Dátum je\",\n            \"neq\": \"Dátum nie je\",\n            \"lt\": \"Dátum je pred\",\n            \"gt\": \"Dátum je po\"\n        },\n        \"number\": {\n            \"eq\": \"Rovná sa\",\n            \"neq\": \"Nerovná sa\",\n            \"gte\": \"Je väčšie alebo rovná sa\",\n            \"gt\": \"Je väčšie ako\",\n            \"lte\": \"Je menšie alebo rovná sa\",\n            \"lt\": \"Je menšie ako\"\n        }\n    }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar, {\n    \"addColumnLeft\": \"Pridať stĺpec vľavo\",\n    \"addColumnRight\": \"Pridať stĺpec vpravo\",\n    \"addRowAbove\": \"Pridať riadok nad\",\n    \"addRowBelow\": \"Pridať riadok pod\",\n    \"alignment\": \"Zarovnanie\",\n    \"alignmentButtons\": {\n        \"justtifyLeft\": \"Zarovnať vľavo\",\n        \"justifyCenter\": \"Centrovať\",\n        \"justifyRight\": \"Zarovnať vpravo\",\n        \"justifyFull\": \"Zarovnať do bloku\",\n        \"alignTop\": \"Zarovnať nahor\",\n        \"alignMiddle\": \"Zarovnať na stred\",\n        \"alignBottom\": \"Zarovnať nadol\"\n    },\n    \"backgroundColor\": \"Pozadie\",\n    \"bold\": \"Tučné\",\n    \"borders\": \"Orámovania\",\n    \"colorPicker\": {\n        \"reset\": \"Nulovať farbu\",\n        \"customColor\": \"Vlastná farba...\"\n    },\n    \"copy\": \"Kopírovať\",\n    \"cut\": \"Vystrihnúť\",\n    \"deleteColumn\": \"Odstrániť stĺpec\",\n    \"deleteRow\": \"Odstrániť riadok\",\n    \"excelImport\": \"Importovať z Excelu...\",\n    \"filter\": \"Filtrovať\",\n    \"fontFamily\": \"Písmo\",\n    \"fontSize\": \"Veľkosť písma\",\n    \"format\": \"Vlastný formát...\",\n    \"formatTypes\": {\n        \"automatic\": \"Automatický\",\n        \"number\": \"Číslo\",\n        \"percent\": \"Percentá\",\n        \"financial\": \"Finančný\",\n        \"currency\": \"Mena\",\n        \"date\": \"Dátum\",\n        \"time\": \"Čas\",\n        \"dateTime\": \"Dátum a čas\",\n        \"duration\": \"Časový úsek\",\n        \"moreFormats\": \"Viac formátov...\"\n    },\n    \"formatDecreaseDecimal\": \"Znížiť destinné miesta\",\n    \"formatIncreaseDecimal\": \"Zvýšiť desatinné miesta\",\n    \"freeze\": \"Zmraziť panely\",\n    \"freezeButtons\": {\n        \"freezePanes\": \"Zmraziť panely\",\n        \"freezeRows\": \"Zmraziť riadky\",\n        \"freezeColumns\": \"Zmraziť stĺpce\",\n        \"unfreeze\": \"Rozmraziť panely\"\n    },\n    \"italic\": \"Šikmé\",\n    \"merge\": \"Spojiť bunky\",\n    \"mergeButtons\": {\n        \"mergeCells\": \"Spojiť všetko\",\n        \"mergeHorizontally\": \"Spojiť vodorovne\",\n        \"mergeVertically\": \"Spojiť zvisle\",\n        \"unmerge\": \"Rozpojiž\"\n    },\n    \"open\": \"Otvoriť...\",\n    \"paste\": \"Vložiť\",\n    \"quickAccess\": {\n        \"redo\": \"Znova\",\n        \"undo\": \"Späť\"\n    },\n    \"saveAs\": \"Uložiť Ako...\",\n    \"sortAsc\": \"Usporiadať vzostupne\",\n    \"sortDesc\": \"Usporiadať zostupne\",\n    \"sortButtons\": {\n        \"sortSheetAsc\": \"Usporiadať list od A do Z\",\n        \"sortSheetDesc\": \"Usporiadať list od Z do A\",\n        \"sortRangeAsc\": \"Usporiadať výber od A do Z\",\n        \"sortRangeDesc\": \"Usporiadať výber od o Z do A\"\n    },\n    \"textColor\": \"Farba Textu\",\n    \"textWrap\": \"Zalomiť text\",\n    \"underline\": \"Podčiarknuté\",\n    \"validation\": \"Overenie údajov...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view, {\n    \"errors\": {\n        \"shiftingNonblankCells\": \"Nemožno vložiť bunky kvôli možnosti straty dát. Vyberte iné miesto na vloženie alebo odstráňte údaje z konca listu.\",\n        \"filterRangeContainingMerges\": \"Nemožno vytvoriť filter na výbere, ktoré obsahuje spájania\",\n        \"validationError\": \"Hodnota, ktorú ste zadali porušuje pravidlá platnosti stanovené pre bunku.\"\n    },\n    \"tabs\": {\n        \"home\": \"Domov\",\n        \"insert\": \"Vložiť\",\n        \"data\": \"Údaje\"\n    }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Zvýšiť\",\n  \"decreaseButtonTitle\": \"Znížiť\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages, {\n    \"noRows\": \"Žiadne záznamy na zobrazenie\",\n    \"loading\": \"Nahrávanie...\",\n    \"requestFailed\": \"Požiadavka zlyhala.\",\n    \"retry\": \"Znova\",\n    \"commands\": {\n        \"edit\": \"Upraviť\",\n        \"update\": \"Uložiť\",\n        \"canceledit\": \"Storno\",\n        \"create\": \"Pridať nový záznam\",\n        \"createchild\": \"Pridať podzáznam\",\n        \"destroy\": \"Odstrániť\",\n        \"excel\": \"Exportovať do Excelu\",\n        \"pdf\": \"Exportovať do PDF\"\n    }\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Nahrávanie...\",\n  \"requestFailed\": \"Požiadavka zlyhala.\",\n  \"retry\": \"Znova\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Vyberte súbory...\",\n  \"cancel\": \"Storno\",\n  \"retry\": \"Znova\",\n  \"remove\": \"Odstrániť\",\n  \"uploadSelectedFiles\": \"Odoslať súbory\",\n  \"dropFilesHere\": \"potiahnite sem súbory, ktoré chcete odoslať\",\n  \"statusUploading\": \"odosielanie\",\n  \"statusUploaded\": \"hotovo\",\n  \"statusWarning\": \"varovanie\",\n  \"statusFailed\": \"zlyhanie\",\n  \"headerStatusUploading\": \"Odosielanie...\",\n  \"headerStatusUploaded\": \"Hotovo\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} je požadovaný údaj\",\n  \"pattern\": \"{0} nie je platný údaj\",\n  \"min\": \"{0} musí byť aspoň {1}\",\n  \"max\": \"{0} môže byť najviac {1}\",\n  \"step\": \"{0} nie je platný údaj\",\n  \"email\": \"{0} nie je platný email\",\n  \"url\": \"{0} nie je platná adresa URL\",\n  \"date\": \"{0} nie je platný dátum\",\n  \"dateCompare\": \"Koncový dátum musí byť väčší alebo rovný ako počiatočný\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Zavrieť\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Storno\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Storno\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}