{"version": 3, "sources": ["messages/kendo.messages.it-IT.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "isnull", "isnotnull", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "isempty", "isnotempty", "enums", "FilterMenu", "ColumnMenu", "messages", "columns", "filter", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "clear", "isFalse", "isTrue", "operator", "and", "info", "title", "or", "selectValue", "cancel", "value", "FilterMultiCheck", "search", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "confirmation", "cancelDelete", "confirmDelete", "Groupable", "empty", "Pager", "allPages", "display", "itemsPerPage", "next", "page", "previous", "refresh", "TreeListPager", "Upload", "localization", "retry", "remove", "uploadSelectedFiles", "dropFilesHere", "statusFailed", "statusUploaded", "statusUploading", "headerStatusUploaded", "headerStatusUploading", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "deleteFile", "directoryNotFound", "emptyFolder", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "backColor", "foreColor", "imageWebAddress", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "viewHtml", "dialogUpdate", "insertFile", "insertFile1", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "noTimezone", "event", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "deleteRecurring", "editR<PERSON><PERSON>ring", "time", "today", "views", "agenda", "month", "week", "workWeek", "showFullDay", "showWorkDay", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "dateCompare", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "dialogs", "apply", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "text", "custom", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "onInvalidData", "rejectInput", "showWarning", "showHint", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "blanks", "operatorNone", "toolbar", "alignment", "alignmentButtons", "backgroundColor", "borders", "copy", "cut", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "tabs", "home", "insert", "data", "Dialog", "close", "<PERSON><PERSON>", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,aACNC,GAAM,SACNC,IAAO,oBACPC,GAAM,UACNC,IAAO,qBACPC,IAAO,iBACPC,OAAU,UACVC,UAAa,eAEfC,QACER,GAAM,aACNC,GAAM,kBACNC,IAAO,0BACPC,GAAM,mBACNC,IAAO,2BACPC,IAAO,iBACPC,OAAU,UACVC,UAAa,eAEfE,QACEC,SAAY,WACZC,eAAkB,eAClBC,SAAY,cACZZ,GAAM,aACNK,IAAO,iBACPQ,WAAc,aACdP,OAAU,UACVC,UAAa,cACbO,QAAW,UACXC,WAAc,eAEhBC,OACEhB,GAAM,aACNK,IAAO,iBACPC,OAAU,UACVC,UAAa,kBAObf,MAAMC,GAAGwB,aACbzB,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,aACNC,GAAM,SACNC,IAAO,oBACPC,GAAM,UACNC,IAAO,qBACPC,IAAO,iBACPC,OAAU,UACVC,UAAa,eAEfC,QACER,GAAM,aACNC,GAAM,kBACNC,IAAO,0BACPC,GAAM,mBACNC,IAAO,2BACPC,IAAO,iBACPC,OAAU,UACVC,UAAa,eAEfE,QACEC,SAAY,WACZC,eAAkB,eAClBC,SAAY,cACZZ,GAAM,aACNK,IAAO,iBACPQ,WAAc,aACdP,OAAU,UACVC,UAAa,cACbO,QAAW,UACXC,WAAc,eAEhBC,OACEhB,GAAM,aACNK,IAAO,iBACPC,OAAU,UACVC,UAAa,kBAObf,MAAMC,GAAGyB,aACb1B,MAAMC,GAAGyB,WAAWvB,UAAUC,QAAQuB,SACtC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyB,WAAWvB,UAAUC,QAAQuB,UACnDC,QAAW,UACXC,OAAU,SACVC,cAAiB,sBACjBC,eAAkB,wBAClBC,SAAY,uBACZC,KAAQ,QACRC,KAAQ,WACRC,OAAU,eAMRnC,MAAMC,GAAGmC,mBACbpC,MAAMC,GAAGmC,iBAAiBjC,UAAUC,QAAQuB,SAC5C7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmC,iBAAiBjC,UAAUC,QAAQuB,UACzDU,OACEC,SAAY,YACZC,YAAe,iBAEjBC,KACEC,MAAS,OACTC,WAAc,gBACdC,MAAS,QACTC,MAAS,MACTC,GAAM,KACNC,YAAe,QAEjBC,aACEV,MAAS,cACTW,QAAW,YACXJ,MAAS,MACTK,OAAU,iBACVC,OAAU,aAEZF,SACEG,IAAO,SACPb,SAAY,UACZC,YAAe,gBACfa,SAAY,oBAEdC,iBACEC,MAAS,QACTC,OAAU,SACVC,KAAQ,SACRC,OAAU,UACVC,MAAS,SAEXT,QACEV,YAAe,eACfa,SAAY,iBACZd,SAAY,gBAEdY,QACES,GAAM,KACNpB,YAAe,eACfa,SAAY,iBACZd,SAAY,WAEdsB,UACET,IAAO,SACPU,QAAW,yBACXC,QAAW,2BAOX9D,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQuB,SACtC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQuB,UACnDoC,MAAS,UACTlC,OAAU,SACVmC,QAAW,UACXC,OAAU,SACVC,SAAY,eAMVlE,MAAMC,GAAGwB,aACbzB,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQuB,SACtC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQuB,UACnDwC,IAAO,IACPJ,MAAS,UACTlC,OAAU,SACVuC,KAAQ,iCACRC,MAAS,gCACTL,QAAW,UACXC,OAAU,SACVK,GAAM,IACNC,YAAe,qBACfC,OAAU,UACVN,SAAY,YACZO,MAAS,YAMPzE,MAAMC,GAAGyE,mBACb1E,MAAMC,GAAGyE,iBAAiBvE,UAAUC,QAAQuB,SAC5C7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyE,iBAAiBvE,UAAUC,QAAQuB,UACzDgD,OAAU,WAMR3E,MAAMC,GAAG2E,OACb5E,MAAMC,GAAG2E,KAAKzE,UAAUC,QAAQuB,SAChC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2E,KAAKzE,UAAUC,QAAQuB,UAC7CkD,UACEC,WAAc,UACdN,OAAU,oBACVO,OAAU,0BACVC,QAAW,UACXC,KAAQ,OACRC,MAAS,kBACTC,IAAO,gBACPC,KAAQ,qBACRC,OAAU,YACVC,OAAU,YAEZC,UACEC,aAAgB,6CAChBC,aAAgB,UAChBC,cAAiB,cAOjB1F,MAAMC,GAAG0F,YACb3F,MAAMC,GAAG0F,UAAUxF,UAAUC,QAAQuB,SACrC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0F,UAAUxF,UAAUC,QAAQuB,UAClDiE,MAAS,4FAMP5F,MAAMC,GAAG4F,QACb7F,MAAMC,GAAG4F,MAAM1F,UAAUC,QAAQuB,SACjC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4F,MAAM1F,UAAUC,QAAQuB,UAC9CmE,SAAY,MACZC,QAAW,4BACXH,MAAS,kCACTtC,MAAS,wBACT0C,aAAgB,sBAChBxC,KAAQ,wBACRyC,KAAQ,2BACRtC,GAAM,SACNuC,KAAQ,SACRC,SAAY,6BACZC,QAAW,cAMTpG,MAAMC,GAAGoG,gBACbrG,MAAMC,GAAGoG,cAAclG,UAAUC,QAAQuB,SACzC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoG,cAAclG,UAAUC,QAAQuB,UACtDmE,SAAY,MACZC,QAAW,4BACXH,MAAS,kCACTtC,MAAS,wBACT0C,aAAgB,sBAChBxC,KAAQ,wBACRyC,KAAQ,2BACRtC,GAAM,SACNuC,KAAQ,SACRC,SAAY,6BACZC,QAAW,cAMTpG,MAAMC,GAAGqG,SACbtG,MAAMC,GAAGqG,OAAOnG,UAAUC,QAAQmG,aAClCzG,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,OAAOnG,UAAUC,QAAQmG,cAC/C/B,OAAU,UACVgC,MAAS,UACTnB,OAAU,eACVoB,OAAU,UACVC,oBAAuB,8BACvBC,cAAiB,mCACjBC,aAAgB,UAChBC,eAAkB,oBAClBC,gBAAmB,kBACnBC,qBAAwB,QACxBC,sBAAyB,wBAMvBhH,MAAMC,GAAGgH,SACbjH,MAAMC,GAAGgH,OAAO9G,UAAUC,QAAQuB,SAClC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgH,OAAO9G,UAAUC,QAAQuB,UAC/CuF,KAAQ,YACRC,WAAc,sBACdC,SAAY,iCACZC,gBAAmB,mBACnBC,SAAY,mCACZC,gBAAmB,yBACnBC,YAAe,kBACfC,OAAU,kBACVC,WAAc,iBACdC,YAAe,qBACfC,kBAAqB,2BACrBC,oBAAuB,+BACvBC,OAAU,SACVC,cAAiB,eACjBC,YAAe,mBACfC,YAAe,8BACfC,aAAgB,4BAChBC,QAAW,iBACXC,cAAiB,UACjBC,OAAU,QACVC,UAAa,WACbC,YAAe,WACfC,UAAa,eACbC,OAAU,oBACVC,WAAc,mCACdC,kBAAqB,wDACrBC,YAAe,iBACfC,gBAAmB,8EACnBC,QAAW,cACXC,YAAe,OACfC,YAAe,aACfC,cAAiB,qFACjBC,WAAc,SACdC,UAAa,gBACbC,UAAa,SACbzC,cAAiB,oCACjB0C,gBAAmB,gBACnBC,sBAAyB,IACzBC,aAAgB,UAChBC,aAAgB,YAChBC,aAAgB,oBAChBC,oBAAuB,kCACvBC,SAAY,QACZC,YAAe,UACfC,eAAkB,gBAClBlF,OAAU,QACVmF,YAAe,eACfC,cAAiB,8BACjBC,eAAkB,4BAClBC,YAAe,sBACfC,YAAe,sBACfC,aAAgB,kBAChBC,UAAa,eACbC,SAAY,YACZC,aAAgB,SAChBC,WAAc,cACdC,YAAe,iBAMbxK,MAAMC,GAAGwK,YACbzK,MAAMC,GAAGwK,UAAUtK,UAAUC,QAAQuB,SACrC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwK,UAAUtK,UAAUC,QAAQuB,UAClD+I,OAAU,kBACVlG,OAAU,UACVe,UACEC,aAAgB,4CAElBjF,KAAQ,OACRyE,QAAW,UACX2F,QACEC,YAAe,kBACfC,YAAe,cACfC,YAAe,SACftI,IAAO,OACPuI,YAAe,qBACfC,OAAU,SACVC,kBAAqB,mDACrBC,MAAS,SACTC,cAAiB,uBACjBC,SAAY,uBACZC,qBAAwB,cACxBC,oBAAuB,aACvBjH,MAAS,SACTkH,WAAc,eAEhBC,MAAS,SACTC,oBACEC,uBAA0B,4BAC1BC,mBAAsB,mBACtBC,kBAAqB,8BACrBC,qBAAwB,6BACxBC,iBAAoB,oBACpBC,gBAAmB,+BACnBC,gBAAmB,yDACnBC,cAAiB,0DAEnB7G,KAAQ,QACR8G,KAAQ,QACRC,MAAS,OACTC,OACEC,OAAU,SACVlJ,IAAO,SACPmJ,MAAS,OACTC,KAAQ,YACRC,SAAY,aAEdZ,kBAAqB,iBACrBa,YAAe,4BACfC,YAAe,mCAMX1M,MAAMC,GAAG0M,YACX3M,MAAMC,GAAG0M,UAAUxM,UAAUC,QAAQuB,SACnC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0M,UAAUxM,UAAUC,QAAQuB,UAClDiL,SAAY,kBACZC,QAAW,mBACXC,IAAO,iDACPC,IAAO,+CACPC,KAAQ,mBACRC,MAAS,sCACTC,IAAO,0BACP3M,KAAQ,mCACR4M,YAAe,2EAOnBnN,MAAMoN,aAAepN,MAAMoN,YAAYzL,SAAS0L,gBACpDrN,MAAMoN,YAAYzL,SAAS0L,cAC3BvN,EAAEQ,QAAO,EAAMN,MAAMoN,YAAYzL,SAAS0L,eACxCC,WAAc,gBACdC,cAAiB,gBACjBC,wBAA2B,4BAC3BC,sBAAyB,0BACzBC,eAAkB,gBAClBC,WAAc,iBACdC,UAAa,kBACbC,YAAe,eACfC,aAAgB,kBAChBC,UAAa,kBAIX/N,MAAMoN,aAAepN,MAAMoN,YAAYzL,SAASqM,UACpDhO,MAAMoN,YAAYzL,SAASqM,QAC3BlO,EAAEQ,QAAO,EAAMN,MAAMoN,YAAYzL,SAASqM,SACxCC,MAAS,UACT7I,KAAQ,QACRZ,OAAU,UACViC,OAAU,UACVyH,OAAU,KACVC,mBACE9J,MAAS,UACT+J,YACEpN,OAAU,SACVqN,SAAY,SACZ9N,KAAQ,SAGZ+N,kBACEjK,MAAS,aAEXkK,gBACElK,MAAS,wBAEXmK,eACEnK,MAAS,SAEXoK,iBACEpK,MAAS,eACTqK,SACCC,aAAgB,2BAChB5G,cAAiB,oBACjBG,aAAgB,yBAChBF,YAAe,aACf4G,SAAY,kBACZC,YAAe,kCACfC,YAAe,qBAGlBC,aACE1K,MAAS,eACTqK,SACEM,WAAc,wBACdC,kBAAqB,yBACrBC,gBAAmB,uBACnBC,QAAW,iBAGfC,cACE/K,MAAS,kBACTqK,SACEW,YAAe,kBACfC,WAAc,eACdC,cAAiB,iBACjBC,SAAY,qBAGhBC,kBACEpL,MAAS,iBACTqL,YAAe,iDACfC,UAAa,gBACbC,UACEC,IAAO,mBACP7O,OAAU,SACV8O,KAAQ,QACRvP,KAAQ,OACRwP,OAAU,0BAEZC,WACEC,YAAe,gBACfC,SAAY,YACZC,QAAW,MACXC,WAAc,UACdC,QAAW,WACXC,WAAc,eACdC,qBAAwB,sBACxBC,kBAAqB,qBAEvBC,kBACER,YAAe,oBACfC,SAAY,gBACZC,QAAW,gBACXC,WAAc,oBACdC,QAAW,eACXC,WAAc,mBACdC,qBAAwB,0BACxBC,kBAAqB,wBACrBT,OAAU,gCAEZW,QACEd,SAAY,UACZe,SAAY,yBACZ7D,IAAO,MACPC,IAAO,MACPtI,MAAS,SACTyG,MAAS,SACT1I,IAAO,OACPoO,cAAiB,6BACjBC,YAAe,sBACfC,YAAe,gBACfC,SAAY,sBACZpB,UAAa,sBACbD,YAAe,0BAEjBsB,cACEC,UAAa,iBACbC,YAAe,sBAGnBC,gBACE9M,MAAS,oBACTqM,QACEU,SAAY,YACZC,WAAc,oBAGlBC,oBACEC,aAAgB,oDAElBC,mBACEnN,MAAS,kBACTkN,aAAgB,+GAChBb,QACEe,QAAW,cACXC,OAAU,eACVC,SAAY,kBAGhBC,4BACEL,aAAgB,8DAKhBvR,MAAMoN,aAAepN,MAAMoN,YAAYzL,SAASkQ,aACpD7R,MAAMoN,YAAYzL,SAASkQ,WAC3B/R,EAAEQ,QAAO,EAAMN,MAAMoN,YAAYzL,SAASkQ,YACxC/P,cAAiB,wBACjBC,eAAkB,uBAClB+P,cAAiB,oBACjBC,kBAAqB,qBACrB9D,MAAS,UACTtJ,OAAU,QACVZ,MAAS,WACTiO,OAAU,UACVC,aAAgB,gBAChB9N,IAAO,IACPG,GAAM,IACNjE,WACEY,QACEC,SAAY,iBACZC,eAAkB,qBAClBE,WAAc,mBACdD,SAAY,qBAEdb,MACEC,GAAM,aACNK,IAAO,iBACPF,GAAM,UACNF,GAAM,UAERO,QACER,GAAM,aACNC,GAAM,kBACNC,IAAO,0BACPC,GAAM,mBACNC,IAAO,2BACPC,IAAO,sBAMTb,MAAMoN,aAAepN,MAAMoN,YAAYzL,SAASuQ,UACpDlS,MAAMoN,YAAYzL,SAASuQ,QAC3BpS,EAAEQ,QAAO,EAAMN,MAAMoN,YAAYzL,SAASuQ,SACxCnI,cAAiB,8BACjBC,eAAkB,4BAClBC,YAAe,sBACfC,YAAe,sBACfiI,UAAa,eACbC,kBACAzD,aAAgB,2BAChB5G,cAAiB,oBACjBG,aAAgB,yBAChBF,YAAe,aACf4G,SAAY,kBACZC,YAAe,kCACfC,YAAe,oBAEfuD,gBAAmB,qBACnBnL,KAAQ,YACRoL,QAAW,QACXC,KAAQ,QACRC,IAAO,SACPrI,aAAgB,kBAChBC,UAAa,eACbvI,OAAU,SACV4Q,WAAc,YACdnL,SAAY,uBACZoL,OAAU,2BACVC,aACEC,UAAa,aACb5R,OAAU,SACV6R,QAAW,cACXC,UAAa,cACbzE,SAAY,SACZ9N,KAAQ,OACR2L,KAAQ,MACR6G,SAAY,aACZC,SAAY,SACZC,YAAe,oBAEjBC,sBAAyB,+BACzBC,sBAAyB,gCACzBC,OAAU,kBACVC,eACEhE,YAAe,kBACfC,WAAc,eACdC,cAAiB,iBACjBC,SAAY,oBAEd1H,OAAU,UACVwL,MAAS,eACTC,cACEvE,WAAc,wBACdC,kBAAqB,yBACrBC,gBAAmB,uBACnBC,QAAW,gBAEbqE,KAAQ,UACRC,MAAS,UACTC,aACEC,KAAQ,aACRC,KAAQ,WAEVC,OAAU,oBACVC,QAAW,mBACXC,SAAY,qBACZC,aACEC,aAAgB,6BAChBC,cAAiB,6BACjBC,aAAgB,kCAChBC,cAAiB,mCAEnBC,UAAa,mBACbC,SAAY,eACZ9L,UAAa,eACb+L,WAAc,uBAIZvU,MAAMoN,aAAepN,MAAMoN,YAAYzL,SAAS6S,OACpDxU,MAAMoN,YAAYzL,SAAS6S,KAC3B1U,EAAEQ,QAAO,EAAMN,MAAMoN,YAAYzL,SAAS6S,MACxCC,QACEC,sBAAyB,sKACzBC,4BAA+B,uFAEjCC,MACEC,KAAQ,OACRC,OAAU,YACVC,KAAQ,WAOR/U,MAAMC,GAAG+U,SACbhV,MAAMC,GAAG+U,OAAO7U,UAAUC,QAAQuB,SAClC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+U,OAAO7U,UAAUC,QAAQmG,cAC/C0O,MAAS,YAMPjV,MAAMC,GAAGiV,QACblV,MAAMC,GAAGiV,MAAM/U,UAAUC,QAAQuB,SACjC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiV,MAAM/U,UAAUC,QAAQmG,cAC9C2H,OAAU,QAMRlO,MAAMC,GAAGkV,UACbnV,MAAMC,GAAGkV,QAAQhV,UAAUC,QAAQuB,SACnC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkV,QAAQhV,UAAUC,QAAQmG,cAChD2H,OAAU,KACV1J,OAAU,aAKRxE,MAAMC,GAAGmV,SACbpV,MAAMC,GAAGmV,OAAOjV,UAAUC,QAAQuB,SAClC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmV,OAAOjV,UAAUC,QAAQmG,cAC/C2H,OAAU,KACV1J,OAAU,cAIT6Q,OAAOrV,MAAMsV", "file": "kendo.messages.it-IT.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"È uguale a\",\n    \"gt\": \"È dopo\",\n    \"gte\": \"È dopo o uguale a\",\n    \"lt\": \"È prima\",\n    \"lte\": \"È prima o uguale a\",\n    \"neq\": \"Non è uguale a\",\n    \"isnull\": \"È nullo\",\n    \"isnotnull\": \"Non è nullo\"\n  },\n  \"number\": {\n    \"eq\": \"È uguale a\",\n    \"gt\": \"È più grande di\",\n    \"gte\": \"È più grande o uguale a\",\n    \"lt\": \"È più piccolo di\",\n    \"lte\": \"È più piccolo o uguale a\",\n    \"neq\": \"Non è uguale a\",\n    \"isnull\": \"È nullo\",\n    \"isnotnull\": \"Non è nullo\"\n  },\n  \"string\": {\n    \"contains\": \"Contiene\",\n    \"doesnotcontain\": \"Non contiene\",\n    \"endswith\": \"Finisce con\",\n    \"eq\": \"È uguale a\",\n    \"neq\": \"Non è uguale a\",\n    \"startswith\": \"Inizia con\",\n    \"isnull\": \"È nullo\",\n    \"isnotnull\": \"Non è nullo\",\n    \"isempty\": \"È vuoto\",\n    \"isnotempty\": \"Non è vuoto\"\n  },\n  \"enums\": {\n    \"eq\": \"È uguale a\",\n    \"neq\": \"Non è uguale a\",\n    \"isnull\": \"È nullo\",\n    \"isnotnull\": \"Non è nullo\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"È uguale a\",\n    \"gt\": \"È dopo\",\n    \"gte\": \"È dopo o uguale a\",\n    \"lt\": \"È prima\",\n    \"lte\": \"È prima o uguale a\",\n    \"neq\": \"Non è uguale a\",\n    \"isnull\": \"È nullo\",\n    \"isnotnull\": \"Non è nullo\"\n  },\n  \"number\": {\n    \"eq\": \"È uguale a\",\n    \"gt\": \"È più grande di\",\n    \"gte\": \"È più grande o uguale a\",\n    \"lt\": \"È più piccolo di\",\n    \"lte\": \"È più piccolo o uguale a\",\n    \"neq\": \"Non è uguale a\",\n    \"isnull\": \"È nullo\",\n    \"isnotnull\": \"Non è nullo\"\n  },\n  \"string\": {\n    \"contains\": \"Contiene\",\n    \"doesnotcontain\": \"Non contiene\",\n    \"endswith\": \"Finisce con\",\n    \"eq\": \"È uguale a\",\n    \"neq\": \"Non è uguale a\",\n    \"startswith\": \"Inizia con\",\n    \"isnull\": \"È nullo\",\n    \"isnotnull\": \"Non è nullo\",\n    \"isempty\": \"È vuoto\",\n    \"isnotempty\": \"Non è vuoto\"\n  },\n  \"enums\": {\n    \"eq\": \"È uguale a\",\n    \"neq\": \"Non è uguale a\",\n    \"isnull\": \"È nullo\",\n    \"isnotnull\": \"Non è nullo\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Colonne\",\n  \"filter\": \"Filtro\",\n  \"sortAscending\": \"In ordine crescente\",\n  \"sortDescending\": \"In ordine decrescente\",\n  \"settings\": \"Impostazioni colonna\",\n  \"done\": \"Fatto\",\n  \"lock\": \"Bloccare\",\n  \"unlock\": \"Sbloccare\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"giorno(i)\",\n    \"repeatEvery\": \"Ripeti ogni: \"\n  },\n  \"end\": {\n    \"after\": \"Dopo\",\n    \"occurrence\": \"Occorrenza(e)\",\n    \"label\": \"Fine:\",\n    \"never\": \"Mai\",\n    \"on\": \"Il\",\n    \"mobileLabel\": \"Ends\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Ogni giorno\",\n    \"monthly\": \"Ogni mese\",\n    \"never\": \"Mai\",\n    \"weekly\": \"Ogni settimana\",\n    \"yearly\": \"Ogni anno\"\n  },\n  \"monthly\": {\n    \"day\": \"Giorno\",\n    \"interval\": \"mese(i)\",\n    \"repeatEvery\": \"Ripeti ogni: \",\n    \"repeatOn\": \"Repeti quando:: \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"primo\",\n    \"fourth\": \"quarto\",\n    \"last\": \"ultimo\",\n    \"second\": \"secondo\",\n    \"third\": \"terzo\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Ripeti ogni:\",\n    \"repeatOn\": \"Ripeti quando:\",\n    \"interval\": \"settimana(e)\"\n  },\n  \"yearly\": {\n    \"of\": \"di\",\n    \"repeatEvery\": \"Ripeti ogni:\",\n    \"repeatOn\": \"Ripeti quando:\",\n    \"interval\": \"anno(i)\"\n  },\n  \"weekdays\": {\n    \"day\": \"giorno\",\n    \"weekday\": \"giorno della settimana\",\n    \"weekend\": \"giorno finesettimana\"\n  }\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"clear\": \"Rimuovi\",\n  \"filter\": \"Filtro\",\n  \"isFalse\": \"è falso\",\n  \"isTrue\": \"è vero\",\n  \"operator\": \"Operatore\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"and\": \"E\",\n  \"clear\": \"Rimuovi\",\n  \"filter\": \"Filtro\",\n  \"info\": \"Mostra elementi il cui valore:\",\n  \"title\": \"Mostra elementi il cui valore\",\n  \"isFalse\": \"è falso\",\n  \"isTrue\": \"è vero\",\n  \"or\": \"O\",\n  \"selectValue\": \"-Seleziona valore-\",\n  \"cancel\": \"Annulla\",\n  \"operator\": \"Operatore\",\n  \"value\": \"Valore\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"Cerca\"\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"canceledit\": \"Annulla\",\n    \"cancel\": \"Annulla modifiche\",\n    \"create\": \"Aggiungi nuovo elemento\",\n    \"destroy\": \"Rimuovi\",\n    \"edit\": \"Edit\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"save\": \"Salva le modifiche\",\n    \"select\": \"Seleziona\",\n    \"update\": \"Aggiorna\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Sicuro di voler rimuovere questo elemento?\",\n    \"cancelDelete\": \"Annulla\",\n    \"confirmDelete\": \"Rimuovi\"\n  }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Trascina l'header di una colonna e rilascialo qui per raggruppare secondo tale colonna\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} di {2} elementi\",\n  \"empty\": \"Nessun elemento da visualizzare\",\n  \"first\": \"Vai alla prima pagina\",\n  \"itemsPerPage\": \"elementi per pagina\",\n  \"last\": \"Vai all'ultima pagina\",\n  \"next\": \"Vai alla prossima pagina\",\n  \"of\": \"di {0}\",\n  \"page\": \"Pagina\",\n  \"previous\": \"Vai alla pagina precedente\",\n  \"refresh\": \"Aggiorna\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} di {2} elementi\",\n  \"empty\": \"Nessun elemento da visualizzare\",\n  \"first\": \"Vai alla prima pagina\",\n  \"itemsPerPage\": \"elementi per pagina\",\n  \"last\": \"Vai all'ultima pagina\",\n  \"next\": \"Vai alla prossima pagina\",\n  \"of\": \"di {0}\",\n  \"page\": \"Pagina\",\n  \"previous\": \"Vai alla pagina precedente\",\n  \"refresh\": \"Aggiorna\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Annulla\",\n  \"retry\": \"Riprova\",\n  \"select\": \"Seleziona...\",\n  \"remove\": \"Rimuovi\",\n  \"uploadSelectedFiles\": \"Upload dei file selezionati\",\n  \"dropFilesHere\": \"rilascia qui i file per l'upload\",\n  \"statusFailed\": \"fallito\",\n  \"statusUploaded\": \"upload effettuato\",\n  \"statusUploading\": \"upload in corso\",\n  \"headerStatusUploaded\": \"Fatto\",\n  \"headerStatusUploading\": \"Upload in corso...\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Grassetto\",\n  \"createLink\": \"Inserisci hyperlink\",\n  \"fontName\": \"Seleziona una famiglia di font\",\n  \"fontNameInherit\": \"(font ereditato)\",\n  \"fontSize\": \"Seleziona la dimensione del font\",\n  \"fontSizeInherit\": \"(dimensione ereditata)\",\n  \"formatBlock\": \"Formatta blocco\",\n  \"indent\": \"Aumenta rientro\",\n  \"insertHtml\": \"Inserisci HTML\",\n  \"insertImage\": \"Inserisci immagine\",\n  \"insertOrderedList\": \"Inserisci lista ordinata\",\n  \"insertUnorderedList\": \"Inserisci lista non ordinata\",\n  \"italic\": \"Italic\",\n  \"justifyCenter\": \"Centra testo\",\n  \"justifyFull\": \"Giustifica testo\",\n  \"justifyLeft\": \"Allinea il testo a sinistra\",\n  \"justifyRight\": \"Allinea il testo a destra\",\n  \"outdent\": \"Riduci rientro\",\n  \"strikethrough\": \"Barrato\",\n  \"styles\": \"Stili\",\n  \"subscript\": \"A pedice\",\n  \"superscript\": \"In apice\",\n  \"underline\": \"Sottolineato\",\n  \"unlink\": \"Rimuovi hyperlink\",\n  \"deleteFile\": \"Sicuro di voler rimuovere \\\"{0}\\\"?\",\n  \"directoryNotFound\": \"Non è stata trovata alcuna directory con questo nome.\",\n  \"emptyFolder\": \"Cartella vuota\",\n  \"invalidFileType\": \"Il file selezionato \\\"{0}\\\" non è valido. I tipi di file supportati sono {1}.\",\n  \"orderBy\": \"Ordina per:\",\n  \"orderByName\": \"Nome\",\n  \"orderBySize\": \"Dimensione\",\n  \"overwriteFile\": \"'Un file con nome \\\"{0}\\\" esiste già nella directory corrente. Vuoi sovrascriverlo?\",\n  \"uploadFile\": \"Upload\",\n  \"backColor\": \"Colore sfondo\",\n  \"foreColor\": \"Colore\",\n  \"dropFilesHere\": \"rilascia qui i files per l'upload\",\n  \"imageWebAddress\": \"Indirizzo Web\",\n  \"dialogButtonSeparator\": \"o\",\n  \"dialogCancel\": \"Annulla\",\n  \"dialogInsert\": \"Inserisci\",\n  \"imageAltText\": \"Testo alternativo\",\n  \"linkOpenInNewWindow\": \"Apri link in una nuova finestra\",\n  \"linkText\": \"Testo\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkWebAddress\": \"Indirizzo Web\",\n  \"search\": \"Cerca\",\n  \"createTable\": \"Crea tabella\",\n  \"addColumnLeft\": \"Aggiungi colonna a sinistra\",\n  \"addColumnRight\": \"Aggiungi colonna a destra\",\n  \"addRowAbove\": \"Aggiungi riga sopra\",\n  \"addRowBelow\": \"Aggiungi riga sotto\",\n  \"deleteColumn\": \"Rimuovi colonna\",\n  \"deleteRow\": \"Rimuovi riga\",\n  \"viewHtml\": \"View HTML\",\n  \"dialogUpdate\": \"Update\",\n  \"insertFile\": \"Insert file\",\n  \"insertFile1\": \"Insert file\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"tutto il giorno\",\n  \"cancel\": \"Annulla\",\n  \"editable\": {\n    \"confirmation\": \"Sicuro di voler rimuovere questo evento?\"\n  },\n  \"date\": \"Data\",\n  \"destroy\": \"Rimuovi\",\n  \"editor\": {\n    \"allDayEvent\": \"Giornata intera\",\n    \"description\": \"Descrizione\",\n    \"editorTitle\": \"Evento\",\n    \"end\": \"Fine\",\n    \"endTimezone\": \"Fuso orario finale\",\n    \"repeat\": \"Ripeti\",\n    \"separateTimezones\": \"Usa differenti fusi orari per l'inizio e la fine\",\n    \"start\": \"Inizio\",\n    \"startTimezone\": \"Fuso orario iniziale\",\n    \"timezone\": \"Modifica fuso orario\",\n    \"timezoneEditorButton\": \"Fuso orario\",\n    \"timezoneEditorTitle\": \"Fusi orari\",\n    \"title\": \"Titolo\",\n    \"noTimezone\": \"No timezone\"\n  },\n  \"event\": \"Evento\",\n  \"recurrenceMessages\": {\n    \"deleteWindowOccurrence\": \"Rimuovi questa accorrenza\",\n    \"deleteWindowSeries\": \"Rimuovi la serie\",\n    \"deleteWindowTitle\": \"Rimuovi elemento ricorrente\",\n    \"editWindowOccurrence\": \"Modifica l'evento corrente\",\n    \"editWindowSeries\": \"Modifica la serie\",\n    \"editWindowTitle\": \"Modifica elemento ricorrente\",\n    \"deleteRecurring\": \"Vuoi rimuovere solo questo evento o la serie completa?\",\n    \"editRecurring\": \"Vuoi modifcare solo questo evento o la serie completa?\"\n  },\n  \"save\": \"Salva\",\n  \"time\": \"Tempo\",\n  \"today\": \"Oggi\",\n  \"views\": {\n    \"agenda\": \"Agenda\",\n    \"day\": \"Giorno\",\n    \"month\": \"Mese\",\n    \"week\": \"Settimana\",\n    \"workWeek\": \"Work Week\"\n  },\n  \"deleteWindowTitle\": \"Rimuovi evento\",\n  \"showFullDay\": \"Mostra il giorno completo\",\n  \"showWorkDay\": \"Mostra solo le ore lavorative\"\n});\n}\n\n  /* Validator messages */\n\n  if (kendo.ui.Validator) {\n    kendo.ui.Validator.prototype.options.messages =\n      $.extend(true, kendo.ui.Validator.prototype.options.messages, {\n        \"required\": \"{0} è richiesto\",\n        \"pattern\": \"{0} non è valido\",\n        \"min\": \"{0} dovrebbe essere maggiore di o uguale a {1}\",\n        \"max\": \"{0} dovrebbe essere minore di o uguale a {1}\",\n        \"step\": \"{0} non è valido\",\n        \"email\": \"{0} non è un formato email corretto\",\n        \"url\": \"{0} non è un URL valido\",\n        \"date\": \"{0} non è un formato data valido\",\n        \"dateCompare\": \"La data di fine dovrebbe essere maggiore o uguale di quella di inizio\"\n      });\n  }\n\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"Tutti i bordi\",\n  \"insideBorders\": \"Bordi interni\",\n  \"insideHorizontalBorders\": \"Bordi interni orizzontali\",\n  \"insideVerticalBorders\": \"Bordi interni verticali\",\n  \"outsideBorders\": \"Bordi esterni\",\n  \"leftBorder\": \"Bordo sinistro\",\n  \"topBorder\": \"Bordo superiore\",\n  \"rightBorder\": \"Bordo destro\",\n  \"bottomBorder\": \"Bordo inferiore\",\n  \"noBorders\": \"Nessun bordo\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Applica\",\n  \"save\": \"Salva\",\n  \"cancel\": \"Annulla\",\n  \"remove\": \"Rimuovi\",\n  \"okText\": \"OK\",\n  \"formatCellsDialog\": {\n    \"title\": \"Formato\",\n    \"categories\": {\n      \"number\": \"Numero\",\n      \"currency\": \"Valuta\",\n      \"date\": \"Data\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Carattere\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Dimensione carattere\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Bordi\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Allineamento\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Allinea testo a sinistra\",\n     \"justifyCenter\": \"Allinea al centro\",\n     \"justifyRight\": \"Allinea testo a destra\",\n     \"justifyFull\": \"Giustifica\",\n     \"alignTop\": \"Allinea in alto\",\n     \"alignMiddle\": \"Allinea al centro verticalmente\",\n     \"alignBottom\": \"Allinea in basso\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Unisci celle\",\n    \"buttons\": {\n      \"mergeCells\": \"Unisci tutte le celle\",\n      \"mergeHorizontally\": \"Unisci orizzontalmente\",\n      \"mergeVertically\": \"Unisci verticalmente\",\n      \"unmerge\": \"Dividi celle\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Blocca riquadri\",\n    \"buttons\": {\n      \"freezePanes\": \"Blocca riquadri\",\n      \"freezeRows\": \"Blocca righe\",\n      \"freezeColumns\": \"Blocca colonne\",\n      \"unfreeze\": \"Sblocca riquadri\"\n    }\n  },\n  \"validationDialog\": {\n    \"title\": \"Convalida dati\",\n    \"hintMessage\": \"Si prega di inserire un {0} valore valido {1}.\",\n    \"hintTitle\": \"Convalida {0}\",\n    \"criteria\": {\n      \"any\": \"Qualsiasi valore\",\n      \"number\": \"Numero\",\n      \"text\": \"Testo\",\n      \"date\": \"Data\",\n      \"custom\": \"Formula personalizzata\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"più grande di\",\n      \"lessThan\": \"minore di\",\n      \"between\": \"tra\",\n      \"notBetween\": \"non tra\",\n      \"equalTo\": \"uguale a\",\n      \"notEqualTo\": \"non uguale a\",\n      \"greaterThanOrEqualTo\": \"maggiore o uguale a\",\n      \"lessThanOrEqualTo\": \"minore o uguale a\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"più grande di {0}\",\n      \"lessThan\": \"minore di {0}\",\n      \"between\": \"tra {0} e {1}\",\n      \"notBetween\": \"non tra {0} e {1}\",\n      \"equalTo\": \"uguale a {0}\",\n      \"notEqualTo\": \"non uguale a {0}\",\n      \"greaterThanOrEqualTo\": \"maggiore o uguale a {0}\",\n      \"lessThanOrEqualTo\": \"minore o uguale a {0}\",\n      \"custom\": \"che soddisfa la formula: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Criteri\",\n      \"comparer\": \"Operatore di confronto\",\n      \"min\": \"Min\",\n      \"max\": \"Max\",\n      \"value\": \"Valore\",\n      \"start\": \"Inizia\",\n      \"end\": \"Fine\",\n      \"onInvalidData\": \"In base ai dati non validi\",\n      \"rejectInput\": \"Rifiuta di ingresso\",\n      \"showWarning\": \"Mostra avviso\",\n      \"showHint\": \"Mostra suggerimento\",\n      \"hintTitle\": \"Titolo suggerimento\",\n      \"hintMessage\": \"Messaggio suggerimento\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Tipo di titolo\",\n      \"typeMessage\": \"Tipo di messaggio\"\n    }\n  },\n  \"exportAsDialog\": {\n    \"title\": \"Salva con nome...\",\n    \"labels\": {\n      \"fileName\": \"Nome file\",\n      \"saveAsType\": \"Salva come tipo\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Impossibile modificare parte di una cella unita.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Copia e incolla\",\n    \"errorMessage\": \"Queste azioni non possono essere invocate attraverso il menu. Ti prego, utilizza le scorciatoie da tastiera:\",\n    \"labels\": {\n      \"forCopy\": \"per copiare\",\n      \"forCut\": \"per tagliare\",\n      \"forPaste\": \"per incollare\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"L'azione non può essere eseguita su selezione multipla.\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Ordina dall'A alla Z \",\n  \"sortDescending\": \"Ordina dalla Z all'A\",\n  \"filterByValue\": \"Filtra per valore\",\n  \"filterByCondition\": \"Filtra per criteri\",\n  \"apply\": \"Applica\",\n  \"search\": \"Cerca\",\n  \"clear\": \"Cancella\",\n  \"blanks\": \"(Vuote)\",\n  \"operatorNone\": \"Senza criteri\",\n  \"and\": \"E\",\n  \"or\": \"O\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"Testo contiene\",\n      \"doesnotcontain\": \"Testo non contiene\",\n      \"startswith\": \"Testo inizia con\",\n      \"endswith\": \"Testo finisce con\"\n    },\n    \"date\": {\n      \"eq\": \"È uguale a\",\n      \"neq\": \"Non è uguale a\",\n      \"lt\": \"È prima\",\n      \"gt\": \"È dopo\"\n    },\n    \"number\": {\n      \"eq\": \"È uguale a\",\n      \"gt\": \"È più grande di\",\n      \"gte\": \"È più grande o uguale a\",\n      \"lt\": \"È più piccolo di\",\n      \"lte\": \"È più piccolo o uguale a\",\n      \"neq\": \"Non è uguale a\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Aggiungi colonna a sinistra\",\n  \"addColumnRight\": \"Aggiungi colonna a destra\",\n  \"addRowAbove\": \"Aggiungi riga sopra\",\n  \"addRowBelow\": \"Aggiungi riga sotto\",\n  \"alignment\": \"Allineamento\",\n  \"alignmentButtons\": {\n  \"justtifyLeft\": \"Allinea testo a sinistra\",\n  \"justifyCenter\": \"Allinea al centro\",\n  \"justifyRight\": \"Allinea testo a destra\",\n  \"justifyFull\": \"Giustifica\",\n  \"alignTop\": \"Allinea in alto\",\n  \"alignMiddle\": \"Allinea al centro verticalmente\",\n  \"alignBottom\": \"Allinea in basso\"\n  },\n  \"backgroundColor\": \"Colore riempimento\",\n  \"bold\": \"Grassetto\",\n  \"borders\": \"Bordi\",\n  \"copy\": \"Copia\",\n  \"cut\": \"Taglia\",\n  \"deleteColumn\": \"Rimuovi colonna\",\n  \"deleteRow\": \"Rimuovi riga\",\n  \"filter\": \"Filtro\",\n  \"fontFamily\": \"Carattere\",\n  \"fontSize\": \"Dimensione carattere\",\n  \"format\": \"Format personalizzato...\",\n  \"formatTypes\": {\n    \"automatic\": \"Automatico\",\n    \"number\": \"Numero\",\n    \"percent\": \"Percentuale\",\n    \"financial\": \"Contabilità\",\n    \"currency\": \"Valuta\",\n    \"date\": \"Data\",\n    \"time\": \"Ora\",\n    \"dateTime\": \"Data e ora\",\n    \"duration\": \"Durata\",\n    \"moreFormats\": \"Altri formati...\"\n  },\n  \"formatDecreaseDecimal\": \"Riduce il numero di decimali\",\n  \"formatIncreaseDecimal\": \"Aumenta il numero di decimali\",\n  \"freeze\": \"Blocca riquadri\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Blocca riquadri\",\n    \"freezeRows\": \"Blocca righe\",\n    \"freezeColumns\": \"Blocca colonne\",\n    \"unfreeze\": \"Sblocca riquadri\"\n  },\n  \"italic\": \"Corsivo\",\n  \"merge\": \"Unisci celle\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Unisci tutte le celle\",\n    \"mergeHorizontally\": \"Unisci orizzontalmente\",\n    \"mergeVertically\": \"Unisci verticalmente\",\n    \"unmerge\": \"Dividi celle\"\n  },\n  \"open\": \"Apri...\",\n  \"paste\": \"Incolla\",\n  \"quickAccess\": {\n    \"redo\": \"Ripristina\",\n    \"undo\": \"Annulla\"\n  },\n  \"saveAs\": \"Salva con nome...\",\n  \"sortAsc\": \"Ordine crescente\",\n  \"sortDesc\": \"Ordine decrescente\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Ordina fogli dall'A alle Z\",\n    \"sortSheetDesc\": \"Ordina fogli dalle Z all'A\",\n    \"sortRangeAsc\": \"Ordina intervalli dall'A alle Z\",\n    \"sortRangeDesc\": \"Ordina intervalli dalle Z all'A\"\n  },\n  \"textColor\": \"Colore carattere\",\n  \"textWrap\": \"Testo a capo\",\n  \"underline\": \"Sottolineato\",\n  \"validation\": \"Convalida dati...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Impossibile inserire cellule a causa di una possibile perdita di dati. Seleziona un altro punto di inserimento o cancellare i dati dalla fine del foglio di lavoro.\",\n    \"filterRangeContainingMerges\": \"Impossibile creare un filtro all'interno di un intervallo che contiene celle unite.\"\n  },\n  \"tabs\": {\n    \"home\": \"Home\",\n    \"insert\": \"Inserisci\",\n    \"data\": \"Dati\"\n  }\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Vicino\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Annulla\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Annulla\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}