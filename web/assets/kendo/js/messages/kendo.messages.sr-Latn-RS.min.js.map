{"version": 3, "sources": ["messages/kendo.messages.sr-Latn-RS.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "noColor", "clearColor", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "overflowAnchor", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "cleanFormatting", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "units", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "expandCollapseColumnHeader", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "confirmationDialog", "text", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "colorPicker", "toolbar", "alignmentButtons", "backgroundColor", "borders", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "ListBox", "tools", "moveUp", "moveDown", "transferTo", "transferFrom", "transferAllTo", "transferAllFrom", "TreeView", "Upload", "localization", "clearSelectedFiles", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "progress", "Dialog", "close", "Calendar", "weekColumnHeader", "<PERSON><PERSON>", "Confirm", "Prompt", "DateInput", "hour", "minute", "dayperiod", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN;;;;;;;;;;;;;;;;;;;;;;;;CA0BF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,YACTC,OAAU,WACVC,QAAW,YACXC,WAAc,iBAMZV,MAAMC,GAAGU,cACbX,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,UACpDE,MAAS,YACTC,OAAU,WACVC,QAAW,YACXC,WAAc,iBAMZV,MAAMC,GAAGW,aACbZ,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,UACnDQ,cAAiB,oBACjBC,eAAkB,qBAClBC,OAAU,SACVC,QAAW,SACXC,KAAQ,SACRC,SAAY,kBACZC,KAAQ,YACRC,OAAU,eAMRpB,MAAMC,GAAGoB,SACbrB,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,UAC/CiB,KAAQ,OACRC,OAAU,SACVC,UAAa,WACbC,cAAiB,SACjBC,YAAe,UACfC,UAAa,UACbC,cAAiB,0BACjBC,YAAe,wBACfC,aAAgB,yBAChBC,YAAe,4BACfC,oBAAuB,wBACvBC,kBAAqB,sBACrBC,OAAU,YACVC,QAAW,SACXC,WAAc,oBACdC,OAAU,mBACVC,YAAe,cACfC,WAAc,aACdC,WAAc,aACdC,SAAY,mBACZC,SAAY,6BACZC,gBAAmB,mBACnBC,SAAY,0BACZC,gBAAmB,uBACnBC,YAAe,SACfC,WAAc,SACdC,UAAa,OACbC,UAAa,gBACbC,MAAS,OACTC,YAAe,qBACfC,WAAc,YACdC,eAAkB,YAClBC,QAAW,gBACXC,YAAe,WACfC,YAAe,MACfC,gBAAmB,yEACnBC,WAAc,iDACdC,cAAiB,iGACjBC,kBAAqB,4CACrBC,gBAAmB,aACnBC,aAAgB,qBAChBC,WAAc,cACdC,YAAe,cACfC,eAAkB,aAClBC,UAAa,SACbC,eAAkB,aAClBC,SAAY,QACZC,YAAe,SACfC,oBAAuB,gCACvBC,aAAgB,aAChBC,aAAgB,UAChBC,sBAAyB,MACzBC,aAAgB,WAChBC,gBAAmB,qBACnBC,YAAe,iBACfC,cAAiB,gCACjBC,eAAkB,iCAClBC,YAAe,oBACfC,YAAe,oBACfC,UAAa,aACbC,aAAgB,iBAChBC,SAAY,KACZC,YAAe,sBACfC,SAAY,SACZC,QAAW,SACXC,iBAAoB,gBACpBC,QAAW,SACXC,QAAW,SACXC,MAAS,SACTC,OAAU,SACVC,MAAS,WACTC,YAAe,iBACfC,YAAe,sBACfC,WAAc,kBACdC,UAAa,aACbC,WAAc,WACdC,SAAY,YACZC,GAAM,KACNC,OAAU,MACVC,YAAe,YACfC,gBAAmB,aACnBC,SAAY,gBACZC,0BAA6B,kCAC7BC,UAAa,iBACbC,YAAe,oBACfC,WAAc,kBACdC,aAAgB,sBAChBC,eAAkB,wBAClBC,cAAiB,uBACjBC,gBAAmB,yBACnBC,kBAAqB,2BACrBC,iBAAoB,0BACpBC,gBAAmB,sBACnBC,kBAAqB,wBACrBC,iBAAoB,uBACpBC,YAAe,sBACfrG,QAAW,SACXsG,KAAQ,MACRC,eAAkB,2BAMhBvH,MAAMC,GAAGuH,cACbxH,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,UACpD+C,WAAc,YACdE,QAAW,eACXE,YAAe,MACfD,YAAe,WACfK,kBAAqB,4CACrBT,YAAe,qBACfO,WAAc,iDACdD,gBAAmB,yEACnBE,cAAiB,iGACjB8D,cAAiB,wCACjBC,OAAU,WAMR1H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,UACnDuH,OAAU,QACVC,QAAW,UACX9G,OAAU,SACV+G,MAAS,QACTC,SAAY,cAMV/H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,aACNC,IAAO,kBACPC,WAAc,aACdC,SAAY,SACZC,eAAkB,YAClBC,SAAY,cACZC,OAAU,mBACVC,UAAa,eACbC,QAAW,SACXC,WAAc,eAEhBC,QACEV,GAAM,gBACNC,IAAO,kBACPU,IAAO,yBACPC,GAAM,cACNC,IAAO,0BACPC,GAAM,cACNR,OAAU,mBACVC,UAAa,gBAEfQ,MACEf,GAAM,gBACNC,IAAO,kBACPU,IAAO,0BACPC,GAAM,WACNC,IAAO,qBACPC,GAAM,cACNR,OAAU,mBACVC,UAAa,gBAEfS,OACEhB,GAAM,gBACNC,IAAO,kBACPK,OAAU,mBACVC,UAAa,mBAObzI,MAAMC,GAAGkJ,aACbnJ,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQC,UACnD+I,KAAQ,4CACRC,MAAS,2CACTzB,OAAU,WACVC,QAAW,aACX9G,OAAU,SACV+G,MAAS,SACTwB,IAAO,IACPC,GAAM,MACNC,YAAe,uBACfzB,SAAY,WACZ0B,MAAS,WACTjJ,OAAU,cAMRR,MAAMC,GAAGkJ,aACbnJ,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,gBACNC,IAAO,kBACPC,WAAc,aACdC,SAAY,SACZC,eAAkB,YAClBC,SAAY,cACZC,OAAU,mBACVC,UAAa,eACbC,QAAW,SACXC,WAAc,eAEhBC,QACEV,GAAM,gBACNC,IAAO,kBACPU,IAAO,4BACPC,GAAM,cACNC,IAAO,0BACPC,GAAM,cACNR,OAAU,mBACVC,UAAa,gBAEfQ,MACEf,GAAM,gBACNC,IAAO,kBACPU,IAAO,0BACPC,GAAM,WACNC,IAAO,wBACPC,GAAM,SACNR,OAAU,mBACVC,UAAa,gBAEfS,OACEhB,GAAM,gBACNC,IAAO,kBACPK,OAAU,mBACVC,UAAa,mBAObzI,MAAMC,GAAGyJ,mBACb1J,MAAMC,GAAGyJ,iBAAiBvJ,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyJ,iBAAiBvJ,UAAUC,QAAQC,UACzDsJ,SAAY,cACZ7B,MAAS,SACT/G,OAAU,SACV2G,OAAU,WAMR1H,MAAMC,GAAG2J,QACb5J,MAAMC,GAAG2J,MAAMzJ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,MAAMzJ,UAAUC,QAAQC,UAC9CwJ,SACEC,SAAY,aACZC,OAAU,gBACVC,YAAe,cACfC,aAAgB,aAChBC,IAAO,gBAET1J,OAAU,WACV2J,4BAA+B,mBAC/BC,sBAAyB,mBACzBC,QAAW,UACXC,QACEC,aAAgB,SAChBC,YAAe,UACfC,IAAO,OACPC,gBAAmB,UACnBC,UAAa,UACbC,qBAAwB,UACxBC,gBAAmB,UACnBC,MAAS,QACTzB,MAAS,SACT0B,YAAe,YAEjBC,KAAQ,UACRC,OACEC,IAAO,MACPT,IAAO,OACPU,MAAS,QACTL,MAAS,QACTM,KAAQ,UACRC,KAAQ,aAORrL,MAAMC,GAAGqL,OACbtL,MAAMC,GAAGqL,KAAKnL,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqL,KAAKnL,UAAUC,QAAQC,UAC7CkL,UACE/K,OAAU,iBACVgL,WAAc,WACdC,OAAU,qBACVpB,QAAW,UACXqB,KAAQ,SACRC,MAAS,mBACTzB,IAAO,eACPc,KAAQ,kBACRY,OAAU,YACVC,OAAU,YAEZC,UACEC,aAAgB,WAChBC,aAAgB,oDAChBC,cAAiB,WAEnBC,UAAa,eACbC,2BAA8B,MAM5BnM,MAAMC,GAAGmM,WACbpM,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,UAC/CgM,OAAU,0BACVC,QAAW,gBACXC,cAAiB,qBACjBC,MAAS,iBACTjB,UACIG,KAAQ,SACRG,OAAU,WACVL,WAAc,WACdC,OAAU,qBACVgB,YAAe,wBACfpC,QAAW,UACXsB,MAAS,iBACTzB,IAAO,mBAOXlK,MAAMC,GAAGyM,YACb1M,MAAMC,GAAGyM,UAAUvM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyM,UAAUvM,UAAUC,QAAQC,UAClDsM,MAAS,6EAMP3M,MAAMC,GAAG2M,iBACb5M,MAAMC,GAAG2M,eAAezM,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,eAAezM,UAAUC,SAC/CyM,YAAe,sBACfC,cAAiB,qBAMf9M,MAAMC,GAAG8M,cACb/M,MAAMC,GAAG8M,YAAY5M,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8M,YAAY5M,UAAUC,QAAQC,UACpD2M,MAAS,QACTC,KAAQ,gBACRC,KAAQ,kBACRC,OAAU,iBACVC,QAAW,WACXC,WAAc,eAMZrN,MAAMC,GAAGqN,QACbtN,MAAMC,GAAGqN,MAAMnN,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqN,MAAMnN,UAAUC,QAAQC,UAC9CkN,SAAY,MACZC,QAAW,0BACXb,MAAS,wBACTc,KAAQ,SACRC,GAAM,SACNC,aAAgB,uBAChBC,MAAS,yBACTC,SAAY,8BACZC,KAAQ,6BACRC,KAAQ,8BACRC,QAAW,UACXC,UAAa,mBAMXjO,MAAMC,GAAGiO,gBACblO,MAAMC,GAAGiO,cAAc/N,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiO,cAAc/N,UAAUC,QAAQC,UACtDkN,SAAY,MACZC,QAAW,0BACXb,MAAS,wBACTc,KAAQ,SACRC,GAAM,SACNC,aAAgB,uBAChBC,MAAS,yBACTC,SAAY,8BACZC,KAAQ,6BACRC,KAAQ,8BACRC,QAAW,UACXC,UAAa,mBAMXjO,MAAMC,GAAGkO,YACbnO,MAAMC,GAAGkO,UAAUhO,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkO,UAAUhO,UAAUC,QAAQC,UAClD+N,cAAiB,+BACjBC,aAAgB,gCAChBC,UAAa,gCAMXtO,MAAMC,GAAGsO,iBACbvO,MAAMC,GAAGsO,eAAepO,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsO,eAAepO,UAAUC,QAAQC,UACvD+I,KAAQ,uCACRoF,aAAgB,eAChBzN,OAAU,SACV0N,QAAW,mBACXpF,MAAS,wBACTvB,MAAS,SACT4G,GAAM,KACNlO,OAAU,WACVwH,WACEK,SAAY,SACZC,eAAkB,YAClBF,WAAc,aACdG,SAAY,cACZL,GAAM,gBACNC,IAAO,sBAOPnI,MAAMC,GAAG0O,mBACb3O,MAAMC,GAAG0O,iBAAiBxO,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0O,iBAAiBxO,UAAUC,QAAQC,UACzDuO,aACEC,MAAS,QACTC,OAAU,UACVC,MAAS,SACTC,OAAU,WACVC,QAAW,UACXC,OAAU,YAEZJ,QACEK,YAAe,mBACfC,SAAY,WAEdL,OACEI,YAAe,mBACfC,SAAY,YAEdJ,QACEI,SAAY,eACZD,YAAe,mBACfE,SAAY,iBAEdJ,SACEE,YAAe,mBACfE,SAAY,gBACZD,SAAY,WACZlE,IAAO,QAETgE,QACEC,YAAe,mBACfE,SAAY,gBACZD,SAAY,aACZ1B,GAAM,QAERjD,KACE6E,MAAS,QACTC,YAAe,WACfV,MAAS,QACTW,MAAS,QACTC,WAAc,UACdC,GAAM,OAERC,iBACE/B,MAAS,OACTgC,OAAU,QACVC,MAAS,SACTC,OAAU,UACV/B,KAAQ,aAEVgC,UACE7E,IAAO,MACP8E,QAAW,YACXC,QAAW,aAOXjQ,MAAMC,GAAGiQ,YACblQ,MAAMC,GAAGiQ,UAAU/P,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiQ,UAAU/P,UAAUC,QAAQC,UAClD8P,OAAU,UACVlH,KAAQ,OACRmH,MAAS,UACTC,KAAQ,QACRC,YAAe,kBACfC,YAAe,sBACfC,MAAS,QACTxF,KAAQ,UACRxK,OAAU,WACV6J,QAAW,UACXoG,kBAAqB,iBACrBC,cAAiB,6BACjBC,eAAkB,uBAClB7E,UACEE,aAAgB,uDAElBf,OACEC,IAAO,MACPE,KAAQ,UACRwF,SAAY,gBACZC,OAAU,SACV1F,MAAS,SAEX2F,oBACEL,kBAAqB,iCACrBM,uBAA0B,4BAC1BC,mBAAsB,mBACtBC,gBAAmB,sBACnBC,qBAAwB,8BACxBC,iBAAoB,gBACpBC,gBAAmB,yEACnBC,cAAiB,6DAEnB/G,QACEjB,MAAS,SACTyB,MAAS,QACTL,IAAO,OACP6G,YAAe,qBACfC,YAAe,OACfC,OAAU,SACVC,SAAY,IACZC,cAAiB,2BACjBC,YAAe,sBACfC,kBAAqB,sDACrBC,oBAAuB,iBACvBC,qBAAwB,iBACxBC,cAAiB,iBACjBC,WAAc,qBACdxH,YAAe,cAOfxK,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS6R,gBACpDlS,MAAMiS,YAAY5R,SAAS6R,cAC3BpS,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS6R,eACxCC,WAAc,YACdC,cAAiB,oBACjBC,wBAA2B,6BAC3BC,sBAAyB,2BACzBC,eAAkB,YAClBC,WAAc,UACdC,UAAa,YACbC,YAAe,YACfC,aAAgB,YAChBC,UAAa,aACbC,MAAS,mBACTC,YAAe,sBACfvS,MAAS,YACTC,OAAU,cAIRR,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS0S,UACpD/S,MAAMiS,YAAY5R,SAAS0S,QAC3BjT,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS0S,SACxCxS,MAAS,YACTyK,KAAQ,WACRxK,OAAU,WACVwS,OAAU,SACVxG,MAAS,SACTyG,OAAU,WACVC,OAAU,KACVC,mBACE9J,MAAS,SACT+J,YACExK,OAAU,OACVyK,SAAY,SACZpK,KAAQ,SAGZqK,kBACEjK,MAAS,QAEXkK,gBACElK,MAAS,oBAEXmK,eACEnK,MAAS,SAEXoK,iBACEpK,MAAS,aACTqK,SACCC,aAAgB,iBAChB/R,cAAiB,SACjBE,aAAgB,kBAChBC,YAAe,4BACf6R,SAAY,iBACZC,YAAe,oBACfC,YAAe,mBAGlBC,aACE1K,MAAS,kBACTqK,SACEM,WAAc,YACdC,kBAAqB,qBACrBC,gBAAmB,mBACnBC,QAAW,UAGfC,cACE/K,MAAS,mBACTqK,SACEW,YAAe,mBACfC,WAAc,mBACdC,cAAiB,mBACjBC,SAAY,qBAGhBC,oBACEC,KAAQ,kDACRrL,MAAS,mBAEXsL,kBACEtL,MAAS,sBACTuL,YAAe,0CACfC,UAAa,iBACbC,UACEC,IAAO,mBACPnM,OAAU,OACV8L,KAAQ,QACRzL,KAAQ,OACR+L,OAAU,sBACVC,KAAQ,SAEVC,WACEC,YAAe,WACfC,SAAY,WACZC,QAAW,SACXC,WAAc,cACdC,QAAW,aACXC,WAAc,kBACdC,qBAAwB,sBACxBC,kBAAqB,wBAEvBC,kBACER,YAAe,eACfC,SAAY,eACZC,QAAW,mBACXC,WAAc,wBACdC,QAAW,iBACXC,WAAc,sBACdC,qBAAwB,0BACxBC,kBAAqB,2BACrBV,OAAU,iCAEZY,QACEd,SAAY,aACZe,SAAY,YACZC,IAAO,MACPC,IAAO,MACPtM,MAAS,YACTqB,MAAS,QACTL,IAAO,OACPuL,cAAiB,wBACjBC,YAAe,eACfC,YAAe,qBACfC,SAAY,eACZtB,UAAa,eACbD,YAAe,eACfwB,YAAe,oBAEjBC,cACEC,UAAa,iBACbC,YAAe,mBAGnBC,gBACEnN,MAAS,WACTuM,QACEa,SAAY,kBACZC,WAAc,kBACdC,WAAc,QACdC,UAAa,kBACbC,QAAW,UACXC,YAAe,eACfC,MAAS,UACTC,WAAc,WACdC,OAAU,SACVC,aAAgB,eAChBC,WAAc,eAGlBC,oBACEC,aAAgB,0CAElBC,mBACEjO,MAAS,uBAETgO,aAAgB,4FAChBzB,QACE2B,QAAW,eACXC,OAAU,SACVC,SAAY,gBAGhBC,4BACEL,aAAgB,oDAKhBrX,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAASsX,aACpD3X,MAAMiS,YAAY5R,SAASsX,WAC3B7X,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAASsX,YACxC9W,cAAiB,6BACjBC,eAAkB,6BAClB8W,cAAiB,sBACjBC,kBAAqB,sBACrBtX,MAAS,YACTmH,OAAU,QACVoQ,aAAgB,uBAChBhQ,MAAS,SACTiQ,OAAU,WACVC,aAAgB,QAChB1O,IAAO,IACPC,GAAM,MACNvB,WACEC,QACEI,SAAY,eACZC,eAAkB,kBAClBF,WAAc,mBACdG,SAAY,qBAEdU,MACEf,GAAO,WACPC,IAAO,aACPa,GAAO,eACPF,GAAO,kBAETF,QACEV,GAAM,gBACNC,IAAO,kBACPU,IAAO,4BACPC,GAAM,cACNC,IAAO,0BACPC,GAAM,oBAMRhJ,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS4X,cACpDjY,MAAMiS,YAAY5R,SAAS4X,YAC3BnY,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS4X,aACxCpF,MAAS,mBACTC,YAAe,sBACfvS,MAAS,YACTC,OAAU,cAIRR,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS6X,UACpDlY,MAAMiS,YAAY5R,SAAS6X,QAC3BpY,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS6X,SACxCrT,cAAiB,sBACjBC,eAAkB,uBAClBC,YAAe,sBACfC,YAAe,sBACfgB,UAAa,aACbmS,kBACExE,aAAgB,iBAChB/R,cAAiB,SACjBE,aAAgB,kBAChBC,YAAe,4BACf6R,SAAY,iBACZC,YAAe,oBACfC,YAAe,kBAEjBsE,gBAAmB,WACnB9W,KAAQ,OACR+W,QAAW,QACXJ,aACEpF,MAAS,mBACTC,YAAe,uBAEjBwF,KAAQ,UACRC,IAAO,MACPrT,aAAgB,iBAChBD,UAAa,cACbuT,YAAe,qBACfzX,OAAU,SACV0X,WAAc,OACd7V,SAAY,gBACZ8V,OAAU,uBACVC,aACEC,UAAa,aACbhQ,OAAU,OACViQ,QAAW,UACXC,UAAa,cACbzF,SAAY,SACZpK,KAAQ,QACRoH,KAAQ,QACR0I,SAAY,cACZC,SAAY,WACZC,YAAe,mBAEjBC,sBAAyB,kBACzBC,sBAAyB,qBACzBC,OAAU,mBACVC,eACEhF,YAAe,mBACfC,WAAc,gBACdC,cAAiB,iBACjBC,SAAY,oBAEdjT,OAAU,SACV+X,MAAS,kBACTC,cACEvF,WAAc,YACdC,kBAAqB,qBACrBC,gBAAmB,mBACnBC,QAAW,WAEbqF,KAAQ,YACRC,MAAS,SACTC,aACEC,KAAQ,SACRC,KAAQ,aAEVC,OAAU,aACVC,QAAW,oBACXC,SAAY,mBACZC,aACEC,aAAgB,wBAChBC,cAAiB,wBACjBC,aAAgB,0BAChBC,cAAiB,2BAEnBC,UAAa,cACbC,SAAY,kBACZ9Y,UAAa,WACb+Y,WAAc,4BAIZva,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAASma,OACpDxa,MAAMiS,YAAY5R,SAASma,KAC3B1a,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAASma,MACxCC,QACEC,sBAAyB,iJACzBC,4BAA+B,0DAC/BC,gBAAmB,yEAErBC,MACEC,KAAQ,OACRC,OAAU,QACVC,KAAQ,cAORhb,MAAMC,GAAGgb,SACbjb,MAAMC,GAAGgb,OAAO9a,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgb,OAAO9a,UAAUC,SACvC8a,oBAAuB,aACvBC,oBAAuB,cAMrBnb,MAAMC,GAAGmb,UACbpb,MAAMC,GAAGmb,QAAQjb,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmb,QAAQjb,UAAUC,QAAQC,UAChDgb,OACErI,OAAU,UACVsI,OAAU,cACVC,SAAY,cACZC,WAAc,YACdC,aAAgB,YAChBC,cAAiB,iBACjBC,gBAAmB,qBAOnB3b,MAAMC,GAAGmM,WACbpM,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,UACjDgM,OAAU,0BACVC,QAAW,gBACXC,cAAiB,qBACjBC,MAAS,iBACTjB,UACIG,KAAQ,SACRG,OAAU,WACVL,WAAc,WACdC,OAAU,qBACVgB,YAAe,wBACfpC,QAAW,UACXsB,MAAS,iBACTzB,IAAO,mBAOTlK,MAAMC,GAAG2b,WACb5b,MAAMC,GAAG2b,SAASzb,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2b,SAASzb,UAAUC,QAAQC,UACjDiM,QAAW,gBACXC,cAAiB,qBACjBC,MAAS,YAMPxM,MAAMC,GAAG4b,SACb7b,MAAMC,GAAG4b,OAAO1b,UAAUC,QAAQ0b,aAClChc,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4b,OAAO1b,UAAUC,QAAQ0b,cAC/ClQ,OAAU,wBACVpL,OAAU,WACVgM,MAAS,SACTwG,OAAU,SACV+I,mBAAsB,SACtBC,oBAAuB,qBACvBvU,cAAiB,gDACjBwU,gBAAmB,YACnBC,eAAkB,oBAClBC,cAAiB,aACjBC,aAAgB,aAChBC,sBAAyB,eACzBC,qBAAwB,SACxBC,mBAAsB,kCACtBC,mBAAsB,gCACtBC,qBAAwB,kCAMtBzc,MAAMC,GAAGyc,YACb1c,MAAMC,GAAGyc,UAAUvc,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyc,UAAUvc,UAAUC,QAAQC,UAClDsc,SAAY,kBACZC,QAAW,mBACX9G,IAAO,0CACPC,IAAO,yCACP8G,KAAQ,mBACRC,MAAS,0BACTC,IAAO,2BACP9T,KAAQ,yBACR+T,YAAe,8DAKbhd,MAAMC,GAAGgd,WACbjd,MAAMC,GAAGgd,SAAS5c,SAClBP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgd,SAAS5c,UAC7BiM,QAAS,mBAMTtM,MAAMC,GAAGid,SACbld,MAAMC,GAAGid,OAAO/c,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGid,OAAO/c,UAAUC,QAAQ0b,cAC/CqB,MAAS,cAKPnd,MAAMC,GAAGmd,WACbpd,MAAMC,GAAGmd,SAASjd,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmd,SAASjd,UAAUC,QAAQC,UACjDgd,iBAAoB,MAMlBrd,MAAMC,GAAGqd,QACbtd,MAAMC,GAAGqd,MAAMnd,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqd,MAAMnd,UAAUC,QAAQ0b,cAC9C5I,OAAU,QAMRlT,MAAMC,GAAGsd,UACbvd,MAAMC,GAAGsd,QAAQpd,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsd,QAAQpd,UAAUC,QAAQ0b,cAChD5I,OAAU,KACV1S,OAAU,cAKRR,MAAMC,GAAGud,SACbxd,MAAMC,GAAGud,OAAOrd,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGud,OAAOrd,UAAUC,QAAQ0b,cAC/C5I,OAAU,KACV1S,OAAU,cAKRR,MAAMC,GAAGwd,YACXzd,MAAMC,GAAGwd,UAAUtd,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwd,UAAUtd,UAAUC,QAAQC,UAClDgL,KAAQ,SACRF,MAAS,QACTD,IAAO,MACP8E,QAAW,gBACX0N,KAAQ,OACRC,OAAU,SACV/N,OAAU,UACVgO,UAAa,YAIhBC,OAAO7d,MAAM8d", "file": "kendo.messages.sr-Latn-RS.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n/**\n * Kendo UI v2017.3.1026 (http://www.telerik.com/kendo-ui)\n * Copyright 2017 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.\n *\n * Kendo UI commercial licenses may be obtained at\n * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete\n * If you do not own a commercial license, this file shall be governed by the trial license terms.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n*/\n\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"primeniti\",\n  \"cancel\": \"otkažite\",\n  \"noColor\": \"nema boje\",\n  \"clearColor\": \"O<PERSON><PERSON>i boja\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"primeniti\",\n  \"cancel\": \"otkažite\",\n  \"noColor\": \"nema boje\",\n  \"clearColor\": \"Očisti boja\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Sortiraj rastući\",\n  \"sortDescending\": \"Sortiraj padajuće\",\n  \"filter\": \"Filter\",\n  \"columns\": \"Kolone\",\n  \"done\": \"Gotovo\",\n  \"settings\": \"Postavke kolone\",\n  \"lock\": \"Zaključaj\",\n  \"unlock\": \"Otključaj\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Bold\",\n  \"italic\": \"Italic\",\n  \"underline\": \"Podvući\",\n  \"strikethrough\": \"Prorez\",\n  \"superscript\": \"Nadznak\",\n  \"subscript\": \"Podznak\",\n  \"justifyCenter\": \"Poravnajte tekst centar\",\n  \"justifyLeft\": \"Poravnajte tekst levo\",\n  \"justifyRight\": \"Poravnajte tekst desno\",\n  \"justifyFull\": \"Poravnajte tekst do ivica\",\n  \"insertUnorderedList\": \"Umetni nesređenu lisu\",\n  \"insertOrderedList\": \"Umetni sređenu lisu\",\n  \"indent\": \"Uvlačenje\",\n  \"outdent\": \"Izvuci\",\n  \"createLink\": \"Ubacite hipervezu\",\n  \"unlink\": \"Ukloni hipervezu\",\n  \"insertImage\": \"Ubaci sliku\",\n  \"insertFile\": \"Ubaci fajl\",\n  \"insertHtml\": \"Ubaci HTML\",\n  \"viewHtml\": \"Pregledajte HTML\",\n  \"fontName\": \"Izaberite porodicu fontova\",\n  \"fontNameInherit\": \"(nasleđeni font)\",\n  \"fontSize\": \"Izaberite veličinu font\",\n  \"fontSizeInherit\": \"(nasleđena veličina)\",\n  \"formatBlock\": \"Format\",\n  \"formatting\": \"Format\",\n  \"foreColor\": \"Boja\",\n  \"backColor\": \"Boja pozadine\",\n  \"style\": \"Stil\",\n  \"emptyFolder\": \"Prazan direktorium\",\n  \"uploadFile\": \"Otpremiti\",\n  \"overflowAnchor\": \"Još alata\",\n  \"orderBy\": \"Sortirati po:\",\n  \"orderBySize\": \"Veličina\",\n  \"orderByName\": \"Ime\",\n  \"invalidFileType\": \"Izabrana datoteka \\\"{0}\\\" nije važeća. Podržani tipovi datoteka su {1}.\",\n  \"deleteFile\": 'Dali ste sigurni da želite da izbrišete \"{0}\"?',\n  \"overwriteFile\": 'Datoteka sa imenom \"{0}\" već postoji u trenutnom direktorijumu. Da li želite da je prepisati?',\n  \"directoryNotFound\": \"Direktorium sa ovim imenom nije pronađen.\",\n  \"imageWebAddress\": \"Web adresa\",\n  \"imageAltText\": \"Alternativni tekst\",\n  \"imageWidth\": \"Širina (px)\",\n  \"imageHeight\": \"Visina (px)\",\n  \"fileWebAddress\": \"Web adresa\",\n  \"fileTitle\": \"Naslov\",\n  \"linkWebAddress\": \"Web adresa\",\n  \"linkText\": \"Tekst\",\n  \"linkToolTip\": \"Oznaka\",\n  \"linkOpenInNewWindow\": \"Otvorite link u novom prozoru\",\n  \"dialogUpdate\": \"Ažuriranje\",\n  \"dialogInsert\": \"Ubacite\",\n  \"dialogButtonSeparator\": \"ili\",\n  \"dialogCancel\": \"Otkazati\",\n  \"cleanFormatting\": \"Čisto formatiranje\",\n  \"createTable\": \"Napravi tabelu\",\n  \"addColumnLeft\": \"Dodajte kolonu sa leve strane\",\n  \"addColumnRight\": \"Dodajte kolonu sa desne strane\",\n  \"addRowAbove\": \"Dodajte red iznad\",\n  \"addRowBelow\": \"Dodajte red ispod\",\n  \"deleteRow\": \"Obriši red\",\n  \"deleteColumn\": \"Izbriši kolonu\",\n  \"dialogOk\": \"Ok\",\n  \"tableWizard\": \"Čarobnjak za tebele\",\n  \"tableTab\": \"Tabele\",\n  \"cellTab\": \"Čelija\",\n  \"accessibilityTab\": \"Pristupačnost\",\n  \"caption\": \"Naslov\",\n  \"summary\": \"Rezime\",\n  \"width\": \"Širina\",\n  \"height\": \"Visina\",\n  \"units\": \"Jedinice\",\n  \"cellSpacing\": \"Razmak ćelija\",\n  \"cellPadding\": \"Popunjenost ćelija\",\n  \"cellMargin\": \"Margina ćelija\",\n  \"alignment\": \"Poravnanje\",\n  \"background\": \"Pozadina\",\n  \"cssClass\": \"CSS Klasa\",\n  \"id\": \"ID\",\n  \"border\": \"Rub\",\n  \"borderStyle\": \"Stil ruba\",\n  \"collapseBorders\": \"Sakrij rub\",\n  \"wrapText\": \"Zamotaj tekst\",\n  \"associateCellsWithHeaders\": \"Udružene ćelije sa zaglavljima\",\n  \"alignLeft\": \"Poravnati levo\",\n  \"alignCenter\": \"Poravnajte centar\",\n  \"alignRight\": \"Poravnati desno\",\n  \"alignLeftTop\": \"Poravnati Levo Gore\",\n  \"alignCenterTop\": \"Poravnati Centar Gore\",\n  \"alignRightTop\": \"Poravnati Desno Gore\",\n  \"alignLeftMiddle\": \"Poravnati Levo Sredina\",\n  \"alignCenterMiddle\": \"Poravnati Centar Sredina\",\n  \"alignRightMiddle\": \"Poravnati Desno Sredina\",\n  \"alignLeftBottom\": \"Poravnati Levo Dole\",\n  \"alignCenterBottom\": \"Poravnati Centar Dole\",\n  \"alignRightBottom\": \"Poravnati Desno Dole\",\n  \"alignRemove\": \"Uklonite poravnanje\",\n  \"columns\": \"Kolone\",\n  \"rows\": \"Red\",\n  \"selectAllCells\": \"Izaberite sve ćelije\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Otpremiti\",\n  \"orderBy\": \"Sortirati po\",\n  \"orderByName\": \"Ime\",\n  \"orderBySize\": \"Veličina\",\n  \"directoryNotFound\": \"Direktorium sa ovim imenom nije pronađen.\",\n  \"emptyFolder\": \"Prazan direktorium\",\n  \"deleteFile\": 'Dali ste sigurni da želite da izbrišete \"{0}\"?',\n  \"invalidFileType\": \"Izabrana datoteka \\\"{0}\\\" nije važeća. Podržani tipovi datoteka su {1}.\",\n  \"overwriteFile\": \"Datoteka sa imenom \\\"{0}\\\" već postoji u trenutnom direktorijumu. Da li želite da je prepisati?\",\n  \"dropFilesHere\": \"otpustite datoteku ovde za otpremanje\",\n  \"search\": \"Traži\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"tačno\",\n  \"isFalse\": \"netačno\",\n  \"filter\": \"Filter\",\n  \"clear\": \"Jasno\",\n  \"operator\": \"Operator\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Jednako na\",\n    \"neq\": \"Nije jednako na\",\n    \"startswith\": \"Počinje sa\",\n    \"contains\": \"Sadrži\",\n    \"doesnotcontain\": \"Ne sadrži\",\n    \"endswith\": \"Završava sa\",\n    \"isnull\": \"Nikakva vrednost\",\n    \"isnotnull\": \"Ima vrednost\",\n    \"isempty\": \"Prazno\",\n    \"isnotempty\": \"Nije prazno\"\n  },\n  \"number\": {\n    \"eq\": \"Je jednako na\",\n    \"neq\": \"Nije jednako na\",\n    \"gte\": \"Je više ili jednako na\",\n    \"gt\": \"Je veći od\",\n    \"lte\": \"Je manje ili jednako na\",\n    \"lt\": \"Je manje od\",\n    \"isnull\": \"Nikakva vrednost\",\n    \"isnotnull\": \"Ima vrednost\"\n  },\n  \"date\": {\n    \"eq\": \"Je jednako na\",\n    \"neq\": \"Nije jednako na\",\n    \"gte\": \"Je nakon ili jednako na\",\n    \"gt\": \"Je nakon\",\n    \"lte\": \"Pre ili jednako na\",\n    \"lt\": \"Je pre nego\",\n    \"isnull\": \"Nikakva vrednost\",\n    \"isnotnull\": \"Ima vrednost\"\n  },\n  \"enums\": {\n    \"eq\": \"Je jednako na\",\n    \"neq\": \"Nije jednako na\",\n    \"isnull\": \"Nikakva vrednost\",\n    \"isnotnull\": \"Ima vrednost\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Pokažite stavke s vrijednosnim koi imaju:\",\n  \"title\": \"Pokažite stavke s vrijednosnim koi imaju\",\n  \"isTrue\": \"je tačno\",\n  \"isFalse\": \"nije tačno\",\n  \"filter\": \"filter\",\n  \"clear\": \"Očisti\",\n  \"and\": \"I\",\n  \"or\": \"Ili\",\n  \"selectValue\": \"-Izaberite vrednost-\",\n  \"operator\": \"Operator\",\n  \"value\": \"Vrednost\",\n  \"cancel\": \"Otkazati\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Je jednako na\",\n    \"neq\": \"Nije jednako na\",\n    \"startswith\": \"Počinje sa\",\n    \"contains\": \"Sadrži\",\n    \"doesnotcontain\": \"Ne sadrži\",\n    \"endswith\": \"Završava sa\",\n    \"isnull\": \"Nikakva vrednost\",\n    \"isnotnull\": \"Ima vrednost\",\n    \"isempty\": \"Prazno\",\n    \"isnotempty\": \"Nije prazno\"\n  },\n  \"number\": {\n    \"eq\": \"Je jednako na\",\n    \"neq\": \"Nije jednako na\",\n    \"gte\": \"Je veći od ili jednak na\",\n    \"gt\": \"Je veći od\",\n    \"lte\": \"Je manje ili jednako od\",\n    \"lt\": \"Je manje od\",\n    \"isnull\": \"Nikakva vrednost\",\n    \"isnotnull\": \"Ima vrednost\"\n  },\n  \"date\": {\n    \"eq\": \"Je jednako na\",\n    \"neq\": \"Nije jednako na\",\n    \"gte\": \"Je nakon ili jednako na\",\n    \"gt\": \"Je nakon\",\n    \"lte\": \"Je Pre ili jednako na\",\n    \"lt\": \"Je Pre\",\n    \"isnull\": \"Nikakva vrednost\",\n    \"isnotnull\": \"Ima vrednost\"\n  },\n  \"enums\": {\n    \"eq\": \"Je jednako na\",\n    \"neq\": \"Nije jednako na\",\n    \"isnull\": \"Nikakva vrednost\",\n    \"isnotnull\": \"Ima vrednost\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Izaberi sve\",\n  \"clear\": \"Očisti\",\n  \"filter\": \"Filter\",\n  \"search\": \"Traži\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Dodaj Dete\",\n    \"append\": \"Dodaj Zadatak\",\n    \"insertAfter\": \"Dodaj Ispod\",\n    \"insertBefore\": \"Dodaj Gore\",\n    \"pdf\": \"Izvozi u PDF\"\n  },\n  \"cancel\": \"Otkazati\",\n  \"deleteDependencyWindowTitle\": \"Obriši zavisnost\",\n  \"deleteTaskWindowTitle\": \"Obrišite zadatak\",\n  \"destroy\": \"Izbriši\",\n  \"editor\": {\n    \"assingButton\": \"Dodeli\",\n    \"editorTitle\": \"Zadatak\",\n    \"end\": \"Kraj\",\n    \"percentComplete\": \"Završen\",\n    \"resources\": \"Resursi\",\n    \"resourcesEditorTitle\": \"Resursi\",\n    \"resourcesHeader\": \"Resursi\",\n    \"start\": \"Start\",\n    \"title\": \"Naslov\",\n    \"unitsHeader\": \"Jedinice\"\n  },\n  \"save\": \"Sačuvaj\",\n  \"views\": {\n    \"day\": \"Den\",\n    \"end\": \"Kraj\",\n    \"month\": \"Mesec\",\n    \"start\": \"Start\",\n    \"week\": \"Nedelja\",\n    \"year\": \"Godina\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Otkaži promene\",\n    \"canceledit\": \"Otkazati\",\n    \"create\": \"Dodajte novi zapis\",\n    \"destroy\": \"Izbriši\",\n    \"edit\": \"Izmeni\",\n    \"excel\": \"Izvozite u Excel\",\n    \"pdf\": \"Izvozi u PDF\",\n    \"save\": \"Sačuvaj promene\",\n    \"select\": \"Izaberite\",\n    \"update\": \"Ažuriraj\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Otkazati\",\n    \"confirmation\": \"Da li ste sigurni da želite izbrisati ovaj zapis?\",\n    \"confirmDelete\": \"Izbriši\"\n  },\n  \"noRecords\": \"Nema stavki.\",\n  \"expandCollapseColumnHeader\": \"\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n    \"noRows\": \"Nema podataka za prikaz\",\n    \"loading\": \"Učitavanje...\",\n    \"requestFailed\": \"Zahtev nije uspeo.\",\n    \"retry\": \"Pokušaj ponovo\",\n    \"commands\": {\n        \"edit\": \"Izmeni\",\n        \"update\": \"Ažuriraj\",\n        \"canceledit\": \"Otkazati\",\n        \"create\": \"Dodajte novi zapis\",\n        \"createchild\": \"Dodadite detalj zapis\",\n        \"destroy\": \"Izbriši\",\n        \"excel\": \"Izvozi u Excel\",\n        \"pdf\": \"Izvozi u PDF\"\n    }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Prevucite zaglavlje kolone i pustite je ovde da se grupiraju do kolonom\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Povećajte vrednost\",\n  \"downArrowText\": \"Smanji vrednost\"\n});\n}\n\n/* MediaPlayer messages */\n\nif (kendo.ui.MediaPlayer) {\nkendo.ui.MediaPlayer.prototype.options.messages =\n$.extend(true, kendo.ui.MediaPlayer.prototype.options.messages,{\n  \"pause\": \"Pauza\",\n  \"play\": \"Reprodukovati\",\n  \"mute\": \"Isključite glas\",\n  \"unmute\": \"Uključite glas\",\n  \"quality\": \"Kvalitet\",\n  \"fullscreen\": \"Ceo ekran\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Sve\",\n  \"display\": \"{0} - {1} od {2} stavke\",\n  \"empty\": \"Nema stavki za prikaz\",\n  \"page\": \"Strana\",\n  \"of\": \"od {0}\",\n  \"itemsPerPage\": \"Predmeti po stranici\",\n  \"first\": \"Idite na prvu stranicu\",\n  \"previous\": \"Idite na prethodnu stranicu\",\n  \"next\": \"Idite na sledeću stranicu\",\n  \"last\": \"Idite na poslednju stranicu\",\n  \"refresh\": \"Osvježi\",\n  \"morePages\": \"Više stranica\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"Sve\",\n  \"display\": \"{0} - {1} od {2} stavke\",\n  \"empty\": \"Nema stavki za prikaz\",\n  \"page\": \"Strana\",\n  \"of\": \"od {0}\",\n  \"itemsPerPage\": \"Predmeti po stranici\",\n  \"first\": \"Idite na prvu stranicu\",\n  \"previous\": \"Idite na prethodnu stranicu\",\n  \"next\": \"Idite na sledeću stranicu\",\n  \"last\": \"Idite na poslednju stranicu\",\n  \"refresh\": \"Osvježi\",\n  \"morePages\": \"Više stranica\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"Spustite polja podataka ovde\",\n  \"columnFields\": \"Spustite kolona podataka ovde\",\n  \"rowFields\": \"Spustite red podataka ovde\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"Pokažite stavke s vrijednosnim koji:\",\n  \"filterFields\": \"Filter polja\",\n  \"filter\": \"Filter\",\n  \"include\": \"Uključi polja...\",\n  \"title\": \"Polja za uključivanje\",\n  \"clear\": \"Očisti\",\n  \"ok\": \"Ok\",\n  \"cancel\": \"Otkazati\",\n  \"operators\": {\n    \"contains\": \"Sadrži\",\n    \"doesnotcontain\": \"Ne sadrži\",\n    \"startswith\": \"Počinje sa\",\n    \"endswith\": \"Završava sa\",\n    \"eq\": \"Je jednako na\",\n    \"neq\": \"Nije jednako na\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Nikad\",\n    \"hourly\": \"Po satu\",\n    \"daily\": \"Dnevno\",\n    \"weekly\": \"Nedeljno\",\n    \"monthly\": \"Mesečno\",\n    \"yearly\": \"Godišnje\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Ponovite svako: \",\n    \"interval\": \" sat(i)\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Ponovite svako: \",\n    \"interval\": \" dan/ovi\"\n  },\n  \"weekly\": {\n    \"interval\": \" nedelja(ma)\",\n    \"repeatEvery\": \"Ponovite svako: \",\n    \"repeatOn\": \"Ponoviti na: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Ponovite svako: \",\n    \"repeatOn\": \"Ponoviti na: \",\n    \"interval\": \" mesec/i\",\n    \"day\": \"Day \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Ponovite svako: \",\n    \"repeatOn\": \"Ponoviti na: \",\n    \"interval\": \" godina(e)\",\n    \"of\": \" od \"\n  },\n  \"end\": {\n    \"label\": \"Kraj:\",\n    \"mobileLabel\": \"Završava\",\n    \"never\": \"Nikad\",\n    \"after\": \"Posle\",\n    \"occurrence\": \" pojava\",\n    \"on\": \"na \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"prvi\",\n    \"second\": \"drugi\",\n    \"third\": \"treći\",\n    \"fourth\": \"četvrti\",\n    \"last\": \"poslednji\"\n  },\n  \"weekdays\": {\n    \"day\": \"den\",\n    \"weekday\": \"radni dan\",\n    \"weekend\": \"vikend\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"ceo dan\",\n  \"date\": \"Data\",\n  \"event\": \"Događaj\",\n  \"time\": \"Vreme\",\n  \"showFullDay\": \"Prikaži ceo dan\",\n  \"showWorkDay\": \"Prikaži radno vreme\",\n  \"today\": \"Danas\",\n  \"save\": \"Začuvaj\",\n  \"cancel\": \"Otkazati\",\n  \"destroy\": \"Izbriši\",\n  \"deleteWindowTitle\": \"Obriši događaj\",\n  \"ariaSlotLabel\": \"Izabrano od {0:t} do {1:t}\",\n  \"ariaEventLabel\": \"{0} na {1:D} u {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"Da li ste sigurni da želite izbrisati ovaj događaj?\"\n  },\n  \"views\": {\n    \"day\": \"Dan\",\n    \"week\": \"Nedelja\",\n    \"workWeek\": \"Radna nedelja\",\n    \"agenda\": \"Agenda\",\n    \"month\": \"Mesec\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Izbrišite ponavljajuću stavku\",\n    \"deleteWindowOccurrence\": \"Obrišite trenutnu događaj\",\n    \"deleteWindowSeries\": \"Izbrišite seriju\",\n    \"editWindowTitle\": \"Edit Recurring Item\",\n    \"editWindowOccurrence\": \"Izmeni ponavljajuću stavku\",\n    \"editWindowSeries\": \"Izmeni seriju\",\n    \"deleteRecurring\": \"Da li želite da izbrišete samo pojavu ovog događaja ili čitavu seriju?\",\n    \"editRecurring\": \"Želite li urediti samo pojavu događaja ili čitavu seriju?\"\n  },\n  \"editor\": {\n    \"title\": \"Naslov\",\n    \"start\": \"Start\",\n    \"end\": \"Kraj\",\n    \"allDayEvent\": \"Celodnevni događaj\",\n    \"description\": \"Opis\",\n    \"repeat\": \"Ponovi\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"Započnite vremensku zonu\",\n    \"endTimezone\": \"Kraj vremenske zone\",\n    \"separateTimezones\": \"Koristite odvojene početne i krajnje vremenske zone\",\n    \"timezoneEditorTitle\": \"Vremenske zone\",\n    \"timezoneEditorButton\": \"Vremenske zone\",\n    \"timezoneTitle\": \"Vremenske zone\",\n    \"noTimezone\": \"Bez vremenske zone\",\n    \"editorTitle\": \"Događaj\"\n  }\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"Sve ivice\",\n  \"insideBorders\": \"Unutrašnji rubovi\",\n  \"insideHorizontalBorders\": \"Unutar horizontalnih ivica\",\n  \"insideVerticalBorders\": \"Unutar vertikalnih ivica\",\n  \"outsideBorders\": \"Van ivica\",\n  \"leftBorder\": \"Lev rub\",\n  \"topBorder\": \"Gorni rub\",\n  \"rightBorder\": \"Desen rub\",\n  \"bottomBorder\": \"Donji rub\",\n  \"noBorders\": \"Nema ivice\",\n  \"reset\": \"Resetovanje boje\",\n  \"customColor\": \"Prilagođena boja...\",\n  \"apply\": \"Primeniti\",\n  \"cancel\": \"Otkazati\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Primeniti\",\n  \"save\": \"Sačuvati\",\n  \"cancel\": \"Otkazati\",\n  \"remove\": \"Ukloni\",\n  \"retry\": \"Ponovo\",\n  \"revert\": \"Vratiti \",\n  \"okText\": \"OK\",\n  \"formatCellsDialog\": {\n    \"title\": \"Format\",\n    \"categories\": {\n      \"number\": \"Broj\",\n      \"currency\": \"Valuta\",\n      \"date\": \"Data\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Font\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Veličina na font\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Ivice\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Poravnanje\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Poravnati levo\",\n     \"justifyCenter\": \"Centar\",\n     \"justifyRight\": \"Poravnati desno\",\n     \"justifyFull\": \"Poravnajte tekst do ivica\",\n     \"alignTop\": \"Poravnati gore\",\n     \"alignMiddle\": \"Poravnati sredina\",\n     \"alignBottom\": \"Poravnati dole\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Spajati ćelije\",\n    \"buttons\": {\n      \"mergeCells\": \"Spoji svi\",\n      \"mergeHorizontally\": \"Spoji horizontalno\",\n      \"mergeVertically\": \"Spoji vertikalno\",\n      \"unmerge\": \"Odvoj\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Zamrznuti paneli\",\n    \"buttons\": {\n      \"freezePanes\": \"Zamrznuti paneli\",\n      \"freezeRows\": \"Zamrznuti redovi\",\n      \"freezeColumns\": \"Zamrznuti koloni\",\n      \"unfreeze\": \"Odzračite panele\"\n    }\n  },\n  \"confirmationDialog\": {\n    \"text\": \"Da li ste sigurni da želite ukloniti ovaj list?\",\n    \"title\": \"Odstranite list\"\n  },\n  \"validationDialog\": {\n    \"title\": \"Validacija podataka\",\n    \"hintMessage\": \"Molimo unesite važeću{0} vrednost {1}.\",\n    \"hintTitle\": \"Validacija {0}\",\n    \"criteria\": {\n      \"any\": \"Svaka vrijednost\",\n      \"number\": \"Broj\",\n      \"text\": \"Tekst\",\n      \"date\": \"Data\",\n      \"custom\": \"Prilagođena Formula\",\n      \"list\": \"Lista\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"veće od\",\n      \"lessThan\": \"manje od\",\n      \"between\": \"između\",\n      \"notBetween\": \"nije između\",\n      \"equalTo\": \"jednako na\",\n      \"notEqualTo\": \"nije jednako na\",\n      \"greaterThanOrEqualTo\": \"više ili jednako na\",\n      \"lessThanOrEqualTo\": \"manje ili jednako na\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"veće od {0}\",\n      \"lessThan\": \"manje od {0}\",\n      \"between\": \"između {0} i {1}\",\n      \"notBetween\": \"nije između {0} i {1}\",\n      \"equalTo\": \"jednako na {0}\",\n      \"notEqualTo\": \"nije jednako na {0}\",\n      \"greaterThanOrEqualTo\": \"više ili jednako na {0}\",\n      \"lessThanOrEqualTo\": \"manje ili jednako na {0}\",\n      \"custom\": \"koja zadovoljava formulu: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Kriterijum\",\n      \"comparer\": \"Uporedite\",\n      \"min\": \"Min\",\n      \"max\": \"Max\",\n      \"value\": \"Vrednost`\",\n      \"start\": \"Start\",\n      \"end\": \"Kraj\",\n      \"onInvalidData\": \"O nevažećim podacima\",\n      \"rejectInput\": \"Odbijte upis\",\n      \"showWarning\": \"Prikaži upozorenje\",\n      \"showHint\": \"Pokaži savet\",\n      \"hintTitle\": \"Naslov savet\",\n      \"hintMessage\": \"Savet poruka\",\n      \"ignoreBlank\": \"Ignoriraj prazno\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Unesite naslov\",\n      \"typeMessage\": \"Unesite poruka\"\n    }\n  },\n  \"exportAsDialog\": {\n    \"title\": \"Izvoz...\",\n    \"labels\": {\n      \"fileName\": \"Ime na datoteka\",\n      \"saveAsType\": \"Sačuvaj kao tip\",\n      \"exportArea\": \"Izvoz\",\n      \"paperSize\": \"Veličina papira\",\n      \"margins\": \"Margine\",\n      \"orientation\": \"Orijentacija\",\n      \"print\": \"Štampaj\",\n      \"guidelines\": \"Smernice\",\n      \"center\": \"Centar\",\n      \"horizontally\": \"Horizontalno\",\n      \"vertically\": \"Vertikalno\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Ne mogu da menjam deo spojene ćelije.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Kopiranje i umetanje\",\n\n    \"errorMessage\": \"Ove akcije se ne mogu pozivati preko menija. Umjesto toga koristite prečice na tastaturi:\",\n    \"labels\": {\n      \"forCopy\": \"za kopiranje\",\n      \"forCut\": \"za rez\",\n      \"forPaste\": \"za umetanje\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"Ta akcija se ne može izvoditi na više izbora.\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Sortirajte opseg od A do Z\",\n  \"sortDescending\": \"Sortirajte opseg od Z do A\",\n  \"filterByValue\": \"Filter po vrednosti\",\n  \"filterByCondition\": \"Filtriraj po stanju\",\n  \"apply\": \"Primeniti\",\n  \"search\": \"Traži\",\n  \"addToCurrent\": \"Dodaj trenutni izbor\",\n  \"clear\": \"Očisti\",\n  \"blanks\": \"(Prazno)\",\n  \"operatorNone\": \"Ništa\",\n  \"and\": \"I\",\n  \"or\": \"ILI\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"Tekst sadrži\",\n      \"doesnotcontain\": \"Tekst ne sadrži\",\n      \"startswith\": \"Tekst počinje sa\",\n      \"endswith\": \"Tekst se završava\"\n    },\n    \"date\": {\n      \"eq\":  \"Datum je\",\n      \"neq\": \"Datum nije\",\n      \"lt\":  \"Datum je pre\",\n      \"gt\":  \"Datum je posle\"\n    },\n    \"number\": {\n      \"eq\": \"Je jednako na\",\n      \"neq\": \"Nije jednako na\",\n      \"gte\": \"Je veći od ili jednak na\",\n      \"gt\": \"Je veći od\",\n      \"lte\": \"Je manje ili jednako na\",\n      \"lt\": \"Is less than\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.colorPicker) {\nkendo.spreadsheet.messages.colorPicker =\n$.extend(true, kendo.spreadsheet.messages.colorPicker,{\n  \"reset\": \"Resetovanje boje\",\n  \"customColor\": \"Prilagođena boja...\",\n  \"apply\": \"Primeniti\",\n  \"cancel\": \"Otkazati\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Dodajte kolonu levo\",\n  \"addColumnRight\": \"Dodajte kolonu desno\",\n  \"addRowAbove\": \"Dodajte kolonu gore\",\n  \"addRowBelow\": \"Dodajte kolonu dole\",\n  \"alignment\": \"Poravnanje\",\n  \"alignmentButtons\": {\n    \"justtifyLeft\": \"Poravnati levo\",\n    \"justifyCenter\": \"Centar\",\n    \"justifyRight\": \"Poravnati desno\",\n    \"justifyFull\": \"Poravnajte tekst do ivica\",\n    \"alignTop\": \"Poravnati gore\",\n    \"alignMiddle\": \"Poravnati sredina\",\n    \"alignBottom\": \"Poravnati dole\"\n  },\n  \"backgroundColor\": \"Pozadina\",\n  \"bold\": \"Bold\",\n  \"borders\": \"Ivica\",\n  \"colorPicker\": {\n    \"reset\": \"Resetovanje boja\",\n    \"customColor\": \"Prilagođena boja...\"\n  },\n  \"copy\": \"Kopiraj\",\n  \"cut\": \"Rez\",\n  \"deleteColumn\": \"Izbriši kolonu\",\n  \"deleteRow\": \"Izbriši red\",\n  \"excelImport\": \"Import od Excel...\",\n  \"filter\": \"Filter\",\n  \"fontFamily\": \"Font\",\n  \"fontSize\": \"Font veličina\",\n  \"format\": \"Prilagođen format...\",\n  \"formatTypes\": {\n    \"automatic\": \"Automatski\",\n    \"number\": \"Broj\",\n    \"percent\": \"Procent\",\n    \"financial\": \"Finansijski\",\n    \"currency\": \"Valuta\",\n    \"date\": \"Datum\",\n    \"time\": \"Vreme\",\n    \"dateTime\": \"Datum vreme\",\n    \"duration\": \"Trajanje\",\n    \"moreFormats\": \"Više formata...\"\n  },\n  \"formatDecreaseDecimal\": \"Smanji decimala\",\n  \"formatIncreaseDecimal\": \"Povećati decimala\",\n  \"freeze\": \"Zamrznuti paneli\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Zamrznuti paneli\",\n    \"freezeRows\": \"Zamrznuti red\",\n    \"freezeColumns\": \"Zamrzne kolone\",\n    \"unfreeze\": \"Odzračite panele\"\n  },\n  \"italic\": \"Italic\",\n  \"merge\": \"Spajati ćelije\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Spoji svi\",\n    \"mergeHorizontally\": \"Spoji horizontalno\",\n    \"mergeVertically\": \"Spoji verticalno\",\n    \"unmerge\": \"Razdoji\"\n  },\n  \"open\": \"Otvori...\",\n  \"paste\": \"Umetni\",\n  \"quickAccess\": {\n    \"redo\": \"Ponovo\",\n    \"undo\": \"Poništiti\"\n  },\n  \"saveAs\": \"Save As...\",\n  \"sortAsc\": \"Sortiraj rastuće\",\n  \"sortDesc\": \"Sortiraj silazno\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Sortiraj listu A do Z\",\n    \"sortSheetDesc\": \"Sortiraj listu Z do A\",\n    \"sortRangeAsc\": \"Sortirajte opseg A do Z\",\n    \"sortRangeDesc\": \"Sortirajte opseg Z do A\"\n  },\n  \"textColor\": \"Boja teksta\",\n  \"textWrap\": \"Prelomiti tekst\",\n  \"underline\": \"Podvući\",\n  \"validation\": \"Validacija podataka...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Ne mogu uložiti ćelije zbog mogućnosti gubitka podataka. Izaberite drugu lokaciju za ubacivanje ili izbrišite podatke sa kraja radnog lista.\",\n    \"filterRangeContainingMerges\": \"Ne mogu napraviti filter unutar opsega koji sadrži spoj\",\n    \"validationError\": \"Vrednost koju unesete krši pravila validacije postavljena na ćeliji.\"\n  },\n  \"tabs\": {\n    \"home\": \"Doma\",\n    \"insert\": \"Ubaci\",\n    \"data\": \"Podatci\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Povećajte\",\n  \"decreaseButtonTitle\": \"Smanjite\"\n});\n}\n\n/* ListBox messaages */\n\nif (kendo.ui.ListBox) {\nkendo.ui.ListBox.prototype.options.messages =\n$.extend(true, kendo.ui.ListBox.prototype.options.messages,{\n  \"tools\": {\n    \"remove\": \"Izbriši\",\n    \"moveUp\": \"Pomeri Gore\",\n    \"moveDown\": \"Pomeri Dole\",\n    \"transferTo\": \"Prenos Do\",\n    \"transferFrom\": \"Prenos Od\",\n    \"transferAllTo\": \"Prenosi Sve Do\",\n    \"transferAllFrom\": \"Prenosi Sve Od\"\n  }\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n  \"noRows\": \"Nema podataka za prikaz\",\n  \"loading\": \"Učitavanje...\",\n  \"requestFailed\": \"Zahtev nije uspeo.\",\n  \"retry\": \"Pokušaj ponovo\",\n  \"commands\": {\n      \"edit\": \"Izmeni\",\n      \"update\": \"Ažuriraj\",\n      \"canceledit\": \"Otkazati\",\n      \"create\": \"Dodajte novi zapis\",\n      \"createchild\": \"Dodadite detalj zapis\",\n      \"destroy\": \"Izbriši\",\n      \"excel\": \"Export u Excel\",\n      \"pdf\": \"Export u PDF\"\n  }\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Učitavanje...\",\n  \"requestFailed\": \"Zahtev nije uspeo.\",\n  \"retry\": \"Ponovo\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Izaberite datoteke...\",\n  \"cancel\": \"Otkazati\",\n  \"retry\": \"Ponovo\",\n  \"remove\": \"Ukloni\",\n  \"clearSelectedFiles\": \"Očisti\",\n  \"uploadSelectedFiles\": \"Otpremiti datoteka\",\n  \"dropFilesHere\": \"Ispustite datoteke ovde da biste ih otpremili\",\n  \"statusUploading\": \"otpremiti\",\n  \"statusUploaded\": \"zavrsio otpremiti\",\n  \"statusWarning\": \"upozorenje\",\n  \"statusFailed\": \"nije uspeo\",\n  \"headerStatusUploading\": \"otpremiti...\",\n  \"headerStatusUploaded\": \"Gotovo\",\n  \"invalidMaxFileSize\": \"Veličina datoteke je prevelika.\",\n  \"invalidMinFileSize\": \"Veličina datoteke je premala.\",\n  \"invalidFileExtension\": \"Tip datoteke nije dozvoljen.\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} je potrebno\",\n  \"pattern\": \"{0} nije važeća\",\n  \"min\": \"{0} treba da bude veća ili jednaka {1}\",\n  \"max\": \"{0} treba da bude manji ili jednak {1}\",\n  \"step\": \"{0} nije važeća\",\n  \"email\": \"{0} nije važeći e-mail\",\n  \"url\": \"{0} nije važeći URL URL\",\n  \"date\": \"{0} nije važeći datum\",\n  \"dateCompare\": \"Krajnji datum mora biti veći ili jednak početnom datumu\"\n});\n}\n\n/* kendo.ui.progress method */\nif (kendo.ui.progress) {\nkendo.ui.progress.messages =\n$.extend(true, kendo.ui.progress.messages, {\n    loading: \"Učitavanje...\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Otkazati\"\n});\n}\n\n/* Calendar */\nif (kendo.ui.Calendar) {\nkendo.ui.Calendar.prototype.options.messages =\n$.extend(true, kendo.ui.Calendar.prototype.options.messages, {\n  \"weekColumnHeader\": \"\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Otkazati\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Otkazati\"\n});\n}\n\n/* DateInput */\nif (kendo.ui.DateInput) {\n  kendo.ui.DateInput.prototype.options.messages =\n    $.extend(true, kendo.ui.DateInput.prototype.options.messages, {\n      \"year\": \"godina\",\n      \"month\": \"mesec\",\n      \"day\": \"den\",\n      \"weekday\": \"dan u nedelji\",\n      \"hour\": \"sati\",\n      \"minute\": \"minuta\",\n      \"second\": \"sekunde\",\n      \"dayperiod\": \"AM/PM\"\n    });\n}\n\n})(window.kendo.jQuery);}));"]}