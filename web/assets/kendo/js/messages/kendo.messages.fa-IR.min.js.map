{"version": 3, "sources": ["messages/kendo.messages.fa-IR.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "TreeView", "Upload", "localization", "remove", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "dateCompare", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGNC,MAAMC,GAAGC,kBACTF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACtDE,MAAS,QACTC,OAAU,YAMdR,MAAMC,GAAGQ,cACTT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UAClDE,MAAS,QACTC,OAAU,YAMdR,MAAMC,GAAGS,aACTV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACjDM,cAAiB,kBACjBC,eAAkB,kBAClBC,OAAU,QACVC,QAAW,UACXC,KAAQ,OACRC,SAAY,kBACZC,KAAQ,OACRC,OAAU,cAMdlB,MAAMC,GAAGkB,SACTnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC7Ce,KAAQ,OACRC,OAAU,SACVC,UAAa,YACbC,cAAiB,gBACjBC,YAAe,cACfC,UAAa,YACbC,cAAiB,cACjBC,YAAe,kBACfC,aAAgB,mBAChBC,YAAe,UACfC,oBAAuB,wBACvBC,kBAAqB,sBACrBC,OAAU,SACVC,QAAW,UACXC,WAAc,mBACdC,OAAU,mBACVC,YAAe,eACfC,WAAc,cACdC,WAAc,gBACdC,SAAY,YACZC,SAAY,qBACZC,gBAAmB,mBACnBC,SAAY,mBACZC,gBAAmB,mBACnBC,YAAe,SACfC,WAAc,SACdC,UAAa,QACbC,UAAa,mBACbC,MAAS,SACTC,YAAe,eACfC,WAAc,SACdC,QAAW,cACXC,YAAe,OACfC,YAAe,OACfC,gBAAmB,sEACnBC,WAAc,yCACdC,cAAiB,+FACjBC,kBAAqB,4CACrBC,gBAAmB,cACnBC,aAAgB,iBAChBC,WAAc,aACdC,YAAe,cACfC,eAAkB,cAClBC,UAAa,QACbC,eAAkB,cAClBC,SAAY,OACZC,YAAe,UACfC,oBAAuB,0BACvBC,aAAgB,SAChBC,aAAgB,SAChBC,sBAAyB,KACzBC,aAAgB,SAChBC,YAAe,eACfC,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,gBACfC,YAAe,gBACfC,UAAa,aACbC,aAAgB,mBAMpB9E,MAAMC,GAAG8E,cACT/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UAClD6C,WAAc,WACdC,QAAW,oBACXE,YAAe,MACfD,YAAe,SACfK,kBAAqB,0BACrBR,YAAe,aACfM,WAAc,kCACdD,gBAAmB,wEACnBE,cAAiB,mFACjBwB,cAAiB,yBACjBC,OAAU,WAMdjF,MAAMC,GAAGiF,aACTlF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACjD8E,OAAU,YACVC,QAAW,aACXvE,OAAU,QACVwE,MAAS,WACTC,SAAY,WAMhBtF,MAAMC,GAAGiF,aACTlF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,WACjDC,QACIC,GAAM,gBACNC,IAAO,iBACPC,WAAc,cACdC,SAAY,YACZC,eAAkB,aAClBC,SAAY,iBAEhBC,QACIN,GAAM,gBACNC,IAAO,iBACPM,IAAO,0BACPC,GAAM,iBACNC,IAAO,0BACPC,GAAM,gBAEVC,MACIX,GAAM,gBACNC,IAAO,iBACPM,IAAO,4BACPC,GAAM,SACNC,IAAO,4BACPC,GAAM,UAEVE,OACIZ,GAAM,gBACNC,IAAO,qBAOf1F,MAAMC,GAAGqG,aACTtG,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQC,UACjDkG,KAAQ,2BACRC,MAAS,2BACTrB,OAAU,YACVC,QAAW,aACXvE,OAAU,QACVwE,MAAS,WACToB,IAAO,IACPC,GAAM,KACNC,YAAe,iBACfrB,SAAY,QACZsB,MAAS,QACTpG,OAAU,YAMdR,MAAMC,GAAGqG,aACTtG,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQmF,WACjDC,QACIC,GAAM,gBACNC,IAAO,iBACPC,WAAc,cACdC,SAAY,YACZC,eAAkB,aAClBC,SAAY,iBAEhBC,QACIN,GAAM,gBACNC,IAAO,iBACPM,IAAO,0BACPC,GAAM,iBACNC,IAAO,0BACPC,GAAM,gBAEVC,MACIX,GAAM,gBACNC,IAAO,iBACPM,IAAO,4BACPC,GAAM,SACNC,IAAO,4BACPC,GAAM,UAEVE,OACIZ,GAAM,gBACNC,IAAO,qBAOf1F,MAAMC,GAAG4G,mBACT7G,MAAMC,GAAG4G,iBAAiB1G,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4G,iBAAiB1G,UAAUC,QAAQC,UACvDyG,SAAY,aACZzB,MAAS,WACTxE,OAAU,WAMdb,MAAMC,GAAG8G,QACT/G,MAAMC,GAAG8G,MAAM5G,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8G,MAAM5G,UAAUC,QAAQC,UAC5C2G,SACIC,SAAY,mBACZC,OAAU,iBACVC,YAAe,eACfC,aAAgB,iBAChBC,IAAO,mBAEX7G,OAAU,SACV8G,4BAA+B,YAC/BC,sBAAyB,UACzBC,QAAW,MACXC,QACIC,aAAgB,aAChBC,YAAe,MACfC,IAAO,QACPC,gBAAmB,SACnBC,UAAa,QACbC,qBAAwB,QACxBC,gBAAmB,QACnBC,MAAS,OACTzB,MAAS,QACT0B,YAAe,UAEnBC,KAAQ,QACRC,OACIC,IAAO,MACPT,IAAO,QACPU,MAAS,MACTL,MAAS,OACTM,KAAQ,OACRC,KAAQ,UAOhBxI,MAAMC,GAAGwI,OACTzI,MAAMC,GAAGwI,KAAKtI,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwI,KAAKtI,UAAUC,QAAQC,UAC3CqI,UACIlI,OAAU,SACVmI,WAAc,SACdC,OAAU,uBACVpB,QAAW,MACXqB,KAAQ,SACRC,MAAS,cACTzB,IAAO,YACPc,KAAQ,QACRY,OAAU,SACVC,OAAU,SAEdC,UACIC,aAAgB,SAChBC,aAAgB,+BAChBC,cAAiB,OAErBC,UAAa,yBAMjBrJ,MAAMC,GAAGqJ,YACTtJ,MAAMC,GAAGqJ,UAAUnJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqJ,UAAUnJ,UAAUC,QAAQC,UAChDkJ,MAAS,kEAMbvJ,MAAMC,GAAGuJ,iBACTxJ,MAAMC,GAAGuJ,eAAerJ,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuJ,eAAerJ,UAAUC,SAC7CqJ,YAAe,aACfC,cAAiB,aAMrB1J,MAAMC,GAAG0J,QACT3J,MAAMC,GAAG0J,MAAMxJ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0J,MAAMxJ,UAAUC,QAAQC,UAC5CuJ,SAAY,MACZC,QAAW,8BACXN,MAAS,8BACTO,KAAQ,OACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,kBACTC,SAAY,kBACZC,KAAQ,kBACRC,KAAQ,kBACRC,QAAW,gBACXC,UAAa,iBAMjBtK,MAAMC,GAAGsK,gBACTvK,MAAMC,GAAGsK,cAAcpK,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsK,cAAcpK,UAAUC,QAAQC,UACpDuJ,SAAY,MACZC,QAAW,8BACXN,MAAS,8BACTO,KAAQ,OACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,kBACTC,SAAY,kBACZC,KAAQ,kBACRC,KAAQ,kBACRC,QAAW,gBACXC,UAAa,iBAMjBtK,MAAMC,GAAGuK,YACTxK,MAAMC,GAAGuK,UAAUrK,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuK,UAAUrK,UAAUC,QAAQC,UAChDoK,cAAiB,wBACjBC,aAAgB,0BAChBC,UAAa,2BAMjB3K,MAAMC,GAAG2K,iBACT5K,MAAMC,GAAG2K,eAAezK,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2K,eAAezK,UAAUC,QAAQC,UACrDkG,KAAQ,8BACRsE,aAAgB,gBAChBhK,OAAU,SACViK,QAAW,oBACXtE,MAAS,oBACTnB,MAAS,QACT0F,GAAM,KACNvK,OAAU,SACV+E,WACIK,SAAY,WACZC,eAAkB,mBAClBF,WAAc,cACdG,SAAY,YACZL,GAAM,cACNC,IAAO,sBAOf1F,MAAMC,GAAG+K,mBACThL,MAAMC,GAAG+K,iBAAiB7K,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+K,iBAAiB7K,UAAUC,QAAQC,UACvD4K,aACIC,MAAS,UACTC,OAAU,QACVC,MAAS,SACTC,OAAU,QACVC,QAAW,SACXC,OAAU,WAEdJ,QACIK,YAAe,gBACfC,SAAY,SAEhBL,OACII,YAAe,gBACfC,SAAY,QAEhBJ,QACII,SAAY,QACZD,YAAe,gBACfE,SAAY,iBAEhBJ,SACIE,YAAe,gBACfE,SAAY,gBACZD,SAAY,OACZpD,IAAO,QAEXkD,QACIC,YAAe,gBACfE,SAAY,gBACZD,SAAY,OACZ1B,GAAM,QAEVnC,KACI+D,MAAS,SACTC,YAAe,QACfV,MAAS,UACTW,MAAS,UACTC,WAAc,cACdC,GAAM,OAEVC,iBACI/B,MAAS,MACTgC,OAAU,MACVC,MAAS,MACTC,OAAU,QACV/B,KAAQ,OAEZgC,UACI/D,IAAO,MACPgE,QAAW,WACXC,QAAW,iBAOnBtM,MAAMC,GAAGsM,YACTvM,MAAMC,GAAGsM,UAAUpM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsM,UAAUpM,UAAUC,QAAQC,UAChDmM,OAAU,UACVpG,KAAQ,OACRqG,MAAS,QACTC,KAAQ,OACRC,YAAe,gBACfC,YAAe,sBACfC,MAAS,QACT1E,KAAQ,OACR3H,OAAU,SACVgH,QAAW,SACXsF,kBAAqB,eACrBC,cAAiB,+BACjBC,eAAkB,wBAClB/D,UACIE,aAAgB,+CAEpBf,OACIC,IAAO,MACPE,KAAQ,OACR0E,SAAY,YACZC,OAAU,SACV5E,MAAS,SAEb6E,oBACIL,kBAAqB,wBACrBM,uBAA0B,4BAC1BC,mBAAsB,oBACtBC,gBAAmB,sBACnBC,qBAAwB,0BACxBC,iBAAoB,kBACpBC,gBAAmB,wEACnBC,cAAiB,uEAErBjG,QACIjB,MAAS,QACTyB,MAAS,QACTL,IAAO,MACP+F,YAAe,gBACfC,YAAe,cACfC,OAAU,SACVC,SAAY,IACZC,cAAiB,iBACjBC,YAAe,eACfC,kBAAqB,wCACrBC,oBAAuB,YACvBC,qBAAwB,YACxBC,cAAiB,aACjBC,WAAc,cACd1G,YAAe,YAOvB3H,MAAMC,GAAGqO,SACTtO,MAAMC,GAAGqO,OAAOnO,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqO,OAAOnO,UAAUC,SACrCmO,oBAAuB,SACvBC,oBAAuB,UAM3BxO,MAAMC,GAAGwO,WACTzO,MAAMC,GAAGwO,SAAStO,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwO,SAAStO,UAAUC,QAAQC,UAC/CqO,OAAU,8BACVC,QAAW,qBACXC,cAAiB,yBACjBC,MAAS,YACTnG,UACIG,KAAQ,SACRG,OAAU,QACVL,WAAc,SACdC,OAAU,gBACVkG,YAAe,eACftH,QAAW,MACXsB,MAAS,cACTzB,IAAO,gBAOfrH,MAAMC,GAAG8O,WACT/O,MAAMC,GAAG8O,SAAS5O,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8O,SAAS5O,UAAUC,QAAQC,UAC/CsO,QAAW,qBACXC,cAAiB,yBACjBC,MAAS,eAMb7O,MAAMC,GAAG+O,SACThP,MAAMC,GAAG+O,OAAO7O,UAAUC,QAAQ6O,aAClCnP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+O,OAAO7O,UAAUC,QAAQ6O,cAC7ClG,OAAU,qBACVvI,OAAU,SACVqO,MAAS,YACTK,OAAU,MACVC,oBAAuB,oBACvBnK,cAAiB,2CACjBoK,gBAAmB,kBACnBC,eAAkB,iBAClBC,cAAiB,QACjBC,aAAgB,kBAChBC,sBAAyB,qBACzBC,qBAAwB,oBAM5BzP,MAAMC,GAAGyP,YACT1P,MAAMC,GAAGyP,UAAUvP,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyP,UAAUvP,UAAUC,QAAQC,UAChDsP,SAAY,iBACZC,QAAW,iBACXC,IAAO,uCACPC,IAAO,uCACPC,KAAQ,iBACRC,MAAS,0BACTC,IAAO,8BACP7J,KAAQ,uBACR8J,YAAe,uDAGxBC,OAAOnQ,MAAMoQ", "file": "kendo.messages.fa-IR.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n    /* FlatColorPicker messages */\n\n    if (kendo.ui.FlatColorPicker) {\n        kendo.ui.FlatColorPicker.prototype.options.messages =\n        $.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages, {\n            \"apply\": \"تایید\",\n            \"cancel\": \"انصراف\"\n        });\n    }\n\n    /* ColorPicker messages */\n\n    if (kendo.ui.ColorPicker) {\n        kendo.ui.ColorPicker.prototype.options.messages =\n        $.extend(true, kendo.ui.ColorPicker.prototype.options.messages, {\n            \"apply\": \"تایید\",\n            \"cancel\": \"انصراف\"\n        });\n    }\n\n    /* ColumnMenu messages */\n\n    if (kendo.ui.ColumnMenu) {\n        kendo.ui.ColumnMenu.prototype.options.messages =\n        $.extend(true, kendo.ui.ColumnMenu.prototype.options.messages, {\n            \"sortAscending\": \"مرتب سازی صعودی\",\n            \"sortDescending\": \"مرتب سازی نزولی\",\n            \"filter\": \"فیلتر\",\n            \"columns\": \"ستون ها\",\n            \"done\": \"تمام\",\n            \"settings\": \"تنظیمات ستون ها\",\n            \"lock\": \"بستن\",\n            \"unlock\": \"باز کردن\"\n        });\n    }\n\n    /* Editor messages */\n\n    if (kendo.ui.Editor) {\n        kendo.ui.Editor.prototype.options.messages =\n        $.extend(true, kendo.ui.Editor.prototype.options.messages, {\n            \"bold\": \"Bold\",\n            \"italic\": \"Italic\",\n            \"underline\": \"Underline\",\n            \"strikethrough\": \"Strikethrough\",\n            \"superscript\": \"Superscript\",\n            \"subscript\": \"Subscript\",\n            \"justifyCenter\": \"Center text\",\n            \"justifyLeft\": \"Align text left\",\n            \"justifyRight\": \"Align text right\",\n            \"justifyFull\": \"Justify\",\n            \"insertUnorderedList\": \"Insert unordered list\",\n            \"insertOrderedList\": \"Insert ordered list\",\n            \"indent\": \"Indent\",\n            \"outdent\": \"Outdent\",\n            \"createLink\": \"Insert hyperlink\",\n            \"unlink\": \"Remove hyperlink\",\n            \"insertImage\": \"Insert image\",\n            \"insertFile\": \"Insert file\",\n            \"insertHtml\": \"درج متن آماده\",\n            \"viewHtml\": \"View HTML\",\n            \"fontName\": \"Select font family\",\n            \"fontNameInherit\": \"(inherited font)\",\n            \"fontSize\": \"Select font size\",\n            \"fontSizeInherit\": \"(inherited size)\",\n            \"formatBlock\": \"Format\",\n            \"formatting\": \"Format\",\n            \"foreColor\": \"Color\",\n            \"backColor\": \"Background color\",\n            \"style\": \"Styles\",\n            \"emptyFolder\": \"Empty Folder\",\n            \"uploadFile\": \"Upload\",\n            \"orderBy\": \"Arrange by:\",\n            \"orderBySize\": \"Size\",\n            \"orderByName\": \"Name\",\n            \"invalidFileType\": \"The selected file \\\"{0}\\\" is not valid. Supported file types are {1}.\",\n            \"deleteFile\": 'Are you sure you want to delete \"{0}\"?',\n            \"overwriteFile\": 'A file with name \"{0}\" already exists in the current directory. Do you want to overwrite it?',\n            \"directoryNotFound\": \"A directory with this name was not found.\",\n            \"imageWebAddress\": \"Web address\",\n            \"imageAltText\": \"Alternate text\",\n            \"imageWidth\": \"Width (px)\",\n            \"imageHeight\": \"Height (px)\",\n            \"fileWebAddress\": \"Web address\",\n            \"fileTitle\": \"Title\",\n            \"linkWebAddress\": \"Web address\",\n            \"linkText\": \"Text\",\n            \"linkToolTip\": \"ToolTip\",\n            \"linkOpenInNewWindow\": \"Open link in new window\",\n            \"dialogUpdate\": \"Update\",\n            \"dialogInsert\": \"Insert\",\n            \"dialogButtonSeparator\": \"or\",\n            \"dialogCancel\": \"Cancel\",\n            \"createTable\": \"Create table\",\n            \"addColumnLeft\": \"Add column on the left\",\n            \"addColumnRight\": \"Add column on the right\",\n            \"addRowAbove\": \"Add row above\",\n            \"addRowBelow\": \"Add row below\",\n            \"deleteRow\": \"Delete row\",\n            \"deleteColumn\": \"Delete column\"\n        });\n    }\n\n    /* FileBrowser messages */\n\n    if (kendo.ui.FileBrowser) {\n        kendo.ui.FileBrowser.prototype.options.messages =\n        $.extend(true, kendo.ui.FileBrowser.prototype.options.messages, {\n            \"uploadFile\": \"بارگزاری\",\n            \"orderBy\": \"مرتب سازی بر اساس\",\n            \"orderByName\": \"نام\",\n            \"orderBySize\": \"اندازه\",\n            \"directoryNotFound\": \"فولدر مورد نظر پیدا نشد\",\n            \"emptyFolder\": \"فولدر خالی\",\n            \"deleteFile\": 'آیا از حذف \"{0}\" اطمینان دارید؟',\n            \"invalidFileType\": \"انتخاب فایل با پسوند \\\"{0}\\\" امکانپذیر نیست. پسوندهای پشیتبانی شده: {1}\",\n            \"overwriteFile\": \"فایل با نام \\\"{0}\\\" در فولدر انتخابی وجود دارد. آیا می خواهید آن را بازنویسی کنید؟\",\n            \"dropFilesHere\": \"فایل را به اینجا بکشید\",\n            \"search\": \"جستجو\"\n        });\n    }\n\n    /* FilterCell messages */\n\n    if (kendo.ui.FilterCell) {\n        kendo.ui.FilterCell.prototype.options.messages =\n        $.extend(true, kendo.ui.FilterCell.prototype.options.messages, {\n            \"isTrue\": \"درست باشد\",\n            \"isFalse\": \"درست نباشد\",\n            \"filter\": \"فیلتر\",\n            \"clear\": \"پاک کردن\",\n            \"operator\": \"عملگر\"\n        });\n    }\n\n    /* FilterCell operators */\n\n    if (kendo.ui.FilterCell) {\n        kendo.ui.FilterCell.prototype.options.operators =\n        $.extend(true, kendo.ui.FilterCell.prototype.options.operators, {\n            \"string\": {\n                \"eq\": \"برابر باشد با\",\n                \"neq\": \"برابر نباشد با\",\n                \"startswith\": \"شروع شود با\",\n                \"contains\": \"شامل باشد\",\n                \"doesnotcontain\": \"شامل نباشد\",\n                \"endswith\": \"پایان یابد با\"\n            },\n            \"number\": {\n                \"eq\": \"برابر باشد با\",\n                \"neq\": \"برابر نباشد با\",\n                \"gte\": \"برابر یا بزرگتر باشد از\",\n                \"gt\": \"بزرگتر باشد از\",\n                \"lte\": \"کمتر و یا برابر باشد با\",\n                \"lt\": \"کمتر باشد از\"\n            },\n            \"date\": {\n                \"eq\": \"برابر باشد با\",\n                \"neq\": \"برابر نباشد با\",\n                \"gte\": \"بعد از یا هم زمان باشد با\",\n                \"gt\": \"بعد از\",\n                \"lte\": \"قبل از یا هم زمان باشد با\",\n                \"lt\": \"قبل از\"\n            },\n            \"enums\": {\n                \"eq\": \"برابر باشد با\",\n                \"neq\": \"برابر نباشد با\"\n            }\n        });\n    }\n\n    /* FilterMenu messages */\n\n    if (kendo.ui.FilterMenu) {\n        kendo.ui.FilterMenu.prototype.options.messages =\n        $.extend(true, kendo.ui.FilterMenu.prototype.options.messages, {\n            \"info\": \"ردیف های را نشان بده که:\",\n            \"title\": \"ردیف های را نشان بده که:\",\n            \"isTrue\": \"درست باشد\",\n            \"isFalse\": \"درست نباشد\",\n            \"filter\": \"فیلتر\",\n            \"clear\": \"پاک کردن\",\n            \"and\": \"و\",\n            \"or\": \"یا\",\n            \"selectValue\": \"-انتخاب مقدار-\",\n            \"operator\": \"عملگر\",\n            \"value\": \"مقدار\",\n            \"cancel\": \"انصراف\"\n        });\n    }\n\n    /* FilterMenu operator messages */\n\n    if (kendo.ui.FilterMenu) {\n        kendo.ui.FilterMenu.prototype.options.operators =\n        $.extend(true, kendo.ui.FilterMenu.prototype.options.operators, {\n            \"string\": {\n                \"eq\": \"برابر باشد با\",\n                \"neq\": \"برابر نباشد با\",\n                \"startswith\": \"شروع شود با\",\n                \"contains\": \"شامل باشد\",\n                \"doesnotcontain\": \"شامل نباشد\",\n                \"endswith\": \"پایان یابد با\"\n            },\n            \"number\": {\n                \"eq\": \"برابر باشد با\",\n                \"neq\": \"برابر نباشد با\",\n                \"gte\": \"برابر یا بزرگتر باشد از\",\n                \"gt\": \"بزرگتر باشد از\",\n                \"lte\": \"کمتر و یا برابر باشد با\",\n                \"lt\": \"کمتر باشد از\"\n            },\n            \"date\": {\n                \"eq\": \"برابر باشد با\",\n                \"neq\": \"برابر نباشد با\",\n                \"gte\": \"بعد از یا هم زمان باشد با\",\n                \"gt\": \"بعد از\",\n                \"lte\": \"قبل از یا هم زمان باشد با\",\n                \"lt\": \"قبل از\"\n            },\n            \"enums\": {\n                \"eq\": \"برابر باشد با\",\n                \"neq\": \"برابر نباشد با\"\n            }\n        });\n    }\n\n    /* FilterMultiCheck messages */\n\n    if (kendo.ui.FilterMultiCheck) {\n        kendo.ui.FilterMultiCheck.prototype.options.messages =\n        $.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages, {\n            \"checkAll\": \"انتخاب همه\",\n            \"clear\": \"پاک کردن\",\n            \"filter\": \"فیلتر\"\n        });\n    }\n\n    /* Gantt messages */\n\n    if (kendo.ui.Gantt) {\n        kendo.ui.Gantt.prototype.options.messages =\n        $.extend(true, kendo.ui.Gantt.prototype.options.messages, {\n            \"actions\": {\n                \"addChild\": \"اضافه کردن فرزند\",\n                \"append\": \"اضافه کردن کار\",\n                \"insertAfter\": \"اضافه کن زیر\",\n                \"insertBefore\": \"اضافه کن بالای\",\n                \"pdf\": \"گرفتن خروجی PDF\"\n            },\n            \"cancel\": \"انصراف\",\n            \"deleteDependencyWindowTitle\": \"حذف رابطه\",\n            \"deleteTaskWindowTitle\": \"حذف کار\",\n            \"destroy\": \"حذف\",\n            \"editor\": {\n                \"assingButton\": \"ارجاع دادن\",\n                \"editorTitle\": \"کار\",\n                \"end\": \"پایان\",\n                \"percentComplete\": \"پیشرفت\",\n                \"resources\": \"منابع\",\n                \"resourcesEditorTitle\": \"منابع\",\n                \"resourcesHeader\": \"منابع\",\n                \"start\": \"شروع\",\n                \"title\": \"عنوان\",\n                \"unitsHeader\": \"واحدها\"\n            },\n            \"save\": \"ذخیره\",\n            \"views\": {\n                \"day\": \"روز\",\n                \"end\": \"پایان\",\n                \"month\": \"ماه\",\n                \"start\": \"شروع\",\n                \"week\": \"هفته\",\n                \"year\": \"سال\"\n            }\n        });\n    }\n\n    /* Grid messages */\n\n    if (kendo.ui.Grid) {\n        kendo.ui.Grid.prototype.options.messages =\n        $.extend(true, kendo.ui.Grid.prototype.options.messages, {\n            \"commands\": {\n                \"cancel\": \"انصراف\",\n                \"canceledit\": \"انصراف\",\n                \"create\": \"اضافه کردن ردیف جدید\",\n                \"destroy\": \"حذف\",\n                \"edit\": \"ویرایش\",\n                \"excel\": \"خروجی Excel\",\n                \"pdf\": \"خروجی PDF\",\n                \"save\": \"ذخیره\",\n                \"select\": \"انتخاب\",\n                \"update\": \"ذخیره\"\n            },\n            \"editable\": {\n                \"cancelDelete\": \"انصراف\",\n                \"confirmation\": \"آیا از حذف این ردیف مطمئنید؟\",\n                \"confirmDelete\": \"حذف\"\n            },\n            \"noRecords\": \"اطلاعاتی وجود ندارد\"\n        });\n    }\n\n    /* Groupable messages */\n\n    if (kendo.ui.Groupable) {\n        kendo.ui.Groupable.prototype.options.messages =\n        $.extend(true, kendo.ui.Groupable.prototype.options.messages, {\n            \"empty\": \"برای گروه بندی بر اساس یک ستون، عنوان ستون را به اینجا بکشید\"\n        });\n    }\n\n    /* NumericTextBox messages */\n\n    if (kendo.ui.NumericTextBox) {\n        kendo.ui.NumericTextBox.prototype.options =\n        $.extend(true, kendo.ui.NumericTextBox.prototype.options, {\n            \"upArrowText\": \"اضافه کردن\",\n            \"downArrowText\": \"کم کردن\"\n        });\n    }\n\n    /* Pager messages */\n\n    if (kendo.ui.Pager) {\n        kendo.ui.Pager.prototype.options.messages =\n        $.extend(true, kendo.ui.Pager.prototype.options.messages, {\n            \"allPages\": \"همه\",\n            \"display\": \"ردیف {0} تا {1} از {2} ردیف\",\n            \"empty\": \"ردیفی برای نمایش وجود ندارد\",\n            \"page\": \"صفحه\",\n            \"of\": \"از {0}\",\n            \"itemsPerPage\": \"ردیف های هر صفحه\",\n            \"first\": \"برو به صفحه اول\",\n            \"previous\": \"برو به صفحه قبل\",\n            \"next\": \"برو به صفحه بعد\",\n            \"last\": \"برو به صفحه آخر\",\n            \"refresh\": \"بارگزاری مجدد\",\n            \"morePages\": \"صفحات بیشتر\"\n        });\n    }\n\n    /* TreeListPager messages */\n\n    if (kendo.ui.TreeListPager) {\n        kendo.ui.TreeListPager.prototype.options.messages =\n        $.extend(true, kendo.ui.TreeListPager.prototype.options.messages, {\n            \"allPages\": \"همه\",\n            \"display\": \"ردیف {0} تا {1} از {2} ردیف\",\n            \"empty\": \"ردیفی برای نمایش وجود ندارد\",\n            \"page\": \"صفحه\",\n            \"of\": \"از {0}\",\n            \"itemsPerPage\": \"ردیف های هر صفحه\",\n            \"first\": \"برو به صفحه اول\",\n            \"previous\": \"برو به صفحه قبل\",\n            \"next\": \"برو به صفحه بعد\",\n            \"last\": \"برو به صفحه آخر\",\n            \"refresh\": \"بارگزاری مجدد\",\n            \"morePages\": \"صفحات بیشتر\"\n        });\n    }\n\n    /* PivotGrid messages */\n\n    if (kendo.ui.PivotGrid) {\n        kendo.ui.PivotGrid.prototype.options.messages =\n        $.extend(true, kendo.ui.PivotGrid.prototype.options.messages, {\n            \"measureFields\": \"Drop Data Fields Here\",\n            \"columnFields\": \"Drop Column Fields Here\",\n            \"rowFields\": \"Drop Rows Fields Here\"\n        });\n    }\n\n    /* PivotFieldMenu messages */\n\n    if (kendo.ui.PivotFieldMenu) {\n        kendo.ui.PivotFieldMenu.prototype.options.messages =\n        $.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages, {\n            \"info\": \"Show items with value that:\",\n            \"filterFields\": \"Fields Filter\",\n            \"filter\": \"Filter\",\n            \"include\": \"Include Fields...\",\n            \"title\": \"Fields to include\",\n            \"clear\": \"Clear\",\n            \"ok\": \"Ok\",\n            \"cancel\": \"Cancel\",\n            \"operators\": {\n                \"contains\": \"Contains\",\n                \"doesnotcontain\": \"Does not contain\",\n                \"startswith\": \"Starts with\",\n                \"endswith\": \"Ends with\",\n                \"eq\": \"Is equal to\",\n                \"neq\": \"Is not equal to\"\n            }\n        });\n    }\n\n    /* RecurrenceEditor messages */\n\n    if (kendo.ui.RecurrenceEditor) {\n        kendo.ui.RecurrenceEditor.prototype.options.messages =\n        $.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages, {\n            \"frequencies\": {\n                \"never\": \"هیچ وقت\",\n                \"hourly\": \"ساعتی\",\n                \"daily\": \"روزانه\",\n                \"weekly\": \"هفتگی\",\n                \"monthly\": \"ماهانه\",\n                \"yearly\": \"سالیانه\"\n            },\n            \"hourly\": {\n                \"repeatEvery\": \"تکرار کن هر: \",\n                \"interval\": \" ساعت\"\n            },\n            \"daily\": {\n                \"repeatEvery\": \"تکرار کن هر: \",\n                \"interval\": \" روز\"\n            },\n            \"weekly\": {\n                \"interval\": \" هفته\",\n                \"repeatEvery\": \"تکرار کن هر: \",\n                \"repeatOn\": \"تکرار کن در: \"\n            },\n            \"monthly\": {\n                \"repeatEvery\": \"تکرار کن هر: \",\n                \"repeatOn\": \"تکرار کن در: \",\n                \"interval\": \" ماه\",\n                \"day\": \"روز \"\n            },\n            \"yearly\": {\n                \"repeatEvery\": \"تکرار کن هر: \",\n                \"repeatOn\": \"تکرار کن در: \",\n                \"interval\": \" سال\",\n                \"of\": \" از \"\n            },\n            \"end\": {\n                \"label\": \"پایان:\",\n                \"mobileLabel\": \"پایان\",\n                \"never\": \"هیچ وقت\",\n                \"after\": \"بعد از \",\n                \"occurrence\": \" دفعات وقوع\",\n                \"on\": \"در \"\n            },\n            \"offsetPositions\": {\n                \"first\": \"اول\",\n                \"second\": \"دوم\",\n                \"third\": \"سوم\",\n                \"fourth\": \"چهارم\",\n                \"last\": \"آخر\"\n            },\n            \"weekdays\": {\n                \"day\": \"روز\",\n                \"weekday\": \"روز هفته\",\n                \"weekend\": \"پایان هفته\"\n            }\n        });\n    }\n\n    /* Scheduler messages */\n\n    if (kendo.ui.Scheduler) {\n        kendo.ui.Scheduler.prototype.options.messages =\n        $.extend(true, kendo.ui.Scheduler.prototype.options.messages, {\n            \"allDay\": \"all day\",\n            \"date\": \"Date\",\n            \"event\": \"Event\",\n            \"time\": \"Time\",\n            \"showFullDay\": \"Show full day\",\n            \"showWorkDay\": \"Show business hours\",\n            \"today\": \"Today\",\n            \"save\": \"Save\",\n            \"cancel\": \"Cancel\",\n            \"destroy\": \"Delete\",\n            \"deleteWindowTitle\": \"Delete event\",\n            \"ariaSlotLabel\": \"Selected from {0:t} to {1:t}\",\n            \"ariaEventLabel\": \"{0} on {1:D} at {2:t}\",\n            \"editable\": {\n                \"confirmation\": \"Are you sure you want to delete this event?\"\n            },\n            \"views\": {\n                \"day\": \"Day\",\n                \"week\": \"Week\",\n                \"workWeek\": \"Work Week\",\n                \"agenda\": \"Agenda\",\n                \"month\": \"Month\"\n            },\n            \"recurrenceMessages\": {\n                \"deleteWindowTitle\": \"Delete Recurring Item\",\n                \"deleteWindowOccurrence\": \"Delete current occurrence\",\n                \"deleteWindowSeries\": \"Delete the series\",\n                \"editWindowTitle\": \"Edit Recurring Item\",\n                \"editWindowOccurrence\": \"Edit current occurrence\",\n                \"editWindowSeries\": \"Edit the series\",\n                \"deleteRecurring\": \"Do you want to delete only this event occurrence or the whole series?\",\n                \"editRecurring\": \"Do you want to edit only this event occurrence or the whole series?\"\n            },\n            \"editor\": {\n                \"title\": \"Title\",\n                \"start\": \"Start\",\n                \"end\": \"End\",\n                \"allDayEvent\": \"All day event\",\n                \"description\": \"Description\",\n                \"repeat\": \"Repeat\",\n                \"timezone\": \" \",\n                \"startTimezone\": \"Start timezone\",\n                \"endTimezone\": \"End timezone\",\n                \"separateTimezones\": \"Use separate start and end time zones\",\n                \"timezoneEditorTitle\": \"Timezones\",\n                \"timezoneEditorButton\": \"Time zone\",\n                \"timezoneTitle\": \"Time zones\",\n                \"noTimezone\": \"No timezone\",\n                \"editorTitle\": \"Event\"\n            }\n        });\n    }\n\n    /* Slider messages */\n\n    if (kendo.ui.Slider) {\n        kendo.ui.Slider.prototype.options =\n        $.extend(true, kendo.ui.Slider.prototype.options, {\n            \"increaseButtonTitle\": \"افزایش\",\n            \"decreaseButtonTitle\": \"کاهش\"\n        });\n    }\n\n    /* TreeList messages */\n\n    if (kendo.ui.TreeList) {\n        kendo.ui.TreeList.prototype.options.messages =\n        $.extend(true, kendo.ui.TreeList.prototype.options.messages, {\n            \"noRows\": \"ردیفی برای نمایش موجود نیست\",\n            \"loading\": \"در حال بارگزاری...\",\n            \"requestFailed\": \"شکست در انجام درخواست.\",\n            \"retry\": \"تلاش مجدد\",\n            \"commands\": {\n                \"edit\": \"ویرایش\",\n                \"update\": \"ذخیره\",\n                \"canceledit\": \"انصراف\",\n                \"create\": \"درج ردیف جدید\",\n                \"createchild\": \"درج گره جدید\",\n                \"destroy\": \"حذف\",\n                \"excel\": \"خروجی Excel\",\n                \"pdf\": \"خروجی PDF\"\n            }\n        });\n    }\n\n    /* TreeView messages */\n\n    if (kendo.ui.TreeView) {\n        kendo.ui.TreeView.prototype.options.messages =\n        $.extend(true, kendo.ui.TreeView.prototype.options.messages, {\n            \"loading\": \"در حال بارگزاری...\",\n            \"requestFailed\": \"شکست در انجام درخواست.\",\n            \"retry\": \"تلاش مجدد\"\n        });\n    }\n\n    /* Upload messages */\n\n    if (kendo.ui.Upload) {\n        kendo.ui.Upload.prototype.options.localization =\n        $.extend(true, kendo.ui.Upload.prototype.options.localization, {\n            \"select\": \"انتخاب فایل(ها)...\",\n            \"cancel\": \"انصراف\",\n            \"retry\": \"تلاش مجدد\",\n            \"remove\": \"حذف\",\n            \"uploadSelectedFiles\": \"بارگزاری فایل(ها)\",\n            \"dropFilesHere\": \"فایل(ها) را برای بارگزاری به اینجا بکشید\",\n            \"statusUploading\": \"در حال بارگزاری\",\n            \"statusUploaded\": \"پایان بارگزاری\",\n            \"statusWarning\": \"هشداد\",\n            \"statusFailed\": \"خطا در بارگزاری\",\n            \"headerStatusUploading\": \"در حال بارگزاری...\",\n            \"headerStatusUploaded\": \"اتمام بارگزاری\"\n        });\n    }\n\n    /* Validator messages */\n\n    if (kendo.ui.Validator) {\n        kendo.ui.Validator.prototype.options.messages =\n        $.extend(true, kendo.ui.Validator.prototype.options.messages, {\n            \"required\": \"{0} اجباری است\",\n            \"pattern\": \"{0} معتبر نیست\",\n            \"min\": \"{0} باید بزرگتر یا برابر باشد با {1}\",\n            \"max\": \"{0} باید کوچکتر یا برابر باشد با {1}\",\n            \"step\": \"{0} معتبر نیست\",\n            \"email\": \"{0} یک ایمیل معتبر نیست\",\n            \"url\": \"{0} آدرس وب سایت معتبر نیست\",\n            \"date\": \"{0} تاریخ معتبر نیست\",\n            \"dateCompare\": \"تاریخ پایان باید برابر یا بعد از تاریخ آغاز باشد\"\n        });\n    }\n})(window.kendo.jQuery);\n\n}));"]}