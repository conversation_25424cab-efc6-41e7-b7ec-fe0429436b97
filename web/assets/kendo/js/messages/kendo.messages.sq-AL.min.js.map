{"version": 3, "sources": ["messages/kendo.messages.sq-AL.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "noColor", "clearColor", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "overflowAnchor", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "cleanFormatting", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "units", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "expandCollapseColumnHeader", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "confirmationDialog", "text", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "colorPicker", "toolbar", "alignmentButtons", "backgroundColor", "borders", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "ListBox", "tools", "moveUp", "moveDown", "transferTo", "transferFrom", "transferAllTo", "transferAllFrom", "TreeView", "Upload", "localization", "clearSelectedFiles", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "progress", "Dialog", "close", "Calendar", "weekColumnHeader", "<PERSON><PERSON>", "Confirm", "Prompt", "DateInput", "hour", "minute", "dayperiod", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN;;;;;;;;;;;;;;;;;;;;;;;;CA0BF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,SACTC,OAAU,QACVC,QAAW,YACXC,WAAc,oBAMZV,MAAMC,GAAGU,cACbX,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,UACpDE,MAAS,SACTC,OAAU,QACVC,QAAW,YACXC,WAAc,oBAMZV,MAAMC,GAAGW,aACbZ,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,UACnDQ,cAAiB,4BACjBC,eAAkB,iCAClBC,OAAU,SACVC,QAAW,SACXC,KAAQ,WACRC,SAAY,sBACZC,KAAQ,SACRC,OAAU,UAMRpB,MAAMC,GAAGoB,SACbrB,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,UAC/CiB,KAAQ,OACRC,OAAU,SACVC,UAAa,UACbC,cAAiB,gBACjBC,YAAe,cACfC,UAAa,YACbC,cAAiB,mBACjBC,YAAe,wBACfC,aAAgB,0BAChBC,YAAe,YACfC,oBAAuB,gCACvBC,kBAAqB,6BACrBC,OAAU,UACVC,QAAW,WACXC,WAAc,oBACdC,OAAU,mBACVC,YAAe,qBACfC,WAAc,gBACdC,WAAc,gBACdC,SAAY,aACZC,SAAY,6BACZC,gBAAmB,sBACnBC,SAAY,4BACZC,gBAAmB,yBACnBC,YAAe,UACfC,WAAc,UACdC,UAAa,SACbC,UAAa,qBACbC,MAAS,SACTC,YAAe,mBACfC,WAAc,SACdC,eAAkB,iBAClBC,QAAW,kBACXC,YAAe,WACfC,YAAe,OACfC,gBAAmB,wFACnBC,WAAc,2CACdC,cAAiB,oFACjBC,kBAAqB,uCACrBC,gBAAmB,aACnBC,aAAgB,eAChBC,WAAc,gBACdC,YAAe,gBACfC,eAAkB,aAClBC,UAAa,UACbC,eAAkB,aAClBC,SAAY,SACZC,YAAe,WACfC,oBAAuB,+BACvBC,aAAgB,YAChBC,aAAgB,SAChBC,sBAAyB,MACzBC,aAAgB,QAChBC,gBAAmB,kBACnBC,YAAe,eACfC,cAAiB,0BACjBC,eAAkB,4BAClBC,YAAe,oBACfC,YAAe,qBACfC,UAAa,gBACbC,aAAgB,gBAChBC,SAAY,KACZC,YAAe,gBACfC,SAAY,SACZC,QAAW,SACXC,iBAAoB,QACpBC,QAAW,UACXC,QAAW,aACXC,MAAS,WACTC,OAAU,WACVC,MAAS,UACTC,YAAe,mBACfC,YAAe,qBACfC,WAAc,qBACdC,UAAa,WACbC,WAAc,WACdC,SAAY,YACZC,GAAM,KACNC,OAAU,SACVC,YAAe,kBACfC,gBAAmB,iBACnBC,SAAY,mBACZC,0BAA6B,+BAC7BC,UAAa,gBACbC,YAAe,gBACfC,WAAc,kBACdC,aAAgB,sBAChBC,eAAkB,sBAClBC,cAAiB,wBACjBC,gBAAmB,uBACnBC,kBAAqB,uBACrBC,iBAAoB,yBACpBC,gBAAmB,uBACnBC,kBAAqB,uBACrBC,iBAAoB,yBACpBC,YAAe,kBACfrG,QAAW,UACXsG,KAAQ,WACRC,eAAkB,8BAMhBvH,MAAMC,GAAGuH,cACbxH,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,UACpD+C,WAAc,SACdE,QAAW,iBACXE,YAAe,OACfD,YAAe,WACfK,kBAAqB,qCACrBT,YAAe,mBACfO,WAAc,0CACdD,gBAAmB,yFACnBE,cAAiB,sFACjB8D,cAAiB,uCACjBC,OAAU,WAMR1H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,UACnDuH,OAAU,kBACVC,QAAW,oBACX9G,OAAU,SACV+G,MAAS,SACTC,SAAY,cAMV/H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,uBACNC,IAAO,2BACPC,WAAc,YACdC,SAAY,UACZC,eAAkB,cAClBC,SAAY,eACZC,OAAU,aACVC,UAAa,iBACbC,QAAW,kBACXC,WAAc,uBAEhBC,QACEV,GAAM,uBACNC,IAAO,2BACPU,IAAO,sCACPC,GAAM,sBACNC,IAAO,sCACPC,GAAM,sBACNR,OAAU,aACVC,UAAa,kBAEfQ,MACEf,GAAM,uBACNC,IAAO,2BACPU,IAAO,+BACPC,GAAM,YACNC,IAAO,gCACPC,GAAM,aACNR,OAAU,aACVC,UAAa,kBAEfS,OACEhB,GAAM,uBACNC,IAAO,2BACPK,OAAU,aACVC,UAAa,qBAObzI,MAAMC,GAAGkJ,aACbnJ,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQC,UACnD+I,KAAQ,8BACRC,MAAS,6BACTzB,OAAU,kBACVC,QAAW,oBACX9G,OAAU,SACV+G,MAAS,SACTwB,IAAO,MACPC,GAAM,MACNC,YAAe,oBACfzB,SAAY,YACZ0B,MAAS,QACTjJ,OAAU,WAMRR,MAAMC,GAAGkJ,aACbnJ,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,uBACNC,IAAO,2BACPC,WAAc,YACdC,SAAY,UACZC,eAAkB,cAClBC,SAAY,eACZC,OAAU,aACVC,UAAa,iBACbC,QAAW,kBACXC,WAAc,uBAEhBC,QACEV,GAAM,uBACNC,IAAO,2BACPU,IAAO,sCACPC,GAAM,sBACNC,IAAO,sCACPC,GAAM,sBACNR,OAAU,aACVC,UAAa,kBAEfQ,MACEf,GAAM,uBACNC,IAAO,2BACPU,IAAO,uBACPC,GAAM,WACNC,IAAO,wBACPC,GAAM,YACNR,OAAU,aACVC,UAAa,kBAEfS,OACEhB,GAAM,uBACNC,IAAO,2BACPK,OAAU,aACVC,UAAa,qBAObzI,MAAMC,GAAGyJ,mBACb1J,MAAMC,GAAGyJ,iBAAiBvJ,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyJ,iBAAiBvJ,UAAUC,QAAQC,UACzDsJ,SAAY,oBACZ7B,MAAS,SACT/G,OAAU,SACV2G,OAAU,WAMR1H,MAAMC,GAAG2J,QACb5J,MAAMC,GAAG2J,MAAMzJ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,MAAMzJ,UAAUC,QAAQC,UAC9CwJ,SACEC,SAAY,cACZC,OAAU,cACVC,YAAe,cACfC,aAAgB,aAChBC,IAAO,mBAET1J,OAAU,QACV2J,4BAA+B,iBAC/BC,sBAAyB,gBACzBC,QAAW,QACXC,QACEC,aAAgB,QAChBC,YAAe,SACfC,IAAO,WACPC,gBAAmB,WACnBC,UAAa,UACbC,qBAAwB,UACxBC,gBAAmB,UACnBC,MAAS,OACTzB,MAAS,UACT0B,YAAe,WAEjBC,KAAQ,OACRC,OACEC,IAAO,OACPT,IAAO,WACPU,MAAS,QACTL,MAAS,QACTM,KAAQ,OACRC,KAAQ,WAORrL,MAAMC,GAAGqL,OACbtL,MAAMC,GAAGqL,KAAKnL,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqL,KAAKnL,UAAUC,QAAQC,UAC7CkL,UACE/K,OAAU,mBACVgL,WAAc,QACdC,OAAU,sBACVpB,QAAW,QACXqB,KAAQ,UACRC,MAAS,oBACTzB,IAAO,kBACPc,KAAQ,kBACRY,OAAU,SACVC,OAAU,aAEZC,UACEC,aAAgB,QAChBC,aAAgB,mDAChBC,cAAiB,SAEnBC,UAAa,kCACbC,2BAA8B,MAM5BnM,MAAMC,GAAGmM,WACbpM,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,UAC/CgM,OAAU,iCACVC,QAAW,mBACXC,cAAiB,mBACjBC,MAAS,eACTjB,UACIG,KAAQ,UACRG,OAAU,YACVL,WAAc,QACdC,OAAU,sBACVgB,YAAe,uBACfpC,QAAW,QACXsB,MAAS,oBACTzB,IAAO,sBAOXlK,MAAMC,GAAGyM,YACb1M,MAAMC,GAAGyM,UAAUvM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyM,UAAUvM,UAAUC,QAAQC,UAClDsM,MAAS,gFAMP3M,MAAMC,GAAG2M,iBACb5M,MAAMC,GAAG2M,eAAezM,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,eAAezM,UAAUC,SAC/CyM,YAAe,cACfC,cAAiB,oBAMf9M,MAAMC,GAAG8M,cACb/M,MAAMC,GAAG8M,YAAY5M,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8M,YAAY5M,UAAUC,QAAQC,UACpD2M,MAAS,QACTC,KAAQ,OACRC,KAAQ,QACRC,OAAU,iBACVC,QAAW,YACXC,WAAc,mBAMZrN,MAAMC,GAAGqN,QACbtN,MAAMC,GAAGqN,MAAMnN,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqN,MAAMnN,UAAUC,QAAQC,UAC9CkN,SAAY,YACZC,QAAW,+BACXb,MAAS,gCACTc,KAAQ,OACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,yBACTC,SAAY,8BACZC,KAAQ,6BACRC,KAAQ,2BACRC,QAAW,WACXC,UAAa,mBAMXjO,MAAMC,GAAGiO,gBACblO,MAAMC,GAAGiO,cAAc/N,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiO,cAAc/N,UAAUC,QAAQC,UACtDkN,SAAY,YACZC,QAAW,+BACXb,MAAS,gCACTc,KAAQ,OACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,yBACTC,SAAY,8BACZC,KAAQ,6BACRC,KAAQ,2BACRC,QAAW,WACXC,UAAa,mBAMXjO,MAAMC,GAAGkO,YACbnO,MAAMC,GAAGkO,UAAUhO,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkO,UAAUhO,UAAUC,QAAQC,UAClD+N,cAAiB,gCACjBC,aAAgB,+BAChBC,UAAa,kCAMXtO,MAAMC,GAAGsO,iBACbvO,MAAMC,GAAGsO,eAAepO,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsO,eAAepO,UAAUC,QAAQC,UACvD+I,KAAQ,qCACRoF,aAAgB,mBAChBzN,OAAU,SACV0N,QAAW,qBACXpF,MAAS,2BACTvB,MAAS,SACT4G,GAAM,KACNlO,OAAU,QACVwH,WACEK,SAAY,WACZC,eAAkB,eAClBF,WAAc,YACdG,SAAY,eACZL,GAAM,uBACNC,IAAO,+BAOPnI,MAAMC,GAAG0O,mBACb3O,MAAMC,GAAG0O,iBAAiBxO,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0O,iBAAiBxO,UAAUC,QAAQC,UACzDuO,aACEC,MAAS,YACTC,OAAU,UACVC,MAAS,WACTC,OAAU,WACVC,QAAW,WACXC,OAAU,WAEZJ,QACEK,YAAe,oBACfC,SAAY,QAEdL,OACEI,YAAe,oBACfC,SAAY,SAEdJ,QACEI,SAAY,QACZD,YAAe,oBACfE,SAAY,oBAEdJ,SACEE,YAAe,oBACfE,SAAY,mBACZD,SAAY,QACZlE,IAAO,SAETgE,QACEC,YAAe,oBACfE,SAAY,mBACZD,SAAY,OACZ1B,GAAM,QAERjD,KACE6E,MAAS,aACTC,YAAe,WACfV,MAAS,YACTW,MAAS,OACTC,WAAc,cACdC,GAAM,OAERC,iBACE/B,MAAS,SACTgC,OAAU,SACVC,MAAS,UACTC,OAAU,YACV/B,KAAQ,YAEVgC,UACE7E,IAAO,OACP8E,QAAW,YACXC,QAAW,uBAOXjQ,MAAMC,GAAGiQ,YACblQ,MAAMC,GAAGiQ,UAAU/P,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiQ,UAAU/P,UAAUC,QAAQC,UAClD8P,OAAU,aACVlH,KAAQ,OACRmH,MAAS,UACTC,KAAQ,OACRC,YAAe,sBACfC,YAAe,wBACfC,MAAS,MACTxF,KAAQ,OACRxK,OAAU,QACV6J,QAAW,QACXoG,kBAAqB,iBACrBC,cAAiB,iCACjBC,eAAkB,wBAClB7E,UACEE,aAAgB,mDAElBf,OACEC,IAAO,OACPE,KAAQ,OACRwF,SAAY,eACZC,OAAU,cACV1F,MAAS,QAEX2F,oBACEL,kBAAqB,iCACrBM,uBAA0B,4BAC1BC,mBAAsB,gBACtBC,gBAAmB,kCACnBC,qBAAwB,6BACxBC,iBAAoB,iBACpBC,gBAAmB,yDACnBC,cAAiB,6DAEnB/G,QACEjB,MAAS,UACTyB,MAAS,UACTL,IAAO,aACP6G,YAAe,uBACfC,YAAe,aACfC,OAAU,WACVC,SAAY,IACZC,cAAiB,qBACjBC,YAAe,wBACfC,kBAAqB,4DACrBC,oBAAuB,eACvBC,qBAAwB,cACxBC,cAAiB,eACjBC,WAAc,qBACdxH,YAAe,cAOfxK,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS6R,gBACpDlS,MAAMiS,YAAY5R,SAAS6R,cAC3BpS,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS6R,eACxCC,WAAc,oBACdC,cAAiB,sBACjBC,wBAA2B,iCAC3BC,sBAAyB,+BACzBC,eAAkB,oBAClBC,WAAc,iBACdC,UAAa,eACbC,YAAe,mBACfC,aAAgB,gBAChBC,UAAa,UACbC,MAAS,qBACTC,YAAe,4BACfvS,MAAS,SACTC,OAAU,WAIRR,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS0S,UACpD/S,MAAMiS,YAAY5R,SAAS0S,QAC3BjT,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS0S,SACxCxS,MAAS,SACTyK,KAAQ,OACRxK,OAAU,QACVwS,OAAU,SACVxG,MAAS,eACTyG,OAAU,SACVC,OAAU,KACVC,mBACE9J,MAAS,UACT+J,YACExK,OAAU,QACVyK,SAAY,SACZpK,KAAQ,SAGZqK,kBACEjK,MAAS,SAEXkK,gBACElK,MAAS,qBAEXmK,eACEnK,MAAS,WAEXoK,iBACEpK,MAAS,WACTqK,SACCC,aAAgB,gBAChB/R,cAAiB,SACjBE,aAAgB,kBAChBC,YAAe,YACf6R,SAAY,eACZC,YAAe,gBACfC,YAAe,kBAGlBC,aACE1K,MAAS,iBACTqK,SACEM,WAAc,mBACdC,kBAAqB,wBACrBC,gBAAmB,sBACnBC,QAAW,eAGfC,cACE/K,MAAS,qBACTqK,SACEW,YAAe,qBACfC,WAAc,sBACdC,cAAiB,qBACjBC,SAAY,wBAGhBC,oBACEC,KAAQ,gDACRrL,MAAS,cAEXsL,kBACEtL,MAAS,2BACTuL,YAAe,iDACfC,UAAa,kBACbC,UACEC,IAAO,YACPnM,OAAU,QACV8L,KAAQ,SACRzL,KAAQ,OACR+L,OAAU,0BACVC,KAAQ,SAEVC,WACEC,YAAe,gBACfC,SAAY,gBACZC,QAAW,WACXC,WAAc,cACdC,QAAW,iBACXC,WAAc,oBACdC,qBAAwB,gCACxBC,kBAAqB,iCAEvBC,kBACER,YAAe,oBACfC,SAAY,oBACZC,QAAW,uBACXC,WAAc,0BACdC,QAAW,qBACXC,WAAc,wBACdC,qBAAwB,oCACxBC,kBAAqB,oCACrBV,OAAU,8BAEZY,QACEd,SAAY,UACZe,SAAY,YACZC,IAAO,MACPC,IAAO,MACPtM,MAAS,QACTqB,MAAS,QACTL,IAAO,WACPuL,cAAiB,6BACjBC,YAAe,gBACfC,YAAe,qBACfC,SAAY,eACZtB,UAAa,kBACbD,YAAe,mBACfwB,YAAe,eAEjBC,cACEC,UAAa,iBACbC,YAAe,mBAGnBC,gBACEnN,MAAS,cACTuM,QACEa,SAAY,gBACZC,WAAc,cACdC,WAAc,WACdC,UAAa,oBACbC,QAAW,WACXC,YAAe,YACfC,MAAS,SACTC,WAAc,WACdC,OAAU,SACVC,aAAgB,iBAChBC,WAAc,iBAGlBC,oBACEC,aAAgB,0DAElBC,mBACEjO,MAAS,sBACTgO,aAAgB,qGAChBzB,QACE2B,QAAW,aACXC,OAAU,aACVC,SAAY,gBAGhBC,4BACEL,aAAgB,kEAKhBrX,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAASsX,aACpD3X,MAAMiS,YAAY5R,SAASsX,WAC3B7X,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAASsX,YACxC9W,cAAiB,6BACjBC,eAAkB,6BAClB8W,cAAiB,sBACjBC,kBAAqB,wBACrBtX,MAAS,SACTmH,OAAU,QACVoQ,aAAgB,iCAChBhQ,MAAS,SACTiQ,OAAU,cACVC,aAAgB,QAChB1O,IAAO,MACPC,GAAM,MACNvB,WACEC,QACEI,SAAY,iBACZC,eAAkB,qBAClBF,WAAc,mBACdG,SAAY,uBAEdU,MACEf,GAAO,aACPC,IAAO,iBACPa,GAAO,kBACPF,GAAO,kBAETF,QACEV,GAAM,uBACNC,IAAO,2BACPU,IAAO,sCACPC,GAAM,sBACNC,IAAO,sCACPC,GAAM,2BAMRhJ,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS4X,cACpDjY,MAAMiS,YAAY5R,SAAS4X,YAC3BnY,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS4X,aACxCpF,MAAS,qBACTC,YAAe,4BACfvS,MAAS,SACTC,OAAU,WAIRR,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS6X,UACpDlY,MAAMiS,YAAY5R,SAAS6X,QAC3BpY,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS6X,SACxCrT,cAAiB,qBACjBC,eAAkB,uBAClBC,YAAe,oBACfC,YAAe,qBACfgB,UAAa,WACbmS,kBACExE,aAAgB,gBAChB/R,cAAiB,SACjBE,aAAgB,kBAChBC,YAAe,YACf6R,SAAY,eACZC,YAAe,gBACfC,YAAe,iBAEjBsE,gBAAmB,WACnB9W,KAAQ,OACR+W,QAAW,UACXJ,aACEpF,MAAS,oBACTC,YAAe,6BAEjBwF,KAAQ,QACRC,IAAO,OACPrT,aAAgB,gBAChBD,UAAa,iBACbuT,YAAe,uBACfzX,OAAU,SACV0X,WAAc,QACd7V,SAAY,oBACZ8V,OAAU,6BACVC,aACEC,UAAa,YACbhQ,OAAU,QACViQ,QAAW,YACXC,UAAa,YACbzF,SAAY,SACZpK,KAAQ,OACRoH,KAAQ,OACR0I,SAAY,YACZC,SAAY,cACZC,YAAe,uBAEjBC,sBAAyB,kBACzBC,sBAAyB,kBACzBC,OAAU,qBACVC,eACEhF,YAAe,qBACfC,WAAc,sBACdC,cAAiB,qBACjBC,SAAY,uBAEdjT,OAAU,SACV+X,MAAS,iBACTC,cACEvF,WAAc,mBACdC,kBAAqB,wBACrBC,gBAAmB,sBACnBC,QAAW,cAEbqF,KAAQ,UACRC,MAAS,SACTC,aACEC,KAAQ,QACRC,KAAQ,QAEVC,OAAU,aACVC,QAAW,kBACXC,SAAY,kBACZC,aACEC,aAAgB,0BAChBC,cAAiB,0BACjBC,aAAgB,2BAChBC,cAAiB,4BAEnBC,UAAa,mBACbC,SAAY,mBACZ9Y,UAAa,UACb+Y,WAAc,iCAIZva,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAASma,OACpDxa,MAAMiS,YAAY5R,SAASma,KAC3B1a,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAASma,MACxCC,QACEC,sBAAyB,wIACzBC,4BAA+B,sEAC/BC,gBAAmB,2EAErBC,MACEC,KAAQ,UACRC,OAAU,SACVC,KAAQ,eAORhb,MAAMC,GAAGgb,SACbjb,MAAMC,GAAGgb,OAAO9a,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgb,OAAO9a,UAAUC,SACvC8a,oBAAuB,QACvBC,oBAAuB,WAMrBnb,MAAMC,GAAGmb,UACbpb,MAAMC,GAAGmb,QAAQjb,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmb,QAAQjb,UAAUC,QAAQC,UAChDgb,OACErI,OAAU,QACVsI,OAAU,iBACVC,SAAY,kBACZC,WAAc,eACdC,aAAgB,iBAChBC,cAAiB,yBACjBC,gBAAmB,+BAOnB3b,MAAMC,GAAGmM,WACbpM,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,UACjDgM,OAAU,iCACVC,QAAW,mBACXC,cAAiB,mBACjBC,MAAS,eACTjB,UACIG,KAAQ,YACRG,OAAU,YACVL,WAAc,QACdC,OAAU,sBACVgB,YAAe,uBACfpC,QAAW,QACXsB,MAAS,oBACTzB,IAAO,qBAOTlK,MAAMC,GAAG2b,WACb5b,MAAMC,GAAG2b,SAASzb,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2b,SAASzb,UAAUC,QAAQC,UACjDiM,QAAW,mBACXC,cAAiB,mBACjBC,MAAS,kBAMPxM,MAAMC,GAAG4b,SACb7b,MAAMC,GAAG4b,OAAO1b,UAAUC,QAAQ0b,aAClChc,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4b,OAAO1b,UAAUC,QAAQ0b,cAC/ClQ,OAAU,mBACVpL,OAAU,QACVgM,MAAS,eACTwG,OAAU,QACV+I,mBAAsB,SACtBC,oBAAuB,gBACvBvU,cAAiB,uCACjBwU,gBAAmB,gBACnBC,eAAkB,aAClBC,cAAiB,eACjBC,aAAgB,UAChBC,sBAAyB,mBACzBC,qBAAwB,WACxBC,mBAAsB,yCACtBC,mBAAsB,yCACtBC,qBAAwB,iCAMtBzc,MAAMC,GAAGyc,YACb1c,MAAMC,GAAGyc,UAAUvc,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyc,UAAUvc,UAAUC,QAAQC,UAClDsc,SAAY,gBACZC,QAAW,2BACX9G,IAAO,sDACPC,IAAO,sDACP8G,KAAQ,2BACRC,MAAS,kCACTC,IAAO,kCACP9T,KAAQ,gCACR+T,YAAe,qFAKbhd,MAAMC,GAAGgd,WACbjd,MAAMC,GAAGgd,SAAS5c,SAClBP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgd,SAAS5c,UAC7BiM,QAAS,sBAMTtM,MAAMC,GAAGid,SACbld,MAAMC,GAAGid,OAAO/c,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGid,OAAO/c,UAAUC,QAAQ0b,cAC/CqB,MAAS,YAKPnd,MAAMC,GAAGmd,WACbpd,MAAMC,GAAGmd,SAASjd,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmd,SAASjd,UAAUC,QAAQC,UACjDgd,iBAAoB,MAMlBrd,MAAMC,GAAGqd,QACbtd,MAAMC,GAAGqd,MAAMnd,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqd,MAAMnd,UAAUC,QAAQ0b,cAC9C5I,OAAU,QAMRlT,MAAMC,GAAGsd,UACbvd,MAAMC,GAAGsd,QAAQpd,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsd,QAAQpd,UAAUC,QAAQ0b,cAChD5I,OAAU,KACV1S,OAAU,WAKRR,MAAMC,GAAGud,SACbxd,MAAMC,GAAGud,OAAOrd,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGud,OAAOrd,UAAUC,QAAQ0b,cAC/C5I,OAAU,KACV1S,OAAU,WAKRR,MAAMC,GAAGwd,YACXzd,MAAMC,GAAGwd,UAAUtd,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwd,UAAUtd,UAAUC,QAAQC,UAClDgL,KAAQ,OACRF,MAAS,QACTD,IAAO,OACP8E,QAAW,eACX0N,KAAQ,OACRC,OAAU,UACV/N,OAAU,WACVgO,UAAa,YAIhBC,OAAO7d,MAAM8d", "file": "kendo.messages.sq-AL.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n/**\n * Kendo UI v2017.3.1026 (http://www.telerik.com/kendo-ui)\n * Copyright 2017 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.\n *\n * Kendo UI commercial licenses may be obtained at\n * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete\n * If you do not own a commercial license, this file shall be governed by the trial license terms.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n*/\n\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Apliko\",\n  \"cancel\": \"Anulo\",\n  \"noColor\": \"pa ngjyrë\",\n  \"clearColor\": \"Pastro ngjyrën\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"Apliko\",\n  \"cancel\": \"Anulo\",\n  \"noColor\": \"pa ngjyrë\",\n  \"clearColor\": \"Pastro ngjyrën\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Rendit duke rritur vlerën\",\n  \"sortDescending\": \"Rendit duke e zvogëluar vlerën\",\n  \"filter\": \"Filtro\",\n  \"columns\": \"Kolona\",\n  \"done\": \"Përfundo\",\n  \"settings\": \"Cilësimet e kolonës\",\n  \"lock\": \"Mbylle\",\n  \"unlock\": \"Hape\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Bold\",\n  \"italic\": \"Italic\",\n  \"underline\": \"Nënvizë\",\n  \"strikethrough\": \"I mesvijëzuar\",\n  \"superscript\": \"Superscript\",\n  \"subscript\": \"Nënscript\",\n  \"justifyCenter\": \"Teksti në qendër\",\n  \"justifyLeft\": \"Radhit tekstin majtas\",\n  \"justifyRight\": \"Radhit tekstin djathtas\",\n  \"justifyFull\": \"Justifiko\",\n  \"insertUnorderedList\": \"Vendosni listë të pa renditur\",\n  \"insertOrderedList\": \"Vendosni listë të renditur\",\n  \"indent\": \"Porosit\",\n  \"outdent\": \"Nxjerrje\",\n  \"createLink\": \"Vendosi hyperlink\",\n  \"unlink\": \"Fshini hyperlink\",\n  \"insertImage\": \"Vendosni fotografi\",\n  \"insertFile\": \"Vendosni fajl\",\n  \"insertHtml\": \"Vendosni HTML\",\n  \"viewHtml\": \"Shiko HTML\",\n  \"fontName\": \"Zgjedh llojin e shkronjave\",\n  \"fontNameInherit\": \"(font i trashëguar)\",\n  \"fontSize\": \"Zgjedh madhësinë e fontit\",\n  \"fontSizeInherit\": \"(madhësi e trashëguar)\",\n  \"formatBlock\": \"Formati\",\n  \"formatting\": \"Formati\",\n  \"foreColor\": \"Ngjyra\",\n  \"backColor\": \"Ngjyra e prapavisë\",\n  \"style\": \"Stilet\",\n  \"emptyFolder\": \"Folder i zbrazët\",\n  \"uploadFile\": \"Ngarko\",\n  \"overflowAnchor\": \"Më shumë vegla\",\n  \"orderBy\": \"Organizoni nga:\",\n  \"orderBySize\": \"Madhësia\",\n  \"orderByName\": \"Emri\",\n  \"invalidFileType\": \"Fajli i zgjedhur \\\"{0}\\\" nuk është i vlefshëm. Llojet e fajleve të mbështetur janë {1}.\",\n  \"deleteFile\": 'Jeni të sigurtë që doni të fshini \"{0}\"?',\n  \"overwriteFile\": 'Fajli me emrin \"{0}\" tashmë ekziston në dosjen aktuale. Dëshironi ta mbishkruani?',\n  \"directoryNotFound\": \"Dosje me këtë emër nuk është gjetur.\",\n  \"imageWebAddress\": \"Web adresa\",\n  \"imageAltText\": \"Tekst tjetër\",\n  \"imageWidth\": \"Gjërësia (px)\",\n  \"imageHeight\": \"Lartësia (px)\",\n  \"fileWebAddress\": \"Web adresa\",\n  \"fileTitle\": \"Titulli\",\n  \"linkWebAddress\": \"Web adresa\",\n  \"linkText\": \"Teksti\",\n  \"linkToolTip\": \"Ndihmesë\",\n  \"linkOpenInNewWindow\": \"Hape linkun në dritare të re\",\n  \"dialogUpdate\": \"Përditëso\",\n  \"dialogInsert\": \"Vendos\",\n  \"dialogButtonSeparator\": \"ose\",\n  \"dialogCancel\": \"Anulo\",\n  \"cleanFormatting\": \"Pastro formatin\",\n  \"createTable\": \"Krijo tabelë\",\n  \"addColumnLeft\": \"Shto kolonë në të majtë\",\n  \"addColumnRight\": \"Shto kolonë në të djathtë\",\n  \"addRowAbove\": \"Shto rresht lartë\",\n  \"addRowBelow\": \"Shto rresht poshtë\",\n  \"deleteRow\": \"Fshij reshtin\",\n  \"deleteColumn\": \"Fshij kolonën\",\n  \"dialogOk\": \"Ok\",\n  \"tableWizard\": \"Tabela Wizard\",\n  \"tableTab\": \"Tabela\",\n  \"cellTab\": \"Qelizë\",\n  \"accessibilityTab\": \"Qasja\",\n  \"caption\": \"Titulli\",\n  \"summary\": \"Përmledhja\",\n  \"width\": \"Gjërësia\",\n  \"height\": \"Lartësia\",\n  \"units\": \"Njësitë\",\n  \"cellSpacing\": \"Ndarja e qelizës\",\n  \"cellPadding\": \"Mbushja e qelizave\",\n  \"cellMargin\": \"Margjina e qelizës\",\n  \"alignment\": \"Radhitja\",\n  \"background\": \"Prapavia\",\n  \"cssClass\": \"CSS Klasa\",\n  \"id\": \"ID\",\n  \"border\": \"Kufiri\",\n  \"borderStyle\": \"Stili i Kufirit\",\n  \"collapseBorders\": \"Mbylli kufijtë\",\n  \"wrapText\": \"Përfundo tekstin\",\n  \"associateCellsWithHeaders\": \"Ndërlidhni qelizat me titujt\",\n  \"alignLeft\": \"Radhit majtas\",\n  \"alignCenter\": \"Radhit në mes\",\n  \"alignRight\": \"Radhit djathtas\",\n  \"alignLeftTop\": \"Radhit majtas lartë\",\n  \"alignCenterTop\": \"Radhit në mes lartë\",\n  \"alignRightTop\": \"Radhit djathtas lartë\",\n  \"alignLeftMiddle\": \"Radhit majtas në mes\",\n  \"alignCenterMiddle\": \"Radhit në mes në mes\",\n  \"alignRightMiddle\": \"Radhit djathtas në mes\",\n  \"alignLeftBottom\": \"Radhit majtas poshtë\",\n  \"alignCenterBottom\": \"Radhit në mes poshtë\",\n  \"alignRightBottom\": \"Radhit djathtas poshtë\",\n  \"alignRemove\": \"Fshij Radhitjen\",\n  \"columns\": \"Kolonat\",\n  \"rows\": \"Rreshtat\",\n  \"selectAllCells\": \"Zgjedh të gjitha qelizat\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Ngarko\",\n  \"orderBy\": \"Organizoni nga\",\n  \"orderByName\": \"Emri\",\n  \"orderBySize\": \"Madhësia\",\n  \"directoryNotFound\": \"Një dosje me këtë emër nuk u gjet.\",\n  \"emptyFolder\": \"Folder i zbrazët\",\n  \"deleteFile\": 'Jeni të sigurt se doni të fshini \"{0}\"?',\n  \"invalidFileType\": \"Fajli i selektuar \\\"{0}\\\" nuk është i vlefshëm. Llojet e fajleve të mbështetur janë {1}.\",\n  \"overwriteFile\": \"Fajli me emrin \\\"{0}\\\" tashmë ekziston në dosjen aktuale. Dëshironi t'a mbishkruani?\",\n  \"dropFilesHere\": \"lëshoni fajlet këtu për t'i ngarkuar\",\n  \"search\": \"Kërko\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"është e vërtetë\",\n  \"isFalse\": \"është e pavërtetë\",\n  \"filter\": \"Filtro\",\n  \"clear\": \"Pastro\",\n  \"operator\": \"Operator\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Është e barabartë me\",\n    \"neq\": \"Nuk është e barabartë me\",\n    \"startswith\": \"Fillon me\",\n    \"contains\": \"Përmban\",\n    \"doesnotcontain\": \"Nuk përmban\",\n    \"endswith\": \"Përfundon me\",\n    \"isnull\": \"Është zero\",\n    \"isnotnull\": \"Nuk është zero\",\n    \"isempty\": \"Është e zbrazët\",\n    \"isnotempty\": \"Nuk është e zbrazët\"\n  },\n  \"number\": {\n    \"eq\": \"Është e barabartë me\",\n    \"neq\": \"Nuk është e barabartë me\",\n    \"gte\": \"Është më e madhe ose e barabartë me\",\n    \"gt\": \"Është më e madhe se\",\n    \"lte\": \"Është më e vogël ose e barabartë me\",\n    \"lt\": \"Është me e vogël se\",\n    \"isnull\": \"Është zero\",\n    \"isnotnull\": \"Nuk është zero\"\n  },\n  \"date\": {\n    \"eq\": \"Është e barabartë me\",\n    \"neq\": \"Nuk është e barabartë me\",\n    \"gte\": \"Është pas ose e barabartë me\",\n    \"gt\": \"Është pas\",\n    \"lte\": \"Është para ose e barabartë me\",\n    \"lt\": \"Është para\",\n    \"isnull\": \"Është zero\",\n    \"isnotnull\": \"Nuk është zero\"\n  },\n  \"enums\": {\n    \"eq\": \"Është e barabartë me\",\n    \"neq\": \"Nuk është e barabartë me\",\n    \"isnull\": \"Është zero\",\n    \"isnotnull\": \"Nuk është zero\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Trego artikujt me vlerë që:\",\n  \"title\": \"Trego artikujt me vlerë që\",\n  \"isTrue\": \"është e vërtetë\",\n  \"isFalse\": \"është e pavërtetë\",\n  \"filter\": \"Filtro\",\n  \"clear\": \"Pastro\",\n  \"and\": \"Dhe\",\n  \"or\": \"Ose\",\n  \"selectValue\": \"-Zgjidhni vlerën-\",\n  \"operator\": \"Operatori\",\n  \"value\": \"Vlera\",\n  \"cancel\": \"Anulo\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Është e barabartë me\",\n    \"neq\": \"Nuk është e barabartë me\",\n    \"startswith\": \"Fillon me\",\n    \"contains\": \"Përmban\",\n    \"doesnotcontain\": \"Nuk përmban\",\n    \"endswith\": \"Përfundon me\",\n    \"isnull\": \"Është zero\",\n    \"isnotnull\": \"Nuk është zero\",\n    \"isempty\": \"Është e zbrazët\",\n    \"isnotempty\": \"Nuk është e zbrazët\"\n  },\n  \"number\": {\n    \"eq\": \"Është e barabartë me\",\n    \"neq\": \"Nuk është e barabartë me\",\n    \"gte\": \"Është më e madhe ose e barabartë me\",\n    \"gt\": \"Është më e madhe se\",\n    \"lte\": \"Është më e vogël ose e barabartë me\",\n    \"lt\": \"Është më e vogël se\",\n    \"isnull\": \"Është zero\",\n    \"isnotnull\": \"Nuk është zero\"\n  },\n  \"date\": {\n    \"eq\": \"Është e barabartë me\",\n    \"neq\": \"Nuk është e barabartë me\",\n    \"gte\": \"Is after or equal to\",\n    \"gt\": \"Is after\",\n    \"lte\": \"Is before or equal to\",\n    \"lt\": \"Is before\",\n    \"isnull\": \"Është zero\",\n    \"isnotnull\": \"Nuk është zero\"\n  },\n  \"enums\": {\n    \"eq\": \"Është e barabartë me\",\n    \"neq\": \"Nuk është e barabartë me\",\n    \"isnull\": \"Është zero\",\n    \"isnotnull\": \"Nuk është zero\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Selekto të gjitha\",\n  \"clear\": \"Pastro\",\n  \"filter\": \"Filtro\",\n  \"search\": \"Kërko\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Shto fëmijë\",\n    \"append\": \"Shto detyrë\",\n    \"insertAfter\": \"Shto poshtë\",\n    \"insertBefore\": \"Shto lartë\",\n    \"pdf\": \"Eksporto në PDF\"\n  },\n  \"cancel\": \"Anulo\",\n  \"deleteDependencyWindowTitle\": \"Fshij varësinë\",\n  \"deleteTaskWindowTitle\": \"Fshij detyrën\",\n  \"destroy\": \"Fshij\",\n  \"editor\": {\n    \"assingButton\": \"Cakto\",\n    \"editorTitle\": \"Detyrë\",\n    \"end\": \"Përfundo\",\n    \"percentComplete\": \"Kompleto\",\n    \"resources\": \"Burimet\",\n    \"resourcesEditorTitle\": \"Burimet\",\n    \"resourcesHeader\": \"Burimet\",\n    \"start\": \"Filo\",\n    \"title\": \"Titulli\",\n    \"unitsHeader\": \"Njësitë\"\n  },\n  \"save\": \"Ruaj\",\n  \"views\": {\n    \"day\": \"Dita\",\n    \"end\": \"Përfundo\",\n    \"month\": \"Muaji\",\n    \"start\": \"Fillo\",\n    \"week\": \"Java\",\n    \"year\": \"Viti\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Anulo ndryshimet\",\n    \"canceledit\": \"Anulo\",\n    \"create\": \"Shto të dhënë të re\",\n    \"destroy\": \"Fshij\",\n    \"edit\": \"Redakto\",\n    \"excel\": \"Eksporto në Excel\",\n    \"pdf\": \"Eksporto në PDF\",\n    \"save\": \"Ruaj ndryshimet\",\n    \"select\": \"Zgjedh\",\n    \"update\": \"Përditëso\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Anulo\",\n    \"confirmation\": \"Je i sigurt që dëshiron ta fshish këtë të dhënë?\",\n    \"confirmDelete\": \"Fshij\"\n  },\n  \"noRecords\": \"Nuk ka të dhëna në dispozicion.\",\n  \"expandCollapseColumnHeader\": \"\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n    \"noRows\": \"Nuk ka të dhëna për të shfaqur\",\n    \"loading\": \"Duke ngarkuar...\",\n    \"requestFailed\": \"Kërkesa dështoi.\",\n    \"retry\": \"Provo sërish\",\n    \"commands\": {\n        \"edit\": \"Redakto\",\n        \"update\": \"Përditëso\",\n        \"canceledit\": \"Anulo\",\n        \"create\": \"Shto të dhënë të re\",\n        \"createchild\": \"Shto të dhënë fëmijë\",\n        \"destroy\": \"Fshij\",\n        \"excel\": \"Eksporto në Excel\",\n        \"pdf\": \"Eksporto në PDF\"\n    }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Tërheq titullin e kolonës dhe lësho atë këtu për të grupuar nga ajo kolonë\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Rrit vlerën\",\n  \"downArrowText\": \"Zvogëlo vlerën\"\n});\n}\n\n/* MediaPlayer messages */\n\nif (kendo.ui.MediaPlayer) {\nkendo.ui.MediaPlayer.prototype.options.messages =\n$.extend(true, kendo.ui.MediaPlayer.prototype.options.messages,{\n  \"pause\": \"Pauzo\",\n  \"play\": \"Luaj\",\n  \"mute\": \"Pa zë\",\n  \"unmute\": \"Aktivizo zërin\",\n  \"quality\": \"Kualiteti\",\n  \"fullscreen\": \"Ekran i plotë\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Të gjitha\",\n  \"display\": \"{0} - {1} prej {2} artikujve\",\n  \"empty\": \"Nuk ka artikuj për të shfaqur\",\n  \"page\": \"Faqe\",\n  \"of\": \"of {0}\",\n  \"itemsPerPage\": \"artikuj për faqe\",\n  \"first\": \"Shkoni në faqen e parë\",\n  \"previous\": \"Shkoni në faqen e mëparshme\",\n  \"next\": \"Shkoni në faqen e ardhshme\",\n  \"last\": \"Shkoni në faqen e fundit\",\n  \"refresh\": \"Rifresko\",\n  \"morePages\": \"Më shumë faqe\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"Të gjitha\",\n  \"display\": \"{0} - {1} prej {2} artikujve\",\n  \"empty\": \"Nuk ka artikuj për të shfaqur\",\n  \"page\": \"Faqe\",\n  \"of\": \"of {0}\",\n  \"itemsPerPage\": \"artikuj për faqe\",\n  \"first\": \"Shkoni në faqen e parë\",\n  \"previous\": \"Shkoni në faqen e mëparshme\",\n  \"next\": \"Shkoni në faqen e ardhshme\",\n  \"last\": \"Shkoni në faqen e fundit\",\n  \"refresh\": \"Rifresko\",\n  \"morePages\": \"Më shumë faqe\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"Lësho fushat me të dhëna këtu\",\n  \"columnFields\": \"Lësho fushat e kolonave këtu\",\n  \"rowFields\": \"Lësho fushat e reshtave këtu\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"Trego artikujt me vlerat të cilat:\",\n  \"filterFields\": \"Filtri i fushave\",\n  \"filter\": \"Filtro\",\n  \"include\": \"Përfshij Fushat...\",\n  \"title\": \"Fushat për t'i përfshirë\",\n  \"clear\": \"Pastro\",\n  \"ok\": \"Ok\",\n  \"cancel\": \"Anulo\",\n  \"operators\": {\n    \"contains\": \"Përmbanë\",\n    \"doesnotcontain\": \"Nuk përmbanë\",\n    \"startswith\": \"Fillon me\",\n    \"endswith\": \"Përfundon me\",\n    \"eq\": \"Është e barabartë me\",\n    \"neq\": \"Nuk është e barabartë me\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Asnjëherë\",\n    \"hourly\": \"Çdo orë\",\n    \"daily\": \"Çdo ditë\",\n    \"weekly\": \"Çdo javë\",\n    \"monthly\": \"Çdo muaj\",\n    \"yearly\": \"Çdo vit\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Përsëriteni çdo: \",\n    \"interval\": \" orë\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Përsëriteni çdo: \",\n    \"interval\": \" ditë\"\n  },\n  \"weekly\": {\n    \"interval\": \" javë\",\n    \"repeatEvery\": \"Përsëriteni çdo: \",\n    \"repeatOn\": \"Përsëriteni në: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Përsëriteni çdo: \",\n    \"repeatOn\": \"Përsëriteni në: \",\n    \"interval\": \" muaj\",\n    \"day\": \"Ditë \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Përsëriteni çdo: \",\n    \"repeatOn\": \"Përsëriteni në: \",\n    \"interval\": \" vit\",\n    \"of\": \" të \"\n  },\n  \"end\": {\n    \"label\": \"Përfundoj:\",\n    \"mobileLabel\": \"Pëfundon\",\n    \"never\": \"Asnjëherë\",\n    \"after\": \"Pas \",\n    \"occurrence\": \" dukuri(të)\",\n    \"on\": \"Në \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"e para\",\n    \"second\": \"e dyta\",\n    \"third\": \"e treta\",\n    \"fourth\": \"e katërta\",\n    \"last\": \"e fundit\"\n  },\n  \"weekdays\": {\n    \"day\": \"ditë\",\n    \"weekday\": \"ditë jave\",\n    \"weekend\": \"ditë e fundjavës\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"tërë ditën\",\n  \"date\": \"Data\",\n  \"event\": \"Ngjarje\",\n  \"time\": \"Koha\",\n  \"showFullDay\": \"Trego ditën e plotë\",\n  \"showWorkDay\": \"Trego orët e biznesit\",\n  \"today\": \"Sot\",\n  \"save\": \"Ruaj\",\n  \"cancel\": \"Anulo\",\n  \"destroy\": \"Fshij\",\n  \"deleteWindowTitle\": \"Fshij ngjarjen\",\n  \"ariaSlotLabel\": \"Zgjedhur prej {0:t} deri {1:t}\",\n  \"ariaEventLabel\": \"{0} në {1:D} me {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"Je i sigurt që dëshiron ta fshish këtë ngjarje?\"\n  },\n  \"views\": {\n    \"day\": \"Ditë\",\n    \"week\": \"Javë\",\n    \"workWeek\": \"Javë punuese\",\n    \"agenda\": \"Rendi ditës\",\n    \"month\": \"Muaj\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Fshije artikullin e përsëritur\",\n    \"deleteWindowOccurrence\": \"Fshije dukurin e tanishme\",\n    \"deleteWindowSeries\": \"Fshini serinë\",\n    \"editWindowTitle\": \"Redakto artikullin e përsëritur\",\n    \"editWindowOccurrence\": \"Redakto dukurin e tanishme\",\n    \"editWindowSeries\": \"Redakto serinë\",\n    \"deleteRecurring\": \"Doni të fshini vetëm këtë dukuri apo të gjithë serinë?\",\n    \"editRecurring\": \"Doni të redaktoni vetëm këtë dukuri apo të gjithë serinë?\"\n  },\n  \"editor\": {\n    \"title\": \"Titulli\",\n    \"start\": \"Fillimi\",\n    \"end\": \"Përfundimi\",\n    \"allDayEvent\": \"Ngjarja gjithë ditës\",\n    \"description\": \"Përshkrimi\",\n    \"repeat\": \"Përsërit\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"Fillo zonën kohore\",\n    \"endTimezone\": \"Përfundo zonën kohore\",\n    \"separateTimezones\": \"Përdorni zona të ndara kohore të fillimit dhe përfundimit\",\n    \"timezoneEditorTitle\": \"Zonat kohore\",\n    \"timezoneEditorButton\": \"Zona kohore\",\n    \"timezoneTitle\": \"Zonat kohore\",\n    \"noTimezone\": \"Nuk ka zonë kohore\",\n    \"editorTitle\": \"Ngjarje\"\n  }\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"Të gjitha kufijtë\",\n  \"insideBorders\": \"Kufijtë e brendshëm\",\n  \"insideHorizontalBorders\": \"Kufijtë e brendshëm horizontal\",\n  \"insideVerticalBorders\": \"Kufijtë e brendshëm vertikal\",\n  \"outsideBorders\": \"Kufijtë e jashtme\",\n  \"leftBorder\": \"Kufiri i majtë\",\n  \"topBorder\": \"Kufiri lartë\",\n  \"rightBorder\": \"Kufiri i djathtë\",\n  \"bottomBorder\": \"Kufiri poshtë\",\n  \"noBorders\": \"Pa kufi\",\n  \"reset\": \"Rivendosni ngjyrën\",\n  \"customColor\": \"Ngjyra e personalizuar...\",\n  \"apply\": \"Apliko\",\n  \"cancel\": \"Anulo\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Apliko\",\n  \"save\": \"Ruaj\",\n  \"cancel\": \"Anulo\",\n  \"remove\": \"Fshije\",\n  \"retry\": \"Provo sërish\",\n  \"revert\": \"Ktheni\",\n  \"okText\": \"OK\",\n  \"formatCellsDialog\": {\n    \"title\": \"Formato\",\n    \"categories\": {\n      \"number\": \"Numri\",\n      \"currency\": \"Valuta\",\n      \"date\": \"Data\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Fonti\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Madhësia e fontit\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Kufijtë\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Radhitja\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Radhit majtas\",\n     \"justifyCenter\": \"Qendër\",\n     \"justifyRight\": \"Radhit djathtas\",\n     \"justifyFull\": \"Justifiko\",\n     \"alignTop\": \"Radhit lartë\",\n     \"alignMiddle\": \"Radhit në mes\",\n     \"alignBottom\": \"Radhit poshtë\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Bashko qelizat\",\n    \"buttons\": {\n      \"mergeCells\": \"Bashko të gjitha\",\n      \"mergeHorizontally\": \"Bashko horizontalisht\",\n      \"mergeVertically\": \"Bashko vertikalisht\",\n      \"unmerge\": \"Mos bashko\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Ngrirje e panelëve\",\n    \"buttons\": {\n      \"freezePanes\": \"Ngrirje e panelëve\",\n      \"freezeRows\": \"Ngrirje e rreshtave\",\n      \"freezeColumns\": \"Ngrirje e kolonave\",\n      \"unfreeze\": \"Zbllokim i panelëve\"\n    }\n  },\n  \"confirmationDialog\": {\n    \"text\": \"Je i sigurt që dëshiron ta heqësh këtë fletë?\",\n    \"title\": \"Heq fletën\"\n  },\n  \"validationDialog\": {\n    \"title\": \"Vlefshmëria e të dhënave\",\n    \"hintMessage\": \"Ju lutemi vendosni të vlefshme {0} vlerën {1}.\",\n    \"hintTitle\": \"Vlefshmëria {0}\",\n    \"criteria\": {\n      \"any\": \"Çdo vlerë\",\n      \"number\": \"Numri\",\n      \"text\": \"Teksti\",\n      \"date\": \"Data\",\n      \"custom\": \"Formula e personalizuar\",\n      \"list\": \"Lista\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"më e madhe se\",\n      \"lessThan\": \"më e vogël se\",\n      \"between\": \"ndërmjet\",\n      \"notBetween\": \"jo ndërmjet\",\n      \"equalTo\": \"e barabartë me\",\n      \"notEqualTo\": \"jo e barabartë me\",\n      \"greaterThanOrEqualTo\": \"më e madhe ose e barabartë me\",\n      \"lessThanOrEqualTo\": \"më e vogël ose e barabartë me\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"më e madhe se {0}\",\n      \"lessThan\": \"më e vogël se {0}\",\n      \"between\": \"ndërmjet {0} dhe {1}\",\n      \"notBetween\": \"jo ndërmjet {0} dhe {1}\",\n      \"equalTo\": \"e barabartë me {0}\",\n      \"notEqualTo\": \"jo e barabartë me {0}\",\n      \"greaterThanOrEqualTo\": \"më e madhe ose e barabartë me {0}\",\n      \"lessThanOrEqualTo\": \"më e vogël ose e barabartë me {0}\",\n      \"custom\": \"kjo plotëson formulën: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Kriteri\",\n      \"comparer\": \"Krahasimi\",\n      \"min\": \"Min\",\n      \"max\": \"Max\",\n      \"value\": \"Vlera\",\n      \"start\": \"Fillo\",\n      \"end\": \"Përfundo\",\n      \"onInvalidData\": \"Për të dhënat e pavlefshme\",\n      \"rejectInput\": \"Refuzo hyrjen\",\n      \"showWarning\": \"Trego paralajmërim\",\n      \"showHint\": \"Trego ndihmë\",\n      \"hintTitle\": \"Titulli ndihmës\",\n      \"hintMessage\": \"Porosia ndihmëse\",\n      \"ignoreBlank\": \"Refuzo bosh\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Shëno titullin\",\n      \"typeMessage\": \"Shëno porosinë\"\n    }\n  },\n  \"exportAsDialog\": {\n    \"title\": \"Eksporto...\",\n    \"labels\": {\n      \"fileName\": \"Emri i fajlit\",\n      \"saveAsType\": \"Ruaj si tip\",\n      \"exportArea\": \"Eksporto\",\n      \"paperSize\": \"Madhësia e letrës\",\n      \"margins\": \"Mbulesat\",\n      \"orientation\": \"Orientimi\",\n      \"print\": \"Printo\",\n      \"guidelines\": \"udhëzime\",\n      \"center\": \"Qendër\",\n      \"horizontally\": \"Horizontalisht\",\n      \"vertically\": \"Vertikalisht\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Nuk mund të ndryshojë pjesë të një qelize të bashkuar.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Kopjimi dhe ngjitja\",\n    \"errorMessage\": \"Këto veprime nuk mund të thirren përmes menusë. Ju lutemi përdorni shkurtesat e tastierës në vend:\",\n    \"labels\": {\n      \"forCopy\": \"për kopjim\",\n      \"forCut\": \"për prerje\",\n      \"forPaste\": \"për ngjitje\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"Ky veprim nuk mund të kryhet në përzgjedhje të shumëfishtë.\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Rendit vargjet prej A te Z\",\n  \"sortDescending\": \"Rendit vargjet prej Z te A\",\n  \"filterByValue\": \"Filtro sipas vlerës\",\n  \"filterByCondition\": \"Filtro sipas kushteve\",\n  \"apply\": \"Apliko\",\n  \"search\": \"Kërko\",\n  \"addToCurrent\": \"Shtoni në përzgjedhjen aktuale\",\n  \"clear\": \"Pastro\",\n  \"blanks\": \"(Boshllëqe)\",\n  \"operatorNone\": \"Asnjë\",\n  \"and\": \"DHE\",\n  \"or\": \"OSE\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"Teksti përmban\",\n      \"doesnotcontain\": \"Teksti nuk përmban\",\n      \"startswith\": \"Teksti fillon me\",\n      \"endswith\": \"Teksti përfundon me\"\n    },\n    \"date\": {\n      \"eq\":  \"Data është\",\n      \"neq\": \"Data nuk është\",\n      \"lt\":  \"Data është para\",\n      \"gt\":  \"Data është pas\"\n    },\n    \"number\": {\n      \"eq\": \"Është e barabartë me\",\n      \"neq\": \"Nuk është e barabartë me\",\n      \"gte\": \"Është më e madhe ose e barabartë me\",\n      \"gt\": \"Është më e madhe se\",\n      \"lte\": \"Është më e vogël ose e barabartë me\",\n      \"lt\": \"Është më e vogël se\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.colorPicker) {\nkendo.spreadsheet.messages.colorPicker =\n$.extend(true, kendo.spreadsheet.messages.colorPicker,{\n  \"reset\": \"Rivendosni ngjyrën\",\n  \"customColor\": \"Ngjyra e personalizuar...\",\n  \"apply\": \"Apliko\",\n  \"cancel\": \"Anulo\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Shto kolonë majtas\",\n  \"addColumnRight\": \"Shto kolonë djathtas\",\n  \"addRowAbove\": \"Shto rresht lartë\",\n  \"addRowBelow\": \"Shto rresht poshtë\",\n  \"alignment\": \"Radhitja\",\n  \"alignmentButtons\": {\n    \"justtifyLeft\": \"Radhit majtas\",\n    \"justifyCenter\": \"Qendër\",\n    \"justifyRight\": \"Radhit djathtas\",\n    \"justifyFull\": \"Justifiko\",\n    \"alignTop\": \"Radhit lartë\",\n    \"alignMiddle\": \"Radhit në mes\",\n    \"alignBottom\": \"Radhit poshtë\"\n  },\n  \"backgroundColor\": \"Prapavia\",\n  \"bold\": \"Bold\",\n  \"borders\": \"Kufijtë\",\n  \"colorPicker\": {\n    \"reset\": \"Rivendosni ngjyrë\",\n    \"customColor\": \"Ngjyra e personalizuar...\"\n  },\n  \"copy\": \"Kopjo\",\n  \"cut\": \"Prej\",\n  \"deleteColumn\": \"Fshij kolonën\",\n  \"deleteRow\": \"Fshij rreshtin\",\n  \"excelImport\": \"Import prej Excel...\",\n  \"filter\": \"Filtro\",\n  \"fontFamily\": \"Fonti\",\n  \"fontSize\": \"Madhësia e fontit\",\n  \"format\": \"Formati i personalizuar...\",\n  \"formatTypes\": {\n    \"automatic\": \"Automatik\",\n    \"number\": \"Numri\",\n    \"percent\": \"Perqindja\",\n    \"financial\": \"Financiar\",\n    \"currency\": \"Valuta\",\n    \"date\": \"Data\",\n    \"time\": \"Koha\",\n    \"dateTime\": \"Data koha\",\n    \"duration\": \"Kohëzgjatja\",\n    \"moreFormats\": \"Më shumë formate...\"\n  },\n  \"formatDecreaseDecimal\": \"Zbrit decimalin\",\n  \"formatIncreaseDecimal\": \"Ngrit decimalin\",\n  \"freeze\": \"Ngrirje e panelëve\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Ngrirje e panelëve\",\n    \"freezeRows\": \"Ngrirje e rreshtave\",\n    \"freezeColumns\": \"Ngrirje e kolonave\",\n    \"unfreeze\": \"Zbllokim i panelëve\"\n  },\n  \"italic\": \"Italic\",\n  \"merge\": \"Bashko qelizat\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Bashko të gjitha\",\n    \"mergeHorizontally\": \"Bashko horizontalisht\",\n    \"mergeVertically\": \"Bashko vertikalisht\",\n    \"unmerge\": \"Mos bashko\"\n  },\n  \"open\": \"Hape...\",\n  \"paste\": \"Ngjite\",\n  \"quickAccess\": {\n    \"redo\": \"Ribëj\",\n    \"undo\": \"Kthe\"\n  },\n  \"saveAs\": \"Ruaj si...\",\n  \"sortAsc\": \"Sorto (rritëse)\",\n  \"sortDesc\": \"Sorto (rrënëse)\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Sorto faqen prej A te Z\",\n    \"sortSheetDesc\": \"Sorto faqen prej Z te A\",\n    \"sortRangeAsc\": \"Sorto vargun prej A te Z\",\n    \"sortRangeDesc\": \"Sorto vargun prej Z te A\"\n  },\n  \"textColor\": \"Ngjyra e tekstit\",\n  \"textWrap\": \"Përfundo tekstin\",\n  \"underline\": \"Nënvizë\",\n  \"validation\": \"Vlefshmëria e të dhënave...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Nuk mund të futen qeliza për shkak të humbjes së të dhënave. Përzgjidhni një lokacion tjetër ose fshini të dhënat nga fundi i fletës.\",\n    \"filterRangeContainingMerges\": \"Nuk mund të krijojë një filtër brenda një vargu që përmban shkrirje\",\n    \"validationError\": \"Vlera që keni futur shkel rregullat e validimit të vendosura në qelizë.\"\n  },\n  \"tabs\": {\n    \"home\": \"Ballina\",\n    \"insert\": \"Vendos\",\n    \"data\": \"Të dhëna\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Ngrit\",\n  \"decreaseButtonTitle\": \"Zbrit\"\n});\n}\n\n/* ListBox messaages */\n\nif (kendo.ui.ListBox) {\nkendo.ui.ListBox.prototype.options.messages =\n$.extend(true, kendo.ui.ListBox.prototype.options.messages,{\n  \"tools\": {\n    \"remove\": \"Fshij\",\n    \"moveUp\": \"Zhvendos lartë\",\n    \"moveDown\": \"Zhvendos poshtë\",\n    \"transferTo\": \"Transfero Në\",\n    \"transferFrom\": \"Transfero Prej\",\n    \"transferAllTo\": \"Transfero Të Gjitha Në\",\n    \"transferAllFrom\": \"Transfero Të Gjitha Prej\"\n  }\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n  \"noRows\": \"Nuk ka të dhëna për tu shfaqur\",\n  \"loading\": \"Duke ngarkuar...\",\n  \"requestFailed\": \"Kërkesa dështoi.\",\n  \"retry\": \"Provo sërish\",\n  \"commands\": {\n      \"edit\": \"redaktoni\",\n      \"update\": \"Përditëso\",\n      \"canceledit\": \"Anulo\",\n      \"create\": \"Shto të dhënë të re\",\n      \"createchild\": \"Shto të dhënë fëmijë\",\n      \"destroy\": \"Fshij\",\n      \"excel\": \"Eksporto në Excel\",\n      \"pdf\": \"Exporto në PDF\"\n  }\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Duke ngarkuar...\",\n  \"requestFailed\": \"Kërkesa dështoi.\",\n  \"retry\": \"Provo sërish\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Zgjedh fajlet...\",\n  \"cancel\": \"Anulo\",\n  \"retry\": \"Provo sërish\",\n  \"remove\": \"Fshij\",\n  \"clearSelectedFiles\": \"Pastro\",\n  \"uploadSelectedFiles\": \"Ngarko fajlet\",\n  \"dropFilesHere\": \"lëshoni fajlet këtu për t'i ngarkuar\",\n  \"statusUploading\": \"duke ngarkuar\",\n  \"statusUploaded\": \"e ngarkuar\",\n  \"statusWarning\": \"paralajmërim\",\n  \"statusFailed\": \"dështoi\",\n  \"headerStatusUploading\": \"Duke ngarkuar...\",\n  \"headerStatusUploaded\": \"Përfundo\",\n  \"invalidMaxFileSize\": \"Madhësia e fajlit është shumë e madhe.\",\n  \"invalidMinFileSize\": \"Madhësia e fajlit është shumë e vogël.\",\n  \"invalidFileExtension\": \"Lloji i fajlit nuk lejohet.\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} nevojitet\",\n  \"pattern\": \"{0} nuk është e vlefshme\",\n  \"min\": \"{0} duhet të jetë më e madhe ose e barabartë me {1}\",\n  \"max\": \"{0} duhet të jetë më e vogël ose e barabartë me {1}\",\n  \"step\": \"{0} nuk është e vlefshme\",\n  \"email\": \"{0} emaili nuk është i vlefshëm\",\n  \"url\": \"{0} URL-ja nuk është e vlefshme\",\n  \"date\": \"{0} data nuk është e vlefshme\",\n  \"dateCompare\": \"Data e përfundimit duhet të jetë më e madhe ose e barabartë me datën e fillimit\"\n});\n}\n\n/* kendo.ui.progress method */\nif (kendo.ui.progress) {\nkendo.ui.progress.messages =\n$.extend(true, kendo.ui.progress.messages, {\n    loading: \"Duke ngarkuar...\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Mbylle\"\n});\n}\n\n/* Calendar */\nif (kendo.ui.Calendar) {\nkendo.ui.Calendar.prototype.options.messages =\n$.extend(true, kendo.ui.Calendar.prototype.options.messages, {\n  \"weekColumnHeader\": \"\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Anulo\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Anulo\"\n});\n}\n\n/* DateInput */\nif (kendo.ui.DateInput) {\n  kendo.ui.DateInput.prototype.options.messages =\n    $.extend(true, kendo.ui.DateInput.prototype.options.messages, {\n      \"year\": \"viti\",\n      \"month\": \"muaji\",\n      \"day\": \"dita\",\n      \"weekday\": \"dita e javës\",\n      \"hour\": \"orët\",\n      \"minute\": \"minutat\",\n      \"second\": \"sekondat\",\n      \"dayperiod\": \"AM/PM\"\n    });\n}\n\n})(window.kendo.jQuery);}));"]}