{"version": 3, "sources": ["messages/kendo.messages.ro-RO.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "enums", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "FilterMenu", "ColumnMenu", "messages", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "Editor", "backColor", "bold", "createLink", "deleteFile", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "directoryNotFound", "dropFilesHere", "emptyFolder", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "foreColor", "formatBlock", "imageAltText", "imageWebAddress", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "invalidFileType", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "orderBy", "orderByName", "orderBySize", "outdent", "overwriteFile", "search", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "uploadFile", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "formatting", "viewHtml", "dialogUpdate", "insertFile", "clear", "filter", "isFalse", "isTrue", "operator", "and", "info", "title", "or", "selectValue", "cancel", "value", "FilterMultiCheck", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "confirmation", "cancelDelete", "confirmDelete", "Groupable", "empty", "Pager", "allPages", "display", "itemsPerPage", "next", "page", "previous", "refresh", "morePages", "TreeListPager", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "showFullDay", "showWorkDay", "Upload", "localization", "remove", "retry", "statusFailed", "statusUploaded", "statusUploading", "uploadSelectedFiles", "headerStatusUploaded", "headerStatusUploading", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,UACNC,GAAM,OACNC,IAAO,mBACPC,GAAM,aACNC,IAAO,sBACPC,IAAO,cAETC,OACEN,GAAM,UACNK,IAAO,cAETE,QACEP,GAAM,UACNC,GAAM,iBACNC,IAAO,uBACPC,GAAM,gBACNC,IAAO,sBACPC,IAAO,cAETG,QACEC,SAAY,UACZC,eAAkB,aAClBC,SAAY,gBACZX,GAAM,UACNK,IAAO,aACPO,WAAc,gBAOdpB,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,UACNC,GAAM,OACNC,IAAO,mBACPC,GAAM,aACNC,IAAO,sBACPC,IAAO,cAETC,OACEN,GAAM,UACNK,IAAO,cAETE,QACEP,GAAM,UACNC,GAAM,iBACNC,IAAO,uBACPC,GAAM,gBACNC,IAAO,sBACPC,IAAO,cAETG,QACEC,SAAY,UACZC,eAAkB,aAClBC,SAAY,gBACZX,GAAM,UACNK,IAAO,aACPO,WAAc,gBAOdpB,MAAMC,GAAGqB,aACbtB,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,UACnDC,QAAW,UACXC,cAAiB,qBACjBC,eAAkB,sBAClBC,SAAY,iBACZC,KAAQ,QACRC,KAAQ,UACRC,OAAU,eAMR9B,MAAMC,GAAG8B,mBACb/B,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,UACzDS,OACEC,SAAY,UACZC,YAAe,iBAEjBC,KACEC,MAAS,OACTC,WAAc,gBACdC,MAAS,OACTC,MAAS,YACTC,GAAM,KACNC,YAAe,QAEjBC,aACEV,MAAS,SACTW,QAAW,QACXJ,MAAS,YACTK,OAAU,aACVC,OAAU,SAEZF,SACEG,IAAO,MACPb,SAAY,WACZC,YAAe,gBACfa,SAAY,cAEdC,iBACEC,MAAS,QACTC,OAAU,SACVC,KAAQ,OACRC,OAAU,SACVC,MAAS,SAEXT,QACEV,YAAe,qBACfa,SAAY,aACZd,SAAY,WAEdY,QACES,GAAM,KACNpB,YAAe,qBACfa,SAAY,aACZd,SAAY,WAEdsB,UACET,IAAO,MACPU,QAAW,UACXC,QAAW,kBAOXzD,MAAMC,GAAGyD,SACb1D,MAAMC,GAAGyD,OAAOvD,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyD,OAAOvD,UAAUC,QAAQmB,UAC/CoC,UAAa,iBACbC,KAAQ,OACRC,WAAc,sBACdC,WAAc,qCACdC,sBAAyB,MACzBC,aAAgB,WAChBC,aAAgB,YAChBC,kBAAqB,6CACrBC,cAAiB,0CACjBC,YAAe,YACfC,SAAY,gCACZC,gBAAmB,kBACnBC,SAAY,kCACZC,gBAAmB,yBACnBC,UAAa,UACbC,YAAe,SACfC,aAAgB,kBAChBC,gBAAmB,aACnBC,OAAU,YACVC,WAAc,iBACdC,YAAe,oBACfC,kBAAqB,2BACrBC,oBAAuB,6BACvBC,gBAAmB,iFACnBC,OAAU,SACVC,cAAiB,mBACjBC,YAAe,kBACfC,YAAe,4BACfC,aAAgB,6BAChBC,oBAAuB,yCACvBC,SAAY,OACZC,YAAe,UACfC,eAAkB,aAClBC,QAAW,iBACXC,YAAe,OACfC,YAAe,aACfC,QAAW,oBACXC,cAAiB,2FACjBC,OAAU,QACVC,cAAiB,QACjBC,OAAU,UACVC,UAAa,SACbC,YAAe,WACfC,UAAa,YACbC,OAAU,oBACVC,WAAc,gBACdC,YAAe,sBACfC,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,gBACfC,YAAe,gBACfC,aAAgB,iBAChBC,UAAa,cACbC,WAAc,SACdC,SAAY,YACZC,aAAgB,eAChBC,WAAc,sBAMZnH,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACnD6F,MAAS,SACTC,OAAU,YACVC,QAAW,YACXC,OAAU,gBACVC,SAAY,cAMVxH,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACnDkG,IAAO,KACPL,MAAS,SACTC,OAAU,YACVK,KAAQ,qBACRC,MAAS,oBACTL,QAAW,YACXC,OAAU,gBACVK,GAAM,MACNC,YAAe,yBACfC,OAAU,WACVN,SAAY,WACZO,MAAS,aAMP/H,MAAMC,GAAG+H,mBACbhI,MAAMC,GAAG+H,iBAAiB7H,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+H,iBAAiB7H,UAAUC,QAAQmB,UACzD0E,OAAU,WAMRjG,MAAMC,GAAGgI,OACbjI,MAAMC,GAAGgI,KAAK9H,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgI,KAAK9H,UAAUC,QAAQmB,UAC7C2G,UACEC,WAAc,WACdL,OAAU,wBACVM,OAAU,qBACVC,QAAW,SACXC,KAAQ,WACRC,MAAS,kBACTC,IAAO,gBACPC,KAAQ,wBACRC,OAAU,aACVC,OAAU,gBAEZC,UACEC,aAAgB,2CAChBC,aAAgB,WAChBC,cAAiB,aAOjB/I,MAAMC,GAAG+I,YACbhJ,MAAMC,GAAG+I,UAAU7I,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+I,UAAU7I,UAAUC,QAAQmB,UAClD0H,MAAS,oFAMPjJ,MAAMC,GAAGiJ,QACblJ,MAAMC,GAAGiJ,MAAM/I,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiJ,MAAM/I,UAAUC,QAAQmB,UAC9C4H,SAAY,MACZC,QAAW,6BACXH,MAAS,oCACThG,MAAS,eACToG,aAAgB,sBAChBlG,KAAQ,gBACRmG,KAAQ,mBACRhG,GAAM,UACNiG,KAAQ,SACRC,SAAY,oBACZC,QAAW,eACXC,UAAa,sBAMX1J,MAAMC,GAAG0J,gBACb3J,MAAMC,GAAG0J,cAAcxJ,UAAUC,QAAQmB,SACzCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0J,cAAcxJ,UAAUC,QAAQmB,UACtD4H,SAAY,MACZC,QAAW,6BACXH,MAAS,oCACThG,MAAS,eACToG,aAAgB,sBAChBlG,KAAQ,gBACRmG,KAAQ,mBACRhG,GAAM,UACNiG,KAAQ,SACRC,SAAY,oBACZC,QAAW,eACXC,UAAa,sBAMX1J,MAAMC,GAAG2J,YACb5J,MAAMC,GAAG2J,UAAUzJ,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,UAAUzJ,UAAUC,QAAQmB,UAClDsI,OAAU,aACV/B,OAAU,WACVc,UACEC,aAAgB,+CAElBtI,KAAQ,OACR8H,QAAW,SACXyB,QACEC,YAAe,gBACfC,YAAe,cACfC,YAAe,YACf9H,IAAO,MACP+H,YAAe,eACfC,OAAU,SACVC,kBAAqB,wCACrBC,MAAS,QACTC,cAAiB,iBACjBC,SAAY,IACZC,qBAAwB,WACxBC,oBAAuB,YACvB9C,MAAS,QACT+C,WAAc,eAEhBC,MAAS,QACTC,oBACEC,gBAAmB,wEACnBC,uBAA0B,4BAC1BC,mBAAsB,oBACtBC,kBAAqB,wBACrBC,cAAiB,sEACjBC,qBAAwB,0BACxBC,iBAAoB,kBACpBC,gBAAmB,uBAErB3C,KAAQ,WACR4C,KAAQ,OACRC,MAAS,SACTC,OACEC,OAAU,SACV1I,IAAO,KACP2I,MAAS,OACTC,KAAQ,YACRC,SAAY,sBAEdX,kBAAqB,eACrBY,YAAe,gBACfC,YAAe,yBAMb7L,MAAMC,GAAG6L,SACb9L,MAAMC,GAAG6L,OAAO3L,UAAUC,QAAQ2L,aAClCjM,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6L,OAAO3L,UAAUC,QAAQ2L,cAC/CjE,OAAU,WACV3D,cAAiB,0CACjB6H,OAAU,UACVC,MAAS,mBACTvD,OAAU,gBACVwD,aAAgB,QAChBC,eAAkB,WAClBC,gBAAmB,UACnBC,oBAAuB,oBACvBC,qBAAwB,OACxBC,sBAAyB,kBAMvBvM,MAAMC,GAAGuM,SACbxM,MAAMC,GAAGuM,OAAOrM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuM,OAAOrM,UAAUC,QAAQ2L,cAC/CU,MAAS,aAMPzM,MAAMC,GAAGyM,QACb1M,MAAMC,GAAGyM,MAAMvM,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyM,MAAMvM,UAAUC,QAAQ2L,cAC9CY,OAAU,SAMR3M,MAAMC,GAAG2M,UACb5M,MAAMC,GAAG2M,QAAQzM,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,QAAQzM,UAAUC,QAAQ2L,cAChDY,OAAU,MACV7E,OAAU,cAKR9H,MAAMC,GAAG4M,SACb7M,MAAMC,GAAG4M,OAAO1M,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4M,OAAO1M,UAAUC,QAAQ2L,cAC/CY,OAAU,MACV7E,OAAU,eAITgF,OAAO9M,MAAM+M", "file": "kendo.messages.ro-RO.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Egal cu\",\n    \"gt\": \"Dup<PERSON>\",\n    \"gte\": \"După sau egal cu\",\n    \"lt\": \"Înainte de\",\n    \"lte\": \"Înainte sau egal cu\",\n    \"neq\": \"Diferit de\"\n  },\n  \"enums\": {\n    \"eq\": \"Egal cu\",\n    \"neq\": \"Diferit de\"\n  },\n  \"number\": {\n    \"eq\": \"Egal cu\",\n    \"gt\": \"Mai mare decât\",\n    \"gte\": \"Mai mare sau egal cu\",\n    \"lt\": \"Mai mic decât\",\n    \"lte\": \"Mai mic sau egal cu\",\n    \"neq\": \"Diferit de\"\n  },\n  \"string\": {\n    \"contains\": \"Conține\",\n    \"doesnotcontain\": \"Nu conține\",\n    \"endswith\": \"Se termină cu\",\n    \"eq\": \"Egal cu\",\n    \"neq\": \"Diferit de\",\n    \"startswith\": \"Începe cu\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Egal cu\",\n    \"gt\": \"După\",\n    \"gte\": \"După sau egal cu\",\n    \"lt\": \"Înainte de\",\n    \"lte\": \"Înainte sau egal cu\",\n    \"neq\": \"Diferit de\"\n  },\n  \"enums\": {\n    \"eq\": \"Egal cu\",\n    \"neq\": \"Diferit de\"\n  },\n  \"number\": {\n    \"eq\": \"Egal cu\",\n    \"gt\": \"Mai mare decât\",\n    \"gte\": \"Mai mare sau egal cu\",\n    \"lt\": \"Mai mic decât\",\n    \"lte\": \"Mai mic sau egal cu\",\n    \"neq\": \"Diferit de\"\n  },\n  \"string\": {\n    \"contains\": \"Conține\",\n    \"doesnotcontain\": \"Nu conține\",\n    \"endswith\": \"Se termină cu\",\n    \"eq\": \"Egal cu\",\n    \"neq\": \"Diferit de\",\n    \"startswith\": \"Începe cu\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Coloane\",\n  \"sortAscending\": \"Sortare ascendentă\",\n  \"sortDescending\": \"Sortare descendentă\",\n  \"settings\": \"Setări coloană\",\n  \"done\": \"Făcut\",\n  \"lock\": \"Blocare\",\n  \"unlock\": \"Deblocare\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"zi/zile\",\n    \"repeatEvery\": \"Repeat every:\"\n  },\n  \"end\": {\n    \"after\": \"După\",\n    \"occurrence\": \"occurrence(s)\",\n    \"label\": \"End:\",\n    \"never\": \"Niciodată\",\n    \"on\": \"On\",\n    \"mobileLabel\": \"Ends\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Zilnic\",\n    \"monthly\": \"Lunar\",\n    \"never\": \"Niciodată\",\n    \"weekly\": \"Saptamanal\",\n    \"yearly\": \"Anual\"\n  },\n  \"monthly\": {\n    \"day\": \"Day\",\n    \"interval\": \"month(s)\",\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"first\",\n    \"fourth\": \"fourth\",\n    \"last\": \"last\",\n    \"second\": \"second\",\n    \"third\": \"third\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Repetă în fiecare:\",\n    \"repeatOn\": \"Repetă la:\",\n    \"interval\": \"week(s)\"\n  },\n  \"yearly\": {\n    \"of\": \"of\",\n    \"repeatEvery\": \"Repetă în fiecare:\",\n    \"repeatOn\": \"Repetă la:\",\n    \"interval\": \"year(s)\"\n  },\n  \"weekdays\": {\n    \"day\": \"day\",\n    \"weekday\": \"weekday\",\n    \"weekend\": \"weekend day\"\n  }\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"backColor\": \"Culoare fundal\",\n  \"bold\": \"Bold\",\n  \"createLink\": \"Inserează hyperlink\",\n  \"deleteFile\": \"Sigur doriți să ștergeți \\\" {0} \\\" ?\",\n  \"dialogButtonSeparator\": \"sau\",\n  \"dialogCancel\": \"Anulează\",\n  \"dialogInsert\": \"Inserează\",\n  \"directoryNotFound\": \"Nu a fost găsit un director cu acest nume.\",\n  \"dropFilesHere\": \"plasați fișierele aici pentru încărcare\",\n  \"emptyFolder\": \"Dosar gol\",\n  \"fontName\": \"Selectează familia de fonturi\",\n  \"fontNameInherit\": \"(font moștenit)\",\n  \"fontSize\": \"Selectează dimensiunea fontului\",\n  \"fontSizeInherit\": \"(dimensiune moștenită)\",\n  \"foreColor\": \"Culoare\",\n  \"formatBlock\": \"Format\",\n  \"imageAltText\": \"Text alternativ\",\n  \"imageWebAddress\": \"Adresă Web\",\n  \"indent\": \"Indentare\",\n  \"insertHtml\": \"Inserează HTML\",\n  \"insertImage\": \"Inserează imagine\",\n  \"insertOrderedList\": \"Inserează listă ordonată\",\n  \"insertUnorderedList\": \"Inserează listă neordonată\",\n  \"invalidFileType\": \"Fișierul selectat \\\"{0}\\\" nu este valid. Tipurile de fișiere acceptate sunt {1}.\",\n  \"italic\": \"Cursiv\",\n  \"justifyCenter\": \"Centrează textul\",\n  \"justifyFull\": \"Aliniază textul\",\n  \"justifyLeft\": \"Aliniază textul la stânga\",\n  \"justifyRight\": \"Aliniază textul la dreapta\",\n  \"linkOpenInNewWindow\": \"Deschide link-ul într-o fereastră nouă\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"Indiciu\",\n  \"linkWebAddress\": \"Adresă Web\",\n  \"orderBy\": \"Ordonare după:\",\n  \"orderByName\": \"Nume\",\n  \"orderBySize\": \"Dimensiune\",\n  \"outdent\": \"Anulare indentare\",\n  \"overwriteFile\": \"Există deja un fișier cu numele \\\" {0} \\\" în directorul curent. Doriți să-l suprascrieți ?\",\n  \"search\": \"Caută\",\n  \"strikethrough\": \"Tăiat\",\n  \"styles\": \"Stiluri\",\n  \"subscript\": \"Indice\",\n  \"superscript\": \"Exponent\",\n  \"underline\": \"Subliniat\",\n  \"unlink\": \"Elimină hyperlink\",\n  \"uploadFile\": \"Upload fișier\",\n  \"createTable\": \"Inserează tabelelor\",\n  \"addColumnLeft\": \"Add column on the left\",\n  \"addColumnRight\": \"Add column on the right\",\n  \"addRowAbove\": \"Add row above\",\n  \"addRowBelow\": \"Add row below\",\n  \"deleteColumn\": \"Șterge coloană\",\n  \"deleteRow\": \"Șterge rand\",\n  \"formatting\": \"Format\",\n  \"viewHtml\": \"View HTML\",\n  \"dialogUpdate\": \"Actualizează\",\n  \"insertFile\": \"Inserează fisier\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"clear\": \"Șterge\",\n  \"filter\": \"Filtrează\",\n  \"isFalse\": \"este fals\",\n  \"isTrue\": \"este adevărat\",\n  \"operator\": \"Operator\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"and\": \"Și\",\n  \"clear\": \"Șterge\",\n  \"filter\": \"Filtrează\",\n  \"info\": \"Criterii filtrare:\",\n  \"title\": \"Criterii filtrare\",\n  \"isFalse\": \"este fals\",\n  \"isTrue\": \"este adevărat\",\n  \"or\": \"Sau\",\n  \"selectValue\": \"- Selectează valoare -\",\n  \"cancel\": \"Anulează\",\n  \"operator\": \"Operator\",\n  \"value\": \"Valoare\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"Caută\"\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"canceledit\": \"Anulează\",\n    \"cancel\": \"Anulează modificările\",\n    \"create\": \"Adaugă element nou\",\n    \"destroy\": \"Șterge\",\n    \"edit\": \"Modifică\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"save\": \"Salvează modificările\",\n    \"select\": \"Selectează\",\n    \"update\": \"Actualizează\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Sigur doriți să ștergeți acest element ?\",\n    \"cancelDelete\": \"Anulează\",\n    \"confirmDelete\": \"Șterge\"\n  }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Trageți un antet de coloană și plasați-l aici pentru a grupa după acea coloană\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} din {2} elemente\",\n  \"empty\": \"Nu există elemente pentru afișare\",\n  \"first\": \"Prima pagină\",\n  \"itemsPerPage\": \"elemente per pagină\",\n  \"last\": \"Ultima pagină\",\n  \"next\": \"Pagina următoare\",\n  \"of\": \"din {0}\",\n  \"page\": \"Pagina\",\n  \"previous\": \"Pagina precedentă\",\n  \"refresh\": \"Actualizează\",\n  \"morePages\": \"Mai multe pagini\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} din {2} elemente\",\n  \"empty\": \"Nu există elemente pentru afișare\",\n  \"first\": \"Prima pagină\",\n  \"itemsPerPage\": \"elemente per pagină\",\n  \"last\": \"Ultima pagină\",\n  \"next\": \"Pagina următoare\",\n  \"of\": \"din {0}\",\n  \"page\": \"Pagina\",\n  \"previous\": \"Pagina precedentă\",\n  \"refresh\": \"Actualizează\",\n  \"morePages\": \"Mai multe pagini\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"toată ziua\",\n  \"cancel\": \"Anulează\",\n  \"editable\": {\n    \"confirmation\": \"Are you sure you want to delete this event?\"\n  },\n  \"date\": \"Data\",\n  \"destroy\": \"Șterge\",\n  \"editor\": {\n    \"allDayEvent\": \"All day event\",\n    \"description\": \"Description\",\n    \"editorTitle\": \"Eveniment\",\n    \"end\": \"End\",\n    \"endTimezone\": \"End timezone\",\n    \"repeat\": \"Repeat\",\n    \"separateTimezones\": \"Use separate start and end time zones\",\n    \"start\": \"Start\",\n    \"startTimezone\": \"Start timezone\",\n    \"timezone\": \" \",\n    \"timezoneEditorButton\": \"Fus orar\",\n    \"timezoneEditorTitle\": \"Timezones\",\n    \"title\": \"Title\",\n    \"noTimezone\": \"No timezone\"\n  },\n  \"event\": \"Event\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Do you want to delete only this event occurrence or the whole series?\",\n    \"deleteWindowOccurrence\": \"Delete current occurrence\",\n    \"deleteWindowSeries\": \"Delete the series\",\n    \"deleteWindowTitle\": \"Delete Recurring Item\",\n    \"editRecurring\": \"Do you want to edit only this event occurrence or the whole series?\",\n    \"editWindowOccurrence\": \"Edit current occurrence\",\n    \"editWindowSeries\": \"Edit the series\",\n    \"editWindowTitle\": \"Edit Recurring Item\"\n  },\n  \"save\": \"Salvează\",\n  \"time\": \"Timp\",\n  \"today\": \"Astăzi\",\n  \"views\": {\n    \"agenda\": \"Agendă\",\n    \"day\": \"Zi\",\n    \"month\": \"Lună\",\n    \"week\": \"Săptămână\",\n    \"workWeek\": \"Săptămână de lucru\"\n  },\n  \"deleteWindowTitle\": \"Delete event\",\n  \"showFullDay\": \"Show full day\",\n  \"showWorkDay\": \"Show business hours\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Anulează\",\n  \"dropFilesHere\": \"plasați fișierele aici pentru încărcare\",\n  \"remove\": \"Elimină\",\n  \"retry\": \"Incearcă din nou\",\n  \"select\": \"Selectează...\",\n  \"statusFailed\": \"eșuat\",\n  \"statusUploaded\": \"încărcat\",\n  \"statusUploading\": \"încarcă\",\n  \"uploadSelectedFiles\": \"Încărcă fișierele\",\n  \"headerStatusUploaded\": \"Done\",\n  \"headerStatusUploading\": \"Uploading...\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Închide\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"O.K\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"O.K\",\n  \"cancel\": \"Anulează\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"O.K\",\n  \"cancel\": \"Anulează\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}