{"version": 3, "sources": ["messages/kendo.messages.sv-SE.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "enums", "FilterMultiCheck", "messages", "checkAll", "clear", "filter", "search", "selectedItemsFormat", "FilterMenu", "ColumnMenu", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "isFalse", "isTrue", "operator", "and", "info", "title", "or", "selectValue", "cancel", "value", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "confirmation", "cancelDelete", "confirmDelete", "Groupable", "empty", "Pager", "allPages", "display", "itemsPerPage", "next", "page", "previous", "refresh", "morePages", "TreeListPager", "Upload", "localization", "retry", "remove", "uploadSelectedFiles", "dropFilesHere", "statusFailed", "statusUploaded", "statusUploading", "headerStatusUploaded", "headerStatusUploading", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "deleteFile", "directoryNotFound", "emptyFolder", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "backColor", "foreColor", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "imageWebAddress", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "formatting", "viewHtml", "dialogUpdate", "insertFile", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "showFullDay", "showWorkDay", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,cACNC,GAAM,eACNC,IAAO,0BACPC,GAAM,iBACNC,IAAO,4BACPC,IAAO,oBAETC,QACEN,GAAM,cACNC,GAAM,eACNC,IAAO,0BACPC,GAAM,eACNC,IAAO,0BACPC,IAAO,oBAETE,QACEC,SAAY,aACZC,eAAkB,kBAClBC,SAAY,aACZV,GAAM,cACNK,IAAO,mBACPM,WAAc,cAEhBC,OACEZ,GAAM,cACNK,IAAO,uBAOPb,MAAMC,GAAGoB,mBACXrB,MAAMC,GAAGoB,iBAAiBlB,UAAUC,QAAQkB,SACxCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,iBAAiBlB,UAAUC,QAAQkB,UACvDC,SAAY,eACZC,MAAS,QACTC,OAAU,WACVC,OAAU,MACVC,oBAAuB,yBAM7B3B,MAAMC,GAAG2B,aACb5B,MAAMC,GAAG2B,WAAWzB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2B,WAAWzB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,cACNC,GAAM,eACNC,IAAO,0BACPC,GAAM,iBACNC,IAAO,4BACPC,IAAO,oBAETC,QACEN,GAAM,cACNC,GAAM,eACNC,IAAO,0BACPC,GAAM,eACNC,IAAO,0BACPC,IAAO,oBAETE,QACEC,SAAY,aACZC,eAAkB,kBAClBC,SAAY,aACZV,GAAM,cACNK,IAAO,mBACPM,WAAc,cAEhBC,OACEZ,GAAM,cACNK,IAAO,uBAOPb,MAAMC,GAAG4B,aACb7B,MAAMC,GAAG4B,WAAW1B,UAAUC,QAAQkB,SACtCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4B,WAAW1B,UAAUC,QAAQkB,UACnDQ,QAAW,WACXC,cAAiB,mBACjBC,eAAkB,mBAClBC,SAAY,sBACZC,KAAQ,OACRC,KAAQ,MACRC,OAAU,aAMRpC,MAAMC,GAAGoC,mBACbrC,MAAMC,GAAGoC,iBAAiBlC,UAAUC,QAAQkB,SAC5CxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoC,iBAAiBlC,UAAUC,QAAQkB,UACzDgB,OACEC,SAAY,UACZC,YAAe,iBAEjBC,KACEC,MAAS,QACTC,WAAc,gBACdC,MAAS,OACTC,MAAS,QACTC,GAAM,KACNC,YAAe,QAEjBC,aACEV,MAAS,QACTW,QAAW,UACXJ,MAAS,QACTK,OAAU,SACVC,OAAU,UAEZF,SACEG,IAAO,MACPb,SAAY,WACZC,YAAe,gBACfa,SAAY,cAEdC,iBACEC,MAAS,QACTC,OAAU,SACVC,KAAQ,OACRC,OAAU,SACVC,MAAS,SAEXT,QACEV,YAAe,gBACfa,SAAY,aACZd,SAAY,WAEdY,QACES,GAAM,KACNpB,YAAe,gBACfa,SAAY,aACZd,SAAY,WAEdsB,UACET,IAAO,MACPU,QAAW,UACXC,QAAW,kBAOX/D,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQkB,SACtCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQkB,UACnDE,MAAS,QACTC,OAAU,WACVuC,QAAW,YACXC,OAAU,UACVC,SAAY,cAMVlE,MAAMC,GAAG2B,aACb5B,MAAMC,GAAG2B,WAAWzB,UAAUC,QAAQkB,SACtCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2B,WAAWzB,UAAUC,QAAQkB,UACnD6C,IAAO,MACP3C,MAAS,QACTC,OAAU,WACV2C,KAAQ,yBACRC,MAAS,wBACTL,QAAW,YACXC,OAAU,UACVK,GAAM,QACNC,YAAe,SACfC,OAAU,SACVN,SAAY,WACZO,MAAS,WAMPzE,MAAMC,GAAGyE,OACb1E,MAAMC,GAAGyE,KAAKvE,UAAUC,QAAQkB,SAChCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyE,KAAKvE,UAAUC,QAAQkB,UAC7CqD,UACEC,WAAc,SACdJ,OAAU,mBACVK,OAAU,iBACVC,QAAW,SACXC,KAAQ,QACRC,MAAS,kBACTC,IAAO,gBACPC,KAAQ,kBACRC,OAAU,OACVC,OAAU,SAEZC,UACEC,aAAgB,gDAChBC,aAAgB,SAChBC,cAAiB,aAOjBxF,MAAMC,GAAGwF,YACbzF,MAAMC,GAAGwF,UAAUtF,UAAUC,QAAQkB,SACrCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwF,UAAUtF,UAAUC,QAAQkB,UAClDoE,MAAS,6DAMP1F,MAAMC,GAAG0F,QACb3F,MAAMC,GAAG0F,MAAMxF,UAAUC,QAAQkB,SACjCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0F,MAAMxF,UAAUC,QAAQkB,UAC9CsE,SAAY,OACZC,QAAW,0BACXH,MAAS,wBACTnC,MAAS,uBACTuC,aAAgB,kBAChBrC,KAAQ,sBACRsC,KAAQ,qBACRnC,GAAM,SACNoC,KAAQ,OACRC,SAAY,0BACZC,QAAW,YACXC,UAAa,gBAMXnG,MAAMC,GAAGmG,gBACbpG,MAAMC,GAAGmG,cAAcjG,UAAUC,QAAQkB,SACzCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmG,cAAcjG,UAAUC,QAAQkB,UACtDsE,SAAY,OACZC,QAAW,0BACXH,MAAS,wBACTnC,MAAS,uBACTuC,aAAgB,kBAChBrC,KAAQ,sBACRsC,KAAQ,qBACRnC,GAAM,SACNoC,KAAQ,OACRC,SAAY,0BACZC,QAAW,YACXC,UAAa,gBAMXnG,MAAMC,GAAGoG,SACbrG,MAAMC,GAAGoG,OAAOlG,UAAUC,QAAQkG,aAClCxG,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoG,OAAOlG,UAAUC,QAAQkG,cAC/C9B,OAAU,SACV+B,MAAS,cACTpB,OAAU,UACVqB,OAAU,UACVC,oBAAuB,kBACvBC,cAAiB,oCACjBC,aAAgB,eAChBC,eAAkB,YAClBC,gBAAmB,aACnBC,qBAAwB,YACxBC,sBAAyB,mBAMvB/G,MAAMC,GAAG+G,SACbhH,MAAMC,GAAG+G,OAAO7G,UAAUC,QAAQkB,SAClCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+G,OAAO7G,UAAUC,QAAQkB,UAC/C2F,KAAQ,MACRC,WAAc,iBACdC,SAAY,gBACZC,gBAAmB,kBACnBC,SAAY,eACZC,gBAAmB,iBACnBC,YAAe,cACfC,OAAU,aACVC,WAAc,iBACdC,YAAe,iBACfC,kBAAqB,2BACrBC,oBAAuB,uBACvBC,OAAU,SACVC,cAAiB,iBACjBC,YAAe,wBACfC,YAAe,uBACfC,aAAgB,qBAChBC,QAAW,gBACXC,cAAiB,eACjBC,OAAU,OACVC,UAAa,WACbC,YAAe,UACfC,UAAa,eACbC,OAAU,eACVC,WAAc,2CACdC,kBAAqB,sCACrBC,YAAe,WACfC,gBAAmB,wDACnBC,QAAW,cACXC,YAAe,OACfC,YAAe,UACfC,cAAiB,+EACjBC,WAAc,YACdC,UAAa,gBACbC,UAAa,OACbzC,cAAiB,oCACjB0C,sBAAyB,QACzBC,aAAgB,SAChBC,aAAgB,YAChBC,aAAgB,kBAChBC,gBAAmB,aACnBC,oBAAuB,gCACvBC,SAAY,OACZC,YAAe,YACfC,eAAkB,aAClBlI,OAAU,MACVmI,YAAe,eACfC,cAAiB,0BACjBC,eAAkB,wBAClBC,YAAe,wBACfC,YAAe,sBACfC,aAAgB,iBAChBC,UAAa,cACbC,WAAc,SACdC,SAAY,YACZC,aAAgB,YAChBC,WAAc,cAMZvK,MAAMC,GAAGuK,YACbxK,MAAMC,GAAGuK,UAAUrK,UAAUC,QAAQkB,SACrCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuK,UAAUrK,UAAUC,QAAQkB,UAClDmJ,OAAU,aACVjG,OAAU,SACVa,UACEC,aAAgB,kDAElB/E,KAAQ,QACRuE,QAAW,UACX4F,QACEC,YAAe,mBACfC,YAAe,cACfC,YAAe,YACfpI,IAAO,OACPqI,YAAe,cACfC,OAAU,UACVC,kBAAsB,0CACtBC,MAAS,QACTC,cAAiB,eACjBC,SAAY,IACZC,qBAAwB,UACxBC,oBAAuB,YACvBhH,MAAS,QACTiH,WAAc,iBAEhBC,MAAS,YACTC,oBACEC,gBAAmB,6DACnBC,uBAA0B,+BAC1BC,mBAAsB,iBACtBC,kBAAqB,8BACrBC,cAAiB,6DACjBC,qBAAwB,kCACxBC,iBAAoB,iBACpBC,gBAAmB,gCAErB9G,KAAQ,QACR+G,KAAQ,MACRC,MAAS,OACTC,OACEC,OAAU,SACVhJ,IAAO,MACPiJ,MAAS,QACTC,KAAQ,QACRC,SAAY,eAEdX,kBAAqB,oBACrBY,YAAe,cACfC,YAAe,oBAMbzM,MAAMC,GAAGyM,SACb1M,MAAMC,GAAGyM,OAAOvM,UAAUC,QAAQkB,SAClCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyM,OAAOvM,UAAUC,QAAQkG,cAC/CqG,MAAS,WAMP3M,MAAMC,GAAG2M,QACb5M,MAAMC,GAAG2M,MAAMzM,UAAUC,QAAQkB,SACjCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,MAAMzM,UAAUC,QAAQkG,cAC9CuG,OAAU,QAMR7M,MAAMC,GAAG6M,UACb9M,MAAMC,GAAG6M,QAAQ3M,UAAUC,QAAQkB,SACnCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6M,QAAQ3M,UAAUC,QAAQkG,cAChDuG,OAAU,KACVrI,OAAU,YAKRxE,MAAMC,GAAG8M,SACb/M,MAAMC,GAAG8M,OAAO5M,UAAUC,QAAQkB,SAClCxB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8M,OAAO5M,UAAUC,QAAQkG,cAC/CuG,OAAU,KACVrI,OAAU,aAITwI,OAAOhN,MAAMiN", "file": "kendo.messages.sv-SE.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Är lika med\",\n    \"gt\": \"Är senare än\",\n    \"gte\": \"Är lika eller senare än\",\n    \"lt\": \"Är tidigare än\",\n    \"lte\": \"Är lika eller tidigare än\",\n    \"neq\": \"Är inte lika med\"\n  },\n  \"number\": {\n    \"eq\": \"Är lika med\",\n    \"gt\": \"Är större än\",\n    \"gte\": \"Är lika eller större än\",\n    \"lt\": \"Är mindre än\",\n    \"lte\": \"Är lika eller mindre än\",\n    \"neq\": \"Är inte lika med\"\n  },\n  \"string\": {\n    \"contains\": \"Innehåller\",\n    \"doesnotcontain\": \"Innehåller inte\",\n    \"endswith\": \"Slutar med\",\n    \"eq\": \"Är lika med\",\n    \"neq\": \"Är inte lika med\",\n    \"startswith\": \"Börjar med\"\n  },\n  \"enums\": {\n    \"eq\": \"Är lika med\",\n    \"neq\": \"Är inte lika med\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\n  kendo.ui.FilterMultiCheck.prototype.options.messages =\n      $.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages, {\n          \"checkAll\": \"Markera alla\",\n          \"clear\": \"Rensa\",\n          \"filter\": \"Filtrera\",\n          \"search\": \"Sök\",\n          \"selectedItemsFormat\": \"{0} alternativ valt\"\n      });\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Är lika med\",\n    \"gt\": \"Är senare än\",\n    \"gte\": \"Är lika eller senare än\",\n    \"lt\": \"Är tidigare än\",\n    \"lte\": \"Är lika eller tidigare än\",\n    \"neq\": \"Är inte lika med\"\n  },\n  \"number\": {\n    \"eq\": \"Är lika med\",\n    \"gt\": \"Är större än\",\n    \"gte\": \"Är lika eller större än\",\n    \"lt\": \"Är mindre än\",\n    \"lte\": \"Är lika eller mindre än\",\n    \"neq\": \"Är inte lika med\"\n  },\n  \"string\": {\n    \"contains\": \"Innehåller\",\n    \"doesnotcontain\": \"Innehåller inte\",\n    \"endswith\": \"Slutar med\",\n    \"eq\": \"Är lika med\",\n    \"neq\": \"Är inte lika med\",\n    \"startswith\": \"Börjar med\"\n  },\n  \"enums\": {\n    \"eq\": \"Är lika med\",\n    \"neq\": \"Är inte lika med\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Kolumner\",\n  \"sortAscending\": \"Sortera stigande\",\n  \"sortDescending\": \"Sortera fallande\",\n  \"settings\": \"Kolumninställningar\",\n  \"done\": \"Klar\",\n  \"lock\": \"Lås\",\n  \"unlock\": \"Lås upp\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"days(s)\",\n    \"repeatEvery\": \"Repeat every:\"\n  },\n  \"end\": {\n    \"after\": \"After\",\n    \"occurrence\": \"occurrence(s)\",\n    \"label\": \"End:\",\n    \"never\": \"Never\",\n    \"on\": \"On\",\n    \"mobileLabel\": \"Ends\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Daily\",\n    \"monthly\": \"Monthly\",\n    \"never\": \"Never\",\n    \"weekly\": \"Weekly\",\n    \"yearly\": \"Yearly\"\n  },\n  \"monthly\": {\n    \"day\": \"Day\",\n    \"interval\": \"month(s)\",\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"first\",\n    \"fourth\": \"fourth\",\n    \"last\": \"last\",\n    \"second\": \"second\",\n    \"third\": \"third\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\",\n    \"interval\": \"week(s)\"\n  },\n  \"yearly\": {\n    \"of\": \"of\",\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\",\n    \"interval\": \"year(s)\"\n  },\n  \"weekdays\": {\n    \"day\": \"day\",\n    \"weekday\": \"weekday\",\n    \"weekend\": \"weekend day\"\n  }\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"clear\": \"Rensa\",\n  \"filter\": \"Filtrera\",\n  \"isFalse\": \"är falskt\",\n  \"isTrue\": \"är sant\",\n  \"operator\": \"Operatör\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"and\": \"Och\",\n  \"clear\": \"Rensa\",\n  \"filter\": \"Filtrera\",\n  \"info\": \"Visa poster med värde:\",\n  \"title\": \"Visa poster med värde\",\n  \"isFalse\": \"är falskt\",\n  \"isTrue\": \"är sant\",\n  \"or\": \"Eller\",\n  \"selectValue\": \"-Välj-\",\n  \"cancel\": \"Avbryt\",\n  \"operator\": \"Operatör\",\n  \"value\": \"Värde\"\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"canceledit\": \"Avbryt\",\n    \"cancel\": \"Avbryt ändringar\",\n    \"create\": \"Lägg till post\",\n    \"destroy\": \"Radera\",\n    \"edit\": \"Ändra\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"save\": \"Spara ändringar\",\n    \"select\": \"Välj\",\n    \"update\": \"Spara\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Är du säker på att du vill radera denna post?\",\n    \"cancelDelete\": \"Avbryt\",\n    \"confirmDelete\": \"Radera\"\n  }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Dra en kolumnrubrik hit för att sortera på den kolumnen\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Alla\",\n  \"display\": \"{0} - {1} av {2} poster\",\n  \"empty\": \"Det finns inga poster\",\n  \"first\": \"Gå till första sidan\",\n  \"itemsPerPage\": \"poster per sida\",\n  \"last\": \"Gå till sista sidan\",\n  \"next\": \"Gå till nästa sida\",\n  \"of\": \"av {0}\",\n  \"page\": \"Sida\",\n  \"previous\": \"Gå till föregående sida\",\n  \"refresh\": \"Uppdatera\",\n  \"morePages\": \"Fler sidor\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"Alla\",\n  \"display\": \"{0} - {1} av {2} poster\",\n  \"empty\": \"Det finns inga poster\",\n  \"first\": \"Gå till första sidan\",\n  \"itemsPerPage\": \"poster per sida\",\n  \"last\": \"Gå till sista sidan\",\n  \"next\": \"Gå till nästa sida\",\n  \"of\": \"av {0}\",\n  \"page\": \"Sida\",\n  \"previous\": \"Gå till föregående sida\",\n  \"refresh\": \"Uppdatera\",\n  \"morePages\": \"Fler sidor\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Avbryt\",\n  \"retry\": \"Försök igen\",\n  \"select\": \"Välj...\",\n  \"remove\": \"Ta bort\",\n  \"uploadSelectedFiles\": \"Ladda upp filer\",\n  \"dropFilesHere\": \"släpp filer här för att ladda upp\",\n  \"statusFailed\": \"misslyckades\",\n  \"statusUploaded\": \"uppladdad\",\n  \"statusUploading\": \"laddar upp\",\n  \"headerStatusUploaded\": \"Uppladdad\",\n  \"headerStatusUploading\": \"Laddar upp...\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Fet\",\n  \"createLink\": \"Lägg till länk\",\n  \"fontName\": \"Välj typsnitt\",\n  \"fontNameInherit\": \"(ärvt typsnitt)\",\n  \"fontSize\": \"Välj storlek\",\n  \"fontSizeInherit\": \"(ärvd storlek)\",\n  \"formatBlock\": \"Formatering\",\n  \"indent\": \"Öka indrag\",\n  \"insertHtml\": \"Lägg till HTML\",\n  \"insertImage\": \"Lägg till bild\",\n  \"insertOrderedList\": \"Lägg till numrerad lista\",\n  \"insertUnorderedList\": \"Lägg till punktlista\",\n  \"italic\": \"Kursiv\",\n  \"justifyCenter\": \"Centrerad text\",\n  \"justifyFull\": \"Marginaljusterad text\",\n  \"justifyLeft\": \"Vänsterjusterad text\",\n  \"justifyRight\": \"Högerjusterad text\",\n  \"outdent\": \"Minska indrag\",\n  \"strikethrough\": \"Genomstruken\",\n  \"styles\": \"Stil\",\n  \"subscript\": \"Nedsänkt\",\n  \"superscript\": \"Upphöjd\",\n  \"underline\": \"Understruken\",\n  \"unlink\": \"Ta bort länk\",\n  \"deleteFile\": \"Är du säker på att du vill radera \\\"{0}\\\"?\",\n  \"directoryNotFound\": \"En mapp med detta namn hittades ej.\",\n  \"emptyFolder\": \"Tom mapp\",\n  \"invalidFileType\": \"Filen \\\"{0}\\\" är inte giltig. Tillåtna filtyper är {1}.\",\n  \"orderBy\": \"Sortera på:\",\n  \"orderByName\": \"Namn\",\n  \"orderBySize\": \"Storlek\",\n  \"overwriteFile\": \"'En fil med namn \\\"{0}\\\" finns redan i aktuell mapp. Vill du skriva över den?\",\n  \"uploadFile\": \"Ladda upp\",\n  \"backColor\": \"Bakgrundsfärg\",\n  \"foreColor\": \"Färg\",\n  \"dropFilesHere\": \"släpp filer här för att ladda upp\",\n  \"dialogButtonSeparator\": \"eller\",\n  \"dialogCancel\": \"Avbryt\",\n  \"dialogInsert\": \"Lägg till\",\n  \"imageAltText\": \"Alternativ text\",\n  \"imageWebAddress\": \"Webbadress\",\n  \"linkOpenInNewWindow\": \"Öppna länk i ett nytt fönster\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"Skärmtips\",\n  \"linkWebAddress\": \"Webbadress\",\n  \"search\": \"Sök\",\n  \"createTable\": \"Skapa tabell\",\n  \"addColumnLeft\": \"Lägg till vänsterkolumn\",\n  \"addColumnRight\": \"Lägg till högerkolumn\",\n  \"addRowAbove\": \"Lägg till rad ovanför\",\n  \"addRowBelow\": \"Lägg till rad under\",\n  \"deleteColumn\": \"Ta bort kolumn\",\n  \"deleteRow\": \"Ta bort rad\",\n  \"formatting\": \"Format\",\n  \"viewHtml\": \"Visa HTML\",\n  \"dialogUpdate\": \"Uppdatera\",\n  \"insertFile\": \"Ange fil\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"Hela dagen\",\n  \"cancel\": \"Avbryt\",\n  \"editable\": {\n    \"confirmation\": \"Är du säker på att du vill ta bort tillfället?\"\n  },\n  \"date\": \"Datum\",\n  \"destroy\": \"Ta bort\",\n  \"editor\": {\n    \"allDayEvent\": \"Heldagstillfälle\",\n    \"description\": \"Beskrivning\",\n    \"editorTitle\": \"Tillfälle\",\n    \"end\": \"Slut\",\n    \"endTimezone\": \"Sluttidszon\",\n    \"repeat\": \"Upprepa\",\n    \"separateTimezones\":  \"Använd separata start och sluttidszoner\",\n    \"start\": \"Start\",\n    \"startTimezone\": \"Starttidszon\",\n    \"timezone\": \" \",\n    \"timezoneEditorButton\": \"Tidszon\",\n    \"timezoneEditorTitle\": \"Tidszoner\",\n    \"title\": \"Titel\",\n    \"noTimezone\": \"Ingen tidszon\"\n  },\n  \"event\": \"Tillfälle\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Vill du ta bort enbart detta tillfället eller hela serien?\",\n    \"deleteWindowOccurrence\": \"Ta bort nuvarande upprepning\",\n    \"deleteWindowSeries\": \"Ta bort serien\",\n    \"deleteWindowTitle\": \"Ta bort återkommande objekt\",\n    \"editRecurring\": \"Vill du redigera enbart detta tillfälle eller hela serien?\",\n    \"editWindowOccurrence\": \"Redigera återkommande tillfälle\",\n    \"editWindowSeries\": \"Redigera serie\",\n    \"editWindowTitle\": \"Redigera återkommande objekt\"\n  },\n  \"save\": \"Spara\",\n  \"time\": \"Tid\",\n  \"today\": \"Idag\",\n  \"views\": {\n    \"agenda\": \"Agenda\",\n    \"day\": \"Dag\",\n    \"month\": \"Månad\",\n    \"week\": \"Vecka\",\n    \"workWeek\": \"Arbetsvecka\"\n  },\n  \"deleteWindowTitle\": \"Ta bort tillfälle\",\n  \"showFullDay\": \"Visa heldag\",\n  \"showWorkDay\": \"Visa arbetsdag\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Stäng\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Avbryt\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Avbryt\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}