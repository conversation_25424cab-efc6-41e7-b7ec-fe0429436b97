{"version": 3, "sources": ["messages/kendo.messages.mk-MK.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "noColor", "clearColor", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "overflowAnchor", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "cleanFormatting", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "units", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "expandCollapseColumnHeader", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "confirmationDialog", "text", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "colorPicker", "toolbar", "alignmentButtons", "backgroundColor", "borders", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "ListBox", "tools", "moveUp", "moveDown", "transferTo", "transferFrom", "transferAllTo", "transferAllFrom", "TreeView", "Upload", "localization", "clearSelectedFiles", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "progress", "Dialog", "close", "Calendar", "weekColumnHeader", "<PERSON><PERSON>", "Confirm", "Prompt", "DateInput", "hour", "minute", "dayperiod", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN;;;;;;;;;;;;;;;;;;;;;;;;CA0BF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,UACTC,OAAU,SACVC,QAAW,YACXC,WAAc,kBAMZV,MAAMC,GAAGU,cACbX,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,UACpDE,MAAS,UACTC,OAAU,SACVC,QAAW,YACXC,WAAc,kBAMZV,MAAMC,GAAGW,aACbZ,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,UACnDQ,cAAiB,qBACjBC,eAAkB,qBAClBC,OAAU,SACVC,QAAW,SACXC,KAAQ,WACRC,SAAY,uBACZC,KAAQ,UACRC,OAAU,aAMRpB,MAAMC,GAAGoB,SACbrB,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,UAC/CiB,KAAQ,OACRC,OAAU,SACVC,UAAa,aACbC,cAAiB,YACjBC,YAAe,UACfC,UAAa,UACbC,cAAiB,kBACjBC,YAAe,qBACfC,aAAgB,sBAChBC,YAAe,UACfC,oBAAuB,2BACvBC,kBAAqB,yBACrBC,OAAU,cACVC,QAAW,cACXC,WAAc,oBACdC,OAAU,sBACVC,YAAe,eACfC,WAAc,kBACdC,WAAc,cACdC,SAAY,gBACZC,SAAY,2BACZC,gBAAmB,kBACnBC,SAAY,0BACZC,gBAAmB,uBACnBC,YAAe,SACfC,WAAc,SACdC,UAAa,OACbC,UAAa,kBACbC,MAAS,UACTC,YAAe,qBACfC,WAAc,UACdC,eAAkB,gBAClBC,QAAW,aACXC,YAAe,WACfC,YAAe,QACfC,gBAAmB,6EACnBC,WAAc,sDACdC,cAAiB,4FACjBC,kBAAqB,4CACrBC,gBAAmB,aACnBC,aAAgB,qBAChBC,WAAc,cACdC,YAAe,cACfC,eAAkB,aAClBC,UAAa,SACbC,eAAkB,aAClBC,SAAY,QACZC,YAAe,SACfC,oBAAuB,+BACvBC,aAAgB,WAChBC,aAAgB,SAChBC,sBAAyB,MACzBC,aAAgB,SAChBC,gBAAmB,sBACnBC,YAAe,iBACfC,cAAiB,qBACjBC,eAAkB,sBAClBC,YAAe,kBACfC,YAAe,kBACfC,UAAa,cACbC,aAAgB,iBAChBC,SAAY,KACZC,YAAe,sBACfC,SAAY,SACZC,QAAW,SACXC,iBAAoB,cACpBC,QAAW,SACXC,QAAW,SACXC,MAAS,SACTC,OAAU,SACVC,MAAS,UACTC,YAAe,sBACfC,YAAe,uBACfC,WAAc,mBACdC,UAAa,cACbC,WAAc,UACdC,SAAY,YACZC,GAAM,KACNC,OAAU,MACVC,YAAe,cACfC,gBAAmB,gBACnBC,SAAY,kBACZC,0BAA6B,8BAC7BC,UAAa,sBACbC,YAAe,wBACfC,WAAc,uBACdC,aAAgB,2BAChBC,eAAkB,6BAClBC,cAAiB,2BACjBC,gBAAmB,iCACnBC,kBAAqB,mCACrBC,iBAAoB,kCACpBC,gBAAmB,4BACnBC,kBAAqB,6BACrBC,iBAAoB,4BACpBC,YAAe,uBACfrG,QAAW,SACXsG,KAAQ,SACRC,eAAkB,0BAMhBvH,MAAMC,GAAGuH,cACbxH,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,UACpD+C,WAAc,UACdE,QAAW,aACXE,YAAe,QACfD,YAAe,WACfK,kBAAqB,4CACrBT,YAAe,qBACfO,WAAc,sDACdD,gBAAmB,6EACnBE,cAAiB,4FACjB8D,cAAiB,iDACjBC,OAAU,cAMR1H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,UACnDuH,OAAU,QACVC,QAAW,UACX9G,OAAU,SACV+G,MAAS,UACTC,SAAY,cAMV/H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,eACNC,IAAO,kBACPC,WAAc,aACdC,SAAY,SACZC,eAAkB,YAClBC,SAAY,cACZC,OAAU,gBACVC,UAAa,eACbC,QAAW,WACXC,WAAc,eAEhBC,QACEV,GAAM,eACNC,IAAO,kBACPU,IAAO,4BACPC,GAAM,gBACNC,IAAO,0BACPC,GAAM,cACNR,OAAU,gBACVC,UAAa,gBAEfQ,MACEf,GAAM,eACNC,IAAO,kBACPU,IAAO,uBACPC,GAAM,QACNC,IAAO,4BACPC,GAAM,WACNR,OAAU,gBACVC,UAAa,gBAEfS,OACEhB,GAAM,eACNC,IAAO,kBACPK,OAAU,gBACVC,UAAa,mBAObzI,MAAMC,GAAGkJ,aACbnJ,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQC,UACnD+I,KAAQ,kCACRC,MAAS,iCACTzB,OAAU,UACVC,QAAW,aACX9G,OAAU,SACV+G,MAAS,UACTwB,IAAO,IACPC,GAAM,MACNC,YAAe,oBACfzB,SAAY,WACZ0B,MAAS,WACTjJ,OAAU,YAMRR,MAAMC,GAAGkJ,aACbnJ,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkJ,WAAWhJ,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,eACNC,IAAO,kBACPC,WAAc,aACdC,SAAY,SACZC,eAAkB,YAClBC,SAAY,cACZC,OAAU,gBACVC,UAAa,eACbC,QAAW,WACXC,WAAc,eAEhBC,QACEV,GAAM,eACNC,IAAO,kBACPU,IAAO,4BACPC,GAAM,gBACNC,IAAO,0BACPC,GAAM,cACNR,OAAU,gBACVC,UAAa,gBAEfQ,MACEf,GAAM,eACNC,IAAO,kBACPU,IAAO,uBACPC,GAAM,QACNC,IAAO,4BACPC,GAAM,WACNR,OAAU,gBACVC,UAAa,gBAEfS,OACEhB,GAAM,eACNC,IAAO,kBACPK,OAAU,gBACVC,UAAa,mBAObzI,MAAMC,GAAGyJ,mBACb1J,MAAMC,GAAGyJ,iBAAiBvJ,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyJ,iBAAiBvJ,UAAUC,QAAQC,UACzDsJ,SAAY,iBACZ7B,MAAS,UACT/G,OAAU,SACV2G,OAAU,cAMR1H,MAAMC,GAAG2J,QACb5J,MAAMC,GAAG2J,MAAMzJ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,MAAMzJ,UAAUC,QAAQC,UAC9CwJ,SACEC,SAAY,cACZC,OAAU,gBACVC,YAAe,cACfC,aAAgB,cAChBC,IAAO,kBAET1J,OAAU,SACV2J,4BAA+B,oBAC/BC,sBAAyB,iBACzBC,QAAW,UACXC,QACEC,aAAgB,SAChBC,YAAe,SACfC,IAAO,OACPC,gBAAmB,WACnBC,UAAa,UACbC,qBAAwB,UACxBC,gBAAmB,UACnBC,MAAS,UACTzB,MAAS,SACT0B,YAAe,WAEjBC,KAAQ,UACRC,OACEC,IAAO,MACPT,IAAO,OACPU,MAAS,QACTL,MAAS,UACTM,KAAQ,SACRC,KAAQ,aAORrL,MAAMC,GAAGqL,OACbtL,MAAMC,GAAGqL,KAAKnL,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqL,KAAKnL,UAAUC,QAAQC,UAC7CkL,UACE/K,OAAU,iBACVgL,WAAc,SACdC,OAAU,mBACVpB,QAAW,UACXqB,KAAQ,SACRC,MAAS,mBACTzB,IAAO,iBACPc,KAAQ,kBACRY,OAAU,SACVC,OAAU,YAEZC,UACEC,aAAgB,SAChBC,aAAgB,2DAChBC,cAAiB,WAEnBC,UAAa,wBACbC,2BAA8B,MAM5BnM,MAAMC,GAAGmM,WACbpM,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,UAC/CgM,OAAU,6BACVC,QAAW,eACXC,cAAiB,qBACjBC,MAAS,UACTjB,UACIG,KAAQ,SACRG,OAAU,WACVL,WAAc,SACdC,OAAU,mBACVgB,YAAe,wBACfpC,QAAW,UACXsB,MAAS,mBACTzB,IAAO,qBAOXlK,MAAMC,GAAGyM,YACb1M,MAAMC,GAAGyM,UAAUvM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyM,UAAUvM,UAAUC,QAAQC,UAClDsM,MAAS,2FAMP3M,MAAMC,GAAG2M,iBACb5M,MAAMC,GAAG2M,eAAezM,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,eAAezM,UAAUC,SAC/CyM,YAAe,mBACfC,cAAiB,qBAMf9M,MAAMC,GAAG8M,cACb/M,MAAMC,GAAG8M,YAAY5M,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8M,YAAY5M,UAAUC,QAAQC,UACpD2M,MAAS,QACTC,KAAQ,eACRC,KAAQ,eACRC,OAAU,cACVC,QAAW,WACXC,WAAc,eAMZrN,MAAMC,GAAGqN,QACbtN,MAAMC,GAAGqN,MAAMnN,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqN,MAAMnN,UAAUC,QAAQC,UAC9CkN,SAAY,OACZC,QAAW,0BACXb,MAAS,6BACTc,KAAQ,SACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,qBACTC,SAAY,0BACZC,KAAQ,uBACRC,KAAQ,yBACRC,QAAW,SACXC,UAAa,mBAMXjO,MAAMC,GAAGiO,gBACblO,MAAMC,GAAGiO,cAAc/N,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiO,cAAc/N,UAAUC,QAAQC,UACtDkN,SAAY,OACZC,QAAW,0BACXb,MAAS,6BACTc,KAAQ,SACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,qBACTC,SAAY,0BACZC,KAAQ,uBACRC,KAAQ,yBACRC,QAAW,SACXC,UAAa,mBAMXjO,MAAMC,GAAGkO,YACbnO,MAAMC,GAAGkO,UAAUhO,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkO,UAAUhO,UAAUC,QAAQC,UAClD+N,cAAiB,wCACjBC,aAAgB,wCAChBC,UAAa,2CAMXtO,MAAMC,GAAGsO,iBACbvO,MAAMC,GAAGsO,eAAepO,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsO,eAAepO,UAAUC,QAAQC,UACvD+I,KAAQ,kCACRoF,aAAgB,mBAChBzN,OAAU,SACV0N,QAAW,mBACXpF,MAAS,uBACTvB,MAAS,UACT4G,GAAM,KACNlO,OAAU,SACVwH,WACEK,SAAY,SACZC,eAAkB,YAClBF,WAAc,aACdG,SAAY,cACZL,GAAM,eACNC,IAAO,sBAOPnI,MAAMC,GAAG0O,mBACb3O,MAAMC,GAAG0O,iBAAiBxO,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0O,iBAAiBxO,UAAUC,QAAQC,UACzDuO,aACEC,MAAS,UACTC,OAAU,UACVC,MAAS,SACTC,OAAU,UACVC,QAAW,UACXC,OAAU,WAEZJ,QACEK,YAAe,uBACfC,SAAY,aAEdL,OACEI,YAAe,uBACfC,SAAY,aAEdJ,QACEI,SAAY,YACZD,YAAe,uBACfE,SAAY,gBAEdJ,SACEE,YAAe,uBACfE,SAAY,eACZD,SAAY,YACZlE,IAAO,QAETgE,QACEC,YAAe,wBACfE,SAAY,eACZD,SAAY,YACZ1B,GAAM,QAERjD,KACE6E,MAAS,QACTC,YAAe,WACfV,MAAS,UACTW,MAAS,MACTC,WAAc,aACdC,GAAM,OAERC,iBACE/B,MAAS,SACTgC,OAAU,UACVC,MAAS,UACTC,OAAU,YACV/B,KAAQ,cAEVgC,UACE7E,IAAO,MACP8E,QAAW,cACXC,QAAW,oBAOXjQ,MAAMC,GAAGiQ,YACblQ,MAAMC,GAAGiQ,UAAU/P,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiQ,UAAU/P,UAAUC,QAAQC,UAClD8P,OAAU,UACVlH,KAAQ,QACRmH,MAAS,SACTC,KAAQ,QACRC,YAAe,kBACfC,YAAe,yBACfC,MAAS,QACTxF,KAAQ,UACRxK,OAAU,SACV6J,QAAW,UACXoG,kBAAqB,iBACrBC,cAAiB,2BACjBC,eAAkB,wBAClB7E,UACEE,aAAgB,6DAElBf,OACEC,IAAO,MACPE,KAAQ,SACRwF,SAAY,iBACZC,OAAU,SACV1F,MAAS,SAEX2F,oBACEL,kBAAqB,8BACrBM,uBAA0B,yBAC1BC,mBAAsB,qBACtBC,gBAAmB,6BACnBC,qBAAwB,wBACxBC,iBAAoB,oBACpBC,gBAAmB,kEACnBC,cAAiB,kEAEnB/G,QACEjB,MAAS,SACTyB,MAAS,UACTL,IAAO,OACP6G,YAAe,gBACfC,YAAe,OACfC,OAAU,UACVC,SAAY,IACZC,cAAiB,yBACjBC,YAAe,wBACfC,kBAAqB,kDACrBC,oBAAuB,iBACvBC,qBAAwB,iBACxBC,cAAiB,iBACjBC,WAAc,sBACdxH,YAAe,aAOfxK,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS6R,gBACpDlS,MAAMiS,YAAY5R,SAAS6R,cAC3BpS,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS6R,eACxCC,WAAc,cACdC,cAAiB,qBACjBC,wBAA2B,kCAC3BC,sBAAyB,gCACzBC,eAAkB,qBAClBC,WAAc,UACdC,UAAa,YACbC,YAAe,YACfC,aAAgB,YAChBC,UAAa,UACbC,MAAS,iBACTC,YAAe,sBACfvS,MAAS,UACTC,OAAU,YAIRR,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS0S,UACpD/S,MAAMiS,YAAY5R,SAAS0S,QAC3BjT,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS0S,SACxCxS,MAAS,UACTyK,KAAQ,UACRxK,OAAU,SACVwS,OAAU,WACVxG,MAAS,UACTyG,OAAU,QACVC,OAAU,KACVC,mBACE9J,MAAS,SACT+J,YACExK,OAAU,OACVyK,SAAY,SACZpK,KAAQ,UAGZqK,kBACEjK,MAAS,QAEXkK,gBACElK,MAAS,oBAEXmK,eACEnK,MAAS,UAEXoK,iBACEpK,MAAS,cACTqK,SACCC,aAAgB,mBAChB/R,cAAiB,SACjBE,aAAgB,oBAChBC,YAAe,UACf6R,SAAY,eACZC,YAAe,qBACfC,YAAe,iBAGlBC,aACE1K,MAAS,aACTqK,SACEM,WAAc,YACdC,kBAAqB,oBACrBC,gBAAmB,kBACnBC,QAAW,YAGfC,cACE/K,MAAS,iBACTqK,SACEW,YAAe,iBACfC,WAAc,iBACdC,cAAiB,iBACjBC,SAAY,mBAGhBC,oBACEC,KAAQ,0DACRrL,MAAS,iBAEXsL,kBACEtL,MAAS,yBACTuL,YAAe,8CACfC,UAAa,iBACbC,UACEC,IAAO,qBACPnM,OAAU,OACV8L,KAAQ,QACRzL,KAAQ,QACR+L,OAAU,sBACVC,KAAQ,UAEVC,WACEC,YAAe,cACfC,SAAY,YACZC,QAAW,SACXC,WAAc,cACdC,QAAW,aACXC,WAAc,kBACdC,qBAAwB,6BACxBC,kBAAqB,yBAEvBC,kBACER,YAAe,kBACfC,SAAY,gBACZC,QAAW,mBACXC,WAAc,wBACdC,QAAW,iBACXC,WAAc,sBACdC,qBAAwB,iCACxBC,kBAAqB,4BACrBV,OAAU,oCAEZY,QACEd,SAAY,aACZe,SAAY,uBACZC,IAAO,MACPC,IAAO,OACPtM,MAAS,WACTqB,MAAS,UACTL,IAAO,OACPuL,cAAiB,wBACjBC,YAAe,cACfC,YAAe,yBACfC,SAAY,gBACZtB,UAAa,kBACbD,YAAe,kBACfwB,YAAe,oBAEjBC,cACEC,UAAa,iBACbC,YAAe,mBAGnBC,gBACEnN,MAAS,aACTuM,QACEa,SAAY,oBACZC,WAAc,mBACdC,WAAc,UACdC,UAAa,mBACbC,QAAW,UACXC,YAAe,cACfC,MAAS,SACTC,WAAc,SACdC,OAAU,SACVC,aAAgB,eAChBC,WAAc,eAGlBC,oBACEC,aAAgB,6CAElBC,mBACEjO,MAAS,wBACTgO,aAAgB,qGAChBzB,QACE2B,QAAW,cACXC,OAAU,YACVC,SAAY,kBAGhBC,4BACEL,aAAgB,+DAKhBrX,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAASsX,aACpD3X,MAAMiS,YAAY5R,SAASsX,WAC3B7X,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAASsX,YACxC9W,cAAiB,oBACjBC,eAAkB,oBAClB8W,cAAiB,wBACjBC,kBAAqB,qBACrBtX,MAAS,UACTmH,OAAU,WACVoQ,aAAgB,8BAChBhQ,MAAS,UACTiQ,OAAU,iBACVC,aAAgB,iBAChB1O,IAAO,IACPC,GAAM,MACNvB,WACEC,QACEI,SAAY,iBACZC,eAAkB,oBAClBF,WAAc,qBACdG,SAAY,uBAEdU,MACEf,GAAO,YACPC,IAAO,eACPa,GAAO,iBACPF,GAAO,mBAETF,QACEV,GAAM,eACNC,IAAO,kBACPU,IAAO,4BACPC,GAAM,gBACNC,IAAO,0BACPC,GAAM,mBAMRhJ,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS4X,cACpDjY,MAAMiS,YAAY5R,SAAS4X,YAC3BnY,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS4X,aACxCpF,MAAS,iBACTC,YAAe,sBACfvS,MAAS,UACTC,OAAU,YAIRR,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAAS6X,UACpDlY,MAAMiS,YAAY5R,SAAS6X,QAC3BpY,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAAS6X,SACxCrT,cAAiB,wBACjBC,eAAkB,yBAClBC,YAAe,kBACfC,YAAe,kBACfgB,UAAa,cACbmS,kBACExE,aAAgB,eAChB/R,cAAiB,SACjBE,aAAgB,gBAChBC,YAAe,UACf6R,SAAY,eACZC,YAAe,qBACfC,YAAe,gBAEjBsE,gBAAmB,UACnB9W,KAAQ,OACR+W,QAAW,SACXJ,aACEpF,MAAS,iBACTC,YAAe,uBAEjBwF,KAAQ,UACRC,IAAO,OACPrT,aAAgB,eAChBD,UAAa,YACbuT,YAAe,yBACfzX,OAAU,SACV0X,WAAc,OACd7V,SAAY,mBACZ8V,OAAU,uBACVC,aACEC,UAAa,aACbhQ,OAAU,OACViQ,QAAW,UACXC,UAAa,aACbzF,SAAY,SACZpK,KAAQ,QACRoH,KAAQ,QACR0I,SAAY,cACZC,SAAY,cACZC,YAAe,qBAEjBC,sBAAyB,kBACzBC,sBAAyB,mBACzBC,OAAU,iBACVC,eACEhF,YAAe,iBACfC,WAAc,iBACdC,cAAiB,iBACjBC,SAAY,kBAEdjT,OAAU,SACV+X,MAAS,aACTC,cACEvF,WAAc,YACdC,kBAAqB,oBACrBC,gBAAmB,kBACnBC,QAAW,WAEbqF,KAAQ,YACRC,MAAS,SACTC,aACEC,KAAQ,UACRC,KAAQ,SAEVC,OAAU,kBACVC,QAAW,oBACXC,SAAY,oBACZC,aACEC,aAAgB,yBAChBC,cAAiB,yBACjBC,aAAgB,0BAChBC,cAAiB,2BAEnBC,UAAa,gBACbC,SAAY,gBACZ9Y,UAAa,WACb+Y,WAAc,+BAIZva,MAAMiS,aAAejS,MAAMiS,YAAY5R,SAASma,OACpDxa,MAAMiS,YAAY5R,SAASma,KAC3B1a,EAAEQ,QAAO,EAAMN,MAAMiS,YAAY5R,SAASma,MACxCC,QACEC,sBAAyB,mKACzBC,4BAA+B,6DAC/BC,gBAAmB,oFAErBC,MACEC,KAAQ,OACRC,OAAU,SACVC,KAAQ,eAORhb,MAAMC,GAAGgb,SACbjb,MAAMC,GAAGgb,OAAO9a,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgb,OAAO9a,UAAUC,SACvC8a,oBAAuB,UACvBC,oBAAuB,YAMrBnb,MAAMC,GAAGmb,UACbpb,MAAMC,GAAGmb,QAAQjb,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmb,QAAQjb,UAAUC,QAAQC,UAChDgb,OACErI,OAAU,UACVsI,OAAU,kBACVC,SAAY,kBACZC,WAAc,aACdC,aAAgB,aAChBC,cAAiB,kBACjBC,gBAAmB,sBAOnB3b,MAAMC,GAAGmM,WACbpM,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmM,SAASjM,UAAUC,QAAQC,UACjDgM,OAAU,6BACVC,QAAW,eACXC,cAAiB,qBACjBC,MAAS,UACTjB,UACIG,KAAQ,SACRG,OAAU,WACVL,WAAc,SACdC,OAAU,mBACVgB,YAAe,wBACfpC,QAAW,UACXsB,MAAS,mBACTzB,IAAO,qBAOTlK,MAAMC,GAAG2b,WACb5b,MAAMC,GAAG2b,SAASzb,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2b,SAASzb,UAAUC,QAAQC,UACjDiM,QAAW,eACXC,cAAiB,qBACjBC,MAAS,aAMPxM,MAAMC,GAAG4b,SACb7b,MAAMC,GAAG4b,OAAO1b,UAAUC,QAAQ0b,aAClChc,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4b,OAAO1b,UAAUC,QAAQ0b,cAC/ClQ,OAAU,qBACVpL,OAAU,SACVgM,MAAS,UACTwG,OAAU,WACV+I,mBAAsB,UACtBC,oBAAuB,mBACvBvU,cAAiB,4CACjBwU,gBAAmB,YACnBC,eAAkB,WAClBC,cAAiB,UACjBC,aAAgB,SAChBC,sBAAyB,eACzBC,qBAAwB,YACxBC,mBAAsB,wCACtBC,mBAAsB,qCACtBC,qBAAwB,sCAMtBzc,MAAMC,GAAGyc,YACb1c,MAAMC,GAAGyc,UAAUvc,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyc,UAAUvc,UAAUC,QAAQC,UAClDsc,SAAY,0BACZC,QAAW,mBACX9G,IAAO,gDACPC,IAAO,8CACP8G,KAAQ,mBACRC,MAAS,0BACTC,IAAO,uBACP9T,KAAQ,uBACR+T,YAAe,yEAKbhd,MAAMC,GAAGgd,WACbjd,MAAMC,GAAGgd,SAAS5c,SAClBP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgd,SAAS5c,UAC7BiM,QAAS,kBAMTtM,MAAMC,GAAGid,SACbld,MAAMC,GAAGid,OAAO/c,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGid,OAAO/c,UAAUC,QAAQ0b,cAC/CqB,MAAS,aAKPnd,MAAMC,GAAGmd,WACbpd,MAAMC,GAAGmd,SAASjd,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmd,SAASjd,UAAUC,QAAQC,UACjDgd,iBAAoB,MAMlBrd,MAAMC,GAAGqd,QACbtd,MAAMC,GAAGqd,MAAMnd,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqd,MAAMnd,UAAUC,QAAQ0b,cAC9C5I,OAAU,QAMRlT,MAAMC,GAAGsd,UACbvd,MAAMC,GAAGsd,QAAQpd,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsd,QAAQpd,UAAUC,QAAQ0b,cAChD5I,OAAU,KACV1S,OAAU,YAKRR,MAAMC,GAAGud,SACbxd,MAAMC,GAAGud,OAAOrd,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGud,OAAOrd,UAAUC,QAAQ0b,cAC/C5I,OAAU,KACV1S,OAAU,YAKRR,MAAMC,GAAGwd,YACXzd,MAAMC,GAAGwd,UAAUtd,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwd,UAAUtd,UAAUC,QAAQC,UAClDgL,KAAQ,SACRF,MAAS,QACTD,IAAO,MACP8E,QAAW,kBACX0N,KAAQ,SACRC,OAAU,SACV/N,OAAU,UACVgO,UAAa,YAIhBC,OAAO7d,MAAM8d", "file": "kendo.messages.mk-MK.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n/**\n * Kendo UI v2017.3.1026 (http://www.telerik.com/kendo-ui)\n * Copyright 2017 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.\n *\n * Kendo UI commercial licenses may be obtained at\n * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete\n * If you do not own a commercial license, this file shall be governed by the trial license terms.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n*/\n\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Примени\",\n  \"cancel\": \"Откажи\",\n  \"noColor\": \"нема боја\",\n  \"clearColor\": \"Исчисти боја\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"Примени\",\n  \"cancel\": \"Откажи\",\n  \"noColor\": \"нема боја\",\n  \"clearColor\": \"Исчисти боја\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Сортирање Растечки\",\n  \"sortDescending\": \"Сортирање Опаѓачки\",\n  \"filter\": \"Филтер\",\n  \"columns\": \"Колони\",\n  \"done\": \"Завршено\",\n  \"settings\": \"Нагодувања на колони\",\n  \"lock\": \"Заклучи\",\n  \"unlock\": \"Отклучи\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Болд\",\n  \"italic\": \"Италик\",\n  \"underline\": \"Подвлечено\",\n  \"strikethrough\": \"Прецртано\",\n  \"superscript\": \"Надзнак\",\n  \"subscript\": \"Подзнак\",\n  \"justifyCenter\": \"Центрирај текст\",\n  \"justifyLeft\": \"Порамни текст лево\",\n  \"justifyRight\": \"Порамни текст десно\",\n  \"justifyFull\": \"Порамни\",\n  \"insertUnorderedList\": \"Вметни неподреден список\",\n  \"insertOrderedList\": \"Вметни подреден список\",\n  \"indent\": \"Вовлекување\",\n  \"outdent\": \"Извлекување\",\n  \"createLink\": \"Вметни хиперврска\",\n  \"unlink\": \"Отстрани хиперврска\",\n  \"insertImage\": \"Вметни слика\",\n  \"insertFile\": \"Вметни датотека\",\n  \"insertHtml\": \"Вметни ХТМЛ\",\n  \"viewHtml\": \"Погледни ХТМЛ\",\n  \"fontName\": \"Избери семејство на фонт\",\n  \"fontNameInherit\": \"(наследен фонт)\",\n  \"fontSize\": \"Избери големина на фонт\",\n  \"fontSizeInherit\": \"(наследена големина)\",\n  \"formatBlock\": \"Формат\",\n  \"formatting\": \"Формат\",\n  \"foreColor\": \"Боја\",\n  \"backColor\": \"Боја на заднина\",\n  \"style\": \"Стилови\",\n  \"emptyFolder\": \"Празен директориум\",\n  \"uploadFile\": \"Постави\",\n  \"overflowAnchor\": \"Повеќе алатки\",\n  \"orderBy\": \"Нареди од:\",\n  \"orderBySize\": \"Големина\",\n  \"orderByName\": \"Назив\",\n  \"invalidFileType\": \"Избраната датотека \\\"{0}\\\" е невалидна. Поддржани типови на датотеки се {1}.\",\n  \"deleteFile\": 'Дали сте сигурни дека сакате да гo избришете \"{0}\"?',\n  \"overwriteFile\": 'Датотека со име \"{0}\" веќе постои во тековниот директориум. Дали сакате да ја пребришете?',\n  \"directoryNotFound\": \"Директориум со ова име не беше пронајден.\",\n  \"imageWebAddress\": \"Веб адреса\",\n  \"imageAltText\": \"Алтернативен текст\",\n  \"imageWidth\": \"Ширина (px)\",\n  \"imageHeight\": \"Висина (px)\",\n  \"fileWebAddress\": \"Веб адреса\",\n  \"fileTitle\": \"Наслов\",\n  \"linkWebAddress\": \"Веб адреса\",\n  \"linkText\": \"Текст\",\n  \"linkToolTip\": \"Ознака\",\n  \"linkOpenInNewWindow\": \"Отвори врска во нов прозорец\",\n  \"dialogUpdate\": \"Ажурирај\",\n  \"dialogInsert\": \"Вметни\",\n  \"dialogButtonSeparator\": \"или\",\n  \"dialogCancel\": \"Откажи\",\n  \"cleanFormatting\": \"Исчисти форматирање\",\n  \"createTable\": \"Креирај табела\",\n  \"addColumnLeft\": \"Додади колона лево\",\n  \"addColumnRight\": \"Додади колона десно\",\n  \"addRowAbove\": \"Додади ред горе\",\n  \"addRowBelow\": \"Додади ред долу\",\n  \"deleteRow\": \"Избриши ред\",\n  \"deleteColumn\": \"Избриши колона\",\n  \"dialogOk\": \"ОК\",\n  \"tableWizard\": \"Волшебник за табела\",\n  \"tableTab\": \"Табела\",\n  \"cellTab\": \"Ќелија\",\n  \"accessibilityTab\": \"Пристапност\",\n  \"caption\": \"Наслов\",\n  \"summary\": \"Резиме\",\n  \"width\": \"Ширина\",\n  \"height\": \"Висина\",\n  \"units\": \"Единици\",\n  \"cellSpacing\": \"Растојание на ќелии\",\n  \"cellPadding\": \"Пополнување на ќелии\",\n  \"cellMargin\": \"Маргина на ќелии\",\n  \"alignment\": \"Порамнување\",\n  \"background\": \"Заднина\",\n  \"cssClass\": \"ЦСС класа\",\n  \"id\": \"ИД\",\n  \"border\": \"Раб\",\n  \"borderStyle\": \"Стил на раб\",\n  \"collapseBorders\": \"Сокриј рабови\",\n  \"wrapText\": \"Прелом на текст\",\n  \"associateCellsWithHeaders\": \"Асоцирај ќелии со заглавија\",\n  \"alignLeft\": \"Порамнување од лево\",\n  \"alignCenter\": \"Централно порaмнување\",\n  \"alignRight\": \"Порамнување од десно\",\n  \"alignLeftTop\": \"Порамнување од лево горе\",\n  \"alignCenterTop\": \"Централно порамнување горе\",\n  \"alignRightTop\": \"Порамнување од лево горе\",\n  \"alignLeftMiddle\": \"Порамнување од лево на средина\",\n  \"alignCenterMiddle\": \"Централно порамнување на средина\",\n  \"alignRightMiddle\": \"Порамнување од десно на средина\",\n  \"alignLeftBottom\": \"Порамнување од лево долу \",\n  \"alignCenterBottom\": \"Централно порамнување долу\",\n  \"alignRightBottom\": \"Порамнување од десно долу\",\n  \"alignRemove\": \"Отстрани порамнување\",\n  \"columns\": \"Колони\",\n  \"rows\": \"Редови\",\n  \"selectAllCells\": \"Избери ги сите ќелии\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Постави\",\n  \"orderBy\": \"Подреди по\",\n  \"orderByName\": \"Назив\",\n  \"orderBySize\": \"Големина\",\n  \"directoryNotFound\": \"Директориум со ова име не беше пронајден.\",\n  \"emptyFolder\": \"Празен директориум\",\n  \"deleteFile\": 'Дали сте сигурни дека сакате да го избришете \"{0}\"?',\n  \"invalidFileType\": \"Избраната датотека \\\"{0}\\\" е невалидна. Поддржани типови на датотеки се {1}.\",\n  \"overwriteFile\": 'Датотека со име \"{0}\" веќе постои во тековниот директориум. Дали сакате да ја пребришете?',\n  \"dropFilesHere\": \"Преместете ја датотеката тука за да се ипсрати\",\n  \"search\": \"Пребарај\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"точно\",\n  \"isFalse\": \"неточно\",\n  \"filter\": \"Филтер\",\n  \"clear\": \"Исчисти\",\n  \"operator\": \"Оператор\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Е еднакво на\",\n    \"neq\": \"Не е еднакво на\",\n    \"startswith\": \"Почнува со\",\n    \"contains\": \"Содржи\",\n    \"doesnotcontain\": \"Не содржи\",\n    \"endswith\": \"Завршува на\",\n    \"isnull\": \"Нема вредност\",\n    \"isnotnull\": \"Има вредност\",\n    \"isempty\": \"E празно\",\n    \"isnotempty\": \"Не е празно\"\n  },\n  \"number\": {\n    \"eq\": \"Е еднакво на\",\n    \"neq\": \"Не е еднакво на\",\n    \"gte\": \"Е поголемо или еднакво на\",\n    \"gt\": \"Е поголемо од\",\n    \"lte\": \"Е помало или еднакво на\",\n    \"lt\": \"Е помало од\",\n    \"isnull\": \"Нема вредност\",\n    \"isnotnull\": \"Има вредност\"\n  },\n  \"date\": {\n    \"eq\": \"E eднакво на\",\n    \"neq\": \"Не е еднакво на\",\n    \"gte\": \"Следи или еднаков на\",\n    \"gt\": \"Следи\",\n    \"lte\": \"Претходи или е еднаков на\",\n    \"lt\": \"Претходи\",\n    \"isnull\": \"Нема вредност\",\n    \"isnotnull\": \"Има вредност\"\n  },\n  \"enums\": {\n    \"eq\": \"E eднакво на\",\n    \"neq\": \"Не е еднакво на\",\n    \"isnull\": \"Нема вредност\",\n    \"isnotnull\": \"Има вредност\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Прикажи ставки со вредност што:\",\n  \"title\": \"Прикажи ставки со вредност што\",\n  \"isTrue\": \"е точна\",\n  \"isFalse\": \"не е точна\",\n  \"filter\": \"Филтер\",\n  \"clear\": \"Исчисти\",\n  \"and\": \"И\",\n  \"or\": \"Или\",\n  \"selectValue\": \"-Избери вредност-\",\n  \"operator\": \"Оператор\",\n  \"value\": \"Вредност\",\n  \"cancel\": \"Откажи\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Е еднакво на\",\n    \"neq\": \"Не е еднакво на\",\n    \"startswith\": \"Почнува со\",\n    \"contains\": \"Содржи\",\n    \"doesnotcontain\": \"Не содржи\",\n    \"endswith\": \"Завршува на\",\n    \"isnull\": \"Нема вредност\",\n    \"isnotnull\": \"Има вредност\",\n    \"isempty\": \"Е празно\",\n    \"isnotempty\": \"Не е празно\"\n  },\n  \"number\": {\n    \"eq\": \"Е еднакво на\",\n    \"neq\": \"Не е еднакво на\",\n    \"gte\": \"Е поголемо или еднакво на\",\n    \"gt\": \"Е поголемо од\",\n    \"lte\": \"Е помало или еднакво од\",\n    \"lt\": \"Е помало од\",\n    \"isnull\": \"Нема вредност\",\n    \"isnotnull\": \"Има вредност\"\n  },\n  \"date\": {\n    \"eq\": \"Е еднакво на\",\n    \"neq\": \"Не е еднакво на\",\n    \"gte\": \"Следи или еднаков на\",\n    \"gt\": \"Следи\",\n    \"lte\": \"Претходи или е еднаков на\",\n    \"lt\": \"Претходи\",\n    \"isnull\": \"Нема вредност\",\n    \"isnotnull\": \"Има вредност\"\n  },\n  \"enums\": {\n    \"eq\": \"Е еднакво на\",\n    \"neq\": \"Не е еднакво на\",\n    \"isnull\": \"Нема вредност\",\n    \"isnotnull\": \"Има вредност\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Избери ги сите\",\n  \"clear\": \"Исчисти\",\n  \"filter\": \"Филтер\",\n  \"search\": \"Пребарај\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Додади дете\",\n    \"append\": \"Додади задача\",\n    \"insertAfter\": \"Додади долу\",\n    \"insertBefore\": \"Додади горе\",\n    \"pdf\": \"Експорт во ПДФ\"\n  },\n  \"cancel\": \"Откажи\",\n  \"deleteDependencyWindowTitle\": \"Избриши зависност\",\n  \"deleteTaskWindowTitle\": \"Избриши задача\",\n  \"destroy\": \"Избриши\",\n  \"editor\": {\n    \"assingButton\": \"Додели\",\n    \"editorTitle\": \"Задача\",\n    \"end\": \"Крај\",\n    \"percentComplete\": \"Завршено\",\n    \"resources\": \"Ресурси\",\n    \"resourcesEditorTitle\": \"Ресурси\",\n    \"resourcesHeader\": \"Ресурси\",\n    \"start\": \"Почеток\",\n    \"title\": \"Наслов\",\n    \"unitsHeader\": \"Единици\"\n  },\n  \"save\": \"Зачувај\",\n  \"views\": {\n    \"day\": \"Ден\",\n    \"end\": \"Крај\",\n    \"month\": \"Месец\",\n    \"start\": \"Почеток\",\n    \"week\": \"Недела\",\n    \"year\": \"Година\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Откажи промени\",\n    \"canceledit\": \"Откажи\",\n    \"create\": \"Додади нов запис\",\n    \"destroy\": \"Избриши\",\n    \"edit\": \"Измени\",\n    \"excel\": \"Експорт во Ексел\",\n    \"pdf\": \"Експорт во ПДФ\",\n    \"save\": \"Зачувај промени\",\n    \"select\": \"Избери\",\n    \"update\": \"Ажурирај\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Откажи\",\n    \"confirmation\": \"Дали сте сигурни дека сакате да го избришете овој запис?\",\n    \"confirmDelete\": \"Избриши\"\n  },\n  \"noRecords\": \"Нема достапни записи.\",\n  \"expandCollapseColumnHeader\": \"\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n    \"noRows\": \"Нема записи за прикажување\",\n    \"loading\": \"Вчитување...\",\n    \"requestFailed\": \"Барањето не успеа.\",\n    \"retry\": \"Повтори\",\n    \"commands\": {\n        \"edit\": \"Измени\",\n        \"update\": \"Ажурирај\",\n        \"canceledit\": \"Откажи\",\n        \"create\": \"Додади нов запис\",\n        \"createchild\": \"Додади подреден запис\",\n        \"destroy\": \"Избриши\",\n        \"excel\": \"Експорт во Ексел\",\n        \"pdf\": \"Експорт во ПДФ\"\n    }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Повлечете го заглавието на колоната и префрлете го овде за да групирате по таа колона\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Зголеми вредност\",\n  \"downArrowText\": \"Намали вредност\"\n});\n}\n\n/* MediaPlayer messages */\n\nif (kendo.ui.MediaPlayer) {\nkendo.ui.MediaPlayer.prototype.options.messages =\n$.extend(true, kendo.ui.MediaPlayer.prototype.options.messages,{\n  \"pause\": \"Пауза\",\n  \"play\": \"Репродуцирај\",\n  \"mute\": \"Исклучи глас\",\n  \"unmute\": \"Вклучи глас\",\n  \"quality\": \"Квалитет\",\n  \"fullscreen\": \"Цел екран\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Сите\",\n  \"display\": \"{0} - {1} од {2} ставки\",\n  \"empty\": \"Нема ставки за прикажување\",\n  \"page\": \"Страна\",\n  \"of\": \"од {0}\",\n  \"itemsPerPage\": \"ставки по страна\",\n  \"first\": \"Оди на прва страна\",\n  \"previous\": \"Оди на претходна страна\",\n  \"next\": \"Оди на следна страна\",\n  \"last\": \"Оди на последна страна\",\n  \"refresh\": \"Освежи\",\n  \"morePages\": \"Повеќе страни\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"Сите\",\n  \"display\": \"{0} - {1} од {2} ставки\",\n  \"empty\": \"Нема ставки за прикажување\",\n  \"page\": \"Страна\",\n  \"of\": \"од {0}\",\n  \"itemsPerPage\": \"ставки по страна\",\n  \"first\": \"Оди на прва страна\",\n  \"previous\": \"Оди на претходна страна\",\n  \"next\": \"Оди на следна страна\",\n  \"last\": \"Оди на последна страна\",\n  \"refresh\": \"Освежи\",\n  \"morePages\": \"Повеќе страни\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"Преместете ги тука податочните полиња\",\n  \"columnFields\": \"Преместете ги тука податочните колони\",\n  \"rowFields\": \"Преместете ги тука податочните редови\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"Прикажи ставки со вредност што:\",\n  \"filterFields\": \"Филтер на полиња\",\n  \"filter\": \"Филтер\",\n  \"include\": \"Вклучи полиња...\",\n  \"title\": \"Полиња за вклучување\",\n  \"clear\": \"Исчисти\",\n  \"ok\": \"ОК\",\n  \"cancel\": \"Откажи\",\n  \"operators\": {\n    \"contains\": \"Содржи\",\n    \"doesnotcontain\": \"Не содржи\",\n    \"startswith\": \"Почнува со\",\n    \"endswith\": \"Завршува на\",\n    \"eq\": \"Е еднакво на\",\n    \"neq\": \"Не е еднакво на\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Никогаш\",\n    \"hourly\": \"Часовно\",\n    \"daily\": \"Дневно\",\n    \"weekly\": \"Неделно\",\n    \"monthly\": \"Месечно\",\n    \"yearly\": \"Годишно\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Повтори на секој/и: \",\n    \"interval\": \" час(ови)\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Повтори на секој/и: \",\n    \"interval\": \" ден(ови)\"\n  },\n  \"weekly\": {\n    \"interval\": \" недела/и\",\n    \"repeatEvery\": \"Повтори на секој/и: \",\n    \"repeatOn\": \"Повтори на: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Повтори на секој/и: \",\n    \"repeatOn\": \"Повтори на: \",\n    \"interval\": \" месец(и)\",\n    \"day\": \"Ден \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Повтори на секоја/и: \",\n    \"repeatOn\": \"Повтори на: \",\n    \"interval\": \" година/и\",\n    \"of\": \" од \"\n  },\n  \"end\": {\n    \"label\": \"Крај:\",\n    \"mobileLabel\": \"Завршува\",\n    \"never\": \"Никогаш\",\n    \"after\": \"По \",\n    \"occurrence\": \" случувања\",\n    \"on\": \"На \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"прво/а\",\n    \"second\": \"второ/а\",\n    \"third\": \"трето/а\",\n    \"fourth\": \"четврто/а\",\n    \"last\": \"последно/а\"\n  },\n  \"weekdays\": {\n    \"day\": \"ден\",\n    \"weekday\": \"работен ден\",\n    \"weekend\": \"ден од викенд\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"цел ден\",\n  \"date\": \"Датум\",\n  \"event\": \"Настан\",\n  \"time\": \"Време\",\n  \"showFullDay\": \"Прикажи цел ден\",\n  \"showWorkDay\": \"Прикажи работни часови\",\n  \"today\": \"Денес\",\n  \"save\": \"Зачувај\",\n  \"cancel\": \"Откажи\",\n  \"destroy\": \"Избриши\",\n  \"deleteWindowTitle\": \"Избриши настан\",\n  \"ariaSlotLabel\": \"Избран од {0:t} до {1:t}\",\n  \"ariaEventLabel\": \"{0} на {1:D} во {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"Дали сте сигурни дека сакате да го избришете овој настан?\"\n  },\n  \"views\": {\n    \"day\": \"Ден\",\n    \"week\": \"Недела\",\n    \"workWeek\": \"Работна недела\",\n    \"agenda\": \"Агенда\",\n    \"month\": \"Месец\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Избриши повторувачка ставка\",\n    \"deleteWindowOccurrence\": \"Избриши тековен настан\",\n    \"deleteWindowSeries\": \"Избриши ги сериите\",\n    \"editWindowTitle\": \"Измени повторувачка ставка\",\n    \"editWindowOccurrence\": \"Измени тековен настан\",\n    \"editWindowSeries\": \"Измени ги сериите\",\n    \"deleteRecurring\": \"Дали сакате да го избришете само овој настан или целата серија?\",\n    \"editRecurring\": \"Дали сакате да го измените само овој настан или целата серија?\"\n  },\n  \"editor\": {\n    \"title\": \"Наслов\",\n    \"start\": \"Почеток\",\n    \"end\": \"Крај\",\n    \"allDayEvent\": \"All day event\",\n    \"description\": \"Опис\",\n    \"repeat\": \"Повтори\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"Почетна временска зона\",\n    \"endTimezone\": \"Крајна временска зона\",\n    \"separateTimezones\": \"Користи посебни почетни и крајни временски зони\",\n    \"timezoneEditorTitle\": \"Временски зони\",\n    \"timezoneEditorButton\": \"Временска зона\",\n    \"timezoneTitle\": \"Временски зони\",\n    \"noTimezone\": \"Нема временска зона\",\n    \"editorTitle\": \"Настан\"\n  }\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"Сите рабови\",\n  \"insideBorders\": \"Внатре во рабовите\",\n  \"insideHorizontalBorders\": \"Внатре во хоризонталните рабови\",\n  \"insideVerticalBorders\": \"Внатре во вертикалните рабови\",\n  \"outsideBorders\": \"Надвор од рабовите\",\n  \"leftBorder\": \"Лев раб\",\n  \"topBorder\": \"Горен раб\",\n  \"rightBorder\": \"Десен раб\",\n  \"bottomBorder\": \"Долен раб\",\n  \"noBorders\": \"Без раб\",\n  \"reset\": \"Ресетирај боја\",\n  \"customColor\": \"Прилагодена боја...\",\n  \"apply\": \"Примени\",\n  \"cancel\": \"Откажи\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Примени\",\n  \"save\": \"Зачувај\",\n  \"cancel\": \"Откажи\",\n  \"remove\": \"Отстрани\",\n  \"retry\": \"Повтори\",\n  \"revert\": \"Врати\",\n  \"okText\": \"OK\",\n  \"formatCellsDialog\": {\n    \"title\": \"Формат\",\n    \"categories\": {\n      \"number\": \"Број\",\n      \"currency\": \"Валута\",\n      \"date\": \"Датум\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Фонт\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Големина на фонт\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Рабови\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Порамнување\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Лево порамнување\",\n     \"justifyCenter\": \"Центар\",\n     \"justifyRight\": \"Десно порамнување\",\n     \"justifyFull\": \"Порамни\",\n     \"alignTop\": \"Порамни горе\",\n     \"alignMiddle\": \"Порамни во средина\",\n     \"alignBottom\": \"Порамни долу\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Спои ќелии\",\n    \"buttons\": {\n      \"mergeCells\": \"Спои сите\",\n      \"mergeHorizontally\": \"Спои хоризонтално\",\n      \"mergeVertically\": \"Спои вертикално\",\n      \"unmerge\": \"Раздвои\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Замрзни панели\",\n    \"buttons\": {\n      \"freezePanes\": \"Замрзни панели\",\n      \"freezeRows\": \"Замрзни редови\",\n      \"freezeColumns\": \"Замрзни колони\",\n      \"unfreeze\": \"Одмрзни панели\"\n    }\n  },\n  \"confirmationDialog\": {\n    \"text\": \"Дали сте сигурни дека сакате да го остраните овој лист?\",\n    \"title\": \"Отстрани лист\"\n  },\n  \"validationDialog\": {\n    \"title\": \"Валидација на податоци\",\n    \"hintMessage\": \"Ве молиме внесете валидна {0} вредност {1}.\",\n    \"hintTitle\": \"Валидација {0}\",\n    \"criteria\": {\n      \"any\": \"Било која вредност\",\n      \"number\": \"Број\",\n      \"text\": \"Текст\",\n      \"date\": \"Датум\",\n      \"custom\": \"Прилагодена формула\",\n      \"list\": \"Список\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"поголемо од\",\n      \"lessThan\": \"помало од\",\n      \"between\": \"помеѓу\",\n      \"notBetween\": \"не е помеѓу\",\n      \"equalTo\": \"еднакво на\",\n      \"notEqualTo\": \"не е еднакво на\",\n      \"greaterThanOrEqualTo\": \"поголемо од или еднакво на\",\n      \"lessThanOrEqualTo\": \"помало или еднакво на\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"поголемо од {0}\",\n      \"lessThan\": \"помало од {0}\",\n      \"between\": \"помеѓу {0} и {1}\",\n      \"notBetween\": \"не е помеѓу {0} и {1}\",\n      \"equalTo\": \"еднакво на {0}\",\n      \"notEqualTo\": \"не е еднакво на {0}\",\n      \"greaterThanOrEqualTo\": \"поголемо од или еднакво на {0}\",\n      \"lessThanOrEqualTo\": \"помало или еднакво на {0}\",\n      \"custom\": \"што ја задоволува формулата: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Критериуми\",\n      \"comparer\": \"Оператор за споредба\",\n      \"min\": \"Мин\",\n      \"max\": \"Макс\",\n      \"value\": \"Вредност\",\n      \"start\": \"Почеток\",\n      \"end\": \"Крај\",\n      \"onInvalidData\": \"На невалидни податоци\",\n      \"rejectInput\": \"Отфрли влез\",\n      \"showWarning\": \"Прикажи предупредување\",\n      \"showHint\": \"Прикажи совет\",\n      \"hintTitle\": \"Наслов на совет\",\n      \"hintMessage\": \"Порака на совет\",\n      \"ignoreBlank\": \"Игнорирај празно\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Внесeте наслов\",\n      \"typeMessage\": \"Внесете порака\"\n    }\n  },\n  \"exportAsDialog\": {\n    \"title\": \"Експорт...\",\n    \"labels\": {\n      \"fileName\": \"Назив на датотека\",\n      \"saveAsType\": \"Зачувај како тип\",\n      \"exportArea\": \"Експорт\",\n      \"paperSize\": \"Големина на лист\",\n      \"margins\": \"Маргини\",\n      \"orientation\": \"Ориентација\",\n      \"print\": \"Печати\",\n      \"guidelines\": \"Насоки\",\n      \"center\": \"Центар\",\n      \"horizontally\": \"Хоризонтално\",\n      \"vertically\": \"Вертикално\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Не може да се смени дел од споена ќелија.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Копирање и вметнување\",\n    \"errorMessage\": \"Овие акции не можат да се повикуваат преку менито. Наместо тоа, користете кратенки на тастатурата:\",\n    \"labels\": {\n      \"forCopy\": \"за копирање\",\n      \"forCut\": \"за сечење\",\n      \"forPaste\": \"за вметнување\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"Ова дејство не може да се изврши при повеќекратен избор.\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Сортирај растечки\",\n  \"sortDescending\": \"Сортирај опаѓачки\",\n  \"filterByValue\": \"Филтрирај по вредност\",\n  \"filterByCondition\": \"Филтрирај по услов\",\n  \"apply\": \"Примени\",\n  \"search\": \"Пребарај\",\n  \"addToCurrent\": \"Додади во тековна селекција\",\n  \"clear\": \"Исчисти\",\n  \"blanks\": \"(Празни места)\",\n  \"operatorNone\": \"Без критериуми\",\n  \"and\": \"И\",\n  \"or\": \"ИЛИ\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"Текстот содржи\",\n      \"doesnotcontain\": \"Текстот не содржи\",\n      \"startswith\": \"Текстот почнува со\",\n      \"endswith\": \"Текстот завршува на\"\n    },\n    \"date\": {\n      \"eq\":  \"Датумот е\",\n      \"neq\": \"Датумот не е\",\n      \"lt\":  \"Датумот е пред\",\n      \"gt\":  \"Датумот е после\"\n    },\n    \"number\": {\n      \"eq\": \"Е еднакво на\",\n      \"neq\": \"Не е еднакво на\",\n      \"gte\": \"Е поголемо или еднакво на\",\n      \"gt\": \"Е поголемо од\",\n      \"lte\": \"Е помало или еднакво на\",\n      \"lt\": \"Е помало од\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.colorPicker) {\nkendo.spreadsheet.messages.colorPicker =\n$.extend(true, kendo.spreadsheet.messages.colorPicker,{\n  \"reset\": \"Ресетирај боја\",\n  \"customColor\": \"Прилагодена боја...\",\n  \"apply\": \"Примени\",\n  \"cancel\": \"Откажи\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Додади колона од лево\",\n  \"addColumnRight\": \"Додади колона од десно\",\n  \"addRowAbove\": \"Додади ред горе\",\n  \"addRowBelow\": \"Додади ред долу\",\n  \"alignment\": \"Порамнување\",\n  \"alignmentButtons\": {\n    \"justtifyLeft\": \"Порамни лево\",\n    \"justifyCenter\": \"Центар\",\n    \"justifyRight\": \"Порамни десно\",\n    \"justifyFull\": \"Порамни\",\n    \"alignTop\": \"Порамни горе\",\n    \"alignMiddle\": \"Порамни на средина\",\n    \"alignBottom\": \"Порамни долу\"\n  },\n  \"backgroundColor\": \"Заднина\",\n  \"bold\": \"Болд\",\n  \"borders\": \"Рабови\",\n  \"colorPicker\": {\n    \"reset\": \"Ресетирај боја\",\n    \"customColor\": \"Прилагодена боја...\"\n  },\n  \"copy\": \"Копирај\",\n  \"cut\": \"Сечи\",\n  \"deleteColumn\": \"Бриши колона\",\n  \"deleteRow\": \"Бриши ред\",\n  \"excelImport\": \"Импортирај од Ексел...\",\n  \"filter\": \"Филтер\",\n  \"fontFamily\": \"Фонт\",\n  \"fontSize\": \"Големина на фонт\",\n  \"format\": \"Прилагоден формат...\",\n  \"formatTypes\": {\n    \"automatic\": \"Автоматски\",\n    \"number\": \"Број\",\n    \"percent\": \"Процент\",\n    \"financial\": \"Финансиски\",\n    \"currency\": \"Валута\",\n    \"date\": \"Датум\",\n    \"time\": \"Време\",\n    \"dateTime\": \"Датум време\",\n    \"duration\": \"Времетраење\",\n    \"moreFormats\": \"Повеќе формати...\"\n  },\n  \"formatDecreaseDecimal\": \"Намали децимала\",\n  \"formatIncreaseDecimal\": \"Зголеми децимала\",\n  \"freeze\": \"Замрзни панели\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Замрзни панели\",\n    \"freezeRows\": \"Замрзни редови\",\n    \"freezeColumns\": \"Замрзни колони\",\n    \"unfreeze\": \"Одмрзни панели\"\n  },\n  \"italic\": \"Италик\",\n  \"merge\": \"Спои ќелии\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Спои сите\",\n    \"mergeHorizontally\": \"Спои хоризонтално\",\n    \"mergeVertically\": \"Спои вертикално\",\n    \"unmerge\": \"Раздвои\"\n  },\n  \"open\": \"Отвори...\",\n  \"paste\": \"Вменти\",\n  \"quickAccess\": {\n    \"redo\": \"Повтори\",\n    \"undo\": \"Врати\"\n  },\n  \"saveAs\": \"Зачувај како...\",\n  \"sortAsc\": \"Сортирај растечки\",\n  \"sortDesc\": \"Сортирај опаѓачки\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Сортирај лист растечки\",\n    \"sortSheetDesc\": \"Сортирај лист опаѓачки\",\n    \"sortRangeAsc\": \"Сортирај опсег растечки\",\n    \"sortRangeDesc\": \"Сортирај опсег опаѓачки\"\n  },\n  \"textColor\": \"Боја на текст\",\n  \"textWrap\": \"Преломи текст\",\n  \"underline\": \"Подвлечи\",\n  \"validation\": \"Валидација на податоци...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Не може да се вметнат ќелии поради можност за загуба на податоци. Изберете друга локација за вметнување или избришете ги податоците од крајот на работниот лист.\",\n    \"filterRangeContainingMerges\": \"Не може да се создаде филтер во опсег кој содржи спојувања\",\n    \"validationError\": \"Вредноста што ја внесовте ги крши правилата за валидација поставени на ќелијата.\"\n  },\n  \"tabs\": {\n    \"home\": \"Дома\",\n    \"insert\": \"Вметни\",\n    \"data\": \"Податоци\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Зголеми\",\n  \"decreaseButtonTitle\": \"Намали\"\n});\n}\n\n/* ListBox messaages */\n\nif (kendo.ui.ListBox) {\nkendo.ui.ListBox.prototype.options.messages =\n$.extend(true, kendo.ui.ListBox.prototype.options.messages,{\n  \"tools\": {\n    \"remove\": \"Избриши\",\n    \"moveUp\": \"Премести нагоре\",\n    \"moveDown\": \"Премести надолу\",\n    \"transferTo\": \"Префрли до\",\n    \"transferFrom\": \"Префрли од\",\n    \"transferAllTo\": \"Префрли сите до\",\n    \"transferAllFrom\": \"Префрли сите од\"\n  }\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n  \"noRows\": \"Нема записи за прикажување\",\n  \"loading\": \"Вчитување...\",\n  \"requestFailed\": \"Барањето не успеа.\",\n  \"retry\": \"Повтори\",\n  \"commands\": {\n      \"edit\": \"Измени\",\n      \"update\": \"Ажурирај\",\n      \"canceledit\": \"Откажи\",\n      \"create\": \"Додади нов запис\",\n      \"createchild\": \"Додади подреден запис\",\n      \"destroy\": \"Избриши\",\n      \"excel\": \"Експорт во Ексел\",\n      \"pdf\": \"Експорт во ПДФ\"\n  }\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Вчитување...\",\n  \"requestFailed\": \"Барањето не успеа.\",\n  \"retry\": \"Повтори\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Избери датотеки...\",\n  \"cancel\": \"Откажи\",\n  \"retry\": \"Повтори\",\n  \"remove\": \"Отстрани\",\n  \"clearSelectedFiles\": \"Исчисти\",\n  \"uploadSelectedFiles\": \"Постави датотеки\",\n  \"dropFilesHere\": \"Премести датотеки тука за да ги поставите\",\n  \"statusUploading\": \"uploading\",\n  \"statusUploaded\": \"uploaded\",\n  \"statusWarning\": \"warning\",\n  \"statusFailed\": \"failed\",\n  \"headerStatusUploading\": \"Испраќање...\",\n  \"headerStatusUploaded\": \"Направено\",\n  \"invalidMaxFileSize\": \"Големината на датотеката е преголема.\",\n  \"invalidMinFileSize\": \"Големината на датотеката е премала\",\n  \"invalidFileExtension\": \"Типот на датотека не е дозволен.\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} е задолжително поле\",\n  \"pattern\": \"{0} не е валидно\",\n  \"min\": \"{0} треба да биде поголемо или еднакво на {1}\",\n  \"max\": \"{0} треба да биде помало или еднакво на {1}\",\n  \"step\": \"{0} не е валидно\",\n  \"email\": \"{0} не е валиден е-маил\",\n  \"url\": \"{0} не е валидно УРЛ\",\n  \"date\": \"{0} не валиден датум\",\n  \"dateCompare\": \"Крајниот датум треба да биде поголем или еднаков на почетниот датум\"\n});\n}\n\n/* kendo.ui.progress method */\nif (kendo.ui.progress) {\nkendo.ui.progress.messages =\n$.extend(true, kendo.ui.progress.messages, {\n    loading: \"Вчитување...\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Затвори\"\n});\n}\n\n/* Calendar */\nif (kendo.ui.Calendar) {\nkendo.ui.Calendar.prototype.options.messages =\n$.extend(true, kendo.ui.Calendar.prototype.options.messages, {\n  \"weekColumnHeader\": \"\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Откажи\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Откажи\"\n});\n}\n\n/* DateInput */\nif (kendo.ui.DateInput) {\n  kendo.ui.DateInput.prototype.options.messages =\n    $.extend(true, kendo.ui.DateInput.prototype.options.messages, {\n      \"year\": \"година\",\n      \"month\": \"месец\",\n      \"day\": \"ден\",\n      \"weekday\": \"ден од неделата\",\n      \"hour\": \"часови\",\n      \"minute\": \"минути\",\n      \"second\": \"секунди\",\n      \"dayperiod\": \"AM/PM\"\n    });\n}\n\n})(window.kendo.jQuery);}));"]}