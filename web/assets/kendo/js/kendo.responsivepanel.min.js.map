{"version": 3, "sources": ["kendo.responsivepanel.js"], "names": ["f", "define", "$", "undefined", "proxy", "NS", "OPEN", "CLOSE", "ACTIVATE_EVENTS", "Widget", "kendo", "ui", "ResponsivePanel", "extend", "init", "element", "options", "fn", "call", "this", "_guid", "guid", "_to<PERSON><PERSON><PERSON><PERSON>", "_toggleButtonClick", "_<PERSON><PERSON><PERSON><PERSON>", "_close", "document", "documentElement", "on", "to<PERSON><PERSON><PERSON><PERSON>", "_registerBreakpoint", "addClass", "orientation", "_resizeHandler", "resize", "window", "_mediaQuery", "_registerStyle", "template", "breakpoint", "cssText", "head", "style", "createElement", "append<PERSON><PERSON><PERSON>", "styleSheet", "createTextNode", "name", "autoClose", "events", "_resize", "removeClass", "off", "e", "preventDefault", "type", "hasClass", "close", "open", "trigger", "prevented", "isDefaultPrevented", "container", "target", "closest", "length", "destroy", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,yBAA0B,cAAeD,IAClD,WAqGE,MA7FC,UAAUE,EAAGC,GAAb,GACOC,GAAQF,EAAEE,MACVC,EAAK,wBACLC,EAAO,OACPC,EAAQ,QACRC,EAAkB,QAAUH,EAAK,cAAgBA,EAAK,YAAcA,EACpEI,EAASC,MAAMC,GAAGF,OAClBG,EAAkBH,EAAOI,QACzBC,KAAM,SAAUC,EAASC,GACrBP,EAAOQ,GAAGH,KAAKI,KAAKC,KAAMJ,EAASC,GACnCG,KAAKC,MAAQ,IAAMV,MAAMW,OACzBF,KAAKG,eAAiBlB,EAAMe,KAAKI,mBAAoBJ,MACrDA,KAAKK,cAAgBpB,EAAMe,KAAKM,OAAQN,MACxCjB,EAAEwB,SAASC,iBAAiBC,GAAGpB,EAAiBW,KAAKH,QAAQa,aAAcV,KAAKG,gBAChFH,KAAKW,sBACLX,KAAKJ,QAAQgB,SAAS,qBAAuBZ,KAAKH,QAAQgB,YAAc,IAAMb,KAAKC,OACnFD,KAAKc,eAAiB7B,EAAMe,KAAKe,OAAQf,MAAM,GAC/CjB,EAAEiC,QAAQP,GAAG,SAAWvB,EAAIc,KAAKc,iBAErCG,YAAa,8+BACbN,oBAAqB,WACjB,GAAId,GAAUG,KAAKH,OACnBG,MAAKkB,eAAe3B,MAAM4B,SAASnB,KAAKiB,cACpCG,WAAYvB,EAAQuB,WACpBV,aAAcb,EAAQa,aACtBR,KAAMF,KAAKC,UAGnBiB,eAAgB,SAAUG,GAAV,GACRC,GAAOvC,EAAE,aAAa,GACtBwC,EAAQhB,SAASiB,cAAc,QACnCF,GAAKG,YAAYF,GACbA,EAAMG,WACNH,EAAMG,WAAWL,QAAUA,EAE3BE,EAAME,YAAYlB,SAASoB,eAAeN,KAGlDxB,SACI+B,KAAM,kBACNf,YAAa,OACbH,aAAc,mBACdU,WAAY,IACZS,WAAW,GAEfC,QACI3C,EACAC,GAEJ2C,QAAS,WACL/B,KAAKJ,QAAQoC,YAAY,sCACzBjD,EAAEwB,SAASC,iBAAiByB,IAAI5C,EAAiBW,KAAKK,gBAE1DD,mBAAoB,SAAU8B,GAC1BA,EAAEC,iBACY,YAAVD,EAAEE,OAGFpC,KAAKJ,QAAQyC,SAAS,qBACtBrC,KAAKsC,QAELtC,KAAKuC,SAGbA,KAAM,WACGvC,KAAKwC,QAAQrD,KACda,KAAKJ,QAAQgB,SAAS,sCAClBZ,KAAKH,QAAQgC,WACb9C,EAAEwB,SAASC,iBAAiBC,GAAGpB,EAAiBW,KAAKK,iBAIjEiC,MAAO,WACEtC,KAAKwC,QAAQpD,KACdY,KAAKJ,QAAQgB,SAAS,oBAAoBoB,YAAY,qBACtDjD,EAAEwB,SAASC,iBAAiByB,IAAI5C,EAAiBW,KAAKK,iBAG9DC,OAAQ,SAAU4B,GAAV,GACAO,GAAYP,EAAEQ,qBACdC,EAAY5D,EAAEmD,EAAEU,QAAQC,QAAQ7C,KAAKH,QAAQa,aAAe,aAC3DiC,GAAUG,QAAWL,GACtBzC,KAAKsC,SAGbS,QAAS,WACLzD,EAAOQ,GAAGiD,QAAQhD,KAAKC,MACvBjB,EAAEiC,QAAQiB,IAAI,SAAW/C,EAAIc,KAAKc,gBAClC/B,EAAEwB,SAASC,iBAAiByB,IAAI5C,EAAiBW,KAAKK,iBAG9Dd,OAAMC,GAAGwD,OAAOvD,IAClBuB,OAAOzB,MAAM0D,QACRjC,OAAOzB,OACE,kBAAVT,SAAwBA,OAAOoE,IAAMpE,OAAS,SAAUqE,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.responsivepanel.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.responsivepanel', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'responsive-panel',\n        name: 'Responsive Panel',\n        category: 'web',\n        description: 'The Responsive Panel widget allows a panel of content to be hidden on mobile devices, available through a toggle button.',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var proxy = $.proxy;\n        var NS = '.kendoResponsivePanel';\n        var OPEN = 'open';\n        var CLOSE = 'close';\n        var ACTIVATE_EVENTS = 'click' + NS + ' touchstart' + NS + ' touchend' + NS;\n        var Widget = kendo.ui.Widget;\n        var ResponsivePanel = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this._guid = '_' + kendo.guid();\n                this._toggleHandler = proxy(this._toggleButtonClick, this);\n                this._closeHandler = proxy(this._close, this);\n                $(document.documentElement).on(ACTIVATE_EVENTS, this.options.toggleButton, this._toggleHandler);\n                this._registerBreakpoint();\n                this.element.addClass('k-rpanel k-rpanel-' + this.options.orientation + ' ' + this._guid);\n                this._resizeHandler = proxy(this.resize, this, true);\n                $(window).on('resize' + NS, this._resizeHandler);\n            },\n            _mediaQuery: '@media (max-width: #= breakpoint-1 #px) {' + '.#= guid #.k-rpanel-animate.k-rpanel-left,' + '.#= guid #.k-rpanel-animate.k-rpanel-right {' + '-webkit-transition: -webkit-transform .2s ease-out;' + '-ms-transition: -ms-transform .2s ease-out;' + 'transition: transform .2s ease-out;' + '} ' + '.#= guid #.k-rpanel-top {' + 'overflow: hidden;' + '}' + '.#= guid #.k-rpanel-animate.k-rpanel-top {' + '-webkit-transition: max-height .2s linear;' + '-ms-transition: max-height .2s linear;' + 'transition: max-height .2s linear;' + '}' + '} ' + '@media (min-width: #= breakpoint #px) {' + '#= toggleButton # { display: none; } ' + '.#= guid #.k-rpanel-left { float: left; } ' + '.#= guid #.k-rpanel-right { float: right; } ' + '.#= guid #.k-rpanel-left, .#= guid #.k-rpanel-right {' + 'position: relative;' + '-webkit-transform: translateX(0);' + '-ms-transform: translateX(0);' + 'transform: translateX(0);' + '-webkit-transform: translateX(0) translateZ(0);' + '-ms-transform: translateX(0) translateZ(0);' + 'transform: translateX(0) translateZ(0);' + '} ' + '.k-ie9 .#= guid #.k-rpanel-left { left: 0; } ' + '.#= guid #.k-rpanel-top { max-height: none; }' + '}',\n            _registerBreakpoint: function () {\n                var options = this.options;\n                this._registerStyle(kendo.template(this._mediaQuery)({\n                    breakpoint: options.breakpoint,\n                    toggleButton: options.toggleButton,\n                    guid: this._guid\n                }));\n            },\n            _registerStyle: function (cssText) {\n                var head = $('head,body')[0];\n                var style = document.createElement('style');\n                head.appendChild(style);\n                if (style.styleSheet) {\n                    style.styleSheet.cssText = cssText;\n                } else {\n                    style.appendChild(document.createTextNode(cssText));\n                }\n            },\n            options: {\n                name: 'ResponsivePanel',\n                orientation: 'left',\n                toggleButton: '.k-rpanel-toggle',\n                breakpoint: 640,\n                autoClose: true\n            },\n            events: [\n                OPEN,\n                CLOSE\n            ],\n            _resize: function () {\n                this.element.removeClass('k-rpanel-animate k-rpanel-expanded');\n                $(document.documentElement).off(ACTIVATE_EVENTS, this._closeHandler);\n            },\n            _toggleButtonClick: function (e) {\n                e.preventDefault();\n                if (e.type == 'touchend') {\n                    return;\n                }\n                if (this.element.hasClass('k-rpanel-expanded')) {\n                    this.close();\n                } else {\n                    this.open();\n                }\n            },\n            open: function () {\n                if (!this.trigger(OPEN)) {\n                    this.element.addClass('k-rpanel-animate k-rpanel-expanded');\n                    if (this.options.autoClose) {\n                        $(document.documentElement).on(ACTIVATE_EVENTS, this._closeHandler);\n                    }\n                }\n            },\n            close: function () {\n                if (!this.trigger(CLOSE)) {\n                    this.element.addClass('k-rpanel-animate').removeClass('k-rpanel-expanded');\n                    $(document.documentElement).off(ACTIVATE_EVENTS, this._closeHandler);\n                }\n            },\n            _close: function (e) {\n                var prevented = e.isDefaultPrevented();\n                var container = $(e.target).closest(this.options.toggleButton + ',.k-rpanel');\n                if (!container.length && !prevented) {\n                    this.close();\n                }\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                $(window).off('resize' + NS, this._resizeHandler);\n                $(document.documentElement).off(ACTIVATE_EVENTS, this._closeHandler);\n            }\n        });\n        kendo.ui.plugin(ResponsivePanel);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}