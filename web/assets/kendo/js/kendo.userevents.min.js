/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.userevents.min",["kendo.core.min"],e)}(function(){return function(e,t){function n(e,t){var n=e.x.location,i=e.y.location,o=t.x.location,r=t.y.location,s=n-o,a=i-r;return{center:{x:(n+o)/2,y:(i+r)/2},distance:Math.sqrt(s*s+a*a)}}function i(e){var t,n,i,o=[],r=e.originalEvent,a=e.currentTarget,c=0;if(e.api)o.push({id:2,event:e,target:e.target,currentTarget:e.target,location:e,type:"api"});else if(e.type.match(/touch/))for(n=r?r.changedTouches:[],t=n.length;c<t;c++)i=n[c],o.push({location:i,event:e,target:i.target,currentTarget:a,id:i.identifier,type:"touch"});else o.push(s.pointers||s.msPointers?{location:r,event:e,target:e.target,currentTarget:a,id:r.pointerId,type:"pointer"}:{id:1,event:e,target:e.target,currentTarget:a,location:e,type:"mouse"});return o}function o(e){for(var t=r.eventMap.up.split(" "),n=0,i=t.length;n<i;n++)e(t[n])}var r=window.kendo,s=r.support,a=r.Class,c=r.Observable,u=e.now,l=e.extend,h=s.mobileOS,p=h&&h.android,d=800,f=300,v=s.browser.msie?5:0,g="press",_="hold",m="select",T="start",y="move",k="end",E="cancel",x="tap",D="doubleTap",M="release",w="gesturestart",b="gesturechange",A="gestureend",C="gesturetap",I={api:0,touch:0,mouse:9,pointer:9},S=!s.touch||s.mouseAndTouchPresent,P=a.extend({init:function(e,t){var n=this;n.axis=e,n._updateLocationData(t),n.startLocation=n.location,n.velocity=n.delta=0,n.timeStamp=u()},move:function(e){var t=this,n=e["page"+t.axis],i=u(),o=i-t.timeStamp||1;!n&&p||(t.delta=n-t.location,t._updateLocationData(e),t.initialDelta=n-t.startLocation,t.velocity=t.delta/o,t.timeStamp=i)},_updateLocationData:function(e){var t=this,n=t.axis;t.location=e["page"+n],t.client=e["client"+n],t.screen=e["screen"+n]}}),L=a.extend({init:function(e,t,n){l(this,{x:new P("X",n.location),y:new P("Y",n.location),type:n.type,useClickAsTap:e.useClickAsTap,threshold:e.threshold||I[n.type],userEvents:e,target:t,currentTarget:n.currentTarget,initialTouch:n.target,id:n.id,pressEvent:n,_clicks:e._clicks,supportDoubleTap:e.supportDoubleTap,_moved:!1,_finished:!1})},press:function(){this._holdTimeout=setTimeout(e.proxy(this,"_hold"),this.userEvents.minHold),this._trigger(g,this.pressEvent)},_tap:function(e){var t=this;t.userEvents._clicks++,1==t.userEvents._clicks&&(t._clickTimeout=setTimeout(function(){1==t.userEvents._clicks?t._trigger(x,e):t._trigger(D,e),t.userEvents._clicks=0},f))},_hold:function(){this._trigger(_,this.pressEvent)},move:function(e){var t=this;if(!t._finished){if(t.x.move(e.location),t.y.move(e.location),!t._moved){if(t._withinIgnoreThreshold())return;if(X.current&&X.current!==t.userEvents)return t.dispose();t._start(e)}t._finished||t._trigger(y,e)}},end:function(e){this.endTime=u(),this._finished||(this._finished=!0,this._trigger(M,e),this._moved?this._trigger(k,e):this.useClickAsTap||(this.supportDoubleTap?this._tap(e):this._trigger(x,e)),clearTimeout(this._holdTimeout),this.dispose())},dispose:function(){var t=this.userEvents,n=t.touches;this._finished=!0,this.pressEvent=null,clearTimeout(this._holdTimeout),n.splice(e.inArray(this,n),1)},skip:function(){this.dispose()},cancel:function(){this.dispose()},isMoved:function(){return this._moved},_start:function(e){clearTimeout(this._holdTimeout),this.startTime=u(),this._moved=!0,this._trigger(T,e)},_trigger:function(e,t){var n=this,i=t.event,o={touch:n,x:n.x,y:n.y,target:n.target,event:i};n.userEvents.notify(e,o)&&i.preventDefault()},_withinIgnoreThreshold:function(){var e=this.x.initialDelta,t=this.y.initialDelta;return Math.sqrt(e*e+t*t)<=this.threshold}}),X=c.extend({init:function(t,n){var i,a,u,h,p=this,f=r.guid();n=n||{},i=p.filter=n.filter,p.threshold=n.threshold||v,p.minHold=n.minHold||d,p.touches=[],p._maxTouches=n.multiTouch?2:1,p.allowSelection=n.allowSelection,p.captureUpIfMoved=n.captureUpIfMoved,p.useClickAsTap=!n.fastTap&&!s.delayedClick(),p.eventNS=f,p._clicks=0,p.supportDoubleTap=n.supportDoubleTap,t=e(t).handler(p),c.fn.init.call(p),l(p,{element:t,surface:e(n.global&&S?t[0].ownerDocument.documentElement:n.surface||t),stopPropagation:n.stopPropagation,pressed:!1}),p.surface.handler(p).on(r.applyEventMap("move",f),"_move").on(r.applyEventMap("up cancel",f),"_end"),t.on(r.applyEventMap("down",f),i,"_start"),p.useClickAsTap&&t.on(r.applyEventMap("click",f),i,"_click"),(s.pointers||s.msPointers)&&(s.browser.version<11?(a="pinch-zoom double-tap-zoom",t.css("-ms-touch-action",n.touchAction&&"none"!=n.touchAction?a+" "+n.touchAction:a)):t.css("touch-action",n.touchAction||"none")),n.preventDragEvent&&t.on(r.applyEventMap("dragstart",f),r.preventDefault),t.on(r.applyEventMap("mousedown",f),i,{root:t},"_select"),p.captureUpIfMoved&&s.eventCapture&&(u=p.surface[0],h=e.proxy(p.preventIfMoving,p),o(function(e){u.addEventListener(e,h,!0)})),p.bind([g,_,x,D,T,y,k,M,E,w,b,A,C,m],n)},preventIfMoving:function(e){this._isMoved()&&e.preventDefault()},destroy:function(){var e,t=this;t._destroyed||(t._destroyed=!0,t.captureUpIfMoved&&s.eventCapture&&(e=t.surface[0],o(function(n){e.removeEventListener(n,t.preventIfMoving)})),t.element.kendoDestroy(t.eventNS),t.surface.kendoDestroy(t.eventNS),t.element.removeData("handler"),t.surface.removeData("handler"),t._disposeAll(),t.unbind(),delete t.surface,delete t.element,delete t.currentTarget)},capture:function(){X.current=this},cancel:function(){this._disposeAll(),this.trigger(E)},notify:function(e,t){var i=this,o=i.touches;if(this._isMultiTouch()){switch(e){case y:e=b;break;case k:e=A;break;case x:e=C}l(t,{touches:o},n(o[0],o[1]))}return this.trigger(e,l(t,{type:e}))},press:function(e,t,n){this._apiCall("_start",e,t,n)},move:function(e,t){this._apiCall("_move",e,t)},end:function(e,t){this._apiCall("_end",e,t)},_isMultiTouch:function(){return this.touches.length>1},_maxTouchesReached:function(){return this.touches.length>=this._maxTouches},_disposeAll:function(){for(var e=this.touches;e.length>0;)e.pop().dispose()},_isMoved:function(){return e.grep(this.touches,function(e){return e.isMoved()}).length},_select:function(e){this.allowSelection&&!this.trigger(m,{event:e})||e.preventDefault()},_start:function(t){var n,o,r=this,s=0,a=r.filter,c=i(t),u=c.length,l=t.which;if(!(l&&l>1||r._maxTouchesReached()))for(X.current=null,r.currentTarget=t.currentTarget,r.stopPropagation&&t.stopPropagation();s<u&&!r._maxTouchesReached();s++)o=c[s],n=a?e(o.currentTarget):r.element,n.length&&(o=new L(r,n,o),r.touches.push(o),o.press(),r._isMultiTouch()&&r.notify("gesturestart",{}))},_move:function(e){this._eachTouch("move",e)},_end:function(e){this._eachTouch("end",e)},_click:function(t){var n={touch:{initialTouch:t.target,target:e(t.currentTarget),endTime:u(),x:{location:t.pageX,client:t.clientX},y:{location:t.pageY,client:t.clientY}},x:t.pageX,y:t.pageY,target:e(t.currentTarget),event:t,type:"tap"};this.trigger("tap",n)&&t.preventDefault()},_eachTouch:function(e,t){var n,o,r,s,a=this,c={},u=i(t),l=a.touches;for(n=0;n<l.length;n++)o=l[n],c[o.id]=o;for(n=0;n<u.length;n++)r=u[n],s=c[r.id],s&&s[e](r)},_apiCall:function(t,n,i,o){this[t]({api:!0,pageX:n,pageY:i,clientX:n,clientY:i,target:e(o||this.element)[0],stopPropagation:e.noop,preventDefault:e.noop})}});X.defaultThreshold=function(e){v=e},X.minHold=function(e){d=e},r.getTouches=i,r.touchDelta=n,r.UserEvents=X}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.userevents.min.js.map
