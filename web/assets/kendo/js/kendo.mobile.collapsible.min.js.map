{"version": 3, "sources": ["kendo.mobile.collapsible.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "ui", "mobile", "Widget", "COLLAPSIBLE", "HEADER", "CONTENT", "INSET", "HEADER_WRAPPER", "CONTENT_WRAPPER", "COLLAPSED", "EXPANDED", "ANIMATED", "LEFT", "EXAPND", "COLLAPSE", "Collapsible", "extend", "init", "element", "options", "that", "this", "container", "fn", "call", "addClass", "_buildHeader", "content", "children", "not", "header", "wrapAll", "parent", "_userEvents", "UserEvents", "fastTap", "tap", "toggle", "collapsed", "inset", "animation", "height", "hide", "events", "name", "collapseIcon", "expandIcon", "iconPosition", "destroy", "expand", "instant", "icon", "ios", "support", "mobileOS", "trigger", "find", "removeClass", "off", "show", "_getContentHeight", "resize", "collapse", "one", "isCollapsed", "hasClass", "iconSpan", "prepend", "style", "attr", "css", "position", "visibility", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,4BAA6B,cAAeD,IACrD,WAuIE,MA5HC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAMG,OAAOD,GAAIE,EAASF,EAAGE,OAAQC,EAAc,iBAAkBC,EAAS,wBAAyBC,EAAU,yBAA0BC,EAAQ,sBAAuBC,EAAiB,8CAAmDH,EAAS,WAAaI,EAAkB,+CAAoDH,EAAU,WAAaI,EAAY,eAAgBC,EAAW,cAAeC,EAAW,cAAeC,EAAO,OAAQC,EAAS,SAAUC,EAAW,WACzfC,EAAcb,EAAOc,QACrBC,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOC,KAAMC,EAAY1B,EAAEsB,EAC/BhB,GAAOqB,GAAGN,KAAKO,KAAKJ,EAAME,EAAWH,GACrCG,EAAUG,SAAStB,GACnBiB,EAAKM,eACLN,EAAKO,QAAUL,EAAUM,WAAWC,IAAIT,EAAKU,QAAQC,QAAQvB,GAAiBwB,SAC9EZ,EAAKa,YAAc,GAAInC,GAAMoC,WAAWd,EAAKU,QACzCK,SAAS,EACTC,IAAK,WACDhB,EAAKiB,YAGbf,EAAUG,SAASL,EAAKD,QAAQmB,UAAY7B,EAAYC,GACpDU,EAAKD,QAAQoB,OACbjB,EAAUG,SAASnB,GAEnBc,EAAKD,QAAQqB,WACbpB,EAAKO,QAAQF,SAASd,GACtBS,EAAKO,QAAQc,OAAO,GAChBrB,EAAKD,QAAQmB,WACblB,EAAKO,QAAQe,QAEVtB,EAAKD,QAAQmB,WACpBlB,EAAKO,QAAQe,QAGrBC,QACI9B,EACAC,GAEJK,SACIyB,KAAM,cACNN,WAAW,EACXO,aAAc,UACdC,WAAY,UACZC,aAAcnC,EACd4B,WAAW,EACXD,OAAO,GAEXS,QAAS,WACL9C,EAAOqB,GAAGyB,QAAQxB,KAAKH,MACvBA,KAAKY,YAAYe,WAErBC,OAAQ,SAAUC,GACd,GAAIC,GAAO9B,KAAKF,QAAQ0B,aAAclB,EAAUN,KAAKM,QAASyB,EAAMtD,EAAMuD,QAAQC,SAASF,GACtF/B,MAAKkC,QAAQ1C,KACVsC,GACA9B,KAAKS,OAAO0B,KAAK,YAAYC,cAAchC,SAAS,cAAgB0B,GAExE9B,KAAKH,QAAQuC,YAAYhD,GAAWgB,SAASf,GACzCW,KAAKF,QAAQqB,YAAcU,GAC3BvB,EAAQ+B,IAAI,iBACZ/B,EAAQgC,OACJP,GACAzB,EAAQ8B,YAAY9C,GAExBgB,EAAQc,OAAOpB,KAAKuC,qBAChBR,GACAzB,EAAQF,SAASd,GAErBb,EAAM+D,OAAOlC,IAEbA,EAAQgC,SAIpBG,SAAU,SAAUZ,GAChB,GAAIC,GAAO9B,KAAKF,QAAQ2B,WAAYnB,EAAUN,KAAKM,OAC9CN,MAAKkC,QAAQzC,KACVqC,GACA9B,KAAKS,OAAO0B,KAAK,YAAYC,cAAchC,SAAS,cAAgB0B,GAExE9B,KAAKH,QAAQuC,YAAY/C,GAAUe,SAAShB,GACxCY,KAAKF,QAAQqB,YAAcU,GAC3BvB,EAAQoC,IAAI,gBAAiB,WACzBpC,EAAQe,SAEZf,EAAQc,OAAO,IAEfd,EAAQe,SAIpBL,OAAQ,SAAUa,GACV7B,KAAK2C,cACL3C,KAAK4B,OAAOC,GAEZ7B,KAAKyC,SAASZ,IAGtBc,YAAa,WACT,MAAO3C,MAAKH,QAAQ+C,SAASxD,IAEjCoD,OAAQ,YACCxC,KAAK2C,eAAiB3C,KAAKF,QAAQqB,WACpCnB,KAAKM,QAAQc,OAAOpB,KAAKuC,sBAGjClC,aAAc,WACV,GAAII,GAAST,KAAKH,QAAQU,SAAS,WAAWG,QAAQxB,GAAiB2D,EAAWtE,EAAE,2BAA4BuD,EAAO9B,KAAKF,QAAQmB,UAAYjB,KAAKF,QAAQ2B,WAAazB,KAAKF,QAAQ0B,aAAcE,EAAe1B,KAAKF,QAAQ4B,YAC7NI,KACArB,EAAOqC,QAAQD,GACfA,EAASzC,SAAS,MAAQ0B,IAE9B9B,KAAKS,OAASA,EAAOE,SACrBX,KAAKS,OAAOL,SAAS,WAAasB,IAEtCa,kBAAmB,WACf,GAAwCnB,GAApC2B,EAAQ/C,KAAKM,QAAQ0C,KAAK,QAQ9B,OAPAhD,MAAKM,QAAQ2C,KACTC,SAAU,WACVC,WAAY,SACZ/B,OAAQ,SAEZA,EAASpB,KAAKM,QAAQc,SACtBpB,KAAKM,QAAQ0C,KAAK,QAASD,EAAQA,EAAQ,IACpC3B,IAGfzC,GAAGyE,OAAO1D,IACZhB,OAAOD,MAAM4E,QACR3E,OAAOD,OACE,kBAAVH,SAAwBA,OAAOgF,IAAMhF,OAAS,SAAUiF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.collapsible.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.collapsible', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.collapsible',\n        name: 'Collapsible',\n        category: 'mobile',\n        description: 'The Kendo mobile Collapsible widget provides ability for creating collapsible blocks of content.',\n        depends: [\n            'core',\n            'userevents'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.mobile.ui, Widget = ui.Widget, COLLAPSIBLE = 'km-collapsible', HEADER = 'km-collapsible-header', CONTENT = 'km-collapsible-content', INSET = 'km-collapsibleinset', HEADER_WRAPPER = '<div data-role=\\'collapsible-header\\' class=\\'' + HEADER + '\\'></div>', CONTENT_WRAPPER = '<div data-role=\\'collapsible-content\\' class=\\'' + CONTENT + '\\'></div>', COLLAPSED = 'km-collapsed', EXPANDED = 'km-expanded', ANIMATED = 'km-animated', LEFT = 'left', EXAPND = 'expand', COLLAPSE = 'collapse';\n        var Collapsible = Widget.extend({\n            init: function (element, options) {\n                var that = this, container = $(element);\n                Widget.fn.init.call(that, container, options);\n                container.addClass(COLLAPSIBLE);\n                that._buildHeader();\n                that.content = container.children().not(that.header).wrapAll(CONTENT_WRAPPER).parent();\n                that._userEvents = new kendo.UserEvents(that.header, {\n                    fastTap: true,\n                    tap: function () {\n                        that.toggle();\n                    }\n                });\n                container.addClass(that.options.collapsed ? COLLAPSED : EXPANDED);\n                if (that.options.inset) {\n                    container.addClass(INSET);\n                }\n                if (that.options.animation) {\n                    that.content.addClass(ANIMATED);\n                    that.content.height(0);\n                    if (that.options.collapsed) {\n                        that.content.hide();\n                    }\n                } else if (that.options.collapsed) {\n                    that.content.hide();\n                }\n            },\n            events: [\n                EXAPND,\n                COLLAPSE\n            ],\n            options: {\n                name: 'Collapsible',\n                collapsed: true,\n                collapseIcon: 'arrow-n',\n                expandIcon: 'arrow-s',\n                iconPosition: LEFT,\n                animation: true,\n                inset: false\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this._userEvents.destroy();\n            },\n            expand: function (instant) {\n                var icon = this.options.collapseIcon, content = this.content, ios = kendo.support.mobileOS.ios;\n                if (!this.trigger(EXAPND)) {\n                    if (icon) {\n                        this.header.find('.km-icon').removeClass().addClass('km-icon km-' + icon);\n                    }\n                    this.element.removeClass(COLLAPSED).addClass(EXPANDED);\n                    if (this.options.animation && !instant) {\n                        content.off('transitionend');\n                        content.show();\n                        if (ios) {\n                            content.removeClass(ANIMATED);\n                        }\n                        content.height(this._getContentHeight());\n                        if (ios) {\n                            content.addClass(ANIMATED);\n                        }\n                        kendo.resize(content);\n                    } else {\n                        content.show();\n                    }\n                }\n            },\n            collapse: function (instant) {\n                var icon = this.options.expandIcon, content = this.content;\n                if (!this.trigger(COLLAPSE)) {\n                    if (icon) {\n                        this.header.find('.km-icon').removeClass().addClass('km-icon km-' + icon);\n                    }\n                    this.element.removeClass(EXPANDED).addClass(COLLAPSED);\n                    if (this.options.animation && !instant) {\n                        content.one('transitionend', function () {\n                            content.hide();\n                        });\n                        content.height(0);\n                    } else {\n                        content.hide();\n                    }\n                }\n            },\n            toggle: function (instant) {\n                if (this.isCollapsed()) {\n                    this.expand(instant);\n                } else {\n                    this.collapse(instant);\n                }\n            },\n            isCollapsed: function () {\n                return this.element.hasClass(COLLAPSED);\n            },\n            resize: function () {\n                if (!this.isCollapsed() && this.options.animation) {\n                    this.content.height(this._getContentHeight());\n                }\n            },\n            _buildHeader: function () {\n                var header = this.element.children(':header').wrapAll(HEADER_WRAPPER), iconSpan = $('<span class=\"km-icon\"/>'), icon = this.options.collapsed ? this.options.expandIcon : this.options.collapseIcon, iconPosition = this.options.iconPosition;\n                if (icon) {\n                    header.prepend(iconSpan);\n                    iconSpan.addClass('km-' + icon);\n                }\n                this.header = header.parent();\n                this.header.addClass('km-icon-' + iconPosition);\n            },\n            _getContentHeight: function () {\n                var style = this.content.attr('style'), height;\n                this.content.css({\n                    position: 'absolute',\n                    visibility: 'hidden',\n                    height: 'auto'\n                });\n                height = this.content.height();\n                this.content.attr('style', style ? style : '');\n                return height;\n            }\n        });\n        ui.plugin(Collapsible);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}