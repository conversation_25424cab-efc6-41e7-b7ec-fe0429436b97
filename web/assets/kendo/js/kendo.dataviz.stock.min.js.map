{"version": 3, "sources": ["kendo.dataviz.stock.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "createDiv", "className", "div", "ClonedObject", "clone", "obj", "prototype", "dataviz", "elementStyles", "toTime", "datavizConstants", "Chart", "drawing", "FadeOutAnimation", "NavigatorHint", "NAVIGATOR_PANE", "NAVIGATOR_AXIS", "constants", "ZOOM_ACCELERATION", "Navigator", "AUTO_CATEGORY_WIDTH", "<PERSON><PERSON><PERSON>", "Animation", "setup", "_initialOpacity", "parseFloat", "element", "opacity", "step", "pos", "interpolateV<PERSON>ue", "abort", "fn", "call", "display", "cancel", "container", "chartService", "padding", "chartPadding", "top", "paddingTop", "left", "paddingLeft", "createElements", "tooltip", "scroll", "innerHTML", "show", "from", "to", "bbox", "tooltipStyle", "ref", "middle", "toDate", "scrollWidth", "minPos", "center", "x", "maxPos", "posRange", "range", "max", "min", "scale", "offset", "intl", "format", "template", "getTemplate", "clearHideTimeout", "_visible", "visibility", "y1", "marginTop", "borderTopWidth", "_hideTimeout", "clearTimeout", "_hideAnimation", "hide", "this$1", "setTimeout", "play", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "setDefaultOptions", "chart", "select", "navigator", "parseDate", "defined", "hint", "visible", "chartObserver", "InstanceObserver", "DRAG", "DRAG_END", "ZOOM", "ZOOM_END", "addObserver", "clean", "selection", "removeObserver", "redraw", "_redrawSelf", "initSelection", "axis", "mainAxis", "ref$1", "roundedRange", "ref$2", "mousewheel", "axisClone", "categoriesCount", "Selection", "valueOrDefault", "zoom", "selectStart", "selectEnd", "setRang<PERSON>", "<PERSON><PERSON><PERSON>", "_createPlotArea", "namedCategoryAxes", "filterAxes", "silent", "_plotArea", "last", "panes", "redrawSlaves", "slavePanes", "slice", "srcSeries", "series", "categoryAxis", "clearSeriesPointsCache", "_drag", "e", "duration", "coords", "_eventCoordinates", "originalEvent", "navigatorA<PERSON>s", "naviRange", "inNavigator", "pane", "containsPoint", "axisRanges", "name", "limitValue", "liveDrag", "set", "showHint", "_dragEnd", "filter", "readSelection", "ref_selection_options", "allAxes", "idx", "args", "axisOptions", "requiresHandlers", "type", "DateCategoryAxis", "baseUnit", "categories", "addDuration", "baseUnitStep", "trigger", "_zoom", "fromIx", "toIx", "ref_options", "delta", "categoryIndex", "preventDefault", "Math", "abs", "expand", "scaleRange", "_zoomEnd", "backgroundBox", "_selectStart", "_select", "_selectEnd", "themeOptions", "naviOptions", "paneOptions", "__navi", "concat", "attachAxes", "attachSeries", "categoryAxes", "valueAxes", "valueAxis", "equallySpacedSeries", "filterSeriesByType", "EQUALLY_SPACED_SERIES", "justifyAxis", "base", "roundToBaseUnit", "justified", "_collapse", "majorTicks", "labels", "autoBind", "autoBindElements", "autoBaseUnitSteps", "minutes", "hours", "days", "weeks", "months", "years", "user", "maxDateGroups", "title", "position", "plotBands", "_overlap", "mirror", "majorGridLines", "navigatorSeries", "seriesColors", "defaults", "seriesDefaults", "color", "categoryField", "dateField", "visibleInLegend", "applyDefaults", "elementSize", "DEFAULT_WIDTH", "theme", "stockDefaults", "axisDefaults", "floor", "_setElementClass", "addClass", "setOptions", "destroyNavigator", "noTransitionsRedraw", "transitions", "_fullRedraw", "_resize", "_redraw", "_dirty", "partialRedraw", "seriesCount", "grep", "s", "dirty", "_seriesCount", "_trackSharedTooltip", "paneByPoint", "_unsetActivePoint", "bindCategories", "copyNavigatorCategories", "axisIx", "definitions", "_stopChartHandlers", "narrowRange", "markers", "line", "legend", "buildFilter", "field", "operator", "ChartInstanceObserver", "ui", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "proxy", "CHANGE", "StockInstanceObserver", "handlerMap", "navigator<PERSON><PERSON><PERSON>", "navigatorCreated", "persistSeriesVisibility", "_createChart", "_initNavigatorOptions", "_instance", "observer", "sender", "rtl", "_isRtl", "navigatorOptions", "support", "is<PERSON><PERSON>ch", "touch", "isFirefox", "browser", "mozilla", "dataSource", "_initDataSource", "userOptions", "dummy<PERSON>xis", "hasServerFiltering", "serverFiltering", "hasSelect", "_onNavigatorCreated", "_navigator", "_initNavigatorDataSource", "dsOptions", "_navigatorDataChangedHandler", "_onNavigatorDataChanged", "_navigatorDataSource", "data", "DataSource", "create", "bind", "fetch", "_bindNavigatorSeries", "seriesIx", "currentSeries", "seriesLength", "_isBindable", "currentAxis", "naviCategories", "instance", "<PERSON><PERSON><PERSON><PERSON>", "view", "_sourceSeries", "_bindCategoryAxis", "_model", "_dataBound", "_bindCategories", "_onDataChanged", "_removeNavigatorDataSource", "_onNavigatorFilter", "names", "inArray", "navigatorDataSource", "unbind", "plugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,mCAAoC,uBAAwBD,IACrE,YACG,WA4BG,QAASoF,GAAUC,EAAW1D,GAC1B,GAAI2D,GAAM9B,SAASC,cAAc,MAKjC,OAJA6B,GAAID,UAAYA,EACZ1D,IACA2D,EAAI3D,MAAM+B,QAAU/B,GAEjB2D,EA2fX,QAASC,MAET,QAASC,GAAMC,GAEX,MADAF,GAAaG,UAAYD,EAClB,GAAIF,GAjiBlB,GAEOI,GACAC,EACAd,EACAe,EACAC,EACAC,EACAC,EACAC,EA2BAC,EAyGAC,EACAC,EACAC,EAIAC,EACAC,EA+YAC,EACAC,CAniBJtE,QAAOC,MAAMuD,QAAUxD,OAAOC,MAAMuD,YAChCA,EAAUvD,MAAMuD,QAChBC,EAAgBD,EAAQC,cACxBd,EAAaa,EAAQb,WACrBe,EAASF,EAAQE,OACjBC,EAAmBH,EAAQU,UAC3BN,EAAQJ,EAAQI,MAChBC,EAAU5D,MAAM4D,QAChBC,EAAmBD,EAAQU,UAAUnE,QACrCoE,MAAO,WACHjE,KAAKkE,gBAAkBC,WAAWjB,EAAclD,KAAKoE,QAAS,WAAWC,UAE7EC,KAAM,SAAUC,GACZrB,EAAclD,KAAKoE,SAAWC,QAAgBpB,EAAQuB,iBAAiBxE,KAAKkE,gBAAiB,EAAGK,GAAzD5G,MAE3C8G,MAAO,WACHnB,EAAQU,UAAUU,GAAGD,MAAME,KAAK3E,MAChCkD,EAAclD,KAAKoE,SACfQ,QAAS,OACTP,QAAgBrE,KAAKkE,gBAAZvG,MAGjBkH,OAAQ,WACJvB,EAAQU,UAAUU,GAAGD,MAAME,KAAK3E,MAChCkD,EAAclD,KAAKoE,SAAWC,QAAgBrE,KAAKkE,gBAAZvG,QAW3C6F,EAAgBP,EAAQrD,MAAMC,QAC9BC,KAAM,SAAUgF,EAAWC,EAAc9D,GACrCjB,KAAKiB,QAAUmB,KAAepC,KAAKiB,QAASA,GAC5CjB,KAAK8E,UAAYA,EACjB9E,KAAK+E,aAAeA,CACpB,IAAIC,GAAU9B,EAAc4B,GACxB,cACA,cAEJ9E,MAAKiF,cACDC,IAAKF,EAAQG,WACbC,KAAMJ,EAAQK,aAElBrF,KAAKsF,iBACLR,EAAUlD,YAAY5B,KAAKoE,UAE/BkB,eAAgB,WAAA,GACRlB,GAAUpE,KAAKoE,QAAU1B,EAAU,mBAAoB,2DACvD6C,EAAUvF,KAAKuF,QAAU7C,EAAU,6BACnC8C,EAASxF,KAAKwF,OAAS9C,EAAU,WACrC6C,GAAQE,UAAY,SACpBrB,EAAQxC,YAAY2D,GACpBnB,EAAQxC,YAAY4D,IAExBE,KAAM,SAAUC,EAAMC,EAAIC,GAApB,GAmCEC,GAlCAC,EAAM/F,KACNoE,EAAU2B,EAAI3B,QACdnD,EAAU8E,EAAI9E,QACduE,EAASO,EAAIP,OACbD,EAAUQ,EAAIR,QACdS,EAAS/C,EAAQgD,OAAO9C,EAAOwC,GAAQxC,EAAOyC,EAAKD,GAAQ,GAC3DO,EAA6B,GAAfL,EAAKhH,QACnBsH,EAASN,EAAKO,SAASC,EAAIH,EAC3BI,EAAST,EAAKO,SAASC,EACvBE,EAAWD,EAASH,EACpBK,EAAQvF,EAAQwF,IAAMxF,EAAQyF,IAC9BC,EAAQJ,EAAWC,EACnBI,EAASZ,EAAS/E,EAAQyF,IAC1BhJ,EAAOsC,KAAK+E,aAAa8B,KAAKC,OAAO7F,EAAQ6F,OAAQnB,EAAMC,GAC3DmB,EAAW9D,EAAQ+D,YAAY/F,EACnCjB,MAAKiH,mBACAjH,KAAKkH,WACNhE,EAAckB,GACV+C,WAAY,SACZvC,QAAS,UAEb5E,KAAKkH,UAAW,GAEhBH,IACArJ,EAAOqJ,GACHpB,KAAMA,EACNC,GAAIA,KAGZL,EAAQE,UAAY/H,EACpBwF,EAAcqC,GACVH,KAAMS,EAAKO,SAASC,EAAId,EAAQzD,YAAc,EAC9CoD,IAAKW,EAAKuB,KAEVtB,EAAe5C,EAAcqC,GAC7B,YACA,iBACA,WAEJrC,EAAcsC,GACV3G,MAAOqH,EACPd,KAAMe,EAASS,EAASD,EACxBzB,IAAKW,EAAKuB,GAAKtB,EAAauB,UAAYvB,EAAawB,eAAiBxB,EAAahH,OAAS,IAEhGoE,EAAckB,GAAW+C,WAAY,aAEzCF,iBAAkB,WACVjH,KAAKuH,cACLC,aAAaxH,KAAKuH,cAElBvH,KAAKyH,gBACLzH,KAAKyH,eAAe5C,UAG5B6C,KAAM,WACF,GAAIC,GAAS3H,IACbA,MAAKiH,mBACLjH,KAAKuH,aAAeK,WAAW,WAC3BD,EAAOT,UAAW,EAClBS,EAAOF,eAAiB,GAAIlE,GAAiBoE,EAAOvD,SACpDuD,EAAOF,eAAexD,QACtB0D,EAAOF,eAAeI,QACvB7H,KAAKiB,QAAQ6G,YAEpBC,QAAS,WACL/H,KAAKiH,mBACDjH,KAAK8E,WACL9E,KAAK8E,UAAU5C,YAAYlC,KAAKoE,eAE7BpE,MAAK8E,gBACL9E,MAAK+E,mBACL/E,MAAKoE,cACLpE,MAAKuF,cACLvF,MAAKwF,UAGpBvC,EAAQ+E,kBAAkBxE,GACtBsD,OAAQ,gBACRgB,UAAW,MAEXrE,EAAiB,aACjBC,EAAiBD,EACjBE,GACAD,eAAgBA,EAChBD,eAAgBA,GAEhBG,EAAoB,EACpBC,EAAYZ,EAAQrD,MAAMC,QAC1BC,KAAM,SAAUmI,GAAV,GAEEhH,GACAiH,EAQAnF,CAVJ/C,MAAKiI,MAAQA,EACThH,EAAUjB,KAAKiB,QAAUmB,KAAepC,KAAKiB,QAASgH,EAAMhH,QAAQkH,WACpED,EAASjH,EAAQiH,OACjBA,IACAA,EAAOvC,KAAO3F,KAAKoI,UAAUF,EAAOvC,MACpCuC,EAAOtC,GAAK5F,KAAKoI,UAAUF,EAAOtC,KAEjC3C,EAAQoF,QAAQpH,EAAQqH,KAAKC,WAC9BtH,EAAQqH,KAAKC,QAAUtH,EAAQsH,SAGnCvI,KAAKwI,cAAgB,GAAIvF,GAAQwF,iBAAiBzI,MAAO+C,KAAUA,EAAIK,EAAiBsF,MAAQ,QAAS3F,EAAIK,EAAiBuF,UAAY,WAAY5F,EAAIK,EAAiBwF,MAAQ,QAAS7F,EAAIK,EAAiByF,UAAY,WAAY9F,IACzOkF,EAAMa,YAAY9I,KAAKwI,gBAE3BJ,UAAW,SAAU/H,GACjB,MAAO4C,GAAQmF,UAAUpI,KAAKiI,MAAMlD,aAAa8B,KAAMxG,IAE3D0I,MAAO,WACC/I,KAAKgJ,YACLhJ,KAAKgJ,UAAUjB,UACf/H,KAAKgJ,UAAY,MAEjBhJ,KAAKsI,OACLtI,KAAKsI,KAAKP,UACV/H,KAAKsI,KAAO,OAGpBP,QAAS,WACD/H,KAAKiI,QACLjI,KAAKiI,MAAMgB,eAAejJ,KAAKwI,qBACxBxI,MAAKiI,OAEhBjI,KAAK+I,SAETG,OAAQ,WACJlJ,KAAKmJ,cACLnJ,KAAKoJ,iBAETA,cAAe,WAAA,GACPrD,GAAM/F,KACNiI,EAAQlC,EAAIkC,MACZhH,EAAU8E,EAAI9E,QACdoI,EAAOrJ,KAAKsJ,WACZC,EAAQF,EAAKG,eACb9C,EAAM6C,EAAM7C,IACZD,EAAM8C,EAAM9C,IACZgD,EAAQxI,EAAQiH,OAChBvC,EAAO8D,EAAM9D,KACbC,EAAK6D,EAAM7D,GACX8D,EAAaD,EAAMC,WACnBC,EAAY7G,EAAMuG,EACS,KAA3BA,EAAKO,oBAGT5J,KAAK+I,QACLY,EAAUnI,IAAM6H,EAAK7H,IACrBxB,KAAKgJ,UAAY,GAAI/F,GAAQ4G,UAAU5B,EAAO0B,GAC1CjD,IAAKA,EACLD,IAAKA,EACLd,KAAMA,GAAQe,EACdd,GAAIA,GAAMa,EACViD,WAAYzG,EAAQ6G,eAAeJ,GAAcK,KAAM,SACvDxB,QAAStH,EAAQsH,SAClB,GAAItF,GAAQwF,iBAAiBzI,MAC5BgK,YAAa,eACb9B,OAAQ,UACR+B,UAAW,gBAEXhJ,EAAQqH,KAAKC,UACbvI,KAAKsI,KAAO,GAAI9E,GAAcyE,EAAM7D,QAAS6D,EAAMlD,cAC/C2B,IAAKA,EACLD,IAAKA,EACLM,SAAU9D,EAAQ+D,YAAY/F,EAAQqH,MACtCxB,OAAQ7F,EAAQqH,KAAKxB,YAIjCoD,SAAU,WAAA,GAWFtE,GAVAuE,EAAWnK,KAAKiI,MAAMmC,iBAAgB,GACtCf,EAAOc,EAASE,kBAAkB3G,GAClCqC,EAAMsD,EAAKG,eACX9C,EAAMX,EAAIW,IACVD,EAAMV,EAAIU,IACVyB,EAASlI,KAAKiB,QAAQiH,WACtBvC,EAAOuC,EAAOvC,MAAQe,CACtBf,GAAOe,IACPf,EAAOe,GAEPd,EAAKsC,EAAOtC,IAAMa,EAClBb,EAAKa,IACLb,EAAKa,GAETzG,KAAKiB,QAAQiH,OAAS9F,KAAe8F,GACjCvC,KAAMA,EACNC,GAAIA,IAER5F,KAAKsK,cAETnB,YAAa,SAAUoB,GACnB,GAAIJ,GAAWnK,KAAKiI,MAAMuC,SACtBL,IACAA,EAASjB,OAAOjG,EAAQwH,KAAKN,EAASO,OAAQH,IAGtDI,aAAc,WAAA,GACN1C,GAAQjI,KAAKiI,MACbkC,EAAWlC,EAAMuC,UACjBI,EAAaT,EAASO,MAAMG,MAAM,KACtCV,GAASW,UAAY7C,EAAMhH,QAAQ8J,OACnCZ,EAASlJ,QAAQ+J,aAAe/C,EAAMhH,QAAQ+J,aAC9Cb,EAASc,yBACTd,EAASjB,OAAO0B,IAEpBM,MAAO,SAAUC,GAAV,GAWCC,GASAzF,EACAC,EApBAG,EAAM/F,KACNiI,EAAQlC,EAAIkC,MACZe,EAAYjD,EAAIiD,UAChBqC,EAASpD,EAAMqD,kBAAkBH,EAAEI,eACnCC,EAAgBxL,KAAKsJ,WACrBmC,EAAYD,EAAchC,eAC1BkC,EAAcF,EAAcG,KAAKnK,IAAIoK,cAAcP,GACnDhC,EAAOpB,EAAMuC,UAAUQ,aACvBxE,EAAQ2E,EAAEU,WAAWxC,EAAKpI,QAAQ6K,MAClC5D,EAASlI,KAAKiB,QAAQiH,MAErB1B,KAASkF,GAAgB1C,IAI1BoC,EADAlD,EAAOvC,MAAQuC,EAAOtC,GACXzC,EAAO+E,EAAOtC,IAAMzC,EAAO+E,EAAOvC,MAElCxC,EAAO6F,EAAU/H,QAAQ2E,IAAMzC,EAAO6F,EAAU/H,QAAQ0E,MAEnEA,EAAO1C,EAAQgD,OAAOhD,EAAQ8I,WAAW5I,EAAOqD,EAAME,KAAM+E,EAAU/E,IAAKvD,EAAOsI,EAAUhF,KAAO2E,IACnGxF,EAAK3C,EAAQgD,OAAOhD,EAAQ8I,WAAW5I,EAAOwC,GAAQyF,EAAUjI,EAAOsI,EAAU/E,KAAO0E,EAAUK,EAAUhF,MAChHzG,KAAKiB,QAAQiH,QACTvC,KAAMA,EACNC,GAAIA,GAEJ5F,KAAKiB,QAAQ+K,WACbhM,KAAKsK,aACLtK,KAAK2K,gBAET3B,EAAUiD,IAAItG,EAAMC,GACpB5F,KAAKkM,SAASvG,EAAMC,KAExBuG,SAAU,WACNnM,KAAKsK,aACLtK,KAAKoM,SACLpM,KAAK2K,eACD3K,KAAKsI,MACLtI,KAAKsI,KAAKZ,QAGlB2E,cAAe,WAAA,GACPtG,GAAM/F,KACNsM,EAAwBvG,EAAIiD,UAAU/H,QACtC0E,EAAO2G,EAAsB3G,KAC7BC,EAAK0G,EAAsB1G,GAC3BsC,EAASnC,EAAI9E,QAAQiH,MACzBA,GAAOvC,KAAOA,EACduC,EAAOtC,GAAKA,GAEhB0E,WAAY,WAAA,GAMJrC,GACAsE,EACA5G,EACAC,EACK4G,EACDnD,EAVJtD,EAAM/F,KACNkI,EAASnC,EAAI9E,QAAQiH,MAQzB,KAPe,SAAXA,IACAA,MAEAD,EAAQlC,EAAIkC,MACZsE,EAAUtE,EAAMhH,QAAQ+J,aACxBrF,EAAOuC,EAAOvC,KACdC,EAAKsC,EAAOtC,GACP4G,EAAM,EAAGA,EAAMD,EAAQ7N,OAAQ8N,IAChCnD,EAAOkD,EAAQC,GACfnD,EAAKsC,OAASlI,IACd4F,EAAK3C,IAAMf,EACX0D,EAAK5C,IAAMb,IAIvBwG,OAAQ,WAAA,GAOA9C,GACAmD,EAKIC,EAZJ3G,EAAM/F,KACNiI,EAAQlC,EAAIkC,MACZC,EAASnC,EAAI9E,QAAQiH,MACpBD,GAAM0E,kBAAkB,sBAGzBrD,EAAWtJ,KAAKsJ,WAChBmD,GACA9G,KAAMuC,EAAOvC,KACbC,GAAIsC,EAAOtC,IAEe,aAA1B0D,EAASrI,QAAQ2L,OACbF,EAAc,GAAIzJ,GAAQ4J,iBAAiBzK,GAAa0K,SAAU,OAAS7E,EAAMhH,QAAQ+J,aAAa,IACtG+B,YACI7E,EAAOvC,KACPuC,EAAOtC,MAEXqC,EAAMlD,cAAc9D,QACxBwL,EAAK9G,KAAO1C,EAAQ+J,YAAYN,EAAYhG,KAAMgG,EAAYO,aAAcP,EAAYI,UACxFL,EAAK7G,GAAK3C,EAAQ+J,YAAYN,EAAYjG,IAAKiG,EAAYO,aAAcP,EAAYI,WAEzF9M,KAAKiI,MAAMiF,QAAQ,kBAAmBT,KAE1CU,MAAO,SAAUhC,GAAV,GAYCiC,GACAC,EAZAtH,EAAM/F,KACNqJ,EAAOtD,EAAIkC,MAAMuC,UAAUQ,aAC3BhC,EAAYjD,EAAIiD,UAChBsE,EAAcvH,EAAI9E,QAClBiH,EAASoF,EAAYpF,OACrB8D,EAAWsB,EAAYtB,SACvB1C,EAAWtJ,KAAKsJ,WAChBiE,EAAQpC,EAAEoC,KACTvE,KAGDoE,EAAS9D,EAASkE,cAAcxE,EAAU/H,QAAQ0E,MAClD0H,EAAO/D,EAASkE,cAAcxE,EAAU/H,QAAQ2E,IACpDuF,EAAEI,cAAckC,iBACZC,KAAKC,IAAIJ,GAAS,IAClBA,GAAS3J,GAETyJ,EAAOD,EAAS,GAChBpE,EAAU4E,OAAOL,GACjBvN,KAAKqM,kBAELhD,EAAKpI,QAAQyF,IAAMwB,EAAOvC,KAC1BuC,EAAOvC,KAAO0D,EAAKwE,YAAY1C,EAAEoC,OAAO7G,KAExCsF,IACAhM,KAAKsK,aACLtK,KAAK2K,gBAET3B,EAAUiD,IAAI/D,EAAOvC,KAAMuC,EAAOtC,IAClC5F,KAAKkM,SAASlM,KAAKiB,QAAQiH,OAAOvC,KAAM3F,KAAKiB,QAAQiH,OAAOtC,MAEhEkI,SAAU,SAAU3C,GAChBnL,KAAKmM,SAAShB,IAElBe,SAAU,SAAUvG,EAAMC,GACtB,GAAIuE,GAAWnK,KAAKiI,MAAMuC,SACtBxK,MAAKsI,MACLtI,KAAKsI,KAAK5C,KAAKC,EAAMC,EAAIuE,EAAS4D,kBAG1CC,aAAc,SAAU7C,GACpB,MAAOnL,MAAKiI,MAAM+F,aAAa7C,IAEnC8C,QAAS,SAAU9C,GAEf,MADAnL,MAAKkM,SAASf,EAAExF,KAAMwF,EAAEvF,IACjB5F,KAAKiI,MAAMgG,QAAQ9C,IAE9B+C,WAAY,SAAU/C,GAQlB,MAPInL,MAAKsI,MACLtI,KAAKsI,KAAKZ,OAEd1H,KAAKqM,gBACLrM,KAAKsK,aACLtK,KAAKoM,SACLpM,KAAK2K,eACE3K,KAAKiI,MAAMiG,WAAW/C,IAEjC7B,SAAU,WACN,GAAIa,GAAWnK,KAAKiI,MAAMuC,SAC1B,IAAIL,EACA,MAAOA,GAASE,kBAAkB3G,IAG1CwE,OAAQ,SAAUvC,EAAMC,GACpB,GAAIsC,GAASlI,KAAKiB,QAAQiH,MAS1B,OARIvC,IAAQC,IACRsC,EAAOvC,KAAO3F,KAAKoI,UAAUzC,GAC7BuC,EAAOtC,GAAK5F,KAAKoI,UAAUxC,GAC3B5F,KAAKsK,aACLtK,KAAKoM,SACLpM,KAAK2K,eACL3K,KAAKgJ,UAAUiD,IAAItG,EAAMC,KAGzBD,KAAMuC,EAAOvC,KACbC,GAAIsC,EAAOtC,OAIvB/B,EAAUI,MAAQ,SAAUhD,EAASkN,GAAnB,GAWVC,GACA1D,EACA2D,CAZY,UAAZpN,IACAA,MAEiB,SAAjBkN,IACAA,MAEAlN,EAAQqN,SAGZrN,EAAQqN,QAAS,EACbF,EAAchM,KAAe+L,EAAahG,UAAWlH,EAAQkH,WAC7DuC,EAAQzJ,EAAQyJ,SAAW6D,OAAOtN,EAAQyJ,OAC1C2D,EAAcjM,KAAegM,EAAYzC,MAAQG,KAAMrI,IACtD2K,EAAY7F,UACb8F,EAAY9F,SAAU,EACtB8F,EAAYvP,OAAS,IAEzB4L,EAAMvM,KAAKkQ,GACXxK,EAAU2K,WAAWvN,EAASmN,GAC9BvK,EAAU4K,aAAaxN,EAASmN,EAAaD,KAEjDtK,EAAU2K,WAAa,SAAUvN,EAASmN,GAAnB,GACfrD,GAASqD,EAAYrD,WACrB2D,EAAezN,EAAQ+J,gBAAkBuD,OAAOtN,EAAQ+J,cACxD2D,EAAY1N,EAAQ2N,aAAeL,OAAOtN,EAAQ2N,WAClDC,EAAsB5L,EAAQ6L,mBAAmB/D,EAAQ3H,EAAiB2L,uBAC1EC,EAA6C,IAA/BH,EAAoBnQ,OAClCuQ,EAAO7M,GACPwK,KAAM,OACNjB,KAAMlI,EACNyL,iBAAkBF,EAClBG,UAAWH,EACXI,WAAW,EACXC,YAAc9G,SAAS,GACvBhD,SAAWgD,SAAS,GACpB+G,QAAUhL,KAAM,GAChBiL,SAAUnB,EAAYoB,iBACtBC,mBACIC,SAAU,GACVC,OACI,EACA,GAEJC,MACI,EACA,GAEJC,SACAC,QAAS,GACTC,OAAQ,MAGZC,EAAO5B,EAAYpD,YACvB0D,GAAavQ,KAAKiE,KAAe6M,GAAQgB,cAAe,KAAOD,GAC3DlE,KAAMpI,EACNwM,MAAO,KACPpD,SAAU,MACVG,aAAc,OACdqC,QAAU/G,SAAS,GACnB8G,YAAc9G,SAAS,KACvBnG,KAAe6M,EAAMe,GACrBlE,KAAMpI,EAAiB,UACvBuM,cAAe,GACfhD,aAAc,OACdqC,QAAUa,SAAU,IACpBC,aACAX,mBAAqBC,YACrBW,UAAU,IACVjO,KAAe6M,EAAMe,GACrBlE,KAAMpI,EAAiB,SACvBuM,cAAe,IACfZ,YAAcxQ,MAAO,IACrBuR,aACAF,MAAO,KACPZ,QACI/G,SAAS,EACT+H,QAAQ,GAEZD,UAAU,KAEd1B,EAAUxQ,KAAKiE,GACX0J,KAAMpI,EACNiI,KAAMlI,EACN8M,gBAAkBhI,SAAS,GAC3BA,SAAS,GACV6F,EAAYQ,aAEnB/K,EAAU4K,aAAe,SAAUxN,EAASmN,EAAaD,GAAhC,GAKZ3B,GAJLzB,EAAS9J,EAAQ8J,OAAS9J,EAAQ8J,WAClCyF,KAAqBjC,OAAOH,EAAYrD,YACxC0F,EAAetC,EAAasC,aAC5BC,EAAWtC,EAAYuC,cAC3B,KAASnE,EAAM,EAAGA,EAAMgE,EAAgB9R,OAAQ8N,IAC5CzB,EAAO5M,KAAKiE,GACRwO,MAAOH,EAAajE,EAAMiE,EAAa/R,QACvCmS,cAAezC,EAAY0C,UAC3BC,iBAAiB,EACjBxL,SAAWgD,SAAS,IACrBmI,EAAUF,EAAgBhE,IACzBnD,KAAM3F,EACNsH,aAActH,EACd6L,SAAUnB,EAAYoB,qBAU9B1L,EAAsB,GACtBC,EAAaV,EAAMxD,QACnBmR,cAAe,SAAU/P,EAASkN,GAAnB,GACPtP,GAAQoE,EAAQgO,YAAYjR,KAAKoE,SAASvF,OAASuE,EAAiB8N,cACpEC,EAAQhD,EACRiD,GACAT,gBAAkBE,cAAe5P,EAAQ6P,WACzCO,cACIrG,cACIc,KAAM,UACNyE,gBAAkBhI,SAAS,GAC3B+G,QAAUhL,KAAM,GAChB+K,YAAc9G,SAAS,GACvB0H,cAAevC,KAAK4D,MAAMzS,EAAQiF,KAI1CqN,KACAA,EAAQ/O,KAAe+O,EAAOC,IAElCvN,EAAUI,MAAMhD,EAASkQ,GACzB9N,EAAMqB,GAAGsM,cAAcrM,KAAK3E,KAAMiB,EAASkQ,IAE/CI,iBAAkB,SAAUnN,GACxBnB,EAAQuO,SAASpN,EAAS,yBAE9BqN,WAAY,SAAUxQ,GAClBjB,KAAK0R,mBACLrO,EAAMqB,GAAG+M,WAAW9M,KAAK3E,KAAMiB,IAEnC0Q,oBAAqB,WACjB,GAAIC,GAAc5R,KAAKiB,QAAQ2Q,WAC/B5R,MAAKiB,QAAQ2Q,aAAc,EAC3B5R,KAAK6R,cACL7R,KAAKiB,QAAQ2Q,YAAcA,GAE/BE,QAAS,WACL9R,KAAK2R,uBAETI,QAAS,WACL,GAAI5J,GAAYnI,KAAKmI,WAChBnI,KAAKgS,UAAY7J,GAAaA,EAAUlH,QAAQgR,cACjD9J,EAAUwC,eAEV3K,KAAK6R,eAGbG,OAAQ,WAAA,GACA/Q,GAAUjB,KAAKiB,QACf8J,KAAYwD,OAAOtN,EAAQ8J,OAAQ9J,EAAQkH,UAAU4C,QACrDmH,EAAcjP,EAAQkP,KAAKpH,EAAQ,SAAUqH,GAC7C,MAAOA,IAAKA,EAAE7J,UACf7J,OACC2T,EAAQrS,KAAKsS,eAAiBJ,CAElC,OADAlS,MAAKsS,aAAeJ,EACbG,GAEXR,YAAa,WACT,GAAI1J,GAAYnI,KAAKmI,SAChBA,KACDA,EAAYnI,KAAKmI,UAAY,GAAItE,GAAU7D,MAC3CA,KAAKkN,QAAQ,oBAAsB/E,UAAWA,KAElDA,EAAUY,QACVZ,EAAU+B,WACV7G,EAAMqB,GAAGqN,QAAQpN,KAAK3E,MACtBmI,EAAUiB,iBAEdmJ,oBAAqB,SAAUlH,GAAV,GACblB,GAAWnK,KAAKwK,UAChBmB,EAAOxB,EAASqI,YAAYnH,EAC5BM,IAAQA,EAAK1K,QAAQ6K,OAASrI,EAC9BzD,KAAKyS,oBAELpP,EAAMqB,GAAG6N,oBAAoB5N,KAAK3E,KAAMqL,IAGhDqH,eAAgB,WACZrP,EAAMqB,GAAGgO,eAAe/N,KAAK3E,MAC7BA,KAAK2S,2BAETA,wBAAyB,WAAA,GAEjB5F,GACK6F,EACDvJ,EAHJwJ,KAAiBtE,OAAOvO,KAAKiB,QAAQ+J,aAEzC,KAAS4H,EAAS,EAAGA,EAASC,EAAYnU,OAAQkU,IAC1CvJ,EAAOwJ,EAAYD,GACnBvJ,EAAKyC,OAASpI,EACdqJ,EAAa1D,EAAK0D,WACXA,GAAc1D,EAAKsC,OAASlI,IACnC4F,EAAK0D,WAAaA,IAI9B2E,iBAAkB,WACV1R,KAAKmI,YACLnI,KAAKmI,UAAUJ,UACf/H,KAAKmI,UAAY,OAGzBJ,QAAS,WACL/H,KAAK0R,mBACLrO,EAAMqB,GAAGqD,QAAQpD,KAAK3E,OAE1B8S,mBAAoB,SAAU3H,GAAV,GACZE,GAASrL,KAAKsL,kBAAkBH,GAChCQ,EAAO3L,KAAKwK,UAAUgI,YAAYnH,EACtC,OAAOhI,GAAMqB,GAAGoO,mBAAmBnO,KAAK3E,KAAMmL,IAAMQ,GAAQA,EAAK1K,QAAQ6K,OAASrI,KAG1FR,EAAQ+E,kBAAkBjE,GACtB+M,UAAW,OACXO,cACIrG,cACI4B,KAAM,OACNE,SAAU,MACVqC,WAAW,GAEfP,WACImE,aAAa,EACbzD,QAAUxI,OAAQ,OAG1BqB,WACID,UACAyI,gBACIqC,SAAWzK,SAAS,GACpBhD,SAAWgD,SAAS,GACpB0K,MAAQpU,MAAO,IAEnByJ,QACAC,SAAS,GAEbhD,SAAWgD,SAAS,GACpB2K,QAAU3K,SAAS,KAEvB7I,MAAM0C,WAAW1C,MAAMuD,SACnBU,UAAWA,EACXE,UAAWA,EACXL,cAAeA,EACfO,WAAYA,QAGJ,kBAAVxG,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,6BAA8B,mCAAoCD,IAC3E,YACG,SAAUE,GAsLP,QAAS2V,GAAYxN,EAAMC,GACvB,QAEQwN,MAAO,OACPC,SAAU,MACVhT,MAAOsF,IAGPyN,MAAO,OACPC,SAAU,KACVhT,MAAOuF,IAhMtB,GACOlG,GAAQD,OAAOC,MACfuD,EAAUvD,EAAMuD,QAChBqQ,EAAwBrQ,EAAQqQ,sBAChCjQ,EAAQJ,EAAQsQ,GAAGlQ,MACnBmQ,EAAkBvQ,EAAQc,WAC1BJ,EAAYV,EAAQU,UACpBD,EAAiBC,EAAUD,eAC3BD,EAAiBE,EAAUF,eAC3BrB,EAAa1C,EAAM0C,WACnBiG,EAAUpF,EAAQoF,QAClBoL,EAAQjW,EAAEiW,MACVC,EAAS,SACTC,EAAwBL,EAAsBzT,QAC9C+T,YACIC,gBAAiB,qBACjBC,iBAAkB,yBAGtB/P,EAAaV,EAAMxD,QACnBoB,SACI6K,KAAM,aACNgF,UAAW,OACXO,cACIrG,cACI4B,KAAM,OACNE,SAAU,MACVqC,WAAW,GAEfP,WACImE,aAAa,EACbzD,QAAUxI,OAAQ,OAG1BqB,WACID,UACAyI,gBACIqC,SAAWzK,SAAS,GACpBhD,SACIgD,SAAS,EACTxB,SAAU,sCAEdkM,MAAQpU,MAAO,IAEnByJ,QACAC,SAAS,GAEbhD,SAAWgD,SAAS,GACpB2K,QAAU3K,SAAS,GACnBwL,yBAAyB,GAE7BC,aAAc,SAAU/S,EAASkN,GAC7BnO,KAAKiU,sBAAsBhT,GAC3BjB,KAAKkU,UAAY,GAAIV,GAAgBxT,KAAKoE,QAAQ,GAAInD,EAASkN,GAC3DgG,SAAU,GAAIR,GAAsB3T,MACpCoU,OAAQpU,KACRqU,IAAKrU,KAAKsU,YAGlBL,sBAAuB,SAAUhT,GAAV,GACfsT,GAAmBtT,EAAQkH,cAC3BqM,EAAU9U,EAAM8U,QAChBC,EAAUD,EAAQE,MAClBC,EAAYH,EAAQI,QAAQC,OAChCzS,GAAWmS,GACP/E,kBAAmB+E,EAAiBO,WACpC7C,cAAesC,EAAiBO,WAChC9I,UAAWyI,IAAYE,KAG/BI,gBAAiB,SAAUC,GAAV,GAGL5I,GACAzG,EACAC,EACAqP,EALJhU,EAAU+T,MAAmBF,EAAa7T,EAAQ6T,WAAYI,EAAqBJ,GAAcA,EAAWK,gBAAiB7L,KAAciF,OAAOtN,EAAQ+J,cAAc,GAAIoD,EAAcnN,EAAQkH,cAAiBD,EAASkG,EAAYlG,OAAQkN,EAAYlN,GAAUA,EAAOvC,MAAQuC,EAAOtC,EAC5RsP,IAAsBE,IAClBhJ,KAAYmC,OAAOuG,EAAW1I,YAC9BzG,EAAOjG,EAAM0I,UAAUF,EAAOvC,MAC9BC,EAAKlG,EAAM0I,UAAUF,EAAOtC,IAC5BqP,EAAY,GAAIhS,GAAQ4J,iBAAiBzK,GAAa0K,SAAU,OAASxD,GACzEyD,YACIpH,EACAC,KAEJlG,GACJoV,EAAW1I,OAAS+G,EAAY8B,EAAUzO,QAAQE,IAAKd,GAAI2I,OAAOnC,IAEtE/I,EAAMqB,GAAGqQ,gBAAgBpQ,KAAK3E,KAAMgV,IAExCK,oBAAqB,SAAUlK,GAC3BnL,KAAKkU,UAAY/I,EAAEiJ,OACnBpU,KAAKiB,QAAUkK,EAAEiJ,OAAOnT,QACxBjB,KAAKsV,WAAatV,KAAKmI,UAAYgD,EAAEhD,UACrCnI,KAAKuV,4BAETA,yBAA0B,WAAA,GAClBhB,GAAmBvU,KAAKiB,QAAQkH,UAChCoH,EAAWgF,EAAiBhF,SAC5BiG,EAAYjB,EAAiBO,UAC7BU,KACAxV,KAAKyV,6BAA+BzV,KAAKyV,8BAAgChC,EAAMzT,KAAK0V,wBAAyB1V,MAC7GA,KAAK2V,qBAAuBjW,EAAMkW,KAAKC,WAAWC,OAAON,GAAWO,KAAKrC,EAAQ1T,KAAKyV,8BACjFpN,EAAQkH,KACTA,EAAWvP,KAAKiB,QAAQsO,UAExBA,GACAvP,KAAK2V,qBAAqBK,UAItCC,qBAAsB,SAAUlL,EAAQ6K,GACpC,GAAIM,GAAUC,EAAeC,EAAerL,EAAOrM,MACnD,KAAKwX,EAAW,EAAGA,EAAWE,EAAcF,IACxCC,EAAgBpL,EAAOmL,GACnBC,EAAc9M,MAAQ3F,GAAkB1D,KAAKqW,YAAYF,KACzDA,EAAcP,KAAOA,IAIjCF,wBAAyB,WAAA,GACoE9C,GAAoF0D,EAAaC,EAiBlLpO,EAjBJF,EAAQjI,KAAMwW,EAAWvO,EAAMiM,UAAWxF,EAAezG,EAAMhH,QAAQ+J,aAAsByL,EAAa/H,EAAahQ,OAAQkX,EAAO3N,EAAM0N,qBAAqBe,MAKrK,KAJA1W,KAAKiW,qBAAqBhO,EAAMhH,QAAQ8J,OAAQ6K,GAC5C3N,EAAM0O,eACN3W,KAAKiW,qBAAqBhO,EAAM0O,cAAef,GAE9ChD,EAAS,EAAGA,EAAS6D,EAAY7D,IAClC0D,EAAc5H,EAAakE,GACvB0D,EAAY3K,MAAQlI,IAChB6S,EAAYxK,MAAQpI,GACpBuE,EAAM2O,kBAAkBN,EAAaV,EAAMhD,GAC3C2D,EAAiBD,EAAYvJ,YAE7BuJ,EAAYvJ,WAAawJ,EAIjCC,GAASK,SACL1O,EAAYnI,KAAKmI,UACrBA,EAAUe,SACVf,EAAU+B,aACLjC,EAAMhH,QAAQ6T,YAAc7M,EAAMhH,QAAQ6T,YAAc7M,EAAM6O,aAC/D3O,EAAUwC,iBAItBoM,gBAAiB,WACb1T,EAAMqB,GAAGqS,gBAAgBpS,KAAK3E,MAC1BA,KAAKkU,WACLlU,KAAKkU,UAAUvB,2BAGvBqE,eAAgB,WACZ3T,EAAMqB,GAAGsS,eAAerS,KAAK3E,MAC7BA,KAAK8W,YAAa,GAEtBrF,WAAY,SAAUxQ,GAClBjB,KAAKiX,6BACLjX,KAAKiU,sBAAsBhT,GAC3BjB,KAAKkU,UAAUxC,mBACfrO,EAAMqB,GAAG+M,WAAW9M,KAAK3E,KAAMiB,IAEnCiW,mBAAoB,SAAU/L,GAC1BnL,KAAK8U,WAAW1I,OAAO+G,EAAYhI,EAAExF,KAAMwF,EAAEvF,MAEjD+G,iBAAkB,SAAUwK,GAAV,GAENrC,GACAI,CAFR,OAAIjS,GAAQmU,QAAQ,kBAAmBD,IAC/BrC,EAAa9U,KAAK8U,WAClBI,EAAqBJ,GAAcA,EAAW7T,QAAQkU,gBACnDD,GAAsBlV,KAAKiB,QAAQkH,UAAU2M,YAEjDzR,EAAMqB,GAAGiI,iBAAiBhI,KAAK3E,KAAMmX,IAEhDF,2BAA4B,WACxB,GAAII,GAAsBrX,KAAK2V,oBAC3B0B,KACAA,EAAoBC,OAAO5D,EAAQ1T,KAAKyV,oCACjCzV,MAAK2V,uBAGpB5N,QAAS,WACL1E,EAAMqB,GAAGqD,QAAQpD,KAAK3E,MACtBA,KAAKiX,+BAGbhU,GAAQsQ,GAAGgE,OAAOxT,IAepBtE,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,uBACH,kCACA,6BACDD,IACL,aAQkB,kBAAVC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dataviz.stock.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/stock/kendo-stock-chart', ['kendo.dataviz.chart'], f);\n}(function () {\n    (function () {\n        window.kendo.dataviz = window.kendo.dataviz || {};\n        var dataviz = kendo.dataviz;\n        var elementStyles = dataviz.elementStyles;\n        var deepExtend = dataviz.deepExtend;\n        var toTime = dataviz.toTime;\n        var datavizConstants = dataviz.constants;\n        var Chart = dataviz.Chart;\n        var drawing = kendo.drawing;\n        var FadeOutAnimation = drawing.Animation.extend({\n            setup: function () {\n                this._initialOpacity = parseFloat(elementStyles(this.element, 'opacity').opacity);\n            },\n            step: function (pos) {\n                elementStyles(this.element, { opacity: String(dataviz.interpolateValue(this._initialOpacity, 0, pos)) });\n            },\n            abort: function () {\n                drawing.Animation.fn.abort.call(this);\n                elementStyles(this.element, {\n                    display: 'none',\n                    opacity: String(this._initialOpacity)\n                });\n            },\n            cancel: function () {\n                drawing.Animation.fn.abort.call(this);\n                elementStyles(this.element, { opacity: String(this._initialOpacity) });\n            }\n        });\n        function createDiv(className, style) {\n            var div = document.createElement('div');\n            div.className = className;\n            if (style) {\n                div.style.cssText = style;\n            }\n            return div;\n        }\n        var NavigatorHint = dataviz.Class.extend({\n            init: function (container, chartService, options) {\n                this.options = deepExtend({}, this.options, options);\n                this.container = container;\n                this.chartService = chartService;\n                var padding = elementStyles(container, [\n                    'paddingLeft',\n                    'paddingTop'\n                ]);\n                this.chartPadding = {\n                    top: padding.paddingTop,\n                    left: padding.paddingLeft\n                };\n                this.createElements();\n                container.appendChild(this.element);\n            },\n            createElements: function () {\n                var element = this.element = createDiv('k-navigator-hint', 'display: none; position: absolute; top: 1px; left: 1px;');\n                var tooltip = this.tooltip = createDiv('k-tooltip k-chart-tooltip');\n                var scroll = this.scroll = createDiv('k-scroll');\n                tooltip.innerHTML = '&nbsp;';\n                element.appendChild(tooltip);\n                element.appendChild(scroll);\n            },\n            show: function (from, to, bbox) {\n                var ref = this;\n                var element = ref.element;\n                var options = ref.options;\n                var scroll = ref.scroll;\n                var tooltip = ref.tooltip;\n                var middle = dataviz.toDate(toTime(from) + toTime(to - from) / 2);\n                var scrollWidth = bbox.width() * 0.4;\n                var minPos = bbox.center().x - scrollWidth;\n                var maxPos = bbox.center().x;\n                var posRange = maxPos - minPos;\n                var range = options.max - options.min;\n                var scale = posRange / range;\n                var offset = middle - options.min;\n                var text = this.chartService.intl.format(options.format, from, to);\n                var template = dataviz.getTemplate(options);\n                this.clearHideTimeout();\n                if (!this._visible) {\n                    elementStyles(element, {\n                        visibility: 'hidden',\n                        display: 'block'\n                    });\n                    this._visible = true;\n                }\n                if (template) {\n                    text = template({\n                        from: from,\n                        to: to\n                    });\n                }\n                tooltip.innerHTML = text;\n                elementStyles(tooltip, {\n                    left: bbox.center().x - tooltip.offsetWidth / 2,\n                    top: bbox.y1\n                });\n                var tooltipStyle = elementStyles(tooltip, [\n                    'marginTop',\n                    'borderTopWidth',\n                    'height'\n                ]);\n                elementStyles(scroll, {\n                    width: scrollWidth,\n                    left: minPos + offset * scale,\n                    top: bbox.y1 + tooltipStyle.marginTop + tooltipStyle.borderTopWidth + tooltipStyle.height / 2\n                });\n                elementStyles(element, { visibility: 'visible' });\n            },\n            clearHideTimeout: function () {\n                if (this._hideTimeout) {\n                    clearTimeout(this._hideTimeout);\n                }\n                if (this._hideAnimation) {\n                    this._hideAnimation.cancel();\n                }\n            },\n            hide: function () {\n                var this$1 = this;\n                this.clearHideTimeout();\n                this._hideTimeout = setTimeout(function () {\n                    this$1._visible = false;\n                    this$1._hideAnimation = new FadeOutAnimation(this$1.element);\n                    this$1._hideAnimation.setup();\n                    this$1._hideAnimation.play();\n                }, this.options.hideDelay);\n            },\n            destroy: function () {\n                this.clearHideTimeout();\n                if (this.container) {\n                    this.container.removeChild(this.element);\n                }\n                delete this.container;\n                delete this.chartService;\n                delete this.element;\n                delete this.tooltip;\n                delete this.scroll;\n            }\n        });\n        dataviz.setDefaultOptions(NavigatorHint, {\n            format: '{0:d} - {1:d}',\n            hideDelay: 500\n        });\n        var NAVIGATOR_PANE = '_navigator';\n        var NAVIGATOR_AXIS = NAVIGATOR_PANE;\n        var constants = {\n            NAVIGATOR_AXIS: NAVIGATOR_AXIS,\n            NAVIGATOR_PANE: NAVIGATOR_PANE\n        };\n        var ZOOM_ACCELERATION = 3;\n        var Navigator = dataviz.Class.extend({\n            init: function (chart) {\n                this.chart = chart;\n                var options = this.options = deepExtend({}, this.options, chart.options.navigator);\n                var select = options.select;\n                if (select) {\n                    select.from = this.parseDate(select.from);\n                    select.to = this.parseDate(select.to);\n                }\n                if (!dataviz.defined(options.hint.visible)) {\n                    options.hint.visible = options.visible;\n                }\n                var obj;\n                this.chartObserver = new dataviz.InstanceObserver(this, (obj = {}, obj[datavizConstants.DRAG] = '_drag', obj[datavizConstants.DRAG_END] = '_dragEnd', obj[datavizConstants.ZOOM] = '_zoom', obj[datavizConstants.ZOOM_END] = '_zoomEnd', obj));\n                chart.addObserver(this.chartObserver);\n            },\n            parseDate: function (value) {\n                return dataviz.parseDate(this.chart.chartService.intl, value);\n            },\n            clean: function () {\n                if (this.selection) {\n                    this.selection.destroy();\n                    this.selection = null;\n                }\n                if (this.hint) {\n                    this.hint.destroy();\n                    this.hint = null;\n                }\n            },\n            destroy: function () {\n                if (this.chart) {\n                    this.chart.removeObserver(this.chartObserver);\n                    delete this.chart;\n                }\n                this.clean();\n            },\n            redraw: function () {\n                this._redrawSelf();\n                this.initSelection();\n            },\n            initSelection: function () {\n                var ref = this;\n                var chart = ref.chart;\n                var options = ref.options;\n                var axis = this.mainAxis();\n                var ref$1 = axis.roundedRange();\n                var min = ref$1.min;\n                var max = ref$1.max;\n                var ref$2 = options.select;\n                var from = ref$2.from;\n                var to = ref$2.to;\n                var mousewheel = ref$2.mousewheel;\n                var axisClone = clone(axis);\n                if (axis.categoriesCount() === 0) {\n                    return;\n                }\n                this.clean();\n                axisClone.box = axis.box;\n                this.selection = new dataviz.Selection(chart, axisClone, {\n                    min: min,\n                    max: max,\n                    from: from || min,\n                    to: to || max,\n                    mousewheel: dataviz.valueOrDefault(mousewheel, { zoom: 'left' }),\n                    visible: options.visible\n                }, new dataviz.InstanceObserver(this, {\n                    selectStart: '_selectStart',\n                    select: '_select',\n                    selectEnd: '_selectEnd'\n                }));\n                if (options.hint.visible) {\n                    this.hint = new NavigatorHint(chart.element, chart.chartService, {\n                        min: min,\n                        max: max,\n                        template: dataviz.getTemplate(options.hint),\n                        format: options.hint.format\n                    });\n                }\n            },\n            setRange: function () {\n                var plotArea = this.chart._createPlotArea(true);\n                var axis = plotArea.namedCategoryAxes[NAVIGATOR_AXIS];\n                var ref = axis.roundedRange();\n                var min = ref.min;\n                var max = ref.max;\n                var select = this.options.select || {};\n                var from = select.from || min;\n                if (from < min) {\n                    from = min;\n                }\n                var to = select.to || max;\n                if (to > max) {\n                    to = max;\n                }\n                this.options.select = deepExtend({}, select, {\n                    from: from,\n                    to: to\n                });\n                this.filterAxes();\n            },\n            _redrawSelf: function (silent) {\n                var plotArea = this.chart._plotArea;\n                if (plotArea) {\n                    plotArea.redraw(dataviz.last(plotArea.panes), silent);\n                }\n            },\n            redrawSlaves: function () {\n                var chart = this.chart;\n                var plotArea = chart._plotArea;\n                var slavePanes = plotArea.panes.slice(0, -1);\n                plotArea.srcSeries = chart.options.series;\n                plotArea.options.categoryAxis = chart.options.categoryAxis;\n                plotArea.clearSeriesPointsCache();\n                plotArea.redraw(slavePanes);\n            },\n            _drag: function (e) {\n                var ref = this;\n                var chart = ref.chart;\n                var selection = ref.selection;\n                var coords = chart._eventCoordinates(e.originalEvent);\n                var navigatorAxis = this.mainAxis();\n                var naviRange = navigatorAxis.roundedRange();\n                var inNavigator = navigatorAxis.pane.box.containsPoint(coords);\n                var axis = chart._plotArea.categoryAxis;\n                var range = e.axisRanges[axis.options.name];\n                var select = this.options.select;\n                var duration;\n                if (!range || inNavigator || !selection) {\n                    return;\n                }\n                if (select.from && select.to) {\n                    duration = toTime(select.to) - toTime(select.from);\n                } else {\n                    duration = toTime(selection.options.to) - toTime(selection.options.from);\n                }\n                var from = dataviz.toDate(dataviz.limitValue(toTime(range.min), naviRange.min, toTime(naviRange.max) - duration));\n                var to = dataviz.toDate(dataviz.limitValue(toTime(from) + duration, toTime(naviRange.min) + duration, naviRange.max));\n                this.options.select = {\n                    from: from,\n                    to: to\n                };\n                if (this.options.liveDrag) {\n                    this.filterAxes();\n                    this.redrawSlaves();\n                }\n                selection.set(from, to);\n                this.showHint(from, to);\n            },\n            _dragEnd: function () {\n                this.filterAxes();\n                this.filter();\n                this.redrawSlaves();\n                if (this.hint) {\n                    this.hint.hide();\n                }\n            },\n            readSelection: function () {\n                var ref = this;\n                var ref_selection_options = ref.selection.options;\n                var from = ref_selection_options.from;\n                var to = ref_selection_options.to;\n                var select = ref.options.select;\n                select.from = from;\n                select.to = to;\n            },\n            filterAxes: function () {\n                var ref = this;\n                var select = ref.options.select;\n                if (select === void 0) {\n                    select = {};\n                }\n                var chart = ref.chart;\n                var allAxes = chart.options.categoryAxis;\n                var from = select.from;\n                var to = select.to;\n                for (var idx = 0; idx < allAxes.length; idx++) {\n                    var axis = allAxes[idx];\n                    if (axis.pane !== NAVIGATOR_PANE) {\n                        axis.min = from;\n                        axis.max = to;\n                    }\n                }\n            },\n            filter: function () {\n                var ref = this;\n                var chart = ref.chart;\n                var select = ref.options.select;\n                if (!chart.requiresHandlers(['navigatorFilter'])) {\n                    return;\n                }\n                var mainAxis = this.mainAxis();\n                var args = {\n                    from: select.from,\n                    to: select.to\n                };\n                if (mainAxis.options.type !== 'category') {\n                    var axisOptions = new dataviz.DateCategoryAxis(deepExtend({ baseUnit: 'fit' }, chart.options.categoryAxis[0], {\n                        categories: [\n                            select.from,\n                            select.to\n                        ]\n                    }), chart.chartService).options;\n                    args.from = dataviz.addDuration(axisOptions.min, -axisOptions.baseUnitStep, axisOptions.baseUnit);\n                    args.to = dataviz.addDuration(axisOptions.max, axisOptions.baseUnitStep, axisOptions.baseUnit);\n                }\n                this.chart.trigger('navigatorFilter', args);\n            },\n            _zoom: function (e) {\n                var ref = this;\n                var axis = ref.chart._plotArea.categoryAxis;\n                var selection = ref.selection;\n                var ref_options = ref.options;\n                var select = ref_options.select;\n                var liveDrag = ref_options.liveDrag;\n                var mainAxis = this.mainAxis();\n                var delta = e.delta;\n                if (!selection) {\n                    return;\n                }\n                var fromIx = mainAxis.categoryIndex(selection.options.from);\n                var toIx = mainAxis.categoryIndex(selection.options.to);\n                e.originalEvent.preventDefault();\n                if (Math.abs(delta) > 1) {\n                    delta *= ZOOM_ACCELERATION;\n                }\n                if (toIx - fromIx > 1) {\n                    selection.expand(delta);\n                    this.readSelection();\n                } else {\n                    axis.options.min = select.from;\n                    select.from = axis.scaleRange(-e.delta).min;\n                }\n                if (liveDrag) {\n                    this.filterAxes();\n                    this.redrawSlaves();\n                }\n                selection.set(select.from, select.to);\n                this.showHint(this.options.select.from, this.options.select.to);\n            },\n            _zoomEnd: function (e) {\n                this._dragEnd(e);\n            },\n            showHint: function (from, to) {\n                var plotArea = this.chart._plotArea;\n                if (this.hint) {\n                    this.hint.show(from, to, plotArea.backgroundBox());\n                }\n            },\n            _selectStart: function (e) {\n                return this.chart._selectStart(e);\n            },\n            _select: function (e) {\n                this.showHint(e.from, e.to);\n                return this.chart._select(e);\n            },\n            _selectEnd: function (e) {\n                if (this.hint) {\n                    this.hint.hide();\n                }\n                this.readSelection();\n                this.filterAxes();\n                this.filter();\n                this.redrawSlaves();\n                return this.chart._selectEnd(e);\n            },\n            mainAxis: function () {\n                var plotArea = this.chart._plotArea;\n                if (plotArea) {\n                    return plotArea.namedCategoryAxes[NAVIGATOR_AXIS];\n                }\n            },\n            select: function (from, to) {\n                var select = this.options.select;\n                if (from && to) {\n                    select.from = this.parseDate(from);\n                    select.to = this.parseDate(to);\n                    this.filterAxes();\n                    this.filter();\n                    this.redrawSlaves();\n                    this.selection.set(from, to);\n                }\n                return {\n                    from: select.from,\n                    to: select.to\n                };\n            }\n        });\n        Navigator.setup = function (options, themeOptions) {\n            if (options === void 0) {\n                options = {};\n            }\n            if (themeOptions === void 0) {\n                themeOptions = {};\n            }\n            if (options.__navi) {\n                return;\n            }\n            options.__navi = true;\n            var naviOptions = deepExtend({}, themeOptions.navigator, options.navigator);\n            var panes = options.panes = [].concat(options.panes);\n            var paneOptions = deepExtend({}, naviOptions.pane, { name: NAVIGATOR_PANE });\n            if (!naviOptions.visible) {\n                paneOptions.visible = false;\n                paneOptions.height = 0.1;\n            }\n            panes.push(paneOptions);\n            Navigator.attachAxes(options, naviOptions);\n            Navigator.attachSeries(options, naviOptions, themeOptions);\n        };\n        Navigator.attachAxes = function (options, naviOptions) {\n            var series = naviOptions.series || [];\n            var categoryAxes = options.categoryAxis = [].concat(options.categoryAxis);\n            var valueAxes = options.valueAxis = [].concat(options.valueAxis);\n            var equallySpacedSeries = dataviz.filterSeriesByType(series, datavizConstants.EQUALLY_SPACED_SERIES);\n            var justifyAxis = equallySpacedSeries.length === 0;\n            var base = deepExtend({\n                type: 'date',\n                pane: NAVIGATOR_PANE,\n                roundToBaseUnit: !justifyAxis,\n                justified: justifyAxis,\n                _collapse: false,\n                majorTicks: { visible: true },\n                tooltip: { visible: false },\n                labels: { step: 1 },\n                autoBind: naviOptions.autoBindElements,\n                autoBaseUnitSteps: {\n                    minutes: [1],\n                    hours: [\n                        1,\n                        2\n                    ],\n                    days: [\n                        1,\n                        2\n                    ],\n                    weeks: [],\n                    months: [1],\n                    years: [1]\n                }\n            });\n            var user = naviOptions.categoryAxis;\n            categoryAxes.push(deepExtend({}, base, { maxDateGroups: 200 }, user, {\n                name: NAVIGATOR_AXIS,\n                title: null,\n                baseUnit: 'fit',\n                baseUnitStep: 'auto',\n                labels: { visible: false },\n                majorTicks: { visible: false }\n            }), deepExtend({}, base, user, {\n                name: NAVIGATOR_AXIS + '_labels',\n                maxDateGroups: 20,\n                baseUnitStep: 'auto',\n                labels: { position: '' },\n                plotBands: [],\n                autoBaseUnitSteps: { minutes: [] },\n                _overlap: true\n            }), deepExtend({}, base, user, {\n                name: NAVIGATOR_AXIS + '_ticks',\n                maxDateGroups: 200,\n                majorTicks: { width: 0.5 },\n                plotBands: [],\n                title: null,\n                labels: {\n                    visible: false,\n                    mirror: true\n                },\n                _overlap: true\n            }));\n            valueAxes.push(deepExtend({\n                name: NAVIGATOR_AXIS,\n                pane: NAVIGATOR_PANE,\n                majorGridLines: { visible: false },\n                visible: false\n            }, naviOptions.valueAxis));\n        };\n        Navigator.attachSeries = function (options, naviOptions, themeOptions) {\n            var series = options.series = options.series || [];\n            var navigatorSeries = [].concat(naviOptions.series || []);\n            var seriesColors = themeOptions.seriesColors;\n            var defaults = naviOptions.seriesDefaults;\n            for (var idx = 0; idx < navigatorSeries.length; idx++) {\n                series.push(deepExtend({\n                    color: seriesColors[idx % seriesColors.length],\n                    categoryField: naviOptions.dateField,\n                    visibleInLegend: false,\n                    tooltip: { visible: false }\n                }, defaults, navigatorSeries[idx], {\n                    axis: NAVIGATOR_AXIS,\n                    categoryAxis: NAVIGATOR_AXIS,\n                    autoBind: naviOptions.autoBindElements\n                }));\n            }\n        };\n        function ClonedObject() {\n        }\n        function clone(obj) {\n            ClonedObject.prototype = obj;\n            return new ClonedObject();\n        }\n        var AUTO_CATEGORY_WIDTH = 28;\n        var StockChart = Chart.extend({\n            applyDefaults: function (options, themeOptions) {\n                var width = dataviz.elementSize(this.element).width || datavizConstants.DEFAULT_WIDTH;\n                var theme = themeOptions;\n                var stockDefaults = {\n                    seriesDefaults: { categoryField: options.dateField },\n                    axisDefaults: {\n                        categoryAxis: {\n                            name: 'default',\n                            majorGridLines: { visible: false },\n                            labels: { step: 2 },\n                            majorTicks: { visible: false },\n                            maxDateGroups: Math.floor(width / AUTO_CATEGORY_WIDTH)\n                        }\n                    }\n                };\n                if (theme) {\n                    theme = deepExtend({}, theme, stockDefaults);\n                }\n                Navigator.setup(options, theme);\n                Chart.fn.applyDefaults.call(this, options, theme);\n            },\n            _setElementClass: function (element) {\n                dataviz.addClass(element, 'k-chart k-stockchart');\n            },\n            setOptions: function (options) {\n                this.destroyNavigator();\n                Chart.fn.setOptions.call(this, options);\n            },\n            noTransitionsRedraw: function () {\n                var transitions = this.options.transitions;\n                this.options.transitions = false;\n                this._fullRedraw();\n                this.options.transitions = transitions;\n            },\n            _resize: function () {\n                this.noTransitionsRedraw();\n            },\n            _redraw: function () {\n                var navigator = this.navigator;\n                if (!this._dirty() && navigator && navigator.options.partialRedraw) {\n                    navigator.redrawSlaves();\n                } else {\n                    this._fullRedraw();\n                }\n            },\n            _dirty: function () {\n                var options = this.options;\n                var series = [].concat(options.series, options.navigator.series);\n                var seriesCount = dataviz.grep(series, function (s) {\n                    return s && s.visible;\n                }).length;\n                var dirty = this._seriesCount !== seriesCount;\n                this._seriesCount = seriesCount;\n                return dirty;\n            },\n            _fullRedraw: function () {\n                var navigator = this.navigator;\n                if (!navigator) {\n                    navigator = this.navigator = new Navigator(this);\n                    this.trigger('navigatorCreated', { navigator: navigator });\n                }\n                navigator.clean();\n                navigator.setRange();\n                Chart.fn._redraw.call(this);\n                navigator.initSelection();\n            },\n            _trackSharedTooltip: function (coords) {\n                var plotArea = this._plotArea;\n                var pane = plotArea.paneByPoint(coords);\n                if (pane && pane.options.name === NAVIGATOR_PANE) {\n                    this._unsetActivePoint();\n                } else {\n                    Chart.fn._trackSharedTooltip.call(this, coords);\n                }\n            },\n            bindCategories: function () {\n                Chart.fn.bindCategories.call(this);\n                this.copyNavigatorCategories();\n            },\n            copyNavigatorCategories: function () {\n                var definitions = [].concat(this.options.categoryAxis);\n                var categories;\n                for (var axisIx = 0; axisIx < definitions.length; axisIx++) {\n                    var axis = definitions[axisIx];\n                    if (axis.name === NAVIGATOR_AXIS) {\n                        categories = axis.categories;\n                    } else if (categories && axis.pane === NAVIGATOR_PANE) {\n                        axis.categories = categories;\n                    }\n                }\n            },\n            destroyNavigator: function () {\n                if (this.navigator) {\n                    this.navigator.destroy();\n                    this.navigator = null;\n                }\n            },\n            destroy: function () {\n                this.destroyNavigator();\n                Chart.fn.destroy.call(this);\n            },\n            _stopChartHandlers: function (e) {\n                var coords = this._eventCoordinates(e);\n                var pane = this._plotArea.paneByPoint(coords);\n                return Chart.fn._stopChartHandlers.call(this, e) || pane && pane.options.name === NAVIGATOR_PANE;\n            }\n        });\n        dataviz.setDefaultOptions(StockChart, {\n            dateField: 'date',\n            axisDefaults: {\n                categoryAxis: {\n                    type: 'date',\n                    baseUnit: 'fit',\n                    justified: true\n                },\n                valueAxis: {\n                    narrowRange: true,\n                    labels: { format: 'C' }\n                }\n            },\n            navigator: {\n                select: {},\n                seriesDefaults: {\n                    markers: { visible: false },\n                    tooltip: { visible: true },\n                    line: { width: 2 }\n                },\n                hint: {},\n                visible: true\n            },\n            tooltip: { visible: true },\n            legend: { visible: false }\n        });\n        kendo.deepExtend(kendo.dataviz, {\n            constants: constants,\n            Navigator: Navigator,\n            NavigatorHint: NavigatorHint,\n            StockChart: StockChart\n        });\n    }());\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/stock/stock-chart', ['dataviz/stock/kendo-stock-chart'], f);\n}(function () {\n    (function ($) {\n        var kendo = window.kendo;\n        var dataviz = kendo.dataviz;\n        var ChartInstanceObserver = dataviz.ChartInstanceObserver;\n        var Chart = dataviz.ui.Chart;\n        var KendoStockChart = dataviz.StockChart;\n        var constants = dataviz.constants;\n        var NAVIGATOR_AXIS = constants.NAVIGATOR_AXIS;\n        var NAVIGATOR_PANE = constants.NAVIGATOR_PANE;\n        var deepExtend = kendo.deepExtend;\n        var defined = dataviz.defined;\n        var proxy = $.proxy;\n        var CHANGE = 'change';\n        var StockInstanceObserver = ChartInstanceObserver.extend({\n            handlerMap: {\n                navigatorFilter: '_onNavigatorFilter',\n                navigatorCreated: '_onNavigatorCreated'\n            }\n        });\n        var StockChart = Chart.extend({\n            options: {\n                name: 'StockChart',\n                dateField: 'date',\n                axisDefaults: {\n                    categoryAxis: {\n                        type: 'date',\n                        baseUnit: 'fit',\n                        justified: true\n                    },\n                    valueAxis: {\n                        narrowRange: true,\n                        labels: { format: 'C' }\n                    }\n                },\n                navigator: {\n                    select: {},\n                    seriesDefaults: {\n                        markers: { visible: false },\n                        tooltip: {\n                            visible: true,\n                            template: '#= kendo.toString(category, \\'d\\') #'\n                        },\n                        line: { width: 2 }\n                    },\n                    hint: {},\n                    visible: true\n                },\n                tooltip: { visible: true },\n                legend: { visible: false },\n                persistSeriesVisibility: true\n            },\n            _createChart: function (options, themeOptions) {\n                this._initNavigatorOptions(options);\n                this._instance = new KendoStockChart(this.element[0], options, themeOptions, {\n                    observer: new StockInstanceObserver(this),\n                    sender: this,\n                    rtl: this._isRtl()\n                });\n            },\n            _initNavigatorOptions: function (options) {\n                var navigatorOptions = options.navigator || {};\n                var support = kendo.support;\n                var isTouch = support.touch;\n                var isFirefox = support.browser.mozilla;\n                deepExtend(navigatorOptions, {\n                    autoBindElements: !navigatorOptions.dataSource,\n                    partialRedraw: navigatorOptions.dataSource,\n                    liveDrag: !isTouch && !isFirefox\n                });\n            },\n            _initDataSource: function (userOptions) {\n                var options = userOptions || {}, dataSource = options.dataSource, hasServerFiltering = dataSource && dataSource.serverFiltering, mainAxis = [].concat(options.categoryAxis)[0], naviOptions = options.navigator || {}, select = naviOptions.select, hasSelect = select && select.from && select.to;\n                if (hasServerFiltering && hasSelect) {\n                    var filter = [].concat(dataSource.filter || []);\n                    var from = kendo.parseDate(select.from);\n                    var to = kendo.parseDate(select.to);\n                    var dummyAxis = new dataviz.DateCategoryAxis(deepExtend({ baseUnit: 'fit' }, mainAxis, {\n                        categories: [\n                            from,\n                            to\n                        ]\n                    }), kendo);\n                    dataSource.filter = buildFilter(dummyAxis.range().min, to).concat(filter);\n                }\n                Chart.fn._initDataSource.call(this, userOptions);\n            },\n            _onNavigatorCreated: function (e) {\n                this._instance = e.sender;\n                this.options = e.sender.options;\n                this._navigator = this.navigator = e.navigator;\n                this._initNavigatorDataSource();\n            },\n            _initNavigatorDataSource: function () {\n                var navigatorOptions = this.options.navigator;\n                var autoBind = navigatorOptions.autoBind;\n                var dsOptions = navigatorOptions.dataSource;\n                if (dsOptions) {\n                    this._navigatorDataChangedHandler = this._navigatorDataChangedHandler || proxy(this._onNavigatorDataChanged, this);\n                    this._navigatorDataSource = kendo.data.DataSource.create(dsOptions).bind(CHANGE, this._navigatorDataChangedHandler);\n                    if (!defined(autoBind)) {\n                        autoBind = this.options.autoBind;\n                    }\n                    if (autoBind) {\n                        this._navigatorDataSource.fetch();\n                    }\n                }\n            },\n            _bindNavigatorSeries: function (series, data) {\n                var seriesIx, currentSeries, seriesLength = series.length;\n                for (seriesIx = 0; seriesIx < seriesLength; seriesIx++) {\n                    currentSeries = series[seriesIx];\n                    if (currentSeries.axis == NAVIGATOR_AXIS && this._isBindable(currentSeries)) {\n                        currentSeries.data = data;\n                    }\n                }\n            },\n            _onNavigatorDataChanged: function () {\n                var chart = this, instance = chart._instance, categoryAxes = chart.options.categoryAxis, axisIx, axesLength = categoryAxes.length, data = chart._navigatorDataSource.view(), currentAxis, naviCategories;\n                this._bindNavigatorSeries(chart.options.series, data);\n                if (chart._sourceSeries) {\n                    this._bindNavigatorSeries(chart._sourceSeries, data);\n                }\n                for (axisIx = 0; axisIx < axesLength; axisIx++) {\n                    currentAxis = categoryAxes[axisIx];\n                    if (currentAxis.pane == NAVIGATOR_PANE) {\n                        if (currentAxis.name == NAVIGATOR_AXIS) {\n                            chart._bindCategoryAxis(currentAxis, data, axisIx);\n                            naviCategories = currentAxis.categories;\n                        } else {\n                            currentAxis.categories = naviCategories;\n                        }\n                    }\n                }\n                if (instance._model) {\n                    var navigator = this.navigator;\n                    navigator.redraw();\n                    navigator.setRange();\n                    if (!chart.options.dataSource || chart.options.dataSource && chart._dataBound) {\n                        navigator.redrawSlaves();\n                    }\n                }\n            },\n            _bindCategories: function () {\n                Chart.fn._bindCategories.call(this);\n                if (this._instance) {\n                    this._instance.copyNavigatorCategories();\n                }\n            },\n            _onDataChanged: function () {\n                Chart.fn._onDataChanged.call(this);\n                this._dataBound = true;\n            },\n            setOptions: function (options) {\n                this._removeNavigatorDataSource();\n                this._initNavigatorOptions(options);\n                this._instance.destroyNavigator();\n                Chart.fn.setOptions.call(this, options);\n            },\n            _onNavigatorFilter: function (e) {\n                this.dataSource.filter(buildFilter(e.from, e.to));\n            },\n            requiresHandlers: function (names) {\n                if (dataviz.inArray('navigatorFilter', names)) {\n                    var dataSource = this.dataSource;\n                    var hasServerFiltering = dataSource && dataSource.options.serverFiltering;\n                    return hasServerFiltering && this.options.navigator.dataSource;\n                }\n                return Chart.fn.requiresHandlers.call(this, names);\n            },\n            _removeNavigatorDataSource: function () {\n                var navigatorDataSource = this._navigatorDataSource;\n                if (navigatorDataSource) {\n                    navigatorDataSource.unbind(CHANGE, this._navigatorDataChangedHandler);\n                    delete this._navigatorDataSource;\n                }\n            },\n            destroy: function () {\n                Chart.fn.destroy.call(this);\n                this._removeNavigatorDataSource();\n            }\n        });\n        dataviz.ui.plugin(StockChart);\n        function buildFilter(from, to) {\n            return [\n                {\n                    field: 'Date',\n                    operator: 'gte',\n                    value: from\n                },\n                {\n                    field: 'Date',\n                    operator: 'lt',\n                    value: to\n                }\n            ];\n        }\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.dataviz.stock', [\n        'dataviz/stock/kendo-stock-chart',\n        'dataviz/stock/stock-chart'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dataviz.stockchart',\n        name: 'StockChart',\n        category: 'dataviz',\n        description: 'StockChart widget and associated financial series.',\n        depends: ['dataviz.chart']\n    };\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}