/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.router.min",["kendo.core.min"],t)}(function(){return function(t,n){function e(t,n){if(!n)return t;t+"/"===n&&(t=n);var e=RegExp("^"+n,"i");return e.test(t)||(t=n+"/"+t),d.protocol+"//"+(d.host+"/"+t).replace(/\/\/+/g,"/")}function r(t){return t?"#!":"#"}function i(t){var n=d.href;return"#!"===t&&n.indexOf("#")>-1&&n.indexOf("#!")<0?null:n.split(t)[1]||""}function o(t,n){return 0===n.indexOf(t)?n.substr(t.length).replace(/\/\//g,"/"):n}function a(t){return t.replace(/^(#)?/,"#")}function s(t){return t.replace(/^(#(!)?)?/,"#!")}var h=window.kendo,u="change",c="back",l="same",f=h.support,d=window.location,p=window.history,g=50,b=h.support.browser.msie,k=/^#*/,w=window.document,v=h.Class.extend({back:function(){b?setTimeout(function(){p.back()}):p.back()},forward:function(){b?setTimeout(function(){p.forward()}):p.forward()},length:function(){return p.length},replaceLocation:function(t){d.replace(t)}}),x=v.extend({init:function(t){this.root=t},navigate:function(t){p.pushState({},w.title,e(t,this.root))},replace:function(t){p.replaceState({},w.title,e(t,this.root))},normalize:function(t){return o(this.root,t)},current:function(){var t=d.pathname;return d.search&&(t+=d.search),o(this.root,t)},change:function(n){t(window).bind("popstate.kendo",n)},stop:function(){t(window).unbind("popstate.kendo")},normalizeCurrent:function(t){var n,o=t.root,a=d.pathname,s=i(r(t.hashBang));o===a+"/"&&(n=o),o===a&&s&&(n=e(s.replace(k,""),o)),n&&p.pushState({},w.title,n)}}),_=v.extend({init:function(t){this._id=h.guid(),this.prefix=r(t),this.fix=t?s:a},navigate:function(t){d.hash=this.fix(t)},replace:function(t){this.replaceLocation(this.fix(t))},normalize:function(t){return t.indexOf(this.prefix)<0?t:t.split(this.prefix)[1]},change:function(n){f.hashChange?t(window).on("hashchange."+this._id,n):this._interval=setInterval(n,g)},stop:function(){t(window).off("hashchange."+this._id),clearInterval(this._interval)},current:function(){return i(this.prefix)},normalizeCurrent:function(t){var n=d.pathname,e=t.root;return!(!t.pushState||e===n)&&(this.replaceLocation(e+this.prefix+o(e,n)),!0)}}),m=h.Observable.extend({start:function(n){if(n=n||{},this.bind([u,c,l],n),!this._started){this._started=!0,n.root=n.root||"/";var e,r=this.createAdapter(n);r.normalizeCurrent(n)||(e=r.current(),t.extend(this,{adapter:r,root:n.root,historyLength:r.length(),current:e,locations:[e]}),r.change(t.proxy(this,"_checkUrl")))}},createAdapter:function(t){return f.pushState&&t.pushState?new x(t.root):new _(t.hashBang)},stop:function(){this._started&&(this.adapter.stop(),this.unbind(u),this._started=!1)},change:function(t){this.bind(u,t)},replace:function(t,n){this._navigate(t,n,function(n){n.replace(t),this.locations[this.locations.length-1]=this.current})},navigate:function(t,e){return"#:back"===t?(this.backCalled=!0,this.adapter.back(),n):(this._navigate(t,e,function(n){n.navigate(t),this.locations.push(this.current)}),n)},_navigate:function(t,e,r){var i=this.adapter;return t=i.normalize(t),this.current===t||this.current===decodeURIComponent(t)?(this.trigger(l),n):(!e&&this.trigger(u,{url:t,decode:!1})||(this.current=t,r.call(this,i),this.historyLength=i.length()),n)},_checkUrl:function(){var t=this.adapter,e=t.current(),r=t.length(),i=this.historyLength===r,o=e===this.locations[this.locations.length-2]&&i,a=this.backCalled,s=this.current;return null===e||this.current===e||this.current===decodeURIComponent(e)||(this.historyLength=r,this.backCalled=!1,this.current=e,o&&this.trigger("back",{url:s,to:e})?(t.forward(),this.current=s,n):this.trigger(u,{url:e,backButtonPressed:!a})?(o?t.forward():(t.back(),this.historyLength--),this.current=s,n):(o?this.locations.pop():this.locations.push(e),n))}});h.History=m,h.History.HistoryAdapter=v,h.History.HashAdapter=_,h.History.PushStateAdapter=x,h.absoluteURL=e,h.history=new m}(window.kendo.jQuery),function(){function t(t,n){return n?t:"([^/]+)"}function n(n,e){return RegExp("^"+n.replace(p,"\\$&").replace(l,"(?:$1)?").replace(f,t).replace(d,"(.*?)")+"$",e?"i":"")}function e(t){return t.replace(/(\?.*)|(#.*)/g,"")}var r=window.kendo,i=r.history,o=r.Observable,a="init",s="routeMissing",h="change",u="back",c="same",l=/\((.*?)\)/g,f=/(\(\?)?:\w+/g,d=/\*\w+/g,p=/[\-{}\[\]+?.,\\\^$|#\s]/g,g=r.Class.extend({init:function(t,e,r){t instanceof RegExp||(t=n(t,r)),this.route=t,this._callback=e},callback:function(t,n,i){var o,a,s=0,h=r.parseQueryStringParams(t);if(h._back=n,t=e(t),o=this.route.exec(t).slice(1),a=o.length,i)for(;s<a;s++)void 0!==o[s]&&(o[s]=decodeURIComponent(o[s]));o.push(h),this._callback.apply(null,o)},worksWith:function(t,n,r){return!!this.route.test(e(t))&&(this.callback(t,n,r),!0)}}),b=o.extend({init:function(t){t||(t={}),o.fn.init.call(this),this.routes=[],this.pushState=t.pushState,this.hashBang=t.hashBang,this.root=t.root,this.ignoreCase=t.ignoreCase!==!1,this.bind([a,s,h,c,u],t)},destroy:function(){i.unbind(h,this._urlChangedProxy),i.unbind(c,this._sameProxy),i.unbind(u,this._backProxy),this.unbind()},start:function(){var t,n=this,e=function(){n._same()},r=function(t){n._back(t)},o=function(t){n._urlChanged(t)};i.start({same:e,change:o,back:r,pushState:n.pushState,hashBang:n.hashBang,root:n.root}),t={url:i.current||"/",preventDefault:$.noop},n.trigger(a,t)||n._urlChanged(t),this._urlChangedProxy=o,this._backProxy=r},route:function(t,n){this.routes.push(new g(t,n,this.ignoreCase))},navigate:function(t,n){r.history.navigate(t,n)},replace:function(t,n){r.history.replace(t,n)},_back:function(t){this.trigger(u,{url:t.url,to:t.to})&&t.preventDefault()},_same:function(){this.trigger(c)},_urlChanged:function(t){var n,e,i,o,a=t.url,u=!!t.decode,c=t.backButtonPressed;if(a||(a="/"),this.trigger(h,{url:t.url,params:r.parseQueryStringParams(t.url),backButtonPressed:c}))return void t.preventDefault();for(n=0,e=this.routes,o=e.length;n<o;n++)if(i=e[n],i.worksWith(a,c,u))return;this.trigger(s,{url:a,params:r.parseQueryStringParams(a),backButtonPressed:c})&&t.preventDefault()}});r.Router=b}(),window.kendo},"function"==typeof define&&define.amd?define:function(t,n,e){(e||n)()});
//# sourceMappingURL=kendo.router.min.js.map
