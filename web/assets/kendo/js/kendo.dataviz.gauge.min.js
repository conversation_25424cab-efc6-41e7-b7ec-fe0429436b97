/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("util/text-metrics.min",["kendo.core.min"],t)}(function(){!function(t){function e(t){return(t+"").replace(a,l)}function i(t){var e,i=[];for(e in t)i.push(e+t[e]);return i.sort().join("")}function n(t){var e,i=2166136261;for(e=0;e<t.length;++e)i+=(i<<1)+(i<<4)+(i<<7)+(i<<8)+(i<<24),i^=t.charCodeAt(e);return i>>>0}function o(){return{width:0,height:0,baseline:0}}function r(t,e,i){return u.current.measure(t,e,i)}var s,a,l,h,c,u;window.kendo.util=window.kendo.util||{},s=kendo.Class.extend({init:function(t){this._size=t,this._length=0,this._map={}},put:function(t,e){var i=this._map,n={key:t,value:e};i[t]=n,this._head?(this._tail.newer=n,n.older=this._tail,this._tail=n):this._head=this._tail=n,this._length>=this._size?(i[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(t){var e=this._map[t];if(e)return e===this._head&&e!==this._tail&&(this._head=e.newer,this._head.older=null),e!==this._tail&&(e.older&&(e.older.newer=e.newer,e.newer.older=e.older),e.older=this._tail,e.newer=null,this._tail.newer=e,this._tail=e),e.value}}),a=/\r?\n|\r|\t/g,l=" ",h={baselineMarkerSize:1},"undefined"!=typeof document&&(c=document.createElement("div"),c.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),u=kendo.Class.extend({init:function(e){this._cache=new s(1e3),this.options=t.extend({},h,e)},measure:function(t,r,s){var a,l,h,u,d,p,f,g,m;if(void 0===s&&(s={}),!t)return o();if(a=i(r),l=n(t+a),h=this._cache.get(l))return h;u=o(),d=s.box||c,p=this._baselineMarker().cloneNode(!1);for(f in r)g=r[f],void 0!==g&&(d.style[f]=g);return m=s.normalizeText!==!1?e(t):t+"",d.textContent=m,d.appendChild(p),document.body.appendChild(d),m.length&&(u.width=d.offsetWidth-this.options.baselineMarkerSize,u.height=d.offsetHeight,u.baseline=p.offsetTop+this.options.baselineMarkerSize),u.width>0&&u.height>0&&this._cache.put(l,u),d.parentNode.removeChild(d),u},_baselineMarker:function(){var t=document.createElement("div");return t.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",t}}),u.current=new u,kendo.deepExtend(kendo.util,{LRUCache:s,TextMetrics:u,measureText:r,objectKey:i,hashKey:n,normalizeText:e})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/gauge/kendo-gauges.min",["kendo.core.min","kendo.color.min","kendo.drawing.min","kendo.dataviz.core.min"],t)}(function(){!function(t){function e(t,e){var i=t.getOrigin(),n=t.getSize(),o=c(e);return t.setOrigin([i.x-o.left,i.y-o.top]),t.setSize([n.width+(o.left+o.right),n.height+(o.top+o.bottom)]),t}function i(t,i){var n=t.box,o=t.children[0].box,r=i.border||{},s=i.background||"",a=K.fromRect(new M([n.x1,n.y1],[n.width(),n.height()]),{stroke:{}}),l=new Q(t.text,new E(o.x1,o.y1),{font:i.font,fill:{color:i.color}}),h=e(l.bbox().clone(),i.padding),c=K.fromRect(h,{stroke:{color:r.width?r.color:"",width:r.width,dashType:r.dashType,lineJoin:"round",lineCap:"round"},fill:{color:s}}),u=new H;return u.append(a),u.append(c),u.append(l),u}function n(t,e,i){var n=u(t.from)?t.from:d.MIN_VALUE,o=u(t.to)?t.to:d.MAX_VALUE;return t.from=Math.max(Math.min(o,n),e),t.to=Math.min(Math.max(o,n),i),t}function o(t,i){var n=c(i);return n.left=-n.left,n.top=-n.top,n.right=-n.right,n.bottom=-n.bottom,e(t,n)}function r(t,e){var i,n,o,r=t.position,s=t.tickX,a=t.tickY;return t.vertical?(i=new it(s,r),n=new it(s+e.size,r)):(i=new it(r,a),n=new it(r,a+e.size)),o=new tt({stroke:{color:e.color,width:e.width}}).moveTo(i).lineTo(n)}function s(t,e,i,n){var o,s,a,l=e.length;if(n.visible)for(o=i.mirror,s=i.lineBox,a=n.skip;a<l;a+=n.step)a%n.skipUnit!==0&&(i.tickX=o?s.x2:s.x2-n.size,i.tickY=o?s.y1-n.size:s.y1,i.position=e[a],t.append(r(i,n)))}function a(t,e,i,n){var o,r,s,a=new _t,l=t.center,h=t.getRadiusX();if(n.visible)for(o=0;o<e.length;o++)r=t.pointAt(e[o]),s=new E(l.x+h-n.size,l.y).rotate(e[o],l),a.append(new bt({stroke:{color:n.color,width:n.width}}).moveTo(r).lineTo(s));return a}function l(t,e,i,n){return{from:t,to:e,color:i,opacity:n}}var h,c,u,d,p,f,g,m,v,w,x,b,_,k,y,A,T,z,S,R,C,M,E,P,j,O,U,B,L,V,X,G,D,Y,N,F,I,W,H,K,Q,J,q,Z,$,tt,et,it,nt,ot,rt,st,at,lt,ht,ct,ut,dt,pt,ft,gt,mt,vt,wt,xt,bt,_t,kt,yt,At,Tt,zt,St,Rt,Ct,Mt,Et,Pt,jt,Ot,Ut;window.kendo.dataviz=window.kendo.dataviz||{},h=kendo.dataviz,c=h.getSpacing,u=h.defined,d=h.constants,p=d.BLACK,f=d.COORD_PRECISION,g=h.services,m=h.deepExtend,v=h.isArray,w=h.setDefaultOptions,x=h.NumericAxis,b=h.limitValue,_=h.Box,k=h.interpolateValue,y=h.round,A=kendo.drawing,T=A.Group,z=A.Path,S=A.Animation,R=A.AnimationFactory,C=kendo.geometry,M=C.Rect,E=C.Point,P=C.transform,j=150,O=250,U="arrow",B="arrowPointer",L="barPointer",V=200,X=.5,G=200,D=Math.PI/180,Y="inside",N="linear",F="outside",I="radialPointer",W="radialRangePointer",H=T,K=z,Q=A.Text,J=5,q=z,Z=A.Surface,$=h.Class.extend({init:function(t,e,i,n){void 0===n&&(n={}),this.element=t,this.theme=i,this.contextService=new g.ChartService(this,n),this._originalOptions=m({},this.options,e),this.options=m({},this._originalOptions),this._initTheme(i),this.redraw()},destroy:function(){this.surface&&(this.surface.destroy(),this.surface=null),delete this.element,delete this.surfaceElement},value:function(t){var e=this.pointers[0];return 0===arguments.length?e.value():(e.value(t),void this._setValueOptions(t))},_draw:function(){var t=this.surface;t.clear(),t.draw(this._visuals)},exportVisual:function(){return this._visuals},allValues:function(t){var e,i,n=this.pointers,o=[];if(0===arguments.length){for(e=0;e<n.length;e++)o.push(n[e].value());return o}if(v(t))for(i=0;i<t.length;i++)h.isNumber(t[i])&&n[i].value(t[i]);this._setValueOptions(t)},_setValueOptions:function(t){var e,i=[].concat(this.options.pointer),n=[].concat(t);for(e=0;e<n.length;e++)i[e].value=n[e]},resize:function(){this.noTransitionsRedraw()},noTransitionsRedraw:function(){var t=this.options.transitions;this._toggleTransitions(!1),this.redraw(),this._toggleTransitions(t)},redraw:function(){var t,e=this._surfaceSize(),i=new M([0,0],[e.width,e.height]);this._initSurface(),this.gaugeArea=this._createGaugeArea(),this._createModel(),t=o(i.bbox(),this._gaugeAreaMargin),this.reflow(t)},setOptions:function(t,e){this._originalOptions=m(this._originalOptions,t),this.options=m({},this._originalOptions),this._initTheme(e),this.redraw()},setDirection:function(t){this.contextService.rtl=!!t,this.surface&&"svg"===this.surface.type&&(this.surface.destroy(),this.surface=null)},setIntlService:function(t){this.contextService.intl=t},_initTheme:function(t){var e,i,n,o,r=t||this.theme||{};if(this.theme=r,this.options=m({},r,this.options),e=this.options,i=e.pointer,v(i)){for(n=[],o=0;o<i.length;o++)n.push(m({},r.pointer,i[o]));e.pointer=n}},_createGaugeArea:function(){var t,e=this.options.gaugeArea,i=this.surface.size(),n=e.border||{},r=new M([0,0],[i.width,i.height]);return this._gaugeAreaMargin=e.margin||J,n.width>0&&(r=o(r,n.width)),t=q.fromRect(r,{stroke:{color:n.width?n.color:"",width:n.width,dashType:n.dashType,lineJoin:"round",lineCap:"round"},fill:{color:e.background}})},_initSurface:function(){var t=this,e=t.options,i=t.surface,n=this._surfaceElement(),o=this._surfaceSize();h.elementSize(n,o),i&&i.options.type===e.renderAs?(this.surface.clear(),this.surface.resize()):(i&&i.destroy(),this.surface=Z.create(n,{type:e.renderAs}))},_surfaceSize:function(){var t=this.options,e=this._getSize();return t.gaugeArea&&m(e,t.gaugeArea),e},_surfaceElement:function(){return this.surfaceElement||(this.surfaceElement=document.createElement("div"),this.element.appendChild(this.surfaceElement)),this.surfaceElement},getSize:function(){return this._getSize()},_getSize:function(){var t=this.element,e=this._defaultSize(),i=t.offsetWidth,n=t.offsetHeight;return i||(i=e.width),n||(n=e.height),{width:i,height:n}},_defaultSize:function(){return{width:G,height:V}},_toggleTransitions:function(t){var e,i=this;for(this.options.transitions=t,e=0;e<this.pointers.length;e++)i.pointers[e].options.animation.transitions=t}}),w($,{plotArea:{},theme:"default",renderAs:"",pointer:{},scale:{},gaugeArea:{}}),tt=z,et=T,it=E,nt=x.extend({init:function(e,i){var n=e||{};!u(n.reverse)&&n.vertical===!1&&(i||{}).rtl&&(n=t.extend({},n,{reverse:!0})),x.fn.init.call(this,0,1,n,i),this.options.minorUnit=this.options.minorUnit||this.options.majorUnit/10},initUserOptions:function(t){var e=m({},this.options,t);return e=m({},e,{labels:{mirror:e.mirror}}),e.majorUnit=e.majorUnit||h.autoMajorUnit(e.min,e.max),e},initFields:function(){},render:function(){var t=this.elements=new et,e=this.renderLabels(),i=this.renderLine(),n=this.renderTicks(),o=this.renderRanges();return t.append(i,e,n,o),t},renderRanges:function(){var t,e,i,o,r,s=this,a=this.options,l=a.min,h=a.max,c=a.vertical,u=a.labels.mirror,d=a.ranges||[],p=new et,f=d.length,g=a.rangeSize||a.minorTicks.size/2;for(t=0;t<f;t++)e=n(d[t],l,h),i=s.getSlot(e.from,e.to),o=c?s.lineBox():i,r=c?i:s.lineBox(),c?o.x1-=g*(u?-1:1):r.y2+=g*(u?-1:1),p.append(tt.fromRect(new M([o.x1,r.y1],[o.x2-o.x1,r.y2-r.y1]),{fill:{color:e.color,opacity:e.opacity},stroke:{}}));return p},renderLabels:function(){var t,e=this,n=e.labels,o=e.options,r=new et;for(t=0;t<n.length;t++)r.append(i(n[t],o.labels));return r},renderLine:function(){var t,e=this.options.line,i=this.lineBox(),n=new et;return e.width>0&&e.visible&&(t=new tt({stroke:{color:e.color,dashType:e.dashType,width:e.width}}),t.moveTo(i.x1,i.y1).lineTo(i.x2,i.y2),n.append(t)),n},renderTicks:function(){var t=new et,e=this.options,i=e.majorTicks.visible?e.majorUnit:0,n={vertical:e.vertical,mirror:e.labels.mirror,lineBox:this.lineBox()};return s(t,this.getMajorTickPositions(),n,e.majorTicks),s(t,this.getMinorTickPositions(),n,m({},{skipUnit:i/e.minorUnit},e.minorTicks)),t}}),w(nt,{min:0,max:50,majorTicks:{size:15,align:Y,color:p,width:X,visible:!0},minorTicks:{size:10,align:Y,color:p,width:X,visible:!0},line:{width:X},labels:{position:Y,padding:2},mirror:!1,_alignLines:!1}),ot=h.Class.extend({init:function(t,e){var i=t.options,n=i.min,o=i.max,r=this.options=m({},this.options,e);r.fill=r.color,this.scale=t,r.value=u(r.value)?b(r.value,n,o):n},value:function(t){var e,i,n,o=this.options,r=o.value;return 0===arguments.length?r:(e=this.scale.options,i=e.min,n=e.max,o._oldValue=u(o._oldValue)?o.value:i,o.value=b(t,i,n),void(this.elements&&this.repaint()))}}),w(ot,{color:p}),rt=ot.extend({init:function(t,e){ot.fn.init.call(this,t,e),this.options=m({track:{visible:u(e.track)}},this.options)},reflow:function(){var t,e,i,n=this,o=n.options,r=n.scale,s=r.options,a=s.mirror,l=s.vertical,h=r.lineBox(),u=o.track.size||o.size,d=o.size/2,p=c(o.margin),f=l?p[a?"left":"right"]:p[a?"bottom":"top"];f=a?-f:f,l?(i=new _(h.x1+f,h.y1,h.x1+f,h.y2),a?i.x1-=u:i.x2+=u,o.shape!==L&&(e=new _(h.x2+f,h.y1-d,h.x2+f,h.y2+d),t=e)):(i=new _(h.x1,h.y1-f,h.x2,h.y1-f),a?i.y2+=u:i.y1-=u,o.shape!==L&&(e=new _(h.x1-d,h.y1-f,h.x2+d,h.y1-f),t=e)),this.trackBox=i,this.pointerRangeBox=e,this.box=t||i.clone().pad(o.border.width)},getElementOptions:function(){var t=this.options;return{fill:{color:t.color,opacity:t.opacity},stroke:u(t.border)?{color:t.border.width?t.border.color||t.color:"",width:t.border.width,dashType:t.border.dashType,opacity:t.opacity}:null}},_margin:function(){var t=this,e=t.scale,i=t.options,n=e.options,o=n.mirror,r=n.vertical,s=c(i.margin),a=r?s[o?"left":"right"]:s[o?"bottom":"top"];return a}}),w(rt,{shape:L,track:{border:{width:1}},color:p,border:{width:1},opacity:1,margin:c(3),animation:{type:L},visible:!0}),st=S.extend({setup:function(){var t,e,i=this.options,n=i.margin,o=i.from,r=i.to,s=i.vertical,a=s?"x1":"y1";i.mirror===s?(o[a]-=n,r[a]-=n):(o[a]+=n,r[a]+=n),t=this.fromScale=new E(o.x1,o.y1),e=this.toScale=new E(r.x1,r.y1),0!==i.duration&&(i.duration=Math.max(t.distanceTo(e)/i.duration*1e3,1))},step:function(t){var e=k(this.fromScale.x,this.toScale.x,t),i=k(this.fromScale.y,this.toScale.y,t);this.element.transform(P().translate(e,i))}}),w(st,{easing:N,duration:O}),R.current.register(B,st),at=E,lt=z,ht=rt.extend({init:function(t,e){rt.fn.init.call(this,t,e),u(this.options.size)||(this.options.size=.6*this.scale.options.majorTicks.size)},pointerShape:function(){var t,e=this,i=e.scale,n=e.options.size,o=n/2,r=i.options.mirror?-1:1;return t=i.options.vertical?[new at(0,0-o),new at(0-r*n,0),new at(0,0+o)]:[new at(0-o,0),new at(0,0+r*n),new at(0+o,0)]},repaint:function(){var t=this,e=t.scale,i=t.options,n=new st(this.elements,m(i.animation,{vertical:e.options.vertical,mirror:e.options.mirror,margin:this._margin(i.margin),from:e.getSlot(i._oldValue),to:e.getSlot(i.value)}));i.animation.transitions===!1&&(n.options.duration=0),n.setup(),n.play()},render:function(){var t,e,i=this,n=i.scale,o=i.options,r=this.getElementOptions(),s=this.pointerShape(o.value);return o.animation.type=B,t=new lt({stroke:r.stroke,fill:r.fill}).moveTo(s[0]).lineTo(s[1]).lineTo(s[2]).close(),e=n.getSlot(o.value),t.transform(P().translate(e.x1,e.y1)),this.elements=t,t}}),ct=S.extend({setup:function(){var t=this.options,e=this.axis=t.vertical?d.Y:d.X,i=this.to=t.newPoints[0][e],n=this.from=t.oldPoints[0][e];0!==t.duration&&(t.duration=Math.max(Math.abs(i-n)/t.speed*1e3,1)),this._set(n)},step:function(t){var e=k(this.from,this.to,t);this._set(e)},_set:function(t){var e="set"+this.axis.toUpperCase(),i=this.options.newPoints;i[0][e](t),i[1][e](t)}}),w(ct,{easing:N,speed:O}),R.current.register(L,ct),ut=T,dt=z,pt=rt.extend({init:function(t,e){rt.fn.init.call(this,t,e),u(this.options.size)||(this.options.size=.3*this.scale.options.majorTicks.size)},pointerShape:function(t){var e,i,n,o=this,r=o.scale,s=o.options,a=r.options,l=a.mirror,h=a.vertical,c=l===h?-1:1,u=s.size*c,p=r.getSlot(r.options.min),f=r.getSlot(t),g=h?d.Y:d.X,m=h?d.X:d.Y,v=this._margin()*c,w=new E;return w[g]=p[g+"1"],w[m]=p[m+"1"],e=new E,e[g]=f[g+"1"],e[m]=f[m+"1"],h?(w.translate(v,0),e.translate(v,0)):(w.translate(0,v),e.translate(0,v)),i=e.clone(),n=w.clone(),h?(i.translate(u,0),n.translate(u,0)):(i.translate(0,u),n.translate(0,u)),[w,e,i,n]},repaint:function(){var t,e=this,i=e.scale,n=e.options,o=this.pointerShape(n.value),r=this.pointerPath,s=this.pointerShape(n._oldValue);r.moveTo(o[0]).lineTo(o[1]).lineTo(o[2]).lineTo(o[3]).close(),t=new ct(r,m(n.animation,{reverse:i.options.reverse,vertical:i.options.vertical,oldPoints:[s[1],s[2]],newPoints:[o[1],o[2]]})),n.animation.transitions===!1&&(t.options.duration=0),t.setup(),t.play()},render:function(){var t,e=new ut,i=this.getElementOptions();return this.options.track.visible&&e.append(this.renderTrack()),t=this.pointerPath=new dt({stroke:i.stroke,fill:i.fill}),e.append(t),this.elements=e,e},renderTrack:function(){var t=this.options.track,e=t.border||{},i=this.trackBox.clone().pad(e.width||0);return new dt.fromRect(i.toRect(),{fill:{color:t.color,opacity:t.opacity},stroke:{color:e.width?e.color||t.color:"",width:e.width,dashType:e.dashType}})}}),ft=60,gt=60,mt=T,vt=$.extend({reflow:function(t){var e,i=this.pointers,n=t.origin.x,o=t.origin.y,r=new _(n,o,n+t.width(),o+t.height());for(this.scale.reflow(r),this._shrinkScaleWidth(r),e=0;e<i.length;e++)i[e].reflow();this.bbox=this._getBox(r),this._alignElements(),this._shrinkElements(),this._buildVisual(),this._draw()},_buildVisual:function(){var t,e,i=new mt,n=this.scale.render(),o=this.pointers;for(i.append(this.gaugeArea),i.append(n),t=0;t<o.length;t++)e=o[t],i.append(e.render()),e.value(e.options.value);this._visuals=i},_createModel:function(){var t,e,i,n,o=this,r=this.options,s=this.scale=new nt(r.scale,this.contextService);for(this.pointers=[],t=r.pointer,t=v(t)?t:[t],e=0;e<t.length;e++)i=m({},t[e],{animation:{transitions:r.transitions}}),n=i.shape===U?ht:pt,o.pointers.push(new n(s,i))},_defaultSize:function(){var t=this.options.scale.vertical;return{width:t?ft:G,height:t?V:gt}},_getBox:function(t){var e,i,n=this,o=n.scale,r=n.pointers,s=t.center(),a=r[0].box.clone().wrap(o.box);for(e=0;e<r.length;e++)a.wrap(r[e].box.clone());return o.options.vertical?(i=a.width()/2,a=new _(s.x-i,t.y1,s.x+i,t.y2)):(i=a.height()/2,a=new _(t.x1,s.y-i,t.x2,s.y+i)),a},_alignElements:function(){var t,e,i,n=this,o=this,r=o.scale,s=o.pointers,a=r.box,l=s[0].box.clone().wrap(r.box),h=this.bbox;for(t=0;t<s.length;t++)l.wrap(s[t].box.clone());for(r.options.vertical?(e=h.center().x-l.center().x,r.reflow(new _(a.x1+e,h.y1,a.x2+e,h.y2))):(e=h.center().y-l.center().y,r.reflow(new _(a.x1,a.y1+e,a.x2,a.y2+e))),i=0;i<s.length;i++)s[i].reflow(n.bbox)},_shrinkScaleWidth:function(t){var e,i=this,n=i.scale;n.options.vertical||(e=n.contentBox().width()-t.width(),e>0&&(n.box.shrink(e,0),n.box.alignTo(t,"center"),n.reflow(n.box)))},_shrinkElements:function(){var t,e,i=this,n=this,o=n.scale,r=n.pointers,s=o.box.clone(),a=o.options.vertical?"y":"x",l=r[0].box;for(t=0;t<r.length;t++)l.wrap(r[t].box.clone());for(s[a+1]+=Math.max(s[a+1]-l[a+1],0),s[a+2]-=Math.max(l[a+2]-s[a+2],0),o.reflow(s),e=0;e<r.length;e++)r[e].reflow(i.bbox)}}),w(vt,{transitions:!0,gaugeArea:{background:""},scale:{vertical:!0}}),wt=180,xt=A.Arc,bt=z,_t=T,kt=x.extend({init:function(t,e){x.fn.init.call(this,0,1,t,e)},initUserOptions:function(t){var e=m({},this.options,t);return e.majorUnit=e.majorUnit||h.autoMajorUnit(e.min,e.max),e.minorUnit=e.minorUnit||e.majorUnit/10,e},initFields:function(){},render:function(t,e){var i=this.renderArc(t,e);this.bbox=i.bbox(),this.labelElements=this.renderLabels(),this.ticks=this.renderTicks(),this.ranges=this.renderRanges()},reflow:function(t){var e=t.center(),i=Math.min(t.height(),t.width())/2;return u(this.bbox)?(this.bbox=this.arc.bbox(),this.radius(this.arc.getRadiusX()),this.repositionRanges(),this.renderLabels(),void 0):this.render(e,i)},slotAngle:function(t){var e,i=this.options,n=i.min,o=i.max,r=i.reverse,s=i.startAngle,a=i.endAngle,l=a-s;return e=r?a-(t-n)/(o-n)*l:(t-n)/(o-n)*l+s,e+wt},hasRanges:function(){var t=this.options.ranges;return t&&t.length},ticksSize:function(){var t=this.options,e=t.majorTicks,i=t.minorTicks,n=0;return e.visible&&(n=e.size),i.visible&&(n=Math.max(i.size,n)),n},renderLabels:function(){var t,e,n,o,r,s,a,l,h,c,d,p,f,g,m,v,w,x,b,k=this,y=this.options,A=this.arc.clone(),T=A.getRadiusX(),z=this.tickAngles(A,y.majorUnit),S=y.rangeSize=y.rangeSize||.1*T,R=new _t,C=.05*T;for(u(y.rangeDistance)?C=y.rangeDistance:y.rangeDistance=C,t=y.labels,e=t.position===Y,n=u(this.labelElements),e&&(T-=this.ticksSize(),this.hasRanges()&&!n&&(T-=S+C),A.setRadiusX(T).setRadiusY(T)),o=this.labels,r=o.length,s=t.padding,a=0;a<r;a++)l=o[a],h=l.box.width()/2,c=l.box.height()/2,d=z[a],p=(d-wt)*D,f=A.pointAt(d),g=f.x+Math.cos(p)*(h+s)*(e?1:-1),m=f.y+Math.sin(p)*(c+s)*(e?1:-1),l.reflow(new _(g-h,m-c,g+h,m+c)),v=new E(l.box.x1,l.box.y1),w=void 0,n?(w=k.labelElements.children[a],x=w.bbox().origin,b=w.transform()||P(),b.translate(v.x-x.x,v.y-x.y),w.transform(b)):(w=i(l,y.labels),R.append(w)),k.bbox=M.union(k.bbox,w.bbox());return R},repositionRanges:function(){var t,e,i,n,o,r,s=this.ranges.children;if(s.length>0){for(t=this.options,e=t.rangeDistance,i=t.rangeSize,n=this.getRangeRadius(),this.options.labels.position===Y&&(n+=i+e),o=n+i/2,r=0;r<s.length;r++)s[r]._geometry.setRadiusX(o).setRadiusY(o);this.bbox=M.union(this.bbox,this.ranges.bbox())}},renderRanges:function(){var t,e,i,n,o,r,s,a,l,h=this,c=this.rangeSegments(),u=c.length,d=new _t;if(u)for(t=this.options,e=t.rangeSize,i=t.reverse,n=t.rangeDistance,o=this.getRangeRadius(),this.radius(this.radius()-e-n),r=0;r<u;r++)s=c[r],a=h.slotAngle(s[i?"to":"from"]),l=h.slotAngle(s[i?"from":"to"]),l-a!==0&&d.append(h.createRange(a,l,o,s));return d},createRange:function(t,e,i,n){var o=this.options.rangeSize,r=new C.Arc(this.arc.center,{radiusX:i+o/2,radiusY:i+o/2,startAngle:t,endAngle:e});return new xt(r,{stroke:{width:o,color:n.color,opacity:n.opacity,lineCap:n.lineCap}})},rangeSegments:function(){var t,e,i,o,r,s,a,h,c=this.options,u=c.ranges||[],d=u.length,p=[];if(d)for(t=c.min,e=c.max,i=c.rangePlaceholderColor,p.push(l(t,e,i)),o=0;o<d;o++)for(r=n(u[o],t,e),s=p.length,a=0;a<s;a++)if(h=p[a],h.from<=r.from&&r.from<=h.to){p.push(l(r.from,r.to,r.color,r.opacity)),h.from<=r.to&&r.to<=h.to&&p.push(l(r.to,h.to,i,r.opacity)),h.to=r.from;break}return p},getRangeRadius:function(){var t,e=this,i=e.arc,n=e.options,o=n.rangeSize,r=n.rangeDistance,s=n.majorTicks.size;return t=n.labels.position===F?i.getRadiusX()-s-r-o:i.getRadiusX()-o},renderArc:function(t,e){var i=this.options,n=this.arc=new C.Arc(t,{radiusX:e,radiusY:e,startAngle:i.startAngle+wt,endAngle:i.endAngle+wt});return n},renderTicks:function(){var t,e,i,n,o=this,r=o.arc,s=o.options,l=r.clone();return this.majorTickAngles=this.tickAngles(r,s.majorUnit),this.majorTicks=a(l,this.majorTickAngles,s.majorUnit,s.majorTicks),t=new _t,t.append(this.majorTicks),e=s.majorTicks.size,i=s.minorTicks.size,this._tickDifference=e-i,s.labels.position===F&&(n=l.getRadiusX(),l.setRadiusX(n-e+i).setRadiusY(n-e+i)),this.minorTickAngles=this.normalizeTickAngles(this.tickAngles(r,s.minorUnit)),this.minorTicks=a(l,this.minorTickAngles,s.minorUnit,s.minorTicks),t.append(this.minorTicks),t},normalizeTickAngles:function(t){var e,i=this.options,n=i.majorUnit/i.minorUnit;for(e=t.length-1;e>=0;e--)e%n===0&&t.splice(e,1);return t},tickAngles:function(t,e){var i,n,o=this.options,r=o.reverse,s=o.max-o.min,a=t.endAngle-t.startAngle,l=s/e,h=t.startAngle,c=a/l;for(r&&(h+=a,c=-c),i=[],n=0;n<l;n++)i.push(y(h,f)),h+=c;return y(h)<=t.endAngle&&i.push(h),i},radius:function(t){return t?(this.arc.setRadiusX(t).setRadiusY(t),this.repositionTicks(this.majorTicks.children,this.majorTickAngles),this.repositionTicks(this.minorTicks.children,this.minorTickAngles,!0),void 0):this.arc.getRadiusX()},repositionTicks:function(t,e,i){var n,o,r,s,a,l=i?this._tickDifference||0:0,h=this.arc,c=h.getRadiusX();for(i&&this.options.labels.position===F&&0!==l&&(h=this.arc.clone(),h.setRadiusX(c-l).setRadiusY(c-l)),n=0;n<t.length;n++)o=h.pointAt(e[n]),r=t[n].segments,s=o.x-r[0].anchor().x,a=o.y-r[0].anchor().y,t[n].transform((new P).translate(s,a))}}),w(kt,{min:0,max:100,majorTicks:{size:15,align:Y,color:p,width:X,visible:!0},minorTicks:{size:10,align:Y,color:p,width:X,visible:!0},startAngle:-30,endAngle:210,labels:{position:Y,padding:2}}),yt=S.extend({init:function(t,e){S.fn.init.call(this,t,e);var i=this.options;i.duration=Math.max(Math.abs(i.newAngle-i.oldAngle)/i.duration*1e3,1)},step:function(t){var e=this.options,i=k(e.oldAngle,e.newAngle,t);this.element.transform(P().rotate(i,e.center))}}),w(yt,{easing:N,duration:j}),R.current.register(I,yt),At=.05,Tt=A.Circle,zt=T,St=z,Rt=ot.extend({setAngle:function(t){this.elements.transform(P().rotate(t,this.center))},repaint:function(){var t=this,e=t.scale,i=t.options,n=e.slotAngle(i._oldValue),o=e.slotAngle(i.value);i.animation.transitions===!1?this.setAngle(o):new yt(this.elements,m(i.animation,{oldAngle:n,newAngle:o})).play()},render:function(){var t=this,e=t.scale,i=t.options,n=new zt;return i.animation!==!1&&m(i.animation,{startAngle:0,center:e.arc.center,reverse:e.options.reverse}),n.append(this._renderNeedle(),this._renderCap()),this.elements=n,this.setAngle(D),n},reflow:function(t){var e=this.center=t.center,i=b(this.options.length||1,.1,1.5),n=this.radius=t.getRadiusX()*i,o=this.capSize=Math.round(n*this.options.cap.size);this.bbox=M.fromPoints(new E(e.x-o,e.y-o),new E(e.x+o,e.y+o))},_renderNeedle:function(){var t=this.scale.options.minorTicks.size,e=this.center,i=this.options.color,n=new St({fill:{color:i},stroke:{color:i,width:X}});return n.moveTo(e.x+this.radius-t,e.y).lineTo(e.x,e.y-this.capSize/2).lineTo(e.x,e.y+this.capSize/2).close(),n},_renderCap:function(){var t=this.options,e=t.cap.color||t.color,i=new C.Circle(this.center,this.capSize),n=new Tt(i,{fill:{color:e},stroke:{color:e}});return n}}),w(Rt,{cap:{size:At},arrow:{width:16,height:14},animation:{type:I,duration:j}}),Ct=T,Mt=$.extend({reflow:function(t){var e,i=this,n=this.pointers;for(this.scale.reflow(t),this._initialPlotArea=this.scale.bbox,e=0;e<n.length;e++)n[e].reflow(i.scale.arc),i._initialPlotArea=M.union(i._initialPlotArea,n[e].bbox);this.fitScale(t),this.alignScale(t),this._buildVisual(this.gaugeArea,n,this.scale),this._draw()},_buildVisual:function(t,e,i){var n=this._visuals=new Ct;n.append(t),n.append(i.ticks),n.append(i.ranges),this._buildPointers(e),n.append(i.labelElements)},_buildPointers:function(t){var e,i,n=this;for(e=0;e<t.length;e++)i=t[e],i.render(),n._visuals.append(i.elements),i.value(i.options.value)},fitScale:function(t){for(var e,i,n,o,r,s=this,a=this.scale.arc,l=this._initialPlotArea,h=Math.abs(this.getDiff(l,t)),c=y(h,f),u=y(-h,f),d=0,p=0;!(!(p++<100)||(d=r===n?d+1:0,d>5)||c!==o&&(e=s.getPlotBox(c,t,a),0<=e&&e<=2)||u!==o&&(n=s.getPlotBox(u,t,a),0<=n&&n<=2)||(o=e>0&&n>0?2*c:e<0&&n<0?2*u:y((c+u)/2||1,f),i=s.getPlotBox(o,t,a),0<=i&&i<=2));)r=n,i>0?(u=o,n=i):(c=o,e=i)},getPlotBox:function(t,e,i){var n,o=this,r=this.scale,s=this.pointers,a=i.getRadiusX(),l=i.clone();for(l.setRadiusX(a+t).setRadiusY(a+t),r.arc=l,r.reflow(e),this.plotBbox=r.bbox,n=0;n<s.length;n++)s[n].reflow(l),o.plotBbox=M.union(o.plotBbox,s[n].bbox);return this.getDiff(this.plotBbox,e)},getDiff:function(t,e){return Math.min(e.width()-t.width(),e.height()-t.height())},alignScale:function(t){var e,i=this,n=this.plotBbox.center(),o=t.center(),r=n.x-o.x,s=n.y-o.y,a=this,l=a.scale,h=a.pointers;for(l.arc.center.x-=r,l.arc.center.y-=s,l.reflow(t),e=0;e<h.length;e++)h[e].reflow(l.arc),i.plotBbox=M.union(l.bbox,h[e].bbox)},_createModel:function(){var t,e,i,n=this,o=this.options,r=o.pointer,s=this.scale=new kt(o.scale,this.contextService);for(this.pointers=[],t=v(r)?r:[r],e=0;e<t.length;e++)i=new Rt(s,m({},t[e],{animation:{transitions:o.transitions}})),n.pointers.push(i)}}),w(Mt,{transitions:!0,gaugeArea:{background:""}}),Et=kt.extend({rangeSegments:function(){var t=this.options,e=t.min,i=t.max,n=t.rangePlaceholderColor,o=t.rangeLineCap;return[{from:e,to:i,color:n,lineCap:o}]},hasRanges:function(){return!0},placeholderRangeAngle:function(t){var e=this.ranges.children[0].geometry();this.options.reverse?e.setEndAngle(t):e.setStartAngle(t)},addRange:function(t,e,i){var n=this.options.reverse,o=this.slotAngle(n?e:t),r=this.slotAngle(n?t:e),s=this.createRange(o,r,this.getRangeRadius(),i);return this.ranges.append(s),s}}),w(Et,{min:0,max:100,majorTicks:{visible:!1},minorTicks:{visible:!1},labels:{visible:!1},startAngle:0,endAngle:180,rangeLineCap:"round"}),Pt=800,jt=S.extend({init:function(t,e){var i,n,o,r;S.fn.init.call(this,t,e),i=this.options,n=Math.abs(i.newAngle-i.oldAngle)/i.duration*1e3,i.duration=b(n,j,Pt),o=t.elements.options.get("stroke.color"),r=t.currentColor(),o!==r&&(this.startColor=new kendo.Color(o),this.color=new kendo.Color(r))},step:function(t){var e,i,n,o=this,r=o.options,s=o.startColor,a=o.color,l=k(r.oldAngle,r.newAngle,t);this.element.angle(l),a&&(e=y(k(s.r,a.r,t)),i=y(k(s.g,a.g,t)),n=y(k(s.b,a.b,t)),this.element.stroke(new kendo.Color(e,i,n).toHex()))}}),w(jt,{easing:N,duration:j}),R.current.register(W,jt),Ot=ot.extend({repaint:function(){var t=this,e=t.scale,i=t.options,n=e.slotAngle(i._oldValue),o=e.slotAngle(i.value);this.animation&&this.animation.abort(),i.animation.transitions===!1?(this.angle(o),this.stroke(this.currentColor())):(this.animation=new jt(this,m(i.animation,{oldAngle:n,newAngle:o})),this.animation.play())},angle:function(t){var e=this.elements.geometry();this.scale.options.reverse?e.setStartAngle(t):e.setEndAngle(t),this.scale.placeholderRangeAngle(t)},stroke:function(t){this.elements.stroke(t)},render:function(){var t,e,i;this.elements||(t=this,e=t.scale,i=t.options,i.animation!==!1&&m(i.animation,{startAngle:0,center:e.arc.center,reverse:e.options.reverse}),this.elements=e.addRange(e.options.min,this.options.value,{color:this.currentColor(),opacity:i.opacity,lineCap:e.options.rangeLineCap}))},currentColor:function(){var t,e,i,n,o,r=this.scale.options,s=r.min,a=r.max,l=this.options,c=l.colors,u=l.color,d=l.value,p=h.isNumber(d)?d:s;if(c)for(t=0;t<c.length;t++)if(e=c[t],i=e.color,n=e.from,void 0===n&&(n=s),o=e.to,void 0===o&&(o=a),n<=p&&p<=o)return i;return u},reflow:function(){this.render(),this.bbox=this.elements.bbox()}}),w(Ot,{animation:{type:W,duration:j}}),Ut=Mt.extend({_initTheme:function(t){Mt.fn._initTheme.call(this,t),this.options.color=this.options.color||(this.theme.pointer||{}).color},_createModel:function(){var t=this.options,e=this.scale=new Et(t.scale,this.contextService),i=new Ot(e,m({},{colors:t.colors,color:t.color,value:t.value,opacity:t.opacity,animation:{transitions:t.transitions}}));this.pointers=[i]},_buildPointers:function(t){var e,i;for(e=0;e<t.length;e++)i=t[e],i.render(),i.value(i.options.value)},_setValueOptions:function(t){this.options.value=t},currentColor:function(){var t=this.pointers[0];if(t)return t.currentColor()},centerLabelPosition:function(t,e){var i,n,o,r,s=this.getSize(),a=this.scale.arc.center,l=a.x-t/2,h=a.y-e/2;return t<s.width&&(i=l+t,l=Math.max(l,0),i>s.width&&(l-=i-s.width)),e<s.height&&(n=this.scale.bbox,o=n.bottomRight().y,r=h+e,h=Math.max(h,n.origin.y),r>o&&(h-=r-o)),{left:l,top:h}}}),kendo.deepExtend(kendo.dataviz,{Gauge:$,LinearGauge:vt,LinearPointer:rt,ArrowLinearPointer:ht,BarLinearPointer:pt,LinearScale:nt,RadialGauge:Mt,RadialPointer:Rt,RadialScale:kt,ArcGauge:Ut,RangePointer:Ot,ArcScale:Et})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/gauge/main.min",["dataviz/gauge/kendo-gauges.min"],t)}(function(){return function(t){function e(t){var e=u.ui.themes||{},i=t.theme||"",n=i.toLowerCase();return u.SASS_THEMES.indexOf(n)!=-1?u.autoTheme().gauge:(e[i]||e[n]||{}).gauge}function i(t){m.fn[t]=function(){return this._instance[t].apply(this._instance,arguments)}}function n(t){a.fn[t]=function(e){var i=this,n=g[t];return i._centerElement?g.drawDOM(i.element).then(function(t){return n(t,e)}):n(i.exportVisual(),e)}}var o,r,s,a,l,h=window.kendo,c=h.ui.Widget,u=h.dataviz,d=u.LinearGauge,p=u.RadialGauge,f=u.ArcGauge,g=h.drawing,m=c.extend({init:function(e,i){h.destroy(e),t(e).empty(),c.fn.init.call(this,e),this.options=h.deepExtend(this.options,i),this.wrapper=this.element,this._createInstance(),this.element.addClass("k-gauge"),h.notify(this,u.ui)},options:{theme:"default",renderAs:"",pointer:{},scale:{},gaugeArea:{background:""},transitions:!0},setOptions:function(t){this._instance.setOptions(t,e(t)),this._copyFields()},redraw:function(){this._instance.redraw(),this._copyFields()},destroy:function(){c.fn.destroy.call(this),this._instance.destroy()},_createInstance:function(){var t=this._gaugeType();this._instance=new t(this.element[0],this.options,e(this.options)),this._copyFields()},_copyFields:function(){this._originalOptions=this._instance._originalOptions,this.options=this._instance.options,this.surface=this._instance.surface,this.bbox=this._instance.bbox,this.gaugeArea=this._instance.gaugeArea,this.pointers=this._instance.pointers,this.scale=this._instance.scale},_resize:function(){this._instance.resize()}}),v=["getSize","value","allValues","exportVisual"];for(o=0;o<v.length;o++)i(v[o]);for(u.ExportMixin.extend(m.fn),r=m.extend({options:{name:"RadialGauge"},_gaugeType:function(){return p}}),s=m.extend({options:{name:"LinearGauge",scale:{vertical:!0}},_gaugeType:function(){return d}}),a=m.extend({init:function(t,e){m.fn.init.call(this,t,e),this.element.css("position","relative"),this.element.addClass("k-arcgauge"),this._centerTemplate()},options:{name:"ArcGauge"},setOptions:function(t){m.fn.setOptions.call(this,t),this._centerTemplate()},redraw:function(){m.fn.redraw.call(this),this._centerTemplate()},value:function(t){var e=this._instance;return 0===arguments.length?e.value():(e.value(t),void this._centerTemplate())},destroy:function(){
m.fn.destroy.call(this),delete this._centerElement},exportVisual:function(){return!this._centerElement&&m.fn.exportVisual.call(this)},_resize:function(){this._instance.resize(),this._centerTemplate()},_centerTemplate:function(){var t,e,i,n;this.options.centerTemplate?(t=h.template(this.options.centerTemplate),e=this._instance,i=this._getCenterElement(),i.html(t({color:e.currentColor(),value:e.value()})),n=e.centerLabelPosition(i.width(),i.height()),i.css(n)):this._centerElement&&(this._centerElement.remove(),this._centerElement=null)},_getCenterElement:function(){var e=this._centerElement;return e||(e=this._centerElement=t("<div></div>").addClass("k-arcgauge-label"),this.element.append(e)),e},_gaugeType:function(){return f}}),l=["exportSVG","exportImage","exportPDF"],o=0;o<l.length;o++)n(l[o]);u.ui.plugin(s),u.ui.plugin(r),u.ui.plugin(a),h.deepExtend(u,{Gauge:m,LinearGauge:s,RadialGauge:r,ArcGauge:a})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("kendo.dataviz.gauge.min",["dataviz/gauge/main.min","kendo.dataviz.themes.min"],t)}(function(){return window.kendo},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()});
//# sourceMappingURL=kendo.dataviz.gauge.min.js.map
