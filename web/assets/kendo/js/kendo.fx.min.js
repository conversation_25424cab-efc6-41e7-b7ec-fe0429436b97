/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.fx.min",["kendo.core.min"],t)}(function(){return function(t,e){function i(t){return parseInt(t,10)}function r(t,e){return i(t.css(e))}function n(t){var e,i=[];for(e in t)i.push(e);return i}function s(t){for(var e in t)L.indexOf(e)!=-1&&Q.indexOf(e)==-1&&delete t[e];return t}function o(t,e){var i,r,n,s,o=[],a={};for(r in e)i=r.toLowerCase(),s=H&&L.indexOf(i)!=-1,!E.hasHW3D&&s&&Q.indexOf(i)==-1?delete e[r]:(n=e[r],s?o.push(r+"("+n+")"):a[r]=n);return o.length&&(a[at]=o.join(" ")),a}function a(t,e){var r,n,s;return H?(r=t.css(at),r==J?"scale"==e?1:0:(n=r.match(RegExp(e+"\\s*\\(([\\d\\w\\.]+)")),s=0,n?s=i(n[1]):(n=r.match(I)||[0,0,0,0,0],e=e.toLowerCase(),A.test(e)?s=parseFloat(n[3]/n[2]):"translatey"==e?s=parseFloat(n[4]/n[2]):"scale"==e?s=parseFloat(n[2]):"rotate"==e&&(s=parseFloat(Math.atan2(n[2],n[1])))),s)):parseFloat(t.css(e))}function c(t){return t.charAt(0).toUpperCase()+t.substring(1)}function l(t,e){var i=h.extend(e),r=i.prototype.directions;P[c(t)]=i,P.Element.prototype[t]=function(t,e,r,n){return new i(this.element,t,e,r,n)},T(r,function(e,r){P.Element.prototype[t+c(r)]=function(t,e,n){return new i(this.element,r,t,e,n)}})}function d(t,i,r,n){l(t,{directions:v,startValue:function(t){return this._startValue=t,this},endValue:function(t){return this._endValue=t,this},shouldHide:function(){return this._shouldHide},prepare:function(t,s){var o,a,c=this,l="out"===this._direction,d=c.element.data(i),u=!(isNaN(d)||d==r);o=u?d:e!==this._startValue?this._startValue:l?r:n,a=e!==this._endValue?this._endValue:l?n:r,this._reverse?(t[i]=a,s[i]=o):(t[i]=o,s[i]=a),c._shouldHide=s[i]===n}})}function u(t,e){var i=C.directions[e].vertical,r=t[i?Y:X]()/2+"px";return _[e].replace("$size",r)}var f,p,h,m,v,x,_,g,y,k,b,w,C=window.kendo,P=C.effects,T=t.each,N=t.extend,z=t.proxy,E=C.support,R=E.browser,H=E.transforms,D=E.transitions,O={scale:0,scalex:0,scaley:0,scale3d:0},F={translate:0,translatex:0,translatey:0,translate3d:0},S=e!==document.documentElement.style.zoom&&!H,I=/matrix3?d?\s*\(.*,\s*([\d\.\-]+)\w*?,\s*([\d\.\-]+)\w*?,\s*([\d\.\-]+)\w*?,\s*([\d\.\-]+)\w*?/i,q=/^(-?[\d\.\-]+)?[\w\s]*,?\s*(-?[\d\.\-]+)?[\w\s]*/i,A=/translatex?$/i,V=/(zoom|fade|expand)(\w+)/,M=/(zoom|fade|expand)/,$=/[xy]$/i,L=["perspective","rotate","rotatex","rotatey","rotatez","rotate3d","scale","scalex","scaley","scalez","scale3d","skew","skewx","skewy","translate","translatex","translatey","translatez","translate3d","matrix","matrix3d"],Q=["rotate","scale","scalex","scaley","skew","skewx","skewy","translate","translatex","translatey","matrix"],W={rotate:"deg",scale:"",skew:"px",translate:"px"},j=H.css,B=Math.round,U="",G="px",J="none",K="auto",X="width",Y="height",Z="hidden",tt="origin",et="abortId",it="overflow",rt="translate",nt="position",st="completeCallback",ot=j+"transition",at=j+"transform",ct=j+"backface-visibility",lt=j+"perspective",dt="1500px",ut="perspective("+dt+")",ft={left:{reverse:"right",property:"left",transition:"translatex",vertical:!1,modifier:-1},right:{reverse:"left",property:"left",transition:"translatex",vertical:!1,modifier:1},down:{reverse:"up",property:"top",transition:"translatey",vertical:!0,modifier:1},up:{reverse:"down",property:"top",transition:"translatey",vertical:!0,modifier:-1},top:{reverse:"bottom"},bottom:{reverse:"top"},"in":{reverse:"out",modifier:-1},out:{reverse:"in",modifier:1},vertical:{reverse:"vertical"},horizontal:{reverse:"horizontal"}};C.directions=ft,N(t.fn,{kendoStop:function(t,e){return D?P.stopQueue(this,t||!1,e||!1):this.stop(t,e)}}),H&&!D&&(T(Q,function(i,r){t.fn[r]=function(i){if(e===i)return a(this,r);var n=t(this)[0],s=r+"("+i+W[r.replace($,"")]+")";return n.style.cssText.indexOf(at)==-1?t(this).css(at,s):n.style.cssText=n.style.cssText.replace(RegExp(r+"\\(.*?\\)","i"),s),this},t.fx.step[r]=function(e){t(e.elem)[r](e.now)}}),f=t.fx.prototype.cur,t.fx.prototype.cur=function(){return Q.indexOf(this.prop)!=-1?parseFloat(t(this.elem)[this.prop]()):f.apply(this,arguments)}),C.toggleClass=function(t,e,i,r){return e&&(e=e.split(" "),D&&(i=N({exclusive:"all",duration:400,ease:"ease-out"},i),t.css(ot,i.exclusive+" "+i.duration+"ms "+i.ease),setTimeout(function(){t.css(ot,"").css(Y)},i.duration)),T(e,function(e,i){t.toggleClass(i,r)})),t},C.parseEffects=function(t,e){var i={};return"string"==typeof t?T(t.split(" "),function(t,r){var n=!M.test(r),s=r.replace(V,function(t,e,i){return e+":"+i.toLowerCase()}),o=s.split(":"),a=o[1],c={};o.length>1&&(c.direction=e&&n?ft[a].reverse:a),i[o[0]]=c}):T(t,function(t){var r=this.direction;r&&e&&!M.test(t)&&(this.direction=ft[r].reverse),i[t]=this}),i},D&&N(P,{transition:function(e,i,r){var s,a,c,l,d=0,u=e.data("keys")||[];r=N({duration:200,ease:"ease-out",complete:null,exclusive:"all"},r),c=!1,l=function(){c||(c=!0,a&&(clearTimeout(a),a=null),e.removeData(et).dequeue().css(ot,"").css(ot),r.complete.call(e))},r.duration=t.fx?t.fx.speeds[r.duration]||r.duration:r.duration,s=o(e,i),t.merge(u,n(s)),t.hasOwnProperty("uniqueSort")?e.data("keys",t.uniqueSort(u)).height():e.data("keys",t.unique(u)).height(),e.css(ot,r.exclusive+" "+r.duration+"ms "+r.ease).css(ot),e.css(s).css(at),D.event&&(e.one(D.event,l),0!==r.duration&&(d=500)),a=setTimeout(l,r.duration+d),e.data(et,a),e.data(st,l)},stopQueue:function(t,e,i){var r,n=t.data("keys"),s=!i&&n,o=t.data(st);return s&&(r=C.getComputedStyles(t[0],n)),o&&o(),s&&t.css(r),t.removeData("keys").stop(e)}}),p=C.Class.extend({init:function(t,e){var i=this;i.element=t,i.effects=[],i.options=e,i.restore=[]},run:function(e){var i,r,n,a,c,l,d,u=this,f=e.length,p=u.element,h=u.options,m=t.Deferred(),v={},x={};for(u.effects=e,m.done(t.proxy(u,"complete")),p.data("animating",!0),r=0;r<f;r++)for(i=e[r],i.setReverse(h.reverse),i.setOptions(h),u.addRestoreProperties(i.restore),i.prepare(v,x),c=i.children(),n=0,l=c.length;n<l;n++)c[n].duration(h.duration).run();for(d in h.effects)N(x,h.effects[d].properties);for(p.is(":visible")||N(v,{display:p.data("olddisplay")||"block"}),H&&!h.reset&&(a=p.data("targetTransform"),a&&(v=N(a,v))),v=o(p,v),H&&!D&&(v=s(v)),p.css(v).css(at),r=0;r<f;r++)e[r].setup();return h.init&&h.init(),p.data("targetTransform",x),P.animate(p,x,N({},h,{complete:m.resolve})),m.promise()},stop:function(){t(this.element).kendoStop(!0,!0)},addRestoreProperties:function(t){for(var e,i=this.element,r=0,n=t.length;r<n;r++)e=t[r],this.restore.push(e),i.data(e)||i.data(e,i.css(e))},restoreCallback:function(){var t,e,i,r=this.element;for(t=0,e=this.restore.length;t<e;t++)i=this.restore[t],r.css(i,r.data(i))},complete:function(){var e=this,i=0,r=e.element,n=e.options,s=e.effects,o=s.length;for(r.removeData("animating").dequeue(),n.hide&&r.data("olddisplay",r.css("display")).hide(),this.restoreCallback(),S&&!H&&setTimeout(t.proxy(this,"restoreCallback"),0);i<o;i++)s[i].teardown();n.completeCallback&&n.completeCallback(r)}}),P.promise=function(t,e){var i,r,n,s=[],o=new p(t,e),a=C.parseEffects(e.effects);e.effects=a;for(n in a)i=P[c(n)],i&&(r=new i(t,a[n].direction),s.push(r));s[0]?o.run(s):(t.is(":visible")||t.css({display:t.data("olddisplay")||"block"}).css("display"),e.init&&e.init(),t.dequeue(),o.complete())},N(P,{animate:function(i,n,o){var a=o.transition!==!1;delete o.transition,D&&"transition"in P&&a?P.transition(i,n,o):H?i.animate(s(n),{queue:!1,show:!1,hide:!1,duration:o.duration,complete:o.complete}):i.each(function(){var i=t(this),s={};T(L,function(t,o){var a,c,l,d,u,f,p,h=n?n[o]+" ":null;h&&(c=n,o in O&&n[o]!==e?(a=h.match(q),H&&N(c,{scale:+a[0]})):o in F&&n[o]!==e&&(l=i.css(nt),d="absolute"==l||"fixed"==l,i.data(rt)||(d?i.data(rt,{top:r(i,"top")||0,left:r(i,"left")||0,bottom:r(i,"bottom"),right:r(i,"right")}):i.data(rt,{top:r(i,"marginTop")||0,left:r(i,"marginLeft")||0})),u=i.data(rt),a=h.match(q),a&&(f=o==rt+"y"?0:+a[1],p=o==rt+"y"?+a[1]:+a[2],d?(isNaN(u.right)?isNaN(f)||N(c,{left:u.left+f}):isNaN(f)||N(c,{right:u.right-f}),isNaN(u.bottom)?isNaN(p)||N(c,{top:u.top+p}):isNaN(p)||N(c,{bottom:u.bottom-p})):(isNaN(f)||N(c,{marginLeft:u.left+f}),isNaN(p)||N(c,{marginTop:u.top+p})))),!H&&"scale"!=o&&o in c&&delete c[o],c&&N(s,c))}),R.msie&&delete s.scale,i.animate(s,{queue:!1,show:!1,hide:!1,duration:o.duration,complete:o.complete})})}}),P.animatedPromise=P.promise,h=C.Class.extend({init:function(t,e){var i=this;i.element=t,i._direction=e,i.options={},i._additionalEffects=[],i.restore||(i.restore=[])},reverse:function(){return this._reverse=!0,this.run()},play:function(){return this._reverse=!1,this.run()},add:function(t){return this._additionalEffects.push(t),this},direction:function(t){return this._direction=t,this},duration:function(t){return this._duration=t,this},compositeRun:function(){var t=this,e=new p(t.element,{reverse:t._reverse,duration:t._duration}),i=t._additionalEffects.concat([t]);return e.run(i)},run:function(){if(this._additionalEffects&&this._additionalEffects[0])return this.compositeRun();var e,i,r=this,n=r.element,a=0,c=r.restore,l=c.length,d=t.Deferred(),u={},f={},p=r.children(),h=p.length;for(d.done(t.proxy(r,"_complete")),n.data("animating",!0),a=0;a<l;a++)e=c[a],n.data(e)||n.data(e,n.css(e));for(a=0;a<h;a++)p[a].duration(r._duration).run();return r.prepare(u,f),n.is(":visible")||N(u,{display:n.data("olddisplay")||"block"}),H&&(i=n.data("targetTransform"),i&&(u=N(i,u))),u=o(n,u),H&&!D&&(u=s(u)),n.css(u).css(at),r.setup(),n.data("targetTransform",f),P.animate(n,f,{duration:r._duration,complete:d.resolve}),d.promise()},stop:function(){var e=0,i=this.children(),r=i.length;for(e=0;e<r;e++)i[e].stop();return t(this.element).kendoStop(!0,!0),this},restoreCallback:function(){var t,e,i,r=this.element;for(t=0,e=this.restore.length;t<e;t++)i=this.restore[t],r.css(i,r.data(i))},_complete:function(){var e=this,i=e.element;i.removeData("animating").dequeue(),e.restoreCallback(),e.shouldHide()&&i.data("olddisplay",i.css("display")).hide(),S&&!H&&setTimeout(t.proxy(e,"restoreCallback"),0),e.teardown()},setOptions:function(t){N(!0,this.options,t)},children:function(){return[]},shouldHide:t.noop,setup:t.noop,prepare:t.noop,teardown:t.noop,directions:[],setReverse:function(t){return this._reverse=t,this}}),m=["left","right","up","down"],v=["in","out"],l("slideIn",{directions:m,divisor:function(t){return this.options.divisor=t,this},prepare:function(t,e){var i,r=this,n=r.element,s=C._outerWidth,o=C._outerHeight,a=ft[r._direction],c=-a.modifier*(a.vertical?o(n):s(n)),l=c/(r.options&&r.options.divisor||1)+G,d="0px";r._reverse&&(i=t,t=e,e=i),H?(t[a.transition]=l,e[a.transition]=d):(t[a.property]=l,e[a.property]=d)}}),l("tile",{directions:m,init:function(t,e,i){h.prototype.init.call(this,t,e),this.options={previous:i}},previousDivisor:function(t){return this.options.previousDivisor=t,this},children:function(){var t=this,e=t._reverse,i=t.options.previous,r=t.options.previousDivisor||1,n=t._direction,s=[C.fx(t.element).slideIn(n).setReverse(e)];return i&&s.push(C.fx(i).slideIn(ft[n].reverse).divisor(r).setReverse(!e)),s}}),d("fade","opacity",1,0),d("zoom","scale",1,.01),l("slideMargin",{prepare:function(t,e){var i,r=this,n=r.element,s=r.options,o=n.data(tt),a=s.offset,c=r._reverse;c||null!==o||n.data(tt,parseFloat(n.css("margin-"+s.axis))),i=n.data(tt)||0,e["margin-"+s.axis]=c?i:i+a}}),l("slideTo",{prepare:function(t,e){var i=this,r=i.element,n=i.options,s=n.offset.split(","),o=i._reverse;H?(e.translatex=o?0:s[0],e.translatey=o?0:s[1]):(e.left=o?0:s[0],e.top=o?0:s[1]),r.css("left")}}),l("expand",{directions:["horizontal","vertical"],restore:[it],prepare:function(t,i){var r=this,n=r.element,s=r.options,o=r._reverse,a="vertical"===r._direction?Y:X,c=n[0].style[a],l=n.data(a),d=parseFloat(l||c),u=B(n.css(a,K)[a]());t.overflow=Z,d=s&&s.reset?u||d:d||u,i[a]=(o?0:d)+G,t[a]=(o?d:0)+G,l===e&&n.data(a,c)},shouldHide:function(){return this._reverse},teardown:function(){var t=this,e=t.element,i="vertical"===t._direction?Y:X,r=e.data(i);r!=K&&r!==U||setTimeout(function(){e.css(i,K).css(i)},0)}}),x={position:"absolute",marginLeft:0,marginTop:0,scale:1},l("transfer",{init:function(t,e){this.element=t,this.options={target:e},this.restore=[]},setup:function(){this.element.appendTo(document.body)},prepare:function(t,e){var i=this,r=i.element,n=P.box(r),s=P.box(i.options.target),o=a(r,"scale"),c=P.fillScale(s,n),l=P.transformOrigin(s,n);N(t,x),e.scale=1,r.css(at,"scale(1)").css(at),r.css(at,"scale("+o+")"),t.top=n.top,t.left=n.left,t.transformOrigin=l.x+G+" "+l.y+G,i._reverse?t.scale=c:e.scale=c}}),_={top:"rect(auto auto $size auto)",bottom:"rect($size auto auto auto)",left:"rect(auto $size auto auto)",right:"rect(auto auto auto $size)"},g={top:{start:"rotatex(0deg)",end:"rotatex(180deg)"},bottom:{start:"rotatex(-180deg)",end:"rotatex(0deg)"},left:{start:"rotatey(0deg)",end:"rotatey(-180deg)"},right:{start:"rotatey(180deg)",end:"rotatey(0deg)"}},l("turningPage",{directions:m,init:function(t,e,i){h.prototype.init.call(this,t,e),this._container=i},prepare:function(t,e){var i=this,r=i._reverse,n=r?ft[i._direction].reverse:i._direction,s=g[n];t.zIndex=1,i._clipInHalf&&(t.clip=u(i._container,C.directions[n].reverse)),t[ct]=Z,e[at]=ut+(r?s.start:s.end),t[at]=ut+(r?s.end:s.start)},setup:function(){this._container.append(this.element)},face:function(t){return this._face=t,this},shouldHide:function(){var t=this,e=t._reverse,i=t._face;return e&&!i||!e&&i},clipInHalf:function(t){return this._clipInHalf=t,this},temporary:function(){return this.element.addClass("temp-page"),this}}),l("staticPage",{directions:m,init:function(t,e,i){h.prototype.init.call(this,t,e),this._container=i},restore:["clip"],prepare:function(t,e){var i=this,r=i._reverse?ft[i._direction].reverse:i._direction;t.clip=u(i._container,r),t.opacity=.999,e.opacity=1},shouldHide:function(){var t=this,e=t._reverse,i=t._face;return e&&!i||!e&&i},face:function(t){return this._face=t,this}}),l("pageturn",{directions:["horizontal","vertical"],init:function(t,e,i,r){h.prototype.init.call(this,t,e),this.options={},this.options.face=i,this.options.back=r},children:function(){var t,e=this,i=e.options,r="horizontal"===e._direction?"left":"top",n=C.directions[r].reverse,s=e._reverse,o=i.face.clone(!0).removeAttr("id"),a=i.back.clone(!0).removeAttr("id"),c=e.element;return s&&(t=r,r=n,n=t),[C.fx(i.face).staticPage(r,c).face(!0).setReverse(s),C.fx(i.back).staticPage(n,c).setReverse(s),C.fx(o).turningPage(r,c).face(!0).clipInHalf(!0).temporary().setReverse(s),C.fx(a).turningPage(n,c).clipInHalf(!0).temporary().setReverse(s)]},prepare:function(t,e){t[lt]=dt,t.transformStyle="preserve-3d",t.opacity=.999,e.opacity=1},teardown:function(){this.element.find(".temp-page").remove()}}),l("flip",{directions:["horizontal","vertical"],init:function(t,e,i,r){h.prototype.init.call(this,t,e),this.options={},this.options.face=i,this.options.back=r},children:function(){var t,e=this,i=e.options,r="horizontal"===e._direction?"left":"top",n=C.directions[r].reverse,s=e._reverse,o=e.element;return s&&(t=r,r=n,n=t),[C.fx(i.face).turningPage(r,o).face(!0).setReverse(s),C.fx(i.back).turningPage(n,o).setReverse(s)]},prepare:function(t){t[lt]=dt,t.transformStyle="preserve-3d"}}),y=!E.mobileOS.android,k=".km-touch-scrollbar, .km-actionsheet-wrapper",l("replace",{_before:t.noop,_after:t.noop,init:function(e,i,r){h.prototype.init.call(this,e),this._previous=t(i),this._transitionClass=r},duration:function(){throw Error("The replace effect does not support duration setting; the effect duration may be customized through the transition class rule")},beforeTransition:function(t){return this._before=t,this},afterTransition:function(t){return this._after=t,this},_both:function(){return t().add(this._element).add(this._previous)},_containerClass:function(){var t=this._direction,e="k-fx k-fx-start k-fx-"+this._transitionClass;return t&&(e+=" k-fx-"+t),this._reverse&&(e+=" k-fx-reverse"),e},complete:function(e){if(!(!this.deferred||e&&t(e.target).is(k))){var i=this.container;i.removeClass("k-fx-end").removeClass(this._containerClass()).off(D.event,this.completeProxy),this._previous.hide().removeClass("k-fx-current"),this.element.removeClass("k-fx-next"),y&&i.css(it,""),this.isAbsolute||this._both().css(nt,""),this.deferred.resolve(),delete this.deferred}},run:function(){if(this._additionalEffects&&this._additionalEffects[0])return this.compositeRun();var e,i=this,r=i.element,n=i._previous,s=r.parents().filter(n.parents()).first(),o=i._both(),a=t.Deferred(),c=r.css(nt);return s.length||(s=r.parent()),this.container=s,this.deferred=a,this.isAbsolute="absolute"==c,this.isAbsolute||o.css(nt,"absolute"),y&&(e=s.css(it),s.css(it,"hidden")),D?(r.addClass("k-fx-hidden"),s.addClass(this._containerClass()),this.completeProxy=t.proxy(this,"complete"),s.on(D.event,this.completeProxy),C.animationFrame(function(){r.removeClass("k-fx-hidden").addClass("k-fx-next"),n.css("display","").addClass("k-fx-current"),i._before(n,r),C.animationFrame(function(){s.removeClass("k-fx-start").addClass("k-fx-end"),i._after(n,r)})})):this.complete(),a.promise()},stop:function(){this.complete()}}),b=C.Class.extend({init:function(){var t=this;t._tickProxy=z(t._tick,t),t._started=!1},tick:t.noop,done:t.noop,onEnd:t.noop,onCancel:t.noop,start:function(){this.enabled()&&(this.done()?this.onEnd():(this._started=!0,C.animationFrame(this._tickProxy)))},enabled:function(){return!0},cancel:function(){this._started=!1,this.onCancel()},_tick:function(){var t=this;t._started&&(t.tick(),t.done()?(t._started=!1,t.onEnd()):C.animationFrame(t._tickProxy))}}),w=b.extend({init:function(t){var e=this;N(e,t),b.fn.init.call(e)},done:function(){return this.timePassed()>=this.duration},timePassed:function(){return Math.min(this.duration,new Date-this.startDate)},moveTo:function(t){var e=this,i=e.movable;e.initial=i[e.axis],e.delta=t.location-e.initial,e.duration="number"==typeof t.duration?t.duration:300,e.tick=e._easeProxy(t.ease),e.startDate=new Date,e.start()},_easeProxy:function(t){var e=this;return function(){e.movable.moveAxis(e.axis,t(e.timePassed(),e.initial,e.delta,e.duration))}}}),N(w,{easeOutExpo:function(t,e,i,r){return t==r?e+i:i*(-Math.pow(2,-10*t/r)+1)+e},easeOutBack:function(t,e,i,r,n){return n=1.70158,i*((t=t/r-1)*t*((n+1)*t+n)+1)+e}}),P.Animation=b,P.Transition=w,P.createEffect=l,P.box=function(e){e=t(e);var i=e.offset();return i.width=C._outerWidth(e),i.height=C._outerHeight(e),i},P.transformOrigin=function(t,e){var i=(t.left-e.left)*e.width/(e.width-t.width),r=(t.top-e.top)*e.height/(e.height-t.height);return{x:isNaN(i)?0:i,y:isNaN(r)?0:r}},P.fillScale=function(t,e){return Math.min(t.width/e.width,t.height/e.height)},P.fitScale=function(t,e){return Math.max(t.width/e.width,t.height/e.height)}}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()});
//# sourceMappingURL=kendo.fx.min.js.map
