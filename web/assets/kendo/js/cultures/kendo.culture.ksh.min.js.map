{"version": 3, "sources": ["cultures/kendo.culture.ksh.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAc,KAChBC,KAAM,MACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,YAAY,aAAa,UAAU,eAAe,YAAY,aAClFC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAChDC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,UAAU,UAAU,OAAO,SAAS,MAAM,QAAQ,QAAQ,QAAQ,YAAY,WAAW,WAAW,YAC5GC,WAAY,OAAO,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,SAE5FG,IAAK,OAAO,OAAO,QACnBC,IAAK,OAAO,OAAO,QACnBC,UACIC,EAAG,aACHC,EAAG,0BACHC,EAAG,mCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,KACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ksh.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ksh\"] = {\n        name: \"ksh\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"€\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Sunndaach\",\"Moondaach\",\"<PERSON><PERSON><PERSON>ach\",\"Met<PERSON><PERSON>\",\"Dunn<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\"],\n                    namesAbbr: [\"<PERSON>.\",\"<PERSON>.\",\"<PERSON>.\",\"<PERSON>.\",\"Du.\",\"Fr.\",\"Sa.\"],\n                    namesShort: [\"Su\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"Sa\"]\n                },\n                months: {\n                    names: [\"Jannewa\",\"Fäbrowa\",\"Määz\",\"Aprell\",\"Mäi\",\"Juuni\",\"Juuli\",\"Oujoß\",\"Septämber\",\"Oktoober\",\"Novämber\",\"Dez<PERSON>mber\"],\n                    namesAbbr: [\"Jan.\",\"Fäb.\",\"Mäz.\",\"Apr.\",\"Mäi\",\"Jun.\",\"Jul.\",\"Ouj.\",\"Säp.\",\"Okt.\",\"Nov.\",\"Dez.\"]\n                },\n                AM: [\"v.m.\",\"v.m.\",\"V.M.\"],\n                PM: [\"n.m.\",\"n.m.\",\"N.M.\"],\n                patterns: {\n                    d: \"d. M. yyyy\",\n                    D: \"dddd, 'dä' d. MMMM yyyy\",\n                    F: \"dddd, 'dä' d. MMMM yyyy HH:mm:ss\",\n                    g: \"d. M. yyyy HH:mm\",\n                    G: \"d. M. yyyy HH:mm:ss\",\n                    m: \"d. MMMM\",\n                    M: \"d. MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \". \",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}