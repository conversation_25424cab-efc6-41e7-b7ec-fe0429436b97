{"version": 3, "sources": ["cultures/kendo.culture.tig-ER.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,iBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,KAAK,OAAO,QAAQ,MAAM,OAAO,YACpDC,WAAY,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,OAC/CC,YAAa,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,QAEpDC,QACIH,OAAQ,QAAQ,QAAQ,MAAM,OAAO,KAAK,KAAK,MAAM,OAAO,SAAS,SAAS,QAAQ,SACtFC,WAAY,MAAM,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,QAEhFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,2BACHC,EAAG,sCACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.tig-ER.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"tig-ER\"] = {\n        name: \"tig-ER\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Eritrean Nakfa\",\n                abbr: \"ERN\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"Nfk\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ሰንበት ዓባይ\",\"ሰኖ\",\"ታላሸኖ\",\"ኣረርባዓ\",\"ከሚሽ\",\"ጅምዓት\",\"ሰንበት ንኢሽ\"],\n                    namesAbbr: [\"ሰ/ዓ\",\"ሰኖ\",\"ታላሸ\",\"ኣረር\",\"ከሚሽ\",\"ጅምዓ\",\"ሰ/ን\"],\n                    namesShort: [\"ሰ/ዓ\",\"ሰኖ\",\"ታላሸ\",\"ኣረር\",\"ከሚሽ\",\"ጅምዓ\",\"ሰ/ን\"]\n                },\n                months: {\n                    names: [\"ጃንዩወሪ\",\"ፌብሩወሪ\",\"ማርች\",\"ኤፕረል\",\"ሜይ\",\"ጁን\",\"ጁላይ\",\"ኦገስት\",\"ሴፕቴምበር\",\"ኦክተውበር\",\"ኖቬምበር\",\"ዲሴምበር\"],\n                    namesAbbr: [\"ጃንዩ\",\"ፌብሩ\",\"ማርች\",\"ኤፕረ\",\"ሜይ\",\"ጁን\",\"ጁላይ\",\"ኦገስ\",\"ሴፕቴ\",\"ኦክተ\",\"ኖቬም\",\"ዲሴም\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd፡ dd MMMM ዮም yyyy gg\",\n                    F: \"dddd፡ dd MMMM ዮም yyyy gg h:mm:ss tt\",\n                    g: \"dd/MM/yyyy h:mm tt\",\n                    G: \"dd/MM/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}