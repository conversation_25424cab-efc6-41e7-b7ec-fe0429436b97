{"version": 3, "sources": ["cultures/kendo.culture.ml-IN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,cAAc,YAAY,YAAY,aAAa,eAAe,aACtFC,WAAY,OAAO,SAAS,QAAQ,OAAO,SAAS,SAAS,OAC7DC,YAAa,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,MAEjDC,QACIH,OAAQ,SAAS,YAAY,UAAU,SAAS,OAAO,MAAM,OAAO,WAAW,aAAa,WAAW,QAAQ,UAC/GC,WAAY,MAAM,SAAS,MAAM,QAAQ,OAAO,MAAM,OAAO,KAAK,WAAW,QAAQ,MAAM,SAE/FG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,qBACHC,EAAG,gCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ml-IN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ml-IN\"] = {\n        name: \"ml-IN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Indian Rupee\",\n                abbr: \"INR\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ഞായറാഴ്‌ച\",\"തിങ്കളാഴ്‌ച\",\"ചൊവ്വാഴ്ച\",\"ബുധനാഴ്‌ച\",\"വ്യാഴാഴ്‌ച\",\"വെള്ളിയാഴ്‌ച\",\"ശനിയാഴ്‌ച\"],\n                    namesAbbr: [\"ഞായർ\",\"തിങ്കൾ\",\"ചൊവ്വ\",\"ബുധൻ\",\"വ്യാഴം\",\"വെള്ളി\",\"ശനി\"],\n                    namesShort: [\"ഞാ\",\"തി\",\"ചൊ\",\"ബു\",\"വ്യാ\",\"വെ\",\"ശ\"]\n                },\n                months: {\n                    names: [\"ജനുവരി\",\"ഫെബ്രുവരി\",\"മാർച്ച്\",\"ഏപ്രിൽ\",\"മേയ്\",\"ജൂൺ\",\"ജൂലൈ\",\"ഓഗസ്റ്റ്\",\"സെപ്റ്റംബർ\",\"ഒക്‌ടോബർ\",\"നവംബർ\",\"ഡിസംബർ\"],\n                    namesAbbr: [\"ജനു\",\"ഫെബ്രു\",\"മാർ\",\"ഏപ്രി\",\"മേയ്\",\"ജൂൺ\",\"ജൂലൈ\",\"ഓഗ\",\"സെപ്റ്റം\",\"ഒക്ടോ\",\"നവം\",\"ഡിസം\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"yyyy, MMMM d, dddd\",\n                    F: \"yyyy, MMMM d, dddd h:mm:ss tt\",\n                    g: \"d/M/yyyy h:mm tt\",\n                    G: \"d/M/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}