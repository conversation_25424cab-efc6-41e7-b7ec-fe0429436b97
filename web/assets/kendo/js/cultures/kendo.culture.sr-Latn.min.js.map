{"version": 3, "sources": ["cultures/kendo.culture.sr-Latn.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,YACXC,KAAM,UACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,aAAa,SAAS,QAAQ,WAAW,QAAQ,UACnEC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAChDC,YAAa,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAErDC,QACIH,OAAQ,SAAS,UAAU,OAAO,QAAQ,MAAM,MAAM,MAAM,SAAS,YAAY,UAAU,WAAW,YACtGC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,YAAY,YAAY,aAC7BC,IAAK,WAAW,WAAW,YAC3BC,UACIC,EAAG,YACHC,EAAG,uBACHC,EAAG,gCACHC,EAAG,kBACHC,EAAG,qBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.sr-Latn.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"sr-Latn\"] = {\n        name: \"sr-Latn\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 0,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"RSD\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"nedelja\",\"ponedeljak\",\"utorak\",\"sreda\",\"četvrtak\",\"petak\",\"subota\"],\n                    namesAbbr: [\"ned\",\"pon\",\"uto\",\"sre\",\"čet\",\"pet\",\"sub\"],\n                    namesShort: [\"ned\",\"pon\",\"uto\",\"sre\",\"čet\",\"pet\",\"sub\"]\n                },\n                months: {\n                    names: [\"januar\",\"februar\",\"mart\",\"april\",\"maj\",\"jun\",\"jul\",\"avgust\",\"septembar\",\"oktobar\",\"novembar\",\"decembar\"],\n                    namesAbbr: [\"jan\",\"feb\",\"mar\",\"apr\",\"maj\",\"jun\",\"jul\",\"avg\",\"sep\",\"okt\",\"nov\",\"dec\"]\n                },\n                AM: [\"pre podne\",\"pre podne\",\"PRE PODNE\"],\n                PM: [\"po podne\",\"po podne\",\"PO PODNE\"],\n                patterns: {\n                    d: \"d.M.yyyy.\",\n                    D: \"dddd, dd. MMMM yyyy.\",\n                    F: \"dddd, dd. MMMM yyyy. HH.mm.ss\",\n                    g: \"d.M.yyyy. HH.mm\",\n                    G: \"d.M.yyyy. HH.mm.ss\",\n                    m: \"d. MMMM\",\n                    M: \"d. MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH.mm\",\n                    T: \"HH.mm.ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy.\",\n                    Y: \"MMMM yyyy.\"\n                },\n                \"/\": \".\",\n                \":\": \".\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}