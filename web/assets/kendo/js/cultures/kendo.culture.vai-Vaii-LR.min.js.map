{"version": 3, "sources": ["cultures/kendo.culture.vai-Vaii-LR.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,gBACXC,KAAM,cACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,kBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,OAC9CC,WAAY,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,OAClDC,YAAa,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,QAEvDC,QACIH,OAAQ,SAAS,OAAO,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,WAAW,UACzEC,WAAY,SAAS,OAAO,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,WAAW,WAEjFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,oBACHC,EAAG,+BACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.vai-Vaii-LR.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"vai-Vaii-LR\"] = {\n        name: \"vai-Vaii-LR\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Liberian Dollar\",\n                abbr: \"LRD\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"$\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ꕞꕌꔵ\",\"ꗳꗡꘉ\",\"ꕚꕞꕚ\",\"ꕉꕞꕒ\",\"ꕉꔤꕆꕢ\",\"ꕉꔤꕀꕮ\",\"ꔻꔬꔳ\"],\n                    namesAbbr: [\"ꕞꕌꔵ\",\"ꗳꗡꘉ\",\"ꕚꕞꕚ\",\"ꕉꕞꕒ\",\"ꕉꔤꕆꕢ\",\"ꕉꔤꕀꕮ\",\"ꔻꔬꔳ\"],\n                    namesShort: [\"ꕞꕌꔵ\",\"ꗳꗡꘉ\",\"ꕚꕞꕚ\",\"ꕉꕞꕒ\",\"ꕉꔤꕆꕢ\",\"ꕉꔤꕀꕮ\",\"ꔻꔬꔳ\"]\n                },\n                months: {\n                    names: [\"ꖨꕪꖃ ꔞꕮ\",\"ꕒꕡꖝꖕ\",\"ꕾꖺ\",\"ꖢꖕ\",\"ꖑꕱ\",\"6\",\"7\",\"ꗛꔕ\",\"ꕢꕌ\",\"ꕭꖃ\",\"ꔞꘋꕔꕿ ꕸꖃꗏ\",\"ꖨꕪꕱ ꗏꕮ\"],\n                    namesAbbr: [\"ꖨꕪꖃ ꔞꕮ\",\"ꕒꕡꖝꖕ\",\"ꕾꖺ\",\"ꖢꖕ\",\"ꖑꕱ\",\"6\",\"7\",\"ꗛꔕ\",\"ꕢꕌ\",\"ꕭꖃ\",\"ꔞꘋꕔꕿ ꕸꖃꗏ\",\"ꖨꕪꕱ ꗏꕮ\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd, d MMMM yyyy\",\n                    F: \"dddd, d MMMM yyyy h:mm:ss tt\",\n                    g: \"dd/MM/yyyy h:mm tt\",\n                    G: \"dd/MM/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}