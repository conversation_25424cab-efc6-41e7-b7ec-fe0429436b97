{"version": 3, "sources": ["cultures/kendo.culture.kab.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAc,KAChBC,KAAM,MACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,UAAU,SAAS,SAAS,UAAU,UAChEC,WAAY,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,OAClDC,YAAa,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,QAEvDC,QACIH,OAAQ,WAAW,QAAQ,SAAS,SAAS,QAAQ,QAAQ,QAAQ,OAAO,UAAU,QAAQ,WAAW,YACzGC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,mBACHC,EAAG,8BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.kab.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"kab\"] = {\n        name: \"kab\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-n$\",\"n$\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"DA\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Yanass\",\"Sanass\",\"Kraḍass\",\"Kuẓass\",\"<PERSON>ass\",\"Sḍ<PERSON>ss\",\"Sayass\"],\n                    namesAbbr: [\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Kuẓ\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>\"],\n                    namesShort: [\"<PERSON>\",\"<PERSON>\",\"<PERSON>ra<PERSON>\",\"Kuẓ\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Say\"]\n                },\n                months: {\n                    names: [\"<PERSON>nnayer\",\"Fuṛar\",\"Meɣres\",\"Yebrir\",\"Mayyu\",\"Yunyu\",\"Yulyu\",\"Ɣuct\",\"<PERSON>tembeṛ\",\"Tubeṛ\",\"Nunembeṛ\",\"Duǧembeṛ\"],\n                    names<PERSON>bbr: [\"Yen\",\"<PERSON>r\",\"Meɣ\",\"Yeb\",\"May\",\"Yun\",\"Yul\",\"Ɣuc\",\"<PERSON>te\",\"Tub\",\"Nun\",\"Duǧ\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"dddd d MMMM yyyy\",\n                    F: \"dddd d MMMM yyyy h:mm:ss tt\",\n                    g: \"d/M/yyyy h:mm tt\",\n                    G: \"d/M/yyyy h:mm:ss tt\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 6\n            }\n        }\n    }\n})(this);\n}));"]}