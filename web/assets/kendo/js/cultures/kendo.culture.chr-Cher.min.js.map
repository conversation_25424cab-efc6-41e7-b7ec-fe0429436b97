{"version": 3, "sources": ["cultures/kendo.culture.chr-Cher.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,aACXC,KAAM,WACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,UAAU,QAAQ,QAAQ,QAAQ,SAAS,WAC7DC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAChDC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,QAAQ,MAAM,MAAM,MAAM,QAAQ,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,QACnFC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,qBACHC,EAAG,gCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.chr-Cher.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"chr-Cher\"] = {\n        name: \"chr-Cher\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"$\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ᎤᎾᏙᏓᏆᏍᎬ\",\"ᎤᎾᏙᏓᏉᏅᎯ\",\"ᏔᎵᏁᎢᎦ\",\"ᏦᎢᏁᎢᎦ\",\"ᏅᎩᏁᎢᎦ\",\"ᏧᎾᎩᎶᏍᏗ\",\"ᎤᎾᏙᏓᏈᏕᎾ\"],\n                    namesAbbr: [\"ᏆᏍᎬ\",\"ᏉᏅᎯ\",\"ᏔᎵᏁ\",\"ᏦᎢᏁ\",\"ᏅᎩᏁ\",\"ᏧᎾᎩ\",\"ᏈᏕᎾ\"],\n                    namesShort: [\"ᏆᏍ\",\"ᏉᏅ\",\"ᏔᎵ\",\"ᏦᎢ\",\"ᏅᎩ\",\"ᏧᎾ\",\"ᏈᏕ\"]\n                },\n                months: {\n                    names: [\"ᎤᏃᎸᏔᏅ\",\"ᎧᎦᎵ\",\"ᎠᏅᏱ\",\"ᏝᏬᏂ\",\"ᎠᏂᏍᎬᏘ\",\"ᏕᎭᎷᏱ\",\"ᎫᏰᏉᏂ\",\"ᎦᎶᏂ\",\"ᏚᎵᏍᏗ\",\"ᏚᏂᏅᏗ\",\"ᏅᏓᏕᏆ\",\"ᎤᏍᎩᏱ\"],\n                    namesAbbr: [\"ᎤᏃᎸ\",\"ᎧᎦᎵ\",\"ᎠᏅᏱ\",\"ᏝᏬᏂ\",\"ᎠᏂᏍ\",\"ᏕᎭᎷ\",\"ᎫᏰᏉ\",\"ᎦᎶᏂ\",\"ᏚᎵᏍ\",\"ᏚᏂᏅ\",\"ᏅᏓᏕ\",\"ᎤᏍᎩ\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"M/d/yyyy\",\n                    D: \"dddd, MMMM dd,yyyy\",\n                    F: \"dddd, MMMM dd,yyyy h:mm:ss tt\",\n                    g: \"M/d/yyyy h:mm tt\",\n                    G: \"M/d/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}