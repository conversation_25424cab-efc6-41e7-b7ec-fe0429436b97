{"version": 3, "sources": ["cultures/kendo.culture.ps-AF.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,iBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,UAAU,UAAU,UAAU,OAAO,QAC/DC,WAAY,SAAS,SAAS,UAAU,UAAU,UAAU,OAAO,QACnEC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,MAAM,OAAO,UAAU,QAAQ,OAAO,MAAM,MAAM,MAAM,QAAQ,SAAS,SAAS,MAC1FC,WAAY,MAAM,OAAO,UAAU,QAAQ,OAAO,MAAM,MAAM,MAAM,QAAQ,SAAS,SAAS,OAElGG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,cACHC,EAAG,sBACHC,EAAG,gBACHC,EAAG,mBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,UACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ps-AF.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ps-AF\"] = {\n        name: \"ps-AF\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Afghan Afghani\",\n                abbr: \"AFN\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 0,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"؋\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"یکشنبه\",\"دوشنبه\",\"سه‌شنبه\",\"چارشنبه\",\"پنجشنبه\",\"جمعه\",\"شنبه\"],\n                    namesAbbr: [\"یکشنبه\",\"دوشنبه\",\"سه‌شنبه\",\"چارشنبه\",\"پنجشنبه\",\"جمعه\",\"شنبه\"],\n                    namesShort: [\"ی\",\"د\",\"س\",\"چ\",\"پ\",\"ج\",\"ش\"]\n                },\n                months: {\n                    names: [\"وری\",\"غويی\",\"غبرګولی\",\"چنګاښ\",\"زمری\",\"وږی\",\"تله\",\"لړم\",\"ليندۍ\",\"مرغومی\",\"سلواغه\",\"كب\"],\n                    namesAbbr: [\"وری\",\"غويی\",\"غبرګولی\",\"چنګاښ\",\"زمری\",\"وږی\",\"تله\",\"لړم\",\"ليندۍ\",\"مرغومی\",\"سلواغه\",\"كب\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"yyyy/M/d\",\n                    D: \"d MMMM yyyy\",\n                    F: \"d MMMM yyyy H:mm:ss\",\n                    g: \"yyyy/M/d H:mm\",\n                    G: \"yyyy/M/d H:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"H:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 6\n            }\n        }\n    }\n})(this);\n}));"]}