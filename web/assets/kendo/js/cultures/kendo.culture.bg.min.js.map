{"version": 3, "sources": ["cultures/kendo.culture.bg.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,aAAa,UAAU,QAAQ,YAAY,QAAQ,UACpEC,WAAY,MAAM,MAAM,KAAK,KAAK,OAAO,MAAM,OAC/CC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,SAAS,WAAW,OAAO,QAAQ,MAAM,MAAM,MAAM,SAAS,YAAY,WAAW,UAAU,YACvGC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,gBACHC,EAAG,oBACHC,EAAG,4BACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,UACHC,EAAG,iCACHC,EAAG,iBACHC,EAAG,kBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.bg.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"bg\"] = {\n        name: \"bg\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"лв.\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"неделя\",\"понеделник\",\"вторник\",\"сряда\",\"четвъртък\",\"петък\",\"събота\"],\n                    namesAbbr: [\"нед\",\"пон\",\"вт\",\"ср\",\"четв\",\"пет\",\"съб\"],\n                    namesShort: [\"нд\",\"пн\",\"вт\",\"ср\",\"чт\",\"пт\",\"сб\"]\n                },\n                months: {\n                    names: [\"януари\",\"февруари\",\"март\",\"април\",\"май\",\"юни\",\"юли\",\"август\",\"септември\",\"октомври\",\"ноември\",\"декември\"],\n                    namesAbbr: [\"яну\",\"фев\",\"мар\",\"апр\",\"май\",\"юни\",\"юли\",\"авг\",\"сеп\",\"окт\",\"ное\",\"дек\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"d.M.yyyy 'г.'\",\n                    D: \"dd MMMM yyyy 'г.'\",\n                    F: \"dd MMMM yyyy 'г.' H:mm:ss\",\n                    g: \"d.M.yyyy 'г.' H:mm\",\n                    G: \"d.M.yyyy 'г.' H:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"H:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy 'г.'\",\n                    Y: \"MMMM yyyy 'г.'\"\n                },\n                \"/\": \".\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}