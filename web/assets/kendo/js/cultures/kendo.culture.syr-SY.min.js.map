{"version": 3, "sources": ["cultures/kendo.culture.syr-SY.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,UAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,YAAY,YAAY,aAAa,YAAY,SAAS,QAC5EC,WAAY,SAAS,SAAS,SAAS,SAAS,SAAS,QAAQ,OACjEC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,YAAY,MAAM,MAAM,OAAO,MAAM,QAAQ,OAAO,KAAK,QAAQ,YAAY,YAAY,aACjGC,WAAY,SAAS,MAAM,MAAM,OAAO,MAAM,QAAQ,OAAO,KAAK,QAAQ,SAAS,SAAS,WAEhGG,IAAK,MAAM,MAAM,OACjBC,IAAK,MAAM,MAAM,OACjBC,UACIC,EAAG,aACHC,EAAG,gBACHC,EAAG,4BACHC,EAAG,sBACHC,EAAG,yBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,WACHC,EAAG,cACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.syr-SY.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"syr-SY\"] = {\n        name: \"syr-SY\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Syrian Pound\",\n                abbr: \"SYP\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"ܠ.ܣ.‏\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ܚܕ ܒܫܒܐ\",\"ܬܪܝܢ ܒܫܒܐ\",\"ܬܠܬܐ ܒܫܒܐ\",\"ܐܪܒܥܐ ܒܫܒܐ\",\"ܚܡܫܐ ܒܫܒܐ\",\"ܥܪܘܒܬܐ\",\"ܫܒܬܐ\"],\n                    namesAbbr: [\"܏ܐ ܏ܒܫ\",\"܏ܒ ܏ܒܫ\",\"܏ܓ ܏ܒܫ\",\"܏ܕ ܏ܒܫ\",\"܏ܗ ܏ܒܫ\",\"܏ܥܪܘܒ\",\"܏ܫܒ\"],\n                    namesShort: [\"ܐ\",\"ܒ\",\"ܓ\",\"ܕ\",\"ܗ\",\"ܥ\",\"ܫ\"]\n                },\n                months: {\n                    names: [\"ܟܢܘܢ ܐܚܪܝ\",\"ܫܒܛ\",\"ܐܕܪ\",\"ܢܝܣܢ\",\"ܐܝܪ\",\"ܚܙܝܪܢ\",\"ܬܡܘܙ\",\"ܐܒ\",\"ܐܝܠܘܠ\",\"ܬܫܪܝ ܩܕܝܡ\",\"ܬܫܪܝ ܐܚܪܝ\",\"ܟܢܘܢ ܩܕܝܡ\"],\n                    namesAbbr: [\"܏ܟܢ ܏ܒ\",\"ܫܒܛ\",\"ܐܕܪ\",\"ܢܝܣܢ\",\"ܐܝܪ\",\"ܚܙܝܪܢ\",\"ܬܡܘܙ\",\"ܐܒ\",\"ܐܝܠܘܠ\",\"܏ܬܫ ܏ܐ\",\"܏ܬܫ ܏ܒ\",\"܏ܟܢ ܏ܐ\"]\n                },\n                AM: [\"ܩ.ܛ\",\"ܩ.ܛ\",\"ܩ.ܛ\"],\n                PM: [\"ܒ.ܛ\",\"ܒ.ܛ\",\"ܒ.ܛ\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dd MMMM, yyyy\",\n                    F: \"dd MMMM, yyyy hh:mm:ss tt\",\n                    g: \"dd/MM/yyyy hh:mm tt\",\n                    G: \"dd/MM/yyyy hh:mm:ss tt\",\n                    m: \"MMMM dd\",\n                    M: \"MMMM dd\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"hh:mm tt\",\n                    T: \"hh:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}