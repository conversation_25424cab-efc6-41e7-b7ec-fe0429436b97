{"version": 3, "sources": ["cultures/kendo.culture.sa-IN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,WAAW,aAAa,WAAW,YAAY,aAAa,YAC/EC,WAAY,MAAM,MAAM,OAAO,MAAM,OAAO,QAAQ,OACpDC,YAAa,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,MAE7CC,QACIH,OAAQ,YAAY,YAAY,QAAQ,SAAS,KAAK,MAAM,OAAO,QAAQ,WAAW,UAAU,YAAY,WAC5GC,WAAY,YAAY,YAAY,QAAQ,SAAS,KAAK,MAAM,OAAO,QAAQ,WAAW,UAAU,YAAY,YAEpHG,IAAK,cAAc,cAAc,eACjCC,IAAK,eAAe,eAAe,gBACnCC,UACIC,EAAG,aACHC,EAAG,oBACHC,EAAG,6BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.sa-IN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"sa-IN\"] = {\n        name: \"sa-IN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Indian Rupee\",\n                abbr: \"INR\",\n                pattern: [\"$ -n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"रविवासरः\",\"सोमवासरः\",\"मङ्गलवासरः\",\"बुधवासरः\",\"गुरुवासरः\",\"शुक्रवासरः\",\"शनिवासरः\"],\n                    namesAbbr: [\"रवि\",\"सोम\",\"मङ्ग\",\"बुध\",\"गुरु\",\"शुक्र\",\"शनि\"],\n                    namesShort: [\"र\",\"सो\",\"म\",\"बु\",\"गु\",\"शु\",\"श\"]\n                },\n                months: {\n                    names: [\"जान्युअरी\",\"फेब्रुअरी\",\"मार्च\",\"एप्रिल\",\"मे\",\"जून\",\"जुलै\",\"ऑगस्ट\",\"सप्टेंबर\",\"ऑक्टोबर\",\"नोव्हेंबर\",\"डिसेंबर\"],\n                    namesAbbr: [\"जान्युअरी\",\"फेब्रुअरी\",\"मार्च\",\"एप्रिल\",\"मे\",\"जुन\",\"जुलै\",\"ऑगस्ट\",\"सप्टेंबर\",\"ऑक्टोबर\",\"नोव्हेंबर\",\"डिसेंबर\"]\n                },\n                AM: [\"मध्यानपूर्व\",\"मध्यानपूर्व\",\"मध्यानपूर्व\"],\n                PM: [\"मध्यानपच्यात\",\"मध्यानपच्यात\",\"मध्यानपच्यात\"],\n                patterns: {\n                    d: \"dd-MM-yyyy\",\n                    D: \"dd MMMM yyyy dddd\",\n                    F: \"dd MMMM yyyy dddd HH:mm:ss\",\n                    g: \"dd-MM-yyyy HH:mm\",\n                    G: \"dd-MM-yyyy HH:mm:ss\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}