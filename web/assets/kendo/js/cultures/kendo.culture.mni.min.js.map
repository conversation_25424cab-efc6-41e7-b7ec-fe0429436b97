{"version": 3, "sources": ["cultures/kendo.culture.mni.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAc,KAChBC,KAAM,MACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,aAAa,eAAe,aAAa,UAAU,OAAO,SAC9EC,WAAY,YAAY,aAAa,eAAe,aAAa,UAAU,OAAO,SAClFC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,SAAS,aAAa,OAAO,QAAQ,SAAS,OAAO,OAAO,OAAO,QAAQ,QAAQ,QAAQ,SACnGC,WAAY,SAAS,aAAa,OAAO,QAAQ,SAAS,OAAO,OAAO,OAAO,QAAQ,QAAQ,QAAQ,UAE3GG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,aACHC,EAAG,eACHC,EAAG,wBACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.mni.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"mni\"] = {\n        name: \"mni\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"নোংমাইজিং\",\"নিংথৌকাঃবা\",\"লৈঃপাক পোকপা\",\"য়ুমশা কৈশা\",\"সগোনসেল\",\"ইরাই\",\"থাংজা\"],\n                    namesAbbr: [\"নোংমাইজিং\",\"নিংথৌকাঃবা\",\"লৈঃপাক পোকপা\",\"য়ুমশা কৈশা\",\"সগোনসেল\",\"ইরাই\",\"থাংজা\"],\n                    namesShort: [\"ন\",\"ন\",\"ল\",\"য়\",\"স\",\"ই\",\"থ\"]\n                },\n                months: {\n                    names: [\"ৱাকিচঙ\",\"লাঘাফাইরেল\",\"লমদা\",\"সজিবু\",\"কালেন্\",\"ইঙাঃ\",\"ইঙেন\",\"থৱান\",\"লাংবন\",\"মেরাঃ\",\"হাগৈঃ\",\"পোইনু\"],\n                    namesAbbr: [\"ৱাকিচঙ\",\"লাঘাফাইরেল\",\"লমদা\",\"সজিবু\",\"কালেন্\",\"ইঙাঃ\",\"ইঙেন\",\"থৱান\",\"লাংবন\",\"মেরাঃ\",\"হাগৈঃ\",\"পোইনু\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dd MMMM yyyy\",\n                    F: \"dd MMMM yyyy HH:mm:ss\",\n                    g: \"dd/MM/yyyy HH:mm\",\n                    G: \"dd/MM/yyyy HH:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}