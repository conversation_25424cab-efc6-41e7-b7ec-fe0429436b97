{"version": 3, "sources": ["cultures/kendo.culture.nus.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAc,KAChBC,KAAM,MACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,YAAY,YAAY,cAAc,cAAc,eAAe,eACvFC,WAAY,MAAM,OAAO,MAAM,QAAQ,QAAQ,SAAS,SACxDC,YAAa,MAAM,OAAO,MAAM,QAAQ,QAAQ,SAAS,UAE7DC,QACIH,OAAQ,gBAAgB,MAAM,UAAU,OAAO,OAAO,WAAW,cAAc,UAAU,OAAO,QAAQ,MAAM,mBAC9GC,WAAY,OAAO,MAAM,SAAS,OAAO,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,QAExFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,YACHC,EAAG,mBACHC,EAAG,8BACHC,EAAG,oBACHC,EAAG,uBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.nus.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"nus\"] = {\n        name: \"nus\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"£\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Cäŋ kuɔth\",\"<PERSON><PERSON> la̱t\",\"<PERSON>ɛ<PERSON> lätni\",\"Diɔ̱k lätni\",\"Ŋuaan lätni\",\"<PERSON><PERSON><PERSON><PERSON> l<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON> lätni\"],\n                    namesAbbr: [\"<PERSON>äŋ\",\"<PERSON><PERSON>\",\"<PERSON>ɛw\",\"Diɔ̱k\",\"Ŋuaan\",\"<PERSON><PERSON><PERSON><PERSON>\",\"Bäkɛl\"],\n                    namesShort: [\"Cäŋ\",\"Jiec\",\"Rɛw\",\"Diɔ̱k\",\"Ŋuaan\",\"Dhieec\",\"Bäkɛl\"]\n                },\n                months: {\n                    names: [\"Tiop thar pɛt\",\"Pɛt\",\"Duɔ̱ɔ̱ŋ\",\"Guak\",\"Duät\",\"Kornyoot\",\"Pay yie̱tni\",\"Tho̱o̱r\",\"Tɛɛr\",\"Laath\",\"Kur\",\"Tio̱p in di̱i̱t\"],\n                    namesAbbr: [\"Tiop\",\"Pɛt\",\"Duɔ̱ɔ̱\",\"Guak\",\"Duä\",\"Kor\",\"Pay\",\"Thoo\",\"Tɛɛ\",\"Laa\",\"Kur\",\"Tid\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"d/MM/yyyy\",\n                    D: \"dddd d MMMM yyyy\",\n                    F: \"dddd d MMMM yyyy h:mm:ss tt\",\n                    g: \"d/MM/yyyy h:mm tt\",\n                    G: \"d/MM/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}