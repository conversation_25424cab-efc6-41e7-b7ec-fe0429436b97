{"version": 3, "sources": ["cultures/kendo.culture.rw.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,cAAc,aAAa,cAAc,cAAc,YAAY,cAAc,kBACzFC,WAAY,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,QACtDC,YAAa,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,SAE3DC,QACIH,OAAQ,WAAW,cAAc,UAAU,OAAO,YAAY,SAAS,WAAW,SAAS,QAAQ,WAAW,aAAa,WAC3HC,WAAY,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,SAE7FG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,qBACHC,EAAG,8BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.rw.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"rw\"] = {\n        name: \"rw\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 0,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"RF\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Ku cyumweru\",\"<PERSON>wa mbere\",\"<PERSON>wa kabiri\",\"<PERSON>wa gatatu\",\"<PERSON><PERSON> kane\",\"<PERSON><PERSON> gatanu\",\"<PERSON>wa gatandatu\"],\n                    namesAbbr: [\"cyu.\",\"mbe.\",\"kab.\",\"gtu.\",\"kan.\",\"gnu.\",\"gnd.\"],\n                    namesShort: [\"cyu.\",\"mbe.\",\"kab.\",\"gtu.\",\"kan.\",\"gnu.\",\"gnd.\"]\n                },\n                months: {\n                    names: [\"Mutarama\",\"Gashyantare\",\"Werurwe\",\"Mata\",\"Gicuransi\",\"<PERSON>a\",\"Nyakanga\",\"Kanama\",\"Nzeli\",\"Ukwakira\",\"Ugushyingo\",\"Ukuboza\"],\n                    namesAbbr: [\"mut.\",\"gas.\",\"wer.\",\"mat.\",\"gic.\",\"kam.\",\"nya.\",\"kan.\",\"nze.\",\"ukw.\",\"ugu.\",\"uku.\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"yyyy/MM/dd\",\n                    D: \"dddd, yyyy MMMM dd\",\n                    F: \"dddd, yyyy MMMM dd HH:mm:ss\",\n                    g: \"yyyy/MM/dd HH:mm\",\n                    G: \"yyyy/MM/dd HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}