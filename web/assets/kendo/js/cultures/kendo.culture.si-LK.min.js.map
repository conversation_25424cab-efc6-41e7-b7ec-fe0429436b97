{"version": 3, "sources": ["cultures/kendo.culture.si-LK.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,mBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,QAAQ,QAAQ,YAAY,QAAQ,iBAAiB,WAAW,aACxEC,WAAY,QAAQ,QAAQ,MAAM,QAAQ,UAAU,OAAO,OAC3DC,YAAa,MAAM,MAAM,KAAK,MAAM,QAAQ,OAAO,QAEvDC,QACIH,OAAQ,SAAS,WAAW,SAAS,WAAW,OAAO,OAAO,OAAO,UAAU,cAAc,WAAW,YAAY,aACpHC,WAAY,KAAK,MAAM,OAAO,WAAW,OAAO,OAAO,OAAO,MAAM,OAAO,MAAM,OAAO,SAE5FG,IAAK,QAAQ,QAAQ,SACrBC,IAAK,OAAO,OAAO,QACnBC,UACIC,EAAG,aACHC,EAAG,oBACHC,EAAG,6BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.si-LK.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"si-LK\"] = {\n        name: \"si-LK\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Sri Lankan Rupee\",\n                abbr: \"LKR\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"රු.\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ඉරිදා\",\"සඳුදා\",\"අඟහරුවාදා\",\"බදාදා\",\"බ්‍රහස්පතින්දා\",\"සිකුරාදා\",\"සෙනසුරාදා\"],\n                    namesAbbr: [\"ඉරිදා\",\"සඳුදා\",\"අඟහ\",\"බදාදා\",\"බ්‍රහස්\",\"සිකු\",\"සෙන\"],\n                    namesShort: [\"ඉරි\",\"සඳු\",\"අඟ\",\"බදා\",\"බ්‍රහ\",\"සිකු\",\"සෙන\"]\n                },\n                months: {\n                    names: [\"ජනවාරි\",\"පෙබරවාරි\",\"මාර්තු\",\"අප්‍රේල්\",\"මැයි\",\"ජූනි\",\"ජූලි\",\"අගෝස්තු\",\"සැප්තැම්බර්\",\"ඔක්තෝබර්\",\"නොවැම්බර්\",\"දෙසැම්බර්\"],\n                    namesAbbr: [\"ජන\",\"පෙබ\",\"මාර්\",\"අප්‍රේල්\",\"මැයි\",\"ජූනි\",\"ජූලි\",\"අගෝ\",\"සැප්\",\"ඔක්\",\"නොවැ\",\"දෙසැ\"]\n                },\n                AM: [\"පෙ.ව.\",\"පෙ.ව.\",\"පෙ.ව.\"],\n                PM: [\"ප.ව.\",\"ප.ව.\",\"ප.ව.\"],\n                patterns: {\n                    d: \"yyyy-MM-dd\",\n                    D: \"yyyy MMMM d, dddd\",\n                    F: \"yyyy MMMM d, dddd HH.mm.ss\",\n                    g: \"yyyy-MM-dd HH.mm\",\n                    G: \"yyyy-MM-dd HH.mm.ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH.mm\",\n                    T: \"HH.mm.ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"-\",\n                \":\": \".\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}