{"version": 3, "sources": ["cultures/kendo.culture.lo.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,SAAS,YAAY,SAAS,WAAW,SAAS,WACrEC,WAAY,WAAW,SAAS,YAAY,SAAS,WAAW,SAAS,WACzEC,YAAa,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,OAElDC,QACIH,OAAQ,SAAS,QAAQ,OAAO,OAAO,UAAU,SAAS,UAAU,QAAQ,QAAQ,OAAO,QAAQ,SACnGC,WAAY,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO,OAAO,OAAO,OAAO,SAE9FG,IAAK,WAAW,WAAW,YAC3BC,IAAK,WAAW,WAAW,YAC3BC,UACIC,EAAG,WACHC,EAAG,yBACHC,EAAG,iCACHC,EAAG,gBACHC,EAAG,mBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,UACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.lo.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"lo\"] = {\n        name: \"lo\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"$-n\",\"$n\"],\n                decimals: 0,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"₭\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ວັນອາທິດ\",\"ວັນຈັນ\",\"ວັນອັງຄານ\",\"ວັນພຸດ\",\"ວັນພະຫັດ\",\"ວັນສຸກ\",\"ວັນເສົາ\"],\n                    namesAbbr: [\"ວັນອາທິດ\",\"ວັນຈັນ\",\"ວັນອັງຄານ\",\"ວັນພຸດ\",\"ວັນພະຫັດ\",\"ວັນສຸກ\",\"ວັນເສົາ\"],\n                    namesShort: [\"ອາ.\",\"ຈ.\",\"ອ.\",\"ພ.\",\"ພຫ.\",\"ສຸ.\",\"ສ.\"]\n                },\n                months: {\n                    names: [\"ມັງກອນ\",\"ກຸມພາ\",\"ມີນາ\",\"ເມສາ\",\"ພຶດສະພາ\",\"ມິຖຸນາ\",\"ກໍລະກົດ\",\"ສິງຫາ\",\"ກັນຍາ\",\"ຕຸລາ\",\"ພະຈິກ\",\"ທັນວາ\"],\n                    namesAbbr: [\"ມ.ກ.\",\"ກ.ພ.\",\"ມ.ນ.\",\"ມ.ສ.\",\"ພ.ພ.\",\"ມິ.ຖ.\",\"ກ.ລ.\",\"ສ.ຫ.\",\"ກ.ຍ.\",\"ຕ.ລ.\",\"ພ.ຈ.\",\"ທ.ວ.\"]\n                },\n                AM: [\"ກ່ອນທ່ຽງ\",\"ກ່ອນທ່ຽງ\",\"ກ່ອນທ່ຽງ\"],\n                PM: [\"ຫຼັງທ່ຽງ\",\"ຫຼັງທ່ຽງ\",\"ຫຼັງທ່ຽງ\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"dddd ທີ d MMMM gg yyyy\",\n                    F: \"dddd ທີ d MMMM gg yyyy H:mm:ss\",\n                    g: \"d/M/yyyy H:mm\",\n                    G: \"d/M/yyyy H:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"H:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}