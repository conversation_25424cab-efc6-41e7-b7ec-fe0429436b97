{"version": 3, "sources": ["cultures/kendo.culture.ewo-CM.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,4BACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,SAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,qBAAqB,qBAAqB,qBAAqB,SAAS,UAClGC,WAAY,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,OAClDC,YAAa,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,QAEvDC,QACIH,OAAQ,WAAW,WAAW,YAAY,aAAa,YAAY,cAAc,gBAAgB,YAAY,aAAa,YAAY,oBAAoB,oBAC1JC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,SAEnFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,mBACHC,EAAG,4BACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ewo-CM.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ewo-CM\"] = {\n        name: \"ewo-CM\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Central African CFA Franc\",\n                abbr: \"XAF\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 0,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"FCFA\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"sɔ́ndɔ\",\"mɔ́ndi\",\"sɔ́ndɔ məlú mə́bɛ̌\",\"sɔ́ndɔ məlú mə́lɛ́\",\"sɔ́ndɔ məlú mə́nyi\",\"fúladé\",\"séradé\"],\n                    namesAbbr: [\"sɔ́n\",\"mɔ́n\",\"smb\",\"sml\",\"smn\",\"fúl\",\"sér\"],\n                    namesShort: [\"sɔ́n\",\"mɔ́n\",\"smb\",\"sml\",\"smn\",\"fúl\",\"sér\"]\n                },\n                months: {\n                    names: [\"ngɔn osú\",\"ngɔn bɛ̌\",\"ngɔn lála\",\"ngɔn nyina\",\"ngɔn tána\",\"ngɔn saməna\",\"ngɔn zamgbála\",\"ngɔn mwom\",\"ngɔn ebulú\",\"ngɔn awóm\",\"ngɔn awóm ai dziá\",\"ngɔn awóm ai bɛ̌\"],\n                    namesAbbr: [\"ngo\",\"ngb\",\"ngl\",\"ngn\",\"ngt\",\"ngs\",\"ngz\",\"ngm\",\"nge\",\"nga\",\"ngad\",\"ngab\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"dddd d MMMM yyyy\",\n                    F: \"dddd d MMMM yyyy HH:mm:ss\",\n                    g: \"d/M/yyyy HH:mm\",\n                    G: \"d/M/yyyy HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}