{"version": 3, "sources": ["cultures/kendo.culture.sr-Cyrl-RS.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,eACXC,KAAM,aACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,gBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,SAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,YAAY,SAAS,QAAQ,WAAW,QAAQ,UACjEC,WAAY,OAAO,OAAO,MAAM,MAAM,OAAO,OAAO,QACpDC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,SAAS,UAAU,OAAO,QAAQ,MAAM,MAAM,MAAM,SAAS,YAAY,UAAU,WAAW,YACtGC,WAAY,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,QAAQ,OAAO,OAAO,SAE3FG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,cACHC,EAAG,gBACHC,EAAG,wBACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,UACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.sr-Cyrl-RS.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"sr-Cyrl-RS\"] = {\n        name: \"sr-Cyrl-RS\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Serbian Dinar\",\n                abbr: \"RSD\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"дин.\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"недеља\",\"понедељак\",\"уторак\",\"среда\",\"четвртак\",\"петак\",\"субота\"],\n                    namesAbbr: [\"нед.\",\"пон.\",\"ут.\",\"ср.\",\"чет.\",\"пет.\",\"суб.\"],\n                    namesShort: [\"не\",\"по\",\"ут\",\"ср\",\"че\",\"пе\",\"су\"]\n                },\n                months: {\n                    names: [\"јануар\",\"фебруар\",\"март\",\"април\",\"мај\",\"јун\",\"јул\",\"август\",\"септембар\",\"октобар\",\"новембар\",\"децембар\"],\n                    namesAbbr: [\"јан.\",\"феб.\",\"март\",\"апр.\",\"мај\",\"јун\",\"јул\",\"авг.\",\"септ.\",\"окт.\",\"нов.\",\"дец.\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"dd.MM.yyyy.\",\n                    D: \"d. MMMM yyyy.\",\n                    F: \"d. MMMM yyyy. H:mm:ss\",\n                    g: \"dd.MM.yyyy. H:mm\",\n                    G: \"dd.MM.yyyy. H:mm:ss\",\n                    m: \"d. MMMM\",\n                    M: \"d. MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"H:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy.\",\n                    Y: \"MMMM yyyy.\"\n                },\n                \"/\": \".\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}