{"version": 3, "sources": ["cultures/kendo.culture.lkt-US.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,YACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,eAAe,cAAc,cAAc,cAAc,aAAa,eAAe,iBAC7FC,WAAY,eAAe,cAAc,cAAc,cAAc,aAAa,eAAe,iBACjGC,YAAa,eAAe,cAAc,cAAc,cAAc,aAAa,eAAe,kBAEtGC,QACIH,OAAQ,gBAAgB,kBAAkB,oBAAoB,cAAc,iBAAiB,qBAAqB,iBAAiB,cAAc,gBAAgB,oBAAoB,cAAc,kBACnMC,WAAY,gBAAgB,kBAAkB,oBAAoB,cAAc,iBAAiB,qBAAqB,iBAAiB,cAAc,gBAAgB,oBAAoB,cAAc,mBAE3MG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,qBACHC,EAAG,gCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.lkt-US.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"lkt-US\"] = {\n        name: \"lkt-US\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"US Dollar\",\n                abbr: \"USD\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"$\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Aŋpétuwakȟaŋ\",\"Aŋpétuwaŋži\",\"<PERSON>ŋ<PERSON><PERSON><PERSON>uŋ<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>ŋ<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>ŋ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\"Ow<PERSON>ŋgyužažapi\"],\n                    namesAbbr: [\"Aŋpétuwakȟaŋ\",\"Aŋpétuwaŋži\",\"Aŋpétunuŋpa\",\"Aŋpétuyamni\",\"Aŋpétutopa\",\"Aŋpétuzaptaŋ\",\"Owáŋgyužažapi\"],\n                    namesShort: [\"Aŋpétuwakȟaŋ\",\"Aŋpétuwaŋži\",\"Aŋpétunuŋpa\",\"Aŋpétuyamni\",\"Aŋpétutopa\",\"Aŋpétuzaptaŋ\",\"Owáŋgyužažapi\"]\n                },\n                months: {\n                    names: [\"Wiótheȟika Wí\",\"Thiyóȟeyuŋka Wí\",\"Ištáwičhayazaŋ Wí\",\"Pȟežítȟo Wí\",\"Čhaŋwápetȟo Wí\",\"Wípazukȟa-wašté Wí\",\"Čhaŋpȟásapa Wí\",\"Wasútȟuŋ Wí\",\"Čhaŋwápeǧi Wí\",\"Čhaŋwápe-kasná Wí\",\"Waníyetu Wí\",\"Tȟahékapšuŋ Wí\"],\n                    namesAbbr: [\"Wiótheȟika Wí\",\"Thiyóȟeyuŋka Wí\",\"Ištáwičhayazaŋ Wí\",\"Pȟežítȟo Wí\",\"Čhaŋwápetȟo Wí\",\"Wípazukȟa-wašté Wí\",\"Čhaŋpȟásapa Wí\",\"Wasútȟuŋ Wí\",\"Čhaŋwápeǧi Wí\",\"Čhaŋwápe-kasná Wí\",\"Waníyetu Wí\",\"Tȟahékapšuŋ Wí\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"M/d/yyyy\",\n                    D: \"dddd, MMMM d, yyyy\",\n                    F: \"dddd, MMMM d, yyyy h:mm:ss tt\",\n                    g: \"M/d/yyyy h:mm tt\",\n                    G: \"M/d/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}