{"version": 3, "sources": ["cultures/kendo.culture.prs-AF.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,UACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,WAAW,YAAY,UAAU,OAAO,QAClEC,WAAY,SAAS,SAAS,WAAW,YAAY,UAAU,OAAO,QACtEC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,MAAM,MAAM,OAAO,QAAQ,MAAM,QAAQ,QAAQ,OAAO,MAAM,MAAM,MAAM,OAClFC,WAAY,MAAM,MAAM,OAAO,QAAQ,MAAM,QAAQ,QAAQ,OAAO,MAAM,MAAM,MAAM,QAE1FG,IAAK,MAAM,MAAM,OACjBC,IAAK,MAAM,MAAM,OACjBC,UACIC,EAAG,WACHC,EAAG,oBACHC,EAAG,+BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.prs-AF.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"prs-AF\"] = {\n        name: \"prs-AF\",\n        numberFormat: {\n            pattern: [\"n-\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Afghani\",\n                abbr: \"AFN\",\n                pattern: [\"$n-\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"؋\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"یکشنبه\",\"دوشنبه\",\"سه‌ شنبه\",\"چهار شنبه\",\"پنجشنبه\",\"جمعه\",\"شنبه\"],\n                    namesAbbr: [\"یکشنبه\",\"دوشنبه\",\"سه‌ شنبه\",\"چهار شنبه\",\"پنجشنبه\",\"جمعه\",\"شنبه\"],\n                    namesShort: [\"ی\",\"د\",\"س\",\"چ\",\"پ\",\"ج\",\"ش\"]\n                },\n                months: {\n                    names: [\"حمل\",\"ثور\",\"جوزا\",\"سرطان\",\"اسد\",\"سنبله\",\"میزان\",\"عقرب\",\"قوس\",\"جدی\",\"دلو\",\"حوت\"],\n                    namesAbbr: [\"حمل\",\"ثور\",\"جوزا\",\"سرطان\",\"اسد\",\"سنبله\",\"میزان\",\"عقرب\",\"قوس\",\"جدی\",\"دلو\",\"حوت\"]\n                },\n                AM: [\"غ.م\",\"غ.م\",\"غ.م\"],\n                PM: [\"غ.و\",\"غ.و\",\"غ.و\"],\n                patterns: {\n                    d: \"yyyy/M/d\",\n                    D: \"dddd, d MMMM yyyy\",\n                    F: \"dddd, d MMMM yyyy h:mm:ss tt\",\n                    g: \"yyyy/M/d h:mm tt\",\n                    G: \"yyyy/M/d h:mm:ss tt\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 6\n            }\n        }\n    }\n})(this);\n}));"]}