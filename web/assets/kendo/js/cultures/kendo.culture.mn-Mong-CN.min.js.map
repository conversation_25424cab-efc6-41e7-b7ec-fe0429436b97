{"version": 3, "sources": ["cultures/kendo.culture.mn-Mong-CN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,eACXC,KAAM,aACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,gBAAgB,iBAAiB,iBAAiB,kBAAkB,kBAAkB,iBAAiB,qBAC/GC,WAAY,gBAAgB,iBAAiB,iBAAiB,kBAAkB,kBAAkB,iBAAiB,qBACnHC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,kBAAkB,mBAAmB,mBAAmB,mBAAmB,kBAAkB,mBAAmB,kBAAkB,mBAAmB,kBAAkB,kBAAkB,wBAAwB,0BACzNC,WAAY,kBAAkB,mBAAmB,mBAAmB,mBAAmB,kBAAkB,mBAAmB,kBAAkB,mBAAmB,kBAAkB,kBAAkB,wBAAwB,2BAEjOG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,WACHC,EAAG,0BACHC,EAAG,kCACHC,EAAG,gBACHC,EAAG,mBACHC,EAAG,aACHC,EAAG,aACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,UACHC,EAAG,iCACHC,EAAG,cACHC,EAAG,eAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.mn-Mong-CN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"mn-Mong-CN\"] = {\n        name: \"mn-Mong-CN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,0],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,0],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"PRC Renminbi\",\n                abbr: \"CNY\",\n                pattern: [\"$-n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,0],\n                symbol: \"¥\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠡᠳᠦᠷ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠨᠢᠭᠡᠨ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠬᠣᠶᠠᠷ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠭᠤᠷᠪᠠᠨ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠳᠥᠷᠪᠡᠨ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠲᠠᠪᠤᠨ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠵᠢᠷᠭᠤᠭᠠᠨ\"],\n                    namesAbbr: [\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠡᠳᠦᠷ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠨᠢᠭᠡᠨ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠬᠣᠶᠠᠷ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠭᠤᠷᠪᠠᠨ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠳᠥᠷᠪᠡᠨ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠲᠠᠪᠤᠨ\",\"ᠭᠠᠷᠠᠭ ᠤᠨ ᠵᠢᠷᠭᠤᠭᠠᠨ\"],\n                    namesShort: [\"ᠭ᠗\",\"ᠭ᠑\",\"ᠭ᠒\",\"ᠭ᠓\",\"ᠭ᠔\",\"ᠭ᠕\",\"ᠭ᠖\"]\n                },\n                months: {\n                    names: [\"ᠨᠢᠭᠡᠳᠦᠭᠡᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠬᠤᠶ᠋ᠠᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠭᠤᠷᠪᠠᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠲᠦᠷᠪᠡᠳᠦᠭᠡᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠲᠠᠪᠤᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠵᠢᠷᠭᠤᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠲᠤᠯᠤᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠨᠠᠢᠮᠠᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠶᠢᠰᠦᠳᠦᠭᠡᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠠᠷᠪᠠᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠠᠷᠪᠠᠨ ᠨᠢᠭᠡᠳᠦᠭᠡᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠠᠷᠪᠠᠨ ᠬᠤᠶ᠋ᠠᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\"],\n                    namesAbbr: [\"ᠨᠢᠭᠡᠳᠦᠭᠡᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠬᠤᠶ᠋ᠠᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠭᠤᠷᠪᠠᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠲᠦᠷᠪᠡᠳᠦᠭᠡᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠲᠠᠪᠤᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠵᠢᠷᠭᠤᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠲᠤᠯᠤᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠨᠠᠢᠮᠠᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠶᠢᠰᠦᠳᠦᠭᠡᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠠᠷᠪᠠᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠠᠷᠪᠠᠨ ᠨᠢᠭᠡᠳᠦᠭᠡᠷ ᠰᠠᠷ᠎ᠠ\",\"ᠠᠷᠪᠠᠨ ᠬᠤᠶ᠋ᠠᠳᠤᠭᠠᠷ ᠰᠠᠷ᠎ᠠ\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"yyyy/M/d\",\n                    D: \"yyyyᠣᠨ MMMM dᠡᠳᠦᠷ᠂ dddd\",\n                    F: \"yyyyᠣᠨ MMMM dᠡᠳᠦᠷ᠂ dddd H:mm:ss\",\n                    g: \"yyyy/M/d H:mm\",\n                    G: \"yyyy/M/d H:mm:ss\",\n                    m: \"MMMM dᠡᠳᠦᠷ\",\n                    M: \"MMMM dᠡᠳᠦᠷ\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"H:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyyᠣᠨ MMMM\",\n                    Y: \"yyyyᠣᠨ MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}