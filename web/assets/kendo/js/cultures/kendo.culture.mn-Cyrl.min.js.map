{"version": 3, "sources": ["cultures/kendo.culture.mn-Cyrl.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,YACXC,KAAM,UACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,MAAM,QAAQ,SAAS,SAAS,QAAQ,SAAS,SACzDC,WAAY,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAC1CC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,gBAAgB,iBAAiB,kBAAkB,kBAAkB,gBAAgB,kBAAkB,gBAAgB,iBAAiB,eAAe,iBAAiB,sBAAsB,wBACtMC,WAAY,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,WAAW,WAAW,aAEhIG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,gCACHC,EAAG,yCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.mn-Cyrl.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"mn-Cyrl\"] = {\n        name: \"mn-Cyrl\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 0,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"₮\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ням\",\"даваа\",\"мягмар\",\"лхагва\",\"пүрэв\",\"баа<PERSON>ан\",\"бямба\"],\n                    namesAbbr: [\"Ня\",\"Да\",\"Мя\",\"Лх\",\"Пү\",\"Ба\",\"Бя\"],\n                    namesShort: [\"Ня\",\"Да\",\"Мя\",\"Лх\",\"Пү\",\"Ба\",\"Бя\"]\n                },\n                months: {\n                    names: [\"Нэгдүгээр сар\",\"Хоёрдугаар сар\",\"Гуравдугаар сар\",\"Дөрөвдүгээр сар\",\"Тавдугаар сар\",\"Зургадугаар сар\",\"Долдугаар сар\",\"Наймдугаар сар\",\"Есдүгээр сар\",\"Аравдугаар сар\",\"Арван нэгдүгээр сар\",\"Арван хоёрдугаар сар\"],\n                    namesAbbr: [\"1-р сар\",\"2-р сар\",\"3-р сар\",\"4-р сар\",\"5-р сар\",\"6-р сар\",\"7-р сар\",\"8-р сар\",\"9-р сар\",\"10-р сар\",\"11-р сар\",\"12-р сар\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"yyyy-MM-dd\",\n                    D: \"dddd, yyyy 'оны' MM 'сарын' d\",\n                    F: \"dddd, yyyy 'оны' MM 'сарын' d HH:mm:ss\",\n                    g: \"yyyy-MM-dd HH:mm\",\n                    G: \"yyyy-MM-dd HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}