{"version": 3, "sources": ["cultures/kendo.culture.dua-CM.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,4BACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,SAGhBG,WACIC,UACIC,MACIC,OAAQ,MAAM,QAAQ,QAAQ,UAAU,QAAQ,SAAS,WACzDC,WAAY,KAAK,OAAO,MAAM,MAAM,MAAM,MAAM,OAChDC,YAAa,KAAK,OAAO,MAAM,MAAM,MAAM,MAAM,QAErDC,QACIH,OAAQ,UAAU,SAAS,OAAO,SAAS,WAAW,YAAY,eAAe,WAAW,UAAU,UAAU,SAAS,WACzHC,WAAY,KAAK,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,QAEnFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,mBACHC,EAAG,4BACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.dua-CM.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"dua-CM\"] = {\n        name: \"dua-CM\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Central African CFA Franc\",\n                abbr: \"XAF\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 0,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"FCFA\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"éti\",\"mɔ́sú\",\"kwasú\",\"mukɔ́sú\",\"ŋgis<PERSON>\",\"ɗónɛsú\",\"esaɓasú\"],\n                    namesAbbr: [\"ét\",\"mɔ́s\",\"kwa\",\"muk\",\"ŋgi\",\"ɗón\",\"esa\"],\n                    namesShort: [\"ét\",\"mɔ́s\",\"kwa\",\"muk\",\"ŋgi\",\"ɗón\",\"esa\"]\n                },\n                months: {\n                    names: [\"dimɔ́di\",\"ŋgɔndɛ\",\"sɔŋɛ\",\"diɓáɓá\",\"emiasele\",\"esɔpɛsɔpɛ\",\"madiɓɛ́díɓɛ́\",\"diŋgindi\",\"nyɛtɛki\",\"mayésɛ́\",\"tiníní\",\"eláŋgɛ́\"],\n                    namesAbbr: [\"di\",\"ŋgɔn\",\"sɔŋ\",\"diɓ\",\"emi\",\"esɔ\",\"mad\",\"diŋ\",\"nyɛt\",\"may\",\"tin\",\"elá\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"dddd d MMMM yyyy\",\n                    F: \"dddd d MMMM yyyy HH:mm:ss\",\n                    g: \"d/M/yyyy HH:mm\",\n                    G: \"d/M/yyyy HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}