{"version": 3, "sources": ["cultures/kendo.culture.mk.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,aAAa,UAAU,QAAQ,WAAW,QAAQ,UACnEC,WAAY,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,QACrDC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,UAAU,WAAW,OAAO,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,WAAW,UAAU,YAC1GC,WAAY,OAAO,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,QAAQ,OAAO,QAAQ,SAE9FG,IAAK,aAAa,aAAa,cAC/BC,IAAK,WAAW,WAAW,YAC3BC,UACIC,EAAG,YACHC,EAAG,qBACHC,EAAG,8BACHC,EAAG,kBACHC,EAAG,qBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,iBACHC,EAAG,kBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.mk.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"mk\"] = {\n        name: \"mk\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"ден\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"недела\",\"понеделник\",\"вторник\",\"среда\",\"четврток\",\"петок\",\"сабота\"],\n                    namesAbbr: [\"нед.\",\"пон.\",\"вт.\",\"сре.\",\"чет.\",\"пет.\",\"саб.\"],\n                    namesShort: [\"не\",\"по\",\"вт\",\"ср\",\"че\",\"пе\",\"са\"]\n                },\n                months: {\n                    names: [\"јануари\",\"февруари\",\"март\",\"април\",\"мај\",\"јуни\",\"јули\",\"август\",\"септември\",\"октомври\",\"ноември\",\"декември\"],\n                    namesAbbr: [\"јан.\",\"фев.\",\"мар.\",\"апр.\",\"мај\",\"јун.\",\"јул.\",\"авг.\",\"септ.\",\"окт.\",\"ноем.\",\"дек.\"]\n                },\n                AM: [\"претпладне\",\"претпладне\",\"ПРЕТПЛАДНЕ\"],\n                PM: [\"попладне\",\"попладне\",\"ПОПЛАДНЕ\"],\n                patterns: {\n                    d: \"dd.M.yyyy\",\n                    D: \"dddd, dd MMMM yyyy\",\n                    F: \"dddd, dd MMMM yyyy HH:mm:ss\",\n                    g: \"dd.M.yyyy HH:mm\",\n                    G: \"dd.M.yyyy HH:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy 'г'.\",\n                    Y: \"MMMM yyyy 'г'.\"\n                },\n                \"/\": \".\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}