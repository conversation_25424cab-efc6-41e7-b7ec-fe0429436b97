{"version": 3, "sources": ["cultures/kendo.culture.fa.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,SAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,UAAU,WAAW,UAAU,OAAO,QAChEC,WAAY,SAAS,SAAS,UAAU,WAAW,UAAU,OAAO,QACpEC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,UAAU,WAAW,QAAQ,MAAM,QAAQ,SAAS,MAAM,OAAO,MAAM,KAAK,OAAO,SAC3FC,WAAY,UAAU,WAAW,QAAQ,MAAM,QAAQ,SAAS,MAAM,OAAO,MAAM,KAAK,OAAO,UAEnGG,IAAK,MAAM,MAAM,OACjBC,IAAK,MAAM,MAAM,OACjBC,UACIC,EAAG,aACHC,EAAG,oBACHC,EAAG,gCACHC,EAAG,sBACHC,EAAG,yBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,WACHC,EAAG,cACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.fa.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"fa\"] = {\n        name: \"fa\",\n        numberFormat: {\n            pattern: [\"n-\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \"/\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"n- %\",\"n %\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \"/\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"n-$\",\"n$\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \"/\",\n                groupSize: [3],\n                symbol: \"ريال\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"يكشنبه\",\"دوشنبه\",\"سه شنبه\",\"چهارشنبه\",\"پنجشنبه\",\"جمعه\",\"شنبه\"],\n                    namesAbbr: [\"يكشنبه\",\"دوشنبه\",\"سه شنبه\",\"چهارشنبه\",\"پنجشنبه\",\"جمعه\",\"شنبه\"],\n                    namesShort: [\"ی\",\"د\",\"س\",\"چ\",\"پ\",\"ج\",\"ش\"]\n                },\n                months: {\n                    names: [\"فروردین\",\"اردیبهشت\",\"خرداد\",\"تیر\",\"مرداد\",\"شهریور\",\"مهر\",\"آبان\",\"آذر\",\"دی\",\"بهمن\",\"اسفند\"],\n                    namesAbbr: [\"فروردین\",\"اردیبهشت\",\"خرداد\",\"تیر\",\"مرداد\",\"شهریور\",\"مهر\",\"آبان\",\"آذر\",\"دی\",\"بهمن\",\"اسفند\"]\n                },\n                AM: [\"ق.ظ\",\"ق.ظ\",\"ق.ظ\"],\n                PM: [\"ب.ظ\",\"ب.ظ\",\"ب.ظ\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd, d MMMM yyyy\",\n                    F: \"dddd, d MMMM yyyy hh:mm:ss tt\",\n                    g: \"dd/MM/yyyy hh:mm tt\",\n                    G: \"dd/MM/yyyy hh:mm:ss tt\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"hh:mm tt\",\n                    T: \"hh:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 6\n            }\n        }\n    }\n})(this);\n}));"]}