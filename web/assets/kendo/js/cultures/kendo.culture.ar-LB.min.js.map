{"version": 3, "sources": ["cultures/kendo.culture.ar-LB.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,iBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,UAGhBG,WACIC,UACIC,MACIC,OAAQ,QAAQ,UAAU,WAAW,WAAW,SAAS,SAAS,SAClEC,WAAY,QAAQ,UAAU,WAAW,WAAW,SAAS,SAAS,SACtEC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,eAAe,OAAO,OAAO,QAAQ,OAAO,SAAS,OAAO,KAAK,QAAQ,cAAc,eAAe,eAC9GC,WAAY,eAAe,OAAO,OAAO,QAAQ,OAAO,SAAS,OAAO,KAAK,QAAQ,cAAc,eAAe,gBAEtHG,IAAK,IAAI,IAAI,KACbC,IAAK,IAAI,IAAI,KACbC,UACIC,EAAG,aACHC,EAAG,gBACHC,EAAG,4BACHC,EAAG,sBACHC,EAAG,yBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,WACHC,EAAG,cACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ar-LB.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ar-LB\"] = {\n        name: \"ar-LB\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Lebanese Pound\",\n                abbr: \"LBP\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"ل.ل.‏\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"الأحد\",\"الإثنين\",\"الثلاثاء\",\"الأربعاء\",\"الخميس\",\"الجمعة\",\"السبت\"],\n                    namesAbbr: [\"الأحد\",\"الإثنين\",\"الثلاثاء\",\"الأربعاء\",\"الخميس\",\"الجمعة\",\"السبت\"],\n                    namesShort: [\"ح\",\"ن\",\"ث\",\"ر\",\"خ\",\"ج\",\"س\"]\n                },\n                months: {\n                    names: [\"كانون الثاني\",\"شباط\",\"آذار\",\"نيسان\",\"أيار\",\"حزيران\",\"تموز\",\"آب\",\"أيلول\",\"تشرين الأول\",\"تشرين الثاني\",\"كانون الأول\"],\n                    namesAbbr: [\"كانون الثاني\",\"شباط\",\"آذار\",\"نيسان\",\"أيار\",\"حزيران\",\"تموز\",\"آب\",\"أيلول\",\"تشرين الأول\",\"تشرين الثاني\",\"كانون الأول\"]\n                },\n                AM: [\"ص\",\"ص\",\"ص\"],\n                PM: [\"م\",\"م\",\"م\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dd MMMM, yyyy\",\n                    F: \"dd MMMM, yyyy hh:mm:ss tt\",\n                    g: \"dd/MM/yyyy hh:mm tt\",\n                    G: \"dd/MM/yyyy hh:mm:ss tt\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"hh:mm tt\",\n                    T: \"hh:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}