{"version": 3, "sources": ["cultures/kendo.culture.bn-BD.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,mBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,WAAW,SAAS,cAAc,WAAW,UACvEC,WAAY,OAAO,OAAO,SAAS,OAAO,OAAO,SAAS,QAC1DC,YAAa,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,MAE7CC,QACIH,OAAQ,YAAY,cAAc,QAAQ,SAAS,KAAK,MAAM,QAAQ,QAAQ,aAAa,UAAU,UAAU,YAC/GC,WAAY,QAAQ,UAAU,QAAQ,SAAS,KAAK,MAAM,QAAQ,MAAM,UAAU,SAAS,OAAO,UAEtGG,IAAK,YAAY,YAAY,aAC7BC,IAAK,UAAU,UAAU,WACzBC,UACIC,EAAG,WACHC,EAAG,eACHC,EAAG,wBACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.bn-BD.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"bn-BD\"] = {\n        name: \"bn-BD\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Bangladeshi Taka\",\n                abbr: \"BDT\",\n                pattern: [\"-n$\",\"n$\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"৳\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"রবিবার\",\"সোমবার\",\"মঙ্গলবার\",\"বুধ<PERSON><PERSON><PERSON>\",\"বৃহস্পতিবার\",\"শুক<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"শনিবার\"],\n                    namesAbbr: [\"রবি.\",\"সোম.\",\"মঙ্গল.\",\"বুধ.\",\"বৃহ.\",\"শুক্র.\",\"শনি.\"],\n                    namesShort: [\"র\",\"সো\",\"ম\",\"বু\",\"বৃ\",\"শু\",\"শ\"]\n                },\n                months: {\n                    names: [\"জানুয়ারী\",\"ফেব্রুয়ারী\",\"মার্চ\",\"এপ্রিল\",\"মে\",\"জুন\",\"জুলাই\",\"আগস্ট\",\"সেপ্টেম্বর\",\"অক্টোবর\",\"নভেম্বর\",\"ডিসেম্বর\"],\n                    namesAbbr: [\"জানু.\",\"ফেব্রু.\",\"মার্চ\",\"এপ্রিল\",\"মে\",\"জুন\",\"জুলাই\",\"আগ.\",\"সেপ্টে.\",\"অক্টো.\",\"নভে.\",\"ডিসে.\"]\n                },\n                AM: [\"পুর্বাহ্ন\",\"পুর্বাহ্ন\",\"পুর্বাহ্ন\"],\n                PM: [\"অপরাহ্ন\",\"অপরাহ্ন\",\"অপরাহ্ন\"],\n                patterns: {\n                    d: \"dd-MM-yy\",\n                    D: \"dd MMMM yyyy\",\n                    F: \"dd MMMM yyyy HH.mm.ss\",\n                    g: \"dd-MM-yy HH.mm\",\n                    G: \"dd-MM-yy HH.mm.ss\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH.mm\",\n                    T: \"HH.mm.ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \".\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}