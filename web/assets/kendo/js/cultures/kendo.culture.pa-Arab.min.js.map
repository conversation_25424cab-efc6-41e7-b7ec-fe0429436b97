{"version": 3, "sources": ["cultures/kendo.culture.pa-Arab.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,YACXC,KAAM,UACNC,cACIC,SAAU,OACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,MAAM,OAAO,MAAM,SAAS,OAAO,OAAO,SAClDC,WAAY,MAAM,OAAO,MAAM,SAAS,OAAO,OAAO,SACtDC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,QAAQ,QAAQ,OAAO,QAAQ,MAAM,MAAM,SAAS,OAAO,QAAQ,SAAS,QAAQ,SAC5FC,WAAY,QAAQ,QAAQ,OAAO,QAAQ,MAAM,MAAM,SAAS,OAAO,QAAQ,SAAS,QAAQ,UAEpGG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,oBACHC,EAAG,+BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.pa-Arab.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"pa-Arab\"] = {\n        name: \"pa-Arab\",\n        numberFormat: {\n            pattern: [\"- n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"Rs\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"پير\",\"منگل\",\"بدھ\",\"جمعرات\",\"جمعه\",\"هفته\",\"اتوار\"],\n                    namesAbbr: [\"پير\",\"منگل\",\"بدھ\",\"جمعرات\",\"جمعه\",\"هفته\",\"اتوار\"],\n                    namesShort: [\"پ\",\"م\",\"ب\",\"ج\",\"ج\",\"ه\",\"ا\"]\n                },\n                months: {\n                    names: [\"جنوری\",\"فروری\",\"مارچ\",\"اپریل\",\"مئی\",\"جون\",\"جولائی\",\"اگست\",\"ستمبر\",\"اکتوبر\",\"نومبر\",\"دسمبر\"],\n                    namesAbbr: [\"جنوری\",\"فروری\",\"مارچ\",\"اپریل\",\"مئی\",\"جون\",\"جولائی\",\"اگست\",\"ستمبر\",\"اکتوبر\",\"نومبر\",\"دسمبر\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd-MM-yy\",\n                    D: \"dd MMMM yyyy dddd\",\n                    F: \"dd MMMM yyyy dddd h.mm.ss tt\",\n                    g: \"dd-MM-yy h.mm tt\",\n                    G: \"dd-MM-yy h.mm.ss tt\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h.mm tt\",\n                    T: \"h.mm.ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \".\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}