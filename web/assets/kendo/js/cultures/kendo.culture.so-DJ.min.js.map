{"version": 3, "sources": ["cultures/kendo.culture.so-DJ.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,mBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,OAAO,SAAS,UAAU,SAAS,UAAU,QAAQ,SAC7DC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAChDC,YAAa,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAErDC,QACIH,OAAQ,gBAAgB,eAAe,kBAAkB,eAAe,gBAAgB,eAAe,iBAAiB,kBAAkB,kBAAkB,gBAAgB,wBAAwB,0BACpMC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,sBACHC,EAAG,iCACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.so-DJ.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"so-DJ\"] = {\n        name: \"so-DJ\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Djiboutian Franc\",\n                abbr: \"DJF\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 0,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"Fdj\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Axad\",\"<PERSON>ii<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Arb<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\"],\n                    namesAbbr: [\"Axd\",\"Isn\",\"Tal\",\"Arb\",\"<PERSON>ha\",\"<PERSON>\",\"Sab\"],\n                    namesShort: [\"Axd\",\"Isn\",\"Tal\",\"Arb\",\"<PERSON>ha\",\"<PERSON>\",\"Sab\"]\n                },\n                months: {\n                    names: [\"Bisha Koobaad\",\"Bisha Labaad\",\"Bisha Saddexaad\",\"Bisha Afraad\",\"Bisha Shanaad\",\"Bisha <PERSON>xaad\",\"Bisha Todobaad\",\"Bisha Sideedaad\",\"Bisha Sagaalaad\",\"Bisha Tobnaad\",\"Bisha Kow iyo Tobnaad\",\"Bisha Laba iyo Tobnaad\"],\n                    namesAbbr: [\"Kob\",\"Lab\",\"Sad\",\"Afr\",\"Sha\",\"Lix\",\"Tod\",\"Sid\",\"Sag\",\"Tob\",\"KIT\",\"LIT\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd, MMMM dd, yyyy\",\n                    F: \"dddd, MMMM dd, yyyy h:mm:ss tt\",\n                    g: \"dd/MM/yyyy h:mm tt\",\n                    G: \"dd/MM/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 6\n            }\n        }\n    }\n})(this);\n}));"]}