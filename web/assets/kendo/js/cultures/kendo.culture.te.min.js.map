{"version": 3, "sources": ["cultures/kendo.culture.te.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,MACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,UAAU,WAAW,UAAU,WAAW,YAAY,WACxEC,WAAY,OAAO,OAAO,QAAQ,OAAO,QAAQ,SAAS,QAC1DC,YAAa,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,MAE9CC,QACIH,OAAQ,QAAQ,WAAW,SAAS,UAAU,KAAK,OAAO,OAAO,SAAS,aAAa,WAAW,SAAS,YAC3GC,WAAY,QAAQ,WAAW,SAAS,UAAU,KAAK,OAAO,OAAO,SAAS,aAAa,WAAW,SAAS,aAEnHG,IAAK,YAAY,YAAY,aAC7BC,IAAK,UAAU,UAAU,WACzBC,UACIC,EAAG,WACHC,EAAG,eACHC,EAAG,wBACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.te.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"te\"] = {\n        name: \"te\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"$ -n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ఆదివారం\",\"సోమవారం\",\"మంగళవారం\",\"బుధవారం\",\"గురువారం\",\"శుక్రవారం\",\"శనివారం\"],\n                    namesAbbr: [\"ఆది.\",\"సోమ.\",\"మంగళ.\",\"బుధ.\",\"గురు.\",\"శుక్ర.\",\"శని.\"],\n                    namesShort: [\"ఆ\",\"సో\",\"మం\",\"బు\",\"గు\",\"శు\",\"శ\"]\n                },\n                months: {\n                    names: [\"జనవరి\",\"ఫిబ్రవరి\",\"మార్చి\",\"ఏప్రిల్\",\"మే\",\"జూన్\",\"జూలై\",\"ఆగస్టు\",\"సెప్టెంబర్\",\"అక్టోబర్\",\"నవంబర్\",\"డిసెంబర్\"],\n                    namesAbbr: [\"జనవరి\",\"ఫిబ్రవరి\",\"మార్చి\",\"ఏప్రిల్\",\"మే\",\"జూన్\",\"జూలై\",\"ఆగస్టు\",\"సెప్టెంబర్\",\"అక్టోబర్\",\"నవంబర్\",\"డిసెంబర్\"]\n                },\n                AM: [\"పూర్వాహ్న\",\"పూర్వాహ్న\",\"పూర్వాహ్న\"],\n                PM: [\"అపరాహ్న\",\"అపరాహ్న\",\"అపరాహ్న\"],\n                patterns: {\n                    d: \"dd-MM-yy\",\n                    D: \"dd MMMM yyyy\",\n                    F: \"dd MMMM yyyy HH:mm:ss\",\n                    g: \"dd-MM-yy HH:mm\",\n                    G: \"dd-MM-yy HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}