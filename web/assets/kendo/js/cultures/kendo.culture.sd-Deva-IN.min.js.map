{"version": 3, "sources": ["cultures/kendo.culture.sd-Deva-IN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,eACXC,KAAM,aACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,MACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,MAAM,OAAO,MAAM,KAAK,OAAO,KAAK,QAC5CC,WAAY,MAAM,OAAO,MAAM,KAAK,OAAO,KAAK,QAChDC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,QAAQ,SAAS,OAAO,OAAO,KAAK,MAAM,OAAO,OAAO,UAAU,QAAQ,SAAS,SAC3FC,WAAY,QAAQ,SAAS,OAAO,OAAO,KAAK,MAAM,OAAO,OAAO,UAAU,QAAQ,SAAS,UAEnGG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,aACHC,EAAG,eACHC,EAAG,wBACHC,EAAG,kBACHC,EAAG,sBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.sd-Deva-IN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"sd-Deva-IN\"] = {\n        name: \"sd-Deva-IN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Indian Rupee\",\n                abbr: \"INR\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"आचर\",\"सवमर\",\"ङरव\",\"रब\",\"ख़मयस\",\"जम\",\"छनछर\"],\n                    namesAbbr: [\"आचर\",\"सवमर\",\"ङरव\",\"रब\",\"ख़मयस\",\"जम\",\"छनछर\"],\n                    namesShort: [\"आ\",\"स\",\"ङ\",\"र\",\"ख़\",\"ज\",\"छ\"]\n                },\n                months: {\n                    names: [\"जनवरय\",\"फ़बरवरय\",\"मारच\",\"परयल\",\"मय\",\"जवन\",\"वलाय\",\"आगसट\",\"सयपटमबर\",\"आटवबर\",\"नववमबर\",\"डसमबर\"],\n                    namesAbbr: [\"जनवरय\",\"फ़बरवरय\",\"मारच\",\"परयल\",\"मय\",\"जवन\",\"वलाय\",\"आगसट\",\"सयपटमबर\",\"आटवबर\",\"नववमबर\",\"डसमबर\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dd MMMM yyyy\",\n                    F: \"dd MMMM yyyy HH:mm:ss\",\n                    g: \"dd/MM/yyyy H:mm\",\n                    G: \"dd/MM/yyyy HH:mm:ss\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}