{"version": 3, "sources": ["cultures/kendo.culture.bn-IN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,WAAW,SAAS,cAAc,WAAW,UACvEC,WAAY,OAAO,OAAO,SAAS,OAAO,YAAY,SAAS,QAC/DC,YAAa,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,MAE5CC,QACIH,OAAQ,YAAY,cAAc,QAAQ,SAAS,KAAK,MAAM,QAAQ,QAAQ,aAAa,UAAU,UAAU,YAC/GC,WAAY,QAAQ,UAAU,QAAQ,SAAS,KAAK,MAAM,QAAQ,MAAM,UAAU,SAAS,OAAO,UAEtGG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,eACHC,EAAG,wBACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.bn-IN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"bn-IN\"] = {\n        name: \"bn-IN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Indian Rupee\",\n                abbr: \"INR\",\n                pattern: [\"$ -n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"রবিবার\",\"সোমবার\",\"মঙ্গলবার\",\"বুধব<PERSON><PERSON>\",\"বৃহস্পতিবার\",\"শুক<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"শনিবার\"],\n                    namesAbbr: [\"রবি.\",\"সোম.\",\"মঙ্গল.\",\"বুধ.\",\"বৃহস্পতি.\",\"শুক্র.\",\"শনি.\"],\n                    namesShort: [\"র\",\"স\",\"ম\",\"বু\",\"বৃ\",\"শু\",\"শ\"]\n                },\n                months: {\n                    names: [\"জানুয়ারী\",\"ফেব্রুয়ারী\",\"মার্চ\",\"এপ্রিল\",\"মে\",\"জুন\",\"জুলাই\",\"আগস্ট\",\"সেপ্টেম্বর\",\"অক্টোবর\",\"নভেম্বর\",\"ডিসেম্বর\"],\n                    namesAbbr: [\"জানু.\",\"ফেব্রু.\",\"মার্চ\",\"এপ্রিল\",\"মে\",\"জুন\",\"জুলাই\",\"আগ.\",\"সেপ্টে.\",\"অক্টো.\",\"নভে.\",\"ডিসে.\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd-MM-yy\",\n                    D: \"dd MMMM yyyy\",\n                    F: \"dd MMMM yyyy HH.mm.ss\",\n                    g: \"dd-MM-yy HH.mm\",\n                    G: \"dd-MM-yy HH.mm.ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH.mm\",\n                    T: \"HH.mm.ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \".\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}