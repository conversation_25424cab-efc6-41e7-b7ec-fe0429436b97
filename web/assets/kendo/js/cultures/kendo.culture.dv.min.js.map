{"version": 3, "sources": ["cultures/kendo.culture.dv.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,OAAO,WAAW,OAAO,aAAa,SAAS,YAClEC,WAAY,WAAW,OAAO,WAAW,OAAO,aAAa,SAAS,YACtEC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,WAAW,aAAa,QAAQ,WAAW,OAAO,OAAO,SAAS,WAAW,cAAc,YAAY,YAAY,aAC3HC,WAAY,WAAW,aAAa,QAAQ,WAAW,OAAO,OAAO,SAAS,WAAW,cAAc,YAAY,YAAY,cAEnIG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,oBACHC,EAAG,6BACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.dv.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"dv\"] = {\n        name: \"dv\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"n $-\",\"n $\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"ރ.\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"އާދީއްތަ\",\"ހޯމަ\",\"އަންގާރަ\",\"ބުދަ\",\"ބުރާސްފަތި\",\"ހުކުރު\",\"ހޮނިހިރު\"],\n                    namesAbbr: [\"އާދީއްތަ\",\"ހޯމަ\",\"އަންގާރަ\",\"ބުދަ\",\"ބުރާސްފަތި\",\"ހުކުރު\",\"ހޮނިހިރު\"],\n                    namesShort: [\"އާ\",\"ހޯ\",\"އަ\",\"ބު\",\"ބު\",\"ހު\",\"ހޮ\"]\n                },\n                months: {\n                    names: [\"ޖަނަވަރީ\",\"ފެބްރުއަރީ\",\"މާރޗް\",\"އޭޕްރިލް\",\"މެއި\",\"ޖޫން\",\"ޖުލައި\",\"އޮގަސްޓް\",\"ސެޕްޓެމްބަރ\",\"އޮކްޓޯބަރ\",\"ނޮވެމްބަރ\",\"ޑިސެމްބަރ\"],\n                    namesAbbr: [\"ޖަނަވަރީ\",\"ފެބްރުއަރީ\",\"މާރޗް\",\"އޭޕްރިލް\",\"މެއި\",\"ޖޫން\",\"ޖުލައި\",\"އޮގަސްޓް\",\"ސެޕްޓެމްބަރ\",\"އޮކްޓޯބަރ\",\"ނޮވެމްބަރ\",\"ޑިސެމްބަރ\"]\n                },\n                AM: [\"މކ\",\"މކ\",\"މކ\"],\n                PM: [\"މފ\",\"މފ\",\"މފ\"],\n                patterns: {\n                    d: \"dd/MM/yy\",\n                    D: \"ddd, yyyy MMMM dd\",\n                    F: \"ddd, yyyy MMMM dd HH:mm:ss\",\n                    g: \"dd/MM/yy HH:mm\",\n                    G: \"dd/MM/yy HH:mm:ss\",\n                    m: \"MMMM dd\",\n                    M: \"MMMM dd\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy, MMMM\",\n                    Y: \"yyyy, MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}