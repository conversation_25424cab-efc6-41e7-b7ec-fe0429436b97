{"version": 3, "sources": ["cultures/kendo.culture.ses-ML.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,yBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,WAAW,SAAS,YAAY,SAAS,UACnEC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAChDC,YAAa,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAErDC,QACIH,OAAQ,UAAU,YAAY,QAAQ,SAAS,KAAK,QAAQ,QAAQ,KAAK,YAAY,WAAW,YAAY,aAC5GC,WAAY,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,QAEhFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,mBACHC,EAAG,4BACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ses-ML.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ses-ML\"] = {\n        name: \"ses-ML\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"West African CFA Franc\",\n                abbr: \"XOF\",\n                pattern: [\"-n$\",\"n$\"],\n                decimals: 0,\n                \",\": \" \",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"CFA\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\"],\n                    namesAbbr: [\"Alh\",\"Ati\",\"Ata\",\"Ala\",\"Alm\",\"Alz\",\"Asi\"],\n                    namesShort: [\"Alh\",\"Ati\",\"Ata\",\"<PERSON>a\",\"Alm\",\"Alz\",\"Asi\"]\n                },\n                months: {\n                    names: [\"Žanwiye\",\"<PERSON>ewiriye\",\"<PERSON>i\",\"Awiril\",\"<PERSON>\",\"Žuweŋ\",\"Žuyye\",\"Ut\",\"Se<PERSON>anbur\",\"<PERSON>toobur\",\"Noowanbur\",\"<PERSON>sanbur\"],\n                    names<PERSON>bbr: [\"Žan\",\"<PERSON>e\",\"Mar\",\"Awi\",\"Me\",\"Žuw\",\"Žuy\",\"Ut\",\"Sek\",\"Okt\",\"Noo\",\"Dee\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"dddd d MMMM yyyy\",\n                    F: \"dddd d MMMM yyyy HH:mm:ss\",\n                    g: \"d/M/yyyy HH:mm\",\n                    G: \"d/M/yyyy HH:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}