{"version": 3, "sources": ["cultures/kendo.culture.kok-IN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,QAAQ,QAAQ,SAAS,SAAS,YAAY,UAAU,UAChEC,WAAY,MAAM,OAAO,QAAQ,OAAO,QAAQ,SAAS,QACzDC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,WAAW,aAAa,QAAQ,SAAS,KAAK,MAAM,OAAO,QAAQ,WAAW,UAAU,WAAW,WAC3GC,WAAY,OAAO,SAAS,QAAQ,SAAS,KAAK,MAAM,OAAO,MAAM,UAAU,SAAS,QAAQ,UAEpGG,IAAK,QAAQ,QAAQ,SACrBC,IAAK,QAAQ,QAAQ,SACrBC,UACIC,EAAG,aACHC,EAAG,eACHC,EAAG,wBACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.kok-IN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"kok-IN\"] = {\n        name: \"kok-IN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Indian Rupee\",\n                abbr: \"INR\",\n                pattern: [\"$ -n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"आयतार\",\"सोमार\",\"मंगळार\",\"बुधवार\",\"बिरेस्तार\",\"सुक्रार\",\"शेनवार\"],\n                    namesAbbr: [\"आय.\",\"सोम.\",\"मंगळ.\",\"बुध.\",\"बिरे.\",\"सुक्र.\",\"शेन.\"],\n                    namesShort: [\"आ\",\"स\",\"म\",\"ब\",\"ब\",\"स\",\"श\"]\n                },\n                months: {\n                    names: [\"जानेवारी\",\"फेब्रुवारी\",\"मार्च\",\"एप्रिल\",\"मे\",\"जून\",\"जुलै\",\"ऑगस्ट\",\"सप्टेंबर\",\"ऑक्टोबर\",\"नोवेम्बर\",\"डिसेंबर\"],\n                    namesAbbr: [\"जाने\",\"फेब्रु\",\"मार्च\",\"एप्रिल\",\"मे\",\"जून\",\"जुलै\",\"ऑग.\",\"सप्टें.\",\"ऑक्टो.\",\"नोवे.\",\"डिसें\"]\n                },\n                AM: [\"म.पू.\",\"म.पू.\",\"म.पू.\"],\n                PM: [\"म.नं.\",\"म.नं.\",\"म.नं.\"],\n                patterns: {\n                    d: \"dd-MM-yyyy\",\n                    D: \"dd MMMM yyyy\",\n                    F: \"dd MMMM yyyy HH:mm:ss\",\n                    g: \"dd-MM-yyyy HH:mm\",\n                    G: \"dd-MM-yyyy HH:mm:ss\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}