{"version": 3, "sources": ["cultures/kendo.culture.de-AT.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,OACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,SAAS,WAAW,WAAW,aAAa,UAAU,WACxEC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAChDC,YAAa,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAErDC,QACIH,OAAQ,SAAS,UAAU,OAAO,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YACxGC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,QAAQ,QAAQ,SACrBC,IAAK,SAAS,SAAS,UACvBC,UACIC,EAAG,aACHC,EAAG,qBACHC,EAAG,8BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.de-AT.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"de-AT\"] = {\n        name: \"de-AT\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Euro\",\n                abbr: \"EUR\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"€\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Sonntag\",\"Montag\",\"Dienstag\",\"Mittwoch\",\"Donnerstag\",\"Freitag\",\"Samstag\"],\n                    namesAbbr: [\"So.\",\"<PERSON>.\",\"Di.\",\"Mi.\",\"Do.\",\"Fr.\",\"Sa.\"],\n                    namesShort: [\"So.\",\"Mo.\",\"Di.\",\"Mi.\",\"Do.\",\"Fr.\",\"Sa.\"]\n                },\n                months: {\n                    names: [\"J<PERSON><PERSON>\",\"Februar\",\"M<PERSON>rz\",\"April\",\"<PERSON>\",\"Juni\",\"Juli\",\"August\",\"September\",\"Oktober\",\"November\",\"Dezember\"],\n                    namesAbbr: [\"J<PERSON>n\",\"Feb\",\"M<PERSON>r\",\"Apr\",\"<PERSON>\",\"Jun\",\"Jul\",\"Aug\",\"Sep\",\"Okt\",\"Nov\",\"Dez\"]\n                },\n                AM: [\"vorm.\",\"vorm.\",\"VORM.\"],\n                PM: [\"nachm.\",\"nachm.\",\"NACHM.\"],\n                patterns: {\n                    d: \"dd.MM.yyyy\",\n                    D: \"dddd, d. MMMM yyyy\",\n                    F: \"dddd, d. MMMM yyyy HH:mm:ss\",\n                    g: \"dd.MM.yyyy HH:mm\",\n                    G: \"dd.MM.yyyy HH:mm:ss\",\n                    m: \"d. MMMM\",\n                    M: \"d. MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \".\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}