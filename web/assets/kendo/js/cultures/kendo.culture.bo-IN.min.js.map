{"version": 3, "sources": ["cultures/kendo.culture.bo-IN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,YAAY,eAAe,aAAa,cAAc,aAAa,eACvFC,WAAY,QAAQ,QAAQ,WAAW,SAAS,UAAU,SAAS,WACnEC,YAAa,QAAQ,QAAQ,WAAW,SAAS,UAAU,SAAS,YAExEC,QACIH,OAAQ,cAAc,eAAe,eAAe,cAAc,aAAa,eAAe,eAAe,gBAAgB,cAAc,cAAc,mBAAmB,oBAC5KC,WAAY,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,QAAQ,UAE/FG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,0BACHC,EAAG,qCACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,eACHC,EAAG,eACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.bo-IN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"bo-IN\"] = {\n        name: \"bo-IN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Indian Rupee\",\n                abbr: \"INR\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"གཟའ་ཉི་མ་\",\"གཟའ་ཟླ་བ་\",\"གཟའ་མིག་དམར་\",\"གཟའ་ལྷག་པ་\",\"གཟའ་ཕུར་བུ་\",\"གཟའ་པ་སངས་\",\"གཟའ་སྤེན་པ་\"],\n                    namesAbbr: [\"ཉི་མ་\",\"ཟླ་བ་\",\"མིག་དམར་\",\"ལྷག་པ་\",\"ཕུར་བུ་\",\"པ་སངས་\",\"སྤེན་པ་\"],\n                    namesShort: [\"ཉི་མ་\",\"ཟླ་བ་\",\"མིག་དམར་\",\"ལྷག་པ་\",\"ཕུར་བུ་\",\"པ་སངས་\",\"སྤེན་པ་\"]\n                },\n                months: {\n                    names: [\"ཟླ་བ་དང་པོ་\",\"ཟླ་བ་གཉིས་པ་\",\"ཟླ་བ་གསུམ་པ་\",\"ཟླ་བ་བཞི་པ་\",\"ཟླ་བ་ལྔ་པ་\",\"ཟླ་བ་དྲུག་པ་\",\"ཟླ་བ་བདུན་པ་\",\"ཟླ་བ་བརྒྱད་པ་\",\"ཟླ་བ་དགུ་པ་\",\"ཟླ་བ་བཅུ་པ་\",\"ཟླ་བ་བཅུ་གཅིག་པ་\",\"ཟླ་བ་བཅུ་གཉིས་པ་\"],\n                    namesAbbr: [\"ཟླ་༡\",\"ཟླ་༢\",\"ཟླ་༣\",\"ཟླ་༤\",\"ཟླ་༥\",\"ཟླ་༦\",\"ཟླ་༧\",\"ཟླ་༨\",\"ཟླ་༩\",\"ཟླ་༡༠\",\"ཟླ་༡༡\",\"ཟླ་༡༢\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"yyyy-MM-dd\",\n                    D: \"yyyy MMMMའི་ཚེས་d, dddd\",\n                    F: \"yyyy MMMMའི་ཚེས་d, dddd h:mm:ss tt\",\n                    g: \"yyyy-MM-dd h:mm tt\",\n                    G: \"yyyy-MM-dd h:mm:ss tt\",\n                    m: \"MMMMའི་ཚེས་d\",\n                    M: \"MMMMའི་ཚེས་d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}