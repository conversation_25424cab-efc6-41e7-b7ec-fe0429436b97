{"version": 3, "sources": ["cultures/kendo.culture.nnh-CM.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,4BACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,SAGhBG,WACIC,UACIC,MACIC,OAAQ,gBAAgB,aAAa,sBAAsB,iBAAiB,0BAA0B,kBAAkB,cACxHC,WAAY,gBAAgB,aAAa,sBAAsB,iBAAiB,0BAA0B,kBAAkB,cAC5HC,YAAa,gBAAgB,aAAa,sBAAsB,iBAAiB,0BAA0B,kBAAkB,eAEjIC,QACIH,OAAQ,mBAAmB,gBAAgB,iBAAiB,UAAU,gBAAgB,cAAc,wBAAwB,YAAY,kBAAkB,mBAAmB,cAAc,WAC3LC,WAAY,mBAAmB,gBAAgB,iBAAiB,UAAU,gBAAgB,cAAc,wBAAwB,YAAY,kBAAkB,mBAAmB,cAAc,YAEnMG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,mCACHC,EAAG,4CACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.nnh-CM.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"nnh-CM\"] = {\n        name: \"nnh-CM\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Central African CFA Franc\",\n                abbr: \"XAF\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 0,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"FCFA\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"lyɛʼɛ́ sẅíŋtè\",\"mvfò lyɛ̌ʼ\",\"mbɔ́ɔntè mvfò lyɛ̌ʼ\",\"tsètsɛ̀ɛ lyɛ̌ʼ\",\"mbɔ́ɔntè tsetsɛ̀ɛ lyɛ̌ʼ\",\"mvfò màga lyɛ̌ʼ\",\"màga lyɛ̌ʼ\"],\n                    namesAbbr: [\"lyɛʼɛ́ sẅíŋtè\",\"mvfò lyɛ̌ʼ\",\"mbɔ́ɔntè mvfò lyɛ̌ʼ\",\"tsètsɛ̀ɛ lyɛ̌ʼ\",\"mbɔ́ɔntè tsetsɛ̀ɛ lyɛ̌ʼ\",\"mvfò màga lyɛ̌ʼ\",\"màga lyɛ̌ʼ\"],\n                    namesShort: [\"lyɛʼɛ́ sẅíŋtè\",\"mvfò lyɛ̌ʼ\",\"mbɔ́ɔntè mvfò lyɛ̌ʼ\",\"tsètsɛ̀ɛ lyɛ̌ʼ\",\"mbɔ́ɔntè tsetsɛ̀ɛ lyɛ̌ʼ\",\"mvfò màga lyɛ̌ʼ\",\"màga lyɛ̌ʼ\"]\n                },\n                months: {\n                    names: [\"saŋ tsetsɛ̀ɛ lùm\",\"saŋ kàg ngwóŋ\",\"saŋ lepyè shúm\",\"saŋ cÿó\",\"saŋ tsɛ̀ɛ cÿó\",\"saŋ njÿoláʼ\",\"saŋ tyɛ̀b tyɛ̀b mbʉ̀ŋ\",\"saŋ mbʉ̀ŋ\",\"saŋ ngwɔ̀ʼ mbÿɛ\",\"saŋ tàŋa tsetsáʼ\",\"saŋ mejwoŋó\",\"saŋ lùm\"],\n                    namesAbbr: [\"saŋ tsetsɛ̀ɛ lùm\",\"saŋ kàg ngwóŋ\",\"saŋ lepyè shúm\",\"saŋ cÿó\",\"saŋ tsɛ̀ɛ cÿó\",\"saŋ njÿoláʼ\",\"saŋ tyɛ̀b tyɛ̀b mbʉ̀ŋ\",\"saŋ mbʉ̀ŋ\",\"saŋ ngwɔ̀ʼ mbÿɛ\",\"saŋ tàŋa tsetsáʼ\",\"saŋ mejwoŋó\",\"saŋ lùm\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd , 'lyɛ'̌ʼ d 'na' MMMM, yyyy\",\n                    F: \"dddd , 'lyɛ'̌ʼ d 'na' MMMM, yyyy HH:mm:ss\",\n                    g: \"dd/MM/yyyy HH:mm\",\n                    G: \"dd/MM/yyyy HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}