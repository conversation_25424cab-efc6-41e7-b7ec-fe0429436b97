{"version": 3, "sources": ["cultures/kendo.culture.sah-RU.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,gBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,cAAc,eAAe,UAAU,UAAU,WAAW,WAC7EC,WAAY,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAC1CC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,YAAY,UAAU,cAAc,aAAa,UAAU,UAAU,SAAS,eAAe,cAAc,WAAW,WAAW,YACzIC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,gCACHC,EAAG,wCACHC,EAAG,kBACHC,EAAG,qBACHC,EAAG,cACHC,EAAG,cACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,UACHC,EAAG,iCACHC,EAAG,iBACHC,EAAG,kBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.sah-RU.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"sah-RU\"] = {\n        name: \"sah-RU\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Russian Ruble\",\n                abbr: \"RUB\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"₽\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Өрөбүл\",\"энидиэнньик\",\"Оптуорунньук\",\"Сэрэдээ\",\"Чэппиэр\",\"Бээтин<PERSON>э\",\"Субуота\"],\n                    namesAbbr: [\"Өр\",\"Бн\",\"Оп\",\"Ср\",\"Чп\",\"Бт\",\"Сб\"],\n                    namesShort: [\"Өр\",\"Бн\",\"Оп\",\"Ср\",\"Чп\",\"Бт\",\"Сб\"]\n                },\n                months: {\n                    names: [\"Тохсунньу\",\"Олунньу\",\"Кулун тутар\",\"Муус устар\",\"Ыам ыйа\",\"Бэс ыйа\",\"От ыйа\",\"Атырдьах ыйа\",\"Балаҕан ыйа\",\"Алтынньы\",\"Сэтинньи\",\"Ахсынньы\"],\n                    namesAbbr: [\"Тхс\",\"Олн\",\"Клн\",\"Мсу\",\"Ыам\",\"Бэс\",\"Оты\",\"Атр\",\"Блҕ\",\"Алт\",\"Сэт\",\"Ахс\"]\n                },\n                AM: [\"КИ\",\"ки\",\"КИ\"],\n                PM: [\"КК\",\"кк\",\"КК\"],\n                patterns: {\n                    d: \"dd.MM.yyyy\",\n                    D: \"dddd, yyyy 'с.' MMMM d 'күнэ'\",\n                    F: \"dddd, yyyy 'с.' MMMM d 'күнэ' H:mm:ss\",\n                    g: \"dd.MM.yyyy H:mm\",\n                    G: \"dd.MM.yyyy H:mm:ss\",\n                    m: \"MMMM d күнэ\",\n                    M: \"MMMM d күнэ\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"H:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy 'с.' MMMM\",\n                    Y: \"yyyy 'с.' MMMM\"\n                },\n                \"/\": \".\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}