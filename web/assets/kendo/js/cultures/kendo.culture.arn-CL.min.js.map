{"version": 3, "sources": ["cultures/kendo.culture.arn-CL.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,WAAW,YAAY,YAAY,aAAa,YAAY,cAChFC,WAAY,OAAO,MAAM,OAAO,OAAO,QAAQ,OAAO,SACtDC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,gBAAgB,MAAM,OAAO,OAAO,QAAQ,OAAO,QAAQ,QAAQ,OAAO,QAAQ,aAAa,aACvGC,WAAY,gBAAgB,MAAM,OAAO,OAAO,QAAQ,OAAO,QAAQ,QAAQ,OAAO,QAAQ,aAAa,cAE/GG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,aACHC,EAAG,+BACHC,EAAG,uCACHC,EAAG,kBACHC,EAAG,qBACHC,EAAG,cACHC,EAAG,cACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,UACHC,EAAG,iCACHC,EAAG,iBACHC,EAAG,kBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.arn-CL.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"arn-CL\"] = {\n        name: \"arn-CL\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Chilean Peso\",\n                abbr: \"CLP\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"$\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"<PERSON><PERSON><PERSON> An<PERSON>\",\"<PERSON><PERSON> An<PERSON>\",\"<PERSON><PERSON> An<PERSON>\",\"<PERSON><PERSON> An<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"],\n                    namesAbbr: [\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"],\n                    namesShort: [\"kñ\",\"ep\",\"kl\",\"me\",\"ke\",\"ca\",\"re\"]\n                },\n                months: {\n                    names: [\"Kiñe <PERSON>antu\",\"Epu\",\"<PERSON>la\",\"Meli\",\"Kechu\",\"<PERSON>ayu\",\"<PERSON>le\",\"<PERSON>urha\",\"<PERSON>ya\",\"<PERSON>hi\",\"<PERSON>hi Kiñe\",\"<PERSON>hi <PERSON>pu\"],\n                    names<PERSON>bbr: [\"<PERSON>ñe <PERSON>antu\",\"Epu\",\"Kila\",\"Meli\",\"Kechu\",\"Cayu\",\"Regle\",\"Purha\",\"Aiya\",\"Marhi\",\"Marhi Kiñe\",\"Marhi Epu\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"dd-MM-yyyy\",\n                    D: \"dddd, dd' de 'MMMM' de 'yyyy\",\n                    F: \"dddd, dd' de 'MMMM' de 'yyyy H:mm:ss\",\n                    g: \"dd-MM-yyyy H:mm\",\n                    G: \"dd-MM-yyyy H:mm:ss\",\n                    m: \"d 'de' MMMM\",\n                    M: \"d 'de' MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"H:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM' de 'yyyy\",\n                    Y: \"MMMM' de 'yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}