{"version": 3, "sources": ["cultures/kendo.culture.kk-KZ.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,oBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,WAAW,WAAW,WAAW,WAAW,OAAO,SACtEC,WAAY,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAC1CC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,SAAS,QAAQ,SAAS,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,WAAW,QAAQ,SAAS,aACvGC,WAAY,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,SAE7FG,IAAK,QAAQ,QAAQ,SACrBC,IAAK,cAAc,cAAc,eACjCC,UACIC,EAAG,aACHC,EAAG,yBACHC,EAAG,kCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,iBACHC,EAAG,kBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.kk-KZ.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"kk-KZ\"] = {\n        name: \"kk-KZ\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Kazakhstani Tenge\",\n                abbr: \"KZT\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"₸\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"жексенбі\",\"дүйсенбі\",\"сейсенбі\",\"сәрсенбі\",\"бейсенб<PERSON>\",\"жұма\",\"сенбі\"],\n                    namesAbbr: [\"Жс\",\"Дс\",\"Сс\",\"Ср\",\"Бс\",\"Жм\",\"Сб\"],\n                    namesShort: [\"Жс\",\"Дс\",\"Сс\",\"Ср\",\"Бс\",\"Жм\",\"Сб\"]\n                },\n                months: {\n                    names: [\"Қаңтар\",\"Ақпан\",\"Наурыз\",\"Сәуір\",\"Мамыр\",\"Маусым\",\"Шілде\",\"Тамыз\",\"Қыркүйек\",\"Қазан\",\"Қараша\",\"Желтоқсан\"],\n                    namesAbbr: [\"Қаң.\",\"Ақп.\",\"Нау.\",\"Сәу.\",\"Мам.\",\"Мау.\",\"Шіл.\",\"Там.\",\"Қыр.\",\"Қаз.\",\"Қар.\",\"Жел.\"]\n                },\n                AM: [\"таңғы\",\"таңғы\",\"ТАҢҒЫ\"],\n                PM: [\"түскі/кешкі\",\"түскі/кешкі\",\"ТҮСКІ/КЕШКІ\"],\n                patterns: {\n                    d: \"dd.MM.yyyy\",\n                    D: \"yyyy 'ж'. d MMMM, dddd\",\n                    F: \"yyyy 'ж'. d MMMM, dddd HH:mm:ss\",\n                    g: \"dd.MM.yyyy HH:mm\",\n                    G: \"dd.MM.yyyy HH:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy 'ж'. MMMM\",\n                    Y: \"yyyy 'ж'. MMMM\"\n                },\n                \"/\": \".\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}