/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.scheduler.monthview.min",["kendo.scheduler.view.min"],t)}(function(){return function(t){function e(t,e){return t.slice(e).concat(t.slice(0,e))}function n(t,e){for(var n=e.firstDay,o=new Date(t.getFullYear(),t.getMonth(),0,t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds());o.getDay()!=n;)i.date.setTime(o,-1*u);return o}function o(t,e,n){var o,i=e,r=n;return o=t,o>=i&&o<=r}var i=window.kendo,r=i.ui,s=r.SchedulerView,a=".kendoMonthView",l=t.extend,d=i.date.getDate,u=i.date.MS_PER_DAY,c=6,h=7,f="k-event-inverse",_=i.template('<span class="k-link k-nav-day">#:kendo.toString(date, "dd")#</span>'),p='<div role="gridcell" aria-selected="false" data-#=ns#uid="#=uid#"#if (resources[0]) { #style="background-color:#=resources[0].color #; border-color: #=resources[0].color#"class="k-event"#} else {#class="k-event"#}#><span class="k-event-actions"># if(data.tail || data.middle) {#<span class="k-icon k-i-arrow-60-left"></span>#}## if(data.isException()) {#<span class="k-icon k-i-non-recurrence"></span># } else if(data.isRecurring()) {#<span class="k-icon k-i-reload"></span>#}#</span>{0}<span class="k-event-actions">#if (showDelete) {#<a href="\\#" class="k-link k-event-delete" title="${data.messages.destroy}" aria-label="${data.messages.destroy}"><span class="k-icon k-i-close"></span></a>#}## if(data.head || data.middle) {#<span class="k-icon k-i-arrow-60-right"></span>#}#</span># if(resizable && !data.tail && !data.middle) {#<span class="k-resize-handle k-resize-w"></span>#}## if(resizable && !data.head && !data.middle) {#<span class="k-resize-handle k-resize-e"></span>#}#</div>',v=i.template('<div title="#=title.replace(/"/g,"&\\#34;")#"><div class="k-event-template">#:title#</div></div>'),g=i.template('<div style="width:#=width#px;left:#=left#px;top:#=top#px" class="k-more-events k-button"><span>...</span></div>'),m=i.Class.extend({init:function(t){this._view=t},_verticalRowCountForLevel:function(t){var e=this._view;return e._rowCountForLevel(t)},_horizontalGroupCountForLevel:function(t){var e=this._view;return e._columnCountForLevel(t)},_getCalendarRowsLength:function(t,e){return e/t},_createRows:function(t,e,n,o){var i,r=this._view,s=h,a=r._isVerticallyGrouped(),l="";for(i=0;i<n;i++)l+=r._createRow(t,e,s,a?o:i);return l},_adjustStartDate:function(t){return i.date.addDays(t,h)},_getContent:function(t,e,n){return t({date:e,resources:n})},_getTimeSlotByPosition:function(t,e,n){var o=this._view.groups[n];return o.daySlotByPosition(t,e)},_nextSlotStartDate:function(t){return i.date.nextDay(t)},_createRowsLayout:function(t,e,n){var o=this._view;return o._createRowsLayout(t,e,n)},_createVerticalColumnsLayout:function(t,e,n,o){return o},_createColumnsLayout:function(t,e,n){var o=this._view;return o._createColumnsLayout(t,e,n)},_verticalGroupCount:function(t){var e=this._view;return e._rowCountForLevel(t)},_horizontalGroupCount:function(t){var e=this._view;return e._columnCountForLevel(t)/e._columnOffsetForResource(t)},_positionEvent:function(t,e,n,o,i,r,s){var a,l=this._view,d=l._isMobile();o>1&&(0===s?r=n.end.endDate():s==o-1?i=n.start.startDate():(i=n.start.startDate(),r=n.end.endDate())),a=t.clone({start:i,end:r,head:n.head,tail:n.tail}),d?l._positionMobileEvent(n,l._createEventElement(a),e):l._positionEvent(n,l._createEventElement(a),e)},_addDaySlotCollections:function(t,e,n){var o,r,s,a,l,d,u,f,_,p,v,g=this._view,m=h,y=c;for(o=0;o<t;o++)for(r=0,s=0,g._isVerticallyGrouped()&&(s=o),a=s*y;a<(s+1)*y;a++)for(l=g.groups[o],d=l.addDaySlotCollection(i.date.addDays(n,r),i.date.addDays(n,r+m)),u=e[a],f=u.children,_=0,u.setAttribute("role","row"),g._isVerticallyGrouped()||(_=o),p=_*m;p<(_+1)*m;p++)v=f[p],g.addDaySlot(d,v,n,r),r++},_changePeriodGroupIndex:function(t){var e=this._view;return t?e.groups.length-1:0},_createResizeHint:function(t){var e=this._view,n=t.startSlot().offsetLeft,o=t.start.offsetTop,i=t.innerWidth(),r=t.start.clientHeight-2,a=s.fn._createResizeHint.call(e,n,o,i,r);e._appendResizeHint(a)},_createMoveHint:function(t,e){var n=this._view,o=t.startSlot(),i=t.endSlot(),r=n._createEventElement(e.clone({head:t.head,tail:t.tail}));r.css({left:o.offsetLeft+2,top:o.offsetTop+o.firstChildHeight,height:n.options.eventHeight,width:t.innerWidth()-(o.index!==i.index?5:4)}),r.addClass("k-event-drag-hint"),e.inverseColor&&r.addClass(f),n._appendMoveHint(r)}}),y=i.Class.extend({init:function(t){this._view=t},_verticalRowCountForLevel:function(){return 1},_horizontalGroupCountForLevel:function(t){var e=this._view;return e._columnCountForLevel(t+1)/h},_createRows:function(t,e,n){var o,r,s=this._view,a=h,l=s._isVerticallyGrouped(),d="",u=0;if(l){for(o=new Date(t),r=s._groupCount(),u;u<c;u++)d+=s._createRow(o,e,r,u),o=i.date.addDays(o,a);t=i.date.nextDay(t)}else{for(u;u<a;u++)d+=s._createRow(t,e,n,u),t=i.date.nextDay(t);t=i.date.addDays(t,a)}return d},_adjustStartDate:function(t,e){var n=this._view,o=n._isVerticallyGrouped();return o?e?i.date.addDays(t,h*(c-1)+1):i.date.nextDay(t):i.date.addDays(t,h)},_getContent:function(t,e,n,o){return 0===o?t({date:e,resources:n}):""},_getTimeSlotByPosition:function(t,e,n){var o=this._view.groups[n];return o.daySlotByPosition(t,e,!0)},_nextSlotStartDate:function(t){return t},_getCalendarRowsLength:function(){var t=this._view,e=t._isVerticallyGrouped();return e?h:c},_createRowsLayout:function(t,e,n,o){var i=this._view;return i._createDateLayout(o,null,!1)},_createVerticalColumnsLayout:function(t,e,n){var o,r,s=this._view,a=t[0],l=[],d=a.dataSource.view();for(o=0;o<d.length*c;o++)r={text:n({text:i.htmlEncode(i.getter(a.dataTextField)(d[o%d.length])),color:i.getter(a.dataColorField)(d[o%d.length]),field:a.field,title:a.title,name:a.name,value:i.getter(a.dataValueField)(d[o%d.length])}),className:"k-slot-cell"},r.columns=s._createColumnsLayout(t.slice(1),null,n),l.push(r);return l},_createColumnsLayout:function(t,e,n,o){var i=this._view;return i._createColumnsLayout(t,e,n,o,!0)},_verticalGroupCount:function(t){var e=this._view;return e._columnCountForLevel(t)/c},_horizontalGroupCount:function(t){var e=this._view;return e._columnCountForLevel(t)/h},_positionEvent:function(t,e,n,o,i,r){var s,a,l,d,u=this._view,c=n.start.index,h=n.end.index,f=u._isMobile();for(s=n.start.index;s<=n.end.index;s++)a=n.collection._slots[s],l=e.daySlotRanges(a.start,a.start,!0)[0],d=t.clone({start:s===c?i:a.startDate(),end:s===h?r:a.endDate(),head:s!==h||n.head,tail:s!==c||n.tail}),f?u._positionMobileEvent(l,u._createEventElement(d),e):u._positionEvent(l,u._createEventElement(d),e)},_addDaySlotCollections:function(t,e,n){var o,r,s,a,l,d,u,f,_,p,v,g,m,y,w=this._view,D=h,C=c,S=w._isVerticallyGrouped();for(o=0;o<D;o++)for(r=0;r<C;r++)for(s=0,a=S?o:r,l=e[a],d=l.children,u=0,l.setAttribute("role","row"),w._isVerticallyGrouped()||(u=o),f=u*t;f<(u+1)*t;f++)_=r*D+o,p=S?f+r*t:f,v=d[p],g=S?f:s,m=w.groups[g],y=0===o?m.addDaySlotCollection(i.date.addDays(n,_),i.date.addDays(n,_+D)):m._daySlotCollections[r],w.addDaySlot(y,v,n,_),s++},_changePeriodGroupIndex:function(t,e,n){var o=this._view;return e&&o._isVerticallyGrouped()?t?o.groups.length-1:0:n},_createResizeHint:function(t){var e,n,o,i,r,a,l,d=this._view;if(d._isVerticallyGrouped())e=t.startSlot().offsetLeft,n=t.start.offsetTop,o=t.startSlot().offsetWidth,i=t.endSlot().offsetTop+t.startSlot().offsetHeight-t.startSlot().offsetTop-2,r=s.fn._createResizeHint.call(d,e,n,o,i),d._appendResizeHint(r);else for(a=t.startSlot().index;a<=t.endSlot().index;a++)l=t.collection._slots[a],e=l.offsetLeft,n=l.offsetTop,o=l.offsetWidth,i=l.offsetHeight-2,r=s.fn._createResizeHint.call(d,e,n,o,i),d._appendResizeHint(r)},_createMoveHint:function(t,e){var n,o,i,r=this._view,s=t.startSlot(),a=t.endSlot();for(n=s.index;n<=a.index;n++)o=t.collection._slots[n],i=r._createEventElement(e.clone({head:t.head,tail:t.tail})),i.css({left:o.offsetLeft,top:o.offsetTop+o.firstChildHeight,height:r.options.eventHeight,width:o.offsetWidth-2}),i.addClass("k-event-drag-hint"),e.inverseColor&&i.addClass(f),r._appendMoveHint(i)}});i.ui.scheduler.MonthGroupedView=m,i.ui.scheduler.MonthGroupedByDateView=y,r.MonthView=s.extend({init:function(t,e){var n=this;s.fn.init.call(n,t,e),n._groupedView=n._getGroupedView(),n.title=n.options.title,n._templates(),n._editable(),n._renderLayout(n.options.date),n._groups()},name:"month",_getGroupedView:function(){return this._isGroupedByDate()?new i.ui.scheduler.MonthGroupedByDateView(this):new i.ui.scheduler.MonthGroupedView(this)},_updateDirection:function(t,e,n,o,i){var r,s,a,l,d;n&&(r=e[0].start,s=e[e.length-1].end,a=r.index===s.index,l=r.collectionIndex===s.collectionIndex,d=i?a&&l||l:a&&l,d&&(t.backward=o))},_changeDate:function(t,e,n){var o,i,r,s=this.groups[t.groupIndex];if(n){if(o=s._getCollections(s.daySlotCollectionCount()),i=e.collectionIndex-1,i>=0)return o[i]._slots[o[i]._slots.length-1]}else if(o=s._getCollections(s.daySlotCollectionCount()),i=e.collectionIndex+1,r=0,o[i]&&o[i]._slots[r])return o[i]._slots[r]},_getNextHorizontalRange:function(t,e,n){var o=this._isVerticallyGrouped();return n.startSlot=t[e](n.startSlot,o),n.endSlot=t[e](n.endSlot,o),n},_getNextVerticalRange:function(t,e,n,o){var i=this._isVerticallyGrouped()&&this._isGroupedByDate();return n.startSlot=t[e](n.startSlot,o,i),n.endSlot=t[e](n.endSlot,o,i),n},_changeViewPeriod:function(t,e,n){var o,r,s=n?7:1;return e&&(s*=-1),o=i.date.addDays(t.start,s),r=i.date.addDays(t.end,s),!this._isInRange(o,r)&&(t.start=o,t.end=r,(!n||n&&this._isVerticallyGrouped())&&(t.groupIndex=this._groupedView._changePeriodGroupIndex(e,n,t.groupIndex)),t.events=[],!0)},_continuousSlot:function(t,e,n){var o=t.backward?0:e.length-1,i=this.groups[t.groupIndex];return i.continuousSlot(e[o].start,n)},_changeGroupContinuously:function(t,e,n,o){var i,r,s,a;return n||(i=t.groupIndex,r=this.groups.length-1,s=this._isVerticallyGrouped(),a=this.groups[i],!e&&s?(e=a[o?"lastSlot":"firstSlot"](),i+=o?-1:1):e&&!s&&(i=o?r:0),(i<0||i>r)&&(i=o?r:0,e=null),t.groupIndex=i),e},_normalizeHorizontalSelection:function(t,e,n){var o;return o=n?e[0].start:e[e.length-1].end},_normalizeVerticalSelection:function(t,e){var n;return n=t.backward?e[0].start:e[e.length-1].end},_templates:function(){var t=this.options,e=l({},i.Template,t.templateSettings);this.eventTemplate=this._eventTmpl(t.eventTemplate,p),this.dayTemplate=i.template(t.dayTemplate,e),this.groupHeaderTemplate=i.template(t.groupHeaderTemplate,e)},dateForTitle:function(){return i.format(this.options.selectedDateFormat,this._firstDayOfMonth,this._lastDayOfMonth)},shortDateForTitle:function(){return i.format(this.options.selectedShortDateFormat,this._firstDayOfMonth,this._lastDayOfMonth)},mobileDateForTitle:function(){return i.format(this.options.selectedMobileDateFormat,this._firstDayOfMonth,this._lastDayOfMonth)},nextDate:function(){return i.date.nextDay(this._lastDayOfMonth)},previousDate:function(){return i.date.previousDay(this._firstDayOfMonth)},startDate:function(){return this._startDate},endDate:function(){return this._endDate},_renderLayout:function(e){var o=this;this._firstDayOfMonth=i.date.firstDayOfMonth(e),this._lastDayOfMonth=i.date.lastDayOfMonth(e),this._startDate=n(e,this.calendarInfo()),this.createLayout(this._layout()),this._content(),this.refreshLayout(),this.content.on("click"+a,".k-nav-day,.k-more-events",function(e){var n=t(e.currentTarget).offset(),i=o._slotByPosition(n.left,n.top);e.preventDefault(),o.trigger("navigate",{view:"day",date:i.startDate()})}),this._footer()},_editable:function(){this.options.editable&&(this._isMobile()?this._touchEditable():this._mouseEditable())},_mouseEditable:function(){var e=this;e.element.on("click"+a,".k-scheduler-monthview .k-event a:has(.k-i-close)",function(n){e.trigger("remove",{uid:t(this).closest(".k-event").attr(i.attr("uid"))}),n.preventDefault()}),e.options.editable.create!==!1&&e.element.on("dblclick"+a,".k-scheduler-monthview .k-scheduler-content td",function(n){var o,i=t(n.currentTarget).offset(),r=e._slotByPosition(i.left,i.top);r&&(o=e._resourceBySlot(r),e.trigger("add",{eventInfo:l({isAllDay:!0,start:r.startDate(),end:r.startDate()},o)})),n.preventDefault()}),e.options.editable.update!==!1&&e.element.on("dblclick"+a,".k-scheduler-monthview .k-event",function(n){e.trigger("edit",{uid:t(this).closest(".k-event").attr(i.attr("uid"))}),n.preventDefault()})},_touchEditable:function(){var e=this,n=0;i.support.mobileOS.android&&(n=5),e.options.editable.create!==!1&&(e._addUserEvents=new i.UserEvents(e.element,{threshold:n,useClickAsTap:!i.support.browser.edge,filter:".k-scheduler-monthview .k-scheduler-content td",tap:function(n){var o,i,r;e._scrolling||(o=t(n.target).offset(),i=e._slotByPosition(o.left,o.top),i&&(r=e._resourceBySlot(i),e.trigger("add",{eventInfo:l({isAllDay:!0,start:i.startDate(),end:i.startDate()},r)})),n.preventDefault())}}))},selectionByElement:function(e){var n=t(e).offset();return this._slotByPosition(n.left,n.top)},_columnCountForLevel:function(t){var e=this.columnLevels[t];return e?e.length:0},_rowCountForLevel:function(t){var e=this.rowLevels[t];return e?e.length:0},_content:function(){var t,e="<tbody>",n=1,o=this._groupedView,i=this.groupedResources;for(i.length&&this._isVerticallyGrouped()&&(n=o._verticalRowCountForLevel(i.length-1)),t=0;t<n;t++)e+=this._createCalendar(t);e+="</tbody>",this.content.find("table").html(e)},_createCalendar:function(t){var e,n,o,r=this.startDate(),s=h*c,a=h,l=[r],d="",u=1,f=this._isVerticallyGrouped(),_=this._groupedView,p=this.groupedResources;for(p.length&&(f||(u=_._horizontalGroupCountForLevel(p.length-1))),this._slotIndices={},e=_._getCalendarRowsLength(a,s),n=0;n<e;n++)d+="<tr>",l.push(r),o=n*a,d+=_._createRows(r,o,u,t),r=_._adjustStartDate(r,n===e-1),d+="</tr>";return this._weekStartDates=l,this._endDate=i.date.previousDay(r),d},_createRow:function(t,e,n,o){var r,s=this,a=s._firstDayOfMonth,l=s._lastDayOfMonth,u=s.dayTemplate,c="",h="",f=this._groupedView,_=function(){return s._resourceBySlot({groupIndex:o})};for(r=0;r<n;r++)c="",i.date.isToday(t)&&(c+="k-today"),i.date.isInDateRange(t,a,l)||(c+=" k-other-month"),h+="<td ",""!==c&&(h+='class="'+c+'"'),h+=">",h+=f._getContent(u,t,_,r),h+="</td>",s._slotIndices[d(t).getTime()]=e+r,t=f._nextSlotStartDate(t);return h},_layout:function(){var n,o,i,r=this.calendarInfo(),s=this._isMobile()?r.days.namesShort.map(function(t){return t[0]}):r.days.names,a=e(s,r.firstDay),l=t.map(a,function(t){return{text:t}}),d=this.groupedResources,u=this._groupedView;if(d.length)if(this._isVerticallyGrouped()){for(o=[],i=0;i<6;i++)o.push({text:"<div>&nbsp;</div>",className:"k-hidden k-slot-cell"});n=u._createRowsLayout(d,o,this.groupHeaderTemplate,l),l=u._createVerticalColumnsLayout(d,o,this.groupHeaderTemplate,l)}else l=u._createColumnsLayout(d,l,this.groupHeaderTemplate,l);return{columns:l,rows:n}},_createEventElement:function(e){var n,o=this.options,r=o.editable,s=this._isMobile();return e.showDelete=r&&r.destroy!==!1&&!s,e.resizable=r&&r.resize!==!1&&!s,e.ns=i.ns,e.resources=this.eventResources(e),e.inverseColor=!1,e.messages=o.messages||{destroy:"Delete"},n=t(this.eventTemplate(e)),this.angular("compile",function(){return{elements:n,data:[{dataItem:e}]}}),n},_isInDateSlot:function(t){var e=this.groups[0],n=e.firstSlot().start,r=e.lastSlot().end-1,s=i.date.toUtcTime(t.start),a=i.date.toUtcTime(t.end);return(o(s,n,r)||o(a,n,r)||o(n,s,a)||o(r,s,a))&&(!o(a,n,n)||o(a,s,s)||t.isAllDay)},_slotIndex:function(t){return this._slotIndices[d(t).getTime()]},_positionMobileEvent:function(e,n,o){var r,a,l,d,u,c,h,f=e.start;e.start.offsetLeft>e.end.offsetLeft&&(f=e.end),r=e.start.index,a=r,l=3,d=s.collidingEvents(e.events(),r,a),d.push({element:n,start:r,end:a}),u=s.createRows(d),c=e.collection.at(r),h=c.container,h||(h=t(i.format('<div class="k-events-container" style="top:{0};left:{1};width:{2}"/>',f.offsetTop+f.firstChildTop+f.firstChildHeight+"px",f.offsetLeft+"px",f.offsetWidth+"px")),c.container=h,this.content[0].appendChild(h[0])),u.length<=l&&(e.addEvent({element:n,start:r,end:a,groupIndex:f.groupIndex}),o._continuousEvents.push({element:n,uid:n.attr(i.attr("uid")),start:e.start,end:e.end}),h[0].appendChild(n[0]))},_positionEvent:function(e,n,o){var r,a,l,d,u,c,h,f,_,p,v,m,y,w,D,C=this.options.eventHeight,S=e.start;for(e.start.offsetLeft>e.end.offsetLeft&&(S=e.end),r=e.start.index,a=e.end.index,l=S.eventCount,d=s.collidingEvents(e.events(),r,a),u=r!==a?5:4,d.push({element:n,start:r,end:a}),c=s.createRows(d),h=0,f=Math.min(c.length,l);h<f;h++)for(_=c[h].events,p=S.offsetTop+S.firstChildTop+S.firstChildHeight+h*C+3*h+"px",v=0,m=_.length;v<m;v++)_[v].element[0].style.top=p;if(c.length>l)for(y=r;y<=a;y++)w=e.collection,D=w.at(y),D.more||(D.more=t(g({ns:i.ns,start:y,end:y,width:D.clientWidth-2,left:D.offsetLeft+2,top:D.offsetTop+D.firstChildTop+D.firstChildHeight+l*C+3*l})),this.content[0].appendChild(D.more[0]));else e.addEvent({element:n,start:r,end:a,groupIndex:S.groupIndex}),n[0].style.width=e.innerWidth()-u+"px",n[0].style.left=S.offsetLeft+2+"px",n[0].style.height=C+"px",o._continuousEvents.push({element:n,uid:n.attr(i.attr("uid")),start:e.start,end:e.end}),n.appendTo(this.content),this._inverseEventColor(n)},_slotByPosition:function(t,e){var n,o,i=this.content.offset();for(t-=i.left,e-=i.top,e+=this.content[0].scrollTop,t+=this.content[0].scrollLeft,t=Math.ceil(t),e=Math.ceil(e),n=0;n<this.groups.length;n++)if(o=this._groupedView._getTimeSlotByPosition(t,e,n))return o;return null},_appendResizeHint:function(t){t.appendTo(this.content),this._resizeHint=this._resizeHint.add(t)},_updateResizeHint:function(t,e,n,o){var r,s,a;for(this._removeResizeHint(),r=this.groups[e],s=r.ranges(n,o,!0,t.isAllDay),a=0;a<s.length;a++)this._groupedView._createResizeHint(s[a]);this._resizeHint.find(".k-label-top,.k-label-bottom").text(""),this._resizeHint.first().addClass("k-first").find(".k-label-top").text(i.toString(i.timezone.toLocalDate(n),"M/dd")),this._resizeHint.last().addClass("k-last").find(".k-label-bottom").text(i.toString(i.timezone.toLocalDate(o),"M/dd"))},_updateMoveHint:function(t,e,n){var o,r=i.date.toUtcTime(t.start)+n,s=r+t.duration(),a=this.groups[e],l=a.ranges(r,s,!0,t.isAllDay);for(this._removeMoveHint(t.uid),o=0;o<l.length;o++)this._groupedView._createMoveHint(l[o],t)},_appendMoveHint:function(t){t.appendTo(this.content),this._moveHint=this._moveHint.add(t)},_groups:function(){var t,e=this._groupCount(),n=this.content[0].getElementsByTagName("tr"),o=this.startDate();for(this.groups=[],t=0;t<e;t++)this._addResourceView(t);this._groupedView._addDaySlotCollections(e,n,o)},addDaySlot:function(t,e,n,o){var r,s=e.clientHeight,a=e.children.length?e.children[0].offsetHeight+3:0,l=i.date.addDays(n,o),d=i.date.MS_PER_DAY;n.getHours()!==l.getHours()&&(d+=(n.getHours()-l.getHours())*i.date.MS_PER_HOUR),l=i.date.toUtcTime(l),d+=l,r=Math.floor((s-a-this.options.moreButtonHeight)/(this.options.eventHeight+3)),e.setAttribute("role","gridcell"),e.setAttribute("aria-selected",!1),t.addDaySlot(e,l,d,r)},render:function(t){this.content.children(".k-event,.k-more-events,.k-events-container").remove(),this._groups(),t=new i.data.Query(t).sort([{field:"start",dir:"asc"},{field:"end",dir:"desc"}]).toArray();var e=this.groupedResources;e.length?this._renderGroups(t,e,0,1):this._renderEvents(t,0),this.refreshLayout(),this.trigger("activate")},_renderEvents:function(t,e){var n,o,r,s,a,l,d,u,c,h,f,_,p,v,g,m,y;for(o=0,r=t.length;o<r;o++)if(n=t[o],this._isInDateSlot(n))if(d=this.groups[e],u=this._groupedView._view,c=u._isMobile(),d._continuousEvents||(d._continuousEvents=[]),h=d.slotRanges(n,!0),f=h.length,c)for(s=h[0],a=s.start.start,l=s.end.end,_=new Date(s.start.start),p=h[h.length-1].end.end,v=new Date(_),g=new Date(l);_.getTime()<=p&&n.end>=i.timezone.toLocalDate(_)&&n.start<=i.timezone.toLocalDate(p);)m=d.daySlotRanges(v.getTime(),g.getTime(),!0)[0],g.setDate(g.getDate()+1),v.setDate(v.getDate()+1),m&&(m.head=null,m.middle=null,m.tail=null,this._groupedView._positionEvent(n,d,m,1,a,l,0)),_=i.date.addDays(_,1);else for(y=0;y<f;y++)s=h[y],a=n.start,l=n.end,this._groupedView._positionEvent(n,d,s,f,a,l,y)},_renderGroups:function(t,e,n,o){var r,a,l,d,u=e[0];if(u)for(r=u.dataSource.view(),a=0;a<r.length;a++)l=this._resourceValue(u,r[a]),d=new i.data.Query(t).filter({field:u.field,operator:s.groupEqFilter(l)}).toArray(),e.length>1?n=this._renderGroups(d,e.slice(1),n++,o+1):this._renderEvents(d,n++);return n},_groupCount:function(){var t=this.groupedResources,e=this._groupedView;return t.length?this._isVerticallyGrouped()?e._verticalGroupCount(t.length-1):e._horizontalGroupCount(t.length):1},_columnOffsetForResource:function(t){return this._columnCountForLevel(t)/this._columnCountForLevel(t-1)},destroy:function(){this.table&&this.table.removeClass("k-scheduler-monthview"),this.content&&this.content.off(a),this.element&&this.element.off(a),s.fn.destroy.call(this),this._isMobile()&&this.options.editable&&this.options.editable.create!==!1&&this._addUserEvents.destroy()},events:["remove","add","edit","navigate"],options:{title:"Month",name:"month",eventHeight:25,moreButtonHeight:13,editable:!0,selectedDateFormat:"{0:y}",selectedShortDateFormat:"{0:y}",selectedMobileDateFormat:"{0:MMMM}",groupHeaderTemplate:"#=text#",dayTemplate:_,eventTemplate:v}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()});
//# sourceMappingURL=kendo.scheduler.monthview.min.js.map
