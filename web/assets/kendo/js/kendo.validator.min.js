/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.validator.min",["kendo.core.min"],t)}(function(){return function(t,e){function a(e){var a,r=s.ui.validator.ruleResolvers||{},n={};for(a in r)t.extend(!0,n,r[a].resolve(e));return n}function r(t){return t.replace(/&amp/g,"&amp;").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">")}function n(t){return t=(t+"").split("."),t.length>1?t[1].length:0}function i(e){return t(t.parseHTML?t.parseHTML(e):e)}function u(e,a){var r,n,i,u,l=t();for(i=0,u=e.length;i<u;i++)r=e[i],c.test(r.className)&&(n=r.getAttribute(s.attr("for")),n===a&&(l=l.add(r)));return l}var l,s=window.kendo,o=s.ui.Widget,d=".kendoValidator",F="k-invalid-msg",c=RegExp(F,"i"),p="k-invalid",f="k-valid",h=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/i,m=/^(https?|ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i,v=":input:not(:button,[type=submit],[type=reset],[disabled],[readonly])",g=":checkbox:not([disabled],[readonly])",D="[type=number],[type=range]",_="blur",y="name",k="form",E="novalidate",A="validate",C="change",b="validateInput",x=t.proxy,z=function(t,e){return"string"==typeof e&&(e=RegExp("^(?:"+e+")$")),e.test(t)},w=function(t,e,a){var r=t.val();return!t.filter(e).length||""===r||z(r,a)},M=function(t,e){return!!t.length&&null!=t[0].attributes[e]};s.ui.validator||(s.ui.validator={rules:{},messages:{}}),l=o.extend({init:function(e,r){var n=this,i=a(e),u="["+s.attr("validate")+"!=false]";r=r||{},r.rules=t.extend({},s.ui.validator.rules,i.rules,r.rules),r.messages=t.extend({},s.ui.validator.messages,i.messages,r.messages),o.fn.init.call(n,e,r),n._errorTemplate=s.template(n.options.errorTemplate),n.element.is(k)&&n.element.attr(E,E),n._inputSelector=v+u,n._checkboxSelector=g+u,n._errors={},n._attachEvents(),n._isValidated=!1},events:[A,C,b],options:{name:"Validator",errorTemplate:'<span class="k-widget k-tooltip k-tooltip-validation"><span class="k-icon k-i-warning"> </span> #=message#</span>',messages:{required:"{0} is required",pattern:"{0} is not valid",min:"{0} should be greater than or equal to {1}",max:"{0} should be smaller than or equal to {1}",step:"{0} is not valid",email:"{0} is not valid email",url:"{0} is not valid URL",date:"{0} is not valid date",dateCompare:"End date should be greater than or equal to the start date"},rules:{required:function(t){var e=t.filter("[type=checkbox]").length&&!t.is(":checked"),a=t.val();return!(M(t,"required")&&(!a||""===a||0===a.length||e))},pattern:function(t){return!t.filter("[type=text],[type=email],[type=url],[type=tel],[type=search],[type=password]").filter("[pattern]").length||""===t.val()||z(t.val(),t.attr("pattern"))},min:function(t){if(t.filter(D+",["+s.attr("type")+"=number]").filter("[min]").length&&""!==t.val()){var e=parseFloat(t.attr("min"))||0,a=s.parseFloat(t.val());return e<=a}return!0},max:function(t){if(t.filter(D+",["+s.attr("type")+"=number]").filter("[max]").length&&""!==t.val()){var e=parseFloat(t.attr("max"))||0,a=s.parseFloat(t.val());return e>=a}return!0},step:function(t){if(t.filter(D+",["+s.attr("type")+"=number]").filter("[step]").length&&""!==t.val()){var e,a=parseFloat(t.attr("min"))||0,r=parseFloat(t.attr("step"))||1,i=parseFloat(t.val()),u=n(r);return u?(e=Math.pow(10,u),Math.floor((i-a)*e)%(r*e)/Math.pow(100,u)===0):(i-a)%r===0}return!0},email:function(t){return w(t,"[type=email],["+s.attr("type")+"=email]",h)},url:function(t){return w(t,"[type=url],["+s.attr("type")+"=url]",m)},date:function(t){return!t.filter("[type^=date],["+s.attr("type")+"=date]").length||""===t.val()||null!==s.parseDate(t.val(),t.attr(s.attr("format")))}},validateOnBlur:!0},destroy:function(){o.fn.destroy.call(this),this.element.off(d)},value:function(){return!!this._isValidated&&0===this.errors().length},_submit:function(t){return!!this.validate()||(t.stopPropagation(),t.stopImmediatePropagation(),t.preventDefault(),!1)},_checkElement:function(t){var e=this.value();this.validateInput(t),this.value()!==e&&this.trigger(C)},_attachEvents:function(){var e=this;e.element.is(k)&&e.element.on("submit"+d,x(e._submit,e)),e.options.validateOnBlur&&(e.element.is(v)?(e.element.on(_+d,function(){e._checkElement(e.element)}),e.element.is(g)&&e.element.on("click"+d,function(){e._checkElement(e.element)})):(e.element.on(_+d,e._inputSelector,function(){e._checkElement(t(this))}),e.element.on("click"+d,e._checkboxSelector,function(){e._checkElement(t(this))})))},validate:function(){var t,e,a,r,n=!1,i=this.value();if(this._errors={},this.element.is(v))n=this.validateInput(this.element);else{for(r=!1,t=this.element.find(this._inputSelector),e=0,a=t.length;e<a;e++)this.validateInput(t.eq(e))||(r=!0);n=!r}return this.trigger(A,{valid:n}),i!==n&&this.trigger(C),n},validateInput:function(e){var a,n,u,l,o,d,c,h,m,v,g,D;return e=t(e),this._isValidated=!0,a=this,n=a._errorTemplate,u=a._checkValidity(e),l=u.valid,o="."+F,d=e.attr(y)||"",c=a._findMessageContainer(d).add(e.next(o).filter(function(){var e=t(this);return!e.filter("["+s.attr("for")+"]").length||e.attr(s.attr("for"))===d})).hide(),m=!e.attr("aria-invalid"),e.removeAttr("aria-invalid"),l?delete a._errors[d]:(h=a._extractMessage(e,u.key),a._errors[d]=h,v=i(n({message:r(h)})),g=c.attr("id"),a._decorateMessageContainer(v,d),g&&v.attr("id",g),c.replaceWith(v).length||v.insertAfter(e),v.show(),e.attr("aria-invalid",!0)),m!==l&&this.trigger(b,{valid:l,input:e}),e.toggleClass(p,!l),e.toggleClass(f,l),s.widgetInstance(e)&&(D=s.widgetInstance(e)._inputWrapper,D&&(D.toggleClass(p,!l),D.toggleClass(p,!l))),l},hideMessages:function(){var t=this,e="."+F,a=t.element;a.is(v)?a.next(e).hide():a.find(e).hide()},_findMessageContainer:function(e){var a,r,n,i=s.ui.validator.messageLocators,l=t();for(r=0,n=this.element.length;r<n;r++)l=l.add(u(this.element[r].getElementsByTagName("*"),e));for(a in i)l=l.add(i[a].locate(this.element,e));return l},_decorateMessageContainer:function(t,e){var a,r=s.ui.validator.messageLocators;t.addClass(F).attr(s.attr("for"),e||"");for(a in r)r[a].decorate(t,e);t.attr("role","alert")},_extractMessage:function(t,e){var a,r=this,n=r.options.messages[e],i=t.attr(y);return s.ui.Validator.prototype.options.messages[e]||(a=s.isFunction(n)?n(t):n),n=s.isFunction(n)?n(t):n,s.format(t.attr(s.attr(e+"-msg"))||t.attr("validationMessage")||a||t.attr("title")||n||"",i,t.attr(e)||t.attr(s.attr(e)))},_checkValidity:function(t){var e,a=this.options.rules;for(e in a)if(!a[e].call(this,t))return{valid:!1,key:e};return{valid:!0}},errors:function(){var t,e=[],a=this._errors;for(t in a)e.push(a[t]);return e}}),s.ui.plugin(l)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,a){(a||e)()});
//# sourceMappingURL=kendo.validator.min.js.map
