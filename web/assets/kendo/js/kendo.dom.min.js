/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.dom.min",["kendo.core.min"],e)}(function(){return function(e){function t(){this.node=null}function n(){}function o(e,t,n){this.nodeName=e,this.attr=t||{},this.children=n||[]}function r(e){this.nodeValue=e+""}function i(e){this.html=e}function d(e,t){for(f.innerHTML=t;f.firstChild;)e.appendChild(f.firstChild)}function s(e){return new i(e)}function h(e,t,n){return new o(e,t,n)}function l(e){return new r(e)}function a(e){this.root=e,this.children=[]}var u,f;t.prototype={remove:function(){this.node.parentNode&&this.node.parentNode.removeChild(this.node),this.attr={}},attr:{},text:function(){return""}},n.prototype={nodeName:"#null",attr:{style:{}},children:[],remove:function(){}},u=new n,o.prototype=new t,o.prototype.appendTo=function(e){var t,n=document.createElement(this.nodeName),o=this.children;for(t=0;t<o.length;t++)o[t].render(n,u);return e.appendChild(n),n},o.prototype.render=function(e,t){var n,o,r,i,d,s;if(t.nodeName!==this.nodeName)t.remove(),n=this.appendTo(e);else{if(n=t.node,r=this.children,i=r.length,d=t.children,s=d.length,Math.abs(s-i)>2)return void this.render({appendChild:function(n){e.replaceChild(n,t.node)}},u);for(o=0;o<i;o++)r[o].render(n,d[o]||u);for(o=i;o<s;o++)d[o].remove()}this.node=n,this.syncAttributes(t.attr),this.removeAttributes(t.attr)},o.prototype.syncAttributes=function(e){var t,n,o,r=this.attr;for(t in r)n=r[t],o=e[t],"style"===t?this.setStyle(n,o):n!==o&&this.setAttribute(t,n,o)},o.prototype.setStyle=function(e,t){var n,o=this.node;if(t)for(n in e)e[n]!==t[n]&&(o.style[n]=e[n]);else for(n in e)o.style[n]=e[n]},o.prototype.removeStyle=function(e){var t,n=this.attr.style||{},o=this.node;for(t in e)void 0===n[t]&&(o.style[t]="")},o.prototype.removeAttributes=function(e){var t,n=this.attr;for(t in e)"style"===t?this.removeStyle(e.style):void 0===n[t]&&this.removeAttribute(t)},o.prototype.removeAttribute=function(e){var t=this.node;"style"===e?t.style.cssText="":"className"===e?t.className="":t.removeAttribute(e)},o.prototype.setAttribute=function(e,t){var n=this.node;void 0!==n[e]?n[e]=t:n.setAttribute(e,t)},o.prototype.text=function(){var e,t="";for(e=0;e<this.children.length;++e)t+=this.children[e].text();return t},r.prototype=new t,r.prototype.nodeName="#text",r.prototype.render=function(e,t){var n;t.nodeName!==this.nodeName?(t.remove(),n=document.createTextNode(this.nodeValue),e.appendChild(n)):(n=t.node,this.nodeValue!==t.nodeValue&&n.parentNode&&(n.nodeValue=this.nodeValue)),this.node=n},r.prototype.text=function(){return this.nodeValue},i.prototype={nodeName:"#html",attr:{},remove:function(){var e,t;for(e=0;e<this.nodes.length;e++)t=this.nodes[e],t.parentNode&&t.parentNode.removeChild(t)},render:function(e,t){var n,o;if(t.nodeName!==this.nodeName||t.html!==this.html)for(t.remove(),n=e.lastChild,d(e,this.html),this.nodes=[],o=n?n.nextSibling:e.firstChild;o;o=o.nextSibling)this.nodes.push(o);else this.nodes=t.nodes.slice(0)}},f=document.createElement("div"),a.prototype={html:s,element:h,text:l,render:function(e){var t,n,o,r=this.children;for(t=0,n=e.length;t<n;t++)o=r[t],o?o.node&&o.node.parentNode||(o.remove(),o=u):o=u,e[t].render(this.root,o);for(t=n;t<r.length;t++)r[t].remove();this.children=e}},e.dom={html:s,text:l,element:h,Tree:a,Node:t}}(window.kendo),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.dom.min.js.map
