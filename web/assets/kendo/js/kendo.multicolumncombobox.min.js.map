{"version": 3, "sources": ["kendo.multicolumncombobox.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "ui", "ComboBox", "percentageUnitsRegex", "MCCOMBOBOX", "POPUPCLASS", "MultiColumnComboBox", "extend", "init", "element", "options", "fn", "call", "this", "list", "addClass", "_allColumnsWidthsAreSet", "width", "_calculateDropDownWidth", "dropDownWidth", "name", "ns", "columns", "filterFields", "setOptions", "i", "currentWidth", "length", "isNaN", "parseInt", "test", "totalWidth", "support", "scrollbar", "_wrapper", "wrapper", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,6BAA8B,kBAAmBD,IAC1D,WA8EE,MAxDC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAAWD,EAAGC,SAAUC,EAAuB,kBAAmBC,EAAa,iBAAkBC,EAAa,qCACnJC,EAAsBJ,EAASK,QAC/BC,KAAM,SAAUC,EAASC,GACrBR,EAASS,GAAGH,KAAKI,KAAKC,KAAMJ,EAASC,GACrCG,KAAKC,KAAKC,SAASV,GACfQ,KAAKG,wBAAwBH,KAAKH,SAClCG,KAAKC,KAAKG,MAAMJ,KAAKK,wBAAwBL,KAAKH,UAC3CG,KAAKH,QAAQS,eACpBN,KAAKC,KAAKG,MAAMJ,KAAKH,QAAQS,gBAGrCT,SACIU,KAAM,sBACNC,GAAI,4BACJC,WACAH,cAAe,KACfI,iBAEJC,WAAY,SAAUd,GAClBR,EAASS,GAAGa,WAAWZ,KAAKC,KAAMH,GAC9BG,KAAKG,wBAAwBN,GAC7BG,KAAKC,KAAKG,MAAMJ,KAAKK,wBAAwBR,IACtCG,KAAKH,QAAQS,eACpBN,KAAKC,KAAKG,MAAMJ,KAAKH,QAAQS,gBAGrCH,wBAAyB,SAAUN,GAAV,GAKZe,GACDC,EALJJ,EAAUZ,EAAQY,OACtB,KAAKA,IAAYA,EAAQK,OACrB,OAAO,CAEX,KAASF,EAAI,EAAGA,EAAIH,EAAQK,OAAQF,IAEhC,GADIC,EAAeJ,EAAQG,GAAGR,OACzBS,GAAgBE,MAAMC,SAASH,EAAc,MAAQvB,EAAqB2B,KAAKJ,GAChF,OAAO,CAGf,QAAO,GAEXR,wBAAyB,SAAUR,GAAV,GAGZe,GACDC,EAHJJ,EAAUZ,EAAQY,QAClBS,EAAahC,EAAMiC,QAAQC,WAC/B,KAASR,EAAI,EAAGA,EAAIH,EAAQK,OAAQF,IAC5BC,EAAeJ,EAAQG,GAAGR,MAC9Bc,GAA0BF,SAASH,EAAc,GAErD,OAAOK,IAEXG,SAAU,WACNhC,EAASS,GAAGuB,SAAStB,KAAKC,MAC1BA,KAAKsB,QAAQpB,SAASX,KAG9BH,GAAGmC,OAAO9B,IACZN,OAAOD,MAAMsC,QACRrC,OAAOD,OACE,kBAAVH,SAAwBA,OAAO0C,IAAM1C,OAAS,SAAU2C,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.multicolumncombobox.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.multicolumncombobox', ['kendo.combobox'], f);\n}(function () {\n    var __meta__ = {\n        id: 'multicolumncombobox',\n        name: 'MultiColumnComboBox',\n        category: 'web',\n        description: 'The MultiColumnComboBox widget allows the selection from pre-defined values or entering a new value where the list popup is rendered in table layout.',\n        depends: ['combobox'],\n        features: [\n            {\n                id: 'mobile-scroller',\n                name: 'Mobile scroller',\n                description: 'Support for kinetic scrolling in mobile device',\n                depends: ['mobile.scroller']\n            },\n            {\n                id: 'virtualization',\n                name: 'VirtualList',\n                description: 'Support for virtualization',\n                depends: ['virtuallist']\n            }\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, ComboBox = ui.ComboBox, percentageUnitsRegex = /^\\d+(\\.\\d+)?%$/i, MCCOMBOBOX = 'k-dropdowngrid', POPUPCLASS = 'k-dropdowngrid-popup k-popup-flush';\n        var MultiColumnComboBox = ComboBox.extend({\n            init: function (element, options) {\n                ComboBox.fn.init.call(this, element, options);\n                this.list.addClass(POPUPCLASS);\n                if (this._allColumnsWidthsAreSet(this.options)) {\n                    this.list.width(this._calculateDropDownWidth(this.options));\n                } else if (this.options.dropDownWidth) {\n                    this.list.width(this.options.dropDownWidth);\n                }\n            },\n            options: {\n                name: 'MultiColumnComboBox',\n                ns: '.kendoMultiColumnComboBox',\n                columns: [],\n                dropDownWidth: null,\n                filterFields: []\n            },\n            setOptions: function (options) {\n                ComboBox.fn.setOptions.call(this, options);\n                if (this._allColumnsWidthsAreSet(options)) {\n                    this.list.width(this._calculateDropDownWidth(options));\n                } else if (this.options.dropDownWidth) {\n                    this.list.width(this.options.dropDownWidth);\n                }\n            },\n            _allColumnsWidthsAreSet: function (options) {\n                var columns = options.columns;\n                if (!columns || !columns.length) {\n                    return false;\n                }\n                for (var i = 0; i < columns.length; i++) {\n                    var currentWidth = columns[i].width;\n                    if (!currentWidth || isNaN(parseInt(currentWidth, 10)) || percentageUnitsRegex.test(currentWidth)) {\n                        return false;\n                    }\n                }\n                return true;\n            },\n            _calculateDropDownWidth: function (options) {\n                var columns = options.columns;\n                var totalWidth = kendo.support.scrollbar();\n                for (var i = 0; i < columns.length; i++) {\n                    var currentWidth = columns[i].width;\n                    totalWidth = totalWidth + parseInt(currentWidth, 10);\n                }\n                return totalWidth;\n            },\n            _wrapper: function () {\n                ComboBox.fn._wrapper.call(this);\n                this.wrapper.addClass(MCCOMBOBOX);\n            }\n        });\n        ui.plugin(MultiColumnComboBox);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}