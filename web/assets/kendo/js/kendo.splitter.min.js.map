{"version": 3, "sources": ["kendo.splitter.js"], "names": ["f", "define", "$", "undefined", "isPercentageSize", "size", "percentageUnitsRegex", "test", "isPixelSize", "pxUnitsRegex", "isFluid", "calculateSize", "total", "output", "parseInt", "Math", "floor", "panePropertyAccessor", "propertyName", "triggersResize", "pane", "value", "splitter", "paneConfig", "this", "element", "find", "data", "PANE", "arguments", "length", "options", "name", "resize", "PaneResizing", "that", "orientation", "owner", "_element", "extend", "HORIZONTAL", "horizontalDefaults", "verticalDefaults", "_resizable", "kendo", "ui", "Resizable", "handle", "_marker", "hint", "proxy", "_createHint", "start", "_start", "max", "_max", "min", "_min", "invalidClass", "resizeend", "_stop", "window", "keys", "Widget", "NS", "EXPAND", "COLLAPSE", "CONTENTLOAD", "ERROR", "RESIZE", "LAYOUTCHANGE", "VERTICAL", "MOUSEENTER", "CLICK", "MOUSELEAVE", "FOCUSED", "KPANE", "PANECLASS", "Splitter", "init", "isHorizontal", "fn", "call", "wrapper", "toLowerCase", "_dimension", "_keys", "decrease", "LEFT", "UP", "increase", "RIGHT", "DOWN", "_resizeStep", "guid", "substring", "_initPanes", "resizing", "<PERSON><PERSON><PERSON><PERSON>", "events", "_addOverlays", "_panes", "append", "_removeOverlays", "children", "remove", "_attachEvents", "on", "_keydown", "e", "currentTarget", "focus", "addClass", "removeClass", "end", "_togglePane", "_arrowClick", "document", "_detachEvents", "off", "panes", "destroy", "key", "keyCode", "target", "navigationKeys", "ctrl<PERSON>ey", "isResizing", "_triggerAction", "move", "preventDefault", "HOME", "_maxPosition", "END", "ENTER", "panesConfig", "each", "i", "nodeName", "_initPane", "config", "attr", "toggleClass", "scrollable", "ajaxRequest", "url", "contentUrl", "isLocalUrl", "j<PERSON><PERSON><PERSON>", "ajax", "type", "dataType", "success", "angular", "elements", "get", "html", "trigger", "error", "xhr", "status", "arrow", "closest", "is", "prev", "next", "arrowType", "parent", "_updateSplitBar", "splitbar", "previousPane", "nextPane", "catIconIf", "iconType", "condition", "draggable", "resizable", "prevCollapsible", "collapsible", "prevCollapsed", "collapsed", "nextCollapsible", "nextCollapsed", "removeAttr", "_updateSplitBars", "prevAll", "first", "nextAll", "_removeSplitBars", "_resize", "sizedPanesWidth", "sizedPanesCount", "freeSizedPanes", "freeSizePanesCount", "freeSizePaneWidth", "sum", "alternateSizingProperty", "positioningProperty", "sizingDomProperty", "lastNonCollapsedPane", "splitBars", "splitBarsCount", "sizingProperty", "totalSize", "slice", "after", "css", "position", "top", "collapsedSize", "add", "eq", "filter", "last", "child", "tagName", "style", "toggle", "expand", "collapse", "_addPane", "idx", "paneElement", "splice", "appendTo", "insertBefore", "referencePane", "index", "insertAfter", "plugin", "mousePositioningProperty", "prototype", "press", "delta", "pressed", "previousPaneConfig", "nextPaneConfig", "prevBoundary", "nextBoundary", "toPx", "val", "prevMinSize", "prevMaxSize", "nextMinSize", "nextMaxSize", "_minPosition", "ghostPosition", "previousPaneNewSize", "nextPaneNewSize", "fluidPanesCount", "ESC", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,kBAAmB,mBAAoBD,IAChD,WAoeE,MA5dC,UAAUE,EAAGC,GAEV,QAASC,GAAiBC,GACtB,MAAOC,GAAqBC,KAAKF,GAErC,QAASG,GAAYH,GACjB,MAAOI,GAAaF,KAAKF,IAAS,QAAQE,KAAKF,GAEnD,QAASK,GAAQL,GACb,OAAQD,EAAiBC,KAAUG,EAAYH,GAEnD,QAASM,GAAcN,EAAMO,GACzB,GAAIC,GAASC,SAAST,EAAM,GAI5B,OAHID,GAAiBC,KACjBQ,EAASE,KAAKC,MAAMH,EAASD,EAAQ,MAElCC,EAEX,QAASI,GAAqBC,EAAcC,GACxC,MAAO,UAAUC,EAAMC,GAAhB,GAOKC,GANJC,EAAaC,KAAKC,QAAQC,KAAKN,GAAMO,KAAKC,EAC9C,OAAwB,IAApBC,UAAUC,OACHP,EAAWL,IAEtBK,EAAWL,GAAgBG,EACvBF,IACIG,EAAWE,KAAKC,QAAQE,KAAK,QAAUH,KAAKO,QAAQC,MACxDV,EAASW,QAAO,IAHpBV,IAgXR,QAASW,GAAaZ,GAClB,GAAIa,GAAOX,KAAMY,EAAcd,EAASc,WACxCD,GAAKE,MAAQf,EACba,EAAKG,SAAWhB,EAASG,QACzBU,EAAKC,YAAcA,EACnBG,EAAOJ,EAAMC,IAAgBI,EAAaC,EAAqBC,GAC/DP,EAAKQ,WAAa,GAAIC,GAAMC,GAAGC,UAAUxB,EAASG,SAC9CW,YAAaA,EACbW,OAAQ,yBAA2BX,EAAc,gBAAkBd,EAAS0B,QAAU,IACtFC,KAAMC,EAAMf,EAAKgB,YAAahB,GAC9BiB,MAAOF,EAAMf,EAAKkB,OAAQlB,GAC1BmB,IAAKJ,EAAMf,EAAKoB,KAAMpB,GACtBqB,IAAKN,EAAMf,EAAKsB,KAAMtB,GACtBuB,aAAc,qBAAuBtB,EACrCuB,UAAWT,EAAMf,EAAKyB,MAAOzB,KAtZxC,GA0XOO,GAOAD,EAhYAG,EAAQiB,OAAOjB,MAAOC,EAAKD,EAAMC,GAAIiB,EAAOlB,EAAMkB,KAAMvB,EAASrC,EAAEqC,OAAQW,EAAQhD,EAAEgD,MAAOa,EAASlB,EAAGkB,OAAQtD,EAAe,mBAAoBH,EAAuB,kBAAmB0D,EAAK,iBAAkBC,EAAS,SAAUC,EAAW,WAAYC,EAAc,cAAeC,EAAQ,QAASC,EAAS,SAAUC,EAAe,eAAgB9B,EAAa,aAAc+B,EAAW,WAAYC,EAAa,aAAcC,EAAQ,QAAS7C,EAAO,OAAQ8C,EAAa,aAAcC,EAAU,kBAAmBC,EAAQ,KAAOhD,EAAMiD,EAAY,IAAMD,EA8B1iBE,EAAWf,EAAOxB,QAClBwC,KAAM,SAAUtD,EAASM,GACrB,GAAiBiD,GAAb7C,EAAOX,IACXuC,GAAOkB,GAAGF,KAAKG,KAAK/C,EAAMV,EAASM,GACnCI,EAAKgD,QAAUhD,EAAKV,QAChBU,EAAKJ,QAAQK,cACb4C,EAAe7C,EAAKJ,QAAQK,YAAYgD,eAAiBb,GAE7DpC,EAAKC,YAAc4C,EAAexC,EAAa+B,EAC/CpC,EAAKkD,WAAaL,EAAe,QAAU,SAC3C7C,EAAKmD,OACDC,SAAUP,EAAelB,EAAK0B,KAAO1B,EAAK2B,GAC1CC,SAAUV,EAAelB,EAAK6B,MAAQ7B,EAAK8B,MAE/CzD,EAAK0D,YAAc,GACnB1D,EAAKa,QAAUJ,EAAMkD,OAAOC,UAAU,EAAG,GACzC5D,EAAK6D,aACL7D,EAAK8D,SAAW,GAAI/D,GAAaC,GACjCA,EAAKV,QAAQyE,eAAe,OAASlC,IAEzCmC,QACIlC,EACAC,EACAC,EACAC,EACAC,EACAC,GAEJ8B,aAAc,WACV5E,KAAK6E,SAASC,OAAO,iDAEzBC,gBAAiB,WACb/E,KAAK6E,SAASG,SAAS,uBAAuBC,UAElDC,cAAe,WACX,GAAIvE,GAAOX,KAAMY,EAAcD,EAAKJ,QAAQK,WAC5CD,GAAKV,QAAQ+E,SAAS,yBAA2BpE,GAAauE,GAAG,UAAY3C,EAAId,EAAMf,EAAKyE,SAAUzE,IAAOwE,GAAG,YAAc3C,EAAI,SAAU6C,GACxIA,EAAEC,cAAcC,UACjBJ,GAAG,QAAU3C,EAAI,SAAU6C,GAC1B3G,EAAE2G,EAAEC,eAAeE,SAASrC,KAC7BgC,GAAG,OAAS3C,EAAI,SAAU6C,GACzB3G,EAAE2G,EAAEC,eAAeG,YAAYtC,GAC3BxC,EAAK8D,UACL9D,EAAK8D,SAASiB,QAEnBP,GAAGnC,EAAaR,EAAI,WACnB9D,EAAEsB,MAAMwF,SAAS,cAAgB7E,EAAKC,YAAc,YACrDuE,GAAGjC,EAAaV,EAAI,WACnB9D,EAAEsB,MAAMyF,YAAY,cAAgB9E,EAAKC,YAAc,YACxDuE,GAAG,YAAc3C,EAAId,EAAMf,EAAKiE,aAAcjE,IAAO+E,MAAMV,SAAS,eAAeG,GAAG,WAAa3C,EAAId,EAAMf,EAAKgF,YAAahF,IAAOqE,SAAS,sCAAsCG,GAAGlC,EAAQT,EAAI7B,EAAKiF,YAAYlD,IAAWgD,MAAMV,SAAS,kCAAkCG,GAAGlC,EAAQT,EAAI7B,EAAKiF,YAAYnD,IAASiD,MAAMA,MACnUhH,EAAE2D,QAAQ8C,GAAG,SAAW3C,EAAK7B,EAAKa,QAASE,EAAMf,EAAKF,OAAQE,GAAM,IACpEjC,EAAEmH,UAAUV,GAAG,UAAY3C,EAAK7B,EAAKa,QAASE,EAAMf,EAAKoE,gBAAiBpE,KAE9EmF,cAAe,WACX,GAAInF,GAAOX,IACXW,GAAKV,QAAQ+E,SAAS,yBAA2BrE,EAAKC,aAAamF,IAAIvD,GAAIkD,MAAMV,SAAS,eAAee,IAAI,WAAavD,GAAIwC,SAAS,sEAAsEe,IAAIvD,GACjN9D,EAAE2D,QAAQ0D,IAAIvD,EAAK7B,EAAKa,SACxB9C,EAAEmH,UAAUE,IAAIvD,EAAK7B,EAAKa,UAE9BjB,SACIC,KAAM,WACNI,YAAaI,EACbgF,UAEJC,QAAS,WACL1D,EAAOkB,GAAGwC,QAAQvC,KAAK1D,MACvBA,KAAK8F,gBACD9F,KAAKyE,UACLzE,KAAKyE,SAASwB,UAElB7E,EAAM6E,QAAQjG,KAAKC,SACnBD,KAAK2D,QAAU3D,KAAKC,QAAU,MAElCmF,SAAU,SAAUC,GAChB,GAA8MzF,GAA1Me,EAAOX,KAAMkG,EAAMb,EAAEc,QAAS1B,EAAW9D,EAAK8D,SAAU2B,EAAS1H,EAAE2G,EAAEC,eAAgBe,EAAiB1F,EAAKmD,MAAOI,EAAWgC,IAAQG,EAAenC,SAAUH,EAAWmC,IAAQG,EAAetC,QAChMG,IAAYH,GACRsB,EAAEiB,SACF1G,EAAOwG,EAAOrC,EAAW,OAAS,UAC9BU,GAAYA,EAAS8B,cACrB9B,EAASiB,MAER9F,EAAKe,EAAKkD,cAGXlD,EAAK6F,eAAe9D,EAAU0D,EAAOrC,EAAW,OAAS,WAFzDpD,EAAK6F,eAAe/D,EAAQ7C,IAIzB6E,GACPA,EAASgC,MAAM1C,KAAgB,GAAKpD,EAAK0D,YAAa+B,GAE1Df,EAAEqB,kBACKR,IAAQ5D,EAAKqE,MACpBlC,EAASgC,MAAMhC,EAASmC,aAAcR,GACtCf,EAAEqB,kBACKR,IAAQ5D,EAAKuE,KACpBpC,EAASgC,KAAKhC,EAASmC,aAAcR,GACrCf,EAAEqB,kBACKR,IAAQ5D,EAAKwE,OAASrC,IAC7BA,EAASiB,MACTL,EAAEqB,mBAGVlC,WAAY,WAAA,GACJuC,GAAc/G,KAAKO,QAAQyF,UAC3BrF,EAAOX,IACXA,MAAKC,QAAQuF,SAAS,YAAYA,SAAS,cAAcR,WAAWgC,KAAK,SAAUC,EAAGrH,GAC/C,UAA/BA,EAAKsH,SAAStD,eACdjD,EAAKwG,UAAUvH,EAAMmH,EAAYE,MAGzCjH,KAAKS,UAET0G,UAAW,SAAUvH,EAAMwH,GACvBxH,EAAOlB,EAAEkB,GAAMyH,KAAK,OAAQ,SAAS7B,SAASpC,GAC9CxD,EAAKO,KAAKC,EAAMgH,EAASA,MAAaE,YAAY,gBAAgBF,GAASA,EAAOG,cAAe,GACjGvH,KAAKwH,YAAY5H,IAErB4H,YAAa,SAAU5H,EAAM6H,EAAKtH,GAC9B,GAAiBJ,GAAbY,EAAOX,IACXJ,GAAOe,EAAKV,QAAQC,KAAKN,GACzBG,EAAaH,EAAKO,KAAKC,GACvBqH,EAAMA,GAAO1H,EAAW2H,WACpBD,IACA7H,EAAKkF,OAAO,sDACR1D,EAAMuG,WAAWF,GACjBG,OAAOC,MACHJ,IAAKA,EACLtH,KAAMA,MACN2H,KAAM,MACNC,SAAU,OACVC,QAAS,SAAU7H,GACfQ,EAAKsH,QAAQ,UAAW,WACpB,OAASC,SAAUtI,EAAKuI,SAE5BvI,EAAKwI,KAAKjI,GACVQ,EAAKsH,QAAQ,UAAW,WACpB,OAASC,SAAUtI,EAAKuI,SAE5BxH,EAAK0H,QAAQ1F,GAAe/C,KAAMA,EAAK,MAE3C0I,MAAO,SAAUC,EAAKC,GAClB7H,EAAK0H,QAAQzF,GACThD,KAAMA,EAAK,GACX4I,OAAQA,EACRD,IAAKA,OAKjB3I,EAAK6F,YAAY,gBAAgB2C,KAAK,gBAAmBX,EAAM,2GAI3EjB,eAAgB,SAAUsB,EAAMlI,GACvBI,KAAKqI,QAAQP,GAAQlI,KAAMA,EAAK,MACjCI,KAAK8H,GAAMlI,EAAK,KAGxB+F,YAAa,SAAUN,GACnB,GAAuCoD,GAAnC9H,EAAOX,KAAMoG,EAAS1H,EAAE2G,EAAEe,OAC1BA,GAAOsC,QAAQ,eAAe,IAAM/H,EAAKV,QAAQ,KAGrDwI,EAAQrC,EAAOpB,SAAS,iCACH,IAAjByD,EAAMnI,SAGNmI,EAAME,GAAG,oBACThI,EAAK6F,eAAe9D,EAAU0D,EAAOwC,QAC9BH,EAAME,GAAG,oBAChBhI,EAAK6F,eAAe9D,EAAU0D,EAAOyC,QAC9BJ,EAAME,GAAG,kBAChBhI,EAAK6F,eAAe/D,EAAQ2D,EAAOwC,QAC5BH,EAAME,GAAG,mBAChBhI,EAAK6F,eAAe/D,EAAQ2D,EAAOyC,WAG3CjD,YAAa,SAAUkD,GACnB,GAAInI,GAAOX,IACX,OAAO,UAAUqF,GACb,GAA0BzF,GAAtBwG,EAAS1H,EAAE2G,EAAEe,OACbA,GAAOsC,QAAQ,eAAe,IAAM/H,EAAKV,QAAQ,KAIjDL,EADAwG,EAAOuC,GAAG,MAAQG,EAAY,SACvB1C,EAAO2C,SAASH,OAEhBxC,EAAO2C,SAASF,OAE3BlI,EAAK6F,eAAesC,EAAWlJ,MAGvCoJ,gBAAiB,SAAUC,EAAUC,EAAcC,GAC/C,GAAIC,GAAY,SAAUC,EAAUC,GAC5B,MAAOA,GAAY,sBAAyBD,EAAW,OAAU,IAClEzI,EAAcZ,KAAKY,YAAa2I,EAAYL,EAAaM,aAAc,GAASL,EAASK,aAAc,EAAOC,EAAkBP,EAAaQ,YAAaC,EAAgBT,EAAaU,UAAWC,EAAkBV,EAASO,YAAaI,EAAgBX,EAASS,SAC1QX,GAASzD,SAAS,yCAA2C5E,GAAayG,KAAK,OAAQ,aAAaA,KAAK,kBAAmBsC,GAAiBG,IAAgBrE,YAAY,cAAgB7E,EAAc,UAAU0G,YAAY,wBAA0B1G,EAAa2I,IAAcI,IAAkBG,GAAexC,YAAY,qBAAuB1G,GAAc2I,IAAcE,IAAoBI,GAAiBzB,KAAKgB,EAAU,kCAAmCK,IAAoBE,IAAkBG,GAAiBlJ,GAAemC,GAAYqG,EAAU,oCAAqCK,IAAoBE,IAAkBG,GAAiBlJ,GAAeI,GAAcoI,EAAU,kCAAmCK,GAAmBE,IAAkBG,GAAiBlJ,GAAemC,GAAYqG,EAAU,mCAAoCK,GAAmBE,IAAkBG,GAAiBlJ,GAAeI,GAAcoI,EAAU,2BAA4BG,GAAa3I,GAAemC,GAAYqG,EAAU,2BAA4BG,GAAa3I,GAAeI,GAAcoI,EAAU,oCAAqCS,IAAoBC,IAAkBH,GAAiB/I,GAAemC,GAAYqG,EAAU,qCAAsCS,IAAoBC,IAAkBH,GAAiB/I,GAAeI,GAAcoI,EAAU,gCAAiCS,GAAmBC,IAAkBH,GAAiB/I,GAAemC,GAAYqG,EAAU,kCAAmCS,GAAmBC,IAAkBH,GAAiB/I,GAAeI,IACziDuI,GAAcE,GAAoBI,GACnCZ,EAASc,WAAW,aAG5BC,iBAAkB,WACd,GAAIrJ,GAAOX,IACXA,MAAKC,QAAQ+E,SAAS,eAAegC,KAAK,WACtC,GAAIiC,GAAWvK,EAAEsB,MAAOkJ,EAAeD,EAASgB,QAAQ5G,GAAW6G,QAAQ/J,KAAKC,GAAO+I,EAAWF,EAASkB,QAAQ9G,GAAW6G,QAAQ/J,KAAKC,EACtI+I,IAGLxI,EAAKqI,gBAAgBC,EAAUC,EAAcC,MAGrDiB,iBAAkB,WACdpK,KAAKC,QAAQ+E,SAAS,eAAeC,UAEzCJ,OAAQ,WACJ,MAAK7E,MAAKC,QAGHD,KAAKC,QAAQ+E,SAAS3B,GAFlB3E,KAIf2L,QAAS,WAAA,GAcDC,GAAqBC,EAAqBC,EAqB1CC,EAA4CC,EAE5CC,EAASC,EAA6DC,EAAqDC,EAEvHC,EAtCJpK,EAAOX,KAAMC,EAAUU,EAAKV,QAAS+F,EAAQ/F,EAAQ+E,SAAS3B,GAAYG,EAAe7C,EAAKC,aAAeI,EAAYgK,EAAY/K,EAAQ+E,SAAS,eAAgBiG,EAAiBD,EAAU1K,OAAQ4K,EAAiB1H,EAAe,QAAU,SAAU2H,EAAYlL,EAAQiL,IACrRvK,GAAKgD,QAAQ6B,SAAS,uBACC,IAAnByF,GACAA,EAAiBjF,EAAM1F,OAAS,EAChC0F,EAAMoF,MAAM,EAAGH,GAAgBI,MAAM,qDAA4D1K,EAAKa,QAAU,QAChHb,EAAKqJ,mBACLgB,EAAY/K,EAAQ+E,SAAS,gBAE7BrE,EAAKqJ,mBAETgB,EAAUhE,KAAK,WACXmE,GAAanL,KAAKwD,EAAe,cAAgB,kBAEjD8G,EAAkB,EAAGC,EAAkB,EAAGC,EAAiB9L,IAC/DsH,EAAMsF,KACFC,SAAU,WACVC,IAAK,IACNN,GAAgB,WACf,GAA0DrM,GAAtDoB,EAAUvB,EAAEsB,MAAOoH,EAASnH,EAAQE,KAAKC,MAE7C,IADAH,EAAQwF,YAAY,qBAChB2B,EAAOwC,UACP/K,EAAOuI,EAAOqE,cAAgBtM,EAAciI,EAAOqE,cAAeN,GAAa,EAC/ElL,EAAQqL,IAAI,WAAY,UAAU9F,SAAS,yBACxC,CAAA,GAAItG,EAAQkI,EAAOvI,MAEtB,MADA2L,GAAiBA,EAAekB,IAAI1L,MACpC,CAEAnB,GAAOM,EAAciI,EAAOvI,KAAMsM,GAItC,MAFAZ,KACAD,GAAmBzL,EACZA,IAEXsM,GAAab,EACTG,EAAqBD,EAAelK,OAAQoK,EAAoBnL,KAAKC,MAAM2L,EAAYV,GAC3FD,EAAeY,MAAM,EAAGX,EAAqB,GAAGa,IAAIJ,EAAgBR,GAAmBhF,MAAMiG,GAAGlB,EAAqB,GAAGa,IAAIJ,EAAgBC,GAAaV,EAAqB,GAAKC,GAC/KC,EAAM,EAAGC,EAA0BpH,EAAe,SAAW,QAASqH,EAAsBrH,EAAe,OAAS,MAAOsH,EAAoBtH,EAAe,cAAgB,eACvJ,IAAvBiH,IACIM,EAAuB/E,EAAM4F,OAAO,WACpC,QAASlN,EAAEsB,MAAMG,KAAKC,QAAawJ,YACpCiC,OACHd,EAAqBG,GAAgBC,EAAYJ,EAAqB,GAAGD,KAE7E7K,EAAQ+E,WAAWsG,IAAIV,EAAyB3K,EAAQ2K,MAA4B5D,KAAK,SAAUC,EAAG6E,GAC/D,UAA/BA,EAAMC,QAAQnI,gBACdkI,EAAME,MAAMnB,GAAuBtL,KAAKC,MAAMmL,GAAO,KACrDA,GAAOmB,EAAMhB,MAGrBnK,EAAKmF,gBACLnF,EAAKuE,gBACLvE,EAAKgD,QAAQ8B,YAAY,uBACzBrE,EAAMX,OAAOuF,GACbrF,EAAK0H,QAAQvF,IAEjBmJ,OAAQ,SAAUrM,EAAMsM,GACpB,GAAiBnM,GAAbY,EAAOX,IACXJ,GAAOe,EAAKV,QAAQC,KAAKN,GACzBG,EAAaH,EAAKO,KAAKC,IAClB8L,GAAWnM,EAAW2J,eAGH,GAApBrJ,UAAUC,SACV4L,EAASnM,EAAW6J,YAAcjL,GAAoBoB,EAAW6J,WAErE7J,EAAW6J,WAAasC,EACpBnM,EAAW6J,UACXhK,EAAK0L,IAAI,WAAY,UAErB1L,EAAK0L,IAAI,WAAY,IAEzB3K,EAAKF,QAAO,KAEhB0L,SAAU,SAAUvM,GAChBI,KAAKiM,OAAOrM,GAAM,IAEtBsM,OAAQ,SAAUtM,GACdI,KAAKiM,OAAOrM,GAAM,IAEtBwM,SAAU,SAAUhF,EAAQiF,EAAKC,GAC7B,GAAI3L,GAAOX,IAOX,OANIsM,GAAYhM,SACZK,EAAKJ,QAAQyF,MAAMuG,OAAOF,EAAK,EAAGjF,GAClCzG,EAAKwG,UAAUmF,EAAalF,GAC5BzG,EAAKyJ,mBACLzJ,EAAKF,QAAO,IAET6L,GAEXxH,OAAQ,SAAUsC,GACdA,EAASA,KACT,IAAIzG,GAAOX,KAAMsM,EAAc5N,EAAE,WAAW8N,SAAS7L,EAAKV,QAC1D,OAAOU,GAAKyL,SAAShF,EAAQzG,EAAKJ,QAAQyF,MAAM1F,OAAQgM,IAE5DG,aAAc,SAAUrF,EAAQsF,GAC5BA,EAAgBhO,EAAEgO,GAClBtF,EAASA,KACT,IAAIzG,GAAOX,KAAMqM,EAAM1L,EAAKgD,QAAQqB,SAAS,WAAW2H,MAAMD,GAAgBJ,EAAc5N,EAAE,WAAW+N,aAAa/N,EAAEgO,GACxH,OAAO/L,GAAKyL,SAAShF,EAAQiF,EAAKC,IAEtCM,YAAa,SAAUxF,EAAQsF,GAC3BA,EAAgBhO,EAAEgO,GAClBtF,EAASA,KACT,IAAIzG,GAAOX,KAAMqM,EAAM1L,EAAKgD,QAAQqB,SAAS,WAAW2H,MAAMD,GAAgBJ,EAAc5N,EAAE,WAAWkO,YAAYlO,EAAEgO,GACvH,OAAO/L,GAAKyL,SAAShF,EAAQiF,EAAM,EAAGC,IAE1CrH,OAAQ,SAAUrF,GACd,GAAIe,GAAOX,IAaX,OAZAJ,GAAOe,EAAKgD,QAAQzD,KAAKN,GACrBA,EAAKU,SACLc,EAAM6E,QAAQrG,GACdA,EAAKoH,KAAK,SAAUqF,EAAKpM,GACrBU,EAAKJ,QAAQyF,MAAMuG,OAAO5L,EAAKgD,QAAQqB,SAAS,WAAW2H,MAAM1M,GAAU,GAC3EvB,EAAEuB,GAASgF,WAEftE,EAAKyJ,mBACDzJ,EAAKJ,QAAQyF,MAAM1F,QACnBK,EAAKF,QAAO,IAGbE,GAEX9B,KAAMY,EAAqB,QAAQ,GACnCuC,IAAKvC,EAAqB,OAC1BqC,IAAKrC,EAAqB,QAE9B4B,GAAGwL,OAAOvJ,GACNpC,GACAgK,eAAgB,SAChBJ,kBAAmB,eACnBF,wBAAyB,QACzBC,oBAAqB,MACrBiC,yBAA0B,SAE1B7L,GACAiK,eAAgB,QAChBJ,kBAAmB,cACnBF,wBAAyB,SACzBC,oBAAqB,OACrBiC,yBAA0B,SAmB9BpM,EAAaqM,WACTC,MAAO,SAAU5G,GACbpG,KAAKmB,WAAW6L,MAAM5G,IAE1BK,KAAM,SAAUwG,EAAO7G,GACdpG,KAAKkN,UACNlN,KAAKgN,MAAM5G,GACXpG,KAAKkN,SAAU,GAEdlN,KAAKmB,WAAWiF,QACjBpG,KAAKmB,WAAW6L,MAAM5G,GAE1BpG,KAAKmB,WAAWsF,KAAKwG,IAEzBvH,IAAK,WACD1F,KAAKmB,WAAWuE,MAChB1F,KAAKkN,SAAU,GAEnBjH,QAAS,WACLjG,KAAKmB,WAAW8E,UAChBjG,KAAKmB,WAAanB,KAAKc,SAAWd,KAAKa,MAAQ,MAEnD0F,WAAY,WACR,MAAOvG,MAAKmB,WAAWsD,UAE3B9C,YAAa,SAAUJ,GACnB,GAAIZ,GAAOX,IACX,OAAOtB,GAAE,iDAAoDiC,EAAKC,YAAc,wBAAyB0K,IAAI3K,EAAKiK,wBAAyBrJ,EAAOZ,EAAKiK,6BAE3J/I,OAAQ,SAAUwD,GACd,GAAI1E,GAAOX,KAAMiJ,EAAWvK,EAAE2G,EAAEC,eAAgB4D,EAAeD,EAASL,OAAQO,EAAWF,EAASJ,OAAQsE,EAAqBjE,EAAa/I,KAAKC,GAAOgN,EAAiBjE,EAAShJ,KAAKC,GAAOiN,EAAe/N,SAAS4J,EAAa,GAAG8C,MAAMrL,EAAKkK,qBAAsB,IAAKyC,EAAehO,SAAS6J,EAAS,GAAG6C,MAAMrL,EAAKkK,qBAAsB,IAAM1B,EAAS,GAAGxI,EAAKmK,mBAAqB7B,EAAS,GAAGtI,EAAKmK,mBAAoBK,EAAY7L,SAASqB,EAAKG,SAASwK,IAAI3K,EAAKuK,gBAAiB,IAAKqC,EAAO,SAAU1N,GAC/e,GAAI2N,GAAMlO,SAASO,EAAO,GAC1B,QAAQb,EAAYa,GAAS2N,EAAMrC,EAAYqC,EAAM,MAAQ,GAC9DC,EAAcF,EAAKJ,EAAmBnL,KAAM0L,EAAcH,EAAKJ,EAAmBrL,MAAQwL,EAAeD,EAAcM,EAAcJ,EAAKH,EAAepL,KAAM4L,EAAcL,EAAKH,EAAetL,MAAQwL,EAAeD,CAC/N1M,GAAKuI,aAAeA,EACpBvI,EAAKwI,SAAWA,EAChBxI,EAAKiG,aAAerH,KAAKyC,IAAIsL,EAAeK,EAAaN,EAAeK,GACxE/M,EAAKkN,aAAetO,KAAKuC,IAAIuL,EAAeI,EAAaH,EAAeM,IAE5E7L,KAAM,WACF,MAAO/B,MAAK4G,cAEhB3E,KAAM,WACF,MAAOjC,MAAK6N,cAEhBzL,MAAO,SAAUiD,GAAV,GAIKyI,GAA4B5E,EAAgCC,EAA4BgE,EAA8CC,EAAsCW,EAAqGC,EAAyKC,EAH9btN,EAAOX,KAAMiJ,EAAWvK,EAAE2G,EAAEC,eAAgBzE,EAAQF,EAAKE,KAiB7D,OAhBAA,GAAMgE,SAASG,SAAS,uBAAuBC,SAC3CI,EAAEc,UAAY/E,EAAMkB,KAAK4L,MACrBJ,EAAgBzI,EAAEkG,SAAUrC,EAAeD,EAASL,OAAQO,EAAWF,EAASJ,OAAQsE,EAAqBjE,EAAa/I,KAAKC,GAAOgN,EAAiBjE,EAAShJ,KAAKC,GAAO2N,EAAsBD,EAAgBxO,SAAS4J,EAAa,GAAG8C,MAAMrL,EAAKkK,qBAAsB,IAAKmD,EAAkB1O,SAAS6J,EAAS,GAAG6C,MAAMrL,EAAKkK,qBAAsB,IAAM1B,EAAS,GAAGxI,EAAKmK,mBAAqBgD,EAAgB7E,EAAS,GAAGtI,EAAKmK,mBAAoBmD,EAAkBtN,EAAKG,SAASkE,SAAS3B,GAAWuI,OAAO,WACjf,MAAO1M,GAAQR,EAAEsB,MAAMG,KAAKC,GAAMvB,QACnCyB,SACFpB,EAAQiO,EAAmBtO,OAASoP,EAAkB,KACnD/O,EAAQiO,EAAmBtO,OAC3BoP,IAEJd,EAAmBtO,KAAOkP,EAAsB,QAE/C7O,EAAQkO,EAAevO,OAASoP,EAAkB,KACnDb,EAAevO,KAAOmP,EAAkB,MAE5CnN,EAAMJ,QAAO,KAEV,KAGjB4B,OAAOjB,MAAMwG,QACRvF,OAAOjB,OACE,kBAAV3C,SAAwBA,OAAO0P,IAAM1P,OAAS,SAAU2P,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.splitter.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.splitter', ['kendo.resizable'], f);\n}(function () {\n    var __meta__ = {\n        id: 'splitter',\n        name: 'Splitter',\n        category: 'web',\n        description: 'The Splitter widget provides an easy way to create a dynamic layout of resizable and collapsible panes.',\n        depends: ['resizable']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, keys = kendo.keys, extend = $.extend, proxy = $.proxy, Widget = ui.Widget, pxUnitsRegex = /^\\d+(\\.\\d+)?px$/i, percentageUnitsRegex = /^\\d+(\\.\\d+)?%$/i, NS = '.kendoSplitter', EXPAND = 'expand', COLLAPSE = 'collapse', CONTENTLOAD = 'contentLoad', ERROR = 'error', RESIZE = 'resize', LAYOUTCHANGE = 'layoutChange', HORIZONTAL = 'horizontal', VERTICAL = 'vertical', MOUSEENTER = 'mouseenter', CLICK = 'click', PANE = 'pane', MOUSELEAVE = 'mouseleave', FOCUSED = 'k-state-focused', KPANE = 'k-' + PANE, PANECLASS = '.' + KPANE;\n        function isPercentageSize(size) {\n            return percentageUnitsRegex.test(size);\n        }\n        function isPixelSize(size) {\n            return pxUnitsRegex.test(size) || /^\\d+$/.test(size);\n        }\n        function isFluid(size) {\n            return !isPercentageSize(size) && !isPixelSize(size);\n        }\n        function calculateSize(size, total) {\n            var output = parseInt(size, 10);\n            if (isPercentageSize(size)) {\n                output = Math.floor(output * total / 100);\n            }\n            return output;\n        }\n        function panePropertyAccessor(propertyName, triggersResize) {\n            return function (pane, value) {\n                var paneConfig = this.element.find(pane).data(PANE);\n                if (arguments.length == 1) {\n                    return paneConfig[propertyName];\n                }\n                paneConfig[propertyName] = value;\n                if (triggersResize) {\n                    var splitter = this.element.data('kendo' + this.options.name);\n                    splitter.resize(true);\n                }\n            };\n        }\n        var Splitter = Widget.extend({\n            init: function (element, options) {\n                var that = this, isHorizontal;\n                Widget.fn.init.call(that, element, options);\n                that.wrapper = that.element;\n                if (that.options.orientation) {\n                    isHorizontal = that.options.orientation.toLowerCase() != VERTICAL;\n                }\n                that.orientation = isHorizontal ? HORIZONTAL : VERTICAL;\n                that._dimension = isHorizontal ? 'width' : 'height';\n                that._keys = {\n                    decrease: isHorizontal ? keys.LEFT : keys.UP,\n                    increase: isHorizontal ? keys.RIGHT : keys.DOWN\n                };\n                that._resizeStep = 10;\n                that._marker = kendo.guid().substring(0, 8);\n                that._initPanes();\n                that.resizing = new PaneResizing(that);\n                that.element.triggerHandler('init' + NS);\n            },\n            events: [\n                EXPAND,\n                COLLAPSE,\n                CONTENTLOAD,\n                ERROR,\n                RESIZE,\n                LAYOUTCHANGE\n            ],\n            _addOverlays: function () {\n                this._panes().append('<div class=\\'k-splitter-overlay k-overlay\\' />');\n            },\n            _removeOverlays: function () {\n                this._panes().children('.k-splitter-overlay').remove();\n            },\n            _attachEvents: function () {\n                var that = this, orientation = that.options.orientation;\n                that.element.children('.k-splitbar-draggable-' + orientation).on('keydown' + NS, proxy(that._keydown, that)).on('mousedown' + NS, function (e) {\n                    e.currentTarget.focus();\n                }).on('focus' + NS, function (e) {\n                    $(e.currentTarget).addClass(FOCUSED);\n                }).on('blur' + NS, function (e) {\n                    $(e.currentTarget).removeClass(FOCUSED);\n                    if (that.resizing) {\n                        that.resizing.end();\n                    }\n                }).on(MOUSEENTER + NS, function () {\n                    $(this).addClass('k-splitbar-' + that.orientation + '-hover');\n                }).on(MOUSELEAVE + NS, function () {\n                    $(this).removeClass('k-splitbar-' + that.orientation + '-hover');\n                }).on('mousedown' + NS, proxy(that._addOverlays, that)).end().children('.k-splitbar').on('dblclick' + NS, proxy(that._togglePane, that)).children('.k-collapse-next, .k-collapse-prev').on(CLICK + NS, that._arrowClick(COLLAPSE)).end().children('.k-expand-next, .k-expand-prev').on(CLICK + NS, that._arrowClick(EXPAND)).end().end();\n                $(window).on('resize' + NS + that._marker, proxy(that.resize, that, false));\n                $(document).on('mouseup' + NS + that._marker, proxy(that._removeOverlays, that));\n            },\n            _detachEvents: function () {\n                var that = this;\n                that.element.children('.k-splitbar-draggable-' + that.orientation).off(NS).end().children('.k-splitbar').off('dblclick' + NS).children('.k-collapse-next, .k-collapse-prev, .k-expand-next, .k-expand-prev').off(NS);\n                $(window).off(NS + that._marker);\n                $(document).off(NS + that._marker);\n            },\n            options: {\n                name: 'Splitter',\n                orientation: HORIZONTAL,\n                panes: []\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this._detachEvents();\n                if (this.resizing) {\n                    this.resizing.destroy();\n                }\n                kendo.destroy(this.element);\n                this.wrapper = this.element = null;\n            },\n            _keydown: function (e) {\n                var that = this, key = e.keyCode, resizing = that.resizing, target = $(e.currentTarget), navigationKeys = that._keys, increase = key === navigationKeys.increase, decrease = key === navigationKeys.decrease, pane;\n                if (increase || decrease) {\n                    if (e.ctrlKey) {\n                        pane = target[decrease ? 'next' : 'prev']();\n                        if (resizing && resizing.isResizing()) {\n                            resizing.end();\n                        }\n                        if (!pane[that._dimension]()) {\n                            that._triggerAction(EXPAND, pane);\n                        } else {\n                            that._triggerAction(COLLAPSE, target[decrease ? 'prev' : 'next']());\n                        }\n                    } else if (resizing) {\n                        resizing.move((decrease ? -1 : 1) * that._resizeStep, target);\n                    }\n                    e.preventDefault();\n                } else if (key === keys.HOME) {\n                    resizing.move(-resizing._maxPosition, target);\n                    e.preventDefault();\n                } else if (key === keys.END) {\n                    resizing.move(resizing._maxPosition, target);\n                    e.preventDefault();\n                } else if (key === keys.ENTER && resizing) {\n                    resizing.end();\n                    e.preventDefault();\n                }\n            },\n            _initPanes: function () {\n                var panesConfig = this.options.panes || [];\n                var that = this;\n                this.element.addClass('k-widget').addClass('k-splitter').children().each(function (i, pane) {\n                    if (pane.nodeName.toLowerCase() != 'script') {\n                        that._initPane(pane, panesConfig[i]);\n                    }\n                });\n                this.resize();\n            },\n            _initPane: function (pane, config) {\n                pane = $(pane).attr('role', 'group').addClass(KPANE);\n                pane.data(PANE, config ? config : {}).toggleClass('k-scrollable', config ? config.scrollable !== false : true);\n                this.ajaxRequest(pane);\n            },\n            ajaxRequest: function (pane, url, data) {\n                var that = this, paneConfig;\n                pane = that.element.find(pane);\n                paneConfig = pane.data(PANE);\n                url = url || paneConfig.contentUrl;\n                if (url) {\n                    pane.append('<span class=\\'k-icon k-i-loading k-pane-loading\\' />');\n                    if (kendo.isLocalUrl(url)) {\n                        jQuery.ajax({\n                            url: url,\n                            data: data || {},\n                            type: 'GET',\n                            dataType: 'html',\n                            success: function (data) {\n                                that.angular('cleanup', function () {\n                                    return { elements: pane.get() };\n                                });\n                                pane.html(data);\n                                that.angular('compile', function () {\n                                    return { elements: pane.get() };\n                                });\n                                that.trigger(CONTENTLOAD, { pane: pane[0] });\n                            },\n                            error: function (xhr, status) {\n                                that.trigger(ERROR, {\n                                    pane: pane[0],\n                                    status: status,\n                                    xhr: xhr\n                                });\n                            }\n                        });\n                    } else {\n                        pane.removeClass('k-scrollable').html('<iframe src=\\'' + url + '\\' frameborder=\\'0\\' class=\\'k-content-frame\\'>' + 'This page requires frames in order to show content' + '</iframe>');\n                    }\n                }\n            },\n            _triggerAction: function (type, pane) {\n                if (!this.trigger(type, { pane: pane[0] })) {\n                    this[type](pane[0]);\n                }\n            },\n            _togglePane: function (e) {\n                var that = this, target = $(e.target), arrow;\n                if (target.closest('.k-splitter')[0] != that.element[0]) {\n                    return;\n                }\n                arrow = target.children('.k-icon:not(.k-resize-handle)');\n                if (arrow.length !== 1) {\n                    return;\n                }\n                if (arrow.is('.k-collapse-prev')) {\n                    that._triggerAction(COLLAPSE, target.prev());\n                } else if (arrow.is('.k-collapse-next')) {\n                    that._triggerAction(COLLAPSE, target.next());\n                } else if (arrow.is('.k-expand-prev')) {\n                    that._triggerAction(EXPAND, target.prev());\n                } else if (arrow.is('.k-expand-next')) {\n                    that._triggerAction(EXPAND, target.next());\n                }\n            },\n            _arrowClick: function (arrowType) {\n                var that = this;\n                return function (e) {\n                    var target = $(e.target), pane;\n                    if (target.closest('.k-splitter')[0] != that.element[0]) {\n                        return;\n                    }\n                    if (target.is('.k-' + arrowType + '-prev')) {\n                        pane = target.parent().prev();\n                    } else {\n                        pane = target.parent().next();\n                    }\n                    that._triggerAction(arrowType, pane);\n                };\n            },\n            _updateSplitBar: function (splitbar, previousPane, nextPane) {\n                var catIconIf = function (iconType, condition) {\n                        return condition ? '<div class=\\'k-icon ' + iconType + '\\' />' : '';\n                    }, orientation = this.orientation, draggable = previousPane.resizable !== false && nextPane.resizable !== false, prevCollapsible = previousPane.collapsible, prevCollapsed = previousPane.collapsed, nextCollapsible = nextPane.collapsible, nextCollapsed = nextPane.collapsed;\n                splitbar.addClass('k-splitbar k-state-default k-splitbar-' + orientation).attr('role', 'separator').attr('aria-expanded', !(prevCollapsed || nextCollapsed)).removeClass('k-splitbar-' + orientation + '-hover').toggleClass('k-splitbar-draggable-' + orientation, draggable && !prevCollapsed && !nextCollapsed).toggleClass('k-splitbar-static-' + orientation, !draggable && !prevCollapsible && !nextCollapsible).html(catIconIf('k-collapse-prev k-i-arrow-60-up', prevCollapsible && !prevCollapsed && !nextCollapsed && orientation == VERTICAL) + catIconIf('k-collapse-prev k-i-arrow-60-left', prevCollapsible && !prevCollapsed && !nextCollapsed && orientation == HORIZONTAL) + catIconIf('k-expand-prev k-i-arrow-60-down', prevCollapsible && prevCollapsed && !nextCollapsed && orientation == VERTICAL) + catIconIf('k-expand-prev k-i-arrow-60-right', prevCollapsible && prevCollapsed && !nextCollapsed && orientation == HORIZONTAL) + catIconIf('k-resize-handle k-i-hbar', draggable && orientation == VERTICAL) + catIconIf('k-resize-handle k-i-vbar', draggable && orientation == HORIZONTAL) + catIconIf('k-collapse-next k-i-arrow-60-down', nextCollapsible && !nextCollapsed && !prevCollapsed && orientation == VERTICAL) + catIconIf('k-collapse-next k-i-arrow-60-right', nextCollapsible && !nextCollapsed && !prevCollapsed && orientation == HORIZONTAL) + catIconIf('k-expand-next k-i-arrow-60-up', nextCollapsible && nextCollapsed && !prevCollapsed && orientation == VERTICAL) + catIconIf('k-expand-next k-i-arrow-60-left', nextCollapsible && nextCollapsed && !prevCollapsed && orientation == HORIZONTAL));\n                if (!draggable && !prevCollapsible && !nextCollapsible) {\n                    splitbar.removeAttr('tabindex');\n                }\n            },\n            _updateSplitBars: function () {\n                var that = this;\n                this.element.children('.k-splitbar').each(function () {\n                    var splitbar = $(this), previousPane = splitbar.prevAll(PANECLASS).first().data(PANE), nextPane = splitbar.nextAll(PANECLASS).first().data(PANE);\n                    if (!nextPane) {\n                        return;\n                    }\n                    that._updateSplitBar(splitbar, previousPane, nextPane);\n                });\n            },\n            _removeSplitBars: function () {\n                this.element.children('.k-splitbar').remove();\n            },\n            _panes: function () {\n                if (!this.element) {\n                    return $();\n                }\n                return this.element.children(PANECLASS);\n            },\n            _resize: function () {\n                var that = this, element = that.element, panes = element.children(PANECLASS), isHorizontal = that.orientation == HORIZONTAL, splitBars = element.children('.k-splitbar'), splitBarsCount = splitBars.length, sizingProperty = isHorizontal ? 'width' : 'height', totalSize = element[sizingProperty]();\n                that.wrapper.addClass('k-splitter-resizing');\n                if (splitBarsCount === 0) {\n                    splitBarsCount = panes.length - 1;\n                    panes.slice(0, splitBarsCount).after('<div tabindex=\\'0\\' class=\\'k-splitbar\\' data-marker=\\'' + that._marker + '\\' />');\n                    that._updateSplitBars();\n                    splitBars = element.children('.k-splitbar');\n                } else {\n                    that._updateSplitBars();\n                }\n                splitBars.each(function () {\n                    totalSize -= this[isHorizontal ? 'offsetWidth' : 'offsetHeight'];\n                });\n                var sizedPanesWidth = 0, sizedPanesCount = 0, freeSizedPanes = $();\n                panes.css({\n                    position: 'absolute',\n                    top: 0\n                })[sizingProperty](function () {\n                    var element = $(this), config = element.data(PANE) || {}, size;\n                    element.removeClass('k-state-collapsed');\n                    if (config.collapsed) {\n                        size = config.collapsedSize ? calculateSize(config.collapsedSize, totalSize) : 0;\n                        element.css('overflow', 'hidden').addClass('k-state-collapsed');\n                    } else if (isFluid(config.size)) {\n                        freeSizedPanes = freeSizedPanes.add(this);\n                        return;\n                    } else {\n                        size = calculateSize(config.size, totalSize);\n                    }\n                    sizedPanesCount++;\n                    sizedPanesWidth += size;\n                    return size;\n                });\n                totalSize -= sizedPanesWidth;\n                var freeSizePanesCount = freeSizedPanes.length, freeSizePaneWidth = Math.floor(totalSize / freeSizePanesCount);\n                freeSizedPanes.slice(0, freeSizePanesCount - 1).css(sizingProperty, freeSizePaneWidth).end().eq(freeSizePanesCount - 1).css(sizingProperty, totalSize - (freeSizePanesCount - 1) * freeSizePaneWidth);\n                var sum = 0, alternateSizingProperty = isHorizontal ? 'height' : 'width', positioningProperty = isHorizontal ? 'left' : 'top', sizingDomProperty = isHorizontal ? 'offsetWidth' : 'offsetHeight';\n                if (freeSizePanesCount === 0) {\n                    var lastNonCollapsedPane = panes.filter(function () {\n                        return !($(this).data(PANE) || {}).collapsed;\n                    }).last();\n                    lastNonCollapsedPane[sizingProperty](totalSize + lastNonCollapsedPane[0][sizingDomProperty]);\n                }\n                element.children().css(alternateSizingProperty, element[alternateSizingProperty]()).each(function (i, child) {\n                    if (child.tagName.toLowerCase() != 'script') {\n                        child.style[positioningProperty] = Math.floor(sum) + 'px';\n                        sum += child[sizingDomProperty];\n                    }\n                });\n                that._detachEvents();\n                that._attachEvents();\n                that.wrapper.removeClass('k-splitter-resizing');\n                kendo.resize(panes);\n                that.trigger(LAYOUTCHANGE);\n            },\n            toggle: function (pane, expand) {\n                var that = this, paneConfig;\n                pane = that.element.find(pane);\n                paneConfig = pane.data(PANE);\n                if (!expand && !paneConfig.collapsible) {\n                    return;\n                }\n                if (arguments.length == 1) {\n                    expand = paneConfig.collapsed === undefined ? false : paneConfig.collapsed;\n                }\n                paneConfig.collapsed = !expand;\n                if (paneConfig.collapsed) {\n                    pane.css('overflow', 'hidden');\n                } else {\n                    pane.css('overflow', '');\n                }\n                that.resize(true);\n            },\n            collapse: function (pane) {\n                this.toggle(pane, false);\n            },\n            expand: function (pane) {\n                this.toggle(pane, true);\n            },\n            _addPane: function (config, idx, paneElement) {\n                var that = this;\n                if (paneElement.length) {\n                    that.options.panes.splice(idx, 0, config);\n                    that._initPane(paneElement, config);\n                    that._removeSplitBars();\n                    that.resize(true);\n                }\n                return paneElement;\n            },\n            append: function (config) {\n                config = config || {};\n                var that = this, paneElement = $('<div />').appendTo(that.element);\n                return that._addPane(config, that.options.panes.length, paneElement);\n            },\n            insertBefore: function (config, referencePane) {\n                referencePane = $(referencePane);\n                config = config || {};\n                var that = this, idx = that.wrapper.children('.k-pane').index(referencePane), paneElement = $('<div />').insertBefore($(referencePane));\n                return that._addPane(config, idx, paneElement);\n            },\n            insertAfter: function (config, referencePane) {\n                referencePane = $(referencePane);\n                config = config || {};\n                var that = this, idx = that.wrapper.children('.k-pane').index(referencePane), paneElement = $('<div />').insertAfter($(referencePane));\n                return that._addPane(config, idx + 1, paneElement);\n            },\n            remove: function (pane) {\n                var that = this;\n                pane = that.wrapper.find(pane);\n                if (pane.length) {\n                    kendo.destroy(pane);\n                    pane.each(function (idx, element) {\n                        that.options.panes.splice(that.wrapper.children('.k-pane').index(element), 1);\n                        $(element).remove();\n                    });\n                    that._removeSplitBars();\n                    if (that.options.panes.length) {\n                        that.resize(true);\n                    }\n                }\n                return that;\n            },\n            size: panePropertyAccessor('size', true),\n            min: panePropertyAccessor('min'),\n            max: panePropertyAccessor('max')\n        });\n        ui.plugin(Splitter);\n        var verticalDefaults = {\n            sizingProperty: 'height',\n            sizingDomProperty: 'offsetHeight',\n            alternateSizingProperty: 'width',\n            positioningProperty: 'top',\n            mousePositioningProperty: 'pageY'\n        };\n        var horizontalDefaults = {\n            sizingProperty: 'width',\n            sizingDomProperty: 'offsetWidth',\n            alternateSizingProperty: 'height',\n            positioningProperty: 'left',\n            mousePositioningProperty: 'pageX'\n        };\n        function PaneResizing(splitter) {\n            var that = this, orientation = splitter.orientation;\n            that.owner = splitter;\n            that._element = splitter.element;\n            that.orientation = orientation;\n            extend(that, orientation === HORIZONTAL ? horizontalDefaults : verticalDefaults);\n            that._resizable = new kendo.ui.Resizable(splitter.element, {\n                orientation: orientation,\n                handle: '.k-splitbar-draggable-' + orientation + '[data-marker=' + splitter._marker + ']',\n                hint: proxy(that._createHint, that),\n                start: proxy(that._start, that),\n                max: proxy(that._max, that),\n                min: proxy(that._min, that),\n                invalidClass: 'k-restricted-size-' + orientation,\n                resizeend: proxy(that._stop, that)\n            });\n        }\n        PaneResizing.prototype = {\n            press: function (target) {\n                this._resizable.press(target);\n            },\n            move: function (delta, target) {\n                if (!this.pressed) {\n                    this.press(target);\n                    this.pressed = true;\n                }\n                if (!this._resizable.target) {\n                    this._resizable.press(target);\n                }\n                this._resizable.move(delta);\n            },\n            end: function () {\n                this._resizable.end();\n                this.pressed = false;\n            },\n            destroy: function () {\n                this._resizable.destroy();\n                this._resizable = this._element = this.owner = null;\n            },\n            isResizing: function () {\n                return this._resizable.resizing;\n            },\n            _createHint: function (handle) {\n                var that = this;\n                return $('<div class=\\'k-ghost-splitbar k-ghost-splitbar-' + that.orientation + ' k-state-default\\' />').css(that.alternateSizingProperty, handle[that.alternateSizingProperty]());\n            },\n            _start: function (e) {\n                var that = this, splitbar = $(e.currentTarget), previousPane = splitbar.prev(), nextPane = splitbar.next(), previousPaneConfig = previousPane.data(PANE), nextPaneConfig = nextPane.data(PANE), prevBoundary = parseInt(previousPane[0].style[that.positioningProperty], 10), nextBoundary = parseInt(nextPane[0].style[that.positioningProperty], 10) + nextPane[0][that.sizingDomProperty] - splitbar[0][that.sizingDomProperty], totalSize = parseInt(that._element.css(that.sizingProperty), 10), toPx = function (value) {\n                        var val = parseInt(value, 10);\n                        return (isPixelSize(value) ? val : totalSize * val / 100) || 0;\n                    }, prevMinSize = toPx(previousPaneConfig.min), prevMaxSize = toPx(previousPaneConfig.max) || nextBoundary - prevBoundary, nextMinSize = toPx(nextPaneConfig.min), nextMaxSize = toPx(nextPaneConfig.max) || nextBoundary - prevBoundary;\n                that.previousPane = previousPane;\n                that.nextPane = nextPane;\n                that._maxPosition = Math.min(nextBoundary - nextMinSize, prevBoundary + prevMaxSize);\n                that._minPosition = Math.max(prevBoundary + prevMinSize, nextBoundary - nextMaxSize);\n            },\n            _max: function () {\n                return this._maxPosition;\n            },\n            _min: function () {\n                return this._minPosition;\n            },\n            _stop: function (e) {\n                var that = this, splitbar = $(e.currentTarget), owner = that.owner;\n                owner._panes().children('.k-splitter-overlay').remove();\n                if (e.keyCode !== kendo.keys.ESC) {\n                    var ghostPosition = e.position, previousPane = splitbar.prev(), nextPane = splitbar.next(), previousPaneConfig = previousPane.data(PANE), nextPaneConfig = nextPane.data(PANE), previousPaneNewSize = ghostPosition - parseInt(previousPane[0].style[that.positioningProperty], 10), nextPaneNewSize = parseInt(nextPane[0].style[that.positioningProperty], 10) + nextPane[0][that.sizingDomProperty] - ghostPosition - splitbar[0][that.sizingDomProperty], fluidPanesCount = that._element.children(PANECLASS).filter(function () {\n                            return isFluid($(this).data(PANE).size);\n                        }).length;\n                    if (!isFluid(previousPaneConfig.size) || fluidPanesCount > 1) {\n                        if (isFluid(previousPaneConfig.size)) {\n                            fluidPanesCount--;\n                        }\n                        previousPaneConfig.size = previousPaneNewSize + 'px';\n                    }\n                    if (!isFluid(nextPaneConfig.size) || fluidPanesCount > 1) {\n                        nextPaneConfig.size = nextPaneNewSize + 'px';\n                    }\n                    owner.resize(true);\n                }\n                return false;\n            }\n        };\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}