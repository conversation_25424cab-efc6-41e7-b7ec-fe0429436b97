/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("util/text-metrics.min",["kendo.core.min"],t)}(function(){!function(t){function e(t){return(t+"").replace(r,l)}function i(t){var e,i=[];for(e in t)i.push(e+t[e]);return i.sort().join("")}function a(t){var e,i=2166136261;for(e=0;e<t.length;++e)i+=(i<<1)+(i<<4)+(i<<7)+(i<<8)+(i<<24),i^=t.charCodeAt(e);return i>>>0}function n(){return{width:0,height:0,baseline:0}}function o(t,e,i){return d.current.measure(t,e,i)}var s,r,l,h,c,d;window.kendo.util=window.kendo.util||{},s=kendo.Class.extend({init:function(t){this._size=t,this._length=0,this._map={}},put:function(t,e){var i=this._map,a={key:t,value:e};i[t]=a,this._head?(this._tail.newer=a,a.older=this._tail,this._tail=a):this._head=this._tail=a,this._length>=this._size?(i[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(t){var e=this._map[t];if(e)return e===this._head&&e!==this._tail&&(this._head=e.newer,this._head.older=null),e!==this._tail&&(e.older&&(e.older.newer=e.newer,e.newer.older=e.older),e.older=this._tail,e.newer=null,this._tail.newer=e,this._tail=e),e.value}}),r=/\r?\n|\r|\t/g,l=" ",h={baselineMarkerSize:1},"undefined"!=typeof document&&(c=document.createElement("div"),c.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),d=kendo.Class.extend({init:function(e){this._cache=new s(1e3),this.options=t.extend({},h,e)},measure:function(t,o,s){var r,l,h,d,u,p,f,v,m;if(void 0===s&&(s={}),!t)return n();if(r=i(o),l=a(t+r),h=this._cache.get(l))return h;d=n(),u=s.box||c,p=this._baselineMarker().cloneNode(!1);for(f in o)v=o[f],void 0!==v&&(u.style[f]=v);return m=s.normalizeText!==!1?e(t):t+"",u.textContent=m,u.appendChild(p),document.body.appendChild(u),m.length&&(d.width=u.offsetWidth-this.options.baselineMarkerSize,d.height=u.offsetHeight,d.baseline=p.offsetTop+this.options.baselineMarkerSize),d.width>0&&d.height>0&&this._cache.put(l,d),u.parentNode.removeChild(u),d},_baselineMarker:function(){var t=document.createElement("div");return t.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",t}}),d.current=new d,kendo.deepExtend(kendo.util,{LRUCache:s,TextMetrics:d,measureText:o,objectKey:i,hashKey:a,normalizeText:e})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/stock/kendo-stock-chart.min",["kendo.dataviz.chart.min"],t)}(function(){!function(){function t(t,e){var i=document.createElement("div");return i.className=t,e&&(i.style.cssText=e),i}function e(){}function i(t){return e.prototype=t,new e}var a,n,o,s,r,l,h,c,d,u,p,f,v,m,g,_;window.kendo.dataviz=window.kendo.dataviz||{},a=kendo.dataviz,n=a.elementStyles,o=a.deepExtend,s=a.toTime,r=a.constants,l=a.Chart,h=kendo.drawing,c=h.Animation.extend({setup:function(){this._initialOpacity=parseFloat(n(this.element,"opacity").opacity)},step:function(t){n(this.element,{opacity:a.interpolateValue(this._initialOpacity,0,t)+""})},abort:function(){h.Animation.fn.abort.call(this),n(this.element,{display:"none",opacity:this._initialOpacity+""})},cancel:function(){h.Animation.fn.abort.call(this),n(this.element,{opacity:this._initialOpacity+""})}}),d=a.Class.extend({init:function(t,e,i){this.options=o({},this.options,i),this.container=t,this.chartService=e;var a=n(t,["paddingLeft","paddingTop"]);this.chartPadding={top:a.paddingTop,left:a.paddingLeft},this.createElements(),t.appendChild(this.element)},createElements:function(){var e=this.element=t("k-navigator-hint","display: none; position: absolute; top: 1px; left: 1px;"),i=this.tooltip=t("k-tooltip k-chart-tooltip"),a=this.scroll=t("k-scroll");i.innerHTML="&nbsp;",e.appendChild(i),e.appendChild(a)},show:function(t,e,i){var o,r=this,l=r.element,h=r.options,c=r.scroll,d=r.tooltip,u=a.toDate(s(t)+s(e-t)/2),p=.4*i.width(),f=i.center().x-p,v=i.center().x,m=v-f,g=h.max-h.min,_=m/g,b=u-h.min,y=this.chartService.intl.format(h.format,t,e),x=a.getTemplate(h);this.clearHideTimeout(),this._visible||(n(l,{visibility:"hidden",display:"block"}),this._visible=!0),x&&(y=x({from:t,to:e})),d.innerHTML=y,n(d,{left:i.center().x-d.offsetWidth/2,top:i.y1}),o=n(d,["marginTop","borderTopWidth","height"]),n(c,{width:p,left:f+b*_,top:i.y1+o.marginTop+o.borderTopWidth+o.height/2}),n(l,{visibility:"visible"})},clearHideTimeout:function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this._hideAnimation&&this._hideAnimation.cancel()},hide:function(){var t=this;this.clearHideTimeout(),this._hideTimeout=setTimeout(function(){t._visible=!1,t._hideAnimation=new c(t.element),t._hideAnimation.setup(),t._hideAnimation.play()},this.options.hideDelay)},destroy:function(){this.clearHideTimeout(),this.container&&this.container.removeChild(this.element),delete this.container,delete this.chartService,delete this.element,delete this.tooltip,delete this.scroll}}),a.setDefaultOptions(d,{format:"{0:d} - {1:d}",hideDelay:500}),u="_navigator",p=u,f={NAVIGATOR_AXIS:p,NAVIGATOR_PANE:u},v=3,m=a.Class.extend({init:function(t){var e,i,n;this.chart=t,e=this.options=o({},this.options,t.options.navigator),i=e.select,i&&(i.from=this.parseDate(i.from),i.to=this.parseDate(i.to)),a.defined(e.hint.visible)||(e.hint.visible=e.visible),this.chartObserver=new a.InstanceObserver(this,(n={},n[r.DRAG]="_drag",n[r.DRAG_END]="_dragEnd",n[r.ZOOM]="_zoom",n[r.ZOOM_END]="_zoomEnd",n)),t.addObserver(this.chartObserver)},parseDate:function(t){return a.parseDate(this.chart.chartService.intl,t)},clean:function(){this.selection&&(this.selection.destroy(),this.selection=null),this.hint&&(this.hint.destroy(),this.hint=null)},destroy:function(){this.chart&&(this.chart.removeObserver(this.chartObserver),delete this.chart),this.clean()},redraw:function(){this._redrawSelf(),this.initSelection()},initSelection:function(){var t=this,e=t.chart,n=t.options,o=this.mainAxis(),s=o.roundedRange(),r=s.min,l=s.max,h=n.select,c=h.from,u=h.to,p=h.mousewheel,f=i(o);0!==o.categoriesCount()&&(this.clean(),f.box=o.box,this.selection=new a.Selection(e,f,{min:r,max:l,from:c||r,to:u||l,mousewheel:a.valueOrDefault(p,{zoom:"left"}),visible:n.visible},new a.InstanceObserver(this,{selectStart:"_selectStart",select:"_select",selectEnd:"_selectEnd"})),n.hint.visible&&(this.hint=new d(e.element,e.chartService,{min:r,max:l,template:a.getTemplate(n.hint),format:n.hint.format})))},setRange:function(){var t,e=this.chart._createPlotArea(!0),i=e.namedCategoryAxes[p],a=i.roundedRange(),n=a.min,s=a.max,r=this.options.select||{},l=r.from||n;l<n&&(l=n),t=r.to||s,t>s&&(t=s),this.options.select=o({},r,{from:l,to:t}),this.filterAxes()},_redrawSelf:function(t){var e=this.chart._plotArea;e&&e.redraw(a.last(e.panes),t)},redrawSlaves:function(){var t=this.chart,e=t._plotArea,i=e.panes.slice(0,-1);e.srcSeries=t.options.series,e.options.categoryAxis=t.options.categoryAxis,e.clearSeriesPointsCache(),e.redraw(i)},_drag:function(t){var e,i,n,o=this,r=o.chart,l=o.selection,h=r._eventCoordinates(t.originalEvent),c=this.mainAxis(),d=c.roundedRange(),u=c.pane.box.containsPoint(h),p=r._plotArea.categoryAxis,f=t.axisRanges[p.options.name],v=this.options.select;f&&!u&&l&&(e=v.from&&v.to?s(v.to)-s(v.from):s(l.options.to)-s(l.options.from),i=a.toDate(a.limitValue(s(f.min),d.min,s(d.max)-e)),n=a.toDate(a.limitValue(s(i)+e,s(d.min)+e,d.max)),this.options.select={from:i,to:n},this.options.liveDrag&&(this.filterAxes(),this.redrawSlaves()),l.set(i,n),this.showHint(i,n))},_dragEnd:function(){this.filterAxes(),this.filter(),this.redrawSlaves(),this.hint&&this.hint.hide()},readSelection:function(){var t=this,e=t.selection.options,i=e.from,a=e.to,n=t.options.select;n.from=i,n.to=a},filterAxes:function(){var t,e,i,a,n,o,s=this,r=s.options.select;for(void 0===r&&(r={}),t=s.chart,e=t.options.categoryAxis,i=r.from,a=r.to,n=0;n<e.length;n++)o=e[n],o.pane!==u&&(o.min=i,o.max=a)},filter:function(){var t,e,i,n=this,s=n.chart,r=n.options.select;s.requiresHandlers(["navigatorFilter"])&&(t=this.mainAxis(),e={from:r.from,to:r.to},"category"!==t.options.type&&(i=new a.DateCategoryAxis(o({baseUnit:"fit"},s.options.categoryAxis[0],{categories:[r.from,r.to]}),s.chartService).options,e.from=a.addDuration(i.min,-i.baseUnitStep,i.baseUnit),e.to=a.addDuration(i.max,i.baseUnitStep,i.baseUnit)),this.chart.trigger("navigatorFilter",e))},_zoom:function(t){var e,i,a=this,n=a.chart._plotArea.categoryAxis,o=a.selection,s=a.options,r=s.select,l=s.liveDrag,h=this.mainAxis(),c=t.delta;o&&(e=h.categoryIndex(o.options.from),i=h.categoryIndex(o.options.to),t.originalEvent.preventDefault(),Math.abs(c)>1&&(c*=v),i-e>1?(o.expand(c),this.readSelection()):(n.options.min=r.from,r.from=n.scaleRange(-t.delta).min),l&&(this.filterAxes(),this.redrawSlaves()),o.set(r.from,r.to),this.showHint(this.options.select.from,this.options.select.to))},_zoomEnd:function(t){this._dragEnd(t)},showHint:function(t,e){var i=this.chart._plotArea;this.hint&&this.hint.show(t,e,i.backgroundBox())},_selectStart:function(t){return this.chart._selectStart(t)},_select:function(t){return this.showHint(t.from,t.to),this.chart._select(t)},_selectEnd:function(t){return this.hint&&this.hint.hide(),this.readSelection(),this.filterAxes(),this.filter(),this.redrawSlaves(),this.chart._selectEnd(t)},mainAxis:function(){var t=this.chart._plotArea;if(t)return t.namedCategoryAxes[p]},select:function(t,e){var i=this.options.select;return t&&e&&(i.from=this.parseDate(t),i.to=this.parseDate(e),this.filterAxes(),this.filter(),this.redrawSlaves(),this.selection.set(t,e)),{from:i.from,to:i.to}}}),m.setup=function(t,e){var i,a,n;void 0===t&&(t={}),void 0===e&&(e={}),t.__navi||(t.__navi=!0,i=o({},e.navigator,t.navigator),a=t.panes=[].concat(t.panes),n=o({},i.pane,{name:u}),i.visible||(n.visible=!1,n.height=.1),a.push(n),m.attachAxes(t,i),m.attachSeries(t,i,e))},m.attachAxes=function(t,e){var i=e.series||[],n=t.categoryAxis=[].concat(t.categoryAxis),s=t.valueAxis=[].concat(t.valueAxis),l=a.filterSeriesByType(i,r.EQUALLY_SPACED_SERIES),h=0===l.length,c=o({type:"date",pane:u,roundToBaseUnit:!h,justified:h,_collapse:!1,majorTicks:{visible:!0},tooltip:{visible:!1},labels:{step:1},autoBind:e.autoBindElements,autoBaseUnitSteps:{minutes:[1],hours:[1,2],days:[1,2],weeks:[],months:[1],years:[1]}}),d=e.categoryAxis;n.push(o({},c,{maxDateGroups:200},d,{name:p,title:null,baseUnit:"fit",baseUnitStep:"auto",labels:{visible:!1},majorTicks:{visible:!1}}),o({},c,d,{name:p+"_labels",maxDateGroups:20,baseUnitStep:"auto",labels:{position:""},plotBands:[],autoBaseUnitSteps:{minutes:[]},_overlap:!0}),o({},c,d,{name:p+"_ticks",maxDateGroups:200,majorTicks:{width:.5},plotBands:[],title:null,labels:{visible:!1,mirror:!0},_overlap:!0})),s.push(o({name:p,pane:u,majorGridLines:{visible:!1},visible:!1},e.valueAxis))},m.attachSeries=function(t,e,i){var a,n=t.series=t.series||[],s=[].concat(e.series||[]),r=i.seriesColors,l=e.seriesDefaults;for(a=0;a<s.length;a++)n.push(o({color:r[a%r.length],categoryField:e.dateField,visibleInLegend:!1,tooltip:{visible:!1}},l,s[a],{axis:p,categoryAxis:p,autoBind:e.autoBindElements}))},g=28,_=l.extend({applyDefaults:function(t,e){var i=a.elementSize(this.element).width||r.DEFAULT_WIDTH,n=e,s={seriesDefaults:{categoryField:t.dateField},axisDefaults:{categoryAxis:{name:"default",majorGridLines:{visible:!1},labels:{step:2},majorTicks:{visible:!1},maxDateGroups:Math.floor(i/g)}}};n&&(n=o({},n,s)),m.setup(t,n),l.fn.applyDefaults.call(this,t,n)},_setElementClass:function(t){a.addClass(t,"k-chart k-stockchart")},setOptions:function(t){this.destroyNavigator(),l.fn.setOptions.call(this,t)},noTransitionsRedraw:function(){var t=this.options.transitions;this.options.transitions=!1,this._fullRedraw(),this.options.transitions=t},_resize:function(){this.noTransitionsRedraw()},_redraw:function(){var t=this.navigator;!this._dirty()&&t&&t.options.partialRedraw?t.redrawSlaves():this._fullRedraw()},_dirty:function(){var t=this.options,e=[].concat(t.series,t.navigator.series),i=a.grep(e,function(t){return t&&t.visible}).length,n=this._seriesCount!==i;return this._seriesCount=i,n},_fullRedraw:function(){var t=this.navigator;t||(t=this.navigator=new m(this),this.trigger("navigatorCreated",{navigator:t})),t.clean(),t.setRange(),l.fn._redraw.call(this),t.initSelection()},_trackSharedTooltip:function(t){var e=this._plotArea,i=e.paneByPoint(t);i&&i.options.name===u?this._unsetActivePoint():l.fn._trackSharedTooltip.call(this,t)},bindCategories:function(){l.fn.bindCategories.call(this),this.copyNavigatorCategories()},copyNavigatorCategories:function(){var t,e,i,a=[].concat(this.options.categoryAxis);for(e=0;e<a.length;e++)i=a[e],i.name===p?t=i.categories:t&&i.pane===u&&(i.categories=t)},destroyNavigator:function(){this.navigator&&(this.navigator.destroy(),this.navigator=null)},destroy:function(){this.destroyNavigator(),l.fn.destroy.call(this)},_stopChartHandlers:function(t){var e=this._eventCoordinates(t),i=this._plotArea.paneByPoint(e);return l.fn._stopChartHandlers.call(this,t)||i&&i.options.name===u}}),a.setDefaultOptions(_,{dateField:"date",axisDefaults:{categoryAxis:{type:"date",baseUnit:"fit",justified:!0},valueAxis:{narrowRange:!0,labels:{format:"C"}}},navigator:{select:{},seriesDefaults:{markers:{visible:!1},tooltip:{visible:!0},line:{width:2}},hint:{},visible:!0},tooltip:{visible:!0},legend:{visible:!1}}),kendo.deepExtend(kendo.dataviz,{constants:f,Navigator:m,NavigatorHint:d,StockChart:_})}()},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/stock/stock-chart.min",["dataviz/stock/kendo-stock-chart.min"],t)}(function(){!function(t){function e(t,e){return[{field:"Date",operator:"gte",value:t},{field:"Date",operator:"lt",value:e}]}var i=window.kendo,a=i.dataviz,n=a.ChartInstanceObserver,o=a.ui.Chart,s=a.StockChart,r=a.constants,l=r.NAVIGATOR_AXIS,h=r.NAVIGATOR_PANE,c=i.deepExtend,d=a.defined,u=t.proxy,p="change",f=n.extend({handlerMap:{navigatorFilter:"_onNavigatorFilter",navigatorCreated:"_onNavigatorCreated"}}),v=o.extend({options:{name:"StockChart",dateField:"date",axisDefaults:{categoryAxis:{type:"date",baseUnit:"fit",justified:!0},valueAxis:{narrowRange:!0,labels:{format:"C"}}},navigator:{select:{},seriesDefaults:{markers:{visible:!1},tooltip:{visible:!0,template:"#= kendo.toString(category, 'd') #"},line:{width:2}},hint:{},visible:!0},tooltip:{visible:!0},legend:{visible:!1},persistSeriesVisibility:!0},_createChart:function(t,e){this._initNavigatorOptions(t),this._instance=new s(this.element[0],t,e,{observer:new f(this),sender:this,rtl:this._isRtl()})},_initNavigatorOptions:function(t){var e=t.navigator||{},a=i.support,n=a.touch,o=a.browser.mozilla;c(e,{autoBindElements:!e.dataSource,partialRedraw:e.dataSource,liveDrag:!n&&!o})},_initDataSource:function(t){var n,s,r,l,h=t||{},d=h.dataSource,u=d&&d.serverFiltering,p=[].concat(h.categoryAxis)[0],f=h.navigator||{},v=f.select,m=v&&v.from&&v.to;u&&m&&(n=[].concat(d.filter||[]),s=i.parseDate(v.from),r=i.parseDate(v.to),l=new a.DateCategoryAxis(c({baseUnit:"fit"},p,{categories:[s,r]}),i),d.filter=e(l.range().min,r).concat(n)),o.fn._initDataSource.call(this,t)},_onNavigatorCreated:function(t){this._instance=t.sender,this.options=t.sender.options,this._navigator=this.navigator=t.navigator,this._initNavigatorDataSource()},_initNavigatorDataSource:function(){var t=this.options.navigator,e=t.autoBind,a=t.dataSource;a&&(this._navigatorDataChangedHandler=this._navigatorDataChangedHandler||u(this._onNavigatorDataChanged,this),this._navigatorDataSource=i.data.DataSource.create(a).bind(p,this._navigatorDataChangedHandler),d(e)||(e=this.options.autoBind),e&&this._navigatorDataSource.fetch())},_bindNavigatorSeries:function(t,e){var i,a,n=t.length;for(i=0;i<n;i++)a=t[i],a.axis==l&&this._isBindable(a)&&(a.data=e)},_onNavigatorDataChanged:function(){var t,e,i,a,n=this,o=n._instance,s=n.options.categoryAxis,r=s.length,c=n._navigatorDataSource.view();for(this._bindNavigatorSeries(n.options.series,c),n._sourceSeries&&this._bindNavigatorSeries(n._sourceSeries,c),t=0;t<r;t++)e=s[t],e.pane==h&&(e.name==l?(n._bindCategoryAxis(e,c,t),i=e.categories):e.categories=i);o._model&&(a=this.navigator,a.redraw(),a.setRange(),(!n.options.dataSource||n.options.dataSource&&n._dataBound)&&a.redrawSlaves())},_bindCategories:function(){o.fn._bindCategories.call(this),this._instance&&this._instance.copyNavigatorCategories()},_onDataChanged:function(){o.fn._onDataChanged.call(this),this._dataBound=!0},setOptions:function(t){this._removeNavigatorDataSource(),this._initNavigatorOptions(t),this._instance.destroyNavigator(),o.fn.setOptions.call(this,t)},_onNavigatorFilter:function(t){this.dataSource.filter(e(t.from,t.to))},requiresHandlers:function(t){var e,i;return a.inArray("navigatorFilter",t)?(e=this.dataSource,i=e&&e.options.serverFiltering,i&&this.options.navigator.dataSource):o.fn.requiresHandlers.call(this,t)},_removeNavigatorDataSource:function(){var t=this._navigatorDataSource;t&&(t.unbind(p,this._navigatorDataChangedHandler),delete this._navigatorDataSource)},destroy:function(){o.fn.destroy.call(this),this._removeNavigatorDataSource()}});a.ui.plugin(v)}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("kendo.dataviz.stock.min",["dataviz/stock/kendo-stock-chart.min","dataviz/stock/stock-chart.min"],t)}(function(){},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()});
//# sourceMappingURL=kendo.dataviz.stock.min.js.map
