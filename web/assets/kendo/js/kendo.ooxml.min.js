/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("ooxml/utils.min",["kendo.core.min"],e)}(function(){!function(){kendo.ooxml=kendo.ooxml||{},kendo.ooxml.createZip=function(){if("undefined"==typeof JSZip)throw Error("JSZip not found. Check http://docs.telerik.com/kendo-ui/framework/excel/introduction#requirements for more details.");return new JSZip}}()},"function"==typeof define&&define.amd?define:function(e,n,t){(t||n)()}),function(e,define){define("ooxml/kendo-ooxml.min",["kendo.core.min","ooxml/utils.min"],e)}(function(){!function(e){function n(e,n,t){return(1461*(e+4800+((n-13)/12|0))/4|0)+(367*(n-1-12*((n-13)/12|0))/12|0)-(3*((e+4900+((n-13)/12|0))/100|0)/4|0)+t-32075}function t(e,t,o){return n(e,t,o)-j}function o(e,n,t,o){return(e+(n+(t+o/1e3)/60)/60)/24}function r(e){var n=o(e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()),r=t(e.getFullYear(),e.getMonth(),e.getDate());return r<0?r-1+n:r+n}function i(e){return V+e}function s(e,n){return n.indexOf(e)}function l(e){return(e+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\"/g,"&quot;").replace(/\'/g,"&#39;")}function a(e,n){var t,o="";for(t=0;t<e;++t)o+=n(t);return o}function m(e,n){var t,o="";if(null!=e)if(Array.isArray(e))for(t=0;t<e.length;++t)o+=n(e[t],t);else"object"==typeof e&&Object.keys(e).forEach(function(t,r){o+=n(e[t],t,r)});return o}function c(e){return"string"==typeof e?"<f>"+l(e)+"</f>":'<f t="array" ref="'+e.ref+'">'+l(e.src)+"</f>"}function f(e){var n=Math.floor(e/26)-1;return(n>=0?f(n):"")+String.fromCharCode(65+e%26)}function d(e,n){return f(n)+(e+1)}function u(e,n){return"$"+f(n)+"$"+(e+1)}function h(e){var n=e.frozenRows||(e.freezePane||{}).rowSplit||1;return n-1}function p(e){var n=7;return e/n-Math.floor(128/n)/256}function g(e){return.75*e}function x(e){return(e+"").replace(/[\x00-\x09\x0B\x0C\x0E-\x1F]/g,"").replace(/\r?\n/g,"\r\n")}function y(e){var n=e;return n.length<6&&(n=n.replace(/(\w)/g,function(e,n){return n+n})),n=n.substring(1).toUpperCase(),n.length<8&&(n="FF"+n),n}function v(e){var n="thin";return 2===e?n="medium":3===e&&(n="thick"),n}function w(e,n){var t="";return n&&(t+="<"+e+' style="'+v(n.size)+'">',n.color&&(t+='<color rgb="'+y(n.color)+'"/>'),t+="</"+e+">"),t}function b(e){return"<border>"+w("left",e.left)+w("right",e.right)+w("top",e.top)+w("bottom",e.bottom)+"</border>"}function k(e,n){var t,o,r,i=[],s=[];for(I(e,function(e,n){var t={_source:e,index:n,height:e.height,level:e.level,cells:[]};i.push(t),s[n]=t}),t=T(i).slice(0),o={rowData:i,rowsByIndex:s,mergedCells:n},r=0;r<t.length;r++)F(t[r],o),delete t[r]._source;return T(i)}function I(e,n){var t,o,r;for(t=0;t<e.length;t++)o=e[t],o&&(r=o.index,"number"!=typeof r&&(r=t),n(o,r))}function T(e){return e.sort(function(e,n){return e.index-n.index})}function _(e,n){e.indexOf(n)<0&&e.push(n)}function S(e,n){var t,o,r,i,s;for(t=0;t<e.length;++t)if(o=e[t],r=o.split(":"),i=r[0],i===n)return s=r[1],i=C(i),s=C(s),{rowSpan:s.row-i.row+1,colSpan:s.col-i.col+1}}function C(e){function n(e){var n,t=e.toUpperCase(),o=0;for(n=0;n<t.length;++n)o=26*o+t.charCodeAt(n)-64;return o-1}function t(e){return parseInt(e,10)-1}var o=/^([a-z]+)(\d+)$/i.exec(e);return{row:t(o[2]),col:n(o[1])}}function D(e){return Math.round(9525*e)}function F(e,n){var t,o,r,i,s,l,a,m,c,f=e._source,u=e.index,h=f.cells,p=e.cells;if(h)for(t=0;t<h.length;t++)if(o=h[t]||fe,r=o.rowSpan||1,i=o.colSpan||1,s=P(p,o),l=d(u,s),1===r&&1===i&&(a=S(n.mergedCells,l),a&&(i=a.colSpan,r=a.rowSpan)),R(o,p,s,i),(r>1||i>1)&&_(n.mergedCells,l+":"+d(u+r-1,s+i-1)),r>1)for(m=u+1;m<u+r;m++)c=n.rowsByIndex[m],c||(c=n.rowsByIndex[m]={index:m,cells:[]},n.rowData.push(c)),R(o,c.cells,s-1,i+1)}function P(e,n){var t;return"number"==typeof n.index?(t=n.index,M(e,n,n.index)):t=A(e,n),t}function M(e,n,t){e[t]=n}function A(e,n){var t,o=e.length;for(t=0;t<e.length+1;t++)if(!e[t]){e[t]=n,o=t;break}return o}function R(e,n,t,o){var r,i;for(r=1;r<o;r++)i={borderTop:e.borderTop,borderRight:e.borderRight,borderBottom:e.borderBottom,borderLeft:e.borderLeft},M(n,i,t+r)}function L(e){return de({ref:e.ref,columns:e.columns,generators:{custom:ue,dynamic:he,top:pe,value:ge}})}var O,E,N,q,z,j,B,V,W,U,X,H,J,Z,G,$,Q,K,Y,ee,ne,te,oe,re,ie,se,le,ae,me,ce,fe,de,ue,he,pe,ge;window.kendo.ooxml=window.kendo.ooxml||{},O=kendo.ooxml,E=e.map,N=O.createZip,q={toString:function(e){return e}},z=kendo.Class.extend({}),z.register=function(e){q=e},z.toString=function(e,n){return q.toString(e,n)},j=n(1900,0,-1),B="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",V="data:"+B+";base64,",W={compression:"DEFLATE",type:"base64"},U=JSON.parse.bind(JSON),X='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r',H=X+'\n            <Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">\n               <Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties" Target="docProps/app.xml"/>\n               <Relationship Id="rId2" Type="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties" Target="docProps/core.xml"/>\n               <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>\n            </Relationships>',J=function(e){var n=e.creator,t=e.lastModifiedBy,o=e.created,r=e.modified;return X+'\n <cp:coreProperties xmlns:cp="http://schemas.openxmlformats.org/package/2006/metadata/core-properties"\n   xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/"\n   xmlns:dcmitype="http://purl.org/dc/dcmitype/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">\n   <dc:creator>'+l(n)+"</dc:creator>\n   <cp:lastModifiedBy>"+l(t)+'</cp:lastModifiedBy>\n   <dcterms:created xsi:type="dcterms:W3CDTF">'+l(o)+'</dcterms:created>\n   <dcterms:modified xsi:type="dcterms:W3CDTF">'+l(r)+"</dcterms:modified>\n</cp:coreProperties>"},Z=function(e){var n=e.sheets;return X+'\n<Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties" xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">\n  <Application>Microsoft Excel</Application>\n  <DocSecurity>0</DocSecurity>\n  <ScaleCrop>false</ScaleCrop>\n  <HeadingPairs>\n    <vt:vector size="2" baseType="variant">\n      <vt:variant>\n        <vt:lpstr>Worksheets</vt:lpstr>\n      </vt:variant>\n      <vt:variant>\n        <vt:i4>'+n.length+'</vt:i4>\n      </vt:variant>\n    </vt:vector>\n  </HeadingPairs>\n  <TitlesOfParts>\n    <vt:vector size="'+n.length+'" baseType="lpstr">'+m(n,function(e,n){return e.options.title?"<vt:lpstr>"+l(e.options.title)+"</vt:lpstr>":"<vt:lpstr>Sheet"+(n+1)+"</vt:lpstr>"})+"</vt:vector>\n  </TitlesOfParts>\n  <LinksUpToDate>false</LinksUpToDate>\n  <SharedDoc>false</SharedDoc>\n  <HyperlinksChanged>false</HyperlinksChanged>\n  <AppVersion>14.0300</AppVersion>\n</Properties>"},G=function(e){var n=e.sheetCount,t=e.commentFiles,o=e.drawingFiles;return X+'\n<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">\n  <Default Extension="png" ContentType="image/png"/>\n  <Default Extension="gif" ContentType="image/gif"/>\n  <Default Extension="jpg" ContentType="image/jpeg"/>\n  <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml" />\n  <Default Extension="xml" ContentType="application/xml" />\n  <Default Extension="vml" ContentType="application/vnd.openxmlformats-officedocument.vmlDrawing"/>\n  <Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml" />\n  <Override PartName="/xl/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml"/>\n  <Override PartName="/xl/sharedStrings.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"/>\n  '+a(n,function(e){return'<Override PartName="/xl/worksheets/sheet'+(e+1)+'.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml" />'})+"\n  "+m(t,function(e){return'<Override PartName="/xl/'+e+'" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml"/>'})+"\n  "+m(o,function(e){return'<Override PartName="/xl/drawings/'+e+'" ContentType="application/vnd.openxmlformats-officedocument.drawing+xml"/>'})+'\n  <Override PartName="/docProps/core.xml" ContentType="application/vnd.openxmlformats-package.core-properties+xml" />\n  <Override PartName="/docProps/app.xml" ContentType="application/vnd.openxmlformats-officedocument.extended-properties+xml" />\n</Types>'},$=function(e){var n=e.sheets,t=e.filterNames,o=e.userNames;return X+'\n<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">\n  <fileVersion appName="xl" lastEdited="5" lowestEdited="5" rupBuild="9303" />\n  <workbookPr defaultThemeVersion="124226" />\n  <bookViews>\n    <workbookView xWindow="240" yWindow="45" windowWidth="18195" windowHeight="7995" />\n  </bookViews>\n  <sheets>\n  '+m(n,function(e,n){var t=e.options,o=t.name||t.title||"Sheet"+(n+1);return'<sheet name="'+l(o)+'" sheetId="'+(n+1)+'" r:id="rId'+(n+1)+'" />'})+"\n  </sheets>\n  "+(t.length||o.length?"\n    <definedNames>\n      "+m(t,function(e){return'\n         <definedName name="_xlnm._FilterDatabase" hidden="1" localSheetId="'+e.localSheetId+'">'+l(e.name)+"!"+l(e.from)+":"+l(e.to)+"</definedName>"})+"\n      "+m(o,function(e){return'\n         <definedName name="'+e.name+'" hidden="'+(e.hidden?1:0)+'" '+(null!=e.localSheetId?'localSheetId="'+e.localSheetId+'"':"")+">"+l(e.value)+"</definedName>"})+"\n    </definedNames>":"")+'\n  <calcPr fullCalcOnLoad="1" calcId="145621" />\n</workbook>'},Q=function(e){var n=e.frozenColumns,t=e.frozenRows,o=e.columns,r=e.defaults,i=e.data,s=e.index,a=e.mergeCells,f=e.autoFilter,d=e.filter,u=e.showGridLines,h=e.hyperlinks,x=e.validations,y=e.defaultCellStyleId,v=e.rtl,w=e.legacyDrawing,b=e.drawing,k=e.lastRow;return X+'\n<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac" mc:Ignorable="x14ac">\n   <dimension ref="A1:A'+k+'" />\n\n   <sheetViews>\n     <sheetView '+(v?'rightToLeft="1"':"")+" "+(0===s?'tabSelected="1"':"")+' workbookViewId="0" '+(u===!1?'showGridLines="0"':"")+">\n     "+(t||n?'\n       <pane state="frozen"\n         '+(n?'xSplit="'+n+'"':"")+"\n         "+(t?'ySplit="'+t+'"':"")+'\n         topLeftCell="'+(String.fromCharCode(65+(n||0))+((t||0)+1))+'"\n       />':"")+'\n     </sheetView>\n   </sheetViews>\n\n   <sheetFormatPr x14ac:dyDescent="0.25" customHeight="1" defaultRowHeight="'+(r.rowHeight?.75*r.rowHeight:15)+'"\n     '+(r.columnWidth?'defaultColWidth="'+p(r.columnWidth)+'"':"")+" />\n\n   "+(null!=y||o&&o.length>0?"\n     <cols>\n       "+(o&&o.length?"":'\n         <col min="1" max="16384" style="'+y+'"\n              '+(r.columnWidth?'width="'+p(r.columnWidth)+'"':"")+" /> ")+"\n       "+m(o,function(e,n){var t="number"==typeof e.index?e.index+1:n+1;return 0===e.width?"<col "+(null!=y?'style="'+y+'"':"")+'\n                        min="'+t+'" max="'+t+'" hidden="1" customWidth="1" />':"<col "+(null!=y?'style="'+y+'"':"")+'\n                      min="'+t+'" max="'+t+'" customWidth="1"\n                      '+(e.autoWidth?'width="'+(7*e.width+5)/7*256/256+'" bestFit="1"':'width="'+p(e.width)+'"')+" />"})+"\n     </cols>":"")+"\n\n   <sheetData>\n     "+m(i,function(e,n){var t="number"==typeof e.index?e.index+1:n+1;return'\n         <row r="'+t+'" x14ac:dyDescent="0.25"\n              '+(e.level?'outlineLevel="'+e.level+'"':"")+"\n              "+(0===e.height?'hidden="1"':e.height?'ht="'+g(e.height)+'" customHeight="1"':"")+">\n           "+m(e.data,function(e){return'\n             <c r="'+e.ref+'" '+(e.style?'s="'+e.style+'"':"")+" "+(e.type?'t="'+e.type+'"':"")+">\n               "+(null!=e.formula?c(e.formula):"")+"\n               "+(null!=e.value?"<v>"+l(e.value)+"</v>":"")+"\n             </c>"})+"\n         </row>\n       "})+"\n   </sheetData>\n\n   "+(f?'<autoFilter ref="'+f.from+":"+f.to+'"/>':d?L(d):"")+"\n\n   "+(a.length?'\n     <mergeCells count="'+a.length+'">\n       '+m(a,function(e){return'<mergeCell ref="'+e+'"/>'})+"\n     </mergeCells>":"")+"\n\n   "+(x.length?"\n     <dataValidations>\n       "+m(x,function(e){return'\n         <dataValidation sqref="'+e.sqref.join(" ")+'"\n                         showErrorMessage="'+e.showErrorMessage+'"\n                         type="'+l(e.type)+'"\n                         '+("list"!==e.type?'operator="'+l(e.operator)+'"':"")+'\n                         allowBlank="'+e.allowBlank+'"\n                         showDropDown="'+e.showDropDown+'"\n                         '+(e.error?'error="'+l(e.error)+'"':"")+"\n                         "+(e.errorTitle?'errorTitle="'+l(e.errorTitle)+'"':"")+">\n           "+(e.formula1?"<formula1>"+l(e.formula1)+"</formula1>":"")+"\n           "+(e.formula2?"<formula2>"+l(e.formula2)+"</formula2>":"")+"\n         </dataValidation>"})+"\n     </dataValidations>":"")+"\n\n   "+(h.length?"\n     <hyperlinks>\n       "+m(h,function(e){return'\n         <hyperlink ref="'+e.ref+'" r:id="'+e.rId+'"/>'})+"\n     </hyperlinks>":"")+'\n\n   <pageMargins left="0.7" right="0.7" top="0.75" bottom="0.75" header="0.3" footer="0.3" />\n   '+(w?'<legacyDrawing r:id="'+w+'"/>':"")+"\n   "+(b?'<drawing r:id="'+b+'"/>':"")+"\n</worksheet>"},K=function(e){var n=e.count;return X+'\n<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">\n  '+a(n,function(e){return'\n    <Relationship Id="rId'+(e+1)+'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet'+(e+1)+'.xml" />'})+'\n  <Relationship Id="rId'+(n+1)+'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Target="styles.xml" />\n  <Relationship Id="rId'+(n+2)+'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings" Target="sharedStrings.xml" />\n</Relationships>'},Y=function(e){var n=e.hyperlinks,t=e.comments,o=e.sheetIndex,r=e.drawings;return X+'\n<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">\n  '+m(n,function(e){return'\n    <Relationship Id="'+e.rId+'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink" Target="'+l(e.target)+'" TargetMode="External" />'})+"\n  "+(t.length?'\n    <Relationship Id="comment'+o+'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments" Target="../comments'+o+'.xml"/>\n    <Relationship Id="vml'+o+'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing" Target="../drawings/vmlDrawing'+o+'.vml"/>':"")+"\n  "+(r.length?'\n    <Relationship Id="drw'+o+'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing" Target="../drawings/drawing'+o+'.xml"/>':"")+"\n</Relationships>"},ee=function(e){var n=e.comments;return X+'\n<comments xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">\n  <authors>\n    <author></author>\n  </authors>\n  <commentList>\n    '+m(n,function(e){return'\n      <comment ref="'+e.ref+'" authorId="0">\n        <text>\n          <r>\n            <rPr>\n              <sz val="8"/>\n              <color indexed="81"/>\n              <rFont val="Tahoma"/>\n              <charset val="1"/>\n            </rPr>\n            <t>'+l(e.text)+"</t>\n          </r>\n        </text>\n      </comment>"})+"\n  </commentList>\n</comments>"},ne=function(e){var n=e.comments;return'<xml xmlns:v="urn:schemas-microsoft-com:vml"\n     xmlns:o="urn:schemas-microsoft-com:office:office"\n     xmlns:x="urn:schemas-microsoft-com:office:excel">\n  <v:shapetype id="_x0000_t202" path="m,l,21600r21600,l21600,xe"></v:shapetype>\n  '+m(n,function(e){return'\n    <v:shape type="#_x0000_t202" style="visibility: hidden" fillcolor="#ffffe1" o:insetmode="auto">\n      <v:shadow on="t" color="black" obscured="t"/>\n      <x:ClientData ObjectType="Note">\n        <x:MoveWithCells/>\n        <x:SizeWithCells/>\n        <x:Anchor>'+e.anchor+"</x:Anchor>\n        <x:AutoFill>False</x:AutoFill>\n        <x:Row>"+e.row+"</x:Row>\n        <x:Column>"+e.col+"</x:Column>\n      </x:ClientData>\n    </v:shape>"})+"\n</xml>"},te=function(e){return X+'\n<xdr:wsDr xmlns:xdr="http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing"\n          xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main"\n          xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">\n  '+m(e,function(e,n){return'\n    <xdr:oneCellAnchor editAs="oneCell">\n      <xdr:from>\n        <xdr:col>'+e.col+"</xdr:col>\n        <xdr:colOff>"+e.colOffset+"</xdr:colOff>\n        <xdr:row>"+e.row+"</xdr:row>\n        <xdr:rowOff>"+e.rowOffset+'</xdr:rowOff>\n      </xdr:from>\n      <xdr:ext cx="'+e.width+'" cy="'+e.height+'" />\n      <xdr:pic>\n        <xdr:nvPicPr>\n          <xdr:cNvPr id="'+(n+1)+'" name="Picture '+(n+1)+'"/>\n          <xdr:cNvPicPr/>\n        </xdr:nvPicPr>\n        <xdr:blipFill>\n          <a:blip r:embed="'+e.imageId+'"/>\n          <a:stretch>\n            <a:fillRect/>\n          </a:stretch>\n        </xdr:blipFill>\n        <xdr:spPr>\n          <a:prstGeom prst="rect">\n            <a:avLst/>\n          </a:prstGeom>\n        </xdr:spPr>\n      </xdr:pic>\n      <xdr:clientData/>\n    </xdr:oneCellAnchor>'})+"\n</xdr:wsDr>"},oe=function(e){return X+'\n<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">\n  '+m(e,function(e){return'\n    <Relationship Id="'+e.rId+'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image" Target="'+e.target+'"/>'})+"\n</Relationships>"},re=function(e){var n=e.count,t=e.uniqueCount,o=e.indexes;return X+'\n<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="'+n+'" uniqueCount="'+t+'">\n  '+m(Object.keys(o),function(e){return'\n    <si><t xml:space="preserve">'+l(e.substring(1))+"</t></si>"})+"\n</sst>"},ie=function(e){var n=e.formats,t=e.fonts,o=e.fills,r=e.borders,i=e.styles;return X+'\n<styleSheet\n    xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main"\n    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"\n    mc:Ignorable="x14ac"\n    xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac">\n  <numFmts count="'+n.length+'">\n  '+m(n,function(e,n){return'\n    <numFmt formatCode="'+l(e.format)+'" numFmtId="'+(165+n)+'" />'})+'\n  </numFmts>\n  <fonts count="'+(t.length+1)+'" x14ac:knownFonts="1">\n    <font>\n       <sz val="11" />\n       <color theme="1" />\n       <name val="Calibri" />\n       <family val="2" />\n       <scheme val="minor" />\n    </font>\n    '+m(t,function(e){return'\n    <font>\n      <sz val="'+(e.fontSize||11)+'" />\n      '+(e.bold?"<b/>":"")+"\n      "+(e.italic?"<i/>":"")+"\n      "+(e.underline?"<u/>":"")+"\n      "+(e.color?'<color rgb="'+l(e.color)+'" />':'<color theme="1" />')+"\n      "+(e.fontFamily?'\n        <name val="'+l(e.fontFamily)+'" />\n        <family val="2" />\n      ':'\n        <name val="Calibri" />\n        <family val="2" />\n        <scheme val="minor" />\n      ')+"\n    </font>"})+'\n  </fonts>\n  <fills count="'+(o.length+2)+'">\n      <fill><patternFill patternType="none"/></fill>\n      <fill><patternFill patternType="gray125"/></fill>\n    '+m(o,function(e){return"\n      "+(e.background?'\n        <fill>\n          <patternFill patternType="solid">\n              <fgColor rgb="'+l(e.background)+'"/>\n          </patternFill>\n        </fill>\n      ':"")})+'\n  </fills>\n  <borders count="'+(r.length+1)+'">\n    <border><left/><right/><top/><bottom/><diagonal/></border>\n    '+m(r,b)+'\n  </borders>\n  <cellStyleXfs count="1">\n    <xf borderId="0" fillId="0" fontId="0" />\n  </cellStyleXfs>\n  <cellXfs count="'+(i.length+1)+'">\n    <xf numFmtId="0" fontId="0" fillId="0" borderId="0" xfId="0" />\n    '+m(i,function(e){return'\n      <xf xfId="0"\n          '+(e.fontId?'fontId="'+e.fontId+'" applyFont="1"':"")+"\n          "+(e.fillId?'fillId="'+e.fillId+'" applyFill="1"':"")+"\n          "+(e.numFmtId?'numFmtId="'+e.numFmtId+'" applyNumberFormat="1"':"")+"\n          "+(e.textAlign||e.verticalAlign||e.wrap?'applyAlignment="1"':"")+"\n          "+(e.borderId?'borderId="'+e.borderId+'" applyBorder="1"':"")+">\n        "+(e.textAlign||e.verticalAlign||e.wrap?"\n        <alignment\n          "+(e.textAlign?'horizontal="'+l(e.textAlign)+'"':"")+"\n          "+(e.verticalAlign?'vertical="'+l(e.verticalAlign)+'"':"")+"\n          "+(e.indent?'indent="'+l(e.indent)+'"':"")+"\n          "+(e.wrap?'wrapText="1"':"")+" />\n        ":"")+"\n      </xf>\n    "})+'\n  </cellXfs>\n  <cellStyles count="1">\n    <cellStyle name="Normal" xfId="0" builtinId="0"/>\n  </cellStyles>\n  <dxfs count="0" />\n  <tableStyles count="0" defaultTableStyle="TableStyleMedium2" defaultPivotStyle="PivotStyleMedium9" />\n</styleSheet>'},se=kendo.Class.extend({init:function(n,t,o,r){this.options=n,this._strings=t,this._styles=o,this._borders=r,this._validations={},this._comments=[],this._drawings=n.drawings||[],this._hyperlinks=(this.options.hyperlinks||[]).map(function(n,t){return e.extend({},n,{rId:"link"+t})})},relsToXML:function(){var e=this._hyperlinks,n=this._comments,t=this._drawings;if(e.length||n.length||t.length)return Y({hyperlinks:e,comments:n,sheetIndex:this.options.sheetIndex,drawings:t})},toXML:function(e){var n,t,o,r,i,s,l,a,m=this,c=this.options.mergedCells||[],f=this.options.rows||[],u=k(f,c);this._readCells(u),n=this.options.filter,n&&"number"==typeof n.from&&"number"==typeof n.to?n={from:d(h(this.options),n.from),to:d(h(this.options),n.to)}:n&&n.ref&&n.columns&&(t=n,n=null),o=[];for(r in this._validations)Object.prototype.hasOwnProperty.call(m._validations,r)&&o.push(m._validations[r]);return i=null,this.options.defaultCellStyle&&(i=this._lookupStyle(this.options.defaultCellStyle)),s=this.options.freezePane||{},l=this.options.defaults||{},a=this.options.rows?this._getLastRow():1,Q({frozenColumns:this.options.frozenColumns||s.colSplit,frozenRows:this.options.frozenRows||s.rowSplit,columns:this.options.columns,defaults:l,data:u,index:e,mergeCells:c,autoFilter:n,filter:t,showGridLines:this.options.showGridLines,hyperlinks:this._hyperlinks,validations:o,defaultCellStyleId:i,rtl:void 0!==this.options.rtl?this.options.rtl:l.rtl,legacyDrawing:this._comments.length?"vml"+this.options.sheetIndex:null,drawing:this._drawings.length?"drw"+this.options.sheetIndex:null,lastRow:a})},commentsXML:function(){if(this._comments.length)return ee({comments:this._comments})},drawingsXML:function(e){var n,t;if(this._drawings.length)return n={},t=this._drawings.map(function(t){var o=C(t.topLeftCell),r=n[t.image];return r||(r=n[t.image]={rId:"img"+t.image,target:e[t.image].target}),{col:o.col,colOffset:D(t.offsetX),row:o.row,rowOffset:D(t.offsetY),width:D(t.width),height:D(t.height),imageId:r.rId}}),{main:te(t),rels:oe(n)}},legacyDrawing:function(){if(this._comments.length)return ne({comments:this._comments})},_lookupString:function(e){var n,t="$"+e,o=this._strings.indexes[t];return void 0!==o?n=o:(n=this._strings.indexes[t]=this._strings.uniqueCount,this._strings.uniqueCount++),this._strings.count++,n},_lookupStyle:function(e){var n,t=JSON.stringify(e);return"{}"===t?0:(n=s(t,this._styles),n<0&&(n=this._styles.push(t)-1),n+1)},_lookupBorder:function(e){var n,t=JSON.stringify(e);if("{}"!==t)return n=s(t,this._borders),n<0&&(n=this._borders.push(t)-1),n+1},_readCells:function(e){var n,t,o,r,i,s=this;for(n=0;n<e.length;n++)for(t=e[n],o=t.cells,t.data=[],r=0;r<o.length;r++)i=s._cell(o[r],t.index,r),i&&t.data.push(i)},_cell:function(e,n,t){var o,i,s,l,a,m,c,f,u,h;return e&&e!==fe?(o=e.value,i={},e.borderLeft&&(i.left=e.borderLeft),e.borderRight&&(i.right=e.borderRight),e.borderTop&&(i.top=e.borderTop),e.borderBottom&&(i.bottom=e.borderBottom),i=this._lookupBorder(i),s=this.options.defaultCellStyle||{},l={borderId:i},function(e){e("color"),e("background"),e("bold"),e("italic"),e("underline"),e("fontFamily")||e("fontName","fontFamily"),e("fontSize"),e("format"),e("textAlign")||e("hAlign","textAlign"),e("verticalAlign")||e("vAlign","verticalAlign"),e("wrap"),e("indent")}(function(n,t){var o=e[n];if(void 0===o&&(o=s[n]),void 0!==o)return l[t||n]=o,!0}),a=this.options.columns||[],m=a[t],c=typeof o,m&&m.autoWidth&&(f=o,"number"===c&&(f=z.toString(o,e.format)),m.width=Math.max(m.width||0,(f+"").length)),"string"===c?(o=x(o),o=this._lookupString(o),c="s"):"number"===c?c="n":"boolean"===c?(c="b",o=+o):o&&o.getTime?(c=null,o=r(o),l.format||(l.format="mm-dd-yy")):(c=null,o=null),l=this._lookupStyle(l),u=d(n,t),e.validation&&this._addValidation(e.validation,u),e.comment&&(h=[t+1,15,n,10,t+3,15,n+3,4],this._comments.push({ref:u,text:e.comment,row:n,col:t,anchor:h.join(", ")})),{value:o,formula:e.formula,type:c,style:l,ref:u}):null},_addValidation:function(e,n){var t={showErrorMessage:"reject"===e.type?1:0,formula1:e.from,formula2:e.to,type:ae[e.dataType]||e.dataType,operator:le[e.comparerType]||e.comparerType,allowBlank:e.allowNulls?1:0,showDropDown:e.showButton?0:1,error:e.messageTemplate,errorTitle:e.titleTemplate},o=JSON.stringify(t);this._validations[o]||(this._validations[o]=t,t.sqref=[]),this._validations[o].sqref.push(n)},_getLastRow:function(){var e=this.options.rows,n=e.length;return e.forEach(function(e){e.index&&e.index>=n&&(n=e.index+1)}),n}}),le={greaterThanOrEqualTo:"greaterThanOrEqual",lessThanOrEqualTo:"lessThanOrEqual"},ae={number:"decimal"},me={General:0,0:1,"0.00":2,"#,##0":3,"#,##0.00":4,"0%":9,"0.00%":10,"0.00E+00":11,"# ?/?":12,"# ??/??":13,"mm-dd-yy":14,"d-mmm-yy":15,"d-mmm":16,"mmm-yy":17,"h:mm AM/PM":18,"h:mm:ss AM/PM":19,"h:mm":20,"h:mm:ss":21,"m/d/yy h:mm":22,"#,##0 ;(#,##0)":37,"#,##0 ;[Red](#,##0)":38,"#,##0.00;(#,##0.00)":39,"#,##0.00;[Red](#,##0.00)":40,"mm:ss":45,"[h]:mm:ss":46,"mmss.0":47,"##0.0E+0":48,"@":49,"[$-404]e/m/d":27,"m/d/yy":30,t0:59,"t0.00":60,"t#,##0":61,"t#,##0.00":62,"t0%":67,"t0.00%":68,"t# ?/?":69,"t# ??/??":70},ce=kendo.Class.extend({init:function(e){var n=this;this.options=e||{},this._strings={indexes:{},count:0,uniqueCount:0},this._styles=[],this._borders=[],this._images=this.options.images,this._imgId=0,this._sheets=E(this.options.sheets||[],function(e,t){return e.defaults=n.options,e.sheetIndex=t+1,new se(e,n._strings,n._styles,n._borders)})},imageFilename:function(e){var n=++this._imgId;switch(e){case"image/jpg":case"image/jpeg":return"image"+n+".jpg";case"image/png":return"image"+n+".png";case"image/gif":return"image"+n+".gif";default:return"image"+n+".bin"}},toZIP:function(){var e,n,t,o,r,i,l,a,m,c,f,d,p,g,x,v,w,b,k,I,T,_,S,D,F,P,M,A,R,L=this,O=N(),q=O.folder("docProps");for(q.file("core.xml",J({creator:this.options.creator||"Kendo UI",lastModifiedBy:this.options.creator||"Kendo UI",created:this.options.date||(new Date).toJSON(),modified:this.options.date||(new Date).toJSON()})),e=this._sheets.length,q.file("app.xml",Z({sheets:this._sheets})),n=O.folder("_rels"),n.file(".rels",H),t=O.folder("xl"),o=t.folder("_rels"),o.file("workbook.xml.rels",K({count:e})),this._images&&(r=t.folder("media"),Object.keys(this._images).forEach(function(e){var n=L._images[e],t=L.imageFilename(n.type);r.file(t,n.data),n.target="../media/"+t})),i={},t.file("workbook.xml",$({sheets:this._sheets,filterNames:E(this._sheets,function(e,n){var t,o,r,s,l=e.options,a=l.name||l.title||"Sheet"+(n+1);if(i[a.toLowerCase()]=n,t=l.filter){if(t.ref)return o=t.ref.split(":"),r=C(o[0]),s=C(o[1]),{localSheetId:n,name:a,from:u(r.row,r.col),to:u(s.row,s.col)};if(void 0!==t.from&&void 0!==t.to)return{localSheetId:n,name:a,from:u(h(l),t.from),to:u(h(l),t.to)}}}),userNames:E(this.options.names||[],function(e){return{name:e.localName,localSheetId:e.sheet?i[e.sheet.toLowerCase()]:null,value:e.value,hidden:e.hidden}})})),l=t.folder("worksheets"),a=t.folder("drawings"),m=a.folder("_rels"),c=l.folder("_rels"),f=[],d=[],p=0;p<e;p++)g=L._sheets[p],x="sheet"+(p+1)+".xml",v=g.toXML(p),w=g.relsToXML(),b=g.commentsXML(),k=g.legacyDrawing(),I=g.drawingsXML(L._images),w&&c.file(x+".rels",w),b&&(T="comments"+g.options.sheetIndex+".xml",t.file(T,b),f.push(T)),k&&a.file("vmlDrawing"+g.options.sheetIndex+".vml",k),I&&(_="drawing"+g.options.sheetIndex+".xml",a.file(_,I.main),m.file(_+".rels",I.rels),d.push(_)),l.file(x,v);return S=E(this._borders,U),D=E(this._styles,U),F=function(e){return e.underline||e.bold||e.italic||e.color||e.fontFamily||e.fontSize},P=function(e){var n,t=+e;return t&&(n=3*t/4),n},M=E(D,function(e){if(e.fontSize&&(e.fontSize=P(e.fontSize)),e.color&&(e.color=y(e.color)),F(e))return e}),A=E(D,function(e){if(e.format&&void 0===me[e.format])return e}),R=E(D,function(e){if(e.background)return e.background=y(e.background),e}),t.file("styles.xml",ie({fonts:M,fills:R,formats:A,borders:S,styles:E(D,function(e){var n={};return F(e)&&(n.fontId=s(e,M)+1),e.background&&(n.fillId=s(e,R)+2),n.textAlign=e.textAlign,n.indent=e.indent,n.verticalAlign=e.verticalAlign,n.wrap=e.wrap,n.borderId=e.borderId,e.format&&(n.numFmtId=void 0!==me[e.format]?me[e.format]:165+s(e,A)),n})})),t.file("sharedStrings.xml",re(this._strings)),O.file("[Content_Types].xml",G({sheetCount:e,commentFiles:f,drawingFiles:d})),O},toDataURL:function(){var e=this.toZIP();return e.generateAsync?e.generateAsync(W).then(i):i(e.generate(W))},toBlob:function(){var e=this.toZIP();return e.generateAsync?e.generateAsync({type:"blob"}):new Blob([e.generate({type:"arraybuffer"})],{type:B})}}),fe={},de=function(e){var n=e.ref,t=e.columns,o=e.generators;return'\n<autoFilter ref="'+n+'">\n  '+m(t,function(e){return'\n    <filterColumn colId="'+e.index+'">\n      '+o[e.filter](e)+"\n    </filterColumn>\n  "})+"\n</autoFilter>"},ue=function(e){var n=e.logic,t=e.criteria;return"\n<customFilters "+("and"===n?'and="1"':"")+">\n"+m(t,function(e){var n=L.customOperator(e),t=L.customValue(e);return"<customFilter "+(n?'operator="'+n+'"':"")+' val="'+t+'"/>'})+"\n</customFilters>"},he=function(e){var n=e.type;return'<dynamicFilter type="'+L.dynamicFilterType(n)+'" />'},pe=function(e){var n=e.type,t=e.value;return'<top10 percent="'+(/percent$/i.test(n)?1:0)+'"\n       top="'+(/^top/i.test(n)?1:0)+'"\n       val="'+t+'" />'},ge=function(e){var n=e.blanks,t=e.values;return"<filters "+(n?'blank="1"':"")+">\n    "+m(t,function(e){return'\n      <filter val="'+e+'" />'})+"\n  </filters>"},L.customOperator=function(e){return{eq:"equal",gt:"greaterThan",gte:"greaterThanOrEqual",lt:"lessThan",lte:"lessThanOrEqual",ne:"notEqual",doesnotstartwith:"notEqual",doesnotendwith:"notEqual",doesnotcontain:"notEqual",doesnotmatch:"notEqual"}[e.operator.toLowerCase()]},L.customValue=function(e){function n(e){return e.replace(/([*?])/g,"~$1")}switch(e.operator.toLowerCase()){case"startswith":case"doesnotstartwith":return n(e.value)+"*";case"endswith":case"doesnotendwith":return"*"+n(e.value);case"contains":case"doesnotcontain":return"*"+n(e.value)+"*";default:return e.value}},L.dynamicFilterType=function(e){return{quarter1:"Q1",quarter2:"Q2",quarter3:"Q3",quarter4:"Q4",january:"M1",february:"M2",march:"M3",april:"M4",may:"M5",june:"M6",july:"M7",august:"M8",september:"M9",october:"M10",november:"M11",december:"M12"}[e.toLowerCase()]||e},kendo.deepExtend(kendo.ooxml,{IntlService:z,Workbook:ce,Worksheet:se})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,n,t){(t||n)()}),function(e,define){define("ooxml/main.min",["kendo.core.min","ooxml/kendo-ooxml.min"],e)}(function(){!function(e){var n=kendo.ooxml.Workbook;kendo.ooxml.IntlService.register({toString:kendo.toString
}),kendo.ooxml.Workbook=n.extend({toDataURL:function(){var e=n.fn.toDataURL.call(this);if("string"!=typeof e)throw Error("The toDataURL method can be used only with jsZip 2. Either include jsZip 2 or use the toDataURLAsync method.");return e},toDataURLAsync:function(){var t=e.Deferred(),o=n.fn.toDataURL.call(this);return"string"==typeof o?o=t.resolve(o):o&&o.then&&o.then(function(e){t.resolve(e)},function(){t.reject()}),t.promise()}})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,n,t){(t||n)()}),function(e,define){define("kendo.ooxml.min",["ooxml/main.min"],e)}(function(){},"function"==typeof define&&define.amd?define:function(e,n,t){(t||n)()});
//# sourceMappingURL=kendo.ooxml.min.js.map
