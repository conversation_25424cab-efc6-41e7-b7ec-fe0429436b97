{"version": 3, "sources": ["kendo.autocomplete.js"], "names": ["f", "define", "$", "undefined", "indexOfWordAtCaret", "caretIdx", "text", "separator", "substring", "split", "length", "wordAtCaret", "replaceWordAtCaret", "word", "defaultSeparator", "words", "splice", "push", "join", "kendo", "window", "support", "caret", "activeElement", "_activeElement", "placeholderSupported", "placeholder", "ui", "List", "keys", "DataSource", "data", "ARIA_DISABLED", "ARIA_READONLY", "CHANGE", "DEFAULT", "DISABLED", "READONLY", "FOCUSED", "SELECTED", "STATEDISABLED", "AUTOCOMPLETEVALUE", "browser", "chrome", "HOVER", "ns", "HOVEREVENTS", "proxy", "AutoComplete", "extend", "init", "element", "options", "wrapper", "disabled", "that", "this", "isArray", "dataSource", "fn", "call", "attr", "_wrapper", "_loader", "_clearButton", "_dataSource", "_ignoreCase", "type", "_popup", "addClass", "on", "_keydown", "_keypress", "_search", "_prev", "_accessor", "_oldText", "_placeholder", "_change", "close", "removeClass", "autocomplete", "role", "aria-haspopup", "_clear", "_clearValue", "_enable", "_old", "id", "ul", "_aria", "_initList", "parents", "is", "enable", "listView", "bind", "e", "preventDefault", "_resetFocusItemHandler", "_resetFocusItem", "notify", "_toggleCloseVisibility", "name", "enabled", "suggest", "template", "groupTemplate", "fixedGroupTemplate", "dataTextField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delay", "height", "filter", "ignoreCase", "highlight<PERSON><PERSON><PERSON>", "animation", "virtual", "value", "clearButton", "autoWidth", "popup", "_refresh<PERSON><PERSON><PERSON>", "_unbindDataSource", "_progress<PERSON><PERSON><PERSON>", "_showBusy", "_error<PERSON><PERSON><PERSON>", "_hideBusy", "create", "setDataSource", "events", "setOptions", "listOptions", "_listOptions", "_accessors", "skipUpdateOnBind", "dataValueField", "selectedItemChange", "_editable", "off", "readonly", "disable", "_toggleHover", "removeAttr", "current", "focus", "destroy", "refresh", "select", "li", "_select", "search", "_separator", "accentFoldingFiltering", "clearTimeout", "_typingTimeout", "_open", "_mute", "_filterSource", "toLocaleLowerCase", "toLowerCase", "operator", "field", "one", "_unifySeparators", "idx", "key", "_last", "wordIndex", "selectionEnd", "BACKSPACE", "DELETE", "view", "inArray", "_text", "indexOf", "lastIndexOf", "_click", "item", "dataItem", "dataItemByIndex", "getElementIndex", "_active", "trigger", "val", "done", "_blur", "_clearText", "noop", "index", "scrollTo", "_listBound", "action", "flatView", "groupsLength", "_group", "isActive", "_renderFooter", "_renderNoData", "_toggleNoData", "_toggleHeader", "_resizePopup", "position", "_allowOpening", "unbind", "_touchScroller", "reset", "_makeUnselectable", "callback", "_muted", "_listChange", "_selectValue", "selectedDataItems", "_defaultSeparator", "_preselect", "_inputValue", "oldText", "setValue", "unifyType", "valueUpdated", "_typing", "itemSelected", "typing", "className", "direction", "keyCode", "visible", "DOWN", "_move", "open", "UP", "HOME", "END", "ENTER", "TAB", "ESC", "PAGEDOWN", "PAGEUP", "_hovered", "scrollWith", "screenHeight", "_busy", "_loading", "hide", "_showClear", "setTimeout", "show", "_hideClear", "toggleClass", "document", "Array", "RegExp", "candidate", "insertAfter", "currentTarget", "DOMelement", "parent", "wrap", "style", "cssText", "css", "width", "_focused", "_inputWrapper", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,sBACH,aACA,wBACA,qBACDD,IACL,WAglBE,MA1jBC,UAAUE,EAAGC,GAEV,QAASC,GAAmBC,EAAUC,EAAMC,GACxC,MAAOA,GAAYD,EAAKE,UAAU,EAAGH,GAAUI,MAAMF,GAAWG,OAAS,EAAI,EAEjF,QAASC,GAAYN,EAAUC,EAAMC,GACjC,MAAOD,GAAKG,MAAMF,GAAWH,EAAmBC,EAAUC,EAAMC,IAEpE,QAASK,GAAmBP,EAAUC,EAAMO,EAAMN,EAAWO,GACzD,GAAIC,GAAQT,EAAKG,MAAMF,EAKvB,OAJAQ,GAAMC,OAAOZ,EAAmBC,EAAUC,EAAMC,GAAY,EAAGM,GAC3DN,GAAyC,KAA5BQ,EAAMA,EAAML,OAAS,IAClCK,EAAME,KAAK,IAERF,EAAMG,KAAKJ,GAdzB,GACOK,GAAQC,OAAOD,MAAOE,EAAUF,EAAME,QAASC,EAAQH,EAAMG,MAAOC,EAAgBJ,EAAMK,eAAgBC,EAAuBJ,EAAQK,YAAaC,EAAKR,EAAMQ,GAAIC,EAAOD,EAAGC,KAAMC,EAAOV,EAAMU,KAAMC,EAAaX,EAAMY,KAAKD,WAAYE,EAAgB,gBAAiBC,EAAgB,gBAAiBC,EAAS,SAAUC,EAAU,kBAAmBC,EAAW,WAAYC,EAAW,WAAYC,EAAU,kBAAmBC,EAAW,mBAAoBC,EAAgB,mBAAoBC,EAAoBpB,EAAQqB,QAAQC,OAAS,WAAa,MAAOC,EAAQ,gBAAiBC,EAAK,qBAAsBC,EAAc,aAAeD,EAAK,cAAgBA,EAAIE,EAAQ7C,EAAE6C,MAejqBC,EAAepB,EAAKqB,QACpBC,KAAM,SAAUC,EAASC,GACrB,GAAiBC,GAASC,EAAtBC,EAAOC,IACXD,GAAKV,GAAKA,EACVO,EAAUlD,EAAEuD,QAAQL,IAAaM,WAAYN,GAAYA,EACzDxB,EAAK+B,GAAGT,KAAKU,KAAKL,EAAMJ,EAASC,GACjCD,EAAUI,EAAKJ,QACfC,EAAUG,EAAKH,QACfA,EAAQ1B,YAAc0B,EAAQ1B,aAAeyB,EAAQU,KAAK,eACtDpC,GACA0B,EAAQU,KAAK,cAAeT,EAAQ1B,aAExC6B,EAAKO,WACLP,EAAKQ,UACLR,EAAKS,eACLT,EAAKU,cACLV,EAAKW,cACLf,EAAQ,GAAGgB,KAAO,OAClBd,EAAUE,EAAKF,QACfE,EAAKa,SACLjB,EAAQkB,SAAS,WAAWC,GAAG,UAAYzB,EAAIE,EAAMQ,EAAKgB,SAAUhB,IAAOe,GAAG,WAAazB,EAAIE,EAAMQ,EAAKiB,UAAWjB,IAAOe,GAAG,QAAUzB,EAAIE,EAAMQ,EAAKkB,QAASlB,IAAOe,GAAG,QAAUzB,EAAIE,EAAMQ,EAAKkB,QAASlB,IAAOe,GAAG,QAAUzB,EAAI,WACjOU,EAAKmB,MAAQnB,EAAKoB,YAClBpB,EAAKqB,SAAWrB,EAAKmB,MACrBnB,EAAKsB,cAAa,GAClBxB,EAAQgB,SAAS/B,KAClBgC,GAAG,WAAazB,EAAI,WACnBU,EAAKuB,UACLvB,EAAKsB,eACLtB,EAAKwB,QACL1B,EAAQ2B,YAAY1C,KACrBuB,MACCoB,aAAcxC,EACdyC,KAAM,UACNC,iBAAiB,IAErB5B,EAAK6B,OAAOd,GAAG,QAAUzB,EAAK,YAAcA,EAAIE,EAAMQ,EAAK8B,YAAa9B,IACxEA,EAAK+B,UACL/B,EAAKgC,KAAOhC,EAAKoB,YACbxB,EAAQ,GAAGqC,IACXrC,EAAQU,KAAK,YAAaN,EAAKkC,GAAG,GAAGD,IAEzCjC,EAAKmC,QACLnC,EAAKsB,eACLtB,EAAKoC,YACLrC,EAAWpD,EAAEqD,EAAKJ,SAASyC,QAAQ,YAAYC,GAAG,aAC9CvC,GACAC,EAAKuC,QAAO,GAEhBvC,EAAKwC,SAASC,KAAK,QAAS,SAAUC,GAClCA,EAAEC,mBAEN3C,EAAK4C,uBAAyBjG,EAAE6C,MAAMQ,EAAK6C,gBAAiB7C,GAC5DpC,EAAMkF,OAAO9C,GACbA,EAAK+C,0BAETlD,SACImD,KAAM,eACNC,SAAS,EACTC,SAAS,EACTC,SAAU,GACVC,cAAe,UACfC,mBAAoB,UACpBC,cAAe,GACfC,UAAW,EACXC,kBAAkB,EAClBC,MAAO,IACPC,OAAQ,IACRC,OAAQ,aACRC,YAAY,EACZC,gBAAgB,EAChB7G,UAAW,KACXmB,YAAa,GACb2F,aACAC,SAAS,EACTC,MAAO,KACPC,aAAa,EACbC,WAAW,EACXC,MAAO,MAEXzD,YAAa,WACT,GAAIV,GAAOC,IACPD,GAAKG,YAAcH,EAAKoE,gBACxBpE,EAAKqE,qBAELrE,EAAKsE,iBAAmB9E,EAAMQ,EAAKuE,UAAWvE,GAC9CA,EAAKwE,cAAgBhF,EAAMQ,EAAKyE,UAAWzE,IAE/CA,EAAKG,WAAa5B,EAAWmG,OAAO1E,EAAKH,QAAQM,YAAYsC,KAAK,WAAYzC,EAAKsE,kBAAkB7B,KAAK,QAASzC,EAAKwE,gBAE5HG,cAAe,SAAUxE,GACrBF,KAAKJ,QAAQM,WAAaA,EAC1BF,KAAKS,cACLT,KAAKuC,SAASmC,cAAc1E,KAAKE,aAErCyE,QACI,OACA,QACAjG,EACA,SACA,YACA,cACA,aAEJkG,WAAY,SAAUhF,GAClB,GAAIiF,GAAc7E,KAAK8E,aAAalF,EACpCxB,GAAK+B,GAAGyE,WAAWxE,KAAKJ,KAAMJ,GAC9BI,KAAKuC,SAASqC,WAAWC,GACzB7E,KAAK+E,aACL/E,KAAKkC,QACLlC,KAAKQ,gBAETsE,aAAc,SAAUlF,GACpB,GAAIiF,GAAczG,EAAK+B,GAAG2E,aAAa1E,KAAKJ,KAAMtD,EAAE+C,OAAOG,GAAWoF,kBAAkB,IAGxF,OAFAH,GAAYI,eAAiBJ,EAAYxB,cACzCwB,EAAYK,mBAAqB,KAC1BL,GAEXM,UAAW,SAAUvF,GACjB,GAAIG,GAAOC,KAAML,EAAUI,EAAKJ,QAASE,EAAUE,EAAKF,QAAQuF,IAAI/F,GAAKgG,EAAWzF,EAAQyF,SAAUC,EAAU1F,EAAQ0F,OACnHD,IAAaC,GAIdzF,EAAQgB,SAASyE,EAAUtG,EAAgBL,GAAS6C,YAAY8D,EAAU3G,EAAUK,GACpFW,EAAQU,KAAKzB,EAAU0G,GAASjF,KAAKxB,EAAUwG,GAAUhF,KAAK7B,EAAe8G,GAASjF,KAAK5B,EAAe4G,KAJ1GxF,EAAQgB,SAASlC,GAAS6C,YAAYxC,GAAe8B,GAAGxB,EAAaS,EAAKwF,cAC1E5F,EAAQ6F,WAAW5G,GAAU4G,WAAW3G,GAAUwB,KAAK7B,GAAe,GAAO6B,KAAK5B,GAAe,KAMzG8C,MAAO,WAAA,GACCxB,GAAOC,KACPyF,EAAU1F,EAAKwC,SAASmD,OACxBD,IACAA,EAAQjE,YAAYzC,GAExBgB,EAAKmE,MAAM3C,SAEfoE,QAAS,WACL,GAAI5F,GAAOC,IACXD,GAAKJ,QAAQyF,IAAI/F,GACjBU,EAAK6B,OAAOwD,IAAI/F,GAChBU,EAAKF,QAAQuF,IAAI/F,GACjBjB,EAAK+B,GAAGwF,QAAQvF,KAAKL,IAEzB6F,QAAS,WACL5F,KAAKuC,SAASqD,WAElBC,OAAQ,SAAUC,GACd9F,KAAK+F,QAAQD,IAEjBE,OAAQ,SAAU3I,GACd,GAAyGH,GAArG6C,EAAOC,KAAMJ,EAAUG,EAAKH,QAAS+D,EAAa/D,EAAQ+D,WAAY5G,EAAYgD,EAAKkG,aAAsBC,EAAyBnG,EAAKG,WAAWN,QAAQsG,sBAClK7I,GAAOA,GAAQ0C,EAAKoB,YACpBgF,aAAapG,EAAKqG,gBACdrJ,IACAM,EAAOF,EAAYW,EAAMiC,EAAKJ,SAAS,GAAItC,EAAMN,IAErDG,EAASG,EAAKH,SACT0C,EAAQ2D,mBAAqBrG,GAAUA,GAAU0C,EAAQ0D,aAC1DvD,EAAKsG,OAAQ,EACbtG,EAAKuG,MAAM,WACPtG,KAAKuC,SAASwB,YAElBhE,EAAKwG,eACDxC,MAAOJ,EAAauC,EAAyB7I,EAAKmJ,kBAAkBN,GAA0B7I,EAAKoJ,cAAgBpJ,EACnHqJ,SAAU9G,EAAQ8D,OAClBiD,MAAO/G,EAAQyD,cACfM,WAAYA,IAEhB5D,EAAK6G,IAAI,QAASlK,EAAE6C,MAAMQ,EAAK8G,iBAAkB9G,KAErDA,EAAK+C,0BAETG,QAAS,SAAU5F,GACf,GAA0QyJ,GAAtQ/G,EAAOC,KAAM+G,EAAMhH,EAAKiH,MAAOjD,EAAQhE,EAAKoB,YAAaxB,EAAUI,EAAKJ,QAAQ,GAAI9C,EAAWiB,EAAM6B,GAAS,GAAI5C,EAAYgD,EAAKkG,aAAc1I,EAAQwG,EAAM9G,MAAMF,GAAYkK,EAAYrK,EAAmBC,EAAUkH,EAAOhH,GAAYmK,EAAerK,EAAeqJ,EAAyBnG,EAAKG,WAAWN,QAAQsG,sBAChU,OAAIa,IAAO1I,EAAK8I,WAAaJ,GAAO1I,EAAK+I,QACrCrH,EAAKiH,MAAQrK,EACb,IAEJU,EAAOA,GAAQ,GACK,gBAATA,KACHA,EAAK,KACLA,EAAO0C,EAAKG,WAAWmH,OAAOjJ,EAAKkJ,QAAQjK,EAAK,GAAI0C,EAAKkC,GAAG,MAEhE5E,EAAOA,EAAO0C,EAAKwH,MAAMlK,GAAQ,IAEjCR,GAAY,IACZA,GAAYqJ,EAAyBnC,EAAMyC,kBAAkBN,GAA0BnC,EAAM0C,eAAee,QAAQtB,EAAyB7I,EAAKmJ,kBAAkBN,GAA0B7I,EAAKoJ,eAAiB,GAExNK,EAAM/C,EAAM/G,UAAU,EAAGH,GAAU4K,YAAY1K,GAC/C+J,EAAMA,KAAWjK,GAAYiK,EAAM/J,EAAUG,QAAUL,EACvDkH,EAAQxG,EAAM0J,GAAWjK,UAAU,EAAG8J,GAClCzJ,IACAA,EAAOA,GAAAA,EACPyJ,GAAOZ,EAAyB7I,EAAKmJ,kBAAkBN,GAA0B7I,EAAKoJ,eAAee,QAAQtB,EAAyBnC,EAAMyC,kBAAkBN,GAA0BnC,EAAM0C,eAC1LK,OACAzJ,EAAOA,EAAKL,UAAU8J,EAAM/C,EAAM7G,QAClCgK,EAAerK,EAAWQ,EAAKH,OAC/B6G,GAAS1G,GAETN,GAAyC,KAA5BQ,EAAMA,EAAML,OAAS,IAClCK,EAAME,KAAK,KAGnBF,EAAM0J,GAAalD,EACnBhE,EAAKoB,UAAU5D,EAAMG,KAAKX,GAAa,KACnC4C,IAAY5B,KACZD,EAAM6B,EAAS9C,EAAUqK,GA5B7B7J,IA+BJ0G,MAAO,SAAUA,GACb,MAAIA,KAAUpH,EAMHqD,KAAKmB,aALZnB,KAAKuC,SAASwB,MAAMA,GACpB/D,KAAKmB,UAAU4C,GACf/D,KAAK+B,KAAO/B,KAAKmB,YACjBnB,KAAKoB,SAAWpB,KAAKmB,YAIzBnB,KAAK8C,yBAPD9C,IASR0H,OAAQ,SAAUjF,GAAV,GACAkF,GAAOlF,EAAEkF,KACT5H,EAAOC,KACPL,EAAUI,EAAKJ,QACfiI,EAAW7H,EAAKwC,SAASsF,gBAAgB9H,EAAKwC,SAASuF,gBAAgBH,GAG3E,OAFAlF,GAAEC,iBACF3C,EAAKgI,SAAU,EACXhI,EAAKiI,QAAQ,UACTJ,SAAUA,EACVD,KAAMA,KAEV5H,EAAKwB,QACL,IAEJxB,EAAKqB,SAAWzB,EAAQsI,MACxBlI,EAAKgG,QAAQ4B,GAAMO,KAAK,WACpBnI,EAAKoI,QACLrK,EAAM6B,EAASA,EAAQsI,MAAM/K,UAHjC6C,IAMJqI,WAAY1L,EAAE2L,KACdzF,gBAAiB,WACb,GAAI0F,GAAQtI,KAAKJ,QAAQgE,eAAiB,IACtC5D,MAAKJ,QAAQkE,SACb9D,KAAKuC,SAASgG,SAAS,GAE3BvI,KAAKuC,SAASmD,MAAM4C,IAExBE,WAAY,WAAA,GAQJC,GAPA1I,EAAOC,KACPkE,EAAQnE,EAAKmE,MACbtE,EAAUG,EAAKH,QACfrB,EAAOwB,EAAKG,WAAWwI,WACvBxL,EAASqB,EAAKrB,OACdyL,EAAe5I,EAAKG,WAAW0I,OAAO1L,OACtC2L,EAAW9I,EAAKJ,QAAQ,KAAO5B,GAEnCgC,GAAK+I,gBACL/I,EAAKgJ,gBACLhJ,EAAKiJ,eAAe9L,GACpB6C,EAAKkJ,gBAAgBN,KAAkBzL,GACvC6C,EAAKmJ,eACLhF,EAAMiF,WACFjM,GACI0C,EAAQqD,SAAW4F,GACnB9I,EAAKkD,QAAQ1E,EAAK,IAGtBwB,EAAKsG,QACLtG,EAAKsG,OAAQ,EACboC,EAAS1I,EAAKqJ,gBAAkB,OAAS,QACrCrJ,EAAKqG,iBAAmByC,IACxBJ,EAAS,SAETvL,IACA6C,EAAK6C,kBACDhD,EAAQkE,SACR/D,EAAKmE,MAAMmF,OAAO,WAAYtJ,EAAK4C,wBAAwBiE,IAAI,WAAY7G,EAAK4C,yBAGxFuB,EAAMuE,KACN1I,EAAKqG,eAAiBzJ,GAEtBoD,EAAKuJ,gBACLvJ,EAAKuJ,eAAeC,QAExBxJ,EAAKyE,YACLzE,EAAKyJ,oBACLzJ,EAAKiI,QAAQ,cAEjB1B,MAAO,SAAUmD,GACbzJ,KAAK0J,QAAS,EACdD,EAASrJ,KAAKJ,MACdA,KAAK0J,QAAS,GAElBC,YAAa,WACT,GAAId,GAAW7I,KAAK+H,SAAW/H,KAAKL,QAAQ,KAAO5B,GAC/C8K,KAAa7I,KAAK0J,QAClB1J,KAAK4J,aAAa5J,KAAKuC,SAASsH,oBAAoB,KAG5DD,aAAc,SAAUhC,GAAV,GACN7K,GAAYiD,KAAKiG,aACjBnJ,EAAO,EACP8K,KACA9K,EAAOkD,KAAKuH,MAAMK,IAET,OAAT9K,IACAA,EAAO,IAEPC,IACAD,EAAOM,EAAmBU,EAAMkC,KAAKL,SAAS,GAAIK,KAAKmB,YAAarE,EAAMC,EAAWiD,KAAK8J,sBAE9F9J,KAAKkB,MAAQpE,EACbkD,KAAKmB,UAAUrE,GACfkD,KAAKqB,gBAETwF,iBAAkB,WAEd,MADA7G,MAAKmB,UAAUnB,KAAK+D,QAAQ9G,MAAM+C,KAAKiG,cAAcvI,KAAKsC,KAAK8J,sBACxD9J,MAEX+J,WAAY,SAAUhG,EAAOjH,GACzBkD,KAAKgK,YAAYlN,GACjBkD,KAAKmB,UAAU4C,GACf/D,KAAK+B,KAAO/B,KAAKiK,QAAUjK,KAAKmB,YAChCnB,KAAKuC,SAAS2H,SAASnG,GACvB/D,KAAKqB,gBAETC,QAAS,WAAA,GACDvB,GAAOC,KACP+D,EAAQhE,EAAK8G,mBAAmB9C,QAChCiE,EAAUjE,IAAU3F,EAAK+L,UAAUpK,EAAKgC,WAAagC,IACrDqG,EAAepC,IAAYjI,EAAKsK,QAChCC,EAAevK,EAAKqB,WAAa2C,CACrChE,GAAKgC,KAAOgC,EACZhE,EAAKqB,SAAW2C,GACZqG,GAAgBE,IAChBvK,EAAKJ,QAAQqI,QAAQtJ,GAErBsJ,GACAjI,EAAKiI,QAAQtJ,GAEjBqB,EAAKwK,QAAS,EACdxK,EAAK+C,0BAET3B,UAAW,SAAU4C,GACjB,GAAIhE,GAAOC,KAAML,EAAUI,EAAKJ,QAAQ,EACxC,OAAIoE,KAAUpH,GAIVoH,EAAQpE,EAAQoE,MACZpE,EAAQ6K,UAAUhD,QAAQ,kBACtBzD,IAAUhE,EAAKH,QAAQ1B,YAChB,GAKR6F,IAXPpE,EAAQoE,MAAkB,OAAVA,EAAiB,GAAKA,EACtChE,EAAKsB,eADL1B,IAcRoB,SAAU,SAAU0B,GAAV,GAsCMmF,GAmBJ6C,EAxDJ1K,EAAOC,KACP+G,EAAMtE,EAAEiI,QACRnI,EAAWxC,EAAKwC,SAChBoI,EAAU5K,EAAKmE,MAAMyG,UACrBlF,EAAUlD,EAASmD,OAEvB,IADA3F,EAAKiH,MAAQD,EACTA,IAAQ1I,EAAKuM,KACTD,EACA3K,KAAK6K,MAAMpF,EAAU,YAAc,cAC5B1F,EAAKgE,SACZhE,EAAKwG,eACDxC,MAAOhE,EAAK4D,WAAa5D,EAAKgE,QAAQ0C,cAAgB1G,EAAKgE,QAC3D2C,SAAU3G,EAAKH,QAAQ8D,OACvBiD,MAAO5G,EAAKH,QAAQyD,cACpBM,WAAY5D,EAAK4D,aAClBuE,KAAK,WACAnI,EAAKqJ,kBACLrJ,EAAK6C,kBACL7C,EAAKmE,MAAM4G,UAIvBrI,EAAEC,qBACC,IAAIqE,IAAQ1I,EAAK0M,GAChBJ,GACA3K,KAAK6K,MAAMpF,EAAU,YAAc,aAEvChD,EAAEC,qBACC,IAAIqE,IAAQ1I,EAAK2M,KACpBhL,KAAK6K,MAAM,kBACR,IAAI9D,IAAQ1I,EAAK4M,IACpBjL,KAAK6K,MAAM,iBACR,IAAI9D,IAAQ1I,EAAK6M,OAASnE,IAAQ1I,EAAK8M,IAAK,CAI/C,GAHIpE,IAAQ1I,EAAK6M,OAASP,GACtBlI,EAAEC,iBAEFiI,GAAWlF,EAAS,CAEpB,GADImC,EAAWrF,EAASsF,gBAAgBtF,EAASuF,gBAAgBrC,IAC7D1F,EAAKiI,QAAQ,UACTJ,SAAUA,EACVD,KAAMlC,IAEV,MAEJzF,MAAK+F,QAAQN,GAEjBzF,KAAKmI,YACEpB,KAAQ1I,EAAK+M,KAChBT,EACAlI,EAAEC,iBAEF3C,EAAK8B,cAET9B,EAAKwB,UACExB,EAAKmE,MAAMyG,WAAc5D,IAAQ1I,EAAKgN,UAAYtE,IAAQ1I,EAAKiN,QAKtEvL,EAAKmE,MAAMqH,UAAW,EACtBxL,EAAKkB,YALLwB,EAAEC,iBACE+H,EAAY1D,IAAQ1I,EAAKgN,SAAW,KACxC9I,EAASiJ,WAAWf,EAAYlI,EAASkJ,kBAMjDzK,UAAW,WACPhB,KAAKoB,SAAWpB,KAAKL,QAAQsI,MAC7BjI,KAAKqK,SAAU,GAEnBQ,MAAO,SAAUpC,GACbzI,KAAKuC,SAASkG,KACVzI,KAAKJ,QAAQqD,SACbjD,KAAKiD,QAAQjD,KAAKuC,SAASmD,UAGnClB,UAAW,WACP,GAAIzE,GAAOC,IACXmG,cAAapG,EAAK2L,OAClB3L,EAAK4L,SAASC,OACd7L,EAAKJ,QAAQU,KAAK,aAAa,GAC/BN,EAAK2L,MAAQ,KACb3L,EAAK8L,cAETvH,UAAW,WACP,GAAIvE,GAAOC,IACPD,GAAK2L,QAGT3L,EAAK2L,MAAQI,WAAW,WACpB/L,EAAKJ,QAAQU,KAAK,aAAa,GAC/BN,EAAK4L,SAASI,OACdhM,EAAKiM,cACN,OAEP3K,aAAc,SAAU0K,GACpB,IAAI9N,EAAJ,CAGA,GAAiF8F,GAA7EhE,EAAOC,KAAML,EAAUI,EAAKJ,QAASzB,EAAc6B,EAAKH,QAAQ1B,WACpE,IAAIA,EAAa,CAYb,GAXA6F,EAAQpE,EAAQsI,MACZ8D,IAASpP,IACToP,GAAQhI,GAEPgI,IAEG7N,EADA6F,IAAU7F,EACI6F,EAEA,IAGlBA,IAAUhE,EAAKgC,OAASgK,EACxB,MAEJpM,GAAQsM,YAAY,aAAcF,GAAM9D,IAAI/J,GACvCA,GAAeyB,EAAQ,KAAOuM,SAASnO,eACxCD,EAAM6B,EAAQ,GAAI,EAAG,MAIjCsG,WAAY,WACR,GAAIlJ,GAAYiD,KAAKJ,QAAQ7C,SAC7B,OAAIA,aAAqBoP,OACVC,OAAOrP,EAAUW,KAAK,KAAM,MAEpCX,GAEX+M,kBAAmB,WACf,GAAI/M,GAAYiD,KAAKJ,QAAQ7C,SAC7B,OAAIA,aAAqBoP,OACdpP,EAAU,GAEdA,GAEXiN,YAAa,WACT,MAAOhK,MAAKL,QAAQsI,OAExBhH,QAAS,WACL,GAAIlB,GAAOC,IACXmG,cAAapG,EAAKqG,gBAClBrG,EAAKqG,eAAiB0F,WAAW,WACzB/L,EAAKmB,QAAUnB,EAAKoB,cACpBpB,EAAKmB,MAAQnB,EAAKoB,YAClBpB,EAAKiG,WAEVjG,EAAKH,QAAQ4D,QAEpBuC,QAAS,SAAUsG,GACf,GAAItM,GAAOC,IAEX,OADAD,GAAKgI,SAAU,EACRhI,EAAKwC,SAASsD,OAAOwG,GAAWnE,KAAK,WACxCnI,EAAKgI,SAAU,KAGvBxH,QAAS,WACLP,KAAK2L,SAAWjP,EAAE,iEAAiE4P,YAAYtM,KAAKL,UAExGa,aAAc,WACVpC,EAAK+B,GAAGK,aAAaJ,KAAKJ,MACtBA,KAAKJ,QAAQoE,cACbhE,KAAK4B,OAAO0K,YAAYtM,KAAKL,SAC7BK,KAAKH,QAAQgB,SAAS,8BAG9B0E,aAAc,SAAU9C,GACpB/F,EAAE+F,EAAE8J,eAAeN,YAAY7M,EAAkB,eAAXqD,EAAE9B,OAE5CmC,uBAAwB,WAChB9C,KAAK+D,QACL/D,KAAK6L,aAEL7L,KAAKgM,cAGb1L,SAAU,WACN,GAAkET,GAA9DE,EAAOC,KAAML,EAAUI,EAAKJ,QAAS6M,EAAa7M,EAAQ,EAC9DE,GAAUF,EAAQ8M,SACb5M,EAAQwC,GAAG,mBACZxC,EAAUF,EAAQ+M,KAAK,YAAYD,UAEvC5M,EAAQQ,KAAK,eACbR,EAAQQ,KAAK,OAAQ,gBACrBR,EAAQ,GAAG8M,MAAMC,QAAUJ,EAAWG,MAAMC,QAC5CjN,EAAQkN,KACJC,MAAO,GACPrJ,OAAQ+I,EAAWG,MAAMlJ,SAE7B1D,EAAKgN,SAAWhN,EAAKJ,QACrBI,EAAKF,QAAUA,EAAQgB,SAAS,2BAA2BA,SAAS2L,EAAWhC,WAC/EzK,EAAKiN,cAAgBtQ,EAAEmD,EAAQ,MAGvC1B,GAAG8O,OAAOzN,IACZ5B,OAAOD,MAAMuP,QACRtP,OAAOD,OACE,kBAAVlB,SAAwBA,OAAO0Q,IAAM1Q,OAAS,SAAU2Q,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.autocomplete.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.autocomplete', [\n        'kendo.list',\n        'kendo.mobile.scroller',\n        'kendo.virtuallist'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'autocomplete',\n        name: 'AutoComplete',\n        category: 'web',\n        description: 'The AutoComplete widget provides suggestions depending on the typed text.It also allows multiple value entries.',\n        depends: ['list'],\n        features: [\n            {\n                id: 'mobile-scroller',\n                name: 'Mobile scroller',\n                description: 'Support for kinetic scrolling in mobile device',\n                depends: ['mobile.scroller']\n            },\n            {\n                id: 'virtualization',\n                name: 'VirtualList',\n                description: 'Support for virtualization',\n                depends: ['virtuallist']\n            }\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, support = kendo.support, caret = kendo.caret, activeElement = kendo._activeElement, placeholderSupported = support.placeholder, ui = kendo.ui, List = ui.List, keys = kendo.keys, DataSource = kendo.data.DataSource, ARIA_DISABLED = 'aria-disabled', ARIA_READONLY = 'aria-readonly', CHANGE = 'change', DEFAULT = 'k-state-default', DISABLED = 'disabled', READONLY = 'readonly', FOCUSED = 'k-state-focused', SELECTED = 'k-state-selected', STATEDISABLED = 'k-state-disabled', AUTOCOMPLETEVALUE = support.browser.chrome ? 'disabled' : 'off', HOVER = 'k-state-hover', ns = '.kendoAutoComplete', HOVEREVENTS = 'mouseenter' + ns + ' mouseleave' + ns, proxy = $.proxy;\n        function indexOfWordAtCaret(caretIdx, text, separator) {\n            return separator ? text.substring(0, caretIdx).split(separator).length - 1 : 0;\n        }\n        function wordAtCaret(caretIdx, text, separator) {\n            return text.split(separator)[indexOfWordAtCaret(caretIdx, text, separator)];\n        }\n        function replaceWordAtCaret(caretIdx, text, word, separator, defaultSeparator) {\n            var words = text.split(separator);\n            words.splice(indexOfWordAtCaret(caretIdx, text, separator), 1, word);\n            if (separator && words[words.length - 1] !== '') {\n                words.push('');\n            }\n            return words.join(defaultSeparator);\n        }\n        var AutoComplete = List.extend({\n            init: function (element, options) {\n                var that = this, wrapper, disabled;\n                that.ns = ns;\n                options = $.isArray(options) ? { dataSource: options } : options;\n                List.fn.init.call(that, element, options);\n                element = that.element;\n                options = that.options;\n                options.placeholder = options.placeholder || element.attr('placeholder');\n                if (placeholderSupported) {\n                    element.attr('placeholder', options.placeholder);\n                }\n                that._wrapper();\n                that._loader();\n                that._clearButton();\n                that._dataSource();\n                that._ignoreCase();\n                element[0].type = 'text';\n                wrapper = that.wrapper;\n                that._popup();\n                element.addClass('k-input').on('keydown' + ns, proxy(that._keydown, that)).on('keypress' + ns, proxy(that._keypress, that)).on('input' + ns, proxy(that._search, that)).on('paste' + ns, proxy(that._search, that)).on('focus' + ns, function () {\n                    that._prev = that._accessor();\n                    that._oldText = that._prev;\n                    that._placeholder(false);\n                    wrapper.addClass(FOCUSED);\n                }).on('focusout' + ns, function () {\n                    that._change();\n                    that._placeholder();\n                    that.close();\n                    wrapper.removeClass(FOCUSED);\n                }).attr({\n                    autocomplete: AUTOCOMPLETEVALUE,\n                    role: 'textbox',\n                    'aria-haspopup': true\n                });\n                that._clear.on('click' + ns + ' touchend' + ns, proxy(that._clearValue, that));\n                that._enable();\n                that._old = that._accessor();\n                if (element[0].id) {\n                    element.attr('aria-owns', that.ul[0].id);\n                }\n                that._aria();\n                that._placeholder();\n                that._initList();\n                disabled = $(that.element).parents('fieldset').is(':disabled');\n                if (disabled) {\n                    that.enable(false);\n                }\n                that.listView.bind('click', function (e) {\n                    e.preventDefault();\n                });\n                that._resetFocusItemHandler = $.proxy(that._resetFocusItem, that);\n                kendo.notify(that);\n                that._toggleCloseVisibility();\n            },\n            options: {\n                name: 'AutoComplete',\n                enabled: true,\n                suggest: false,\n                template: '',\n                groupTemplate: '#:data#',\n                fixedGroupTemplate: '#:data#',\n                dataTextField: '',\n                minLength: 1,\n                enforceMinLength: false,\n                delay: 200,\n                height: 200,\n                filter: 'startswith',\n                ignoreCase: true,\n                highlightFirst: false,\n                separator: null,\n                placeholder: '',\n                animation: {},\n                virtual: false,\n                value: null,\n                clearButton: true,\n                autoWidth: false,\n                popup: null\n            },\n            _dataSource: function () {\n                var that = this;\n                if (that.dataSource && that._refreshHandler) {\n                    that._unbindDataSource();\n                } else {\n                    that._progressHandler = proxy(that._showBusy, that);\n                    that._errorHandler = proxy(that._hideBusy, that);\n                }\n                that.dataSource = DataSource.create(that.options.dataSource).bind('progress', that._progressHandler).bind('error', that._errorHandler);\n            },\n            setDataSource: function (dataSource) {\n                this.options.dataSource = dataSource;\n                this._dataSource();\n                this.listView.setDataSource(this.dataSource);\n            },\n            events: [\n                'open',\n                'close',\n                CHANGE,\n                'select',\n                'filtering',\n                'dataBinding',\n                'dataBound'\n            ],\n            setOptions: function (options) {\n                var listOptions = this._listOptions(options);\n                List.fn.setOptions.call(this, options);\n                this.listView.setOptions(listOptions);\n                this._accessors();\n                this._aria();\n                this._clearButton();\n            },\n            _listOptions: function (options) {\n                var listOptions = List.fn._listOptions.call(this, $.extend(options, { skipUpdateOnBind: true }));\n                listOptions.dataValueField = listOptions.dataTextField;\n                listOptions.selectedItemChange = null;\n                return listOptions;\n            },\n            _editable: function (options) {\n                var that = this, element = that.element, wrapper = that.wrapper.off(ns), readonly = options.readonly, disable = options.disable;\n                if (!readonly && !disable) {\n                    wrapper.addClass(DEFAULT).removeClass(STATEDISABLED).on(HOVEREVENTS, that._toggleHover);\n                    element.removeAttr(DISABLED).removeAttr(READONLY).attr(ARIA_DISABLED, false).attr(ARIA_READONLY, false);\n                } else {\n                    wrapper.addClass(disable ? STATEDISABLED : DEFAULT).removeClass(disable ? DEFAULT : STATEDISABLED);\n                    element.attr(DISABLED, disable).attr(READONLY, readonly).attr(ARIA_DISABLED, disable).attr(ARIA_READONLY, readonly);\n                }\n            },\n            close: function () {\n                var that = this;\n                var current = that.listView.focus();\n                if (current) {\n                    current.removeClass(SELECTED);\n                }\n                that.popup.close();\n            },\n            destroy: function () {\n                var that = this;\n                that.element.off(ns);\n                that._clear.off(ns);\n                that.wrapper.off(ns);\n                List.fn.destroy.call(that);\n            },\n            refresh: function () {\n                this.listView.refresh();\n            },\n            select: function (li) {\n                this._select(li);\n            },\n            search: function (word) {\n                var that = this, options = that.options, ignoreCase = options.ignoreCase, separator = that._separator(), length, accentFoldingFiltering = that.dataSource.options.accentFoldingFiltering;\n                word = word || that._accessor();\n                clearTimeout(that._typingTimeout);\n                if (separator) {\n                    word = wordAtCaret(caret(that.element)[0], word, separator);\n                }\n                length = word.length;\n                if (!options.enforceMinLength && !length || length >= options.minLength) {\n                    that._open = true;\n                    that._mute(function () {\n                        this.listView.value([]);\n                    });\n                    that._filterSource({\n                        value: ignoreCase ? accentFoldingFiltering ? word.toLocaleLowerCase(accentFoldingFiltering) : word.toLowerCase() : word,\n                        operator: options.filter,\n                        field: options.dataTextField,\n                        ignoreCase: ignoreCase\n                    });\n                    that.one('close', $.proxy(that._unifySeparators, that));\n                }\n                that._toggleCloseVisibility();\n            },\n            suggest: function (word) {\n                var that = this, key = that._last, value = that._accessor(), element = that.element[0], caretIdx = caret(element)[0], separator = that._separator(), words = value.split(separator), wordIndex = indexOfWordAtCaret(caretIdx, value, separator), selectionEnd = caretIdx, idx, accentFoldingFiltering = that.dataSource.options.accentFoldingFiltering;\n                if (key == keys.BACKSPACE || key == keys.DELETE) {\n                    that._last = undefined;\n                    return;\n                }\n                word = word || '';\n                if (typeof word !== 'string') {\n                    if (word[0]) {\n                        word = that.dataSource.view()[List.inArray(word[0], that.ul[0])];\n                    }\n                    word = word ? that._text(word) : '';\n                }\n                if (caretIdx <= 0) {\n                    caretIdx = (accentFoldingFiltering ? value.toLocaleLowerCase(accentFoldingFiltering) : value.toLowerCase()).indexOf(accentFoldingFiltering ? word.toLocaleLowerCase(accentFoldingFiltering) : word.toLowerCase()) + 1;\n                }\n                idx = value.substring(0, caretIdx).lastIndexOf(separator);\n                idx = idx > -1 ? caretIdx - (idx + separator.length) : caretIdx;\n                value = words[wordIndex].substring(0, idx);\n                if (word) {\n                    word = word.toString();\n                    idx = (accentFoldingFiltering ? word.toLocaleLowerCase(accentFoldingFiltering) : word.toLowerCase()).indexOf(accentFoldingFiltering ? value.toLocaleLowerCase(accentFoldingFiltering) : value.toLowerCase());\n                    if (idx > -1) {\n                        word = word.substring(idx + value.length);\n                        selectionEnd = caretIdx + word.length;\n                        value += word;\n                    }\n                    if (separator && words[words.length - 1] !== '') {\n                        words.push('');\n                    }\n                }\n                words[wordIndex] = value;\n                that._accessor(words.join(separator || ''));\n                if (element === activeElement()) {\n                    caret(element, caretIdx, selectionEnd);\n                }\n            },\n            value: function (value) {\n                if (value !== undefined) {\n                    this.listView.value(value);\n                    this._accessor(value);\n                    this._old = this._accessor();\n                    this._oldText = this._accessor();\n                } else {\n                    return this._accessor();\n                }\n                this._toggleCloseVisibility();\n            },\n            _click: function (e) {\n                var item = e.item;\n                var that = this;\n                var element = that.element;\n                var dataItem = that.listView.dataItemByIndex(that.listView.getElementIndex(item));\n                e.preventDefault();\n                that._active = true;\n                if (that.trigger('select', {\n                        dataItem: dataItem,\n                        item: item\n                    })) {\n                    that.close();\n                    return;\n                }\n                that._oldText = element.val();\n                that._select(item).done(function () {\n                    that._blur();\n                    caret(element, element.val().length);\n                });\n            },\n            _clearText: $.noop,\n            _resetFocusItem: function () {\n                var index = this.options.highlightFirst ? 0 : -1;\n                if (this.options.virtual) {\n                    this.listView.scrollTo(0);\n                }\n                this.listView.focus(index);\n            },\n            _listBound: function () {\n                var that = this;\n                var popup = that.popup;\n                var options = that.options;\n                var data = that.dataSource.flatView();\n                var length = data.length;\n                var groupsLength = that.dataSource._group.length;\n                var isActive = that.element[0] === activeElement();\n                var action;\n                that._renderFooter();\n                that._renderNoData();\n                that._toggleNoData(!length);\n                that._toggleHeader(!!groupsLength && !!length);\n                that._resizePopup();\n                popup.position();\n                if (length) {\n                    if (options.suggest && isActive) {\n                        that.suggest(data[0]);\n                    }\n                }\n                if (that._open) {\n                    that._open = false;\n                    action = that._allowOpening() ? 'open' : 'close';\n                    if (that._typingTimeout && !isActive) {\n                        action = 'close';\n                    }\n                    if (length) {\n                        that._resetFocusItem();\n                        if (options.virtual) {\n                            that.popup.unbind('activate', that._resetFocusItemHandler).one('activate', that._resetFocusItemHandler);\n                        }\n                    }\n                    popup[action]();\n                    that._typingTimeout = undefined;\n                }\n                if (that._touchScroller) {\n                    that._touchScroller.reset();\n                }\n                that._hideBusy();\n                that._makeUnselectable();\n                that.trigger('dataBound');\n            },\n            _mute: function (callback) {\n                this._muted = true;\n                callback.call(this);\n                this._muted = false;\n            },\n            _listChange: function () {\n                var isActive = this._active || this.element[0] === activeElement();\n                if (isActive && !this._muted) {\n                    this._selectValue(this.listView.selectedDataItems()[0]);\n                }\n            },\n            _selectValue: function (dataItem) {\n                var separator = this._separator();\n                var text = '';\n                if (dataItem) {\n                    text = this._text(dataItem);\n                }\n                if (text === null) {\n                    text = '';\n                }\n                if (separator) {\n                    text = replaceWordAtCaret(caret(this.element)[0], this._accessor(), text, separator, this._defaultSeparator());\n                }\n                this._prev = text;\n                this._accessor(text);\n                this._placeholder();\n            },\n            _unifySeparators: function () {\n                this._accessor(this.value().split(this._separator()).join(this._defaultSeparator()));\n                return this;\n            },\n            _preselect: function (value, text) {\n                this._inputValue(text);\n                this._accessor(value);\n                this._old = this.oldText = this._accessor();\n                this.listView.setValue(value);\n                this._placeholder();\n            },\n            _change: function () {\n                var that = this;\n                var value = that._unifySeparators().value();\n                var trigger = value !== List.unifyType(that._old, typeof value);\n                var valueUpdated = trigger && !that._typing;\n                var itemSelected = that._oldText !== value;\n                that._old = value;\n                that._oldText = value;\n                if (valueUpdated || itemSelected) {\n                    that.element.trigger(CHANGE);\n                }\n                if (trigger) {\n                    that.trigger(CHANGE);\n                }\n                that.typing = false;\n                that._toggleCloseVisibility();\n            },\n            _accessor: function (value) {\n                var that = this, element = that.element[0];\n                if (value !== undefined) {\n                    element.value = value === null ? '' : value;\n                    that._placeholder();\n                } else {\n                    value = element.value;\n                    if (element.className.indexOf('k-readonly') > -1) {\n                        if (value === that.options.placeholder) {\n                            return '';\n                        } else {\n                            return value;\n                        }\n                    }\n                    return value;\n                }\n            },\n            _keydown: function (e) {\n                var that = this;\n                var key = e.keyCode;\n                var listView = that.listView;\n                var visible = that.popup.visible();\n                var current = listView.focus();\n                that._last = key;\n                if (key === keys.DOWN) {\n                    if (visible) {\n                        this._move(current ? 'focusNext' : 'focusFirst');\n                    } else if (that.value()) {\n                        that._filterSource({\n                            value: that.ignoreCase ? that.value().toLowerCase() : that.value(),\n                            operator: that.options.filter,\n                            field: that.options.dataTextField,\n                            ignoreCase: that.ignoreCase\n                        }).done(function () {\n                            if (that._allowOpening()) {\n                                that._resetFocusItem();\n                                that.popup.open();\n                            }\n                        });\n                    }\n                    e.preventDefault();\n                } else if (key === keys.UP) {\n                    if (visible) {\n                        this._move(current ? 'focusPrev' : 'focusLast');\n                    }\n                    e.preventDefault();\n                } else if (key === keys.HOME) {\n                    this._move('focusFirst');\n                } else if (key === keys.END) {\n                    this._move('focusLast');\n                } else if (key === keys.ENTER || key === keys.TAB) {\n                    if (key === keys.ENTER && visible) {\n                        e.preventDefault();\n                    }\n                    if (visible && current) {\n                        var dataItem = listView.dataItemByIndex(listView.getElementIndex(current));\n                        if (that.trigger('select', {\n                                dataItem: dataItem,\n                                item: current\n                            })) {\n                            return;\n                        }\n                        this._select(current);\n                    }\n                    this._blur();\n                } else if (key === keys.ESC) {\n                    if (visible) {\n                        e.preventDefault();\n                    } else {\n                        that._clearValue();\n                    }\n                    that.close();\n                } else if (that.popup.visible() && (key === keys.PAGEDOWN || key === keys.PAGEUP)) {\n                    e.preventDefault();\n                    var direction = key === keys.PAGEDOWN ? 1 : -1;\n                    listView.scrollWith(direction * listView.screenHeight());\n                } else {\n                    that.popup._hovered = true;\n                    that._search();\n                }\n            },\n            _keypress: function () {\n                this._oldText = this.element.val();\n                this._typing = true;\n            },\n            _move: function (action) {\n                this.listView[action]();\n                if (this.options.suggest) {\n                    this.suggest(this.listView.focus());\n                }\n            },\n            _hideBusy: function () {\n                var that = this;\n                clearTimeout(that._busy);\n                that._loading.hide();\n                that.element.attr('aria-busy', false);\n                that._busy = null;\n                that._showClear();\n            },\n            _showBusy: function () {\n                var that = this;\n                if (that._busy) {\n                    return;\n                }\n                that._busy = setTimeout(function () {\n                    that.element.attr('aria-busy', true);\n                    that._loading.show();\n                    that._hideClear();\n                }, 100);\n            },\n            _placeholder: function (show) {\n                if (placeholderSupported) {\n                    return;\n                }\n                var that = this, element = that.element, placeholder = that.options.placeholder, value;\n                if (placeholder) {\n                    value = element.val();\n                    if (show === undefined) {\n                        show = !value;\n                    }\n                    if (!show) {\n                        if (value !== placeholder) {\n                            placeholder = value;\n                        } else {\n                            placeholder = '';\n                        }\n                    }\n                    if (value === that._old && !show) {\n                        return;\n                    }\n                    element.toggleClass('k-readonly', show).val(placeholder);\n                    if (!placeholder && element[0] === document.activeElement) {\n                        caret(element[0], 0, 0);\n                    }\n                }\n            },\n            _separator: function () {\n                var separator = this.options.separator;\n                if (separator instanceof Array) {\n                    return new RegExp(separator.join('|'), 'gi');\n                }\n                return separator;\n            },\n            _defaultSeparator: function () {\n                var separator = this.options.separator;\n                if (separator instanceof Array) {\n                    return separator[0];\n                }\n                return separator;\n            },\n            _inputValue: function () {\n                return this.element.val();\n            },\n            _search: function () {\n                var that = this;\n                clearTimeout(that._typingTimeout);\n                that._typingTimeout = setTimeout(function () {\n                    if (that._prev !== that._accessor()) {\n                        that._prev = that._accessor();\n                        that.search();\n                    }\n                }, that.options.delay);\n            },\n            _select: function (candidate) {\n                var that = this;\n                that._active = true;\n                return that.listView.select(candidate).done(function () {\n                    that._active = false;\n                });\n            },\n            _loader: function () {\n                this._loading = $('<span class=\"k-icon k-i-loading\" style=\"display:none\"></span>').insertAfter(this.element);\n            },\n            _clearButton: function () {\n                List.fn._clearButton.call(this);\n                if (this.options.clearButton) {\n                    this._clear.insertAfter(this.element);\n                    this.wrapper.addClass('k-autocomplete-clearable');\n                }\n            },\n            _toggleHover: function (e) {\n                $(e.currentTarget).toggleClass(HOVER, e.type === 'mouseenter');\n            },\n            _toggleCloseVisibility: function () {\n                if (this.value()) {\n                    this._showClear();\n                } else {\n                    this._hideClear();\n                }\n            },\n            _wrapper: function () {\n                var that = this, element = that.element, DOMelement = element[0], wrapper;\n                wrapper = element.parent();\n                if (!wrapper.is('span.k-widget')) {\n                    wrapper = element.wrap('<span />').parent();\n                }\n                wrapper.attr('tabindex', -1);\n                wrapper.attr('role', 'presentation');\n                wrapper[0].style.cssText = DOMelement.style.cssText;\n                element.css({\n                    width: '',\n                    height: DOMelement.style.height\n                });\n                that._focused = that.element;\n                that.wrapper = wrapper.addClass('k-widget k-autocomplete').addClass(DOMelement.className);\n                that._inputWrapper = $(wrapper[0]);\n            }\n        });\n        ui.plugin(AutoComplete);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}