{"version": 3, "sources": ["kendo.imagebrowser.js"], "names": ["f", "define", "$", "undefined", "concatPaths", "path", "name", "match", "sizeF<PERSON>atter", "value", "suffix", "Math", "round", "offsetTop", "ImageBrowser", "kendo", "window", "FileBrowser", "ui", "isPlainObject", "proxy", "extend", "browser", "support", "isFunction", "trimSlashesRegExp", "ERROR", "NS", "NAMEFIELD", "SIZEFIELD", "TYPEFIELD", "DEFAULTSORTORDER", "field", "dir", "EMPTYTILE", "template", "data", "schemas", "imagebrowser", "items", "model", "id", "fields", "size", "type", "transports", "RemoteTransport", "init", "options", "fn", "call", "this", "_call", "read", "create", "destroy", "update", "msie", "version", "element", "height", "that", "addClass", "fileTypes", "selected", "_selectedItem", "imageUrl", "transport", "get", "replace", "format", "encodeURIComponent", "_fileUpload", "e", "file", "filterRegExp", "RegExp", "split", "join", "fileName", "files", "fileSize", "fileNameField", "sizeField", "test", "_createFile", "_uploading", "upload", "one", "_insertFileToList", "_override", "set", "response", "_getField<PERSON>ame", "listView", "dataSource", "pushUpdate", "_tiles", "filter", "attr", "_scroll", "preventDefault", "_showMessage", "messages", "invalidFileType", "_content", "list", "appendTo", "on", "_dblClick", "ListView", "_itemTmpl", "editTemplate", "_editTmpl", "selectable", "autoBind", "dataBinding", "toolbar", "find", "parent", "action", "progress", "dataBound", "view", "length", "wrapper", "append", "text", "emptyFolder", "change", "_listViewChange", "_dataSource", "schema", "typeSortOrder", "nameSortOrder", "sort", "_error<PERSON><PERSON><PERSON>", "unbind", "_error", "DataSource", "bind", "_loadImage", "li", "dataItem", "getByUid", "thumbnailUrl", "img", "alt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hide", "prev", "remove", "end", "fadeIn", "after", "indexOf", "Date", "getTime", "loaded", "clearTimeout", "_timeout", "setTimeout", "_outerHeight", "viewTop", "scrollTop", "viewBottom", "each", "top", "bottom", "offsetHeight", "html", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,sBAAuB,qBAAsBD,IACtD,WA6QE,MApQC,UAAUE,EAAGC,GAkEV,QAASC,GAAYC,EAAMC,GAIvB,MAHID,KAASF,GAAcE,EAAKE,MAAM,SAClCF,GAAQA,GAAQ,IAAM,KAEnBA,EAAOC,EAElB,QAASE,GAAcC,GACnB,IAAKA,EACD,MAAO,EAEX,IAAIC,GAAS,QAWb,OAVID,IAAS,YACTC,EAAS,MACTD,GAAS,YACFA,GAAS,SAChBC,EAAS,MACTD,GAAS,SACFA,GAAS,OAChBC,EAAS,MACTD,GAAS,MAENE,KAAKC,MAAc,IAARH,GAAe,IAAMC,EAvF9C,GAwDOG,GAiCAC,EAxFAC,EAAQC,OAAOD,MAAOE,EAAcF,EAAMG,GAAGD,YAAaE,EAAgBjB,EAAEiB,cAAeC,EAAQlB,EAAEkB,MAAOC,EAASnB,EAAEmB,OAAQC,EAAUP,EAAMQ,QAAQD,QAASE,EAAaT,EAAMS,WAAYC,EAAoB,aAAcC,EAAQ,QAASC,EAAK,qBAAsBC,EAAY,OAAQC,EAAY,OAAQC,EAAY,OAAQC,GACrUC,MAAOF,EACPG,IAAK,OACNC,EAAYnB,EAAMoB,SAAS,yDAClCd,IAAO,EAAMN,EAAMqB,MACfC,SACIC,cACIF,KAAM,SAAUA,GACZ,MAAOA,GAAKG,OAASH,OAEzBI,OACIC,GAAI,OACJC,QACIpC,KAAM,OACNqC,KAAM,OACNC,KAAM,aAM1BvB,GAAO,EAAMN,EAAMqB,MACfS,YACIP,aAAgBvB,EAAMqB,KAAKU,gBAAgBzB,QACvC0B,KAAM,SAAUC,GACZjC,EAAMqB,KAAKU,gBAAgBG,GAAGF,KAAKG,KAAKC,KAAMjD,EAAEmB,QAAO,KAAU8B,KAAKH,QAASA,KAEnFI,MAAO,SAAUR,EAAMI,GACnBA,EAAQZ,KAAOlC,EAAEmB,UAAW2B,EAAQZ,MAAQ/B,KAAM8C,KAAKH,QAAQ3C,SAC3DmB,EAAW2B,KAAKH,QAAQJ,IACxBO,KAAKH,QAAQJ,GAAMM,KAAKC,KAAMH,GAE9BjC,EAAMqB,KAAKU,gBAAgBG,GAAGL,GAAMM,KAAKC,KAAMH,IAGvDK,KAAM,SAAUL,GACZG,KAAKC,MAAM,OAAQJ,IAEvBM,OAAQ,SAAUN,GACdG,KAAKC,MAAM,SAAUJ,IAEzBO,QAAS,SAAUP,GACfG,KAAKC,MAAM,UAAWJ,IAE1BQ,OAAQ,aAERR,SACIK,MAAQT,KAAM,QACdY,QAAUZ,KAAM,QAChBU,QAAUV,KAAM,QAChBW,SAAWX,KAAM,cAO7B/B,EADAS,EAAQmC,MAAQnC,EAAQoC,QAAU,EACtB,SAAUC,GAClB,MAAOA,GAAQ9C,WAGP,SAAU8C,GAClB,MAAOA,GAAQ9C,UAAYX,EAAEyD,GAASC,UA0B1C9C,EAAeG,EAAYI,QAC3B0B,KAAM,SAAUY,EAASX,GACrB,GAAIa,GAAOV,IACXH,GAAUA,MACV/B,EAAYgC,GAAGF,KAAKG,KAAKW,EAAMF,EAASX,GACxCa,EAAKF,QAAQG,SAAS,mBAE1Bd,SACI1C,KAAM,eACNyD,UAAW,4BAEftD,MAAO,WACH,GAAkDJ,GAA9CwD,EAAOV,KAAMa,EAAWH,EAAKI,gBAAuBC,EAAWL,EAAKb,QAAQmB,UAAUD,QAC1F,IAAIF,GAAwC,MAA5BA,EAASI,IAAItC,GAKzB,MAJAzB,GAAOD,EAAYyD,EAAKxD,OAAQ2D,EAASI,IAAIxC,IAAYyC,QAAQ5C,EAAmB,IAChFyC,IACA7D,EAAOmB,EAAW0C,GAAYA,EAAS7D,GAAQU,EAAMuD,OAAOJ,EAAUK,mBAAmBlE,KAEtFA,GAGfmE,YAAa,SAAUC,GACnB,GAA0RC,GAAtRb,EAAOV,KAAMH,EAAUa,EAAKb,QAASe,EAAYf,EAAQe,UAAWY,EAAmBC,QAAQ,IAAMb,EAAUc,MAAM,KAAKC,KAAK,OAAS,KAAKT,QAAQ,QAAS,OAAQ,KAAMU,EAAWN,EAAEO,MAAM,GAAG1E,KAAM2E,EAAWR,EAAEO,MAAM,GAAGrC,KAAMuC,EAAgBtD,EAAWuD,EAAYtD,CAC3Q8C,GAAaS,KAAKL,IAClBN,EAAErC,MAAS/B,KAAMwD,EAAKxD,QACtBqE,EAAOb,EAAKwB,YAAYN,EAAUE,GAC7BP,GAGDA,EAAKY,YAAa,EAClBzB,EAAK0B,OAAOC,IAAI,QAAS,WACrBd,EAAOvE,IAEX0D,EAAK0B,OAAOC,IAAI,UAAW,SAAUf,GACjC,GAAIC,EAAM,OACCA,GAAKY,UACZ,IAAI9C,GAAQqB,EAAK4B,kBAAkBf,EAC/BlC,GAAMkD,YACNlD,EAAMmD,IAAIT,EAAeT,EAAEmB,SAAS/B,EAAKgC,cAAcX,KACvD1C,EAAMmD,IAAIR,EAAWV,EAAEmB,SAAS/B,EAAKgC,cAAcV,KACnDtB,EAAKiC,SAASC,WAAWC,WAAWxD,IAExCqB,EAAKoC,OAASpC,EAAKiC,SAASvD,QAAQ2D,OAAO,IAAMnF,EAAMoF,KAAK,QAAU,OACtEtC,EAAKuC,cAhBb3B,EAAE4B,mBAqBN5B,EAAE4B,iBACFxC,EAAKyC,aAAavF,EAAMuD,OAAOtB,EAAQuD,SAASC,gBAAiBzB,EAAUhB,MAGnF0C,SAAU,WACN,GAAI5C,GAAOV,IACXU,GAAK6C,KAAOxG,EAAE,2CAA2CyG,SAAS9C,EAAKF,SAASiD,GAAG,SAAWjF,EAAIP,EAAMyC,EAAKuC,QAASvC,IAAO+C,GAAG,WAAajF,EAAI,KAAMP,EAAMyC,EAAKgD,UAAWhD,IAC7KA,EAAKiC,SAAW,GAAI/E,GAAMG,GAAG4F,SAASjD,EAAK6C,MACvCX,WAAYlC,EAAKkC,WACjB5D,SAAU0B,EAAKkD,YACfC,aAAcnD,EAAKoD,YACnBC,YAAY,EACZC,UAAU,EACVC,YAAa,SAAU3C,GACnBZ,EAAKwD,QAAQC,KAAK,cAAcC,SAASzD,SAAS,oBACjC,WAAbW,EAAE+C,QAAoC,SAAb/C,EAAE+C,SAC3B/C,EAAE4B,iBACFtF,EAAMG,GAAGuG,SAAS5D,EAAKiC,SAASnC,SAAS,KAGjD+D,UAAW,WACH7D,EAAKkC,WAAW4B,OAAOC,QACvB/D,EAAKoC,OAAS9C,KAAKZ,QAAQ2D,OAAO,IAAMnF,EAAMoF,KAAK,QAAU,OAC7DtC,EAAKuC,WAELjD,KAAK0E,QAAQC,OAAO5F,GAAY6F,KAAMlE,EAAKb,QAAQuD,SAASyB,gBAGpEC,OAAQ7G,EAAMyC,EAAKqE,gBAAiBrE,MAG5CsE,YAAa,WACT,GAGOC,GAHHvE,EAAOV,KAAMH,EAAUa,EAAKb,QAASmB,EAAYnB,EAAQmB,UAAWkE,EAAgBhH,KAAWU,GAAmBuG,GAC9GtG,MAAOJ,EACPK,IAAK,OACE8D,GACPnD,KAAMuB,EAAUvB,MAAQ,eACxB2F,MACIF,EACAC,GAGRnH,GAAcgD,KACdA,EAAU9D,KAAOe,EAAMyC,EAAKxD,KAAMwD,GAClCkC,EAAW5B,UAAYA,GAEvBhD,EAAc6B,EAAQoF,QACtBrC,EAAWqC,OAASpF,EAAQoF,OACrBjE,EAAUvB,MAAQzB,EAAcJ,EAAMqB,KAAKC,QAAQ8B,EAAUvB,SACpEwF,EAASrH,EAAMqB,KAAKC,QAAQ8B,EAAUvB,OAEtCiB,EAAKkC,YAAclC,EAAK2E,cACxB3E,EAAKkC,WAAW0C,OAAO/G,EAAOmC,EAAK2E,eAEnC3E,EAAK2E,cAAgBpH,EAAMyC,EAAK6E,OAAQ7E,GAE5CA,EAAKkC,WAAahF,EAAMqB,KAAKuG,WAAWrF,OAAOyC,GAAY6C,KAAKlH,EAAOmC,EAAK2E,gBAEhFK,WAAY,SAAUC,GAClB,GAAIjF,GAAOV,KAAMQ,EAAUzD,EAAE4I,GAAKC,EAAWlF,EAAKkC,WAAWiD,SAASrF,EAAQwC,KAAKpF,EAAMoF,KAAK,SAAU7F,EAAOyI,EAAS3E,IAAIxC,GAAYqH,EAAepF,EAAKb,QAAQmB,UAAU8E,aAAcC,EAAMhJ,EAAE,WAAaiJ,IAAK7I,IAAS8I,EAAU,GACrOL,GAASzD,aAGb4D,EAAIG,OAAOzC,GAAG,OAASjF,EAAI,WACvBzB,EAAEiD,MAAMmG,OAAOC,SAASC,MAAM1F,SAAS,WAAW2F,WAEtD9F,EAAQ2D,KAAK,gBAAgBoC,MAAMR,GAC/B1H,EAAWyH,GACXA,EAAeA,EAAapF,EAAKxD,OAAQkE,mBAAmBjE,KAExD2I,EAAaU,QAAQ,MAAQ,IAC7BP,EAAU,KAEdH,EAAeA,EAAeG,EAAU,QAAU7E,mBAAmBV,EAAKxD,OAASC,GAC/EyI,EAASrD,YACTuD,GAAgB,OAAQ,GAAIW,OAAOC,gBAC5Bd,GAASrD,YAGxBwD,EAAI/C,KAAK,MAAO8C,GAChBH,EAAGgB,QAAS,IAEhB1D,QAAS,WACL,GAAIvC,GAAOV,IACPU,GAAKb,QAAQmB,WAAaN,EAAKb,QAAQmB,UAAU8E,eACjDc,aAAalG,EAAKmG,UAClBnG,EAAKmG,SAAWC,WAAW,WACvB,GAAIrG,GAAS7C,EAAMmJ,aAAarG,EAAK6C,MAAOyD,EAAUtG,EAAK6C,KAAK0D,YAAaC,EAAaF,EAAUvG,CACpGC,GAAKoC,OAAOqE,KAAK,WACb,GAAIC,GAAM1J,EAAUsC,MAAOqH,EAASD,EAAMpH,KAAKsH,YAI/C,KAHIF,GAAOJ,GAAWI,EAAMF,GAAcG,GAAUL,GAAWK,EAASH,IACpExG,EAAKgF,WAAW1F,MAEhBoH,EAAMF,EACN,OAAO,IAGfxG,EAAKoC,OAASpC,EAAKoC,OAAOC,OAAO,WAC7B,OAAQ/C,KAAK2G,UAElB,OAGX/C,UAAW,WACP,GAAIlD,GAAOV,KAAMuH,EAAO,sBAAwB3J,EAAMoF,KAAK,OAAS,YAcpE,OAbAuE,IAAQ3J,EAAMoF,KAAK,QAAU,OAASrE,EAAY,MAClD4I,GAAQ,OAAS5I,EAAY,eAC7B4I,GAAQ,qEACRA,GAAQ,WAEJA,GADA7G,EAAKb,QAAQmB,WAAaN,EAAKb,QAAQmB,UAAU8E,aACzC,sEAEA,mEAEZyB,GAAQ,MACRA,GAAQ,aAAe9I,EAAY,aACnC8I,GAAQ,OAAS5I,EAAY,8DAAgED,EAAY,gBACzG6I,GAAQ,QACDtJ,EAAML,EAAMoB,SAASuI,IAASlK,cAAeA,OAG5DO,EAAMG,GAAGyJ,OAAO7J,IAClBE,OAAOD,MAAM6J,QACR5J,OAAOD,OACE,kBAAVd,SAAwBA,OAAO4K,IAAM5K,OAAS,SAAU6K,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.imagebrowser.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.imagebrowser', ['kendo.filebrowser'], f);\n}(function () {\n    var __meta__ = {\n        id: 'imagebrowser',\n        name: 'ImageBrowser',\n        category: 'web',\n        description: '',\n        hidden: true,\n        depends: ['filebrowser']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, FileBrowser = kendo.ui.FileBrowser, isPlainObject = $.isPlainObject, proxy = $.proxy, extend = $.extend, browser = kendo.support.browser, isFunction = kendo.isFunction, trimSlashesRegExp = /(^\\/|\\/$)/g, ERROR = 'error', NS = '.kendoImageBrowser', NAMEFIELD = 'name', SIZEFIELD = 'size', TYPEFIELD = 'type', DEFAULTSORTORDER = {\n                field: TYPEFIELD,\n                dir: 'asc'\n            }, EMPTYTILE = kendo.template('<li class=\"k-tile-empty\"><strong>${text}</strong></li>');\n        extend(true, kendo.data, {\n            schemas: {\n                'imagebrowser': {\n                    data: function (data) {\n                        return data.items || data || [];\n                    },\n                    model: {\n                        id: 'name',\n                        fields: {\n                            name: 'name',\n                            size: 'size',\n                            type: 'type'\n                        }\n                    }\n                }\n            }\n        });\n        extend(true, kendo.data, {\n            transports: {\n                'imagebrowser': kendo.data.RemoteTransport.extend({\n                    init: function (options) {\n                        kendo.data.RemoteTransport.fn.init.call(this, $.extend(true, {}, this.options, options));\n                    },\n                    _call: function (type, options) {\n                        options.data = $.extend({}, options.data, { path: this.options.path() });\n                        if (isFunction(this.options[type])) {\n                            this.options[type].call(this, options);\n                        } else {\n                            kendo.data.RemoteTransport.fn[type].call(this, options);\n                        }\n                    },\n                    read: function (options) {\n                        this._call('read', options);\n                    },\n                    create: function (options) {\n                        this._call('create', options);\n                    },\n                    destroy: function (options) {\n                        this._call('destroy', options);\n                    },\n                    update: function () {\n                    },\n                    options: {\n                        read: { type: 'POST' },\n                        update: { type: 'POST' },\n                        create: { type: 'POST' },\n                        destroy: { type: 'POST' }\n                    }\n                })\n            }\n        });\n        var offsetTop;\n        if (browser.msie && browser.version < 8) {\n            offsetTop = function (element) {\n                return element.offsetTop;\n            };\n        } else {\n            offsetTop = function (element) {\n                return element.offsetTop - $(element).height();\n            };\n        }\n        function concatPaths(path, name) {\n            if (path === undefined || !path.match(/\\/$/)) {\n                path = (path || '') + '/';\n            }\n            return path + name;\n        }\n        function sizeFormatter(value) {\n            if (!value) {\n                return '';\n            }\n            var suffix = ' bytes';\n            if (value >= 1073741824) {\n                suffix = ' GB';\n                value /= 1073741824;\n            } else if (value >= 1048576) {\n                suffix = ' MB';\n                value /= 1048576;\n            } else if (value >= 1024) {\n                suffix = ' KB';\n                value /= 1024;\n            }\n            return Math.round(value * 100) / 100 + suffix;\n        }\n        var ImageBrowser = FileBrowser.extend({\n            init: function (element, options) {\n                var that = this;\n                options = options || {};\n                FileBrowser.fn.init.call(that, element, options);\n                that.element.addClass('k-imagebrowser');\n            },\n            options: {\n                name: 'ImageBrowser',\n                fileTypes: '*.png,*.gif,*.jpg,*.jpeg'\n            },\n            value: function () {\n                var that = this, selected = that._selectedItem(), path, imageUrl = that.options.transport.imageUrl;\n                if (selected && selected.get(TYPEFIELD) === 'f') {\n                    path = concatPaths(that.path(), selected.get(NAMEFIELD)).replace(trimSlashesRegExp, '');\n                    if (imageUrl) {\n                        path = isFunction(imageUrl) ? imageUrl(path) : kendo.format(imageUrl, encodeURIComponent(path));\n                    }\n                    return path;\n                }\n            },\n            _fileUpload: function (e) {\n                var that = this, options = that.options, fileTypes = options.fileTypes, filterRegExp = new RegExp(('(' + fileTypes.split(',').join(')|(') + ')').replace(/\\*\\./g, '.*.'), 'i'), fileName = e.files[0].name, fileSize = e.files[0].size, fileNameField = NAMEFIELD, sizeField = SIZEFIELD, file;\n                if (filterRegExp.test(fileName)) {\n                    e.data = { path: that.path() };\n                    file = that._createFile(fileName, fileSize);\n                    if (!file) {\n                        e.preventDefault();\n                    } else {\n                        file._uploading = true;\n                        that.upload.one('error', function () {\n                            file = undefined;\n                        });\n                        that.upload.one('success', function (e) {\n                            if (file) {\n                                delete file._uploading;\n                                var model = that._insertFileToList(file);\n                                if (model._override) {\n                                    model.set(fileNameField, e.response[that._getFieldName(fileNameField)]);\n                                    model.set(sizeField, e.response[that._getFieldName(sizeField)]);\n                                    that.listView.dataSource.pushUpdate(model);\n                                }\n                                that._tiles = that.listView.items().filter('[' + kendo.attr('type') + '=f]');\n                                that._scroll();\n                            }\n                        });\n                    }\n                } else {\n                    e.preventDefault();\n                    that._showMessage(kendo.format(options.messages.invalidFileType, fileName, fileTypes));\n                }\n            },\n            _content: function () {\n                var that = this;\n                that.list = $('<ul class=\"k-reset k-floats k-tiles\" />').appendTo(that.element).on('scroll' + NS, proxy(that._scroll, that)).on('dblclick' + NS, 'li', proxy(that._dblClick, that));\n                that.listView = new kendo.ui.ListView(that.list, {\n                    dataSource: that.dataSource,\n                    template: that._itemTmpl(),\n                    editTemplate: that._editTmpl(),\n                    selectable: true,\n                    autoBind: false,\n                    dataBinding: function (e) {\n                        that.toolbar.find('.k-i-close').parent().addClass('k-state-disabled');\n                        if (e.action === 'remove' || e.action === 'sync') {\n                            e.preventDefault();\n                            kendo.ui.progress(that.listView.element, false);\n                        }\n                    },\n                    dataBound: function () {\n                        if (that.dataSource.view().length) {\n                            that._tiles = this.items().filter('[' + kendo.attr('type') + '=f]');\n                            that._scroll();\n                        } else {\n                            this.wrapper.append(EMPTYTILE({ text: that.options.messages.emptyFolder }));\n                        }\n                    },\n                    change: proxy(that._listViewChange, that)\n                });\n            },\n            _dataSource: function () {\n                var that = this, options = that.options, transport = options.transport, typeSortOrder = extend({}, DEFAULTSORTORDER), nameSortOrder = {\n                        field: NAMEFIELD,\n                        dir: 'asc'\n                    }, schema, dataSource = {\n                        type: transport.type || 'imagebrowser',\n                        sort: [\n                            typeSortOrder,\n                            nameSortOrder\n                        ]\n                    };\n                if (isPlainObject(transport)) {\n                    transport.path = proxy(that.path, that);\n                    dataSource.transport = transport;\n                }\n                if (isPlainObject(options.schema)) {\n                    dataSource.schema = options.schema;\n                } else if (transport.type && isPlainObject(kendo.data.schemas[transport.type])) {\n                    schema = kendo.data.schemas[transport.type];\n                }\n                if (that.dataSource && that._errorHandler) {\n                    that.dataSource.unbind(ERROR, that._errorHandler);\n                } else {\n                    that._errorHandler = proxy(that._error, that);\n                }\n                that.dataSource = kendo.data.DataSource.create(dataSource).bind(ERROR, that._errorHandler);\n            },\n            _loadImage: function (li) {\n                var that = this, element = $(li), dataItem = that.dataSource.getByUid(element.attr(kendo.attr('uid'))), name = dataItem.get(NAMEFIELD), thumbnailUrl = that.options.transport.thumbnailUrl, img = $('<img />', { alt: name }), urlJoin = '?';\n                if (dataItem._uploading) {\n                    return;\n                }\n                img.hide().on('load' + NS, function () {\n                    $(this).prev().remove().end().addClass('k-image').fadeIn();\n                });\n                element.find('.k-i-loading').after(img);\n                if (isFunction(thumbnailUrl)) {\n                    thumbnailUrl = thumbnailUrl(that.path(), encodeURIComponent(name));\n                } else {\n                    if (thumbnailUrl.indexOf('?') >= 0) {\n                        urlJoin = '&';\n                    }\n                    thumbnailUrl = thumbnailUrl + urlJoin + 'path=' + encodeURIComponent(that.path() + name);\n                    if (dataItem._override) {\n                        thumbnailUrl += '&_=' + new Date().getTime();\n                        delete dataItem._override;\n                    }\n                }\n                img.attr('src', thumbnailUrl);\n                li.loaded = true;\n            },\n            _scroll: function () {\n                var that = this;\n                if (that.options.transport && that.options.transport.thumbnailUrl) {\n                    clearTimeout(that._timeout);\n                    that._timeout = setTimeout(function () {\n                        var height = kendo._outerHeight(that.list), viewTop = that.list.scrollTop(), viewBottom = viewTop + height;\n                        that._tiles.each(function () {\n                            var top = offsetTop(this), bottom = top + this.offsetHeight;\n                            if (top >= viewTop && top < viewBottom || bottom >= viewTop && bottom < viewBottom) {\n                                that._loadImage(this);\n                            }\n                            if (top > viewBottom) {\n                                return false;\n                            }\n                        });\n                        that._tiles = that._tiles.filter(function () {\n                            return !this.loaded;\n                        });\n                    }, 250);\n                }\n            },\n            _itemTmpl: function () {\n                var that = this, html = '<li class=\"k-tile\" ' + kendo.attr('uid') + '=\"#=uid#\" ';\n                html += kendo.attr('type') + '=\"${' + TYPEFIELD + '}\">';\n                html += '#if(' + TYPEFIELD + ' == \"d\") { #';\n                html += '<div class=\"k-thumb\"><span class=\"k-icon k-i-folder\"></span></div>';\n                html += '#}else{#';\n                if (that.options.transport && that.options.transport.thumbnailUrl) {\n                    html += '<div class=\"k-thumb\"><span class=\"k-icon k-i-loading\"></span></div>';\n                } else {\n                    html += '<div class=\"k-thumb\"><span class=\"k-icon k-i-file\"></span></div>';\n                }\n                html += '#}#';\n                html += '<strong>${' + NAMEFIELD + '}</strong>';\n                html += '#if(' + TYPEFIELD + ' == \"f\") { # <span class=\"k-filesize\">${this.sizeFormatter(' + SIZEFIELD + ')}</span> #}#';\n                html += '</li>';\n                return proxy(kendo.template(html), { sizeFormatter: sizeFormatter });\n            }\n        });\n        kendo.ui.plugin(ImageBrowser);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}