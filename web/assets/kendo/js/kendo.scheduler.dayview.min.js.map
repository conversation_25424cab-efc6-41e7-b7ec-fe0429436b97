{"version": 3, "sources": ["kendo.scheduler.dayview.js"], "names": ["f", "define", "$", "undefined", "toInvariantTime", "date", "staticDate", "Date", "setTime", "getMilliseconds", "isInDateRange", "value", "min", "max", "isInTimeRange", "overlaps", "addContinuousEvent", "group", "range", "element", "isAllDay", "idx", "events", "_continuousEvents", "lastEvent", "length", "startDate", "getDate", "start", "getTime", "splice", "uid", "attr", "kendo", "end", "push", "getWorkDays", "options", "workDays", "dayIndex", "workWeekStart", "workWeekEnd", "Math", "abs", "window", "ui", "browser", "support", "SchedulerView", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "extend", "proxy", "MS_PER_MINUTE", "MS_PER_DAY", "CURRENT_TIME_MARKER_CLASS", "CURRENT_TIME_MARKER_ARROW_CLASS", "INVERSE_COLOR_CLASS", "BORDER_SIZE_COEFF", "NS", "DAY_VIEW_EVENT_TEMPLATE", "template", "DAY_VIEW_ALL_DAY_EVENT_TEMPLATE", "DATA_HEADER_TEMPLATE", "ALLDAY_EVENT_WRAPPER_STRING", "EVENT_WRAPPER_STRING", "MultiDayView", "init", "that", "this", "fn", "call", "title", "name", "_workDays", "_templates", "_editable", "calculateDateRange", "_groups", "_currentTime", "_currentTimeMarkerUpdater", "_updateCurrentTimeMarker", "currentTime", "timezone", "timezoneOffset", "groupsCount", "firstTimesCell", "lastTimesCell", "groupIndex", "currentGroup", "utcCurrentTime", "ranges", "collection", "slotElement", "elementHtml", "timesTableMarker", "markerTopPosition", "timesTableMarkerCss", "marker<PERSON>id<PERSON>", "currentTimeMarker", "useLocalTimezone", "dataSource", "schema", "offset", "convert", "getTimezoneOffset", "times", "find", "remove", "content", "orientation", "groups", "toUtcTime", "timeSlotRanges", "slotByStartDate", "prependTo", "round", "innerRect", "top", "scrollWidth", "msie", "edge", "_isRtl", "right", "position", "left", "addClass", "css", "height", "width", "setUpdateTimer", "markerOptions", "updateInterval", "_currentTimeUpdateTimer", "setInterval", "_updateResizeHint", "event", "startTime", "endTime", "hint", "rangeIndex", "slotIdx", "slot", "rect", "format", "container", "multiday", "isMultiDay", "_removeResizeHint", "startSlot", "_isGroupedByDate", "index", "_slots", "offsetWidth", "clientHeight", "offsetTop", "_createResizeHint", "offsetLeft", "_resizeHint", "add", "innerWidth", "outerRect", "snap", "bottom", "appendTo", "text", "first", "toString", "toLocalDate", "last", "_updateMoveHint", "distance", "eventHint", "duration", "_removeMoveHint", "pop", "clientWidth", "offsetHeight", "_createEventElement", "clone", "inverseColor", "_appendMoveHint", "_moveHint", "_slotByPosition", "x", "y", "_isVerticallyGrouped", "scrollTop", "scrollLeft", "ceil", "daySlotByPosition", "timeSlotByPosition", "_groupCount", "resources", "groupedResources", "byDate", "_groupOrientation", "_columnCountForLevel", "_rowCountForLevel", "_columnOffsetForResource", "_columnCountInResourceView", "_timeSlotGroups", "groupCount", "columnCount", "time", "rowIndex", "cellIndex", "rowCount", "rowMultiplier", "cellMultiplier", "cells", "timeIndex", "interval", "_timeSlotInterval", "verticalViews", "tableRows", "floor", "children", "_addTimeSlotGroup", "cell", "getTimeSlotCollection", "currentDate", "_dates", "UTC", "getFullYear", "getMonth", "setAttribute", "addTimeSlot", "_addDaySlotGroup", "cellCount", "addDaySlot", "_daySlotGroups", "getDaySlotCollection", "view", "columnIndex", "_addResourceView", "addTimeSlotCollection", "addDays", "allDaySlot", "addDaySlotCollection", "selectedDateFormat", "selectedShortDateFormat", "selectedMobileDateFormat", "showWorkHours", "today", "minorTickCount", "majorTick", "majorTimeHeaderTemplate", "minorTimeHeaderTemplate", "groupHeaderTemplate", "slotTemplate", "allDaySlotTemplate", "eventTemplate", "allDayEventTemplate", "dateHeaderTemplate", "editable", "workDayStart", "workDayEnd", "footer", "command", "messages", "allDay", "showFullDay", "showWorkDay", "settings", "Template", "templateSettings", "_eventTmpl", "_isMobile", "_touchEditable", "_mouseEditable", "on", "e", "trigger", "closest", "preventDefault", "create", "resourceInfo", "parent", "hasClass", "pageX", "pageY", "_resourceBySlot", "eventInfo", "endDate", "update", "threshold", "mobileOS", "android", "_addUserEvents", "UserEvents", "filter", "useClickAsTap", "tap", "_scrolling", "target", "location", "_allDayUserEvents", "_editUserEvents", "eventElement", "touchElement", "touch", "initialTouch", "_layout", "dates", "column", "columns", "rows", "isMobile", "isToday", "className", "cellContent", "_forTimeRange", "middleRow", "lastSlotRow", "row", "_createDateLayout", "_createColumnsLayout", "_createRowsLayout", "_footer", "html", "isWorkDay", "action", "after", "msMin", "msMax", "msMajorInterval", "msInterval", "startDay", "msStart", "majorTickDivider", "isMajorTickRow", "isMiddleRow", "isLastSlotRow", "_content", "allDayVerticalGroupRow", "appendRow", "rowIdx", "isVerticalGroupped", "dateID", "rowLevels", "columnLevels", "result", "dateGroupIndex", "dateIndex", "groupIdx", "_addCells<PERSON>oContent", "append", "tmplDate", "classes", "_isWorkDay", "i", "day", "getDay", "_render", "allDayHeader", "_startDate", "_endDate", "createLayout", "refreshLayout", "_allDayHeaderHeight", "th", "currentTarget", "additioanlWidth", "additionalHeight", "<PERSON><PERSON><PERSON><PERSON>", "_end", "nextDate", "nextDay", "previousDate", "previousDay", "destroy", "clearInterval", "off", "inRange", "selectionByElement", "_timeSlotIndex", "eventStartTime", "timeSlotInterval", "_slotIndex", "_dateSlotIndex", "slotStart", "slotEnd", "slots", "_positionAllDayEvent", "slotRange", "rowEvents", "j", "event<PERSON>ength", "slotWidth", "startIndex", "endIndex", "allDayEvents", "collidingEvents", "currentColumnCount", "_headerColumnCount", "leftOffset", "rightOffset", "eventHeight", "addEvent", "slotIndex", "createRows", "_arrangeColumns", "eventRightOffset", "columnEvents", "eventElements", "slotEvents", "columnWidth", "calculatedWidth", "createColumns", "style", "_positionEvent", "_startTime", "_endTime", "isOneDayEvent", "head", "tail", "middle", "eventStartDate", "eventEndDate", "data", "showDelete", "resizable", "resize", "_time", "eventEndTime", "eventResources", "apply", "ns", "singleDay", "angular", "elements", "dataItem", "_isInTimeSlot", "slotStartTime", "slotEndTime", "_date", "_isInDateSlot", "firstSlot", "lastSlot", "_updateAllDayHeaderHeight", "allDaySlots", "_height", "refresh", "_renderEvents", "isMultiDayEvent", "rangeCount", "occurrence", "newStart", "newEnd", "date<PERSON><PERSON><PERSON>", "allDayEventContainer", "slotRanges", "daySlotR<PERSON><PERSON>", "setDate", "_inverseEventColor", "render", "eventsByResource", "eventsPerDate", "Query", "sort", "field", "dir", "toArray", "_eventsByResource", "map", "grep", "itemIdx", "eventsFilteredByResource", "resource", "_resourceValue", "operator", "groupEqFilter", "slice", "level", "columnLevel", "rowLevel", "clearSelection", "removeAttr", "removeClass", "_updateDirection", "selection", "multiple", "reverse", "vertical", "isDaySlot", "endSlot", "collectionIndex", "backward", "_changeViewPeriod", "verticalByDate", "endMilliseconds", "newDateStart", "newDateEnd", "_timeSlotCollections", "_getCollections", "daySlotCollectionCount", "_isInRange", "<PERSON><PERSON><PERSON><PERSON>", "WeekView", "selectedDate", "dayOfWeek", "calendarInfo", "firstDay", "WorkWeekView", "weekStart", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,2BAA4B,wBAAyBD,IAC9D,WAogDE,MA3/CC,UAAUE,EAAGC,GAGV,QAASC,GAAgBC,GACrB,GAAIC,GAAa,GAAIC,MAAK,KAAM,EAAG,EAAG,EAAG,EAAG,EAE5C,OADAC,GAAQF,EAAYG,EAAgBJ,IAC7BC,EAEX,QAASI,GAAcC,EAAOC,EAAKC,GAC/B,MAAOF,IAASC,GAAOD,GAASE,EAEpC,QAASC,GAAcH,EAAOC,EAAKC,EAAKE,GAEpC,MADAA,GAAWA,EAAWJ,GAASE,EAAMF,EAAQE,EACtCF,EAAQC,GAAOG,EAE1B,QAASC,GAAmBC,EAAOC,EAAOC,EAASC,GAAnD,GAKYC,GAJJC,EAASL,EAAMM,kBACfC,EAAYF,EAAOA,EAAOG,OAAS,GACnCC,EAAYC,EAAQT,EAAMU,MAAMF,aAAaG,SACjD,IAAIT,GAAYI,GAAaG,EAAQH,EAAUI,MAAMF,aAAaG,WAAaH,EAAW,CAEtF,IADIL,EAAMC,EAAOG,OAAS,EACnBJ,QACCC,EAAOD,GAAKD,UAAYO,EAAQL,EAAOD,GAAKO,MAAMF,aAAaG,UAAYH,GADlEL,KAKjBC,EAAOQ,OAAOT,EAAM,EAAG,GACnBF,QAASA,EACTC,UAAU,EACVW,IAAKZ,EAAQa,KAAKC,EAAMD,KAAK,QAC7BJ,MAAOV,EAAMU,MACbM,IAAKhB,EAAMgB,UAGfZ,GAAOa,MACHhB,QAASA,EACTC,SAAUA,EACVW,IAAKZ,EAAQa,KAAKC,EAAMD,KAAK,QAC7BJ,MAAOV,EAAMU,MACbM,IAAKhB,EAAMgB,MAIvB,QAASE,GAAYC,GAArB,GACQC,MACAC,EAAWF,EAAQG,cAAgB,EACnCC,EAAcC,KAAKC,IAAIN,EAAQI,YAAc,EAEjD,KADAH,EAASH,KAAKI,GACPE,GAAeF,GACdA,EAAW,EACXA,GAAY,EAEZA,IAEJD,EAASH,KAAKI,EAElB,OAAOD,GAxDd,GACOL,GAAQW,OAAOX,MAAOY,EAAKZ,EAAMY,GAAIC,EAAUb,EAAMc,QAAQD,QAAStC,EAAUyB,EAAM5B,KAAKG,QAASwC,EAAgBH,EAAGG,cAAeC,EAAahB,EAAMiB,YAAaC,EAAclB,EAAMmB,aAAcC,EAASnD,EAAEmD,OAAQC,EAAQpD,EAAEoD,MAAO3B,EAAUM,EAAM5B,KAAKsB,QAAS4B,EAAgBtB,EAAM5B,KAAKkD,cAAeC,EAAavB,EAAM5B,KAAKmD,WAAYC,EAA4B,iBAAkBC,EAAkC,uBAAwBC,EAAsB,kBAAmBC,EAAoB,MAAQnD,EAAkBwB,EAAM5B,KAAKI,gBAAiBoD,EAAK,qBACxjBC,EAA0B7B,EAAM8B,SAAS,iPAAiQC,EAAkC/B,EAAM8B,SAAS,uIAAkJE,EAAuBhC,EAAM8B,SAAS,sJAAkKG,EAA8B,0/BAAqqCC,EAAuB,8jCAwD/4DC,EAAepB,EAAcK,QAC7BgB,KAAM,SAAUlD,EAASkB,GACrB,GAAIiC,GAAOC,IACXvB,GAAcwB,GAAGH,KAAKI,KAAKH,EAAMnD,EAASkB,GAC1CiC,EAAKI,MAAQJ,EAAKjC,QAAQqC,OAASJ,EAAKjC,QAAQsC,KAChDL,EAAKM,UAAYxC,EAAYkC,EAAKjC,SAClCiC,EAAKO,aACLP,EAAKQ,YACLR,EAAKS,qBACLT,EAAKU,UACLV,EAAKW,cAAa,IAEtBC,0BAA2B,WACvBX,KAAKY,yBAAyB,GAAI5E,QAEtC4E,yBAA0B,SAAUC,GAAV,GAGdC,GAEIC,EAMRC,EACAC,EACAC,EACKC,EACDC,EAIAC,EACAC,EAIAC,EACAC,EAEIC,EACAC,EACAC,EACAC,EACAC,EA9BR/D,EAAUkC,KAAKlC,OAanB,KAZIA,EAAQgE,kBAAkBC,oBAAqB,IAC3CjB,EAAWhD,EAAQkE,WAAWlE,QAAQmE,OAAOnB,SAC7ChD,EAAQkE,YAAclB,IAClBC,EAAiBrD,EAAMoD,SAASoB,OAAOrB,EAAaC,GACxDD,EAAcnD,EAAMoD,SAASqB,QAAQtB,EAAaA,EAAYuB,oBAAqBrB,KAG3Ff,KAAKqC,MAAMC,KAAK,IAAMpD,GAA2BqD,SACjDvC,KAAKwC,QAAQF,KAAK,IAAMpD,GAA2BqD,SAC/CvB,EAAelD,EAAQpB,OAAsC,cAA7BoB,EAAQpB,MAAM+F,YAAkCzC,KAAK0C,OAAOxF,OAAhB,EAC5E+D,EAAiBjB,KAAKqC,MAAMC,KAAK,qBACjCpB,EAAgBlB,KAAKqC,MAAMC,KAAK,oBAC3BnB,EAAa,EAAGA,EAAaH,EAAaG,IAAc,CAE7D,GADIC,EAAepB,KAAK0C,OAAOvB,IAC1BC,EACD,MAIJ,IAFIC,EAAiB3D,EAAM5B,KAAK6G,UAAU9B,GACtCS,EAASF,EAAawB,eAAevB,EAAgBA,EAAiB,GACpD,IAAlBC,EAAOpE,OACP,MAEAqE,GAAaD,EAAO,GAAGC,WACvBC,EAAcD,EAAWsB,gBAAgBhC,GACzCW,IACIC,EAAc,eAAkBvC,EAA4B,WAC5DwC,EAAmB/F,EAAE8F,GAAaqB,UAAU9C,KAAKqC,OACjDV,EAAoBxD,KAAK4E,MAAMzB,EAAO,GAAG0B,UAAUnC,EAAa,GAAI7E,MAAK6E,EAAYvD,UAAY,IAAI,GAAO2F,KAC5GrB,KACAC,EAAc7B,KAAKwC,QAAQ,GAAGU,aAC9B3E,EAAQ4E,MAAQ5E,EAAQ6E,QACxBvB,GAAe,GAEf7B,KAAKqD,QACLzB,EAAoB0B,MAAQrC,EAAesC,WAAWC,KAAO5E,EAAYqC,GAAkBrC,EAAYsC,GACvGQ,EAAiB+B,SAAStE,EAAkC,WAE5DyC,EAAoB4B,KAAOtC,EAAcqC,WAAWC,KACpD9B,EAAiB+B,SAAStE,EAAkC,WAEhEyC,EAAoBqB,IAAMtB,EAAoBjD,EAAWgD,GAAoBrC,EAAoB,EACjGqC,EAAiBgC,IAAI9B,GACrBjG,EAAE8F,GAAaqB,UAAU9C,KAAKwC,SAASkB,KACnCT,IAAKtB,EACLgC,OAAQ,MACRL,MAAO,EACPM,MAAO/B,EACP2B,KAAM,OAKtB9C,aAAc,SAAUmD,GAAV,GACN9D,GAAOC,KACP8D,EAAgB/D,EAAKjC,QAAQgE,iBAC7BgC,MAAkB,GAASA,EAAcC,iBAAmBnI,IAC5DmE,EAAKY,4BACDkD,IACA9D,EAAKiE,wBAA0BC,YAAYlF,EAAMiB,KAAKW,0BAA2BZ,GAAO+D,EAAcC,mBAIlHG,kBAAmB,SAAUC,EAAOhD,EAAYiD,EAAWC,GAAxC,GAIXT,GAAOD,EAAQV,EAAKqB,EAEfC,EACD5H,EACAU,EAESmH,EACDC,EAcAC,EAQZC,EACAC,EAjCAC,EAAWV,EAAMW,aACjBpI,EAAQsD,KAAK0C,OAAOvB,GACpBG,EAAS5E,EAAM4E,OAAO8C,EAAWC,EAASQ,EAAUV,EAAMtH,SAG9D,KADAmD,KAAK+E,oBACIR,EAAa,EAAGA,EAAajD,EAAOpE,OAAQqH,IAGjD,GAFI5H,EAAQ2E,EAAOiD,GACflH,EAAQV,EAAMqI,YACdhF,KAAKiF,oBAAsBJ,EAC3B,IAASL,EAAUnH,EAAM6H,MAAOV,GAAW7H,EAAMgB,IAAIuH,MAAOV,IACpDC,EAAO9H,EAAM4E,WAAW4D,OAAOX,GACnCZ,EAAQa,EAAKW,YACbzB,EAASc,EAAKY,aACdpC,EAAMwB,EAAKa,UACXhB,EAAO7F,EAAcwB,GAAGsF,kBAAkBrF,KAAKF,KAAMyE,EAAKe,WAAYvC,EAAKW,EAAOD,GAClF3D,KAAKyF,YAAczF,KAAKyF,YAAYC,IAAIpB,OAG5CV,GAAQvG,EAAM+H,YACdzB,EAAStG,EAAMgI,aACfpC,EAAM5F,EAAMiI,UACRT,EACAjB,EAAQjH,EAAMgJ,cAEVjB,EAAO/H,EAAMiJ,UAAUxB,EAAWC,EAASrE,KAAKlC,QAAQ+H,MAC5D5C,EAAMyB,EAAKzB,IACXU,EAASe,EAAKoB,OAASpB,EAAKzB,KAEhCqB,EAAO7F,EAAcwB,GAAGsF,kBAAkBrF,KAAKF,KAAM3C,EAAMmI,WAAYvC,EAAKW,EAAOD,GACnF3D,KAAKyF,YAAczF,KAAKyF,YAAYC,IAAIpB,EAG5CK,GAAS,IACTC,EAAY5E,KAAKwC,QACjBqC,IACAF,EAAS,OACTC,EAAY5E,KAAKpD,QAAQ0F,KAAK,mEACzBsC,EAAU1H,SACX0H,EAAY5E,KAAKwC,UAGzBxC,KAAKyF,YAAYM,SAASnB,GAC1B5E,KAAKyF,YAAYnD,KAAK,gCAAgC0D,KAAK,IAC3DhG,KAAKyF,YAAYQ,QAAQxC,SAAS,WAAWnB,KAAK,gBAAgB0D,KAAKtI,EAAMwI,SAASxI,EAAMoD,SAASqF,YAAY/B,GAAYO,IAC7H3E,KAAKyF,YAAYW,OAAO3C,SAAS,UAAUnB,KAAK,mBAAmB0D,KAAKtI,EAAMwI,SAASxI,EAAMoD,SAASqF,YAAY9B,GAAUM,KAEhI0B,gBAAiB,SAAUlC,EAAOhD,EAAYmF,GAA7B,GAcTC,GACKhC,EACD5H,EACAqI,EACAV,EACAZ,EAKSc,EACDC,EAqBAC,EAgBZlC,EA7DAqC,EAAWV,EAAMW,aACjBpI,EAAQsD,KAAK0C,OAAOvB,GACpB9D,EAAQK,EAAM5B,KAAK6G,UAAUwB,EAAM9G,OAASiJ,EAC5C3I,EAAMN,EAAQ8G,EAAMqC,WACpBlF,EAAS5E,EAAM4E,OAAOjE,EAAOM,EAAKkH,EAAUV,EAAMtH,SAUtD,KATAQ,EAAQK,EAAMoD,SAASqF,YAAY9I,GACnCM,EAAMD,EAAMoD,SAASqF,YAAYxI,GACjCqC,KAAKyG,gBAAgBtC,EAAM3G,MACtBqH,IAAsC,IAAzB3I,EAAgByB,IAAczB,EAAgByB,GAAOzB,EAAgB8D,KAAKoE,eACpF9C,EAAOpE,OAAS,GAChBoE,EAAOoF,MAGXH,EAAY5K,IACP4I,EAAa,EAAGA,EAAajD,EAAOpE,OAAQqH,IAQjD,GAPI5H,EAAQ2E,EAAOiD,GACfS,EAAYrI,EAAMU,MAElBqG,GACAF,KAAMwB,EAAUQ,WAAa,EAC7BvC,IAAK+B,EAAUM,WAEftF,KAAKiF,oBAAsBJ,EAC3B,IAASL,EAAUQ,EAAUE,MAAOV,GAAW7H,EAAMgB,IAAIuH,MAAOV,IACxDC,EAAO9H,EAAM4E,WAAW4D,OAAOX,GACnCd,EAAIF,KAAOxD,KAAKqD,OAA4B,GAAnBoB,EAAKkC,YAAoBlC,EAAKe,WAAa,EAAIf,EAAKe,WAAa,EAC1F9B,EAAIC,OAASc,EAAKmC,aAClBlD,EAAIE,MAA2B,GAAnBa,EAAKkC,YAAoB,EACrCrC,EAAOtE,KAAK6G,oBAAoB1C,EAAM2C,OAClCzJ,MAAOA,EACPM,IAAKA,KACJkH,GACDV,EAAM4C,cACNzC,EAAKb,SAASrE,GAElBY,KAAKgH,gBAAgB1C,EAAMZ,GAC3B6C,EAAYA,EAAUb,IAAIpB,OAG1BtE,MAAKqD,SACLK,EAAIF,KAA+B,GAAxBwB,EAAU2B,YAAoB3B,EAAUQ,WAAa,GAEhEX,EACAnB,EAAIE,MAAQjH,EAAMgJ,aAAe,GAE7BjB,EAAO/H,EAAMiJ,UAAUvI,EAAOM,EAAKqC,KAAKlC,QAAQ+H,MACpDnC,EAAIT,IAAMyB,EAAKzB,IACfS,EAAIC,OAASe,EAAKoB,OAASpB,EAAKzB,IAChCS,EAAIE,MAAgC,GAAxBoB,EAAU2B,YAAoB,GAE9CrC,EAAOtE,KAAK6G,oBAAoB1C,EAAM2C,OAClCzJ,MAAOA,EACPM,IAAKA,KACJkH,GACDV,EAAM4C,cACNzC,EAAKb,SAASrE,GAElBY,KAAKgH,gBAAgB1C,EAAMZ,GAC3B6C,EAAYA,EAAUb,IAAIpB,EAG9B9B,GAAUxC,KAAKwC,QACfqC,IACArC,EAAUxC,KAAKpD,QAAQ0F,KAAK,mEACvBE,EAAQtF,SACTsF,EAAUxC,KAAKwC,UAGvB+D,EAAUR,SAASvD,IAEvBwE,gBAAiB,SAAU1C,EAAMZ,GAC7BY,EAAKb,SAAS,qBACda,EAAKZ,IAAIA,GACT1D,KAAKiH,UAAYjH,KAAKiH,UAAUvB,IAAIpB,IAExC4C,gBAAiB,SAAUC,EAAGC,GAAb,GACT3C,GAAMvC,EAcNxF,EACAyE,CACJ,KAfInB,KAAKqH,wBACLnF,EAASlC,KAAKwC,QAAQN,SACtBkF,GAAKpH,KAAKwC,QAAQ,GAAG8E,UACrBH,GAAKnH,KAAKwC,QAAQ,GAAG+E,YAErBrF,EAASlC,KAAKpD,QAAQ0F,KAAK,6DAA6DA,KAAK,QAAQJ,SAErGA,IACAiF,GAAKjF,EAAOsB,KACZ4D,GAAKlF,EAAOe,KAEhBkE,EAAIhJ,KAAKqJ,KAAKL,GACdC,EAAIjJ,KAAKqJ,KAAKJ,GAGTjG,EAAa,EAAGA,EAAanB,KAAK0C,OAAOxF,OAAQiE,IAGlD,GAFAzE,EAAQsD,KAAK0C,OAAOvB,GACpBsD,EAAO/H,EAAM+K,kBAAkBN,EAAGC,EAAGpH,KAAKiF,oBAEtC,MAAOR,EAgBf,KAbIvC,IACAiF,GAAKjF,EAAOsB,KACZ4D,GAAKlF,EAAOe,KAEhBf,EAASlC,KAAKwC,QAAQN,SACtBiF,GAAKjF,EAAOsB,KACZ4D,GAAKlF,EAAOe,IACPjD,KAAKqH,yBACND,GAAKpH,KAAKwC,QAAQ,GAAG8E,UACrBH,GAAKnH,KAAKwC,QAAQ,GAAG+E,YAEzBJ,EAAIhJ,KAAKqJ,KAAKL,GACdC,EAAIjJ,KAAKqJ,KAAKJ,GACTjG,EAAa,EAAGA,EAAanB,KAAK0C,OAAOxF,OAAQiE,IAGlD,GAFAzE,EAAQsD,KAAK0C,OAAOvB,GACpBsD,EAAO/H,EAAMgL,mBAAmBP,EAAGC,GAE/B,MAAO3C,EAGf,OAAO,OAEXkD,YAAa,WAAA,GACLC,GAAY5H,KAAK6H,iBACjBC,EAAS9H,KAAKiF,kBAClB,OAAI2C,GAAU1K,OACuB,aAA7B8C,KAAK+H,oBACDD,EACO9H,KAAKgI,qBAAqBJ,EAAU1K,OAAS,GAE7C8C,KAAKiI,kBAAkBL,EAAU1K,OAAS,GAGjD4K,EACO9H,KAAKgI,qBAAqBJ,EAAU1K,QAAU8C,KAAKgI,qBAAqB,GAExEhI,KAAKgI,qBAAqBJ,EAAU1K,QAAU8C,KAAKkI,yBAAyBN,EAAU1K,QAIlG,GAEXiL,2BAA4B,WAAA,GACpBP,GAAY5H,KAAK6H,iBACjBC,EAAS9H,KAAKiF,kBAClB,QAAK2C,EAAU1K,QAAU8C,KAAKqH,uBACtBS,EACO9H,KAAKiI,kBAAkB,GAEvBjI,KAAKgI,qBAAqB,GAGrCF,EACO9H,KAAKgI,qBAAqB,GAE1BhI,KAAKkI,yBAAyBN,EAAU1K,SAGvDkL,gBAAiB,SAAUC,EAAYC,GAAtB,GAKT5L,GAAO6L,EAAMC,EAAUC,EAEvBC,EAOKvH,EACDwH,EACAC,EAQIC,EAIAC,EA3BRC,EAAW/I,KAAKgJ,oBAChBC,EAAgBZ,EAChBP,EAAS9H,KAAKiF,mBACdiE,EAAYlJ,KAAKwC,QAAQF,KAAK,sCAUlC,KARA4G,EAAUzL,KAAK,OAAQ,OACnBiL,EAAWQ,EAAUhM,OACrB8C,KAAKqH,yBACDS,IACAmB,EAAgBX,GAEpBI,EAAWvK,KAAKgL,MAAMT,EAAWO,IAE5B9H,EAAa,EAAGA,EAAa8H,EAAe9H,IASjD,IARIwH,EAAgB,EAChBC,EAAiB,EACjB5I,KAAKqH,uBACLsB,EAAgBxH,EAEhByH,EAAiBzH,EAErBqH,EAAWG,EAAgBD,EACpBF,GAAYG,EAAgB,GAAKD,GAAU,CAM9C,GALIG,EAAQK,EAAUV,GAAUY,SAC5BZ,EAAWE,IAAa,IACxBH,EAAOrM,EAAgB,GAAIF,QAAMgE,KAAKoE,gBAEtC0E,EAAY,EACZhB,EACA,GAAI9H,KAAKqH,uBACL,IAAKoB,EAAY,EAAGA,EAAYJ,EAAYI,IACxC/L,EAAQsD,KAAK0C,OAAO+F,GACpBzI,KAAKqJ,kBAAkB3M,EAAOmM,EAAOJ,EAAWF,EAAMQ,EAAU5H,OAIpE,KADAzE,EAAQsD,KAAK0C,OAAOvB,GACfsH,EAAYG,EAAgBH,EAAYJ,EAAaC,EAAaG,GAAwBJ,EAC3FrI,KAAKqJ,kBAAkB3M,EAAOmM,EAAOJ,EAAWF,EAAMQ,EAAUD,GAChEA,QAKR,KADApM,EAAQsD,KAAK0C,OAAOvB,GACfsH,EAAYG,EAAiBN,EAAaG,GAAaG,EAAiB,GAAKN,EAAaG,IAC3FzI,KAAKqJ,kBAAkB3M,EAAOmM,EAAOJ,EAAWF,EAAMQ,EAAUD,GAChEA,GAGRP,IAAQQ,EACRP,MAIZa,kBAAmB,SAAU3M,EAAOmM,EAAOJ,EAAWF,EAAMQ,EAAUD,GAAnD,GAOXjI,GACAxD,EACAM,EARA2L,EAAOT,EAAMJ,GACblH,EAAa7E,EAAM6M,sBAAsBT,GACzCU,EAAcxJ,KAAKyJ,OAAOX,EACzBU,KAGD3I,EAAc7E,KAAK0N,IAAIF,EAAYG,cAAeH,EAAYI,WAAYJ,EAAYpM,WACtFC,EAAQwD,EAAc0H,EACtB5K,EAAMN,EAAQ0L,EAClBO,EAAKO,aAAa,OAAQ,YAC1BP,EAAKO,aAAa,iBAAiB,GACnCtI,EAAWuI,YAAYR,EAAMjM,EAAOM,KAExCoM,iBAAkB,SAAUxI,EAAYsH,EAAOJ,EAAWH,EAAa0B,GAArD,GAMVnJ,GALAyI,EAAOT,EAAMJ,GACbpL,EAAQ2C,KAAKyJ,OAAOO,EACnB3M,KAGDwD,EAAc7E,KAAK0N,IAAIrM,EAAMsM,cAAetM,EAAMuM,WAAYvM,EAAMD,WACxEkM,EAAKO,aAAa,OAAQ,YAC1BP,EAAKO,aAAa,iBAAiB,GACnCtI,EAAW0I,WAAWX,EAAMzI,EAAaA,EAAcnD,EAAM5B,KAAKmD,cAEtEiL,eAAgB,SAAU7B,EAAYC,GAAtB,GACRY,GAAWT,EAYNtH,EACDwH,EACAjM,EAAO6E,EAIPsH,EACAD,EAIAoB,EAtBJf,EAAgBZ,EAChBP,EAAS9H,KAAKiF,kBAUlB,KATIjF,KAAKqH,wBACDS,IACAmB,EAAgBX,GAEpBY,EAAYlJ,KAAKpD,QAAQ0F,KAAK,gCAE9B4G,EAAYlJ,KAAKpD,QAAQ0F,KAAK,kCAElC4G,EAAUzL,KAAK,OAAQ,OACd0D,EAAa,EAAGA,EAAa8H,EAAe9H,IAYjD,GAXIwH,EAAgB,EAEhB3I,KAAKqH,yBACLsB,EAAgBxH,GAEhB0H,EAAQK,EAAUP,GAAeS,SACjCR,EAAiB,EAChB5I,KAAKqH,yBACNuB,EAAiBzH,GAEjB6I,EAAY,EACZlC,EACA,GAAI9H,KAAKqH,uBACL,IAAKoB,EAAY,EAAGA,EAAYJ,EAAYI,IACxC/L,EAAQsD,KAAK0C,OAAO+F,GACpBlH,EAAa7E,EAAMyN,qBAAqB,GACxCnK,KAAK+J,iBAAiBxI,EAAYsH,EAAOJ,EAAWH,EAAanH,OAKrE,KAFAzE,EAAQsD,KAAK0C,OAAOvB,GACpBI,EAAa7E,EAAMyN,qBAAqB,GACnC1B,EAAYG,EAAgBH,EAAYJ,EAAaC,EAAaG,GAAwBJ,EAC3FrI,KAAK+J,iBAAiBxI,EAAYsH,EAAOJ,EAAWH,EAAa0B,GACjEA,QAMR,KAFAtN,EAAQsD,KAAK0C,OAAOvB,GACpBI,EAAa7E,EAAMyN,qBAAqB,GACnC1B,EAAYG,EAAiBN,EAAaG,GAAaG,EAAiB,GAAKN,EAAaG,IAC3FzI,KAAK+J,iBAAiBxI,EAAYsH,EAAOJ,EAAWH,EAAa0B,GACjEA,KAKhBvJ,QAAS,WAAA,GAII3D,GACDsN,EACKC,EALThC,EAAarI,KAAK2H,cAClBW,EAActI,KAAKmI,4BAEvB,KADAnI,KAAK0C,UACI5F,EAAM,EAAGA,EAAMuL,EAAYvL,IAAO,CAEvC,IADIsN,EAAOpK,KAAKsK,iBAAiBxN,GACxBuN,EAAc,EAAGA,EAAc/B,EAAa+B,IAC7CrK,KAAKyJ,OAAOY,IACZD,EAAKG,sBAAsBvK,KAAKyJ,OAAOY,GAAc3M,EAAM5B,KAAK0O,QAAQxK,KAAKyJ,OAAOY,GAAc,GAGtGrK,MAAKlC,QAAQ2M,YACbL,EAAKM,qBAAqB1K,KAAKyJ,OAAO,GAAI/L,EAAM5B,KAAK0O,QAAQxK,KAAKyJ,OAAOzJ,KAAKyJ,OAAOvM,OAAS,GAAI,IAG1G8C,KAAKoI,gBAAgBC,EAAYC,GAC7BtI,KAAKlC,QAAQ2M,YACbzK,KAAKkK,eAAe7B,EAAYC,IAGxCxK,SACIsC,KAAM,eACNuK,mBAAoB,QACpBC,wBAAyB,QACzBC,yBAA0B,0BAC1BJ,YAAY,EACZK,eAAe,EACf3K,MAAO,GACPiE,UAAW1G,EAAM5B,KAAKiP,QACtB1G,QAAS3G,EAAM5B,KAAKiP,QACpBC,eAAgB,EAChBC,UAAW,GACXC,wBAAyB,oIACzBC,wBAAyB,YACzBC,oBAAqB,UACrBC,aAAc,SACdC,mBAAoB,SACpBC,cAAehM,EACfiM,oBAAqB/L,EACrBgM,mBAAoB/L,EACpBgM,UAAU,EACVC,aAAc,GAAI3P,MAAK,KAAM,EAAG,EAAG,EAAG,EAAG,GACzC4P,WAAY,GAAI5P,MAAK,KAAM,EAAG,EAAG,GAAI,EAAG,GACxCiC,cAAe,EACfC,YAAa,EACb2N,QAAUC,QAAS,WACnBC,UACIC,OAAQ,UACRC,YAAa,gBACbC,YAAa,uBAEjBpK,mBACIiC,eAAgB,IAChBhC,kBAAkB,IAG1BhF,QACI,SACA,MACA,QAEJuD,WAAY,WACR,GAAIxC,GAAUkC,KAAKlC,QAASqO,EAAWrN,KAAWpB,EAAM0O,SAAUtO,EAAQuO,iBAC1ErM,MAAKuL,cAAgBvL,KAAKsM,WAAWxO,EAAQyN,cAAe3L,GAC5DI,KAAKwL,oBAAsBxL,KAAKsM,WAAWxO,EAAQ0N,oBAAqB7L,GACxEK,KAAKkL,wBAA0BxN,EAAM8B,SAAS1B,EAAQoN,wBAAyBiB,GAC/EnM,KAAKmL,wBAA0BzN,EAAM8B,SAAS1B,EAAQqN,wBAAyBgB,GAC/EnM,KAAKyL,mBAAqB/N,EAAM8B,SAAS1B,EAAQ2N,mBAAoBU,GACrEnM,KAAKqL,aAAe3N,EAAM8B,SAAS1B,EAAQuN,aAAcc,GACzDnM,KAAKsL,mBAAqB5N,EAAM8B,SAAS1B,EAAQwN,mBAAoBa,GACrEnM,KAAKoL,oBAAsB1N,EAAM8B,SAAS1B,EAAQsN,oBAAqBe,IAE3E5L,UAAW,WACHP,KAAKlC,QAAQ4N,WACT1L,KAAKuM,YACLvM,KAAKwM,iBAELxM,KAAKyM,mBAIjBA,eAAgB,WACZ,GAAI1M,GAAOC,IACXD,GAAKnD,QAAQ8P,GAAG,QAAUpN,EAAI,6BAA8B,SAAUqN,GAClE5M,EAAK6M,QAAQ,UAAYpP,IAAK7B,EAAEqE,MAAM6M,QAAQ,YAAYpP,KAAKC,EAAMD,KAAK,UAC1EkP,EAAEG,mBAEF/M,EAAKjC,QAAQ4N,SAASqB,UAAW,GACjChN,EAAKnD,QAAQ8P,GAAG,WAAapN,EAAI,0BAA2B,SAAUqN,GAAV,GAEhDlI,GAEIuI,CAHPrR,GAAEqE,MAAMiN,SAASC,SAAS,gCACvBzI,EAAO1E,EAAKmH,gBAAgByF,EAAEQ,MAAOR,EAAES,OACvC3I,IACIuI,EAAejN,EAAKsN,gBAAgB5I,GACxC1E,EAAK6M,QAAQ,OACTU,UAAWxO,GACPzB,MAAOoH,EAAKtH,YACZQ,IAAK8G,EAAK8I,WACXP,MAGXL,EAAEG,oBAEPJ,GAAG,WAAapN,EAAI,iCAAkC,SAAUqN,GAAV,GAG7CK,GAFJvI,EAAO1E,EAAKmH,gBAAgByF,EAAEQ,MAAOR,EAAES,MACvC3I,KACIuI,EAAejN,EAAKsN,gBAAgB5I,GACxC1E,EAAK6M,QAAQ,OACTU,UAAWxO,MACPjC,UAAU,EACVQ,MAAOK,EAAM5B,KAAKsB,QAAQqH,EAAKtH,aAC/BQ,IAAKD,EAAM5B,KAAKsB,QAAQqH,EAAKtH,cAC9B6P,MAGXL,EAAEG,mBAGN/M,EAAKjC,QAAQ4N,SAAS8B,UAAW,GACjCzN,EAAKnD,QAAQ8P,GAAG,WAAapN,EAAI,WAAY,SAAUqN,GACnD5M,EAAK6M,QAAQ,QAAUpP,IAAK7B,EAAEqE,MAAM6M,QAAQ,YAAYpP,KAAKC,EAAMD,KAAK,UACxEkP,EAAEG,oBAIdN,eAAgB,WAAA,GACRzM,GAAOC,KACPyN,EAAY,CACZ/P,GAAMc,QAAQkP,SAASC,UACvBF,EAAY,GAEZ1N,EAAKjC,QAAQ4N,SAASqB,UAAW,IACjChN,EAAK6N,eAAiB,GAAIlQ,GAAMmQ,WAAW9N,EAAKnD,SAC5C6Q,UAAWA,EACXK,OAAQ,0BACRC,eAAgBrQ,EAAMc,QAAQD,QAAQ6E,KACtC4K,IAAK,SAAUrB,GAAV,GAKOxF,GACAC,EACA3C,EAEIuI,CARRjN,GAAKkO,YAGJtS,EAAEgR,EAAEuB,QAAQjB,SAASC,SAAS,gCAC3B/F,EAAIwF,EAAExF,EAAEgH,WAAavS,EAAY+Q,EAAExF,EAAEgH,SAAWxB,EAAExF,EAClDC,EAAIuF,EAAEvF,EAAE+G,WAAavS,EAAY+Q,EAAEvF,EAAE+G,SAAWxB,EAAEvF,EAClD3C,EAAO1E,EAAKmH,gBAAgBC,EAAGC,GAC/B3C,IACIuI,EAAejN,EAAKsN,gBAAgB5I,GACxC1E,EAAK6M,QAAQ,OACTU,UAAWxO,GACPzB,MAAOoH,EAAKtH,YACZQ,IAAK8G,EAAK8I,WACXP,MAGXL,EAAEG,qBAId/M,EAAKqO,kBAAoB,GAAI1Q,GAAMmQ,WAAW9N,EAAKnD,SAC/C6Q,UAAWA,EACXM,eAAgBrQ,EAAMc,QAAQD,QAAQ6E,KACtC0K,OAAQ,iCACRE,IAAK,SAAUrB,GAAV,GAIGxF,GACAC,EACA3C,EAEIuI,CAPJjN,GAAKkO,aAGL9G,EAAIwF,EAAExF,EAAEgH,WAAavS,EAAY+Q,EAAExF,EAAEgH,SAAWxB,EAAExF,EAClDC,EAAIuF,EAAEvF,EAAE+G,WAAavS,EAAY+Q,EAAEvF,EAAE+G,SAAWxB,EAAEvF,EAClD3C,EAAO1E,EAAKmH,gBAAgBC,EAAGC,GAC/B3C,IACIuI,EAAejN,EAAKsN,gBAAgB5I,GACxC1E,EAAK6M,QAAQ,OACTU,UAAWxO,MACPjC,UAAU,EACVQ,MAAOK,EAAM5B,KAAKsB,QAAQqH,EAAKtH,aAC/BQ,IAAKD,EAAM5B,KAAKsB,QAAQqH,EAAKtH,cAC9B6P,MAGXL,EAAEG,sBAIV/M,EAAKjC,QAAQ4N,SAAS8B,UAAW,IACjCzN,EAAKsO,gBAAkB,GAAI3Q,GAAMmQ,WAAW9N,EAAKnD,SAC7C6Q,UAAWA,EACXM,eAAgBrQ,EAAMc,QAAQD,QAAQ6E,KACtC0K,OAAQ,WACRE,IAAK,SAAUrB,GAAV,GAIG2B,GACAC,CAJAxO,GAAKkO,aAGLK,EAAe3S,EAAEgR,EAAEuB,QAAQrB,QAAQ,YACnC0B,EAAe5S,EAAEgR,EAAE6B,MAAMC,cACzBF,EAAarB,SAAS,aACtBnN,EAAK6M,QAAQ,UAAYpP,IAAK8Q,EAAa7Q,KAAKC,EAAMD,KAAK,UACnD6Q,EAAapB,SAAS,mBAC9BnN,EAAK6M,QAAQ,QAAUpP,IAAK8Q,EAAa7Q,KAAKC,EAAMD,KAAK,UAE7DkP,EAAEG,uBAKlB4B,QAAS,SAAUC,GAAV,GAMI7R,GACD8R,EAUJhH,EAhBAiH,KACAC,KACAhR,EAAUkC,KAAKlC,QACfiC,EAAOC,KACP8H,EAAS/H,EAAKkF,kBAClB,KAASnI,EAAM,EAAGA,EAAM6R,EAAMzR,OAAQJ,IAC9B8R,KACJA,EAAO5I,KAAOjG,EAAK0L,oBACf3P,KAAM6S,EAAM7R,GACZiS,SAAUhP,EAAKwM,cAEf7O,EAAM5B,KAAKkT,QAAQL,EAAM7R,MACzB8R,EAAOK,UAAY,WAEvBJ,EAAQjR,KAAKgR,EA2CjB,OAzCIhH,GAAY5H,KAAK6H,iBACjB/J,EAAQ2M,YACRqE,EAAKlR,MACDoI,KAAMlI,EAAQiO,SAASC,OACvBA,QAAQ,EACRkD,YAAa,SAAUpS,GACnB,GAAIqE,GAAarE,CAEjB,OADAA,GAAM8K,EAAU1K,QAAuC,aAA7B6C,EAAKgI,oBAAqCjL,EAAM6R,EAAMzR,OAASJ,EAClFiD,EAAKuL,oBACRxP,KAAM6S,EAAM7R,GACZ8K,UAAW,WACP,MAAO7H,GAAKsN,iBAAkBlM,WAAYA,UAM9DnB,KAAKmP,cAAcnP,KAAKoE,YAAapE,KAAKqE,UAAW,SAAUvI,EAAMmP,EAAWmE,EAAWC,GAAtC,GAC7C7P,GAAWyL,EAAYlL,EAAKmL,wBAA0BnL,EAAKoL,wBAC3DmE,GACAtJ,KAAMxG,GAAW1D,KAAMA,IACvBmT,UAAWI,EAAc,cAAgB,GAE7CP,GAAKlR,KAAK0R,KAEV1H,EAAU1K,SACuB,aAA7B8C,KAAK+H,oBACDD,GACAgH,EAAO9O,KAAKuP,kBAAkBV,EAASC,GACvCD,EAAU7O,KAAKwP,qBAAqB5H,EAAW,KAAM5H,KAAKoL,sBAE1D0D,EAAO9O,KAAKyP,kBAAkB7H,EAAWkH,EAAM9O,KAAKoL,qBAIpDyD,EADA/G,EACU9H,KAAKwP,qBAAqB5H,EAAWiH,EAAS7O,KAAKoL,oBAAqByD,GAExE7O,KAAKwP,qBAAqB5H,EAAWiH,EAAS7O,KAAKoL,uBAKrEyD,QAASA,EACTC,KAAMA,IAGdY,QAAS,WAAA,GAGGC,GACA7D,EAoBA/L,EAvBJjC,EAAUkC,KAAKlC,OACfA,GAAQ+N,UAAW,IACf8D,EAAO,4CACP7D,EAAUhO,EAAQ+N,OAAOC,QACzB9L,KAAKuM,cACLoD,GAAQ,8EACRA,GAAQ7R,EAAQiO,SAAShB,MAAQ,eAEjCe,GAAuB,YAAZA,EACP9L,KAAKuM,aACLoD,GAAQ,gFACRA,IAAS7R,EAAQgN,cAAgBhN,EAAQiO,SAASE,YAAcnO,EAAQiO,SAASG,aAAe,gBAEhGyD,GAAQ,gCACRA,GAAQ,oHACRA,IAAS7R,EAAQgN,cAAgBhN,EAAQiO,SAASE,YAAcnO,EAAQiO,SAASG,aAAe,YAChGyD,GAAQ,SAGZA,GAAQ,SAEZA,GAAQ,SACR3P,KAAK6L,OAASlQ,EAAEgU,GAAM5J,SAAS/F,KAAKpD,SAChCmD,EAAOC,KACXA,KAAK6L,OAAOa,GAAG,QAAUpN,EAAI,uBAAwB,SAAUqN,GAC3DA,EAAEG,iBACF/M,EAAK6M,QAAQ,YACTxC,KAAMrK,EAAKK,MAAQtC,EAAQsC,KAC3BtE,KAAMgC,EAAQhC,KACd8T,WAAY9R,EAAQgN,kBAG5B9K,KAAK6L,OAAOa,GAAG,QAAUpN,EAAI,qBAAsB,SAAUqN,GAAV,GAE3C7L,GACA+O,EACArG,EACA1N,EAEIiF,CANR4L,GAAEG,iBACEhM,EAAWf,EAAKjC,QAAQgD,SACxB+O,EAAS,QACTrG,EAAc,GAAIxN,MAElB8E,GACIC,EAAiBrD,EAAMoD,SAASoB,OAAOsH,EAAa1I,GACxDhF,EAAO4B,EAAMoD,SAASqB,QAAQqH,EAAaA,EAAYpH,oBAAqBrB,IAE5EjF,EAAO0N,EAEXzJ,EAAK6M,QAAQ,YACTxC,KAAMrK,EAAKK,MAAQtC,EAAQsC,KAC3ByP,OAAQA,EACR/T,KAAMA,QAKtBqT,cAAe,SAAU9S,EAAKC,EAAKuT,EAAQC,GAA5B,GAGP/P,GAAagQ,EAA8BC,EAA8BhF,EAA8CiF,EAA0DC,EAAoD7S,EAAwB8S,EAA4BC,EAAStT,EAASI,EAAQyS,EAU/SU,EAAyDC,EAAyCC,EAAqDC,CAD/J,KAXAnU,EAAMR,EAAgBQ,GACtBC,EAAMT,EAAgBS,GAClByD,EAAOC,KAAM+P,EAAQ7T,EAAgBG,GAAM2T,EAAQ9T,EAAgBI,GAAM0O,EAAiBjL,EAAKjC,QAAQkN,eAAgBiF,EAAkBlQ,EAAKjC,QAAQmN,UAAYjM,EAAekR,EAAaD,EAAkBjF,GAAkB,EAAG3N,EAAQ,GAAIrB,QAAMK,IAAM8T,EAAW9S,EAAMD,UAAoBN,EAAM,EAAW6S,EAAO,GAC9TzS,EAAS+B,EAAaiR,EAClBH,GAASC,IACLD,EAAQC,IACRA,GAAS/Q,GAEb/B,GAAU8S,EAAQD,GAASG,GAE/BhT,EAASiB,KAAK4E,MAAM7F,GACbJ,EAAMI,EAAQJ,IACbuT,EAAmBvT,GAAOmT,EAAkBC,GAAaI,EAAsC,IAArBD,EAAwBE,EAAcF,EAAmBrF,EAAiB,EAAGwF,EAAgBH,IAAqBrF,EAAiB,EACjN2E,GAAQE,EAAOxS,EAAOiT,EAAgBC,EAAaC,GACnDvU,EAAQoB,EAAO6S,GAAY,EAc/B,OAZIF,KACAI,EAAUlU,EAAgBmB,GACtB8S,EAAW9S,EAAMD,YACjBgT,GAAWnR,GAEXmR,EAAUJ,IACV3S,EAAQ,GAAIrB,QAAMM,MAGtBwT,IACAH,GAAQG,EAAMzS,IAEXsS,GAEXc,SAAU,SAAU9B,GAAV,GAYF+B,GA4CAC,EA4BKC,EAnFL7Q,EAAOC,KACPlC,EAAUiC,EAAKjC,QACfT,EAAQ0C,EAAKqE,YACbzG,EAAMqC,KAAKqE,UACXrD,EAAc,EACd0H,EAAW,EACXJ,EAAcqG,EAAMzR,OACpByS,EAAO,GACP/H,EAAY5H,KAAK6H,iBACjByD,EAAqBtL,KAAKsL,mBAC1BuF,GAAqB,EAErB/I,EAAS/H,EAAKkF,mBACd6L,EAAS,CAsEb,KArEIlJ,EAAU1K,SACV2T,EAAkD,aAA7B9Q,EAAKgI,oBACtB8I,GACAnI,EAAW1I,KAAKiI,kBAAkBjI,KAAK+Q,UAAU7T,OAAS,GACtD4K,IACA9G,EAAchB,KAAKgI,qBAAqBhI,KAAKgR,aAAa9T,OAAS,IAEnEY,EAAQ2M,aACRiG,EAAyB,SAAUvP,GAAV,GAcRrE,GAbTmU,EAAS,0CACTC,EAAiBpJ,EAAS,EAAI3G,EAC9ByG,EAAY,WACZ,MAAO7H,GAAKsN,iBAAkBlM,WAAY+P,IAE9C,IAAIpJ,EACA,KAAOoJ,EAAiBlQ,EAAakQ,IACjCD,GAAU,OAAS3F,GACfxP,KAAM6S,EAAMmC,GACZlJ,UAAWA,IACV,YAGT,KAAS9K,EAAM,EAAGA,EAAM6R,EAAMzR,OAAQJ,IAClCmU,GAAU,OAAS3F,GACfxP,KAAM6S,EAAM7R,GACZ8K,UAAWA,IACV,OAGb,OAAOqJ,GAAS,WAKpBjQ,EADA8G,EACc9H,KAAKgI,qBAAqBhI,KAAKgR,aAAa9T,OAAS,GAAK8C,KAAKgI,qBAAqB,GAEpFhI,KAAKgI,qBAAqBhI,KAAKgR,aAAa9T,OAAS,IAI/EyS,GAAQ,UACJgB,EAAY,SAAU7U,EAAMmP,EAAWmE,GAA3B,GAGRtS,GAAKI,EAKOiU,EAPZ3O,EAAU,GACV4O,EAAW,CAGf,IADA5O,EAAU,OAAS4M,EAAY,wBAA0B,IAAM,IAC3DtH,EACA,IAAKhL,EAAM,EAAGI,EAASoL,EAAaxL,EAAMI,EAAQJ,IAAO,CACrD,IAAKsU,EAAW,EAAGA,EAAWpQ,EAAaoQ,IACnCD,EAAYrU,EACZ+T,IACAM,EAAYL,GAEhBtO,EAAUzC,EAAKsR,mBAAmB7O,EAASmM,EAAO7S,EAAMqV,EAAWC,EAAUR,EAEjF,IAAIC,EACA,UAIR,MAAOO,EAAWpQ,EAAaoQ,IAC3B,IAAKtU,EAAM,EAAGI,EAASoL,EAAaxL,EAAMI,EAAQJ,IAC9C0F,EAAUzC,EAAKsR,mBAAmB7O,EAASmM,EAAO7S,EAAMgB,EAAKsU,EAAUR,EAKnF,OADApO,IAAW,SAGNoO,EAAS,EAAGA,EAASlI,EAAUkI,IACpCjB,GAAQe,EAAyBA,EAAuBE,GAAU,GAClEjB,GAAQ3P,KAAKmP,cAAc9R,EAAOM,EAAKgT,GACnCE,GACAC,GAGRnB,IAAQ,WACR3P,KAAKwC,QAAQF,KAAK,SAASgP,OAAO3B,IAEtC0B,mBAAoB,SAAU7O,EAASmM,EAAO7S,EAAMgB,EAAKsU,EAAUR,GAA/C,GAGZW,GAFAxR,EAAOC,KACPwR,EAAU,GAEVnG,EAAerL,KAAKqL,aACpBwF,EAAkD,aAA7B7Q,KAAK+H,oBAC1BH,EAAY,SAAUzG,GACtB,MAAO,YACH,MAAOpB,GAAKsN,iBAAkBlM,WAAYA,KAiBlD,OAdIzD,GAAM5B,KAAKkT,QAAQL,EAAM7R,MACzB0U,GAAW,YAEX9T,EAAM5B,KAAKI,gBAAgBJ,GAAQ4B,EAAM5B,KAAKI,gBAAgB8D,KAAKlC,QAAQ6N,eAAiBjO,EAAM5B,KAAKI,gBAAgBJ,IAAS4B,EAAM5B,KAAKI,gBAAgB8D,KAAKlC,QAAQ8N,cAAgB5L,KAAKyR,WAAW9C,EAAM7R,OAC9M0U,GAAW,mBAEfhP,GAAW,OAAqB,KAAZgP,EAAiB,WAAaA,EAAU,IAAM,IAAM,IACxED,EAAW7T,EAAM5B,KAAKsB,QAAQuR,EAAM7R,IACpCY,EAAM5B,KAAKG,QAAQsV,EAAU7T,EAAM5B,KAAKI,gBAAgBJ,IACxD0G,GAAW6I,GACPvP,KAAMyV,EACN3J,UAAWA,EAAUiJ,IAAuB9Q,EAAKkF,mBAAqB2L,EAASQ,KAEnF5O,GAAW,SAGfiP,WAAY,SAAU3V,GAAV,GAGC4V,GAFLC,EAAM7V,EAAK8V,SACX7T,EAAWiC,KAAKK,SACpB,KAASqR,EAAI,EAAGA,EAAI3T,EAASb,OAAQwU,IACjC,GAAI3T,EAAS2T,KAAOC,EAChB,OAAO,CAGf,QAAO,GAEXE,QAAS,SAAUlD,GAAV,GAUDmD,GATA/R,EAAOC,IACX2O,GAAQA,MACR3O,KAAKyJ,OAASkF,EACd3O,KAAK+R,WAAapD,EAAM,GACxB3O,KAAKgS,SAAWrD,EAAMA,EAAMzR,OAAS,GAAK,GAC1C8C,KAAKiS,aAAajS,KAAK0O,QAAQC,IAC/B3O,KAAKyQ,SAAS9B,GACd3O,KAAK0P,UACL1P,KAAKkS,gBACDJ,EAAe9R,KAAKpD,QAAQ0F,KAAK,kCACjCwP,EAAa5U,SACb8C,KAAKmS,oBAAsBL,EAAa7L,QAAQ,GAAGZ,cAEvDtF,EAAKnD,QAAQ8P,GAAG,QAAUpN,EAAI,aAAc,SAAUqN,GAAV,GAapClI,GAZA2N,EAAKzW,EAAEgR,EAAE0F,eAAexF,QAAQ,MAChC3K,EAASkQ,EAAGlQ,SACZoQ,EAAkB,EAClBC,EAAmB3T,EAAYwT,EAC/BrS,GAAKkF,qBACDlF,EAAKsH,wBACLiL,EAAkB5T,EAAWqB,EAAKsC,OAClCkQ,EAAmB,GAEnBA,EAAmB3T,EAAYmB,EAAKyS,cAGxC/N,EAAO1E,EAAKmH,gBAAgBhF,EAAOsB,KAAO8O,EAAiBpQ,EAAOe,IAAMsP,GAC5ExS,EAAK6M,QAAQ,YACTxC,KAAM,MACNtO,KAAM2I,EAAKtH,iBAIvBiH,UAAW,WACP,GAAItG,GAAUkC,KAAKlC,OACnB,OAAOA,GAAQgN,cAAgBhN,EAAQ6N,aAAe7N,EAAQsG,WAElEC,QAAS,WACL,GAAIvG,GAAUkC,KAAKlC,OACnB,OAAOA,GAAQgN,cAAgBhN,EAAQ8N,WAAa9N,EAAQuG,SAEhElH,UAAW,WACP,MAAO6C,MAAK+R,YAEhBxE,QAAS,WACL,MAAOvN,MAAKgS,UAEhBS,KAAM,SAAU5V,GACZ,GAAI0L,GAAOrM,EAAgB8D,KAAKqE,YAAcpF,CAI9C,OAHIpC,KACA0L,EAAO,GAEJ,GAAIvM,MAAKgE,KAAKgS,SAAS1U,UAAYiL,IAE9CmK,SAAU,WACN,MAAOhV,GAAM5B,KAAK6W,QAAQ3S,KAAKuN,YAEnCqF,aAAc,WACV,MAAOlV,GAAM5B,KAAK+W,YAAY7S,KAAK7C,cAEvCqD,mBAAoB,WAChBR,KAAK6R,SAAS7R,KAAKlC,QAAQhC,QAE/BgX,QAAS,WACL,GAAI/S,GAAOC,IACPD,GAAKiE,yBACL+O,cAAchT,EAAKiE,yBAEnBjE,EAAKyS,aACLzS,EAAKyS,YAAYQ,IAAI1T,GAErBS,EAAKnD,SACLmD,EAAKnD,QAAQoW,IAAI1T,GAEjBS,EAAK8L,QACL9L,EAAK8L,OAAOtJ,SAEhB9D,EAAcwB,GAAG6S,QAAQ5S,KAAKF,MAC1BA,KAAKuM,aAAexM,EAAKjC,QAAQ4N,WAC7B3L,EAAKjC,QAAQ4N,SAASqB,UAAW,IACjChN,EAAK6N,eAAekF,UACpB/S,EAAKqO,kBAAkB0E,WAEvB/S,EAAKjC,QAAQ4N,SAAS8B,UAAW,GACjCzN,EAAKsO,gBAAgByE,YAIjCG,QAAS,SAAUnV,GAAV,GAKDsG,GACAC,EACAhH,EACAM,EAPAsV,EAAUxU,EAAcwB,GAAGgT,QAAQ/S,KAAKF,KAAMlC,EAClD,OAAIA,GAAQjB,SACDoW,GAEP7O,EAAYlI,EAAgB8D,KAAKoE,aACjCC,EAAUnI,EAAgB8D,KAAKqE,YAAc3G,EAAM5B,KAAKmD,WACxD5B,EAAQnB,EAAgB4B,EAAQT,OAChCM,EAAMzB,EAAgB4B,EAAQH,MAAQD,EAAM5B,KAAKmD,WAC9CgU,GAAW7O,GAAa/G,GAASM,GAAO0G,IAEnD6O,mBAAoB,SAAU5J,GAC1B,GAAIpH,GAASoH,EAAKpH,QAClB,OAAOlC,MAAKkH,gBAAgBhF,EAAOsB,KAAMtB,EAAOe,MAEpD+F,kBAAmB,WACf,GAAIlL,GAAUkC,KAAKlC,OACnB,OAAOA,GAAQmN,UAAYnN,EAAQkN,eAAiBhM,GAExDmU,eAAgB,SAAUrX,GAAV,GACRgC,GAAUkC,KAAKlC,QACfsV,EAAiBlX,EAAgBJ,GACjCsI,EAAYlI,EAAgB8D,KAAKoE,aACjCiP,EAAmBvV,EAAQmN,UAAYnN,EAAQkN,eAAiBhM,CACpE,QAAQoU,EAAiBhP,GAAaiP,GAE1CC,WAAY,SAAUxX,EAAM+I,GACxB,MAAIA,GACO7E,KAAKuT,eAAezX,GAExBkE,KAAKmT,eAAerX,IAE/ByX,eAAgB,SAAUzX,EAAMU,GAAhB,GACRM,GACAI,EAEAsW,EACAC,EAFAC,EAAQ1T,KAAKyJ,WAGbvH,EAAS,CACb,KAAKpF,EAAM,EAAGI,EAASwW,EAAMxW,OAAQJ,EAAMI,EAAQJ,IAG/C,GAFA0W,EAAY9V,EAAM5B,KAAKsB,QAAQsW,EAAM5W,IACrC2W,EAAU,GAAIzX,MAAK0B,EAAM5B,KAAKsB,QAAQsW,EAAM5W,IAAMQ,UAAY2B,GAAczC,EAAW,EAAI,IACvFL,EAAcL,EAAM0X,EAAWC,GAC/B,MAAO3W,GAAMoF,CAGrB,WAEJyR,qBAAsB,SAAU/W,EAASgX,GAAnB,GA0Bd9E,GAIA7L,EACKnG,EAASI,EACV2W,EACKC,EAAOC,EAhChBC,EAAYJ,EAAUjO,aACtBsO,EAAaL,EAAUvW,MAAM6H,MAC7BgP,EAAWN,EAAUjW,IAAIuH,MACzBiP,EAAe1V,EAAc2V,gBAAgBR,EAAU7W,SAAUkX,EAAYC,GAC7EG,EAAqBrU,KAAKsU,oBAAsB,EAChDC,EAAa,EACbC,EAAcP,IAAeC,EAAW,EAAI,EAC5CO,EAAczU,KAAKmS,oBACnB9U,EAAQuW,EAAU5O,WAsBtB,KArBApI,EAAQ8G,KACJF,KAAMnG,EAAMmI,WAAa+O,EACzB3Q,MAAOoQ,EAAYQ,IAEvBZ,EAAUc,UACNC,UAAWV,EACX5W,MAAO4W,EACPtW,IAAKuW,EACLtX,QAASA,IAEbuX,EAAavW,MACT+W,UAAWV,EACX5W,MAAO4W,EACPtW,IAAKuW,EACLtX,QAASA,IAETkS,EAAOrQ,EAAcmW,WAAWT,GAChCrF,EAAK5R,QAAU4R,EAAK5R,OAASmX,IAC7BrU,KAAKsU,mBAAqBxF,EAAK5R,QAE/B+F,EAAM2Q,EAAUvW,MAAMiI,UACjBxI,EAAM,EAAGI,EAAS4R,EAAK5R,OAAQJ,EAAMI,EAAQJ,IAElD,IADI+W,EAAY/E,EAAKhS,GAAKC,OACjB+W,EAAI,EAAGC,EAAcF,EAAU3W,OAAQ4W,EAAIC,EAAaD,IAC7DnY,EAAEkY,EAAUC,GAAGlX,SAAS8G,KAAMT,IAAKA,EAAMnG,EAAM2X,KAI3DI,gBAAiB,SAAUjY,EAASqG,EAAKU,EAAQiQ,GAAhC,GAQT/E,GAASmF,EAAmCc,EAAoCC,EAAcC,EAAoCC,EAIlIC,EACKpY,EAASI,EAEL4W,EAAOC,EACRoB,EAfRnQ,EAAY4O,EAAUvW,KAY1B,KAXAT,GACIA,QAASA,EACT+X,UAAW3P,EAAUE,MACrB7H,MAAO4F,EACPtF,IAAKsF,EAAMU,GAEFqQ,EAAYhP,EAAU2B,YAAamO,EAA+B,GAAZd,EAA+BgB,EAAgBpB,EAAU7W,SAAUkY,EAAaxW,EAAc2V,gBAAgBY,EAAepY,EAAQS,MAAOT,EAAQe,KACvNiW,EAAUc,SAAS9X,GACnBqY,EAAWrX,KAAKhB,GAChBiS,EAAUpQ,EAAc2W,cAAcH,GAClCC,GAAelB,EAAYc,GAAoBjG,EAAQ3R,OAClDJ,EAAM,EAAGI,EAAS2R,EAAQ3R,OAAQJ,EAAMI,EAAQJ,IAErD,IADAiY,EAAelG,EAAQ/R,GAAKC,OACnB+W,EAAI,EAAGC,EAAcgB,EAAa7X,OAAQ4W,EAAIC,EAAaD,IAC5DqB,EAAkBD,EAAc,EACpCH,EAAajB,GAAGlX,QAAQ,GAAGyY,MAAMzR,OAASuR,EAAkB,EAAIA,EAAkBD,GAAe,KACjGH,EAAajB,GAAGlX,QAAQ,GAAGyY,MAAM7R,MAAQxD,KAAKqD,OAASyR,EAAmB,GAAK9P,EAAUQ,WAAa1I,EAAMoY,EAAc,EAAI,MAI1II,eAAgB,SAAUnR,EAAOvH,EAASgX,GAA1B,GACRvW,GAAQ8G,EAAMoR,YAAcpR,EAAM9G,MAClCM,EAAMwG,EAAMqR,UAAYrR,EAAMxG,IAC9B+G,EAAOkP,EAAU5Q,UAAU3F,EAAOM,GAAK,GACvCgG,EAASe,EAAKoB,OAASpB,EAAKzB,IAAM,CAClCU,GAAS,IACTA,EAAS,GAEb/G,EAAQ8G,KACJT,IAAKyB,EAAKzB,IACVU,OAAQA,IAEZ3D,KAAK6U,gBAAgBjY,EAAS8H,EAAKzB,IAAKrG,EAAQ,GAAGyI,aAAcuO,IAErE/M,oBAAqB,SAAU1C,EAAOsR,EAAeC,EAAMC,GAAtC,GAabC,GAOAC,EACAC,EAWAlO,EASAmO,EAeAnZ,EAvDA4C,EAAWiW,EAAgBzV,KAAKuL,cAAgBvL,KAAKwL,oBACrD1N,EAAUkC,KAAKlC,QACf4N,EAAW5N,EAAQ4N,SACnBqD,EAAW/O,KAAKuM,YAChByJ,EAAatK,GAAYA,EAASoH,WAAY,IAAU/D,EACxDkH,EAAYvK,GAAYA,EAASwK,UAAW,EAC5C/Y,EAAYC,EAAQ4C,KAAK7C,aACzBoQ,EAAUnQ,EAAQ4C,KAAKuN,WACvBnJ,EAAYlI,EAAgB8D,KAAKoE,aACjCC,EAAUnI,EAAgB8D,KAAKqE,WAC/B+O,EAAiBjP,EAAMgS,MAAM,SAC7BC,EAAejS,EAAMgS,MAAM,MAmD/B,OAjDI/R,IAAaC,IACbA,EAAUnI,EAAgB,GAAIF,MAAKgE,KAAKqE,UAAU/G,UAAY2B,EAAa,KAE1EwW,GAAkBtR,EAAMtH,WACzB0Q,EAAU,GAAIvR,MAAKuR,EAAQjQ,UAAY2B,IAEvC4W,EAAiB1R,EAAM9G,MACvByY,EAAe3R,EAAMxG,IACrBwG,EAAMtH,WACNiZ,EAAe1Y,EAAQ+G,EAAMxG,OAE5BxB,EAAciB,EAAQyY,GAAiB1Y,EAAWoQ,KAAapR,EAAc2Z,EAAc3Y,EAAWoQ,IAAYkI,GAAiBrC,EAAiBhP,GAAagS,EAAe/R,EACjLuR,GAAS,EACFxY,EAAQyY,GAAkB1Y,GAAasY,GAAiBrC,EAAiBhP,EAChFuR,GAAO,GACAG,EAAevI,IAAYkI,GAAiBA,GAAiBW,EAAe/R,KACnFqR,GAAO,GAEP9N,EAAY5H,KAAKqW,eAAelS,GAChCA,EAAMoR,YAAcnC,IAAmB1V,EAAM5B,KAAKI,gBAAgBiI,EAAM9G,SACxEwY,EAAiB,GAAI7Z,MAAKoX,GAC1ByC,EAAiBnY,EAAMoD,SAASwV,MAAMT,EAAgB,YAEtD1R,EAAMqR,UAAYY,IAAiB1Y,EAAM5B,KAAKI,gBAAgBiI,EAAMxG,OACpEmY,EAAe,GAAI9Z,MAAKoa,GACxBN,EAAepY,EAAMoD,SAASwV,MAAMR,EAAc,YAElDC,EAAOjX,MACPyX,GAAI7Y,EAAM6Y,GACVN,UAAWA,EACXD,WAAYA,EACZJ,OAAQA,EACRF,KAAMA,EACNC,KAAMA,EACNa,UAAiC,GAAtBxW,KAAKyJ,OAAOvM,OACvB0K,UAAWA,EACXb,cAAc,EACdgF,SAAUjO,EAAQiO,UACnB5H,GACC9G,MAAOwY,EACPlY,IAAKmY,IAELlZ,EAAUjB,EAAE6D,EAASuW,IACzB/V,KAAKyW,QAAQ,UAAW,WACpB,OACIC,SAAU9Z,EACVmZ,OAASY,SAAUZ,OAGpBnZ,GAEXga,cAAe,SAAUzS,GAAV,GAgBP3H,GAfAqa,EAAgB7W,KAAKoE,YAAa0S,EAAc9W,KAAKqE,UAAWD,EAAYD,EAAMoR,YAAcpR,EAAM9G,MAAOgH,EAAUF,EAAMqR,UAAYrR,EAAMxG,GAYnJ,OAXIzB,GAAgB4a,KAAiB5a,EAAgBwB,EAAM5B,KAAKsB,QAAQ0Z,MACpEA,EAAcpZ,EAAM5B,KAAKsB,QAAQ0Z,GACjC7a,EAAQ6a,EAAa7X,EAAa,IAElCkF,EAAM4S,MAAM,OAAS5S,EAAM4S,MAAM,WACjC1S,GAAWF,EAAM4S,MAAM,QAAU9X,EAAa,IAElDoF,EAAUF,EAAMqR,SAAWnR,EAAUF,EAAM4S,MAAM,OAAS7a,EAAgB,GAAIF,MAAKqI,IACnFD,EAAYD,EAAMoR,WAAanR,EAAYD,EAAM4S,MAAM,SAAW7a,EAAgB,GAAIF,MAAKoI,IAC3F0S,EAAc5a,EAAgB4a,GAC9BD,EAAgB3a,EAAgB2a,GAC5BA,IAAkBzS,GAAaA,IAAcC,IAG7C7H,EAAW4H,IAAc0S,EACtBva,EAAc6H,EAAWyS,EAAeC,EAAata,IAAaD,EAAc8H,EAASwS,EAAeC,EAAata,IAAaD,EAAcsa,EAAezS,EAAWC,IAAY9H,EAAcua,EAAa1S,EAAWC,KAEvO2S,cAAe,SAAU7S,GAAV,GACPzB,GAAS1C,KAAK0C,OAAO,GACrB8Q,EAAY9Q,EAAOuU,YAAY5Z,MAC/BoW,EAAU/Q,EAAOwU,WAAWvZ,IAAM,EAClCyG,EAAY1G,EAAM5B,KAAK6G,UAAUwB,EAAM9G,OACvCgH,EAAU3G,EAAM5B,KAAK6G,UAAUwB,EAAMxG,IACzC,QAAQxB,EAAciI,EAAWoP,EAAWC,IAAYtX,EAAckI,EAASmP,EAAWC,IAAYtX,EAAcqX,EAAWpP,EAAWC,IAAYlI,EAAcsX,EAASrP,EAAWC,OAAelI,EAAckI,EAASmP,EAAWA,IAAcrX,EAAckI,EAASD,EAAWA,IAAcD,EAAMtH,WAEjTsa,0BAA2B,SAAUxT,GAAV,GAGfyT,GAGSjW,CALjB,IAAInB,KAAKqX,UAAY1T,IACjB3D,KAAKqX,QAAU1T,EACXyT,EAAcpX,KAAKpD,QAAQ0F,KAAK,kCAChC8U,EAAYla,QAEZ,IADAka,EAAYnK,SAASvH,IAAI1F,KAAKpD,QAAQ0F,KAAK,8BAA8B2K,UAAUtJ,OAAOA,GACjFxC,EAAa,EAAGA,EAAanB,KAAK0C,OAAOxF,OAAQiE,IACtDnB,KAAK0C,OAAOvB,GAAYmW,WAKxCC,cAAe,SAAUxa,EAAQoE,GAAlB,GAGPgD,GACArH,EACAI,EAIQsa,EACA5S,EACAhI,EAAS0E,EAAQ3E,EAAOU,EAAOM,EAAKjB,EAQ5B+a,EACKlT,EAcDmT,EAOIhC,EAiBRzB,EACAC,EAIIyD,EACAC,EACKlG,EAEDmG,EAlExBC,EAAuB9X,KAAKwS,YAAYlQ,KAAK,kCAC7CwF,EAAS9H,KAAKiF,kBAIlB,KAAKnI,EAAM,EAAGI,EAASH,EAAOG,OAAQJ,EAAMI,EAAQJ,IAEhD,GADAqH,EAAQpH,EAAOD,GACXkD,KAAKgX,cAAc7S,GAInB,GAHIqT,EAAkBrT,EAAMtH,UAAYsH,EAAMqC,YAAcvH,EACxD2F,EAAY4S,IAAoBxX,KAAKqH,uBAAyByQ,EAAuB9X,KAAKwC,QAEzFgV,GAsCE,GAAIxX,KAAKlC,QAAQ2M,aACpB/N,EAAQsD,KAAK0C,OAAOvB,GACfzE,EAAMM,oBACPN,EAAMM,sBAEVsE,EAAS5E,EAAMqb,WAAW5T,GACtB7C,EAAOpE,QAIP,GAHAP,EAAQ2E,EAAO,GACX2S,EAAatX,EAAMU,MAAM6H,MACzBgP,EAAWvX,EAAMgB,IAAIuH,MACrB4C,GAAUmM,IAAeC,EAKzB,IAJA7W,EAAQV,EAAMU,MAAMA,MACpBM,EAAMhB,EAAMgB,IAAIA,IACZga,EAAW,GAAI3b,MAAKqB,GACpBua,EAAS,GAAI5b,MAAKqB,GACbqU,EAAI/U,EAAMU,MAAM6H,MAAOwM,GAAK/U,EAAMgB,IAAIuH,MAAOwM,IAClD9U,EAAUoD,KAAK6G,oBAAoB1C,GAAQqT,EAAiB9F,IAAMwC,EAAUxC,IAAMuC,GAC9E4D,EAAYnb,EAAMsb,cAAcL,EAAUC,GAAQ,GAAM,GAC5DA,EAAOK,QAAQL,EAAOxa,UAAY,GAClCua,EAASM,QAAQN,EAASva,UAAY,GACtC4C,KAAK2T,qBAAqB/W,EAASib,GACnCpb,EAAmBC,EAAOmb,EAAWjb,GAAS,GAC9CA,EAAQmJ,SAASnB,GACjB5E,KAAKkY,mBAAmBtb,OAG5BA,GAAUoD,KAAK6G,oBAAoB1C,GAAQqT,GAC3CxX,KAAK2T,qBAAqB/W,EAAS0E,EAAO,IAC1C7E,EAAmBC,EAAO4E,EAAO,GAAI1E,GAAS,GAC9CA,EAAQmJ,SAASnB,GACjB5E,KAAKkY,mBAAmBtb,OAnEhC,IAAIoD,KAAK4W,cAAczS,GAOnB,IANAzH,EAAQsD,KAAK0C,OAAOvB,GACfzE,EAAMM,oBACPN,EAAMM,sBAEVsE,EAAS5E,EAAMqb,WAAW5T,GACtBsT,EAAanW,EAAOpE,OACfqH,EAAa,EAAGA,EAAakT,EAAYlT,IAC9C5H,EAAQ2E,EAAOiD,GACflH,EAAQ8G,EAAM9G,MACdM,EAAMwG,EAAMxG,IACR8Z,EAAa,IACM,IAAflT,EACA5G,EAAMhB,EAAMgB,IAAI4P,UACThJ,GAAckT,EAAa,EAClCpa,EAAQV,EAAMU,MAAMF,aAEpBE,EAAQV,EAAMU,MAAMF,YACpBQ,EAAMhB,EAAMgB,IAAI4P,YAGpBmK,EAAavT,EAAM2C,OACnBzJ,MAAOA,EACPM,IAAKA,EACL4X,WAAYpR,EAAMoR,WAClBC,SAAUrR,EAAME,UAEhBrE,KAAK4W,cAAcc,KACfhC,EAAO/Y,EAAM+Y,KACjB9Y,EAAUoD,KAAK6G,oBAAoB1C,GAAQqT,EAAiB9B,EAAM/Y,EAAMgZ,MACxE/Y,EAAQmJ,SAASnB,GACjB5E,KAAKkY,mBAAmBtb,GACxBoD,KAAKsV,eAAeoC,EAAY9a,EAASD,GACzCF,EAAmBC,EAAOC,EAAOC,GAAS,KAyCtEub,OAAQ,SAAUpb,GAAV,GAcAqb,GAEAC,EAOA1U,EAEKxC,CAAT,KAxBAnB,KAAKsU,mBAAqB,EAC1BtU,KAAKS,UACLT,KAAKpD,QAAQ0F,KAAK,YAAYC,SAC9BxF,EAAS,GAAIW,GAAMqY,KAAKuC,MAAMvb,GAAQwb,OAE9BC,MAAO,QACPC,IAAK,QAGLD,MAAO,MACPC,IAAK,UAEVC,UACCN,KACJpY,KAAK2Y,kBAAkB5b,EAAQiD,KAAK6H,iBAAkBuQ,GAClDC,EAAgB1c,EAAEid,IAAI5Y,KAAKyJ,OAAQ,SAAU3N,GAC7C,MAAOqC,MAAK7B,IAAIga,MAAM,KAAM3a,EAAEid,IAAIR,EAAkB,SAAUrb,GAC1D,MAAOpB,GAAEkd,KAAK9b,EAAQ,SAAUoH,GAC5B,MAAOA,GAAMW,cAAgB3I,EAAcL,EAAMsB,EAAQ+G,EAAM9G,OAAQD,EAAQ+G,EAAMxG,QACtFT,YAGPyG,EAASxF,KAAK7B,IAAIga,MAAM,KAAM+B,GAClCrY,KAAKmX,2BAA2BxT,EAAS,GAAK3D,KAAKmS,qBAC1ChR,EAAa,EAAGA,EAAaiX,EAAiBlb,OAAQiE,IAC3DnB,KAAKuX,cAAca,EAAiBjX,GAAaA,EAErDnB,MAAKkS,gBACLlS,KAAKU,cAAa,GAClBV,KAAK4M,QAAQ,aAEjB+L,kBAAmB,SAAU5b,EAAQ6K,EAAWqJ,GAA7B,GAGP7G,GACK0O,EACD1c,EACA2c,EALRC,EAAWpR,EAAU,EACzB,IAAIoR,EAEA,IADI5O,EAAO4O,EAAShX,WAAWoI,OACtB0O,EAAU,EAAGA,EAAU1O,EAAKlN,OAAQ4b,IACrC1c,EAAQ4D,KAAKiZ,eAAeD,EAAU5O,EAAK0O,IAC3CC,EAA2B,GAAIrb,GAAMqY,KAAKuC,MAAMvb,GAAQ+Q,QACxD0K,MAAOQ,EAASR,MAChBU,SAAUza,EAAc0a,cAAc/c,KACvCsc,UACC9Q,EAAU1K,OAAS,EACnB8C,KAAK2Y,kBAAkBI,EAA0BnR,EAAUwR,MAAM,GAAInI,GAErEA,EAAOrT,KAAKmb,OAIpB9H,GAAOrT,KAAKb,IAGpBmL,yBAA0B,SAAUhD,GAChC,MAAOlF,MAAKgI,qBAAqB9C,GAASlF,KAAKgI,qBAAqB9C,EAAQ,IAEhF8C,qBAAsB,SAAUqR,GAC5B,GAAIC,GAActZ,KAAKgR,aAAaqI,EACpC,OAAOC,GAAcA,EAAYpc,OAAS,GAE9C+K,kBAAmB,SAAUoR,GACzB,GAAIE,GAAWvZ,KAAK+Q,UAAUsI,EAC9B,OAAOE,GAAWA,EAASrc,OAAS,GAExCsc,eAAgB,WACZxZ,KAAKwC,QAAQkD,IAAI1F,KAAKwS,aAAalQ,KAAK,qBAAqBmX,WAAW,MAAMhc,KAAK,iBAAiB,GAAOic,YAAY,qBAE3HC,iBAAkB,SAAUC,EAAWtY,EAAQuY,EAAUC,EAASC,GAAhD,GACVC,GAAYJ,EAAU/c,SACtBmI,EAAY1D,EAAO,GAAGjE,MACtB4c,EAAU3Y,EAAOA,EAAOpE,OAAS,GAAGS,GACpCkc,KACIE,EACKC,GAAahV,EAAUE,QAAU+U,EAAQ/U,OAASF,EAAUkV,kBAAoBD,EAAQC,kBACzFN,EAAUO,SAAWL,IAGrBE,GAAahV,EAAUE,QAAU+U,EAAQ/U,QAAU8U,GAAahV,EAAUkV,kBAAoBD,EAAQC,mBACtGN,EAAUO,SAAWL,KAKrCM,kBAAmB,SAAUR,EAAWE,EAASC,GAA9B,GAEPje,GACAuB,EACAM,EACA0c,EACA3d,EACA6E,EACAmS,EACAiB,EACA2F,EACAC,EAAcC,EASV7C,EACAC,CApBZ,KAAKmC,EAaD,MAZIje,GAAOge,EAAU9Z,KAAK4S,eAAiB5S,KAAK0S,WAC5CrV,EAAQuc,EAAUvc,MAClBM,EAAMic,EAAUjc,IAChB0c,EAAiBra,KAAKiF,oBAAsBjF,KAAKqH,uBACjD3K,EAAQsD,KAAK0C,OAAOkX,EAAUzY,YAC9BI,EAAauY,EAAUpd,EAAM+d,qBAAuB/d,EAAMge,gBAAgBhe,EAAMie,0BAChFjH,EAAQnS,EAAWA,EAAWrE,OAAS,GAAGiI,OAC1CwP,EAAamF,GAAYpd,EAAMie,yBAA+BjH,EAAMxW,OAAS,EAAnB,EAG9Dqd,EAAe,GAAIve,MAAKF,GACxB0e,EAAa,GAAIxe,MAAKF,IAClBkE,KAAK4a,WAAWL,EAAcC,KAGlCZ,EAAUvc,MAAQkd,EAClBX,EAAUjc,IAAM6c,EACZH,GACI1C,EAAW,GAAI3b,MAAK0X,EAAMiB,GAAWxX,aACrCya,EAAS,GAAI5b,MAAK0X,EAAMiB,GAAWpH,WACvC+M,EAAkBpe,EAAgB0b,GAAU1b,EAAgB0b,GAAU3Y,EACtEhD,EAAQ2d,EAAUvc,MAAOnB,EAAgByb,IACzC1b,EAAQ2d,EAAUjc,IAAK2c,GACnB5d,EAAMie,2BACNf,EAAU/c,UAAY+c,EAAU/c,YAGpCyd,EAAkBV,EAAU/c,WAAaX,EAAgByB,GAAOsB,EAAa/C,EAAgByB,GAC7F1B,EAAQ2d,EAAUvc,MAAOnB,EAAgBmB,IACzCpB,EAAQ2d,EAAUjc,IAAK2c,IAEtBta,KAAKqH,yBACNuS,EAAUzY,WAAa2Y,EAAU9Z,KAAK0C,OAAOxF,OAAS,EAAI,GAE9D0c,EAAU7c,WACH,KAInB+B,IAAO,EAAMR,GACTuB,aAAcA,EACdgb,QAAShb,EAAaf,QAClBhB,SACIsC,KAAM,UACND,MAAO,MACP0K,yBAA0B,aAE9BzK,KAAM,QAEV0a,SAAUjb,EAAaf,QACnBhB,SACIsC,KAAM,WACND,MAAO,OACPwK,mBAAoB,gBACpBC,wBAAyB,iBAE7BxK,KAAM,OACNI,mBAAoB,WAChB,GAAoH1D,GAAKI,EAArH6d,EAAe/a,KAAKlC,QAAQhC,KAAMuB,EAAQK,EAAM5B,KAAKkf,UAAUD,EAAc/a,KAAKib,eAAeC,aAA4BvM,IACjI,KAAK7R,EAAM,EAAGI,EAAS,EAAGJ,EAAMI,EAAQJ,IACpC6R,EAAM/Q,KAAKP,GACXA,EAAQK,EAAM5B,KAAK6W,QAAQtV,EAE/B2C,MAAK6R,QAAQlD,MAGrBwM,aAActb,EAAaf,QACvBhB,SACIsC,KAAM,eACND,MAAO,YACPwK,mBAAoB,gBACpBC,wBAAyB,iBAE7BxK,KAAM,WACNsS,SAAU,WACN,GAAI0I,GAAY1d,EAAM5B,KAAKkf,UAAUtd,EAAM5B,KAAK6W,QAAQ3S,KAAK7C,aAAc6C,KAAKib,eAAeC,SAAU,EACzG,OAAOxd,GAAM5B,KAAK0O,QAAQ4Q,EAAWpb,KAAKK,UAAU,KAExDuS,aAAc,WAAA,GACNwI,GAAY1d,EAAM5B,KAAKkf,UAAUhb,KAAK7C,YAAa6C,KAAKib,eAAeC,aACvEnd,EAAWiC,KAAKK,SACpB,OAAO3C,GAAM5B,KAAK0O,QAAQ4Q,EAAWrd,EAASA,EAASb,OAAS,GAAK,IAEzEsD,mBAAoB,WAEhB,IADA,GAAIua,GAAe/a,KAAKlC,QAAQhC,KAAMkf,EAAYtd,EAAM5B,KAAKkf,UAAWI,EAAYJ,EAAUD,EAAc/a,KAAKib,eAAeC,aAAe7d,EAAQ2d,EAAUI,EAAWpb,KAAKlC,QAAQG,cAAe,GAAIN,EAAMqd,EAAU3d,EAAO2C,KAAKlC,QAAQI,YAAa,GAAIyQ,KAC1PtR,GAASM,GACZgR,EAAM/Q,KAAKP,GACXA,EAAQK,EAAM5B,KAAK6W,QAAQtV,EAE/B2C,MAAK6R,QAAQlD,SAI3BtQ,OAAOX,MAAM2d,QACRhd,OAAOX,OACE,kBAAVhC,SAAwBA,OAAO4f,IAAM5f,OAAS,SAAU6f,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.scheduler.dayview.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.scheduler.dayview', ['kendo.scheduler.view'], f);\n}(function () {\n    var __meta__ = {\n        id: 'scheduler.dayview',\n        name: 'Scheduler Day View',\n        category: 'web',\n        description: 'The Scheduler Day View',\n        depends: ['scheduler.view'],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, browser = kendo.support.browser, setTime = kendo.date.setTime, SchedulerView = ui.SchedulerView, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, extend = $.extend, proxy = $.proxy, getDate = kendo.date.getDate, MS_PER_MINUTE = kendo.date.MS_PER_MINUTE, MS_PER_DAY = kendo.date.MS_PER_DAY, CURRENT_TIME_MARKER_CLASS = 'k-current-time', CURRENT_TIME_MARKER_ARROW_CLASS = 'k-current-time-arrow', INVERSE_COLOR_CLASS = 'k-event-inverse', BORDER_SIZE_COEFF = 0.8666, getMilliseconds = kendo.date.getMilliseconds, NS = '.kendoMultiDayView';\n        var DAY_VIEW_EVENT_TEMPLATE = kendo.template('<div title=\"(#=kendo.format(\"{0:t} - {1:t}\", start, end)#): #=title.replace(/\"/g,\"&\\\\#34;\")#\">' + '<div class=\"k-event-template k-event-time\">#:kendo.format(\"{0:t} - {1:t}\", start, end)#</div>' + '<div class=\"k-event-template\">${title}</div>' + '</div>'), DAY_VIEW_ALL_DAY_EVENT_TEMPLATE = kendo.template('<div title=\"(#=kendo.format(\"{0:t}\", start)#): #=title.replace(/\"/g,\"&\\\\#34;\")#\">' + '<div class=\"k-event-template\">${title}</div>' + '</div>'), DATA_HEADER_TEMPLATE = kendo.template('#var dateString = isMobile ? kendo.toString(date,\\'ddd\\')[0] : kendo.toString(date,\\'ddd M/dd\\'); #' + '<span class=\\'k-link k-nav-day\\'>#=dateString#</span>'), ALLDAY_EVENT_WRAPPER_STRING = '<div role=\"gridcell\" aria-selected=\"false\" ' + 'data-#=ns#uid=\"#=uid#\"' + '#if (resources[0]) { #' + 'style=\"background-color:#=resources[0].color#; border-color: #=resources[0].color#\"' + 'class=\"k-event\"' + '#} else {#' + 'class=\"k-event\"' + '#}#' + '>' + '<span class=\"k-event-actions\">' + '# if(data.tail || data.middle) {#' + '<span class=\"k-icon k-i-arrow-60-left\"></span>' + '#}#' + '# if(data.isException()) {#' + '<span class=\"k-icon k-i-non-recurrence\"></span>' + '# } else if(data.isRecurring()) {#' + '<span class=\"k-icon k-i-reload\"></span>' + '# } #' + '</span>' + '{0}' + '<span class=\"k-event-actions\">' + '#if (showDelete) {#' + '<a href=\"\\\\#\" class=\"k-link k-event-delete\" title=\"${data.messages.destroy}\" aria-label=\"${data.messages.destroy}\"><span class=\"k-icon k-i-close\"></span></a>' + '#}#' + '# if(data.head || data.middle) {#' + '<span class=\"k-icon k-i-arrow-60-right\"></span>' + '#}#' + '</span>' + '#if(resizable && !singleDay && !data.tail && !data.middle){#' + '<span class=\"k-resize-handle k-resize-w\"></span>' + '#}#' + '#if(resizable && !singleDay && !data.head && !data.middle){#' + '<span class=\"k-resize-handle k-resize-e\"></span>' + '#}#' + '</div>', EVENT_WRAPPER_STRING = '<div role=\"gridcell\" aria-selected=\"false\" ' + 'data-#=ns#uid=\"#=uid#\" ' + '#if (resources[0]) { #' + 'style=\"background-color:#=resources[0].color #; border-color: #=resources[0].color#\"' + 'class=\"k-event\"' + '#} else {#' + 'class=\"k-event\"' + '#}#' + '>' + '<span class=\"k-event-actions\">' + '# if(data.isException()) {#' + '<span class=\"k-icon k-i-non-recurrence\"></span>' + '# } else if(data.isRecurring()) {#' + '<span class=\"k-icon k-i-reload\"></span>' + '# } #' + '</span>' + '{0}' + '<span class=\"k-event-actions\">' + '#if (showDelete) {#' + '<a href=\"\\\\#\" class=\"k-link k-event-delete\" title=\"${data.messages.destroy}\" aria-label=\"${data.messages.destroy}\"><span class=\"k-icon k-i-close\"></span></a>' + '#}#' + '</span>' + '<span class=\"k-event-top-actions\">' + '# if(data.tail || data.middle) {#' + '<span class=\"k-icon k-i-arrow-60-up\"></span>' + '# } #' + '</span>' + '<span class=\"k-event-bottom-actions\">' + '# if(data.head || data.middle) {#' + '<span class=\"k-icon k-i-arrow-60-down\"></span>' + '# } #' + '</span>' + '# if(resizable && !data.tail && !data.middle) {#' + '<span class=\"k-resize-handle k-resize-n\"></span>' + '# } #' + '# if(resizable && !data.head && !data.middle) {#' + '<span class=\"k-resize-handle k-resize-s\"></span>' + '# } #' + '</div>';\n        function toInvariantTime(date) {\n            var staticDate = new Date(1980, 1, 1, 0, 0, 0);\n            setTime(staticDate, getMilliseconds(date));\n            return staticDate;\n        }\n        function isInDateRange(value, min, max) {\n            return value >= min && value <= max;\n        }\n        function isInTimeRange(value, min, max, overlaps) {\n            overlaps = overlaps ? value <= max : value < max;\n            return value > min && overlaps;\n        }\n        function addContinuousEvent(group, range, element, isAllDay) {\n            var events = group._continuousEvents;\n            var lastEvent = events[events.length - 1];\n            var startDate = getDate(range.start.startDate()).getTime();\n            if (isAllDay && lastEvent && getDate(lastEvent.start.startDate()).getTime() == startDate) {\n                var idx = events.length - 1;\n                for (; idx > -1; idx--) {\n                    if (events[idx].isAllDay || getDate(events[idx].start.startDate()).getTime() < startDate) {\n                        break;\n                    }\n                }\n                events.splice(idx + 1, 0, {\n                    element: element,\n                    isAllDay: true,\n                    uid: element.attr(kendo.attr('uid')),\n                    start: range.start,\n                    end: range.end\n                });\n            } else {\n                events.push({\n                    element: element,\n                    isAllDay: isAllDay,\n                    uid: element.attr(kendo.attr('uid')),\n                    start: range.start,\n                    end: range.end\n                });\n            }\n        }\n        function getWorkDays(options) {\n            var workDays = [];\n            var dayIndex = options.workWeekStart % 7;\n            var workWeekEnd = Math.abs(options.workWeekEnd % 7);\n            workDays.push(dayIndex);\n            while (workWeekEnd != dayIndex) {\n                if (dayIndex > 6) {\n                    dayIndex -= 7;\n                } else {\n                    dayIndex++;\n                }\n                workDays.push(dayIndex);\n            }\n            return workDays;\n        }\n        var MultiDayView = SchedulerView.extend({\n            init: function (element, options) {\n                var that = this;\n                SchedulerView.fn.init.call(that, element, options);\n                that.title = that.options.title || that.options.name;\n                that._workDays = getWorkDays(that.options);\n                that._templates();\n                that._editable();\n                that.calculateDateRange();\n                that._groups();\n                that._currentTime(true);\n            },\n            _currentTimeMarkerUpdater: function () {\n                this._updateCurrentTimeMarker(new Date());\n            },\n            _updateCurrentTimeMarker: function (currentTime) {\n                var options = this.options;\n                if (options.currentTimeMarker.useLocalTimezone === false) {\n                    var timezone = options.dataSource.options.schema.timezone;\n                    if (options.dataSource && timezone) {\n                        var timezoneOffset = kendo.timezone.offset(currentTime, timezone);\n                        currentTime = kendo.timezone.convert(currentTime, currentTime.getTimezoneOffset(), timezoneOffset);\n                    }\n                }\n                this.times.find('.' + CURRENT_TIME_MARKER_CLASS).remove();\n                this.content.find('.' + CURRENT_TIME_MARKER_CLASS).remove();\n                var groupsCount = !options.group || options.group.orientation == 'horizontal' ? 1 : this.groups.length;\n                var firstTimesCell = this.times.find('tr:first th:first');\n                var lastTimesCell = this.times.find('tr:first th:last');\n                for (var groupIndex = 0; groupIndex < groupsCount; groupIndex++) {\n                    var currentGroup = this.groups[groupIndex];\n                    if (!currentGroup) {\n                        return;\n                    }\n                    var utcCurrentTime = kendo.date.toUtcTime(currentTime);\n                    var ranges = currentGroup.timeSlotRanges(utcCurrentTime, utcCurrentTime + 1);\n                    if (ranges.length === 0) {\n                        return;\n                    }\n                    var collection = ranges[0].collection;\n                    var slotElement = collection.slotByStartDate(currentTime);\n                    if (slotElement) {\n                        var elementHtml = '<div class=\\'' + CURRENT_TIME_MARKER_CLASS + '\\'></div>';\n                        var timesTableMarker = $(elementHtml).prependTo(this.times);\n                        var markerTopPosition = Math.round(ranges[0].innerRect(currentTime, new Date(currentTime.getTime() + 1), false).top);\n                        var timesTableMarkerCss = {};\n                        var markerWidth = this.content[0].scrollWidth;\n                        if (browser.msie || browser.edge) {\n                            markerWidth -= 1;\n                        }\n                        if (this._isRtl) {\n                            timesTableMarkerCss.right = firstTimesCell.position().left + outerHeight(firstTimesCell) - outerHeight(lastTimesCell);\n                            timesTableMarker.addClass(CURRENT_TIME_MARKER_ARROW_CLASS + '-left');\n                        } else {\n                            timesTableMarkerCss.left = lastTimesCell.position().left;\n                            timesTableMarker.addClass(CURRENT_TIME_MARKER_ARROW_CLASS + '-right');\n                        }\n                        timesTableMarkerCss.top = markerTopPosition - outerWidth(timesTableMarker) * BORDER_SIZE_COEFF / 2;\n                        timesTableMarker.css(timesTableMarkerCss);\n                        $(elementHtml).prependTo(this.content).css({\n                            top: markerTopPosition,\n                            height: '1px',\n                            right: 0,\n                            width: markerWidth,\n                            left: 0\n                        });\n                    }\n                }\n            },\n            _currentTime: function (setUpdateTimer) {\n                var that = this;\n                var markerOptions = that.options.currentTimeMarker;\n                if (markerOptions !== false && markerOptions.updateInterval !== undefined) {\n                    that._currentTimeMarkerUpdater();\n                    if (setUpdateTimer) {\n                        that._currentTimeUpdateTimer = setInterval(proxy(this._currentTimeMarkerUpdater, that), markerOptions.updateInterval);\n                    }\n                }\n            },\n            _updateResizeHint: function (event, groupIndex, startTime, endTime) {\n                var multiday = event.isMultiDay();\n                var group = this.groups[groupIndex];\n                var ranges = group.ranges(startTime, endTime, multiday, event.isAllDay);\n                var width, height, top, hint;\n                this._removeResizeHint();\n                for (var rangeIndex = 0; rangeIndex < ranges.length; rangeIndex++) {\n                    var range = ranges[rangeIndex];\n                    var start = range.startSlot();\n                    if (this._isGroupedByDate() && multiday) {\n                        for (var slotIdx = start.index; slotIdx <= range.end.index; slotIdx++) {\n                            var slot = range.collection._slots[slotIdx];\n                            width = slot.offsetWidth;\n                            height = slot.clientHeight;\n                            top = slot.offsetTop;\n                            hint = SchedulerView.fn._createResizeHint.call(this, slot.offsetLeft, top, width, height);\n                            this._resizeHint = this._resizeHint.add(hint);\n                        }\n                    } else {\n                        width = start.offsetWidth;\n                        height = start.clientHeight;\n                        top = start.offsetTop;\n                        if (multiday) {\n                            width = range.innerWidth();\n                        } else {\n                            var rect = range.outerRect(startTime, endTime, this.options.snap);\n                            top = rect.top;\n                            height = rect.bottom - rect.top;\n                        }\n                        hint = SchedulerView.fn._createResizeHint.call(this, start.offsetLeft, top, width, height);\n                        this._resizeHint = this._resizeHint.add(hint);\n                    }\n                }\n                var format = 't';\n                var container = this.content;\n                if (multiday) {\n                    format = 'M/dd';\n                    container = this.element.find('.k-scheduler-header-wrap:has(.k-scheduler-header-all-day) > div');\n                    if (!container.length) {\n                        container = this.content;\n                    }\n                }\n                this._resizeHint.appendTo(container);\n                this._resizeHint.find('.k-label-top,.k-label-bottom').text('');\n                this._resizeHint.first().addClass('k-first').find('.k-label-top').text(kendo.toString(kendo.timezone.toLocalDate(startTime), format));\n                this._resizeHint.last().addClass('k-last').find('.k-label-bottom').text(kendo.toString(kendo.timezone.toLocalDate(endTime), format));\n            },\n            _updateMoveHint: function (event, groupIndex, distance) {\n                var multiday = event.isMultiDay();\n                var group = this.groups[groupIndex];\n                var start = kendo.date.toUtcTime(event.start) + distance;\n                var end = start + event.duration();\n                var ranges = group.ranges(start, end, multiday, event.isAllDay);\n                start = kendo.timezone.toLocalDate(start);\n                end = kendo.timezone.toLocalDate(end);\n                this._removeMoveHint(event.uid);\n                if (!multiday && (getMilliseconds(end) === 0 || getMilliseconds(end) < getMilliseconds(this.startTime()))) {\n                    if (ranges.length > 1) {\n                        ranges.pop();\n                    }\n                }\n                var eventHint = $();\n                for (var rangeIndex = 0; rangeIndex < ranges.length; rangeIndex++) {\n                    var range = ranges[rangeIndex];\n                    var startSlot = range.start;\n                    var hint;\n                    var css = {\n                        left: startSlot.offsetLeft + 2,\n                        top: startSlot.offsetTop\n                    };\n                    if (this._isGroupedByDate() && multiday) {\n                        for (var slotIdx = startSlot.index; slotIdx <= range.end.index; slotIdx++) {\n                            var slot = range.collection._slots[slotIdx];\n                            css.left = this._isRtl ? slot.clientWidth * 0.1 + slot.offsetLeft + 2 : slot.offsetLeft + 2;\n                            css.height = slot.offsetHeight;\n                            css.width = slot.clientWidth * 0.9 - 4;\n                            hint = this._createEventElement(event.clone({\n                                start: start,\n                                end: end\n                            }), !multiday);\n                            if (event.inverseColor) {\n                                hint.addClass(INVERSE_COLOR_CLASS);\n                            }\n                            this._appendMoveHint(hint, css);\n                            eventHint = eventHint.add(hint);\n                        }\n                    } else {\n                        if (this._isRtl) {\n                            css.left = startSlot.clientWidth * 0.1 + startSlot.offsetLeft + 2;\n                        }\n                        if (multiday) {\n                            css.width = range.innerWidth() - 4;\n                        } else {\n                            var rect = range.outerRect(start, end, this.options.snap);\n                            css.top = rect.top;\n                            css.height = rect.bottom - rect.top;\n                            css.width = startSlot.clientWidth * 0.9 - 4;\n                        }\n                        hint = this._createEventElement(event.clone({\n                            start: start,\n                            end: end\n                        }), !multiday);\n                        if (event.inverseColor) {\n                            hint.addClass(INVERSE_COLOR_CLASS);\n                        }\n                        this._appendMoveHint(hint, css);\n                        eventHint = eventHint.add(hint);\n                    }\n                }\n                var content = this.content;\n                if (multiday) {\n                    content = this.element.find('.k-scheduler-header-wrap:has(.k-scheduler-header-all-day) > div');\n                    if (!content.length) {\n                        content = this.content;\n                    }\n                }\n                eventHint.appendTo(content);\n            },\n            _appendMoveHint: function (hint, css) {\n                hint.addClass('k-event-drag-hint');\n                hint.css(css);\n                this._moveHint = this._moveHint.add(hint);\n            },\n            _slotByPosition: function (x, y) {\n                var slot, offset;\n                if (this._isVerticallyGrouped()) {\n                    offset = this.content.offset();\n                    y += this.content[0].scrollTop;\n                    x += this.content[0].scrollLeft;\n                } else {\n                    offset = this.element.find('.k-scheduler-header-wrap:has(.k-scheduler-header-all-day)').find('>div').offset();\n                }\n                if (offset) {\n                    x -= offset.left;\n                    y -= offset.top;\n                }\n                x = Math.ceil(x);\n                y = Math.ceil(y);\n                var group;\n                var groupIndex;\n                for (groupIndex = 0; groupIndex < this.groups.length; groupIndex++) {\n                    group = this.groups[groupIndex];\n                    slot = group.daySlotByPosition(x, y, this._isGroupedByDate());\n                    if (slot) {\n                        return slot;\n                    }\n                }\n                if (offset) {\n                    x += offset.left;\n                    y += offset.top;\n                }\n                offset = this.content.offset();\n                x -= offset.left;\n                y -= offset.top;\n                if (!this._isVerticallyGrouped()) {\n                    y += this.content[0].scrollTop;\n                    x += this.content[0].scrollLeft;\n                }\n                x = Math.ceil(x);\n                y = Math.ceil(y);\n                for (groupIndex = 0; groupIndex < this.groups.length; groupIndex++) {\n                    group = this.groups[groupIndex];\n                    slot = group.timeSlotByPosition(x, y);\n                    if (slot) {\n                        return slot;\n                    }\n                }\n                return null;\n            },\n            _groupCount: function () {\n                var resources = this.groupedResources;\n                var byDate = this._isGroupedByDate();\n                if (resources.length) {\n                    if (this._groupOrientation() === 'vertical') {\n                        if (byDate) {\n                            return this._columnCountForLevel(resources.length - 1);\n                        } else {\n                            return this._rowCountForLevel(resources.length - 1);\n                        }\n                    } else {\n                        if (byDate) {\n                            return this._columnCountForLevel(resources.length) / this._columnCountForLevel(0);\n                        } else {\n                            return this._columnCountForLevel(resources.length) / this._columnOffsetForResource(resources.length);\n                        }\n                    }\n                }\n                return 1;\n            },\n            _columnCountInResourceView: function () {\n                var resources = this.groupedResources;\n                var byDate = this._isGroupedByDate();\n                if (!resources.length || this._isVerticallyGrouped()) {\n                    if (byDate) {\n                        return this._rowCountForLevel(0);\n                    } else {\n                        return this._columnCountForLevel(0);\n                    }\n                }\n                if (byDate) {\n                    return this._columnCountForLevel(0);\n                } else {\n                    return this._columnOffsetForResource(resources.length);\n                }\n            },\n            _timeSlotGroups: function (groupCount, columnCount) {\n                var interval = this._timeSlotInterval();\n                var verticalViews = groupCount;\n                var byDate = this._isGroupedByDate();\n                var tableRows = this.content.find('tr:not(.k-scheduler-header-all-day)');\n                var group, time, rowIndex, cellIndex;\n                tableRows.attr('role', 'row');\n                var rowCount = tableRows.length;\n                if (this._isVerticallyGrouped()) {\n                    if (byDate) {\n                        verticalViews = columnCount;\n                    }\n                    rowCount = Math.floor(rowCount / verticalViews);\n                }\n                for (var groupIndex = 0; groupIndex < verticalViews; groupIndex++) {\n                    var rowMultiplier = 0;\n                    var cellMultiplier = 0;\n                    if (this._isVerticallyGrouped()) {\n                        rowMultiplier = groupIndex;\n                    } else {\n                        cellMultiplier = groupIndex;\n                    }\n                    rowIndex = rowMultiplier * rowCount;\n                    while (rowIndex < (rowMultiplier + 1) * rowCount) {\n                        var cells = tableRows[rowIndex].children;\n                        if (rowIndex % rowCount === 0) {\n                            time = getMilliseconds(new Date(+this.startTime()));\n                        }\n                        var timeIndex = 0;\n                        if (byDate) {\n                            if (this._isVerticallyGrouped()) {\n                                for (cellIndex = 0; cellIndex < groupCount; cellIndex++) {\n                                    group = this.groups[cellIndex];\n                                    this._addTimeSlotGroup(group, cells, cellIndex, time, interval, groupIndex);\n                                }\n                            } else {\n                                group = this.groups[groupIndex];\n                                for (cellIndex = cellMultiplier; cellIndex < groupCount * columnCount; cellIndex = cellIndex + groupCount) {\n                                    this._addTimeSlotGroup(group, cells, cellIndex, time, interval, timeIndex);\n                                    timeIndex++;\n                                }\n                            }\n                        } else {\n                            group = this.groups[groupIndex];\n                            for (cellIndex = cellMultiplier * columnCount; cellIndex < (cellMultiplier + 1) * columnCount; cellIndex++) {\n                                this._addTimeSlotGroup(group, cells, cellIndex, time, interval, timeIndex);\n                                timeIndex++;\n                            }\n                        }\n                        time += interval;\n                        rowIndex++;\n                    }\n                }\n            },\n            _addTimeSlotGroup: function (group, cells, cellIndex, time, interval, timeIndex) {\n                var cell = cells[cellIndex];\n                var collection = group.getTimeSlotCollection(timeIndex);\n                var currentDate = this._dates[timeIndex];\n                if (!currentDate) {\n                    return;\n                }\n                var currentTime = Date.UTC(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());\n                var start = currentTime + time;\n                var end = start + interval;\n                cell.setAttribute('role', 'gridcell');\n                cell.setAttribute('aria-selected', false);\n                collection.addTimeSlot(cell, start, end);\n            },\n            _addDaySlotGroup: function (collection, cells, cellIndex, columnCount, cellCount) {\n                var cell = cells[cellIndex];\n                var start = this._dates[cellCount];\n                if (!start) {\n                    return;\n                }\n                var currentTime = Date.UTC(start.getFullYear(), start.getMonth(), start.getDate());\n                cell.setAttribute('role', 'gridcell');\n                cell.setAttribute('aria-selected', false);\n                collection.addDaySlot(cell, currentTime, currentTime + kendo.date.MS_PER_DAY);\n            },\n            _daySlotGroups: function (groupCount, columnCount) {\n                var tableRows, cellIndex;\n                var verticalViews = groupCount;\n                var byDate = this._isGroupedByDate();\n                if (this._isVerticallyGrouped()) {\n                    if (byDate) {\n                        verticalViews = columnCount;\n                    }\n                    tableRows = this.element.find('.k-scheduler-header-all-day');\n                } else {\n                    tableRows = this.element.find('.k-scheduler-header-all-day tr');\n                }\n                tableRows.attr('role', 'row');\n                for (var groupIndex = 0; groupIndex < verticalViews; groupIndex++) {\n                    var rowMultiplier = 0;\n                    var group, collection;\n                    if (this._isVerticallyGrouped()) {\n                        rowMultiplier = groupIndex;\n                    }\n                    var cells = tableRows[rowMultiplier].children;\n                    var cellMultiplier = 0;\n                    if (!this._isVerticallyGrouped()) {\n                        cellMultiplier = groupIndex;\n                    }\n                    var cellCount = 0;\n                    if (byDate) {\n                        if (this._isVerticallyGrouped()) {\n                            for (cellIndex = 0; cellIndex < groupCount; cellIndex++) {\n                                group = this.groups[cellIndex];\n                                collection = group.getDaySlotCollection(0);\n                                this._addDaySlotGroup(collection, cells, cellIndex, columnCount, groupIndex);\n                            }\n                        } else {\n                            group = this.groups[groupIndex];\n                            collection = group.getDaySlotCollection(0);\n                            for (cellIndex = cellMultiplier; cellIndex < groupCount * columnCount; cellIndex = cellIndex + groupCount) {\n                                this._addDaySlotGroup(collection, cells, cellIndex, columnCount, cellCount);\n                                cellCount++;\n                            }\n                        }\n                    } else {\n                        group = this.groups[groupIndex];\n                        collection = group.getDaySlotCollection(0);\n                        for (cellIndex = cellMultiplier * columnCount; cellIndex < (cellMultiplier + 1) * columnCount; cellIndex++) {\n                            this._addDaySlotGroup(collection, cells, cellIndex, columnCount, cellCount);\n                            cellCount++;\n                        }\n                    }\n                }\n            },\n            _groups: function () {\n                var groupCount = this._groupCount();\n                var columnCount = this._columnCountInResourceView();\n                this.groups = [];\n                for (var idx = 0; idx < groupCount; idx++) {\n                    var view = this._addResourceView(idx);\n                    for (var columnIndex = 0; columnIndex < columnCount; columnIndex++) {\n                        if (this._dates[columnIndex]) {\n                            view.addTimeSlotCollection(this._dates[columnIndex], kendo.date.addDays(this._dates[columnIndex], 1));\n                        }\n                    }\n                    if (this.options.allDaySlot) {\n                        view.addDaySlotCollection(this._dates[0], kendo.date.addDays(this._dates[this._dates.length - 1], 1));\n                    }\n                }\n                this._timeSlotGroups(groupCount, columnCount);\n                if (this.options.allDaySlot) {\n                    this._daySlotGroups(groupCount, columnCount);\n                }\n            },\n            options: {\n                name: 'MultiDayView',\n                selectedDateFormat: '{0:D}',\n                selectedShortDateFormat: '{0:d}',\n                selectedMobileDateFormat: '{0:MMM} {0:dd} - {1:dd}',\n                allDaySlot: true,\n                showWorkHours: false,\n                title: '',\n                startTime: kendo.date.today(),\n                endTime: kendo.date.today(),\n                minorTickCount: 2,\n                majorTick: 60,\n                majorTimeHeaderTemplate: '<span class=\\'k-time-text\\'>#=kendo.toString(date, \\'h:mm\\')#</span> ' + '<span class=\\'k-time-period\\'>#=kendo.toString(date, \\'tt\\')#</span>',\n                minorTimeHeaderTemplate: '&\\\\#8203;',\n                groupHeaderTemplate: '#=text#',\n                slotTemplate: '&nbsp;',\n                allDaySlotTemplate: '&nbsp;',\n                eventTemplate: DAY_VIEW_EVENT_TEMPLATE,\n                allDayEventTemplate: DAY_VIEW_ALL_DAY_EVENT_TEMPLATE,\n                dateHeaderTemplate: DATA_HEADER_TEMPLATE,\n                editable: true,\n                workDayStart: new Date(1980, 1, 1, 8, 0, 0),\n                workDayEnd: new Date(1980, 1, 1, 17, 0, 0),\n                workWeekStart: 1,\n                workWeekEnd: 5,\n                footer: { command: 'workDay' },\n                messages: {\n                    allDay: 'all day',\n                    showFullDay: 'Show full day',\n                    showWorkDay: 'Show business hours'\n                },\n                currentTimeMarker: {\n                    updateInterval: 10000,\n                    useLocalTimezone: true\n                }\n            },\n            events: [\n                'remove',\n                'add',\n                'edit'\n            ],\n            _templates: function () {\n                var options = this.options, settings = extend({}, kendo.Template, options.templateSettings);\n                this.eventTemplate = this._eventTmpl(options.eventTemplate, EVENT_WRAPPER_STRING);\n                this.allDayEventTemplate = this._eventTmpl(options.allDayEventTemplate, ALLDAY_EVENT_WRAPPER_STRING);\n                this.majorTimeHeaderTemplate = kendo.template(options.majorTimeHeaderTemplate, settings);\n                this.minorTimeHeaderTemplate = kendo.template(options.minorTimeHeaderTemplate, settings);\n                this.dateHeaderTemplate = kendo.template(options.dateHeaderTemplate, settings);\n                this.slotTemplate = kendo.template(options.slotTemplate, settings);\n                this.allDaySlotTemplate = kendo.template(options.allDaySlotTemplate, settings);\n                this.groupHeaderTemplate = kendo.template(options.groupHeaderTemplate, settings);\n            },\n            _editable: function () {\n                if (this.options.editable) {\n                    if (this._isMobile()) {\n                        this._touchEditable();\n                    } else {\n                        this._mouseEditable();\n                    }\n                }\n            },\n            _mouseEditable: function () {\n                var that = this;\n                that.element.on('click' + NS, '.k-event a:has(.k-i-close)', function (e) {\n                    that.trigger('remove', { uid: $(this).closest('.k-event').attr(kendo.attr('uid')) });\n                    e.preventDefault();\n                });\n                if (that.options.editable.create !== false) {\n                    that.element.on('dblclick' + NS, '.k-scheduler-content td', function (e) {\n                        if (!$(this).parent().hasClass('k-scheduler-header-all-day')) {\n                            var slot = that._slotByPosition(e.pageX, e.pageY);\n                            if (slot) {\n                                var resourceInfo = that._resourceBySlot(slot);\n                                that.trigger('add', {\n                                    eventInfo: extend({\n                                        start: slot.startDate(),\n                                        end: slot.endDate()\n                                    }, resourceInfo)\n                                });\n                            }\n                            e.preventDefault();\n                        }\n                    }).on('dblclick' + NS, '.k-scheduler-header-all-day td', function (e) {\n                        var slot = that._slotByPosition(e.pageX, e.pageY);\n                        if (slot) {\n                            var resourceInfo = that._resourceBySlot(slot);\n                            that.trigger('add', {\n                                eventInfo: extend({}, {\n                                    isAllDay: true,\n                                    start: kendo.date.getDate(slot.startDate()),\n                                    end: kendo.date.getDate(slot.startDate())\n                                }, resourceInfo)\n                            });\n                        }\n                        e.preventDefault();\n                    });\n                }\n                if (that.options.editable.update !== false) {\n                    that.element.on('dblclick' + NS, '.k-event', function (e) {\n                        that.trigger('edit', { uid: $(this).closest('.k-event').attr(kendo.attr('uid')) });\n                        e.preventDefault();\n                    });\n                }\n            },\n            _touchEditable: function () {\n                var that = this;\n                var threshold = 0;\n                if (kendo.support.mobileOS.android) {\n                    threshold = 5;\n                }\n                if (that.options.editable.create !== false) {\n                    that._addUserEvents = new kendo.UserEvents(that.element, {\n                        threshold: threshold,\n                        filter: '.k-scheduler-content td',\n                        useClickAsTap: !kendo.support.browser.edge,\n                        tap: function (e) {\n                            if (that._scrolling) {\n                                return;\n                            }\n                            if (!$(e.target).parent().hasClass('k-scheduler-header-all-day')) {\n                                var x = e.x.location !== undefined ? e.x.location : e.x;\n                                var y = e.y.location !== undefined ? e.y.location : e.y;\n                                var slot = that._slotByPosition(x, y);\n                                if (slot) {\n                                    var resourceInfo = that._resourceBySlot(slot);\n                                    that.trigger('add', {\n                                        eventInfo: extend({\n                                            start: slot.startDate(),\n                                            end: slot.endDate()\n                                        }, resourceInfo)\n                                    });\n                                }\n                                e.preventDefault();\n                            }\n                        }\n                    });\n                    that._allDayUserEvents = new kendo.UserEvents(that.element, {\n                        threshold: threshold,\n                        useClickAsTap: !kendo.support.browser.edge,\n                        filter: '.k-scheduler-header-all-day td',\n                        tap: function (e) {\n                            if (that._scrolling) {\n                                return;\n                            }\n                            var x = e.x.location !== undefined ? e.x.location : e.x;\n                            var y = e.y.location !== undefined ? e.y.location : e.y;\n                            var slot = that._slotByPosition(x, y);\n                            if (slot) {\n                                var resourceInfo = that._resourceBySlot(slot);\n                                that.trigger('add', {\n                                    eventInfo: extend({}, {\n                                        isAllDay: true,\n                                        start: kendo.date.getDate(slot.startDate()),\n                                        end: kendo.date.getDate(slot.startDate())\n                                    }, resourceInfo)\n                                });\n                            }\n                            e.preventDefault();\n                        }\n                    });\n                }\n                if (that.options.editable.update !== false) {\n                    that._editUserEvents = new kendo.UserEvents(that.element, {\n                        threshold: threshold,\n                        useClickAsTap: !kendo.support.browser.edge,\n                        filter: '.k-event',\n                        tap: function (e) {\n                            if (that._scrolling) {\n                                return;\n                            }\n                            var eventElement = $(e.target).closest('.k-event');\n                            var touchElement = $(e.touch.initialTouch);\n                            if (touchElement.hasClass('k-i-close')) {\n                                that.trigger('remove', { uid: eventElement.attr(kendo.attr('uid')) });\n                            } else if (!eventElement.hasClass('k-event-active')) {\n                                that.trigger('edit', { uid: eventElement.attr(kendo.attr('uid')) });\n                            }\n                            e.preventDefault();\n                        }\n                    });\n                }\n            },\n            _layout: function (dates) {\n                var columns = [];\n                var rows = [];\n                var options = this.options;\n                var that = this;\n                var byDate = that._isGroupedByDate();\n                for (var idx = 0; idx < dates.length; idx++) {\n                    var column = {};\n                    column.text = that.dateHeaderTemplate({\n                        date: dates[idx],\n                        isMobile: that._isMobile()\n                    });\n                    if (kendo.date.isToday(dates[idx])) {\n                        column.className = 'k-today';\n                    }\n                    columns.push(column);\n                }\n                var resources = this.groupedResources;\n                if (options.allDaySlot) {\n                    rows.push({\n                        text: options.messages.allDay,\n                        allDay: true,\n                        cellContent: function (idx) {\n                            var groupIndex = idx;\n                            idx = resources.length && that._groupOrientation() !== 'vertical' ? idx % dates.length : idx;\n                            return that.allDaySlotTemplate({\n                                date: dates[idx],\n                                resources: function () {\n                                    return that._resourceBySlot({ groupIndex: groupIndex });\n                                }\n                            });\n                        }\n                    });\n                }\n                this._forTimeRange(this.startTime(), this.endTime(), function (date, majorTick, middleRow, lastSlotRow) {\n                    var template = majorTick ? that.majorTimeHeaderTemplate : that.minorTimeHeaderTemplate;\n                    var row = {\n                        text: template({ date: date }),\n                        className: lastSlotRow ? 'k-slot-cell' : ''\n                    };\n                    rows.push(row);\n                });\n                if (resources.length) {\n                    if (this._groupOrientation() === 'vertical') {\n                        if (byDate) {\n                            rows = this._createDateLayout(columns, rows);\n                            columns = this._createColumnsLayout(resources, null, this.groupHeaderTemplate);\n                        } else {\n                            rows = this._createRowsLayout(resources, rows, this.groupHeaderTemplate);\n                        }\n                    } else {\n                        if (byDate) {\n                            columns = this._createColumnsLayout(resources, columns, this.groupHeaderTemplate, columns);\n                        } else {\n                            columns = this._createColumnsLayout(resources, columns, this.groupHeaderTemplate);\n                        }\n                    }\n                }\n                return {\n                    columns: columns,\n                    rows: rows\n                };\n            },\n            _footer: function () {\n                var options = this.options;\n                if (options.footer !== false) {\n                    var html = '<div class=\"k-header k-scheduler-footer\">';\n                    var command = options.footer.command;\n                    if (this._isMobile()) {\n                        html += '<span class=\"k-state-default k-scheduler-today\"><a href=\"#\" class=\"k-link\">';\n                        html += options.messages.today + '</a></span>';\n                    }\n                    if (command && command === 'workDay') {\n                        if (this._isMobile()) {\n                            html += '<span class=\"k-state-default k-scheduler-fullday\"><a href=\"#\" class=\"k-link\">';\n                            html += (options.showWorkHours ? options.messages.showFullDay : options.messages.showWorkDay) + '</a></span>';\n                        } else {\n                            html += '<ul class=\"k-reset k-header\">';\n                            html += '<li class=\"k-state-default k-scheduler-fullday\"><a href=\"#\" class=\"k-link\"><span class=\"k-icon k-i-clock\"></span>';\n                            html += (options.showWorkHours ? options.messages.showFullDay : options.messages.showWorkDay) + '</a></li>';\n                            html += '</ul>';\n                        }\n                    } else {\n                        html += '&nbsp;';\n                    }\n                    html += '</div>';\n                    this.footer = $(html).appendTo(this.element);\n                    var that = this;\n                    this.footer.on('click' + NS, '.k-scheduler-fullday', function (e) {\n                        e.preventDefault();\n                        that.trigger('navigate', {\n                            view: that.name || options.name,\n                            date: options.date,\n                            isWorkDay: !options.showWorkHours\n                        });\n                    });\n                    this.footer.on('click' + NS, '.k-scheduler-today', function (e) {\n                        e.preventDefault();\n                        var timezone = that.options.timezone;\n                        var action = 'today';\n                        var currentDate = new Date();\n                        var date;\n                        if (timezone) {\n                            var timezoneOffset = kendo.timezone.offset(currentDate, timezone);\n                            date = kendo.timezone.convert(currentDate, currentDate.getTimezoneOffset(), timezoneOffset);\n                        } else {\n                            date = currentDate;\n                        }\n                        that.trigger('navigate', {\n                            view: that.name || options.name,\n                            action: action,\n                            date: date\n                        });\n                    });\n                }\n            },\n            _forTimeRange: function (min, max, action, after) {\n                min = toInvariantTime(min);\n                max = toInvariantTime(max);\n                var that = this, msMin = getMilliseconds(min), msMax = getMilliseconds(max), minorTickCount = that.options.minorTickCount, msMajorInterval = that.options.majorTick * MS_PER_MINUTE, msInterval = msMajorInterval / minorTickCount || 1, start = new Date(+min), startDay = start.getDate(), msStart, idx = 0, length, html = '';\n                length = MS_PER_DAY / msInterval;\n                if (msMin != msMax) {\n                    if (msMin > msMax) {\n                        msMax += MS_PER_DAY;\n                    }\n                    length = (msMax - msMin) / msInterval;\n                }\n                length = Math.round(length);\n                for (; idx < length; idx++) {\n                    var majorTickDivider = idx % (msMajorInterval / msInterval), isMajorTickRow = majorTickDivider === 0, isMiddleRow = majorTickDivider < minorTickCount - 1, isLastSlotRow = majorTickDivider === minorTickCount - 1;\n                    html += action(start, isMajorTickRow, isMiddleRow, isLastSlotRow);\n                    setTime(start, msInterval, false);\n                }\n                if (msMax) {\n                    msStart = getMilliseconds(start);\n                    if (startDay < start.getDate()) {\n                        msStart += MS_PER_DAY;\n                    }\n                    if (msStart > msMax) {\n                        start = new Date(+max);\n                    }\n                }\n                if (after) {\n                    html += after(start);\n                }\n                return html;\n            },\n            _content: function (dates) {\n                var that = this;\n                var options = that.options;\n                var start = that.startTime();\n                var end = this.endTime();\n                var groupsCount = 1;\n                var rowCount = 1;\n                var columnCount = dates.length;\n                var html = '';\n                var resources = this.groupedResources;\n                var allDaySlotTemplate = this.allDaySlotTemplate;\n                var isVerticalGroupped = false;\n                var allDayVerticalGroupRow;\n                var byDate = that._isGroupedByDate();\n                var dateID = 0;\n                if (resources.length) {\n                    isVerticalGroupped = that._groupOrientation() === 'vertical';\n                    if (isVerticalGroupped) {\n                        rowCount = this._rowCountForLevel(this.rowLevels.length - 2);\n                        if (byDate) {\n                            groupsCount = this._columnCountForLevel(this.columnLevels.length - 1);\n                        }\n                        if (options.allDaySlot) {\n                            allDayVerticalGroupRow = function (groupIndex) {\n                                var result = '<tr class=\"k-scheduler-header-all-day\">';\n                                var dateGroupIndex = byDate ? 0 : groupIndex;\n                                var resources = function () {\n                                    return that._resourceBySlot({ groupIndex: dateGroupIndex });\n                                };\n                                if (byDate) {\n                                    for (; dateGroupIndex < groupsCount; dateGroupIndex++) {\n                                        result += '<td>' + allDaySlotTemplate({\n                                            date: dates[dateID],\n                                            resources: resources\n                                        }) + '</td>';\n                                    }\n                                } else {\n                                    for (var idx = 0; idx < dates.length; idx++) {\n                                        result += '<td>' + allDaySlotTemplate({\n                                            date: dates[idx],\n                                            resources: resources\n                                        }) + '</td>';\n                                    }\n                                }\n                                return result + '</tr>';\n                            };\n                        }\n                    } else {\n                        if (byDate) {\n                            groupsCount = this._columnCountForLevel(this.columnLevels.length - 1) / this._columnCountForLevel(0);\n                        } else {\n                            groupsCount = this._columnCountForLevel(this.columnLevels.length - 2);\n                        }\n                    }\n                }\n                html += '<tbody>';\n                var appendRow = function (date, majorTick, middleRow) {\n                    var content = '';\n                    var groupIdx = 0;\n                    var idx, length;\n                    content = '<tr' + (middleRow ? ' class=\"k-middle-row\"' : '') + '>';\n                    if (byDate) {\n                        for (idx = 0, length = columnCount; idx < length; idx++) {\n                            for (groupIdx = 0; groupIdx < groupsCount; groupIdx++) {\n                                var dateIndex = idx;\n                                if (isVerticalGroupped) {\n                                    dateIndex = dateID;\n                                }\n                                content = that._addCellsToContent(content, dates, date, dateIndex, groupIdx, rowIdx);\n                            }\n                            if (isVerticalGroupped) {\n                                break;\n                            }\n                        }\n                    } else {\n                        for (; groupIdx < groupsCount; groupIdx++) {\n                            for (idx = 0, length = columnCount; idx < length; idx++) {\n                                content = that._addCellsToContent(content, dates, date, idx, groupIdx, rowIdx);\n                            }\n                        }\n                    }\n                    content += '</tr>';\n                    return content;\n                };\n                for (var rowIdx = 0; rowIdx < rowCount; rowIdx++) {\n                    html += allDayVerticalGroupRow ? allDayVerticalGroupRow(rowIdx) : '';\n                    html += this._forTimeRange(start, end, appendRow);\n                    if (isVerticalGroupped) {\n                        dateID++;\n                    }\n                }\n                html += '</tbody>';\n                this.content.find('table').append(html);\n            },\n            _addCellsToContent: function (content, dates, date, idx, groupIdx, rowIdx) {\n                var that = this;\n                var classes = '';\n                var tmplDate;\n                var slotTemplate = this.slotTemplate;\n                var isVerticalGroupped = this._groupOrientation() === 'vertical';\n                var resources = function (groupIndex) {\n                    return function () {\n                        return that._resourceBySlot({ groupIndex: groupIndex });\n                    };\n                };\n                if (kendo.date.isToday(dates[idx])) {\n                    classes += 'k-today';\n                }\n                if (kendo.date.getMilliseconds(date) < kendo.date.getMilliseconds(this.options.workDayStart) || kendo.date.getMilliseconds(date) >= kendo.date.getMilliseconds(this.options.workDayEnd) || !this._isWorkDay(dates[idx])) {\n                    classes += ' k-nonwork-hour';\n                }\n                content += '<td' + (classes !== '' ? ' class=\"' + classes + '\"' : '') + '>';\n                tmplDate = kendo.date.getDate(dates[idx]);\n                kendo.date.setTime(tmplDate, kendo.date.getMilliseconds(date));\n                content += slotTemplate({\n                    date: tmplDate,\n                    resources: resources(isVerticalGroupped && !that._isGroupedByDate() ? rowIdx : groupIdx)\n                });\n                content += '</td>';\n                return content;\n            },\n            _isWorkDay: function (date) {\n                var day = date.getDay();\n                var workDays = this._workDays;\n                for (var i = 0; i < workDays.length; i++) {\n                    if (workDays[i] === day) {\n                        return true;\n                    }\n                }\n                return false;\n            },\n            _render: function (dates) {\n                var that = this;\n                dates = dates || [];\n                this._dates = dates;\n                this._startDate = dates[0];\n                this._endDate = dates[dates.length - 1 || 0];\n                this.createLayout(this._layout(dates));\n                this._content(dates);\n                this._footer();\n                this.refreshLayout();\n                var allDayHeader = this.element.find('.k-scheduler-header-all-day td');\n                if (allDayHeader.length) {\n                    this._allDayHeaderHeight = allDayHeader.first()[0].clientHeight;\n                }\n                that.element.on('click' + NS, '.k-nav-day', function (e) {\n                    var th = $(e.currentTarget).closest('th');\n                    var offset = th.offset();\n                    var additioanlWidth = 0;\n                    var additionalHeight = outerHeight(th);\n                    if (that._isGroupedByDate()) {\n                        if (that._isVerticallyGrouped()) {\n                            additioanlWidth = outerWidth(that.times);\n                            additionalHeight = 0;\n                        } else {\n                            additionalHeight = outerHeight(that.datesHeader);\n                        }\n                    }\n                    var slot = that._slotByPosition(offset.left + additioanlWidth, offset.top + additionalHeight);\n                    that.trigger('navigate', {\n                        view: 'day',\n                        date: slot.startDate()\n                    });\n                });\n            },\n            startTime: function () {\n                var options = this.options;\n                return options.showWorkHours ? options.workDayStart : options.startTime;\n            },\n            endTime: function () {\n                var options = this.options;\n                return options.showWorkHours ? options.workDayEnd : options.endTime;\n            },\n            startDate: function () {\n                return this._startDate;\n            },\n            endDate: function () {\n                return this._endDate;\n            },\n            _end: function (isAllDay) {\n                var time = getMilliseconds(this.endTime()) || MS_PER_DAY;\n                if (isAllDay) {\n                    time = 0;\n                }\n                return new Date(this._endDate.getTime() + time);\n            },\n            nextDate: function () {\n                return kendo.date.nextDay(this.endDate());\n            },\n            previousDate: function () {\n                return kendo.date.previousDay(this.startDate());\n            },\n            calculateDateRange: function () {\n                this._render([this.options.date]);\n            },\n            destroy: function () {\n                var that = this;\n                if (that._currentTimeUpdateTimer) {\n                    clearInterval(that._currentTimeUpdateTimer);\n                }\n                if (that.datesHeader) {\n                    that.datesHeader.off(NS);\n                }\n                if (that.element) {\n                    that.element.off(NS);\n                }\n                if (that.footer) {\n                    that.footer.remove();\n                }\n                SchedulerView.fn.destroy.call(this);\n                if (this._isMobile() && that.options.editable) {\n                    if (that.options.editable.create !== false) {\n                        that._addUserEvents.destroy();\n                        that._allDayUserEvents.destroy();\n                    }\n                    if (that.options.editable.update !== false) {\n                        that._editUserEvents.destroy();\n                    }\n                }\n            },\n            inRange: function (options) {\n                var inRange = SchedulerView.fn.inRange.call(this, options);\n                if (options.isAllDay) {\n                    return inRange;\n                }\n                var startTime = getMilliseconds(this.startTime());\n                var endTime = getMilliseconds(this.endTime()) || kendo.date.MS_PER_DAY;\n                var start = getMilliseconds(options.start);\n                var end = getMilliseconds(options.end) || kendo.date.MS_PER_DAY;\n                return inRange && startTime <= start && end <= endTime;\n            },\n            selectionByElement: function (cell) {\n                var offset = cell.offset();\n                return this._slotByPosition(offset.left, offset.top);\n            },\n            _timeSlotInterval: function () {\n                var options = this.options;\n                return options.majorTick / options.minorTickCount * MS_PER_MINUTE;\n            },\n            _timeSlotIndex: function (date) {\n                var options = this.options;\n                var eventStartTime = getMilliseconds(date);\n                var startTime = getMilliseconds(this.startTime());\n                var timeSlotInterval = options.majorTick / options.minorTickCount * MS_PER_MINUTE;\n                return (eventStartTime - startTime) / timeSlotInterval;\n            },\n            _slotIndex: function (date, multiday) {\n                if (multiday) {\n                    return this._dateSlotIndex(date);\n                }\n                return this._timeSlotIndex(date);\n            },\n            _dateSlotIndex: function (date, overlaps) {\n                var idx;\n                var length;\n                var slots = this._dates || [];\n                var slotStart;\n                var slotEnd;\n                var offset = 1;\n                for (idx = 0, length = slots.length; idx < length; idx++) {\n                    slotStart = kendo.date.getDate(slots[idx]);\n                    slotEnd = new Date(kendo.date.getDate(slots[idx]).getTime() + MS_PER_DAY - (overlaps ? 0 : 1));\n                    if (isInDateRange(date, slotStart, slotEnd)) {\n                        return idx * offset;\n                    }\n                }\n                return -1;\n            },\n            _positionAllDayEvent: function (element, slotRange) {\n                var slotWidth = slotRange.innerWidth();\n                var startIndex = slotRange.start.index;\n                var endIndex = slotRange.end.index;\n                var allDayEvents = SchedulerView.collidingEvents(slotRange.events(), startIndex, endIndex);\n                var currentColumnCount = this._headerColumnCount || 0;\n                var leftOffset = 2;\n                var rightOffset = startIndex !== endIndex ? 5 : 4;\n                var eventHeight = this._allDayHeaderHeight;\n                var start = slotRange.startSlot();\n                element.css({\n                    left: start.offsetLeft + leftOffset,\n                    width: slotWidth - rightOffset\n                });\n                slotRange.addEvent({\n                    slotIndex: startIndex,\n                    start: startIndex,\n                    end: endIndex,\n                    element: element\n                });\n                allDayEvents.push({\n                    slotIndex: startIndex,\n                    start: startIndex,\n                    end: endIndex,\n                    element: element\n                });\n                var rows = SchedulerView.createRows(allDayEvents);\n                if (rows.length && rows.length > currentColumnCount) {\n                    this._headerColumnCount = rows.length;\n                }\n                var top = slotRange.start.offsetTop;\n                for (var idx = 0, length = rows.length; idx < length; idx++) {\n                    var rowEvents = rows[idx].events;\n                    for (var j = 0, eventLength = rowEvents.length; j < eventLength; j++) {\n                        $(rowEvents[j].element).css({ top: top + idx * eventHeight });\n                    }\n                }\n            },\n            _arrangeColumns: function (element, top, height, slotRange) {\n                var startSlot = slotRange.start;\n                element = {\n                    element: element,\n                    slotIndex: startSlot.index,\n                    start: top,\n                    end: top + height\n                };\n                var columns, slotWidth = startSlot.clientWidth, eventRightOffset = slotWidth * 0.1, columnEvents, eventElements = slotRange.events(), slotEvents = SchedulerView.collidingEvents(eventElements, element.start, element.end);\n                slotRange.addEvent(element);\n                slotEvents.push(element);\n                columns = SchedulerView.createColumns(slotEvents);\n                var columnWidth = (slotWidth - eventRightOffset) / columns.length;\n                for (var idx = 0, length = columns.length; idx < length; idx++) {\n                    columnEvents = columns[idx].events;\n                    for (var j = 0, eventLength = columnEvents.length; j < eventLength; j++) {\n                        var calculatedWidth = columnWidth - 4;\n                        columnEvents[j].element[0].style.width = (calculatedWidth > 0 ? calculatedWidth : columnWidth) + 'px';\n                        columnEvents[j].element[0].style.left = (this._isRtl ? eventRightOffset : 0) + startSlot.offsetLeft + idx * columnWidth + 2 + 'px';\n                    }\n                }\n            },\n            _positionEvent: function (event, element, slotRange) {\n                var start = event._startTime || event.start;\n                var end = event._endTime || event.end;\n                var rect = slotRange.innerRect(start, end, false);\n                var height = rect.bottom - rect.top - 2;\n                if (height < 0) {\n                    height = 0;\n                }\n                element.css({\n                    top: rect.top,\n                    height: height\n                });\n                this._arrangeColumns(element, rect.top, element[0].clientHeight, slotRange);\n            },\n            _createEventElement: function (event, isOneDayEvent, head, tail) {\n                var template = isOneDayEvent ? this.eventTemplate : this.allDayEventTemplate;\n                var options = this.options;\n                var editable = options.editable;\n                var isMobile = this._isMobile();\n                var showDelete = editable && editable.destroy !== false && !isMobile;\n                var resizable = editable && editable.resize !== false;\n                var startDate = getDate(this.startDate());\n                var endDate = getDate(this.endDate());\n                var startTime = getMilliseconds(this.startTime());\n                var endTime = getMilliseconds(this.endTime());\n                var eventStartTime = event._time('start');\n                var eventEndTime = event._time('end');\n                var middle;\n                if (startTime >= endTime) {\n                    endTime = getMilliseconds(new Date(this.endTime().getTime() + MS_PER_DAY - 1));\n                }\n                if (!isOneDayEvent && !event.isAllDay) {\n                    endDate = new Date(endDate.getTime() + MS_PER_DAY);\n                }\n                var eventStartDate = event.start;\n                var eventEndDate = event.end;\n                if (event.isAllDay) {\n                    eventEndDate = getDate(event.end);\n                }\n                if (!isInDateRange(getDate(eventStartDate), startDate, endDate) && !isInDateRange(eventEndDate, startDate, endDate) || isOneDayEvent && eventStartTime < startTime && eventEndTime > endTime) {\n                    middle = true;\n                } else if (getDate(eventStartDate) < startDate || isOneDayEvent && eventStartTime < startTime) {\n                    tail = true;\n                } else if (eventEndDate > endDate && !isOneDayEvent || isOneDayEvent && eventEndTime > endTime) {\n                    head = true;\n                }\n                var resources = this.eventResources(event);\n                if (event._startTime && eventStartTime !== kendo.date.getMilliseconds(event.start)) {\n                    eventStartDate = new Date(eventStartTime);\n                    eventStartDate = kendo.timezone.apply(eventStartDate, 'Etc/UTC');\n                }\n                if (event._endTime && eventEndTime !== kendo.date.getMilliseconds(event.end)) {\n                    eventEndDate = new Date(eventEndTime);\n                    eventEndDate = kendo.timezone.apply(eventEndDate, 'Etc/UTC');\n                }\n                var data = extend({}, {\n                    ns: kendo.ns,\n                    resizable: resizable,\n                    showDelete: showDelete,\n                    middle: middle,\n                    head: head,\n                    tail: tail,\n                    singleDay: this._dates.length == 1,\n                    resources: resources,\n                    inverseColor: false,\n                    messages: options.messages\n                }, event, {\n                    start: eventStartDate,\n                    end: eventEndDate\n                });\n                var element = $(template(data));\n                this.angular('compile', function () {\n                    return {\n                        elements: element,\n                        data: [{ dataItem: data }]\n                    };\n                });\n                return element;\n            },\n            _isInTimeSlot: function (event) {\n                var slotStartTime = this.startTime(), slotEndTime = this.endTime(), startTime = event._startTime || event.start, endTime = event._endTime || event.end;\n                if (getMilliseconds(slotEndTime) === getMilliseconds(kendo.date.getDate(slotEndTime))) {\n                    slotEndTime = kendo.date.getDate(slotEndTime);\n                    setTime(slotEndTime, MS_PER_DAY - 1);\n                }\n                if (event._date('end') > event._date('start')) {\n                    endTime = +event._date('end') + (MS_PER_DAY - 1);\n                }\n                endTime = event._endTime ? endTime - event._date('end') : getMilliseconds(new Date(endTime));\n                startTime = event._startTime ? startTime - event._date('start') : getMilliseconds(new Date(startTime));\n                slotEndTime = getMilliseconds(slotEndTime);\n                slotStartTime = getMilliseconds(slotStartTime);\n                if (slotStartTime === startTime && startTime === endTime) {\n                    return true;\n                }\n                var overlaps = startTime !== slotEndTime;\n                return isInTimeRange(startTime, slotStartTime, slotEndTime, overlaps) || isInTimeRange(endTime, slotStartTime, slotEndTime, overlaps) || isInTimeRange(slotStartTime, startTime, endTime) || isInTimeRange(slotEndTime, startTime, endTime);\n            },\n            _isInDateSlot: function (event) {\n                var groups = this.groups[0];\n                var slotStart = groups.firstSlot().start;\n                var slotEnd = groups.lastSlot().end - 1;\n                var startTime = kendo.date.toUtcTime(event.start);\n                var endTime = kendo.date.toUtcTime(event.end);\n                return (isInDateRange(startTime, slotStart, slotEnd) || isInDateRange(endTime, slotStart, slotEnd) || isInDateRange(slotStart, startTime, endTime) || isInDateRange(slotEnd, startTime, endTime)) && (!isInDateRange(endTime, slotStart, slotStart) || isInDateRange(endTime, startTime, startTime) || event.isAllDay);\n            },\n            _updateAllDayHeaderHeight: function (height) {\n                if (this._height !== height) {\n                    this._height = height;\n                    var allDaySlots = this.element.find('.k-scheduler-header-all-day td');\n                    if (allDaySlots.length) {\n                        allDaySlots.parent().add(this.element.find('.k-scheduler-times-all-day').parent()).height(height);\n                        for (var groupIndex = 0; groupIndex < this.groups.length; groupIndex++) {\n                            this.groups[groupIndex].refresh();\n                        }\n                    }\n                }\n            },\n            _renderEvents: function (events, groupIndex) {\n                var allDayEventContainer = this.datesHeader.find('.k-scheduler-header-wrap > div');\n                var byDate = this._isGroupedByDate();\n                var event;\n                var idx;\n                var length;\n                for (idx = 0, length = events.length; idx < length; idx++) {\n                    event = events[idx];\n                    if (this._isInDateSlot(event)) {\n                        var isMultiDayEvent = event.isAllDay || event.duration() >= MS_PER_DAY;\n                        var container = isMultiDayEvent && !this._isVerticallyGrouped() ? allDayEventContainer : this.content;\n                        var element, ranges, range, start, end, group;\n                        if (!isMultiDayEvent) {\n                            if (this._isInTimeSlot(event)) {\n                                group = this.groups[groupIndex];\n                                if (!group._continuousEvents) {\n                                    group._continuousEvents = [];\n                                }\n                                ranges = group.slotRanges(event);\n                                var rangeCount = ranges.length;\n                                for (var rangeIndex = 0; rangeIndex < rangeCount; rangeIndex++) {\n                                    range = ranges[rangeIndex];\n                                    start = event.start;\n                                    end = event.end;\n                                    if (rangeCount > 1) {\n                                        if (rangeIndex === 0) {\n                                            end = range.end.endDate();\n                                        } else if (rangeIndex == rangeCount - 1) {\n                                            start = range.start.startDate();\n                                        } else {\n                                            start = range.start.startDate();\n                                            end = range.end.endDate();\n                                        }\n                                    }\n                                    var occurrence = event.clone({\n                                        start: start,\n                                        end: end,\n                                        _startTime: event._startTime,\n                                        _endTime: event.endTime\n                                    });\n                                    if (this._isInTimeSlot(occurrence)) {\n                                        var head = range.head;\n                                        element = this._createEventElement(event, !isMultiDayEvent, head, range.tail);\n                                        element.appendTo(container);\n                                        this._inverseEventColor(element);\n                                        this._positionEvent(occurrence, element, range);\n                                        addContinuousEvent(group, range, element, false);\n                                    }\n                                }\n                            }\n                        } else if (this.options.allDaySlot) {\n                            group = this.groups[groupIndex];\n                            if (!group._continuousEvents) {\n                                group._continuousEvents = [];\n                            }\n                            ranges = group.slotRanges(event);\n                            if (ranges.length) {\n                                range = ranges[0];\n                                var startIndex = range.start.index;\n                                var endIndex = range.end.index;\n                                if (byDate && startIndex !== endIndex) {\n                                    start = range.start.start;\n                                    end = range.end.end;\n                                    var newStart = new Date(start);\n                                    var newEnd = new Date(start);\n                                    for (var i = range.start.index; i <= range.end.index; i++) {\n                                        element = this._createEventElement(event, !isMultiDayEvent, i !== endIndex, i !== startIndex);\n                                        var dateRange = group.daySlotRanges(newStart, newEnd, true)[0];\n                                        newEnd.setDate(newEnd.getDate() + 1);\n                                        newStart.setDate(newStart.getDate() + 1);\n                                        this._positionAllDayEvent(element, dateRange);\n                                        addContinuousEvent(group, dateRange, element, true);\n                                        element.appendTo(container);\n                                        this._inverseEventColor(element);\n                                    }\n                                } else {\n                                    element = this._createEventElement(event, !isMultiDayEvent);\n                                    this._positionAllDayEvent(element, ranges[0]);\n                                    addContinuousEvent(group, ranges[0], element, true);\n                                    element.appendTo(container);\n                                    this._inverseEventColor(element);\n                                }\n                            }\n                        }\n                    }\n                }\n            },\n            render: function (events) {\n                this._headerColumnCount = 0;\n                this._groups();\n                this.element.find('.k-event').remove();\n                events = new kendo.data.Query(events).sort([\n                    {\n                        field: 'start',\n                        dir: 'asc'\n                    },\n                    {\n                        field: 'end',\n                        dir: 'desc'\n                    }\n                ]).toArray();\n                var eventsByResource = [];\n                this._eventsByResource(events, this.groupedResources, eventsByResource);\n                var eventsPerDate = $.map(this._dates, function (date) {\n                    return Math.max.apply(null, $.map(eventsByResource, function (events) {\n                        return $.grep(events, function (event) {\n                            return event.isMultiDay() && isInDateRange(date, getDate(event.start), getDate(event.end));\n                        }).length;\n                    }));\n                });\n                var height = Math.max.apply(null, eventsPerDate);\n                this._updateAllDayHeaderHeight((height + 1) * this._allDayHeaderHeight);\n                for (var groupIndex = 0; groupIndex < eventsByResource.length; groupIndex++) {\n                    this._renderEvents(eventsByResource[groupIndex], groupIndex);\n                }\n                this.refreshLayout();\n                this._currentTime(false);\n                this.trigger('activate');\n            },\n            _eventsByResource: function (events, resources, result) {\n                var resource = resources[0];\n                if (resource) {\n                    var view = resource.dataSource.view();\n                    for (var itemIdx = 0; itemIdx < view.length; itemIdx++) {\n                        var value = this._resourceValue(resource, view[itemIdx]);\n                        var eventsFilteredByResource = new kendo.data.Query(events).filter({\n                            field: resource.field,\n                            operator: SchedulerView.groupEqFilter(value)\n                        }).toArray();\n                        if (resources.length > 1) {\n                            this._eventsByResource(eventsFilteredByResource, resources.slice(1), result);\n                        } else {\n                            result.push(eventsFilteredByResource);\n                        }\n                    }\n                } else {\n                    result.push(events);\n                }\n            },\n            _columnOffsetForResource: function (index) {\n                return this._columnCountForLevel(index) / this._columnCountForLevel(index - 1);\n            },\n            _columnCountForLevel: function (level) {\n                var columnLevel = this.columnLevels[level];\n                return columnLevel ? columnLevel.length : 0;\n            },\n            _rowCountForLevel: function (level) {\n                var rowLevel = this.rowLevels[level];\n                return rowLevel ? rowLevel.length : 0;\n            },\n            clearSelection: function () {\n                this.content.add(this.datesHeader).find('.k-state-selected').removeAttr('id').attr('aria-selected', false).removeClass('k-state-selected');\n            },\n            _updateDirection: function (selection, ranges, multiple, reverse, vertical) {\n                var isDaySlot = selection.isAllDay;\n                var startSlot = ranges[0].start;\n                var endSlot = ranges[ranges.length - 1].end;\n                if (multiple) {\n                    if (vertical) {\n                        if (!isDaySlot && startSlot.index === endSlot.index && startSlot.collectionIndex === endSlot.collectionIndex) {\n                            selection.backward = reverse;\n                        }\n                    } else {\n                        if (isDaySlot && startSlot.index === endSlot.index || !isDaySlot && startSlot.collectionIndex === endSlot.collectionIndex) {\n                            selection.backward = reverse;\n                        }\n                    }\n                }\n            },\n            _changeViewPeriod: function (selection, reverse, vertical) {\n                if (!vertical) {\n                    var date = reverse ? this.previousDate() : this.nextDate();\n                    var start = selection.start;\n                    var end = selection.end;\n                    var verticalByDate = this._isGroupedByDate() && this._isVerticallyGrouped();\n                    var group = this.groups[selection.groupIndex];\n                    var collection = reverse ? group._timeSlotCollections : group._getCollections(group.daySlotCollectionCount());\n                    var slots = collection[collection.length - 1]._slots;\n                    var slotIndex = !reverse && !group.daySlotCollectionCount() ? 0 : slots.length - 1;\n                    var endMilliseconds;\n                    var newDateStart, newDateEnd;\n                    newDateStart = new Date(date);\n                    newDateEnd = new Date(date);\n                    if (this._isInRange(newDateStart, newDateEnd)) {\n                        return false;\n                    }\n                    selection.start = newDateStart;\n                    selection.end = newDateEnd;\n                    if (verticalByDate) {\n                        var newStart = new Date(slots[slotIndex].startDate());\n                        var newEnd = new Date(slots[slotIndex].endDate());\n                        endMilliseconds = getMilliseconds(newEnd) ? getMilliseconds(newEnd) : MS_PER_DAY;\n                        setTime(selection.start, getMilliseconds(newStart));\n                        setTime(selection.end, endMilliseconds);\n                        if (group.daySlotCollectionCount()) {\n                            selection.isAllDay = !selection.isAllDay;\n                        }\n                    } else {\n                        endMilliseconds = selection.isAllDay || !getMilliseconds(end) ? MS_PER_DAY : getMilliseconds(end);\n                        setTime(selection.start, getMilliseconds(start));\n                        setTime(selection.end, endMilliseconds);\n                    }\n                    if (!this._isVerticallyGrouped()) {\n                        selection.groupIndex = reverse ? this.groups.length - 1 : 0;\n                    }\n                    selection.events = [];\n                    return true;\n                }\n            }\n        });\n        extend(true, ui, {\n            MultiDayView: MultiDayView,\n            DayView: MultiDayView.extend({\n                options: {\n                    name: 'DayView',\n                    title: 'Day',\n                    selectedMobileDateFormat: '{0:MMM d}'\n                },\n                name: 'day'\n            }),\n            WeekView: MultiDayView.extend({\n                options: {\n                    name: 'WeekView',\n                    title: 'Week',\n                    selectedDateFormat: '{0:D} - {1:D}',\n                    selectedShortDateFormat: '{0:d} - {1:d}'\n                },\n                name: 'week',\n                calculateDateRange: function () {\n                    var selectedDate = this.options.date, start = kendo.date.dayOfWeek(selectedDate, this.calendarInfo().firstDay, -1), idx, length, dates = [];\n                    for (idx = 0, length = 7; idx < length; idx++) {\n                        dates.push(start);\n                        start = kendo.date.nextDay(start);\n                    }\n                    this._render(dates);\n                }\n            }),\n            WorkWeekView: MultiDayView.extend({\n                options: {\n                    name: 'WorkWeekView',\n                    title: 'Work Week',\n                    selectedDateFormat: '{0:D} - {1:D}',\n                    selectedShortDateFormat: '{0:d} - {1:d}'\n                },\n                name: 'workWeek',\n                nextDate: function () {\n                    var weekStart = kendo.date.dayOfWeek(kendo.date.nextDay(this.startDate()), this.calendarInfo().firstDay, 1);\n                    return kendo.date.addDays(weekStart, this._workDays[0]);\n                },\n                previousDate: function () {\n                    var weekStart = kendo.date.dayOfWeek(this.startDate(), this.calendarInfo().firstDay, -1);\n                    var workDays = this._workDays;\n                    return kendo.date.addDays(weekStart, workDays[workDays.length - 1] - 7);\n                },\n                calculateDateRange: function () {\n                    var selectedDate = this.options.date, dayOfWeek = kendo.date.dayOfWeek, weekStart = dayOfWeek(selectedDate, this.calendarInfo().firstDay, -1), start = dayOfWeek(weekStart, this.options.workWeekStart, 1), end = dayOfWeek(start, this.options.workWeekEnd, 1), dates = [];\n                    while (start <= end) {\n                        dates.push(start);\n                        start = kendo.date.nextDay(start);\n                    }\n                    this._render(dates);\n                }\n            })\n        });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}