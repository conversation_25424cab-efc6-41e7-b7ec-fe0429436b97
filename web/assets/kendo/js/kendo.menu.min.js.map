{"version": 3, "sources": ["kendo.menu.js"], "names": ["f", "define", "$", "undefined", "getEffectDirection", "direction", "root", "split", "replace", "parseDirection", "isRtl", "output", "origin", "position", "horizontal", "test", "kendo", "directions", "reverse", "join", "contains", "parent", "child", "e", "updateItemClasses", "item", "addClass", "children", "IMG", "IMAGE", "LINK", "filter", "DEFAULTSTATE", "empty", "append", "DISABLEDSTATE", "removeAttr", "attr", "length", "LINK_SELECTOR", "contents", "this", "nodeName", "match", "excludedNodesRegExp", "nodeType", "trim", "nodeValue", "wrapAll", "updateArrow", "updateFirstLast", "find", "remove", "each", "arrowCssClass", "getArrowCssClass", "support", "hasClass", "MENU", "removeClass", "FIRST", "LAST", "updateHasAriaPopup", "parents", "index", "parentLi", "eq", "getParentLiItems", "group", "parentsUntil", "storeItemSelectEventHandler", "element", "options", "<PERSON><PERSON><PERSON><PERSON>", "getItemSelectEventHandler", "setItemData", "items", "i", "data", "select", "isFunction", "popupOpenerSelector", "id", "popupGroupSelector", "getChildPopups", "currentPopup", "overflowWrapper", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "opener", "popupId", "popup", "POPUP_OPENER_ATTR", "push", "popupParentItem", "popupElement", "POPUP_ID_ATTR", "itemPopup", "animationContainerSelector", "overflowMenuParents", "current", "last", "getParents", "parentNode", "is", "elem", "mousewheelDel<PERSON>", "delta", "wheelDelta", "Math", "ceil", "floor", "detail", "round", "parentsScroll", "scrollDirection", "scroll", "isNaN", "isPointer<PERSON><PERSON>ch", "allPointers", "originalEvent", "pointerType", "touchPointerTypes", "is<PERSON><PERSON>ch", "ev", "touch", "type", "removeSpacesBetweenItems", "ul", "window", "ui", "activeElement", "_activeElement", "mobileOS", "isArray", "HierarchicalDataSource", "MOUSEDOWN", "CLICK", "DELAY", "SCROLLSPEED", "extend", "proxy", "template", "keys", "Widget", "NS", "OPEN", "ICON_SELECTOR", "CLOSE", "TIMER", "SELECT", "ZINDEX", "ACTIVATE", "DEACTIVATE", "POINTERDOWN", "pointers", "msPointers", "CHANGE", "ERROR", "TOUCHSTART", "MOUSEENTER", "MOUSELEAVE", "MOUSEWHEEL", "RESIZE", "resize", "SCROLLWIDTH", "SCROLLHEIGHT", "OFFSETWIDTH", "OFFSETHEIGHT", "DOCUMENT_ELEMENT", "document", "documentElement", "KENDOPOPUP", "HOVERSTATE", "FOCUSEDSTATE", "SELECTEDSTATE", "menuSelector", "groupSelector", "popupSelector", "allItemsSelector", "disabledSelector", "itemSelector", "availableItemsSelector", "linkSelector", "exclusionSelector", "nextSelector", "lastSelector", "templateSelector", "scrollButtonSelector", "2", "STRING", "DATABOUND", "bindings", "text", "url", "spriteCssClass", "imageUrl", "imageAttr", "content", "rendering", "wrapperCssClass", "enabled", "firstLevel", "cssClass", "hasOwnProperty", "selected", "itemCssAttributes", "attributes", "imageCssAttributes", "imgAttributes", "toJSON", "contentCssAttributes", "contentAttr", "defaultClasses", "textClass", "arrowClass", "groupAttributes", "expanded", "groupCssClass", "<PERSON><PERSON>", "DataBoundWidget", "init", "that", "fn", "call", "wrapper", "_accessors", "_templates", "_dataSource", "_updateClasses", "_animations", "nextItemZIndex", "_tabindex", "_initOverflow", "_attachMenuEventsHandlers", "openOnClick", "clicked", "_ariaId", "format", "notify", "events", "name", "animation", "open", "duration", "close", "orientation", "closeOnClick", "hoverDelay", "scrollable", "popupCollision", "_initData", "dataSource", "angular", "elements", "view", "_overflowWrapper", "on", "_focus<PERSON><PERSON><PERSON>", "_click", "_preventClose", "_mouseenter", "_mouseleave", "_mousedown", "_toggleHover", "_keydown", "_focus", "_removeHoverItem", "_checkActiveElement", "_mouseleavePopup", "_mouseenterPopup", "_documentClickHandler", "_documentClick", "click", "_detachMenuEventsHandlers", "off", "unbind", "backwardBtn", "forwardBtn", "initialWidth", "initialCssWidth", "isHorizontal", "_openedPopups", "_scrollWrapper", "wrap", "templates", "scrollButton", "add", "appendTo", "_initScrolling", "outerWidth", "style", "width", "throttle", "_setOverflowWrapperWidth", "_toggleScrollButtons", "_popupsWrapper", "wrapperWidth", "menuWidth", "borders", "wrapperCssWidth", "css", "offsetWidth", "clientWidth", "min", "_reinitOverflow", "overflowChanged", "distance", "_destroyOverflow", "popupWrapper", "popupParentLi", "unwrap", "scrollElement", "isNumeric", "mouseWheelDistance", "backward", "forward", "backwardDouble", "forwardDouble", "scrolling", "touchEvents", "value", "scrollValue", "scrollLeft", "scrollTop", "finish", "animate", "mouseenterHandler", "mousedownHandler", "stop", "currentTarget", "trigger", "stopPropagation", "preventDefault", "eventMap", "down", "scrollSpeed", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "abs", "_closeChildPopups", "currentScroll", "scrollSize", "offset", "toggle", "setOptions", "destroy", "enable", "_toggleDisabled", "disable", "attemptGetItem", "candidate", "referenceItem", "inserted", "_insert", "insertBefore", "before", "insertAfter", "after", "groups", "plain", "groupData", "isPlainObject", "ObservableObject", "renderGroup", "ObservableArray", "map", "idx", "get", "renderItem", "char<PERSON>t", "parentItems", "container", "_openAfterLoad", "dataItem", "loaded", "_loading", "one", "visiblePopups", "closePopup", "getByUid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "load", "closest", "siblings", "li", "clearTimeout", "setTimeout", "overflowPopup", "windowHeight", "setScrolling", "nextZindex", "parentHorizontal", "effects", "openEffects", "_getPopup", "_triggerEvent", "height", "maxHeight", "_outerHeight", "getShadows", "bottom", "overflow", "browser", "msie", "version", "zIndex", "kendoPopup", "activate", "deactivate", "_closing", "sender", "removeData", "opacity", "collision", "anchor", "_popupOpen", "hide", "mouseAndTouchPresent", "_configurePopupOverflow", "_hovered", "_initPopupScrolling", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "groupId", "_wrapPopupElement", "Date", "getTime", "autosize", "display", "skipMouseEvents", "scrollHeight", "offsetHeight", "_initPopupScrollButtons", "scrollButtons", "timeout", "p", "isEmptyObject", "_closeParentPopups", "first", "_keyTriggered", "_setPopupHeight", "isFixed", "location", "popupOuterHeight", "popupOffsetTop", "bottomScrollbar", "canFit", "popups", "_initialHeight", "_location", "max", "top", "innerHeight", "dontClearClose", "hasChildPopupsHovered", "isPopupMouseLeaved", "_isRootItem", "_forceClose", "toggleClass", "target", "eventTarget", "isEnter", "indexOf", "_closurePrevented", "hoverItem", "_hoverItem", "_findRootParent", "hasFocus", "oldHoverItem", "_oldHoverItem", "nonContentGroupsSelector", "matchesSelector", "end", "_itemHasC<PERSON><PERSON>n", "pointerTouch", "<PERSON><PERSON><PERSON><PERSON>", "rootMenuItems", "subMenuItems", "relatedTarget", "_", "sibling", "$window", "stopImmediatePropagation", "toElement", "clientX", "clientY", "_closePopups", "rootPopup", "groupParent", "innerPopup", "_innerPopup", "openHandle", "childGroup", "childGroupVisible", "shouldCloseTheRootItem", "childPopupId", "targetElement", "toUpperCase", "formNode", "link", "itemElement", "href", "targetHref", "sampleHref", "isLink", "isLocalLink", "isTargetLink", "handled", "_triggerSelect", "_parentsUntil", "enter<PERSON>ey", "context", "selector", "itemSelectEventData", "isSelectItemDefaultPrevented", "isSelectDefaultPrevented", "_getEventData", "isDefaultPrevented", "eventData", "_defaultPrevented", "active", "_moveHover", "focus", "belongsToVertical", "key", "keyCode", "ESC", "_itemBelongsToVertival", "RIGHT", "LEFT", "DOWN", "_itemDown", "UP", "_itemUp", "HOME", "END", "_itemEsc", "ENTER", "SPACEBAR", "_childPopupElement", "TAB", "menuIsVertical", "nextItem", "_scrollToItem", "_itemRight", "parentItem", "nextAll", "prevAll", "_itemLeft", "scrollDir", "getSize", "currentScrollOffset", "itemSize", "itemOffset", "ulSize", "scrollButtonSize", "itemPosition", "_outerWidth", "_unbindDataSource", "fields", "field", "create", "_bindDataSource", "fetch", "_refresh<PERSON><PERSON><PERSON>", "refresh", "_error<PERSON><PERSON><PERSON>", "_error", "bind", "findByUid", "uid", "wrapperElement", "node", "action", "parentElement", "itemsToUpdate", "updateProxy", "_updateItem", "removeProxy", "_removeItem", "_appendItems", "for<PERSON>ach", "nextElement", "next", "textField", "_fieldAccessor", "fieldName", "fieldB<PERSON>ings", "count", "x", "expr", "fieldAccessor", "itemWrapper", "arrow", "sprite", "menu", "renderContent", "dataContentField", "subGroup", "renderItems", "html", "len", "ContextMenu", "_marker", "guid", "substring", "_popup", "_wire", "_initialWidth", "showOn", "alignToAnchor", "_showProxy", "userEvents", "mousedown", "_closeProxy", "y", "visible", "kendoStop", "left", "_configurePopupScrolling", "downEvent", "_mousedownProxy", "visibility", "_setPopupWidth", "windowWidth", "popupOuterWidth", "popupOffsetLeft", "shadow", "max<PERSON><PERSON><PERSON>", "right", "arguments", "_closeTimeoutProxy", "_show<PERSON><PERSON><PERSON>", "event", "pageX", "pageY", "_event<PERSON><PERSON><PERSON>", "_targetChild", "_<PERSON><PERSON><PERSON><PERSON>", "normalClick", "<PERSON><PERSON><PERSON><PERSON>", "containment", "which", "UserEvents", "allowSelection", "_triggerProxy", "copyAnchorStyles", "noop", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,cACH,cACA,cACDD,IACL,WAm+DE,MAv9DC,UAAUE,EAAGC,GAmGV,QAASC,GAAmBC,EAAWC,GAEnC,MADAD,GAAYA,EAAUE,MAAM,MAAMD,EAAO,IAAMD,EACxCA,EAAUG,QAAQ,MAAO,MAAMA,QAAQ,SAAU,QAE5D,QAASC,GAAeJ,EAAWC,EAAMI,GACrCL,EAAYA,EAAUE,MAAM,MAAMD,EAAO,IAAMD,CAC/C,IAAIM,IACIC,QACI,SACAF,EAAQ,QAAU,QAEtBG,UACI,MACAH,EAAQ,QAAU,SAEvBI,EAAa,aAAaC,KAAKV,EAatC,OAZIS,IACAH,EAAOC,QACH,MACAP,GAEJM,EAAOE,SAAS,GAAKG,EAAMC,WAAWZ,GAAWa,UAEjDP,EAAOC,OAAO,GAAKP,EACnBM,EAAOE,SAAS,GAAKG,EAAMC,WAAWZ,GAAWa,SAErDP,EAAOC,OAASD,EAAOC,OAAOO,KAAK,KACnCR,EAAOE,SAAWF,EAAOE,SAASM,KAAK,KAChCR,EAEX,QAASS,GAASC,EAAQC,GACtB,IACI,MAAOpB,GAAEkB,SAASC,EAAQC,GAC5B,MAAOC,GACL,OAAO,GAGf,QAASC,GAAkBC,GACvBA,EAAOvB,EAAEuB,GACTA,EAAKC,SAAS,UAAUC,SAASC,GAAKF,SAASG,GAC/CJ,EAAKE,SAAS,KAAKD,SAASI,GAAMH,SAASC,GAAKF,SAASG,GACzDJ,EAAKM,OAAO,oBAAoBL,SAASM,IACzCP,EAAKM,OAAO,gBAAgBE,QAAQC,OAAO,UAC3CT,EAAKM,OAAO,gBAAgBL,SAASS,IAAeC,WAAW,YAAYC,KAAK,iBAAiB,GAC5FZ,EAAKM,OAAO,UAAUO,QACvBb,EAAKY,KAAK,OAAQ,YAEjBZ,EAAKE,SAASY,GAAeD,QAC9Bb,EAAKe,WAAWT,OAAO,WACnB,QAAQU,KAAKC,SAASC,MAAMC,IAA2C,GAAjBH,KAAKI,WAAkB3C,EAAE4C,KAAKL,KAAKM,cAC1FC,QAAQ,gBAAmBlB,EAAO,OAEzCmB,EAAYxB,GACZyB,EAAgBzB,GAEpB,QAASwB,GAAYxB,GACjBA,EAAOvB,EAAEuB,GACTA,EAAK0B,KAAK,oDAAoDC,SAC9D3B,EAAKM,OAAO,uBAAuBJ,SAAS,wDAAwD0B,KAAK,WACrG,GAAI5B,GAAOvB,EAAEuC,MAAOa,EAAgBC,EAAiB9B,EACrDA,GAAKS,OAAO,sBAAyBoB,EAAgB,6BAG7D,QAASC,GAAiB9B,GACtB,GAAI6B,GAAejC,EAASI,EAAKJ,SAASA,SAAUX,EAAQM,EAAMwC,QAAQ9C,MAAMW,EAUhF,OARIiC,GADAjC,EAAOoC,SAASC,EAAO,eACP,qBAEZhD,EACgB,qBAEA,sBAK5B,QAASwC,GAAgBzB,GACrBA,EAAOvB,EAAEuB,GACTA,EAAKM,OAAO,8BAA8B4B,YAAYC,GACtDnC,EAAKM,OAAO,4BAA4B4B,YAAYE,GACpDpC,EAAKM,OAAO,gBAAgBL,SAASkC,GACrCnC,EAAKM,OAAO,eAAeL,SAASmC,GAExC,QAASC,GAAmBC,GAA5B,GAEiBC,GACDC,CAFZ,IAAIF,GAAWA,EAAQzB,OACnB,IAAS0B,IAASD,GACVE,EAAWF,EAAQG,GAAGF,GACtBC,EAASd,KAAK,MAAMb,OACpB2B,EAAS5B,KAAK,iBAAiB,GAE/B4B,EAAS7B,WAAW,iBAKpC,QAAS+B,GAAiBC,GACtB,IAAKA,EAAMX,SAASC,GAChB,MAAOU,GAAMC,aAAa,IAAMX,EAAM,MAG9C,QAASY,GAA4BC,EAASC,GAC1C,GAAIC,GAAgBC,EAA0BF,EAC1CC,IACAE,EAAYJ,EAASE,GAErBD,EAAQI,OACR1E,EAAEqE,GAAS5C,SAAS,MAAMA,SAAS,MAAM0B,KAAK,SAAUwB,GACpDP,EAA4B7B,KAAM+B,EAAQI,MAAMC,MAI5D,QAASF,GAAYJ,EAASE,GAC1BvE,EAAEqE,GAAS5C,SAAS,WAAWmD,MAAOL,cAAeA,IAEzD,QAASC,GAA0BF,GAC/B,GAAIC,GAAgBD,EAAQO,OAAQC,EAAahE,EAAMgE,UACvD,OAAIP,IAAiBO,EAAWP,GACrBA,EAEJ,KAEX,QAASQ,GAAoBC,GACzB,MAAOA,GAAK,wBAA2BA,EAAK,KAAQ,uBAExD,QAASC,GAAmBD,GACxB,MAAOA,GAAK,kBAAqBA,EAAK,KAAQ,iBAElD,QAASE,GAAeC,EAAcC,GAAtC,GACQC,GAAmBF,EAAalC,KAAK8B,KACrCO,IAcJ,OAbAD,GAAiBlC,KAAK,SAAUwB,EAAGY,GAAb,GAEdC,GACAC,CACJ,KAHAF,EAASvF,EAAEuF,GACPC,EAAUD,EAAOX,KAAKc,IACtBD,EAAQN,EACLK,GACHC,EAAQL,EAAgBnC,KAAKgC,EAAmBO,GAAW,YACvDC,EAAMrD,QACNkD,EAAOK,KAAKF,GAEhBF,EAASE,EAAMxC,KAAK8B,KACpBS,EAAUD,EAAOX,KAAKc,MAGvBJ,EAEX,QAASM,GAAgBC,EAAcT,GACnC,GAAII,GAAUK,EAAajB,KAAKkB,GAChC,OAAON,GAAUJ,EAAgBnC,KAAK8B,EAAoBS,IAAYxF,MAE1E,QAAS+F,GAAUxE,EAAM6D,GACrB,GAAII,GAAUjE,EAAKqD,KAAKc,GACxB,OAAOF,GAAUJ,EAAgB3D,SAASuE,IAA4BvE,SAASwD,EAAmBO,IAAYxF,MAElH,QAASiG,GAAoBC,EAASd,GAAtC,GAUQe,GAEIN,EAXJhC,KACAuC,EAAa,SAAU7E,GACvB,KAAOA,EAAK8E,aAAejB,EAAgBkB,GAAG/E,EAAK8E,aAC/CxC,EAAQ8B,KAAKpE,EAAK8E,YAClB9E,EAAOA,EAAK8E,YAGhBE,EAAOL,EAAQ,IAAMA,CAGzB,KAFAE,EAAWG,GACPJ,EAAOtC,EAAQA,EAAQzB,OAAS,GAC7BpC,EAAEmG,GAAMG,GAAGN,MACVH,EAAe7F,EAAEmG,GAAM1E,SAAS,MACpC8E,EAAOX,EAAgBC,EAAcT,GAAiB,KAItDvB,EAAQ8B,KAAKY,GACbH,EAAWG,GACXJ,EAAOtC,EAAQA,EAAQzB,OAAS,EAEpC,OAAOyB,GAEX,QAAS2C,GAAgBnF,GACrB,GAAIoF,GAAQ,CAQZ,OAPIpF,GAAEqF,aACFD,GAASpF,EAAEqF,WAAa,IACxBD,EAAQA,EAAQ,EAAIE,KAAKC,KAAKH,GAASE,KAAKE,MAAMJ,IAElDpF,EAAEyF,SACFL,EAAQE,KAAKI,MAAM1F,EAAEyF,OAAS,IAE3BL,EAEX,QAASO,GAAcd,EAASe,GAG5B,IAHJ,GACQC,GAAS,EACT/F,EAAS+E,EAAQG,WACdlF,IAAWgG,MAAMhG,EAAO8F,KAC3BC,GAAU/F,EAAO8F,GACjB9F,EAASA,EAAOkF,UAEpB,OAAOa,GAEX,QAASE,GAAe/F,GACpB,MAAOgG,KAAehG,EAAEiG,eAAiBjG,EAAEiG,cAAcC,cAAeC,IAE5E,QAASC,GAAQpG,GACb,GAAIqG,GAAKrG,EAAEiG,aACX,OAAOK,IAAS,SAAS9G,KAAK6G,EAAGE,MAAQ,IAE7C,QAASC,GAAyBC,GAC9BA,EAAGxF,WAAWT,OAAO,WACjB,MAAwB,MAAjBU,KAAKC,WACbU,SAjTV,GACOpC,GAAQiH,OAAOjH,MAAOkH,EAAKlH,EAAMkH,GAAIC,EAAgBnH,EAAMoH,eAAgBP,EAAQ7G,EAAMwC,QAAQqE,OAAS7G,EAAMwC,QAAQ6E,SAAUC,EAAUpI,EAAEoI,QAASC,EAAyBvH,EAAM8D,KAAKyD,uBAAwBC,EAAY,YAAaC,EAAQ,QAASC,EAAQ,GAAIC,EAAc,GAAIC,EAAS1I,EAAE0I,OAAQC,EAAQ3I,EAAE2I,MAAOxF,EAAOnD,EAAEmD,KAAMyF,EAAW9H,EAAM8H,SAAUC,EAAO/H,EAAM+H,KAAMC,EAASd,EAAGc,OAAQpG,EAAsB,gBAAiBqG,EAAK,aAAcrH,EAAM,MAAOsH,EAAO,OAAQxF,EAAO,SAAU5B,EAAO,qBAAsBS,EAAgB,UAAW4G,EAAgB,UAAWtF,EAAO,SAAUuF,EAAQ,QAASC,EAAQ,QAASzF,EAAQ,UAAW/B,EAAQ,UAAWyH,GAAS,SAAUC,GAAS,SAAUC,GAAW,WAAYC,GAAa,aAAcC,GAAc,aAAeT,EAAK,iBAAmBA,EAAK,eAAiBA,EAAIU,GAAW3I,EAAMwC,QAAQmG,SAAUC,GAAa5I,EAAMwC,QAAQoG,WAAYrC,GAAcqC,IAAcD,GAAUE,GAAS,SAAUC,GAAQ,QAASC,GAAa/I,EAAMwC,QAAQqE,MAAQ,aAAe,GAAImC,GAAaL,GAAW,cAAgBC,GAAa,gBAAkB,aAAcK,GAAaN,GAAW,aAAeC,GAAa,eAAiB,aAAcM,GAAa,iBAAmBjB,EAAK,cAAgBA,EAAIkB,GAASnJ,EAAMwC,QAAQ4G,OAASnB,EAAIoB,GAAc,cAAeC,GAAe,eAAgBC,GAAc,cAAeC,GAAe,eAAgBxE,GAAgB,QAASJ,GAAoB,cAAe6E,GAAmBvK,EAAEwK,SAASC,iBAAkBC,GAAa,aAAc5I,GAAe,kBAAmB6I,GAAa,gBAAiBC,GAAe,kBAAmB3I,GAAgB,mBAAoB4I,GAAgB,mBAAoBC,GAAe,UAAWC,GAAgB,gBAAiB/E,GAA6B,yBAA0BgF,GAAgBD,GAAgB,IAAM/E,GAA4BiF,GAAmB,0BAA2BC,GAAmB,2BAA4BC,GAAe,UAAWC,GAAyB,iCAAkCC,GAAe,2CAA4CC,GAAoB,4BAA6BC,GAAeJ,GAAeG,GAAoB,SAAUE,GAAeL,GAAeG,GAAoB,QAASG,GAAmB,oDAAqDC,GAAuB,wBAAyBlE,IAC/3EmE,EAAK,EACLhE,MAAS,GACViE,GAAS,SAAUC,GAAY,YAAaC,IAC3CC,KAAM,gBACNC,IAAK,eACLC,eAAgB,0BAChBC,SAAU,oBACVC,UAAW,qBACXC,QAAS,oBACVC,IACCC,gBAAiB,SAAUpI,EAAO3C,GAC9B,GAAI+D,GAAS,SAAUxB,EAAQvC,EAAKuC,KAqBpC,OAnBIwB,IADA/D,EAAKgL,WAAY,EACP,oBAEA,mBAEVrI,EAAMsI,YAAwB,IAAV1I,IACpBwB,GAAU,YAEVxB,GAASI,EAAM9B,OAAS,IACxBkD,GAAU,WAEV/D,EAAKkL,WACLnH,GAAU,IAAM/D,EAAKkL,UAErBlL,EAAKY,MAAQZ,EAAKY,KAAKuK,eAAe,WACtCpH,GAAU,IAAM/D,EAAKY,KAAK,UAE1BZ,EAAKoL,WACLrH,GAAU,IAAMuF,IAEbvF,GAEXsH,kBAAmB,SAAUrL,GAAV,GAGNY,GAFLmD,EAAS,GACTuH,EAAatL,EAAKY,QACtB,KAASA,IAAQ0K,GACTA,EAAWH,eAAevK,IAAkB,UAATA,IACnCmD,GAAUnD,EAAO,KAAO0K,EAAW1K,GAAQ,KAGnD,OAAOmD,IAEXwH,mBAAoB,SAAUC,GAAV,GAQP5K,GAPLmD,EAAS,GACTuH,EAAaE,GAAiBA,EAAcC,OAASD,EAAcC,WAClEH,GAAW,SAGZA,EAAW,UAAY,IAAMlL,EAF7BkL,EAAW,SAAWlL,CAI1B,KAASQ,IAAQ0K,GACTA,EAAWH,eAAevK,KAC1BmD,GAAUnD,EAAO,KAAO0K,EAAW1K,GAAQ,KAGnD,OAAOmD,IAEX2H,qBAAsB,SAAU1L,GAAV,GASTY,GARLmD,EAAS,GACTuH,EAAatL,EAAK2L,gBAClBC,EAAiB,gCAChBN,GAAW,SAGZA,EAAW,UAAY,IAAMM,EAF7BN,EAAW,SAAWM,CAI1B,KAAShL,IAAQ0K,GACTA,EAAWH,eAAevK,KAC1BmD,GAAUnD,EAAO,KAAO0K,EAAW1K,GAAQ,KAGnD,OAAOmD,IAEX8H,UAAW,WACP,MAAOxL,IAEXyL,WAAY,SAAU9L,EAAM2C,GACxB,GAAIoB,GAAS,QAMb,OAJIA,IADApB,EAAMtD,WACI,qBAEA,uBAIlB0M,gBAAiB,SAAUpJ,GACvB,MAAOA,GAAMqJ,YAAa,EAAO,wBAA4B,IAEjEC,cAAe,WACX,MAAO,wBAEXpB,QAAS,SAAU7K,GACf,MAAOA,GAAK6K,QAAU7K,EAAK6K,QAAU,WAmN7CqB,GAAO3M,EAAMkH,GAAG0F,gBAAgBhF,QAChCiF,KAAM,SAAUtJ,EAASC,GACrB,GAAIsJ,GAAOrL,IACXuG,GAAO+E,GAAGF,KAAKG,KAAKF,EAAMvJ,EAASC,GACnCD,EAAUuJ,EAAKG,QAAUH,EAAKvJ,QAC9BC,EAAUsJ,EAAKtJ,QACfsJ,EAAKI,aACLJ,EAAKK,aACLL,EAAKM,cACLN,EAAKO,iBACLP,EAAKQ,YAAY9J,GACjBsJ,EAAKS,eAAiB,IACtBT,EAAKU,YACLV,EAAKW,cAAcjK,GACnBsJ,EAAKY,4BACDlK,EAAQmK,cACRb,EAAKc,SAAU,GAEnBrK,EAAQlC,KAAK,OAAQ,WACjBkC,EAAQ,GAAGW,KACX4I,EAAKe,QAAU7N,EAAM8N,OAAO,gBAAiBvK,EAAQ,GAAGW,KAE5DlE,EAAM+N,OAAOjB,IAEjBkB,QACI9F,EACAE,EACAI,GACAC,GACAH,GACAyC,IAEJvH,SACIyK,KAAM,OACNC,WACIC,MAAQC,SAAU,KAClBC,OAASD,SAAU,MAEvBE,YAAa,aACbjP,UAAW,UACXsO,aAAa,EACbY,cAAc,EACdC,WAAY,IACZC,YAAY,EACZC,eAAgBvP,GAEpBwP,UAAW,WACP,GAAI7B,GAAOrL,IACPqL,GAAK8B,aACL9B,EAAK+B,QAAQ,UAAW,WACpB,OAASC,SAAUhC,EAAKvJ,QAAQ5C,cAEpCmM,EAAKvJ,QAAQtC,QACb6L,EAAK5L,OAAO4L,EAAK8B,WAAWG,OAAQjC,EAAKvJ,SACzCuJ,EAAK+B,QAAQ,UAAW,WACpB,OAASC,SAAUhC,EAAKvJ,QAAQ5C,gBAI5C+M,0BAA2B,WAAA,GACnBZ,GAAOrL,KACP8B,EAAUuJ,EAAKvJ,QACfC,EAAUsJ,EAAKtJ,QACfc,EAAkBwI,EAAKkC,oBAC1B1K,GAAmBf,GAAS0L,GAAGvG,GAAa2B,GAAcxC,EAAMiF,EAAKoC,cAAepC,IAAOmC,GAAGxH,EAAQQ,EAAImC,IAAkB,GAAO6E,GAAGxH,EAAQQ,EAAIoC,GAAcxC,EAAMiF,EAAKqC,OAAQrC,IAAOmC,GAAGvG,GAAc,IAAMlB,EAAYS,EAAI,aAAcJ,EAAMiF,EAAKsC,cAAetC,IAAOmC,GAAGjG,GAAaf,EAAIqC,GAAwBzC,EAAMiF,EAAKuC,YAAavC,IAAOmC,GAAGhG,GAAahB,EAAIqC,GAAwBzC,EAAMiF,EAAKwC,YAAaxC,IAAOmC,GAAGzH,EAAYS,EAAIqC,GAAwBzC,EAAMiF,EAAKyC,WAAYzC,IAAOmC,GAAGlG,GAAad,EAAK,IAAMe,GAAaf,EAAK,IAAMgB,GAAahB,EAAK,IAAMT,EAAYS,EAAK,IAAMR,EAAQQ,EAAIsC,GAAc1C,EAAMiF,EAAK0C,aAAc1C,IACroBvJ,EAAQ0L,GAAG,UAAYhH,EAAIJ,EAAMiF,EAAK2C,SAAU3C,IAAOmC,GAAG,QAAUhH,EAAIJ,EAAMiF,EAAK4C,OAAQ5C,IAAOmC,GAAG,QAAUhH,EAAI,aAAcJ,EAAMiF,EAAK4C,OAAQ5C,IAAOmC,GAAG,OAAShH,EAAIJ,EAAMiF,EAAK6C,iBAAkB7C,IAAOmC,GAAG,OAAShH,EAAI,aAAcJ,EAAMiF,EAAK8C,oBAAqB9C,IACzQxI,GACAA,EAAgB2K,GAAGhG,GAAahB,EAAIiC,GAAerC,EAAMiF,EAAK+C,iBAAkB/C,IAAOmC,GAAGjG,GAAaf,EAAIiC,GAAerC,EAAMiF,EAAKgD,iBAAkBhD,IAEvJtJ,EAAQmK,cACRb,EAAKiD,sBAAwBlI,EAAMiF,EAAKkD,eAAgBlD,GACxD5N,EAAEwK,UAAUuG,MAAMnD,EAAKiD,yBAG/BG,0BAA2B,WAAA,GACnBpD,GAAOrL,KACP6C,EAAkBwI,EAAKkC,kBACvB1K,IACAA,EAAgB6L,IAAIlI,GAExB6E,EAAKvJ,QAAQ4M,IAAIlI,GACb6E,EAAKiD,uBACL7Q,EAAEwK,UAAU0G,OAAO,QAAStD,EAAKiD,wBAGzCtC,cAAe,SAAUjK,GAAV,GAGP6M,GAAaC,EAWTC,EACAC,EAdJ1D,EAAOrL,KACPgP,EAAsC,cAAvBjN,EAAQ8K,WAEvB9K,GAAQiL,aACR3B,EAAK4D,iBACL5D,EAAK6D,eAAiB7D,EAAKvJ,QAAQqN,KAAK,qCAAwCpN,EAAQ8K,YAAc,YAAajO,SAC/GoQ,GACA1J,EAAyB+F,EAAKvJ,SAElC8M,EAAcnR,EAAE4N,EAAK+D,UAAUC,cAAezR,UAAWoR,EAAe,OAAS,QACjFH,EAAapR,EAAE4N,EAAK+D,UAAUC,cAAezR,UAAWoR,EAAe,QAAU,UACjFJ,EAAYU,IAAIT,GAAYU,SAASlE,EAAK6D,gBAC1C7D,EAAKmE,eAAenE,EAAKvJ,QAAS8M,EAAaC,EAAYG,GACvDF,EAAezD,EAAKvJ,QAAQ2N,aAC5BV,EAAkB1D,EAAKvJ,QAAQ,GAAG4N,MAAMC,MAC5CZ,EAAsC,SAApBA,EAA6B,GAAKA,EAChDC,GACAvR,EAAE+H,QAAQgI,GAAG9F,GAAQnJ,EAAMqR,SAAS,WAChCvE,EAAKwE,yBAAyBf,EAAcC,GAC5C1D,EAAKyE,qBAAqBzE,EAAKvJ,QAAS8M,EAAaC,EAAYG,IAClE,MAEP3D,EAAKwE,yBAAyBf,EAAcC,GAC5C1D,EAAKyE,qBAAqBzE,EAAKvJ,QAAS8M,EAAaC,EAAYG,KAGzEzB,iBAAkB,WACd,MAAOvN,MAAKkP,gBAAkBlP,KAAK+P,gBAEvCF,yBAA0B,SAAUf,EAAcC,GAAxB,GAIlBiB,GAEAC,EACAC,EAEIP,EARJtE,EAAOrL,KACPmQ,EAAkB9E,EAAK6D,eAAekB,IAAI,QAC9C/E,GAAK6D,eAAekB,KAAMT,MAAO,KAC7BK,EAAe3E,EAAK6D,eAAeO,aACvCpE,EAAK6D,eAAekB,KAAMT,MAAOQ,IAC7BF,EAAY5E,EAAKvJ,QAAQ2N,aACzBS,EAAU7E,EAAKvJ,QAAQ,GAAGuO,YAAchF,EAAKvJ,QAAQ,GAAGwO,YACxDL,GAAaD,GAAgBA,EAAe,IACxCL,EAAQZ,EAAkB3K,KAAKmM,IAAIzB,EAAckB,GAAgBA,EACrE3E,EAAKvJ,QAAQ6N,MAAMA,EAAQO,GAC3B7E,EAAK6D,eAAeS,MAAMA,KAGlCa,gBAAiB,SAAUzO,GAAV,GACTsJ,GAAOrL,KACPyQ,EAAkB1O,EAAQiL,aAAe3B,EAAKtJ,QAAQiL,aAAejL,EAAQiL,YAAc3B,EAAKtJ,QAAQiL,YAAcjL,EAAQiL,YAAc3B,EAAKtJ,QAAQiL,YAAcjL,EAAQiL,WAAW0D,UAAYrF,EAAKtJ,QAAQiL,WAAW0D,UAAY3O,EAAQ8K,aAAexB,EAAKtJ,QAAQ8K,WAC9Q4D,KACApF,EAAKoD,4BACLpD,EAAKsF,mBACLtF,EAAKW,cAAcjK,GACnBsJ,EAAKY,8BAGb0E,iBAAkB,WAAA,GACVtF,GAAOrL,KACP6C,EAAkBwI,EAAKkC,kBACvB1K,KACAA,EAAgB6L,IAAIlI,GACpB3D,EAAgBnC,KAAKyI,IAAsBuF,IAAIlI,GAAI7F,SACnDkC,EAAgB3D,SAASuE,IAA4B7C,KAAK,SAAUwB,EAAGwO,GAAb,GAGlDC,GAFAtL,EAAK9H,EAAEmT,GAAc1R,SAASsJ,GAClCjD,GAAGmJ,IAAIjH,IACHoJ,EAAgBxN,EAAgBkC,EAAI1C,GACpCgO,EAAchR,QACdgR,EAAcpR,OAAOmR,KAG7B/N,EAAgBnC,KAAK8B,KAAuB7C,WAAW,oBACvDkD,EAAgBnC,KAAKgC,KAAsB/C,WAAW,cACtD0L,EAAKvJ,QAAQ4M,IAAIjH,IACjBhK,EAAE+H,QAAQkJ,IAAIhH,IACd7E,EAAgB9C,WAAW+Q,SAC3BzF,EAAK6D,eAAiB7D,EAAK0E,eAAiB1E,EAAK4D,cAAgBvR,IAGzE8R,eAAgB,SAAUuB,EAAenC,EAAaC,EAAYG,GAAlD,GACR3D,GAAOrL,KACPgN,EAAa3B,EAAKtJ,QAAQiL,WAC1B0D,EAAWjT,EAAEuT,UAAUhE,EAAW0D,UAAY1D,EAAW0D,SAAWxK,EACpE+K,EAAqBP,EAAW,EAChCQ,EAAW,KAAOR,EAClBS,EAAU,KAAOT,EACjBU,EAAiB,KAAkB,EAAXV,EACxBW,EAAgB,KAAkB,EAAXX,EACvBY,GAAY,EACZC,GAAc,EACd5M,EAAS,SAAU6M,GACnB,GAAIC,GAAczC,GAAiB0C,WAAcF,IAAYG,UAAaH,EAC1ET,GAAca,SAASC,QAAQJ,EAAa,OAAQ,SAAU,WACtDH,GACA3M,EAAO6M,KAGfnG,EAAKyE,qBAAqBiB,EAAenC,EAAaC,EAAYG,IAElE8C,EAAoB,SAAUhT,GACzBwS,GAAcC,IACf5M,EAAO7F,EAAEuD,KAAKzE,WACd0T,GAAY,IAGhBS,EAAmB,SAAUjT,GAC7B,GAAI2S,GAAczC,GAAiB0C,WAAc5S,EAAEuD,KAAKzE,YAAgB+T,UAAa7S,EAAEuD,KAAKzE,UAC5F2T,GAAcrM,EAAQpG,IAAM+F,EAAe/F,GAC3CiS,EAAciB,OAAOH,QAAQJ,EAAa,OAAQ,SAAU,WACnDF,GAGDlG,EAAKyE,qBAAqBiB,EAAenC,EAAaC,EAAYG,GAClEsC,GAAY,GAHZ7T,EAAEqB,EAAEmT,eAAeC,QAAQ3K,MAMnC+J,GAAY,EACZxS,EAAEqT,kBACFrT,EAAEsT,iBAENxD,GAAYpB,GAAGjG,GAAaf,GAAM5I,UAAWsT,GAAYY,GAAmBtE,GAAGjP,EAAM8T,SAASC,KAAO9L,GAAM5I,UAAWwT,GAAkBW,GACxIlD,EAAWrB,GAAGjG,GAAaf,GAAM5I,UAAWuT,GAAWW,GAAmBtE,GAAGjP,EAAM8T,SAASC,KAAO9L,GAAM5I,UAAWyT,GAAiBU,GACrInD,EAAYU,IAAIT,GAAYrB,GAAGhG,GAAahB,EAAI,WAC5CuK,EAAciB,OACdV,GAAY,EACZjG,EAAKyE,qBAAqBiB,EAAenC,EAAaC,EAAYG,KAEtE+B,EAAcvD,GAAG/F,GAAY,SAAU3I,GAAV,GAEjBqF,GACAoO,EACAf,EACAC,CAJH3S,GAAE0T,SAAY1T,EAAE2T,UAAa3T,EAAE4T,SAC5BvO,EAAaF,EAAgBnF,EAAEiG,eAC/BwN,EAAcnO,KAAKuO,IAAIxO,GAAc8M,EACrCO,GAASrN,EAAa,EAAI,KAAO,MAAQoO,EACzCd,EAAczC,GAAiB0C,WAAcF,IAAYG,UAAaH,GAC1EnG,EAAKuH,kBAAkB7B,GACvBA,EAAca,SAASC,QAAQJ,EAAa,OAAQ,SAAU,WAC1DpG,EAAKyE,qBAAqBiB,EAAenC,EAAaC,EAAYG,KAEtElQ,EAAEsT,qBAIdtC,qBAAsB,SAAUiB,EAAenC,EAAaC,EAAYxQ,GAAlD,GACdwU,GAAgBxU,EAAa0S,EAAcW,aAAeX,EAAcY,YACxEmB,EAAazU,EAAauJ,GAAcC,GACxCkL,EAAS1U,EAAayJ,GAAcC,EACxC6G,GAAYoE,OAAyB,IAAlBH,GACnBhE,EAAWmE,OAAOH,EAAgB9B,EAAc,GAAG+B,GAAc/B,EAAc,GAAGgC,GAAU,IAEhGE,WAAY,SAAUlR,GAClB,GAAI0K,GAAYzM,KAAK+B,QAAQ0K,SAC7BzM,MAAK6L,YAAY9J,GACjBA,EAAQ0K,UAAYtG,GAAO,EAAMsG,EAAW1K,EAAQ0K,WAChD,cAAgB1K,IAChB/B,KAAK2L,YAAY5J,GAErB/B,KAAK4L,iBACL5L,KAAKwQ,gBAAgBzO,GACrBwE,EAAO+E,GAAG2H,WAAW1H,KAAKvL,KAAM+B,IAEpCmR,QAAS,WACL,GAAI7H,GAAOrL,IACXuG,GAAO+E,GAAG4H,QAAQ3H,KAAKF,GACvBA,EAAKoD,4BACLpD,EAAKsF,mBACLpS,EAAM2U,QAAQ7H,EAAKvJ,UAEvBqR,OAAQ,SAAUrR,EAASqR,GAEvB,MADAnT,MAAKoT,gBAAgBtR,EAASqR,KAAW,GAClCnT,MAEXqT,QAAS,SAAUvR,GAEf,MADA9B,MAAKoT,gBAAgBtR,GAAS,GACvB9B,MAEXsT,eAAgB,SAAUC,GAAV,GAERvU,GACA6D,CACJ,OAHA0Q,GAAYA,GAAavT,KAAK8B,QAC1B9C,EAAOgB,KAAK8B,QAAQpB,KAAK6S,GACzB1Q,EAAkB7C,KAAKuN,mBACvBvO,EAAKa,QAAU0T,IAAcvT,KAAK8B,QAC3B9C,EACA6D,EACAA,EAAgBnC,KAAK6S,GAErB9V,KAGfgC,OAAQ,SAAUT,EAAMwU,GACpBA,EAAgBxT,KAAKsT,eAAeE,EACpC,IAAIC,GAAWzT,KAAK0T,QAAQ1U,EAAMwU,EAAeA,EAAc3T,OAAS2T,EAAc9S,KAAK,6DAA+D,KAS1J,OARAE,GAAK6S,EAAStR,MAAO,SAAUC,GAC3BqR,EAAS9R,MAAMlC,OAAOO,MACtBQ,EAAYR,MACZ6B,EAA4B7B,KAAMhB,EAAKoD,IAAMpD,KAEjDwB,EAAYgT,GACZ/S,EAAgBgT,EAAS9R,MAAMjB,KAAK,qBAAqB4O,IAAImE,EAAStR,QACtEd,EAAmBK,EAAiB+R,EAAS9R,QACtC3B,MAEX2T,aAAc,SAAU3U,EAAMwU,GAC1BA,EAAgBxT,KAAKsT,eAAeE,EACpC,IAAIC,GAAWzT,KAAK0T,QAAQ1U,EAAMwU,EAAeA,EAAc5U,SAQ/D,OAPAgC,GAAK6S,EAAStR,MAAO,SAAUC,GAC3BoR,EAAcI,OAAO5T,MACrBQ,EAAYR,MACZS,EAAgBT,MAChB6B,EAA4B7B,KAAMhB,EAAKoD,IAAMpD,KAEjDyB,EAAgB+S,GACTxT,MAEX6T,YAAa,SAAU7U,EAAMwU,GACzBA,EAAgBxT,KAAKsT,eAAeE,EACpC,IAAIC,GAAWzT,KAAK0T,QAAQ1U,EAAMwU,EAAeA,EAAc5U,SAQ/D,OAPAgC,GAAK6S,EAAStR,MAAO,SAAUC,GAC3BoR,EAAcM,MAAM9T,MACpBQ,EAAYR,MACZS,EAAgBT,MAChB6B,EAA4B7B,KAAMhB,EAAKoD,IAAMpD,KAEjDyB,EAAgB+S,GACTxT,MAEX0T,QAAS,SAAU1U,EAAMwU,EAAe5U,GAA/B,GACYuD,GAAO4R,EAIpBC,EAA8EC,EAJ9E5I,EAAOrL,IAuCX,OAtCKwT,IAAkBA,EAAc3T,SACjCjB,EAASyM,EAAKvJ,SAEdkS,EAAQvW,EAAEyW,cAAclV,IAASA,YAAgBT,GAAM8D,KAAK8R,iBAAkBF,GAC1EhK,WAAYrL,EAAOoC,SAASC,GAC5B5C,WAAYO,EAAOoC,SAASC,EAAO,eACnC+J,UAAU,EACVnL,OAAQjB,EAAOM,WAAWW,QAE9B2T,IAAkB5U,EAAOiB,SACzBjB,EAASnB,EAAE4N,EAAK+I,aACZzS,MAAOsS,EACPlS,QAASsJ,EAAKtJ,WACdwN,SAASiE,IAEbQ,GAASnO,EAAQ7G,IAASA,YAAgBT,GAAM8D,KAAKgS,gBACrDlS,EAAQ1E,EAAEA,EAAE6W,IAAIN,GAAShV,GAAQA,EAAM,SAAUwS,EAAO+C,GACpD,MAAqB,gBAAV/C,GACA/T,EAAE+T,GAAOgD,MAET/W,EAAE4N,EAAKoJ,YACV9S,MAAOsS,EACPjV,KAAMmH,EAAOqL,GAASjQ,MAAOgT,OAC7BC,UAKRrS,EADe,gBAARnD,IAAsC,KAAlBA,EAAK0V,OAAO,GAC/BrJ,EAAKvJ,QAAQpB,KAAK1B,GAElBvB,EAAEuB,GAEd+U,EAAS5R,EAAMzB,KAAK,QAAQzB,SAAS,gBAAgBW,KAAK,OAAQ,QAClEuC,EAAQA,EAAM7C,OAAO,MACrB6C,EAAMmN,IAAIyE,EAAOrT,KAAK,SAASE,KAAK,WAChC7B,EAAkBiB,UAItBmC,MAAOA,EACPR,MAAO/C,IAGf+B,OAAQ,SAAUmB,GAAV,GAEAuJ,GAAazM,EAA+D+C,EAGxEgT,EACAC,CAaR,OAlBA9S,GAAU9B,KAAKsT,eAAexR,GAC1BuJ,EAAOrL,KAAMpB,EAASkD,EAAQF,aAAayJ,EAAKvJ,QAAS4G,IAAmB/G,EAAQG,EAAQlD,OAAO,mBACvGkD,EAAQnB,SACJgB,IAAUA,EAAMzC,SAASwJ,IAAkB7I,SACvC8U,EAAcjT,EAAiBC,GAC/BiT,EAAYjT,EAAM/C,OAAO6E,IACzBmR,EAAU/U,OACV+U,EAAUjU,SAEVgB,EAAMhB,SAEVU,EAAmBsT,IAEnB/V,EAAOiB,SACPjB,EAASA,EAAO6C,GAAG,GACnBjB,EAAY5B,GACZ6B,EAAgB7B,IAEbyM,GAEXwJ,eAAgB,SAAU/S,EAASgT,GAC/B,GAAIzJ,GAAOrL,IACP8U,GAASC,UACT1J,EAAKqB,KAAK5K,GACVuJ,EAAK2J,UAAW,GAEhBF,EAASG,IAAI7N,GAAQ,WACjBtF,EAAQpB,KAAKgG,GAAexF,YAAY,eACpCmK,EAAK2J,WACL3J,EAAKqB,KAAK5K,GACVuJ,EAAK2J,UAAW,MAKhCtI,KAAM,SAAU5K,GAAV,GAQEgT,GAeAI,EACAC,EAvBA9J,EAAOrL,KACP+B,EAAUsJ,EAAKtJ,QACf1D,EAAoC,cAAvB0D,EAAQ8K,YACrBjP,EAAYmE,EAAQnE,UACpBK,EAAQM,EAAMwC,QAAQ9C,MAAMoN,EAAKG,SACjC3I,EAAkBwI,EAAKkC,kBAG3B,OAFAzL,IAAWe,GAAmBwI,EAAKvJ,SAASpB,KAAKoB,GAC7CgT,EAAWzJ,EAAK8B,YAAc9B,EAAK8B,WAAWiI,SAAStT,EAAQO,KAAK,QACpEyS,GAAYA,EAASO,cAAgBP,EAASC,WAAa1J,EAAK2J,UAChE3J,EAAK2J,UAAW,EAChBlT,EAAQpB,KAAKgG,GAAezH,SAAS,eACrC6V,EAASQ,OACTjK,EAAKwJ,eAAe/S,EAASgT,GAC7B,IAEA,yBAAyBxW,KAAKV,KAE1BA,EADAK,EACYI,GAAcT,EAAY,SAASG,QAAQ,UAAW,UAAY,OAElEM,GAAcT,EAAY,UAAUG,QAAQ,UAAW,UAAY,SAGnFmX,EAAgB,6DAChBC,EAAa,WACb,GAAIjS,GAAQzF,EAAEuC,MAAMqC,KAAK8F,GACrBjF,IACAmI,EAAKuB,MAAMnP,EAAEuC,MAAMuV,QAAQ,cAAc,IAGjDzT,EAAQ0T,WAAW9U,KAAKwU,GAAetU,KAAKuU,GACxCtS,GACAf,EAAQpB,KAAKwU,GAAetU,KAAKuU,GAEjC9J,EAAKtJ,QAAQmK,cACbb,EAAKc,SAAU,GAEnBrK,EAAQlB,KAAK,WACT,GAAI6U,GAAKhY,EAAEuC,KACX0V,cAAaD,EAAGpT,KAAKuE,IACrB6O,EAAGpT,KAAKuE,EAAO+O,WAAW,WAAA,GAElBzS,GACA0S,EAaQC,EAAmCC,EAkBvCC,EAMAlY,EAAmCmY,EAAuCxX,EAAqDyX,EAA0CC,EAvC7K3Q,EAAKkQ,EAAG/U,KAAK,+BAGZ6E,EAAG,IAAM1C,IACV+S,EAAgBvK,EAAK8K,UAAUV,GAC/BlQ,EAAKqQ,GAAiBA,EAAc9T,SAEpCyD,EAAGxB,GAAG,aAGNwB,EAAG,IAAM8F,EAAK+K,eACVpX,KAAMyW,EAAG,GACTpQ,KAAMoB,OACH,KACFlB,EAAG7E,KAAK,iBAAiB,IAAM6E,EAAGrG,SAAS,WAAWW,OAAS,GAC5DgW,EAAepY,EAAE+H,QAAQ6Q,SAAUP,EAAe,WAC9CvQ,EAAG6K,KACCkG,UAAWT,GAAgBtX,EAAMgY,aAAahR,GAAMA,EAAG8Q,UAAY9X,EAAMiY,WAAWjR,GAAIkR,OACxFC,SAAU,UAGlBnY,EAAMwC,QAAQ4V,QAAQC,MAAQrY,EAAMwC,QAAQ4V,QAAQE,SAAW,EAC/DlB,WAAWG,EAAc,GAEzBA,KAGJvQ,EAAG6K,KACCkG,UAAW,GACXI,SAAU,KAGlBjB,EAAGpT,KAAKyE,GAAQ2O,EAAGrF,IAAItJ,KACnBiP,EAAa1K,EAAKS,iBACtB2J,EAAGrF,IAAItJ,GAAQiP,GACX1K,EAAKtJ,QAAQiL,YACbyI,EAAG7W,SAAS4W,SAASrM,IAAsBiH,KAAM0G,SAAUf,IAE/D7S,EAAQqC,EAAGlD,KAAK8F,IACZtK,EAAO4X,EAAG7W,SAASoC,SAASC,GAAO+U,EAAmBnY,GAAQQ,EAAYG,EAAaR,EAAeJ,EAAWC,EAAMI,GAAQgY,EAAUlU,EAAQ0K,UAAUC,KAAKuJ,QAASC,EAAcD,IAAYvY,EAAYuY,EAAU,WAAatY,EAAmBC,EAAWC,GACnQqF,GAuDDA,EAAQqC,EAAGlD,KAAK8F,IAChBjF,EAAMnB,QAAQ5D,OAASK,EAAWL,OAClC+E,EAAMnB,QAAQ3D,SAAWI,EAAWJ,SACpC8E,EAAMnB,QAAQ0K,UAAUC,KAAKuJ,QAAUC,GAzDvChT,EAAQqC,EAAGwR,YACPC,SAAU,WACN3L,EAAK+K,eACDpX,KAAMgB,KAAKwL,QAAQ5M,SACnByG,KAAM0B,MAGdkQ,WAAY,SAAUnY,GAClBuM,EAAK6L,UAAW,EAChBpY,EAAEqY,OAAOrV,QAAQsV,WAAW,mBAAmBhH,KAAMiH,QAAS,KAC9DhM,EAAK+K,eACDpX,KAAMgB,KAAKwL,QAAQ5M,SACnByG,KAAM2B,MAGd7I,OAAQK,EAAWL,OACnBC,SAAUI,EAAWJ,SACrBkZ,UAAWvV,EAAQkL,iBAAmBvP,EAAYqE,EAAQkL,eAAiB+I,EAAmB,MAAQ,WACtGuB,OAAQ9B,EACRlG,SAAU1M,GAAmB4S,EAC7BhJ,WACIC,KAAMvG,GAAO,GAAQ8P,QAASC,GAAenU,EAAQ0K,UAAUC,MAC/DE,MAAO7K,EAAQ0K,UAAUG,OAE7BF,KAAMtG,EAAMiF,EAAKmM,WAAYnM,GAC7BuB,MAAO,SAAU9N,GAAV,GAEC2W,GAEIxS,CAHRoI,GAAK6L,UAAW,EACZzB,EAAK3W,EAAEqY,OAAO3L,QAAQ5M,SACtBiE,IACII,EAAUnE,EAAEqY,OAAOrV,QAAQO,KAAKkB,IAChCN,IACAwS,GAAM5S,GAAmBwI,EAAKvJ,SAASpB,KAAK8B,EAAoBS,KAEpEnE,EAAEqY,OAAO3L,QAAQtM,SAASiK,IAAsBsO,QAE/CpM,EAAK+K,eACFpX,KAAMyW,EAAG,GACTpQ,KAAMsB,IAYV7H,EAAEsT,kBAVFqD,EAAGrF,IAAItJ,GAAQ2O,EAAGpT,KAAKyE,KACvB2O,EAAG2B,WAAWtQ,IACVuE,EAAKtJ,QAAQiL,YACbyI,EAAG7W,SAAS4W,SAASrM,IAAsBiH,KAAM0G,OAAQ,MAEzD1R,GAASN,IAAevG,EAAMwC,QAAQ2W,wBACtCjC,EAAGvU,YAAYkH,IACfiD,EAAK6C,wBAMlB7L,KAAK8F,IAOZ5C,EAAG5F,WAAW,eACd0L,EAAKsM,wBAAwBzU,EAAOuS,GACpCvS,EAAM0U,UAAW,EACjB1U,EAAMwJ,OACNrB,EAAKwM,oBAAoB3U,KAE9BmI,EAAKtJ,QAAQgL,eAEb1B,IAEXsM,wBAAyB,SAAUzU,EAAO4U,GAAjB,GAKTC,GAJR1M,EAAOrL,IACPqL,GAAKtJ,QAAQiL,aACb3B,EAAK2M,kBAAkB9U,GAClB4U,EAAYlY,KAAK,sBACdmY,GAAU,GAAIE,OAAOC,UACzBJ,EAAYlY,KAAK,mBAAoBmY,GACrC7U,EAAMpB,QAAQlC,KAAK,aAAcmY,MAI7CC,kBAAmB,SAAU9U,GACpBA,EAAMpB,QAAQlD,SAASmF,GAAGN,MAC3BP,EAAMsI,QAAUjN,EAAM4Q,KAAKjM,EAAMpB,QAASoB,EAAMnB,QAAQoW,UAAU/H,KAC9DsG,SAAU,SACV0B,QAAS,QACTha,SAAU,eAItByZ,oBAAqB,SAAU3U,EAAO8L,EAAcqJ,GAChD,GAAIhN,GAAOrL,IACPqL,GAAKtJ,QAAQiL,YAAc9J,EAAMpB,QAAQ,GAAGwW,aAAepV,EAAMpB,QAAQ,GAAGyW,cAC5ElN,EAAKmN,wBAAwBtV,EAAO8L,EAAcqJ,IAG1DG,wBAAyB,SAAUtV,EAAO8L,EAAcqJ,GAA/B,GACjBhN,GAAOrL,KACPyY,EAAgBvV,EAAMsI,QAAQtM,SAASiK,IACvCsD,EAAYpB,EAAKtJ,QAAQ0K,UACzBiM,GAAWjM,GAAaA,EAAUC,MAAQD,EAAUC,KAAKC,UAAY,GAAK1G,CAC9E0P,YAAW,WAAA,GAEC/G,GACAC,CAFH4J,GAAc5Y,SACX+O,EAAcnR,EAAE4N,EAAK+D,UAAUC,cAAezR,UAAWoR,EAAe,OAAS,QACjFH,EAAapR,EAAE4N,EAAK+D,UAAUC,cAAezR,UAAWoR,EAAe,QAAU,UACrFyJ,EAAgB7J,EAAYU,IAAIT,GAAYU,SAASrM,EAAMsI,SAC3DH,EAAKmE,eAAetM,EAAMpB,QAAS8M,EAAaC,EAAYG,GACvDqJ,GACDI,EAAcjL,GAAGjG,GAAaf,EAAI,WAC9B,GAAI3D,GAAkBwI,EAAKkC,kBAC3B9P,GAAEkF,EAAeO,EAAMpB,QAASe,IAAkBjC,KAAK,SAAUwB,EAAGuW,GAChE,GAAIb,GAAcjV,EAAgBnC,KAAK8B,EAAoBmW,EAAEtW,KAAKkB,KAClE8H,GAAKuB,MAAMkL,OAEhBtK,GAAGhG,GAAahB,EAAI,WACnBmP,WAAW,WACHlY,EAAEmb,cAAcvN,EAAK4D,gBACrB5D,EAAKwN,mBAAmB3V,EAAMpB,UAEnCmE,MAIfoF,EAAKyE,qBAAqB5M,EAAMpB,QAAS2W,EAAcK,QAASL,EAAc7U,OAAQoL,IACvF0J,IAEPlB,WAAY,SAAU1Y,GACbkB,KAAK+Y,eACNja,EAAEqY,OAAOrV,QAAQ5C,SAAS,IAAMmJ,IAAcnH,YAAYmH,IAE1DrI,KAAK+B,QAAQiL,YACbhN,KAAKgZ,gBAAgBla,EAAEqY,SAG/B6B,gBAAiB,SAAU9V,EAAO+V,GAAjB,GAITC,GACArD,EACAsD,EACAC,EACAzH,EACA0H,EACA/C,EACAgD,EAEIjD,EAZJ/S,EAAeJ,EAAMpB,QACrByX,EAASjW,EAAagM,IAAIhM,EAAa1E,OAAO6E,IAClD8V,GAAOlD,OAAO/S,EAAatC,SAASC,IAASjB,KAAKwZ,gBAAkB,IAChEN,EAAWhW,EAAMuW,UAAUR,GAC3BpD,EAAepY,EAAE+H,QAAQ6Q,SACzB8C,EAAmBD,EAAS7C,OAC5B+C,EAAiBH,EAAU,EAAI7U,KAAKsV,IAAIR,EAASS,IAAK,GACtDhI,EAAYsH,EAAU,EAAIxU,EAAczE,KAAKuN,mBAAmB,GAAI,aACpE8L,EAAkB7T,OAAOoU,YAAc/D,EACvCS,EAAYT,EAAetX,EAAMiY,WAAWlT,GAAcmT,OAAS4C,EACnEC,EAAShD,EAAY3E,EAAYwH,EAAmBC,EACnDE,IACGjD,EAASjS,KAAKmM,IAAI+F,EAAWA,EAAY8C,EAAiBzH,GAC9D4H,EAAOnJ,KACHsG,SAAU,SACVL,OAAQA,EAAS,SAI7BzJ,MAAO,SAAUzK,EAAO0X,GAAjB,GAQCC,GAWAC,EAlBA1O,EAAOrL,KACP6C,EAAkBwI,EAAKkC,mBACvBzL,EAAUe,GAAmBwI,EAAKvJ,OA0CtC,OAzCAK,GAAQL,EAAQpB,KAAKyB,GAChBA,EAAMtC,SACPsC,EAAQL,EAAQpB,KAAK,aAErBoZ,EAAwB,SAAUlX,GAClC,GAAIG,IAAS,CACb,OAAItF,GAAEmb,cAAcvN,EAAK4D,eACdlM,GAEXtF,EAAEkF,EAAeC,EAAcC,IAAkBjC,KAAK,SAAUwB,EAAGc,GAE/D,MADAH,KAAWsI,EAAK4D,cAAc/L,GAAAA,EAAMb,KAAKkB,MACjCR,IAELA,IAEPgX,EAAqB,SAAU/W,GAC/B,GAAI+U,GAAU/U,EAAOX,KAAKc,GAC1B,QAAQN,IAAoBkV,IAAY1M,EAAK4D,cAAc8I,GAAAA,IAE/D5V,EAAMvB,KAAK,WACP,GAAI6U,GAAKhY,EAAEuC,OACN6Z,GAAkBxO,EAAK2O,YAAYvE,KACpCpK,EAAKc,SAAU,GAEnBuJ,aAAaD,EAAGpT,KAAKuE,IACrB6O,EAAGpT,KAAKuE,EAAO+O,WAAW,WACtB,GAAIzS,GAAQmI,EAAK8K,UAAUV,EAC3B,IAAIvS,IAAU6W,EAAmBtE,IAAOpK,EAAK4O,aAAc,CACvD,IAAK5O,EAAK4O,aAAeH,EAAsB5W,EAAMpB,SACjD,MAEJoB,GAAM0J,QACN1J,EAAMpB,QAAQlC,KAAK,eAAe,GAC9BiD,GACIwI,EAAK4O,aAAe9X,EAAMyB,OAAOG,GAAG0R,EAAG,WAChCpK,GAAK4O,cAIzB5O,EAAKtJ,QAAQgL,eAEb1B,GAEX8K,UAAW,SAAUV,GAAV,GAKCsC,GAEIzU,EANR+H,EAAOrL,KACPkD,EAAQuS,EAAG/U,KAAK,iFAAiF2B,KAAK8F,IACtGtF,EAAkBwI,EAAKkC,kBAQ3B,QAPKrK,GAASL,IACNkV,EAAUtC,EAAGpT,KAAKc,IAClB4U,IACIzU,EAAeT,EAAgBnC,KAAKgC,EAAmBqV,IAC3D7U,EAAQI,EAAajB,KAAK8F,MAG3BjF,GAEXkQ,gBAAiB,SAAUjR,EAAOgR,GAC9BnT,KAAK8B,QAAQpB,KAAKyB,GAAOvB,KAAK,WAC1BnD,EAAEuC,MAAMka,YAAY3a,GAAc4T,GAAQ+G,YAAYxa,IAAgByT,GAAQvT,KAAK,iBAAkBuT,MAG7GpF,aAAc,SAAUjP,GACpB,GAAIqb,GAAS1c,EAAEc,EAAM6b,YAAYtb,IAAMA,EAAEqb,QAAQ5E,QAAQ7M,IAAmB2R,EAAUvb,EAAEuG,MAAQkC,IAAcxB,EAAUuU,QAAQxb,EAAEuG,UAClI8U,GAAO3E,WAAWtU,YAAYkH,IACzB+R,EAAO7Y,QAAQ,MAAQ5B,IAAeG,QACvCsa,EAAOD,YAAY9R,GAAYiS,GAAqB,aAAVvb,EAAEuG,MAAiC,eAAVvG,EAAEuG,MAAyBvG,EAAEuG,MAAQiC,IAE5GtH,KAAKkO,oBAETP,cAAe,WACN3N,KAAK+B,QAAQ+K,eACd9M,KAAKua,mBAAoB,IAGjCpM,oBAAqB,SAAUrP,GAC3B,GAAIuM,GAAOrL,KAAMwa,EAAY/c,EAAEqB,EAAIA,EAAEmT,cAAgBjS,KAAKya,cAAeN,EAAS9O,EAAKqP,gBAAgBF,GAAW,EAC7Gxa,MAAKua,mBACN5E,WAAW,WACF1N,SAAS0S,aAAehc,EAASwb,EAAQ5b,EAAMoH,oBAAqB7G,GAAMH,EAASwb,EAAQrb,EAAEmT,iBAC9F5G,EAAKuB,MAAMuN,IAEhB,GAEPna,KAAKua,mBAAoB,GAE7BrM,iBAAkB,WACd,GAAI0M,GAAe5a,KAAKya,YACpBG,IAAgBA,EAAa5Z,SAASqH,MACtCuS,EAAa1Z,YAAYmH,IACzBrI,KAAK6a,cAAgB,OAG7BjP,eAAgB,WACZ,GAA8EzJ,GAA1EL,EAAU9B,KAAK8B,QAASgZ,EAA2B,qBACvDhZ,GAAQZ,YAAY,qCACpBY,EAAQ7C,SAAS,yCAA2CgC,GAAMhC,SAASgC,EAAO,IAAMjB,KAAK+B,QAAQ8K,aACrG/K,EAAQpB,KAAK,WAAWpB,OAAO,WAC3B,OAAQf,EAAMwC,QAAQga,gBAAgBxP,KAAKvL,KAAM8a,KAClD7b,SAAS,wBAAwBW,KAAK,OAAQ,QAAQA,KAAK,cAAekC,EAAQiC,GAAG,aAAanF,OAAO,MAAMgB,KAAK,gBAAiB,QAAQob,MAAMta,KAAK,YAAYzB,SAAS,aAAaW,KAAK,WAAY,MAC9MuC,EAAQL,EAAQpB,KAAK,2BACrBoB,EAAQZ,YAAY,eACpBiB,EAAMvB,KAAK,WACP7B,EAAkBiB,SAG1B4N,YAAa,SAAU9O,GAAV,GACLuM,GAAOrL,KACP8B,EAAUrE,EAAEqB,EAAEmT,eACdoD,EAAchK,EAAK4P,iBAAiBnZ,GACpCmB,EAAUnB,EAAQO,KAAKc,KAAsBrB,EAAQlD,SAASyD,KAAKkB,IACnE2X,EAAerW,EAAe/F,EAC9BmE,KACAoI,EAAK4D,cAAchM,GAAAA,IAAsB,GAEzCoI,EAAK6L,UAAYpY,EAAEqc,gBAAkBrZ,EAAQR,QAAQiH,IAAc,IAAMzJ,EAAEqc,gBAAkBrZ,EAAQR,QAAQ,4CAA4C,KAG7J+J,EAAK0N,eAAgB,EACjB1N,EAAKtJ,QAAQmK,YAAYkP,eAAiB/P,EAAK2O,YAAYlY,EAAQyT,QAAQ7M,MAAsB2C,EAAKtJ,QAAQmK,YAAYmP,eAAiBhQ,EAAK2O,YAAYlY,EAAQyT,QAAQ7M,SAG3K2C,EAAKtJ,QAAQmK,eAAgB,GAASb,EAAKtJ,QAAQmK,YAAYkP,iBAAkB,GAAS/P,EAAK2O,YAAYlY,EAAQyT,QAAQ7M,MAAsB2C,EAAKtJ,QAAQmK,YAAYmP,gBAAiB,IAAUhQ,EAAK2O,YAAYlY,EAAQyT,QAAQ7M,MAAsB2C,EAAKc,UAAa/G,GAAW8V,GAAgB7P,EAAK2O,YAAYlY,EAAQyT,QAAQ7M,OACtU/J,EAASG,EAAEmT,cAAenT,EAAEwc,gBAAkBjG,GAC/ChK,EAAKqB,KAAK5K,IAGduJ,EAAKtJ,QAAQmK,eAAgB,GAAQb,EAAKc,SAAW/G,IACrDtD,EAAQ0T,WAAW5U,KAAKwF,EAAM,SAAUmV,EAAGC,GACvCnQ,EAAKuB,MAAM4O,GAAS,IACrBnQ,OAGXyC,WAAY,SAAUhP,GAAV,GACJuM,GAAOrL,KACP8B,EAAUrE,EAAEqB,EAAEmT,gBACd5G,EAAKtJ,QAAQmK,YAAYmP,eAAiBhQ,EAAK2O,YAAYlY,IAAYsD,IACvEtD,EAAQ0T,WAAW5U,KAAKwF,EAAM,SAAUmV,EAAGC,GACvCnQ,EAAKuB,MAAM4O,GAAS,IACrBnQ,KAGXwC,YAAa,SAAU/O,GAAV,GACLuM,GAAOrL,KACP8B,EAAUrE,EAAEqB,EAAEmT,eACd6F,EAAchW,EAAQO,KAAKc,IAC3BkS,EAAcvT,EAAQ5C,SAASuE,IAA4B5D,QAAUiC,EAAQ5C,SAASsJ,IAAe3I,QAAUiY,EAC/G2D,EAAUhe,EAAE+H,OAIhB,OAHIsS,UACOzM,GAAK4D,cAAc6I,GAAAA,GAE1BhW,EAAQF,aAAa6B,GAA4B,2CAA2C,IAC5F3E,EAAE4c,2BACF,GAECrQ,EAAKtJ,QAAQmK,eAAgB,IAAUb,EAAKtJ,QAAQmK,YAAYkP,gBAAiB/P,EAAK2O,YAAYlY,MAAauJ,EAAKtJ,QAAQmK,YAAYmP,cAAiBhQ,EAAK2O,YAAYlY,KAAcsD,GAAUP,EAAe/F,IAAOH,EAASG,EAAEmT,cAAenT,EAAEwc,eAAiBxc,EAAEqb,UAAW9E,GAAgB1W,EAASG,EAAEmT,cAAe1T,EAAMoH,oBAKnUpH,EAAMwC,QAAQ4V,QAAQC,OAAS9X,EAAE6c,YAAc7c,EAAEwc,gBAAkBzW,EAAe/F,IAAMA,EAAE8c,QAAU,GAAK9c,EAAE+c,QAAU,GAAK/c,EAAE+c,QAAUJ,EAAQpF,UAAYvX,EAAE8c,QAAUH,EAAQ9L,UAC9KtE,EAAKuB,MAAM9K,GADf,IAJIuJ,EAAKuB,MAAM9K,GAAS,GACpBuJ,EAAK2J,UAAW,EAChB,IAMR3G,iBAAkB,SAAUvP,GAAV,GAOVmE,GANAoI,EAAOrL,KACPsD,EAAe7F,EAAEqB,EAAEmT,cACnB3O,GAAa1E,SAASmF,GAAGN,MAG7BH,EAAeA,EAAapE,SAAS,MACjC+D,EAAUK,EAAajB,KAAKkB,IAC5BN,IACAoI,EAAK4D,cAAchM,GAAAA,IAAsB,KAGjDmL,iBAAkB,SAAUtP,GAAV,GACVuM,GAAOrL,KACPsD,EAAe7F,EAAEqB,EAAEmT,gBAClBpN,EAAe/F,IAAMwE,EAAaS,GAAGN,KACtC4H,EAAKyQ,aAAaxY,EAAapE,SAAS,QAGhD4c,aAAc,SAAUC,GAAV,GAMFC,GALJ3Q,EAAOrL,KACP6C,EAAkBwI,EAAKkC,mBACvBtK,EAAU8Y,EAAU1Z,KAAKkB,GACzBN,WACOoI,GAAK4D,cAAchM,GAAAA,GACtB+Y,EAAcnZ,EAAgBnC,KAAK8B,EAAoBS,IAC3D0S,WAAW,WACP,GAAItK,EAAKtJ,QAAQmK,YACbb,EAAKuH,kBAAkBmJ,OAEvB,IAAIte,EAAEmb,cAAcvN,EAAK4D,eAAgB,CACrC,GAAIgN,GAAa5Q,EAAK6Q,YAAYH,EAClC1Q,GAAKwN,mBAAmBoD,OAExB5Q,GAAKuB,MAAMoP,GAAa,IAGjC,KAGXpJ,kBAAmB,SAAUjP,GAAV,GACX0H,GAAOrL,KACP6C,EAAkBwI,EAAKkC,kBAC3B9P,GAAEkF,EAAegB,EAASd,IAAkBjC,KAAK,WAC7C,GAAIkX,GAAcjV,EAAgBnC,KAAK8B,EAAoBxC,KAAKqC,KAAKkB,KACrE8H,GAAKuB,MAAMkL,GAAa,MAGhCoE,YAAa,SAAUvY,GAAV,GACLd,GAAkB7C,KAAKuN,mBACvBgM,EAAS5W,EAAegB,EAASd,EACrC,OAAO0W,GAAOA,EAAO1Z,OAAS,IAAM8D,GAExCkV,mBAAoB,SAAUlV,GAAV,GACZ0H,GAAOrL,KACP6C,EAAkBwI,EAAKkC,mBACvBtK,EAAUU,EAAQtB,KAAKkB,IACvBuU,EAAcjV,EAAgBnC,KAAK8B,EAAoBS,GAG3D,KAFAA,EAAU6U,EAAYlZ,SAASyD,KAAKkB,IACpC8H,EAAKuB,MAAMkL,GAAa,GACjB7U,IAAYoI,EAAK4D,cAAchM,KAC9B6U,EAAYlZ,SAASmF,GAAGwE,KAG5BuP,EAAcjV,EAAgBnC,KAAK8B,EAAoBS,IACvDoI,EAAKuB,MAAMkL,GAAa,GACxB7U,EAAU6U,EAAYlZ,SAASyD,KAAKkB,KAG5CmK,OAAQ,SAAU5O,GAEd,IAFI,GACaqd,GAA4YC,EAAYC,EAA+RC,EAiBhsBC,EAUAvd,EA3BJqM,EAAOrL,KAAkB+B,EAAUsJ,EAAKtJ,QAASoY,EAAS1c,EAAEc,EAAM6b,YAAYtb,IAAK0d,EAAgBrC,EAAO,GAAIla,EAAWka,EAAO,GAAKA,EAAO,GAAGla,SAASwc,cAAgB,GAAIC,EAAuB,SAAZzc,GAAmC,UAAZA,GAAoC,UAAZA,GAAoC,SAAZA,EAAqB0c,EAAOxC,EAAO5E,QAAQzV,GAAgBgC,EAAUqY,EAAO5E,QAAQ7M,IAAmBkU,EAAc9a,EAAQ,GAAI+a,EAAOF,EAAK/c,KAAK,QAAwCkd,EAAa3C,EAAOva,KAAK,QAASmd,EAAatf,EAAE,kBAAoBmC,KAAK,QAASod,IAAWH,GAAQA,IAASE,EAAYE,EAAcD,KAAYH,EAAK3c,MAAM,MAAOgd,IAAiBJ,GAAcA,IAAeC,EAAYla,EAAkBwI,EAAKkC,mBAC7qBiP,GAAiBA,EAAc1Y,YAAc8Y,GAChDJ,EAAgBA,EAAc1Y,UAElC,KAAIrG,EAAE+e,GAAezY,GAAGmF,IAAxB,CAGA,GAAIpH,EAAQd,SAAStB,IAEjB,MADAZ,GAAEsT,iBACF,CAeJ,IAbKtT,EAAEqe,UAAW9R,EAAK+R,eAAejD,EAAQyC,IAAiBF,GAC3D5d,EAAEsT,iBAENtT,EAAEqe,SAAU,EACZf,EAAata,EAAQ5C,SAASuJ,IAC1B5F,IACI0Z,EAAeza,EAAQO,KAAKc,IAC5BoZ,IACAH,EAAavZ,EAAgBnC,KAAKgC,EAAmB6Z,MAG7DF,EAAoBD,EAAWrY,GAAG,YAClCuY,EAAyBva,EAAQmK,aAAemQ,GAAqBhR,EAAK2O,YAAYlY,GAClFC,EAAQ+K,gBAAkBkQ,GAAUC,MAAkBb,EAAWvc,QAAUyc,GAU3E,MATAxa,GAAQZ,YAAYkH,IAAYgI,IAAI,UACpC/E,EAAKwP,cAAgBxP,EAAKqP,gBAAgB5Y,GACtC9C,EAAOqM,EAAKgS,cAAcV,EAAMtR,EAAKvJ,QAAS4G,IAClD2C,EAAK4O,cAAgBpX,EACrBwI,EAAKuB,MAAM5N,GACXqM,EAAKc,SAAU,EACX,cAAcmO,QAAQxb,EAAEuG,WACxBvG,EAAEsT,iBAEN,CAEA4K,IAAUle,EAAEwe,UACZX,EAAK,GAAGnO,SAENnD,EAAK2O,YAAYlY,IAAYC,EAAQmK,eAAgB,GAAWnK,EAAQmK,YAAYmP,cAAiB9c,EAAMwC,QAAQqE,OAAWP,EAAe/F,IAAMuM,EAAK2O,YAAYlY,EAAQyT,QAAQ7M,QAGrLsU,GAAWN,GAAaQ,GACzBpe,EAAEsT,iBAEN/G,EAAKc,SAAU,EACfgQ,EAAaC,EAAWrY,GAAG,YAAc4C,EAAQF,GAC5C1E,EAAQ+K,cAAgBqP,GAAcxV,IAG3C0E,EAAK8Q,GAAYra,MAErBub,cAAe,SAAUE,EAAS5D,EAAK6D,GAAxB,GAKHlc,GACAyB,EALJF,EAAkB7C,KAAKuN,kBAC3B,OAAK1K,IAGGvB,EAAUoC,EAAoB6Z,EAAS1a,GACvCE,KACJtF,EAAE6D,GAASV,KAAK,WACZ,GAAIhC,GAASnB,EAAEuC,KACf,QAAIpB,EAAOmF,GAAG4V,KAGV/a,EAAOmF,GAAGyZ,IACVza,EAAOK,KAAKpD,MADhB,KAIGvC,EAAEsF,IAbFwa,EAAQ3b,aAAa+X,EAAK6D,IAgBzCJ,eAAgB,SAAUjD,EAAQyC,GAAlB,GAER5a,GAA8Cyb,EAK9CC,EACAC,CAIJ,OAXAxD,GAASA,EAAOpW,GAAG,WAAaoW,EAASA,EAAO5E,QAAQ,WACpDvT,EAAgBmY,EAAO9X,KAAK,iBAC5BL,IACAyb,EAAsBzd,KAAK4d,cAAczD,GACzCnY,EAAcuJ,KAAKvL,KAAMyd,IAEzBC,EAA+BD,GAAuBA,EAAoBI,qBAC1EF,EAA2B3d,KAAKoW,eAChCpX,KAAM4d,EACNvX,KAAMwB,KAEH6W,GAAgCC,GAE3CC,cAAe,SAAUzD,GACrB,GAAI2D,IACA3G,OAAQnX,KACRma,OAAQA,EACR4D,mBAAmB,EACnB3L,eAAgB,WACZpS,KAAK+d,mBAAoB,GAE7BF,mBAAoB,WAChB,MAAO7d,MAAK+d,mBAGpB,OAAOD,IAEXvP,eAAgB,SAAUzP,GACtB,GAAIuM,GAAOrL,IACPrB,IAAU0M,EAAKkC,oBAAsBlC,EAAKvJ,SAAS,GAAIhD,EAAEqb,UAG7D9O,EAAKc,SAAU,IAEnB8B,OAAQ,SAAUnP,GACd,GAAIuM,GAAOrL,KAAMma,EAASrb,EAAEqb,OAAQK,EAAYnP,EAAKoP,aAAcuD,EAAStY,GAC5E,OAAIyU,IAAU9O,EAAKG,QAAQ,IAAO/N,EAAE0c,GAAQpW,GAAG,oBAM3Cia,IAAWlf,EAAEmT,gBACTuI,EAAU3a,OACVwL,EAAK4S,cAAezD,GACZnP,EAAKwP,eACbxP,EAAK4S,cAAe5S,EAAKG,QAAQtM,WAAW4Z,UAJpD,IALIha,EAAEqT,kBACF1U,EAAE0c,GAAQ5E,QAAQ,cAAcA,QAAQ,iBAAiBA,QAAQ,WAAWtW,SAASoJ,IACrFgD,EAAKG,QAAQ0S,QACb,IAURlQ,SAAU,SAAUlP,GAChB,GAAkEqb,GAAQgE,EAAmB9I,EAAzFhK,EAAOrL,KAAMoe,EAAMtf,EAAEuf,QAAS7D,EAAYnP,EAAKwP,cAAuD5c,EAAQM,EAAMwC,QAAQ9C,MAAMoN,EAAKG,QAC3I,IAAI1M,EAAEqb,QAAUrb,EAAEmT,eAAiBmM,GAAO9X,EAAKgY,IAA/C,CASA,GANK9D,IACDA,EAAYnP,EAAKwP,cAAgBxP,EAAKoP,cAE1C0D,EAAoB9S,EAAKkT,uBAAuB/D,GAChDnF,EAAchK,EAAK4P,iBAAiBT,GACpCnP,EAAK0N,eAAgB,EACjBqF,GAAO9X,EAAKkY,MACZrE,EAAS9O,EAAKpN,EAAQ,YAAc,cAAcuc,EAAW2D,EAAmB9I,OAC7E,IAAI+I,GAAO9X,EAAKmY,KACnBtE,EAAS9O,EAAKpN,EAAQ,aAAe,aAAauc,EAAW2D,EAAmB9I,OAC7E,IAAI+I,GAAO9X,EAAKoY,KACnBvE,EAAS9O,EAAKsT,UAAUnE,EAAW2D,EAAmB9I,OACnD,IAAI+I,GAAO9X,EAAKsY,GACnBzE,EAAS9O,EAAKwT,QAAQrE,EAAW2D,EAAmB9I,OACjD,IAAI+I,GAAO9X,EAAKwY,KACnBzT,EAAK4S,WAAWzD,EAAWA,EAAU5b,SAASM,WAAW4Z,SACzDha,EAAEsT,qBACC,IAAIgM,GAAO9X,EAAKyY,IACnB1T,EAAK4S,WAAWzD,EAAWA,EAAU5b,SAASM,WAAW0E,QACzD9E,EAAEsT,qBACC,IAAIgM,GAAO9X,EAAKgY,IACnBnE,EAAS9O,EAAK2T,SAASxE,EAAW2D,OAC/B,IAAIC,GAAO9X,EAAK2Y,OAASb,GAAO9X,EAAK4Y,SACxC/E,EAASK,EAAUtb,SAAS,WACxBib,EAAOta,OAAS,IAChBwL,EAAKqC,QACDyM,OAAQA,EAAO,GACf/H,eAAgB,aAEhBkL,UAAU,IAEVjI,IAAgBmF,EAAUxZ,SAAStB,KACnC2L,EAAKqB,KAAK8N,GACVnP,EAAK4S,WAAWzD,EAAWnP,EAAK8T,mBAAmB3E,GAAWtb,WAAW4Z,UAEzEzN,EAAK4S,WAAWzD,EAAWnP,EAAKqP,gBAAgBF,SAGrD,IAAI4D,GAAO9X,EAAK8Y,IAInB,MAHAjF,GAAS9O,EAAKqP,gBAAgBF,GAC9BnP,EAAK4S,WAAWzD,EAAWL,GAC3B9O,EAAK8C,sBACL,CAEAgM,IAAUA,EAAO,KACjBrb,EAAEsT,iBACFtT,EAAEqT,qBAGVsI,WAAY,WACR,MAAOza,MAAKwL,QAAQ9K,KAAK,iDAAiDpB,OAAO,aAErFif,uBAAwB,SAAUvf,GAC9B,GAAIqgB,GAAiBrf,KAAKwL,QAAQxK,SAAS,kBAC3C,OAAKhC,GAAKa,OAGHb,EAAKJ,SAASoC,SAAS,iBAAmBqe,EAFtCA,GAIfpE,iBAAkB,SAAUjc,GACxB,SAAKA,GAASA,EAAKa,QAAWb,EAAK,GAAGoB,YAG/BpB,EAAKE,SAAS,4CAA4CW,OAAS,KAAOb,EAAKqD,KAAKc,OAAwBnD,KAAKuN,mBAAmBrO,SAASwD,EAAmB1D,EAAKqD,KAAKc,QAErL8a,WAAY,SAAUjf,EAAMsgB,GACxB,GAAIjU,GAAOrL,KAAMyC,EAAK4I,EAAKe,OACvBpN,GAAKa,QAAUyf,EAASzf,QACxBb,EAAKkC,YAAYmH,IAEjBiX,EAASzf,SACLyf,EAAS,GAAG7c,KACZA,EAAK6c,EAAS,GAAG7c,IAErB6c,EAASrgB,SAASoJ,IAClBgD,EAAKwP,cAAgByE,EACjB7c,IACA4I,EAAKvJ,QAAQnC,WAAW,yBACxBlC,EAAE,IAAMgF,GAAI9C,WAAW,MACvB2f,EAAS1f,KAAK,KAAM6C,GACpB4I,EAAKvJ,QAAQlC,KAAK,wBAAyB6C,IAE/C4I,EAAKkU,cAAcD,KAG3B5E,gBAAiB,SAAU1b,GACvB,MAAIgB,MAAKga,YAAYhb,GACVA,EAEAgB,KAAKqd,cAAcre,EAAMuJ,GAAc,aAAa3E,QAGnEoW,YAAa,SAAUhb,GACnB,MAAOA,GAAKJ,SAASoC,SAASC,IAElCue,WAAY,SAAUxgB,EAAMmf,EAAmB9I,GAAnC,GACSiK,GAAUG,EAAY5c,EAc3BkZ,EAdR1Q,EAAOrL,IA0BX,OAzBKme,GAMM9I,IAAgBrW,EAAKgC,SAAStB,KACrC2L,EAAKqB,KAAK1N,GACVsgB,EAAWjU,EAAK8T,mBAAmBngB,GAAME,WAAW4Z,SACjB,cAA5BzN,EAAKtJ,QAAQ8K,cACpB4S,EAAapU,EAAKqP,gBAAgB1b,GAClC6D,EAAkBwI,EAAKkC,mBACnB1K,IACIkZ,EAAYvY,EAAUic,EAAY5c,GACtCwI,EAAKuH,kBAAkBmJ,IAE3B1Q,EAAKuB,MAAM6S,GACXH,EAAWG,EAAWC,QAAQ1W,MAhB9BsW,EAAWtgB,EAAK0gB,QAAQ1W,IACnBsW,EAASzf,SACVyf,EAAWtgB,EAAK2gB,QAAQ1W,KAE5BoC,EAAKuB,MAAM5N,IAcXsgB,IAAaA,EAASzf,OACtByf,EAAWjU,EAAKG,QAAQtM,SAAS,WAAW4Z,QACpCwG,IACRA,MAEJjU,EAAK4S,WAAWjf,EAAMsgB,GACfA,GAEXM,UAAW,SAAU5gB,EAAMmf,GACvB,GAAiBmB,GAAUzc,EAAvBwI,EAAOrL,IAsBX,OArBKme,IAODmB,EAAWtgB,EAAKJ,SAAS2W,QAAQ,WACjC1S,EAAkBwI,EAAKkC,oBAClB+R,EAASzf,QAAUgD,IACpByc,EAAWjc,EAAgBrE,EAAKJ,SAAUiE,IAE9CwI,EAAKuB,MAAM0S,GACPjU,EAAK2O,YAAYsF,IAAyC,cAA5BjU,EAAKtJ,QAAQ8K,cAC3CyS,EAAWA,EAASK,QAAQ3W,OAbhCsW,EAAWtgB,EAAK2gB,QAAQ3W,IACnBsW,EAASzf,SACVyf,EAAWtgB,EAAK0gB,QAAQzW,KAE5BoC,EAAKuB,MAAM5N,IAYVsgB,EAASzf,SACVyf,EAAWjU,EAAKG,QAAQtM,SAAS,WAAW0E,QAEhDyH,EAAK4S,WAAWjf,EAAMsgB,GACfA,GAEXX,UAAW,SAAU3f,EAAMmf,EAAmB9I,GAC1C,GAAiBiK,GAAbjU,EAAOrL,IACX,IAAKme,EAQDmB,EAAWtgB,EAAK0gB,QAAQ1W,QARJ,CACpB,IAAKqM,GAAerW,EAAKgC,SAAStB,IAC9B,MAEA2L,GAAKqB,KAAK1N,GACVsgB,EAAWjU,EAAK8T,mBAAmBngB,GAAME,WAAW4Z,QAW5D,OANKwG,EAASzf,QAAUb,EAAKa,OACzByf,EAAWtgB,EAAKJ,SAASM,WAAW4Z,QAC5B9Z,EAAKa,SACbyf,EAAWjU,EAAKG,QAAQtM,SAAS,WAAW4Z,SAEhDzN,EAAK4S,WAAWjf,EAAMsgB,GACfA,GAEXT,QAAS,SAAU7f,EAAMmf,GACrB,GAAiBmB,GAAbjU,EAAOrL,IACX,IAAKme,EAWL,MARImB,GAAWtgB,EAAK2gB,QAAQ3W,KAEvBsW,EAASzf,QAAUb,EAAKa,OACzByf,EAAWtgB,EAAKJ,SAASM,WAAW0E,OAC5B5E,EAAKa,SACbyf,EAAWjU,EAAKG,QAAQtM,SAAS,WAAW0E,QAEhDyH,EAAK4S,WAAWjf,EAAMsgB,GACfA,GAEXC,cAAe,SAAUvgB,GAAV,GAGHuG,GACAyJ,EACA6Q,EACAC,EACAC,EACAC,EACAC,EACAC,EACAzH,EACA0H,EACAC,EAOI9O,EAnBRjG,EAAOrL,IACPqL,GAAKtJ,QAAQiL,YAAchO,GAAQA,EAAKa,SACpC0F,EAAKvG,EAAKJ,SACVoQ,IAAezJ,EAAGvE,SAASC,IAAoC,cAA5BoK,EAAKtJ,QAAQ8K,YAChDgT,EAAY7Q,EAAe,aAAe,YAC1C8Q,EAAU9Q,EAAezQ,EAAM8hB,YAAc9hB,EAAMgY,aACnDwJ,EAAsBxa,EAAGsa,KACzBG,EAAWF,EAAQ9gB,GACnBihB,EAAajhB,EAAK,GAAGgQ,EAAe,aAAe,aACnDkR,EAASJ,EAAQva,GACjBkT,EAAgBlT,EAAGiQ,SAASrM,IAC5BgX,EAAmB1H,EAAc5Y,OAASigB,EAAQrH,EAAcK,SAAW,EAE3EiH,EAAsBG,EAASD,EAAaD,EAAWG,EACvDC,EAAeH,EAAaD,EAAWE,EAASC,EACzCJ,EAAsBE,EAAaE,IAC1CC,EAAeH,EAAaE,GAE3Bvb,MAAMwb,KACH9O,KACJA,EAAUuO,GAAaO,EACvB7a,EAAGqM,SAASC,QAAQP,EAAW,OAAQ,SAAU,WAC7CjG,EAAKyE,qBAAqBvK,EAAIkT,EAAcK,QAASL,EAAc7U,OAAQoL,QAK3FgQ,SAAU,SAAUhgB,EAAMmf,GACtB,GAAiBmB,GAAbjU,EAAOrL,IACX,OAAKme,IAGDmB,EAAWtgB,EAAKJ,SAAS2W,QAAQ,WACjClK,EAAKuB,MAAM0S,GACXjU,EAAK4S,WAAWjf,EAAMsgB,GAEnBA,GANItgB,GAQfmgB,mBAAoB,SAAUngB,GAAV,GACZsE,GAAetE,EAAK0B,KAAK,iBACzB8K,EAAUxL,KAAKuN,kBAInB,QAHKjK,EAAazD,QAAU2L,IACxBlI,EAAeE,EAAUxE,EAAMwM,IAE5BlI,GAEX8S,cAAe,SAAUtX,GACrB,GAAIuM,GAAOrL,IACX,OAAOqL,GAAK6G,QAAQpT,EAAEuG,MAClBA,KAAMvG,EAAEuG,KACRrG,KAAMF,EAAEE,QAGhByO,cAAe,SAAU3O,GACrB,GAAIuM,GAAOrL,KAAMhB,EAAOvB,EAAEc,EAAM6b,YAAYtb,IAAIyW,QAAQ7M,GACpD1J,GAAKgC,SAAStB,KAGlBiW,WAAW,WACPtK,EAAK4S,cAAejf,GAChBA,EAAKE,SAAS,cAAc,IAC5BF,EAAKJ,SAAS2W,QAAQ,WAAWrU,YAAYmH,KAElD,MAEPwD,YAAa,SAAU9J,GACfA,GAAW,aAAeA,KAAYA,EAAQ0K,YAC9C1K,EAAQ0K,WACJC,MAAQuJ,YACRrJ,OACI6K,MAAM,EACNxB,eAKhBtK,YAAa,SAAU5J,GACnB,GAAIsJ,GAAOrL,KAAMmN,EAAapL,EAAUA,EAAQoL,WAAa9B,EAAKtJ,QAAQoL,UACrEA,KAGLA,EAAatH,EAAQsH,IAAgB9K,KAAM8K,GAAeA,EAC1D9B,EAAKiV,oBACAnT,EAAWoT,SACZpT,EAAWoT,SACLC,MAAO,QACPA,MAAO,SACPA,MAAO,QACPA,MAAO,aACPA,MAAO,mBACPA,MAAO,aACPA,MAAO,cACPA,MAAO,SACPA,MAAO,gBACPA,MAAO,YACPA,MAAO,YACPA,MAAO,UACPA,MAAO,YAGjBnV,EAAK8B,WAAarH,EAAuB2a,OAAOtT,GAChD9B,EAAKqV,kBACLrV,EAAK8B,WAAWwT,UAEpBD,gBAAiB,WACb1gB,KAAK4gB,gBAAkBxa,EAAMpG,KAAK6gB,QAAS7gB,MAC3CA,KAAK8gB,cAAgB1a,EAAMpG,KAAK+gB,OAAQ/gB,MACxCA,KAAKmN,WAAW6T,KAAK5Z,GAAQpH,KAAK4gB,iBAClC5gB,KAAKmN,WAAW6T,KAAK3Z,GAAOrH,KAAK8gB,gBAErCR,kBAAmB,WACf,GAAInT,GAAanN,KAAKmN,UAClBA,KACAA,EAAWwB,OAAOvH,GAAQpH,KAAK4gB,iBAC/BzT,EAAWwB,OAAOtH,GAAOrH,KAAK8gB,iBAGtCC,OAAQ,aAERE,UAAW,SAAUC,GACjB,GAAIC,GAAiBnhB,KAAKuN,oBAAsBvN,KAAK8B,OACrD,OAAOqf,GAAezgB,KAAK,aAAewgB,EAAM,MAEpDL,QAAS,SAAU1b,GAAV,GACDkG,GAAOrL,KACPohB,EAAOjc,EAAGic,KACVC,EAASlc,EAAGkc,OACZC,EAAgBF,EAAO/V,EAAK4V,UAAUG,EAAKF,KAAO7V,EAAKvJ,QACvDyf,EAAgBpc,EAAGhD,MACnBZ,EAAQ4D,EAAG5D,MACXigB,EAAc/jB,EAAE2I,MAAMiF,EAAKoW,YAAapW,GACxCqW,EAAcjkB,EAAE2I,MAAMiF,EAAKsW,YAAatW,EAC9B,QAAVgW,EACAhW,EAAKuW,aAAaL,EAAehgB,EAAO+f,GACvB,UAAVD,EACPE,EAAcM,QAAQH,GACL,cAAVL,EACPE,EAAcM,QAAQL,GACJ,eAAXH,EACPhW,EAAK5L,OAAO0F,EAAGhD,MAAOmf,GAEtBthB,KAAKkN,YAETlN,KAAKkS,QAAQ5I,IACTtK,KAAMsiB,EACNxM,SAAUsM,KAGlBQ,aAAc,SAAUzf,EAAOZ,EAAO3C,GAAxB,GACNyM,GAAOrL,KACPwT,EAAgB5U,EAAO8B,KAAKkI,IAAcnH,GAAGF,EAC7CiS,GAAc3T,OACdwL,EAAKsI,aAAaxR,EAAOqR,GAEzBnI,EAAK5L,OAAO0C,EAAOvD,IAG3B+iB,YAAa,SAAU3iB,GAAV,GACLqM,GAAOrL,KACP8B,EAAUuJ,EAAK4V,UAAUjiB,EAAKkiB,IAClC7V,GAAK1K,OAAOmB,IAEhB2f,YAAa,SAAUziB,GAAV,GACLqM,GAAOrL,KACP8B,EAAUuJ,EAAK4V,UAAUjiB,EAAKkiB,KAC9BY,EAAchgB,EAAQigB,OACtBje,EAAa9E,EAAK8E,YACtBuH,GAAK1K,OAAOmB,GACRggB,EAAYjiB,OACZwL,EAAKsI,aAAa3U,EAAM8iB,GAExBzW,EAAK5L,OAAOT,EAAM8E,GAAcuH,EAAK4V,UAAUnd,EAAWod,OAGlEzV,WAAY,WACR,GAAyCrJ,GAAGoe,EAAOwB,EAA/C3W,EAAOrL,KAAM+B,EAAUsJ,EAAKtJ,QAA8BD,EAAUuJ,EAAKvJ,OAC7E,KAAKM,IAAKmH,IACNiX,EAAQze,EAAQwH,GAASnH,IACzB4f,EAAYlgB,EAAQlC,KAAKrB,EAAMqB,KAAKwC,EAAI,YACnCoe,GAASwB,IACVxB,EAAQwB,GAEPxB,IACDA,EAAQpe,GAEPyD,EAAQ2a,KACTA,GAASA,IAEbze,EAAQwH,GAASnH,IAAMoe,GAG/ByB,eAAgB,SAAUC,GACtB,GAAIC,GAAgBniB,KAAK+B,QAAQwH,GAAS2Y,QAAmBE,EAAQD,EAActiB,OAAQkD,EAAS,mBAWpG,OAVc,KAAVqf,EACArf,GAAU,gBAAmBmf,EAAY,OAEzCnf,GAAU,iBAAmBtF,EAAE6W,IAAI6N,EAAe,SAAUE,GACxD,MAAO,uBAAyB9jB,EAAM+jB,KAAKD,GAAK,MACjD3jB,KAAK,KAAO,KACfqE,GAAU,uDAAyDqf,EAAQ,mBAC3Erf,GAAU,kBAAoBqf,EAAQ,cAE1Crf,GAAU,MAGd2I,WAAY,WACR,GAAIL,GAAOrL,KAAM+B,EAAUsJ,EAAKtJ,QAASwgB,EAAgBnc,EAAMiF,EAAK4W,eAAgB5W,EAChFtJ,GAAQsE,gBAAmBtE,GAAQsE,UAAYgD,GAC/CtH,EAAQsE,SAAWA,EAAStE,EAAQsE,UAC5BtE,EAAQsE,WAChBtE,EAAQsE,SAAWA,EAAS,gBAAkBkc,EAAc,QAAU,sIAE1ElX,EAAK+D,WACDvF,QAASxD,EAAS,sBAAwBkc,EAAc,WAAa;AACrE5gB,MAAO0E,EAAS,+HAChBmc,YAAanc,EAAS,eAAiBkc,EAAc,OAAS,6BAAoCA,EAAc,YAAc,kCAAyCA,EAAc,aAAe,mSACpMvjB,KAAMqH,EAAS,sBAAwBkc,EAAc,WAAa,4OAA4PhkB,EAAMqB,KAAK,OAAS,0QAClVyP,aAAchJ,EAAS,uKACvBoc,MAAOpc,EAAS,sDAChBqc,OAAQrc,EAAS,0BAA4Bkc,EAAc,kBAAoB,sFAC/E/iB,MAAO6G,EAAS,MAGxBoO,WAAY,SAAU1S,GAAV,GAMJvC,GAA8BR,EAL9BqM,EAAOrL,IAMX,OALA+B,GAAUoE,GACNwc,KAAMtX,EACN1J,UACDI,GACCvC,EAAQ6L,EAAK+D,UAAU5P,MAAOR,EAAO+C,EAAQ/C,KAC1CqM,EAAK+D,UAAUpQ,KAAKmH,EAAOpE,GAC9B2gB,OAAQrX,EAAK+D,UAAUsT,OACvBF,YAAanX,EAAK+D,UAAUoT,YAC5BI,cAAevX,EAAKuX,cACpBH,MAAOzjB,EAAKmD,OAASnD,EAAK6K,SAAW7K,EAAKqM,EAAKtJ,QAAQ8gB,iBAAiB,IAAMxX,EAAK+D,UAAUqT,MAAQjjB,EACrGsjB,SAAUzX,EAAK+I,aAChBtK,MAEPsK,YAAa,SAAUrS,GAAV,GACLsJ,GAAOrL,KACPoP,EAAY/D,EAAK+D,WAAarN,EAAQ4gB,KAAKvT,SAC/C,OAAOA,GAAUzN,MAAMwE,GACnB4c,YAAa,SAAUhhB,GAEnB,IADA,GAAIihB,GAAO,GAAI5gB,EAAI,EAAGD,EAAQJ,EAAQI,MAAO8gB,EAAM9gB,EAAQA,EAAMtC,OAAS,EAAG8B,EAAQwE,GAAStG,OAAQojB,GAAOlhB,EAAQJ,OAC9GS,EAAI6gB,EAAK7gB,IACZ4gB,GAAQjhB,EAAQ4gB,KAAKlO,WAAWtO,EAAOpE,GACnCJ,MAAOA,EACP3C,KAAMmH,GAAS5E,MAAOa,GAAKD,EAAMC,MAGzC,OAAO4gB,KAEZjhB,EAAS+H,MAEhB8Y,cAAe,SAAU7gB,GACrB,MAAOA,GAAQ4gB,KAAKvT,UAAUvF,QAAQ1D,EAAOpE,EAAS+H,QAG1DoZ,GAAchY,GAAK/E,QACnBiF,KAAM,SAAUtJ,EAASC,GACrB,GAAIsJ,GAAOrL,IACXkL,IAAKI,GAAGF,KAAKG,KAAKF,EAAMvJ,EAASC,GACjCsJ,EAAK8X,QAAU5kB,EAAM6kB,OAAOC,UAAU,EAAG,GACzChY,EAAK8O,OAAS1c,EAAE4N,EAAKtJ,QAAQoY,QAC7B9O,EAAKiY,SACLjY,EAAKkY,SAETvX,cAAe,SAAUjK,GACrB,GAAIsJ,GAAOrL,IACP+B,GAAQiL,aAAe3B,EAAKkC,qBAC5BlC,EAAK4D,iBACL5D,EAAK0E,gBAAkB1E,EAAKvJ,QAAQlD,SAASmF,GAAGN,IAA8B4H,EAAKvJ,QAAQlD,SAAWyM,EAAKvJ,SAASqN,KAAK,gCAAmCpN,EAAQ8K,YAAc,YAAajO,SAC/J,cAA5ByM,EAAKtJ,QAAQ8K,aACbvH,EAAyB+F,EAAKvJ,SAE9BC,EAAQwN,UACRxN,EAAQwN,SAAS9P,OAAO4L,EAAK0E,gBAEjC1E,EAAKmO,eAAiBnO,EAAKvJ,QAAQ,GAAG4N,MAAM2G,OAC5ChL,EAAKmY,cAAgBnY,EAAKvJ,QAAQ,GAAG4N,MAAMC,QAGnD5N,SACIyK,KAAM,cACNlN,OAAQ,KACRmkB,OAAQ,cACR5W,YAAa,WACb6W,eAAe,EACfvJ,OAAQ,QAEZ5N,QACI9F,EACAE,EACAI,GACAC,GACAH,IAEJoM,WAAY,SAAUlR,GAClB,GAAIsJ,GAAOrL,IACXkL,IAAKI,GAAG2H,WAAW1H,KAAKF,EAAMtJ,GAC9BsJ,EAAK8O,OAAOzL,IAAIrD,EAAKoY,OAASjd,EAAK6E,EAAK8X,QAAS9X,EAAKsY,YAClDtY,EAAKuY,YACLvY,EAAKuY,WAAW1Q,UAEpB7H,EAAK8O,OAAS1c,EAAE4N,EAAKtJ,QAAQoY,QACzBpY,EAAQ8K,aAAexB,EAAKnI,MAAMsI,QAAQ,IAC1CH,EAAKnI,MAAMpB,QAAQgP,SAEvBzF,EAAKkY,QACLrY,GAAKI,GAAG2H,WAAW1H,KAAKvL,KAAM+B,IAElCmR,QAAS,WACL,GAAI7H,GAAOrL,IACXqL,GAAK8O,OAAOzL,IAAIrD,EAAKtJ,QAAQ0hB,OAASjd,EAAK6E,EAAK8X,SAChDnb,GAAiB0G,IAAInQ,EAAMwC,QAAQ8iB,UAAYrd,EAAK6E,EAAK8X,QAAS9X,EAAKyY,aACnEzY,EAAKuY,YACLvY,EAAKuY,WAAW1Q,UAEpBhI,GAAKI,GAAG4H,QAAQ3H,KAAKF,IAEzBqB,KAAM,SAAU2V,EAAG0B,GAAb,GAeclhB,GAEIkQ,EAhBhB1H,EAAOrL,IAiCX,OAhCAqiB,GAAI5kB,EAAE4kB,GAAG,GACL1jB,EAAS0M,EAAKvJ,QAAQ,GAAIrE,EAAE4kB,GAAG,KAAOhX,EAAK4P,iBAAiBxd,EAAE4kB,IAC9DnX,GAAKI,GAAGoB,KAAKnB,KAAKF,EAAMgX,GAEpBhX,EAAK+K,eACDpX,KAAMqM,EAAKvJ,QACXuD,KAAMoB,OACH,IACH4E,EAAKnI,MAAM8gB,WAAa3Y,EAAKtJ,QAAQzC,SACrC+L,EAAKnI,MAAM0J,OAAM,GACjBvB,EAAKnI,MAAMpB,QAAQmiB,WAAU,IAE7BF,IAAMrmB,GACFmF,EAAkBwI,EAAKkC,mBACvB1K,IACIkQ,EAASlQ,EAAgBkQ,SAC7BsP,GAAKtP,EAAOmR,KACZH,GAAKhR,EAAO4G,KAEhBtO,EAAKnI,MAAMsI,QAAQiM,OACnBpM,EAAK8Y,yBAAyB9B,EAAG0B,GACjC1Y,EAAKnI,MAAMwJ,KAAK2V,EAAG0B,KAEnB1Y,EAAKnI,MAAMnB,QAAQwV,QAAU8K,EAAIA,EAAIhX,EAAKnI,MAAMqU,SAAWlM,EAAK8O,OAChE9O,EAAKnI,MAAMpB,QAAQmiB,WAAU,GAC7B5Y,EAAK8Y,2BACL9Y,EAAKnI,MAAMwJ,QAEf1E,GAAiB0G,IAAIrD,EAAKnI,MAAMkhB,UAAW/Y,EAAKnI,MAAMmhB,iBACtDrc,GAAiBwF,GAAGjP,EAAMwC,QAAQ8iB,UAAYrd,EAAK6E,EAAK8X,QAAS9X,EAAKyY,cAGvEzY,GAEX8Y,yBAA0B,SAAU9B,EAAG0B,GAAb,GAClB1Y,GAAOrL,KACPkD,EAAQmI,EAAKnI,MACb8L,EAA2C,cAA5B3D,EAAKtJ,QAAQ8K,WAC5BxB,GAAKtJ,QAAQiL,aACb3B,EAAK2M,kBAAkB9U,GACvBA,EAAMpB,QAAQlD,SAASwR,KACnBhS,SAAU,GACViY,OAAQ,KAEZnT,EAAMpB,QAAQsO,KACVkU,WAAY,SACZlM,QAAS,GACTha,SAAU,KAEV4Q,EACA3D,EAAKkZ,eAAerhB,EAAO0B,MAAMyd,GAAK3kB,GAClCub,SAAS,EACToJ,EAAGA,EACH0B,EAAGA,IAGP1Y,EAAK2N,gBAAgB9V,EAAO0B,MAAMyd,GAAK3kB,GACnCub,SAAS,EACToJ,EAAGA,EACH0B,EAAGA,IAGX7gB,EAAMpB,QAAQsO,KACVkU,WAAY,GACZlM,QAAS,OACTha,SAAU,aAEdiN,EAAKmN,wBAAwBtV,EAAO8L,GAAc,GAClD9L,EAAMpB,QAAQ0T,SAASrM,IAAsBsO,SAGrD8M,eAAgB,SAAUrhB,EAAO+V,GAAjB,GAIRC,GACAsL,EACAC,EACAC,EACAhT,EACAiT,EACAC,EACAtL,EAVAhW,EAAeJ,EAAMpB,QACrByX,EAASjW,EAAagM,IAAIhM,EAAa1E,OAAO6E,IAClD8V,GAAO5J,MAAM3P,KAAKwjB,eAAiB,IAC/BtK,EAAWhW,EAAMuW,UAAUR,GAC3BuL,EAAc/mB,EAAE+H,QAAQmK,QACxB8U,EAAkBvL,EAASvJ,MAC3B+U,EAAkBtgB,KAAKsV,IAAIR,EAASgL,KAAM,GAC1CxS,EAAauH,EAAU,EAAIxU,EAAczE,KAAKuN,mBAAmB,GAAI,cACrEoX,EAASpmB,EAAMiY,WAAWlT,GAC1BshB,EAAWJ,EAAcG,EAAOT,KAAOS,EAAOE,MAC9CvL,EAASsL,EAAWlT,EAAa+S,EAAkBC,EAClDpL,GACDC,EAAOnJ,KACHsG,SAAU,SACV/G,MAAOiV,EAAWF,EAAkBhT,EAAa,QAI7D9E,MAAO,WACH,GAAIvB,GAAOrL,IACPrB,GAAS0M,EAAKvJ,QAAQ,GAAIrE,EAAEqnB,UAAU,IAAI,KAAOzZ,EAAK4P,iBAAiB6J,UAAU,IACjF5Z,GAAKI,GAAGsB,MAAMrB,KAAKF,EAAMyZ,UAAU,IAE/BzZ,EAAKnI,MAAM8gB,WACP3Y,EAAK+K,eACDpX,KAAMqM,EAAKvJ,QACXuD,KAAMsB,OACH,IACP0E,EAAKnI,MAAM0J,QACX5E,GAAiB0G,IAAInQ,EAAMwC,QAAQ8iB,UAAYrd,EAAK6E,EAAK8X,QAAS9X,EAAKyY,aACvEzY,EAAKsD,OAAO9H,GAAQwE,EAAK0Z,sBAKzCC,aAAc,SAAUlmB,GACpB,GAAYiU,GAAR5N,EAAKrG,EAAWuM,EAAOrL,KAAM+B,EAAUsJ,EAAKtJ,QAASoY,EAAkC1c,EAAzBc,EAAMwC,QAAQ6E,SAAaT,EAAGgV,OAAYhV,EAAG8M,cAC3GnT,GAAEmmB,QACF9f,EAAKrG,EAAEmmB,MACP9f,EAAG+f,MAAQpmB,EAAEujB,EAAEnJ,SACf/T,EAAGggB,MAAQrmB,EAAEilB,EAAE7K,UAEfva,EAAS0M,EAAKvJ,QAAQ,GAAIhD,EAAEwc,eAAiBxc,EAAEqb,UAGnD9O,EAAK+Z,aAAejgB,EACpBA,EAAGiN,iBACHjN,EAAGuW,2BACHrQ,EAAKvJ,QAAQpB,KAAK,IAAM2H,IAAcnH,YAAYmH,KAC9CtG,EAAQzC,QAAU6a,EAAOpW,GAAGhC,EAAQzC,UAAYyC,EAAQzC,UACpDyC,EAAQ2hB,eACRrY,EAAKnI,MAAMnB,QAAQwV,OAASpS,EAAG8M,cAC/B5G,EAAKqB,KAAKvH,EAAG8M,iBAEb5G,EAAKnI,MAAMnB,QAAQwV,OAASpS,EAAG8M,cAC3B5G,EAAKga,cACLtS,EAAS1H,EAAK8O,OAAOpH,SACrB1H,EAAKqB,KAAKvH,EAAG+f,MAAQnS,EAAOmR,KAAM/e,EAAGggB,MAAQpS,EAAO4G,MAEpDtO,EAAKqB,KAAKvH,EAAG+f,MAAO/f,EAAGggB,WAKvCG,cAAe,SAAUxmB,GAAV,GAGPymB,GAFAla,EAAOrL,KAAMma,EAAS1c,EAAEqB,EAAEwc,eAAiBxc,EAAEqb,QAASqL,EAAarL,EAAO5E,QAAQlK,EAAK8O,OAAOqD,UAAU,IAAMnS,EAAK8O,OAAO,GAAInb,EAAOmb,EAAO5E,QAAQ3M,IAAe1J,EAAWmM,EAAK4P,iBAAiBjc,GAAO6D,EAAkBwI,EAAKkC,mBAAoBkY,EAAc9mB,EAAS0M,EAAKvJ,QAAQ,GAAIqY,EAAO,KAAOtX,GAAmBlE,EAASkE,EAAgB,GAAIsX,EAAO,GACvW9O,GAAK+Z,aAAetmB,EAChBymB,EAA0B,IAAZzmB,EAAE4mB,MAChBra,EAAKnI,MAAM8gB,YAAcuB,GAAeC,IAAeA,KAAgBna,EAAKtJ,QAAQ+K,eAAiB5N,GAAYumB,IAAgBA,KAC7HA,GACAzlB,KAAK2O,OAAO9H,GAAQ7G,KAAK+kB,oBACzB1Z,EAAK2V,KAAKna,GAAQwE,EAAK0Z,qBAEvB1Z,EAAKuB,UAIjB2W,MAAO,WACH,GAAIlY,GAAOrL,KAAM+B,EAAUsJ,EAAKtJ,QAASoY,EAAS9O,EAAK8O,MACvD9O,GAAKsY,WAAavd,EAAMiF,EAAK2Z,aAAc3Z,GAC3CA,EAAKyY,YAAc1d,EAAMiF,EAAKia,cAAeja,GAC7CA,EAAK0Z,mBAAqB3e,EAAMiF,EAAKuB,MAAOvB,GACxC8O,EAAO,KACH5b,EAAMwC,QAAQ6E,UAA8B,eAAlB7D,EAAQ0hB,QAClCpY,EAAKuY,WAAa,GAAIrlB,GAAMonB,WAAWxL,GACnC7a,OAAQyC,EAAQzC,OAChBsmB,gBAAgB,IAEpBzL,EAAO3M,GAAGzL,EAAQ0hB,OAASjd,EAAK6E,EAAK8X,SAAS,GAC9C9X,EAAKuY,WAAW5C,KAAK,OAAQ3V,EAAKsY,aAE9B5hB,EAAQzC,OACR6a,EAAO3M,GAAGzL,EAAQ0hB,OAASjd,EAAK6E,EAAK8X,QAASphB,EAAQzC,OAAQ+L,EAAKsY,YAEnExJ,EAAO3M,GAAGzL,EAAQ0hB,OAASjd,EAAK6E,EAAK8X,QAAS9X,EAAKsY,cAKnEvN,cAAe,SAAUtX,GACrB,GAAIuM,GAAOrL,KAAMuX,EAAS9Z,EAAE4N,EAAKnI,MAAMnB,QAAQwV,QAAQ,GAAIpZ,EAASkN,EAAK+Z,YAEzE,OADA/Z,GAAK+Z,aAAe1nB,EACb2N,EAAK6G,QAAQpT,EAAEuG,KAAMc,GACxBd,KAAMvG,EAAEuG,KACRrG,KAAMF,EAAEE,MAAQgB,KAAK8B,QAAQ,GAC7BqY,OAAQ5C,GACTpZ,GAAW8mB,MAAO9mB,SAEzBmlB,OAAQ,WAAA,GACAjY,GAAOrL,KACP6C,EAAkBwI,EAAKkC,kBAC3BlC,GAAKwa,cAAgBzf,EAAMiF,EAAK+K,cAAe/K,GAC/CA,EAAKnI,MAAQmI,EAAKvJ,QAAQ7C,SAAS,kBAAkB8X,YACjDoB,SAAuC,eAA7B9M,EAAKtJ,QAAQ8K,YACvB0K,OAAQlM,EAAK8O,QAAU,OACvB2L,iBAAkBza,EAAKtJ,QAAQ+jB,iBAC/BxO,UAAWjM,EAAKtJ,QAAQkL,gBAAkB,MAC1CR,UAAWpB,EAAKtJ,QAAQ0K,UACxBuK,SAAU3L,EAAKwa,cACf5O,WAAY5L,EAAKwa,cACjBtW,SAAU1M,GAAmBwI,EAAKtJ,QAAQwN,SAC1C3C,MAAQ/J,EAA2B,SAAU/D,GACzCrB,EAAEkF,EAAe7D,EAAEqY,OAAOrV,QAASe,IAAkBjC,KAAK,SAAUwB,EAAGuW,GACnE,GAAIzV,GAAQyV,EAAEtW,KAAK8F,GACfjF,IACAA,EAAM0J,OAAM,MAJEnP,EAAEsoB,OAQ7B1jB,KAAK8F,IACRkD,EAAKga,aAAe1mB,EAAS0M,EAAK8O,OAAO,GAAI9O,EAAKnI,MAAMpB,QAAQ,MAGxE2D,GAAGugB,OAAO9a,IACVzF,EAAGugB,OAAO9C,KACZ1d,OAAOjH,MAAM0nB,QACRzgB,OAAOjH,OACE,kBAAVf,SAAwBA,OAAO0oB,IAAM1oB,OAAS,SAAU2oB,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.menu.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.menu', [\n        'kendo.popup',\n        'kendo.data'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'menu',\n        name: 'Menu',\n        category: 'web',\n        description: 'The Menu widget displays hierarchical data as a multi-level menu.',\n        depends: [\n            'popup',\n            'data',\n            'data.odata'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, activeElement = kendo._activeElement, touch = kendo.support.touch && kendo.support.mobileOS, isArray = $.isArray, HierarchicalDataSource = kendo.data.HierarchicalDataSource, MOUSEDOWN = 'mousedown', CLICK = 'click', DELAY = 30, SCROLLSPEED = 50, extend = $.extend, proxy = $.proxy, each = $.each, template = kendo.template, keys = kendo.keys, Widget = ui.Widget, excludedNodesRegExp = /^(ul|a|div)$/i, NS = '.kendoMenu', IMG = 'img', OPEN = 'open', MENU = 'k-menu', LINK = 'k-link k-menu-link', LINK_SELECTOR = '.k-link', ICON_SELECTOR = '.k-icon', LAST = 'k-last', CLOSE = 'close', TIMER = 'timer', FIRST = 'k-first', IMAGE = 'k-image', SELECT = 'select', ZINDEX = 'zIndex', ACTIVATE = 'activate', DEACTIVATE = 'deactivate', POINTERDOWN = 'touchstart' + NS + ' MSPointerDown' + NS + ' pointerdown' + NS, pointers = kendo.support.pointers, msPointers = kendo.support.msPointers, allPointers = msPointers || pointers, CHANGE = 'change', ERROR = 'error', TOUCHSTART = kendo.support.touch ? 'touchstart' : '', MOUSEENTER = pointers ? 'pointerover' : msPointers ? 'MSPointerOver' : 'mouseenter', MOUSELEAVE = pointers ? 'pointerout' : msPointers ? 'MSPointerOut' : 'mouseleave', MOUSEWHEEL = 'DOMMouseScroll' + NS + ' mousewheel' + NS, RESIZE = kendo.support.resize + NS, SCROLLWIDTH = 'scrollWidth', SCROLLHEIGHT = 'scrollHeight', OFFSETWIDTH = 'offsetWidth', OFFSETHEIGHT = 'offsetHeight', POPUP_ID_ATTR = 'group', POPUP_OPENER_ATTR = 'groupparent', DOCUMENT_ELEMENT = $(document.documentElement), KENDOPOPUP = 'kendoPopup', DEFAULTSTATE = 'k-state-default', HOVERSTATE = 'k-state-hover', FOCUSEDSTATE = 'k-state-focused', DISABLEDSTATE = 'k-state-disabled', SELECTEDSTATE = 'k-state-selected', menuSelector = '.k-menu', groupSelector = '.k-menu-group', animationContainerSelector = '.k-animation-container', popupSelector = groupSelector + ',' + animationContainerSelector, allItemsSelector = ':not(.k-list) > .k-item', disabledSelector = '.k-item.k-state-disabled', itemSelector = '.k-item', availableItemsSelector = '.k-item:not(.k-state-disabled)', linkSelector = '.k-item:not(.k-state-disabled) > .k-link', exclusionSelector = ':not(.k-item.k-separator)', nextSelector = itemSelector + exclusionSelector + ':eq(0)', lastSelector = itemSelector + exclusionSelector + ':last', templateSelector = 'div:not(.k-animation-container,.k-list-container)', scrollButtonSelector = '.k-menu-scroll-button', touchPointerTypes = {\n                '2': 1,\n                'touch': 1\n            }, STRING = 'string', DATABOUND = 'dataBound', bindings = {\n                text: 'dataTextField',\n                url: 'dataUrlField',\n                spriteCssClass: 'dataSpriteCssClassField',\n                imageUrl: 'dataImageUrlField',\n                imageAttr: 'dataImageAttrField',\n                content: 'dataContentField'\n            }, rendering = {\n                wrapperCssClass: function (group, item) {\n                    var result = 'k-item', index = item.index;\n                    if (item.enabled === false) {\n                        result += ' k-state-disabled';\n                    } else {\n                        result += ' k-state-default';\n                    }\n                    if (group.firstLevel && index === 0) {\n                        result += ' k-first';\n                    }\n                    if (index == group.length - 1) {\n                        result += ' k-last';\n                    }\n                    if (item.cssClass) {\n                        result += ' ' + item.cssClass;\n                    }\n                    if (item.attr && item.attr.hasOwnProperty('class')) {\n                        result += ' ' + item.attr['class'];\n                    }\n                    if (item.selected) {\n                        result += ' ' + SELECTEDSTATE;\n                    }\n                    return result;\n                },\n                itemCssAttributes: function (item) {\n                    var result = '';\n                    var attributes = item.attr || {};\n                    for (var attr in attributes) {\n                        if (attributes.hasOwnProperty(attr) && attr !== 'class') {\n                            result += attr + '=\"' + attributes[attr] + '\" ';\n                        }\n                    }\n                    return result;\n                },\n                imageCssAttributes: function (imgAttributes) {\n                    var result = '';\n                    var attributes = imgAttributes && imgAttributes.toJSON ? imgAttributes.toJSON() : {};\n                    if (!attributes['class']) {\n                        attributes['class'] = IMAGE;\n                    } else {\n                        attributes['class'] += ' ' + IMAGE;\n                    }\n                    for (var attr in attributes) {\n                        if (attributes.hasOwnProperty(attr)) {\n                            result += attr + '=\"' + attributes[attr] + '\" ';\n                        }\n                    }\n                    return result;\n                },\n                contentCssAttributes: function (item) {\n                    var result = '';\n                    var attributes = item.contentAttr || {};\n                    var defaultClasses = 'k-content k-group k-menu-group';\n                    if (!attributes['class']) {\n                        attributes['class'] = defaultClasses;\n                    } else {\n                        attributes['class'] += ' ' + defaultClasses;\n                    }\n                    for (var attr in attributes) {\n                        if (attributes.hasOwnProperty(attr)) {\n                            result += attr + '=\"' + attributes[attr] + '\" ';\n                        }\n                    }\n                    return result;\n                },\n                textClass: function () {\n                    return LINK;\n                },\n                arrowClass: function (item, group) {\n                    var result = 'k-icon';\n                    if (group.horizontal) {\n                        result += ' k-i-arrow-60-down';\n                    } else {\n                        result += ' k-i-arrow-60-right';\n                    }\n                    return result;\n                },\n                groupAttributes: function (group) {\n                    return group.expanded !== true ? ' style=\\'display:none\\'' : '';\n                },\n                groupCssClass: function () {\n                    return 'k-group k-menu-group';\n                },\n                content: function (item) {\n                    return item.content ? item.content : '&nbsp;';\n                }\n            };\n        function getEffectDirection(direction, root) {\n            direction = direction.split(' ')[!root + 0] || direction;\n            return direction.replace('top', 'up').replace('bottom', 'down');\n        }\n        function parseDirection(direction, root, isRtl) {\n            direction = direction.split(' ')[!root + 0] || direction;\n            var output = {\n                    origin: [\n                        'bottom',\n                        isRtl ? 'right' : 'left'\n                    ],\n                    position: [\n                        'top',\n                        isRtl ? 'right' : 'left'\n                    ]\n                }, horizontal = /left|right/.test(direction);\n            if (horizontal) {\n                output.origin = [\n                    'top',\n                    direction\n                ];\n                output.position[1] = kendo.directions[direction].reverse;\n            } else {\n                output.origin[0] = direction;\n                output.position[0] = kendo.directions[direction].reverse;\n            }\n            output.origin = output.origin.join(' ');\n            output.position = output.position.join(' ');\n            return output;\n        }\n        function contains(parent, child) {\n            try {\n                return $.contains(parent, child);\n            } catch (e) {\n                return false;\n            }\n        }\n        function updateItemClasses(item) {\n            item = $(item);\n            item.addClass('k-item').children(IMG).addClass(IMAGE);\n            item.children('a').addClass(LINK).children(IMG).addClass(IMAGE);\n            item.filter(':not([disabled])').addClass(DEFAULTSTATE);\n            item.filter('.k-separator').empty().append('&nbsp;');\n            item.filter('li[disabled]').addClass(DISABLEDSTATE).removeAttr('disabled').attr('aria-disabled', true);\n            if (!item.filter('[role]').length) {\n                item.attr('role', 'menuitem');\n            }\n            if (!item.children(LINK_SELECTOR).length) {\n                item.contents().filter(function () {\n                    return !this.nodeName.match(excludedNodesRegExp) && !(this.nodeType == 3 && !$.trim(this.nodeValue));\n                }).wrapAll('<span class=\\'' + LINK + '\\'/>');\n            }\n            updateArrow(item);\n            updateFirstLast(item);\n        }\n        function updateArrow(item) {\n            item = $(item);\n            item.find('> .k-link > [class*=k-i-arrow-60]:not(.k-sprite)').remove();\n            item.filter(':has(.k-menu-group)').children('.k-link:not(:has([class*=k-i-arrow]:not(.k-sprite)))').each(function () {\n                var item = $(this), arrowCssClass = getArrowCssClass(item);\n                item.append('<span class=\\'k-icon' + arrowCssClass + ' k-menu-expand-arrow\\'/>');\n            });\n        }\n        function getArrowCssClass(item) {\n            var arrowCssClass, parent = item.parent().parent(), isRtl = kendo.support.isRtl(parent);\n            if (parent.hasClass(MENU + '-horizontal')) {\n                arrowCssClass = ' k-i-arrow-60-down';\n            } else {\n                if (isRtl) {\n                    arrowCssClass = ' k-i-arrow-60-left';\n                } else {\n                    arrowCssClass = ' k-i-arrow-60-right';\n                }\n            }\n            return arrowCssClass;\n        }\n        function updateFirstLast(item) {\n            item = $(item);\n            item.filter('.k-first:not(:first-child)').removeClass(FIRST);\n            item.filter('.k-last:not(:last-child)').removeClass(LAST);\n            item.filter(':first-child').addClass(FIRST);\n            item.filter(':last-child').addClass(LAST);\n        }\n        function updateHasAriaPopup(parents) {\n            if (parents && parents.length) {\n                for (var index in parents) {\n                    var parentLi = parents.eq(index);\n                    if (parentLi.find('ul').length) {\n                        parentLi.attr('aria-haspopup', true);\n                    } else {\n                        parentLi.removeAttr('aria-haspopup');\n                    }\n                }\n            }\n        }\n        function getParentLiItems(group) {\n            if (!group.hasClass(MENU)) {\n                return group.parentsUntil('.' + MENU, 'li');\n            }\n        }\n        function storeItemSelectEventHandler(element, options) {\n            var selectHandler = getItemSelectEventHandler(options);\n            if (selectHandler) {\n                setItemData(element, selectHandler);\n            }\n            if (options.items) {\n                $(element).children('ul').children('li').each(function (i) {\n                    storeItemSelectEventHandler(this, options.items[i]);\n                });\n            }\n        }\n        function setItemData(element, selectHandler) {\n            $(element).children('.k-link').data({ selectHandler: selectHandler });\n        }\n        function getItemSelectEventHandler(options) {\n            var selectHandler = options.select, isFunction = kendo.isFunction;\n            if (selectHandler && isFunction(selectHandler)) {\n                return selectHandler;\n            }\n            return null;\n        }\n        function popupOpenerSelector(id) {\n            return id ? 'li[data-groupparent=\\'' + id + '\\']' : 'li[data-groupparent]';\n        }\n        function popupGroupSelector(id) {\n            return id ? 'ul[data-group=\\'' + id + '\\']' : 'ul[data-group]';\n        }\n        function getChildPopups(currentPopup, overflowWrapper) {\n            var childPopupOpener = currentPopup.find(popupOpenerSelector());\n            var result = [];\n            childPopupOpener.each(function (i, opener) {\n                opener = $(opener);\n                var popupId = opener.data(POPUP_OPENER_ATTR);\n                var popup = currentPopup;\n                while (popupId) {\n                    popup = overflowWrapper.find(popupGroupSelector(popupId) + ':visible');\n                    if (popup.length) {\n                        result.push(popup);\n                    }\n                    opener = popup.find(popupOpenerSelector());\n                    popupId = opener.data(POPUP_OPENER_ATTR);\n                }\n            });\n            return result;\n        }\n        function popupParentItem(popupElement, overflowWrapper) {\n            var popupId = popupElement.data(POPUP_ID_ATTR);\n            return popupId ? overflowWrapper.find(popupOpenerSelector(popupId)) : $([]);\n        }\n        function itemPopup(item, overflowWrapper) {\n            var popupId = item.data(POPUP_OPENER_ATTR);\n            return popupId ? overflowWrapper.children(animationContainerSelector).children(popupGroupSelector(popupId)) : $([]);\n        }\n        function overflowMenuParents(current, overflowWrapper) {\n            var parents = [];\n            var getParents = function (item) {\n                while (item.parentNode && !overflowWrapper.is(item.parentNode)) {\n                    parents.push(item.parentNode);\n                    item = item.parentNode;\n                }\n            };\n            var elem = current[0] || current;\n            getParents(elem);\n            var last = parents[parents.length - 1];\n            while ($(last).is(animationContainerSelector)) {\n                var popupElement = $(last).children('ul');\n                elem = popupParentItem(popupElement, overflowWrapper)[0];\n                if (!elem) {\n                    break;\n                }\n                parents.push(elem);\n                getParents(elem);\n                last = parents[parents.length - 1];\n            }\n            return parents;\n        }\n        function mousewheelDelta(e) {\n            var delta = 0;\n            if (e.wheelDelta) {\n                delta = -e.wheelDelta / 120;\n                delta = delta > 0 ? Math.ceil(delta) : Math.floor(delta);\n            }\n            if (e.detail) {\n                delta = Math.round(e.detail / 3);\n            }\n            return delta;\n        }\n        function parentsScroll(current, scrollDirection) {\n            var scroll = 0;\n            var parent = current.parentNode;\n            while (parent && !isNaN(parent[scrollDirection])) {\n                scroll += parent[scrollDirection];\n                parent = parent.parentNode;\n            }\n            return scroll;\n        }\n        function isPointerTouch(e) {\n            return allPointers && e.originalEvent && e.originalEvent.pointerType in touchPointerTypes;\n        }\n        function isTouch(e) {\n            var ev = e.originalEvent;\n            return touch && /touch/i.test(ev.type || '');\n        }\n        function removeSpacesBetweenItems(ul) {\n            ul.contents().filter(function () {\n                return this.nodeName != 'LI';\n            }).remove();\n        }\n        var Menu = kendo.ui.DataBoundWidget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                element = that.wrapper = that.element;\n                options = that.options;\n                that._accessors();\n                that._templates();\n                that._dataSource();\n                that._updateClasses();\n                that._animations(options);\n                that.nextItemZIndex = 100;\n                that._tabindex();\n                that._initOverflow(options);\n                that._attachMenuEventsHandlers();\n                if (options.openOnClick) {\n                    that.clicked = false;\n                }\n                element.attr('role', 'menubar');\n                if (element[0].id) {\n                    that._ariaId = kendo.format('{0}_mn_active', element[0].id);\n                }\n                kendo.notify(that);\n            },\n            events: [\n                OPEN,\n                CLOSE,\n                ACTIVATE,\n                DEACTIVATE,\n                SELECT,\n                DATABOUND\n            ],\n            options: {\n                name: 'Menu',\n                animation: {\n                    open: { duration: 200 },\n                    close: { duration: 100 }\n                },\n                orientation: 'horizontal',\n                direction: 'default',\n                openOnClick: false,\n                closeOnClick: true,\n                hoverDelay: 100,\n                scrollable: false,\n                popupCollision: undefined\n            },\n            _initData: function () {\n                var that = this;\n                if (that.dataSource) {\n                    that.angular('cleanup', function () {\n                        return { elements: that.element.children() };\n                    });\n                    that.element.empty();\n                    that.append(that.dataSource.view(), that.element);\n                    that.angular('compile', function () {\n                        return { elements: that.element.children() };\n                    });\n                }\n            },\n            _attachMenuEventsHandlers: function () {\n                var that = this;\n                var element = that.element;\n                var options = that.options;\n                var overflowWrapper = that._overflowWrapper();\n                (overflowWrapper || element).on(POINTERDOWN, itemSelector, proxy(that._focusHandler, that)).on(CLICK + NS, disabledSelector, false).on(CLICK + NS, itemSelector, proxy(that._click, that)).on(POINTERDOWN + ' ' + MOUSEDOWN + NS, '.k-content', proxy(that._preventClose, that)).on(MOUSEENTER + NS, availableItemsSelector, proxy(that._mouseenter, that)).on(MOUSELEAVE + NS, availableItemsSelector, proxy(that._mouseleave, that)).on(MOUSEDOWN + NS, availableItemsSelector, proxy(that._mousedown, that)).on(TOUCHSTART + NS + ' ' + MOUSEENTER + NS + ' ' + MOUSELEAVE + NS + ' ' + MOUSEDOWN + NS + ' ' + CLICK + NS, linkSelector, proxy(that._toggleHover, that));\n                element.on('keydown' + NS, proxy(that._keydown, that)).on('focus' + NS, proxy(that._focus, that)).on('focus' + NS, '.k-content', proxy(that._focus, that)).on('blur' + NS, proxy(that._removeHoverItem, that)).on('blur' + NS, '[tabindex]', proxy(that._checkActiveElement, that));\n                if (overflowWrapper) {\n                    overflowWrapper.on(MOUSELEAVE + NS, popupSelector, proxy(that._mouseleavePopup, that)).on(MOUSEENTER + NS, popupSelector, proxy(that._mouseenterPopup, that));\n                }\n                if (options.openOnClick) {\n                    that._documentClickHandler = proxy(that._documentClick, that);\n                    $(document).click(that._documentClickHandler);\n                }\n            },\n            _detachMenuEventsHandlers: function () {\n                var that = this;\n                var overflowWrapper = that._overflowWrapper();\n                if (overflowWrapper) {\n                    overflowWrapper.off(NS);\n                }\n                that.element.off(NS);\n                if (that._documentClickHandler) {\n                    $(document).unbind('click', that._documentClickHandler);\n                }\n            },\n            _initOverflow: function (options) {\n                var that = this;\n                var isHorizontal = options.orientation == 'horizontal';\n                var backwardBtn, forwardBtn;\n                if (options.scrollable) {\n                    that._openedPopups = {};\n                    that._scrollWrapper = that.element.wrap('<div class=\\'k-menu-scroll-wrapper ' + options.orientation + '\\'></div>').parent();\n                    if (isHorizontal) {\n                        removeSpacesBetweenItems(that.element);\n                    }\n                    backwardBtn = $(that.templates.scrollButton({ direction: isHorizontal ? 'left' : 'up' }));\n                    forwardBtn = $(that.templates.scrollButton({ direction: isHorizontal ? 'right' : 'down' }));\n                    backwardBtn.add(forwardBtn).appendTo(that._scrollWrapper);\n                    that._initScrolling(that.element, backwardBtn, forwardBtn, isHorizontal);\n                    var initialWidth = that.element.outerWidth();\n                    var initialCssWidth = that.element[0].style.width;\n                    initialCssWidth = initialCssWidth === 'auto' ? '' : initialCssWidth;\n                    if (isHorizontal) {\n                        $(window).on(RESIZE, kendo.throttle(function () {\n                            that._setOverflowWrapperWidth(initialWidth, initialCssWidth);\n                            that._toggleScrollButtons(that.element, backwardBtn, forwardBtn, isHorizontal);\n                        }, 100));\n                    }\n                    that._setOverflowWrapperWidth(initialWidth, initialCssWidth);\n                    that._toggleScrollButtons(that.element, backwardBtn, forwardBtn, isHorizontal);\n                }\n            },\n            _overflowWrapper: function () {\n                return this._scrollWrapper || this._popupsWrapper;\n            },\n            _setOverflowWrapperWidth: function (initialWidth, initialCssWidth) {\n                var that = this;\n                var wrapperCssWidth = that._scrollWrapper.css('width');\n                that._scrollWrapper.css({ width: '' });\n                var wrapperWidth = that._scrollWrapper.outerWidth();\n                that._scrollWrapper.css({ width: wrapperCssWidth });\n                var menuWidth = that.element.outerWidth();\n                var borders = that.element[0].offsetWidth - that.element[0].clientWidth;\n                if (menuWidth != wrapperWidth && wrapperWidth > 0) {\n                    var width = initialCssWidth ? Math.min(initialWidth, wrapperWidth) : wrapperWidth;\n                    that.element.width(width - borders);\n                    that._scrollWrapper.width(width);\n                }\n            },\n            _reinitOverflow: function (options) {\n                var that = this;\n                var overflowChanged = options.scrollable && !that.options.scrollable || !options.scrollable && that.options.scrollable || options.scrollable && that.options.scrollable && options.scrollable.distance != that.options.scrollable.distance || options.orientation != that.options.orientation;\n                if (overflowChanged) {\n                    that._detachMenuEventsHandlers();\n                    that._destroyOverflow();\n                    that._initOverflow(options);\n                    that._attachMenuEventsHandlers();\n                }\n            },\n            _destroyOverflow: function () {\n                var that = this;\n                var overflowWrapper = that._overflowWrapper();\n                if (overflowWrapper) {\n                    overflowWrapper.off(NS);\n                    overflowWrapper.find(scrollButtonSelector).off(NS).remove();\n                    overflowWrapper.children(animationContainerSelector).each(function (i, popupWrapper) {\n                        var ul = $(popupWrapper).children(groupSelector);\n                        ul.off(MOUSEWHEEL);\n                        var popupParentLi = popupParentItem(ul, overflowWrapper);\n                        if (popupParentLi.length) {\n                            popupParentLi.append(popupWrapper);\n                        }\n                    });\n                    overflowWrapper.find(popupOpenerSelector()).removeAttr('data-groupparent');\n                    overflowWrapper.find(popupGroupSelector()).removeAttr('data-group');\n                    that.element.off(MOUSEWHEEL);\n                    $(window).off(RESIZE);\n                    overflowWrapper.contents().unwrap();\n                    that._scrollWrapper = that._popupsWrapper = that._openedPopups = undefined;\n                }\n            },\n            _initScrolling: function (scrollElement, backwardBtn, forwardBtn, isHorizontal) {\n                var that = this;\n                var scrollable = that.options.scrollable;\n                var distance = $.isNumeric(scrollable.distance) ? scrollable.distance : SCROLLSPEED;\n                var mouseWheelDistance = distance / 2;\n                var backward = '-=' + distance;\n                var forward = '+=' + distance;\n                var backwardDouble = '-=' + distance * 2;\n                var forwardDouble = '+=' + distance * 2;\n                var scrolling = false;\n                var touchEvents = false;\n                var scroll = function (value) {\n                    var scrollValue = isHorizontal ? { 'scrollLeft': value } : { 'scrollTop': value };\n                    scrollElement.finish().animate(scrollValue, 'fast', 'linear', function () {\n                        if (scrolling) {\n                            scroll(value);\n                        }\n                    });\n                    that._toggleScrollButtons(scrollElement, backwardBtn, forwardBtn, isHorizontal);\n                };\n                var mouseenterHandler = function (e) {\n                    if (!scrolling && !touchEvents) {\n                        scroll(e.data.direction);\n                        scrolling = true;\n                    }\n                };\n                var mousedownHandler = function (e) {\n                    var scrollValue = isHorizontal ? { 'scrollLeft': e.data.direction } : { 'scrollTop': e.data.direction };\n                    touchEvents = isTouch(e) || isPointerTouch(e);\n                    scrollElement.stop().animate(scrollValue, 'fast', 'linear', function () {\n                        if (!touchEvents) {\n                            $(e.currentTarget).trigger(MOUSEENTER);\n                        } else {\n                            that._toggleScrollButtons(scrollElement, backwardBtn, forwardBtn, isHorizontal);\n                            scrolling = true;\n                        }\n                    });\n                    scrolling = false;\n                    e.stopPropagation();\n                    e.preventDefault();\n                };\n                backwardBtn.on(MOUSEENTER + NS, { direction: backward }, mouseenterHandler).on(kendo.eventMap.down + NS, { direction: backwardDouble }, mousedownHandler);\n                forwardBtn.on(MOUSEENTER + NS, { direction: forward }, mouseenterHandler).on(kendo.eventMap.down + NS, { direction: forwardDouble }, mousedownHandler);\n                backwardBtn.add(forwardBtn).on(MOUSELEAVE + NS, function () {\n                    scrollElement.stop();\n                    scrolling = false;\n                    that._toggleScrollButtons(scrollElement, backwardBtn, forwardBtn, isHorizontal);\n                });\n                scrollElement.on(MOUSEWHEEL, function (e) {\n                    if (!e.ctrlKey && !e.shiftKey && !e.altKey) {\n                        var wheelDelta = mousewheelDelta(e.originalEvent);\n                        var scrollSpeed = Math.abs(wheelDelta) * mouseWheelDistance;\n                        var value = (wheelDelta > 0 ? '+=' : '-=') + scrollSpeed;\n                        var scrollValue = isHorizontal ? { 'scrollLeft': value } : { 'scrollTop': value };\n                        that._closeChildPopups(scrollElement);\n                        scrollElement.finish().animate(scrollValue, 'fast', 'linear', function () {\n                            that._toggleScrollButtons(scrollElement, backwardBtn, forwardBtn, isHorizontal);\n                        });\n                        e.preventDefault();\n                    }\n                });\n            },\n            _toggleScrollButtons: function (scrollElement, backwardBtn, forwardBtn, horizontal) {\n                var currentScroll = horizontal ? scrollElement.scrollLeft() : scrollElement.scrollTop();\n                var scrollSize = horizontal ? SCROLLWIDTH : SCROLLHEIGHT;\n                var offset = horizontal ? OFFSETWIDTH : OFFSETHEIGHT;\n                backwardBtn.toggle(currentScroll !== 0);\n                forwardBtn.toggle(currentScroll < scrollElement[0][scrollSize] - scrollElement[0][offset] - 1);\n            },\n            setOptions: function (options) {\n                var animation = this.options.animation;\n                this._animations(options);\n                options.animation = extend(true, animation, options.animation);\n                if ('dataSource' in options) {\n                    this._dataSource(options);\n                }\n                this._updateClasses();\n                this._reinitOverflow(options);\n                Widget.fn.setOptions.call(this, options);\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that._detachMenuEventsHandlers();\n                that._destroyOverflow();\n                kendo.destroy(that.element);\n            },\n            enable: function (element, enable) {\n                this._toggleDisabled(element, enable !== false);\n                return this;\n            },\n            disable: function (element) {\n                this._toggleDisabled(element, false);\n                return this;\n            },\n            attemptGetItem: function (candidate) {\n                candidate = candidate || this.element;\n                var item = this.element.find(candidate);\n                var overflowWrapper = this._overflowWrapper();\n                if (item.length || candidate === this.element) {\n                    return item;\n                } else if (overflowWrapper) {\n                    return overflowWrapper.find(candidate);\n                } else {\n                    return $();\n                }\n            },\n            append: function (item, referenceItem) {\n                referenceItem = this.attemptGetItem(referenceItem);\n                var inserted = this._insert(item, referenceItem, referenceItem.length ? referenceItem.find('> .k-menu-group, > .k-animation-container > .k-menu-group') : null);\n                each(inserted.items, function (i) {\n                    inserted.group.append(this);\n                    updateArrow(this);\n                    storeItemSelectEventHandler(this, item[i] || item);\n                });\n                updateArrow(referenceItem);\n                updateFirstLast(inserted.group.find('.k-first, .k-last').add(inserted.items));\n                updateHasAriaPopup(getParentLiItems(inserted.group));\n                return this;\n            },\n            insertBefore: function (item, referenceItem) {\n                referenceItem = this.attemptGetItem(referenceItem);\n                var inserted = this._insert(item, referenceItem, referenceItem.parent());\n                each(inserted.items, function (i) {\n                    referenceItem.before(this);\n                    updateArrow(this);\n                    updateFirstLast(this);\n                    storeItemSelectEventHandler(this, item[i] || item);\n                });\n                updateFirstLast(referenceItem);\n                return this;\n            },\n            insertAfter: function (item, referenceItem) {\n                referenceItem = this.attemptGetItem(referenceItem);\n                var inserted = this._insert(item, referenceItem, referenceItem.parent());\n                each(inserted.items, function (i) {\n                    referenceItem.after(this);\n                    updateArrow(this);\n                    updateFirstLast(this);\n                    storeItemSelectEventHandler(this, item[i] || item);\n                });\n                updateFirstLast(referenceItem);\n                return this;\n            },\n            _insert: function (item, referenceItem, parent) {\n                var that = this, items, groups;\n                if (!referenceItem || !referenceItem.length) {\n                    parent = that.element;\n                }\n                var plain = $.isPlainObject(item) || item instanceof kendo.data.ObservableObject, groupData = {\n                        firstLevel: parent.hasClass(MENU),\n                        horizontal: parent.hasClass(MENU + '-horizontal'),\n                        expanded: true,\n                        length: parent.children().length\n                    };\n                if (referenceItem && !parent.length) {\n                    parent = $(that.renderGroup({\n                        group: groupData,\n                        options: that.options\n                    })).appendTo(referenceItem);\n                }\n                if (plain || isArray(item) || item instanceof kendo.data.ObservableArray) {\n                    items = $($.map(plain ? [item] : item, function (value, idx) {\n                        if (typeof value === 'string') {\n                            return $(value).get();\n                        } else {\n                            return $(that.renderItem({\n                                group: groupData,\n                                item: extend(value, { index: idx })\n                            })).get();\n                        }\n                    }));\n                } else {\n                    if (typeof item == 'string' && item.charAt(0) != '<') {\n                        items = that.element.find(item);\n                    } else {\n                        items = $(item);\n                    }\n                    groups = items.find('> ul').addClass('k-menu-group').attr('role', 'menu');\n                    items = items.filter('li');\n                    items.add(groups.find('> li')).each(function () {\n                        updateItemClasses(this);\n                    });\n                }\n                return {\n                    items: items,\n                    group: parent\n                };\n            },\n            remove: function (element) {\n                element = this.attemptGetItem(element);\n                var that = this, parent = element.parentsUntil(that.element, allItemsSelector), group = element.parent('ul:not(.k-menu)');\n                element.remove();\n                if (group && !group.children(allItemsSelector).length) {\n                    var parentItems = getParentLiItems(group);\n                    var container = group.parent(animationContainerSelector);\n                    if (container.length) {\n                        container.remove();\n                    } else {\n                        group.remove();\n                    }\n                    updateHasAriaPopup(parentItems);\n                }\n                if (parent.length) {\n                    parent = parent.eq(0);\n                    updateArrow(parent);\n                    updateFirstLast(parent);\n                }\n                return that;\n            },\n            _openAfterLoad: function (element, dataItem) {\n                var that = this;\n                if (dataItem.loaded()) {\n                    that.open(element);\n                    that._loading = false;\n                } else {\n                    dataItem.one(CHANGE, function () {\n                        element.find(ICON_SELECTOR).removeClass('k-i-loading');\n                        if (that._loading) {\n                            that.open(element);\n                            that._loading = false;\n                        }\n                    });\n                }\n            },\n            open: function (element) {\n                var that = this;\n                var options = that.options;\n                var horizontal = options.orientation == 'horizontal';\n                var direction = options.direction;\n                var isRtl = kendo.support.isRtl(that.wrapper);\n                var overflowWrapper = that._overflowWrapper();\n                element = (overflowWrapper || that.element).find(element);\n                var dataItem = that.dataSource && that.dataSource.getByUid(element.data('uid'));\n                if (dataItem && dataItem.hasChildren && !dataItem.loaded() && !that._loading) {\n                    that._loading = true;\n                    element.find(ICON_SELECTOR).addClass('k-i-loading');\n                    dataItem.load();\n                    that._openAfterLoad(element, dataItem);\n                    return;\n                }\n                if (/^(top|bottom|default)$/.test(direction)) {\n                    if (isRtl) {\n                        direction = horizontal ? (direction + ' left').replace('default', 'bottom') : 'left';\n                    } else {\n                        direction = horizontal ? (direction + ' right').replace('default', 'bottom') : 'right';\n                    }\n                }\n                var visiblePopups = '>.k-popup:visible,>.k-animation-container>.k-popup:visible';\n                var closePopup = function () {\n                    var popup = $(this).data(KENDOPOPUP);\n                    if (popup) {\n                        that.close($(this).closest('li.k-item'), true);\n                    }\n                };\n                element.siblings().find(visiblePopups).each(closePopup);\n                if (overflowWrapper) {\n                    element.find(visiblePopups).each(closePopup);\n                }\n                if (that.options.openOnClick) {\n                    that.clicked = true;\n                }\n                element.each(function () {\n                    var li = $(this);\n                    clearTimeout(li.data(TIMER));\n                    li.data(TIMER, setTimeout(function () {\n                        var ul = li.find('.k-menu-group:first:hidden');\n                        var popup;\n                        var overflowPopup;\n                        if (!ul[0] && overflowWrapper) {\n                            overflowPopup = that._getPopup(li);\n                            ul = overflowPopup && overflowPopup.element;\n                        }\n                        if (ul.is(':visible')) {\n                            return;\n                        }\n                        if (ul[0] && that._triggerEvent({\n                                item: li[0],\n                                type: OPEN\n                            }) === false) {\n                            if (!ul.find('.k-menu-group')[0] && ul.children('.k-item').length > 1) {\n                                var windowHeight = $(window).height(), setScrolling = function () {\n                                        ul.css({\n                                            maxHeight: windowHeight - (kendo._outerHeight(ul) - ul.height()) - kendo.getShadows(ul).bottom,\n                                            overflow: 'auto'\n                                        });\n                                    };\n                                if (kendo.support.browser.msie && kendo.support.browser.version <= 7) {\n                                    setTimeout(setScrolling, 0);\n                                } else {\n                                    setScrolling();\n                                }\n                            } else {\n                                ul.css({\n                                    maxHeight: '',\n                                    overflow: ''\n                                });\n                            }\n                            li.data(ZINDEX, li.css(ZINDEX));\n                            var nextZindex = that.nextItemZIndex++;\n                            li.css(ZINDEX, nextZindex);\n                            if (that.options.scrollable) {\n                                li.parent().siblings(scrollButtonSelector).css({ zIndex: ++nextZindex });\n                            }\n                            popup = ul.data(KENDOPOPUP);\n                            var root = li.parent().hasClass(MENU), parentHorizontal = root && horizontal, directions = parseDirection(direction, root, isRtl), effects = options.animation.open.effects, openEffects = effects !== undefined ? effects : 'slideIn:' + getEffectDirection(direction, root);\n                            if (!popup) {\n                                popup = ul.kendoPopup({\n                                    activate: function () {\n                                        that._triggerEvent({\n                                            item: this.wrapper.parent(),\n                                            type: ACTIVATE\n                                        });\n                                    },\n                                    deactivate: function (e) {\n                                        that._closing = false;\n                                        e.sender.element.removeData('targetTransform').css({ opacity: '' });\n                                        that._triggerEvent({\n                                            item: this.wrapper.parent(),\n                                            type: DEACTIVATE\n                                        });\n                                    },\n                                    origin: directions.origin,\n                                    position: directions.position,\n                                    collision: options.popupCollision !== undefined ? options.popupCollision : parentHorizontal ? 'fit' : 'fit flip',\n                                    anchor: li,\n                                    appendTo: overflowWrapper || li,\n                                    animation: {\n                                        open: extend(true, { effects: openEffects }, options.animation.open),\n                                        close: options.animation.close\n                                    },\n                                    open: proxy(that._popupOpen, that),\n                                    close: function (e) {\n                                        that._closing = true;\n                                        var li = e.sender.wrapper.parent();\n                                        if (overflowWrapper) {\n                                            var popupId = e.sender.element.data(POPUP_ID_ATTR);\n                                            if (popupId) {\n                                                li = (overflowWrapper || that.element).find(popupOpenerSelector(popupId));\n                                            }\n                                            e.sender.wrapper.children(scrollButtonSelector).hide();\n                                        }\n                                        if (!that._triggerEvent({\n                                                item: li[0],\n                                                type: CLOSE\n                                            })) {\n                                            li.css(ZINDEX, li.data(ZINDEX));\n                                            li.removeData(ZINDEX);\n                                            if (that.options.scrollable) {\n                                                li.parent().siblings(scrollButtonSelector).css({ zIndex: '' });\n                                            }\n                                            if (touch || allPointers || kendo.support.mouseAndTouchPresent) {\n                                                li.removeClass(HOVERSTATE);\n                                                that._removeHoverItem();\n                                            }\n                                        } else {\n                                            e.preventDefault();\n                                        }\n                                    }\n                                }).data(KENDOPOPUP);\n                            } else {\n                                popup = ul.data(KENDOPOPUP);\n                                popup.options.origin = directions.origin;\n                                popup.options.position = directions.position;\n                                popup.options.animation.open.effects = openEffects;\n                            }\n                            ul.removeAttr('aria-hidden');\n                            that._configurePopupOverflow(popup, li);\n                            popup._hovered = true;\n                            popup.open();\n                            that._initPopupScrolling(popup);\n                        }\n                    }, that.options.hoverDelay));\n                });\n                return that;\n            },\n            _configurePopupOverflow: function (popup, popupOpener) {\n                var that = this;\n                if (that.options.scrollable) {\n                    that._wrapPopupElement(popup);\n                    if (!popupOpener.attr('data-groupparent')) {\n                        var groupId = new Date().getTime();\n                        popupOpener.attr('data-groupparent', groupId);\n                        popup.element.attr('data-group', groupId);\n                    }\n                }\n            },\n            _wrapPopupElement: function (popup) {\n                if (!popup.element.parent().is(animationContainerSelector)) {\n                    popup.wrapper = kendo.wrap(popup.element, popup.options.autosize).css({\n                        overflow: 'hidden',\n                        display: 'block',\n                        position: 'absolute'\n                    });\n                }\n            },\n            _initPopupScrolling: function (popup, isHorizontal, skipMouseEvents) {\n                var that = this;\n                if (that.options.scrollable && popup.element[0].scrollHeight > popup.element[0].offsetHeight) {\n                    that._initPopupScrollButtons(popup, isHorizontal, skipMouseEvents);\n                }\n            },\n            _initPopupScrollButtons: function (popup, isHorizontal, skipMouseEvents) {\n                var that = this;\n                var scrollButtons = popup.wrapper.children(scrollButtonSelector);\n                var animation = that.options.animation;\n                var timeout = (animation && animation.open && animation.open.duration || 0) + DELAY;\n                setTimeout(function () {\n                    if (!scrollButtons.length) {\n                        var backwardBtn = $(that.templates.scrollButton({ direction: isHorizontal ? 'left' : 'up' }));\n                        var forwardBtn = $(that.templates.scrollButton({ direction: isHorizontal ? 'right' : 'down' }));\n                        scrollButtons = backwardBtn.add(forwardBtn).appendTo(popup.wrapper);\n                        that._initScrolling(popup.element, backwardBtn, forwardBtn, isHorizontal);\n                        if (!skipMouseEvents) {\n                            scrollButtons.on(MOUSEENTER + NS, function () {\n                                var overflowWrapper = that._overflowWrapper();\n                                $(getChildPopups(popup.element, overflowWrapper)).each(function (i, p) {\n                                    var popupOpener = overflowWrapper.find(popupOpenerSelector(p.data(POPUP_ID_ATTR)));\n                                    that.close(popupOpener);\n                                });\n                            }).on(MOUSELEAVE + NS, function () {\n                                setTimeout(function () {\n                                    if ($.isEmptyObject(that._openedPopups)) {\n                                        that._closeParentPopups(popup.element);\n                                    }\n                                }, DELAY);\n                            });\n                        }\n                    }\n                    that._toggleScrollButtons(popup.element, scrollButtons.first(), scrollButtons.last(), isHorizontal);\n                }, timeout);\n            },\n            _popupOpen: function (e) {\n                if (!this._keyTriggered) {\n                    e.sender.element.children('.' + FOCUSEDSTATE).removeClass(FOCUSEDSTATE);\n                }\n                if (this.options.scrollable) {\n                    this._setPopupHeight(e.sender);\n                }\n            },\n            _setPopupHeight: function (popup, isFixed) {\n                var popupElement = popup.element;\n                var popups = popupElement.add(popupElement.parent(animationContainerSelector));\n                popups.height(popupElement.hasClass(MENU) && this._initialHeight || '');\n                var location = popup._location(isFixed);\n                var windowHeight = $(window).height();\n                var popupOuterHeight = location.height;\n                var popupOffsetTop = isFixed ? 0 : Math.max(location.top, 0);\n                var scrollTop = isFixed ? 0 : parentsScroll(this._overflowWrapper()[0], 'scrollTop');\n                var bottomScrollbar = window.innerHeight - windowHeight;\n                var maxHeight = windowHeight - kendo.getShadows(popupElement).bottom + bottomScrollbar;\n                var canFit = maxHeight + scrollTop > popupOuterHeight + popupOffsetTop;\n                if (!canFit) {\n                    var height = Math.min(maxHeight, maxHeight - popupOffsetTop + scrollTop);\n                    popups.css({\n                        overflow: 'hidden',\n                        height: height + 'px'\n                    });\n                }\n            },\n            close: function (items, dontClearClose) {\n                var that = this;\n                var overflowWrapper = that._overflowWrapper();\n                var element = overflowWrapper || that.element;\n                items = element.find(items);\n                if (!items.length) {\n                    items = element.find('>.k-item');\n                }\n                var hasChildPopupsHovered = function (currentPopup) {\n                    var result = false;\n                    if ($.isEmptyObject(that._openedPopups)) {\n                        return result;\n                    }\n                    $(getChildPopups(currentPopup, overflowWrapper)).each(function (i, popup) {\n                        result = !!that._openedPopups[popup.data(POPUP_ID_ATTR).toString()];\n                        return !result;\n                    });\n                    return result;\n                };\n                var isPopupMouseLeaved = function (opener) {\n                    var groupId = opener.data(POPUP_OPENER_ATTR);\n                    return !overflowWrapper || !groupId || !that._openedPopups[groupId.toString()];\n                };\n                items.each(function () {\n                    var li = $(this);\n                    if (!dontClearClose && that._isRootItem(li)) {\n                        that.clicked = false;\n                    }\n                    clearTimeout(li.data(TIMER));\n                    li.data(TIMER, setTimeout(function () {\n                        var popup = that._getPopup(li);\n                        if (popup && (isPopupMouseLeaved(li) || that._forceClose)) {\n                            if (!that._forceClose && hasChildPopupsHovered(popup.element)) {\n                                return;\n                            }\n                            popup.close();\n                            popup.element.attr('aria-hidden', true);\n                            if (overflowWrapper) {\n                                if (that._forceClose && items.last().is(li[0])) {\n                                    delete that._forceClose;\n                                }\n                            }\n                        }\n                    }, that.options.hoverDelay));\n                });\n                return that;\n            },\n            _getPopup: function (li) {\n                var that = this;\n                var popup = li.find('.k-menu-group:not(.k-list-container):not(.k-calendar-container):first:visible').data(KENDOPOPUP);\n                var overflowWrapper = that._overflowWrapper();\n                if (!popup && overflowWrapper) {\n                    var groupId = li.data(POPUP_OPENER_ATTR);\n                    if (groupId) {\n                        var popupElement = overflowWrapper.find(popupGroupSelector(groupId));\n                        popup = popupElement.data(KENDOPOPUP);\n                    }\n                }\n                return popup;\n            },\n            _toggleDisabled: function (items, enable) {\n                this.element.find(items).each(function () {\n                    $(this).toggleClass(DEFAULTSTATE, enable).toggleClass(DISABLEDSTATE, !enable).attr('aria-disabled', !enable);\n                });\n            },\n            _toggleHover: function (e) {\n                var target = $(kendo.eventTarget(e) || e.target).closest(allItemsSelector), isEnter = e.type == MOUSEENTER || MOUSEDOWN.indexOf(e.type) !== -1;\n                target.siblings().removeClass(HOVERSTATE);\n                if (!target.parents('li.' + DISABLEDSTATE).length) {\n                    target.toggleClass(HOVERSTATE, isEnter || e.type == 'mousedown' || e.type == 'pointerover' || e.type == TOUCHSTART);\n                }\n                this._removeHoverItem();\n            },\n            _preventClose: function () {\n                if (!this.options.closeOnClick) {\n                    this._closurePrevented = true;\n                }\n            },\n            _checkActiveElement: function (e) {\n                var that = this, hoverItem = $(e ? e.currentTarget : this._hoverItem()), target = that._findRootParent(hoverItem)[0];\n                if (!this._closurePrevented) {\n                    setTimeout(function () {\n                        if (!document.hasFocus() || !contains(target, kendo._activeElement()) && e && !contains(target, e.currentTarget)) {\n                            that.close(target);\n                        }\n                    }, 0);\n                }\n                this._closurePrevented = false;\n            },\n            _removeHoverItem: function () {\n                var oldHoverItem = this._hoverItem();\n                if (oldHoverItem && oldHoverItem.hasClass(FOCUSEDSTATE)) {\n                    oldHoverItem.removeClass(FOCUSEDSTATE);\n                    this._oldHoverItem = null;\n                }\n            },\n            _updateClasses: function () {\n                var element = this.element, nonContentGroupsSelector = '.k-menu-init div ul', items;\n                element.removeClass('k-menu-horizontal k-menu-vertical');\n                element.addClass('k-widget k-reset k-header k-menu-init ' + MENU).addClass(MENU + '-' + this.options.orientation);\n                element.find('li > ul').filter(function () {\n                    return !kendo.support.matchesSelector.call(this, nonContentGroupsSelector);\n                }).addClass('k-group k-menu-group').attr('role', 'menu').attr('aria-hidden', element.is(':visible')).parent('li').attr('aria-haspopup', 'true').end().find('li > div').addClass('k-content').attr('tabindex', '-1');\n                items = element.find('> li,.k-menu-group > li');\n                element.removeClass('k-menu-init');\n                items.each(function () {\n                    updateItemClasses(this);\n                });\n            },\n            _mouseenter: function (e) {\n                var that = this;\n                var element = $(e.currentTarget);\n                var hasChildren = that._itemHasChildren(element);\n                var popupId = element.data(POPUP_OPENER_ATTR) || element.parent().data(POPUP_ID_ATTR);\n                var pointerTouch = isPointerTouch(e);\n                if (popupId) {\n                    that._openedPopups[popupId.toString()] = true;\n                }\n                if (that._closing || e.delegateTarget != element.parents(menuSelector)[0] && e.delegateTarget != element.parents('.k-menu-scroll-wrapper,.k-popups-wrapper')[0]) {\n                    return;\n                }\n                that._keyTriggered = false;\n                if (that.options.openOnClick.rootMenuItems && that._isRootItem(element.closest(allItemsSelector)) || that.options.openOnClick.subMenuItems && !that._isRootItem(element.closest(allItemsSelector))) {\n                    return;\n                }\n                if ((that.options.openOnClick === false || that.options.openOnClick.rootMenuItems === false && that._isRootItem(element.closest(allItemsSelector)) || that.options.openOnClick.subMenuItems === false && !that._isRootItem(element.closest(allItemsSelector)) || that.clicked) && !touch && !(pointerTouch && that._isRootItem(element.closest(allItemsSelector)))) {\n                    if (!contains(e.currentTarget, e.relatedTarget) && hasChildren) {\n                        that.open(element);\n                    }\n                }\n                if (that.options.openOnClick === true && that.clicked || touch) {\n                    element.siblings().each(proxy(function (_, sibling) {\n                        that.close(sibling, true);\n                    }, that));\n                }\n            },\n            _mousedown: function (e) {\n                var that = this;\n                var element = $(e.currentTarget);\n                if (that.options.openOnClick.subMenuItems && !that._isRootItem(element) || touch) {\n                    element.siblings().each(proxy(function (_, sibling) {\n                        that.close(sibling, true);\n                    }, that));\n                }\n            },\n            _mouseleave: function (e) {\n                var that = this;\n                var element = $(e.currentTarget);\n                var popupOpener = element.data(POPUP_OPENER_ATTR);\n                var hasChildren = element.children(animationContainerSelector).length || element.children(groupSelector).length || popupOpener;\n                var $window = $(window);\n                if (popupOpener) {\n                    delete that._openedPopups[popupOpener.toString()];\n                }\n                if (element.parentsUntil(animationContainerSelector, '.k-list-container,.k-calendar-container')[0]) {\n                    e.stopImmediatePropagation();\n                    return;\n                }\n                if ((that.options.openOnClick === false || !that.options.openOnClick.rootMenuItems && that._isRootItem(element) || !that.options.openOnClick.subMenuItems && !that._isRootItem(element)) && !touch && !isPointerTouch(e) && !contains(e.currentTarget, e.relatedTarget || e.target) && hasChildren && !contains(e.currentTarget, kendo._activeElement())) {\n                    that.close(element, true);\n                    that._loading = false;\n                    return;\n                }\n                if (kendo.support.browser.msie && !e.toElement && !e.relatedTarget && !isPointerTouch(e) || e.clientX < 0 || e.clientY < 0 || e.clientY > $window.height() || e.clientX > $window.width()) {\n                    that.close(element);\n                }\n            },\n            _mouseenterPopup: function (e) {\n                var that = this;\n                var popupElement = $(e.currentTarget);\n                if (popupElement.parent().is(animationContainerSelector)) {\n                    return;\n                }\n                popupElement = popupElement.children('ul');\n                var popupId = popupElement.data(POPUP_ID_ATTR);\n                if (popupId) {\n                    that._openedPopups[popupId.toString()] = true;\n                }\n            },\n            _mouseleavePopup: function (e) {\n                var that = this;\n                var popupElement = $(e.currentTarget);\n                if (!isPointerTouch(e) && popupElement.is(animationContainerSelector)) {\n                    that._closePopups(popupElement.children('ul'));\n                }\n            },\n            _closePopups: function (rootPopup) {\n                var that = this;\n                var overflowWrapper = that._overflowWrapper();\n                var popupId = rootPopup.data(POPUP_ID_ATTR);\n                if (popupId) {\n                    delete that._openedPopups[popupId.toString()];\n                    var groupParent = overflowWrapper.find(popupOpenerSelector(popupId));\n                    setTimeout(function () {\n                        if (that.options.openOnClick) {\n                            that._closeChildPopups(rootPopup);\n                        } else {\n                            if ($.isEmptyObject(that._openedPopups)) {\n                                var innerPopup = that._innerPopup(rootPopup);\n                                that._closeParentPopups(innerPopup);\n                            } else {\n                                that.close(groupParent, true);\n                            }\n                        }\n                    }, 0);\n                }\n            },\n            _closeChildPopups: function (current) {\n                var that = this;\n                var overflowWrapper = that._overflowWrapper();\n                $(getChildPopups(current, overflowWrapper)).each(function () {\n                    var popupOpener = overflowWrapper.find(popupOpenerSelector(this.data(POPUP_ID_ATTR)));\n                    that.close(popupOpener, true);\n                });\n            },\n            _innerPopup: function (current) {\n                var overflowWrapper = this._overflowWrapper();\n                var popups = getChildPopups(current, overflowWrapper);\n                return popups[popups.length - 1] || current;\n            },\n            _closeParentPopups: function (current) {\n                var that = this;\n                var overflowWrapper = that._overflowWrapper();\n                var popupId = current.data(POPUP_ID_ATTR);\n                var popupOpener = overflowWrapper.find(popupOpenerSelector(popupId));\n                popupId = popupOpener.parent().data(POPUP_ID_ATTR);\n                that.close(popupOpener, true);\n                while (popupId && !that._openedPopups[popupId]) {\n                    if (popupOpener.parent().is(menuSelector)) {\n                        break;\n                    }\n                    popupOpener = overflowWrapper.find(popupOpenerSelector(popupId));\n                    that.close(popupOpener, true);\n                    popupId = popupOpener.parent().data(POPUP_ID_ATTR);\n                }\n            },\n            _click: function (e) {\n                var that = this, openHandle, options = that.options, target = $(kendo.eventTarget(e)), targetElement = target[0], nodeName = target[0] ? target[0].nodeName.toUpperCase() : '', formNode = nodeName == 'INPUT' || nodeName == 'SELECT' || nodeName == 'BUTTON' || nodeName == 'LABEL', link = target.closest(LINK_SELECTOR), element = target.closest(allItemsSelector), itemElement = element[0], href = link.attr('href'), childGroup, childGroupVisible, targetHref = target.attr('href'), sampleHref = $('<a href=\\'#\\' />').attr('href'), isLink = !!href && href !== sampleHref, isLocalLink = isLink && !!href.match(/^#/), isTargetLink = !!targetHref && targetHref !== sampleHref, overflowWrapper = that._overflowWrapper(), shouldCloseTheRootItem;\n                while (targetElement && targetElement.parentNode != itemElement) {\n                    targetElement = targetElement.parentNode;\n                }\n                if ($(targetElement).is(templateSelector)) {\n                    return;\n                }\n                if (element.hasClass(DISABLEDSTATE)) {\n                    e.preventDefault();\n                    return;\n                }\n                if (!e.handled && that._triggerSelect(target, itemElement) && !formNode) {\n                    e.preventDefault();\n                }\n                e.handled = true;\n                childGroup = element.children(popupSelector);\n                if (overflowWrapper) {\n                    var childPopupId = element.data(POPUP_OPENER_ATTR);\n                    if (childPopupId) {\n                        childGroup = overflowWrapper.find(popupGroupSelector(childPopupId));\n                    }\n                }\n                childGroupVisible = childGroup.is(':visible');\n                shouldCloseTheRootItem = options.openOnClick && childGroupVisible && that._isRootItem(element);\n                if (options.closeOnClick && (!isLink || isLocalLink) && (!childGroup.length || shouldCloseTheRootItem)) {\n                    element.removeClass(HOVERSTATE).css('height');\n                    that._oldHoverItem = that._findRootParent(element);\n                    var item = that._parentsUntil(link, that.element, allItemsSelector);\n                    that._forceClose = !!overflowWrapper;\n                    that.close(item);\n                    that.clicked = false;\n                    if ('MSPointerUp'.indexOf(e.type) != -1) {\n                        e.preventDefault();\n                    }\n                    return;\n                }\n                if (isLink && e.enterKey) {\n                    link[0].click();\n                }\n                if ((!that._isRootItem(element) || options.openOnClick === false) && !options.openOnClick.subMenuItems && !kendo.support.touch && !(isPointerTouch(e) && that._isRootItem(element.closest(allItemsSelector)))) {\n                    return;\n                }\n                if (!isLink && !formNode && !isTargetLink) {\n                    e.preventDefault();\n                }\n                that.clicked = true;\n                openHandle = childGroup.is(':visible') ? CLOSE : OPEN;\n                if (!options.closeOnClick && openHandle == CLOSE) {\n                    return;\n                }\n                that[openHandle](element);\n            },\n            _parentsUntil: function (context, top, selector) {\n                var overflowWrapper = this._overflowWrapper();\n                if (!overflowWrapper) {\n                    return context.parentsUntil(top, selector);\n                } else {\n                    var parents = overflowMenuParents(context, overflowWrapper);\n                    var result = [];\n                    $(parents).each(function () {\n                        var parent = $(this);\n                        if (parent.is(top)) {\n                            return false;\n                        }\n                        if (parent.is(selector)) {\n                            result.push(this);\n                        }\n                    });\n                    return $(result);\n                }\n            },\n            _triggerSelect: function (target, itemElement) {\n                target = target.is('.k-link') ? target : target.closest('.k-link');\n                var selectHandler = target.data('selectHandler'), itemSelectEventData;\n                if (selectHandler) {\n                    itemSelectEventData = this._getEventData(target);\n                    selectHandler.call(this, itemSelectEventData);\n                }\n                var isSelectItemDefaultPrevented = itemSelectEventData && itemSelectEventData.isDefaultPrevented();\n                var isSelectDefaultPrevented = this._triggerEvent({\n                    item: itemElement,\n                    type: SELECT\n                });\n                return isSelectItemDefaultPrevented || isSelectDefaultPrevented;\n            },\n            _getEventData: function (target) {\n                var eventData = {\n                    sender: this,\n                    target: target,\n                    _defaultPrevented: false,\n                    preventDefault: function () {\n                        this._defaultPrevented = true;\n                    },\n                    isDefaultPrevented: function () {\n                        return this._defaultPrevented;\n                    }\n                };\n                return eventData;\n            },\n            _documentClick: function (e) {\n                var that = this;\n                if (contains((that._overflowWrapper() || that.element)[0], e.target)) {\n                    return;\n                }\n                that.clicked = false;\n            },\n            _focus: function (e) {\n                var that = this, target = e.target, hoverItem = that._hoverItem(), active = activeElement();\n                if (target != that.wrapper[0] && !$(target).is(':kendoFocusable')) {\n                    e.stopPropagation();\n                    $(target).closest('.k-content').closest('.k-menu-group').closest('.k-item').addClass(FOCUSEDSTATE);\n                    that.wrapper.focus();\n                    return;\n                }\n                if (active === e.currentTarget) {\n                    if (hoverItem.length) {\n                        that._moveHover([], hoverItem);\n                    } else if (!that._oldHoverItem) {\n                        that._moveHover([], that.wrapper.children().first());\n                    }\n                }\n            },\n            _keydown: function (e) {\n                var that = this, key = e.keyCode, hoverItem = that._oldHoverItem, target, belongsToVertical, hasChildren, isRtl = kendo.support.isRtl(that.wrapper);\n                if (e.target != e.currentTarget && key != keys.ESC) {\n                    return;\n                }\n                if (!hoverItem) {\n                    hoverItem = that._oldHoverItem = that._hoverItem();\n                }\n                belongsToVertical = that._itemBelongsToVertival(hoverItem);\n                hasChildren = that._itemHasChildren(hoverItem);\n                that._keyTriggered = true;\n                if (key == keys.RIGHT) {\n                    target = that[isRtl ? '_itemLeft' : '_itemRight'](hoverItem, belongsToVertical, hasChildren);\n                } else if (key == keys.LEFT) {\n                    target = that[isRtl ? '_itemRight' : '_itemLeft'](hoverItem, belongsToVertical, hasChildren);\n                } else if (key == keys.DOWN) {\n                    target = that._itemDown(hoverItem, belongsToVertical, hasChildren);\n                } else if (key == keys.UP) {\n                    target = that._itemUp(hoverItem, belongsToVertical, hasChildren);\n                } else if (key == keys.HOME) {\n                    that._moveHover(hoverItem, hoverItem.parent().children().first());\n                    e.preventDefault();\n                } else if (key == keys.END) {\n                    that._moveHover(hoverItem, hoverItem.parent().children().last());\n                    e.preventDefault();\n                } else if (key == keys.ESC) {\n                    target = that._itemEsc(hoverItem, belongsToVertical);\n                } else if (key == keys.ENTER || key == keys.SPACEBAR) {\n                    target = hoverItem.children('.k-link');\n                    if (target.length > 0) {\n                        that._click({\n                            target: target[0],\n                            preventDefault: function () {\n                            },\n                            enterKey: true\n                        });\n                        if (hasChildren && !hoverItem.hasClass(DISABLEDSTATE)) {\n                            that.open(hoverItem);\n                            that._moveHover(hoverItem, that._childPopupElement(hoverItem).children().first());\n                        } else {\n                            that._moveHover(hoverItem, that._findRootParent(hoverItem));\n                        }\n                    }\n                } else if (key == keys.TAB) {\n                    target = that._findRootParent(hoverItem);\n                    that._moveHover(hoverItem, target);\n                    that._checkActiveElement();\n                    return;\n                }\n                if (target && target[0]) {\n                    e.preventDefault();\n                    e.stopPropagation();\n                }\n            },\n            _hoverItem: function () {\n                return this.wrapper.find('.k-item.k-state-hover,.k-item.k-state-focused').filter(':visible');\n            },\n            _itemBelongsToVertival: function (item) {\n                var menuIsVertical = this.wrapper.hasClass('k-menu-vertical');\n                if (!item.length) {\n                    return menuIsVertical;\n                }\n                return item.parent().hasClass('k-menu-group') || menuIsVertical;\n            },\n            _itemHasChildren: function (item) {\n                if (!item || !item.length || !item[0].nodeType) {\n                    return false;\n                }\n                return item.children('.k-menu-group, div.k-animation-container').length > 0 || !!item.data(POPUP_OPENER_ATTR) && !!this._overflowWrapper().children(popupGroupSelector(item.data(POPUP_OPENER_ATTR)));\n            },\n            _moveHover: function (item, nextItem) {\n                var that = this, id = that._ariaId;\n                if (item.length && nextItem.length) {\n                    item.removeClass(FOCUSEDSTATE);\n                }\n                if (nextItem.length) {\n                    if (nextItem[0].id) {\n                        id = nextItem[0].id;\n                    }\n                    nextItem.addClass(FOCUSEDSTATE);\n                    that._oldHoverItem = nextItem;\n                    if (id) {\n                        that.element.removeAttr('aria-activedescendant');\n                        $('#' + id).removeAttr('id');\n                        nextItem.attr('id', id);\n                        that.element.attr('aria-activedescendant', id);\n                    }\n                    that._scrollToItem(nextItem);\n                }\n            },\n            _findRootParent: function (item) {\n                if (this._isRootItem(item)) {\n                    return item;\n                } else {\n                    return this._parentsUntil(item, menuSelector, 'li.k-item').last();\n                }\n            },\n            _isRootItem: function (item) {\n                return item.parent().hasClass(MENU);\n            },\n            _itemRight: function (item, belongsToVertical, hasChildren) {\n                var that = this, nextItem, parentItem, overflowWrapper;\n                if (!belongsToVertical) {\n                    nextItem = item.nextAll(nextSelector);\n                    if (!nextItem.length) {\n                        nextItem = item.prevAll(lastSelector);\n                    }\n                    that.close(item);\n                } else if (hasChildren && !item.hasClass(DISABLEDSTATE)) {\n                    that.open(item);\n                    nextItem = that._childPopupElement(item).children().first();\n                } else if (that.options.orientation == 'horizontal') {\n                    parentItem = that._findRootParent(item);\n                    overflowWrapper = that._overflowWrapper();\n                    if (overflowWrapper) {\n                        var rootPopup = itemPopup(parentItem, overflowWrapper);\n                        that._closeChildPopups(rootPopup);\n                    }\n                    that.close(parentItem);\n                    nextItem = parentItem.nextAll(nextSelector);\n                }\n                if (nextItem && !nextItem.length) {\n                    nextItem = that.wrapper.children('.k-item').first();\n                } else if (!nextItem) {\n                    nextItem = [];\n                }\n                that._moveHover(item, nextItem);\n                return nextItem;\n            },\n            _itemLeft: function (item, belongsToVertical) {\n                var that = this, nextItem, overflowWrapper;\n                if (!belongsToVertical) {\n                    nextItem = item.prevAll(nextSelector);\n                    if (!nextItem.length) {\n                        nextItem = item.nextAll(lastSelector);\n                    }\n                    that.close(item);\n                } else {\n                    nextItem = item.parent().closest('.k-item');\n                    overflowWrapper = that._overflowWrapper();\n                    if (!nextItem.length && overflowWrapper) {\n                        nextItem = popupParentItem(item.parent(), overflowWrapper);\n                    }\n                    that.close(nextItem);\n                    if (that._isRootItem(nextItem) && that.options.orientation == 'horizontal') {\n                        nextItem = nextItem.prevAll(nextSelector);\n                    }\n                }\n                if (!nextItem.length) {\n                    nextItem = that.wrapper.children('.k-item').last();\n                }\n                that._moveHover(item, nextItem);\n                return nextItem;\n            },\n            _itemDown: function (item, belongsToVertical, hasChildren) {\n                var that = this, nextItem;\n                if (!belongsToVertical) {\n                    if (!hasChildren || item.hasClass(DISABLEDSTATE)) {\n                        return;\n                    } else {\n                        that.open(item);\n                        nextItem = that._childPopupElement(item).children().first();\n                    }\n                } else {\n                    nextItem = item.nextAll(nextSelector);\n                }\n                if (!nextItem.length && item.length) {\n                    nextItem = item.parent().children().first();\n                } else if (!item.length) {\n                    nextItem = that.wrapper.children('.k-item').first();\n                }\n                that._moveHover(item, nextItem);\n                return nextItem;\n            },\n            _itemUp: function (item, belongsToVertical) {\n                var that = this, nextItem;\n                if (!belongsToVertical) {\n                    return;\n                } else {\n                    nextItem = item.prevAll(nextSelector);\n                }\n                if (!nextItem.length && item.length) {\n                    nextItem = item.parent().children().last();\n                } else if (!item.length) {\n                    nextItem = that.wrapper.children('.k-item').last();\n                }\n                that._moveHover(item, nextItem);\n                return nextItem;\n            },\n            _scrollToItem: function (item) {\n                var that = this;\n                if (that.options.scrollable && item && item.length) {\n                    var ul = item.parent();\n                    var isHorizontal = ul.hasClass(MENU) ? that.options.orientation == 'horizontal' : false;\n                    var scrollDir = isHorizontal ? 'scrollLeft' : 'scrollTop';\n                    var getSize = isHorizontal ? kendo._outerWidth : kendo._outerHeight;\n                    var currentScrollOffset = ul[scrollDir]();\n                    var itemSize = getSize(item);\n                    var itemOffset = item[0][isHorizontal ? 'offsetLeft' : 'offsetTop'];\n                    var ulSize = getSize(ul);\n                    var scrollButtons = ul.siblings(scrollButtonSelector);\n                    var scrollButtonSize = scrollButtons.length ? getSize(scrollButtons.first()) : 0;\n                    var itemPosition;\n                    if (currentScrollOffset + ulSize < itemOffset + itemSize + scrollButtonSize) {\n                        itemPosition = itemOffset + itemSize - ulSize + scrollButtonSize;\n                    } else if (currentScrollOffset > itemOffset - scrollButtonSize) {\n                        itemPosition = itemOffset - scrollButtonSize;\n                    }\n                    if (!isNaN(itemPosition)) {\n                        var scrolling = {};\n                        scrolling[scrollDir] = itemPosition;\n                        ul.finish().animate(scrolling, 'fast', 'linear', function () {\n                            that._toggleScrollButtons(ul, scrollButtons.first(), scrollButtons.last(), isHorizontal);\n                        });\n                    }\n                }\n            },\n            _itemEsc: function (item, belongsToVertical) {\n                var that = this, nextItem;\n                if (!belongsToVertical) {\n                    return item;\n                } else {\n                    nextItem = item.parent().closest('.k-item');\n                    that.close(nextItem);\n                    that._moveHover(item, nextItem);\n                }\n                return nextItem;\n            },\n            _childPopupElement: function (item) {\n                var popupElement = item.find('.k-menu-group');\n                var wrapper = this._overflowWrapper();\n                if (!popupElement.length && wrapper) {\n                    popupElement = itemPopup(item, wrapper);\n                }\n                return popupElement;\n            },\n            _triggerEvent: function (e) {\n                var that = this;\n                return that.trigger(e.type, {\n                    type: e.type,\n                    item: e.item\n                });\n            },\n            _focusHandler: function (e) {\n                var that = this, item = $(kendo.eventTarget(e)).closest(allItemsSelector);\n                if (item.hasClass(DISABLEDSTATE)) {\n                    return;\n                }\n                setTimeout(function () {\n                    that._moveHover([], item);\n                    if (item.children('.k-content')[0]) {\n                        item.parent().closest('.k-item').removeClass(FOCUSEDSTATE);\n                    }\n                }, 200);\n            },\n            _animations: function (options) {\n                if (options && 'animation' in options && !options.animation) {\n                    options.animation = {\n                        open: { effects: {} },\n                        close: {\n                            hide: true,\n                            effects: {}\n                        }\n                    };\n                }\n            },\n            _dataSource: function (options) {\n                var that = this, dataSource = options ? options.dataSource : that.options.dataSource;\n                if (!dataSource) {\n                    return;\n                }\n                dataSource = isArray(dataSource) ? { data: dataSource } : dataSource;\n                that._unbindDataSource();\n                if (!dataSource.fields) {\n                    dataSource.fields = [\n                        { field: 'uid' },\n                        { field: 'text' },\n                        { field: 'url' },\n                        { field: 'cssClass' },\n                        { field: 'spriteCssClass' },\n                        { field: 'imageUrl' },\n                        { field: 'imageAttr' },\n                        { field: 'attr' },\n                        { field: 'contentAttr' },\n                        { field: 'content' },\n                        { field: 'encoded' },\n                        { field: 'items' },\n                        { field: 'select' }\n                    ];\n                }\n                that.dataSource = HierarchicalDataSource.create(dataSource);\n                that._bindDataSource();\n                that.dataSource.fetch();\n            },\n            _bindDataSource: function () {\n                this._refreshHandler = proxy(this.refresh, this);\n                this._errorHandler = proxy(this._error, this);\n                this.dataSource.bind(CHANGE, this._refreshHandler);\n                this.dataSource.bind(ERROR, this._errorHandler);\n            },\n            _unbindDataSource: function () {\n                var dataSource = this.dataSource;\n                if (dataSource) {\n                    dataSource.unbind(CHANGE, this._refreshHandler);\n                    dataSource.unbind(ERROR, this._errorHandler);\n                }\n            },\n            _error: function () {\n            },\n            findByUid: function (uid) {\n                var wrapperElement = this._overflowWrapper() || this.element;\n                return wrapperElement.find('[data-uid=' + uid + ']');\n            },\n            refresh: function (ev) {\n                var that = this;\n                var node = ev.node;\n                var action = ev.action;\n                var parentElement = node ? that.findByUid(node.uid) : that.element;\n                var itemsToUpdate = ev.items;\n                var index = ev.index;\n                var updateProxy = $.proxy(that._updateItem, that);\n                var removeProxy = $.proxy(that._removeItem, that);\n                if (action == 'add') {\n                    that._appendItems(itemsToUpdate, index, parentElement);\n                } else if (action == 'remove') {\n                    itemsToUpdate.forEach(removeProxy);\n                } else if (action == 'itemchange') {\n                    itemsToUpdate.forEach(updateProxy);\n                } else if (action === 'itemloaded') {\n                    that.append(ev.items, parentElement);\n                } else {\n                    this._initData();\n                }\n                this.trigger(DATABOUND, {\n                    item: parentElement,\n                    dataItem: node\n                });\n            },\n            _appendItems: function (items, index, parent) {\n                var that = this;\n                var referenceItem = parent.find(itemSelector).eq(index);\n                if (referenceItem.length) {\n                    that.insertBefore(items, referenceItem);\n                } else {\n                    that.append(items, parent);\n                }\n            },\n            _removeItem: function (item) {\n                var that = this;\n                var element = that.findByUid(item.uid);\n                that.remove(element);\n            },\n            _updateItem: function (item) {\n                var that = this;\n                var element = that.findByUid(item.uid);\n                var nextElement = element.next();\n                var parentNode = item.parentNode();\n                that.remove(element);\n                if (nextElement.length) {\n                    that.insertBefore(item, nextElement);\n                } else {\n                    that.append(item, parentNode && that.findByUid(parentNode.uid));\n                }\n            },\n            _accessors: function () {\n                var that = this, options = that.options, i, field, textField, element = that.element;\n                for (i in bindings) {\n                    field = options[bindings[i]];\n                    textField = element.attr(kendo.attr(i + '-field'));\n                    if (!field && textField) {\n                        field = textField;\n                    }\n                    if (!field) {\n                        field = i;\n                    }\n                    if (!isArray(field)) {\n                        field = [field];\n                    }\n                    options[bindings[i]] = field;\n                }\n            },\n            _fieldAccessor: function (fieldName) {\n                var fieldBindings = this.options[bindings[fieldName]] || [], count = fieldBindings.length, result = '(function(item) {';\n                if (count === 0) {\n                    result += 'return item[\\'' + fieldName + '\\'];';\n                } else {\n                    result += 'var levels = [' + $.map(fieldBindings, function (x) {\n                        return 'function(d){ return ' + kendo.expr(x) + '}';\n                    }).join(',') + '];';\n                    result += 'if(item.level){return levels[Math.min(item.level(), ' + count + '-1)](item);}else';\n                    result += '{return levels[' + count + '-1](item)}';\n                }\n                result += '})';\n                return result;\n            },\n            _templates: function () {\n                var that = this, options = that.options, fieldAccessor = proxy(that._fieldAccessor, that);\n                if (options.template && typeof options.template == STRING) {\n                    options.template = template(options.template);\n                } else if (!options.template) {\n                    options.template = template('# var text = ' + fieldAccessor('text') + '(data.item); #' + '# if (typeof data.item.encoded != \\'undefined\\' && data.item.encoded === false) {#' + '#= text #' + '# } else { #' + '#: text #' + '# } #');\n                }\n                that.templates = {\n                    content: template('#var contentHtml = ' + fieldAccessor('content') + '(item);#' + '<div #= contentCssAttributes(item.toJSON ? item.toJSON() : item) # tabindex=\\'-1\\'>#= contentHtml || \\'\\' #</div>'),\n                    group: template('<ul class=\\'#= groupCssClass(group) #\\'#= groupAttributes(group) # role=\\'menu\\' aria-hidden=\\'true\\'>' + '#= renderItems(data) #' + '</ul>'),\n                    itemWrapper: template('# var url = ' + fieldAccessor('url') + '(item); #' + '# var imageUrl = ' + fieldAccessor('imageUrl') + '(item); #' + '# var imgAttributes = ' + fieldAccessor('imageAttr') + '(item);#' + '# var tag = url ? \\'a\\' : \\'span\\' #' + '<#= tag # class=\\'#= textClass(item) #\\' #if(url){#href=\\'#= url #\\'#}#>' + '# if (imageUrl) { #' + '<img #= imageCssAttributes(imgAttributes) #  alt=\\'\\' src=\\'#= imageUrl #\\' />' + '# } #' + '#= sprite(item) ##= data.menu.options.template(data) #' + '#= arrow(data) #' + '</#= tag #>'),\n                    item: template('#var contentHtml = ' + fieldAccessor('content') + '(item);#' + '<li class=\\'#= wrapperCssClass(group, item) #\\' #= itemCssAttributes(item.toJSON ? item.toJSON() : item) # role=\\'menuitem\\'  #=item.items ? \"aria-haspopup=\\'true\\'\": \"\"#' + '#=item.enabled === false ? \"aria-disabled=\\'true\\'\" : \\'\\'#' + kendo.attr('uid') + '=\\'#= item.uid #\\' >' + '#= itemWrapper(data) #' + '#if (item.hasChildren || item.items) { #' + '#= subGroup({ items: item.items, menu: menu, group: { expanded: item.expanded } }) #' + '# } else if (item.content || item.contentUrl || contentHtml) { #' + '#= renderContent(data) #' + '# } #' + '</li>'),\n                    scrollButton: template('<span class=\\'k-button k-button-icon k-menu-scroll-button k-scroll-#= direction #\\' unselectable=\\'on\\'>' + '<span class=\\'k-icon k-i-arrow-60-#= direction #\\'></span></span>'),\n                    arrow: template('<span class=\\'#= arrowClass(item, group) #\\'></span>'),\n                    sprite: template('# var spriteCssClass = ' + fieldAccessor('spriteCssClass') + '(data); if(spriteCssClass) {#<span class=\\'k-sprite #= spriteCssClass #\\'></span>#}#'),\n                    empty: template('')\n                };\n            },\n            renderItem: function (options) {\n                var that = this;\n                options = extend({\n                    menu: that,\n                    group: {}\n                }, options);\n                var empty = that.templates.empty, item = options.item;\n                return that.templates.item(extend(options, {\n                    sprite: that.templates.sprite,\n                    itemWrapper: that.templates.itemWrapper,\n                    renderContent: that.renderContent,\n                    arrow: item.items || item.content || item[that.options.dataContentField[0]] ? that.templates.arrow : empty,\n                    subGroup: that.renderGroup\n                }, rendering));\n            },\n            renderGroup: function (options) {\n                var that = this;\n                var templates = that.templates || options.menu.templates;\n                return templates.group(extend({\n                    renderItems: function (options) {\n                        var html = '', i = 0, items = options.items, len = items ? items.length : 0, group = extend({ length: len }, options.group);\n                        for (; i < len; i++) {\n                            html += options.menu.renderItem(extend(options, {\n                                group: group,\n                                item: extend({ index: i }, items[i])\n                            }));\n                        }\n                        return html;\n                    }\n                }, options, rendering));\n            },\n            renderContent: function (options) {\n                return options.menu.templates.content(extend(options, rendering));\n            }\n        });\n        var ContextMenu = Menu.extend({\n            init: function (element, options) {\n                var that = this;\n                Menu.fn.init.call(that, element, options);\n                that._marker = kendo.guid().substring(0, 8);\n                that.target = $(that.options.target);\n                that._popup();\n                that._wire();\n            },\n            _initOverflow: function (options) {\n                var that = this;\n                if (options.scrollable && !that._overflowWrapper()) {\n                    that._openedPopups = {};\n                    that._popupsWrapper = (that.element.parent().is(animationContainerSelector) ? that.element.parent() : that.element).wrap('<div class=\\'k-popups-wrapper ' + options.orientation + '\\'></div>').parent();\n                    if (that.options.orientation == 'horizontal') {\n                        removeSpacesBetweenItems(that.element);\n                    }\n                    if (options.appendTo) {\n                        options.appendTo.append(that._popupsWrapper);\n                    }\n                    that._initialHeight = that.element[0].style.height;\n                    that._initialWidth = that.element[0].style.width;\n                }\n            },\n            options: {\n                name: 'ContextMenu',\n                filter: null,\n                showOn: 'contextmenu',\n                orientation: 'vertical',\n                alignToAnchor: false,\n                target: 'body'\n            },\n            events: [\n                OPEN,\n                CLOSE,\n                ACTIVATE,\n                DEACTIVATE,\n                SELECT\n            ],\n            setOptions: function (options) {\n                var that = this;\n                Menu.fn.setOptions.call(that, options);\n                that.target.off(that.showOn + NS + that._marker, that._showProxy);\n                if (that.userEvents) {\n                    that.userEvents.destroy();\n                }\n                that.target = $(that.options.target);\n                if (options.orientation && that.popup.wrapper[0]) {\n                    that.popup.element.unwrap();\n                }\n                that._wire();\n                Menu.fn.setOptions.call(this, options);\n            },\n            destroy: function () {\n                var that = this;\n                that.target.off(that.options.showOn + NS + that._marker);\n                DOCUMENT_ELEMENT.off(kendo.support.mousedown + NS + that._marker, that._closeProxy);\n                if (that.userEvents) {\n                    that.userEvents.destroy();\n                }\n                Menu.fn.destroy.call(that);\n            },\n            open: function (x, y) {\n                var that = this;\n                x = $(x)[0];\n                if (contains(that.element[0], $(x)[0]) || that._itemHasChildren($(x))) {\n                    Menu.fn.open.call(that, x);\n                } else {\n                    if (that._triggerEvent({\n                            item: that.element,\n                            type: OPEN\n                        }) === false) {\n                        if (that.popup.visible() && that.options.filter) {\n                            that.popup.close(true);\n                            that.popup.element.kendoStop(true);\n                        }\n                        if (y !== undefined) {\n                            var overflowWrapper = that._overflowWrapper();\n                            if (overflowWrapper) {\n                                var offset = overflowWrapper.offset();\n                                x -= offset.left;\n                                y -= offset.top;\n                            }\n                            that.popup.wrapper.hide();\n                            that._configurePopupScrolling(x, y);\n                            that.popup.open(x, y);\n                        } else {\n                            that.popup.options.anchor = (x ? x : that.popup.anchor) || that.target;\n                            that.popup.element.kendoStop(true);\n                            that._configurePopupScrolling();\n                            that.popup.open();\n                        }\n                        DOCUMENT_ELEMENT.off(that.popup.downEvent, that.popup._mousedownProxy);\n                        DOCUMENT_ELEMENT.on(kendo.support.mousedown + NS + that._marker, that._closeProxy);\n                    }\n                }\n                return that;\n            },\n            _configurePopupScrolling: function (x, y) {\n                var that = this;\n                var popup = that.popup;\n                var isHorizontal = that.options.orientation == 'horizontal';\n                if (that.options.scrollable) {\n                    that._wrapPopupElement(popup);\n                    popup.element.parent().css({\n                        position: '',\n                        height: ''\n                    });\n                    popup.element.css({\n                        visibility: 'hidden',\n                        display: '',\n                        position: ''\n                    });\n                    if (isHorizontal) {\n                        that._setPopupWidth(popup, isNaN(x) ? undefined : {\n                            isFixed: true,\n                            x: x,\n                            y: y\n                        });\n                    } else {\n                        that._setPopupHeight(popup, isNaN(x) ? undefined : {\n                            isFixed: true,\n                            x: x,\n                            y: y\n                        });\n                    }\n                    popup.element.css({\n                        visibility: '',\n                        display: 'none',\n                        position: 'absolute'\n                    });\n                    that._initPopupScrollButtons(popup, isHorizontal, true);\n                    popup.element.siblings(scrollButtonSelector).hide();\n                }\n            },\n            _setPopupWidth: function (popup, isFixed) {\n                var popupElement = popup.element;\n                var popups = popupElement.add(popupElement.parent(animationContainerSelector));\n                popups.width(this._initialWidth || '');\n                var location = popup._location(isFixed);\n                var windowWidth = $(window).width();\n                var popupOuterWidth = location.width;\n                var popupOffsetLeft = Math.max(location.left, 0);\n                var scrollLeft = isFixed ? 0 : parentsScroll(this._overflowWrapper()[0], 'scrollLeft');\n                var shadow = kendo.getShadows(popupElement);\n                var maxWidth = windowWidth - shadow.left - shadow.right;\n                var canFit = maxWidth + scrollLeft > popupOuterWidth + popupOffsetLeft;\n                if (!canFit) {\n                    popups.css({\n                        overflow: 'hidden',\n                        width: maxWidth - popupOffsetLeft + scrollLeft + 'px'\n                    });\n                }\n            },\n            close: function () {\n                var that = this;\n                if (contains(that.element[0], $(arguments[0])[0]) || that._itemHasChildren(arguments[0])) {\n                    Menu.fn.close.call(that, arguments[0]);\n                } else {\n                    if (that.popup.visible()) {\n                        if (that._triggerEvent({\n                                item: that.element,\n                                type: CLOSE\n                            }) === false) {\n                            that.popup.close();\n                            DOCUMENT_ELEMENT.off(kendo.support.mousedown + NS + that._marker, that._closeProxy);\n                            that.unbind(SELECT, that._closeTimeoutProxy);\n                        }\n                    }\n                }\n            },\n            _showHandler: function (e) {\n                var ev = e, offset, that = this, options = that.options, target = kendo.support.mobileOS ? $(ev.target) : $(ev.currentTarget);\n                if (e.event) {\n                    ev = e.event;\n                    ev.pageX = e.x.location;\n                    ev.pageY = e.y.location;\n                }\n                if (contains(that.element[0], e.relatedTarget || e.target)) {\n                    return;\n                }\n                that._eventOrigin = ev;\n                ev.preventDefault();\n                ev.stopImmediatePropagation();\n                that.element.find('.' + FOCUSEDSTATE).removeClass(FOCUSEDSTATE);\n                if (options.filter && target.is(options.filter) || !options.filter) {\n                    if (options.alignToAnchor) {\n                        that.popup.options.anchor = ev.currentTarget;\n                        that.open(ev.currentTarget);\n                    } else {\n                        that.popup.options.anchor = ev.currentTarget;\n                        if (that._targetChild) {\n                            offset = that.target.offset();\n                            that.open(ev.pageX - offset.left, ev.pageY - offset.top);\n                        } else {\n                            that.open(ev.pageX, ev.pageY);\n                        }\n                    }\n                }\n            },\n            _closeHandler: function (e) {\n                var that = this, target = $(e.relatedTarget || e.target), sameTarget = target.closest(that.target.selector)[0] == that.target[0], item = target.closest(itemSelector), children = that._itemHasChildren(item), overflowWrapper = that._overflowWrapper(), containment = contains(that.element[0], target[0]) || overflowWrapper && contains(overflowWrapper[0], target[0]);\n                that._eventOrigin = e;\n                var normalClick = e.which !== 3;\n                if (that.popup.visible() && (normalClick && sameTarget || !sameTarget) && (that.options.closeOnClick && !children && containment || !containment)) {\n                    if (containment) {\n                        this.unbind(SELECT, this._closeTimeoutProxy);\n                        that.bind(SELECT, that._closeTimeoutProxy);\n                    } else {\n                        that.close();\n                    }\n                }\n            },\n            _wire: function () {\n                var that = this, options = that.options, target = that.target;\n                that._showProxy = proxy(that._showHandler, that);\n                that._closeProxy = proxy(that._closeHandler, that);\n                that._closeTimeoutProxy = proxy(that.close, that);\n                if (target[0]) {\n                    if (kendo.support.mobileOS && options.showOn == 'contextmenu') {\n                        that.userEvents = new kendo.UserEvents(target, {\n                            filter: options.filter,\n                            allowSelection: false\n                        });\n                        target.on(options.showOn + NS + that._marker, false);\n                        that.userEvents.bind('hold', that._showProxy);\n                    } else {\n                        if (options.filter) {\n                            target.on(options.showOn + NS + that._marker, options.filter, that._showProxy);\n                        } else {\n                            target.on(options.showOn + NS + that._marker, that._showProxy);\n                        }\n                    }\n                }\n            },\n            _triggerEvent: function (e) {\n                var that = this, anchor = $(that.popup.options.anchor)[0], origin = that._eventOrigin;\n                that._eventOrigin = undefined;\n                return that.trigger(e.type, extend({\n                    type: e.type,\n                    item: e.item || this.element[0],\n                    target: anchor\n                }, origin ? { event: origin } : {}));\n            },\n            _popup: function () {\n                var that = this;\n                var overflowWrapper = that._overflowWrapper();\n                that._triggerProxy = proxy(that._triggerEvent, that);\n                that.popup = that.element.addClass('k-context-menu').kendoPopup({\n                    autosize: that.options.orientation === 'horizontal',\n                    anchor: that.target || 'body',\n                    copyAnchorStyles: that.options.copyAnchorStyles,\n                    collision: that.options.popupCollision || 'fit',\n                    animation: that.options.animation,\n                    activate: that._triggerProxy,\n                    deactivate: that._triggerProxy,\n                    appendTo: overflowWrapper || that.options.appendTo,\n                    close: !overflowWrapper ? $.noop : function (e) {\n                        $(getChildPopups(e.sender.element, overflowWrapper)).each(function (i, p) {\n                            var popup = p.data(KENDOPOPUP);\n                            if (popup) {\n                                popup.close(true);\n                            }\n                        });\n                    }\n                }).data(KENDOPOPUP);\n                that._targetChild = contains(that.target[0], that.popup.element[0]);\n            }\n        });\n        ui.plugin(Menu);\n        ui.plugin(ContextMenu);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}