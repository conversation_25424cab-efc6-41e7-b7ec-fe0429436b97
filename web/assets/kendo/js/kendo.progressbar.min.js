/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.progressbar.min",["kendo.core.min"],e)}(function(){return function(e,r){var s=window.kendo,t=s.ui,a=t.Widget,n="horizontal",o="vertical",p=0,i=100,u=0,l=5,d="k-progressbar",g="k-progressbar-reverse",c="k-progressbar-indeterminate",v="k-complete",_="k-state-selected",f="k-progress-status",h="k-state-selected",m="k-state-default",k="k-state-disabled",P={VALUE:"value",PERCENT:"percent",CHUNK:"chunk"},C="change",w="complete",y="boolean",S=Math,W=e.extend,b=e.proxy,A=100,x=400,U=3,N={progressStatus:"<span class='k-progress-status-wrap'><span class='k-progress-status'></span></span>"},V=a.extend({init:function(e,r){var s=this;a.fn.init.call(this,e,r),r=s.options,s._progressProperty=r.orientation===n?"width":"height",s._fields(),r.value=s._validateValue(r.value),s._validateType(r.type),s._wrapper(),s._progressAnimation(),r.value!==r.min&&r.value!==!1&&s._updateProgress()},setOptions:function(e){var r=this;a.fn.setOptions.call(r,e),e.hasOwnProperty("reverse")&&r.wrapper.toggleClass("k-progressbar-reverse",e.reverse),e.hasOwnProperty("enable")&&r.enable(e.enable),r._progressAnimation(),r._validateValue(),r._updateProgress()},events:[C,w],options:{name:"ProgressBar",orientation:n,reverse:!1,min:p,max:i,value:u,enable:!0,type:P.VALUE,chunkCount:l,showStatus:!0,animation:{}},_fields:function(){var r=this;r._isStarted=!1,r.progressWrapper=r.progressStatus=e()},_validateType:function(r){var t=!1;if(e.each(P,function(e,s){if(s===r)return t=!0,!1}),!t)throw Error(s.format("Invalid ProgressBar type '{0}'",r))},_wrapper:function(){var e,r=this,s=r.wrapper=r.element,t=r.options,a=t.orientation;s.addClass("k-widget "+d),s.addClass(d+"-"+(a===n?n:o)),t.enable===!1&&s.addClass(k),t.reverse&&s.addClass(g),t.value===!1&&s.addClass(c),t.type===P.CHUNK?r._addChunkProgressWrapper():t.showStatus&&(r.progressStatus=r.wrapper.prepend(N.progressStatus).find("."+f),e=t.value!==!1?t.value:t.min,r.progressStatus.text(t.type===P.VALUE?e:r._calculatePercentage(e).toFixed()+"%"))},value:function(e){return this._value(e)},_value:function(e){var s,t=this,a=t.options;return e===r?a.value:(typeof e!==y?(e=t._roundValue(e),isNaN(e)||(s=t._validateValue(e),s!==a.value&&(t.wrapper.removeClass(c),a.value=s,t._isStarted=!0,t._updateProgress()))):e||(t.wrapper.addClass(c),a.value=!1),r)},_roundValue:function(e){e=parseFloat(e);var r=S.pow(10,U);return S.floor(e*r)/r},_validateValue:function(e){var r=this,s=r.options;if(e!==!1){if(e<=s.min||e===!0)return s.min;if(e>=s.max)return s.max}else if(e===!1)return!1;return isNaN(r._roundValue(e))?s.min:e},_updateProgress:function(){var e=this,r=e.options,s=e._calculatePercentage();r.type===P.CHUNK?(e._updateChunks(s),e._onProgressUpdateAlways(r.value)):e._updateProgressWrapper(s)},_updateChunks:function(e){var r,s=this,t=s.options,a=t.chunkCount,p=parseInt(A/a*100,10)/100,i=parseInt(100*e,10)/100,u=S.floor(i/p);r=s.wrapper.find(t.orientation===n&&!t.reverse||t.orientation===o&&t.reverse?"li.k-item:lt("+u+")":"li.k-item:gt(-"+(u+1)+")"),s.wrapper.find("."+h).removeClass(h).addClass(m),r.removeClass(m).addClass(h)},_updateProgressWrapper:function(e){var r=this,s=r.options,t=r.wrapper.find("."+_),a=r._isStarted?r._animation.duration:0,n={};0===t.length&&r._addRegularProgressWrapper(),n[r._progressProperty]=e+"%",r.progressWrapper.animate(n,{duration:a,start:b(r._onProgressAnimateStart,r),progress:b(r._onProgressAnimate,r),complete:b(r._onProgressAnimateComplete,r,s.value),always:b(r._onProgressUpdateAlways,r,s.value)})},_onProgressAnimateStart:function(){this.progressWrapper.show()},_onProgressAnimate:function(e){var r,s=this,t=s.options,a=parseFloat(e.elem.style[s._progressProperty],10);t.showStatus&&(r=1e4/parseFloat(s.progressWrapper[0].style[s._progressProperty]),s.progressWrapper.find(".k-progress-status-wrap").css(s._progressProperty,r+"%")),t.type!==P.CHUNK&&a<=98&&s.progressWrapper.removeClass(v)},_onProgressAnimateComplete:function(e){var r,s=this,t=s.options,a=parseFloat(s.progressWrapper[0].style[s._progressProperty]);t.type!==P.CHUNK&&a>98&&s.progressWrapper.addClass(v),t.showStatus&&(r=t.type===P.VALUE?e:t.type==P.PERCENT?s._calculatePercentage(e).toFixed()+"%":S.floor(s._calculatePercentage(e))+"%",s.progressStatus.text(r)),e===t.min&&s.progressWrapper.hide()},_onProgressUpdateAlways:function(e){var r=this,s=r.options;r._isStarted&&r.trigger(C,{value:e}),e===s.max&&r._isStarted&&r.trigger(w,{value:s.max})},enable:function(e){var s=this,t=s.options;t.enable=r===e||e,s.wrapper.toggleClass(k,!t.enable)},destroy:function(){var e=this;a.fn.destroy.call(e)},_addChunkProgressWrapper:function(){var e,r=this,s=r.options,t=r.wrapper,a=A/s.chunkCount,n="";for(s.chunkCount<=1&&(s.chunkCount=1),n+="<ul class='k-reset'>",e=s.chunkCount-1;e>=0;e--)n+="<li class='k-item k-state-default'></li>";n+="</ul>",t.append(n).find(".k-item").css(r._progressProperty,a+"%").first().addClass("k-first").end().last().addClass("k-last"),r._normalizeChunkSize()},_normalizeChunkSize:function(){var e=this,r=e.options,s=e.wrapper.find(".k-item:last"),t=parseFloat(s[0].style[e._progressProperty]),a=A-r.chunkCount*t;a>0&&s.css(e._progressProperty,t+a+"%")},_addRegularProgressWrapper:function(){var r=this;r.progressWrapper=e("<div class='"+_+"'></div>").appendTo(r.wrapper),r.options.showStatus&&(r.progressWrapper.append(N.progressStatus),r.progressStatus=r.wrapper.find("."+f))},_calculateChunkSize:function(){var e=this,r=e.options.chunkCount,s=e.wrapper.find("ul.k-reset");return(parseInt(s.css(e._progressProperty),10)-(r-1))/r},_calculatePercentage:function(e){var s=this,t=s.options,a=e!==r?e:t.value,n=t.min,o=t.max;return s._onePercent=S.abs((o-n)/100),S.abs((a-n)/s._onePercent)},_progressAnimation:function(){var e=this,r=e.options,s=r.animation;e._animation=s===!1?{duration:0}:W({duration:x},r.animation)}});s.ui.plugin(V)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,r,s){(s||r)()});
//# sourceMappingURL=kendo.progressbar.min.js.map
