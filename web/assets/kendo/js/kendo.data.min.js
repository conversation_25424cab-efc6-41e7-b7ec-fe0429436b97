/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.data.min",["kendo.core.min","kendo.data.odata.min","kendo.data.xml.min"],e)}(function(){return function(e,t){function r(e,t,r,i){return function(n){var a,s={};for(a in n)s[a]=n[a];s.field=i?r+"."+n.field:r,t==Ae&&e._notifyChange&&e._notifyChange(s),e.trigger(t,s)}}function i(t,r){if(t===r)return!0;var n,a=e.type(t),s=e.type(r);if(a!==s)return!1;if("date"===a)return t.getTime()===r.getTime();if("object"!==a&&"array"!==a)return!1;for(n in t)if(!i(t[n],r[n]))return!1;return!0}function n(e,t){var r,i;for(i in e){if(r=e[i],_e(r)&&r.field&&r.field===t)return r;if(r===t)return r}return null}function a(e){this.data=e||[]}function s(e,r){if(e){var i=typeof e===qe?{field:e,dir:r}:e,n=me(i)?i:i!==t?[i]:[];return ye(n,function(e){return!!e.dir})}}function o(e){var t,r,i,n,a=e.filters;if(a)for(t=0,r=a.length;t<r;t++)i=a[t],n=i.operator,n&&typeof n===qe&&(i.operator=re[n.toLowerCase()]||n),o(i)}function u(e){if(e&&!ve(e))return!me(e)&&e.filters||(e={logic:"and",filters:me(e)?e:[e]}),o(e),e}function l(e,t){return!e.logic&&!t.logic&&(e.field===t.field&&e.value===t.value&&e.operator===t.operator)}function h(e){return e=e||{},ve(e)?{logic:"and",filters:[]}:u(e)}function d(e,t){return t.logic||e.field>t.field?1:e.field<t.field?-1:0}function f(e,t){var r,i,n,a,s;if(e=h(e),t=h(t),e.logic!==t.logic)return!1;if(n=(e.filters||[]).slice(),a=(t.filters||[]).slice(),n.length!==a.length)return!1;for(n=n.sort(d),a=a.sort(d),s=0;s<n.length;s++)if(r=n[s],i=a[s],r.logic&&i.logic){if(!f(r,i))return!1}else if(!l(r,i))return!1;return!0}function c(e){return me(e)?e:[e]}function g(e,r,i,n){var a=typeof e===qe?{field:e,dir:r,compare:i,skipItemSorting:n}:e,s=me(a)?a:a!==t?[a]:[];return Q(s,function(e){return{field:e.field,dir:e.dir||"asc",aggregates:e.aggregates,compare:e.compare,skipItemSorting:e.skipItemSorting}})}function p(e,t,r){var i,n=g(e,t,r);for(i=0;i<n.length;i++)delete n[i].compare;return n}function _(e){var t,r=me(e)?e:[e];for(t=0;t<r.length;t++)if(r[t]&&Fe(r[t].compare))return!0;return!1}function v(e,t){return e&&e.getTime&&t&&t.getTime?e.getTime()===t.getTime():e===t}function m(e,t,r,i,n,a){var s,o,u,l,h;for(t=t||[],l=t.length,s=0;s<l;s++)o=t[s],u=o.aggregate,h=o.field,e[h]=e[h]||{},a[h]=a[h]||{},a[h][u]=a[h][u]||{},e[h][u]=ie[u.toLowerCase()](e[h][u],r,ke.accessor(h),i,n,a[h][u])}function y(e){return"number"==typeof e&&!isNaN(e)}function S(e){return e&&e.getTime}function b(e){var t,r=e.length,i=Array(r);for(t=0;t<r;t++)i[t]=e[t].toJSON();return i}function w(e,t,r,i,n){var a,s,o,u,l,h={};for(u=0,l=e.length;u<l;u++){a=e[u];for(s in t)o=n[s],o&&o!==s&&(h[o]||(h[o]=ke.setter(o)),h[o](a,t[s](a)),delete a[s])}}function k(e,t,r,i,n){var a,s,o,u,l;for(u=0,l=e.length;u<l;u++){a=e[u];for(s in t)a[s]=r._parse(s,t[s](a)),o=n[s],o&&o!==s&&delete a[o]}}function F(e,t,r,i,n){var a,s,o,u;for(s=0,u=e.length;s<u;s++)a=e[s],o=i[a.field],o&&o!=a.field&&(a.field=o),a.value=r._parse(a.field,a.value),a.hasSubgroups?F(a.items,t,r,i,n):k(a.items,t,r,i,n)}function x(e,t,r,i,n,a){return function(s){return s=e(s),R(t,r,i,n,a)(s)}}function R(e,t,r,i,n){return function(a){return a&&!ve(r)&&("[object Array]"===Ze.call(a)||a instanceof rt||(a=[a]),t(a,r,new e,i,n)),a||[]}}function q(e,t){var r,i,n;if(t.items&&t.items.length)for(n=0;n<t.items.length;n++)r=e.items[n],i=t.items[n],r&&i?r.hasSubgroups?q(r,i):r.field&&r.value==i.value?r.items.push.apply(r.items,i.items):e.items.push.apply(e.items,[i]):i&&e.items.push.apply(e.items,[i])}function D(e,t,r,i){for(var n,a,s,o=0;t.length&&i&&(n=t[o],a=n.items,s=a.length,e&&e.field===n.field&&e.value===n.value?(e.hasSubgroups&&e.items.length?D(e.items[e.items.length-1],n.items,r,i):(a=a.slice(r,r+i),e.items=e.items.concat(a)),t.splice(o--,1)):n.hasSubgroups&&a.length?(D(n,a,r,i),n.items.length||t.splice(o--,1)):(a=a.slice(r,r+i),n.items=a,n.items.length||t.splice(o--,1)),0===a.length?r-=s:(r=0,i-=a.length),!(++o>=t.length)););o<t.length&&t.splice(o,t.length-o)}function z(e,t){var r,i,n,a,s=[],o=(e||[]).length,u=Fe(t)?t:function(e,t){return e[t]};for(n=0;n<o;n++)if(r=u(e,n),r.hasSubgroups)s=s.concat(z(r.items));else for(i=r.items,a=0;a<i.length;a++)s.push(u(i,a));return s}function C(e){var t,r,i,n,a,s=[];for(t=0,r=e.length;t<r;t++)if(a=e.at(t),a.hasSubgroups)s=s.concat(C(a.items));else for(i=a.items,n=0;n<i.length;n++)s.push(i.at(n));return s}function O(e,t){var r,i,n;if(t)for(r=0,i=e.length;r<i;r++)n=e.at(r),n.hasSubgroups?O(n.items,t):n.items=new W(n.items,t,n.items._events)}function P(e,t){for(var r=0;r<e.length;r++)if(e[r].hasSubgroups){if(P(e[r].items,t))return!0}else if(t(e[r].items,e[r]))return!0}function T(e,t,r,i){for(var n=0;n<e.length&&e[n].data!==t&&!A(e[n].data,r,i);n++);}function A(e,t,r){for(var i=0,n=e.length;i<n;i++){if(e[i]&&e[i].hasSubgroups)return A(e[i].items,t,r);if(e[i]===t||e[i]===r)return e[i]=r,!0}}function I(e,r,i,n,a){var s,o,u,l;for(s=0,o=e.length;s<o;s++)if(u=e[s],u&&!(u instanceof n))if(u.hasSubgroups===t||a){for(l=0;l<r.length;l++)if(r[l]===u){e[s]=r.at(l),T(i,r,u,e[s]);break}}else I(u.items,r,i,n,a)}function M(e,t){var r,i,n=e.length;for(i=0;i<n;i++)if(r=e[i],r.uid&&r.uid==t.uid)return e.splice(i,1),r}function N(e,t){return t?L(e,function(e){return e.uid&&e.uid==t.uid||e[t.idField]===t.id&&t.id!==t._defaultId}):-1}function G(e,t){return t?L(e,function(e){return e.uid==t.uid}):-1}function L(e,t){var r,i;for(r=0,i=e.length;r<i;r++)if(t(e[r]))return r;return-1}function j(e,t){var r,i;return e&&!ve(e)?(r=e[t],i=_e(r)?r.from||r.field||t:e[t]||t,Fe(i)?t:i):t}function E(e,t){var r,i,n,a={};for(n in e)"filters"!==n&&(a[n]=e[n]);if(e.filters)for(a.filters=[],r=0,i=e.filters.length;r<i;r++)a.filters[r]=E(e.filters[r],t);else a.field=j(t.fields,a.field);return a}function B(e,t){var r,i,n,a,s,o=[];for(r=0,i=e.length;r<i;r++){n={},a=e[r];for(s in a)n[s]=a[s];n.field=j(t.fields,n.field),n.aggregates&&me(n.aggregates)&&(n.aggregates=B(n.aggregates,t)),o.push(n)}return o}function H(t,r){var i,n,a,s,o,u,l,h,d,f;for(t=e(t)[0],i=t.options,n=r[0],a=r[1],s=[],o=0,u=i.length;o<u;o++)d={},h=i[o],l=h.parentNode,l===t&&(l=null),h.disabled||l&&l.disabled||(l&&(d.optgroup=l.label),d[n.field]=h.text,f=h.attributes.value,f=f&&f.specified?h.value:h.text,d[a.field]=f,s.push(d));return s}function U(t,r){var i,n,a,s,o,u,l,h=e(t)[0].tBodies[0],d=h?h.rows:[],f=r.length,c=[];for(i=0,n=d.length;i<n;i++){for(o={},l=!0,s=d[i].cells,a=0;a<f;a++)u=s[a],"th"!==u.nodeName.toLowerCase()&&(l=!1,o[r[a].field]=u.innerHTML);l||c.push(o)}return c}function J(e){return function(){var t=this._data,r=ue.fn[e].apply(this,Xe.call(arguments));return this._data!=t&&this._attachBubbleHandlers(),r}}function V(t,r){function i(e,t){return e.filter(t).add(e.find(t))}var n,a,s,o,u,l,h,d,f=e(t).children(),c=[],g=r[0].field,p=r[1]&&r[1].field,_=r[2]&&r[2].field,v=r[3]&&r[3].field;for(n=0,a=f.length;n<a;n++)s={_loaded:!0},o=f.eq(n),l=o[0].firstChild,d=o.children(),t=d.filter("ul"),d=d.filter(":not(ul)"),u=o.attr("data-id"),u&&(s.id=u),l&&(s[g]=3==l.nodeType?l.nodeValue:d.text()),p&&(s[p]=i(d,"a").attr("href")),v&&(s[v]=i(d,"img").attr("src")),_&&(h=i(d,".k-sprite").prop("className"),s[_]=h&&e.trim(h.replace("k-sprite",""))),t.length&&(s.items=V(t.eq(0),r)),"true"==o.attr("data-hasChildren")&&(s.hasChildren=!0),c.push(s);return c}var Q,W,$,K,X,Y,Z,ee,te,re,ie,ne,ae,se,oe,ue,le,he,de,fe,ce,ge=e.extend,pe=e.proxy,_e=e.isPlainObject,ve=e.isEmptyObject,me=e.isArray,ye=e.grep,Se=e.ajax,be=e.each,we=e.noop,ke=window.kendo,Fe=ke.isFunction,xe=ke.Observable,Re=ke.Class,qe="string",De="function",ze="asc",Ce="create",Oe="read",Pe="update",Te="destroy",Ae="change",Ie="sync",Me="get",Ne="error",Ge="requestStart",Le="progress",je="requestEnd",Ee=[Ce,Oe,Pe,Te],Be=function(e){return e},He=ke.getter,Ue=ke.stringify,Je=Math,Ve=[].push,Qe=[].join,We=[].pop,$e=[].splice,Ke=[].shift,Xe=[].slice,Ye=[].unshift,Ze={}.toString,et=ke.support.stableSort,tt=/^\/Date\((.*?)\)\/$/,rt=xe.extend({init:function(e,t){var r=this;r.type=t||$,xe.fn.init.call(r),r.length=e.length,r.wrapAll(e,r)},at:function(e){return this[e]},toJSON:function(){var e,t,r=this.length,i=Array(r);for(e=0;e<r;e++)t=this[e],t instanceof $&&(t=t.toJSON()),i[e]=t;return i},parent:we,wrapAll:function(e,t){var r,i,n=this,a=function(){return n};for(t=t||[],r=0,i=e.length;r<i;r++)t[r]=n.wrap(e[r],a);return t},wrap:function(e,t){var r,i=this;return null!==e&&"[object Object]"===Ze.call(e)&&(r=e instanceof i.type||e instanceof Y,r||(e=e instanceof $?e.toJSON():e,e=new i.type(e)),e.parent=t,e.bind(Ae,function(e){i.trigger(Ae,{field:e.field,node:e.node,index:e.index,items:e.items||[this],action:e.node?e.action||"itemloaded":"itemchange"})})),e},push:function(){var e,t=this.length,r=this.wrapAll(arguments);return e=Ve.apply(this,r),this.trigger(Ae,{action:"add",index:t,items:r}),e},slice:Xe,sort:[].sort,join:Qe,pop:function(){var e=this.length,t=We.apply(this);return e&&this.trigger(Ae,{action:"remove",index:e-1,items:[t]}),t},splice:function(e,t,r){var i,n,a,s=this.wrapAll(Xe.call(arguments,2));if(i=$e.apply(this,[e,t].concat(s)),i.length)for(this.trigger(Ae,{action:"remove",index:e,items:i}),n=0,a=i.length;n<a;n++)i[n]&&i[n].children&&i[n].unbind(Ae);return r&&this.trigger(Ae,{action:"add",index:e,items:s}),i},shift:function(){var e=this.length,t=Ke.apply(this);return e&&this.trigger(Ae,{action:"remove",index:0,items:[t]}),t},unshift:function(){var e,t=this.wrapAll(arguments);return e=Ye.apply(this,t),this.trigger(Ae,{action:"add",index:0,items:t}),e},indexOf:function(e){var t,r,i=this;for(t=0,r=i.length;t<r;t++)if(i[t]===e)return t;return-1},forEach:function(e,t){for(var r=0,i=this.length,n=t||window;r<i;r++)e.call(n,this[r],r,this)},map:function(e,t){for(var r=0,i=[],n=this.length,a=t||window;r<n;r++)i[r]=e.call(a,this[r],r,this);return i},reduce:function(e){var t,r=0,i=this.length;for(2==arguments.length?t=arguments[1]:r<i&&(t=this[r++]);r<i;r++)t=e(t,this[r],r,this);return t},reduceRight:function(e){var t,r=this.length-1;for(2==arguments.length?t=arguments[1]:r>0&&(t=this[r--]);r>=0;r--)t=e(t,this[r],r,this);return t},filter:function(e,t){for(var r,i=0,n=[],a=this.length,s=t||window;i<a;i++)r=this[i],e.call(s,r,i,this)&&(n[n.length]=r);return n},find:function(e,t){for(var r,i=0,n=this.length,a=t||window;i<n;i++)if(r=this[i],e.call(a,r,i,this))return r},every:function(e,t){for(var r,i=0,n=this.length,a=t||window;i<n;i++)if(r=this[i],!e.call(a,r,i,this))return!1;return!0},some:function(e,t){for(var r,i=0,n=this.length,a=t||window;i<n;i++)if(r=this[i],e.call(a,r,i,this))return!0;return!1},remove:function(e){var t=this.indexOf(e);t!==-1&&this.splice(t,1)},empty:function(){this.splice(0,this.length)}});"undefined"!=typeof Symbol&&Symbol.iterator&&!rt.prototype[Symbol.iterator]&&(rt.prototype[Symbol.iterator]=[][Symbol.iterator]),W=rt.extend({init:function(e,t,r){xe.fn.init.call(this),this.type=t||$,r&&(this._events=r);for(var i=0;i<e.length;i++)this[i]=e[i];this.length=i,this._parent=pe(function(){return this},this)},at:function(e){var t=this[e];return t instanceof this.type?t.parent=this._parent:t=this[e]=this.wrap(t,this._parent),t}}),$=xe.extend({init:function(e){var t,r,i=this,n=function(){return i};xe.fn.init.call(this),this._handlers={};for(r in e)t=e[r],"object"==typeof t&&t&&!t.getTime&&"_"!=r.charAt(0)&&(t=i.wrap(t,r,n)),i[r]=t;i.uid=ke.guid()},shouldSerialize:function(e){return this.hasOwnProperty(e)&&"_handlers"!==e&&"_events"!==e&&typeof this[e]!==De&&"uid"!==e},forEach:function(e){for(var t in this)this.shouldSerialize(t)&&e(this[t],t)},toJSON:function(){var e,t,r={};for(t in this)this.shouldSerialize(t)&&(e=this[t],(e instanceof $||e instanceof rt)&&(e=e.toJSON()),r[t]=e);return r},get:function(e){var t,r=this;return r.trigger(Me,{field:e}),t="this"===e?r:ke.getter(e,!0)(r)},_set:function(e,t){var r,i,n,a=this,s=e.indexOf(".")>=0;if(s)for(r=e.split("."),i="";r.length>1;){if(i+=r.shift(),n=ke.getter(i,!0)(a),n instanceof $)return n.set(r.join("."),t),s;i+="."}return ke.setter(e)(a,t),s},set:function(e,t){var r=this,i=!1,n=e.indexOf(".")>=0,a=ke.getter(e,!0)(r);return a!==t&&(a instanceof xe&&this._handlers[e]&&(this._handlers[e].get&&a.unbind(Me,this._handlers[e].get),a.unbind(Ae,this._handlers[e].change)),i=r.trigger("set",{field:e,value:t}),i||(n||(t=r.wrap(t,e,function(){return r})),(!r._set(e,t)||e.indexOf("(")>=0||e.indexOf("[")>=0)&&r.trigger(Ae,{field:e}))),i},parent:we,wrap:function(e,t,i){var n,a,s,o,u=this,l=Ze.call(e);return null==e||"[object Object]"!==l&&"[object Array]"!==l||(s=e instanceof rt,o=e instanceof ue,"[object Object]"!==l||o||s?("[object Array]"===l||s||o)&&(s||o||(e=new rt(e)),a=r(u,Ae,t,!1),e.bind(Ae,a),u._handlers[t]={change:a}):(e instanceof $||(e=new $(e)),n=r(u,Me,t,!0),e.bind(Me,n),a=r(u,Ae,t,!0),e.bind(Ae,a),u._handlers[t]={get:n,change:a}),e.parent=i),e}}),K={number:function(e){return typeof e===qe&&"null"===e.toLowerCase()?null:ke.parseFloat(e)},date:function(e){return typeof e===qe&&"null"===e.toLowerCase()?null:ke.parseDate(e)},"boolean":function(e){return typeof e===qe?"null"===e.toLowerCase()?null:"true"===e.toLowerCase():null!=e?!!e:e},string:function(e){return typeof e===qe&&"null"===e.toLowerCase()?null:null!=e?e+"":e},"default":function(e){return e}},X={string:"",number:0,date:new Date,"boolean":!1,"default":""},Y=$.extend({init:function(r){var i,n,a=this;if((!r||e.isEmptyObject(r))&&(r=e.extend({},a.defaults,r),a._initializers))for(i=0;i<a._initializers.length;i++)n=a._initializers[i],r[n]=a.defaults[n]();$.fn.init.call(a,r),a.dirty=!1,a.dirtyFields={},a.idField&&(a.id=a.get(a.idField),a.id===t&&(a.id=a._defaultId))},shouldSerialize:function(e){return $.fn.shouldSerialize.call(this,e)&&"uid"!==e&&!("id"!==this.idField&&"id"===e)&&"dirty"!==e&&"dirtyFields"!==e&&"_accessors"!==e},_parse:function(e,t){var r,i=this,a=e,s=i.fields||{};return e=s[e],e||(e=n(s,a)),e&&(r=e.parse,!r&&e.type&&(r=K[e.type.toLowerCase()])),r?r(t):t},_notifyChange:function(e){var t=e.action;"add"!=t&&"remove"!=t||(this.dirty=!0,this.dirtyFields[e.field]=!0)},editable:function(e){return e=(this.fields||{})[e],!e||e.editable!==!1},set:function(e,t,r){var n=this,a=n.dirty;n.editable(e)&&(t=n._parse(e,t),i(t,n.get(e))?n.trigger("equalSet",{field:e,value:t}):(n.dirty=!0,n.dirtyFields[e]=!0,$.fn.set.call(n,e,t,r)&&!a&&(n.dirty=a,n.dirty||(n.dirtyFields[e]=!1))))},accept:function(e){var t,r,i=this,n=function(){return i};for(t in e)r=e[t],"_"!=t.charAt(0)&&(r=i.wrap(e[t],t,n)),i._set(t,r);i.idField&&(i.id=i.get(i.idField)),i.dirty=!1,i.dirtyFields={}},isNew:function(){return this.id===this._defaultId}}),Y.define=function(e,r){r===t&&(r=e,e=Y);var i,n,a,s,o,u,l,h,d=ge({defaults:{}},r),f={},c=d.id,g=[];if(c&&(d.idField=c),d.id&&delete d.id,c&&(d.defaults[c]=d._defaultId=""),"[object Array]"===Ze.call(d.fields)){for(u=0,l=d.fields.length;u<l;u++)a=d.fields[u],typeof a===qe?f[a]={}:a.field&&(f[a.field]=a);d.fields=f}for(n in d.fields)a=d.fields[n],s=a.type||"default",o=null,h=n,n=typeof a.field===qe?a.field:n,a.nullable||(o=d.defaults[h!==n?h:n]=a.defaultValue!==t?a.defaultValue:X[s.toLowerCase()],"function"==typeof o&&g.push(n)),r.id===n&&(d._defaultId=o),d.defaults[h!==n?h:n]=o,a.parse=a.parse||K[s];return g.length>0&&(d._initializers=g),i=e.extend(d),i.define=function(e){return Y.define(i,e)},d.fields&&(i.fields=d.fields,i.idField=d.idField),i},Z={selector:function(e){return Fe(e)?e:He(e)},compare:function(e){var t=this.selector(e);return function(e,r){return e=t(e),r=t(r),null==e&&null==r?0:null==e?-1:null==r?1:e.localeCompare?e.localeCompare(r):e>r?1:e<r?-1:0}},create:function(e){var t=e.compare||this.compare(e.field);return"desc"==e.dir?function(e,r){return t(r,e,!0)}:t},combine:function(e){return function(t,r){var i,n,a=e[0](t,r);for(i=1,n=e.length;i<n;i++)a=a||e[i](t,r);return a}}},ee=ge({},Z,{asc:function(e){var t=this.selector(e);return function(e,r){var i=t(e),n=t(r);return i&&i.getTime&&n&&n.getTime&&(i=i.getTime(),n=n.getTime()),i===n?e.__position-r.__position:null==i?-1:null==n?1:i.localeCompare?i.localeCompare(n):i>n?1:-1}},desc:function(e){var t=this.selector(e);return function(e,r){var i=t(e),n=t(r);return i&&i.getTime&&n&&n.getTime&&(i=i.getTime(),n=n.getTime()),i===n?e.__position-r.__position:null==i?1:null==n?-1:n.localeCompare?n.localeCompare(i):i<n?1:-1}},create:function(e){return this[e.dir](e.field)}}),Q=function(e,t){var r,i=e.length,n=Array(i);for(r=0;r<i;r++)n[r]=t(e[r],r,e);return n},te=function(){function e(e){return"string"==typeof e&&(e=e.replace(/[\r\n]+/g,"")),JSON.stringify(e)}function t(t){return function(r,i,n,a){return i+="",n&&(r="("+r+" || '').toString()"+(a?".toLocaleLowerCase('"+a+"')":".toLowerCase()"),i=a?i.toLocaleLowerCase(a):i.toLowerCase()),t(r,e(i),n)}}function r(t,r,i,n,a){if(null!=i){if(typeof i===qe){var s=tt.exec(i);s?i=new Date((+s[1])):n?(i=e(a?i.toLocaleLowerCase(a):i.toLowerCase()),r="(("+r+" || '')+'')"+(a?".toLocaleLowerCase('"+a+"')":".toLowerCase()")):i=e(i)}i.getTime&&(r="("+r+"&&"+r+".getTime?"+r+".getTime():"+r+")",i=i.getTime())}return r+" "+t+" "+i}function i(e){var t,r,i,n;for(t="/^",r=!1,i=0;i<e.length;++i){if(n=e.charAt(i),r)t+="\\"+n;else{if("~"==n){r=!0;continue}t+="*"==n?".*":"?"==n?".":".+^$()[]{}|\\/\n\r\u2028\u2029 ".indexOf(n)>=0?"\\"+n:n}r=!1}return t+"$/"}return{quote:function(t){return t&&t.getTime?"new Date("+t.getTime()+")":e(t)},eq:function(e,t,i,n){return r("==",e,t,i,n)},neq:function(e,t,i,n){return r("!=",e,t,i,n)},gt:function(e,t,i){return r(">",e,t,i)},gte:function(e,t,i){return r(">=",e,t,i)},lt:function(e,t,i){return r("<",e,t,i)},lte:function(e,t,i){return r("<=",e,t,i)},startswith:t(function(e,t){return e+".lastIndexOf("+t+", 0) == 0"}),doesnotstartwith:t(function(e,t){return e+".lastIndexOf("+t+", 0) == -1"}),endswith:t(function(e,t){var r=t?t.length-2:0;return e+".indexOf("+t+", "+e+".length - "+r+") >= 0"}),doesnotendwith:t(function(e,t){var r=t?t.length-2:0;return e+".indexOf("+t+", "+e+".length - "+r+") < 0"}),contains:t(function(e,t){return e+".indexOf("+t+") >= 0"}),doesnotcontain:t(function(e,t){return e+".indexOf("+t+") == -1"}),matches:t(function(e,t){return t=t.substring(1,t.length-1),i(t)+".test("+e+")"}),doesnotmatch:t(function(e,t){return t=t.substring(1,t.length-1),"!"+i(t)+".test("+e+")"}),isempty:function(e){return e+" === ''"},isnotempty:function(e){return e+" !== ''"},isnull:function(e){return"("+e+" == null)"},isnotnull:function(e){return"("+e+" != null)"},isnullorempty:function(e){return"("+e+" === null) || ("+e+" === '')"},isnotnullorempty:function(e){return"("+e+" !== null) && ("+e+" !== '')"}}}(),a.filterExpr=function(e){var r,i,n,s,o,u,l=[],h={and:" && ",or:" || "},d=[],f=[],c=e.filters;for(r=0,i=c.length;r<i;r++)n=c[r],o=n.field,u=n.operator,n.filters?(s=a.filterExpr(n),n=s.expression.replace(/__o\[(\d+)\]/g,function(e,t){return t=+t,"__o["+(f.length+t)+"]"}).replace(/__f\[(\d+)\]/g,function(e,t){return t=+t,"__f["+(d.length+t)+"]"}),f.push.apply(f,s.operators),d.push.apply(d,s.fields)):(typeof o===De?(s="__f["+d.length+"](d)",d.push(o)):s=ke.expr(o),typeof u===De?(n="__o["+f.length+"]("+s+", "+te.quote(n.value)+")",f.push(u)):n=te[(u||"eq").toLowerCase()](s,n.value,n.ignoreCase===t||n.ignoreCase,e.accentFoldingFiltering)),l.push(n);return{expression:"("+l.join(h[e.logic])+")",fields:d,operators:f}},re={"==":"eq",equals:"eq",isequalto:"eq",equalto:"eq",equal:"eq","!=":"neq",ne:"neq",notequals:"neq",isnotequalto:"neq",notequalto:"neq",notequal:"neq","<":"lt",islessthan:"lt",lessthan:"lt",less:"lt","<=":"lte",le:"lte",islessthanorequalto:"lte",lessthanequal:"lte",">":"gt",isgreaterthan:"gt",greaterthan:"gt",greater:"gt",">=":"gte",isgreaterthanorequalto:"gte",greaterthanequal:"gte",ge:"gte",notsubstringof:"doesnotcontain",isnull:"isnull",isempty:"isempty",isnotempty:"isnotempty"},a.normalizeFilter=u,a.compareFilters=f,a.prototype={toArray:function(){return this.data},range:function(e,t){return new a(this.data.slice(e,e+t))},skip:function(e){return new a(this.data.slice(e))},take:function(e){return new a(this.data.slice(0,e))},select:function(e){return new a(Q(this.data,e))},order:function(e,t,r){var i={dir:t};return e&&(e.compare?i.compare=e.compare:i.field=e),new a(r?this.data.sort(Z.create(i)):this.data.slice(0).sort(Z.create(i)))},orderBy:function(e,t){return this.order(e,"asc",t)},orderByDescending:function(e,t){return this.order(e,"desc",t)},sort:function(e,t,r,i){var n,a,o=s(e,t),u=[];if(r=r||Z,o.length){for(n=0,a=o.length;n<a;n++)u.push(r.create(o[n]));return this.orderBy({compare:r.combine(u)},i)}return this},filter:function(e){var t,r,i,n,s,o,l,h,d=this.data,f=[];if(e=u(e),!e||0===e.filters.length)return this;for(n=a.filterExpr(e),o=n.fields,l=n.operators,s=h=Function("d, __f, __o","return "+n.expression),(o.length||l.length)&&(h=function(e){return s(e,o,l)}),t=0,i=d.length;t<i;t++)r=d[t],h(r)&&f.push(r);return new a(f)},group:function(e,t){e=g(e||[]),t=t||this.data;var r,i=this,n=new a(i.data);return e.length>0&&(r=e[0],n=n.groupBy(r).select(function(i){var n=new a(t).filter([{field:i.field,operator:"eq",value:i.value,ignoreCase:!1}]);return{field:i.field,value:i.value,items:e.length>1?new a(i.items).group(e.slice(1),n.toArray()).toArray():i.items,hasSubgroups:e.length>1,aggregates:n.aggregate(r.aggregates)}})),n},groupBy:function(e){var t,r,i,n,s,o,u,l,h,d,f=this;if(ve(e)||!this.data.length)return new a([]);for(t=e.field,r=e.skipItemSorting?this.data:this._sortForGrouping(t,e.dir||"asc"),i=ke.accessor(t),s=i.get(r[0],t),o={field:t,value:s,items:[]},d=[o],l=0,h=r.length;l<h;l++)n=r[l],u=i.get(n,t),v(s,u)||(s=u,o={field:t,value:s,items:[]},d.push(o)),o.items.push(n);return d=f._sortGroups(d,e),new a(d)},_sortForGrouping:function(e,t){var r,i,n=this.data;if(!et){for(r=0,i=n.length;r<i;r++)n[r].__position=r;for(n=new a(n).sort(e,t,ee).toArray(),r=0,i=n.length;r<i;r++)delete n[r].__position;return n}return this.sort(e,t).toArray()},_sortGroups:function(e,t){var r=e;return t&&Fe(t.compare)&&(r=new a(r).order({compare:t.compare},t.dir||ze).toArray()),r},aggregate:function(e){var t,r,i={},n={};if(e&&e.length)for(t=0,r=this.data.length;t<r;t++)m(i,e,this.data[t],t,r,n);return i}},ie={sum:function(e,t,r){var i=r.get(t);return y(e)?y(i)&&(e+=i):e=i,e},count:function(e){return(e||0)+1},average:function(e,r,i,n,a,s){var o=i.get(r);return s.count===t&&(s.count=0),y(e)?y(o)&&(e+=o):e=o,y(o)&&s.count++,n==a-1&&y(e)&&(e/=s.count),e},max:function(e,t,r){var i=r.get(t);return y(e)||S(e)||(e=i),e<i&&(y(i)||S(i))&&(e=i),e},min:function(e,t,r){var i=r.get(t);return y(e)||S(e)||(e=i),e>i&&(y(i)||S(i))&&(e=i),e}},a.normalizeGroup=g,a.normalizeSort=s,a.process=function(e,r,i){var n,o,u,l,h,d,f,c,v,m,y,S;return r=r||{},n=r.group,o=_(g(n||[])),u=new a(e),l=p(n||[]),h=s(r.sort||[]),d=o?h:l.concat(h),v=r.filterCallback,m=r.filter,y=r.skip,S=r.take,d&&i&&(u=u.sort(d,t,t,i)),m&&(u=u.filter(m),v&&(u=v(u)),c=u.toArray().length),d&&!i&&(u=u.sort(d),n&&(e=u.toArray())),o?(u=u.group(n,e),y!==t&&S!==t&&(u=new a(z(u.toArray())).range(y,S),f=Q(l,function(e){return ge({},e,{skipItemSorting:!0})}),u=u.group(f,e))):(y!==t&&S!==t&&(u=u.range(y,S)),n&&(u=u.group(n,e))),{total:c,data:u.toArray()}},ne=Re.extend({init:function(e){this.data=e.data},read:function(e){e.success(this.data)},update:function(e){e.success(e.data)},create:function(e){e.success(e.data)},destroy:function(e){e.success(e.data)}}),ae=Re.extend({init:function(e){var t,r=this;e=r.options=ge({},r.options,e),be(Ee,function(t,r){typeof e[r]===qe&&(e[r]={url:e[r]})}),r.cache=e.cache?se.create(e.cache):{find:we,add:we},t=e.parameterMap,e.submit&&(r.submit=e.submit),Fe(e.push)&&(r.push=e.push),r.push||(r.push=Be),r.parameterMap=Fe(t)?t:function(e){var r={};return be(e,function(e,i){e in t&&(e=t[e],_e(e)&&(i=e.value(i),e=e.key)),r[e]=i}),r}},options:{parameterMap:Be},create:function(e){return Se(this.setup(e,Ce))},read:function(r){var i,n,a,s=this,o=s.cache;r=s.setup(r,Oe),i=r.success||we,n=r.error||we,a=o.find(r.data),a!==t?i(a):(r.success=function(e){o.add(r.data,e),i(e)},e.ajax(r))},update:function(e){return Se(this.setup(e,Pe))},destroy:function(e){return Se(this.setup(e,Te))},setup:function(e,t){e=e||{};var r,i=this,n=i.options[t],a=Fe(n.data)?n.data(e.data):n.data;return e=ge(!0,{},n,e),r=ge(!0,{},a,e.data),e.data=i.parameterMap(r,t),Fe(e.url)&&(e.url=e.url(r)),e}}),se=Re.extend({init:function(){this._store={}},add:function(e,r){e!==t&&(this._store[Ue(e)]=r)},find:function(e){return this._store[Ue(e)]},clear:function(){this._store={}},remove:function(e){delete this._store[Ue(e)]}}),se.create=function(e){var t={inmemory:function(){return new se}};return _e(e)&&Fe(e.find)?e:e===!0?new se:t[e]()},oe=Re.extend({init:function(e){var t,r,i,n,a,s,o,u,l,h,d,f,c,g,p=this;e=e||{};for(t in e)r=e[t],p[t]=typeof r===qe?He(r):r;n=e.modelBase||Y,_e(p.model)&&(p.model=i=n.define(p.model)),a=pe(p.data,p),p._dataAccessFunction=a,p.model&&(s=pe(p.groups,p),o=pe(p.serialize,p),u={},l={},h={},d={},f=!1,i=p.model,i.fields&&(be(i.fields,function(e,t){var r;c=e,_e(t)&&t.field?c=t.field:typeof t===qe&&(c=t),_e(t)&&t.from&&(r=t.from),f=f||r&&r!==e||c!==e,g=r||c,l[e]=g.indexOf(".")!==-1?He(g,!0):He(g),h[e]=He(e),u[r||c]=e,d[e]=r||c}),!e.serialize&&f&&(p.serialize=x(o,i,w,h,u,d))),p._dataAccessFunction=a,p._wrapDataAccessBase=R(i,k,l,u,d),p.data=x(a,i,k,l,u,d),p.groups=x(s,i,F,l,u,d))},errors:function(e){return e?e.errors:null},parse:Be,data:Be,total:function(e){return e.length},groups:Be,aggregates:function(){return{}},serialize:function(e){return e}}),ue=xe.extend({init:function(e){var r,i,n,a=this;e&&(i=e.data),e=a.options=ge({},a.options,e),a._map={},a._prefetch={},a._data=[],a._pristineData=[],a._ranges=[],a._view=[],a._pristineTotal=0,a._destroyed=[],a._pageSize=e.pageSize,a._page=e.page||(e.pageSize?1:t),a._sort=s(e.sort),a._filter=u(e.filter),a._group=g(e.group),a._aggregate=e.aggregate,a._total=e.total,a._shouldDetachObservableParents=!0,xe.fn.init.call(a),a.transport=le.create(e,i,a),Fe(a.transport.push)&&a.transport.push({pushCreate:pe(a._pushCreate,a),pushUpdate:pe(a._pushUpdate,a),pushDestroy:pe(a._pushDestroy,a)}),null!=e.offlineStorage&&("string"==typeof e.offlineStorage?(n=e.offlineStorage,a._storage={getItem:function(){return JSON.parse(localStorage.getItem(n))},setItem:function(e){localStorage.setItem(n,Ue(a.reader.serialize(e)))}}):a._storage=e.offlineStorage),a.reader=new ke.data.readers[e.schema.type||"json"](e.schema),r=a.reader.model||{},a._detachObservableParents(),a._data=a._observe(a._data),a._online=!0,a.bind(["push",Ne,Ae,Ge,Ie,je,Le],e)},options:{data:null,schema:{modelBase:Y},offlineStorage:null,serverSorting:!1,serverPaging:!1,serverFiltering:!1,serverGrouping:!1,serverAggregates:!1,batch:!1,inPlaceSort:!1},clone:function(){return this},online:function(r){return r!==t?this._online!=r&&(this._online=r,r)?this.sync():e.Deferred().resolve().promise():this._online},offlineData:function(e){return null==this.options.offlineStorage?null:e!==t?this._storage.setItem(e):this._storage.getItem()||[]},_isServerGrouped:function(){var e=this.group()||[];return this.options.serverGrouping&&e.length},_pushCreate:function(e){this._push(e,"pushCreate")},_pushUpdate:function(e){this._push(e,"pushUpdate")},_pushDestroy:function(e){this._push(e,"pushDestroy")},_push:function(e,t){var r=this._readData(e);r||(r=e),this[t](r)},_flatData:function(e,t){if(e){if(this._isServerGrouped())return C(e);if(!t)for(var r=0;r<e.length;r++)e.at(r)}return e},parent:we,get:function(e){var t,r,i=this._flatData(this._data,this.options.useRanges);for(t=0,r=i.length;t<r;t++)if(i[t].id==e)return i[t]},getByUid:function(e){return this._getByUid(e,this._data)},_getByUid:function(e,t){var r,i,n=this._flatData(t,this.options.useRanges);if(n)for(r=0,i=n.length;r<i;r++)if(n[r].uid==e)return n[r]},indexOf:function(e){return G(this._data,e)},at:function(e){return this._data.at(e)},data:function(e){var r,i=this;if(e===t){if(i._data)for(r=0;r<i._data.length;r++)i._data.at(r);return i._data}i._detachObservableParents(),i._data=this._observe(e),i._pristineData=e.slice(0),i._storeData(),i._ranges=[],i.trigger("reset"),i._addRange(i._data),i._total=i._data.length,i._pristineTotal=i._total,i._process(i._data)},view:function(e){return e===t?this._view:(this._view=this._observeView(e),t)},_observeView:function(e){var t,r=this;return I(e,r._data,r._ranges,r.reader.model||$,r._isServerGrouped()),t=new W(e,r.reader.model),t.parent=function(){return r.parent()},t},flatView:function(){var e=this.group()||[];return e.length?C(this._view):this._view},add:function(e){return this.insert(this._data.length,e)},_createNewModel:function(e){return this.reader.model?new this.reader.model(e):e instanceof $?e:new $(e)},insert:function(e,t){return t||(t=e,e=0),t instanceof Y||(t=this._createNewModel(t)),this._isServerGrouped()?this._data.splice(e,0,this._wrapInEmptyGroup(t)):this._data.splice(e,0,t),this._insertModelInRange(e,t),t},pushInsert:function(t,r){var i,n,a,s,o,u,l=this,h=l._getCurrentRangeSpan();r||(r=t,t=0),me(r)||(r=[r]),i=[],n=this.options.autoSync,this.options.autoSync=!1;try{for(a=0;a<r.length;a++)s=r[a],o=this.insert(t,s),i.push(o),u=o.toJSON(),this._isServerGrouped()&&(u=this._wrapInEmptyGroup(u)),this._pristineData.push(u),h&&h.length&&e(h).last()[0].pristineData.push(u),t++}finally{this.options.autoSync=n}i.length&&this.trigger("push",{type:"create",items:i})},pushCreate:function(e){this.pushInsert(this._data.length,e)},pushUpdate:function(e){var t,r,i,n,a;for(me(e)||(e=[e]),t=[],r=0;r<e.length;r++)i=e[r],n=this._createNewModel(i),a=this.get(n.id),a?(t.push(a),a.accept(i),a.trigger(Ae),this._updatePristineForModel(a,i)):this.pushCreate(i);t.length&&this.trigger("push",{type:"update",items:t})},pushDestroy:function(e){var t=this._removeItems(e);t.length&&this.trigger("push",{type:"destroy",items:t})},_removeItems:function(e,r){var i,n,a,s,o,u,l;me(e)||(e=[e]),i=t===r||r,n=[],a=this.options.autoSync,this.options.autoSync=!1;try{for(s=0;s<e.length;s++)o=e[s],u=this._createNewModel(o),l=!1,this._eachItem(this._data,function(e){var t,r;for(t=0;t<e.length;t++)if(r=e.at(t),r.id===u.id){n.push(r),e.splice(t,1),l=!0;break}}),l&&i&&(this._removePristineForModel(u),this._destroyed.pop())}finally{this.options.autoSync=a}return n},remove:function(e){var t,r=this,i=r._isServerGrouped();return this._eachItem(r._data,function(n){if(t=M(n,e),t&&i)return t.isNew&&t.isNew()||r._destroyed.push(t),!0}),this._removeModelFromRanges(e),e},destroyed:function(){return this._destroyed},created:function(){var e,t,r=[],i=this._flatData(this._data,this.options.useRanges);for(e=0,t=i.length;e<t;e++)i[e].isNew&&i[e].isNew()&&r.push(i[e]);return r},updated:function(){var e,t,r=[],i=this._flatData(this._data,this.options.useRanges);for(e=0,t=i.length;e<t;e++)i[e].isNew&&!i[e].isNew()&&i[e].dirty&&r.push(i[e]);return r},sync:function(){var t,r=this,i=[],n=[],a=r._destroyed,s=e.Deferred().resolve().promise();if(r.online()){if(!r.reader.model)return s;i=r.created(),n=r.updated(),t=[],r.options.batch&&r.transport.submit?t=r._sendSubmit(i,n,a):(t.push.apply(t,r._send("create",i)),t.push.apply(t,r._send("update",n)),t.push.apply(t,r._send("destroy",a))),s=e.when.apply(null,t).then(function(){var e,t;for(e=0,t=arguments.length;e<t;e++)arguments[e]&&r._accept(arguments[e]);r._storeData(!0),r._syncEnd(),r._change({action:"sync"}),r.trigger(Ie)})}else r._storeData(!0),r._syncEnd(),r._change({action:"sync"});return s},_syncEnd:we,cancelChanges:function(e){var t=this;e instanceof ke.data.Model?t._cancelModel(e):(t._destroyed=[],t._detachObservableParents(),t._data=t._observe(t._pristineData),t.options.serverPaging&&(t._total=t._pristineTotal),t._ranges=[],t._addRange(t._data,0),t._changesCanceled(),t._change(),t._markOfflineUpdatesAsDirty())},_changesCanceled:we,_markOfflineUpdatesAsDirty:function(){var e=this;null!=e.options.offlineStorage&&e._eachItem(e._data,function(e){var t,r;for(t=0;t<e.length;t++)r=e.at(t),"update"!=r.__state__&&"create"!=r.__state__||(r.dirty=!0)})},hasChanges:function(){var e,t,r=this._flatData(this._data,this.options.useRanges);if(this._destroyed.length)return!0;for(e=0,t=r.length;e<t;e++)if(r[e].isNew&&r[e].isNew()||r[e].dirty)return!0;
return!1},_accept:function(t){var r,i=this,n=t.models,a=t.response,s=0,o=i._isServerGrouped(),u=i._pristineData,l=t.type;if(i.trigger(je,{response:a,type:l}),a&&!ve(a)){if(a=i.reader.parse(a),i._handleCustomErrors(a))return;a=i.reader.data(a),me(a)||(a=[a])}else a=e.map(n,function(e){return e.toJSON()});for("destroy"===l&&(i._destroyed=[]),s=0,r=n.length;s<r;s++)"destroy"!==l?(n[s].accept(a[s]),"create"===l?u.push(o?i._wrapInEmptyGroup(n[s].toJSON()):a[s]):"update"===l&&i._updatePristineForModel(n[s],a[s])):i._removePristineForModel(n[s])},_updatePristineForModel:function(e,t){this._executeOnPristineForModel(e,function(e,r){ke.deepExtend(r[e],t)})},_executeOnPristineForModel:function(e,t){this._eachPristineItem(function(r){var i=N(r,e);if(i>-1)return t(i,r),!0})},_removePristineForModel:function(e){this._executeOnPristineForModel(e,function(e,t){t.splice(e,1)})},_readData:function(e){var t=this._isServerGrouped()?this.reader.groups:this.reader.data;return t.call(this.reader,e)},_eachPristineItem:function(e){var t=this,r=t.options,i=t._getCurrentRangeSpan();t._eachItem(t._pristineData,e),r.serverPaging&&r.useRanges&&be(i,function(r,i){t._eachItem(i.pristineData,e)})},_eachItem:function(e,t){e&&e.length&&(this._isServerGrouped()?P(e,t):t(e))},_pristineForModel:function(e){var t,r,i=function(i){if(r=N(i,e),r>-1)return t=i[r],!0};return this._eachPristineItem(i),t},_cancelModel:function(e){var t=this,r=this._pristineForModel(e);this._eachItem(this._data,function(i){var n=G(i,e);n>=0&&(!r||e.isNew()&&!r.__state__?(t._modelCanceled(e),i.splice(n,1),t._removeModelFromRanges(e)):(i[n].accept(r),"update"==r.__state__&&(i[n].dirty=!0)))})},_modelCanceled:we,_submit:function(t,r){var i=this;i.trigger(Ge,{type:"submit"}),i.trigger(Le),i.transport.submit(ge({success:function(r,i){var n=e.grep(t,function(e){return e.type==i})[0];n&&n.resolve({response:r,models:n.models,type:i})},error:function(e,r,n){for(var a=0;a<t.length;a++)t[a].reject(e);i.error(e,r,n)}},r))},_sendSubmit:function(t,r,i){var n=this,a=[];return n.options.batch&&(t.length&&a.push(e.Deferred(function(e){e.type="create",e.models=t})),r.length&&a.push(e.Deferred(function(e){e.type="update",e.models=r})),i.length&&a.push(e.Deferred(function(e){e.type="destroy",e.models=i})),n._submit(a,{data:{created:n.reader.serialize(b(t)),updated:n.reader.serialize(b(r)),destroyed:n.reader.serialize(b(i))}})),a},_promise:function(t,r,i){var n=this;return e.Deferred(function(e){n.trigger(Ge,{type:i}),n.trigger(Le),n.transport[i].call(n.transport,ge({success:function(t){e.resolve({response:t,models:r,type:i})},error:function(t,r,i){e.reject(t),n.error(t,r,i)}},t))}).promise()},_send:function(e,t){var r,i,n=this,a=[],s=n.reader.serialize(b(t));if(n.options.batch)t.length&&a.push(n._promise({data:{models:s}},t,e));else for(r=0,i=t.length;r<i;r++)a.push(n._promise({data:s[r]},[t[r]],e));return a},read:function(t){var r=this,i=r._params(t),n=e.Deferred();return r._queueRequest(i,function(){var e=r.trigger(Ge,{type:"read"});e?(r._dequeueRequest(),n.resolve(e)):(r.trigger(Le),r._ranges=[],r.trigger("reset"),r.online()?r.transport.read({data:i,success:function(e){r._ranges=[],r.success(e,i),n.resolve()},error:function(){var e=Xe.call(arguments);r.error.apply(r,e),n.reject.apply(n,e)}}):null!=r.options.offlineStorage&&(r.success(r.offlineData(),i),n.resolve()))}),n.promise()},_readAggregates:function(e){return this.reader.aggregates(e)},success:function(e){var r,i,n,a,s,o,u,l,h,d,f,c=this,g=c.options;if(c.trigger(je,{response:e,type:"read"}),c.online()){if(e=c.reader.parse(e),c._handleCustomErrors(e))return c._dequeueRequest(),t;c._total=c.reader.total(e),c._pageSize>c._total&&(c._pageSize=c._total,c.options.pageSize&&c.options.pageSize>c._pageSize&&(c._pageSize=c.options.pageSize)),c._aggregate&&g.serverAggregates&&(c._aggregateResult=c._readAggregates(e)),e=c._readData(e),c._destroyed=[]}else{for(e=c._readData(e),r=[],n={},a=c.reader.model,s=a?a.idField:"id",o=0;o<this._destroyed.length;o++)u=this._destroyed[o][s],n[u]=u;for(o=0;o<e.length;o++)l=e[o],h=l.__state__,"destroy"==h?n[l[s]]||this._destroyed.push(this._createNewModel(l)):r.push(l);e=r,c._total=e.length}if(c._pristineTotal=c._total,i=c._skip&&c._data.length&&c._skip<c._data.length,c.options.endless)for(i&&c._pristineData.splice(c._skip,c._pristineData.length),r=e.slice(0),d=0;d<r.length;d++)c._pristineData.push(r[d]);else c._pristineData=e.slice(0);if(c._detachObservableParents(),c.options.endless){for(c._data.unbind(Ae,c._changeHandler),c._isServerGrouped()&&c._data[c._data.length-1].value===e[0].value&&(q(c._data[c._data.length-1],e[0]),e.shift()),e=c._observe(e),i&&c._data.splice(c._skip,c._data.length),f=0;f<e.length;f++)c._data.push(e[f]);c._data.bind(Ae,c._changeHandler)}else c._data=c._observe(e);c._markOfflineUpdatesAsDirty(),c._storeData(),c._addRange(c._data),c._process(c._data),c._dequeueRequest()},_detachObservableParents:function(){if(this._data&&this._shouldDetachObservableParents)for(var e=0;e<this._data.length;e++)this._data[e].parent&&(this._data[e].parent=we)},_storeData:function(e){function t(e){var r,i,n,a=[];for(r=0;r<e.length;r++)i=e.at(r),n=i.toJSON(),s&&i.items?n.items=t(i.items):(n.uid=i.uid,o&&(i.isNew()?n.__state__="create":i.dirty&&(n.__state__="update"))),a.push(n);return a}var r,i,n,a,s=this._isServerGrouped(),o=this.reader.model;if(null!=this.options.offlineStorage){for(r=t(this._data),i=[],n=0;n<this._destroyed.length;n++)a=this._destroyed[n].toJSON(),a.__state__="destroy",i.push(a);this.offlineData(r.concat(i)),e&&(this._pristineData=this.reader.reader?this.reader.reader._wrapDataAccessBase(r):this.reader._wrapDataAccessBase(r))}},_addRange:function(e,r){var i=this,n=t!==r?r:i._skip||0,a=n+i._flatData(e,!0).length;i._ranges.push({start:n,end:a,data:e,pristineData:e.toJSON(),timestamp:i._timeStamp()}),i._sortRanges()},_sortRanges:function(){this._ranges.sort(function(e,t){return e.start-t.start})},error:function(e,t,r){this._dequeueRequest(),this.trigger(je,{}),this.trigger(Ne,{xhr:e,status:t,errorThrown:r})},_params:function(e){var t=this,r=ge({take:t.take(),skip:t.skip(),page:t.page(),pageSize:t.pageSize(),sort:t._sort,filter:t._filter,group:t._group,aggregate:t._aggregate},e);return t.options.serverPaging||(delete r.take,delete r.skip,delete r.page,delete r.pageSize),t.options.serverGrouping?t.reader.model&&r.group&&(r.group=B(r.group,t.reader.model)):delete r.group,t.options.serverFiltering?t.reader.model&&r.filter&&(r.filter=E(r.filter,t.reader.model)):delete r.filter,t.options.serverSorting?t.reader.model&&r.sort&&(r.sort=B(r.sort,t.reader.model)):delete r.sort,t.options.serverAggregates?t.reader.model&&r.aggregate&&(r.aggregate=B(r.aggregate,t.reader.model)):delete r.aggregate,r},_queueRequest:function(e,r){var i=this;i._requestInProgress?i._pending={callback:pe(r,i),options:e}:(i._requestInProgress=!0,i._pending=t,r())},_dequeueRequest:function(){var e=this;e._requestInProgress=!1,e._pending&&e._queueRequest(e._pending.options,e._pending.callback)},_handleCustomErrors:function(e){if(this.reader.errors){var t=this.reader.errors(e);if(t)return this.trigger(Ne,{xhr:null,status:"customerror",errorThrown:"custom error",errors:t}),!0}return!1},_shouldWrap:function(e){var t=this.reader.model;return!(!t||!e.length)&&!(e[0]instanceof t)},_observe:function(e){var t,r=this,i=r.reader.model;return r._shouldDetachObservableParents=!0,e instanceof rt?(r._shouldDetachObservableParents=!1,r._shouldWrap(e)&&(e.type=r.reader.model,e.wrapAll(e,e))):(t=r.pageSize()&&!r.options.serverPaging?W:rt,e=new t(e,r.reader.model),e.parent=function(){return r.parent()}),r._isServerGrouped()&&O(e,i),!(r._changeHandler&&r._data&&r._data instanceof rt)||r.options.useRanges&&r.options.serverPaging?r._changeHandler=pe(r._change,r):r._data.unbind(Ae,r._changeHandler),e.bind(Ae,r._changeHandler)},_updateTotalForAction:function(e,t){var r=this,i=parseInt(r._total,10);y(r._total)||(i=parseInt(r._pristineTotal,10)),"add"===e?i+=t.length:"remove"===e?i-=t.length:"itemchange"===e||"sync"===e||r.options.serverPaging?"sync"===e&&(i=r._pristineTotal=parseInt(r._total,10)):i=r._pristineTotal,r._total=i},_change:function(e){var t,r,i,n=this,a=e?e.action:"";if("remove"===a)for(t=0,r=e.items.length;t<r;t++)e.items[t].isNew&&e.items[t].isNew()||n._destroyed.push(e.items[t]);!n.options.autoSync||"add"!==a&&"remove"!==a&&"itemchange"!==a?(n._updateTotalForAction(a,e?e.items:[]),n._process(n._data,e)):(i=function(t){"sync"===t.action&&(n.unbind("change",i),n._updateTotalForAction(a,e.items))},n.first("change",i),n.sync())},_calculateAggregates:function(e,t){t=t||{};var r=new a(e),i=t.aggregate,n=t.filter;return n&&(r=r.filter(n)),r.aggregate(i)},_process:function(e,r){var i,n=this,a={};n.options.serverPaging!==!0&&(a.skip=n._skip,a.take=n._take||n._pageSize,a.skip===t&&n._page!==t&&n._pageSize!==t&&(a.skip=(n._page-1)*n._pageSize),n.options.useRanges&&(a.skip=n.currentRangeStart())),n.options.serverSorting!==!0&&(a.sort=n._sort),n.options.serverFiltering!==!0&&(a.filter=n._filter),n.options.serverGrouping!==!0&&(a.group=n._group),n.options.serverAggregates!==!0&&(a.aggregate=n._aggregate),n.options.serverGrouping&&n._clearEmptyGroups(e),i=n._queryProcess(e,a),n.options.serverAggregates!==!0&&(n._aggregateResult=n._calculateAggregates(i.dataToAggregate||e,a)),n.view(i.data),n._setFilterTotal(i.total,!1),r=r||{},r.items=r.items||n._view,n.trigger(Ae,r)},_clearEmptyGroups:function(e){var t,r;for(t=e.length-1;t>=0;t--)r=e[t],r.hasSubgroups?this._clearEmptyGroups(r.items):r.items&&!r.items.length&&$e.apply(r.parent(),[t,1])},_queryProcess:function(e,t){return this.options.inPlaceSort?a.process(e,t,this.options.inPlaceSort):a.process(e,t)},_mergeState:function(r){var i=this;return r!==t&&(i._pageSize=r.pageSize,i._page=r.page,i._sort=r.sort,i._filter=r.filter,i._group=r.group,i._aggregate=r.aggregate,i._skip=i._currentRangeStart=r.skip,i._take=r.take,i._skip===t&&(i._skip=i._currentRangeStart=i.skip(),r.skip=i.skip()),i._take===t&&i._pageSize!==t&&(i._take=i._pageSize,r.take=i._take),r.sort&&(i._sort=r.sort=s(r.sort)),r.filter&&(i._filter=r.filter=i.options.accentFoldingFiltering&&!e.isEmptyObject(r.filter)?e.extend({},u(r.filter),{accentFoldingFiltering:i.options.accentFoldingFiltering}):u(r.filter)),r.group&&(i._group=r.group=g(r.group)),r.aggregate&&(i._aggregate=r.aggregate=c(r.aggregate))),r},query:function(r){var i,n,a,s=this.options.serverSorting||this.options.serverPaging||this.options.serverFiltering||this.options.serverGrouping||this.options.serverAggregates;return s||(this._data===t||0===this._data.length)&&!this._destroyed.length?(this.options.endless&&(n=r.pageSize-this.pageSize(),n>0?(n=this.pageSize(),r.page=r.pageSize/n,r.pageSize=n):(r.page=1,this.options.endless=!1)),this.read(this._mergeState(r))):(a=this.trigger(Ge,{type:"read"}),a||(this.trigger(Le),i=this._queryProcess(this._data,this._mergeState(r)),this._setFilterTotal(i.total,!0),this._aggregateResult=this._calculateAggregates(i.dataToAggregate||this._data,r),this.view(i.data),this.trigger(je,{type:"read"}),this.trigger(Ae,{items:i.data})),e.Deferred().resolve(a).promise())},_setFilterTotal:function(e,r){var i=this;i.options.serverFiltering||(e!==t?i._total=e:r&&(i._total=i._data.length))},fetch:function(e){var t=this,r=function(r){r!==!0&&Fe(e)&&e.call(t)};return this._query().done(r)},_query:function(e){var t=this;return t.query(ge({},{page:t.page(),pageSize:t.pageSize(),sort:t.sort(),filter:t.filter(),group:t.group(),aggregate:t.aggregate()},e))},next:function(e){var t=this,r=t.page(),i=t.total();if(e=e||{},r&&!(i&&r+1>t.totalPages()))return t._skip=t._currentRangeStart=r*t.take(),r+=1,e.page=r,t._query(e),r},prev:function(e){var t=this,r=t.page();if(e=e||{},r&&1!==r)return t._skip=t._currentRangeStart=t._skip-t.take(),r-=1,e.page=r,t._query(e),r},page:function(e){var r,i=this;return e!==t?(e=Je.max(Je.min(Je.max(e,1),i.totalPages()),1),i._query(i._pageableQueryOptions({page:e})),t):(r=i.skip(),r!==t?Je.round((r||0)/(i.take()||1))+1:t)},pageSize:function(e){var r=this;return e!==t?(r._query(r._pageableQueryOptions({pageSize:e,page:1})),t):r.take()},sort:function(e){var r=this;return e!==t?(r._query({sort:e}),t):r._sort},filter:function(e){var r=this;return e===t?r._filter:(r.trigger("reset"),r._query({filter:e,page:1}),t)},group:function(e){var r=this;return e!==t?(r._query({group:e}),t):r._group},total:function(){return parseInt(this._total||0,10)},aggregate:function(e){var r=this;return e!==t?(r._query({aggregate:e}),t):r._aggregate},aggregates:function(){var e=this._aggregateResult;return ve(e)&&(e=this._emptyAggregates(this.aggregate())),e},_emptyAggregates:function(e){var t,r,i={};if(!ve(e))for(t={},me(e)||(e=[e]),r=0;r<e.length;r++)t[e[r].aggregate]=0,i[e[r].field]=t;return i},_pageableQueryOptions:function(e){return e},_wrapInEmptyGroup:function(e){var t,r,i,n,a=this.group();for(i=a.length-1,n=0;i>=n;i--)r=a[i],t={value:e.get?e.get(r.field):e[r.field],field:r.field,items:t?[t]:[e],hasSubgroups:!!t,aggregates:this._emptyAggregates(r.aggregates)};return t},totalPages:function(){var e=this,t=e.pageSize()||e.total();return Je.ceil((e.total()||0)/t)},inRange:function(e,t){var r=this,i=Je.min(e+t,r.total());return!r.options.serverPaging&&r._data.length>0||r._findRange(e,i).length>0},lastRange:function(){var e=this._ranges;return e[e.length-1]||{start:0,end:0,data:[]}},firstItemUid:function(){var e=this._ranges;return e.length&&e[0].data.length&&e[0].data[0].uid},enableRequestsInProgress:function(){this._skipRequestsInProgress=!1},_timeStamp:function(){return(new Date).getTime()},range:function(e,r,i){this._currentRequestTimeStamp=this._timeStamp(),this._skipRequestsInProgress=!0,e=Je.min(e||0,this.total()),i=Fe(i)?i:we;var n,a=this,s=Je.max(Je.floor(e/r),0)*r,o=Je.min(s+r,a.total());return n=a._findRange(e,Je.min(e+r,a.total())),n.length||0===a.total()?(a._processRangeData(n,e,r,s,o),i(),t):(r!==t&&(a._rangeExists(s,o)?s<e&&a.prefetch(o,r,function(){a.range(e,r,i)}):a.prefetch(s,r,function(){e>s&&o<a.total()&&!a._rangeExists(o,Je.min(o+r,a.total()))?a.prefetch(o,r,function(){a.range(e,r,i)}):a.range(e,r,i)})),t)},_findRange:function(e,r){var i,n,a,o,u,l,h,d,f,c,g,_,v=this,m=v._ranges,y=[],S=v.options,b=S.serverSorting||S.serverPaging||S.serverFiltering||S.serverGrouping||S.serverAggregates;for(n=0,g=m.length;n<g;n++)if(i=m[n],e>=i.start&&e<=i.end){for(c=0,a=n;a<g;a++)if(i=m[a],f=v._flatData(i.data,!0),f.length&&e+c>=i.start&&(l=i.data,h=i.end,b||(S.inPlaceSort?d=v._queryProcess(i.data,{filter:v.filter()}):(_=p(v.group()||[]).concat(s(v.sort()||[])),d=v._queryProcess(i.data,{sort:_,filter:v.filter()})),f=l=d.data,d.total!==t&&(h=d.total)),o=0,e+c>i.start&&(o=e+c-i.start),u=f.length,h>r&&(u-=h-r),c+=u-o,y=v._mergeGroups(y,l,o,u),r<=i.end&&c==r-e))return y;break}return[]},_mergeGroups:function(e,t,r,i){if(this._isServerGrouped()){var n,a=t.toJSON();return e.length&&(n=e[e.length-1]),D(n,a,r,i),e.concat(a)}return e.concat(t.slice(r,i))},_processRangeData:function(e,r,i,n,a){var s,o,u,l,h=this;h._pending=t,h._skip=r>h.skip()?Je.min(a,(h.totalPages()-1)*h.take()):n,h._currentRangeStart=r,h._take=i,s=h.options.serverPaging,o=h.options.serverSorting,u=h.options.serverFiltering,l=h.options.serverAggregates;try{h.options.serverPaging=!0,h._isServerGrouped()||h.group()&&h.group().length||(h.options.serverSorting=!0),h.options.serverFiltering=!0,h.options.serverPaging=!0,h.options.serverAggregates=!0,s&&(h._detachObservableParents(),h._data=e=h._observe(e)),h._process(e)}finally{h.options.serverPaging=s,h.options.serverSorting=o,h.options.serverFiltering=u,h.options.serverAggregates=l}},skip:function(){var e=this;return e._skip===t?e._page!==t?(e._page-1)*(e.take()||1):t:e._skip},currentRangeStart:function(){return this._currentRangeStart||0},take:function(){return this._take||this._pageSize},_prefetchSuccessHandler:function(e,t,r,i){var n=this,a=n._timeStamp();return function(s){var o,u,l,h=!1,d={start:e,end:t,data:[],timestamp:n._timeStamp()};if(n._dequeueRequest(),n.trigger(je,{response:s,type:"read"}),s=n.reader.parse(s),l=n._readData(s),l.length){for(o=0,u=n._ranges.length;o<u;o++)if(n._ranges[o].start===e){h=!0,d=n._ranges[o],d.pristineData=l,d.data=n._observe(l),d.end=d.start+n._flatData(d.data,!0).length,n._sortRanges();break}h||n._addRange(n._observe(l),e)}n._total=n.reader.total(s),(i||a>=n._currentRequestTimeStamp||!n._skipRequestsInProgress)&&(r&&l.length?r():n.trigger(Ae,{}))}},prefetch:function(e,t,r){var i=this,n=Je.min(e+t,i.total()),a={take:t,skip:e,page:e/t+1,pageSize:t,sort:i._sort,filter:i._filter,group:i._group,aggregate:i._aggregate};i._rangeExists(e,n)?r&&r():(clearTimeout(i._timeout),i._timeout=setTimeout(function(){i._queueRequest(a,function(){i.trigger(Ge,{type:"read"})?i._dequeueRequest():i.transport.read({data:i._params(a),success:i._prefetchSuccessHandler(e,n,r),error:function(){var e=Xe.call(arguments);i.error.apply(i,e)}})})},100))},_multiplePrefetch:function(e,t,r){var i=this,n=Je.min(e+t,i.total()),a={take:t,skip:e,page:e/t+1,pageSize:t,sort:i._sort,filter:i._filter,group:i._group,aggregate:i._aggregate};i._rangeExists(e,n)?r&&r():i.trigger(Ge,{type:"read"})||i.transport.read({data:i._params(a),success:i._prefetchSuccessHandler(e,n,r,!0)})},_rangeExists:function(e,t){var r,i,n=this,a=n._ranges;for(r=0,i=a.length;r<i;r++)if(a[r].start<=e&&a[r].end>=t)return!0;return!1},_getCurrentRangeSpan:function(){var e,t,r=this,i=r._ranges,n=r.currentRangeStart(),a=n+(r.take()||0),s=[],o=i.length;for(t=0;t<o;t++)e=i[t],(e.start<=n&&e.end>=n||e.start>=n&&e.start<=a)&&s.push(e);return s},_removeModelFromRanges:function(e){var t,r,i,n=this;for(r=0,i=this._ranges.length;r<i;r++)t=this._ranges[r],n._removeModelFromRange(t,e);n._updateRangesLength()},_removeModelFromRange:function(e,t){this._eachItem(e.data,function(e){var r,i;for(r=0;r<e.length;r++)if(i=e[r],i.uid&&i.uid==t.uid){[].splice.call(e,r,1);break}})},_insertModelInRange:function(e,t){var r,i,n=this,a=n._ranges||[],s=a.length;for(i=0;i<s;i++)if(r=a[i],r.start<=e&&r.end>=e){n._getByUid(t.uid,r.data)||(n._isServerGrouped()?r.data.splice(e,0,n._wrapInEmptyGroup(t)):r.data.splice(e,0,t));break}n._updateRangesLength()},_updateRangesLength:function(){var e,t,r=this,i=r._ranges||[],n=i.length,a=!1,s=0,o=0;for(t=0;t<n;t++)e=i[t],o=r._flatData(e.data,!0).length-Je.abs(e.end-e.start),a||0===o?a&&(e.start+=s,e.end+=s):(a=!0,s=o,e.end+=s)}}),le={},le.create=function(t,r,i){var n,a=t.transport?e.extend({},t.transport):null;return a?(a.read=typeof a.read===qe?{url:a.read}:a.read,"jsdo"===t.type&&(a.dataSource=i),t.type&&(ke.data.transports=ke.data.transports||{},ke.data.schemas=ke.data.schemas||{},ke.data.transports[t.type]?_e(ke.data.transports[t.type])?a=ge(!0,{},ke.data.transports[t.type],a):n=new ke.data.transports[t.type](ge(a,{data:r})):ke.logToConsole("Unknown DataSource transport type '"+t.type+"'.\nVerify that registration scripts for this type are included after Kendo UI on the page.","warn"),t.schema=ge(!0,{},ke.data.schemas[t.type],t.schema)),n||(n=Fe(a.read)?a:new ae(a))):n=new ne({data:t.data||[]}),n},ue.create=function(e){(me(e)||e instanceof rt)&&(e={data:e});var r,i,n,a=e||{},s=a.data,o=a.fields,u=a.table,l=a.select,h={};if(s||!o||a.transport||(u?s=U(u,o):l&&(s=H(l,o),a.group===t&&s[0]&&s[0].optgroup!==t&&(a.group="optgroup"))),ke.data.Model&&o&&(!a.schema||!a.schema.model)){for(r=0,i=o.length;r<i;r++)n=o[r],n.type&&(h[n.field]=n);ve(h)||(a.schema=ge(!0,a.schema,{model:{fields:h}}))}return a.data=s,l=null,a.select=null,u=null,a.table=null,a instanceof ue?a:new ue(a)},he=Y.define({idField:"id",init:function(e){var t,r=this,i=r.hasChildren||e&&e.hasChildren,n="items",a={};ke.data.Model.fn.init.call(r,e),typeof r.children===qe&&(n=r.children),a={schema:{data:n,model:{hasChildren:i,id:r.idField,fields:r.fields}}},typeof r.children!==qe&&ge(a,r.children),a.data=e,i||(i=a.schema.data),typeof i===qe&&(i=ke.getter(i)),Fe(i)&&(t=i.call(r,r),r.hasChildren=(!t||0!==t.length)&&!!t),r._childrenOptions=a,r.hasChildren&&r._initChildren(),r._loaded=!(!e||!e._loaded)},_initChildren:function(){var e,t,r,i=this;i.children instanceof de||(e=i.children=new de(i._childrenOptions),t=e.transport,r=t.parameterMap,t.parameterMap=function(e,t){return e[i.idField||"id"]=i.id,r&&(e=r(e,t)),e},e.parent=function(){return i},e.bind(Ae,function(e){e.node=e.node||i,i.trigger(Ae,e)}),e.bind(Ne,function(e){var t=i.parent();t&&(e.node=e.node||i,t.trigger(Ne,e))}),i._updateChildrenField())},append:function(e){this._initChildren(),this.loaded(!0),this.children.add(e)},hasChildren:!1,level:function(){for(var e=this.parentNode(),t=0;e&&e.parentNode;)t++,e=e.parentNode?e.parentNode():null;return t},_updateChildrenField:function(){var e=this._childrenOptions.schema.data;this[e||"items"]=this.children.data()},_childrenLoaded:function(){this._loaded=!0,this._updateChildrenField()},load:function(){var r,i,n={},a="_query";return this.hasChildren?(this._initChildren(),r=this.children,n[this.idField||"id"]=this.id,this._loaded||(r._data=t,a="read"),r.one(Ae,pe(this._childrenLoaded,this)),this._matchFilter&&(n.filter={field:"_matchFilter",operator:"eq",value:!0}),i=r[a](n)):this.loaded(!0),i||e.Deferred().resolve().promise()},parentNode:function(){var e=this.parent();return e.parent()},loaded:function(e){return e===t?this._loaded:(this._loaded=e,t)},shouldSerialize:function(e){return Y.fn.shouldSerialize.call(this,e)&&"children"!==e&&"_loaded"!==e&&"hasChildren"!==e&&"_childrenOptions"!==e}}),de=ue.extend({init:function(e){var t=he.define({children:e});e.filter&&!e.serverFiltering&&(this._hierarchicalFilter=e.filter,e.filter=null),ue.fn.init.call(this,ge(!0,{},{schema:{modelBase:t,model:t}},e)),this._attachBubbleHandlers()},_attachBubbleHandlers:function(){var e=this;e._data.bind(Ne,function(t){e.trigger(Ne,t)})},read:function(e){var t=ue.fn.read.call(this,e);return this._hierarchicalFilter&&(this._data&&this._data.length>0?this.filter(this._hierarchicalFilter):(this.options.filter=this._hierarchicalFilter,this._filter=u(this.options.filter),this._hierarchicalFilter=null)),t},remove:function(e){var t,r=e.parentNode(),i=this;return r&&r._initChildren&&(i=r.children),t=ue.fn.remove.call(i,e),r&&!i.data().length&&(r.hasChildren=!1),t},success:J("success"),data:J("data"),insert:function(e,t){var r=this.parent();return r&&r._initChildren&&(r.hasChildren=!0,r._initChildren()),ue.fn.insert.call(this,e,t)},filter:function(e){return e===t?this._filter:(!this.options.serverFiltering&&this._markHierarchicalQuery(e)&&(e={logic:"or",filters:[e,{field:"_matchFilter",operator:"equals",value:!0}]}),this.trigger("reset"),this._query({filter:e,page:1}),t)},_markHierarchicalQuery:function(t){var r,i,n,s,o,l=this.options.accentFoldingFiltering;return t=l?e.extend({},u(t),{accentFoldingFiltering:l}):u(t),t&&0!==t.filters.length?(r=a.filterExpr(t),n=r.fields,s=r.operators,i=o=Function("d, __f, __o","return "+r.expression),(n.length||s.length)&&(o=function(e){return i(e,n,s)}),this._updateHierarchicalFilter(o),!0):(this._updateHierarchicalFilter(function(){return!0}),!1)},_updateHierarchicalFilter:function(e){var t,r,i=this._data,n=!1;for(r=0;r<i.length;r++)t=i[r],t.hasChildren?(t._matchFilter=t.children._updateHierarchicalFilter(e),t._matchFilter||(t._matchFilter=e(t))):t._matchFilter=e(t),t._matchFilter&&(n=!0);return n},_find:function(e,t){var r,i,n,a,s=this._data;if(s){if(n=ue.fn[e].call(this,t))return n;for(s=this._flatData(this._data),r=0,i=s.length;r<i;r++)if(a=s[r].children,a instanceof de&&(n=a[e](t)))return n}},get:function(e){return this._find("get",e)},getByUid:function(e){return this._find("getByUid",e)}}),de.create=function(e){e=e&&e.push?{data:e}:e;var t=e||{},r=t.data,i=t.fields,n=t.list;return r&&r._dataSource?r._dataSource:(r||!i||t.transport||n&&(r=V(n,i)),t.data=r,t instanceof de?t:new de(t))},fe=ke.Observable.extend({init:function(e,t,r){ke.Observable.fn.init.call(this),this._prefetching=!1,this.dataSource=e,this.prefetch=!r;var i=this;e.bind("change",function(){i._change()}),e.bind("reset",function(){i._reset()}),this._syncWithDataSource(),this.setViewSize(t)},setViewSize:function(e){this.viewSize=e,this._recalculate()},at:function(e){var r=this.pageSize,i=!0;return e>=this.total()?(this.trigger("endreached",{index:e}),null):this.useRanges?this.useRanges?((e<this.dataOffset||e>=this.skip+r)&&(i=this.range(Math.floor(e/r)*r)),e===this.prefetchThreshold&&this._prefetch(),e===this.midPageThreshold?this.range(this.nextMidRange,!0):e===this.nextPageThreshold?this.range(this.nextFullRange):e===this.pullBackThreshold&&this.range(this.offset===this.skip?this.previousMidRange:this.previousFullRange),i?this.dataSource.at(e-this.dataOffset):(this.trigger("endreached",{index:e}),null)):t:this.dataSource.view()[e]},indexOf:function(e){return this.dataSource.data().indexOf(e)+this.dataOffset},total:function(){return parseInt(this.dataSource.total(),10)},next:function(){var e=this,t=e.pageSize,r=e.skip-e.viewSize+t,i=Je.max(Je.floor(r/t),0)*t;this.offset=r,this.dataSource.prefetch(i,t,function(){e._goToRange(r,!0)})},range:function(e,t){if(this.offset===e)return!0;var r=this,i=this.pageSize,n=Je.max(Je.floor(e/i),0)*i,a=this.dataSource;return t&&(n+=i),a.inRange(e,i)?(this.offset=e,this._recalculate(),this._goToRange(e),!0):!this.prefetch||(a.prefetch(n,i,function(){r.offset=e,r._recalculate(),r._goToRange(e,!0)}),!1)},syncDataSource:function(){var e=this.offset;this.offset=null,this.range(e)},destroy:function(){this.unbind()},_prefetch:function(){var e=this,t=this.pageSize,r=this.skip+t,i=this.dataSource;i.inRange(r,t)||this._prefetching||!this.prefetch||(this._prefetching=!0,this.trigger("prefetching",{skip:r,take:t}),i.prefetch(r,t,function(){e._prefetching=!1,e.trigger("prefetched",{skip:r,take:t})}))},_goToRange:function(e,t){this.offset===e&&(this.dataOffset=e,this._expanding=t,this.dataSource.range(e,this.pageSize),this.dataSource.enableRequestsInProgress())},_reset:function(){this._syncPending=!0},_change:function(){var e=this.dataSource;this.length=this.useRanges?e.lastRange().end:e.view().length,this._syncPending&&(this._syncWithDataSource(),this._recalculate(),this._syncPending=!1,this.trigger("reset",{offset:this.offset})),this.trigger("resize"),this._expanding&&this.trigger("expand"),delete this._expanding},_syncWithDataSource:function(){var e=this.dataSource;this._firstItemUid=e.firstItemUid(),this.dataOffset=this.offset=e.skip()||0,this.pageSize=e.pageSize(),this.useRanges=e.options.serverPaging},_recalculate:function(){var e=this.pageSize,t=this.offset,r=this.viewSize,i=Math.ceil(t/e)*e;this.skip=i,this.midPageThreshold=i+e-1,this.nextPageThreshold=i+r-1,this.prefetchThreshold=i+Math.floor(e/3*2),this.pullBackThreshold=this.offset-1,this.nextMidRange=i+e-r,this.nextFullRange=i,this.previousMidRange=t-r,this.previousFullRange=i-e}}),ce=ke.Observable.extend({init:function(e,t){var r=this;ke.Observable.fn.init.call(r),this.dataSource=e,this.batchSize=t,this._total=0,this.buffer=new fe(e,3*t),this.buffer.bind({endreached:function(e){r.trigger("endreached",{index:e.index})},prefetching:function(e){r.trigger("prefetching",{skip:e.skip,take:e.take})},prefetched:function(e){r.trigger("prefetched",{skip:e.skip,take:e.take})},reset:function(){r._total=0,r.trigger("reset")},resize:function(){r._total=Math.ceil(this.length/r.batchSize),r.trigger("resize",{total:r.total(),offset:this.offset})}})},syncDataSource:function(){this.buffer.syncDataSource()},at:function(e){var t,r,i=this.buffer,n=e*this.batchSize,a=this.batchSize,s=[];for(i.offset>n&&i.at(i.offset-1),r=0;r<a&&(t=i.at(n+r),null!==t);r++)s.push(t);return s},total:function(){return this._total},destroy:function(){this.buffer.destroy(),this.unbind()}}),ge(!0,ke.data,{readers:{json:oe},Query:a,DataSource:ue,HierarchicalDataSource:de,Node:he,ObservableObject:$,ObservableArray:rt,LazyObservableArray:W,LocalTransport:ne,RemoteTransport:ae,Cache:se,DataReader:oe,Model:Y,Buffer:fe,BatchBuffer:ce})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,r){(r||t)()});
//# sourceMappingURL=kendo.data.min.js.map
