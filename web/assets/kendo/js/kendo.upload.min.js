/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.upload.min",["kendo.core.min"],e)}(function(){return function(e,t){function a(t){return e.map(n(t),function(e){return e.name}).join(", ")}function n(e){var t=e[0];return t.files?i(t.files):[{name:s(t.value),extension:r(t.value),size:null}]}function i(t){return e.map(t,function(e){return o(e)})}function o(e){var t=e.name||e.fileName;return{name:U.htmlEncode(t),extension:r(t),size:"number"==typeof e.size?e.size:e.fileSize,rawFile:e}}function r(e){var t=e.match(x);return t?t[0]:""}function s(e){var t=e.lastIndexOf("\\");return t!=-1?e.substr(t+1):e}function l(t,a){var n=U.guid();return e.map(t,function(e){return e.uid=a?U.guid():n,e})}function p(e,t){var a,n=d(t.allowedExtensions),i=t.maxFileSize,o=t.minFileSize;for(a=0;a<e.length;a++)u(e[a],n),c(e[a],o,i)}function d(t){var a=e.map(t,function(e){var t="."===e.substring(0,1)?e:"."+e;return t.toLowerCase()});return a}function u(t,a){a.length>0&&a.indexOf(t.extension.toLowerCase())<0&&(t.validationErrors=t.validationErrors||[],e.inArray(j,t.validationErrors)===-1&&t.validationErrors.push(j))}function c(t,a,n){0!==a&&t.size<a&&(t.validationErrors=t.validationErrors||[],e.inArray(V,t.validationErrors)===-1&&t.validationErrors.push(V)),0!==n&&t.size>n&&(t.validationErrors=t.validationErrors||[],e.inArray(O,t.validationErrors)===-1&&t.validationErrors.push(O))}function f(e){var t,a=0;if("number"!=typeof e[0].size)return"";for(t=0;t<e.length;t++)e[t].size&&(a+=e[t].size);return a/=1024,a<1024?a.toFixed(2)+" KB":(a/1024).toFixed(2)+" MB"}function h(t){return!t.multiple&&e(".k-file",t.wrapper).length>1}function m(a,n,i,o){var r,s;return n._supportsRemove()?(r=a.data("fileNames"),s=e.map(r,function(e){return e.name}),o===!1?(n._removeFileEntry(a),t):(n._submitRemove(s,i,function(e,t,i){var o=n.trigger(R,{operation:"remove",files:r,response:e,XMLHttpRequest:i});o||n._removeFileEntry(a)},function(e){h(n)&&n._removeFileEntry(a),n.trigger(I,{operation:"remove",files:r,XMLHttpRequest:e}),z("Server response: "+e.responseText)}),t)):(!h(n)&&o||n._removeFileEntry(a),t)}function k(t,a,n){var i=!1,o="";try{o=e.parseJSON(v(t)),i=!0}catch(r){n()}i&&a(o)}function v(e){return t!==e&&""!==e||(e="{}"),e}function g(e){e.stopPropagation(),e.preventDefault()}function _(e,t,a,n){var i,o;e.on("dragenter"+t,function(e){a(e),o=new Date,i||(i=setInterval(function(){var e=new Date-o;e>100&&(n(),clearInterval(i),i=null)},100))}).on("dragover"+t,function(){o=new Date})}function y(e){return e.is(".k-file-progress, .k-file-success, .k-file-error")}function F(t){return e(t.target).closest(".k-file")}var w,C,U=window.kendo,S=U.ui.Widget,b=U.antiForgeryTokens,z=U.logToConsole,x=/\.([^\.]+)$/,E=".kendoUpload",D="select",A="upload",R="success",I="error",P="complete",T="cancel",N="clear",B="pause",H="resume",q="progress",M="remove",L="validationErrors",O="invalidMaxFileSize",V="invalidMinFileSize",j="invalidFileExtension",X=1e3,Z=2e3,W={loading:"k-i-loading",warning:"k-i-warning",success:"k-i-check"},K=S.extend({init:function(t,a){var n,i,o,r=this;S.fn.init.call(r,t,a),r.name=t.name,r.multiple=r.options.multiple,r.directory=r.options.directory,r.localization=r.options.localization,n=r.element,r.wrapper=n.closest(".k-upload"),0===r.wrapper.length&&(r.wrapper=r._wrapInput(n)),r._activeInput(n),r.toggle(r.options.enabled),i=r._ns=E+"-"+U.guid(),n.closest("form").on("submit"+i,e.proxy(r._onParentFormSubmit,r)).on("reset"+i,e.proxy(r._onParentFormReset,r)),r.options.async.saveUrl?(r._module=r._supportsFormData()?new C(r):new w(r),r._async=!0,o=r.options.files,o.length>0&&r._renderInitialFiles(o)):r._module=new G(r),r._supportsDrop()&&(""!==r.options.dropZone?r._setupCustomDropZone():r._setupDropZone()),r.wrapper.on("click",".k-upload-action",e.proxy(r._onFileAction,r)).on("click",".k-clear-selected",e.proxy(r._onClearSelected,r)).on("click",".k-upload-selected",e.proxy(r._onUploadSelected,r)),r.element.val()&&r._onInputChange({target:r.element})},events:[D,A,R,I,P,T,N,q,M,B,H],options:{name:"Upload",enabled:!0,multiple:!0,directory:!1,showFileList:!0,template:"",files:[],async:{autoRetryAfter:0,bufferChunkSize:1e7,maxAutoRetries:1,removeVerb:"POST",autoUpload:!0,withCredentials:!0,accept:"*/*; q=0.5, application/json",useArrayBuffer:!1},localization:{select:"Select files...",cancel:"Cancel",retry:"Retry",remove:"Remove",pause:"Pause",resume:"Resume",clearSelectedFiles:"Clear",uploadSelectedFiles:"Upload",dropFilesHere:"Drop files here to upload",invalidFiles:"Invalid file(s). Please check file upload requirements.",statusUploading:"uploading",statusUploaded:"uploaded",statusWarning:"warning",statusFailed:"failed",headerStatusUploading:"Uploading...",headerStatusPaused:"Paused",headerStatusUploaded:"Done",invalidMaxFileSize:"File size too large.",invalidMinFileSize:"File size too small.",invalidFileExtension:"File type not allowed."},validation:{allowedExtensions:[],maxFileSize:0,minFileSize:0},dropZone:""},setOptions:function(e){var t=this,a=t.element;S.fn.setOptions.call(t,e),t.multiple=t.options.multiple,t.directory=t.options.directory,a.attr("multiple",!!t._supportsMultiple()&&t.multiple),t.directory&&(a.attr("webkitdirectory",t.directory),a.attr("directory",t.directory)),t.toggle(t.options.enabled)},enable:function(e){e=t===e||e,this.toggle(e)},disable:function(){this.toggle(!1)},toggle:function(e){e=t===e?e:!e,this.wrapper.toggleClass("k-state-disabled",e),this.element.prop("disabled",e)},focus:function(){this.element.focus()},destroy:function(){var t=this,a=e(t.options.dropZone);e(document).add(e(".k-dropzone",t.wrapper)).add(t.wrapper.closest("form")).off(t._ns),a.length>0&&a.off(t._ns),e(t.element).off(E),S.fn.destroy.call(t)},pause:function(t){this._module.onPause({target:e(t,this.wrapper)});var a=t.find(".k-i-pause-sm");a.removeClass("k-i-pause-sm").addClass("k-i-play-sm").attr("title",this.localization.resume),e(a).parent().attr("aria-label",this.localization.resume)},resume:function(t){this._module.onResume({target:e(t,this.wrapper)});var a=t.find(".k-i-play-sm");a.removeClass("k-i-play-sm").addClass("k-i-pause-sm").attr("title",this.localization.pause),e(a).parent().attr("aria-label",this.localization.pause)},upload:function(){var e=this;e._module.onSaveSelected()},getFiles:function(){var t,a,n,i=this,o=[],r=i.wrapper.find(".k-file");for(a=0;a<r.length;a++)if(t=e(r[a]).data("fileNames"))for(n=0;n<t.length;n++)o.push(t[n]);return o},clearAllFiles:function(){var e=this,t=e.wrapper.find(".k-file");t.each(function(t,a){e._removeFileByDomElement(a,!1)})},removeAllFiles:function(){var e=this,t=e.wrapper.find(".k-file");t.each(function(t,a){e._removeFileByDomElement(a,!0)})},removeFileByUid:function(e){this._removeFileByUid(e,!0)},clearFileByUid:function(e){this._removeFileByUid(e,!1)},_removeFileByUid:function(t,a){var n,i=this;"string"==typeof t&&(n=e(".k-file["+U.attr("uid")+'="'+t+'"]',i.wrapper),n.length>0&&i._removeFileByDomElement(n,a))},clearFile:function(e){this._removeFile(e,!1)},removeFile:function(e){this._removeFile(e,!0)},_removeFile:function(t,a){var n,i=this,o=i.wrapper.find(".k-file");"function"==typeof t&&o.each(function(o,r){n=e(r).data("fileNames"),t(n)&&i._removeFileByDomElement(r,a)})},_removeFileByDomElement:function(t,a){var n,i=this,o={target:e(t,i.wrapper)};i.options.async.saveUrl?(e(t).hasClass("k-file-progress")?i._module.onCancel(o):i._module.onRemove(o,{},a),n=e(".k-file",i.wrapper),0===n.length?i._hideHeaderUploadstatus():i._updateHeaderUploadStatus()):i._module.onRemove(o,{},a)},_addInput:function(t){if(t[0].nodeType){var a=this,n=t.clone().val("");n.insertAfter(a.element).data("kendo"+a.options.prefix+a.options.name,a),e(a.element).hide().attr("tabindex","-1").removeAttr("id").off(E),a._activeInput(n),a.element.focus()}},_activeInput:function(t){var a=this,n=a.wrapper;a.element=t,a.directory&&(t.attr("webkitdirectory",a.directory),t.attr("directory",a.directory)),t.attr("multiple",!!a._supportsMultiple()&&a.multiple).attr("autocomplete","off").on("click"+E,function(e){n.hasClass("k-state-disabled")&&e.preventDefault()}).on("focus"+E,function(){e(this).parent().addClass("k-state-focused")}).on("blur"+E,function(){e(this).parent().removeClass("k-state-focused")}).on("change"+E,e.proxy(a._onInputChange,a)).on("keydown"+E,e.proxy(a._onInputKeyDown,a))},_onInputKeyDown:function(e){var t=this,a=t.wrapper.find(".k-upload-action:visible:first");e.keyCode===U.keys.TAB&&a.length>0&&!e.shiftKey&&(e.preventDefault(),a.focus())},_onInputChange:function(t){var a,n=this,i=e(t.target),o=l(n._inputFiles(i),n._isAsyncNonBatch());p(o,n.options.validation),a=n.trigger(D,{files:o}),a?(n._addInput(i),i.remove()):n._module.onSelect({target:i},o)},_readDirectory:function(t){var a=new e.Deferred,n=t.createReader(),i=[],o=function(){n.readEntries(function(e){e.length?(i=i.concat(e),o()):a.resolve(i)},a.reject)};return o(),a.promise()},_readFile:function(e){var t=this,a=e.fullPath;e.file(function(e){e.relativePath=a.slice(1),t.droppedFolderFiles.push(e),t.droppedFolderCounter--,0===t.droppedFolderCounter&&setTimeout(function(){0===t.droppedFolderCounter&&t.droppedFolderFiles.length&&(t._proceedDroppedItems(t.droppedFolderFiles),t.droppedFolderFiles=[])},0)},function(){z("File error.")})},_traverseFileTree:function(e,t){var a=this;t||a.droppedFolderCounter--,this._readDirectory(e).then(function(e){a.droppedFolderCounter+=e.length;for(var t=0;t<e.length;t++)e[t].isFile?a._readFile(e[t]):e[t].isDirectory&&a._traverseFileTree(e[t])})},_onDrop:function(e){var t,a,n,i=e.originalEvent.dataTransfer,o=this,r=i.files;if(g(e),o.options.directoryDrop&&i.items)for(t=i.items.length,o.droppedFolderCounter=0,o.droppedFolderFiles=[],a=0;a<t;a++)i.items[a].webkitGetAsEntry?(n=i.items[a].webkitGetAsEntry(),n.isDirectory?o._traverseFileTree(n,!0):n.isFile&&o.droppedFolderFiles.push(i.files[a])):o._proceedDroppedItems(r);else o._proceedDroppedItems(r)},_proceedDroppedItems:function(t){var a,n=this,o=l(i(t),n._isAsyncNonBatch());t.length>0&&!n.wrapper.hasClass("k-state-disabled")&&(!n.multiple&&o.length>1&&o.splice(1,o.length-1),p(o,n.options.validation),a=n.trigger(D,{files:o}),a||n._module.onSelect({target:e(".k-dropzone",n.wrapper)},o))},_filesContainValidationErrors:function(t){var a=!1;return e(t).each(function(e,t){if(t[L]&&t[L].length>0)return a=!0,!1}),a},_isAsyncNonBatch:function(){return this._async&&!this.options.async.batch||!1},_renderInitialFiles:function(e){var t,a,n=this,i=0;for(e=l(e,!0),i=0;i<e.length;i++)t=e[i],a=n._enqueueFile(t.name,{fileNames:[t]}),a.addClass("k-file-success").data("files",[e[i]]),n._supportsRemove()&&n._fileAction(a,M)},_prepareTemplateData:function(e,t){var a=t.fileNames,n={},i=0,o=0;for(o=0;o<a.length;o++)i+=a[o].size;return n.name=e,n.size=i,n.files=t.fileNames,n},_prepareDefaultSingleFileEntryTemplate:function(t){var a=this,n=t.fileNames[0],i=f(t.fileNames),o=n[L],r="";return r+=o&&o.length>0?"<li class='k-file k-file-invalid'><span class='k-progress'></span><span class='k-file-invalid-extension-wrapper'><span class='k-file-invalid-icon'>!</span><span class='k-file-state'></span></span><span class='k-file-name-size-wrapper'><span class='k-file-name k-file-name-invalid' title='"+n.name+"'>"+n.name+"</span><span class='k-file-validation-message'>"+a.localization[o[0]]+"</span></span>":"<li class='k-file'><span class='k-progress'></span><span class='k-file-extension-wrapper'><span class='k-file-extension'>"+n.extension.substring(1)+"</span><span class='k-file-state'></span></span><span class='k-file-name-size-wrapper'><span class='k-file-name' title='"+n.name+"'>"+n.name+"</span><span class='k-file-size'>"+i+"</span></span>",r+="<strong class='k-upload-status'></strong>",e(r)},_prepareDefaultMultipleFileEntriesTemplate:function(t){var a,n,i=this,o=t.fileNames,r=i._filesContainValidationErrors(o),s=f(o),l="";for(l+=r?"<li class='k-file k-file-invalid'><span class='k-progress'></span><span class='k-multiple-files-invalid-extension-wrapper'><span class='k-file-invalid-icon'>!</span>":"<li class='k-file'><span class='k-progress'></span><span class='k-multiple-files-extension-wrapper'>",l+="<span class='k-file-state'></span></span>",o.sort(function(e,t){return e[L]?-1:t[L]?1:0}),l+="<span class='k-file-name-size-wrapper'>",a=0;a<o.length;a++)n=o[a],l+=n[L]&&n[L].length>0?"<span class='k-file-name k-file-name-invalid' title='"+n.name+"'>"+n.name+"</span>":"<span class='k-file-name' title='"+n.name+"'>"+n.name+"</span>";return l+=r?"<span class='k-file-validation-message'>"+i.localization.invalidFiles+"</span>":"<span class='k-file-information'>Total: "+o.length+" files, "+s+"</span>",l+="</span><strong class='k-upload-status'></strong>",e(l)},_enqueueFile:function(t,a){var n,i,o,r,s=this,l=a.fileNames[0].uid,p=e(".k-upload-files",s.wrapper),d=s.options,u=d.template;return 0===p.length&&(p=e("<ul class='k-upload-files k-reset'></ul>").appendTo(s.wrapper),s.options.showFileList||p.hide(),s.wrapper.removeClass("k-upload-empty")),n=e(".k-file",p),u?(o=s._prepareTemplateData(t,a),u=U.template(u),i=e("<li class='k-file'>"+u(o)+"</li>"),i.find(".k-upload-action").addClass("k-button"),s.angular("compile",function(){return{elements:i,data:[o]}})):i=1===a.fileNames.length?s._prepareDefaultSingleFileEntryTemplate(a):s._prepareDefaultMultipleFileEntriesTemplate(a),i.attr(U.attr("uid"),l).appendTo(p).data(a),s._async||e(".k-progress",i).width("100%"),!s.multiple&&n.length>0&&(r={files:n.data("fileNames"),headers:{}},s.trigger(M,r)||s._module.onRemove({target:e(n,s.wrapper)},r)),i},_removeFileEntry:function(t){var a,n,i,o=this,r=t.closest(".k-upload-files");t.remove(),a=e(".k-file",r),n=e(".k-file-success, .k-file-error",r),i=e(".k-file-invalid",r),n.length!==a.length&&i.length!==a.length||this._hideUploadButton(),0===a.length?(r.remove(),o.wrapper.addClass("k-upload-empty"),o._hideHeaderUploadstatus()):o._updateHeaderUploadStatus()},_fileAction:function(e,t,a){var n,i={remove:"k-i-x",cancel:"k-i-cancel",retry:"k-i-retry",pause:"k-i-pause-sm"},o={remove:"k-i-close",cancel:"k-i-close",retry:"k-i-reload-sm",pause:"k-i-pause-sm"};i.hasOwnProperty(t)&&(a||this._clearFileAction(e),this.options.template?(n=e.find(".k-upload-action").first(),n.find(".k-icon").length?n.next(".k-upload-action").length&&n.next(".k-upload-action").addClass("k-button").append("<span class='k-icon "+o[t]+" "+i[t]+"' title='"+this.localization[t]+"'aria-label='"+this.localization[t]+"'></span>").show():n.addClass("k-button").append("<span class='k-icon "+o[t]+" "+i[t]+"' title='"+this.localization[t]+"'aria-label='"+this.localization[t]+"'></span>").show()):(a||e.find(".k-upload-status .k-upload-action").remove(),e.find(".k-upload-status").append(this._renderAction(i[t],this.localization[t],o[t]))))},_fileState:function(t,a){var n=this.localization,i={uploading:{text:n.statusUploading},uploaded:{text:n.statusUploaded},failed:{text:n.statusFailed}},o=i[a];o&&e("span.k-file-state",t).text(o.text)},_renderAction:function(t,a,n){return""!==t?e("<button type='button' class='k-button k-upload-action' aria-label='"+a+"'><span class='k-icon "+n+" "+t+"' title='"+a+"'></span></button>").on("focus",function(){e(this).addClass("k-state-focused")}).on("blur",function(){e(this).removeClass("k-state-focused")}):e("<button type='button' class='k-button'>"+a+"</button>")},_clearFileAction:function(t){e(".k-upload-action",t).empty().hide()},_onFileAction:function(t){var a,n,i,o,r,s,l=this;return l.wrapper.hasClass("k-state-disabled")||(a=e(t.target).closest(".k-upload-action"),n=a.find(".k-icon"),i=a.closest(".k-file"),o=i.data("fileNames"),r=l._filesContainValidationErrors(o),s={files:o,headers:{}},l._retryClicked=!1,n.hasClass("k-i-x")?l.trigger(M,s)||l._module.onRemove({target:e(i,l.wrapper)},s,!r):n.hasClass("k-i-cancel")?(l.trigger(T,s),l._module.onCancel({target:e(i,l.wrapper)}),l._checkAllComplete(),l._updateHeaderUploadStatus()):n.hasClass("k-i-pause-sm")?(l.trigger(B,s),l.pause(i),l._updateHeaderUploadStatus()):n.hasClass("k-i-play-sm")?(l.trigger(H,s),l.resume(i)):n.hasClass("k-i-retry")&&(e(".k-i-warning",i).remove(),e(".k-progress",i).finish().show(),l._module.onRetry({target:e(i,l.wrapper)}),l._retryClicked=!0)),!1},_onUploadSelected:function(){var e=this,t=e.wrapper;return t.hasClass("k-state-disabled")||this._module.onSaveSelected(),!1},_onClearSelected:function(){var e=this,t=e.wrapper,a={};return t.hasClass("k-state-disabled")||e.trigger(N,a)||e.clearAllFiles(),!1},_onFileProgress:function(t,a){var n,i;a>100&&(a=100),this.options.template?e(".k-progress",t.target).width(a+"%"):(n=e(".k-upload-pct",t.target),i=e(".k-i-warning",t.target),i.length?i.removeClass("k-i-warning").removeClass("k-icon").addClass("k-upload-pct"):0===n.length&&e(".k-upload-status",t.target).prepend("<span class='k-upload-pct'></span>"),100!==a?e(".k-upload-pct",t.target).text(a+"%"):e(".k-upload-pct",t.target).remove(),e(".k-progress",t.target).width(a+"%")),this.trigger(q,{files:F(t).data("fileNames"),percentComplete:a})},_onUploadSuccess:function(e,t,a){var n=this,i=F(e),o=n.trigger(R,{files:i.data("fileNames"),response:t,operation:"upload",XMLHttpRequest:a});o?n._setUploadErrorState(i):(n._fileState(i,"uploaded"),i.removeClass("k-file-progress").addClass("k-file-success"),n._updateHeaderUploadStatus(),n._supportsRemove()?n._fileAction(i,M):n._clearFileAction(i)),n._hideUploadProgress(i),n._checkAllComplete()},_onUploadError:function(e,t){var a=this,n=a._module,i=F(e),o=i.data("uid");a._setUploadErrorState(i),a.trigger(I,{operation:"upload",files:i.data("fileNames"),XMLHttpRequest:t}),z("Server response: "+t.responseText),a.options.async.chunkSize?n._decreasePosition&&n._decreasePosition(o):a._hideUploadProgress(i),a._checkAllComplete(),this.options.async.autoRetryAfter&&this._autoRetryAfter(i)},_autoRetryAfter:function(e){var t=this,a=this._module.retries;a&&(a[e.data("uid")]||(a[e.data("uid")]=1),a[e.data("uid")]<=this.options.async.maxAutoRetries&&(a[e.data("uid")]++,setTimeout(function(){t._module.performUpload(e)},this.options.async.autoRetryAfter)))},_setUploadErrorState:function(t){var a,n=this;n._fileState(t,"failed"),t.removeClass("k-file-progress").addClass("k-file-error"),n._updateUploadProgress(t),a=e(".k-upload-pct",t),a.length>0?(a.parent().find(".k-i-warning").length||a.removeClass("k-upload-pct").addClass("k-icon k-i-warning"),a.empty()):e(".k-upload-status",t).prepend("<span class='k-icon k-i-warning'></span>"),this._updateHeaderUploadStatus(),this._fileAction(t,"retry"),this._fileAction(t,M,!0),n._retryClicked&&t.find(".k-i-retry").parent().focus()},_updateUploadProgress:function(t){var a,n,i,o=this;o.options.async.chunkSize?(a=t.data("uid"),o._module.metaData&&(n=o._module.metaData[a],n&&(i=n.totalChunks?Math.round(n.chunkIndex/n.totalChunks*100):100,o._onFileProgress({target:e(t,o.wrapper)},i)))):e(".k-progress",t).width("100%")},_hideUploadProgress:function(t){e(".k-progress",t).delay(X).fadeOut(Z,function(){e(this).css("width","0%")})},_showUploadButton:function(){var t=this,a=e(".k-upload-selected",t.wrapper),n=e(".k-clear-selected",t.wrapper);0===a.length&&(a=t._renderAction("",this.localization.uploadSelectedFiles).addClass("k-upload-selected"),n=t._renderAction("",this.localization.clearSelectedFiles).addClass("k-clear-selected")),this.wrapper.append(n,a)},_hideUploadButton:function(){e(".k-upload-selected, .k-clear-selected",this.wrapper).remove()},_showHeaderUploadStatus:function(t){var a=this,n=a.localization,i=e(".k-dropzone",a.wrapper),o=e(".k-upload-status-total",a.wrapper);0!==o.length&&o.remove(),o='<strong class="k-upload-status k-upload-status-total"><span class="k-icon"></span></strong>',t?(o=e(o).append(n.headerStatusUploading),o.find(".k-icon").addClass(W.loading)):(o=e(o).append(n.headerStatusUploaded),o.find(".k-icon").addClass(W.warning)),i.length>0?i.append(o):e(".k-upload-button",a.wrapper).after(o)},_updateHeaderUploadStatus:function(){var t,a,n=this,i=e(".k-upload-status-total",this.wrapper),o=e(".k-file",n.wrapper).not(".k-file-success, .k-file-error, .k-file-invalid"),r=e(".k-file-invalid",n.wrapper),s=e(".k-file-error",n.wrapper),l=e(".k-file",n.wrapper).find(".k-i-play-sm");!l.length||l.length!==o.length&&n.options.async.concurrent?(0===o.length||r.length>0||s.length>0)&&(t=e(".k-file.k-file-error, .k-file.k-file-invalid",n.wrapper),i=e(".k-upload-status-total",n.wrapper),a=e(".k-icon",i).removeClass().addClass("k-icon").addClass(0!==t.length?W.warning:W.success),i.html(a).append(n.localization.headerStatusUploaded)):(a=e(".k-icon",i).removeClass().addClass("k-icon").addClass("k-i-pause-sm"),i.html(a).append(n.localization.headerStatusPaused))},_hideHeaderUploadstatus:function(){e(".k-upload-status-total",this.wrapper).remove()},_onParentFormSubmit:function(){var a,n=this,i=n.element;t!==this._module.onAbort&&this._module.onAbort(),i.value||(a=e(i),a.attr("disabled","disabled"),window.setTimeout(function(){a.removeAttr("disabled")},0))},_onParentFormReset:function(){e(".k-upload-files",this.wrapper).remove()},_supportsFormData:function(){return"undefined"!=typeof FormData},_supportsMultiple:function(){var e=this._userAgent().indexOf("Windows")>-1;return!(U.support.browser.opera||U.support.browser.safari&&e)},_supportsDrop:function(){var e=this._userAgent().toLowerCase(),t=/chrome/.test(e),a=!t&&/safari/.test(e),n=a&&/windows/.test(e);return!n&&this._supportsFormData()&&this.options.async.saveUrl},_userAgent:function(){return navigator.userAgent},_setupDropZone:function(){var t,a,n=this;e(".k-upload-button",n.wrapper).wrap("<div class='k-dropzone'></div>"),t=n._ns,a=e(".k-dropzone",n.wrapper).append(e("<em>"+n.localization.dropFilesHere+"</em>")).on("dragenter"+t,g).on("dragover"+t,function(e){e.preventDefault()}).on("drop"+t,e.proxy(n._onDrop,n)),_(a,t,function(){a.closest(".k-upload").hasClass("k-state-disabled")||a.addClass("k-dropzone-hovered")},function(){a.removeClass("k-dropzone-hovered")}),n._bindDocumentDragEventWrappers(a)},_setupCustomDropZone:function(){var t,a=this,n=e(a.options.dropZone);e(".k-upload-button",a.wrapper).wrap("<div class='k-dropzone'></div>").after(e("<em>"+a.localization.dropFilesHere+"</em>")),t=a._ns,n.on("dragenter"+t,g).on("dragover"+t,function(e){e.preventDefault()}).on("drop"+t,e.proxy(a._onDrop,a)),_(n,t,function(t){a.wrapper.hasClass("k-state-disabled")||(n.removeClass("k-dropzone-hovered"),e(t.target).addClass("k-dropzone-hovered"))},function(){n.removeClass("k-dropzone-hovered"),n.find(".k-dropzone-hovered").removeClass("k-dropzone-hovered")}),a._bindDocumentDragEventWrappers(n)},_bindDocumentDragEventWrappers:function(t){var a=this,n=a._ns;_(e(document),n,function(){a.wrapper.hasClass("k-state-disabled")||(t.addClass("k-dropzone-active"),t.closest(".k-upload").removeClass("k-upload-empty"))},function(){t.removeClass("k-dropzone-active"),0===e("li.k-file",t.closest(".k-upload")).length&&t.closest(".k-upload").addClass("k-upload-empty")})},_supportsRemove:function(){return!!this.options.async.removeUrl},_submitRemove:function(t,a,n,i){var o=this,r=o.options.async.removeField||"fileNames",s=e.extend(a.data,b());s[r]=t,jQuery.ajax({type:this.options.async.removeVerb,dataType:"json",dataFilter:v,url:this.options.async.removeUrl,traditional:!0,data:s,headers:a.headers,success:n,error:i,xhrFields:{withCredentials:this.options.async.withCredentials}})},_wrapInput:function(e){var t=this,a=t.options;return e.wrap("<div class='k-widget k-upload k-header'><div class='k-button k-upload-button' aria-label='"+this.localization.select+"'></div></div>"),a.async.saveUrl||e.closest(".k-upload").addClass("k-upload-sync"),e.closest(".k-upload").addClass("k-upload-empty"),e.closest(".k-button").append("<span>"+this.localization.select+"</span>"),e.closest(".k-upload")},_checkAllComplete:function(){0===e(".k-file.k-file-progress",this.wrapper).length&&this.trigger(P)},_inputFiles:function(e){return n(e)}}),G=function(e){this.name="syncUploadModule",this.element=e.wrapper,this.upload=e,this.element.closest("form").attr("enctype","multipart/form-data").attr("encoding","multipart/form-data")};G.prototype={onSelect:function(t,n){var i,o,r=this.upload,s=e(t.target),l=r._filesContainValidationErrors(n);r._addInput(s),i={fileNames:n},l?s.remove():i.relatedInput=s,o=r._enqueueFile(a(s),i),l&&r._hideUploadProgress(o),r._fileAction(o,M)},onRemove:function(e){var t=F(e),a=t.data("relatedInput");a&&a.remove(),this.upload._removeFileEntry(t)}},w=function(e){this.name="iframeUploadModule",this.element=e.wrapper,this.upload=e,this.iframes=[]},K._frameId=0,w.prototype={onSelect:function(t,a){var n=this.upload,i=e(t.target),o=n._filesContainValidationErrors(a),r=this.prepareUpload(i,a,o);n.options.async.autoUpload?o?(n._fileAction(r,M),n._showHeaderUploadStatus(!1)):this.performUpload(r):(n._fileAction(r,M),o?n._updateHeaderUploadStatus():n._showUploadButton()),o&&n._hideUploadProgress(r)},prepareUpload:function(t,n,i){var o,r,s,l,p=this.upload,d=e(p.element),u=p.options.async.saveField||t.attr("name");return p._addInput(t),t.attr("name",u),i?(t.remove(),r={fileNames:n}):(s=this.createFrame(p.name+"_"+K._frameId++),this.registerFrame(s),l=this.createForm(p.options.async.saveUrl,s.attr("name")).append(d),r={frame:s,relatedInput:d,fileNames:n}),o=p._enqueueFile(a(t),r),s&&s.data({form:l,file:o}),o},performUpload:function(t){var a,n,i,o={files:t.data("fileNames")},r=t.data("frame"),s=this.upload;if(s.trigger(A,o))s._removeFileEntry(r.data("file")),this.cleanupFrame(r),this.unregisterFrame(r);else{s._hideUploadButton(),s._showHeaderUploadStatus(!0),r.appendTo(document.body),a=r.data("form").attr("action",s.options.async.saveUrl).appendTo(document.body),o.data=e.extend({},o.data,b());for(n in o.data)i=a.find("input[name='"+n+"']"),0===i.length&&(i=e("<input>",{type:"hidden",name:n}).prependTo(a)),i.val(o.data[n]);s._fileAction(t,T),s._fileState(t,"uploading"),e(t).removeClass("k-file-error").addClass("k-file-progress"),r.one("load",e.proxy(this.onIframeLoad,this)),a[0].submit()}},onSaveSelected:function(){var t=this,a=t.upload;e(".k-file",this.element).each(function(){var n=e(this),i=y(n),o=a._filesContainValidationErrors(n.data("fileNames"));i||o||t.performUpload(n)})},onIframeLoad:function(t){var a,n=e(t.target);try{a=n.contents().text()}catch(i){a="Error trying to get server response: "+i}this.processResponse(n,a)},processResponse:function(t,a){var n=t.data("file"),i=this,o={responseText:a};k(a,function(a){e.extend(o,{statusText:"OK",status:"200"}),i.upload._onFileProgress({target:e(n,i.upload.wrapper)},100),i.upload._onUploadSuccess({target:e(n,i.upload.wrapper)},a,o),i.cleanupFrame(t),i.unregisterFrame(t)},function(){e.extend(o,{statusText:"error",status:"500"}),i.upload._onUploadError({target:e(n,i.upload.wrapper)},o)})},onCancel:function(t){var a=e(t.target).data("frame");this.stopFrameSubmit(a),this.cleanupFrame(a),this.unregisterFrame(a),this.upload._removeFileEntry(a.data("file"))},onRetry:function(e){var t=F(e);this.performUpload(t)},onRemove:function(e,t,a){var n=this,i=n.upload,o=F(e),r=o.data("frame");r?(n.unregisterFrame(r),i._removeFileEntry(o),n.cleanupFrame(r)):o.hasClass("k-file-success")?m(o,i,t,a):i._removeFileEntry(o)},onAbort:function(){var t=this.element,a=this;e.each(this.iframes,function(){e("input",this.data("form")).appendTo(t),a.stopFrameSubmit(this[0]),this.data("form").remove(),this.remove()}),this.iframes=[]},createFrame:function(t){return e("<iframe name='"+t+"' id='"+t+"' style='display:none;' />")},createForm:function(t,a){return e("<form enctype='multipart/form-data' method='POST' action='"+t+"' target='"+a+"'/>")},stopFrameSubmit:function(e){t!==e.stop?e.stop():e.document&&e.document.execCommand("Stop")},registerFrame:function(e){this.iframes.push(e)},unregisterFrame:function(t){this.iframes=e.grep(this.iframes,function(e){return e.attr("name")!=t.attr("name")})},cleanupFrame:function(e){var t=e.data("form");e.data("file").data("frame",null),setTimeout(function(){t.remove(),e.remove()},1)}},C=function(e){this.name="formDataUploadModule",this.element=e.wrapper,this.upload=e,this.position={},this.metaData={},this.cancelled={},this.resume={},this.paused={},this.retries={}},C.prototype={onSelect:function(t,a){var n,i,o=this.upload,r=this,s=e(t.target),l=this.prepareUpload(s,a);e.each(l,function(t){n=o._filesContainValidationErrors(e(this.data("fileNames"))),o.options.async.autoUpload?n?(o._fileAction(this,M),o._showHeaderUploadStatus(!1)):o.options.async.chunkSize?(r.prepareChunk(this),i=this.prev(),(o.options.async.concurrent||0===t&&!i.length||0===t&&i.hasClass("k-file-success"))&&r.performUpload(this)):r.performUpload(this):(o._fileAction(this,M),n?o._updateHeaderUploadStatus():(o._showUploadButton(),this.addClass("k-toupload"))),n&&o._hideUploadProgress(this)})},prepareUpload:function(t,a){var n=this.enqueueFiles(a);return t.is("input")&&(e.each(n,function(){e(this).data("relatedInput",t)}),t.data("relatedFileEntries",n),this.upload._addInput(t)),n},enqueueFiles:function(t){var a,n,i,o,r=this.upload,s=t.length,l=[];if(r.options.async.batch===!0)a=e.map(t,function(e){return e.name}).join(", "),(r.directory||r.options.directoryDrop)&&e(t).each(function(){(this.rawFile.webkitRelativePath||this.rawFile.relativePath)&&(this.name=this.rawFile.webkitRelativePath||this.rawFile.relativePath)}),o=r._enqueueFile(a,{fileNames:t}),o.data("files",t),l.push(o);else for(n=0;n<s;n++)i=t[n],a=i.name,(r.directory||r.options.directoryDrop)&&(i.rawFile.webkitRelativePath||i.rawFile.relativePath)&&(i.name=i.rawFile.webkitRelativePath||i.rawFile.relativePath),o=r._enqueueFile(a,{fileNames:[i]}),o.data("files",[i]),l.push(o);return l},performUpload:function(t){var a,n,i=this.upload,o=this.createFormData(),r=this.createXHR(),s={files:t.data("fileNames"),XMLHttpRequest:r};if(i.trigger(A,s))this.removeFileEntry(t);else{if(0===t.find(".k-i-cancel").length&&(i.options.async.chunkSize&&i._fileAction(t,B),i._fileAction(t,T,i.options.async.chunkSize)),i.wrapper.find(".k-toupload").length||i._hideUploadButton(),i._showHeaderUploadStatus(!0),s.formData)o=s.formData;else{s.data=e.extend({},s.data,b());for(n in s.data)o.append(n,s.data[n]);a=t.data("files"),a&&this.populateFormData(o,a)}i._fileState(t,"uploading"),e(t).removeClass("k-file-error").addClass("k-file-progress"),i.options.async.useArrayBuffer&&window.FileReader?this._readFile(i.options.async.saveUrl,o,t,r):this.postFormData(i.options.async.saveUrl,o,t,r)}},_readFile:function(a,n,i,o){var r=this,s=r.upload,l=i.data("files")[0],p=new FileReader;p.onload=function(p){try{r.fileArrayBuffer=r.fileArrayBuffer?r._appendBuffer(r.fileArrayBuffer,p.target.result):p.target.result}catch(d){return s._onUploadError({target:e(i,s.wrapper)},o),t}r.position[l.uid]>l.size?(r.postFormData(s.options.async.saveUrl,r.fileArrayBuffer,i,o),r.fileArrayBuffer=null):r._readFile(a,n,i,o)},p.onerror=function(){s._onUploadError({target:e(i,s.wrapper)},o)},p.readAsArrayBuffer(r._getCurrentChunk(l.rawFile,l.uid))},_appendBuffer:function(e,t){var a=new Uint8Array(e.byteLength+t.byteLength);return a.set(new Uint8Array(e),0),a.set(new Uint8Array(t),e.byteLength),a.buffer},onSaveSelected:function(){var t=this,a=t.upload;e(".k-toupload",this.element).filter(function(){var t=e(this),n=y(t),i=a._filesContainValidationErrors(t.data("fileNames"));return!n&&!i}).each(function(n){var i=e(this),o=i.prev();i.removeClass("k-toupload"),a.options.async.chunkSize?(t.prepareChunk(i),(a.options.async.concurrent||0===n&&!o.length||0===n&&o.hasClass("k-file-success")||o.hasClass("k-file-error"))&&t.performUpload(i)):t.performUpload(i)})},onCancel:function(e){var t=F(e);this.upload.options.async.chunkSize&&(this.cancelled[t.data("uid")]=!0),this.stopUploadRequest(t),this.removeFileEntry(t)},onPause:function(e){var t=F(e),a=t.data("uid"),n=this.upload.options.async;n.chunkSize&&(this.retries[a]=n.maxAutoRetries+1,this.paused[a]=!0,this.resume[a]=!1)},onResume:function(e){var t=F(e),a=t.data("uid");this.upload.options.async.chunkSize&&(delete this.paused[a],this.resume[a]=!0,this.retries[t.data("uid")]=1,this._increaseChunkIndex(a),this.performUpload(t));
},onRetry:function(e){var t=F(e),a=this.upload.options.async;a.chunkSize&&(this.retries[t.data("uid")]=a.maxAutoRetries+1,delete this.paused[t.data("uid")]),this.performUpload(t)},onRemove:function(e,t,a){var n=this,i=n.upload,o=F(e),r=this.upload.options.async;r.chunkSize&&(this.retries[o.data("uid")]=r.maxAutoRetries+1),o.hasClass("k-file-success")?m(o,i,t,a):n.removeFileEntry(o)},createXHR:function(){return new XMLHttpRequest},postFormData:function(e,t,a,n){var i,o=this;a.data("request",n),n.addEventListener("load",function(e){o.onRequestSuccess.call(o,e,a)},!1),n.addEventListener(I,function(e){o.onRequestError.call(o,e,a)},!1),n.upload.addEventListener("progress",function(e){o.onRequestProgress.call(o,e,a)},!1),n.open("POST",e,!0),n.withCredentials=this.upload.options.async.withCredentials,i=this.upload.options.async.accept,i&&n.setRequestHeader("Accept",i),n.send(t)},createFormData:function(){return new FormData},populateFormData:function(e,t){var a,n,i,o,r=t.length,s=this.upload;if(s.options.async.chunkSize)i=t[0].uid,a=this._getCurrentChunk(t[0].rawFile,i),e.append(s.options.async.saveField||s.name,a),o=JSON.stringify(this.metaData[i]),e.append("metadata",o);else for(n=0;n<r;n++)e.append(s.options.async.saveField||s.name,t[n].rawFile);return e},onRequestSuccess:function(t,a){function n(){r.upload._onUploadError({target:e(a,r.upload.wrapper)},o)}function i(t){var n=r.upload.options.async.batch,i=r.upload.options.async.chunkSize,s=r.upload.options.async.concurrent,l=t.fileUid;r.paused[l]||r.cancelled[l]||(delete r.retries[l],!i||n||t.uploaded?!i||n||s||!a.next().length||a.next().hasClass("k-toupload")?(r.upload._onFileProgress({target:e(a,r.upload.wrapper)},100),r.upload._onUploadSuccess({target:e(a,r.upload.wrapper)},t,o),r.cleanupFileEntry(a)):(r.upload._onFileProgress({target:e(a,r.upload.wrapper)},100),r._resetChunkIndex(l),r.upload._onUploadSuccess({target:e(a,r.upload.wrapper)},t,o),r.performUpload(a.next())):(r._increaseChunkIndex(l),r.performUpload(a)))}var o=t.target,r=this;o.status>=200&&o.status<=299?k(o.responseText,i,n):n()},onRequestError:function(t,a){var n=t.target;this.upload._onUploadError({target:e(a,this.upload.wrapper)},n)},cleanupFileEntry:function(t){var a=t.data("relatedInput"),n=!0;a&&(e.each(a.data("relatedFileEntries")||[],function(){this.parent().length>0&&this[0]!=t[0]&&(n=n&&this.hasClass("k-file-success"))}),n&&a.remove())},removeFileEntry:function(e){var t=this.upload.options.async.chunkSize,a=this.upload.options.async.concurrent,n=this.upload.wrapper.find(".k-upload-selected").length>0;this.cleanupFileEntry(e),!t||a||n||e.next().length&&this.performUpload(e.next()),this.upload._removeFileEntry(e)},onRequestProgress:function(t,a){var n,i=Math.round(100*t.loaded/t.total),o=a.data("uid");this.upload.options.async.chunkSize&&(n=this.metaData[o],i=n&&n.totalChunks?Math.round(n.chunkIndex/n.totalChunks*100):100),this.upload._onFileProgress({target:e(a,this.upload.wrapper)},i)},stopUploadRequest:function(e){e.data("request").abort()},prepareChunk:function(e){var t=e.data("files")[0],a=t.rawFile,n=t.uid,i=this.upload.options.async.chunkSize;this.position[n]=0,this.metaData[n]={chunkIndex:0,contentType:a.type,fileName:a.name,relativePath:t.name,totalFileSize:a.size,totalChunks:Math.ceil(a.size/i),uploadUid:n}},_decreaseChunkIndex:function(e){this.metaData[e].chunkIndex--},_increaseChunkIndex:function(e){this.metaData[e].chunkIndex++},_resetChunkIndex:function(e){this.metaData[e].chunkIndex=0},_decreasePosition:function(e){this.position[e]-=this.upload.options.async.chunkSize},_getCurrentChunk:function(e,t){var a,n=this.position[t],i=this.upload.options.async,o=i.chunkSize||i.bufferChunkSize;return this.position[t]||(this.position[t]=0),this.position[t]+=o,(a=this._getChunker(e))?e[a](n,this.position[t]):e},_getChunker:function(e){return e.slice?"slice":e.mozSlice?"mozSlice":e.webkitSlice?"webkitSlice":null}},U.ui.plugin(K)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,a){(a||t)()});
//# sourceMappingURL=kendo.upload.min.js.map
