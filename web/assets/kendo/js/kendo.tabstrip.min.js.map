{"version": 3, "sources": ["kendo.tabstrip.js"], "names": ["f", "define", "$", "undefined", "updateTabClasses", "tabs", "children", "IMG", "addClass", "IMAGE", "LINK", "filter", "DEFAULTSTATE", "DISABLEDSTATE", "attr", "removeAttr", "parent", "ACTIVESTATE", "TABONTOP", "each", "item", "this", "length", "contents", "nodeName", "match", "excludedNodesRegExp", "nodeType", "trim", "nodeValue", "wrapAll", "updateFirstLast", "tabGroup", "removeClass", "FIRST", "LAST", "scrollButtonHtml", "buttonClass", "iconClass", "kendo", "window", "ui", "keys", "map", "extend", "isFunction", "template", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "Widget", "NS", "HREF", "PREV", "NEXT", "SHOW", "CLICK", "ERROR", "EMPTY", "SELECT", "ACTIVATE", "CONTENT", "CONTENTURL", "MOUSEENTER", "MOUSELEAVE", "CONTENTLOAD", "FOCUSEDSTATE", "HOVERSTATE", "NAVIGATABLEITEMS", "KEYBOARDNAVIGATABLEITEMS", "HOVERABLEITEMS", "DEFAULTDISTANCE", "templates", "content", "itemWrapper", "image", "sprite", "empty", "rendering", "wrapperCssClass", "group", "result", "index", "enabled", "textAttributes", "url", "text", "encoded", "htmlEncode", "tag", "contentAttributes", "active", "contentUrl", "TabStrip", "init", "element", "options", "value", "selectedItems", "that", "fn", "call", "_animations", "_contentUrls", "contentUrls", "_wrapper", "_isRtl", "support", "isRtl", "wrapper", "_tabindex", "_updateClasses", "_dataSource", "dataSource", "fetch", "_tabPosition", "_scrollable", "find", "data", "on", "_toggleHover", "proxy", "_active", "_current", "_keyDownProxy", "_keydown", "navigatable", "e", "msie", "wr", "document", "activeElement", "browser", "setActive", "j", "focus", "_click", "currentTarget", "preventDefault", "contentHolder", "childNodes", "activateTab", "eq", "id", "_ariaId", "notify", "_endItem", "action", "_getItem", "_item", "endItem", "hasClass", "_focused", "candidate", "focused", "key", "keyCode", "current", "rtl", "isHorizontal", "test", "tabPosition", "target", "DOWN", "UP", "RIGHT", "LEFT", "ENTER", "SPACEBAR", "HOME", "END", "_refresh<PERSON><PERSON><PERSON>", "unbind", "refresh", "DataSource", "create", "bind", "setDataSource", "animation", "open", "effects", "close", "idx", "tab", "getter", "dataEncodedField", "dataTextField", "dataContentField", "dataContentUrlField", "dataImageUrlField", "dataUrlField", "dataSpriteCssClass", "view", "items", "imageUrl", "spriteCssClass", "insertBefore", "append", "remove", "indexOf", "field", "get", "trigger", "select", "setOptions", "off", "events", "name", "duration", "collapsible", "scrollable", "distance", "destroy", "scrollWrap", "_scrollableModeActive", "_scrollPrevButton", "_scrollNextButton", "unwrap", "arguments", "isNaN", "contentElement", "enable", "state", "_toggleDisabled", "disable", "reload", "ajaxRequest", "inserted", "_create", "before", "angular", "elements", "_updateContentElements", "resize", "_appendUrlItem", "push", "_moveUrlItem", "from", "to", "splice", "_removeUrlItem", "referenceTab", "is", "next", "reference<PERSON><PERSON>nt", "fromIndex", "newTabsCreated", "insertAfter", "prev", "after", "type", "ObservableArray", "toJSON", "isPlainObject", "isArray", "renderItem", "renderContent", "parentNode", "className", "getAttribute", "add", "toggleClass", "activeItem", "activeTab", "appendTo", "contentElements", "css", "display", "_elementId", "tabStripID", "elementId", "wrapperId", "guid", "isInitialUpdate", "setAttribute", "currentC<PERSON>nt", "prependTo", "contentAnimators", "tabsHeight", "parseInt", "kineticScrollNeeded", "mobile", "<PERSON><PERSON><PERSON>", "touchScroller", "_setContentElementsDimensions", "contentDivs", "activeDiv", "marginStyleProperty", "margin", "minHeight", "Math", "ceil", "height", "setTimeout", "_resize", "_sizeScrollWrap", "h", "floor", "prevent", "isAnchor", "link", "href", "collapse", "neighbours", "oldFocusedTab", "closest", "_scrollTabsToItem", "char<PERSON>t", "deactivateTab", "wrapperOffsetWidth", "tabGroupScrollWidth", "scrollPrevButton", "scrollNextButton", "mouseDown", "mouseUp", "isRtlScrollDirection", "_scrollableAllowed", "offsetWidth", "scrollWidth", "_nowScrollingTabs", "mobileOS", "edge", "marginLeft", "marginRight", "_scrollTabsByDelta", "_toggleScrollButtons", "itemPosition", "currentScrollOffset", "scrollLeft", "itemWidth", "itemOffset", "position", "left", "first", "tabGroupWidth", "tabGroupPadding", "parseFloat", "finish", "animate", "delta", "scrLeft", "j<PERSON><PERSON><PERSON>", "fx", "ul", "toggle", "animationSettings", "hasCloseAnimation", "reverse", "hide", "size", "kendoAddClass", "kendoRemoveClass", "kendoStop", "kendoAnimate", "oldTab", "itemIndex", "isAnimationEnabled", "visibleContents", "isAjaxContent", "showContentElement", "showContent", "inRequest", "xhr", "abort", "complete", "opacity", "i", "len", "scrollContainer", "touch", "halfWidth", "fakeProgress", "statusIcon", "endState", "oldProgressAnimation", "ajaxOptions", "ajaxSettings", "width", "version", "cache", "dataType", "request", "event", "progressUpload", "progress", "upload", "addEventListener", "evt", "noProgress", "XMLHttpRequest", "lengthComputable", "percent", "loaded", "total", "stop", "error", "status", "stopProgress", "clearInterval", "style", "cssText", "statusText", "success", "console", "setInterval", "min", "html", "message", "ajax", "tabStrip", "plugin", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,kBAAmB,cAAeD,IAC3C,WA4kCE,MA9jCC,UAAUE,EAAGC,GA2CV,QAASC,GAAiBC,GACtBA,EAAKC,SAASC,GAAKC,SAASC,GAC5BJ,EAAKC,SAAS,KAAKE,SAASE,GAAMJ,SAASC,GAAKC,SAASC,GACzDJ,EAAKM,OAAO,mDAAmDH,SAASI,GACxEP,EAAKM,OAAO,gBAAgBH,SAASK,GAAeC,KAAK,gBAAiB,QAAQC,WAAW,YAC7FV,EAAKM,OAAO,0BAA0BL,SAAS,KAAKK,OAAO,UAAUK,SAASR,SAASS,EAAc,IAAMC,GAC3Gb,EAAKS,KAAK,OAAQ,OAClBT,EAAKM,OAAO,IAAMM,GAAaH,KAAK,iBAAiB,GACrDT,EAAKc,KAAK,WACN,GAAIC,GAAOlB,EAAEmB,KACRD,GAAKd,SAAS,IAAMI,GAAMY,QAC3BF,EAAKG,WAAWZ,OAAO,WACnB,QAAQU,KAAKG,SAASC,MAAMC,IAA2C,GAAjBL,KAAKM,WAAkBC,EAAKP,KAAKQ,cACxFC,QAAQ,kCAAuCpB,EAAO,SAIrE,QAASqB,GAAgBC,GACrB,GAAI3B,GAAO2B,EAAS1B,SAAS,UAC7BD,GAAKM,OAAO,8BAA8BsB,YAAYC,GACtD7B,EAAKM,OAAO,4BAA4BsB,YAAYE,GACpD9B,EAAKM,OAAO,gBAAgBH,SAAS0B,GACrC7B,EAAKM,OAAO,eAAeH,SAAS2B,GAExC,QAASC,GAAiBC,EAAaC,GACnC,MAAO,yDAA4DD,EAAc,2CAAiDC,EAAY,mBApErJ,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAAOH,EAAMG,KAAMC,EAAMzC,EAAEyC,IAAKxB,EAAOjB,EAAEiB,KAAMS,EAAO1B,EAAE0B,KAAMgB,EAAS1C,EAAE0C,OAAQC,EAAaN,EAAMM,WAAYC,EAAWP,EAAMO,SAAUC,EAAaR,EAAMS,YAAaC,EAAcV,EAAMW,aAAcC,EAASV,EAAGU,OAAQzB,EAAsB,aAAc0B,EAAK,iBAAkB7C,EAAM,MAAO8C,EAAO,OAAQC,EAAO,OAAQC,EAAO,OAAQC,EAAO,OAAQ9C,EAAO,SAAUyB,EAAO,SAAUsB,EAAQ,QAASC,EAAQ,QAASC,EAAQ,SAAUlD,EAAQ,UAAWyB,EAAQ,UAAW0B,EAAS,SAAUC,EAAW,WAAYC,EAAU,YAAaC,EAAa,aAAcC,EAAa,aAAcC,EAAa,aAAcC,EAAc,cAAerD,EAAgB,mBAAoBD,EAAe,kBAAmBK,EAAc,iBAAkBkD,EAAe,kBAAmBC,EAAa,gBAAiBlD,EAAW,eAAgBmD,EAAmB,gBAAkBxD,EAAgB,IAAKyD,EAA2B,UAAWC,EAAiB,uBAAyBF,EAAmB,SAAWpD,EAAc,IAAKuD,EAAkB,IAAKC,GACpkCC,QAAS5B,EAAS,+FAClB6B,YAAa7B,EAAS,kJACtB1B,KAAM0B,EAAS,uIACf8B,MAAO9B,EAAS,sDAChB+B,OAAQ/B,EAAS,sDACjBgC,MAAOhC,EAAS,KACjBiC,GACCC,gBAAiB,SAAUC,EAAO7D,GAC9B,GAAI8D,GAAS,SAAUC,EAAQ/D,EAAK+D,KAYpC,OAVID,IADA9D,EAAKgE,WAAY,EACP,oBAEA,mBAEA,IAAVD,IACAD,GAAU,YAEVC,GAASF,EAAM3D,OAAS,IACxB4D,GAAU,WAEPA,GAEXG,eAAgB,SAAUjE,GACtB,MAAOA,GAAKkE,IAAM,UAAalE,EAAKkE,IAAM,IAAO,IAErDC,KAAM,SAAUnE,GACZ,MAAOA,GAAKoE,WAAY,EAAQpE,EAAKmE,KAAOhD,EAAMkD,WAAWrE,EAAKmE,OAEtEG,IAAK,SAAUtE,GACX,MAAOA,GAAKkE,IAAM,IAAM,QAE5BK,kBAAmB,SAAUjB,GACzB,MAAOA,GAAQkB,UAAW,EAAO,iEAAyE,IAE9GlB,QAAS,SAAUtD,GACf,MAAOA,GAAKsD,QAAUtD,EAAKsD,QAAUtD,EAAKyE,WAAa,GAAK,UAEhEA,WAAY,SAAUzE,GAClB,MAAOA,GAAKyE,WAAatD,EAAMzB,KAAK,eAAiB,KAAOM,EAAKyE,WAAa,IAAM,KA8B5FC,EAAW3C,EAAOP,QAClBmD,KAAM,SAAUC,EAASC,GAAnB,GACeC,GAqDbC,EAA6DzB,EArD7D0B,EAAO/E,IACX8B,GAAOkD,GAAGN,KAAKO,KAAKF,EAAMJ,EAASC,GACnCG,EAAKG,YAAYH,EAAKH,SACtBA,EAAUG,EAAKH,QACfG,EAAKI,aAAeP,EAAQQ,gBAC5BL,EAAKM,WACLN,EAAKO,OAASpE,EAAMqE,QAAQC,MAAMT,EAAKU,SACvCV,EAAKW,YACLX,EAAKY,iBACLZ,EAAKa,cACDhB,EAAQiB,YACRd,EAAKc,WAAWC,QAEpBf,EAAKgB,eACLhB,EAAKiB,cACDjB,EAAKI,aAAalF,OAClB8E,EAAKU,QAAQQ,KAAK,+BAA+BnG,KAAK,SAAUgE,EAAO/D,GACnE,GAAIkE,GAAMc,EAAKI,aAAarB,EACT,iBAARG,IACPpF,EAAEkB,GAAMkG,KAAK,KAAO5G,GAAM6G,KAAKxD,EAAYuB,KAInDc,EAAKI,aAAalF,OAAS8E,EAAKpE,SAASsF,KAAK,aAAahG,OAE/D8E,EAAKU,QAAQU,GAAGxD,EAAaZ,EAAK,IAAMa,EAAab,EAAImB,EAAgB6B,EAAKqB,cAAcD,GAAG,QAAUpE,EAAIlD,EAAEwH,MAAMtB,EAAKuB,QAASvB,IAAOoB,GAAG,OAASpE,EAAI,WACtJgD,EAAKwB,SAAS,QAElBxB,EAAKyB,cAAgB3H,EAAEwH,MAAMtB,EAAK0B,SAAU1B,GACxCH,EAAQ8B,aACR3B,EAAKU,QAAQU,GAAG,UAAYpE,EAAIgD,EAAKyB,eAErCzB,EAAKH,QAAQC,QACbA,EAAQE,EAAKH,QAAQC,OAEzBE,EAAKU,QAAQxG,SAAS,qBAAqBkH,GAAG/D,EAAQL,EAAI,6BAA6B,GAAOoE,GAAG/D,EAAQL,EAAI,MAAQiB,EAAkB,SAAU2D,GAAV,GAG3HC,GAFJC,EAAK9B,EAAKU,QAAQ,EACtB,IAAIoB,IAAOC,SAASC,cAEhB,GADIH,EAAO1F,EAAMqE,QAAQyB,QAAQJ,KAE7B,IACIC,EAAGI,YACL,MAAOC,GACLL,EAAGM,YAGPN,GAAGM,OAGPpC,GAAKqC,OAAOvI,EAAE8H,EAAEU,iBAChBV,EAAEW,mBAGNxC,EAAgBC,EAAKpE,SAAS1B,SAAS,MAAQW,GAAcyD,EAAU0B,EAAKwC,cAAczC,EAAchB,SACxGgB,EAAc,IAAMzB,EAAQpD,OAAS,GAAsC,IAAjCoD,EAAQ,GAAGmE,WAAWvH,QAChE8E,EAAK0C,YAAY3C,EAAc4C,GAAG,IAEtC3C,EAAKJ,QAAQlF,KAAK,OAAQ,WACtBsF,EAAKJ,QAAQ,GAAGgD,KAChB5C,EAAK6C,QAAU7C,EAAKJ,QAAQ,GAAGgD,GAAK,cAExC5C,EAAKF,MAAMA,GACX3D,EAAM2G,OAAO9C,IAEjBuB,QAAS,WACL,GAAIvG,GAAOC,KAAKW,SAAS1B,WAAWK,OAAO,IAAMM,EACjDG,GAAOA,EAAK,GAAKA,EAAOC,KAAK8H,SAAS,SAClC/H,EAAK,IACLC,KAAKuG,SAASxG,IAGtB+H,SAAU,SAAUC,GAChB,MAAO/H,MAAKW,SAAS1B,SAAS+D,GAAkB+E,MAEpDC,SAAU,SAAUD,GAChB,MAAO/H,MAAKW,SAAS1B,SAASgE,GAA0B8E,MAE5DE,MAAO,SAAUlI,EAAMgI,GACnB,GAAIG,EAMJ,OAJIA,GADAH,IAAW9F,EACD,OAEA,QAETlC,GAGLA,EAAOA,EAAKgI,KACPhI,EAAK,KACNA,EAAOC,KAAKW,SAAS1B,SAASgE,GAA0BiF,MAExDnI,EAAKoI,SAAS3I,IACdO,EAAKZ,SAAS2D,IAEd/C,EAAKoI,SAAS3I,IAAkBO,EAAKoI,SAASvI,MAC9CI,KAAKoI,SAAWrI,GAEbA,GAZIC,KAAK8H,SAASI,IAc7B3B,SAAU,SAAU8B,GAChB,GAAItD,GAAO/E,KAAMsI,EAAUvD,EAAKqD,SAAUT,EAAK5C,EAAK6C,OACpD,OAAIS,KAAcvJ,EACPwJ,GAEPA,IACAvD,EAAKpE,SAAS1B,SAAS,IAAM0I,GAAIjI,WAAW,MAC5C4I,EAAQ1H,YAAYkC,IAEpBuF,IACKA,EAAUF,SAASvI,IACpByI,EAAUlJ,SAAS2D,GAEvBiC,EAAKJ,QAAQjF,WAAW,yBACxBiI,EAAKU,EAAU,GAAGV,IAAMA,EACpBA,IACAU,EAAU5I,KAAK,KAAMkI,GACrB5C,EAAKJ,QAAQlF,KAAK,wBAAyBkI,KAGnD5C,EAAKqD,SAAWC,EAfhB,IAiBJ5B,SAAU,SAAUE,GAChB,GAA4IoB,GAAxIhD,EAAO/E,KAAMuI,EAAM5B,EAAE6B,QAASC,EAAU1D,EAAKwB,WAAYmC,EAAM3D,EAAKO,OAAQqD,EAAe,aAAaC,KAAK7D,EAAKH,QAAQiE,YAC9H,IAAIlC,EAAEmC,QAAUnC,EAAEU,cAAlB,CAGA,GAAIkB,IAAQlH,EAAK0H,MAASJ,EAEnB,GAAIJ,IAAQlH,EAAK2H,IAAOL,EAExB,GAAIJ,IAAQlH,EAAK4H,OAASN,EAC7BZ,EAASW,EAAMzG,EAAOC,MACnB,IAAIqG,IAAQlH,EAAK6H,MAAQP,EAC5BZ,EAASW,EAAMxG,EAAOD,MACnB,IAAIsG,GAAOlH,EAAK8H,OAASZ,GAAOlH,EAAK+H,SACxCrE,EAAKqC,OAAOqB,GACZ9B,EAAEW,qBACC,CAAA,GAAIiB,GAAOlH,EAAKgI,KAGnB,MAFAtE,GAAKqC,OAAOrC,EAAKiD,SAAS,UAC1BrB,EAAEW,iBACF,CACG,IAAIiB,GAAOlH,EAAKiI,IAGnB,MAFAvE,GAAKqC,OAAOrC,EAAKiD,SAAS,SAC1BrB,EAAEW,iBACF,MAfAS,GAAS9F,MAFT8F,GAAS7F,CAmBT6F,KACAhD,EAAKqC,OAAOrC,EAAKkD,MAAMQ,EAASV,IAChCpB,EAAEW,oBAGV1B,YAAa,WACT,GAAIb,GAAO/E,IACP+E,GAAKc,YAAcd,EAAKwE,gBACxBxE,EAAKc,WAAW2D,OAAO,SAAUzE,EAAKwE,iBAEtCxE,EAAKwE,gBAAkB1K,EAAEwH,MAAMtB,EAAK0E,QAAS1E,GAEjDA,EAAKc,WAAa3E,EAAMgF,KAAKwD,WAAWC,OAAO5E,EAAKH,QAAQiB,YAAY+D,KAAK,SAAU7E,EAAKwE,kBAEhGM,cAAe,SAAUhE,GACrB,GAAId,GAAO/E,IACX+E,GAAKH,QAAQiB,WAAaA,EAC1Bd,EAAKa,cACLb,EAAKc,WAAWC,SAEpBZ,YAAa,SAAUN,GACfA,GAAW,aAAeA,KAAYA,EAAQkF,YAC9ClF,EAAQkF,WACJC,MAAQC,YACRC,OAASD,eAIrBP,QAAS,SAAU9C,GACf,GAA+XuD,GAAgBC,EAAKpC,EAAuC9H,EAAvb8E,EAAO/E,KAAM4E,EAAUG,EAAKH,QAAST,EAAUjD,EAAMkJ,OAAOxF,EAAQyF,kBAAmBnG,EAAOhD,EAAMkJ,OAAOxF,EAAQ0F,eAAgBjH,EAAUnC,EAAMkJ,OAAOxF,EAAQ2F,kBAAmB/F,EAAatD,EAAMkJ,OAAOxF,EAAQ4F,qBAAsBjH,EAAQrC,EAAMkJ,OAAOxF,EAAQ6F,mBAAoBxG,EAAM/C,EAAMkJ,OAAOxF,EAAQ8F,cAAelH,EAAStC,EAAMkJ,OAAOxF,EAAQ+F,oBAA0B3L,KAAwB4L,EAAO7F,EAAKc,WAAW+E,MAMnb,KALAjE,EAAIA,MACJoB,EAASpB,EAAEoB,OACPA,IACA6C,EAAOjE,EAAEkE,OAERX,EAAM,EAAGjK,EAAS2K,EAAK3K,OAAQiK,EAAMjK,EAAQiK,IAC9CC,GAAQjG,KAAMA,EAAK0G,EAAKV,KACpBtF,EAAQyF,mBACRF,EAAIhG,QAAUA,EAAQyG,EAAKV,KAE3BtF,EAAQ2F,mBACRJ,EAAI9G,QAAUA,EAAQuH,EAAKV,KAE3BtF,EAAQ4F,sBACRL,EAAI3F,WAAaA,EAAWoG,EAAKV,KAEjCtF,EAAQ8F,eACRP,EAAIlG,IAAMA,EAAI2G,EAAKV,KAEnBtF,EAAQ6F,oBACRN,EAAIW,SAAWvH,EAAMqH,EAAKV,KAE1BtF,EAAQ+F,qBACRR,EAAIY,eAAiBvH,EAAOoH,EAAKV,KAErClL,EAAKkL,GAAOC,CAEhB,IAAgB,OAAZxD,EAAEoB,OACEpB,EAAE7C,MAAQiB,EAAKpE,SAAS1B,WAAWgB,OACnC8E,EAAKiG,aAAahM,EAAM+F,EAAKpE,SAAS1B,WAAWyI,GAAGf,EAAE7C,QAEtDiB,EAAKkG,OAAOjM,OAEb,IAAgB,UAAZ2H,EAAEoB,OACT,IAAKmC,EAAM,EAAGA,EAAMU,EAAK3K,OAAQiK,IAC7BnF,EAAKmG,OAAOvE,EAAE7C,WAEC,cAAZ6C,EAAEoB,QACTmC,EAAMnF,EAAKc,WAAW+E,OAAOO,QAAQP,EAAK,IACtCjE,EAAEyE,QAAUxG,EAAQ0F,eACpBvF,EAAKpE,SAAS1B,WAAWyI,GAAGwC,GAAKjE,KAAK,WAAW/B,KAAK0G,EAAK,GAAGS,IAAI1E,EAAEyE,QAEpEzE,EAAEyE,QAAUxG,EAAQ8F,eACpB3F,EAAKI,aAAa+E,GAAOU,EAAK,GAAGS,IAAI1E,EAAEyE,UAG3CrG,EAAKuG,QAAQ,eACbvG,EAAKmG,OAAO,MACZnG,EAAKI,gBACLJ,EAAKkG,OAAOjM,GACZ+F,EAAKuG,QAAQ,eAGrBzG,MAAO,SAAUA,GACb,GAAIE,GAAO/E,IACX,OAAI6E,KAAU/F,EASHiG,EAAKwG,SAASrH,QARjBW,GAASE,EAAKF,SACdE,EAAKpE,SAAS1B,WAAWa,KAAK,WACtBjB,EAAE0B,KAAK1B,EAAEmB,MAAMkE,SAAWW,GAC1BE,EAAKwG,OAAOvL,QAHxB,IAWR6K,MAAO,WACH,MAAO7K,MAAKW,SAAS,GAAG1B,UAE5BuM,WAAY,SAAU5G,GAClB,GAAIG,GAAO/E,KAAM8J,EAAY/E,EAAKH,QAAQkF,SAC1C/E,GAAKG,YAAYN,GACbA,EAAQQ,cACRL,EAAKI,aAAeP,EAAQQ,aAEhCR,EAAQkF,UAAYvI,GAAO,EAAMuI,EAAWlF,EAAQkF,WAChDlF,EAAQ8B,YACR3B,EAAKU,QAAQU,GAAG,UAAYpE,EAAIgD,EAAKyB,eAErCzB,EAAKU,QAAQgG,IAAI,UAAY1J,EAAIgD,EAAKyB,eAE1C1E,EAAOkD,GAAGwG,WAAWvG,KAAKF,EAAMH,IAEpC8G,QACInJ,EACAC,EACAL,EACAE,EACAQ,EACA,SACA,cACA,aAEJ+B,SACI+G,KAAM,WACNtB,iBAAkB,GAClBC,cAAe,GACfC,iBAAkB,GAClBE,kBAAmB,GACnBC,aAAc,GACdC,mBAAoB,GACpBH,oBAAqB,GACrB3B,YAAa,MACbiB,WACIC,MACIC,QAAS,yBACT4B,SAAU,KAEd3B,OAAS2B,SAAU,MAEvBC,aAAa,EACbnF,aAAa,EACbtB,aAAa,EACb0G,YAAcC,SAAU5I,IAE5B6I,QAAS,WACL,GAAIjH,GAAO/E,KAAMiM,EAAalH,EAAKkH,UACnCnK,GAAOkD,GAAGgH,QAAQ/G,KAAKF,GACnBA,EAAKwE,iBACLxE,EAAKc,WAAW2D,OAAO,SAAUzE,EAAKwE,iBAE1CxE,EAAKU,QAAQgG,IAAI1J,GACjBgD,EAAKU,QAAQxG,SAAS,qBAAqBwM,IAAI1J,GAC3CgD,EAAKmH,wBACLnH,EAAKoH,kBAAkBV,MAAMP,SAC7BnG,EAAKqH,kBAAkBX,MAAMP,UAEjChK,EAAM8K,QAAQjH,EAAKU,SACnBwG,EAAWhN,SAAS,eAAeoN,UAEvCd,OAAQ,SAAU5G,GACd,GAAII,GAAO/E,IACX,OAAyB,KAArBsM,UAAUrM,OACH8E,EAAKpE,SAAS1B,SAAS,MAAQW,IAErC2M,MAAM5H,KACPA,EAAUI,EAAKpE,SAAS1B,WAAWoM,IAAI1G,IAE3CA,EAAUI,EAAKpE,SAASsF,KAAKtB,GAC7B9F,EAAE8F,GAAS7E,KAAK,SAAUgE,EAAO/D,GAC7BA,EAAOlB,EAAEkB,GACJA,EAAKoI,SAASvI,IAAiBmF,EAAKuG,QAAQ/I,GACzCxC,KAAMA,EAAK,GACXyM,eAAgBzH,EAAKwC,cAAcxH,EAAK+D,SAAS,MAErDiB,EAAK0C,YAAY1H,KAGlBgF,IAEX0H,OAAQ,SAAU9H,EAAS+H,GAEvB,MADA1M,MAAK2M,gBAAgBhI,EAAS+H,KAAU,GACjC1M,MAEX4M,QAAS,SAAUjI,GAEf,MADA3E,MAAK2M,gBAAgBhI,GAAS,GACvB3E,MAEX6M,OAAQ,SAAUlI,GAAV,GAEAI,GACAK,CAOJ,OATAT,GAAU3E,KAAKW,SAASsF,KAAKtB,GACzBI,EAAO/E,KACPoF,EAAcL,EAAKI,aACvBR,EAAQ7E,KAAK,WACT,GAAIC,GAAOlB,EAAEmB,MAAOwE,EAAazE,EAAKkG,KAAK,IAAM5G,GAAM6G,KAAKxD,IAAe0C,EAAYrF,EAAK+D,SAAUT,EAAU0B,EAAKwC,cAAcxH,EAAK+D,QACpIU,IACAO,EAAK+H,YAAY/M,EAAMsD,EAAS,KAAMmB,KAGvCO,GAEXkG,OAAQ,SAAUd,GACd,GAAIpF,GAAO/E,KAAM+M,EAAWhI,EAAKiI,QAAQ7C,EAkBzC,OAjBArK,GAAKiN,EAAS/N,KAAM,SAAUkL,GAC1B,GAAIhK,GAAW6M,EAAS7M,SAASgK,EACjCnF,GAAKpE,SAASsK,OAAOjL,MACW,UAA5B+E,EAAKH,QAAQiE,YACb9D,EAAKpE,SAASsM,OAAO/M,GACd6E,EAAKmH,sBACZnH,EAAKoH,kBAAkBc,OAAO/M,GAE9B6E,EAAKU,QAAQwF,OAAO/K,GAExB6E,EAAKmI,QAAQ,UAAW,WACpB,OAASC,UAAWjN,QAG5BQ,EAAgBqE,EAAKpE,UACrBoE,EAAKqI,yBACLrI,EAAKsI,QAAO,GACLtI,GAEXuI,eAAgB,SAAUrJ,GACtBjE,KAAKmF,aAAaoI,KAAKtJ,IAE3BuJ,aAAc,SAAUC,EAAMC,GAC1B1N,KAAKmF,aAAawI,OAAOD,EAAI,EAAG1N,KAAKmF,aAAawI,OAAOF,EAAM,GAAG,KAEtEG,eAAgB,SAAU9J,GACtB9D,KAAKmF,aAAawI,OAAO7J,EAAO,IAEpCkH,aAAc,SAAUb,EAAK0D,GAErBA,EADAhP,EAAEsL,GAAK2D,GAAGjP,EAAEgP,IACG7N,KAAKW,SAASsF,KAAK4H,GAAcE,OAEjC/N,KAAKW,SAASsF,KAAK4H,EAEtC,IAAI9I,GAAO/E,KAAM+M,EAAWhI,EAAKiI,QAAQ7C,GAAM6D,EAAmBjJ,EAAKJ,QAAQsB,KAAK,QAAW4H,EAAapO,KAAK,iBAAmB,KAcpI,OAbAK,GAAKiN,EAAS/N,KAAM,SAAUkL,GAAV,GACZhK,GAAW6M,EAAS7M,SAASgK,GAC7B+D,EAAYlB,EAASmB,eAAiBnJ,EAAKI,aAAalF,QAAU8M,EAAS/N,KAAKiB,OAASiK,GAAOrL,EAAEqB,GAAU4D,QAAU,CAC1H+J,GAAaZ,OAAOjN,MACpBgO,EAAiBf,OAAO/M,GACxB6E,EAAKyI,aAAaS,EAAWpP,EAAEmB,MAAM8D,SACrCiB,EAAKmI,QAAQ,UAAW,WACpB,OAASC,UAAWjN,QAG5BQ,EAAgBqE,EAAKpE,UACrBoE,EAAKqI,uBAAuBL,EAASmB,gBACrCnJ,EAAKsI,QAAO,GACLtI,GAEXoJ,YAAa,SAAUhE,EAAK0D,GAEpBA,EADAhP,EAAEsL,GAAK2D,GAAGjP,EAAEgP,IACG7N,KAAKW,SAASsF,KAAK4H,GAAcO,OAEjCpO,KAAKW,SAASsF,KAAK4H,EAEtC,IAAI9I,GAAO/E,KAAM+M,EAAWhI,EAAKiI,QAAQ7C,GAAM6D,EAAmBjJ,EAAKJ,QAAQsB,KAAK,QAAW4H,EAAapO,KAAK,iBAAmB,KAcpI,OAbAK,GAAKiN,EAAS/N,KAAM,SAAUkL,GAAV,GACZhK,GAAW6M,EAAS7M,SAASgK,GAC7B+D,EAAYlB,EAASmB,eAAiBnJ,EAAKI,aAAalF,QAAU8M,EAAS/N,KAAKiB,OAASiK,GAAOrL,EAAEqB,GAAU4D,QAAU,CAC1H+J,GAAaQ,MAAMrO,MACnBgO,EAAiBK,MAAMnO,GACvB6E,EAAKyI,aAAaS,EAAWpP,EAAEmB,MAAM8D,SACrCiB,EAAKmI,QAAQ,UAAW,WACpB,OAASC,UAAWjN,QAG5BQ,EAAgBqE,EAAKpE,UACrBoE,EAAKqI,uBAAuBL,EAASmB,gBACrCnJ,EAAKsI,QAAO,GACLtI,GAEXmG,OAAQ,SAAUiC,GAAV,GAGAjN,GAFA6E,EAAO/E,KACPsO,QAAcnB,EAmBlB,OAjBa,WAATmB,EACAnB,EAAWpI,EAAKpE,SAASsF,KAAKkH,GACd,WAATmB,IACPnB,EAAWpI,EAAKpE,SAAS1B,WAAWyI,GAAGyF,IAE3CjN,EAAWiN,EAAS7L,IAAI,WAAA,GAChB4I,GAAMrL,EAAEmB,MAAM8D,QACdT,EAAU0B,EAAKyH,eAAetC,EAGlC,OAFAhJ,GAAM8K,QAAQ3I,GACd0B,EAAK6I,eAAe1D,GACb7G,IAEX8J,EAASjC,SACThL,EAASuD,QACTvD,EAASgL,SACTnG,EAAKqI,yBACLrI,EAAKsI,QAAO,GACLtI,GAEXiI,QAAS,SAAU7C,GACf,GAAiBnL,GAAMkB,EAAUmD,EAA7B0B,EAAO/E,KAA+BkO,GAAiB,CAmC3D,OAlCA/D,GAAMA,YAAejJ,GAAMgF,KAAKqI,gBAAkBpE,EAAIqE,SAAWrE,EAC7DtL,EAAE4P,cAActE,IAAQtL,EAAE6P,QAAQvE,IAClCA,EAAMtL,EAAE6P,QAAQvE,GAAOA,GAAOA,GAC9B+D,GAAiB,EACjBlP,EAAOsC,EAAI6I,EAAK,SAAUtF,EAAOqF,GAE7B,MADAnF,GAAKuI,eAAenD,EAAID,GAAK1F,YAAc,MACpC3F,EAAE4F,EAASkK,YACd/K,MAAOmB,EAAKpE,SACZZ,KAAMwB,EAAOsD,GAASf,MAAOoG,SAGrChK,EAAWoB,EAAI6I,EAAK,SAAUtF,EAAOqF,GACjC,GAA4B,gBAAjBrF,GAAMxB,SAAuBwB,EAAML,WAC1C,MAAO3F,GAAE4F,EAASmK,eAAgB7O,KAAMwB,EAAOsD,GAASf,MAAOoG,WAKnElL,EADc,gBAAPmL,IAA6B,KAAVA,EAAI,GACvBpF,EAAKJ,QAAQsB,KAAKkE,GAElBtL,EAAEsL,GAEbjK,EAAWrB,IACXG,EAAKc,KAAK,WACN,GAAI,mBAAmB8I,KAAK5I,KAAK6O,WAAWC,WAAY,CACpD,GAAInK,GAAUI,EAAKJ,QAAQsB,KAAK,QAAWjG,KAAK+O,aAAa,iBAAmB,KAChF1L,GAAUsB,MAEVtB,GAAUxE,EAAE,eAAkB4D,EAAU,MAE5CvC,GAAWA,EAAS8O,IAAI3L,KAE5BtE,EAAiBC,KAGjBA,KAAMA,EACNkB,SAAUA,EACVgO,eAAgBA,IAGxBvB,gBAAiB,SAAUhI,EAAS8H,GAChC9H,EAAU3E,KAAKW,SAASsF,KAAKtB,GAC7BA,EAAQ7E,KAAK,WACTjB,EAAEmB,MAAMiP,YAAY1P,EAAckN,GAAQwC,YAAYzP,GAAgBiN,GAAQhN,KAAK,iBAAkBgN,MAG7G9G,eAAgB,WACZ,GAAiB3G,GAAMkQ,EAAYC,EAA/BpK,EAAO/E,IACX+E,GAAKU,QAAQtG,SAAS,gCACtB4F,EAAKpE,SAAWoE,EAAKU,QAAQxG,SAAS,MAAME,SAAS,4BAChD4F,EAAKpE,SAAS,KACfoE,EAAKpE,SAAW9B,EAAE,0CAA4CuQ,SAASrK,EAAKU,UAEhFzG,EAAO+F,EAAKpE,SAASsF,KAAK,MAAM9G,SAAS,UACrCH,EAAKiB,SACLiP,EAAalQ,EAAKM,OAAO,IAAMM,GAAakE,QAC5CqL,EAAYD,GAAc,EAAIA,EAAapQ,EAC3CiG,EAAKpE,SAAST,WAAWZ,OAAO,WAC5B,MAAwB,IAAjBU,KAAKM,WAAkBC,EAAKP,KAAKQ,aACzC0K,UAEHgE,GAAc,GACdlQ,EAAK0I,GAAGwH,GAAY/P,SAASU,GAEjCkF,EAAKsK,gBAAkBtK,EAAKU,QAAQxG,SAAS,OAC7C8F,EAAKsK,gBAAgBlQ,SAASsD,GAASiF,GAAGyH,GAAWhQ,SAASS,GAAa0P,KAAMC,QAAS,UACtFvQ,EAAKiB,SACLlB,EAAiBC,GACjB0B,EAAgBqE,EAAKpE,UACrBoE,EAAKqI,wBAAuB,KAGpCoC,WAAY,SAAU7K,EAASuF,GAAnB,GAIAuF,GAHJC,EAAY/K,EAAQlF,KAAK,MACzBkQ,EAAY3P,KAAK2E,QAAQlF,KAAK,KAClC,QAAKiQ,GAAaA,EAAUvE,QAAQwE,EAAY,SACxCF,GAAcE,GAAazO,EAAM0O,QAAU,IACxCH,GAAcvF,EAAM,IAExBwF,GAEXtC,uBAAwB,SAAUyC,GAC9B,GAAI9K,GAAO/E,KAAMoF,EAAcL,EAAKI,aAAc0F,EAAQ9F,EAAKpE,SAAS1B,SAAS,WAAYoQ,EAAkBtK,EAAKU,QAAQxG,SAAS,OAAQuQ,EAAazK,EAAKyK,WAAW5F,KAAK7E,EAC3KsK,GAAgBpP,QAAU4K,EAAM5K,OAASoP,EAAgBpP,OACzDoP,EAAgBvP,KAAK,SAAUoK,GAAV,GACbvC,GAAK6H,EAAW3Q,EAAEmB,MAAOkK,GACzBnK,EAAO8K,EAAMvL,OAAO,mBAAqBU,KAAK2H,IAAM,GAAK,KAAK,IAC7D5H,GAAQ8P,IACT9P,EAAO8K,EAAMX,IAEbnK,GACAA,EAAK+P,aAAa,gBAAiBnI,GAEvC3H,KAAK8P,aAAa,KAAMnI,KAG5BkD,EAAM/K,KAAK,SAAUoK,GAAV,GACH6F,GAAiBV,EAAgB3H,GAAGwC,GACpCvC,EAAK6H,EAAWO,EAAgB7F,EACpClK,MAAK8P,aAAa,gBAAiBnI,IAC9BoI,EAAe9P,QAAUmF,EAAY8E,GACtCrL,EAAE,eAAkB4D,EAAU,OAAQ2M,SAASrK,EAAKU,SAAShG,KAAK,KAAMkI,IAExEoI,EAAetQ,KAAK,KAAMkI,GACrB9I,EAAEmB,MAAMf,SAAS,cAAc,IAAOmG,EAAY8E,IACnDrL,EAAE,wCAA0CmR,UAAUhQ,OAG9D+P,EAAetQ,KAAK,OAAQ,YAC5BsQ,EAAezQ,OAAO,SAAWM,EAAc,KAAKH,KAAK,eAAe,GAAMA,KAAK,iBAAiB,GACpGsQ,EAAezQ,OAAO,IAAMM,GAAaH,KAAK,iBAAiB,KAGvEsF,EAAKsK,gBAAkBtK,EAAKkL,iBAAmBlL,EAAKU,QAAQxG,SAAS,OACrE8F,EAAKmL,WAAatO,EAAYmD,EAAKpE,UAAYwP,SAASpL,EAAKU,QAAQ6J,IAAI,oBAAqB,IAAMa,SAASpL,EAAKU,QAAQ6J,IAAI,uBAAwB,IAClJpO,EAAMkP,qBAAuBlP,EAAMmP,OAAOjP,GAAGkP,WAC7CpP,EAAMqP,cAAcxL,EAAKsK,iBACzBtK,EAAKsK,gBAAkBtK,EAAKsK,gBAAgBpQ,SAAS,0BAG7DoG,SAAU,WACN,GAAIN,GAAO/E,IAEP+E,GAAKU,QADLV,EAAKJ,QAAQmJ,GAAG,MACD/I,EAAKJ,QAAQlE,QAAQ,WAAWd,SAEhCoF,EAAKJ,QAExBI,EAAKkH,WAAalH,EAAKU,QAAQ9F,OAAO,uBACjCoF,EAAKkH,WAAW,KACjBlH,EAAKkH,WAAalH,EAAKU,QAAQhF,QAAQ,sCAAwCd,WAGvFoG,aAAc,WACV,GAAIhB,GAAO/E,KAAM6I,EAAc9D,EAAKH,QAAQiE,WAC5C9D,GAAKU,QAAQtG,SAAS,0BAA4B0J,GAC/B,UAAfA,GACA9D,EAAKpE,SAASyO,SAASrK,EAAKU,SAEhCV,EAAKsI,QAAO,IAEhBmD,8BAA+B,WAAA,GAGnBC,GAAmDC,EAA4CC,EAA+ChQ,EAA0BiQ,EACxKC,EAHJ9L,EAAO/E,KAAM6I,EAAc9D,EAAKH,QAAQiE,WACzB,SAAfA,GAAwC,SAAfA,IACrB4H,EAAc1L,EAAKU,QAAQxG,SAAS,cAAeyR,EAAYD,EAAYnR,OAAO,YAAaqR,EAAsB,UAAY9H,EAAalI,EAAWoE,EAAKpE,SAAUiQ,EAASlP,EAAWf,GAC5LkQ,EAAYC,KAAKC,KAAKpQ,EAASqQ,UAAYb,SAASO,EAAUpB,IAAI,eAAgB,IAAMa,SAASO,EAAUpB,IAAI,kBAAmB,IAAMa,SAASO,EAAUpB,IAAI,oBAAqB,IAAMa,SAASO,EAAUpB,IAAI,uBAAwB,IAC7O2B,WAAW,WACPR,EAAYnB,IAAIqB,EAAqBC,GAAQtB,IAAI,aAAcuB,OAI3EK,QAAS,WACLlR,KAAKwQ,gCACLxQ,KAAKgG,eAETmL,gBAAiB,SAAUxM,GAAV,GAELkE,GACAuI,CAFJzM,GAAQmJ,GAAG,cACPjF,EAAc7I,KAAK4E,QAAQiE,YAC3BuI,EAAIN,KAAKO,MAAMzP,EAAY+C,GAAS,KAA0B,SAAhBkE,GAA0C,UAAhBA,EAA0B,EAAI7I,KAAKkQ,YAC/GlQ,KAAKiM,WAAWqD,IAAI,SAAU8B,GAAG9B,IAAI,YAG7ClJ,aAAc,SAAUO,GACpB9H,EAAE8H,EAAEU,eAAe4H,YAAYlM,EAAY4D,EAAE2H,MAAQ3L,IAEzDyE,OAAQ,SAAUrH,GACd,GAA6KuR,GAASC,EAAlLxM,EAAO/E,KAAMwR,EAAOzR,EAAKkG,KAAK,IAAM5G,GAAOoS,EAAOD,EAAK/R,KAAKuC,GAAO0P,EAAW3M,EAAKH,QAAQiH,YAAa/H,EAAQ/D,EAAK+D,QAASyD,EAAgBxC,EAAKwC,cAAczD,GAA2B6N,EAAa5R,EAAKJ,SAASV,WAAY2S,EAAgBD,EAAWrS,OAAO,IAAMwD,EAC/Q,IAAI/C,EAAK8R,QAAQ,aAAa,IAAM9M,EAAKU,QAAQ,GAAjD,CAGA,GAAI1F,EAAK+N,GAAG,IAAMtO,GAAkBkS,EAAgC,GAArB,KAAO9R,IAQlD,MAPAgS,GAAchR,YAAYkC,GAC1BiC,EAAKqD,SAAWrI,EAChBA,EAAKZ,SAAS2D,GACdiC,EAAKwB,SAASxG,GACVgF,EAAKmH,uBACLnH,EAAK+M,kBAAkB/R,IAEpB,CAIX,IAFAwR,EAAWC,EAAKtL,KAAKxD,IAAeqC,EAAKI,aAAarB,IAAU2N,IAAyC,KAAhCA,EAAKM,OAAON,EAAKxR,OAAS,IAAawR,EAAKtG,QAAQ,IAAMpG,EAAKJ,QAAQ,GAAGgD,GAAK,UACxJ2J,GAAWG,GAAQF,EACfxM,EAAKpE,SAAS1B,SAAS,oBAAoBgB,OAC3C,MAAOqR,EAEX,IAAIvM,EAAKuG,QAAQ/I,GACTxC,KAAMA,EAAK,GACXyM,eAAgBjF,EAAc,KAElC,OAAO,CAEX,IAAI+J,KAAY,EAGhB,MAAII,IAAY3R,EAAK+N,GAAG,IAAMlO,IAC1BmF,EAAKiN,cAAcjS,IACZ,IAEPgF,EAAK0C,YAAY1H,KACjBuR,GAAU,GAEPA,KAEXtL,YAAa,WAAA,GACgCiM,GAAoBC,EAAqBC,EAAkBC,EAQxFC,EACAC,EACAtL,EACAuL,EAXRxN,EAAO/E,KAAM4E,EAAUG,EAAKH,OAC5BG,GAAKyN,uBACLzN,EAAKU,QAAQtG,SAAS,yBACtB8S,EAAqBlN,EAAKU,QAAQ,GAAGgN,YACrCP,EAAsBnN,EAAKpE,SAAS,GAAG+R,YACnCR,EAAsBD,IAAuBlN,EAAKmH,uBAClDnH,EAAK4N,mBAAoB,EACzB5N,EAAKO,OAASpE,EAAMqE,QAAQC,MAAMT,EAAKJ,SACnC0N,EAAYnR,EAAMqE,QAAQqN,SAAW,aAAe,YACpDN,EAAUpR,EAAMqE,QAAQqN,SAAW,WAAa,UAChD5L,EAAU9F,EAAMqE,QAAQyB,QACxBuL,EAAuBxN,EAAKO,SAAW0B,EAAQJ,OAASI,EAAQ6L,KACpE9N,EAAKU,QAAQwF,OAAOlK,EAAiB,OAAQ,qBAAuBA,EAAiB,OAAQ,uBAC7FoR,EAAmBpN,EAAKoH,kBAAoBpH,EAAKU,QAAQxG,SAAS,oBAClEmT,EAAmBrN,EAAKqH,kBAAoBrH,EAAKU,QAAQxG,SAAS,oBAClE8F,EAAKpE,SAAS2O,KACVwD,WAAYpR,EAAWyQ,GAAoB,EAC3CY,YAAarR,EAAW0Q,GAAoB,KAEhDD,EAAiBhM,GAAGkM,EAAYtQ,EAAI,WAChCgD,EAAK4N,mBAAoB,EACzB5N,EAAKiO,mBAAmBpO,EAAQkH,WAAWC,UAAYwG,EAAuB,SAElFH,EAAiBjM,GAAGkM,EAAYtQ,EAAI,WAChCgD,EAAK4N,mBAAoB,EACzB5N,EAAKiO,mBAAmBpO,EAAQkH,WAAWC,UAAYwG,KAA4B,MAEvFJ,EAAiBnD,IAAIoD,GAAkBjM,GAAGmM,EAAUvQ,EAAI,WACpDgD,EAAK4N,mBAAoB,IAE7B5N,EAAKmH,uBAAwB,EAC7BnH,EAAKkO,wBACElO,EAAKmH,uBAAyBgG,GAAuBD,GAC5DlN,EAAKmH,uBAAwB,EAC7BnH,EAAKU,QAAQ7E,YAAY,yBACzBmE,EAAKoH,kBAAkBV,MAAMP,SAC7BnG,EAAKqH,kBAAkBX,MAAMP,SAC7BnG,EAAKpE,SAAS2O,KACVwD,WAAY,GACZC,YAAa,MAEThO,EAAKmH,sBAGbnH,EAAKkO,uBAFLlO,EAAKU,QAAQ7E,YAAY,2BAMrC4R,mBAAoB,WAChB,GAAI5N,GAAU5E,KAAK4E,OAInB,OAHIA,GAAQkH,aAAelH,EAAQkH,WAAWC,WAC1CnH,EAAQkH,YAAeC,SAAU5I,IAE9ByB,EAAQkH,aAAeS,MAAM3H,EAAQkH,WAAWC,YAAqC,OAAvBnH,EAAQiE,aAA+C,UAAvBjE,EAAQiE,cAEjHiJ,kBAAmB,SAAU/R,GACzB,GAA4VmT,GAAxVnO,EAAO/E,KAAMW,EAAWoE,EAAKpE,SAAUwS,EAAsBxS,EAASyS,aAAcC,EAAY3R,EAAW3B,GAAOuT,EAAavO,EAAKO,OAASvF,EAAKwT,WAAWC,KAAOzT,EAAKwT,WAAWC,KAAO7S,EAAS1B,WAAWwU,QAAQF,WAAWC,KAAME,EAAgB/S,EAAS,GAAG8R,YAAakB,EAAkB7C,KAAKC,KAAK6C,WAAWjT,EAAS2O,IAAI,iBACrUvK,GAAKO,OACDgO,EAAa,EACbJ,EAAeC,EAAsBG,GAAcI,EAAgBP,GAAuBQ,EACnFL,EAAaD,EAAYK,IAChCR,EAAeC,EAAsBG,EAAaD,EAA8B,EAAlBM,GAG9DR,EAAsBO,EAAgBJ,EAAaD,EACnDH,EAAeI,EAAaD,EAAYK,EAAkC,EAAlBC,EACjDR,EAAsBG,IAC7BJ,EAAeI,EAAaK,GAGpChT,EAASkT,SAASC,SAAUV,WAAcF,GAAgB,OAAQ,SAAU,WACxEnO,EAAKkO,0BAGbD,mBAAoB,SAAUe,GAAV,GACZhP,GAAO/E,KACPW,EAAWoE,EAAKpE,SAChBqT,EAAUrT,EAASyS,YACvBzS,GAASkT,SAASC,SAAUV,WAAcY,EAAUD,GAAS,OAAQ,SAAU,WACvEhP,EAAK4N,oBAAsBsB,OAAOC,GAAGzI,IACrC1G,EAAKiO,mBAAmBe,GAExBhP,EAAKkO,0BAIjBA,qBAAsB,WAClB,GAAIlO,GAAO/E,KAAMmU,EAAKpP,EAAKpE,SAAUyS,EAAalS,EAAMkS,WAAWe,EACnEpP,GAAKoH,kBAAkBiI,OAAsB,IAAfhB,GAC9BrO,EAAKqH,kBAAkBgI,OAAOhB,EAAae,EAAG,GAAGzB,YAAcyB,EAAG,GAAG1B,YAAc,IAEvFT,cAAe,SAAUjS,GACrB,GAAIgF,GAAO/E,KAAMqU,EAAoBtP,EAAKH,QAAQkF,UAAWA,EAAYuK,EAAkBtK,KAAME,EAAQ1I,KAAW8S,EAAkBpK,OAAQqK,EAAoBrK,GAAS,WAAaA,EACxLlK,GAAOgF,EAAKpE,SAASsF,KAAKlG,GAC1BkK,EAAQ1I,EAAO+S,EAAoBrK,EAAQ1I,GAASgT,SAAS,GAAQzK,IAAc0K,MAAM,IACrFtT,EAAMuT,KAAK3K,EAAUE,UACrBjK,EAAK2U,cAAcnV,GAAgBqM,SAAU9B,EAAU8B,WACvD7L,EAAK4U,iBAAiB/U,GAAegM,SAAU9B,EAAU8B,aAEzD7L,EAAKZ,SAASI,GACdQ,EAAKa,YAAYhB,IAErBG,EAAKL,WAAW,iBAChBqF,EAAKkL,iBAAiB3Q,OAAO,IAAMM,GAAagV,WAAU,GAAM,GAAMC,aAAa5K,GAAOrJ,YAAYhB,GAAaH,KAAK,eAAe,IAE3IgI,YAAa,SAAU1H,GAAV,GAKLgF,GAAasP,EAA4CvK,EAAoCG,EAA6CqK,EAAiD3C,EAAuCmD,EAA+CC,EAAoCC,EASrT/E,EAgBAgF,EAA8D1N,EAA+CiF,EAQ7G0I,EAAkIC,EA6B/HC,CAlEP,KAAIpV,KAAKW,SAAS1B,SAAS,oBAAoBgB,OAkB/C,MAfAF,GAAOC,KAAKW,SAASsF,KAAKlG,GACtBgF,EAAO/E,KAAMqU,EAAoBtP,EAAKH,QAAQkF,UAAWA,EAAYuK,EAAkBtK,KAAME,EAAQ1I,KAAW8S,EAAkBpK,OAAQqK,EAAoBrK,GAAS,WAAaA,GAAO0H,EAAa5R,EAAKJ,SAASV,WAAY6V,EAASnD,EAAWrS,OAAO,IAAMM,GAAcmV,EAAYpD,EAAW7N,MAAM/D,GAAOiV,EAAqBlL,GAAa,YAAcA,IAAa,WAAaA,GACnYG,EAAQ1I,EAAO+S,EAAoBrK,EAAQ1I,GAASgT,SAAS,GAAQzK,IAAc0K,MAAM,IACrFtT,EAAMuT,KAAK3K,EAAUE,UACrB8K,EAAOH,iBAAiB/U,GAAegM,SAAU3B,EAAM2B,WACvD7L,EAAK4U,iBAAiB5R,GAAc6I,SAAU3B,EAAM2B,aAEpDkJ,EAAOlU,YAAYhB,GACnBG,EAAKa,YAAYmC,IAEjBkN,EAAmBlL,EAAKkL,iBACxBlL,EAAKsQ,YACLtQ,EAAKuQ,IAAIC,QACTxQ,EAAKsQ,WAAY,GAEW,IAA5BpF,EAAiBhQ,QACjB8E,EAAKpE,SAASsF,KAAK,IAAMpG,GAAUe,YAAYf,GAC/CE,EAAKZ,SAASU,GAAUyP,IAAI,WAC5BvP,EAAKZ,SAASS,GACdmF,EAAKwB,SAASxG,GACdgF,EAAKuG,QAAQ,UACTvG,EAAKmH,uBACLnH,EAAK+M,kBAAkB/R,IAEpB,IAEPkV,EAAkBhF,EAAiB3Q,OAAO,IAAMM,GAAc2H,EAAgBxC,EAAKwC,cAAcwN,GAAYvI,EAAiBjF,EAAcsK,QAAQ,cACxJ9M,EAAKmL,WAAatO,EAAYmD,EAAKpE,UAAYwP,SAASpL,EAAKU,QAAQ6J,IAAI,oBAAqB,IAAMa,SAASpL,EAAKU,QAAQ6J,IAAI,uBAAwB,IACtJvK,EAAKoM,gBAAgB8D,GACQ,IAAzB1N,EAActH,QACdgV,EAAgBrU,YAAYhB,GAAaH,KAAK,eAAe,GAAMmV,WAAU,GAAM,GAAMC,aAAa5K,IAC/F,IAEXlK,EAAKN,KAAK,kBAAkB,GACxByV,GAAiBnV,EAAKd,SAAS,IAAMI,GAAM6G,KAAKxD,IAAeqC,EAAKI,aAAa4P,KAAc,IAAUxN,EAAcuG,GAAGxL,GAAQ6S,EAAqB,WACnJL,EAAOpV,WAAW,iBAClBK,EAAKN,KAAK,iBAAiB,GAC3BsF,EAAKwB,SAASxG,GACdgF,EAAKoM,gBAAgB3E,GACrBA,EAAerN,SAASS,GAAaF,WAAW,eAAekV,WAAU,GAAM,GAAMnV,KAAK,iBAAiB,GAAMoV,aAAatT,GAC1HmD,KAAM,WACFK,EAAKuG,QAAQnJ,GACTpC,KAAMA,EAAK,GACXyM,eAAgBjF,EAAc,KAElCrG,EAAMmM,OAAO9F,KAElBuC,GACC0L,SAAU,WACNzV,EAAKL,WAAW,kBAChBqF,EAAKuG,QAAQ9I,GACTzC,KAAMA,EAAK,GACXyM,eAAgBjF,EAAc,KAElCrG,EAAMmM,OAAO9F,GACbxC,EAAKkH,WAAWqD,IAAI,SAAU,IAAIA,IAAI,UAClC0F,IAAuB9T,EAAMqE,QAAQyB,QAAQJ,MAAQ1F,EAAMqE,QAAQyB,QAAQ6L,OAC3EtL,EAAcsM,SAASC,SAAU2B,QAAS,IAAO,OAAQ,SAAU,WAC/DlO,EAAcsM,SAASC,SAAU2B,QAAS,GAAK,OAAQ,iBAKxEL,EAAc,WACRF,GAIDnV,EAAKL,WAAW,kBAChBqF,EAAK+H,YAAY/M,EAAMwH,EAAe,WAClCxH,EAAKN,KAAK,kBAAkB,GAC5B0V,IACApQ,EAAKuG,QAAQ,cAPjB6J,IACApQ,EAAKuG,QAAQ,WASbvG,EAAKmH,uBACLnH,EAAK+M,kBAAkB/R,IAGnCkV,EAAgBrU,YAAYhB,GAC5BmF,EAAKpE,SAASsF,KAAK,IAAMpG,GAAUe,YAAYf,GAC/CE,EAAKZ,SAASU,GAAUyP,IAAI,WACxBpO,EAAMuT,KAAK3K,EAAUE,UACrB8K,EAAOJ,cAAcnV,GAAgBqM,SAAU9B,EAAU8B,WACzD7L,EAAK2U,cAAc9U,GAAegM,SAAU9B,EAAU8B,aAEtDkJ,EAAO3V,SAASI,GAChBQ,EAAKZ,SAASS,IAElBqV,EAAgBxV,KAAK,eAAe,GACpCwV,EAAgBxV,KAAK,iBAAiB,GAClCwV,EAAgBhV,OAChBgV,EAAgBL,WAAU,GAAM,GAAMC,aAAatT,GAASiU,SAAUJ,GAAenL,IAErFmL,KAEG,KAEX5I,eAAgB,SAAUuI,GAAV,GAIR1F,GACA1H,EAES+N,EAAOC,CANpB,IAAIpJ,MAAMwI,EAAY,GAClB,MAAOjW,EAIX,IAFIuQ,EAAkBrP,KAAKqP,iBAAmBrP,KAAKqP,gBAAgB,KAAOnO,EAAMkP,oBAAsBpQ,KAAKqP,gBAAkBrP,KAAKiQ,iBAC9HtI,EAAK9I,EAAEmB,KAAKW,SAAS1B,WAAW8V,IAAYtV,KAAK,iBACjD4P,EACA,IAASqG,EAAI,EAAGC,EAAMtG,EAAgBpP,OAAQyV,EAAIC,EAAKD,IACnD,GAAIrG,EAAgB3H,GAAGgO,GAAG7D,QAAQ,cAAc,GAAGlK,IAAMA,EACrD,MAAO0H,GAAgBqG,EAInC,OAAO5W,IAEXyI,cAAe,SAAUwN,GACrB,GAAIvI,GAAiB3N,EAAEmB,KAAKwM,eAAeuI,IAAaa,EAAkBpJ,EAAevN,SAAS,uBAClG,OAAOiC,GAAMqE,QAAQsQ,OAASD,EAAgB,GAAKA,EAAkBpJ,GAEzEM,YAAa,SAAUnI,EAAStB,EAASmS,EAAUvR,GAAtC,GAELc,GAAauQ,EAA0B9D,EAAiCtL,EAAW4P,EAAiCC,EAAsBC,EAI1IC,EACAC,EAQAC,CAdJxR,GAAU3E,KAAKW,SAASsF,KAAKtB,GACzBI,EAAO/E,KAAMsV,EAAMzW,EAAEuX,aAAad,IAAK9D,EAAO7M,EAAQsB,KAAK,IAAM5G,GAAO6G,KAAW4P,EAAYnR,EAAQ0R,QAAU,EAAGN,GAAe,EAAOC,EAAarR,EAAQsB,KAAK,cAAcrF,YAAY,cAC7LoV,EAAW,KACZA,EAAanX,EAAE,6BAA+BmR,UAAUrL,IAExDsR,EAAuB,EAAZH,EAAgBE,EAAWK,QACtCH,EAAuB,WACvBF,EAAWlC,SAAUhB,YAAa3C,SAAS6F,EAAW1G,IAAI,cAAe,KAAO,GAAKwG,EAAYG,EAAW,GAAK,IAAKC,IAEtHhV,EAAMqE,QAAQyB,QAAQJ,MAAQ1F,EAAMqE,QAAQyB,QAAQsP,QAAU,IAC9DrF,WAAWiF,EAAsB,IAErCjS,EAAMA,GAAOuN,EAAKtL,KAAKxD,IAAeqC,EAAKI,aAAaR,EAAQb,UAAY0N,EAAK/R,KAAKuC,GACtF+C,EAAKsQ,WAAY,EACbc,GACA7H,KAAM,MACNiI,OAAO,EACPtS,IAAKA,EACLuS,SAAU,OACVtQ,KAAMA,EACNoP,IAAK,WACD,GAAI7M,GAAUzI,KAAMyW,EAAUnB,IAAOoB,EAAQjO,EAAQkO,eAAiB,mBAAmBlO,EAAQmO,UAAW,UAgB5G,OAfIH,IACA5X,EAAEiB,MACE2W,EACAA,EAAQI,QACT,WACK7W,KAAK8W,kBACL9W,KAAK8W,iBAAiB,WAAY,SAAUC,GACpCL,GACAjO,EAAQiO,GAAOK,KAEpB,KAIftO,EAAQuO,aAAe7V,OAAO8V,gBAAkB,UAAY,IAAIA,iBACzDR,GAEXG,SAAU,SAAUG,GAChB,GAAIA,EAAIG,iBAAkB,CACtB,GAAIC,GAAUhH,SAAS4G,EAAIK,OAASL,EAAIM,MAAQ,IAAK,IAAM,GAC3DrB,GAAWsB,MAAK,GAAMnY,SAAS,cAAcmQ,KACzC+G,MAASc,EACTrE,WAAc,MAI1ByE,MAAO,SAAUjC,EAAKkC,GACdzS,EAAKuG,QAAQ,SACTgK,IAAKA,EACLkC,OAAQA,KAEZxX,KAAKwV,YAGbiC,aAAc,WACVC,cAAc3B,GACdC,EAAWsB,MAAK,GAAMnY,SAAS,cAAc,GAAGwY,MAAMC,QAAU,IAEpEpC,SAAU,SAAUF,GAChBvQ,EAAKsQ,WAAY,EACbrV,KAAKgX,WACL/F,WAAWjR,KAAKyX,aAAc,KAE9BzX,KAAKyX,eAEa,SAAlBnC,EAAIuC,YACJ7B,EAAW9K,UAGnB4M,QAAS,SAAU5R,GAAV,GAGGuC,GAAgB2O,EAkBhBW,CApBR/B,GAAW7W,SAAS,aACpB,KACQsJ,EAAUzI,KAAMoX,EAAS,GACzB3O,EAAQuO,aACRhB,EAAWK,MAAMe,EAAS,KAC1BrB,EAAeiC,YAAY,WACvBvP,EAAQmO,UACJM,kBAAkB,EAClBE,OAAQtG,KAAKmH,IAAIb,EAAQ,KACzBC,MAAO,MAEXD,GAAU,IACX,KAEPrS,EAAKmI,QAAQ,UAAW,WACpB,OAASC,SAAU9J,EAAQgI,SAE/BnK,EAAM8K,QAAQ3I,GACdA,EAAQ6U,KAAKhS,GACf,MAAOS,GACDoR,EAAU5W,OAAO4W,QACjBA,GAAWA,EAAQR,OACnBQ,EAAQR,MAAM5Q,EAAEgF,KAAO,KAAOhF,EAAEwR,QAAU,OAASlU,GAEvDjE,KAAKuX,MAAMvX,KAAKsV,IAAK,SAErBE,GACAA,EAASvQ,KAAKF,EAAM1B,GAExB0B,EAAKmI,QAAQ,UAAW,WACpB,OAASC,SAAU9J,EAAQgI,SAE/BtG,EAAKuG,QAAQzI,GACT9C,KAAM4E,EAAQ,GACd6H,eAAgBnJ,EAAQ,OAIjB,gBAARY,KACPkS,EAActX,EAAE0C,QAAO,KAAU4U,EAAalS,GAC1CzC,EAAW2U,EAAYlS,OACvBkS,EAAYlS,IAAMkS,EAAYlS,QAGtCc,EAAKuQ,IAAMzW,EAAEuZ,KAAKjC,KAG1B5U,GAAOkD,GACHkK,WAAY,SAAU/J,GAClBA,EAAUrD,GACN8W,YACAzU,UACDgB,EACH,IAAInB,GAAQL,EAAUK,MAAO1D,EAAO6E,EAAQ7E,IAC5C,OAAOqD,GAAUrD,KAAKwB,EAAOqD,GACzBrB,MAAOxD,EAAK+K,SAAW1H,EAAUG,MAAQE,EACzCD,OAAQzD,EAAKgL,eAAiB3H,EAAUI,OAASC,EACjDH,YAAaF,EAAUE,aACxBI,KAEPkL,cAAe,SAAUhK,GACrB,MAAOxB,GAAUC,QAAQ9B,EAAOqD,EAASlB,OAGjDxC,EAAME,GAAGkX,OAAO7T,IAClBtD,OAAOD,MAAM+S,QACR9S,OAAOD,OACE,kBAAVtC,SAAwBA,OAAO2Z,IAAM3Z,OAAS,SAAU4Z,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.tabstrip.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.tabstrip', ['kendo.data'], f);\n}(function () {\n    var __meta__ = {\n        id: 'tabstrip',\n        name: 'TabStrip',\n        category: 'web',\n        description: 'The TabStrip widget displays a collection of tabs with associated tab content.',\n        depends: ['data'],\n        features: [{\n                id: 'tabstrip-fx',\n                name: 'Animation',\n                description: 'Support for animation',\n                depends: ['fx']\n            }]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, keys = kendo.keys, map = $.map, each = $.each, trim = $.trim, extend = $.extend, isFunction = kendo.isFunction, template = kendo.template, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, Widget = ui.Widget, excludedNodesRegExp = /^(a|div)$/i, NS = '.kendoTabStrip', IMG = 'img', HREF = 'href', PREV = 'prev', NEXT = 'next', SHOW = 'show', LINK = 'k-link', LAST = 'k-last', CLICK = 'click', ERROR = 'error', EMPTY = ':empty', IMAGE = 'k-image', FIRST = 'k-first', SELECT = 'select', ACTIVATE = 'activate', CONTENT = 'k-content', CONTENTURL = 'contentUrl', MOUSEENTER = 'mouseenter', MOUSELEAVE = 'mouseleave', CONTENTLOAD = 'contentLoad', DISABLEDSTATE = 'k-state-disabled', DEFAULTSTATE = 'k-state-default', ACTIVESTATE = 'k-state-active', FOCUSEDSTATE = 'k-state-focused', HOVERSTATE = 'k-state-hover', TABONTOP = 'k-tab-on-top', NAVIGATABLEITEMS = '.k-item:not(.' + DISABLEDSTATE + ')', KEYBOARDNAVIGATABLEITEMS = '.k-item', HOVERABLEITEMS = '.k-tabstrip-items > ' + NAVIGATABLEITEMS + ':not(.' + ACTIVESTATE + ')', DEFAULTDISTANCE = 200, templates = {\n                content: template('<div class=\\'k-content\\'#= contentAttributes(data) # role=\\'tabpanel\\'>#= content(item) #</div>'),\n                itemWrapper: template('<#= tag(item) # class=\\'k-link\\'#= contentUrl(item) ##= textAttributes(item) #>' + '#= image(item) ##= sprite(item) ##= text(item) #' + '</#= tag(item) #>'),\n                item: template('<li class=\\'#= wrapperCssClass(group, item) #\\' role=\\'tab\\' #=item.active ? \"aria-selected=\\'true\\'\" : \\'\\'#>' + '#= itemWrapper(data) #' + '</li>'),\n                image: template('<img class=\\'k-image\\' alt=\\'\\' src=\\'#= imageUrl #\\' />'),\n                sprite: template('<span class=\\'k-sprite #= spriteCssClass #\\'></span>'),\n                empty: template('')\n            }, rendering = {\n                wrapperCssClass: function (group, item) {\n                    var result = 'k-item', index = item.index;\n                    if (item.enabled === false) {\n                        result += ' k-state-disabled';\n                    } else {\n                        result += ' k-state-default';\n                    }\n                    if (index === 0) {\n                        result += ' k-first';\n                    }\n                    if (index == group.length - 1) {\n                        result += ' k-last';\n                    }\n                    return result;\n                },\n                textAttributes: function (item) {\n                    return item.url ? ' href=\\'' + item.url + '\\'' : '';\n                },\n                text: function (item) {\n                    return item.encoded === false ? item.text : kendo.htmlEncode(item.text);\n                },\n                tag: function (item) {\n                    return item.url ? 'a' : 'span';\n                },\n                contentAttributes: function (content) {\n                    return content.active !== true ? ' style=\\'display:none\\' aria-hidden=\\'true\\' aria-expanded=\\'false\\'' : '';\n                },\n                content: function (item) {\n                    return item.content ? item.content : item.contentUrl ? '' : '&nbsp;';\n                },\n                contentUrl: function (item) {\n                    return item.contentUrl ? kendo.attr('content-url') + '=\"' + item.contentUrl + '\"' : '';\n                }\n            };\n        function updateTabClasses(tabs) {\n            tabs.children(IMG).addClass(IMAGE);\n            tabs.children('a').addClass(LINK).children(IMG).addClass(IMAGE);\n            tabs.filter(':not([disabled]):not([class*=k-state-disabled])').addClass(DEFAULTSTATE);\n            tabs.filter('li[disabled]').addClass(DISABLEDSTATE).attr('aria-disabled', 'true').removeAttr('disabled');\n            tabs.filter(':not([class*=k-state])').children('a').filter(':focus').parent().addClass(ACTIVESTATE + ' ' + TABONTOP);\n            tabs.attr('role', 'tab');\n            tabs.filter('.' + ACTIVESTATE).attr('aria-selected', true);\n            tabs.each(function () {\n                var item = $(this);\n                if (!item.children('.' + LINK).length) {\n                    item.contents().filter(function () {\n                        return !this.nodeName.match(excludedNodesRegExp) && !(this.nodeType == 3 && !trim(this.nodeValue));\n                    }).wrapAll('<span UNSELECTABLE=\\'on\\' class=\\'' + LINK + '\\'/>');\n                }\n            });\n        }\n        function updateFirstLast(tabGroup) {\n            var tabs = tabGroup.children('.k-item');\n            tabs.filter('.k-first:not(:first-child)').removeClass(FIRST);\n            tabs.filter('.k-last:not(:last-child)').removeClass(LAST);\n            tabs.filter(':first-child').addClass(FIRST);\n            tabs.filter(':last-child').addClass(LAST);\n        }\n        function scrollButtonHtml(buttonClass, iconClass) {\n            return '<span class=\\'k-button k-button-icon k-bare k-tabstrip-' + buttonClass + '\\' unselectable=\\'on\\'><span class=\\'k-icon ' + iconClass + '\\'></span></span>';\n        }\n        var TabStrip = Widget.extend({\n            init: function (element, options) {\n                var that = this, value;\n                Widget.fn.init.call(that, element, options);\n                that._animations(that.options);\n                options = that.options;\n                that._contentUrls = options.contentUrls || [];\n                that._wrapper();\n                that._isRtl = kendo.support.isRtl(that.wrapper);\n                that._tabindex();\n                that._updateClasses();\n                that._dataSource();\n                if (options.dataSource) {\n                    that.dataSource.fetch();\n                }\n                that._tabPosition();\n                that._scrollable();\n                if (that._contentUrls.length) {\n                    that.wrapper.find('.k-tabstrip-items > .k-item').each(function (index, item) {\n                        var url = that._contentUrls[index];\n                        if (typeof url === 'string') {\n                            $(item).find('>.' + LINK).data(CONTENTURL, url);\n                        }\n                    });\n                } else {\n                    that._contentUrls.length = that.tabGroup.find('li.k-item').length;\n                }\n                that.wrapper.on(MOUSEENTER + NS + ' ' + MOUSELEAVE + NS, HOVERABLEITEMS, that._toggleHover).on('focus' + NS, $.proxy(that._active, that)).on('blur' + NS, function () {\n                    that._current(null);\n                });\n                that._keyDownProxy = $.proxy(that._keydown, that);\n                if (options.navigatable) {\n                    that.wrapper.on('keydown' + NS, that._keyDownProxy);\n                }\n                if (that.options.value) {\n                    value = that.options.value;\n                }\n                that.wrapper.children('.k-tabstrip-items').on(CLICK + NS, '.k-state-disabled .k-link', false).on(CLICK + NS, ' > ' + NAVIGATABLEITEMS, function (e) {\n                    var wr = that.wrapper[0];\n                    if (wr !== document.activeElement) {\n                        var msie = kendo.support.browser.msie;\n                        if (msie) {\n                            try {\n                                wr.setActive();\n                            } catch (j) {\n                                wr.focus();\n                            }\n                        } else {\n                            wr.focus();\n                        }\n                    }\n                    if (that._click($(e.currentTarget))) {\n                        e.preventDefault();\n                    }\n                });\n                var selectedItems = that.tabGroup.children('li.' + ACTIVESTATE), content = that.contentHolder(selectedItems.index());\n                if (selectedItems[0] && content.length > 0 && content[0].childNodes.length === 0) {\n                    that.activateTab(selectedItems.eq(0));\n                }\n                that.element.attr('role', 'tablist');\n                if (that.element[0].id) {\n                    that._ariaId = that.element[0].id + '_ts_active';\n                }\n                that.value(value);\n                kendo.notify(that);\n            },\n            _active: function () {\n                var item = this.tabGroup.children().filter('.' + ACTIVESTATE);\n                item = item[0] ? item : this._endItem('first');\n                if (item[0]) {\n                    this._current(item);\n                }\n            },\n            _endItem: function (action) {\n                return this.tabGroup.children(NAVIGATABLEITEMS)[action]();\n            },\n            _getItem: function (action) {\n                return this.tabGroup.children(KEYBOARDNAVIGATABLEITEMS)[action]();\n            },\n            _item: function (item, action) {\n                var endItem;\n                if (action === PREV) {\n                    endItem = 'last';\n                } else {\n                    endItem = 'first';\n                }\n                if (!item) {\n                    return this._endItem(endItem);\n                }\n                item = item[action]();\n                if (!item[0]) {\n                    item = this.tabGroup.children(KEYBOARDNAVIGATABLEITEMS)[endItem]();\n                }\n                if (item.hasClass(DISABLEDSTATE)) {\n                    item.addClass(FOCUSEDSTATE);\n                }\n                if (item.hasClass(DISABLEDSTATE) || item.hasClass(ACTIVESTATE)) {\n                    this._focused = item;\n                }\n                return item;\n            },\n            _current: function (candidate) {\n                var that = this, focused = that._focused, id = that._ariaId;\n                if (candidate === undefined) {\n                    return focused;\n                }\n                if (focused) {\n                    that.tabGroup.children('#' + id).removeAttr('id');\n                    focused.removeClass(FOCUSEDSTATE);\n                }\n                if (candidate) {\n                    if (!candidate.hasClass(ACTIVESTATE)) {\n                        candidate.addClass(FOCUSEDSTATE);\n                    }\n                    that.element.removeAttr('aria-activedescendant');\n                    id = candidate[0].id || id;\n                    if (id) {\n                        candidate.attr('id', id);\n                        that.element.attr('aria-activedescendant', id);\n                    }\n                }\n                that._focused = candidate;\n            },\n            _keydown: function (e) {\n                var that = this, key = e.keyCode, current = that._current(), rtl = that._isRtl, isHorizontal = /top|bottom/.test(that.options.tabPosition), action;\n                if (e.target != e.currentTarget) {\n                    return;\n                }\n                if (key === keys.DOWN && !isHorizontal) {\n                    action = NEXT;\n                } else if (key === keys.UP && !isHorizontal) {\n                    action = PREV;\n                } else if (key === keys.RIGHT && isHorizontal) {\n                    action = rtl ? PREV : NEXT;\n                } else if (key === keys.LEFT && isHorizontal) {\n                    action = rtl ? NEXT : PREV;\n                } else if (key == keys.ENTER || key == keys.SPACEBAR) {\n                    that._click(current);\n                    e.preventDefault();\n                } else if (key == keys.HOME) {\n                    that._click(that._getItem('first'));\n                    e.preventDefault();\n                    return;\n                } else if (key == keys.END) {\n                    that._click(that._getItem('last'));\n                    e.preventDefault();\n                    return;\n                }\n                if (action) {\n                    that._click(that._item(current, action));\n                    e.preventDefault();\n                }\n            },\n            _dataSource: function () {\n                var that = this;\n                if (that.dataSource && that._refreshHandler) {\n                    that.dataSource.unbind('change', that._refreshHandler);\n                } else {\n                    that._refreshHandler = $.proxy(that.refresh, that);\n                }\n                that.dataSource = kendo.data.DataSource.create(that.options.dataSource).bind('change', that._refreshHandler);\n            },\n            setDataSource: function (dataSource) {\n                var that = this;\n                that.options.dataSource = dataSource;\n                that._dataSource();\n                that.dataSource.fetch();\n            },\n            _animations: function (options) {\n                if (options && 'animation' in options && !options.animation) {\n                    options.animation = {\n                        open: { effects: {} },\n                        close: { effects: {} }\n                    };\n                }\n            },\n            refresh: function (e) {\n                var that = this, options = that.options, encoded = kendo.getter(options.dataEncodedField), text = kendo.getter(options.dataTextField), content = kendo.getter(options.dataContentField), contentUrl = kendo.getter(options.dataContentUrlField), image = kendo.getter(options.dataImageUrlField), url = kendo.getter(options.dataUrlField), sprite = kendo.getter(options.dataSpriteCssClass), idx, tabs = [], tab, action, view = that.dataSource.view(), length;\n                e = e || {};\n                action = e.action;\n                if (action) {\n                    view = e.items;\n                }\n                for (idx = 0, length = view.length; idx < length; idx++) {\n                    tab = { text: text(view[idx]) };\n                    if (options.dataEncodedField) {\n                        tab.encoded = encoded(view[idx]);\n                    }\n                    if (options.dataContentField) {\n                        tab.content = content(view[idx]);\n                    }\n                    if (options.dataContentUrlField) {\n                        tab.contentUrl = contentUrl(view[idx]);\n                    }\n                    if (options.dataUrlField) {\n                        tab.url = url(view[idx]);\n                    }\n                    if (options.dataImageUrlField) {\n                        tab.imageUrl = image(view[idx]);\n                    }\n                    if (options.dataSpriteCssClass) {\n                        tab.spriteCssClass = sprite(view[idx]);\n                    }\n                    tabs[idx] = tab;\n                }\n                if (e.action == 'add') {\n                    if (e.index < that.tabGroup.children().length) {\n                        that.insertBefore(tabs, that.tabGroup.children().eq(e.index));\n                    } else {\n                        that.append(tabs);\n                    }\n                } else if (e.action == 'remove') {\n                    for (idx = 0; idx < view.length; idx++) {\n                        that.remove(e.index);\n                    }\n                } else if (e.action == 'itemchange') {\n                    idx = that.dataSource.view().indexOf(view[0]);\n                    if (e.field === options.dataTextField) {\n                        that.tabGroup.children().eq(idx).find('.k-link').text(view[0].get(e.field));\n                    }\n                    if (e.field === options.dataUrlField) {\n                        that._contentUrls[idx] = view[0].get(e.field);\n                    }\n                } else {\n                    that.trigger('dataBinding');\n                    that.remove('li');\n                    that._contentUrls = [];\n                    that.append(tabs);\n                    that.trigger('dataBound');\n                }\n            },\n            value: function (value) {\n                var that = this;\n                if (value !== undefined) {\n                    if (value != that.value()) {\n                        that.tabGroup.children().each(function () {\n                            if ($.trim($(this).text()) == value) {\n                                that.select(this);\n                            }\n                        });\n                    }\n                } else {\n                    return that.select().text();\n                }\n            },\n            items: function () {\n                return this.tabGroup[0].children;\n            },\n            setOptions: function (options) {\n                var that = this, animation = that.options.animation;\n                that._animations(options);\n                if (options.contentUrls) {\n                    that._contentUrls = options.contentUrls;\n                }\n                options.animation = extend(true, animation, options.animation);\n                if (options.navigatable) {\n                    that.wrapper.on('keydown' + NS, that._keyDownProxy);\n                } else {\n                    that.wrapper.off('keydown' + NS, that._keyDownProxy);\n                }\n                Widget.fn.setOptions.call(that, options);\n            },\n            events: [\n                SELECT,\n                ACTIVATE,\n                SHOW,\n                ERROR,\n                CONTENTLOAD,\n                'change',\n                'dataBinding',\n                'dataBound'\n            ],\n            options: {\n                name: 'TabStrip',\n                dataEncodedField: '',\n                dataTextField: '',\n                dataContentField: '',\n                dataImageUrlField: '',\n                dataUrlField: '',\n                dataSpriteCssClass: '',\n                dataContentUrlField: '',\n                tabPosition: 'top',\n                animation: {\n                    open: {\n                        effects: 'expand:vertical fadeIn',\n                        duration: 200\n                    },\n                    close: { duration: 200 }\n                },\n                collapsible: false,\n                navigatable: true,\n                contentUrls: false,\n                scrollable: { distance: DEFAULTDISTANCE }\n            },\n            destroy: function () {\n                var that = this, scrollWrap = that.scrollWrap;\n                Widget.fn.destroy.call(that);\n                if (that._refreshHandler) {\n                    that.dataSource.unbind('change', that._refreshHandler);\n                }\n                that.wrapper.off(NS);\n                that.wrapper.children('.k-tabstrip-items').off(NS);\n                if (that._scrollableModeActive) {\n                    that._scrollPrevButton.off().remove();\n                    that._scrollNextButton.off().remove();\n                }\n                kendo.destroy(that.wrapper);\n                scrollWrap.children('.k-tabstrip').unwrap();\n            },\n            select: function (element) {\n                var that = this;\n                if (arguments.length === 0) {\n                    return that.tabGroup.children('li.' + ACTIVESTATE);\n                }\n                if (!isNaN(element)) {\n                    element = that.tabGroup.children().get(element);\n                }\n                element = that.tabGroup.find(element);\n                $(element).each(function (index, item) {\n                    item = $(item);\n                    if (!item.hasClass(ACTIVESTATE) && !that.trigger(SELECT, {\n                            item: item[0],\n                            contentElement: that.contentHolder(item.index())[0]\n                        })) {\n                        that.activateTab(item);\n                    }\n                });\n                return that;\n            },\n            enable: function (element, state) {\n                this._toggleDisabled(element, state !== false);\n                return this;\n            },\n            disable: function (element) {\n                this._toggleDisabled(element, false);\n                return this;\n            },\n            reload: function (element) {\n                element = this.tabGroup.find(element);\n                var that = this;\n                var contentUrls = that._contentUrls;\n                element.each(function () {\n                    var item = $(this), contentUrl = item.find('.' + LINK).data(CONTENTURL) || contentUrls[item.index()], content = that.contentHolder(item.index());\n                    if (contentUrl) {\n                        that.ajaxRequest(item, content, null, contentUrl);\n                    }\n                });\n                return that;\n            },\n            append: function (tab) {\n                var that = this, inserted = that._create(tab);\n                each(inserted.tabs, function (idx) {\n                    var contents = inserted.contents[idx];\n                    that.tabGroup.append(this);\n                    if (that.options.tabPosition == 'bottom') {\n                        that.tabGroup.before(contents);\n                    } else if (that._scrollableModeActive) {\n                        that._scrollPrevButton.before(contents);\n                    } else {\n                        that.wrapper.append(contents);\n                    }\n                    that.angular('compile', function () {\n                        return { elements: [contents] };\n                    });\n                });\n                updateFirstLast(that.tabGroup);\n                that._updateContentElements();\n                that.resize(true);\n                return that;\n            },\n            _appendUrlItem: function (url) {\n                this._contentUrls.push(url);\n            },\n            _moveUrlItem: function (from, to) {\n                this._contentUrls.splice(to, 0, this._contentUrls.splice(from, 1)[0]);\n            },\n            _removeUrlItem: function (index) {\n                this._contentUrls.splice(index, 1);\n            },\n            insertBefore: function (tab, referenceTab) {\n                if ($(tab).is($(referenceTab))) {\n                    referenceTab = this.tabGroup.find(referenceTab).next();\n                } else {\n                    referenceTab = this.tabGroup.find(referenceTab);\n                }\n                var that = this, inserted = that._create(tab), referenceContent = that.element.find('[id=\\'' + referenceTab.attr('aria-controls') + '\\']');\n                each(inserted.tabs, function (idx) {\n                    var contents = inserted.contents[idx];\n                    var fromIndex = inserted.newTabsCreated ? that._contentUrls.length - (inserted.tabs.length - idx) : $(contents).index() - 1;\n                    referenceTab.before(this);\n                    referenceContent.before(contents);\n                    that._moveUrlItem(fromIndex, $(this).index());\n                    that.angular('compile', function () {\n                        return { elements: [contents] };\n                    });\n                });\n                updateFirstLast(that.tabGroup);\n                that._updateContentElements(inserted.newTabsCreated);\n                that.resize(true);\n                return that;\n            },\n            insertAfter: function (tab, referenceTab) {\n                if ($(tab).is($(referenceTab))) {\n                    referenceTab = this.tabGroup.find(referenceTab).prev();\n                } else {\n                    referenceTab = this.tabGroup.find(referenceTab);\n                }\n                var that = this, inserted = that._create(tab), referenceContent = that.element.find('[id=\\'' + referenceTab.attr('aria-controls') + '\\']');\n                each(inserted.tabs, function (idx) {\n                    var contents = inserted.contents[idx];\n                    var fromIndex = inserted.newTabsCreated ? that._contentUrls.length - (inserted.tabs.length - idx) : $(contents).index() - 1;\n                    referenceTab.after(this);\n                    referenceContent.after(contents);\n                    that._moveUrlItem(fromIndex, $(this).index());\n                    that.angular('compile', function () {\n                        return { elements: [contents] };\n                    });\n                });\n                updateFirstLast(that.tabGroup);\n                that._updateContentElements(inserted.newTabsCreated);\n                that.resize(true);\n                return that;\n            },\n            remove: function (elements) {\n                var that = this;\n                var type = typeof elements;\n                var contents;\n                if (type === 'string') {\n                    elements = that.tabGroup.find(elements);\n                } else if (type === 'number') {\n                    elements = that.tabGroup.children().eq(elements);\n                }\n                contents = elements.map(function () {\n                    var idx = $(this).index();\n                    var content = that.contentElement(idx);\n                    kendo.destroy(content);\n                    that._removeUrlItem(idx);\n                    return content;\n                });\n                elements.remove();\n                contents.empty();\n                contents.remove();\n                that._updateContentElements();\n                that.resize(true);\n                return that;\n            },\n            _create: function (tab) {\n                var that = this, tabs, contents, content, newTabsCreated = false;\n                tab = tab instanceof kendo.data.ObservableArray ? tab.toJSON() : tab;\n                if ($.isPlainObject(tab) || $.isArray(tab)) {\n                    tab = $.isArray(tab) ? tab : [tab];\n                    newTabsCreated = true;\n                    tabs = map(tab, function (value, idx) {\n                        that._appendUrlItem(tab[idx].contentUrl || null);\n                        return $(TabStrip.renderItem({\n                            group: that.tabGroup,\n                            item: extend(value, { index: idx })\n                        }));\n                    });\n                    contents = map(tab, function (value, idx) {\n                        if (typeof value.content == 'string' || value.contentUrl) {\n                            return $(TabStrip.renderContent({ item: extend(value, { index: idx }) }));\n                        }\n                    });\n                } else {\n                    if (typeof tab == 'string' && tab[0] != '<') {\n                        tabs = that.element.find(tab);\n                    } else {\n                        tabs = $(tab);\n                    }\n                    contents = $();\n                    tabs.each(function () {\n                        if (/k-tabstrip-items/.test(this.parentNode.className)) {\n                            var element = that.element.find('[id=\\'' + this.getAttribute('aria-controls') + '\\']');\n                            content = element;\n                        } else {\n                            content = $('<div class=\\'' + CONTENT + '\\'/>');\n                        }\n                        contents = contents.add(content);\n                    });\n                    updateTabClasses(tabs);\n                }\n                return {\n                    tabs: tabs,\n                    contents: contents,\n                    newTabsCreated: newTabsCreated\n                };\n            },\n            _toggleDisabled: function (element, enable) {\n                element = this.tabGroup.find(element);\n                element.each(function () {\n                    $(this).toggleClass(DEFAULTSTATE, enable).toggleClass(DISABLEDSTATE, !enable).attr('aria-disabled', !enable);\n                });\n            },\n            _updateClasses: function () {\n                var that = this, tabs, activeItem, activeTab;\n                that.wrapper.addClass('k-widget k-header k-tabstrip');\n                that.tabGroup = that.wrapper.children('ul').addClass('k-tabstrip-items k-reset');\n                if (!that.tabGroup[0]) {\n                    that.tabGroup = $('<ul class=\\'k-tabstrip-items k-reset\\'/>').appendTo(that.wrapper);\n                }\n                tabs = that.tabGroup.find('li').addClass('k-item');\n                if (tabs.length) {\n                    activeItem = tabs.filter('.' + ACTIVESTATE).index();\n                    activeTab = activeItem >= 0 ? activeItem : undefined;\n                    that.tabGroup.contents().filter(function () {\n                        return this.nodeType == 3 && !trim(this.nodeValue);\n                    }).remove();\n                }\n                if (activeItem >= 0) {\n                    tabs.eq(activeItem).addClass(TABONTOP);\n                }\n                that.contentElements = that.wrapper.children('div');\n                that.contentElements.addClass(CONTENT).eq(activeTab).addClass(ACTIVESTATE).css({ display: 'block' });\n                if (tabs.length) {\n                    updateTabClasses(tabs);\n                    updateFirstLast(that.tabGroup);\n                    that._updateContentElements(true);\n                }\n            },\n            _elementId: function (element, idx) {\n                var elementId = element.attr('id');\n                var wrapperId = this.element.attr('id');\n                if (!elementId || elementId.indexOf(wrapperId + '-') > -1) {\n                    var tabStripID = (wrapperId || kendo.guid()) + '-';\n                    return tabStripID + (idx + 1);\n                }\n                return elementId;\n            },\n            _updateContentElements: function (isInitialUpdate) {\n                var that = this, contentUrls = that._contentUrls, items = that.tabGroup.children('.k-item'), contentElements = that.wrapper.children('div'), _elementId = that._elementId.bind(that);\n                if (contentElements.length && items.length > contentElements.length) {\n                    contentElements.each(function (idx) {\n                        var id = _elementId($(this), idx);\n                        var item = items.filter('[aria-controls=' + (this.id || 0) + ']')[0];\n                        if (!item && isInitialUpdate) {\n                            item = items[idx];\n                        }\n                        if (item) {\n                            item.setAttribute('aria-controls', id);\n                        }\n                        this.setAttribute('id', id);\n                    });\n                } else {\n                    items.each(function (idx) {\n                        var currentContent = contentElements.eq(idx);\n                        var id = _elementId(currentContent, idx);\n                        this.setAttribute('aria-controls', id);\n                        if (!currentContent.length && contentUrls[idx]) {\n                            $('<div class=\\'' + CONTENT + '\\'/>').appendTo(that.wrapper).attr('id', id);\n                        } else {\n                            currentContent.attr('id', id);\n                            if (!$(this).children('.k-loading')[0] && !contentUrls[idx]) {\n                                $('<span class=\\'k-loading k-complete\\'/>').prependTo(this);\n                            }\n                        }\n                        currentContent.attr('role', 'tabpanel');\n                        currentContent.filter(':not(.' + ACTIVESTATE + ')').attr('aria-hidden', true).attr('aria-expanded', false);\n                        currentContent.filter('.' + ACTIVESTATE).attr('aria-expanded', true);\n                    });\n                }\n                that.contentElements = that.contentAnimators = that.wrapper.children('div');\n                that.tabsHeight = outerHeight(that.tabGroup) + parseInt(that.wrapper.css('border-top-width'), 10) + parseInt(that.wrapper.css('border-bottom-width'), 10);\n                if (kendo.kineticScrollNeeded && kendo.mobile.ui.Scroller) {\n                    kendo.touchScroller(that.contentElements);\n                    that.contentElements = that.contentElements.children('.km-scroll-container');\n                }\n            },\n            _wrapper: function () {\n                var that = this;\n                if (that.element.is('ul')) {\n                    that.wrapper = that.element.wrapAll('<div />').parent();\n                } else {\n                    that.wrapper = that.element;\n                }\n                that.scrollWrap = that.wrapper.parent('.k-tabstrip-wrapper');\n                if (!that.scrollWrap[0]) {\n                    that.scrollWrap = that.wrapper.wrapAll('<div class=\\'k-tabstrip-wrapper\\' />').parent();\n                }\n            },\n            _tabPosition: function () {\n                var that = this, tabPosition = that.options.tabPosition;\n                that.wrapper.addClass('k-floatwrap k-tabstrip-' + tabPosition);\n                if (tabPosition == 'bottom') {\n                    that.tabGroup.appendTo(that.wrapper);\n                }\n                that.resize(true);\n            },\n            _setContentElementsDimensions: function () {\n                var that = this, tabPosition = that.options.tabPosition;\n                if (tabPosition == 'left' || tabPosition == 'right') {\n                    var contentDivs = that.wrapper.children('.k-content'), activeDiv = contentDivs.filter(':visible'), marginStyleProperty = 'margin-' + tabPosition, tabGroup = that.tabGroup, margin = outerWidth(tabGroup);\n                    var minHeight = Math.ceil(tabGroup.height()) - parseInt(activeDiv.css('padding-top'), 10) - parseInt(activeDiv.css('padding-bottom'), 10) - parseInt(activeDiv.css('border-top-width'), 10) - parseInt(activeDiv.css('border-bottom-width'), 10);\n                    setTimeout(function () {\n                        contentDivs.css(marginStyleProperty, margin).css('min-height', minHeight);\n                    });\n                }\n            },\n            _resize: function () {\n                this._setContentElementsDimensions();\n                this._scrollable();\n            },\n            _sizeScrollWrap: function (element) {\n                if (element.is(':visible')) {\n                    var tabPosition = this.options.tabPosition;\n                    var h = Math.floor(outerHeight(element, true)) + (tabPosition === 'left' || tabPosition === 'right' ? 2 : this.tabsHeight);\n                    this.scrollWrap.css('height', h).css('height');\n                }\n            },\n            _toggleHover: function (e) {\n                $(e.currentTarget).toggleClass(HOVERSTATE, e.type == MOUSEENTER);\n            },\n            _click: function (item) {\n                var that = this, link = item.find('.' + LINK), href = link.attr(HREF), collapse = that.options.collapsible, index = item.index(), contentHolder = that.contentHolder(index), prevent, isAnchor, neighbours = item.parent().children(), oldFocusedTab = neighbours.filter('.' + FOCUSEDSTATE);\n                if (item.closest('.k-widget')[0] != that.wrapper[0]) {\n                    return;\n                }\n                if (item.is('.' + DISABLEDSTATE + (!collapse ? ',.' + ACTIVESTATE : ''))) {\n                    oldFocusedTab.removeClass(FOCUSEDSTATE);\n                    that._focused = item;\n                    item.addClass(FOCUSEDSTATE);\n                    that._current(item);\n                    if (that._scrollableModeActive) {\n                        that._scrollTabsToItem(item);\n                    }\n                    return true;\n                }\n                isAnchor = link.data(CONTENTURL) || that._contentUrls[index] || href && (href.charAt(href.length - 1) == '#' || href.indexOf('#' + that.element[0].id + '-') != -1);\n                prevent = !href || isAnchor;\n                if (that.tabGroup.children('[data-animating]').length) {\n                    return prevent;\n                }\n                if (that.trigger(SELECT, {\n                        item: item[0],\n                        contentElement: contentHolder[0]\n                    })) {\n                    return true;\n                }\n                if (prevent === false) {\n                    return;\n                }\n                if (collapse && item.is('.' + ACTIVESTATE)) {\n                    that.deactivateTab(item);\n                    return true;\n                }\n                if (that.activateTab(item)) {\n                    prevent = true;\n                }\n                return prevent;\n            },\n            _scrollable: function () {\n                var that = this, options = that.options, wrapperOffsetWidth, tabGroupScrollWidth, scrollPrevButton, scrollNextButton;\n                if (that._scrollableAllowed()) {\n                    that.wrapper.addClass('k-tabstrip-scrollable');\n                    wrapperOffsetWidth = that.wrapper[0].offsetWidth;\n                    tabGroupScrollWidth = that.tabGroup[0].scrollWidth;\n                    if (tabGroupScrollWidth > wrapperOffsetWidth && !that._scrollableModeActive) {\n                        that._nowScrollingTabs = false;\n                        that._isRtl = kendo.support.isRtl(that.element);\n                        var mouseDown = kendo.support.mobileOS ? 'touchstart' : 'mousedown';\n                        var mouseUp = kendo.support.mobileOS ? 'touchend' : 'mouseup';\n                        var browser = kendo.support.browser;\n                        var isRtlScrollDirection = that._isRtl && !browser.msie && !browser.edge;\n                        that.wrapper.append(scrollButtonHtml('prev', 'k-i-arrow-60-left') + scrollButtonHtml('next', 'k-i-arrow-60-right'));\n                        scrollPrevButton = that._scrollPrevButton = that.wrapper.children('.k-tabstrip-prev');\n                        scrollNextButton = that._scrollNextButton = that.wrapper.children('.k-tabstrip-next');\n                        that.tabGroup.css({\n                            marginLeft: outerWidth(scrollPrevButton) + 9,\n                            marginRight: outerWidth(scrollNextButton) + 12\n                        });\n                        scrollPrevButton.on(mouseDown + NS, function () {\n                            that._nowScrollingTabs = true;\n                            that._scrollTabsByDelta(options.scrollable.distance * (isRtlScrollDirection ? 1 : -1));\n                        });\n                        scrollNextButton.on(mouseDown + NS, function () {\n                            that._nowScrollingTabs = true;\n                            that._scrollTabsByDelta(options.scrollable.distance * (isRtlScrollDirection ? -1 : 1));\n                        });\n                        scrollPrevButton.add(scrollNextButton).on(mouseUp + NS, function () {\n                            that._nowScrollingTabs = false;\n                        });\n                        that._scrollableModeActive = true;\n                        that._toggleScrollButtons();\n                    } else if (that._scrollableModeActive && tabGroupScrollWidth <= wrapperOffsetWidth) {\n                        that._scrollableModeActive = false;\n                        that.wrapper.removeClass('k-tabstrip-scrollable');\n                        that._scrollPrevButton.off().remove();\n                        that._scrollNextButton.off().remove();\n                        that.tabGroup.css({\n                            marginLeft: '',\n                            marginRight: ''\n                        });\n                    } else if (!that._scrollableModeActive) {\n                        that.wrapper.removeClass('k-tabstrip-scrollable');\n                    } else {\n                        that._toggleScrollButtons();\n                    }\n                }\n            },\n            _scrollableAllowed: function () {\n                var options = this.options;\n                if (options.scrollable && !options.scrollable.distance) {\n                    options.scrollable = { distance: DEFAULTDISTANCE };\n                }\n                return options.scrollable && !isNaN(options.scrollable.distance) && (options.tabPosition == 'top' || options.tabPosition == 'bottom');\n            },\n            _scrollTabsToItem: function (item) {\n                var that = this, tabGroup = that.tabGroup, currentScrollOffset = tabGroup.scrollLeft(), itemWidth = outerWidth(item), itemOffset = that._isRtl ? item.position().left : item.position().left - tabGroup.children().first().position().left, tabGroupWidth = tabGroup[0].offsetWidth, tabGroupPadding = Math.ceil(parseFloat(tabGroup.css('padding-left'))), itemPosition;\n                if (that._isRtl) {\n                    if (itemOffset < 0) {\n                        itemPosition = currentScrollOffset + itemOffset - (tabGroupWidth - currentScrollOffset) - tabGroupPadding;\n                    } else if (itemOffset + itemWidth > tabGroupWidth) {\n                        itemPosition = currentScrollOffset + itemOffset - itemWidth + tabGroupPadding * 2;\n                    }\n                } else {\n                    if (currentScrollOffset + tabGroupWidth < itemOffset + itemWidth) {\n                        itemPosition = itemOffset + itemWidth - tabGroupWidth + tabGroupPadding * 2;\n                    } else if (currentScrollOffset > itemOffset) {\n                        itemPosition = itemOffset - tabGroupPadding;\n                    }\n                }\n                tabGroup.finish().animate({ 'scrollLeft': itemPosition }, 'fast', 'linear', function () {\n                    that._toggleScrollButtons();\n                });\n            },\n            _scrollTabsByDelta: function (delta) {\n                var that = this;\n                var tabGroup = that.tabGroup;\n                var scrLeft = tabGroup.scrollLeft();\n                tabGroup.finish().animate({ 'scrollLeft': scrLeft + delta }, 'fast', 'linear', function () {\n                    if (that._nowScrollingTabs && !jQuery.fx.off) {\n                        that._scrollTabsByDelta(delta);\n                    } else {\n                        that._toggleScrollButtons();\n                    }\n                });\n            },\n            _toggleScrollButtons: function () {\n                var that = this, ul = that.tabGroup, scrollLeft = kendo.scrollLeft(ul);\n                that._scrollPrevButton.toggle(scrollLeft !== 0);\n                that._scrollNextButton.toggle(scrollLeft < ul[0].scrollWidth - ul[0].offsetWidth - 1);\n            },\n            deactivateTab: function (item) {\n                var that = this, animationSettings = that.options.animation, animation = animationSettings.open, close = extend({}, animationSettings.close), hasCloseAnimation = close && 'effects' in close;\n                item = that.tabGroup.find(item);\n                close = extend(hasCloseAnimation ? close : extend({ reverse: true }, animation), { hide: true });\n                if (kendo.size(animation.effects)) {\n                    item.kendoAddClass(DEFAULTSTATE, { duration: animation.duration });\n                    item.kendoRemoveClass(ACTIVESTATE, { duration: animation.duration });\n                } else {\n                    item.addClass(DEFAULTSTATE);\n                    item.removeClass(ACTIVESTATE);\n                }\n                item.removeAttr('aria-selected');\n                that.contentAnimators.filter('.' + ACTIVESTATE).kendoStop(true, true).kendoAnimate(close).removeClass(ACTIVESTATE).attr('aria-hidden', true);\n            },\n            activateTab: function (item) {\n                if (this.tabGroup.children('[data-animating]').length) {\n                    return;\n                }\n                item = this.tabGroup.find(item);\n                var that = this, animationSettings = that.options.animation, animation = animationSettings.open, close = extend({}, animationSettings.close), hasCloseAnimation = close && 'effects' in close, neighbours = item.parent().children(), oldTab = neighbours.filter('.' + ACTIVESTATE), itemIndex = neighbours.index(item), isAnimationEnabled = animation && 'duration' in animation && 'effects' in animation;\n                close = extend(hasCloseAnimation ? close : extend({ reverse: true }, animation), { hide: true });\n                if (kendo.size(animation.effects)) {\n                    oldTab.kendoRemoveClass(ACTIVESTATE, { duration: close.duration });\n                    item.kendoRemoveClass(HOVERSTATE, { duration: close.duration });\n                } else {\n                    oldTab.removeClass(ACTIVESTATE);\n                    item.removeClass(HOVERSTATE);\n                }\n                var contentAnimators = that.contentAnimators;\n                if (that.inRequest) {\n                    that.xhr.abort();\n                    that.inRequest = false;\n                }\n                if (contentAnimators.length === 0) {\n                    that.tabGroup.find('.' + TABONTOP).removeClass(TABONTOP);\n                    item.addClass(TABONTOP).css('z-index');\n                    item.addClass(ACTIVESTATE);\n                    that._current(item);\n                    that.trigger('change');\n                    if (that._scrollableModeActive) {\n                        that._scrollTabsToItem(item);\n                    }\n                    return false;\n                }\n                var visibleContents = contentAnimators.filter('.' + ACTIVESTATE), contentHolder = that.contentHolder(itemIndex), contentElement = contentHolder.closest('.k-content');\n                that.tabsHeight = outerHeight(that.tabGroup) + parseInt(that.wrapper.css('border-top-width'), 10) + parseInt(that.wrapper.css('border-bottom-width'), 10);\n                that._sizeScrollWrap(visibleContents);\n                if (contentHolder.length === 0) {\n                    visibleContents.removeClass(ACTIVESTATE).attr('aria-hidden', true).kendoStop(true, true).kendoAnimate(close);\n                    return false;\n                }\n                item.attr('data-animating', true);\n                var isAjaxContent = (item.children('.' + LINK).data(CONTENTURL) || that._contentUrls[itemIndex] || false) && contentHolder.is(EMPTY), showContentElement = function () {\n                        oldTab.removeAttr('aria-selected');\n                        item.attr('aria-selected', true);\n                        that._current(item);\n                        that._sizeScrollWrap(contentElement);\n                        contentElement.addClass(ACTIVESTATE).removeAttr('aria-hidden').kendoStop(true, true).attr('aria-expanded', true).kendoAnimate(extend({\n                            init: function () {\n                                that.trigger(SHOW, {\n                                    item: item[0],\n                                    contentElement: contentHolder[0]\n                                });\n                                kendo.resize(contentHolder);\n                            }\n                        }, animation, {\n                            complete: function () {\n                                item.removeAttr('data-animating');\n                                that.trigger(ACTIVATE, {\n                                    item: item[0],\n                                    contentElement: contentHolder[0]\n                                });\n                                kendo.resize(contentHolder);\n                                that.scrollWrap.css('height', '').css('height');\n                                if (isAnimationEnabled && (kendo.support.browser.msie || kendo.support.browser.edge)) {\n                                    contentHolder.finish().animate({ opacity: 0.9 }, 'fast', 'linear', function () {\n                                        contentHolder.finish().animate({ opacity: 1 }, 'fast', 'linear');\n                                    });\n                                }\n                            }\n                        }));\n                    }, showContent = function () {\n                        if (!isAjaxContent) {\n                            showContentElement();\n                            that.trigger('change');\n                        } else {\n                            item.removeAttr('data-animating');\n                            that.ajaxRequest(item, contentHolder, function () {\n                                item.attr('data-animating', true);\n                                showContentElement();\n                                that.trigger('change');\n                            });\n                        }\n                        if (that._scrollableModeActive) {\n                            that._scrollTabsToItem(item);\n                        }\n                    };\n                visibleContents.removeClass(ACTIVESTATE);\n                that.tabGroup.find('.' + TABONTOP).removeClass(TABONTOP);\n                item.addClass(TABONTOP).css('z-index');\n                if (kendo.size(animation.effects)) {\n                    oldTab.kendoAddClass(DEFAULTSTATE, { duration: animation.duration });\n                    item.kendoAddClass(ACTIVESTATE, { duration: animation.duration });\n                } else {\n                    oldTab.addClass(DEFAULTSTATE);\n                    item.addClass(ACTIVESTATE);\n                }\n                visibleContents.attr('aria-hidden', true);\n                visibleContents.attr('aria-expanded', false);\n                if (visibleContents.length) {\n                    visibleContents.kendoStop(true, true).kendoAnimate(extend({ complete: showContent }, close));\n                } else {\n                    showContent();\n                }\n                return true;\n            },\n            contentElement: function (itemIndex) {\n                if (isNaN(itemIndex - 0)) {\n                    return undefined;\n                }\n                var contentElements = this.contentElements && this.contentElements[0] && !kendo.kineticScrollNeeded ? this.contentElements : this.contentAnimators;\n                var id = $(this.tabGroup.children()[itemIndex]).attr('aria-controls');\n                if (contentElements) {\n                    for (var i = 0, len = contentElements.length; i < len; i++) {\n                        if (contentElements.eq(i).closest('.k-content')[0].id == id) {\n                            return contentElements[i];\n                        }\n                    }\n                }\n                return undefined;\n            },\n            contentHolder: function (itemIndex) {\n                var contentElement = $(this.contentElement(itemIndex)), scrollContainer = contentElement.children('.km-scroll-container');\n                return kendo.support.touch && scrollContainer[0] ? scrollContainer : contentElement;\n            },\n            ajaxRequest: function (element, content, complete, url) {\n                element = this.tabGroup.find(element);\n                var that = this, xhr = $.ajaxSettings.xhr, link = element.find('.' + LINK), data = {}, halfWidth = element.width() / 2, fakeProgress = false, statusIcon = element.find('.k-loading').removeClass('k-complete');\n                if (!statusIcon[0]) {\n                    statusIcon = $('<span class=\\'k-loading\\'/>').prependTo(element);\n                }\n                var endState = halfWidth * 2 - statusIcon.width();\n                var oldProgressAnimation = function () {\n                    statusIcon.animate({ marginLeft: (parseInt(statusIcon.css('marginLeft'), 10) || 0) < halfWidth ? endState : 0 }, 500, oldProgressAnimation);\n                };\n                if (kendo.support.browser.msie && kendo.support.browser.version < 10) {\n                    setTimeout(oldProgressAnimation, 40);\n                }\n                url = url || link.data(CONTENTURL) || that._contentUrls[element.index()] || link.attr(HREF);\n                that.inRequest = true;\n                var ajaxOptions = {\n                    type: 'GET',\n                    cache: false,\n                    url: url,\n                    dataType: 'html',\n                    data: data,\n                    xhr: function () {\n                        var current = this, request = xhr(), event = current.progressUpload ? 'progressUpload' : current.progress ? 'progress' : false;\n                        if (request) {\n                            $.each([\n                                request,\n                                request.upload\n                            ], function () {\n                                if (this.addEventListener) {\n                                    this.addEventListener('progress', function (evt) {\n                                        if (event) {\n                                            current[event](evt);\n                                        }\n                                    }, false);\n                                }\n                            });\n                        }\n                        current.noProgress = !(window.XMLHttpRequest && 'upload' in new XMLHttpRequest());\n                        return request;\n                    },\n                    progress: function (evt) {\n                        if (evt.lengthComputable) {\n                            var percent = parseInt(evt.loaded / evt.total * 100, 10) + '%';\n                            statusIcon.stop(true).addClass('k-progress').css({\n                                'width': percent,\n                                'marginLeft': 0\n                            });\n                        }\n                    },\n                    error: function (xhr, status) {\n                        if (that.trigger('error', {\n                                xhr: xhr,\n                                status: status\n                            })) {\n                            this.complete();\n                        }\n                    },\n                    stopProgress: function () {\n                        clearInterval(fakeProgress);\n                        statusIcon.stop(true).addClass('k-progress')[0].style.cssText = '';\n                    },\n                    complete: function (xhr) {\n                        that.inRequest = false;\n                        if (this.noProgress) {\n                            setTimeout(this.stopProgress, 500);\n                        } else {\n                            this.stopProgress();\n                        }\n                        if (xhr.statusText == 'abort') {\n                            statusIcon.remove();\n                        }\n                    },\n                    success: function (data) {\n                        statusIcon.addClass('k-complete');\n                        try {\n                            var current = this, loaded = 10;\n                            if (current.noProgress) {\n                                statusIcon.width(loaded + '%');\n                                fakeProgress = setInterval(function () {\n                                    current.progress({\n                                        lengthComputable: true,\n                                        loaded: Math.min(loaded, 100),\n                                        total: 100\n                                    });\n                                    loaded += 10;\n                                }, 40);\n                            }\n                            that.angular('cleanup', function () {\n                                return { elements: content.get() };\n                            });\n                            kendo.destroy(content);\n                            content.html(data);\n                        } catch (e) {\n                            var console = window.console;\n                            if (console && console.error) {\n                                console.error(e.name + ': ' + e.message + ' in ' + url);\n                            }\n                            this.error(this.xhr, 'error');\n                        }\n                        if (complete) {\n                            complete.call(that, content);\n                        }\n                        that.angular('compile', function () {\n                            return { elements: content.get() };\n                        });\n                        that.trigger(CONTENTLOAD, {\n                            item: element[0],\n                            contentElement: content[0]\n                        });\n                    }\n                };\n                if (typeof url === 'object') {\n                    ajaxOptions = $.extend(true, {}, ajaxOptions, url);\n                    if (isFunction(ajaxOptions.url)) {\n                        ajaxOptions.url = ajaxOptions.url();\n                    }\n                }\n                that.xhr = $.ajax(ajaxOptions);\n            }\n        });\n        extend(TabStrip, {\n            renderItem: function (options) {\n                options = extend({\n                    tabStrip: {},\n                    group: {}\n                }, options);\n                var empty = templates.empty, item = options.item;\n                return templates.item(extend(options, {\n                    image: item.imageUrl ? templates.image : empty,\n                    sprite: item.spriteCssClass ? templates.sprite : empty,\n                    itemWrapper: templates.itemWrapper\n                }, rendering));\n            },\n            renderContent: function (options) {\n                return templates.content(extend(options, rendering));\n            }\n        });\n        kendo.ui.plugin(TabStrip);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}