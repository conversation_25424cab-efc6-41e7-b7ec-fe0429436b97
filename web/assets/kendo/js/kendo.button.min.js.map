{"version": 3, "sources": ["kendo.button.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "Widget", "ui", "proxy", "keys", "CLICK", "MOUSEDOWN", "support", "mousedown", "MOUSEUP", "mouseup", "KBUTTON", "KBUTTONICON", "KBUTTONICONTEXT", "NS", "DISABLED", "DISABLEDSTATE", "FOCUSEDSTATE", "SELECTEDSTATE", "<PERSON><PERSON>", "extend", "init", "element", "options", "that", "this", "fn", "call", "wrapper", "addClass", "attr", "enable", "_tabindex", "iconElement", "on", "_click", "_focus", "_blur", "_keydown", "_removeActive", "_addActive", "notify", "destroy", "off", "events", "name", "icon", "iconClass", "spriteCssClass", "imageUrl", "_isNativeButton", "prop", "toLowerCase", "e", "trigger", "event", "preventDefault", "removeClass", "setTimeout", "keyCode", "ENTER", "SPACEBAR", "span", "img", "isEmpty", "contents", "filter", "hasClass", "each", "idx", "el", "nodeType", "trim", "nodeValue", "length", "children", "first", "prependTo", "toggleClass", "blur", "err", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,gBAAiB,cAAeD,IACzC,WA0IE,MAlIC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAASF,EAAMG,GAAGD,OAAQE,EAAQN,EAAEM,MAAOC,EAAOL,EAAMK,KAAMC,EAAQ,QAASC,EAAYP,EAAMQ,QAAQC,UAAWC,EAAUV,EAAMQ,QAAQG,QAASC,EAAU,WAAYC,EAAc,gBAAiBC,EAAkB,oBAAqBC,EAAK,eAAgBC,EAAW,WAAYC,EAAgB,mBAAoBC,EAAe,kBAAmBC,EAAgB,iBACzYC,EAASlB,EAAOmB,QAChBC,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOC,IACXxB,GAAOyB,GAAGL,KAAKM,KAAKH,EAAMF,EAASC,GACnCD,EAAUE,EAAKI,QAAUJ,EAAKF,QAC9BC,EAAUC,EAAKD,QACfD,EAAQO,SAASlB,GAASmB,KAAK,OAAQ,UACvCP,EAAQQ,OAASR,EAAQQ,SAAWT,EAAQQ,KAAKf,GACjDS,EAAKO,OAAOR,EAAQQ,QAChBR,EAAQQ,QACRP,EAAKQ,YAETR,EAAKS,cACLX,EAAQY,GAAG7B,EAAQS,EAAIX,EAAMqB,EAAKW,OAAQX,IAAOU,GAAG,QAAUpB,EAAIX,EAAMqB,EAAKY,OAAQZ,IAAOU,GAAG,OAASpB,EAAIX,EAAMqB,EAAKa,MAAOb,IAAOU,GAAG,UAAYpB,EAAIX,EAAMqB,EAAKc,SAAUd,IAAOU,GAAG,QAAUpB,EAAIX,EAAMqB,EAAKe,cAAef,IAAOU,GAAG5B,EAAYQ,EAAIX,EAAMqB,EAAKgB,WAAYhB,IAAOU,GAAGzB,EAAUK,EAAIX,EAAMqB,EAAKe,cAAef,IAClUzB,EAAM0C,OAAOjB,IAEjBkB,QAAS,WACL,GAAIlB,GAAOC,IACXD,GAAKI,QAAQe,IAAI7B,GACjBb,EAAOyB,GAAGgB,QAAQf,KAAKH,IAE3BoB,QAASvC,GACTkB,SACIsB,KAAM,SACNC,KAAM,GACNC,UAAW,GACXC,eAAgB,GAChBC,SAAU,GACVlB,QAAQ,GAEZmB,gBAAiB,WACb,MAAqD,UAA9CzB,KAAKH,QAAQ6B,KAAK,WAAWC,eAExCjB,OAAQ,SAAUkB,GACV5B,KAAKF,QAAQQ,QACTN,KAAK6B,QAAQjD,GAASkD,MAAOF,KAC7BA,EAAEG,kBAIdpB,OAAQ,WACAX,KAAKF,QAAQQ,QACbN,KAAKH,QAAQO,SAASZ,IAG9BoB,MAAO,WACH,GAAIb,GAAOC,IACXD,GAAKF,QAAQmC,YAAYxC,GACzByC,WAAW,WACPlC,EAAKF,QAAQmC,YAAYvC,MAGjCoB,SAAU,SAAUe,GAChB,GAAI7B,GAAOC,IACP4B,GAAEM,SAAWvD,EAAKwD,OAASP,EAAEM,SAAWvD,EAAKyD,WAC7CrC,EAAKgB,aACAhB,EAAK0B,oBACFG,EAAEM,SAAWvD,EAAKyD,UAClBR,EAAEG,iBAENhC,EAAKW,OAAOkB,MAIxBd,cAAe,WACXd,KAAKH,QAAQmC,YAAYvC,IAE7BsB,WAAY,WACJf,KAAKF,QAAQQ,QACbN,KAAKH,QAAQO,SAASX,IAG9Be,YAAa,WACT,GAA2L6B,GAAMC,EAAKC,EAAlMxC,EAAOC,KAAMH,EAAUE,EAAKF,QAASC,EAAUC,EAAKD,QAASuB,EAAOvB,EAAQuB,KAAMC,EAAYxB,EAAQwB,UAAWC,EAAiBzB,EAAQyB,eAAgBC,EAAW1B,EAAQ0B,UAC7KD,GAAkBC,GAAYH,GAAQC,KACtCiB,GAAU,EACV1C,EAAQ2C,WAAWC,OAAO,WACtB,OAAQrE,EAAE4B,MAAM0C,SAAS,cAAgBtE,EAAE4B,MAAM0C,SAAS,YAActE,EAAE4B,MAAM0C,SAAS,aAC1FC,KAAK,SAAUC,EAAKC,IACA,GAAfA,EAAGC,UAAgC,GAAfD,EAAGC,UAAiB1E,EAAE2E,KAAKF,EAAGG,WAAWC,OAAS,KACtEV,GAAU,KAId1C,EAAQO,SADRmC,EACiBpD,EAEAC,IAGrBoC,GACAc,EAAMzC,EAAQqD,SAAS,eAAeC,QACjCb,EAAI,KACLA,EAAMlE,EAAE,sCAAsCgF,UAAUvD,IAE5DyC,EAAIjC,KAAK,MAAOmB,IACTH,GAAQC,GACfe,EAAOxC,EAAQqD,SAAS,eAAeC,QAClCd,EAAK,KACNA,EAAOjE,EAAE,iBAAiBgF,UAAUvD,IAExCwC,EAAKhC,KAAK,QAASgB,EAAO,cAAgBA,EAAOC,IAC1CC,IACPc,EAAOxC,EAAQqD,SAAS,iBAAiBC,QACpCd,EAAK,KACNA,EAAOjE,EAAE,kCAAkCgF,UAAUvD,IAEzDwC,EAAKjC,SAASmB,KAGtBjB,OAAQ,SAAUA,GACd,GAAIP,GAAOC,KAAMH,EAAUE,EAAKF,OAC5BS,KAAWjC,IACXiC,GAAS,GAEbA,IAAWA,EACXP,EAAKD,QAAQQ,OAASA,EACtBT,EAAQwD,YAAY9D,GAAgBe,GAAQD,KAAK,iBAAkBC,GAAQD,KAAKf,GAAWgB,GACvFA,GACAP,EAAKQ,WAET,KACIV,EAAQyD,OACV,MAAOC,OAIjBjF,GAAMG,GAAG+E,OAAO9D,IAClBnB,OAAOD,MAAMmF,QACRlF,OAAOD,OACE,kBAAVH,SAAwBA,OAAOuF,IAAMvF,OAAS,SAAUwF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.button.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.button', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'button',\n        name: 'Button',\n        category: 'web',\n        description: 'The Button widget displays styled buttons.',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, proxy = $.proxy, keys = kendo.keys, CLICK = 'click', MOUSEDOWN = kendo.support.mousedown, MOUSEUP = kendo.support.mouseup, KBUTTON = 'k-button', KBUTTONICON = 'k-button-icon', KBUTTONICONTEXT = 'k-button-icontext', NS = '.kendoButton', DISABLED = 'disabled', DISABLEDSTATE = 'k-state-disabled', FOCUSEDSTATE = 'k-state-focused', SELECTEDSTATE = 'k-state-active';\n        var Button = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                element = that.wrapper = that.element;\n                options = that.options;\n                element.addClass(KBUTTON).attr('role', 'button');\n                options.enable = options.enable && !element.attr(DISABLED);\n                that.enable(options.enable);\n                if (options.enable) {\n                    that._tabindex();\n                }\n                that.iconElement();\n                element.on(CLICK + NS, proxy(that._click, that)).on('focus' + NS, proxy(that._focus, that)).on('blur' + NS, proxy(that._blur, that)).on('keydown' + NS, proxy(that._keydown, that)).on('keyup' + NS, proxy(that._removeActive, that)).on(MOUSEDOWN + NS, proxy(that._addActive, that)).on(MOUSEUP + NS, proxy(that._removeActive, that));\n                kendo.notify(that);\n            },\n            destroy: function () {\n                var that = this;\n                that.wrapper.off(NS);\n                Widget.fn.destroy.call(that);\n            },\n            events: [CLICK],\n            options: {\n                name: 'Button',\n                icon: '',\n                iconClass: '',\n                spriteCssClass: '',\n                imageUrl: '',\n                enable: true\n            },\n            _isNativeButton: function () {\n                return this.element.prop('tagName').toLowerCase() == 'button';\n            },\n            _click: function (e) {\n                if (this.options.enable) {\n                    if (this.trigger(CLICK, { event: e })) {\n                        e.preventDefault();\n                    }\n                }\n            },\n            _focus: function () {\n                if (this.options.enable) {\n                    this.element.addClass(FOCUSEDSTATE);\n                }\n            },\n            _blur: function () {\n                var that = this;\n                that.element.removeClass(FOCUSEDSTATE);\n                setTimeout(function () {\n                    that.element.removeClass(SELECTEDSTATE);\n                });\n            },\n            _keydown: function (e) {\n                var that = this;\n                if (e.keyCode == keys.ENTER || e.keyCode == keys.SPACEBAR) {\n                    that._addActive();\n                    if (!that._isNativeButton()) {\n                        if (e.keyCode == keys.SPACEBAR) {\n                            e.preventDefault();\n                        }\n                        that._click(e);\n                    }\n                }\n            },\n            _removeActive: function () {\n                this.element.removeClass(SELECTEDSTATE);\n            },\n            _addActive: function () {\n                if (this.options.enable) {\n                    this.element.addClass(SELECTEDSTATE);\n                }\n            },\n            iconElement: function () {\n                var that = this, element = that.element, options = that.options, icon = options.icon, iconClass = options.iconClass, spriteCssClass = options.spriteCssClass, imageUrl = options.imageUrl, span, img, isEmpty;\n                if (spriteCssClass || imageUrl || icon || iconClass) {\n                    isEmpty = true;\n                    element.contents().filter(function () {\n                        return !$(this).hasClass('k-sprite') && !$(this).hasClass('k-icon') && !$(this).hasClass('k-image');\n                    }).each(function (idx, el) {\n                        if (el.nodeType == 1 || el.nodeType == 3 && $.trim(el.nodeValue).length > 0) {\n                            isEmpty = false;\n                        }\n                    });\n                    if (isEmpty) {\n                        element.addClass(KBUTTONICON);\n                    } else {\n                        element.addClass(KBUTTONICONTEXT);\n                    }\n                }\n                if (imageUrl) {\n                    img = element.children('img.k-image').first();\n                    if (!img[0]) {\n                        img = $('<img alt=\"icon\" class=\"k-image\" />').prependTo(element);\n                    }\n                    img.attr('src', imageUrl);\n                } else if (icon || iconClass) {\n                    span = element.children('span.k-icon').first();\n                    if (!span[0]) {\n                        span = $('<span></span>').prependTo(element);\n                    }\n                    span.attr('class', icon ? 'k-icon k-i-' + icon : iconClass);\n                } else if (spriteCssClass) {\n                    span = element.children('span.k-sprite').first();\n                    if (!span[0]) {\n                        span = $('<span class=\"k-sprite\"></span>').prependTo(element);\n                    }\n                    span.addClass(spriteCssClass);\n                }\n            },\n            enable: function (enable) {\n                var that = this, element = that.element;\n                if (enable === undefined) {\n                    enable = true;\n                }\n                enable = !!enable;\n                that.options.enable = enable;\n                element.toggleClass(DISABLEDSTATE, !enable).attr('aria-disabled', !enable).attr(DISABLED, !enable);\n                if (enable) {\n                    that._tabindex();\n                }\n                try {\n                    element.blur();\n                } catch (err) {\n                }\n            }\n        });\n        kendo.ui.plugin(Button);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}