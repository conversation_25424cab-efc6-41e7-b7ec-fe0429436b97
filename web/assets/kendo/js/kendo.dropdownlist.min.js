/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.dropdownlist.min",["kendo.list.min","kendo.mobile.scroller.min","kendo.virtuallist.min"],e)}(function(){return function(e,t){function i(e,t,i){for(var n,s=0,o=t.length-1;s<o;++s)n=t[s],n in e||(e[n]={}),e=e[n];e[t[o]]=i}function n(e,t){return e>=t&&(e-=t),e}function s(e,t){for(var i=0;i<e.length;i++)if(e.charAt(i)!==t)return!1;return!0}var o=window.kendo,a=o.ui,l=a.List,r=a.Select,p=o.support,u=o._activeElement,c=o.data.ObservableObject,d=o.keys,f=".kendoDropDownList",_=f+"FocusEvent",h="disabled",m="readonly",b="change",v="k-state-focused",w="k-state-default",I="k-state-disabled",g="aria-disabled",x="click"+f+" touchend"+f,L="mouseenter"+f+" mouseleave"+f,k="tabindex",y="filter",T="accept",V="The `optionLabel` option is not valid due to missing fields. Define a custom optionLabel as shown here http://docs.telerik.com/kendo-ui/api/javascript/ui/dropdownlist#configuration-optionLabel",C=e.proxy,O=r.extend({init:function(i,n){var s,a,l,p=this,u=n&&n.index;p.ns=f,n=e.isArray(n)?{dataSource:n}:n,r.fn.init.call(p,i,n),n=p.options,i=p.element.on("focus"+f,C(p._focusHandler,p)),p._focusInputHandler=e.proxy(p._focusInput,p),p.optionLabel=e(),p._optionLabel(),p._inputTemplate(),p._reset(),p._prev="",p._word="",p._wrapper(),p._tabindex(),p.wrapper.data(k,p.wrapper.attr(k)),p._span(),p._popup(),p._mobile(),p._dataSource(),p._ignoreCase(),p._filterHeader(),p._aria(),p.wrapper.attr("aria-live","polite"),p._enable(),p._attachFocusHandlers(),p._oldIndex=p.selectedIndex=-1,u!==t&&(n.index=u),p._initialIndex=n.index,p.requireValueMapper(p.options),p._initList(),p._cascade(),p.one("set",function(e){!e.sender.listView.bound()&&p.hasOptionLabel()&&p._textAccessor(p._optionLabelText())}),n.autoBind?p.dataSource.fetch():p.selectedIndex===-1&&(a=n.text||"",a||(s=n.optionLabel,s&&0===n.index?a=s:p._isSelect&&(a=i.children(":selected").text())),p._textAccessor(a)),l=e(p.element).parents("fieldset").is(":disabled"),l&&p.enable(!1),p.listView.bind("click",function(e){e.preventDefault()}),o.notify(p)},options:{name:"DropDownList",enabled:!0,autoBind:!0,index:0,text:null,value:null,delay:500,height:200,dataTextField:"",dataValueField:"",optionLabel:"",cascadeFrom:"",cascadeFromField:"",cascadeFromParentField:"",ignoreCase:!0,animation:{},filter:"none",minLength:1,enforceMinLength:!1,virtual:!1,template:null,valueTemplate:null,optionLabelTemplate:null,groupTemplate:"#:data#",fixedGroupTemplate:"#:data#",autoWidth:!1,popup:null},events:["open","close",b,"select","filtering","dataBinding","dataBound","cascade","set"],setOptions:function(e){r.fn.setOptions.call(this,e),this.listView.setOptions(this._listOptions(e)),this._optionLabel(),this._inputTemplate(),this._accessors(),this._filterHeader(),this._enable(),this._aria(),!this.value()&&this.hasOptionLabel()&&this.select(0)},destroy:function(){var e=this;r.fn.destroy.call(e),e.wrapper.off(f),e.wrapper.off(_),e.element.off(f),e._inputWrapper.off(f),e._arrow.off(),e._arrow=null,e._arrowIcon=null,e.optionLabel.off(),e.filterInput&&e.filterInput.off(_)},open:function(){var e=this,t=!!e.dataSource.filter()&&e.dataSource.filter().filters.length>0;e.popup.visible()||(e.listView.bound()&&e._state!==T?e._allowOpening()&&(e._focusFilter=!0,e.popup.one("activate",e._focusInputHandler),e.popup._hovered=!0,e.popup.open(),e._resizeFilterInput(),e._focusItem()):(e._open=!0,e._state="rebind",e.filterInput&&(e.filterInput.val(""),e._prev=""),e.filterInput&&1!==e.options.minLength&&!t?(e.refresh(),e.popup.one("activate",e._focusInputHandler),e.popup.open(),e._resizeFilterInput()):e._filterSource()))},_focusInput:function(){this._focusElement(this.filterInput)},_resizeFilterInput:function(){var e,t,i=this.filterInput,n=this._prevent;i&&(e=this.filterInput[0]===u(),t=o.caret(this.filterInput[0])[0],this._prevent=!0,i.css("display","none").css("width",this.popup.element.css("width")).css("display","inline-block"),e&&(i.focus(),o.caret(i[0],t)),this._prevent=n)},_allowOpening:function(){return this.hasOptionLabel()||this.filterInput||r.fn._allowOpening.call(this)},toggle:function(e){this._toggle(e,!0)},current:function(e){var i;return e===t?(i=this.listView.focus(),!i&&0===this.selectedIndex&&this.hasOptionLabel()?this.optionLabel:i):(this._focus(e),t)},dataItem:function(i){var n=this,s=null;if(null===i)return i;if(i===t)s=n.listView.selectedDataItems()[0];else{if("number"!=typeof i){if(n.options.virtual)return n.dataSource.getByUid(e(i).data("uid"));i=i.hasClass("k-list-optionlabel")?-1:e(n.items()).index(i)}else n.hasOptionLabel()&&(i-=1);s=n.dataSource.flatView()[i]}return s||(s=n._optionLabelDataItem()),s},refresh:function(){this.listView.refresh()},text:function(e){var i,n=this,s=n.options.ignoreCase;return e=null===e?"":e,e===t?n._textAccessor():"string"!=typeof e?(n._textAccessor(e),t):(i=s?e.toLowerCase():e,n._select(function(e){return e=n._text(e),s&&(e=(e+"").toLowerCase()),e===i}).done(function(){n._textAccessor(n.dataItem()||e)}),t)},_clearFilter:function(){e(this.filterInput).val(""),r.fn._clearFilter.call(this)},value:function(e){var i=this,n=i.listView,s=i.dataSource;return e===t?(e=i._accessor()||i.listView.value()[0],e===t||null===e?"":e):(i.requireValueMapper(i.options,e),!e&&i.hasOptionLabel()||(i._initialIndex=null),this.trigger("set",{value:e}),i._request&&i.options.cascadeFrom&&i.listView.bound()?(i._valueSetter&&s.unbind(b,i._valueSetter),i._valueSetter=C(function(){i.value(e)},i),s.one(b,i._valueSetter),t):(i._isFilterEnabled()&&n.bound()&&n.isFiltered()?i._clearFilter():i._fetchData(),n.value(e).done(function(){i._old=i._valueBeforeCascade=i._accessor(),i._oldIndex=i.selectedIndex}),t))},hasOptionLabel:function(){return this.optionLabel&&!!this.optionLabel[0]},_optionLabel:function(){var i=this,n=i.options,s=n.optionLabel,a=n.optionLabelTemplate;return s?(a||(a="#:",a+="string"==typeof s?"data":o.expr(n.dataTextField,"data"),a+="#"),"function"!=typeof a&&(a=o.template(a)),i.optionLabelTemplate=a,i.hasOptionLabel()||(i.optionLabel=e('<div class="k-list-optionlabel"></div>').prependTo(i.list)),i.optionLabel.html(a(s)).off().on(x,C(i._click,i)).on(L,i._toggleHover),i.angular("compile",function(){return{elements:i.optionLabel,data:[{dataItem:i._optionLabelDataItem()}]}}),t):(i.optionLabel.off().remove(),i.optionLabel=e(),t)},_optionLabelText:function(){var e=this.options.optionLabel;return"string"==typeof e?e:this._text(e)},_optionLabelDataItem:function(){var i=this,n=i.options.optionLabel;return i.hasOptionLabel()?e.isPlainObject(n)?new c(n):i._assignInstance(i._optionLabelText(),""):t},_buildOptions:function(e){var i,n,s,o=this;o._isSelect&&(i=o.listView.value()[0],n=o._optionLabelDataItem(),s=n&&o._value(n),i!==t&&null!==i||(i=""),n&&(s!==t&&null!==s||(s=""),n='<option value="'+s+'">'+o._text(n)+"</option>"),o._options(e,n,i),i!==l.unifyType(o._accessor(),typeof i)&&(o._customOption=null,o._custom(i)))},_listBound:function(){var e,t=this,i=t._initialIndex,n=t._state===y,s=t.dataSource.flatView();t._presetValue=!1,t._renderFooter(),t._renderNoData(),t._toggleNoData(!s.length),t._resizePopup(!0),t.popup.position(),t._buildOptions(s),t._makeUnselectable(),n||(t._open&&t.toggle(t._allowOpening()),t._open=!1,t._fetch||(s.length?(!t.listView.value().length&&i>-1&&null!==i&&t.select(i),t._initialIndex=null,e=t.listView.selectedDataItems()[0],e&&t.text()!==t._text(e)&&t._selectValue(e)):t._textAccessor()!==t._optionLabelText()&&(t.listView.value(""),t._selectValue(null),t._oldIndex=t.selectedIndex))),t._hideBusy(),t.trigger("dataBound")},_listChange:function(){this._selectValue(this.listView.selectedDataItems()[0]),(this._presetValue||this._old&&this._oldIndex===-1)&&(this._oldIndex=this.selectedIndex)},_filterPaste:function(){this._search()},_attachFocusHandlers:function(){var e=this,t=e.wrapper;t.on("focusin"+_,C(e._focusinHandler,e)).on("focusout"+_,C(e._focusoutHandler,e)),e.filterInput&&e.filterInput.on("focusin"+_,C(e._focusinHandler,e)).on("focusout"+_,C(e._focusoutHandler,e))},_focusHandler:function(){this.wrapper.focus()},_focusinHandler:function(){this._inputWrapper.addClass(v),this._prevent=!1},_focusoutHandler:function(){var e=this,t=window.self!==window.top;e._prevent||(clearTimeout(e._typingTimeout),p.mobileOS.ios&&t?e._change():e._blur(),e._inputWrapper.removeClass(v),e._prevent=!0,e._open=!1,e.element.blur())},_wrapperMousedown:function(){this._prevent=!!this.filterInput},_wrapperClick:function(e){e.preventDefault(),this.popup.unbind("activate",this._focusInputHandler),this._focused=this.wrapper,this._prevent=!1,this._toggle()},_editable:function(e){var t=this,i=t.element,n=e.disable,s=e.readonly,a=t.wrapper.add(t.filterInput).off(f),l=t._inputWrapper.off(L);s||n?n?(a.removeAttr(k),l.addClass(I).removeClass(w)):l.addClass(w).removeClass(I):(i.removeAttr(h).removeAttr(m),l.addClass(w).removeClass(I).on(L,t._toggleHover),a.attr(k,a.data(k)).attr(g,!1).on("keydown"+f,C(t._keydown,t)).on(o.support.mousedown+f,C(t._wrapperMousedown,t)).on("paste"+f,C(t._filterPaste,t)),t.wrapper.on("click"+f,C(t._wrapperClick,t)),t.filterInput?a.on("input"+f,C(t._search,t)):a.on("keypress"+f,C(t._keypress,t))),i.attr(h,n).attr(m,s),a.attr(g,n)},_keydown:function(e){var i,n,s,o=this,a=e.keyCode,l=e.altKey,r=o.popup.visible();if(o.filterInput&&(i=o.filterInput[0]===u()),a===d.LEFT?(a=d.UP,n=!0):a===d.RIGHT&&(a=d.DOWN,n=!0),!n||!i){if(e.keyCode=a,(l&&a===d.UP||a===d.ESC)&&o._focusElement(o.wrapper),o._state===y&&a===d.ESC&&(o._clearFilter(),o._open=!1,o._state=T),a===d.ENTER&&o._typingTimeout&&o.filterInput&&r)return e.preventDefault(),t;if(a!==d.SPACEBAR||i||(o.toggle(!r),e.preventDefault()),n=o._move(e),!n){if((!r||!o.filterInput)&&(s=o._focus(),a===d.HOME?(n=!0,o._firstItem()):a===d.END&&(n=!0,o._lastItem()),n)){if(o.trigger("select",{dataItem:o._getElementDataItem(o._focus()),item:o._focus()}))return o._focus(s),t;o._select(o._focus(),!0).done(function(){r||o._blur()}),e.preventDefault()}l||n||!o.filterInput||o._search()}}},_matchText:function(e,i){var n=this.options.ignoreCase;return e!==t&&null!==e&&(e+="",n&&(e=e.toLowerCase()),0===e.indexOf(i))},_shuffleData:function(e,t){var i=this._optionLabelDataItem();return i&&(e=[i].concat(e)),e.slice(t).concat(e.slice(0,t))},_selectNext:function(){var e,t,i,o=this,a=o.dataSource.flatView(),l=a.length+(o.hasOptionLabel()?1:0),r=s(o._word,o._last),p=o.selectedIndex;for(p===-1?p=0:(p+=r?1:0,p=n(p,l)),a=a.toJSON?a.toJSON():a.slice(),a=o._shuffleData(a,p),i=0;i<l&&(t=o._text(a[i]),!r||!o._matchText(t,o._last))&&!o._matchText(t,o._word);i++);i!==l&&(e=o._focus(),o._select(n(p+i,l)).done(function(){var t=function(){o.popup.visible()||o._change()};o.trigger("select",{dataItem:o._getElementDataItem(o._focus()),item:o._focus()})?o._select(e).done(t):t()}))},_keypress:function(e){var t,i=this;0!==e.which&&e.keyCode!==o.keys.ENTER&&(t=String.fromCharCode(e.charCode||e.keyCode),i.options.ignoreCase&&(t=t.toLowerCase())," "===t&&e.preventDefault(),i._word+=t,i._last=t,i._search())},_popupOpen:function(){var e=this.popup;e.wrapper=o.wrap(e.element),e.element.closest(".km-root")[0]&&(e.wrapper.addClass("km-popup km-widget"),this.wrapper.addClass("km-widget"))},_popup:function(){r.fn._popup.call(this),this.popup.one("open",C(this._popupOpen,this))},_getElementDataItem:function(e){return e&&e[0]?e[0]===this.optionLabel[0]?this._optionLabelDataItem():this.listView.dataItemByIndex(this.listView.getElementIndex(e)):null},_click:function(i){var n=this,s=i.item||e(i.currentTarget);return i.preventDefault(),n.trigger("select",{dataItem:n._getElementDataItem(s),item:s})?(n.close(),t):(n._userTriggered=!0,n._select(s).done(function(){n._focusElement(n.wrapper),n._blur()}),t)},_focusElement:function(e){var t=u(),i=this.wrapper,n=this.filterInput,s=e===n?i:n,o=p.mobileOS&&(p.touch||p.MSPointers||p.pointers);n&&n[0]===e[0]&&o||n&&(s[0]===t||this._focusFilter)&&(this._focusFilter=!1,this._prevent=!0,this._focused=e.focus())},_searchByWord:function(e){var t,i;e&&(t=this,i=t.options.ignoreCase,i&&(e=e.toLowerCase()),t._select(function(i){return t._matchText(t._text(i),e)}))},_inputValue:function(){return this.text()},_search:function(){var e=this,i=e.dataSource;if(clearTimeout(e._typingTimeout),e._isFilterEnabled())e._typingTimeout=setTimeout(function(){var t=e.filterInput.val();e._prev!==t&&(e._prev=t,e.search(t),e._resizeFilterInput()),e._typingTimeout=null},e.options.delay);else{if(e._typingTimeout=setTimeout(function(){e._word=""},e.options.delay),!e.listView.bound())return i.fetch().done(function(){e._selectNext()}),t;e._selectNext()}},_get:function(t){var i,n,s,o="function"==typeof t,a=o?e():e(t);if(this.hasOptionLabel()&&("number"==typeof t?t>-1&&(t-=1):a.hasClass("k-list-optionlabel")&&(t=-1)),o){for(i=this.dataSource.flatView(),s=0;s<i.length;s++)if(t(i[s])){t=s,n=!0;break}n||(t=-1)}return t},_firstItem:function(){this.hasOptionLabel()?this._focus(this.optionLabel):this.listView.focusFirst()},_lastItem:function(){this._resetOptionLabel(),this.listView.focusLast()},_nextItem:function(){this.optionLabel.hasClass("k-state-focused")?(this._resetOptionLabel(),this.listView.focusFirst()):this.listView.focusNext()},_prevItem:function(){this.optionLabel.hasClass("k-state-focused")||(this.listView.focusPrev(),this.listView.focus()||this._focus(this.optionLabel))},_focusItem:function(){var e=this.options,i=this.listView,n=i.focus(),s=i.select();s=s[s.length-1],s===t&&e.highlightFirst&&!n&&(s=0),s!==t?i.focus(s):!e.optionLabel||e.virtual&&"dataItem"===e.virtual.mapValueTo?i.scrollToIndex(0):(this._focus(this.optionLabel),this._select(this.optionLabel),this.listView.content.scrollTop(0))},_resetOptionLabel:function(e){this.optionLabel.removeClass("k-state-focused"+(e||"")).removeAttr("id")},_focus:function(e){var i=this.listView,n=this.optionLabel;return e===t?(e=i.focus(),!e&&n.hasClass("k-state-focused")&&(e=n),e):(this._resetOptionLabel(),e=this._get(e),i.focus(e),e===-1&&(n.addClass("k-state-focused").attr("id",i._optionID),this._focused.add(this.filterInput).removeAttr("aria-activedescendant").attr("aria-activedescendant",i._optionID)),t)},_select:function(e,t){var i=this;return e=i._get(e),i.listView.select(e).done(function(){t||i._state!==y||(i._state=T),e===-1&&i._selectValue(null)})},_selectValue:function(e){var i=this,n=i.options.optionLabel,s=i.listView.select(),o="",a="";s=s[s.length-1],s===t&&(s=-1),this._resetOptionLabel(" k-state-selected"),e||0===e?(a=e,o=i._dataValue(e),n&&(s+=1)):n&&(i._focus(i.optionLabel.addClass("k-state-selected")),a=i._optionLabelText(),o="string"==typeof n?"":i._value(n),s=0),i.selectedIndex=s,null===o&&(o=""),i._textAccessor(a),i._accessor(o,s),i._triggerCascade()},_mobile:function(){var e=this,t=e.popup,i=p.mobileOS,n=t.element.parents(".km-root").eq(0);n.length&&i&&(t.options.animation.open.effects=i.android||i.meego?"fadeIn":i.ios||i.wp?"slideIn:up":t.options.animation.open.effects)},_filterHeader:function(){var t;this.filterInput&&(this.filterInput.off(f).parent().remove(),this.filterInput=null),this._isFilterEnabled()&&(t='<span class="k-icon k-i-zoom"></span>',this.filterInput=e('<input class="k-textbox"/>').attr({placeholder:this.element.attr("placeholder"),title:this.element.attr("title"),role:"listbox","aria-haspopup":!0,"aria-expanded":!1}),this.list.prepend(e('<span class="k-list-filter" />').append(this.filterInput.add(t))))},_span:function(){var t,i=this,n=i.wrapper,s="span.k-input";t=n.find(s),t[0]||(n.append('<span unselectable="on" class="k-dropdown-wrap k-state-default"><span unselectable="on" class="k-input">&nbsp;</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span>').append(i.element),t=n.find(s)),i.span=t,i._inputWrapper=e(n[0].firstChild),i._arrow=n.find(".k-select"),i._arrowIcon=i._arrow.find(".k-icon")},_wrapper:function(){var e,t=this,i=t.element,n=i[0];e=i.parent(),e.is("span.k-widget")||(e=i.wrap("<span />").parent(),e[0].style.cssText=n.style.cssText,e[0].title=n.title),t._focused=t.wrapper=e.addClass("k-widget k-dropdown").addClass(n.className).css("display","").attr({accesskey:i.attr("accesskey"),unselectable:"on",role:"listbox","aria-haspopup":!0,"aria-expanded":!1}),i.hide().removeAttr("accesskey")},_clearSelection:function(e){this.select(e.value()?0:-1)},_inputTemplate:function(){var t=this,i=t.options.valueTemplate;if(i=i?o.template(i):e.proxy(o.template("#:this._text(data)#",{useWithBlock:!1}),t),t.valueTemplate=i,t.hasOptionLabel()&&!t.options.optionLabelTemplate)try{t.valueTemplate(t._optionLabelDataItem())}catch(n){throw Error(V)}},_textAccessor:function(i){var n,s=null,o=this.valueTemplate,a=this._optionLabelText(),l=this.span;if(i===t)return l.text();e.isPlainObject(i)||i instanceof c?s=i:a&&a===i&&(s=this.options.optionLabel),s||(s=this._assignInstance(i,this._accessor())),this.hasOptionLabel()&&(s!==a&&this._text(s)!==a||(o=this.optionLabelTemplate,"string"!=typeof this.options.optionLabel||this.options.optionLabelTemplate||(s=a))),n=function(){return{elements:l.get(),data:[{dataItem:s}]}},this.angular("cleanup",n);try{l.html(o(s))}catch(r){l.html("")}this.angular("compile",n)},_preselect:function(e,t){e||t||(t=this._optionLabelText()),this._accessor(e),this._textAccessor(t),this._old=this._accessor(),this._oldIndex=this.selectedIndex,this.listView.setValue(e),this._initialIndex=null,this._presetValue=!0},_assignInstance:function(e,t){var n=this.options.dataTextField,s={};return n?(i(s,n.split("."),e),i(s,this.options.dataValueField.split("."),t),s=new c(s)):s=e,s}});a.plugin(O)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()});
//# sourceMappingURL=kendo.dropdownlist.min.js.map
