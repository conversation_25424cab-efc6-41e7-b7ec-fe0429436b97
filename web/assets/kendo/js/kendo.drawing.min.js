/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("drawing/util.min",["kendo.core.min"],t)}(function(){return function(t){function e(){return t.Deferred()}function n(e){return t.when.apply(t,e)}kendo.drawing.util=kendo.drawing.util||{},kendo.deepExtend(kendo.drawing.util,{createPromise:e,promiseAll:n})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()}),function(t,define){define("util/text-metrics.min",["kendo.core.min"],t)}(function(){!function(t){function e(t){return(t+"").replace(a,h)}function n(t){var e,n=[];for(e in t)n.push(e+t[e]);return n.sort().join("")}function i(t){var e,n=2166136261;for(e=0;e<t.length;++e)n+=(n<<1)+(n<<4)+(n<<7)+(n<<8)+(n<<24),n^=t.charCodeAt(e);return n>>>0}function r(){return{width:0,height:0,baseline:0}}function o(t,e,n){return u.current.measure(t,e,n)}var s,a,h,l,c,u;window.kendo.util=window.kendo.util||{},s=kendo.Class.extend({init:function(t){this._size=t,this._length=0,this._map={}},put:function(t,e){var n=this._map,i={key:t,value:e};n[t]=i,this._head?(this._tail.newer=i,i.older=this._tail,this._tail=i):this._head=this._tail=i,this._length>=this._size?(n[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(t){var e=this._map[t];if(e)return e===this._head&&e!==this._tail&&(this._head=e.newer,this._head.older=null),e!==this._tail&&(e.older&&(e.older.newer=e.newer,e.newer.older=e.older),e.older=this._tail,e.newer=null,this._tail.newer=e,this._tail=e),e.value}}),a=/\r?\n|\r|\t/g,h=" ",l={baselineMarkerSize:1},"undefined"!=typeof document&&(c=document.createElement("div"),c.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),u=kendo.Class.extend({init:function(e){this._cache=new s(1e3),this.options=t.extend({},l,e)},measure:function(t,o,s){var a,h,l,u,d,f,p,g,m;if(void 0===s&&(s={}),!t)return r();if(a=n(o),h=i(t+a),l=this._cache.get(h))return l;u=r(),d=s.box||c,f=this._baselineMarker().cloneNode(!1);for(p in o)g=o[p],void 0!==g&&(d.style[p]=g);return m=s.normalizeText!==!1?e(t):t+"",d.textContent=m,d.appendChild(f),document.body.appendChild(d),m.length&&(u.width=d.offsetWidth-this.options.baselineMarkerSize,u.height=d.offsetHeight,u.baseline=f.offsetTop+this.options.baselineMarkerSize),u.width>0&&u.height>0&&this._cache.put(h,u),d.parentNode.removeChild(d),u},_baselineMarker:function(){var t=document.createElement("div");return t.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",t}}),u.current=new u,kendo.deepExtend(kendo.util,{LRUCache:s,TextMetrics:u,measureText:o,objectKey:n,hashKey:i,normalizeText:e})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()}),function(t,define){define("drawing/kendo-drawing.min",["drawing/util.min","kendo.color.min","util/text-metrics.min"],t)}(function(){!function(t){function e(t,e){return t.push.apply(t,e),t}function n(t){for(var e=[1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],n="";t>0;)t<e[0]?e.shift():(n+=Qe[e[0]],t-=e[0]);return n}function i(t){return typeof t!==Ze}function r(){return"kdef"+Je++}function o(t){return t/Ke}function s(t){var e,n,i="";for(e=0;e<t.length;e++)n=t.charCodeAt(e),n<128?i+=rn(n):n<2048?(i+=rn(192|n>>>6),i+=rn(128|63&n)):n<65536&&(i+=rn(224|n>>>12),i+=rn(128|n>>>6&63),i+=rn(128|63&n));return i}function a(t){for(var e,n,i,r,o,a,h,l="",c=0,u=s(t);c<u.length;)e=u.charCodeAt(c++),n=u.charCodeAt(c++),i=u.charCodeAt(c++),r=e>>2,o=(3&e)<<4|n>>4,a=(15&n)<<2|i>>6,h=63&i,isNaN(n)?a=h=64:isNaN(i)&&(h=64),l=l+nn.charAt(r)+nn.charAt(o)+nn.charAt(a)+nn.charAt(h);return l}function h(t){return i((t.x||{}).location)?{x:t.x.location,y:t.y.location}:{x:t.pageX||t.clientX||0,y:t.pageY||t.clientY||0}}function l(t){return void 0===t&&(t={}),t.touch?t.touch.initialTouch:t.target}function c(t){return""===t||null===t||"none"===t||"transparent"===t||!i(t)}function u(t){if(t)return t[t.length-1]}function d(t,e,n){return Math.max(Math.min(t,n),e)}function f(t,e){function n(t,n){for(var i=[],r=0,o=0,s=0;r<t.length&&o<n.length;)e(t[r],n[o])<=0?i[s++]=t[r++]:i[s++]=n[o++];return r<t.length&&i.push.apply(i,t.slice(r)),o<n.length&&i.push.apply(i,n.slice(o)),i}return t.length<2?t.slice():function i(t){var e,r,o;return t.length<=1?t:(e=Math.floor(t.length/2),r=t.slice(0,e),o=t.slice(e),r=i(r),o=i(o),n(r,o))}(t)}function p(t){return t*Ke}function g(t){return t?Math.pow(10,t):1}function m(t,e){var n=g(e);return Math.round(t*n)/n}function v(t,e){return i(t)?t:e}function x(t,e){var n,i,r;for(n in e)for(i=n.trim().split(" "),r=0;r<i.length;r++)t.addEventListener(i[r],e[n],!1)}function y(t){var e=t.getBoundingClientRect(),n=document.documentElement;return{top:e.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||0),left:e.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||0)}}function w(t,e){var n,i,r={},o=window.getComputedStyle(t)||{},s=Array.isArray(e)?e:[e];for(n=0;n<s.length;n++)i=s[n],r[i]=o[i];return r}function _(t){return isNaN(t)?t:t+"px"}function b(t,e){var n,r,o;return e?(n=e.width,r=e.height,i(n)&&(t.style.width=_(n)),i(r)&&(t.style.height=_(r)),void 0):(o=w(t,["width","height"]),{width:parseInt(o.width,10),height:parseInt(o.height,10)})}function C(t,e){var n,i,r;void 0===e&&(e={});for(n in e)for(i=n.trim().split(" "),r=0;r<i.length;r++)t.removeEventListener(i[r],e[n],!1)}function k(t){return function(e){return this[t]!==e&&(this[t]=e,this.geometryChange()),this}}function T(t){return function(){return this[t]}}function M(t,e){var n,i,r;for(n=0;n<e.length;n++)i=e[n],r=i.charAt(0).toUpperCase()+i.substring(1,i.length),t["set"+r]=k(i),t["get"+r]=T(i)}function S(t){return t&&"function"==typeof t.matrix?t.matrix():t}function E(t){return null===t?null:t instanceof dn?t:new dn(t)}function A(t,e,n,i){var r=0,o=0;return i&&(r=Math.atan2(i.c*n,i.a*e),0!==i.b&&(o=Math.atan2(i.d*n,i.b*e))),{x:r,y:o}}function P(t){var e="_"+t;return function(t){return i(t)?(this._observerField(e,t),this.geometryChange(),this):this[e]}}function O(t,e){for(var n=0;n<e.length;n++)t[e[n]]=P(e[n])}function z(t,e,n){return void 0===n&&(n=bn),0===m(Math.abs(t-e),n)}function N(t,e,n){return t<e||z(t,e,n)}function R(t,e,n,i){var r=e.x-t.x,o=i.x-n.x,s=e.y-t.y,a=i.y-n.y,h=t.x-n.x,l=t.y-n.y,c=r*a-o*s,u=(r*l-s*h)/c,d=(o*l-a*h)/c;if(u>=0&&u<=1&&d>=0&&d<=1)return new ln(t.x+d*r,t.y+d*s)}function B(t,e,n,i,r,s){var a=m((r-t)/n,3),h=m((s-e)/i,3);return m(o(Math.atan2(h,a)))}function L(t){var e,n,i,r,s,a,h,l,c,u,d,f,g,v,x,y,w,_,b,C,k,T,M,S,E,A=t.x1,P=t.y1,O=t.x2,z=t.y2,N=t.rx,R=t.ry,B=t.largeArc,L=t.swipe,F=t.rotation;return void 0===F&&(F=0),e=p(F),n=Math.cos(e),i=Math.sin(e),r=n*(A-O)/2+i*(P-z)/2,s=-i*(A-O)/2+n*(P-z)/2,a=B!==L?1:-1,h=Math.pow(r,2),l=Math.pow(s,2),c=Math.pow(N,2),u=Math.pow(R,2),d=h/c+l/u,d>1&&(d=Math.sqrt(h/c+l/u),N=d*N,c=Math.pow(N,2),R=d*R,u=Math.pow(R,2)),f=a*Math.sqrt((c*u-c*l-u*h)/(c*l+u*h)),isNaN(f)&&(f=0),g=f*(N*s)/R,v=-f*(R*r)/N,x=n*g-i*v+(A+O)/2,y=i*g+n*v+(P+z)/2,w=(r-g)/N,_=(s-v)/R,b=-(r+g)/N,C=-(s+v)/R,k=(_>=0?1:-1)*o(Math.acos(w/Math.sqrt(w*w+_*_))),T=m((w*b+_*C)/(Math.sqrt(w*w+_*_)*Math.sqrt(b*b+C*C)),10),M=(w*C-_*b>=0?1:-1)*o(Math.acos(T)),!L&&M>0&&(M-=360),L&&M<0&&(M+=360),S=k+M,E=S>=0?1:-1,S=Math.abs(S)%360*E,{center:new ln(x,y),startAngle:k,endAngle:S,radiusX:N,radiusY:R,xRotation:F}}function F(t,e){for(var n=t;n<e;)n+=90;return n}function I(t){var e="_"+t;return function(t){return i(t)?(this._observerField(e,ln.create(t)),this.geometryChange(),this):this[e]}}function D(t,e){for(var n=0;n<e.length;n++)t[e[n]]=I(e[n])}function H(t,e,n){var i=o(Math.atan2(e.y-t.y,e.x-t.x)),r=n.transformCopy(E().rotate(-i,t));return r.x<t.x}function j(t,e,n){var i=1-t;return Math.pow(i,3)*n[0][e]+3*Math.pow(i,2)*t*n[1][e]+3*Math.pow(t,2)*i*n[2][e]+Math.pow(t,3)*n[3][e]}function q(t,e){return[-t[0][e]+3*t[1][e]-3*t[2][e]+t[3][e],3*(t[0][e]-2*t[1][e]+t[2][e]),3*(-t[0][e]+t[1][e]),t[0][e]]}function G(t){return t<0?-1:1}function $(t,e,n){var i=Math.sqrt(Math.pow(e,2)-4*t*n);return[(-e+i)/(2*t),(-e-i)/(2*t)]}function V(t,e,n,i){var r,o,s,a,h,l,c,u,d,f,p,g,v;return 0===t?$(e,n,i):(r=(3*t*n-Math.pow(e,2))/(3*Math.pow(t,2)),o=(2*Math.pow(e,3)-9*t*e*n+27*Math.pow(t,2)*i)/(27*Math.pow(t,3)),s=Math.pow(r/3,3)+Math.pow(o/2,2),a=new Rn(0,1),h=-e/(3*t),s<0?(l=new Rn(-o/2,Math.sqrt(-s)).nthRoot(3),c=new Rn(-o/2,(-Math.sqrt(-s))).nthRoot(3)):(l=-o/2+Math.sqrt(s),l=new Rn(G(l)*Math.pow(Math.abs(l),1/3)),c=-o/2-Math.sqrt(s),c=new Rn(G(c)*Math.pow(Math.abs(c),1/3))),u=l.add(c),p=l.add(c).multiplyConstant(-.5),g=l.add(c.negate()).multiplyConstant(Math.sqrt(3)/2),d=p.add(a.multiply(g)),f=p.add(a.negate().multiply(g)),v=[],u.isReal()&&v.push(m(u.real+h,bn)),d.isReal()&&v.push(m(d.real+h,bn)),f.isReal()&&v.push(m(f.real+h,bn)),v)}function U(t,e,n,i,r){var o,s,a=q(t,i),h=V(a[0],a[1],a[2],a[3]-e[i]);for(s=0;s<h.length;s++)if(0<=h[s]&&h[s]<=1&&(o=j(h[s],n,t),Math.abs(o-e[n])<=r))return!0}function X(t,e,n){var i,r,o,s=q(t,"x"),a=V(s[0],s[1],s[2],s[3]-e.x),h=0;for(o=0;o<a.length;o++)i=j(a[o],"y",t),r=z(i,e.y)||i>e.y,r&&((0===a[o]||1===a[o])&&n.bottomRight().x>e.x||0<a[o]&&a[o]<1)&&h++;return h}function Y(t,e,n){var i,r,o,s,a,h;return t.x!==e.x&&(r=Math.min(t.x,e.x),o=Math.max(t.x,e.x),s=Math.min(t.y,e.y),a=Math.max(t.y,e.y),h=r<=n.x&&n.x<o,i=s===a?n.y<=s&&h:h&&(a-s)*((t.x-e.x)*(t.y-e.y)>0?n.x-r:o-n.x)/(o-r)+s-n.y>=0),i?1:0}function W(t){var e,n=t.length,i=tn,r=en;for(e=0;e<n;e++)r=Math.max(r,t[e]),i=Math.min(i,t[e]);return{min:i,max:r}}function Q(t,e,n){var i,r,o,s;for(r=0;r<t.length;r++)o=t[r],o.visible()&&(s=e?o.bbox(n):o.rawBBox(),s&&(i=i?un.union(i,s):s));return i}function Z(t,e){var n,i,r,o;for(i=0;i<t.length;i++)r=t[i],r.visible()&&(o=r.clippedBBox(e),o&&(n=n?un.union(n,o):o));return n}function J(t,e,n){var i,r=e?0:1;for(i=0;i<t.length;i+=2)t.splice(i+r,0,n)}function K(t,e){if(t&&e)return e.scaleCopy(2).translate(-t.x,-t.y)}function tt(t,e,n){var i=e.clone().scale(2/3);return{controlOut:i.clone().translateWith(t.scaleCopy(In)),controlIn:i.translateWith(n.scaleCopy(In))}}function et(t){var e=[];return t.replace(Hn,function(t,n){e.push(parseFloat(n))}),e}function nt(t,e,n){var i=n.transform()||E(),r=i.matrix();r.e+=t.x-e.origin.x,r.f+=t.y-e.origin.y,i.matrix(r),n.transform(i)}function it(t,e,n,i,r){var o;return o="start"===n?e.origin[i]:"end"===n?e.origin[i]+e.size[r]-t:e.origin[i]+(e.size[r]-t)/2}function rt(t,e,n,i,r){var o;return o="start"===n?e.origin[i]+e.size[r]-t:"end"===n?e.origin[i]:e.origin[i]+(e.size[r]-t)/2}function ot(t,e,n,i,r){var o,s,a;for(o=0;o<t.length;o++)s=t[o].clippedBBox(),s&&(a=s.origin.clone(),a[i]=it(s.size[r],e,n||"start",i,r),nt(a,s,t[o]))}function st(t,e,n){ot(t,e,n,"x","width")}function at(t,e,n){ot(t,e,n,"y","height")}function ht(t,e,n,i){var r,o,s,a,h;if(t.length>1)for(r=new ln,o=t[0].bbox,s=1;s<t.length;s++)a=t[s].element,h=t[s].bbox,r[e]=o.origin[e]+o.size[i],r[n]=h.origin[n],nt(r,h,a),h.origin[e]=r[e],o=h}function lt(t){var e,n,i,r=[];for(e=0;e<t.length;e++)n=t[e],i=n.clippedBBox(),i&&r.push({element:n,bbox:i});return r}function ct(t){ht(lt(t),"x","y","width")}function ut(t){ht(lt(t),"y","x","height")}function dt(t,e,n){var i,r,o,s,a=e.size[n],h=[],l=[],c=0,u=function(){l.push({element:i,bbox:r})};for(o=0;o<t.length;o++)i=t[o],r=i.clippedBBox(),r&&(s=r.size[n],c+s>a?l.length?(h.push(l),l=[],u(),c=s):(u(),h.push(l),l=[],c=0):(u(),c+=s));return l.length&&h.push(l),h}function ft(t,e,n,i,r){var o,s,a,h,l=dt(t,e,r),c=e.origin.clone(),u=[];for(o=0;o<l.length;o++)for(s=l[o],a=s[0],c[i]=a.bbox.origin[i],nt(c,a.bbox,a.element),a.bbox.origin[n]=c[n],ht(s,n,i,r),u.push([]),h=0;h<s.length;h++)u[o].push(s[h].element);return u}function pt(t,e){return ft(t,e,"x","y","width")}function gt(t,e){return ft(t,e,"y","x","height")}function mt(t,e){var n,i,r,o,s=t.clippedBBox();s&&(n=s.size,i=e.size,(i.width<n.width||i.height<n.height)&&(r=Math.min(i.width/n.width,i.height/n.height),o=t.transform()||E(),o.scale(r,r),t.transform(o)))}function vt(t){return function(e){return i(e)?(this.options.set(t,e),this):this.options.get(t)}}function xt(t,e){for(var n=0;n<e.length;n++)t[e[n]]=vt(e[n])}function yt(t){return.5-Math.cos(t*Math.PI)/2}function wt(t){return t}function _t(t,e,n,i){var r=1.70158,o=0,s=i;return 0===t?n:1===t?n+i:(o||(o=.5),s<Math.abs(i)?(s=i,r=o/4):r=o/(2*Math.PI)*Math.asin(i/s),s*Math.pow(2,-10*t)*Math.sin((+t-r)*(1.1*Math.PI)/o)+i+n)}function bt(t,e){return i(e)&&null!==e?" "+t+'="'+e+'" ':""}function Ct(t){var e,n="";for(e=0;e<t.length;e++)n+=bt(t[e][0],t[e][1]);return n}function kt(t){var e,n,r="";for(e=0;e<t.length;e++)n=t[e][1],i(n)&&(r+=t[e][0]+":"+n+";");if(""!==r)return r}function Tt(t,e){return"clip"===t||"fill"===t&&(!e||"Gradient"===e.nodeType)}function Mt(){var t,e=document.getElementsByTagName("base")[0],n=document.location.href,i="";return e&&!Ue.msie&&(t=n.indexOf("#"),t!==-1&&(n=n.substring(0,t)),i=n),i}function St(t){var e,n,i,r;try{e=t.getScreenCTM?t.getScreenCTM():null}catch(o){}e&&(n=-e.e%1,i=-e.f%1,r=t.style,0===n&&0===i||(r.left=n+"px",r.top=i+"px"))}function Et(t){if(!t||"string"!=typeof t||!Yi.test(t))return t;var e=Et._element;return Yi.lastIndex=0,t.replace(Yi,function(t){return e.innerHTML=t,e.textContent||e.innerText})}function At(t){var e,n,i,r=new Ri({skipBaseHref:!0}),o=t.clippedBBox(),s=t;return o&&(e=o.getOrigin(),n=new Jn,n.transform(E().translate(-e.x,-e.y)),n.children.push(t),s=n),r.load([s]),i="<?xml version='1.0' ?><svg xmlns='"+xi+"' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1'>"+r.render()+"</svg>",r.destroy(),i}function Pt(t,e){var n,i,r,o,s,a,h=e.segments;if(0!==h.length){for(n=h[0],i=n.anchor(),t.moveTo(i.x,i.y),r=1;r<h.length;r++)n=h[r],i=n.anchor(),o=h[r-1],s=o.controlOut(),a=n.controlIn(),s&&a?t.bezierCurveTo(s.x,s.y,a.x,a.y,i.x,i.y):t.lineTo(i.x,i.y);e.options.closed&&t.closePath()}}function Ot(t,e){return t._zIndex<e._zIndex?1:t._zIndex>e._zIndex?-1:0}function zt(t,e){var n,i,r;for(n=0;n<e.length;n++)i=e[n],r=kendo.parseColor(i.color()),r.a*=i.opacity(),t.addColorStop(i.offset(),r.toCssRgba())}function Nt(e,n){var i,r,o,s,a,h,l,c,u={width:"800px",height:"600px",cors:"Anonymous"},d=e,f=e.clippedBBox();return f&&(i=f.getOrigin(),d=new Jn,d.transform(E().translate(-i.x,-i.y)),d.children.push(e),r=f.getSize(),u.width=r.width+"px",u.height=r.height+"px"),o=t.extend(u,n),s=document.createElement("div"),a=s.style,a.display="none",a.width=o.width,a.height=o.height,document.body.appendChild(s),h=new ur(s,o),h.suspendTracking(),h.draw(d),l=h.image(),c=function(){h.destroy(),document.body.removeChild(s)},l.then(c,c),l}function Rt(t,e){var n=At(t);return e&&e.raw||(n="data:image/svg+xml;base64,"+a(n)),Xe().resolve(n)}function Bt(t){return Array.prototype.slice.call(t)}function Lt(t,e){t.classList?t.classList.add(e):t.className+=" "+e}function Ft(t,e){t.classList?t.classList.remove(e):t.className=t.className.split(/\s+/).reduce(function(t,n){return n!=e&&t.push(n),t},[]).join(" ")}function It(t,e){Object.keys(e).forEach(function(n){t.style[n]=e[n]})}function Dt(t,e){if(t.closest)return t.closest(e);for(;t&&!/^\[object (?:HTML)?Document\]$/.test(t+"");){if(1==t.nodeType&&Sr(t,e))return t;t=t.parentNode}}function Ht(t){return"number"==typeof t?{x:t,y:t}:Array.isArray(t)?{x:t[0],y:t[1]}:{x:t.x,y:t.y}}function jt(t,e){function n(t){var n=new Jn,i=t.getBoundingClientRect();return me(n,[s.x,0,0,s.y,-i.left*s.x,-i.top*s.y]),Cr._clipbox=!1,Cr._matrix=hn.unit(),Cr._stackingContext={element:t,group:n},Cr._avoidLinks=e.avoidLinks===!0?"a":e.avoidLinks,Lt(t,"k-pdf-export"),De(t,n),Ft(t,"k-pdf-export"),n}function i(t){if(null!=t)return"string"==typeof t&&(t=kendo.template(t.replace(/^\s+|\s+$/g,""))),"function"==typeof t?function(e){var n,i=t(e);return i&&"string"==typeof i&&(n=document.createElement("div"),n.innerHTML=i,i=n.firstElementChild),i}:function(){return t.cloneNode(!0)}}function r(t,e,n,r,o,s,a){function h(){("-"!=n||o)&&c(w);var e=f();w.parentNode.insertBefore(e,w),e.appendChild(w),v&&y.forEach(function(t,e){var n=v({element:t,pageNum:e+1,totalPages:y.length});n&&t.appendChild(n)}),Jt(y,function(){Zt(y,function(){t({pages:y,container:_})})})}function l(t){if(a.keepTogether&&Sr(t,a.keepTogether)&&t.offsetHeight<=o-b)return!0;var e=t.tagName;return!(/^h[1-6]$/i.test(e)&&t.offsetHeight>=o-b)&&(t.getAttribute("data-kendo-chart")||/^(?:img|tr|thead|th|tfoot|iframe|svg|object|canvas|input|textarea|select|video|h[1-6])/i.test(t.tagName))}function c(t){var e,i,r,s,a,h,u;if("TABLE"==t.tagName&&It(t,{tableLayout:"fixed"}),!l(t)){for(e=oe(t),i=parseFloat(se(e,"padding-bottom")),r=parseFloat(se(e,"border-bottom-width")),s=b,b+=i+r,a=!0,h=t.firstChild;h;h=h.nextSibling)if(1==h.nodeType){if(a=!1,Sr(h,n)){d(h);continue}if(!o){c(h);continue}if(!/^(?:static|relative)$/.test(se(oe(h),"position")))continue;u=p(h),1==u?d(h):u&&l(h)?d(h):c(h)}else 3==h.nodeType&&o&&(g(h,a),a=!1);b=s}}function u(t){var e=t.parentNode,n=e.firstChild;if(t===n)return!0;if(t===e.children[0]){if(7==n.nodeType||8==n.nodeType)return!0;if(3==n.nodeType)return!/\S/.test(n.data)}return!1}function d(t){var e,n,i,r,o,s,h;return 1==t.nodeType&&t!==w&&u(t)?d(t.parentNode):(e=Dt(t,"table"),n=e&&e.querySelector("colgroup"),a.repeatHeaders&&(i=e&&e.querySelector("thead"),r=Dt(t,".k-grid.k-widget"),r&&r.querySelector(".k-auto-scrollable")&&(o=r.querySelector(".k-grid-header"))),s=f(),h=x.createRange(),h.setStartBefore(w),h.setEndBefore(t),s.appendChild(h.extractContents()),w.parentNode.insertBefore(s,w),m(t.parentNode),e&&(e=Dt(t,"table"),a.repeatHeaders&&i&&e.insertBefore(i.cloneNode(!0),e.firstChild),n&&e.insertBefore(n.cloneNode(!0),e.firstChild)),void(a.repeatHeaders&&o&&(r=Dt(t,".k-grid.k-widget"),r.insertBefore(o.cloneNode(!0),r.firstChild))))}function f(){var t=x.createElement("KENDO-PDF-PAGE");return It(t,{display:"block",boxSizing:"content-box",width:r?r+"px":"auto",padding:s.top+"px "+s.right+"px "+s.bottom+"px "+s.left+"px",position:"relative",height:o?o+"px":"auto",overflow:o||r?"hidden":"visible",clear:"both"}),a&&a.pageClassName&&(t.className=a.pageClassName),y.push(t),t}function p(t){var e,n,i=t.getBoundingClientRect();return 0===i.width||0===i.height?0:(e=w.getBoundingClientRect().top,n=o-b,i.height>n?3:i.top-e>n?1:i.bottom-e>n?2:0)}function g(t,e){var n,i,r,o,s;/\S/.test(t.data)&&(n=t.data.length,i=x.createRange(),i.selectNodeContents(t),r=p(i),r&&(o=t,1==r?d(e?t.parentNode:t):(!function a(e,n,r){return i.setEnd(t,n),e==n||n==r?n:p(i)?a(e,e+n>>1,n):a(n,n+r>>1,r)}(0,n>>1,n),!/\S/.test(""+i)&&e?d(t.parentNode):(o=t.splitText(i.endOffset),s=f(),i.setStartBefore(w),s.appendChild(i.extractContents()),w.parentNode.insertBefore(s,w),m(o.parentNode))),g(o)))}function m(t){var e=Dt(t,"li");e&&(e.setAttribute("kendo-no-bullet","1"),m(e.parentNode))}var v=i(a.template),x=e.ownerDocument,y=[],w=a._destructive?e:Er(e),_=x.createElement("KENDO-PDF-DOCUMENT"),b=0;Bt(w.querySelectorAll("tfoot")).forEach(function(t){t.parentNode.appendChild(t)}),Bt(w.querySelectorAll("ol")).forEach(function(t){Bt(t.children).forEach(function(t,e){t.setAttribute("kendo-split-index",e)})}),It(_,{display:"block",position:"absolute",boxSizing:"content-box",left:"-10000px",top:"-10000px"}),r&&(It(_,{width:r+"px",paddingLeft:s.left+"px",paddingRight:s.right+"px"}),It(w,{overflow:"hidden"})),e.parentNode.insertBefore(_,e),_.appendChild(w),a.beforePageBreak?setTimeout(function(){a.beforePageBreak(_,h)},15):setTimeout(h,15)}var o,s;if(e||(e={}),o=Xe(),!t)return o.reject("No element to export");if("function"!=typeof window.getComputedStyle)throw Error("window.getComputedStyle is missing.  You are using an unsupported browser, or running in IE8 compatibility mode.  Drawing HTML is supported in Chrome, Firefox, Safari and IE9+.");return kendo.pdf.defineFont(qt(t.ownerDocument)),s=Ht(e.scale||1),Jt(t,function(){var i,a=e&&e.forcePageBreak,h=e&&e.paperSize&&"auto"!=e.paperSize,l=kendo.pdf.getPaperOptions(function(t,n){return"paperSize"==t?h?e[t]:"A4":t in e?e[t]:n}),c=h&&l.paperSize[0],u=h&&l.paperSize[1],d=e.margin&&l.margin,f=!!d;a||u?(d||(d={left:0,top:0,right:0,bottom:0}),c&&(c/=s.x),u&&(u/=s.y),d.left/=s.x,d.right/=s.x,d.top/=s.y,d.bottom/=s.y,i=new Jn({pdf:{multiPage:!0,paperSize:h?l.paperSize:"auto",_ignoreMargin:f}}),r(function(t){if(e.progress){var r=!1,s=0;!function a(){if(s<t.pages.length){var h=n(t.pages[s]);i.append(h),e.progress({page:h,pageNum:++s,totalPages:t.pages.length,cancel:function(){r=!0}}),r?t.container.parentNode.removeChild(t.container):setTimeout(a)}else t.container.parentNode.removeChild(t.container),o.resolve(i)}()}else t.pages.forEach(function(t){i.append(n(t))}),t.container.parentNode.removeChild(t.container),o.resolve(i)},t,a,c?c-d.left-d.right:null,u?u-d.top-d.bottom:null,d,e)):o.resolve(n(t))}),o}function qt(t){function e(t){if(t){var e=null;try{e=t.cssRules}catch(n){}e&&i(t,e)}}function n(t){var e,n=se(t.style,"src");return n?Pr(n).reduce(function(t,e){var n=Or(e);return n&&t.push(n),t},[]):(e=Or(t.cssText),e?[e]:[])}function i(t,i){var o,s,a,h,l,c,u;for(o=0;o<i.length;++o)switch(s=i[o],s.type){case 3:e(s.styleSheet);break;case 5:a=s.style,h=Pr(se(a,"font-family")),l=/^([56789]00|bold)$/i.test(se(a,"font-weight")),c="italic"==se(a,"font-style"),u=n(s),u.length>0&&r(t,h,l,c,u[0])}}function r(t,e,n,i,r){/^data:/i.test(r)||/^[^\/:]+:\/\//.test(r)||/^\//.test(r)||(r=(t.href+"").replace(/[^\/]*$/,"")+r),e.forEach(function(t){t=t.replace(/^(['"]?)(.*?)\1$/,"$2"),n&&(t+="|bold"),i&&(t+="|italic"),o[t]=r})}var o,s;for(null==t&&(t=document),o={},s=0;s<t.styleSheets.length;++s)e(t.styleSheets[s]);return o}function Gt(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function $t(t){return t="_counter_"+t,Cr[t]}function Vt(t){var e=[],n=Cr;for(t="_counter_"+t;n;)Gt(n,t)&&e.push(n[t]),n=Object.getPrototypeOf(n);return e.reverse()}function Ut(t,e){var n=Cr;for(t="_counter_"+t;n&&!Gt(n,t);)n=Object.getPrototypeOf(n);n||(n=Cr._root),n[t]=(n[t]||0)+(null==e?1:e)}function Xt(t,e){t="_counter_"+t,Cr[t]=null==e?0:e}function Yt(t,e,n){var i,r,o;for(i=0;i<t.length;)r=t[i++],o=parseFloat(t[i]),isNaN(o)?e(r,n):(e(r,o),++i)}function Wt(t){var e,n=se(t,"counter-reset");n&&Yt(Pr(n,/^\s+/),Xt,0),e=se(t,"counter-increment"),e&&Yt(Pr(e,/^\s+/),Ut,1)}function Qt(t,e){var n=kendo.parseColor(t,!0);return n&&(n=n.toRGB(),e?n=n.toCssRgba():0===n.a&&(n=null)),n}function Zt(t,e){function n(){--i<=0&&e()}var i=0;t.forEach(function(t){var e,r,o=t.querySelectorAll("img");for(e=0;e<o.length;++e)r=o[e],r.complete||(i++,r.onload=r.onerror=n)}),i||n()}function Jt(t,e){function n(t){br[t]||(br[t]=!0,s.push(t))}function i(t){/^img$/i.test(t.tagName)&&n(t.src),Ar(se(oe(t),"background-image")).forEach(function(t){"url"==t.type&&n(t.url)}),t.children&&Bt(t.children).forEach(i)}function r(){--o<=0&&e()}var o,s=[];Array.isArray(t)?t.forEach(i):i(t),o=s.length,0===o&&r(),s.forEach(function(t){var e=br[t]=new window.Image;/^data:/i.test(t)||(e.crossOrigin="Anonymous"),e.src=t,e.complete?r():(e.onload=r,e.onerror=function(){br[t]=null,r()})})}function Kt(t){var e,n="";do e=t%26,n=String.fromCharCode(97+e)+n,t=Math.floor(t/26);while(t>0);return n}function te(t,e,n){var i,r;Cr=Object.create(Cr),Cr[t.tagName.toLowerCase()]={element:t,style:e},i=se(e,"text-decoration"),i&&"none"!=i&&(r=se(e,"color"),i.split(/\s+/g).forEach(function(t){Cr[t]||(Cr[t]=r)})),re(e)&&(Cr._stackingContext={element:t,group:n})}function ee(){Cr=Object.getPrototypeOf(Cr)}function ne(t){if(null!=Cr._clipbox){var e=t.bbox(Cr._matrix);Cr._clipbox=Cr._clipbox?un.intersect(Cr._clipbox,e):e}}function ie(){var t=Cr._clipbox;return null==t||(t?0===t.width()||0===t.height():void 0)}function re(t){function e(e){return se(t,e)}if("none"!=e("transform")||"static"!=e("position")||"auto"!=e("z-index")||e("opacity")<1)return!0}function oe(t,e){return window.getComputedStyle(t,e||null)}function se(t,e,n){var i=t.getPropertyValue(e);return null!=i&&""!==i||(wr.webkit?i=t.getPropertyValue("-webkit-"+e):wr.mozilla?i=t.getPropertyValue("-moz-"+e):wr.opera?i=t.getPropertyValue("-o-"+e):Tr&&(i=t.getPropertyValue("-ms-"+e))),arguments.length>2&&(null==i||""===i)?n:i}function ae(t,e,n,i){t.setProperty(e,n,i),wr.webkit?t.setProperty("-webkit-"+e,n,i):wr.mozilla?t.setProperty("-moz-"+e,n,i):wr.opera?t.setProperty("-o-"+e,n,i):Tr&&(t.setProperty("-ms-"+e,n,i),e="ms"+e.replace(/(^|-)([a-z])/g,function(t,e,n){return e+n.toUpperCase()}),t[e]=n)}function he(t,e){return e="border-"+e,{width:parseFloat(se(t,e+"-width")),style:se(t,e+"-style"),color:Qt(se(t,e+"-color"),!0)}}function le(t,e){var n=t.style.cssText,i=e();return t.style.cssText=n,i}function ce(t,e){var n=se(t,"border-"+e+"-radius").split(/\s+/g).map(parseFloat);return 1==n.length&&n.push(n[0]),ye({x:n[0],y:n[1]})}function ue(t){var e=t.getBoundingClientRect();return e=de(e,"border-*-width",t),e=de(e,"padding-*",t)}function de(t,e,n){var i,r,o,s,a;return"string"==typeof e?(i=oe(n),r=parseFloat(se(i,e.replace("*","top"))),o=parseFloat(se(i,e.replace("*","right"))),s=parseFloat(se(i,e.replace("*","bottom"))),a=parseFloat(se(i,e.replace("*","left")))):"number"==typeof e&&(r=o=s=a=e),{top:t.top+r,right:t.right-o,bottom:t.bottom-s,left:t.left+a,width:t.right-t.left-o-a,height:t.bottom-t.top-s-r}}function fe(t){var e,n,i=se(t,"transform");return"none"==i?null:(e=/^\s*matrix\(\s*(.*?)\s*\)\s*$/.exec(i),e?(n=se(t,"transform-origin"),e=e[1].split(/\s*,\s*/g).map(parseFloat),n=n.split(/\s+/g).map(parseFloat),{matrix:e,origin:n}):void 0)}function pe(t){return 180*t/Math.PI%360}function ge(t){var e=parseFloat(t);return/grad$/.test(t)?Math.PI*e/200:/rad$/.test(t)?e:/turn$/.test(t)?Math.PI*e*2:/deg$/.test(t)?Math.PI*e/180:void 0}function me(t,e){return e=new hn(e[0],e[1],e[2],e[3],e[4],e[5]),t.transform(e),e}function ve(t,e){t.clip(e)}function xe(t,e,n,i){for(var r=new Tn([e,n],i).curvePoints(),o=1;o<r.length;)t.curveTo(r[o++],r[o++],r[o++])}function ye(t){return(t.x<=0||t.y<=0)&&(t.x=t.y=0),t}function we(t,e,n,i,r){var o=Math.max(0,e.x),s=Math.max(0,e.y),a=Math.max(0,n.x),h=Math.max(0,n.y),l=Math.max(0,i.x),c=Math.max(0,i.y),u=Math.max(0,r.x),d=Math.max(0,r.y),f=Math.min(t.width/(o+a),t.height/(h+c),t.width/(l+u),t.height/(d+s));return f<1&&(o*=f,s*=f,a*=f,h*=f,l*=f,c*=f,u*=f,d*=f),{tl:{x:o,y:s},tr:{x:a,y:h},br:{x:l,y:c},bl:{x:u,y:d}}}function _e(t,e,n){var i,r,o,s,a,h,l,c,u=oe(t),d=ce(u,"top-left"),f=ce(u,"top-right"),p=ce(u,"bottom-left"),g=ce(u,"bottom-right");return"padding"!=n&&"content"!=n||(i=he(u,"top"),r=he(u,"right"),o=he(u,"bottom"),s=he(u,"left"),d.x-=s.width,d.y-=i.width,f.x-=r.width,f.y-=i.width,g.x-=r.width,g.y-=o.width,p.x-=s.width,p.y-=o.width,"content"==n&&(a=parseFloat(se(u,"padding-top")),h=parseFloat(se(u,"padding-right")),l=parseFloat(se(u,"padding-bottom")),c=parseFloat(se(u,"padding-left")),d.x-=c,d.y-=a,f.x-=h,f.y-=a,g.x-=h,g.y-=l,p.x-=c,p.y-=l)),"number"==typeof n&&(d.x-=n,d.y-=n,f.x-=n,f.y-=n,g.x-=n,g.y-=n,p.x-=n,p.y-=n),be(e,d,f,g,p)}function be(t,e,n,i,r){var o=we(t,e,n,i,r),s=o.tl,a=o.tr,h=o.br,l=o.bl,c=new $n({fill:null,stroke:null});return c.moveTo(t.left,t.top+s.y),s.x&&xe(c,t.left+s.x,t.top+s.y,{startAngle:-180,endAngle:-90,radiusX:s.x,radiusY:s.y}),c.lineTo(t.right-a.x,t.top),a.x&&xe(c,t.right-a.x,t.top+a.y,{startAngle:-90,endAngle:0,radiusX:a.x,radiusY:a.y}),c.lineTo(t.right,t.bottom-h.y),h.x&&xe(c,t.right-h.x,t.bottom-h.y,{startAngle:0,endAngle:90,radiusX:h.x,radiusY:h.y}),c.lineTo(t.left+l.x,t.bottom),l.x&&xe(c,t.left+l.x,t.bottom-l.y,{startAngle:90,endAngle:180,radiusX:l.x,radiusY:l.y}),c.close()}function Ce(t,e){var i=parseFloat(t)+"";switch(e){case"decimal-leading-zero":return i.length<2&&(i="0"+i),i;case"lower-roman":return n(t).toLowerCase();case"upper-roman":return n(t).toUpperCase();case"lower-latin":case"lower-alpha":return Kt(t-1);case"upper-latin":case"upper-alpha":return Kt(t-1).toUpperCase();default:return i}}function ke(t,e){function n(t,e,n){return n?(n=n.replace(/^\s*(["'])(.*)\1\s*$/,"$2"),Vt(t).map(function(t){return Ce(t,e)}).join(n)):Ce($t(t)||0,e)}var i,r=Pr(e,/^\s+/),o=[];return r.forEach(function(e){var r;(i=/^\s*(["'])(.*)\1\s*$/.exec(e))?o.push(i[2].replace(/\\([0-9a-f]{4})/gi,function(t,e){return String.fromCharCode(parseInt(e,16))})):(i=/^\s*counter\((.*?)\)\s*$/.exec(e))?(r=Pr(i[1]),o.push(n(r[0],r[1]))):(i=/^\s*counters\((.*?)\)\s*$/.exec(e))?(r=Pr(i[1]),o.push(n(r[0],r[2],r[1]))):o.push((i=/^\s*attr\((.*?)\)\s*$/.exec(e))?t.getAttribute(i[1])||"":e)}),o.join("")}function Te(t){var e,n;if(t.cssText)return t.cssText;for(e=[],n=0;n<t.length;++n)e.push(t[n]+": "+se(t,t[n]));return e.join(";\n")}function Me(t,e){function n(e,n){var r,o=oe(t,e),s=o.content;Wt(o),s&&"normal"!=s&&"none"!=s&&"0px"!=o.width&&(r=t.ownerDocument.createElement(_r),r.style.cssText=Te(o),r.textContent=ke(t,s),t.insertBefore(r,n),i.push(r))}var i,r;return t.tagName==_r?void Se(t,e):(i=[],n(":before",t.firstChild),n(":after",null),void(i.length>0?(r=t.className,t.className+=" kendo-pdf-hide-pseudo-elements",Se(t,e),t.className=r,i.forEach(function(e){t.removeChild(e)})):Se(t,e)))}function Se(t,e){function i(e){var n,i,r,o,s,a;if(/^td$/i.test(t.tagName)&&(n=Cr.table,n&&"collapse"==se(n.style,"border-collapse"))){if(i=he(n.style,"left").width,r=he(n.style,"top").width,0===i&&0===r)return e;if(o=n.element.getBoundingClientRect(),s=n.element.rows[0].cells[0],a=s.getBoundingClientRect(),a.top==o.top||a.left==o.left)return Bt(e).map(function(t){return{left:t.left+i,top:t.top+r,right:t.right+i,bottom:t.bottom+r,height:t.height,width:t.width}})}return e}function r(t,n,i,r,o,s,a,h){function l(e,n,r){var o=Math.PI/2*e/(e+i),s={x:n.x-e,y:n.y-i},a=new $n({fill:{color:t},stroke:null}).moveTo(0,0);me(a,r),xe(a,0,n.y,{startAngle:-90,endAngle:-pe(o),radiusX:n.x,radiusY:n.y}),s.x>0&&s.y>0?(a.lineTo(s.x*Math.cos(o),n.y-s.y*Math.sin(o)),xe(a,0,n.y,{startAngle:-pe(o),endAngle:-90,radiusX:s.x,radiusY:s.y,anticlockwise:!0})):s.x>0?a.lineTo(s.x,i).lineTo(0,i):a.lineTo(s.x,i).lineTo(s.x,0),u.append(a.close())}if(!(i<=0)){var c,u=new Jn;me(u,h),e.append(u),ye(s),ye(a),c=new $n({fill:{color:t},stroke:null}),u.append(c),c.moveTo(s.x?Math.max(s.x,r):0,0).lineTo(n-(a.x?Math.max(a.x,o):0),0).lineTo(n-Math.max(a.x,o),i).lineTo(Math.max(s.x,r),i).close(),s.x&&l(r,s,[-1,0,0,1,s.x,0]),a.x&&l(o,a,[1,0,0,1,n-a.x,0])}}function o(t){var n,i,r=new Jn;for(ve(r,be(t,S,E,P,A)),e.append(r),z&&(n=new $n({fill:{color:z.toCssRgba()},stroke:null}),n.moveTo(t.left,t.top).lineTo(t.right,t.top).lineTo(t.right,t.bottom).lineTo(t.left,t.bottom).close(),r.append(n)),i=l.length;--i>=0;)s(r,t,l[i],c[i%c.length],u[i%u.length],d[i%d.length],f[i%f.length])}function s(e,n,i,r,o,s,a){function h(e,n,i,h,l){function c(){for(;m.origin.x>n.left;)m.origin.x-=i}function u(){for(;m.origin.y>n.top;)m.origin.y-=h}function d(){for(;m.origin.x<n.right;)l(e,m.clone()),m.origin.x+=i}var f,p,g,m,v,x=i/h,y=n;switch("content-box"==s?(y=de(y,"border-*-width",t),y=de(y,"padding-*",t)):"padding-box"==s&&(y=de(y,"border-*-width",t)),/^\s*auto(\s+auto)?\s*$/.test(a)||("contain"==a?(f=Math.min(y.width/i,y.height/h),i*=f,h*=f):"cover"==a?(f=Math.max(y.width/i,y.height/h),i*=f,h*=f):(p=a.split(/\s+/g),i=/%$/.test(p[0])?y.width*parseFloat(p[0])/100:parseFloat(p[0]),h=1==p.length||"auto"==p[1]?i/x:/%$/.test(p[1])?y.height*parseFloat(p[1])/100:parseFloat(p[1]))),g=o+""){case"bottom":g="50% 100%";break;case"top":g="50% 0";break;case"left":g="0 50%";break;case"right":g="100% 50%";break;case"center":g="50% 50%"}if(g=g.split(/\s+/),1==g.length&&(g[1]="50%"),g[0]=/%$/.test(g[0])?parseFloat(g[0])/100*(y.width-i):parseFloat(g[0]),g[1]=/%$/.test(g[1])?parseFloat(g[1])/100*(y.height-h):parseFloat(g[1]),m=new un([y.left+g[0],y.top+g[1]],[i,h]),"no-repeat"==r)l(e,m);else if("repeat-x"==r)c(),d();else if("repeat-y"==r)for(u();m.origin.y<n.bottom;)l(e,m.clone()),m.origin.y+=h;else if("repeat"==r)for(c(),u(),v=m.origin.clone();m.origin.y<n.bottom;)m.origin.x=v.x,d(),m.origin.y+=h}if(i&&"none"!=i)if("url"==i.type){
if(/^url\(\"data:image\/svg/i.test(i.url))return;var l=br[i.url];l&&l.width>0&&l.height>0&&h(e,n,l.width,l.height,function(t,e){t.append(new Qn(i.url,e))})}else{if("linear"!=i.type)return;h(e,n,n.width,n.height,Ee(i))}}function a(){function i(n){le(t,function(){t.style.position="relative";var i=t.ownerDocument.createElement(_r);i.style.position="absolute",i.style.boxSizing="border-box","outside"==o?(i.style.width="6em",i.style.left="-6.8em",i.style.textAlign="right"):i.style.left="0px",n(i),t.insertBefore(i,t.firstChild),De(i,e),t.removeChild(i)})}function r(e){var n,i=t.parentNode.children,r=t.getAttribute("kendo-split-index");if(null!=r)return e(0|r,i.length);for(n=0;n<i.length;++n)if(i[n]===t)return e(n,i.length)}var o,s=se(b,"list-style-type");if("none"!=s)switch(o=se(b,"list-style-position"),s){case"circle":case"disc":case"square":i(function(t){t.style.fontSize="60%",t.style.lineHeight="200%",t.style.paddingRight="0.5em",t.style.fontFamily="DejaVu Serif",t.innerHTML={disc:"●",circle:"◯",square:"■"}[s]});break;case"decimal":case"decimal-leading-zero":i(function(t){r(function(e){++e,"decimal-leading-zero"==s&&e<10&&(e="0"+e),t.innerHTML=e+"."})});break;case"lower-roman":case"upper-roman":i(function(t){r(function(e){e=n(e+1),"upper-roman"==s&&(e=e.toUpperCase()),t.innerHTML=e+"."})});break;case"lower-latin":case"lower-alpha":case"upper-latin":case"upper-alpha":i(function(t){r(function(e){e=Kt(e),/^upper/i.test(s)&&(e=e.toUpperCase()),t.innerHTML=e+"."})})}}function h(n,i,s){function a(t){return{x:t.y,y:t.x}}var h,l,c,u,d,f,p,g;if(0!==n.width&&0!==n.height&&(o(n),h=M.width>0&&(i&&"ltr"==O||s&&"rtl"==O),l=k.width>0&&(s&&"ltr"==O||i&&"rtl"==O),0!==C.width||0!==M.width||0!==k.width||0!==T.width)){if(C.color==k.color&&C.color==T.color&&C.color==M.color&&C.width==k.width&&C.width==T.width&&C.width==M.width&&h&&l)return n=de(n,C.width/2),c=_e(t,n,C.width/2),c.options.stroke={color:C.color,width:C.width},void e.append(c);if(0===S.x&&0===E.x&&0===P.x&&0===A.x&&C.width<2&&M.width<2&&k.width<2&&T.width<2)return C.width>0&&e.append(new $n({stroke:{width:C.width,color:C.color}}).moveTo(n.left,n.top+C.width/2).lineTo(n.right,n.top+C.width/2)),T.width>0&&e.append(new $n({stroke:{width:T.width,color:T.color}}).moveTo(n.left,n.bottom-T.width/2).lineTo(n.right,n.bottom-T.width/2)),h&&e.append(new $n({stroke:{width:M.width,color:M.color}}).moveTo(n.left+M.width/2,n.top).lineTo(n.left+M.width/2,n.bottom)),void(l&&e.append(new $n({stroke:{width:k.width,color:k.color}}).moveTo(n.right-k.width/2,n.top).lineTo(n.right-k.width/2,n.bottom)));u=we(n,S,E,P,A),d=u.tl,f=u.tr,p=u.br,g=u.bl,r(C.color,n.width,C.width,M.width,k.width,d,f,[1,0,0,1,n.left,n.top]),r(T.color,n.width,T.width,k.width,M.width,p,g,[-1,0,0,-1,n.right,n.bottom]),r(M.color,n.height,M.width,T.width,C.width,a(g),a(d),[0,-1,1,0,n.left,n.bottom]),r(k.color,n.height,k.width,C.width,T.width,a(f),a(p),[0,1,-1,0,n.right,n.top])}}var l,c,u,d,f,p,g,m,v,x,y,w,_,b=oe(t),C=he(b,"top"),k=he(b,"right"),T=he(b,"bottom"),M=he(b,"left"),S=ce(b,"top-left"),E=ce(b,"top-right"),A=ce(b,"bottom-left"),P=ce(b,"bottom-right"),O=se(b,"direction"),z=se(b,"background-color");if(z=Qt(z),l=Ar(se(b,"background-image")),c=Pr(se(b,"background-repeat")),u=Pr(se(b,"background-position")),d=Pr(se(b,"background-origin")),f=Pr(se(b,"background-size")),Tr&&(p=b.textOverflow,"ellipsis"==p&&(g=t.style.textOverflow,t.style.textOverflow="clip")),wr.msie&&wr.version<10&&(u=Pr(t.currentStyle.backgroundPosition)),m=de(t.getBoundingClientRect(),"border-*-width",t),function(){var t,n,i,r,o,s,a,h=se(b,"clip"),l=/^\s*rect\((.*)\)\s*$/.exec(h);l&&(t=l[1].split(/[ ,]+/g),n="auto"==t[0]?m.top:parseFloat(t[0])+m.top,i="auto"==t[1]?m.right:parseFloat(t[1])+m.left,r="auto"==t[2]?m.bottom:parseFloat(t[2])+m.top,o="auto"==t[3]?m.left:parseFloat(t[3])+m.left,s=new Jn,a=(new $n).moveTo(o,n).lineTo(i,n).lineTo(i,r).lineTo(o,r).close(),ve(s,a),e.append(s),e=s,ne(a))}(),w=se(b,"display"),"table-row"==w)for(v=[],x=0,y=t.children;x<y.length;++x)v.push(y[x].getBoundingClientRect());else v=t.getClientRects(),1==v.length&&(v=[t.getBoundingClientRect()]);for(v=i(v),x=0;x<v.length;++x)h(v[x],0===x,x==v.length-1);return"A"==t.tagName&&t.href&&!/^#?$/.test(t.getAttribute("href"))&&(Cr._avoidLinks&&Sr(t,Cr._avoidLinks)||(_=document.createRange(),_.selectNodeContents(t),Bt(_.getClientRects()).forEach(function(n){var i=new Jn;i._pdfLink={url:t.href,top:n.top,right:n.right,bottom:n.bottom,left:n.left},e.append(i)}))),v.length>0&&"list-item"==w&&!t.getAttribute("kendo-no-bullet")&&a(v[0]),function(){function n(){var n=_e(t,m,"padding"),i=new Jn;ve(i,n),e.append(i),e=i,ne(n)}ze(t)?n():/^(hidden|auto|scroll)/.test(se(b,"overflow"))?n():/^(hidden|auto|scroll)/.test(se(b,"overflow-x"))?n():/^(hidden|auto|scroll)/.test(se(b,"overflow-y"))&&n()}(),Ae(t,e)||Le(t,e),Tr&&"ellipsis"==p&&(t.style.textOverflow=g),e}function Ee(t){return function(e,n){var i,r,o,s,a,h,l,c,u,d,f,p,g,m=n.width(),v=n.height();switch(t.type){case"linear":switch(i=null!=t.angle?t.angle:Math.PI,t.to){case"top":i=0;break;case"left":i=-Math.PI/2;break;case"bottom":i=Math.PI;break;case"right":i=Math.PI/2;break;case"top left":case"left top":i=-Math.atan2(v,m);break;case"top right":case"right top":i=Math.atan2(v,m);break;case"bottom left":case"left bottom":i=Math.PI+Math.atan2(v,m);break;case"bottom right":case"right bottom":i=Math.PI-Math.atan2(v,m)}t.reverse&&(i-=Math.PI),i%=2*Math.PI,i<0&&(i+=2*Math.PI),r=Math.abs(m*Math.sin(i))+Math.abs(v*Math.cos(i)),o=Math.atan(m*Math.tan(i)/v),s=Math.sin(o),a=Math.cos(o),h=Math.abs(s)+Math.abs(a),l=h/2*s,c=h/2*a,i>Math.PI/2&&i<=3*Math.PI/2&&(l=-l,c=-c),u=[],d=0,f=t.stops.map(function(e,n){var i,o=e.percent;return o?o=parseFloat(o)/100:e.length?o=parseFloat(e.length)/r:0===n?o=0:n==t.stops.length-1&&(o=1),i={color:e.color.toCssRgba(),offset:o},null!=o?(d=o,u.forEach(function(t,e){var n=t.stop;n.offset=t.left+(d-t.left)*(e+1)/(u.length+1)}),u=[]):u.push({left:d,stop:i}),i}),p=[.5-l,.5+c],g=[.5+l,.5-c],e.append($n.fromRect(n).stroke(null).fill(new ai({start:p,end:g,stops:f,userSpace:!1})));break;case"radial":window.console&&window.console.log&&window.console.log("Radial gradients are not yet supported in HTML renderer")}}}function Ae(t,e){var n,i,r,o;return t._kendoExportVisual?n=t._kendoExportVisual():window.kendo&&window.kendo.jQuery&&t.getAttribute(window.kendo.attr("role"))&&(i=window.kendo.widgetInstance(window.kendo.jQuery(t)),i&&(i.exportDOMVisual||i.exportVisual)&&(n=i.exportDOMVisual?i.exportDOMVisual():i.exportVisual())),!!n&&(r=new Jn,r.children.push(n),o=t.getBoundingClientRect(),r.transform(E().translate(o.left,o.top)),e.append(r),!0)}function Pe(t,e,n){var i=ue(t),r=new un([i.left,i.top],[i.width,i.height]),o=new Qn(e,r);ve(o,_e(t,i,"content")),n.append(o)}function Oe(t,e){var n=oe(t),i=oe(e),r=parseFloat(se(n,"z-index")),o=parseFloat(se(i,"z-index")),s=se(n,"position"),a=se(i,"position");return isNaN(r)&&isNaN(o)?/static|absolute/.test(s)&&/static|absolute/.test(a)?0:"static"==s?-1:"static"==a?1:0:isNaN(r)?0===o?0:o>0?-1:1:isNaN(o)?0===r?0:r>0?1:-1:parseFloat(r)-parseFloat(o)}function ze(t){return/^(?:textarea|select|input)$/i.test(t.tagName)}function Ne(t){return t.selectedOptions&&t.selectedOptions.length>0?t.selectedOptions[0]:t.options[t.selectedIndex]}function Re(t,e){var n=oe(t),i=se(n,"color"),r=t.getBoundingClientRect();"checkbox"==t.type?(e.append($n.fromRect(new un([r.left+1,r.top+1],[r.width-2,r.height-2])).stroke(i,1)),t.checked&&e.append((new $n).stroke(i,1.2).moveTo(r.left+.22*r.width,r.top+.55*r.height).lineTo(r.left+.45*r.width,r.top+.75*r.height).lineTo(r.left+.78*r.width,r.top+.22*r.width))):(e.append(new _n(new gn([(r.left+r.right)/2,(r.top+r.bottom)/2],Math.min(r.width-2,r.height-2)/2)).stroke(i,1)),t.checked&&e.append(new _n(new gn([(r.left+r.right)/2,(r.top+r.bottom)/2],Math.min(r.width-8,r.height-8)/2)).fill(i).stroke(null)))}function Be(t,e){var n,i,r,o,s,a=t.tagName.toLowerCase();if("input"==a&&("checkbox"==t.type||"radio"==t.type))return Re(t,e);if(n=t.parentNode,i=t.ownerDocument,r=i.createElement(_r),r.style.cssText=Te(oe(t)),"input"==a&&(r.style.whiteSpace="pre"),"select"!=a&&"textarea"!=a||(r.style.overflow="auto"),"select"==a)if(t.multiple)for(s=0;s<t.options.length;++s)o=i.createElement(_r),o.style.cssText=Te(oe(t.options[s])),o.style.display="block",o.textContent=t.options[s].textContent,r.appendChild(o);else o=Ne(t),o&&(r.textContent=o.textContent);else r.textContent=t.value;n.insertBefore(r,t),r.scrollLeft=t.scrollLeft,r.scrollTop=t.scrollTop,t.style.display="none",Le(r,e),t.style.display="",n.removeChild(r)}function Le(t,e){var n,i,r,o,s,a,h;switch(Cr._stackingContext.element===t&&(Cr._stackingContext.group=e),t.tagName.toLowerCase()){case"img":Pe(t,t.src,e);break;case"canvas":try{Pe(t,t.toDataURL("image/png"),e)}catch(l){}break;case"textarea":case"input":case"select":Be(t,e);break;default:for(n=[],i=[],r=[],o=t.firstChild;o;o=o.nextSibling)switch(o.nodeType){case 3:/\S/.test(o.data)&&Fe(t,o,e);break;case 1:s=oe(o),a=se(s,"float"),h=se(s,"position"),"static"!=h?r.push(o):"none"!=a?i.push(o):n.push(o)}f(n,Oe).forEach(function(t){De(t,e)}),f(i,Oe).forEach(function(t){De(t,e)}),f(r,Oe).forEach(function(t){De(t,e)})}}function Fe(t,e,n){function i(t){var e,n,i,r,o;if(Tr||wr.chrome){for(e=t.getClientRects(),n={top:1/0,right:-(1/0),bottom:-(1/0),left:1/0},i=!1,r=0;r<e.length;++r)o=e[r],o.width<=1||o.bottom===b||(n.left=Math.min(o.left,n.left),n.top=Math.min(o.top,n.top),n.right=Math.max(o.right,n.right),n.bottom=Math.max(o.bottom,n.bottom),i=!0);return i?(n.width=n.right-n.left,n.height=n.bottom-n.top,n):t.getBoundingClientRect()}return t.getBoundingClientRect()}function r(){var t,n,r,s,a,u,d,f=l,p=h.substr(l).search(/\S/);if(l+=p,p<0||l>=c)return!0;if(g.setStart(e,l),g.setEnd(e,l+1),t=i(g),n=!1,(v||x>1)&&(p=h.substr(l).search(/\s/),p>=0&&(g.setEnd(e,l+p),r=i(g),r.bottom==t.bottom&&(t=r,n=!0,l+=p))),!n){if(p=function m(n,r,o){g.setEnd(e,r);var s=i(g);return s.bottom!=t.bottom&&n<r?m(n,n+r>>1,r):s.right!=t.right?(t=s,r<o?m(r,r+o>>1,o):r):r}(l,Math.min(c,l+_),c),p==l)return!0;if(l=p,p=(""+g).search(/\s+$/),0===p)return!1;p>0&&(g.setEnd(e,g.startOffset+p),t=i(g))}if(Tr&&(t=g.getClientRects()[0]),s=""+g,/^(?:pre|pre-wrap)$/i.test(y)){if(/\t/.test(s)){for(a=0,p=f;p<g.startOffset;++p)u=h.charCodeAt(p),9==u?a+=8-a%8:10==u||13==u?a=0:a++;for(;(p=s.search("\t"))>=0;)d="        ".substr(0,8-(a+p)%8),s=s.substr(0,p)+d+s.substr(p+1)}}else s=s.replace(/\s+/g," ");n||(b=t.bottom),o(s,t)}function o(t,e){var i,r,o;switch(Tr&&!isNaN(d)&&(i=zr(f),r=(e.top+e.bottom-i)/2,e={top:r,right:e.right,bottom:r+i,left:e.left,height:i,width:e.right-e.left}),w){case"uppercase":t=t.toUpperCase();break;case"lowercase":t=t.toLowerCase();break;case"capitalize":t=t.replace(/(?:^|\s)\S/g,function(t){return t.toUpperCase()})}o=new Mr(t,new un([e.left,e.top],[e.width,e.height]),{font:f,fill:{color:p}}),n.append(o)}function s(t){function e(e,i){var r,o;e&&(r=u/12,o=new $n({stroke:{width:r,color:e}}),i-=r,o.moveTo(t.left,i).lineTo(t.right,i),n.append(o))}e(C,t.bottom),e(k,t.bottom-t.height/2.7),e(T,t.top)}var a,h,l,c,u,d,f,p,g,m,v,x,y,w,_,b,C,k,T,M;if(!ie()&&(a=oe(t),!(parseFloat(se(a,"text-indent"))<-500)&&(h=e.data,l=0,c=h.search(/\S\s*$/)+1,c&&(u=se(a,"font-size"),d=se(a,"line-height"),f=[se(a,"font-style"),se(a,"font-variant"),se(a,"font-weight"),u,se(a,"font-family")].join(" "),u=parseFloat(u),d=parseFloat(d),0!==u)))){for(p=se(a,"color"),g=t.ownerDocument.createRange(),m=se(a,"text-align"),v="justify"==m,x=se(a,"column-count",1),y=se(a,"white-space"),w=se(a,"text-transform"),_=t.getBoundingClientRect().width/u*5,0===_&&(_=500),b=null,C=Cr.underline,k=Cr["line-through"],T=Cr.overline,M=C||k||T;!r(););M&&(g.selectNode(e),Bt(g.getClientRects()).forEach(s))}}function Ie(t,e,n){var i,r,o,s,a,h;for("auto"!=n?(i=Cr._stackingContext.group,n=parseFloat(n)):(i=e,n=0),r=i.children,o=0;o<r.length&&!(null!=r[o]._dom_zIndex&&r[o]._dom_zIndex>n);++o);return s=new Jn,i.insert(o,s),s._dom_zIndex=n,i!==e&&Cr._clipbox&&(a=Cr._matrix.invert(),h=Cr._clipbox.transformCopy(a),ve(s,$n.fromRect(h))),s}function De(t,e){var n,i,r,o,s,a,h=oe(t);Wt(h),/^(style|script|link|meta|iframe|svg|col|colgroup)$/i.test(t.tagName)||null!=Cr._clipbox&&(n=parseFloat(se(h,"opacity")),i=se(h,"visibility"),r=se(h,"display"),0!==n&&"hidden"!=i&&"none"!=r&&(o=fe(h),a=se(h,"z-index"),(o||n<1)&&"auto"==a&&(a=0),s=Ie(t,e,a),n<1&&s.opacity(n*s.opacity()),te(t,h,s),o?le(t,function(){var e,n,i,r;ae(t.style,"transform","none","important"),ae(t.style,"transition","none","important"),"static"==se(h,"position")&&ae(t.style,"position","relative","important"),e=t.getBoundingClientRect(),n=e.left+o.origin[0],i=e.top+o.origin[1],r=[1,0,0,1,-n,-i],r=He(r,o.matrix),r=He(r,[1,0,0,1,n,i]),r=me(s,r),Cr._matrix=Cr._matrix.multiplyCopy(r),Me(t,s)}):Me(t,s),ee()))}function He(t,e){var n=t[0],i=t[1],r=t[2],o=t[3],s=t[4],a=t[5],h=e[0],l=e[1],c=e[2],u=e[3],d=e[4],f=e[5];return[n*h+i*c,n*l+i*u,r*h+o*c,r*l+o*u,s*h+a*c+d,s*l+a*u+f]}var je,qe,Ge,$e,Ve,Ue,Xe,Ye,We,Qe,Ze,Je,Ke,tn,en,nn,rn,on,sn,an,hn,ln,cn,un,dn,fn,pn,gn,mn,vn,xn,yn,wn,_n,bn,Cn,kn,Tn,Mn,Sn,En,An,Pn,On,zn,Nn,Rn,Bn,Ln,Fn,In,Dn,Hn,jn,qn,Gn,$n,Vn,Un,Xn,Yn,Wn,Qn,Zn,Jn,Kn,ti,ei,ni,ii,ri,oi,si,ai,hi,li,ci,ui,di,fi,pi,gi,mi,vi,xi,yi,wi,_i,bi,Ci,ki,Ti,Mi,Si,Ei,Ai,Pi,Oi,zi,Ni,Ri,Bi,Li,Fi,Ii,Di,Hi,ji,qi,Gi,$i,Vi,Ui,Xi,Yi,Wi,Qi,Zi,Ji,Ki,tr,er,nr,ir,rr,or,sr,ar,hr,lr,cr,ur,dr,fr,pr,gr,mr,vr,xr,yr,wr,_r,br,Cr,kr,Tr,Mr,Sr,Er,Ar,Pr,Or,zr,Nr;window.kendo=window.kendo||{},je=kendo.drawing,qe=je.util,Ge=kendo.Class,$e=kendo.util,Ve=kendo.support,Ue=Ve.browser,Xe=qe.createPromise,Ye=qe.promiseAll,We={extend:function(t){var e,n=this;for(e in this)"extend"!==e&&(t[e]=n[e])},observers:function(){return this._observers=this._observers||[]},addObserver:function(t){return this._observers?this._observers.push(t):this._observers=[t],this},removeObserver:function(t){var e=this.observers(),n=e.indexOf(t);return n!==-1&&e.splice(n,1),this},trigger:function(t,e){var n,i,r=this._observers;if(r&&!this._suspended)for(n=0;n<r.length;n++)i=r[n],i[t]&&i[t](e);return this},optionsChange:function(t){void 0===t&&(t={}),t.element=this,this.trigger("optionsChange",t)},geometryChange:function(){this.trigger("geometryChange",{element:this})},suspend:function(){return this._suspended=(this._suspended||0)+1,this},resume:function(){return this._suspended=Math.max((this._suspended||0)-1,0),this},_observerField:function(t,e){this[t]&&this[t].removeObserver(this),this[t]=e,e.addObserver(this)}},Qe={1:"i",10:"x",100:"c",2:"ii",20:"xx",200:"cc",3:"iii",30:"xxx",300:"ccc",4:"iv",40:"xl",400:"cd",5:"v",50:"l",500:"d",6:"vi",60:"lx",600:"dc",7:"vii",70:"lxx",700:"dcc",8:"viii",80:"lxxx",800:"dccc",9:"ix",90:"xc",900:"cm",1e3:"m"},Ze="undefined",Je=1,Ke=Math.PI/180,tn=Number.MAX_VALUE,en=-Number.MAX_VALUE,nn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",rn=String.fromCharCode,on={append:e,arabicToRoman:n,createPromise:Xe,defined:i,definitionId:r,deg:o,encodeBase64:a,eventCoordinates:h,eventElement:l,isTransparent:c,last:u,limitValue:d,mergeSort:f,promiseAll:Ye,rad:p,round:m,valueOrDefault:v,bindEvents:x,elementOffset:y,elementSize:b,elementStyles:w,unbindEvents:C,DEG_TO_RAD:Ke,MAX_NUM:tn,MIN_NUM:en},sn={}.toString,an=Ge.extend({init:function(t,e){var n,i,r=this;void 0===e&&(e=""),this.prefix=e;for(n in t)i=t[n],i=r._wrap(i,n),r[n]=i},get:function(t){for(var e,n=t.split("."),i=this;n.length&&i;)e=n.shift(),i=i[e];return i},set:function(t,e){var n=this.get(t);n!==e&&(this._set(t,this._wrap(e,t)),this.optionsChange({field:this.prefix+t,value:e}))},_set:function(t,e){var n,i,r,o=this,s=t.indexOf(".")>=0,a=this,h=t;if(s){for(n=h.split("."),i=this.prefix;n.length>1;)h=n.shift(),i+=h+".",r=a[h],r||(r=new an({},i),r.addObserver(o),a[h]=r),a=r;h=n[0]}a._clear(h),a[h]=e},_clear:function(t){var e=this[t];e&&e.removeObserver&&e.removeObserver(this)},_wrap:function(t,e){var n=sn.call(t),r=t;return null!==r&&i(r)&&"[object Object]"===n&&(t instanceof an||t instanceof Ge||(r=new an(r,this.prefix+e+".")),r.addObserver(this)),r}}),We.extend(an.prototype),hn=Ge.extend({init:function(t,e,n,i,r,o){void 0===t&&(t=0),void 0===e&&(e=0),void 0===n&&(n=0),void 0===i&&(i=0),void 0===r&&(r=0),void 0===o&&(o=0),this.a=t,this.b=e,this.c=n,this.d=i,this.e=r,this.f=o},multiplyCopy:function(t){return new hn(this.a*t.a+this.c*t.b,this.b*t.a+this.d*t.b,this.a*t.c+this.c*t.d,this.b*t.c+this.d*t.d,this.a*t.e+this.c*t.f+this.e,this.b*t.e+this.d*t.f+this.f)},invert:function(){var t=this,e=t.a,n=t.b,i=t.c,r=t.d,o=t.e,s=t.f,a=e*r-n*i;return 0===a?null:new hn(r/a,-n/a,-i/a,e/a,(i*s-r*o)/a,(n*o-e*s)/a)},clone:function(){return new hn(this.a,this.b,this.c,this.d,this.e,this.f)},equals:function(t){return!!t&&(this.a===t.a&&this.b===t.b&&this.c===t.c&&this.d===t.d&&this.e===t.e&&this.f===t.f)},round:function(t){return this.a=m(this.a,t),this.b=m(this.b,t),this.c=m(this.c,t),this.d=m(this.d,t),this.e=m(this.e,t),this.f=m(this.f,t),this},toArray:function(t){var e,n=[this.a,this.b,this.c,this.d,this.e,this.f];if(i(t))for(e=0;e<n.length;e++)n[e]=m(n[e],t);return n},toString:function(t,e){return void 0===e&&(e=","),this.toArray(t).join(e)}}),hn.translate=function(t,e){return new hn(1,0,0,1,t,e)},hn.unit=function(){return new hn(1,0,0,1,0,0)},hn.rotate=function(t,e,n){var i=new hn;return i.a=Math.cos(p(t)),i.b=Math.sin(p(t)),i.c=-i.b,i.d=i.a,i.e=e-e*i.a+n*i.b||0,i.f=n-n*i.a-e*i.b||0,i},hn.scale=function(t,e){return new hn(t,0,0,e,0,0)},hn.IDENTITY=hn.unit(),ln=Ge.extend({init:function(t,e){this.x=t||0,this.y=e||0},equals:function(t){return t&&t.x===this.x&&t.y===this.y},clone:function(){return new ln(this.x,this.y)},rotate:function(t,e){var n=ln.create(e)||ln.ZERO;return this.transform(hn.rotate(t,n.x,n.y))},translate:function(t,e){return this.x+=t,this.y+=e,this.geometryChange(),this},translateWith:function(t){return this.translate(t.x,t.y)},move:function(t,e){return this.x=this.y=0,this.translate(t,e)},scale:function(t,e){return void 0===e&&(e=t),this.x*=t,this.y*=e,this.geometryChange(),this},scaleCopy:function(t,e){return this.clone().scale(t,e)},transform:function(t){var e=S(t),n=this,i=n.x,r=n.y;return this.x=e.a*i+e.c*r+e.e,this.y=e.b*i+e.d*r+e.f,this.geometryChange(),this},transformCopy:function(t){var e=this.clone();return t&&e.transform(t),e},distanceTo:function(t){var e=this.x-t.x,n=this.y-t.y;return Math.sqrt(e*e+n*n)},round:function(t){return this.x=m(this.x,t),this.y=m(this.y,t),this.geometryChange(),this},toArray:function(t){var e=i(t),n=e?m(this.x,t):this.x,r=e?m(this.y,t):this.y;return[n,r]},toString:function(t,e){var n,r,o;return void 0===e&&(e=" "),n=this,r=n.x,o=n.y,i(t)&&(r=m(r,t),o=m(o,t)),r+e+o}}),ln.create=function(t,e){if(i(t))return t instanceof ln?t:1===arguments.length&&2===t.length?new ln(t[0],t[1]):new ln(t,e)},ln.min=function(){var t,e,n=arguments,i=tn,r=tn;for(t=0;t<arguments.length;t++)e=n[t],i=Math.min(e.x,i),r=Math.min(e.y,r);return new ln(i,r)},ln.max=function(){var t,e,n=arguments,i=en,r=en;for(t=0;t<arguments.length;t++)e=n[t],i=Math.max(e.x,i),r=Math.max(e.y,r);return new ln(i,r)},ln.minPoint=function(){return new ln(en,en)},ln.maxPoint=function(){return new ln(tn,tn)},Object.defineProperties&&Object.defineProperties(ln,{ZERO:{get:function(){return new ln(0,0)}}}),M(ln.prototype,["x","y"]),We.extend(ln.prototype),cn=Ge.extend({init:function(t,e){this.width=t||0,this.height=e||0},equals:function(t){return t&&t.width===this.width&&t.height===this.height},clone:function(){return new cn(this.width,this.height)},toArray:function(t){var e=i(t),n=e?m(this.width,t):this.width,r=e?m(this.height,t):this.height;return[n,r]}}),cn.create=function(t,e){if(i(t))return t instanceof cn?t:1===arguments.length&&2===t.length?new cn(t[0],t[1]):new cn(t,e)},Object.defineProperties&&Object.defineProperties(cn,{ZERO:{get:function(){return new cn(0,0)}}}),M(cn.prototype,["width","height"]),We.extend(cn.prototype),un=Ge.extend({init:function(t,e){void 0===t&&(t=new ln),void 0===e&&(e=new cn),this.setOrigin(t),this.setSize(e)},clone:function(){return new un(this.origin.clone(),this.size.clone())},equals:function(t){return t&&t.origin.equals(this.origin)&&t.size.equals(this.size)},setOrigin:function(t){return this._observerField("origin",ln.create(t)),this.geometryChange(),this},getOrigin:function(){return this.origin},setSize:function(t){return this._observerField("size",cn.create(t)),this.geometryChange(),this},getSize:function(){return this.size},width:function(){return this.size.width},height:function(){return this.size.height},topLeft:function(){return this.origin.clone()},bottomRight:function(){return this.origin.clone().translate(this.width(),this.height())},topRight:function(){return this.origin.clone().translate(this.width(),0)},bottomLeft:function(){return this.origin.clone().translate(0,this.height())},center:function(){return this.origin.clone().translate(this.width()/2,this.height()/2)},bbox:function(t){var e=this.topLeft().transformCopy(t),n=this.topRight().transformCopy(t),i=this.bottomRight().transformCopy(t),r=this.bottomLeft().transformCopy(t);return un.fromPoints(e,n,i,r)},transformCopy:function(t){return un.fromPoints(this.topLeft().transform(t),this.bottomRight().transform(t))},expand:function(t,e){return void 0===e&&(e=t),this.size.width+=2*t,this.size.height+=2*e,this.origin.translate(-t,-e),this},expandCopy:function(t,e){return this.clone().expand(t,e)},containsPoint:function(t){var e=this.origin,n=this.bottomRight();return!(t.x<e.x||t.y<e.y||n.x<t.x||n.y<t.y)},_isOnPath:function(t,e){var n=this.expandCopy(e,e),i=this.expandCopy(-e,-e);return n.containsPoint(t)&&!i.containsPoint(t)}}),un.fromPoints=function(){var t=ln.min.apply(null,arguments),e=ln.max.apply(null,arguments),n=new cn(e.x-t.x,e.y-t.y);return new un(t,n)},un.union=function(t,e){return un.fromPoints(ln.min(t.topLeft(),e.topLeft()),ln.max(t.bottomRight(),e.bottomRight()))},un.intersect=function(t,e){var n={left:t.topLeft().x,top:t.topLeft().y,right:t.bottomRight().x,bottom:t.bottomRight().y},i={left:e.topLeft().x,top:e.topLeft().y,right:e.bottomRight().x,bottom:e.bottomRight().y};if(n.left<=i.right&&i.left<=n.right&&n.top<=i.bottom&&i.top<=n.bottom)return un.fromPoints(new ln(Math.max(n.left,i.left),Math.max(n.top,i.top)),new ln(Math.min(n.right,i.right),Math.min(n.bottom,i.bottom)))},We.extend(un.prototype),dn=Ge.extend({init:function(t){void 0===t&&(t=hn.unit()),this._matrix=t},clone:function(){return new dn(this._matrix.clone())},equals:function(t){return t&&t._matrix.equals(this._matrix)},translate:function(t,e){return this._matrix=this._matrix.multiplyCopy(hn.translate(t,e)),this._optionsChange(),this},scale:function(t,e,n){void 0===e&&(e=t),void 0===n&&(n=null);var i=n;return i&&(i=ln.create(i),this._matrix=this._matrix.multiplyCopy(hn.translate(i.x,i.y))),this._matrix=this._matrix.multiplyCopy(hn.scale(t,e)),i&&(this._matrix=this._matrix.multiplyCopy(hn.translate(-i.x,-i.y))),this._optionsChange(),this},rotate:function(t,e){var n=ln.create(e)||ln.ZERO;return this._matrix=this._matrix.multiplyCopy(hn.rotate(t,n.x,n.y)),this._optionsChange(),this},multiply:function(t){var e=S(t);return this._matrix=this._matrix.multiplyCopy(e),this._optionsChange(),this},matrix:function(t){return t?(this._matrix=t,this._optionsChange(),this):this._matrix},_optionsChange:function(){this.optionsChange({field:"transform",value:this})}}),We.extend(dn.prototype),fn=Ge.extend({init:function(t){this._initOptions(t)},_initOptions:function(t){var e,n;void 0===t&&(t={}),e=t.clip,n=t.transform,n&&(t.transform=E(n)),e&&!e.id&&(e.id=r()),this.options=new an(t),this.options.addObserver(this)},transform:function(t){return i(t)?void this.options.set("transform",E(t)):this.options.get("transform")},parentTransform:function(){for(var t,e,n=this;n.parent;)n=n.parent,e=n.transform(),e&&(t=e.matrix().multiplyCopy(t||hn.unit()));if(t)return E(t)},currentTransform:function(t){var e,n,i,r;if(void 0===t&&(t=this.parentTransform()),e=this.transform(),n=S(e),i=S(t),r=n&&i?i.multiplyCopy(n):n||i)return E(r)},visible:function(t){return i(t)?(this.options.set("visible",t),this):this.options.get("visible")!==!1},clip:function(t){var e=this.options;return i(t)?(t&&!t.id&&(t.id=r()),e.set("clip",t),this):e.get("clip")},opacity:function(t){return i(t)?(this.options.set("opacity",t),this):v(this.options.get("opacity"),1)},clippedBBox:function(t){var e,n=this._clippedBBox(t);if(n)return e=this.clip(),e?un.intersect(n,e.bbox(t)):n},containsPoint:function(t,e){var n,i;return!!this.visible()&&(n=this.currentTransform(e),i=t,n&&(i=t.transformCopy(n.matrix().invert())),this._hasFill()&&this._containsPoint(i)||this._isOnPath&&this._hasStroke()&&this._isOnPath(i))},_hasFill:function(){var t=this.options.fill;return t&&!c(t.color)},_hasStroke:function(){var t=this.options.stroke;return t&&t.width>0&&!c(t.color)},_clippedBBox:function(t){return this.bbox(t)}}),fn.prototype.nodeType="Element",We.extend(fn.prototype),pn=Math.PI/2,gn=Ge.extend({init:function(t,e){void 0===t&&(t=new ln),void 0===e&&(e=0),this.setCenter(t),this.setRadius(e)},setCenter:function(t){return this._observerField("center",ln.create(t)),this.geometryChange(),this},getCenter:function(){return this.center},equals:function(t){return t&&t.center.equals(this.center)&&t.radius===this.radius},clone:function(){return new gn(this.center.clone(),this.radius)},pointAt:function(t){return this._pointAt(p(t))},bbox:function(t){var e,n,i,r,o=this,s=A(this.center,this.radius,this.radius,t),a=ln.maxPoint(),h=ln.minPoint();for(e=0;e<4;e++)n=o._pointAt(s.x+e*pn).transformCopy(t),i=o._pointAt(s.y+e*pn).transformCopy(t),r=new ln(n.x,i.y),a=ln.min(a,r),h=ln.max(h,r);return un.fromPoints(a,h)},_pointAt:function(t){var e=this,n=e.center,i=e.radius;return new ln(n.x+i*Math.cos(t),n.y+i*Math.sin(t))},containsPoint:function(t){var e=this,n=e.center,i=e.radius,r=Math.pow(t.x-n.x,2)+Math.pow(t.y-n.y,2)<=Math.pow(i,2);return r},_isOnPath:function(t,e){var n=this,i=n.center,r=n.radius,o=i.distanceTo(t);return r-e<=o&&o<=r+e}}),M(gn.prototype,["radius"]),We.extend(gn.prototype),mn="Gradient",vn={extend:function(t){t.fill=this.fill,t.stroke=this.stroke},fill:function(t,e){var n,r=this.options;return i(t)?(t&&t.nodeType!==mn?(n={color:t},i(e)&&(n.opacity=e),r.set("fill",n)):r.set("fill",t),this):r.get("fill")},stroke:function(t,e,n){return i(t)?(this.options.set("stroke.color",t),i(e)&&this.options.set("stroke.width",e),i(n)&&this.options.set("stroke.opacity",n),this):this.options.get("stroke")}},xn=""+hn.IDENTITY,yn={extend:function(t){t.bbox=this.bbox,t.geometryChange=this.geometryChange},bbox:function(t){var e,n,i=S(this.currentTransform(t)),r=i?""+i:xn;return this._bboxCache&&this._matrixHash===r?e=this._bboxCache.clone():(e=this._bbox(i),this._bboxCache=e?e.clone():null,this._matrixHash=r),n=this.options.get("stroke.width"),n&&e&&e.expand(n/2),e},geometryChange:function(){delete this._bboxCache,this.trigger("geometryChange",{element:this})}},wn="#000",_n=fn.extend({init:function(t,e){void 0===t&&(t=new gn),void 0===e&&(e={}),fn.fn.init.call(this,e),this.geometry(t),i(this.options.stroke)||this.stroke(wn)},rawBBox:function(){return this._geometry.bbox()},_bbox:function(t){return this._geometry.bbox(t)},_containsPoint:function(t){return this.geometry().containsPoint(t)},_isOnPath:function(t){return this.geometry()._isOnPath(t,this.options.stroke.width/2)}}),_n.prototype.nodeType="Circle",vn.extend(_n.prototype),yn.extend(_n.prototype),O(_n.prototype,["geometry"]),bn=10,Cn=45,kn=Math.pow,Tn=Ge.extend({init:function(t,e){void 0===t&&(t=new ln),void 0===e&&(e={}),this.setCenter(t),this.radiusX=e.radiusX,this.radiusY=e.radiusY||e.radiusX,this.startAngle=e.startAngle,this.endAngle=e.endAngle,this.anticlockwise=e.anticlockwise||!1,this.xRotation=e.xRotation},clone:function(){return new Tn(this.center,{radiusX:this.radiusX,radiusY:this.radiusY,startAngle:this.startAngle,endAngle:this.endAngle,anticlockwise:this.anticlockwise})},setCenter:function(t){return this._observerField("center",ln.create(t)),this.geometryChange(),this},getCenter:function(){return this.center},pointAt:function(t){var e=this.center,n=p(t);return new ln(e.x+this.radiusX*Math.cos(n),e.y+this.radiusY*Math.sin(n))},curvePoints:function(){var t,e,n,i,r=this,o=this.startAngle,s=this.anticlockwise?-1:1,a=[this.pointAt(o)],h=this._arcInterval(),l=h.endAngle-h.startAngle,c=Math.ceil(l/Cn),u=l/c,d=o;for(this.xRotation&&(t=E().rotate(this.xRotation,this.center)),e=1;e<=c;e++)n=d+s*u,i=r._intervalCurvePoints(d,n,t),a.push(i.cp1,i.cp2,i.p2),d=n;return a},bbox:function(t){for(var e,n,i=this,r=this._arcInterval(),s=r.startAngle,a=r.endAngle,h=A(this.center,this.radiusX,this.radiusY,t),l=o(h.x),c=o(h.y),u=this.pointAt(a).transformCopy(t),d=F(l,s),f=F(c,s),p=this.pointAt(s).transformCopy(t),g=ln.min(p,u),m=ln.max(p,u);d<a||f<a;)e=void 0,d<a&&(e=i.pointAt(d).transformCopy(t),d+=90),n=void 0,f<a&&(n=i.pointAt(f).transformCopy(t),f+=90),p=new ln(e.x,n.y),g=ln.min(g,p),m=ln.max(m,p);return un.fromPoints(g,m)},_arcInterval:function(){var t,e=this,n=e.startAngle,i=e.endAngle,r=e.anticlockwise;return r&&(t=n,n=i,i=t),(n>i||r&&n===i)&&(i+=360),{startAngle:n,endAngle:i}},_intervalCurvePoints:function(t,e,n){var i=this.pointAt(t),r=this.pointAt(e),o=this._derivativeAt(t),s=this._derivativeAt(e),a=(p(e)-p(t))/3,h=new ln(i.x+a*o.x,i.y+a*o.y),l=new ln(r.x-a*s.x,r.y-a*s.y);return n&&(i.transform(n),r.transform(n),h.transform(n),l.transform(n)),{p1:i,cp1:h,cp2:l,p2:r}},_derivativeAt:function(t){var e=p(t);return new ln(-this.radiusX*Math.sin(e),this.radiusY*Math.cos(e))},containsPoint:function(t){var e,n,i,r=this._arcInterval(),o=r.endAngle-r.startAngle,s=this,a=s.center,h=s.radiusX,l=s.radiusY,c=a.distanceTo(t),u=Math.atan2(t.y-a.y,t.x-a.x),d=h*l/Math.sqrt(kn(h,2)*kn(Math.sin(u),2)+kn(l,2)*kn(Math.cos(u),2)),f=this.pointAt(this.startAngle).round(bn),p=this.pointAt(this.endAngle).round(bn),g=R(a,t.round(bn),f,p);return o<180?e=g&&N(a.distanceTo(g),c)&&N(c,d):(n=B(a.x,a.y,h,l,t.x,t.y),360!==n&&(n=(360+n)%360),i=r.startAngle<=n&&n<=r.endAngle,e=i&&N(c,d)||!i&&(!g||g.equals(t))),e},_isOnPath:function(t,e){var n,i=this._arcInterval(),r=this.center,o=B(r.x,r.y,this.radiusX,this.radiusY,t.x,t.y);return 360!==o&&(o=(360+o)%360),n=i.startAngle<=o&&o<=i.endAngle,n&&this.pointAt(o).distanceTo(t)<=e}}),Tn.fromPoints=function(t,e,n,i,r,o,s){var a=L({x1:t.x,y1:t.y,x2:e.x,y2:e.y,rx:n,ry:i,largeArc:r,swipe:o,rotation:s});return new Tn(a.center,{startAngle:a.startAngle,endAngle:a.endAngle,radiusX:a.radiusX,radiusY:a.radiusY,xRotation:a.xRotation,anticlockwise:0===o})},M(Tn.prototype,["radiusX","radiusY","startAngle","endAngle","anticlockwise"]),We.extend(Tn.prototype),Mn=[].push,Sn=[].pop,En=[].splice,An=[].shift,Pn=[].slice,On=[].unshift,zn=Ge.extend({init:function(t){void 0===t&&(t=[]),this.length=0,this._splice(0,t.length,t)},elements:function(t){return t?(this._splice(0,this.length,t),this._change(),this):this.slice(0)},push:function(){var t=arguments,e=Mn.apply(this,t);return this._add(t),e},slice:function(){return Pn.call(this)},pop:function(){var t=this.length,e=Sn.apply(this);return t&&this._remove([e]),e},splice:function(t,e){var n=Pn.call(arguments,2),i=this._splice(t,e,n);return this._change(),i},shift:function(){var t=this.length,e=An.apply(this);return t&&this._remove([e]),e},unshift:function(){var t=arguments,e=On.apply(this,t);return this._add(t),e},indexOf:function(t){var e,n=this,i=this.length;for(e=0;e<i;e++)if(n[e]===t)return e;return-1},_splice:function(t,e,n){var i=En.apply(this,[t,e].concat(n));return this._clearObserver(i),this._setObserver(n),i},_add:function(t){this._setObserver(t),this._change()},_remove:function(t){this._clearObserver(t),this._change()},_setObserver:function(t){var e,n=this;for(e=0;e<t.length;e++)t[e].addObserver(n)},_clearObserver:function(t){var e,n=this;for(e=0;e<t.length;e++)t[e].removeObserver(n)},_change:function(){}}),We.extend(zn.prototype),Nn=zn.extend({_change:function(){this.geometryChange()}}),Rn=Ge.extend({init:function(t,e){void 0===t&&(t=0),void 0===e&&(e=0),
this.real=t,this.img=e},add:function(t){return new Rn(m(this.real+t.real,bn),m(this.img+t.img,bn))},addConstant:function(t){return new Rn(this.real+t,this.img)},negate:function(){return new Rn((-this.real),(-this.img))},multiply:function(t){return new Rn(this.real*t.real-this.img*t.img,this.real*t.img+this.img*t.real)},multiplyConstant:function(t){return new Rn(this.real*t,this.img*t)},nthRoot:function(t){var e=Math.atan2(this.img,this.real),n=Math.sqrt(Math.pow(this.img,2)+Math.pow(this.real,2)),i=Math.pow(n,1/t);return new Rn(i*Math.cos(e/t),i*Math.sin(e/t))},equals:function(t){return this.real===t.real&&this.img===t.img},isReal:function(){return 0===this.img}}),Bn=Ge.extend({init:function(t,e,n){this.anchor(t||new ln),this.controlIn(e),this.controlOut(n)},bboxTo:function(t,e){var n,i=this.anchor().transformCopy(e),r=t.anchor().transformCopy(e);return n=this.controlOut()&&t.controlIn()?this._curveBoundingBox(i,this.controlOut().transformCopy(e),t.controlIn().transformCopy(e),r):this._lineBoundingBox(i,r)},_lineBoundingBox:function(t,e){return un.fromPoints(t,e)},_curveBoundingBox:function(t,e,n,i){var r=[t,e,n,i],o=this._curveExtremesFor(r,"x"),s=this._curveExtremesFor(r,"y"),a=W([o.min,o.max,t.x,i.x]),h=W([s.min,s.max,t.y,i.y]);return un.fromPoints(new ln(a.min,h.min),new ln(a.max,h.max))},_curveExtremesFor:function(t,e){var n=this._curveExtremes(t[0][e],t[1][e],t[2][e],t[3][e]);return{min:j(n.min,e,t),max:j(n.max,e,t)}},_curveExtremes:function(t,e,n,i){var r,o,s=t-3*e+3*n-i,a=-2*(t-2*e+n),h=t-e,l=Math.sqrt(a*a-4*s*h),c=0,u=1;return 0===s?0!==a&&(c=u=-h/a):isNaN(l)||(c=(-a+l)/(2*s),u=(-a-l)/(2*s)),r=Math.max(Math.min(c,u),0),(r<0||r>1)&&(r=0),o=Math.min(Math.max(c,u),1),(o>1||o<0)&&(o=1),{min:r,max:o}},_intersectionsTo:function(t,e){var n;return n=this.controlOut()&&t.controlIn()?X([this.anchor(),this.controlOut(),t.controlIn(),t.anchor()],e,this.bboxTo(t)):Y(this.anchor(),t.anchor(),e)},_isOnCurveTo:function(t,e,n,i){var r,o,s,a,h,l,c,u=this.bboxTo(t).expand(n,n);if(u.containsPoint(e))return r=this.anchor(),o=this.controlOut(),s=t.controlIn(),a=t.anchor(),"start"===i&&r.distanceTo(e)<=n?!H(r,o,e):"end"===i&&a.distanceTo(e)<=n?!H(a,s,e):(h=[r,o,s,a],!(!U(h,e,"x","y",n)&&!U(h,e,"y","x",n))||(l=E().rotate(45,e),c=[r.transformCopy(l),o.transformCopy(l),s.transformCopy(l),a.transformCopy(l)],U(c,e,"x","y",n)||U(c,e,"y","x",n)))},_isOnLineTo:function(t,e,n){var i=this.anchor(),r=t.anchor(),s=o(Math.atan2(r.y-i.y,r.x-i.x)),a=new un([i.x,i.y-n/2],[i.distanceTo(r),n]);return a.containsPoint(e.transformCopy(E().rotate(-s,i)))},_isOnPathTo:function(t,e,n,i){var r;return r=this.controlOut()&&t.controlIn()?this._isOnCurveTo(t,e,n/2,i):this._isOnLineTo(t,e,n)}}),D(Bn.prototype,["anchor","controlIn","controlOut"]),We.extend(Bn.prototype),Ln=fn.extend({init:function(t){fn.fn.init.call(this,t),this.paths=new Nn,this.paths.addObserver(this),i(this.options.stroke)||this.stroke("#000")},moveTo:function(t,e){var n=new $n;return n.moveTo(t,e),this.paths.push(n),this},lineTo:function(t,e){return this.paths.length>0&&u(this.paths).lineTo(t,e),this},curveTo:function(t,e,n){return this.paths.length>0&&u(this.paths).curveTo(t,e,n),this},arc:function(t,e,n,i,r){return this.paths.length>0&&u(this.paths).arc(t,e,n,i,r),this},arcTo:function(t,e,n,i,r,o){return this.paths.length>0&&u(this.paths).arcTo(t,e,n,i,r,o),this},close:function(){return this.paths.length>0&&u(this.paths).close(),this},_bbox:function(t){return Q(this.paths,!0,t)},rawBBox:function(){return Q(this.paths,!1)},_containsPoint:function(t){var e,n=this.paths;for(e=0;e<n.length;e++)if(n[e]._containsPoint(t))return!0;return!1},_isOnPath:function(t){var e,n=this.paths,i=this.options.stroke.width;for(e=0;e<n.length;e++)if(n[e]._isOnPath(t,i))return!0;return!1},_clippedBBox:function(t){return Z(this.paths,this.currentTransform(t))}}),Ln.prototype.nodeType="MultiPath",vn.extend(Ln.prototype),yn.extend(Ln.prototype),Fn={l:function(t,e){var n,i,r=e.parameters,o=e.position;for(n=0;n<r.length;n+=2)i=new ln(r[n],r[n+1]),e.isRelative&&i.translateWith(o),t.lineTo(i.x,i.y),o.x=i.x,o.y=i.y},c:function(t,e){var n,i,r,o,s=e.parameters,a=e.position;for(n=0;n<s.length;n+=6)i=new ln(s[n],s[n+1]),r=new ln(s[n+2],s[n+3]),o=new ln(s[n+4],s[n+5]),e.isRelative&&(r.translateWith(a),i.translateWith(a),o.translateWith(a)),t.curveTo(i,r,o),a.x=o.x,a.y=o.y},v:function(t,e){var n=e.isRelative?0:e.position.x;J(e.parameters,!0,n),this.l(t,e)},h:function(t,e){var n=e.isRelative?0:e.position.y;J(e.parameters,!1,n),this.l(t,e)},a:function(t,e){var n,i,r,o,s,a,h,l=e.parameters,c=e.position;for(n=0;n<l.length;n+=7)i=l[n],r=l[n+1],o=l[n+2],s=l[n+3],a=l[n+4],h=new ln(l[n+5],l[n+6]),e.isRelative&&h.translateWith(c),c.x===h.x&&c.y===h.y||(t.arcTo(h,i,r,s,a,o),c.x=h.x,c.y=h.y)},s:function(t,e){var n,i,r,o,s,a=e.parameters,h=e.position,l=e.previousCommand;for("s"!==l&&"c"!==l||(n=u(u(t.paths).segments).controlIn()),i=0;i<a.length;i+=4)r=new ln(a[i],a[i+1]),o=new ln(a[i+2],a[i+3]),s=void 0,e.isRelative&&(r.translateWith(h),o.translateWith(h)),s=n?K(n,h):h.clone(),n=r,t.curveTo(s,r,o),h.x=o.x,h.y=o.y},q:function(t,e){var n,i,r,o,s=e.parameters,a=e.position;for(n=0;n<s.length;n+=4)i=new ln(s[n],s[n+1]),r=new ln(s[n+2],s[n+3]),e.isRelative&&(i.translateWith(a),r.translateWith(a)),o=tt(a,i,r),t.curveTo(o.controlOut,o.controlIn,r),a.x=r.x,a.y=r.y},t:function(t,e){var n,i,r,o,s,a=e.parameters,h=e.position,l=e.previousCommand;for("q"!==l&&"t"!==l||(i=u(u(t.paths).segments),n=i.controlIn().clone().translateWith(h.scaleCopy(-1/3)).scale(1.5)),r=0;r<a.length;r+=2)o=new ln(a[r],a[r+1]),e.isRelative&&o.translateWith(h),n=n?K(n,h):h.clone(),s=tt(h,n,o),t.curveTo(s.controlOut,s.controlIn,o),h.x=o.x,h.y=o.y}},In=1/3,Dn=/([a-df-z]{1})([^a-df-z]*)(z)?/gi,Hn=/[,\s]?([+\-]?(?:\d*\.\d+|\d+)(?:[eE][+\-]?\d+)?)/g,jn="m",qn="z",Gn=Ge.extend({parse:function(t,e){var n,i=new Ln(e),r=new ln;return t.replace(Dn,function(t,e,o,s){var a=e.toLowerCase(),h=a===e,l=et(o.trim());if(a===jn&&(h?(r.x+=l[0],r.y+=l[1]):(r.x=l[0],r.y=l[1]),i.moveTo(r.x,r.y),l.length>2&&(a="l",l.splice(0,2))),Fn[a])Fn[a](i,{parameters:l,position:r,isRelative:h,previousCommand:n}),s&&s.toLowerCase()===qn&&i.close();else if(a!==jn)throw Error("Error while parsing SVG path. Unsupported command: "+a);n=a}),i}}),Gn.current=new Gn,$n=fn.extend({init:function(t){fn.fn.init.call(this,t),this.segments=new Nn,this.segments.addObserver(this),i(this.options.stroke)||(this.stroke("#000"),i(this.options.stroke.lineJoin)||this.options.set("stroke.lineJoin","miter"))},moveTo:function(t,e){return this.suspend(),this.segments.elements([]),this.resume(),this.lineTo(t,e),this},lineTo:function(t,e){var n=i(e)?new ln(t,e):t,r=new Bn(n);return this.segments.push(r),this},curveTo:function(t,e,n){var i,r;return this.segments.length>0&&(i=u(this.segments),r=new Bn(n,e),this.suspend(),i.controlOut(t),this.resume(),this.segments.push(r)),this},arc:function(t,e,n,i,r){var o,s,a,h,l;return this.segments.length>0&&(o=u(this.segments),s=o.anchor(),a=p(t),h=new ln(s.x-n*Math.cos(a),s.y-i*Math.sin(a)),l=new Tn(h,{startAngle:t,endAngle:e,radiusX:n,radiusY:i,anticlockwise:r}),this._addArcSegments(l)),this},arcTo:function(t,e,n,i,r,o){var s,a,h;return this.segments.length>0&&(s=u(this.segments),a=s.anchor(),h=Tn.fromPoints(a,t,e,n,i,r,o),this._addArcSegments(h)),this},_addArcSegments:function(t){var e,n,i=this;for(this.suspend(),e=t.curvePoints(),n=1;n<e.length;n+=3)i.curveTo(e[n],e[n+1],e[n+2]);this.resume(),this.geometryChange()},close:function(){return this.options.closed=!0,this.geometryChange(),this},rawBBox:function(){return this._bbox()},_containsPoint:function(t){var e,n,i,r=this.segments,o=r.length,s=0;for(i=1;i<o;i++)e=r[i-1],n=r[i],s+=e._intersectionsTo(n,t);return!this.options.closed&&r[0].anchor().equals(r[o-1].anchor())||(s+=Y(r[0].anchor(),r[o-1].anchor(),t)),s%2!==0},_isOnPath:function(t,e){var n,i=this.segments,r=i.length,o=e||this.options.stroke.width;if(r>1){if(i[0]._isOnPathTo(i[1],t,o,"start"))return!0;for(n=2;n<=r-2;n++)if(i[n-1]._isOnPathTo(i[n],t,o))return!0;if(i[r-2]._isOnPathTo(i[r-1],t,o,"end"))return!0}return!1},_bbox:function(t){var e,n,i,r,o=this.segments,s=o.length;if(1===s)n=o[0].anchor().transformCopy(t),e=new un(n,cn.ZERO);else if(s>0)for(i=1;i<s;i++)r=o[i-1].bboxTo(o[i],t),e=e?un.union(e,r):r;return e}}),$n.fromRect=function(t,e){return new $n(e).moveTo(t.topLeft()).lineTo(t.topRight()).lineTo(t.bottomRight()).lineTo(t.bottomLeft()).close()},$n.fromPoints=function(t,e){var n,i,r;if(t){for(n=new $n(e),i=0;i<t.length;i++)r=ln.create(t[i]),r&&(0===i?n.moveTo(r):n.lineTo(r));return n}},$n.fromArc=function(t,e){var n=new $n(e),i=t.startAngle,r=t.pointAt(i);return n.moveTo(r.x,r.y),n.arc(i,t.endAngle,t.radiusX,t.radiusY,t.anticlockwise),n},$n.prototype.nodeType="Path",vn.extend($n.prototype),yn.extend($n.prototype),$n.parse=function(t,e){return Gn.current.parse(t,e)},Vn="#000",Un=fn.extend({init:function(t,e){void 0===t&&(t=new Tn),void 0===e&&(e={}),fn.fn.init.call(this,e),this.geometry(t),i(this.options.stroke)||this.stroke(Vn)},_bbox:function(t){return this._geometry.bbox(t)},rawBBox:function(){return this.geometry().bbox()},toPath:function(){var t,e=new $n,n=this.geometry().curvePoints();if(n.length>0)for(e.moveTo(n[0].x,n[0].y),t=1;t<n.length;t+=3)e.curveTo(n[t],n[t+1],n[t+2]);return e},_containsPoint:function(t){return this.geometry().containsPoint(t)},_isOnPath:function(t){return this.geometry()._isOnPath(t,this.options.stroke.width/2)}}),Un.prototype.nodeType="Arc",vn.extend(Un.prototype),yn.extend(Un.prototype),O(Un.prototype,["geometry"]),Xn="12px sans-serif",Yn="#000",Wn=fn.extend({init:function(t,e,n){void 0===e&&(e=new ln),void 0===n&&(n={}),fn.fn.init.call(this,n),this.content(t),this.position(e),this.options.font||(this.options.font=Xn),i(this.options.fill)||this.fill(Yn)},content:function(t){return i(t)?(this.options.set("content",t),this):this.options.get("content")},measure:function(){var t=$e.measureText(this.content(),{font:this.options.get("font")});return t},rect:function(){var t=this.measure(),e=this.position().clone();return new un(e,[t.width,t.height])},bbox:function(t){var e=S(this.currentTransform(t));return this.rect().bbox(e)},rawBBox:function(){return this.rect().bbox()},_containsPoint:function(t){return this.rect().containsPoint(t)}}),Wn.prototype.nodeType="Text",vn.extend(Wn.prototype),D(Wn.prototype,["position"]),Qn=fn.extend({init:function(t,e,n){void 0===e&&(e=new un),void 0===n&&(n={}),fn.fn.init.call(this,n),this.src(t),this.rect(e)},src:function(t){return i(t)?(this.options.set("src",t),this):this.options.get("src")},bbox:function(t){var e=S(this.currentTransform(t));return this._rect.bbox(e)},rawBBox:function(){return this._rect.bbox()},_containsPoint:function(t){return this._rect.containsPoint(t)},_hasFill:function(){return this.src()}}),Qn.prototype.nodeType="Image",O(Qn.prototype,["rect"]),Zn={extend:function(t,e){t.traverse=function(t){var n,i,r=this[e];for(n=0;n<r.length;n++)i=r[n],i.traverse?i.traverse(t):t(i);return this}}},Jn=fn.extend({init:function(t){fn.fn.init.call(this,t),this.children=[]},childrenChange:function(t,e,n){this.trigger("childrenChange",{action:t,items:e,index:n})},append:function(){return e(this.children,arguments),this._reparent(arguments,this),this.childrenChange("add",arguments),this},insert:function(t,e){return this.children.splice(t,0,e),e.parent=this,this.childrenChange("add",[e],t),this},insertAt:function(t,e){return this.insert(e,t)},remove:function(t){var e=this.children.indexOf(t);return e>=0&&(this.children.splice(e,1),t.parent=null,this.childrenChange("remove",[t],e)),this},removeAt:function(t){if(0<=t&&t<this.children.length){var e=this.children[t];this.children.splice(t,1),e.parent=null,this.childrenChange("remove",[e],t)}return this},clear:function(){var t=this.children;return this.children=[],this._reparent(t,null),this.childrenChange("remove",t,0),this},bbox:function(t){return Q(this.children,!0,this.currentTransform(t))},rawBBox:function(){return Q(this.children,!1)},_clippedBBox:function(t){return Z(this.children,this.currentTransform(t))},currentTransform:function(t){return fn.prototype.currentTransform.call(this,t)||null},containsPoint:function(t,e){var n,i,r;if(this.visible())for(n=this.children,i=this.currentTransform(e),r=0;r<n.length;r++)if(n[r].containsPoint(t,i))return!0;return!1},_reparent:function(t,e){var n,i,r,o=this;for(n=0;n<t.length;n++)i=t[n],r=i.parent,r&&r!==o&&r.remove&&r.remove(i),i.parent=e}}),Jn.prototype.nodeType="Group",Zn.extend(Jn.prototype,"children"),Kn={alignContent:"start",justifyContent:"start",alignItems:"start",spacing:0,orientation:"horizontal",lineSpacing:0,wrap:!0,revers:!1},ti=function(t,e){t.forEach(e)},ei=function(t,e){var n,i=t.length;for(n=i-1;n>=0;n--)e(t[n],n)},ni=Jn.extend({init:function(e,n){Jn.fn.init.call(this,t.extend({},Kn,n)),this._rect=e,this._fieldMap={}},rect:function(t){return t?(this._rect=t,this):this._rect},_initMap:function(){var t=this.options,e=this._fieldMap;"horizontal"===t.orientation?(e.sizeField="width",e.groupsSizeField="height",e.groupAxis="x",e.groupsAxis="y"):(e.sizeField="height",e.groupsSizeField="width",e.groupAxis="y",e.groupsAxis="x"),t.reverse?(this.forEach=ei,this.justifyAlign=rt):(this.forEach=ti,this.justifyAlign=it)},reflow:function(){var t,e,n,i,r,o,s,a,h,l,c,u,d,f,p,g,m,v,x,y,w,_,b,C,k=this;if(this._rect&&0!==this.children.length){for(this._initMap(),this.options.transform&&this.transform(null),t=this.options,e=this._rect,n=this._initGroups(),i=n.groups,r=n.groupsSize,o=this._fieldMap,s=o.sizeField,a=o.groupsSizeField,h=o.groupAxis,l=o.groupsAxis,c=new ln,u=new ln,d=new cn,f=it(r,e,t.alignContent,l,a),v=function(e,n){var i=g.elements[n];u[h]=p,u[l]=it(e.size[a],m,t.alignItems,l,a),nt(u,e,i),p+=e.size[s]+t.spacing},x=0;x<i.length;x++)g=i[x],c[h]=p=k.justifyAlign(g.size,e,t.justifyContent,h,s),c[l]=f,d[s]=g.size,d[a]=g.lineSize,m=new un(c,d),k.forEach(g.bboxes,v),f+=g.lineSize+t.lineSpacing;!t.wrap&&g.size>e.size[s]&&(y=e.size[s]/m.size[s],w=m.topLeft().scale(y,y),_=m.size[a]*y,b=it(_,e,t.alignContent,l,a),C=E(),"x"===h?C.translate(e.origin.x-w.x,b-w.y):C.translate(b-w.x,e.origin.y-w.y),C.scale(y,y),this.transform(C))}},_initGroups:function(){var t,e,n,i=this,r=this,o=r.options,s=r.children,a=o.lineSpacing,h=o.wrap,l=o.spacing,c=this._fieldMap.sizeField,u=this._newGroup(),d=[],f=function(){d.push(u),p+=u.lineSize+a},p=-a;for(t=0;t<s.length;t++)e=s[t],n=s[t].clippedBBox(),e.visible()&&n&&(h&&u.size+n.size[c]+l>i._rect.size[c]?0===u.bboxes.length?(i._addToGroup(u,n,e),f(),u=i._newGroup()):(f(),u=i._newGroup(),i._addToGroup(u,n,e)):i._addToGroup(u,n,e));return u.bboxes.length&&f(),{groups:d,groupsSize:p}},_addToGroup:function(t,e,n){t.size+=e.size[this._fieldMap.sizeField]+this.options.spacing,t.lineSize=Math.max(e.size[this._fieldMap.groupsSizeField],t.lineSize),t.bboxes.push(e),t.elements.push(n)},_newGroup:function(){return{lineSize:0,size:-this.options.spacing,bboxes:[],elements:[]}}}),ii=fn.extend({init:function(t,e){void 0===t&&(t=new un),void 0===e&&(e={}),fn.fn.init.call(this,e),this.geometry(t),i(this.options.stroke)||this.stroke("#000")},_bbox:function(t){return this._geometry.bbox(t)},rawBBox:function(){return this._geometry.bbox()},_containsPoint:function(t){return this._geometry.containsPoint(t)},_isOnPath:function(t){return this.geometry()._isOnPath(t,this.options.stroke.width/2)}}),ii.prototype.nodeType="Rect",vn.extend(ii.prototype),yn.extend(ii.prototype),O(ii.prototype,["geometry"]),ri=zn.extend({_change:function(){this.optionsChange({field:"stops"})}}),oi=Ge.extend({init:function(t,e,n){this.options=new an({offset:t,color:e,opacity:i(n)?n:1}),this.options.addObserver(this)}}),oi.create=function(t){if(i(t)){var e;return e=t instanceof oi?t:t.length>1?new oi(t[0],t[1],t[2]):new oi(t.offset,t.color,t.opacity)}},xt(oi.prototype,["offset","color","opacity"]),We.extend(oi.prototype),si=Ge.extend({init:function(t){void 0===t&&(t={}),this.stops=new ri(this._createStops(t.stops)),this.stops.addObserver(this),this._userSpace=t.userSpace,this.id=r()},userSpace:function(t){return i(t)?(this._userSpace=t,this.optionsChange(),this):this._userSpace},_createStops:function(t){var e,n;for(void 0===t&&(t=[]),e=[],n=0;n<t.length;n++)e.push(oi.create(t[n]));return e},addStop:function(t,e,n){this.stops.push(new oi(t,e,n))},removeStop:function(t){var e=this.stops.indexOf(t);e>=0&&this.stops.splice(e,1)}}),si.prototype.nodeType="Gradient",We.extend(si.prototype),t.extend(si.prototype,{optionsChange:function(t){this.trigger("optionsChange",{field:"gradient"+(t?"."+t.field:""),value:this})},geometryChange:function(){this.optionsChange()}}),ai=si.extend({init:function(t){void 0===t&&(t={}),si.fn.init.call(this,t),this.start(t.start||new ln),this.end(t.end||new ln(1,0))}}),D(ai.prototype,["start","end"]),hi=si.extend({init:function(t){void 0===t&&(t={}),si.fn.init.call(this,t),this.center(t.center||new ln),this._radius=i(t.radius)?t.radius:1,this._fallbackFill=t.fallbackFill},radius:function(t){return i(t)?(this._radius=t,this.geometryChange(),this):this._radius},fallbackFill:function(t){return i(t)?(this._fallbackFill=t,this.optionsChange(),this):this._fallbackFill}}),D(hi.prototype,["center"]),li={swing:yt,linear:wt,easeOutElastic:_t},ci=Ge.extend({init:function(){this._items=[]},register:function(t,e){this._items.push({name:t,type:e})},create:function(t,e){var n,i,r,o=this._items;if(e&&e.type)for(i=e.type.toLowerCase(),r=0;r<o.length;r++)if(o[r].name.toLowerCase()===i){n=o[r];break}if(n)return new n.type(t,e)}}),ci.current=new ci,ui=Date.now||function(){return(new Date).getTime()},di=Ge.extend({init:function(e,n){this.options=t.extend({},this.options,n),this.element=e},setup:function(){},step:function(){},play:function(){var t,e,n,i=this,r=this.options,o=r.duration,s=r.delay;void 0===s&&(s=0),t=li[r.easing],e=ui()+s,n=e+o,0===o?(this.step(1),this.abort()):setTimeout(function(){var r=function(){var s,a,h,l;i._stopped||(s=ui(),a=d(s-e,0,o),h=a/o,l=t(h,a,0,1,o),i.step(l),s<n?kendo.animationFrame(r):i.abort())};r()},s)},abort:function(){this._stopped=!0},destroy:function(){this.abort()}}),di.prototype.options={duration:500,easing:"swing"},di.create=function(t,e,n){return ci.current.create(t,e,n)},fi=Ge.extend({init:function(){this._items=[]},register:function(t,e,n){var i=this._items,r=i[0],o={name:t,type:e,order:n};!r||n<r.order?i.unshift(o):i.push(o)},create:function(t,e){var n,i,r=this._items,o=r[0];if(e&&e.type)for(n=e.type.toLowerCase(),i=0;i<r.length;i++)if(r[i].name===n){o=r[i];break}return o?new o.type(t,e):void kendo.logToConsole("Warning: Unable to create Kendo UI Drawing Surface. Possible causes:\n- The browser does not support SVG and Canvas. User agent: "+navigator.userAgent)}}),fi.current=new fi,pi=["click","mouseenter","mouseleave","mousemove","resize"],gi=kendo.Observable.extend({init:function(e,n){kendo.Observable.fn.init.call(this),this.options=t.extend({},n),this.element=e,this.element._kendoExportVisual=this.exportVisual.bind(this),this._click=this._handler("click"),this._mouseenter=this._handler("mouseenter"),this._mouseleave=this._handler("mouseleave"),this._mousemove=this._handler("mousemove"),this._visual=new Jn,b(e,this.options),this.bind(pi,this.options),this._enableTracking()},draw:function(t){this._visual.children.push(t)},clear:function(){this._visual.children=[]},destroy:function(){this._visual=null,this.element._kendoExportVisual=null,this.unbind()},eventTarget:function(t){for(var e,n=this,i=l(t);!e&&i&&(e=i._kendoNode,i!==n.element);)i=i.parentElement;if(e)return e.srcElement},exportVisual:function(){return this._visual},getSize:function(){return b(this.element)},currentSize:function(t){return t?void(this._size=t):this._size},setSize:function(t){b(this.element,t),this.currentSize(t),this._resize()},resize:function(t){var e=this.getSize(),n=this.currentSize();(t||(e.width>0||e.height>0)&&(!n||e.width!==n.width||e.height!==n.height))&&(this.currentSize(e),this._resize(e,t),this.trigger("resize",e))},size:function(t){return t?void this.setSize(t):this.getSize()},suspendTracking:function(){this._suspendedTracking=!0},resumeTracking:function(){this._suspendedTracking=!1},_enableTracking:function(){},_resize:function(){},_handler:function(t){var e=this;return function(n){var i=e.eventTarget(n);i&&!e._suspendedTracking&&e.trigger(t,{element:i,originalEvent:n,type:t})}},_elementOffset:function(){var t=this.element,e=w(t,["paddingLeft","paddingTop"]),n=e.paddingLeft,i=e.paddingTop,r=y(t),o=r.left,s=r.top;return{left:o+parseInt(n,10),top:s+parseInt(i,10)}},_surfacePoint:function(t){var e=this._elementOffset(),n=h(t),i=n.x-e.left,r=n.y-e.top;return new ln(i,r)}}),gi.create=function(t,e){return fi.current.create(t,e)},gi.support={},mi=Ge.extend({init:function(t){this.childNodes=[],this.parent=null,t&&(this.srcElement=t,this.observe())},destroy:function(){var t,e,n=this;for(this.srcElement&&this.srcElement.removeObserver(this),t=this.childNodes,e=0;e<t.length;e++)n.childNodes[e].destroy();this.parent=null},load:function(){},observe:function(){this.srcElement&&this.srcElement.addObserver(this)},append:function(t){this.childNodes.push(t),t.parent=this},insertAt:function(t,e){this.childNodes.splice(e,0,t),t.parent=this},remove:function(t,e){var n,i=this,r=t+e;for(n=t;n<r;n++)i.childNodes[n].removeSelf();this.childNodes.splice(t,e)},removeSelf:function(){this.clear(),this.destroy()},clear:function(){this.remove(0,this.childNodes.length)},invalidate:function(){this.parent&&this.parent.invalidate()},geometryChange:function(){this.invalidate()},optionsChange:function(){this.invalidate()},childrenChange:function(t){"add"===t.action?this.load(t.items,t.index):"remove"===t.action&&this.remove(t.index,t.items.length),this.invalidate()}}),vi={},xi="http://www.w3.org/2000/svg",yi="none",wi=function(t,e){t.innerHTML=e},"undefined"!=typeof document&&(_i="<svg xmlns='"+xi+"'></svg>",bi=document.createElement("div"),Ci="undefined"!=typeof DOMParser,bi.innerHTML=_i,Ci&&bi.firstChild.namespaceURI!==xi&&(wi=function(t,e){var n=new DOMParser,i=n.parseFromString(e,"text/xml"),r=document.adoptNode(i.documentElement);t.innerHTML="",t.appendChild(r)})),ki=wi,Ti="transform",Mi={clip:"clip-path",fill:"fill"},Si=mi.extend({init:function(t,e){mi.fn.init.call(this,t),this.definitions={},this.options=e},destroy:function(){this.element&&(this.element._kendoNode=null,this.element=null),this.clearDefinitions(),mi.fn.destroy.call(this)},load:function(t,e){var n,r,o,s,a,h=this;for(n=0;n<t.length;n++)r=t[n],o=r.children,s=new vi[r.nodeType](r,h.options),i(e)?h.insertAt(s,e):h.append(s),s.createDefinitions(),o&&o.length>0&&s.load(o),a=h.element,a&&s.attachTo(a,e)},root:function(){for(var t=this;t.parent;)t=t.parent;return t},attachTo:function(t,e){var n,r=document.createElement("div");ki(r,"<svg xmlns='"+xi+"' version='1.1'>"+this.render()+"</svg>"),n=r.firstChild.firstChild,n&&(i(e)?t.insertBefore(n,t.childNodes[e]||null):t.appendChild(n),this.setElement(n))},setElement:function(t){var e,n,i;for(this.element&&(this.element._kendoNode=null),this.element=t,this.element._kendoNode=this,e=this.childNodes,n=0;n<e.length;n++)i=t.childNodes[n],e[n].setElement(i)},clear:function(){var t,e;for(this.clearDefinitions(),this.element&&(this.element.innerHTML=""),t=this.childNodes,e=0;e<t.length;e++)t[e].destroy();this.childNodes=[]},removeSelf:function(){if(this.element){var t=this.element.parentNode;t&&t.removeChild(this.element),this.element=null}mi.fn.removeSelf.call(this)},template:function(){return this.renderChildren()},render:function(){return this.template()},renderChildren:function(){var t,e=this.childNodes,n="";for(t=0;t<e.length;t++)n+=e[t].render();return n},optionsChange:function(t){var e=t.field,n=t.value;"visible"===e?this.css("display",n?"":yi):Mi[e]&&Tt(e,n)?this.updateDefinition(e,n):"opacity"===e?this.attr("opacity",n):"cursor"===e?this.css("cursor",n):"id"===e&&(n?this.attr("id",n):this.removeAttr("id")),mi.fn.optionsChange.call(this,t)},attr:function(t,e){this.element&&this.element.setAttribute(t,e)},allAttr:function(t){var e,n=this;for(e=0;e<t.length;e++)n.attr(t[e][0],t[e][1])},css:function(t,e){this.element&&(this.element.style[t]=e)},allCss:function(t){var e,n=this;for(e=0;e<t.length;e++)n.css(t[e][0],t[e][1])},removeAttr:function(t){this.element&&this.element.removeAttribute(t)},mapTransform:function(t){var e=[];return t&&e.push([Ti,"matrix("+t.matrix().toString(6)+")"]),e},renderTransform:function(){return Ct(this.mapTransform(this.srcElement.transform()))},transformChange:function(t){t?this.allAttr(this.mapTransform(t)):this.removeAttr(Ti)},mapStyle:function(){var t=this.srcElement.options,e=[["cursor",t.cursor]];return t.visible===!1&&e.push(["display",yi]),e},renderStyle:function(){return bt("style",kt(this.mapStyle(!0)))},renderOpacity:function(){return bt("opacity",this.srcElement.options.opacity)},renderId:function(){return bt("id",this.srcElement.options.id)},createDefinitions:function(){var t,e,n,i,r=this.srcElement,o=this.definitions;if(r){t=r.options;for(n in Mi)i=t.get(n),i&&Tt(n,i)&&(o[n]=i,e=!0);e&&this.definitionChange({action:"add",definitions:o})}},definitionChange:function(t){this.parent&&this.parent.definitionChange(t)},updateDefinition:function(t,e){var n=this.definitions,i=n[t],r=Mi[t],o={};i&&(o[t]=i,this.definitionChange({action:"remove",definitions:o}),delete n[t]),e?(o[t]=e,this.definitionChange({action:"add",definitions:o}),n[t]=e,this.attr(r,this.refUrl(e.id))):i&&this.removeAttr(r)},clearDefinitions:function(){var t=this.definitions;this.definitionChange({action:"remove",definitions:t}),this.definitions={}},renderDefinitions:function(){return Ct(this.mapDefinitions())},mapDefinitions:function(){var t,e=this,n=this.definitions,i=[];for(t in n)i.push([Mi[t],e.refUrl(n[t].id)]);return i},refUrl:function(t){var e=(this.options||{}).skipBaseHref,n=this.baseUrl().replace(/'/g,"\\'"),i=e?"":n;return"url("+i+"#"+t+")"},baseUrl:function(){return Mt()}}),Ei=Si.extend({template:function(){return"<stop "+this.renderOffset()+" "+this.renderStyle()+" />"},renderOffset:function(){return bt("offset",this.srcElement.offset())},mapStyle:function(){var t=this.srcElement;return[["stop-color",t.color()],["stop-opacity",t.opacity()]]},optionsChange:function(t){"offset"===t.field?this.attr(t.field,t.value):"color"!==t.field&&"opacity"!==t.field||this.css("stop-"+t.field,t.value)}}),Ai=Si.extend({init:function(t){Si.fn.init.call(this,t),this.id=t.id,this.loadStops()},loadStops:function(){var t,e,n=this,i=this.srcElement.stops,r=this.element;for(t=0;t<i.length;t++)e=new Ei(i[t]),n.append(e),r&&e.attachTo(r)},optionsChange:function(t){"gradient.stops"===t.field?(mi.prototype.clear.call(this),this.loadStops()):"gradient"===t.field&&this.allAttr(this.mapCoordinates())},renderCoordinates:function(){return Ct(this.mapCoordinates())},mapSpace:function(){return["gradientUnits",this.srcElement.userSpace()?"userSpaceOnUse":"objectBoundingBox"]}}),Pi=Ai.extend({template:function(){return"<linearGradient id='"+this.id+"' "+this.renderCoordinates()+">"+this.renderChildren()+"</linearGradient>"},mapCoordinates:function(){var t=this.srcElement,e=t.start(),n=t.end(),i=[["x1",e.x],["y1",e.y],["x2",n.x],["y2",n.y],this.mapSpace()];return i}}),Oi=Ai.extend({template:function(){return"<radialGradient id='"+this.id+"' "+this.renderCoordinates()+">"+this.renderChildren()+"</radialGradient>"},mapCoordinates:function(){var t=this.srcElement,e=t.center(),n=t.radius(),i=[["cx",e.x],["cy",e.y],["r",n],this.mapSpace()];return i}}),zi=Si.extend({init:function(t){Si.fn.init.call(this),this.srcElement=t,this.id=t.id,this.load([t])},template:function(){return"<clipPath id='"+this.id+"'>"+this.renderChildren()+"</clipPath>"}}),Ni=Si.extend({init:function(){Si.fn.init.call(this),this.definitionMap={}},attachTo:function(t){this.element=t},template:function(){return"<defs>"+this.renderChildren()+"</defs>"},definitionChange:function(t){var e=t.definitions,n=t.action;"add"===n?this.addDefinitions(e):"remove"===n&&this.removeDefinitions(e)},createDefinition:function(t,e){var n;return"clip"===t?n=zi:"fill"===t&&(e instanceof ai?n=Pi:e instanceof hi&&(n=Oi)),new n(e)},addDefinitions:function(t){var e,n=this;for(e in t)n.addDefinition(e,t[e])},addDefinition:function(t,e){var n,i=this,r=i.element,o=i.definitionMap,s=e.id,a=o[s];a?a.count++:(n=this.createDefinition(t,e),o[s]={element:n,count:1},this.append(n),r&&n.attachTo(this.element))},removeDefinitions:function(t){var e,n=this;for(e in t)n.removeDefinition(t[e])},removeDefinition:function(t){var e=this.definitionMap,n=t.id,i=e[n];i&&(i.count--,0===i.count&&(this.remove(this.childNodes.indexOf(i.element),1),delete e[n]))}}),Ri=Si.extend({init:function(t){Si.fn.init.call(this),this.options=t,this.defs=new Ni},attachTo:function(t){this.element=t,this.defs.attachTo(t.firstElementChild)},clear:function(){mi.prototype.clear.call(this)},template:function(){return this.defs.render()+this.renderChildren()},definitionChange:function(t){this.defs.definitionChange(t)}}),Bi="rtl",Li=gi.extend({init:function(e,n){gi.fn.init.call(this,e,n),this._root=new Ri(t.extend({rtl:w(e,"direction").direction===Bi},this.options)),ki(this.element,this._template()),this._rootElement=this.element.firstElementChild,St(this._rootElement),this._root.attachTo(this._rootElement),x(this.element,{click:this._click,mouseover:this._mouseenter,mouseout:this._mouseleave,mousemove:this._mousemove}),this.resize()},destroy:function(){this._root&&(this._root.destroy(),this._root=null,this._rootElement=null,C(this.element,{click:this._click,mouseover:this._mouseenter,mouseout:this._mouseleave,mousemove:this._mousemove})),gi.fn.destroy.call(this)},translate:function(t){var e=Math.round(t.x)+" "+Math.round(t.y)+" "+this._size.width+" "+this._size.height;this._offset=t,this._rootElement.setAttribute("viewBox",e)},draw:function(t){gi.fn.draw.call(this,t),this._root.load([t])},clear:function(){gi.fn.clear.call(this),this._root.clear()},svg:function(){return"<?xml version='1.0' ?>"+this._template()},exportVisual:function(){var t,e=this,n=e._visual,i=e._offset;return i&&(t=new Jn,t.children.push(n),t.transform(E().translate(-i.x,-i.y)),n=t),n},_resize:function(){this._offset&&this.translate(this._offset)},_template:function(){return"<svg style='width: 100%; height: 100%; overflow: hidden;' xmlns='"+xi+"' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1'>"+this._root.render()+"</svg>"}}),Li.prototype.type="svg","undefined"!=typeof document&&document.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#BasicStructure","1.1")&&(gi.support.svg=!0,fi.current.register("svg",Li,10)),Fi=Si.extend({template:function(){return"<g"+(this.renderId()+this.renderTransform()+this.renderStyle()+this.renderOpacity()+this.renderDefinitions())+">"+this.renderChildren()+"</g>"},optionsChange:function(t){"transform"===t.field&&this.transformChange(t.value),Si.fn.optionsChange.call(this,t)}}),vi.Group=Fi,Ii={dot:[1.5,3.5],dash:[4,3.5],longdash:[8,3.5],dashdot:[3.5,3.5,1.5,3.5],longdashdot:[8,3.5,1.5,3.5],longdashdotdot:[8,3.5,1.5,3.5,1.5,3.5]},Di="solid",Hi="butt",ji={"fill.opacity":"fill-opacity","stroke.color":"stroke","stroke.width":"stroke-width","stroke.opacity":"stroke-opacity"},qi=" ",Gi=Si.extend({geometryChange:function(){this.attr("d",this.renderData()),this.invalidate()},optionsChange:function(t){switch(t.field){case"fill":t.value?this.allAttr(this.mapFill(t.value)):this.removeAttr("fill");break;case"fill.color":this.allAttr(this.mapFill({color:t.value}));break;case"stroke":t.value?this.allAttr(this.mapStroke(t.value)):this.removeAttr("stroke");break;case"transform":this.transformChange(t.value);break;default:var e=ji[t.field];e&&this.attr(e,t.value)}Si.fn.optionsChange.call(this,t)},content:function(){this.element&&(this.element.textContent=this.srcElement.content())},renderData:function(){return this.printPath(this.srcElement)},printPath:function(t){var e,n,i,r,o,s=this,a=t.segments,h=a.length;if(h>0){for(e=[],r=1;r<h;r++)o=s.segmentType(a[r-1],a[r]),
o!==i&&(i=o,e.push(o)),e.push("L"===o?s.printPoints(a[r].anchor()):s.printPoints(a[r-1].controlOut(),a[r].controlIn(),a[r].anchor()));return n="M"+this.printPoints(a[0].anchor())+qi+e.join(qi),t.options.closed&&(n+="Z"),n}},printPoints:function(){var t,e=arguments,n=e.length,i=[];for(t=0;t<n;t++)i.push(e[t].toString(3));return i.join(" ")},segmentType:function(t,e){return t.controlOut()&&e.controlIn()?"C":"L"},mapStroke:function(t){var e=[];return t&&!c(t.color)?(e.push(["stroke",t.color]),e.push(["stroke-width",t.width]),e.push(["stroke-linecap",this.renderLinecap(t)]),e.push(["stroke-linejoin",t.lineJoin]),i(t.opacity)&&e.push(["stroke-opacity",t.opacity]),i(t.dashType)&&e.push(["stroke-dasharray",this.renderDashType(t)])):e.push(["stroke",yi]),e},renderStroke:function(){return Ct(this.mapStroke(this.srcElement.options.stroke))},renderDashType:function(t){var e,n,i,r=t.dashType,o=t.width;if(void 0===o&&(o=1),r&&r!==Di){for(e=Ii[r.toLowerCase()],n=[],i=0;i<e.length;i++)n.push(e[i]*o);return n.join(" ")}},renderLinecap:function(t){var e=t.dashType,n=t.lineCap;return e&&"solid"!==e?Hi:n},mapFill:function(t){var e=[];return t&&"Gradient"===t.nodeType||(t&&!c(t.color)?(e.push(["fill",t.color]),i(t.opacity)&&e.push(["fill-opacity",t.opacity])):e.push(["fill",yi])),e},renderFill:function(){return Ct(this.mapFill(this.srcElement.options.fill))},template:function(){return"<path "+this.renderId()+" "+this.renderStyle()+" "+this.renderOpacity()+" "+bt("d",this.renderData())+this.renderStroke()+this.renderFill()+this.renderDefinitions()+this.renderTransform()+"></path>"}}),vi.Path=Gi,$i=Gi.extend({renderData:function(){return this.printPath(this.srcElement.toPath())}}),vi.Arc=$i,Vi=Gi.extend({geometryChange:function(){var t=this.center();this.attr("cx",t.x),this.attr("cy",t.y),this.attr("r",this.radius()),this.invalidate()},center:function(){return this.srcElement.geometry().center},radius:function(){return this.srcElement.geometry().radius},template:function(){return"<circle "+this.renderId()+" "+this.renderStyle()+" "+this.renderOpacity()+"cx='"+this.center().x+"' cy='"+this.center().y+"' r='"+this.radius()+"'"+this.renderStroke()+" "+this.renderFill()+" "+this.renderDefinitions()+this.renderTransform()+" ></circle>"}}),vi.Circle=Vi,Ui=Gi.extend({geometryChange:function(){var t=this.srcElement.geometry();this.attr("x",t.origin.x),this.attr("y",t.origin.y),this.attr("width",t.size.width),this.attr("height",t.size.height),this.invalidate()},size:function(){return this.srcElement.geometry().size},origin:function(){return this.srcElement.geometry().origin},template:function(){return"<rect "+this.renderId()+" "+this.renderStyle()+" "+this.renderOpacity()+" x='"+this.origin().x+"' y='"+this.origin().y+"' width='"+this.size().width+"' height='"+this.size().height+"' "+this.renderStroke()+" "+this.renderFill()+" "+this.renderDefinitions()+" "+this.renderTransform()+" />"}}),vi.Rect=Ui,Xi=Gi.extend({geometryChange:function(){this.allAttr(this.mapPosition()),this.invalidate()},optionsChange:function(t){"src"===t.field&&this.allAttr(this.mapSource()),Gi.fn.optionsChange.call(this,t)},mapPosition:function(){var t=this.srcElement.rect(),e=t.topLeft();return[["x",e.x],["y",e.y],["width",t.width()+"px"],["height",t.height()+"px"]]},renderPosition:function(){return Ct(this.mapPosition())},mapSource:function(t){var e=this.srcElement.src();return t&&(e=kendo.htmlEncode(e)),[["xlink:href",e]]},renderSource:function(){return Ct(this.mapSource(!0))},template:function(){return"<image preserveAspectRatio='none' "+this.renderId()+" "+this.renderStyle()+" "+this.renderTransform()+" "+this.renderOpacity()+this.renderPosition()+" "+this.renderSource()+" "+this.renderDefinitions()+"></image>"}}),vi.Image=Xi,Yi=/&(?:[a-zA-Z]+|#\d+);/g,"undefined"!=typeof document&&(Et._element=document.createElement("span")),Wi=Gi.extend({geometryChange:function(){var t=this.pos();this.attr("x",t.x),this.attr("y",t.y),this.invalidate()},optionsChange:function(t){"font"===t.field?(this.attr("style",kt(this.mapStyle())),this.geometryChange()):"content"===t.field&&Gi.fn.content.call(this,this.srcElement.content()),Gi.fn.optionsChange.call(this,t)},mapStyle:function(t){var e=Gi.fn.mapStyle.call(this,t),n=this.srcElement.options.font;return t&&(n=kendo.htmlEncode(n)),e.push(["font",n],["white-space","pre"]),e},pos:function(){var t=this.srcElement.position(),e=this.srcElement.measure();return t.clone().setY(t.y+e.baseline)},renderContent:function(){var t=this.srcElement.content();return t=Et(t),t=kendo.htmlEncode(t),$e.normalizeText(t)},renderTextAnchor:function(){var t;return!(this.options||{}).rtl||Ue.msie||Ue.edge||(t="end"),bt("text-anchor",t)},template:function(){return"<text "+this.renderId()+" "+this.renderTextAnchor()+" "+this.renderStyle()+" "+this.renderOpacity()+"x='"+this.pos().x+"' y='"+this.pos().y+"' "+this.renderStroke()+" "+this.renderTransform()+" "+this.renderDefinitions()+this.renderFill()+">"+this.renderContent()+"</text>"}}),vi.Text=Wi,Qi=Gi.extend({renderData:function(){var t,e,n=this,i=this.srcElement.paths;if(i.length>0){for(t=[],e=0;e<i.length;e++)t.push(n.printPath(i[e]));return t.join(" ")}}}),vi.MultiPath=Qi,Zi={Circle:gn,Arc:Tn,Rect:un,Point:ln,Segment:Bn,Matrix:hn,Size:cn,toMatrix:S,Transformation:dn,transform:E},Ji={Surface:Li,RootNode:Ri,Node:Si,GroupNode:Fi,ArcNode:$i,CircleNode:Vi,RectNode:Ui,ImageNode:Xi,TextNode:Wi,PathNode:Gi,MultiPathNode:Qi,DefinitionNode:Ni,ClipNode:zi,GradientStopNode:Ei,LinearGradientNode:Pi,RadialGradientNode:Oi,exportGroup:At},Ki={},tr=mi.extend({init:function(t){mi.fn.init.call(this,t),t&&this.initClip()},initClip:function(){var t=this.srcElement.clip();t&&(this.clip=t,t.addObserver(this))},clear:function(){this.srcElement&&this.srcElement.removeObserver(this),this.clearClip(),mi.fn.clear.call(this)},clearClip:function(){this.clip&&(this.clip.removeObserver(this),delete this.clip)},setClip:function(t){this.clip&&(t.beginPath(),Pt(t,this.clip),t.clip())},optionsChange:function(t){"clip"===t.field&&(this.clearClip(),this.initClip()),mi.fn.optionsChange.call(this,t)},setTransform:function(t){if(this.srcElement){var e=this.srcElement.transform();e&&t.transform.apply(t,e.matrix().toArray(6))}},loadElements:function(t,e,n){var r,o,s,a,h=this;for(r=0;r<t.length;r++)o=t[r],s=o.children,a=new Ki[o.nodeType](o,n),s&&s.length>0&&a.load(s,e,n),i(e)?h.insertAt(a,e):h.append(a)},load:function(t,e,n){this.loadElements(t,e,n),this.invalidate()},setOpacity:function(t){if(this.srcElement){var e=this.srcElement.opacity();i(e)&&this.globalAlpha(t,e)}},globalAlpha:function(t,e){var n=e;n&&t.globalAlpha&&(n*=t.globalAlpha),t.globalAlpha=n},visible:function(){var t=this.srcElement;return!t||t&&t.options.visible!==!1}}),er=tr.extend({renderTo:function(t){var e,n,i;if(this.visible()){for(t.save(),this.setTransform(t),this.setClip(t),this.setOpacity(t),e=this.childNodes,n=0;n<e.length;n++)i=e[n],i.visible()&&i.renderTo(t);t.restore()}}}),Zn.extend(er.prototype,"childNodes"),Ki.Group=er,nr=1e3/60,ir=er.extend({init:function(t){er.fn.init.call(this),this.canvas=t,this.ctx=t.getContext("2d");var e=this._invalidate.bind(this);this.invalidate=kendo.throttle(function(){kendo.animationFrame(e)},nr)},destroy:function(){er.fn.destroy.call(this),this.canvas=null,this.ctx=null},load:function(t,e,n){this.loadElements(t,e,n),this._invalidate()},_invalidate:function(){this.ctx&&(this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.renderTo(this.ctx))}}),Zn.extend(ir.prototype,"childNodes"),rr=Ge.extend({init:function(){this.shapes=[]},_add:function(t,e){this.shapes.push({bbox:e,shape:t}),t._quadNode=this},pointShapes:function(t){var e,n=this.shapes,i=n.length,r=[];for(e=0;e<i;e++)n[e].bbox.containsPoint(t)&&r.push(n[e].shape);return r},insert:function(t,e){this._add(t,e)},remove:function(t){var e,n=this.shapes,i=n.length;for(e=0;e<i;e++)if(n[e].shape===t){n.splice(e,1);break}}}),or=rr.extend({init:function(t){rr.fn.init.call(this),this.children=[],this.rect=t},inBounds:function(t){var e=this.rect,n=e.bottomRight(),i=t.bottomRight(),r=e.origin.x<=t.origin.x&&e.origin.y<=t.origin.y&&i.x<=n.x&&i.y<=n.y;return r},pointShapes:function(t){var n,i=this.children,r=i.length,o=rr.fn.pointShapes.call(this,t);for(n=0;n<r;n++)e(o,i[n].pointShapes(t));return o},insert:function(t,e){var n,i=this.children,r=!1;if(this.inBounds(e)){if(this.shapes.length<4)this._add(t,e);else{for(i.length||this._initChildren(),n=0;n<i.length;n++)if(i[n].insert(t,e)){r=!0;break}r||this._add(t,e)}r=!0}return r},_initChildren:function(){var t=this,e=t.rect,n=t.children,i=e.center(),r=e.width()/2,o=e.height()/2;n.push(new or(new un([e.origin.x,e.origin.y],[r,o])),new or(new un([i.x,e.origin.y],[r,o])),new or(new un([e.origin.x,i.y],[r,o])),new or(new un([i.x,i.y],[r,o])))}}),sr=3e3,ar=1e4,hr=75,lr=Ge.extend({init:function(){this.initRoots()},initRoots:function(){this.rootMap={},this.root=new rr,this.rootElements=[]},clear:function(){var t,e=this,n=this.rootElements;for(t=0;t<n.length;t++)e.remove(n[t]);this.initRoots()},pointShape:function(t){var e,n=(this.rootMap[Math.floor(t.x/sr)]||{})[Math.floor(t.y/sr)],i=this.root.pointShapes(t);for(n&&(i=i.concat(n.pointShapes(t))),this.assignZindex(i),i.sort(Ot),e=0;e<i.length;e++)if(i[e].containsPoint(t))return i[e]},assignZindex:function(t){var e,n,i,r,o,s=this;for(e=0;e<t.length;e++){for(n=t[e],i=0,r=Math.pow(ar,hr),o=[];n;)o.push(n),n=n.parent;for(;o.length;)n=o.pop(),i+=((n.parent?n.parent.children:s.rootElements).indexOf(n)+1)*r,r/=ar;t[e]._zIndex=i}},optionsChange:function(t){"transform"!==t.field&&"stroke.width"!==t.field||this.bboxChange(t.element)},geometryChange:function(t){this.bboxChange(t.element)},bboxChange:function(t){var e,n=this;if("Group"===t.nodeType)for(e=0;e<t.children.length;e++)n.bboxChange(t.children[e]);else t._quadNode&&t._quadNode.remove(t),this._insertShape(t)},add:function(t){var n=Array.isArray(t)?t.slice(0):[t];e(this.rootElements,n),this._insert(n)},childrenChange:function(t){var e,n=this;if("remove"===t.action)for(e=0;e<t.items.length;e++)n.remove(t.items[e]);else this._insert(Array.prototype.slice.call(t.items,0))},_insert:function(t){for(var n,i=this;t.length>0;)n=t.pop(),n.addObserver(i),"Group"===n.nodeType?e(t,n.children):i._insertShape(n)},_insertShape:function(t){var e,n,i,r,o=t.bbox();o&&(e=this.getSectors(o),n=e[0][0],i=e[1][0],this.inRoot(e)?this.root.insert(t,o):(r=this.rootMap,r[n]||(r[n]={}),r[n][i]||(r[n][i]=new or(new un([n*sr,i*sr],[sr,sr]))),r[n][i].insert(t,o)))},remove:function(t){var e,n,i=this;if(t.removeObserver(this),"Group"===t.nodeType)for(e=t.children,n=0;n<e.length;n++)i.remove(e[n]);else t._quadNode&&(t._quadNode.remove(t),delete t._quadNode)},inRoot:function(t){return t[0].length>1||t[1].length>1},getSectors:function(t){var e,n,i=t.bottomRight(),r=Math.floor(i.x/sr),o=Math.floor(i.y/sr),s=[[],[]];for(e=Math.floor(t.origin.x/sr);e<=r;e++)s[0].push(e);for(n=Math.floor(t.origin.y/sr);n<=o;n++)s[1].push(n);return s}}),cr=Ge.extend({init:function(t){t.bind("mouseenter",this._mouseenter.bind(this)),t.bind("mouseleave",this._mouseleave.bind(this)),this.element=t.element},clear:function(){this._resetCursor()},destroy:function(){this._resetCursor(),delete this.element},_mouseenter:function(t){var e=this._shapeCursor(t);e?(this._current||(this._defaultCursor=this._getCursor()),this._setCursor(e)):this._resetCursor()},_mouseleave:function(){this._resetCursor()},_shapeCursor:function(t){for(var e=t.element;e&&!i(e.options.cursor);)e=e.parent;if(e)return e.options.cursor},_getCursor:function(){if(this.element)return this.element.style.cursor},_setCursor:function(t){this.element&&(this.element.style.cursor=t,this._current=t)},_resetCursor:function(){this._current&&(this._setCursor(this._defaultCursor||""),delete this._current)}}),ur=gi.extend({init:function(t,e){var n,i;gi.fn.init.call(this,t,e),this.element.innerHTML=this._template(this),n=this.element.firstElementChild,i=b(t),n.width=i.width,n.height=i.height,this._rootElement=n,this._root=new ir(n),this._mouseTrackHandler=this._trackMouse.bind(this),x(this.element,{click:this._mouseTrackHandler,mousemove:this._mouseTrackHandler})},destroy:function(){gi.fn.destroy.call(this),this._root&&(this._root.destroy(),this._root=null),this._searchTree&&(this._searchTree.clear(),delete this._searchTree),this._cursor&&(this._cursor.destroy(),delete this._cursor),C(this.element,{click:this._mouseTrackHandler,mousemove:this._mouseTrackHandler})},draw:function(t){gi.fn.draw.call(this,t),this._root.load([t],void 0,this.options.cors),this._searchTree&&this._searchTree.add([t])},clear:function(){gi.fn.clear.call(this),this._root.clear(),this._searchTree&&this._searchTree.clear(),this._cursor&&this._cursor.clear()},eventTarget:function(t){var e,n;if(this._searchTree)return e=this._surfacePoint(t),n=this._searchTree.pointShape(e)},image:function(){var t,e,n=this,i=n._root,r=n._rootElement,o=[];return i.traverse(function(t){t.loading&&o.push(t.loading)}),t=Xe(),e=function(){i._invalidate();try{var e=r.toDataURL();t.resolve(e)}catch(n){t.reject(n)}},Ye(o).then(e,e),t},suspendTracking:function(){gi.fn.suspendTracking.call(this),this._searchTree&&(this._searchTree.clear(),delete this._searchTree)},resumeTracking:function(){var t,e,n;if(gi.fn.resumeTracking.call(this),!this._searchTree){for(this._searchTree=new lr,t=this._root.childNodes,e=[],n=0;n<t.length;n++)e.push(t[n].srcElement);this._searchTree.add(e)}},_resize:function(){this._rootElement.width=this._size.width,this._rootElement.height=this._size.height,this._root.invalidate()},_template:function(){return"<canvas style='width: 100%; height: 100%;'></canvas>"},_enableTracking:function(){this._searchTree=new lr,this._cursor=new cr(this),gi.fn._enableTracking.call(this)},_trackMouse:function(t){var e,n;this._suspendedTracking||(e=this.eventTarget(t),"click"!==t.type?(n=this._currentShape,n&&n!==e&&this.trigger("mouseleave",{element:n,originalEvent:t,type:"mouseleave"}),e&&n!==e&&this.trigger("mouseenter",{element:e,originalEvent:t,type:"mouseenter"}),this.trigger("mousemove",{element:e,originalEvent:t,type:"mousemove"}),this._currentShape=e):e&&this.trigger("click",{element:e,originalEvent:t,type:"click"}))}}),ur.prototype.type="canvas","undefined"!=typeof document&&document.createElement("canvas").getContext&&(gi.support.canvas=!0,fi.current.register("canvas",ur,20)),dr=tr.extend({renderTo:function(t){t.save(),this.setTransform(t),this.setClip(t),this.setOpacity(t),t.beginPath(),this.renderPoints(t,this.srcElement),this.setLineDash(t),this.setLineCap(t),this.setLineJoin(t),this.setFill(t),this.setStroke(t),t.restore()},setFill:function(t){var e=this.srcElement.options.fill,n=!1;return e&&("Gradient"===e.nodeType?(this.setGradientFill(t,e),n=!0):c(e.color)||(t.fillStyle=e.color,t.save(),this.globalAlpha(t,e.opacity),t.fill(),t.restore(),n=!0)),n},setGradientFill:function(t,e){var n,i,r,o,s=this.srcElement.rawBBox();e instanceof ai?(i=e.start(),r=e.end(),n=t.createLinearGradient(i.x,i.y,r.x,r.y)):e instanceof hi&&(o=e.center(),n=t.createRadialGradient(o.x,o.y,0,o.x,o.y,e.radius())),zt(n,e.stops),t.save(),e.userSpace()||t.transform(s.width(),0,0,s.height(),s.origin.x,s.origin.y),t.fillStyle=n,t.fill(),t.restore()},setStroke:function(t){var e=this.srcElement.options.stroke;if(e&&!c(e.color)&&e.width>0)return t.strokeStyle=e.color,t.lineWidth=v(e.width,1),t.save(),this.globalAlpha(t,e.opacity),t.stroke(),t.restore(),!0},dashType:function(){var t=this.srcElement.options.stroke;if(t&&t.dashType)return t.dashType.toLowerCase()},setLineDash:function(t){var e,n=this.dashType();n&&n!==Di&&(e=Ii[n],t.setLineDash?t.setLineDash(e):(t.mozDash=e,t.webkitLineDash=e))},setLineCap:function(t){var e=this.dashType(),n=this.srcElement.options.stroke;e&&e!==Di?t.lineCap=Hi:n&&n.lineCap&&(t.lineCap=n.lineCap)},setLineJoin:function(t){var e=this.srcElement.options.stroke;e&&e.lineJoin&&(t.lineJoin=e.lineJoin)},renderPoints:function(t,e){Pt(t,e)}}),Ki.Path=dr,fr=dr.extend({renderPoints:function(t){var e=this.srcElement.toPath();Pt(t,e)}}),Ki.Arc=fr,pr=dr.extend({renderPoints:function(t){var e=this.srcElement.geometry(),n=e.center,i=e.radius;t.arc(n.x,n.y,i,0,2*Math.PI)}}),Ki.Circle=pr,gr=dr.extend({renderPoints:function(t){var e=this.srcElement.geometry(),n=e.origin,i=e.size;t.rect(n.x,n.y,i.width,i.height)}}),Ki.Rect=gr,mr=dr.extend({init:function(t,e){dr.fn.init.call(this,t),this.onLoad=this.onLoad.bind(this),this.onError=this.onError.bind(this),this.loading=Xe();var n=this.img=new Image;e&&!/^data:/i.test(t.src())&&(n.crossOrigin=e),n.src=t.src(),n.complete?this.onLoad():(n.onload=this.onLoad,n.onerror=this.onError)},renderTo:function(t){"resolved"===this.loading.state()&&(t.save(),this.setTransform(t),this.setClip(t),this.drawImage(t),t.restore())},optionsChange:function(t){"src"===t.field?(this.loading=Xe(),this.img.src=this.srcElement.src()):dr.fn.optionsChange.call(this,t)},onLoad:function(){this.loading.resolve(),this.invalidate()},onError:function(){this.loading.reject(Error("Unable to load image '"+this.img.src+"'. Check for connectivity and verify CORS headers."))},drawImage:function(t){var e=this.srcElement.rect(),n=e.topLeft();t.drawImage(this.img,n.x,n.y,e.width(),e.height())}}),Ki.Image=mr,vr=dr.extend({renderTo:function(t){var e=this.srcElement,n=e.position(),i=e.measure();t.save(),this.setTransform(t),this.setClip(t),this.setOpacity(t),t.beginPath(),t.font=e.options.font,t.textAlign="left",this.setFill(t)&&t.fillText(e.content(),n.x,n.y+i.baseline),this.setStroke(t)&&(this.setLineDash(t),t.strokeText(e.content(),n.x,n.y+i.baseline)),t.restore()}}),Ki.Text=vr,xr=dr.extend({renderPoints:function(t){var e,n=this.srcElement.paths;for(e=0;e<n.length;e++)Pt(t,n[e])}}),Ki.MultiPath=xr,yr={Surface:ur,RootNode:ir,Node:tr,GroupNode:er,ArcNode:fr,CircleNode:pr,RectNode:gr,ImageNode:mr,TextNode:vr,PathNode:dr,MultiPathNode:xr},wr=Ue,_r="KENDO-PSEUDO-ELEMENT",br={},Cr={},Cr._root=Cr,kr="undefined"!=typeof window,Tr=!!kr&&(wr.msie||wr.edge),Mr=Wn.extend({init:function(t,e,n){Wn.fn.init.call(this,t,e.getOrigin(),n),this._pdfRect=e},rect:function(){return this._pdfRect},rawBBox:function(){return this._pdfRect}}),Sr="undefined"!=typeof Element&&Element.prototype&&function(t){return t.matches?function(t,e){return t.matches(e)}:t.webkitMatchesSelector?function(t,e){return t.webkitMatchesSelector(e)}:t.mozMatchesSelector?function(t,e){return t.mozMatchesSelector(e)}:t.msMatchesSelector?function(t,e){return t.msMatchesSelector(e)}:function(t){return[].indexOf.call(document.querySelectorAll(t),this)!==-1}}(Element.prototype),Er=function(t){return t?function e(n){var i,r,o,s,a=n.cloneNode(!1);if(1==n.nodeType){i=t(n),r=t(a),s=i.data();for(o in s)r.data(o,s[o]);for(/^canvas$/i.test(n.tagName)?a.getContext("2d").drawImage(n,0,0):/^(?:input|select|textarea|option)$/i.test(n.tagName)&&(a.removeAttribute("id"),a.removeAttribute("name"),a.value=n.value,a.checked=n.checked,a.selected=n.selected),o=n.firstChild;o;o=o.nextSibling)a.appendChild(e(o))}return a}:function(t){var e,n=function r(t){var e,n=t.cloneNode(!1);for(t._kendoExportVisual&&(n._kendoExportVisual=t._kendoExportVisual),e=t.firstChild;e;e=e.nextSibling)n.appendChild(r(e));return n}(t),i=t.querySelectorAll("canvas");return i.length&&Bt(n.querySelectorAll("canvas")).forEach(function(t,e){t.getContext("2d").drawImage(i[e],0,0)}),e=t.querySelectorAll("input, select, textarea, option"),Bt(n.querySelectorAll("input, select, textarea, option")).forEach(function(t,n){t.removeAttribute("id"),t.removeAttribute("name"),t.value=e[n].value,t.checked=e[n].checked,t.selected=e[n].selected}),n}}("undefined"!=typeof window&&window.kendo&&window.kendo.jQuery),jt.getFontFaces=qt,jt.drawText=function(t){var e=new Jn;return Cr._clipbox=!1,Cr._matrix=hn.unit(),Cr._stackingContext={element:t,group:e},te(t,oe(t),e),3==t.firstChild.nodeType?Fe(t,t.firstChild,e):Se(t,e),ee(),e},Ar=function(){function t(t){function f(){var e=s.exec(t);e&&(t=t.substr(e[1].length))}function p(e){f();var n=e.exec(t);if(n)return t=t.substr(n[1].length),n[1]}function g(){var e,r,o,s=kendo.parseColor(t,!0);if(s)return o=/^#[0-9a-f]+/i.exec(t)||/^rgba?\(.*?\)/i.exec(t)||/^..*?\b/.exec(t),t=t.substr(o[0].length),s=s.toRGB(),(e=p(i))||(r=p(n)),{color:s,length:e,percent:r}}function m(e){var i,s,c,u,d,f,m=[],v=!1;if(p(a)){for(i=p(o),i?(i=ge(i),p(l)):(s=p(r),"to"==s?s=p(r):s&&/^-/.test(e)&&(v=!0),c=p(r),p(l)),/-moz-/.test(e)&&null==i&&null==s&&(u=p(n),d=p(n),v=!0,"0%"==u?s="left":"100%"==u&&(s="right"),"0%"==d?c="top":"100%"==d&&(c="bottom"),p(l));t&&!p(h)&&(f=g());)m.push(f),p(l);return{type:"linear",angle:i,to:s&&c?s+" "+c:s?s:c?c:null,stops:m,reverse:v}}}function v(){if(p(a)){var t=p(u);return t=t.replace(/^['"]+|["']+$/g,""),p(h),{type:"url",url:t}}}var x,y=t;return Gt(d,y)?d[y]:((x=p(e))?x=m(x):(x=p(c))&&(x=v()),d[y]=x||{type:"none"})}var e=/^((-webkit-|-moz-|-o-|-ms-)?linear-gradient\s*)\(/,n=/^([-0-9.]+%)/,i=/^([-0-9.]+px)/,r=/^(left|right|top|bottom|to|center)\W/,o=/^([-0-9.]+(deg|grad|rad|turn))/,s=/^(\s+)/,a=/^(\()/,h=/^(\))/,l=/^(,)/,c=/^(url)\(/,u=/^(.*?)\)/,d={},f={};return function(e){return Gt(f,e)?f[e]:f[e]=Pr(e).map(t)}}(),Pr=function(){var t={};return function(e,n){function i(t){return u=t.exec(e.substr(h))}function r(t){return t.replace(/^\s+|\s+$/g,"")}var o,s,a,h,l,c,u;if(n||(n=/^\s*,\s*/),o=e+n,Gt(t,o))return t[o];for(s=[],a=0,h=0,l=0,c=!1;h<e.length;)!c&&i(/^[\(\[\{]/)?(l++,h++):!c&&i(/^[\)\]\}]/)?(l--,h++):!c&&i(/^[\"\']/)?(c=u[0],h++):"'"==c&&i(/^\\\'/)?h+=2:'"'==c&&i(/^\\\"/)?h+=2:"'"==c&&i(/^\'/)?(c=!1,h++):'"'==c&&i(/^\"/)?(c=!1,h++):i(n)?(!c&&!l&&h>a&&(s.push(r(e.substring(a,h))),a=h+u[0].length),h+=u[0].length):h++;return a<h&&s.push(r(e.substring(a,h))),t[o]=s}}(),Or=function(t){return function(e){var n,i=t[e];return i||((n=/url\((['"]?)([^'")]*?)\1\)\s+format\((['"]?)truetype\3\)/.exec(e))?i=t[e]=n[2]:(n=/url\((['"]?)([^'")]*?\.ttf)\1\)/.exec(e))&&(i=t[e]=n[2])),i}}(Object.create?Object.create(null):{}),zr=function(t){return function(e){var n=t[e];return null==n&&(n=t[e]=$e.measureText("Mapq",{font:e}).height),n}}(Object.create?Object.create(null):{}),Nr={svg:Ji,canvas:yr,util:on,PathParser:Gn,Surface:gi,BaseNode:mi,SurfaceFactory:fi,OptionsStore:an,exportImage:Nt,exportSVG:Rt,QuadNode:or,ShapesQuadTree:lr,ObserversMixin:We,Element:fn,Circle:_n,Arc:Un,Path:$n,MultiPath:Ln,Text:Wn,Image:Qn,Group:Jn,Layout:ni,Rect:ii,align:st,vAlign:at,stack:ct,vStack:ut,wrap:pt,vWrap:gt,fit:mt,LinearGradient:ai,RadialGradient:hi,GradientStop:oi,Gradient:si,Animation:di,AnimationFactory:ci,drawDOM:jt},kendo.deepExtend(kendo,{drawing:Nr,geometry:Zi}),kendo.drawing.Segment=kendo.geometry.Segment,kendo.dataviz.drawing=kendo.drawing,kendo.dataviz.geometry=kendo.geometry,kendo.drawing.util.measureText=kendo.util.measureText,kendo.drawing.util.objectKey=kendo.util.objectKey,kendo.drawing.Color=kendo.Color,kendo.util.encodeBase64=kendo.drawing.util.encodeBase64}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()}),function(t,define){define("drawing/surface-tooltip.min",["kendo.popup.min","drawing/kendo-drawing.min"],t)}(function(){!function(t){var e=".kendo",n=window.kendo,i=n.deepExtend,r=n.drawing.util,o=r.defined,s=r.limitValue,a=r.eventCoordinates,h=n._outerWidth,l=n._outerHeight,c=t.proxy,u='<div class="k-tooltip"><div class="k-tooltip-content"></div></div>',d='<div class="k-tooltip-button"><a href="\\#" class="k-icon k-i-close">close</a></div>',f=n.Class.extend({init:function(e,n){this.element=t(u),this.content=this.element.children(".k-tooltip-content"),n=n||{},this.options=i({},this.options,this._tooltipOptions(n)),this.popupOptions={appendTo:n.appendTo,animation:n.animation,copyAnchorStyles:!1,collision:"fit fit"},this._openPopupHandler=t.proxy(this._openPopup,this),this.surface=e,this._bindEvents()},options:{position:"top",showOn:"mouseenter",offset:7,autoHide:!0,hideDelay:0,showAfter:100},_bindEvents:function(){this._showHandler=c(this._showEvent,this),this._surfaceLeaveHandler=c(this._surfaceLeave,this),this._mouseleaveHandler=c(this._mouseleave,this),this._mousemoveHandler=c(this._mousemove,this),this.surface.bind("click",this._showHandler),this.surface.bind("mouseenter",this._showHandler),this.surface.bind("mouseleave",this._mouseleaveHandler),this.surface.bind("mousemove",this._mousemoveHandler),this.surface.element.on("mouseleave"+e,this._surfaceLeaveHandler),this.element.on("click"+e,".k-tooltip-button",c(this._hideClick,this)),this.element.on("mouseleave"+e,c(this._tooltipLeave,this))},getPopup:function(){return this.popup||(this.popup=new n.ui.Popup(this.element,this.popupOptions)),this.popup},destroy:function(){var t=this.popup;this.surface.unbind("click",this._showHandler),this.surface.unbind("mouseenter",this._showHandler),this.surface.unbind("mouseleave",this._mouseleaveHandler),this.surface.unbind("mousemove",this._mousemoveHandler),this.surface.element.off("mouseleave"+e,this._surfaceLeaveHandler),this.element.off("click"+e),this.element.off("mouseleave"+e),t&&(t.destroy(),delete this.popup),delete this.popupOptions,clearTimeout(this._timeout),delete this.element,delete this.content,delete this.surface},_tooltipOptions:function(t){return t=t||{},{position:t.position,showOn:t.showOn,offset:t.offset,autoHide:t.autoHide,width:t.width,height:t.height,content:t.content,shared:t.shared,hideDelay:t.hideDelay,showAfter:t.showAfter}},_tooltipShape:function(t){for(;t&&!t.options.tooltip;)t=t.parent;return t},_updateContent:function(t,e,i){var r=i.content;if(n.isFunction(r)&&(r=r({element:e,target:t})),r)return this.content.html(r),!0},_position:function(t,e,n,i){var r,o=e.position,h=e.offset||0,l=this.surface,c=l._instance._elementOffset(),u=l.getSize(),d=l._instance._offset,f=t.bbox(),p=n.width,g=n.height,m=0,v=0;return f.origin.translate(c.left,c.top),d&&f.origin.translate(-d.x,-d.y),"cursor"==o&&i?(r=a(i),m=r.x-p/2,v=r.y-g-h):"left"==o?(m=f.origin.x-p-h,v=f.center().y-g/2):"right"==o?(m=f.bottomRight().x+h,v=f.center().y-g/2):"bottom"==o?(m=f.center().x-p/2,v=f.bottomRight().y+h):(m=f.center().x-p/2,v=f.origin.y-g-h),{left:s(m,c.left,c.left+u.width),top:s(v,c.top,c.top+u.height)}},show:function(t,e){this._show(t,t,i({},this.options,this._tooltipOptions(t.options.tooltip),e))},hide:function(){var t=this.popup,e=this._current;delete this._current,clearTimeout(this._showTimeout),t&&t.visible()&&e&&!this.surface.trigger("tooltipClose",{element:e.shape,target:e.target,popup:t})&&t.close()},_hideClick:function(t){t.preventDefault(),this.hide()},_show:function(t,e,n,i,r){var o,s,a=this._current;clearTimeout(this._timeout),a&&(a.shape===e&&n.shared||a.target===t)||(clearTimeout(this._showTimeout),o=this.getPopup(),!this.surface.trigger("tooltipOpen",{element:e,target:t,popup:o})&&this._updateContent(t,e,n)&&(this._autoHide(n),s=this._measure(n),o.visible()&&o.close(!0),this._current={options:n,elementSize:s,shape:e,target:t,position:this._position(n.shared?e:t,n,s,i)},r?this._showTimeout=setTimeout(this._openPopupHandler,n.showAfter||0):this._openPopup()))},_openPopup:function(){var t=this._current,e=t.position;this.getPopup().open(e.left,e.top)},_autoHide:function(e){e.autoHide&&this._closeButton&&(this.element.removeClass("k-tooltip-closable"),this._closeButton.remove(),delete this._closeButton),e.autoHide||this._closeButton||(this.element.addClass("k-tooltip-closable"),this._closeButton=t(d).prependTo(this.element))},_showEvent:function(t){var e,n=this._tooltipShape(t.element);n&&(e=i({},this.options,this._tooltipOptions(n.options.tooltip)),e&&e.showOn==t.type&&this._show(t.element,n,e,t.originalEvent,!0))},_measure:function(t){var e,n,i,r=this.getPopup();return this.element.css({width:"auto",height:"auto"}),i=r.visible(),i||r.wrapper.show(),this.element.css({width:o(t.width)?t.width:"auto",height:o(t.height)?t.height:"auto"}),e=h(this.element),n=l(this.element),i||r.wrapper.hide(),{width:e,height:n}},_mouseleave:function(t){var e,n;this.popup&&!this._popupRelatedTarget(t.originalEvent)&&(e=this,n=e._current,n&&n.options.autoHide&&(e._timeout=setTimeout(function(){clearTimeout(e._showTimeout),e.hide()},n.options.hideDelay||0)))},_mousemove:function(t){var e,n,i=this._current;i&&t.element&&(e=i.options,"cursor"==e.position&&(n=this._position(t.element,e,i.elementSize,t.originalEvent),i.position=n,this.getPopup().wrapper.css({left:n.left,top:n.top})))},_surfaceLeave:function(t){this.popup&&!this._popupRelatedTarget(t)&&(clearTimeout(this._showTimeout),this.hide())},_popupRelatedTarget:function(e){return e.relatedTarget&&t(e.relatedTarget).closest(this.popup.wrapper).length},_tooltipLeave:function(){var t=this,e=t._current;e&&e.options.autoHide&&(t._timeout=setTimeout(function(){t.hide()},e.options.hideDelay||0))}});n.drawing.SurfaceTooltip=f}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()}),function(t,define){define("drawing/surface.min",["drawing/kendo-drawing.min","drawing/surface-tooltip.min"],t)}(function(){!function(t){function e(t){this._instance.translate(t)}var n,i=window.kendo,r=i.drawing,o=r.Surface,s=i.ui.Widget,a=i.deepExtend,h=t.proxy;i.support.svg=o.support.svg,i.support.canvas=o.support.canvas,n=s.extend({init:function(t,n){s.fn.init.call(this,t,{}),this.options=a({},this.options,n),this._instance=o.create(this.element[0],n),this._instance.translate&&(this.translate=e),this._triggerInstanceHandler=h(this._triggerInstanceEvent,this),this._bindHandler("click"),this._bindHandler("mouseenter"),this._bindHandler("mouseleave"),this._bindHandler("mousemove"),this._enableTracking()},options:{name:"Surface",tooltip:{}},events:["click","mouseenter","mouseleave","mousemove","resize","tooltipOpen","tooltipClose"],_triggerInstanceEvent:function(t){this.trigger(t.type,t)},_bindHandler:function(t){this._instance.bind(t,this._triggerInstanceHandler)},draw:function(t){this._instance.draw(t)},clear:function(){this._instance&&this._instance.clear(),this.hideTooltip()},destroy:function(){this._instance&&(this._instance.destroy(),delete this._instance),this._tooltip&&(this._tooltip.destroy(),delete this._tooltip),s.fn.destroy.call(this)},exportVisual:function(){return this._instance.exportVisual()},eventTarget:function(t){return this._instance.eventTarget(t)},showTooltip:function(t,e){this._tooltip&&this._tooltip.show(t,e)},hideTooltip:function(){this._tooltip&&this._tooltip.hide()},suspendTracking:function(){this._instance.suspendTracking(),this.hideTooltip()},resumeTracking:function(){this._instance.resumeTracking()},getSize:function(){return{width:this.element.width(),height:this.element.height()}},setSize:function(t){this.element.css({width:t.width,height:t.height}),this._size=t,this._instance.currentSize(t),this._resize()},_resize:function(){this._instance.currentSize(this._size),this._instance._resize()},_enableTracking:function(){i.ui.Popup&&(this._tooltip=new r.SurfaceTooltip(this,this.options.tooltip||{}))}}),i.ui.plugin(n),n.create=function(t,e){return new n(t,e)},i.drawing.Surface=n}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()}),function(t,define){define("drawing/html.min",["drawing/kendo-drawing.min"],t)}(function(){!function(t){var e=window.kendo,n=e.drawing,i=n.drawDOM;n.drawDOM=function(e,n){return i(t(e)[0],n)},n.drawDOM.drawText=i.drawText,n.drawDOM.getFontFaces=i.getFontFaces}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()}),function(t,define){define("kendo.drawing.min",["drawing/util.min","drawing/kendo-drawing.min","drawing/surface-tooltip.min","drawing/surface.min","drawing/html.min"],t)}(function(){},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()});
//# sourceMappingURL=kendo.drawing.min.js.map
