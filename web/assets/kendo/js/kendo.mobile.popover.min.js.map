{"version": 3, "sources": ["kendo.mobile.popover.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "mobile", "ui", "HIDE", "OPEN", "CLOSE", "WRAPPER", "ARROW", "OVERLAY", "DIRECTION_CLASSES", "Widget", "DIRECTIONS", "down", "origin", "position", "up", "left", "collision", "right", "ANIMATION", "animation", "open", "effects", "duration", "close", "DIMENSIONS", "horizontal", "offset", "size", "vertical", "REVERSE", "Popup", "extend", "init", "element", "options", "popupOptions", "axis", "that", "this", "containerPopup", "closest", "viewport", "children", "first", "container", "document", "body", "copyAnchorStyles", "autosize", "overlay", "show", "activate", "proxy", "_activate", "deactivate", "hide", "_apiCall", "trigger", "fn", "call", "wrap", "addClass", "direction", "match", "dimensions", "wrapper", "parent", "css", "width", "height", "arrow", "prependTo", "appendTo", "className", "popup", "name", "events", "target", "anchor", "destroy", "remove", "anchorOffset", "elementOffset", "cssClass", "flipped", "min", "max", "offsetAmount", "removeClass", "PopOver", "initialOpen", "on", "e", "preventDefault", "pane", "Pane", "$angular", "notify", "view", "_invokeNgController", "navigateToInitial", "navigate", "_position", "openFor", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,wBACH,cACA,qBACDD,IACL,WAuME,MA5LC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAASF,EAAME,OAAQC,EAAKD,EAAOC,GAAIC,EAAO,OAAQC,EAAO,OAAQC,EAAQ,QAASC,EAAU,mCAAoCC,EAAQ,iCAAkCC,EAAU,mCAAoCC,EAAoB,iCAAkCC,EAASR,EAAGQ,OAAQC,GACxTC,MACIC,OAAQ,gBACRC,SAAU,cAEdC,IACIF,OAAQ,aACRC,SAAU,iBAEdE,MACIH,OAAQ,cACRC,SAAU,eACVG,UAAW,YAEfC,OACIL,OAAQ,eACRC,SAAU,cACVG,UAAW,aAEhBE,GACCC,WACIC,MACIC,QAAS,UACTC,SAAU,GAEdC,OACIF,QAAS,WACTC,SAAU,OAGnBE,GACCC,YACIC,OAAQ,MACRC,KAAM,UAEVC,UACIF,OAAQ,OACRC,KAAM,UAEXE,GACCf,GAAM,OACNH,KAAQ,KACRI,KAAQ,QACRE,MAAS,QAEba,EAAQrB,EAAOsB,QACfC,KAAM,SAAUC,EAASC,GACrB,GAA4MC,GAAcC,EAAtNC,EAAOC,KAAMC,EAAiBN,EAAQO,QAAQ,yBAA0BC,EAAWR,EAAQO,QAAQ,YAAYE,SAAS,YAAYC,QAASC,EAAYL,EAAe,GAAKA,EAAiBE,CAC9LP,GAAQO,SACRA,EAAWP,EAAQO,SACXA,EAAS,KACjBA,EAAW1C,QAEXmC,EAAQU,UACRA,EAAYV,EAAQU,UACZA,EAAU,KAClBA,EAAYC,SAASC,MAEzBX,GACIM,SAAUA,EACVM,kBAAkB,EAClBC,UAAU,EACV5B,KAAM,WACFiB,EAAKY,QAAQC,QAEjBC,SAAUvD,EAAEwD,MAAMf,EAAKgB,UAAWhB,GAClCiB,WAAY,WACRjB,EAAKY,QAAQM,OACRlB,EAAKmB,UACNnB,EAAKoB,QAAQvD,GAEjBmC,EAAKmB,UAAW,IAGxB/C,EAAOiD,GAAG1B,KAAK2B,KAAKtB,EAAMJ,EAASC,GACnCD,EAAUI,EAAKJ,QACfC,EAAUG,EAAKH,QACfD,EAAQ2B,KAAKvD,GAASwD,SAAS,YAAYX,OAC3Cd,EAAOC,EAAKH,QAAQ4B,UAAUC,MAAM,cAAgB,aAAe,WACnE1B,EAAK2B,WAAaxC,EAAWY,GAC7BC,EAAK4B,QAAUhC,EAAQiC,SAASC,KAC5BC,MAAOlC,EAAQkC,MACfC,OAAQnC,EAAQmC,SACjBR,SAAS,uBAAyB3B,EAAQ4B,WAAWP,OACxDlB,EAAKiC,MAAQ1E,EAAEU,GAAOiE,UAAUlC,EAAK4B,SAASV,OAC9ClB,EAAKY,QAAUrD,EAAEW,GAASiE,SAAS5B,GAAWW,OAC9CpB,EAAaqC,SAAWnC,EAAKY,QACzBf,EAAQuC,WACRpC,EAAKY,QAAQY,SAAS3B,EAAQuC,WAElCpC,EAAKqC,MAAQ,GAAI5E,GAAMG,GAAG6B,MAAMO,EAAK4B,QAASrE,EAAEmC,QAAO,EAAMI,EAAcjB,EAAWR,EAAWwB,EAAQ4B,cAE7G5B,SACIyC,KAAM,QACNP,MAAO,IACPC,OAAQ,GACRP,UAAW,OACXlB,UAAW,KACXH,SAAU,MAEdmC,QAAS1E,GACTgD,KAAM,SAAU2B,GACZvC,KAAKoC,MAAMxC,QAAQ4C,OAASlF,EAAEiF,GAC9BvC,KAAKoC,MAAMtD,QAEfmC,KAAM,WACFjB,KAAKkB,UAAW,EAChBlB,KAAKoC,MAAMnD,SAEfwD,QAAS,WACLtE,EAAOiD,GAAGqB,QAAQpB,KAAKrB,MACvBA,KAAKoC,MAAMK,UACXzC,KAAKW,QAAQ+B,UAEjBH,OAAQ,WACJ,MAAOvC,MAAKoC,MAAMxC,QAAQ4C,QAE9BzB,UAAW,WACP,GAAIhB,GAAOC,KAAMwB,EAAYzB,EAAKH,QAAQ4B,UAAWE,EAAa3B,EAAK2B,WAAYtC,EAASsC,EAAWtC,OAAQgD,EAAQrC,EAAKqC,MAAOI,EAASJ,EAAMxC,QAAQ4C,OAAQG,EAAerF,EAAEkF,GAAQpD,SAAUwD,EAAgBtF,EAAE8E,EAAMzC,SAASP,SAAUyD,EAAWT,EAAMU,QAAUvD,EAAQiC,GAAaA,EAAWuB,EAAsC,EAAhChD,EAAKiC,MAAMN,EAAWrC,QAAa2D,EAAMjD,EAAKJ,QAAQ+B,EAAWrC,QAAUU,EAAKiC,MAAMN,EAAWrC,QAASA,EAAO/B,EAAEkF,GAAQd,EAAWrC,QAAS4D,EAAeN,EAAavD,GAAUwD,EAAcxD,GAAUC,EAAO,CACjgB4D,GAAeF,IACfE,EAAeF,GAEfE,EAAeD,IACfC,EAAeD,GAEnBjD,EAAK4B,QAAQuB,YAAYhF,GAAmBqD,SAAS,MAAQsB,GAC7D9C,EAAKiC,MAAMH,IAAIzC,EAAQ6D,GAAcrC,UAGzCuC,EAAUhF,EAAOsB,QACjBC,KAAM,SAAUC,EAASC,GACrB,GAAiBC,GAAbE,EAAOC,IACXD,GAAKqD,aAAc,EACnBjF,EAAOiD,GAAG1B,KAAK2B,KAAKtB,EAAMJ,EAASC,GACnCC,EAAevC,EAAEmC,QACb0C,UAAW,kBACXlB,KAAM,WACFlB,EAAKoB,QAAQrD,KAElBkC,KAAKJ,QAAQwC,OAChBrC,EAAKqC,MAAQ,GAAI5C,GAAMO,EAAKJ,QAASE,GACrCE,EAAKqC,MAAMzB,QAAQ0C,GAAG,OAAQ,SAAUC,GAChCA,EAAEf,QAAUxC,EAAKqC,MAAMzB,QAAQ,IAC/B2C,EAAEC,mBAGVxD,EAAKyD,KAAO,GAAI7F,GAAG8F,KAAK1D,EAAKJ,QAASrC,EAAEmC,OAAOO,KAAKJ,QAAQ4D,MAAQE,SAAU1D,KAAKJ,QAAQ8D,YAC3FlG,EAAMmG,OAAO5D,EAAMpC,IAEvBiC,SACIyC,KAAM,UACND,SACAoB,SAEJlB,QACIzE,EACAC,GAEJgB,KAAM,SAAUyD,GACZvC,KAAKoC,MAAMxB,KAAK2B,GACXvC,KAAKoD,YAONpD,KAAKwD,KAAKI,OAAOC,uBANZ7D,KAAKwD,KAAKM,qBACX9D,KAAKwD,KAAKO,SAAS,IAEvB/D,KAAKoC,MAAMA,MAAM4B,YACjBhE,KAAKoD,aAAc,IAK3Ba,QAAS,SAAU1B,GACfvC,KAAKlB,KAAKyD,GACVvC,KAAKmB,QAAQtD,GAAQ0E,OAAQvC,KAAKoC,MAAMG,YAE5CtD,MAAO,WACHe,KAAKoC,MAAMnB,QAEfwB,QAAS,WACLtE,EAAOiD,GAAGqB,QAAQpB,KAAKrB,MACvBA,KAAKwD,KAAKf,UACVzC,KAAKoC,MAAMK,UACXjF,EAAMiF,QAAQzC,KAAKL,WAG3BhC,GAAGuG,OAAO1E,GACV7B,EAAGuG,OAAOf,IACZ1F,OAAOD,MAAM2G,QACR1G,OAAOD,OACE,kBAAVH,SAAwBA,OAAO+G,IAAM/G,OAAS,SAAUgH,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.popover.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.popover', [\n        'kendo.popup',\n        'kendo.mobile.pane'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.popover',\n        name: 'PopOver',\n        category: 'mobile',\n        description: 'The mobile PopOver widget represents a transient view which is displayed when the user taps on a navigational widget or area on the screen. ',\n        depends: [\n            'popup',\n            'mobile.pane'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, mobile = kendo.mobile, ui = mobile.ui, HIDE = 'hide', OPEN = 'open', CLOSE = 'close', WRAPPER = '<div class=\"km-popup-wrapper\" />', ARROW = '<div class=\"km-popup-arrow\" />', OVERLAY = '<div class=\"km-popup-overlay\" />', DIRECTION_CLASSES = 'km-up km-down km-left km-right', Widget = ui.Widget, DIRECTIONS = {\n                'down': {\n                    origin: 'bottom center',\n                    position: 'top center'\n                },\n                'up': {\n                    origin: 'top center',\n                    position: 'bottom center'\n                },\n                'left': {\n                    origin: 'center left',\n                    position: 'center right',\n                    collision: 'fit flip'\n                },\n                'right': {\n                    origin: 'center right',\n                    position: 'center left',\n                    collision: 'fit flip'\n                }\n            }, ANIMATION = {\n                animation: {\n                    open: {\n                        effects: 'fade:in',\n                        duration: 0\n                    },\n                    close: {\n                        effects: 'fade:out',\n                        duration: 400\n                    }\n                }\n            }, DIMENSIONS = {\n                'horizontal': {\n                    offset: 'top',\n                    size: 'height'\n                },\n                'vertical': {\n                    offset: 'left',\n                    size: 'width'\n                }\n            }, REVERSE = {\n                'up': 'down',\n                'down': 'up',\n                'left': 'right',\n                'right': 'left'\n            };\n        var Popup = Widget.extend({\n            init: function (element, options) {\n                var that = this, containerPopup = element.closest('.km-modalview-wrapper'), viewport = element.closest('.km-root').children('.km-pane').first(), container = containerPopup[0] ? containerPopup : viewport, popupOptions, axis;\n                if (options.viewport) {\n                    viewport = options.viewport;\n                } else if (!viewport[0]) {\n                    viewport = window;\n                }\n                if (options.container) {\n                    container = options.container;\n                } else if (!container[0]) {\n                    container = document.body;\n                }\n                popupOptions = {\n                    viewport: viewport,\n                    copyAnchorStyles: false,\n                    autosize: true,\n                    open: function () {\n                        that.overlay.show();\n                    },\n                    activate: $.proxy(that._activate, that),\n                    deactivate: function () {\n                        that.overlay.hide();\n                        if (!that._apiCall) {\n                            that.trigger(HIDE);\n                        }\n                        that._apiCall = false;\n                    }\n                };\n                Widget.fn.init.call(that, element, options);\n                element = that.element;\n                options = that.options;\n                element.wrap(WRAPPER).addClass('km-popup').show();\n                axis = that.options.direction.match(/left|right/) ? 'horizontal' : 'vertical';\n                that.dimensions = DIMENSIONS[axis];\n                that.wrapper = element.parent().css({\n                    width: options.width,\n                    height: options.height\n                }).addClass('km-popup-wrapper km-' + options.direction).hide();\n                that.arrow = $(ARROW).prependTo(that.wrapper).hide();\n                that.overlay = $(OVERLAY).appendTo(container).hide();\n                popupOptions.appendTo = that.overlay;\n                if (options.className) {\n                    that.overlay.addClass(options.className);\n                }\n                that.popup = new kendo.ui.Popup(that.wrapper, $.extend(true, popupOptions, ANIMATION, DIRECTIONS[options.direction]));\n            },\n            options: {\n                name: 'Popup',\n                width: 240,\n                height: '',\n                direction: 'down',\n                container: null,\n                viewport: null\n            },\n            events: [HIDE],\n            show: function (target) {\n                this.popup.options.anchor = $(target);\n                this.popup.open();\n            },\n            hide: function () {\n                this._apiCall = true;\n                this.popup.close();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.popup.destroy();\n                this.overlay.remove();\n            },\n            target: function () {\n                return this.popup.options.anchor;\n            },\n            _activate: function () {\n                var that = this, direction = that.options.direction, dimensions = that.dimensions, offset = dimensions.offset, popup = that.popup, anchor = popup.options.anchor, anchorOffset = $(anchor).offset(), elementOffset = $(popup.element).offset(), cssClass = popup.flipped ? REVERSE[direction] : direction, min = that.arrow[dimensions.size]() * 2, max = that.element[dimensions.size]() - that.arrow[dimensions.size](), size = $(anchor)[dimensions.size](), offsetAmount = anchorOffset[offset] - elementOffset[offset] + size / 2;\n                if (offsetAmount < min) {\n                    offsetAmount = min;\n                }\n                if (offsetAmount > max) {\n                    offsetAmount = max;\n                }\n                that.wrapper.removeClass(DIRECTION_CLASSES).addClass('km-' + cssClass);\n                that.arrow.css(offset, offsetAmount).show();\n            }\n        });\n        var PopOver = Widget.extend({\n            init: function (element, options) {\n                var that = this, popupOptions;\n                that.initialOpen = false;\n                Widget.fn.init.call(that, element, options);\n                popupOptions = $.extend({\n                    className: 'km-popover-root',\n                    hide: function () {\n                        that.trigger(CLOSE);\n                    }\n                }, this.options.popup);\n                that.popup = new Popup(that.element, popupOptions);\n                that.popup.overlay.on('move', function (e) {\n                    if (e.target == that.popup.overlay[0]) {\n                        e.preventDefault();\n                    }\n                });\n                that.pane = new ui.Pane(that.element, $.extend(this.options.pane, { $angular: this.options.$angular }));\n                kendo.notify(that, ui);\n            },\n            options: {\n                name: 'PopOver',\n                popup: {},\n                pane: {}\n            },\n            events: [\n                OPEN,\n                CLOSE\n            ],\n            open: function (target) {\n                this.popup.show(target);\n                if (!this.initialOpen) {\n                    if (!this.pane.navigateToInitial()) {\n                        this.pane.navigate('');\n                    }\n                    this.popup.popup._position();\n                    this.initialOpen = true;\n                } else {\n                    this.pane.view()._invokeNgController();\n                }\n            },\n            openFor: function (target) {\n                this.open(target);\n                this.trigger(OPEN, { target: this.popup.target() });\n            },\n            close: function () {\n                this.popup.hide();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.pane.destroy();\n                this.popup.destroy();\n                kendo.destroy(this.element);\n            }\n        });\n        ui.plugin(Popup);\n        ui.plugin(PopOver);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}