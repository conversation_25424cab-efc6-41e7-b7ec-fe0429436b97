/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("chat/messageBox.min",["kendo.core.min"],t)}(function(){return function(t,e){var s=window.kendo,n=s.ui.Widget,i=t.extend,o=t.proxy,a=".",r=".kendoChat",l=s.keys,c='<svg version="1.1" ixmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16 16" xml:space="preserve"><path d="M0,14.3c-0.1,0.6,0.3,0.8,0.8,0.6l14.8-6.5c0.5-0.2,0.5-0.6,0-0.8L0.8,1.1C0.3,0.9-0.1,1.1,0,1.7l0.7,4.2C0.8,6.5,1.4,7,1.9,7.1l8.8,0.8c0.6,0.1,0.6,0.1,0,0.2L1.9,8.9C1.4,9,0.8,9.5,0.7,10.1L0,14.3z"/></svg>',d='<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512;" xml:space="preserve"><g>   <path d="M128,240c0-26.4-21.6-48-48-48s-48,21.6-48,48s21.6,48,48,48S128,266.4,128,240z"/>   <path d="M192,240c0,26.4,21.6,48,48,48c26.4,0,48-21.6,48-48s-21.6-48-48-48C213.6,192,192,213.6,192,240z"/>   <path d="M352,240c0,26.4,21.6,48,48,48c26.4,0,48-21.6,48-48s-21.6-48-48-48C373.6,192,352,213.6,352,240z"/></g></svg>',h={input:"k-input",button:"k-button",buttonFlat:"k-flat",buttonIcon:"k-button-icon",buttonSend:"k-button-send",buttonToggle:"k-button-toggle",iconAdd:"k-icon k-i-add",hidden:"k-hidden"},u=n.extend({init:function(t,e){n.fn.init.call(this,t,e),this._wrapper(),this._attachEvents(),this._typing=!1},events:[],options:{messages:{placeholder:"Type a message..."}},destroy:function(){n.fn.destroy.call(this),this.input&&(this.input.off(r),this.input.remove(),this.input=null),this.element.off(r),this.element.empty()},_wrapper:function(){var e=u.styles,n=this.options,i=n.messages,o="inputId_"+s.guid();t("<label>").addClass(e.hidden).html(i.placeholder).attr("for",o).appendTo(this.element),this.input=t("<input type='text'>").addClass(e.input).attr("id",o).attr("placeholder",i.placeholder).appendTo(this.element),n.toolbar&&n.toolbar.toggleable&&n.toolbar.buttons&&t("<button>").addClass(e.button).addClass(e.buttonFlat).addClass(e.buttonIcon).addClass(e.buttonToggle).attr("type","button").append(t(d)).appendTo(this.element),t("<button>").addClass(e.button).addClass(e.buttonFlat).addClass(e.buttonIcon).addClass(e.buttonSend).append(t(c)).appendTo(this.element)},_attachEvents:function(){var t=u.styles;this.input.on("keydown"+r,o(this._keydown,this)).on("input"+r,o(this._input,this)).on("focusout"+r,o(this._inputFocusout,this)),this.element.on("click"+r,a+t.buttonSend,o(this._buttonClick,this)),this.element.on("click"+r,a+t.buttonToggle,o(this._toggleToolbar,this))},_input:function(){var t=this.input.val(),e=t.length>0;this._triggerTyping(e)},_keydown:function(t){var e=t.keyCode;switch(e){case l.ENTER:t.preventDefault(),this._sendMessage()}},_buttonClick:function(t){t.preventDefault(),this._sendMessage()},_sendMessage:function(){var t,e=this.input.val();e.length&&(this._triggerTyping(!1),t={text:e},this.trigger("sendMessage",t),this.input.val(""))},_inputFocusout:function(){this._triggerTyping(!1)},_triggerTyping:function(t){t?this._typing||(this.trigger("typingStart",{}),this._typing=!0):this._typing&&(this.trigger("typingEnd",{}),this._typing=!1)},_toggleToolbar:function(t){this.trigger("toggleToolbar",{originalEvent:t})}});i(!0,u,{styles:h}),i(s,{chat:{ChatMessageBox:u}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,s){(s||e)()}),function(t,define){define("chat/toolbar.min",["kendo.core.min"],t)}(function(){return function(t,e){var s=window.kendo,n=s.ui.Widget,i=t.extend,o=t.proxy,a=".",r=".kendoChat",l="kButtonName",c="chatToolbarScrollLeft",d="chatToolbarScrollRight",h=":visible",u={effects:"expand:vertical",duration:200},p={expand:{show:!0},collapse:{hide:!0}},g={button:"k-button",buttonFlat:"k-flat",buttonList:"k-button-list",scrollButton:"k-scroll-button",scrollButtonLeft:"k-scroll-button-left",scrollButtonRight:"k-scroll-button-right",scrollButtonLeftIcon:"k-icon k-i-arrow-chevron-left",scrollButtonRightIcon:"k-icon k-i-arrow-chevron-right",buttonIcon:"k-button-icon"},m=n.extend({init:function(t,e){var s,a;e=i({},e,{name:"ChatToolbar"}),s=e.toolbar,a=s.buttons&&s.buttons.length,n.fn.init.call(this,t,e),a&&this._createButtonList(),a&&s.scrollable&&this.buttonsWidth()>this.element.width()&&this._initScrolling(),this._setupAnimation(),s.toggleable&&this.toggle(!0),this.element.on("click"+r,o(this._onClick,this))},events:["click"],destroy:function(){n.fn.destroy.call(this),this.element.off(r),this.element.empty()},_createButtonList:function(){var e,s,n=this,i=m.styles,o=n.options.toolbar.buttons,a=t("<div class='"+i.buttonList+"'></div>");for(e=0;e<o.length;e++)s=n._createButton(o[e]),a.append(s);a.appendTo(this.element),this.buttonList=a},_createButton:function(e){var s=m.styles,n=t("<button>");return"string"==typeof e&&(e={name:e}),n.attr(e.attr||{}).attr("title",e.text).attr("type","button").addClass(e.name).data(l,e.name).addClass(s.button),e.iconClass&&(n.addClass(s.buttonIcon),n.prepend("<span class='"+e.iconClass+"'></span>")),n},_onClick:function(e){var s=m.styles,n=t(e.target).closest(a+s.button);n.is(a+s.scrollButton)&&!this._scrolling&&this._scroll(n.data(l)),n.data(l)&&this.trigger("click",{button:n[0],name:n.data(l),originalEvent:e})},_initScrolling:function(){var t=m.styles;this.scrollButtonLeft=this._createButton({name:c,iconClass:t.scrollButtonLeftIcon,attr:{"class":t.scrollButton+" "+t.scrollButtonLeft}}),this.scrollButtonRight=this._createButton({name:d,iconClass:t.scrollButtonRightIcon,attr:{"class":t.scrollButton+" "+t.scrollButtonRight}}),this.element.prepend(this.scrollButtonLeft),this.element.append(this.scrollButtonRight),this._refreshScrollButtons(),this.element.on("keydown"+r,o(this._refreshScrollButtons,this))},_scroll:function(t){var e=this,s=e.buttonWidth(),n=this.maxScrollSize(),i=t===c?s*-1:s,o=this.currentScrollLeft(),a=o+i;a=Math.min(Math.max(a,0),n),t!==c&&t!==d||(e.buttonList.scrollLeft(a),e._refreshScrollButtons(a))},_refreshScrollButtons:function(t){var s=this.maxScrollSize(),n=t===e||isNaN(parseInt(t,10))?this.currentScrollLeft():t;(this.scrollButtonLeft||this.scrollButtonRight)&&(this.scrollButtonLeft.toggle(0!==n),this.scrollButtonRight.toggle(n!==s))},_setupAnimation:function(){var t=this.options.toolbar.animation,e=i({},u),s=i({reverse:!0,hide:!0},u);t=t===!1?i(!0,{},p):i(!0,{expand:e,collapse:s},t),this.options.toolbar.animation=t},_animationComplete:function(){this._refreshScrollButtons()},currentScrollLeft:function(){return Math.round(this.buttonList.scrollLeft())},maxScrollSize:function(){return Math.round(this.buttonList[0].scrollWidth-this.buttonList[0].clientWidth)},buttons:function(){var t=m.styles;return this.buttonList?this.buttonList.children(a+t.button):null},buttonWidth:function(){return Math.round(this.buttons().last().outerWidth(!0))},buttonsWidth:function(){var t=0;return this.buttons()&&(t=this.buttonWidth()*this.buttons().length),t},toggle:function(t){var e=this.options.toolbar.animation;t&&(e=i(!0,{},p)),e.expand.complete=o(this._animationComplete,this),e.collapse.complete=o(this._animationComplete,this),this.element.is(h)?this.element.kendoStop().kendoAnimate(e.collapse):this.element.kendoStop().kendoAnimate(e.expand)}});i(!0,m,{styles:g}),i(s.chat,{ChatToolBar:m})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,s){(s||e)()}),function(t,define){define("chat/view.min",["kendo.core.min","kendo.draganddrop.min"],t)}(function(){return function(t,e){var s,n,i,o,a=window.kendo,r=a.ui.Widget,l=t.extend,c=t.proxy,d=".",h=" ",u=".kendoChat",p=a.template('<div #:text# class="#=styles.messageGroup# #= url ? "" : styles.noAvatar #"><p class="#=styles.author#">#:text#</p># if (url) { #<img src="#=url#" alt="#:text#" class="#=styles.avatar#"># } #</div>'),g=a.template('<div me class="#=styles.messageGroup# #=styles.self# #= url ? "" : styles.noAvatar #"># if (url) { #<img src="#=url#" alt="#:text#" class="#=styles.avatar#"># } #</div>'),m=a.template('<div class="#=styles.message#"><time class="#=styles.messageTime#">#= kendo.toString(kendo.parseDate(timestamp), "HH:mm:ss") #</time><div class="#=styles.bubble#">#:text#</div></div>'),f=a.template('<div class="#=styles.messageListContent# #=styles.typingIndicatorBubble#"><p class="#=styles.author#">#:text#</p><div class="#=styles.message#"><div class="#=styles.bubble#"><div class="#=styles.typingIndicator#"><span></span><span></span><span></span></div></div></div></div>'),b=a.template('<div class="#=styles.suggestedActions#"># for (var i = 0; i < suggestedActions.length; i++) { #<span class="#=styles.suggestedAction#" data-value="#:suggestedActions[i].value#">#:suggestedActions[i].title#</span># } #</div>'),y=a.template('<div class="#=styles.card# #=styles.cardRich#"># if (typeof images !== "undefined" && images.length > 0) { #<img src="#:images[0].url#" alt="#:images[0].alt#" class="#=styles.cardImage#" /># } #<div class="#=styles.cardBody#"># if (typeof title !== "undefined") { #<h5 class="#=styles.cardTitle#">#:title#</h5># } ## if (typeof subtitle !== "undefined") { #<h6 class="#=styles.cardSubtitle#">#:subtitle#</h6># } ## if (typeof text !== "undefined") { #<p>#:text#</p># } #</div># if (typeof buttons !== "undefined" && buttons.length > 0) { #<div class="#=styles.cardActions# #=styles.cardActionsVertical#"># for (var i = 0; i < buttons.length; i++) { #<span class="#=styles.cardAction#"><span class="#=styles.button# #=styles.buttonPrimary#" data-value="#:buttons[i].value#">#:buttons[i].title#</span></span># } #</div># } #</div>');l(a.chat,{Templates:{},Components:{}}),a.chat.registerTemplate=function(t,e){a.chat.Templates[t]=a.template(e)},a.chat.getTemplate=function(t){return a.chat.Templates[t]||m},a.chat.registerTemplate("text",m),a.chat.registerTemplate("message",m),a.chat.registerTemplate("typing",f),a.chat.registerTemplate("suggestedAction",b),a.chat.registerTemplate("heroCard",y),a.chat.registerTemplate("application/vnd.microsoft.card.hero",y),a.chat.registerComponent=function(t,e){a.chat.Components[t]=e},a.chat.getComponent=function(t){return a.chat.Components[t]||null},s=a.chat.Component=a.Class.extend({init:function(e,s){this.element=t("<div></div>"),this.options=e,this.view=s},destroy:function(){a.destroy(this.element)}}),n=s.extend({init:function(t,e){s.fn.init.call(this,t,e),this.element.kendoCalendar({change:function(){e.trigger("suggestedAction",{text:a.toString(this.value(),"d"),type:"message"})}})},destroy:function(){}}),a.chat.registerComponent("calendar",n),i={wrapper:"k-widget k-chat",messageList:"k-avatars",messageListContent:"k-message-list-content",messageTime:"k-message-time",messageGroup:"k-message-group",message:"k-message",only:"k-only",first:"k-first",middle:"k-middle",last:"k-last",author:"k-author",avatar:"k-avatar",noAvatar:"k-no-avatar",self:"k-alt",button:"k-button",iconButton:"k-button-icon",buttonPrimary:"k-flat k-primary",scrollButtonIcon:"k-icon",scrollButtonIconLeft:"k-i-arrow-chevron-left",scrollButtonIconRight:"k-i-arrow-chevron-right",typingIndicator:"k-typing-indicator",typingIndicatorBubble:"k-typing-indicator-bubble",bubble:"k-bubble",suggestedActions:"k-quick-replies",suggestedAction:"k-quick-reply",cardWrapper:"k-card-container",cardDeckScrollWrap:"k-card-deck-scrollwrap",cardDeck:"k-card-deck",cardList:"k-card-list",card:"k-card",cardRich:"k-card-type-rich",cardBody:"k-card-body",cardImage:"k-card-image",cardTitle:"k-card-title",cardSubtitle:"k-card-subtitle",cardActions:"k-card-actions",cardActionsVertical:"k-card-actions-vertical",cardAction:"k-card-action",selected:"k-state-selected"},o=a.chat.ChatView=r.extend({init:function(t,e){r.fn.init.call(this,t,e),this._list(),this._lastSender=null,this.typingParticipants=[],this._attachEvents(),this._scrollable()},events:[],options:{messages:{isTyping:" is typing.",areTyping:" are typing.",and:" and "}},destroy:function(){r.fn.destroy.call(this),this._scrollDraggable&&this._scrollDraggable.destroy(),this.element.empty(),this.element.off(u),this.list=null,this._lastSender=null},_list:function(){var e=o.styles;this.element.addClass(e.messageList).attr("aria-live","polite"),this.list=t("<div>").addClass(e.messageListContent).appendTo(this.element)},_attachEvents:function(){var t=o.styles;this.element.on("click"+u,c(this._listClick,this)).on("click"+u,d+t.message,c(this._messageClick,this)).on("click"+u,d+t.suggestedAction,c(this._suggestedActionClick,this)).on("click"+u,d+t.cardAction+h+d+t.button,c(this._cardActionClick,this))},_scrollable:function(){var t=o.styles;this.element.on("click"+u,d+t.cardDeckScrollWrap+h+d+t.button,c(this._scrollButtonClick,this))},_scrollButtonClick:function(e){var s=o.styles,n=t(e.currentTarget),i=0!==n.find(d+s.scrollButtonIconLeft).length,a=n.siblings(d+s.cardDeck),r=a.find(d+s.card).last(),l=r.outerWidth(!0);a.scrollLeft(i?a.scrollLeft()-l:a.scrollLeft()+l)},getTemplate:function(t){return a.chat.getTemplate(t)},getComponent:function(t){return a.chat.getComponent(t)},renderMessage:function(t,e){t.timestamp||(t.timestamp=new Date),t.text||(t.text="");var s=this._renderTemplate(t.type,t);this._renderBubble(t.type,s,e),"typing"==t.type?this.typingParticipants.length>0&&this._removeTypingParticipant(e):this._lastSender=e.id},renderSuggestedActions:function(t){this._removeSuggestedActions();var e=this._renderTemplate("suggestedAction",{suggestedActions:t});this.list.append(e),this._scrollToBottom()},renderAttachments:function(t){var e,s,n=this._renderAttachmentWrapper(t.attachmentLayout),i="carousel"===t.attachmentLayout?n.find(d+o.styles.cardDeck):n,a=t.attachments;if(a.length){for(e=0;e<a.length;e++)s=this._renderTemplate(a[e].contentType,a[e].content),i.append(s);this._removeSuggestedActions(),this._removeTypingIndicator(),this.list.append(n),this._lastSender=null}},renderComponent:function(t){var e=this.getComponent(t),s=new e({},this);this.list.append(s.element),this._scrollToBottom()},_renderAttachmentWrapper:function(e){var s,n,i=o.styles,a=t("<div>");return"carousel"===e?(a.addClass(i.cardDeckScrollWrap),s=this._renderScrollButton(i.scrollButtonIconLeft),a.append(s),a.append(t("<div>").addClass(i.cardDeck)),n=this._renderScrollButton(i.scrollButtonIconRight),a.append(n)):a.addClass(i.cardList),a},_renderScrollButton:function(e){var s=o.styles;return t("<button>").addClass(s.button).addClass(s.iconButton).append(t("<span>").addClass(s.scrollButtonIcon).addClass(e))},_removeSuggestedActions:function(){this.list.find(d+o.styles.suggestedActions).remove()},_listClick:function(e){var s=o.styles,n=t(e.target);n.hasClass(s.message)||n.parents(d+s.message).length||this._clearSelection()},_messageClick:function(e){this._clearSelection(),t(e.currentTarget).addClass(o.styles.selected)},_suggestedActionClick:function(e){var s=t(e.target).data("value")||"";this.trigger("actionClick",{text:s}),this._removeSuggestedActions()},_cardActionClick:function(e){var s=t(e.target).data("value")||"";this.trigger("actionClick",{text:s})},_renderBubble:function(t,e,s){this._removeSuggestedActions(),this._removeTypingIndicator();var n=this._getMessageGroup(s,t);this._appendToGroup(n,e,t),this._scrollToBottom()},_renderTemplate:function(e,s){var n,i,a,r,c=this.getComponent(e);return c?(i=new c(s,this),n=i.element):(a=this.getTemplate(e),r=l(!0,{},s,{styles:o.styles}),n=t(a(r))),n},_getMessageGroup:function(e,s){var n,i=o.styles,a=this._getMessageGroupTemplate(e,s),r="typing"==s?this.element:this.list;return e.id===this._lastSender&&null!==this._lastSender&&"typing"!==s&&(n=this.list.find(d+i.messageGroup).last(),n.length)?n:t(a({text:e.name,url:e.iconUrl,styles:i})).appendTo(r)},_getMessageGroupTemplate:function(t,e){var s=t.id===this.options.user.id,n=s?g:p;return"typing"==e&&(n=f),n},_appendToGroup:function(t,e,s){var n=o.styles,i=t.find(d+n.message),a=i.length,r=this.element.find(d+n.typingIndicator);r.length&&"typing"==s||(e.addClass(0===a?n.only:n.last),i.filter(d+n.only).removeClass(n.only).addClass(n.first),i.filter(d+n.last).removeClass(n.last).addClass(n.middle),t.append(e))},_renderTypingIndicator:function(e){var s,n,o=this.element.find(d+i.typingIndicatorBubble);this._addTypingParticipant(e),o.length?(n=this._composeTypingParticipantsText(this.typingParticipants),s=o.find(d+i.author).first(),s.text(n)):t(f({text:e.name+this.options.messages.isTyping,styles:i})).appendTo(this.element),this._scrollToBottom()},_addTypingParticipant:function(t){var e,s=!1;for(e=0;e<this.typingParticipants.length;e+=1)if(this.typingParticipants[e].id==t.id){s=!0;break}s||this.typingParticipants.push(t)},_removeTypingParticipant:function(t){var e,s,n,o=this.element.find(d+i.typingIndicatorBubble);if(o.length){for(n=0;n<this.typingParticipants.length;n+=1)this.typingParticipants[n].id==t.id&&this.typingParticipants.splice(n,1);s=this._composeTypingParticipantsText(this.typingParticipants),""===s?o.remove():(e=o.find(d+i.author).first(),e.text(s))}},_composeTypingParticipantsText:function(t){var e=this.options.messages,s=1==t.length?e.isTyping:e.areTyping,n="";return 0===t.length?n:n=this.typingParticipants.map(function(t){return t.name}).join(", ").replace(/,(?!.*,)/gim,e.and.trimRight())+s},_removeTypingIndicator:function(){var t=this.element.find(d+i.typingIndicatorBubble);t.length&&(this.typingParticipants=[],t.remove())},_clearSelection:function(){var t=o.styles.selected;this.element.find(d+t).removeClass(t)},_scrollToBottom:function(){this.element.scrollTop(this.element.prop("scrollHeight"))}}),l(!0,o,{styles:i})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,s){(s||e)()}),function(t,define){define("kendo.chat.min",["chat/messageBox.min","chat/toolbar.min","chat/view.min"],t)}(function(){return function(t,e){var s=window.kendo,n=s.ui.Widget,i=t.extend,o=".",a={wrapper:"k-widget k-chat",canvas:"k-chat-canvas",viewWrapper:"k-message-list",messageBoxWrapper:"k-message-box",toolbarBoxWrapper:"k-toolbar-box"},r=n.extend({init:function(t,e,i){n.fn.init.call(this,t,e),i&&(this._events=i),this._user(),this._wrapper(),this._view(),this._messageBox(),e&&e.toolbar&&e.toolbar.buttons&&this._toolbar(),s.notify(this)},events:["typingStart","typingEnd","post","sendMessage","actionClick","toolClick"],options:{user:{name:"User",iconUrl:""},name:"Chat",messages:{placeholder:"Type a message..."},toolbar:!1},setOptions:function(e){this._setEvents(e),t.extend(!0,this.options,e),this.toolbar&&"toolbar"in e&&(this.toolbar.destroy(),this.toolbar=null),this.messageBox&&(this.messageBox.unbind(),this.messageBox.destroy(),this.messageBox=null),this._messageBox(),"toolbar"in e&&(this._resetToolbarButtons(e),this._toolbar())},_resetToolbarButtons:function(t){var e=this.wrapper.find(o+a.toolbarBoxWrapper);e.is(":visible")||e.show(),"buttons"in t.toolbar&&(this.options.toolbar.buttons=t.toolbar.buttons)},destroy:function(){this.view&&(this.view.unbind(),this.view.destroy(),this.view=null),this.messageBox&&(this.messageBox.unbind(),this.messageBox.destroy(),this.messageBox=null),this.toolbar&&(this.toolbar.destroy(),this.toolbar=null),n.fn.destroy.call(this)},_user:function(){this.options.user.id=s.guid()},getUser:function(){return i(!0,{},this.options.user)},_wrapper:function(){var t=r.styles,e=this.options,s=e.height,n=e.width,i="<div class='"+t.viewWrapper+"'></div><div class='"+t.messageBoxWrapper+"'></div><div class='"+t.toolbarBoxWrapper+"' role='toolbar' style='display:none;'></div>";this.wrapper=this.element.addClass(t.wrapper).append(i),e.toolbar&&e.toolbar.buttons&&e.toolbar.buttons.length&&this.wrapper.find(o+t.toolbarBoxWrapper).show(),s&&this.wrapper.height(s),n&&this.wrapper.css("max-width",n)},_view:function(){var t=this,e=r.styles,n=i(!0,{},this.options),a=this.wrapper.find(o+e.viewWrapper+"");this.view=new s.chat.ChatView(a,n),this.view.bind("actionClick",function(e){t.trigger("actionClick",e),t.postMessage(e.text)})},_messageBox:function(){var t=this,e=r.styles,n=i(!0,{},this.options),a=this.wrapper.find(o+e.messageBoxWrapper+"");this.messageBox=new s.chat.ChatMessageBox(a,n),this.messageBox.bind("typingStart",function(e){t.trigger("typingStart",e)}).bind("typingEnd",function(e){t.trigger("typingEnd",e)}).bind("sendMessage",function(e){t.trigger("sendMessage",e),t.postMessage(e.text)}).bind("toggleToolbar",function(){t.toggleToolbar()})},_toolbar:function(){var t=this,n=r.styles,a=i(!0,{},t.options),l=t.wrapper.find(o+n.toolbarBoxWrapper+"");a.toolbar.scrollable===e&&(this.options.toolbar.scrollable=a.toolbar.scrollable=!0),a.toolbar.toggleable===e&&(this.options.toolbar.toggleable=a.toolbar.toggleable=!1),t.toolbar=new s.chat.ChatToolBar(l,a),t.toolbar.bind("click",function(e){t.trigger("toolClick",{sender:t,name:e.name,button:e.button,messageBox:t.messageBox.input[0],originalEvent:e.originalEvent})})},postMessage:function(t){var e=i(!0,{},{text:t,type:"message",timestamp:new Date,from:this.getUser()});this.trigger("post",e),this.renderMessage(e,e.from)},renderMessage:function(t,e){this.view.renderMessage(t,e)},renderSuggestedActions:function(t){this.view.renderSuggestedActions(t)},renderAttachments:function(t,e){this.view.renderAttachments(t,e)},toggleToolbar:function(t){this.toolbar.toggle(t)},renderUserTypingIndicator:function(t){this.view._renderTypingIndicator(t)},clearUserTypingIndicator:function(t){this.view._removeTypingParticipant(t)},removeTypingIndicator:function(){this.view._removeTypingIndicator()}});s.ui.plugin(r),i(!0,r,{styles:a})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,s){(s||e)()});
//# sourceMappingURL=kendo.chat.min.js.map
