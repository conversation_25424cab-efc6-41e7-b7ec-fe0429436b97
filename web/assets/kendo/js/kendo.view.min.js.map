{"version": 3, "sources": ["kendo.view.js"], "names": ["f", "define", "$", "undefined", "parseTransition", "transition", "matches", "match", "transitionRegExp", "type", "direction", "reverse", "kendo", "window", "attr", "ui", "attrValue", "directiveSelector", "Observable", "Widget", "roleSelector", "SCRIPT", "INIT", "TRANSITION_START", "TRANSITION_END", "SHOW", "HIDE", "ATTACH", "DETACH", "sizzleErrorRegExp", "bodyRegExp", "LOAD_START", "LOAD_COMPLETE", "SHOW_START", "SAME_VIEW_REQUESTED", "VIEW_SHOW", "VIEW_TYPE_DETERMINED", "AFTER", "classNames", "content", "view", "stretchedView", "widget", "header", "footer", "View", "extend", "init", "options", "that", "this", "id", "guid", "fn", "call", "_initOptions", "renderOnInit", "_createElement", "wrapInSections", "_renderSections", "tagName", "model", "_wrap", "wrap", "_evalTemplate", "evalTemplate", "_fragments", "bind", "name", "detachOnHide", "detachOnDestroy", "render", "container", "notInitialized", "element", "append", "trigger", "_eachFragment", "clone", "ViewClone", "triggerBeforeShow", "triggerBeforeHide", "showStart", "css", "showEnd", "hideEnd", "hide", "beforeTransition", "afterTransition", "detach", "destroy", "unbind", "remove", "purge", "add", "wrapper", "off", "fragments", "methodName", "placeholder", "result", "document", "getElementById", "html", "e", "test", "message", "replace", "template", "contents", "contains", "replaceWith", "wrapAll", "parent", "_wrapper", "_createContent", "_createHeader", "_createFooter", "is", "ns", "addClass", "ccontentElements", "contentSelector", "children", "filter", "child", "contentElement", "Class", "noop", "Layout", "containers", "selector", "_createContainer", "showIn", "show", "root", "find", "length", "Error", "ViewContainer", "Fragment", "attach", "history", "running", "after", "end", "previous", "locationID", "current", "previousEntry", "back", "theTransition", "transitionData", "effect", "stop", "pop", "push", "effects", "enabled", "fx", "setReverse", "run", "then", "ViewEngine", "views", "sandbox", "_hideViews", "rootView", "first", "layouts", "viewContainer", "params", "events", "showView", "url", "RegExp", "remoteViewURLPrefix", "_findViewElement", "widgetInstance", "reload", "remote", "_createView", "url<PERSON><PERSON>", "split", "$1", "innerHTML", "wrapInner", "_locate", "selectors", "$angular", "indexOf", "char<PERSON>t", "_findViewElementById", "_createSpaView", "_createMobileView", "initWidget", "defaultTransition", "loader", "getLayout", "getLayoutProxy", "modelScope", "roles", "viewOptions", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,cACH,aACA,eACA,YACDD,IACL,WA2gBE,MA9fC,UAAUE,EAAGC,GA+QV,QAASC,GAAgBC,GACrB,IAAKA,EACD,QAEJ,IAAIC,GAAUD,EAAWE,MAAMC,MAC/B,QACIC,KAAMH,EAAQ,GACdI,UAAWJ,EAAQ,GACnBK,QAAwB,YAAfL,EAAQ,IAvR5B,GACOM,GAAQC,OAAOD,MAAOE,EAAOF,EAAME,KAAMC,EAAKH,EAAMG,GAAIC,EAAYJ,EAAMI,UAAWC,EAAoBL,EAAMK,kBAAmBC,EAAaN,EAAMM,WAAYC,EAASP,EAAMG,GAAGI,OAAQC,EAAeR,EAAMQ,aAAcC,EAAS,SAAUC,EAAO,OAAQC,EAAmB,kBAAmBC,EAAiB,gBAAiBC,EAAO,OAAQC,EAAO,OAAQC,EAAS,SAAUC,EAAS,SAAUC,EAAoB,0BAChaC,EAAa,wDACbC,EAAa,YACbC,EAAgB,eAChBC,EAAa,YACbC,EAAsB,oBACtBC,EAAY,WACZC,EAAuB,qBACvBC,EAAQ,QACRC,GACAC,QAAS,YACTC,KAAM,SACNC,cAAe,mBACfC,OAAQ,WACRC,OAAQ,WACRC,OAAQ,YAERC,EAAOjC,EAAMG,GAAGI,OAAO2B,QACvBC,KAAM,SAAUR,EAASS,GACrB,GAAIC,GAAOC,IACXF,GAAUA,MACVC,EAAKE,GAAKvC,EAAMwC,OAChBlC,EAAWmC,GAAGN,KAAKO,KAAKL,GACxBA,EAAKM,aAAaP,GAClBC,EAAKV,QAAUA,EACXU,EAAKD,QAAQQ,cACbrC,EAAOkC,GAAGN,KAAKO,KAAKL,EAAMA,EAAKQ,iBAAkBT,GAEjDC,EAAKD,QAAQU,gBACbT,EAAKU,kBAETV,EAAKW,QAAUZ,EAAQY,SAAW,MAClCX,EAAKY,MAAQb,EAAQa,MACrBZ,EAAKa,MAAQd,EAAQe,QAAS,EAC9Bb,KAAKc,cAAgBhB,EAAQiB,eAAgB,EAC7ChB,EAAKiB,cACLjB,EAAKkB,MACD7C,EACAG,EACAC,EACAH,EACAC,GACDwB,IAEPA,SACIoB,KAAM,OACNZ,cAAc,EACdE,gBAAgB,EAChBW,cAAc,EACdC,iBAAiB,GAErBC,OAAQ,SAAUC,GACd,GAAIvB,GAAOC,KAAMuB,GAAkBxB,EAAKyB,OAexC,OAdID,KACAxB,EAAKyB,QAAUzB,EAAKQ,kBAEpBe,GACAtE,EAAEsE,GAAWG,OAAO1B,EAAKyB,SAEzBD,IACA7D,EAAMuD,KAAKlB,EAAKyB,QAASzB,EAAKY,OAC9BZ,EAAK2B,QAAQtD,IAEbkD,IACAvB,EAAK4B,cAAclD,GACnBsB,EAAK2B,QAAQnD,IAEVwB,EAAKyB,SAEhBI,MAAO,WACH,MAAO,IAAIC,GAAU7B,OAEzB8B,kBAAmB,WACf,OAAO,GAEXC,kBAAmB,WACf,OAAO,GAEXC,UAAW,WAAA,GACHjC,GAAOC,KACPwB,EAAUzB,EAAKsB,QACfG,IACAA,EAAQS,IAAI,UAAW,IAE3BjC,KAAK0B,QAAQnD,GAAQe,KAAMU,QAE/BkC,QAAS,aAETC,QAAS,WACLnC,KAAKoC,QAETC,iBAAkB,SAAU9E,GACxByC,KAAK0B,QAAQrD,GAAoBd,KAAMA,KAE3C+E,gBAAiB,SAAU/E,GACvByC,KAAK0B,QAAQpD,GAAkBf,KAAMA,KAEzC6E,KAAM,WACEpC,KAAKF,QAAQqB,eACbnB,KAAK2B,cAAcjD,GACnB1B,EAAEgD,KAAKwB,SAASe,UAEpBvC,KAAK0B,QAAQlD,IAEjBgE,QAAS,WAAA,GACDzC,GAAOC,KACPwB,EAAUzB,EAAKyB,OACfA,KACAvD,EAAOkC,GAAGqC,QAAQpC,KAAKL,GACvBrC,EAAM+E,OAAOjB,GACb9D,EAAM8E,QAAQhB,GACVzB,EAAKD,QAAQsB,iBACbI,EAAQkB,WAIpBC,MAAO,WACH,GAAI5C,GAAOC,IACXD,GAAKyC,UACLxF,EAAE+C,EAAKyB,SAASoB,IAAI7C,EAAKV,SAASuD,IAAI7C,EAAK8C,SAASC,MAAMJ,UAE9DK,UAAW,SAAUA,GACjB/F,EAAE4C,OAAOI,KAAKgB,WAAY+B,IAE9BpB,cAAe,SAAUqB,GACrB,IAAK,GAAIC,KAAejD,MAAKgB,WACzBhB,KAAKgB,WAAWiC,GAAaD,GAAYhD,KAAMiD,IAGvD1C,eAAgB,WAAA,GAC2CiB,GAASnC,EAuBpD6D,EAvBRnD,EAAOC,KAAM6C,EAAU,IAAM9C,EAAKW,QAAU,KAChD,KACIrB,EAAUrC,EAAEmG,SAASC,eAAerD,EAAKV,UAAYU,EAAKV,SACtDA,EAAQ,GAAGqB,UAAYvC,IACvBkB,EAAUA,EAAQgE,QAExB,MAAOC,GACD3E,EAAkB4E,KAAKD,EAAEE,WACzBnE,EAAUU,EAAKV,SAyBvB,MAtBuB,gBAAZA,IACPA,EAAUA,EAAQoE,QAAQ,aAAc,IACpC1D,EAAKe,gBACLzB,EAAU3B,EAAMgG,SAASrE,GAASU,EAAKY,YAE3Ca,EAAUxE,EAAE6F,GAASpB,OAAOpC,GACvBU,EAAKa,QACNY,EAAUA,EAAQmC,cAGtBnC,EAAUnC,EACNU,EAAKe,gBACDoC,EAASlG,EAAEU,EAAMgG,SAAS1G,EAAE,WAAWyE,OAAOD,EAAQI,OAAM,IAAOyB,QAAQtD,EAAKY,YAChF3D,EAAE4G,SAAST,SAAU3B,EAAQ,KAC7BA,EAAQqC,YAAYX,GAExB1B,EAAU0B,GAEVnD,EAAKa,QACLY,EAAUA,EAAQsC,QAAQjB,GAASkB,WAGpCvC,GAEXf,gBAAiB,WACb,GAAIV,GAAOC,IACPD,GAAKD,QAAQU,iBACbT,EAAKiE,WACLjE,EAAKkE,iBACLlE,EAAKmE,gBACLnE,EAAKoE,kBAGbH,SAAU,WAAA,GAQFnB,GAPA9C,EAAOC,KACPX,EAAUU,EAAKV,OAEfU,GAAK8C,QADLxD,EAAQ+E,GAAGlG,EAAa,SACT6B,EAAKV,QAELA,EAAQwB,KAAK,aAAenD,EAAM2G,GAAK,uBAAyB3G,EAAM2G,GAAK,oBAAsB3G,EAAM2G,GAAK,+BAA+BN,SAE1JlB,EAAU9C,EAAK8C,QACnBA,EAAQjF,KAAK,KAAMmC,EAAKE,IACxB4C,EAAQyB,SAASlF,EAAWE,MAC5BuD,EAAQyB,SAASlF,EAAWI,QAC5BqD,EAAQjF,KAAK,OAAQ,SAEzBqG,eAAgB,WAAA,GAKJM,GAJJxE,EAAOC,KACP6C,EAAU7F,EAAE+C,EAAK8C,SACjB2B,EAAkBtG,EAAa,UAC9B2E,GAAQ4B,SAASD,GAAiB,KAC/BD,EAAmB1B,EAAQ4B,WAAWC,OAAO,WAC7C,GAAIC,GAAQ3H,EAAEgD,KACd,KAAK2E,EAAMP,GAAGlG,EAAa,aAAeyG,EAAMP,GAAGlG,EAAa,WAC5D,MAAOyG,KAGfJ,EAAiB1D,KAAK,QAAUjD,EAAK,QAAU,sBAEnDoC,KAAK4E,eAAiB/B,EAAQ4B,SAASvG,EAAa,YACpD8B,KAAK4E,eAAeN,SAASlF,EAAWG,eAAe+E,SAASlF,EAAWC,UAE/E6E,cAAe,WAAA,GACPnE,GAAOC,KACP6C,EAAU9C,EAAK8C,OACnB7C,MAAKP,OAASoD,EAAQ4B,SAASvG,EAAa,WAAWoG,SAASlF,EAAWK,SAE/E0E,cAAe,WAAA,GACPpE,GAAOC,KACP6C,EAAU9C,EAAK8C,OACnB7C,MAAKN,OAASmD,EAAQ4B,SAASvG,EAAa,WAAWoG,SAASlF,EAAWM,WAG/EmC,EAAYnE,EAAMmH,MAAMjF,QACxBC,KAAM,SAAUP,GACZtC,EAAE4C,OAAOI,MACLwB,QAASlC,EAAKkC,QAAQI,OAAM,GAC5BzE,WAAYmC,EAAKnC,WACjB8C,GAAIX,EAAKW,KAEbX,EAAKkC,QAAQuC,SAAStC,OAAOzB,KAAKwB,UAEtCW,QAAS,WACLnC,KAAKwB,QAAQkB,UAEjBL,iBAAkBrF,EAAE8H,KACpBxC,gBAAiBtF,EAAE8H,OAEnBC,EAASpF,EAAKC,QACdC,KAAM,SAAUR,EAASS,GACrBH,EAAKQ,GAAGN,KAAKO,KAAKJ,KAAMX,EAASS,GACjCE,KAAKgF,eAET1D,UAAW,SAAU2D,GACjB,GAAI3D,GAAYtB,KAAKgF,WAAWC,EAKhC,OAJK3D,KACDA,EAAYtB,KAAKkF,iBAAiBD,GAClCjF,KAAKgF,WAAWC,GAAY3D,GAEzBA,GAEX6D,OAAQ,SAAUF,EAAU3F,EAAMnC,GAC9B6C,KAAKsB,UAAU2D,GAAUG,KAAK9F,EAAMnC,IAExC+H,iBAAkB,SAAUD,GACxB,GAAyD3D,GAArD+D,EAAOrF,KAAKqB,SAAUG,EAAU6D,EAAKC,KAAKL,EAC9C,KAAKzD,EAAQ+D,QAAUF,EAAKjB,GAAGa,GAAW,CACtC,IAAII,EAAKjB,GAAGa,GAGR,KAAUO,OAAM,6CAAgDP,EAAW,YAF3EzD,GAAU6D,EASlB,MAJA/D,GAAY,GAAImE,GAAcjE,GAC9BF,EAAUL,KAAK,WAAY,SAAUqC,GACjCA,EAAEhE,KAAK+B,OAAOG,KAEXF,KAGXoE,EAAW/F,EAAKC,QAChB+F,OAAQ,SAAUrG,EAAM2D,GACpB3D,EAAKkC,QAAQ8D,KAAKrC,GAAaY,YAAY7D,KAAKqB,WAEpDkB,OAAQ,eAGRjF,EAAmB,4BAYnBmI,EAAgBzH,EAAW4B,QAC3BC,KAAM,SAAUyB,GACZtD,EAAWmC,GAAGN,KAAKO,KAAKJ,MACxBA,KAAKsB,UAAYA,EACjBtB,KAAK4F,WACL5F,KAAKV,KAAO,KACZU,KAAK6F,SAAU,GAEnBC,MAAO,WACH9F,KAAK6F,SAAU,EACf7F,KAAK0B,QAAQ,YAAcpC,KAAMU,KAAKV,OACtCU,KAAK0B,QAAQ,UAEjBqE,IAAK,WACD/F,KAAKV,KAAK4C,UACVlC,KAAKgG,SAAS7D,UACdnC,KAAK8F,SAETV,KAAM,SAAU9F,EAAMnC,EAAY8I,GAC9B,IAAK3G,EAAKwC,qBAAuB9B,KAAKV,OAASU,KAAKV,KAAKyC,oBAErD,MADA/B,MAAK0B,QAAQ,UACN,CAEXuE,GAAaA,GAAc3G,EAAKW,EAChC,IAAIF,GAAOC,KAAMkG,EAAU5G,IAASS,EAAKT,KAAOA,EAAKsC,QAAU7B,EAAKT,KAAMsG,EAAU7F,EAAK6F,QAASO,EAAgBP,EAAQA,EAAQL,OAAS,OAAUa,EAAOD,EAAclG,KAAOgG,EAAYI,EAAgBlJ,IAAeiJ,EAAOR,EAAQA,EAAQL,OAAS,GAAGpI,WAAamC,EAAKnC,YAAamJ,EAAiBpJ,EAAgBmJ,EAmB/T,OAlBItG,GAAK8F,SACL9F,EAAKwG,OAAOC,OAEM,SAAlBH,IACAA,EAAgB,MAEpBtG,EAAK2B,QAAQ,YAAcpC,KAAMA,IACjCS,EAAKT,KAAOA,EACZS,EAAKiG,SAAWE,EAChBnG,EAAK8F,SAAU,EACVO,EAMDR,EAAQa,MALRb,EAAQc,MACJzG,GAAIgG,EACJ9I,WAAYkJ,IAKfH,GAMAG,GAAkB3I,EAAMiJ,QAAQC,SAIjCtH,EAAKkC,QAAQ8C,SAAS,eACtBhF,EAAK0C,YACDoE,IAASjJ,IACTmJ,EAAe7I,SAAW6I,EAAe7I,SAE7CsC,EAAKwG,OAAS7I,EAAMmJ,GAAGvH,EAAKkC,SAASiC,QAAQyC,EAAQ1E,QAAS8E,EAAe/I,MAAM8E,iBAAiB,WAChG/C,EAAK+C,iBAAiB,QACtB6D,EAAQ7D,iBAAiB,UAC1BC,gBAAgB,WACfhD,EAAKgD,gBAAgB,QACrB4D,EAAQ5D,gBAAgB,UACzB9E,UAAU8I,EAAe9I,WAAWsJ,WAAWR,EAAe7I,SACjEsC,EAAKwG,OAAOQ,MAAMC,KAAK,WACnBjH,EAAKgG,UAhBTzG,EAAK0C,YACLjC,EAAKgG,QAkBF,IAzBHzG,EAAK0C,YACL1C,EAAK4C,UACLnC,EAAK+F,SACE,IAwBftD,QAAS,WAAA,GACDzC,GAAOC,KACPV,EAAOS,EAAKT,IACZA,IAAQA,EAAKkD,SACblD,EAAKkD,aAIbyE,EAAajJ,EAAW4B,QACxBC,KAAM,SAAUC,GACZ,GAAiBoH,GAAO5F,EAApBvB,EAAOC,IACXhC,GAAWmC,GAAGN,KAAKO,KAAKL,GACxBA,EAAKD,QAAUA,EACf9C,EAAE4C,OAAOG,EAAMD,GACfC,EAAKoH,QAAUnK,EAAE,WACjBsE,EAAYvB,EAAKuB,UACjB4F,EAAQnH,EAAKqH,WAAW9F,GACxBvB,EAAKsH,SAAWH,EAAMI,QACtBvH,EAAKwH,WACLxH,EAAKyH,cAAgB,GAAI9J,GAAM+H,cAAc1F,EAAKuB,WAClDvB,EAAKyH,cAAcvG,KAAK,WAAY,SAAUqC,GAC1CA,EAAEhE,KAAKmI,OAAS1H,EAAK0H,SAEzB1H,EAAKyH,cAAcvG,KAAK,WAAY,SAAUqC,GAC1CvD,EAAK2B,QAAQzC,GAAaK,KAAMgE,EAAEhE,SAEtCS,EAAKyH,cAAcvG,KAAK9B,EAAO,WAC3BY,EAAK2B,QAAQvC,KAEjBa,KAAKiB,KAAKjB,KAAK0H,OAAQ5H,IAE3B4H,QACI3I,EACAI,EACAF,EACAJ,EACAC,EACAE,EACAE,GAEJsD,QAAS,WAAA,GAIIvC,GAHLF,EAAOC,KACPwH,EAAgBzH,EAAKyH,aACzB9J,GAAM8E,QAAQzC,EAAKuB,UACnB,KAASrB,IAAMF,GAAKwH,QAChBvH,KAAKuH,QAAQtH,GAAIuC,SAEjBgF,IACAA,EAAchF,WAGtBlD,KAAM,WACF,MAAOU,MAAKwH,cAAclI,MAE9BqI,SAAU,SAAUC,EAAKzK,EAAYsK,GAKjC,GAJAG,EAAMA,EAAInE,QAAYoE,OAAO,IAAM7H,KAAK8H,qBAAsB,IAClD,KAARF,GAAc5H,KAAK8H,sBACnBF,EAAM,KAENA,EAAInE,QAAQ,KAAM,MAAQzD,KAAK4H,IAE/B,MADA5H,MAAK0B,QAAQ1C,IACN,CAEXgB,MAAK0B,QAAQ3C,EACb,IAAIgB,GAAOC,KAAMwB,EAAUzB,EAAKgI,iBAAiBH,GAAMtI,EAAO5B,EAAMsK,eAAexG,EAWnF,OAVAzB,GAAK6H,IAAMA,EAAInE,QAAQ,KAAM,IAC7B1D,EAAK0H,OAASA,EACVnI,GAAQA,EAAK2I,SACb3I,EAAKqD,QACLnB,MAEJxB,KAAK0B,QAAQxC,GACTgJ,OAA2B,IAAnB1G,EAAQ+D,OAChBqC,IAAKA,KAELpG,EAAQ,KACHlC,IACDA,EAAOS,EAAKoI,YAAY3G,IAErBzB,EAAKyH,cAAcpC,KAAK9F,EAAMnC,EAAYyK,KAKzDnG,OAAQ,SAAU4B,EAAMuE,GACpB,GAA6FV,GAAO5H,EAAhG6H,EAAUnH,KAAKmH,QAASiB,GAAWR,GAAO,IAAIS,MAAM,KAAK,GAAI/G,EAAYtB,KAAKsB,SAelF,OAdI1C,GAAW2E,KAAKF,KAChBA,EAAOwE,OAAOS,IAElBnB,EAAQ,GAAGoB,UAAYlF,EACvB/B,EAAUG,OAAO0F,EAAQ1C,SAAS,kBAClCyC,EAAQlH,KAAKoH,WAAWD,GACxB7H,EAAO4H,EAAMI,QACRhI,EAAKiG,SACN2B,EAAQ5H,EAAO6H,EAAQqB,UAAU,0BAA0B/D,YAE3D2D,GACA9I,EAAK8C,OAAOxE,KAAKA,EAAK,OAAQwK,GAElC9G,EAAUG,OAAOyF,GACVlH,KAAKmI,YAAY7I,IAE5BmJ,QAAS,SAAUC,GACf,MAAO1I,MAAK2I,SAAW5K,EAAkB2K,GAAaxK,EAAawK,IAEvEX,iBAAkB,SAAUH,GACxB,GAAIpG,GAAS4G,EAAUR,EAAIS,MAAM,KAAK,EACtC,OAAKD,IAGL5G,EAAUxB,KAAKsB,UAAUmD,SAAS,IAAM7G,EAAK,OAAS,KAAQwK,EAAU,MACnE5G,EAAQ,IAAM4G,EAAQQ,QAAQ,YAC/BpH,EAAUxB,KAAKsB,UAAUmD,SAA+B,MAAtB2D,EAAQS,OAAO,GAAaT,EAAU,IAAMA,IAE7E5G,EAAQ,KACTA,EAAUxB,KAAK8I,qBAAqBlB,IAEjCpG,GATIxB,KAAKqH,UAWpByB,qBAAsB,SAAU7I,GAC5B,GAAIuB,GAAUxB,KAAKsB,UAAUmD,SAAS,QAAWxE,EAAK,KACtD,OAAOuB,IAEX2G,YAAa,SAAU3G,GACnB,MAAOxB,MAAK+I,eAAevH,IAE/BwH,kBAAmB,SAAUxH,GACzB,MAAO9D,GAAMuL,WAAWzH,GACpB0H,kBAAmBlJ,KAAK7C,WACxBgM,OAAQnJ,KAAKmJ,OACb7H,UAAWtB,KAAKsB,UAChB8H,UAAWpJ,KAAKqJ,eAChBC,WAAYtJ,KAAKsJ,WACjBrB,OAAQnK,EAAU0D,EAAS,WAC5B3D,EAAG0L,QAEVR,eAAgB,SAAUvH,GACtB,GAAIgI,IAAexJ,KAAKF,aAAe0J,eACvC,OAAO,IAAI9L,GAAMiC,KAAK6B,GAClBlB,aAAckJ,EAAYlJ,aAC1BO,KAAM2I,EAAY3I,OAAQ,EAC1BL,eAAgBgJ,EAAYhJ,eAC5BW,aAAcqI,EAAYrI,aAC1BC,gBAAiBoI,EAAYpI,mBAGrCgG,WAAY,SAAU9F,GAClB,MAAOA,GAAUmD,SAASzE,KAAKyI,QAAQ,SAASrG,SAGxD1E,GAAMuJ,WAAaA,EACnBvJ,EAAM+H,cAAgBA,EACtB/H,EAAMgI,SAAWA,EACjBhI,EAAMqH,OAASA,EACfrH,EAAMiC,KAAOA,EACbjC,EAAMmE,UAAYA,GACpBlE,OAAOD,MAAM+L,QACR9L,OAAOD,OACE,kBAAVX,SAAwBA,OAAO2M,IAAM3M,OAAS,SAAU4M,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.view.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.view', [\n        'kendo.core',\n        'kendo.binder',\n        'kendo.fx'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'view',\n        name: 'View',\n        category: 'framework',\n        description: 'The View class instantiates and handles the events of a certain screen from the application.',\n        depends: [\n            'core',\n            'binder',\n            'fx'\n        ],\n        hidden: false\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, attr = kendo.attr, ui = kendo.ui, attrValue = kendo.attrValue, directiveSelector = kendo.directiveSelector, Observable = kendo.Observable, Widget = kendo.ui.Widget, roleSelector = kendo.roleSelector, SCRIPT = 'SCRIPT', INIT = 'init', TRANSITION_START = 'transitionStart', TRANSITION_END = 'transitionEnd', SHOW = 'show', HIDE = 'hide', ATTACH = 'attach', DETACH = 'detach', sizzleErrorRegExp = /unrecognized expression/;\n        var bodyRegExp = /<body[^>]*>(([\\u000a\\u000d\\u2028\\u2029]|.)*)<\\/body>/i;\n        var LOAD_START = 'loadStart';\n        var LOAD_COMPLETE = 'loadComplete';\n        var SHOW_START = 'showStart';\n        var SAME_VIEW_REQUESTED = 'sameViewRequested';\n        var VIEW_SHOW = 'viewShow';\n        var VIEW_TYPE_DETERMINED = 'viewTypeDetermined';\n        var AFTER = 'after';\n        var classNames = {\n            content: 'k-content',\n            view: 'k-view',\n            stretchedView: 'k-stretched-view',\n            widget: 'k-widget',\n            header: 'k-header',\n            footer: 'k-footer'\n        };\n        var View = kendo.ui.Widget.extend({\n            init: function (content, options) {\n                var that = this;\n                options = options || {};\n                that.id = kendo.guid();\n                Observable.fn.init.call(that);\n                that._initOptions(options);\n                that.content = content;\n                if (that.options.renderOnInit) {\n                    Widget.fn.init.call(that, that._createElement(), options);\n                }\n                if (that.options.wrapInSections) {\n                    that._renderSections();\n                }\n                that.tagName = options.tagName || 'div';\n                that.model = options.model;\n                that._wrap = options.wrap !== false;\n                this._evalTemplate = options.evalTemplate || false;\n                that._fragments = {};\n                that.bind([\n                    INIT,\n                    SHOW,\n                    HIDE,\n                    TRANSITION_START,\n                    TRANSITION_END\n                ], options);\n            },\n            options: {\n                name: 'View',\n                renderOnInit: false,\n                wrapInSections: false,\n                detachOnHide: true,\n                detachOnDestroy: true\n            },\n            render: function (container) {\n                var that = this, notInitialized = !that.element;\n                if (notInitialized) {\n                    that.element = that._createElement();\n                }\n                if (container) {\n                    $(container).append(that.element);\n                }\n                if (notInitialized) {\n                    kendo.bind(that.element, that.model);\n                    that.trigger(INIT);\n                }\n                if (container) {\n                    that._eachFragment(ATTACH);\n                    that.trigger(SHOW);\n                }\n                return that.element;\n            },\n            clone: function () {\n                return new ViewClone(this);\n            },\n            triggerBeforeShow: function () {\n                return true;\n            },\n            triggerBeforeHide: function () {\n                return true;\n            },\n            showStart: function () {\n                var that = this;\n                var element = that.render();\n                if (element) {\n                    element.css('display', '');\n                }\n                this.trigger(SHOW, { view: this });\n            },\n            showEnd: function () {\n            },\n            hideEnd: function () {\n                this.hide();\n            },\n            beforeTransition: function (type) {\n                this.trigger(TRANSITION_START, { type: type });\n            },\n            afterTransition: function (type) {\n                this.trigger(TRANSITION_END, { type: type });\n            },\n            hide: function () {\n                if (this.options.detachOnHide) {\n                    this._eachFragment(DETACH);\n                    $(this.element).detach();\n                }\n                this.trigger(HIDE);\n            },\n            destroy: function () {\n                var that = this;\n                var element = that.element;\n                if (element) {\n                    Widget.fn.destroy.call(that);\n                    kendo.unbind(element);\n                    kendo.destroy(element);\n                    if (that.options.detachOnDestroy) {\n                        element.remove();\n                    }\n                }\n            },\n            purge: function () {\n                var that = this;\n                that.destroy();\n                $(that.element).add(that.content).add(that.wrapper).off().remove();\n            },\n            fragments: function (fragments) {\n                $.extend(this._fragments, fragments);\n            },\n            _eachFragment: function (methodName) {\n                for (var placeholder in this._fragments) {\n                    this._fragments[placeholder][methodName](this, placeholder);\n                }\n            },\n            _createElement: function () {\n                var that = this, wrapper = '<' + that.tagName + ' />', element, content;\n                try {\n                    content = $(document.getElementById(that.content) || that.content);\n                    if (content[0].tagName === SCRIPT) {\n                        content = content.html();\n                    }\n                } catch (e) {\n                    if (sizzleErrorRegExp.test(e.message)) {\n                        content = that.content;\n                    }\n                }\n                if (typeof content === 'string') {\n                    content = content.replace(/^\\s+|\\s+$/g, '');\n                    if (that._evalTemplate) {\n                        content = kendo.template(content)(that.model || {});\n                    }\n                    element = $(wrapper).append(content);\n                    if (!that._wrap) {\n                        element = element.contents();\n                    }\n                } else {\n                    element = content;\n                    if (that._evalTemplate) {\n                        var result = $(kendo.template($('<div />').append(element.clone(true)).html())(that.model || {}));\n                        if ($.contains(document, element[0])) {\n                            element.replaceWith(result);\n                        }\n                        element = result;\n                    }\n                    if (that._wrap) {\n                        element = element.wrapAll(wrapper).parent();\n                    }\n                }\n                return element;\n            },\n            _renderSections: function () {\n                var that = this;\n                if (that.options.wrapInSections) {\n                    that._wrapper();\n                    that._createContent();\n                    that._createHeader();\n                    that._createFooter();\n                }\n            },\n            _wrapper: function () {\n                var that = this;\n                var content = that.content;\n                if (content.is(roleSelector('view'))) {\n                    that.wrapper = that.content;\n                } else {\n                    that.wrapper = content.wrap('<div data-' + kendo.ns + 'stretch=\"true\" data-' + kendo.ns + 'role=\"view\" data-' + kendo.ns + 'init-widgets=\"false\"></div>').parent();\n                }\n                var wrapper = that.wrapper;\n                wrapper.attr('id', that.id);\n                wrapper.addClass(classNames.view);\n                wrapper.addClass(classNames.widget);\n                wrapper.attr('role', 'view');\n            },\n            _createContent: function () {\n                var that = this;\n                var wrapper = $(that.wrapper);\n                var contentSelector = roleSelector('content');\n                if (!wrapper.children(contentSelector)[0]) {\n                    var ccontentElements = wrapper.children().filter(function () {\n                        var child = $(this);\n                        if (!child.is(roleSelector('header')) && !child.is(roleSelector('footer'))) {\n                            return child;\n                        }\n                    });\n                    ccontentElements.wrap('<div ' + attr('role') + '=\"content\"></div>');\n                }\n                this.contentElement = wrapper.children(roleSelector('content'));\n                this.contentElement.addClass(classNames.stretchedView).addClass(classNames.content);\n            },\n            _createHeader: function () {\n                var that = this;\n                var wrapper = that.wrapper;\n                this.header = wrapper.children(roleSelector('header')).addClass(classNames.header);\n            },\n            _createFooter: function () {\n                var that = this;\n                var wrapper = that.wrapper;\n                this.footer = wrapper.children(roleSelector('footer')).addClass(classNames.footer);\n            }\n        });\n        var ViewClone = kendo.Class.extend({\n            init: function (view) {\n                $.extend(this, {\n                    element: view.element.clone(true),\n                    transition: view.transition,\n                    id: view.id\n                });\n                view.element.parent().append(this.element);\n            },\n            hideEnd: function () {\n                this.element.remove();\n            },\n            beforeTransition: $.noop,\n            afterTransition: $.noop\n        });\n        var Layout = View.extend({\n            init: function (content, options) {\n                View.fn.init.call(this, content, options);\n                this.containers = {};\n            },\n            container: function (selector) {\n                var container = this.containers[selector];\n                if (!container) {\n                    container = this._createContainer(selector);\n                    this.containers[selector] = container;\n                }\n                return container;\n            },\n            showIn: function (selector, view, transition) {\n                this.container(selector).show(view, transition);\n            },\n            _createContainer: function (selector) {\n                var root = this.render(), element = root.find(selector), container;\n                if (!element.length && root.is(selector)) {\n                    if (root.is(selector)) {\n                        element = root;\n                    } else {\n                        throw new Error('can\\'t find a container with the specified ' + selector + ' selector');\n                    }\n                }\n                container = new ViewContainer(element);\n                container.bind('accepted', function (e) {\n                    e.view.render(element);\n                });\n                return container;\n            }\n        });\n        var Fragment = View.extend({\n            attach: function (view, placeholder) {\n                view.element.find(placeholder).replaceWith(this.render());\n            },\n            detach: function () {\n            }\n        });\n        var transitionRegExp = /^(\\w+)(:(\\w+))?( (\\w+))?$/;\n        function parseTransition(transition) {\n            if (!transition) {\n                return {};\n            }\n            var matches = transition.match(transitionRegExp) || [];\n            return {\n                type: matches[1],\n                direction: matches[3],\n                reverse: matches[5] === 'reverse'\n            };\n        }\n        var ViewContainer = Observable.extend({\n            init: function (container) {\n                Observable.fn.init.call(this);\n                this.container = container;\n                this.history = [];\n                this.view = null;\n                this.running = false;\n            },\n            after: function () {\n                this.running = false;\n                this.trigger('complete', { view: this.view });\n                this.trigger('after');\n            },\n            end: function () {\n                this.view.showEnd();\n                this.previous.hideEnd();\n                this.after();\n            },\n            show: function (view, transition, locationID) {\n                if (!view.triggerBeforeShow() || this.view && !this.view.triggerBeforeHide()) {\n                    this.trigger('after');\n                    return false;\n                }\n                locationID = locationID || view.id;\n                var that = this, current = view === that.view ? view.clone() : that.view, history = that.history, previousEntry = history[history.length - 2] || {}, back = previousEntry.id === locationID, theTransition = transition || (back ? history[history.length - 1].transition : view.transition), transitionData = parseTransition(theTransition);\n                if (that.running) {\n                    that.effect.stop();\n                }\n                if (theTransition === 'none') {\n                    theTransition = null;\n                }\n                that.trigger('accepted', { view: view });\n                that.view = view;\n                that.previous = current;\n                that.running = true;\n                if (!back) {\n                    history.push({\n                        id: locationID,\n                        transition: theTransition\n                    });\n                } else {\n                    history.pop();\n                }\n                if (!current) {\n                    view.showStart();\n                    view.showEnd();\n                    that.after();\n                    return true;\n                }\n                if (!theTransition || !kendo.effects.enabled) {\n                    view.showStart();\n                    that.end();\n                } else {\n                    view.element.addClass('k-fx-hidden');\n                    view.showStart();\n                    if (back && !transition) {\n                        transitionData.reverse = !transitionData.reverse;\n                    }\n                    that.effect = kendo.fx(view.element).replace(current.element, transitionData.type).beforeTransition(function () {\n                        view.beforeTransition('show');\n                        current.beforeTransition('hide');\n                    }).afterTransition(function () {\n                        view.afterTransition('show');\n                        current.afterTransition('hide');\n                    }).direction(transitionData.direction).setReverse(transitionData.reverse);\n                    that.effect.run().then(function () {\n                        that.end();\n                    });\n                }\n                return true;\n            },\n            destroy: function () {\n                var that = this;\n                var view = that.view;\n                if (view && view.destroy) {\n                    view.destroy();\n                }\n            }\n        });\n        var ViewEngine = Observable.extend({\n            init: function (options) {\n                var that = this, views, container;\n                Observable.fn.init.call(that);\n                that.options = options;\n                $.extend(that, options);\n                that.sandbox = $('<div />');\n                container = that.container;\n                views = that._hideViews(container);\n                that.rootView = views.first();\n                that.layouts = {};\n                that.viewContainer = new kendo.ViewContainer(that.container);\n                that.viewContainer.bind('accepted', function (e) {\n                    e.view.params = that.params;\n                });\n                that.viewContainer.bind('complete', function (e) {\n                    that.trigger(VIEW_SHOW, { view: e.view });\n                });\n                that.viewContainer.bind(AFTER, function () {\n                    that.trigger(AFTER);\n                });\n                this.bind(this.events, options);\n            },\n            events: [\n                SHOW_START,\n                AFTER,\n                VIEW_SHOW,\n                LOAD_START,\n                LOAD_COMPLETE,\n                SAME_VIEW_REQUESTED,\n                VIEW_TYPE_DETERMINED\n            ],\n            destroy: function () {\n                var that = this;\n                var viewContainer = that.viewContainer;\n                kendo.destroy(that.container);\n                for (var id in that.layouts) {\n                    this.layouts[id].destroy();\n                }\n                if (viewContainer) {\n                    viewContainer.destroy();\n                }\n            },\n            view: function () {\n                return this.viewContainer.view;\n            },\n            showView: function (url, transition, params) {\n                url = url.replace(new RegExp('^' + this.remoteViewURLPrefix), '');\n                if (url === '' && this.remoteViewURLPrefix) {\n                    url = '/';\n                }\n                if (url.replace(/^#/, '') === this.url) {\n                    this.trigger(SAME_VIEW_REQUESTED);\n                    return false;\n                }\n                this.trigger(SHOW_START);\n                var that = this, element = that._findViewElement(url), view = kendo.widgetInstance(element);\n                that.url = url.replace(/^#/, '');\n                that.params = params;\n                if (view && view.reload) {\n                    view.purge();\n                    element = [];\n                }\n                this.trigger(VIEW_TYPE_DETERMINED, {\n                    remote: element.length === 0,\n                    url: url\n                });\n                if (element[0]) {\n                    if (!view) {\n                        view = that._createView(element);\n                    }\n                    return that.viewContainer.show(view, transition, url);\n                } else {\n                    return true;\n                }\n            },\n            append: function (html, url) {\n                var sandbox = this.sandbox, urlPath = (url || '').split('?')[0], container = this.container, views, view;\n                if (bodyRegExp.test(html)) {\n                    html = RegExp.$1;\n                }\n                sandbox[0].innerHTML = html;\n                container.append(sandbox.children('script, style'));\n                views = this._hideViews(sandbox);\n                view = views.first();\n                if (!view.length) {\n                    views = view = sandbox.wrapInner('<div data-role=view />').children();\n                }\n                if (urlPath) {\n                    view.hide().attr(attr('url'), urlPath);\n                }\n                container.append(views);\n                return this._createView(view);\n            },\n            _locate: function (selectors) {\n                return this.$angular ? directiveSelector(selectors) : roleSelector(selectors);\n            },\n            _findViewElement: function (url) {\n                var element, urlPath = url.split('?')[0];\n                if (!urlPath) {\n                    return this.rootView;\n                }\n                element = this.container.children('[' + attr('url') + '=\\'' + urlPath + '\\']');\n                if (!element[0] && urlPath.indexOf('/') === -1) {\n                    element = this.container.children(urlPath.charAt(0) === '#' ? urlPath : '#' + urlPath);\n                }\n                if (!element[0]) {\n                    element = this._findViewElementById(url);\n                }\n                return element;\n            },\n            _findViewElementById: function (id) {\n                var element = this.container.children('[id=\\'' + id + '\\']');\n                return element;\n            },\n            _createView: function (element) {\n                return this._createSpaView(element);\n            },\n            _createMobileView: function (element) {\n                return kendo.initWidget(element, {\n                    defaultTransition: this.transition,\n                    loader: this.loader,\n                    container: this.container,\n                    getLayout: this.getLayoutProxy,\n                    modelScope: this.modelScope,\n                    reload: attrValue(element, 'reload')\n                }, ui.roles);\n            },\n            _createSpaView: function (element) {\n                var viewOptions = (this.options || {}).viewOptions || {};\n                return new kendo.View(element, {\n                    renderOnInit: viewOptions.renderOnInit,\n                    wrap: viewOptions.wrap || false,\n                    wrapInSections: viewOptions.wrapInSections,\n                    detachOnHide: viewOptions.detachOnHide,\n                    detachOnDestroy: viewOptions.detachOnDestroy\n                });\n            },\n            _hideViews: function (container) {\n                return container.children(this._locate('view')).hide();\n            }\n        });\n        kendo.ViewEngine = ViewEngine;\n        kendo.ViewContainer = ViewContainer;\n        kendo.Fragment = Fragment;\n        kendo.Layout = Layout;\n        kendo.View = View;\n        kendo.ViewClone = ViewClone;\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}