{"version": 3, "sources": ["kendo.maskedtextbox.js"], "names": ["f", "define", "$", "undefined", "ns", "name", "NS", "stringDiffStart", "str1", "str2", "i", "length", "escapeRegExp", "text", "replace", "global", "window", "min", "Math", "kendo", "caret", "keys", "ui", "Widget", "proxy", "setTimeout", "STATEDISABLED", "STATEINVALID", "DISABLED", "READONLY", "CHANGE", "MOUSEUP", "DROP", "KEYDOWN", "PASTE", "INPUT", "INPUT_EVENT_NAME", "support", "propertyChangeEvent", "MaskedTextBox", "extend", "init", "element", "options", "DOMElement", "disabled", "that", "this", "fn", "call", "_rules", "rules", "_wrapper", "_tokenize", "_form", "addClass", "attr", "on", "value", "_togglePrompt", "_old", "_emptyMask", "_old<PERSON><PERSON><PERSON>", "_timeoutId", "_maskLength", "val", "clearTimeout", "_change", "is", "parents", "enable", "readonly", "_validationIcon", "insertAfter", "notify", "clearPromptChar", "unmaskOnPost", "promptChar", "culture", "mask", "events", "0", "9", "#", "L", "?", "&", "C", "A", "a", "setOptions", "_unbindInput", "_bindInput", "destroy", "off", "_formElement", "_reset<PERSON><PERSON><PERSON>", "_submit<PERSON><PERSON><PERSON>", "raw", "unmasked", "_unmask", "RegExp", "emptyMask", "_mask", "_unmaskedValue", "_activeElement", "show", "_editable", "disable", "version", "$angular", "_keydown", "_drop", "_trackChange", "_input<PERSON><PERSON><PERSON>", "browser", "msie", "join", "_legacyIEInputHandler", "wrapper", "toggleClass", "removeAttr", "removeClass", "trigger", "__changing", "inputChange", "backward", "contentStart", "content", "caretPos", "endContent", "old", "selection", "cursor", "lengthDiff", "mobile", "mobileOS", "__dropping", "android", "substring", "_trimStartPromptChars", "_findCaretPosBackwards", "count", "indexOf", "pos", "caretStart", "_find", "__backward", "e", "input", "type", "__pasting", "formId", "form", "closest", "key", "keyCode", "BACKSPACE", "ENTER", "idx", "step", "char<PERSON>t", "tokens", "start", "end", "valueLength", "chr", "current", "empty", "chrIdx", "split", "token", "tokenIdx", "tokensLength", "result", "test", "isFunction", "_blinkInvalidState", "wrap", "parent", "style", "cssText", "width", "className", "_invalidStateTimeout", "_removeInvalidState", "rule", "l", "mask<PERSON><PERSON><PERSON>", "numberFormat", "getCulture", "currency", "symbol", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,uBAAwB,cAAeD,IAChD,WAsfE,MA9eC,UAAUE,EAAGC,GAqBV,QAASC,GAAGC,GACR,MAAOA,GAAOC,EAGlB,QAASC,GAAgBC,EAAMC,GAE3B,IADA,GAAIC,GAAI,EACDA,EAAID,EAAKE,QACRH,EAAKE,KAAOD,EAAKC,IAGrBA,GAEJ,OAAOA,GAwcX,QAASE,GAAaC,GAClB,MAAOA,GAAKC,QAAQ,2BAA4B,QA1evD,GACOC,GAASC,OACTC,EAAMF,EAAOG,KAAKD,IAClBE,EAAQJ,EAAOI,MACfC,EAAQD,EAAMC,MACdC,EAAOF,EAAME,KACbC,EAAKH,EAAMG,GACXC,EAASD,EAAGC,OACZjB,EAAK,sBACLkB,EAAQtB,EAAEsB,MACVC,EAAaT,OAAOS,WACpBC,EAAgB,mBAChBC,EAAe,kBACfC,EAAW,WACXC,EAAW,WACXC,EAAS,SACTC,EAAU,UACVC,EAAO,OACPC,EAAU,UACVC,EAAQ,QACRC,EAAQ,QAIRC,EAAmBhC,EAAGe,EAAMkB,QAAQC,oBAAsB,iBAAmBH,GAW7EI,EAAgBhB,EAAOiB,QACvBC,KAAM,SAAUC,EAASC,GAAnB,GAEEC,GA6BAC,EA9BAC,EAAOC,IAEXxB,GAAOyB,GAAGP,KAAKQ,KAAKH,EAAMJ,EAASC,GACnCG,EAAKI,OAAShD,EAAEsC,UAAWM,EAAKK,MAAOL,EAAKH,QAAQQ,OACpDT,EAAUI,EAAKJ,QACfE,EAAaF,EAAQ,GACrBI,EAAKM,WACLN,EAAKO,YACLP,EAAKQ,QACLR,EAAKJ,QAAQa,SAAS,aAAaC,KAAK,eAAgB,OAAOC,GAAG,QAAUnD,EAAI,WAC5E,GAAIoD,GAAQd,EAAWc,KAClBA,GAGDZ,EAAKa,eAAc,GAFnBf,EAAWc,MAAQZ,EAAKc,KAAOd,EAAKe,WAIxCf,EAAKgB,UAAYJ,EACjBZ,EAAKiB,WAAatC,EAAW,WACzBL,EAAMsB,EAAS,EAAGgB,EAAQZ,EAAKkB,YAAc,OAElDP,GAAG,WAAanD,EAAI,WACnB,GAAIoD,GAAQhB,EAAQuB,KACpBC,cAAapB,EAAKiB,YAClBnB,EAAWc,MAAQZ,EAAKc,KAAO,GAC3BF,IAAUZ,EAAKe,aACfjB,EAAWc,MAAQZ,EAAKc,KAAOF,GAEnCZ,EAAKqB,UACLrB,EAAKa,kBAELd,EAAWH,EAAQ0B,GAAG,eAAiBlE,EAAE4C,EAAKJ,SAAS2B,QAAQ,YAAYD,GAAG,aAC9EvB,EACAC,EAAKwB,QAAO,GAEZxB,EAAKyB,SAAS7B,EAAQ0B,GAAG,eAE7BtB,EAAKY,MAAMZ,EAAKH,QAAQe,OAAShB,EAAQuB,OACzCnB,EAAK0B,gBAAkBtE,EAAE,4CAA8CuE,YAAY/B,GACnFvB,EAAMuD,OAAO5B,IAEjBH,SACItC,KAAM,gBACNsE,iBAAiB,EACjBC,cAAc,EACdC,WAAY,IACZC,QAAS,GACT3B,SACAO,MAAO,GACPqB,KAAM,IAEVC,QAASlD,GACTqB,OACI8B,EAAK,KACLC,EAAK,QACLC,IAAK,cACLC,EAAK,WACLC,IAAK,cACLC,IAAK,KACLC,EAAK,IACLC,EAAK,cACLC,EAAK,kBAETC,WAAY,SAAU/C,GAClB,GAAIG,GAAOC,IACXxB,GAAOyB,GAAG0C,WAAWzC,KAAKH,EAAMH,GAChCG,EAAKI,OAAShD,EAAEsC,UAAWM,EAAKK,MAAOL,EAAKH,QAAQQ,OACpDL,EAAKO,YACLN,KAAK4C,eACL5C,KAAK6C,aACL9C,EAAKY,MAAMZ,EAAKJ,QAAQuB,QAE5B4B,QAAS,WACL,GAAI/C,GAAOC,IACXD,GAAKJ,QAAQoD,IAAIxF,GACbwC,EAAKiD,eACLjD,EAAKiD,aAAaD,IAAI,QAAShD,EAAKkD,eACpClD,EAAKiD,aAAaD,IAAI,SAAUhD,EAAKmD,iBAEzC1E,EAAOyB,GAAG6C,QAAQ5C,KAAKH,IAE3BoD,IAAK,WACD,GAAIC,GAAWpD,KAAKqD,QAAQrD,KAAKL,QAAQuB,MAAO,EAChD,OAAOkC,GAASrF,QAAYuF,OAAOzF,EAAamC,KAAKJ,QAAQkC,YAAa,KAAM,KAEpFnB,MAAO,SAAUA,GAAV,GACChB,GAAUK,KAAKL,QACf4D,EAAYvD,KAAKc,UACrB,OAAIH,KAAUvD,EACH4C,KAAKL,QAAQuB,OAEV,OAAVP,IACAA,EAAQ,IAEP4C,GAKL5C,EAAQX,KAAKqD,QAAQ1C,EAAQ,IAC7BhB,EAAQuB,IAAIP,EAAQ4C,EAAY,IAChCvD,KAAKwD,MAAM,EAAGxD,KAAKiB,YAAaN,GAChCX,KAAKyD,eAAiB,KACtB9C,EAAQhB,EAAQuB,MAChBlB,KAAKe,UAAYJ,EACbvC,EAAMsF,mBAAqB/D,IACvBgB,IAAU4C,EACV5D,EAAQuB,IAAI,IAEZlB,KAAKY,iBAVbD,IAJIX,KAAKe,UAAYJ,EACjBhB,EAAQuB,IAAIP,GACZ,KAgBRC,cAAe,SAAU+C,GAAV,GACP9D,GAAaG,KAAKL,QAAQ,GAC1BgB,EAAQd,EAAWc,KACnBX,MAAKJ,QAAQgC,kBAITjB,EAHCgD,EAGO3D,KAAKe,UAFLJ,EAAM5C,QAAYuF,OAAOzF,EAAamC,KAAKJ,QAAQkC,YAAa,KAAM,KAIlFjC,EAAWc,MAAQX,KAAKa,KAAOF,IAGvCa,SAAU,SAAUA,GAChBxB,KAAK4D,WACDpC,SAAUA,IAAapE,GAAmBoE,EAC1CqC,SAAS,KAGjBtC,OAAQ,SAAUA,GACdvB,KAAK4D,WACDpC,UAAU,EACVqC,UAAWtC,EAASA,IAAWnE,GAAmBmE,MAG1DsB,WAAY,WAAA,GAQIiB,GAEI7B,EATZlC,EAAOC,IACPD,GAAKkB,cACDlB,EAAKH,QAAQmE,UACbhE,EAAKJ,QAAQoD,IAAI3D,GAErBW,EAAKJ,QAAQe,GAAGrD,EAAG6B,GAAUT,EAAMsB,EAAKiE,SAAUjE,IAAOW,GAAGrD,EAAG4B,GAAOR,EAAMsB,EAAKkE,MAAOlE,IAAOW,GAAGrD,EAAG0B,GAASN,EAAMsB,EAAKmE,aAAcnE,IAAOW,GAAGrB,EAAkBZ,EAAMsB,EAAKoE,cAAepE,IACzL3B,EAAMkB,QAAQ8E,QAAQC,OAClBP,EAAU1F,EAAMkB,QAAQ8E,QAAQN,QAChCA,EAAU,GAAKA,EAAU,KACrB7B,GACA5E,EAAG2B,GACH3B,EAAG4B,GACH5B,EAAG6B,GACH7B,EAAG8B,IACLmF,KAAK,KACPvE,EAAKJ,QAAQe,GAAGuB,EAAQxD,EAAMsB,EAAKwE,sBAAuBxE,QAK1E6C,aAAc,WACV,GAAIX,IACA5C,EACAhC,EAAG6B,GACH7B,EAAG2B,GACH3B,EAAG4B,GACH5B,EAAG8B,IACLmF,KAAK,IACPtE,MAAKL,QAAQoD,IAAId,IAErB2B,UAAW,SAAUhE,GAAV,GACHG,GAAOC,KACPL,EAAUI,EAAKJ,QACf6E,EAAUzE,EAAKyE,QACfX,EAAUjE,EAAQiE,QAClBrC,EAAW5B,EAAQ4B,QACvBzB,GAAK6C,eACApB,GAAaqC,GAKdlE,EAAQc,KAAK5B,EAAUgF,GAASpD,KAAK3B,EAAU0C,GAC/CgD,EAAQC,YAAY9F,EAAekF,KALnClE,EAAQ+E,WAAW7F,GAAU6F,WAAW5F,GACxC0F,EAAQG,YAAYhG,GACpBoB,EAAK8C,eAMbzB,QAAS,WAAA,GACDrB,GAAOC,KACPW,EAAQZ,EAAKY,OACbA,KAAUZ,EAAKgB,WACfhB,EAAKgB,UAAYJ,EACjBZ,EAAK6E,QAAQ7F,GACbgB,EAAKJ,QAAQiF,QAAQ7F,IACJ,KAAV4B,GAAgBZ,EAAK8E,YAC5B9E,EAAKJ,QAAQiF,QAAQ7F,IAG7B+F,YAAa,SAAUC,GAAV,GAeLC,GACAC,EAEAC,EACAC,EACA/B,EAnBArD,EAAOC,KACPoF,EAAMrF,EAAKc,KACXlB,EAAUI,EAAKJ,QAAQ,GACvBgB,EAAQhB,EAAQgB,MAChB0E,EAAYhH,EAAMsB,GAClB2F,EAASD,EAAU,GACnBE,EAAa5E,EAAM/C,OAASwH,EAAIxH,OAChC4H,EAASpH,EAAMkB,QAAQmG,QACvB1F,GAAK2F,YAAcH,EAAa,IAGhCA,QAAqBC,EAAOG,SAA8B,WAAnBH,EAAOpB,UAC9CW,GAAW,GAEXC,EAAe9G,EAAIoH,EAAQ9H,EAAgBmD,EAAOyE,IAClDH,EAAUtE,EAAMiF,UAAUZ,EAAcM,GAC5C3F,EAAQgB,MAAQA,EAAMiF,UAAU,EAAGZ,GAAgBjF,EAAKe,WAAW8E,UAAUZ,GACzEE,EAAWnF,EAAKyD,MAAMwB,EAAcM,EAAQL,GAC5CE,EAAapF,EAAK8F,sBAAsBlF,EAAMiF,UAAUN,GAASpH,EAAIqH,EAAYL,EAAWF,IAC5F5B,EAAWrD,EAAKsD,QAAQ8B,EAAYC,EAAIxH,OAASuH,EAAWvH,QAChEmC,EAAKyD,MAAM0B,EAAUA,EAAU9B,GAC3B2B,IACAG,EAAWnF,EAAK+F,uBAAuBd,IAE3C3G,EAAMsB,EAASuF,GACfnF,EAAK2F,YAAa,IAEtBG,sBAAuB,SAAUZ,EAASc,GAEtC,IADA,GAAIjE,GAAa9B,KAAKJ,QAAQkC,WACvBiE,KAAU,GAAqC,IAAhCd,EAAQe,QAAQlE,IAClCmD,EAAUA,EAAQW,UAAU,EAEhC,OAAOX,IAEXa,uBAAwB,SAAUG,GAC9B,GAAIC,GAAalG,KAAKmG,MAAMF,GAAK,EAIjC,OAHIC,GAAaD,IACbC,GAAc,GAEXA,GAEX/B,cAAe,WACP/F,EAAMsF,mBAAqB1D,KAAKL,QAAQ,IAG5CK,KAAK8E,YAAY9E,KAAKoG,aAE1B7B,sBAAuB,SAAU8B,GAAV,GACftG,GAAOC,KACPsG,EAAQvG,EAAKJ,QAAQ,GACrBgB,EAAQ2F,EAAM3F,MACd4F,EAAOF,EAAEE,IACbxG,GAAKyG,UAAqB,UAATD,EACjB7H,EAAW,WACM,YAAT6H,GAAsBxG,EAAKyG,WAG3BF,EAAM3F,OAAS2F,EAAM3F,QAAUA,GAC/BZ,EAAK+E,YAAY/E,EAAKqG,eAIlClC,aAAc,WACV,GAAInE,GAAOC,IACXD,GAAK8E,YAAa,EAClBnG,EAAW,WACPqB,EAAK8E,YAAa,KAG1BtE,MAAO,WAAA,GACCR,GAAOC,KACPL,EAAUI,EAAKJ,QACf8G,EAAS9G,EAAQc,KAAK,QACtBiG,EAAOD,EAAStJ,EAAE,IAAMsJ,GAAU9G,EAAQgH,QAAQ,OAClDD,GAAK,KACL3G,EAAKkD,cAAgB,WACjBvE,EAAW,WACPqB,EAAKY,MAAMhB,EAAQ,GAAGgB,UAG9BZ,EAAKmD,eAAiB,WAClBnD,EAAKJ,QAAQ,GAAGgB,MAAQZ,EAAKc,KAAOd,EAAKoD,OAEzCpD,EAAKH,QAAQiC,cACb6E,EAAKhG,GAAG,SAAUX,EAAKmD,gBAE3BnD,EAAKiD,aAAe0D,EAAKhG,GAAG,QAASX,EAAKkD,iBAGlDe,SAAU,SAAUqC,GAChB,GAAIO,GAAMP,EAAEQ,OACZ7G,MAAKoG,WAAaQ,IAAQtI,EAAKwI,UAC3BF,IAAQtI,EAAKyI,OACb/G,KAAKoB,WAGb6C,MAAO,WACHjE,KAAK0F,YAAa,GAEtBS,MAAO,SAAUa,EAAKjC,GAAf,GACCpE,GAAQX,KAAKL,QAAQuB,OAASlB,KAAKc,WACnCmG,EAAO,CAIX,KAHIlC,KAAa,IACbkC,MAEGD,MAAYA,GAAOhH,KAAKiB,aAAa,CACxC,GAAIN,EAAMuG,OAAOF,KAAShH,KAAKmH,OAAOH,GAClC,MAAOA,EAEXA,IAAOC,EAEX,UAEJzD,MAAO,SAAU4D,EAAOC,EAAK1G,EAAOoE,GAA7B,GAICuC,GAEAlE,EACAmE,EACAP,EAPArH,EAAUK,KAAKL,QAAQ,GACvB6H,EAAU7H,EAAQgB,OAASX,KAAKc,WAChC2G,EAAQzH,KAAKJ,QAAQkC,WAErB4F,EAAS,CAiBb,KAbAN,EAAQpH,KAAKmG,MAAMiB,EAAOrC,GACtBqC,EAAQC,IACRA,EAAMD,GAEVhE,EAAWpD,KAAKqD,QAAQmE,EAAQ5B,UAAUyB,GAAMA,GAChD1G,EAAQX,KAAKqD,QAAQ1C,EAAOyG,GAC5BE,EAAc3G,EAAM/C,OAChB+C,IACAyC,EAAWA,EAASrF,QAAYuF,OAAO,QAAUgE,EAAc,KAAM,KAEzE3G,GAASyC,EACToE,EAAUA,EAAQG,MAAM,IACxBJ,EAAM5G,EAAMuG,OAAOQ,GACZN,EAAQpH,KAAKiB,aAChBuG,EAAQJ,GAASG,GAAOE,EACxBF,EAAM5G,EAAMuG,SAASQ,GACjBV,IAAQ5J,GAAasK,EAASJ,IAC9BN,EAAMI,GAEVA,EAAQpH,KAAKmG,MAAMiB,EAAQ,EAS/B,OAPAzH,GAAQgB,MAAQX,KAAKa,KAAO2G,EAAQlD,KAAK,IACrClG,EAAMsF,mBAAqB/D,IACvBqH,IAAQ5J,IACR4J,EAAMhH,KAAKiB,aAEf5C,EAAMsB,EAASqH,IAEZA,GAEX3D,QAAS,SAAU1C,EAAOqG,GAAjB,GAQDO,GACAK,EACAF,EACAG,EACAJ,EACAH,EACAQ,EACAC,CAdJ,KAAKpH,EACD,MAAO,EAEX,IAAIX,KAAKyD,iBAAmB9C,EACxB,MAAOX,MAAKyD,cAWhB,KATA9C,GAASA,EAAQ,IAAIgH,MAAM,IAGvBD,EAAS,EACTG,EAAWb,GAAO,EAClBS,EAAQzH,KAAKJ,QAAQkC,WACrBwF,EAAc3G,EAAM/C,OACpBkK,EAAe9H,KAAKmH,OAAOvJ,OAC3BmK,EAAS,GACNF,EAAWC,IACdP,EAAM5G,EAAM+G,GACZE,EAAQ5H,KAAKmH,OAAOU,GAChBN,IAAQK,GAASL,IAAQE,GACzBM,GAAUR,IAAQE,EAAQA,EAAQ,GAClCC,GAAU,EACVG,GAAY,GACY,gBAAVD,IACVA,GAASA,EAAMI,MAAQJ,EAAMI,KAAKT,IAAQpK,EAAE8K,WAAWL,IAAUA,EAAML,IACvEQ,GAAUR,EACVM,GAAY,GAEQ,IAAhBP,GACAtH,KAAKkI,qBAGbR,GAAU,GAEVG,GAAY,IAEZH,GAAUJ,MAKlB,MADAtH,MAAKyD,eAAiBsE,EACfA,GAEX1H,SAAU,WAAA,GACFN,GAAOC,KACPL,EAAUI,EAAKJ,QACfE,EAAaF,EAAQ,GACrB6E,EAAU7E,EAAQwI,KAAK,kDAAoDC,QAC/E5D,GAAQ,GAAG6D,MAAMC,QAAUzI,EAAWwI,MAAMC,QAC5CzI,EAAWwI,MAAME,MAAQ,OACzBxI,EAAKyE,QAAUA,EAAQhE,SAASX,EAAW2I,YAE/CN,mBAAoB,WAChB,GAAInI,GAAOC,IACXD,GAAKyE,QAAQhE,SAAS5B,GACtBuC,aAAapB,EAAK0I,sBAClB1I,EAAK0I,qBAAuB/J,EAAWD,EAAMsB,EAAK2I,oBAAqB3I,GAAO,MAElF2I,oBAAqB,WACjB,GAAI3I,GAAOC,IACXD,GAAKyE,QAAQG,YAAY/F,GACzBmB,EAAK0I,qBAAuB,MAEhCnI,UAAW,WAaP,IAbO,GAOHiH,GACAoB,EAsBahL,EAAOiL,EA7BpBzB,KACAU,EAAW,EACX7F,EAAOhC,KAAKJ,QAAQoC,MAAQ,GAC5B6G,EAAY7G,EAAK2F,MAAM,IACvB/J,EAASiL,EAAUjL,OACnBoJ,EAAM,EAGNzD,EAAY,GACZzB,EAAa9B,KAAKJ,QAAQkC,WAC1BgH,EAAe1K,EAAM2K,WAAW/I,KAAKJ,QAAQmC,SAAS+G,aACtD1I,EAAQJ,KAAKG,OACV6G,EAAMpJ,EAAQoJ,IAGjB,GAFAO,EAAMsB,EAAU7B,GAChB2B,EAAOvI,EAAMmH,GAETJ,EAAOU,GAAYc,EACnBpF,GAAazB,EACb+F,GAAY,MAWZ,KATY,MAARN,GAAuB,MAARA,EACfA,EAAMuB,EAAavB,GACJ,MAARA,EACPA,EAAMuB,EAAaE,SAASC,OACb,OAAR1B,IACPP,GAAO,EACPO,EAAMsB,EAAU7B,IAEpBO,EAAMA,EAAII,MAAM,IACPhK,EAAI,EAAGiL,EAAIrB,EAAI3J,OAAQD,EAAIiL,EAAGjL,IACnCwJ,EAAOU,GAAYN,EAAI5J,GACvB4F,GAAagE,EAAI5J,GACjBkK,GAAY,CAIxB7H,MAAKmH,OAASA,EACdnH,KAAKc,WAAayC,EAClBvD,KAAKiB,YAAcsC,EAAU3F,SAMrCW,GAAG2K,OAAO1J,IACZvB,OAAOG,MAAM+K,QACRlL,OAAOG,OACE,kBAAVlB,SAAwBA,OAAOkM,IAAMlM,OAAS,SAAUmM,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.maskedtextbox.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.maskedtextbox', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'maskedtextbox',\n        name: 'MaskedTextBox',\n        category: 'web',\n        description: 'The MaskedTextBox widget allows to specify a mask type on an input field.',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var global = window;\n        var min = global.Math.min;\n        var kendo = global.kendo;\n        var caret = kendo.caret;\n        var keys = kendo.keys;\n        var ui = kendo.ui;\n        var Widget = ui.Widget;\n        var NS = '.kendoMaskedTextBox';\n        var proxy = $.proxy;\n        var setTimeout = window.setTimeout;\n        var STATEDISABLED = 'k-state-disabled';\n        var STATEINVALID = 'k-state-invalid';\n        var DISABLED = 'disabled';\n        var READONLY = 'readonly';\n        var CHANGE = 'change';\n        var MOUSEUP = 'mouseup';\n        var DROP = 'drop';\n        var KEYDOWN = 'keydown';\n        var PASTE = 'paste';\n        var INPUT = 'input';\n        function ns(name) {\n            return name + NS;\n        }\n        var INPUT_EVENT_NAME = ns(kendo.support.propertyChangeEvent ? 'propertychange' : INPUT);\n        function stringDiffStart(str1, str2) {\n            var i = 0;\n            while (i < str2.length) {\n                if (str1[i] !== str2[i]) {\n                    break;\n                }\n                i++;\n            }\n            return i;\n        }\n        var MaskedTextBox = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                var DOMElement;\n                Widget.fn.init.call(that, element, options);\n                that._rules = $.extend({}, that.rules, that.options.rules);\n                element = that.element;\n                DOMElement = element[0];\n                that._wrapper();\n                that._tokenize();\n                that._form();\n                that.element.addClass('k-textbox').attr('autocomplete', 'off').on('focus' + NS, function () {\n                    var value = DOMElement.value;\n                    if (!value) {\n                        DOMElement.value = that._old = that._emptyMask;\n                    } else {\n                        that._togglePrompt(true);\n                    }\n                    that._oldValue = value;\n                    that._timeoutId = setTimeout(function () {\n                        caret(element, 0, value ? that._maskLength : 0);\n                    });\n                }).on('focusout' + NS, function () {\n                    var value = element.val();\n                    clearTimeout(that._timeoutId);\n                    DOMElement.value = that._old = '';\n                    if (value !== that._emptyMask) {\n                        DOMElement.value = that._old = value;\n                    }\n                    that._change();\n                    that._togglePrompt();\n                });\n                var disabled = element.is('[disabled]') || $(that.element).parents('fieldset').is(':disabled');\n                if (disabled) {\n                    that.enable(false);\n                } else {\n                    that.readonly(element.is('[readonly]'));\n                }\n                that.value(that.options.value || element.val());\n                that._validationIcon = $('<span class=\\'k-icon k-i-warning\\'></span>').insertAfter(element);\n                kendo.notify(that);\n            },\n            options: {\n                name: 'MaskedTextBox',\n                clearPromptChar: false,\n                unmaskOnPost: false,\n                promptChar: '_',\n                culture: '',\n                rules: {},\n                value: '',\n                mask: ''\n            },\n            events: [CHANGE],\n            rules: {\n                '0': /\\d/,\n                '9': /\\d|\\s/,\n                '#': /\\d|\\s|\\+|\\-/,\n                'L': /[a-zA-Z]/,\n                '?': /[a-zA-Z]|\\s/,\n                '&': /\\S/,\n                'C': /./,\n                'A': /[a-zA-Z0-9]/,\n                'a': /[a-zA-Z0-9]|\\s/\n            },\n            setOptions: function (options) {\n                var that = this;\n                Widget.fn.setOptions.call(that, options);\n                that._rules = $.extend({}, that.rules, that.options.rules);\n                that._tokenize();\n                this._unbindInput();\n                this._bindInput();\n                that.value(that.element.val());\n            },\n            destroy: function () {\n                var that = this;\n                that.element.off(NS);\n                if (that._formElement) {\n                    that._formElement.off('reset', that._resetHandler);\n                    that._formElement.off('submit', that._submitHandler);\n                }\n                Widget.fn.destroy.call(that);\n            },\n            raw: function () {\n                var unmasked = this._unmask(this.element.val(), 0);\n                return unmasked.replace(new RegExp(escapeRegExp(this.options.promptChar), 'g'), '');\n            },\n            value: function (value) {\n                var element = this.element;\n                var emptyMask = this._emptyMask;\n                if (value === undefined) {\n                    return this.element.val();\n                }\n                if (value === null) {\n                    value = '';\n                }\n                if (!emptyMask) {\n                    this._oldValue = value;\n                    element.val(value);\n                    return;\n                }\n                value = this._unmask(value + '');\n                element.val(value ? emptyMask : '');\n                this._mask(0, this._maskLength, value);\n                this._unmaskedValue = null;\n                value = element.val();\n                this._oldValue = value;\n                if (kendo._activeElement() !== element) {\n                    if (value === emptyMask) {\n                        element.val('');\n                    } else {\n                        this._togglePrompt();\n                    }\n                }\n            },\n            _togglePrompt: function (show) {\n                var DOMElement = this.element[0];\n                var value = DOMElement.value;\n                if (this.options.clearPromptChar) {\n                    if (!show) {\n                        value = value.replace(new RegExp(escapeRegExp(this.options.promptChar), 'g'), ' ');\n                    } else {\n                        value = this._oldValue;\n                    }\n                    DOMElement.value = this._old = value;\n                }\n            },\n            readonly: function (readonly) {\n                this._editable({\n                    readonly: readonly === undefined ? true : readonly,\n                    disable: false\n                });\n            },\n            enable: function (enable) {\n                this._editable({\n                    readonly: false,\n                    disable: !(enable = enable === undefined ? true : enable)\n                });\n            },\n            _bindInput: function () {\n                var that = this;\n                if (that._maskLength) {\n                    if (that.options.$angular) {\n                        that.element.off(INPUT);\n                    }\n                    that.element.on(ns(KEYDOWN), proxy(that._keydown, that)).on(ns(DROP), proxy(that._drop, that)).on(ns(CHANGE), proxy(that._trackChange, that)).on(INPUT_EVENT_NAME, proxy(that._inputHandler, that));\n                    if (kendo.support.browser.msie) {\n                        var version = kendo.support.browser.version;\n                        if (version > 8 && version < 11) {\n                            var events = [\n                                ns(MOUSEUP),\n                                ns(DROP),\n                                ns(KEYDOWN),\n                                ns(PASTE)\n                            ].join(' ');\n                            that.element.on(events, proxy(that._legacyIEInputHandler, that));\n                        }\n                    }\n                }\n            },\n            _unbindInput: function () {\n                var events = [\n                    INPUT_EVENT_NAME,\n                    ns(KEYDOWN),\n                    ns(MOUSEUP),\n                    ns(DROP),\n                    ns(PASTE)\n                ].join(' ');\n                this.element.off(events);\n            },\n            _editable: function (options) {\n                var that = this;\n                var element = that.element;\n                var wrapper = that.wrapper;\n                var disable = options.disable;\n                var readonly = options.readonly;\n                that._unbindInput();\n                if (!readonly && !disable) {\n                    element.removeAttr(DISABLED).removeAttr(READONLY);\n                    wrapper.removeClass(STATEDISABLED);\n                    that._bindInput();\n                } else {\n                    element.attr(DISABLED, disable).attr(READONLY, readonly);\n                    wrapper.toggleClass(STATEDISABLED, disable);\n                }\n            },\n            _change: function () {\n                var that = this;\n                var value = that.value();\n                if (value !== that._oldValue) {\n                    that._oldValue = value;\n                    that.trigger(CHANGE);\n                    that.element.trigger(CHANGE);\n                } else if (value === '' && that.__changing) {\n                    that.element.trigger(CHANGE);\n                }\n            },\n            inputChange: function (backward) {\n                var that = this;\n                var old = that._old;\n                var element = that.element[0];\n                var value = element.value;\n                var selection = caret(element);\n                var cursor = selection[1];\n                var lengthDiff = value.length - old.length;\n                var mobile = kendo.support.mobileOS;\n                if (that.__dropping && lengthDiff < 0) {\n                    return;\n                }\n                if (lengthDiff === -1 && mobile.android && mobile.browser === 'chrome') {\n                    backward = true;\n                }\n                var contentStart = min(cursor, stringDiffStart(value, old));\n                var content = value.substring(contentStart, cursor);\n                element.value = value.substring(0, contentStart) + that._emptyMask.substring(contentStart);\n                var caretPos = that._mask(contentStart, cursor, content);\n                var endContent = that._trimStartPromptChars(value.substring(cursor), min(lengthDiff, caretPos - contentStart));\n                var unmasked = that._unmask(endContent, old.length - endContent.length);\n                that._mask(caretPos, caretPos, unmasked);\n                if (backward) {\n                    caretPos = that._findCaretPosBackwards(contentStart);\n                }\n                caret(element, caretPos);\n                that.__dropping = false;\n            },\n            _trimStartPromptChars: function (content, count) {\n                var promptChar = this.options.promptChar;\n                while (count-- > 0 && content.indexOf(promptChar) === 0) {\n                    content = content.substring(1);\n                }\n                return content;\n            },\n            _findCaretPosBackwards: function (pos) {\n                var caretStart = this._find(pos, true);\n                if (caretStart < pos) {\n                    caretStart += 1;\n                }\n                return caretStart;\n            },\n            _inputHandler: function () {\n                if (kendo._activeElement() !== this.element[0]) {\n                    return;\n                }\n                this.inputChange(this.__backward);\n            },\n            _legacyIEInputHandler: function (e) {\n                var that = this;\n                var input = that.element[0];\n                var value = input.value;\n                var type = e.type;\n                that.__pasting = type === 'paste';\n                setTimeout(function () {\n                    if (type === 'mouseup' && that.__pasting) {\n                        return;\n                    }\n                    if (input.value && input.value !== value) {\n                        that.inputChange(that.__backward);\n                    }\n                });\n            },\n            _trackChange: function () {\n                var that = this;\n                that.__changing = true;\n                setTimeout(function () {\n                    that.__changing = false;\n                });\n            },\n            _form: function () {\n                var that = this;\n                var element = that.element;\n                var formId = element.attr('form');\n                var form = formId ? $('#' + formId) : element.closest('form');\n                if (form[0]) {\n                    that._resetHandler = function () {\n                        setTimeout(function () {\n                            that.value(element[0].value);\n                        });\n                    };\n                    that._submitHandler = function () {\n                        that.element[0].value = that._old = that.raw();\n                    };\n                    if (that.options.unmaskOnPost) {\n                        form.on('submit', that._submitHandler);\n                    }\n                    that._formElement = form.on('reset', that._resetHandler);\n                }\n            },\n            _keydown: function (e) {\n                var key = e.keyCode;\n                this.__backward = key === keys.BACKSPACE;\n                if (key === keys.ENTER) {\n                    this._change();\n                }\n            },\n            _drop: function () {\n                this.__dropping = true;\n            },\n            _find: function (idx, backward) {\n                var value = this.element.val() || this._emptyMask;\n                var step = 1;\n                if (backward === true) {\n                    step = -1;\n                }\n                while (idx > -1 || idx <= this._maskLength) {\n                    if (value.charAt(idx) !== this.tokens[idx]) {\n                        return idx;\n                    }\n                    idx += step;\n                }\n                return -1;\n            },\n            _mask: function (start, end, value, backward) {\n                var element = this.element[0];\n                var current = element.value || this._emptyMask;\n                var empty = this.options.promptChar;\n                var valueLength;\n                var chrIdx = 0;\n                var unmasked;\n                var chr;\n                var idx;\n                start = this._find(start, backward);\n                if (start > end) {\n                    end = start;\n                }\n                unmasked = this._unmask(current.substring(end), end);\n                value = this._unmask(value, start);\n                valueLength = value.length;\n                if (value) {\n                    unmasked = unmasked.replace(new RegExp('^_{0,' + valueLength + '}'), '');\n                }\n                value += unmasked;\n                current = current.split('');\n                chr = value.charAt(chrIdx);\n                while (start < this._maskLength) {\n                    current[start] = chr || empty;\n                    chr = value.charAt(++chrIdx);\n                    if (idx === undefined && chrIdx > valueLength) {\n                        idx = start;\n                    }\n                    start = this._find(start + 1);\n                }\n                element.value = this._old = current.join('');\n                if (kendo._activeElement() === element) {\n                    if (idx === undefined) {\n                        idx = this._maskLength;\n                    }\n                    caret(element, idx);\n                }\n                return idx;\n            },\n            _unmask: function (value, idx) {\n                if (!value) {\n                    return '';\n                }\n                if (this._unmaskedValue === value) {\n                    return this._unmaskedValue;\n                }\n                value = (value + '').split('');\n                var chr;\n                var token;\n                var chrIdx = 0;\n                var tokenIdx = idx || 0;\n                var empty = this.options.promptChar;\n                var valueLength = value.length;\n                var tokensLength = this.tokens.length;\n                var result = '';\n                while (tokenIdx < tokensLength) {\n                    chr = value[chrIdx];\n                    token = this.tokens[tokenIdx];\n                    if (chr === token || chr === empty) {\n                        result += chr === empty ? empty : '';\n                        chrIdx += 1;\n                        tokenIdx += 1;\n                    } else if (typeof token !== 'string') {\n                        if (token && token.test && token.test(chr) || $.isFunction(token) && token(chr)) {\n                            result += chr;\n                            tokenIdx += 1;\n                        } else {\n                            if (valueLength === 1) {\n                                this._blinkInvalidState();\n                            }\n                        }\n                        chrIdx += 1;\n                    } else {\n                        tokenIdx += 1;\n                    }\n                    if (chrIdx >= valueLength) {\n                        break;\n                    }\n                }\n                this._unmaskedValue = result;\n                return result;\n            },\n            _wrapper: function () {\n                var that = this;\n                var element = that.element;\n                var DOMElement = element[0];\n                var wrapper = element.wrap('<span class=\\'k-widget k-maskedtextbox\\'></span>').parent();\n                wrapper[0].style.cssText = DOMElement.style.cssText;\n                DOMElement.style.width = '100%';\n                that.wrapper = wrapper.addClass(DOMElement.className);\n            },\n            _blinkInvalidState: function () {\n                var that = this;\n                that.wrapper.addClass(STATEINVALID);\n                clearTimeout(that._invalidStateTimeout);\n                that._invalidStateTimeout = setTimeout(proxy(that._removeInvalidState, that), 100);\n            },\n            _removeInvalidState: function () {\n                var that = this;\n                that.wrapper.removeClass(STATEINVALID);\n                that._invalidStateTimeout = null;\n            },\n            _tokenize: function () {\n                var tokens = [];\n                var tokenIdx = 0;\n                var mask = this.options.mask || '';\n                var maskChars = mask.split('');\n                var length = maskChars.length;\n                var idx = 0;\n                var chr;\n                var rule;\n                var emptyMask = '';\n                var promptChar = this.options.promptChar;\n                var numberFormat = kendo.getCulture(this.options.culture).numberFormat;\n                var rules = this._rules;\n                for (; idx < length; idx++) {\n                    chr = maskChars[idx];\n                    rule = rules[chr];\n                    if (rule) {\n                        tokens[tokenIdx] = rule;\n                        emptyMask += promptChar;\n                        tokenIdx += 1;\n                    } else {\n                        if (chr === '.' || chr === ',') {\n                            chr = numberFormat[chr];\n                        } else if (chr === '$') {\n                            chr = numberFormat.currency.symbol;\n                        } else if (chr === '\\\\') {\n                            idx += 1;\n                            chr = maskChars[idx];\n                        }\n                        chr = chr.split('');\n                        for (var i = 0, l = chr.length; i < l; i++) {\n                            tokens[tokenIdx] = chr[i];\n                            emptyMask += chr[i];\n                            tokenIdx += 1;\n                        }\n                    }\n                }\n                this.tokens = tokens;\n                this._emptyMask = emptyMask;\n                this._maskLength = emptyMask.length;\n            }\n        });\n        function escapeRegExp(text) {\n            return text.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n        }\n        ui.plugin(MaskedTextBox);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}