{"version": 3, "sources": ["kendo.mobile.drawer.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "mobile", "os", "support", "mobileOS", "Transition", "effects", "roleSelector", "AXIS", "ui", "SWIPE_TO_OPEN", "ios", "majorVersion", "appMode", "BEFORE_SHOW", "INIT", "SHOW", "HIDE", "AFTER_HIDE", "NULL_VIEW", "enable", "noop", "Drawer", "View", "extend", "init", "element", "options", "pane", "userEvents", "container", "drawer", "hide", "parent", "prepend", "Widget", "fn", "call", "this", "$angular", "_layout", "_scroller", "_model", "closest", "data", "bind", "e", "_viewShow", "UserEvents", "fastTap", "filter", "allowSelection", "current<PERSON>iew", "Error", "_attachTransition", "visible", "preventDefault", "swipeToOpen", "transition", "cancel", "_start", "_update", "_end", "leftPositioned", "position", "addClass", "trigger", "name", "views", "swipeToOpenViews", "title", "events", "show", "_activate", "_show", "current", "_moveViewTo", "view", "openFor", "destroy", "visibleOnCurrentView", "_currentViewIncludedIn", "_setAsCurrent", "_invokeNgController", "length", "inArray", "id", "replace", "attr", "offset", "width", "last", "moveTo", "location", "duration", "ease", "easeOutExpo", "that", "movable", "currentOffset", "x", "moveAxis", "Movable", "axis", "onEnd", "style", "cssText", "animationFrame", "removeClass", "canMoveLeft", "canMoveRight", "leftSwipe", "sender", "Math", "abs", "velocity", "y", "triggeredByInput", "event", "capture", "limitedPosition", "newPosition", "delta", "min", "max", "stopPropagation", "shouldShow", "pastHalf", "velocityThreshold", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,uBACH,oBACA,oBACDD,IACL,WAuPE,MA5OC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAASF,EAAME,OAAQC,EAAKH,EAAMI,QAAQC,SAAUC,EAAaN,EAAMO,QAAQD,WAAYE,EAAeR,EAAMQ,aAAcC,EAAO,IAAKC,EAAKR,EAAOQ,GAAIC,IAAkBR,EAAGS,KAA0B,GAAnBT,EAAGU,eAAsBV,EAAGW,SAAUC,EAAc,aAAcC,EAAO,OAAQC,EAAO,OAAQC,EAAO,OAAQC,EAAa,YAAaC,GAAcC,OAAQvB,EAAEwB,MAC7WC,EAASb,EAAGc,KAAKC,QACjBC,KAAM,SAAUC,EAASC,GAAnB,GAQEC,GAA2EC,EAgBvEC,EAUJC,EACAC,CA1BJ,IARAnC,EAAE6B,GAASO,SAASC,QAAQR,GAC5BzB,EAAOQ,GAAG0B,OAAOC,GAAGX,KAAKY,KAAKC,KAAMZ,EAASC,GACxCW,KAAKX,QAAQY,WACdD,KAAKE,UACLF,KAAKG,aAETH,KAAKI,SACDd,EAAOU,KAAKZ,QAAQiB,QAAQpC,EAAa,SAASqC,KAAK,mBAEvDN,KAAKV,KAAOA,EACZU,KAAKV,KAAKiB,KAAK,WAAY,SAAUC,GACjCf,EAAOgB,UAAUD,KAErBR,KAAKV,KAAKiB,KAAK,oBAAqB,WAChCd,EAAOC,SAEXH,EAAaS,KAAKT,WAAa,GAAI9B,GAAMiD,WAAWpB,EAAKF,SACrDuB,SAAS,EACTC,OAAQ3C,EAAa,kBACrB4C,gBAAgB,QAEjB,CAGH,GAFAb,KAAKc,YAAcjC,EACfW,EAAYjC,EAAEyC,KAAKX,QAAQG,YAC1BA,EACD,KAAUuB,OAAM,yDAEpBxB,GAAaS,KAAKT,WAAa,GAAI9B,GAAMiD,WAAWlB,GAChDmB,SAAS,EACTE,gBAAgB,IAEpBb,KAAKgB,kBAAkBxB,GAEvBC,EAASO,KACTN,EAAO,SAAUc,GACbf,EAAOwB,UACPxB,EAAOC,OACPc,EAAEU,mBAGNlB,KAAKX,QAAQ8B,aAAe/C,GAC5BmB,EAAWgB,KAAK,QAAS,WACrBd,EAAO2B,WAAWC,WAEtB9B,EAAWgB,KAAK,QAAS,SAAUC,GAC/Bf,EAAO6B,OAAOd,KAElBjB,EAAWgB,KAAK,OAAQ,SAAUC,GAC9Bf,EAAO8B,QAAQf,KAEnBjB,EAAWgB,KAAK,MAAO,SAAUC,GAC7Bf,EAAO+B,KAAKhB,KAEhBjB,EAAWgB,KAAK,MAAOb,IAEvBH,EAAWgB,KAAK,QAASb,GAE7BM,KAAKyB,eAA2C,SAA1BzB,KAAKX,QAAQqC,SACnC1B,KAAKiB,SAAU,EACfjB,KAAKZ,QAAQM,OAAOiC,SAAS,aAAaA,SAAS3B,KAAKyB,eAAiB,iBAAmB,mBAC5FzB,KAAK4B,QAAQnD,IAEjBY,SACIwC,KAAM,SACNH,SAAU,OACVI,SACAC,oBACAZ,aAAa,EACba,MAAO,GACPxC,UAAW,MAEfyC,QACIzD,EACAG,EACAC,EACAH,EACAC,GAEJwD,KAAM,WACElC,KAAKmC,aACLnC,KAAKoC,SAGb1C,KAAM,WACGM,KAAKc,cAGVd,KAAKc,YAAYhC,SACjBE,EAAOqD,QAAU,KACjBrC,KAAKsC,YAAY,GACjBtC,KAAK4B,QAAQjD,GAAQ4D,KAAMvC,SAE/BwC,QAAS,WACDxC,KAAKiB,QACLjB,KAAKN,OAELM,KAAKkC,QAGbO,QAAS,WACLtE,EAAGc,KAAKa,GAAG2C,QAAQ1C,KAAKC,MACxBA,KAAKT,WAAWkD,WAEpBN,UAAW,WACP,GAAInC,KAAKiB,QACL,OAAO,CAEX,IAAIyB,GAAuB1C,KAAK2C,uBAAuB3C,KAAKX,QAAQyC,MACpE,UAAKY,GAAwB1C,KAAK4B,QAAQpD,GAAe+D,KAAMvC,UAG/DA,KAAK4C,gBACL5C,KAAKZ,QAAQ8C,OACblC,KAAK4B,QAAQlD,GAAQ6D,KAAMvC,OAC3BA,KAAK6C,uBACE,IAEXF,uBAAwB,SAAUb,GAC9B,IAAK9B,KAAKV,OAASwC,EAAMgB,OACrB,OAAO,CAEX,IAAIP,GAAOvC,KAAKV,KAAKiD,MACrB,OAAOhF,GAAEwF,QAAQR,EAAKS,GAAGC,QAAQ,IAAK,IAAKnB,OAAevE,EAAEwF,QAAQR,EAAKnD,QAAQ8D,KAAK,MAAOpB,OAEjGM,MAAO,WACHpC,KAAKc,YAAYhC,QAAO,GACxBkB,KAAKiB,SAAU,CACf,IAAIkC,GAASnD,KAAKZ,QAAQgE,OACrBpD,MAAKyB,iBACN0B,GAAUA,GAEdnD,KAAKsC,YAAYa,IAErBP,cAAe,WACP5D,EAAOqE,OAASrD,OACZhB,EAAOqE,MACPrE,EAAOqE,KAAKjE,QAAQM,OAExBM,KAAKZ,QAAQ8C,QAEjBlD,EAAOqE,KAAOrD,KACdhB,EAAOqD,QAAUrC,MAErBsC,YAAa,SAAUa,GACnBnD,KAAKT,WAAW8B,SAChBrB,KAAKoB,WAAWkC,QACZC,SAAUJ,EACVK,SAAU,IACVC,KAAM1F,EAAW2F,eAGzBjD,UAAW,SAAUD,GAIjB,MAHIR,MAAKc,aACLd,KAAKc,YAAYhC,SAEjBkB,KAAKc,cAAgBN,EAAE+B,MACvBvC,KAAKN,OACL,IAEJM,KAAKc,YAAcN,EAAE+B,KACrBvC,KAAKgB,kBAAkBR,EAAE+B,KAAKnD,SAD9BY,IAGJgB,kBAAmB,SAAU5B,GACzB,GAAIuE,GAAO3D,KAAM4D,EAAU5D,KAAK4D,QAASC,EAAgBD,GAAWA,EAAQE,CACxE9D,MAAKoB,aACLpB,KAAKoB,WAAWC,SAChBrB,KAAK4D,QAAQG,SAAS,IAAK,IAE/BH,EAAU5D,KAAK4D,QAAU,GAAInG,GAAMU,GAAG6F,QAAQ5E,GAC9CY,KAAKoB,WAAa,GAAIrD,IAClBkG,KAAM/F,EACN0F,QAAS5D,KAAK4D,QACdM,MAAO,WACmB,IAAlBN,EAAQ1F,KACRkB,EAAQ,GAAG+E,MAAMC,QAAU,GAC3BT,EAAKvE,QAAQM,OACbiE,EAAK/B,QAAQhD,GACb+E,EAAK1C,SAAU,MAIvB4C,IACAzE,EAAQuC,SAAS,eACjBlE,EAAM4G,eAAe,WACjBjF,EAAQkF,YAAY,eACpBX,EAAKC,QAAQG,SAAS7F,EAAM2F,GAC5BF,EAAKjE,WAIjB4B,OAAQ,SAAUd,GAAV,GAMAiB,GAAsCR,EAAwBsD,EAA+EC,EAAgFC,EAL7NlF,EAAaiB,EAAEkE,MACnB,OAAIC,MAAKC,IAAIpE,EAAEsD,EAAEe,UAAYF,KAAKC,IAAIpE,EAAEsE,EAAED,WAAapH,EAAMsH,iBAAiBvE,EAAEwE,SAAWhF,KAAK2C,uBAAuB3C,KAAKX,QAAQ0C,mBAChIxC,EAAW8B,SACX,IAEAI,EAAiBzB,KAAKyB,eAAgBR,EAAUjB,KAAKiB,QAASsD,EAAc9C,GAAkBR,IAAYQ,IAAmBzC,EAAOqD,QAASmC,GAAgB/C,GAAkBR,GAAWQ,IAAmBzC,EAAOqD,QAASoC,EAAYjE,EAAEsD,EAAEe,SAAW,GACxPN,GAAeE,GAAaD,IAAiBC,IACzCzE,KAAKmC,aACL5C,EAAW0F,UACX,IAGR1F,EAAW8B,SAAX9B,KAEJgC,QAAS,SAAUf,GACf,GAAiE0E,GAA7DtB,EAAU5D,KAAK4D,QAASuB,EAAcvB,EAAQE,EAAItD,EAAEsD,EAAEsB,KAEtDF,GADAlF,KAAKyB,eACakD,KAAKU,IAAIV,KAAKW,IAAI,EAAGH,GAAcnF,KAAKZ,QAAQgE,SAEhDuB,KAAKW,IAAIX,KAAKU,IAAI,EAAGF,IAAenF,KAAKZ,QAAQgE,SAEvEpD,KAAK4D,QAAQG,SAAS7F,EAAMgH,GAC5B1E,EAAEwE,MAAM9D,iBACRV,EAAEwE,MAAMO,mBAEZ/D,KAAM,SAAUhB,GACZ,GAAsHgF,GAAlHX,EAAWrE,EAAEsD,EAAEe,SAAUY,EAAWd,KAAKC,IAAI5E,KAAK4D,QAAQE,GAAK9D,KAAKZ,QAAQgE,QAAU,EAAGsC,EAAoB,EAE7GF,GADAxF,KAAKyB,eACQoD,GAAYa,IAAsBb,EAAWa,GAAqBD,GAElEZ,EAAWa,IAAsBb,GAAYa,GAAqBD,GAE/ED,EACAxF,KAAKoC,QAELpC,KAAKN,SAIjBvB,GAAGwH,OAAO3G,IACZtB,OAAOD,MAAMmI,QACRlI,OAAOD,OACE,kBAAVH,SAAwBA,OAAOuI,IAAMvI,OAAS,SAAUwI,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.drawer.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.drawer', [\n        'kendo.mobile.view',\n        'kendo.userevents'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.drawer',\n        name: 'Drawer',\n        category: 'mobile',\n        description: 'The Kendo Mobile Drawer widget provides slide to reveal global application toolbox',\n        depends: [\n            'mobile.view',\n            'userevents'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, mobile = kendo.mobile, os = kendo.support.mobileOS, Transition = kendo.effects.Transition, roleSelector = kendo.roleSelector, AXIS = 'x', ui = mobile.ui, SWIPE_TO_OPEN = !(os.ios && os.majorVersion == 7 && !os.appMode), BEFORE_SHOW = 'beforeShow', INIT = 'init', SHOW = 'show', HIDE = 'hide', AFTER_HIDE = 'afterHide', NULL_VIEW = { enable: $.noop };\n        var Drawer = ui.View.extend({\n            init: function (element, options) {\n                $(element).parent().prepend(element);\n                mobile.ui.Widget.fn.init.call(this, element, options);\n                if (!this.options.$angular) {\n                    this._layout();\n                    this._scroller();\n                }\n                this._model();\n                var pane = this.element.closest(roleSelector('pane')).data('kendoMobilePane'), userEvents;\n                if (pane) {\n                    this.pane = pane;\n                    this.pane.bind('viewShow', function (e) {\n                        drawer._viewShow(e);\n                    });\n                    this.pane.bind('sameViewRequested', function () {\n                        drawer.hide();\n                    });\n                    userEvents = this.userEvents = new kendo.UserEvents(pane.element, {\n                        fastTap: true,\n                        filter: roleSelector('view splitview'),\n                        allowSelection: true\n                    });\n                } else {\n                    this.currentView = NULL_VIEW;\n                    var container = $(this.options.container);\n                    if (!container) {\n                        throw new Error('The drawer needs a container configuration option set.');\n                    }\n                    userEvents = this.userEvents = new kendo.UserEvents(container, {\n                        fastTap: true,\n                        allowSelection: true\n                    });\n                    this._attachTransition(container);\n                }\n                var drawer = this;\n                var hide = function (e) {\n                    if (drawer.visible) {\n                        drawer.hide();\n                        e.preventDefault();\n                    }\n                };\n                if (this.options.swipeToOpen && SWIPE_TO_OPEN) {\n                    userEvents.bind('press', function () {\n                        drawer.transition.cancel();\n                    });\n                    userEvents.bind('start', function (e) {\n                        drawer._start(e);\n                    });\n                    userEvents.bind('move', function (e) {\n                        drawer._update(e);\n                    });\n                    userEvents.bind('end', function (e) {\n                        drawer._end(e);\n                    });\n                    userEvents.bind('tap', hide);\n                } else {\n                    userEvents.bind('press', hide);\n                }\n                this.leftPositioned = this.options.position === 'left';\n                this.visible = false;\n                this.element.hide().addClass('km-drawer').addClass(this.leftPositioned ? 'km-left-drawer' : 'km-right-drawer');\n                this.trigger(INIT);\n            },\n            options: {\n                name: 'Drawer',\n                position: 'left',\n                views: [],\n                swipeToOpenViews: [],\n                swipeToOpen: true,\n                title: '',\n                container: null\n            },\n            events: [\n                BEFORE_SHOW,\n                HIDE,\n                AFTER_HIDE,\n                INIT,\n                SHOW\n            ],\n            show: function () {\n                if (this._activate()) {\n                    this._show();\n                }\n            },\n            hide: function () {\n                if (!this.currentView) {\n                    return;\n                }\n                this.currentView.enable();\n                Drawer.current = null;\n                this._moveViewTo(0);\n                this.trigger(HIDE, { view: this });\n            },\n            openFor: function () {\n                if (this.visible) {\n                    this.hide();\n                } else {\n                    this.show();\n                }\n            },\n            destroy: function () {\n                ui.View.fn.destroy.call(this);\n                this.userEvents.destroy();\n            },\n            _activate: function () {\n                if (this.visible) {\n                    return true;\n                }\n                var visibleOnCurrentView = this._currentViewIncludedIn(this.options.views);\n                if (!visibleOnCurrentView || this.trigger(BEFORE_SHOW, { view: this })) {\n                    return false;\n                }\n                this._setAsCurrent();\n                this.element.show();\n                this.trigger(SHOW, { view: this });\n                this._invokeNgController();\n                return true;\n            },\n            _currentViewIncludedIn: function (views) {\n                if (!this.pane || !views.length) {\n                    return true;\n                }\n                var view = this.pane.view();\n                return $.inArray(view.id.replace('#', ''), views) > -1 || $.inArray(view.element.attr('id'), views) > -1;\n            },\n            _show: function () {\n                this.currentView.enable(false);\n                this.visible = true;\n                var offset = this.element.width();\n                if (!this.leftPositioned) {\n                    offset = -offset;\n                }\n                this._moveViewTo(offset);\n            },\n            _setAsCurrent: function () {\n                if (Drawer.last !== this) {\n                    if (Drawer.last) {\n                        Drawer.last.element.hide();\n                    }\n                    this.element.show();\n                }\n                Drawer.last = this;\n                Drawer.current = this;\n            },\n            _moveViewTo: function (offset) {\n                this.userEvents.cancel();\n                this.transition.moveTo({\n                    location: offset,\n                    duration: 400,\n                    ease: Transition.easeOutExpo\n                });\n            },\n            _viewShow: function (e) {\n                if (this.currentView) {\n                    this.currentView.enable();\n                }\n                if (this.currentView === e.view) {\n                    this.hide();\n                    return;\n                }\n                this.currentView = e.view;\n                this._attachTransition(e.view.element);\n            },\n            _attachTransition: function (element) {\n                var that = this, movable = this.movable, currentOffset = movable && movable.x;\n                if (this.transition) {\n                    this.transition.cancel();\n                    this.movable.moveAxis('x', 0);\n                }\n                movable = this.movable = new kendo.ui.Movable(element);\n                this.transition = new Transition({\n                    axis: AXIS,\n                    movable: this.movable,\n                    onEnd: function () {\n                        if (movable[AXIS] === 0) {\n                            element[0].style.cssText = '';\n                            that.element.hide();\n                            that.trigger(AFTER_HIDE);\n                            that.visible = false;\n                        }\n                    }\n                });\n                if (currentOffset) {\n                    element.addClass('k-fx-hidden');\n                    kendo.animationFrame(function () {\n                        element.removeClass('k-fx-hidden');\n                        that.movable.moveAxis(AXIS, currentOffset);\n                        that.hide();\n                    });\n                }\n            },\n            _start: function (e) {\n                var userEvents = e.sender;\n                if (Math.abs(e.x.velocity) < Math.abs(e.y.velocity) || kendo.triggeredByInput(e.event) || !this._currentViewIncludedIn(this.options.swipeToOpenViews)) {\n                    userEvents.cancel();\n                    return;\n                }\n                var leftPositioned = this.leftPositioned, visible = this.visible, canMoveLeft = leftPositioned && visible || !leftPositioned && !Drawer.current, canMoveRight = !leftPositioned && visible || leftPositioned && !Drawer.current, leftSwipe = e.x.velocity < 0;\n                if (canMoveLeft && leftSwipe || canMoveRight && !leftSwipe) {\n                    if (this._activate()) {\n                        userEvents.capture();\n                        return;\n                    }\n                }\n                userEvents.cancel();\n            },\n            _update: function (e) {\n                var movable = this.movable, newPosition = movable.x + e.x.delta, limitedPosition;\n                if (this.leftPositioned) {\n                    limitedPosition = Math.min(Math.max(0, newPosition), this.element.width());\n                } else {\n                    limitedPosition = Math.max(Math.min(0, newPosition), -this.element.width());\n                }\n                this.movable.moveAxis(AXIS, limitedPosition);\n                e.event.preventDefault();\n                e.event.stopPropagation();\n            },\n            _end: function (e) {\n                var velocity = e.x.velocity, pastHalf = Math.abs(this.movable.x) > this.element.width() / 2, velocityThreshold = 0.8, shouldShow;\n                if (this.leftPositioned) {\n                    shouldShow = velocity > -velocityThreshold && (velocity > velocityThreshold || pastHalf);\n                } else {\n                    shouldShow = velocity < velocityThreshold && (velocity < -velocityThreshold || pastHalf);\n                }\n                if (shouldShow) {\n                    this._show();\n                } else {\n                    this.hide();\n                }\n            }\n        });\n        ui.plugin(Drawer);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}