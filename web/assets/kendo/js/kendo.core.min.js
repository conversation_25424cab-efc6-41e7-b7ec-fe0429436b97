/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.core.min",["jquery"],e)}(function(){return function(e,t,n){function r(){}function o(e,t){if(t)return"'"+e.split("'").join("\\'").split('\\"').join('\\\\\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t")+"'";var n=e.charAt(0),r=e.substring(1);return"="===n?"+("+r+")+":":"===n?"+$kendoHtmlEncode("+r+")+":";"+e+";$kendoOutput+="}function i(e,t,n){return e+="",t=t||2,n=t-e.length,n?j[t].substring(0,n)+e:e}function a(e){var t=e.css(ve.support.transitions.css+"box-shadow")||e.css("box-shadow"),n=t?t.match(Ce)||[0,0,0,0,0]:[0,0,0,0,0],r=Te.max(+n[3],+(n[4]||0));return{left:-n[1]+r,right:+n[1]+r,bottom:+n[2]+r}}function s(n,r){var o,i,a,s,l,c,d=Oe.browser,f=ve._outerWidth,p=ve._outerHeight,m=n.parent(),h=f(t);return m.removeClass("k-animation-container-sm"),m.hasClass("k-animation-container")?u(n,r):(i=n[0].style.width,a=n[0].style.height,s=ze.test(i),l=ze.test(a),c=n.hasClass("k-tooltip")||n.is(".k-menu-horizontal.k-context-menu"),o=s||l,!s&&(!r||r&&i||c)&&(i=r?f(n)+1:f(n)),(!l&&(!r||r&&a)||n.is(".k-menu-horizontal.k-context-menu"))&&(a=p(n)),n.wrap(e("<div/>").addClass("k-animation-container").css({width:i,height:a})),m=n.parent(),o&&n.css({width:"100%",height:"100%",boxSizing:"border-box",mozBoxSizing:"border-box",webkitBoxSizing:"border-box"})),h<f(m)&&(m.addClass("k-animation-container-sm"),u(n,r)),d.msie&&Te.floor(d.version)<=7&&(n.css({zoom:1}),n.children(".k-menu").width(n.width())),m}function u(e,t){var n,r=ve._outerWidth,o=ve._outerHeight,i=e.parent(".k-animation-container"),a=i[0].style;i.is(":hidden")&&i.css({display:"",position:""}),n=ze.test(a.width)||ze.test(a.height),n||i.css({width:t?r(e)+1:r(e),height:o(e),boxSizing:"content-box",mozBoxSizing:"content-box",webkitBoxSizing:"content-box"})}function l(e){var t=1,n=arguments.length;for(t=1;t<n;t++)c(e,arguments[t]);return e}function c(e,t){var n,r,o,i,a,s=ve.data.ObservableArray,u=ve.data.LazyObservableArray,l=ve.data.DataSource,d=ve.data.HierarchicalDataSource;for(n in t)r=t[n],o=typeof r,i=o===Ne&&null!==r?r.constructor:null,i&&i!==Array&&i!==s&&i!==u&&i!==l&&i!==d&&i!==RegExp?r instanceof Date?e[n]=new Date(r.getTime()):H(r.clone)?e[n]=r.clone():(a=e[n],e[n]=typeof a===Ne?a||{}:{},c(e[n],r)):o!==Re&&(e[n]=r);return e}function d(e,t,r){for(var o in t)if(t.hasOwnProperty(o)&&t[o].test(e))return o;return r!==n?r:e}function f(e){return e.replace(/([a-z][A-Z])/g,function(e){return e.charAt(0)+"-"+e.charAt(1).toLowerCase()})}function p(e){return e.replace(/\-(\w)/g,function(e,t){return t.toUpperCase()})}function m(t,n){var r,o={};return document.defaultView&&document.defaultView.getComputedStyle?(r=document.defaultView.getComputedStyle(t,""),n&&e.each(n,function(e,t){o[t]=r.getPropertyValue(t)})):(r=t.currentStyle,n&&e.each(n,function(e,t){o[t]=r[p(t)]})),ve.size(o)||(o=r),o}function h(e){if(e&&e.className&&"string"==typeof e.className&&e.className.indexOf("k-auto-scrollable")>-1)return!0;var t=m(e,["overflow"]).overflow;return"auto"==t||"scroll"==t}function g(t,r){var o,i=Oe.browser.webkit,a=Oe.browser.mozilla,s=t instanceof e?t[0]:t;if(t)return o=Oe.isRtl(t),r===n?o&&i?s.scrollWidth-s.clientWidth-s.scrollLeft:Math.abs(s.scrollLeft):(s.scrollLeft=o&&i?s.scrollWidth-s.clientWidth-r:o&&a?-r:r,n)}function y(e){var t,n=0;for(t in e)e.hasOwnProperty(t)&&"toJSON"!=t&&n++;return n}function v(e,n,r){var o,i,a;return n||(n="offset"),o=e[n](),i={top:o.top,right:o.right,bottom:o.bottom,left:o.left},Oe.browser.msie&&(Oe.pointers||Oe.msPointers)&&!r&&(a=Oe.isRtl(e)?1:-1,i.top-=t.pageYOffset-document.documentElement.scrollTop,i.left-=t.pageXOffset+a*document.documentElement.scrollLeft),i}function b(e){var t={};return we("string"==typeof e?e.split(" "):e,function(e){t[e]=this}),t}function w(e){return new ve.effects.Element(e)}function M(e,t,n,r){return typeof e===Ae&&(H(t)&&(r=t,t=400,n=!1),H(n)&&(r=n,n=!1),typeof t===Fe&&(n=t,t=400),e={effects:e,duration:t,reverse:n,complete:r}),be({effects:{},duration:400,reverse:!1,init:xe,teardown:xe,hide:!1},e,{completeCallback:e.complete,complete:xe})}function S(t,n,r,o,i){for(var a,s=0,u=t.length;s<u;s++)a=e(t[s]),a.queue(function(){Y.promise(a,M(n,r,o,i))});return t}function x(e,t,n,r){return t&&(t=t.split(" "),we(t,function(t,n){e.toggleClass(n,r)})),e}function T(e){return(""+e).replace(q,"&amp;").replace(J,"&lt;").replace(G,"&gt;").replace(V,"&quot;").replace(Q,"&#39;")}function k(e,t){var r;return 0===t.indexOf("data")&&(t=t.substring(4),t=t.charAt(0).toLowerCase()+t.substring(1)),t=t.replace(ie,"-$1"),r=e.getAttribute("data-"+ve.ns+t),null===r?r=n:"null"===r?r=null:"true"===r?r=!0:"false"===r?r=!1:Ee.test(r)&&"mask"!=t?r=parseFloat(r):re.test(r)&&!oe.test(r)&&(r=Function("return ("+r+")")()),r}function O(t,r,o){var i,a,s={},u=t.getAttribute("data-"+ve.ns+"role");for(i in r)a=k(t,i),a!==n&&(ne.test(i)&&"drawer"!=u&&("string"==typeof a?e("#"+a).length?a=ve.template(e("#"+a).html()):o&&(a=ve.template(o[a])):a=t.getAttribute(i)),s[i]=a);return s}function z(t,n){return e.contains(t,n)?-1:1}function D(){var t=e(this);return e.inArray(t.attr("data-"+ve.ns+"role"),["slider","rangeslider"])>-1||t.is(":visible")}function C(e,t){var n=e.nodeName.toLowerCase();return(/input|select|textarea|button|object/.test(n)?!e.disabled:"a"===n?e.href||t:t)&&E(e)}function E(t){return e.expr.pseudos.visible(t)&&!e(t).parents().addBack().filter(function(){return"hidden"===e.css(this,"visibility")}).length}function _(e,t){return new _.fn.init(e,t)}var A,H,N,P,F,R,U,W,I,$,L,j,B,Y,q,J,V,Q,G,K,Z,X,ee,te,ne,re,oe,ie,ae,se,ue,le,ce,de,fe,pe,me,he,ge,ye,ve=t.kendo=t.kendo||{cultures:{}},be=e.extend,we=e.each,Me=e.isArray,Se=e.proxy,xe=e.noop,Te=Math,ke=t.JSON||{},Oe={},ze=/%/,De=/\{(\d+)(:[^\}]+)?\}/g,Ce=/(\d+(?:\.?)\d*)px\s*(\d+(?:\.?)\d*)px\s*(\d+(?:\.?)\d*)px\s*(\d+)?/i,Ee=/^(\+|-?)\d+(\.?)\d*$/,_e="function",Ae="string",He="number",Ne="object",Pe="null",Fe="boolean",Re="undefined",Ue={},We={},Ie=[].slice,$e=function(){var e,t,r,o,i,a,s=arguments[0]||{},u=1,l=arguments.length,c=!1;for("boolean"==typeof s&&(c=s,s=arguments[u]||{},u++),"object"==typeof s||jQuery.isFunction(s)||(s={}),u===l&&(s=this,u--);u<l;u++)if(null!=(i=arguments[u]))for(o in i)"filters"!=o&&"concat"!=o&&":"!=o&&(e=s[o],r=i[o],s!==r&&(c&&r&&(jQuery.isPlainObject(r)||(t=jQuery.isArray(r)))?(t?(t=!1,a=e&&jQuery.isArray(e)?e:[]):a=e&&jQuery.isPlainObject(e)?e:{},s[o]=$e(c,a,r)):r!==n&&(s[o]=r)));return s};ve.version="2019.2.619".replace(/^\s+|\s+$/g,""),r.extend=function(e){var t,n,r=function(){},o=this,i=e&&e.init?e.init:function(){o.apply(this,arguments)};r.prototype=o.prototype,n=i.fn=i.prototype=new r;for(t in e)n[t]=null!=e[t]&&e[t].constructor===Object?be(!0,{},r.prototype[t],e[t]):e[t];return n.constructor=i,i.extend=o.extend,i},r.prototype._initOptions=function(e){this.options=l({},this.options,e)},H=ve.isFunction=function(e){return"function"==typeof e},N=function(){this._defaultPrevented=!0},P=function(){return this._defaultPrevented===!0},F=r.extend({init:function(){this._events={}},bind:function(e,t,r){var o,i,a,s,u,l=this,c=typeof e===Ae?[e]:e,d=typeof t===_e;if(t===n){for(o in e)l.bind(o,e[o]);return l}for(o=0,i=c.length;o<i;o++)e=c[o],s=d?t:t[e],s&&(r&&(a=s,s=function(){l.unbind(e,s),a.apply(l,arguments)},s.original=a),u=l._events[e]=l._events[e]||[],u.push(s));return l},one:function(e,t){return this.bind(e,t,!0)},first:function(e,t){var n,r,o,i,a=this,s=typeof e===Ae?[e]:e,u=typeof t===_e;for(n=0,r=s.length;n<r;n++)e=s[n],o=u?t:t[e],o&&(i=a._events[e]=a._events[e]||[],i.unshift(o));return a},trigger:function(e,t){var n,r,o=this,i=o._events[e];if(i){for(t=t||{},t.sender=o,t._defaultPrevented=!1,t.preventDefault=N,t.isDefaultPrevented=P,i=i.slice(),n=0,r=i.length;n<r;n++)i[n].call(o,t);return t._defaultPrevented===!0}return!1},unbind:function(e,t){var r,o=this,i=o._events[e];if(e===n)o._events={};else if(i)if(t)for(r=i.length-1;r>=0;r--)i[r]!==t&&i[r].original!==t||i.splice(r,1);else o._events[e]=[];return o}}),R=/^\w+/,U=/\$\{([^}]*)\}/g,W=/\\\}/g,I=/__CURLY__/g,$=/\\#/g,L=/__SHARP__/g,j=["","0","00","000","0000"],A={paramName:"data",useWithBlock:!0,render:function(e,t){var n,r,o="";for(n=0,r=t.length;n<r;n++)o+=e(t[n]);return o},compile:function(e,t){var n,r,i,a=be({},this,t),s=a.paramName,u=s.match(R)[0],l=a.useWithBlock,c="var $kendoOutput, $kendoHtmlEncode = kendo.htmlEncode;";if(H(e))return e;for(c+=l?"with("+s+"){":"",c+="$kendoOutput=",r=e.replace(W,"__CURLY__").replace(U,"#=$kendoHtmlEncode($1)#").replace(I,"}").replace($,"__SHARP__").split("#"),i=0;i<r.length;i++)c+=o(r[i],i%2===0);c+=l?";}":";",c+="return $kendoOutput;",c=c.replace(L,"#");try{return n=Function(u,c),n._slotCount=Math.floor(r.length/2),n}catch(d){throw Error(ve.format("Invalid template:'{0}' Generated code:'{1}'",e,c))}}},function(){function e(e){return a.lastIndex=0,a.test(e)?'"'+e.replace(a,function(e){var t=s[e];return typeof t===Ae?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function t(i,a){var s,l,c,d,f,p,m=n,h=a[i];if(h&&typeof h===Ne&&typeof h.toJSON===_e&&(h=h.toJSON(i)),typeof o===_e&&(h=o.call(a,i,h)),p=typeof h,p===Ae)return e(h);if(p===He)return isFinite(h)?h+"":Pe;if(p===Fe||p===Pe)return h+"";if(p===Ne){if(!h)return Pe;if(n+=r,f=[],"[object Array]"===u.apply(h)){for(d=h.length,s=0;s<d;s++)f[s]=t(s,h)||Pe;return c=0===f.length?"[]":n?"[\n"+n+f.join(",\n"+n)+"\n"+m+"]":"["+f.join(",")+"]",n=m,c}if(o&&typeof o===Ne)for(d=o.length,s=0;s<d;s++)typeof o[s]===Ae&&(l=o[s],c=t(l,h),c&&f.push(e(l)+(n?": ":":")+c));else for(l in h)Object.hasOwnProperty.call(h,l)&&(c=t(l,h),c&&f.push(e(l)+(n?": ":":")+c));return c=0===f.length?"{}":n?"{\n"+n+f.join(",\n"+n)+"\n"+m+"}":"{"+f.join(",")+"}",n=m,c}}var n,r,o,a=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,s={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},u={}.toString;typeof Date.prototype.toJSON!==_e&&(Date.prototype.toJSON=function(){var e=this;return isFinite(e.valueOf())?i(e.getUTCFullYear(),4)+"-"+i(e.getUTCMonth()+1)+"-"+i(e.getUTCDate())+"T"+i(e.getUTCHours())+":"+i(e.getUTCMinutes())+":"+i(e.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(){return this.valueOf()}),typeof ke.stringify!==_e&&(ke.stringify=function(e,i,a){var s;if(n="",r="",typeof a===He)for(s=0;s<a;s+=1)r+=" ";else typeof a===Ae&&(r=a);if(o=i,i&&typeof i!==_e&&(typeof i!==Ne||typeof i.length!==He))throw Error("JSON.stringify");return t("",{"":e})})}(),function(){function t(e){if(e){if(e.numberFormat)return e;if(typeof e===Ae){var t=ve.cultures;return t[e]||t[e.split("-")[0]]||null}return null}return null}function r(e){return e&&(e=t(e)),e||ve.cultures.current}function o(e,t,o){o=r(o);var a=o.calendars.standard,s=a.days,u=a.months;return t=a.patterns[t]||t,t.replace(c,function(t){var r,o,l;return"d"===t?o=e.getDate():"dd"===t?o=i(e.getDate()):"ddd"===t?o=s.namesAbbr[e.getDay()]:"dddd"===t?o=s.names[e.getDay()]:"M"===t?o=e.getMonth()+1:"MM"===t?o=i(e.getMonth()+1):"MMM"===t?o=u.namesAbbr[e.getMonth()]:"MMMM"===t?o=u.names[e.getMonth()]:"yy"===t?o=i(e.getFullYear()%100):"yyyy"===t?o=i(e.getFullYear(),4):"h"===t?o=e.getHours()%12||12:"hh"===t?o=i(e.getHours()%12||12):"H"===t?o=e.getHours():"HH"===t?o=i(e.getHours()):"m"===t?o=e.getMinutes():"mm"===t?o=i(e.getMinutes()):"s"===t?o=e.getSeconds():"ss"===t?o=i(e.getSeconds()):"f"===t?o=Te.floor(e.getMilliseconds()/100):"ff"===t?(o=e.getMilliseconds(),o>99&&(o=Te.floor(o/10)),o=i(o)):"fff"===t?o=i(e.getMilliseconds(),3):"tt"===t?o=e.getHours()<12?a.AM[0]:a.PM[0]:"zzz"===t?(r=e.getTimezoneOffset(),l=r<0,o=(""+Te.abs(r/60)).split(".")[0],r=Te.abs(r)-60*o,o=(l?"+":"-")+i(o),o+=":"+i(r)):"zz"!==t&&"z"!==t||(o=e.getTimezoneOffset()/60,l=o<0,o=(""+Te.abs(o)).split(".")[0],o=(l?"+":"-")+("zz"===t?i(o):o)),o!==n?o:t.slice(1,t.length-1)})}function a(e,t,o){var i,a,l,c,w,M,S,x,T,k,O,z,D,C,E,_,A,H,N,P,F,R,U,W,I,$,L,j,B,Y,q,J,V,Q;if(o=r(o),i=o.numberFormat,a=i[h],l=i.decimals,c=i.pattern[0],w=[],O=e<0,_=m,A=m,q=-1,e===n)return m;if(!isFinite(e))return e;if(!t)return o.name.length?e.toLocaleString():""+e;if(k=d.exec(t)){if(t=k[1].toLowerCase(),S="c"===t,x="p"===t,(S||x)&&(i=S?i.currency:i.percent,a=i[h],l=i.decimals,M=i.symbol,c=i.pattern[O?0:1]),T=k[2],T&&(l=+T),"e"===t)return V=T?e.toExponential(l):e.toExponential(),V.replace(h,i[h]);if(x&&(e*=100),e=u(e,l),O=e<0,e=e.split(h),z=e[0],D=e[1],O&&(z=z.substring(1)),A=s(z,0,z.length,i),D&&(A+=a+D),"n"===t&&!O)return A;for(e=m,H=0,N=c.length;H<N;H++)P=c.charAt(H),e+="n"===P?A:"$"===P||"%"===P?M:P;return e}if((t.indexOf("'")>-1||t.indexOf('"')>-1||t.indexOf("\\")>-1)&&(t=t.replace(f,function(e){var t=e.charAt(0).replace("\\",""),n=e.slice(1).replace(t,"");return w.push(n),b})),t=t.split(";"),O&&t[1])t=t[1],R=!0;else if(0===e&&t[2]){if(t=t[2],t.indexOf(y)==-1&&t.indexOf(v)==-1)return t}else t=t[0];if(j=t.indexOf("%"),B=t.indexOf("$"),x=j!=-1,S=B!=-1,x&&(e*=100),S&&"\\"===t[B-1]&&(t=t.split("\\").join(""),S=!1),(S||x)&&(i=S?i.currency:i.percent,a=i[h],l=i.decimals,M=i.symbol),F=t.indexOf(g)>-1,F&&(t=t.replace(p,m)),U=t.indexOf(h),N=t.length,U!=-1)if(D=(""+e).split("e"),D=D[1]?u(e,Math.abs(D[1])):D[0],D=D.split(h)[1]||m,I=t.lastIndexOf(v)-U,W=t.lastIndexOf(y)-U,$=I>-1,L=W>-1,H=D.length,$||L||(t=t.substring(0,U)+t.substring(U+1),N=t.length,U=-1,H=0),$&&I>W)H=I;else if(W>I)if(L&&H>W){for(Q=u(e,W,O);Q.charAt(Q.length-1)===v&&W>0&&W>I;)W--,Q=u(e,W,O);H=W}else $&&H<I&&(H=I);if(e=u(e,H,O),W=t.indexOf(y),Y=I=t.indexOf(v),q=W==-1&&I!=-1?I:W!=-1&&I==-1?W:W>I?I:W,W=t.lastIndexOf(y),I=t.lastIndexOf(v),J=W==-1&&I!=-1?I:W!=-1&&I==-1?W:W>I?W:I,q==N&&(J=q),q!=-1){for(A=(""+e).split(h),z=A[0],D=A[1]||m,C=z.length,E=D.length,O&&e*-1>=0&&(O=!1),e=t.substring(0,q),O&&!R&&(e+="-"),H=q;H<N;H++){if(P=t.charAt(H),U==-1){if(J-H<C){e+=z;break}}else if(I!=-1&&I<H&&(_=m),U-H<=C&&U-H>-1&&(e+=z,H=U),U===H){e+=(D?a:m)+D,H+=J-U+1;continue}P===v?(e+=P,_=P):P===y&&(e+=_)}if(F&&(e=s(e,q+(O&&!R?1:0),Math.max(J,C+q),i)),J>=q&&(e+=t.substring(J+1)),S||x){for(A=m,H=0,N=e.length;H<N;H++)P=e.charAt(H),A+="$"===P||"%"===P?M:P;e=A}if(N=w.length)for(H=0;H<N;H++)e=e.replace(b,w[H])}return e}var s,u,l,c=/dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|HH|H|hh|h|mm|m|fff|ff|f|tt|ss|s|zzz|zz|z|"[^"]*"|'[^']*'/g,d=/^(n|c|p|e)(\d*)$/i,f=/(\\.)|(['][^']*[']?)|(["][^"]*["]?)/g,p=/\,/g,m="",h=".",g=",",y="#",v="0",b="??",w="en-US",M={}.toString;ve.cultures["en-US"]={name:w,numberFormat:{pattern:["-n"],decimals:2,",":",",".":".",groupSize:[3],percent:{pattern:["-n %","n %"],decimals:2,",":",",".":".",groupSize:[3],symbol:"%"},currency:{name:"US Dollar",abbr:"USD",pattern:["($n)","$n"],decimals:2,",":",",".":".",groupSize:[3],symbol:"$"}},calendars:{standard:{days:{names:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],namesAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],namesShort:["Su","Mo","Tu","We","Th","Fr","Sa"]},months:{names:["January","February","March","April","May","June","July","August","September","October","November","December"],namesAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]},AM:["AM","am","AM"],PM:["PM","pm","PM"],patterns:{d:"M/d/yyyy",D:"dddd, MMMM dd, yyyy",F:"dddd, MMMM dd, yyyy h:mm:ss tt",g:"M/d/yyyy h:mm tt",G:"M/d/yyyy h:mm:ss tt",m:"MMMM dd",M:"MMMM dd",s:"yyyy'-'MM'-'ddTHH':'mm':'ss",t:"h:mm tt",T:"h:mm:ss tt",u:"yyyy'-'MM'-'dd HH':'mm':'ss'Z'",y:"MMMM, yyyy",Y:"MMMM, yyyy"},"/":"/",":":":",firstDay:0,twoDigitYearMax:2029}}},ve.culture=function(e){var r,o=ve.cultures;return e===n?o.current:(r=t(e)||o[w],r.calendar=r.calendars.standard,o.current=r,n)},ve.findCulture=t,ve.getCulture=r,ve.culture(w),s=function(e,t,r,o){var i,a,s,u,l,c,d=e.indexOf(o[h]),f=o.groupSize.slice(),p=f.shift();if(r=d!==-1?d:r+1,i=e.substring(t,r),a=i.length,a>=p){for(s=a,u=[];s>-1;)if(l=i.substring(s-p,s),l&&u.push(l),s-=p,c=f.shift(),p=c!==n?c:p,0===p){s>0&&u.push(i.substring(0,s));break}i=u.reverse().join(o[g]),e=e.substring(0,t)+i+e.substring(r)}return e},u=function(e,t,n){return t=t||0,e=(""+e).split("e"),e=Math.round(+(e[0]+"e"+(e[1]?+e[1]+t:t))),n&&(e=-e),e=(""+e).split("e"),e=+(e[0]+"e"+(e[1]?+e[1]-t:-t)),e.toFixed(Math.min(t,20))},l=function(e,t,r){if(t){if("[object Date]"===M.call(e))return o(e,t,r);if(typeof e===He)return a(e,t,r)}return e!==n?e:""},ve.format=function(e){var t=arguments;return e.replace(De,function(e,n,r){var o=t[parseInt(n,10)+1];return l(o,r?r.substring(1):"")})},ve._extractFormat=function(e){return"{0:"===e.slice(0,3)&&(e=e.slice(3,e.length-1)),e},ve._activeElement=function(){try{return document.activeElement}catch(e){return document.documentElement.activeElement}},ve._round=u,ve._outerWidth=function(t,n){return e(t).outerWidth(n||!1)||0},ve._outerHeight=function(t,n){return e(t).outerHeight(n||!1)||0},ve.toString=l}(),function(){function t(e,t,n){return!(e>=t&&e<=n)}function r(e){return e.charAt(0)}function o(t){return e.map(t,r)}function i(e,t){t||23!==e.getHours()||e.setHours(e.getHours()+2)}function a(e){for(var t=0,n=e.length,r=[];t<n;t++)r[t]=(e[t]+"").toLowerCase();return r}function s(e){var t,n={};for(t in e)n[t]=a(e[t]);return n}function u(e,r,a,u){if(!e)return null;var l,c,d,f,p,g,y,v,b,M,S,x,T,k=function(e){for(var t=0;r[R]===e;)t++,R++;return t>0&&(R-=1),t},O=function(t){var n=w[t]||RegExp("^\\d{1,"+t+"}"),r=e.substr(U,t).match(n);return r?(r=r[0],U+=r.length,parseInt(r,10)):null},z=function(t,n){for(var r,o,i,a=0,s=t.length,u=0,l=0;a<s;a++)r=t[a],o=r.length,i=e.substr(U,o),n&&(i=i.toLowerCase()),i==r&&o>u&&(u=o,l=a);return u?(U+=u,l+1):null},D=function(){var t=!1;return e.charAt(U)===r[R]&&(U++,t=!0),t},C=a.calendars.standard,E=null,_=null,A=null,H=null,N=null,P=null,F=null,R=0,U=0,W=!1,I=new Date,$=C.twoDigitYearMax||2029,L=I.getFullYear();for(r||(r="d"),f=C.patterns[r],f&&(r=f),r=r.split(""),d=r.length;R<d;R++)if(l=r[R],W)"'"===l?W=!1:D();else if("d"===l){if(c=k("d"),C._lowerDays||(C._lowerDays=s(C.days)),null!==A&&c>2)continue;if(A=c<3?O(2):z(C._lowerDays[3==c?"namesAbbr":"names"],!0),null===A||t(A,1,31))return null}else if("M"===l){if(c=k("M"),C._lowerMonths||(C._lowerMonths=s(C.months)),_=c<3?O(2):z(C._lowerMonths[3==c?"namesAbbr":"names"],!0),null===_||t(_,1,12))return null;_-=1}else if("y"===l){if(c=k("y"),E=O(c),null===E)return null;2==c&&("string"==typeof $&&($=L+parseInt($,10)),E=L-L%100+E,E>$&&(E-=100))}else if("h"===l){if(k("h"),H=O(2),12==H&&(H=0),null===H||t(H,0,11))return null}else if("H"===l){if(k("H"),H=O(2),null===H||t(H,0,23))return null}else if("m"===l){if(k("m"),N=O(2),null===N||t(N,0,59))return null}else if("s"===l){if(k("s"),P=O(2),null===P||t(P,0,59))return null}else if("f"===l){if(c=k("f"),T=e.substr(U,c).match(w[3]),F=O(c),null!==F&&(F=parseFloat("0."+T[0],10),F=ve._round(F,3),F*=1e3),null===F||t(F,0,999))return null}else if("t"===l){if(c=k("t"),v=C.AM,b=C.PM,1===c&&(v=o(v),b=o(b)),p=z(b),!p&&!z(v))return null}else if("z"===l){if(g=!0,c=k("z"),"Z"===e.substr(U,1)){D();continue}if(y=e.substr(U,6).match(c>2?h:m),!y)return null;if(y=y[0].split(":"),M=y[0],S=y[1],!S&&M.length>3&&(U=M.length-2,S=M.substring(U),M=M.substring(0,U)),M=parseInt(M,10),t(M,-12,13))return null;if(c>2&&(S=y[0][0]+S,S=parseInt(S,10),isNaN(S)||t(S,-59,59)))return null}else if("'"===l)W=!0,D();else if(!D())return null;return u&&!/^\s*$/.test(e.substr(U))?null:(x=null!==H||null!==N||P||null,null===E&&null===_&&null===A&&x?(E=L,_=I.getMonth(),A=I.getDate()):(null===E&&(E=L),null===A&&(A=1)),p&&H<12&&(H+=12),g?(M&&(H+=-M),S&&(N+=-S),e=new Date(Date.UTC(E,_,A,H,N,P,F))):(e=new Date(E,_,A,H,N,P,F),i(e,H)),E<100&&e.setFullYear(E),e.getDate()!==A&&g===n?null:e)}function l(e){var t="-"===e.substr(0,1)?-1:1;return e=e.substring(1),e=60*parseInt(e.substr(0,2),10)+parseInt(e.substring(2),10),t*e}function c(e){var t,n,r,o=Te.max(v.length,b.length),i=e.calendar||e.calendars.standard,a=i.patterns,s=[];for(r=0;r<o;r++){for(t=v[r],n=0;n<t.length;n++)s.push(a[t[n]]);s=s.concat(b[r])}return s}function d(e,t,n,r){var o,i,a,s;if("[object Date]"===M.call(e))return e;if(o=0,i=null,e&&0===e.indexOf("/D")&&(i=g.exec(e)))return i=i[1],s=y.exec(i.substring(1)),i=new Date(parseInt(i,10)),s&&(s=l(s[0]),i=ve.timezone.apply(i,0),i=ve.timezone.convert(i,0,-1*s)),i;for(n=ve.getCulture(n),t||(t=c(n)),t=Me(t)?t:[t],a=t.length;o<a;o++)if(i=u(e,t[o],n,r))return i;return i}var f=/\u00A0/g,p=/[eE][\-+]?[0-9]+/,m=/[+|\-]\d{1,2}/,h=/[+|\-]\d{1,2}:?\d{2}/,g=/^\/Date\((.*?)\)\/$/,y=/[+-]\d*/,v=[[],["G","g","F"],["D","d","y","m","T","t"]],b=[["yyyy-MM-ddTHH:mm:ss.fffffffzzz","yyyy-MM-ddTHH:mm:ss.fffffff","yyyy-MM-ddTHH:mm:ss.fffzzz","yyyy-MM-ddTHH:mm:ss.fff","ddd MMM dd yyyy HH:mm:ss","yyyy-MM-ddTHH:mm:sszzz","yyyy-MM-ddTHH:mmzzz","yyyy-MM-ddTHH:mmzz","yyyy-MM-ddTHH:mm:ss","yyyy-MM-dd HH:mm:ss","yyyy/MM/dd HH:mm:ss"],["yyyy-MM-ddTHH:mm","yyyy-MM-dd HH:mm","yyyy/MM/dd HH:mm"],["yyyy/MM/dd","yyyy-MM-dd","HH:mm:ss","HH:mm"]],w={2:/^\d{1,2}/,3:/^\d{1,3}/,4:/^\d{4}/},M={}.toString;ve.parseDate=function(e,t,n){return d(e,t,n,!1)},ve.parseExactDate=function(e,t,n){return d(e,t,n,!0)},ve.parseInt=function(e,t){var n=ve.parseFloat(e,t);return n&&(n=0|n),n},ve.parseFloat=function(e,t,n){if(!e&&0!==e)return null;if(typeof e===He)return e;e=""+e,t=ve.getCulture(t);var r,o,i=t.numberFormat,a=i.percent,s=i.currency,u=s.symbol,l=a.symbol,c=e.indexOf("-");return p.test(e)?(e=parseFloat(e.replace(i["."],".")),isNaN(e)&&(e=null),e):c>0?null:(c=c>-1,e.indexOf(u)>-1||n&&n.toLowerCase().indexOf("c")>-1?(i=s,r=i.pattern[0].replace("$",u).split("n"),e.indexOf(r[0])>-1&&e.indexOf(r[1])>-1&&(e=e.replace(r[0],"").replace(r[1],""),c=!0)):e.indexOf(l)>-1&&(o=!0,i=a,u=l),e=e.replace("-","").replace(u,"").replace(f," ").split(i[","].replace(f," ")).join("").replace(i["."],"."),e=parseFloat(e),isNaN(e)?e=null:c&&(e*=-1),e&&o&&(e/=100),e)}}(),function(){var r,o,i,a,s,u,l,c,f,p;Oe._scrollbar=n,Oe.scrollbar=function(e){if(isNaN(Oe._scrollbar)||e){var t,n=document.createElement("div");return n.style.cssText="overflow:scroll;overflow-x:hidden;zoom:1;clear:both;display:block",n.innerHTML="&nbsp;",document.body.appendChild(n),Oe._scrollbar=t=n.offsetWidth-n.scrollWidth,document.body.removeChild(n),t}return Oe._scrollbar},Oe.isRtl=function(t){return e(t).closest(".k-rtl").length>0},r=document.createElement("table");try{r.innerHTML="<tr><td></td></tr>",Oe.tbodyInnerHtml=!0}catch(m){Oe.tbodyInnerHtml=!1}Oe.touch="ontouchstart"in t,o=document.documentElement.style,i=Oe.transitions=!1,a=Oe.transforms=!1,s="HTMLElement"in t?HTMLElement.prototype:[],Oe.hasHW3D="WebKitCSSMatrix"in t&&"m11"in new t.WebKitCSSMatrix||"MozPerspective"in o||"msPerspective"in o,Oe.cssFlexbox="flexWrap"in o||"WebkitFlexWrap"in o||"msFlexWrap"in o,we(["Moz","webkit","O","ms"],function(){var e,t=""+this,n=typeof r.style[t+"Transition"]===Ae;if(n||typeof r.style[t+"Transform"]===Ae)return e=t.toLowerCase(),a={css:"ms"!=e?"-"+e+"-":"",prefix:t,event:"o"===e||"webkit"===e?e:""},n&&(i=a,i.event=i.event?i.event+"TransitionEnd":"transitionend"),!1}),r=null,Oe.transforms=a,Oe.transitions=i,Oe.devicePixelRatio=t.devicePixelRatio===n?1:t.devicePixelRatio;try{Oe.screenWidth=t.outerWidth||t.screen?t.screen.availWidth:t.innerWidth,Oe.screenHeight=t.outerHeight||t.screen?t.screen.availHeight:t.innerHeight}catch(m){Oe.screenWidth=t.screen.availWidth,Oe.screenHeight=t.screen.availHeight}Oe.detectOS=function(e){var n,r,o=!1,i=[],a=!/mobile safari/i.test(e),s={wp:/(Windows Phone(?: OS)?)\s(\d+)\.(\d+(\.\d+)?)/,fire:/(Silk)\/(\d+)\.(\d+(\.\d+)?)/,android:/(Android|Android.*(?:Opera|Firefox).*?\/)\s*(\d+)\.?(\d+(\.\d+)?)?/,iphone:/(iPhone|iPod).*OS\s+(\d+)[\._]([\d\._]+)/,ipad:/(iPad).*OS\s+(\d+)[\._]([\d_]+)/,meego:/(MeeGo).+NokiaBrowser\/(\d+)\.([\d\._]+)/,webos:/(webOS)\/(\d+)\.(\d+(\.\d+)?)/,blackberry:/(BlackBerry|BB10).*?Version\/(\d+)\.(\d+(\.\d+)?)/,playbook:/(PlayBook).*?Tablet\s*OS\s*(\d+)\.(\d+(\.\d+)?)/,windows:/(MSIE)\s+(\d+)\.(\d+(\.\d+)?)/,tizen:/(tizen).*?Version\/(\d+)\.(\d+(\.\d+)?)/i,sailfish:/(sailfish).*rv:(\d+)\.(\d+(\.\d+)?).*firefox/i,ffos:/(Mobile).*rv:(\d+)\.(\d+(\.\d+)?).*Firefox/},u={ios:/^i(phone|pad|pod)$/i,android:/^android|fire$/i,blackberry:/^blackberry|playbook/i,windows:/windows/,wp:/wp/,flat:/sailfish|ffos|tizen/i,meego:/meego/},l={tablet:/playbook|ipad|fire/i},c={omini:/Opera\sMini/i,omobile:/Opera\sMobi/i,firefox:/Firefox|Fennec/i,mobilesafari:/version\/.*safari/i,ie:/MSIE|Windows\sPhone/i,chrome:/chrome|crios/i,webkit:/webkit/i};for(r in s)if(s.hasOwnProperty(r)&&(i=e.match(s[r]))){if("windows"==r&&"plugins"in navigator)return!1;o={},o.device=r,o.tablet=d(r,l,!1),o.browser=d(e,c,"default"),o.name=d(r,u),o[o.name]=!0,o.majorVersion=i[2],o.minorVersion=(i[3]||"0").replace("_","."),n=o.minorVersion.replace(".","").substr(0,2),o.flatVersion=o.majorVersion+n+Array(3-(n.length<3?n.length:2)).join("0"),o.cordova=typeof t.PhoneGap!==Re||typeof t.cordova!==Re,o.appMode=t.navigator.standalone||/file|local|wmapp/.test(t.location.protocol)||o.cordova,o.android&&(Oe.devicePixelRatio<1.5&&o.flatVersion<400||a)&&(Oe.screenWidth>800||Oe.screenHeight>800)&&(o.tablet=r);break}return o},u=Oe.mobileOS=Oe.detectOS(navigator.userAgent),Oe.wpDevicePixelRatio=u.wp?screen.width/320:0,Oe.hasNativeScrolling=!1,(u.ios||u.android&&u.majorVersion>2||u.wp)&&(Oe.hasNativeScrolling=u),Oe.delayedClick=function(){if(Oe.touch){if(u.ios)return!0;if(u.android)return!Oe.browser.chrome||!(Oe.browser.version<32)&&!(e("meta[name=viewport]").attr("content")||"").match(/user-scalable=no/i)}return!1},Oe.mouseAndTouchPresent=Oe.touch&&!(Oe.mobileOS.ios||Oe.mobileOS.android),Oe.detectBrowser=function(e){var t,n=!1,r=[],o={edge:/(edge)[ \/]([\w.]+)/i,webkit:/(chrome|crios)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,msie:/(msie\s|trident.*? rv:)([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(t in o)if(o.hasOwnProperty(t)&&(r=e.match(o[t]))){n={},n[t]=!0,n[r[1].toLowerCase().split(" ")[0].split("/")[0]]=!0,n.version=parseInt(document.documentMode||r[2],10);break}return n},Oe.browser=Oe.detectBrowser(navigator.userAgent),Oe.detectClipboardAccess=function(){var e={copy:!!document.queryCommandSupported&&document.queryCommandSupported("copy"),cut:!!document.queryCommandSupported&&document.queryCommandSupported("cut"),paste:!!document.queryCommandSupported&&document.queryCommandSupported("paste")};return Oe.browser.chrome&&(e.paste=!1,Oe.browser.version>=43&&(e.copy=!0,e.cut=!0)),e},Oe.clipboard=Oe.detectClipboardAccess(),Oe.zoomLevel=function(){var e,n,r;try{return e=Oe.browser,n=0,r=document.documentElement,e.msie&&11==e.version&&r.scrollHeight>r.clientHeight&&!Oe.touch&&(n=Oe.scrollbar()),Oe.touch?r.clientWidth/t.innerWidth:e.msie&&e.version>=10?((top||t).document.documentElement.offsetWidth+n)/(top||t).innerWidth:1}catch(o){return 1}},Oe.cssBorderSpacing=n!==o.borderSpacing&&!(Oe.browser.msie&&Oe.browser.version<8),function(t){var n="",r=e(document.documentElement),o=parseInt(t.version,10);t.msie?n="ie":t.mozilla?n="ff":t.safari?n="safari":t.webkit?n="webkit":t.opera?n="opera":t.edge&&(n="edge"),n&&(n="k-"+n+" k-"+n+o),Oe.mobileOS&&(n+=" k-mobile"),Oe.cssFlexbox||(n+=" k-no-flexbox"),r.addClass(n)}(Oe.browser),Oe.eventCapture=document.documentElement.addEventListener,l=document.createElement("input"),Oe.placeholder="placeholder"in l,Oe.propertyChangeEvent="onpropertychange"in l,Oe.input=function(){for(var e,t=["number","date","time","month","week","datetime","datetime-local"],n=t.length,r="test",o={},i=0;i<n;i++)e=t[i],l.setAttribute("type",e),l.value=r,o[e.replace("-","")]="text"!==l.type&&l.value!==r;return o}(),l.style.cssText="float:left;",Oe.cssFloat=!!l.style.cssFloat,l=null,Oe.stableSort=function(){var e,t=513,n=[{index:0,field:"b"}];for(e=1;e<t;e++)n.push({index:e,field:"a"});return n.sort(function(e,t){return e.field>t.field?1:e.field<t.field?-1:0}),1===n[0].index}(),Oe.matchesSelector=s.webkitMatchesSelector||s.mozMatchesSelector||s.msMatchesSelector||s.oMatchesSelector||s.matchesSelector||s.matches||function(t){for(var n=document.querySelectorAll?(this.parentNode||document).querySelectorAll(t)||[]:e(t),r=n.length;r--;)if(n[r]==this)return!0;return!1},Oe.matchMedia="matchMedia"in t,Oe.pushState=t.history&&t.history.pushState,c=document.documentMode,Oe.hashChange="onhashchange"in t&&!(Oe.browser.msie&&(!c||c<=8)),Oe.customElements="registerElement"in t.document,f=Oe.browser.chrome,p=Oe.browser.mozilla,Oe.msPointers=!f&&t.MSPointerEvent,Oe.pointers=!f&&!p&&t.PointerEvent,Oe.kineticScrollNeeded=u&&(Oe.touch||Oe.msPointers||Oe.pointers)}(),B={left:{reverse:"right"},right:{reverse:"left"},down:{reverse:"up"},up:{reverse:"down"},top:{reverse:"bottom"},bottom:{reverse:"top"},"in":{reverse:"out"},out:{reverse:"in"}},Y={},e.extend(Y,{enabled:!0,Element:function(t){this.element=e(t)},promise:function(e,t){e.is(":visible")||e.css({display:e.data("olddisplay")||"block"}).css("display"),t.hide&&e.data("olddisplay",e.css("display")).hide(),t.init&&t.init(),t.completeCallback&&t.completeCallback(e),e.dequeue()},disable:function(){this.enabled=!1,this.promise=this.promiseShim},enable:function(){this.enabled=!0,this.promise=this.animatedPromise}}),Y.promiseShim=Y.promise,"kendoAnimate"in e.fn||be(e.fn,{kendoStop:function(e,t){return this.stop(e,t)},kendoAnimate:function(e,t,n,r){return S(this,e,t,n,r)},kendoAddClass:function(e,t){return ve.toggleClass(this,e,t,!0)},kendoRemoveClass:function(e,t){return ve.toggleClass(this,e,t,!1)},kendoToggleClass:function(e,t,n){return ve.toggleClass(this,e,t,n)}}),q=/&/g,J=/</g,V=/"/g,Q=/'/g,G=/>/g,K=function(e){return e.target},Oe.touch&&(K=function(e){var t="originalEvent"in e?e.originalEvent.changedTouches:"changedTouches"in e?e.changedTouches:null;return t?document.elementFromPoint(t[0].clientX,t[0].clientY):e.target},we(["swipe","swipeLeft","swipeRight","swipeUp","swipeDown","doubleTap","tap"],function(t,n){e.fn[n]=function(e){return this.bind(n,e)}})),Oe.touch?Oe.mobileOS?(Oe.mousedown="touchstart",Oe.mouseup="touchend",Oe.mousemove="touchmove",Oe.mousecancel="touchcancel",Oe.click="touchend",Oe.resize="orientationchange"):(Oe.mousedown="mousedown touchstart",Oe.mouseup="mouseup touchend",Oe.mousemove="mousemove touchmove",Oe.mousecancel="mouseleave touchcancel",Oe.click="click",Oe.resize="resize"):Oe.pointers?(Oe.mousemove="pointermove",Oe.mousedown="pointerdown",Oe.mouseup="pointerup",Oe.mousecancel="pointercancel",Oe.click="pointerup",Oe.resize="orientationchange resize"):Oe.msPointers?(Oe.mousemove="MSPointerMove",Oe.mousedown="MSPointerDown",Oe.mouseup="MSPointerUp",Oe.mousecancel="MSPointerCancel",Oe.click="MSPointerUp",Oe.resize="orientationchange resize"):(Oe.mousemove="mousemove",Oe.mousedown="mousedown",Oe.mouseup="mouseup",Oe.mousecancel="mouseleave",Oe.click="click",Oe.resize="resize"),Z=function(e,t){var n,r,o,i,a=t||"d",s=1;for(r=0,o=e.length;r<o;r++)i=e[r],""!==i&&(n=i.indexOf("["),0!==n&&(n==-1?i="."+i:(s++,i="."+i.substring(0,n)+" || {})"+i.substring(n))),s++,a+=i+(r<o-1?" || {})":")"));return Array(s).join("(")+a},X=/^([a-z]+:)?\/\//i,be(ve,{widgets:[],_widgetRegisteredCallbacks:[],ui:ve.ui||{},fx:ve.fx||w,effects:ve.effects||Y,mobile:ve.mobile||{},data:ve.data||{},dataviz:ve.dataviz||{},drawing:ve.drawing||{},spreadsheet:{messages:{}},keys:{INSERT:45,DELETE:46,BACKSPACE:8,TAB:9,ENTER:13,ESC:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,END:35,HOME:36,SPACEBAR:32,PAGEUP:33,PAGEDOWN:34,F2:113,F10:121,F12:123,NUMPAD_PLUS:107,NUMPAD_MINUS:109,NUMPAD_DOT:110},support:ve.support||Oe,animate:ve.animate||S,ns:"",attr:function(e){return"data-"+ve.ns+e},getShadows:a,wrap:s,deepExtend:l,getComputedStyles:m,isScrollable:h,scrollLeft:g,size:y,toCamelCase:p,toHyphens:f,getOffset:ve.getOffset||v,parseEffects:ve.parseEffects||b,toggleClass:ve.toggleClass||x,directions:ve.directions||B,Observable:F,Class:r,Template:A,template:Se(A.compile,A),render:Se(A.render,A),stringify:Se(ke.stringify,ke),eventTarget:K,htmlEncode:T,isLocalUrl:function(e){return e&&!X.test(e)},expr:function(e,t,n){return e=e||"",typeof t==Ae&&(n=t,t=!1),n=n||"d",e&&"["!==e.charAt(0)&&(e="."+e),t?(e=e.replace(/"([^.]*)\.([^"]*)"/g,'"$1_$DOT$_$2"'),
e=e.replace(/'([^.]*)\.([^']*)'/g,"'$1_$DOT$_$2'"),e=Z(e.split("."),n),e=e.replace(/_\$DOT\$_/g,".")):e=n+e,e},getter:function(e,t){var n=e+t;return Ue[n]=Ue[n]||Function("d","return "+ve.expr(e,t))},setter:function(e){return We[e]=We[e]||Function("d,value",ve.expr(e)+"=value")},accessor:function(e){return{get:ve.getter(e),set:ve.setter(e)}},guid:function(){var e,t,n="";for(e=0;e<32;e++)t=16*Te.random()|0,8!=e&&12!=e&&16!=e&&20!=e||(n+="-"),n+=(12==e?4:16==e?3&t|8:t).toString(16);return n},roleSelector:function(e){return e.replace(/(\S+)/g,"["+ve.attr("role")+"=$1],").slice(0,-1)},directiveSelector:function(e){var t,n=e.split(" ");if(n)for(t=0;t<n.length;t++)"view"!=n[t]&&(n[t]=n[t].replace(/(\w*)(view|bar|strip|over)$/,"$1-$2"));return n.join(" ").replace(/(\S+)/g,"kendo-mobile-$1,").slice(0,-1)},triggeredByInput:function(e){return/^(label|input|textarea|select)$/i.test(e.target.tagName)},onWidgetRegistered:function(e){for(var t=0,n=ve.widgets.length;t<n;t++)e(ve.widgets[t]);ve._widgetRegisteredCallbacks.push(e)},logToConsole:function(e,r){var o=t.console;!ve.suppressLog&&n!==o&&o.log&&o[r||"log"](e)}}),ee=F.extend({init:function(e,t){var n,r=this;r.element=ve.jQuery(e).handler(r),r.angular("init",t),F.fn.init.call(r),n=t?t.dataSource:null,n&&(t=be({},t,{dataSource:{}})),t=r.options=be(!0,{},r.options,t),n&&(t.dataSource=n),r.element.attr(ve.attr("role"))||r.element.attr(ve.attr("role"),(t.name||"").toLowerCase()),r.element.data("kendo"+t.prefix+t.name,r),r.bind(r.events,t)},events:[],options:{prefix:""},_hasBindingTarget:function(){return!!this.element[0].kendoBindingTarget},_tabindex:function(e){e=e||this.wrapper;var t=this.element,n="tabindex",r=e.attr(n)||t.attr(n);t.removeAttr(n),e.attr(n,isNaN(r)?0:r)},setOptions:function(t){this._setEvents(t),e.extend(this.options,t)},_setEvents:function(e){for(var t,n=this,r=0,o=n.events.length;r<o;r++)t=n.events[r],n.options[t]&&e[t]&&n.unbind(t,n.options[t]);n.bind(n.events,e)},resize:function(e){var t=this.getSize(),n=this._size;(e||(t.width>0||t.height>0)&&(!n||t.width!==n.width||t.height!==n.height))&&(this._size=t,this._resize(t,e),this.trigger("resize",t))},getSize:function(){return ve.dimensions(this.element)},size:function(e){return e?(this.setSize(e),n):this.getSize()},setSize:e.noop,_resize:e.noop,destroy:function(){var e=this;e.element.removeData("kendo"+e.options.prefix+e.options.name),e.element.removeData("handler"),e.unbind()},_destroy:function(){this.destroy()},angular:function(){},_muteAngularRebind:function(e){this._muteRebind=!0,e.call(this),this._muteRebind=!1}}),te=ee.extend({dataItems:function(){return this.dataSource.flatView()},_angularItems:function(t){var n=this;n.angular(t,function(){return{elements:n.items(),data:e.map(n.dataItems(),function(e){return{dataItem:e}})}})}}),ve.dimensions=function(e,t){var n=e[0];return t&&e.css(t),{width:n.offsetWidth,height:n.offsetHeight}},ve.notify=xe,ne=/template$/i,re=/^\s*(?:\{(?:.|\r\n|\n)*\}|\[(?:.|\r\n|\n)*\])\s*$/,oe=/^\{(\d+)(:[^\}]+)?\}|^\[[A-Za-z_]+\]$/,ie=/([A-Z])/g,ve.initWidget=function(r,o,i){var a,s,u,l,c,d,f,p,m,h,g,y,v;if(i?i.roles&&(i=i.roles):i=ve.ui.roles,r=r.nodeType?r:r[0],d=r.getAttribute("data-"+ve.ns+"role")){m=d.indexOf(".")===-1,u=m?i[d]:ve.getter(d)(t),g=e(r).data(),y=u?"kendo"+u.fn.options.prefix+u.fn.options.name:"",h=m?RegExp("^kendo.*"+d+"$","i"):RegExp("^"+y+"$","i");for(v in g)if(v.match(h)){if(v!==y)return g[v];a=g[v]}if(u){for(p=k(r,"dataSource"),o=e.extend({},O(r,u.fn.options),o),p&&(o.dataSource=typeof p===Ae?ve.getter(p)(t):p),l=0,c=u.fn.events.length;l<c;l++)s=u.fn.events[l],f=k(r,s),f!==n&&(o[s]=ve.getter(f)(t));return a?e.isEmptyObject(o)||a.setOptions(o):a=new u(r,o),a}}},ve.rolesFromNamespaces=function(e){var t,n,r=[];for(e[0]||(e=[ve.ui,ve.dataviz.ui]),t=0,n=e.length;t<n;t++)r[t]=e[t].roles;return be.apply(null,[{}].concat(r.reverse()))},ve.init=function(t){var n=ve.rolesFromNamespaces(Ie.call(arguments,1));e(t).find("[data-"+ve.ns+"role]").addBack().each(function(){ve.initWidget(this,{},n)})},ve.destroy=function(t){e(t).find("[data-"+ve.ns+"role]").addBack().each(function(){var t,n=e(this).data();for(t in n)0===t.indexOf("kendo")&&typeof n[t].destroy===_e&&n[t].destroy()})},ve.resize=function(t,n){var r,o=e(t).find("[data-"+ve.ns+"role]").addBack().filter(D);o.length&&(r=e.makeArray(o),r.sort(z),e.each(r,function(){var t=ve.widgetInstance(e(this));t&&t.resize(n)}))},ve.parseOptions=O,be(ve.ui,{Widget:ee,DataBoundWidget:te,roles:{},progress:function(t,n,r){var o,i,a,s,u,l=t.find(".k-loading-mask"),c=ve.support,d=c.browser;r=e.extend({},{width:"100%",height:"100%",top:t.scrollTop(),opacity:!1},r),u=r.opacity?"k-loading-mask k-opaque":"k-loading-mask",n?l.length||(o=c.isRtl(t),i=o?"right":"left",s=t.scrollLeft(),a=d.webkit&&o?t[0].scrollWidth-t.width()-2*s:0,l=e(ve.format("<div class='{0}'><span class='k-loading-text'>{1}</span><div class='k-loading-image'/><div class='k-loading-color'/></div>",u,ve.ui.progress.messages.loading)).width(r.width).height(r.height).css("top",r.top).css(i,Math.abs(s)+a).prependTo(t)):l&&l.remove()},plugin:function(t,r,o){var i,a,s,u,l=t.fn.options.name;for(r=r||ve.ui,o=o||"",r[l]=t,r.roles[l.toLowerCase()]=t,i="getKendo"+o+l,l="kendo"+o+l,a={name:l,widget:t,prefix:o||""},ve.widgets.push(a),s=0,u=ve._widgetRegisteredCallbacks.length;s<u;s++)ve._widgetRegisteredCallbacks[s](a);e.fn[l]=function(r){var o,i=this;return typeof r===Ae?(o=Ie.call(arguments,1),this.each(function(){var t,a,s=e.data(this,l);if(!s)throw Error(ve.format("Cannot call method '{0}' of {1} before it is initialized",r,l));if(t=s[r],typeof t!==_e)throw Error(ve.format("Cannot find method '{0}' of {1}",r,l));if(a=t.apply(s,o),a!==n)return i=a,!1})):this.each(function(){return new t(this,r)}),i},e.fn[l].widget=t,e.fn[i]=function(){return this.data(l)}}}),ve.ui.progress.messages={loading:"Loading..."},ae={bind:function(){return this},nullObject:!0,options:{}},se=ee.extend({init:function(e,t){ee.fn.init.call(this,e,t),this.element.autoApplyNS(),this.wrapper=this.element,this.element.addClass("km-widget")},destroy:function(){ee.fn.destroy.call(this),this.element.kendoDestroy()},options:{prefix:"Mobile"},events:[],view:function(){var e=this.element.closest(ve.roleSelector("view splitview modalview drawer"));return ve.widgetInstance(e,ve.mobile.ui)||ae},viewHasNativeScrolling:function(){var e=this.view();return e&&e.options.useNativeScrolling},container:function(){var e=this.element.closest(ve.roleSelector("view layout modalview drawer splitview"));return ve.widgetInstance(e.eq(0),ve.mobile.ui)||ae}}),be(ve.mobile,{init:function(e){ve.init(e,ve.mobile.ui,ve.ui,ve.dataviz.ui)},appLevelNativeScrolling:function(){return ve.mobile.application&&ve.mobile.application.options&&ve.mobile.application.options.useNativeScrolling},roles:{},ui:{Widget:se,DataBoundWidget:te.extend(se.prototype),roles:{},plugin:function(e){ve.ui.plugin(e,ve.mobile.ui,"Mobile")}}}),l(ve.dataviz,{init:function(e){ve.init(e,ve.dataviz.ui)},ui:{roles:{},themes:{},views:[],plugin:function(e){ve.ui.plugin(e,ve.dataviz.ui)}},roles:{}}),ve.touchScroller=function(t,n){return n||(n={}),n.useNative=!0,e(t).map(function(t,r){return r=e(r),!(!Oe.kineticScrollNeeded||!ve.mobile.ui.Scroller||r.data("kendoMobileScroller"))&&(r.kendoMobileScroller(n),r.data("kendoMobileScroller"))})[0]},ve.preventDefault=function(e){e.preventDefault()},ve.widgetInstance=function(e,n){var r,o,i,a,s,u=e.data(ve.ns+"role"),l=[];if(u){if("content"===u&&(u="scroller"),"editortoolbar"===u&&(i=e.data("kendoEditorToolbar")))return i;if("view"===u)return e.data("kendoView");if(n)if(n[0])for(r=0,o=n.length;r<o;r++)l.push(n[r].roles[u]);else l.push(n.roles[u]);else l=[ve.ui.roles[u],ve.dataviz.ui.roles[u],ve.mobile.ui.roles[u]];for(u.indexOf(".")>=0&&(l=[ve.getter(u)(t)]),r=0,o=l.length;r<o;r++)if(a=l[r],a&&(s=e.data("kendo"+a.fn.options.prefix+a.fn.options.name)))return s}},ve.onResize=function(n){var r=n;return Oe.mobileOS.android&&(r=function(){setTimeout(n,600)}),e(t).on(Oe.resize,r),r},ve.unbindResize=function(n){e(t).off(Oe.resize,n)},ve.attrValue=function(e,t){return e.data(ve.ns+t)},ve.days={Sunday:0,Monday:1,Tuesday:2,Wednesday:3,Thursday:4,Friday:5,Saturday:6},e.extend(e.expr.pseudos,{kendoFocusable:function(t){var n=e.attr(t,"tabindex");return C(t,!isNaN(n)&&n>-1)}}),ue=["mousedown","mousemove","mouseenter","mouseleave","mouseover","mouseout","mouseup","click"],le="label, input, [data-rel=external]",ce={setupMouseMute:function(){var t,n=0,r=ue.length,o=document.documentElement;if(!ce.mouseTrap&&Oe.eventCapture)for(ce.mouseTrap=!0,ce.bustClick=!1,ce.captureMouse=!1,t=function(t){ce.captureMouse&&("click"===t.type?ce.bustClick&&!e(t.target).is(le)&&(t.preventDefault(),t.stopPropagation()):t.stopPropagation())};n<r;n++)o.addEventListener(ue[n],t,!0)},muteMouse:function(e){ce.captureMouse=!0,e.data.bustClick&&(ce.bustClick=!0),clearTimeout(ce.mouseTrapTimeoutID)},unMuteMouse:function(){clearTimeout(ce.mouseTrapTimeoutID),ce.mouseTrapTimeoutID=setTimeout(function(){ce.captureMouse=!1,ce.bustClick=!1},400)}},de={down:"touchstart mousedown",move:"mousemove touchmove",up:"mouseup touchend touchcancel",cancel:"mouseleave touchcancel"},Oe.touch&&(Oe.mobileOS.ios||Oe.mobileOS.android)?de={down:"touchstart",move:"touchmove",up:"touchend touchcancel",cancel:"touchcancel"}:Oe.pointers?de={down:"pointerdown",move:"pointermove",up:"pointerup",cancel:"pointercancel pointerleave"}:Oe.msPointers&&(de={down:"MSPointerDown",move:"MSPointerMove",up:"MSPointerUp",cancel:"MSPointerCancel MSPointerLeave"}),!Oe.msPointers||"onmspointerenter"in t||e.each({MSPointerEnter:"MSPointerOver",MSPointerLeave:"MSPointerOut"},function(t,n){e.event.special[t]={delegateType:n,bindType:n,handle:function(t){var r,o=this,i=t.relatedTarget,a=t.handleObj;return i&&(i===o||e.contains(o,i))||(t.type=a.origType,r=a.handler.apply(this,arguments),t.type=n),r}}}),fe=function(e){return de[e]||e},pe=/([^ ]+)/g,ve.applyEventMap=function(e,t){return e=e.replace(pe,fe),t&&(e=e.replace(pe,"$1."+t)),e},me=e.fn.on,$e(!0,_,e),_.fn=_.prototype=new e,_.fn.constructor=_,_.fn.init=function(t,n){return n&&n instanceof e&&!(n instanceof _)&&(n=_(n)),e.fn.init.call(this,t,n,he)},_.fn.init.prototype=_.fn,he=_(document),be(_.fn,{handler:function(e){return this.data("handler",e),this},autoApplyNS:function(e){return this.data("kendoNS",e||ve.guid()),this},on:function(){var e,t,n,r,o,i,a=this,s=a.data("kendoNS");return 1===arguments.length?me.call(a,arguments[0]):(e=a,t=Ie.call(arguments),typeof t[t.length-1]===Re&&t.pop(),n=t[t.length-1],r=ve.applyEventMap(t[0],s),Oe.mouseAndTouchPresent&&r.search(/mouse|click/)>-1&&this[0]!==document.documentElement&&(ce.setupMouseMute(),o=2===t.length?null:t[1],i=r.indexOf("click")>-1&&r.indexOf("touchend")>-1,me.call(this,{touchstart:ce.muteMouse,touchend:ce.unMuteMouse},o,{bustClick:i})),typeof n===Ae&&(e=a.data("handler"),n=e[n],t[t.length-1]=function(t){n.call(e,t)}),t[0]=r,me.apply(a,t),a)},kendoDestroy:function(e){return e=e||this.data("kendoNS"),e&&this.off("."+e),this}}),ve.jQuery=_,ve.eventMap=de,ve.timezone=function(){function e(e,t){var n,r,o,i=t[3],a=t[4],s=t[5],u=t[8];return u||(t[8]=u={}),u[e]?u[e]:(isNaN(a)?0===a.indexOf("last")?(n=new Date(Date.UTC(e,c[i]+1,1,s[0]-24,s[1],s[2],0)),r=d[a.substr(4,3)],o=n.getUTCDay(),n.setUTCDate(n.getUTCDate()+r-o-(r>o?7:0))):a.indexOf(">=")>=0&&(n=new Date(Date.UTC(e,c[i],a.substr(5),s[0],s[1],s[2],0)),r=d[a.substr(0,3)],o=n.getUTCDay(),n.setUTCDate(n.getUTCDate()+r-o+(r<o?7:0))):n=new Date(Date.UTC(e,c[i],a,s[0],s[1],s[2],0)),u[e]=n)}function t(t,n,r){var o,i,a,s;return(n=n[r])?(a=new Date(t).getUTCFullYear(),n=jQuery.grep(n,function(e){var t=e[0],n=e[1];return t<=a&&(n>=a||t==a&&"only"==n||"max"==n)}),n.push(t),n.sort(function(t,n){return"number"!=typeof t&&(t=+e(a,t)),"number"!=typeof n&&(n=+e(a,n)),t-n}),s=n[jQuery.inArray(t,n)-1]||n[n.length-1],isNaN(s)?s:null):(o=r.split(":"),i=0,o.length>1&&(i=60*o[0]+ +o[1]),[-1e6,"max","-","Jan",1,[0,0,0],i,"-"])}function n(e,t,n){var r,o,i,a=t[n];if("string"==typeof a&&(a=t[a]),!a)throw Error('Timezone "'+n+'" is either incorrect, or kendo.timezones.min.js is not included.');for(r=a.length-1;r>=0&&(o=a[r][3],!(o&&e>o));r--);if(i=a[r+1],!i)throw Error('Timezone "'+n+'" not found on '+e+".");return i}function r(e,r,o,i){typeof e!=He&&(e=Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));var a=n(e,r,i);return{zone:a,rule:t(e,o,a[1])}}function o(e,t){var n,o,i;return"Etc/UTC"==t||"Etc/GMT"==t?0:(n=r(e,this.zones,this.rules,t),o=n.zone,i=n.rule,ve.parseFloat(i?o[0]-i[6]:o[0]))}function i(e,t){var n=r(e,this.zones,this.rules,t),o=n.zone,i=n.rule,a=o[2];return a.indexOf("/")>=0?a.split("/")[i&&+i[6]?1:0]:a.indexOf("%s")>=0?a.replace("%s",i&&"-"!=i[7]?i[7]:""):a}function a(e,t,n){var r,o,i,a=n;return typeof t==Ae&&(t=this.offset(e,t)),typeof n==Ae&&(n=this.offset(e,n)),o=e.getTimezoneOffset(),e=new Date(e.getTime()+6e4*(t-n)),i=e.getTimezoneOffset(),typeof a==Ae&&(a=this.offset(e,a)),r=i-o+(n-a),new Date(e.getTime()+6e4*r)}function s(e,t){return this.convert(e,e.getTimezoneOffset(),t)}function u(e,t){return this.convert(e,t,e.getTimezoneOffset())}function l(e){return this.apply(new Date(e),"Etc/UTC")}var c={Jan:0,Feb:1,Mar:2,Apr:3,May:4,Jun:5,Jul:6,Aug:7,Sep:8,Oct:9,Nov:10,Dec:11},d={Sun:0,Mon:1,Tue:2,Wed:3,Thu:4,Fri:5,Sat:6};return{zones:{},rules:{},offset:o,convert:a,apply:s,remove:u,abbr:i,toLocalDate:l}}(),ve.date=function(){function e(e,t){return 0===t&&23===e.getHours()&&(e.setHours(e.getHours()+2),!0)}function t(t,n,r){var o=t.getHours();r=r||1,n=(n-t.getDay()+7*r)%7,t.setDate(t.getDate()+n),e(t,o)}function r(e,n,r){return e=new Date(e),t(e,n,r),e}function o(e){return new Date(e.getFullYear(),e.getMonth(),1)}function i(e){var t=new Date(e.getFullYear(),e.getMonth()+1,0),n=o(e),r=Math.abs(t.getTimezoneOffset()-n.getTimezoneOffset());return r&&t.setHours(n.getHours()+r/60),t}function a(e,t){return 1!==t?m(r(e,t,-1),4):m(e,4-(e.getDay()||7))}function s(e,t){var n=new Date(e.getFullYear(),0,1,(-6)),r=a(e,t),o=r.getTime()-n.getTime(),i=Math.floor(o/M);return 1+Math.floor(i/7)}function u(e,t){var r,o,i;return t===n&&(t=ve.culture().calendar.firstDay),r=m(e,-7),o=m(e,7),i=s(e,t),0===i?s(r,t)+1:53===i&&s(o,t)>1?1:i}function l(t){return t=new Date(t.getFullYear(),t.getMonth(),t.getDate(),0,0,0),e(t,0),t}function c(e){return Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds())}function d(e){return b(e).getTime()-l(b(e))}function f(e,t,n){var r,o=d(t),i=d(n);return!e||o==i||(t>=n&&(n+=M),r=d(e),o>r&&(r+=M),i<o&&(i+=M),r>=o&&r<=i)}function p(e,t,n){var r,o=t.getTime(),i=n.getTime();return o>=i&&(i+=M),r=e.getTime(),r>=o&&r<=i}function m(t,n){var r=t.getHours();return t=new Date(t),h(t,n*M),e(t,r),t}function h(e,t,n){var r,o=e.getTimezoneOffset();e.setTime(e.getTime()+t),n||(r=e.getTimezoneOffset()-o,e.setTime(e.getTime()+r*w))}function g(t,n){return t=new Date(ve.date.getDate(t).getTime()+ve.date.getMilliseconds(n)),e(t,n.getHours()),t}function y(){return l(new Date)}function v(e){return l(e).getTime()==y().getTime()}function b(e){var t=new Date(1980,1,1,0,0,0);return e&&t.setHours(e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()),t}var w=6e4,M=864e5;return{adjustDST:e,dayOfWeek:r,setDayOfWeek:t,getDate:l,isInDateRange:p,isInTimeRange:f,isToday:v,nextDay:function(e){return m(e,1)},previousDay:function(e){return m(e,-1)},toUtcTime:c,MS_PER_DAY:M,MS_PER_HOUR:60*w,MS_PER_MINUTE:w,setTime:h,setHours:g,addDays:m,today:y,toInvariantTime:b,firstDayOfMonth:o,lastDayOfMonth:i,weekInYear:u,getMilliseconds:d}}(),ve.stripWhitespace=function(e){var t,n,r;if(document.createNodeIterator)for(t=document.createNodeIterator(e,NodeFilter.SHOW_TEXT,function(t){return t.parentNode==e?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT},!1);t.nextNode();)t.referenceNode&&!t.referenceNode.textContent.trim()&&t.referenceNode.parentNode.removeChild(t.referenceNode);else for(n=0;n<e.childNodes.length;n++)r=e.childNodes[n],3!=r.nodeType||/\S/.test(r.nodeValue)||(e.removeChild(r),n--),1==r.nodeType&&ve.stripWhitespace(r)},ge=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.oRequestAnimationFrame||t.msRequestAnimationFrame||function(e){setTimeout(e,1e3/60)},ve.animationFrame=function(e){ge.call(t,e)},ye=[],ve.queueAnimation=function(e){ye[ye.length]=e,1===ye.length&&ve.runNextAnimation()},ve.runNextAnimation=function(){ve.animationFrame(function(){ye[0]&&(ye.shift()(),ye[0]&&ve.runNextAnimation())})},ve.parseQueryStringParams=function(e){for(var t=e.split("?")[1]||"",n={},r=t.split(/&|=/),o=r.length,i=0;i<o;i+=2)""!==r[i]&&(n[decodeURIComponent(r[i])]=decodeURIComponent(r[i+1]));return n},ve.elementUnderCursor=function(e){if(n!==e.x.client)return document.elementFromPoint(e.x.client,e.y.client)},ve.wheelDeltaY=function(e){var t,r=e.originalEvent,o=r.wheelDeltaY;return r.wheelDelta?(o===n||o)&&(t=r.wheelDelta):r.detail&&r.axis===r.VERTICAL_AXIS&&(t=10*-r.detail),t},ve.throttle=function(e,t){var r,o,i=0;return!t||t<=0?e:(o=function(){function o(){e.apply(a,u),i=+new Date}var a=this,s=+new Date-i,u=arguments;return i?(r&&clearTimeout(r),s>t?o():r=setTimeout(o,t-s),n):o()},o.cancel=function(){clearTimeout(r)},o)},ve.caret=function(t,r,o){var i,a,s,u,l,c=r!==n;if(o===n&&(o=r),t[0]&&(t=t[0]),!c||!t.disabled){try{t.selectionStart!==n?c?(t.focus(),a=Oe.mobileOS,a.wp||a.android?setTimeout(function(){t.setSelectionRange(r,o)},0):t.setSelectionRange(r,o)):r=[t.selectionStart,t.selectionEnd]:document.selection&&(e(t).is(":visible")&&t.focus(),i=t.createTextRange(),c?(i.collapse(!0),i.moveStart("character",r),i.moveEnd("character",o-r),i.select()):(s=i.duplicate(),i.moveToBookmark(document.selection.createRange().getBookmark()),s.setEndPoint("EndToStart",i),u=s.text.length,l=u+i.text.length,r=[u,l]))}catch(d){r=[]}return r}},ve.compileMobileDirective=function(e,n){var r=t.angular;return e.attr("data-"+ve.ns+"role",e[0].tagName.toLowerCase().replace("kendo-mobile-","").replace("-","")),r.element(e).injector().invoke(["$compile",function(t){t(e)(n),/^\$(digest|apply)$/.test(n.$$phase)||n.$digest()}]),ve.widgetInstance(e,ve.mobile.ui)},ve.antiForgeryTokens=function(){var t={},r=e("meta[name=csrf-token],meta[name=_csrf]").attr("content"),o=e("meta[name=csrf-param],meta[name=_csrf_header]").attr("content");return e("input[name^='__RequestVerificationToken']").each(function(){t[this.name]=this.value}),o!==n&&r!==n&&(t[o]=r),t},ve.cycleForm=function(e){function t(e){var t=ve.widgetInstance(e);t&&t.focus?t.focus():e.focus()}var n=e.find("input, .k-widget").first(),r=e.find("button, .k-button").last();r.on("keydown",function(e){e.keyCode!=ve.keys.TAB||e.shiftKey||(e.preventDefault(),t(n))}),n.on("keydown",function(e){e.keyCode==ve.keys.TAB&&e.shiftKey&&(e.preventDefault(),t(r))})},ve.focusElement=function(n){var r=[],o=n.parentsUntil("body").filter(function(e,t){var n=ve.getComputedStyles(t,["overflow"]);return"visible"!==n.overflow}).add(t);o.each(function(t,n){r[t]=e(n).scrollTop()});try{n[0].setActive()}catch(i){n[0].focus()}o.each(function(t,n){e(n).scrollTop(r[t])})},ve.matchesMedia=function(e){var n=ve._bootstrapToMedia(e)||e;return Oe.matchMedia&&t.matchMedia(n).matches},ve._bootstrapToMedia=function(e){return{xs:"(max-width: 576px)",sm:"(min-width: 576px)",md:"(min-width: 768px)",lg:"(min-width: 992px)",xl:"(min-width: 1200px)"}[e]},function(){function n(t,n,r,o){var i,a,s=e("<form>").attr({action:r,method:"POST",target:o}),u=ve.antiForgeryTokens();u.fileName=n,i=t.split(";base64,"),u.contentType=i[0].replace("data:",""),u.base64=i[1];for(a in u)u.hasOwnProperty(a)&&e("<input>").attr({value:u[a],name:a,type:"hidden"}).appendTo(s);s.appendTo("body").submit().remove()}function r(e,t){var n,r,o,i,a,s=e;if("string"==typeof e){for(n=e.split(";base64,"),r=n[0],o=atob(n[1]),i=new Uint8Array(o.length),a=0;a<o.length;a++)i[a]=o.charCodeAt(a);s=new Blob([i.buffer],{type:r})}navigator.msSaveBlob(s,t)}function o(e,n){t.Blob&&e instanceof Blob&&(e=URL.createObjectURL(e)),i.download=n,i.href=e;var r=document.createEvent("MouseEvents");r.initMouseEvent("click",!0,!1,t,0,0,0,0,0,!1,!1,!1,!1,0,null),i.dispatchEvent(r),setTimeout(function(){URL.revokeObjectURL(e)})}var i=document.createElement("a"),a="download"in i&&!ve.support.browser.edge;ve.saveAs=function(e){var t=n;e.forceProxy||(a?t=o:navigator.msSaveBlob&&(t=r)),t(e.dataURI,e.fileName,e.proxyURL,e.proxyTarget)}}(),ve.proxyModelSetters=function(e){var t={};return Object.keys(e||{}).forEach(function(n){Object.defineProperty(t,n,{get:function(){return e[n]},set:function(t){e[n]=t,e.dirty=!0}})}),t}}(jQuery,window),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.core.min.js.map
