{"version": 3, "sources": ["kendo.multiselect.js"], "names": ["f", "define", "$", "undefined", "compare", "a", "b", "length", "kendo", "window", "ui", "List", "keys", "extend", "A", "activeElement", "_activeElement", "ObservableArray", "data", "proxy", "ID", "LI", "ACCEPT", "FILTER", "REBIND", "OPEN", "CLOSE", "CHANGE", "PROGRESS", "SELECT", "DESELECT", "ARIA_DISABLED", "FOCUSEDCLASS", "SELECTEDCLASS", "HIDDENCLASS", "HOVERCLASS", "STATEDISABLED", "DISABLED", "READONLY", "AUTOCOMPLETEVALUE", "support", "browser", "chrome", "ns", "CLICK", "KEYDOWN", "MOUSEENTER", "MOUSELEAVE", "HOVEREVENTS", "quotRegExp", "isArray", "styles", "MultiSelect", "init", "element", "options", "id", "disabled", "that", "this", "fn", "call", "_optionsMap", "_customOptions", "_wrapper", "_tagList", "_input", "_textContainer", "_loader", "_clearButton", "_tabindex", "input", "attr", "hide", "placeholder", "_tagID", "tagList", "_initialOpen", "_a<PERSON><PERSON><PERSON><PERSON>", "_ariaSetLive", "_dataSource", "_ignoreCase", "_popup", "_tagTemplate", "requireValueMapper", "_initList", "_reset", "_enable", "_placeholder", "autoBind", "dataSource", "fetch", "value", "_preselect", "parents", "is", "enable", "_ariaSetSize", "notify", "_toggleCloseVisibility", "name", "tagMode", "enabled", "autoClose", "highlight<PERSON><PERSON><PERSON>", "dataTextField", "dataValueField", "filter", "ignoreCase", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delay", "maxSelectedItems", "height", "animation", "virtual", "itemTemplate", "tagTemplate", "groupTemplate", "fixedGroupTemplate", "clearButton", "autoWidth", "popup", "events", "setDataSource", "_state", "persistTagList", "listView", "setOptions", "listOptions", "_listOptions", "_accessors", "_aria", "currentTag", "candidate", "_currentTag", "removeClass", "removeAttr", "find", "addClass", "dataItems", "selectedDataItems", "destroy", "clearTimeout", "_busy", "_typingTimeout", "wrapper", "off", "_clear", "_activateItem", "visible", "selectedItemChange", "_selectedItemChange", "selectable", "template", "expr", "_setListValue", "_initialValues", "slice", "_listChange", "e", "i", "flatView", "optionsMap", "valueGetter", "_value", "added", "dataItem", "_render", "_selectValue", "removed", "context", "idx", "items", "children", "eq", "index", "html", "tagTextTemplate", "item", "_wrapperMousedown", "notInput", "target", "nodeName", "toLowerCase", "closeButton", "hasClass", "closest", "mobileOS", "cancelable", "preventDefault", "focus", "open", "_inputFocus", "_inputFocusout", "close", "skipUpdate", "bound", "isFiltered", "_clearFilter", "blur", "_removeTag", "tag", "should<PERSON><PERSON>ger", "option", "list<PERSON>iew<PERSON><PERSON>d", "done", "state", "position", "customIndex", "listView<PERSON><PERSON><PERSON>n", "trigger", "_close", "_change", "select", "selected", "removeAt", "classList", "remove", "_updateTagListHTML", "_tagListClick", "currentTarget", "_clearClick", "_clearSingleTagValue", "each", "val", "_search", "_hideClear", "_editable", "disable", "readonly", "add", "on", "_toggleHover", "_keydown", "_filterSource", "force", "_retrieveData", "_activeItem", "_request", "_open", "_focusItem", "_allowOpening", "isPlainObject", "_hovered", "toggle", "refresh", "_listBound", "skip", "_renderFooter", "_renderNoData", "_toggleNoData", "_resizePopup", "focusFirst", "_touchScroller", "reset", "_hideBusy", "_makeUnselectable", "_inputValue", "inputValue", "oldValue", "clearFilters", "_normalizeValues", "_old", "_valueBeforeCascade", "_fetchData", "ObservableObject", "_setOption", "hasItems", "view", "isEmptyArray", "_fetch", "read", "_isBound", "fields", "field", "_refresh<PERSON><PERSON><PERSON>", "_unbindDataSource", "_progress<PERSON><PERSON><PERSON>", "_showBusy", "_error<PERSON><PERSON><PERSON>", "DataSource", "create", "bind", "formId", "form", "_reset<PERSON><PERSON><PERSON>", "setTimeout", "_form", "_initValue", "map", "_click", "_select", "_getActiveItem", "_getSelectedIndices", "_selectedIndices", "_selectedIndexes", "activeItemIdx", "activeIndex", "currentIndex", "direction", "key", "keyCode", "hasValue", "isRtl", "dir", "ENTER", "_multipleSelection", "DOWN", "shift<PERSON>ey", "getElementIndex", "first", "focusNext", "_select<PERSON>ange", "focusLast", "UP", "focusPrev", "LEFT", "RIGHT", "prev", "<PERSON><PERSON><PERSON><PERSON>", "next", "ctrl<PERSON>ey", "altKey", "SPACEBAR", "ESC", "HOME", "<PERSON><PERSON><PERSON><PERSON>", "END", "DELETE", "BACKSPACE", "PAGEDOWN", "PAGEUP", "_scale", "scrollWith", "screenHeight", "_loading", "_showBusyHandler", "show", "skipCaret", "active", "isActive", "caretPos", "_prev", "toggleClass", "caret", "textWidth", "wrapperWidth", "width", "span", "_span", "text", "appendTo", "document", "documentElement", "_option", "dataValue", "dataText", "indexOf", "replace", "htmlEncode", "selectedIndex", "custom", "selectedItems", "values", "_buildSelectedItems", "_selectedItemIndex", "splice", "_text", "valueField", "textField", "result", "push", "search", "_showClear", "_allowSelection", "max", "_angularTagItems", "cmd", "angular", "elements", "updatePersistTagList", "_removedAddedIndexes", "removedItem", "addedItem", "total", "getter", "<PERSON><PERSON><PERSON><PERSON>", "append", "_maxTotal", "maxTotal", "currentTotal", "isSelected", "resolved", "Deferred", "resolve", "dataItemByIndex", "startIndex", "endIndex", "indices", "indicesToSelect", "selectIndices", "for<PERSON>ach", "accessKey", "_innerWrapper", "_focused", "accesskey", "autocomplete", "role", "title", "aria-expanded", "aria-haspopup", "aria-autocomplete", "defaultTemplate", "hasDataSource", "isMultiple", "useWithBlock", "insertAfter", "computedStyles", "getComputedStyles", "visibility", "top", "left", "css", "parent", "wrap", "style", "cssText", "insertBefore", "className", "ul", "_isFilterEnabled", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBACH,aACA,wBACA,qBACDD,IACL,WAiqCE,MA3oCC,UAAUE,EAAGC,GAynCV,QAASC,GAAQC,EAAGC,GAChB,GAAIC,EACJ,IAAU,OAANF,GAAoB,OAANC,GAAoB,OAAND,GAAoB,OAANC,EAC1C,OAAO,CAGX,IADAC,EAASF,EAAEE,OACPA,IAAWD,EAAEC,OACb,OAAO,CAEX,MAAOA,KACH,GAAIF,EAAEE,KAAYD,EAAEC,GAChB,OAAO,CAGf,QAAO,EAvoCd,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAAOD,EAAGC,KAAMC,EAAOV,EAAEW,QAASC,EAAG,IAAMN,EAAMI,MAAOG,EAAgBP,EAAMQ,eAAgBC,EAAkBT,EAAMU,KAAKD,gBAAiBE,EAAQjB,EAAEiB,MAAOC,EAAK,KAAMC,EAAK,KAAMC,EAAS,SAAUC,EAAS,SAAUC,EAAS,SAAUC,EAAO,OAAQC,EAAQ,QAASC,EAAS,SAAUC,EAAW,WAAYC,EAAS,SAAUC,EAAW,WAAYC,EAAgB,gBAAiBC,EAAe,kBAAmBC,EAAgB,mBAAoBC,EAAc,WAAYC,EAAa,gBAAiBC,EAAgB,mBAAoBC,EAAW,WAAYC,EAAW,WAAYC,EAAoB/B,EAAMgC,QAAQC,QAAQC,OAAS,WAAa,MAAOC,EAAK,oBAAqBC,EAAQ,QAAUD,EAAIE,EAAU,UAAYF,EAAIG,EAAa,aAAeH,EAAII,EAAa,aAAeJ,EAAIK,EAAcF,EAAa,IAAMC,EAAYE,EAAa,KAAMC,EAAUhD,EAAEgD,QAASC,GACx5B,cACA,YACA,eACA,aACA,cACA,iBACA,iBACA,eAEJC,EAAczC,EAAKE,QACnBwC,KAAM,SAAUC,EAASC,GACrB,GAAiBC,GAAIC,EAAjBC,EAAOC,IACXD,GAAKf,GAAKA,EACVhC,EAAKiD,GAAGP,KAAKQ,KAAKH,EAAMJ,EAASC,GACjCG,EAAKI,eACLJ,EAAKK,kBACLL,EAAKM,WACLN,EAAKO,WACLP,EAAKQ,SACLR,EAAKS,iBACLT,EAAKU,UACLV,EAAKW,eACLX,EAAKY,UAAUZ,EAAKa,OACpBjB,EAAUI,EAAKJ,QAAQkB,KAAK,WAAY,YAAYC,OACpDlB,EAAUG,EAAKH,QACVA,EAAQmB,cACTnB,EAAQmB,YAAcpB,EAAQpC,KAAK,gBAEvCsC,EAAKF,EAAQkB,KAAKpD,GACdoC,IACAE,EAAKiB,OAASnB,EAAK,cACnBA,GAAU,WACVE,EAAKkB,QAAQJ,KAAKpD,EAAIoC,GACtBE,EAAKa,MAAMC,KAAK,mBAAoBhB,IAExCE,EAAKmB,cAAe,EACpBnB,EAAKoB,aACLpB,EAAKqB,eACLrB,EAAKsB,cACLtB,EAAKuB,cACLvB,EAAKwB,SACLxB,EAAKyB,eACLzB,EAAK0B,mBAAmB1B,EAAKH,SAC7BG,EAAK2B,YACL3B,EAAK4B,SACL5B,EAAK6B,UACL7B,EAAK8B,eACDjC,EAAQkC,SACR/B,EAAKgC,WAAWC,QACTpC,EAAQqC,OACflC,EAAKmC,WAAWtC,EAAQqC,OAE5BnC,EAAWvD,EAAEwD,EAAKJ,SAASwC,QAAQ,YAAYC,GAAG,aAC9CtC,GACAC,EAAKsC,QAAO,GAEhBtC,EAAKuC,aAAavC,EAAKkC,QAAQrF,QAC/BC,EAAM0F,OAAOxC,GACbA,EAAKyC,0BAET5C,SACI6C,KAAM,cACNC,QAAS,WACTC,SAAS,EACTb,UAAU,EACVc,WAAW,EACXC,gBAAgB,EAChBC,cAAe,GACfC,eAAgB,GAChBC,OAAQ,aACRC,YAAY,EACZC,UAAW,EACXC,kBAAkB,EAClBC,MAAO,IACPnB,MAAO,KACPoB,iBAAkB,KAClBtC,YAAa,GACbuC,OAAQ,IACRC,aACAC,SAAS,EACTC,aAAc,GACdC,YAAa,GACbC,cAAe,UACfC,mBAAoB,UACpBC,aAAa,EACbC,WAAW,EACXC,MAAO,MAEXC,QACIlG,EACAC,EACAC,EACAE,EACAC,EACA,YACA,cACA,aAEJ8F,cAAe,SAAUlC,GACrB/B,KAAKJ,QAAQmC,WAAaA,EAC1B/B,KAAKkE,OAAS,GACdlE,KAAKqB,cACLrB,KAAKmE,gBAAiB,EACtBnE,KAAKoE,SAASH,cAAcjE,KAAK+B,YAC7B/B,KAAKJ,QAAQkC,UACb9B,KAAK+B,WAAWC,SAGxBqC,WAAY,SAAUzE,GAClB,GAAI0E,GAActE,KAAKuE,aAAa3E,EACpC5C,GAAKiD,GAAGoE,WAAWnE,KAAKF,KAAMJ,GAC9BI,KAAKoE,SAASC,WAAWC,GACzBtE,KAAKwE,aACLxE,KAAKyE,MAAMzE,KAAKiB,QAAQJ,KAAKpD,IAC7BuC,KAAKwB,eACLxB,KAAK6B,eACL7B,KAAKU,gBAETgE,WAAY,SAAUC,GAClB,GAAI5E,GAAOC,IACX,OAAI2E,KAAcnI,EAaPuD,EAAK6E,aAZR7E,EAAK6E,cACL7E,EAAK6E,YAAYC,YAAYxG,GAAcyG,WAAWrH,GACtDsC,EAAK6E,YAAYG,KAAK,aAAalE,KAAK,eAAe,GACvDd,EAAKa,MAAMkE,WAAW,0BAEtBH,IACAA,EAAUK,SAAS3G,GAAcwC,KAAKpD,EAAIsC,EAAKiB,QAC/C2D,EAAUI,KAAK,aAAaD,WAAW,eACvC/E,EAAKa,MAAMC,KAAK,wBAAyBd,EAAKiB,SAElDjB,EAAK6E,YAAcD,EAVnB,IAeRM,UAAW,WACP,MAAOjF,MAAKoE,SAASc,qBAEzBC,QAAS,WACL,GAAIpF,GAAOC,KAAMhB,EAAKe,EAAKf,EAC3BoG,cAAarF,EAAKsF,OAClBD,aAAarF,EAAKuF,gBAClBvF,EAAKwF,QAAQC,IAAIxG,GACjBe,EAAKkB,QAAQuE,IAAIxG,GACjBe,EAAKa,MAAM4E,IAAIxG,GACfe,EAAK0F,OAAOD,IAAIxG,GAChBhC,EAAKiD,GAAGkF,QAAQjF,KAAKH,IAEzB2F,cAAe,WACP1F,KAAK+D,MAAM4B,WACX3I,EAAKiD,GAAGyF,cAAcxF,KAAKF,MAE/BA,KAAK0E,WAAW,OAEpBH,aAAc,SAAU3E,GAAV,GACNG,GAAOC,KACPsE,EAActH,EAAKiD,GAAGsE,aAAarE,KAAKH,EAAMxD,EAAEW,OAAO0C,GACvDgG,mBAAoBpI,EAAMuC,EAAK8F,oBAAqB9F,GACpD+F,WAAY,cAEZrC,EAAezD,KAAKJ,QAAQ6D,cAAgBzD,KAAKJ,QAAQmG,SACzDA,EAAWzB,EAAYb,cAAgBA,GAAgBa,EAAYyB,QAKvE,OAJKA,KACDA,EAAW,KAAOlJ,EAAMmJ,KAAK1B,EAAYxB,cAAe,QAAU,KAEtEwB,EAAYyB,SAAWA,EAChBzB,GAEX2B,cAAe,WACXjJ,EAAKiD,GAAGgG,cAAc/F,KAAKF,KAAMA,KAAKkG,eAAeC,MAAM,KAE/DC,YAAa,SAAUC,GAAV,GAOAC,GANL/I,EAAOyC,KAAK+B,WAAWwE,WACvBC,EAAaxG,KAAKG,YAClBsG,EAAczG,KAAK0G,MAIvB,KAHI1G,KAAKkE,SAAWrG,IAChBmC,KAAKkE,OAAS,IAEToC,EAAI,EAAGA,EAAID,EAAEM,MAAM/J,OAAQ0J,IAChC,GAAIE,EAAWC,EAAYJ,EAAEM,MAAML,GAAGM,aAAepK,EAAW,CAC5DwD,KAAK6G,QAAQtJ,EACb,OAGRyC,KAAK8G,aAAaT,EAAEM,MAAON,EAAEU,UAEjClB,oBAAqB,SAAUQ,GAAV,GAEbW,GACAC,EAFAC,EAAQb,EAAEa,KAGd,KAAKD,EAAM,EAAGA,EAAMC,EAAMtK,OAAQqK,IAC9BD,EAAUE,EAAMD,GAChBjH,KAAKiB,QAAQkG,WAAWC,GAAGJ,EAAQK,OAAOF,SAAS,cAAcG,KAAKtH,KAAKuH,gBAAgBP,EAAQQ,QAG3GC,kBAAmB,SAAUpB,GAAV,GACXtG,GAAOC,KACP0H,EAA+C,UAApCrB,EAAEsB,OAAOC,SAASC,cAC7BF,EAASpL,EAAE8J,EAAEsB,QACbG,EAAcH,EAAOI,SAAS,aAAeJ,EAAOI,SAAS,SAC7DD,KACAA,GAAeH,EAAOK,QAAQ,aAAab,SAAS,sBAAsBvK,SAE1E8K,GAAcI,GAAejL,EAAMgC,QAAQoJ,WAAa5B,EAAE6B,YAC1D7B,EAAE8B,iBAEDL,IACG/H,EAAKa,MAAM,KAAOxD,KAAmBsK,GACrC3H,EAAKa,MAAMwH,QAEgB,IAA3BrI,EAAKH,QAAQsD,WACbnD,EAAKsI,SAIjBC,YAAa,WACTtI,KAAK6B,cAAa,GAClB7B,KAAKuF,QAAQP,SAAS3G,IAE1BkK,eAAgB,WACZ,GAAIxI,GAAOC,IACXoF,cAAarF,EAAKuF,gBAClBvF,EAAKwF,QAAQV,YAAYxG,GACzB0B,EAAK8B,cAAc9B,EAAKqE,SAASc,oBAAoB,IAAI,GACzDnF,EAAKyI,QACDzI,EAAKmE,SAAWtG,IAChBmC,EAAKmE,OAASvG,EACdoC,EAAKqE,SAASqE,YAAW,IAEzB1I,EAAKqE,SAASsE,SAAW3I,EAAKqE,SAASuE,eACvC5I,EAAKoE,gBAAiB,EACtBpE,EAAK6I,gBAET7I,EAAKJ,QAAQkJ,QAEjBC,WAAY,SAAUC,EAAKC,GAAf,GASJC,GACAC,EAWAC,EApBApJ,EAAOC,KACPoJ,EAAQrJ,EAAKmE,OACbmF,EAAWN,EAAI1B,QACfjD,EAAWrE,EAAKqE,SAChBnC,EAAQmC,EAASnC,QAAQoH,GACzBzC,EAAW7G,EAAKqE,SAASc,oBAAoBmE,GAC7CC,EAAcvJ,EAAKK,eAAe6B,GAClCsH,EAAmBnF,EAASzE,QAAQ,GAAGwH,QAG3C,OAAIpH,GAAKyJ,QAAQrL,GACTyI,SAAUA,EACVY,KAAMuB,KAEVhJ,EAAK0J,SACL,IAEAH,IAAgB9M,GAAc4M,IAAUzL,GAAUyL,IAAUxL,IAC5D0L,EAAcvJ,EAAKI,YAAY8B,IAE/BkH,EAAO,WACPpJ,EAAK2E,WAAW,MACZsE,GACAjJ,EAAK2J,UAET3J,EAAK0J,UAELH,IAAgB9M,GAAa4H,EAASuF,SAAS/M,QAC/CmD,EAAKoE,gBAAiB,EACtBC,EAASuF,OAAOvF,EAASuF,SAASN,IAAWF,KAAKA,KAElDF,EAASlJ,EAAKJ,QAAQ,GAAGwH,SAASmC,GAC9BL,IACAA,EAAOW,UAAW,GAEtBxF,EAASyF,SAASR,GAClBH,EAAgBK,EAAiBD,GAC7BJ,GACAK,EAAiBD,GAAaQ,UAAUC,OAAO,oBAEtB,WAAzBhK,EAAKH,QAAQ8C,QACbqG,EAAIgB,SAEJhK,EAAKiK,qBAETb,KA5BJ,IA+BJc,cAAe,SAAU5D,GACrB,GAAIsB,GAASpL,EAAE8J,EAAE6D,cACZvC,GAAOR,SAAS,sBAAsBvK,QACvCoD,KAAK8I,WAAWnB,EAAOK,QAAQtK,IAAK,IAG5CyM,YAAa,WACT,GAAIpK,GAAOC,IACkB,YAAzBD,EAAKH,QAAQ8C,QACb3C,EAAKqK,uBAELrK,EAAKkB,QAAQkG,WAAWkD,KAAK,SAAUhD,EAAO0B,GAC1ChJ,EAAK+I,WAAWvM,EAAEwM,IAAM,KAGhChJ,EAAKa,MAAM0J,IAAI,IACfvK,EAAKwK,UACLxK,EAAK2J,UACL3J,EAAKqI,QACLrI,EAAKyK,aACDzK,EAAKmE,SAAWtG,IAChBmC,EAAKmE,OAASvG,IAGtByM,qBAAsB,WAAA,GACdrK,GAAOC,KACPmE,EAAiBpE,EAAKoE,cACtBA,KACApE,EAAKoE,gBAAiB,GAE1BpE,EAAKqE,SAASnC,UACdlC,EAAKoE,eAAiBA,GAE1BsG,UAAW,SAAU7K,GACjB,GAAIG,GAAOC,KAAM0K,EAAU9K,EAAQ8K,QAASC,EAAW/K,EAAQ+K,SAAUpF,EAAUxF,EAAKwF,QAAQC,IAAIxG,GAAKiC,EAAUlB,EAAKkB,QAAQuE,IAAIxG,GAAK4B,EAAQb,EAAKJ,QAAQiL,IAAI7K,EAAKa,MAAM4E,IAAIxG,GAC5K2L,IAAaD,GAWVA,EACAnF,EAAQP,SAASvG,GAEjB8G,EAAQV,YAAYpG,GAExBmC,EAAMC,KAAKnC,EAAUgM,GAAS7J,KAAKlC,EAAUgM,GAAU9J,KAAKzC,EAAesM,KAf3EnF,EAAQV,YAAYpG,GAAeoM,GAAGxL,EAAaU,EAAK+K,cAAcD,GAAG,YAAc7L,EAAK,YAAcA,EAAIxB,EAAMuC,EAAK0H,kBAAmB1H,IAC5IA,EAAKa,MAAMiK,GAAG3L,EAAS1B,EAAMuC,EAAKgL,SAAUhL,IAAO8K,GAAG,QAAU7L,EAAIxB,EAAMuC,EAAKwK,QAASxK,IAAO8K,GAAG,QAAU7L,EAAIxB,EAAMuC,EAAKwK,QAASxK,IAAO8K,GAAG,QAAU7L,EAAIxB,EAAMuC,EAAKuI,YAAavI,IAAO8K,GAAG,WAAa7L,EAAIxB,EAAMuC,EAAKwI,eAAgBxI,IAC1OA,EAAK0F,OAAOoF,GAAG5L,EAAQD,EAAK,YAAcA,EAAIxB,EAAMuC,EAAKoK,YAAapK,IACtEa,EAAMkE,WAAWpG,GAAUoG,WAAWnG,GAAUkC,KAAKzC,GAAe,GACpE6C,EAAQ4J,GAAG1L,EAAYzB,EAAI,WACvBnB,EAAEyD,MAAMgF,SAASxG,KAClBqM,GAAGzL,EAAY1B,EAAI,WAClBnB,EAAEyD,MAAM6E,YAAYrG,KACrBqM,GAAG5L,EAAO,wBAAyBzB,EAAMuC,EAAKkK,cAAelK,MAUxE0J,OAAQ,WACJ,GAAI1J,GAAOC,IACPD,GAAKH,QAAQgD,UACb7C,EAAKyI,QAELzI,EAAKgE,MAAMsF,YAGnB2B,cAAe,SAAUhI,EAAQiI,GACxBA,IACDA,EAAQjL,KAAKkL,eAEjBlL,KAAKkL,eAAgB,EACrBlO,EAAKiD,GAAG+K,cAAc9K,KAAKF,KAAMgD,EAAQiI,IAE7CzC,MAAO,WACHxI,KAAKmL,YAAc,KACnBnL,KAAKY,MAAMkE,WAAW,yBACtB9E,KAAK+D,MAAMyE,SAEfH,KAAM,WACF,GAAItI,GAAOC,IACPD,GAAKqL,WACLrL,EAAKmL,eAAgB,GAErBnL,EAAKmL,gBAAkBnL,EAAKqE,SAASsE,SAAW3I,EAAKmE,SAAWvG,GAChEoC,EAAKsL,OAAQ,EACbtL,EAAKmE,OAASrG,EACdkC,EAAKqE,SAASqE,YAAW,GACzB1I,EAAKoE,iBAAiBpE,EAAKmB,eAAiBnB,EAAKqE,SAASsE,SAC1D3I,EAAKiL,gBACLjL,EAAKuL,cACEvL,EAAKwL,mBACRxL,EAAKmB,cAAiBnB,EAAKH,QAAQkC,UAAa/B,EAAKH,QAAQ4D,UAAWzD,EAAKH,QAAQqC,OAAU1F,EAAEiP,cAAczL,EAAKH,QAAQqC,MAAM,KAClIlC,EAAKkC,MAAMlC,EAAKmG,gBAEpBnG,EAAKgE,MAAM0H,UAAW,EACtB1L,EAAKmB,cAAe,EACpBnB,EAAKgE,MAAMsE,OACXtI,EAAKuL,eAGbI,OAAQ,SAAUA,GACdA,EAASA,IAAWlP,EAAYkP,GAAU1L,KAAK+D,MAAM4B,UACrD3F,KAAK0L,EAAS5N,EAAOC,MAEzB4N,QAAS,WACL3L,KAAKoE,SAASuH,WAElBC,WAAY,WAAA,GACJ7L,GAAOC,KACPzC,EAAOwC,EAAKgC,WAAWwE,WACvBsF,EAAO9L,EAAKqE,SAASyH,MACzB9L,GAAK8G,QAAQtJ,GACbwC,EAAK+L,gBACL/L,EAAKgM,gBACLhM,EAAKiM,eAAezO,EAAKX,QACzBmD,EAAKkM,eACDlM,EAAKsL,QACLtL,EAAKsL,OAAQ,EACbtL,EAAK2L,OAAO3L,EAAKwL,kBAErBxL,EAAKgE,MAAMsF,YACPtJ,EAAKH,QAAQiD,gBAAmBgJ,IAASrP,GAAsB,IAATqP,GACtD9L,EAAKqE,SAAS8H,aAEdnM,EAAKoM,gBACLpM,EAAKoM,eAAeC,QAExBrM,EAAKsM,YACLtM,EAAKuM,oBACLvM,EAAKyJ,QAAQ,cAEjB+C,YAAa,WAAA,GACLxM,GAAOC,KACPwM,EAAazM,EAAKa,MAAM0J,KAI5B,OAHIvK,GAAKH,QAAQmB,cAAgByL,IAC7BA,EAAa,IAEVA,GAEXvK,MAAO,SAAUA,GAAV,GACClC,GAAOC,KACPoE,EAAWrE,EAAKqE,SAChBqI,EAAWrI,EAASnC,QAAQkE,QAC5B9C,EAAmBtD,EAAKH,QAAQyD,iBAChCqJ,EAAetI,EAASsE,SAAWtE,EAASuE,YAChD,OAAI1G,KAAUzF,EACHiQ,GAEX1M,EAAKoE,gBAAiB,EACtBpE,EAAK0B,mBAAmB1B,EAAKH,QAASqC,GACtCA,EAAQlC,EAAK4M,iBAAiB1K,GACL,OAArBoB,GAA6BpB,EAAMrF,OAASyG,IAC5CpB,EAAQA,EAAMkE,MAAM,EAAG9C,IAEvBqJ,GACA3M,EAAK6I,eAETxE,EAASnC,MAAMA,GACflC,EAAK6M,KAAO7M,EAAK8M,oBAAsB5K,EAAMkE,QACxCuG,GACD3M,EAAK+M,aAET/M,EAAKuC,aAAavC,EAAKkC,QAAQrF,QAC/BmD,EAAKyC,yBAfLzC,IAiBJmC,WAAY,SAAU3E,EAAM0E,GACxB,GAAIlC,GAAOC,IACNT,GAAQhC,IAAWA,YAAgBV,GAAMU,KAAKD,kBAC/CC,GAAQA,KAERhB,EAAEiP,cAAcjO,EAAK,KAAOA,EAAK,YAAcV,GAAMU,KAAKwP,mBAAqBhN,EAAKH,QAAQmD,kBAC5FhD,EAAKgC,WAAWxE,KAAKA,GACrBwC,EAAKkC,MAAMA,GAASlC,EAAKmG,gBACzBnG,EAAKmL,eAAgB,IAG7B8B,WAAY,SAAU/K,EAAO2H,GACzB,GAAIX,GAASjJ,KAAKL,QAAQ,GAAGwH,SAASnH,KAAKG,YAAY8B,GACnDgH,KACAA,EAAOW,SAAWA,IAG1BkD,WAAY,WAAA,GACJ/M,GAAOC,KACPiN,IAAalN,EAAKgC,WAAWmL,OAAOtQ,OACpCuQ,EAAgD,IAAjCpN,EAAKqE,SAASnC,QAAQrF,MACrCuQ,IAAgBpN,EAAKqL,WAGrBrL,EAAKmL,gBAAkBnL,EAAKqN,SAAWH,KACvClN,EAAKqN,QAAS,EACdrN,EAAKmL,eAAgB,EACrBnL,EAAKgC,WAAWsL,OAAOlE,KAAK,WACxBpJ,EAAKqN,QAAS,MAI1BE,SAAU,WACN,MAAOtN,MAAKoE,SAASsE,UAAY1I,KAAKkL,eAE1C7J,YAAa,WACT,GAAItB,GAAOC,KAAML,EAAUI,EAAKJ,QAASC,EAAUG,EAAKH,QAASmC,EAAanC,EAAQmC,cACtFA,GAAaxC,EAAQwC,IAAgBxE,KAAMwE,GAAeA,EAC1DA,EAAW4H,OAAShK,EACpBoC,EAAWwL,SACLC,MAAO5N,EAAQkD,gBACf0K,MAAO5N,EAAQmD,iBAEjBhD,EAAKgC,YAAchC,EAAK0N,gBACxB1N,EAAK2N,qBAEL3N,EAAK4N,iBAAmBnQ,EAAMuC,EAAK6N,UAAW7N,GAC9CA,EAAK8N,cAAgBrQ,EAAMuC,EAAKsM,UAAWtM,IAE/CA,EAAKgC,WAAalF,EAAMU,KAAKuQ,WAAWC,OAAOhM,GAAYiM,KAAK/P,EAAU8B,EAAK4N,kBAAkBK,KAAK,QAASjO,EAAK8N,gBAExHlM,OAAQ,WACJ,GAAI5B,GAAOC,KAAML,EAAUI,EAAKJ,QAASsO,EAAStO,EAAQkB,KAAK,QAASqN,EAAOD,EAAS1R,EAAE,IAAM0R,GAAUtO,EAAQqI,QAAQ,OACtHkG,GAAK,KACLnO,EAAKoO,cAAgB,WACjBC,WAAW,WACPrO,EAAKkC,MAAMlC,EAAKmG,gBAChBnG,EAAK8B,kBAGb9B,EAAKsO,MAAQH,EAAKrD,GAAG,QAAS9K,EAAKoO,iBAG3CG,WAAY,WACR,GAAIrM,GAAQjC,KAAKJ,QAAQqC,OAASjC,KAAKL,QAAQ2K,KAC/CtK,MAAK4M,KAAO5M,KAAKkG,eAAiBlG,KAAK2M,iBAAiB1K,IAE5D0K,iBAAkB,SAAU1K,GACxB,GAAIlC,GAAOC,IAcX,OAbc,QAAViC,EACAA,KACOA,GAAS1F,EAAEiP,cAAcvJ,GAChCA,GAASlC,EAAK2G,OAAOzE,IACdA,GAAS1F,EAAEiP,cAAcvJ,EAAM,IACtCA,EAAQ1F,EAAEgS,IAAItM,EAAO,SAAU2E,GAC3B,MAAO7G,GAAK2G,OAAOE,KAEfrH,EAAQ0C,IAAYA,YAAiB3E,GAEtCiC,EAAQ0C,KACfA,EAAQA,EAAMkE,SAFdlE,GAASA,GAINA,GAEXyH,QAAS,WACL,GAAI3J,GAAOC,KAAMiC,EAAQlC,EAAKkC,OACzBxF,GAAQwF,EAAOlC,EAAK6M,QACrB7M,EAAK6M,KAAO3K,EAAMkE,QAClBpG,EAAKyJ,QAAQxL,GACb+B,EAAKJ,QAAQ6J,QAAQxL,IAEzB+B,EAAKgE,MAAMsF,WACXtJ,EAAKuC,aAAaL,EAAMrF,QACxBmD,EAAKyC,0BAETgM,OAAQ,SAAUnI,GAAV,GACAtG,GAAOC,KACPwH,EAAOnB,EAAEmB,IACbnB,GAAE8B,iBACFpI,EAAK0O,QAAQjH,GAAM2B,KAAK,WACpBpJ,EAAKoL,YAAc3D,EACnBzH,EAAK2J,UACL3J,EAAK0J,YAGbiF,eAAgB,WACZ,MAAO1O,MAAKmL,aAAe5O,EAAEyD,KAAKoE,SAAS8C,QAAQlH,KAAK2O,sBAAsB/R,OAAS,KAAOoD,KAAKoE,SAASgE,SAEhHuG,oBAAqB,WACjB,MAAO3O,MAAKoE,SAASwK,kBAAoB5O,KAAKoE,SAASyK,kBAE3D9D,SAAU,SAAU1E,GAAV,GASFyI,GAiGIC,EACAC,EA+DAC,EAzKJlP,EAAOC,KACPkP,EAAM7I,EAAE8I,QACRpG,EAAMhJ,EAAK6E,YACXR,EAAWrE,EAAKqE,SAChBgL,EAAWrP,EAAKa,MAAM0J,MACtB+E,EAAQxS,EAAMgC,QAAQwQ,MAAMtP,EAAKwF,SACjCI,EAAU5F,EAAKgE,MAAM4B,UACrB2J,EAAM,CAKV,IAHIJ,IAAQjS,EAAKsS,QACbvP,KAAKwP,oBAAqB,GAE1BN,IAAQjS,EAAKwS,KAAM,CAEnB,GADApJ,EAAE8B,kBACGxC,EAKD,MAJA5F,GAAKsI,OACAjE,EAASgE,SACVhE,EAAS8H,aAEb,CAEA9H,GAASgE,UACJrI,EAAKoL,aAAe9E,EAAEqJ,WACvB3P,EAAKoL,YAAc/G,EAASgE,QAC5BkH,MAEJR,EAAgB1K,EAASuL,gBAAgB5P,EAAK2O,iBAAiBkB,SAC/DxL,EAASyL,YACJzL,EAASgE,QAGN/B,EAAEqJ,WACF1P,KAAKwP,oBAAqB,EAC1BzP,EAAK+P,aAAahB,EAAe1K,EAASuL,gBAAgBvL,EAASgE,QAAQwH,SAAWN,IAJ1FlL,EAAS2L,aAQb3L,EAAS8H,iBAEV,IAAIgD,IAAQjS,EAAK+S,GAChBrK,KACK5F,EAAKoL,aAAe9E,EAAEqJ,WACvB3P,EAAKoL,YAAc/G,EAASgE,QAC5BkH,EAAM,GAEVR,EAAgB1K,EAASuL,gBAAgB5P,EAAK2O,iBAAiBkB,SAC/DxL,EAAS6L,YACJ7L,EAASgE,QAGN/B,EAAEqJ,WACF1P,KAAKwP,oBAAqB,EAC1BzP,EAAK+P,aAAahB,EAAe1K,EAASuL,gBAAgBvL,EAASgE,QAAQwH,SAAWN,IAJ1FvP,EAAKyI,SAQbnC,EAAE8B,qBACC,IAAI+G,IAAQjS,EAAKiT,OAASb,GAASH,IAAQjS,EAAKkT,OAASd,EACvDD,IACDrG,EAAMA,EAAMA,EAAIqH,OAAS7T,EAAEwD,EAAKkB,QAAQ,GAAGoP,WACvCtH,EAAI,IACJhJ,EAAK2E,WAAWqE,QAGrB,IAAImG,IAAQjS,EAAKkT,QAAUd,GAASH,IAAQjS,EAAKiT,MAAQb,GACvDD,GAAYrG,IACbA,EAAMA,EAAIuH,OACVvQ,EAAK2E,WAAWqE,EAAI,GAAKA,EAAM,WAEhC,IAAI1C,EAAEkK,UAAYlK,EAAEmK,QAAUtB,IAAQjS,EAAKE,GAAKwI,IAAY5F,EAAKH,QAAQ4D,QAC5ExD,KAAKwP,oBAAqB,EACtBxP,KAAK2O,sBAAsB/R,SAAWwH,EAAS8C,QAAQtK,SACvDmD,EAAKoL,YAAc,MAEnB/G,EAAS8C,QAAQtK,QACjBmD,EAAK+P,aAAa,EAAG1L,EAAS8C,QAAQtK,OAAS,OAEhD,IAAIsS,IAAQjS,EAAKsS,OAAS5J,EAAS,CACtC,IAAKvB,EAASgE,QACV,MAGJ,IADA/B,EAAE8B,iBACEnI,KAAKwP,qBACLxP,KAAKwP,oBAAqB,EACtBpL,EAASgE,QAAQL,SAASzJ,IAE1B,MADAyB,GAAK0J,SACL,CAGR1J,GAAK0O,QAAQrK,EAASgE,SAASe,KAAK,WAChCpJ,EAAK2J,UACL3J,EAAK0J,eAEN,IAAIyF,IAAQjS,EAAKwT,UAAYpK,EAAEkK,SAAW5K,EACzC5F,EAAKoL,aAAe/G,EAASgE,SAAWhE,EAASgE,QAAQ,KAAOrI,EAAKoL,YAAY,KACjFpL,EAAKoL,YAAc,MAElB5O,EAAE6H,EAASgE,SAASL,SAASzJ,KAC9ByB,EAAKoL,YAAc/G,EAASgE,SAEhCrI,EAAK0O,QAAQrK,EAASgE,SAASe,KAAK,WAChCpJ,EAAK2J,YAETrD,EAAE8B,qBACC,IAAI+G,IAAQjS,EAAKwT,UAAYpK,EAAEqJ,UAAY/J,EAC1CoJ,EAAc3K,EAASuL,gBAAgB5P,EAAK2O,kBAC5CM,EAAe5K,EAASuL,gBAAgBvL,EAASgE,SACjD2G,IAAgBvS,GAAawS,IAAiBxS,GAC9CuD,EAAK+P,aAAaf,EAAaC,GAEnC3I,EAAE8B,qBACC,IAAI+G,IAAQjS,EAAKyT,IAChB/K,EACAU,EAAE8B,kBAEFpI,EAAKkB,QAAQkG,WAAWkD,KAAK,SAAUhD,EAAO0B,GAC1ChJ,EAAK+I,WAAWvM,EAAEwM,IAAM,KAE5BhJ,EAAK2J,WAET3J,EAAKyI,YACF,IAAI0G,IAAQjS,EAAK0T,KAChBhL,EACKvB,EAASgE,SAGN/B,EAAEkK,SAAWlK,EAAEqJ,WAAa3P,EAAKH,QAAQ4D,SACzCzD,EAAK+P,aAAa1L,EAASuL,gBAAgBvL,EAASgE,QAAQ,IAAK,GAErEhE,EAAS8H,cALTnM,EAAKyI,QAOD4G,IACRrG,EAAMhJ,EAAKkB,QAAQ,GAAG2P,WAClB7H,GACAhJ,EAAK2E,WAAWnI,EAAEwM,SAGvB,IAAImG,IAAQjS,EAAK4T,IAChBlL,EACKvB,EAASgE,SAGN/B,EAAEkK,SAAWlK,EAAEqJ,WAAa3P,EAAKH,QAAQ4D,SACzCzD,EAAK+P,aAAa1L,EAASuL,gBAAgBvL,EAASgE,QAAQ,IAAKhE,EAASzE,QAAQwH,WAAWvK,OAAS,GAE1GwH,EAAS2L,aALThQ,EAAKyI,QAOD4G,IACRrG,EAAMhJ,EAAKkB,QAAQ,GAAGoP,UAClBtH,GACAhJ,EAAK2E,WAAWnI,EAAEwM,SAGvB,IAAKmG,IAAQjS,EAAK6T,QAAU5B,IAAQjS,EAAK8T,WAAe3B,GAcpDrP,EAAKgE,MAAM4B,WAAcuJ,IAAQjS,EAAK+T,UAAY9B,IAAQjS,EAAKgU,QAKtE7L,aAAarF,EAAKuF,gBAClB8I,WAAW,WACPrO,EAAKmR,WAETnR,EAAKwK,YARLlE,EAAE8B,iBACE8G,EAAYC,IAAQjS,EAAK+T,SAAW,KACxC5M,EAAS+M,WAAWlC,EAAY7K,EAASgN,qBAjB4B,CAErE,GADArR,EAAKmE,OAASvG,EACe,WAAzBoC,EAAKH,QAAQ8C,QAIb,MAHA3C,GAAKqK,uBACLrK,EAAK2J,UACL3J,EAAK0J,SACL,CAEAyF,KAAQjS,EAAK8T,WAAchI,IAC3BA,EAAMxM,EAAEwD,EAAKkB,QAAQ,GAAGoP,YAExBtH,GAAOA,EAAI,IACXhJ,EAAK+I,WAAWC,GAAK,KAcjCsD,UAAW,WACP,GAAItM,GAAOC,IACXoF,cAAarF,EAAKsF,OAClBtF,EAAKa,MAAMC,KAAK,aAAa,GAC7Bd,EAAKsR,SAASrM,SAASzG,GACvBwB,EAAKqL,UAAW,EAChBrL,EAAKsF,MAAQ,KACbtF,EAAKyC,0BAET8O,iBAAkB,WACdtR,KAAKY,MAAMC,KAAK,aAAa,GAC7Bb,KAAKqR,SAASxM,YAAYtG,GAC1ByB,KAAKwK,cAEToD,UAAW,WACP,GAAI7N,GAAOC,IACXD,GAAKqL,UAAW,EACZrL,EAAKsF,QAGTtF,EAAKsF,MAAQ+I,WAAW5Q,EAAMuC,EAAKuR,iBAAkBvR,GAAO,OAEhE8B,aAAc,SAAU0P,EAAMC,GAAhB,GACNzR,GAAOC,KACPY,EAAQb,EAAKa,MACb6Q,EAASrU,IACT2D,EAAchB,EAAKH,QAAQmB,YAC3ByL,EAAa5L,EAAM0J,MACnBoH,EAAW9Q,EAAM,KAAO6Q,EACxBE,EAAWnF,EAAW5P,MACrB8U,KAAY3R,EAAKH,QAAQgD,WAAa4J,IAAezL,IACtD4Q,EAAW,EACXnF,EAAa,IAEb+E,IAAS/U,IACT+U,GAAO,EACH3Q,EAAM,KAAO6Q,IACbF,GAAQxR,EAAKqE,SAASc,oBAAoB,KAGlDnF,EAAK6R,MAAQpF,EACb5L,EAAMiR,YAAY,aAAcN,GAAMjH,IAAIiH,EAAOxQ,EAAcyL,GAC3DkF,IAAaF,GACb3U,EAAMiV,MAAMlR,EAAM,GAAI+Q,EAAUA,GAEpC5R,EAAKmR,UAETA,OAAQ,WACJ,GAA+Ia,GAA3IhS,EAAOC,KAAMuF,EAAUxF,EAAKwF,QAAQR,KAAK,uBAAwBiN,EAAezM,EAAQ0M,QAASC,EAAOnS,EAAKoS,MAAMC,KAAKrS,EAAKa,MAAM0J,MAClI/E,GAAQnD,GAAG,YAKZ2P,EAAYG,EAAKD,QAAU,IAJ3BC,EAAKG,SAASC,SAASC,iBACvBP,EAAeD,EAAYG,EAAKD,QAAU,GAC1CC,EAAKG,SAAS9M,IAIlBxF,EAAKa,MAAMqR,MAAMF,EAAYC,EAAeA,EAAeD,IAE/DS,QAAS,SAAUC,EAAWC,EAAU9I,GACpC,GAAIX,GAAS,SAeb,OAdIwJ,KAAcjW,IACdiW,GAAa,GACTA,EAAUE,QAAQ,YAClBF,EAAYA,EAAUG,QAAQtT,EAAY,WAE9C2J,GAAU,WAAawJ,EAAY,KAEnC7I,IACAX,GAAU,aAEdA,GAAU,IACNyJ,IAAalW,IACbyM,GAAUpM,EAAMgW,WAAWH,IAExBzJ,GAAU,aAErBpC,QAAS,SAAUtJ,GAAV,GAIDuV,GAEAlM,EACA3E,EACAgF,EAIA8L,EACAvM,EAZAwM,EAAgBhT,KAAKoE,SAASc,oBAC9B+N,EAASjT,KAAKoE,SAASnC,QACvBrF,EAASW,EAAKX,OAEdgD,EAAU,EASd,KALIqT,EAAOrW,SAAWoW,EAAcpW,SAChCoW,EAAgBhT,KAAKkT,oBAAoBD,IAEzCF,KACAvM,KACCS,EAAM,EAAGA,EAAMrK,EAAQqK,IACxBL,EAAWrJ,EAAK0J,GAChBhF,EAAQjC,KAAK0G,OAAOE,GACpBkM,EAAgB9S,KAAKmT,mBAAmBlR,EAAO+Q,GAC3CF,QACAE,EAAcI,OAAON,EAAe,GAExCtM,EAAWvE,GAASgF,EACpBrH,GAAWI,KAAKwS,QAAQvQ,EAAOjC,KAAKqT,MAAMzM,GAAWkM,OAEzD,IAAIE,EAAcpW,OACd,IAAKqK,EAAM,EAAGA,EAAM+L,EAAcpW,OAAQqK,IACtCL,EAAWoM,EAAc/L,GACzBhF,EAAQjC,KAAK0G,OAAOE,GACpBmM,EAAO9Q,GAASrF,EAChB4J,EAAWvE,GAASrF,EACpBA,GAAU,EACVgD,GAAWI,KAAKwS,QAAQvQ,EAAOjC,KAAKqT,MAAMzM,IAAW,EAG7D5G,MAAKI,eAAiB2S,EACtB/S,KAAKG,YAAcqG,EACnBxG,KAAKL,QAAQ2H,KAAK1H,IAEtBsT,oBAAqB,SAAUD,GAAV,GAIbzL,GACKP,EAJLqM,EAAatT,KAAKJ,QAAQmD,eAC1BwQ,EAAYvT,KAAKJ,QAAQkD,cACzB0Q,IAEJ,KAASvM,EAAM,EAAGA,EAAMgM,EAAOrW,OAAQqK,IACnCO,KACAA,EAAK8L,GAAcL,EAAOhM,GAC1BO,EAAK+L,GAAaN,EAAOhM,GACzBuM,EAAOC,KAAKjM,EAEhB,OAAOgM,IAEXL,mBAAoB,SAAUlR,EAAO+Q,GAGjC,IAHgB,GACZvM,GAAczG,KAAK0G,OACnBO,EAAM,EACHA,EAAM+L,EAAcpW,OAAQqK,IAC/B,GAAIhF,IAAUwE,EAAYuM,EAAc/L,IACpC,MAAOA,EAGf,WAEJsD,QAAS,WACL,GAAIxK,GAAOC,IACXoF,cAAarF,EAAKuF,gBAClBvF,EAAKuF,eAAiB8I,WAAW,WAC7B,GAAInM,GAAQlC,EAAKwM,aACbxM,GAAK6R,QAAU3P,IACflC,EAAK6R,MAAQ3P,EACblC,EAAK2T,OAAOzR,GACZlC,EAAKyC,2BAEVzC,EAAKH,QAAQwD,QAEpBZ,uBAAwB,WAChBxC,KAAKiC,QAAQrF,QAAUoD,KAAKY,MAAM0J,OAAStK,KAAKY,MAAM0J,QAAUtK,KAAKJ,QAAQmB,YAC7Ef,KAAK2T,aAEL3T,KAAKwK,cAGbe,cAAe,WACX,MAAOvL,MAAK4T,mBAAqB5W,EAAKiD,GAAGsL,cAAcrL,KAAKF,OAEhE4T,gBAAiB,WACb,GAAIC,GAAM7T,KAAKJ,QAAQyD,gBACvB,OAAe,QAARwQ,GAAgBA,EAAM7T,KAAKoE,SAASnC,QAAQrF,QAEvDkX,iBAAkB,SAAUC,GACxB,GAAIhU,GAAOC,IACXD,GAAKiU,QAAQD,EAAK,WACd,OACIE,SAAUlU,EAAKkB,QAAQ,GAAGkG,SAC1B5J,KAAMhB,EAAEgS,IAAIxO,EAAKkF,YAAa,SAAU2B,GACpC,OAASA,SAAUA,SAKnCsN,qBAAsB,SAAUvN,EAAOI,GAC/B/G,KAAKmE,eAAewC,OAAS3G,KAAKmE,eAAewC,MAAM/J,SAAWmK,EAAQnK,QAAUoD,KAAKmE,eAAe4C,SAAW/G,KAAKmE,eAAe4C,QAAQnK,SAAW+J,EAAM/J,OAChKoD,KAAKmE,gBAAiB,GAEtBnE,KAAKoE,SAAS+P,qBAAuBnU,KAAK4M,KAAKzG,QAC/CnG,KAAKmE,gBACDwC,MAAOA,EACPI,QAASA,KAIrBD,aAAc,SAAUH,EAAOI,GAAjB,GAKNqN,GACAC,EACApN,EANAlH,EAAOC,KACPsU,EAAQvU,EAAKgC,WAAWuS,QACxBrT,EAAUlB,EAAKkB,QACfsT,EAASxU,EAAK2G,MAIlB,IAAI1G,KAAKmE,eAEL,MADAnE,MAAKkU,qBAAqBvN,EAAOI,GACjC,CAGJ,IADAhH,EAAK+T,iBAAiB,WACO,aAAzB/T,EAAKH,QAAQ8C,QAAwB,CACrC,IAAKuE,EAAMF,EAAQnK,OAAS,EAAGqK,KAAUA,IACrCmN,EAAcrN,EAAQE,GAClBhG,EAAQkG,WAAWvK,SACnBqE,EAAQ,GAAGuT,YAAYvT,EAAQ,GAAGkG,SAASiN,EAAY/K,WACvDtJ,EAAKiN,WAAWuH,EAAOH,EAAYxN,WAAW,GAGtD,KAAKK,EAAM,EAAGA,EAAMN,EAAM/J,OAAQqK,IAC9BoN,EAAY1N,EAAMM,GAClBhG,EAAQwT,OAAO1U,EAAK2D,YAAY2Q,EAAUzN,WAC1C7G,EAAKiN,WAAWuH,EAAOF,EAAUzN,WAAW,OAE7C,CAKH,MAJK7G,EAAK2U,WAAa3U,EAAK2U,UAAYJ,KACpCvU,EAAK2U,UAAYJ,GAErBtU,KAAKgK,qBACA/C,EAAMF,EAAQnK,OAAS,EAAGqK,KAAUA,IACrClH,EAAKiN,WAAWuH,EAAOxN,EAAQE,GAAKL,WAAW,EAEnD,KAAKK,EAAM,EAAGA,EAAMN,EAAM/J,OAAQqK,IAC9BlH,EAAKiN,WAAWuH,EAAO5N,EAAMM,GAAKL,WAAW,GAGrD7G,EAAK+T,iBAAiB,WACtB/T,EAAK8B,gBAETmI,mBAAoB,WAAA,GACZjK,GAAOC,KACPiT,EAASlT,EAAKkC,QACdqS,EAAQvU,EAAKgC,WAAWuS,QACxBrT,EAAUlB,EAAKkB,OACnBA,GAAQqG,KAAK,IACT2L,EAAOrW,QACPqE,EAAQwT,OAAO1U,EAAK2D,aAChBuP,OAAQA,EACRhO,UAAWlF,EAAKkF,YAChB0P,SAAU5U,EAAK2U,UACfE,aAAcN,MAI1B7F,QAAS,SAAU9J,GAAV,GAKD5E,GACAqE,EACAwC,EACAiO,EAPAC,EAAWvY,EAAEwY,WAAWC,SAC5B,OAAKrQ,IAGD5E,EAAOC,KACPoE,EAAWrE,EAAKqE,SAChBwC,EAAWxC,EAAS6Q,gBAAgB7Q,EAASuL,gBAAgBhL,IAC7DkQ,EAAalQ,EAAUoD,SAAS,oBAChChI,EAAKmE,SAAWrG,IAChBkC,EAAKmE,OAAS,IAEbnE,EAAK6T,mBAAsBiB,EAG5B9U,EAAKyJ,QAAQqL,EAAa1W,EAAWD,GACjC0I,SAAUA,EACVY,KAAM7C,KAEV5E,EAAK0J,SACEqL,IAEX/U,EAAKoE,gBAAiB,EACfC,EAASuF,OAAOhF,GAAWwE,KAAK,WACnCpJ,EAAK8B,eACD9B,EAAKmE,SAAWtG,IAChBmC,EAAKmE,OAASvG,EACdyG,EAASqE,YAAW,OAdjBqM,GAVAA,GA4BfhF,aAAc,SAAUoF,EAAYC,GAAtB,GAMN7O,GA+BIe,EApCJtH,EAAOC,KACPoE,EAAWpE,KAAKoE,SAChBf,EAAmBrD,KAAKJ,QAAQyD,iBAChC+R,EAAUpV,KAAK2O,sBAAsBxI,QACrCkP,KAEAC,EAAgB,SAAUF,GAC1BhR,EAASuF,OAAOyL,GAASjM,KAAK,WAC1BiM,EAAQG,QAAQ,SAAUlO,GAAV,GACRT,GAAWxC,EAAS6Q,gBAAgB5N,GACpC1C,EAAYP,EAASzE,QAAQwH,WAAWE,GACxCwN,EAAatY,EAAEoI,GAAWoD,SAAS,mBACvChI,GAAKyJ,QAAQqL,EAAa3W,EAASC,GAC/ByI,SAAUA,EACVY,KAAMjL,EAAEoI,OAGhB5E,EAAK2J,YAGb,IAAI0L,EAAQxY,OAAS,IAAMuY,EAAWD,EAClC,MAAOI,GAAcF,EAEzB,IAAIF,EAAaC,EACb,IAAK7O,EAAI4O,EAAY5O,GAAK6O,EAAU7O,IAChC+O,EAAgB5B,KAAKnN,OAGzB,KAAKA,EAAI4O,EAAY5O,GAAK6O,EAAU7O,IAChC+O,EAAgB5B,KAAKnN,EAM7B,KAHyB,OAArBjD,GAA6BgS,EAAgBzY,OAASyG,IACtDgS,EAAkBA,EAAgBlP,MAAM,EAAG9C,IAE1CiD,EAAI,EAAGA,EAAI+O,EAAgBzY,OAAQ0J,IAChCe,EAAQgO,EAAgB/O,GACxBtG,KAAK2O,sBAAsBgE,QAAQtL,OACnC+N,EAAQ3B,KAAKpM,GAEb+N,EAAQhC,OAAOgC,EAAQzC,QAAQtL,GAAQ,EAG/C,OAAK+N,GAAQxY,QAGbmD,EAAKoE,gBAAiB,EACfmR,EAAcF,IAJrB,GAMJ7U,OAAQ,WAAA,GACAR,GAAOC,KACPL,EAAUI,EAAKJ,QACf6V,EAAY7V,EAAQ,GAAG6V,UACvB5U,EAAQb,EAAK0V,cAActO,SAAS,gBACnCvG,GAAM,KACPA,EAAQrE,EAAE,iDAAiD8V,SAAStS,EAAK0V,gBAE7E9V,EAAQmF,WAAW,aACnB/E,EAAK2V,SAAW3V,EAAKa,MAAQA,EAAMC,MAC/B8U,UAAaH,EACbI,aAAgBhX,EAChBiX,KAAQ,UACRC,MAASnW,EAAQ,GAAGmW,MACpBC,iBAAiB,EACjBC,gBAAiB,UACjBC,oBAAqB,UAG7B3V,SAAU,WACN,GAAIP,GAAOC,KAAMiB,EAAUlB,EAAK0V,cAActO,SAAS,KAClDlG,GAAQ,KACTA,EAAU1E,EAAE,2CAA2C8V,SAAStS,EAAK0V,gBAEzE1V,EAAKkB,QAAUA,GAEnBO,aAAc,WAAA,GAMN0U,GALAnW,EAAOC,KACPJ,EAAUG,EAAKH,QACf8D,EAAc9D,EAAQ8D,YACtByS,EAAgBvW,EAAQmC,WACxBqU,EAAiC,aAApBxW,EAAQ8C,OAErB3C,GAAKJ,QAAQ,GAAG/C,SAAWuZ,IAC3BvW,EAAQkD,cAAgBlD,EAAQkD,eAAiB,OACjDlD,EAAQmD,eAAiBnD,EAAQmD,gBAAkB,SAEvDmT,EAAkBE,EAAavZ,EAAMkJ,SAAS,KAAOlJ,EAAMmJ,KAAKpG,EAAQkD,cAAe,QAAU,KAAOuT,cAAc,IAAWxZ,EAAMkJ,SAAS,qCAChJhG,EAAKwH,gBAAkB7D,EAAcA,EAAc7G,EAAMkJ,SAASrC,GAAewS,EACjFnW,EAAK2D,YAAc,SAAUnG,GACzB,MAAO,qGAAuGmG,EAAYnG,GAAQ,kEAAoE6Y,EAAa,SAAW,QAAU,2CAA6CA,EAAa,YAAc,qBAAuB,0BAG/U3V,QAAS,WACLT,KAAKqR,SAAW9U,EAAE,mCAAqCgC,EAAc,aAAa+X,YAAYtW,KAAKY,QAEvGF,aAAc,WACV1D,EAAKiD,GAAGS,aAAaR,KAAKF,MACtBA,KAAKJ,QAAQiE,cACb7D,KAAKyF,OAAO6Q,YAAYtW,KAAKY,OAC7BZ,KAAKuF,QAAQP,SAAS,6BAG9BxE,eAAgB,WACZ,GAAI+V,GAAiB1Z,EAAM2Z,kBAAkBxW,KAAKY,MAAM,GAAIpB,EAC5D+W,GAAelN,SAAW,WAC1BkN,EAAeE,WAAa,SAC5BF,EAAeG,UACfH,EAAeI,WACf3W,KAAKmS,MAAQ5V,EAAE,WAAWqa,IAAIL,GAAgBlE,SAASrS,KAAKuF,UAEhElF,SAAU,WACN,GAAIN,GAAOC,KAAML,EAAUI,EAAKJ,QAAS4F,EAAU5F,EAAQkX,OAAO,qBAC7DtR,GAAQ,KACTA,EAAU5F,EAAQmX,KAAK,4DAA4DD,SACnFtR,EAAQ,GAAGwR,MAAMC,QAAUrX,EAAQ,GAAGoX,MAAMC,QAC5CzR,EAAQ,GAAGuQ,MAAQnW,EAAQ,GAAGmW,MAC9BvZ,EAAE,mFAAmF0a,aAAatX,IAEtGI,EAAKwF,QAAUA,EAAQP,SAASrF,EAAQ,GAAGuX,WAAWN,IAAI,UAAW,IACrE7W,EAAK0V,cAAgBlZ,EAAEgJ,EAAQ,GAAGqL,aAEtCtO,aAAc,SAAUL,GAAV,GACNlC,GAAOC,KACPgT,EAAgBjT,EAAKkB,QAAQkG,UAC7BlF,IAAS+Q,EAAcpW,QACvBoW,EAAcnS,KAAK,eAAgBoB,IAG3Cb,aAAc,WACV,GAAIrB,GAAOC,IACXD,GAAKoX,GAAGtW,KAAK,YAAcd,EAAKqX,mBAA6B,SAAR,SAmB7Dra,GAAGsa,OAAO5X,IACZ3C,OAAOD,MAAMya,QACRxa,OAAOD,OACE,kBAAVP,SAAwBA,OAAOib,IAAMjb,OAAS,SAAUkb,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.multiselect.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.multiselect', [\n        'kendo.list',\n        'kendo.mobile.scroller',\n        'kendo.virtuallist'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'multiselect',\n        name: 'MultiSelect',\n        category: 'web',\n        description: 'The MultiSelect widget allows the selection from pre-defined values.',\n        depends: ['list'],\n        features: [\n            {\n                id: 'mobile-scroller',\n                name: 'Mobile scroller',\n                description: 'Support for kinetic scrolling in mobile device',\n                depends: ['mobile.scroller']\n            },\n            {\n                id: 'virtualization',\n                name: 'VirtualList',\n                description: 'Support for virtualization',\n                depends: ['virtuallist']\n            }\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, List = ui.List, keys = $.extend({ A: 65 }, kendo.keys), activeElement = kendo._activeElement, ObservableArray = kendo.data.ObservableArray, proxy = $.proxy, ID = 'id', LI = 'li', ACCEPT = 'accept', FILTER = 'filter', REBIND = 'rebind', OPEN = 'open', CLOSE = 'close', CHANGE = 'change', PROGRESS = 'progress', SELECT = 'select', DESELECT = 'deselect', ARIA_DISABLED = 'aria-disabled', FOCUSEDCLASS = 'k-state-focused', SELECTEDCLASS = 'k-state-selected', HIDDENCLASS = 'k-hidden', HOVERCLASS = 'k-state-hover', STATEDISABLED = 'k-state-disabled', DISABLED = 'disabled', READONLY = 'readonly', AUTOCOMPLETEVALUE = kendo.support.browser.chrome ? 'disabled' : 'off', ns = '.kendoMultiSelect', CLICK = 'click' + ns, KEYDOWN = 'keydown' + ns, MOUSEENTER = 'mouseenter' + ns, MOUSELEAVE = 'mouseleave' + ns, HOVEREVENTS = MOUSEENTER + ' ' + MOUSELEAVE, quotRegExp = /\"/g, isArray = $.isArray, styles = [\n                'font-family',\n                'font-size',\n                'font-stretch',\n                'font-style',\n                'font-weight',\n                'letter-spacing',\n                'text-transform',\n                'line-height'\n            ];\n        var MultiSelect = List.extend({\n            init: function (element, options) {\n                var that = this, id, disabled;\n                that.ns = ns;\n                List.fn.init.call(that, element, options);\n                that._optionsMap = {};\n                that._customOptions = {};\n                that._wrapper();\n                that._tagList();\n                that._input();\n                that._textContainer();\n                that._loader();\n                that._clearButton();\n                that._tabindex(that.input);\n                element = that.element.attr('multiple', 'multiple').hide();\n                options = that.options;\n                if (!options.placeholder) {\n                    options.placeholder = element.data('placeholder');\n                }\n                id = element.attr(ID);\n                if (id) {\n                    that._tagID = id + '_tag_active';\n                    id = id + '_taglist';\n                    that.tagList.attr(ID, id);\n                    that.input.attr('aria-describedby', id);\n                }\n                that._initialOpen = true;\n                that._ariaLabel();\n                that._ariaSetLive();\n                that._dataSource();\n                that._ignoreCase();\n                that._popup();\n                that._tagTemplate();\n                that.requireValueMapper(that.options);\n                that._initList();\n                that._reset();\n                that._enable();\n                that._placeholder();\n                if (options.autoBind) {\n                    that.dataSource.fetch();\n                } else if (options.value) {\n                    that._preselect(options.value);\n                }\n                disabled = $(that.element).parents('fieldset').is(':disabled');\n                if (disabled) {\n                    that.enable(false);\n                }\n                that._ariaSetSize(that.value().length);\n                kendo.notify(that);\n                that._toggleCloseVisibility();\n            },\n            options: {\n                name: 'MultiSelect',\n                tagMode: 'multiple',\n                enabled: true,\n                autoBind: true,\n                autoClose: true,\n                highlightFirst: true,\n                dataTextField: '',\n                dataValueField: '',\n                filter: 'startswith',\n                ignoreCase: true,\n                minLength: 1,\n                enforceMinLength: false,\n                delay: 100,\n                value: null,\n                maxSelectedItems: null,\n                placeholder: '',\n                height: 200,\n                animation: {},\n                virtual: false,\n                itemTemplate: '',\n                tagTemplate: '',\n                groupTemplate: '#:data#',\n                fixedGroupTemplate: '#:data#',\n                clearButton: true,\n                autoWidth: false,\n                popup: null\n            },\n            events: [\n                OPEN,\n                CLOSE,\n                CHANGE,\n                SELECT,\n                DESELECT,\n                'filtering',\n                'dataBinding',\n                'dataBound'\n            ],\n            setDataSource: function (dataSource) {\n                this.options.dataSource = dataSource;\n                this._state = '';\n                this._dataSource();\n                this.persistTagList = false;\n                this.listView.setDataSource(this.dataSource);\n                if (this.options.autoBind) {\n                    this.dataSource.fetch();\n                }\n            },\n            setOptions: function (options) {\n                var listOptions = this._listOptions(options);\n                List.fn.setOptions.call(this, options);\n                this.listView.setOptions(listOptions);\n                this._accessors();\n                this._aria(this.tagList.attr(ID));\n                this._tagTemplate();\n                this._placeholder();\n                this._clearButton();\n            },\n            currentTag: function (candidate) {\n                var that = this;\n                if (candidate !== undefined) {\n                    if (that._currentTag) {\n                        that._currentTag.removeClass(FOCUSEDCLASS).removeAttr(ID);\n                        that._currentTag.find('.k-select').attr('aria-hidden', true);\n                        that.input.removeAttr('aria-activedescendant');\n                    }\n                    if (candidate) {\n                        candidate.addClass(FOCUSEDCLASS).attr(ID, that._tagID);\n                        candidate.find('.k-select').removeAttr('aria-hidden');\n                        that.input.attr('aria-activedescendant', that._tagID);\n                    }\n                    that._currentTag = candidate;\n                } else {\n                    return that._currentTag;\n                }\n            },\n            dataItems: function () {\n                return this.listView.selectedDataItems();\n            },\n            destroy: function () {\n                var that = this, ns = that.ns;\n                clearTimeout(that._busy);\n                clearTimeout(that._typingTimeout);\n                that.wrapper.off(ns);\n                that.tagList.off(ns);\n                that.input.off(ns);\n                that._clear.off(ns);\n                List.fn.destroy.call(that);\n            },\n            _activateItem: function () {\n                if (this.popup.visible()) {\n                    List.fn._activateItem.call(this);\n                }\n                this.currentTag(null);\n            },\n            _listOptions: function (options) {\n                var that = this;\n                var listOptions = List.fn._listOptions.call(that, $.extend(options, {\n                    selectedItemChange: proxy(that._selectedItemChange, that),\n                    selectable: 'multiple'\n                }));\n                var itemTemplate = this.options.itemTemplate || this.options.template;\n                var template = listOptions.itemTemplate || itemTemplate || listOptions.template;\n                if (!template) {\n                    template = '#:' + kendo.expr(listOptions.dataTextField, 'data') + '#';\n                }\n                listOptions.template = template;\n                return listOptions;\n            },\n            _setListValue: function () {\n                List.fn._setListValue.call(this, this._initialValues.slice(0));\n            },\n            _listChange: function (e) {\n                var data = this.dataSource.flatView();\n                var optionsMap = this._optionsMap;\n                var valueGetter = this._value;\n                if (this._state === REBIND) {\n                    this._state = '';\n                }\n                for (var i = 0; i < e.added.length; i++) {\n                    if (optionsMap[valueGetter(e.added[i].dataItem)] === undefined) {\n                        this._render(data);\n                        break;\n                    }\n                }\n                this._selectValue(e.added, e.removed);\n            },\n            _selectedItemChange: function (e) {\n                var items = e.items;\n                var context;\n                var idx;\n                for (idx = 0; idx < items.length; idx++) {\n                    context = items[idx];\n                    this.tagList.children().eq(context.index).children('span:first').html(this.tagTextTemplate(context.item));\n                }\n            },\n            _wrapperMousedown: function (e) {\n                var that = this;\n                var notInput = e.target.nodeName.toLowerCase() !== 'input';\n                var target = $(e.target);\n                var closeButton = target.hasClass('k-select') || target.hasClass('k-icon');\n                if (closeButton) {\n                    closeButton = !target.closest('.k-select').children('.k-i-arrow-60-down').length;\n                }\n                if (notInput && !(closeButton && kendo.support.mobileOS) && e.cancelable) {\n                    e.preventDefault();\n                }\n                if (!closeButton) {\n                    if (that.input[0] !== activeElement() && notInput) {\n                        that.input.focus();\n                    }\n                    if (that.options.minLength === 1) {\n                        that.open();\n                    }\n                }\n            },\n            _inputFocus: function () {\n                this._placeholder(false);\n                this.wrapper.addClass(FOCUSEDCLASS);\n            },\n            _inputFocusout: function () {\n                var that = this;\n                clearTimeout(that._typingTimeout);\n                that.wrapper.removeClass(FOCUSEDCLASS);\n                that._placeholder(!that.listView.selectedDataItems()[0], true);\n                that.close();\n                if (that._state === FILTER) {\n                    that._state = ACCEPT;\n                    that.listView.skipUpdate(true);\n                }\n                if (that.listView.bound() && that.listView.isFiltered()) {\n                    that.persistTagList = true;\n                    that._clearFilter();\n                }\n                that.element.blur();\n            },\n            _removeTag: function (tag, shouldTrigger) {\n                var that = this;\n                var state = that._state;\n                var position = tag.index();\n                var listView = that.listView;\n                var value = listView.value()[position];\n                var dataItem = that.listView.selectedDataItems()[position];\n                var customIndex = that._customOptions[value];\n                var listViewChildren = listView.element[0].children;\n                var option;\n                var listViewChild;\n                if (that.trigger(DESELECT, {\n                        dataItem: dataItem,\n                        item: tag\n                    })) {\n                    that._close();\n                    return;\n                }\n                if (customIndex === undefined && (state === ACCEPT || state === FILTER)) {\n                    customIndex = that._optionsMap[value];\n                }\n                var done = function () {\n                    that.currentTag(null);\n                    if (shouldTrigger) {\n                        that._change();\n                    }\n                    that._close();\n                };\n                if (customIndex === undefined && listView.select().length) {\n                    that.persistTagList = false;\n                    listView.select(listView.select()[position]).done(done);\n                } else {\n                    option = that.element[0].children[customIndex];\n                    if (option) {\n                        option.selected = false;\n                    }\n                    listView.removeAt(position);\n                    listViewChild = listViewChildren[customIndex];\n                    if (listViewChild) {\n                        listViewChildren[customIndex].classList.remove('k-state-selected');\n                    }\n                    if (that.options.tagMode !== 'single') {\n                        tag.remove();\n                    } else {\n                        that._updateTagListHTML();\n                    }\n                    done();\n                }\n            },\n            _tagListClick: function (e) {\n                var target = $(e.currentTarget);\n                if (!target.children('.k-i-arrow-60-down').length) {\n                    this._removeTag(target.closest(LI), true);\n                }\n            },\n            _clearClick: function () {\n                var that = this;\n                if (that.options.tagMode === 'single') {\n                    that._clearSingleTagValue();\n                } else {\n                    that.tagList.children().each(function (index, tag) {\n                        that._removeTag($(tag), false);\n                    });\n                }\n                that.input.val('');\n                that._search();\n                that._change();\n                that.focus();\n                that._hideClear();\n                if (that._state === FILTER) {\n                    that._state = ACCEPT;\n                }\n            },\n            _clearSingleTagValue: function () {\n                var that = this;\n                var persistTagList = that.persistTagList;\n                if (persistTagList) {\n                    that.persistTagList = false;\n                }\n                that.listView.value([]);\n                that.persistTagList = persistTagList;\n            },\n            _editable: function (options) {\n                var that = this, disable = options.disable, readonly = options.readonly, wrapper = that.wrapper.off(ns), tagList = that.tagList.off(ns), input = that.element.add(that.input.off(ns));\n                if (!readonly && !disable) {\n                    wrapper.removeClass(STATEDISABLED).on(HOVEREVENTS, that._toggleHover).on('mousedown' + ns + ' touchend' + ns, proxy(that._wrapperMousedown, that));\n                    that.input.on(KEYDOWN, proxy(that._keydown, that)).on('paste' + ns, proxy(that._search, that)).on('input' + ns, proxy(that._search, that)).on('focus' + ns, proxy(that._inputFocus, that)).on('focusout' + ns, proxy(that._inputFocusout, that));\n                    that._clear.on(CLICK + ns + ' touchend' + ns, proxy(that._clearClick, that));\n                    input.removeAttr(DISABLED).removeAttr(READONLY).attr(ARIA_DISABLED, false);\n                    tagList.on(MOUSEENTER, LI, function () {\n                        $(this).addClass(HOVERCLASS);\n                    }).on(MOUSELEAVE, LI, function () {\n                        $(this).removeClass(HOVERCLASS);\n                    }).on(CLICK, 'li.k-button .k-select', proxy(that._tagListClick, that));\n                } else {\n                    if (disable) {\n                        wrapper.addClass(STATEDISABLED);\n                    } else {\n                        wrapper.removeClass(STATEDISABLED);\n                    }\n                    input.attr(DISABLED, disable).attr(READONLY, readonly).attr(ARIA_DISABLED, disable);\n                }\n            },\n            _close: function () {\n                var that = this;\n                if (that.options.autoClose) {\n                    that.close();\n                } else {\n                    that.popup.position();\n                }\n            },\n            _filterSource: function (filter, force) {\n                if (!force) {\n                    force = this._retrieveData;\n                }\n                this._retrieveData = false;\n                List.fn._filterSource.call(this, filter, force);\n            },\n            close: function () {\n                this._activeItem = null;\n                this.input.removeAttr('aria-activedescendant');\n                this.popup.close();\n            },\n            open: function () {\n                var that = this;\n                if (that._request) {\n                    that._retrieveData = false;\n                }\n                if (that._retrieveData || !that.listView.bound() || that._state === ACCEPT) {\n                    that._open = true;\n                    that._state = REBIND;\n                    that.listView.skipUpdate(true);\n                    that.persistTagList = that._initialOpen && !that.listView.bound() ? false : true;\n                    that._filterSource();\n                    that._focusItem();\n                } else if (that._allowOpening()) {\n                    if (that._initialOpen && !that.options.autoBind && !that.options.virtual && that.options.value && !$.isPlainObject(that.options.value[0])) {\n                        that.value(that._initialValues);\n                    }\n                    that.popup._hovered = true;\n                    that._initialOpen = false;\n                    that.popup.open();\n                    that._focusItem();\n                }\n            },\n            toggle: function (toggle) {\n                toggle = toggle !== undefined ? toggle : !this.popup.visible();\n                this[toggle ? OPEN : CLOSE]();\n            },\n            refresh: function () {\n                this.listView.refresh();\n            },\n            _listBound: function () {\n                var that = this;\n                var data = that.dataSource.flatView();\n                var skip = that.listView.skip();\n                that._render(data);\n                that._renderFooter();\n                that._renderNoData();\n                that._toggleNoData(!data.length);\n                that._resizePopup();\n                if (that._open) {\n                    that._open = false;\n                    that.toggle(that._allowOpening());\n                }\n                that.popup.position();\n                if (that.options.highlightFirst && (skip === undefined || skip === 0)) {\n                    that.listView.focusFirst();\n                }\n                if (that._touchScroller) {\n                    that._touchScroller.reset();\n                }\n                that._hideBusy();\n                that._makeUnselectable();\n                that.trigger('dataBound');\n            },\n            _inputValue: function () {\n                var that = this;\n                var inputValue = that.input.val();\n                if (that.options.placeholder === inputValue) {\n                    inputValue = '';\n                }\n                return inputValue;\n            },\n            value: function (value) {\n                var that = this;\n                var listView = that.listView;\n                var oldValue = listView.value().slice();\n                var maxSelectedItems = that.options.maxSelectedItems;\n                var clearFilters = listView.bound() && listView.isFiltered();\n                if (value === undefined) {\n                    return oldValue;\n                }\n                that.persistTagList = false;\n                that.requireValueMapper(that.options, value);\n                value = that._normalizeValues(value);\n                if (maxSelectedItems !== null && value.length > maxSelectedItems) {\n                    value = value.slice(0, maxSelectedItems);\n                }\n                if (clearFilters) {\n                    that._clearFilter();\n                }\n                listView.value(value);\n                that._old = that._valueBeforeCascade = value.slice();\n                if (!clearFilters) {\n                    that._fetchData();\n                }\n                that._ariaSetSize(that.value().length);\n                that._toggleCloseVisibility();\n            },\n            _preselect: function (data, value) {\n                var that = this;\n                if (!isArray(data) && !(data instanceof kendo.data.ObservableArray)) {\n                    data = [data];\n                }\n                if ($.isPlainObject(data[0]) || data[0] instanceof kendo.data.ObservableObject || !that.options.dataValueField) {\n                    that.dataSource.data(data);\n                    that.value(value || that._initialValues);\n                    that._retrieveData = true;\n                }\n            },\n            _setOption: function (value, selected) {\n                var option = this.element[0].children[this._optionsMap[value]];\n                if (option) {\n                    option.selected = selected;\n                }\n            },\n            _fetchData: function () {\n                var that = this;\n                var hasItems = !!that.dataSource.view().length;\n                var isEmptyArray = that.listView.value().length === 0;\n                if (isEmptyArray || that._request) {\n                    return;\n                }\n                if (that._retrieveData || !that._fetch && !hasItems) {\n                    that._fetch = true;\n                    that._retrieveData = false;\n                    that.dataSource.read().done(function () {\n                        that._fetch = false;\n                    });\n                }\n            },\n            _isBound: function () {\n                return this.listView.bound() && !this._retrieveData;\n            },\n            _dataSource: function () {\n                var that = this, element = that.element, options = that.options, dataSource = options.dataSource || {};\n                dataSource = isArray(dataSource) ? { data: dataSource } : dataSource;\n                dataSource.select = element;\n                dataSource.fields = [\n                    { field: options.dataTextField },\n                    { field: options.dataValueField }\n                ];\n                if (that.dataSource && that._refreshHandler) {\n                    that._unbindDataSource();\n                } else {\n                    that._progressHandler = proxy(that._showBusy, that);\n                    that._errorHandler = proxy(that._hideBusy, that);\n                }\n                that.dataSource = kendo.data.DataSource.create(dataSource).bind(PROGRESS, that._progressHandler).bind('error', that._errorHandler);\n            },\n            _reset: function () {\n                var that = this, element = that.element, formId = element.attr('form'), form = formId ? $('#' + formId) : element.closest('form');\n                if (form[0]) {\n                    that._resetHandler = function () {\n                        setTimeout(function () {\n                            that.value(that._initialValues);\n                            that._placeholder();\n                        });\n                    };\n                    that._form = form.on('reset', that._resetHandler);\n                }\n            },\n            _initValue: function () {\n                var value = this.options.value || this.element.val();\n                this._old = this._initialValues = this._normalizeValues(value);\n            },\n            _normalizeValues: function (value) {\n                var that = this;\n                if (value === null) {\n                    value = [];\n                } else if (value && $.isPlainObject(value)) {\n                    value = [that._value(value)];\n                } else if (value && $.isPlainObject(value[0])) {\n                    value = $.map(value, function (dataItem) {\n                        return that._value(dataItem);\n                    });\n                } else if (!isArray(value) && !(value instanceof ObservableArray)) {\n                    value = [value];\n                } else if (isArray(value)) {\n                    value = value.slice();\n                }\n                return value;\n            },\n            _change: function () {\n                var that = this, value = that.value();\n                if (!compare(value, that._old)) {\n                    that._old = value.slice();\n                    that.trigger(CHANGE);\n                    that.element.trigger(CHANGE);\n                }\n                that.popup.position();\n                that._ariaSetSize(value.length);\n                that._toggleCloseVisibility();\n            },\n            _click: function (e) {\n                var that = this;\n                var item = e.item;\n                e.preventDefault();\n                that._select(item).done(function () {\n                    that._activeItem = item;\n                    that._change();\n                    that._close();\n                });\n            },\n            _getActiveItem: function () {\n                return this._activeItem || $(this.listView.items()[this._getSelectedIndices().length - 1]) || this.listView.focus();\n            },\n            _getSelectedIndices: function () {\n                return this.listView._selectedIndices || this.listView._selectedIndexes;\n            },\n            _keydown: function (e) {\n                var that = this;\n                var key = e.keyCode;\n                var tag = that._currentTag;\n                var listView = that.listView;\n                var hasValue = that.input.val();\n                var isRtl = kendo.support.isRtl(that.wrapper);\n                var visible = that.popup.visible();\n                var dir = 0;\n                var activeItemIdx;\n                if (key !== keys.ENTER) {\n                    this._multipleSelection = false;\n                }\n                if (key === keys.DOWN) {\n                    e.preventDefault();\n                    if (!visible) {\n                        that.open();\n                        if (!listView.focus()) {\n                            listView.focusFirst();\n                        }\n                        return;\n                    }\n                    if (listView.focus()) {\n                        if (!that._activeItem && e.shiftKey) {\n                            that._activeItem = listView.focus();\n                            dir = -1;\n                        }\n                        activeItemIdx = listView.getElementIndex(that._getActiveItem().first());\n                        listView.focusNext();\n                        if (!listView.focus()) {\n                            listView.focusLast();\n                        } else {\n                            if (e.shiftKey) {\n                                this._multipleSelection = true;\n                                that._selectRange(activeItemIdx, listView.getElementIndex(listView.focus().first()) + dir);\n                            }\n                        }\n                    } else {\n                        listView.focusFirst();\n                    }\n                } else if (key === keys.UP) {\n                    if (visible) {\n                        if (!that._activeItem && e.shiftKey) {\n                            that._activeItem = listView.focus();\n                            dir = 1;\n                        }\n                        activeItemIdx = listView.getElementIndex(that._getActiveItem().first());\n                        listView.focusPrev();\n                        if (!listView.focus()) {\n                            that.close();\n                        } else {\n                            if (e.shiftKey) {\n                                this._multipleSelection = true;\n                                that._selectRange(activeItemIdx, listView.getElementIndex(listView.focus().first()) + dir);\n                            }\n                        }\n                    }\n                    e.preventDefault();\n                } else if (key === keys.LEFT && !isRtl || key === keys.RIGHT && isRtl) {\n                    if (!hasValue) {\n                        tag = tag ? tag.prev() : $(that.tagList[0].lastChild);\n                        if (tag[0]) {\n                            that.currentTag(tag);\n                        }\n                    }\n                } else if (key === keys.RIGHT && !isRtl || key === keys.LEFT && isRtl) {\n                    if (!hasValue && tag) {\n                        tag = tag.next();\n                        that.currentTag(tag[0] ? tag : null);\n                    }\n                } else if (e.ctrlKey && !e.altKey && key === keys.A && visible && !that.options.virtual) {\n                    this._multipleSelection = true;\n                    if (this._getSelectedIndices().length === listView.items().length) {\n                        that._activeItem = null;\n                    }\n                    if (listView.items().length) {\n                        that._selectRange(0, listView.items().length - 1);\n                    }\n                } else if (key === keys.ENTER && visible) {\n                    if (!listView.focus()) {\n                        return;\n                    }\n                    e.preventDefault();\n                    if (this._multipleSelection) {\n                        this._multipleSelection = false;\n                        if (listView.focus().hasClass(SELECTEDCLASS)) {\n                            that._close();\n                            return;\n                        }\n                    }\n                    that._select(listView.focus()).done(function () {\n                        that._change();\n                        that._close();\n                    });\n                } else if (key === keys.SPACEBAR && e.ctrlKey && visible) {\n                    if (that._activeItem && listView.focus() && listView.focus()[0] === that._activeItem[0]) {\n                        that._activeItem = null;\n                    }\n                    if (!$(listView.focus()).hasClass(SELECTEDCLASS)) {\n                        that._activeItem = listView.focus();\n                    }\n                    that._select(listView.focus()).done(function () {\n                        that._change();\n                    });\n                    e.preventDefault();\n                } else if (key === keys.SPACEBAR && e.shiftKey && visible) {\n                    var activeIndex = listView.getElementIndex(that._getActiveItem());\n                    var currentIndex = listView.getElementIndex(listView.focus());\n                    if (activeIndex !== undefined && currentIndex !== undefined) {\n                        that._selectRange(activeIndex, currentIndex);\n                    }\n                    e.preventDefault();\n                } else if (key === keys.ESC) {\n                    if (visible) {\n                        e.preventDefault();\n                    } else {\n                        that.tagList.children().each(function (index, tag) {\n                            that._removeTag($(tag), false);\n                        });\n                        that._change();\n                    }\n                    that.close();\n                } else if (key === keys.HOME) {\n                    if (visible) {\n                        if (!listView.focus()) {\n                            that.close();\n                        } else {\n                            if (e.ctrlKey && e.shiftKey && !that.options.virtual) {\n                                that._selectRange(listView.getElementIndex(listView.focus()[0]), 0);\n                            }\n                            listView.focusFirst();\n                        }\n                    } else if (!hasValue) {\n                        tag = that.tagList[0].firstChild;\n                        if (tag) {\n                            that.currentTag($(tag));\n                        }\n                    }\n                } else if (key === keys.END) {\n                    if (visible) {\n                        if (!listView.focus()) {\n                            that.close();\n                        } else {\n                            if (e.ctrlKey && e.shiftKey && !that.options.virtual) {\n                                that._selectRange(listView.getElementIndex(listView.focus()[0]), listView.element.children().length - 1);\n                            }\n                            listView.focusLast();\n                        }\n                    } else if (!hasValue) {\n                        tag = that.tagList[0].lastChild;\n                        if (tag) {\n                            that.currentTag($(tag));\n                        }\n                    }\n                } else if ((key === keys.DELETE || key === keys.BACKSPACE) && !hasValue) {\n                    that._state = ACCEPT;\n                    if (that.options.tagMode === 'single') {\n                        that._clearSingleTagValue();\n                        that._change();\n                        that._close();\n                        return;\n                    }\n                    if (key === keys.BACKSPACE && !tag) {\n                        tag = $(that.tagList[0].lastChild);\n                    }\n                    if (tag && tag[0]) {\n                        that._removeTag(tag, true);\n                    }\n                } else if (that.popup.visible() && (key === keys.PAGEDOWN || key === keys.PAGEUP)) {\n                    e.preventDefault();\n                    var direction = key === keys.PAGEDOWN ? 1 : -1;\n                    listView.scrollWith(direction * listView.screenHeight());\n                } else {\n                    clearTimeout(that._typingTimeout);\n                    setTimeout(function () {\n                        that._scale();\n                    });\n                    that._search();\n                }\n            },\n            _hideBusy: function () {\n                var that = this;\n                clearTimeout(that._busy);\n                that.input.attr('aria-busy', false);\n                that._loading.addClass(HIDDENCLASS);\n                that._request = false;\n                that._busy = null;\n                that._toggleCloseVisibility();\n            },\n            _showBusyHandler: function () {\n                this.input.attr('aria-busy', true);\n                this._loading.removeClass(HIDDENCLASS);\n                this._hideClear();\n            },\n            _showBusy: function () {\n                var that = this;\n                that._request = true;\n                if (that._busy) {\n                    return;\n                }\n                that._busy = setTimeout(proxy(that._showBusyHandler, that), 100);\n            },\n            _placeholder: function (show, skipCaret) {\n                var that = this;\n                var input = that.input;\n                var active = activeElement();\n                var placeholder = that.options.placeholder;\n                var inputValue = input.val();\n                var isActive = input[0] === active;\n                var caretPos = inputValue.length;\n                if (!isActive || that.options.autoClose || inputValue === placeholder) {\n                    caretPos = 0;\n                    inputValue = '';\n                }\n                if (show === undefined) {\n                    show = false;\n                    if (input[0] !== active) {\n                        show = !that.listView.selectedDataItems()[0];\n                    }\n                }\n                that._prev = inputValue;\n                input.toggleClass('k-readonly', show).val(show ? placeholder : inputValue);\n                if (isActive && !skipCaret) {\n                    kendo.caret(input[0], caretPos, caretPos);\n                }\n                that._scale();\n            },\n            _scale: function () {\n                var that = this, wrapper = that.wrapper.find('.k-multiselect-wrap'), wrapperWidth = wrapper.width(), span = that._span.text(that.input.val()), textWidth;\n                if (!wrapper.is(':visible')) {\n                    span.appendTo(document.documentElement);\n                    wrapperWidth = textWidth = span.width() + 25;\n                    span.appendTo(wrapper);\n                } else {\n                    textWidth = span.width() + 25;\n                }\n                that.input.width(textWidth > wrapperWidth ? wrapperWidth : textWidth);\n            },\n            _option: function (dataValue, dataText, selected) {\n                var option = '<option';\n                if (dataValue !== undefined) {\n                    dataValue += '';\n                    if (dataValue.indexOf('\"') !== -1) {\n                        dataValue = dataValue.replace(quotRegExp, '&quot;');\n                    }\n                    option += ' value=\"' + dataValue + '\"';\n                }\n                if (selected) {\n                    option += ' selected';\n                }\n                option += '>';\n                if (dataText !== undefined) {\n                    option += kendo.htmlEncode(dataText);\n                }\n                return option += '</option>';\n            },\n            _render: function (data) {\n                var selectedItems = this.listView.selectedDataItems();\n                var values = this.listView.value();\n                var length = data.length;\n                var selectedIndex;\n                var options = '';\n                var dataItem;\n                var value;\n                var idx;\n                if (values.length !== selectedItems.length) {\n                    selectedItems = this._buildSelectedItems(values);\n                }\n                var custom = {};\n                var optionsMap = {};\n                for (idx = 0; idx < length; idx++) {\n                    dataItem = data[idx];\n                    value = this._value(dataItem);\n                    selectedIndex = this._selectedItemIndex(value, selectedItems);\n                    if (selectedIndex !== -1) {\n                        selectedItems.splice(selectedIndex, 1);\n                    }\n                    optionsMap[value] = idx;\n                    options += this._option(value, this._text(dataItem), selectedIndex !== -1);\n                }\n                if (selectedItems.length) {\n                    for (idx = 0; idx < selectedItems.length; idx++) {\n                        dataItem = selectedItems[idx];\n                        value = this._value(dataItem);\n                        custom[value] = length;\n                        optionsMap[value] = length;\n                        length += 1;\n                        options += this._option(value, this._text(dataItem), true);\n                    }\n                }\n                this._customOptions = custom;\n                this._optionsMap = optionsMap;\n                this.element.html(options);\n            },\n            _buildSelectedItems: function (values) {\n                var valueField = this.options.dataValueField;\n                var textField = this.options.dataTextField;\n                var result = [];\n                var item;\n                for (var idx = 0; idx < values.length; idx++) {\n                    item = {};\n                    item[valueField] = values[idx];\n                    item[textField] = values[idx];\n                    result.push(item);\n                }\n                return result;\n            },\n            _selectedItemIndex: function (value, selectedItems) {\n                var valueGetter = this._value;\n                var idx = 0;\n                for (; idx < selectedItems.length; idx++) {\n                    if (value === valueGetter(selectedItems[idx])) {\n                        return idx;\n                    }\n                }\n                return -1;\n            },\n            _search: function () {\n                var that = this;\n                clearTimeout(that._typingTimeout);\n                that._typingTimeout = setTimeout(function () {\n                    var value = that._inputValue();\n                    if (that._prev !== value) {\n                        that._prev = value;\n                        that.search(value);\n                        that._toggleCloseVisibility();\n                    }\n                }, that.options.delay);\n            },\n            _toggleCloseVisibility: function () {\n                if (this.value().length || this.input.val() && this.input.val() !== this.options.placeholder) {\n                    this._showClear();\n                } else {\n                    this._hideClear();\n                }\n            },\n            _allowOpening: function () {\n                return this._allowSelection() && List.fn._allowOpening.call(this);\n            },\n            _allowSelection: function () {\n                var max = this.options.maxSelectedItems;\n                return max === null || max > this.listView.value().length;\n            },\n            _angularTagItems: function (cmd) {\n                var that = this;\n                that.angular(cmd, function () {\n                    return {\n                        elements: that.tagList[0].children,\n                        data: $.map(that.dataItems(), function (dataItem) {\n                            return { dataItem: dataItem };\n                        })\n                    };\n                });\n            },\n            updatePersistTagList: function (added, removed) {\n                if (this.persistTagList.added && this.persistTagList.added.length === removed.length && this.persistTagList.removed && this.persistTagList.removed.length === added.length) {\n                    this.persistTagList = false;\n                } else {\n                    this.listView._removedAddedIndexes = this._old.slice();\n                    this.persistTagList = {\n                        added: added,\n                        removed: removed\n                    };\n                }\n            },\n            _selectValue: function (added, removed) {\n                var that = this;\n                var total = that.dataSource.total();\n                var tagList = that.tagList;\n                var getter = that._value;\n                var removedItem;\n                var addedItem;\n                var idx;\n                if (this.persistTagList) {\n                    this.updatePersistTagList(added, removed);\n                    return;\n                }\n                that._angularTagItems('cleanup');\n                if (that.options.tagMode === 'multiple') {\n                    for (idx = removed.length - 1; idx > -1; idx--) {\n                        removedItem = removed[idx];\n                        if (tagList.children().length) {\n                            tagList[0].removeChild(tagList[0].children[removedItem.position]);\n                            that._setOption(getter(removedItem.dataItem), false);\n                        }\n                    }\n                    for (idx = 0; idx < added.length; idx++) {\n                        addedItem = added[idx];\n                        tagList.append(that.tagTemplate(addedItem.dataItem));\n                        that._setOption(getter(addedItem.dataItem), true);\n                    }\n                } else {\n                    if (!that._maxTotal || that._maxTotal < total) {\n                        that._maxTotal = total;\n                    }\n                    this._updateTagListHTML();\n                    for (idx = removed.length - 1; idx > -1; idx--) {\n                        that._setOption(getter(removed[idx].dataItem), false);\n                    }\n                    for (idx = 0; idx < added.length; idx++) {\n                        that._setOption(getter(added[idx].dataItem), true);\n                    }\n                }\n                that._angularTagItems('compile');\n                that._placeholder();\n            },\n            _updateTagListHTML: function () {\n                var that = this;\n                var values = that.value();\n                var total = that.dataSource.total();\n                var tagList = that.tagList;\n                tagList.html('');\n                if (values.length) {\n                    tagList.append(that.tagTemplate({\n                        values: values,\n                        dataItems: that.dataItems(),\n                        maxTotal: that._maxTotal,\n                        currentTotal: total\n                    }));\n                }\n            },\n            _select: function (candidate) {\n                var resolved = $.Deferred().resolve();\n                if (!candidate) {\n                    return resolved;\n                }\n                var that = this;\n                var listView = that.listView;\n                var dataItem = listView.dataItemByIndex(listView.getElementIndex(candidate));\n                var isSelected = candidate.hasClass('k-state-selected');\n                if (that._state === REBIND) {\n                    that._state = '';\n                }\n                if (!that._allowSelection() && !isSelected) {\n                    return resolved;\n                }\n                if (that.trigger(isSelected ? DESELECT : SELECT, {\n                        dataItem: dataItem,\n                        item: candidate\n                    })) {\n                    that._close();\n                    return resolved;\n                }\n                that.persistTagList = false;\n                return listView.select(candidate).done(function () {\n                    that._placeholder();\n                    if (that._state === FILTER) {\n                        that._state = ACCEPT;\n                        listView.skipUpdate(true);\n                    }\n                });\n            },\n            _selectRange: function (startIndex, endIndex) {\n                var that = this;\n                var listView = this.listView;\n                var maxSelectedItems = this.options.maxSelectedItems;\n                var indices = this._getSelectedIndices().slice();\n                var indicesToSelect = [];\n                var i;\n                var selectIndices = function (indices) {\n                    listView.select(indices).done(function () {\n                        indices.forEach(function (index) {\n                            var dataItem = listView.dataItemByIndex(index);\n                            var candidate = listView.element.children()[index];\n                            var isSelected = $(candidate).hasClass('k-state-selected');\n                            that.trigger(isSelected ? SELECT : DESELECT, {\n                                dataItem: dataItem,\n                                item: $(candidate)\n                            });\n                        });\n                        that._change();\n                    });\n                };\n                if (indices.length - 1 === endIndex - startIndex) {\n                    return selectIndices(indices);\n                }\n                if (startIndex < endIndex) {\n                    for (i = startIndex; i <= endIndex; i++) {\n                        indicesToSelect.push(i);\n                    }\n                } else {\n                    for (i = startIndex; i >= endIndex; i--) {\n                        indicesToSelect.push(i);\n                    }\n                }\n                if (maxSelectedItems !== null && indicesToSelect.length > maxSelectedItems) {\n                    indicesToSelect = indicesToSelect.slice(0, maxSelectedItems);\n                }\n                for (i = 0; i < indicesToSelect.length; i++) {\n                    var index = indicesToSelect[i];\n                    if (this._getSelectedIndices().indexOf(index) == -1) {\n                        indices.push(index);\n                    } else {\n                        indices.splice(indices.indexOf(index), 1);\n                    }\n                }\n                if (!indices.length) {\n                    return;\n                }\n                that.persistTagList = false;\n                return selectIndices(indices);\n            },\n            _input: function () {\n                var that = this;\n                var element = that.element;\n                var accessKey = element[0].accessKey;\n                var input = that._innerWrapper.children('input.k-input');\n                if (!input[0]) {\n                    input = $('<input class=\"k-input\" style=\"width: 25px\" />').appendTo(that._innerWrapper);\n                }\n                element.removeAttr('accesskey');\n                that._focused = that.input = input.attr({\n                    'accesskey': accessKey,\n                    'autocomplete': AUTOCOMPLETEVALUE,\n                    'role': 'listbox',\n                    'title': element[0].title,\n                    'aria-expanded': false,\n                    'aria-haspopup': 'listbox',\n                    'aria-autocomplete': 'list'\n                });\n            },\n            _tagList: function () {\n                var that = this, tagList = that._innerWrapper.children('ul');\n                if (!tagList[0]) {\n                    tagList = $('<ul unselectable=\"on\" class=\"k-reset\"/>').appendTo(that._innerWrapper);\n                }\n                that.tagList = tagList;\n            },\n            _tagTemplate: function () {\n                var that = this;\n                var options = that.options;\n                var tagTemplate = options.tagTemplate;\n                var hasDataSource = options.dataSource;\n                var isMultiple = options.tagMode === 'multiple';\n                var defaultTemplate;\n                if (that.element[0].length && !hasDataSource) {\n                    options.dataTextField = options.dataTextField || 'text';\n                    options.dataValueField = options.dataValueField || 'value';\n                }\n                defaultTemplate = isMultiple ? kendo.template('#:' + kendo.expr(options.dataTextField, 'data') + '#', { useWithBlock: false }) : kendo.template('#:values.length# item(s) selected');\n                that.tagTextTemplate = tagTemplate = tagTemplate ? kendo.template(tagTemplate) : defaultTemplate;\n                that.tagTemplate = function (data) {\n                    return '<li role=\"option\" aria-selected=\"true\" class=\"k-button\" unselectable=\"on\"><span unselectable=\"on\">' + tagTemplate(data) + '</span><span aria-hidden=\"true\" unselectable=\"on\" aria-label=\"' + (isMultiple ? 'delete' : 'open') + '\" class=\"k-select\"><span class=\"k-icon ' + (isMultiple ? 'k-i-close' : 'k-i-arrow-60-down') + '\">' + '</span></span></li>';\n                };\n            },\n            _loader: function () {\n                this._loading = $('<span class=\"k-icon k-i-loading ' + HIDDENCLASS + '\"></span>').insertAfter(this.input);\n            },\n            _clearButton: function () {\n                List.fn._clearButton.call(this);\n                if (this.options.clearButton) {\n                    this._clear.insertAfter(this.input);\n                    this.wrapper.addClass('k-multiselect-clearable');\n                }\n            },\n            _textContainer: function () {\n                var computedStyles = kendo.getComputedStyles(this.input[0], styles);\n                computedStyles.position = 'absolute';\n                computedStyles.visibility = 'hidden';\n                computedStyles.top = -3333;\n                computedStyles.left = -3333;\n                this._span = $('<span/>').css(computedStyles).appendTo(this.wrapper);\n            },\n            _wrapper: function () {\n                var that = this, element = that.element, wrapper = element.parent('span.k-multiselect');\n                if (!wrapper[0]) {\n                    wrapper = element.wrap('<div class=\"k-widget k-multiselect\" unselectable=\"on\" />').parent();\n                    wrapper[0].style.cssText = element[0].style.cssText;\n                    wrapper[0].title = element[0].title;\n                    $('<div class=\"k-multiselect-wrap k-floatwrap\" role=\"listbox\" unselectable=\"on\" />').insertBefore(element);\n                }\n                that.wrapper = wrapper.addClass(element[0].className).css('display', '');\n                that._innerWrapper = $(wrapper[0].firstChild);\n            },\n            _ariaSetSize: function (value) {\n                var that = this;\n                var selectedItems = that.tagList.children();\n                if (value && selectedItems.length) {\n                    selectedItems.attr('aria-setsize', value);\n                }\n            },\n            _ariaSetLive: function () {\n                var that = this;\n                that.ul.attr('aria-live', !that._isFilterEnabled() ? 'off' : 'polite');\n            }\n        });\n        function compare(a, b) {\n            var length;\n            if (a === null && b !== null || a !== null && b === null) {\n                return false;\n            }\n            length = a.length;\n            if (length !== b.length) {\n                return false;\n            }\n            while (length--) {\n                if (a[length] !== b[length]) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        ui.plugin(MultiSelect);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}