{"version": 3, "sources": ["kendo.mobile.pane.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "mobile", "roleSelector", "ui", "Widget", "ViewEngine", "View", "Loader", "EXTERNAL", "HREF", "DUMMY_HREF", "NAVIGATE", "VIEW_SHOW", "SAME_VIEW_REQUESTED", "OS", "support", "mobileOS", "SKIP_TRANSITION_ON_BACK_BUTTON", "ios", "appMode", "flatVersion", "WIDGET_RELS", "BACK", "attrValue", "Pane", "extend", "init", "element", "options", "that", "this", "fn", "call", "addClass", "collapsible", "history", "historyCallback", "url", "params", "backButtonPressed", "transition", "viewEngine", "showView", "_historyNavigate", "length", "pop", "push", "parseQueryStringParams", "_historyReplace", "loader", "loading", "container", "modelScope", "rootNeeded", "initial", "serverNavigation", "remoteViewURLPrefix", "root", "layout", "$angular", "showStart", "closeActiveDialogs", "after", "transitionDone", "viewShow", "e", "trigger", "loadStart", "show", "loadComplete", "hide", "sameViewRequested", "viewTypeDetermined", "remote", "_setPortraitWidth", "onResize", "_setupAppLinks", "dialogs", "find", "filter", "each", "widgetInstance", "close", "navigateToInitial", "navigate", "name", "<PERSON><PERSON><PERSON><PERSON>", "events", "append", "html", "destroy", "userEvents", "id", "replace", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "router", "bind", "attrUrl", "pushState", "rootView", "attr", "preventDefault", "hideLoading", "showLoading", "changeLoadingMessage", "message", "changeMessage", "view", "width", "application", "is", "css", "linkRoles", "pressedButtonSelector", "ns", "buttonSelectors", "map", "role", "join", "handler", "on", "UserEvents", "fastTap", "tap", "event", "currentTarget", "touch", "_mouseup", "_appLinkClick", "href", "which", "isDefaultPrevented", "pane", "link", "rel", "target", "delayedTouchEnd", "offsetHeight", "setTimeout", "match", "openFor", "stopPropagation", "data", "wrap", "parent", "paneContainer", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBACH,oBACA,uBACDD,IACL,WA4PE,MAhPC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAASF,EAAME,OAAQC,EAAeH,EAAMG,aAAcC,EAAKF,EAAOE,GAAIC,EAASD,EAAGC,OAAQC,EAAaJ,EAAOI,WAAYC,EAAOH,EAAGG,KAAMC,EAASN,EAAOE,GAAGI,OAAQC,EAAW,WAAYC,EAAO,OAAQC,EAAa,KAAMC,EAAW,WAAYC,EAAY,WAAYC,EAAsB,oBAAqBC,EAAKf,EAAMgB,QAAQC,SAAUC,EAAiCH,EAAGI,MAAQJ,EAAGK,SAAWL,EAAGM,aAAe,IAAKC,EAAc,uCAAwCC,EAAO,SAAUC,EAAYxB,EAAMwB,UACzhBC,EAAOpB,EAAOqB,QACdC,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOC,IACX1B,GAAO2B,GAAGL,KAAKM,KAAKH,EAAMF,EAASC,GACnCA,EAAUC,EAAKD,QACfD,EAAUE,EAAKF,QACfA,EAAQM,SAAS,WACbJ,EAAKD,QAAQM,aACbP,EAAQM,SAAS,uBAErBH,KAAKK,WACLL,KAAKM,gBAAkB,SAAUC,EAAKC,EAAQC,GAC1C,GAAIC,GAAaX,EAAKW,UAKtB,OAJAX,GAAKW,WAAa,KACdvB,GAAkCsB,IAClCC,EAAa,QAEVX,EAAKY,WAAWC,SAASL,EAAKG,EAAYF,IAErDR,KAAKa,iBAAmB,SAAUN,GAC9B,GAAIA,IAAQf,EAAM,CACd,GAA4B,IAAxBO,EAAKM,QAAQS,OACb,MAEJf,GAAKM,QAAQU,MACbR,EAAMR,EAAKM,QAAQN,EAAKM,QAAQS,OAAS,OAEzCf,GAAKM,QAAQW,KAAKT,EAEtBR,GAAKO,gBAAgBC,EAAKtC,EAAMgD,uBAAuBV,KAE3DP,KAAKkB,gBAAkB,SAAUX,GAC7B,GAAIC,GAASvC,EAAMgD,uBAAuBV,EAC1CR,GAAKM,QAAQN,EAAKM,QAAQS,OAAS,GAAKP,EACxCR,EAAKO,gBAAgBC,EAAKC,IAE9BT,EAAKoB,OAAS,GAAI1C,GAAOoB,GAAWuB,QAASrB,EAAKD,QAAQsB,UAC1DrB,EAAKY,WAAa,GAAIpC,IAClB8C,UAAWxB,EACXa,WAAYZ,EAAQY,WACpBY,WAAYxB,EAAQwB,WACpBC,YAAazB,EAAQ0B,QACrBC,iBAAkB3B,EAAQ2B,iBAC1BC,oBAAqB5B,EAAQ6B,MAAQ,GACrCC,OAAQ9B,EAAQ8B,OAChBC,SAAU/B,EAAQ+B,SAClBV,OAAQpB,EAAKoB,OACbW,UAAW,WACP/B,EAAKoB,OAAOT,aACZX,EAAKgC,sBAETC,MAAO,WACHjC,EAAKoB,OAAOc,kBAEhBC,SAAU,SAAUC,GAChBpC,EAAKqC,QAAQtD,EAAWqD,IAE5BE,UAAW,WACPtC,EAAKoB,OAAOmB,QAEhBC,aAAc,WACVxC,EAAKoB,OAAOqB,QAEhBC,kBAAmB,WACf1C,EAAKqC,QAAQrD,IAEjB2D,mBAAoB,SAAUP,GACrBA,EAAEQ,QAAW5C,EAAKD,QAAQ2B,kBAC3B1B,EAAKqC,QAAQvD,GAAY0B,IAAK4B,EAAE5B,SAI5CP,KAAK4C,oBACL3E,EAAM4E,SAAS,WACX9C,EAAK6C,sBAET7C,EAAK+C,kBAETf,mBAAoB,WAChB,GAAIgB,GAAU/C,KAAKH,QAAQmD,KAAK5E,EAAa,kCAAkC6E,OAAO,WACtFF,GAAQG,KAAK,WACTjF,EAAMkF,eAAepF,EAAEiC,MAAO3B,GAAI+E,WAG1CC,kBAAmB,WACf,GAAI7B,GAAUxB,KAAKF,QAAQ0B,OAI3B,OAHIA,IACAxB,KAAKsD,SAAS9B,GAEXA,GAEX1B,SACIyD,KAAM,OACNC,cAAe,GACf9C,WAAY,GACZkB,OAAQ,GACRxB,aAAa,EACboB,QAAS,KACTF,WAAYpD,OACZkD,QAAS,uBAEbqC,QACI5E,EACAC,EACAC,GAEJ2E,OAAQ,SAAUC,GACd,MAAO3D,MAAKW,WAAW+C,OAAOC,IAElCC,QAAS,WACLtF,EAAO2B,GAAG2D,QAAQ1D,KAAKF,MACvBA,KAAKW,WAAWiD,UAChB5D,KAAK6D,WAAWD,WAEpBN,SAAU,SAAU/C,EAAKG,GACjBH,YAAe/B,KACf+B,EAAMA,EAAIuD,IAEd9D,KAAKU,WAAaA,EAClBV,KAAKa,iBAAiBN,IAE1BwD,QAAS,SAAUxD,EAAKG,GAChBH,YAAe/B,KACf+B,EAAMA,EAAIuD,IAEd9D,KAAKU,WAAaA,EAClBV,KAAKkB,gBAAgBX,IAEzByD,aAAc,SAAUC,GACpB,GAAIlE,GAAOC,KAAMK,EAAUL,KAAKK,QAASM,EAAaX,KAAKW,UAC3DsD,GAAOC,KAAK,OAAQ,SAAU/B,GAAV,GAGZrB,GAFAP,EAAM4B,EAAE5B,IAAK4D,EAAUF,EAAOG,UAAY7D,EAAM,GACpDI,GAAW0D,SAASC,KAAKrG,EAAMqG,KAAK,OAAQH,GACxCrD,EAAST,EAAQS,OACT,MAARP,GAAeO,IACfmD,EAAOX,SAASjD,EAAQS,EAAS,IAAI,GACrCqB,EAAEoC,oBAGVN,EAAOC,KAAK,eAAgB,SAAU/B,GAC7BpC,EAAKO,gBAAgB6B,EAAE5B,IAAK4B,EAAE3B,OAAQ2B,EAAE1B,oBACzC0B,EAAEoC,mBAGVN,EAAOC,KAAK,OAAQ,WAChBnE,EAAKqC,QAAQrD,KAEjBgB,EAAKc,iBAAmB,SAAUN,GAC9B0D,EAAOX,SAAS/C,IAEpBR,EAAKmB,gBAAkB,SAAUX,GAC7B0D,EAAOF,QAAQxD,KAGvBiE,YAAa,WACTxE,KAAKmB,OAAOqB,QAEhBiC,YAAa,WACTzE,KAAKmB,OAAOmB,QAEhBoC,qBAAsB,SAAUC,GAC5B3E,KAAKmB,OAAOyD,cAAcD,IAE9BE,KAAM,WACF,MAAO7E,MAAKW,WAAWkE,QAE3BjC,kBAAmB,WACf,GAAIkC,GAAOtB,EAAgBxD,KAAKF,QAAQ0D,aACpCA,KACAsB,EAAQ7G,EAAME,OAAO4G,YAAYlF,QAAQmF,GAAG,gBAAkBxB,EAAgB,OAC9ExD,KAAKH,QAAQoF,IAAI,QAASH,KAGlChC,eAAgB,WACZ,GAAI/C,GAAOC,KAAMkF,EAAY,MAAOC,EAAwB,SAAWlH,EAAMmH,GAAK,qBAAsBC,EAAkBtH,EAAEuH,KACpH,SACA,aACA,eACA,iBACD,SAAUC,GACT,MAAOnH,GAAamH,GAAQ,QAAUJ,EAAwB,MAC/DK,KAAK,IACZxF,MAAKH,QAAQ4F,QAAQzF,MAAM0F,GAAG,OAAQtH,EAAa8G,GAAa,IAAMC,EAAuB,YAAYO,GAAG,QAAStH,EAAa8G,GAAa,IAAMG,EAAkB,IAAMF,EAAuB,iBACpMnF,KAAK6D,WAAa,GAAI5F,GAAM0H,WAAW3F,KAAKH,SACxC+F,SAAS,EACT3C,OAAQoC,EACRQ,IAAK,SAAU1D,GACXA,EAAE2D,MAAMC,cAAgB5D,EAAE6D,MAAMD,cAChChG,EAAKkG,SAAS9D,EAAE2D,UAGxB9F,KAAKH,QAAQoF,IAAI,mBAAoB,KAEzCiB,cAAe,SAAU/D,GAAV,GACPgE,GAAOpI,EAAEoE,EAAE4D,eAAezB,KAAK,QAC/B3B,EAASwD,GAAoB,MAAZA,EAAK,IAAcnG,KAAKF,QAAQ2B,gBAChDkB,IAAUlD,EAAU1B,EAAEoE,EAAE4D,eAAgB,QAAUrH,GACnDyD,EAAEoC,kBAGV0B,SAAU,SAAU9D,GAChB,KAAIA,EAAEiE,MAAQ,GAAKjE,EAAEkE,sBAArB,CAGA,GAAIC,GAAOtG,KAAMuG,EAAOxI,EAAEoE,EAAE4D,eAAgBrF,EAAajB,EAAU8G,EAAM,cAAeC,EAAM/G,EAAU8G,EAAM,QAAU,GAAIE,EAAShH,EAAU8G,EAAM,UAAWJ,EAAOI,EAAKjC,KAAK3F,GAAO+H,EAAkBvH,GAA2D,IAAzBoH,EAAK,GAAGI,aAAoBhE,EAASwD,GAAoB,MAAZA,EAAK,IAAcnG,KAAKF,QAAQ2B,gBACrTiF,IAAmB/D,GAAU6D,IAAQ9H,GAA4B,IAATyH,GAAwBA,IAASvH,IAG7F2H,EAAKjC,KAAK3F,EAAMC,GAChBgI,WAAW,WACPL,EAAKjC,KAAK3F,EAAMwH,KAEhBK,EAAIK,MAAMtH,IACVtB,EAAMkF,eAAepF,EAAEoI,GAAO9H,GAAIyI,QAAQP,GAC9B,gBAARC,GAAiC,WAARA,GACzBrE,EAAE4E,oBAGS,SAAXN,EACAH,EAAOnI,EAAO4G,YAAYuB,KACnBG,IACPH,EAAOvI,EAAE,IAAM0I,GAAQO,KAAK,oBAEhCV,EAAKhD,SAAS6C,EAAMzF,IAExByB,EAAEoC,qBAGV7E,GAAKuH,KAAO,SAAUpH,GACbA,EAAQmF,GAAG5G,EAAa,WACzByB,EAAUA,EAAQoH,KAAK,aAAehJ,EAAMmH,GAAK,0CAA0C8B,SAE/F,IAAIC,GAAgBtH,EAAQoH,KAAK,kDAAkDC,SAAUZ,EAAO,GAAI5G,GAAKyH,EAE7G,OADAb,GAAKhD,SAAS,IACPgD,GAEXjI,EAAG+I,OAAO1H,IACZxB,OAAOD,MAAMoJ,QACRnJ,OAAOD,OACE,kBAAVH,SAAwBA,OAAOwJ,IAAMxJ,OAAS,SAAUyJ,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.pane.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.pane', [\n        'kendo.mobile.view',\n        'kendo.mobile.loader'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.pane',\n        name: 'Pane',\n        category: 'mobile',\n        description: 'Mobile Pane',\n        depends: [\n            'mobile.view',\n            'mobile.loader'\n        ],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, mobile = kendo.mobile, roleSelector = kendo.roleSelector, ui = mobile.ui, Widget = ui.Widget, ViewEngine = mobile.ViewEngine, View = ui.View, Loader = mobile.ui.Loader, EXTERNAL = 'external', HREF = 'href', DUMMY_HREF = '#!', NAVIGATE = 'navigate', VIEW_SHOW = 'viewShow', SAME_VIEW_REQUESTED = 'sameViewRequested', OS = kendo.support.mobileOS, SKIP_TRANSITION_ON_BACK_BUTTON = OS.ios && !OS.appMode && OS.flatVersion >= 700, WIDGET_RELS = /popover|actionsheet|modalview|drawer/, BACK = '#:back', attrValue = kendo.attrValue;\n        var Pane = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                element = that.element;\n                element.addClass('km-pane');\n                if (that.options.collapsible) {\n                    element.addClass('km-collapsible-pane');\n                }\n                this.history = [];\n                this.historyCallback = function (url, params, backButtonPressed) {\n                    var transition = that.transition;\n                    that.transition = null;\n                    if (SKIP_TRANSITION_ON_BACK_BUTTON && backButtonPressed) {\n                        transition = 'none';\n                    }\n                    return that.viewEngine.showView(url, transition, params);\n                };\n                this._historyNavigate = function (url) {\n                    if (url === BACK) {\n                        if (that.history.length === 1) {\n                            return;\n                        }\n                        that.history.pop();\n                        url = that.history[that.history.length - 1];\n                    } else {\n                        that.history.push(url);\n                    }\n                    that.historyCallback(url, kendo.parseQueryStringParams(url));\n                };\n                this._historyReplace = function (url) {\n                    var params = kendo.parseQueryStringParams(url);\n                    that.history[that.history.length - 1] = url;\n                    that.historyCallback(url, params);\n                };\n                that.loader = new Loader(element, { loading: that.options.loading });\n                that.viewEngine = new ViewEngine({\n                    container: element,\n                    transition: options.transition,\n                    modelScope: options.modelScope,\n                    rootNeeded: !options.initial,\n                    serverNavigation: options.serverNavigation,\n                    remoteViewURLPrefix: options.root || '',\n                    layout: options.layout,\n                    $angular: options.$angular,\n                    loader: that.loader,\n                    showStart: function () {\n                        that.loader.transition();\n                        that.closeActiveDialogs();\n                    },\n                    after: function () {\n                        that.loader.transitionDone();\n                    },\n                    viewShow: function (e) {\n                        that.trigger(VIEW_SHOW, e);\n                    },\n                    loadStart: function () {\n                        that.loader.show();\n                    },\n                    loadComplete: function () {\n                        that.loader.hide();\n                    },\n                    sameViewRequested: function () {\n                        that.trigger(SAME_VIEW_REQUESTED);\n                    },\n                    viewTypeDetermined: function (e) {\n                        if (!e.remote || !that.options.serverNavigation) {\n                            that.trigger(NAVIGATE, { url: e.url });\n                        }\n                    }\n                });\n                this._setPortraitWidth();\n                kendo.onResize(function () {\n                    that._setPortraitWidth();\n                });\n                that._setupAppLinks();\n            },\n            closeActiveDialogs: function () {\n                var dialogs = this.element.find(roleSelector('actionsheet popover modalview')).filter(':visible');\n                dialogs.each(function () {\n                    kendo.widgetInstance($(this), ui).close();\n                });\n            },\n            navigateToInitial: function () {\n                var initial = this.options.initial;\n                if (initial) {\n                    this.navigate(initial);\n                }\n                return initial;\n            },\n            options: {\n                name: 'Pane',\n                portraitWidth: '',\n                transition: '',\n                layout: '',\n                collapsible: false,\n                initial: null,\n                modelScope: window,\n                loading: '<h1>Loading...</h1>'\n            },\n            events: [\n                NAVIGATE,\n                VIEW_SHOW,\n                SAME_VIEW_REQUESTED\n            ],\n            append: function (html) {\n                return this.viewEngine.append(html);\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.viewEngine.destroy();\n                this.userEvents.destroy();\n            },\n            navigate: function (url, transition) {\n                if (url instanceof View) {\n                    url = url.id;\n                }\n                this.transition = transition;\n                this._historyNavigate(url);\n            },\n            replace: function (url, transition) {\n                if (url instanceof View) {\n                    url = url.id;\n                }\n                this.transition = transition;\n                this._historyReplace(url);\n            },\n            bindToRouter: function (router) {\n                var that = this, history = this.history, viewEngine = this.viewEngine;\n                router.bind('init', function (e) {\n                    var url = e.url, attrUrl = router.pushState ? url : '/';\n                    viewEngine.rootView.attr(kendo.attr('url'), attrUrl);\n                    var length = history.length;\n                    if (url === '/' && length) {\n                        router.navigate(history[length - 1], true);\n                        e.preventDefault();\n                    }\n                });\n                router.bind('routeMissing', function (e) {\n                    if (!that.historyCallback(e.url, e.params, e.backButtonPressed)) {\n                        e.preventDefault();\n                    }\n                });\n                router.bind('same', function () {\n                    that.trigger(SAME_VIEW_REQUESTED);\n                });\n                that._historyNavigate = function (url) {\n                    router.navigate(url);\n                };\n                that._historyReplace = function (url) {\n                    router.replace(url);\n                };\n            },\n            hideLoading: function () {\n                this.loader.hide();\n            },\n            showLoading: function () {\n                this.loader.show();\n            },\n            changeLoadingMessage: function (message) {\n                this.loader.changeMessage(message);\n            },\n            view: function () {\n                return this.viewEngine.view();\n            },\n            _setPortraitWidth: function () {\n                var width, portraitWidth = this.options.portraitWidth;\n                if (portraitWidth) {\n                    width = kendo.mobile.application.element.is('.km-vertical') ? portraitWidth : 'auto';\n                    this.element.css('width', width);\n                }\n            },\n            _setupAppLinks: function () {\n                var that = this, linkRoles = 'tab', pressedButtonSelector = '[data-' + kendo.ns + 'navigate-on-press]', buttonSelectors = $.map([\n                        'button',\n                        'backbutton',\n                        'detailbutton',\n                        'listview-link'\n                    ], function (role) {\n                        return roleSelector(role) + ':not(' + pressedButtonSelector + ')';\n                    }).join(',');\n                this.element.handler(this).on('down', roleSelector(linkRoles) + ',' + pressedButtonSelector, '_mouseup').on('click', roleSelector(linkRoles) + ',' + buttonSelectors + ',' + pressedButtonSelector, '_appLinkClick');\n                this.userEvents = new kendo.UserEvents(this.element, {\n                    fastTap: true,\n                    filter: buttonSelectors,\n                    tap: function (e) {\n                        e.event.currentTarget = e.touch.currentTarget;\n                        that._mouseup(e.event);\n                    }\n                });\n                this.element.css('-ms-touch-action', '');\n            },\n            _appLinkClick: function (e) {\n                var href = $(e.currentTarget).attr('href');\n                var remote = href && href[0] !== '#' && this.options.serverNavigation;\n                if (!remote && attrValue($(e.currentTarget), 'rel') != EXTERNAL) {\n                    e.preventDefault();\n                }\n            },\n            _mouseup: function (e) {\n                if (e.which > 1 || e.isDefaultPrevented()) {\n                    return;\n                }\n                var pane = this, link = $(e.currentTarget), transition = attrValue(link, 'transition'), rel = attrValue(link, 'rel') || '', target = attrValue(link, 'target'), href = link.attr(HREF), delayedTouchEnd = SKIP_TRANSITION_ON_BACK_BUTTON && link[0].offsetHeight === 0, remote = href && href[0] !== '#' && this.options.serverNavigation;\n                if (delayedTouchEnd || remote || rel === EXTERNAL || typeof href === 'undefined' || href === DUMMY_HREF) {\n                    return;\n                }\n                link.attr(HREF, DUMMY_HREF);\n                setTimeout(function () {\n                    link.attr(HREF, href);\n                });\n                if (rel.match(WIDGET_RELS)) {\n                    kendo.widgetInstance($(href), ui).openFor(link);\n                    if (rel === 'actionsheet' || rel === 'drawer') {\n                        e.stopPropagation();\n                    }\n                } else {\n                    if (target === '_top') {\n                        pane = mobile.application.pane;\n                    } else if (target) {\n                        pane = $('#' + target).data('kendoMobilePane');\n                    }\n                    pane.navigate(href, transition);\n                }\n                e.preventDefault();\n            }\n        });\n        Pane.wrap = function (element) {\n            if (!element.is(roleSelector('view'))) {\n                element = element.wrap('<div data-' + kendo.ns + 'role=\"view\" data-stretch=\"true\"></div>').parent();\n            }\n            var paneContainer = element.wrap('<div class=\"km-pane-wrapper\"><div></div></div>').parent(), pane = new Pane(paneContainer);\n            pane.navigate('');\n            return pane;\n        };\n        ui.plugin(Pane);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}