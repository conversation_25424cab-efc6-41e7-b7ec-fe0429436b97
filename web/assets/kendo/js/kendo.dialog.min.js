/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.dialog.min",["kendo.core.min","kendo.popup.min"],t)}(function(){return function(t,e){function n(t){return e!==t}function i(t,e,n){return Math.max(Math.min(parseInt(t,10),n===1/0?n:parseInt(n,10)),parseInt(e,10))}function o(t){return t.keyCode==v.ENTER||t.keyCode==v.SPACEBAR}var a,r,s,l,c,d,p,u,h=window.kendo,f=h.ui.Widget,m=h.ui.Popup.TabKeyTrap,_=t.proxy,g=h.template,v=h.keys,w=t.isFunction,k="kendoWindow",y=".k-dialog",b=".k-window",x=".k-dialog-close",C="k-content k-window-content k-dialog-content",O=".k-window-content",T=".k-content",S="k-scroll",A="k-dialog-titleless",F=".k-dialog-title",H=F+"bar",I=".k-dialog-buttongroup",E=".k-button",z="k-alert",D="k-confirm",R="k-prompt",W=".k-textbox",B=".k-overlay",M=":visible",N="zIndex",L="body",K="initOpen",j="touchstart",P="touchmove",Y="open",Q="close",V="show",q="hide",G="width",J={small:"k-window-sm",medium:"k-window-md",large:"k-window-lg"},U="hidden",X="overflow",Z="original-overflow-rule",$="tap-y",tt=100,et=h.support.cssFlexbox,nt={okText:"OK",cancel:"Cancel",promptInput:"Input"},it=Math.ceil,ot=":not(link,meta,script,style)",at=f.extend({init:function(t,e){var n=this;f.fn.init.call(n,t,e),n._init(n.element,n.options),h.notify(n)},_init:function(i,o){var r,s=this;s._centerCallback=_(s._center,s),s.appendTo=t(L),n(o.visible)&&null!==o.visible||(o.visible=i.is(M)),s.wrapperTemplate===e&&(s.wrapperTemplate=a.wrapper),s._createDialog(),r=s.wrapper=i.closest(y),o._defaultFocus===e&&(s._defaultFocus=i[0]),s._tabindex(i),s._dimensions(),this._tabKeyTrap=new m(r),s.options.visible?s._triggerOpen():s.wrapper.hide()},setOptions:function(n){var i,o=this,a=o.options.size;n=t.extend(o.options,n),f.fn.setOptions.call(o,n),n.title!==e&&o.title(n.title),n.content&&(h.destroy(o.element.children()),o.element.html(n.content)),n.actions&&(o.wrapper.children(I).remove(),o._createActionbar(o.wrapper)),o.wrapper.show(),o._closable(o.wrapper),o.wrapper.removeClass(J[a]),o._dimensions(),n.visible?o._triggerOpen():o.wrapper.hide(),e!==n.modal&&(i=o.options.visible!==!1,o._enableDocumentScrolling(),o._overlay(n.modal&&i))},_dimensions:function(){var t,e,n=this,o=n.wrapper,a=n.options,r=a.width,s=a.height,l=a.size,c=["minWidth","minHeight","maxWidth","maxHeight"];for(t=0;t<c.length;t++)e=a[c[t]],e&&e!=1/0&&o.css(c[t],e);this._setElementMaxHeight(),r&&((""+r).indexOf("%")>0?o.width(r):o.outerWidth(i(r,a.minWidth,a.maxWidth))),s&&((""+s).indexOf("%")>0?o.height(s):o.outerHeight(i(s,a.minHeight,a.maxHeight)),this._setElementHeight()),l&&J[l]&&o.addClass(J[l])},_setElementMaxHeight:function(){var t,e,n=this,i=n.element,o=n.options.maxHeight;o!=1/0&&(t=n._paddingBox(i),e=parseFloat(o,10)-n._uiHeight()-t.vertical,e>0&&i.css({maxHeight:it(e)+"px"}))},_paddingBox:function(t){var e=parseFloat(t.css("padding-top"),10),n=parseFloat(t.css("padding-left"),10),i=parseFloat(t.css("padding-bottom"),10),o=parseFloat(t.css("padding-right"),10);return{vertical:e+i,horizontal:n+o}},_setElementHeight:function(){var t=this,e=t.element,n=t.options.height,i=t._paddingBox(e),o=parseFloat(n,10)-t._uiHeight()-i.vertical;o<0&&(o=0),e.css({height:it(o)+"px"}),this._applyScrollClassName(e)},_applyScrollClassName:function(t){var e=t.get(0).scrollHeight>t.outerHeight();e?t.addClass(S):t.removeClass(S)},_uiHeight:function(){var t=this,e=t.wrapper,n=e.children(I),i=n[0]&&n[0].offsetHeight||0,o=e.children(H),a=o[0]&&o[0].offsetHeight||0;return i+a},_overlay:function(e){var n=this.appendTo.children(B),i=this.wrapper;return n.length||(n=t(a.overlay)),n.insertBefore(i[0]).toggle(e).css(N,parseInt(i.css(N),10)-1),e?this._waiAriaOverlay():this._removeWaiAriaOverlay(),this.options.modal.preventScroll&&this._stopDocumentScrolling(),n},_waiAriaOverlay:function(){var e=this.wrapper;this._overlayedNodes=e.prevAll(ot).add(e.nextAll(ot)).each(function(){var e=t(this);e.data("ariaHidden",e.attr("aria-hidden")),e.attr("aria-hidden","true")})},_removeWaiAriaOverlay:function(){return this._overlayedNodes&&this._overlayedNodes.each(function(){var e=t(this),n=e.data("ariaHidden");n?e.attr("aria-hidden",n):e.removeAttr("aria-hidden")})},_closeClick:function(t){t.preventDefault(),this.close(!1)},_closeKeyHandler:function(t){(o(t)||t.keyCode==v.ESC)&&this.close(!1)},_keydown:function(t){var e=this,n=e.options,i=t.keyCode;i==v.ESC&&!e._closing&&n.closable&&e.close(!1)},_createDialog:function(){var e=this,n=e.element,i=e.options,o=h.support.isRtl(n),r=t(a.titlebar(i)),s=(n.id||h.guid())+"_title",l=t(e.wrapperTemplate(i));l.toggleClass("k-rtl",o),n.addClass(C),e.appendTo.append(l),i.title!==!1?(l.append(r),r.attr("id",s),l.attr("aria-labelledby",s)):l.addClass(A),e._closable(l),l.append(n),i.content&&(h.destroy(n.children()),n.html(i.content)),i.actions.length&&e._createActionbar(l)},_closable:function(t){var e=this,n=e.options,i=t.children(H),o=i.find(".k-window-actions"),r=o.length?o.find(".k-dialog-close"):t.find(".k-dialog-close");r.remove(),n.closable!==!1&&(n.title!==!1&&o.length?o.append(a.close(n)):t.prepend(a.close(n)),t.autoApplyNS(k),e.element.autoApplyNS(k),t.find(x).on("click",_(e._closeClick,e)).on("keydown",_(e._closeKeyHandler,e)),e.element.on("keydown",_(e._keydown,e)))},_createActionbar:function(e){var n="stretched"===this.options.buttonLayout,i=n?"stretched":"normal",o=t(a.actionbar({buttonLayout:i}));this._addButtons(o),n&&!et&&this._normalizeButtonSize(o),e.append(o)},_addButtons:function(e){var n,i,o,r,s=this,l=s.options,c=_(s._actionClick,s),d=_(s._actionKeyHandler,s),p=s.options.actions,u=p.length,h=Math.round(tt/u);for(o=0;o<u;o++)n=p[o],i=s._mergeTextWithOptions(n),r=t(a.action(n)).autoApplyNS(k).html(i).appendTo(e).data("action",n.action).on("click",c).on("keydown",d),"stretched"!==l.buttonLayout||et||(o==u-1&&(h=tt-o*h),r.css(G,h+"%"))},_mergeTextWithOptions:function(t){var e=t.text;return e?g(e)(this.options):""},_normalizeButtonSize:function(t){var e=this,n=e.options,i=t.children(E+":last"),o=parseFloat(i[0]?i[0].style[G]:0),a=tt-n.actions.length*o;a>0&&i.css(G,o+a+"%")},_tabindex:function(t){var e,n=this,i=n.wrapper,o=i.find(x),a=i.find(I+" "+E);f.fn._tabindex.call(this,t),e=t.attr("tabindex"),o.attr("tabIndex",e),a.attr("tabIndex",e)},_actionClick:function(t){this.wrapper.is(M)&&this._runActionBtn(t.currentTarget)},_actionKeyHandler:function(t){o(t)?this._runActionBtn(t.currentTarget):t.keyCode==v.ESC&&this.close(!1)},_runActionBtn:function(e){var n,i,o=this;o._closing||(n=t(e).data("action"),i=w(n)&&n({sender:o})===!1,i||o.close(!1))},_triggerOpen:function(){var t=this,e=t.options,n=t.wrapper;t.toFront(),t._triggerInitOpen(),t.trigger(Y),e.modal&&(t._overlay(n.is(M)).css({opacity:.5}),t._focusDialog())},open:function(){var t,e,n,i=this,o=i.wrapper,a=this._animationOptions(Y),r=i.options;return this._triggerInitOpen(),i.trigger(Y)||(i._closing&&o.kendoStop(!0,!0),i._closing=!1,i.toFront(),r.visible=!0,r.modal&&(e=!!i._modals().length,t=i._overlay(e),t.kendoStop(!0,!0),a.duration&&h.effects.Fade&&!e?(n=h.fx(t).fadeIn(),n.duration(a.duration||0),n.endValue(.5),n.play()):t.css("opacity",.5),t.show()),o.show().kendoStop().kendoAnimate({effects:a.effects,duration:a.duration,complete:_(i._openAnimationEnd,i)}),o.show()),i},_animationOptions:function(t){var e=this.options.animation,n={open:{effects:{}},close:{hide:!0,effects:{}}};return e&&e[t]||n[t]},_openAnimationEnd:function(){this.options.modal&&this._focusDialog(),this.trigger(V)},_triggerInitOpen:function(){n(this._initOpenTriggered)||(this._initOpenTriggered=!0,this.trigger(K))},toFront:function(){var e=this,n=e.wrapper,i=+n.css(N),o=i;return e.center(),t(b).each(function(e,n){var o=t(n),a=o.css(N);isNaN(a)||(i=Math.max(+a,i))}),(!n[0].style.zIndex||o<i)&&n.css(N,i+2),e.element.find("> .k-overlay").remove(),n=null,e},close:function(t){return arguments.length||(t=!0),this._close(t),this._stopCenterOnResize(),this},_close:function(t){var e=this,n=e.wrapper,i=e.options,o=this._animationOptions("open"),a=this._animationOptions("close");if(n.is(M)&&!e.trigger(Q,{userTriggered:!t})){if(e._closing)return;e._closing=!0,i.visible=!1,this._removeOverlay(),n.kendoStop().kendoAnimate({effects:a.effects||o.effects,reverse:a.reverse===!0,duration:a.duration,complete:_(this._closeAnimationEnd,this)})}return e},center:function(){this._center(),this._centerOnResize()},_center:function(){var e=this,n=e.wrapper,i=t(window),o=0,a=0,r=a+Math.max(0,(i.width()-n.width())/2),s=o+Math.max(0,(i.height()-n.height()-parseInt(n.css("paddingTop"),10))/2);return n.css({left:r,top:s}),e},_centerOnResize:function(){this._trackResize||(h.onResize(this._centerCallback),this._trackResize=!0)},_stopCenterOnResize:function(){h.unbindResize(this._centerCallback),this._trackResize=!1},_removeOverlay:function(){var t=this._modals(),e=this.options,n=e.modal&&!t.length;n?(this._overlay(!1).remove(),e.modal.preventScroll&&this._enableDocumentScrolling()):t.length&&(this._object(t.last())._overlay(!0),e.modal.preventScroll&&this._stopDocumentScrolling())},_stopDocumentScrolling:function(){var e,n,i=this,o=t("body");i._storeOverflowRule(o),o.css(X,U),e=t("html"),n=e[0],i._storeOverflowRule(e),e.css(X,U),h.support.mobileOS.ios&&(n.addEventListener(j,i._touchStart,{passive:!1}),n.addEventListener(P,i._touchMove,{passive:!1}))},_touchStart:function(e){t(this).data($,e.changedTouches[0].pageY)},_touchMove:function(e){var n=e.target,i=t(e.target),o=e.changedTouches[0].pageY-t(this).data($)>0,a=i.is(O)&&o&&0===i.scrollTop()||!o&&i.scrollTop()===n.scrollHeight-n.clientHeight;i.is(O)&&!a||e.preventDefault()},_enableDocumentScrolling:function(){var e=this,n=t(document.body),i=t("html"),o=i[0];e._restoreOverflowRule(n),e._restoreOverflowRule(i),h.support.mobileOS.ios&&(i.removeData($),o.removeEventListener(j,e._touchStart,{passive:!1}),o.removeEventListener(P,e._touchMove,{passive:!1}))},_storeOverflowRule:function(t){if(!this._isOverflowStored(t)){var e=t.get(0).style.overflow;"string"==typeof e&&t.data(Z,e)}},_isOverflowStored:function(t){return"string"==typeof t.data(Z)},_restoreOverflowRule:function(t){var n=t.data(Z);null!==n&&n!==e?(t.css(X,n),t.removeData(Z)):t.css(X,"")},_closeAnimationEnd:function(){var t,e=this;e._closing=!1,e.wrapper.hide().css("opacity",""),e.trigger(q),e.options.modal&&(t=e._object(e._modals().last()),t&&t.toFront())},_modals:function(){var e=this,n=t(b).filter(function(){var n=t(this),i=e._object(n),o=i&&i.options;return o&&o.modal&&e.options.appendTo==o.appendTo&&o.visible&&n.is(M)}).sort(function(e,n){return+t(e).css("zIndex")-+t(n).css("zIndex")});return e=null,n},_object:function(t){var n=t.children(T),i=h.widgetInstance(n);return i?i:e},destroy:function(){var e=this;e._destroy(),f.fn.destroy.call(e),e.wrapper.remove(),e.wrapper=e.element=t()},_destroy:function(){var t=this,e="."+k;t.wrapper.off(e),t.element.off(e),t.wrapper.find(x+","+I+" > "+E).off(e),t._stopCenterOnResize()},title:function(e){var n=this,i=n.wrapper,o=n.options,r=i.children(H),s=r.children(F),l=h.htmlEncode(e);return arguments.length?(e===!1?(r.remove(),i.addClass(A)):(r.length||(r=t(a.titlebar(o)).prependTo(i),s=r.children(F),i.removeClass(A)),s.html(l)),n.options.title=l,n):s.html()},content:function(t,e){var i=this,o=i.wrapper.children(T);return n(t)?(this.angular("cleanup",function(){return{elements:o.children()}}),h.destroy(o.children()),o.html(t),this.angular("compile",function(){var t,n=[];for(t=o.length;--t>=0;)n.push({dataItem:e});return{elements:o.children(),data:n}}),i.options.content=t,i):o.html()},_focusDialog:function(){this._defaultFocus&&this._focus(this._defaultFocus),this._tabKeyTrap.trap()},_focus:function(t){t&&t.focus()},events:[K,Y,Q,V,q],options:{title:"",buttonLayout:"stretched",actions:[],modal:!0,size:"auto",width:null,height:null,minWidth:0,minHeight:0,maxWidth:1/0,maxHeight:1/0,content:null,visible:null,appendTo:L,closable:!0}}),rt=at.extend({options:{name:"Dialog",messages:{close:"Close"}}});h.ui.plugin(rt),r=at.extend({_init:function(t,e){var n=this;n.wrapperTemplate=a.alertWrapper,e._defaultFocus=null,n._ensureContentId(t),at.fn._init.call(n,t,e),n.bind(q,_(n.destroy,n)),n._ariaDescribedBy(),n._initFocus()},_ensureContentId:function(e){var n=t(e);n.attr("id")||n.attr("id",h.guid()+"_k-popup")},_ariaDescribedBy:function(){this.wrapper.attr("aria-describedby",this.element.attr("id"))},_initFocus:function(){var t=this.options;this._defaultFocus=this._chooseEntryFocus(),this._defaultFocus&&t.visible&&t.modal&&this._focusDialog()},_chooseEntryFocus:function(){return this.wrapper.find(I+" > "+E)[0]},options:{title:window.location.host,closable:!1,messages:nt}}),s=r.extend({_init:function(t,e){var n=this;r.fn._init.call(n,t,e),n.wrapper.addClass(z)},options:{name:"Alert",modal:!0,actions:[{text:"#: messages.okText #"}]}}),h.ui.plugin(s),l=function(e){return t(a.alert).kendoAlert({content:e}).data("kendoAlert").open()},c=r.extend({_init:function(e,n){var i=this;r.fn._init.call(i,e,n),i.wrapper.addClass(D),i.result=t.Deferred()},options:{name:"Confirm",modal:!0,actions:[{text:"#: messages.okText #",primary:!0,action:function(t){t.sender.result.resolve()}},{text:"#: messages.cancel #",action:function(t){t.sender.result.reject()}}]}}),h.ui.plugin(c),d=function(e){var n=t(a.confirm).kendoConfirm({content:e}).data("kendoConfirm").open();return n.result},p=r.extend({_init:function(e,n){var i=this;r.fn._init.call(i,e,n),i.wrapper.addClass(R),i._createPrompt(),i.result=t.Deferred()},_createPrompt:function(){var e=this.options.value,n=t(a.promptInputContainer(this.options)).insertAfter(this.element);e&&n.children(W).val(e),this._defaultFocus=this._chooseEntryFocus(),this._focusDialog()},_chooseEntryFocus:function(){return this.wrapper.find(W)[0]},options:{name:"Prompt",modal:!0,value:"",actions:[{text:"#: messages.okText #",primary:!0,action:function(t){var e=t.sender,n=e.wrapper.find(W).val();e.result.resolve(n)}},{text:"#: messages.cancel #",action:function(t){var e=t.sender,n=e.wrapper.find(W).val();t.sender.result.reject(n)}}]}}),h.ui.plugin(p),u=function(e,n){var i=t(a.prompt).kendoPrompt({content:e,value:n}).data("kendoPrompt").open();return i.result},a={wrapper:g("<div class='k-widget k-window k-dialog' role='dialog' />"),action:g("<button type='button' class='k-button# if (data.primary) { # k-primary# } role='button' #'></button>"),titlebar:g("<div class='k-window-titlebar k-dialog-titlebar k-header'><span class='k-window-title k-dialog-title'>#: title #</span><div class='k-window-actions k-dialog-actions' /></div>"),close:g("<a role='button' href='\\#' class='k-button k-bare k-button-icon k-window-action k-dialog-action k-dialog-close' title='#: messages.close #' aria-label='#: messages.close #' tabindex='-1'><span class='k-icon k-i-close'></span></a>"),actionbar:g("<div class='k-dialog-buttongroup k-dialog-button-layout-#: buttonLayout #' role='toolbar' />"),overlay:"<div class='k-overlay' />",alertWrapper:g("<div class='k-widget k-window k-dialog' role='alertdialog' />"),alert:"<div />",confirm:"<div />",prompt:"<div />",promptInputContainer:g("<div class='k-prompt-container'><input type='text' class='k-textbox' title='#: messages.promptInput #' aria-label='#: messages.promptInput #' /></div>")},h.alert=l,h.confirm=d,h.prompt=u}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()});
//# sourceMappingURL=kendo.dialog.min.js.map
