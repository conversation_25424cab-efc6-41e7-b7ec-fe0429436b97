/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("util/text-metrics.min",["kendo.core.min"],e)}(function(){!function(e){function t(e){return(e+"").replace(s,p)}function i(e){var t,i=[];for(t in e)i.push(t+e[t]);return i.sort().join("")}function o(e){var t,i=2166136261;for(t=0;t<e.length;++t)i+=(i<<1)+(i<<4)+(i<<7)+(i<<8)+(i<<24),i^=e.charCodeAt(t);return i>>>0}function n(){return{width:0,height:0,baseline:0}}function a(e,t,i){return g.current.measure(e,t,i)}var r,s,p,l,d,g;window.kendo.util=window.kendo.util||{},r=kendo.Class.extend({init:function(e){this._size=e,this._length=0,this._map={}},put:function(e,t){var i=this._map,o={key:e,value:t};i[e]=o,this._head?(this._tail.newer=o,o.older=this._tail,this._tail=o):this._head=this._tail=o,this._length>=this._size?(i[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(e){var t=this._map[e];if(t)return t===this._head&&t!==this._tail&&(this._head=t.newer,this._head.older=null),t!==this._tail&&(t.older&&(t.older.newer=t.newer,t.newer.older=t.older),t.older=this._tail,t.newer=null,this._tail.newer=t,this._tail=t),t.value}}),s=/\r?\n|\r|\t/g,p=" ",l={baselineMarkerSize:1},"undefined"!=typeof document&&(d=document.createElement("div"),d.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),g=kendo.Class.extend({init:function(t){this._cache=new r(1e3),this.options=e.extend({},l,t)},measure:function(e,a,r){var s,p,l,g,h,c,u,f,_;if(void 0===r&&(r={}),!e)return n();if(s=i(a),p=o(e+s),l=this._cache.get(p))return l;g=n(),h=r.box||d,c=this._baselineMarker().cloneNode(!1);for(u in a)f=a[u],void 0!==f&&(h.style[u]=f);return _=r.normalizeText!==!1?t(e):e+"",h.textContent=_,h.appendChild(c),document.body.appendChild(h),_.length&&(g.width=h.offsetWidth-this.options.baselineMarkerSize,g.height=h.offsetHeight,g.baseline=c.offsetTop+this.options.baselineMarkerSize),g.width>0&&g.height>0&&this._cache.put(p,g),h.parentNode.removeChild(h),g},_baselineMarker:function(){var e=document.createElement("div");return e.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",e}}),g.current=new g,kendo.deepExtend(kendo.util,{LRUCache:r,TextMetrics:g,measureText:a,objectKey:i,hashKey:o,normalizeText:t})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("kendo.pdfviewer.min",["pdf-viewer/processors/pdfjs-processor.min","pdf-viewer/processors/dpl-processor.min","pdf-viewer/toolbar.min","pdf-viewer/page.min","pdf-viewer/dialogs.min","pdf-viewer/commands.min"],e)}(function(){return function(e,t){var i,o=".kendoPDFViewer",n=window.kendo,a=n.ui,r=e.proxy,s=e.extend,p=n.drawing,l=a.Widget,d=n.ui.progress,g="scroll",h="render",c="open",u="error",f="focus",_="#ffffff",m="tabindex",v={pdfjs:"pdfjs",dpl:"dpl"},w={viewer:"k-pdf-viewer k-widget",scroller:"k-canvas k-list-scroller"},b=l.extend({init:function(e,t){var i=this;l.fn.init.call(i,e,n.deepExtend({},this.options,t)),i._wrapper(),i.options.toolbar&&i._renderToolbar(),i.wrapper.on(f,r(i._focus,i)),i._initProcessor(t||{}),i._renderPageContainer(),i._loadDocument(),i._tabindex(),n.notify(i,n.ui)},events:[h,c,u],options:{name:"PDFViewer",view:{type:"canvas"},pdfjsProcessing:{file:null},dplProcessing:{read:{url:null,type:"GET",dataType:"json",pageField:"pageNumber"},upload:{url:null,saveField:"file"},download:{url:null},loadOnDemand:!1},toolbar:{items:[]},width:1e3,height:1200,page:1,defaultPageSize:{width:794,height:1123},messages:{defaultFileName:"Document",toolbar:{open:"Open",exportAs:"Export",download:"Download",pager:{first:"Go to the first page",previous:"Go to the previous page",next:"Go to the next page",last:"Go to the last page",of:" of {0} ",page:"page",pages:"pages"}},errorMessages:{notSupported:"Only pdf files allowed.",parseError:"PDF file fails to process.",notFound:"File is not found."},dialogs:{exportAsDialog:{title:"Export...",defaultFileName:"Document",pdf:"Portable Document Format (.pdf)",png:"Portable Network Graphics (.png)",svg:"Scalable Vector Graphics (.svg)",labels:{fileName:"File name",saveAsType:"Save as",page:"Page"}},okText:"OK",save:"Save",cancel:"Cancel"}}},_wrapper:function(){var e=this,t=e.options;e.wrapper=e.element,e.wrapper.width(t.width).height(t.height).addClass(w.viewer),e._resizeHandler=n.onResize(function(){e.resize()})},_focus:function(e){this.toolbar?this.toolbar.wrapper.focus():this.pageContainer.focus(),e.preventDefault()},_initProcessor:function(e){var t,o=this;t=e.dplProcessing?o.options.dplProcessing:o.options.pdfjsProcessing,o.processingLib=e.dplProcessing?v.dpl:v.pdfjs,o.processor=new n.pdfviewer[o.processingLib].processor(t,o),i=n.pdfviewer[o.processingLib].Page},_renderToolbar:function(){var t=this,i=t.options,o={pager:{messages:i.messages.toolbar.pager},resizable:!0,items:i.toolbar.items,width:i.width,action:t.execute.bind(t),messages:i.messages.toolbar},a=e("<div />");a.appendTo(t.element),t.toolbar=new n.pdfviewer.Toolbar(a,o)},_initErrorDialog:function(e){var t,i=this;return i._errorDialog||(e=s(e,{messages:i.options.messages}),t=new n.pdfviewer.dialogs.ErrorDialog(e),i._errorDialog=t._dialog),i._errorDialog},_renderPageContainer:function(){var t=this;t.pageContainer||(t.pageContainer=e("<div />"),t.pageContainer.addClass(w.scroller),t.pageContainer.attr(m,0),t.wrapper.append(t.pageContainer))},_triggerError:function(e){var t=this._initErrorDialog();s(e,{dialog:t}),this.pageContainer&&d(this.pageContainer,!1),this.trigger(u,e)||t.open().content(e.message)},_renderPages:function(){var e,n,a,p,l=this,d=l.document;if(l.pages=[],!d||!d.total)return l._renderBlankPage(),t;for(e=d.pages,n=1;n<=d.total;n++)p={processor:l.processor,number:n},e&&e.length&&(p=s(p,e[n-1])),a=new i(p,l),l.pages.push(a),l.pageContainer.append(a.element);l.pages.length>1&&l.pageContainer.on(g+o,r(l._scroll,l))},_renderBlankPage:function(){this._blankPage=new i(this.options.defaultPageSize,this),this.pageContainer.append(this._blankPage.element),this._updatePager(1,1)},_updatePager:function(e,t){this.toolbar&&this.toolbar.pager&&this.toolbar.pager.setOptions({page:e,total:t})},_resize:function(){var e,i=this,o=0,n=i.pageContainer[0].clientWidth,a=i.pageContainer[0].clientHeight;return i.pages&&i.pages.length?(i.toolbar&&i.toolbar.resize(!0),i._visiblePagesCount=1,i.pages.forEach(function(t){e=n/t.element.width(),t.resize(e),o+=t.element.height(),o<a&&t.pageNumber>1&&i._visiblePagesCount++}),t):(i._blankPage&&(e=n/i._blankPage.element.width(),i._blankPage.resize(e)),t)},_scroll:function(){var e,i,o,n=this,a=n.pageContainer[0].scrollHeight,r=n.pageContainer.height(),s=n.pageContainer.scrollTop(),p=n.pageContainer.offset().top,l=n.options.page,d=l-1,g=n.pages.length,h=l,c=n.pages[d],u=c.element.offset().top-p,f=c.element.height(),_=s-n._prevScrollTop>0?1:-1;return n._preventScroll?(n._preventScroll=!1,t):(_==-1&&n.pages[d+_]&&(e=n.pages[d-n._visiblePagesCount]||n.pages[d+_],i=e.element.offset().top-p,o=e.element.height()),Math.abs(s-(n._prevScrollTop||0))>r?h=Math.floor(s*(1/(a/g)))+1:u<0&&Math.abs(u)>=f/2&&1===_?h++:e&&Math.abs(i)<=o/2&&h--,l!==h&&h>=1&&h<=g&&(n.options.page=h,n._loadVisiblePages(),n._updatePager(h,g)),n._prevScrollTop=s,t)},execute:function(e){var t=s({viewer:this},e.options),i=new n.pdfviewer[e.command](t);i.exec()},_loadDocument:function(){var e=this,t=e.options.page;d(e.pageContainer,!0),e.processor.fetchDocument().done(function(i){e._clearPages(),e.document=i,e._renderPages(),e.resize(!0),i&&(t=t>=1&&t<=i.total?t:1,e.activatePage(t)),d(e.pageContainer,!1)})},loadPage:function(e){var t=this.pages&&this.pages[e-1];t&&t.load()},activatePage:function(e){var t=this.pages&&this.pages[e-1],i=this.pageContainer.scrollTop();t&&(this.options.page=e,this._loadVisiblePages(),this._preventScroll=!0,this.pageContainer.scrollTop(i+t.element.position().top),this._updatePager(e,this.pages.length))},_loadVisiblePages:function(){var e,t=this.pages&&this.pages.length,i=this.options.page,o=Math.min(i+this._visiblePagesCount,t);for(e=i;e<=o;e++)this.loadPage(e)},fromFile:function(e){this.processor._updateDocument(e),this._loadDocument()},exportImage:function(e){var t,i=this,o=e.page,a=i.pages[o-1]||i._blankPage,r=new p.Group;a.load(),t=n.drawing.Path.fromRect(new n.geometry.Rect([0,0],[a.width,a.height]),{fill:{color:_},stroke:null}),d(i.pageContainer,!0),r.append(t,a.group),p.exportImage(r).done(function(t){d(i.pageContainer,!1),n.saveAs({dataURI:t,fileName:e.fileName,proxyURL:e.proxyURL||"",forceProxy:e.forceProxy,proxyTarget:e.proxyTarget})})},exportSVG:function(e){var t=this,i=e.page,o=t.pages[i-1]||t._blankPage;d(t.pageContainer,!0),o.load(),p.exportSVG(o.group).done(function(i){d(t.pageContainer,!1),n.saveAs({dataURI:i,fileName:e.fileName,proxyURL:e.proxyURL||"",forceProxy:e.forceProxy,proxyTarget:e.proxyTarget})})},setOptions:function(t){var i=this;(t.pdfjsProcessing||t.dplProcessing)&&i._initProcessor(),t=e.extend(i.options,t),l.fn.setOptions.call(i,t),t.page&&i.activatePage(t.page),t.width&&i.element.width(t.width),t.height&&i.element.height(t.height)},destroy:function(){n.unbindResize(this._resizeHandler),this._errorDialog&&this._errorDialog.destroy(),this._saveDialog&&this._saveDialog.destroy(),this._upload&&this._upload.destroy(),this.toolbar&&(this.toolbar.unbind(),this.toolbar.destroy(),this.toolbar=null),this.pages&&this.pages.length&&(this.pages.forEach(function(e){e.destroy()}),this.pages=[]),this.pageContainer.off(o),l.fn.destroy.call(this)},_clearPages:function(){this.pages=[],this.document=null,this.options.page=1,this.pageContainer.empty(),this.pageContainer.off(g+o),this.pageContainer.scrollTop(0)}});a.plugin(b)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()});
//# sourceMappingURL=kendo.pdfviewer.min.js.map
