{"version": 3, "sources": ["kendo.scheduler.recurrence.js"], "names": ["f", "define", "$", "undefined", "intervalExcess", "diff", "interval", "excess", "dayInYear", "date", "month", "getMonth", "days", "leapYear", "DAYS_IN_LEAPYEAR", "DAYS_IN_YEAR", "getDate", "weekInYear", "weekStart", "year", "Date", "getFullYear", "adjustDST", "setDayOfWeek", "setDate", "getDay", "Math", "floor", "getTime", "weekInMonth", "firstWeekDay", "firstDayOfMonth", "firstWeekLength", "ceil", "normalizeDayIndex", "weekDay", "normalizeOffset", "rule", "lastDate", "weeksInMonth", "day", "<PERSON><PERSON><PERSON><PERSON>", "skipLast", "offset", "numberOfWeeks", "isInWeek", "ruleWeekValues", "weekDays", "ruleWeekOffset", "weekNumber", "currentDay", "length", "result", "idx", "push", "ruleValues", "rules", "value", "normalize", "ruleValue", "availableRules", "parseArray", "list", "range", "parseInt", "isNaN", "start", "end", "sort", "numberSortPredicate", "parseWeekDayList", "valueLength", "substring", "toUpperCase", "WEEK_DAYS_IDX", "serializeWeekDayList", "valueString", "WEEK_DAYS", "getMonthLength", "MONTHS", "a", "b", "parseExceptions", "exceptions", "zone", "dates", "split", "indexOf", "parseUTCDate", "isException", "isArray", "dateTime", "getMilliseconds", "toExceptionString", "concat", "kendo", "timezone", "convert", "getTimezoneOffset", "toString", "RECURRENCE_DATE_FORMAT", "join", "startPeriodByFreq", "freq", "setFullYear", "hours", "setHours", "minutes", "setMinutes", "seconds", "setSeconds", "endPeriodByFreq", "eventsByPosition", "periodEvents", "positions", "position", "event", "periodEventsLength", "events", "removeExceptionDates", "exceptionDates", "expand", "startTime", "endTime", "endDate", "durationMS", "startPeriod", "inPeriod", "ruleStart", "ruleEnd", "useEventStart", "freqName", "eventStartTime", "eventStartMS", "eventStart", "count", "currentIdx", "shiftedStart", "shiftedEnd", "shiftedStartTime", "shifterEndTime", "startZone", "endZone", "startOffsetDiff", "endOffsetDiff", "startTZOffsetDiff", "endTZOffsetDiff", "offsetTicksStart", "offsetTicksEnd", "isMissingDSTHour", "parseRule", "recurrenceRule", "clone", "recurrenceException", "exdates", "set", "frequencies", "until", "getHours", "getMinutes", "getSeconds", "_startPeriod", "kendoDate", "_endPeriod", "duration", "_startTime", "toInvariantTime", "setup", "limit", "setTime", "toUtcTime", "isAllDay", "startTimezone", "endTimezone", "getZoneOffset", "toOccurrence", "_endTime", "next", "slice", "isDSTMissingHour", "dateOffset", "dateMinusHour", "dateMinusHourOffset", "parseDate", "DATE_FORMATS", "apply", "parseDateRule", "dateRule", "pair", "property", "tzid", "valueIdx", "pairs", "recur", "splits", "part", "parts", "instance", "predicate", "day1", "day2", "trim", "toLowerCase", "monthDays", "yearDays", "months", "weeks", "culture", "calendar", "firstDay", "_hasRuleValue", "serializeDateRule", "val", "serialize", "ruleString", "weekDayCheckBoxes", "mobileWeekDayCheckBoxes", "RECURRENCE_VIEW_TEMPLATE", "DAY_RULE", "WEEKDAY_RULE", "WEEKEND_RULE", "BaseRecurrenceEditor", "RecurrenceEditor", "RECURRENCE_HEADER_TEMPLATE", "RECURRENCE_REPEAT_PATTERN_TEMPLATE", "RECURRENCE_END_PATTERN_TEMPLATE", "RECURRENCE_GROUP_BUTTON_TEMPLATE", "MobileRecurrenceEditor", "window", "Class", "ui", "Widget", "DropDownList", "0", "1", "2", "3", "4", "5", "6", "SU", "MO", "TU", "WE", "TH", "FR", "SA", "RULE_NAMES", "RULE_NAMES_LENGTH", "limitation", "monthRules", "changed", "setMonth", "month<PERSON><PERSON>th", "monthDay", "yearDay", "week", "weekDayRule", "weekDayRules", "hourRules", "startHours", "minuteRules", "currentMinutes", "secondRules", "BaseFrequency", "extend", "options", "this", "_hour", "ruleName", "firstRule", "modified", "current", "startTimeHours", "normalizedCurrentIndex", "normalizedStartIndex", "frequency", "MS_PER_HOUR", "MS_PER_MINUTE", "round", "MS_PER_DAY", "_date", "_getNumberOfWeeksBetweenDates", "first", "second", "exactWeeks", "HourlyFrequency", "fn", "DailyFrequency", "WeeklyFrequency", "eventStartDate", "MonthlyFrequency", "YearlyFrequency", "hourly", "daily", "weekly", "monthly", "yearly", "CLICK", "CHANGE", "recurrence", "parse", "shortNames", "namesShort", "values", "names", "template", "init", "element", "that", "call", "wrapper", "today", "_namespace", "name", "spinners", "mobile", "messages", "recurrenceEditorTitle", "never", "repeatEvery", "repeatOn", "of", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "third", "fourth", "last", "weekdays", "weekday", "weekend", "_initInterval", "_value", "_container", "find", "kendoNumericTextBox", "decimals", "format", "min", "change", "_trigger", "_weekDayRule", "clear", "_weekDay", "_weekDayOffset", "_weekDay<PERSON><PERSON>w", "weekDayOffset", "weekDayValue", "method", "weekDayOffsetWidget", "_initWeekDay", "data", "weekdayMessage", "offsetMessage", "weekDayInput", "dataTextField", "dataValueField", "dataSource", "text", "map", "day<PERSON><PERSON>", "_initWeekDays", "i", "l", "filter", "checkbox", "checked", "_initMonthDay", "monthDayInput", "_monthDay", "NumericTextBox", "max", "_initCount", "input", "_count", "_initUntil", "_until", "kendoDatePicker", "trigger", "_initFrequency", "_initContainer", "destroy", "_frequency", "off", "select", "_initView", "container", "edit<PERSON><PERSON><PERSON>", "parent", "insertAfter", "append", "ddl", "attr", "title", "html", "_initMonth", "_period", "_end", "monthInputs", "monthName", "_month1", "_month2", "endRule", "namespace", "click", "e", "_toggleEnd", "currentTarget", "_buttonNever", "_buttonCount", "_buttonUntil", "toggleRule", "_toggleMonthDay", "_toggleYear", "selector", "radioButtons", "_buttonMonthDay", "eq", "_buttonWeekDay", "enableCount", "enableUntil", "prop", "enable", "monthRule", "enableMonthDay", "enableWeekDay", "yearRule", "enableMonth1", "enableMonth2", "plugin", "_optionTemplate", "_pane", "pane", "_initRepeatButton", "_initParentRepeatEnd", "_defaultValue", "animations", "left", "right", "cancel", "update", "endTitle", "repeatTitle", "headerTitle", "patterns", "repeatBy", "dayOfMonth", "dayOfWeek", "every", "_destroyView", "_endFields", "target", "_chevronButton", "_repeatValue", "parents", "preventDefault", "_createView", "navigate", "_view", "_endLiItem", "endLabelField", "endEditField", "appendTo", "_navigateT<PERSON><PERSON>iew", "_endParentLabelField", "toggle", "_endParentEndButton", "_endText", "_initRepeatEnd", "_end<PERSON><PERSON><PERSON>Field", "toggleClass", "_endButton", "viewName", "frequencyMessages", "ns", "_initRepeatView", "_initEndNavigation", "endMessages", "_initEndView", "viewType", "returnViewId", "view", "id", "stopPropagation", "hasClass", "one", "remove", "isMobile", "content", "show", "hide", "ev", "endPattern", "weekDaySelect", "_options", "monthSelect", "monthNames", "_monthSelect", "currentValue", "repeatRuleGroupButton", "weekDayView", "monthDayView", "weekDayName", "support", "optionLabel", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,8BACH,qBACA,mBACA,wBACDD,IACL,WA20EE,MA/zEC,UAAUE,EAAGC,GAugBV,QAASC,GAAeC,EAAMC,GAC1B,GAAIC,EASJ,OARa,KAATF,GAAcA,EAAOC,EACrBC,EAASD,EAAWD,GAEpBE,EAASF,EAAOC,EACZC,IACAA,EAASD,EAAWC,IAGrBA,EAEX,QAASC,GAAUC,GAAnB,GACQC,GAAQD,EAAKE,WACbC,EAAOC,EAASJ,GAAQK,GAAiBJ,GAASK,GAAaL,EACnE,OAAOE,GAAOH,EAAKO,UAEvB,QAASC,GAAWR,EAAMS,GACtB,GAAIC,GAAMP,CAYV,OAXAH,GAAO,GAAIW,MAAKX,EAAKY,cAAeZ,EAAKE,WAAYF,EAAKO,WAC1DM,GAAUb,EAAM,GAChBU,EAAOV,EAAKY,cACRH,IAAcf,GACdoB,GAAad,EAAMS,MACnBT,EAAKe,QAAQf,EAAKO,UAAY,IAE9BP,EAAKe,QAAQf,EAAKO,WAAa,GAAKP,EAAKgB,UAAY,KAEzDH,GAAUb,EAAM,GAChBG,EAAOc,KAAKC,OAAOlB,EAAKmB,UAAY,GAAIR,MAAKD,EAAM,EAAG,SAAU,OACzD,EAAIO,KAAKC,MAAMf,EAAO,GAEjC,QAASiB,GAAYpB,EAAMS,GAA3B,GACQY,GAAeC,GAAgBtB,GAAMgB,SACrCO,EAAkB,GAAKF,EAAe,GAAKZ,GAAa,KAAO,CAInE,OAHIc,GAAkB,IAClBA,GAAmB,GAEhBN,KAAKO,MAAMxB,EAAKO,UAAYgB,GAAmB,GAAK,EAE/D,QAASE,GAAkBC,EAASjB,GAChC,MAAOiB,IAAWA,EAAUjB,EAAY,EAAI,GAEhD,QAASkB,GAAgB3B,EAAM4B,EAAMnB,GAArC,GAKQoB,GACAC,EACAC,EACAC,EACAC,EARAC,EAASN,EAAKM,MAClB,OAAKA,IAGDL,EAAW,GAAIlB,MAAKX,EAAKY,cAAeZ,EAAKE,WAAa,EAAG,GAC7D4B,EAAeV,EAAYS,EAAUpB,GACrCsB,EAAMN,EAAkBG,EAAKG,IAAKtB,GAClCuB,EAAYD,EAAMN,EAAkB,GAAId,MAAKX,EAAKY,cAAeZ,EAAKE,WAAY,GAAGc,SAAUP,GAC/FwB,EAAWF,EAAMN,EAAkBI,EAASb,SAAUP,GACtDyB,EAAS,EACTA,EAASJ,GAAgBI,EAAS,GAAKD,EAAW,EAAI,IAC/CD,IACPE,GAAU,GAEdJ,GAAgBG,EAAW,EAAI,EAC3BC,GAAUF,EAAY,EAAI,IAAME,EAASJ,EAClC,KAEJI,GAhBId,EAAYpB,EAAMS,GAkBjC,QAAS0B,GAAcnC,EAAMS,GACzB,MAAOW,GAAY,GAAIT,MAAKX,EAAKY,cAAeZ,EAAKE,WAAa,EAAG,GAAIO,GAE7E,QAAS2B,GAASpC,EAAM4B,EAAMnB,GAC1B,MAAOW,GAAYpB,EAAMS,KAAekB,EAAgB3B,EAAM4B,EAAMnB,GAExE,QAAS4B,GAAeC,EAAUtC,EAAMS,GAQpC,IARJ,GAGQ8B,GACAb,EAASK,EACTS,EAJAC,EAAahB,EAAkBzB,EAAKgB,SAAUP,GAC9CiC,EAASJ,EAASI,OAIlBC,KACAC,EAAM,EACHA,EAAMF,EAAQE,IAIjB,GAHAlB,EAAUY,EAASM,GACnBJ,EAAapB,EAAYpB,EAAMS,GAC/B8B,EAAiBZ,EAAgB3B,EAAM0B,EAASjB,GACzB,OAAnB8B,EAGJ,GAAIC,EAAaD,EACbI,EAAOE,KAAKnB,OACT,IAAIc,IAAeD,EAEtB,GADAR,EAAMN,EAAkBC,EAAQK,IAAKtB,GACjCgC,EAAaV,EACbY,EAAOE,KAAKnB,OACT,IAAIe,IAAeV,EACtB,MAAO,KAInB,OAAOY,GAEX,QAASG,GAAWC,EAAOC,EAAOC,GAE9B,IADA,GAAyDC,GAArDN,EAAM,EAAGF,EAASK,EAAML,OAAQS,KAC7BP,EAAMF,EAAQE,IAAO,CAKxB,GAJAM,EAAYH,EAAMH,GACdK,IACAC,EAAYD,EAAUC,IAEtBF,IAAUE,EACV,MAAO,KACAF,GAAQE,GACfC,EAAeN,KAAKK,GAG5B,MAAOC,GAEX,QAASC,GAAWC,EAAMC,GAEtB,IADA,GAAmCN,GAA/BJ,EAAM,EAAGF,EAASW,EAAKX,OACpBE,EAAMF,EAAQE,IAAO,CAExB,GADAI,EAAQO,SAASF,EAAKT,GAAM,IACxBY,MAAMR,IAAUA,EAAQM,EAAMG,OAAST,EAAQM,EAAMI,KAAiB,IAAVV,GAAeM,EAAMG,MAAQ,EACzF,MAAO,KAEXJ,GAAKT,GAAOI,EAEhB,MAAOK,GAAKM,KAAKC,GAErB,QAASC,GAAiBR,GAEtB,IADA,GAAmCL,GAAOc,EAAa/B,EAAnDa,EAAM,EAAGF,EAASW,EAAKX,OACpBE,EAAMF,EAAQE,IAAO,CAKxB,GAJAI,EAAQK,EAAKT,GACbkB,EAAcd,EAAMN,OACpBX,EAAMiB,EAAMe,UAAUD,EAAc,GAAGE,cACvCjC,EAAMkC,GAAclC,GAChBA,IAAQrC,EACR,MAAO,KAEX2D,GAAKT,IACDV,OAAQqB,SAASP,EAAMe,UAAU,EAAGD,EAAc,GAAI,KAAO,EAC7D/B,IAAKA,GAGb,MAAOsB,GAEX,QAASa,GAAqBb,GAE1B,IADA,GAAmCL,GAAOmB,EAAtCvB,EAAM,EAAGF,EAASW,EAAKX,OAA4BC,KAChDC,EAAMF,EAAQE,IACjBI,EAAQK,EAAKT,GACQ,gBAAVI,GACPmB,EAAcnB,GAEdmB,EAAc,GAAKC,GAAUpB,EAAMjB,KAC/BiB,EAAMd,SACNiC,EAAcnB,EAAMd,OAASiC,IAGrCxB,EAAOE,KAAKsB,EAEhB,OAAOxB,GAAAA,EAEX,QAAS0B,GAAerE,GACpB,GAAIC,GAAQD,EAAKE,UACjB,OAAc,KAAVD,EACuD,IAAnD,GAAIU,MAAKX,EAAKY,cAAe,EAAG,IAAIV,WAC7B,GAEJ,GAEJoE,GAAOrE,GAElB,QAASG,GAASM,GAEd,MADAA,GAAOA,EAAKE,cACLF,EAAO,IAAM,GAAKA,EAAO,MAAQ,GAAKA,EAAO,MAAQ,EAEhE,QAASkD,GAAoBW,EAAGC,GAC5B,MAAOD,GAAIC,EAEf,QAASC,GAAgBC,EAAYC,GACjC,GAAajC,GAAQ1C,EAAjB4C,EAAM,EAAiBgC,IAC3B,IAAIF,EAGA,IAFAA,EAAaA,EAAWG,MAAMH,EAAWI,QAAQ,UAAc,IAAM,KACrEpC,EAASgC,EAAWhC,OACbE,EAAMF,EAAQE,IACjB5C,EAAO+E,EAAaL,EAAW9B,GAAM+B,GACjC3E,GACA4E,EAAM/B,KAAK7C,EAIvB,OAAO4E,GAEX,QAASI,GAAYN,EAAY1E,EAAM2E,GAEnC,IADA,GAAIC,GAAQnF,EAAEwF,QAAQP,GAAcA,EAAaD,EAAgBC,EAAYC,GAAOO,EAAWlF,EAAKmB,UAAYnB,EAAKmF,kBAAmBvC,EAAM,EAAGF,EAASkC,EAAMlC,OACzJE,EAAMF,EAAQE,IACjB,GAAIgC,EAAMhC,GAAKzB,YAAc+D,EACzB,OAAO,CAGf,QAAO,EAEX,QAASE,GAAkBR,EAAOD,GAAlC,GAEQjC,GACA1C,EAFA4C,EAAM,EAGND,KAAY0C,OAAOT,EACvB,KAAKlC,EAASC,EAAOD,OAAQE,EAAMF,EAAQE,IACvC5C,EAAO2C,EAAOC,GACd5C,EAAOsF,EAAMC,SAASC,QAAQxF,EAAM2E,GAAQ3E,EAAKyF,oBAAqB,WACtE9C,EAAOC,GAAO0C,EAAMI,SAAS1F,EAAM2F,GAEvC,OAAOhD,GAAOiD,KAAK,KAEvB,QAASC,GAAkBpC,EAAO7B,GAC9B,GAAI5B,GAAO,GAAIW,MAAK8C,EACpB,QAAQ7B,EAAKkE,MACb,IAAK,SACD9F,EAAK+F,YAAY/F,EAAKY,cAAe,EAAG,EACxC,MACJ,KAAK,UACDZ,EAAK+F,YAAY/F,EAAKY,cAAeZ,EAAKE,WAAY,EACtD,MACJ,KAAK,SACDY,GAAad,EAAM4B,EAAKnB,cAc5B,MATImB,GAAKoE,OACLhG,EAAKiG,SAAS,GAEdrE,EAAKsE,SACLlG,EAAKmG,WAAW,GAEhBvE,EAAKwE,SACLpG,EAAKqG,WAAW,GAEbrG,EAEX,QAASsG,GAAgB7C,EAAO7B,GAC5B,GAAI5B,GAAO,GAAIW,MAAK8C,EACpB,QAAQ7B,EAAKkE,MACb,IAAK,SACD9F,EAAK+F,YAAY/F,EAAKY,cAAe,GAAI,GACzC,MACJ,KAAK,UACDZ,EAAK+F,YAAY/F,EAAKY,cAAeZ,EAAKE,WAAa,EAAG,EAC1D,MACJ,KAAK,SACDY,GAAad,EAAM4B,EAAKnB,cACxBT,EAAKe,QAAQf,EAAKO,UAAY,GAclC,MATIqB,GAAKoE,OACLhG,EAAKiG,SAAS,IAEdrE,EAAKsE,SACLlG,EAAKmG,WAAW,IAEhBvE,EAAKwE,SACLpG,EAAKqG,WAAW,IAEbrG,EAEX,QAASuG,GAAiBC,EAAc/C,EAAOgD,GAA/C,GAGQC,GACAC,EACK/D,EAASF,EAJdkE,EAAqBJ,EAAa9D,OAClCmE,IAGJ,KAASjE,EAAM,EAAGF,EAAS+D,EAAU/D,OAAQE,EAAMF,EAAQE,IACvD8D,EAAWD,EAAU7D,GACjB8D,EAAW,EACXA,EAAWE,EAAqBF,EAEhCA,GAAY,EAEhBC,EAAQH,EAAaE,GACjBC,GAASA,EAAMlD,OAASA,GACxBoD,EAAOhE,KAAK8D,EAGpB,OAAOE,GAEX,QAASC,GAAqBN,EAAcO,EAAgBpC,GAA5D,GAEQgC,GACK/D,EAFLiE,IAEJ,KAASjE,EAAM,EAAGA,EAAM4D,EAAa9D,OAAQE,IACzC+D,EAAQH,EAAa5D,GAChBoC,EAAY+B,EAAgBJ,EAAMlD,MAAOkB,IAC1CkC,EAAOhE,KAAK8D,EAGpB,OAAOE,GAEX,QAASG,GAAOL,EAAOlD,EAAOC,EAAKiB,GAAnC,GACsDsC,GAAWC,EAASC,EAASnB,EAAOE,EAASE,EAASgB,EAAYC,EAAaC,EAAUC,EAAWC,EAASC,EAAeC,EAAUX,EAAgBY,EAAgBC,EAAcC,EAAYC,EAAOhC,EAAMW,EAAWsB,EAAYvB,EAA2BwB,EAAcC,EAAYC,EAAkBC,EAoDrVvI,EAwBYwI,EACAC,EAEIC,EACAC,EACAC,EACAC,EAEIC,EAKAC,EAqChBC,EA9HRhH,EAAOiH,EAAUlC,EAAMmC,eAAgBnE,GAAyPkC,KACpS,KAAKjF,EACD,OAAQ+E,EAiEZ,KA/DAF,EAAY7E,EAAK6E,UACjBsB,EAAatB,EAAY,EAAI,EAC7Bc,EAAY3F,EAAK6B,MACjB+D,EAAU5F,EAAK8B,KACX6D,GAAaC,KACbb,EAAQA,EAAMoC,OACVtF,MAAO8D,EAAY,GAAI5G,MAAK4G,EAAUvE,MAAM,IAAMtD,EAClDgE,IAAK8D,EAAU,GAAI7G,MAAK6G,EAAQxE,MAAM,IAAMtD,KAGpDmI,EAAalB,EAAMlD,MACnBmE,EAAeC,EAAW1G,UAC1BwG,EAAiBxC,GAAgB0C,GACjCd,EAAiBtC,EAAgBkC,EAAMqC,oBAAqBrE,IACvDoC,EAAe,IAAMnF,EAAKqH,UAC3BlC,EAAiBnF,EAAKqH,QAAQjG,MAC9B2D,EAAMuC,IAAI,sBAAuB9D,EAAkB2B,EAAgBpC,KAEvE0C,EAAc5D,EAAQ,GAAI9C,MAAK8C,GAC/BC,EAAM,GAAI/C,MAAK+C,GACfgE,EAAW9F,EAAKkE,KAChBA,EAAOqD,GAAYzB,GACnBI,EAAQlG,EAAKkG,MACTlG,EAAKwH,OAASxH,EAAKwH,MAAQ1F,IAC3BA,EAAM,GAAI/C,MAAKiB,EAAKwH,QAExB3B,EAA6B,WAAbC,GAAsC,YAAbA,GAAuC,WAAbA,EAC/DjE,EAAQmE,GAAgBE,GAASlG,EAAK/B,SAAW,GAAK4H,IAA+B,UAAbC,GAAqC,WAAbA,KAA2B9F,EAAKwE,QAChI3C,EAAQ,GAAI9C,MAAKiH,IAEjB5B,EAAQvC,EAAM4F,WACdnD,EAAUzC,EAAM6F,aAChBlD,EAAU3C,EAAM8F,aACX3H,EAAKoE,QACNA,EAAQ6B,EAAWwB,YAElBzH,EAAKsE,UACNA,EAAU2B,EAAWyB,cAEpB1H,EAAKwE,UACNA,EAAUyB,EAAW0B,cAEzB9F,EAAMwC,SAASD,EAAOE,EAASE,EAASyB,EAAW1C,oBAEvDvD,EAAK4H,aAAe,GAAI7I,MAAK8C,GACzBgD,IACAhD,EAAQoC,EAAkBpC,EAAO7B,GACjC8B,EAAM4C,EAAgB5C,EAAK9B,GACvBhC,EAAOuF,GAAgBzB,GAAOyB,GAAgB1B,GAC9C7D,EAAO,IACPoG,EAAQvC,EAAM4F,WACd3F,EAAIuC,SAASD,EAAOvC,EAAM6F,aAAc7F,EAAM8F,aAAc9F,EAAM0B,mBAClEsE,EAAU5I,UAAU6C,EAAKsC,IAE7BpE,EAAK4H,aAAe,GAAI7I,MAAK8C,GAC7B7B,EAAK8H,WAAapD,EAAgB7C,EAAO7B,IAE7CwF,EAAaT,EAAMgD,WACnB/H,EAAKgI,WAAa3C,EAAYwC,EAAUI,gBAAgBpG,GACpDqC,EAAKgE,OACLhE,EAAKgE,MAAMlI,EAAMiG,EAAYpE,GAEjCqC,EAAKiE,MAAMtG,EAAOC,EAAK9B,GAChB6B,GAASC,GAyCZ,GAxCAyD,EAAU,GAAIxG,MAAK8C,GACnBuG,EAAQ7C,EAASC,GACjBE,EAAW7D,GAAS4D,GAAeF,EAAUE,GACzCC,IAAatC,EAAY+B,EAAgBtD,EAAOkB,IAAS8B,KACzDQ,EAAYwC,EAAUQ,UAAUR,EAAUlJ,QAAQkD,IAAU0B,GAAgBvD,EAAKgI,YACjF1C,EAAUD,EAAYG,EAClBQ,IAAiBnE,EAAMtC,WAAawG,IAAmBxC,GAAgBvD,EAAKgI,aACvEjD,EAAMuD,WACH9B,EAAYzB,EAAMwD,eAAiBxD,EAAMyD,YACzC/B,EAAU1B,EAAMyD,aAAezD,EAAMwD,eACrCxF,GAAQyD,IAAczD,IAASyD,KAC3BE,EAAkB+B,EAAc5G,EAAOkB,GAAQ0F,EAAc1D,EAAMlD,MAAOkB,GAC1E4D,EAAgB8B,EAAclD,EAASxC,GAAQ0F,EAAc1D,EAAMjD,IAAKiB,GACxE6D,EAAoB6B,EAAc5G,EAAO2E,GAAaiC,EAAc1D,EAAMlD,MAAO2E,GACjFK,EAAkB4B,EAAclD,EAASkB,GAAWgC,EAAc1D,EAAMjD,IAAK2E,GAC7EC,IAAoBE,IAChBE,EAA2D,KAAvCJ,EAAkBE,GAC1CR,EAAe,GAAIrH,MAAK8C,EAAMtC,UAAYuH,GAC1CR,EAAmBjB,EAAYyB,GAE/BH,IAAkBE,IACdE,EAAqD,KAAnCJ,EAAgBE,GACtCR,EAAa,GAAItH,MAAKwG,EAAQhG,UAAYwH,GAC1CR,EAAiBjB,EAAUyB,KAIvC9B,GAAOhE,KAAK8D,EAAM2D,cACd7G,MAAOuE,GAAgB,GAAIrH,MAAK8C,GAChCC,IAAKuE,GAAcd,EACnByC,WAAY1B,GAAoBjB,EAChCsD,SAAUpC,GAAkBjB,KAEhCc,EAAeC,EAAaC,EAAmBC,EAAiB,OAEhExB,EAAMiD,WAAa3C,EACnBN,EAAM4D,SAAWrD,EACjBL,GAAOhE,KAAK8D,KAGhBF,GAUA,GATAX,EAAK0E,KAAK/G,EAAO7B,GACjBkE,EAAKiE,MAAMtG,EAAOC,EAAK9B,GACnB6B,EAAQ7B,EAAK8H,aACblD,EAAeD,EAAiBM,GAAO4D,MAAM1C,GAAaF,EAAYpB,GACtED,EAAeM,EAAqBN,EAAcO,EAAgBpC,GAClEkC,GAASA,GAAO4D,MAAM,EAAG1C,GAAY1C,OAAOmB,GAC5C5E,EAAK8H,WAAapD,EAAgB7C,EAAO7B,GACzCmG,EAAalB,GAAOnE,QAEpBoF,GAASA,IAAUC,EACnB,UAED,CACH,GAAID,GAASA,IAAUC,EACnB,KAEJA,IAAc,EACVa,EAAmB8B,EAAiBjH,GACxCqC,EAAK0E,KAAK/G,EAAO7B,GACbgH,GAAkC,WAAdhH,EAAKkE,MAAqB2D,EAAUI,gBAAgBlD,EAAMlD,OAAOtC,YAAcsI,EAAUI,gBAAgBpG,GAAOtC,YACpIS,EAAKgI,WAAa3C,EAAY,GAAItG,MAAK8C,EAAMtC,UAAY,OAE7D2E,EAAKiE,MAAMtG,EAAOC,EAAK9B,GAG/B,MAAOiF,IAEX,QAAS6D,GAAiB1K,GAA1B,GACQ2K,GAAa3K,EAAKyF,oBAClBmF,EAAgB,GAAIjK,MAAKX,EAAKmB,UAAY,MAC1C0J,EAAsBD,EAAcnF,mBACxC,OAAOkF,GAAaE,EAExB,QAASR,GAAcrK,EAAM2E,GACzB,MAAOA,GAAOW,EAAMC,SAASrD,OAAOlC,EAAM2E,GAAQ3E,EAAKyF,oBAE3D,QAASV,GAAa/B,EAAO2B,GAKzB,MAJA3B,GAAQsC,EAAMwF,UAAU9H,EAAO+H,IAC3B/H,GAAS2B,IACT3B,EAAQuC,EAASyF,MAAMhI,EAAO2B,IAE3B3B,EAEX,QAASiI,GAAcC,EAAUvG,GAAjC,GAEQwG,GACAC,EACApI,EACAqI,EACAC,EAAUxH,EACLlB,EAASF,EANd6I,EAAQL,EAASrG,MAAM,IAM3B,KAASjC,EAAM,EAAGF,EAAS6I,EAAM7I,OAAQE,EAAMF,EAAQE,IAOnD,GANAuI,EAAOI,EAAM3I,GAAKiC,MAAM,KACxBuG,EAAWD,EAAK,GAChBnI,EAAQmI,EAAK,GACTC,EAAStG,QAAQ,eACjBuG,EAAOD,EAASrH,UAAUqH,EAAStG,QAAQ,SAASD,MAAM,KAAK,IAE/D7B,EAEA,IADAA,EAAQA,EAAM6B,MAAM,KACfyG,EAAW,EAAGxH,EAAcd,EAAMN,OAAQ4I,EAAWxH,EAAawH,IACnEtI,EAAMsI,GAAYvG,EAAa/B,EAAMsI,GAAWD,GAAQ1G,EAIpE,IAAI3B,EACA,OACIA,MAAOA,EACPqI,KAAMA,GAIlB,QAASxC,GAAU2C,EAAO7G,GAA1B,GAEQ8G,GAAQzI,EACCN,EAETd,EAAM8J,EAAMC,EACZP,EAAU3K,EAAW6B,EALrBsJ,KAEAhJ,EAAM,EACNM,GAAY,EAGZ2I,EAAY,SAAUtH,EAAGC,GACzB,GAAIsH,GAAOvH,EAAExC,IAAKgK,EAAOvH,EAAEzC,GAO3B,OANI+J,GAAOrL,IACPqL,GAAQ,GAERC,EAAOtL,IACPsL,GAAQ,GAELD,EAAOC,EAElB,KAAKP,EACD,MAAO,KAMX,KAJAG,EAAQH,EAAM3G,MAAM,MACf8G,EAAM,IAAOH,EAAM1G,QAAQ,iBAAqB0G,EAAM1G,QAAQ,eAAmB0G,EAAM1G,QAAQ,iBAChG6G,EAAQH,EAAM3G,MAAM,MAEnBjC,EAAM,EAAGF,EAASiJ,EAAMjJ,OAAQE,EAAMF,EAAQE,IAC/C8I,EAAOjM,EAAEuM,KAAKL,EAAM/I,IAChB8I,EAAK5G,QAAQ,gBACb8G,EAASnI,MAAQwH,EAAcS,EAAM/G,GAC9B+G,EAAK5G,QAAQ,cACpB8G,EAASlI,IAAMuH,EAAcS,EAAM/G,GAC5B+G,EAAK5G,QAAQ,eACpB8G,EAAS3C,QAAUgC,EAAcS,EAAM/G,GAChC+G,EAAK5G,QAAQ,cACpBlD,EAAO8J,EAAK3H,UAAU,GACftE,EAAEuM,KAAKN,KACd9J,EAAO8J,EAIf,KADA9J,EAAOA,EAAKiD,MAAM,KACbjC,EAAM,EAAGF,EAASd,EAAKc,OAAQE,EAAMF,EAAQE,IAI9C,OAHAwI,EAAWxJ,EAAKgB,GAChB6I,EAASL,EAASvG,MAAM,KACxB7B,EAAQvD,EAAEuM,KAAKP,EAAO,IAAI5G,MAAM,KACxBpF,EAAEuM,KAAKP,EAAO,IAAIzH,eAC1B,IAAK,OACD4H,EAAS9F,KAAO9C,EAAM,GAAGiJ,aACzB,MACJ,KAAK,QACDL,EAASxC,MAAQrE,EAAa/B,EAAM,GAAI2B,EACxC,MACJ,KAAK,QACDiH,EAAS9D,MAAQvE,SAASP,EAAM,GAAI,GACpC,MACJ,KAAK,WACD4I,EAAS/L,SAAW0D,SAASP,EAAM,GAAI,GACvC,MACJ,KAAK,WACD4I,EAASxF,QAAUhD,EAAWJ,GAC1BS,MAAO,EACPC,IAAK,KAETR,GAAY,CACZ,MACJ,KAAK,WACD0I,EAAS1F,QAAU9C,EAAWJ,GAC1BS,MAAO,EACPC,IAAK,KAETR,GAAY,CACZ,MACJ,KAAK,SACD0I,EAAS5F,MAAQ5C,EAAWJ,GACxBS,MAAO,EACPC,IAAK,KAETR,GAAY,CACZ,MACJ,KAAK,aACD0I,EAASM,UAAY9I,EAAWJ,GAC5BS,UACAC,IAAK,KAETR,GAAY,CACZ,MACJ,KAAK,YACD0I,EAASO,SAAW/I,EAAWJ,GAC3BS,WACAC,IAAK,MAETR,GAAY,CACZ,MACJ,KAAK,UACD0I,EAASQ,OAAShJ,EAAWJ,GACzBS,MAAO,EACPC,IAAK,KAETR,GAAY,CACZ,MACJ,KAAK,QACD0I,EAAStJ,SAAWA,EAAWuB,EAAiBb,GAChDE,GAAY,CACZ,MACJ,KAAK,WACD0I,EAASS,MAAQjJ,EAAWJ,GACxBS,UACAC,IAAK,KAETR,GAAY,CACZ,MACJ,KAAK,WACD0I,EAASnF,UAAYrD,EAAWJ,GAC5BS,WACAC,IAAK,KAET,MACJ,KAAK,OACDkI,EAASnL,UAAYA,EAAYwD,GAAcjB,EAAM,IAI7D,MAAI4I,GAAS9F,OAASpG,GAAakM,EAAS9D,QAAUpI,GAAakM,EAASxC,MACjE,MAENwC,EAAS/L,WACV+L,EAAS/L,SAAW,GAEpBY,IAAcf,IACdkM,EAASnL,UAAYA,EAAY6E,EAAMgH,UAAUC,SAASC,UAE1DlK,IACAsJ,EAAStJ,SAAWA,EAASqB,KAAKkI,IAElCD,EAASnF,YAAcvD,IACvB0I,EAASnF,UAAY,MAEzBmF,EAASa,cAAgBvJ,EAClB0I,GAEX,QAASc,GAAkBxB,EAAUvG,GAMjC,IANJ,GAKQgI,GAJA3J,EAAQkI,EAASlI,MACjBqI,EAAOH,EAASG,MAAQ,GACxB3I,EAASM,EAAMN,OACfE,EAAM,EAEHA,EAAMF,EAAQE,IACjB+J,EAAM3J,EAAMJ,GACZ+J,EAAMpH,EAASC,QAAQmH,EAAKtB,GAAQ1G,GAAQgI,EAAIlH,oBAAqB,WACrEzC,EAAMJ,GAAO0C,EAAMI,SAASiH,EAAK,mBAKrC,OAHItB,KACAA,EAAO,SAAWA,GAEfA,EAAO,IAAMrI,EAAM4C,KAAK,KAAO,IAE1C,QAASgH,GAAUhL,EAAM+C,GAAzB,GACQlE,GAAYmB,EAAKnB,UACjBoM,EAAa,QAAUjL,EAAKkE,KAAK9B,cACjCiF,EAAUrH,EAAKqH,SAAW,GAC1BxF,EAAQ7B,EAAK6B,OAAS,GACtBC,EAAM9B,EAAK8B,KAAO,GAClB0F,EAAQxH,EAAKwH,KAqDjB,OApDIxH,GAAK/B,SAAW,IAChBgN,GAAc,aAAejL,EAAK/B,UAElC+B,EAAKkG,QACL+E,GAAc,UAAYjL,EAAKkG,OAE/BsB,IACAA,EAAQ7D,EAASC,QAAQ4D,EAAOzE,GAAQyE,EAAM3D,oBAAqB,WACnEoH,GAAc,UAAYvH,EAAMI,SAAS0D,EAAO,qBAEhDxH,EAAKwK,SACLS,GAAc,YAAcjL,EAAKwK,QAEjCxK,EAAKyK,QACLQ,GAAc,aAAejL,EAAKyK,OAElCzK,EAAKuK,WACLU,GAAc,cAAgBjL,EAAKuK,UAEnCvK,EAAKsK,YACLW,GAAc,eAAiBjL,EAAKsK,WAEpCtK,EAAKU,WACLuK,GAAc,UAAY3I,EAAqBtC,EAAKU,WAEpDV,EAAKoE,QACL6G,GAAc,WAAajL,EAAKoE,OAEhCpE,EAAKsE,UACL2G,GAAc,aAAejL,EAAKsE,SAElCtE,EAAKwE,UACLyG,GAAc,aAAejL,EAAKwE,SAElCxE,EAAK6E,YACLoG,GAAc,aAAejL,EAAK6E,WAElChG,IAAcf,IACdmN,GAAc,SAAWzI,GAAU3D,IAEnCgD,IACAA,EAAQ,UAAYiJ,EAAkBjJ,EAAOkB,IAE7CjB,IACAA,EAAM,QAAUgJ,EAAkBhJ,EAAKiB,IAEvCsE,IACAA,EAAU,SAAWyD,EAAkBzD,EAAStE,KAEhDlB,GAASC,GAAOuF,KAChB4D,EAAapJ,EAAQC,EAAMuF,EAAU,SAAW4D,GAE7CA,EArrCd,GAosCOC,GAYAC,EAeAC,EACAC,EA8BAC,EAsBAC,EAUAC,EA2TAC,EAkPAC,EACAC,EACAC,EACAC,EACAC,EA90DApI,EAAQqI,OAAOrI,MAAOC,EAAWD,EAAMC,SAAUqI,EAAQtI,EAAMsI,MAAOC,EAAKvI,EAAMuI,GAAIC,EAASD,EAAGC,OAAQC,EAAeF,EAAGE,aAActE,EAAYnE,EAAMtF,KAAMgK,EAAUP,EAAUO,QAASlJ,GAAe2I,EAAU3I,aAAcD,GAAY4I,EAAU5I,UAAWS,GAAkBmI,EAAUnI,gBAAiB6D,GAAkBsE,EAAUtE,gBAAiB9E,IAC5V,EACA,GACA,GACA,GACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KACDC,IACC,EACA,GACA,GACA,GACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KACDgE,IACC,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,IACDF,IACC4J,EAAG,KACHC,EAAG,KACHC,EAAG,KACHC,EAAG,KACHC,EAAG,KACHC,EAAG,KACHC,EAAG,MACJrK,IACCsK,GAAM,EACNC,GAAM,EACNC,GAAM,EACNC,GAAM,EACNC,GAAM,EACNC,GAAM,EACNC,GAAM,GACP9D,IACC,6BACA,yBACA,sBACA,mBACA,gBACA,aACA,wBACA,qBACA,kBACA,gBACA,cACA,YACD+D,IACC,SACA,QACA,WACA,YACA,WACA,QACA,UACA,WACDC,GAAoBD,GAAWpM,OAAQiD,GAAyB,mBAAoBqJ,IACnF5C,OAAQ,SAAUpM,EAAM0D,EAAK9B,GACzB,GAAIqN,GAAarN,EAAKwK,OAAQA,EAAStJ,EAAWmM,EAAYjP,EAAKE,WAAa,GAAIgP,GAAU,CAS9F,OARe,QAAX9C,IACIA,EAAO1J,OACP1C,EAAKmP,SAAS/C,EAAO,GAAK,EAAG,GAE7BpM,EAAK+F,YAAY/F,EAAKY,cAAgB,EAAGqO,EAAW,GAAK,EAAG,GAEhEC,GAAU,GAEPA,GAEXhD,UAAW,SAAUlM,EAAM0D,EAAK9B,GAO5B,IANA,GAAIwN,GAAanP,EAAOE,EAAM+O,GAAU,EAAOlJ,EAAQhG,EAAKqJ,WAAYpG,EAAY,SAAUoM,GAItF,MAHIA,GAAW,IACXA,EAAWD,EAAcC,EAAW,GAEjCA,GAERrP,GAAQ0D,GAAK,CAIhB,GAHAzD,EAAQD,EAAKE,WACbkP,EAAc/K,EAAerE,GAC7BG,EAAO2C,EAAWlB,EAAKsK,UAAWlM,EAAKO,UAAW0C,GACrC,OAAT9C,EACA,MAAO+O,EAGX,IADAA,GAAU,EACN/O,EAAKuC,QAGL,GAFA1C,EAAKmP,SAASlP,EAAOE,EAAKwD,KAAKC,GAAqB,IACpD/C,GAAUb,EAAMgG,GACZ/F,IAAUD,EAAKE,WACf,UAGJF,GAAKmP,SAASlP,EAAQ,EAAG,GAGjC,MAAOiP,IAEX/C,SAAU,SAAUnM,EAAM0D,EAAK9B,GAO3B,IANA,GAAIlB,GAAMyL,EAAU+C,GAAU,EAAOlJ,EAAQhG,EAAKqJ,WAAYpG,EAAY,SAAUqM,GAI5E,MAHIA,GAAU,IACVA,EAAU5O,EAAO4O,GAEdA,GAERtP,EAAO0D,GAAK,CAGf,GAFAhD,EAAON,EAASJ,GAAQ,IAAM,IAC9BmM,EAAWrJ,EAAWlB,EAAKuK,SAAUpM,EAAUC,GAAOiD,GACrC,OAAbkJ,EACA,MAAO+C,EAIX,IAFAA,GAAU,EACVxO,EAAOV,EAAKY,cACRuL,EAASzJ,OAAQ,CACjB1C,EAAK+F,YAAYrF,EAAM,EAAGyL,EAASxI,KAAKC,GAAqB,IAC7D/C,GAAUb,EAAMgG,EAChB,OAEAhG,EAAK+F,YAAYrF,EAAO,EAAG,EAAG,GAGtC,MAAOwO,IAEX7C,MAAO,SAAUrM,EAAM0D,EAAK9B,GAOxB,IANA,GAAgClB,GAAM2L,EAAOtK,EAAzCtB,EAAYmB,EAAKnB,UAA6ByO,GAAU,EAAOlJ,EAAQhG,EAAKqJ,WAAYpG,EAAY,SAAUsM,GAI1G,MAHIA,GAAO,IACPA,EAAO,GAAKA,GAETA,GAERvP,EAAO0D,GAAK,CAEf,GADA2I,EAAQvJ,EAAWlB,EAAKyK,MAAO7L,EAAWR,EAAMS,GAAYwC,GAC9C,OAAVoJ,EACA,MAAO6C,EAIX,IAFAA,GAAU,EACVxO,EAAOV,EAAKY,cACRyL,EAAM3J,OAAQ,CACdX,EAA2C,EAArCsK,EAAM1I,KAAKC,GAAqB,GAAS,EAC/C5D,EAAK+F,YAAYrF,EAAM,EAAGqB,GAC1BjB,GAAad,EAAMS,MACnBI,GAAUb,EAAMgG,EAChB,OAEAhG,EAAK+F,YAAYrF,EAAO,EAAG,EAAG,GAGtC,MAAOwO,IAEX5M,SAAU,SAAUtC,EAAM0D,EAAK9B,GAArB,GAKF4N,GAAazN,EAJbO,EAAWV,EAAKU,SAChB7B,EAAYmB,EAAKnB,UACjBgP,EAAepN,EAAeC,EAAUtC,EAAMS,GAC9CuF,EAAQhG,EAAKqJ,UAEjB,IAAqB,OAAjBoG,EACA,OAAO,CAQX,IANAD,EAAcC,EAAa,GACtBD,IACDA,EAAclN,EAAS,GACvBxB,GAAad,EAAMS,IAEvBsB,EAAMyN,EAAYzN,IACdyN,EAAYtN,OACZ,KAAOlC,GAAQ0D,IAAQtB,EAASpC,EAAMwP,EAAa/O,IAC3CW,EAAYpB,EAAMS,KAAe0B,EAAcnC,EAAMS,IACrDT,EAAKmP,SAASnP,EAAKE,WAAa,EAAG,GACnCW,GAAUb,EAAMgG,KAEhBhG,EAAKe,QAAQf,EAAKO,UAAY,GAC9BM,GAAUb,EAAMgG,GAChBlF,GAAad,EAAMS,MAO/B,OAHIT,GAAKgB,WAAae,GAClBjB,GAAad,EAAM+B,IAEhB,GAEXiE,MAAO,SAAUhG,EAAM0D,EAAK9B,GACxB,GAAI8N,GAAY9N,EAAKoE,MAAOiB,EAAYrF,EAAKgI,WAAY+F,EAAa1I,EAAUoC,WAAYrD,EAAQlD,EAAW4M,EAAWC,GAAaT,GAAU,CAqBjJ,OApBc,QAAVlJ,IACAkJ,GAAU,EACVlP,EAAKiG,SAAS0J,GACd9O,GAAUb,EAAM2P,GACZ3J,EAAMtD,QACNsD,EAAQA,EAAM,GACdhG,EAAKiG,SAASD,KAEdA,EAAQhG,EAAKqJ,WACbrJ,EAAKe,QAAQf,EAAKO,UAAY,GAC9BM,GAAUb,EAAMgG,GAChBA,EAAQ0J,EAAU,GAClB1P,EAAKiG,SAASD,GACdnF,GAAUb,EAAMgG,IAEhBpE,EAAKsE,SACLlG,EAAKmG,WAAW,GAEpBc,EAAUhB,SAASD,EAAOhG,EAAKsJ,eAE5B4F,GAEXhJ,QAAS,SAAUlG,EAAM0D,EAAK9B,GAC1B,GAAIgO,GAAchO,EAAKsE,QAAS2J,EAAiB7P,EAAKsJ,aAAcpD,EAAUpD,EAAW8M,EAAaC,GAAiB7J,EAAQpE,EAAKgI,WAAWP,WAAY6F,GAAU,CAiBrK,OAhBgB,QAAZhJ,IACAgJ,GAAU,EACNhJ,EAAQxD,OACRwD,EAAUA,EAAQ,IAElBF,GAAS,EACTE,EAAU0J,EAAY,IAEtBhO,EAAKwE,SACLpG,EAAKqG,WAAW,GAEpBrG,EAAKiG,SAASD,EAAOE,GACrBF,GAAgB,GAChBnF,GAAUb,EAAMgG,GAChBpE,EAAKgI,WAAW3D,SAASD,EAAOE,EAASlG,EAAKuJ,eAE3C2F,GAEX9I,QAAS,SAAUpG,EAAM0D,EAAK9B,GAC1B,GAAIkO,GAAclO,EAAKwE,QAASJ,EAAQpE,EAAKgI,WAAWP,WAAYjD,EAAUtD,EAAWgN,EAAa9P,EAAKuJ,cAAerD,EAAUlG,EAAKsJ,aAAc4F,GAAU,CAejK,OAdgB,QAAZ9I,IACA8I,GAAU,EACN9I,EAAQ1D,OACR1C,EAAKqG,WAAWD,EAAQ,KAExBF,GAAW,EACXlG,EAAKmG,WAAWD,EAAS4J,EAAY,IACjC5J,EAAU,KACVA,GAAoB,GACpBF,GAASA,EAAQ,GAAK,KAG9BpE,EAAKgI,WAAW3D,SAASD,EAAOE,EAASlG,EAAKuJ,eAE3C2F,IAEZa,GAAgBnC,EAAMoC,QACrBxF,KAAM,SAAUxK,EAAM4B,GAClB,GAA4DsE,GAASE,EAAjEa,EAAYrF,EAAKgI,WAAY7H,EAAMkF,EAAU1G,SACjD,IAAIqB,EAAKwE,QACLA,EAAUpG,EAAKuJ,aAAe,EAC9BvJ,EAAKqG,WAAWD,GAChBa,EAAUZ,WAAWD,GACrBa,EAAUlG,QAAQgB,OACf,CAAA,IAAIH,EAAKsE,QAMZ,OAAO,CALPA,GAAUlG,EAAKsJ,aAAe,EAC9BtJ,EAAKmG,WAAWD,GAChBe,EAAUd,WAAWD,GACrBe,EAAUlG,QAAQgB,GAItB,OAAO,GAEXkB,UAAW,SAAUgN,GACjB,GAAIrO,GAAOqO,EAAQrO,IACC,KAAhBqO,EAAQrN,KAAahB,EAAKoE,QAC1BpE,EAAKgI,WAAW3D,SAAS,GACzBiK,KAAKC,MAAMF,EAAQjQ,KAAM4B,KAGjCmI,MAAO,SAAU/J,EAAM0D,EAAK9B,GAExB,IADA,GAA8BwO,GAAUC,EAAWC,EAAU1N,EAAKb,EAA9DlC,EAAW+B,EAAK/B,SACbG,GAAQ0D,GAAK,CAGhB,IAFA4M,EAAWD,EAAY3Q,EACvBqC,EAAM/B,EAAKO,UACNqC,EAAM,EAAGA,EAAMmM,GAAmBnM,IAAO,CAE1C,GADAwN,EAAWtB,GAAWlM,GAClBhB,EAAKwO,GAAW,CAEhB,GADAE,EAAWtB,GAAWoB,GAAUpQ,EAAM0D,EAAK9B,GACvCyO,IAAc3Q,GAAa4Q,EAC3B,KAEAD,GAAYC,EAGhBA,GACAJ,KAAKjN,WACDjD,KAAMA,EACN4B,KAAMA,EACNG,IAAKA,EACLa,IAAKA,IAIjB,IAAkB,IAAb/C,IAAmBqQ,KAAKrQ,SAAS+B,EAAM5B,KAAU4C,IAAQmM,GAC1D,QAIZlP,SAAU,SAAU+B,EAAM2O,GAAhB,GAWF3Q,GACA4Q,EA4BIC,EACAC,EAxCJjN,EAAQ,GAAI9C,MAAKiB,EAAK4H,cACtBxJ,EAAO,GAAIW,MAAK4P,GAChBvK,EAAQuK,EAAQlH,WAChB5I,EAAYmB,EAAKnB,UACjBZ,EAAW+B,EAAK/B,SAChB8Q,EAAY/O,EAAKkE,KACjBwK,GAAW,EACXxQ,EAAS,EACTG,EAAQ,EACR8B,EAAM,CAmEV,OAhEkB,WAAd4O,GACA/Q,EAAOI,EAAKyF,oBAAsBhC,EAAMgC,oBACxC+K,EAAiB5O,EAAKgI,WAAWP,WACjCrJ,EAAOA,EAAKmB,UACR6E,IAAUwK,IACVxQ,IAASwQ,EAAiBxK,GAASyD,EAAUmH,aAEjD5Q,GAAQyD,EACJ7D,IACAI,GAAQJ,EAAO6J,EAAUoH,eAE7BjR,EAAOqB,KAAKC,MAAMlB,EAAOyJ,EAAUmH,aACnC9Q,EAASH,EAAeC,EAAMC,GACf,IAAXC,IACAoQ,KAAKC,MAAMI,EAAS3O,EAAM9B,GAC1BwQ,GAAW,IAEM,UAAdK,GACPlH,EAAUO,QAAQhK,GAAOyD,GAAO,GAChC7D,EAAOqB,KAAK6P,MAAM9Q,EAAOyJ,EAAUsH,YACnCjR,EAASH,EAAeC,EAAMC,GACf,IAAXC,IACAoQ,KAAKc,MAAMT,EAAS3O,EAAM9B,GAC1BwQ,GAAW,IAEM,WAAdK,GACP7Q,EAASoQ,KAAKe,8BAA8BxN,EAAO8M,GAC/CE,EAAyBhP,EAAkB8O,EAAQvP,SAAUP,GAC7DiQ,EAAuBjP,EAAkBgC,EAAMzC,SAAUP,GACzDgQ,EAAyBC,IACzB5Q,GAAU,GAEdA,EAASH,EAAeG,EAAQD,GACjB,IAAXC,IACA2J,EAAU3I,aAAayP,EAAS3O,EAAKnB,cACrC8P,EAAQxP,QAAQwP,EAAQhQ,UAAqB,EAATT,GACpCe,GAAU0P,EAASvK,GACnBsK,GAAW,IAEM,YAAdK,GACP/Q,EAAO2Q,EAAQ3P,cAAgB6C,EAAM7C,cACrChB,EAAO2Q,EAAQrQ,WAAauD,EAAMvD,WAAoB,GAAPN,EAC/CE,EAASH,EAAeC,EAAMC,GACf,IAAXC,IACAiC,EAAMH,EAAK6K,cAAgB,EAAI8D,EAAQhQ,UACvCgQ,EAAQxK,YAAYwK,EAAQ3P,cAAe2P,EAAQrQ,WAAaJ,EAAQiC,GACxElB,GAAU0P,EAASvK,GACnBsK,GAAW,IAEM,WAAdK,IACP/Q,EAAO2Q,EAAQ3P,cAAgB6C,EAAM7C,cACrCd,EAASH,EAAeC,EAAMC,GACzB+B,EAAKwK,SACNnM,EAAQsQ,EAAQrQ,YAEf0B,EAAKuK,UAAavK,EAAKsK,WAActK,EAAKU,WAC3CP,EAAMwO,EAAQhQ,WAEH,IAAXT,IACAyQ,EAAQxK,YAAYwK,EAAQ3P,cAAgBd,EAAQG,EAAO8B,GAC3DlB,GAAU0P,EAASvK,GACnBsK,GAAW,IAGZA,GAEXW,8BAA+B,SAAUC,EAAOC,GAAjB,GACvB9E,IAAS8E,EAASD,GAAS,OAC3BE,EAAanQ,KAAKC,MAAMmL,EAI5B,OAHIA,GAAQ+E,EAAa,MACrBA,EAAanQ,KAAK6P,MAAMzE,IAErB+E,GAEXjB,MAAO,SAAUnQ,EAAM4B,EAAM/B,GACzB,GAAIoH,GAAYrF,EAAKgI,WAAY5D,EAAQiB,EAAUoC,UAC/CxJ,KACAmG,GAASnG,GAEbG,EAAKiG,SAASD,GACdA,GAAgB,GAChBiB,EAAUhB,SAASD,GACnBnF,GAAUb,EAAMgG,IAEpBgL,MAAO,SAAUhR,EAAM4B,EAAM/B,GACzB,GAAImG,GAAQhG,EAAKqJ,UACjBrJ,GAAKe,QAAQf,EAAKO,UAAYV,GACzBgB,GAAUb,EAAMgG,IACjBkK,KAAKC,MAAMnQ,EAAM4B,MAGzByP,GAAkBtB,GAAcC,QAChCxF,KAAM,SAAUxK,EAAM4B,GACbmO,GAAcuB,GAAG9G,KAAKxK,EAAM4B,IAC7BsO,KAAKC,MAAMnQ,EAAM4B,EAAM,IAG/BqB,UAAW,SAAUgN,GACjB,GAAIrO,GAAOqO,EAAQrO,IACC,KAAhBqO,EAAQrN,MACRhB,EAAKgI,WAAW3D,SAAS,GACzBiK,KAAKC,MAAMF,EAAQjQ,KAAM4B,OAGjC2P,GAAiBxB,GAAcC,QAC/BxF,KAAM,SAAUxK,EAAM4B,GACbmO,GAAcuB,GAAG9G,KAAKxK,EAAM4B,IAC7BsO,KAAKtO,EAAKoE,MAAQ,QAAU,SAAShG,EAAM4B,EAAM,MAGzD4P,GAAkBD,GAAevB,QACjClG,MAAO,SAAUlI,EAAM6P,GACd7P,EAAKU,WACNV,EAAKU,WACGP,IAAK0P,EAAezQ,SACpBkB,OAAQ,QAIxBwP,GAAmB3B,GAAcC,QACjCxF,KAAM,SAAUxK,EAAM4B,GAClB,GAAIG,GAAKiE,CACT,KAAK+J,GAAcuB,GAAG9G,KAAKxK,EAAM4B,GAC7B,GAAIA,EAAKoE,MACLkK,KAAKC,MAAMnQ,EAAM4B,EAAM,OACpB,IAAIA,EAAKsK,WAAatK,EAAKU,UAAYV,EAAKuK,UAAYvK,EAAKyK,MAChE6D,KAAKc,MAAMhR,EAAM4B,EAAM,OACpB,CAKH,IAJAG,EAAM/B,EAAKO,UACXyF,EAAQhG,EAAKqJ,WACbrJ,EAAKmP,SAASnP,EAAKE,WAAa,GAChCW,GAAUb,EAAMgG,GACThG,EAAKO,YAAcwB,GACtB/B,EAAKe,QAAQgB,GACblB,GAAUb,EAAMgG,EAEpBkK,MAAKC,MAAMnQ,EAAM4B,KAI7BqB,UAAW,SAAUgN,GACjB,GAAIrO,GAAOqO,EAAQrO,KAAM5B,EAAOiQ,EAAQjQ,KAAMgG,EAAQhG,EAAKqJ,UACvC,KAAhB4G,EAAQrN,KAAchB,EAAKsK,WAActK,EAAKU,SAI9CyN,GAAcuB,GAAGrO,UAAUgN,IAH3BjQ,EAAKe,QAAQkP,EAAQlO,KACrBlB,GAAUb,EAAMgG,KAKxB8D,MAAO,SAAUlI,EAAM6P,EAAgBzR,GAC9B4B,EAAKsK,WAActK,EAAKU,UACzBtC,EAAKe,QAAQ0Q,EAAelR,cAGpCoR,GAAkBD,GAAiB1B,QACnCxF,KAAM,SAAUxK,EAAM4B,GAClB,GAAIG,GAAKiE,EAAQhG,EAAKqJ,UACtB,KAAK0G,GAAcuB,GAAG9G,KAAKxK,EAAM4B,GAC7B,GAAIA,EAAKoE,MACLkK,KAAKC,MAAMnQ,EAAM4B,EAAM,OACpB,IAAIA,EAAKsK,WAAatK,EAAKU,UAAYV,EAAKuK,UAAYvK,EAAKyK,MAChE6D,KAAKc,MAAMhR,EAAM4B,EAAM,OACpB,IAAIA,EAAKwK,OAAQ,CAIpB,IAHArK,EAAM/B,EAAKO,UACXP,EAAKmP,SAASnP,EAAKE,WAAa,GAChCW,GAAUb,EAAMgG,GACThG,EAAKO,YAAcwB,GACtB/B,EAAKe,QAAQgB,GACblB,GAAUb,EAAMgG,EAEpBkK,MAAKC,MAAMnQ,EAAM4B,OAEjB5B,GAAK+F,YAAY/F,EAAKY,cAAgB,GACtCC,GAAUb,EAAMgG,GAChBkK,KAAKC,MAAMnQ,EAAM4B,IAI7BkI,MAAO,eAEPX,IACAyI,OAAU,GAAIP,IACdQ,MAAS,GAAIN,IACbO,OAAU,GAAIN,IACdO,QAAW,GAAIL,IACfM,OAAU,GAAIL,KACfM,GAAQ,QAASC,GAAS,QAirBjC5M,GAAM6M,YACFvQ,MACIwQ,MAAOvJ,EACP+D,UAAWA,GAEf5F,OAAQA,EACRjH,UAAWA,EACXS,WAAYA,EACZY,YAAaA,EACbe,cAAeA,EACf6C,YAAaA,EACbI,kBAAmBA,GAEnB0H,EAAoB,SAAUN,GAE9B,IADA,GAAI6F,GAAa/M,EAAMgH,UAAUC,SAASpM,KAAKmS,WAAY5P,EAAS2P,EAAW3P,OAAQC,EAAS,GAAIC,EAAM,EAAG2P,KACtG3P,EAAMF,EAAQE,IACjB2P,EAAO1P,KAAKD,EAIhB,KAFAyP,EAAaA,EAAW5H,MAAM+B,GAAUnH,OAAOgN,EAAW5H,MAAM,EAAG+B,IACnE+F,EAASA,EAAO9H,MAAM+B,GAAUnH,OAAOkN,EAAO9H,MAAM,EAAG+B,IAClD5J,EAAM,EAAGA,EAAMF,EAAQE,IACxBD,GAAU,yFAA2F4P,EAAO3P,GAAO,QAAUyP,EAAWzP,GAAO,UAEnJ,OAAOD,IAEPoK,EAA0B,SAAUP,GAEpC,IADA,GAAI6F,GAAa/M,EAAMgH,UAAUC,SAASpM,KAAKqS,MAAO9P,EAAS2P,EAAW3P,OAAQC,EAAS,GAAIC,EAAM,EAAG2P,KACjG3P,EAAMF,EAAQE,IACjB2P,EAAO1P,KAAKD,EAIhB,KAFAyP,EAAaA,EAAW5H,MAAM+B,GAAUnH,OAAOgN,EAAW5H,MAAM,EAAG+B,IACnE+F,EAASA,EAAO9H,MAAM+B,GAAUnH,OAAOkN,EAAO9H,MAAM,EAAG+B,IAClD5J,EAAM,EAAGA,EAAMF,EAAQE,IACxBD,GAAU,6CACVA,GAAU,8BAAgC0P,EAAWzP,GAAO,UAC5DD,GAAU,0EAA4E4P,EAAO3P,GAAO,OACpGD,GAAU,eAEd,OAAOA,IAEPqK,EAA2B1H,EAAMmN,SAAS,wwEAC1CxF,IAEIlL,IAAK,EACLG,OAAQ,IAGRH,IAAK,EACLG,OAAQ,IAGRH,IAAK,EACLG,OAAQ,IAGRH,IAAK,EACLG,OAAQ,IAGRH,IAAK,EACLG,OAAQ,IAGRH,IAAK,EACLG,OAAQ,IAGRH,IAAK,EACLG,OAAQ,IAGZgL,IAEInL,IAAK,EACLG,OAAQ,IAGRH,IAAK,EACLG,OAAQ,IAGRH,IAAK,EACLG,OAAQ,IAGRH,IAAK,EACLG,OAAQ,IAGRH,IAAK,EACLG,OAAQ,IAGZiL,IAEIpL,IAAK,EACLG,OAAQ,IAGRH,IAAK,EACLG,OAAQ,IAGZkL,EAAuBU,EAAOkC,QAC9B0C,KAAM,SAAUC,EAAS1C,GAAnB,GACExM,GACAmP,EAAO1C,KACP/G,EAAc8G,GAAWA,EAAQ9G,WACrC2E,GAAOwD,GAAGoB,KAAKG,KAAKD,EAAMD,EAAS1C,GACnC2C,EAAKE,QAAUF,EAAKD,QACpB1C,EAAU2C,EAAK3C,QACfA,EAAQxM,MAAQA,EAAQwM,EAAQxM,OAASgG,EAAUsJ,QAC/C5J,IACA8G,EAAQ9G,YAAcA,GAEL,gBAAV1F,KACPwM,EAAQxM,MAAQ6B,EAAMwF,UAAUrH,EAAO,oBAEd,OAAzBwM,EAAQ5O,eACR4O,EAAQ5O,aAAeiE,EAAMgH,UAAUC,SAASC,UAEpDoG,EAAKI,WAAa,IAAM/C,EAAQgD,MAEpChD,SACIjN,MAAO,GACPS,MAAO,GACP8B,SAAU,GACV2N,UAAU,EACV7R,aAAc,KACd8H,aACI,QACA,QACA,SACA,UACA,UAEJgK,QAAQ,EACRC,UACIC,sBAAuB,oBACvBlK,aACImK,MAAO,QACP1B,OAAQ,SACRC,MAAO,QACPC,OAAQ,SACRC,QAAS,UACTC,OAAQ,UAEZJ,QACI2B,YAAa,iBACb1T,SAAU,YAEdgS,OACI0B,YAAa,iBACb1T,SAAU,WAEdiS,QACIjS,SAAU,WACV0T,YAAa,iBACbC,SAAU,eAEdzB,SACIwB,YAAa,iBACbC,SAAU,cACV3T,SAAU,YACVkC,IAAK,QAETiQ,QACIuB,YAAa,iBACbC,SAAU,cACV3T,SAAU,WACV4T,GAAI,QAER/P,KACIgQ,MAAO,OACPC,YAAa,OACbL,MAAO,QACPM,MAAO,SACPC,WAAY,iBACZC,GAAI,OAERC,iBACI7C,MAAO,QACPC,OAAQ,SACR6C,MAAO,QACPC,OAAQ,SACRC,KAAM,QAEVC,UACIpS,IAAK,MACLqS,QAAS,UACTC,QAAS,iBAIrBxN,QAAS,UACTyN,cAAe,WAAA,GACP1B,GAAO1C,KACPtO,EAAOgR,EAAK2B,MAChB3B,GAAK4B,WAAWC,KAAK,qBAAqBC,qBACtCxB,SAAUN,EAAK3C,QAAQiD,SACvBlQ,MAAOpB,EAAK/B,UAAY,EACxB8U,SAAU,EACVC,OAAQ,IACRC,IAAK,EACLC,OAAQ,WACJlT,EAAK/B,SAAWqQ,KAAKlN,QACrB4P,EAAKmC,eAIjBC,aAAc,SAAUC,GAAV,GACNrC,GAAO1C,KACPkE,GAAWxB,EAAKsC,SAASvC,SAAWC,EAAKsC,UAAUvI,MACnDzK,IAAiB0Q,EAAKuC,eAAexC,SAAWC,EAAKuC,gBAAgBxI,MACrErK,EAAW,KACXmE,EAAY,IACXwO,KACe,QAAZb,GACA9R,EAAW2K,EACXxG,EAAYvE,GACO,YAAZkS,GACP9R,EAAW4K,EACXzG,EAAYvE,GACO,YAAZkS,GACP9R,EAAW6K,EACX1G,EAAYvE,GAEZI,IACQJ,OAAQA,EACRH,KAAYqS,KAI5BxB,EAAK2B,OAAOjS,SAAWA,EACvBsQ,EAAK2B,OAAO9N,UAAYA,GAE5B2O,aAAc,WAAA,GAKNC,GACAC,EACA5S,EACA6S,EAPA3C,EAAO1C,KACP5N,EAAWsQ,EAAK2B,OAAOjS,SACvBmE,EAAYmM,EAAK2B,OAAO9N,UACxB+O,EAAsB5C,EAAKuC,cAK3B7S,KACAI,EAASJ,EAASI,OACd+D,IACe,IAAX/D,GACA4S,EAAe,MACfD,EAAgB5O,GACE,IAAX/D,GACP4S,EAAe,UACfD,EAAgB5O,GACE,IAAX/D,IACP4S,EAAe,UACfD,EAAgB5O,IAGnB6O,IACDhT,EAAWA,EAAS,GACpBgT,EAAehT,EAASP,IACxBsT,EAAgB/S,EAASJ,QAAU,IAEvCqT,EAASC,EAAoBxS,MAAQ,QAAU,MAC/CwS,EAAoBD,GAAQF,GAC5BzC,EAAKsC,SAASK,GAAQD,KAG9BG,aAAc,WAAA,GACOC,GAAb9C,EAAO1C,KACPyF,EAAiB/C,EAAK3C,QAAQmD,SAASe,SACvCyB,EAAgBhD,EAAK3C,QAAQmD,SAASW,gBACtC8B,EAAejD,EAAK4B,WAAWC,KAAK,oBACpCK,EAAS,WACTlC,EAAKoC,eACLpC,EAAKmC,WAELc,GAAa,KACbjD,EAAKuC,eAAiB,GAAIpH,GAAa6E,EAAK4B,WAAWC,KAAK,4BACxDK,OAAQA,EACRgB,cAAe,OACfC,eAAgB,QAChBC,aAEQC,KAAML,EAAc1E,MACpBlO,MAAO,MAGPiT,KAAML,EAAczE,OACpBnO,MAAO,MAGPiT,KAAML,EAAc5B,MACpBhR,MAAO,MAGPiT,KAAML,EAAc3B,OACpBjR,MAAO,MAGPiT,KAAML,EAAc1B,KACpBlR,MAAO,SAInB0S,IAEQO,KAAMN,EAAe5T,IACrBiB,MAAO,QAGPiT,KAAMN,EAAevB,QACrBpR,MAAO,YAGPiT,KAAMN,EAAetB,QACrBrR,MAAO,YAGf4P,EAAKsC,SAAW,GAAInH,GAAa8H,GAC7B7S,MAAO4P,EAAK3C,QAAQxM,MAAMzC,SAC1B8T,OAAQA,EACRgB,cAAe,OACfC,eAAgB,QAChBC,WAAYN,EAAKrQ,OAAO5F,EAAEyW,IAAI5Q,EAAMgH,UAAUC,SAASpM,KAAKqS,MAAO,SAAU2D,EAASvT,GAClF,OACIqT,KAAME,EACNnT,MAAOJ,QAInBgQ,EAAKwC,iBAGbgB,cAAe,WAAA,GAiBCxT,GAAKlB,EACL2U,EAAOC,EACP5T,EAlBRkQ,EAAO1C,KACPtO,EAAOgR,EAAK2B,OACZjS,EAAWsQ,EAAK4B,WAAWC,KAAK,4BACpC,IAAInS,EAAS,KACTA,EAASwR,GAAG7B,GAAQW,EAAKI,WAAY,WACjCpR,EAAKU,SAAW7C,EAAEyW,IAAI5T,EAASiU,OAAO,YAAa,SAAUC,GACzD,OACIzU,KAAYyU,EAASxT,MACrBd,OAAQ,KAGX0Q,EAAK3C,QAAQkD,QACdP,EAAKmC,aAGTnT,EAAKU,UAIL,IAFI+T,EAAI,EAAGC,EAAIhU,EAASI,OACpBA,EAASd,EAAKU,SAASI,OACpB2T,EAAIC,EAAGD,IAEV,IADA3U,EAAUY,EAAS+T,GACdzT,EAAM,EAAGA,EAAMF,EAAQE,IACpBlB,EAAQsB,OAASpB,EAAKU,SAASM,GAAKb,MACpCL,EAAQ+U,SAAU,IAO1CC,cAAe,WAAA,GACP9D,GAAO1C,KACPtO,EAAOgR,EAAK2B,OACZoC,EAAgB/D,EAAK4B,WAAWC,KAAK,oBACrCkC,GAAc,KACd/D,EAAKgE,UAAY,GAAItR,GAAMuI,GAAGgJ,eAAeF,GACzCzD,SAAUN,EAAK3C,QAAQiD,SACvB2B,IAAK,EACLiC,IAAK,GACLnC,SAAU,EACVC,OAAQ,IACR5R,MAAOpB,EAAKsK,UAAYtK,EAAKsK,UAAU,GAAK0G,EAAK3C,QAAQxM,MAAMlD,UAC/DuU,OAAQ,WACJ,GAAI9R,GAAQkN,KAAKlN,OACjBpB,GAAKsK,UAAYlJ,GAASA,GAASA,EACnC4P,EAAKmC,gBAKrBgC,WAAY,WACR,GAAInE,GAAO1C,KAAM8G,EAAQpE,EAAK4B,WAAWC,KAAK,kBAAmB7S,EAAOgR,EAAK2B,MAC7E3B,GAAKqE,OAASD,EAAMtC,qBAChBxB,SAAUN,EAAK3C,QAAQiD,SACvBlQ,MAAOpB,EAAKkG,OAAS,EACrB6M,SAAU,EACVC,OAAQ,IACRC,IAAK,EACLC,OAAQ,WACJlT,EAAKkG,MAAQoI,KAAKlN,QAClB4P,EAAKmC,cAEVW,KAAK,wBAEZwB,WAAY,WACR,GAAItE,GAAO1C,KAAM8G,EAAQpE,EAAK4B,WAAWC,KAAK,kBAAmBhR,EAAQmP,EAAK3C,QAAQxM,MAAO7B,EAAOgR,EAAK2B,OAAQnL,EAAQxH,EAAKwH,KAC9HwJ,GAAKuE,OAASH,EAAMI,iBAChBvC,IAAKzL,GAASA,EAAQ3F,EAAQ2F,EAAQ3F,EACtCT,MAAOoG,GAAS,GAAIzI,MAAK8C,EAAM7C,cAAe6C,EAAMvD,WAAYuD,EAAMlD,UAAW,GAAI,GAAI,IACzFuU,OAAQ,WACJ,GAAI9U,GAAOkQ,KAAKlN,OAChBpB,GAAKwH,MAAQ,GAAIzI,MAAKX,EAAKY,cAAeZ,EAAKE,WAAYF,EAAKO,UAAW,GAAI,GAAI,IACnFqS,EAAKmC,cAEVW,KAAK,oBAEZX,SAAU,WACD7E,KAAKD,QAAQkD,QACdjD,KAAKmH,QAAQ,aAIrBhK,EAAmBD,EAAqB4C,QACxC0C,KAAM,SAAUC,EAAS1C,GACrB,GAAI2C,GAAO1C,IACX9C,GAAqBkE,GAAGoB,KAAKG,KAAKD,EAAMD,EAAS1C,GACjD2C,EAAK0E,iBACL1E,EAAK2E,iBACL3E,EAAK5P,MAAM4P,EAAK3C,QAAQjN,QAE5BiN,SAAWgD,KAAM,oBACjBpM,QAAS,UACT2Q,QAAS,WACL,GAAI5E,GAAO1C,IACX0C,GAAK6E,WAAWD,UAChB5E,EAAK4B,WAAWC,KAAK,0CAA0CiD,IAAIzF,GAAQW,EAAKI,YAChF1N,EAAMkS,QAAQ5E,EAAK4B,YACnBpH,EAAqBkE,GAAGkG,QAAQ3E,KAAKD,IAEzC5P,MAAO,SAAUA,GAAV,GAGC8C,GAFA8M,EAAO1C,KACP3K,EAAWqN,EAAK3C,QAAQ1K,QAE5B,OAAIvC,KAAUtD,EACLkT,EAAK2B,OAAOzO,KAGV8G,EAAUgG,EAAK2B,OAAQhP,GAFnB,IAIfqN,EAAK2B,OAAS1L,EAAU7F,EAAOuC,OAC/BO,EAAO8M,EAAK2B,OAAOzO,KACfA,EACA8M,EAAK6E,WAAWzU,MAAM8C,GAEtB8M,EAAK6E,WAAWE,OAAO,GAE3B/E,EAAKgF,UAAUhF,EAAK6E,WAAWzU,SAP/B4P,IASJ2E,eAAgB,WACZ,GAAI5E,GAAUzC,KAAKyC,QAASkF,EAAYpY,EAAE,gCAAiCqY,EAAgBnF,EAAQoF,OAAO,gBACtGD,GAAc,GACdD,EAAUG,YAAYF,GAEtBnF,EAAQsF,OAAOJ,GAEnB3H,KAAKsE,WAAaqD,GAEtBP,eAAgB,WACZ,GAAmM3G,GAA/LiC,EAAO1C,KAAMD,EAAU2C,EAAK3C,QAAS9G,EAAc8G,EAAQ9G,YAAaiK,EAAWnD,EAAQmD,SAASjK,YAAa+O,EAAMzY,EAAE,aAAa0Y,MAAOC,MAAOnI,EAAQmD,SAASC,uBACzKlK,GAAc1J,EAAEyW,IAAI/M,EAAa,SAAUwH,GACvC,OACIsF,KAAM7C,EAASzC,GACf3N,MAAO2N,KAGfA,EAAYxH,EAAY,GACpBwH,GAAiC,UAApBA,EAAU3N,QACvB2N,EAAU3N,MAAQ,IAEtB4P,EAAKD,QAAQsF,OAAOC,GACpBtF,EAAK6E,WAAa,GAAI1J,GAAamK,GAC/BpC,cAAe,OACfC,eAAgB,QAChBC,WAAY7M,EACZ2L,OAAQ,WACJlC,EAAK2B,UACL3B,EAAKgF,UAAUhF,EAAK6E,WAAWzU,SAC/B4P,EAAKyE,QAAQ,cAIzBO,UAAW,SAAUjH,GAAV,GACHiC,GAAO1C,KACPtO,EAAOgR,EAAK2B,OACZtE,EAAU2C,EAAK3C,QACfyF,GACA/E,UAAWA,GAAa,QACxB7D,kBAAmBA,EACnBzL,aAAc4O,EAAQ5O,aACtB+R,SAAUnD,EAAQmD,SAASzC,GAC3BjN,IAAKuM,EAAQmD,SAAS1P,IAI1B,OAFA4B,GAAMkS,QAAQ5E,EAAK4B,YACnB5B,EAAK4B,WAAW6D,KAAKrL,EAAyB0I,IACzC/E,GAIL/O,EAAKkE,KAAO6K,EACM,WAAdA,GAA2B/O,EAAKU,WAChCV,EAAKU,WACGP,IAAKkO,EAAQxM,MAAMzC,SACnBkB,OAAQ,KAGpB0Q,EAAK0B,gBACL1B,EAAKwD,gBACLxD,EAAK8D,gBACL9D,EAAK6C,eACL7C,EAAK0F,aACL1F,EAAKmE,aACLnE,EAAKsE,aACLtE,EAAK2F,UACL3F,EAAK4F,OAfL5W,IAHIgR,EAAK2B,UACL,IAmBR+D,WAAY,WAAA,GAKJrI,GAJA2C,EAAO1C,KACPtO,EAAOgR,EAAK2B,OACZtU,EAAQ2B,EAAKwK,SAAWwG,EAAK3C,QAAQxM,MAAMvD,WAAa,GACxDuY,EAAc7F,EAAK4B,WAAWC,KAAK,iBAEnCgE,GAAY,KACZxI,GACI6E,OAAQ,WACJlT,EAAKwK,SAAiB8D,KAAKlN,SAC3B4P,EAAKyE,QAAQ,WAEjBvB,cAAe,OACfC,eAAgB,QAChBC,WAAYvW,EAAEyW,IAAI5Q,EAAMgH,UAAUC,SAASH,OAAOoG,MAAO,SAAUkG,EAAW9V,GAC1E,OACIqT,KAAMyC,EACN1V,MAAOJ,EAAM,MAIzBgQ,EAAK+F,QAAU,GAAI5K,GAAa0K,EAAY,GAAIxI,GAChD2C,EAAKgG,QAAU,GAAI7K,GAAa0K,EAAY,GAAIxI,GAC5ChQ,IACAA,EAAQA,EAAM,GACd2S,EAAK+F,QAAQ3V,MAAM/C,GACnB2S,EAAKgG,QAAQ5V,MAAM/C,MAI/BuY,KAAM,WAAA,GASEK,GARAjG,EAAO1C,KACPtO,EAAOgR,EAAK2B,OACZsD,EAAYjF,EAAK4B,WACjBsE,EAAYlG,EAAKI,WACjB+F,EAAQ,SAAUC,GAClBpG,EAAKqG,WAAWD,EAAEE,cAAclW,OAChC4P,EAAKyE,QAAQ,UAGjBzE,GAAKuG,aAAetB,EAAUpD,KAAK,sBAAsBX,GAAG7B,GAAQ6G,EAAWC,GAC/EnG,EAAKwG,aAAevB,EAAUpD,KAAK,sBAAsBX,GAAG7B,GAAQ6G,EAAWC,GAC/EnG,EAAKyG,aAAexB,EAAUpD,KAAK,sBAAsBX,GAAG7B,GAAQ6G,EAAWC,GAC3EnX,EAAKkG,MACL+Q,EAAU,QACHjX,EAAKwH,QACZyP,EAAU,SAEdjG,EAAKqG,WAAWJ,IAEpBN,QAAS,WAAA,GACD3F,GAAO1C,KACPtO,EAAOgR,EAAK2B,OACZxC,EAAwB,YAAdnQ,EAAKkE,KACfwT,EAAavH,EAAUa,EAAK2G,gBAAkB3G,EAAK4G,YACnDC,EAAW,aAAe1H,EAAU,QAAU,QAAU,SACxD2H,EAAe9G,EAAK4B,WAAWC,KAAKgF,IACnC1H,GAAyB,WAAdnQ,EAAKkE,QAGrB4T,EAAa5F,GAAG7B,GAAQW,EAAKI,WAAY,SAAUgG,GAC/CM,EAAWzG,KAAKD,EAAMoG,EAAEE,cAAclW,OACtC4P,EAAKyE,QAAQ,YAEjBzE,EAAK+G,gBAAkBD,EAAaE,GAAG,GACvChH,EAAKiH,eAAiBH,EAAaE,GAAG,GACtCN,EAAWzG,KAAKD,EAAMhR,EAAKU,SAAW,UAAY,cAEtD2W,WAAY,SAAUJ,GAAV,GAEJ/Q,GAAOsB,EACP0Q,EAAaC,EAFbnH,EAAO1C,IAGK,WAAZ2I,GACAjG,EAAKwG,aAAaY,KAAK,WAAW,GAClCF,GAAc,EACdC,GAAc,EACdjS,EAAQ8K,EAAKqE,OAAOjU,QACpBoG,EAAQ,MACW,UAAZyP,GACPjG,EAAKyG,aAAaW,KAAK,WAAW,GAClCF,GAAc,EACdC,GAAc,EACdjS,EAAQ,KACRsB,EAAQwJ,EAAKuE,OAAOnU,UAEpB4P,EAAKuG,aAAaa,KAAK,WAAW,GAClCF,EAAcC,GAAc,EAC5BjS,EAAQsB,EAAQ,MAEhBwJ,EAAKqE,QACLrE,EAAKqE,OAAOgD,OAAOH,GAEnBlH,EAAKuE,QACLvE,EAAKuE,OAAO8C,OAAOF,GAEvBnH,EAAK2B,OAAOzM,MAAQA,EACpB8K,EAAK2B,OAAOnL,MAAQA,GAExBmQ,gBAAiB,SAAUW,GAAV,GAKThO,GAJA0G,EAAO1C,KACPiK,GAAiB,EACjBC,GAAgB,EAChBnF,GAAQ,CAEM,cAAdiF,GACAtH,EAAK+G,gBAAgBK,KAAK,WAAW,GACrC9N,GAAa0G,EAAKgE,UAAU5T,SAC5BmX,GAAiB,EACjBC,GAAgB,EAChBnF,GAAQ,IAERrC,EAAKiH,eAAeG,KAAK,WAAW,GACpC9N,EAAY,MAEhB0G,EAAKsC,SAAS+E,OAAOG,GACrBxH,EAAKuC,eAAe8E,OAAOG,GAC3BxH,EAAKgE,UAAUqD,OAAOE,GACtBvH,EAAK2B,OAAOrI,UAAYA,EACxB0G,EAAKoC,aAAaC,IAEtBuE,YAAa,SAAUa,GAAV,GAILpa,GAHA2S,EAAO1C,KACPoK,GAAe,EACfC,GAAe,CAEF,cAAbF,GACAC,GAAe,EACfC,GAAe,EACfta,EAAQ2S,EAAK+F,QAAQ3V,SAErB/C,EAAQ2S,EAAKgG,QAAQ5V,QAEzB4P,EAAK+F,QAAQsB,OAAOK,GACpB1H,EAAKgG,QAAQqB,OAAOM,GACpB3H,EAAK2B,OAAOnI,QAAUnM,GACtB2S,EAAK2G,gBAAgBc,MAG7BxM,EAAG2M,OAAOnN,GACNC,EAA6BhI,EAAMmN,SAAS,2NAC5ClF,EAAqCjI,EAAMmN,SAAS,oiFACpDjF,EAAkClI,EAAMmN,SAAS,kXACjDhF,EAAmCnI,EAAMmN,SAAS,8OAClD/E,EAAyBN,EAAqB4C,QAC9C0C,KAAM,SAAUC,EAAS1C,GACrB,GAAI2C,GAAO1C,IACX9C,GAAqBkE,GAAGoB,KAAKG,KAAKD,EAAMD,EAAS1C,GACjDA,EAAU2C,EAAK3C,QACf2C,EAAK6H,gBAAkBnV,EAAMmN,SAAS,6CACtCG,EAAK5P,MAAMiN,EAAQjN,OACnB4P,EAAK8H,MAAQzK,EAAQ0K,KACrB/H,EAAKgI,oBACLhI,EAAKiI,uBACLjI,EAAKkI,cAAgBlI,EAAK2B,QAE9BtE,SACIgD,KAAM,yBACN8H,YACIC,KAAM,QACNC,MAAO,eAEX9H,QAAQ,EACRC,UACI8H,OAAQ,SACRC,OAAQ,OACRC,SAAU,cACVC,YAAa,iBACbC,YAAa,eACb5X,KACI6X,UACIjI,MAAO,QACPM,MAAO,WACPE,GAAI,SAERR,MAAO,QACPM,MAAO,mBACPE,GAAI,iBAERjC,OAAShS,SAAU,IACnB+R,QAAU/R,SAAU,IACpBiS,QAAUjS,SAAU,IACpBkS,SACIlS,SAAU,GACV2b,SAAU,cACVC,WAAY,mBACZC,UAAW,kBACXnI,YAAa,eACboI,MAAO,QACP5Z,IAAK,QAETiQ,QACInS,SAAU,GACV2b,SAAU,cACVC,WAAY,mBACZC,UAAW,kBACXnI,YAAa,iBACboI,MAAO,QACP1b,MAAO,QACP8B,IAAK,SAIjB8E,QAAS,UACT7D,MAAO,SAAUA,GAAV,GACC4P,GAAO1C,KACP3K,EAAWqN,EAAK3C,QAAQ1K,QAC5B,OAAIvC,KAAUtD,EACLkT,EAAK2B,OAAOzO,KAGV8G,EAAUgG,EAAK2B,OAAQhP,GAFnB,IAIfqN,EAAK2B,OAAS1L,EAAU7F,EAAOuC,OAA/BqN,IAEJ4E,QAAS,WACLtH,KAAK0L,eACLtW,EAAMkS,QAAQtH,KAAK2L,YACnB3L,KAAKyC,QAAQ+E,IAAIzF,GAAQ/B,KAAK8C,YAC9B5F,EAAqBkE,GAAGkG,QAAQ3E,KAAK3C,OAEzCoE,cAAe,WAAA,GACP1B,GAAO1C,KACPtO,EAAOgR,EAAK2B,MAChB3B,GAAK4B,WAAWC,KAAK,qBAAqB9H,IAAIiG,EAAK2B,OAAO1U,UAAY,GAAGiU,GAAG5B,GAASU,EAAKI,WAAY,SAAUgG,GAC5GpX,EAAK/B,SAAWmZ,EAAE8C,OAAO9Y,MACzB4P,EAAKmC,cAGb6F,kBAAmB,WAAA,GACXhI,GAAO1C,KACPpK,EAAO8M,EAAK3C,QAAQmD,SAASjK,YAAY+G,KAAKqE,OAAOzO,MAAQ,QACjE8M,GAAKmJ,eAAiBtc,EAAE,wDACxBmT,EAAKoJ,aAAevc,EAAE,kBAAoBqG,EAAO,WACjD8M,EAAKD,QAAQsF,OAAOrF,EAAKoJ,cAAc/D,OAAOrF,EAAKmJ,gBACnDnJ,EAAKD,QAAQsJ,QAAQ,aAAanI,GAAG7B,GAAQW,EAAKI,WAAY,SAAUgG,GACpEA,EAAEkD,iBACFtJ,EAAKuJ,YAAY,UACjBvJ,EAAK8H,MAAM0B,SAASxJ,EAAKyJ,MAAOzJ,EAAK3C,QAAQ8K,WAAWC,SAGhEsB,WAAY,WACR,GAAI1J,GAAO1C,IACX,OAAO,wEAA0E0C,EAAK3C,QAAQmD,SAAS1P,IAAIiQ,YAAc,uBAE7HkH,qBAAsB,WAAA,GACdjI,GAAO1C,KACPqM,EAAgB9c,EAAEmT,EAAK0J,cAActE,YAAYpF,EAAKD,QAAQsJ,QAAQ,cACtEO,EAAe/c,EAAE,6FAA6Fgd,SAASF,EAAc9H,KAAK,YAC9I8H,GAAczI,GAAG7B,GAAQW,EAAKI,WAAY,SAAUgG,GAChDA,EAAEkD,iBACFtJ,EAAK8J,gBAAgB;GAEzB9J,EAAK+J,qBAAuBJ,EAAcK,SAAShK,EAAK2B,OAAOzO,MAC/D8M,EAAKiK,oBAAsBL,EAAa/H,KAAK,0BAA0BwB,KAAKrD,EAAKkK,aAErFC,eAAgB,WAAA,GACRnK,GAAO1C,KACPqM,EAAgB9c,EAAEmT,EAAK0J,cAActE,YAAYpF,EAAK4B,YACtDgI,EAAe/c,EAAE,iJAAiJgd,SAASF,EAAc9H,KAAK,YAClM8H,GAAczI,GAAG7B,GAAQW,EAAKI,WAAY,SAAUgG,GAChDA,EAAEkD,iBACFtJ,EAAK8J,gBAAgB,SAEzB9J,EAAKoK,eAAiBT,EAAcU,YAAY,oBAAqBrK,EAAK2B,OAAOzO,MACjF8M,EAAKsK,WAAaV,EAAa/H,KAAK,0BAA0BwB,KAAKrD,EAAKkK,aAE5EJ,gBAAiB,SAAUS,GACvB,GAAIvK,GAAO1C,IACX0C,GAAKuJ,YAAYgB,GACjBvK,EAAK8H,MAAM0B,SAASxJ,EAAKyJ,MAAOzJ,EAAK3C,QAAQ8K,WAAWC,OAE5D8B,SAAU,WAAA,GACFlb,GAAOsO,KAAKqE,OACZnB,EAAWlD,KAAKD,QAAQmD,SAAS1P,IACjCuS,EAAO7C,EAASE,KAMpB,OALI1R,GAAKkG,MACLmO,EAAO3Q,EAAMsP,OAAO,UAAWxB,EAASQ,MAAOhS,EAAKkG,OAC7ClG,EAAKwH,QACZ6M,EAAO3Q,EAAMsP,OAAO,YAAaxB,EAASU,GAAIlS,EAAKwH,QAEhD6M,GAEXqB,eAAgB,WAAA,GACR1E,GAAO1C,KACPkN,EAAoBxK,EAAK3C,QAAQmD,SAASjK,YAC1CkP,EAAO5K,GACPuI,WAAYvW,EAAEyW,IAAIhG,KAAKD,QAAQ9G,YAAa,SAAUwH,GAClD,OACIsF,KAAMmH,EAAkBzM,GACxB3N,MAAqB,UAAd2N,EAAwBA,EAAY,MAGnD3N,MAAO4P,EAAK2B,OAAOzO,MAAQ,GAC3BuX,GAAI/X,EAAM+X,IAEdzK,GAAKyJ,MAAM1J,QAAQ8B,KAAK,oBAAoBwD,OAAOI,GACnDzF,EAAKyJ,MAAM1J,QAAQ8B,KAAK,uBAAuBX,GAAG5B,GAASU,EAAKI,WAAY,SAAUgG,GAClF,GAAIhW,GAAQgW,EAAE8C,OAAO9Y,KACrB4P,GAAK2B,QAAWzO,KAAM9C,GACtB4P,EAAK0K,iBAAgB,MAG7BC,mBAAoB,WAAA,GAUZlF,GATAzF,EAAO1C,KACPsN,EAAc5K,EAAK3C,QAAQmD,SAAS1P,IAAI6X,SACxC3Z,EAAOgR,EAAK2B,OACZvR,EAAQ,EACRpB,GAAKkG,MACL9E,EAAQ,QACDpB,EAAKwH,QACZpG,EAAQ,SAERqV,EAAO5K,GACPuI,aAEQC,KAAMuH,EAAYlK,MAClBtQ,MAAO,KAGPiT,KAAMuH,EAAY5J,MAClB5Q,MAAO,UAGPiT,KAAMuH,EAAY1J,GAClB9Q,MAAO,UAGfA,MAAOA,EACPqa,GAAI/X,EAAM+X,KAEdzK,EAAKyJ,MAAM1J,QAAQ8B,KAAK,oBAAoBwD,OAAOI,GACnDzF,EAAKyJ,MAAM1J,QAAQ8B,KAAK,uBAAuBX,GAAG5B,GAASU,EAAKI,WAAY,SAAUgG,GAAV,GACpEhW,GAAQgW,EAAE8C,OAAO9Y,MACjB8E,EAAQ,KACRsB,EAAQ,IACZwJ,GAAK6K,aAAaza,GACd4P,EAAKqE,OAAOvU,QACZoF,EAAQ8K,EAAKqE,OAAOtK,MACpBvD,EAAQ,MACDwJ,EAAKuE,OAAOzU,SACnBoF,EAAQ,KACRsB,EAAQwJ,EAAKuE,OAAOxK,IAAMrH,EAAMwF,UAAU8H,EAAKuE,OAAOxK,MAAO,cAAgBiG,EAAKuE,OAAOnU,SAE7FpB,EAAKkG,MAAQA,EACblG,EAAKwH,MAAQA,KAGrB+S,YAAa,SAAUuB,GAAV,GACL9K,GAAO1C,KACPD,EAAU2C,EAAK3C,QACfmD,EAAWnD,EAAQmD,SACnBkI,EAAclI,EAAsB,WAAbsK,EAAwB,cAAgB,YAC/DrF,EAAO,qNAAiOjF,EAAS8H,OAAS,gBAAuB9H,EAAS8H,OAAS,4DAA8D9H,EAASkI,YAAc,sEAAwElI,EAAS+H,OAAS,iBAAwB/H,EAAS+H,OAAS,qDAC5fwC,EAAe/K,EAAK8H,MAAMkD,OAAOC,EACrCjL,GAAKyJ,MAAQzJ,EAAK8H,MAAMzC,OAAOI,EAAO/K,GAA6BgO,YAAaA,KAChF1I,EAAKyJ,MAAM1J,QAAQmB,GAAG7B,GAAQW,EAAKI,WAAY,6CAA8C,SAAUgG,GACnGA,EAAEkD,iBACFlD,EAAE8E,kBACEre,EAAEyQ,MAAM6N,SAAS,uBACjBnL,EAAKyE,QAAQ,UACbzE,EAAKkI,cAAgBrb,EAAEuQ,UAAW4C,EAAK2B,SAEvC3B,EAAK2B,OAAS3B,EAAKkI,aAEvB,IAAInK,GAAYiC,EAAK2B,OAAOzO,IAC5B8M,GAAKiK,oBAAoB5G,KAAKrD,EAAKkK,YACnClK,EAAK+J,qBAAqBC,SAASjM,GAA2B,UAAdA,GAChDiC,EAAKsK,WAAWjH,KAAKrD,EAAKkK,YAC1BlK,EAAKoJ,aAAa/F,KAAK7C,EAASjK,YAAYwH,GAAa,UACzDiC,EAAK8H,MAAMsD,IAAI,WAAY,WACvBpL,EAAKgJ,iBAEThJ,EAAK8H,MAAM0B,SAASuB,EAAc/K,EAAK3C,QAAQ8K,WAAWE,SAE9DrI,EAAK4B,WAAa5B,EAAKyJ,MAAM1J,QAAQ8B,KAAK,iBACzB,WAAbiJ,GACA9K,EAAK0E,iBACL1E,EAAK0K,iBAAgB,GACrB1K,EAAKmK,mBAELnK,EAAK2K,qBACL3K,EAAK6K,iBAGb7B,aAAc,WACN1L,KAAKmM,QACLnM,KAAKmM,MAAM7E,UACXtH,KAAKmM,MAAM1J,QAAQsL,SACnB/N,KAAKsE,WAAa,MAEtBtE,KAAKmM,MAAQ,MAEjBiB,gBAAiB,SAAUY,GAAV,GACTtL,GAAO1C,KACPS,EAAYiC,EAAK2B,OAAOzO,MAAQ,QAChC4P,GACA/E,UAAWA,EACX7D,kBAAmBoR,EAAWnR,EAA0BD,EACxDzL,aAAcuR,EAAK3C,QAAQ5O,aAC3B+R,SAAUR,EAAK3C,QAAQmD,SAASzC,IAEhC0H,EAAO9K,EAAmCmI,GAC1CmC,EAAYjF,EAAK4B,WAAa5B,EAAK4B,YAActE,KAAKwK,MAAMkD,OAAOO,QAAQ1J,KAAK,mBAChF7S,EAAOgR,EAAK2B,MAMhB,OALI3B,GAAKoK,gBACLpK,EAAKoK,eAAeC,YAAY,mBAAkC,UAAdtM,GAExDrL,EAAMkS,QAAQK,GACdA,EAAUQ,KAAKA,GACVA,GAKDR,EAAUuG,OAEI,WAAdzN,GAA2B/O,EAAKU,WAChCV,EAAKU,WACGP,IAAK6Q,EAAK3C,QAAQxM,MAAMzC,SACxBkB,OAAQ,KAGpB0Q,EAAK0B,gBACL1B,EAAK8D,gBACL9D,EAAKwD,gBACLxD,EAAK6C,eACL7C,EAAK0F,aACL1F,EAAK2F,UAbDV,IAJAjF,EAAK2B,UACLsD,EAAUwG,OACV,IAiBR3H,cAAe,WAAA,GACP9D,GAAO1C,KACPtO,EAAOgR,EAAK2B,OACZoC,EAAgB/D,EAAKgE,UAAYhE,EAAK4B,WAAWC,KAAK,oBAC1DkC,GAAcwB,MACVtD,IAAK,EACLiC,IAAK,KACNnK,IAAI/K,EAAKsK,UAAYtK,EAAKsK,UAAU,GAAK0G,EAAK3C,QAAQxM,MAAMlD,WAAWuT,GAAG5B,GAASU,EAAKI,WAAY,SAAUgG,GAC7GpX,EAAKkG,MAAQkR,EAAE8C,OAAO9Y,MACtB4P,EAAKmC,cAGbgC,WAAY,WACR,GAAInE,GAAO1C,KAAM8G,EAAQpE,EAAKqE,OAASrE,EAAK4B,WAAWC,KAAK,kBAAmB7S,EAAOgR,EAAK2B,MAC3FyC,GAAMrK,IAAI/K,EAAKkG,OAAS,GAAGgM,GAAG5B,GAASU,EAAKI,WAAY,SAAUsL,GAC9D1c,EAAKkG,MAAQwW,EAAGxC,OAAO9Y,MACvB4P,EAAKmC,cAGb0I,aAAc,SAAUc,GAAV,GAUN7I,GATA9C,EAAO1C,KACPtO,EAAOgR,EAAK2B,MACZgK,KAAe7e,IACXkC,EAAKkG,MACLyW,EAAa,QACN3c,EAAKwH,QACZmV,EAAa,UAGjB7I,GACA6I,WAAYA,EACZnL,SAAUR,EAAK3C,QAAQmD,SAAS1P,KAEpC4B,EAAMkS,QAAQ5E,EAAK4B,YACnB5B,EAAK4B,WAAW6D,KAAK7K,EAAgCkI,IACrD9C,EAAKmE,aACLnE,EAAKsE,cAETzB,aAAc,WAAA,GACOC,GAAb9C,EAAO1C,KACPyF,EAAiB/C,EAAK3C,QAAQmD,SAASe,SACvCyB,EAAgBhD,EAAK3C,QAAQmD,SAASW,gBACtCyK,EAAgB5L,EAAK4B,WAAWC,KAAK,oBACrCK,EAAS,WACTlC,EAAKoC,eACLpC,EAAKyE,QAAQ,UAEbmH,GAAc,KACd5L,EAAKuC,eAAiBvC,EAAK4B,WAAWC,KAAK,2BAA2B4D,KAAKzF,EAAK6L,WAExExI,KAAML,EAAc1E,MACpBlO,MAAO,MAGPiT,KAAML,EAAczE,OACpBnO,MAAO,MAGPiT,KAAML,EAAc5B,MACpBhR,MAAO,MAGPiT,KAAML,EAAc3B,OACpBjR,MAAO,MAGPiT,KAAML,EAAc1B,KACpBlR,MAAO,SAEX8R,OAAOA,GACXY,IAEQO,KAAMN,EAAe5T,IACrBiB,MAAO,QAGPiT,KAAMN,EAAevB,QACrBpR,MAAO,YAGPiT,KAAMN,EAAetB,QACrBrR,MAAO,YAGf0S,EAAOA,EAAKrQ,OAAO5F,EAAEyW,IAAI5Q,EAAMgH,UAAUC,SAASpM,KAAKqS,MAAO,SAAU2D,EAASvT,GAC7E,OACIqT,KAAME,EACNnT,MAAOJ,MAGfgQ,EAAKsC,SAAWsJ,EAAcnG,KAAKzF,EAAK6L,SAAS/I,IAAOZ,OAAOA,GAAQnI,IAAIiG,EAAK3C,QAAQxM,MAAMzC,UAC9F4R,EAAKwC,iBAGbkD,WAAY,WAAA,GAQA5C,GAPJ9C,EAAO1C,KACPtO,EAAOgR,EAAK2B,OACZ9Q,EAAQmP,EAAK3C,QAAQxM,MACrBxD,EAAQ2B,EAAKwK,SAAW3I,EAAMvD,WAAa,GAC3Cwe,EAAc9L,EAAK4B,WAAWC,KAAK,kBACnCkK,EAAarZ,EAAMgH,UAAUC,SAASH,OAAOoG,KAC7CkM,GAAY,KACRhJ,EAAOjW,EAAEyW,IAAIyI,EAAY,SAAUjG,EAAW9V,GAC9C,OACIqT,KAAMyC,EACN1V,MAAOJ,EAAM,KAGrB8b,EAAYrG,KAAKzF,EAAK6L,SAAS/I,IAAOZ,OAAO,WACzClT,EAAKwK,SAAiB8D,KAAKlN,SAE/B4P,EAAKgM,aAAeF,EAChBze,GACAye,EAAY/R,IAAI1M,EAAM,MAIlCsY,QAAS,WAAA,GASGsG,GACAxG,EAcA3F,EAvBJE,EAAO1C,KACPtO,EAAOgR,EAAK2B,OACZsD,EAAYjF,EAAK4B,WACjBpB,EAAWR,EAAK3C,QAAQmD,SAASxR,EAAKkE,MACtCgZ,EAAwBjH,EAAUpD,KAAK,kBACvCsK,EAAclH,EAAUpD,KAAK,mBAC7BuK,EAAenH,EAAUpD,KAAK,mBAC9BqK,GAAsB,KAClBD,EAAejd,EAAKU,SAAW,UAAY,WAC3C+V,EAAO5K,GACPzK,MAAO6b,EACP7I,aAEQC,KAAM7C,EAASqI,WACfzY,MAAO,aAGPiT,KAAM7C,EAASsI,UACf1Y,MAAO,YAGfqa,GAAI/X,EAAM+X,KAEV3K,EAAO,SAAU/F,GAAV,GACHsS,GAAcrM,EAAKsC,SAASvI,MAC5B0I,EAAgBzC,EAAKuC,eAAexI,MACpC0C,EAAWuD,EAAKgE,UAAUjK,MAC1B1M,EAAQ2S,EAAKgM,aAAehM,EAAKgM,aAAajS,MAAQ,IAC9C,cAARA,GACA/K,EAAKU,SAAW,KAChBV,EAAKsK,UAAYmD,GAAYA,GAAYA,EACzCzN,EAAKwK,OAASnM,IAAgBA,GAAUA,EACxC8e,EAAYV,OACZW,EAAaZ,SAEbxc,EAAKsK,UAAY,KACjBtK,EAAKwK,OAASnM,IAAgBA,GAAUA,EACxC2B,EAAKU,WACGJ,QAAemT,EACftT,KAAYkd,IAEpBF,EAAYX,OACZY,EAAaX,SAGrBS,EAAsB7G,OAAOI,GAC7ByG,EAAsBrK,KAAK,uBAAuBX,GAAG5B,GAASU,EAAKI,WAAY,SAAUgG,GACrFtG,EAAKsG,EAAE8C,OAAO9Y,SAElB0P,EAAKmM,KAGb3H,WAAY,WAAA,GACJtE,GAAO1C,KACP8G,EAAQpE,EAAK4B,WAAWC,KAAK,kBAC7BhR,EAAQmP,EAAK3C,QAAQxM,MACrB7B,EAAOgR,EAAK2B,OACZnL,EAAQxH,EAAKwH,MACbyL,EAAMzL,GAASA,EAAQ3F,EAAQ2F,EAAQ3F,CAEvCmP,GAAKuE,OADL7R,EAAM4Z,QAAQlI,MAAMhX,KACNgX,EAAMmB,KAAK,MAAO7S,EAAMI,SAASmP,EAAK,eAAelI,IAAIrH,EAAMI,SAAS0D,GAAS3F,EAAO,eAAeqQ,GAAG,SAAU,WAC9HlS,EAAKwH,MAAQ9D,EAAMwF,UAAUoF,KAAKlN,MAAO,gBAG/BgU,EAAMI,iBAChBvC,IAAKA,EACL7R,MAAOoG,GAAS3F,EAChBqR,OAAQ,WACJlT,EAAKwH,MAAQ8G,KAAKlN,WAEvB0S,KAAK,oBAGhB+I,SAAU,SAAU/I,EAAMyJ,GAAhB,GACFvc,GAAM,EACNyV,EAAO,GACP3V,EAASgT,EAAKhT,OACd+P,EAAWvC,KAAKuK,eAOpB,KANI0E,IACA9G,GAAQ5F,GACJzP,MAAO,GACPiT,KAAMkJ,KAGPvc,EAAMF,EAAQE,IACjByV,GAAQ5F,EAASiD,EAAK9S,GAE1B,OAAOyV,MAGfxK,EAAG2M,OAAO9M,IACZC,OAAOrI,MAAM8Z,QACRzR,OAAOrI,OACE,kBAAV9F,SAAwBA,OAAO6f,IAAM7f,OAAS,SAAU8f,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.scheduler.recurrence.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.scheduler.recurrence', [\n        'kendo.dropdownlist',\n        'kendo.datepicker',\n        'kendo.numerictextbox'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'scheduler.recurrence',\n        name: 'Recurrence',\n        category: 'web',\n        depends: [\n            'dropdownlist',\n            'datepicker',\n            'numerictextbox'\n        ],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, timezone = kendo.timezone, Class = kendo.Class, ui = kendo.ui, Widget = ui.Widget, DropDownList = ui.DropDownList, kendoDate = kendo.date, setTime = kendoDate.setTime, setDayOfWeek = kendoDate.setDayOfWeek, adjustDST = kendoDate.adjustDST, firstDayOfMonth = kendoDate.firstDayOfMonth, getMilliseconds = kendoDate.getMilliseconds, DAYS_IN_LEAPYEAR = [\n                0,\n                31,\n                60,\n                91,\n                121,\n                152,\n                182,\n                213,\n                244,\n                274,\n                305,\n                335,\n                366\n            ], DAYS_IN_YEAR = [\n                0,\n                31,\n                59,\n                90,\n                120,\n                151,\n                181,\n                212,\n                243,\n                273,\n                304,\n                334,\n                365\n            ], MONTHS = [\n                31,\n                28,\n                31,\n                30,\n                31,\n                30,\n                31,\n                31,\n                30,\n                31,\n                30,\n                31\n            ], WEEK_DAYS = {\n                0: 'SU',\n                1: 'MO',\n                2: 'TU',\n                3: 'WE',\n                4: 'TH',\n                5: 'FR',\n                6: 'SA'\n            }, WEEK_DAYS_IDX = {\n                'SU': 0,\n                'MO': 1,\n                'TU': 2,\n                'WE': 3,\n                'TH': 4,\n                'FR': 5,\n                'SA': 6\n            }, DATE_FORMATS = [\n                'yyyy-MM-ddTHH:mm:ss.fffzzz',\n                'yyyy-MM-ddTHH:mm:sszzz',\n                'yyyy-MM-ddTHH:mm:ss',\n                'yyyy-MM-ddTHH:mm',\n                'yyyy-MM-ddTHH',\n                'yyyy-MM-dd',\n                'yyyyMMddTHHmmssfffzzz',\n                'yyyyMMddTHHmmsszzz',\n                'yyyyMMddTHHmmss',\n                'yyyyMMddTHHmm',\n                'yyyyMMddTHH',\n                'yyyyMMdd'\n            ], RULE_NAMES = [\n                'months',\n                'weeks',\n                'yearDays',\n                'monthDays',\n                'weekDays',\n                'hours',\n                'minutes',\n                'seconds'\n            ], RULE_NAMES_LENGTH = RULE_NAMES.length, RECURRENCE_DATE_FORMAT = 'yyyyMMddTHHmmssZ', limitation = {\n                months: function (date, end, rule) {\n                    var monthRules = rule.months, months = ruleValues(monthRules, date.getMonth() + 1), changed = false;\n                    if (months !== null) {\n                        if (months.length) {\n                            date.setMonth(months[0] - 1, 1);\n                        } else {\n                            date.setFullYear(date.getFullYear() + 1, monthRules[0] - 1, 1);\n                        }\n                        changed = true;\n                    }\n                    return changed;\n                },\n                monthDays: function (date, end, rule) {\n                    var monthLength, month, days, changed = false, hours = date.getHours(), normalize = function (monthDay) {\n                            if (monthDay < 0) {\n                                monthDay = monthLength + monthDay + 1;\n                            }\n                            return monthDay;\n                        };\n                    while (date <= end) {\n                        month = date.getMonth();\n                        monthLength = getMonthLength(date);\n                        days = ruleValues(rule.monthDays, date.getDate(), normalize);\n                        if (days === null) {\n                            return changed;\n                        }\n                        changed = true;\n                        if (days.length) {\n                            date.setMonth(month, days.sort(numberSortPredicate)[0]);\n                            adjustDST(date, hours);\n                            if (month === date.getMonth()) {\n                                break;\n                            }\n                        } else {\n                            date.setMonth(month + 1, 1);\n                        }\n                    }\n                    return changed;\n                },\n                yearDays: function (date, end, rule) {\n                    var year, yearDays, changed = false, hours = date.getHours(), normalize = function (yearDay) {\n                            if (yearDay < 0) {\n                                yearDay = year + yearDay;\n                            }\n                            return yearDay;\n                        };\n                    while (date < end) {\n                        year = leapYear(date) ? 366 : 365;\n                        yearDays = ruleValues(rule.yearDays, dayInYear(date), normalize);\n                        if (yearDays === null) {\n                            return changed;\n                        }\n                        changed = true;\n                        year = date.getFullYear();\n                        if (yearDays.length) {\n                            date.setFullYear(year, 0, yearDays.sort(numberSortPredicate)[0]);\n                            adjustDST(date, hours);\n                            break;\n                        } else {\n                            date.setFullYear(year + 1, 0, 1);\n                        }\n                    }\n                    return changed;\n                },\n                weeks: function (date, end, rule) {\n                    var weekStart = rule.weekStart, year, weeks, day, changed = false, hours = date.getHours(), normalize = function (week) {\n                            if (week < 0) {\n                                week = 53 + week;\n                            }\n                            return week;\n                        };\n                    while (date < end) {\n                        weeks = ruleValues(rule.weeks, weekInYear(date, weekStart), normalize);\n                        if (weeks === null) {\n                            return changed;\n                        }\n                        changed = true;\n                        year = date.getFullYear();\n                        if (weeks.length) {\n                            day = weeks.sort(numberSortPredicate)[0] * 7 - 1;\n                            date.setFullYear(year, 0, day);\n                            setDayOfWeek(date, weekStart, -1);\n                            adjustDST(date, hours);\n                            break;\n                        } else {\n                            date.setFullYear(year + 1, 0, 1);\n                        }\n                    }\n                    return changed;\n                },\n                weekDays: function (date, end, rule) {\n                    var weekDays = rule.weekDays;\n                    var weekStart = rule.weekStart;\n                    var weekDayRules = ruleWeekValues(weekDays, date, weekStart);\n                    var hours = date.getHours();\n                    var weekDayRule, day;\n                    if (weekDayRules === null) {\n                        return false;\n                    }\n                    weekDayRule = weekDayRules[0];\n                    if (!weekDayRule) {\n                        weekDayRule = weekDays[0];\n                        setDayOfWeek(date, weekStart);\n                    }\n                    day = weekDayRule.day;\n                    if (weekDayRule.offset) {\n                        while (date <= end && !isInWeek(date, weekDayRule, weekStart)) {\n                            if (weekInMonth(date, weekStart) === numberOfWeeks(date, weekStart)) {\n                                date.setMonth(date.getMonth() + 1, 1);\n                                adjustDST(date, hours);\n                            } else {\n                                date.setDate(date.getDate() + 7);\n                                adjustDST(date, hours);\n                                setDayOfWeek(date, weekStart, -1);\n                            }\n                        }\n                    }\n                    if (date.getDay() !== day) {\n                        setDayOfWeek(date, day);\n                    }\n                    return true;\n                },\n                hours: function (date, end, rule) {\n                    var hourRules = rule.hours, startTime = rule._startTime, startHours = startTime.getHours(), hours = ruleValues(hourRules, startHours), changed = false;\n                    if (hours !== null) {\n                        changed = true;\n                        date.setHours(startHours);\n                        adjustDST(date, startHours);\n                        if (hours.length) {\n                            hours = hours[0];\n                            date.setHours(hours);\n                        } else {\n                            hours = date.getHours();\n                            date.setDate(date.getDate() + 1);\n                            adjustDST(date, hours);\n                            hours = hourRules[0];\n                            date.setHours(hours);\n                            adjustDST(date, hours);\n                        }\n                        if (rule.minutes) {\n                            date.setMinutes(0);\n                        }\n                        startTime.setHours(hours, date.getMinutes());\n                    }\n                    return changed;\n                },\n                minutes: function (date, end, rule) {\n                    var minuteRules = rule.minutes, currentMinutes = date.getMinutes(), minutes = ruleValues(minuteRules, currentMinutes), hours = rule._startTime.getHours(), changed = false;\n                    if (minutes !== null) {\n                        changed = true;\n                        if (minutes.length) {\n                            minutes = minutes[0];\n                        } else {\n                            hours += 1;\n                            minutes = minuteRules[0];\n                        }\n                        if (rule.seconds) {\n                            date.setSeconds(0);\n                        }\n                        date.setHours(hours, minutes);\n                        hours = hours % 24;\n                        adjustDST(date, hours);\n                        rule._startTime.setHours(hours, minutes, date.getSeconds());\n                    }\n                    return changed;\n                },\n                seconds: function (date, end, rule) {\n                    var secondRules = rule.seconds, hours = rule._startTime.getHours(), seconds = ruleValues(secondRules, date.getSeconds()), minutes = date.getMinutes(), changed = false;\n                    if (seconds !== null) {\n                        changed = true;\n                        if (seconds.length) {\n                            date.setSeconds(seconds[0]);\n                        } else {\n                            minutes += 1;\n                            date.setMinutes(minutes, secondRules[0]);\n                            if (minutes > 59) {\n                                minutes = minutes % 60;\n                                hours = (hours + 1) % 24;\n                            }\n                        }\n                        rule._startTime.setHours(hours, minutes, date.getSeconds());\n                    }\n                    return changed;\n                }\n            }, BaseFrequency = Class.extend({\n                next: function (date, rule) {\n                    var startTime = rule._startTime, day = startTime.getDate(), minutes, seconds;\n                    if (rule.seconds) {\n                        seconds = date.getSeconds() + 1;\n                        date.setSeconds(seconds);\n                        startTime.setSeconds(seconds);\n                        startTime.setDate(day);\n                    } else if (rule.minutes) {\n                        minutes = date.getMinutes() + 1;\n                        date.setMinutes(minutes);\n                        startTime.setMinutes(minutes);\n                        startTime.setDate(day);\n                    } else {\n                        return false;\n                    }\n                    return true;\n                },\n                normalize: function (options) {\n                    var rule = options.rule;\n                    if (options.idx === 4 && rule.hours) {\n                        rule._startTime.setHours(0);\n                        this._hour(options.date, rule);\n                    }\n                },\n                limit: function (date, end, rule) {\n                    var interval = rule.interval, ruleName, firstRule, modified, idx, day;\n                    while (date <= end) {\n                        modified = firstRule = undefined;\n                        day = date.getDate();\n                        for (idx = 0; idx < RULE_NAMES_LENGTH; idx++) {\n                            ruleName = RULE_NAMES[idx];\n                            if (rule[ruleName]) {\n                                modified = limitation[ruleName](date, end, rule);\n                                if (firstRule !== undefined && modified) {\n                                    break;\n                                } else {\n                                    firstRule = modified;\n                                }\n                            }\n                            if (modified) {\n                                this.normalize({\n                                    date: date,\n                                    rule: rule,\n                                    day: day,\n                                    idx: idx\n                                });\n                            }\n                        }\n                        if ((interval === 1 || !this.interval(rule, date)) && idx === RULE_NAMES_LENGTH) {\n                            break;\n                        }\n                    }\n                },\n                interval: function (rule, current) {\n                    var start = new Date(rule._startPeriod);\n                    var date = new Date(current);\n                    var hours = current.getHours();\n                    var weekStart = rule.weekStart;\n                    var interval = rule.interval;\n                    var frequency = rule.freq;\n                    var modified = false;\n                    var excess = 0;\n                    var month = 0;\n                    var day = 1;\n                    var diff;\n                    var startTimeHours;\n                    if (frequency === 'hourly') {\n                        diff = date.getTimezoneOffset() - start.getTimezoneOffset();\n                        startTimeHours = rule._startTime.getHours();\n                        date = date.getTime();\n                        if (hours !== startTimeHours) {\n                            date += (startTimeHours - hours) * kendoDate.MS_PER_HOUR;\n                        }\n                        date -= start;\n                        if (diff) {\n                            date -= diff * kendoDate.MS_PER_MINUTE;\n                        }\n                        diff = Math.floor(date / kendoDate.MS_PER_HOUR);\n                        excess = intervalExcess(diff, interval);\n                        if (excess !== 0) {\n                            this._hour(current, rule, excess);\n                            modified = true;\n                        }\n                    } else if (frequency === 'daily') {\n                        kendoDate.setTime(date, -start, true);\n                        diff = Math.round(date / kendoDate.MS_PER_DAY);\n                        excess = intervalExcess(diff, interval);\n                        if (excess !== 0) {\n                            this._date(current, rule, excess);\n                            modified = true;\n                        }\n                    } else if (frequency === 'weekly') {\n                        excess = this._getNumberOfWeeksBetweenDates(start, current);\n                        var normalizedCurrentIndex = normalizeDayIndex(current.getDay(), weekStart);\n                        var normalizedStartIndex = normalizeDayIndex(start.getDay(), weekStart);\n                        if (normalizedCurrentIndex < normalizedStartIndex) {\n                            excess += 1;\n                        }\n                        excess = intervalExcess(excess, interval);\n                        if (excess !== 0) {\n                            kendoDate.setDayOfWeek(current, rule.weekStart, -1);\n                            current.setDate(current.getDate() + excess * 7);\n                            adjustDST(current, hours);\n                            modified = true;\n                        }\n                    } else if (frequency === 'monthly') {\n                        diff = current.getFullYear() - start.getFullYear();\n                        diff = current.getMonth() - start.getMonth() + diff * 12;\n                        excess = intervalExcess(diff, interval);\n                        if (excess !== 0) {\n                            day = rule._hasRuleValue ? 1 : current.getDate();\n                            current.setFullYear(current.getFullYear(), current.getMonth() + excess, day);\n                            adjustDST(current, hours);\n                            modified = true;\n                        }\n                    } else if (frequency === 'yearly') {\n                        diff = current.getFullYear() - start.getFullYear();\n                        excess = intervalExcess(diff, interval);\n                        if (!rule.months) {\n                            month = current.getMonth();\n                        }\n                        if (!rule.yearDays && !rule.monthDays && !rule.weekDays) {\n                            day = current.getDate();\n                        }\n                        if (excess !== 0) {\n                            current.setFullYear(current.getFullYear() + excess, month, day);\n                            adjustDST(current, hours);\n                            modified = true;\n                        }\n                    }\n                    return modified;\n                },\n                _getNumberOfWeeksBetweenDates: function (first, second) {\n                    var weeks = (second - first) / 604800000;\n                    var exactWeeks = Math.floor(weeks);\n                    if (weeks - exactWeeks > 0.99) {\n                        exactWeeks = Math.round(weeks);\n                    }\n                    return exactWeeks;\n                },\n                _hour: function (date, rule, interval) {\n                    var startTime = rule._startTime, hours = startTime.getHours();\n                    if (interval) {\n                        hours += interval;\n                    }\n                    date.setHours(hours);\n                    hours = hours % 24;\n                    startTime.setHours(hours);\n                    adjustDST(date, hours);\n                },\n                _date: function (date, rule, interval) {\n                    var hours = date.getHours();\n                    date.setDate(date.getDate() + interval);\n                    if (!adjustDST(date, hours)) {\n                        this._hour(date, rule);\n                    }\n                }\n            }), HourlyFrequency = BaseFrequency.extend({\n                next: function (date, rule) {\n                    if (!BaseFrequency.fn.next(date, rule)) {\n                        this._hour(date, rule, 1);\n                    }\n                },\n                normalize: function (options) {\n                    var rule = options.rule;\n                    if (options.idx === 4) {\n                        rule._startTime.setHours(0);\n                        this._hour(options.date, rule);\n                    }\n                }\n            }), DailyFrequency = BaseFrequency.extend({\n                next: function (date, rule) {\n                    if (!BaseFrequency.fn.next(date, rule)) {\n                        this[rule.hours ? '_hour' : '_date'](date, rule, 1);\n                    }\n                }\n            }), WeeklyFrequency = DailyFrequency.extend({\n                setup: function (rule, eventStartDate) {\n                    if (!rule.weekDays) {\n                        rule.weekDays = [{\n                                day: eventStartDate.getDay(),\n                                offset: 0\n                            }];\n                    }\n                }\n            }), MonthlyFrequency = BaseFrequency.extend({\n                next: function (date, rule) {\n                    var day, hours;\n                    if (!BaseFrequency.fn.next(date, rule)) {\n                        if (rule.hours) {\n                            this._hour(date, rule, 1);\n                        } else if (rule.monthDays || rule.weekDays || rule.yearDays || rule.weeks) {\n                            this._date(date, rule, 1);\n                        } else {\n                            day = date.getDate();\n                            hours = date.getHours();\n                            date.setMonth(date.getMonth() + 1);\n                            adjustDST(date, hours);\n                            while (date.getDate() !== day) {\n                                date.setDate(day);\n                                adjustDST(date, hours);\n                            }\n                            this._hour(date, rule);\n                        }\n                    }\n                },\n                normalize: function (options) {\n                    var rule = options.rule, date = options.date, hours = date.getHours();\n                    if (options.idx === 0 && !rule.monthDays && !rule.weekDays) {\n                        date.setDate(options.day);\n                        adjustDST(date, hours);\n                    } else {\n                        BaseFrequency.fn.normalize(options);\n                    }\n                },\n                setup: function (rule, eventStartDate, date) {\n                    if (!rule.monthDays && !rule.weekDays) {\n                        date.setDate(eventStartDate.getDate());\n                    }\n                }\n            }), YearlyFrequency = MonthlyFrequency.extend({\n                next: function (date, rule) {\n                    var day, hours = date.getHours();\n                    if (!BaseFrequency.fn.next(date, rule)) {\n                        if (rule.hours) {\n                            this._hour(date, rule, 1);\n                        } else if (rule.monthDays || rule.weekDays || rule.yearDays || rule.weeks) {\n                            this._date(date, rule, 1);\n                        } else if (rule.months) {\n                            day = date.getDate();\n                            date.setMonth(date.getMonth() + 1);\n                            adjustDST(date, hours);\n                            while (date.getDate() !== day) {\n                                date.setDate(day);\n                                adjustDST(date, hours);\n                            }\n                            this._hour(date, rule);\n                        } else {\n                            date.setFullYear(date.getFullYear() + 1);\n                            adjustDST(date, hours);\n                            this._hour(date, rule);\n                        }\n                    }\n                },\n                setup: function () {\n                }\n            }), frequencies = {\n                'hourly': new HourlyFrequency(),\n                'daily': new DailyFrequency(),\n                'weekly': new WeeklyFrequency(),\n                'monthly': new MonthlyFrequency(),\n                'yearly': new YearlyFrequency()\n            }, CLICK = 'click', CHANGE = 'change';\n        function intervalExcess(diff, interval) {\n            var excess;\n            if (diff !== 0 && diff < interval) {\n                excess = interval - diff;\n            } else {\n                excess = diff % interval;\n                if (excess) {\n                    excess = interval - excess;\n                }\n            }\n            return excess;\n        }\n        function dayInYear(date) {\n            var month = date.getMonth();\n            var days = leapYear(date) ? DAYS_IN_LEAPYEAR[month] : DAYS_IN_YEAR[month];\n            return days + date.getDate();\n        }\n        function weekInYear(date, weekStart) {\n            var year, days;\n            date = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n            adjustDST(date, 0);\n            year = date.getFullYear();\n            if (weekStart !== undefined) {\n                setDayOfWeek(date, weekStart, -1);\n                date.setDate(date.getDate() + 4);\n            } else {\n                date.setDate(date.getDate() + (4 - (date.getDay() || 7)));\n            }\n            adjustDST(date, 0);\n            days = Math.floor((date.getTime() - new Date(year, 0, 1, -6)) / 86400000);\n            return 1 + Math.floor(days / 7);\n        }\n        function weekInMonth(date, weekStart) {\n            var firstWeekDay = firstDayOfMonth(date).getDay();\n            var firstWeekLength = 7 - (firstWeekDay + 7 - (weekStart || 7)) || 7;\n            if (firstWeekLength < 0) {\n                firstWeekLength += 7;\n            }\n            return Math.ceil((date.getDate() - firstWeekLength) / 7) + 1;\n        }\n        function normalizeDayIndex(weekDay, weekStart) {\n            return weekDay + (weekDay < weekStart ? 7 : 0);\n        }\n        function normalizeOffset(date, rule, weekStart) {\n            var offset = rule.offset;\n            if (!offset) {\n                return weekInMonth(date, weekStart);\n            }\n            var lastDate = new Date(date.getFullYear(), date.getMonth() + 1, 0);\n            var weeksInMonth = weekInMonth(lastDate, weekStart);\n            var day = normalizeDayIndex(rule.day, weekStart);\n            var skipFirst = day < normalizeDayIndex(new Date(date.getFullYear(), date.getMonth(), 1).getDay(), weekStart);\n            var skipLast = day > normalizeDayIndex(lastDate.getDay(), weekStart);\n            if (offset < 0) {\n                offset = weeksInMonth + (offset + 1 - (skipLast ? 1 : 0));\n            } else if (skipFirst) {\n                offset += 1;\n            }\n            weeksInMonth -= skipLast ? 1 : 0;\n            if (offset < (skipFirst ? 1 : 0) || offset > weeksInMonth) {\n                return null;\n            }\n            return offset;\n        }\n        function numberOfWeeks(date, weekStart) {\n            return weekInMonth(new Date(date.getFullYear(), date.getMonth() + 1, 0), weekStart);\n        }\n        function isInWeek(date, rule, weekStart) {\n            return weekInMonth(date, weekStart) === normalizeOffset(date, rule, weekStart);\n        }\n        function ruleWeekValues(weekDays, date, weekStart) {\n            var currentDay = normalizeDayIndex(date.getDay(), weekStart);\n            var length = weekDays.length;\n            var ruleWeekOffset;\n            var weekDay, day;\n            var weekNumber;\n            var result = [];\n            var idx = 0;\n            for (; idx < length; idx++) {\n                weekDay = weekDays[idx];\n                weekNumber = weekInMonth(date, weekStart);\n                ruleWeekOffset = normalizeOffset(date, weekDay, weekStart);\n                if (ruleWeekOffset === null) {\n                    continue;\n                }\n                if (weekNumber < ruleWeekOffset) {\n                    result.push(weekDay);\n                } else if (weekNumber === ruleWeekOffset) {\n                    day = normalizeDayIndex(weekDay.day, weekStart);\n                    if (currentDay < day) {\n                        result.push(weekDay);\n                    } else if (currentDay === day) {\n                        return null;\n                    }\n                }\n            }\n            return result;\n        }\n        function ruleValues(rules, value, normalize) {\n            var idx = 0, length = rules.length, availableRules = [], ruleValue;\n            for (; idx < length; idx++) {\n                ruleValue = rules[idx];\n                if (normalize) {\n                    ruleValue = normalize(ruleValue);\n                }\n                if (value === ruleValue) {\n                    return null;\n                } else if (value < ruleValue) {\n                    availableRules.push(ruleValue);\n                }\n            }\n            return availableRules;\n        }\n        function parseArray(list, range) {\n            var idx = 0, length = list.length, value;\n            for (; idx < length; idx++) {\n                value = parseInt(list[idx], 10);\n                if (isNaN(value) || value < range.start || value > range.end || value === 0 && range.start < 0) {\n                    return null;\n                }\n                list[idx] = value;\n            }\n            return list.sort(numberSortPredicate);\n        }\n        function parseWeekDayList(list) {\n            var idx = 0, length = list.length, value, valueLength, day;\n            for (; idx < length; idx++) {\n                value = list[idx];\n                valueLength = value.length;\n                day = value.substring(valueLength - 2).toUpperCase();\n                day = WEEK_DAYS_IDX[day];\n                if (day === undefined) {\n                    return null;\n                }\n                list[idx] = {\n                    offset: parseInt(value.substring(0, valueLength - 2), 10) || 0,\n                    day: day\n                };\n            }\n            return list;\n        }\n        function serializeWeekDayList(list) {\n            var idx = 0, length = list.length, value, valueString, result = [];\n            for (; idx < length; idx++) {\n                value = list[idx];\n                if (typeof value === 'string') {\n                    valueString = value;\n                } else {\n                    valueString = '' + WEEK_DAYS[value.day];\n                    if (value.offset) {\n                        valueString = value.offset + valueString;\n                    }\n                }\n                result.push(valueString);\n            }\n            return result.toString();\n        }\n        function getMonthLength(date) {\n            var month = date.getMonth();\n            if (month === 1) {\n                if (new Date(date.getFullYear(), 1, 29).getMonth() === 1) {\n                    return 29;\n                }\n                return 28;\n            }\n            return MONTHS[month];\n        }\n        function leapYear(year) {\n            year = year.getFullYear();\n            return year % 4 === 0 && year % 100 !== 0 || year % 400 === 0;\n        }\n        function numberSortPredicate(a, b) {\n            return a - b;\n        }\n        function parseExceptions(exceptions, zone) {\n            var idx = 0, length, date, dates = [];\n            if (exceptions) {\n                exceptions = exceptions.split(exceptions.indexOf(';') !== -1 ? ';' : ',');\n                length = exceptions.length;\n                for (; idx < length; idx++) {\n                    date = parseUTCDate(exceptions[idx], zone);\n                    if (date) {\n                        dates.push(date);\n                    }\n                }\n            }\n            return dates;\n        }\n        function isException(exceptions, date, zone) {\n            var dates = $.isArray(exceptions) ? exceptions : parseExceptions(exceptions, zone), dateTime = date.getTime() - date.getMilliseconds(), idx = 0, length = dates.length;\n            for (; idx < length; idx++) {\n                if (dates[idx].getTime() === dateTime) {\n                    return true;\n                }\n            }\n            return false;\n        }\n        function toExceptionString(dates, zone) {\n            var idx = 0;\n            var length;\n            var date;\n            var result = [].concat(dates);\n            for (length = result.length; idx < length; idx++) {\n                date = result[idx];\n                date = kendo.timezone.convert(date, zone || date.getTimezoneOffset(), 'Etc/UTC');\n                result[idx] = kendo.toString(date, RECURRENCE_DATE_FORMAT);\n            }\n            return result.join(',');\n        }\n        function startPeriodByFreq(start, rule) {\n            var date = new Date(start);\n            switch (rule.freq) {\n            case 'yearly':\n                date.setFullYear(date.getFullYear(), 0, 1);\n                break;\n            case 'monthly':\n                date.setFullYear(date.getFullYear(), date.getMonth(), 1);\n                break;\n            case 'weekly':\n                setDayOfWeek(date, rule.weekStart, -1);\n                break;\n            default:\n                break;\n            }\n            if (rule.hours) {\n                date.setHours(0);\n            }\n            if (rule.minutes) {\n                date.setMinutes(0);\n            }\n            if (rule.seconds) {\n                date.setSeconds(0);\n            }\n            return date;\n        }\n        function endPeriodByFreq(start, rule) {\n            var date = new Date(start);\n            switch (rule.freq) {\n            case 'yearly':\n                date.setFullYear(date.getFullYear(), 11, 31);\n                break;\n            case 'monthly':\n                date.setFullYear(date.getFullYear(), date.getMonth() + 1, 0);\n                break;\n            case 'weekly':\n                setDayOfWeek(date, rule.weekStart, -1);\n                date.setDate(date.getDate() + 6);\n                break;\n            default:\n                break;\n            }\n            if (rule.hours) {\n                date.setHours(23);\n            }\n            if (rule.minutes) {\n                date.setMinutes(59);\n            }\n            if (rule.seconds) {\n                date.setSeconds(59);\n            }\n            return date;\n        }\n        function eventsByPosition(periodEvents, start, positions) {\n            var periodEventsLength = periodEvents.length;\n            var events = [];\n            var position;\n            var event;\n            for (var idx = 0, length = positions.length; idx < length; idx++) {\n                position = positions[idx];\n                if (position < 0) {\n                    position = periodEventsLength + position;\n                } else {\n                    position -= 1;\n                }\n                event = periodEvents[position];\n                if (event && event.start >= start) {\n                    events.push(event);\n                }\n            }\n            return events;\n        }\n        function removeExceptionDates(periodEvents, exceptionDates, zone) {\n            var events = [];\n            var event;\n            for (var idx = 0; idx < periodEvents.length; idx++) {\n                event = periodEvents[idx];\n                if (!isException(exceptionDates, event.start, zone)) {\n                    events.push(event);\n                }\n            }\n            return events;\n        }\n        function expand(event, start, end, zone) {\n            var rule = parseRule(event.recurrenceRule, zone), startTime, endTime, endDate, hours, minutes, seconds, durationMS, startPeriod, inPeriod, ruleStart, ruleEnd, useEventStart, freqName, exceptionDates, eventStartTime, eventStartMS, eventStart, count, freq, positions, currentIdx, periodEvents, events = [], shiftedStart, shiftedEnd, shiftedStartTime, shifterEndTime;\n            if (!rule) {\n                return [event];\n            }\n            positions = rule.positions;\n            currentIdx = positions ? 0 : 1;\n            ruleStart = rule.start;\n            ruleEnd = rule.end;\n            if (ruleStart || ruleEnd) {\n                event = event.clone({\n                    start: ruleStart ? new Date(ruleStart.value[0]) : undefined,\n                    end: ruleEnd ? new Date(ruleEnd.value[0]) : undefined\n                });\n            }\n            eventStart = event.start;\n            eventStartMS = eventStart.getTime();\n            eventStartTime = getMilliseconds(eventStart);\n            exceptionDates = parseExceptions(event.recurrenceException, zone);\n            if (!exceptionDates[0] && rule.exdates) {\n                exceptionDates = rule.exdates.value;\n                event.set('recurrenceException', toExceptionString(exceptionDates, zone));\n            }\n            startPeriod = start = new Date(start);\n            end = new Date(end);\n            freqName = rule.freq;\n            freq = frequencies[freqName];\n            count = rule.count;\n            if (rule.until && rule.until < end) {\n                end = new Date(rule.until);\n            }\n            useEventStart = freqName === 'yearly' || freqName === 'monthly' || freqName === 'weekly';\n            if (start < eventStartMS || count || rule.interval > 1 || useEventStart || (freqName === 'daily' || freqName === 'hourly') && !rule.seconds) {\n                start = new Date(eventStartMS);\n            } else {\n                hours = start.getHours();\n                minutes = start.getMinutes();\n                seconds = start.getSeconds();\n                if (!rule.hours) {\n                    hours = eventStart.getHours();\n                }\n                if (!rule.minutes) {\n                    minutes = eventStart.getMinutes();\n                }\n                if (!rule.seconds) {\n                    seconds = eventStart.getSeconds();\n                }\n                start.setHours(hours, minutes, seconds, eventStart.getMilliseconds());\n            }\n            rule._startPeriod = new Date(start);\n            if (positions) {\n                start = startPeriodByFreq(start, rule);\n                end = endPeriodByFreq(end, rule);\n                var diff = getMilliseconds(end) - getMilliseconds(start);\n                if (diff < 0) {\n                    hours = start.getHours();\n                    end.setHours(hours, start.getMinutes(), start.getSeconds(), start.getMilliseconds());\n                    kendoDate.adjustDST(end, hours);\n                }\n                rule._startPeriod = new Date(start);\n                rule._endPeriod = endPeriodByFreq(start, rule);\n            }\n            durationMS = event.duration();\n            rule._startTime = startTime = kendoDate.toInvariantTime(start);\n            if (freq.setup) {\n                freq.setup(rule, eventStart, start);\n            }\n            freq.limit(start, end, rule);\n            while (start <= end) {\n                endDate = new Date(start);\n                setTime(endDate, durationMS);\n                inPeriod = start >= startPeriod || endDate > startPeriod;\n                if (inPeriod && !isException(exceptionDates, start, zone) || positions) {\n                    startTime = kendoDate.toUtcTime(kendoDate.getDate(start)) + getMilliseconds(rule._startTime);\n                    endTime = startTime + durationMS;\n                    if (eventStartMS !== start.getTime() || eventStartTime !== getMilliseconds(rule._startTime)) {\n                        if (!event.isAllDay) {\n                            var startZone = event.startTimezone || event.endTimezone;\n                            var endZone = event.endTimezone || event.startTimezone;\n                            if (zone && startZone || !zone && !startZone) {\n                                var startOffsetDiff = getZoneOffset(start, zone) - getZoneOffset(event.start, zone);\n                                var endOffsetDiff = getZoneOffset(endDate, zone) - getZoneOffset(event.end, zone);\n                                var startTZOffsetDiff = getZoneOffset(start, startZone) - getZoneOffset(event.start, startZone);\n                                var endTZOffsetDiff = getZoneOffset(endDate, endZone) - getZoneOffset(event.end, endZone);\n                                if (startOffsetDiff !== startTZOffsetDiff) {\n                                    var offsetTicksStart = (startOffsetDiff - startTZOffsetDiff) * 60000;\n                                    shiftedStart = new Date(start.getTime() - offsetTicksStart);\n                                    shiftedStartTime = startTime - offsetTicksStart;\n                                }\n                                if (endOffsetDiff !== endTZOffsetDiff) {\n                                    var offsetTicksEnd = (endOffsetDiff - endTZOffsetDiff) * 60000;\n                                    shiftedEnd = new Date(endDate.getTime() - offsetTicksEnd);\n                                    shifterEndTime = endTime - offsetTicksEnd;\n                                }\n                            }\n                        }\n                        events.push(event.toOccurrence({\n                            start: shiftedStart || new Date(start),\n                            end: shiftedEnd || endDate,\n                            _startTime: shiftedStartTime || startTime,\n                            _endTime: shifterEndTime || endTime\n                        }));\n                        shiftedStart = shiftedEnd = shiftedStartTime = shifterEndTime = null;\n                    } else {\n                        event._startTime = startTime;\n                        event._endTime = endTime;\n                        events.push(event);\n                    }\n                }\n                if (positions) {\n                    freq.next(start, rule);\n                    freq.limit(start, end, rule);\n                    if (start > rule._endPeriod) {\n                        periodEvents = eventsByPosition(events.slice(currentIdx), eventStart, positions);\n                        periodEvents = removeExceptionDates(periodEvents, exceptionDates, zone);\n                        events = events.slice(0, currentIdx).concat(periodEvents);\n                        rule._endPeriod = endPeriodByFreq(start, rule);\n                        currentIdx = events.length;\n                    }\n                    if (count && count === currentIdx) {\n                        break;\n                    }\n                } else {\n                    if (count && count === currentIdx) {\n                        break;\n                    }\n                    currentIdx += 1;\n                    var isMissingDSTHour = isDSTMissingHour(start);\n                    freq.next(start, rule);\n                    if (isMissingDSTHour && rule.freq !== 'hourly' && kendoDate.toInvariantTime(event.start).getTime() !== kendoDate.toInvariantTime(start).getTime()) {\n                        rule._startTime = startTime = new Date(start.getTime() - 3600000);\n                    }\n                    freq.limit(start, end, rule);\n                }\n            }\n            return events;\n        }\n        function isDSTMissingHour(date) {\n            var dateOffset = date.getTimezoneOffset();\n            var dateMinusHour = new Date(date.getTime() - 3600000);\n            var dateMinusHourOffset = dateMinusHour.getTimezoneOffset();\n            return dateOffset < dateMinusHourOffset;\n        }\n        function getZoneOffset(date, zone) {\n            return zone ? kendo.timezone.offset(date, zone) : date.getTimezoneOffset();\n        }\n        function parseUTCDate(value, zone) {\n            value = kendo.parseDate(value, DATE_FORMATS);\n            if (value && zone) {\n                value = timezone.apply(value, zone);\n            }\n            return value;\n        }\n        function parseDateRule(dateRule, zone) {\n            var pairs = dateRule.split(';');\n            var pair;\n            var property;\n            var value;\n            var tzid;\n            var valueIdx, valueLength;\n            for (var idx = 0, length = pairs.length; idx < length; idx++) {\n                pair = pairs[idx].split(':');\n                property = pair[0];\n                value = pair[1];\n                if (property.indexOf('TZID') !== -1) {\n                    tzid = property.substring(property.indexOf('TZID')).split('=')[1];\n                }\n                if (value) {\n                    value = value.split(',');\n                    for (valueIdx = 0, valueLength = value.length; valueIdx < valueLength; valueIdx++) {\n                        value[valueIdx] = parseUTCDate(value[valueIdx], tzid || zone);\n                    }\n                }\n            }\n            if (value) {\n                return {\n                    value: value,\n                    tzid: tzid\n                };\n            }\n        }\n        function parseRule(recur, zone) {\n            var instance = {};\n            var splits, value;\n            var idx = 0, length;\n            var ruleValue = false;\n            var rule, part, parts;\n            var property, weekStart, weekDays;\n            var predicate = function (a, b) {\n                var day1 = a.day, day2 = b.day;\n                if (day1 < weekStart) {\n                    day1 += 7;\n                }\n                if (day2 < weekStart) {\n                    day2 += 7;\n                }\n                return day1 - day2;\n            };\n            if (!recur) {\n                return null;\n            }\n            parts = recur.split('\\n');\n            if (!parts[1] && (recur.indexOf('DTSTART') !== -1 || recur.indexOf('DTEND') !== -1 || recur.indexOf('EXDATE') !== -1)) {\n                parts = recur.split(' ');\n            }\n            for (idx = 0, length = parts.length; idx < length; idx++) {\n                part = $.trim(parts[idx]);\n                if (part.indexOf('DTSTART') !== -1) {\n                    instance.start = parseDateRule(part, zone);\n                } else if (part.indexOf('DTEND') !== -1) {\n                    instance.end = parseDateRule(part, zone);\n                } else if (part.indexOf('EXDATE') !== -1) {\n                    instance.exdates = parseDateRule(part, zone);\n                } else if (part.indexOf('RRULE') !== -1) {\n                    rule = part.substring(6);\n                } else if ($.trim(part)) {\n                    rule = part;\n                }\n            }\n            rule = rule.split(';');\n            for (idx = 0, length = rule.length; idx < length; idx++) {\n                property = rule[idx];\n                splits = property.split('=');\n                value = $.trim(splits[1]).split(',');\n                switch ($.trim(splits[0]).toUpperCase()) {\n                case 'FREQ':\n                    instance.freq = value[0].toLowerCase();\n                    break;\n                case 'UNTIL':\n                    instance.until = parseUTCDate(value[0], zone);\n                    break;\n                case 'COUNT':\n                    instance.count = parseInt(value[0], 10);\n                    break;\n                case 'INTERVAL':\n                    instance.interval = parseInt(value[0], 10);\n                    break;\n                case 'BYSECOND':\n                    instance.seconds = parseArray(value, {\n                        start: 0,\n                        end: 60\n                    });\n                    ruleValue = true;\n                    break;\n                case 'BYMINUTE':\n                    instance.minutes = parseArray(value, {\n                        start: 0,\n                        end: 59\n                    });\n                    ruleValue = true;\n                    break;\n                case 'BYHOUR':\n                    instance.hours = parseArray(value, {\n                        start: 0,\n                        end: 23\n                    });\n                    ruleValue = true;\n                    break;\n                case 'BYMONTHDAY':\n                    instance.monthDays = parseArray(value, {\n                        start: -31,\n                        end: 31\n                    });\n                    ruleValue = true;\n                    break;\n                case 'BYYEARDAY':\n                    instance.yearDays = parseArray(value, {\n                        start: -366,\n                        end: 366\n                    });\n                    ruleValue = true;\n                    break;\n                case 'BYMONTH':\n                    instance.months = parseArray(value, {\n                        start: 1,\n                        end: 12\n                    });\n                    ruleValue = true;\n                    break;\n                case 'BYDAY':\n                    instance.weekDays = weekDays = parseWeekDayList(value);\n                    ruleValue = true;\n                    break;\n                case 'BYWEEKNO':\n                    instance.weeks = parseArray(value, {\n                        start: -53,\n                        end: 53\n                    });\n                    ruleValue = true;\n                    break;\n                case 'BYSETPOS':\n                    instance.positions = parseArray(value, {\n                        start: -366,\n                        end: 366\n                    });\n                    break;\n                case 'WKST':\n                    instance.weekStart = weekStart = WEEK_DAYS_IDX[value[0]];\n                    break;\n                }\n            }\n            if (instance.freq === undefined || instance.count !== undefined && instance.until) {\n                return null;\n            }\n            if (!instance.interval) {\n                instance.interval = 1;\n            }\n            if (weekStart === undefined) {\n                instance.weekStart = weekStart = kendo.culture().calendar.firstDay;\n            }\n            if (weekDays) {\n                instance.weekDays = weekDays.sort(predicate);\n            }\n            if (instance.positions && !ruleValue) {\n                instance.positions = null;\n            }\n            instance._hasRuleValue = ruleValue;\n            return instance;\n        }\n        function serializeDateRule(dateRule, zone) {\n            var value = dateRule.value;\n            var tzid = dateRule.tzid || '';\n            var length = value.length;\n            var idx = 0;\n            var val;\n            for (; idx < length; idx++) {\n                val = value[idx];\n                val = timezone.convert(val, tzid || zone || val.getTimezoneOffset(), 'Etc/UTC');\n                value[idx] = kendo.toString(val, 'yyyyMMddTHHmmssZ');\n            }\n            if (tzid) {\n                tzid = ';TZID=' + tzid;\n            }\n            return tzid + ':' + value.join(',') + ' ';\n        }\n        function serialize(rule, zone) {\n            var weekStart = rule.weekStart;\n            var ruleString = 'FREQ=' + rule.freq.toUpperCase();\n            var exdates = rule.exdates || '';\n            var start = rule.start || '';\n            var end = rule.end || '';\n            var until = rule.until;\n            if (rule.interval > 1) {\n                ruleString += ';INTERVAL=' + rule.interval;\n            }\n            if (rule.count) {\n                ruleString += ';COUNT=' + rule.count;\n            }\n            if (until) {\n                until = timezone.convert(until, zone || until.getTimezoneOffset(), 'Etc/UTC');\n                ruleString += ';UNTIL=' + kendo.toString(until, 'yyyyMMddTHHmmssZ');\n            }\n            if (rule.months) {\n                ruleString += ';BYMONTH=' + rule.months;\n            }\n            if (rule.weeks) {\n                ruleString += ';BYWEEKNO=' + rule.weeks;\n            }\n            if (rule.yearDays) {\n                ruleString += ';BYYEARDAY=' + rule.yearDays;\n            }\n            if (rule.monthDays) {\n                ruleString += ';BYMONTHDAY=' + rule.monthDays;\n            }\n            if (rule.weekDays) {\n                ruleString += ';BYDAY=' + serializeWeekDayList(rule.weekDays);\n            }\n            if (rule.hours) {\n                ruleString += ';BYHOUR=' + rule.hours;\n            }\n            if (rule.minutes) {\n                ruleString += ';BYMINUTE=' + rule.minutes;\n            }\n            if (rule.seconds) {\n                ruleString += ';BYSECOND=' + rule.seconds;\n            }\n            if (rule.positions) {\n                ruleString += ';BYSETPOS=' + rule.positions;\n            }\n            if (weekStart !== undefined) {\n                ruleString += ';WKST=' + WEEK_DAYS[weekStart];\n            }\n            if (start) {\n                start = 'DTSTART' + serializeDateRule(start, zone);\n            }\n            if (end) {\n                end = 'DTEND' + serializeDateRule(end, zone);\n            }\n            if (exdates) {\n                exdates = 'EXDATE' + serializeDateRule(exdates, zone);\n            }\n            if (start || end || exdates) {\n                ruleString = start + end + exdates + 'RRULE:' + ruleString;\n            }\n            return ruleString;\n        }\n        kendo.recurrence = {\n            rule: {\n                parse: parseRule,\n                serialize: serialize\n            },\n            expand: expand,\n            dayInYear: dayInYear,\n            weekInYear: weekInYear,\n            weekInMonth: weekInMonth,\n            numberOfWeeks: numberOfWeeks,\n            isException: isException,\n            toExceptionString: toExceptionString\n        };\n        var weekDayCheckBoxes = function (firstDay) {\n            var shortNames = kendo.culture().calendar.days.namesShort, length = shortNames.length, result = '', idx = 0, values = [];\n            for (; idx < length; idx++) {\n                values.push(idx);\n            }\n            shortNames = shortNames.slice(firstDay).concat(shortNames.slice(0, firstDay));\n            values = values.slice(firstDay).concat(values.slice(0, firstDay));\n            for (idx = 0; idx < length; idx++) {\n                result += '<label class=\"k-check\"><input class=\"k-recur-weekday-checkbox\" type=\"checkbox\" value=\"' + values[idx] + '\" /> ' + shortNames[idx] + '</label>';\n            }\n            return result;\n        };\n        var mobileWeekDayCheckBoxes = function (firstDay) {\n            var shortNames = kendo.culture().calendar.days.names, length = shortNames.length, result = '', idx = 0, values = [];\n            for (; idx < length; idx++) {\n                values.push(idx);\n            }\n            shortNames = shortNames.slice(firstDay).concat(shortNames.slice(0, firstDay));\n            values = values.slice(firstDay).concat(values.slice(0, firstDay));\n            for (idx = 0; idx < length; idx++) {\n                result += '<li class=\"k-item\"><label class=\"k-label\">';\n                result += '<span class=\"k-item-title\">' + shortNames[idx] + '</span>';\n                result += '<input class=\"k-recur-weekday-checkbox k-check\" type=\"checkbox\" value=\"' + values[idx] + '\" />';\n                result += '</label></li>';\n            }\n            return result;\n        };\n        var RECURRENCE_VIEW_TEMPLATE = kendo.template('# if (frequency !== \"never\") { #' + '<div class=\"k-edit-label\"><label>#:messages.repeatEvery#</label></div>' + '<div class=\"k-edit-field\"><input class=\"k-recur-interval\" title=\"#:messages.interval#\"/>#:messages.interval#</div>' + '# } #' + '# if (frequency === \"weekly\") { #' + '<div class=\"k-edit-label\"><label>#:messages.repeatOn#</label></div>' + '<div class=\"k-edit-field\">#=weekDayCheckBoxes(firstWeekDay)#</div>' + '# } else if (frequency === \"monthly\") { #' + '<div class=\"k-edit-label\"><label>#:messages.repeatOn#</label></div>' + '<div class=\"k-edit-field\">' + '<ul class=\"k-reset\">' + '<li>' + '<label><input class=\"k-recur-month-radio\" type=\"radio\" name=\"month\" value=\"monthday\" title=\"#:messages.day#\" />#:messages.day#</label>' + '<input class=\"k-recur-monthday\" title=\"#:messages.day#\" />' + '</li>' + '<li>' + '<input class=\"k-recur-month-radio\" type=\"radio\" name=\"month\" value=\"weekday\" title=\"#:messages.repeatOn#\" />' + '<input class=\"k-recur-weekday-offset\" title=\"#:messages.repeatOn#\" /><input class=\"k-recur-weekday\" title=\"#:messages.day#\" />' + '</li>' + '</ul>' + '</div>' + '# } else if (frequency === \"yearly\") { #' + '<div class=\"k-edit-label\"><label>#:messages.repeatOn#</label></div>' + '<div class=\"k-edit-field\">' + '<ul class=\"k-reset\">' + '<li>' + '<input class=\"k-recur-year-radio\" type=\"radio\" name=\"year\" value=\"monthday\" title=\"#:messages.repeatOn#\" />' + '<input class=\"k-recur-month\" title=\"#:messages.repeatOn#\" /><input class=\"k-recur-monthday\" title=\"#:messages.day#\" />' + '</li>' + '<li>' + '<input class=\"k-recur-year-radio\" type=\"radio\" name=\"year\" value=\"weekday\" title=\"#:messages.repeatOn#\" />' + '<input class=\"k-recur-weekday-offset\" title=\"#:messages.repeatOn#\" /><input class=\"k-recur-weekday\" title=\"#:messages.day#\"  />#:messages.of#<input class=\"k-recur-month\" title=\"#:messages.of#\"/>' + '</li>' + '</ul>' + '</div>' + '# } #' + '# if (frequency !== \"never\") { #' + '<div class=\"k-edit-label\"><label>#:end.label#</label></div>' + '<div class=\"k-edit-field\">' + '<ul class=\"k-reset\">' + '<li>' + '<label><input class=\"k-recur-end-never\" type=\"radio\" name=\"end\" value=\"never\" />#:end.never#</label>' + '</li>' + '<li>' + '<label><input class=\"k-recur-end-count\" type=\"radio\" name=\"end\" value=\"count\" />#:end.after#</label>' + '<input class=\"k-recur-count\" title=\"#:end.occurrence#\" />#:end.occurrence#' + '</li>' + '<li>' + '<label><input class=\"k-recur-end-until\" type=\"radio\" name=\"end\" value=\"until\" />#:end.on#</label>' + '<input class=\"k-recur-until\" title=\"#:end.on#\" />' + '</li>' + '</ul>' + '</div>' + '# } #');\n        var DAY_RULE = [\n            {\n                day: 0,\n                offset: 0\n            },\n            {\n                day: 1,\n                offset: 0\n            },\n            {\n                day: 2,\n                offset: 0\n            },\n            {\n                day: 3,\n                offset: 0\n            },\n            {\n                day: 4,\n                offset: 0\n            },\n            {\n                day: 5,\n                offset: 0\n            },\n            {\n                day: 6,\n                offset: 0\n            }\n        ];\n        var WEEKDAY_RULE = [\n            {\n                day: 1,\n                offset: 0\n            },\n            {\n                day: 2,\n                offset: 0\n            },\n            {\n                day: 3,\n                offset: 0\n            },\n            {\n                day: 4,\n                offset: 0\n            },\n            {\n                day: 5,\n                offset: 0\n            }\n        ];\n        var WEEKEND_RULE = [\n            {\n                day: 0,\n                offset: 0\n            },\n            {\n                day: 6,\n                offset: 0\n            }\n        ];\n        var BaseRecurrenceEditor = Widget.extend({\n            init: function (element, options) {\n                var start;\n                var that = this;\n                var frequencies = options && options.frequencies;\n                Widget.fn.init.call(that, element, options);\n                that.wrapper = that.element;\n                options = that.options;\n                options.start = start = options.start || kendoDate.today();\n                if (frequencies) {\n                    options.frequencies = frequencies;\n                }\n                if (typeof start === 'string') {\n                    options.start = kendo.parseDate(start, 'yyyyMMddTHHmmss');\n                }\n                if (options.firstWeekDay === null) {\n                    options.firstWeekDay = kendo.culture().calendar.firstDay;\n                }\n                that._namespace = '.' + options.name;\n            },\n            options: {\n                value: '',\n                start: '',\n                timezone: '',\n                spinners: true,\n                firstWeekDay: null,\n                frequencies: [\n                    'never',\n                    'daily',\n                    'weekly',\n                    'monthly',\n                    'yearly'\n                ],\n                mobile: false,\n                messages: {\n                    recurrenceEditorTitle: 'Recurrence editor',\n                    frequencies: {\n                        never: 'Never',\n                        hourly: 'Hourly',\n                        daily: 'Daily',\n                        weekly: 'Weekly',\n                        monthly: 'Monthly',\n                        yearly: 'Yearly'\n                    },\n                    hourly: {\n                        repeatEvery: 'Repeat every: ',\n                        interval: ' hour(s)'\n                    },\n                    daily: {\n                        repeatEvery: 'Repeat every: ',\n                        interval: ' day(s)'\n                    },\n                    weekly: {\n                        interval: ' week(s)',\n                        repeatEvery: 'Repeat every: ',\n                        repeatOn: 'Repeat on: '\n                    },\n                    monthly: {\n                        repeatEvery: 'Repeat every: ',\n                        repeatOn: 'Repeat on: ',\n                        interval: ' month(s)',\n                        day: 'Day '\n                    },\n                    yearly: {\n                        repeatEvery: 'Repeat every: ',\n                        repeatOn: 'Repeat on: ',\n                        interval: ' year(s)',\n                        of: ' of '\n                    },\n                    end: {\n                        label: 'End:',\n                        mobileLabel: 'Ends',\n                        never: 'Never',\n                        after: 'After ',\n                        occurrence: ' occurrence(s)',\n                        on: 'On '\n                    },\n                    offsetPositions: {\n                        first: 'first',\n                        second: 'second',\n                        third: 'third',\n                        fourth: 'fourth',\n                        last: 'last'\n                    },\n                    weekdays: {\n                        day: 'day',\n                        weekday: 'weekday',\n                        weekend: 'weekend day'\n                    }\n                }\n            },\n            events: ['change'],\n            _initInterval: function () {\n                var that = this;\n                var rule = that._value;\n                that._container.find('.k-recur-interval').kendoNumericTextBox({\n                    spinners: that.options.spinners,\n                    value: rule.interval || 1,\n                    decimals: 0,\n                    format: '#',\n                    min: 1,\n                    change: function () {\n                        rule.interval = this.value();\n                        that._trigger();\n                    }\n                });\n            },\n            _weekDayRule: function (clear) {\n                var that = this;\n                var weekday = (that._weekDay.element || that._weekDay).val();\n                var offset = Number((that._weekDayOffset.element || that._weekDayOffset).val());\n                var weekDays = null;\n                var positions = null;\n                if (!clear) {\n                    if (weekday === 'day') {\n                        weekDays = DAY_RULE;\n                        positions = offset;\n                    } else if (weekday === 'weekday') {\n                        weekDays = WEEKDAY_RULE;\n                        positions = offset;\n                    } else if (weekday === 'weekend') {\n                        weekDays = WEEKEND_RULE;\n                        positions = offset;\n                    } else {\n                        weekDays = [{\n                                offset: offset,\n                                day: Number(weekday)\n                            }];\n                    }\n                }\n                that._value.weekDays = weekDays;\n                that._value.positions = positions;\n            },\n            _weekDayView: function () {\n                var that = this;\n                var weekDays = that._value.weekDays;\n                var positions = that._value.positions;\n                var weekDayOffsetWidget = that._weekDayOffset;\n                var weekDayOffset;\n                var weekDayValue;\n                var length;\n                var method;\n                if (weekDays) {\n                    length = weekDays.length;\n                    if (positions) {\n                        if (length === 7) {\n                            weekDayValue = 'day';\n                            weekDayOffset = positions;\n                        } else if (length === 5) {\n                            weekDayValue = 'weekday';\n                            weekDayOffset = positions;\n                        } else if (length === 2) {\n                            weekDayValue = 'weekend';\n                            weekDayOffset = positions;\n                        }\n                    }\n                    if (!weekDayValue) {\n                        weekDays = weekDays[0];\n                        weekDayValue = weekDays.day;\n                        weekDayOffset = weekDays.offset || '';\n                    }\n                    method = weekDayOffsetWidget.value ? 'value' : 'val';\n                    weekDayOffsetWidget[method](weekDayOffset);\n                    that._weekDay[method](weekDayValue);\n                }\n            },\n            _initWeekDay: function () {\n                var that = this, data;\n                var weekdayMessage = that.options.messages.weekdays;\n                var offsetMessage = that.options.messages.offsetPositions;\n                var weekDayInput = that._container.find('.k-recur-weekday');\n                var change = function () {\n                    that._weekDayRule();\n                    that._trigger();\n                };\n                if (weekDayInput[0]) {\n                    that._weekDayOffset = new DropDownList(that._container.find('.k-recur-weekday-offset'), {\n                        change: change,\n                        dataTextField: 'text',\n                        dataValueField: 'value',\n                        dataSource: [\n                            {\n                                text: offsetMessage.first,\n                                value: '1'\n                            },\n                            {\n                                text: offsetMessage.second,\n                                value: '2'\n                            },\n                            {\n                                text: offsetMessage.third,\n                                value: '3'\n                            },\n                            {\n                                text: offsetMessage.fourth,\n                                value: '4'\n                            },\n                            {\n                                text: offsetMessage.last,\n                                value: '-1'\n                            }\n                        ]\n                    });\n                    data = [\n                        {\n                            text: weekdayMessage.day,\n                            value: 'day'\n                        },\n                        {\n                            text: weekdayMessage.weekday,\n                            value: 'weekday'\n                        },\n                        {\n                            text: weekdayMessage.weekend,\n                            value: 'weekend'\n                        }\n                    ];\n                    that._weekDay = new DropDownList(weekDayInput, {\n                        value: that.options.start.getDay(),\n                        change: change,\n                        dataTextField: 'text',\n                        dataValueField: 'value',\n                        dataSource: data.concat($.map(kendo.culture().calendar.days.names, function (dayName, idx) {\n                            return {\n                                text: dayName,\n                                value: idx\n                            };\n                        }))\n                    });\n                    that._weekDayView();\n                }\n            },\n            _initWeekDays: function () {\n                var that = this;\n                var rule = that._value;\n                var weekDays = that._container.find('.k-recur-weekday-checkbox');\n                if (weekDays[0]) {\n                    weekDays.on(CLICK + that._namespace, function () {\n                        rule.weekDays = $.map(weekDays.filter(':checked'), function (checkbox) {\n                            return {\n                                day: Number(checkbox.value),\n                                offset: 0\n                            };\n                        });\n                        if (!that.options.mobile) {\n                            that._trigger();\n                        }\n                    });\n                    if (rule.weekDays) {\n                        var idx, weekDay;\n                        var i = 0, l = weekDays.length;\n                        var length = rule.weekDays.length;\n                        for (; i < l; i++) {\n                            weekDay = weekDays[i];\n                            for (idx = 0; idx < length; idx++) {\n                                if (weekDay.value == rule.weekDays[idx].day) {\n                                    weekDay.checked = true;\n                                }\n                            }\n                        }\n                    }\n                }\n            },\n            _initMonthDay: function () {\n                var that = this;\n                var rule = that._value;\n                var monthDayInput = that._container.find('.k-recur-monthday');\n                if (monthDayInput[0]) {\n                    that._monthDay = new kendo.ui.NumericTextBox(monthDayInput, {\n                        spinners: that.options.spinners,\n                        min: 1,\n                        max: 31,\n                        decimals: 0,\n                        format: '#',\n                        value: rule.monthDays ? rule.monthDays[0] : that.options.start.getDate(),\n                        change: function () {\n                            var value = this.value();\n                            rule.monthDays = value ? [value] : value;\n                            that._trigger();\n                        }\n                    });\n                }\n            },\n            _initCount: function () {\n                var that = this, input = that._container.find('.k-recur-count'), rule = that._value;\n                that._count = input.kendoNumericTextBox({\n                    spinners: that.options.spinners,\n                    value: rule.count || 1,\n                    decimals: 0,\n                    format: '#',\n                    min: 1,\n                    change: function () {\n                        rule.count = this.value();\n                        that._trigger();\n                    }\n                }).data('kendoNumericTextBox');\n            },\n            _initUntil: function () {\n                var that = this, input = that._container.find('.k-recur-until'), start = that.options.start, rule = that._value, until = rule.until;\n                that._until = input.kendoDatePicker({\n                    min: until && until < start ? until : start,\n                    value: until || new Date(start.getFullYear(), start.getMonth(), start.getDate(), 23, 59, 59),\n                    change: function () {\n                        var date = this.value();\n                        rule.until = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);\n                        that._trigger();\n                    }\n                }).data('kendoDatePicker');\n            },\n            _trigger: function () {\n                if (!this.options.mobile) {\n                    this.trigger('change');\n                }\n            }\n        });\n        var RecurrenceEditor = BaseRecurrenceEditor.extend({\n            init: function (element, options) {\n                var that = this;\n                BaseRecurrenceEditor.fn.init.call(that, element, options);\n                that._initFrequency();\n                that._initContainer();\n                that.value(that.options.value);\n            },\n            options: { name: 'RecurrenceEditor' },\n            events: ['change'],\n            destroy: function () {\n                var that = this;\n                that._frequency.destroy();\n                that._container.find('input[type=radio],input[type=checkbox]').off(CLICK + that._namespace);\n                kendo.destroy(that._container);\n                BaseRecurrenceEditor.fn.destroy.call(that);\n            },\n            value: function (value) {\n                var that = this;\n                var timezone = that.options.timezone;\n                var freq;\n                if (value === undefined) {\n                    if (!that._value.freq) {\n                        return '';\n                    }\n                    return serialize(that._value, timezone);\n                }\n                that._value = parseRule(value, timezone) || {};\n                freq = that._value.freq;\n                if (freq) {\n                    that._frequency.value(freq);\n                } else {\n                    that._frequency.select(0);\n                }\n                that._initView(that._frequency.value());\n            },\n            _initContainer: function () {\n                var element = this.element, container = $('<div class=\"k-recur-view\" />'), editContainer = element.parent('.k-edit-field');\n                if (editContainer[0]) {\n                    container.insertAfter(editContainer);\n                } else {\n                    element.append(container);\n                }\n                this._container = container;\n            },\n            _initFrequency: function () {\n                var that = this, options = that.options, frequencies = options.frequencies, messages = options.messages.frequencies, ddl = $('<input />').attr({ title: options.messages.recurrenceEditorTitle }), frequency;\n                frequencies = $.map(frequencies, function (frequency) {\n                    return {\n                        text: messages[frequency],\n                        value: frequency\n                    };\n                });\n                frequency = frequencies[0];\n                if (frequency && frequency.value === 'never') {\n                    frequency.value = '';\n                }\n                that.element.append(ddl);\n                that._frequency = new DropDownList(ddl, {\n                    dataTextField: 'text',\n                    dataValueField: 'value',\n                    dataSource: frequencies,\n                    change: function () {\n                        that._value = {};\n                        that._initView(that._frequency.value());\n                        that.trigger('change');\n                    }\n                });\n            },\n            _initView: function (frequency) {\n                var that = this;\n                var rule = that._value;\n                var options = that.options;\n                var data = {\n                    frequency: frequency || 'never',\n                    weekDayCheckBoxes: weekDayCheckBoxes,\n                    firstWeekDay: options.firstWeekDay,\n                    messages: options.messages[frequency],\n                    end: options.messages.end\n                };\n                kendo.destroy(that._container);\n                that._container.html(RECURRENCE_VIEW_TEMPLATE(data));\n                if (!frequency) {\n                    that._value = {};\n                    return;\n                }\n                rule.freq = frequency;\n                if (frequency === 'weekly' && !rule.weekDays) {\n                    rule.weekDays = [{\n                            day: options.start.getDay(),\n                            offset: 0\n                        }];\n                }\n                that._initInterval();\n                that._initWeekDays();\n                that._initMonthDay();\n                that._initWeekDay();\n                that._initMonth();\n                that._initCount();\n                that._initUntil();\n                that._period();\n                that._end();\n            },\n            _initMonth: function () {\n                var that = this;\n                var rule = that._value;\n                var month = rule.months || [that.options.start.getMonth() + 1];\n                var monthInputs = that._container.find('.k-recur-month');\n                var options;\n                if (monthInputs[0]) {\n                    options = {\n                        change: function () {\n                            rule.months = [Number(this.value())];\n                            that.trigger('change');\n                        },\n                        dataTextField: 'text',\n                        dataValueField: 'value',\n                        dataSource: $.map(kendo.culture().calendar.months.names, function (monthName, idx) {\n                            return {\n                                text: monthName,\n                                value: idx + 1\n                            };\n                        })\n                    };\n                    that._month1 = new DropDownList(monthInputs[0], options);\n                    that._month2 = new DropDownList(monthInputs[1], options);\n                    if (month) {\n                        month = month[0];\n                        that._month1.value(month);\n                        that._month2.value(month);\n                    }\n                }\n            },\n            _end: function () {\n                var that = this;\n                var rule = that._value;\n                var container = that._container;\n                var namespace = that._namespace;\n                var click = function (e) {\n                    that._toggleEnd(e.currentTarget.value);\n                    that.trigger('change');\n                };\n                var endRule;\n                that._buttonNever = container.find('.k-recur-end-never').on(CLICK + namespace, click);\n                that._buttonCount = container.find('.k-recur-end-count').on(CLICK + namespace, click);\n                that._buttonUntil = container.find('.k-recur-end-until').on(CLICK + namespace, click);\n                if (rule.count) {\n                    endRule = 'count';\n                } else if (rule.until) {\n                    endRule = 'until';\n                }\n                that._toggleEnd(endRule);\n            },\n            _period: function () {\n                var that = this;\n                var rule = that._value;\n                var monthly = rule.freq === 'monthly';\n                var toggleRule = monthly ? that._toggleMonthDay : that._toggleYear;\n                var selector = '.k-recur-' + (monthly ? 'month' : 'year') + '-radio';\n                var radioButtons = that._container.find(selector);\n                if (!monthly && rule.freq !== 'yearly') {\n                    return;\n                }\n                radioButtons.on(CLICK + that._namespace, function (e) {\n                    toggleRule.call(that, e.currentTarget.value);\n                    that.trigger('change');\n                });\n                that._buttonMonthDay = radioButtons.eq(0);\n                that._buttonWeekDay = radioButtons.eq(1);\n                toggleRule.call(that, rule.weekDays ? 'weekday' : 'monthday');\n            },\n            _toggleEnd: function (endRule) {\n                var that = this;\n                var count, until;\n                var enableCount, enableUntil;\n                if (endRule === 'count') {\n                    that._buttonCount.prop('checked', true);\n                    enableCount = true;\n                    enableUntil = false;\n                    count = that._count.value();\n                    until = null;\n                } else if (endRule === 'until') {\n                    that._buttonUntil.prop('checked', true);\n                    enableCount = false;\n                    enableUntil = true;\n                    count = null;\n                    until = that._until.value();\n                } else {\n                    that._buttonNever.prop('checked', true);\n                    enableCount = enableUntil = false;\n                    count = until = null;\n                }\n                if (that._count) {\n                    that._count.enable(enableCount);\n                }\n                if (that._until) {\n                    that._until.enable(enableUntil);\n                }\n                that._value.count = count;\n                that._value.until = until;\n            },\n            _toggleMonthDay: function (monthRule) {\n                var that = this;\n                var enableMonthDay = false;\n                var enableWeekDay = true;\n                var clear = false;\n                var monthDays;\n                if (monthRule === 'monthday') {\n                    that._buttonMonthDay.prop('checked', true);\n                    monthDays = [that._monthDay.value()];\n                    enableMonthDay = true;\n                    enableWeekDay = false;\n                    clear = true;\n                } else {\n                    that._buttonWeekDay.prop('checked', true);\n                    monthDays = null;\n                }\n                that._weekDay.enable(enableWeekDay);\n                that._weekDayOffset.enable(enableWeekDay);\n                that._monthDay.enable(enableMonthDay);\n                that._value.monthDays = monthDays;\n                that._weekDayRule(clear);\n            },\n            _toggleYear: function (yearRule) {\n                var that = this;\n                var enableMonth1 = false;\n                var enableMonth2 = true;\n                var month;\n                if (yearRule === 'monthday') {\n                    enableMonth1 = true;\n                    enableMonth2 = false;\n                    month = that._month1.value();\n                } else {\n                    month = that._month2.value();\n                }\n                that._month1.enable(enableMonth1);\n                that._month2.enable(enableMonth2);\n                that._value.months = [month];\n                that._toggleMonthDay(yearRule);\n            }\n        });\n        ui.plugin(RecurrenceEditor);\n        var RECURRENCE_HEADER_TEMPLATE = kendo.template('<div data-role=\"content\"><ul><li class=\"k-item\"><label class=\"k-label\"><span class=\"k-item-title\">#:headerTitle#</span>' + '<div class=\"k-recur-pattern\"></div></label></li>' + '<li class=\"k-item k-recur-view\"></li></ul></div>');\n        var RECURRENCE_REPEAT_PATTERN_TEMPLATE = kendo.template('# if (frequency !== \"never\") { #' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.repeatEvery#</span>' + '<div class=\"k-recur-editor-wrap\">' + '<input class=\"k-recur-interval\" type=\"number\" pattern=\"\\\\\\\\d*\"/>' + '# if (messages.interval.length) { #' + '<label class=\"k-recur-editor-text\">#:messages.interval#</label>' + '# } #' + '</div>' + '</label>' + '# } #' + '# if (frequency === \"weekly\") { #' + '<ul class=\"k-recur-items-wrap\">' + '<li class=\"k-item k-no-click\"><label class=\"k-label\"><span class=\"k-item-title\">#:messages.repeatOn#</span></label></li>' + '#=weekDayCheckBoxes(firstWeekDay)#' + '</ul>' + '# } else if (frequency === \"monthly\") { #' + '<ul class=\"k-recur-items-wrap\">' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.repeatBy#</span>' + '<div class=\"k-repeat-rule\"></div>' + '</label>' + '</li>' + '<li class=\"k-monthday-view k-item\" style=\"display:none\">' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.day#</span>' + '<div><input class=\"k-recur-monthday\" type=\"number\" title=\"#:messages.day#\" pattern=\"\\\\\\\\d*\"/></div>' + '</label>' + '</li>' + '<li class=\"k-weekday-view k-item\" style=\"display:none\">' + '<ul class=\"k-recur-items-wrap\">' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.every#</span>' + '<div><select class=\"k-recur-weekday-offset\" title=\"#:messages.every#\"></select></div>' + '</label>' + '</li>' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.day#</span>' + '<div><select class=\"k-recur-weekday\" title=\"#:messages.day#\"></select></div>' + '</label>' + '</li>' + '</ul>' + '</li>' + '</ul>' + '# } else if (frequency === \"yearly\") { #' + '<ul class=\"k-recur-items-wrap\">' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.repeatBy#</span>' + '<div class=\"k-repeat-rule\"></div>' + '</label>' + '</li>' + '<li class=\"k-monthday-view k-item\" style=\"display:none\">' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.day#</span>' + '<div><input class=\"k-recur-monthday\" type=\"number\" title=\"#:messages.day#\" pattern=\"\\\\\\\\d*\"/></div>' + '</label>' + '</li>' + '<li class=\"k-weekday-view k-item\" style=\"display:none\">' + '<ul class=\"k-recur-items-wrap\">' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.every#</span>' + '<div><select class=\"k-recur-weekday-offset\" title=\"#:messages.every#\"></select></div>' + '</label>' + '</li>' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.day#</span>' + '<div><select class=\"k-recur-weekday\" title=\"#:messages.day#\"></select></div>' + '</label>' + '</li>' + '</ul>' + '</li>' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.month#</span>' + '<div><select class=\"k-recur-month\" title=\"#:messages.month#\"></select></div>' + '</label>' + '</li>' + '</ul>' + '# } #');\n        var RECURRENCE_END_PATTERN_TEMPLATE = kendo.template('# if (endPattern === \"count\") { #' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.after#</span>' + '<div><input class=\"k-recur-count\" type=\"number\" pattern=\"\\\\\\\\d*\" /></div>' + '</label>' + '# } else if (endPattern === \"until\") { #' + '<label class=\"k-label\">' + '<span class=\"k-item-title\">#:messages.on#</span>' + '<div><input type=\"date\" class=\"k-recur-until\" /></div>' + '</label>' + '# } #');\n        var RECURRENCE_GROUP_BUTTON_TEMPLATE = kendo.template('<select class=\"k-scheduler-select\">' + '#for (var i = 0, length = dataSource.length; i < length; i++) {#' + '<option value=\"#=dataSource[i].value#\" #= value === dataSource[i].value  ? \"selected\" : \"\" #>#:dataSource[i].text#</option>' + '#}#' + '</select>');\n        var MobileRecurrenceEditor = BaseRecurrenceEditor.extend({\n            init: function (element, options) {\n                var that = this;\n                BaseRecurrenceEditor.fn.init.call(that, element, options);\n                options = that.options;\n                that._optionTemplate = kendo.template('<option value=\"#:value#\">#:text#</option>');\n                that.value(options.value);\n                that._pane = options.pane;\n                that._initRepeatButton();\n                that._initParentRepeatEnd();\n                that._defaultValue = that._value;\n            },\n            options: {\n                name: 'MobileRecurrenceEditor',\n                animations: {\n                    left: 'slide',\n                    right: 'slide:right'\n                },\n                mobile: true,\n                messages: {\n                    cancel: 'Cancel',\n                    update: 'Save',\n                    endTitle: 'Repeat ends',\n                    repeatTitle: 'Repeat pattern',\n                    headerTitle: 'Repeat event',\n                    end: {\n                        patterns: {\n                            never: 'Never',\n                            after: 'After...',\n                            on: 'On...'\n                        },\n                        never: 'Never',\n                        after: 'End repeat after',\n                        on: 'End repeat on'\n                    },\n                    daily: { interval: '' },\n                    hourly: { interval: '' },\n                    weekly: { interval: '' },\n                    monthly: {\n                        interval: '',\n                        repeatBy: 'Repeat by: ',\n                        dayOfMonth: 'Day of the month',\n                        dayOfWeek: 'Day of the week',\n                        repeatEvery: 'Repeat every',\n                        every: 'Every',\n                        day: 'Day '\n                    },\n                    yearly: {\n                        interval: '',\n                        repeatBy: 'Repeat by: ',\n                        dayOfMonth: 'Day of the month',\n                        dayOfWeek: 'Day of the week',\n                        repeatEvery: 'Repeat every: ',\n                        every: 'Every',\n                        month: 'Month',\n                        day: 'Day'\n                    }\n                }\n            },\n            events: ['change'],\n            value: function (value) {\n                var that = this;\n                var timezone = that.options.timezone;\n                if (value === undefined) {\n                    if (!that._value.freq) {\n                        return '';\n                    }\n                    return serialize(that._value, timezone);\n                }\n                that._value = parseRule(value, timezone) || {};\n            },\n            destroy: function () {\n                this._destroyView();\n                kendo.destroy(this._endFields);\n                this.element.off(CLICK + this._namespace);\n                BaseRecurrenceEditor.fn.destroy.call(this);\n            },\n            _initInterval: function () {\n                var that = this;\n                var rule = that._value;\n                that._container.find('.k-recur-interval').val(that._value.interval || 1).on(CHANGE + that._namespace, function (e) {\n                    rule.interval = e.target.value;\n                    that._trigger();\n                });\n            },\n            _initRepeatButton: function () {\n                var that = this;\n                var freq = that.options.messages.frequencies[this._value.freq || 'never'];\n                that._chevronButton = $('<span class=\"k-icon k-i-arrow-chevron-right\"></span>');\n                that._repeatValue = $('<span class=\"\">' + freq + '</span>');\n                that.element.append(that._repeatValue).append(that._chevronButton);\n                that.element.parents('li.k-item').on(CLICK + that._namespace, function (e) {\n                    e.preventDefault();\n                    that._createView('repeat');\n                    that._pane.navigate(that._view, that.options.animations.left);\n                });\n            },\n            _endLiItem: function () {\n                var that = this;\n                return '<li class=\"k-item\"><label class=\"k-label\"><span class=\"k-item-title\">' + that.options.messages.end.mobileLabel + '</span><label></li>';\n            },\n            _initParentRepeatEnd: function () {\n                var that = this;\n                var endLabelField = $(that._endLiItem()).insertAfter(that.element.parents('li.k-item'));\n                var endEditField = $('<div class=\"k-scheduler-recur-end-wrap\"><span class=\"k-scheduler-recur-end\"></span></div>').appendTo(endLabelField.find('.k-label'));\n                endLabelField.on(CLICK + that._namespace, function (e) {\n                    e.preventDefault();\n                    that._navigateToView('repeat');\n                });\n                that._endParentLabelField = endLabelField.toggle(!!that._value.freq);\n                that._endParentEndButton = endEditField.find('.k-scheduler-recur-end').text(that._endText());\n            },\n            _initRepeatEnd: function () {\n                var that = this;\n                var endLabelField = $(that._endLiItem()).insertAfter(that._container);\n                var endEditField = $('<div class=\"k-scheduler-recur-end-wrap\"><span class=\"k-scheduler-recur-end\"></span><span class=\"k-icon k-i-arrow-chevron-right\"></span></div>').appendTo(endLabelField.find('.k-label'));\n                endLabelField.on(CLICK + that._namespace, function (e) {\n                    e.preventDefault();\n                    that._navigateToView('end');\n                });\n                that._endLabelField = endLabelField.toggleClass('k-state-disabled', !that._value.freq);\n                that._endButton = endEditField.find('.k-scheduler-recur-end').text(that._endText());\n            },\n            _navigateToView: function (viewName) {\n                var that = this;\n                that._createView(viewName);\n                that._pane.navigate(that._view, that.options.animations.left);\n            },\n            _endText: function () {\n                var rule = this._value;\n                var messages = this.options.messages.end;\n                var text = messages.never;\n                if (rule.count) {\n                    text = kendo.format('{0} {1}', messages.after, rule.count);\n                } else if (rule.until) {\n                    text = kendo.format('{0} {1:d}', messages.on, rule.until);\n                }\n                return text;\n            },\n            _initFrequency: function () {\n                var that = this;\n                var frequencyMessages = that.options.messages.frequencies;\n                var html = RECURRENCE_GROUP_BUTTON_TEMPLATE({\n                    dataSource: $.map(this.options.frequencies, function (frequency) {\n                        return {\n                            text: frequencyMessages[frequency],\n                            value: frequency !== 'never' ? frequency : ''\n                        };\n                    }),\n                    value: that._value.freq || '',\n                    ns: kendo.ns\n                });\n                that._view.element.find('.k-recur-pattern').append(html);\n                that._view.element.find('.k-scheduler-select').on(CHANGE + that._namespace, function (e) {\n                    var value = e.target.value;\n                    that._value = { freq: value };\n                    that._initRepeatView(true);\n                });\n            },\n            _initEndNavigation: function () {\n                var that = this;\n                var endMessages = that.options.messages.end.patterns;\n                var rule = that._value;\n                var value = '';\n                if (rule.count) {\n                    value = 'count';\n                } else if (rule.until) {\n                    value = 'until';\n                }\n                var html = RECURRENCE_GROUP_BUTTON_TEMPLATE({\n                    dataSource: [\n                        {\n                            text: endMessages.never,\n                            value: ''\n                        },\n                        {\n                            text: endMessages.after,\n                            value: 'count'\n                        },\n                        {\n                            text: endMessages.on,\n                            value: 'until'\n                        }\n                    ],\n                    value: value,\n                    ns: kendo.ns\n                });\n                that._view.element.find('.k-recur-pattern').append(html);\n                that._view.element.find('.k-scheduler-select').on(CHANGE + that._namespace, function (e) {\n                    var value = e.target.value;\n                    var count = null;\n                    var until = null;\n                    that._initEndView(value);\n                    if (that._count.length) {\n                        count = that._count.val();\n                        until = null;\n                    } else if (that._until.length) {\n                        count = null;\n                        until = that._until.val ? kendo.parseDate(that._until.val(), 'yyyy-MM-dd') : that._until.value();\n                    }\n                    rule.count = count;\n                    rule.until = until;\n                });\n            },\n            _createView: function (viewType) {\n                var that = this;\n                var options = that.options;\n                var messages = options.messages;\n                var headerTitle = messages[viewType === 'repeat' ? 'repeatTitle' : 'endTitle'];\n                var html = '<div data-role=\"view\" class=\"k-popup-edit-form k-scheduler-edit-form k-mobile-list\" id=\"recurrence\">' + '<div data-role=\"header\" class=\"k-header\">' + '<a href=\"#\" class=\"k-header-cancel k-scheduler-cancel k-link\" title=\"' + messages.cancel + '\"' + 'aria-label=\"' + messages.cancel + '\"><span class=\"k-icon k-i-arrow-chevron-left\"></span></a>' + messages.headerTitle + '<a href=\"#\" class=\"k-header-done k-scheduler-update k-link\" title=\"' + messages.update + '\" ' + 'aria-label=\"' + messages.update + '\"><span class=\"k-icon k-i-check\"></span></a>' + '</div>';\n                var returnViewId = that._pane.view().id;\n                that._view = that._pane.append(html + RECURRENCE_HEADER_TEMPLATE({ headerTitle: headerTitle }));\n                that._view.element.on(CLICK + that._namespace, 'a.k-scheduler-cancel, a.k-scheduler-update', function (e) {\n                    e.preventDefault();\n                    e.stopPropagation();\n                    if ($(this).hasClass('k-scheduler-update')) {\n                        that.trigger('change');\n                        that._defaultValue = $.extend({}, that._value);\n                    } else {\n                        that._value = that._defaultValue;\n                    }\n                    var frequency = that._value.freq;\n                    that._endParentEndButton.text(that._endText());\n                    that._endParentLabelField.toggle(!!frequency && frequency !== 'never');\n                    that._endButton.text(that._endText());\n                    that._repeatValue.text(messages.frequencies[frequency || 'never']);\n                    that._pane.one('viewShow', function () {\n                        that._destroyView();\n                    });\n                    that._pane.navigate(returnViewId, that.options.animations.right);\n                });\n                that._container = that._view.element.find('.k-recur-view');\n                if (viewType === 'repeat') {\n                    that._initFrequency();\n                    that._initRepeatView(true);\n                    that._initRepeatEnd();\n                } else {\n                    that._initEndNavigation();\n                    that._initEndView();\n                }\n            },\n            _destroyView: function () {\n                if (this._view) {\n                    this._view.destroy();\n                    this._view.element.remove();\n                    this._container = null;\n                }\n                this._view = null;\n            },\n            _initRepeatView: function (isMobile) {\n                var that = this;\n                var frequency = that._value.freq || 'never';\n                var data = {\n                    frequency: frequency,\n                    weekDayCheckBoxes: isMobile ? mobileWeekDayCheckBoxes : weekDayCheckBoxes,\n                    firstWeekDay: that.options.firstWeekDay,\n                    messages: that.options.messages[frequency]\n                };\n                var html = RECURRENCE_REPEAT_PATTERN_TEMPLATE(data);\n                var container = that._container = that._container || this._pane.view().content.find('li.k-recur-view');\n                var rule = that._value;\n                if (that._endLabelField) {\n                    that._endLabelField.toggleClass('k-state-disabled', frequency === 'never');\n                }\n                kendo.destroy(container);\n                container.html(html);\n                if (!html) {\n                    that._value = {};\n                    container.hide();\n                    return;\n                } else {\n                    container.show();\n                }\n                if (frequency === 'weekly' && !rule.weekDays) {\n                    rule.weekDays = [{\n                            day: that.options.start.getDay(),\n                            offset: 0\n                        }];\n                }\n                that._initInterval();\n                that._initMonthDay();\n                that._initWeekDays();\n                that._initWeekDay();\n                that._initMonth();\n                that._period();\n            },\n            _initMonthDay: function () {\n                var that = this;\n                var rule = that._value;\n                var monthDayInput = that._monthDay = that._container.find('.k-recur-monthday');\n                monthDayInput.attr({\n                    min: 1,\n                    max: 31\n                }).val(rule.monthDays ? rule.monthDays[0] : that.options.start.getDate()).on(CHANGE + that._namespace, function (e) {\n                    rule.count = e.target.value;\n                    that._trigger();\n                });\n            },\n            _initCount: function () {\n                var that = this, input = that._count = that._container.find('.k-recur-count'), rule = that._value;\n                input.val(rule.count || 1).on(CHANGE + that._namespace, function (ev) {\n                    rule.count = ev.target.value;\n                    that._trigger();\n                });\n            },\n            _initEndView: function (endPattern) {\n                var that = this;\n                var rule = that._value;\n                if (endPattern === undefined) {\n                    if (rule.count) {\n                        endPattern = 'count';\n                    } else if (rule.until) {\n                        endPattern = 'until';\n                    }\n                }\n                var data = {\n                    endPattern: endPattern,\n                    messages: that.options.messages.end\n                };\n                kendo.destroy(that._container);\n                that._container.html(RECURRENCE_END_PATTERN_TEMPLATE(data));\n                that._initCount();\n                that._initUntil();\n            },\n            _initWeekDay: function () {\n                var that = this, data;\n                var weekdayMessage = that.options.messages.weekdays;\n                var offsetMessage = that.options.messages.offsetPositions;\n                var weekDaySelect = that._container.find('.k-recur-weekday');\n                var change = function () {\n                    that._weekDayRule();\n                    that.trigger('change');\n                };\n                if (weekDaySelect[0]) {\n                    that._weekDayOffset = that._container.find('.k-recur-weekday-offset').html(that._options([\n                        {\n                            text: offsetMessage.first,\n                            value: '1'\n                        },\n                        {\n                            text: offsetMessage.second,\n                            value: '2'\n                        },\n                        {\n                            text: offsetMessage.third,\n                            value: '3'\n                        },\n                        {\n                            text: offsetMessage.fourth,\n                            value: '4'\n                        },\n                        {\n                            text: offsetMessage.last,\n                            value: '-1'\n                        }\n                    ])).change(change);\n                    data = [\n                        {\n                            text: weekdayMessage.day,\n                            value: 'day'\n                        },\n                        {\n                            text: weekdayMessage.weekday,\n                            value: 'weekday'\n                        },\n                        {\n                            text: weekdayMessage.weekend,\n                            value: 'weekend'\n                        }\n                    ];\n                    data = data.concat($.map(kendo.culture().calendar.days.names, function (dayName, idx) {\n                        return {\n                            text: dayName,\n                            value: idx\n                        };\n                    }));\n                    that._weekDay = weekDaySelect.html(that._options(data)).change(change).val(that.options.start.getDay());\n                    that._weekDayView();\n                }\n            },\n            _initMonth: function () {\n                var that = this;\n                var rule = that._value;\n                var start = that.options.start;\n                var month = rule.months || [start.getMonth() + 1];\n                var monthSelect = that._container.find('.k-recur-month');\n                var monthNames = kendo.culture().calendar.months.names;\n                if (monthSelect[0]) {\n                    var data = $.map(monthNames, function (monthName, idx) {\n                        return {\n                            text: monthName,\n                            value: idx + 1\n                        };\n                    });\n                    monthSelect.html(that._options(data)).change(function () {\n                        rule.months = [Number(this.value)];\n                    });\n                    that._monthSelect = monthSelect;\n                    if (month) {\n                        monthSelect.val(month[0]);\n                    }\n                }\n            },\n            _period: function () {\n                var that = this;\n                var rule = that._value;\n                var container = that._container;\n                var messages = that.options.messages[rule.freq];\n                var repeatRuleGroupButton = container.find('.k-repeat-rule');\n                var weekDayView = container.find('.k-weekday-view');\n                var monthDayView = container.find('.k-monthday-view');\n                if (repeatRuleGroupButton[0]) {\n                    var currentValue = rule.weekDays ? 'weekday' : 'monthday';\n                    var html = RECURRENCE_GROUP_BUTTON_TEMPLATE({\n                        value: currentValue,\n                        dataSource: [\n                            {\n                                text: messages.dayOfMonth,\n                                value: 'monthday'\n                            },\n                            {\n                                text: messages.dayOfWeek,\n                                value: 'weekday'\n                            }\n                        ],\n                        ns: kendo.ns\n                    });\n                    var init = function (val) {\n                        var weekDayName = that._weekDay.val();\n                        var weekDayOffset = that._weekDayOffset.val();\n                        var monthDay = that._monthDay.val();\n                        var month = that._monthSelect ? that._monthSelect.val() : null;\n                        if (val === 'monthday') {\n                            rule.weekDays = null;\n                            rule.monthDays = monthDay ? [monthDay] : monthDay;\n                            rule.months = month ? [Number(month)] : month;\n                            weekDayView.hide();\n                            monthDayView.show();\n                        } else {\n                            rule.monthDays = null;\n                            rule.months = month ? [Number(month)] : month;\n                            rule.weekDays = [{\n                                    offset: Number(weekDayOffset),\n                                    day: Number(weekDayName)\n                                }];\n                            weekDayView.show();\n                            monthDayView.hide();\n                        }\n                    };\n                    repeatRuleGroupButton.append(html);\n                    repeatRuleGroupButton.find('.k-scheduler-select').on(CHANGE + that._namespace, function (e) {\n                        init(e.target.value);\n                    });\n                    init(currentValue);\n                }\n            },\n            _initUntil: function () {\n                var that = this;\n                var input = that._container.find('.k-recur-until');\n                var start = that.options.start;\n                var rule = that._value;\n                var until = rule.until;\n                var min = until && until < start ? until : start;\n                if (kendo.support.input.date) {\n                    that._until = input.attr('min', kendo.toString(min, 'yyyy-MM-dd')).val(kendo.toString(until || start, 'yyyy-MM-dd')).on('change', function () {\n                        rule.until = kendo.parseDate(this.value, 'yyyy-MM-dd');\n                    });\n                } else {\n                    that._until = input.kendoDatePicker({\n                        min: min,\n                        value: until || start,\n                        change: function () {\n                            rule.until = this.value();\n                        }\n                    }).data('kendoDatePicker');\n                }\n            },\n            _options: function (data, optionLabel) {\n                var idx = 0;\n                var html = '';\n                var length = data.length;\n                var template = this._optionTemplate;\n                if (optionLabel) {\n                    html += template({\n                        value: '',\n                        text: optionLabel\n                    });\n                }\n                for (; idx < length; idx++) {\n                    html += template(data[idx]);\n                }\n                return html;\n            }\n        });\n        ui.plugin(MobileRecurrenceEditor);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}