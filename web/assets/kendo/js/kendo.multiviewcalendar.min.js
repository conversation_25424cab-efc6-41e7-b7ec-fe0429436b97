/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.multiviewcalendar.min",["kendo.core.min","kendo.selectable.min","kendo.calendar.min"],e)}(function(){return function(e,t){function a(t){var a=e(this).hasClass("k-state-disabled");a||e(this).toggleClass(O,j.indexOf(t.type)>-1||t.type==q)}function n(e,t,a,n){var l,s;for(l=0;l<=t;l++)s=new Date(a.getTime()),s=new Date(s.setDate(s.getDate()+l)),n(s)||e.push(s)}function l(e,t){var a,n,l;return+t<+e&&(a=+e,i.views[0].setDate(e,t),i.views[0].setDate(t,new Date(a))),n=Date.UTC(e.getFullYear(),e.getMonth(),e.getDate()),l=Date.UTC(t.getFullYear(),t.getMonth(),t.getDate()),Math.ceil((+l-+n)/r.date.MS_PER_DAY)}function s(e,a,n){var l;return"month"===a?(l=new ne(e.getFullYear(),e.getMonth()+n,e.getDate()),l.setFullYear(e.getFullYear()),(Math.abs(l.getMonth()-e.getMonth())>n||n>10)&&(l.setMonth(e.getMonth()+n),l=i.views[0].last(l)),l):"year"===a?(l=new ne(1,e.getMonth(),e.getDate()),l.setFullYear(e.getFullYear()+n),Math.abs(l.getFullYear()-e.getFullYear())>n&&(l=new ne(1,e.getMonth(),1),l.setFullYear(e.getFullYear()+n),l=i.views[1].last(l)),l):"decade"===a?(l=new ne(1,e.getMonth(),e.getDate()),l.setFullYear(e.getFullYear()+10*n),Math.abs(l.getFullYear()-e.getFullYear())>10*n&&(l=new ne(1,e.getMonth(),1),l.setFullYear(e.getFullYear()+10*n),l=i.views[2].last(l)),l):"century"===a?(l=new ne(1,e.getMonth(),e.getDate()),l.setFullYear(e.getFullYear()+100*n),Math.abs(l.getFullYear()-e.getFullYear())>100*n&&(l=new ne(1,e.getMonth(),1),l.setFullYear(e.getFullYear()+100*n),l=i.views[3].last(l)),l):t}var r=window.kendo,i=r.calendar,o=r.support,c=i.isInRange,u=i.toDateObject,d=i.createDate,_=i.isEqualDate,f=i.getToday,g=r.keys,v=r.ui,h=v.Widget,m=v.Selectable,b=r.template,p=o.mobileOS,w=".kendoMultiViewCalendar",k="click",D="keydown",C="id",S="min",y="month",V=".",x="century",A="decade",F="change",T="navigate",Y="value",R="k-state-focused",E="k-state-selected",M="k-range-mid",B="k-range-split-end",N="k-range-split-start",I="k-range-start",P="k-range-end",O="k-state-hover",W="k-state-disabled",H="k-nav-today",U="k-other-month",G="k-out-of-range",z="td:has(.k-link):not(."+G+")",K="td:has(.k-link):not(."+W+"):not(."+G+")",L="blur",q="focus",j=o.touch?"touchstart":"mouseenter",Q=o.touch?"touchend"+w+" touchmove"+w:"mouseleave"+w,J="_prevArrow",X="_nextArrow",Z="aria-selected",$="input,a,textarea,.k-multiselect-wrap,select,button,.k-button>span,.k-button>img,span.k-icon.k-i-arrow-60-down,span.k-icon.k-i-arrow-60-up",ee="aria-disabled",te="aria-label",ae=e.proxy,ne=Date,le={month:0,year:1,decade:2,century:3},se=h.extend({init:function(e,t){var a=this;h.fn.init.call(a,e,t),a.userEvents=new r.UserEvents(a.element,{global:!0,allowSelection:!0,filter:a.options.filter,tap:ae(a._tap,a),touchAction:"none"})},events:[F],options:{name:"RangeSelectable",filter:">*",inputSelectors:$,multiple:!1,dragToSelect:!0,relatedTarget:e.noop},destroy:function(){var e=this;h.fn.destroy.call(e),e.userEvents.destroy(),e._lastActive=e.element=e.userEvents=e._start=e._end=null},_allowSelection:function(t){return!e(t).is(this.options.inputSelectors)||(this.userEvents.cancel(),!1)},start:function(e){return e===t?this._start:(e.addClass(I+" "+E),this._start=e,t)},end:function(e){return e===t?this._start:(e.addClass(P+" "+E),this._end=e,t)},mid:function(t){var a=this.element.find("table.k-month");t.addClass(M),a.each(function(){var t=e(this),a=t.find(K+":last"),n=t.find(K+":first");a.hasClass(M)&&a.addClass(B),n.hasClass(M)&&n.addClass(N)})},clear:function(e){this.element.find(z).removeClass(P+" "+E+" "+I+" "+M+" "+B+" "+N),e&&(this._start=this._end=null)},selectFrom:function(t){var a=this,n=a.element.find(z),l=e.inArray(e(t)[0],n);a.clear(),a.start(t),n=n.filter(function(e){return e>l}),a.mid(n)},selectTo:function(t){var a=this,n=a.element.find(z),l=e.inArray(e(t)[0],n);a.clear(),n=n.filter(function(e){return e<l}),a.mid(n),a.end(e(t))},range:function(a,n){var l,s,r,i,o=this;return a===t?{start:o._start,end:o._end}:(l=o.element.find(z),s=e.inArray(e(a)[0],l),r=e.inArray(e(n)[0],l),s>r&&(i=n,n=a,a=i,i=s,s=r,r=i),o.clear(),a.addClass(I+" "+E),o._start=a,l=l.filter(function(e){return e>s&&e<r}),o.mid(l),o.end(e(n)),t)},change:function(){this.trigger(F)},_tap:function(a){var n,l,s,r=e(a.target),i=this;if(i._lastActive=r,!i._start)return i.start(r),i.trigger(F),t;if(i._start&&!i._end)return n=i.element.find(z),l=e.inArray(e(i._start)[0],n),s=e.inArray(e(r)[0],n),+u(e(i._start).find("a"))>+u(e(r).find("a"))?(i.clear(),i.start(r),i.trigger(F),t):(n=n.filter(function(e){return e>l&&e<s}),i.mid(n),i.end(e(r)),i.trigger(F),t);if(i._start&&i._end){if(r.hasClass(M))return i._toggling?i.range(i._start,r):i.range(r,i._end),i._toggling=!i._toggling,i.trigger(F),t;i._toggling=!1,i._end=null,i.clear(),i.start(r),i.trigger(F)}}}),re=h.extend({init:function(t,a){var n,l,s=this;h.fn.init.call(s,t,a),t=s.wrapper=s.element,a=s.options,s.options.disableDates=i.disabled(s.options.disableDates),l=r.getCulture(a.culture),a.format=r._extractFormat(a.format||l.calendars.standard.patterns.d),s._templates(),s._header(),s._wrapper(),n=t.addClass("k-widget k-calendar k-calendar-range"+(a.weekNumber?" k-week-number":"")).on(D+w,"table.k-content",ae(s._move,s)).on(L+w,"table",ae(s._blur,s)).on(k+w,K,function(t){var a=t.currentTarget.firstChild;a.href.indexOf("#")!=-1&&t.preventDefault(),s._click(e(a))}).on(j+w,K,ae(s._mouseEnter,s)).on(Q,K,function(){e(this).removeClass(O)}).attr(C),n&&(s._cellID=n+"_cell_selected"),s._calendarWidth=s.element.width(),s._range=a.range,s._initViews({viewName:a.start,value:a.value}),s._selectable(),s._footer(s.footer),s._selectDates=[],s.value(a.value),"multiple"==a.selectable&&(s._selectDates=a.selectDates.length?a.selectDates:s._selectDates,s._restoreSelection()),"range"==a.selectable&&s.selectRange(s._range),r.notify(s)},options:{name:"MultiViewCalendar",value:null,min:new ne(1900,0,1),max:new ne(2099,11,31),dates:[],disableDates:null,culture:"",footer:"",format:"",month:{},range:{start:null,end:null},weekNumber:!1,views:2,showViewHeader:!1,selectable:"single",selectDates:[],start:y,depth:y,messages:{weekColumnHeader:""}},events:[F,T],setOptions:function(e){var t,a=this;for(i.normalize(e),e.disableDates=i.disabled(e.disableDates),h.fn.setOptions.call(a,e),a._selectable(),a._templates(),a._footer(a.footer),t=0;t<a._views.length;t++)a._views[t].off(w).remove();a._initViews({viewName:e.start,value:e.value}),a._range=e.range||{start:null,end:null},a._restoreSelection()},destroy:function(){var e,t=this;if(t._cell=null,t._currentView=null,t._current=null,t._views)for(e=0;e<t._views.length;e++)t._views[e].off(w).remove();t.element.off(w),t.header&&(t.header.off(w),t._title=null,t.header=null),t.selectable&&(t.selectable.destroy(),t.selectable=null),t.rangeSelectable&&(t.rangeSelectable.destroy(),t.rangeSelectable=null),t._today&&r.destroy(t._today.off(w)),t._views=null,h.fn.destroy.call(t)},current:function(){return this._current},focus:function(){var e;this._cell?this._cell.closest("table").trigger("focus"):this._current&&this._dateInViews(this._current)?(this._cell=this._cellByDate(this._current),this._cell.closest("table").trigger("focus")):(e=this.element.find("table").first().trigger("focus"),this._cell=e.find(K+":first"),this._current=u(this._cell.find("a"))),this._cell.addClass(R)},min:function(e){return this._option(S,e)},max:function(e){return this._option("max",e)},view:function(){return this._currentView},navigateToPast:function(){this._navigate(J,-1)},navigateToFuture:function(){this._navigate(X,1)},navigateUp:function(){var e=this,t=e._index;e._title.hasClass(W)||e.navigate(e._current,++t)},navigateDown:function(e){var a=this,n=a._index,l=a.options.depth;if(e)return n===le[l]?(_(a._value,a._current)&&_(a._value,e)||(a.value(e),a.trigger(F)),t):(a.navigate(e,--n),t)},navigate:function(e,a){var n,l,s,r,o;for(a=isNaN(a)?i.views[i.viewsEnum[a]]:i.views[a],n=this,l=n.options,s=l.min,r=l.max,n._current=e?e:e=new ne((+i.restrictValue(e,s,r))),a===t&&(a=n._currentView),n._currentView=a,o=0;o<n._views.length;o++)n._views[o].off(w).remove();n._initViews({viewName:a.name,value:e}),n._restoreSelection()},_updateHeader:function(){var t,a,n,l,r=this,i=r._currentView,o=r._title,u=r._firstViewValue,d=r.options,_=r._visibleRange(),f=d.culture,g=d.min,v=d.max;i.name===A||i.name===x?(t=s(u,i.name,d.views-1),c(t,g,v)||(t=v),o.html(i.first(u).getFullYear()+" - "+i.last(t).getFullYear())):o.html(i.title(u,g,v,f)+" - "+i.title(s(u,i.name,d.views-1),g,v,f)),a=i.name===x,o.toggleClass(W,a).attr(ee,a),n=i.compare(_.start,r.options.min)<1,l=i.compare(_.end,r.options.max)>-1,n&&l?r._navContainer&&(r._navContainer.remove(),r._navContainer=null):(r._navContainer||(r._navContainer=e('<span class="k-calendar-nav"><a href="#" role="button" class="k-button k-button-icon k-prev-view" '+te+'="Previous"><span class="k-icon k-i-arrow-60-left"></span></a><a href="#" role="button" class="k-button k-button-icon k-next-view" '+te+'="Next"><span class="k-icon k-i-arrow-60-right"></span></a></span>').appendTo(r.header),r[J]=r._navContainer.find(".k-prev-view"),r[X]=r._navContainer.find(".k-next-view")),r[J].toggleClass(W,n).attr(ee,n),r[J].hasClass(W)&&r[J].removeClass(O),r[X].toggleClass(W,l).attr(ee,l),r[X].hasClass(W)&&r[X].removeClass(O))},_mouseEnter:function(t){var a,n,l,s,r=this,i=e(t.currentTarget);if(i.addClass(O),r.rangeSelectable&&"month"===r._currentView.name&&(a=r.selectRange(),a.start&&!a.end)){if(r._dateInViews(r.selectRange().start)){if(n=r.element.find(r.rangeSelectable.options.filter),l=e.inArray(e(r.rangeSelectable._start)[0],n),s=e.inArray(e(i)[0],n),l>s)return;r.rangeSelectable.range(r.rangeSelectable._start,i)}else+u(r.element.find(z+":first").find("a"))>+a.start&&r.rangeSelectable.selectTo(i);r.rangeSelectable._end=null}},_move:function(a,n){var l,s,i,o,_,f,v=this,h=v.options,m=a.keyCode,b=v._index,p=h.min,w=h.max,k=v.element.find(V+R),D=k.closest("table"),C=new ne((+(v._current||u(k.find("a"))))),S=r.support.isRtl(v.wrapper),y=!1;if(m==g.RIGHT&&!S||m==g.LEFT&&S?(l=1,s=!0):m==g.LEFT&&!S||m==g.RIGHT&&S?(l=-1,s=!0):m==g.UP?(l=0===b?-7:-4,s=!0):m==g.DOWN?(l=0===b?7:4,s=!0):m==g.SPACEBAR?(l=0,s=!0):m==g.HOME?(s=!0,o=D.find(K).eq(0),o.hasClass(R)?(D=D.prev(),D.length?v._focusCell(D.find(K).eq(0)):(y=v[J]&&!v[J].hasClass(W),v._navigate(J,-1,n),v._focusCell(v.element.find("table:first "+K+":first")))):v._focusCell(o)):m==g.END&&(s=!0,o=D.find(K).last(),o.hasClass(R)?(D=D.next(),D.length?v._focusCell(D.find(K).last()):(y=v[X]&&!v[X].hasClass(W),v._navigate(X,1,n),v._focusCell(v.element.find("table:last "+K+":last")))):v._focusCell(o)),a.ctrlKey||a.metaKey)m==g.RIGHT&&!S||m==g.LEFT&&S?(y=v[X]&&!v[X].hasClass(W),v._navigate(X,1,n),s=!0):m==g.LEFT&&!S||m==g.RIGHT&&S?(y=v[J]&&!v[J].hasClass(W),v._navigate(J,-1,n),s=!0):m==g.UP?(y=!v._title.hasClass(W),v.navigateUp(),v._focusCell(v._cellByDate(v._current),!n),s=!0):m==g.DOWN?("month"===v._currentView.name?v.value(C):(v.navigateDown(C),v._focusCell(v._cellByDate(v._current),!n),y=!0),s=!0):m!=g.ENTER&&m!=g.SPACEBAR||"multiple"===h.selectable&&v._toggleSelection(a);else if(a.shiftKey&&"single"!==h.selectable){if(l!==t||i){if(i||v._currentView.setDate(C,l),"month"!==v._currentView.name)return;h.disableDates(C)&&(C=v._nextNavigatable(C,l)),p=d(p.getFullYear(),p.getMonth(),p.getDate()),c(C,p,w)&&(v._dateInViews(C)||(l>0?(y=v[X]&&!v[X].hasClass(W),v._navigate(X,1,n)):(y=v[J]&&!v[J].hasClass(W),v._navigate(J,-1,n))),o=v._cellByDate(C),v._current=C,v.selectable&&(v._selectRange(u((v.selectable._lastActive||k).find("a")),C),v.selectable._lastActive||(v.selectable._lastActive=k),v.trigger(F),v._focusCell(o)),v.rangeSelectable&&(_=u((v.rangeSelectable._lastActive||k).find("a")),v._dateInViews(_)?(v.rangeSelectable._lastActive=v.rangeSelectable._end&&v.rangeSelectable._end.is(V+R)?v.rangeSelectable._start:v._cellByDate(_),v.rangeSelectable.range(v.rangeSelectable._lastActive,o)):+_>+C?(v.rangeSelectable._end=v.rangeSelectable._lastActive,v.rangeSelectable.selectFrom(o)):v.rangeSelectable.selectTo(o),v.rangeSelectable.change(),v._focusCell(o)))}}else m==g.ENTER||m==g.SPACEBAR?("month"===v._currentView.name?(v.selectable&&(v.selectable._lastActive=v._cellByDate(C)),v.value(C),v.rangeSelectable&&v.rangeSelectable.change()):v._click(e(v._cell[0].firstChild),n),s=!0):m!=g.PAGEUP&&m!=g.PAGEDOWN||(s=!0,f=D.find(K).index(k),D=m==g.PAGEUP?D.prev():D.next(),D.length||(m==g.PAGEUP?(y=v[J]&&!v[J].hasClass(W),v.navigateToPast(),D=v.element.find("table:first")):(y=v[X]&&!v[X].hasClass(W),v.navigateToFuture(),D=v.element.find("table:last"))),o=D.find(K).eq(f),v._focusCell(o.length?o:D.find(K).last())),(l||i)&&(i||v._currentView.setDate(C,l),p=d(p.getFullYear(),p.getMonth(),p.getDate()),c(C,p,w)&&(v.selectable&&h.disableDates(C)&&(C=v._nextNavigatable(C,l)),v._dateInViews(C)||(l>0?(y=v[X]&&!v[X].hasClass(W),v._navigate(X,1,n)):(y=v[J]&&!v[J].hasClass(W),v._navigate(X,-1,n))),o=v._cellByDate(C),v._current=C,v._focusCell(o,!n)));return y&&v.trigger(T),s&&a.preventDefault(),v._current},_visualizeSelectedDatesInView:function(){var t,a=this,n={};e.each(a._selectDates,function(e,t){n[r.calendar.views[0].toDateString(t)]=t}),a.selectable.clear(),t=a.element.find("table").find(z).filter(function(t,a){return n[e(a.firstChild).attr(r.attr(Y))]}),t.length>0&&a.selectable._selectElement(t,!0)},_nextNavigatable:function(e,t){var a=this,n=!0,l=a._currentView,s=a.options.min,r=a.options.max,i=a.options.disableDates,o=new Date(e.getTime());for(l.setDate(o,-t);n;){if(l.setDate(e,t),!c(e,s,r)){e=o;break}n=i(e)}return e},_toggleSelection:function(t){var a=this;a.selectable._lastActive=e(a._cell[0]),e(a._cell[0]).hasClass(E)?(a.selectable._unselect(e(a._cell[0])),a.selectable.trigger(F,{event:t})):a.selectable.value(e(a._cell[0]),{event:t})},_option:function(e,a){var n,l=this,s=l.options,i=l._value||l._current;return a===t?s[e]:(a=r.parseDate(a,s.format,s.culture),a&&(s[e]=new ne((+a)),n=e===S?a>i:i>a,n&&(l._value=null),l.navigate(l._value),l._toggle()),t)},_cellByDate:function(t){return t instanceof Date&&(t=this._currentView.toDateString(t)),this.element.find("table").find("td:not(."+U+")").filter(function(){return e(this.firstChild).attr(r.attr(Y))===t})},_selectable:function(){var e=this,t=e.options.selectable;e.selectable&&(e.selectable.destroy(),e.selectable=null),e.rangeSelectable&&(e.rangeSelectable.destroy(),e.rangeSelectable=null),"range"===t.toLowerCase()?e.rangeSelectable=new se(e.wrapper,{filter:"table.k-month "+K,change:ae(e._rangeSelection,e)}):e.selectable=new m(e.wrapper,{aria:!0,dragToSelect:!1,inputSelectors:"input,textarea,.k-multiselect-wrap,select,button,.k-button>span,.k-button>img,span.k-icon.k-i-arrow-60-down,span.k-icon.k-i-arrow-60-up",multiple:m.parseOptions(t).multiple,filter:"table.k-content "+K,change:ae(e._selection,e),relatedTarget:ae(e._onRelatedTarget,e),unselect:ae(e._unselecting,e)})},_onRelatedTarget:function(e){var t=this;t.selectable.options.multiple&&e.is(K)&&e.length>1&&t._focusCell(e.first(),!0)},_getFirstViewDate:function(e){var t,a,n,l=this,r=l.options,i=[],o=new Date((+l._current));for(n=0;n<r.views;n++){if(t=e.first(o),a=e.last(o),+a>+r.max){+t<=+r.max&&i.push({start:t,end:new Date((+r.max))});break}i.push({start:t,end:a}),o=new Date((+s(a,e.name,1)))}for(o=new Date((+l._current)),n=0;n<r.views;n++){if(t=e.first(o),a=e.last(o),+t<+r.min){+a>=+r.min&&i.push({start:new Date((+r.min)),end:a});break}i.push({start:t,end:a}),o=new Date((+s(t,e.name,-1)))}for(t=i[0].start,n=0;n<r.views+1&&i[n];n++)+t>+i[n].start&&(t=i[n].start);return new Date((+t))},_canRenderNextView:function(e){var t=e.getFullYear(),a=e.getMonth(),n=e.getDate(),l=this.options.max,s=l.getFullYear(),r=l.getMonth();return t<s||(t===s&&a<r||(t===s&&a===r&&n<l.getDate()||t===s&&a===r&&n===l.getDate()))},_initViews:function(t){var a,n,l=this,r=l.options,o=i.viewsEnum[t.viewName],c=i.views[o];for(l._current=new ne((+i.restrictValue(t.value,r.min,r.max))),l._views=[],l._index=o,a=l._getFirstViewDate(c),a.setDate(1),l._firstViewValue=new Date((+a)),n=0;n<r.views&&(a=n?s(a,c.name,1):a,a.setDate(1),l._canRenderNextView(a));n++)l._table=e(c.content(e.extend({min:r.min,max:r.max,date:a,url:r.url,dates:r.dates,format:r.format,culture:r.culture,disableDates:r.disableDates,showHeader:r.showViewHeader,isWeekColumnVisible:r.weekNumber,otherMonth:r.otherMonth,messages:r.messages},l[c.name]))),l._table.appendTo(l.tablesWrapper).addClass("k-"+c.name),l._views.push(l._table);l._currentView=c,l.tablesWrapper.attr("class","k-calendar-view k-calendar-"+c.name+"view"),l._updateHeader()},_rangeSelection:function(e){var t,a,n=this,l=e.sender.range();l.start&&(t=u(l.start.find("a"))),l.end&&(a=u(l.end.find("a"))),n._range={start:t,end:a},n._preventChange||n.trigger(F)},_selection:function(t){var a,n=this,l=t.sender.value(),s=t.event,r=e(s&&s.currentTarget),i=r.is("td");"single"===n.options.selectable&&n._validateValue(l[0]?u(l.first().find("a")):t.sender._lastActive?u(t.sender._lastActive.find("a")):n.value()),"multiple"==n.options.selectable&&(i&&(a=u(r.find("a"))),s&&s.ctrlKey?i?r.hasClass(E)?n._selectDates.push(a):n._deselect(a):(n.element.find("table "+K).each(function(t,a){var l=u(e(a).find("a"));n._deselect(l)}),n._addSelectedCellsToArray()):s&&s.shiftKey?n._selectRange(u(t.sender._lastActive?t.sender._lastActive.find("a"):l.first().find("a")),a):i?(n._selectDates=[],n._selectDates.push(a)):(n._selectDates=[],n._addSelectedCellsToArray())),n._preventChange||n.trigger(F)},_addSelectedCellsToArray:function(){var t=this;t.selectable.value().each(function(a,n){var l=u(e(n.firstChild));t.options.disableDates(l)||t._selectDates.push(l)})},_deselect:function(e){var t=this,a=t._selectDates.map(Number).indexOf(+e);a!=-1&&t._selectDates.splice(a,1)},_unselecting:function(e){var t=this,a=e.element;"single"===t.options.selectable&&!p&&a.hasClass(R)&&e.preventDefault()},_visibleRange:function(){var e=this.element.find(".k-calendar-view table"),t=u(e.first().find(z+":first").find("a")),a=u(e.last().find(z+":last").find("a"));return{start:t,end:a}},_dateInViews:function(e){var t=this,a=t.element.find(".k-calendar-view table"),n=u(a.first().find(z+":first").find("a")),l=u(a.last().find(z+":last").find("a"));return+e<=+l&&+e>=+n},_fillRange:function(e,t){var a,s=this;s._selectDates=[],a=l(e,t),n(s._selectDates,a,e,s.options.disableDates)},_selectRange:function(e,t){var a,n=this;+t<+e&&(a=t,t=e,e=a),n._fillRange(e,t),n._visualizeSelectedDatesInView()},_header:function(){var t,n=this,l=n.element,s=l.find(".k-calendar-header");s.length||(s=e('<div class="k-calendar-header"><a href="#" role="button" class="k-button k-title" aria-live="assertive" aria-atomic="true"></a><span class="k-calendar-nav"><a href="#" role="button" class="k-button k-button-icon k-prev-view" '+te+'="Previous"><span class="k-icon k-i-arrow-60-left"></span></a><a href="#" role="button" class="k-button k-button-icon k-next-view" '+te+'="Next"><span class="k-icon k-i-arrow-60-right"></span></a></span></div>').prependTo(l)),n.header=s,s.on(j+w+" "+Q+" "+q+w+" "+L+w,".k-button",a).on("click",function(){return!1}).on(k+w,".k-button.k-title",function(){n.navigateUp(),n._focusCell(n._cellByDate(n._current),!0),n.trigger(T)}).on(k+w,".k-button.k-prev-view",function(e){e.preventDefault(),n.navigateToPast(),n.trigger(T)}).on(k+w,".k-button.k-next-view",function(e){e.preventDefault(),n.navigateToFuture(),n.trigger(T)}),t=s.find(".k-button"),n._title=t.filter(".k-title"),n._navContainer=s.find(".k-calendar-nav"),n[J]=t.filter(".k-prev-view"),n[X]=t.filter(".k-next-view")},_wrapper:function(){this.tablesWrapper=e('<div class="k-calendar-view" />').insertAfter(this.element[0].firstChild)},_templates:function(){var e=this,t=e.options,a=t.month,n=a.content,l=a.weekNumber,s=a.empty;e.month={content:b('<td#=data.cssClass# role="gridcell"><a tabindex="-1" class="k-link#=data.linkClass#" href="#=data.url#" '+r.attr(Y)+'="#=data.dateString#" title="#=data.title#">'+(n||"#=data.value#")+"</a></td>",{useWithBlock:!!n}),empty:b('<td role="gridcell"'+(s?">":' class="k-out-of-range">')+(s||"<a class='k-link'></a>")+"</td>",{useWithBlock:!!s}),weekNumber:b('<td class="k-alt">'+(l||"#= data.weekNumber #")+"</td>",{useWithBlock:!!l})}},_footer:function(){var a=this,n=a.options,l=n.footer!==!1?r.template(a.options.footer||'#= kendo.toString(data,"D","'+n.culture+'") #',{useWithBlock:!1}):null,s=f(),i=a.element,o=i.find(".k-footer");return l?(o[0]||(o=e('<div class="k-footer"><a href="#" class="k-link k-nav-today"></a></div>').appendTo(i)),a._today=o.show().find(".k-link").html(l(s)).attr("title",r.toString(s,"D",a.options.culture)),a._toggle(),t):(a._toggle(!1),o.hide(),t)},_navigate:function(e,t,a){var n,l=this,s=l._index+1,r=new ne((+l._current)),o=new ne((+l._current));e=l[e],n=l._cellByDate(r).closest("table").index(),t>0?n=1-n:n+=1,e&&e.hasClass(W)||(s>3?r.setFullYear(r.getFullYear()+100*(t*n)):i.views[s].setDate(r,t*n),l.navigate(r),l._dateInViews(o)?(l._focusCell(l._cellByDate(o),!a),l._current=o):(s>3?o.setFullYear(o.getFullYear()+100*t):i.views[s].setDate(o,t),l._focusCell(l._cellByDate(o),!a),l._current=o))},_toggle:function(e){var a=this,n=a.options,l="range"!==n.selectable&&a.options.disableDates(f()),s=a._today;e===t&&(e=c(f(),n.min,n.max)),s&&(s.off(k+w),e&&!l?s.addClass(H).removeClass(W).on(k+w,ae(a._todayClick,a)):s.removeClass(H).addClass(W).on(k+w,function(e){e.preventDefault()}))},_click:function(e,t){var a=this,n=a.options,l=new Date((+a._current)),s=u(e);r.date.adjustDST(s,0),a._currentView.setDate(l,s),a._current=s,a._currentView.name!==n.depth?(a.navigateDown(i.restrictValue(l,n.min,n.max)),a._focusCell(a._cellByDate(a._current),!t),a.trigger(T)):a._focusCell(e.closest("td"),!t)},_blur:function(){var e=this;e._cell&&e._cell.removeClass(R)},_focus:function(t){var a=this,n=e(t.currentTarget),l=a._cell;l&&e.contains(n[0],l[0])||(l=n.find(K+":first")),a._focusCell(l)},_focusCell:function(e,t){var a=this,n=a._cellID,l=e.closest("table");a._cell&&a._cell.length&&(a._cell[0].removeAttribute(Z),a._cell[0].removeAttribute(te),a._cell.removeClass(R),a._cell[0].removeAttribute(C),a._cell.closest("table")[0].removeAttribute("aria-activedescendant")),a._cell=e,t&&l.trigger("focus"),n&&(e.attr(C,n),l.attr("aria-activedescendant",n)),e.attr(Z,!0).addClass(R),e.length&&"month"==a._currentView.name&&(a._current=u(e.find("a")))},_todayClick:function(e){var t=this,a=t.options.disableDates,n=f(),l=!1;e.preventDefault(),a(n)||(t._value=n,"multiple"===t.options.selectable&&(t._selectDates=[n]),"range"===t.options.selectable&&(t.rangeSelectable.clear(!0),t._range={start:n,end:null}),"month"==t._currentView.name&&t._dateInViews(n)||(l=!0),t.navigate(n,t.options.depth),"single"===t.options.selectable&&(t.selectable._lastActive=null),l&&t.trigger(T),t.trigger(F))},_validateValue:function(e){var a=this,n=a.options,l=n.min,s=n.max;return e=r.parseDate(e,n.format,n.culture),null!==e&&(e=new ne((+e)),c(e,l,s)||(e=null)),null!==e&&a.options.disableDates(new Date((+e)))?a._value===t&&(a._value=null):a._value=e,a._value},clearSelection:function(){var e=this;e.selectable&&e.element.find(V+E).removeClass(E),e.rangeSelectable&&e.rangeSelectable.clear(!0)},_restoreSelection:function(){var e,a=this,n=a.options.selectable;if(a._currentView.name===a.options.depth){if(a._preventChange=!0,"range"===n){if(e=a.selectRange(),!e||!e.start)return a._preventChange=!1,t;a.selectRange(e)}"single"===n&&a.value()&&a.selectable.value(a._cellByDate(a.value())),"multiple"===n&&a._visualizeSelectedDatesInView(),a._preventChange=!1}},value:function(e){var a,n=this;return e===t?n._value:(e=n._validateValue(e),n.clearSelection(),e&&!n._dateInViews(e)&&n.navigate(e),null!==e&&n._currentView.name===y&&(a=n._cellByDate(e),n.selectable&&n.selectable.value(a),n.rangeSelectable&&(n.rangeSelectable.start(a),n.rangeSelectable._lastActive=a)),t)},selectDates:function(a){var n,l,s=this;return a===t?s._selectDates:(l=a.map(function(e){return e.getTime()}).filter(function(e,t,a){return a.indexOf(e)===t}).map(function(e){return new Date(e)}),n=e.grep(l,function(e){if(e)return+s._validateValue(new Date(e.setHours(0,0,0,0)))===+e}),s._selectDates=n.length>0?n:0===l.length?l:s._selectDates,s._visualizeSelectedDatesInView(),t)},selectRange:function(e){var a,n,l,s=this;return e===t?s._range:(s._range=e,e.start&&(l=s._visibleRange(),a=s._dateInViews(e.start),n=e.end&&s._dateInViews(e.end),!a&&n&&s.rangeSelectable.selectTo(s._cellByDate(e.end)),a&&n&&s.rangeSelectable.range(s._cellByDate(e.start),s._cellByDate(e.end)),e.end&&a&&!n&&s.rangeSelectable.selectFrom(s._cellByDate(e.start)),!e.end&&a&&s.rangeSelectable.start(s._cellByDate(e.start)),+l.start>+e.start&&+l.end<+e.end&&s.rangeSelectable.mid(s.element.find(K))),t)}});r.ui.plugin(re)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,a){(a||t)()});
//# sourceMappingURL=kendo.multiviewcalendar.min.js.map
