/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.multiselect.min",["kendo.list.min","kendo.mobile.scroller.min","kendo.virtuallist.min"],e)}(function(){return function(e,t){function i(e,t){var i;if(null===e&&null!==t||null!==e&&null===t)return!1;if(i=e.length,i!==t.length)return!1;for(;i--;)if(e[i]!==t[i])return!1;return!0}var a=window.kendo,s=a.ui,n=s.List,l=e.extend({A:65},a.keys),o=a._activeElement,r=a.data.ObservableArray,c=e.proxy,u="id",d="li",p="accept",h="filter",_="rebind",f="open",g="close",m="change",v="progress",T="select",w="deselect",I="aria-disabled",b="k-state-focused",y="k-state-selected",S="k-hidden",V="k-state-hover",k="k-state-disabled",x="disabled",C="readonly",L=a.support.browser.chrome?"disabled":"off",D=".kendoMultiSelect",O="click"+D,E="keydown"+D,A="mouseenter"+D,B="mouseleave"+D,F=A+" "+B,M=/"/g,H=e.isArray,P=["font-family","font-size","font-stretch","font-style","font-weight","letter-spacing","text-transform","line-height"],K=n.extend({init:function(t,i){var s,l,o=this;o.ns=D,n.fn.init.call(o,t,i),o._optionsMap={},o._customOptions={},o._wrapper(),o._tagList(),o._input(),o._textContainer(),o._loader(),o._clearButton(),o._tabindex(o.input),t=o.element.attr("multiple","multiple").hide(),i=o.options,i.placeholder||(i.placeholder=t.data("placeholder")),s=t.attr(u),s&&(o._tagID=s+"_tag_active",s+="_taglist",o.tagList.attr(u,s),o.input.attr("aria-describedby",s)),o._initialOpen=!0,o._ariaLabel(),o._ariaSetLive(),o._dataSource(),o._ignoreCase(),o._popup(),o._tagTemplate(),o.requireValueMapper(o.options),o._initList(),o._reset(),o._enable(),o._placeholder(),i.autoBind?o.dataSource.fetch():i.value&&o._preselect(i.value),l=e(o.element).parents("fieldset").is(":disabled"),l&&o.enable(!1),o._ariaSetSize(o.value().length),a.notify(o),o._toggleCloseVisibility()},options:{name:"MultiSelect",tagMode:"multiple",enabled:!0,autoBind:!0,autoClose:!0,highlightFirst:!0,dataTextField:"",dataValueField:"",filter:"startswith",ignoreCase:!0,minLength:1,enforceMinLength:!1,delay:100,value:null,maxSelectedItems:null,placeholder:"",height:200,animation:{},virtual:!1,itemTemplate:"",tagTemplate:"",groupTemplate:"#:data#",fixedGroupTemplate:"#:data#",clearButton:!0,autoWidth:!1,popup:null},events:[f,g,m,T,w,"filtering","dataBinding","dataBound"],setDataSource:function(e){this.options.dataSource=e,this._state="",this._dataSource(),this.persistTagList=!1,this.listView.setDataSource(this.dataSource),this.options.autoBind&&this.dataSource.fetch()},setOptions:function(e){var t=this._listOptions(e);n.fn.setOptions.call(this,e),this.listView.setOptions(t),this._accessors(),this._aria(this.tagList.attr(u)),this._tagTemplate(),this._placeholder(),this._clearButton()},currentTag:function(e){var i=this;return e===t?i._currentTag:(i._currentTag&&(i._currentTag.removeClass(b).removeAttr(u),i._currentTag.find(".k-select").attr("aria-hidden",!0),i.input.removeAttr("aria-activedescendant")),e&&(e.addClass(b).attr(u,i._tagID),e.find(".k-select").removeAttr("aria-hidden"),i.input.attr("aria-activedescendant",i._tagID)),i._currentTag=e,t)},dataItems:function(){return this.listView.selectedDataItems()},destroy:function(){var e=this,t=e.ns;clearTimeout(e._busy),clearTimeout(e._typingTimeout),e.wrapper.off(t),e.tagList.off(t),e.input.off(t),e._clear.off(t),n.fn.destroy.call(e)},_activateItem:function(){this.popup.visible()&&n.fn._activateItem.call(this),this.currentTag(null)},_listOptions:function(t){var i=this,s=n.fn._listOptions.call(i,e.extend(t,{selectedItemChange:c(i._selectedItemChange,i),selectable:"multiple"})),l=this.options.itemTemplate||this.options.template,o=s.itemTemplate||l||s.template;return o||(o="#:"+a.expr(s.dataTextField,"data")+"#"),s.template=o,s},_setListValue:function(){n.fn._setListValue.call(this,this._initialValues.slice(0))},_listChange:function(e){var i,a=this.dataSource.flatView(),s=this._optionsMap,n=this._value;for(this._state===_&&(this._state=""),i=0;i<e.added.length;i++)if(s[n(e.added[i].dataItem)]===t){this._render(a);break}this._selectValue(e.added,e.removed)},_selectedItemChange:function(e){var t,i,a=e.items;for(i=0;i<a.length;i++)t=a[i],this.tagList.children().eq(t.index).children("span:first").html(this.tagTextTemplate(t.item))},_wrapperMousedown:function(t){var i=this,s="input"!==t.target.nodeName.toLowerCase(),n=e(t.target),l=n.hasClass("k-select")||n.hasClass("k-icon");l&&(l=!n.closest(".k-select").children(".k-i-arrow-60-down").length),!s||l&&a.support.mobileOS||!t.cancelable||t.preventDefault(),l||(i.input[0]!==o()&&s&&i.input.focus(),1===i.options.minLength&&i.open())},_inputFocus:function(){this._placeholder(!1),this.wrapper.addClass(b)},_inputFocusout:function(){var e=this;clearTimeout(e._typingTimeout),e.wrapper.removeClass(b),e._placeholder(!e.listView.selectedDataItems()[0],!0),e.close(),e._state===h&&(e._state=p,e.listView.skipUpdate(!0)),e.listView.bound()&&e.listView.isFiltered()&&(e.persistTagList=!0,e._clearFilter()),e.element.blur()},_removeTag:function(e,i){var a,s,n,l=this,o=l._state,r=e.index(),c=l.listView,u=c.value()[r],d=l.listView.selectedDataItems()[r],_=l._customOptions[u],f=c.element[0].children;return l.trigger(w,{dataItem:d,item:e})?(l._close(),t):(_!==t||o!==p&&o!==h||(_=l._optionsMap[u]),n=function(){l.currentTag(null),i&&l._change(),l._close()},_===t&&c.select().length?(l.persistTagList=!1,c.select(c.select()[r]).done(n)):(a=l.element[0].children[_],a&&(a.selected=!1),c.removeAt(r),s=f[_],s&&f[_].classList.remove("k-state-selected"),"single"!==l.options.tagMode?e.remove():l._updateTagListHTML(),n()),t)},_tagListClick:function(t){var i=e(t.currentTarget);i.children(".k-i-arrow-60-down").length||this._removeTag(i.closest(d),!0)},_clearClick:function(){var t=this;"single"===t.options.tagMode?t._clearSingleTagValue():t.tagList.children().each(function(i,a){t._removeTag(e(a),!1)}),t.input.val(""),t._search(),t._change(),t.focus(),t._hideClear(),t._state===h&&(t._state=p)},_clearSingleTagValue:function(){var e=this,t=e.persistTagList;t&&(e.persistTagList=!1),e.listView.value([]),e.persistTagList=t},_editable:function(t){var i=this,a=t.disable,s=t.readonly,n=i.wrapper.off(D),l=i.tagList.off(D),o=i.element.add(i.input.off(D));s||a?(a?n.addClass(k):n.removeClass(k),o.attr(x,a).attr(C,s).attr(I,a)):(n.removeClass(k).on(F,i._toggleHover).on("mousedown"+D+" touchend"+D,c(i._wrapperMousedown,i)),i.input.on(E,c(i._keydown,i)).on("paste"+D,c(i._search,i)).on("input"+D,c(i._search,i)).on("focus"+D,c(i._inputFocus,i)).on("focusout"+D,c(i._inputFocusout,i)),i._clear.on(O+D+" touchend"+D,c(i._clearClick,i)),o.removeAttr(x).removeAttr(C).attr(I,!1),l.on(A,d,function(){e(this).addClass(V)}).on(B,d,function(){e(this).removeClass(V)}).on(O,"li.k-button .k-select",c(i._tagListClick,i)))},_close:function(){var e=this;e.options.autoClose?e.close():e.popup.position()},_filterSource:function(e,t){t||(t=this._retrieveData),this._retrieveData=!1,n.fn._filterSource.call(this,e,t)},close:function(){this._activeItem=null,this.input.removeAttr("aria-activedescendant"),this.popup.close()},open:function(){var t=this;t._request&&(t._retrieveData=!1),t._retrieveData||!t.listView.bound()||t._state===p?(t._open=!0,t._state=_,t.listView.skipUpdate(!0),t.persistTagList=!(t._initialOpen&&!t.listView.bound()),t._filterSource(),t._focusItem()):t._allowOpening()&&(!t._initialOpen||t.options.autoBind||t.options.virtual||!t.options.value||e.isPlainObject(t.options.value[0])||t.value(t._initialValues),t.popup._hovered=!0,t._initialOpen=!1,t.popup.open(),t._focusItem())},toggle:function(e){e=e!==t?e:!this.popup.visible(),this[e?f:g]()},refresh:function(){this.listView.refresh()},_listBound:function(){var e=this,i=e.dataSource.flatView(),a=e.listView.skip();e._render(i),e._renderFooter(),e._renderNoData(),e._toggleNoData(!i.length),e._resizePopup(),e._open&&(e._open=!1,e.toggle(e._allowOpening())),e.popup.position(),!e.options.highlightFirst||a!==t&&0!==a||e.listView.focusFirst(),e._touchScroller&&e._touchScroller.reset(),e._hideBusy(),e._makeUnselectable(),e.trigger("dataBound")},_inputValue:function(){var e=this,t=e.input.val();return e.options.placeholder===t&&(t=""),t},value:function(e){var i=this,a=i.listView,s=a.value().slice(),n=i.options.maxSelectedItems,l=a.bound()&&a.isFiltered();return e===t?s:(i.persistTagList=!1,i.requireValueMapper(i.options,e),e=i._normalizeValues(e),null!==n&&e.length>n&&(e=e.slice(0,n)),l&&i._clearFilter(),a.value(e),i._old=i._valueBeforeCascade=e.slice(),l||i._fetchData(),i._ariaSetSize(i.value().length),i._toggleCloseVisibility(),t)},_preselect:function(t,i){var s=this;H(t)||t instanceof a.data.ObservableArray||(t=[t]),(e.isPlainObject(t[0])||t[0]instanceof a.data.ObservableObject||!s.options.dataValueField)&&(s.dataSource.data(t),s.value(i||s._initialValues),s._retrieveData=!0)},_setOption:function(e,t){var i=this.element[0].children[this._optionsMap[e]];i&&(i.selected=t)},_fetchData:function(){var e=this,t=!!e.dataSource.view().length,i=0===e.listView.value().length;i||e._request||(e._retrieveData||!e._fetch&&!t)&&(e._fetch=!0,e._retrieveData=!1,e.dataSource.read().done(function(){e._fetch=!1}))},_isBound:function(){return this.listView.bound()&&!this._retrieveData},_dataSource:function(){var e=this,t=e.element,i=e.options,s=i.dataSource||{};s=H(s)?{data:s}:s,s.select=t,s.fields=[{field:i.dataTextField},{field:i.dataValueField}],e.dataSource&&e._refreshHandler?e._unbindDataSource():(e._progressHandler=c(e._showBusy,e),e._errorHandler=c(e._hideBusy,e)),e.dataSource=a.data.DataSource.create(s).bind(v,e._progressHandler).bind("error",e._errorHandler)},_reset:function(){var t=this,i=t.element,a=i.attr("form"),s=a?e("#"+a):i.closest("form");s[0]&&(t._resetHandler=function(){setTimeout(function(){t.value(t._initialValues),t._placeholder()})},t._form=s.on("reset",t._resetHandler))},_initValue:function(){var e=this.options.value||this.element.val();this._old=this._initialValues=this._normalizeValues(e)},_normalizeValues:function(t){var i=this;return null===t?t=[]:t&&e.isPlainObject(t)?t=[i._value(t)]:t&&e.isPlainObject(t[0])?t=e.map(t,function(e){return i._value(e)}):H(t)||t instanceof r?H(t)&&(t=t.slice()):t=[t],t},_change:function(){var e=this,t=e.value();i(t,e._old)||(e._old=t.slice(),e.trigger(m),e.element.trigger(m)),e.popup.position(),e._ariaSetSize(t.length),e._toggleCloseVisibility()},_click:function(e){var t=this,i=e.item;e.preventDefault(),t._select(i).done(function(){t._activeItem=i,t._change(),t._close()})},_getActiveItem:function(){return this._activeItem||e(this.listView.items()[this._getSelectedIndices().length-1])||this.listView.focus()},_getSelectedIndices:function(){return this.listView._selectedIndices||this.listView._selectedIndexes},_keydown:function(i){var s,n,o,r,c=this,u=i.keyCode,d=c._currentTag,h=c.listView,_=c.input.val(),f=a.support.isRtl(c.wrapper),g=c.popup.visible(),m=0;if(u!==l.ENTER&&(this._multipleSelection=!1),u===l.DOWN){if(i.preventDefault(),!g)return c.open(),h.focus()||h.focusFirst(),t;h.focus()?(!c._activeItem&&i.shiftKey&&(c._activeItem=h.focus(),m=-1),s=h.getElementIndex(c._getActiveItem().first()),h.focusNext(),h.focus()?i.shiftKey&&(this._multipleSelection=!0,c._selectRange(s,h.getElementIndex(h.focus().first())+m)):h.focusLast()):h.focusFirst()}else if(u===l.UP)g&&(!c._activeItem&&i.shiftKey&&(c._activeItem=h.focus(),m=1),s=h.getElementIndex(c._getActiveItem().first()),h.focusPrev(),h.focus()?i.shiftKey&&(this._multipleSelection=!0,c._selectRange(s,h.getElementIndex(h.focus().first())+m)):c.close()),i.preventDefault();else if(u===l.LEFT&&!f||u===l.RIGHT&&f)_||(d=d?d.prev():e(c.tagList[0].lastChild),d[0]&&c.currentTag(d));else if(u===l.RIGHT&&!f||u===l.LEFT&&f)!_&&d&&(d=d.next(),c.currentTag(d[0]?d:null));else if(i.ctrlKey&&!i.altKey&&u===l.A&&g&&!c.options.virtual)this._multipleSelection=!0,this._getSelectedIndices().length===h.items().length&&(c._activeItem=null),h.items().length&&c._selectRange(0,h.items().length-1);else if(u===l.ENTER&&g){if(!h.focus())return;if(i.preventDefault(),this._multipleSelection&&(this._multipleSelection=!1,h.focus().hasClass(y)))return c._close(),t;c._select(h.focus()).done(function(){c._change(),c._close()})}else if(u===l.SPACEBAR&&i.ctrlKey&&g)c._activeItem&&h.focus()&&h.focus()[0]===c._activeItem[0]&&(c._activeItem=null),e(h.focus()).hasClass(y)||(c._activeItem=h.focus()),c._select(h.focus()).done(function(){c._change()}),i.preventDefault();else if(u===l.SPACEBAR&&i.shiftKey&&g)n=h.getElementIndex(c._getActiveItem()),o=h.getElementIndex(h.focus()),n!==t&&o!==t&&c._selectRange(n,o),i.preventDefault();else if(u===l.ESC)g?i.preventDefault():(c.tagList.children().each(function(t,i){c._removeTag(e(i),!1)}),c._change()),c.close();else if(u===l.HOME)g?h.focus()?(i.ctrlKey&&i.shiftKey&&!c.options.virtual&&c._selectRange(h.getElementIndex(h.focus()[0]),0),h.focusFirst()):c.close():_||(d=c.tagList[0].firstChild,d&&c.currentTag(e(d)));else if(u===l.END)g?h.focus()?(i.ctrlKey&&i.shiftKey&&!c.options.virtual&&c._selectRange(h.getElementIndex(h.focus()[0]),h.element.children().length-1),h.focusLast()):c.close():_||(d=c.tagList[0].lastChild,d&&c.currentTag(e(d)));else if(u!==l.DELETE&&u!==l.BACKSPACE||_)!c.popup.visible()||u!==l.PAGEDOWN&&u!==l.PAGEUP?(clearTimeout(c._typingTimeout),setTimeout(function(){c._scale()}),c._search()):(i.preventDefault(),r=u===l.PAGEDOWN?1:-1,h.scrollWith(r*h.screenHeight()));else{if(c._state=p,"single"===c.options.tagMode)return c._clearSingleTagValue(),c._change(),c._close(),t;u!==l.BACKSPACE||d||(d=e(c.tagList[0].lastChild)),d&&d[0]&&c._removeTag(d,!0)}},_hideBusy:function(){var e=this;clearTimeout(e._busy),e.input.attr("aria-busy",!1),e._loading.addClass(S),e._request=!1,e._busy=null,e._toggleCloseVisibility()},_showBusyHandler:function(){this.input.attr("aria-busy",!0),this._loading.removeClass(S),this._hideClear()},_showBusy:function(){var e=this;e._request=!0,e._busy||(e._busy=setTimeout(c(e._showBusyHandler,e),100))},_placeholder:function(e,i){var s=this,n=s.input,l=o(),r=s.options.placeholder,c=n.val(),u=n[0]===l,d=c.length;u&&!s.options.autoClose&&c!==r||(d=0,c=""),e===t&&(e=!1,n[0]!==l&&(e=!s.listView.selectedDataItems()[0])),s._prev=c,n.toggleClass("k-readonly",e).val(e?r:c),u&&!i&&a.caret(n[0],d,d),s._scale()},_scale:function(){var e,t=this,i=t.wrapper.find(".k-multiselect-wrap"),a=i.width(),s=t._span.text(t.input.val());i.is(":visible")?e=s.width()+25:(s.appendTo(document.documentElement),a=e=s.width()+25,s.appendTo(i)),t.input.width(e>a?a:e)},_option:function(e,i,s){var n="<option";return e!==t&&(e+="",e.indexOf('"')!==-1&&(e=e.replace(M,"&quot;")),n+=' value="'+e+'"'),s&&(n+=" selected"),n+=">",i!==t&&(n+=a.htmlEncode(i)),n+="</option>"},_render:function(e){var t,i,a,s,n,l,o=this.listView.selectedDataItems(),r=this.listView.value(),c=e.length,u="";for(r.length!==o.length&&(o=this._buildSelectedItems(r)),n={},l={},s=0;s<c;s++)i=e[s],a=this._value(i),t=this._selectedItemIndex(a,o),t!==-1&&o.splice(t,1),l[a]=s,u+=this._option(a,this._text(i),t!==-1);if(o.length)for(s=0;s<o.length;s++)i=o[s],a=this._value(i),n[a]=c,l[a]=c,c+=1,u+=this._option(a,this._text(i),!0);this._customOptions=n,this._optionsMap=l,this.element.html(u)},_buildSelectedItems:function(e){var t,i,a=this.options.dataValueField,s=this.options.dataTextField,n=[];for(i=0;i<e.length;i++)t={},t[a]=e[i],t[s]=e[i],n.push(t);return n},_selectedItemIndex:function(e,t){for(var i=this._value,a=0;a<t.length;a++)if(e===i(t[a]))return a;return-1},_search:function(){var e=this;clearTimeout(e._typingTimeout),e._typingTimeout=setTimeout(function(){var t=e._inputValue();e._prev!==t&&(e._prev=t,e.search(t),e._toggleCloseVisibility())},e.options.delay)},_toggleCloseVisibility:function(){this.value().length||this.input.val()&&this.input.val()!==this.options.placeholder?this._showClear():this._hideClear()},_allowOpening:function(){return this._allowSelection()&&n.fn._allowOpening.call(this)},_allowSelection:function(){var e=this.options.maxSelectedItems;return null===e||e>this.listView.value().length},_angularTagItems:function(t){var i=this;i.angular(t,function(){return{elements:i.tagList[0].children,data:e.map(i.dataItems(),function(e){return{dataItem:e}})}})},updatePersistTagList:function(e,t){this.persistTagList.added&&this.persistTagList.added.length===t.length&&this.persistTagList.removed&&this.persistTagList.removed.length===e.length?this.persistTagList=!1:(this.listView._removedAddedIndexes=this._old.slice(),this.persistTagList={added:e,removed:t})},_selectValue:function(e,i){var a,s,n,l=this,o=l.dataSource.total(),r=l.tagList,c=l._value;if(this.persistTagList)return this.updatePersistTagList(e,i),t;if(l._angularTagItems("cleanup"),"multiple"===l.options.tagMode){for(n=i.length-1;n>-1;n--)a=i[n],r.children().length&&(r[0].removeChild(r[0].children[a.position]),l._setOption(c(a.dataItem),!1));for(n=0;n<e.length;n++)s=e[n],r.append(l.tagTemplate(s.dataItem)),l._setOption(c(s.dataItem),!0)}else{for((!l._maxTotal||l._maxTotal<o)&&(l._maxTotal=o),this._updateTagListHTML(),n=i.length-1;n>-1;n--)l._setOption(c(i[n].dataItem),!1);for(n=0;n<e.length;n++)l._setOption(c(e[n].dataItem),!0)}l._angularTagItems("compile"),l._placeholder()},_updateTagListHTML:function(){var e=this,t=e.value(),i=e.dataSource.total(),a=e.tagList;a.html(""),t.length&&a.append(e.tagTemplate({values:t,dataItems:e.dataItems(),maxTotal:e._maxTotal,currentTotal:i}))},_select:function(t){var i,a,s,n,l=e.Deferred().resolve();return t?(i=this,a=i.listView,s=a.dataItemByIndex(a.getElementIndex(t)),n=t.hasClass("k-state-selected"),i._state===_&&(i._state=""),i._allowSelection()||n?i.trigger(n?w:T,{dataItem:s,item:t})?(i._close(),l):(i.persistTagList=!1,a.select(t).done(function(){i._placeholder(),i._state===h&&(i._state=p,a.skipUpdate(!0))})):l):l},_selectRange:function(i,a){var s,n,l=this,o=this.listView,r=this.options.maxSelectedItems,c=this._getSelectedIndices().slice(),u=[],d=function(t){o.select(t).done(function(){t.forEach(function(t){var i=o.dataItemByIndex(t),a=o.element.children()[t],s=e(a).hasClass("k-state-selected");l.trigger(s?T:w,{dataItem:i,item:e(a)})}),l._change()})};if(c.length-1===a-i)return d(c);if(i<a)for(s=i;s<=a;s++)u.push(s);else for(s=i;s>=a;s--)u.push(s);for(null!==r&&u.length>r&&(u=u.slice(0,r)),s=0;s<u.length;s++)n=u[s],this._getSelectedIndices().indexOf(n)==-1?c.push(n):c.splice(c.indexOf(n),1);return c.length?(l.persistTagList=!1,d(c)):t},_input:function(){var t=this,i=t.element,a=i[0].accessKey,s=t._innerWrapper.children("input.k-input");s[0]||(s=e('<input class="k-input" style="width: 25px" />').appendTo(t._innerWrapper)),i.removeAttr("accesskey"),t._focused=t.input=s.attr({accesskey:a,autocomplete:L,role:"listbox",title:i[0].title,"aria-expanded":!1,"aria-haspopup":"listbox","aria-autocomplete":"list"})},_tagList:function(){var t=this,i=t._innerWrapper.children("ul");i[0]||(i=e('<ul unselectable="on" class="k-reset"/>').appendTo(t._innerWrapper)),t.tagList=i},_tagTemplate:function(){var e,t=this,i=t.options,s=i.tagTemplate,n=i.dataSource,l="multiple"===i.tagMode;t.element[0].length&&!n&&(i.dataTextField=i.dataTextField||"text",i.dataValueField=i.dataValueField||"value"),e=l?a.template("#:"+a.expr(i.dataTextField,"data")+"#",{useWithBlock:!1}):a.template("#:values.length# item(s) selected"),t.tagTextTemplate=s=s?a.template(s):e,t.tagTemplate=function(e){return'<li role="option" aria-selected="true" class="k-button" unselectable="on"><span unselectable="on">'+s(e)+'</span><span aria-hidden="true" unselectable="on" aria-label="'+(l?"delete":"open")+'" class="k-select"><span class="k-icon '+(l?"k-i-close":"k-i-arrow-60-down")+'"></span></span></li>'}},_loader:function(){this._loading=e('<span class="k-icon k-i-loading '+S+'"></span>').insertAfter(this.input)},_clearButton:function(){n.fn._clearButton.call(this),this.options.clearButton&&(this._clear.insertAfter(this.input),this.wrapper.addClass("k-multiselect-clearable"))},_textContainer:function(){var t=a.getComputedStyles(this.input[0],P);t.position="absolute",t.visibility="hidden",t.top=-3333,t.left=-3333,this._span=e("<span/>").css(t).appendTo(this.wrapper)},_wrapper:function(){var t=this,i=t.element,a=i.parent("span.k-multiselect");a[0]||(a=i.wrap('<div class="k-widget k-multiselect" unselectable="on" />').parent(),a[0].style.cssText=i[0].style.cssText,a[0].title=i[0].title,e('<div class="k-multiselect-wrap k-floatwrap" role="listbox" unselectable="on" />').insertBefore(i)),t.wrapper=a.addClass(i[0].className).css("display",""),t._innerWrapper=e(a[0].firstChild)},_ariaSetSize:function(e){var t=this,i=t.tagList.children();e&&i.length&&i.attr("aria-setsize",e)},_ariaSetLive:function(){var e=this;e.ul.attr("aria-live",e._isFilterEnabled()?"polite":"off")}});s.plugin(K)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()});
//# sourceMappingURL=kendo.multiselect.min.js.map
