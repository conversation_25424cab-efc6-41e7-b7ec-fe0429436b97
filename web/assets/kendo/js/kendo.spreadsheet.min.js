/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("util/undoredostack.min",["kendo.core.min"],e)}(function(){!function(e){var t=e.Observable.extend({init:function(t){e.Observable.fn.init.call(this,t),this.clear()},events:["undo","redo"],push:function(e){this.stack=this.stack.slice(0,this.currentCommandIndex+1),this.currentCommandIndex=this.stack.push(e)-1},undo:function(){if(this.canUndo()){var e=this.stack[this.currentCommandIndex--];e.undo(),this.trigger("undo",{command:e})}},redo:function(){if(this.canRedo()){var e=this.stack[++this.currentCommandIndex];e.redo(),this.trigger("redo",{command:e})}},clear:function(){this.stack=[],this.currentCommandIndex=-1},canUndo:function(){return this.currentCommandIndex>=0},canRedo:function(){return this.currentCommandIndex!=this.stack.length-1}});e.deepExtend(e,{util:{UndoRedoStack:t}})}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("util/text-metrics.min",["kendo.core.min"],e)}(function(){!function(e){function t(e){return(e+"").replace(a,l)}function n(e){var t,n=[];for(t in e)n.push(t+e[t]);return n.sort().join("")}function i(e){var t,n=2166136261;for(t=0;t<e.length;++t)n+=(n<<1)+(n<<4)+(n<<7)+(n<<8)+(n<<24),n^=e.charCodeAt(t);return n>>>0}function r(){return{width:0,height:0,baseline:0}}function o(e,t,n){return h.current.measure(e,t,n)}var s,a,l,u,c,h;window.kendo.util=window.kendo.util||{},s=kendo.Class.extend({init:function(e){this._size=e,this._length=0,this._map={}},put:function(e,t){var n=this._map,i={key:e,value:t};n[e]=i,this._head?(this._tail.newer=i,i.older=this._tail,this._tail=i):this._head=this._tail=i,this._length>=this._size?(n[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(e){var t=this._map[e];if(t)return t===this._head&&t!==this._tail&&(this._head=t.newer,this._head.older=null),t!==this._tail&&(t.older&&(t.older.newer=t.newer,t.newer.older=t.older),t.older=this._tail,t.newer=null,this._tail.newer=t,this._tail=t),t.value}}),a=/\r?\n|\r|\t/g,l=" ",u={baselineMarkerSize:1},"undefined"!=typeof document&&(c=document.createElement("div"),c.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),h=kendo.Class.extend({init:function(t){this._cache=new s(1e3),this.options=e.extend({},u,t)},measure:function(e,o,s){var a,l,u,h,d,f,p,m,g;if(void 0===s&&(s={}),!e)return r();if(a=n(o),l=i(e+a),u=this._cache.get(l))return u;h=r(),d=s.box||c,f=this._baselineMarker().cloneNode(!1);for(p in o)m=o[p],void 0!==m&&(d.style[p]=m);return g=s.normalizeText!==!1?t(e):e+"",d.textContent=g,d.appendChild(f),document.body.appendChild(d),g.length&&(h.width=d.offsetWidth-this.options.baselineMarkerSize,h.height=d.offsetHeight,h.baseline=f.offsetTop+this.options.baselineMarkerSize),h.width>0&&h.height>0&&this._cache.put(l,h),d.parentNode.removeChild(d),h},_baselineMarker:function(){var e=document.createElement("div");return e.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",e}}),h.current=new h,kendo.deepExtend(kendo.util,{LRUCache:s,TextMetrics:h,measureText:o,objectKey:n,hashKey:i,normalizeText:t})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("util/main.min",["kendo.core.min"],e)}(function(){return function(){function e(e){return e*e}function t(e){return"string"!=typeof e&&(e+="px"),e}function n(e){var t,n,i=[];if(e)for(t=u.toHyphens(e).split("-"),n=0;n<t.length;n++)i.push("k-pos-"+t[n]);return i.join(" ")}function i(e){for(var t={1:"i",10:"x",100:"c",2:"ii",20:"xx",200:"cc",3:"iii",30:"xxx",300:"ccc",4:"iv",40:"xl",400:"cd",5:"v",50:"l",500:"d",6:"vi",60:"lx",600:"dc",7:"vii",70:"lxx",700:"dcc",8:"viii",80:"lxxx",800:"dccc",9:"ix",90:"xc",900:"cm",1e3:"m"},n=[1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],i="";e>0;)e<n[0]?n.shift():(i+=t[n[0]],e-=n[0]);return i}function r(e){var t,n,i,r,o;for(e=e.toLowerCase(),t={i:1,v:5,x:10,l:50,c:100,d:500,m:1e3},n=0,i=0,r=0;r<e.length;++r){if(o=t[e.charAt(r)],!o)return null;n+=o,o>i&&(n-=2*i),i=o}return n}function o(e){var t=Object.create(null);return function(){var n,i="";for(n=arguments.length;--n>=0;)i+=":"+arguments[n];return i in t?t[i]:t[i]=e.apply(this,arguments)}}function s(e){return l.test(e)}function a(e,t){function n(e){this.value=e}try{return e.call(t,function(e){throw new n(e)})}catch(i){if(i instanceof n)return i.value;throw i}}var l,u=window.kendo,c=u.deepExtend,h=Date.now;h||(h=function(){return(new Date).getTime()}),c(u,{util:{now:h,renderPos:n,renderSize:t,sqr:e,romanToArabic:r,arabicToRoman:i,memoize:o,isUnicodeLetter:s,withExit:a}}),l=RegExp("[\\u0041-\\u005A\\u0061-\\u007A\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0-\\u08B2\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58\\u0C59\\u0C60\\u0C61\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D60\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19C1-\\u19C7\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA7AD\\uA7B0\\uA7B1\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB5F\\uAB64\\uAB65\\uABC0-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]")}(),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("util/parse-xml.min",["kendo.core.min","util/main.min"],e)}(function(){"use strict";function e(e){var t,n=[];for(t=0;t<e.length;++t)n.push(e.charCodeAt(t));return n}function t(e,t){t>65535?(t-=65536,e.push(t>>>10&1023|55296,56320|1023&t)):e.push(t)}function n(e,n){function C(n){var i=e[j++];240&i^240?224&i^224?192&i^192?n.push(i):t(n,(31&i)<<6|63&e[j++]):t(n,(15&i)<<12|(63&e[j++])<<6|63&e[j++]):t(n,(3&i)<<18|(63&e[j++])<<12|(63&e[j++])<<6|63&e[j++])}function F(e){throw Error(e+", at "+j)}function R(t){for(var n=[];j<e.length&&t(e[j]);)n.push(e[j++]);return n}function S(e){return i.apply(0,R(e))}function A(){R(B)}function D(t){var n,i=j;for(n=0;n<t.length;++n)if(e[j++]!=t[n])return j=i,!1;return t}function E(e){D(e)||F("Expecting "+e.join(", "))}function B(e){return 9==e||10==e||13==e||32==e}function M(e){return e>=48&&e<=57}function T(e){return e>=48&&e<=57||(e|=32)>=97&&e<=102}function I(e){return 58==e||95==e||(e|=32)>=97&&e<=122}function L(e){return 45==e||M(e)||I(e)}function N(){for(var t=[];j<e.length;){if(D(a))return W("comment",i.apply(0,t));C(t)}}function z(){var e,t;D(d)?V():D(l)?N():(e=H(),t=U(e),q.push(t),D(u)?W("enter",e,t,!0):(E(p),W("enter",e,t),P(e),e!=H()&&F("Bad closing tag"),W("leave",e,t),A(),E(p)),q.pop())}function P(t){for(var n=[];j<e.length;){if(D(c))return n.length&&W("text",i.apply(0,n));if(D(o))for(;j<e.length&&!D(s);)C(n);else D(f)?(n.length&&W("text",i.apply(0,n)),z(),n=[]):D(v)?$(n):C(n)}F("Unclosed tag "+t)}function H(){return I(e[j])||F("Expecting XML name"),S(L)}function O(){var t,n=D(b)||D(w);for(n||F("Expecting string"),t=[];j<e.length;){if(D(n))return i.apply(0,t);D(v)?$(t):C(t)}F("Unfinished string")}function $(e){var n,i;D(y)?(n=D(_)||D(k)?parseInt(S(T),16):parseInt(S(M),10),isNaN(n)&&F("Bad numeric entity")):(i=H(),n=r[i],void 0===n&&F("Unknown entity "+i)),t(e,n),E(m)}function V(){W("decl",H(),U()),E(h)}function U(t){for(var n,i={$tag:t};j<e.length&&(A(),n=e[j],63!=n&&62!=n&&47!=n);)i[H()]=(E(g),O());return i}function W(e,t,i,r){var o=n&&n[e];o&&o.call(K,t,i,r)}var j=0,q=[],K={is:function(e){for(var t=q.length,n=e.length;--t>=0&&--n>=0;)if(q[t].$tag!=e[n]&&"*"!=e[n])return!1;return n<0?q[q.length-1]:null},exit:function(){throw x},stack:q},J=[];for(C(J),65279!=J[0]&&(j=0);j<e.length;)A(),E(f),z(),A()}var i=String.fromCharCode,r={amp:38,lt:60,gt:62,quot:34,apos:39,nbsp:160},o=e("<![CDATA["),s=e("]]>"),a=e("-->"),l=e("!--"),u=e("/>"),c=e("</"),h=e("?>"),d=e("?"),f=e("<"),p=e(">"),m=e(";"),g=e("="),v=e("&"),b=e('"'),w=e("'"),y=e("#"),_=e("x"),k=e("X"),x={};kendo.util.parseXML=function(){try{return n.apply(this,arguments)}catch(e){if(e!==x)throw e}}},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/commands.min",["kendo.core.min","kendo.binder.min","kendo.window.min","kendo.list.min","kendo.tabstrip.min"],e)}(function(){!function(e){function t(e){var t=document.createElement("textarea");n(t).addClass("k-spreadsheet-clipboard").val(e).appendTo(document.body).focus().select(),document.execCommand("copy"),n(t).remove()}var n,i,r,o,s,a,l,u,c,h,d;e.support.browser.msie&&e.support.browser.version<9||(n=e.jQuery,i={AUTO_FILL:"autoFill",CLEAR:"clear",CUT:"cut",EDIT:"edit",PASTE:"paste",VALIDATION:"validation"},r=e.spreadsheet.Command=e.Class.extend({init:function(e){this.options=e,this._workbook=e.workbook,this._property=e&&e.property,this._state={}},range:function(e){return void 0!==e&&this._setRange(e),this._range},_setRange:function(e){this._range=e},redo:function(){this.range().select(),this.exec()},undo:function(){this.setState(this._state)},getState:function(){this._state=this.range().getState(this._property)},setState:function(e){this.range().setState(e)},rejectState:function(e){return this.undo(),{title:e.title,body:e.message,reason:"error",type:"validationError"}},_forEachCell:function(e){var t=this.range(),n=t._ref;n.forEach(function(n){t.sheet().forEach(n.toRangeRef(),e.bind(this))}.bind(this))},usesImage:function(){return!1}}),e.spreadsheet.DrawingUpdateCommand=r.extend({init:function(e){this._sheet=e.sheet,this._drawing=e.drawing,this._orig=this._drawing.clone(),this._previous=e.previous},exec:function(){},undo:function(){this._drawing.reset(this._previous),this._sheet._activeDrawing=this._drawing,this._sheet.triggerChange({layout:!0})},redo:function(){this._drawing.reset(this._orig),this._sheet._activeDrawing=this._drawing,this._sheet.triggerChange({layout:!0})},usesImage:function(e){return this._drawing.image===e||this._orig.image===e||this._previous.image===e}}),o=r.extend({init:function(e){r.fn.init.call(this,e),this._drawing=e.drawing},usesImage:function(e){return this._drawing.image===e}}),e.spreadsheet.InsertImageCommand=o.extend({init:function(e){o.fn.init.call(this,e),this._blob=e.blob,this._width=e.width,this._height=e.height},exec:function(){var e=this.range(),t=e.sheet(),n=this._width,i=this._height,r=n/i;n>i?(n=Math.min(n,300),i=n/r):(i=Math.min(i,300),n=i*r),this._drawing=t.addDrawing({topLeftCell:e.topLeft(),offsetX:5,offsetY:5,width:n,height:i,opacity:1,image:this._workbook.addImage(this._blob)},!0),this._blob=null},undo:function(){var e=this.range().sheet();e._activeDrawing=null,e.removeDrawing(this._drawing)},redo:function(){var e=this.range().sheet();e._activeDrawing=this._drawing,e.addDrawing(this._drawing)}}),e.spreadsheet.DeleteDrawingCommand=o.extend({exec:function(){var e=this.range().sheet();e._activeDrawing=null,e.removeDrawing(this._drawing)},undo:function(){var e=this.range().sheet();e._activeDrawing=this._drawing,e.addDrawing(this._drawing)},redo:function(){this.exec()}}),s=o.extend({exec:function(){var e=this.range().sheet();this._origIndex=e._drawings.indexOf(this._drawing),e._drawings.splice(this._origIndex,1),this._newIndex=this._reorder(),e._drawings.splice(this._newIndex,0,this._drawing),e.triggerChange({drawings:!0})},undo:function(){var e=this.range().sheet();e._drawings.splice(this._newIndex,1),e._drawings.splice(this._origIndex,0,this._drawing),e.triggerChange({drawings:!0})}}),e.spreadsheet.BringToFrontCommand=s.extend({_reorder:function(){return this.range().sheet()._drawings.length}}),e.spreadsheet.SendToBackCommand=s.extend({_reorder:function(){return 0}}),a=r.extend({init:function(e){r.fn.init.call(this,e),this._target=e.target,this._value=e.value},exec:function(){this.getState(),this.setState(this._value)}}),e.spreadsheet.ColumnWidthCommand=a.extend({getState:function(){this._state=this.range().sheet().columnWidth(this._target)},setState:function(e){this.range().sheet().columnWidth(this._target,e)}}),e.spreadsheet.RowHeightCommand=a.extend({getState:function(){this._state=this.range().sheet().rowHeight(this._target)},setState:function(e){this.range().sheet().rowHeight(this._target,e)}}),e.spreadsheet.HyperlinkCommand=r.extend({init:function(e){r.fn.init.call(this,e),this._link=e.link},exec:function(){var e=this.range();this._prevLink=e.link(),this._prevUnderline=e.underline(),e.link(this._link),e.underline(!0),null==e.value()&&(this._hasSetValue=!0,e.value(this._link))},undo:function(){var e=this.range();e.link(this._prevLink),e.underline(this._prevUnderline),this._hasSetValue&&e.value(null)}}),e.spreadsheet.GridLinesChangeCommand=a.extend({getState:function(){this._state=this._range.sheet().showGridLines()},setState:function(e){this._range.sheet().showGridLines(e)}}),l=e.spreadsheet.PropertyChangeCommand=r.extend({_setRange:function(e){r.prototype._setRange.call(this,e.skipHiddenCells())},init:function(e){r.fn.init.call(this,e),this._value=e.value},exec:function(){var e=this.range();e.enable()&&(this.getState(),"format"===this.options.property&&this._workbook.trigger("changeFormat",{range:e}),e[this._property](this._value))}}),e.spreadsheet.ClearContentCommand=r.extend({exec:function(){var e,t,n,r,o,s=[];return this.range().enable()?this.range().canEditArrayFormula()?(this.getState(),e=this.range().skipHiddenCells(),e._ref.refs&&e._ref.refs.length>1?e._ref.refs.forEach(function(t){o=e.sheet().range(t),s=s.concat(o.values())}):s=e.values(),n=[],s.forEach(function(e){t=[],e.forEach(function(){t.push(null)}),n.push(t)}),e.sheet().trigger("changing",{data:n,range:e,changeType:i.CLEAR})?void 0:(e.clearContent(),r=e._getValidationState(),r?this.rejectState(r):void 0)):{reason:"error",type:"intersectsArray"}:{reason:"error",type:"cannotModifyDisabled"}},undo:function(){var e,t=this.range().skipHiddenCells(),n=t.sheet(),r=this._state.data,o=[];r.forEach(function(t){e=[],t.forEach(function(t){e.push(t.value)}),o.push(e)}),n.trigger("changing",{data:o,range:t,changeType:i.CLEAR})||this.setState(this._state)}}),e.spreadsheet.EditCommand=l.extend({init:function(e){e.property=e.property||"input",l.fn.init.call(this,e)},_setRange:function(e){l.prototype._setRange.apply(this,arguments),this._editRange=this.options.arrayFormula?e:e.sheet().activeCellSelection()},getState:function(){this._state=this.range().getState()},exec:function(){return this.range().sheet().withCultureDecimals(this._exec.bind(this))},undo:function(){var e=this._editRange,t=this._state;e.sheet().trigger("changing",{data:t.data[0][0].value,range:e,changeType:i.EDIT})||this.setState(this._state)},_exec:function(){var t,n,r=this.options.arrayFormula,o=this._editRange;if(!o.enable())return{reason:"error",type:"rangeDisabled"};if(!o.canEditArrayFormula())return{reason:"error",type:"intersectsArray"};if(t=this._value,this.getState(),!this.range().sheet().trigger("changing",{data:t,range:this._editRange,changeType:i.EDIT})){if("value"==this._property)return void o.value(t);try{if(o.link(null),""===t?o.value(null):(o.input(t,{arrayFormula:r}),/\n/.test(o.value())&&o.wrap(!0)),o._adjustRowHeight(),n=o._getValidationState())return this.rejectState(n)}catch(s){if(s instanceof e.spreadsheet.calc.ParseError)return{title:"Error in formula",body:s+"",reason:"error"};throw s}}}}),e.spreadsheet.InsertCommentCommand=l.extend({init:function(e){e.property="comment",l.fn.init.call(this,e)}}),e.spreadsheet.TextWrapCommand=l.extend({init:function(e){e.property="wrap",l.fn.init.call(this,e),this._value=e.value},getState:function(){var e={};this.range().forEachRow(function(t){var n=t.topLeft().row;e[n]=t.sheet().rowHeight(n)}),this._state=this.range().getState(this._property),this._rowHeight=e},undo:function(){var e,t=this.range().sheet(),n=this._rowHeight;this.range().setState(this._state);for(e in n)t.rowHeight(e,n[e])}}),e.spreadsheet.AdjustDecimalsCommand=r.extend({init:function(e){this._delta=e.value,e.property="format",r.fn.init.call(this,e)},exec:function(){var t=this.range().sheet(),n=this._delta,i=e.spreadsheet.formatting;this.getState(),t.batch(function(){this.range().forEachCell(function(e,r,o){var s,a=o.format;a||(s=o.value,"number"==typeof s&&/\./.test(s)&&(a="0."+(s+"").split(".")[1].replace(/\d/g,"0"))),(a||n>0)&&(a=i.adjustDecimals(a||"0",n),t.range(e,r).format(a))})}.bind(this))}}),e.spreadsheet.BorderChangeCommand=r.extend({init:function(e){e.property="border",r.fn.init.call(this,e),this._type=e.border,this._style=e.style},_batch:function(e){return this.range().sheet().batch(e,{})},exec:function(){var e=this;e.getState(),e._batch(function(){e[e._type](e._style)})},noBorders:function(){this.range().insideBorders(null),this.outsideBorders(null)},allBorders:function(e){this.range().insideBorders(e),this.outsideBorders(e)},leftBorder:function(e){this.range().leftColumn().borderLeft(e)},rightBorder:function(e){this.range().rightColumn().borderRight(e)},topBorder:function(e){this.range().topRow().borderTop(e)},bottomBorder:function(e){this.range().bottomRow().borderBottom(e)},outsideBorders:function(e){var t=this.range();t.leftColumn().borderLeft(e),t.topRow().borderTop(e),t.rightColumn().borderRight(e),t.bottomRow().borderBottom(e)},insideBorders:function(e){this.range().insideBorders(e),this.outsideBorders(null)},insideHorizontalBorders:function(e){this.range().insideHorizontalBorders(e)},insideVerticalBorders:function(e){this.range().insideVerticalBorders(e)}}),e.spreadsheet.MergeCellCommand=r.extend({init:function(e){r.fn.init.call(this,e),this._type=e.value},exec:function(){this.getState(),this[this._type]()},activate:function(e){this.range().sheet().activeCell(e)},getState:function(){this._state=this.range().getState()},undo:function(){"unmerge"!==this._type&&(this.range().unmerge(),this.activate(this.range().topLeft())),this.range().setState(this._state)},cells:function(){var e=this.range(),t=e._ref;e.merge(),this.activate(t)},horizontally:function(){var e=this.range().topRow()._ref;this.range().forEachRow(function(e){e.merge()}),this.activate(e)},vertically:function(){var e=this.range().leftColumn()._ref;this.range().forEachColumn(function(e){e.merge()}),this.activate(e)},unmerge:function(){var e=this.range(),t=e._ref.topLeft;e.unmerge(),this.activate(t)}}),e.spreadsheet.FreezePanesCommand=r.extend({init:function(e){r.fn.init.call(this,e),this._type=e.value},exec:function(){this.getState(),this._topLeft=this.range().topLeft(),this[this._type]()},getState:function(){this._state=this.range().sheet().getState()},undo:function(){this.range().sheet().setState(this._state)},panes:function(){var e=this._topLeft,t=this.range().sheet();t.frozenColumns(e.col).frozenRows(e.row)},rows:function(){var e=this._topLeft,t=this.range().sheet();t.frozenRows(e.row)},columns:function(){var e=this._topLeft,t=this.range().sheet();t.frozenColumns(e.col)},unfreeze:function(){var e=this.range().sheet();e.frozenRows(0).frozenColumns(0)}}),e.spreadsheet.PasteCommand=r.extend({init:function(e){r.fn.init.call(this,e),this._clipboard=e.workbook.clipboard(),this._clipboard.parse(),this._event=e.event,this._clipboardContent=this._clipboard._content,this._clipboardPasteRef=this._clipboard.pasteRef(),this._sheet=this._workbook.activeSheet(),this._range=this._sheet.range(this._clipboard.pasteRef()),this._state=this._range.getState()},exec:function(){return this.range().sheet().withCultureDecimals(this._exec.bind(this))},undo:function(){var e=this._sheet,t=e.range(this._clipboardPasteRef);e.trigger("changing",{data:this._state.data,range:t,changeType:i.PASTE})||this.setState(this._state)},_exec:function(){var e,t=this._clipboard.canPaste();return t.canPaste?(e=this._sheet.range(this._clipboardPasteRef),this._workbook.trigger("paste",{range:e,clipboardContent:this._clipboardContent})||this._sheet.trigger("changing",{data:this._clipboardContent.data,range:e,changeType:i.PASTE})?void this._event.preventDefault():(this._sheet.range(this._clipboardPasteRef).setState(this._clipboardContent,this._clipboard),void e._adjustRowHeight())):t.menuInvoked?{reason:"error",type:"useKeyboard"}:t.pasteOnMerged?{reason:"error",type:"modifyMerged"}:t.overflow?{reason:"error",type:"overflow"}:t.pasteOnDisabled?(this._event.preventDefault(),{reason:"error",type:"cannotModifyDisabled"}):{reason:"error"}}}),e.spreadsheet.AdjustRowHeightCommand=r.extend({exec:function(){var e=this.options,t=this._workbook.activeSheet(),n=e.range||t.range(e.rowIndex);n._adjustRowHeight()}}),e.spreadsheet.ToolbarPasteCommand=r.extend({exec:function(){return e.support.clipboard.paste?(this._workbook._view.clipboard.focus().select(),void document.execCommand("paste")):{reason:"error",type:"useKeyboard"}}}),e.spreadsheet.CutCommand=r.extend({_eventType:"cut",init:function(e){r.fn.init.call(this,e),this._clipboard=e.workbook.clipboard(),this._event=e.event},exec:function(){var e,t,n,r=this._clipboard.canCopy(),o=[];if(r.canCopy){if(n=this._workbook.activeSheet().selection(),"cut"==this._eventType){if(!n.enable())return this._event.preventDefault(),{reason:"error",type:"cannotModifyDisabled"};this.getState()}if(this._workbook.trigger(this._eventType,{range:n}))this._event.preventDefault();else if("cut"==this._eventType){if(e=n.values(),e.forEach(function(e){t=[],e.forEach(function(){t.push({})}),o.push(t)}),n.sheet().trigger("changing",{data:o,range:n,changeType:i.CUT}))return;this._clipboard.cut()}else this._clipboard.copy()}else{if(r.menuInvoked)return{reason:"error",type:"useKeyboard"};if(r.multiSelection)return{reason:"error",type:"unsupportedSelection"}}},undo:function(){var e=this.range();e.sheet().trigger("changing",{data:this._state.data,range:e,changeType:i.CUT})||this.setState(this._state)}}),e.spreadsheet.CopyCommand=e.spreadsheet.CutCommand.extend({_eventType:"copy",undo:n.noop}),e.spreadsheet.ToolbarCopyCommand=r.extend({init:function(e){r.fn.init.call(this,e),this._clipboard=e.workbook.clipboard()},undo:n.noop,exec:function(){if(!e.support.clipboard.copy)return{reason:"error",type:"useKeyboard"};var n=this._workbook._view.clipboard;t(n.html()),n.trigger("copy")}}),e.spreadsheet.AutoFillCommand=r.extend({init:function(e){r.fn.init.call(this,e)},origin:function(e){this._origin=e},exec:function(){var t,n=this.range();if(!n.enable())return{reason:"error",type:"rangeDisabled"};if(n.intersectingArrayFormula())return{reason:"error",type:"intersectsArray"};this.getState();try{if(t=n._previewFillFrom(this._origin),n.sheet().trigger("changing",{data:t.props,range:t.dest,changeType:i.AUTO_FILL}))return;t.dest._properties(t.props,!0)}catch(r){if(r instanceof e.spreadsheet.Range.FillError)return{reason:"error",type:r.code};throw r}},undo:function(){var e=this.range(),t=this._state;e.sheet().trigger("changing",{data:t.data,range:e,changeType:i.AUTO_FILL})||this.setState(this._state)}}),e.spreadsheet.ToolbarCutCommand=r.extend({init:function(e){r.fn.init.call(this,e),this._clipboard=e.workbook.clipboard()},exec:function(){if(!e.support.clipboard.copy)return{reason:"error",type:"useKeyboard"};var n=this._workbook._view.clipboard;t(n.html()),n.trigger("cut")}}),e.spreadsheet.FilterCommand=r.extend({undo:function(){this.range().filter(this._state)},exec:function(){var e=this.range();this._state=e.hasFilter(),e.filter(!this._state)}}),e.spreadsheet.SortCommand=r.extend({undo:function(){var e=this.range().sheet();e.setState(this._state)},exec:function(){var e,t=this.range(),n=t.sheet(),i=n.activeCell(),r=this.options.sheet?i.topLeft.col:this.options.column||0,o="asc"===this.options.value;return this._state=n.getState(),this.options.sheet&&(t=this.expandRange()),(e=t.cantSort())?{reason:"error",type:e.code}:void t.sort({column:r,ascending:o})},expandRange:function(){var t=this.range().sheet();return new e.spreadsheet.Range(t._sheetRef,t)}}),u=e.spreadsheet.ApplyFilterCommand=r.extend({column:function(){return this.options.column||0},undo:function(){var e=this.range().sheet();e.clearFilter(this.column()),this._state.length&&this.range().filter(this._state)},getState:function(){var e=this.range().sheet(),t=e.filter();t&&(this._state=t.columns.filter(function(e){return e.index==this.column()}.bind(this)))},exec:function(){var t,n,i=this.range(),r=this.column(),o=i.sheet().filter(),s=!1;this.options.valueFilter?n={column:r,filter:new e.spreadsheet.ValueFilter(this.options.valueFilter)}:this.options.customFilter&&(n={column:r,filter:new e.spreadsheet.CustomFilter(this.options.customFilter)}),this.getState(),o&&o.ref.eq(i._ref)&&o.columns.length?(o.columns.forEach(function(e){e.index===r&&(s=!0)}),t=o.columns.map(function(e){return e.index===r?n:{column:e.index,filter:e.filter}}),s||t.push(n)):t=n,i.filter(t)}}),e.spreadsheet.ClearFilterCommand=u.extend({exec:function(){var e=this.range(),t=this.column();this.getState(),e.clearFilter(t)}}),e.spreadsheet.HideLineCommand=r.extend({init:function(e){r.fn.init.call(this,e),this.axis=e.axis},undo:function(){var e=this.range().sheet();e.setAxisState(this._state)},exec:function(){var e=this.range().sheet();this._state=e.getAxisState(),"row"==this.axis?e.axisManager().hideSelectedRows():e.axisManager().hideSelectedColumns()}}),e.spreadsheet.UnHideLineCommand=e.spreadsheet.HideLineCommand.extend({exec:function(){var e=this.range().sheet();this._state=e.getAxisState(),"row"==this.axis?e.axisManager().unhideSelectedRows():e.axisManager().unhideSelectedColumns()}}),c=r.extend({exec:function(){this._expandedRange=this._expand(this.range()),this._state=this._expandedRange.getState(),this._indexes=this._exec(this._expandedRange.sheet())},undo:function(){var e=this,t=e._expandedRange,n=t.sheet();n.batch(function(){e._indexes.forEach(function(t){e._undoOne(n,t),n._restoreModifiedFormulas(t.formulas)}),t.setState(e._state)},{layout:!0,recalc:!0,ref:t._ref})}}),e.spreadsheet.DeleteRowCommand=c.extend({_expand:function(e){return e.resize({left:-(1/0),right:+(1/0)})},_exec:function(e){return e.axisManager().deleteSelectedRows()},_undoOne:function(e,t){e.insertRow(t.index),e.rowHeight(t.index,t.height)}}),e.spreadsheet.DeleteColumnCommand=c.extend({_expand:function(e){return e.resize({top:-(1/0),bottom:+(1/0)})},_exec:function(e){return e.axisManager().deleteSelectedColumns()},_undoOne:function(e,t){e.insertColumn(t.index),e.columnWidth(t.index,t.width)}}),h=r.extend({init:function(e){r.fn.init.call(this,e),this._value=e.value},undo:function(){var e=this,t=e.range().sheet();t.batch(function(){for(var n=e._pos.count;--n>=0;)e._undoOne(t,e._pos.base)},{layout:!0,recalc:!0})}}),e.spreadsheet.AddColumnCommand=h.extend({exec:function(){var e=this.range().sheet(),t=e.axisManager().preventAddColumn();return t?t:void(this._pos="left"===this._value?e.axisManager().addColumnLeft():e.axisManager().addColumnRight())},_undoOne:function(e,t){e.deleteColumn(t)}}),e.spreadsheet.AddRowCommand=h.extend({exec:function(){var e=this.range().sheet(),t=e.axisManager().preventAddRow();return t?t:void(this._pos="above"===this._value?e.axisManager().addRowAbove():e.axisManager().addRowBelow())},_undoOne:function(e,t){e.deleteRow(t)}}),e.spreadsheet.EditValidationCommand=r.extend({init:function(e){r.fn.init.call(this,e),this._value=e.value},exec:function(){var e=this,t=e.range().sheet();this.getState(),t.trigger("changing",{data:e._value,range:e.range(),changeType:i.VALIDATION})||t.withCultureDecimals(function(){e.range().validation(e._value)})},undo:function(){var e=this.range(),t=this._state;e.sheet().trigger("changing",{data:t.data[0][0].validation,range:e,changeType:i.VALIDATION})||this.setState(this._state);
}}),e.spreadsheet.OpenCommand=r.extend({cannotUndo:!0,exec:function(){var e,t=this.options.file;return null===t.name.match(/.xlsx$/i)?{reason:"error",type:"openUnsupported"}:(e=this.options.workbook,void e.fromFile(this.options.file).then(function(){var t=e.excelImportErrors;t&&t.length&&e._view.openDialog("importError",{errors:t})}))}}),e.spreadsheet.SaveAsCommand=r.extend({exec:function(){var e=this.options.name+this.options.extension;".xlsx"===this.options.extension?this.options.workbook.saveAsExcel({fileName:e}):".pdf"===this.options.extension&&this.options.workbook.saveAsPDF(n.extend(this.options.pdf,{workbook:this.options.workbook,fileName:e}))}}),d=r.extend({init:function(e){r.fn.init.call(this,e),this._name=e.name,this._value=e.value},getState:function(){this._state=this._workbook.nameDefinition(this._name)},setState:function(){this._workbook.nameDefinition(this._name,this._state),this._workbook.trigger("change",{recalc:!0})}}),e.spreadsheet.DefineNameCommand=d.extend({exec:function(){this.getState();try{this._workbook.defineName(this._name,this._value),this._workbook.trigger("change",{recalc:!0})}catch(e){return{title:"Error",body:e+"",reason:"error"}}}}),e.spreadsheet.DeleteNameCommand=d.extend({exec:function(){this.getState(),this._workbook.undefineName(this._name),this._workbook.trigger("change",{recalc:!0})}}))}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/formulabar.min",["kendo.core.min"],e)}(function(){!function(e){var t,n,i;e.support.browser.msie&&e.support.browser.version<9||(t=e.jQuery,n={wrapper:"k-spreadsheet-formula-bar"},i=e.ui.Widget.extend({init:function(n,r){e.ui.Widget.call(this,n,r),n=this.element.addClass(i.classNames.wrapper),this.formulaInput=new e.spreadsheet.FormulaInput(t("<div/>").appendTo(n))},destroy:function(){this.formulaInput&&this.formulaInput.destroy(),this.formulaInput=null}}),e.spreadsheet.FormulaBar=i,t.extend(!0,i,{classNames:n}))}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/formulainput.min",["kendo.core.min"],e)}(function(){!function(e,t){function n(e){return"("==e||"["==e||"{"==e}function i(e){return")"==e||"]"==e||"}"==e}function r(e,t){return"("==t?")"==e:"["==t?"]"==e:"{"==t&&"}"==e}function o(e,t){return e.begin<=t.begin&&e.end>=t.end}function s(t){return e.spreadsheet.calc.runtime.FUNCS[t.toLowerCase()]}function a(e,t){return!(!e||!t)&&("ref"==e.type&&"ref"==t.type?e.ref.eq(t.ref):e.value===t.value)}var l,u,c,h,d,f,p,m,g;e.support.browser.msie&&e.support.browser.version<9||(l=e.jQuery,u=e.ui.Widget,c=".kendoFormulaInput",h=e.keys,d={wrapper:"k-spreadsheet-formula-input",listWrapper:"k-spreadsheet-formula-list"},f=["font-family","font-size","font-stretch","font-style","font-weight","letter-spacing","text-transform","line-height"],p={27:"esc",37:"left",39:"right",35:"end",36:"home",32:"spacebar"},m=/(^_|[^a-z0-9]$)/i,g=u.extend({init:function(e,t){u.call(this,e,t),e=this.element,e.addClass(g.classNames.wrapper).attr("contenteditable",!0).attr("spellcheck",!1).css("white-space","pre"),this.options.autoScale&&e.on("input",this.scale.bind(this)),this._highlightedRefs=[],this._staticTokens=[],this._formulaSource(),this._formulaList(),this._popup(),this._tooltip(),e.on("keydown",this._keydown.bind(this)).on("keyup",this._keyup.bind(this)).on("blur",this._blur.bind(this)).on("input click",this._input.bind(this)).on("focus",this._focus.bind(this)).on("paste",this._paste.bind(this))},options:{name:"FormulaInput",autoScale:!1,filterOperator:"startswith",scalePadding:30,minLength:1},events:["keyup","focus"],enable:function(e){return void 0===e?"true"===this.element.attr("contenteditable"):(e?this.element.attr("contenteditable",e):this.element.removeAttr("contenteditable"),void this.element.toggleClass("k-state-disabled",!e))},getPos:function(){function e(e,t){try{!function r(n){if(n===e)throw t;if(1==n.nodeType)for(var i=n.firstChild;i;i=i.nextSibling)r(i);else 3==n.nodeType&&(t+=n.nodeValue.length)}(i)}catch(n){return n}}var n,i=this.element[0],r=t.getSelection(),o=e(r.focusNode,r.focusOffset),s=e(r.anchorNode,r.anchorOffset);if(null!=o&&null!=s)return o>s&&(n=o,o=s,s=n),{begin:o,end:s,collapsed:o==s}},setPos:function(e,n){function i(e,t){return e.startOffset!=t.startOffset||e.endOffset!=t.endOffset||e.startContainer!=t.endContainer||e.endContainer!=t.endContainer}function r(e,t){try{!function i(e){var n,r;if(3==e.nodeType){if(n=e.nodeValue.length,n>=t)throw e;t-=n}else if(1==e.nodeType)for(r=e.firstChild;r;r=r.nextSibling)i(r)}(e)}catch(n){return{node:n,pos:t}}}var o,s,a,l=this.element[0];e=r(l,e),n=null!=n?r(l,n):e,e&&n&&(o=document.createRange(),o.setStart(e.node,e.pos),o.setEnd(n.node,n.pos),s=t.getSelection(),a=s.getRangeAt(0),i(o,a)&&(s.removeAllRanges(),s.addRange(o)))},end:function(){this.setPos(this.length())},home:function(){this.setPos(0)},select:function(){this.setPos(0,this.length())},length:function(){return this.value().length},_formulaSource:function(){var t,n,i=[];for(n in e.spreadsheet.calc.runtime.FUNCS)m.test(n)||(t=n.toUpperCase(),i.push({value:t,text:t}));this.formulaSource=new e.data.DataSource({data:i})},_formulaList:function(){this.list=new e.ui.StaticList(l("<ul />").addClass(g.classNames.listWrapper).insertAfter(this.element),{autoBind:!1,selectable:!0,change:this._formulaListChange.bind(this),dataSource:this.formulaSource,dataValueField:"value",template:"#:data.value#"}),this.list.element.on("mousedown",function(e){e.preventDefault()})},_formulaListChange:function(){var e,t,n,i=this._tokenContext();i&&!this._mute&&(e=i.token,t=this.list.value()[0],n={replace:!0,token:e,end:e.end},i.nextToken&&"("==i.nextToken.value||(t+="("),this._replaceAt(n,t),this.popup.close())},_popup:function(){this.popup=new e.ui.Popup(this.list.element,{anchor:this.element})},_blur:function(){this.popup.close(),clearTimeout(this._focusId),this.trigger("blur")},_isFormula:function(){return/^=/.test(this.value())},_keydown:function(e){var t=e.keyCode;p[t]?(this.popup.close(),this._navigated=!0):this._move(t)&&(this._navigated=!0,e.preventDefault()),this._keyDownTimeout=setTimeout(this._syntaxHighlight.bind(this))},_keyup:function(){var e,t=this.popup;this._isFormula()&&!this._navigated&&(e=((this._tokenContext()||{}).token||{}).value,this.filter(e),e&&this.formulaSource.view().length?(t[t.visible()?"position":"open"](),this.list.focusFirst()):t.close()),this._navigated=!1,this._syntaxHighlight(),this.trigger("keyup")},_input:function(){this._syntaxHighlight()},_focus:function(){setTimeout(this._sync.bind(this)),this._focusTimeout=setTimeout(this._syntaxHighlight.bind(this)),this.trigger("focus")},_paste:function(n){var i,r,o;n.preventDefault(),i=this.getPos(),r=e.support.browser.msie?t.clipboardData.getData("Text"):n.originalEvent.clipboardData.getData("text/plain"),o=this.value(),o=o.substr(0,i.begin)+r+o.substr(i.end),this.value(o),this.setPos(i.begin+r.length),this.scale()},_move:function(e){var t=this.list,n=this.popup;if(n.visible()){if(e===h.DOWN)return t.focusNext(),t.focus()||t.focusFirst(),!0;if(e===h.UP)return t.focusPrev(),t.focus()||t.focusLast(),!0;if(e===h.ENTER)return t.select(t.focus()),n.close(),!0;if(e===h.TAB)return t.select(t.focus()),n.close(),!0;if(e===h.PAGEUP)return t.focusFirst(),!0;if(e===h.PAGEDOWN)return t.focusLast(),!0}return e===h.ENTER||e===h.TAB},_tokenContext:function(){var t,n,i,r=this.getPos(),s=this.value();if(!s||!r||!r.collapsed)return null;for(t=e.spreadsheet.calc.tokenize(s,this.row(),this.col()),i=0;i<t.length;++i)if(n=t[i],o(n,r)&&/^(?:str|sym|func)$/.test(n.type))return{token:n,nextToken:t[i+1]};return null},_sync:function(){this._editorToSync&&this.isActive()&&this._editorToSync.value(this.value())},_textContainer:function(){var t=e.getComputedStyles(this.element[0],f);t.position="absolute",t.visibility="hidden",t.whiteSpace="pre",t.top=-3333,t.left=-3333,this._span=l("<span style='white-space: pre'/>").css(t).insertAfter(this.element)},_tooltip:function(){this._cellTooltip=l('<div class="k-widget k-tooltip" style="position:absolute; display:none">A1</div>').insertAfter(this.element)},tooltip:function(e){this._cellTooltip.text(e)},toggleTooltip:function(e){this._cellTooltip.toggle(e)},isActive:function(){return this.element[0]===e._activeElement()},filter:function(e){!e||e.length<this.options.minLength||(this._mute=!0,this.list.select(-1),this._mute=!1,this.formulaSource.filter({field:this.list.options.dataValueField,operator:this.options.filterOperator,value:e}))},hide:function(){this.enable(!1),this.element.hide(),this._cellTooltip.hide()},show:function(){this.enable(!0),this.element.show()},row:function(){if(this.activeCell)return this.activeCell.row},col:function(){if(this.activeCell)return this.activeCell.col},position:function(e){e&&(this.show(),this.element.css({top:e.top+1+"px",left:e.left+1+"px"}),this._cellTooltip.css({top:e.top-this._cellTooltip.height()-10+"px",left:e.left}))},resize:function(e){e&&this.element.css({width:e.width-1,height:e.height-1})},canInsertRef:function(e){var t,n=this._canInsertRef(e),i=n&&n.token;if(i)for(t=0;t<this._staticTokens.length;t++)if(a(i,this._staticTokens[t]))return null;return n},_canInsertRef:function(t){function i(e){return e.begin>l.begin}function r(e){if(e){if(/^(?:num|str|bool|sym|ref)$/.test(e.type))return{replace:!0,token:e,end:e.end};if(/^(?:op|punc|startexp)$/.test(e.type))return e.end==l.end?s(e,u[h+1]):s(u[h-1],e)}}function s(e,t){if(null==e)return null;if(null==t)return/^(?:op|startexp)$/.test(e.type)||n(e.value)?{token:e,end:l.end}:null;if(a){if("op"==e.type&&/^(?:punc|op)$/.test(t.type))return{token:e,end:l.end}}else{if("startexp"==e.type)return{token:e,end:l.end};if(/^(?:op|punc)$/.test(e.type)&&/^[,;({]$/.test(e.value))return{token:e,end:l.end};if(/^(?:ref|sym)/.test(e.type))return{token:e,replace:!0,end:e.end};if(/^(?:ref|sym)/.test(t.type))return{token:t,replace:!0,end:t.end}}return!1}var a,l,u,c,h;if(this.popup.visible())return null;if(a=t,l=this.getPos(),l&&this._isFormula()){if(0===l.begin)return null;for(u=e.spreadsheet.calc.tokenize(this.value(),this.row(),this.col()),h=0;h<u.length;++h){if(c=u[h],o(c,l))return r(c);if(i(c))return s(u[h-1],c)}return s(c,null)}return null},refAtPoint:function(e){var t,n=this._canInsertRef();n&&(t=e.selection()._ref.simplify().clone().relative(0,0,3),e!==this.activeSheet&&(t=t.setSheet(e.name(),!0)),this._replaceAt(n,t.print(0,0)))},_replaceAt:function(e,t){var n,i=this.value(),r=e.token,o=i.substr(e.end);i=i.substr(0,e.replace?r.begin:e.end)+t,n=i.length,i+=o,this._value(i),this.setPos(n),this.scale(),this._syntaxHighlight()},syncWith:function(t){var n,i=this,r="input"+c,o=i._sync.bind(i);e.support.browser.msie&&(r="keydown"+c,n=function(){setTimeout(o)}),i._editorToSync=t,i.element.off(r).on(r,n||o)},scale:function(){var e,t,n=this.element;this._span||this._textContainer(),this._span.html(n.html()),e=this._span.width()+this.options.scalePadding,t=this._span.height(),e>n.width()&&n.width(e),t>n.height()&&n.height(t),this._sync()},_value:function(e){this.element.text(e)},value:function(e){if(void 0===e){var t=this.element[0].innerText;return t.replace(/\n$/,"")}this._value(e),this._syntaxHighlight()},highlightedRefs:function(){return this._highlightedRefs.slice()},_syntaxHighlight:function(){var t,a=this.getPos(),l=this.value(),u=e.spreadsheet.Pane.classNames.series,c=[],h=0,d=[],f=[];if(!a||a.collapsed){if(!/^=/.test(l))return(this._staticTokens.length||this._highlightedRefs.length)&&(this._staticTokens=[],this._highlightedRefs=[],this.element.text(l)),void(this.popup&&this.popup.close());f=e.spreadsheet.calc.tokenize(l,this.row(),this.col()),f.forEach(function(e){if(e.active=!1,e.cls=["k-syntax-"+e.type],"ref"==e.type&&(e.colorClass=u[h++%u.length],e.cls.push(e.colorClass),c.push(e)),a&&"punc"==e.type)if(n(e.value))d.unshift(e);else if(i(e.value)){var l=d.shift();l?r(e.value,l.value)?(o(e,a)||o(l,a))&&(e.cls.push("k-syntax-paren-match"),l.cls.push("k-syntax-paren-match")):(e.cls.push("k-syntax-error"),l.cls.push("k-syntax-error")):e.cls.push("k-syntax-error")}a&&o(e,a)&&(e.cls.push("k-syntax-at-point"),e.active=!0,t=e),"func"!=e.type||s(e.value)||a&&o(e,a)||e.cls.push("k-syntax-error")}),f.reverse().forEach(function(t){var n=t.begin,i=t.end,r=e.htmlEncode(l.substring(n,i));l=l.substr(0,n)+"<span class='"+t.cls.join(" ")+"'>"+r+"</span>"+l.substr(i)}),this.element.html(l),a&&this.setPos(a.begin,a.end),t&&/^(?:startexp|op|punc)$/.test(t.type)&&this._setStaticTokens(f),this._highlightedRefs=c}},_setStaticTokens:function(e){var t,n;for(this._staticTokens=[],t=0;t<e.length;t++)n=e[t],/^(?:num|str|bool|sym|ref)$/.test(n.type)&&this._staticTokens.push(n)},destroy:function(){this._editorToSync=null,this.element.off(c),clearTimeout(this._focusTimeout),clearTimeout(this._keyDownTimeout),this._cellTooltip=null,this._span=null,this.popup.destroy(),this.popup=null,u.fn.destroy.call(this)},insertNewline:function(){var e=this.value(),t=this.getPos(),n=t.end==e.length;e=e.substr(0,t.begin)+(n?"\n\n":"\n"+e.substr(t.end)),this.value(e),this.setPos(t.begin+1)}}),e.spreadsheet.FormulaInput=g,l.extend(!0,g,{classNames:d}))}(kendo,window)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/eventlistener.min",["kendo.core.min"],e)}(function(){!function(e){var t,n,i,r,o,s;e.support.browser.msie&&e.support.browser.version<9||(t=e.jQuery,n={8:"backspace",9:"tab",13:"enter",27:"esc",37:"left",38:"up",39:"right",40:"down",35:"end",36:"home",32:"spacebar",33:"pageup",34:"pagedown",46:"delete",113:":edit"},i=navigator.platform.toUpperCase().indexOf("MAC")>=0,r=function(e){return e>47&&e<58||e>64&&e<91||e>95&&e<112||e>185&&e<193||e>218&&e<223||229===e},o=function(e){var t=e.keyCode,i=n[t];return!i&&r(t)&&(i=":alphanum"),!i&&e.key&&1==e.key.length&&(i=":alphanum"),i},s=e.Class.extend({init:function(e,n,i){if(this._handlers={},this.target=e,this._observer=n||window,this.keyDownProxy=this.keyDown.bind(this),this.mouseProxy=this.mouse.bind(this),this.touchProxy=this.touch.bind(this),this.threshold=5,this._pressLocation=null,e.on("keydown",this.keyDownProxy),e.on("contextmenu mousedown cut copy paste scroll wheel click dblclick focus",this.mouseProxy),e.on("touchmove touchend",this.touchProxy),t(document.documentElement).on("mousemove mouseup",this.mouseProxy),t(document.documentElement).on("touchmove touchend",this.touchProxy),i)for(var r in i)this.on(r,i[r])},keyDown:function(e){this.handleEvent(e,o(e.originalEvent))},touch:function(e){this.handleEvent(e,e.type)},mouse:function(e){var t,n,i,r,o;e.which?t=3==e.which:e.button&&(t=2==e.button),n=e.type,"mousedown"===n&&(t?n="rightmousedown":this._pressLocation={x:e.pageX,y:e.pageY}),"mouseup"===n&&(t||(this._pressLocation=null)),"mousemove"===n&&this._pressLocation&&(i=this._pressLocation.x-e.pageX,r=this._pressLocation.y-e.pageY,o=Math.sqrt(i*i+r*r),o>this.threshold&&(n="mousedrag")),this.handleEvent(e,n)},handleEvent:function(e,t){var n,r,o="";e.mod=i?e.metaKey:e.ctrlKey&&!e.altKey,e.altKey&&(o+="alt+"),e.shiftKey&&(o+="shift+"),e.ctrlKey&&(o+="ctrl+"),o+=t,n=this._handlers["*+"+t],n&&n.call(this._observer,e,o),r=this._handlers[o],r&&r.call(this._observer,e,o)},on:function(e,t){var n=this._handlers;"string"==typeof t&&(t=this._observer[t]),"string"==typeof e&&(e=e.split(",")),e.forEach(function(e){n[e]=t})},destroy:function(){this.target.off("keydown",this.keyDownProxy),this.target.off("keydown",this.mouseProxy),t(document.documentElement).off("mousemove mouseup",this.mouseProxy)}}),e.spreadsheet.EventListener=s)}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/rangelist.min",["kendo.core.min"],e)}(function(){!function(e){function t(e){if(e.left.level===e.level){var t=e;e=e.left,t.left=e.right,e.right=t}return e}function n(e){if(e.right.right.level===e.level){var t=e;e=e.right,t.right=e.left,e.left=t,e.level+=1}return e}function i(e,r){return e===u?new l(1,r,u,u):(e.value.start>r.start?e.left=i(e.left,r):e.right=i(e.right,r),n(t(e)))}function r(e,i){var o,s;if(e===u)return e;if(o=e.value.start-i.start,0===o)if(e.left!==u&&e.right!==u){for(s=e.left;s.right!==u;)s=s.right;e.value=s.value,e.left=r(e.left,e.value)}else e=e.left===u?e.right:e.left;else o>0?e.left=r(e.left,i):e.right=r(e.right,i);return(e.left.level<e.level-1||e.right.level<e.level-1)&&(e.level-=1,e.right.level>e.level&&(e.right.level=e.level),e=t(e),e.right=t(e.right),e.right.right=t(e.right.right),e=n(e),e.right=n(e.right)),e}function o(e,t){e!==u&&(o(e.left,t),t.push(e.value),o(e.right,t))}function s(e,t,n){if(e!==u){var i=e.value;t.start<i.start&&s(e.left,t,n),i.intersects(t)&&n.push(i),t.end>i.end&&s(e.right,t,n)}}function a(e,t,n){t!==u&&(a(e,t.left,n),e.insert(n(t.value)),a(e,t.right,n))}var l,u,c,h,d,f,p;e.support.browser.msie&&e.support.browser.version<9||(l=e.Class.extend({init:function(e,t,n,i){this.level=e,this.value=t,this.left=n,this.right=i}}),u=new function(){this.left=this,this.right=this,this.level=0},c=e.Class.extend({init:function(e,t,n){this.start=e,this.end=t,this.value=n},intersects:function(e){return e.start<=this.end&&e.end>=this.start},clone:function(){return new c(this.start,this.end,this.value)}}),h=e.Class.extend({init:function(){this.root=u},insert:function(e){this.root=i(this.root,e)},remove:function(e){this.root=r(this.root,e)},findrange:function(e){for(var t=this.root;t!=u;)if(e<t.value.start)t=t.left;else{if(!(e>t.value.end))return t.value;t=t.right}return null},values:function(){var e=[];return o(this.root,e),e},intersecting:function(e,t){var n=[];return s(this.root,new c(e,t),n),n},map:function(e){var t=new h;return a(t,this.root,e),t},clone:function(){return this.map(function(e){return e.clone()})},first:function(){for(var e=this.root;e.left!=u;)e=e.left;return e},last:function(){for(var e=this.root;e.right!=u;)e=e.right;return e}}),d=e.Class.extend({init:function(e,t,n){void 0===t?this.tree=e:(this.tree=new h,this.tree.insert(new c(e,t,n)))},values:function(){return this.tree.values()},map:function(e){return new d(this.tree.map(e))},intersecting:function(e,t){return this.tree.intersecting(e,t)},first:function(){return this.tree.first().value},last:function(){return this.tree.last().value},insert:function(e,t,n){return this.tree.insert(new c(e,t,n))},value:function(e,t,n){var i,r,o,s,a,l,u,c,h;if(void 0===n)return void 0===t&&(t=e),this.intersecting(e,t)[0].value;if(i=this.tree.intersecting(e-1,t+1),i.length)for(r=i[0],o=i[i.length-1],r.end<e&&(r.value===n?e=r.start:i.shift()),o.start>t&&(o.value===n?t=o.end:i.pop()),s=0,a=i.length;s<a;s++)l=i[s],u=l.value,c=l.start,h=l.end,this.tree.remove(l),c<e&&(u!==n?this.insert(c,e-1,u):e=c),h>t&&(u!==n?this.insert(t+1,h,u):t=h);this.insert(e,t,n)},expandedValues:function(e,t){var n,i=this.intersecting(e,t),r=[],o=0;for(n=e;n<=t;n++)i[o].end<n&&o++,r.push({index:n-e,value:i[o].value});return r},sortedIndices:function(e,t,n,i){var r=this.expandedValues(e,t),o=function(e,t){return e.value===t.value?e.index-t.index:n(e.value,t.value)};return i&&(o=function(e,t){var r=i[e.index],o=i[t.index];return r.value===o.value?n(e.value,t.value):e.index-t.index}),r.sort(o),r},sort:function(e,t,n){var i,r,o;if(1!==this.intersecting(e,t).length)for(i=this.expandedValues(e,t),r=0,o=n.length;r<o;r++)this.value(r+e,r+e,i[n[r].index].value)},copy:function(e,t,n){var i,r,o,s,a,l=this.intersecting(e,t),u=n;for(r=0,o=l.length;r<o;r++)s=l[r].start,s<e&&(s=e),a=l[r].end,a>t&&(a=t),i=u+(a-s),this.value(u,i,l[r].value),u=++i},iterator:function(e,t){return new f(e,t,this.intersecting(e,t))},getState:function(){return this.tree.clone()},setState:function(e){this.tree=e.clone()},toJSON:function(){return this.values()},fromJSON:function(e){e.forEach(function(e){this.value(e.start,e.end,e.value)},this)}}),f=e.Class.extend({init:function(e,t,n){this.start=e,this.end=t,this.index=0,this.ranges=n},unique:function(){return this.ranges.map(function(e){return e.value})},at:function(e){for(;this.ranges[this.index]&&this.ranges[this.index].end<e;)this.index++;return this.ranges[this.index]&&this.ranges[this.index].value},forEach:function(e){for(var t=this.start;t<=this.end;t++)e(this.at(t),t);this.index=0}}),p=d.extend({init:function(e,t,n){this.tree=new h,this.range=new c(e,t,n)},intersecting:function(e,t){var n,i,r,o=this.tree.intersecting(e,t),s=[];if(!o.length)return[this.range];for(i=0,r=o.length;i<r;i++)n=o[i],n.start>e&&s.push(new c(e,n.start-1,this.range.value)),s.push(n),e=n.end+1;return n.end<t&&s.push(new c(n.end+1,t,this.range.value)),s},insert:function(e,t,n){n!==this.range.value&&this.tree.insert(new c(e,t,n))},lastRangeStart:function(){var e=this.tree.root;if(e===u)return this.range.start;for(;e.right!==u;)e=e.right;return e.value.end+1}}),e.spreadsheet.RangeTree=h,e.spreadsheet.RangeList=d,e.spreadsheet.SparseRangeList=p,e.spreadsheet.ValueRange=c)}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/propertybag.min",["kendo.core.min"],e)}(function(){!function(e){function t(e){return e.replace(/M/g,"m").replace(/'/g,'"').replace(/tt/,"am/pm")}function n(e){return e=e.clone(),e.value=e.value.deepClone(),e}function i(){var e=this.map(n);return e.clone=i,e}var r,o,s;e.support.browser.msie&&e.support.browser.version<9||(r=e.Class.extend({init:function(e){this.list=e},get:function(e){return this.parse(this.list.value(e,e))},set:function(e,t,n){void 0===n&&(n=t,t=e),this.list.value(e,t,n)},parse:function(e){return e},copy:function(e,t,n){this.list.copy(e,t,n)},iterator:function(e,t){return this.list.iterator(e,t)}}),o=r.extend({set:function(e,t,n){this.list.value(e,t,JSON.stringify(n))},parse:function(e){return JSON.parse(e)}}),s=r.extend({init:function(e,t){r.prototype.init.call(this,e),this.formats=t},set:function(n,i,r){r instanceof Date?(r=e.spreadsheet.dateToNumber(r),this.formats.value(n,i)||this.formats.value(n,i,t(e.culture().calendar.patterns.d))):"number"==typeof r&&(r=e.spreadsheet.calc.runtime.limitPrecision(r)),this.list.value(n,i,r)}}),e.spreadsheet.PropertyBag=e.Class.extend({specs:[{property:r,name:"format",value:null,sortable:!0,serializable:!0},{property:s,name:"value",value:null,sortable:!0,serializable:!0,depends:"format"},{property:r,name:"formula",value:null,sortable:!0,serializable:!0},{property:r,name:"background",value:null,sortable:!0,serializable:!0},{property:o,name:"vBorders",value:null,sortable:!1,serializable:!1},{property:o,name:"hBorders",value:null,sortable:!1,serializable:!1},{property:r,name:"color",value:null,sortable:!0,serializable:!0},{property:r,name:"fontFamily",value:null,sortable:!0,serializable:!0},{property:r,name:"underline",value:null,sortable:!0,serializable:!0},{property:r,name:"fontSize",value:null,sortable:!0,serializable:!0},{property:r,name:"italic",value:null,sortable:!0,serializable:!0},{property:r,name:"bold",value:null,sortable:!0,serializable:!0},{property:r,name:"textAlign",value:null,sortable:!0,serializable:!0},{property:r,name:"indent",value:null,sortable:!0,serializable:!0},{property:r,name:"verticalAlign",value:null,sortable:!0,serializable:!0},{property:r,name:"wrap",value:null,sortable:!0,serializable:!0},{property:r,name:"validation",value:null,sortable:!1,serializable:!0},{property:r,name:"enable",value:null,sortable:!1,serializable:!0},{property:r,name:"link",value:null,sortable:!0,serializable:!0},{property:r,name:"editor",value:null,sortable:!0,serializable:!0},{property:r,name:"comment",value:null,sortable:!0,serializable:!0}],init:function(t,n,r){r=r||{};var o=t*n-1;this.rowCount=t,this.columnCount=n,this.cellCount=o,this.properties={},this.lists={},this.specs.forEach(function(t){var n,i=t.name,s=r[i];void 0===s&&(s=t.value),this.lists[i]=new e.spreadsheet.SparseRangeList(0,o,s),n=this.properties[i]=new t.property(this.lists[i],this.lists[t.depends]),n.spec=t},this),this.lists.formula.tree.clone=i,this.lists.validation.tree.clone=i},getState:function(){var e={};return this.specs.forEach(function(t){e[t.name]=this.lists[t.name].getState()},this),e},setState:function(e){this.specs.forEach(function(t){this.lists[t.name].setState(e[t.name])},this)},get:function(e,t){if(void 0===t)return this.lists[e];switch(e){case"borderRight":t+=this.rowCount;case"borderLeft":e="vBorders";break;case"borderBottom":t++;case"borderTop":e="hBorders"}return t>this.cellCount?null:this.properties[e].get(t)},set:function(e,t,n,i){switch(e){case"borderRight":t+=this.rowCount,n+=this.rowCount;case"borderLeft":e="vBorders";break;case"borderBottom":t++,n++;case"borderTop":e="hBorders"}t<=n&&n<=this.cellCount&&this.properties[e].set(t,n,i)},fromJSON:function(e,t){var n,i;for(n=0;n<this.specs.length;n++)i=this.specs[n],i.serializable&&void 0!==t[i.name]&&this.set(i.name,e,e,t[i.name],!1);["borderLeft","borderRight","borderTop","borderBottom"].forEach(function(n){void 0!==t[n]&&this.set(n,e,e,t[n])},this)},copy:function(e,t,n){this.specs.forEach(function(i){this.properties[i.name].copy(e,t,n)},this)},iterator:function(e,t,n){var i=this.properties[e],r=i.iterator(t,n),o=r.at,s=this.cellCount;return r.at=function(e){return e>s?null:i.parse(o.call(r,e))},r.name=e,r.value=i.spec.value,r},sortable:function(){return this.specs.filter(function(e){return e.sortable}).map(function(e){return this.lists[e.name]},this)},iterators:function(e,t){return this.specs.reduce(function(n,i){return i.serializable&&n.push(this.iterator(i.name,e,t)),n}.bind(this),[])},forEach:function(e,t,n){function i(e,t,n){var i=t.at(n);i!==t.value&&(r[e]=i)}var r,o,s,a,l,u=this.iterators(e,t),c=this.iterator("hBorders",e,t+1),h=this.iterator("vBorders",e,t),d=this.iterator("vBorders",e+this.rowCount,t+this.rowCount);for(o=e;o<=t;o++){for(r={},s=0;s<u.length;s++)a=u[s],l=a.at(o),l!==a.value&&(r[a.name]=l);i("borderLeft",h,o),i("borderRight",d,o+this.rowCount),i("borderTop",c,o),(o+1)%this.rowCount&&i("borderBottom",c,o+1),n(r)}},forEachProperty:function(e){for(var t in this.properties)e(this.properties[t])}}),e.spreadsheet.ALL_PROPERTIES=e.spreadsheet.PropertyBag.prototype.specs.reduce(function(e,t){return t.serializable&&e.push(t.name),e},["borderTop","borderRight","borderBottom","borderLeft"]))}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/references.min",["kendo.core.min"],e)}(function(){"use strict";function e(t){var n=Math.floor(t/26)-1;return(n>=0?e(n):"")+String.fromCharCode(65+t%26)}function t(e){return/^[a-z_][a-z0-9_]*$/i.test(e)?e:"'"+e.replace(/\x27/g,"\\'")+"'"}function n(n,i,r,o){var s="";return++i,isFinite(i)?null==o||2&o||(i="$"+i):i="",isFinite(r)?(s=e(r),null==o||1&o||(s="$"+s)):r="",n?t(n)+"!"+s+i:s+i}var i,r,o,s,a,l,u,c;kendo.support.browser.msie&&kendo.support.browser.version<9||(i=kendo.spreadsheet,r=kendo.Class,o=r.extend({type:"ref",sheet:"",clone:function(){return this},hasSheet:function(){return this._hasSheet},simplify:function(){return this},setSheet:function(e,t){return this.sheet=e,null!=t&&(this._hasSheet=t),this},absolute:function(){return this},relative:function(){return this},adjust:function(){return this},toString:function(){return this.relative(0,0,3,3).print(0,0)},forEach:function(e,t){e.call(t,this)},map:function(e,t){return e.call(t,this)},intersects:function(e){return this.intersect(e)!==s},isCell:function(){return!1},toRow:function(){return this},toColumn:function(){return this},first:function(){return this},lastRange:function(){return this},size:function(){return 1},rangeAt:function(){return this},nextRangeIndex:function(){return 0},previousRangeIndex:function(){return 0},eq:function(e){var t,n,i,r,o=this,a=e;if(o===s||a===s)return o===a;if((a instanceof l||a instanceof u&&!(o instanceof l))&&(o=e,a=this),o instanceof l)return a=a.simplify(),a instanceof l&&o.row==a.row&&o.col==a.col&&o.sheet==a.sheet;if(o instanceof u){if(a instanceof u)return a.topLeft.eq(o.topLeft)&&a.bottomRight.eq(o.bottomRight);if(a instanceof c)return a.single()&&o.eq(a.refs[0])}else if(o instanceof c&&a instanceof c){if(t=o.refs,n=a.refs,t.length!=n.length)return!1;for(i=0,r=t.length;i<r;i++)if(!t[i].eq(n[i]))return!1;return!0}return o===a},concat:function(e){return new c([this,e])},replaceAt:function(e,t){return t},forEachColumnIndex:function(e){this.forEachAxisIndex("col",e)},forEachRowIndex:function(e){this.forEachAxisIndex("row",e)},forEachAxisIndex:function(e,t){var n=[],i="row"===e?"forEachRow":"forEachColumn";this[i](function(t){var i=t.first()[e];n.indexOf(i)===-1&&n.push(i)}),n.sort(function(e,t){return e>t?1:e<t?-1:0}).forEach(t)},valid:function(){return!1},renameSheet:function(e,t){if(this.sheet&&this.sheet.toLowerCase()==e.toLowerCase())return this.sheet=t,!0}}),o.display=n,s=new(o.extend({init:function(){},print:function(){return"#NULL!"},eq:function(e){return e===this},forEach:function(){}})),a=o.extend({ref:"name",init:function(e){this.name=e},clone:function(){return new a(this.name).setSheet(this.sheet,this.hasSheet())},print:function(){var e=t(this.name);return this.hasSheet()&&(e=t(this.sheet)+"!"+e),e}}),l=o.extend({ref:"cell",init:function(e,t,n){this.row=e,this.col=t,this.rel=n||0},clone:function(){return new l(this.row,this.col,this.rel).setSheet(this.sheet,this.hasSheet())},intersect:function(e){return e instanceof l?this.eq(e)?this:s:e.intersect(this)},print:function(e,i,r){var o,s,a=this.col,l=this.row,u=this.rel;return null==e&&u?(s=this.hasSheet()?t(this.sheet)+"!":"",a=isFinite(a)?1&u?"C["+a+"]":"C"+(a+1):"",l=isFinite(l)?2&u?"R["+l+"]":"R"+(l+1):"",s+l+a):(o=this.absolute(e,i),r?(l=o.row%1048576,a=o.col%16384,l<0&&(l+=1048576),a<0&&(a+=16384),n(this._hasSheet&&this.sheet,l,a,u)):o.valid()?n(this._hasSheet&&this.sheet,o.row,o.col,u):"#REF!")},absolute:function(e,t){var n=this.clone();return n.rel&!1?n:(1&n.rel&&(n.col=(n.col+t)%16384),2&n.rel&&(n.row=(n.row+e)%1048576),n.rel=0,n)},toRangeRef:function(){return new u(this,this)},relative:function(e,t,n){var i,r;return null==n&&(n=this.rel),i=2&n?this.row-e:this.row,r=1&n?this.col-t:this.col,new l(i,r,n).setSheet(this.sheet,this.hasSheet())},height:function(){return 1},width:function(){return 1},toString:function(){return n(null,this.row,this.col,3)},isCell:function(){return!0},leftColumn:function(){return this},rightColumn:function(){return this},topRow:function(){return this},bottomRow:function(){return this},forEachRow:function(e){e(this.toRangeRef())},forEachColumn:function(e){e(this.toRangeRef())},adjust:function(e,t,n,i,r,o,a){var l=this.absolute(e,t);if(r){if(l.row>=o){if(a<0&&l.row<o-a)return s;l.row+=a}}else if(l.col>=o){if(a<0&&l.col<o-a)return s;l.col+=a}return null!=n&&null!=i&&(l=l.relative(n,i,this.rel)),l},valid:function(){if(this.rel)throw Error("valid() called on relative reference");var e=this.col,t=this.row;return!(isFinite(e)&&e<0||isFinite(t)&&t<0)}}),u=o.extend({ref:"range",init:function(e,t){e._hasSheet&&t._hasSheet&&e.sheet.toLowerCase()!=t.sheet.toLowerCase()&&(this.endSheet=t.sheet),this.topLeft=new l(e.row,e.col,e.rel),this.bottomRight=new l(t.row,t.col,t.rel),this.normalize()},clone:function(){return new u(this.topLeft.clone(),this.bottomRight.clone()).setSheet(this.sheet,this.hasSheet())},_containsRange:function(e){return this._containsCell(e.topLeft)&&this._containsCell(e.bottomRight)},_containsCell:function(e){return e.sheet==this.sheet&&e.row>=this.topLeft.row&&e.col>=this.topLeft.col&&e.row<=this.bottomRight.row&&e.col<=this.bottomRight.col},contains:function(e){if(e instanceof Array){var t=this;return e.some(function(e){return t.contains(e)})}return e instanceof l?this._containsCell(e):e instanceof u&&this._containsRange(e)},_intersectRange:function(e){var t,n,i,r,o,a,c,h;return this.sheet!=e.sheet?s:(t=this.topLeft.col,n=this.topLeft.row,i=this.bottomRight.col,r=this.bottomRight.row,o=e.topLeft.col,a=e.topLeft.row,c=e.bottomRight.col,h=e.bottomRight.row,t<=c&&o<=i&&n<=h&&a<=r?new u(new l(Math.max(n,a),Math.max(t,o)),new l(Math.min(r,h),Math.min(i,c))).setSheet(this.sheet,this.hasSheet()):s);
},intersect:function(e){return e instanceof l?this._containsCell(e)?e:s:e instanceof u?this._intersectRange(e).simplify():e instanceof c?e.intersect(this):s},simplify:function(){return this.isCell()?new l(this.topLeft.row,this.topLeft.col,this.topLeft.rel).setSheet(this.sheet,this.hasSheet()):this},normalize:function(){var e,t=this.topLeft,n=this.bottomRight,i=t.row,r=t.col,o=n.row,s=n.col,a=2&t.rel,u=1&t.rel,c=2&n.rel,h=1&n.rel,d=!1;return a===c&&u===h&&(i>o&&(d=!0,e=i,i=o,o=e,e=a,a=c,c=e),r>s&&(d=!0,e=r,r=s,s=e,e=u,u=h,h=e),d&&(this.topLeft=new l(i,r,u|a),this.bottomRight=new l(o,s,h|c))),this},print:function(e,n,i){if(i||this.absolute(e,n).valid()){var r=this.topLeft.print(e,n,i)+":"+this.bottomRight.print(e,n,i);return this.hasSheet()&&(r=t(this.sheet)+(this.endSheet?":"+t(this.endSheet):"")+"!"+r),r}return"#REF!"},absolute:function(e,t){return new u(this.topLeft.absolute(e,t),this.bottomRight.absolute(e,t)).setSheet(this.sheet,this.hasSheet())},relative:function(e,t,n,i){return null==i&&(i=n),new u(this.topLeft.relative(e,t,n),this.bottomRight.relative(e,t,i)).setSheet(this.sheet,this.hasSheet())},height:function(){if(this.topLeft.rel!=this.bottomRight.rel)throw Error("Mixed relative/absolute references");return this.bottomRight.row-this.topLeft.row+1},width:function(){if(this.topLeft.rel!=this.bottomRight.rel)throw Error("Mixed relative/absolute references");return this.bottomRight.col-this.topLeft.col+1},collapse:function(){return this.topLeft.toRangeRef()},leftColumn:function(){return new u(this.topLeft,new l(this.bottomRight.row,this.topLeft.col))},rightColumn:function(){return new u(new l(this.topLeft.row,this.bottomRight.col),this.bottomRight)},topRow:function(){return new u(this.topLeft,new l(this.topLeft.row,this.bottomRight.col))},bottomRow:function(){return new u(new l(this.bottomRight.row,this.topLeft.col),this.bottomRight)},toRangeRef:function(){return this},toRow:function(e){return e+=Math.max(0,this.topLeft.row),new u(new l(e,this.topLeft.col),new l(e,this.bottomRight.col)).setSheet(this.sheet,this.hasSheet())},toColumn:function(e){return e+=Math.max(0,this.topLeft.col),new u(new l(this.topLeft.row,e),new l(this.bottomRight.row,e)).setSheet(this.sheet,this.hasSheet())},toCell:function(e,t){return e+=Math.max(0,this.topLeft.row),t+=Math.max(0,this.topLeft.col),new l(e,t,0).setSheet(this.sheet,this.hasSheet())},forEachRow:function(e){var t,n=this.topLeft.row,i=this.bottomRight.row,r=this.topLeft.col,o=this.bottomRight.col;for(t=n;t<=i;t++)e(new u(new l(t,r),new l(t,o)))},forEachColumn:function(e){var t,n=this.topLeft.row,i=this.bottomRight.row,r=this.topLeft.col,o=this.bottomRight.col;for(t=r;t<=o;t++)e(new u(new l(n,t),new l(i,t)))},intersecting:function(e){return e.filter(function(e){return e.toRangeRef().intersects(this)},this)},union:function(e,t){var n,i=this.intersecting(e),r=this.topLeft.row,o=this.topLeft.col,s=this.bottomRight.row,a=this.bottomRight.col,c=!1;return i.forEach(function(e){e=e.toRangeRef(),e.topLeft.row<r&&(c=!0,r=e.topLeft.row),e.topLeft.col<o&&(c=!0,o=e.topLeft.col),e.bottomRight.row>s&&(c=!0,s=e.bottomRight.row),e.bottomRight.col>a&&(c=!0,a=e.bottomRight.col),t&&t(e)}),n=new u(new l(r,o),new l(s,a)),c?n.union(e,t):n},resize:function(e){function t(e){return e||0}var n=Math.max.bind(Math,0),i=this.topLeft.row+t(e.top),r=this.topLeft.col+t(e.left),o=this.bottomRight.row+t(e.bottom),a=this.bottomRight.col+t(e.right);return r<0&&a<0||i<0&&o<0?s:i<=o&&r<=a?new u(new l(n(i),n(r)),new l(n(o),n(a))):s},move:function(e,t){return new u(new l(this.topLeft.row+e,this.topLeft.col+t),new l(this.bottomRight.row+e,this.bottomRight.col+t))},first:function(){return this.topLeft.clone().setSheet(this.sheet,this.hasSheet())},isCell:function(){return!this.endSheet&&this.topLeft.eq(this.bottomRight)},toString:function(){return this.topLeft+":"+this.bottomRight},adjust:function(e,t,n,i,r,o,a){var l=this.topLeft.adjust(e,t,n,i,r,o,a),c=this.bottomRight.adjust(e,t,n,i,r,o,a);return l===s&&c===s?s:(l===s?(l=this.topLeft.absolute(e,t),r?l.row=o:l.col=o,null!=n&&null!=i&&(l=l.relative(n,i,this.topLeft.rel))):c===s&&(c=this.bottomRight.absolute(e,t),r?c.row=o-1:c.col=o-1,null!=n&&null!=i&&(c=c.relative(n,i,this.bottomRight.rel))),new u(l,c).setSheet(this.sheet,this.hasSheet()).simplify())},valid:function(){return this.topLeft.valid()&&this.bottomRight.valid()}}),c=o.extend({init:function(e){this.refs=e,this.length=e.length},clone:function(){return new c(this.refs.slice())},intersect:function(e){var t,n,i=[];for(t=0;t<this.length;++t)n=e.intersect(this.refs[t]),n!==s&&i.push(n);return i.length>0?new c(i).simplify():s},simplify:function(){var e=new c(this.refs.reduce(function(e,t){return t=t.simplify(),t!==s&&e.push(t),e},[]));return e.empty()?s:e.single()?e.refs[0]:e},absolute:function(e,t){return new c(this.refs.map(function(n){return n.absolute(e,t)}))},forEach:function(e,t){this.refs.forEach(function(n){n instanceof c?n.forEach(e,t):e.call(t,n)},t)},toRangeRef:function(){return this.refs[0].toRangeRef()},contains:function(e){return this.refs.some(function(t){return t.contains(e)})},map:function(e,t){var n=[];return this.forEach(function(i){n.push(e.call(t,i))}),new c(n)},first:function(){return this.refs[0].first()},lastRange:function(){return this.refs[this.length-1]},size:function(){return this.length},single:function(){return 1==this.length},empty:function(){return 0===this.length},isCell:function(){return this.single()&&this.refs[0].isCell()},rangeAt:function(e){return this.refs[e]},nextRangeIndex:function(e){return e===this.length-1?0:e+1},previousRangeIndex:function(e){return 0===e?this.length-1:e-1},concat:function(e){return new c(this.refs.concat([e]))},print:function(e,t,n){return this.refs.map(function(i){return i.print(e,t,n)}).join(",")},replaceAt:function(e,t){var n=this.refs.slice();return n.splice(e,1,t),new c(n)},leftColumn:function(){return this.map(function(e){return e.leftColumn()})},rightColumn:function(){return this.map(function(e){return e.rightColumn()})},topRow:function(){return this.map(function(e){return e.topRow()})},bottomRow:function(){return this.map(function(e){return e.bottomRow()})},forEachRow:function(e){this.forEach(function(t){t.forEachRow(e)})},forEachColumn:function(e){this.forEach(function(t){t.forEachColumn(e)})},adjust:function(e,t,n,i,r,o,s){return this.map(function(a){return a.adjust(e,t,n,i,r,o,s)}).simplify()},toString:function(){return this.refs.map(function(e){return""+e}).join(", ")},valid:function(){for(var e=this.refs.length;--e>=0;)if(this.refs[e].valid())return!1;return!0},renameSheet:function(e,t){this.refs.forEach(function(n){n.renameSheet(e,t)})}}),i.NULLREF=s,i.SHEETREF=new u(new l(0,0),new l(1/0,1/0)),i.FIRSTREF=new l(0,0),i.Ref=o,i.NameRef=a,i.CellRef=l,i.RangeRef=u,i.UnionRef=c,i.SHEETREF.print=function(){return"#SHEET"})},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/autofillcalculator.min",["kendo.core.min"],e)}(function(){!function(e){var t,n,i;e.support.browser.msie&&e.support.browser.version<9||(t=e.spreadsheet.RangeRef,n=e.spreadsheet.CellRef,i=e.Class.extend({init:function(e){this._grid=e},rectIsVertical:function(e,t,n,i){var r=this._grid.rectangle(e.toRangeRef()),o=this._grid.rectangle(t.toRangeRef());return Math.abs(o[i]-r[i])>Math.abs(r[n]-o[n])},autoFillDest:function(e,i){var r,o,s,a,l,u,c,h=e.topLeft,d=e.bottomRight,f=i.row>=h.row,p=i.col>=h.col;return r=f?p?4:3:p?2:1,4===r?(o=h,s=d,l=i.row>s.row||i.col>s.col,l&&(i=new n(Math.max(i.row,s.row),Math.max(i.col,s.col))),a=this.rectIsVertical(s,i,"right","bottom")?new n(i.row,s.col):new n(s.row,i.col)):3===r?(u=new n(h.col,d.row),i.row>d.row&&this.rectIsVertical(u,i,"left","bottom")?(o=h,a=new n(i.row,d.col)):(o=d,a=new n(h.row,i.col))):2===r?(c=new n(h.row,d.col),i.col>d.col&&!this.rectIsVertical(c,i,"right","top")?(o=h,a=new n(d.row,i.col)):(o=d,a=new n(i.row,h.col))):(o=d,a=this.rectIsVertical(h,i,"left","top")?new n(i.row,h.col):new n(h.row,i.col)),this._grid.normalize(new t(o,a))}}),e.spreadsheet.AutoFillCalculator=i)}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/navigator.min",["kendo.core.min","spreadsheet/autofillcalculator.min"],e)}(function(){!function(e){var t,n,i,r;e.support.browser.msie&&e.support.browser.version<9||(t=e.spreadsheet.RangeRef,n=e.spreadsheet.CellRef,i=e.Class.extend({init:function(e,t,n,i){this.rangeGetter=n,this.prevLeft=function(n){var r=i(this.range(n)),o=this.range(t.prevVisible(r.topLeft[e]));return i(o).topLeft[e]},this.nextRight=function(n){var r=i(this.range(n)),o=this.range(t.nextVisible(r.bottomRight[e]));return i(o).bottomRight[e]},this.nextLeft=function(n){var r=i(this.range(n));return t.nextVisible(r.bottomRight[e])},this.prevRight=function(n){var r=i(this.range(n));return t.prevVisible(r.topLeft[e])}},boundary:function(e,t){this.top=e,this.bottom=t},range:function(e){return this.rangeGetter(e,this.top,this.bottom)}}),r=e.Class.extend({init:function(t){this._sheet=t,this.columns=this._sheet._grid._columns,this.autoFillCalculator=new e.spreadsheet.AutoFillCalculator(t._grid),this.colEdge=new i("col",this._sheet._grid._columns,this.columnRange.bind(this),this.union.bind(this)),this.rowEdge=new i("row",this._sheet._grid._rows,this.rowRange.bind(this),this.union.bind(this))},height:function(e){this._viewPortHeight=e},union:function(e){return this._sheet.unionWithMerged(e)},columnRange:function(e,t,n){return this._sheet._ref(t,e,n-t,1)},rowRange:function(e,t,n){return this._sheet._ref(e,t,1,n-t)},selectionIncludesMergedCells:function(){return this._sheet.select().contains(this._sheet._mergedCells)},setSelectionValue:function(e){var t=this._sheet.selection();setTimeout(function(){t.value(e())})},selectAll:function(){this._sheet.select(this._sheet._sheetRef)},select:function(e,t,n){e=this.refForMode(e,t),n&&(e=this._sheet.select().concat(e)),this._sheet.select(e)},refForMode:function(e,t){var n=this._sheet._grid;switch(t){case"range":e=n.normalize(e);break;case"row":e=n.rowRef(e.row);break;case"column":e=n.colRef(e.col);break;case"sheet":e=this._sheet._sheetRef}return e},startSelection:function(e,n,i,r){if("autofill"==n)this._sheet.startAutoFill();else if(r&&"range"==n){var o=new t(this._sheet.activeCell().first(),e);this._sheet.select(o,!1,!1),this._sheet.startSelection()}else this._sheet.startSelection(),this.select(e,n,i)},completeSelection:function(){this._sheet.completeSelection()},selectForContextMenu:function(e,t){var n=this._sheet;n._activeDrawing=null,n.select().contains(this.refForMode(e,t))||this.select(e,t)},selectDrawingForContextMenu:function(e){var t=this._sheet;t._activeDrawing=e,t.triggerChange({selection:!0})},modifySelection:function(e){var n,i,r=this.determineDirection(e),o=this._sheet,s=this._viewPortHeight,a=o._grid._rows,l=o._grid._columns,u=o.currentOriginalSelectionRange(),c=o.select().toRangeRef(),h=o.activeCell(),d=u.topLeft.clone(),f=u.bottomRight.clone();switch(this.colEdge.boundary(c.topLeft.row,c.bottomRight.row),this.rowEdge.boundary(c.topLeft.col,c.bottomRight.col),r){case"expand-left":d.col=this.colEdge.prevLeft(d.col),n=d;break;case"shrink-right":d.col=this.colEdge.nextLeft(d.col),n=d;break;case"expand-right":f.col=this.colEdge.nextRight(f.col),n=f;break;case"shrink-left":f.col=this.colEdge.prevRight(f.col),n=f;break;case"expand-up":d.row=this.rowEdge.prevLeft(d.row),n=d;break;case"shrink-down":d.row=this.rowEdge.nextLeft(d.row),n=d;break;case"expand-down":f.row=this.rowEdge.nextRight(f.row),n=f;break;case"shrink-up":f.row=this.rowEdge.prevRight(f.row),n=f;break;case"expand-page-up":d.row=a.prevPage(d.row,s);break;case"shrink-page-up":f.row=a.prevPage(f.row,s);break;case"expand-page-down":f.row=a.nextPage(f.row,s);break;case"shrink-page-down":d.row=a.nextPage(d.row,s);break;case"first-col":d.col=l.firstVisible(),f.col=h.bottomRight.col,n=d;break;case"last-col":f.col=l.lastVisible(),d.col=h.topLeft.col,n=f;break;case"first-row":d.row=a.firstVisible(),f.row=h.bottomRight.row,n=d;break;case"last-row":f.row=a.lastVisible(),d.row=h.topLeft.row,n=f;break;case"last":f.row=a.lastVisible(),f.col=l.lastVisible(),d=h.topLeft,n=f;break;case"first":d.row=a.firstVisible(),d.col=l.firstVisible(),f=h.bottomRight,n=d}return i=new t(d,f),this.union(i).intersects(h)?(n&&o.focus(n),void this.updateCurrentSelectionRange(i)):void this.modifySelection(r.replace("shrink","expand"))},moveActiveCell:function(e){var t=this._sheet,i=t.activeCell(),r=i.topLeft,o=i.bottomRight,s=t.originalActiveCell(),a=t._grid._rows,l=t._grid._columns,u=s.row,c=s.col;switch(e){case"left":c=l.prevVisible(r.col);break;case"up":u=a.prevVisible(r.row);break;case"right":c=l.nextVisible(o.col);break;case"down":u=a.nextVisible(o.row);break;case"first-col":c=l.firstVisible();break;case"last-col":c=l.lastVisible();break;case"first-row":u=a.firstVisible();break;case"last-row":u=a.lastVisible();break;case"last":u=a.lastVisible(),c=l.lastVisible();break;case"first":u=a.firstVisible(),c=l.firstVisible();break;case"next-page":u=a.nextPage(o.row,this._viewPortHeight);break;case"prev-page":u=a.prevPage(o.row,this._viewPortHeight)}t.select(new n(u,c))},navigateInSelection:function(e){function t(e){i=e.topLeft,r=e.bottomRight}var i,r,o,s,a,l=this._sheet,u=l.activeCell(),c=u.topLeft,h=l.originalActiveCell(),d=l._grid._rows,f=l._grid._columns,p=h.row,m=h.col,g=c.col,v=c.row;for(t(l.currentNavigationRange()),s=!1;!s;){switch(a=new n(p,m),e){case"next":r.eq(a)?(t(l.nextNavigationRange()),p=i.row,m=i.col):(m=f.nextVisible(g),(m==g||m>r.col)&&(m=i.col,o=d.nextVisible(p),p=o==p||o>r.row?i.row:o));break;case"previous":i.eq(a)?(t(l.previousNavigationRange()),p=r.row,m=r.col):(m=f.prevVisible(g),(m==g||m<i.col)&&(m=r.col,o=d.prevVisible(p),p=o==p||o<i.row?r.row:o));break;case"lower":r.eq(a)?(t(l.nextNavigationRange()),p=i.row,m=i.col):(p=d.nextVisible(v),(p==v||p>r.row)&&(p=i.row,o=f.nextVisible(m),m=o==m||o>r.col?i.col:o));break;case"upper":i.eq(a)?(t(l.previousNavigationRange()),p=r.row,m=r.col):(p=d.prevVisible(v),(p==v||p<i.row)&&(p=r.row,o=f.prevVisible(m),m=o==m||o<i.col?r.col:o));break;default:throw Error("Unknown entry navigation: "+e)}s=!this.shouldSkip(p,m),g=m,v=p}l.singleCellSelection()?l.select(new n(p,m)):l.activeCell(new n(p,m))},extendSelection:function(e,n){var i,r=this._sheet,o=r._grid;return"autofill"===n?void this.resizeAutoFill(e):("range"===n?e=o.normalize(e):"row"===n?e=o.rowRef(e.row).bottomRight:"column"===n&&(e=o.colRef(e.col).bottomRight),i=r.originalActiveCell().toRangeRef(),void this.updateCurrentSelectionRange(new t(i.topLeft,e)))},shouldSkip:function(e,t){var i,r;return!(!this._sheet.isHiddenRow(e)&&!this._sheet.isHiddenColumn(t))||(i=new n(e,t),r=!1,this._sheet.forEachMergedCell(function(e){e.intersects(i)&&!e.collapse().eq(i)&&(r=!0)}),r)},resizeAutoFill:function(e){var t,n,i,r=this._sheet,o=r.select(),s=r._autoFillOrigin,a=this.autoFillCalculator.autoFillDest(o,e),l=this.punch(o,a);l||(i=r.range(a)._previewFillFrom(r.range(s)),i&&(n=i.direction,t=i.hint)),r.updateAutoFill(a,l,t,n)},determineDirection:function(e){var t=this._sheet.currentSelectionRange(),n=this._sheet.activeCell(),i=n.topLeft.col==t.topLeft.col,r=n.bottomRight.col==t.bottomRight.col,o=n.topLeft.row==t.topLeft.row,s=n.bottomRight.row==t.bottomRight.row;switch(e){case"left":e=r?"expand-left":"shrink-left";break;case"right":e=i?"expand-right":"shrink-right";break;case"up":e=s?"expand-up":"shrink-up";break;case"down":e=o?"expand-down":"shrink-down";break;case"prev-page":e=s?"expand-page-up":"shrink-page-up";break;case"next-page":e=o?"expand-page-down":"shrink-page-down"}return e},updateCurrentSelectionRange:function(e){var t=this._sheet;t.select(t.originalSelect().replaceAt(t.selectionRangeIndex(),e),!1)},punch:function(e,i){var r,o,s;return i.topLeft.eq(e.topLeft)&&(i.bottomRight.row<e.bottomRight.row?(o=this.rowEdge.nextRight(i.bottomRight.row),r=new t(new n(o,e.topLeft.col),e.bottomRight)):i.bottomRight.col<e.bottomRight.col&&(s=this.colEdge.nextRight(i.bottomRight.col),r=new t(new n(e.topLeft.row,s),e.bottomRight))),r}}),e.spreadsheet.SheetNavigator=r)}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/axismanager.min",["kendo.core.min"],e)}(function(){!function(e){if(!(e.support.browser.msie&&e.support.browser.version<9)){var t=e.Class.extend({init:function(e){this._sheet=e},forEachSelectedColumn:function(e){var t=this._sheet;t.batch(function(){t.select().forEachColumnIndex(function(n,i){e(t,n,i)})},{layout:!0,recalc:!0})},forEachSelectedRow:function(e){var t=this._sheet;t.batch(function(){t.select().forEachRowIndex(function(n,i){e(t,n,i)})},{layout:!0,recalc:!0})},includesHiddenColumns:function(e){return this._sheet._grid._columns.includesHidden(e.topLeft.col,e.bottomRight.col)},includesHiddenRows:function(e){return this._sheet._grid._rows.includesHidden(e.topLeft.row,e.bottomRight.row)},selectionIncludesHiddenColumns:function(){return this.includesHiddenColumns(this._sheet.select())},selectionIncludesHiddenRows:function(){return this.includesHiddenRows(this._sheet.select())},deleteSelectedColumns:function(){var e=[],t=0;return this.forEachSelectedColumn(function(n,i){if(i-=t,!n.isHiddenColumn(i)){t++;var r=[];e.unshift({index:i,formulas:r,width:n.columnWidth(i)}),n._saveModifiedFormulas(r,function(){n.deleteColumn(i)})}}),e},deleteSelectedRows:function(){var e=[],t=0;return this.forEachSelectedRow(function(n,i){if(i-=t,!n.isHiddenRow(i)){t++;var r=[];e.unshift({index:i,formulas:r,height:n.rowHeight(i)}),n._saveModifiedFormulas(r,function(){n.deleteRow(i)})}}),e},hideSelectedColumns:function(){var t,n,i,r,o,s,a;for(this.forEachSelectedColumn(function(e,t){e.hideColumn(t)}),t=this._sheet,n=t.select().toRangeRef(),i=n.topLeft.col,r=n.bottomRight.col,o=null;;){if(s=r<t._columns._count,a=i>=0,!a&&!s)break;if(s&&!t.isHiddenColumn(r)){o=r;break}if(a&&!t.isHiddenColumn(i)){o=i;break}i--,r++}null!==o&&(n=new e.spreadsheet.RangeRef(new e.spreadsheet.CellRef(0,o),new e.spreadsheet.CellRef(t._rows._count-1,o)),t.range(n).select())},hideSelectedRows:function(){var t,n,i,r,o,s,a;for(this.forEachSelectedRow(function(e,t){e.hideRow(t)}),t=this._sheet,n=t.select().toRangeRef(),i=n.topLeft.row,r=n.bottomRight.row,o=null;;){if(s=r<t._rows._count,a=i>=0,!a&&!s)break;if(s&&!t.isHiddenRow(r)){o=r;break}if(a&&!t.isHiddenRow(i)){o=i;break}i--,r++}null!==o&&(n=new e.spreadsheet.RangeRef(new e.spreadsheet.CellRef(o,0),new e.spreadsheet.CellRef(o,t._columns._count-1)),t.range(n).select())},unhideSelectedColumns:function(){this.forEachSelectedColumn(function(e,t){e.unhideColumn(t)})},unhideSelectedRows:function(){this.forEachSelectedRow(function(e,t){e.unhideRow(t)})},preventAddRow:function(){var e=this._sheet.select().toRangeRef(),t=e.height();return this._sheet.preventInsertRow(0,t)},preventAddColumn:function(){var e=this._sheet.select().toRangeRef(),t=e.width();return this._sheet.preventInsertColumn(0,t)},addColumnLeft:function(){var e,t=this._sheet,n=0;return t.batch(function(){t.select().forEachColumnIndex(function(i){e||(e=i),t.insertColumn(e),++n})},{recalc:!0,layout:!0}),{base:e,count:n}},addColumnRight:function(){var e,t=this._sheet,n=0;return t.batch(function(){t.select().forEachColumnIndex(function(t){e=t+1,++n});for(var i=0;i<n;++i)t.insertColumn(e)},{recalc:!0,layout:!0}),{base:e,count:n}},addRowAbove:function(){var e,t=this._sheet,n=0;return t.batch(function(){t.select().forEachRowIndex(function(i){e||(e=i),t.insertRow(e),++n})},{recalc:!0,layout:!0}),{base:e,count:n}},addRowBelow:function(){var e,t=this._sheet,n=0;return t.batch(function(){t.select().forEachRowIndex(function(t){e=t+1,++n});for(var i=0;i<n;++i)t.insertRow(e)},{recalc:!0,layout:!0}),{base:e,count:n}}});e.spreadsheet.AxisManager=t}}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/clipboard.min",["kendo.core.min"],e)}(function(){!function(e){function t(){var e=new u(0,0,0);return{ref:e,mergedCells:[],data:[],foreign:!0,origRef:e.toRangeRef()}}function n(e,t,n,i){var r,o=e.data||(e.data=[]);o[t]||(o[t]=[]),o[t][n]=i,r=e.origRef.bottomRight,r.row=Math.max(r.row,t),r.col=Math.max(r.col,n)}function i(e){return e.replace(/^-(?:ms|moz|webkit)-/,"")}function r(e){var t={};return["borderBottom","borderRight","borderLeft","borderTop"].forEach(function(n){t[n]="none"==e[n+"Style"]?null:{size:1,color:e[n+"Color"]}}),t}function o(e,t,n,o,s){var a=window.getComputedStyle(n),l=n.innerText.replace(/\t$/,""),u=r(a),c={value:""===l?null:l,borderTop:u.borderTop||o.get(e,t)||null,borderBottom:u.borderBottom||o.get(e+1,t)||null,borderLeft:u.borderLeft||s.get(e,t)||null,borderRight:u.borderRight||s.get(e,t+1)||null,fontSize:parseInt(a["font-size"],10)};return o.set(e,t,c.borderTop),o.set(e+1,t,c.borderBottom),s.set(e,t,c.borderLeft),s.set(e,t+1,c.borderRight),"rgb(0, 0, 0)"!==a["background-color"]&&"rgba(0, 0, 0, 0)"!==a["background-color"]&&(c.background=a["background-color"]),"rgb(0, 0, 0)"!==a.color&&"rgba(0, 0, 0, 0)"!==a.color&&(c.color=a.color),"underline"==a["text-decoration"]&&(c.underline=!0),"italic"==a["font-style"]&&(c.italic=!0),"bold"==a["font-weight"]&&(c.bold=!0),"right"!==i(a["text-align"])&&(c.textAlign=i(a["text-align"])),"middle"!==a["vertical-align"]&&(c.verticalAlign=a["vertical-align"]),"normal"!==a["word-wrap"]&&(c.wrap=!0),c}function s(i){var r,s,a,l,c,h,d,f,p,m,g,v,b,w=t(),y=[],_=0,k=0;for(r=0;r<i.rows.length;++r)y.push([]);for(s=new e.spreadsheet.calc.runtime.Matrix,a=new e.spreadsheet.calc.runtime.Matrix,l=0;l<i.rows.length;++l,++_)for(c=i.rows[l],k=0,h=0;h<c.cells.length;++h){for(d=c.cells[h],f=d.rowSpan,p=d.colSpan;y[_][k];)k++;for(m=d.getAttribute("style"),g=/mso-ignore:colspan/.test(m),n(w,_,k,o(_,k,d,s,a)),(f>1||p>1&&!g)&&w.mergedCells.push(""+new e.spreadsheet.RangeRef(new u(_,k),new u(_+f-1,k+p-1))),v=_+f;--v>=_;)for(b=k+p;--b>=k;)v<y.length&&(y[v][b]=!0,v==_&&b==k||n(w,v,b,{}))}return w}function a(e){var i,r,o,s,a=t();if(e.indexOf("\t")===-1&&e.indexOf("\n")==-1)n(a,0,0,{value:e});else for(i=e.split("\n"),r=0;r<i.length;r++)for(o=i[r].split("\t"),s=0;s<o.length;s++)n(a,r,s,{value:o[s]});return a}var l,u,c;e.support.browser.msie&&e.support.browser.version<9||(l=e.jQuery,u=e.spreadsheet.CellRef,c=e.Class.extend({init:function(t){this._content={},this._externalContent={},this._internalContent={},this.workbook=t,this.origin=e.spreadsheet.NULLREF,this.iframe=document.createElement("iframe"),this.iframe.className="k-spreadsheet-clipboard-paste",this.menuInvoked=!1,this._uid=e.guid(),document.body.appendChild(this.iframe)},destroy:function(){document.body.removeChild(this.iframe)},canCopy:function(){var t={canCopy:!0},n=this.workbook.activeSheet().select();return n===e.spreadsheet.NULLREF&&(t.canCopy=!1),n instanceof e.spreadsheet.UnionRef&&(t.canCopy=!1,t.multiSelection=!0),this.menuInvoked&&(t.canCopy=!1,t.menuInvoked=!0),t},canPaste:function(){var e=this.workbook.activeSheet(),t=this.pasteRef(),n=e.range(t),i={canPaste:!0,pasteOnMerged:!1,pasteOnDisabled:!1};return n.enable()||(i.canPaste=!1,i.pasteOnDisabled=!0),t.eq(e.unionWithMerged(t))||(i.canPaste=!1,i.pasteOnMerged=!0),this.menuInvoked&&(i.canPaste=!1,i.menuInvoked=!0),(t.bottomRight.row>=e._rows._count||t.bottomRight.col>=e._columns._count)&&(i.canPaste=!1,i.overflow=!0),i},intersectsMerged:function(){var e,t=this.workbook.activeSheet();return this.parse(),this.origin=this._content.origRef,e=this.pasteRef(),!e.eq(t.unionWithMerged(e))},copy:function(){var e=this.workbook.activeSheet();this.origin=e.select(),this._internalContent=e.selection().getState(),delete this._externalContent.html,delete this._externalContent.plain},cut:function(){var e=this.workbook.activeSheet();this.copy(),e.range(e.select()).clear()},pasteRef:function(){var t,n,i,r,o=this.workbook.activeSheet();return this.origin===e.spreadsheet.NULLREF?o.select():(t=o.activeCell().first(),n=this.origin.first(),i=n.row-t.row,r=n.col-t.col,this.origin.relative(i,r,3))},paste:function(){var e=this.workbook.activeSheet(),t=this.pasteRef();e.range(t).setState(this._content,this),e.triggerChange({recalc:!0,ref:t})},external:function(e){return e&&(e.html||e.plain)?void(this._externalContent=e):this._externalContent},isExternal:function(){return!this._isInternal()},parse:function(){var e,n,i,r=t();this._isInternal()?r=this._internalContent:(e=this._externalContent,e.html?(n=this.iframe.contentWindow.document,n.open(),n.write(e.html),n.close(),i=n.querySelector("table"),r=i?s(i):a(e.plain)):r=a(e.plain),this.origin=r.origRef),this._content=r},_isInternal:function(){var e,t;return void 0===this._externalContent.html||(e=!!l("<div/>").html(this._externalContent.html).find("table.kendo-clipboard-"+this._uid).length,t=!!l("<div/>").html(this._externalContent.plain).find("table.kendo-clipboard-"+this._uid).length,e||t)}}),e.spreadsheet.Clipboard=c)}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/range.min",["kendo.core.min","util/text-metrics.min","util/main.min"],e)}(function(){!function(e){function t(e,n,i){for(;e<=n&&i(e);)e++;if(e>n)return[];for(var r=e+1;r<=n;++r)if(i(r))return[{begin:e,end:r-1}].concat(t(r+1,n,i));return[{begin:e,end:n}]}function n(e){return e.replace(/M/g,"m").replace(/'/g,'"').replace(/tt/,"am/pm")}function i(t){return!/^=/.test(t)&&/number|percent/.test(e.spreadsheet.calc.parse(null,0,0,t).type)}function r(t,n,i,r,o){var s={baselineMarkerSize:0,width:o===!0?n+"px":"auto","font-size":(r||12)+"px","font-family":i||"Arial","white-space":o===!0?"pre-wrap":"pre","overflow-wrap":o===!0?"break-word":"normal","word-wrap":o===!0?"break-word":"normal"};return e.util.measureText(t,s,{box:h,normalizeText:!1}).height}var o,s,a,l,u,c,h;e.support.browser.msie&&e.support.browser.version<9||(o=e.jQuery,s=e.spreadsheet.UnionRef,a=e.spreadsheet.CellRef,l=e.spreadsheet.RangeRef,u=["color","fontFamily","underline","italic","bold","textAlign","indent","verticalAlign","background","format","link","editor","borderTop","borderRight","borderBottom","borderLeft","comment"],c=e.Class.extend({init:function(e,t){this._sheet=t,this._ref=e},clone:function(){return new c(this._ref.clone(),this._sheet)},skipHiddenCells:function(){var e=[],n=this,i=n._sheet,r=i.isHiddenRow.bind(i),o=i.isHiddenColumn.bind(i);return n._ref.forEach(function(i){var s,u,c,h,d,f;for(i=n._normalize(i.toRangeRef()),s=i.topLeft,u=i.bottomRight,c=t(s.row,u.row,r),h=t(s.col,u.col,o),d=0;d<c.length;++d)for(f=0;f<h.length;++f)e.push(new l(new a(c[d].begin,h[f].begin),new a(c[d].end,h[f].end)))}),i.range(e.length>1?new s(e):e[0])},_normalize:function(e){return this._sheet._grid.normalize(e)},_set:function(e,t,n){var i=this,r=i._sheet;return i._ref.forEach(function(n){r._set(n.toRangeRef(),e,t)}),n||r.triggerChange({recalc:"formula"==e||"value"==e||"validation"==e,value:t,range:i,ref:i._ref}),i},_get:function(e){return this._sheet._get(this._ref.toRangeRef(),e)},_property:function(e,t){return void 0===t?this._get(e):this._set(e,t)},value:function(e){return void 0!==e&&this._set("formula",null,!0),this._property("value",e)},resize:function(e){var t=this._resizedRef(e);return new c(t,this._sheet)},_resizedRef:function(e){return this._ref.map(function(t){return t.toRangeRef().resize(e)})},input:function(t,r){var s,a,l,u,c,h,d;return r=o.extend({arrayFormula:!1},r),s=this._get("format"),void 0!==t?(l=this._ref.toRangeRef().topLeft,a=e.spreadsheet.calc.parse(this._sheet.name(),l.row,l.col,t,s),this._sheet.batch(function(){var i,o=null;"exp"==a.type?o=e.spreadsheet.calc.compile(a):"@"!=s?(i=s&&e.spreadsheet.formatting.type(a.value,s),"date"==a.type&&"date"!=i?this.format(a.format||n(e.culture().calendar.patterns.d)):"percent"==a.type&&"percent"!=i?this.format(100*a.value==(100*a.value|0)?"0%":"0.00%"):a.format&&(!s||a.currency||"number"==i&&"number"==a.type&&a.format.length>s.length)&&this.format(a.format)):"string"!=a.type&&(a.value=t),this.formula(o,r.arrayFormula),o||this.value(a.value)}.bind(this),{recalc:!0,value:t,ref:this._ref,editorChange:this._sheet.isInEditMode()}),this):(t=this._get("value"),u=this._get("formula"),c=s&&!u&&e.spreadsheet.formatting.type(t,s),u?t="="+u:s&&"date"==c&&(h=e.spreadsheet.formatting.text(t,s),a=e.spreadsheet.calc.parse(null,null,null,h,s),d=e.spreadsheet.formatting.text(a.value,s),h==d)?t=h:"date"===c?t=e.toString(e.spreadsheet.numberToDate(t),e.culture().calendar.patterns.d):"percent"===c?t=e.spreadsheet.calc.runtime.limitPrecision(100*t)+"%":"string"==typeof t&&(/^[=']/.test(t)||/^(?:true|false)$/i.test(t)||i(t))?t="'"+t:this._sheet._useCultureDecimals()&&"number"==typeof t&&t!=Math.floor(t)&&(t=(t+"").replace(".",e.culture().numberFormat["."])),t)},enable:function(t){return void 0===t?!e.util.withExit(function(e){this._sheet.forEach(this._ref,function(t,n,i){i.enable===!1&&e(!0)})},this):this._property("enable",t)},formula:function(e,t){var n,i,r=this;return void 0===e?(n=r._get("formula"),n?""+n:null):(t?(i=this._ref.toRangeRef(),e=r._sheet.range(i.topLeft)._set("formula",e)._get("formula"),e&&e.setArrayFormulaRange(i)):r._set("formula",e),r)},intersectingArrayFormula:function(){var t=this._ref.clone().simplify().setSheet(this._sheet.name());return e.util.withExit(function(n){this._sheet._forFormulas(function(i){var r=i.arrayFormulaRange;r&&(r=t.intersect(r))!==e.spreadsheet.NULLREF&&n({formula:i,intersection:r})})},this)},canEditArrayFormula:function(){var e=this.intersectingArrayFormula();return!e||e.formula.arrayFormulaRange.eq(e.intersection)},validation:function(e){if(void 0===e){var t=this._get("validation");return t?t.toJSON():null}return this._property("validation",e)},_getValidationState:function(){var e,t,n,i=this._ref.toRangeRef(),r=i.topLeft.row,o=i.topLeft.col,s=i.bottomRight.row,a=i.bottomRight.col;for(e=o;e<=a;e++)for(t=r;t<=s;t++)if(n=this._sheet._validation(t,e),n&&"reject"===n.type&&n.value===!1)return n;return!1},merge:function(){return this._ref=this._sheet._merge(this._ref),this},unmerge:function(){var e=this._sheet._mergedCells;return this._ref.forEach(function(t){t.toRangeRef().intersecting(e).forEach(function(t){e.splice(e.indexOf(t),1)})}),this._sheet.triggerChange({}),this},select:function(){return this._sheet.select(this._ref),this},values:function(t){var n,i,r,o,a,l,u,c,h,d;if(this._ref instanceof s)throw Error("Unsupported for multiple ranges.");if(this._ref===e.spreadsheet.NULLREF){if(void 0!==t)throw Error("Unsupported for NULLREF.");return[]}if(n=this._ref.toRangeRef(),i=n.topLeft.row,r=n.topLeft.col,o=n.bottomRight.row,a=n.bottomRight.col,void 0===t){for(t=Array(n.height()),c=0;c<t.length;c++)t[c]=Array(n.width());for(l=r;l<=a;l++)for(u=i;u<=o;u++)t[u-i][l-r]=this._sheet._value(u,l);return t}for(this._sheet._set(n,"formula",null),l=r;l<=a;l++)for(u=i;u<=o;u++)h=t[u-i],h&&(d=h[l-r],void 0!==d&&this._sheet._value(u,l,d));return this._sheet.triggerChange({recalc:!0,ref:n}),this},_properties:function(t,n){var i,r,o,a,l,u,c,h,d,f,p;if(this._ref instanceof s)throw Error("Unsupported for multiple ranges.");if(this._ref===e.spreadsheet.NULLREF){if(void 0!==t)throw Error("Unsupported for NULLREF.");return[]}if(i=this._ref.toRangeRef(),r=i.topLeft.row,o=i.topLeft.col,a=i.bottomRight.row,l=i.bottomRight.col,h=this._sheet,void 0===t)return t=Array(i.height()),h.forEach(i,function(e,n,i){e-=r,n-=o;var s=t[e]||(t[e]=[]);s[n]=i}),t;for(i=i.clone(),f=function(e){var t=d[e];i.topLeft.row=i.bottomRight.row=c,i.topLeft.col=i.bottomRight.col=u,"value"==e&&h._set(i,"formula",null),h._set(i,e,t)},u=o;u<=l;u++)if(n||!h.isHiddenColumn(u))for(c=r;c<=a;c++)!n&&h.isHiddenRow(c)||n&&h.isFilteredRow(c)||(p=t[c-r],p&&(d=p[u-o],d&&Object.keys(d).forEach(f)));return h.triggerChange({recalc:!0,ref:this._ref}),this},clear:function(e){var t,n,i;return e=e||{},t=e.clearAll||!Object.keys(e).length,n=this._sheet,i={recalc:t||e.contentsOnly,ref:this._ref},n.batch(function(){i.recalc&&this.formula(null),t&&this.validation(null),(t||e.formatOnly)&&(u.forEach(function(t){e.keepBorders&&/^border/i.test(t)||this[t](null);
}.bind(this)),this.fontSize(null),this.wrap(null),this.unmerge())}.bind(this),i),this},clearContent:function(){return this.clear({contentsOnly:!0})},clearFormat:function(){return this.clear({formatOnly:!0})},isSortable:function(){return!this.cantSort()},cantSort:function(){var t,n,i,r,o,l;if(this._ref instanceof s)return{code:"cantSortMultipleSelection",message:"Unsupported for multiple ranges."};if(this._ref===e.spreadsheet.NULLREF)return{code:"cantSortNullRef",message:"Unsupported for NULLREF."};t=this._sheet._getMergedCells(this._ref.toRangeRef()),n=t.primary,i=t.secondary,r=null,o=null,l={};try{this._sheet.forEach(this,function(e,s){var u=new a(e,s).print(),c=n[u];if(c){if(null===r)r=c.width(),o=c.height();else if(r!=c.width()||o!=c.height())throw l}else if(!i[u]&&t.hasMerged)throw l})}catch(u){if(u!==l)throw u;return{code:"cantSortMixedCells",message:"Unsupported for range containing cells of different shapes."}}return!1},sort:function(e){var t=this.cantSort();if(t)throw Error(t.message);return void 0===e&&(e={column:0}),e=e instanceof Array?e:[e],this._sheet._sortBy(this._ref.toRangeRef(),e.map(function(e,t){return"number"==typeof e&&(e={column:e}),{index:void 0===e.column?t:e.column,ascending:void 0===e.ascending||e.ascending}})),this},isFilterable:function(){return!(this._ref instanceof s)},filter:function(e){if(this._ref instanceof s)throw Error("Unsupported for multiple ranges.");return e===!1?this.clearFilters():(e=e===!0?[]:e instanceof Array?e:[e],this._sheet._filterBy(this._ref.toRangeRef(),e.map(function(e,t){return{index:void 0===e.column?t:e.column,filter:e.filter}}))),this},clearFilter:function(e){this._sheet.clearFilter(e)},clearFilters:function(){var e,t=this._sheet.filter(),n=[];if(t){for(e=0;e<t.columns.length;e++)n.push(t.columns[e].index);this._sheet.batch(function(){this.clearFilter(n),this._filter=null},{layout:!0,filter:!0})}},hasFilter:function(){var e=this._sheet.filter();return!!e},leftColumn:function(){return new c(this._ref.leftColumn(),this._sheet)},rightColumn:function(){return new c(this._ref.rightColumn(),this._sheet)},topRow:function(){return new c(this._ref.topRow(),this._sheet)},bottomRow:function(){return new c(this._ref.bottomRow(),this._sheet)},column:function(e){return new c(this._ref.toColumn(e),this._sheet)},row:function(e){return new c(this._ref.toRow(e),this._sheet)},forEachRow:function(e){this._ref.forEachRow(function(t){e(new c(t,this._sheet))}.bind(this))},forEachColumn:function(e){this._ref.forEachColumn(function(t){e(new c(t,this._sheet))}.bind(this))},sheet:function(){return this._sheet},topLeft:function(){return this._ref.toRangeRef().topLeft},intersectingMerged:function(){var e=this._sheet,t=[];return e._mergedCells.forEach(function(e){e.intersects(this._ref)&&t.push(""+e)}.bind(this)),t},getState:function(t){var n,i,r=this._ref.first(),o={ref:r,data:[],origRef:this._ref,rows:this._sheet._rows.getState()};return t?n="input"===t?["value","formula"]:"border"===t?["borderLeft","borderTop","borderRight","borderBottom"]:[t]:(n=e.spreadsheet.ALL_PROPERTIES,o.mergedCells=this.intersectingMerged()),i=o.data,this.forEachCell(function(t,o,s){var a={},l=t-r.row,u=o-r.col;i[l]||(i[l]=[]),i[l][u]=a,n.forEach(function(t){var n=void 0===s[t]?null:s[t];(n instanceof e.spreadsheet.calc.runtime.Formula||n instanceof e.spreadsheet.validation.Validation)&&(n=n.deepClone()),a[t]=n})}),o},setState:function(e,t){var n=this._sheet,i=this._ref.first(),r=e.ref.row-i.row,o=e.ref.col-i.col,s=t&&!t.isExternal(),a=t&&!s;n.batch(function(){var l,u;e.mergedCells&&this.unmerge(),t||this._sheet._rows.setState(e.rows),l=i.row,u=this.hasFilter(),e.data.forEach(function(r,o){if(!(u&&s&&n.isHiddenRow(e.ref.row+o))){var c=i.col;r.forEach(function(r,h){var d,f;if(!(u&&s&&n.isHiddenColumn(e.ref.col+h))){if(d=t?n.range(l,c):n.range(i.row+o,i.col+h),d.enable()){for(f in r)"value"!=f&&(t&&"enable"==f||d._set(f,r[f]));if(!r.formula)if(a)try{null==r.value?d._set("value",null):d.input(r.value)}catch(p){d._set("value",r.value)}else d._set("value",r.value)}c++}}),l++}}),e.mergedCells&&e.mergedCells.forEach(function(e){e=n._ref(e).relative(r,o,3),n.range(e).merge()},this)}.bind(this),{recalc:!0,ref:this._ref})},_adjustRowHeight:function(){var t=this,n=t._sheet,i=n._getMergedCells(t._ref.toRangeRef()),r=i.primary,o=i.secondary;n.batch(function(){t.forEachRow(function(t){var i=t._ref.topLeft.row,s=n.rowHeight(i);t.forEachCell(function(t,i,l){var u,c,h,d,f=new a(t,i).print();o[f]||(u=r[f],c=u?n._columns.sum(u.topLeft.col,u.bottomRight.col):n.columnWidth(i),h=l.value,l.format&&null!=h&&(h=e.spreadsheet.formatting.format(h,l.format)),d=e.spreadsheet.util.getTextHeight(h,c,l.fontFamily,l.fontSize,l.wrap),s=Math.max(s,d))}),n.rowHeight(i,s)})},{layout:!0})},forEachCell:function(e){this._ref.forEach(function(t){this._sheet.forEach(t.toRangeRef(),e.bind(this))}.bind(this))},hasValue:function(){var t=this._sheet._defaultCellStyle;return e.util.withExit(function(e){this.forEachCell(function(n,i,r){var o,s;for(o in r)s=r[o],void 0!==s&&null!==s&&s!==t[o]&&e(!0)})},this)},wrap:function(e){return void 0===e?!!this._property("wrap"):(this._property("wrap",e),null!==e&&this._adjustRowHeight(),this)},fontSize:function(e){return void 0===e?this._property("fontSize"):(this._property("fontSize",e),null!==e&&this._adjustRowHeight(),this)},draw:function(e,t){this._sheet.draw(this,e,t)},insideBorders:function(e){return this.insideVerticalBorders(e).insideHorizontalBorders(e)},insideVerticalBorders:function(e){return this._ref.forEach(function(t){t instanceof l&&t.width()>1&&(t=t.clone(),t.topLeft.col++,this._sheet.range(t)._set("vBorders",e))},this),this},insideHorizontalBorders:function(e){return this._ref.forEach(function(t){t instanceof l&&t.height()>1&&(t=t.clone(),t.topLeft.row++,this._sheet.range(t)._set("hBorders",e))},this),this}}),o.each(u,function(e,t){c.prototype[t]=function(e){return this._property(t,e)}}),h=o('<div style="position: absolute !important; top: -4000px !important; height: auto !important;padding: 1px 3px !important; box-sizing: border-box; margin: 0 !important; border: 1px solid black !important;line-height: normal !important; visibility: hidden !important;white-space: pre-wrap;" />')[0],e.spreadsheet.util={getTextHeight:r},e.spreadsheet.Range=c)}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/runtime.min",["spreadsheet/references.min"],e)}(function(){"use strict";function e(t){return t instanceof e?t:void(this.code=t)}function t(e,t,n){for(var i,r=n(e),o=e;++e<t;)i=n(e),i>r&&(r=i,o=e);return o}function n(e,t){if(e.constructor!==t.constructor)return!1;if(e instanceof D)return e.sheet==t.sheet&&e.row==t.row&&e.col==t.col&&e.rel==t.rel;if(e instanceof E)return n(e.topLeft,t.topLeft)&&n(e.bottomRight,t.bottomRight)&&e.endSheet==t.endSheet;if(e instanceof B){var i=e.refs.length;if(i!=t.refs.length)return!1;for(;--i>=0;)if(!n(e.refs[i],t.refs[i]))return!1}return!0}function i(t,n){function i(e){var t,n,r,s,c;return u=e[0],t="{ ",Array.isArray(u)?(d+="while (i < args.length) { ",f+="while (i < args.length) { ",t+="xargs.push(tmp = []); stack.push(xargs); xargs = tmp; ",t+="while (i < args.length) { ",t+=e.map(i).join(""),t+="} ",t+="xargs = stack.pop(); ",f+="} ",d+="} "):"+"==u?(d+="while (i < args.length) { ",f+="while (i < args.length) { ",t+="if (i >= args.length) return new CalcError('N/A'); ",t+="xargs.push(tmp = []); stack.push(xargs); xargs = tmp; ",t+="do { ",t+=e.slice(1).map(i).join(""),t+="} while (i < args.length); ",t+="xargs = stack.pop(); ",f+="} ",d+="} "):"?"==u?t+="if (!("+l(e[1])+")) return new CalcError(err); ":(n=e[1],Array.isArray(n)&&/^#?collect/.test(n[0])?(r=/!$/.test(n[0]),s=n[2],o(),t+="try {var $"+u+" = this.cellValues(args.slice(i",s&&(t+=", i + "+s),t+=")"+(r?",true":"")+").reduce(function(ret, $"+u+"){ ","#"!=n[0].charAt(0)&&(t+="if ($"+u+" instanceof CalcError) throw $"+u+"; "),t+="if ("+l(n[1])+") ret.push($"+u+"); ",t+="return ret; ",t+="}.bind(this), []); ",t+=s?"i += "+s+"; ":"i = args.length; ",t+="xargs.push($"+u+")} catch(ex) { if (ex instanceof CalcError) return ex; throw ex; } ",f+="toResolve.push(args.slice(i)); "):"rest"==n?t+="xargs.push(args.slice(i)); i = args.length; ":((g=/^\*/.test(u))&&(v=!0,u=u.substr(1)),t+="var $"+u+" = args[i++]; ",c=!1,/!$/.test(n)?(n=n.substr(0,n.length-1),c=!0):t+="if ($"+u+" instanceof CalcError) return $"+u+"; ",t+=a(n,c)+"xargs.push($"+u+"); ")),t+="} "}function o(){return c?"$"+u:(m=!0,c=!0,f+="toResolve.push(args[i++]); ","($"+u+" = this.force($"+u+"))")}function s(e){return"("+(e?"(typeof "+o()+" == 'number' ? ($"+u+" = round($"+u+"), true) : false) || ":"(typeof "+o()+" == 'number') || ")+"(typeof $"+u+" == 'boolean' ? ($"+u+" = +$"+u+", true) : false) || (typeof $"+u+" == 'string' && !/^(?:=|true|false)/i.test($"+u+") ? (tmp = kendo.spreadsheet.calc.parse(0, 0, 0, $"+u+"), /^date|number|percent$/.test(tmp.type) ? ($"+u+" = +tmp.value, true) : false) : false))"}function a(e,t){c=!1;var n="if (!("+l(e)+")) { ";return c&&!t&&(n+=" if ($"+u+" instanceof CalcError) return $"+u+"; "),n+="return new CalcError(err); } ",c||(f+="i++; "),d+=g?"var $"+u+" = this._arrayArg(args[i]); if ($"+u+") { xargs.push($"+u+"); width = Math.max(width, $"+u+".width); height = Math.max(height, $"+u+".height); arrays.push(true) } else { xargs.push(args[i]); arrays.push(false); } i++; ":"xargs.push(args[i++]); arrays.push(false); ",n}function l(e){if(Array.isArray(e)){if("or"==e[0])return"("+e.slice(1).map(l).join(") || (")+")";if("and"==e[0])return"("+e.slice(1).map(l).join(") && (")+")";if("values"==e[0])return"("+e.slice(1).map(function(e){return o()+" === "+e}).join(") || (")+")";if("null"==e[0])return"("+l("null")+" ? (($"+u+" = "+e[1]+"), true) : false)";if("between"==e[0]||"[between]"==e[0])return"("+o()+" >= "+e[1]+" && $"+u+" <= "+e[2]+" ? true : ((err = 'NUM'), false))";if("(between)"==e[0])return"("+o()+" > "+e[1]+" && $"+u+" < "+e[2]+" ? true : ((err = 'NUM'), false))";if("(between]"==e[0])return"("+o()+" > "+e[1]+" && $"+u+" <= "+e[2]+" ? true : ((err = 'NUM'), false))";if("[between)"==e[0])return"("+o()+" >= "+e[1]+" && $"+u+" < "+e[2]+" ? true : ((err = 'NUM'), false))";if("assert"==e[0]){var t=e[2]||"N/A";return"(("+e[1]+") ? true : (err = "+JSON.stringify(t)+", false))"}if("not"==e[0])return"!("+l(e[1])+")";throw Error("Unknown array type condition: "+e[0])}if("number"==e||"datetime"==e)return s(!0);if("number!"==e)return"(typeof "+o()+" == 'number' ? ($"+u+" = round($"+u+"), true) : false)";if("integer"==e||"date"==e)return"("+s()+" && (($"+u+" |= 0), true))";if("divisor"==e)return"("+s(!0)+" && ($"+u+" == 0 ? ((err = 'DIV/0'), false) : true))";if("number+"==e)return"("+s(!0)+" && ($"+u+" >= 0 ? true : ((err = 'NUM'), false)))";if("integer+"==e)return"("+s()+" && (($"+u+" |= 0) >= 0 ? true : ((err = 'NUM'), false)))";if("number++"==e)return"("+s(!0)+" && ($"+u+" > 0 ? true : ((err = 'NUM'), false)))";if("integer++"==e)return"("+s()+" && (($"+u+" |= 0) > 0 ? true : ((err = 'NUM'), false)))";if("string"==e)return"((typeof "+o()+" == 'string' || typeof $"+u+" == 'boolean' || typeof $"+u+" == 'number') ? ($"+u+" += '', true) : ($"+u+" === undefined ? (($"+u+" = ''), true) : false))";if("boolean"==e)return"(typeof "+o()+" == 'boolean')";if("logical"==e)return"(typeof "+o()+" == 'boolean' || (typeof $"+u+" == 'number' ? ($"+u+" = !!$"+u+", true) : false))";if("matrix"==e)return o(),"((m = this.asMatrix($"+u+")) ? ($"+u+" = m) : false)";if("#matrix"==e)return"((m = this.asMatrix($"+u+")) ? ($"+u+" = m) : false)";if("ref"==e)return"($"+u+" instanceof kendo.spreadsheet.Ref)";if("area"==e)return"($"+u+" instanceof kendo.spreadsheet.CellRef || $"+u+" instanceof kendo.spreadsheet.RangeRef)";if("cell"==e)return"($"+u+" instanceof kendo.spreadsheet.CellRef)";if("null"==e)return"("+o()+" == null)";if("anyvalue"==e)return"("+o()+" != null && i <= args.length)";if("forced"==e)return"("+o()+", i <= args.length)";if("anything"==e)return"(i <= args.length)";if("blank"==e)return"("+o()+" == null || $"+u+" === '')";throw Error("Can't check for type: "+e)}var u,c,h,d="function arrayArgs(args) { var xargs = [], width = 0, height = 0, arrays = [], i = 0; ",f="function resolve(args, callback) { var toResolve = [], i = 0; ",p="'use strict'; function check(args) { var stack = [], tmp, xargs = [], i = 0, m, err = 'VALUE'; ",m=!1,g=!1,v=!1;return p+=n.map(i).join(""),p+="if (i < args.length) return new CalcError('N/A'); ",p+="return xargs; } ",d+="return { args: xargs, width: width, height: height, arrays: arrays }; } ",m?(f+="this.resolveCells(toResolve, callback); } ",h=Function("CalcError","round",p+f+d+" return { resolve: resolve, check: check, arrayArgs: arrayArgs };")):h=Function("CalcError","round",p+" return { check: check };"),h=h(e,r),v||delete h.arrayArgs,h}function r(e){return e===parseInt(e,10)?e:+e.toPrecision(14)}function o(e){return"number"==typeof e?r(e):e}function s(t,n,i){if(i instanceof e)return i;try{return n.apply(t,i)}catch(r){if(r instanceof e)return r;throw r}}function a(e,t,n,i){return function(r,o){function a(){var t,a,l,u,c,h;if(i&&(t=i.call(this,o),o=t.args,t.width>0&&t.height>0)){for(a=new I(this),l=0;l<t.height;++l)for(u=0;u<t.width;++u){for(c=[],h=0;h<o.length;++h)c[h]=t.arrays[h]?o[h].getNA(l,u):o[h];c=n.call(this,c),a.set(l,u,s(this,e,c))}return r(a)}c=n.call(this,o),r(s(this,e,c))}t?t.call(this,o,a):a.call(this)}}function l(t,n,i,r){return function(o,s){function a(){var n,a,l,u,c,h,d,f;if(r&&(n=r.call(this,s),s=n.args,n.width>0&&n.height>0))for(a=new I(this),l=n.width*n.height,u=function(e,t){return function(n){if(a.set(e,t,n),--l,0===l)return o(a)}},c=0;c<n.height&&l>0;++c)for(h=0;h<n.width&&l>0;++h){for(d=[],f=0;f<s.length;++f)d[f]=n.arrays[f]?s[f].getNA(c,h):s[f];if(d=i.call(this,d),d instanceof e){if(a.set(c,h,d),--l,0===l)return o(a)}else d.unshift(u(c,h)),t.apply(this,d)}else n=i.call(this,s),n instanceof e?o(n):(n.unshift(o),t.apply(this,n))}n?n.call(this,s,a):a.call(this)}}function u(e,t){return e=e.toLowerCase(),N[e]=t,{args:function(n,r){var o,s=i(e,n);return r&&(s.arrayArgs&&console.log(""+s.arrayArgs),s.resolve&&console.log(""+s.resolve),s.check&&console.log(""+s.check)),o=N[e]=a(t,s.resolve,s.check,s.arrayArgs),o.kendoSpreadsheetArgs=n,this},argsAsync:function(n,r){var o,s=i(e,n);return r&&(s.arrayArgs&&console.log(""+s.arrayArgs),s.resolve&&console.log(""+s.resolve),s.check&&console.log(""+s.check)),o=N[e]=l(t,s.resolve,s.check,s.arrayArgs),o.kendoSpreadsheetArgs=n,this}}}function c(e,t,n){return t++,(1461*(e+4800+((t-14)/12|0))/4|0)+(367*(t-2-12*((t-14)/12|0))/12|0)-(3*((e+4900+((t-14)/12|0))/100|0)/4|0)+n-32075}function h(e){var t,n,i,r,o,s,a;return t=e+68569,n=4*t/146097|0,t-=(146097*n+3)/4|0,r=4e3*(t+1)/1461001|0,t=t-(1461*r/4|0)+31,i=80*t/2447|0,s=t-(2447*i/80|0),t=i/11|0,o=i+2-12*t,a=100*(n-49)+r+t,o--,{year:a,month:o,date:s,day:(e+1)%7,ord:H[d(a)][o]+s}}function d(e){return e%4?0:e%100?1:e%400?0:1}function f(e){return d(e)?366:365}function p(e,t){return d(e)&&1==t?29:P[t]}function m(e,t,n){return t>=1&&t<=12&&n>=1&&n<=p(e,t-1)}function g(e){return h((0|e)+z)}function v(e,t,n){return c(e,t,n)-z}function b(e){var t,n,i,r,o=e-(0|e);return o<0&&o++,t=Math.round(V*o),n=Math.floor(t/$),t-=n*$,i=Math.floor(t/O),t-=i*O,r=Math.floor(t/1e3),t-=1e3*r,{hours:n,minutes:i,seconds:r,milliseconds:t}}function w(e){var t=g(e),n=b(e);return new Date(t.year,t.month,t.date,n.hours,n.minutes,n.seconds,n.milliseconds)}function y(e,t,n,i){return(e+(t+(n+i/1e3)/60)/60)/24}function _(e){var t=y(e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds());return e=v(e.getFullYear(),e.getMonth(),e.getDate()),e<0?e-1+t:e+t}function k(e,t){return t&&(t=kendo.spreadsheet.formatting.makeDateFormat(t)),kendo.parseExactDate(e,t)||kendo.parseExactDate(e)||kendo.parseExactDate(e,["MMMM dd yyyy","MMMM dd yy","MMM dd yyyy","MMM dd yy","dd MMMM yyyy","dd MMMM yy","dd MMM yyyy","dd MMM yy","MMMM dd, yyyy","MMMM dd, yy","MMM dd, yyyy","MMM dd, yy","MMMM dd","MMM dd","MMMM yyyy","MMM yyyy","dd MMMM","dd MMM"])}function x(t){return function(n,i){return"string"==typeof n&&"string"!=typeof i&&(i=null==i?"":i+""),"string"!=typeof n&&"string"==typeof i&&(n=null==n?"":n+""),"number"==typeof n&&null==i&&(i=0),"number"==typeof i&&null==n&&(n=0),"string"==typeof n&&"string"==typeof i&&(n=n.toLowerCase(),i=i.toLowerCase()),typeof i==typeof n?t(n,i):new e("VALUE")}}var C,F,R,S,A,D,E,B,M,T,I,L,N,z,P,H,O,$,V,U,W,j;kendo.support.browser.msie&&kendo.support.browser.version<9||(C={},F=kendo.spreadsheet,F.calc=C,R=C.runtime={},S=kendo.Class,A=F.Ref,D=F.CellRef,E=F.RangeRef,B=F.UnionRef,M=F.NULLREF,e.prototype.toString=function(){return"#"+this.code+("NAME"==this.code?"?":"!")},T=S.extend({init:function(e,t,n,i){this.callback=e,this.formula=t,this.ss=n,this.parent=i},resolve:function(e){var t=this;e instanceof A?t.resolveCells([e],function(){t._resolve(e)}):t._resolve(e)},error:function(t){return new e(t)},_resolve:function(e){e=void 0===e?null:Array.isArray(e)?this.asMatrix(e):o(e);var t=this.formula;t.arrayFormulaRange?e=this.asMatrix(e)||this.asMatrix([[e]]):e instanceof E&&(e=this._arrayArg(e)),t.value=e,this.ss.onFormula(t)&&this.callback&&this.callback.call(t,e)},resolveCells:function(e,t){function n(e){e.exec(s.ss,function(){--r||t.call(s)},s)}function i(e){var t,n;for(t=0;t<e.length;++t)n=e[t],n.formula&&a.push(n.formula);return!0}var r,o,s=this,a=[];if(function l(e){var t,n;for(t=0;t<e.length;++t)n=e[t],n instanceof A&&i(s.getRefCells(n)),Array.isArray(n)&&l(n)}(e),!a.length)return t.call(s);for(r=a.length,o=0;o<a.length;++o)n(a[o])},cellValues:function(e,t){var n,i,r=[];for(n=0;n<e.length;++n)i=e[n],i instanceof A?(i=this.getRefData(i,t),r=r.concat(i)):Array.isArray(i)?r=r.concat(this.cellValues(i,t)):i instanceof I?r=r.concat(this.cellValues(i.data,t)):r.push(i);return r},fetchName:function(t,n){var i,r=this.formula,o=this.ss.nameValue(t,r.sheet,r.row,r.col);o instanceof L?(o=o.clone(r.sheet,r.row,r.col,!0),i=new F.ValidationFormulaContext(this.ss.workbook),o.exec(i,n,this)):(o instanceof A&&(o=o.absolute(r.row,r.col),o.sheet||(o.sheet=r.sheet)),n(null==o?new e("NAME"):o))},force:function(e){return e instanceof A?this.getRefData(e):e},func:function(t,n,i){t=t.toLowerCase();var r=N[t];return r?r.call(this,n,i):void n(new e("NAME"))},bool:function(e){return e instanceof A&&(e=this.getRefData(e)),"string"==typeof e?"true"==e.toLowerCase():"number"==typeof e?0!==e:"boolean"==typeof e?e:null!=e},_arrayArg:function(t){var n=this.formula;return!n.arrayFormulaRange&&t instanceof E?1==t.height()&&n.col>=t.topLeft.col&&n.col<=t.bottomRight.col?this.getRefData(new D(t.topLeft.row,n.col).setSheet(t.sheet)):1==t.width()&&n.row>=t.topLeft.row&&n.row<=t.bottomRight.row?this.getRefData(new D(n.row,t.topLeft.col).setSheet(t.sheet)):new e("VALUE"):this.asMatrix(t)},asMatrix:function(e){var t,n,i,r,o,s,a;return e instanceof I?e:(t=this,e instanceof E?(n=e.topLeft,i=n.row,r=n.col,o=t.getRefCells(e),s=new I(t),isFinite(e.width())&&(s.width=e.width()),isFinite(e.height())&&(s.height=e.height()),isFinite(i)||(i=0),isFinite(r)||(r=0),o.forEach(function(e){s.set(e.row-i,e.col-r,e.value)}),s):Array.isArray(e)&&e.length>0?(s=new I(t),a=0,e.forEach(function(e){var n=0,i=1;e.forEach(function(e){var r=e instanceof E;e instanceof A&&!r&&(e=t.getRefData(e)),(r||Array.isArray(e))&&(e=t.asMatrix(e)),e instanceof I?(e.each(function(e,t,i){s.set(a+t,n+i,e)}),i=Math.max(i,e.height),n+=e.width):s.set(a,n++,e)}),a+=i}),s):void 0)},getRefCells:function(e,t,n){var i=this.formula;return this.ss.getRefCells(e,t,i.sheet,i.row,i.col,n)},getRefData:function(e,t){var n=this.formula;return this.ss.getData(e,n.sheet,n.row,n.col,t)},workbook:function(){return this.ss.workbook}}),I=S.extend({init:function(e){this.context=e,this.height=0,this.width=0,this.data=[]},clone:function(){var e=new I(this.context);return e.height=this.height,e.width=this.width,e.data=this.data.map(function(e){return e.slice()}),e},get:function(e,t){var n=this.data[e],i=n?n[t]:null;return i instanceof A?this.context.getRefData(i):i},getNA:function(t,n){return t<this.height&&n<this.width?this.get(t,n):new e("N/A")},set:function(e,t,n){var i=this.data[e];null==i&&(i=this.data[e]=[]),i[t]=n,e>=this.height&&(this.height=e+1),t>=this.width&&(this.width=t+1)},each:function(e,t){var n,i,r;for(n=0;n<this.height;++n)for(i=0;i<this.width;++i)if(r=this.get(n,i),(t||null!=r)&&(r=e.call(this.context,r,n,i),void 0!==r))return r},map:function(e,t){var n=new I(this.context);return this.each(function(t,i,r){n.set(i,r,e.call(this,t,i,r))},t),n},eachRow:function(e){var t,n;for(t=0;t<this.height;++t)if(n=e.call(this.context,t),void 0!==n)return n},eachCol:function(e){var t,n;for(t=0;t<this.width;++t)if(n=e.call(this.context,t),void 0!==n)return n},mapRow:function(e){var t=new I(this.context);return this.eachRow(function(n){t.set(n,0,e.call(this.context,n))}),t},mapCol:function(e){var t=new I(this.context);return this.eachCol(function(n){t.set(0,n,e.call(this.context,n))}),t},toString:function(){return JSON.stringify(this.data)},transpose:function(){var e=new I(this.context);return this.each(function(t,n,i){e.set(i,n,t)}),e},unit:function(e){var t,n,i,r;for(this.width=this.height=e,t=this.data=Array(e),n=e;--n>=0;)for(i=t[n]=Array(e),r=e;--r>=0;)i[r]=n==r?1:0;return this},multiply:function(t){var n,i,r,o,s,a,l=this,u=new I(l.context);for(n=0;n<l.height;++n)for(i=0;i<t.width;++i){for(r=0,o=0;o<l.width;++o){if(s=l.get(n,o),a=t.get(o,i),"number"!=typeof s||"number"!=typeof a)throw new e("VALUE");r+=s*a}u.set(n,i,r)}return u},adds:function(e,t){var n,i,r,o,s=this,a=new I(s.context),l=t?-1:1;for(n=0;n<s.height;++n)for(i=0;i<s.width;++i)r=s.get(n,i),o=e.get(n,i),a.set(n,i,r+l*o);return a},determinant:function(){var e,t,n,i,r,o=this.clone().data,s=o.length,a=1;for(e=0;e<s;e++){for(t=e;t<s&&!o[t][e];t++);if(t==s)return 0;if(t!=e)for(a=-a,i=e;i<s;i++)r=o[e][i],o[e][i]=o[t][i],o[t][i]=r;for(n=e+1;n<s;n++)for(i=e+1;i<s;i++)o[n][i]-=o[e][i]*o[n][e]/o[e][e];a*=o[e][e]}return a},inverse:function(){var e,n,i,r,o,s,a=this.width,l=this.augment(new I(this.context).unit(a)),u=l.data;for(n=0;n<a;++n){if(i=t(n,a,function(e){return u[e][n]}),!u[i][n])return null;for(n!=i&&(e=u[n],u[n]=u[i],u[i]=e),r=n+1;r<a;++r){for(o=n+1;o<2*a;++o)u[r][o]-=u[n][o]*u[r][n]/u[n][n];u[r][n]=0}}for(r=0;r<a;++r)for(s=u[r][r],o=0;o<2*a;++o)u[r][o]/=s;for(n=a;--n>=0;)for(r=n;--r>=0;)if(u[r][n])for(o=2*a;--o>=a;)u[r][o]-=u[n][o]*u[r][n];return l.slice(0,a,a,a)},augment:function(e){var t=this.clone(),n=t.width;return e.each(function(e,i,r){t.set(i,r+n,e)}),t},slice:function(e,t,n,i){var r,o,s=new I(this.context);for(r=0;r<n;++r)for(o=0;o<i;++o)s.set(r,o,this.get(e+r,t+o));return s}}),L=S.extend({init:function(e,t,n,i,r,o,s){this.refs=e,this.handler=t,this.print=n,this.absrefs=null,this.sheet=i,this.row=r,this.col=o,this.onReady=[],this.pending=!1,this.arrayFormulaRange=s},setArrayFormulaRange:function(e){this.arrayFormulaRange=e.clone().setSheet(this.sheet)},clone:function(e,t,n,i){var r=e.toLowerCase(),o=this.refs,s=this.arrayFormulaRange;return(i||r!=this.sheet.toLowerCase())&&(o=o.map(function(t){return t.hasSheet()||t.sheet&&t.sheet.toLowerCase()==r||(t=t.clone().setSheet(e)),t}),s&&(s=s.clone().setSheet(e))),new L(o,this.handler,this.print,e,t,n,s)},deepClone:function(){var e=this.refs.map(function(e){return e.clone()});return new L(e,this.handler,this.print,this.sheet,this.row,this.col,this.arrayFormulaRange)},resolve:function(e){this.pending=!1,this.onReady.forEach(function(t){t(e)})},exec:function(t,n,i){var r,o,s;if("value"in this)n&&n(this.value);else{for(n&&this.onReady.push(n),r=new T(this.resolve,this,t,i),o=0;i;){if(i.formula===this)return this.pending=!1,void r.resolve(new e("CIRCULAR"));i=i.parent,++o}if(this.pending)return;this.pending=!0,s=function(){this.absrefs||(this.absrefs=this.refs.map(function(e){return e.absolute(this.row,this.col)},this)),this.handler.call(r)}.bind(this),o<20?s():setTimeout(s,0)}},reset:function(){this.onReady=[],this.pending=!1,delete this.value},renameSheet:function(e,t){e=e.toLowerCase(),this.absrefs=null,this.sheet.toLowerCase()==e&&(this.sheet=t),this.refs.forEach(function(n){n.renameSheet(e,t)})},adjust:function(e,t,i,r){function o(n){return n===M?n:n.sheet.toLowerCase()!=e?(u&&("row"==t&&s>=i&&(n=n.relative(r,0)),"col"==t&&a>=i&&(n=n.relative(0,r))),n):n.adjust(s,a,c,h,"row"==t,i,r)}var s,a,l,u,c,h,d,f,p;if(e=e.toLowerCase(),s=this.row,a=this.col,l=this.sheet.toLowerCase(),u=!1,l==e&&("row"==t&&s>=i&&(this.row+=r,u=!0),"col"==t&&a>=i&&(this.col+=r,u=!0)),c=this.row,h=this.col,this.absrefs=null,d=this.refs,f=u,this.refs=d.map(function(e){var t=o(e);return f||n(t,e)||(f=!0),t}),p=this.arrayFormulaRange,p&&(this.arrayFormulaRange=o(p),f||n(p,this.arrayFormulaRange)||(f=!0)),f)return new L(d,this.handler,this.print,this.sheet,s,a,p)},toString:function(){return this.print(this.row,this.col)}}),N=Object.create(null),N["if"]=function(t,n){var i=this,r=n[0],o=n[1],s=n[2];this.resolveCells([r],function(){var n=i.asMatrix(r);n?o(function(r){s(function(o){var s=i.asMatrix(r),a=i.asMatrix(o);t(n.map(function(t,n,l){return t instanceof e?t:i.bool(t)?s?s.get(n,l):r:a?a.get(n,l):o}))})}):(r=this.force(r),r instanceof e?t(r):i.bool(r)?o(t):s(t))})},N["φ"]=function(e){e((1+Math.sqrt(5))/2)},z=c(1900,0,-1),P=[31,28,31,30,31,30,31,31,30,31,30,31],H=[[0,31,59,90,120,151,181,212,243,273,304,334],[0,31,60,91,121,152,182,213,244,274,305,335]],O=6e4,$=60*O,V=24*$,R.CalcError=e,R.Formula=L,R.Matrix=I,R.packDate=v,R.unpackDate=g,R.packTime=y,R.unpackTime=b,R.serialToDate=w,R.dateToSerial=_,R.daysInMonth=p,R.validDate=m,R.isLeapYear=d,R.daysInYear=f,R.parseDate=k,R.limitPrecision=r,F.dateToNumber=_,F.numberToDate=w,F.defineFunction=u,F.CalcError=e,R.defineFunction=u,R.defineAlias=function(e,t){var n=N[t];if(!n)throw Error("Function "+t+" is not yet defined");n.kendoSpreadsheetAliases||(n.kendoSpreadsheetAliases=[t]),n.kendoSpreadsheetAliases.push(e),N[e]=n},R.FUNCS=N,U=["or","number",["null",0]],W=[["*a",U],["*b",U]],j=[["*a",["or","anyvalue",["null",0]]],["*b",["or","anyvalue",["null",0]]]],u("binary+",function(e,t){return e+t}).args(W),u("binary-",function(e,t){return e-t}).args(W),u("binary*",function(e,t){return e*t}).args(W),u("binary/",function(e,t){return e/t}).args([["*a",U],["*b","divisor"]]),u("binary^",function(e,t){return Math.pow(e,t)}).args(W),u("binary&",function(e,t){return null==e&&(e=""),null==t&&(t=""),""+e+t}).args([["*a",["or","number","string","boolean","null"]],["*b",["or","number","string","boolean","null"]]]),u("binary=",function(e,t){return e="string"==typeof e?e.toLowerCase():e,t="string"==typeof t?t.toLowerCase():t,e===t}).args(j),u("binary<>",function(e,t){return e!==t}).args(j),u("binary<",x(function(e,t){return e<t})).args(j),u("binary<=",x(function(e,t){return e<=t})).args(j),u("binary>",x(function(e,t){return e>t})).args(j),u("binary>=",x(function(e,t){return e>=t})).args(j),u("unary+",function(e){return e}).args([["*a",U]]),u("unary-",function(e){return-e}).args([["*a",U]]),u("unary%",function(e){return e/100}).args([["*a",U]]),u("binary:",function(e,t){return new E(e,t).setSheet(e.sheet||this.formula.sheet,e.hasSheet())}).args([["a","cell"],["b","cell"]]),u("binary,",function(e,t){return new B([e,t])}).args([["a","ref"],["b","ref"]]),u("binary ",function(e,t){return e.intersect(t)}).args([["a","ref"],["b","ref"]]),u("not",function(e){return!this.bool(e)}).args([["*a",["or","anyvalue",["null",0]]]]),u("isblank",function(e){return e instanceof D&&(e=this.getRefData(e),null==e)}).args([["*value","anything!"]]),u("iserror",function(t){return t instanceof e}).args([["*value","forced!"]]),u("iserr",function(t){return t instanceof e&&"N/A"!=t.code}).args([["*value","forced!"]]),u("isna",function(t){return t instanceof e&&"N/A"==t.code}).args([["*value","forced!"]]),u("islogical",function(e){return"boolean"==typeof e}).args([["*value","forced!"]]),u("isnontext",function(e){return"string"!=typeof e}).args([["*value","forced!"]]),u("istext",function(e){return"string"==typeof e}).args([["*value","forced!"]]),u("isnumber",function(e){return"number"==typeof e}).args([["*value","forced!"]]),u("isref",function(e){return e instanceof D||e instanceof E}).args([["*value","anything!"]]),N[",getname"]=function(e,t){this.fetchName(t[0],e)})},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/validation.min",["spreadsheet/runtime.min"],e)}(function(){"use strict";function e(e,t,r,a){var u,c,h,d;if("string"==typeof a&&(a=JSON.parse(a)),a.from&&("list"!==a.dataType||a.fromIsListValue||(a.from=kendo.format(o,a.from),a.fromIsListValue=!0),"date"===a.dataType&&(h=i.runtime.parseDate(a.from),h&&(a.from=kendo.format(s,a.from),a.fromIsDateValue=!0)),a.from=i.compile(i.parseFormula(e,t,r,a.from))),a.to&&("date"===a.dataType&&(d=i.runtime.parseDate(a.to),d&&(a.to=kendo.format(s,a.to),a.toIsDateValue=!0)),a.to=i.compile(i.parseFormula(e,t,r,a.to))),c="custom"==a.dataType?n.validationComparers.custom:"list"==a.dataType?n.validationComparers.list:n.validationComparers[a.comparerType],!c)throw kendo.format("'{0}' comparer is not implemented.",a.comparerType);return u=function(e){var t,n=this.to&&(this.to_value||0===this.to_value)?this.to_value:void 0;return null===e||""===e?this.value=!!this.allowNulls:"custom"==this.dataType?this.value=c(e,this.from_value,n):"list"==this.dataType?(t=this._getListData(),this.value=c(e,t,n)):this.value=c(e,this.from_value,n),this.value},new kendo.spreadsheet.validation.Validation(l.extend(a,{handler:u,sheet:e,row:t,col:r}))}var t,n,i,r,o,s,a,l=kendo.jQuery;kendo.support.browser.msie&&kendo.support.browser.version<9||(t=kendo.spreadsheet,n={},t.validation=n,i=t.calc,r=kendo.Class,o="_matrix({0})",s='DATEVALUE("{0}")',i.runtime.defineFunction("_matrix",function(e){return"string"==typeof e&&(e=this.asMatrix([e.split(/\s*,\s*/)])),e}).args([["m",["or","matrix","string"]]]),a=r.extend({init:function(e){this.handler=e.handler,this.from=e.from,this.to=e.to,this.dataType=e.dataType,this.comparerType=e.comparerType,this.type=e.type?e.type:"warning",this.allowNulls=!!e.allowNulls,this.fromIsDateValue=!!e.fromIsDateValue,this.toIsDateValue=!!e.toIsDateValue,this.showButton=e.showButton,this.fromIsListValue=!!e.fromIsListValue,this.sheet=e.sheet,this.row=e.row,this.col=e.col,e.tooltipMessageTemplate&&(this.tooltipMessageTemplate=e.tooltipMessageTemplate),e.tooltipTitleTemplate&&(this.tooltipTitleTemplate=e.tooltipTitleTemplate),e.messageTemplate&&(this.messageTemplate=e.messageTemplate),e.titleTemplate&&(this.titleTemplate=e.titleTemplate)},_formatMessages:function(e){var t=this.from?this.from_value:"",n=this.to?this.to_value:"",i=this.from?""+this.from:"",r=this.to?""+this.to:"",o=this.dataType,s=this.type,a=this.comparerType;return kendo.format(e,t,n,i,r,o,s,a)},_setMessages:function(){this.title="",this.message="",this.tooltipTitleTemplate&&(this.tooltipTitle=this._formatMessages(this.tooltipTitleTemplate)),this.tooltipMessageTemplate&&(this.tooltipMessage=this._formatMessages(this.tooltipMessageTemplate)),this.titleTemplate&&(this.title=this._formatMessages(this.titleTemplate)),this.messageTemplate&&(this.message=this._formatMessages(this.messageTemplate))},_getListData:function(){var e,t,n,i,r;if(!this.from_value||!this.from_value.data)return[];for(e=this.from_value.data,i=[],t=0;t<e.length;t++)if(r=e[t])for(n=0;n<r.length;n++)i.push(r[n]);return i},clone:function(e,t,n){var i=this._getOptions();return i.from&&(i.from=i.from.clone(e,t,n)),i.to&&(i.to=i.to.clone(e,t,n)),new a(l.extend(i,{handler:this.handler},{sheet:e,row:t,col:n}))},deepClone:function(){var e=new a(this);return e.from=e.from.deepClone(),e.to&&(e.to=e.to.deepClone()),e},exec:function(e,t,n,i){function r(t){return t instanceof kendo.spreadsheet.Ref&&(t=e.getData(t),Array.isArray(t)&&(t=t[0])),t}var o=this,s=function(e){o.from_value=r(e),o.value=o.handler.call(o,t,n),o._setMessages(),i&&i(o.value)};o.to?o.to.exec(e,function(t){o.to_value=r(t),
o.from.exec(e,s)}):o.from.exec(e,s)},reset:function(){this.from&&this.from.reset(),this.to&&this.to.reset(),delete this.value},adjust:function(e,t,n,i){var r,o,s,l,u=this.row,c=this.col;if(this.from&&(r=this.from.adjust(e,t,n,i)),this.to&&(o=this.to.adjust(e,t,n,i)),this.sheet.toLowerCase()==e.toLowerCase())switch(t){case"row":u>=n&&(s=!0,this.row+=i);break;case"col":c>=n&&(s=!0,this.col+=i)}if(s||r||o)return l=new a(this),l.from=r,l.to=o,l.row=u,l.col=c,l},toJSON:function(){var e=this._getOptions();return e.from&&(e.from=""+e.from,"list"===e.dataType&&(e.from=e.from.replace(/^_matrix\((.*)\)$/i,"$1"),delete e.fromIsListValue),"date"===e.dataType&&this.fromIsDateValue&&(e.from=e.from.replace(/^DATEVALUE\("(.*)"\)$/i,"$1"),delete e.fromIsDateValue)),e.to&&(e.to=""+e.to,"date"===e.dataType&&this.toIsDateValue&&(e.to=e.to.replace(/^DATEVALUE\("(.*)"\)$/i,"$1"),delete e.toIsDateValue)),e},_getOptions:function(){return{from:this.from,to:this.to,dataType:this.dataType,type:this.type,comparerType:this.comparerType,row:this.row,col:this.col,sheet:this.sheet,allowNulls:this.allowNulls,fromIsListValue:this.fromIsListValue,fromIsDateValue:this.fromIsDateValue,toIsDateValue:this.toIsDateValue,tooltipMessageTemplate:this.tooltipMessageTemplate,tooltipTitleTemplate:this.tooltipTitleTemplate,messageTemplate:this.messageTemplate,titleTemplate:this.titleTemplate,showButton:this.showButton}}}),n.compile=e,n.validationComparers={greaterThan:function(e,t){return e>t},lessThan:function(e,t){return e<t},between:function(e,t,n){return e>=t&&e<=n},equalTo:function(e,t){return e==t},notEqualTo:function(e,t){return e!=t},greaterThanOrEqualTo:function(e,t){return e>=t},lessThanOrEqualTo:function(e,t){return e<=t},notBetween:function(e,t,n){return e<t||e>n},custom:function(e,t){return t},list:function(e,t){return t.indexOf(e)>-1}},n.Validation=a)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/sheet.min",["kendo.core.min","kendo.color.min","spreadsheet/runtime.min","spreadsheet/validation.min","spreadsheet/references.min"],e)}(function(){!function(e){var t,n,i,r,o,s,a,l;e.support.browser.msie&&e.support.browser.version<9||(t=e.spreadsheet.RangeRef,n=e.spreadsheet.UnionRef,i=e.spreadsheet.CellRef,r=e.spreadsheet.Range,s=e.Class.extend({init:function(t){this._sheet=t,this.selection=e.spreadsheet.FIRSTREF.toRangeRef(),this.originalSelection=e.spreadsheet.FIRSTREF.toRangeRef(),this._activeCell=e.spreadsheet.FIRSTREF.toRangeRef(),this.originalActiveCell=e.spreadsheet.FIRSTREF},currentSelectionRange:function(){return this.selection.rangeAt(this.selectionRangeIndex).toRangeRef()},currentOriginalNavigationRange:function(){return this.originalSelection.rangeAt(this.selectionRangeIndex).toRangeRef()},currentNavigationRange:function(){return this.singleCellSelection()?this._sheet._sheetRef:this.selection.rangeAt(this.selectionRangeIndex).toRangeRef()},nextNavigationRange:function(){return this.singleCellSelection()||(this.selectionRangeIndex=this.selection.nextRangeIndex(this.selectionRangeIndex)),this.currentNavigationRange()},previousNavigationRange:function(){return this.singleCellSelection()||(this.selectionRangeIndex=this.selection.previousRangeIndex(this.selectionRangeIndex)),this.currentNavigationRange()},activeCell:function(e){return e&&(this.originalActiveCell=e.first(),this._activeCell=this._sheet.unionWithMerged(e.toRangeRef()),this._sheet.focus(e),this._sheet.triggerChange({activeCell:!0,selection:!0})),this._activeCell},select:function(e,t,n){if(e){if(e.eq(this.originalSelection))return;this._sheet.triggerSelect(new r(e,this._sheet)),this.originalSelection=e,this.selection=t,n!==!1?(e.isCell()?(this._sheet.forEachMergedCell(e,function(t){e=t.topLeft}),this.activeCell(e)):this.activeCell(this.selection.lastRange().first()),this.selectionRangeIndex=this.selection.size()-1):this._sheet.triggerChange({selection:!0})}return this.selection},singleCellSelection:function(){return this._activeCell.eq(this.selection)}}),a=e.Observable.extend({init:function(){e.Observable.prototype.init.call(this),this._reinit.apply(this,arguments)},events:["changing","commandRequest","afterInsertRow","afterDeleteRow","insertRow","insertColumn","deleteRow","deleteColumn","hideRow","hideColumn","unhideRow","unhideColumn","select","dataBinding","dataBound"],_reinit:function(t,n,i,r,o,a,l){l=l||{},this._defaultCellStyle={background:l.background,color:l.color,fontFamily:l.fontFamily,fontSize:l.fontSize,italic:l.italic,bold:l.bold,underline:l.underline,wrap:l.wrap,verticalAlign:l.verticalAlign,textAlign:l.textAlign},this._rows=new e.spreadsheet.Axis(t,i),this._columns=new e.spreadsheet.Axis(n,r),this._filteredRows=new e.spreadsheet.RangeList(0,t-1,(!1)),this._mergedCells=[],this._frozenRows=0,this._frozenColumns=0,this._suspendChanges=!1,this._filter=null,this._showGridLines=!0,this._gridLinesColor=null,this._grid=new e.spreadsheet.Grid(this._rows,this._columns,t,n,o,a),this._sheetRef=this._grid.normalize(e.spreadsheet.SHEETREF),this._properties=new e.spreadsheet.PropertyBag(t,n,this._defaultCellStyle),this._sorter=new e.spreadsheet.Sorter(this._grid,this._properties.sortable()),this._viewSelection=new s(this),this._editSelection=new s(this),this._formulaSelections=[],this._drawings=[]},_selectionState:function(){return this._inEdit?this._editSelection:this._viewSelection},navigator:function(){return this._navigator||(this._navigator=new e.spreadsheet.SheetNavigator(this)),this._navigator},axisManager:function(){return this._axisManager||(this._axisManager=new e.spreadsheet.AxisManager(this)),this._axisManager},_name:function(e){return e?(this._sheetName=e,this):this._sheetName},name:function(){return this._name()},_property:function(e,t,n){return void 0===t?e():(e(t),this.triggerChange(n))},_field:function(e,t,n){return void 0===t?this[e]:(this[e]=t,this.triggerChange(n))},suspendChanges:function(e){return void 0===e?this._suspendChanges:(this._suspendChanges=e,this)},triggerChange:function(e){return this._suspendChanges||this.trigger("change",e),this},triggerSelect:function(e){this.trigger("select",{range:e})},setDataSource:function(t,n){this.dataSourceBinder&&this.dataSourceBinder.destroy(),this.dataSourceBinder=new e.spreadsheet.SheetDataSourceBinder({dataSource:t,sheet:this,columns:n}),this.dataSource=this.dataSourceBinder.dataSource},hideColumn:function(e){if(!this.trigger("hideColumn",{index:e}))return this._property(this._columns.hide.bind(this._columns),e,{layout:!0})},unhideColumn:function(e){if(!this.trigger("unhideColumn",{index:e}))return this._property(this._columns.unhide.bind(this._columns),e,{layout:!0})},isHiddenColumn:function(e){return this._grid._columns.hidden(e)},_copyRange:function(e,t){var n=this._grid,i=n.rowCount,r=n.normalize(e.topLeft),o=n.normalize(e.bottomRight),s=r.col*i+r.row,a=o.col*i+o.row,l=t.col*i+t.row;this._properties.copy(s,a,l)},_saveModifiedFormulas:function(e,t){var n,i=o;return o=e,n=t(),o=i,n},_restoreModifiedFormulas:function(t){var n=this._workbook;t.forEach(function(t){var i,r=n.sheetByName(t.sheet);t instanceof e.spreadsheet.calc.runtime.Formula&&(i=r._grid.cellRefIndex(t),r._properties.set("formula",i,i,t)),t instanceof e.spreadsheet.validation.Validation&&(i=r._grid.cellRefIndex(t),r._properties.set("validation",i,i,t))})},_adjustReferences:function(n,i,r,s){var a,l,u;this._mergedCells=s.reduce(function(e,o){return o=o.adjust(null,null,null,null,"row"==n,i,r),o instanceof t&&e.push(o),e},[]),this._workbook&&(a=this._name(),this._workbook._sheets.forEach(function(e){e._forFormulas(function(e){var t=e.adjust(a,n,i,r);t&&o&&o.push(t)}),e._forValidations(function(e){var t=e.adjust(a,n,i,r);t&&o&&o.push(t)})}),this._workbook.adjustNames(a,"row"==n,i,r)),l=this.select(),l=l.adjust(null,null,null,null,"row"==n,i,r),l!==e.spreadsheet.NULLREF&&this.select(l),u="col"==n?this._columns:this._rows,u.adjust(i,r),"row"==n&&(r<0?this._filteredRows.copy(i-r,this._rows._count-1,i):(this._filteredRows.copy(i,this._rows._count,i+r),this._filteredRows.value(i,i+r-1,!1))),this._drawings.forEach(function(e){e.topLeftCell&&(e.topLeftCell=e.topLeftCell.adjust(null,null,null,null,"row"==n,i,r))})},_forFormulas:function(e){var t=this._properties,n=t.get("formula").values(),i=n.length;n.forEach(function(t,n){e.call(this,t.value,n,i)},this)},_forValidations:function(e){var t=this._properties;t.get("validation").values().forEach(function(t){e.call(this,t.value)},this)},preventInsertRow:function(e,t){var n,i;return this.selectedHeaders().allRows?{reason:"error",type:"insertRowWhenColumnIsSelected"}:(t=t||1,n=this._grid,i=this.range(n.rowCount-t,0,t,n.columnCount),!!i.hasValue()&&{reason:"error",type:"shiftingNonblankCells"})},preventInsertColumn:function(e,t){var n,i;return this.selectedHeaders().allCols?{reason:"error",type:"insertColumnWhenRowIsSelected"}:(t=t||1,n=this._grid,i=this.range(0,n.columnCount-t,n.rowCount,t),!!i.hasValue()&&{reason:"error",type:"shiftingNonblankCells"})},insertRow:function(e){var n=this.preventInsertRow(e);if(n)throw Error("Shifting nonblank cells off the worksheet is not supported!");if(!this.trigger("insertRow",{index:e}))return this.batch(function(){var n,o,s,a,l,u,c=this._grid,h=c.columnCount,d=c.rowCount,f=this.frozenRows();for(e<f&&this.frozenRows(f+1),n=this._mergedCells.slice(),o=0;o<h;o++)s=new t(new i(e,o),new i(e,o)),a=c.normalize(s.topLeft),l=c.normalize(s.bottomRight),u=new t(new i(a.row,a.col),new i(d-2,l.col)),this._copyRange(u,new i(a.row+1,a.col)),new r(s,this).clear({clearAll:!0,keepBorders:!0});this._adjustReferences("row",e,1,n)},{recalc:!0,layout:!0,insertRow:{index:e},ref:new t(new i(e,0),new i(1/0,1/0))}),this.trigger("afterInsertRow",{index:e}),this},isEnabledRow:function(e){var n=new t(new i(e,0),new i(e,this._grid.columnCount));return new r(n,this).enable()},deleteRow:function(e){if(!this.isEnabledRow(e))return this;if(!this.trigger("deleteRow",{index:e}))return this.batch(function(){var n,o,s,a,l,u,c,h=this._grid,d=h.columnCount,f=this.frozenRows();for(e<f&&this.frozenRows(f-1),n=this._mergedCells.slice(),o=0;o<d;o++)s=new t(new i(e,o),new i(e,o)),new r(s,this).clear({clearAll:!0,keepBorders:!0}),a=h.normalize(s.topLeft),l=h.normalize(s.bottomRight),u=new t(new i(a.row+1,a.col),new i(1/0,l.col)),this._copyRange(u,a),c=h.normalize(u.bottomRight),new r(new t(c,c),this).clear();this._adjustReferences("row",e,-1,n)},{recalc:!0,layout:!0,deleteRow:{index:e},ref:new t(new i(e,0),new i(1/0,1/0))}),this.trigger("afterDeleteRow",{index:e}),this},insertColumn:function(e){if(!this.trigger("insertColumn",{index:e}))return this.batch(function(){var n,o,s,a,l,u,c=this._grid,h=c.columnCount,d=this.frozenColumns();for(e<d&&this.frozenColumns(d+1),n=this._mergedCells.slice(),o=h;o>=e&&(s=new t(new i(0,o),new i(1/0,o)),new r(s,this).clear({clearAll:!0,keepBorders:!0}),o!=e);o--)a=c.normalize(s.topLeft),l=c.normalize(s.bottomRight),u=new t(new i(a.row,a.col-1),new i(l.row,l.col-1)),this._copyRange(u,a);this._adjustReferences("col",e,1,n)},{recalc:!0,layout:!0,insertColumn:{index:e},ref:new t(new i(0,e),new i(1/0,1/0))}),this},isEnabledColumn:function(e){var n=new t(new i(0,e),new i(1/0,e));return new r(n,this).enable()},deleteColumn:function(e){if(!this.isEnabledColumn(e))return this;if(!this.trigger("deleteColumn",{index:e}))return this.batch(function(){var n,o,s,a,l,u,c=this._grid,h=c.columnCount,d=this.frozenColumns();for(e<d&&this.frozenColumns(d-1),n=this._mergedCells.slice(),o=e;o<h&&(s=new t(new i(0,o),new i(1/0,o)),new r(s,this).clear({clearAll:!0,keepBorders:!0}),o!=h-1);o++)a=c.normalize(s.topLeft),l=c.normalize(s.bottomRight),u=new t(new i(a.row,a.col+1),new i(l.row,l.col+1)),this._copyRange(u,a);this._adjustReferences("col",e,-1,n)},{recalc:!0,layout:!0,deleteColumn:{index:e},ref:new t(new i(0,e),new i(1/0,1/0))}),this},_filterRow:function(e){this._rows.hide(e),this._filteredRows.value(e,e,!0),this.triggerChange({layout:!0})},hideRow:function(e){if(!this.trigger("hideRow",{index:e}))return this._property(this._rows.hide.bind(this._rows),e,{layout:!0})},unhideRow:function(e){if(!this.trigger("unhideRow",{index:e}))return this._property(this._rows.unhide.bind(this._rows),e,{layout:!0})},isHiddenRow:function(e){return this._grid._rows.hidden(e)},isFilteredRow:function(e){return this._filteredRows.value(e)},columnWidth:function(e,t){return this._property(this._columns.value.bind(this._columns,e,e),t,{layout:!0})},rowHeight:function(e,t){return this._property(this._rows.value.bind(this._rows,e,e),t,{layout:!0})},frozenRows:function(e){return this._field("_frozenRows",e,{layout:!0})},frozenColumns:function(e){return this._field("_frozenColumns",e,{layout:!0})},showGridLines:function(e){return this._field("_showGridLines",e,{layout:!0})},gridLinesColor:function(e){return this._field("_gridLinesColor",e,{layout:!0})},_ref:function(n,r,o,s){var a=null;return n instanceof e.spreadsheet.Ref?n:n instanceof e.spreadsheet.Range?n._ref.toRangeRef():("string"==typeof n?a=e.spreadsheet.calc.parseReference(n):(o||(o=1),s||(s=1),a=new t(new i(n,r),new i(n+o-1,r+s-1))),a)},range:function(e,t,n,i){return new r(this._ref(e,t,n,i),this)},_getMergedCells:function(e){var t=this._grid,n={},i={},r=!1;return this.forEachMergedCell(e,function(o){var s=o.topLeft;t.forEach(o,function(t){s.eq(t)?(n[t.print()]=o,r=!0):e.contains(t)&&(i[t.print()]=s,r=!0)})}),{primary:n,secondary:i,hasMerged:r}},forEachMergedCell:function(e,t){var n=!1;void 0===t&&(t=e,n=!0),this._mergedCells.forEach(function(i){(n||i.intersects(e))&&t(i)})},forEachFilterHeader:function(e,t){var n,i=!1;void 0===t&&(t=e,i=!0),this._filter&&(n=[],this._filter.ref.forEachColumn(function(t){(i||t.intersects(e))&&n.push(t.topLeft)}),this._mergedCells.forEach(function(e){n=n.map(function(t){return e.intersects(t)?e:t})}),n.reduce(function(e,t){return e.indexOf(t)<0&&e.push(t),e},[]).forEach(t))},forEach:function(e,i){function r(e){function n(e){i(l++,a,e)}var r,s,a,l,u,c;for(e instanceof t||(e=e.toRangeRef()),r=o._grid.normalize(e.topLeft),s=o._grid.normalize(e.bottomRight),a=r.col;a<=s.col;a++)l=r.row,u=o._grid.index(l,a),c=o._grid.index(s.row,a),o._properties.forEach(u,c,n)}var o=this;e instanceof t||(e=o._ref(e)),e instanceof n?e.forEach(r):r(e)},startResizing:function(e){this._initialPosition=e,this._resizeInProgress=!0},startAutoFill:function(){this._autoFillInProgress=!0;var e=this.select();this._autoFillOrigin=e,this._autoFillDest=e,this.triggerChange({selection:!0})},updateAutoFill:function(e,t,n,i){this._autoFillDest=e,this._autoFillPunch=t,this._autoFillHint=n,this._autoFillDirection=i,this.triggerChange({selection:!0})},autoFillRef:function(){return this._autoFillDest},autoFillPunch:function(){return this._autoFillPunch},autoFillInProgress:function(){return this._autoFillInProgress},resizingInProgress:function(){return this._resizeInProgress},draggingInProgress:function(){return this._draggingInProgress},completeResizing:function(){var e,t;this._resizeInProgress&&(this._resizeInProgress=!1,e=this.resizeHintPosition(),this._initialPosition&&e?(t=this.resizeHandlePosition(),t.col!==-(1/0)?this.trigger("commandRequest",{command:"ColumnWidthCommand",options:{target:t.col,value:this.columnWidth(t.col)-(this._initialPosition.x-e.x)}}):this.trigger("commandRequest",{command:"RowHeightCommand",options:{target:t.row,value:this.rowHeight(t.row)-(this._initialPosition.y-e.y)}})):this.trigger("change",{resize:!0}))},_renderComment:function(e){var t=e?this.range(e).comment():null;t?this._commentRef&&e.eq(this._commentRef)||(this._commentRef=e,this.trigger("change",{comment:!0})):this._commentRef&&(this._commentRef=null,this.trigger("change",{comment:!0}))},resizeHandlePosition:function(){return this._resizeHandlePosition},resizeHintPosition:function(e){return void 0!==e&&(this._resizeHintPosition=e,this.trigger("change",{resize:!0})),this._resizeHintPosition},removeResizeHandle:function(){this._resizeHandlePosition&&(this._resizeHintPosition=void 0,this._resizeHandlePosition=void 0,this._initialPosition=void 0,this.trigger("change",{resize:!0}))},positionResizeHandle:function(e){this._resizeHandlePosition=e,this.trigger("change",{resize:!0})},startDragging:function(e){this._draggingInProgress=e},completeDragging:function(){var e,t,n,r,o,s,a=this._draggingInProgress;if(a){if(this._draggingInProgress=null,e=a.drawing,e.eq(a.copy))return;e.topLeftCell&&(t=this.drawingBoundingBox(e),n=this._rows.indexVisible(t.top),r=this._columns.indexVisible(t.left),o=new i(n,r),s=this.refBoundingBox(o),e.offsetX=t.left-s.left,e.offsetY=t.top-s.top,e.topLeftCell=o,this.triggerChange({dragging:!0})),this.trigger("commandRequest",{command:"DrawingUpdateCommand",options:{sheet:this,drawing:e,previous:a.copy}})}},startSelection:function(){this._selectionInProgress=!0},completeSelection:function(){var e,t;this._selectionInProgress&&(this._selectionInProgress=!1,this._resizeHintPosition=void 0,this.trigger("change",{selection:!0})),this._autoFillInProgress&&(this._autoFillInProgress=!1,e=this._autoFillDest,t=this._autoFillOrigin,this._autoFillPunch?this.trigger("commandRequest",{command:"ClearContentCommand",options:{operatingRange:this.range(this._autoFillPunch)}}):e.eq(t)?this.triggerChange({selection:!0}):this.trigger("commandRequest",{command:"AutoFillCommand",options:{operatingRange:this.range(e),origin:this.range(t)}}),this._autoFillDest=null,this._autoFillPunch=null,this._autoFillOrigin=null,this.select(e))},selectionInProgress:function(){return this._selectionInProgress},select:function(e,t){var n,i=this._selectionState();return e&&(e=this._ref(e),e=this._grid.normalize(e),n=this._grid.isAxis(e)?e:this.unionWithMerged(e)),i.select(e,n,t)},originalSelect:function(){return this._selectionState().originalSelection},currentSelectionRange:function(){return this._selectionState().currentSelectionRange()},currentOriginalSelectionRange:function(){return this._selectionState().currentOriginalNavigationRange()},currentNavigationRange:function(){return this._selectionState().currentNavigationRange()},nextNavigationRange:function(){return this._selectionState().nextNavigationRange()},previousNavigationRange:function(){return this._selectionState().previousNavigationRange()},selectionRangeIndex:function(){return this._selectionState().selectionRangeIndex},activeCell:function(e){return this._selectionState().activeCell(e)},originalActiveCell:function(){return this._selectionState().originalActiveCell},singleCellSelection:function(){return this._selectionState().singleCellSelection()},unionWithMerged:function(e){var t=this._mergedCells;return e.map(function(e){return e.toRangeRef().union(t)})},trim:function(e){var t=[],n=this._grid;return this._properties.forEachProperty(function(i){t.push(n.trim(e,i.list))}),this.unionWithMerged(e.topLeft.toRangeRef().union(t))},focus:function(e){if(!e){var t=this._focus;return this._focus=null,t}this._focus=e.toRangeRef()},activeCellSelection:function(){return new r(this._grid.normalize(this.activeCell()),this)},selection:function(){return new r(this._grid.normalize(this._selectionState().selection),this)},selectedHeaders:function(){var e=this.select(),t={},n={},i=!1,r=!1,o=this._grid.rowCount-1,s=this._grid.columnCount-1;return e.forEach(function(e){var a,l,u,c,h,d="partial",f="partial";if(e=e.toRangeRef(),l=e.bottomRight,u=e.topLeft,c=u.col<=0&&l.col>=s,h=u.row<=0&&l.row>=o,h&&(r=!0,f="full"),c&&(i=!0,d="full"),!h)for(a=u.row;a<=l.row;a++)"full"!==t[a]&&(t[a]=d);if(!c)for(a=u.col;a<=l.col;a++)"full"!==n[a]&&(n[a]=f)}),{rows:t,cols:n,allRows:r,allCols:i,all:r&&i}},isInEditMode:function(e){return void 0===e?this._inEdit:(this._inEdit=e,void(e&&(this._editSelection.selection=this._viewSelection.selection.clone(),this._editSelection.originalSelection=this._viewSelection.originalSelection.clone(),this._editSelection._activeCell=this._viewSelection._activeCell.clone(),this._editSelection.originalActiveCell=this._viewSelection.originalActiveCell.clone())))},_setFormulaSelections:function(e){this._formulaSelections=(e||[]).slice(),this.triggerChange({selection:!0})},_viewActiveCell:function(){return this._viewSelection._activeCell.toRangeRef()},toJSON:function(){function t(e){Object.keys(l).forEach(function(t){e[t]===l[t]&&delete e[t]})}var n,i={},r=this._rows.toJSON("height",i),o=this._columns.toJSON("width",{}),s=this._viewSelection,a=[],l=this._defaultCellStyle||{};return this.forEach(e.spreadsheet.SHEETREF,function(n,o,s){if(t(s),0!==Object.keys(s).length){s.link&&a.push({ref:e.spreadsheet.Ref.display(null,n,o),target:s.link});var l=i[n];void 0===l&&(l=r.length,r.push({index:n}),i[n]=l),n=r[l],s.index=o,void 0===n.cells&&(n.cells=[]),s.formula&&(s.formula=s.formula.arrayFormulaRange?{src:""+s.formula,ref:""+s.formula.arrayFormulaRange}:""+s.formula),s.validation&&(s.validation=s.validation.toJSON()),s.color&&(s.color=e.parseColor(s.color).toCss()),s.background&&(s.background=e.parseColor(s.background).toCss()),s.borderTop&&s.borderTop.color&&(s.borderTop.color=e.parseColor(s.borderTop.color).toCss()),s.borderBottom&&s.borderBottom.color&&(s.borderBottom.color=e.parseColor(s.borderBottom.color).toCss()),s.borderRight&&s.borderRight.color&&(s.borderRight.color=e.parseColor(s.borderRight.color).toCss()),s.borderLeft&&s.borderLeft.color&&(s.borderLeft.color=e.parseColor(s.borderLeft.color).toCss()),n.cells.push(s)}}),n={name:this._name(),rows:r,columns:o,selection:""+s.selection,activeCell:""+s.activeCell(),frozenRows:this.frozenRows(),frozenColumns:this.frozenColumns(),showGridLines:this.showGridLines(),gridLinesColor:this.gridLinesColor(),mergedCells:this._mergedCells.map(function(e){return""+e}),hyperlinks:a,defaultCellStyle:l,drawings:this._drawings.map(function(e){return e.toJSON()})},this._sort&&(n.sort={ref:""+this._sort.ref,columns:this._sort.columns.map(function(e){return{index:e.index,ascending:e.ascending}})}),this._filter&&(n.filter={ref:""+this._filter.ref,columns:this._filter.columns.map(function(e){var t=e.filter.toJSON();return t.index=e.index,t})}),n},fromJSON:function(t){this.batch(function(){var n,i,r,o,s,a,u,c,h,d,f,p;if(void 0!==t.name&&this._name(t.name),void 0!==t.frozenColumns&&this.frozenColumns(t.frozenColumns),void 0!==t.frozenRows&&this.frozenRows(t.frozenRows),void 0!==t.columns&&this._columns.fromJSON("width",t.columns),void 0!==t.rows)for(this._rows.fromJSON("height",t.rows),n=0;n<t.rows.length;n++)if(i=t.rows[n],r=i.index,void 0===r&&(r=n),i.cells)for(o=0;o<i.cells.length;o++)s=i.cells[o],a=s.index,void 0===a&&(a=o),s.formula&&(u="string"!=typeof s.formula,c=u?s.formula.src:s.formula,h=this._compileFormula(r,a,c),u&&h.setArrayFormulaRange(e.spreadsheet.calc.parseReference(s.formula.ref)),s.formula=h),s.validation&&(s.validation=this._compileValidation(r,a,s.validation)),this._properties.fromJSON(this._grid.index(r,a),s);t.drawings&&(this._drawings=t.drawings.map(l.fromJSON)),t.selection&&(this._viewSelection.selection=this._viewSelection.originalSelection=this._ref(t.selection)),t.activeCell&&(d=this._ref(t.activeCell),this._viewSelection._activeCell=d.toRangeRef(),this._viewSelection.originalActiveCell=d.first()),t.mergedCells&&t.mergedCells.forEach(function(e){this.range(e).merge()},this),t.sort&&(this._sort={ref:this._ref(t.sort.ref),columns:t.sort.columns.slice(0)}),t.filter&&(f=t.filter.ref,p=void 0===t.filter.columns?[]:t.filter.columns,f?(this._filter={ref:this._ref(f),columns:p.map(function(t){return{index:t.index,filter:e.spreadsheet.Filter.create(t)}})},this._refreshFilter()):e.logToConsole("Dropping filter for sheet '"+t.name+"' due to missing ref")),void 0!==t.showGridLines&&(this._showGridLines=t.showGridLines),this._gridLinesColor=t.gridLinesColor}),this._rows._refresh(),this._columns._refresh()},formula:function(e){return this._properties.get("formula",this._grid.cellRefIndex(e))},validation:function(e){return this._properties.get("validation",this._grid.cellRefIndex(e))},resetFormulas:function(){this._forFormulas(function(e){e.reset()})},resetValidations:function(){this._forValidations(function(e){e.reset()})},recalc:function(e,t){function n(){o--,s!=r||o||t()}var i=this._properties.get("formula").values(),r=i.length,o=0,s=0;if(!r&&t)return t();for(;s<r;)o++,i[s++].value.exec(e,t?n:null)},revalidate:function(e){var n=this;this._forValidations(function(r){var o=new i(r.row,r.col),s=new t(o,o);r.exec(e,n._get(s,"value"),n._get(s,"format"))})},_value:function(e,t,n){var i=this._grid.index(e,t);return void 0===n?this._properties.get("value",i):void this._properties.set("value",i,i,n)},_validation:function(e,t){var n=this._grid.index(e,t);return this._properties.get("validation",n)},_compileValidation:function(t,n,i){return i instanceof e.spreadsheet.validation.Validation?i.clone(this._name(),t,n):(null!=i.from&&(i.from=(i.from+"").replace(/^=/,"")),null!=i.to&&(i.to=(i.to+"").replace(/^=/,"")),e.spreadsheet.validation.compile(this._name(),t,n,i))},_compileFormula:function(t,n,i){return i=i.replace(/^=/,""),i=e.spreadsheet.calc.parseFormula(this._name(),t,n,i),e.spreadsheet.calc.compile(i)},_copyValuesInRange:function(e,t,n,i){var r,o,s,a,l;for(r=e.col;r<=t.col;r++)for(o=this._grid.index(e.row,r),s=this._grid.index(t.row,r),a=o,l=e.row;a<=s;++a,++l)n=n.clone(this._name(),l,r),this._properties.set(i,a,a,n);return n},_set:function(t,n,i){var r,o,s,a=this._grid.normalize(t.topLeft),l=this._grid.normalize(t.bottomRight);if("number"==typeof i&&(i=e.spreadsheet.calc.runtime.limitPrecision(i)),i&&"formula"==n)"string"==typeof i&&(i=this._compileFormula(a.row,a.col,i)),i=this._copyValuesInRange(a,l,i,"formula");else if(i&&"validation"==n)i=this._compileValidation(a.row,a.col,i),i=this._copyValuesInRange(a,l,i,"validation");else for(r=a.col;r<=l.col;r++)o=this._grid.index(a.row,r),s=this._grid.index(l.row,r),this._properties.set(n,o,s,i),"formula"==n&&this._properties.set("value",o,s,null)},_get:function(e,t){var n=this._grid.normalize(e.topLeft),i=this._grid.index(n.row,n.col);return this._properties.get(t,i)},batch:function(e,t){var n=this.suspendChanges();return this.suspendChanges(!0),e.call(this),this.suspendChanges(n).triggerChange(t||{recalc:!0})},_sortBy:function(e,t){var n=null;t.forEach(function(t){n=this._sorter.sortBy(e,t.index,this._properties.get("value"),t.ascending,n)},this),this._sort={ref:e,columns:t},this._refreshFilter(),this.forEach(e,function(e,t,n){var i,r,o=n.formula;o&&(i=e-o.row,0!==i&&(r=i>0?o.row:o.row+i,o.adjust(this.name(),"row",r,i)))}.bind(this)),this.triggerChange({recalc:!0})},_refreshFilter:function(){this._filter&&this._filterBy(this._filter.ref,this._filter.columns)},_filterBy:function(t,n){this.batch(function(){for(var i=t.topLeft.row;i<=t.bottomRight.row;i++)this.isFilteredRow(i)&&(this._filteredRows.value(i,i,!1),this._rows.unhide(i));n.forEach(function(n){var i,r,o,s=t.resize({top:1}).toColumn(n.index),a=[];if(s!==e.spreadsheet.NULLREF)for(this.forEach(s,function(e,t,n){n.row=e,a.push(n)}),n.filter.prepare(a),i=0;i<a.length;i++)r=a[i],o=n.filter.value(r),n.filter.matches(o)===!1&&this._filterRow(r.row)},this),this._filter={ref:t,columns:n}},{recalc:!0,layout:!0,filter:!0})},filterColumn:function(e){var t=this.filter().ref;return e.toRangeRef().topLeft.col-t.topLeft.col},filter:function(){return this._filter},clearFilter:function(e){this._clearFilter(e instanceof Array?e:[e])},_clearFilter:function(e){this._filter&&this.batch(function(){this._filter.columns=this._filter.columns.filter(function(t){return e.indexOf(t.index)<0}),this._refreshFilter()},{recalc:!0,layout:!0,filter:!0})},getAxisState:function(){return{rows:this._rows.getState(),columns:this._columns.getState()}},setAxisState:function(e){this._rows.setState(e.rows),this._columns.setState(e.columns),this.triggerChange({layout:!0})},getState:function(){return{rows:this._rows.getState(),columns:this._columns.getState(),mergedCells:this._mergedCells.map(function(e){return e.clone()}),properties:this._properties.getState()}},setState:function(t){this._rows.setState(t.rows),this._columns.setState(t.columns),this._mergedCells=t.mergedCells,this._properties.setState(t.properties),this.triggerChange(e.spreadsheet.ALL_REASONS)},_merge:function(t){var n,i=this._mergedCells,o=this;return this.batch(function(){n=t.map(function(t){var n,s,a,l,u,c,h;return t instanceof e.spreadsheet.CellRef?t:(n=t.toRangeRef().union(i,function(e){i.splice(i.indexOf(e),1)}),s=new r(n,o),a=s._get("formula"),l=s.value(),u=s.format(),c=s.background(),s.value(null),s.format(null),s.background(null),h=new r(n.collapse(),o),a?h._set("formula",a):h.value(l),h.format(u),h.background(c),i.push(n),n)});var s=o._viewSelection;s.selection=o.unionWithMerged(s.originalSelection),s._activeCell=o.unionWithMerged(s.originalActiveCell)},{activeCell:!0,selection:!0}),n},_useCultureDecimals:function(){return this._workbook&&this._workbook.options.useCultureDecimals},withCultureDecimals:function(t){var n=".";return this._useCultureDecimals()&&(n=e.culture().numberFormat["."]),e.spreadsheet.calc.withDecimalSeparator(n,t)},drawingBoundingBox:function(t){var n=t.offsetX,i=t.offsetY;return t.topLeftCell&&(n+=this._columns.sum(0,t.topLeftCell.col-1),i+=this._rows.sum(0,t.topLeftCell.row-1)),new e.spreadsheet.Rectangle(n,i,t.width,t.height)},refBoundingBox:function(e){return this._grid.rectangle(e.toRangeRef())},addDrawing:function(e,t){return e instanceof l||(e=new l(e)),this._drawings.push(e),t&&(this._activeDrawing=e),this.triggerChange({layout:!0}),e},removeDrawing:function(e){var t=this._drawings.indexOf(e);t>=0&&(this._drawings.splice(t,1),this.triggerChange({layout:!0}))},usesImage:function(e){for(var t=this._drawings.length;--t>=0;)if(this._drawings[t].image===e)return!0;return!1}}),l=e.Class.extend({init:function(e){this.reset(e)},toJSON:function(){return{topLeftCell:""+this.topLeftCell,offsetX:this.offsetX,offsetY:this.offsetY,width:this.width,height:this.height,image:this.image,opacity:this.opacity}},clone:function(){return new l(this)},reset:function(t){var n=t.topLeftCell;"string"==typeof n&&(n=e.spreadsheet.calc.parseReference(n)),this.topLeftCell=n,this.offsetX=t.offsetX||0,this.offsetY=t.offsetY||0,this.width=t.width,this.height=t.height,this.image=t.image,this.opacity=null!=t.opacity?t.opacity:1},eq:function(e){return(!this.topLeftCell&&!e.topLeftCell||this.topLeftCell&&e.topLeftCell&&this.topLeftCell.eq(e.topLeftCell))&&this.offsetX===e.offsetX&&this.offsetY===e.offsetY&&this.width===e.width&&this.height===e.height&&this.image===e.image&&this.opacity===e.opacity}}),l.fromJSON=function(e){return new l(e)},e.spreadsheet.Sheet=a,e.spreadsheet.Drawing=l)}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/sheetsbar.min",["kendo.core.min","kendo.sortable.min"],e)}(function(){!function(e){var t,n,i,r,o,s;e.support.browser.msie&&e.support.browser.version<9||(t=e.jQuery,n=e._outerWidth,i=".",r=" ",o={sheetsBarWrapper:"k-widget k-header",sheetsBarSheetsWrapper:"k-tabstrip k-floatwrap k-tabstrip-bottom",sheetsBarActive:"k-spreadsheet-sheets-bar-active",sheetsBarInactive:"k-spreadsheet-sheets-bar-inactive",sheetsBarAdd:"k-spreadsheet-sheets-bar-add",sheetsBarRemove:"k-spreadsheet-sheets-remove",sheetsBarItems:"k-spreadsheet-sheets-items",sheetsBarEditor:"k-spreadsheet-sheets-editor",sheetsBarScrollable:"k-tabstrip-scrollable",sheetsBarNext:"k-tabstrip-next",sheetsBarPrev:"k-tabstrip-prev",sheetsBarKItem:"k-item k-state-default",sheetsBarKActive:"k-state-active k-state-tab-on-top",sheetsBarKTextbox:"k-textbox",sheetsBarKLink:"k-link",sheetsBarKIcon:"k-icon",sheetsBarKFontIcon:"k-icon",sheetsBarKButton:"k-button k-button-icon",sheetsBarKButtonBare:"k-bare",sheetsBarKArrowW:"k-i-arrow-60-left",sheetsBarKArrowE:"k-i-arrow-60-right",sheetsBarKReset:"k-reset k-tabstrip-items",sheetsBarKIconX:"k-i-close",sheetsBarKSprite:"k-sprite",sheetsBarKIconPlus:"k-i-plus",sheetsBarHintWrapper:"k-widget k-tabstrip k-tabstrip-bottom k-spreadsheet-sheets-items-hint",sheetsBarKResetItems:"k-reset k-tabstrip-items"},s=e.ui.Widget.extend({init:function(t,n){var r=s.classNames;e.ui.Widget.call(this,t,n),
t=this.element,t.addClass(r.sheetsBarWrapper),this._openDialog=n.openDialog,this._tree=new e.dom.Tree(t[0]),this._tree.render([this._addButton(),this._createSheetsWrapper([])]),this._toggleScrollEvents(!0),this._createSortable(),this._sortable.bind("start",this._onSheetReorderStart.bind(this)),this._sortable.bind("end",this._onSheetReorderEnd.bind(this)),t.on("click",i+r.sheetsBarRemove,this._onSheetRemove.bind(this)),t.on("click","li",this._onSheetSelect.bind(this)),t.on("dblclick","li"+i+r.sheetsBarActive,this._createEditor.bind(this)),t.on("click",i+r.sheetsBarAdd,this._onAddSelect.bind(this))},options:{name:"SheetsBar",scrollable:{distance:200}},events:["select","reorder","rename"],_createEditor:function(){this._editor||(this._renderSheets(this._sheets,this._selectedIndex,!0),this._editor=this.element.find(e.format("input{0}{1}",i,s.classNames.sheetsBarEditor)).focus().on("keydown",this._onEditorKeydown.bind(this)).on("blur",this._onEditorBlur.bind(this)))},_destroyEditor:function(e){var t=e?null:this._editor.val();this._editor.off(),this._editor=null,this._renderSheets(this._sheets,this._selectedIndex,!1),this._onSheetRename(t)},renderSheets:function(e,t){!e||t<0||this._renderSheets(e,t,!1)},_renderSheets:function(t,o,a){var l,u,c,h,d,f,p,m,g,v,b,w=this,y=s.classNames;w._isRtl=e.support.isRtl(w.element),w._sheets=t,w._selectedIndex=o,w._renderHtml(a,!0),w._scrollableAllowed()&&(c=w._sheetsWrapper(),h=c.children(i+y.sheetsBarPrev),d=c.children(i+y.sheetsBarNext),f=2,p=w.element.find(i+y.sheetsBarAdd),m=n(p)+p.position().left+f,g=n(h)+f,v=w._sheetsGroup(),h.css({left:m}),c.addClass(y.sheetsBarScrollable+r+y.sheetsBarSheetsWrapper),v.css({marginLeft:m}),l=c[0].offsetWidth,u=v[0].scrollWidth,u+m>l?(b=Math.ceil(e.parseFloat(d.css("right"))),w._scrollableModeActive||(w._nowScrollingSheets=!1,w._scrollableModeActive=!0),v.css({marginLeft:g+m,marginRight:n(d)+b+f})):w._scrollableModeActive&&u<=l?(w._scrollableModeActive=!1,v.css({marginLeft:m,marginRight:""})):v.css({marginLeft:m}),w._toggleScrollButtons())},_toggleScrollButtons:function(e){var t=this,n=t._sheetsGroup(),r=t._sheetsWrapper(),o=n.scrollLeft(),a=r.find(i+s.classNames.sheetsBarPrev),l=r.find(i+s.classNames.sheetsBarNext);e===!1?(a.toggle(!1),l.toggle(!1)):(a.toggle(t._isRtl?o<n[0].scrollWidth-n[0].offsetWidth-1:0!==o),l.toggle(t._isRtl?0!==o:o<n[0].scrollWidth-n[0].offsetWidth-1))},_toggleScrollEvents:function(e){var t=this,n=s.classNames,r=t.options,o=t._sheetsWrapper(),a=o.children(i+n.sheetsBarPrev),l=o.children(i+n.sheetsBarNext);e?(a.on("mousedown",function(){t._nowScrollingSheets=!0,t._scrollSheetsByDelta(r.scrollable.distance*(t._isRtl?1:-1))}),l.on("mousedown",function(){t._nowScrollingSheets=!0,t._scrollSheetsByDelta(r.scrollable.distance*(t._isRtl?-1:1))}),a.add(l).on("mouseup",function(){t._nowScrollingSheets=!1})):(a.off(),l.off())},_renderHtml:function(t,n){var i,o,a,l,u,c,h=[],d=e.dom,f=d.element,p=this._sheets,m=this._selectedIndex,g=s.classNames;for(i=0;i<p.length;i++)o=p[i],a=i===m,l={className:g.sheetsBarKItem+r},u=[],l.className+=a?g.sheetsBarKActive+r+g.sheetsBarActive:g.sheetsBarInactive,a&&t?u.push(f("input",{type:"text",value:o.name(),className:g.sheetsBarKTextbox+r+g.sheetsBarEditor,maxlength:50},[])):(u.push(f("span",{className:g.sheetsBarKLink,title:o.name()},[d.text(o.name())])),p.length>1&&(c=f("span",{className:g.sheetsBarKIcon+r+g.sheetsBarKFontIcon+r+g.sheetsBarKIconX},[]),u.push(f("span",{className:g.sheetsBarKLink+r+g.sheetsBarRemove},[c])))),h.push(f("li",l,u));this._tree.render([this._addButton(),this._createSheetsWrapper(h,n)])},_createSheetsWrapper:function(t,n){var i,o=e.dom.element,a=s.classNames,l=[o("ul",{className:a.sheetsBarKReset},t)];return n=!0,n&&(i=a.sheetsBarKButton+r+a.sheetsBarKButtonBare+r,l.push(o("span",{className:i+a.sheetsBarPrev},[o("span",{className:a.sheetsBarKIcon+r+a.sheetsBarKArrowW},[])])),l.push(o("span",{className:i+a.sheetsBarNext},[o("span",{className:a.sheetsBarKIcon+r+a.sheetsBarKArrowE},[])]))),o("div",{className:a.sheetsBarItems},l)},_createSortable:function(){var n=s.classNames;this._sortable=new e.ui.Sortable(this.element,{filter:e.format("ul li.{0},ul li.{1}",n.sheetsBarActive,n.sheetsBarInactive),container:i+n.sheetsBarItems,axis:"x",animation:!1,ignore:"input",end:function(){this.draggable.hint&&this.draggable.hint.remove()},hint:function(e){var i=t(e).clone();return i.wrap("<div class='"+n.sheetsBarHintWrapper+"'><ul class='"+n.sheetsBarKResetItems+"'></ul></div>").closest("div")}})},_onEditorKeydown:function(e){this._editor&&(13===e.which&&this._destroyEditor(),27===e.which&&this._destroyEditor(!0))},_onEditorBlur:function(){this._editor&&this._destroyEditor()},_onSheetReorderEnd:function(e){e.preventDefault(),this.trigger("reorder",{oldIndex:e.oldIndex,newIndex:e.newIndex})},_onSheetReorderStart:function(e){this._editor&&e.preventDefault()},_onSheetRemove:function(e){var n,i=t(e.target).closest("li").text();this._editor&&this._destroyEditor(),n=function(e){var t=e.sender;t.isConfirmed()&&this.trigger("remove",{name:i,confirmation:!0})}.bind(this),this._openDialog("confirmation",{close:n})},_onSheetSelect:function(e){var n=t(e.target).text();return t(e.target).is(i+s.classNames.sheetsBarEditor)||!n?void e.preventDefault():(this._editor&&this._destroyEditor(),this._scrollSheetsToItem(t(e.target).closest("li")),void this.trigger("select",{name:n,isAddButton:!1}))},_onSheetRename:function(e){this._sheets[this._selectedIndex].name()!==e&&null!==e&&this.trigger("rename",{name:e,sheetIndex:this._selectedIndex})},_onAddSelect:function(){this.trigger("select",{isAddButton:!0})},_addButton:function(){var t=e.dom.element,n=s.classNames;return t("a",{className:n.sheetsBarAdd+r+n.sheetsBarKButton},[t("span",{className:n.sheetsBarKIcon+r+n.sheetsBarKFontIcon+r+n.sheetsBarKIconPlus},[])])},destroy:function(){this._sortable.destroy()},_scrollableAllowed:function(){var e=this.options;return e.scrollable&&!isNaN(e.scrollable.distance)},_scrollSheetsToItem:function(e){var t,i,r,o,s,a,l,u=this;u._scrollableModeActive&&(t=u._sheetsGroup(),i=t.scrollLeft(),r=n(e),o=u._isRtl?e.position().left:e.position().left-t.children().first().position().left,s=t[0].offsetWidth,a=Math.ceil(parseFloat(t.css("padding-left"))),u._isRtl?o<0?l=i+o-(s-i)-a:o+r>s&&(l=i+o-r+2*a):i+s<o+r?l=o+r-s+2*a:i>o&&(l=o-a),t.finish().animate({scrollLeft:l},"fast","linear",function(){u._toggleScrollButtons()}))},_sheetsGroup:function(){return this._sheetsWrapper().children("ul")},_sheetsWrapper:function(){return this.element.find(i+s.classNames.sheetsBarItems)},_scrollSheetsByDelta:function(e){var t=this,n=t._sheetsGroup(),i=n.scrollLeft();n.finish().animate({scrollLeft:i+e},"fast","linear",function(){t._nowScrollingSheets?t._scrollSheetsByDelta(e):t._toggleScrollButtons()})}}),e.spreadsheet.SheetsBar=s,t.extend(!0,s,{classNames:o}))}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/calc.min",["spreadsheet/runtime.min"],e)}(function(){"use strict";function e(e){D.DEC=e,D.ARG=","==e?";":",",D.COL=","==e?"\\":",",A=","==e?S:R}function t(e){e=e.toUpperCase();for(var t=0,n=0;n<e.length;++n)t=26*t+e.charCodeAt(n)-64;return t-1}function n(e){return parseInt(e,10)-1}function i(e,i){var r,o,s,a,l,u;if("#sheet"==e.toLowerCase())return w.SHEETREF;e:{if(!(r=/^(\$)?([a-z]+)(\$)?(\d+)$/i.exec(e))){for(a=c(e,{}),l=[];;){if(u=a.next(),u instanceof k)u.rel=0;else{if(!(u instanceof _))break e;u.topLeft.rel=0,u.bottomRight.rel=0}if(l.push(u),a.eof())break;if(!a.is("op",D.ARG))break e;a.next()}return 1==l.length?l[0]:new w.UnionRef(l)}if(o=n(r[4]),s=t(r[2]),o<1048576&&s<16384)return new k(n(r[4]),t(r[2]))}if(!i)throw Error("Cannot parse reference: "+e)}function r(e,t,n,i){function r(e){return e.index=v.length,v.push(e),e}function o(e,t,n){if(g(e,t))return i.next();var r=i.peek();r?i.croak("Expected "+e+" «"+t+"» but found "+r.type+" «"+r.value+"»"):n||i.croak("Expected "+e+" «"+t+"»")}function s(e){return m(f(h()),0,e)}function a(e){return"TRUE"==e.upper||"FALSE"==e.upper?"TRUE"==e.upper?B:M:r(new x(e.value))}function l(){var e,t=i.next();for(t=t.value,o("punc","("),e=[];;){if(g("punc",")"))break;if(g("op",D.ARG))e.push({type:"null"}),i.next();else{if(e.push(s(!1)),i.eof()||g("punc",")"))break;o("op",D.ARG)}}return o("punc",")",!0),{type:"func",func:t,args:e}}function u(t){return t.hasSheet()||t.setSheet(e),r(t)}function h(){var e;return g("ref")?e=u(i.next()):g("func")?e=l():g("punc","(")?(i.next(),e=s(!0),o("punc",")",!0)):g("punc","{")?(i.next(),e=d(),o("punc","}",!0)):g("num")||g("str")||g("error")?e=i.next():g("sym")?e=a(i.next()):g("op","+")||g("op","-")?e={type:"prefix",op:i.next().value,exp:h()}:i.croak(i.peek()?g("punc","[")?"External reference not supported":"Parse error":"Incomplete expression"),p(e)}function d(){for(var e=[],t=[e],n=!0;!i.eof()&&!g("punc","}");)n?n=!1:g(null,";")?(t.push(e=[]),i.next()):o(null,D.COL),e.push(s(!1));return{type:"matrix",value:t}}function f(e){return g("punc","(")||g("ref")||g("num")||g("func")?{type:"binary",op:" ",left:e,right:s(!1)}:e}function p(e){return g("op","%")?(i.next(),p({type:"postfix",op:"%",exp:e})):e}function m(e,t,n){var r,o,s=g("op");return s&&(n||s.value!=D.ARG)&&(r=A[s.value],r>t)?(i.next(),o=m(h(),r,n),m({type:"binary",op:";"==s.value?",":s.value,left:e,right:o},t,n)):e}var g,v=[];return i=c(i,{row:t,col:n}),g=i.is,{type:"exp",ast:s(!0),refs:v,sheet:e,row:t,col:n}}function o(e,t){var n,i=r(null,0,0,e);if(!(i.ast instanceof x))throw new E("Invalid name: "+e);return i=i.ast,t instanceof y||(n=r(i.sheet,0,0,t),t=n.ast instanceof y?n.ast:/^(?:str|num|bool|error)$/.test(n.ast.type)?n.ast.value:l(n)),{name:i,value:t}}function s(e){function t(e,i,r){function o(t){var o=e.op,s=A[o]<r||!r&&","==o||"prefix"==i.type&&r==A[o]&&"-"==i.op||"binary"==i.type&&r==A[o]&&e===i.right;return n(t(),s)}switch(e.type){case"num":return"(kendo.spreadsheet.calc._separators.DEC == '.' ? "+JSON.stringify(JSON.stringify(e.value))+" : "+JSON.stringify(JSON.stringify(e.value))+".replace('.' , kendo.spreadsheet.calc._separators.DEC))";case"bool":return JSON.stringify(e.value);case"error":return JSON.stringify("#"+e.value);case"str":return JSON.stringify(JSON.stringify(e.value));case"ref":return"this.refs["+e.index+"].print(row, col, mod)";case"prefix":return o(function(){return JSON.stringify(e.op)+" + "+t(e.exp,e,A[e.op])});case"postfix":return o(function(){return t(e.exp,e,A[e.op])+" + "+JSON.stringify(e.op)});case"binary":return o(function(){var i=n(t(e.left,e,A[e.op]),e.left instanceof x&&":"==e.op),r=n(t(e.right,e,A[e.op]),e.right instanceof x&&":"==e.op);return/^[,;]/.test(e.op)?i+" + kendo.spreadsheet.calc._separators.ARG + "+r:i+" + "+JSON.stringify(e.op)+" + "+r});case"func":return JSON.stringify(e.func+"(")+" + "+(e.args.length>0?e.args.map(function(n){return t(n,e,0)}).join(" + kendo.spreadsheet.calc._separators.ARG + ' ' + "):"''")+" + ')'";case"matrix":return"'{ ' + "+e.value.map(function(n){return n.map(function(n){return t(n,e,0)}).join(" + kendo.spreadsheet.calc._separators.COL + ' ' + ")}).join(" + '; ' + ")+"+ ' }'";case"null":return"''"}throw Error("Cannot make printer for node "+e.type)}function n(e,t){return t?"'(' + "+e+" + ')'":e}return T("function(row, col, mod){return("+t(e.ast,e,0)+")}")}function a(e,t){function n(e,t){switch(e.type){case"ref":return i(e,t);case"num":case"str":case"null":case"error":case"bool":return r(e,t);case"prefix":case"postfix":return s(e,t);case"binary":return a(e,t);case"func":return h(e,t);case"lambda":return d(e,t);case"matrix":return f(e.value,t,!0)}throw Error("Cannot CPS "+e.type)}function i(e,t){return"name"==e.ref?o(e,t):r(e,t)}function r(e,t){return t(e)}function o(e,t){return{type:"func",func:",getname",args:[p(t),e]}}function s(e,t){return n({type:"func",func:"unary"+e.op,args:[e.exp]},t)}function a(e,t){return n({type:"func",func:"binary"+e.op,args:[e.left,e.right]},t)}function l(e,t,i,r){return n(e,function(e){var o=p(r),s=m("T"),a=m("E");return{type:"func",func:"if",args:[o,e,{type:"lambda",vars:[s],body:n(t||B,function(e){return{type:"call",func:{type:"var",name:s},args:[e]}})},{type:"lambda",vars:[a],body:n(i||M,function(e){return{type:"call",func:{type:"var",name:a},args:[e]}})}]}})}function u(e,t){return 0===e.length?r(B,t):n({type:"func",func:"IF",args:[e[0],{type:"func",func:"AND",args:e.slice(1)},M]},t)}function c(e,t){return 0===e.length?r(M,t):n({type:"func",func:"IF",args:[e[0],B,{type:"func",func:"OR",args:e.slice(1)}]},t)}function h(e,t){switch(e.func.toLowerCase()){case"if":return l(e.args[0],e.args[1],e.args[2],t);case"and":return u(e.args,t);case"or":return c(e.args,t);case"true":return t(B);case"false":return t(M)}return function i(t,r){return r==e.args.length?{type:"func",func:e.func,args:t}:n(e.args[r],function(e){return i(t.concat([e]),r+1)})}([p(t)],0)}function d(e,t){var i=m("K"),r=n(e.body,function(e){return{type:"call",func:{type:"var",value:i},args:[e]}});return t({type:"lambda",vars:[i].concat(e.vars),body:r})}function f(e,t,i){var r=[];return function o(s){return s==e.length?t({type:"matrix",value:r}):(i?f:n)(e[s],function(e){return r[s]=e,o(s+1)})}(0)}function p(e){var t=m("R");return{type:"lambda",vars:[t],body:e({type:"var",name:t})}}function m(e){return e||(e=""),e="_"+e,e+ ++g}var g=0;return n(e,t)}function l(e){function t(e){var i=e.type;if("num"==i)return e.value+"";if("str"==i)return JSON.stringify(e.value);if("error"==i)return"context.error("+JSON.stringify(e.value)+")";if("return"==i)return"context.resolve("+t(e.value)+")";if("func"==i)return"context.func("+JSON.stringify(e.func)+", "+t(e.args[0])+", "+n(e.args.slice(1))+")";if("call"==i)return t(e.func)+"("+e.args.map(t).join(", ")+")";if("ref"==i)return"refs["+e.index+"]";if("bool"==i)return""+e.value;if("if"==i)return"(context.bool("+t(e.co)+") ? "+t(e.th)+" : "+t(e.el)+")";if("lambda"==i)return"(function("+e.vars.join(", ")+"){ return("+t(e.body)+") })";if("var"==i)return e.name;if("matrix"==i)return n(e.value);if("null"==i)return"null";throw Error("Cannot compile expression "+i)}function n(e){return"[ "+e.map(t).join(", ")+" ]"}var i,r=s(e),o=r.call(e),l=I[o];return l?l.clone(e.sheet,e.row,e.col):(i=t(a(e.ast,function(e){return{type:"return",value:e}})),i=["function(){","var context = this, refs = context.formula.absrefs",i,"}"].join(";\n"),l=new F.Formula(e.refs,T(i),r,e.sheet,e.row,e.col),I[o]=l.clone(e.sheet,e.row,e.col),l)}function u(e){return e}function c(e,i){function r(e,t){var n=o();return null==n||null!=e&&n.type!==e||null!=t&&n.value!==t?null:n}function o(){return null==C&&(C=a()),C}function s(){if(null!=C){var e=C;return C=null,e}return a()}function a(){var t,n=e.peek();return n&&("sym"!=n.type&&"rc"!=n.type&&"num"!=n.type||(t=w(8,c)||w(6,h)||w(6,p)||w(4,m)||w(4,g)||w(2,v)||w(2,b)),t||(t=e.next())),t}function l(r,o){var s,a,l,u,c;if("rc"==r.type)return!r.rel||i.forEditor||null!=i.row&&null!=i.col||e.croak("Cannot read relative cell in RC notation"),new k(r.row,r.col,r.rel);if("num"==r.type)return r.value<=1048577?F(new k(n(r.value),o?-(1/0):+(1/0),2)):null;if(s=r.value,a=/^(\$)?([a-z]+)(\$)?(\d+)$/i.exec(s))return l=n(a[4]),u=t(a[2]),l<=1048576&&u<=16383?F(new k(n(a[4]),t(a[2]),(a[1]?0:1)|(a[3]?0:2))):null;if(c="$"==s.charAt(0),c&&(s=s.substr(1)),/^\d+$/.test(s)){if(l=n(s),l<=1048576)return F(new k(n(s),o?-(1/0):+(1/0),c?0:2))}else if(u=t(s),u<=16383)return F(new k(o?-(1/0):+(1/0),t(s),c?0:1))}function c(e,t,n,i,r,o,s,a){if("sym"==e.type&&"op"==t.type&&":"==t.value&&"sym"==n.type&&"punc"==i.type&&"!"==i.value&&("sym"==r.type||"rc"==r.type||"num"==r.type&&r.value==r.value|0)&&"op"==o.type&&":"==o.value&&("sym"==s.type||"rc"==s.type||"num"==s.type&&s.value==s.value|0)&&s.type==r.type&&("punc"!=a.type||"("!=a.value||s.space)){var u=l(r,!0),c=l(s,!1);if(u&&c)return y(7),R(new _(u.setSheet(e.value,!0),c.setSheet(n.value,!0)).setSheet(e.value,!0),e,s)}}function h(e,t,n,i,r,o){var s,a;if("sym"==e.type&&"op"==t.type&&":"==t.value&&"sym"==n.type&&"punc"==i.type&&"!"==i.value&&("sym"==r.type||"rc"==r.type||"num"==r.type&&r.value==r.value|0)&&("punc"!=o.type||"("!=o.value||r.space)&&(s=l(r)))return y(5),a=s.clone(),R(new _(s.setSheet(e.value,!0),a.setSheet(n.value,!0)).setSheet(e.value,!0),e,r)}function p(e,t,n,i,r,o){if("sym"==e.type&&"punc"==t.type&&"!"==t.value&&("sym"==n.type||"rc"==n.type||"num"==n.type&&n.value==n.value|0)&&"op"==i.type&&":"==i.value&&("sym"==r.type||"rc"==r.type||"num"==r.type&&r.value==r.value|0)&&("punc"!=o.type||"("!=o.value||r.space)){var s=l(n,!0),a=l(r,!1);if(s&&a)return y(5),R(new _(s,a).setSheet(e.value,!0),e,r)}}function m(e,t,n,i){if("sym"==e.type&&"punc"==t.type&&"!"==t.value&&("sym"==n.type||"rc"==n.type||"num"==n.type&&n.value==n.value|0)&&("punc"!=i.type||"("!=i.value||n.space)){y(3);var r=l(n);return r&&isFinite(r.row)||(r=new x(n.value)),R(r.setSheet(e.value,!0),e,n)}}function g(e,t,n,i){if(("sym"==e.type||"rc"==e.type||"num"==e.type&&e.value==e.value|0)&&"op"==t.type&&":"==t.value&&("sym"==n.type||"rc"==n.type||"num"==n.type&&n.value==n.value|0)&&("punc"!=i.type||"("!=i.value||n.space)){var r=l(e,!0),o=l(n,!1);if(r&&o)return y(3),R(new _(r,o),e,n)}}function v(e,t){if(("sym"==e.type||"rc"==e.type)&&("punc"!=t.type||"("!=t.value||e.space)){var n=l(e);if(n&&isFinite(n.row)&&isFinite(n.col))return y(1),R(n,e,e)}}function b(e,t){if("sym"==e.type&&"punc"==t.type&&"("==t.value&&!e.space)return e.type="func",y(1),e}var w,y,C,F,R;return e=d(f(e),i),w=e.ahead,y=e.skip,C=null,F=null!=i.row&&null!=i.col?function(e){return 1&e.rel&&(e.col-=i.col),2&e.rel&&(e.row-=i.row),e}:u,R=i.forEditor?function(e,t,n){return e.begin=t.begin,e.end=n.end,e}:u,{peek:o,next:s,croak:e.croak,eof:e.eof,is:r}}function h(e){return" \t\r\n ​".indexOf(e)>=0}function d(e,t){function n(e){return/[0-9]/i.test(e)}function i(e){return/[a-z$_]/i.test(e)||b.isUnicodeLetter(e)}function r(e){return i(e)||n(e)||"."==e}function o(e){return e in A}function s(e){return"\\!;(){}[]".indexOf(e)>=0}function a(){var e=!1,t=F(function(t){return t==D.DEC?!e&&(e=!0,!0):n(t)});return t==D.DEC?{type:"punc",value:D.DEC}:{type:"num",value:parseFloat(t.replace(D.DEC,"."))}}function l(t,n){return{type:"sym",value:t,upper:t.toUpperCase(),space:h(e.peek()),quote:n}}function u(e,t,n){if(!e&&!t&&!n)return null;if(!e&&!n||e&&n){var i=t?parseInt(t,10):0;return e?i:i-1}}function c(){var t,n,i=e.lookingAt(/^R(\[)?(-?[0-9]+)?(\])?C(\[)?(-?[0-9]+)?(\])?/i);return i&&(t=u(i[1],i[2],i[3]),n=u(i[4],i[5],i[6]),null!=t&&null!=n)?(e.skip(i),{type:"rc",row:t,col:n,rel:(i[4]||!(i[4]||i[5]||i[6])?1:0)|(i[1]||!(i[1]||i[2]||i[3])?2:0)}):l(F(r))}function d(){return e.next(),{type:"str",value:e.readEscaped('"')}}function f(){return e.next(),l(e.readEscaped("'"),!0)}function p(){return{type:"op",value:F(function(e,t){return t+e in A})}}function m(){return{type:"punc",value:e.next()}}function g(){if(e.eof())return null;var r,l=e.peek();return'"'==l?d():"'"==l?f():n(l)||l==D.DEC?a():i(l)?c():o(l)?p():s(l)?m():(r=e.lookingAt(/^#([a-z\/]+)[?!]?/i))?(e.skip(r),{type:"error",value:r[1]}):(t.forEditor||e.croak("Can't handle character with code: "+l.charCodeAt(0)),{type:"error",value:e.next()})}function v(){for(var n,i;x.length<=C;)F(h),n=e.pos(),i=g(),t.forEditor&&i&&(i.begin=n,i.end=e.pos()),x.push(i);return x[C]}function w(){var e=v();return e&&C++,e}function y(e,t){for(var n=C,i=[];e-- >0;)i.push(w()||L);return C=n,t.apply(i,i)}function _(e){C+=e}function k(){return null==v()}var x=[],C=0,F=e.readWhile;return{next:w,peek:v,eof:k,croak:e.croak,ahead:y,skip:_}}function f(e){function t(){return h}function n(){var t=e.charAt(h++);return"\n"==t?(d++,f=0):f++,t}function i(){return e.charAt(h)}function r(){return""===i()}function o(t){throw new E(t+" (input: "+e+")",h)}function s(t){if("string"==typeof t)e.substr(h,t.length)!=t&&o("Expected "+t),a(t.length);else if(t instanceof RegExp){var n=t.exec(e.substr(h));if(n)return a(n[0].length),n}else a(t[0].length)}function a(e){for(;e-- >0;)n()}function l(e){for(var t,i=!1,o="";!r();)if(t=n(),i)o+=t,i=!1;else if("\\"==t)i=!0;else{if(t==e)break;o+=t}return o}function u(e){for(var t="";!r()&&e(i(),t);)t+=n();return t}function c(t){return t.exec(e.substr(h))}var h=0,d=1,f=0;return{next:n,peek:i,eof:r,croak:o,readWhile:u,readEscaped:l,lookingAt:c,skip:s,forward:a,pos:t}}function p(e,t,n){function i(){var i=e.next();return"sym"==i.type?"TRUE"==i.upper?(i.type="bool",i.value=!0):"FALSE"==i.upper&&(i.type="bool",i.value=!1):"ref"==i.type&&(i={type:"ref",ref:null!=t&&null!=n?i.absolute(t,n):i,begin:i.begin,end:i.end}),i}var r,o=[];for(e=c(e,{forEditor:!0,row:t,col:n});!e.eof();)o.push(i());return r=o[0],"op"==r.type&&"="==r.value&&(r.type="startexp"),o}function m(e,t,n){var i,r;for(t=t||0,n=n||0,e=c(e,{row:t,col:n}),i=[];!e.eof();){if(r=e.next(),"ref"!=r.type)throw new E("Expecting a reference but got: "+JSON.stringify(r));i.push(r.absolute(t,n))}return i}function g(e,t){var n=e+t,i=P[n];return i||(i="^(\\d+(COM\\d{3})*(DOT\\d+)?)",i=i.replace(/DOT/g,"\\"+t).replace(/COM/g,"\\"+e),i=RegExp(i),P[n]=i),i}function v(e,t){for(var n="";t-- >0;)n+=e;return n}var b,w,y,_,k,x,C,F,R,S,A,D,E,B,M,T,I,L,N,z,P;kendo.support.browser.msie&&kendo.support.browser.version<9||(b=kendo.util,w=kendo.spreadsheet,y=w.Ref,_=w.RangeRef,k=w.CellRef,x=w.NameRef,C=w.calc,F=C.runtime,R=Object.create(null),S=Object.create(null),function(e){e.forEach(function(t,n){t.forEach(function(t){R[t]=e.length-n,S[","==t?";":t]=e.length-n})})}([[":"],[" "],[","],["%"],["^"],["*","/"],["+","-"],["&"],["=","<",">","<=",">=","<>"]]),A=R,D={DEC:".",ARG:",",COL:","},C.withDecimalSeparator=function(t,n){if(D.DEC==t)return n();var i=D.DEC;e(t);try{return n()}finally{e(i)}},C._separators=D,E=kendo.Class.extend({init:function(e,t){this.message=e,this.pos=t},toString:function(){return this.message}}),B={type:"bool",value:!0},M={type:"bool",value:!1},T=function(e){return function(t){var n=e[t];return n||(n=e[t]=Function("'use strict';return("+t+")")()),n}}(Object.create(null)),I=Object.create(null),L={type:"eof"},N=[],z=C.registerFormatParser=function(e){N.push(e)},C.parse=function(e,t,n,i,o){var s,a,l,u,c;if(i instanceof Date)return{type:"date",value:F.dateToSerial(i)};if("number"==typeof i)return{type:"number",value:i};if("boolean"==typeof i)return{type:"boolean",value:i};if(i+="",/^'/.test(i))return{type:"string",value:i.substr(1)};if(/^-?[0-9]+%$/.test(i)&&(s=i.substr(0,i.length-1),a=parseFloat(s),!isNaN(a)&&a==s))return{type:"percent",value:a/100};if(/^=/.test(i))return i=i.substr(1),/\S/.test(i)?r(e,t,n,i):{type:"string",value:"="+i};for(l=0;l<N.length;++l)if(u=N[l](i))return u;return"true"==i.toLowerCase()?{type:"boolean",value:!0}:"false"==i.toLowerCase()?{type:"boolean",value:!1}:(c=F.parseDate(i,o))?{type:"date",value:F.dateToSerial(c)}:(a=parseFloat(i),!isNaN(a)&&i.length>0&&a==i?(o=null,a!=Math.floor(a)&&(o="0."+(a+"").split(".")[1].replace(/\d/g,"0")),{type:"number",value:a,format:o}):{type:"string",value:i})},C.parseNameDefinition=o,C.parseFormula=r,C.parseReference=i,C.compile=l,C.parseSqref=m,C.InputStream=f,C.ParseError=E,C.tokenize=p,z(function(e){var t,n,i,r,o,s,a,l,u,c,h,d=0,f="";if(t=/^(\d+)([-\/.])(\d+)\2(\d{2}(?:\d{2})?)(\s*)/.exec(e)){if(n=parseInt(t[1],10),i=t[2],r=parseInt(t[3],10),o=parseInt(t[4],10),o<30?o+=2e3:o<100&&(o+=1900),s=!0,n>12&&(a=n,n=r,r=a,s=!1),!F.validDate(o,n,r))return null;d=F.packDate(o,n-1,r),d<0&&d--,f=s?["mm","dd","yyyy"].join(i):["dd","mm","yyyy"].join(i),f+=t[5],e=e.substr(t[0].length)}return(t=/^(\d+):(\d+)$/.exec(e))?(l=parseInt(t[1],10),u=parseInt(t[2],10),{type:"date",format:f+"hh:mm",value:d+F.packTime(l,u,0,0)}):(t=/^(\d+):(\d+)(\.\d+)$/.exec(e))?(u=parseInt(t[1],10),c=parseInt(t[2],10),h=1e3*parseFloat(t[3]),{type:"date",format:f+"mm:ss.00",value:d+F.packTime(0,u,c,h)}):(t=/^(\d+):(\d+):(\d+)$/.exec(e))?(l=parseInt(t[1],10),u=parseInt(t[2],10),c=parseInt(t[3],10),{type:"date",format:f+"hh:mm:ss",value:d+F.packTime(l,u,c,0)}):(t=/^(\d+):(\d+):(\d+)(\.\d+)$/.exec(e))?(l=parseInt(t[1],10),u=parseInt(t[2],10),c=parseInt(t[3],10),h=1e3*parseFloat(t[4]),{type:"date",format:f+"hh:mm:ss.00",value:d+F.packTime(l,u,c,h)}):void 0}),z(function(e){var t,n,i,r=kendo.culture(),o=r.numberFormat[","],s=r.numberFormat["."],a=r.numberFormat.currency.symbol,l=g(o,s),u=RegExp("^\\s*\\"+a+"\\s*"),c=1,h="",d="",p=!1,m=!1;if(e=f(e.replace(/^\s+|\s+$/g,"")),e.skip(/^-\s*/)&&(c=-1),(t=e.skip(u))&&(p=!0,h+='"'+t[0]+'"'),e.skip(/^-\s*/)){if(c<0)return null;c=-1}if(!(n=e.skip(l)))return null;if(h+="0",t=e.skip(u)){if(p)return null;p=!0,d='"'+t[0]+'"'}return!p&&(t=e.skip(/^\s*%\s*/))&&(m=!0,d=t[0]),e.eof()?((n[2]||p)&&(h=h.replace("0","#"),h+=",0"),n[3]&&(h+="."+v("0",n[3].length-1)),i=n[0].replace(RegExp("\\"+o,"g"),"").replace(RegExp("\\"+s,"g"),"."),i=parseFloat(i),m&&(i/=100),h+=d,p&&(h+=";-"+h),{type:"number",currency:p,format:h,value:c*i}):null}),z(function(e){var t;if(t=/^([0-9]*)\.([0-9]+)(\s*%)$/.exec(e))return{type:"number",value:parseFloat(e)/100,format:"0."+v("0",t[2].length)+t[3]}}),P={})},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/excel-reader.min",["kendo.core.min","kendo.color.min","util/parse-xml.min","spreadsheet/calc.min"],e)}(function(){"use strict";function e(e,t,i){var r=new FileReader;r.onload=function(e){var r=new JSZip(e.target.result);n(r,t,i)},r.readAsArrayBuffer(e)}function t(e){return/^\//.test(e)?e=e.substr(1):/^xl\//.test(e)||(e="xl/"+e),e}function n(e,t,n){var r,a,l,u,c,h,d;T=t.excelImportErrors=[],r=y(e),a=_(e,"_rels/workbook.xml"),l=x(e,a.byType.theme[0]),u=k(e,l),c=[],h=0,w(e,"xl/workbook.xml",{enter:function(n,i){var o,l,d,f;this.is(V)?(o=i["r:id"],l=a.byId[o],d=i.name,f=s(e,l),t.options.columnWidth=f.columnWidth||t.options.columnWidth,t.options.rowHeight=f.rowHeight||t.options.rowHeight,c.push({workbook:t,zip:e,strings:r,styles:u,file:l,options:{name:d,rows:Math.max(t.options.rows||0,f.rows),columns:Math.max(t.options.columns||0,f.cols),columnWidth:f.columnWidth,rowHeight:f.rowHeight}})):this.is(K)&&i.activeTab&&(h=C(i.activeTab))},text:function(e){var n,i,r,o=this.is(N);!o||F(o["function"])||F(o.vbProcedure)||(n=o.localSheetId,i=null,null!=n&&(i=c[n].options.name),r=o.name,"_xlnm._FilterDatabase"!=r&&(i&&(r="'"+i.replace(/\'/g,"\\'")+"'!"+r),v(i,null,function(){t.defineName(r,e,F(o.hidden))},"reading user-defined name: "+r)))}}),d=new D.Deferred,d.progress(function(e){n&&n.notify(e)}).then(function(){var e=t.sheets();o(e),t.activeSheet(e[h]),n&&n.resolve()}),i(c,t,d)}function i(e,t,n){var i,o=(new D.Deferred).resolve();for(i=0;i<e.length;i++)!function(i,s){o=o.then(function(){var o,a,l=t.insertSheet(i.options);return l.suspendChanges(!0),o=r(l,i),a={sheet:l,progress:s/(e.length-1)},o.then(function(){n.notify(a)}),o})}(e[i],i);o.then(function(){n.resolve()})}function r(e,t){var n=new D.Deferred;return setTimeout(function(){u(t.zip,t.file,e,t.strings,t.styles),n.resolve()},0),n}function o(e){for(var t=0;t<e.length;t++)e[t].suspendChanges(!1).triggerChange({recalc:!0})}function s(e,n){var i={rows:0,cols:0};return w(e,t(n),{enter:function(e,t){if("dimension"==e){var n=B(t.ref);n.bottomRight&&(i.cols=n.bottomRight.col+1,i.rows=n.bottomRight.row+1)}else"sheetFormatPr"===e?(t.defaultColWidth&&(i.columnWidth=a(parseFloat(t.defaultColWidth))),t.defaultRowHeight&&(i.rowHeight=l(parseFloat(t.defaultRowHeight)))):this.is(O)&&this.exit()}}),i}function a(e){var t=7,n=(256*e+Math.floor(128/t))/256;return n*t}function l(e){return e*(4/3)}function u(e,n,i,r,o){function s(){i.range(D).filter(ue),D=null}var u,c,h,y,k,x,R,A,D,E,N,V,W,j,K,ie,re={},oe=i._columns._count,se=null,ae=n.replace(/worksheets\//,"worksheets/_rels/"),le=_(e,ae),ue=[];T=i._workbook.excelImportErrors,n=t(n),w(e,n,{enter:function(e,t,n){var r,d,f,v,w,_,x,R,S,A,M;if(this.is(z))n&&"shared"==t.t&&null!=t.si&&(y=i.range(re[t.si])._get("formula"));else if(this.is(I))h=null,y=null,u=t.r,k=null,null==u&&(u=B(se),u.col++,u=""+u),se=u,c=t.t,d=t.s,null!=d&&b(i,u,o,d);else if(this.is(P))i.range(t.ref).merge();else if(this.is(L)){if(f=C(t.min)-1,v=Math.min(oe,C(t.max))-1,t.width&&(w=a(parseFloat(t.width)),0!==w&&i._columns.values.value(f,v,w)),"1"===t.hidden||0===w)for(_=f;_<=v;_++)i.hideColumn(_);null!=t.style&&b(i,new kendo.spreadsheet.RangeRef(new kendo.spreadsheet.CellRef((-(1/0)),f),new kendo.spreadsheet.CellRef((+(1/0)),v)),o,t.style)}else this.is(O)?(x=C(t.r)-1,t.ht&&(R=l(parseFloat(t.ht)),0!==R&&i._rows.values.value(x,x,R)),"1"!==t.hidden&&0!==R||i.hideRow(x)):this.is($)?t.activeCell&&(S=B(t.activeCell),i.select(S,!0)):this.is(H)?"frozen"==t.state&&(t.xSplit&&i.frozenColumns(C(t.xSplit)),t.ySplit&&i.frozenRows(C(t.ySplit))):this.is(J)?i.showGridLines(F(t.showGridLines,!0)):this.is(G)?(A=t["r:id"],M=le.byId[A],M&&i.range(t.ref).link(M)):this.is(["autoFilter"])?(D=t.ref,n&&s()):D&&(this.is(["filterColumn"])?E=parseInt(t.colId,10):this.is(["customFilters"])?(N=F(t.and)?"and":"or",V=[]):this.is(["customFilter"])?(r=p(t.operator,t.val),r&&V.push({operator:r.operator,value:r.value})):this.is(["dynamicFilter"])?ue.push({column:E,filter:new kendo.spreadsheet.DynamicFilter({type:m(t.type)})}):this.is(["top10"])?ue.push({column:E,filter:new kendo.spreadsheet.TopFilter({value:g(t.val),type:function(e,t){return e&&t?"topPercent":t?"topNumber":e?"bottomPercent":"bottomNumber"}(F(t.percent),F(t.top))})}):this.is(["filters"])?(W=F(t.blank),j=[]):this.is(["filter"])&&j.push(g(t.val)))},leave:function(e,t){var n,o;this.is(z)?y||"shared"!=t.t||null==t.si||(y=i.range(re[t.si])._get("formula")):this.is(I)?null!=y?(n=v(i,k||u,function(){i.range(k||u).formula(y,x)},"parsing formula"),n&&i.range(k||u).value(y).background("#ffaaaa")):null!=h&&(o=i.range(u),o._get("formula")||(c&&"n"!=c?"s"==c?h=r[C(h)]:"b"==c?h="1"===h:"d"==c&&(h=kendo.parseDate(h)):h=parseFloat(h),null!=h&&o.value(h))):this.is(Y)||this.is(Z)?!function(){var e=kendo.spreadsheet.calc.parseSqref(t.sqref),n=t.type.toLowerCase(),r=t.operator;/^(?:whole|decimal)$/.test(n)?n="number":"list"==n&&(r="list"),!r&&/^(?:number|date)$/.test(n)&&(r="between"),e.forEach(function(e){v(i,e,function(){i.range(e).validation({type:F(t.showErrorMessage,!0)?"reject":"warning",from:R,to:A,dataType:n,comparerType:M[r]||r,allowNulls:F(t.allowBlank),showButton:F(t.showDropDown)||"date"==n||"list"==n,messageTemplate:t.error,titleTemplate:t.errorTitle})},"parsing validation")})}():"cols"==e?i._columns._refresh():"sheetData"==e?i._rows._refresh():"autoFilter"==e?s():D&&("customFilters"==e?ue.push({column:E,filter:new kendo.spreadsheet.CustomFilter({logic:N,criteria:V})}):"filters"==e&&ue.push({column:E,filter:new kendo.spreadsheet.ValueFilter({values:j,blanks:W})}))},text:function(e){var t;this.is(q)||this.is(U)?h=e:(t=this.is(z))?(y=e,x="array"==t.t,x?k=t.ref:"shared"==t.t&&(re[t.si]=u)):this.is(X)||this.is(te)?R=e:this.is(Q)||this.is(ne)?A=e:this.is(ee)&&(this.stack[this.stack.length-2].sqref=e)}}),le.byType.comments&&(K=S(n,le.byType.comments[0]),f(e,K,i)),le.byType.drawing&&(ie=S(n,le.byType.drawing[0]),d(e,ie,i))}function c(e){var t=/\.([^.]+)$/.exec(e);if(t&&t[1])return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",gif:"image/gif"}[t[1].toLowerCase()]}function h(e){var t=/[^\/]+$/.exec(e);return t&&t[0]}function d(e,t,n){var i,r,o,s,a=["xdr:twoCellAnchor"],l=["xdr:ext"],u=["xdr:oneCellAnchor"],d=["xdr:from"],f=["xdr:to"],p=["xdr:row"],m=["xdr:col"],g=["xdr:rowOff"],v=["xdr:colOff"],b=["xdr:blipFill","a:blip"],y=t.replace(/drawings\//,"drawings/_rels/"),k=_(e,y);k.byType.image&&Object.keys(k.byId).forEach(function(i){var r,o,s,a=S(t,k.byId[i]),l=c(a);l&&(r=e.files[a].asArrayBuffer(),o=h(a),s=!o||kendo.support.browser.msie||kendo.support.browser.edge?new window.Blob([r],{type:l}):new window.File([r],o,{type:l}),k.byId[i]=n._workbook.addImage(s))}),w(e,t,{enter:function(e,t){if(this.is(a)||this.is(u))i={};else if(this.is(d)||this.is(f))r={};else if(this.is(b)){var n=t["r:embed"];i.image=k.byId[n]}else this.is(l)&&(o=A(parseFloat(t.cx)),s=A(parseFloat(t.cy)))},leave:function(){var e,t,l,c;this.is(d)?(i.topLeftCell=new kendo.spreadsheet.CellRef(r.row,r.col),i.offsetX=A(r.colOffset),
i.offsetY=A(r.rowOffset)):this.is(f)?(i.brCell=new kendo.spreadsheet.CellRef(r.row,r.col),i.brX=A(r.colOffset),i.brY=A(r.rowOffset)):this.is(a)?(e=n._columns.sum(0,i.topLeftCell.col-1)+i.offsetX,t=n._rows.sum(0,i.topLeftCell.row-1)+i.offsetY,l=n._columns.sum(0,i.brCell.col-1)+i.brX,c=n._rows.sum(0,i.brCell.row-1)+i.brY,n.addDrawing({topLeftCell:i.topLeftCell,offsetX:i.offsetX,offsetY:i.offsetY,width:null!=o?o:l-e,height:null!=s?s:c-t,image:i.image,opacity:1})):this.is(u)&&n.addDrawing({topLeftCell:i.topLeftCell,offsetX:i.offsetX,offsetY:i.offsetY,width:o,height:s,image:i.image,opacity:1})},text:function(e){this.is(p)?r.row=parseFloat(e):this.is(m)?r.col=parseFloat(e):this.is(g)?r.rowOffset=parseFloat(e):this.is(v)&&(r.colOffset=parseFloat(e))}})}function f(e,t,n){var i,r,o=[];w(e,t,{enter:function(e,t){this.is(ie)?r={author:o[t.authorId],ref:t.ref,text:""}:this.is(re)&&(i="")},leave:function(){this.is(ie)?n.range(r.ref).comment(r.text):this.is(re)&&o.push(i)},text:function(e){this.is(oe)?r.text+=e:this.is(re)&&(i+=e)}})}function p(e,t){var n={equal:"eq",notEqual:"ne",greaterThan:"gt",greaterThanOrEqual:"gte",lessThan:"lt",lessThanOrEqual:"lte"}[e];return t=g(t),n&&"number"==typeof t?{operator:n,value:t}:"notEqual"!=e&&e||"string"!=typeof t?void 0:{operator:e?"doesnotmatch":"matches",value:t}}function m(e){return{Q1:"quarter1",Q2:"quarter2",Q3:"quarter3",Q4:"quarter4",M1:"january",M2:"february",M3:"march",M4:"april",M5:"may",M6:"june",M7:"july",M8:"august",M9:"september",M10:"october",M11:"november",M12:"december"}[e.toUpperCase()]||e}function g(e){var t=parseFloat(e);return isNaN(t)||t!=e?e:t}function v(e,t,n,i){try{return n(),!1}catch(r){var o={context:i,error:r+""};return e&&(o.sheet=e.name()),t&&(o.location=t+""),T.push(o),!0}}function b(e,t,n,i){function r(e){var t="string"==typeof e?e:e.formatCode;null==t||/^general$/i.test(t)||(t=t.replace(/^\[\$-[0-9]+\]/,""),h.format(t))}function o(e){"solid"==e.type&&h.background(e.color)}function s(e){h.fontFamily(e.name),e.size&&h._property("fontSize",4*e.size/3),e.bold&&h.bold(!0),e.italic&&h.italic(!0),e.underline&&h.underline(!0),e.color&&h.color(e.color)}function a(e){function t(t,n){var i,r,o=e[t];o&&(i=se[o.style],0!==i&&(r=o.color,null==r&&(r="#000"),h._property(n,{size:i,color:r})))}t("left","borderLeft"),t("top","borderTop"),t("right","borderRight"),t("bottom","borderBottom")}function l(e,t){var n=d[e];if(null!=n&&!n)return!1;if(c=d[t],u&&null==c){if(n=u[e],null!=n&&!n)return!1;c=u[t]}return null!=c}var u,c,h=e.range(t),d=n.inlineStyles[i];d.xfId&&(u=n.namedStyles[d.xfId]),l("applyBorder","borderId")&&a(n.borders[c]),l("applyFont","fontId")&&s(n.fonts[c]),l("applyAlignment","textAlign")&&h.textAlign(c),l("applyAlignment","verticalAlign")&&h.verticalAlign(c),l("applyAlignment","indent")&&h.indent(c),l("applyAlignment","wrapText")&&h._property("wrap",c),l("applyFill","fillId")&&o(n.fills[c]),l("applyNumberFormat","numFmtId")&&r(n.numFmts[c]||ae[c])}function w(e,t,n){var i=e.files[t];i&&E(i.asUint8Array(),n)}function y(e){var t=[],n=null;return w(e,"xl/sharedStrings.xml",{leave:function(){this.is(j)&&(t.push(n),n=null)},text:function(e){this.is(W)&&(null==n&&(n=""),n+=e)}}),t}function _(e,n){var i={byId:{},byType:{theme:[]}};return w(e,t(n)+".rels",{enter:function(e,t){var n,r;"Relationship"==e&&(i.byId[t.Id]=t.Target,n=t.Type.match(/\w+$/)[0],r=i.byType[n]||[],r.push(t.Target),i.byType[n]=r)}}),i}function k(e,t){function n(e){function t(t){null!=e[t]&&(n[t]=F(e[t]))}var n={borderId:C(e.borderId),fillId:C(e.fillId),fontId:C(e.fontId),numFmtId:C(e.numFmtId),pivotButton:F(e.pivotButton),quotePrefix:F(e.quotePrefix),xfId:C(e.xfId)};return t("applyAlignment"),t("applyBorder"),t("applyFill"),t("applyFont"),t("applyNumberFormat"),t("applyProtection"),n}function i(e){var n,i,r;return e.rgb?R(e.rgb):e.indexed?pe[C(e.indexed)]:e.theme?(n=t.colorScheme[C(e.theme)])?(i=kendo.parseColor(n),e.tint&&(i=i.toHSL(),r=parseFloat(e.tint),i.l=r<0?i.l*(1+r):i.l*(1-r)+(100-100*(1-r))),i.toCssRgba()):pe[0]:void 0}var r={fonts:[],numFmts:{},fills:[],borders:[],namedStyles:[],inlineStyles:[]},o=null,s=null,a=null,l=null;return w(e,"xl/styles.xml",{enter:function(e,t,u){if(this.is(fe))r.numFmts[t.numFmtId]=t;else if(this.is(ce))r.fonts.push(o={});else if(o)"sz"==e?o.size=parseFloat(t.val):"name"==e?o.name=t.val:"b"==e?o.bold=F(t.val,!0):"i"==e?o.italic=F(t.val,!0):"u"==e?o.underline=null==t.val||"single"==t.val:"color"==e&&(o.color=i(t));else if(this.is(ue))r.fills.push(s={});else if(s)"patternFill"==e?s.type=t.patternType:"fgColor"==e&&"solid"===s.type?s.color=i(t):"bgColor"==e&&"solid"!==s.type&&(s.color=i(t));else if(this.is(le))r.borders.push(a={});else if(a){if(/^(?:left|top|right|bottom)$/.test(e)&&(a[e]={style:t.style||"none"}),"color"==e){var c=this.stack[this.stack.length-2].$tag;a[c].color=i(t)}}else this.is(de)?(l=n(t),r.namedStyles.push(l),u&&(l=null)):this.is(he)?(l=n(t),r.inlineStyles.push(l),u&&(l=null)):l&&"alignment"==e&&(/^(?:left|center|right|justify)$/.test(t.horizontal)&&(l.textAlign=t.horizontal),/^(?:top|center|bottom)$/.test(t.vertical)&&(l.verticalAlign=t.vertical),null!=t.wrapText&&(l.wrapText=F(t.wrapText)),null!=t.indent&&(l.indent=C(t.indent)))},leave:function(e){this.is(ce)?o=null:this.is(ue)?s=null:this.is(le)?a=null:"xf"==e&&(l=null)}}),r}function x(e,n){function i(e,t,n){var i=e[t];e[t]=e[n],e[n]=i}var r=[],o={colorScheme:r},s=t(n);return e.files[s]&&(w(e,s,{enter:function(e,t){this.is(ge)?r.push(R("window"==t.val?"FFFFFFFF":"FF000000")):this.is(me)&&r.push(R("FF"+t.val))}}),r.length>3&&(i(r,0,1),i(r,2,3))),o}function C(e){return null==e?null:parseInt(e,10)}function F(e,t){return null==e?t:"true"==e||e===!0||1==e}function R(e){var t=/^([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})$/i.exec(e);return"rgba("+parseInt(t[2],16)+", "+parseInt(t[3],16)+", "+parseInt(t[4],16)+", "+parseInt(t[1],16)/255+")"}function S(e,t){for(e=e.split(/\/+/),t=t.split(/\/+/),e.pop();t.length;){var n=t.shift();if(""===n)e=[];else{if("."===n)continue;".."===n?e.pop():e.push(n)}}return e.join("/")}function A(e){return e/9525}var D,E,B,M,T,I,L,N,z,P,H,O,$,V,U,W,j,q,K,J,G,Y,X,Q,Z,ee,te,ne,ie,re,oe,se,ae,le,ue,ce,he,de,fe,pe,me,ge;kendo.support.browser.msie&&kendo.support.browser.version<9||(D=kendo.jQuery,E=kendo.util.parseXML,B=kendo.spreadsheet.calc.parseReference,M={greaterThanOrEqual:"greaterThanOrEqualTo",lessThanOrEqual:"lessThanOrEqualTo"},T=null,I=["sheetData","row","c"],L=["cols","col"],N=["definedNames","definedName"],z=["sheetData","row","c","f"],P=["mergeCells","mergeCell"],H=["sheetViews","sheetView","pane"],O=["sheetData","row"],$=["sheetViews","sheetView","selection"],V=["sheets","sheet"],U=["sheetData","row","c","is"],W=["t"],j=["si"],q=["sheetData","row","c","v"],K=["bookViews","workbookView"],J=["sheetViews","sheetView"],G=["hyperlinks","hyperlink"],Y=["dataValidations","dataValidation"],X=["dataValidations","dataValidation","formula1"],Q=["dataValidations","dataValidation","formula2"],Z=["x14:dataValidations","x14:dataValidation"],ee=["x14:dataValidations","x14:dataValidation","xm:sqref"],te=["x14:dataValidations","x14:dataValidation","x14:formula1","xm:f"],ne=["x14:dataValidations","x14:dataValidation","x14:formula2","xm:f"],ie=["commentList","comment"],re=["authors","author"],oe=["t"],se={none:0,thin:1,medium:2,dashed:1,dotted:1,thick:3,"double":3,hair:1,mediumDashed:2,dashDot:1,mediumDashDot:2,dashDotDot:1,mediumDashDotDot:2,slantDashDot:1},ae={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"mm-dd-yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@"},le=["borders","border"],ue=["fills","fill"],ce=["fonts","font"],he=["cellXfs","xf"],de=["cellStyleXfs","xf"],fe=["numFmts","numFmt"],pe=[R("FF000000"),R("FFFFFFFF"),R("FFFF0000"),R("FF00FF00"),R("FF0000FF"),R("FFFFFF00"),R("FFFF00FF"),R("FF00FFFF"),R("FF000000"),R("FFFFFFFF"),R("FFFF0000"),R("FF00FF00"),R("FF0000FF"),R("FFFFFF00"),R("FFFF00FF"),R("FF00FFFF"),R("FF800000"),R("FF008000"),R("FF000080"),R("FF808000"),R("FF800080"),R("FF008080"),R("FFC0C0C0"),R("FF808080"),R("FF9999FF"),R("FF993366"),R("FFFFFFCC"),R("FFCCFFFF"),R("FF660066"),R("FFFF8080"),R("FF0066CC"),R("FFCCCCFF"),R("FF000080"),R("FFFF00FF"),R("FFFFFF00"),R("FF00FFFF"),R("FF800080"),R("FF800000"),R("FF008080"),R("FF0000FF"),R("FF00CCFF"),R("FFCCFFFF"),R("FFCCFFCC"),R("FFFFFF99"),R("FF99CCFF"),R("FFFF99CC"),R("FFCC99FF"),R("FFFFCC99"),R("FF3366FF"),R("FF33CCCC"),R("FF99CC00"),R("FFFFCC00"),R("FFFF9900"),R("FFFF6600"),R("FF666699"),R("FF969696"),R("FF003366"),R("FF339966"),R("FF003300"),R("FF333300"),R("FF993300"),R("FF993366"),R("FF333399"),R("FF333333"),R("FF000000"),R("FFFFFFFF")],me=["a:clrScheme","*","a:srgbClr"],ge=["a:clrScheme","*","a:sysClr"],kendo.spreadsheet.readExcel=e,kendo.spreadsheet._readSheet=u,kendo.spreadsheet._readStrings=y,kendo.spreadsheet._readStyles=k,kendo.spreadsheet._readTheme=x,kendo.spreadsheet._readWorkbook=n)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/workbook.min",["kendo.core.min","spreadsheet/runtime.min","spreadsheet/references.min","spreadsheet/excel-reader.min"],e)}(function(){!function(e){function t(e,t){var n=new XMLHttpRequest;n.onload=function(){t(n.response,n.getResponseHeader("Content-Type"))},n.onerror=function(){t(null)},n.open("GET",e),n.responseType="arraybuffer",n.send()}function n(e,t){function n(e,t,n){return void 0!==e?e:void 0!==t?t:n}var i,r,o,s,a,l,u=n(e.rowCount,t.rows,200),c=n(e.columnCount,t.columns,50),h=n(e.rowHeight,t.rowHeight,20),d=n(e.columnWidth,t.columnWidth,64),f=n(e.headerHeight,t.headerHeight,20),p=n(e.headerWidth,t.headerWidth,32);if(void 0!==e.rows)for(i=0;i<e.rows.length;++i)if(r=e.rows[i],o=n(r.index,i),o>=u&&(u=o+1),r.cells)for(s=0;s<r.cells.length;++s)a=r.cells[s],l=n(a.index,s),l>=c&&(c=l+1);return{rowCount:u,columnCount:c,rowHeight:h,columnWidth:d,headerHeight:f,headerWidth:p}}var i,r,o,s,a;e.support.browser.msie&&e.support.browser.version<9||(i=e.jQuery,r=e.spreadsheet.calc.runtime.Formula,o=e.spreadsheet.Ref,s=e.spreadsheet.CalcError,e.spreadsheet.messages.workbook={defaultSheetName:"Sheet"},a=e.Observable.extend({options:{},init:function(t,n){e.Observable.fn.init.call(this),this.options=t,this._view=n,this._sheets=[],this._images={},this._imgID=0,this._sheetsSearchCache={},this._sheet=this.insertSheet({rows:this.options.rows,columns:this.options.columns,rowHeight:this.options.rowHeight,columnWidth:this.options.columnWidth,headerHeight:this.options.headerHeight,headerWidth:this.options.headerWidth,dataSource:this.options.dataSource}),this.undoRedoStack=new e.util.UndoRedoStack,this.undoRedoStack.bind(["undo","redo"],this._onUndoRedo.bind(this)),this._context=new e.spreadsheet.FormulaContext(this),this._validationContext=new e.spreadsheet.ValidationFormulaContext(this),this._names=Object.create(null),this.fromJSON(this.options)},clipboard:function(){return this._clipboard||(this._clipboard=new e.spreadsheet.Clipboard(this)),this._clipboard},destroy:function(){this.unbind(),this._clipboard&&this._clipboard.destroy()},events:["cut","copy","paste","changing","change","excelImport","excelExport","insertSheet","removeSheet","selectSheet","renameSheet","insertRow","insertColumn","deleteRow","deleteColumn","hideRow","hideColumn","unhideRow","unhideColumn","select","changeFormat","dataBinding","dataBound"],_sheetChanging:function(e){this.trigger("changing",e)&&e.preventDefault()},_sheetChange:function(e){this.trigger("change",e)},_sheetInsertRow:function(e){this.trigger("insertRow",{sheet:e.sender,index:e.index})&&e.preventDefault()},_sheetInsertColumn:function(e){this.trigger("insertColumn",{sheet:e.sender,index:e.index})&&e.preventDefault()},_sheetDeleteRow:function(e){this.trigger("deleteRow",{sheet:e.sender,index:e.index})&&e.preventDefault()},_sheetDeleteColumn:function(e){this.trigger("deleteColumn",{sheet:e.sender,index:e.index})&&e.preventDefault()},_sheetHideRow:function(e){this.trigger("hideRow",{sheet:e.sender,index:e.index})&&e.preventDefault()},_sheetHideColumn:function(e){this.trigger("hideColumn",{sheet:e.sender,index:e.index})&&e.preventDefault()},_sheetUnhideRow:function(e){this.trigger("unhideRow",{sheet:e.sender,index:e.index})&&e.preventDefault()},_sheetUnhideColumn:function(e){this.trigger("unhideColumn",{sheet:e.sender,index:e.index})&&e.preventDefault()},_sheetSelect:function(e){this.trigger("select",e)},_sheetDataBinding:function(e){this.trigger("dataBinding",{sheet:e.sender})&&e.preventDefault()},_sheetDataBound:function(e){this.trigger("dataBound",{sheet:e.sender})},_sheetCommandRequest:function(e){this.trigger("commandRequest",e)},_inputForRef:function(t){var n=this;return n._sheet.withCultureDecimals(function(){return new e.spreadsheet.Range(t,n._sheet).input()})},_onUndoRedo:function(e){e.command.range().select()},execute:function(t){var n,r=i.extend({workbook:this},t.options),o=new e.spreadsheet[t.command](r),s=this.activeSheet();return r.origin&&o.origin(r.origin),o.range(r.operatingRange?r.operatingRange:s.selection()),n=o.exec(),n&&"error"===n.reason||(o.cannotUndo?this.undoRedoStack.clear():this.undoRedoStack.push(o)),this.cleanupImages(),n},resetFormulas:function(){this._sheets.forEach(function(e){e.resetFormulas()})},resetValidations:function(){this._sheets.forEach(function(e){e.resetValidations()})},refresh:function(e){e.recalc&&(this.resetFormulas(),this.resetValidations(),this._sheet.recalc(this._context),this._sheet.revalidate(this._validationContext))},activeSheet:function(t){return void 0===t?this._sheet:void(this.sheetByName(t.name())&&(this._sheet=t,t.triggerChange(e.spreadsheet.ALL_REASONS)))},moveSheetToIndex:function(e,t){var n=this.sheetIndex(e),i=this._sheets;n!==-1&&(this._sheetsSearchCache={},i.splice(t,0,i.splice(n,1)[0]),this.trigger("change",{sheetSelection:!0}))},insertSheet:function(t){var n,i,r,o,s,a;if(t=t||{},n=this,i="number"==typeof t.index?t.index:n._sheets.length,o=n._sheets,s=function(t){t=t?t:1;var i=e.spreadsheet.messages.workbook.defaultSheetName+t;return n.sheetByName(i)?s(t+1):i},!t.name||!n.sheetByName(t.name))return this._sheetsSearchCache={},r=t.name||s(),a=new e.spreadsheet.Sheet(t.rows||this.options.rows,t.columns||this.options.columns,t.rowHeight||this.options.rowHeight,t.columnWidth||this.options.columnWidth,t.headerHeight||this.options.headerHeight,t.headerWidth||this.options.headerWidth,t.defaultCellStyle||this.options.defaultCellStyle),a._workbook=this,a._name(r),this._bindSheetEvents(a),o.splice(i,0,a),t.data&&a.fromJSON(t.data),t.dataSource&&a.setDataSource(t.dataSource),this.trigger("change",{sheetSelection:!0}),a},_bindSheetEvents:function(e){e.bind("changing",this._sheetChanging.bind(this)),e.bind("change",this._sheetChange.bind(this)),e.bind("insertRow",this._sheetInsertRow.bind(this)),e.bind("insertColumn",this._sheetInsertColumn.bind(this)),e.bind("deleteRow",this._sheetDeleteRow.bind(this)),e.bind("deleteColumn",this._sheetDeleteColumn.bind(this)),e.bind("hideRow",this._sheetHideRow.bind(this)),e.bind("hideColumn",this._sheetHideColumn.bind(this)),e.bind("unhideRow",this._sheetUnhideRow.bind(this)),e.bind("unhideColumn",this._sheetUnhideColumn.bind(this)),e.bind("select",this._sheetSelect.bind(this)),e.bind("commandRequest",this._sheetCommandRequest.bind(this)),e.bind("dataBinding",this._sheetDataBinding.bind(this)),e.bind("dataBound",this._sheetDataBound.bind(this))},sheets:function(){return this._sheets.slice()},sheetByName:function(e){return this._sheets[this.sheetIndex(e)]},sheetByIndex:function(e){return this._sheets[e]},sheetIndex:function(e){var t,n=this._sheets,i=("string"==typeof e?e:e.name()).toLowerCase(),r=this._sheetsSearchCache[i];if(r>=0)return r;for(r=0;r<n.length;r++)if(t=n[r].name().toLowerCase(),this._sheetsSearchCache[t]=r,t===i)return r;return-1},renameSheet:function(e,t){var n=e.name().toLowerCase();if(t&&n!==t.toLowerCase()&&!this.sheetByName(t)&&(e=this.sheetByName(n),e&&(this._sheetsSearchCache={},!this.trigger("renameSheet",{sheet:e,newSheetName:t}))))return this._sheets.forEach(function(e){e._forFormulas(function(e){e.renameSheet(n,t)})}),this.forEachName(function(e,i){e.nameref.renameSheet(n,t)&&(this.undefineName(i),e.name=e.nameref.print(),this.nameDefinition(e.name,e)),(e.value instanceof o||e.value instanceof r)&&e.value.renameSheet(n,t)}.bind(this)),e._name(t),this.trigger("change",{sheetSelection:!0}),e},removeSheet:function(e){var t,n=this,i=n._sheets,r=e.name(),o=n.sheetIndex(e);1!==i.length&&(this.trigger("removeSheet",{sheet:e})||(this._sheetsSearchCache={},o>-1&&(e.unbind(),i.splice(o,1),n.activeSheet().name()===r?(t=i[o===i.length?o-1:o],n.activeSheet(t)):this.trigger("change",{recalc:!0,sheetSelection:!0}))))},_clearSheets:function(){for(var e=0;e<this._sheets.length;e++)this._sheets[e]._activeDrawing=[],this._sheets[e]._drawings=[],this._sheets[e].unbind();this._sheets=[],this._sheetsSearchCache={},this._names={},this._images={},this._imgID=0},fromJSON:function(e){var t,i,r,o;if(e.sheets)for(this._clearSheets(),e.images&&(this._imgID=0,this._images={},Object.keys(e.images).forEach(function(t){if(!isNaN(t)){var n=parseFloat(t);isFinite(n)&&(this._imgID=Math.max(this._imgID,n))}this._images[t]={url:e.images[t]}},this)),t=0;t<e.sheets.length;t++)i=e.sheets[t],r=n(i,this.options),o=this.insertSheet({rows:r.rowCount,columns:r.columnCount,rowHeight:r.rowHeight,columnWidth:r.columnWidth,headerHeight:r.headerHeight,headerWidth:r.headerWidth,data:i}),i.dataSource&&o.setDataSource(i.dataSource);this.activeSheet(e.activeSheet?this.sheetByName(e.activeSheet):this._sheets[0]),e.names&&e.names.forEach(function(e){this.defineName(e.name,e.value,e.hidden)},this)},toJSON:function(){this.resetFormulas(),this.resetValidations();var e=Object.keys(this._names).map(function(e){var t=this._names[e],n=t.value;return n instanceof o||n instanceof r?n=n.print(0,0,!0):n instanceof s?n+="":n=JSON.stringify(n),{value:n,hidden:t.hidden,name:t.name,sheet:t.nameref.sheet,localName:t.nameref.name}},this);return{activeSheet:this.activeSheet().name(),sheets:this._sheets.map(function(e){return e.recalc(this._context),e.revalidate(this._validationContext),e.toJSON()},this),names:e,columnWidth:this.options.columnWidth,rowHeight:this.options.rowHeight}},saveJSON:function(){function e(){--s<=0&&n.resolve(r)}var t=this,n=new i.Deferred,r=t.toJSON(),o=Object.keys(t._images).filter(function(e){return 1===t.usesImage(e)}),s=o.length;return r.images={},s?o.forEach(function(n){var i,o=t._images[n];o.blob?(i=new FileReader,i.onload=function(){r.images[n]=i.result,e()},i.readAsDataURL(o.blob)):(r.images[n]=o.url,e())}):e(),n.promise()},fromFile:function(t){var n=new i.Deferred,r=n.promise(),o={file:t,promise:r};return t&&!this.trigger("excelImport",o)?(this._clearSheets(),e.spreadsheet.readExcel(t,this,n)):n.reject(),r},saveAsExcel:function(n){function r(){if(--a<=0){o.images=l;var t=new e.ooxml.Workbook(o);e.saveAs({dataURI:n.forceProxy?t.toDataURL():t.toBlob(),fileName:o.fileName||n.fileName,proxyURL:n.proxyURL,forceProxy:n.forceProxy})}}var o,s,a,l,u=this;n=i.extend({},u.options.excel,n),o=u.toJSON(),u.trigger("excelExport",{workbook:o})||(s=Object.keys(u._images).filter(function(e){return 1===u.usesImage(e)}),a=s.length,l=a?{}:null,a?s.forEach(function(e){var n,i=u._images[e];i.blob?(n=new FileReader,n.onload=function(){l[e]={type:i.blob.type,name:i.blob.name,data:n.result},r()},n.readAsArrayBuffer(i.blob)):t(i.url,function(t,n){l[e]={type:n,data:t},r()})}):r())},draw:function(t,n){"function"!=typeof t||n||(n=t,t={});var i=[],r=this._sheets;!function o(s){if(s<r.length)r[s].draw(e.spreadsheet.SHEETREF,t,function(e){i.push(e),o(s+1)});else{var a=i[0];for(s=1;s<i.length;++s)a.children=a.children.concat(i[s].children);n(a)}}(0)},nameForRef:function(e,t){var n,i,r,s;void 0===t&&(t=e.sheet),t=t.toLowerCase(),n=e+"";for(i in this._names)if(r=this._names[i],s=r.value,s instanceof o&&(!s.sheet||s.sheet&&t==s.sheet.toLowerCase())&&s+""==n)return r;return{name:n}},defineName:function(t,n,i){var r=e.spreadsheet.calc.parseNameDefinition(t,n);t=r.name.print(),this._names[t.toLowerCase()]={value:r.value,hidden:i,name:t,nameref:r.name}},undefineName:function(e){delete this._names[e.toLowerCase()]},nameValue:function(e){return e=e.toLowerCase(),e in this._names?this._names[e].value:null},nameDefinition:function(e,t){return e=e.toLowerCase(),arguments.length>1&&(void 0===t?delete this._names[e]:this._names[e]=t),this._names[e]},forEachName:function(e){Object.keys(this._names).forEach(function(t){e(this._names[t],t)},this)},adjustNames:function(e,t,n,i){e=e.toLowerCase(),Object.keys(this._names).forEach(function(s){var a=this._names[s],l=a.value;l instanceof o&&l.sheet.toLowerCase()==e?a.value=l.adjust(null,null,null,null,t,n,i):l instanceof r&&l.adjust(e,t?"row":"col",n,i)},this)},addImage:function(e){var t=++this._imgID+"";return this._images[t]="string"==typeof e?{url:e}:{blob:e},t},imageUrl:function(e){var t=this._images[e],n=t.url;return n||(n=t.url=window.URL.createObjectURL(t.blob)),n},cleanupImages:function(){Object.keys(this._images).forEach(function(e){if(!this.usesImage(e)){var t=this._images[e].url;t&&window.URL.revokeObjectURL(t),delete this._images[e]}},this)},usesImage:function(e){var t,n,i=this._sheets;for(t=i.length;--t>=0;)if(i[t].usesImage(e))return 1;for(n=this.undoRedoStack.stack,t=n.length;--t>=0;)if(n[t].usesImage(e))return 2;return!1}}),e.spreadsheet.Workbook=a,e.PDFMixin&&(e.PDFMixin.extend(a.prototype),a.prototype.saveAsPDF=function(t){var n=new i.Deferred,r=n.promise(),o={promise:r};if(!this.trigger("pdfExport",o))return this._drawPDF(t,n).then(function(n){return t.forceProxy?e.pdf.exportPDF(n):e.pdf.exportPDFToBlob(n)}).done(function(i){e.saveAs({dataURI:i,fileName:t.fileName,proxyURL:t.proxyURL,forceProxy:t.forceProxy,proxyTarget:t.proxyTarget}),n.resolve()}).fail(function(e){n.reject(e)}),r},a.prototype._drawPDF=function(e){var t=new i.Deferred,n=function(e){t.resolve(e)};switch(e.area){case"workbook":e.workbook.draw(e,n);break;case"sheet":e.workbook.activeSheet().draw(e,n);break;case"selection":e.workbook.activeSheet().selection().draw(e,n)}return t.promise()}))}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/formulacontext.min",["kendo.core.min"],e)}(function(){var e,t,n,i,r,o,s,a;kendo.support.browser.msie&&kendo.support.browser.version<9||(e=kendo.spreadsheet,t=e.CellRef,n=e.RangeRef,i=e.UnionRef,r=e.NameRef,o=e.Ref,s=kendo.Class.extend({init:function(e){this.workbook=e},getRefCells:function(e,s,a,l,u,c){var h,d,f,p,m,g,v,b,w,y,_,k,x,C,F,R,S;if(e instanceof t)return h=this.workbook.sheetByName(e.sheet),h&&e.valid()?(d=h.formula(e),f=h.range(e.row,e.col).value(),c||null!=d||null!=f?[{formula:d,value:f,row:e.row,col:e.col,sheet:e.sheet,hidden:!!s&&(0===h.columnWidth(e.col)||0===h.rowHeight(e.row))}]:[]):[{value:new kendo.spreadsheet.calc.runtime.CalcError("REF")}];if(e instanceof n){if(p=this.workbook.sheetIndex(e.sheet),m=[],g=p,e.endSheet&&(g=this.workbook.sheetIndex(e.endSheet),p>g&&(v=p,p=g,g=v)),p<0||g<0||!e.valid())return[{value:new kendo.spreadsheet.calc.runtime.CalcError("REF")}];for(;p<=g;)for(h=this.workbook.sheetByIndex(p++),b=h._grid.normalize(e.topLeft),w=h._grid.normalize(e.bottomRight),y=h._grid.cellRefIndex(b),_=h._grid.cellRefIndex(w),k=h._properties.iterator("value",y,_),x=b.col;x<=w.col;++x)for(C=b.row;C<=w.row;++C)F=h._grid.index(C,x),d=h._properties.get("formula",F),f=k.at(F),(c||null!=d||null!=f)&&m.push({formula:d,value:f,row:C,col:x,sheet:h.name(),hidden:!!s&&(0===h.columnWidth(x)||0===h.rowHeight(C))});return m}if(e instanceof i){for(R=[],p=0;p<e.refs.length;++p)R=R.concat(this.getRefCells(e.refs[p],s,a,l,u));return R}return e instanceof r?(S=this.nameValue(e,a,l,u),S instanceof o?this.getRefCells(S,s,a,l,u):[{value:null==S?new kendo.spreadsheet.calc.runtime.CalcError("NAME"):S}]):[]},nameValue:function(e,t,n,i){var r;return e.hasSheet()?r=this.workbook.nameValue(e.print()):(e=e.clone().setSheet(t,!0),r=this.workbook.nameValue(e.print()),null==r&&(r=this.workbook.nameValue(e.name))),r instanceof o&&(r=r.absolute(n,i)),r},getData:function(e,n,i,o,s){var a,l=e instanceof t;return e instanceof r&&(l=this.workbook.nameValue(e.name)instanceof t),a=this.getRefCells(e,!1,n,i,o,s).map(function(e){var t=e.value;return t instanceof kendo.spreadsheet.calc.runtime.Formula&&(t=t.value),t}),l?a[0]:a},onFormula:function(e){var t,n,i,r,s,a=this.workbook.sheetByName(e.sheet),l=e.row,u=e.col,c=e.value,h=a.formula({row:l,col:u});return h===e&&(t=e.arrayFormulaRange,t?(r=c.width,s=c.height,a.forEach(t,function(e,t){var o,l,u;void 0===n&&(n=e,i=t),o=e-n,l=t-i,u=o<s&&l<r?c.get(o,l):new kendo.spreadsheet.calc.runtime.CalcError("N/A"),a._value(e,t,u)})):(c instanceof o&&(c=this.getData(c,e.sheet,l,u),Array.isArray(c)&&(c=c[0]),void 0===c&&(c=null)),c instanceof kendo.spreadsheet.calc.runtime.Matrix&&(c=c.get(0,0)),a._value(l,u,c)),clearTimeout(a._formulaContextRefresh),a._formulaContextRefresh=setTimeout(function(){a.batch(function(){},{layout:!0})},50),!0)}}),a=s.extend({onFormula:function(){return!0}}),e.FormulaContext=s,e.ValidationFormulaContext=a)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/controller.min",["kendo.core.min"],e)}(function(){!function(e){"use strict";function t(e){return e.map(function(e){return'[data-action="'+e+'"]'}).join(",")}var n,i,r,o,s,a,l,u,c,h,d,f,p,m,g,v,b;if(!(e.support.browser.msie&&e.support.browser.version<9)){n=e.jQuery,i=/:alphanum$/,r={up:"up",down:"down",left:"left",right:"right",home:"first-col","ctrl+left":"first-col",end:"last-col","ctrl+right":"last-col","ctrl+up":"first-row","ctrl+down":"last-row","ctrl+home":"first","ctrl+end":"last",pageup:"prev-page",pagedown:"next-page"},o={tab:"next","shift+tab":"previous",enter:"lower","shift+enter":"upper","delete":"clearContents",backspace:"clearContents","shift+:alphanum":"edit","alt+:alphanum":"edit",":alphanum":"edit","ctrl+:alphanum":"ctrl","alt+ctrl+:alphanum":"edit",":edit":"edit"},s={wheel:"onWheel","*+mousedown":"onMouseDown",contextmenu:"onContextMenu","*+mousedrag":"onMouseDrag","*+mouseup":"onMouseUp","*+dblclick":"onDblClick",mousemove:"onMouseMove",touchmove:"onTouchMove",touchend:"onTouchEnd"},a={pageup:"onPageUp",pagedown:"onPageDown",mouseup:"onMouseUp","*+cut":"onCut","*+paste":"onPaste","*+copy":"onCopy"},l={esc:"onEditorEsc",enter:"onEditorBlur","alt+enter":"insertNewline","shift+enter":"onEditorBlur",tab:"onEditorBlur","shift+tab":"onEditorBlur","shift+ctrl+enter":"onEditorArrayFormula"},u=n.extend({focus:"onEditorBarFocus"},l),c=n.extend({focus:"onEditorCellFocus"},l),h={cell:"range",rowheader:"row",columnheader:"column",topcorner:"sheet",autofill:"autofill"},d=t(["cut","copy","paste","insert-left","insert-right","insert-above","insert-below"]),f=t(["unhide-row","unhide-column"]),p=[],m=[],g=[];for(v in r)p.push(v),m.push("shift+"+v);for(v in o)g.push(v);a[p]="onAction",a[m]="onShiftAction",a[g]="onEntryAction",c[p]="onEditorAction",c[m]="onEditorShiftAction",b=e.Class.extend({init:function(t,i){this.view=t,this.workbook(i),this.container=n(t.container),this.clipboardElement=n(t.clipboard),this.cellContextMenu=t.cellContextMenu,this.rowHeaderContextMenu=t.rowHeaderContextMenu,this.colHeaderContextMenu=t.colHeaderContextMenu,this.drawingContextMenu=t.drawingContextMenu,this.scroller=t.scroller,this.tabstrip=t.tabstrip,this.sheetsbar=t.sheetsbar,t.nameEditor.bind("enter",this.onNameEditorEnter.bind(this)),t.nameEditor.bind("cancel",this.onNameEditorCancel.bind(this)),t.nameEditor.bind("select",this.onNameEditorSelect.bind(this)),t.nameEditor.bind("delete",this.onNameEditorDelete.bind(this)),this.editor=t.editor,this.editor.bind("change",this.onEditorChange.bind(this)),this.editor.bind("activate",this.onEditorActivate.bind(this)),this.editor.bind("deactivate",this.onEditorDeactivate.bind(this)),this.editor.bind("update",this.onEditorUpdate.bind(this)),n(t.scroller).on("scroll",this.onScroll.bind(this)),this.listener=new e.spreadsheet.EventListener(this.container,this,s),this._enableEditorEvents(),this.sheetsbar&&(this.sheetsbar.bind("select",this.onSheetBarSelect.bind(this)),this.sheetsbar.bind("reorder",this.onSheetBarReorder.bind(this)),this.sheetsbar.bind("rename",this.onSheetBarRename.bind(this)),this.sheetsbar.bind("remove",this.onSheetBarRemove.bind(this))),this.cellContextMenu.bind("select",this.onContextMenuSelect.bind(this)),this.rowHeaderContextMenu.bind("select",this.onContextMenuSelect.bind(this)),this.colHeaderContextMenu.bind("select",this.onContextMenuSelect.bind(this)),this.drawingContextMenu.bind("select",this.onContextMenuSelect.bind(this)),this.cellContextMenu.element.add(this.rowHeaderContextMenu.element).add(this.colHeaderContextMenu.element).add(this.drawingContextMenu.element).on("contextmenu",!1),this.tabstrip&&(this.tabstrip.bind("action",this.onCommandRequest.bind(this)),this.tabstrip.bind("dialog",this.onDialogRequest.bind(this)))},_enableEditorEvents:function(t){void 0===t||t?(this.keyListener=new e.spreadsheet.EventListener(this.clipboardElement,this,a),this.barKeyListener=new e.spreadsheet.EventListener(this.editor.barElement(),this,u),this.inputKeyListener=new e.spreadsheet.EventListener(this.editor.cellElement(),this,c)):(this.keyListener.destroy(),this.barKeyListener.destroy(),this.inputKeyListener.destroy())},_execute:function(e){var t=this._workbook.execute(e);return"EditCommand"!==e.command||t||this._workbook.trigger("change",{editorClose:!0}),t&&(this._preventNavigation=!0,"error"===t.reason?(this.editor.deactivate(!0),this.view.showError(t,function(){this.activateEditor(!1),this.editor.value(this._lastEditorValue),this.editor._value=this._workbook._inputForRef(this._workbook.activeSheet()._viewActiveCell()),this.editor.select()}.bind(this))):this.view.openDialog(t.reason)),t},_activeTooltip:function(){return""+this._workbook.activeSheet().activeCell().simplify()},onContextMenuSelect:function(e){var t,i=n(e.item).data("action");switch(i){case"cut":t={command:"ToolbarCutCommand",options:{workbook:this._workbook}};break;case"copy":t={command:"ToolbarCopyCommand",options:{workbook:this._workbook}};break;case"paste":t={command:"ToolbarPasteCommand",options:{workbook:this._workbook}};break;case"delete-drawing":t={command:"DeleteDrawingCommand",options:{drawing:this.navigator._sheet._activeDrawing}};break;case"bring-to-front":t={command:"BringToFrontCommand",options:{drawing:this.navigator._sheet._activeDrawing}};break;case"send-to-back":t={command:"SendToBackCommand",options:{drawing:this.navigator._sheet._activeDrawing}};break;case"unmerge":t={command:"MergeCellCommand",options:{value:"unmerge"}};break;case"merge":this.view.openDialog("merge");break;case"hide-row":t={command:"HideLineCommand",options:{axis:"row"}};break;case"hide-column":t={command:"HideLineCommand",options:{axis:"column"}};break;case"unhide-row":t={command:"UnHideLineCommand",options:{axis:"row"}};break;case"unhide-column":t={command:"UnHideLineCommand",options:{axis:"column"}};break;case"delete-row":t={command:"DeleteRowCommand"};break;case"delete-column":t={command:"DeleteColumnCommand"}}t&&this._execute(t)},onSheetBarRemove:function(e){var t=this._workbook.sheetByName(e.name);t&&this._workbook.removeSheet(t)},destroy:function(){this.listener.destroy(),this._enableEditorEvents(!1),this.keyListener.destroy(),this.inputKeyListener.destroy()},onSheetBarSelect:function(e){var t,n=this._workbook;if(e.isAddButton){if(this._workbook.trigger("insertSheet"))return;t=n.insertSheet()}else t=n.sheetByName(e.name);if(n.activeSheet().name()!==t.name()){if(this._workbook.trigger("selectSheet",{sheet:t}))return;this.editor.canInsertRef(!1)||this.editor.deactivate(),n.activeSheet(t)}},onSheetBarReorder:function(e){
var t=this._workbook.sheetByIndex(e.oldIndex);this._workbook.moveSheetToIndex(t,e.newIndex),this._workbook.activeSheet(t)},onSheetBarRename:function(e){var t=this._workbook.sheetByIndex(e.sheetIndex);return this._workbook.sheetByName(e.name)?void this.view.showError({reason:"error",type:"duplicateSheetName"}):(this._workbook.renameSheet(t,e.name),void this.clipboardElement.focus())},sheet:function(e){this.navigator=e.navigator(),this.axisManager=e.axisManager()},workbook:function(e){this._workbook=e,this.clipboard=e.clipboard(),e.bind("commandRequest",this.onCommandRequest.bind(this))},refresh:function(){var e,t,n=this.editor,i=this._workbook,r=i.activeSheet();this._viewPortHeight=this.view.scroller.clientHeight,this.navigator.height(this._viewPortHeight),n.isActive()||this.isEditorDisabled||(n.enable(r.selection().enable()!==!1),this.resetEditorValue()),e=r.selection()._ref.simplify(),t=this._workbook.nameForRef(e,r.name()),this.view.nameEditor.value(t.name)},onScroll:function(){this.view.render({scroll:!0})},onWheel:function(e){var t=e.originalEvent.deltaX,n=e.originalEvent.deltaY;1===e.originalEvent.deltaMode&&(t*=10,n*=10),this.scrollWith(t,n),e.preventDefault()},onTouchMove:function(){this.view.forceScrollerStackingOrder(2)},onTouchEnd:function(){this.view.forceScrollerStackingOrder(1)},onAction:function(e,t){var n=this._workbook.activeSheet();n._activeDrawing=null,this.navigator.moveActiveCell(r[t]),e.preventDefault()},onPageUp:function(){this.scrollDown(-this._viewPortHeight)},onPageDown:function(){this.scrollDown(this._viewPortHeight)},onEntryAction:function(e,t){var n,r,s,a,l=this._workbook.activeSheet();if(e.mod){switch(n=!0,r=String.fromCharCode(e.keyCode)){case"A":l._activeDrawing=null,this.navigator.selectAll();break;case"Y":this._workbook.undoRedoStack.redo();break;case"Z":this._workbook.undoRedoStack.undo();break;default:n=!1}n&&e.preventDefault()}else if(s=l.selection().enable()===!1,a=":edit"!==t,"delete"==t||"backspace"==t)l._activeDrawing?this._execute({command:"DeleteDrawingCommand",options:{drawing:l._activeDrawing}}):s||this._execute({command:"ClearContentCommand"}),e.preventDefault();else if(i.test(t)||!a){if(l._activeDrawing=null,s)return void e.preventDefault();a&&this.editor.value(""),this.activateEditor(a)}else this.navigator.navigateInSelection(o[t]),e.preventDefault()},onShiftAction:function(e,t){this.navigator.modifySelection(r[t.replace("shift+","")],this.appendSelection),e.preventDefault()},onMouseMove:function(e){var t,n=this._workbook.activeSheet();n.resizingInProgress()||n.selectionInProgress()||(t=this.objectAt(e),"columnresizehandle"===t.type||"rowresizehandle"===t.type?n.positionResizeHandle(t.ref):n.removeResizeHandle(),n._renderComment("cell"==t.type?t.ref:null))},onMouseDown:function(t){var n,i,r=this.objectAt(t);if(r.pane&&(this.originFrame=r.pane),this._startResizingDrawing(t,r))return void t.stopPropagation();if(n=this._workbook.activeSheet(),i=this.container.closest('[data-role="window"]'),i.length&&(i=e.widgetInstance(i),i&&i.options.modal&&t.stopPropagation()),n._activeDrawing=null,"drawing"===r.type)return n._activeDrawing=r.drawing,r.copy=r.drawing.clone(),r.startBox=n.drawingBoundingBox(r.copy),n.startDragging(r),n.triggerChange({dragging:!0}),void t.preventDefault();if("editor"===r.type)return this.onEditorEsc(),this.openCustomEditor(),void t.preventDefault();if(this.editor.canInsertRef(!1)&&r.ref)return this._workbook.activeSheet()._setFormulaSelections(this.editor.highlightedRefs()),this.navigator.startSelection(r.ref,this._selectionMode,this.appendSelection,t.shiftKey),void t.preventDefault();if(this._preventNavigation=!1,this.editor.deactivate(),!this._preventNavigation){if("columnresizehandle"===r.type||"rowresizehandle"===r.type)return n.startResizing({x:r.x,y:r.y}),void t.preventDefault();if("filtericon"===r.type)return this.openFilterMenu(t),void t.preventDefault();this._selectionMode=h[r.type],this.appendSelection=t.mod,this.navigator.startSelection(r.ref,this._selectionMode,this.appendSelection,t.shiftKey)}},_startResizingDrawing:function(e){var t,i,r,o,s=n(e.target).closest(".k-spreadsheet-drawing-handle");if(s.length)return t=this.translateCoords(e),i=s.data("direction"),r=this._workbook.activeSheet(),o=r._activeDrawing,r.startDragging({pane:this.originFrame,drawing:o,copy:o.clone(),startBox:r.drawingBoundingBox(o),resize:i,startX:t.x,startY:t.y}),!0},onContextMenu:function(t){var n,i,r,o,s,a=this._workbook.activeSheet();t.preventDefault(),a.resizingInProgress()||a.draggingInProgress()||(this.cellContextMenu.close(),this.colHeaderContextMenu.close(),this.rowHeaderContextMenu.close(),this.drawingContextMenu.close(),i=this.objectAt(t),"columnresizehandle"!==i.type&&"rowresizehandle"!==i.type&&(i.ref?this.navigator.selectForContextMenu(i.ref,h[i.type]):"drawing"==i.type&&this.navigator.selectDrawingForContextMenu(i.drawing),r=this.navigator._sheet.select()instanceof e.spreadsheet.UnionRef,o=!1,s=!1,"columnheader"==i.type?(n=this.colHeaderContextMenu,o=!r&&this.axisManager.selectionIncludesHiddenColumns()):"rowheader"==i.type?(n=this.rowHeaderContextMenu,o=!r&&this.axisManager.selectionIncludesHiddenRows()):"drawing"==i.type?n=this.drawingContextMenu:(n=this.cellContextMenu,s=this.navigator.selectionIncludesMergedCells()),n.element.find(d).toggle(!r),n.element.find(f).toggle(o),n.element.find("[data-action=unmerge]").toggle(s),setTimeout(function(){n.open(t.pageX,t.pageY)})))},prevent:function(e){e.preventDefault()},constrainResize:function(e,t){var n=this._workbook.activeSheet(),i=n.resizeHandlePosition();return!i||"outside"===e||"topcorner"===e||t.col<i.col||t.row<i.row},_dragDrawing:function(e){var t,n,i,r,o=this._workbook.activeSheet(),s=o.draggingInProgress();return!!s&&(t=this.translateCoords(e),n=s.drawing,i=t.x-s.startX,r=t.y-s.startY,"SE"==s.resize?s.aspect?Math.abs(i)>Math.abs(r)?(n.width=Math.max(s.copy.width+i,20),n.height=n.width/s.aspect):(n.height=Math.max(s.copy.height+r,20),n.width=n.height*s.aspect):(n.width=Math.max(s.copy.width+i,20),n.height=Math.max(s.copy.height+r,20)):"E"==s.resize?n.width=Math.max(s.copy.width+i,20):"S"==s.resize?n.height=Math.max(s.copy.height+r,20):"N"==s.resize?s.copy.height-r>20&&(n.height=s.copy.height-r,n.offsetY=s.copy.offsetY+r):"W"==s.resize?s.copy.width-i>20&&(n.width=s.copy.width-i,n.offsetX=s.copy.offsetX+i):"NE"==s.resize?(n.width=Math.max(s.copy.width+i,20),s.copy.height-r>20&&(n.height=s.copy.height-r,n.offsetY=s.copy.offsetY+r)):"SW"==s.resize?(n.height=Math.max(s.copy.height+r,20),s.copy.width-i>20&&(n.width=s.copy.width-i,n.offsetX=s.copy.offsetX+i)):"NW"==s.resize?(s.copy.height-r>20&&(n.height=s.copy.height-r,n.offsetY=s.copy.offsetY+r),s.copy.width-i>20&&(n.width=s.copy.width-i,n.offsetX=s.copy.offsetX+i)):(n.offsetX=s.copy.offsetX+i,n.offsetY=s.copy.offsetY+r),o.triggerChange({dragging:!0}),!0)},onMouseDrag:function(e){var t,n,i,r;if("sheet"!==this._selectionMode&&(t={clientX:e.clientX,clientY:e.clientY},n=this._workbook.activeSheet(),!this._dragDrawing(e))){if(i=this.objectAt(t),n.resizingInProgress())return void(this.constrainResize(i.type,i.ref)||n.resizeHintPosition({x:i.x,y:i.y}));if("outside"===i.type)return void this.startAutoScroll(i);this.originFrame===i.pane?this.selectToLocation(t):(r=this.originFrame._grid,i.x>r.right&&this.scrollLeft(),i.y>r.bottom&&this.scrollTop(),i.y<r.top||i.x<r.left?this.startAutoScroll(i,t):this.selectToLocation(t)),e.preventDefault()}},onMouseUp:function(e){var t,n,i,r=this._workbook.activeSheet();if(r.completeResizing(),r.completeDragging(),this.navigator.completeSelection(),this.stopAutoScroll(),t=this.editor.activeEditor()){for(n=e.target;n;){if(n===t.element[0])return;n=n.parentNode}i=this.objectAt(e),i&&i.ref&&t.canInsertRef(!1)&&(t.refAtPoint(r),r._setFormulaSelections(t.highlightedRefs()))}},onDblClick:function(e){var t,n=this.objectAt(e),i=this._workbook.activeSheet().selection().enable()===!1;"cell"!==n.type||i||(t=this._workbook.activeSheet().selection(),this.activateEditor(!t.value()&&!t.formula()),this.onEditorUpdate())},onCut:function(e){if(e){var t=this.clipboardElement.find("table.kendo-clipboard-"+this.clipboard._uid).detach();this.clipboardElement.append(t.clone(!1)),setTimeout(function(){this.clipboardElement.empty().append(t)}.bind(this))}this._execute({command:"CutCommand",options:{workbook:this.view._workbook,event:e.originalEvent||e}})},clipBoardValue:function(){return this.clipboardElement.html()},_pasteImage:function(e){var t=this,n=e.getAsFile(),i=new window.Image;i.src=window.URL.createObjectURL(n),i.onload=function(){t._execute({command:"InsertImageCommand",options:{blob:n,width:i.width,height:i.height}})},setTimeout(function(){window.URL.revokeObjectURL(i.src)},10)},onPaste:function(t){var n,i,r,o,s,a,l=this,u="",c="";if(l.clipboard.menuInvoked=void 0===t,t){if(n=t.originalEvent.clipboardData,!n||!n.getData)return a=l.clipboardElement.find("table.kendo-clipboard-"+l.clipboard._uid).detach(),l.clipboardElement.empty(),void setTimeout(function(){var e=l.clipboardElement.html(),n=window.clipboardData.getData("Text").trim();(e||n)&&(l.clipboard.external({html:e,plain:n}),l.clipboardElement.empty().append(a),l._execute({command:"PasteCommand",options:{workbook:l.view._workbook,event:t.originalEvent||t}}),l.clipboard.menuInvoked=!0)});if(t.preventDefault(),i=!1,r=!1,window.DOMStringList&&n.types instanceof window.DOMStringList?(i=n.types.contains("text/html"),r=n.types.contains("text/plain")):(i=/text\/html/.test(n.types),r=/text\/plain/.test(n.types)),i&&(u=n.getData("text/html")),r&&(c=n.getData("text/plain").trim()),!i&&!r&&n.items&&n.items.length)for(o=0;o<n.items.length;++o)if(s=n.items[o],"file"==s.kind&&/^image\/(?:png|jpe?g|gif)$/i.test(s.type))return l._pasteImage(s)}else{if(e.support.browser.msie)return l.clipboardElement.focus().select(),void document.execCommand("paste");l.clipboard.menuInvoked=!0}(u||c)&&(l.clipboard.external({html:u,plain:c}),l._execute({command:"PasteCommand",options:{workbook:l.view._workbook,event:t.originalEvent||t}}))},onCopy:function(e){this.clipboard.menuInvoked=void 0===e,this._execute({command:"CopyCommand",options:{workbook:this.view._workbook,event:e.originalEvent||e}})},scrollTop:function(){this.scroller.scrollTop=0},scrollLeft:function(){this.scroller.scrollLeft=0},scrollDown:function(e){this.scroller.scrollTop+=e},scrollRight:function(e){this.scroller.scrollLeft+=e},scrollWith:function(e,t){this.scroller.scrollTop+=t,this.scroller.scrollLeft+=e},translateCoords:function(e){var t=this.container[0].getBoundingClientRect();return{x:e.clientX-t.left,y:e.clientY-t.top}},objectAt:function(e,t){if(e)return e=this.translateCoords(e),this.view.objectAt(e.x,e.y,t)},selectToLocation:function(e){var t=this.objectAt(e,!0);t.pane&&t.ref&&(this.extendSelection(t),this.lastKnownCellLocation=e,this.originFrame=t.pane),this.stopAutoScroll()},extendSelection:function(e){this.navigator.extendSelection(e.ref,this._selectionMode,this.appendSelection)},autoScroll:function(){var e=this._autoScrollTarget.x,t=this._autoScrollTarget.y,n=this.originFrame._grid,i=this.view.scroller,r=8,o=i.scrollLeft,s=i.scrollTop;e<n.left&&this.scrollRight(-r),e>n.right&&this.scrollRight(r),t<n.top&&this.scrollDown(-r),t>n.bottom&&this.scrollDown(r),s===i.scrollTop&&o===i.scrollLeft?this.selectToLocation(this.finalLocation):this.extendSelection(this.objectAt(this.lastKnownCellLocation))},startAutoScroll:function(e,t){this._scrollInterval||(this._scrollInterval=setInterval(this.autoScroll.bind(this),50)),this.finalLocation=t||this.lastKnownCellLocation,this._autoScrollTarget=e},stopAutoScroll:function(){clearInterval(this._scrollInterval),this._scrollInterval=null},openCustomEditor:function(){this.view.openCustomEditor()},openFilterMenu:function(e){var t=this.objectAt(e),n=this._workbook.activeSheet(),i=n.filterColumn(t.ref),r=this.view.createFilterMenu(i);r.bind("action",this.onCommandRequest.bind(this)),r.bind("action",r.close.bind(r)),r.openFor(e.target)},_saveEditorValue:function(e){var t=this.editor._range.sheet(),n=this.editor.value();this._workbook.activeSheet()!==t&&(this._workbook.activeSheet()._setFormulaSelections(),this._workbook.activeSheet(t)),t.isInEditMode(!1),this._lastEditorValue=n,this._execute({command:"EditCommand",options:{value:n,arrayFormula:e}})},onEditorChange:function(){this._saveEditorValue(!1)},onEditorArrayFormula:function(){this._saveEditorValue(!0),this.editor.deactivate(!0)},onEditorActivate:function(){var e=this._workbook,t=e.activeSheet();t._setFormulaSelections(this.editor.highlightedRefs()),t.isInEditMode(!0)},onEditorDeactivate:function(){var e=this._workbook.activeSheet();e.isInEditMode(!1),e._setFormulaSelections([])},onEditorUpdate:function(){this._workbook.activeSheet()._setFormulaSelections(this.editor.highlightedRefs())},onEditorBarFocus:function(){var e=this._workbook.activeSheet().selection().enable()===!1;e||this.editor.activate({range:this._workbook.activeSheet().selection(),rect:this.view.activeCellRectangle(),tooltip:this._activeTooltip()})},onEditorCellFocus:function(){this.editor.scale()},onEditorEsc:function(){this.resetEditorValue(),this.editor.deactivate(),this.clipboardElement.focus()},insertNewline:function(e){e.preventDefault(),this.editor.insertNewline()},onEditorBlur:function(e,t){this.editor.isFiltered()||(this._preventNavigation=!1,this.editor.deactivate(),this._preventNavigation||(this.clipboardElement.focus(),this.navigator.navigateInSelection(o[t])))},onEditorAction:function(e,t){var n=this.editor,i=this._workbook.activeSheet();this._casualEditing&&/^(?:up|right|down|left)$/.test(t)?(this.deactivateEditor(),this.navigator.moveActiveCell(r[t]),e.preventDefault()):n.canInsertRef(!0)&&(this.navigator.moveActiveCell(r[t]),n.activeEditor().refAtPoint(i),i._setFormulaSelections(n.highlightedRefs()),e.preventDefault())},onEditorShiftAction:function(e,t){var n=this.editor,i=this._workbook.activeSheet();n.canInsertRef(!0)&&(this.navigator.modifySelection(r[t.replace("shift+","")],this.appendSelection),n.activeEditor().refAtPoint(i),i._setFormulaSelections(n.highlightedRefs()),e.preventDefault())},resetEditorValue:function(){var e=this._workbook.activeSheet(),t=e.activeCell(),n=this._workbook._inputForRef(t),i=e.range(t).intersectingArrayFormula();i&&(n="="+i.formula),this.editor.value(n,!!i)},activateEditor:function(e){this._casualEditing=e,this.editor.activate({range:this._workbook.activeSheet().selection(),rect:this.view.activeCellRectangle(),tooltip:this._activeTooltip()}).focus()},deactivateEditor:function(){this.view.editor.deactivate()},onCommandRequest:function(e){e.command?this._execute(e):this._workbook.undoRedoStack[e.action]()},onDialogRequest:function(e){var t={pdfExport:this._workbook.options.pdf,excelExport:this._workbook.options.excel};e.options?n.extend(!0,e.options,t):e.options=t,this.view.openDialog(e.name,e.options)},onNameEditorEnter:function(){var t,n=this._workbook,i=n.activeSheet(),r=this.view.nameEditor.value(),o=e.spreadsheet.calc.parseReference(r,!0)||n.nameValue(r);return o instanceof e.spreadsheet.Ref?(o.sheet&&o.sheet.toLowerCase()!=i.name().toLowerCase()&&(t=n.sheetByName(o.sheet),t&&(n.activeSheet(t),i=t)),void i.range(o).select()):(o=i.selection()._ref.clone().simplify().setSheet(i.name(),!0),this._execute({command:"DefineNameCommand",options:{name:r,value:o}}),void this.clipboardElement.focus())},onNameEditorCancel:function(){this.clipboardElement.focus()},onNameEditorSelect:function(t){var n,i=t.name,r=this._workbook,o=r.activeSheet(),s=r.nameValue(i);return s instanceof e.spreadsheet.Ref?(s.sheet&&s.sheet.toLowerCase()!=o.name().toLowerCase()&&(n=r.sheetByName(s.sheet),n&&(r.activeSheet(n),o=n)),void o.range(s).select()):void this.clipboardElement.focus()},onNameEditorDelete:function(e){this._execute({command:"DeleteNameCommand",options:{name:e.name}}),this.clipboardElement.focus()}}),e.spreadsheet.Controller=b}}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/view.min",["kendo.core.min","kendo.menu.min","spreadsheet/sheetsbar.min","util/main.min"],e)}(function(){!function(e){function t(e){var t,n=window.getSelection();n.removeAllRanges(),t=document.createRange(),t.selectNodeContents(e),n.addRange(t)}function n(e,t){var n=e.trs[t].children;return n[n.length-2]}function i(e,t){var n=e.trs[t-1],i=e.trs[t].children.length-1;if(n&&i>=0)return n.children[i]}function r(e){return(e.size||1)+"px solid "+(e.color||"#000")}function o(e){return/:\/\//.test(e)||(e="http://"+e),e}function s(t,n,i,s){function a(t){var i,r=n.link;return r||"object"==typeof n.value&&(r=n.value.link),r?(i={textDecoration:"none"},n.color&&(i.color=n.color),n.underline&&(i.textDecoration="underline"),e.dom.element("a",{href:o(r),style:i,target:"_blank"},t?[t]:[])):t}var l,u,c,h,d,f,p,m,g,v,b,w,y=null!=n.value||null!=n.validation&&!n.validation.value||n.background||n.merged||n.comment;if(i||y){if(l={},u=n.background,u&&(c=u,s&&(c=e.parseColor(c).toHSV(),c.v*=.9,c=c.toCssRgba()),c=r({color:c}),l.outline=c),u&&(l.backgroundColor=u),n.color&&(l.color=n.color),n.fontFamily&&(l.fontFamily=n.fontFamily),n.underline&&(l.textDecoration="underline"),n.italic&&(l.fontStyle="italic"),n.textAlign&&(l.textAlign=n.textAlign),n.bold&&(l.fontWeight="bold"),n.fontSize&&(l.fontSize=n.fontSize+"px"),n.wrap===!0?(l.whiteSpace="pre-wrap",l.overflowWrap="break-word",l.wordWrap="break-word"):(l.whiteSpace="pre",l.overflowWrap="normal",l.wordWrap="normal"),l.left=n.left+1+"px",l.top=n.top+1+"px",l.width=n.width-1+"px",l.height=n.height-1+"px",h=n.value,d=typeof h,f=n.format,f||"number"!=d||h==Math.floor(h)||(f="0.##############"),f&&null!=h?(h=e.spreadsheet.formatting.format(h,f),h.__dataType&&(d=h.__dataType)):null!==h&&void 0!==h&&(h=e.dom.text(h)),!l.textAlign)switch(d){case"number":case"date":case"percent":case"currency":l.textAlign="right";break;case"boolean":l.textAlign="center"}return e.spreadsheet.draw.applyIndent(n,l),p=[x.cell],i&&p.push(i),n.enable===!1&&p.push("k-state-disabled"),n.merged&&p.push("k-spreadsheet-merged-cell"),n.comment&&p.push("k-spreadsheet-has-comment"),m=n.verticalAlign||"bottom",h=m&&h?e.dom.element("div",{className:"k-vertical-align-"+m},[a(h)]):a(h),g=h?[h]:[],v={style:l},b=n.validation,b&&!b.value&&(g.push(e.dom.element("span",{className:"k-dirty"})),p.push("k-dirty-cell"),v.title=b.message),v.className=p.join(" "),w=e.dom.element("div",v,g),t.push(w),w}}function a(t,o,s){var a,l,u,c,h,d,f,p={};if(s.background&&(p.backgroundColor=s.background),s.color&&(p.color=s.color),s.fontFamily&&(p.fontFamily=s.fontFamily),s.underline&&(p.textDecoration="underline"),s.italic&&(p.fontStyle="italic"),s.textAlign&&(p.textAlign=s.textAlign),s.verticalAlign&&(p.verticalAlign="center"===s.verticalAlign?"middle":s.verticalAlign),s.bold&&(p.fontWeight="bold"),s.fontSize&&(p.fontSize=s.fontSize+"px"),s.wrap===!0&&(p.whiteSpace="pre-wrap"),s.borderRight?p.borderRight=r(s.borderRight):s.background&&(p.borderRightColor=s.background),s.borderBottom?p.borderBottom=r(s.borderBottom):s.background&&(p.borderBottomColor=s.background),a=s.value,l=typeof a,u=s.format,u||"number"!=l||a==Math.floor(a)||(u="0.##############"),u&&null!=a&&(a=e.spreadsheet.formatting.format(a,u),a.__dataType&&(l=a.__dataType)),!p.textAlign)switch(l){case"number":case"date":case"percent":case"currency":p.textAlign="right";break;case"boolean":p.textAlign="center"}return c=null,s.enable===!1&&(c="k-state-disabled"),h=t.addCell(o,a,p,c,s.validation),s.borderLeft?(f=n(t,o),d=r(s.borderLeft),f&&d&&(f.attr.style.borderRight=d)):s.background&&(p.borderLeftColor=s.background),s.borderTop?(f=i(t,o),d=r(s.borderTop),f&&d&&(f.attr.style.borderBottom=d)):s.background&&(p.borderTopColor=s.background),h}function l(e,t,n){return e>=t&&e<=n}function u(t){["N","NE","E","SE","S","SW","W","NW"].forEach(function(n){t.push(e.dom.element("div",{className:"k-spreadsheet-drawing-handle "+n,"data-direction":n}))})}var c,h,d,f,p,m,g,v,b,w,y,_,k,x,C;e.support.browser.msie&&e.support.browser.version<9||(c=e.jQuery,h=e.spreadsheet.CellRef,d=".",f=7,p=20,m={view:"k-spreadsheet-view",fixedContainer:"k-spreadsheet-fixed-container",editContainer:"k-spreadsheet-edit-container",scroller:"k-spreadsheet-scroller",viewSize:"k-spreadsheet-view-size",clipboard:"k-spreadsheet-clipboard",cellEditor:"k-spreadsheet-cell-editor",barEditor:"k-spreadsheet-editor",topCorner:"k-spreadsheet-top-corner",filterHeadersWrapper:"k-filter-wrapper",filterRange:"k-filter-range",filterButton:"k-spreadsheet-filter",filterButtonActive:"k-state-active",horizontalResize:"k-horizontal-resize",verticalResize:"k-vertical-resize",icon:"k-icon",iconFilterDefault:"k-i-arrow-60-down",sheetsBar:"k-spreadsheet-sheets-bar",sheetsBarActive:"k-spreadsheet-sheets-bar-active",sheetsBarInactive:"k-spreadsheet-sheets-bar-inactive",cellContextMenu:"k-spreadsheet-cell-context-menu",rowHeaderContextMenu:"k-spreadsheet-row-header-context-menu",colHeaderContextMenu:"k-spreadsheet-col-header-context-menu",drawingContextMenu:"k-spreadsheet-drawing-context-menu"},e.spreadsheet.messages.view={nameBox:"Name Box",errors:{openUnsupported:"Unsupported format. Please select an .xlsx file.",shiftingNonblankCells:"Cannot insert cells due to data loss possibility. Select another insert location or delete the data from the end of your worksheet.",insertColumnWhenRowIsSelected:"Cannot insert column when all columns are selected.",insertRowWhenColumnIsSelected:"Cannot insert row when all rows are selected.",filterRangeContainingMerges:"Cannot create a filter within a range containing merges",sortRangeContainingMerges:"Cannot sort a range containing merges",cantSortMultipleSelection:"Cannot sort multiple selection",cantSortNullRef:"Cannot sort empty selection",cantSortMixedCells:"Cannot sort range containing cells of mixed shapes",validationError:"The value that you entered violates the validation rules set on the cell.",cannotModifyDisabled:"Cannot modify disabled cells."},tabs:{home:"Home",insert:"Insert",data:"Data"}},e.spreadsheet.messages.menus={cut:"Cut",copy:"Copy",paste:"Paste",merge:"Merge",unmerge:"Unmerge","delete":"Delete",hide:"Hide",unhide:"Unhide",bringToFront:"Bring to front",sendToBack:"Send to back"},g=e.Class.extend({init:function(){this.cols=[],this.trs=[],this._height=0,this._width=0},addColumn:function(t){this._width+=t;var n=e.dom.element("col",{style:{width:t+"px"}});n.visible=t>0,this.cols.push(n)},addRow:function(t){var n,i=null;i={style:{height:t+"px"}},this._height+=t,n=e.dom.element("tr",i),n.visible=t>0,this.trs.push(n)},addCell:function(t,n,i,r,o){var s,a,l;return null!==n&&void 0!==n||(n=""),n instanceof e.dom.Node||(n=e.dom.text(n)),s=[n],a={style:i},o&&!o.value&&(s.push(e.dom.element("span",{className:"k-dirty"})),r=(r||"")+(r?" ":"")+"k-dirty-cell",a.title=o.message),r&&(a.className=r),l=e.dom.element("td",a,s),this.trs[t].children.push(l),l},toDomTree:function(t,n,i){this.trs=this.trs.filter(function(e){return e.visible});var r=0;return this.cols=this.cols.filter(function(e,t){return e.visible||(this.trs.forEach(function(e){e.children.splice(t-r,1)}),r++),e.visible},this),e.dom.element("table",{style:{left:t+"px",top:n+"px",height:this._height+"px",width:this._width+"px"},className:i},[e.dom.element("colgroup",null,this.cols),e.dom.element("tbody",null,this.trs)])}}),v='<ul class="#=classNames.cellContextMenu#"><li data-action=cut>#: messages.cut #</li><li data-action=copy>#: messages.copy #</li><li data-action=paste>#: messages.paste #</li><li class="k-separator"></li><li data-action=merge>#: messages.merge #</li><li data-action=unmerge>#: messages.unmerge #</li></ul>',b='<ul class="#=classNames.rowHeaderContextMenu#"><li data-action=cut>#: messages.cut #</li><li data-action=copy>#: messages.copy #</li><li data-action=paste>#: messages.paste #</li><li class="k-separator"></li><li data-action="delete-row">#: messages.delete #</li><li data-action="hide-row">#: messages.hide #</li><li data-action="unhide-row">#: messages.unhide #</li></ul>',w='<ul class="#=classNames.colHeaderContextMenu#"><li data-action=cut>#: messages.cut #</li><li data-action=copy>#: messages.copy #</li><li data-action=paste>#: messages.paste #</li><li class="k-separator"></li><li data-action="delete-column">#: messages.delete #</li><li data-action="hide-column">#: messages.hide #</li><li data-action="unhide-column">#: messages.unhide #</li></ul>',y='<ul class="#=classNames.drawingContextMenu#"><li data-action="bring-to-front">#: messages.bringToFront #</li><li data-action="send-to-back">#: messages.sendToBack #</li><li class="k-separator"></li><li data-action="delete-drawing">#: messages.delete #</li></ul>',e.spreadsheet.ContextMenu=e.ui.ContextMenu,_=e.template('<div class="#=classNames.view#"><div class="#=classNames.fixedContainer#"></div><div class="#=classNames.scroller#"><div class="#=classNames.viewSize#"></div></div><div tabindex="0" class="#=classNames.clipboard#" contenteditable=true></div><div class="#=classNames.cellEditor#"></div></div><div class="#=classNames.sheetsBar#"></div>'+v+b+w+y),k=e.Class.extend({init:function(t,n){var i,r=k.classNames;this.element=t,this.options=c.extend(!0,{messages:e.spreadsheet.messages.view},this.options,n),this._chrome(),this._dialogs=[],t.append(_({classNames:r,messages:e.spreadsheet.messages.menus})),this._formulaInput(),this.wrapper=t.find(d+r.view),this.container=t.find(d+r.fixedContainer)[0],this.scroller=t.find(d+r.scroller)[0],this.clipboard=t.find(d+r.clipboard),this.viewSize=c(this.scroller.firstChild),this.tree=new e.dom.Tree(this.container),this.clipboardContents=new e.dom.Tree(this.clipboard[0]),this.editor=new e.spreadsheet.SheetEditor(this),this._sheetsbar(),i={target:t,animation:!1,showOn:"never"},this.cellContextMenu=new e.spreadsheet.ContextMenu(t.find(d+r.cellContextMenu),i),this.colHeaderContextMenu=new e.spreadsheet.ContextMenu(t.find(d+r.colHeaderContextMenu),i),this.rowHeaderContextMenu=new e.spreadsheet.ContextMenu(t.find(d+r.rowHeaderContextMenu),i),this.drawingContextMenu=new e.spreadsheet.ContextMenu(t.find(d+r.drawingContextMenu),i)},enableClipboard:function(e){this.isClipboardDeactivated=!e,e?this.clipboard.attr("contenteditable",e):this.clipboard.removeAttr("contenteditable")},_resize:function(){var t=c(this.formulaBar.element).parents(".k-spreadsheet-action-bar"),n=e._outerHeight,i=this.tabstrip?n(this.tabstrip.element):0,r=t?n(t):0,o=this.sheetsbar?n(this.sheetsbar.element):0;this.wrapper.height(this.element.height()-(i+r+o)),this.tabstrip&&this.tabstrip.quickAccessAdjust()},_chrome:function(){var t,n=c("<div class='k-spreadsheet-action-bar' />").prependTo(this.element),i=c("<div class='k-spreadsheet-name-editor' />").appendTo(n);this.nameEditor=new e.spreadsheet.NameEditor(i,this.options),t=c("<div />").appendTo(n),this.formulaBar=new e.spreadsheet.FormulaBar(t),this.options.toolbar&&this._tabstrip()},_formulaInput:function(){var t=this.element.find(d+k.classNames.cellEditor);this.formulaInput=new e.spreadsheet.FormulaInput(t,{autoScale:!0})},_sheetsbar:function(){if(this.options.sheetsbar){var t=c.extend(!0,{openDialog:this.openDialog.bind(this)},this.options.sheetsbar);this.sheetsbar=new e.spreadsheet.SheetsBar(this.element.find(d+k.classNames.sheetsBar),t)}},_tabstrip:function(){var t,n=this.options.messages.tabs,i=c.extend(!0,{home:!0,insert:!0,data:!0},this.options.toolbar),r=[];this.tabstrip&&(this.tabstrip.destroy(),this.element.children(".k-tabstrip").remove());for(t in i)(i[t]===!0||i[t]instanceof Array)&&r.push({id:t,text:n[t],content:""});this.tabstrip=new e.spreadsheet.TabStrip(c("<div />").prependTo(this.element),{animation:!1,dataTextField:"text",dataContentField:"content",dataSource:r,toolbarOptions:i,view:this}),this.tabstrip.select(0)},_executeCommand:function(e){this._sheet.trigger("commandRequest",e)},workbook:function(e){this._workbook=e,e._view=this,this.nameEditor._workbook=e},sheet:function(e){this._sheet=e},activeCellRectangle:function(){return this.cellRectangle(this._sheet._viewActiveCell())},_rectangle:function(e,t){return e._grid.boundingRectangle(t.toRangeRef())},isColumnResizer:function(e,t,n){return e-=this._sheet._grid._headerWidth,t._grid.columns.frozen||(e+=this.scroller.scrollLeft),n=this._sheet._grid._columns.locate(0,n,function(t){return Math.abs(e-t)<=f/2}),null===n||this._sheet.isHiddenColumn(n)?null:n},isRowResizer:function(e,t,n){return e-=this._sheet._grid._headerHeight,t._grid.rows.frozen||(e+=this.scroller.scrollTop),n=this._sheet._grid._rows.locate(0,n,function(t){return Math.abs(e-t)<=f/2}),null===n||this._sheet.isHiddenRow(n)?null:n},isFilterIcon:function(t,n,i,r){var o=this,s=i._grid,a=s.rows.frozen?0:o.scroller.scrollTop,l=s.columns.frozen?0:o.scroller.scrollLeft;return t-=o._sheet._grid._headerWidth-l,n-=o._sheet._grid._headerHeight-a,e.util.withExit(function(e){o._sheet.forEachFilterHeader(r,function(r){var s=o._rectangle(i,r);i.filterIconRect(s).intersects(t,n)&&e(!0)})})},isAutoFill:function(e,t,n){var i,r=this._sheet.select();return!(r.size>1)&&(e-=this._sheet._grid._headerWidth,t-=this._sheet._grid._headerHeight,n._grid.columns.frozen||(e+=this.scroller.scrollLeft),n._grid.rows.frozen||(t+=this.scroller.scrollTop),i=this._rectangle(n,r),Math.abs(i.right-e)<8&&Math.abs(i.bottom-t)<8)},isEditButton:function(e,t,n){var i,r=this._sheet.activeCellCustomEditor();if(r&&(i=this.activeCellRectangle(),t>=i.top&&t<=i.bottom))return n._editorInLastColumn?e<i.left&&e>=i.left-p:e>i.right&&e<=i.right+p},drawingAt:function(e,t,n){var i,r,o,s,a;for(e-=this._sheet._grid._headerWidth,t-=this._sheet._grid._headerHeight,n._grid.columns.frozen||(e+=this.scroller.scrollLeft),n._grid.rows.frozen||(t+=this.scroller.scrollTop),i=this._sheet,r=this._sheet._drawings,o=r.length;--o>=0;)if(s=r[o],a=i.drawingBoundingBox(s),a.intersects(e,t))return{drawing:s,drx:a.left-e,dry:a.top-t}},objectAt:function(e,t,n){var i,r,o,s,a,l,u,c,d=this._sheet._grid;if(e<0||t<0||e>this.scroller.clientWidth||t>this.scroller.clientHeight)i={type:"outside"};else if(e<d._headerWidth&&t<d._headerHeight)i={type:"topcorner"};else if(r=this.paneAt(e,t)){if(!n&&(o=this.drawingAt(e,t,r)))return{type:"drawing",drawing:o.drawing,drx:o.drx,dry:o.dry,pane:r,startX:e,startY:t};s=r._grid.rows.indexVisible(t,this.scroller.scrollTop),a=r._grid.columns.indexVisible(e,this.scroller.scrollLeft),l="cell",u=new h(s,a),c=this._sheet.selectionInProgress(),this.isAutoFill(e,t,r)?l="autofill":this.isFilterIcon(e,t,r,u)?l="filtericon":!c&&e<d._headerWidth?(l="rowheader",null!==(s=this.isRowResizer(t,r,s))&&(u=new h(s,(-(1/0))),l="rowresizehandle")):!c&&t<d._headerHeight?(l="columnheader",null!==(a=this.isColumnResizer(e,r,a))&&(u=new h((-(1/0)),a),l="columnresizehandle")):this.isEditButton(e,t,r)&&(l="editor"),"cell"==l&&this._sheet.forEachMergedCell(u,function(e){u=e.topLeft}),i={type:l,ref:u}}else i={type:"outside"};return i.pane=r,i.x=e,i.y=t,i},paneAt:function(e,t){return this.panes.filter(function(n){var i=n._grid;return l(t,i.top,i.bottom)&&l(e,i.left,i.right)})[0]},containingPane:function(e){return this.panes.filter(function(t){return!!t._grid.contains(e)})[0]},cellRectangle:function(e){var t=this.containingPane(e)._grid,n=this._sheet._grid.rectangle(e);return n.offset(t.headerWidth-(t.columns.frozen?0:this.scroller.scrollLeft),t.headerHeight-(t.rows.frozen?0:this.scroller.scrollTop))},refresh:function(e){var t,n,i=this._sheet;this.tabstrip&&this.tabstrip.refreshTools(i.range(i.activeCell())),e.sheetSelection&&this.sheetsbar&&this.sheetsbar.renderSheets(this._workbook.sheets(),this._workbook.sheetIndex(this._sheet)),this._resize(),this.viewSize[0].style.height=i._grid.totalHeight()+"px",this.viewSize[0].style.width=i._grid.totalWidth()+"px",e.layout&&(t=i.frozenColumns(),n=i.frozenRows(),this.panes=[this._pane(n,t)],t>0&&this.panes.push(this._pane(n,0,null,t)),n>0&&this.panes.push(this._pane(0,t,n,null)),n>0&&t>0&&this.panes.push(this._pane(0,0,n,t))),e.filter&&this._destroyFilterMenu(),e.activeCell&&(this._focus=i.activeCell().toRangeRef())},createFilterMenu:function(t){
var n,i,r,o,s,a;return this._filterMenu&&this._filterMenu.options.column==t?this._filterMenu:(this._destroyFilterMenu(),n=this._sheet,i=n.filter().ref,r=new e.spreadsheet.Range(i,n),o=c("<div />").appendTo(this.element),s={column:t,range:r},a=new e.spreadsheet.FilterMenu(o,s),this._filterMenu=a,a)},selectClipboardContents:function(){this.isClipboardDeactivated||(this.clipboard.focus(),t(this.clipboard[0]))},scrollIntoView:function(e){var t=!1,n=this.containingPane(e)._grid,i=n.scrollBoundaries(e),r=this.scroller,o=n.rows.frozen?0:r.scrollTop,s=n.columns.frozen?0:r.scrollLeft;return i.top<o&&(t=!0,r.scrollTop=i.scrollTop),i.bottom>o&&(t=!0,r.scrollTop=i.scrollBottom),i.left<s&&(t=!0,r.scrollLeft=i.scrollLeft),i.right>s&&(t=!0,r.scrollLeft=i.scrollRight),t},_destroyDialog:function(){this._dialogs.pop()},openCustomEditor:function(){var e=this,t=e._sheet.activeCell().first(),n=e._sheet.activeCellCustomEditor(),i=e._sheet.range(t);n.edit({range:i,rect:e.activeCellRectangle(),view:this,validation:this._sheet.validation(t),callback:function(t,n){e._executeCommand({command:"EditCommand",options:{operatingRange:i,property:n?"input":"value",value:t}})}})},openDialog:function(t,n){var i=this._sheet;return i.withCultureDecimals(function(){var r,o,s=e.spreadsheet.dialogs.create(t,n);if(s)return s.bind("action",this._executeCommand.bind(this)),s.bind("deactivate",this._destroyDialog.bind(this)),this._dialogs.push(s),r=i.activeCell(),o=new e.spreadsheet.Range(r,i),s.open(o),s}.bind(this))},showError:function(t,n){var i,r=this.options.messages.errors,o=function(t){var n=t.sender.dialog().element;n.find(".k-button:first").focus(),n.find(".k-button, input").on("keydown",function(n){n.keyCode==e.keys.ESC&&t.sender.close()})},s=function(e){var t=e.sender;this.selectClipboardContents(),t._retry&&n&&n()}.bind(this);e.spreadsheet.dialogs.registered(t.type)?(i={close:s},"validationError"===t.type&&(i=c.extend(i,{title:t.title||"Error",text:t.body?t.body:r[t.type],activate:o})),this.openDialog(t.type,i)):this.openDialog("message",{title:t.title||"Error",text:t.body?t.body:r[t.type],activate:o,close:s})},destroy:function(){this._dialogs.forEach(function(e){e.destroy()}),this.cellContextMenu.destroy(),this.rowHeaderContextMenu.destroy(),this.colHeaderContextMenu.destroy(),this.tabstrip&&this.tabstrip.destroy(),this._destroyFilterMenu()},_destroyFilterMenu:function(){this._filterMenu&&(this._filterMenu.destroy(),this._filterMenu=void 0,this._filterMenuColumn=void 0)},render:function(t){var n,i,r,o,s,a,l,u;this.element.is(":visible")&&(n=this._sheet,i=n.focus(),i&&this.scrollIntoView(i)||(r=n.resizingInProgress()?n.resizeHandlePosition().col===-(1/0)?"column":"row":"none",this.wrapper.toggleClass(m.editContainer,this.editor.isActive()).toggleClass(m.horizontalResize,"row"==r).toggleClass(m.verticalResize,"column"==r),o=n._grid,s=this.scroller,a=this.panes.map(function(e){return e.render(s)}),l=e.dom.element("div",{style:{width:o._headerWidth+"px",height:o._headerHeight+"px"},className:k.classNames.topCorner}),a.push(l),n.resizeHandlePosition()&&n.resizeHintPosition()&&a.push(this.renderResizeHint()),this.tree.render(a),u=e.support.scrollbar(),c(this.container).css({width:this.wrapper[0].clientWidth-u,height:this.wrapper[0].clientHeight-u}),this.editor.isActive()?this.editor.toggleTooltip(this.activeCellRectangle()):t.resize||t.scroll||t.comment||n.selectionInProgress()||n.resizingInProgress()||n.draggingInProgress()||n.isInEditMode()||this.renderClipboardContents()))},renderResizeHint:function(){var t,n,i=this._sheet,r=i.resizeHandlePosition(),o=r.col!==-(1/0);return t=o?{height:this.scroller.clientHeight+"px",width:f+"px",left:i.resizeHintPosition().x+"px",top:"0px"}:{height:f+"px",width:this.scroller.clientWidth+"px",top:i.resizeHintPosition().y+"px",left:"0px"},n=C.classNames,e.dom.element("div",{className:n.resizeHint+(o?"":" "+n.resizeHintVertical),style:t},[e.dom.element("div",{className:n.resizeHintHandle}),e.dom.element("div",{className:n.resizeHintMarker})])},renderClipboardContents:function(){var e,t,n,i,r,o=this._sheet,s=o._grid,l=s.normalize(o.select().toRangeRef()),u=this._workbook.clipboard().canCopy();return u.canCopy===!1&&u.multiSelection?(this.clipboardContents.render([]),void this.selectClipboardContents()):(l=o.trim(l),e=new g,t=s.rangeDimensions(l),t.rows.forEach(function(t){e.addRow(t)}),t.columns.forEach(function(t){e.addColumn(t)}),n=o._getMergedCells(l),i=n.primary,r=n.secondary,o.forEach(l,function(t,n,o){var s,u,c=new h(t,n).print();r[c]||(s=a(e,t-l.topLeft.row,o),u=i[c],u&&(s.attr.colspan=u.width(),s.attr.rowspan=u.height()))}),this.clipboardContents.render([e.toDomTree(0,0,"kendo-clipboard-"+this._workbook.clipboard()._uid)]),void this.selectClipboardContents())},_pane:function(e,t,n,i){var r=new C(this._sheet,this._sheet._grid.pane({row:e,column:t,rowCount:n,columnCount:i}));return r.refresh(this.scroller.clientWidth,this.scroller.clientHeight),r},forceScrollerStackingOrder:function(e){c(this.scroller).css("z-index",e)}}),x={cell:"k-spreadsheet-cell",vaxis:"k-spreadsheet-vaxis",haxis:"k-spreadsheet-haxis",vborder:"k-spreadsheet-vborder",hborder:"k-spreadsheet-hborder",rowHeader:"k-spreadsheet-row-header",columnHeader:"k-spreadsheet-column-header",pane:"k-spreadsheet-pane",data:"k-spreadsheet-data",mergedCell:"k-spreadsheet-merged-cell",mergedCellsWrapper:"k-merged-cells-wrapper",activeCell:"k-spreadsheet-active-cell",selection:"k-spreadsheet-selection",selectionWrapper:"k-selection-wrapper",autoFillWrapper:"k-auto-fill-wrapper",single:"k-single",top:"k-top",right:"k-right",bottom:"k-bottom",left:"k-left",resizeHandle:"k-resize-handle",columnResizeHandle:"k-column-resize-handle",rowResizeHandle:"k-row-resize-handle",resizeHint:"k-resize-hint",resizeHintHandle:"k-resize-hint-handle",resizeHintMarker:"k-resize-hint-marker",resizeHintVertical:"k-resize-hint-vertical",selectionHighlight:"k-spreadsheet-selection-highlight",series:["k-series-a","k-series-b","k-series-c","k-series-d","k-series-e","k-series-f"]},C=e.Class.extend({init:function(e,t){this._sheet=e,this._grid=t},refresh:function(e,t){this._grid.refresh(e,t)},isVisible:function(e,t,n){return this._grid.view(e,t).ref.intersects(n)},render:function(t){var n,i,r,o,s,a,l,u,c,h=t.scrollLeft,d=t.scrollTop;return d<0&&(d=0),h<0&&(h=0),n=C.classNames,i=this._sheet,r=this._grid,o=r.view(h,d),this._currentView=o,this._currentRect=this._rectangle(o.ref),this._selectedHeaders=i.selectedHeaders(),s=[],s.push(this.renderData()),i._activeDrawing||s.push(this.renderSelection(t)),s.push(this.renderAutoFill()),s.push(this.renderEditorSelection()),s.push(this.renderFilterHeaders()),r.hasRowHeader&&(a=e.dom.element("div",{className:n.rowHeader,style:{width:r.headerWidth+"px",top:o.rowOffset+"px"}}),s.push(a),i.forEach(o.ref.leftColumn(),function(t){if(!i.isHiddenRow(t)){var n=t+1,o=i.rowHeight(t);a.children.push(e.dom.element("div",{className:this.headerClassName(t,"row"),style:{width:r.headerWidth+"px",height:o+"px"}},[e.dom.element("div",{className:"k-vertical-align-center"},[e.dom.text(n+"")])]))}}.bind(this))),r.hasColumnHeader&&(l=e.dom.element("div",{className:n.columnHeader,style:{top:"0px",left:o.columnOffset+"px",width:this._currentRect.width+"px",height:r.headerHeight+"px"}}),s.push(l),u=0,i.forEach(o.ref.topRow(),function(t,n){if(!i.isHiddenColumn(n)){var o=e.spreadsheet.Ref.display(null,1/0,n),s=i.columnWidth(n);l.children.push(e.dom.element("div",{className:this.headerClassName(n,"col"),style:{position:"absolute",left:u+"px",width:s+"px",height:r.headerHeight+"px"}},[e.dom.element("div",{className:"k-vertical-align-center"},[e.dom.text(o+"")])])),u+=s}}.bind(this))),i.resizeHandlePosition()&&(r.hasColumnHeader||r.hasRowHeader)&&(i.resizeHintPosition()||this.renderResizeHandle(s)),c=[n.pane],r.hasColumnHeader&&c.push(n.top),r.hasRowHeader&&c.push(n.left),e.dom.element("div",{style:r.style,className:c.join(" ")},s)},headerClassName:function(e,t){var n,i,r,o=this._selectedHeaders;return"row"===t?(n=o.rows[e],i=o.allRows):(n=o.cols[e],i=o.allCols),r=n||(o.all?"full":i?"partial":"none"),r&&(r="k-selection-"+r),r},renderData:function(){var t,n,i,r,o=this._sheet,a=this._currentView,l=e.dom.element("div",{className:C.classNames.data,style:{position:"relative",left:a.columnOffset+"px",top:a.rowOffset+"px"}}),u=this._currentRect,c=e.spreadsheet.draw.doLayout(o,a.ref,{forScreen:!0}),h=o._showGridLines;return h&&(t=null,c.xCoords.forEach(function(n){n!==t&&(t=n,l.children.push(e.dom.element("div",{className:x.vaxis,style:{left:n+"px",height:u.height+"px",borderColor:o.gridLinesColor()}})))}),t=null,c.yCoords.forEach(function(n){n!==t&&(t=n,l.children.push(e.dom.element("div",{className:x.haxis,style:{top:n+"px",width:u.width+"px",borderColor:o.gridLinesColor()}})))})),n=e.spreadsheet.draw.Borders(),i=o.activeCell().toRangeRef(),r=i.topLeft,c.cells.forEach(function(t){var u,c,d,f=null,p=t.row+a.ref.topLeft.row,m=t.col+a.ref.topLeft.col;o._activeDrawing?(u=o._activeDrawing.topLeftCell,u&&u.row==p&&u.col==m&&(f="k-spreadsheet-drawing-anchor-cell")):p==r.row&&m==r.col&&(f=[C.classNames.activeCell].concat(this._activeFormulaColor(),this._directionClasses(i)),o.singleCellSelection()&&f.push(C.classNames.single),f=f.join(" ")),n.add(t),s(l.children,t,f,h),t.comment&&o._commentRef&&p==o._commentRef.row&&m==o._commentRef.col&&(c=4,d=e.dom.element("div",{className:"k-tooltip k-spreadsheet-cell-comment",style:{left:t.right+c+"px",top:t.top+"px"}},[e.dom.text(t.comment)]),l.children.push(d))},this),n.vert.forEach(function(t){t.forEach(function(t){if(!t.rendered){t.rendered=!0;var n={left:t.x+"px",top:t.top+"px",height:t.bottom-t.top+1+"px",borderWidth:t.size+"px",borderColor:t.color};1!=t.size&&(n.transform="translateX(-"+(t.size-1)/2+"px)"),l.children.push(e.dom.element("div",{className:x.vborder,style:n}))}})}),n.horiz.forEach(function(t){t.forEach(function(t){if(!t.rendered){t.rendered=!0;var n={top:t.y+"px",left:t.left+"px",width:t.right-t.left+"px",borderWidth:t.size+"px",borderColor:t.color};1!=t.size&&(n.transform="translateY(-"+(t.size-1)/2+"px)"),l.children.push(e.dom.element("div",{className:x.hborder,style:n}))}})}),this.renderDrawings(c,l.children),l},renderDrawings:function(t,n){var i=this._sheet,r=i._workbook;t.drawings.forEach(function(t){var o=t.drawing,s=t.box,a=s.toDiv("k-spreadsheet-drawing");o.image&&a.children.push(e.dom.element("div",{className:"k-spreadsheet-drawing-image",style:{backgroundImage:"url('"+r.imageUrl(o.image)+"')",opacity:o.opacity}})),o===i._activeDrawing&&(a.attr.className+=" k-spreadsheet-active-drawing",u(a.children)),n.push(a)})},renderResizeHandle:function(t){var n,i=this._sheet,r=i.resizeHandlePosition(),o=this._rectangle(r),s=[C.classNames.resizeHandle];if(r.col!==-(1/0)){if(this._grid.rows._start>0)return;n={height:this._grid.headerHeight+"px",width:f+"px",left:o.right-f/2+"px",top:"0px"},s.push(m.horizontalResize)}else{if(this._grid.columns._start>0)return;n={height:f+"px",width:this._grid.headerWidth+"px",top:o.bottom-f/2+"px",left:"0px"},s.push(m.verticalResize)}t.push(e.dom.element("div",{className:s.join(" "),style:n}))},filterIconRect:function(t){var n=16,i=3;return new e.spreadsheet.Rectangle(t.right-n-i,t.top+i,n,n)},renderFilterHeaders:function(){function t(t){return e.dom.element("span",{className:o.icon+" "+t})}function n(n,i,r){var o,a={left:i.left+"px",top:i.top+"px"},l=s&&s.columns.some(function(e){return e.index===r}),u=n.filterButton;return l&&(u+=" "+n.filterButtonActive),o=e.dom.element("span",{className:u,style:a},[t(n.iconFilterDefault)])}var i=this._sheet,r=[],o=k.classNames,s=i.filter();return s&&this._addDiv(r,s.ref,o.filterRange),i.forEachFilterHeader(this._currentView.ref,function(e){var t=this._rectangle(e),i=this.filterIconRect(t),s=this._sheet.filterColumn(e),a=n(o,i,s);r.push(a)}.bind(this)),e.dom.element("div",{className:o.filterHeadersWrapper},r)},renderEditorSelection:function(){var t=C.classNames,n=this._sheet,i=[];return n._formulaSelections.forEach(function(n){var r=n.ref;r!==e.spreadsheet.NULLREF&&this._addDiv(i,r,t.selectionHighlight+" "+n.colorClass)}.bind(this)),e.dom.element("div",{className:t.selectionWrapper},i)},renderSelection:function(t){var n=C.classNames,i=[],r=[n.selection],o=this._sheet,s=o.activeCell().toRangeRef(),a=o.select();return r=r.concat(this._activeFormulaColor()),1===a.size()&&r.push("k-single-selection"),this._sheet.autoFillPunch()&&r.push("k-dim-auto-fill-handle"),a.forEach(function(t){t!==e.spreadsheet.NULLREF&&this._addDiv(i,t,r.join(" "))}.bind(this)),this._renderCustomEditorButton(i,s,t),e.dom.element("div",{className:n.selectionWrapper},i)},renderAutoFill:function(){var t,n,i,r,o,s,a,l=[];if(this._sheet.autoFillInProgress())if(t=this._sheet.autoFillRef(),n=this._sheet.autoFillPunch(),i=this._sheet._autoFillDirection,this._addDiv(l,t,"k-auto-fill"),n)this._addDiv(l,n,"k-auto-fill-punch");else if(void 0!==i){switch(i){case 0:r=t.bottomRight,o="k-auto-fill-br-hint";break;case 1:r=t.bottomRight,o="k-auto-fill-br-hint";break;case 2:r=new h(t.topLeft.row,t.bottomRight.col),o="k-auto-fill-tr-hint";break;case 3:r=new h(t.bottomRight.row,t.topLeft.col),o="k-auto-fill-bl-hint"}s=this._addDiv(l,r,o),s&&(a=e.dom.element("span",{className:"k-tooltip"},[e.dom.text(this._sheet._autoFillHint)]),s.children.push(a))}return e.dom.element("div",{className:C.classNames.autoFillWrapper},l)},_addDiv:function(e,t,n){var i,r=this._currentView;return r.ref.intersects(t)&&(i=this._rectangle(t).resize(1,1).toDiv(n),e.push(i)),i},_renderCustomEditorButton:function(t,n,i){var r,o=this,s=o._sheet,a=o._currentView,l=o._grid.columns._axis._count,u=s.activeCellCustomEditor();u&&a.ref.intersects(n)&&(r=o._rectangle(n),s.forEach(n.collapse(),function(n,s,a){var c,h,d;a.left=r.left,a.top=r.top,a.width=r.width,a.height=r.height,c="k-button k-spreadsheet-editor-button",h=s==l-1||o._buttonOutOfVisiblePane(n,s,i),h&&(c+=" k-spreadsheet-last-column"),o._editorInLastColumn=h,d=e.dom.element("div",{className:c,style:{left:a.left+(h?0:a.width)+"px",top:a.top+"px",height:a.height+"px"}}),u.icon&&d.children.push(e.dom.element("span",{className:"k-icon "+u.icon})),t.push(d)}))},_activeFormulaColor:function(){var e,t=[];return this._sheet.isInEditMode()&&(e=this._sheet._formulaSelections.filter(function(e){return e.active&&"ref"==e.type})[0],e&&t.push(e.colorClass)),t},_directionClasses:function(e){var t=[],n=C.classNames,i=this._currentView.ref;return e.move(0,-1).intersects(i)||t.push(n.left),e.move(-1,0).intersects(i)||t.push(n.top),e.move(0,1).intersects(i)||t.push(n.right),e.move(1,0).intersects(i)||t.push(n.bottom),t},_rectangle:function(e){return this._grid.boundingRectangle(e.toRangeRef()).offset(-this._currentView.mergedCellLeft,-this._currentView.mergedCellTop)},_buttonOutOfVisiblePane:function(e,t,n){var i=this,r=i._grid,o=i._sheet,s=o.range(e,t)._ref,a=r.scrollBoundaries(s),l=r.columns.frozen?0:n.scrollLeft;if(a.right+p>l||t+1===o.frozenColumns())return!0}}),e.spreadsheet.View=k,e.spreadsheet.Pane=C,e.spreadsheet.drawCell=s,c.extend(!0,k,{classNames:m}),c.extend(!0,C,{classNames:x}))}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/customeditors.min",["kendo.core.min","kendo.popup.min","kendo.calendar.min","kendo.listview.min","spreadsheet/sheet.min"],e)}(function(){!function(e){"use strict";var t,n,i;e.support.browser.msie&&e.support.browser.version<9||(t=e.jQuery,n={},i=e.spreadsheet.registerEditor=function(e,t){n[e]=t},e.spreadsheet.Sheet.prototype.activeCellCustomEditor=function(){var e,t,i,r=this.activeCell().first();if(this.range(r).enable())return e=this.validation(r),t=this._properties.get("editor",this._grid.cellRefIndex(r)),null!=t?i=n[t]:e&&e.showButton&&(t="_validation_"+e.dataType,i=n[t]),"function"==typeof i&&(i=n[t]=i()),i},i("_validation_date",function(){function n(){o||(o=t("<div>").kendoCalendar(),s=t("<div>").kendoPopup(),o.appendTo(s),o=o.getKendoCalendar(),s=s.getKendoPopup(),o.bind("change",function(){s.close();var t=o.value();r.range.format()||r.range.format("yyyy-mm-dd"),r.callback(e.spreadsheet.dateToNumber(t))})),s.setOptions({anchor:r.view.element.find(".k-spreadsheet-editor-button")})}function i(){var t,i,a,l;n(),t=r.range.value(),o.value(null!=t?e.spreadsheet.numberToDate(t):null),i=r.validation,i?(a=e.ui.Calendar.fn.options.min,l=e.ui.Calendar.fn.options.max,/^(?:greaterThan|between)/.test(i.comparerType)&&(a=e.spreadsheet.numberToDate(i.from.value)),"between"==i.comparerType&&(l=e.spreadsheet.numberToDate(i.to.value)),"lessThan"==i.comparerType&&(l=e.spreadsheet.numberToDate(i.from.value)),o.setOptions({disableDates:function(t){var n=i.from?0|i.from.value:0,r=i.to?0|i.to.value:0;return t=0|e.spreadsheet.dateToNumber(t),!e.spreadsheet.validation.validationComparers[i.comparerType](t,n,r)},min:a,max:l})):o.setOptions({disableDates:null,min:null,max:null}),s.open()}var r,o,s;return{edit:function(e){r=e,i()},icon:"k-i-calendar"}}),i("_validation_list",function(){function n(){o||(o=t("<ul class='k-list k-reset'/>").kendoStaticList({template:"#:value#",selectable:!0,autoBind:!1}),s=t("<div>").kendoPopup(),o.appendTo(s),s=s.getKendoPopup(),o=o.getKendoStaticList(),o.bind("change",function(){s.close();var e=o.value()[0];e&&r.callback(e.value)})),s.setOptions({anchor:r.view.element.find(".k-spreadsheet-editor-button")})}function i(){var t,i,a,l;n(),t=r.validation.from.value,i=[],a=function(e){i.push({value:e})},t instanceof e.spreadsheet.calc.runtime.Matrix?t.each(a):(t+"").split(/\s*,\s*/).forEach(a),l=new e.data.DataSource({data:i}),o.setDataSource(l),l.read(),s.open()}var r,o,s;return{edit:function(e){r=e,i()},icon:"k-i-arrow-60-down"}}))}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/grid.min",["kendo.core.min","spreadsheet/references.min"],e)}(function(){!function(e){var t,n,i,r,o,s;e.support.browser.msie&&e.support.browser.version<9||(t=e.spreadsheet.CellRef,n=e.spreadsheet.RangeRef,i=e.spreadsheet.UnionRef,r=e.Class.extend({init:function(e,t,n,i){this.left=e,this.top=t,this.width=n,this.height=i,this.right=this.left+this.width,this.bottom=this.top+this.height},offset:function(e,t){return new r(this.left+e,this.top+t,this.width,this.height)},resize:function(e,t){return new r(this.left,this.top,this.width+e,this.height+t)},intersects:function(e,t){return e instanceof r?this.intersectsRect(e):this.left<e&&e<this.left+this.width&&this.top<t&&t<this.top+this.height},intersectsRect:function(e){var t=this;return t.left<=e.right&&e.left<=t.right&&t.top<=e.bottom&&e.top<=t.bottom},toDiv:function(t){return e.dom.element("div",{className:t,style:{width:this.width+"px",height:this.height+"px",top:this.top+"px",left:this.left+"px"}})}}),o=e.Class.extend({init:function(e,t,n,i,r,o){this.rowCount=n,this.columnCount=i,this._columns=t,this._rows=e,this._headerHeight=r,this._headerWidth=o},isAxis:function(e){var t,n;return e=e.toRangeRef(),t=e.topLeft,n=e.bottomRight,0===t.row&&n.row===this.rowCount-1||0===t.col&&n.col===this.columnCount-1},width:function(e,t){return this._columns.sum(e,t)},height:function(e,t){return this._rows.sum(e,t)},totalHeight:function(){return this._rows.total+this._headerHeight},totalWidth:function(){return this._columns.total+this._headerWidth},index:function(e,t){return t*this.rowCount+e},cellRef:function(e){return new t(e%this.rowCount,e/this.rowCount>>0)},rowRef:function(e){return new n(new t(e,0),new t(e,this.columnCount-1))},colRef:function(e){return new n(new t(0,e),new t(this.rowCount-1,e))},cellRefIndex:function(e){return this.index(e.row,e.col)},normalize:function(e){return e instanceof n?new n(this.normalize(e.topLeft),this.normalize(e.bottomRight)).setSheet(e.sheet,e.hasSheet()):e instanceof i?e.map(function(e){return this.normalize(e)},this):(e instanceof t&&(e=e.clone(),e.col=Math.max(0,Math.min(this.columnCount-1,e.col)),e.row=Math.max(0,Math.min(this.rowCount-1,e.row))),e)},rectangle:function(e){var t=this.normalize(e.topLeft),n=this.normalize(e.bottomRight);return new r(this.width(0,t.col-1),this.height(0,t.row-1),this.width(t.col,n.col),this.height(t.row,n.row))},pane:function(t){return new s(new e.spreadsheet.PaneAxis(this._rows,t.row,t.rowCount,this._headerHeight),new e.spreadsheet.PaneAxis(this._columns,t.column,t.columnCount,this._headerWidth),this)},rangeDimensions:function(e){return{rows:this._rows.values.iterator(e.topLeft.row,e.bottomRight.row),columns:this._columns.values.iterator(e.topLeft.col,e.bottomRight.col)}},forEach:function(e,n){var i,r,o=this.normalize(e.topLeft),s=this.normalize(e.bottomRight);for(i=o.col;i<=s.col;i++)for(r=o.row;r<=s.row;r++)n(new t(r,i))},trim:function(e,i){var r,o,s,a,l,u=this.normalize(e.topLeft),c=this.normalize(e.bottomRight),h=u.row,d=u.col;for(r=u.col;r<=c.col;r++)o=this.index(u.row,r),s=this.index(c.row,r),a=i.tree.intersecting(o,s),a.length&&(l=this.cellRef(a[a.length-1].end),h=Math.max(h,l.row),d=r);return new n(e.topLeft,new t(Math.min(h,e.bottomRight.row),d))}}),s=e.Class.extend({init:function(e,t,n){this.rows=e,this.columns=t,this._grid=n,this.headerHeight=e.headerSize,this.headerWidth=t.headerSize,this.hasRowHeader=t.hasHeader,this.hasColumnHeader=e.hasHeader},refresh:function(e,t){var n,i;this.columns.viewSize(e),this.rows.viewSize(t),n=this.columns.paneSegment(),i=this.rows.paneSegment(),this.left=n.offset,this.top=i.offset,this.right=n.offset+n.length,this.bottom=i.offset+i.length,this.style={top:i.offset+"px",left:n.offset+"px",height:i.length+"px",width:n.length+"px"}},view:function(e,i){var r=this.rows.visible(i),o=this.columns.visible(e);return{rows:r,columns:o,rowOffset:r.offset,columnOffset:o.offset,mergedCellLeft:o.start,mergedCellTop:r.start,ref:new n(new t(r.values.start,o.values.start),new t(r.values.end,o.values.end))}},contains:function(e){return this.rows.contains(e.topLeft.row,e.bottomRight.row)&&this.columns.contains(e.topLeft.col,e.bottomRight.col)},index:function(e,t){return this._grid.index(e,t)},boundingRectangle:function(e){return this._grid.rectangle(e)},cellRefIndex:function(e){return this._grid.cellRefIndex(e)},scrollBoundaries:function(e){var t=this.boundingRectangle(e),n={top:Math.max(0,t.top-this.top+(this.hasColumnHeader?0:this.headerHeight)),left:Math.max(0,t.left-this.left+(this.hasRowHeader?0:this.headerWidth)),right:t.right-this.columns._viewSize+this.headerWidth,bottom:t.bottom-this.rows._viewSize+this.headerHeight},i=this.columns.defaultValue/2,r=this.rows.defaultValue/2;return n.scrollTop=n.top-r,n.scrollBottom=n.bottom+r,n.scrollLeft=n.left-i,n.scrollRight=n.right+i,n}}),e.spreadsheet.Grid=o,e.spreadsheet.PaneGrid=s,e.spreadsheet.Rectangle=r)}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/axis.min",["kendo.core.min"],e)}(function(){!function(e){var t,n;e.support.browser.msie&&e.support.browser.version<9||(t=e.Class.extend({init:function(t,n){this._value=n,this._count=t,this.values=new e.spreadsheet.RangeList(0,t-1,n),this._hidden=new e.spreadsheet.RangeList(0,t-1,0),this.scrollBarSize=e.support.scrollbar(),this._refresh()},adjust:function(e,t){t<0?(this.values.copy(e-t,this._count-1,e),this._hidden.copy(e-t,this._count-1,e)):(this.values.copy(e,this._count,e+t),this._hidden.copy(e,this._count,e+t),this.values.value(e,e+t-1,this._value),this._hidden.value(e,e+t-1,0)),this._refresh()},toJSON:function(e,t){var n,i,r,o,s,a=[],l=this.values.iterator(0,this._count-1);for(n=0;n<this._count;n++)i=l.at(n),r=this._hidden.value(n,n),(i!==this._value||r)&&(o=t[n],void 0===o&&(o=a.length,s={index:n},s[e]=i,r&&(s.hidden=r),a.push(s),t[n]=o));return a},fromJSON:function(e,t){var n,i,r,o;for(n=0;n<t.length;n++)i=t[n],r=i.index,void 0===r&&(r=n),o=i[e],0===o?(this._hidden.value(r,r,i.hidden||this._value),this.value(r,r,0)):this.value(r,r,o)},hide:function(e){if(!this.hidden(e)){var t=this.value(e,e);this._hidden.value(e,e,t),this.value(e,e,0)}},hidden:function(e){return 0!==this._hidden.value(e,e)},includesHidden:function(e,t){return this._hidden.intersecting(e,t).length>1},nextVisible:function(e){for(var t=this._count-1,n=e;++n<=t;)if(!this.hidden(n))return n;return e},nextPage:function(e,t){return this.index(this.sum(0,e-1)+t)},prevPage:function(e,t){return this.index(this.sum(0,e)-t)},firstVisible:function(){var e=this._hidden.first();return 0===e.value?0:e.end+1},lastVisible:function(){var e=this._hidden.last();return 0===e.value?this._count-1:e.start-1},prevVisible:function(e){for(var t=e;--t>=0;)if(!this.hidden(t))return t;return e},unhide:function(e){if(this.hidden(e)){var t=this._hidden.value(e,e);this._hidden.value(e,e,0),this.value(e,e,t)}},value:function(e,t,n){return void 0===n?this.values.iterator(e,t).at(0):(this.values.value(e,t,n),void this._refresh())},sum:function(e,t){var n,i=this.values.iterator(e,t),r=0;for(n=e;n<=t;n++)r+=i.at(n);return r},locate:function(e,t,n){var i,r,o=this.values.iterator(e,t),s=0;for(i=e;i<=t;i++)if(s+=o.at(i),r=n(s))return i;return null},visible:function(e,t){var n,i,r,o,s,a,l=null,u=null,c=!1;return t>=this.total+this.scrollBarSize&&(c=!0),n=this._pixelValues.intersecting(e,t),l=n[0],u=n[n.length-1],l?(i=e-l.start,r=(i/l.value.value>>0)+l.value.start,o=i-(r-l.value.start)*l.value.value,s=t-u.start,a=(s/u.value.value>>0)+u.value.start,a>u.value.end&&(a=u.value.end),c&&(o+=u.value.value-(s-(a-u.value.start)*u.value.value)),o=Math.min(-o,0),{values:this.values.iterator(r,a),offset:o}):{values:this.values.iterator(0,0),offset:0}},index:function(e){for(var t=0,n=this.values.iterator(0,this._count-1),i=n.at(0);i<e&&t<this._count-1;)i+=n.at(++t);return t},indexVisible:function(e){var t=this.index(e);return this.hidden(t)&&(t=this.prevVisible(t)),t},_refresh:function(){var t=0;this._pixelValues=this.values.map(function(n){var i,r=t;return t+=(n.end-n.start+1)*n.value,i=t-1,new e.spreadsheet.ValueRange(r,i,n)}),this.total=t},getState:function(){return{values:this.values.getState(),hidden:this._hidden.getState()}},setState:function(e){this.values.setState(e.values),this._hidden.setState(e.hidden),this._refresh()}}),n=e.Class.extend({init:function(e,t,n,i){this._axis=e,this._start=t,this._count=n,this.hasHeader=0===t,this.headerSize=i,this.defaultValue=e._value,this.frozen=n>0},viewSize:function(e){this._viewSize=e},sum:function(e,t){return this._axis.sum(e,t-1)},start:function(){return this.sum(0,this._start)},size:function(){return this.sum(this._start,this._start+this._count)},index:function(e,t){return this._axis.index(e+(this.frozen?0:t)-this.headerSize)},indexVisible:function(e,t){return this._axis.indexVisible(e+(this.frozen?0:t)-this.headerSize)},paneSegment:function(){var e,t=this.start();return this.hasHeader||(t+=this.headerSize),this.frozen?(e=this.size(),this.hasHeader?e+=this.headerSize:e-=this.headerSize):e=this._viewSize-t,{offset:t,length:e}},visible:function(e){var t,n,i=this.start();return this.frozen?(t=this.size(),this.hasHeader||(t-=this.headerSize)):(t=this._viewSize-i-this.headerSize,i+=e),n=this._axis.visible(i,i+t-1),this.frozen&&(n.offset=0),n.start=i,this.hasHeader&&(n.offset+=this.headerSize,n.start-=this.headerSize),n},contains:function(e,t){return this.frozen?!(e>this._start+this._count)&&!(t<this._start):t>=this._start}}),e.spreadsheet.Axis=t,e.spreadsheet.PaneAxis=n)}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/filter.min",["kendo.core.min","kendo.data.min"],e)}(function(){!function(e){function t(e){var t=e.getMonth()+1;return t>=1&&t<=3?1:t>=4&&t<=6?2:t>=7&&t<=9?3:4}function n(t,n){if(t instanceof Date){var i=e.date.today();return i.setFullYear(i.getFullYear()+n),i.getFullYear()===t.getFullYear()}return!1}function i(t,n){if(t instanceof Date){var i=e.date.firstDayOfMonth(e.date.today());return i.setMonth(i.getMonth()+n,1),i.getTime()===e.date.firstDayOfMonth(t).getTime()}return!1}function r(t,n){var i,r;return n instanceof Date&&(i=e.date.dayOfWeek(e.date.getDate(t),1),r=e.date.dayOfWeek(e.date.getDate(n),1),i.getTime()===r.getTime())}if(!(e.support.browser.msie&&e.support.browser.version<9)){var o=e.spreadsheet.Filter=e.Class.extend({prepare:function(){},value:function(e){return e.value},matches:function(){throw Error("The 'matches' method is not implemented.")},toJSON:function(){throw Error("The 'toJSON' method is not implemented.")}});o.create=function(t){var n,i=t.filter;if(!i)throw Error("Filter type not specified.");if(n=e.spreadsheet[i.charAt(0).toUpperCase()+i.substring(1)+"Filter"],!n)throw Error("Filter type not recognized.");return new n(t)},e.spreadsheet.ValueFilter=o.extend({_values:[],_dates:[],_blanks:!1,init:function(e){void 0!==e.values&&(this._values=e.values),void 0!==e.blanks&&(this._blanks=e.blanks),void 0!==e.dates&&(this._dates=e.dates)},value:function(t){var n,i=t.value;return this._dates.length>0&&t.format&&"number"==typeof i&&(n=e.spreadsheet.formatting.type(i,t.format),"date"===n&&(i=e.spreadsheet.numberToDate(i))),i},matches:function(e){return null===e||void 0===e?this._blanks:e instanceof Date?this._dates.some(function(t){return!(t.year!==e.getFullYear()||void 0!==t.month&&t.month!==e.getMonth()||void 0!==t.day&&t.day!==e.getDate()||void 0!==t.hours&&t.hours!==e.getHours()||void 0!==t.minutes&&t.minutes!==e.getMinutes()||void 0!==t.seconds&&t.seconds!==e.getSeconds())}):this._values.indexOf(e)>=0},toJSON:function(){return{filter:"value",blanks:this._blanks,values:this._values.slice(0)}}}),e.spreadsheet.CustomFilter=o.extend({_logic:"and",init:function(t){if(void 0!==t.logic&&(this._logic=t.logic),void 0===t.criteria)throw Error("Must specify criteria.");this._criteria=t.criteria;var n=e.data.Query.filterExpr({logic:this._logic,filters:this._criteria}).expression;this._matches=Function("d","return "+n)},matches:function(e){return null!==e&&this._matches(e)},value:function(t){var n=t.value,i=this._criteria[0].value,r=i instanceof Date?"date":typeof i,o=typeof n;return t.format&&(o=e.spreadsheet.formatting.type(n,t.format)),o!=r?"string"==r&&(t.format&&(n=e.spreadsheet.formatting.text(n,t.format)),n+=""):"date"==o&&(n=e.spreadsheet.numberToDate(n)),n},toJSON:function(){return{filter:"custom",logic:this._logic,criteria:this._criteria}}}),e.spreadsheet.TopFilter=o.extend({init:function(e){this._type=e.type,this._value=e.value,this._values=[]},prepare:function(e){var t,n=e.map(this.value).sort().filter(function(e,t,n){return 0===t||e!==n[t-1]});n.sort("topNumber"===this._type||"topPercent"==this._type?function(e,t){return t-e}:function(e,t){return e-t}),t=this._value,"topPercent"!==this._type&&"bottomPercent"!==this._type||(t=n.length*t/100>>0),this._values=n.slice(0,t)},matches:function(e){return this._values.indexOf(e)>=0},toJSON:function(){return{filter:"top",type:this._type,value:this._value}}}),e.spreadsheet.DynamicFilter=o.extend({init:function(e){if(this._type=e.type,this._predicate=this[e.type],"function"!=typeof this._predicate)throw Error("DynamicFilter type '"+e.type+"' not recognized.")},value:function(t){var n,i=t.value;return t.format&&(n=e.spreadsheet.formatting.type(i,t.format),"date"===n&&(i=e.spreadsheet.numberToDate(i))),i},prepare:function(e){var t,n,i=0,r=0;for(t=0;t<e.length;t++)n=this.value(e[t]),"number"==typeof n&&(i+=n,r++);this._average=r>0?i/r:0},matches:function(e){return this._predicate(e)},aboveAverage:function(t){return t instanceof Date&&(t=e.spreadsheet.dateToNumber(t)),"number"==typeof t&&t>this._average},belowAverage:function(t){return t instanceof Date&&(t=e.spreadsheet.dateToNumber(t)),"number"==typeof t&&t<this._average},tomorrow:function(t){if(t instanceof Date){var n=e.date.addDays(e.date.today(),1);return e.date.getDate(t).getTime()===n.getTime()}return!1},today:function(t){return t instanceof Date&&e.date.isToday(t)},yesterday:function(t){if(t instanceof Date){var n=e.date.addDays(e.date.today(),-1);return e.date.getDate(t).getTime()===n.getTime()}return!1},nextWeek:function(t){return r(e.date.addDays(e.date.today(),7),t)},thisWeek:function(t){return r(e.date.today(),t)},lastWeek:function(t){
return r(e.date.addDays(e.date.today(),-7),t)},nextMonth:function(e){return i(e,1)},thisMonth:function(e){return i(e,0)},lastMonth:function(e){return i(e,-1)},nextQuarter:function(n){var i,r;return n instanceof Date&&(i=e.date.today(),r=t(n)-t(i),1===r&&i.getFullYear()===n.getFullYear()||r==-3&&i.getFullYear()+1===n.getFullYear())},thisQuarter:function(n){var i,r;return n instanceof Date&&(i=e.date.today(),r=t(n)-t(i),0===r&&i.getFullYear()===n.getFullYear())},lastQuarter:function(n){var i,r;return n instanceof Date&&(i=e.date.today(),r=t(i)-t(n),1===r&&i.getFullYear()===n.getFullYear()||r==-3&&i.getFullYear()-1===n.getFullYear())},nextYear:function(e){return n(e,1)},thisYear:function(e){return n(e,0)},lastYear:function(e){return n(e,-1)},yearToDate:function(t){if(t instanceof Date){var n=e.date.today();return t.getFullYear()===n.getFullYear()&&t<=n}return!1},toJSON:function(){return{filter:"dynamic",type:this._type}}}),[1,2,3,4].forEach(function(n){e.spreadsheet.DynamicFilter.prototype["quarter"+n]=function(e){return e instanceof Date&&t(e)===n}}),e.cultures["en-US"].calendar.months.names.forEach(function(t,n){e.spreadsheet.DynamicFilter.prototype[t.toLowerCase()]=function(e){return e instanceof Date&&e.getMonth()===n}})}}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/sorter.min",["kendo.core.min"],e)}(function(){!function(e){if(!(e.support.browser.msie&&e.support.browser.version<9)){var t=e.Class.extend({init:function(e,t){this._grid=e,this._lists=t},indices:function(e,n,i,r){var o=t.ascendingComparer;return i===!1&&(o=t.descendingComparer),n.sortedIndices(this._grid.cellRefIndex(e.topLeft),this._grid.cellRefIndex(e.bottomRight),o,r)},sortBy:function(e,t,n,i,r){var o,s,a,l,u=this.indices(e.toColumn(t),n,i,r);for(o=e.topLeft.col;o<=e.bottomRight.col;o++)for(s=this._grid.index(e.topLeft.row,o),a=this._grid.index(e.bottomRight.row,o),l=0;l<this._lists.length;l++)s<this._lists[l].lastRangeStart()&&this._lists[l].sort(s,a,u);return u}});t.ascendingComparer=function(t,n){var i,r;if(null===t&&null===n)return 0;if(null===t)return 1;if(null===n)return-1;if(i=typeof t,r=typeof n,"number"===i)return"number"===r?t-n:-1;if("string"===i)switch(r){case"number":return 1;case"string":return t.localeCompare(n);default:return-1}if("boolean"===i)switch(r){case"number":return 1;case"string":return 1;case"boolean":return t-n;default:return-1}if(t instanceof e.spreadsheet.calc.runtime.CalcError)return n instanceof e.spreadsheet.calc.runtime.CalcError?0:1;throw Error("Cannot compare "+t+" and "+n)},t.descendingComparer=function(e,n){return null===e&&null===n?0:null===e?1:null===n?-1:t.ascendingComparer(n,e)},e.spreadsheet.Sorter=t}}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/numformat.min",["spreadsheet/calc.min","kendo.dom.min","util/main.min"],e)}(function(){"use strict";function e(e){function t(){var t=e.skip(d);if(t)return t[1].toLowerCase()}function n(){var t,n=e.skip(f);if(n&&(t=parseFloat(n[2]),!isNaN(t)))return{op:n[1],value:t,custom:!0}}function i(){for(var t,n=[],i=null;!e.eof()&&(t=o());)"date"==t.type?i&&/^(el)?time$/.test(i.type)&&"h"==i.part&&"m"==t.part&&t.format<3&&(t.type="time"):/^(el)?time$/.test(t.type)&&"s"==t.part&&i&&"date"==i.type&&"m"==i.part&&i.format<3&&(i.type="time"),/^(?:str|space|fill)$/.test(t.type)||(i=t),n.push(t);return n}function r(t){if("date"!=t.type||"m"==t.part&&t.format<3){var n=e.skip(/^\.(0+)/);n&&(t.fraction=n[1].length,"date"==t.type&&(t.type="time"))}return t}function o(){var t,n;if(n=e.skip(/^([#0?]+)(?:,([#0?]+))+/))return{type:"digit",sep:!0,format:n[1]+n[2],decimal:u};if(n=e.skip(/^[#0?]+/))return{type:"digit",sep:!1,format:n[0],decimal:u};if(n=e.skip(/^(e)([+-])/i))return{type:"exp",ch:n[1],sign:n[2]};if(n=e.skip(/^(d{1,4}|m{1,5}|yyyy|yy)/i))return n=n[1].toLowerCase(),r({type:"date",part:n.charAt(0),format:n.length});if(n=e.skip(/^(hh?|ss?)/i))return n=n[1].toLowerCase(),r({type:"time",part:n.charAt(0),format:n.length});if(n=e.skip(/^\[(hh?|mm?|ss?)\]/i))return n=n[1].toLowerCase(),r({type:"eltime",part:n.charAt(0),format:n.length});if(n=e.skip(/^(a[.]?m[.]?\/p[.]?m[.]?|a\/p)/i))return n=n[1].split("/"),{type:"ampm",am:n[0],pm:n[1]};switch(t=e.next()){case";":return null;case"\\":return{type:"str",value:e.next()};case'"':return{type:"str",value:e.readEscaped(t)};case"@":return{type:"text"};case"_":return{type:"space",value:e.next()};case"*":return{type:"fill",value:e.next()};case".":return e.lookingAt(/^\s*[#0?]/)?(u=!0,{type:"dec"}):{type:"str",value:"."};case"%":return{type:"percent"};case",":return{type:"comma"}}return{type:"str",value:t}}function s(){u=!1;var e=t(),r=n();return!e&&r&&(e=t()),{color:e,cond:r,body:i()}}var a,l,u,h;for(e=c.InputStream(e),a=[],l=!1;!e.eof();)h=s(),a.push(h),h.cond&&(l=!0);return l||(1==a.length?a[0].cond="num":2==a.length?(a[0].cond={op:">=",value:0},a[1].cond={op:"<",value:0}):a.length>=3&&(a[0].cond={op:">",value:0},a[1].cond={op:"<",value:0},a[2].cond={op:"=",value:0},a.length>3&&(a[3].cond="text",a=a.slice(0,4)))),a}function t(e){function t(e){var t="";return e.color&&(t+="["+e.color+"]"),e.cond&&"text"!=e.cond&&"num"!=e.cond&&(t+="["+e.cond.op+e.cond.value+"]"),t+=e.body.map(i).join("")}function n(e,t){return t.fraction&&(e+="."+o("",t.fraction,"0")),e}function i(e){return"digit"==e.type?e.sep?e.format.charAt(0)+","+e.format.substr(1):e.format:"exp"==e.type?e.ch+e.sign:"date"==e.type||"time"==e.type?n(o("",e.format,e.part),e):"eltime"==e.type?n("["+o("",e.format,e.part)+"]",e):"ampm"==e.type?e.am+"/"+e.pm:"str"==e.type?JSON.stringify(e.value):"text"==e.type?"@":"space"==e.type?"_"+e.value:"fill"==e.type?"*"+e.value:"dec"==e.type?".":"percent"==e.type?"%":"comma"==e.type?",":void 0}return e.map(t).join(";")}function n(e,t){e.forEach(function(e){var n,i,r,s,a,l=t;if("text"!=e.cond){for(n=e.body,i=!1,r=n.length;0!==l&&--r>=0;)if(s=n[r],"digit"==s.type){if(s.decimal&&(i=!0,l>0?s.format+=o("",l,"0"):l<0&&(a=s.format.length,s.format=s.format.substr(0,a+l),l+=a-s.format.length),0===s.format.length))for(n.splice(r,1);--r>=0;){if(s=n[r],"digit"==s.type&&s.decimal){++r;break}if("dec"==s.type){n.splice(r,1);break}}if(l>0)break}!i&&l>0&&n.splice(r+1,0,{type:"dec"},{type:"digit",sep:!1,decimal:!0,format:o("",l,"0")})}})}function i(e){var t=0;return{next:function(){return e[t++]},eof:function(){return t>=e.length},ahead:function(n,i){if(t+n<=e.length){var r=i.apply(null,e.slice(t,t+n));return r&&(t+=n),r}},restart:function(){t=0}}}function r(e){function t(e,t){("digit"==e.type&&"comma"==t.type||"comma"==e.type&&e.hidden&&"comma"==t.type)&&(t.hidden=!0,h++)}var n,r,o=i(e.body),s=!1,a=!1,l=!1,u=0,c=/[\$\xA2-\xA5\u058F\u060B\u09F2\u09F3\u09FB\u0AF1\u0BF9\u0E3F\u17DB\u20A0-\u20BD\uA838\uFDFC\uFE69\uFF04\uFFE0\uFFE1\uFFE5\uFFE6]/,h=0,d="",f=!1,p=0,m=[],g=[],v=e.cond,b="";for("text"==v?b="if (typeof value == 'string' || value instanceof kendo.spreadsheet.CalcError) { ":"num"==v?b="if (typeof value == 'number') { ":v&&(n="="==v.op?"==":v.op,b="if (typeof value == 'number' && value "+n+" "+v.value+") { ",v.custom||(d+="value = Math.abs(value); ")),e.color&&(d+="result.color = "+JSON.stringify(e.color)+"; ");!o.eof();)o.ahead(2,t),r=o.next(),"percent"==r.type?u++:"digit"==r.type?r.decimal?(p+=r.format.length,g.push(r.format)):(m.push(r.format),r.sep&&(f=!0)):"time"==r.type?a=!0:"date"==r.type?s=!0:"ampm"==r.type&&(l=a=!0);for(u>0&&(d+="value *= "+Math.pow(100,u)+"; "),h>0&&(d+="value /= "+Math.pow(1e3,h)+"; "),m.length&&(d+="var intPart = runtime.formatInt(culture, value, "+JSON.stringify(m)+", "+p+", "+f+"); ",d+="var isNegative = parseInt(intPart[0]) < 0;"),g.length&&(d+="var decPart = runtime.formatDec(value, "+JSON.stringify(g)+", "+p+"); "),(m.length||g.length)&&(d+="type = 'number'; "),s&&(d+="var date = runtime.unpackDate(value); "),a&&(d+="var time = runtime.unpackTime(value); "),(s||a)&&(d+="type = 'date'; "),(u>0||h>0||m.length||g.length||s||a)&&(b||(b="if (typeof value == 'number') { ")),o.restart(),d+="var matchedCurrency = false;";!o.eof();)r=o.next(),"dec"==r.type?d+="output += culture.numberFormat['.']; ":"comma"!=r.type||r.hidden?"percent"==r.type?(d+="type = 'percent'; ",d+="output += culture.numberFormat.percent.symbol; "):"str"==r.type?(c.test(r.value)&&(d+="type = 'currency'; ",d+="if (isNegative) { output += '-'; matchedCurrency = true; }"),d+="output += "+JSON.stringify(r.value)+"; "):"text"==r.type?(d+="type = 'text'; ",d+="output += value; "):"space"==r.type?(d+="if (output) result.body.push(output); ",d+="output = ''; ",d+="result.body.push({ type: 'space', value: "+JSON.stringify(r.value)+" }); "):"fill"==r.type?d+="output += runtime.fill("+JSON.stringify(r.value)+"); ":"digit"==r.type?(d+="if (isNegative && intPart[0] && matchedCurrency) {intPart[0] = intPart[0].replace('-', '');}",d+="output += "+(r.decimal?"decPart":"intPart")+".shift(); "):"date"==r.type?d+="output += runtime.date(culture, date, "+JSON.stringify(r.part)+", "+r.format+"); ":"time"==r.type?d+="output += runtime.time(time, "+JSON.stringify(r.part)+", "+r.format+", "+l+", "+r.fraction+"); ":"eltime"==r.type?d+="output += runtime.eltime(value, "+JSON.stringify(r.part)+", "+r.format+", "+r.fraction+"); ":"ampm"==r.type&&(d+="output += time.hours < 12 ? "+JSON.stringify(r.am)+" : "+JSON.stringify(r.pm)+"; "):d+="output += ','; ";return d+="if (output) result.body.push(output); ",d+="result.type = type; ",d+="return result; ",b&&(d=b+d+"}"),d}function o(e,t,n){for(e+="";e.length<t;)e=n+e;return e}function s(e,t,n){for(e+="";e.length<t;)e+=n;return e}function a(e){var t,n,i,r,a=(e+"").toLowerCase(),l=a.indexOf(".");return l<0?(l=a.indexOf("e"),l<0?(t=a,n=""):(t=a.substr(0,l),n=a.substr(l))):(t=a.substr(0,l),n=a.substr(l+1)),(i=/(\d*)e([-+]?\d+)/.exec(n))&&(r=parseInt(i[2],10),r>=0?(n=s(i[1],r,"0"),t+=n.substr(0,r),n=n.substr(r)):(t=o(t,-r,"0"),n=t.substr(r)+i[1],t=t.substr(0,t.length+r))),{intpart:t||"0",decpart:n}}function l(e){var t,n,i=e.body,r="";for(t=0;t<i.length;++t)n=i[t],"string"==typeof n?r+=n:"space"==n.type&&(r+=" ");return r}var u,c,h,d,f,p,m,g,v;kendo.support.browser.msie&&kendo.support.browser.version<9||(u=kendo.util,c=kendo.spreadsheet.calc,h=kendo.dom,d=/^\[(black|green|white|blue|magenta|yellow|cyan|red)\]/i,f=/^\[(<=|>=|<>|<|>|=)(-?[0-9.]+)\]/,p=r({cond:"text",body:[{type:"text"}]}),m=u.memoize(function(t){var n=e(t),i=n.map(r);return i.push(p),i=i.join("\n"),i="'use strict'; return function(value, culture){ if (!culture) culture = kendo.culture(); var output = '', type = null, result = { body: [] }; "+i+"; return result; };",Function("runtime",i)(v)}),g=u.memoize(function(t){function n(e,t){return t.fraction&&(e+=o("",Math.max(t.fraction,3),"f")),e}function i(e){if("digit"==e.type)return e.sep?e.format.charAt(0)+","+e.format.substr(1):e.format;if("exp"==e.type)return e.ch+e.sign;if("date"==e.type||"time"==e.type){var t=e.part;return"date"==e.type&&/^m/.test(t)?t="M":"time"==e.type&&/^h/.test(t)&&(c||(t=t.toUpperCase())),n(o("",e.format,t),e)}return"ampm"==e.type?"tt":"str"==e.type?e.value:"space"==e.type?" ":"dec"==e.type?".":"percent"==e.type?"%":"comma"==e.type?",":""}var r,s,a,l=e(t),u=!1,c=!1;for(s=0;s<l.length;++s){for(r=l[s],a=0;a<r.body.length;++a)/^(?:date|time|ampm)$/.test(r.body[a].type)&&(u=!0,"ampm"==r.body[a].type&&(c=!0));if(u)break}return u?r.body.map(i).join(""):null}),v={unpackDate:c.runtime.unpackDate,unpackTime:c.runtime.unpackTime,date:function(e,t,n,i){switch(n){case"d":switch(i){case 1:return t.date;case 2:return o(t.date,2,"0");case 3:return e.calendars.standard.days.namesAbbr[t.day];case 4:return e.calendars.standard.days.names[t.day]}break;case"m":switch(i){case 1:return t.month+1;case 2:return o(t.month+1,2,"0");case 3:return e.calendars.standard.months.namesAbbr[t.month];case 4:return e.calendars.standard.months.names[t.month];case 5:return e.calendars.standard.months.names[t.month].charAt(0)}break;case"y":switch(i){case 2:return t.year%100;case 4:return t.year}}return"##"},time:function(e,t,n,i,r){var s,a;switch(t){case"h":s=o(i?e.hours%12||12:e.hours,n,"0"),r&&(a=(e.minutes+(e.seconds+e.milliseconds/1e3)/60)/60);break;case"m":s=o(e.minutes,n,"0"),r&&(a=(e.seconds+e.milliseconds/1e3)/60);break;case"s":s=o(e.seconds,n,"0"),r&&(a=e.milliseconds/1e3)}return a&&(s+=v.toFixed(a,r).replace(/^0+/,"")),s},eltime:function(e,t,n,i){var r,s;switch(t){case"h":r=24*e;break;case"m":r=24*e*60;break;case"s":r=24*e*60*60}return i&&(s=r-(0|r)),r=o(0|r,n,"0"),s&&(r+=v.toFixed(s,i).replace(/^0+/,"")),r},fill:function(e){return e},formatInt:function(e,t,n,i,r){function o(t,n){r&&c&&c%3===0&&/^[0-9]$/.test(t)&&(h=e.numberFormat[","]+h),n&&"-"===t&&(a=!0,t="0"),h=t+h,c++}var s,a,l,u,c,h,d,f,p,m;for(t=v.toFixed(t,i).replace(/\..*$/,""),s=n[n.length-1],i>0&&"0"!=s[n.length-1]&&("0"===t?t="":"-0"===t&&(t="-")),a=!1,l=t.length-1,u=[],c=0,d=n.length;--d>=0;){for(f=n[d],h="",p=f.length;--p>=0;)m=f.charAt(p),l<0?"0"==m?o("0"):"?"==m&&o(" "):("0"==t&&"?"==m?o(" "):"0"==m?o(t.charAt(l),!0):o(t.charAt(l)),l--);if(0===d)for(;l>=0;)o(t.charAt(l--));u.unshift(h)}return a&&(u[0]="-"+u[0]),u},formatDec:function(e,t,n){var i,r,o,s,a,l,u,c;for(e=v.toFixed(e,n),i=e.indexOf("."),e=i>=0?e.substr(i+1).replace(/0+$/,""):"",r=0,o=[],s=0;s<t.length;++s){for(a=t[s],l="",u=0;u<a.length;++u)c=a.charAt(u),r<e.length?l+=e.charAt(r++):"0"==c?l+="0":"?"==c&&(l+=" ");o.push(l)}return o},toFixed:function(e,t){return function n(e,i){var r,o,s,l;if(!isFinite(e))return"#NUM!";if(e<0)return"-"+n(-e);if(0===t)return Math.round(e)+"";if(e===Math.round(e)&&!/e/i.test(e+""))return e.toFixed(t);if(r=a(e),o=r.intpart,s=r.decpart,s.length<=t){for(;s.length<t;)s+="0";return o+"."+s}return i?o+"."+s.substr(0,t):(l=Math.pow(10,t),n(Math.round(e*l)/l,!0))}(+e.toFixed(14))}},kendo.spreadsheet.formatting={compile:m,parse:e,format:function(e,t,n){var i,r,o,s=m(t)(e,n),a=h.element("span");for(a.__dataType=s.type,i=s.body,s.color&&(a.attr.style={color:s.color}),r=0;r<i.length;++r)o=i[r],"string"==typeof o?a.children.push(h.text(o)):"space"==o.type&&a.children.push(h.element("span",{style:{visibility:"hidden"}},[h.text(o.value)]));return a},text:function(e,t,n){var i=m(t)(e,n);return l(i)},textAndColor:function(e,t,n){var i=m(t)(e,n);return{text:l(i),color:i.color,type:i.type}},type:function(e,t){return m(t)(e).type},adjustDecimals:function(i,r){var o=e(i);return n(o,r),t(o)},makeDateFormat:g})},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/runtime.functions.min",["spreadsheet/runtime.min","util/main.min"],e)}(function(){"use strict";function e(e){return(Math.exp(e)+Math.exp(-e))/2}function t(e){return(Math.exp(e)-Math.exp(-e))/2}function n(n){return t(n)/e(n)}function i(e,t){for(;t;){var n=e%t;e=t,t=n}return e}function r(e,t){return Math.abs(e*t)/i(e,t)}function o(e,t){for(var n,i,r,o=[],s=0,a=e[0];s<e.length;)o.push({matrix:e[s++],pred:q(e[s++])});for(n=0;n<a.height;++n)e:for(i=0;i<a.width;++i){for(s=0;s<o.length;++s)if(r=o[s].matrix.get(n,i),!o[s].pred(null==r||""===r?0:r))continue e;t(n,i)}}function s(e){return e.reduce(function(e,t){return e+t},0)/e.length}function a(e,t,n){return null==n&&(n=s(e)),e.reduce(function(e,t){return e+Math.pow(t-n,2)},0)/t}function l(e,t){return Math.sqrt(a(e,t))}function u(e,t,n){var i=0,r=0,o=null,s=null,a=!1;return e.forEach(function(e){e<t?(i++,o=null==o?e:Math.max(o,e)):e>t?(r++,s=null==s?e:Math.min(s,e)):a=!0}),i||r?a?n?(i+1)/(e.length+1):i/(i+r):((s-t)*u(e,o,n)+(t-o)*u(e,s,n))/(s-o):new A("N/A")}function c(e,t,n){var i,r=0,o=s(e),a=s(t),l=e.length;for(i=0;i<l;++i)r+=(e[i]-o)*(t[i]-a);return r/n}function h(e,t){var n,i,r;return e.sort(y),n=e.length,i=0|t,r=t-i,0===i?e[0]:i>=n?e[n-1]:(--i,e[i]+r*(e[i+1]-e[i]))}function d(e,t){var n=t*(e.length-1)+1;return h(e,n)}function f(e,t){var n=t*(e.length+1);return h(e,n)}function p(e,t,n){var i=[],r=1,o=2,s=4;return function a(t){if(t instanceof T)e.getRefCells(t,!0).forEach(function(e){var t,a=e.value;if(!(n&r&&e.hidden)){if(e.formula){if(t=e.formula.print(e.row,e.col),/^\s*(?:aggregate|subtotal)\s*\(/i.test(t)&&!(n&s))return;"value"in e.formula&&(a=e.formula.value)}n&o&&a instanceof A||("number"==typeof a||a instanceof A)&&i.push(a)}});else if(Array.isArray(t))for(var l=0;l<t.length;++l)a(t[l]);else t instanceof M?t.each(a):"number"==typeof t?i.push(t):t instanceof A&&!(n&o)&&i.push(t)}(t),i}function m(e,t,n,i){var r,o;if(i){for(r=0,o=0;o<=e;++o)r+=U(t,o)*Math.pow(n,o)*Math.pow(1-n,t-o);return r}return U(t,e)*Math.pow(n,e)*Math.pow(1-n,t-e)}function g(e){var t=N(L(e,0,1));return 4==t.day||3==t.day&&F.isLeapYear(e)?53:52}function v(e,t,n){var i=N(e),r=N(t);return n?(31==i.date&&(i.date=30),31==r.date&&(r.date=30)):(1==i.month&&1==r.month&&i.date==I(i.year,1)&&r.date==I(r.year,1)&&(r.date=30),i.date==I(i.year,i.month)?(i.date=30,31==r.date&&(r.date=30)):30==i.date&&31==r.date&&(r.date=30)),360*(r.year-i.year)+30*(r.month-i.month)+(r.date-i.date)}function b(e,t){this.link=e,this.text=t}function w(e){return"number"==typeof e||"boolean"==typeof e||null==e||""===e}function y(e,t){return e===t?0:e<t?-1:1}function _(e,t){return e===t?0:e<t?1:-1}var k,x,C,F,R,S,A,D,E,B,M,T,I,L,N,z,P,H,O,$,V,U,W,j,q;kendo.support.browser.msie&&kendo.support.browser.version<9||(k=kendo.util,x=kendo.spreadsheet,C=x.calc,F=C.runtime,R=F.defineFunction,S=F.defineAlias,A=F.CalcError,D=x.RangeRef,E=x.CellRef,B=x.UnionRef,M=F.Matrix,T=x.Ref,I=F.daysInMonth,L=F.packDate,N=F.unpackDate,z=F.daysInYear,["abs","cos","sin","acos","asin","tan","atan","exp","sqrt"].forEach(function(e){R(e,Math[e]).args([["*n","number"]])}),R("ln",Math.log).args([["*n","number"]]),R("log",function(e,t){return Math.log(e)/Math.log(t)}).args([["*num","number++"],["*base",["or","number++",["null",10]]],["?",["assert","$base != 1","DIV/0"]]]),R("log10",function(e){return Math.log(e)/Math.log(10)}).args([["*num","number++"]]),R("pi",function(){return Math.PI}).args([]),R("sqrtpi",function(e){return Math.sqrt(e*Math.PI)}).args([["*num","number+"]]),R("degrees",function(e){return 180*e/Math.PI%360}).args([["*radians","number"]]),R("radians",function(e){return Math.PI*e/180}).args([["*degrees","number"]]),R("cosh",e).args([["*num","number"]]),R("acosh",function(e){return Math.log(e+Math.sqrt(e-1)*Math.sqrt(e+1))}).args([["*num","number"],["?",["assert","$num >= 1"]]]),R("sinh",t).args([["*num","number"]]),R("asinh",function(e){return Math.log(e+Math.sqrt(e*e+1))}).args([["*num","number"]]),R("sec",function(e){return 1/Math.cos(e)}).args([["*num","number"]]),R("sech",function(t){return 1/e(t)}).args([["*num","number"]]),R("csc",function(e){return 1/Math.sin(e)}).args([["*num","number"]]),R("csch",function(e){return 1/t(e)}).args([["*num","number"]]),R("atan2",function(e,t){return Math.atan(t/e)}).args([["*x","divisor"],["*y","number"]]),R("tanh",n).args([["*num","number"]]),R("atanh",function(e){return Math.log(Math.sqrt(1-e*e)/(1-e))}).args([["*num",["and","number",["(between)",-1,1]]]]),R("cot",function(e){return 1/Math.tan(e)}).args([["*num","divisor"]]),R("coth",function(e){return 1/n(e)}).args([["*num","divisor"]]),R("acot",function(e){return Math.PI/2-Math.atan(e)}).args([["*num","number"]]),R("acoth",function(e){return Math.log((e+1)/(e-1))/2}).args([["*num","number"],["?",["or",["assert","$num < -1"],["assert","$num > 1"]]]]),R("power",function(e,t){return Math.pow(e,t)}).args([["*a","number"],["*b","number"]]),R("mod",function(e,t){return e%t}).args([["*a","number"],["*b","divisor"]]),R("quotient",function(e,t){return Math.floor(e/t)}).args([["*a","number"],["*b","divisor"]]),R("ceiling",function(e,t){return t?t*Math.ceil(e/t):0}).args([["*number","number"],["*significance","number"],["?",["assert","$significance >= 0 || $number < 0"]]]),R("ceiling.precise",function(e,t){return t=Math.abs(t),t?t*Math.ceil(e/t):0}).args([["*number","number"],["*significance",["or","number",["null",1]]]]),S("iso.ceiling","ceiling.precise"),R("ceiling.math",function(e,t,n){return t&&e?(e<0&&(!n&&t<0||n&&t>0)&&(t=-t),t?t*Math.ceil(e/t):0):0}).args([["*number","number"],["*significance",["or","number",["null","$number < 0 ? -1 : 1"]]],["*mode",["or","logical",["null",0]]]]),R("floor",function(e,t){return t?t*Math.floor(e/t):0}).args([["*number","number"],["*significance","number"],["?",["assert","$significance >= 0 || $number < 0"]]]),R("floor.precise",function(e,t){return t=Math.abs(t),t?t*Math.floor(e/t):0}).args([["*number","number"],["*significance",["or","number",["null",1]]]]),R("floor.math",function(e,t,n){return t&&e?(e<0&&(!n&&t<0||n&&t>0)&&(t=-t),t?t*Math.floor(e/t):0):0}).args([["*number","number"],["*significance",["or","number",["null","$number < 0 ? -1 : 1"]]],["*mode",["or","logical",["null",0]]]]),R("int",Math.floor).args([["*number","number"]]),R("mround",function(e,t){return t?t*Math.round(e/t):0}).args([["*number","number"],["*multiple","number"]]),R("round",function(e,t){var n=e<0?-1:1;return n<0&&(e=-e),t=Math.pow(10,t),e*=t,e=Math.round(e),n*e/t}).args([["*number","number"],["*digits","number"]]),R("roundup",function(e,t){return t=Math.pow(10,t),e*=t,e=e<0?Math.floor(e):Math.ceil(e),e/t}).args([["*number","number"],["*digits","number"]]),R("rounddown",function(e,t){return t=Math.pow(10,t),e*=t,e=e<0?Math.ceil(e):Math.floor(e),e/t}).args([["*number","number"],["*digits","number"]]),R("even",function(e){var t=e<0?Math.floor(e):Math.ceil(e);return t%2?t+(t<0?-1:1):t}).args([["*number","number"]]),R("odd",function(e){var t=e<0?Math.floor(e):Math.ceil(e);return t%2?t:t+(t<0?-1:1)}).args([["*number","number"]]),R("sign",function(e){return e<0?-1:e>0?1:0}).args([["*number","number"]]),R("gcd",function(e){var t,n=e[0];for(t=1;t<e.length;++t)n=i(n,e[t]);return n}).args([["numbers",["collect","number"]]]),R("lcm",function(e){var t,n=e[0];for(t=1;t<e.length;++t)n=r(n,e[t]);return n}).args([["numbers",["collect","number"]]]),R("sum",function(e){return e.reduce(function(e,t){return e+t},0)}).args([["numbers",["collect","number"]]]),R("product",function(e){return e.reduce(function(e,t){return e*t},1)}).args([["numbers",["collect","number"]]]),R("sumproduct",function(e,t){var n=0;return e.each(function(e,i,r){var o,s;if("number"==typeof e){for(o=0;o<t.length;++o){if(s=t[o].get(i,r),"number"!=typeof s)return;e*=s}n+=e}}),n}).args([["a1","matrix"],["+",["a2",["and","matrix",["assert","$a2.width == $a1.width"],["assert","$a2.height == $a1.height"]]]]]),R("sumsq",function(e){return e.reduce(function(e,t){return e+t*t},0)}).args([["numbers",["collect","number"]]]),R("sumx2my2",function(e,t){var n=0;return e.each(function(e,i,r){var o=t.get(i,r);"number"==typeof e&&"number"==typeof o&&(n+=e*e-o*o)}),n}).args([["a","matrix"],["b",["and","matrix",["assert","$b.width == $a.width"],["assert","$b.height == $a.height"]]]]),R("sumx2py2",function(e,t){var n=0;return e.each(function(e,i,r){var o=t.get(i,r);"number"==typeof e&&"number"==typeof o&&(n+=e*e+o*o)}),n}).args([["a","matrix"],["b",["and","matrix",["assert","$b.width == $a.width"],["assert","$b.height == $a.height"]]]]),R("sumxmy2",function(e,t){var n=0;return e.each(function(e,i,r){var o=t.get(i,r);"number"==typeof e&&"number"==typeof o&&(n+=(e-o)*(e-o))}),n}).args([["a","matrix"],["b",["and","matrix",["assert","$b.width == $a.width"],["assert","$b.height == $a.height"]]]]),R("seriessum",function(e,t,n,i){var r=0;return i.each(function(i){if("number"!=typeof i)throw new A("VALUE");r+=i*Math.pow(e,t),t+=n}),r}).args([["x","number"],["y","number"],["m","number"],["a","matrix"]]),R("min",function(e){return e.length?Math.min.apply(Math,e):0}).args([["numbers",["collect","number"]]]),R("max",function(e){return e.length?Math.max.apply(Math,e):0}).args([["numbers",["collect","number"]]]),R("counta",function(e){return e.length}).args([["values",["#collect","anyvalue"]]]),R("count",function(e){return e.length}).args([["numbers",["#collect","number"]]]),R("countunique",function(e){var t=0,n=[];return e.forEach(function(e){n.indexOf(e)<0&&(t++,n.push(e))}),t}).args([["values",["#collect","anyvalue"]]]),R("countblank",function(e){function t(e){null!=e&&""!==e||i++}function n(e){var n,i;for(n=0;n<e.length;++n)i=e[n],i instanceof M?i.each(t,!0):t(i)}var i=0;return n(e),i}).args([["+",["args",["or","matrix","anyvalue"]]]]),R("iseven",function(e){return e%2===0}).args([["*number","number"]]),R("isodd",function(e){return e%2!==0}).args([["*number","number"]]),R("n",function(e){return"boolean"==typeof e?e?1:0:"number"==typeof e?e:0}).args([["*value","anyvalue"]]),R("na",function(){return new A("N/A")}).args([]),P=[["m1","matrix"],["c1","anyvalue"],[["m2","matrix"],["c2","anyvalue"]]],R("countifs",function(e,t,n){var i=0;return n.unshift(e,t),o(n,function(){i++}),i}).args(P),H=[["range","matrix"]].concat(P),R("sumifs",function(e,t,n,i){i.unshift(e,w,t,n);var r=0;return o(i,function(t,n){var i=e.get(t,n);i&&(r+=i)}),r}).args(H),R("averageifs",function(e,t,n,i){i.unshift(e,w,t,n);var r=0,s=0;return o(i,function(t,n){var i=e.get(t,n);null!=i&&""!==i||(i=0),r+=i,s++}),s?r/s:new A("DIV/0")}).args(H),R("countif",function(e,t){t=q(t);var n=0;return e.each(function(e){t(e)&&n++}),n}).args([["range","matrix"],["*criteria","anyvalue"]]),O=[["range","matrix"],["*criteria","anyvalue"],["sumRange",["or",["and","matrix",["assert","$sumRange.width == $range.width"],["assert","$sumRange.height == $range.height"]],["null","$range"]]]],R("sumif",function(e,t,n){var i=0;return t=q(t),e.each(function(e,r,o){if(t(e)){var s=n.get(r,o);w(s)&&(i+=s||0)}}),i}).args(O),R("averageif",function(e,t,n){var i=0,r=0;return t=q(t),e.each(function(e,o,s){if(t(e)){var a=n.get(o,s);w(a)&&(i+=a||0,r++)}}),r?i/r:new A("DIV/0")}).args(O),function(e){e("large",function(e,t){return e.sort(_)[t]}),e("small",function(e,t){return e.sort(y)[t]})}(function(e,t){R(e,function(e,n){var i=[],r=e.each(function(e){return e instanceof A?e:void("number"==typeof e&&i.push(e))});return r?r:n>i.length?new A("NUM"):t(i,n-1)}).args([["array","matrix"],["*nth","number++"]])}),R("stdev.s",function(e){return l(e,e.length-1)}).args([["numbers",["collect","number"]],["?",["assert","$numbers.length >= 2","NUM"]]]),R("stdev.p",function(e){return l(e,e.length)}).args([["numbers",["collect","number"]],["?",["assert","$numbers.length >= 2","NUM"]]]),R("var.s",function(e){return a(e,e.length-1)}).args([["numbers",["collect","number"]],["?",["assert","$numbers.length >= 2","NUM"]]]),R("var.p",function(e){return a(e,e.length)}).args([["numbers",["collect","number"]],["?",["assert","$numbers.length >= 2","NUM"]]]),R("median",function(e){var t=e.length;return e.sort(y),t%2?e[t>>1]:(e[t>>=1]+e[t-1])/2}).args([["numbers",["collect","number"]],["?",["assert","$numbers.length > 0","N/A"]]]),R("mode.sngl",function(e){var t,n,i,r,o,s;for(e.sort(y),t=null,n=0,i=1,r=null,o=0;o<e.length;++o)s=e[o],s!=t?(n=1,t=s):n++,n>i&&(i=n,r=s);return null==r?new A("N/A"):r}).args([["numbers",["collect","number"]]]),R("mode.mult",function(e){var t,n=Object.create(null),i=2,r=[];return e.forEach(function(e){var t=n[e]||0;n[e]=++t,t==i?r.push(e):t>i&&(i=t,r=[e])}),t=new M(this),r.forEach(function(e,n){t.set(n,0,e)}),t}).args([["numbers",["collect","number"]]]),R("geomean",function(e){var t=e.length,n=e.reduce(function(e,t){if(t<0)throw new A("NUM");return e*t},1);return Math.pow(n,1/t)}).args([["numbers",["collect","number"]],["?",["assert","$numbers.length > 0","NUM"]]]),R("harmean",function(e){var t=e.length,n=e.reduce(function(e,t){if(!t)throw new A("DIV/0");return e+1/t},0);return t/n}).args([["numbers",["collect","number"]],["?",["assert","$numbers.length > 0","NUM"]]]),R("trimmean",function(e,t){var n,i,r,o=e.length;for(e.sort(y),n=Math.floor(o*t),n%2&&--n,n/=2,i=0,r=n;r<o-n;++r)i+=e[r];return i/(o-2*n)}).args([["numbers",["collect","number",1]],["percent",["and","number",["[between)",0,1]]],["?",["assert","$numbers.length > 0","NUM"]]]),R("frequency",function(e,t){function n(t){for(var n=0;r<e.length&&e[r]>i&&e[r]<=t;)++n,++r;return n}var i,r,o;return e.sort(y),t.sort(y),i=-(1/0),r=0,o=new M(this),t.forEach(function(e,t){var r=n(e);i=e,o.set(t,0,r)}),o.set(o.height,0,e.length-r),o}).args([["data",["collect","number",1]],["bins",["collect","number",1]]]),R("rank.eq",function(e,t,n){t.sort(n?y:_);var i=t.indexOf(e);return i<0?new A("N/A"):i+1}).args([["value","number"],["numbers",["collect","number"]],["order",["or","logical",["null",!1]]]]),S("rank","rank.eq"),R("rank.avg",function(e,t,n){var i,r;if(t.sort(n?y:_),i=t.indexOf(e),i<0)return new A("N/A");for(r=i;t[r]==e;++r);return(i+r+1)/2}).args([["value","number"],["numbers",["collect","number"]],["order",["or","logical",["null",!1]]]]),R("kurt",function(e){var t=e.length,n=s(e),i=a(e,t-1,n),r=Math.sqrt(i),o=e.reduce(function(e,t){return e+Math.pow((t-n)/r,4)},0);return t*(t+1)/((t-1)*(t-2)*(t-3))*o-3*Math.pow(t-1,2)/((t-2)*(t-3))}).args([["numbers",["collect","number"]],["?",["assert","$numbers.length >= 4","NUM"]]]),$=[["array",["collect","number",1]],["x","number"],["significance",["or",["null",3],"integer++"]],["?",["assert","$array.length > 0","NUM"]]],R("percentrank.inc",function(e,t,n){var i=u(e,t,0);return i=i.toFixed(n+1),parseFloat(i.substr(0,i.length-1))}).args($),R("percentrank.exc",function(e,t,n){var i=u(e,t,1);return i=i.toFixed(n+1),parseFloat(i.substr(0,i.length-1))}).args($),S("percentrank","percentrank.inc"),R("covariance.p",function(e,t){return c(e,t,e.length)}).args([["array1",["collect","number",1]],["array2",["collect","number",1]],["?",["assert","$array1.length == $array2.length","N/A"]],["?",["assert","$array1.length > 0","DIV/0"]]]),R("covariance.s",function(e,t){return c(e,t,e.length-1)}).args([["array1",["collect","number",1]],["array2",["collect","number",1]],["?",["assert","$array1.length == $array2.length","N/A"]],["?",["assert","$array1.length > 1","DIV/0"]]]),S("covar","covariance.p"),V=k.memoize(function(e){for(var t=2,n=1;t<=e;++t)n*=t;return n}),R("fact",V).args([["*n","integer+"]]),R("factdouble",function(e){for(var t=2+(1&e),n=1;t<=e;t+=2)n*=t;return n}).args([["*n","integer+"]]),R("multinomial",function(e){var t=1,n=0;return e.forEach(function(e){if(e<0)throw new A("NUM");n+=e,t*=V(e)}),V(n)/t}).args([["numbers",["collect","number"]]]),U=k.memoize(function(e,t){for(var n=t+1,i=1,r=1,o=1;i<=e-t;++n,++i)r*=n,o*=i;return r/o}),R("combin",U).args([["*n","integer++"],["*k",["and","integer",["[between]",0,"$n"]]]]),R("combina",function(e,t){return U(e+t-1,e-1)}).args([["*n","integer++"],["*k",["and","integer",["[between]",1,"$n"]]]]),R("average",function(e){var t=e.reduce(function(e,t){return e+t},0);return t/e.length}).args([["numbers",["collect","number!"]],["?",["assert","$numbers.length > 0","DIV/0"]]]),R("averagea",function(e){var t=0,n=0;return e.forEach(function(e){"string"!=typeof e&&(t+=e),++n}),n?t/n:new A("DIV/0")}).args([["values",["collect","anyvalue"]]]),R("percentile.inc",d).args([["numbers",["collect","number",1]],["p",["and","number",["[between]",0,1]]]]),R("percentile.exc",f).args([["numbers",["collect","number",1]],["p",["and","number",["(between)",0,1]]]]),R("quartile.inc",function(e,t){return d(e,t/4)}).args([["numbers",["collect","number",1]],["quarter",["values",0,1,2,3,4]]]),R("quartile.exc",function(e,t){return f(e,t/4)}).args([["numbers",["collect","number",1]],["quarter",["values",0,1,2,3,4]]]),S("quartile","quartile.inc"),S("percentile","percentile.inc"),W=["AVERAGE","COUNT","COUNTA","MAX","MIN","PRODUCT","STDEV.S","STDEV.P","SUM","VAR.S","VAR.P","MEDIAN","MODE.SNGL","LARGE","SMALL","PERCENTILE.INC","QUARTILE.INC","PERCENTILE.EXC","QUARTILE.EXC"],R("aggregate",function(e,t,n,i){var r=this;r.resolveCells(i,function(){var o,s;if(t>12){if(o=p(r,i[0],n),s=i[1],s instanceof E&&(s=r.getRefData(s)),"number"!=typeof s)return e(new A("VALUE"))}else o=p(r,i,n);r.func(W[t-1],e,o)})}).argsAsync([["funcId",["values",1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19]],["options",["or",["null",0],["values",0,1,2,3,4,5,6,7]]],["args","rest"]]),R("subtotal",function(e,t){var n,i,r=this,o=t>100;for(o&&(t-=100),n=[],i=2;i<arguments.length;++i)n.push(arguments[i]);r.resolveCells(n,function(){
var i=p(r,n,o?1:0);r.func(W[t-1],e,i)})}).argsAsync([["funcId",["values",1,2,3,4,5,6,7,8,9,10,11,101,102,103,104,105,106,107,108,109,110,111]],["+",["ref",["or","ref","#matrix"]]]]),R("avedev",function(e){var t=e.reduce(function(e,t){return e+t},0)/e.length;return e.reduce(function(e,n){return e+Math.abs(n-t)},0)/e.length}).args([["numbers",["collect","number"]],["?",["assert","$numbers.length >= 2","NUM"]]]),R("binom.dist",m).args([["successes","integer+"],["trials",["and","integer",["assert","$trials >= $successes"]]],["probability",["and","number",["[between]",0,1]]],["cumulative","logical"]]),S("binomdist","binom.dist"),R("binom.inv",function(e,t,n){for(var i=0;i<=e;++i)if(m(i,e,t,!0)>=n)return i;return new A("N/A")}).args([["trials","integer+"],["probability",["and","number",["[between]",0,1]]],["alpha",["and","number",["[between]",0,1]]]]),S("critbinom","binom.inv"),R("binom.dist.range",function(e,t,n,i){var r,o=0;for(r=n;r<=i;++r)o+=U(e,r)*Math.pow(t,r)*Math.pow(1-t,e-r);return o}).args([["trials","integer+"],["probability",["and","number",["[between]",0,1]]],["successes_min",["and","integer",["[between]",0,"$trials"]]],["successes_max",["or",["and","integer",["[between]","$successes_min","$trials"]],["null","$successes_min"]]]]),R("negbinom.dist",function(e,t,n,i){if(i){for(var r=0;e>=0;)r+=U(e+t-1,e)*Math.pow(n,t)*Math.pow(1-n,e),e--;return r}return U(e+t-1,e)*Math.pow(n,t)*Math.pow(1-n,e)}).args([["number_f","integer+"],["number_s","integer+"],["probability_s",["and","number",["[between]",0,1]]],["cumulative","logical"]]),S("negbinomdist","negbinom.dist"),R("address",function(e,t,n,i,r){var o=new E(e-1,t-1,n-1);return r&&o.setSheet(r,!0),i?o.print(0,0):o.print()}).args([["row","integer++"],["col","integer++"],["abs",["or",["null",1],["values",1,2,3,4]]],["a1",["or",["null",!0],"logical"]],["sheet",["or","null","string"]]]),R("areas",function(e){var t=0;return function n(e){e instanceof E||e instanceof D?t++:e instanceof B&&e.refs.forEach(n)}(e),t}).args([["ref","ref"]]),R("choose",function(e,t){return e>t.length?new A("N/A"):t[e-1]}).args([["*index","integer"],["+",["value","anything"]]]),R("column",function(e){return e?e instanceof E?e.col+1:this.asMatrix(e).mapCol(function(t){return t+e.topLeft.col+1}):this.formula.col+1}).args([["ref",["or","area","null"]]]),R("columns",function(e){return e instanceof T?e.width():e.width}).args([["ref",["or","area","#matrix"]]]),R("formulatext",function(e){var t=this.getRefCells(e)[0];return t.formula?t.formula.print(t.row,t.col):new A("N/A")}).args([["ref","ref"]]),R("hlookup",function(e,t,n,i){var r=null;return t.eachCol(function(n){var o=t.get(0,n);if(i){if(o>e)return!0;r=n}else if(o===e)return r=n,!0}),null==r?new A("N/A"):t.get(n-1,r)}).args([["value","anyvalue"],["range","matrix"],["row","integer++"],["approx",["or","logical",["null",!0]]]]),R("index",function(e,t,n,i,r){var o,s,a,l=this;if(t instanceof B&&(t=t.refs[r-1]),!n&&!i||!t)return e(new A("N/A"));if(t instanceof E&&(t=t.toRangeRef()),t instanceof D){if(n&&i)return i>t.width()||n>t.height()?e(new A("REF")):(o=t.toCell(n-1,i-1),void l.resolveCells([o],function(){e(l.getRefData(o))}));if(!n)return s=t.toColumn(i-1),void l.resolveCells([s],function(){e(l.asMatrix(s))});if(!i)return a=t.toRow(n-1),void l.resolveCells([a],function(){e(l.asMatrix(a))})}else if(t instanceof M){if(t.width>1&&t.height>1){if(n&&i)return e(t.get(n-1,i-1));if(!n)return e(t.mapRow(function(e){return t.get(e,i-1)}));if(!i)return e(t.mapCol(function(e){return t.get(n-1,e)}))}if(1==t.width)return e(t.get(n-1,0));if(1==t.height)return e(t.get(0,i-1))}else e(new A("REF"))}).argsAsync([["range",["or","ref","matrix"]],["row",["or","integer+","null"]],["col",["or","integer+","null"]],["areanum",["or","integer++",["null",1]]]]),R("indirect",function(e){var t,n;try{if(t=this.formula,n=C.parseFormula(t.sheet,t.row,t.col,e),!(n.ast instanceof T))throw 1;return n.ast.absolute(t.row,t.col)}catch(i){return new A("REF")}}).args([["thing","string"]]),R("match",function(e,t,n){var i,r=1;return 0===n?i=q(e):n===-1?i=q("<="+e):1===n&&(i=q(">="+e)),t.each(function(t){return null!=t&&i(t)?(0!==n&&e!=t&&--r,!0):void r++},!0)&&r>0?r:new A("N/A")}).args([["value","anyvalue"],["range","matrix"],["type",["or",["values",-1,0,1],["null",1]]]]),R("offset",function(e,t,n,i,r){var o=(e instanceof E?e:e.topLeft).clone();return o.row+=t,o.col+=n,o.row<0||o.col<0?new A("VALUE"):i>1||r>1?new D(o,new E(o.row+i-1,o.col+r-1)).setSheet(e.sheet,e.hasSheet()):o}).args([["ref","area"],["*rows","integer"],["*cols","integer"],["*height",["or","integer++",["null","$ref.height()"]]],["*width",["or","integer++",["null","$ref.width()"]]]]),R("row",function(e){return e?e instanceof E?e.row+1:this.asMatrix(e).mapRow(function(t){return t+e.topLeft.row+1}):this.formula.row+1}).args([["ref",["or","area","null"]]]),R("rows",function(e){return e instanceof T?e.height():e.height}).args([["ref",["or","area","#matrix"]]]),R("vlookup",function(e,t,n,i){var r=null;return"number"!=typeof e&&(i=!1),"string"==typeof e&&(e=e.toLowerCase()),t.eachRow(function(n){var o=t.get(n,0);if(i){if(o>e)return!0;r=n}else if("string"==typeof o&&(o=o.toLowerCase()),o===e)return r=n,!0}),null==r?new A("N/A"):t.get(r,n-1)}).args([["value","anyvalue"],["range","matrix"],["col","integer++"],["approx",["or","logical",["null",!0]]]]),R("date",function(e,t,n){return L(e,t-1,n)}).args([["*year","integer"],["*month","integer"],["*date","integer"]]),R("day",function(e){return N(e).date}).args([["*date","date"]]),R("month",function(e){return N(e).month+1}).args([["*date","date"]]),R("year",function(e){return N(e).year}).args([["*date","date"]]),R("weekday",function(e){return N(e).day+1}).args([["*date","date"]]),R("weeknum",function(e,t){var n,i=L(N(e).year,0,1),r=N(i);return 21==t?(n=3-(r.day+6)%7,n<0&&(n+=7),i+=n,r.date+=n,r.day=4,t=1):t=1==t?0:2==t?1:(t-10)%7,n=r.day-t,n<0&&(n+=7),i-=n,Math.ceil((e+1-i)/7)}).args([["*date","date"],["*type",["or",["null",1],["values",1,2,11,12,13,14,15,16,17,21]]]]),R("isoweeknum",function(e){var t=N(e),n=t.day||7,i=Math.floor((t.ord-n+10)/7);return i<1?g(t.year-1):53==i&&i>g(t.year)?1:i}).args([["*date","date"]]),R("now",function(){return F.dateToSerial(new Date)}).args([]),R("today",function(){return 0|F.dateToSerial(new Date)}).args([]),R("time",function(e,t,n){return F.packTime(e,t,n,0)}).args([["*hours","integer"],["*minutes","integer"],["*seconds","integer"]]),R("hour",function(e){return F.unpackTime(e).hours}).args([["*time","datetime"]]),R("minute",function(e){return F.unpackTime(e).minutes}).args([["*time","datetime"]]),R("second",function(e){return F.unpackTime(e).seconds}).args([["*time","datetime"]]),R("edate",function(e,t){var n=N(e),i=n.month+t,r=n.year+Math.floor(i/12);return i%=12,i<0&&(i+=12),n=Math.min(n.date,I(r,i)),L(r,i,n)}).args([["*start_date","date"],["*months","integer"]]),R("eomonth",function(e,t){var n=N(e),i=n.month+t,r=n.year+Math.floor(i/12);return i%=12,i<0&&(i+=12),n=I(r,i),L(r,i,n)}).args([["*start_date","date"],["*months","integer"]]),R("workday",function(e,t,n){var i,r=t>0?1:-1;for(t=Math.abs(t),i=N(e).day;t>0;)e+=r,i=(i+r)%7,i>0&&i<6&&n.indexOf(e)<0&&--t;return e}).args([["start_date","date"],["days","integer"],["holidays",["collect","date"]]]),R("networkdays",function(e,t,n){var i,r,o;for(e>t&&(i=e,e=t,t=i),r=0,o=N(e).day;e<=t;)o>0&&o<6&&n.indexOf(e)<0&&r++,e++,o=(o+1)%7;return r}).args([["start_date","date"],["end_date","date"],["holidays",["collect","date"]]]),R("days",function(e,t){return t-e}).args([["*start_date","date"],["*end_date","date"]]),F._days_360=v,R("days360",v).args([["*start_date","date"],["*end_date","date"],["*method",["or","logical",["null",!1]]]]),R("yearfrac",function(e,t,n){switch(n){case 0:return v(e,t,!1)/360;case 1:return(t-e)/z(N(e).year);case 2:return(t-e)/360;case 3:return(t-e)/365;case 4:return v(e,t,!0)/360}}).args([["*start_date","date"],["*end_date","date"],["*method",["or",["null",0],["values",0,1,2,3,4]]]]),R("datevalue",function(e){var t=F.parseDate(e);return t?F.dateToSerial(t):new A("VALUE")}).args([["*text","string"]]),R("timevalue",function(e){var t,n,i,r,o=e.toLowerCase().match(/(\d+):(\d+)(:(\d+)(\.(\d+))?)?\s*(am?|pm?)?/);return o?(t=parseFloat(o[1]),n=parseFloat(o[2]),i=o[3]?parseFloat(o[4]):0,r=o[7],r&&(t>12||t<1)?new A("VALUE"):(/^p/.test(r)&&(t+=12),F.packTime(t,n,i,0))):new A("VALUE")}).args([["*text","string"]]),R("mdeterm",function(e){var t=e.each(function(e){if("number"!=typeof e)return new A("VALUE")},!0);return t||e.determinant()}).args([["m",["and","matrix",["assert","$m.width == $m.height"]]]]),R("transpose",function(e){return e.transpose()}).args([["range","matrix"]]),R("mmult",function(e,t){return e.multiply(t)}).args([["a","matrix"],["b",["and","matrix",["assert","$b.height == $a.width"]]]]),R("munit",function(e){return new M(this).unit(e)}).args([["n","integer+"]]),R("minverse",function(e){var t=e.each(function(e){if("number"!=typeof e)return new A("VALUE")},!0);return t||e.inverse()||new A("VALUE")}).args([["m",["and","matrix",["assert","$m.width == $m.height"]]]]),R("rand",function(){return Math.random()}).args([]),R("randbetween",function(e,t){return e+Math.floor((t-e+1)*Math.random())}).args([["min","integer"],["max",["and","integer",["assert","$max >= $min"]]]]),R("true",function(){return!0}).args([]),R("false",function(){return!0}).args([]),R("roman",function(e){return k.arabicToRoman(e).toUpperCase()}).args([["*number","integer"]]),R("arabic",function(e){var t=k.romanToArabic(e);return null==t?new A("VALUE"):t}).args([["*roman","string"]]),R("base",function(e,t,n){for(var i=e.toString(t).toUpperCase();i.length<n;)i="0"+i;return i}).args([["*number","integer"],["*radix",["and","integer",["[between]",2,36]]],["*minLen",["or","integer+",["null",0]]]]),R("decimal",function(e,t){var n,i,r;for(e=e.toUpperCase(),n=0,i=0;i<e.length;++i){if(r=e.charCodeAt(i),r>=48&&r<=57)r-=48;else{if(!(r>=65&&r<55+t))return new A("VALUE");r-=55}n=n*t+r}return n}).args([["*text","string"],["*radix",["and","integer",["[between]",2,36]]]]),R("char",function(e){return String.fromCharCode(e)}).args([["*code","integer+"]]),j=/[\0-\x1F\x7F-\x9F\xAD\u0378\u0379\u037F-\u0383\u038B\u038D\u03A2\u0528-\u0530\u0557\u0558\u0560\u0588\u058B-\u058E\u0590\u05C8-\u05CF\u05EB-\u05EF\u05F5-\u0605\u061C\u061D\u06DD\u070E\u070F\u074B\u074C\u07B2-\u07BF\u07FB-\u07FF\u082E\u082F\u083F\u085C\u085D\u085F-\u089F\u08A1\u08AD-\u08E3\u08FF\u0978\u0980\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09FC-\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF2-\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B55\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B78-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BFB-\u0C00\u0C04\u0C0D\u0C11\u0C29\u0C34\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5A-\u0C5F\u0C64\u0C65\u0C70-\u0C77\u0C80\u0C81\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0D01\u0D04\u0D0D\u0D11\u0D3B\u0D3C\u0D45\u0D49\u0D4F-\u0D56\u0D58-\u0D5F\u0D64\u0D65\u0D76-\u0D78\u0D80\u0D81\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DF1\u0DF5-\u0E00\u0E3B-\u0E3E\u0E5C-\u0E80\u0E83\u0E85\u0E86\u0E89\u0E8B\u0E8C\u0E8E-\u0E93\u0E98\u0EA0\u0EA4\u0EA6\u0EA8\u0EA9\u0EAC\u0EBA\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F48\u0F6D-\u0F70\u0F98\u0FBD\u0FCD\u0FDB-\u0FFF\u10C6\u10C8-\u10CC\u10CE\u10CF\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u137D-\u137F\u139A-\u139F\u13F5-\u13FF\u169D-\u169F\u16F1-\u16FF\u170D\u1715-\u171F\u1737-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17DE\u17DF\u17EA-\u17EF\u17FA-\u17FF\u180F\u181A-\u181F\u1878-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191D-\u191F\u192C-\u192F\u193C-\u193F\u1941-\u1943\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DB-\u19DD\u1A1C\u1A1D\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1A9F\u1AAE-\u1AFF\u1B4C-\u1B4F\u1B7D-\u1B7F\u1BF4-\u1BFB\u1C38-\u1C3A\u1C4A-\u1C4C\u1C80-\u1CBF\u1CC8-\u1CCF\u1CF7-\u1CFF\u1DE7-\u1DFB\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FC5\u1FD4\u1FD5\u1FDC\u1FF0\u1FF1\u1FF5\u1FFF\u200B-\u200F\u202A-\u202E\u2060-\u206F\u2072\u2073\u208F\u209D-\u209F\u20BB-\u20CF\u20F1-\u20FF\u218A-\u218F\u23F4-\u23FF\u2427-\u243F\u244B-\u245F\u2700\u2B4D-\u2B4F\u2B5A-\u2BFF\u2C2F\u2C5F\u2CF4-\u2CF8\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D71-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E3C-\u2E7F\u2E9A\u2EF4-\u2EFF\u2FD6-\u2FEF\u2FFC-\u2FFF\u3040\u3097\u3098\u3100-\u3104\u312E-\u3130\u318F\u31BB-\u31BF\u31E4-\u31EF\u321F\u32FF\u4DB6-\u4DBF\u9FCD-\u9FFF\uA48D-\uA48F\uA4C7-\uA4CF\uA62C-\uA63F\uA698-\uA69E\uA6F8-\uA6FF\uA78F\uA794-\uA79F\uA7AB-\uA7F7\uA82C-\uA82F\uA83A-\uA83F\uA878-\uA87F\uA8C5-\uA8CD\uA8DA-\uA8DF\uA8FC-\uA8FF\uA954-\uA95E\uA97D-\uA97F\uA9CE\uA9DA-\uA9DD\uA9E0-\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A\uAA5B\uAA7C-\uAA7F\uAAC3-\uAADA\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F-\uABBF\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBC2-\uFBD2\uFD40-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFE\uFDFF\uFE1A-\uFE1F\uFE27-\uFE2F\uFE53\uFE67\uFE6C-\uFE6F\uFE75\uFEFD-\uFF00\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFDF\uFFE7\uFFEF-\uFFFB\uFFFE\uFFFF]/g,R("clean",function(e){return e.replace(j,"")}).args([["*text","string"]]),R("code",function(e){return e.charAt(0)}).args([["*text","string"]]),S("unichar","char"),S("unicode","code"),R("concatenate",function(e){var t,n="";for(t=0;t<e.length;++t)n+=e[t];return n}).args([["+",["*text","string"]]]),R("dollar",function(e,t){for(var n="$#,##0DECIMALS;($#,##0DECIMALS)",i="",r=1;t-- >0;)i+="0";for(;++t<0;)r*=10;return""!==i?i="."+i:1!==r&&(e=Math.round(e/r)*r),n=n.replace(/DECIMALS/g,i),x.formatting.text(e,n)}).args([["*number","number"],["*decimals",["or","integer",["null",2]]]]),R("exact",function(e,t){return e===t}).args([["*text1","string"],["*text2","string"]]),R("find",function(e,t,n){var i=t.indexOf(e,n-1);return i<0?new A("VALUE"):i+1}).args([["*substring","string"],["*string","string"],["*start",["or","integer++",["null",1]]]]),R("fixed",function(e,t,n){var i,r=Math.pow(10,t);if(e=Math.round(e*r)/r,i=n?"0":"#,##0",t>0)for(i+=".";t-- >0;)i+="0";return x.formatting.text(e,i)}).args([["*number","number"],["*decimals",["or","integer",["null",2]]],["*noCommas",["or","boolean",["null",!1]]]]),R("left",function(e,t){return e.substr(0,t)}).args([["*text","string"],["*length",["or","integer+",["null",1]]]]),R("right",function(e,t){return e.substr(-t)}).args([["*text","string"],["*length",["or","integer+",["null",1]]]]),R("len",function(e){return e.length}).args([["*text","string"]]),R("lower",function(e){return e.toLowerCase()}).args([["*text","string"]]),R("upper",function(e){return e.toUpperCase()}).args([["*text","string"]]),R("ltrim",function(e){return e.replace(/^\s+/,"")}).args([["*text","string"]]),R("rtrim",function(e){return e.replace(/\s+$/,"")}).args([["*text","string"]]),R("trim",function(e){return e.replace(/^\s+|\s+$/,"")}).args([["*text","string"]]),R("mid",function(e,t,n){return e.substr(t-1,n)}).args([["*text","string"],["*start","integer++"],["*length","integer+"]]),R("proper",function(e){return e.toLowerCase().replace(/\b./g,function(e){return e.toUpperCase()})}).args([["*text","string"]]),R("replace",function(e,t,n,i){return e.substr(0,--t)+i+e.substr(t+n)}).args([["*text","string"],["*start","integer++"],["*length","integer+"],["*newText","string"]]),R("rept",function(e,t){for(var n="";t-- >0;)n+=e;return n}).args([["*text","string"],["*number","integer+"]]),R("search",function(e,t,n){var i=t.toLowerCase().indexOf(e.toLowerCase(),n-1);return i<0?new A("VALUE"):i+1}).args([["*substring","string"],["*string","string"],["*start",["or","integer++",["null",1]]]]),R("substitute",function(e,t,n,i){function r(){e=e.substring(0,o)+n+e.substring(o+t.length)}if(t===n)return e;for(var o=-1;(o=e.indexOf(t,o+1))>=0;)if(null==i)r();else if(0===--i){r();break}return e}).args([["*text","string"],["*oldText","string"],["*newText","string"],["*nth",["or","integer++","null"]]]),R("t",function(e){return"string"==typeof e?e:""}).args([["*value","anyvalue"]]),R("text",function(e,t){return x.formatting.text(e,t)}).args([["*value","anyvalue"],["*format","string"]]),R("value",function(e){return"number"==typeof e?e:"boolean"==typeof e?+e:(e=(e+"").replace(/[$€,]/g,""),e=parseFloat(e),isNaN(e)?new A("VALUE"):e)}).args([["*value","anyvalue"]]),b.prototype.toString=function(){return this.text},R("hyperlink",function(e,t){return new b(e,t)}).args([["*link","string"],["*text",["or","string",["null","$link"]]]]),R("iferror",function(e,t){return e instanceof A?t:e}).args([["*value","forced!"],["*value_if_error","anyvalue!"]]),q=function(){function e(e,t){if("string"==typeof t){var n=parseFloat(t);isNaN(n)||n!=t||(t=n)}return function(n){var i=t;return"string"==typeof n&&"string"==typeof i&&(n=n.toLowerCase(),i=i.toLowerCase()),e(n,i)}}function t(e){var t,n;return"string"==typeof e&&(e=e.toLowerCase()),/^[0-9.]+%$/.test(e)?(n=e.substr(0,e.length-1),t=parseFloat(n),isNaN(t)||t!=n||(e=t/100)):/^[0-9.]+$/.test(e)&&(t=parseFloat(e),isNaN(t)||t!=e||(e=t)),e}function n(e,n){return t(e)<t(n)}function i(e,n){return t(e)<=t(n)}function r(e,n){return t(e)>t(n)}function o(e,n){return t(e)>=t(n)}function s(e,t){return!a(e,t)}function a(e,n){return n instanceof RegExp?n.test(e):("string"!=typeof e&&"string"!=typeof n||(e+="",n+=""),t(e)==t(n))}var l=Object.create(null);return function(t){var u,c;return"function"==typeof t?t:(u=/^=(.*)$/.exec(t))?e(a,u[1]):(u=/^<>(.*)$/.exec(t))?e(s,u[1]):(u=/^<=(.*)$/.exec(t))?e(i,u[1]):(u=/^<(.*)$/.exec(t))?e(n,u[1]):(u=/^>=(.*)$/.exec(t))?e(o,u[1]):(u=/^>(.*)$/.exec(t))?e(r,u[1]):/[?*]/.exec(t)?(c=l[t],c||(c=t.replace(/(~\?|~\*|[\]({\+\.\|\^\$\\})\[]|[?*])/g,function(e){switch(e){case"~?":return"\\?";case"~*":return"\\*";case"?":return".";case"*":return".*";default:return"\\"+e}}),c=l[t]=RegExp("^"+c+"$","i")),e(a,c)):e(a,t)}}())},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/runtime.functions.2.min",["spreadsheet/runtime.min"],e)}(function(){"use strict";function e(e){var n,i,r,o;if(ct(e)>=3.3)return 1-t(e);for(n=e>0?1:-1,n==-1&&(e=-e),i=0,r=1,o=1;o<100;o++)i+=r,r*=2*e*e/(2*o+1);return 2*n/Math.sqrt(Math.PI)*e*Math.exp(-e*e)*i}function t(t){var n,i,r;if(ct(t)<3.3)return 1-e(t);for(n=1,t<0&&(n=-1,t=-t),i=t,r=8;r>=1;r-=.5)i=t+r/i;return i=1/(t+i),1==n?Math.exp(-t*t)/Math.sqrt(Math.PI)*i:2-Math.exp(-t*t)/Math.sqrt(Math.PI)*i}function n(e){var t,n=[1.000000000190015,76.18009172947146,-86.50532032941678,24.01409824083091,-1.231739572450155,.001208650973866179,-5395239384953e-18],i=e,r=e+5.5,o=n[0];for(r-=(e+.5)*Math.log(r),t=1;t<=6;t++)i+=1,o+=n[t]/i;return-r+Math.log(Math.sqrt(2*Math.PI)*o/e)}function i(e){if(e>0)return Math.exp(n(e));var t=Math.PI,r=-e;return-t/(r*i(r)*Math.sin(t*r))}function r(e,t){return n(e)+n(t)-n(e+t)}function o(e,t){return Math.exp(r(e,t))}function s(e,t){return t<e+1?a(e,t):1-l(e,t)}function a(e,t){var i,r=1/e,o=r,s=e,a=n(e);for(i=1;i<=at&&(s++,o*=t/s,r+=o,!(ct(o)<ct(r)*lt));i++);return r*Math.exp(-t+e*Math.log(t)-a)}function l(e,t){var i,r,o=ut,s=o,a=0,l=1,u=t+1-e,c=n(e);for(i=1;i<=at&&(a=u+l*a,ct(a)<ut&&(a=ut),s=u+l/s,ct(s)<ut&&(s=ut),a=1/a,r=s*a,o*=r,!(ct(r-1)<lt));i++)u+=2,l=-i*(i-e);return o*Math.exp(-t-c+e*Math.log(t))}function u(e,t,n,r){return r?s(t,e/n):Math.pow(e/n,t-1)*Math.exp(-e/n)/(n*i(t))}function c(e,t,n){var i,r,o,s,a,l;if(0===e)return 0;if(1==e)return 1/0;for(i=0,r=10,o=0,s=t*n,s>1&&(r*=s),a=0;a<at&&(o=.5*(i+r),l=u(o,t,n,!0),!(ct(e-l)<1e-16));a++)l>e?r=o:i=o;return o}function h(t,n){return n?.5+.5*e(t/Math.sqrt(2)):Math.exp(-t*t/2)/Math.sqrt(2*Math.PI)}function d(e){var t,n,i=[-39.69683028665376,220.9460984245205,-275.9285104469687,138.357751867269,-30.66479806614716,2.506628277459239],r=[-54.47609879822406,161.5858368580409,-155.6989798598866,66.80131188771972,-13.28068155288572],o=[-.007784894002430293,-.3223964580411365,-2.400758277161838,-2.549732539343734,4.374664141464968,2.938163982698783],s=[.007784695709041462,.3224671290700398,2.445134137142996,3.754408661907416],a=.02425,l=1-a;return e<a?(t=Math.sqrt(-2*Math.log(e)),(((((o[0]*t+o[1])*t+o[2])*t+o[3])*t+o[4])*t+o[5])/((((s[0]*t+s[1])*t+s[2])*t+s[3])*t+1)):l<e?(t=Math.sqrt(-2*Math.log(1-e)),-(((((o[0]*t+o[1])*t+o[2])*t+o[3])*t+o[4])*t+o[5])/((((s[0]*t+s[1])*t+s[2])*t+s[3])*t+1)):(t=e-.5,n=t*t,(((((i[0]*n+i[1])*n+i[2])*n+i[3])*n+i[4])*n+i[5])*t/(((((r[0]*n+r[1])*n+r[2])*n+r[3])*n+r[4])*n+1))}function f(e,t,n,i){return i?h((e-t)/n,!0):Math.exp(-(e-t)*(e-t)/(2*n*n))/(n*Math.sqrt(2*Math.PI))}function p(e,t,n){return t+n*d(e)}function m(e,t,n){return Math.exp((t-1)*Math.log(e)+(n-1)*Math.log(1-e)-r(t,n))}function g(e,t,n){var i=Math.exp(t*Math.log(e)+n*Math.log(1-e)-r(t,n));return e<(t+1)/(t+n+2)?i*v(t,n,e)/t:1-i*v(n,t,1-e)/n}function v(e,t,n){var i,r,o,s,a,l=e+t,u=e+1,c=e-1,h=1,d=1-l*n/u;for(ct(d)<ut&&(d=ut),d=1/d,a=d,i=1;i<=at&&(r=2*i,o=i*(t-i)*n/((c+r)*(e+r)),d=1+o*d,ct(d)<ut&&(d=ut),h=1+o/h,ct(h)<ut&&(h=ut),d=1/d,a*=d*h,o=-(e+i)*(l+i)*n/((e+r)*(u+r)),d=1+o*d,ct(d)<ut&&(d=ut),h=1+o/h,ct(h)<ut&&(h=ut),d=1/d,s=d*h,a*=s,!(ct(s-1)<lt));i++);return a}function b(e,t,n){var i,r,o=0,s=1,a=0;for(i=0;i<at&&(a=.5*(o+s),r=g(a,t,n),!(ct(e-r)<lt));i++)r>e?s=a:o=a;return a}function w(e,t,n,i,r){return g((e-i)/(r-i),t,n)}function y(e,t,n,i,r,o){return i?g((e-r)/(o-r),t,n):m((e-r)/(o-r),t,n)/(o-r)}function _(e,t,n,i,r){return i+(r-i)*b(e,t,n)}function k(e,t,n){return u(e,t/2,2,n)}function x(e,t){return 1-k(e,t,!0)}function C(e,t){return c(e,t/2,2)}function F(e,t){return C(1-e,t)}function R(e,t){var n,i,r,o,s,a=e.length,l=e[0].length,u=0;for(n=0;n<a;n++)for(i=0;i<l;i++)r=t[n][i],o=e[n][i]-r,o*=o,u+=o/r;return s=(a-1)*(l-1),x(u,s)}function S(e,t,n){return n?1-Math.exp(-t*e):t*Math.exp(-t*e)}function A(e,t,n){var i,r;if(n)return 1-k(2*t,2*(e+1),!0);for(i=0,r=2;r<=e;r++)i+=Math.log(r);return Math.exp(e*Math.log(t)-t-i)}function D(e,t,n,i){if(i)return g(t*e/(n+t*e),t/2,n/2);var r=t/n;return t/=2,n/=2,r/o(t,n)*Math.pow(r*e,t-1)/Math.pow(1+r*e,t+n)}function E(e,t,n){return 1-D(e,t,n,!0)}function B(e,t,n){return n/t*(1/_(e,n/2,t/2,0,1)-1)}function M(e,t,n){return n/t*(1/_(1-e,n/2,t/2,0,1)-1)}function T(e){var t,n=0,i=e.length;for(t=0;t<i;t++)n+=e[t];return n/i}function I(e,t){var n,i,r=0,o=e.length;for(n=0;n<o;n++)i=e[n]-t,r+=i*i;return r/(o-1)}function L(e,t){var n=e.length-1,i=t.length-1,r=I(e,T(e)),o=I(t,T(t));if(!r||!o)throw new et("DIV/0");return 2*D(r/o,n,i,!0)}function N(e){return.5*Math.log((1+e)/(1-e))}function z(e){var t=Math.exp(2*e);return(t-1)/(t+1)}function P(e,t,n){return n?1-.5*g(t/(e*e+t),t/2,.5):1/(Math.sqrt(t)*o(.5,t/2))*Math.pow(1+e*e/t,-(t+1)/2)}function H(e,t){return 1-P(e,t,!0)}function O(e,t){return e<0&&(e=-e),2*H(e,t)}function $(e,t){var n=b(2*Math.min(e,1-e),t/2,.5);return n=Math.sqrt(t*(1-n)/n),e>.5?n:-n}function V(e,t){return $(1-e/2,t)}function U(e,t,n,i){var r,o,s,a,l,u,c,h,d,f,p,m,g,v,b,w,y=e.length,_=t.length;if(1==i){for(s=0,a=0,l=0;l<y;l++)u=e[l]-t[l],s+=u,a+=u*u;return c=s/y,r=c/Math.sqrt((a-s*c)/(y*(y-1))),1==n?H(r,y-1):O(r,y-1)}return h=T(e),d=T(t),f=I(e,h),p=I(t,d),3==i?(m=f/y,g=p/_,v=m+g,b=m/v,w=g/v,o=1/(b*b/(y-1)+w*w/(_-1)),r=ct(h-d)/Math.sqrt(v),1==n?H(r,o):O(r,o)):(o=y+_-2,r=ct(h-d)*Math.sqrt(o*y*_/((y+_)*((y-1)*f+(_-1)*p))),1==n?H(r,o):O(r,o))}function W(e,t,n){return-$(e/2,n-1)*t/Math.sqrt(n)}function j(e,t,n){return-d(e/2)*t/Math.sqrt(n)}function q(e){return h(e,!0)-.5}function K(e){return h(e)}function J(t,n,i,r){if(r)return.5+.5*e((Math.log(t)-n)/(i*Math.sqrt(2)));var o=Math.log(t)-n;return Math.exp(-o*o/(2*i*i))/(t*i*Math.sqrt(2*Math.PI))}function G(e,t,n){return Math.exp(p(e,t,n))}function Y(e,t,n,i){var r,o,s,a=e.length,l=0;for(r=0;r<a;r++){if(t[r]<=0||t[r]>1)throw new et("NUM");l+=t[r]}if(1!=l)throw new et("NUM");for(o=0,r=0;r<a;r++)s=e[r],s>=n&&s<=i&&(o+=t[r]);return o}function X(e,t){var n,i,r,o=T(t),s=T(e),a=0,l=0;for(n=0,i=e.length;n<i;n++)r=t[n]-o,a+=r*(e[n]-s),l+=r*r;return a/l}function Q(e,t){var n,i,r,o=T(t),s=T(e),a=0,l=0;for(n=0,i=e.length;n<i;n++)r=t[n]-o,a+=r*(e[n]-s),l+=r*r;return s-a*o/l}function Z(e,t){var n,i,r,o,s,a,l,u,c;for(Ge(e,t),n=T(e),i=T(t),r=0,o=0,s=0,a=0,l=e.length;a<l;a++)u=e[a]-n,c=t[a]-i,r+=u*c,o+=u*u,s+=c*c;return r/Math.sqrt(o*s)}function ee(e,t){var n=Z(e,t);return n*n}function te(e,t){var n,i,r,o=t.length,s=T(t),a=T(e),l=0,u=0,c=0;for(n=0;n<o;n++)i=t[n]-s,r=e[n]-a,l+=r*r,u+=i*r,c+=i*i;return Math.sqrt((l-u*u/c)/(o-2))}function ne(e,t,n){var i,r,o,s,a,l,u=T(n),c=T(t),h=0,d=0;for(i=0,r=n.length;i<r;i++)o=n[i]-u,s=t[i]-c,h+=o*s,d+=o*o;if(0===d)throw new et("N/A");return a=h/d,l=c-a*u,l+a*e}function ie(e){var t,n=e.height,i=0;for(t=0;t<n;t++)i+=e.data[t][0];return i/n}function re(e,t){var n,i,r=e.height,o=0;for(n=0;n<r;n++)i=e.data[n][0]-t,o+=i*i;return o}function oe(e,t,n,i){var r,o,s,a,l,u,c,h,d,f,p,m,g,v,b,w,y,_,k=0;for(t||(t=e.map(function(){return++k})),n&&(t=t.clone(),t.eachRow(function(e){t.data[e].unshift(1)}),++t.width),r=t.transpose(),o=r.multiply(t).inverse().multiply(r).multiply(e),s=[],k=o.height-1;k>=0;k--)s.push(o.data[k][0]);if(n||s.push(0),!i)return this.asMatrix([s]);for(a=t.multiply(o),l=e.adds(a,!0),u=n?ie(a):0,c=re(a,u),h=n?ie(l):0,d=re(l,h),f=[],f.push(c,d),p=c/(c+d),m=e.height-t.width,g=Math.sqrt(d/m),v=[],v.push(p,g),b=n?c/(t.width-1)/(d/m):p/t.width/((1-p)/m),w=[],w.push(b,m),y=r.multiply(t).inverse(),_=[],k=y.height-1;k>=0;k--)_.push(Math.sqrt(y.data[k][k]*d/m));return this.asMatrix([s,_,v,w,f])}function se(e,t,n,i){return oe.call(this,e.map(Math.log),t,n,i).map(Math.exp)}function ae(e,t,n,i){var r,o,s=0;return t||(t=e.map(function(){return++s})),i&&(t=t.clone(),t.eachRow(function(e){t.data[e].unshift(1)}),++t.width),r=t.transpose(),o=r.multiply(t).inverse().multiply(r).multiply(e),n?i&&(n=n.clone(),n.eachRow(function(e){n.data[e].unshift(1)}),++n.width):n=t,n.multiply(o)}function le(e,t,n,i){return ae.call(this,e.map(Math.log),t,n,i).map(Math.exp)}function ue(e,t,n,i){var r,o,s,a,l,u=n||20,c=i||1e-7,h=t;for(r=1;r<=u;r++)if(o=e(h),s=o[0],a=o[1],l=s/a,h-=l,Math.abs(l)<c)return h;return new et("NUM")}function ce(e,t,n,i,r){var o=Math.pow(1+e,t),s=e?(o-1)/e:t;return-(i*o+n*s*(1+e*r))}function he(e,t,n,i,r){if(!e)return-i-n*t;var o=Math.pow(1+e,t);return-(i+n*(o-1)/e*(1+e*r))/o}function de(e,t,n,i,r){if(!e)return-(i+n)/t;var o=Math.pow(1+e,t);return-e*(i+n*o)/((1+e*r)*(o-1))}function fe(e,t,n,i,r){if(!e)return-(i+n)/t;var o=t*(1+e*r);return Math.log((o-i*e)/(o+n*e))/Math.log(1+e)}function pe(e,t,n,i,r,o){function s(o){var s=Math.pow(1+o,e-1),a=s*(1+o);return[n*a+t*(1/o+r)*(a-1)+i,e*n*s+t*(-(a-1)/(o*o)+(1/o+r)*e*s)]}return ue(s,o)}function me(e,t,n,i,r,o){var s,a;return 1==o&&1==t?0:(s=de(e,n,i,r,o),a=ce(e,t-1,s,i,o)*e,o?a/(1+e):a)}function ge(e,t,n,i,r,o){var s=de(e,n,i,r,o);return s-me(e,t,n,i,r,o)}function ve(e,t,n,i,r,o){var s,a,l,u,c,h;return 1==o&&(i--,r--),s=Math.pow(1+e,t),a=Math.pow(1+e,i-1),l=Math.pow(1+e,r),u=e*n*s/(s-1),c=a*n-(a-1)/e*u,h=l*n-(l-1)/e*u,h-c}function be(e,t,n,i,r,o){var s,a=0;for(s=i;s<=r;s++)a+=me(e,s,t,n,0,o);return a}function we(e,t){var n,i,r=0;for(n=0,i=t.length;n<i;n++)r+=t[n]*Math.pow(1+e,-n-1);return r}function ye(e,t){function n(t){var n,i,r=0,o=0;for(n=0,i=e.length;n<i;n++)r+=e[n]*Math.pow(1+t,-n-1),o+=-n*e[n]*Math.pow(1+t,-n-2);return[r,o]}return ue(n,t)}function _e(e,t){return Math.pow(1+e/t,t)-1}function ke(e,t){return t*(Math.pow(e+1,1/t)-1)}function xe(e,t,n){var i,r,o=0;for(i=0,r=t.length;i<r;i++)o+=t[i]*Math.pow(1+e,(n[0]-n[i])/365);return o}function Ce(e,t,n){function i(n){var i,r,o,s=e[0],a=0;for(i=1,r=e.length;i<r;i++)o=(t[0]-t[i])/365,s+=e[i]*Math.pow(1+n,o),a+=o*e[i]*Math.pow(1+n,o-1);return[s,a]}return ue(i,n)}function Fe(e,t,n,i){var r=-i*e;return r*(1-t/n)}function Re(e,t,n,i,r){var o,s,a=1-Math.pow(t/e,1/n);if(a=Math.floor(1e3*a+.5)/1e3,o=e*a*r/12,1==i)return o;for(s=1;s<n;s++){if(s==i-1)return(e-o)*a;o+=(e-o)*a}return(e-o)*a*(12-r)/12}function Se(e,t,n,i,r){var o=r/n,s=-e*(Math.pow(1-o,i-1)-1),a=(e-s)*o;return a=Math.min(a,Math.max(0,e-s-t))}function Ae(e,t,n){return(e-t)/n}function De(e,t,n,i){return(e-t)*(n-i+1)*2/(n*(n+1))}function Ee(e,t,n,i,r,o,s){function a(e,n){var i,r,o;return r=1==f?1==n?e:0:e*Math.pow(1-f,n-1),o=e*Math.pow(1-f,n),i=o<t?r-t:r-o,i<0?0:i}function l(e,n,i){var r,o,s,l=e-t,u=Math.ceil(i),c=0,h=0,d=!1;for(o=1;o<=u;o++)d?r=c:(s=a(e,o),c=l/(n-o+1),c>s?(r=c,d=!0):(r=s,l-=s)),o==u&&(r*=i+1-u),h+=r;return h}var u,c,h,d,f=o>=n?1:o/n,p=Math.floor(i),m=Math.ceil(r),g=0;if(s)for(u=p+1;u<=m;u++)c=a(e,u),u==p+1?c*=Math.min(r,p+1)-i:u==m&&(c*=r+1-m),g+=c;else h=n,i!=Math.floor(i)&&o>1&&i>=n/2&&(d=i-n/2,i=n/2,r-=d,h+=1),e-=l(e,h,i),g=l(e,n-i,r-i);return g}function Be(e,t){var n=nt(e),i=n.month+t,r=n.year+Math.floor(i/12);return i%=12,i<0&&(i+=12),n=Math.min(n.date,rt(r,i)),tt(r,i,n)}function Me(e,t,n){return 1==n||2==n||3==n?t-e:ot(e,t,n)}function Te(e,t,n){var i,r,o,s=nt(e),a=nt(t),l=a.year-s.year;l>0&&(l=(l-1)*n),o=12/n;do l++,i=Be(t,-l*o);while(e<i);return l--,r=Be(t,-l*o),[i,r]}function Ie(e,t,n){var i,r,o,s=nt(t),a=nt(e),l=s.year-a.year;for(l>0&&(l=(l-1)*n),i=e,o=12/n;t>i;)r=i,l++,i=Be(e,l*o);return[r,i]}function Le(e,t,n,i){var r=Te(e,t,n)[0];return Me(r,e,i)}function Ne(e,t,n,i){if(1==i){var r=Te(e,t,n);return Me(r[0],r[1],1)}return 3==i?365/n:360/n}function ze(e,t,n,i){var r=Te(e,t,n)[1];return Me(e,r,i)}function Pe(e,t,n){return Te(e,t,n)[0]}function He(e,t,n){return Te(e,t,n)[1]}function Oe(e,t,n){var i=nt(e),r=nt(t),o=12*(r.year-i.year)+r.month-i.month;return 1+(o*n/12|0)}function $e(e,t){return 3==t?365:1==t?it(e)?366:365:360}function Ve(e,t,n,i,r){var o=$e(nt(t).year,r);return n*i*Me(e,t,r)/o}function Ue(e,t,n,i,r,o,s,a){function l(e,t){return(t-e)*o/v|0}var u,c,h,d,f,p,m=0,g=r*i/o,v=s%2===0?360:365;return n<=t?(u=Te(n,t,o),c=u[0],h=u[1],c<=e?g*Me(e,n,s)/Me(c,h,s):(u=Te(e,c,o),d=u[0],f=u[1],p=l(f,n),g*(p+Me(e,f,s)/Me(d,f,s)+(n<h?Me(c,n,s)/Me(c,h,s):0)))):(u=Ie(t,n,o),c=u[0],h=u[1],p=l(t,n),m=h==n?g*p:g*(p+Me(c,n,s)/Me(c,h,s)),a?(u=Te(e,t,o),c=u[0],h=u[1],p=l(e,t),m+=g*(p+Me(e,h,s)/Me(c,h,s))):m)}function We(e,t,n,i,r){var o=r%2===0?360:it(nt(e).year)?366:365;return(i-n)/i*o/Me(e,t,r)}function je(e,t,n,i,r){var o=r%2===0?360:it(nt(e).year)?366:365;return(i-n)/n*o/Me(e,t,r)}function qe(e,t,n,i,r){var o=r%2===0?360:it(nt(e).year)?366:365;return n/(1-i*Me(e,t,r)/o)}function Ke(e,t,n,i,r,o,s){var a=1+((t-e)*o/(s%2===0?360:365)|0),l=Te(e,t,o),u=l[0],c=l[1],h=Me(u,e,s),d=Me(e,c,s),f=Me(u,c,s),p=100*n/o,m=i/o,g=d/f;return 1==a?(r+p)/(1+g*m)-h/f*p:r/Math.pow(1+m,a-1+g)+p*Math.pow(1+m,1-a-g)*(Math.pow(1+m,a)-1)/m-h/f*p}function Je(e,t,n,i,r){var o=Me(e,t,r),s=$e(nt(t).year,r);return i-n*i*o/s}function Ge(e,t){for(var n=e.length;--n>=0;)"number"==typeof e[n]&&"number"==typeof t[n]||(e.splice(n,1),t.splice(n,1))}var Ye,Xe,Qe,Ze,et,tt,nt,it,rt,ot,st,at,lt,ut,ct;kendo.support.browser.msie&&kendo.support.browser.version<9||(Ye=kendo.spreadsheet,Xe=Ye.calc,Qe=Xe.runtime,Ze=Qe.defineFunction,et=Qe.CalcError,tt=Qe.packDate,nt=Qe.unpackDate,it=Qe.isLeapYear,rt=Qe.daysInMonth,ot=Qe._days_360,Ze("ERF",function(t,n){return null==n?e(t):e(n)-e(t)}).args([["lower_limit","number"],["upper_limit",["or","number","null"]]]),Ze("ERFC",t).args([["x","number"]]),Ze("GAMMALN",n).args([["x","number++"]]),Ze("GAMMA",i).args([["x","number"]]),Ze("GAMMA.DIST",u).args([["x","number+"],["alpha","number++"],["beta","number++"],["cumulative","logical"]]),
Ze("GAMMA.INV",c).args([["p",["and","number",["[between]",0,1]]],["alpha","number++"],["beta","number++"]]),Ze("NORM.S.DIST",h).args([["z","number"],["cumulative","logical"]]),Ze("NORM.S.INV",d).args([["p",["and","number",["[between]",0,1]]]]),Ze("NORM.DIST",f).args([["x","number"],["mean","number"],["stddev","number++"],["cumulative","logical"]]),Ze("NORM.INV",p).args([["p",["and","number",["[between]",0,1]]],["mean","number"],["stddev","number++"]]),Ze("BETADIST",w).args([["x","number"],["alpha","number++"],["beta","number++"],["A",["or","number",["null",0]]],["B",["or","number",["null",1]]],["?",["assert","$x >= $A","NUM"]],["?",["assert","$x <= $B","NUM"]],["?",["assert","$A < $B","NUM"]]]),Ze("BETA.DIST",y).args([["x","number"],["alpha","number++"],["beta","number++"],["cumulative","logical"],["A",["or","number",["null",0]]],["B",["or","number",["null",1]]],["?",["assert","$x >= $A","NUM"]],["?",["assert","$x <= $B","NUM"]],["?",["assert","$A < $B","NUM"]]]),Ze("BETA.INV",_).args([["p",["and","number",["[between]",0,1]]],["alpha","number++"],["beta","number++"],["A",["or","number",["null",0]]],["B",["or","number",["null",1]]]]),Ze("CHISQ.DIST",k).args([["x","number+"],["deg_freedom","integer++"],["cumulative","logical"]]),Ze("CHISQ.DIST.RT",x).args([["x","number+"],["deg_freedom","integer++"]]),Ze("CHISQ.INV",C).args([["p",["and","number",["[between]",0,1]]],["deg_freedom","integer++"]]),Ze("CHISQ.INV.RT",F).args([["p",["and","number",["[between]",0,1]]],["deg_freedom","integer++"]]),Ze("CHISQ.TEST",function(e,t){return R(e.data,t.data)}).args([["actual_range","matrix"],["expected_range","matrix"],["?",["assert","$actual_range.width == $expected_range.width"]],["?",["assert","$actual_range.height == $expected_range.height"]]]),Ze("EXPON.DIST",S).args([["x","number+"],["lambda","number++"],["cumulative","logical"]]),Ze("POISSON.DIST",A).args([["x","integer+"],["mean","number+"],["cumulative","logical"]]),Ze("F.DIST",D).args([["x","number+"],["deg_freedom1","integer++"],["deg_freedom2","integer++"],["cumulative","logical"]]),Ze("F.DIST.RT",E).args([["x","number+"],["deg_freedom1","integer++"],["deg_freedom2","integer++"]]),Ze("F.INV",M).args([["p",["and","number",["[between]",0,1]]],["deg_freedom1","integer++"],["deg_freedom2","integer++"]]),Ze("F.INV.RT",B).args([["p",["and","number",["[between]",0,1]]],["deg_freedom1","integer++"],["deg_freedom2","integer++"]]),Ze("F.TEST",L).args([["array1",["collect","number",1]],["array2",["collect","number",1]],["?",["assert","$array1.length >= 2","DIV/0"]],["?",["assert","$array2.length >= 2","DIV/0"]]]),Ze("FISHER",N).args([["x",["and","number",["(between)",-1,1]]]]),Ze("FISHERINV",z).args([["y","number"]]),Ze("T.DIST",P).args([["x","number"],["deg_freedom","integer++"],["cumulative","logical"]]),Ze("T.DIST.RT",H).args([["x","number"],["deg_freedom","integer++"]]),Ze("T.DIST.2T",O).args([["x","number+"],["deg_freedom","integer++"]]),Ze("T.INV",$).args([["p",["and","number",["(between]",0,1]]],["deg_freedom","integer++"]]),Ze("T.INV.2T",V).args([["p",["and","number",["(between]",0,1]]],["deg_freedom","integer++"]]),Ze("T.TEST",U).args([["array1",["collect","number",1]],["array2",["collect","number",1]],["tails",["and","integer",["values",1,2]]],["type",["and","integer",["values",1,2,3]]],["?",["assert","$type != 1 || $array1.length == $array2.length","N/A"]],["?",["assert","$array1.length >= 2","DIV/0"]],["?",["assert","$array2.length >= 2","DIV/0"]]]),Ze("CONFIDENCE.T",W).args([["alpha",["and","number",["(between)",0,1]]],["standard_dev","number++"],["size",["and","integer++",["assert","$size != 1","DIV/0"]]]]),Ze("CONFIDENCE.NORM",j).args([["alpha",["and","number",["(between)",0,1]]],["standard_dev","number++"],["size",["and","integer++"]]]),Ze("GAUSS",q).args([["z","number"]]),Ze("PHI",K).args([["x","number"]]),Ze("LOGNORM.DIST",J).args([["x","number++"],["mean","number"],["standard_dev","number++"],["cumulative","logical"]]),Ze("LOGNORM.INV",G).args([["probability",["and","number",["(between)",0,1]]],["mean","number"],["standard_dev","number++"]]),Ze("PROB",Y).args([["x_range",["collect","number",1]],["prob_range",["collect","number",1]],["lower_limit","number"],["upper_limit",["or","number",["null","$lower_limit"]]],["?",["assert","$prob_range.length == $x_range.length","N/A"]]]),Ze("SLOPE",X).args([["known_y",["collect","number",1]],["known_x",["collect","number",1]],["?",["assert","$known_x.length == $known_y.length","N/A"]],["?",["assert","$known_x.length > 0 && $known_y.length > 0","N/A"]]]),Ze("INTERCEPT",Q).args([["known_y",["collect","number",1]],["known_x",["collect","number",1]],["?",["assert","$known_x.length == $known_y.length","N/A"]],["?",["assert","$known_x.length > 0 && $known_y.length > 0","N/A"]]]),Ze("PEARSON",Z).args([["array1",["collect!","anything",1]],["array2",["collect!","anything",1]],["?",["assert","$array2.length == $array1.length","N/A"]],["?",["assert","$array2.length > 0 && $array1.length > 0","N/A"]]]),Ze("RSQ",ee).args([["known_y",["collect","number",1]],["known_x",["collect","number",1]],["?",["assert","$known_x.length == $known_y.length","N/A"]],["?",["assert","$known_x.length > 0 && $known_y.length > 0","N/A"]],["?",["assert","$known_x.length != 1 && $known_y.length != 1","N/A"]]]),Ze("STEYX",te).args([["known_y",["collect","number",1]],["known_x",["collect","number",1]],["?",["assert","$known_x.length == $known_y.length","N/A"]],["?",["assert","$known_x.length >= 3 && $known_y.length >= 3","DIV/0"]]]),Ze("FORECAST",ne).args([["x","number"],["known_y",["collect","number",1]],["known_x",["collect","number",1]],["?",["assert","$known_x.length == $known_y.length","N/A"]],["?",["assert","$known_x.length > 0 && $known_y.length > 0","N/A"]]]),Ze("LINEST",oe).args([["known_y","matrix"],["known_x",["or","matrix","null"]],["const",["or","logical",["null",!0]]],["stats",["or","logical",["null",!1]]]]),Ze("LOGEST",se).args([["known_y","matrix"],["known_x",["or","matrix","null"]],["const",["or","logical",["null",!0]]],["stats",["or","logical",["null",!1]]]]),Ze("TREND",ae).args([["known_y","matrix"],["known_x",["or","matrix","null"]],["new_x",["or","matrix","null"]],["const",["or","logical",["null",!0]]]]),Ze("GROWTH",le).args([["known_y","matrix"],["known_x",["or","matrix","null"]],["new_x",["or","matrix","null"]],["const",["or","logical",["null",!0]]]]),Ze("FV",ce).args([["rate","number"],["nper","number"],["pmt",["or","number",["null",0]]],["pv",["or","number",["null",0]]],["type",["or",["values",0,1],["null",0]]],["?",["assert","$pmt || $pv"]]]),Ze("PV",he).args([["rate","number"],["nper","number"],["pmt",["or","number",["null",0]]],["fv",["or","number",["null",0]]],["type",["or",["values",0,1],["null",0]]]]),Ze("PMT",de).args([["rate","number"],["nper","number"],["pmt","number"],["fv",["or","number",["null",0]]],["type",["or",["values",0,1],["null",0]]]]),Ze("NPER",fe).args([["rate","number"],["pmt","number"],["pv","number"],["fv",["or","number",["null",0]]],["type",["or",["values",0,1],["null",0]]]]),Ze("RATE",pe).args([["nper","number"],["pmt",["or","number",["null",0]]],["pv","number"],["fv",["or","number",["null",0]]],["type",["or",["values",0,1],["null",0]]],["guess",["or","number++",["null",.01]]],["?",["assert","$pmt || $fv"]]]),Ze("IPMT",me).args([["rate","number"],["per","number++"],["nper","number++"],["pv","number"],["fv",["or","number",["null",0]]],["type",["or",["values",0,1],["null",0]]],["?",["assert","$per >= 1 && $per <= $nper"]]]),Ze("PPMT",ge).args([["rate","number"],["per","number++"],["nper","number++"],["pv","number"],["fv",["or","number",["null",0]]],["type",["or",["values",0,1],["null",0]]],["?",["assert","$per >= 1 && $per <= $nper"]]]),Ze("CUMPRINC",ve).args([["rate","number++"],["nper","number++"],["pv","number++"],["start_period","number++"],["end_period","number++"],["type",["or",["values",0,1],["null",0]]],["?",["assert","$end_period >= $start_period","NUM"]]]),Ze("CUMIPMT",be).args([["rate","number++"],["nper","number++"],["pv","number++"],["start_period","number++"],["end_period","number++"],["type",["or",["values",0,1],["null",0]]],["?",["assert","$end_period >= $start_period","NUM"]]]),Ze("NPV",we).args([["rate","number"],["values",["collect","number"]],["?",["assert","$values.length > 0","N/A"]]]),Ze("IRR",ye).args([["values",["collect","number",1]],["guess",["or","number",["null",.1]]]]),Ze("EFFECT",_e).args([["nominal_rate","number++"],["npery","integer++"]]),Ze("NOMINAL",ke).args([["effect_rate","number++"],["npery","integer++"]]),Ze("XNPV",xe).args([["rate","number"],["values",["collect","number",1]],["dates",["collect","date",1]],["?",["assert","$values.length == $dates.length","NUM"]]]),Ze("XIRR",Ce).args([["values",["collect","number",1]],["dates",["collect","date",1]],["guess",["or","number",["null",.1]]],["?",["assert","$values.length == $dates.length","NUM"]]]),Ze("ISPMT",Fe).args([["rate","number"],["per","number++"],["nper","number++"],["pv","number"],["?",["assert","$per >= 1 && $per <= $nper"]]]),Ze("DB",Re).args([["cost","number"],["salvage","number"],["life","number++"],["period","number++"],["month",["or","number",["null",12]]]]),Ze("DDB",Se).args([["cost","number"],["salvage","number"],["life","number++"],["period","number++"],["factor",["or","number",["null",2]]]]),Ze("SLN",Ae).args([["cost","number"],["salvage","number"],["life","number++"]]),Ze("SYD",De).args([["cost","number"],["salvage","number"],["life","number++"],["per","number++"]]),Ze("VDB",Ee).args([["cost","number+"],["salvage","number+"],["life","number++"],["start_period","number+"],["end_period","number+"],["factor",["or","number+",["null",2]]],["no_switch",["or","logical",["null",!1]]],["?",["assert","$end_period >= $start_period","NUM"]]]),st=[["settlement","date"],["maturity","date"],["frequency",["and","integer",["values",1,2,4]]],["basis",["or",["null",0],["and","integer",["values",0,1,2,3,4]]]],["?",["assert","$settlement < $maturity","NUM"]]],Ze("COUPDAYBS",Le).args(st),Ze("COUPDAYS",Ne).args(st),Ze("COUPDAYSNC",ze).args(st),Ze("COUPPCD",Pe).args(st),Ze("COUPNCD",He).args(st),Ze("COUPNUM",Oe).args(st),Ze("ACCRINTM",Ve).args([["issue","date"],["settlement","date"],["rate","number++"],["par",["or",["null",1e3],"number++"]],["basis",["or",["null",0],["and","integer",["values",0,1,2,3,4]]]],["?",["assert","$issue < $settlement","NUM"]]]),Ze("ACCRINT",Ue).args([["issue","date"],["first_interest","date"],["settlement","date"],["rate","number++"],["par",["or",["null",1e3],"number++"]],["frequency",["and","integer",["values",1,2,4]]],["basis",["or",["null",0],["and","integer",["values",0,1,2,3,4]]]],["calc_method",["or","logical",["null",!0]]],["?",["assert","$issue < $settlement","NUM"]]]),Ze("DISC",We).args([["settlement","date"],["maturity","date"],["pr","number++"],["redemption","number++"],["basis",["or",["null",0],["and","integer",["values",0,1,2,3,4]]]],["?",["assert","$settlement < $maturity","NUM"]]]),Ze("INTRATE",je).args([["settlement","date"],["maturity","date"],["investment","number++"],["redemption","number++"],["basis",["or",["null",0],["and","integer",["values",0,1,2,3,4]]]],["?",["assert","$settlement < $maturity","NUM"]]]),Ze("RECEIVED",qe).args([["settlement","date"],["maturity","date"],["investment","number++"],["discount","number++"],["basis",["or",["null",0],["and","integer",["values",0,1,2,3,4]]]],["?",["assert","$settlement < $maturity","NUM"]]]),Ze("PRICE",Ke).args([["settlement","date"],["maturity","date"],["rate","number++"],["yld","number++"],["redemption","number++"],["frequency",["and","integer",["values",1,2,4]]],["basis",["or",["null",0],["and","integer",["values",0,1,2,3,4]]]],["?",["assert","$settlement < $maturity","NUM"]]]),Ze("PRICEDISC",Je).args([["settlement","date"],["maturity","date"],["discount","number++"],["redemption","number++"],["basis",["or",["null",0],["and","integer",["values",0,1,2,3,4]]]],["?",["assert","$settlement < $maturity","NUM"]]]),at=300,lt=2.2204e-16,ut=1e-30,ct=Math.abs)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/borderpalette.min",["kendo.core.min","kendo.colorpicker.min","kendo.popup.min"],e)}(function(){!function(e){function t(e){return function(t){return t.preventDefault(),e.apply(this,arguments)}}var n,i,r,o,s,a;e.support.browser.msie&&e.support.browser.version<9||(n=e.jQuery,i=["allBorders","insideBorders","insideHorizontalBorders","insideVerticalBorders","outsideBorders","leftBorder","topBorder","rightBorder","bottomBorder","noBorders"],r=e.spreadsheet.messages.borderPalette={allBorders:"All borders",insideBorders:"Inside borders",insideHorizontalBorders:"Inside horizontal borders",insideVerticalBorders:"Inside vertical borders",outsideBorders:"Outside borders",leftBorder:"Left border",topBorder:"Top border",rightBorder:"Right border",bottomBorder:"Bottom border",noBorders:"No border"},o=e.spreadsheet.messages.colorPicker={reset:"Reset color",customColor:"Custom color...",apply:"Apply",cancel:"Cancel"},s=e.ui.Widget.extend({init:function(n,i){e.ui.Widget.call(this,n,i),this.element=n,this.color=i.color,this._resetButton(),this._colorPalette(),this._customColorPalette(),this._customColorButton(),this.resetButton.on("click",t(this.resetColor.bind(this))),this.customColorButton.on("click",t(this.customColor.bind(this)))},options:{name:"ColorChooser"},events:["change"],destroy:function(){e.unbind(this.dialog.element.find(".k-action-buttons")),this.dialog.destroy(),this.colorPalette.destroy(),this.resetButton.off("click"),this.customColorButton.off("click")},value:function(e){return void 0===e?this.color:(this.color=e,this.customColorButton.find(".k-icon").css("background-color",this.color),this.colorPalette.value(null),this.flatColorPicker.value(this.color),void 0)},_change:function(e){this.color=e,this.trigger("change",{value:e})},_colorPalette:function(){var e=n("<div />",{"class":"k-spreadsheet-color-palette"}),t=this.colorPalette=n("<div />").kendoColorPalette({palette:["#ffffff","#000000","#d6ecff","#4e5b6f","#7fd13b","#ea157a","#feb80a","#00addc","#738ac8","#1ab39f","#f2f2f2","#7f7f7f","#a7d6ff","#d9dde4","#e5f5d7","#fad0e4","#fef0cd","#c5f2ff","#e2e7f4","#c9f7f1","#d8d8d8","#595959","#60b5ff","#b3bcca","#cbecb0","#f6a1c9","#fee29c","#8be6ff","#c7d0e9","#94efe3","#bfbfbf","#3f3f3f","#007dea","#8d9baf","#b2e389","#f272af","#fed46b","#51d9ff","#aab8de","#5fe7d5","#a5a5a5","#262626","#003e75","#3a4453","#5ea226","#af0f5b","#c58c00","#0081a5","#425ea9","#138677","#7f7f7f","#0c0c0c","#00192e","#272d37","#3f6c19","#750a3d","#835d00","#00566e","#2c3f71","#0c594f"],value:this.color,change:function(e){this.customColorButton.find(".k-icon").css("background-color","transparent"),this.flatColorPicker.value(null),this._change(e.value)}.bind(this)}).data("kendoColorPalette");e.append(t.wrapper).appendTo(this.element)},_customColorPalette:function(){var t,i,r=n("<div />",{"class":"k-spreadsheet-window",html:"<div></div><div class='k-action-buttons'><button class='k-button k-primary' data-bind='click: apply'>"+o.apply+"</button><button class='k-button' data-bind='click: close'>"+o.cancel+"</button></div>"}),s=this.dialog=r.appendTo(document.body).kendoWindow({animation:!1,scrollable:!1,resizable:!1,maximizable:!1,modal:!0,visible:!1,width:268,open:function(){this.center()}}).data("kendoWindow");s.one("activate",function(){this.element.find("[data-role=flatcolorpicker]").data("kendoFlatColorPicker")._hueSlider.resize()}),t=this.flatColorPicker=s.element.children().first().kendoFlatColorPicker().data("kendoFlatColorPicker"),i=e.observable({apply:function(){this.customColorButton.find(".k-icon").css("background-color",t.value()),this.colorPalette.value(null),this._change(t.value()),s.close()}.bind(this),close:function(){t.value(null),s.close()}}),e.bind(s.element.find(".k-action-buttons"),i)},_resetButton:function(){this.resetButton=n("<a class='k-button k-reset-color' href='#'><span class='k-icon k-i-reset-color'></span>"+o.reset+"</a>").appendTo(this.element)},_customColorButton:function(){this.customColorButton=n("<a class='k-button k-custom-color' href='#'><span class='k-icon'></span>"+o.customColor+"</a>").appendTo(this.element)},resetColor:function(){this.colorPalette.value(null),this.flatColorPicker.value(null),this._change(null)},customColor:function(){this.dialog.open()}}),a=e.ui.Widget.extend({init:function(n,i){e.ui.Widget.call(this,n,i),this.element=n,this.color="#000",this.element.addClass("k-spreadsheet-border-palette"),this._borderTypePalette(),this._borderColorPalette(),this.element.on("click",".k-spreadsheet-border-type-palette .k-button",t(this._click.bind(this)))},options:{name:"BorderPalette"},events:["change"],destroy:function(){this.colorChooser.destroy(),this.element.off("click")},_borderTypePalette:function(){var t=r,o=i.map(function(n){return'<a title="'+t[n]+'" aria-label="'+t[n]+'" href="#" data-border-type="'+n+'" class="k-button k-button-icon"><span class="k-icon k-i-'+e.toHyphens(n)+'"></span></a>'}).join(""),s=n("<div />",{"class":"k-spreadsheet-border-type-palette",html:o});s.appendTo(this.element)},_borderColorPalette:function(){var e=n("<div />",{"class":"k-spreadsheet-border-color-palette"});e.appendTo(this.element),this.colorChooser=new s(e,{color:this.color,change:this._colorChange.bind(this)})},_click:function(e){this.type=n(e.currentTarget).data("borderType"),this.trigger("change",{type:this.type,color:this.color})},_colorChange:function(e){this.color=e.value,this.type&&this.trigger("change",{type:this.type,color:this.color})}}),e.spreadsheet.ColorChooser=s,e.spreadsheet.BorderPalette=a)}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/toolbar.min",["kendo.toolbar.min","kendo.colorpicker.min","kendo.combobox.min","kendo.dropdownlist.min","kendo.popup.min","spreadsheet/borderpalette.min"],e)}(function(){!function(e){var t,n,i,r,o,s,a,l,u,c,h,d,f,p,m,g,v,b,w,y,_,k,x,C,F,R,S,A,D,E,B,M,T,I,L;e.support.browser.msie&&e.support.browser.version<9||(t=e.jQuery,n=e.ui.ToolBar,i=e.spreadsheet.messages.toolbar={addColumnLeft:"Add column left",addColumnRight:"Add column right",addRowAbove:"Add row above",addRowBelow:"Add row below",alignment:"Alignment",alignmentButtons:{justtifyLeft:"Align left",justifyCenter:"Center",justifyRight:"Align right",justifyFull:"Justify",alignTop:"Align top",alignMiddle:"Align middle",alignBottom:"Align bottom"},backgroundColor:"Background",bold:"Bold",borders:"Borders",copy:"Copy",cut:"Cut",deleteColumn:"Delete column",deleteRow:"Delete row",filter:"Filter",fontFamily:"Font",fontSize:"Font size",format:"Custom format...",formatTypes:{automatic:"Automatic",text:"Text",number:"Number",percent:"Percent",financial:"Financial",currency:"Currency",date:"Date",time:"Time",dateTime:"Date time",duration:"Duration",moreFormats:"More formats..."},formatDecreaseDecimal:"Decrease decimal",formatIncreaseDecimal:"Increase decimal",freeze:"Freeze panes",freezeButtons:{freezePanes:"Freeze panes",freezeRows:"Freeze rows",freezeColumns:"Freeze columns",unfreeze:"Unfreeze panes"},insertComment:"Insert comment",insertImage:"Insert image",italic:"Italic",merge:"Merge cells",mergeButtons:{mergeCells:"Merge all",mergeHorizontally:"Merge horizontally",mergeVertically:"Merge vertically",unmerge:"Unmerge"},open:"Open...",paste:"Paste",quickAccess:{redo:"Redo",undo:"Undo"},exportAs:"Export...",toggleGridlines:"Toggle gridlines",sort:"Sort",sortButtons:{sortRangeAsc:"Sort range A to Z",sortRangeDesc:"Sort range Z to A"},textColor:"Text Color",textWrap:"Wrap text",underline:"Underline",validation:"Data validation...",hyperlink:"Link"},r={home:["open","exportAs",["cut","copy","paste"],["bold","italic","underline"],"hyperlink","insertComment","insertImage","backgroundColor","textColor","borders","fontSize","fontFamily","alignment","textWrap",["formatDecreaseDecimal","formatIncreaseDecimal"],"format","merge","freeze","filter","toggleGridlines"],insert:[["addColumnLeft","addColumnRight","addRowBelow","addRowAbove"],["deleteColumn","deleteRow"]],data:["sort","filter","validation"]},o={open:{type:"open",overflow:"never",iconClass:"file-excel"},exportAs:{type:"exportAsDialog",dialogName:"exportAs",overflow:"never",text:"",iconClass:"file-excel"},bold:{type:"button",command:"PropertyChangeCommand",property:"bold",value:!0,iconClass:"bold",togglable:!0},italic:{type:"button",command:"PropertyChangeCommand",property:"italic",value:!0,iconClass:"italic",togglable:!0},underline:{type:"button",command:"PropertyChangeCommand",property:"underline",value:!0,iconClass:"underline",togglable:!0},formatDecreaseDecimal:{type:"button",command:"AdjustDecimalsCommand",value:-1,iconClass:"decimal-decrease"},formatIncreaseDecimal:{type:"button",command:"AdjustDecimalsCommand",value:1,iconClass:"decimal-increase"},textWrap:{type:"button",command:"TextWrapCommand",property:"wrap",value:!0,iconClass:"text-wrap",togglable:!0},cut:{type:"button",command:"ToolbarCutCommand",iconClass:"cut"},copy:{type:"button",command:"ToolbarCopyCommand",iconClass:"copy"},paste:{type:"button",command:"ToolbarPasteCommand",iconClass:"paste"},separator:{type:"separator"},alignment:{type:"alignment",iconClass:"align-left"},backgroundColor:{type:"colorPicker",property:"background",iconClass:"paint"},textColor:{type:"colorPicker",property:"color",iconClass:"foreground-color"},fontFamily:{type:"fontFamily",property:"fontFamily",iconClass:"font-family"},fontSize:{type:"fontSize",property:"fontSize",iconClass:"font-size"},format:{type:"format",property:"format",iconClass:"custom-format"},filter:{type:"filter",property:"hasFilter",iconClass:"filter"},merge:{type:"merge",iconClass:"cells-merge"},freeze:{type:"freeze",iconClass:"pane-freeze"},borders:{type:"borders",iconClass:"borders-all"},formatCells:{type:"dialog",dialogName:"formatCells",overflow:"never"},hyperlink:{type:"dialog",dialogName:"hyperlink",iconClass:"link-horizontal",overflow:"never",text:""},toggleGridlines:{type:"button",command:"GridLinesChangeCommand",property:"gridLines",value:!0,iconClass:"border-no",togglable:!0},insertComment:{type:"dialog",dialogName:"insertComment",property:"comment",togglable:!0,overflow:"never",iconClass:"comment",text:""},insertImage:{type:"dialog",dialogName:"insertImage",overflow:"never",iconClass:"image",text:""},addColumnLeft:{type:"button",command:"AddColumnCommand",value:"left",iconClass:"table-column-insert-left"},addColumnRight:{type:"button",command:"AddColumnCommand",value:"right",iconClass:"table-column-insert-right"},addRowBelow:{type:"button",command:"AddRowCommand",value:"below",iconClass:"table-row-insert-below"},addRowAbove:{type:"button",command:"AddRowCommand",value:"above",iconClass:"table-row-insert-above"},deleteColumn:{type:"button",command:"DeleteColumnCommand",iconClass:"table-column-delete"},deleteRow:{type:"button",command:"DeleteRowCommand",iconClass:"table-row-delete"},sort:{type:"sort",iconClass:"sort-desc"},validation:{type:"dialog",dialogName:"validation",iconClass:"exception",overflow:"never"}},s=n.extend({init:function(e,i){var r,o;i.items=this._expandTools(i.tools||s.prototype.options.tools[i.toolbarName]),n.fn.init.call(this,e,i),r=this._click.bind(this),this.element.addClass("k-spreadsheet-toolbar"),this._addSeparators(this.element),o=this,this.element.on("keydown",function(e){var n;9===e.keyCode&&(n=o._nextTool(e.shiftKey?-1:1),n&&(document.activeElement.blur(),t(n).is(".k-upload-button")&&t(n).addClass("k-state-focused"),t(n).find("input").length?t(n).find("input").focus():n.focus(),e.preventDefault()))}),this.element.on("focusout",function(){t(this).find(".k-toolbar-first-visible").removeClass("k-state-focused")}),this.bind({click:r,toggle:r})},_nextTool:function(e){var n=this,i=n.element.find(".k-widget, .k-button, .k-button-group > a"),r=i.index(t(document.activeElement).closest(".k-widget, .k-button, .k-button-group > a"));if(r>0)return i[r+e]},_addSeparators:function(e){var t=e.children(".k-widget, a.k-button, .k-button-group");t.before("<span class='k-separator' />")},_expandTools:function(e){function n(e){var r=t.isPlainObject(e)?e:o[e]||{},s="k-icon k-i-"+r.iconClass,a=r.type,l={button:{showText:"overflow"},colorPicker:{toolIcon:s,spriteCssClass:s},borders:{spriteCssClass:s},alignment:{spriteCssClass:s},merge:{spriteCssClass:s},freeze:{spriteCssClass:s}},u=t.extend({name:r.name||e,text:i[r.name||e],icon:r.iconClass,attributes:{title:i[r.name||e],"aria-label":i[r.name||e]}},l[a],r);return"splitButton"==a&&(u.menuButtons=u.menuButtons.map(n)),u.attributes["data-tool"]=e,r.property&&(u.attributes["data-property"]=r.property),u}return e.reduce(function(e,i){return e.push(t.isArray(i)?{type:"buttonGroup",buttons:i.map(n)}:n.call(this,i)),e},[])},_click:function(e){var t,n=e.target.attr("data-tool"),i=o[n]||{},r=i.command;r&&(t={command:r,options:{property:i.property||null,value:i.value||null}},"boolean"==typeof t.options.value&&(t.options.value=!!e.checked||null),this.action(t))},events:["click","toggle","open","close","overflowOpen","overflowClose","action","dialog"],options:{name:"SpreadsheetToolBar",resizable:!0,tools:r},action:function(e){this.trigger("action",e)},dialog:function(e){this.trigger("dialog",e)},refresh:function(t){function n(e,t){var n,i=e.toolbar,r=e.overflow,o=i&&i.options.togglable||r&&r.options.togglable;o&&(n=!1,"boolean"==typeof t?n=t:"string"==typeof t&&(n=i.options.hasOwnProperty("value")?i.options.value===t:null!=t),i.toggle(n),r&&r.toggle(n))}function i(e,t){var n=e.toolbar,i=e.overflow;n&&n.update&&n.update(t),i&&i.update&&i.update(t)}var r,o,s,a,l=t,u=this._tools();for(r=0;r<u.length;r++)o=u[r].property,s=u[r].tool,a=e.isFunction(l[o])?l[o]():l,"gridLines"==o&&(a=l.sheet().showGridLines()),"button"===s.type?n(s,a):i(s,a);this.resize()},_tools:function(){return this.element.find("[data-property]").toArray().map(function(e){return e=t(e),{property:e.attr("data-property"),tool:this._getItem(e)}}.bind(this))},destroy:function(){this.element.find("[data-command],.k-button").each(function(){var e=t(this),n=e.data("instance");n&&n.destroy&&n.destroy()}),n.fn.destroy.call(this)}}),e.spreadsheet.ToolBar=s,a=e.toolbar.Item.extend({init:function(e,n){var i=t("<select />").attr("title",e.attributes.title).attr("aria-label",e.attributes.title).kendoDropDownList({height:"auto"}).data("kendoDropDownList");this.dropDownList=i,this.element=i.wrapper,this.options=e,this.toolbar=n,this.attributes(),this.addUidAttr(),this.addOverflowAttr(),i.bind("open",this._open.bind(this)),i.bind("change",this._change.bind(this)),this.element.width(e.width).attr({"data-command":"PropertyChangeCommand","data-property":e.property})},_open:function(){var t,n=this.dropDownList,i=n.list;i.css({whiteSpace:"nowrap",width:"auto"}),t=i.width(),t>0?t+=20:t=n._listWidth,i.css("width",t+e.support.scrollbar()),n._listWidth=t},_change:function(e){var t=this,n=e.sender,i=n.value(),r=n.dataItem(),o=r?r.popup:void 0;o?setTimeout(function(){t.toolbar.dialog({name:o})}):t.toolbar.action({command:"PropertyChangeCommand",options:{property:this.options.property,value:"null"==i?null:i}})},value:function(e){return void 0===e?this.dropDownList.value():void this.dropDownList.value(e)}}),l=e.toolbar.Item.extend({init:function(e,n){this.element=t("<a href='#' class='k-button k-button-icon'><span class='"+e.spriteCssClass+"'></span><span class='k-icon k-i-arrow-60-down'></span></a>"),this.element.on("click touchend",this.open.bind(this)).attr("data-command",e.command),this.options=e,this.toolbar=n,this.attributes(),this.addUidAttr(),this.addOverflowAttr(),this._popup()},destroy:function(){this.popup.destroy()},open:function(e){e.preventDefault(),this.popup.toggle()},_popup:function(){var e=this.element;this.popup=t("<div class='k-spreadsheet-popup' />").appendTo(e).kendoPopup({anchor:e}).data("kendoPopup")}}),e.toolbar.registerComponent("dialog",e.toolbar.ToolBarButton.extend({init:function(t,n){e.toolbar.ToolBarButton.fn.init.call(this,t,n),this._dialogName=t.dialogName,this.element.bind("click touchend",this.open.bind(this)).data("instance",this)},open:function(){this.toolbar.dialog({name:this._dialogName})}})),e.toolbar.registerComponent("exportAsDialog",e.toolbar.Item.extend({init:function(e,n){this._dialogName=e.dialogName,this.toolbar=n,this._title=e.attributes.title,this.element=t("<button type='button' class='k-button k-button-icon'><span class='k-icon k-i-download' /></button>").attr("title",this._title).attr("aria-label",this._title).data("instance",this),this.element.bind("click",this.open.bind(this)).data("instance",this)},open:function(){this.toolbar.dialog({name:this._dialogName})}})),u=e.toolbar.OverflowButton.extend({init:function(t,n){e.toolbar.OverflowButton.fn.init.call(this,t,n),this.element.on("click touchend",this._click.bind(this)),this.message=this.options.text;var i=this.element.data("button");this.element.data(this.options.type,i)},_click:t.noop}),c=l.extend({init:function(t,n){l.fn.init.call(this,t,n),this.popup.element.addClass("k-spreadsheet-colorpicker"),this.colorChooser=new e.spreadsheet.ColorChooser(this.popup.element,{change:this._colorChange.bind(this)}),this.element.attr({"data-property":t.property}),this.element.data({type:"colorPicker",colorPicker:this,instance:this})},destroy:function(){this.colorChooser.destroy(),l.fn.destroy.call(this)},update:function(e){this.value(e)},value:function(e){this.colorChooser.value(e)},_colorChange:function(e){this.toolbar.action({command:"PropertyChangeCommand",options:{property:this.options.property,value:e.sender.value()}}),this.popup.close()}}),h=u.extend({init:function(e,t){e.iconName="text",u.fn.init.call(this,e,t)},_click:function(){this.toolbar.dialog({name:"colorPicker",options:{title:this.options.property,property:this.options.property}})}}),e.toolbar.registerComponent("colorPicker",c,h),d=[8,9,10,11,12,13,14,16,18,20,22,24,26,28,36,48,72],f=12,p=e.toolbar.Item.extend({init:function(e,n){var i=t("<input />").attr("aria-label",e.attributes.title).kendoComboBox({change:this._valueChange.bind(this),clearButton:!1,dataSource:e.fontSizes||d,value:f}).data("kendoComboBox");this.comboBox=i,this.element=i.wrapper,this.options=e,this.toolbar=n,this.attributes(),this.addUidAttr(),this.addOverflowAttr(),this.element.width(e.width).attr({"data-command":"PropertyChangeCommand","data-property":e.property}),this.element.data({type:"fontSize",fontSize:this})},_valueChange:function(t){this.toolbar.action({command:"PropertyChangeCommand",options:{property:this.options.property,value:e.parseInt(t.sender.value())}})},update:function(t){this.value(e.parseInt(t)||f)},value:function(e){return void 0===e?this.comboBox.value():void this.comboBox.value(e)}}),m=u.extend({_click:function(){this.toolbar.dialog({name:"fontSize",options:{sizes:d,defaultSize:f}})},update:function(e){this._value=e||f,this.element.find(".k-text").text(this.message+" ("+this._value+") ...")}}),e.toolbar.registerComponent("fontSize",p,m),g=["Arial","Courier New","Georgia","Times New Roman","Trebuchet MS","Verdana"],v="Arial",b=a.extend({init:function(e,t){a.fn.init.call(this,e,t);var n=this.dropDownList;n.setDataSource(e.fontFamilies||g),n.value(v),this.element.data({type:"fontFamily",fontFamily:this})},update:function(e){this.value(e||v)}}),w=u.extend({_click:function(){this.toolbar.dialog({name:"fontFamily",options:{fonts:g,defaultFont:v}})},update:function(e){this._value=e||v,this.element.find(".k-text").text(this.message+" ("+this._value+") ...")}}),e.toolbar.registerComponent("fontFamily",b,w),y=e.spreadsheet.formats={automatic:null,text:"@",number:"#,0.00",percent:"0.00%",financial:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',currency:"$#,##0.00;[Red]$#,##0.00",date:"m/d/yyyy",time:"h:mm:ss AM/PM",dateTime:"m/d/yyyy h:mm",duration:"[h]:mm:ss"},_=a.extend({_revertTitle:function(e){e.sender.value(""),e.sender.wrapper.width("auto")},init:function(e,t){var n,r;a.fn.init.call(this,e,t),n=this.dropDownList,r="<span class='k-icon k-i-"+e.iconClass+"' style='line-height: 1em; width: 1.35em;'></span>",n.bind("change",this._revertTitle.bind(this)),
n.bind("dataBound",this._revertTitle.bind(this)),n.setOptions({dataValueField:"format",dataTextField:"name",dataValuePrimitive:!0,valueTemplate:r,template:"# if (data.sample) { #<span class='k-spreadsheet-sample'>#: data.sample #</span># } ##: data.name #"}),n.text(r),n.setDataSource([{format:y.automatic,name:i.formatTypes.automatic},{format:y.text,name:i.formatTypes.text},{format:y.number,name:i.formatTypes.number,sample:"1,499.99"},{format:y.percent,name:i.formatTypes.percent,sample:"14.50%"},{format:y.financial,name:i.formatTypes.financial,sample:"(1,000.12)"},{format:y.currency,name:i.formatTypes.currency,sample:"$1,499.99"},{format:y.date,name:i.formatTypes.date,sample:"4/21/2012"},{format:y.time,name:i.formatTypes.time,sample:"5:49:00 PM"},{format:y.dateTime,name:i.formatTypes.dateTime,sample:"4/21/2012 5:49:00"},{format:y.duration,name:i.formatTypes.duration,sample:"168:05:00"},{popup:"formatCells",name:i.formatTypes.moreFormats}]),this.element.data({type:"format",format:this})}}),k=u.extend({_click:function(){this.toolbar.dialog({name:"formatCells"})}}),e.toolbar.registerComponent("format",_,k),x=l.extend({init:function(e,t){l.fn.init.call(this,e,t),this._borderPalette(),this.element.data({type:"borders",instance:this})},destroy:function(){this.borderPalette.destroy(),l.fn.destroy.call(this)},_borderPalette:function(){var n=t("<div />").appendTo(this.popup.element);this.borderPalette=new e.spreadsheet.BorderPalette(n,{change:this._action.bind(this)})},_action:function(e){this.toolbar.action({command:"BorderChangeCommand",options:{border:e.type,style:{size:1,color:e.color}}})}}),C=u.extend({_click:function(){this.toolbar.dialog({name:"borders"})}}),e.toolbar.registerComponent("borders",x,C),F=l.extend({init:function(e,n){l.fn.init.call(this,e,n),this.element.attr({"data-property":"alignment"}),this._defineButtons(),this._commandPalette(),this.popup.element.on("click",".k-button",function(e){this._action(t(e.currentTarget))}.bind(this)),this.element.data({type:"alignment",alignment:this,instance:this})},_defineButtons:function(){this.buttons=[{property:"textAlign",value:"left",iconClass:"align-left",text:i.alignmentButtons.justtifyLeft},{property:"textAlign",value:"center",iconClass:"align-center",text:i.alignmentButtons.justifyCenter},{property:"textAlign",value:"right",iconClass:"align-right",text:i.alignmentButtons.justifyRight},{property:"textAlign",value:"justify",iconClass:"align-justify",text:i.alignmentButtons.justifyFull},{property:"verticalAlign",value:"top",iconClass:"align-top",text:i.alignmentButtons.alignTop},{property:"verticalAlign",value:"center",iconClass:"align-middle",text:i.alignmentButtons.alignMiddle},{property:"verticalAlign",value:"bottom",iconClass:"align-bottom",text:i.alignmentButtons.alignBottom}]},destroy:function(){this.popup.element.off(),l.fn.destroy.call(this)},update:function(e){var t=e.textAlign(),n=e.verticalAlign(),i=this.popup.element;i.find(".k-button").removeClass("k-state-active"),t&&i.find("[data-property=textAlign][data-value="+t+"]").addClass("k-state-active"),n&&i.find("[data-property=verticalAlign][data-value="+n+"]").addClass("k-state-active")},_commandPalette:function(){var e=this.buttons,n=t("<div />").appendTo(this.popup.element);e.forEach(function(i,r){var o="<a title='"+i.text+"' data-property='"+i.property+"' data-value='"+i.value+"' class='k-button k-button-icon'><span class='k-icon k-i-"+i.iconClass+"'></span></a>";0!==r&&e[r-1].property!==i.property&&n.append(t("<span class='k-separator' />")),n.append(o)})},_action:function(e){var t=e.attr("data-property"),n=e.attr("data-value");this.toolbar.action({command:"PropertyChangeCommand",options:{property:t,value:n}})}}),R=u.extend({_click:function(){this.toolbar.dialog({name:"alignment"})}}),e.toolbar.registerComponent("alignment",F,R),S=l.extend({init:function(e,n){l.fn.init.call(this,e,n),this._defineButtons(),this._commandPalette(),this.popup.element.on("click",".k-button",function(e){this._action(t(e.currentTarget))}.bind(this)),this.element.data({type:"merge",merge:this,instance:this})},_defineButtons:function(){this.buttons=[{value:"cells",iconClass:"cells-merge",text:i.mergeButtons.mergeCells},{value:"horizontally",iconClass:"cells-merge-horizontally",text:i.mergeButtons.mergeHorizontally},{value:"vertically",iconClass:"cells-merge-vertically",text:i.mergeButtons.mergeVertically},{value:"unmerge",iconClass:"table-unmerge",text:i.mergeButtons.unmerge}]},destroy:function(){this.popup.element.off(),l.fn.destroy.call(this)},_commandPalette:function(){var e=t("<div />").appendTo(this.popup.element);this.buttons.forEach(function(t){var n="<a title='"+t.text+"' data-value='"+t.value+"' class='k-button k-button-icontext'><span class='k-icon k-i-"+t.iconClass+"'></span>"+t.text+"</a>";e.append(n)})},_action:function(e){var t=e.attr("data-value");this.toolbar.action({command:"MergeCellCommand",options:{value:t}})}}),A=u.extend({_click:function(){this.toolbar.dialog({name:"merge"})}}),e.toolbar.registerComponent("merge",S,A),D=l.extend({init:function(e,n){l.fn.init.call(this,e,n),this._defineButtons(),this._commandPalette(),this.popup.element.on("click",".k-button",function(e){this._action(t(e.currentTarget))}.bind(this)),this.element.data({type:"freeze",freeze:this,instance:this})},_defineButtons:function(){this.buttons=[{value:"panes",iconClass:"pane-freeze",text:i.freezeButtons.freezePanes},{value:"rows",iconClass:"row-freeze",text:i.freezeButtons.freezeRows},{value:"columns",iconClass:"column-freeze",text:i.freezeButtons.freezeColumns},{value:"unfreeze",iconClass:"table-unmerge",text:i.freezeButtons.unfreeze}]},destroy:function(){this.popup.element.off(),l.fn.destroy.call(this)},_commandPalette:function(){var e=t("<div />").appendTo(this.popup.element);this.buttons.forEach(function(t){var n="<a title='"+t.text+"' data-value='"+t.value+"' class='k-button k-button-icontext'><span class='k-icon k-i-"+t.iconClass+"'></span>"+t.text+"</a>";e.append(n)})},_action:function(e){var t=e.attr("data-value");this.toolbar.action({command:"FreezePanesCommand",options:{value:t}})}}),E=u.extend({_click:function(){this.toolbar.dialog({name:"freeze"})}}),e.toolbar.registerComponent("freeze",D,E),B=a.extend({_revertTitle:function(e){e.sender.value(""),e.sender.wrapper.width("auto")},init:function(e,t){a.fn.init.call(this,e,t);var n=this.dropDownList;n.bind("change",this._revertTitle.bind(this)),n.bind("dataBound",this._revertTitle.bind(this)),n.setOptions({valueTemplate:"<span class='k-icon k-i-"+e.iconClass+"' style='line-height: 1em; width: 1.35em;'></span>",template:"<span class='k-icon k-i-#= iconClass #' style='line-height: 1em; width: 1.35em;'></span>#=text#",dataTextField:"text",dataValueField:"value"}),n.setDataSource([{value:"asc",sheet:!1,text:i.sortButtons.sortRangeAsc,iconClass:"sort-asc"},{value:"desc",sheet:!1,text:i.sortButtons.sortRangeDesc,iconClass:"sort-desc"}]),n.select(0),this.element.data({type:"sort",sort:this})},_change:function(e){var t=e.sender,n=t.dataItem();n&&this.toolbar.action({command:"SortCommand",options:{value:n.value,sheet:n.sheet}})},value:t.noop}),M=u.extend({_click:function(){this.toolbar.dialog({name:"sort"})}}),e.toolbar.registerComponent("sort",B,M),T=e.toolbar.ToolBarButton.extend({init:function(t,n){t.showText="overflow",e.toolbar.ToolBarButton.fn.init.call(this,t,n),this.element.on("click",this._click.bind(this)),this.element.data({type:"filter",filter:this})},_click:function(){this.toolbar.action({command:"FilterCommand"})},update:function(e){this.toggle(e)}}),I=u.extend({init:function(e,t){u.fn.init.call(this,e,t),this.element.data({type:"filter",filter:this})},_click:function(){this.toolbar.action({command:"FilterCommand"})},update:function(e){this.toggle(e)}}),e.toolbar.registerComponent("filter",T,I),L=e.toolbar.Item.extend({init:function(e,n){this.toolbar=n,this.element=t("<div class='k-button k-upload-button k-button-icon'><span class='k-icon k-i-folder-open' /></div>").data("instance",this),this._title=e.attributes.title,this._reset()},_reset:function(){this.element.find("input").remove(),t("<input type='file' autocomplete='off' accept='.xlsx'/>").attr("title",this._title).attr("aria-label",this._title).one("change",this._change.bind(this)).appendTo(this.element)},_change:function(e){this.toolbar.action({command:"OpenCommand",options:{file:e.target.files[0]}}),this._reset()}}),e.toolbar.registerComponent("open",L),e.spreadsheet.TabStrip=e.ui.TabStrip.extend({init:function(n,i){e.ui.TabStrip.fn.init.call(this,n,i),n.addClass("k-spreadsheet-tabstrip"),this._quickAccessButtons(),this.toolbars={};var r=i.dataSource;this.contentElements.each(function(e,n){this._toolbar(t(n),r[e].id,i.toolbarOptions[r[e].id])}.bind(this)),this.one("activate",function(){this.toolbars[this.options.dataSource[0].id].resize()})},events:e.ui.TabStrip.fn.events.concat(["action","dialog"]),destroy:function(){this.quickAccessToolBar.off("click"),e.ui.TabStrip.fn.destroy.call(this);for(var t in this.toolbars)this.toolbars[t].destroy()},action:function(e){this.trigger("action",e)},dialog:function(e){this.trigger("dialog",e)},refreshTools:function(e){var t,n=this.toolbars;for(t in n)n.hasOwnProperty(t)&&n[t].refresh(e)},_quickAccessButtons:function(){var n=[{title:i.quickAccess.undo,iconClass:"undo",action:"undo"},{title:i.quickAccess.redo,iconClass:"redo",action:"redo"}],r=e.template("<a href='\\#' title='#= title #' data-action='#= action #' class='k-button k-button-icon' aria-label='#= title #'><span class='k-icon k-i-#=iconClass#'></span></a>");this.quickAccessToolBar=t("<div />",{"class":"k-spreadsheet-quick-access-toolbar",html:e.render(r,n)}).insertBefore(this.wrapper),this.quickAccessToolBar.on("click",".k-button",function(e){e.preventDefault();var n=t(e.currentTarget).attr("data-action");this.action({action:n})}.bind(this)),this.quickAccessAdjust()},quickAccessAdjust:function(){this.tabGroup.css("padding-left",e._outerWidth(this.quickAccessToolBar))},_toolbar:function(t,n,i){var r,o;this.toolbars[n]&&(this.toolbars[n].destroy(),t.children(".k-toolbar").remove()),i&&(r=t.html("<div />").children("div"),o={tools:"boolean"==typeof i?void 0:i,toolbarName:n,action:this.action.bind(this),dialog:this.dialog.bind(this)},this.toolbars[n]=new e.spreadsheet.ToolBar(r,o))}}))}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/dialogs.min",["kendo.core.min","kendo.binder.min","kendo.validator.min"],e)}(function(){!function(e){function t(t,n){return e.spreadsheet.formatting.text(t,n)}function n(e,t){var n,i=[],r=[];for(n=0;n<t.length;n++)l.inArray(t[n][e],r)==-1&&(i.push(t[n]),r.push(t[n][e]));return i}function i(e){return{value:e,text:s("exportAsDialog.pdf.paper."+e)}}function r(t,n){e.spreadsheet.dialogs.register(t,m.extend({options:{messageId:n}}))}function o(e,t){this.path=e.split("."),this.def=t}function s(e,t){return new o(e,t)}function a(e){return e instanceof o?e.trans():Array.isArray(e)?e.map(a):null!=e&&"object"==typeof e?Object.keys(e).reduce(function(t,n){return t[n]=a(e[n]),t},{}):e}var l,u,c,h,d,f,p,m,g,v,b,w,y,_,k,x,C,F,R,S,A,D,E,B,M,T,I;e.support.browser.msie&&e.support.browser.version<9||(l=e.jQuery,u=e.data.ObservableObject,c=e.spreadsheet.messages.dialogs={apply:"Apply",save:"Save",cancel:"Cancel",remove:"Remove",retry:"Retry",revert:"Revert",okText:"OK",formatCellsDialog:{title:"Format",categories:{number:"Number",currency:"Currency",date:"Date"}},fontFamilyDialog:{title:"Font"},fontSizeDialog:{title:"Font size"},bordersDialog:{title:"Borders"},alignmentDialog:{title:"Alignment",buttons:{justtifyLeft:"Align left",justifyCenter:"Center",justifyRight:"Align right",justifyFull:"Justify",alignTop:"Align top",alignMiddle:"Align middle",alignBottom:"Align bottom"}},mergeDialog:{title:"Merge cells",buttons:{mergeCells:"Merge all",mergeHorizontally:"Merge horizontally",mergeVertically:"Merge vertically",unmerge:"Unmerge"}},freezeDialog:{title:"Freeze panes",buttons:{freezePanes:"Freeze panes",freezeRows:"Freeze rows",freezeColumns:"Freeze columns",unfreeze:"Unfreeze panes"}},confirmationDialog:{text:"Are you sure you want to remove this sheet?",title:"Sheet remove"},validationDialog:{title:"Data Validation",hintMessage:"Please enter a valid {0} value {1}.",hintTitle:"Validation {0}",criteria:{any:"Any value",number:"Number",text:"Text",date:"Date",custom:"Custom Formula",list:"List"},comparers:{greaterThan:"greater than",lessThan:"less than",between:"between",notBetween:"not between",equalTo:"equal to",notEqualTo:"not equal to",greaterThanOrEqualTo:"greater than or equal to",lessThanOrEqualTo:"less than or equal to"},comparerMessages:{greaterThan:"greater than {0}",lessThan:"less than {0}",between:"between {0} and {1}",notBetween:"not between {0} and {1}",equalTo:"equal to {0}",notEqualTo:"not equal to {0}",greaterThanOrEqualTo:"greater than or equal to {0}",lessThanOrEqualTo:"less than or equal to {0}",custom:"that satisfies the formula: {0}"},labels:{criteria:"Criteria",comparer:"Comparer",min:"Min",max:"Max",value:"Value",start:"Start",end:"End",onInvalidData:"On invalid data",rejectInput:"Reject input",showWarning:"Show warning",showHint:"Show hint",hintTitle:"Hint title",hintMessage:"Hint message",ignoreBlank:"Ignore blank",showListButton:"Display button to show list",showCalendarButton:"Display button to show calendar"},placeholders:{typeTitle:"Type title",typeMessage:"Type message"}},exportAsDialog:{title:"Export...",defaultFileName:"Workbook",xlsx:{description:"Excel Workbook (.xlsx)"},pdf:{description:"Portable Document Format (.pdf)",area:{workbook:"Entire Workbook",sheet:"Active Sheet",selection:"Selection"},paper:{a2:"A2 (420 mm × 594 mm)",a3:"A3 (297 mm x 420 mm)",a4:"A4 (210 mm x 297 mm)",a5:"A5 (148 mm x 210 mm)",b3:"B3 (353 mm × 500 mm)",b4:"B4 (250 mm x 353 mm)",b5:"B5 (176 mm x 250 mm)",folio:'Folio (8.5" x 13")',legal:'Legal (8.5" x 14")',letter:'Letter (8.5" x 11")',tabloid:'Tabloid (11" x 17")',executive:'Executive (7.25" x 10.5")'},margin:{normal:"Normal",narrow:"Narrow",wide:"Wide"}},labels:{scale:"Scale",fit:"Fit to page",fileName:"File name",saveAsType:"Save as type",exportArea:"Export",paperSize:"Paper size",margins:"Margins",orientation:"Orientation",print:"Print",guidelines:"Guidelines",center:"Center",horizontally:"Horizontally",vertically:"Vertically"}},modifyMergedDialog:{errorMessage:"Cannot change part of a merged cell."},rangeDisabledDialog:{errorMessage:"Destination range contains disabled cells."},intersectsArrayDialog:{errorMessage:"You cannot alter part of an array"},incompatibleRangesDialog:{errorMessage:"Incompatible ranges"},noFillDirectionDialog:{errorMessage:"Cannot determine fill direction"},duplicateSheetNameDialog:{errorMessage:"Duplicate sheet name"},overflowDialog:{errorMessage:"Cannot paste, because the copy area and the paste area are not the same size and shape."},useKeyboardDialog:{title:"Copying and pasting",errorMessage:"These actions cannot be invoked through the menu. Please use the keyboard shortcuts instead:",labels:{forCopy:"for copy",forCut:"for cut",forPaste:"for paste"}},unsupportedSelectionDialog:{errorMessage:"That action cannot be performed on multiple selection."},linkDialog:{title:"Hyperlink",labels:{text:"Text",url:"Address",removeLink:"Remove link"}},insertCommentDialog:{title:"Insert comment",labels:{comment:"Comment",removeComment:"Remove comment"}},insertImageDialog:{title:"Insert image",info:"Drag an image here, or click to select",typeError:"Please select a JPEG, PNG or GIF image"}},h={},e.spreadsheet.dialogs={register:function(e,t){h[e]=t},registered:function(e){return!!h[e]},create:function(e,t){var n=h[e];if(n)return new n(t)}},d=e.spreadsheet.SpreadsheetDialog=e.Observable.extend({init:function(t){e.Observable.fn.init.call(this,t),this.options=a(l.extend(!0,{},this.options,t)),this.bind(this.events,t)},events:["close","activate"],options:{autoFocus:!0},dialog:function(){if(!this._dialog){var t={autoFocus:!1,scrollable:!1,resizable:!1,modal:!0,visible:!1,width:this.options.width||320,title:this.options.title,open:function(){this.center()},close:this._onDialogClose.bind(this),activate:this._onDialogActivate.bind(this),deactivate:this._onDialogDeactivate.bind(this)};this._dialog=l("<div class='k-spreadsheet-window k-action-window k-popup-edit-form' />").addClass(this.options.className||"").append(e.template(this.options.template)({messages:e.spreadsheet.messages.dialogs||c,errors:this.options.errors})).kendoWindow(t).data("kendoWindow")}return this._dialog},_onDialogClose:function(){this.trigger("close",{action:this._action})},_onDialogActivate:function(){this.trigger("activate")},_onDialogDeactivate:function(){this.trigger("deactivate"),this.destroy()},destroy:function(){this._dialog&&(this._dialog.destroy(),this._dialog=null)},open:function(){this.dialog().open(),this.dialog().element.find(".k-primary").focus()},apply:function(){this.close()},close:function(){this._action="close",this.dialog().close()}}),f=e.spreadsheet.FormatCellsViewModel=u.extend({init:function(e){u.fn.init.call(this,e),this.useCategory(this.category)},useCategory:function(e){var t=e&&e.type||"number",n="currency"==t;this.category=e,this.set("showCurrencyFilter",n&&this.currencies.length>1),n?this.currency(this.currencies[0]):this.set("formats",this.allFormats[t+"Formats"]),this.useFirstFormat()},useFirstFormat:function(){this.formats.length&&this.set("format",this.formats[0].value)},currency:function(e){var n,i;return void 0!==e&&(this._currency=e,n=e.value,i=[{currency:n,decimals:!0},{currency:n,decimals:!0,iso:!0},{currency:n,decimals:!1}],i=i.map(function(e){return e=f.convert.currency(e),{value:e,name:t(1e3,e)}}),this.set("formats",i),this.useFirstFormat()),this._currency||this.currencies[0]},categoryFilter:function(e){return void 0!==e&&this.useCategory(e),this.category},preview:function(){var e=this.get("format"),n=this.value||0;return e&&e.length?t(n,e):n}}),f.convert={currency:function(e){function t(e,t){return Array(t+1).join(e)}var n=e.currency,i=n.pattern[1];return e.decimals&&(i=i.replace(/n/g,"n"+n["."]+t("0",n.decimals))),i=e.iso?'"'+n.abbr+'" '+i.replace(/\s*\$\s*/g,""):i.replace(/\$/g,JSON.stringify(n.symbol)),i=i.replace(/n/g,"?")},date:function(e){return/T|Z/.test(e)?"":e.toLowerCase().replace(/tt/g,"AM/PM").replace(/'/g,'"')}},p=d.extend({init:function(t){var n=e.spreadsheet.messages.dialogs.formatCellsDialog||c,i={title:n.title,categories:[{type:"number",name:n.categories.number},{type:"currency",name:n.categories.currency},{type:"date",name:n.categories.date}]};d.fn.init.call(this,l.extend(i,t)),this._generateFormats()},options:{className:"k-spreadsheet-format-cells",template:"<div class='k-edit-form-container'><div class='k-root-tabs' data-role='tabstrip' data-text-field='name' data-bind='source: categories, value: categoryFilter' data-animation='false' /><div class='k-spreadsheet-preview' data-bind='text: preview' /><script type='text/x-kendo-template' id='format-item-template'>\\#: data.name \\#</script><select data-role='dropdownlist' class='k-format-filter' data-text-field='description' data-value-field='value.name' data-bind='visible: showCurrencyFilter, value: currency, source: currencies' /><ul data-role='staticlist' tabindex='0' class='k-list k-reset' data-template='format-item-template' data-value-primitive='true' data-value-field='value' data-bind='source: formats, value: format' /><div class='k-action-buttons'><button class='k-button k-primary' data-bind='click: apply'>#: messages.apply #</button><button class='k-button' data-bind='click: close'>#: messages.cancel #</button></div></div>"},_generateFormats:function(){var i,r=this.options;r.currencies||(r.currencies=p.currenciesFrom(e.cultures)),r.numberFormats||(r.numberFormats=[{value:"#.00%",name:"100.00%"},{value:"#%",name:"100%"},{value:"#.00",name:"1024.00"},{value:"#,###.00",name:"1,024.00"}]),r.dateFormats||(i=e.cultures.current.calendars.standard.patterns,r.dateFormats=n("value",l.map(i,function(e){if(e=f.convert.date(e))return{value:e,name:t(34567.7678,e)}})))},open:function(t){var n,i,r=this.options,o=t.value(),s=r.categories.slice(0);this.viewModel=new f({currencies:r.currencies.slice(0),allFormats:{numberFormats:r.numberFormats.slice(0),dateFormats:r.dateFormats.slice(0)},categories:s,format:t.format(),category:o instanceof Date?s[2]:s[0],apply:this.apply.bind(this),close:this.close.bind(this),value:o}),d.fn.open.call(this),n=this.dialog().element,e.bind(n,this.viewModel),i=n.find("select.k-format-filter").data("kendoDropDownList"),r.currencies.length>10&&i.setOptions({filter:"contains"}),n.find(e.roleSelector("staticlist")).parent().addClass("k-list-wrapper")},apply:function(){var e=this.viewModel.format;d.fn.apply.call(this),this.trigger("action",{command:"PropertyChangeCommand",options:{property:"format",value:e}})}}),p.currenciesFrom=function(t){return n("description",l.map(t,function(t,n){var i,r;if(/-/.test(n))return i=t.numberFormat.currency,r=e.format("{0} ({1}, {2})",i.name,i.abbr,i.symbol),{description:r,value:i}}))},e.spreadsheet.dialogs.register("formatCells",p),e.spreadsheet.dialogs.FormatCellsDialog=p,m=d.extend({options:{className:"k-spreadsheet-message",title:"",messageId:"",text:"",template:"<div class='k-spreadsheet-message-content' data-bind='text: text' /><div class='k-action-buttons'><button class='k-button k-primary' data-bind='click: close'>#= messages.okText #</button></div>"},open:function(){var t,n;d.fn.open.call(this),t=this.options,n=t.text,t.messageId&&(n=e.getter(t.messageId,!0)(e.spreadsheet.messages.dialogs)),e.bind(this.dialog().element,{text:n,close:this.close.bind(this)})}}),e.spreadsheet.dialogs.register("message",m),g=d.extend({init:function(t){var n=e.spreadsheet.messages.dialogs.confirmationDialog||c,i={title:n.title,text:n.text};d.fn.init.call(this,l.extend(i,t))},options:{className:"k-spreadsheet-message",messageId:"",template:"<div class='k-spreadsheet-message-content' data-bind='text: text' /><div class='k-action-buttons'><button class='k-button k-primary' data-bind='click: confirm'>#= messages.okText #</button><button class='k-button' data-bind='click: cancel'>#= messages.cancel #</button></div>"},open:function(){var t,n;d.fn.open.call(this),t=this.options,n=t.text,t.messageId&&(n=e.getter(t.messageId,!0)(e.spreadsheet.messages.dialogs)),e.bind(this.dialog().element,{text:n,confirm:this.confirm.bind(this),cancel:this.close.bind(this)})},isConfirmed:function(){return this._confirmed},confirm:function(){this._confirmed=!0,this.close()}}),e.spreadsheet.dialogs.register("confirmation",g),v=d.extend({options:{className:"k-spreadsheet-message",title:"",messageId:"",text:"",template:"<div class='k-spreadsheet-message-content' data-bind='text: text' /><div class='k-action-buttons'><button class='k-button k-primary' data-bind='click: retry'>#= messages.retry #</button><button class='k-button' data-bind='click: cancel'>#= messages.cancel #</button></div>"},open:function(){var t,n;d.fn.open.call(this),t=this.options,n=t.text,t.messageId&&(n=e.getter(t.messageId,!0)(e.spreadsheet.messages.dialogs)),e.bind(this.dialog().element,{text:n,retry:this.retry.bind(this),cancel:this.close.bind(this)})},retry:function(){this._retry=!0,this.close()}}),e.spreadsheet.dialogs.register("validationError",v),b=d.extend({init:function(t){var n=e.spreadsheet.messages.dialogs.fontFamilyDialog||c;d.fn.init.call(this,l.extend({title:n.title},t)),this._list()},options:{template:"<ul class='k-list k-reset'></ul>"},_list:function(){var t=this.dialog().element.find("ul"),n=this.options.fonts,i=this.options.defaultFont;this.list=new e.ui.StaticList(t,{dataSource:new e.data.DataSource({data:n}),template:"#: data #",value:i,change:this.apply.bind(this)}),this.list.dataSource.fetch()},apply:function(e){d.fn.apply.call(this),this.trigger("action",{command:"PropertyChangeCommand",options:{property:"fontFamily",value:e.sender.value()[0]}})}}),e.spreadsheet.dialogs.register("fontFamily",b),w=d.extend({init:function(t){var n=e.spreadsheet.messages.dialogs.fontSizeDialog||c;d.fn.init.call(this,l.extend({title:n.title},t)),this._list()},options:{template:"<ul class='k-list k-reset'></ul>"},_list:function(){var t=this.dialog().element.find("ul"),n=this.options.sizes,i=this.options.defaultSize;this.list=new e.ui.StaticList(t,{dataSource:new e.data.DataSource({data:n}),template:"#: data #",value:i,change:this.apply.bind(this)}),this.list.dataSource.fetch()},apply:function(t){d.fn.apply.call(this),this.trigger("action",{command:"PropertyChangeCommand",options:{property:"fontSize",value:e.parseInt(t.sender.value()[0])}})}}),e.spreadsheet.dialogs.register("fontSize",w),y=d.extend({init:function(t){var n=e.spreadsheet.messages.dialogs.bordersDialog||c;d.fn.init.call(this,l.extend({title:n.title},t)),this.element=this.dialog().element,this._borderPalette(),this.viewModel=e.observable({apply:this.apply.bind(this),close:this.close.bind(this)}),e.bind(this.element.find(".k-action-buttons"),this.viewModel)},options:{width:177,template:"<div></div><div class='k-action-buttons'><button class='k-button k-primary' data-bind='click: apply'>#: messages.apply #</button><button class='k-button' data-bind='click: close'>#: messages.cancel #</button></div>"},apply:function(){d.fn.apply.call(this);var e=this.value();this.trigger("action",{command:"BorderChangeCommand",options:{border:e.type,style:{size:1,color:e.color}}})},_borderPalette:function(){var t=this.dialog().element.find("div:first");this.borderPalette=new e.spreadsheet.BorderPalette(t,{change:this.value.bind(this)})},value:function(e){return void 0===e?this._state:void(this._state=e)}}),e.spreadsheet.dialogs.register("borders",y),_=d.extend({init:function(t){d.fn.init.call(this,t),this.element=this.dialog().element,this.property=t.property,this.options.title=t.title,this.viewModel=e.observable({apply:this.apply.bind(this),close:this.close.bind(this)}),e.bind(this.element.find(".k-action-buttons"),this.viewModel)},options:{template:"<div></div><div class='k-action-buttons'><button class='k-button k-primary' data-bind='click: apply'>#: messages.apply #</button><button class='k-button' data-bind='click: close'>#: messages.cancel #</button></div>"},apply:function(){d.fn.apply.call(this),this.trigger("action",{command:"PropertyChangeCommand",options:{property:this.property,value:this.value()}})},value:function(e){return void 0===e?this._value:void(this._value=e.value)}}),k=_.extend({init:function(e){e.width=177,_.fn.init.call(this,e),this._colorPalette()},_colorPalette:function(){var e=this.dialog().element.find("div:first");this.colorPalette=e.kendoColorPalette({palette:["#ffffff","#000000","#d6ecff","#4e5b6f","#7fd13b","#ea157a","#feb80a","#00addc","#738ac8","#1ab39f","#f2f2f2","#7f7f7f","#a7d6ff","#d9dde4","#e5f5d7","#fad0e4","#fef0cd","#c5f2ff","#e2e7f4","#c9f7f1","#d8d8d8","#595959","#60b5ff","#b3bcca","#cbecb0","#f6a1c9","#fee29c","#8be6ff","#c7d0e9","#94efe3","#bfbfbf","#3f3f3f","#007dea","#8d9baf","#b2e389","#f272af","#fed46b","#51d9ff","#aab8de","#5fe7d5","#a5a5a5","#262626","#003e75","#3a4453","#5ea226","#af0f5b","#c58c00","#0081a5","#425ea9","#138677","#7f7f7f","#0c0c0c","#00192e","#272d37","#3f6c19","#750a3d","#835d00","#00566e","#2c3f71","#0c594f"],change:this.value.bind(this)}).data("kendoColorPalette")}}),e.spreadsheet.dialogs.register("colorPicker",k),x=_.extend({init:function(e){e.width=268,_.fn.init.call(this,e),this.dialog().setOptions({animation:!1}),this.dialog().one("activate",this._colorPicker.bind(this))},_colorPicker:function(){var e=this.dialog().element.find("div:first");this.colorPicker=e.kendoFlatColorPicker({change:this.value.bind(this)}).data("kendoFlatColorPicker")}}),e.spreadsheet.dialogs.register("customColor",x),C=d.extend({init:function(t){var n=e.spreadsheet.messages.dialogs.alignmentDialog||c,i={title:n.title,buttons:[{property:"textAlign",value:"left",iconClass:"align-left",text:n.buttons.justtifyLeft},{property:"textAlign",value:"center",iconClass:"align-center",text:n.buttons.justifyCenter},{property:"textAlign",value:"right",iconClass:"align-right",text:n.buttons.justifyRight},{property:"textAlign",value:"justify",iconClass:"align-justify",text:n.buttons.justifyFull},{property:"verticalAlign",value:"top",iconClass:"align-top",text:n.buttons.alignTop},{property:"verticalAlign",value:"center",iconClass:"align-middle",text:n.buttons.alignMiddle},{property:"verticalAlign",value:"bottom",iconClass:"align-bottom",text:n.buttons.alignBottom}]};d.fn.init.call(this,l.extend(i,t)),this._list()},options:{template:"<ul class='k-list k-reset'></ul>"},_list:function(){var t=this.dialog().element.find("ul");this.list=new e.ui.StaticList(t,{dataSource:new e.data.DataSource({data:this.options.buttons}),template:"<a title='#=text#' data-property='#=property#' data-value='#=value#'><span class='k-icon k-i-#=iconClass#'></span>#=text#</a>",change:this.apply.bind(this)}),this.list.dataSource.fetch()},apply:function(e){var t=e.sender.value()[0];d.fn.apply.call(this),this.trigger("action",{command:"PropertyChangeCommand",options:{property:t.property,value:t.value}})}}),e.spreadsheet.dialogs.register("alignment",C),F=d.extend({init:function(t){var n=e.spreadsheet.messages.dialogs.mergeDialog||c,i={title:n.title,buttons:[{value:"cells",iconClass:"cells-merge",text:n.buttons.mergeCells},{value:"horizontally",iconClass:"cells-merge-horizontally",text:n.buttons.mergeHorizontally},{value:"vertically",iconClass:"cells-merge-vertically",text:n.buttons.mergeVertically},{value:"unmerge",iconClass:"table-unmerge",text:n.buttons.unmerge}]};d.fn.init.call(this,l.extend(i,t)),this._list()},options:{template:"<ul class='k-list k-reset'></ul>"},_list:function(){var t=this.dialog().element.find("ul");this.list=new e.ui.StaticList(t,{dataSource:new e.data.DataSource({data:this.options.buttons}),template:"<a title='#=text#' data-value='#=value#'><span class='k-icon k-icon k-i-#=iconClass#'></span>#=text#</a>",change:this.apply.bind(this)}),this.list.dataSource.fetch()},apply:function(e){var t=e.sender.value()[0];d.fn.apply.call(this),this.trigger("action",{command:"MergeCellCommand",options:{value:t.value}})}}),e.spreadsheet.dialogs.register("merge",F),R=d.extend({init:function(t){var n=e.spreadsheet.messages.dialogs.freezeDialog||c,i={title:n.title,buttons:[{value:"panes",iconClass:"pane-freeze",text:n.buttons.freezePanes},{value:"rows",iconClass:"row-freeze",text:n.buttons.freezeRows},{value:"columns",iconClass:"column-freeze",text:n.buttons.freezeColumns},{value:"unfreeze",iconClass:"table-unmerge",text:n.buttons.unfreeze}]};d.fn.init.call(this,l.extend(i,t)),this._list()},options:{template:"<ul class='k-list k-reset'></ul>"},_list:function(){var t=this.dialog().element.find("ul");this.list=new e.ui.StaticList(t,{dataSource:new e.data.DataSource({data:this.options.buttons}),template:"<a title='#=text#' data-value='#=value#'><span class='k-icon k-icon k-i-#=iconClass#'></span>#=text#</a>",change:this.apply.bind(this)}),this.list.dataSource.fetch()},apply:function(e){var t=e.sender.value()[0];d.fn.apply.call(this),this.trigger("action",{command:"FreezePanesCommand",options:{value:t.value}})}}),e.spreadsheet.dialogs.register("freeze",R),S=e.spreadsheet.ValidationCellsViewModel=u.extend({init:function(e){u.fn.init.call(this,e),this.bind("change",function(e){"criterion"===e.field&&(this.reset(),"custom"!==this.criterion&&"list"!==this.criterion||this.setHintMessageTemplate()),"comparer"===e.field&&this.setHintMessageTemplate(),"hintMessage"!=e.field&&"hintTitle"!=e.field||this._mute||(this.shouldBuild=!1),"from"!=e.field&&"to"!=e.field&&"hintMessageTemplate"!=e.field&&"type"!=e.field||!this.shouldBuild||this.buildMessages()}.bind(this)),this.reset()},buildMessages:function(){this._mute=!0,this.set("hintTitle",this.hintTitleTemplate?e.format(this.hintTitleTemplate,this.type):""),this.set("hintMessage",this.hintMessageTemplate?e.format(this.hintMessageTemplate,this.from,this.to):""),this._mute=!1},reset:function(){this.setComparers(),this.set("comparer",this.comparers[0].type),
this.set("from",null),this.set("to",null),this.set("useCustomMessages",!1),this.shouldBuild=!0,this.hintTitleTemplate=this.defaultHintTitle,this.buildMessages()},setComparers:function(){var e,t,n=this.defaultComparers,i=[];if("text"===this.criterion)for(e=["equalTo","notEqualTo"],t=0;t<n.length;t++)e[0]==n[t].type&&(i.push(n[t]),e.shift());else i=n.slice();this.set("comparers",i)},setHintMessageTemplate:function(){"custom"!==this.criterion&&"list"!==this.criterion?this.set("hintMessageTemplate",e.format(this.defaultHintMessage,this.criterion,this.comparerMessages[this.comparer])):(this.set("hintMessageTemplate",""),this.set("hintMessage",""))},isAny:function(){return"any"===this.get("criterion")},isNumber:function(){return"number"===this.get("criterion")},showToForNumber:function(){return this.showTo()&&this.isNumber()},showToForDate:function(){return this.showTo()&&this.isDate()},isText:function(){return"text"===this.get("criterion")},isDate:function(){return"date"===this.get("criterion")},isList:function(){return"list"===this.get("criterion")},isCustom:function(){return"custom"===this.get("criterion")},showRemove:function(){return this.get("hasValidation")},showTo:function(){return"between"==this.get("comparer")||"notBetween"==this.get("comparer")},update:function(e){this.set("hasValidation",!!e),e&&this.fromValidationObject(e)},fromValidationObject:function(e){this.set("criterion",e.dataType),this.set("comparer",e.comparerType),this.set("from",e.from),this.set("to",e.to),this.set("type",e.type),this.set("ignoreBlank",e.allowNulls),this.set("showButton",e.showButton),e.messageTemplate||e.titleTemplate?(this.hintMessageTemplate=e.messageTemplate,this.hintMessage=e.messageTemplate,this.hintTitleTemplate=e.titleTemplate,this.hintTitle=e.titleTemplate,this.useCustomMessages=!0,this.buildMessages()):this.useCustomMessages=!1},toValidationObject:function(){if("any"===this.criterion)return null;var e={type:this.type,dataType:this.criterion,comparerType:this.comparer,from:this.from,to:this.to,allowNulls:this.ignoreBlank,showButton:this.showButton};return this.useCustomMessages&&(e.messageTemplate=this.shouldBuild?this.hintMessageTemplate:this.hintMessage,e.titleTemplate=this.hintTitle),e}}),A=d.extend({init:function(t){var n=e.spreadsheet.messages.dialogs.validationDialog||c,i={title:n.title,hintMessage:n.hintMessage,hintTitle:n.hintTitle,criteria:[{type:"any",name:n.criteria.any},{type:"number",name:n.criteria.number},{type:"text",name:n.criteria.text},{type:"date",name:n.criteria.date},{type:"custom",name:n.criteria.custom},{type:"list",name:n.criteria.list}],comparers:[{type:"greaterThan",name:n.comparers.greaterThan},{type:"lessThan",name:n.comparers.lessThan},{type:"between",name:n.comparers.between},{type:"notBetween",name:n.comparers.notBetween},{type:"equalTo",name:n.comparers.equalTo},{type:"notEqualTo",name:n.comparers.notEqualTo},{type:"greaterThanOrEqualTo",name:n.comparers.greaterThanOrEqualTo},{type:"lessThanOrEqualTo",name:n.comparers.lessThanOrEqualTo}],comparerMessages:n.comparerMessages};d.fn.init.call(this,l.extend(i,t))},options:{width:450,criterion:"any",type:"reject",ignoreBlank:!0,showButton:!0,useCustomMessages:!1,errorTemplate:'<div class="k-widget k-tooltip k-tooltip-validation" style="margin:0.5em"><span class="k-icon k-i-warning"> </span>#= message #<div class="k-callout k-callout-n"></div></div>',template:'<div class="k-edit-form-container"><div class="k-edit-label"><label>#: messages.validationDialog.labels.criteria #:</label></div><div class="k-edit-field"><select data-role="dropdownlist" title="#: messages.validationDialog.labels.criteria #"data-text-field="name" data-value-field="type" data-bind="value: criterion, source: criteria" /></div><div data-bind="visible: isNumber"><div class="k-edit-label"><label>#: messages.validationDialog.labels.comparer #:</label></div><div class="k-edit-field"><select data-role="dropdownlist" title="#: messages.validationDialog.labels.comparer #"data-text-field="name" data-value-field="type" data-bind="value: comparer, source: comparers" /></div><div class="k-edit-label"><label>#: messages.validationDialog.labels.min #:</label></div><div class="k-edit-field"><input name="#: messages.validationDialog.labels.min #" title="#: messages.validationDialog.labels.min #" placeholder="e.g. 10" class="k-textbox" data-bind="value: from, enabled: isNumber" required="required" /></div><div data-bind="visible: showTo"><div class="k-edit-label"><label>#: messages.validationDialog.labels.max #:</label></div><div class="k-edit-field"><input name="#: messages.validationDialog.labels.max #" title="#: messages.validationDialog.labels.max #" placeholder="e.g. 100" class="k-textbox" data-bind="value: to, enabled: showToForNumber" required="required" /></div></div></div><div data-bind="visible: isText"><div class="k-edit-label"><label>#: messages.validationDialog.labels.comparer #:</label></div><div class="k-edit-field"><select data-role="dropdownlist" title="#: messages.validationDialog.labels.comparer #"data-text-field="name" data-value-field="type" data-bind="value: comparer, source: comparers" /></div><div class="k-edit-label"><label>#: messages.validationDialog.labels.value #:</label></div><div class="k-edit-field"><input name="#: messages.validationDialog.labels.value #" title="#: messages.validationDialog.labels.value #" class="k-textbox" data-bind="value: from, enabled: isText" required="required" /></div></div><div data-bind="visible: isDate"><div class="k-edit-label"><label>#: messages.validationDialog.labels.comparer #:</label></div><div class="k-edit-field"><select data-role="dropdownlist" title="#: messages.validationDialog.labels.comparer #"data-text-field="name" data-value-field="type" data-bind="value: comparer, source: comparers" /></div><div class="k-edit-label"><label>#: messages.validationDialog.labels.start #:</label></div><div class="k-edit-field"><input name="#: messages.validationDialog.labels.start #" title="#: messages.validationDialog.labels.start #" class="k-textbox" data-bind="value: from, enabled: isDate" required="required" /></div><div data-bind="visible: showTo"><div class="k-edit-label"><label>#: messages.validationDialog.labels.end #:</label></div><div class="k-edit-field"><input name="#: messages.validationDialog.labels.end #" title="#: messages.validationDialog.labels.end #" class="k-textbox" data-bind="value: to, enabled: showToForDate" required="required" /></div></div></div><div data-bind="visible: isCustom"><div class="k-edit-label"><label>#: messages.validationDialog.labels.value #:</label></div><div class="k-edit-field"><input name="#: messages.validationDialog.labels.value #" title="#: messages.validationDialog.labels.value #" class="k-textbox" data-bind="value: from, enabled: isCustom" required="required" /></div></div><div data-bind="visible: isList"><div class="k-edit-label"><label>#: messages.validationDialog.labels.value #:</label></div><div class="k-edit-field"><input name="#: messages.validationDialog.labels.value #" title="#: messages.validationDialog.labels.value #" class="k-textbox" data-bind="value: from, enabled: isList" required="required" /></div></div><div data-bind="visible: isList"><div class="k-edit-field"><input type="checkbox" name="showButton" id="listShowButton" class="k-checkbox" data-bind="checked: showButton"/><label for="listShowButton" class="k-checkbox-label"> #: messages.validationDialog.labels.showListButton #</label></div></div><div data-bind="visible: isDate"><div class="k-edit-field"><input type="checkbox" name="showButton" id="dateShowButton" class="k-checkbox" data-bind="checked: showButton"/><label for="dateShowButton" class="k-checkbox-label"> #: messages.validationDialog.labels.showCalendarButton #</label></div></div><div data-bind="invisible: isAny"><div class="k-edit-field"><input type="checkbox" title="#: messages.validationDialog.labels.ignoreBlank #" name="ignoreBlank" id="ignoreBlank" class="k-checkbox" data-bind="checked: ignoreBlank"/><label for="ignoreBlank" class="k-checkbox-label"> #: messages.validationDialog.labels.ignoreBlank #</label></div></div><div data-bind="invisible: isAny"><div class="k-hr"></div><div class="k-edit-label"><label>#: messages.validationDialog.labels.onInvalidData #:</label></div><div class="k-edit-field"><input type="radio" title="#: messages.validationDialog.labels.rejectInput #" id="validationTypeReject" name="validationType" value="reject" data-bind="checked: type" class="k-radio" /><label for="validationTypeReject" class="k-radio-label">#: messages.validationDialog.labels.rejectInput #</label> <input type="radio" title="#: messages.validationDialog.labels.showWarning #" id="validationTypeWarning"  name="validationType" value="warning" data-bind="checked: type" class="k-radio" /><label for="validationTypeWarning" class="k-radio-label">#: messages.validationDialog.labels.showWarning #</label></div></div><div data-bind="invisible: isAny" class="hint-wrapper"><div class="k-edit-field"><input type="checkbox" title="#: messages.validationDialog.labels.showHint #" name="useCustomMessages" id="useCustomMessages" class="k-checkbox" data-bind="checked: useCustomMessages" /><label class="k-checkbox-label" for="useCustomMessages"> #: messages.validationDialog.labels.showHint #</label></div><div data-bind="visible: useCustomMessages"><div class="k-edit-label"><label>#: messages.validationDialog.labels.hintTitle #:</label></div><div class="k-edit-field"><input class="k-textbox" title="#: messages.validationDialog.labels.hintTitle #" placeholder="#: messages.validationDialog.placeholders.typeTitle #" data-bind="value: hintTitle" /></div><div class="k-edit-label"><label>#: messages.validationDialog.labels.hintMessage #:</label></div><div class="k-edit-field"><input class="k-textbox" title="#: messages.validationDialog.labels.hintMessage #" placeholder="#: messages.validationDialog.placeholders.typeMessage #" data-bind="value: hintMessage" /></div></div></div><div class="k-action-buttons"><button class="k-button" data-bind="visible: showRemove, click: remove">#: messages.remove #</button><button class="k-button k-primary" data-bind="click: apply">#: messages.apply #</button><button class="k-button" data-bind="click: close">#: messages.cancel #</button></div></div>'},open:function(t){var n,i=this.options;this.viewModel=new S({type:i.type,defaultHintMessage:i.hintMessage,defaultHintTitle:i.hintTitle,defaultComparers:i.comparers.slice(0),comparerMessages:i.comparerMessages,criteria:i.criteria.slice(0),criterion:i.criterion,ignoreBlank:i.ignoreBlank,showButton:i.showButton,apply:this.apply.bind(this),close:this.close.bind(this),remove:this.remove.bind(this)}),this.viewModel.update(t.validation()),d.fn.open.call(this),n=this.dialog().element,this.validatable&&this.validatable.destroy(),e.bind(n,this.viewModel),this.validatable=new e.ui.Validator(n.find(".k-edit-form-container"),{validateOnBlur:!1,errorTemplate:this.options.errorTemplate||void 0})},apply:function(){this.validatable.validate()&&(d.fn.apply.call(this),this.trigger("action",{command:"EditValidationCommand",options:{value:this.viewModel.toValidationObject()}}))},remove:function(){this.viewModel.set("criterion","any"),this.apply()}}),e.spreadsheet.dialogs.register("validation",A),e.spreadsheet.dialogs.ValidationDialog=A,D=d.extend({init:function(t){d.fn.init.call(this,t),t=this.options,this.viewModel=e.observable({title:t.title,name:t.name,extension:t.extension,fileFormats:t.fileFormats,excel:t.excelExport,pdf:{proxyURL:t.pdfExport.proxyURL,forceProxy:t.pdfExport.forceProxy,title:t.pdfExport.title,author:t.pdfExport.author,subject:t.pdfExport.subject,keywords:t.pdfExport.keywords,creator:t.pdfExport.creator,date:t.pdfExport.date,fitWidth:t.pdf.fitWidth,area:t.pdf.area,areas:t.pdf.areas,paperSize:t.pdf.paperSize,paperSizes:t.pdf.paperSizes,margin:t.pdf.margin,margins:t.pdf.margins,landscape:t.pdf.landscape,guidelines:t.pdf.guidelines,hCenter:t.pdf.hCenter,vCenter:t.pdf.vCenter},apply:this.apply.bind(this),close:this.close.bind(this)});var n=this.dialog();this.viewModel.bind("change",function(e){"extension"===e.field&&(this.set("showPdfOptions",".pdf"===this.extension),n.center())}),e.bind(n.element,this.viewModel)},options:{title:s("exportAsDialog.title","Export..."),name:s("exportAsDialog.defaultFileName","Workbook"),extension:".xlsx",fileFormats:[{description:s("exportAsDialog.xlsx.description","Excel Workbook (.xlsx)"),extension:".xlsx"},{description:s("exportAsDialog.pdf.description","Portable Document Format (.pdf)"),extension:".pdf"}],pdf:{fitWidth:!0,area:"workbook",areas:[{area:"workbook",text:s("exportAsDialog.pdf.area.workbook","Entire Workbook")},{area:"sheet",text:s("exportAsDialog.pdf.area.sheet","Active Sheet")},{area:"selection",text:s("exportAsDialog.pdf.area.selection","Selection")}],paperSize:"a4",paperSizes:["a2","a3","a4","a5","b3","b4","b5","folio","legal","letter","tabloid","executive"].map(i),margin:{bottom:"0.75in",left:"0.7in",right:"0.7in",top:"0.75in"},margins:[{value:{bottom:"0.75in",left:"0.7in",right:"0.7in",top:"0.75in"},text:s("exportAsDialog.pdf.margin.normal","Normal")},{value:{bottom:"0.75in",left:"0.25in",right:"0.25in",top:"0.75in"},text:s("exportAsDialog.pdf.margin.narrow","Narrow")},{value:{bottom:"1in",left:"1in",right:"1in",top:"1in"},text:s("exportAsDialog.pdf.margin.wide","Wide")}],landscape:!0,guidelines:!0,hCenter:!0,vCenter:!0},width:520,template:"<div class='k-edit-label'><label>#: messages.exportAsDialog.labels.fileName #:</label></div><div class='k-edit-field'><input class='k-textbox' data-bind='value: name' /></div><div ><div class='k-edit-label'><label>#: messages.exportAsDialog.labels.saveAsType #:</label></div><div class='k-edit-field'><select data-role='dropdownlist' class='k-file-format' data-text-field='description' data-value-field='extension' data-bind='value: extension, source: fileFormats' /></div></div><div class='k-export-config' data-bind='visible: showPdfOptions'><hr class='k-hr' /><div class='k-edit-label'><label>#: messages.exportAsDialog.labels.exportArea #:</label></div><div class='k-edit-field'><select data-role='dropdownlist' class='k-file-format' data-text-field='text' data-value-field='area' data-bind='value: pdf.area, source: pdf.areas' /></div><div class='k-edit-label'><label>#: messages.exportAsDialog.labels.paperSize#:</label></div><div class='k-edit-field'><select data-role='dropdownlist' class='k-file-format' data-text-field='text' data-value-field='value' data-bind='value: pdf.paperSize, source: pdf.paperSizes' /></div><div class='k-edit-label'><label>#: messages.exportAsDialog.labels.margins #:</label></div><div class='k-edit-field'><select data-role='dropdownlist' class='k-file-format' data-value-primitive='true'data-text-field='text' data-value-field='value' data-bind='value: pdf.margin, source: pdf.margins' /></div><div class='k-edit-label'><label>#: messages.exportAsDialog.labels.orientation #:</label></div><div class='k-edit-field'><div class='k-button-group'><input type='radio' id='k-orientation-portrait' name='orientation' data-type='boolean' data-bind='checked: pdf.landscape' value='false' /><label class='k-button k-button-icon k-group-start k-orientation-button' for='k-orientation-portrait'><span class='k-icon k-i-page-portrait'></span></label><input type='radio' id='k-orientation-landscape' name='orientation' data-type='boolean' data-bind='checked: pdf.landscape' value='true' /><label class='k-button k-button-icon k-group-end k-orientation-button' for='k-orientation-landscape'><span class='k-icon k-i-page-landscape'></span></label></div></div><div class='k-edit-label'><label>#: messages.exportAsDialog.labels.print #:</label></div><div class='k-edit-field'><input class='k-checkbox' id='guidelines' type='checkbox' data-bind='checked: pdf.guidelines'/><label class='k-checkbox-label' for='guidelines'>#: messages.exportAsDialog.labels.guidelines#</label></div><div class='k-edit-label'><label>#: messages.exportAsDialog.labels.scale #:</label></div><div class='k-edit-field'><input class='k-checkbox' id='fitWidth' type='checkbox' data-bind='checked: pdf.fitWidth'/><label class='k-checkbox-label' for='fitWidth'>#: messages.exportAsDialog.labels.fit #</label></div><div class='k-edit-label'><label>#: messages.exportAsDialog.labels.center #:</label></div><div class='k-edit-field'><input class='k-checkbox' id='hCenter' type='checkbox' data-bind='checked: pdf.hCenter'/><label class='k-checkbox-label' for='hCenter'>#: messages.exportAsDialog.labels.horizontally #</label><input class='k-checkbox' id='vCenter' type='checkbox' data-bind='checked: pdf.vCenter'/><label class='k-checkbox-label' for='vCenter'>#: messages.exportAsDialog.labels.vertically #</label></div><div class='k-page-orientation'><span class='k-icon k-i-page-portrait' data-bind='invisible: pdf.landscape'></span><span class='k-icon k-i-page-landscape' data-bind='visible: pdf.landscape'></span></div></div><div class='k-action-buttons'><button class='k-button k-primary' data-bind='click: apply'>#: messages.save #</button><button class='k-button' data-bind='click: close'>#: messages.cancel #</button></div>"},apply:function(){d.fn.apply.call(this),this.trigger("action",{command:"SaveAsCommand",options:this.viewModel})}}),e.spreadsheet.dialogs.register("exportAs",D),r("modifyMerged","modifyMergedDialog.errorMessage"),r("rangeDisabled","rangeDisabledDialog.errorMessage"),r("intersectsArray","intersectsArrayDialog.errorMessage"),r("overflow","overflowDialog.errorMessage"),r("unsupportedSelection","unsupportedSelectionDialog.errorMessage"),r("incompatibleRanges","incompatibleRangesDialog.errorMessage"),r("noFillDirection","noFillDirectionDialog.errorMessage"),r("duplicateSheetName","duplicateSheetNameDialog.errorMessage"),E=m.extend({options:{width:640,title:"Errors in import",template:"<div class='k-spreadsheet-message-content k-spreadsheet-import-errors'><div class='k--header-message'>We encountered #= errors.length # errors while reading this file.  Please be aware that some formulas might be missing, or contain invalid results.</div><div class='k--errors'><table><thead><tr><th>Context</th><th>Error message</th></tr></thead># for (var i = 0; i < errors.length; ++i) { ## var err = errors[i]; #<tr><td>#: err.context #</td><td>#: err.error #</td></tr># } #</table></div></div><div class='k-action-buttons'><button class='k-button k-primary' data-bind='click: close'>#: messages.okText #</button></div>"}}),e.spreadsheet.dialogs.register("importError",E),B=m.extend({options:{title:s("useKeyboardDialog.title","Copying and pasting"),template:"#: messages.useKeyboardDialog.errorMessage #<div>Ctrl+C #: messages.useKeyboardDialog.labels.forCopy #</div><div>Ctrl+X #: messages.useKeyboardDialog.labels.forCut #</div><div>Ctrl+V #: messages.useKeyboardDialog.labels.forPaste #</div><div class=\"k-action-buttons\"><button class='k-button k-primary' data-bind='click: close'>#= messages.okText #</button></div>"}}),e.spreadsheet.dialogs.register("useKeyboard",B),M=d.extend({options:{title:s("linkDialog.title","Hyperlink"),template:"<div class='k-edit-label'><label>#: messages.linkDialog.labels.url #:</label></div><div class='k-edit-field'><input class='k-textbox' data-bind='value: url' title='#: messages.linkDialog.labels.url #' /></div><div class='k-action-buttons'><button class='k-button k-left' data-bind='click: remove'>#= messages.linkDialog.labels.removeLink #</button><button class='k-button k-primary' data-bind='click: apply'>#= messages.okText #</button><button class='k-button' data-bind='click: cancel'>#= messages.cancel #</button></div>",autoFocus:!1},open:function(t){var n,i,r=this;d.fn.open.apply(r,arguments),n=r.dialog().element,i=e.observable({url:t.link(),apply:function(){/\S/.test(i.url)||(i.url=null),r.trigger("action",{command:"HyperlinkCommand",options:{link:i.url}}),r.close()},remove:function(){i.url=null,i.apply()},cancel:r.close.bind(r)}),e.bind(n,i),n.find("input").focus().on("keydown",function(e){13==e.keyCode?(i.url=l(this).val(),e.stopPropagation(),e.preventDefault(),i.apply()):27==e.keyCode&&(e.stopPropagation(),e.preventDefault(),i.cancel())})}}),e.spreadsheet.dialogs.register("hyperlink",M),T=d.extend({options:{className:"k-spreadsheet-insert-comment",template:"<div class='k-edit-label'><label>#: messages.insertCommentDialog.labels.comment #:</label></div><div class='k-edit-field'><textarea rows='5' class='k-textbox' data-bind='value: comment'></textarea></div><div class='k-action-buttons'>  <button class='k-button k-left' data-bind='click: remove'>#: messages.insertCommentDialog.labels.removeComment #</button>  <button class='k-button k-primary' data-bind='click: apply'>#: messages.okText #</button>  <button class='k-button' data-bind='click: cancel'>#= messages.cancel #</button></div>",title:s("insertCommentDialog.title","Insert comment"),autoFocus:!1,width:450},open:function(t){var n,i,r=this;d.fn.open.apply(r,arguments),n=r.dialog().element,i=e.observable({comment:t.comment(),apply:function(){/\S/.test(i.comment)||(i.comment=null),r.trigger("action",{command:"InsertCommentCommand",options:{value:i.comment}}),r.close()},remove:function(){i.comment=null,i.apply()},cancel:r.close.bind(r)}),e.bind(n,i),n.find("textarea").focus()}}),e.spreadsheet.dialogs.register("insertComment",T),I=d.extend({options:{template:"<div class='k-spreadsheet-insert-image-dialog'>  <label data-bind='style: { background-image: imageUrl },                    css: { k-spreadsheet-has-image: hasImage, k-state-hovered: isHovered },                    events: { dragenter: dragEnter, dragover: stopEvent, dragleave: dragLeave, drop: drop }'>    <div data-bind='text: info'></div>    <input type='file' data-bind='events: { change: change }'           accept='image/png, image/jpeg, image/gif' />  </label></div><div class='k-action-buttons'>  <button class='k-button k-primary' data-bind='enabled: okEnabled, click: apply'>#: messages.okText #</button>  <button class='k-button' data-bind='click: cancel'>#= messages.cancel #</button></div>",title:s("insertImageDialog.title","Insert image"),width:"auto"},open:function(){var t,n,i=this;d.fn.open.apply(i,arguments),t=i.dialog().element,n=e.observable({okEnabled:!1,info:e.spreadsheet.messages.dialogs.insertImageDialog.info,imageUrl:"",hasImage:!1,isHovered:!1,_url:null,_image:null,apply:function(){window.URL.revokeObjectURL(n._url),i.trigger("action",{command:"InsertImageCommand",options:{blob:n._image,width:n._width,height:n._height}}),i.close()},cancel:i.close.bind(i),stopEvent:function(e){e.stopPropagation(),e.preventDefault()},drop:function(e){n.stopEvent(e),n.selectFile(e.originalEvent.dataTransfer.files),n.set("isHovered",!1)},dragEnter:function(e){n.stopEvent(e),n.set("isHovered",!0)},dragLeave:function(e){n.stopEvent(e),n.set("isHovered",!1)},change:function(e){n.selectFile(e.target.files)},selectFile:function(t){var i,r,o;for(r=0;r<t.length;++r)if(/^image\//i.test(t[r].type)){i=t[r];break}n._url&&window.URL.revokeObjectURL(n._url),i?(n._image=i,n._url=window.URL.createObjectURL(i),o=new Image,o.src=n._url,o.onload=function(){n._width=o.width,n._height=o.height,n.set("info",e.spreadsheet.messages.dialogs.insertImageDialog.info),n.set("okEnabled",!0),n.set("imageUrl","url('"+n._url+"')"),n.set("hasImage",!0)}):(n._image=null,n.set("info",e.spreadsheet.messages.dialogs.insertImageDialog.typeError),n.set("okEnabled",!1),n.set("imageUrl",""),n.set("hasImage",!1))}}),e.bind(t,n)}}),e.spreadsheet.dialogs.register("insertImage",I),o.prototype.trans=function(){var t,n=e.spreadsheet.messages.dialogs;for(t=0;t<this.path.length;++t)if(n=n[this.path[t]],!n)return this.def;return n})}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/sheetbinder.min",["kendo.core.min","kendo.data.min","spreadsheet/sheet.min"],e)}(function(){!function(e){var t,n;e.support.browser.msie&&e.support.browser.version<9||(t=function(e){return e},n=e.Class.extend({init:function(t){this.options=e.jQuery.extend({},this.options,t),this.columns=this._normalizeColumns(this.options.columns),this._sheet(),this._dataSource(),this._header(),this._boundRowsCount=0,this.dataSource.fetch()},_sheet:function(){this.sheet=this.options.sheet,this._sheetChangeHandler=this._sheetChange.bind(this),this._sheetDeleteRowHandler=this._sheetDeleteRow.bind(this),this._sheetInsertRowHandler=this._sheetInsertRow.bind(this),this.sheet.bind("change",this._sheetChangeHandler).bind("afterDeleteRow",this._sheetDeleteRowHandler).bind("afterInsertRow",this._sheetInsertRowHandler)},_sheetInsertRow:function(e){void 0!==e.index&&this.dataSource.insert(Math.max(e.index-1,0),{})},_sheetDeleteRow:function(e){var t,n;void 0!==e.index&&(t=this.dataSource,n=t.view()[e.index-1],n&&t.remove(n))},_header:function(){this.sheet.batch(function(){this.columns.forEach(function(e,t){this.sheet.range(0,t).value(e.title)}.bind(this))}.bind(this))},_sheetChange:function(n){var i,r,o,s,a,l,u;n.insertRow||n.deleteRow||n.recalc&&n.ref&&(i=this.dataSource,r=i.view(),o=this.columns,i.reader.model&&(s=i.reader.model.fields),!o.length&&r.length&&(o=Object.keys(r[0].toJSON())),a=o.map(function(n){var i=n.field;return i&&s&&s[i]&&"date"==s[i].type?e.spreadsheet.numberToDate:t}),this._skipRebind=!0,l=this.sheet._grid.normalize(n.ref),u=this.sheet.range(l).values(),l.forEach(function(e){var t,n,s,l,c;for(e=e.toRangeRef(),n=0,s=e.topLeft.row;s<=e.bottomRight.row;s++){for(t=r[s-1],t||(t=i.insert(s-1,{}),r=i.view()),l=0,c=e.topLeft.col;c<=e.bottomRight.col&&c<o.length;c++)t.set(o[c].field,a[c](u[n][l++]));n++}}),this._boundRowsCount=i.view().length,this._skipRebind=!1)},_normalizeColumns:function(e){return e.map(function(e){var t=e.field||e;return{field:t,title:e.title||t}})},_dataSource:function(){var t=this.options,n=t.dataSource;n=Array.isArray(n)?{data:n}:n,this.dataSource&&this._changeHandler?this.dataSource.unbind("change",this._changeHandler):this._changeHandler=this._change.bind(this),this.dataSource=e.data.DataSource.create(n).bind("change",this._changeHandler)},_change:function(){var t,n,i;this._skipRebind||this.sheet.trigger("dataBinding")||(t=this.dataSource.view(),n=this.columns,!n.length&&t.length&&(this.columns=n=this._normalizeColumns(Object.keys(t[0].toJSON())),this._header()),i=n.map(function(t){return e.getter(t.field)}),this.sheet.batch(function(){var e,n,r,o=Math.max(t.length,this._boundRowsCount);for(e=0;e<o;e++)for(n=0;n<i.length;n++)r=t[e]?i[n](t[e]):null,this.sheet.range(e+1,n).value(r)}.bind(this)),this._boundRowsCount=t.length,this.sheet.trigger("dataBound"))},destroy:function(){this.dataSource.unbind("change",this._changeHandler),this.sheet.unbind("change",this._sheetChangeHandler).unbind("deleteRow",this._sheetDeleteRowHandler).unbind("insertRow",this._sheetInsertRowHandler)},options:{columns:[]}}),e.spreadsheet.SheetDataSourceBinder=n)}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/filtermenu.min",["kendo.core.min","kendo.popup.min","kendo.treeview.min","kendo.numerictextbox.min","kendo.datepicker.min","kendo.datetimepicker.min"],e)}(function(){!function(e){function t(e){var t,n={},i=[];for(t=0;t<e.length;t++)n[e[t].value]?!n[e[t].value].checked&&e[t].checked&&(n[e[t].value].checked=!0):(n[e[t].value]=e[t],i.push(e[t]));return i}function n(t,i){var r,o,s,a,l,u,c=!1,h=t instanceof e.data.HierarchicalDataSource&&t.data(),d=this,f=this.values;for(r=0;r<h.length;r++)o=h[r],s=(""+o.text).toLowerCase(),a=i===!0||""===i||s.indexOf(i)>=0,l=n.bind(d),u=l(o.children,i),c=c||u||a,o.hidden=!a&&!u,i.length||f&&!f.length?o.checked=!o.hidden:f&&f.indexOf(o.text)!=-1&&(o.checked=!0);return h&&t.filter({field:"hidden",operator:"neq",value:!0}),c}function i(t){var n,r,o=t instanceof e.data.HierarchicalDataSource&&t.data();for(n=0;n<o.length;n++)r=o[n],r.checked=!1,r.hasChildren&&i(r.children)}function r(t){var n,i,r=e.spreadsheet.messages.filterMenu.operators,o=[];for(n in t)if(t.hasOwnProperty(n))for(i in t[n])t[n].hasOwnProperty(i)&&o.push({text:r[n][i],value:i,unique:n+"_"+i,type:n});return o}var o,s,a,l,u,c,h,d;e.support.browser.msie&&e.support.browser.version<9||(o=e.jQuery,s=e.ui.Widget,a={details:"k-details",button:"k-button",detailsSummary:"k-details-summary",detailsContent:"k-details-content",icon:"k-icon",iconCollapse:"k-i-arrow-45-down-right",iconExpand:"k-i-arrow-60-right",iconSearch:"k-i-zoom",textbox:"k-textbox",wrapper:"k-spreadsheet-filter-menu",filterByCondition:"k-spreadsheet-condition-filter",filterByValue:"k-spreadsheet-value-filter",valuesTreeViewWrapper:"k-spreadsheet-value-treeview-wrapper",actionButtons:"k-action-buttons"},e.spreadsheet.messages.filterMenu={all:"All",sortAscending:"Sort range A to Z",sortDescending:"Sort range Z to A",filterByValue:"Filter by value",filterByCondition:"Filter by condition",apply:"Apply",search:"Search",addToCurrent:"Add to current selection",clear:"Clear",blanks:"(Blanks)",operatorNone:"None",and:"AND",or:"OR",operators:{string:{contains:"Text contains",doesnotcontain:"Text does not contain",startswith:"Text starts with",endswith:"Text ends with",matches:"Text matches",doesnotmatch:"Text does not match"},date:{eq:"Date is",neq:"Date is not",lt:"Date is before",gt:"Date is after"},number:{eq:"Is equal to",neq:"Is not equal to",gte:"Is greater than or equal to",gt:"Is greater than",lte:"Is less than or equal to",lt:"Is less than"}}},l=s.extend({init:function(t,n){s.fn.init.call(this,t,n),this.element.addClass(d.classNames.details),this._summary=this.element.find("."+d.classNames.detailsSummary).on("click",this._toggle.bind(this));var i=n.expanded?d.classNames.iconCollapse:d.classNames.iconExpand;this._icon=o("<span />",{"class":d.classNames.icon+" "+i}).prependTo(this._summary),this._container=e.wrap(this._summary.next(),!0),n.expanded||this._container.hide()},options:{name:"Details"},events:["toggle"],visible:function(){return this.options.expanded},toggle:function(t){var n=e.fx(this._container).expand("vertical");n.stop()[t?"reverse":"play"](),this._icon.toggleClass(d.classNames.iconExpand,t).toggleClass(d.classNames.iconCollapse,!t),this.options.expanded=!t},_toggle:function(){var e=this.visible();this.toggle(e),this.trigger("toggle",{show:e})}}),e.data.binders.spreadsheetFilterValue=e.data.Binder.extend({init:function(t,n,i){e.data.Binder.fn.init.call(this,t,n,i),this._change=o.proxy(this.change,this),o(this.element).on("change",this._change)},refresh:function(){var e=this,t=e.bindings.spreadsheetFilterValue.get();o(e.element).val(t instanceof Date?"":t)},change:function(){var e=this.element.value;this.bindings.spreadsheetFilterValue.set(e)}}),e.data.binders.widget.spreadsheetFilterValue=e.data.Binder.extend({init:function(t,n,i){e.data.Binder.fn.init.call(this,t.element[0],n,i),this.widget=t,this._change=o.proxy(this.change,this),this.widget.first("change",this._change)},refresh:function(){var e=this.bindings.spreadsheetFilterValue,t=e.get(),n=o(this.widget.element).data("filterType");this.widget.value("date"===n&&t instanceof Date||"number"===n&&!isNaN(t)?t:null)},change:function(){var e=this.widget.value(),t=this.bindings.spreadsheetFilterValue;t.set(e)}}),u={filterByValue:"<div class='"+a.detailsSummary+"'>#= messages.filterByValue #</div><div class='"+a.detailsContent+"'><div class='k-textbox k-space-right'><input placeholder='#= messages.search #' data-#=ns#bind='events: { input: filterValues }' /><span class='k-icon k-i-zoom' /></div><div data-#=ns#bind='visible: hasActiveSearch'><input class='k-checkbox' type='checkbox' data-#=ns#bind='checked: appendToSearch' id='_#=guid#' /><label class='k-checkbox-label' for='_#=guid#'>#= messages.addToCurrent #</label></div><div class='"+a.valuesTreeViewWrapper+"'><div data-#=ns#role='treeview' data-#=ns#checkboxes='{ checkChildren: true }' data-#=ns#bind='source: valuesDataSource, events: { check: valuesChange, select: valueSelect }' /></div></div>",filterByCondition:"<div class='"+a.detailsSummary+"'>#= messages.filterByCondition #</div><div class='"+a.detailsContent+'\'><div><select data-#=ns#role="dropdownlist"data-#=ns#bind="value: operator, source: operators, events: { change: operatorChange } "data-value-primitive="false"data-option-label="#=messages.operatorNone#"data-height="auto"data-text-field="text"data-value-field="unique"></select></div><div data-#=ns#bind="visible: isString"><input data-filter-type="string" data-#=ns#bind="spreadsheetFilterValue: customFilter.criteria[0].value" class="k-textbox" /></div><div data-#=ns#bind="visible: isNumber"><input data-filter-type="number" data-#=ns#role="numerictextbox" data-#=ns#bind="spreadsheetFilterValue: customFilter.criteria[0].value" /></div><div data-#=ns#bind="visible: isDate"><input data-filter-type="date" data-#=ns#role="datepicker" data-#=ns#bind="spreadsheetFilterValue: customFilter.criteria[0].value" /></div></div>',
menuItem:"<li data-command='#=command#' data-dir='#=dir#'><span class='k-icon k-i-#=iconClass#'></span>#=text#</li>",actionButtons:"<button data-#=ns#bind='click: apply' class='k-button k-primary'>#=messages.apply#</button><button data-#=ns#bind='click: clear' class='k-button'>#=messages.clear#</button>"},c=e.spreadsheet.FilterMenuViewModel=e.data.ObservableObject.extend({valuesChange:function(t){var n=t?t.sender.dataSource:this.valuesDataSource,i=function(e){return e.checked},r=function(t){return"date"===t.dataType?e.spreadsheet.dateToNumber(t.value):t.value},o=function(e,t,n){return n.lastIndexOf(e)===t},s=n.data(),a=s[0].children.data().toJSON(),l=a.filter(function(e){return"blank"===e.dataType});l=!!l.length&&l[0].checked,a=a.filter(i).map(r),this.appendToSearch&&this.valueFilter&&this.valueFilter.values.length&&(a=a.concat(this.valueFilter.values.toJSON()).sort().filter(o)),this.set("valueFilter",{values:a,blanks:l})},valueSelect:function(e){e.preventDefault();var t=e.sender.dataItem(e.node);t.set("checked",!t.checked)},hasActiveSearch:!1,appendToSearch:!1,filterValues:function(e){var t,r="string"==typeof e?e:o(e.target).val().toLowerCase(),s=this.valuesDataSource;this.set("hasActiveSearch",!!r),t=n.bind(this.valueFilter),i(s),t(s,r)},reset:function(){this.set("customFilter",{logic:"and",criteria:[{operator:null,value:null}]}),this.set("valueFilter",{values:[]})},operatorChange:function(e){var t=e.sender.dataItem();this.set("operatorType",t.type),this.get("customFilter")||this.reset(),this.set("customFilter.criteria[0].operator",t.value)},isNone:function(){return void 0===this.get("operatorType")},isString:function(){return"string"===this.get("operatorType")},isNumber:function(){return"number"===this.get("operatorType")},isDate:function(){return"date"===this.get("operatorType")}}),h=e.spreadsheet.FilterMenuController={valuesTree:function(t,n){return[{text:e.spreadsheet.messages.filterMenu.all,expanded:!0,checked:!1,items:this.values(t.resize({top:1}),n)}]},values:function(n,i){var r=[],o=e.spreadsheet.messages.filterMenu,s=n.column(i),a=n.sheet();return s.forEachCell(function(t,n,i){var s,l,u,c=!0;a.isHiddenRow(t)&&(c=!1),s=i.value,l=i.dataType,u=i.text,l=void 0===s?"blank":i.format?e.spreadsheet.formatting.type(s,i.format):typeof s,u=null!==s&&i.format?e.spreadsheet.formatting.text(s,i.format):"blank"==l?o.blanks:s,"percent"!==l&&"currency"!==l||(l="number"),"date"===l&&(s=e.spreadsheet.numberToDate(s)),r.push({dataType:l,value:s,text:u,checked:c})}),r=t(r),r.sort(function(e,t){return e.dataType===t.dataType?0:"blank"===e.dataType||"blank"===t.dataType?"blank"===e.dataType?-1:1:"number"===e.dataType||"number"===t.dataType?"number"===e.dataType?-1:1:"date"===e.dataType||"date"===t.dataType?"date"===e.dataType?-1:1:0}),r},filterType:function(e,t){var n,i,r,o=e.sheet(),s=this.filterForColumn(t,o);return s=s&&s.filter.toJSON(),s&&"custom"==s.filter&&(i=s.criteria[0].value,i instanceof Date?n="date":"string"==typeof i?n="string":"number"==typeof i&&(n="number")),n||(r=this.values(e.row(1),t)[0],n=r&&r.dataType,"blank"==n&&(n=null)),n},filterForColumn:function(e,t){var n,i=t.filter();return i&&(n=i.columns.filter(function(t){return t.index===e})[0]),n},filter:function(e,t){var n,i,r,o,s=this.filterForColumn(e,t);if(s)return n=s.filter.toJSON(),i=n.filter,delete n.filter,r={type:i,options:n},o=n.criteria,o&&o.length&&(r.operator=o[0].operator),r}},d=s.extend({init:function(e,t){s.call(this,e,t),this.element.addClass(d.classNames.wrapper),this.viewModel=new c({active:"value",operator:null,operators:r(this.options.operators),clear:this.clear.bind(this),apply:this.apply.bind(this)}),this._filterInit(),this._popup(),this._sort(),this._filterByCondition(),this._filterByValue(),this._actionButtons()},options:{name:"FilterMenu",column:0,range:null,operators:{string:{contains:"Text contains",doesnotcontain:"Text does not contain",startswith:"Text starts with",endswith:"Text ends with",matches:"Text matches",doesnotmatch:"Text does not match"},date:{eq:"Date is",neq:"Date is not",lt:"Date is before",gt:"Date is after"},number:{eq:"Is equal to",neq:"Is not equal to",gte:"Is greater than or equal to",gt:"Is greater than",lte:"Is less than or equal to",lt:"Is less than"}}},events:["action"],destroy:function(){s.fn.destroy.call(this),this.menu.destroy(),this.valuesTreeView.destroy(),this.popup.destroy()},openFor:function(e){this.popup.setOptions({anchor:e}),this.popup.open()},close:function(){this.popup.close()},clear:function(){this.action({command:"ClearFilterCommand",options:{column:this.options.column}}),this.viewModel.reset(),this.close()},apply:function(){var e,t,n;this._active(),e={operatingRange:this.options.range,column:this.options.column},"value"===this.viewModel.active?(this.viewModel.valuesChange({sender:this.valuesTreeView}),t=this.viewModel.valueFilter.toJSON(),(t.blanks||t.values&&t.values.length)&&(e.valueFilter=t)):"custom"===this.viewModel.active&&(n=this.viewModel.customFilter.toJSON(),n.criteria.length&&null!==n.criteria[0].value&&(e.customFilter=n)),(e.valueFilter||e.customFilter)&&this.action({command:"ApplyFilterCommand",options:e})},action:function(e){this.trigger("action",o.extend({},e))},_filterInit:function(){var e,t=this.options.column,n=this.options.range,i=n.sheet(),r=h.filter(t,i);r?(e=h.filterType(n,t),this.viewModel.set("active",r.type),this.viewModel.set(r.type+"Filter",r.options),"custom"==r.type&&(this.viewModel.set("operator",e+"_"+r.operator),this.viewModel.set("operatorType",e))):this.viewModel.reset()},_popup:function(){this.popup=this.element.kendoPopup({copyAnchorStyles:!1}).data("kendoPopup")},_sort:function(){var t=e.template(d.templates.menuItem),n=e.spreadsheet.messages.filterMenu,i=[{command:"sort",dir:"asc",text:n.sortAscending,iconClass:"sort-asc"},{command:"sort",dir:"desc",text:n.sortDescending,iconClass:"sort-desc"}],r=o("<ul />",{html:e.render(t,i)}).appendTo(this.element);this.menu=r.kendoMenu({orientation:"vertical",select:function(e){var t=o(e.item).data("dir"),n=this.options.range.resize({top:1}),i={value:t,sheet:!1,operatingRange:n,column:this.options.column};n.isSortable()?this.action({command:"SortCommand",options:i}):this.close()}.bind(this)}).data("kendoMenu")},_appendTemplate:function(t,n,i,r){var s=e.template(t),a=o("<div class='"+n+"'/>").html(s({messages:e.spreadsheet.messages.filterMenu,guid:e.guid(),ns:e.ns}));return this.element.append(a),i&&(i=new l(a,{expanded:r,toggle:this._detailToggle.bind(this)})),e.bind(a,this.viewModel),a},_detailToggle:function(e){this.element.find("[data-role=details]").not(e.sender.element).data("kendoDetails").toggle(!e.show)},_filterByCondition:function(){var e="custom"===this.viewModel.active;this._appendTemplate(d.templates.filterByCondition,d.classNames.filterByCondition,!0,e)},_filterByValue:function(){var t,n="value"===this.viewModel.active,i=this._appendTemplate(d.templates.filterByValue,d.classNames.filterByValue,!0,n);this.valuesTreeView=i.find("[data-role=treeview]").data("kendoTreeView"),t=h.valuesTree(this.options.range,this.options.column),this.viewModel.set("valuesDataSource",new e.data.HierarchicalDataSource({data:t}))},_actionButtons:function(){this._appendTemplate(d.templates.actionButtons,d.classNames.actionButtons,!1)},_active:function(){var e=this.element.find("[data-role=details]").filter(function(e,t){return o(t).data("kendoDetails").visible()});e.hasClass(d.classNames.filterByValue)?this.viewModel.set("active","value"):e.hasClass(d.classNames.filterByCondition)&&this.viewModel.set("active","custom")}}),e.spreadsheet.FilterMenu=d,o.extend(!0,d,{classNames:a,templates:u}))}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/editor.min",["kendo.core.min"],e)}(function(){!function(e){function t(e,t){return e&&(e.top!==t.top||e.left!==t.left)}if(!(e.support.browser.msie&&e.support.browser.version<9)){var n=e.Observable.extend({init:function(t){e.Observable.fn.init.call(this),this.view=t,this.formulaBar=t.formulaBar,this._active=!1,this.barInput=t.formulaBar.formulaInput,this.cellInput=t.formulaInput,this.barInput.syncWith(this.cellInput),this.cellInput.syncWith(this.barInput),this.barInput.bind("keyup",this._triggerUpdate.bind(this)),this.cellInput.bind("keyup",this._triggerUpdate.bind(this)),this.barInput.bind("blur",this._blur.bind(this)),this.cellInput.bind("blur",this._blur.bind(this))},events:["activate","deactivate","change","update"],_blur:function(){this.deactivate()},_triggerUpdate:function(){this.trigger("update",{value:this.value()})},activeEditor:function(){var t=null,n=e._activeElement();return this.barElement()[0]===n?t=this.barInput:this.cellElement()[0]===n&&(t=this.cellInput),t},activate:function(e){return this._active=!0,this._rect=e.rect,this._range=e.range,this.cellInput.position(e.rect),this.cellInput.resize(e.rect),this.cellInput.tooltip(e.tooltip),this.cellInput.activeCell=this.barInput.activeCell=this._range.topLeft(),this.cellInput.activeSheet=this.barInput.activeSheet=this._range._sheet,this.trigger("activate"),this},deactivate:function(e){var t=this.cellInput;this._active&&(this._active=!1,e||t.value()==this._value||this.trigger("change",{value:t.value(),range:this._range}),this._rect=null,t.hide(),this.trigger("deactivate"))},enable:function(e){this.barInput.enable(e),this.cellInput.enable(e)},barElement:function(){return this.barInput.element},cellElement:function(){return this.cellInput.element},focus:function(e){e=e||"cell","cell"===e?(this.cellInput.element.focus(),this.cellInput.end()):this.barInput.element.focus()},isActive:function(){return this._active},isFiltered:function(){return this.barInput.popup.visible()||this.cellInput.popup.visible()},canInsertRef:function(e){var t=this.activeEditor();return t&&t.canInsertRef(e)},highlightedRefs:function(){var e=this.activeEditor(),t=[];return e&&(t=e.highlightedRefs()),t},scale:function(){this.cellInput.scale()},toggleTooltip:function(e){this.cellInput.toggleTooltip(t(this._rect,e))},value:function(e,t){return void 0===e?this.barInput.value():(null===e&&(e=""),this._value=e,this.barInput.value(e),this.cellInput.value(e),void this.barInput.element.toggleClass("k-spreadsheet-array-formula",!!t))},insertNewline:function(){this.activeEditor().insertNewline(),this.scale()},select:function(){this.activeEditor().select()}});e.spreadsheet.SheetEditor=n}}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/autofill.min",["spreadsheet/runtime.min","spreadsheet/range.min"],e)}(function(){"use strict";function e(e){var t,n,i,r,o,s=e.length,a=(s+1)/2,l=e.reduce(function(e,t){return e+t},0)/s,u=0,c=0;for(t=0;t<s;t++)n=t+1-a,i=e[t]-l,u+=n*i,c+=n*n;return c?(r=u/c,o=l-r*a,function(e){return o+r*(e+1)}):function(t){return e[t%e.length]}}function t(t){function r(e){var t,n=e[1]-e[0];for(t=2;t<e.length;++t)if(e[t]-e[t-1]!=n)return null;return n}function o(e){return e.map(function(e){return e.number})}var s=[],a=t.map(function(e){return e.formula||e.value});return i(a,function(t,n,i,l){var u,c,h,d,f;for("number"==i?(c=o(l),u=e(c)):"string"==i||"formula"==i||"boolean"==i?u=function(e,t){return a[t]}:Array.isArray(i)?1==l.length?u=function(e){return i[(l[0].number+e)%i.length]}:(h=r(o(l)),u=null==h?function(e){return l[e%l.length].value}:function(e){var t=l[0].number+h*e;return i[t%i.length]}):"null"!=i?(c=o(l),1==c.length&&c.push(c[0]+1),c=e(c),u=function(e,t){return a[t].replace(/^(.*\D)\d+/,"$1"+c(e,t))}):u=function(){return null},d={f:u,begin:t,end:n,len:n-t},f=t;f<n;++f)s[f]=d}),function(e,i){var r=s[i],o=e/a.length|0,l=e%a.length,u=o*r.len+l-r.begin,c=r.f(u,i),d=n(t[i]);return delete d.enable,c instanceof h?d.formula=c:d.value=c,d}}function n(e){var t={};return Object.keys(e||{}).forEach(function(n){t[n]=e[n]}),t}function i(e,t){var n,i,o=null,s=0,a=[];for(i=0;i<e.length;++i)n=r(e[i]),a.push(n),null!=o&&n.type!==o.type&&(t(s,i,o.type,a.slice(s,i)),s=i),o=n;t(s,i,o.type,a.slice(s,i))}function r(e){var t,n;if("number"==typeof e)return{type:"number",number:e};if("string"==typeof e)return(t=s(e))?t:(n=/^(.*\D)(\d+)/.exec(e),n?(e=e.replace(/^(.*\D)\d+/,"$1-######"),{type:e,match:n,number:parseFloat(n[2])}):{type:"string"});if("boolean"==typeof e)return{type:"boolean"};if(null==e)return{type:"null"};if(e instanceof h)return{type:"formula"};throw window.console.error(e),Error("Cannot fill data")}function o(){var e=kendo.culture();return[e.calendars.standard.days.namesAbbr,e.calendars.standard.days.names,e.calendars.standard.months.namesAbbr,e.calendars.standard.months.names]}function s(e){var t,n,i,r,s=e.toLowerCase(),a=o();for(t=0;t<a.length;++t)for(n=a[t],i=n.length;--i>=0;)if(r=n[i].toLowerCase(),r==s)return{type:n,number:i,value:e}}function a(e){var t,n,i=e.length,r=e[0].length,o=[];for(t=0;t<r;++t)for(o[t]=[],n=0;n<i;++n)o[t][n]=e[n][t];return o}var l,u,c,h,d,f,p;kendo.support.browser.msie&&kendo.support.browser.version<9||(l=kendo.spreadsheet,u=l.Range,c=l.calc.runtime,h=c.Formula,d="incompatibleRanges",f="noFillDirection",p=u.FillError=function(e){this.code=e},u.prototype._previewFillFrom=function(e,n){var i,r,o,s,l,u,c,h,m,g,v,b,w,y,_,k,x=this,C=x._sheet;if("string"==typeof e&&(e=C.range(e)),i=e._ref.toRangeRef().clone().setSheet(C.name()),r=x._ref.toRangeRef().clone().setSheet(C.name()),i.intersects(r)){if(i.eq(r))return null;if(r=r.clone(),i.topLeft.eq(r.topLeft))if(i.width()==r.width())r.topLeft.row+=i.height(),n=0;else{if(i.height()!=r.height())throw new p(d);r.topLeft.col+=i.width(),n=1}else{if(!i.bottomRight.eq(r.bottomRight))throw new p(d);if(i.width()==r.width())r.bottomRight.row-=i.height(),n=2;else{if(i.height()!=r.height())throw new p(d);r.bottomRight.col-=i.width(),n=3}}return C.range(r)._previewFillFrom(e,n)}if(null==n)if(i.topLeft.col==r.topLeft.col)n=i.topLeft.row<r.topLeft.row?0:2;else{if(i.topLeft.row!=r.topLeft.row)throw new p(f);n=i.topLeft.col<r.topLeft.col?1:3}if(o=1&n,s=2&n,o&&i.height()!=r.height()||!o&&i.width()!=r.width())throw new p(d);for(l=e._properties(),o?u=r.width():(l=a(l),u=r.height()),c=Array(l.length),h=null,m=0;m<l.length;++m)for(g=l[m],v=t(g),b=c[m]=Array(u),w=0;w<u;++w)y=s?-w-1:g.length+w,_=s?g.length-w%g.length-1:w%g.length,k=b[s?u-w-1:w]=v(y,_),null!=k.value&&(h=k.value);return o||(c=a(c)),{props:c,direction:n,dest:x,hint:h}},u.prototype.fillFrom=function(e,t){var n=this._previewFillFrom(e,t);return n.dest._properties(n.props,!0),n.dest})},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/nameeditor.min",["kendo.core.min"],e)}(function(){!function(e){var t,n,i;e.support.browser.msie&&e.support.browser.version<9||(t=e.jQuery,n={input:"k-spreadsheet-name-editor",list:"k-spreadsheet-name-list"},i=e.ui.Widget.extend({init:function(i,r){var o,s,a;e.ui.Widget.call(this,i,r),i.addClass(n.input),o=r.messages.nameBox||"Name Box",s=new e.data.DataSource({transport:{read:function(t){var n=[];this._workbook.forEachName(function(t){!t.hidden&&t.value instanceof e.spreadsheet.Ref&&n.push({name:t.name})}),t.success(n)}.bind(this),cache:!1}}),a=t("<input />").attr("title",o).attr("aria-label",o),this.combo=a.appendTo(i).kendoComboBox({clearButton:!1,dataTextField:"name",dataValueField:"name",template:"#:data.name#<a class='k-button-delete' href='\\#'><span class='k-icon k-i-close'></span></a>",dataSource:s,autoBind:!1,ignoreCase:!0,change:this._on_listChange.bind(this),noDataTemplate:"<div></div>",open:function(){s.read()}}).getKendoComboBox(),this.combo.input.on("keydown",this._on_keyDown.bind(this)).on("focus",this._on_focus.bind(this)),this.combo.popup.element.addClass("k-spreadsheet-names-popup").on("mousemove",function(e){e.stopPropagation()}).on("click",".k-button-delete",function(e){e.preventDefault(),e.stopPropagation();var n=t(e.target).closest(".k-item");n=this.combo.dataItem(n),this._deleteItem(n.name)}.bind(this))},value:function(e){return void 0===e?this.combo.value():void this.combo.value(e)},_deleteItem:function(e){this.trigger("delete",{name:e})},_on_keyDown:function(e){switch(e.keyCode){case 27:this.combo.value(this._prevValue),this.trigger("cancel");break;case 13:this.trigger("enter")}},_on_focus:function(){this._prevValue=this.combo.value()},_on_listChange:function(){var e=this.combo.value();e&&this.trigger("select",{name:e})}}),e.spreadsheet.NameEditor=i)}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("spreadsheet/print.min",["kendo.pdf.min","spreadsheet/sheet.min","spreadsheet/range.min","spreadsheet/references.min","spreadsheet/numformat.min","util/text-metrics.min"],e)}(function(){"use strict";function e(e,t,n,i,r){var o=0,s=[],a=t,l=0;return t&&n&&(n*=t),e.forEach(function(e,u){null!=i&&u<i&&(l+=e),t&&o+e>a&&(a-o<n&&(o=t*Math.ceil(o/t)+l,l>0&&r.push(o-l)),a+=t*Math.ceil(e/t)),s.push(o),o+=e}),s.push(o),s}function t(t,i,u){var c,h,d,f,p,m,g,v,b,y,_,k,x,C,F,R,S,A,D=t._grid;return i=D.normalize(i),c=D.rectangle(i),h=[],t._drawings.forEach(function(e){var n=t.drawingBoundingBox(e);n.intersects(c)&&h.push({drawing:e,box:n.offset(-c.left,-c.top)})}),d=[],f=[],p=[],m=t._getMergedCells(i),g=-1,v=-1,t.forEach(i,function(e,n,r){var o,a,l,c=e-i.topLeft.row,b=n-i.topLeft.col,y=t.rowHeight(e),_=t.columnWidth(n);u.forScreen||(r.drawings=h.filter(function(t){var i=t.drawing.topLeftCell;return i&&i.row==e&&i.col==n})),b||f.push(y),c||p.push(_),!t.isHiddenColumn(n)&&!t.isHiddenRow(e)&&y&&_&&(o=u.forScreen||s(r),(u.emptyCells||o)&&(a=new w(e,n).print(),m.secondary[a]||(o?(g=Math.max(g,c),v=Math.max(v,b)):r.empty=!0,r.row=c,r.col=b,l=m.primary[a],l?(delete m.primary[a],r.merged=!0,r.rowspan=l.height(),r.colspan=l.width(),u.forScreen&&(r.width=t._columns.sum(l.topLeft.col,l.bottomRight.col),r.height=t._rows.sum(l.topLeft.row,l.bottomRight.row))):(r.rowspan=1,r.colspan=1),d.push(r))))}),f=f.slice(0,g+1),p=p.slice(0,v+1),b=u.pageWidth,y=u.pageHeight,_=u.scale||1,u.fitWidth&&(k=p.reduce(r,0),k>b&&(_=b/k)),b=Math.ceil(b/_),y=Math.ceil(y/_),x=[],C=e(f,y||0,u.maxEmpty,u.headerRows,x),F=e(p,b||0,u.maxEmpty),R=0,S=0,A=[],d=d.filter(function(e){return(!e.empty||!(e.row>g||e.col>v))&&(u.headerRows&&e.row<u.headerRows&&A.push(e),e.left=F[e.col],e.top=C[e.row],e.merged?u.forScreen?(e.right=e.left+e.width,e.bottom=e.top+e.height):(e.right=o(F,e.col+e.colspan),e.bottom=o(C,e.row+e.rowspan),e.width=e.right-e.left,e.height=e.bottom-e.top):(e.width=p[e.col],e.height=f[e.row],e.bottom=e.top+e.height,e.right=e.left+e.width),u.forScreen||e.drawings.forEach(function(t){var n=t.box;n.left=e.left+t.drawing.offsetX,n.top=e.top+t.drawing.offsetY,n.right=n.left+n.width,n.bottom=n.top+n.height}),R=Math.max(R,e.right),S=Math.max(S,e.bottom),!0)}),Object.keys(m.primary).forEach(function(e){var n=m.primary[e];t.forEach(n.topLeft.toRangeRef(),function(e,r,o){var s=e-i.topLeft.row,a=r-i.topLeft.col;o.merged=!0,o.colspan=n.width(),o.rowspan=n.height(),o.top=s<0?-t._rows.sum(e,e-s-1):C[s],o.left=a<0?-t._columns.sum(r,r-a-1):F[a],o.height=t._rows.sum(n.topLeft.row,n.bottomRight.row),o.width=t._columns.sum(n.topLeft.col,n.bottomRight.col),o.height>0&&o.width>0&&(o.right=o.left+o.width,o.bottom=o.top+o.height,o.row=s,o.col=a,d.push(o))})}),u.headerRows&&(x.forEach(function(e){A.forEach(function(t){t=n(t),t.top+=e,t.bottom=t.top+t.height,d.push(t)}),C.push(e)}),C.sort(l)),{width:R,height:S,cells:d.sort(a),scale:_,xCoords:F,yCoords:C,drawings:h}}function n(e,t){return t||(t={}),Object.assign?Object.assign(t,e):Object.keys(e).reduce(function(t,n){return t[n]=e[n],t},t)}function i(e,t){return e.size===t.size&&e.color===t.color}function r(e,t){return e+t}function o(e,t){return t<e.length?e[t]:e[e.length-1]}function s(e){return null!=e.value||e.merged||null!=e.background||null!=e.borderRight||null!=e.borderBottom||null!=e.validation&&!e.validation.value}function a(e,t){return e.top<t.top?-1:e.top==t.top?e.left<t.left?-1:e.left==t.left?0:1:1}function l(e,t){return e<t?-1:e>t?1:0}function u(e,t,n,i){function r(r,o){function s(e){return!(e.right<=v||e.left>=b||e.bottom<=w||e.top>=_)&&(C=Math.max(e.bottom,C),F=Math.max(e.right,F),!0)}var a,l,d,f,p,m,v=o*u,b=v+u,w=r*h,_=w+h,C=0,F=0,R=t.cells.filter(s),S=t.drawings.filter(function(e){return s(e.box)});C=Math.min(C,_),F=Math.min(F,b),(R.length||S.length)&&(a=new y.Group,n.append(a),l=new y.Group,a.append(l),l.clip(y.Path.fromRect(new k.Rect([v-1,w-1],[F+1,C+1]))),d=k.Matrix.scale(t.scale,t.scale).multiplyCopy(k.Matrix.translate(-v,-w)),(i.hCenter||i.vCenter)&&(d=d.multiplyCopy(k.Matrix.translate(i.hCenter?(b-F)/2:0,i.vCenter?(_-C)/2:0))),l.transform(d),i.guidelines&&(f=null,t.xCoords.forEach(function(e){e=Math.min(e,F),e!==f&&e>=v&&e<=b&&(f=e,l.append((new y.Path).moveTo(e,w).lineTo(e,C).close().stroke(i.guideColor,x)))}),f=null,t.yCoords.forEach(function(e){e=Math.min(e,C),e!==f&&e>=w&&e<=_&&(f=e,l.append((new y.Path).moveTo(v,e).lineTo(F,e).close().stroke(i.guideColor,x)))})),p=g(),R.forEach(function(t){c(t,l,i),p.add(t,e)}),m=new y.Group,p.vert.forEach(function(e){e.forEach(function(e){e.rendered||(e.rendered=!0,m.append((new y.Path).moveTo(e.x,e.top).lineTo(e.x,e.bottom).close().stroke(e.color,e.size)))})}),p.horiz.forEach(function(e){e.forEach(function(e){e.rendered||(e.rendered=!0,m.append((new y.Path).moveTo(e.left,e.y).lineTo(e.right,e.y).close().stroke(e.color,e.size)))})}),l.append(m),S.forEach(function(t){var n,i,r=t.drawing,o=r.image;null!=o&&(n=t.box,i=e._workbook.imageUrl(o),l.append(new y.Image(i,new k.Rect([n.left,n.top],[n.width,n.height])).opacity(r.opacity)))}))}var o,s,a=Math.ceil(t.width/i.pageWidth),l=Math.ceil(t.height/i.pageHeight),u=Math.ceil(i.pageWidth/t.scale),h=Math.ceil(i.pageHeight/t.scale);for(o=0;o<l;++o)for(s=0;s<a;++s)r(o,s)}function c(e,t,n){var i,r,o,s,a,l,u,c=new y.Group;if(t.append(c),i=new k.Rect([e.left,e.top],[e.width,e.height]),(e.background||e.merged)&&(r=i,n.guidelines&&(r=i.clone(),r.origin.x+=x/2+.1,r.origin.y+=x/2+.1,r.size.width-=x+.2,r.size.height-=x+.2),c.append(new y.Rect(r).fill(e.background||"#fff").stroke(null))),o=e.value,null!=o){if(s="number"==typeof o?"number":null,a=new y.Group,a.clip(y.Path.fromRect(i)),c.append(a),u=e.format,u||"number"!=s||o==Math.floor(o)||(u="0.##############"),u?(l=_.textAndColor(o,u),o=l.text,l.type&&(s=l.type)):o+="",!e.textAlign)switch(s){case"number":case"date":case"percent":case"currency":e.textAlign="right";break;case"boolean":e.textAlign="center"}d(o,l&&l.color||e.color||"#000",e,a)}}function h(e,t){if(e.indent){var n=1.4*e.indent;switch(t.textAlign){case null:case"left":t.paddingLeft=n+"ch";break;case"right":t.paddingRight=n+"ch";break;case"center":t.paddingLeft=n/2+"ch",t.paddingRight=n/2+"ch"}}}function d(e,t,n,i){var r,o;switch(C||(C=document.createElement("div"),C.style.position="fixed",C.style.left="0px",C.style.top="0px",C.style.visibility="hidden",C.style.overflow="hidden",C.style.boxSizing="border-box",C.style.lineHeight="normal",document.body.appendChild(C)),C.firstChild&&C.removeChild(C.firstChild),C.style.padding="2px 4px",C.style.color=t,C.style.font=f(n),C.style.width=n.width+"px",C.style.textAlign=n.textAlign||"left",C.style.textDecoration=n.underline?"underline":"none",h(n,C.style),n.wrap?(C.style.whiteSpace="pre-wrap",C.style.overflowWrap=C.style.wordWrap="break-word"):(C.style.whiteSpace="pre",C.style.overflowWrap=C.style.wordWrap="normal"),C.appendChild(document.createTextNode(e)),r=0,n.verticalAlign){case"center":r=n.height-C.offsetHeight>>1;break;case void 0:case null:case"bottom":r=n.height-C.offsetHeight}r<0&&(r=0),o=kendo.drawing.drawDOM.drawText(C),o.transform(k.Matrix.translate(n.left,n.top+r)),i.append(o)}function f(e){var t=[];return e.italic&&t.push("italic"),e.bold&&t.push("bold"),t.push((e.fontSize||12)+"px"),t.push(e.fontFamily||"Arial"),t.join(" ")}function p(e,n,i,r){var o,s,a,l,c;null==i&&null==r&&(r=n,i={},n=b.SHEETREF),null==r&&(r=i,n instanceof b.Range||n instanceof b.Ref||"string"==typeof n?i={}:(i=n,n=b.SHEETREF)),i=kendo.jQuery.extend({paperSize:"A4",landscape:!0,margin:"1cm",guidelines:!0,guideColor:"#aaa",emptyCells:!0,fitWidth:!1,center:!1,headerRows:null,maxEmpty:.2,scale:1},i),o=new y.Group,s=kendo.pdf.getPaperOptions(i),o.options.set("pdf",{author:i.author,creator:i.creator,date:i.date,keywords:i.keywords,margin:s.margin,multiPage:!0,paperSize:s.paperSize,subject:i.subject,title:i.title}),a=s.paperSize[0],l=s.paperSize[1],s.margin&&(a-=s.margin.left+s.margin.right+1,l-=s.margin.top+s.margin.bottom+1),i.pageWidth=a,i.pageHeight=l,c=t(e,e._ref(n),i),u(e,c,o,i),r(o)}function m(){}function g(){function e(e,i){var r,o;i&&(r=i._properties,o=i._grid,e.borderLeft=r.get("vBorders",o.index(e.row,e.col)),e.borderRight=r.get("vBorders",o.index(e.row,e.col+e.colspan)),e.borderTop=r.get("hBorders",o.index(e.row,e.col)),e.borderBottom=r.get("hBorders",o.index(e.row+e.rowspan,e.col))),e.borderLeft&&t(e.row,e.col,e.borderLeft,e.left,e.top,e.bottom),e.borderRight&&t(e.row,e.col+e.colspan,e.borderRight,e.right,e.top,e.bottom),e.borderTop&&n(e.row,e.col,e.borderTop,e.top,e.left,e.right),e.borderBottom&&n(e.row+e.rowspan,e.col,e.borderBottom,e.bottom,e.left,e.right)}function t(e,t,n,r,s,a){var l=o[t]||(o[t]=new m),u=e>0&&l[e-1];u&&i(u,n)?(l[e]=u,u.bottom=a):l[e]={size:n.size,color:n.color,x:r,top:s,bottom:a}}function n(e,t,n,o,s,a){var l=r[e]||(r[e]=new m),u=t>0&&l[t-1];u&&i(u,n)?(l[t]=u,u.right=a):l[t]={size:n.size,color:n.color,y:o,left:s,right:a}}var r=new m,o=new m;return{add:e,horiz:r,vert:o}}function v(e){function t(e){var t,n;if(null!=e){for(t=12,n=e.length;--n>=0;)t+=r(e.charAt(n));return t}return 0}function i(e){return o?n(e,{borderLeft:o,borderTop:o,borderRight:o,borderBottom:o}):e}var r,o,s=new $.Deferred,a=s.promise();return e=n(e,{dataSource:null,guidelines:!0,guideColor:"#000",columns:null,headerBackground:"#999",headerColor:"#000",oddBackground:null,evenBackground:null,fontFamily:"Arial",fontSize:12,paperSize:"A4",margin:"1cm",landscape:!0,fitWidth:!1,scale:1,rowHeight:20,maxEmpty:1,useGridFormat:!0}),kendo.drawing.pdf.defineFont(kendo.drawing.drawDOM.getFontFaces(document)),r=R(e.fontFamily,e.fontSize),o=e.guidelines?{size:1,color:e.guideColor}:null,e.dataSource.fetch(function(){var n,r,o,a,l,u=e.dataSource.data();return u.length?(n=e.columns.map(function(e){return"string"==typeof e?{title:e,field:e}:e}),r=n.map(function(e){return e.title||e.field}),o=r.map(t),a=u.map(function(r,s){return{cells:n.map(function(n,a){var l=r[n.field];return e.useGridFormat&&(null!=l&&(n.format?l=kendo.format(n.format,l):l+=""),o[a]=Math.max(t(l),o[a])),i({value:l,format:e.useGridFormat?null:n.format,background:s%2?e.evenBackground:e.oddBackground})})}}),a.unshift({cells:r.map(function(t){return i({value:t,background:e.headerBackground,color:e.headerColor})})}),l=new kendo.spreadsheet.Sheet(a.length+1,n.length+1,e.rowHeight,50,20,20,{fontFamily:e.fontFamily,fontSize:e.fontSize,verticalAlign:"center"}),l.fromJSON({name:"Sheet1",rows:a,columns:o.map(function(e,t){return{index:t,width:e}})}),void l.draw({paperSize:e.paperSize,landscape:e.landscape,margin:e.margin,guidelines:!1,scale:e.scale,fitWidth:e.fitWidth,maxEmpty:e.maxEmpty,headerRows:1},s.resolve.bind(s))):s.reject("Empty dataset")}),a}var b,w,y,_,k,x,C,F,R;kendo.support.browser.msie&&kendo.support.browser.version<9||(b=kendo.spreadsheet,w=b.CellRef,y=kendo.drawing,_=b.formatting,k=kendo.geometry,x=.8,b.Sheet.prototype.draw=function(e,t,n){var i=this;i._workbook?i.recalc(i._workbook._context,function(){p(i,e,t,n)}):p(i,e,t,n)},m.prototype={forEach:function(e){Object.keys(this).forEach(function(t){e(this[t],t,this)},this)}},F={},R=function(e,t){var n,i,r,o,s=t+":"+e,a=F[s];if(!a){for(i=document.createElement("div"),i.style.position="fixed",i.style.left="-10000px",i.style.top="-10000px",i.style.fontFamily=e,i.style.fontSize=t+"px",i.style.whiteSpace="pre",r=32;r<128;++r)n=document.createElement("span"),n.appendChild(document.createTextNode(String.fromCharCode(r))),i.appendChild(n);for(document.body.appendChild(i),o={},r=32,n=i.firstChild;r<128&&n;++r,n=n.nextSibling)o[r]=n.offsetWidth;for(;n=i.firstChild;)i.removeChild(n);a=F[s]=function(e){var t=e.charCodeAt(0),r=o[t];return null==r&&(n=document.createElement("span"),n.appendChild(document.createTextNode(String.fromCharCode(t))),i.appendChild(n),r=o[t]=n.offsetWidth,i.removeChild(n)),r}}return a},b.draw={Borders:g,doLayout:t,applyIndent:h},b.drawTabularData=v)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.spreadsheet.min",["util/undoredostack.min","util/text-metrics.min","util/parse-xml.min","kendo.excel.min","kendo.progressbar.min","kendo.pdf.min","spreadsheet/commands.min","spreadsheet/formulabar.min","spreadsheet/formulainput.min","spreadsheet/eventlistener.min","spreadsheet/rangelist.min","spreadsheet/propertybag.min","spreadsheet/references.min","spreadsheet/navigator.min","spreadsheet/axismanager.min","spreadsheet/clipboard.min","spreadsheet/range.min","spreadsheet/sheet.min","spreadsheet/sheetsbar.min","spreadsheet/excel-reader.min","spreadsheet/workbook.min","spreadsheet/formulacontext.min","spreadsheet/controller.min","spreadsheet/view.min","spreadsheet/customeditors.min","spreadsheet/grid.min","spreadsheet/axis.min","spreadsheet/filter.min","spreadsheet/sorter.min","spreadsheet/runtime.min","spreadsheet/calc.min","spreadsheet/numformat.min","spreadsheet/runtime.functions.min","spreadsheet/runtime.functions.2.min","spreadsheet/toolbar.min","spreadsheet/dialogs.min","spreadsheet/sheetbinder.min","spreadsheet/filtermenu.min","spreadsheet/editor.min","spreadsheet/autofill.min","spreadsheet/nameeditor.min","spreadsheet/print.min"],e)}(function(){return function(e,t){var n,i,r,o,s,a,l,u,c,h;e.support.browser.msie&&e.support.browser.version<9||(n=e.jQuery,i=n.extend({F10:121,F11:122,B:66,I:73,U:85,N:78,H:72,A:65,PAGEDOWN:34,PAGEUP:33,DELETE:46,R:82},e.keys),r=e.ui.Widget,o=e.spreadsheet.Workbook,s=e.spreadsheet.Controller,a=e.spreadsheet.View,l=".kendoSpreadsheet",u={recalc:!0,selection:!0,activeCell:!0,layout:!0,sheetSelection:!0,resize:!0,editorChange:!1,editorClose:!1},c={wrapper:"k-widget k-spreadsheet"},h=e.ui.Widget.extend({init:function(e,t){r.fn.init.call(this,e,t),this.element.addClass(h.classNames.wrapper),this._view=new a(this.element,{messages:this.options.messages.view,toolbar:this.options.toolbar,sheetsbar:this.options.sheetsbar}),this._workbook=new o(this.options,this._view),this._controller=new s(this._view,this._workbook),this._autoRefresh=!0,this._bindWorkbookEvents(),this._view.workbook(this._workbook),this._view.enableClipboard(!1),this.refresh(),this._view.enableClipboard(!0),this._resizeHandler=function(){this.resize()}.bind(this),n(window).on("resize"+l,this._resizeHandler),this.element.on("keydown"+l,this._keyDown.bind(this))},_keyDown:function(e){var r,o=e.keyCode,s=n(".k-spreadsheet-quick-access-toolbar [title=Redo]");if(o===i.F11&&e.shiftKey)return this._view.sheetsbar._onAddSelect(),e.preventDefault(),t;if(e.altKey&&o===i.PAGEDOWN)this._view.sheetsbar.trigger("select",{name:this._view.sheetsbar._sheets[this._view.sheetsbar._selectedIndex+1].name(),isAddButton:!1});else if(e.altKey&&o===i.PAGEUP)this._view.sheetsbar.trigger("select",{name:this._view.sheetsbar._sheets[this._view.sheetsbar._selectedIndex-1].name(),isAddButton:!1});else{if(e.altKey&&o===i.DELETE)return r=function(e){var t=e.sender;t.isConfirmed()&&this._view.sheetsbar.trigger("remove",{name:this.activeSheet()._name(),confirmation:!0})}.bind(this),this._view.sheetsbar._openDialog("confirmation",{close:r}),e.preventDefault(),t;if(e.altKey&&o===i.R)return this._view.sheetsbar._createEditor(),e.preventDefault(),t;if(o===i.F10&&this._view.tabstrip||o===i.TAB&&!e.shiftKey&&n(document.activeElement).is(s))return this._view.tabstrip.toolbars[this._view.tabstrip.element.find("li.k-state-active").text().toLowerCase()].element.find(":not(.k-overflow-anchor):kendoFocusable:first").focus(),this._view.tabstrip.toolbars[this._view.tabstrip.element.find("li.k-state-active").text().toLowerCase()].element.find(".k-toolbar-first-visible").addClass("k-state-focused"),e.preventDefault(),t;if(e.ctrlKey&&o===i.B)n("[data-tool=bold]")[0].click();else if(e.ctrlKey&&o===i.I)n("[data-tool=italic]")[0].click();else if(e.ctrlKey&&o===i.U)n("[data-tool=underline]")[0].click();else{
if(e.altKey&&o===i.H)return this._view.tabstrip.select(0),e.preventDefault(),t;if(e.altKey&&o===i.N)return this._view.tabstrip.select(1),e.preventDefault(),t;if(e.altKey&&o===i.A)return this._view.tabstrip.select(2),e.preventDefault(),t}}},_resize:function(){this.refresh({layout:!0})},_workbookChanging:function(e){this.trigger("changing",e)&&e.preventDefault()},_workbookChange:function(t){if(this._autoRefresh&&this.refresh(t),t.recalc&&t.ref){var n=t.range||new e.spreadsheet.Range(t.ref,this.activeSheet());this.trigger("change",{range:n})}},_workbookCut:function(e){this.trigger("cut",e)},_workbookCopy:function(e){this.trigger("copy",e)},_workbookPaste:function(e){this.trigger("paste",e)},activeSheet:function(e){return this._workbook.activeSheet(e)},moveSheetToIndex:function(e,t){return this._workbook.moveSheetToIndex(e,t)},insertSheet:function(e){return this._workbook.insertSheet(e)},sheets:function(){return this._workbook.sheets()},removeSheet:function(e){return this._workbook.removeSheet(e)},sheetByName:function(e){return this._workbook.sheetByName(e)},sheetIndex:function(e){return this._workbook.sheetIndex(e)},sheetByIndex:function(e){return this._workbook.sheetByIndex(e)},renameSheet:function(e,t){return this._workbook.renameSheet(e,t)},refresh:function(e){return e||(e=u),e.editorClose||(this._view.sheet(this._workbook.activeSheet()),this._controller.sheet(this._workbook.activeSheet()),this._workbook.refresh(e)),e.editorChange||(this._view.refresh(e),this._controller.refresh(),this._view.render(e),this.trigger("render")),this},openDialog:function(e,t){return this._view.openDialog(e,t)},autoRefresh:function(e){return e!==t?(this._autoRefresh=e,e===!0&&this.refresh(),this):this._autoRefresh},toJSON:function(){return this._workbook.toJSON()},fromJSON:function(e){e.sheets?(this._workbook.destroy(),this._workbook=new o(n.extend({},this.options,e)),this._bindWorkbookEvents(),this._view.workbook(this._workbook),this._controller.workbook(this._workbook),this.activeSheet(this.activeSheet())):this.refresh()},saveJSON:function(){return this._workbook.saveJSON()},fromFile:function(e,t){return this._workbook.fromFile(e,t)},saveAsPDF:function(e){this._workbook.saveAsPDF(n.extend({},this.options.pdf,e,{workbook:this._workbook}))},saveAsExcel:function(e){this._workbook.saveAsExcel(e)},draw:function(e,t){this._workbook.draw(e,t)},_workbookExcelExport:function(e){this.trigger("excelExport",e)&&e.preventDefault()},_workbookExcelImport:function(e){this.trigger("excelImport",e)?e.preventDefault():this._initProgress(e.promise)},_initProgress:function(t){var i=n("<div class='k-loading-mask' style='width: 100%; height: 100%; top: 0;'><div class='k-loading-color'/></div>").appendTo(this.element),r=n("<div class='k-loading-progress'>").appendTo(i).kendoProgressBar({type:"chunk",chunkCount:10,min:0,max:1,value:0}).data("kendoProgressBar");t.progress(function(e){r.value(e.progress)}).always(function(){e.destroy(i),i.remove()})},_workbookPdfExport:function(e){this.trigger("pdfExport",e)&&e.preventDefault()},_workbookInsertSheet:function(e){this.trigger("insertSheet",e)&&e.preventDefault()},_workbookRemoveSheet:function(e){this.trigger("removeSheet",e)&&e.preventDefault()},_workbookSelectSheet:function(e){this.trigger("selectSheet",e)&&e.preventDefault()},_workbookRenameSheet:function(e){this.trigger("renameSheet",e)&&e.preventDefault()},_workbookInsertRow:function(e){this.trigger("insertRow",e)&&e.preventDefault()},_workbookInsertColumn:function(e){this.trigger("insertColumn",e)&&e.preventDefault()},_workbookDeleteRow:function(e){this.trigger("deleteRow",e)&&e.preventDefault()},_workbookDeleteColumn:function(e){this.trigger("deleteColumn",e)&&e.preventDefault()},_workbookHideRow:function(e){this.trigger("hideRow",e)&&e.preventDefault()},_workbookHideColumn:function(e){this.trigger("hideColumn",e)&&e.preventDefault()},_workbookUnhideRow:function(e){this.trigger("unhideRow",e)&&e.preventDefault()},_workbookUnhideColumn:function(e){this.trigger("unhideColumn",e)&&e.preventDefault()},_workbookSelect:function(e){this.trigger("select",e)},_workbookChangeFormat:function(e){this.trigger("changeFormat",e)},_workbookDataBinding:function(e){this.trigger("dataBinding",e)&&e.preventDefault()},_workbookDataBound:function(e){this.trigger("dataBound",e)},_bindWorkbookEvents:function(){this._workbook.bind("cut",this._workbookCut.bind(this)),this._workbook.bind("copy",this._workbookCopy.bind(this)),this._workbook.bind("paste",this._workbookPaste.bind(this)),this._workbook.bind("changing",this._workbookChanging.bind(this)),this._workbook.bind("change",this._workbookChange.bind(this)),this._workbook.bind("excelExport",this._workbookExcelExport.bind(this)),this._workbook.bind("excelImport",this._workbookExcelImport.bind(this)),this._workbook.bind("pdfExport",this._workbookPdfExport.bind(this)),this._workbook.bind("insertSheet",this._workbookInsertSheet.bind(this)),this._workbook.bind("removeSheet",this._workbookRemoveSheet.bind(this)),this._workbook.bind("selectSheet",this._workbookSelectSheet.bind(this)),this._workbook.bind("renameSheet",this._workbookRenameSheet.bind(this)),this._workbook.bind("insertRow",this._workbookInsertRow.bind(this)),this._workbook.bind("insertColumn",this._workbookInsertColumn.bind(this)),this._workbook.bind("deleteRow",this._workbookDeleteRow.bind(this)),this._workbook.bind("deleteColumn",this._workbookDeleteColumn.bind(this)),this._workbook.bind("hideRow",this._workbookHideRow.bind(this)),this._workbook.bind("hideColumn",this._workbookHideColumn.bind(this)),this._workbook.bind("unhideRow",this._workbookUnhideRow.bind(this)),this._workbook.bind("unhideColumn",this._workbookUnhideColumn.bind(this)),this._workbook.bind("select",this._workbookSelect.bind(this)),this._workbook.bind("changeFormat",this._workbookChangeFormat.bind(this)),this._workbook.bind("dataBinding",this._workbookDataBinding.bind(this)),this._workbook.bind("dataBound",this._workbookDataBound.bind(this))},destroy:function(){e.ui.Widget.fn.destroy.call(this),this._workbook.destroy(),this._controller.destroy(),this._view.destroy(),this._resizeHandler&&n(window).off("resize"+l,this._resizeHandler)},options:{name:"Spreadsheet",toolbar:!0,sheetsbar:!0,rows:200,columns:50,rowHeight:20,columnWidth:64,headerHeight:20,headerWidth:32,excel:{proxyURL:"",fileName:"Workbook.xlsx"},messages:{},pdf:{area:"workbook",fileName:"Workbook.pdf",proxyURL:"",paperSize:"a4",landscape:!0,margin:null,title:null,author:null,subject:null,keywords:null,creator:"Kendo UI PDF Generator v."+e.version,date:null},defaultCellStyle:{fontFamily:"Arial",fontSize:"12"},useCultureDecimals:!1},defineName:function(e,t,n){return this._workbook.defineName(e,t,n)},undefineName:function(e){return this._workbook.undefineName(e)},nameValue:function(e){return this._workbook.nameValue(e)},forEachName:function(e){return this._workbook.forEachName(e)},cellContextMenu:function(){return this._view.cellContextMenu},rowHeaderContextMenu:function(){return this._view.rowHeaderContextMenu},colHeaderContextMenu:function(){return this._view.colHeaderContextMenu},addImage:function(e){return this._workbook.addImage(e)},cleanupImages:function(){return this._workbook.cleanupImages()},events:["cut","copy","paste","pdfExport","excelExport","excelImport","changing","change","render","removeSheet","selectSheet","renameSheet","insertRow","insertColumn","deleteRow","insertSheet","deleteColumn","hideRow","hideColumn","unhideRow","unhideColumn","select","changeFormat","dataBinding","dataBound"]}),e.spreadsheet.ALL_REASONS=u,e.ui.plugin(h),n.extend(!0,h,{classNames:c}))}(window.kendo),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.spreadsheet.min.js.map
