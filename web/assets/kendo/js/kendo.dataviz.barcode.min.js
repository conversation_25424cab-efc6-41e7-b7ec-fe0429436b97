/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("util/text-metrics.min",["kendo.core.min"],t)}(function(){!function(t){function e(t){return(t+"").replace(s,h)}function a(t){var e,a=[];for(e in t)a.push(e+t[e]);return a.sort().join("")}function n(t){var e,a=2166136261;for(e=0;e<t.length;++e)a+=(a<<1)+(a<<4)+(a<<7)+(a<<8)+(a<<24),a^=t.charCodeAt(e);return a>>>0}function i(){return{width:0,height:0,baseline:0}}function r(t,e,a){return c.current.measure(t,e,a)}var o,s,h,d,u,c;window.kendo.util=window.kendo.util||{},o=kendo.Class.extend({init:function(t){this._size=t,this._length=0,this._map={}},put:function(t,e){var a=this._map,n={key:t,value:e};a[t]=n,this._head?(this._tail.newer=n,n.older=this._tail,this._tail=n):this._head=this._tail=n,this._length>=this._size?(a[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(t){var e=this._map[t];if(e)return e===this._head&&e!==this._tail&&(this._head=e.newer,this._head.older=null),e!==this._tail&&(e.older&&(e.older.newer=e.newer,e.newer.older=e.older),e.older=this._tail,e.newer=null,this._tail.newer=e,this._tail=e),e.value}}),s=/\r?\n|\r|\t/g,h=" ",d={baselineMarkerSize:1},"undefined"!=typeof document&&(u=document.createElement("div"),u.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),c=kendo.Class.extend({init:function(e){this._cache=new o(1e3),this.options=t.extend({},d,e)},measure:function(t,r,o){var s,h,d,c,l,p,f,v,g;if(void 0===o&&(o={}),!t)return i();if(s=a(r),h=n(t+s),d=this._cache.get(h))return d;c=i(),l=o.box||u,p=this._baselineMarker().cloneNode(!1);for(f in r)v=r[f],void 0!==v&&(l.style[f]=v);return g=o.normalizeText!==!1?e(t):t+"",l.textContent=g,l.appendChild(p),document.body.appendChild(l),g.length&&(c.width=l.offsetWidth-this.options.baselineMarkerSize,c.height=l.offsetHeight,c.baseline=p.offsetTop+this.options.baselineMarkerSize),c.width>0&&c.height>0&&this._cache.put(h,c),l.parentNode.removeChild(l),c},_baselineMarker:function(){var t=document.createElement("div");return t.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",t}}),c.current=new c,kendo.deepExtend(kendo.util,{LRUCache:o,TextMetrics:c,measureText:r,objectKey:a,hashKey:n,normalizeText:e})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,a){(a||e)()}),function(t,define){define("kendo.dataviz.barcode.min",["kendo.dataviz.core.min","kendo.drawing.min"],t)}(function(){return function(t,e){function a(t,e,a){return t.substring(e,e+a)}var n,i,r,o,s,h,d=window.kendo,u=d.ui.Widget,c=t.extend,l=d.deepExtend,p=t.inArray,f=t.isPlainObject,v=d.drawing,g=d.geometry,b=d.drawing.util,m=b.defined,w=d.dataviz,S=w.Box2D,C=w.TextBox,B=300,x=100,k=10,y=/^\d+$/,A=/^[a-z0-9]+$/i,M="Character '{0}' is not valid for symbology {1}",T=d.Class.extend({init:function(t){this.setOptions(t)},setOptions:function(t){var e=this;e.options=c({},e.options,t),e.quietZoneLength=e.options.addQuietZone?2*e.options.quietZoneLength:0},encode:function(t,e,a){var n=this;return m(t)&&(t+=""),n.initValue(t,e,a),n.options.addQuietZone&&n.addQuietZone(),n.addData(),n.options.addQuietZone&&n.addQuietZone(),{baseUnit:n.baseUnit,pattern:n.pattern}},options:{quietZoneLength:k,addQuietZone:!0,addCheckSum:!0},initValue:function(){},addQuietZone:function(){this.pattern.push(this.options.quietZoneLength||k)},addData:function(){},invalidCharacterError:function(t){throw Error(d.format(M,t,this.name))}}),_={},W=T.extend({minBaseUnitLength:.7,addData:function(){var t,e=this,a=e.value;for(e.addStart(),t=0;t<a.length;t++)e.addCharacter(a.charAt(t));e.options.addCheckSum&&e.pushCheckSum(),e.addStop(),e.prepareValues()},addCharacter:function(t){var e=this,a=e.characterMap[t];a||e.invalidCharacterError(t),e.addBase(a)},addBase:function(){}}),P={addCharacter:function(t){var e=this;e.characterMap[t]?e.addBase(e.characterMap[t]):t.charCodeAt(0)>127?e.invalidCharacterError(t):e.addExtended(t.charCodeAt(0))},addExtended:function(t){var a,n,i,r=this;for(n=0;n<r.extendedMappings.length;n++)if(a=r.extendedMappings[n].call(r,t)){for(i=0;i<a.length;i++)r.addBase(a[i]);return r.dataLength+=a.length-1,e}},extendedMappings:[function(t){if(97<=t&&t<=122){var e=this;return[e.characterMap[e.shiftCharacters[0]],e.characterMap[String.fromCharCode(t-32)]]}},function(t){if(33<=t&&t<=58){var e=this;return[e.characterMap[e.shiftCharacters[1]],e.characterMap[String.fromCharCode(t+32)]]}},function(t){if(1<=t&&t<=26){var e=this;return[e.characterMap[e.shiftCharacters[2]],e.characterMap[String.fromCharCode(t+64)]]}},function(t){var e,a,n,i=this;if(i.specialAsciiCodes[t])for(e=[],n=0;n<i.specialAsciiCodes[t].length;n++)e.push(i.characterMap[i.shiftCharacters[3]]),e.push(i.characterMap[i.specialAsciiCodes[t][n]]);else a=6*Math.floor(t/32)+(t-27)%32+64,e=[i.characterMap[i.shiftCharacters[3]],i.characterMap[String.fromCharCode(a)]];return e}],specialAsciiCodes:{0:["U"],64:["V"],96:["W"],127:["T","X","Y","Z"]},shiftValuesAsciiCodes:{39:36,40:47,41:43,42:37},characterMap:{"+":!1,"/":!1,$:!1,"%":!1},shiftCharacters:["SHIFT0","SHIFT1","SHIFT2","SHIFT3"]};_.code39=W.extend({name:"Code 39",checkSumMod:43,minRatio:2.5,maxRatio:3,gapWidth:1,splitCharacter:"|",initValue:function(t,e,a){var n=this;n.width=e,n.height=a,n.value=t,n.dataLength=t.length,n.pattern=[],n.patternString=""},prepareValues:function(){var t,e,a=this,n=a.minBaseUnitLength,i=a.maxRatio,r=a.minRatio,o=Math.max(.15*a.width,24);if(a.height<o)throw Error("Insufficient Height. The minimum height for value: "+a.value+" is: "+o);for(;(t=a.getBaseUnit(i))<n&&i>r;)i=parseFloat((i-.1).toFixed(1));if(t<n)throw e=Math.ceil(a.getBaseWidth(r)*n),Error("Insufficient width. The minimum width for value: "+a.value+" is: "+e);a.ratio=i,a.baseUnit=t,a.patternString=a.patternString.substring(0,a.patternString.length-1),a.pattern=a.pattern.concat(a.patternString.replace(/ratio/g,i).split(a.splitCharacter))},getBaseUnit:function(t){return this.width/this.getBaseWidth(t)},getBaseWidth:function(t){var e=this,a=3*(t+2);return e.quietZoneLength+a*(e.dataLength+2)+e.gapWidth*(e.dataLength+1)},addStart:function(){var t=this;t.addPattern(t.characterMap.START.pattern),t.addCharacterGap()},addBase:function(t){this.addPattern(t.pattern),this.addCharacterGap()},addStop:function(){this.addPattern(this.characterMap.START.pattern)},addPattern:function(t){for(var e=0;e<t.length;e++)this.patternString+=this.patternMappings[t.charAt(e)]},addCharacterGap:function(){var t=this;t.patternString+=t.gapWidth+t.splitCharacter},patternMappings:{b:"1|",w:"1|",B:"ratio|",W:"ratio|"},characterMap:{0:{pattern:"bwbWBwBwb",value:0},1:{pattern:"BwbWbwbwB",value:1},2:{pattern:"bwBWbwbwB",value:2},3:{pattern:"BwBWbwbwb",value:3},4:{pattern:"bwbWBwbwB",value:4},5:{pattern:"BwbWBwbwb",value:5},6:{pattern:"bwBWBwbwb",value:6},7:{pattern:"bwbWbwBwB",value:7},8:{pattern:"BwbWbwBwb",value:8},9:{pattern:"bwBWbwBwb",value:9},A:{pattern:"BwbwbWbwB",value:10},B:{pattern:"bwBwbWbwB",value:11},C:{pattern:"BwBwbWbwb",value:12},D:{pattern:"bwbwBWbwB",value:13},E:{pattern:"BwbwBWbwb",value:14},F:{pattern:"bwBwBWbwb",value:15},G:{pattern:"bwbwbWBwB",value:16},H:{pattern:"BwbwbWBwb",value:17},I:{pattern:"bwBwbWBwb",value:18},J:{pattern:"bwbwBWBwb",value:19},K:{pattern:"BwbwbwbWB",value:20},L:{pattern:"bwBwbwbWB",value:21},M:{pattern:"BwBwbwbWb",value:22},N:{pattern:"bwbwBwbWB",value:23},O:{pattern:"BwbwBwbWb",value:24},P:{pattern:"bwBwBwbWb",value:25},Q:{pattern:"bwbwbwBWB",value:26},R:{pattern:"BwbwbwBWb",value:27},S:{pattern:"bwBwbwBWb",value:28},T:{pattern:"bwbwBwBWb",value:29},U:{pattern:"BWbwbwbwB",value:30},V:{pattern:"bWBwbwbwB",value:31},W:{pattern:"BWBwbwbwb",value:32},X:{pattern:"bWbwBwbwB",value:33},Y:{pattern:"BWbwBwbwb",value:34},Z:{pattern:"bWBwBwbwb",value:35},"-":{pattern:"bWbwbwBwB",value:36},".":{pattern:"BWbwbwBwb",value:37}," ":{pattern:"bWBwbwBwb",value:38},$:{pattern:"bWbWbWbwb",value:39},"/":{pattern:"bWbWbwbWb",value:40},"+":{pattern:"bWbwbWbWb",value:41},"%":{pattern:"bwbWbWbWb",value:42},START:{pattern:"bWbwBwBwb"}},options:{addCheckSum:!1}}),_.code39extended=_.code39.extend(l({},P,{name:"Code 39 extended",characterMap:{SHIFT0:{pattern:"bWbwbWbWb",value:41},SHIFT1:{pattern:"bWbWbwbWb",value:40},SHIFT2:{pattern:"bWbWbWbwb",value:39},SHIFT3:{pattern:"bwbWbWbWb",value:42}}})),_.code93=W.extend({name:"Code 93",cCheckSumTotal:20,kCheckSumTotal:15,checkSumMod:47,initValue:function(t,e,a){var n=this;n.value=t,n.width=e,n.height=a,n.pattern=[],n.values=[],n.dataLength=t.length},prepareValues:function(){var t=this,e=Math.max(.15*t.width,24);if(t.height<e)throw Error("Insufficient Height");if(t.setBaseUnit(),t.baseUnit<t.minBaseUnitLength)throw Error("Insufficient Width")},setBaseUnit:function(){var t=this,e=2;t.baseUnit=t.width/(9*(t.dataLength+2+e)+t.quietZoneLength+1)},addStart:function(){var t=this.characterMap.START.pattern;this.addPattern(t)},addStop:function(){var t=this;t.addStart(),t.pattern.push(t.characterMap.TERMINATION_BAR)},addBase:function(t){this.addPattern(t.pattern),this.values.push(t.value)},pushCheckSum:function(){var t,e,a=this,n=a._getCheckValues();for(a.checksum=n.join(""),e=0;e<n.length;e++)t=a.characterMap[a._findCharacterByValue(n[e])],a.addPattern(t.pattern)},_getCheckValues:function(){var t,e,a,n=this,i=n.values,r=i.length,o=0;for(a=r-1;a>=0;a--)o+=n.weightedValue(i[a],r-a,n.cCheckSumTotal);for(t=o%n.checkSumMod,o=n.weightedValue(t,1,n.kCheckSumTotal),a=r-1;a>=0;a--)o+=n.weightedValue(i[a],r-a+1,n.kCheckSumTotal);return e=o%n.checkSumMod,[t,e]},_findCharacterByValue:function(t){for(var e in this.characterMap)if(this.characterMap[e].value===t)return e},weightedValue:function(t,e,a){return(e%a||a)*t},addPattern:function(t){var e,a;for(a=0;a<t.length;a++)e=parseInt(t.charAt(a),10),this.pattern.push(e)},characterMap:{0:{pattern:"131112",value:0},1:{pattern:"111213",value:1},2:{pattern:"111312",value:2},3:{pattern:"111411",value:3},4:{pattern:"121113",value:4},5:{pattern:"121212",value:5},6:{pattern:"121311",value:6},7:{pattern:"111114",value:7},8:{pattern:"131211",value:8},9:{pattern:"141111",value:9},A:{pattern:"211113",value:10},B:{pattern:"211212",value:11},C:{pattern:"211311",value:12},D:{pattern:"221112",value:13},E:{pattern:"221211",value:14},F:{pattern:"231111",value:15},G:{pattern:"112113",value:16},H:{pattern:"112212",value:17},I:{pattern:"112311",value:18},J:{pattern:"122112",value:19},K:{pattern:"132111",value:20},L:{pattern:"111123",value:21},M:{pattern:"111222",value:22},N:{pattern:"111321",value:23},O:{pattern:"121122",value:24},P:{pattern:"131121",value:25},Q:{pattern:"212112",value:26},R:{pattern:"212211",value:27},S:{pattern:"211122",value:28},T:{pattern:"211221",value:29},U:{pattern:"221121",value:30},V:{pattern:"222111",value:31},W:{pattern:"112122",value:32},X:{pattern:"112221",value:33},Y:{pattern:"122121",value:34},Z:{pattern:"123111",value:35},"-":{pattern:"121131",value:36},".":{pattern:"311112",value:37}," ":{pattern:"311211",value:38},$:{pattern:"321111",value:39},"/":{pattern:"112131",value:40},"+":{pattern:"113121",value:41},"%":{pattern:"211131",value:42},SHIFT0:{pattern:"122211",value:46},SHIFT1:{pattern:"311121",value:45},SHIFT2:{pattern:"121221",value:43},SHIFT3:{pattern:"312111",value:44},START:{pattern:"111141"},TERMINATION_BAR:"1"}}),_.code93extended=_.code93.extend(l({},P,{name:"Code 93 extended",pushCheckSum:function(){var t,e,a=this,n=a._getCheckValues();for(a.checksum=n.join(""),e=0;e<n.length;e++)t=n[e],a.shiftValuesAsciiCodes[t]?a.addExtended(a.shiftValuesAsciiCodes[t]):a.addPattern(a.characterMap[a._findCharacterByValue(t)].pattern)}})),n=d.Class.extend({init:function(t){this.encoding=t},addStart:function(){},is:function(){},move:function(){},pushState:function(){}}),i=n.extend({FNC4:"FNC4",init:function(t,e){var a=this;a.encoding=t,a.states=e,a._initMoves(e)},addStart:function(){this.encoding.addPattern(this.START)},is:function(t,e){var a=t.charCodeAt(e);return this.isCode(a)},move:function(t){for(var e=this,a=0;!e._moves[a].call(e,t)&&a<e._moves.length;)a++},pushState:function(t){var e,a,n=this,i=n.states,r=t.value,o=r.length;for(p("C",i)>=0&&(a=r.substr(t.index).match(/\d{4,}/g),a&&(o=r.indexOf(a[0],t.index)));(e=t.value.charCodeAt(t.index))>=0&&n.isCode(e)&&t.index<o;)n.encoding.addPattern(n.getValue(e)),t.index++},_initMoves:function(t){var e=this;e._moves=[],p(e.FNC4,t)>=0&&e._moves.push(e._moveFNC),p(e.shiftKey,t)>=0&&e._moves.push(e._shiftState),e._moves.push(e._moveState)},_moveFNC:function(t){if(t.fnc)return t.fnc=!1,t.previousState==this.key},_shiftState:function(t){var e=this;if(t.previousState==e.shiftKey&&(t.index+1>=t.value.length||e.encoding[e.shiftKey].is(t.value,t.index+1)))return e.encoding.addPattern(e.SHIFT),t.shifted=!0,!0},_moveState:function(){return this.encoding.addPattern(this.MOVE),!0},SHIFT:98}),r={},r.A=i.extend({key:"A",shiftKey:"B",isCode:function(t){return 0<=t&&t<96},getValue:function(t){return t<32?t+64:t-32},MOVE:101,START:103}),r.B=i.extend({key:"B",shiftKey:"A",isCode:function(t){return 32<=t&&t<128},getValue:function(t){return t-32},MOVE:100,START:104}),r.C=n.extend({key:"C",addStart:function(){this.encoding.addPattern(this.START)},is:function(t,e){var n=a(t,e,4);return(e+4<=t.length||2==t.length)&&y.test(n)},move:function(){this.encoding.addPattern(this.MOVE)},pushState:function(t){for(var e;(e=a(t.value,t.index,2))&&y.test(e)&&2==e.length;)this.encoding.addPattern(parseInt(e,10)),t.index+=2},getValue:function(t){return t},MOVE:99,START:105}),r.FNC4=n.extend({key:"FNC4",dependentStates:["A","B"],init:function(t,e){this.encoding=t,this._initSubStates(e)},addStart:function(t){var e=t.value.charCodeAt(0)-128,a=this._getSubState(e);this.encoding[a].addStart()},is:function(t,e){var a=t.charCodeAt(e);return this.isCode(a)},isCode:function(t){return 128<=t&&t<256},pushState:function(t){var e,a=this,n=a._initSubState(t),i=a.encoding,r=n.value.length;if(t.index+=r,r<3)for(;n.index<r;n.index++)e=n.value.charCodeAt(n.index),n.state=a._getSubState(e),n.previousState!=n.state&&(n.previousState=n.state,i[n.state].move(n)),i.addPattern(i[n.state].MOVE),i.addPattern(i[n.state].getValue(e));else n.state!=n.previousState&&i[n.state].move(n),a._pushStart(n),i.pushData(n,a.subStates),t.index<t.value.length&&a._pushStart(n);t.fnc=!0,t.state=n.state},_pushStart:function(t){var e=this;e.encoding.addPattern(e.encoding[t.state].MOVE),e.encoding.addPattern(e.encoding[t.state].MOVE)},_initSubState:function(t){var e=this,a={value:e._getAll(t.value,t.index),index:0};return a.state=e._getSubState(a.value.charCodeAt(0)),a.previousState=t.previousState==e.key?a.state:t.previousState,a},_initSubStates:function(t){var e,a=this;for(a.subStates=[],e=0;e<t.length;e++)p(t[e],a.dependentStates)>=0&&a.subStates.push(t[e])},_getSubState:function(t){var e,a=this;for(e=0;e<a.subStates.length;e++)if(a.encoding[a.subStates[e]].isCode(t))return a.subStates[e]},_getAll:function(t,e){for(var a,n="";(a=t.charCodeAt(e++))&&this.isCode(a);)n+=String.fromCharCode(a-128);return n}}),r.FNC1=n.extend({key:"FNC1",startState:"C",dependentStates:["C","B"],startAI:"(",endAI:")",init:function(t,e){this.encoding=t,this.states=e},addStart:function(){this.encoding[this.startState].addStart()},is:function(){return p(this.key,this.states)>=0},pushState:function(t){var e,a,n,i=this,r=i.encoding,o=t.value.replace(/\s/g,""),s=RegExp("["+i.startAI+i.endAI+"]","g"),h=t.index,d={state:i.startState};for(r.addPattern(i.START);;){if(d.index=0,n=o.charAt(h)===i.startAI?2:0,e=n>0?i.getBySeparator(o,h):i.getByLength(o,h),e.ai.length)a=h+n+e.id.length+e.ai.length;else if(a=o.indexOf(i.startAI,h+1),a<0){if(h+e.ai.max+e.id.length+n<o.length)throw Error("Separators are required after variable length identifiers");a=o.length}if(d.value=o.substring(h,a).replace(s,""),i.validate(e,d.value),r.pushData(d,i.dependentStates),a>=o.length)break;h=a,d.state!=i.startState&&(r[i.startState].move(d),d.state=i.startState),e.ai.length||r.addPattern(i.START)}t.index=t.value.length},validate:function(t,e){var a=e.substr(t.id.length),n=t.ai;if(!n.type&&!y.test(a))throw Error("Application identifier "+t.id+" is numeric only but contains non numeric character(s).");if("alphanumeric"==n.type&&!A.test(a))throw Error("Application identifier "+t.id+" is alphanumeric only but contains non alphanumeric character(s).");if(n.length&&n.length!==a.length)throw Error("Application identifier "+t.id+" must be "+n.length+" characters long.");if(n.min&&n.min>a.length)throw Error("Application identifier "+t.id+" must be at least "+n.min+" characters long.");if(n.max&&n.max<a.length)throw Error("Application identifier "+t.id+" must be at most "+n.max+" characters long.")},getByLength:function(t,e){var n,i,r,o=this;for(r=2;r<=4;r++)if(n=a(t,e,r),i=o.getAI(n)||o.getAI(n.substring(0,n.length-1)))return{id:n,ai:i};o.unsupportedAIError(n)},unsupportedAIError:function(t){throw Error(d.format("'{0}' is not a supported Application Identifier"),t)},getBySeparator:function(t,e){var a=this,n=t.indexOf(a.startAI,e),i=t.indexOf(a.endAI,n),r=t.substring(n+1,i),o=a.getAI(r)||a.getAI(r.substr(r.length-1));return o||a.unsupportedAIError(r),{ai:o,id:r}},getAI:function(t){var e,a,n,i=this.applicationIdentifiers,r=i.multiKey;if(i[t])return i[t];for(e=0;e<r.length;e++){if(r[e].ids&&p(t,r[e].ids)>=0)return r[e].type;if(r[e].ranges)for(a=r[e].ranges,n=0;n<a.length;n++)if(a[n][0]<=t&&t<=a[n][1])return r[e].type}},applicationIdentifiers:{22:{max:29,type:"alphanumeric"},402:{length:17},7004:{max:4,type:"alphanumeric"},242:{max:6,type:"alphanumeric"},8020:{max:25,type:"alphanumeric"},703:{min:3,max:30,type:"alphanumeric"},8008:{min:8,max:12,type:"alphanumeric"},253:{min:13,max:17,type:"alphanumeric"},8003:{min:14,max:30,type:"alphanumeric"},multiKey:[{ids:["15","17","8005","8100"],ranges:[[11,13],[310,316],[320,336],[340,369]],type:{length:6}},{ids:["240","241","250","251","400","401","403","7002","8004","8007","8110"],ranges:[[-9]],type:{max:30,type:"alphanumeric"}},{ids:["7001"],ranges:[[410,414]],type:{length:13}},{ids:["10","21","254","420","8002"],type:{max:20,type:"alphanumeric"}},{ids:["00","8006","8017","8018"],type:{length:18}},{ids:["01","02","8001"],type:{length:14}},{ids:["422"],ranges:[[424,426]],type:{length:3}},{ids:["20","8102"],type:{length:2}},{ids:["30","37"],type:{max:8,type:"alphanumeric"}},{ids:["390","392"],type:{max:15,type:"alphanumeric"}},{ids:["421","423"],type:{min:3,max:15,type:"alphanumeric"}},{ids:["391","393"],type:{min:3,max:18,type:"alphanumeric"}},{ids:["7003","8101"],type:{length:10}}]},START:102}),o=T.extend({init:function(t){T.fn.init.call(this,t),this._initStates()},_initStates:function(){var t,e=this;for(t=0;t<e.states.length;t++)e[e.states[t]]=new r[e.states[t]](e,e.states)},initValue:function(t,e,a){var n=this;n.pattern=[],n.value=t,n.width=e,n.height=a,n.checkSum=0,n.totalUnits=0,n.index=0,n.position=1},addData:function(){var t=this,e={value:t.value,index:0,state:""};0!==t.value.length&&(e.state=e.previousState=t.getNextState(e,t.states),t.addStart(e),t.pushData(e,t.states),t.addCheckSum(),t.addStop(),t.setBaseUnit())},pushData:function(t,e){for(var a,n=this;;){if(n[t.state].pushState(t),t.index>=t.value.length)break;t.shifted?(a=t.state,t.state=t.previousState,t.previousState=a,t.shifted=!1):(t.previousState=t.state,t.state=n.getNextState(t,e),n[t.state].move(t))}},addStart:function(t){this[t.state].addStart(t),this.position=1},addCheckSum:function(){var t=this;t.checksum=t.checkSum%103,t.addPattern(t.checksum)},addStop:function(){this.addPattern(this.STOP)},setBaseUnit:function(){var t=this;t.baseUnit=t.width/(t.totalUnits+t.quietZoneLength)},addPattern:function(t){var e,a,n=this,i=""+n.characterMap[t];for(a=0;a<i.length;a++)e=parseInt(i.charAt(a),10),n.pattern.push(e),n.totalUnits+=e;n.checkSum+=t*n.position++},getNextState:function(t,e){for(var a=0;a<e.length;a++)if(this[e[a]].is(t.value,t.index))return e[a];this.invalidCharacterError(t.value.charAt(t.index))},characterMap:[212222,222122,222221,121223,121322,131222,122213,122312,132212,221213,221312,231212,112232,122132,122231,113222,123122,123221,223211,221132,221231,213212,223112,312131,311222,321122,321221,312212,322112,322211,212123,212321,232121,111323,131123,131321,112313,132113,132311,211313,231113,231311,112133,112331,132131,113123,113321,133121,313121,211331,231131,213113,213311,213131,311123,311321,331121,312113,312311,332111,314111,221411,431111,111224,111422,121124,121421,141122,141221,112214,112412,122114,122411,142112,142211,241211,221114,413111,241112,134111,111242,121142,121241,114212,124112,124211,411212,421112,421211,212141,214121,412121,111143,111341,131141,114113,114311,411113,411311,113141,114131,311141,411131,211412,211214,211232,2331112],STOP:106}),_.code128a=o.extend({name:"Code 128 A",states:["A"]}),_.code128b=o.extend({name:"Code 128 B",states:["B"]}),_.code128c=o.extend({name:"Code 128 C",states:["C"]}),_.code128=o.extend({name:"Code 128",states:["C","B","A","FNC4"]}),_["gs1-128"]=o.extend({name:"Code GS1-128",states:["FNC1","C","B"]}),s=T.extend({initValue:function(t,e){var a=this;a.pattern=[],a.value=t,a.checkSumLength=0,a.width=e},setBaseUnit:function(){var t=this,e=7;t.baseUnit=t.width/(12*(t.value.length+t.checkSumLength)+t.quietZoneLength+e)},addData:function(){var t,e=this,a=e.value;for(e.addPattern(e.START),t=0;t<a.length;t++)e.addCharacter(a.charAt(t));e.options.addCheckSum&&e.addCheckSum(),e.addPattern(e.STOP),e.setBaseUnit()},addCharacter:function(t){var e=this,a=e.characterMap[t];a||e.invalidCharacterError(t),e.addPattern(a)},addPattern:function(t){for(var e=0;e<t.length;e++)this.pattern.push(parseInt(t.charAt(e),10))},addCheckSum:function(){var t,e=this,a=e.checkSums[e.checkSumType],n=a.call(e.checkSums,e.value);for(e.checksum=n.join(""),t=0;t<n.length;t++)e.checkSumLength++,e.addPattern(e.characterMap[n[t]])},checkSums:{Modulo10:function(t){var e,a,n,i=[0,""],r=t.length%2;for(e=0;e<t.length;e++)i[(e+r)%2]+=parseInt(t.charAt(e),10);for(n=i[0],a=""+2*i[1],e=0;e<a.length;e++)n+=parseInt(a.charAt(e),10);return[(10-n%10)%10]},Modulo11:function(t){var e,a,n,i=0,r=11,o=t.length;for(n=0;n<o;n++)e=((o-n)%6||6)+1,i+=e*t.charAt(n);return a=(r-i%r)%r,10!=a?[a]:[1,0]},Modulo11Modulo10:function(t){var e,a=this.Modulo11(t);return e=t+a[0],a.concat(this.Modulo10(e))},Modulo10Modulo10:function(t){var e,a=this.Modulo10(t);return e=t+a[0],a.concat(this.Modulo10(e))}},characterMap:["12121212","12121221","12122112","12122121","12211212","12211221","12212112","12212121","21121212","21121221"],START:"21",STOP:"121",checkSumType:""}),_.msimod10=s.extend({name:"MSI Modulo10",checkSumType:"Modulo10"}),_.msimod11=s.extend({name:"MSI Modulo11",checkSumType:"Modulo11"}),_.msimod1110=s.extend({name:"MSI Modulo11 Modulo10",checkSumType:"Modulo11Modulo10"}),_.msimod1010=s.extend({name:"MSI Modulo10 Modulo10",checkSumType:"Modulo10Modulo10"}),_.code11=T.extend({name:"Code 11",cCheckSumTotal:10,kCheckSumTotal:9,kCheckSumMinLength:10,checkSumMod:11,DASH_VALUE:10,DASH:"-",START:"112211",STOP:"11221",initValue:function(t,e){var a=this;a.pattern=[],a.value=t,a.width=e,a.totalUnits=0},addData:function(){var t,e=this,a=e.value;for(e.addPattern(e.START),t=0;t<a.length;t++)e.addCharacter(a.charAt(t));e.options.addCheckSum&&e.addCheckSum(),e.addPattern(e.STOP),e.setBaseUnit()},setBaseUnit:function(){var t=this;t.baseUnit=t.width/(t.totalUnits+t.quietZoneLength)},addCheckSum:function(){var t,e=this,a=e.value,n=a.length,i=e.getWeightedSum(a,n,e.cCheckSumTotal)%e.checkSumMod;e.checksum=i+"",e.addPattern(e.characterMap[i]),n++,n>=e.kCheckSumMinLength&&(t=(i+e.getWeightedSum(a,n,e.kCheckSumTotal))%e.checkSumMod,e.checksum+=t,e.addPattern(e.characterMap[t]))},getWeightedSum:function(t,e,a){var n,i=0;for(n=0;n<t.length;n++)i+=this.weightedValue(this.getValue(t.charAt(n)),e,n,a);return i},weightedValue:function(t,e,a,n){var i=(e-a)%n||n;return i*t},getValue:function(t){var e=this;return isNaN(t)?(t!==e.DASH&&e.invalidCharacterError(t),e.DASH_VALUE):parseInt(t,10)},addCharacter:function(t){var e=this,a=e.getValue(t),n=e.characterMap[a];e.addPattern(n)},addPattern:function(t){var e,a;for(a=0;a<t.length;a++)e=parseInt(t.charAt(a),10),this.pattern.push(e),this.totalUnits+=e},characterMap:["111121","211121","121121","221111","112121","212111","122111","111221","211211","211111","112111"],options:{addCheckSum:!0}}),_.postnet=T.extend({name:"Postnet",START:"2",VALID_CODE_LENGTHS:[5,9,11],DIGIT_SEPARATOR:"-",initValue:function(t,e,a){var n=this;n.height=a,n.width=e,n.baseHeight=a/2,n.value=t.replace(RegExp(n.DIGIT_SEPARATOR,"g"),""),n.pattern=[],n.validate(n.value),n.checkSum=0,n.setBaseUnit()},addData:function(){var t,e=this,a=e.value;for(e.addPattern(e.START),t=0;t<a.length;t++)e.addCharacter(a.charAt(t));e.options.addCheckSum&&e.addCheckSum(),e.addPattern(e.START),e.pattern.pop()},addCharacter:function(t){var e=this,a=e.characterMap[t];e.checkSum+=parseInt(t,10),e.addPattern(a)},addCheckSum:function(){var t=this;t.checksum=(10-t.checkSum%10)%10,t.addCharacter(t.checksum)},setBaseUnit:function(){var t=this,e=3;t.baseUnit=t.width/(10*(t.value.length+1)+e+t.quietZoneLength)},validate:function(t){var e=this;if(y.test(t)||e.invalidCharacterError(t.match(/[^0-9]/)[0]),p(t.length,e.VALID_CODE_LENGTHS)<0)throw Error("Invalid value length. Valid lengths for the Postnet symbology are "+e.VALID_CODE_LENGTHS.join(","))},addPattern:function(t){var e,a,n=this;for(a=0;a<t.length;a++)e=n.height-n.baseHeight*t.charAt(a),n.pattern.push({width:1,y1:e,y2:n.height}),n.pattern.push(1)},characterMap:["22111","11122","11212","11221","12112","12121","12211","21112","21121","21211"]}),_.ean13=T.extend({initValue:function(t,e,a){if(t+="",12!=t.length||/\D/.test(t))throw Error('The value of the "EAN13" encoding should be 12 symbols');var n=this;n.pattern=[],n.options.height=a,n.baseUnit=e/(95+n.quietZoneLength),n.value=t,n.checksum=n.calculateChecksum(),n.leftKey=t[0],n.leftPart=t.substr(1,6),n.rightPart=t.substr(7)+n.checksum},addData:function(){var t=this;t.addPieces(t.characterMap.start),t.addSide(t.leftPart,t.leftKey),t.addPieces(t.characterMap.middle),t.addSide(t.rightPart),t.addPieces(t.characterMap.start)},addSide:function(t,e){var a,n=this;for(a=0;a<t.length;a++)e&&parseInt(n.keyTable[e].charAt(a),10)?n.addPieces(Array.prototype.slice.call(n.characterMap.digits[t.charAt(a)]).reverse(),!0):n.addPieces(n.characterMap.digits[t.charAt(a)],!0)},addPieces:function(t,e){var a,n=this;for(a=0;a<t.length;a++)n.pattern.push(e?{y1:0,y2:.95*n.options.height,width:t[a]}:t[a])},calculateChecksum:function(){var t,e,a=0,n=0,i=this.value.split("").reverse().join("");for(t=0;t<i.length;t++)t%2?n+=parseInt(i.charAt(t),10):a+=parseInt(i.charAt(t),10);return e=(10-(3*a+n)%10)%10},keyTable:["000000","001011","001101","001110","010011","011001","011100","010101","010110","011010"],characterMap:{digits:[[3,2,1,1],[2,2,2,1],[2,1,2,2],[1,4,1,1],[1,1,3,2],[1,2,3,1],[1,1,1,4],[1,3,1,2],[1,2,1,3],[3,1,1,2]],start:[1,1,1],middle:[1,1,1,1,1]}}),_.ean8=_.ean13.extend({initValue:function(t,e,a){var n=this;if(7!=t.length||/\D/.test(t))throw Error("Invalid value provided");n.value=t,n.options.height=a,n.checksum=n.calculateChecksum(n.value),n.leftPart=n.value.substr(0,4),n.rightPart=n.value.substr(4)+n.checksum,n.pattern=[],n.baseUnit=e/(67+n.quietZoneLength)}}),h=u.extend({init:function(e,a){var n=this;u.fn.init.call(n,e,a),n.element=t(e),n.wrapper=n.element,n.element.addClass("k-barcode").css("display","block"),n.surfaceWrap=t("<div />").css("position","relative").appendTo(this.element),n.surface=v.Surface.create(n.surfaceWrap,{type:n.options.renderAs}),n._setOptions(a),a&&m(a.value)&&n.redraw()},setOptions:function(t){this._setOptions(t),this.redraw()},redraw:function(){var t=this._getSize();this.surface.clear(),this.surface.setSize({width:t.width,height:t.height}),this.createVisual(),this.surface.draw(this.visual)},getSize:function(){return d.dimensions(this.element)},_resize:function(){this.redraw()},createVisual:function(){this.visual=this._render()},_render:function(){var t,e,a,n=this,i=n.options,r=i.value,o=i.text,s=w.getSpacing(o.margin),h=n._getSize(),d=i.border||{},u=n.encoding,c=new S(0,0,h.width,h.height).unpad(d.width).unpad(i.padding),l=c.height(),p=new v.Group;return n.contentBox=c,p.append(n._getBackground(h)),o.visible&&(a=v.util.measureText(r,{font:o.font}).height,l-=a+s.top+s.bottom),t=u.encode(r,c.width(),l),o.visible&&(e=r,i.checksum&&m(u.checksum)&&(e+=" "+u.checksum),p.append(n._getText(e))),n.barHeight=l,this._bandsGroup=this._getBands(t.pattern,t.baseUnit),p.append(this._bandsGroup),p},exportVisual:function(){return this._render()},_getSize:function(){var t=this,e=t.element,a=new g.Size(B,x);return e.width()>0&&(a.width=e.width()),e.height()>0&&(a.height=e.height()),t.options.width&&(a.width=t.options.width),t.options.height&&(a.height=t.options.height),a},value:function(t){var a=this;return m(t)?(a.options.value=t+"",a.redraw(),e):a.options.value},_getBands:function(t,e){var a,n,i,r,o,s=this,h=s.contentBox,d=h.x1,u=new v.Group;for(i=0;i<t.length;i++)n=f(t[i])?t[i]:{width:t[i],y1:0,y2:s.barHeight},a=n.width*e,i%2&&(r=g.Rect.fromPoints(new g.Point(d,n.y1+h.y1),new g.Point(d+a,n.y2+h.y1)),o=v.Path.fromRect(r,{fill:{color:s.options.color},stroke:null}),u.append(o)),d+=a;return u},_getBackground:function(t){var e=this,a=e.options,n=a.border||{},i=new S(0,0,t.width,t.height).unpad(n.width/2),r=v.Path.fromRect(i.toRect(),{fill:{color:a.background},stroke:{color:n.width?n.color:"",width:n.width,dashType:n.dashType}});return r},_getText:function(t){var e=this,a=e.options.text,n=e._textbox=new C(t,{font:a.font,color:a.color,align:"center",vAlign:"bottom",margin:a.margin});return n.reflow(e.contentBox),n.renderVisual(),n.visual},_setOptions:function(t){var e=this;if(e.type=(t.type||e.options.type).toLowerCase(),"upca"==e.type&&(e.type="ean13",t.value="0"+t.value),"upce"==e.type&&(e.type="ean8",t.value="0"+t.value),!_[e.type])throw Error("Encoding "+e.type+"is not supported.");e.encoding=new _[e.type],e.options=c(!0,e.options,t)},options:{name:"Barcode",renderAs:"svg",value:"",type:"code39",checksum:!1,width:0,height:0,color:"black",background:"white",text:{visible:!0,font:"16px Consolas, Monaco, Sans Mono, monospace, sans-serif",color:"black",margin:{top:0,bottom:0,left:0,right:0}},border:{width:0,dashType:"solid",color:"black"},padding:{top:0,bottom:0,left:0,right:0}}}),w.ExportMixin.extend(h.fn),w.ui.plugin(h),d.deepExtend(w,{encodings:_,Encoding:T})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,a){(a||e)()});
//# sourceMappingURL=kendo.dataviz.barcode.min.js.map
