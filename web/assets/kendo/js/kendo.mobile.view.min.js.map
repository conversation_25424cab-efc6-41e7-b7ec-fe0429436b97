{"version": 3, "sources": ["kendo.mobile.view.js"], "names": ["f", "define", "$", "undefined", "initPopOvers", "element", "idx", "length", "popovers", "find", "roleSelector", "roles", "ui", "kendo", "initWidget", "preventScrollIfNotInput", "e", "triggeredByInput", "preventDefault", "initWidgets", "collection", "each", "this", "window", "mobile", "attr", "Widget", "ViewClone", "INIT", "UI_OVERLAY", "BEFORE_SHOW", "SHOW", "AFTER_SHOW", "BEFORE_HIDE", "TRANSITION_END", "TRANSITION_START", "HIDE", "DESTROY", "attrValue", "directiveSelector", "compileMobileDirective", "View", "extend", "init", "options", "fn", "call", "params", "transition", "defaultTransition", "_id", "$angular", "_overlay", "_layout", "_scroller", "_model", "events", "name", "title", "layout", "getLayout", "noop", "reload", "useNativeScrolling", "stretch", "zoom", "model", "modelScope", "scroller", "enable", "overlay", "hide", "show", "destroy", "detach", "trigger", "scope", "$destroy", "purge", "remove", "triggerBeforeShow", "view", "triggerBeforeHide", "showStart", "css", "inited", "_invokeNgController", "attach", "_padIfNativeScrolling", "resize", "showEnd", "hideEnd", "that", "beforeTransition", "type", "afterTransition", "appLevelNativeScrolling", "isAndroid", "support", "mobileOS", "android", "skin", "application", "isAndroidForced", "os", "indexOf", "hasPlatformIndependentSkin", "topContainer", "bottomContainer", "content", "paddingTop", "height", "paddingBottom", "contentElement", "scroller<PERSON><PERSON>nt", "clone", "addClass", "kendoMobileScroller", "useNative", "data", "scrollElement", "kineticScrollNeeded", "on", "getter", "bind", "dataviz", "children", "idAttrValue", "id", "guid", "contentSelector", "header", "footer", "wrapInner", "prepend", "append", "setup", "appendTo", "controller", "callback", "proxy", "test", "$$phase", "$apply", "_callController", "injector", "invoke", "constructor", "$scope", "Layout", "_locate", "elements", "add", "selectors", "platform", "cloneNode", "previousView", "current<PERSON>iew", "Observable", "bodyRegExp", "LOAD_START", "LOAD_COMPLETE", "SHOW_START", "SAME_VIEW_REQUESTED", "VIEW_SHOW", "VIEW_TYPE_DETERMINED", "AFTER", "ViewEngine", "views", "errorMessage", "container", "sandbox", "_hideViews", "rootView", "first", "rootNeeded", "Error", "layouts", "viewContainer", "ViewContainer", "getLayoutProxy", "_setupLayouts", "viewOptions", "loader", "showView", "url", "replace", "RegExp", "remoteViewURLPrefix", "showClosure", "_findViewElement", "widgetInstance", "remote", "_createView", "serverNavigation", "location", "href", "_loadView", "html", "modalViews", "url<PERSON><PERSON>", "split", "$1", "innerHTML", "char<PERSON>t", "_getLayout", "_xhr", "abort", "get", "absoluteURL", "always", "_xhrComplete", "response", "success", "status", "responseText", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBACH,aACA,WACA,wBACA,cACDD,IACL,WA2gBE,MA7fC,UAAUE,EAAGC,GAEV,QAASC,GAAaC,GAClB,GAAsDC,GAAKC,EAAvDC,EAAWH,EAAQI,KAAKC,EAAa,YAA0BC,EAAQC,EAAGD,KAC9E,KAAKL,EAAM,EAAGC,EAASC,EAASD,OAAQD,EAAMC,EAAQD,IAClDO,EAAMC,WAAWN,EAASF,MAAUK,GAG5C,QAASI,GAAwBC,GACxBH,EAAMI,iBAAiBD,IACxBA,EAAEE,iBA+NV,QAASC,GAAYC,GACjBA,EAAWC,KAAK,WACZR,EAAMC,WAAWZ,EAAEoB,SAAWV,EAAGD,SA3O5C,GACOE,GAAQU,OAAOV,MAAOW,EAASX,EAAMW,OAAQZ,EAAKY,EAAOZ,GAAIa,EAAOZ,EAAMY,KAAMC,EAASd,EAAGc,OAAQC,EAAYd,EAAMc,UAAWC,EAAO,OAAQC,EAAa,gHAAiHC,EAAc,aAAcC,EAAO,OAAQC,EAAa,YAAaC,EAAc,aAAcC,EAAiB,gBAAiBC,EAAmB,kBAAmBC,EAAO,OAAQC,EAAU,UAAWC,EAAYzB,EAAMyB,UAAW5B,EAAeG,EAAMH,aAAc6B,EAAoB1B,EAAM0B,kBAAmBC,EAAyB3B,EAAM2B,uBAYvmBC,EAAOf,EAAOgB,QACdC,KAAM,SAAUtC,EAASuC,GACrBlB,EAAOmB,GAAGF,KAAKG,KAAKxB,KAAMjB,EAASuC,GACnCtB,KAAKyB,UACL7C,EAAEwC,OAAOpB,KAAMsB,GACftB,KAAK0B,WAAa1B,KAAK0B,YAAc1B,KAAK2B,kBAC1C3B,KAAK4B,MACA5B,KAAKsB,QAAQO,SAMd7B,KAAK8B,YALL9B,KAAK+B,UACL/B,KAAK8B,WACL9B,KAAKgC,YACLhC,KAAKiC,WAKbC,QACI5B,EACAE,EACAC,EACAC,EACAC,EACAG,EACAC,EACAF,EACAD,GAEJU,SACIa,KAAM,OACNC,MAAO,GACPC,OAAQ,KACRC,UAAW1D,EAAE2D,KACbC,QAAQ,EACRd,WAAY,GACZC,kBAAmB,GACnBc,oBAAoB,EACpBC,SAAS,EACTC,MAAM,EACNC,MAAO,KACPC,WAAY5C,OACZ6C,YACAjD,aAAa,GAEjBkD,OAAQ,SAAUA,GACO,IAAVA,IACPA,GAAS,GAETA,EACA/C,KAAKgD,QAAQC,OAEbjD,KAAKgD,QAAQE,QAGrBC,QAAS,WACDnD,KAAKqC,QACLrC,KAAKqC,OAAOe,OAAOpD,MAEvBA,KAAKqD,QAAQtC,GACbX,EAAOmB,GAAG4B,QAAQ3B,KAAKxB,MACnBA,KAAK8C,UACL9C,KAAK8C,SAASK,UAEdnD,KAAKsB,QAAQO,UACb7B,KAAKjB,QAAQuE,QAAQC,WAEzBhE,EAAM4D,QAAQnD,KAAKjB,UAEvByE,MAAO,WACHxD,KAAKmD,UACLnD,KAAKjB,QAAQ0E,UAEjBC,kBAAmB,WACf,OAAI1D,KAAKqD,QAAQ7C,GAAemD,KAAM3D,QAK1C4D,kBAAmB,WACf,OAAI5D,KAAKqD,QAAQ1C,GAAegD,KAAM3D,QAK1C6D,UAAW,WACP,GAAI9E,GAAUiB,KAAKjB,OACnBA,GAAQ+E,IAAI,UAAW,IAClB9D,KAAK+D,OAIN/D,KAAKgE,uBAHLhE,KAAK+D,QAAS,EACd/D,KAAKqD,QAAQ/C,GAAQqD,KAAM3D,QAI3BA,KAAKqC,QACLrC,KAAKqC,OAAO4B,OAAOjE,MAEvBA,KAAKkE,wBACLlE,KAAKqD,QAAQ5C,GAAQkD,KAAM3D,OAC3BT,EAAM4E,OAAOpF,IAEjBqF,QAAS,WACLpE,KAAKqD,QAAQ3C,GAAciD,KAAM3D,OACjCA,KAAKkE,yBAETG,QAAS,WACL,GAAIC,GAAOtE,IACXsE,GAAKvF,QAAQkE,OACbqB,EAAKjB,QAAQvC,GAAQ6C,KAAMW,IACvBA,EAAKjC,QACLiC,EAAKjC,OAAOgB,QAAQvC,GAChB6C,KAAMW,EACNjC,OAAQiC,EAAKjC,UAIzBkC,iBAAkB,SAAUC,GACxBxE,KAAKqD,QAAQxC,GAAoB2D,KAAMA,KAE3CC,gBAAiB,SAAUD,GACvBxE,KAAKqD,QAAQzC,GAAkB4D,KAAMA,KAEzCN,sBAAuB,WACnB,GAAIhE,EAAOwE,0BAA2B,CAClC,GAAIC,GAAYpF,EAAMqF,QAAQC,UAAYtF,EAAMqF,QAAQC,SAASC,QAASC,EAAO7E,EAAO8E,YAAYD,QAAU,GAAIE,EAAkB/E,EAAO8E,YAAYE,GAAGJ,SAAWC,EAAKI,QAAQ,cAAiBC,EAAsC,SAATL,GAAmBA,EAAKI,QAAQ,eAAkBE,GAAgBV,IAAaM,GAAqBG,EAAwC,SAAX,SAAqBE,GAAmBX,IAAaM,GAAqBG,EAAwC,SAAX,QACxcpF,MAAKuF,QAAQzB,KACT0B,WAAYxF,KAAKqF,GAAcI,SAC/BC,cAAe1F,KAAKsF,GAAiBG,aAIjDE,eAAgB,WACZ,GAAIrB,GAAOtE,IACX,OAAOsE,GAAKhD,QAAQoB,QAAU4B,EAAKiB,QAAUjB,EAAKsB,iBAEtDC,MAAO,WACH,MAAO,IAAIxF,GAAUL,OAEzBgC,UAAW,WACP,GAAIsC,GAAOtE,IACPE,GAAOwE,4BAGPJ,EAAKhD,QAAQoB,QACb4B,EAAKiB,QAAQO,SAAS,sBAEtBxB,EAAKiB,QAAQQ,oBAAoBnH,EAAEwC,OAAOkD,EAAKhD,QAAQwB,UACnDH,KAAM2B,EAAKhD,QAAQqB,KACnBqD,UAAW1B,EAAKhD,QAAQmB,sBAE5B6B,EAAKxB,SAAWwB,EAAKiB,QAAQU,KAAK,uBAClC3B,EAAKsB,gBAAkBtB,EAAKxB,SAASoD,eAErC3G,EAAMqF,QAAQuB,sBACdvH,EAAE0F,EAAKvF,SAASqH,GAAG,YAAa,aAAc3G,GACzC6E,EAAKhD,QAAQmB,oBAAuB6B,EAAKhD,QAAQoB,SAClD9D,EAAE0F,EAAKvF,SAASqH,GAAG,YAAa,cAAe3G,MAI3DwC,OAAQ,WACJ,GAAIqC,GAAOtE,KAAMjB,EAAUuF,EAAKvF,QAAS6D,EAAQ0B,EAAKhD,QAAQsB,KACzC,iBAAVA,KACPA,EAAQrD,EAAM8G,OAAOzD,GAAO0B,EAAKhD,QAAQuB,aAE7CyB,EAAK1B,MAAQA,EACb9D,EAAaC,GACbuF,EAAKvF,QAAQ+E,IAAI,UAAW,IACxBQ,EAAKhD,QAAQzB,cACT+C,EACArD,EAAM+G,KAAKvH,EAAS6D,EAAOtD,EAAIC,EAAMD,GAAIC,EAAMgH,QAAQjH,IAEvDY,EAAOmB,KAAKtC,EAAQyH,aAG5BlC,EAAKvF,QAAQ+E,IAAI,UAAW,SAEhClC,IAAK,WACD,GAAI7C,GAAUiB,KAAKjB,QAAS0H,EAAc1H,EAAQoB,KAAK,OAAS,EAChEH,MAAK0G,GAAK1F,EAAUjC,EAAS,QAAU,IAAM0H,EAC9B,KAAXzG,KAAK0G,KACL1G,KAAK0G,GAAKnH,EAAMoH,OAChB5H,EAAQoB,KAAK,KAAMH,KAAK0G,MAGhC3E,QAAS,WACL,GAAI6E,GAAkBxH,EAAa,WAAYL,EAAUiB,KAAKjB,OAC9DA,GAAQ+G,SAAS,WACjB9F,KAAK6G,OAAS9H,EAAQyH,SAASpH,EAAa,WAAW0G,SAAS,aAChE9F,KAAK8G,OAAS/H,EAAQyH,SAASpH,EAAa,WAAW0G,SAAS,aAC3D/G,EAAQyH,SAASI,GAAiB,IACnC7H,EAAQgI,UAAU,QAAU5G,EAAK,QAAU,qBAE/CH,KAAKuF,QAAUxG,EAAQyH,SAASpH,EAAa,YAAY0G,SAAS,cAClE9F,KAAKjB,QAAQiI,QAAQhH,KAAK6G,QAAQI,OAAOjH,KAAK8G,QAC9C9G,KAAKqC,OAASrC,KAAKsB,QAAQgB,UAAUtC,KAAKqC,QACtCrC,KAAKqC,QACLrC,KAAKqC,OAAO6E,MAAMlH,OAG1B8B,SAAU,WACN9B,KAAKgD,QAAUpE,EAAE2B,GAAY4G,SAASnH,KAAKjB,UAE/CiF,oBAAqB,WAAA,GACboD,GAAY9D,EAKJ+D,CAJRrH,MAAKsB,QAAQO,WACbuF,EAAapH,KAAKjB,QAAQqI,aAC1B9D,EAAQtD,KAAKsB,QAAQO,SAAS,GAC1BuF,IACIC,EAAWzI,EAAE0I,MAAMtH,KAAM,kBAAmBoH,EAAY9D,GACxD,qBAAqBiE,KAAKjE,EAAMkE,SAChCH,IAEA/D,EAAMmE,OAAOJ,MAK7BK,gBAAiB,SAAUN,EAAY9D,GACnCtD,KAAKjB,QAAQ4I,WAAWC,OAAOR,EAAWS,YAAaT,GAAcU,OAAQxE,OAQjFyE,EAAS3H,EAAOgB,QAChBC,KAAM,SAAUtC,EAASuC,GACrBlB,EAAOmB,GAAGF,KAAKG,KAAKxB,KAAMjB,EAASuC,GACnCvC,EAAUiB,KAAKjB,QACfiB,KAAK6G,OAAS9H,EAAQyH,SAASxG,KAAKgI,QAAQ,WAAWlC,SAAS,aAChE9F,KAAK8G,OAAS/H,EAAQyH,SAASxG,KAAKgI,QAAQ,WAAWlC,SAAS,aAChE9F,KAAKiI,SAAWjI,KAAK6G,OAAOqB,IAAIlI,KAAK8G,QACrChI,EAAaC,GACRiB,KAAKsB,QAAQO,UACdtC,EAAMW,OAAOmB,KAAKrB,KAAKjB,QAAQyH,YAEnCxG,KAAKjB,QAAQqE,SACbpD,KAAKqD,QAAQ/C,GAAQ+B,OAAQrC,QAEjCgI,QAAS,SAAUG,GACf,MAAOnI,MAAKsB,QAAQO,SAAWZ,EAAkBkH,GAAa/I,EAAa+I,IAE/E7G,SACIa,KAAM,SACNuE,GAAI,KACJ0B,SAAU,MAEdlG,QACI5B,EACAG,EACAK,GAEJoG,MAAO,SAAUvD,GACRA,EAAKkD,OAAO,KACblD,EAAKkD,OAAS7G,KAAK6G,QAElBlD,EAAKmD,OAAO,KACbnD,EAAKmD,OAAS9G,KAAK8G,SAG3B1D,OAAQ,SAAUO,GACd,GAAIW,GAAOtE,IACP2D,GAAKkD,SAAWvC,EAAKuC,QAAUvC,EAAKuC,OAAO,IAC3ClD,EAAK5E,QAAQiI,QAAQ1C,EAAKuC,OAAOzD,SAAS,GAAGiF,WAAU,IAEvD1E,EAAKmD,SAAWxC,EAAKwC,QAAUxC,EAAKwC,OAAO7H,QAC3C0E,EAAK5E,QAAQkI,OAAO3C,EAAKwC,OAAO1D,SAAS,GAAGiF,WAAU,KAG9DpE,OAAQ,SAAUN,GACd,GAAIW,GAAOtE,KAAMsI,EAAehE,EAAKiE,WACjCD,IACAhE,EAAKlB,OAAOkF,GAEZ3E,EAAKkD,SAAWvC,EAAKuC,SACrBvC,EAAKuC,OAAOzD,SACZO,EAAK5E,QAAQyH,SAASpH,EAAa,WAAWqE,SAC9CE,EAAK5E,QAAQiI,QAAQ1C,EAAKuC,SAE1BlD,EAAKmD,SAAWxC,EAAKwC,SACrBxC,EAAKwC,OAAO1D,SACZO,EAAK5E,QAAQyH,SAASpH,EAAa,WAAWqE,SAC9CE,EAAK5E,QAAQkI,OAAO3C,EAAKwC,SAE7BxC,EAAKjB,QAAQ5C,GACT4B,OAAQiC,EACRX,KAAMA,IAEVW,EAAKiE,YAAc5E,KAGvB6E,EAAajJ,EAAMiJ,WAAYC,EAAa,wDAAyDC,EAAa,YAAaC,EAAgB,eAAgBC,EAAa,YAAaC,EAAsB,oBAAqBC,EAAY,WAAYC,EAAuB,qBAAsBC,EAAQ,QACjTC,EAAaT,EAAWpH,QACxBC,KAAM,SAAUC,GACZ,GAAiB4H,GAAOC,EAAcC,EAAWtJ,EAA7CwE,EAAOtE,IAOX,IANAwI,EAAWjH,GAAGF,KAAKG,KAAK8C,GACxB1F,EAAEwC,OAAOkD,EAAMhD,GACfgD,EAAK+E,QAAUzK,EAAE,WACjBwK,EAAY9E,EAAK8E,UACjBF,EAAQ5E,EAAKgF,WAAWF,GACxB9E,EAAKiF,SAAWL,EAAMM,SACjBlF,EAAKiF,SAAS,IAAMjI,EAAQmI,WAM7B,KAJIN,GADAC,EAAU,IAAM7J,EAAMW,OAAO8E,YAAYjG,QAAQ,GAClC,2MAEA,oGAET2K,MAAMP,EAEpB7E,GAAKqF,WACLrF,EAAKsF,cAAgB,GAAIrK,GAAMsK,cAAcvF,EAAK8E,WAClD9E,EAAKsF,cAActD,KAAK,WAAY,SAAU5G,GAC1CA,EAAEiE,KAAKlC,OAAS6C,EAAK7C,SAEzB6C,EAAKsF,cAActD,KAAK,WAAY,SAAU5G,GAC1C4E,EAAKjB,QAAQyF,GAAanF,KAAMjE,EAAEiE,SAEtCW,EAAKsF,cAActD,KAAK0C,EAAO,WAC3B1E,EAAKjB,QAAQ2F,KAEjBhJ,KAAK8J,eAAiBlL,EAAE0I,MAAMtH,KAAM,cACpCsE,EAAKyF,cAAcX,GACnBtJ,EAAasJ,EAAU5C,SAASlC,EAAK0D,QAAQ,qBACzC1D,EAAKzC,UACLyC,EAAKzC,SAAS,GAAGmI,aACbrI,kBAAmB2C,EAAK5C,WACxBuI,OAAQ3F,EAAK2F,OACbb,UAAW9E,EAAK8E,UAChB9G,UAAWgC,EAAKwF,gBAEpBhK,EAAWC,KAAK,SAAUf,EAAKD,GAC3BmC,EAAuBtC,EAAEG,GAAUuC,EAAQO,SAAS,OAGxDhC,EAAYC,GAEhBE,KAAKsG,KAAKtG,KAAKkC,OAAQZ,IAE3BY,QACI0G,EACAI,EACAF,EACAJ,EACAC,EACAE,EACAE,GAEJ5F,QAAS,WACL5D,EAAM4D,QAAQnD,KAAKoJ,UACnB,KAAK,GAAI1C,KAAM1G,MAAK2J,QAChB3J,KAAK2J,QAAQjD,GAAIvD,WAGzBQ,KAAM,WACF,MAAO3D,MAAK4J,cAAcjG,MAE9BuG,SAAU,SAAUC,EAAKzI,EAAYD,GAKjC,GAJA0I,EAAMA,EAAIC,QAAYC,OAAO,IAAMrK,KAAKsK,qBAAsB,IAClD,KAARH,GAAcnK,KAAKsK,sBACnBH,EAAM,KAENA,EAAIC,QAAQ,KAAM,MAAQpK,KAAKmK,IAE/B,MADAnK,MAAKqD,QAAQwF,IACN,CAEX7I,MAAKqD,QAAQuF,EACb,IAAItE,GAAOtE,KAAMuK,EAAc,SAAU5G,GACjC,MAAOW,GAAKsF,cAAc1G,KAAKS,EAAMjC,EAAYyI,IAClDpL,EAAUuF,EAAKkG,iBAAiBL,GAAMxG,EAAOpE,EAAMkL,eAAe1L,EAWzE,OAVAuF,GAAK6F,IAAMA,EAAIC,QAAQ,KAAM,IAC7B9F,EAAK7C,OAASA,EACVkC,GAAQA,EAAKnB,SACbmB,EAAKH,QACLzE,MAEJiB,KAAKqD,QAAQ0F,GACT2B,OAA2B,IAAnB3L,EAAQE,OAChBkL,IAAKA,IAELpL,EAAQ,IACH4E,IACDA,EAAOW,EAAKqG,YAAY5L,IAErBwL,EAAY5G,KAEf3D,KAAK4K,iBACLC,SAASC,KAAOX,EAEhB7F,EAAKyG,UAAUZ,EAAKI,IAEjB,IAGftD,OAAQ,SAAU+D,EAAMb,GACpB,GAA6FjB,GAAO+B,EAAYtH,EAA5G0F,EAAUrJ,KAAKqJ,QAAS6B,GAAWf,GAAO,IAAIgB,MAAM,KAAK,GAAI/B,EAAYpJ,KAAKoJ,SAkBlF,OAjBIX,GAAWlB,KAAKyD,KAChBA,EAAOX,OAAOe,IAElB/B,EAAQ,GAAGgC,UAAYL,EACvB5B,EAAUnC,OAAOoC,EAAQ7C,SAAS,kBAClC0C,EAAQlJ,KAAKsJ,WAAWD,GACxB1F,EAAOuF,EAAMM,QACR7F,EAAK1E,SACNiK,EAAQvF,EAAO0F,EAAQtC,UAAU,0BAA0BP,YAE3D0E,GACAvH,EAAKV,OAAO9C,KAAKA,EAAK,OAAQ+K,GAElClL,KAAK+J,cAAcV,GACnB4B,EAAa5B,EAAQ7C,SAASxG,KAAKgI,QAAQ,qBAC3CoB,EAAUnC,OAAOoC,EAAQ7C,SAASxG,KAAKgI,QAAQ,4BAA4BE,IAAIgB,IAC/ErJ,EAAYoL,GACLjL,KAAK2K,YAAYhH,IAE5BqE,QAAS,SAAUG,GACf,MAAOnI,MAAK6B,SAAWZ,EAAkBkH,GAAa/I,EAAa+I,IAEvEqC,iBAAkB,SAAUL,GACxB,GAAIpL,GAASmM,EAAUf,EAAIgB,MAAM,KAAK,EACtC,OAAKD,IAGLnM,EAAUiB,KAAKoJ,UAAU5C,SAAS,IAAMrG,EAAK,OAAS,KAAQ+K,EAAU,MACnEnM,EAAQ,IAAMmM,EAAQ/F,QAAQ,YAC/BpG,EAAUiB,KAAKoJ,UAAU5C,SAA+B,MAAtB0E,EAAQI,OAAO,GAAaJ,EAAU,IAAMA,IAE3EnM,GANIiB,KAAKuJ,UAQpBoB,YAAa,SAAU5L,GACnB,MAAIiB,MAAK6B,SACEX,EAAuBnC,EAASiB,KAAK6B,SAAS,IAE9CtC,EAAMC,WAAWT,GACpB4C,kBAAmB3B,KAAK0B,WACxBuI,OAAQjK,KAAKiK,OACbb,UAAWpJ,KAAKoJ,UAChB9G,UAAWtC,KAAK8J,eAChBjH,WAAY7C,KAAK6C,WACjBL,OAAQxB,EAAUjC,EAAS,WAC5BO,EAAGD,QAGdkM,WAAY,SAAUpJ,GAClB,MAAa,KAATA,EACO,KAEJA,EAAOnC,KAAK2J,QAAQxH,GAAQnC,KAAK2J,QAAQ3J,KAAKqC,SAEzD0I,UAAW,SAAUZ,EAAK9C,GAClBrH,KAAKwL,MACLxL,KAAKwL,KAAKC,QAEdzL,KAAKqD,QAAQqF,GACb1I,KAAKwL,KAAO5M,EAAE8M,IAAInM,EAAMoM,YAAYxB,EAAKnK,KAAKsK,qBAAsB,QAAQsB,OAAOhN,EAAE0I,MAAMtH,KAAM,eAAgBqH,EAAU8C,KAE/H0B,aAAc,SAAUxE,EAAU8C,EAAK2B,GACnC,GAAIC,IAAU,CACd,IAAwB,gBAAbD,IACiB,IAApBA,EAASE,OAAc,CACvB,KAAIF,EAASG,cAAgBH,EAASG,aAAahN,OAAS,GAIxD,MAHA8M,IAAU,EACVD,EAAWA,EAASG,aAMhCjM,KAAKqD,QAAQsF,GACToD,GACA1E,EAASrH,KAAKiH,OAAO6E,EAAU3B,KAGvCb,WAAY,SAAUF,GAClB,MAAOA,GAAU5C,SAASxG,KAAKgI,QAAQ,mBAAmB/E,QAE9D8G,cAAe,SAAUhL,GACrB,GAAiBsD,GAAbiC,EAAOtE,IACXjB,GAAQyH,SAASlC,EAAK0D,QAAQ,WAAWjI,KAAK,WAEtCsC,EADAiC,EAAKzC,SACIX,EAAuBtC,EAAEoB,MAAOsE,EAAKzC,SAAS,IAE9CtC,EAAMC,WAAWZ,EAAEoB,SAAWV,EAAGD,MAE9C,IAAI+I,GAAW/F,EAAOf,QAAQ8G,QACzBA,IAAYA,IAAalI,EAAO8E,YAAYE,GAAG/C,KAGhDE,EAAOc,UAFPmB,EAAKqF,QAAQtH,EAAOf,QAAQoF,IAAMrE,MAOlD9C,GAAMW,OAAO+I,WAAaA,EAC1B3J,EAAG4M,OAAO/K,GACV7B,EAAG4M,OAAOnE,IACZ9H,OAAOV,MAAM4M,QACRlM,OAAOV,OACE,kBAAVZ,SAAwBA,OAAOyN,IAAMzN,OAAS,SAAU0N,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.view.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.view', [\n        'kendo.core',\n        'kendo.fx',\n        'kendo.mobile.scroller',\n        'kendo.view'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.view',\n        name: 'View',\n        category: 'mobile',\n        description: 'Mobile View',\n        depends: [\n            'core',\n            'fx',\n            'mobile.scroller',\n            'view'\n        ],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, mobile = kendo.mobile, ui = mobile.ui, attr = kendo.attr, Widget = ui.Widget, ViewClone = kendo.ViewClone, INIT = 'init', UI_OVERLAY = '<div style=\"height: 100%; width: 100%; position: absolute; top: 0; left: 0; z-index: 20000; display: none\" />', BEFORE_SHOW = 'beforeShow', SHOW = 'show', AFTER_SHOW = 'afterShow', BEFORE_HIDE = 'beforeHide', TRANSITION_END = 'transitionEnd', TRANSITION_START = 'transitionStart', HIDE = 'hide', DESTROY = 'destroy', attrValue = kendo.attrValue, roleSelector = kendo.roleSelector, directiveSelector = kendo.directiveSelector, compileMobileDirective = kendo.compileMobileDirective;\n        function initPopOvers(element) {\n            var popovers = element.find(roleSelector('popover')), idx, length, roles = ui.roles;\n            for (idx = 0, length = popovers.length; idx < length; idx++) {\n                kendo.initWidget(popovers[idx], {}, roles);\n            }\n        }\n        function preventScrollIfNotInput(e) {\n            if (!kendo.triggeredByInput(e)) {\n                e.preventDefault();\n            }\n        }\n        var View = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this.params = {};\n                $.extend(this, options);\n                this.transition = this.transition || this.defaultTransition;\n                this._id();\n                if (!this.options.$angular) {\n                    this._layout();\n                    this._overlay();\n                    this._scroller();\n                    this._model();\n                } else {\n                    this._overlay();\n                }\n            },\n            events: [\n                INIT,\n                BEFORE_SHOW,\n                SHOW,\n                AFTER_SHOW,\n                BEFORE_HIDE,\n                HIDE,\n                DESTROY,\n                TRANSITION_START,\n                TRANSITION_END\n            ],\n            options: {\n                name: 'View',\n                title: '',\n                layout: null,\n                getLayout: $.noop,\n                reload: false,\n                transition: '',\n                defaultTransition: '',\n                useNativeScrolling: false,\n                stretch: false,\n                zoom: false,\n                model: null,\n                modelScope: window,\n                scroller: {},\n                initWidgets: true\n            },\n            enable: function (enable) {\n                if (typeof enable == 'undefined') {\n                    enable = true;\n                }\n                if (enable) {\n                    this.overlay.hide();\n                } else {\n                    this.overlay.show();\n                }\n            },\n            destroy: function () {\n                if (this.layout) {\n                    this.layout.detach(this);\n                }\n                this.trigger(DESTROY);\n                Widget.fn.destroy.call(this);\n                if (this.scroller) {\n                    this.scroller.destroy();\n                }\n                if (this.options.$angular) {\n                    this.element.scope().$destroy();\n                }\n                kendo.destroy(this.element);\n            },\n            purge: function () {\n                this.destroy();\n                this.element.remove();\n            },\n            triggerBeforeShow: function () {\n                if (this.trigger(BEFORE_SHOW, { view: this })) {\n                    return false;\n                }\n                return true;\n            },\n            triggerBeforeHide: function () {\n                if (this.trigger(BEFORE_HIDE, { view: this })) {\n                    return false;\n                }\n                return true;\n            },\n            showStart: function () {\n                var element = this.element;\n                element.css('display', '');\n                if (!this.inited) {\n                    this.inited = true;\n                    this.trigger(INIT, { view: this });\n                } else {\n                    this._invokeNgController();\n                }\n                if (this.layout) {\n                    this.layout.attach(this);\n                }\n                this._padIfNativeScrolling();\n                this.trigger(SHOW, { view: this });\n                kendo.resize(element);\n            },\n            showEnd: function () {\n                this.trigger(AFTER_SHOW, { view: this });\n                this._padIfNativeScrolling();\n            },\n            hideEnd: function () {\n                var that = this;\n                that.element.hide();\n                that.trigger(HIDE, { view: that });\n                if (that.layout) {\n                    that.layout.trigger(HIDE, {\n                        view: that,\n                        layout: that.layout\n                    });\n                }\n            },\n            beforeTransition: function (type) {\n                this.trigger(TRANSITION_START, { type: type });\n            },\n            afterTransition: function (type) {\n                this.trigger(TRANSITION_END, { type: type });\n            },\n            _padIfNativeScrolling: function () {\n                if (mobile.appLevelNativeScrolling()) {\n                    var isAndroid = kendo.support.mobileOS && kendo.support.mobileOS.android, skin = mobile.application.skin() || '', isAndroidForced = mobile.application.os.android || skin.indexOf('android') > -1, hasPlatformIndependentSkin = skin === 'flat' || skin.indexOf('material') > -1, topContainer = (isAndroid || isAndroidForced) && !hasPlatformIndependentSkin ? 'footer' : 'header', bottomContainer = (isAndroid || isAndroidForced) && !hasPlatformIndependentSkin ? 'header' : 'footer';\n                    this.content.css({\n                        paddingTop: this[topContainer].height(),\n                        paddingBottom: this[bottomContainer].height()\n                    });\n                }\n            },\n            contentElement: function () {\n                var that = this;\n                return that.options.stretch ? that.content : that.scrollerContent;\n            },\n            clone: function () {\n                return new ViewClone(this);\n            },\n            _scroller: function () {\n                var that = this;\n                if (mobile.appLevelNativeScrolling()) {\n                    return;\n                }\n                if (that.options.stretch) {\n                    that.content.addClass('km-stretched-view');\n                } else {\n                    that.content.kendoMobileScroller($.extend(that.options.scroller, {\n                        zoom: that.options.zoom,\n                        useNative: that.options.useNativeScrolling\n                    }));\n                    that.scroller = that.content.data('kendoMobileScroller');\n                    that.scrollerContent = that.scroller.scrollElement;\n                }\n                if (kendo.support.kineticScrollNeeded) {\n                    $(that.element).on('touchmove', '.km-header', preventScrollIfNotInput);\n                    if (!that.options.useNativeScrolling && !that.options.stretch) {\n                        $(that.element).on('touchmove', '.km-content', preventScrollIfNotInput);\n                    }\n                }\n            },\n            _model: function () {\n                var that = this, element = that.element, model = that.options.model;\n                if (typeof model === 'string') {\n                    model = kendo.getter(model)(that.options.modelScope);\n                }\n                that.model = model;\n                initPopOvers(element);\n                that.element.css('display', '');\n                if (that.options.initWidgets) {\n                    if (model) {\n                        kendo.bind(element, model, ui, kendo.ui, kendo.dataviz.ui);\n                    } else {\n                        mobile.init(element.children());\n                    }\n                }\n                that.element.css('display', 'none');\n            },\n            _id: function () {\n                var element = this.element, idAttrValue = element.attr('id') || '';\n                this.id = attrValue(element, 'url') || '#' + idAttrValue;\n                if (this.id == '#') {\n                    this.id = kendo.guid();\n                    element.attr('id', this.id);\n                }\n            },\n            _layout: function () {\n                var contentSelector = roleSelector('content'), element = this.element;\n                element.addClass('km-view');\n                this.header = element.children(roleSelector('header')).addClass('km-header');\n                this.footer = element.children(roleSelector('footer')).addClass('km-footer');\n                if (!element.children(contentSelector)[0]) {\n                    element.wrapInner('<div ' + attr('role') + '=\"content\"></div>');\n                }\n                this.content = element.children(roleSelector('content')).addClass('km-content');\n                this.element.prepend(this.header).append(this.footer);\n                this.layout = this.options.getLayout(this.layout);\n                if (this.layout) {\n                    this.layout.setup(this);\n                }\n            },\n            _overlay: function () {\n                this.overlay = $(UI_OVERLAY).appendTo(this.element);\n            },\n            _invokeNgController: function () {\n                var controller, scope;\n                if (this.options.$angular) {\n                    controller = this.element.controller();\n                    scope = this.options.$angular[0];\n                    if (controller) {\n                        var callback = $.proxy(this, '_callController', controller, scope);\n                        if (/^\\$(digest|apply)$/.test(scope.$$phase)) {\n                            callback();\n                        } else {\n                            scope.$apply(callback);\n                        }\n                    }\n                }\n            },\n            _callController: function (controller, scope) {\n                this.element.injector().invoke(controller.constructor, controller, { $scope: scope });\n            }\n        });\n        function initWidgets(collection) {\n            collection.each(function () {\n                kendo.initWidget($(this), {}, ui.roles);\n            });\n        }\n        var Layout = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                element = this.element;\n                this.header = element.children(this._locate('header')).addClass('km-header');\n                this.footer = element.children(this._locate('footer')).addClass('km-footer');\n                this.elements = this.header.add(this.footer);\n                initPopOvers(element);\n                if (!this.options.$angular) {\n                    kendo.mobile.init(this.element.children());\n                }\n                this.element.detach();\n                this.trigger(INIT, { layout: this });\n            },\n            _locate: function (selectors) {\n                return this.options.$angular ? directiveSelector(selectors) : roleSelector(selectors);\n            },\n            options: {\n                name: 'Layout',\n                id: null,\n                platform: null\n            },\n            events: [\n                INIT,\n                SHOW,\n                HIDE\n            ],\n            setup: function (view) {\n                if (!view.header[0]) {\n                    view.header = this.header;\n                }\n                if (!view.footer[0]) {\n                    view.footer = this.footer;\n                }\n            },\n            detach: function (view) {\n                var that = this;\n                if (view.header === that.header && that.header[0]) {\n                    view.element.prepend(that.header.detach()[0].cloneNode(true));\n                }\n                if (view.footer === that.footer && that.footer.length) {\n                    view.element.append(that.footer.detach()[0].cloneNode(true));\n                }\n            },\n            attach: function (view) {\n                var that = this, previousView = that.currentView;\n                if (previousView) {\n                    that.detach(previousView);\n                }\n                if (view.header === that.header) {\n                    that.header.detach();\n                    view.element.children(roleSelector('header')).remove();\n                    view.element.prepend(that.header);\n                }\n                if (view.footer === that.footer) {\n                    that.footer.detach();\n                    view.element.children(roleSelector('footer')).remove();\n                    view.element.append(that.footer);\n                }\n                that.trigger(SHOW, {\n                    layout: that,\n                    view: view\n                });\n                that.currentView = view;\n            }\n        });\n        var Observable = kendo.Observable, bodyRegExp = /<body[^>]*>(([\\u000a\\u000d\\u2028\\u2029]|.)*)<\\/body>/i, LOAD_START = 'loadStart', LOAD_COMPLETE = 'loadComplete', SHOW_START = 'showStart', SAME_VIEW_REQUESTED = 'sameViewRequested', VIEW_SHOW = 'viewShow', VIEW_TYPE_DETERMINED = 'viewTypeDetermined', AFTER = 'after';\n        var ViewEngine = Observable.extend({\n            init: function (options) {\n                var that = this, views, errorMessage, container, collection;\n                Observable.fn.init.call(that);\n                $.extend(that, options);\n                that.sandbox = $('<div />');\n                container = that.container;\n                views = that._hideViews(container);\n                that.rootView = views.first();\n                if (!that.rootView[0] && options.rootNeeded) {\n                    if (container[0] == kendo.mobile.application.element[0]) {\n                        errorMessage = 'Your kendo mobile application element does not contain any direct child elements with data-role=\"view\" attribute set. Make sure that you instantiate the mobile application using the correct container.';\n                    } else {\n                        errorMessage = 'Your pane element does not contain any direct child elements with data-role=\"view\" attribute set.';\n                    }\n                    throw new Error(errorMessage);\n                }\n                that.layouts = {};\n                that.viewContainer = new kendo.ViewContainer(that.container);\n                that.viewContainer.bind('accepted', function (e) {\n                    e.view.params = that.params;\n                });\n                that.viewContainer.bind('complete', function (e) {\n                    that.trigger(VIEW_SHOW, { view: e.view });\n                });\n                that.viewContainer.bind(AFTER, function () {\n                    that.trigger(AFTER);\n                });\n                this.getLayoutProxy = $.proxy(this, '_getLayout');\n                that._setupLayouts(container);\n                collection = container.children(that._locate('modalview drawer'));\n                if (that.$angular) {\n                    that.$angular[0].viewOptions = {\n                        defaultTransition: that.transition,\n                        loader: that.loader,\n                        container: that.container,\n                        getLayout: that.getLayoutProxy\n                    };\n                    collection.each(function (idx, element) {\n                        compileMobileDirective($(element), options.$angular[0]);\n                    });\n                } else {\n                    initWidgets(collection);\n                }\n                this.bind(this.events, options);\n            },\n            events: [\n                SHOW_START,\n                AFTER,\n                VIEW_SHOW,\n                LOAD_START,\n                LOAD_COMPLETE,\n                SAME_VIEW_REQUESTED,\n                VIEW_TYPE_DETERMINED\n            ],\n            destroy: function () {\n                kendo.destroy(this.container);\n                for (var id in this.layouts) {\n                    this.layouts[id].destroy();\n                }\n            },\n            view: function () {\n                return this.viewContainer.view;\n            },\n            showView: function (url, transition, params) {\n                url = url.replace(new RegExp('^' + this.remoteViewURLPrefix), '');\n                if (url === '' && this.remoteViewURLPrefix) {\n                    url = '/';\n                }\n                if (url.replace(/^#/, '') === this.url) {\n                    this.trigger(SAME_VIEW_REQUESTED);\n                    return false;\n                }\n                this.trigger(SHOW_START);\n                var that = this, showClosure = function (view) {\n                        return that.viewContainer.show(view, transition, url);\n                    }, element = that._findViewElement(url), view = kendo.widgetInstance(element);\n                that.url = url.replace(/^#/, '');\n                that.params = params;\n                if (view && view.reload) {\n                    view.purge();\n                    element = [];\n                }\n                this.trigger(VIEW_TYPE_DETERMINED, {\n                    remote: element.length === 0,\n                    url: url\n                });\n                if (element[0]) {\n                    if (!view) {\n                        view = that._createView(element);\n                    }\n                    return showClosure(view);\n                } else {\n                    if (this.serverNavigation) {\n                        location.href = url;\n                    } else {\n                        that._loadView(url, showClosure);\n                    }\n                    return true;\n                }\n            },\n            append: function (html, url) {\n                var sandbox = this.sandbox, urlPath = (url || '').split('?')[0], container = this.container, views, modalViews, view;\n                if (bodyRegExp.test(html)) {\n                    html = RegExp.$1;\n                }\n                sandbox[0].innerHTML = html;\n                container.append(sandbox.children('script, style'));\n                views = this._hideViews(sandbox);\n                view = views.first();\n                if (!view.length) {\n                    views = view = sandbox.wrapInner('<div data-role=view />').children();\n                }\n                if (urlPath) {\n                    view.hide().attr(attr('url'), urlPath);\n                }\n                this._setupLayouts(sandbox);\n                modalViews = sandbox.children(this._locate('modalview drawer'));\n                container.append(sandbox.children(this._locate('layout modalview drawer')).add(views));\n                initWidgets(modalViews);\n                return this._createView(view);\n            },\n            _locate: function (selectors) {\n                return this.$angular ? directiveSelector(selectors) : roleSelector(selectors);\n            },\n            _findViewElement: function (url) {\n                var element, urlPath = url.split('?')[0];\n                if (!urlPath) {\n                    return this.rootView;\n                }\n                element = this.container.children('[' + attr('url') + '=\\'' + urlPath + '\\']');\n                if (!element[0] && urlPath.indexOf('/') === -1) {\n                    element = this.container.children(urlPath.charAt(0) === '#' ? urlPath : '#' + urlPath);\n                }\n                return element;\n            },\n            _createView: function (element) {\n                if (this.$angular) {\n                    return compileMobileDirective(element, this.$angular[0]);\n                } else {\n                    return kendo.initWidget(element, {\n                        defaultTransition: this.transition,\n                        loader: this.loader,\n                        container: this.container,\n                        getLayout: this.getLayoutProxy,\n                        modelScope: this.modelScope,\n                        reload: attrValue(element, 'reload')\n                    }, ui.roles);\n                }\n            },\n            _getLayout: function (name) {\n                if (name === '') {\n                    return null;\n                }\n                return name ? this.layouts[name] : this.layouts[this.layout];\n            },\n            _loadView: function (url, callback) {\n                if (this._xhr) {\n                    this._xhr.abort();\n                }\n                this.trigger(LOAD_START);\n                this._xhr = $.get(kendo.absoluteURL(url, this.remoteViewURLPrefix), 'html').always($.proxy(this, '_xhrComplete', callback, url));\n            },\n            _xhrComplete: function (callback, url, response) {\n                var success = true;\n                if (typeof response === 'object') {\n                    if (response.status === 0) {\n                        if (response.responseText && response.responseText.length > 0) {\n                            success = true;\n                            response = response.responseText;\n                        } else {\n                            return;\n                        }\n                    }\n                }\n                this.trigger(LOAD_COMPLETE);\n                if (success) {\n                    callback(this.append(response, url));\n                }\n            },\n            _hideViews: function (container) {\n                return container.children(this._locate('view splitview')).hide();\n            },\n            _setupLayouts: function (element) {\n                var that = this, layout;\n                element.children(that._locate('layout')).each(function () {\n                    if (that.$angular) {\n                        layout = compileMobileDirective($(this), that.$angular[0]);\n                    } else {\n                        layout = kendo.initWidget($(this), {}, ui.roles);\n                    }\n                    var platform = layout.options.platform;\n                    if (!platform || platform === mobile.application.os.name) {\n                        that.layouts[layout.options.id] = layout;\n                    } else {\n                        layout.destroy();\n                    }\n                });\n            }\n        });\n        kendo.mobile.ViewEngine = ViewEngine;\n        ui.plugin(View);\n        ui.plugin(Layout);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}