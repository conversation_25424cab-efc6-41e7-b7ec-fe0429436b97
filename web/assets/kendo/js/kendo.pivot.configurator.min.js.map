{"version": 3, "sources": ["kendo.pivot.configurator.js"], "names": ["f", "define", "$", "undefined", "addKPI", "data", "found", "idx", "length", "type", "splice", "caption", "defaultHierarchy", "name", "uniqueName", "kpiNode", "node", "normalizeKPIs", "settingTargetFromNode", "target", "closest", "kpiMeasure", "measure", "hierarchyUniqueName", "kpi", "buildKPImeasures", "value", "goal", "status", "trend", "kendo", "window", "ui", "Widget", "ns", "HOVEREVENTS", "SETTING_CONTAINER_TEMPLATE", "template", "PivotConfigurator", "extend", "init", "element", "options", "fn", "call", "this", "addClass", "_dataSource", "_layout", "refresh", "notify", "events", "filterable", "sortable", "messages", "measures", "columns", "rows", "measures<PERSON>abel", "columnsLabel", "rowsLabel", "fieldsLabel", "that", "dataSource", "_refresh<PERSON><PERSON><PERSON>", "unbind", "_error<PERSON><PERSON><PERSON>", "_progress<PERSON><PERSON><PERSON>", "proxy", "_error", "_requestStart", "PivotDataSource", "create", "bind", "setDataSource", "_treeViewDataSource", "HierarchicalDataSource", "schema", "model", "id", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item", "transport", "read", "promise", "isEmptyObject", "schemaDimensions", "done", "cubeBuilder", "success", "fail", "error", "tree<PERSON>iew", "get", "schemaKPIs", "schemaMeasures", "dimensionUniqueName", "schemaLevels", "schemaHierarchies", "_progress", "toggle", "progress", "form", "appendTo", "_fields", "_targets", "container", "kendoTreeView", "dataTextField", "dragAndDrop", "autoBind", "dragstart", "e", "dataItem", "sourceNode", "aggregator", "preventDefault", "drag", "setting", "drop<PERSON>ar<PERSON>", "validate", "setStatusClass", "drop", "push", "add", "_createTarget", "icons", "PivotSettingTarget", "hint", "wrapper", "find", "append", "clone", "emptyTemplate", "columnsContainer", "icon", "last", "rowsContainer", "measuresContainer", "connectWith", "empty", "fieldMenu", "on", "_toggleHover", "currentTarget", "toggleClass", "_resize", "border", "fields", "height", "outerHeight", "_outerHeight", "is", "children", "innerHeight", "_cube", "cube", "_catalog", "catalog", "fetch", "destroy", "off", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,4BAA6B,aAAcD,IACpD,WA0VE,MA9UC,UAAUE,EAAGC,GAEV,QAASC,GAAOC,GAIZ,IAJJ,GACQC,GACAC,EAAM,EACNC,EAASH,EAAKG,OACXD,EAAMC,EAAQD,IACjB,GAAsB,GAAlBF,EAAKE,GAAKE,KAAW,CACrBH,GAAQ,CACR,OAGJA,GACAD,EAAKK,OAAOH,EAAM,EAAG,GACjBI,QAAS,OACTC,iBAAkB,SAClBC,KAAM,OACNC,WAAY,WAIxB,QAASC,GAAQC,GACb,OACIH,KAAMG,EAAKF,WACXL,KAAMO,EAAKP,MAGnB,QAASQ,GAAcZ,GACnB,IAAK,GAAIE,GAAM,EAAGC,EAASH,EAAKG,OAAQD,EAAMC,EAAQD,IAClDF,EAAKE,GAAKO,WAAaT,EAAKE,GAAKM,KACjCR,EAAKE,GAAKE,KAAO,KAErB,OAAOJ,GAEX,QAASa,GAAsBF,GAC3B,GAAIG,GAASjB,EAAEc,GAAMI,QAAQ,mBAC7B,OAAID,GAAOX,OACAW,EAAOd,KAAK,2BAEhB,KAiRX,QAASgB,GAAWR,EAAMS,EAASb,GAC/B,OACIc,oBAAqBV,EACrBC,WAAYQ,EACZX,QAASW,EACTA,QAASA,EACTT,KAAMS,EACNb,KAAMA,EACNe,KAAK,GAGb,QAASC,GAAiBT,GACtB,GAAIH,GAAOG,EAAKH,IAChB,QACIQ,EAAWR,EAAMG,EAAKU,MAAO,SAC7BL,EAAWR,EAAMG,EAAKW,KAAM,QAC5BN,EAAWR,EAAMG,EAAKY,OAAQ,UAC9BP,EAAWR,EAAMG,EAAKa,MAAO,UAzUxC,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAASD,EAAGC,OAAQC,EAAK,0BAA2BC,EAAc,aAAeD,EAAK,cAAgBA,EAAIE,EAA6BN,EAAMO,SAAS,6GAwC3LC,EAAoBL,EAAOM,QAC3BC,KAAM,SAAUC,EAASC,GACrBT,EAAOU,GAAGH,KAAKI,KAAKC,KAAMJ,EAASC,GACnCG,KAAKJ,QAAQK,SAAS,wDACtBD,KAAKE,cACLF,KAAKG,UACLH,KAAKI,UACLnB,EAAMoB,OAAOL,OAEjBM,UACAT,SACI7B,KAAM,oBACNuC,YAAY,EACZC,UAAU,EACVC,UACIC,SAAU,wBACVC,QAAS,0BACTC,KAAM,wBACNC,cAAe,WACfC,aAAc,UACdC,UAAW,OACXC,YAAa,WAGrBd,YAAa,WACT,GAAIe,GAAOjB,IACPiB,GAAKC,YAAcD,EAAKE,gBACxBF,EAAKC,WAAWE,OAAO,SAAUH,EAAKE,iBAAiBC,OAAO,QAASH,EAAKI,eAAeD,OAAO,WAAYH,EAAKK,mBAEnHL,EAAKI,cAAgBhE,EAAEkE,MAAMN,EAAKO,OAAQP,GAC1CA,EAAKE,gBAAkB9D,EAAEkE,MAAMN,EAAKb,QAASa,GAC7CA,EAAKK,iBAAmBjE,EAAEkE,MAAMN,EAAKQ,cAAeR,IAExDA,EAAKC,WAAajC,EAAMzB,KAAKkE,gBAAgBC,OAAOV,EAAKpB,QAAQqB,YACjED,EAAKC,WAAWU,KAAK,SAAUX,EAAKE,iBAAiBS,KAAK,QAASX,EAAKI,eAAeO,KAAK,WAAYX,EAAKK,mBAEjHO,cAAe,SAAUX,GACrBlB,KAAKH,QAAQqB,WAAaA,EAC1BlB,KAAKE,cACDF,KAAKU,UACLV,KAAKU,SAASmB,cAAcX,GAE5BlB,KAAKY,MACLZ,KAAKY,KAAKiB,cAAcX,GAExBlB,KAAKW,SACLX,KAAKW,QAAQkB,cAAcX,GAE/BlB,KAAKI,WAET0B,oBAAqB,WACjB,GAAIb,GAAOjB,IACX,OAAOf,GAAMzB,KAAKuE,uBAAuBJ,QACrCK,QACIC,OACIC,GAAI,aACJC,YAAa,SAAUC,GACnB,QAAS,uBAAyBA,IAAW,cAAgBA,OAIzEC,WACIC,KAAM,SAAUzC,GAAV,GACE0C,GACApE,EACAQ,CACAtB,GAAEmF,cAAc3C,EAAQrC,OACxB+E,EAAUtB,EAAKC,WAAWuB,mBAC1BF,EAAQG,KAAK,SAAUlF,GACdyD,EAAKC,WAAWyB,aACjBpF,EAAOC,GAEXqC,EAAQ+C,QAAQpF,KACjBqF,KAAKhD,EAAQiD,SAEhB3E,EAAO8C,EAAK8B,SAAS7B,WAAW8B,IAAInD,EAAQrC,KAAKS,YACzB,WAApBE,EAAKF,YACLU,GAAM,EACN4D,EAAUtB,EAAKC,WAAW+B,aAC1BV,EAAQG,KAAK,SAAUlF,GACnBqC,EAAQ+C,QAAQxE,EAAcZ,MAC/BqF,KAAKhD,EAAQiD,QACI,OAAb3E,EAAKP,OACZe,GAAM,EACNkB,EAAQ+C,QAAQhE,EAAiBT,KAEhCQ,IAEG4D,EADa,GAAbpE,EAAKP,KACKqD,EAAKC,WAAWgC,iBACnB/E,EAAKgF,oBACFlC,EAAKC,WAAWkC,aAAavD,EAAQrC,KAAKS,YAE1CgD,EAAKC,WAAWmC,kBAAkBxD,EAAQrC,KAAKS,YAE7DsE,EAAQG,KAAK7C,EAAQ+C,SAASC,KAAKhD,EAAQiD,cAOnEQ,UAAW,SAAUC,GACjBtE,EAAME,GAAGqE,SAASxD,KAAKJ,QAAS2D,IAEpC/B,OAAQ,WACJxB,KAAKsD,WAAU,IAEnB7B,cAAe,WACXzB,KAAKsD,WAAU,IAEnBnD,QAAS,WACLH,KAAKyD,KAAOpG,EAAE,wDAAwDqG,SAAS1D,KAAKJ,SACpFI,KAAK2D,UACL3D,KAAK4D,YAETD,QAAS,WAAA,GACDE,GAAYxG,EAAE,yFAA2F2C,KAAKH,QAAQY,SAASO,YAAc,cAAc0C,SAAS1D,KAAKyD,MACzKjE,EAAW,uQACfQ,MAAK+C,SAAW1F,EAAE,UAAUqG,SAASG,GAAWC,eAC5CtE,SAAUA,EACVuE,cAAe,UACfC,aAAa,EACbC,UAAU,EACV/C,WAAYlB,KAAK8B,sBACjBoC,UAAW,SAAUC,GACjB,GAAIC,GAAWpE,KAAKoE,SAASD,EAAEE,aAC1BD,EAASjC,aAAgBiC,EAASE,YAAeF,EAAS3F,UAA4B,GAAjB2F,EAASxG,MAAqC,WAAxBwG,EAASnG,YACrGkG,EAAEI,kBAGVC,KAAM,SAAUL,GAAV,GACEpF,GAAS,aACT0F,EAAUpG,EAAsB8F,EAAEO,WAClCD,IAAWA,EAAQE,SAAS3E,KAAKoE,SAASD,EAAEE,eAC5CtF,EAAS,YAEboF,EAAES,eAAe7F,IAErB8F,KAAM,SAAUV,GAAV,GAEEM,GACAtG,EACAT,EAAKC,EAAQ+C,EACb1C,CACJ,IALAmG,EAAEI,iBACEE,EAAUpG,EAAsB8F,EAAEO,YAClCvG,EAAO6B,KAAKoE,SAASD,EAAEE,YAGvBI,GAAWA,EAAQE,SAASxG,GAAO,CAEnC,GADAH,EAAOG,EAAKJ,kBAAoBI,EAAKF,WACnB,QAAdE,EAAKP,KAIL,IAHA8C,EAAW9B,EAAiBT,GAC5BR,EAAS+C,EAAS/C,OAClBK,KACKN,EAAM,EAAGA,EAAMC,EAAQD,IACxBM,EAAK8G,KAAK5G,EAAQwC,EAAShD,SAExBS,GAAKQ,MACZX,GAAQE,EAAQC,IAEpBsG,GAAQM,IAAI/G,OAGrBR,KAAK,kBAEZwH,cAAe,SAAUpF,EAASC,GAAnB,GACPL,GAAW,oCAAsCP,EAAMI,GAAK,mCAC5DmB,EAAWX,EAAQW,SACnByE,EAAQ,EAWZ,OAVIzE,KACAyE,GAAS,yBACTA,GAAS,mDACTA,GAAS,QAETpF,EAAQU,YAAcC,KACtByE,GAAS,sEAEbA,GAAS,0DACTzF,GAAY,iCAAmCyF,EAAQ,eAChD,GAAIhG,GAAME,GAAG+F,mBAAmBtF,EAASvC,EAAEqC,QAC9CwB,WAAYlB,KAAKkB,WACjBiE,KAAM,SAAUvF,GACZ,GAAIwF,GAAU/H,EAAE,sEAEhB,OADA+H,GAAQC,KAAK,WAAWC,OAAO1F,EAAQ2F,SAChCH,GAEX5F,SAAUA,EACVgG,cAAe,2CAChB3F,KAEP+D,SAAU,WAAA,GACFC,GAAYxG,EAAE,kCAAkCqG,SAAS1D,KAAKyD,MAC9DgC,EAAmBpI,EAAEkC,GACrBvB,KAAMgC,KAAKH,QAAQY,SAASK,aAC5B4E,KAAM,iBACNhC,SAASG,GACTlD,EAAUtD,EAAE,+DAA+DqG,SAAS+B,EAAiBE,QACrGC,EAAgBvI,EAAEkC,GAClBvB,KAAMgC,KAAKH,QAAQY,SAASM,UAC5B2E,KAAM,cACNhC,SAASG,GACTjD,EAAOvD,EAAE,+DAA+DqG,SAASkC,EAAcD,QAC/FE,EAAoBxI,EAAEkC,GACtBvB,KAAMgC,KAAKH,QAAQY,SAASI,cAC5B6E,KAAM,aACNhC,SAASG,GACTnD,EAAWrD,EAAE,+DAA+DqG,SAASmC,EAAkBF,QACvG9F,EAAUG,KAAKH,OACnBG,MAAKW,QAAUX,KAAKgF,cAAcrE,GAC9BJ,WAAYV,EAAQU,WACpBC,SAAUX,EAAQW,SAClBsF,YAAalF,EACbH,UACIsF,MAAOlG,EAAQY,SAASE,QACxBqF,UAAWnG,EAAQY,SAASuF,aAGpChG,KAAKY,KAAOZ,KAAKgF,cAAcpE,GAC3BL,WAAYV,EAAQU,WACpBC,SAAUX,EAAQW,SAClBiE,QAAS,OACTqB,YAAanF,EACbF,UACIsF,MAAO/F,KAAKH,QAAQY,SAASG,KAC7BoF,UAAWhG,KAAKH,QAAQY,SAASuF,aAGzChG,KAAKU,SAAWV,KAAKgF,cAActE,GAC/B+D,QAAS,WACThE,UAAYsF,MAAOlG,EAAQY,SAASC,YAExCC,EAAQoE,IAAInE,GAAMmE,IAAIrE,GAAUuF,GAAG3G,EAAa,wBAAyBU,KAAKkG,eAElFA,aAAc,SAAU/B,GACpB9G,EAAE8G,EAAEgC,eAAeC,YAAY,gBAA4B,eAAXjC,EAAEvG,OAEtDyI,QAAS,WAAA,GAGDC,GAAQC,EAFR3G,EAAUI,KAAKJ,QACf4G,EAASxG,KAAKH,QAAQ2G,OAEtBC,EAAcxH,EAAMyH,YACnBF,KAGL5G,EAAQ4G,OAAOA,GACX5G,EAAQ+G,GAAG,cACXJ,EAAS3G,EAAQgH,SAAS,cAAcA,SAAS,uBACjDJ,EAAS5G,EAAQiH,cACjBP,GAAUG,EAAY7G,GAAW4G,GAAU,EAC3CA,EAASA,GAAUC,EAAYF,GAAQ,GAAQA,EAAOC,UAAYF,EAClEC,EAAOC,OAAOA,MAGtBpG,QAAS,WACL,GAAIc,GAAalB,KAAKkB,YAClBA,EAAWyB,aAAe3C,KAAK8G,QAAU5F,EAAW6F,QAAU/G,KAAKgH,WAAa9F,EAAW+F,YAC3FjH,KAAK+C,SAAS7B,WAAWgG,QAE7BlH,KAAKgH,SAAWhH,KAAKkB,WAAW+F,UAChCjH,KAAK8G,MAAQ9G,KAAKkB,WAAW6F,OAC7B/G,KAAKqG,UACLrG,KAAKsD,WAAU,IAEnB6D,QAAS,WACL/H,EAAOU,GAAGqH,QAAQpH,KAAKC,MACvBA,KAAKkB,WAAWE,OAAO,SAAUpB,KAAKmB,iBACtCnB,KAAKyD,KAAK4B,KAAK,WAAW+B,IAAI/H,GAC9BW,KAAKY,KAAKuG,UACVnH,KAAKW,QAAQwG,UACbnH,KAAKU,SAASyG,UACdnH,KAAK+C,SAASoE,UACdnH,KAAKJ,QAAU,KACfI,KAAKmB,gBAAkB,OAuB/BhC,GAAGkI,OAAO5H,IACZP,OAAOD,MAAMqI,QACRpI,OAAOD,OACE,kBAAV7B,SAAwBA,OAAOmK,IAAMnK,OAAS,SAAUoK,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.pivot.configurator.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.pivot.configurator', ['kendo.dom'], f);\n}(function () {\n    var __meta__ = {\n        id: 'pivot.configurator',\n        name: 'PivotConfigurator',\n        category: 'web',\n        depends: [\n            'dropdownlist',\n            'treeview',\n            'pivot.fieldmenu'\n        ],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, Widget = ui.Widget, ns = '.kendoPivotConfigurator', HOVEREVENTS = 'mouseenter' + ns + ' mouseleave' + ns, SETTING_CONTAINER_TEMPLATE = kendo.template('<p class=\"k-reset\"><span class=\"k-icon #=icon#\"></span>${name}</p>' + '<div class=\"k-list-container k-reset\"/>');\n        function addKPI(data) {\n            var found;\n            var idx = 0;\n            var length = data.length;\n            for (; idx < length; idx++) {\n                if (data[idx].type == 2) {\n                    found = true;\n                    break;\n                }\n            }\n            if (found) {\n                data.splice(idx + 1, 0, {\n                    caption: 'KPIs',\n                    defaultHierarchy: '[KPIs]',\n                    name: 'KPIs',\n                    uniqueName: '[KPIs]'\n                });\n            }\n        }\n        function kpiNode(node) {\n            return {\n                name: node.uniqueName,\n                type: node.type\n            };\n        }\n        function normalizeKPIs(data) {\n            for (var idx = 0, length = data.length; idx < length; idx++) {\n                data[idx].uniqueName = data[idx].name;\n                data[idx].type = 'kpi';\n            }\n            return data;\n        }\n        function settingTargetFromNode(node) {\n            var target = $(node).closest('.k-pivot-setting');\n            if (target.length) {\n                return target.data('kendoPivotSettingTarget');\n            }\n            return null;\n        }\n        var PivotConfigurator = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this.element.addClass('k-widget k-fieldselector k-alt k-edit-form-container');\n                this._dataSource();\n                this._layout();\n                this.refresh();\n                kendo.notify(this);\n            },\n            events: [],\n            options: {\n                name: 'PivotConfigurator',\n                filterable: false,\n                sortable: false,\n                messages: {\n                    measures: 'Drop Data Fields Here',\n                    columns: 'Drop Column Fields Here',\n                    rows: 'Drop Rows Fields Here',\n                    measuresLabel: 'Measures',\n                    columnsLabel: 'Columns',\n                    rowsLabel: 'Rows',\n                    fieldsLabel: 'Fields'\n                }\n            },\n            _dataSource: function () {\n                var that = this;\n                if (that.dataSource && that._refreshHandler) {\n                    that.dataSource.unbind('change', that._refreshHandler).unbind('error', that._errorHandler).unbind('progress', that._progressHandler);\n                } else {\n                    that._errorHandler = $.proxy(that._error, that);\n                    that._refreshHandler = $.proxy(that.refresh, that);\n                    that._progressHandler = $.proxy(that._requestStart, that);\n                }\n                that.dataSource = kendo.data.PivotDataSource.create(that.options.dataSource);\n                that.dataSource.bind('change', that._refreshHandler).bind('error', that._errorHandler).bind('progress', that._progressHandler);\n            },\n            setDataSource: function (dataSource) {\n                this.options.dataSource = dataSource;\n                this._dataSource();\n                if (this.measures) {\n                    this.measures.setDataSource(dataSource);\n                }\n                if (this.rows) {\n                    this.rows.setDataSource(dataSource);\n                }\n                if (this.columns) {\n                    this.columns.setDataSource(dataSource);\n                }\n                this.refresh();\n            },\n            _treeViewDataSource: function () {\n                var that = this;\n                return kendo.data.HierarchicalDataSource.create({\n                    schema: {\n                        model: {\n                            id: 'uniqueName',\n                            hasChildren: function (item) {\n                                return !('hierarchyUniqueName' in item) && !('aggregator' in item);\n                            }\n                        }\n                    },\n                    transport: {\n                        read: function (options) {\n                            var promise;\n                            var node;\n                            var kpi;\n                            if ($.isEmptyObject(options.data)) {\n                                promise = that.dataSource.schemaDimensions();\n                                promise.done(function (data) {\n                                    if (!that.dataSource.cubeBuilder) {\n                                        addKPI(data);\n                                    }\n                                    options.success(data);\n                                }).fail(options.error);\n                            } else {\n                                node = that.treeView.dataSource.get(options.data.uniqueName);\n                                if (node.uniqueName === '[KPIs]') {\n                                    kpi = true;\n                                    promise = that.dataSource.schemaKPIs();\n                                    promise.done(function (data) {\n                                        options.success(normalizeKPIs(data));\n                                    }).fail(options.error);\n                                } else if (node.type == 'kpi') {\n                                    kpi = true;\n                                    options.success(buildKPImeasures(node));\n                                }\n                                if (!kpi) {\n                                    if (node.type == 2) {\n                                        promise = that.dataSource.schemaMeasures();\n                                    } else if (node.dimensionUniqueName) {\n                                        promise = that.dataSource.schemaLevels(options.data.uniqueName);\n                                    } else {\n                                        promise = that.dataSource.schemaHierarchies(options.data.uniqueName);\n                                    }\n                                    promise.done(options.success).fail(options.error);\n                                }\n                            }\n                        }\n                    }\n                });\n            },\n            _progress: function (toggle) {\n                kendo.ui.progress(this.element, toggle);\n            },\n            _error: function () {\n                this._progress(false);\n            },\n            _requestStart: function () {\n                this._progress(true);\n            },\n            _layout: function () {\n                this.form = $('<div class=\"k-columns k-state-default k-floatwrap\"/>').appendTo(this.element);\n                this._fields();\n                this._targets();\n            },\n            _fields: function () {\n                var container = $('<div class=\"k-state-default\"><p class=\"k-reset\"><span class=\"k-icon k-i-group\"></span>' + this.options.messages.fieldsLabel + '</p></div>').appendTo(this.form);\n                var template = '# if (item.type == 2 || item.uniqueName == \"[KPIs]\") { #' + '<span class=\"k-icon k-i-#= (item.type == 2 ? \"sum\" : \"kpi\") #\"></span>' + '# } else if (item.type && item.type !== \"kpi\") { #' + '<span class=\"k-icon k-i-arrows-dimensions\"></span>' + '# } #' + '#: item.caption || item.name #';\n                this.treeView = $('<div/>').appendTo(container).kendoTreeView({\n                    template: template,\n                    dataTextField: 'caption',\n                    dragAndDrop: true,\n                    autoBind: false,\n                    dataSource: this._treeViewDataSource(),\n                    dragstart: function (e) {\n                        var dataItem = this.dataItem(e.sourceNode);\n                        if (!dataItem.hasChildren && !dataItem.aggregator && !dataItem.measure || dataItem.type == 2 || dataItem.uniqueName === '[KPIs]') {\n                            e.preventDefault();\n                        }\n                    },\n                    drag: function (e) {\n                        var status = 'k-i-cancel';\n                        var setting = settingTargetFromNode(e.dropTarget);\n                        if (setting && setting.validate(this.dataItem(e.sourceNode))) {\n                            status = 'k-i-plus';\n                        }\n                        e.setStatusClass(status);\n                    },\n                    drop: function (e) {\n                        e.preventDefault();\n                        var setting = settingTargetFromNode(e.dropTarget);\n                        var node = this.dataItem(e.sourceNode);\n                        var idx, length, measures;\n                        var name;\n                        if (setting && setting.validate(node)) {\n                            name = node.defaultHierarchy || node.uniqueName;\n                            if (node.type === 'kpi') {\n                                measures = buildKPImeasures(node);\n                                length = measures.length;\n                                name = [];\n                                for (idx = 0; idx < length; idx++) {\n                                    name.push(kpiNode(measures[idx]));\n                                }\n                            } else if (node.kpi) {\n                                name = [kpiNode(node)];\n                            }\n                            setting.add(name);\n                        }\n                    }\n                }).data('kendoTreeView');\n            },\n            _createTarget: function (element, options) {\n                var template = '<li class=\"k-item k-header\" data-' + kendo.ns + 'name=\"${data.name}\">${data.name}';\n                var sortable = options.sortable;\n                var icons = '';\n                if (sortable) {\n                    icons += '#if (data.sortIcon) {#';\n                    icons += '<span class=\"k-icon ${data.sortIcon}-sm\"></span>';\n                    icons += '#}#';\n                }\n                if (options.filterable || sortable) {\n                    icons += '<span class=\"k-icon k-i-more-vertical k-setting-fieldmenu\"></span>';\n                }\n                icons += '<span class=\"k-icon k-i-close k-setting-delete\"></span>';\n                template += '<span class=\"k-field-actions\">' + icons + '</span></li>';\n                return new kendo.ui.PivotSettingTarget(element, $.extend({\n                    dataSource: this.dataSource,\n                    hint: function (element) {\n                        var wrapper = $('<div class=\"k-fieldselector\"><ul class=\"k-list k-reset\"></ul></div>');\n                        wrapper.find('.k-list').append(element.clone());\n                        return wrapper;\n                    },\n                    template: template,\n                    emptyTemplate: '<li class=\"k-item k-empty\">${data}</li>'\n                }, options));\n            },\n            _targets: function () {\n                var container = $('<div class=\"k-state-default\"/>').appendTo(this.form);\n                var columnsContainer = $(SETTING_CONTAINER_TEMPLATE({\n                    name: this.options.messages.columnsLabel,\n                    icon: 'k-i-columns'\n                })).appendTo(container);\n                var columns = $('<ul class=\"k-pivot-configurator-settings k-list k-reset\" />').appendTo(columnsContainer.last());\n                var rowsContainer = $(SETTING_CONTAINER_TEMPLATE({\n                    name: this.options.messages.rowsLabel,\n                    icon: 'k-i-rows'\n                })).appendTo(container);\n                var rows = $('<ul class=\"k-pivot-configurator-settings k-list k-reset\" />').appendTo(rowsContainer.last());\n                var measuresContainer = $(SETTING_CONTAINER_TEMPLATE({\n                    name: this.options.messages.measuresLabel,\n                    icon: 'k-i-sum'\n                })).appendTo(container);\n                var measures = $('<ul class=\"k-pivot-configurator-settings k-list k-reset\" />').appendTo(measuresContainer.last());\n                var options = this.options;\n                this.columns = this._createTarget(columns, {\n                    filterable: options.filterable,\n                    sortable: options.sortable,\n                    connectWith: rows,\n                    messages: {\n                        empty: options.messages.columns,\n                        fieldMenu: options.messages.fieldMenu\n                    }\n                });\n                this.rows = this._createTarget(rows, {\n                    filterable: options.filterable,\n                    sortable: options.sortable,\n                    setting: 'rows',\n                    connectWith: columns,\n                    messages: {\n                        empty: this.options.messages.rows,\n                        fieldMenu: this.options.messages.fieldMenu\n                    }\n                });\n                this.measures = this._createTarget(measures, {\n                    setting: 'measures',\n                    messages: { empty: options.messages.measures }\n                });\n                columns.add(rows).add(measures).on(HOVEREVENTS, '.k-item:not(.k-empty)', this._toggleHover);\n            },\n            _toggleHover: function (e) {\n                $(e.currentTarget).toggleClass('k-state-hover', e.type === 'mouseenter');\n            },\n            _resize: function () {\n                var element = this.element;\n                var height = this.options.height;\n                var border, fields;\n                var outerHeight = kendo._outerHeight;\n                if (!height) {\n                    return;\n                }\n                element.height(height);\n                if (element.is(':visible')) {\n                    fields = element.children('.k-columns').children('div.k-state-default');\n                    height = element.innerHeight();\n                    border = (outerHeight(element) - height) / 2;\n                    height = height - (outerHeight(fields, true) - fields.height()) - border;\n                    fields.height(height);\n                }\n            },\n            refresh: function () {\n                var dataSource = this.dataSource;\n                if (dataSource.cubeBuilder || this._cube !== dataSource.cube() || this._catalog !== dataSource.catalog()) {\n                    this.treeView.dataSource.fetch();\n                }\n                this._catalog = this.dataSource.catalog();\n                this._cube = this.dataSource.cube();\n                this._resize();\n                this._progress(false);\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.dataSource.unbind('change', this._refreshHandler);\n                this.form.find('.k-list').off(ns);\n                this.rows.destroy();\n                this.columns.destroy();\n                this.measures.destroy();\n                this.treeView.destroy();\n                this.element = null;\n                this._refreshHandler = null;\n            }\n        });\n        function kpiMeasure(name, measure, type) {\n            return {\n                hierarchyUniqueName: name,\n                uniqueName: measure,\n                caption: measure,\n                measure: measure,\n                name: measure,\n                type: type,\n                kpi: true\n            };\n        }\n        function buildKPImeasures(node) {\n            var name = node.name;\n            return [\n                kpiMeasure(name, node.value, 'value'),\n                kpiMeasure(name, node.goal, 'goal'),\n                kpiMeasure(name, node.status, 'status'),\n                kpiMeasure(name, node.trend, 'trend')\n            ];\n        }\n        ui.plugin(PivotConfigurator);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}