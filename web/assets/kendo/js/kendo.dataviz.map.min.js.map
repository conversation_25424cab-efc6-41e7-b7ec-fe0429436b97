{"version": 3, "sources": ["kendo.dataviz.map.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "sqr", "renderSize", "renderPos", "pos", "result", "toHyphens", "split", "arabicToRoman", "n", "literals", "1", "10", "100", "2", "20", "200", "3", "30", "300", "4", "40", "400", "5", "50", "500", "6", "60", "600", "7", "70", "700", "8", "80", "800", "9", "90", "900", "1000", "values", "roman", "shift", "romanToArabic", "r", "digits", "prev", "v", "toLowerCase", "x", "l", "c", "d", "m", "char<PERSON>t", "memoize", "cache", "Object", "create", "id", "arguments", "apply", "isUnicodeLetter", "ch", "RX_UNICODE_LETTER", "test", "withExit", "obj", "Return", "call", "ex", "now", "Date", "getTime", "RegExp", "undefined", "Extent", "math", "Math", "abs", "atan", "atan2", "cos", "max", "min", "sin", "tan", "dataviz", "drawing", "defined", "deg", "rad", "round", "valueOrDefault", "Location", "lat", "lng", "DISTANCE_ITERATIONS", "DISTANCE_CONVERGENCE", "DISTANCE_PRECISION", "FORMAT", "toArray", "equals", "loc", "clone", "precision", "wrap", "distanceTo", "dest", "datum", "greatCircleTo", "distance", "destination", "bearing", "fromLat", "fromLng", "dToR", "datums", "WGS84", "a", "asin", "b", "L", "U1", "sinU1", "cosU1", "U2", "sinU2", "cosU2", "lambda", "prevLambda", "converged", "sinLambda", "cosLambda", "sino", "cosA2", "coso", "cos2om", "sigma", "sinA", "C", "u2", "A", "B", "deltao", "azimuthFrom", "azimuthTo", "sqrt", "fn", "toString", "format", "fromLngLat", "ll", "fromLatLng", "nw", "se", "contains", "center", "containsAny", "locs", "include", "includeAll", "edges", "ne", "sw", "overlaps", "extent", "World", "Widget", "ui", "template", "Attribution", "element", "_initOptions", "items", "addClass", "name", "separator", "itemTemplate", "filter", "zoom", "_extent", "_zoom", "_render", "add", "item", "remove", "clear", "empty", "_itemText", "append", "show", "hide", "inZoomLevel", "_inZoomLevel", "minZoom", "max<PERSON><PERSON>", "inArea", "_inArea", "Number", "MAX_VALUE", "area", "plugin", "button", "dir", "keys", "proxy", "NS", "BUTTONS", "Navigator", "on", "parentElement", "parent", "closest", "attr", "_keyroot", "_tabindex", "_keydown", "panStep", "events", "dispose", "off", "_pan", "y", "trigger", "_click", "e", "currentTarget", "is", "preventDefault", "which", "UP", "DOWN", "RIGHT", "LEFT", "iconClass", "PLUS", "MINUS", "FF_PLUS", "FF_MINUS", "ZoomControl", "zoomStep", "_change", "delta", "NUMPAD_PLUS", "NUMPAD_MINUS", "exp", "pow", "log", "g", "geometry", "Point", "limit", "limitValue", "PI", "PI_DIV_2", "PI_DIV_4", "DEG_TO_RAD", "Mercator", "MAX_LNG", "MAX_LAT", "INVERSE_ITERATIONS", "INVERSE_CONVERGENCE", "centralMeridian", "forward", "clamp", "proj", "lng0", "_projectLat", "ecc", "ts", "con", "p", "inverse", "point", "_inverseY", "dphi", "ecch", "phi", "SphericalMercator", "Equirectangular", "EPSG3857", "crs", "_proj", "_tm", "transform", "translate", "scale", "_itm", "toPoint", "toLocation", "EPSG3395", "EPSG4326", "projections", "Layer", "css", "zIndex", "opacity", "appendTo", "scrollElement", "_before<PERSON><PERSON>t", "_reset", "_resize", "_panEnd", "_activate", "_updateAttribution", "destroy", "_deactivate", "reset", "_applyExtent", "_setVisibility", "noop", "matchMinZoom", "matchMaxZoom", "inside", "visible", "bind", "unbind", "attribution", "layers", "DataSource", "data", "Group", "last", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "surface", "Surface", "_initRoot", "movable", "Movable", "_markers", "_handler", "_mouseenter", "_mouseleave", "_initDataSource", "autoBind", "dataSource", "_dataChange", "setDataSource", "fetch", "_translateSurface", "_data", "_load", "_root", "draw", "dsOptions", "sender", "view", "container", "shape", "_clearMarkers", "_loader", "GeoJSONLoader", "parse", "shapeCreated", "args", "cancelled", "Circle", "_createMarker", "layer", "featureCreated", "markers", "location", "dataItem", "_panning", "suspendTracking", "resumeTracking", "locationToView", "moveTo", "event", "originalEvent", "locator", "defaultStyle", "observer", "root", "unwrap", "type", "_loadGeometryTo", "_featureCreated", "children", "_shapeCreated", "group", "properties", "path", "coords", "coordinates", "_loadPolygon", "_setLineFill", "_loadPoint", "segments", "anchor", "fill", "_loadShape", "rings", "_buildPolygon", "j", "MultiPath", "Path", "lineTo", "circle", "schemas", "g<PERSON><PERSON><PERSON>", "features", "geometries", "transports", "read", "dataType", "getter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "locationField", "valueField", "minSize", "maxSize", "symbol", "opt", "getValue", "scaleType", "slice", "_scaleType", "_createSymbol", "_drawSymbol", "isFunction", "scales", "symbols", "SqrtScale", "domain", "range", "domainRange", "outputRange", "_domain", "_range", "_ratio", "rel", "Symbols", "geo", "square", "halfSize", "close", "bubble", "roundPoint", "drawingUtil", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subdomains", "viewType", "_viewType", "_view", "origin", "locationToLayer", "view<PERSON><PERSON>in", "_updateView", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "support", "mobileOS", "throttle", "extentToPoint", "render", "pool", "TilePool", "tileSize", "urlTemplate", "_center", "_view<PERSON><PERSON><PERSON>", "pointToTileIndex", "floor", "tileCount", "firstTileIndex", "indexToPoint", "ceil", "diff", "index", "subdomainText", "subdomainIndex", "tile", "createTile", "currentIndex", "tileOptions", "wrapIndex", "offset", "subdomain", "errorUrlTemplate", "boundary", "wrapValue", "remainder", "ImageTile", "errorUrl", "target", "setAttribute", "removeAttribute", "url", "top", "left", "visibility", "urlResult", "urlOptions", "z", "s", "quadkey", "q", "culture", "_items", "_remove", "_create", "dist", "maxDist", "splice", "<PERSON><PERSON><PERSON><PERSON>", "baseUrl", "_scheme", "_onMetadata", "_fetchMetadata", "imagerySet", "Error", "ajax", "output", "uriScheme", "jsonp", "success", "proto", "protocol", "resource", "resourceSets", "resources", "imageUrl", "imageUrlSubdomains", "zoomMin", "zoomMax", "_addAttribution", "<PERSON><PERSON><PERSON><PERSON>", "imageryProviders", "coverageAreas", "bbox", "tileQuadKey", "digit", "mask", "quadKey", "bing", "doc", "indexOf", "inArray", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_markerClick", "titleField", "arg", "isArray", "_addOne", "update", "showAt", "<PERSON><PERSON>", "addTo", "getLocation", "getTitle", "title", "tooltip", "popup", "_position", "attributes", "renderTooltip", "contentTemplate", "content", "contentUrl", "defaults", "CSS_PREFIX", "FRICTION", "FRICTION_MOBILE", "MOUSEWHEEL", "VELOCITY_MULTIPLIER", "DEFAULT_ZOOM_RATE", "Map", "_get<PERSON><PERSON>in", "_initScroller", "_initMarkers", "_initControls", "_initLayers", "_mousewheel", "controls", "navigator", "layerDefaults", "color", "stroke", "position", "markerDefaults", "wraparound", "scroller", "zoomControl", "level", "_setExtent", "_getExtent", "setOptions", "_layerSize", "layerToLocation", "translateWith", "viewToLocation", "eventOffset", "field", "pageX", "clientX", "pageY", "clientY", "eventToView", "cursor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eventToLocation", "viewSize", "exportVisual", "_set<PERSON><PERSON><PERSON>", "topLeft", "_origin", "invalidate", "bottomRight", "layerWidth", "layerHeight", "raw", "_zoomAround", "pivot", "_createAttribution", "_createNavigator", "_createZoomControl", "_createControlElement", "defaultPos", "posSelector", "_navigator<PERSON>an", "_navigatorCenter", "scrollLeft", "scrollTop", "bounds", "_virtualSize", "one", "_scrollEnd", "scrollTo", "_zoomControlChange", "friction", "zoomable", "mobile", "<PERSON><PERSON><PERSON>", "velocityMultiplier", "mousewheelScrolling", "supportDoubleTap", "_scroll", "userEvents", "_scaleStart", "_scale", "_doubleTap", "_tap", "impl", "defs", "_scrollOffset", "_panComplete", "_panEndTS", "touch", "touches", "cancel", "_scaleToZoom", "gestureCenter", "centerLocation", "centerPoint", "originPoint", "scaleDelta", "tiles", "_resetScroller", "xBounds", "yBounds", "dimensions", "forcedMinScale", "maxScale", "pannable", "makeVirtual", "virtualSize", "_renderLayers", "scrollWrap", "toZoom", "postZoom", "fromZoom", "mwDelta"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,aAAc,cAAeD,IACtC,WAwKE,MAvKC,YAEG,QAASoF,GAAIrC,GACT,MAAOA,GAAQA,EAQnB,QAASsC,GAAW5C,GAIhB,MAHoB,gBAATA,KACPA,GAAQ,MAELA,EAEX,QAAS6C,GAAUC,GAAnB,GAGY3E,GACKM,EAHTsE,IACJ,IAAID,EAEA,IADI3E,EAAQwB,EAAMqD,UAAUF,GAAKG,MAAM,KAC9BxE,EAAI,EAAGA,EAAIN,EAAMQ,OAAQF,IAC9BsE,EAAO3E,KAAK,SAAWD,EAAMM,GAGrC,OAAOsE,GAAOzE,KAAK,KAEvB,QAAS4E,GAAcC,GA8DnB,IA9DJ,GACQC,IACAC,EAAG,IACHC,GAAI,IACJC,IAAK,IACLC,EAAG,KACHC,GAAI,KACJC,IAAK,KACLC,EAAG,MACHC,GAAI,MACJC,IAAK,MACLC,EAAG,KACHC,GAAI,KACJC,IAAK,KACLC,EAAG,IACHC,GAAI,IACJC,IAAK,IACLC,EAAG,KACHC,GAAI,KACJC,IAAK,KACLC,EAAG,MACHC,GAAI,MACJC,IAAK,MACLC,EAAG,OACHC,GAAI,OACJC,IAAK,OACLC,EAAG,KACHC,GAAI,KACJC,IAAK,KACLC,IAAM,KAENC,GACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GAEAC,EAAQ,GACL/B,EAAI,GACHA,EAAI8B,EAAO,GACXA,EAAOE,SAEPD,GAAS9B,EAAS6B,EAAO,IACzB9B,GAAK8B,EAAO,GAGpB,OAAOC,GAEX,QAASE,GAAcC,GAAvB,GAEQC,GASAhF,EAAWiF,EACN9G,EACD+G,CADR,KAXAH,EAAIA,EAAEI,cACFH,GACA7G,EAAG,EACH+G,EAAG,EACHE,EAAG,GACHC,EAAG,GACHC,EAAG,IACHC,EAAG,IACHC,EAAG,KAEHxF,EAAQ,EAAGiF,EAAO,EACb9G,EAAI,EAAGA,EAAI4G,EAAE1G,SAAUF,EAAG,CAE/B,GADI+G,EAAIF,EAAOD,EAAEU,OAAOtH,KACnB+G,EACD,MAAO,KAEXlF,IAASkF,EACLA,EAAID,IACJjF,GAAS,EAAIiF,GAEjBA,EAAOC,EAEX,MAAOlF,GAEX,QAAS0F,GAAQzI,GACb,GAAI0I,GAAQC,OAAOC,OAAO,KAC1B,OAAO,YAAA,GAEM1H,GADL2H,EAAK,EACT,KAAS3H,EAAI4H,UAAU1H,SAAUF,GAAK,GAClC2H,GAAM,IAAMC,UAAU5H,EAE1B,OAAO2H,KAAMH,GAAQA,EAAMG,GAAMH,EAAMG,GAAM7I,EAAE+I,MAAMrG,KAAMoG,YAGnE,QAASE,GAAgBC,GACrB,MAAOC,GAAkBC,KAAKF,GAElC,QAASG,GAASpJ,EAAGqJ,GAWjB,QAASC,GAAOvG,GACZL,KAAKK,MAAQA,EAXjB,IACI,MAAO/C,GAAEuJ,KAAKF,EAAK,SAAUtG,GACzB,KAAM,IAAIuG,GAAOvG,KAEvB,MAAOyG,GACL,GAAIA,YAAcF,GACd,MAAOE,GAAGzG,KAEd,MAAMyG,IAlJjB,GAqKON,GApKA9G,EAAQD,OAAOC,MAAO0C,EAAa1C,EAAM0C,WAIzC2E,EAAMC,KAAKD,GACVA,KACDA,EAAM,WACF,OAAO,GAAIC,OAAOC,YAgJ1B7E,EAAW1C,GACPC,MACIoH,IAAKA,EACLnE,UAAWA,EACXD,WAAYA,EACZD,IAAKA,EACLyC,cAAeA,EACflC,cAAeA,EACf8C,QAASA,EACTO,gBAAiBA,EACjBI,SAAUA,KAGdF,EAAwBU,OAAO,y2JAEhCzH,OAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,wBACH,gBACA,aACDD,IACL,YACG,SAAUE,EAAG2J,GAAb,GAoIOC,GAnIAC,EAAOC,KAAMC,EAAMF,EAAKE,IAAKC,EAAOH,EAAKG,KAAMC,EAAQJ,EAAKI,MAAOC,EAAML,EAAKK,IAAKC,EAAMN,EAAKM,IAAKC,EAAMP,EAAKO,IAAKC,EAAMR,EAAKQ,IAAKC,EAAMT,EAAKS,IAAKpI,EAAQD,OAAOC,MAAOE,EAAQF,EAAME,MAAOmI,EAAUrI,EAAMqI,QAAS3F,EAAa1C,EAAM0C,WAAYzC,EAAOD,EAAMsI,QAAQrI,KAAMsI,EAAUtI,EAAKsI,QAASC,EAAMvI,EAAKuI,IAAKC,EAAMxI,EAAKwI,IAAKC,EAAQzI,EAAKyI,MAAOC,EAAiB1I,EAAK0I,eAAgB3F,EAAMhD,EAAMC,KAAK+C,IACpZ4F,EAAW1I,EAAMC,QACjBC,KAAM,SAAUyI,EAAKC,GACQ,IAArBpC,UAAU1H,QACVsB,KAAKuI,IAAMA,EAAI,GACfvI,KAAKwI,IAAMD,EAAI,KAEfvI,KAAKuI,IAAMA,EACXvI,KAAKwI,IAAMA,IAGnBC,oBAAqB,IACrBC,qBAAsB,MACtBC,mBAAoB,EACpBC,OAAQ,gBACRC,QAAS,WACL,OACI7I,KAAKuI,IACLvI,KAAKwI,MAGbM,OAAQ,SAAUC,GACd,MAAOA,IAAOA,EAAIR,MAAQvI,KAAKuI,KAAOQ,EAAIP,MAAQxI,KAAKwI,KAE3DQ,MAAO,WACH,MAAO,IAAIV,GAAStI,KAAKuI,IAAKvI,KAAKwI,MAEvCJ,MAAO,SAAUa,GAGb,MAFAjJ,MAAKwI,IAAMJ,EAAMpI,KAAKwI,IAAKS,GAC3BjJ,KAAKuI,IAAMH,EAAMpI,KAAKuI,IAAKU,GACpBjJ,MAEXkJ,KAAM,WAGF,MAFAlJ,MAAKwI,IAAMxI,KAAKwI,IAAM,IACtBxI,KAAKuI,IAAMvI,KAAKuI,IAAM,GACfvI,MAEXmJ,WAAY,SAAUC,EAAMC,GACxB,MAAOrJ,MAAKsJ,cAAcF,EAAMC,GAAOE,UAE3CC,YAAa,SAAUD,EAAUE,EAASJ,GAA7B,GAGLK,GACAC,EACAC,EACArB,EACAC,CACJ,OAPAiB,GAAUtB,EAAIsB,GACdJ,EAAQA,GAAStB,EAAQzH,IAAIuJ,OAAOC,MAChCJ,EAAUvB,EAAInI,KAAKuI,KACnBoB,EAAUxB,EAAInI,KAAKwI,KACnBoB,EAAOL,EAAW7J,EAAMqI,QAAQzH,IAAIuJ,OAAOC,MAAMC,EACjDxB,EAAMlB,EAAK2C,KAAKnC,EAAI6B,GAAWhC,EAAIkC,GAAQlC,EAAIgC,GAAW7B,EAAI+B,GAAQlC,EAAI+B,IAC1EjB,EAAMmB,EAAUlC,EAAMI,EAAI4B,GAAW5B,EAAI+B,GAAQlC,EAAIgC,GAAUhC,EAAIkC,GAAQ/B,EAAI6B,GAAW7B,EAAIU,IAC3F,GAAID,GAASJ,EAAIK,GAAML,EAAIM,KAEtCc,cAAe,SAAUF,EAAMC,GAAhB,GAUPU,GACAE,EACA3M,EACA4M,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAlM,EACAmM,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAOIC,EAOAC,EAIJC,EACAC,EACAC,EACAC,EACAC,EACAC,CAlDJ,IAFAtC,EAAOd,EAASpC,OAAOkD,GACvBC,EAAQA,GAAStB,EAAQzH,IAAIuJ,OAAOC,OAC/BV,GAAQpJ,KAAKgJ,QAAQZ,MAAM,GAAGU,OAAOM,EAAKJ,QAAQZ,MAAM,IACzD,OACImB,SAAU,EACVkC,YAAa,EACbC,UAAW,EAwBnB,KArBI3B,EAAIV,EAAMU,EACVE,EAAIZ,EAAMY,EACV3M,EAAI+L,EAAM/L,EACV4M,EAAI/B,EAAIiB,EAAKZ,IAAMxI,KAAKwI,KACxB2B,EAAK3C,GAAM,EAAIlK,GAAKwK,EAAIK,EAAInI,KAAKuI,OACjC6B,EAAQvC,EAAIsC,GACZE,EAAQ3C,EAAIyC,GACZG,EAAK9C,GAAM,EAAIlK,GAAKwK,EAAIK,EAAIiB,EAAKb,OACjCgC,EAAQ1C,EAAIyC,GACZE,EAAQ9C,EAAI4C,GACZG,EAASP,EAET1L,EAAIwB,KAAKyI,oBACTkC,GAAY,GAQRA,GAAanM,KAAM,GACvBoM,EAAY/C,EAAI4C,GAChBI,EAAYnD,EAAI+C,GAChBK,EAAOzD,EAAKsE,KAAKjJ,EAAI8H,EAAQI,GAAalI,EAAI2H,EAAQE,EAAQH,EAAQI,EAAQK,IAC9EG,EAAOZ,EAAQG,EAAQF,EAAQG,EAAQK,EACvCK,EAAQzD,EAAMqD,EAAME,GAChBG,EAAOd,EAAQG,EAAQI,EAAYE,EACvCC,EAAQ,EAAIrI,EAAIyI,GAChBF,EAAS,EACK,IAAVF,IACAE,EAASD,EAAO,EAAIZ,EAAQG,EAAQQ,GAExCL,EAAaD,EACTW,EAAI9N,EAAI,GAAKyN,GAAS,EAAIzN,GAAK,EAAI,EAAIyN,IAC3CN,EAASP,GAAK,EAAIkB,GAAK9N,EAAI6N,GAAQD,EAAQE,EAAIN,GAAQG,EAASG,EAAIJ,MAAa,EAAItI,EAAIuI,MACzFN,EAAYpD,EAAIkD,EAASC,IAAe1K,KAAK0I,oBAQjD,OANI2C,GAAKN,GAASrI,EAAIqH,GAAKrH,EAAIuH,IAAMvH,EAAIuH,GACrCqB,EAAI,EAAID,EAAK,OAAS,KAAOA,QAAaA,GAAM,IAAM,IAAMA,KAC5DE,EAAIF,EAAK,MAAQ,IAAMA,QAAaA,GAAM,GAAK,GAAKA,KACpDG,EAASD,EAAIT,GAAQG,EAASM,EAAI,GAAKP,MAAa,EAAItI,EAAIuI,IAAWM,EAAI,EAAIN,MAAe,EAAIvI,EAAIoI,QAAe,EAAIpI,EAAIuI,MAC7HQ,EAAchE,EAAM+C,EAAQI,EAAWP,EAAQE,EAAQH,EAAQI,EAAQK,GACvEa,EAAYjE,EAAM4C,EAAQO,GAAYR,EAAQI,EAAQH,EAAQE,EAAQM,IAEtEtB,SAAUnB,EAAM6B,EAAIqB,GAAKJ,EAAQM,GAASxL,KAAK2I,oBAC/C8C,YAAavD,EAAIuD,GACjBC,UAAWxD,EAAIwD,MAI3BpD,GAASsD,GAAGC,SAAW,WACnB,MAAOnM,GAAMoM,OAAO9L,KAAK4I,OAAQ5I,KAAKuI,IAAKvI,KAAKwI,MAEpDF,EAASyD,WAAa,SAAUC,GAC5B,MAAO,IAAI1D,GAAS0D,EAAG,GAAIA,EAAG,KAElC1D,EAAS2D,WAAa,SAAUD,GAC5B,MAAO,IAAI1D,GAAS0D,EAAG,GAAIA,EAAG,KAElC1D,EAASpC,OAAS,SAAU6D,EAAGE,GAC3B,GAAIhC,EAAQ8B,GACR,MAAIA,aAAazB,GACNyB,EAAEf,QACmB,IAArB5C,UAAU1H,QAA6B,IAAbqL,EAAErL,OAC5B4J,EAAS2D,WAAWlC,GAEpB,GAAIzB,GAASyB,EAAGE,IAI/B7C,EAASxH,EAAMC,QACfC,KAAM,SAAUoM,EAAIC,GAChBD,EAAK5D,EAASpC,OAAOgG,GACrBC,EAAK7D,EAASpC,OAAOiG,GACjBD,EAAG1D,IAAM,IAAM2D,EAAG3D,IAAM,KAAO0D,EAAG3D,IAAM,GAAK4D,EAAG5D,IAAM,IACtDvI,KAAKmM,GAAKD,EACVlM,KAAKkM,GAAKC,IAEVnM,KAAKmM,GAAKA,EACVnM,KAAKkM,GAAKA,IAGlBE,SAAU,SAAUrD,GAChB,GAAImD,GAAKlM,KAAKkM,GAAIC,EAAKnM,KAAKmM,GAAI3D,EAAMH,EAAeU,EAAIP,IAAKO,EAAI,IAAKR,EAAMF,EAAeU,EAAIR,IAAKQ,EAAI,GACzG,OAAOA,IAAOP,EAAM,KAAO0D,EAAG1D,IAAM,KAAOA,EAAM,KAAO2D,EAAG3D,IAAM,KAAOD,EAAM,IAAM4D,EAAG5D,IAAM,IAAMA,EAAM,IAAM2D,EAAG3D,IAAM,IAE5H8D,OAAQ,WAAA,GACAH,GAAKlM,KAAKkM,GACVC,EAAKnM,KAAKmM,GACV3D,EAAM0D,EAAG1D,KAAO2D,EAAG3D,IAAM0D,EAAG1D,KAAO,EACnCD,EAAM2D,EAAG3D,KAAO4D,EAAG5D,IAAM2D,EAAG3D,KAAO,CACvC,OAAO,IAAID,GAASC,EAAKC,IAE7B8D,YAAa,SAAUC,GAAV,GAEA/N,GADLsE,GAAS,CACb,KAAStE,EAAI,EAAGA,EAAI+N,EAAK7N,OAAQF,IAC7BsE,EAASA,GAAU9C,KAAKoM,SAASG,EAAK/N,GAE1C,OAAOsE,IAEX0J,QAAS,SAAUzD,GACf,GAAImD,GAAKlM,KAAKkM,GAAIC,EAAKnM,KAAKmM,GAAI3D,EAAMH,EAAeU,EAAIP,IAAKO,EAAI,IAAKR,EAAMF,EAAeU,EAAIR,IAAKQ,EAAI,GACzGmD,GAAG1D,IAAMZ,EAAIsE,EAAG1D,IAAKA,GACrB0D,EAAG3D,IAAMZ,EAAIuE,EAAG3D,IAAKA,GACrB4D,EAAG3D,IAAMb,EAAIwE,EAAG3D,IAAKA,GACrB2D,EAAG5D,IAAMX,EAAIuE,EAAG5D,IAAKA,IAEzBkE,WAAY,SAAUF,GAClB,IAAK,GAAI/N,GAAI,EAAGA,EAAI+N,EAAK7N,OAAQF,IAC7BwB,KAAKwM,QAAQD,EAAK/N,KAG1BkO,MAAO,WACH,GAAIR,GAAKlM,KAAKkM,GAAIC,EAAKnM,KAAKmM,EAC5B,QACID,GAAIlM,KAAKkM,GACTS,GAAI,GAAIrE,GAAS4D,EAAG3D,IAAK4D,EAAG3D,KAC5B2D,GAAInM,KAAKmM,GACTS,GAAI,GAAItE,GAAS6D,EAAG5D,IAAK2D,EAAG1D,OAGpCK,QAAS,WACL,GAAIqD,GAAKlM,KAAKkM,GAAIC,EAAKnM,KAAKmM,EAC5B,QACID,EACA,GAAI5D,GAAS4D,EAAG3D,IAAK4D,EAAG3D,KACxB2D,EACA,GAAI7D,GAAS6D,EAAG5D,IAAK2D,EAAG1D,OAGhCqE,SAAU,SAAUC,GAChB,MAAO9M,MAAKsM,YAAYQ,EAAOjE,YAAciE,EAAOR,YAAYtM,KAAK6I,cAG7EzB,EAAO2F,MAAQ,GAAI3F,IACf,cAIA,MAEJA,EAAOlB,OAAS,SAAU6D,EAAGE,GACzB,MAAIF,aAAa3C,GACN2C,EACAA,GAAKE,EACL,GAAI7C,GAAO2C,EAAGE,GACdF,GAAkB,IAAbA,EAAErL,SAAiBuL,EACxB,GAAI7C,IACP2C,EAAE,GACFA,EAAE,KAEFA,EAAE,GACFA,EAAE,KANH,GAUX3H,EAAW2F,GACPzH,KACI8G,OAAQA,EACRkB,SAAUA,MAGpB7I,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,2BAA4B,iBAAkBD,IACvD,YACG,WAAA,GACOoC,GAAQD,OAAOC,MAAOsN,EAAStN,EAAMuN,GAAGD,OAAQE,EAAWxN,EAAMwN,SAAUvN,EAAOD,EAAMsI,QAAQrI,KAAM0I,EAAiB1I,EAAK0I,eAAgBJ,EAAUtI,EAAKsI,QAC3JkF,EAAcH,EAAOnN,QACrBC,KAAM,SAAUsN,EAASnM,GACrB+L,EAAOpB,GAAG9L,KAAK+G,KAAK7G,KAAMoN,EAASnM,GACnCjB,KAAKqN,aAAapM,GAClBjB,KAAKsN,SACLtN,KAAKoN,QAAQG,SAAS,2BAE1BtM,SACIuM,KAAM,cACNC,UAAW,gBACXC,aAAc,aAElBC,OAAQ,SAAUb,EAAQc,GACtB5N,KAAK6N,QAAUf,EACf9M,KAAK8N,MAAQF,EACb5N,KAAK+N,WAETC,IAAK,SAAUC,GACPhG,EAAQgG,KACY,gBAATA,KACPA,GAASvQ,KAAMuQ,IAEnBjO,KAAKsN,MAAMnP,KAAK8P,GAChBjO,KAAK+N,YAGbG,OAAQ,SAAUxQ,GAAV,GAEKc,GACDyP,EAFJnL,IACJ,KAAStE,EAAI,EAAGA,EAAIwB,KAAKsN,MAAM5O,OAAQF,IAC/ByP,EAAOjO,KAAKsN,MAAM9O,GAClByP,EAAKvQ,OAASA,GACdoF,EAAO3E,KAAK8P,EAGpBjO,MAAKsN,MAAQxK,EACb9C,KAAK+N,WAETI,MAAO,WACHnO,KAAKsN,SACLtN,KAAKoN,QAAQgB,SAEjBL,QAAS,WAAA,GAGIvP,GACDyP,EACAvQ,EAJJoF,KACA4K,EAAeR,EAASlN,KAAKiB,QAAQyM,aACzC,KAASlP,EAAI,EAAGA,EAAIwB,KAAKsN,MAAM5O,OAAQF,IAC/ByP,EAAOjO,KAAKsN,MAAM9O,GAClBd,EAAOsC,KAAKqO,UAAUJ,GACb,KAATvQ,GACAoF,EAAO3E,KAAKuP,GAAehQ,KAAMA,IAGrCoF,GAAOpE,OAAS,EAChBsB,KAAKoN,QAAQgB,QAAQE,OAAOxL,EAAOzE,KAAK2B,KAAKiB,QAAQwM,YAAYc,OAEjEvO,KAAKoN,QAAQoB,QAGrBH,UAAW,SAAUJ,GAAV,GACHvQ,GAAO,GACP+Q,EAAczO,KAAK0O,aAAaT,EAAKU,QAASV,EAAKW,SACnDC,EAAS7O,KAAK8O,QAAQb,EAAKnB,OAI/B,OAHI2B,IAAeI,IACfnR,GAAQuQ,EAAKvQ,MAEVA,GAEXgR,aAAc,SAAU9G,EAAKD,GACzB,GAAI7E,IAAS,CAIb,OAHA8E,GAAMS,EAAeT,GAAMmH,OAAOC,WAClCrH,EAAMU,EAAeV,EAAKoH,OAAOC,WACjClM,EAAS9C,KAAK8N,MAAQlG,GAAO5H,KAAK8N,MAAQnG,GAG9CmH,QAAS,SAAUG,GACf,GAAInM,IAAS,CAIb,OAHImM,KACAnM,EAASmM,EAAK7C,SAASpM,KAAK6N,UAEzB/K,IAGfpD,GAAMqI,QAAQkF,GAAGiC,OAAO/B,IAC1B1N,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,yBAA0B,cAAeD,IAClD,YACG,SAAUE,GAMP,QAAS2R,GAAOC,GACZ,MAAO1P,GAAMoM,OAAO,kHAA6HsD,GAPxJ,GACO1P,GAAQD,OAAOC,MACfsN,EAAStN,EAAMuN,GAAGD,OAClBqC,EAAO3P,EAAM2P,KACbC,EAAQ9R,EAAE8R,MACVC,EAAK,kBAILC,EAAUL,EAAO,MAAQA,EAAO,SAAWA,EAAO,QAAUA,EAAO,QACnEM,EAAYzC,EAAOnN,QACnBC,KAAM,SAAUsN,EAASnM,GACrB+L,EAAOpB,GAAG9L,KAAK+G,KAAK7G,KAAMoN,EAASnM,GACnCjB,KAAKqN,aAAapM,GAClBjB,KAAKoN,QAAQG,SAAS,0CAA0Ce,OAAOkB,GAASE,GAAG,QAAUH,EAAI,YAAaD,EAAMtP,KAAM,UAC1H,IAAI2P,GAAgB3P,KAAKoN,QAAQwC,SAASC,QAAQ,IAAMnQ,EAAMoQ,KAAK,QAAU,IAC7E9P,MAAK+P,SAAWJ,EAAcjR,OAAS,EAAIiR,EAAgB3P,KAAKoN,QAChEpN,KAAKgQ,UAAUhQ,KAAK+P,UACpB/P,KAAKiQ,SAAWX,EAAMtP,KAAKiQ,SAAUjQ,MACrCA,KAAK+P,SAASL,GAAG,UAAW1P,KAAKiQ,WAErChP,SACIuM,KAAM,YACN0C,QAAS,GAEbC,QAAS,OACTC,QAAS,WACLpQ,KAAK+P,SAASM,IAAI,UAAWrQ,KAAKiQ,WAEtCK,KAAM,SAAU7K,EAAG8K,GACf,GAAIL,GAAUlQ,KAAKiB,QAAQiP,OAC3BlQ,MAAKwQ,QAAQ,OACT/K,EAAGA,EAAIyK,EACPK,EAAGA,EAAIL,KAGfO,OAAQ,SAAUC,GAAV,GACAjL,GAAI,EACJ8K,EAAI,EACJpB,EAAS3R,EAAEkT,EAAEC,cACbxB,GAAOyB,GAAG,mBACVL,EAAI,EACGpB,EAAOyB,GAAG,qBACjBL,KACOpB,EAAOyB,GAAG,sBACjBnL,EAAI,EACG0J,EAAOyB,GAAG,uBACjBnL,MAEJzF,KAAKsQ,KAAK7K,EAAG8K,GACbG,EAAEG,kBAENZ,SAAU,SAAUS,GAChB,OAAQA,EAAEI,OACV,IAAKzB,GAAK0B,GACN/Q,KAAKsQ,KAAK,EAAG,GACbI,EAAEG,gBACF,MACJ,KAAKxB,GAAK2B,KACNhR,KAAKsQ,KAAK,MACVI,EAAEG,gBACF,MACJ,KAAKxB,GAAK4B,MACNjR,KAAKsQ,KAAK,EAAG,GACbI,EAAEG,gBACF,MACJ,KAAKxB,GAAK6B,KACNlR,KAAKsQ,QAAS,GACdI,EAAEG,oBAKdnR,GAAMqI,QAAQkF,GAAGiC,OAAOO,IAC1BhQ,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,oBAAqB,cAAeD,IAC7C,YACG,SAAUE,GAKP,QAAS2R,GAAOC,EAAK+B,GACjB,MAAOzR,GAAMoM,OAAO,uHAAwHsD,EAAK+B,GANxJ,GACOzR,GAAQD,OAAOC,MACfsN,EAAStN,EAAMuN,GAAGD,OAClBqC,EAAO3P,EAAM2P,KACbC,EAAQ9R,EAAE8R,MAIVC,EAAK,oBACLC,EAAUL,EAAO,KAAM,YAAcA,EAAO,MAAO,aACnDiC,EAAO,IACPC,EAAQ,IACRC,EAAU,GACVC,EAAW,IACXC,EAAcxE,EAAOnN,QACrBC,KAAM,SAAUsN,EAASnM,GACrB+L,EAAOpB,GAAG9L,KAAK+G,KAAK7G,KAAMoN,EAASnM,GACnCjB,KAAKqN,aAAapM,GAClBjB,KAAKoN,QAAQG,SAAS,gGAAgGe,OAAOkB,GAASE,GAAG,QAAUH,EAAI,YAAaD,EAAMtP,KAAM,UAChL,IAAI2P,GAAgB3P,KAAKoN,QAAQwC,SAASC,QAAQ,IAAMnQ,EAAMoQ,KAAK,QAAU,IAC7E9P,MAAK+P,SAAWJ,EAAcjR,OAAS,EAAIiR,EAAgB3P,KAAKoN,QAChEpN,KAAKgQ,UAAUhQ,KAAK+P,UACpB/P,KAAKiQ,SAAWX,EAAMtP,KAAKiQ,SAAUjQ,MACrCA,KAAK+P,SAASL,GAAG,UAAW1P,KAAKiQ,WAErChP,SACIuM,KAAM,cACNiE,SAAU,GAEdtB,QAAS,UACTuB,QAAS,SAAUtC,GACf,GAAIqC,GAAWzR,KAAKiB,QAAQwQ,QAC5BzR,MAAKwQ,QAAQ,UAAYmB,MAAOvC,EAAMqC,KAE1ChB,OAAQ,SAAUC,GAAV,GACAvB,GAAS3R,EAAEkT,EAAEC,eACbvB,EAAM,CACND,GAAOyB,GAAG,iBACVxB,MAEJpP,KAAK0R,QAAQtC,GACbsB,EAAEG,kBAENZ,SAAU,SAAUS,GAChB,OAAQA,EAAEI,OACV,IAAKzB,GAAKuC,YACV,IAAKR,GACL,IAAKE,GACDtR,KAAK0R,QAAQ,EACb,MACJ,KAAKrC,GAAKwC,aACV,IAAKR,GACL,IAAKE,GACDvR,KAAK0R,eAKjBhS,GAAMqI,QAAQkF,GAAGiC,OAAOsC,IAC1B/R,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,mBACH,uBACA,iBACDD,IACL,YACG,SAAUE,EAAG2J,GAAb,GACOE,GAAOC,KAAME,EAAOH,EAAKG,KAAMsK,EAAMzK,EAAKyK,IAAKC,EAAM1K,EAAK0K,IAAKlK,EAAMR,EAAKQ,IAAKmK,EAAM3K,EAAK2K,IAAKlK,EAAMT,EAAKS,IAAKpI,EAAQD,OAAOC,MAAOE,EAAQF,EAAME,MAAOmI,EAAUrI,EAAMqI,QAAS3F,EAAa1C,EAAM0C,WAAY6P,EAAIvS,EAAMwS,SAAUC,EAAQF,EAAEE,MAAO7R,EAAMyH,EAAQzH,IAAKgI,EAAWhI,EAAIgI,SAAU3I,EAAOD,EAAMsI,QAAQrI,KAAMwI,EAAMxI,EAAKwI,IAAKD,EAAMvI,EAAKuI,IAAKkK,EAAQzS,EAAK0S,WAC3WC,EAAKjL,EAAKiL,GAAIC,EAAWD,EAAK,EAAGE,EAAWF,EAAK,EAAGG,EAAaH,EAAK,IACtExI,GACAC,EAAG,QACHE,EAAG,kBACH3M,EAAG,qBACHoT,EAAG,oBAEHgC,EAAW9S,EAAMC,QACjBC,KAAM,SAAUmB,GACZjB,KAAKqN,aAAapM,IAEtB0R,QAAS,IACTC,QAAS,cACTC,mBAAoB,GACpBC,oBAAqB,MACrB7R,SACI8R,gBAAiB,EACjB1J,MAAOS,GAEXkJ,QAAS,SAAUjK,EAAKkK,GACpB,GAAIC,GAAOlT,KAAMiB,EAAUiS,EAAKjS,QAASoI,EAAQpI,EAAQoI,MAAOjE,EAAIiE,EAAMU,EAAGoJ,EAAOlS,EAAQ8R,gBAAiBxK,EAAM6J,EAAMrJ,EAAIR,KAAM2K,EAAKN,QAASM,EAAKN,SAAUpK,EAAMyK,EAAQb,EAAMrJ,EAAIP,KAAM0K,EAAKP,QAASO,EAAKP,SAAW5J,EAAIP,IAAK/C,EAAI0C,EAAIK,EAAM2K,GAAQ/N,EAAGmL,EAAI2C,EAAKE,YAAY7K,EACnR,OAAO,IAAI4J,GAAM1M,EAAG8K,IAExB6C,YAAa,SAAU7K,GACnB,GAAIc,GAAQrJ,KAAKiB,QAAQoI,MAAOgK,EAAMhK,EAAMqH,EAAGtL,EAAIiE,EAAMU,EAAGwG,EAAIpI,EAAII,GAAM+K,EAAKxL,EAAI0K,EAAWjC,EAAI,GAAIgD,EAAMF,EAAMxL,EAAI0I,GAAIiD,EAAIzB,GAAK,EAAIwB,IAAQ,EAAIA,GAAMF,EAAM,EAC/J,OAAOjO,GAAI4M,EAAIsB,EAAKE,IAExBC,QAAS,SAAUC,EAAOT,GACtB,GAAIC,GAAOlT,KAAMiB,EAAUiS,EAAKjS,QAASoI,EAAQpI,EAAQoI,MAAOjE,EAAIiE,EAAMU,EAAGoJ,EAAOlS,EAAQ8R,gBAAiBvK,EAAMkL,EAAMjO,GAAKgN,EAAarN,GAAK+N,EAAM5K,EAAM6J,EAAMc,EAAKS,UAAUD,EAAMnD,IAAK2C,EAAKN,QAASM,EAAKN,QAI/M,OAHIK,KACAzK,EAAM4J,EAAM5J,GAAM0K,EAAKP,QAASO,EAAKP,UAElC,GAAIrK,GAASC,EAAKC,IAE7BmL,UAAW,SAAUpD,GAAV,GACmI/R,GAElI+U,EAAsBC,EAAsCI,EAFhEV,EAAOlT,KAAMqJ,EAAQ6J,EAAKjS,QAAQoI,MAAOjE,EAAIiE,EAAMU,EAAGsJ,EAAMhK,EAAMqH,EAAGmD,EAAOR,EAAM,EAAGC,EAAKxB,GAAKvB,EAAInL,GAAI0O,EAAMvB,EAAW,EAAI/K,EAAK8L,EACrI,KAAK9U,EAAI,EAAGA,GAAK0U,EAAKL,qBACdU,EAAMF,EAAMxL,EAAIiM,GAAMN,EAAIzB,GAAK,EAAIwB,IAAQ,EAAIA,GAAMM,GAAOD,EAAOrB,EAAW,EAAI/K,EAAK8L,EAAKE,GAAKM,EACrGA,GAAOF,IACHvM,EAAKE,IAAIqM,IAASV,EAAKJ,sBAHWtU,KAO1C,MAAO0J,GAAI4L,MAGfC,EAAoBrB,EAAS7S,QAC7B+S,QAAS,cACTQ,YAAa,SAAU7K,GACnB,GAAInD,GAAIpF,KAAKiB,QAAQoI,MAAMU,EAAGwG,EAAIpI,EAAII,GAAM+K,EAAKxL,EAAI0K,EAAWjC,EAAI,EACpE,OAAOnL,GAAI4M,EAAIsB,IAEnBK,UAAW,SAAUpD,GACjB,GAAInL,GAAIpF,KAAKiB,QAAQoI,MAAMU,EAAGuJ,EAAKxB,GAAKvB,EAAInL,EAC5C,OAAO8C,GAAIqK,EAAW,EAAI/K,EAAK8L,OAGnCU,EAAkBpU,EAAMC,QACxBmT,QAAS,SAAUjK,GACf,MAAO,IAAIoJ,GAAMpJ,EAAIP,IAAKO,EAAIR,MAElCkL,QAAS,SAAUC,GACf,MAAO,IAAIpL,GAASoL,EAAMnD,EAAGmD,EAAMjO,MAGvCwO,EAAWrU,EAAMC,QACjBC,KAAM,WAAA,GACEoU,GAAMlU,KAAMkT,EAAOgB,EAAIC,MAAQ,GAAIJ,GACnCpO,EAAI3F,KAAK2F,EAAI,EAAI2M,EAAKY,EAAKjS,QAAQoI,MAAMU,CAC7C/J,MAAKoU,IAAMnC,EAAEoC,YAAYC,UAAU,GAAK,IAAKC,MAAM,EAAI5O,KAAQA,GAC/D3F,KAAKwU,KAAOvC,EAAEoC,YAAYE,MAAM5O,GAAIA,GAAG2O,oBAE3CG,QAAS,SAAU1L,EAAKwL,EAAOtB,GAC3B,GAAIS,GAAQ1T,KAAKmU,MAAMnB,QAAQjK,EAAKkK,EACpC,OAAOS,GAAMW,UAAUrU,KAAKoU,KAAKG,MAAMA,GAAS,IAEpDG,WAAY,SAAUhB,EAAOa,EAAOtB,GAEhC,MADAS,GAAQA,EAAM1K,QAAQuL,MAAM,GAAKA,GAAS,IAAIF,UAAUrU,KAAKwU,MACtDxU,KAAKmU,MAAMV,QAAQC,EAAOT,MAGrC0B,EAAW/U,EAAMC,QACjBC,KAAM,WACFE,KAAKmU,MAAQ,GAAIzB,IAErB+B,QAAS,SAAU1L,GACf,MAAO/I,MAAKmU,MAAMnB,QAAQjK,IAE9B2L,WAAY,SAAUhB,GAClB,MAAO1T,MAAKmU,MAAMV,QAAQC,MAG9BkB,EAAWhV,EAAMC,QACjBC,KAAM,WACFE,KAAKmU,MAAQ,GAAIH,IAErBS,QAAS,SAAU1L,GACf,MAAO/I,MAAKmU,MAAMnB,QAAQjK,IAE9B2L,WAAY,SAAUhB,GAClB,MAAO1T,MAAKmU,MAAMV,QAAQC,KAGlCtR,GAAW2F,GACPzH,KACI4T,KACIS,SAAUA,EACVV,SAAUA,EACVW,SAAUA,GAEd/K,QAAUC,MAAOA,GACjB+K,aACIb,gBAAiBA,EACjBtB,SAAUA,EACVqB,kBAAmBA,OAIjCtU,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,2BACH,aACA,wBACDD,IACL,YACG,SAAUE,EAAG2J,GAAb,GACOmI,GAAQ9R,EAAE8R,MAAO5P,EAAQD,OAAOC,MAAOE,EAAQF,EAAME,MAAOmI,EAAUrI,EAAMqI,QAAS3F,EAAa1C,EAAM0C,WAAYgF,EAASW,EAAQzH,IAAI8G,OAAQzH,EAAOD,EAAMsI,QAAQrI,KAAMsI,EAAUtI,EAAKsI,QAC3L6M,EAAQlV,EAAMC,QACdC,KAAM,SAAUQ,EAAKW,GACjBjB,KAAKqN,aAAapM,GAClBjB,KAAKM,IAAMA,EACXN,KAAKoN,QAAU5P,EAAE,+BAAiCuX,KAC9CC,OAAUhV,KAAKiB,QAAQ+T,OACvBC,QAAWjV,KAAKiB,QAAQgU,UACzBC,SAAS5U,EAAI6U,eAChBnV,KAAKoV,aAAe9F,EAAMtP,KAAKoV,aAAcpV,MAC7CA,KAAKqV,OAAS/F,EAAMtP,KAAKqV,OAAQrV,MACjCA,KAAKsV,QAAUhG,EAAMtP,KAAKsV,QAAStV,MACnCA,KAAKuV,QAAUjG,EAAMtP,KAAKuV,QAASvV,MACnCA,KAAKwV,YACLxV,KAAKyV,sBAETC,QAAS,WACL1V,KAAK2V,eAETpH,KAAM,WACFvO,KAAK4V,QACL5V,KAAKwV,YACLxV,KAAK6V,cAAa,IAEtBrH,KAAM,WACFxO,KAAK2V,cACL3V,KAAK8V,gBAAe,IAExBF,MAAO,WACH5V,KAAKoV,eACLpV,KAAKqV,UAETA,OAAQ,WACJrV,KAAK6V,gBAETT,aAAc5X,EAAEuY,KAChBT,QAAS9X,EAAEuY,KACXR,QAAS,WACLvV,KAAK6V,gBAETA,aAAc,WAAA,GACN5U,GAAUjB,KAAKiB,QACf2M,EAAO5N,KAAKM,IAAIsN,OAChBoI,GAAgB/N,EAAQhH,EAAQ0N,UAAYf,GAAQ3M,EAAQ0N,QAC5DsH,GAAgBhO,EAAQhH,EAAQ2N,UAAYhB,GAAQ3M,EAAQ2N,QAC5D9B,EAAS1F,EAAOlB,OAAOjF,EAAQ6L,QAC/BoJ,GAAUpJ,GAAUA,EAAOD,SAAS7M,KAAKM,IAAIwM,SACjD9M,MAAK8V,eAAeE,GAAgBC,GAAgBC,IAExDJ,eAAgB,SAAUK,GACtBnW,KAAKoN,QAAQ2H,IAAI,UAAWoB,EAAU,GAAK,SAE/CX,UAAW,WACP,GAAIlV,GAAMN,KAAKM,GACfA,GAAI8V,KAAK,cAAepW,KAAKoV,cAC7B9U,EAAI8V,KAAK,QAASpW,KAAKqV,QACvB/U,EAAI8V,KAAK,SAAUpW,KAAKsV,SACxBhV,EAAI8V,KAAK,SAAUpW,KAAKuV,UAE5BI,YAAa,WACT,GAAIrV,GAAMN,KAAKM,GACfA,GAAI+V,OAAO,cAAerW,KAAKoV,cAC/B9U,EAAI+V,OAAO,QAASrW,KAAKqV,QACzB/U,EAAI+V,OAAO,SAAUrW,KAAKsV,SAC1BhV,EAAI+V,OAAO,SAAUrW,KAAKuV,UAE9BE,mBAAoB,WAChB,GAAI3F,GAAO9P,KAAKM,IAAIgW,WAChBxG,IACAA,EAAK9B,IAAIhO,KAAKiB,QAAQqV,eAIlClU,GAAW2F,GAAWzH,KAAOiW,QAAUzB,MAAOA,OAChDrV,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,4BACH,0BACA,wBACDD,IACL,YACG,SAAUE,EAAG2J,GAAb,GACOmI,GAAQ9R,EAAE8R,MAAO5P,EAAQD,OAAOC,MAAOE,EAAQF,EAAME,MAAO4W,EAAa9W,EAAM+W,KAAKD,WAAYzO,EAAUrI,EAAMqI,QAAS3F,EAAa1C,EAAM0C,WAAY6P,EAAIvS,EAAMwS,SAAUtM,EAAIlG,EAAMsI,QAAS0O,EAAQ9Q,EAAE8Q,MAAOC,EAAO/Q,EAAEjG,KAAKgX,KAAM1O,EAAUrC,EAAEjG,KAAKsI,QAAS3H,EAAMyH,EAAQzH,IAAKgI,EAAWhI,EAAIgI,SAAUwM,EAAQxU,EAAIiW,OAAOzB,MAC7T8B,EAAa9B,EAAMjV,QACnBC,KAAM,SAAUQ,EAAKW,GACjBjB,KAAKsQ,KAAOhB,EAAMtP,KAAKsQ,KAAMtQ,MAC7B8U,EAAMlJ,GAAG9L,KAAK+G,KAAK7G,KAAMM,EAAKW,GAC9BjB,KAAK6W,QAAUjR,EAAEkR,QAAQ5Q,OAAOlG,KAAKoN,SACjCvO,MAAOyB,EAAI6U,cAActW,QACzBC,OAAQwB,EAAI6U,cAAcrW,WAE9BkB,KAAK+W,YACL/W,KAAKgX,QAAU,GAAItX,GAAMuN,GAAGgK,QAAQjX,KAAK6W,QAAQzJ,SACjDpN,KAAKkX,YACLlX,KAAKyQ,OAASzQ,KAAKmX,SAAS,cAC5BnX,KAAK6W,QAAQT,KAAK,QAASpW,KAAKyQ,QAChCzQ,KAAKoX,YAAcpX,KAAKmX,SAAS,mBACjCnX,KAAK6W,QAAQT,KAAK,aAAcpW,KAAKoX,aACrCpX,KAAKqX,YAAcrX,KAAKmX,SAAS,mBACjCnX,KAAK6W,QAAQT,KAAK,aAAcpW,KAAKqX,aACrCrX,KAAKsX,mBAETrW,SAAWsW,UAAU,GACrB7B,QAAS,WACLZ,EAAMlJ,GAAG8J,QAAQ7O,KAAK7G,MACtBA,KAAK6W,QAAQnB,UACb1V,KAAKwX,WAAWnB,OAAO,SAAUrW,KAAKyX,cAE1CC,cAAe,SAAUF,GACjBxX,KAAKwX,YACLxX,KAAKwX,WAAWnB,OAAO,SAAUrW,KAAKyX,aAE1CzX,KAAKwX,WAAa9X,EAAM+W,KAAKD,WAAWtQ,OAAOsR,GAC/CxX,KAAKwX,WAAWpB,KAAK,SAAUpW,KAAKyX,aAChCzX,KAAKiB,QAAQsW,UACbvX,KAAKwX,WAAWG,SAGxBtC,OAAQ,WACJP,EAAMlJ,GAAGyJ,OAAOxO,KAAK7G,MACrBA,KAAK4X,oBACD5X,KAAK6X,OACL7X,KAAK8X,MAAM9X,KAAK6X,QAGxBd,UAAW,WACP/W,KAAK+X,MAAQ,GAAIrB,GACjB1W,KAAK6W,QAAQmB,KAAKhY,KAAK+X,QAE3B3C,aAAc,WACVpV,KAAK6W,QAAQ1I,QACbnO,KAAK+W,aAETzB,QAAS,WACLtV,KAAK6W,QAAQ9W,KAAKC,KAAKM,IAAIP,SAE/BuX,gBAAiB,WACb,GAAIW,GAAYjY,KAAKiB,QAAQuW,UAC7BxX,MAAKyX,YAAcnI,EAAMtP,KAAKyX,YAAazX,MAC3CA,KAAKwX,WAAahB,EAAWtQ,OAAO+R,GAAW7B,KAAK,SAAUpW,KAAKyX,aAC/DQ,GAAajY,KAAKiB,QAAQsW,UAC1BvX,KAAKwX,WAAWG,SAGxBF,YAAa,SAAU/G,GACnB1Q,KAAK6X,MAAQnH,EAAEwH,OAAOC,OACtBnY,KAAK8X,MAAM9X,KAAK6X,QAEpBC,MAAO,SAAUrB,GAAV,GAKC2B,GACK5Z,EACD6Z,CADR,KALArY,KAAKsY,gBACAtY,KAAKuY,UACNvY,KAAKuY,QAAU,GAAIC,GAAcxY,KAAKM,IAAKN,KAAKiB,QAAQhC,MAAOe,OAE/DoY,EAAY,GAAI1B,GACXlY,EAAI,EAAGA,EAAIiY,EAAK/X,OAAQF,IACzB6Z,EAAQrY,KAAKuY,QAAQE,MAAMhC,EAAKjY,IAChC6Z,GACAD,EAAU9J,OAAO+J,EAGzBrY,MAAK+X,MAAM5J,QACXnO,KAAK+X,MAAMzJ,OAAO8J,IAEtBM,aAAc,SAAUL,GAAV,GAMFM,GALJC,GAAY,CAWhB,OAVIP,aAAiBzS,GAAEiT,SACnBD,EAAY3Q,EAAQjI,KAAK8Y,cAAcT,KAEtCO,IACGD,GACAI,MAAO/Y,KACPqY,MAAOA,GAEXO,EAAY5Y,KAAKM,IAAIkQ,QAAQ,eAAgBmI,IAE1CC,GAEXI,eAAgB,SAAUtI,GACtBA,EAAEqI,MAAQ/Y,KACVA,KAAKM,IAAIkQ,QAAQ,sBAAuBE,IAE5CoI,cAAe,SAAUT,GACrB,GAAIlW,GAASnC,KAAKM,IAAI2Y,QAAQ7C,MAAO8C,SAAUb,EAAMa,UAAYb,EAAMc,SAIvE,OAHIhX,IACAnC,KAAKkX,SAAS/Y,KAAKgE,GAEhBA,GAEXmW,cAAe,WACX,IAAK,GAAI9Z,GAAI,EAAGA,EAAIwB,KAAKkX,SAASxY,OAAQF,IACtCwB,KAAKM,IAAI2Y,QAAQ/K,OAAOlO,KAAKkX,SAAS1Y,GAE1CwB,MAAKkX,aAET5G,KAAM,WACGtQ,KAAKoZ,WACNpZ,KAAKoZ,UAAW,EAChBpZ,KAAK6W,QAAQwC,oBAGrB9D,QAAS,SAAU7E,GACfoE,EAAMlJ,GAAG2J,QAAQ1O,KAAK7G,KAAM0Q,GAC5B1Q,KAAK4X,oBACL5X,KAAK6W,QAAQyC,iBACbtZ,KAAKoZ,UAAW,GAEpBxB,kBAAmB,WAAA,GACXtX,GAAMN,KAAKM,IACX4L,EAAK5L,EAAIiZ,eAAejZ,EAAIwM,SAASZ,GACrClM,MAAK6W,QAAQvC,YACbtU,KAAK6W,QAAQvC,UAAUpI,GACvBlM,KAAKgX,QAAQwC,QACT/T,EAAGyG,EAAGzG,EACN8K,EAAGrE,EAAGqE,MAIlB4G,SAAU,SAAUsC,GAChB,GAAIV,GAAQ/Y,IACZ,OAAO,UAAU0Q,GACb,GAAIA,EAAEtD,QAAS,CACX,GAAIuL,IACAI,MAAOA,EACPV,MAAO3H,EAAEtD,QACTsM,cAAehJ,EAAEgJ,cAErBX,GAAMzY,IAAIkQ,QAAQiJ,EAAOd,MAIrCnD,UAAW,WACPV,EAAMlJ,GAAG4J,UAAU3O,KAAK7G,MACxBA,KAAKM,IAAI8V,KAAK,MAAOpW,KAAKsQ,OAE9BqF,YAAa,WACTb,EAAMlJ,GAAG+J,YAAY9O,KAAK7G,MAC1BA,KAAKM,IAAI+V,OAAO,MAAOrW,KAAKsQ,SAGhCkI,EAAgB5Y,EAAMC,QACtBC,KAAM,SAAU6Z,EAASC,EAAcC,GACnC7Z,KAAK6Z,SAAWA,EAChB7Z,KAAK2Z,QAAUA,EACf3Z,KAAKf,MAAQ2a,GAEjBnB,MAAO,SAAUxK,GAAV,GACC6L,GAAO,GAAIpD,GACXqD,GAAS,CAWb,OAVkB,YAAd9L,EAAK+L,MACLD,GAAS,EACT/Z,KAAKia,gBAAgBH,EAAM7L,EAAKiE,SAAUjE,GAC1CjO,KAAKka,gBAAgBJ,EAAM7L,IAE3BjO,KAAKia,gBAAgBH,EAAM7L,EAAMA,GAEjC8L,GAAUD,EAAKK,SAASzb,OAAS,IACjCob,EAAOA,EAAKK,SAAS,IAElBL,GAEXM,cAAe,SAAU/B,GACrB,GAAIO,IAAY,CAIhB,OAHI5Y,MAAK6Z,UAAY7Z,KAAK6Z,SAASnB,eAC/BE,EAAY5Y,KAAK6Z,SAASnB,aAAaL,IAEpCO,GAEXsB,gBAAiB,SAAUG,EAAOlB,GAC1BnZ,KAAK6Z,UAAY7Z,KAAK6Z,SAASb,gBAC/BhZ,KAAK6Z,SAASb,gBACVqB,MAAOA,EACPlB,SAAUA,EACVmB,WAAYnB,EAASmB,cAIjCL,gBAAiB,SAAU7B,EAAWlG,EAAUiH,GAA/B,GAET3a,GACA+b,EAFAC,EAAStI,EAASuI,WAGtB,QAAQvI,EAAS8H,MACjB,IAAK,aACDO,EAAOva,KAAK0a,aAAatC,GAAYoC,GAASrB,GAC9CnZ,KAAK2a,aAAaJ,EAClB,MACJ,KAAK,kBACD,IAAK/b,EAAI,EAAGA,EAAIgc,EAAO9b,OAAQF,IAC3B+b,EAAOva,KAAK0a,aAAatC,GAAYoC,EAAOhc,IAAK2a,GACjDnZ,KAAK2a,aAAaJ,EAEtB,MACJ,KAAK,UACDva,KAAK0a,aAAatC,EAAWoC,EAAQrB,EACrC,MACJ,KAAK,eACD,IAAK3a,EAAI,EAAGA,EAAIgc,EAAO9b,OAAQF,IAC3BwB,KAAK0a,aAAatC,EAAWoC,EAAOhc,GAAI2a,EAE5C,MACJ,KAAK,QACDnZ,KAAK4a,WAAWxC,EAAWoC,EAAQrB,EACnC,MACJ,KAAK,aACD,IAAK3a,EAAI,EAAGA,EAAIgc,EAAO9b,OAAQF,IAC3BwB,KAAK4a,WAAWxC,EAAWoC,EAAOhc,GAAI2a,KAKlDwB,aAAc,SAAUJ,GACpB,GAAIM,GAAWN,EAAKM,UAChBA,EAASnc,OAAS,IAAMmc,EAAS,GAAGC,SAAShS,OAAO6N,EAAKkE,GAAUC,aACnEP,EAAKtZ,QAAQ8Z,KAAO,OAG5BC,WAAY,SAAU5C,EAAWC,GAI7B,MAHKrY,MAAKoa,cAAc/B,IACpBD,EAAU9J,OAAO+J,GAEdA,GAEXqC,aAAc,SAAUtC,EAAW6C,EAAO9B,GACtC,GAAId,GAAQrY,KAAKkb,cAAcD,EAE/B,OADA5C,GAAMc,SAAWA,EACVnZ,KAAKgb,WAAW5C,EAAWC,IAEtC6C,cAAe,SAAUD,GAAV,GAGFzc,GACI2c,EACDzH,EAJRsG,EAAOiB,EAAMvc,OAAS,EAAIkH,EAAEwV,UAAYxV,EAAEyV,KAC1Cd,EAAO,GAAIP,GAAKha,KAAKf,MACzB,KAAST,EAAI,EAAGA,EAAIyc,EAAMvc,OAAQF,IAC9B,IAAS2c,EAAI,EAAGA,EAAIF,EAAMzc,GAAGE,OAAQyc,IAC7BzH,EAAQ1T,KAAK2Z,QAAQJ,eAAejR,EAASyD,WAAWkP,EAAMzc,GAAG2c,KAC3D,IAANA,EACAZ,EAAKf,OAAO9F,EAAMjO,EAAGiO,EAAMnD,GAE3BgK,EAAKe,OAAO5H,EAAMjO,EAAGiO,EAAMnD,EAIvC,OAAOgK,IAEXK,WAAY,SAAUxC,EAAWoC,EAAQrB,GAA7B,GACJD,GAAW5Q,EAASyD,WAAWyO,GAC/B9G,EAAQ1T,KAAK2Z,QAAQJ,eAAeL,GACpCqC,EAAS,GAAItJ,GAAE4G,OAAOnF,EAAO,IAC7B2E,EAAQ,GAAIzS,GAAEiT,OAAO0C,EAAQvb,KAAKf,MAGtC,OAFAoZ,GAAMc,SAAWA,EACjBd,EAAMa,SAAWA,EACVlZ,KAAKgb,WAAW5C,EAAWC,KAG1CjW,GAAW1C,EAAM+W,MACb+E,SACIC,SACIzB,KAAM,OACNvD,KAAM,SAAUA,GACZ,MAAkB,sBAAdA,EAAKuD,KACEvD,EAAKiF,SAEE,uBAAdjF,EAAKuD,KACEvD,EAAKkF,WAETlF,KAInBmF,YAAcH,SAAWI,MAAQC,SAAU,YAE/C1Z,EAAW2F,GACPzH,KACIiW,QACI8B,MAAOzB,EACPA,WAAYA,GAEhB4B,cAAeA,MAGzB/Y,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,6BAA8B,4BAA6BD,IACpE,YACG,SAAUE,EAAG2J,GAAb,GACOzH,GAAQD,OAAOC,MAAOqc,EAASrc,EAAMqc,OAAQhU,EAAUrI,EAAMqI,QAAS3F,EAAa1C,EAAM0C,WAAY6P,EAAIvS,EAAMwS,SAAUtM,EAAIlG,EAAMsI,QAASrI,EAAOiG,EAAEjG,KAAMsI,EAAUtI,EAAKsI,QAAS3H,EAAMyH,EAAQzH,IAAKgI,EAAWhI,EAAIgI,SAAUsO,EAAatW,EAAIiW,OAAOK,WACvPoF,EAAcpF,EAAW/W,QACzBoB,SACIsW,UAAU,EACV0E,cAAe,WACfC,WAAY,QACZC,QAAS,EACTC,QAAS,IACT7H,MAAO,OACP8H,OAAQ,UAEZvE,MAAO,SAAUrB,GAAV,GAKC6F,GACAC,EAKAC,EACAjI,EACK/V,EACD2a,EACAD,EACA7Y,EAYIgM,EACAtM,EACAsc,CA5BZ,IADArc,KAAK6W,QAAQ1I,QACO,IAAhBsI,EAAK/X,OAWT,IARI4d,EAAMtc,KAAKiB,QACXsb,EAAWR,EAAOO,EAAIJ,YAC1BzF,EAAOA,EAAKgG,MAAM,GAClBhG,EAAKrY,KAAK,SAAU2L,EAAGE,GACnB,MAAOsS,GAAStS,GAAKsS,EAASxS,KAE9ByS,EAAYxc,KAAK0c,aAEZle,EAAI,EAAGA,EAAIiY,EAAK/X,OAAQF,IACzB2a,EAAW1C,EAAKjY,GAChB0a,EAAW6C,EAAOO,EAAIL,eAAe9C,GACrC9Y,EAAQ0b,EAAOO,EAAIJ,YAAY/C,GAC/BlR,EAAQiR,IAAajR,EAAQ5H,KACxBkU,IACDA,EAAQ,GAAIiI,IACR,EACAnc,IAEAic,EAAIH,QACJG,EAAIF,WAGZlD,EAAW5Q,EAASpC,OAAOgT,GACvB7M,EAASrM,KAAKM,IAAIiZ,eAAeL,GACjCnZ,EAAOwU,EAAMjU,IAAID,GACjBgc,EAASrc,KAAK2c,eACdtQ,OAAQA,EACRtM,KAAMA,EACNd,MAAOqd,EAAIrd,MACXka,SAAUA,EACVD,SAAUA,IAEdmD,EAAOlD,SAAWA,EAClBkD,EAAOnD,SAAWA,EAClBmD,EAAOhc,MAAQA,EACfL,KAAK4c,YAAYP,KAI7BK,WAAY,WACR,GAAInI,GAAQvU,KAAKiB,QAAQsT,KACzB,OAAI7U,GAAMmd,WAAWtI,GACVA,EAEJxM,EAAQzH,IAAIwc,OAAOvI,IAE9BoI,cAAe,SAAUhE,GACrB,GAAI0D,GAASrc,KAAKiB,QAAQob,MAI1B,OAHK3c,GAAMmd,WAAWR,KAClBA,EAAStU,EAAQzH,IAAIyc,QAAQV,IAE1BA,EAAO1D,IAElBiE,YAAa,SAAUvE,GAAV,GACLM,IACAI,MAAO/Y,KACPqY,MAAOA,GAEPO,EAAY5Y,KAAKM,IAAIkQ,QAAQ,eAAgBmI,EAC5CC,IACD5Y,KAAK6W,QAAQmB,KAAKK,MAI1B2E,EAAYtd,EAAME,MAAMC,QACxBC,KAAM,SAAUmd,EAAQC,GAAlB,GAGEC,GACAC,CAHJpd,MAAKqd,QAAUJ,EACfjd,KAAKsd,OAASJ,EACVC,EAAc7V,KAAKqE,KAAKsR,EAAO,IAAM3V,KAAKqE,KAAKsR,EAAO,IACtDG,EAAcF,EAAM,GAAKA,EAAM,GACnCld,KAAKud,OAASH,EAAcD,GAEhC7c,IAAK,SAAUD,GACX,GAAImd,IAAOlW,KAAKqE,KAAKtL,GAASiH,KAAKqE,KAAK3L,KAAKqd,QAAQ,KAAOrd,KAAKud,MACjE,OAAOvd,MAAKsd,OAAO,GAAKE,KAG5BC,GACAlC,OAAQ,SAAU5C,GACd,GAAI+E,GAAM,GAAIzL,GAAE4G,OAAOF,EAAKtM,OAAQsM,EAAK5Y,KAAO,EAChD,OAAO,IAAI6F,GAAEiT,OAAO6E,EAAK/E,EAAK1Z,QAElC0e,OAAQ,SAAUhF,GAAV,GACA4B,GAAO,GAAI3U,GAAEyV,KAAK1C,EAAK1Z,OACvB2e,EAAWjF,EAAK5Y,KAAO,EACvBsM,EAASsM,EAAKtM,MAElB,OADAkO,GAAKf,OAAOnN,EAAO5G,EAAImY,EAAUvR,EAAOkE,EAAIqN,GAAUtC,OAAOjP,EAAO5G,EAAImY,EAAUvR,EAAOkE,EAAIqN,GAAUtC,OAAOjP,EAAO5G,EAAImY,EAAUvR,EAAOkE,EAAIqN,GAAUtC,OAAOjP,EAAO5G,EAAImY,EAAUvR,EAAOkE,EAAIqN,GAAUC,QAClMtD,GAGfnY,GAAW2F,GACPzH,KACIiW,QACIuH,OAAQ9B,EACRA,YAAaA,GAEjBc,QAAUnR,KAAMqR,GAChBD,QAASU,MAGnBhe,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,2BACH,0BACA,wBACDD,IACL,YACG,SAAUE,EAAG2J,GAmTV,QAAS4W,GAAWrK,GAChB,MAAO,IAAIvB,GAAM/J,EAAMsL,EAAMjO,GAAI2C,EAAMsL,EAAMnD,IApTpD,GACOlJ,GAAOC,KAAMgI,EAAQ9R,EAAE8R,MAAO5P,EAAQD,OAAOC,MAAOE,EAAQF,EAAME,MAAOsN,EAAWxN,EAAMwN,SAAUnF,EAAUrI,EAAMqI,QAAS3F,EAAa1C,EAAM0C,WAAY6P,EAAIvS,EAAMwS,SAAUC,EAAQF,EAAEE,MAAO2C,EAAQ/M,EAAQzH,IAAIiW,OAAOzB,MAAOnV,EAAOD,EAAMC,KAAMgD,EAAahD,EAAKgD,WAAYqb,EAActe,EAAMsI,QAAQrI,KAAMyI,EAAQ4V,EAAY5V,MAAOgK,EAAQ4L,EAAY3L,WACrW4L,EAAYnJ,EAAMjV,QAClBC,KAAM,SAAUQ,EAAKW,GACjB6T,EAAMlJ,GAAG9L,KAAK+G,KAAK7G,KAAMM,EAAKW,GACS,gBAA5BjB,MAAKiB,QAAQid,aACpBle,KAAKiB,QAAQid,WAAale,KAAKiB,QAAQid,WAAWlb,MAAM,IAE5D,IAAImb,GAAWne,KAAKoe,WACpBpe,MAAKqe,MAAQ,GAAIF,GAASne,KAAKoN,QAASpN,KAAKiB,UAEjDyU,QAAS,WACLZ,EAAMlJ,GAAG8J,QAAQ7O,KAAK7G,MACtBA,KAAKqe,MAAM3I,UACX1V,KAAKqe,MAAQ,MAEjBjJ,aAAc,WAAA,GACN9U,GAAMN,KAAKM,IACXge,EAAShe,EAAIie,gBAAgBje,EAAIwM,SAASZ,IAAI9D,OAClDpI,MAAKqe,MAAMG,WAAWF,IAE1BjJ,OAAQ,WACJP,EAAMlJ,GAAGyJ,OAAOxO,KAAK7G,MACrBA,KAAKye,cACLze,KAAKqe,MAAMzI,SAEfwI,UAAW,WACP,MAAOM,IAEXlJ,UAAW,WACPV,EAAMlJ,GAAG4J,UAAU3O,KAAK7G,MACnBN,EAAMif,QAAQC,WACV5e,KAAKsQ,OACNtQ,KAAKsQ,KAAO5Q,EAAMmf,SAASvP,EAAMtP,KAAK+N,QAAS/N,MAAO,MAE1DA,KAAKM,IAAI8V,KAAK,MAAOpW,KAAKsQ,QAGlCqF,YAAa,WACTb,EAAMlJ,GAAG+J,YAAY9O,KAAK7G,MACtBA,KAAKsQ,MACLtQ,KAAKM,IAAI+V,OAAO,MAAOrW,KAAKsQ,OAGpCmO,YAAa,WACT,GAAItG,GAAOnY,KAAKqe,MAAO/d,EAAMN,KAAKM,IAAKwM,EAASxM,EAAIwM,SAAUgS,GACtD5S,GAAI5L,EAAIie,gBAAgBzR,EAAOZ,IAAI9D,QACnC+D,GAAI7L,EAAIie,gBAAgBzR,EAAOX,IAAI/D,QAE3C+P,GAAK9L,OAAO/L,EAAIie,gBAAgBje,EAAI+L,WACpC8L,EAAKrL,OAAOgS,GACZ3G,EAAKvK,KAAKtN,EAAIsN,SAElB0H,QAAS,WACLtV,KAAK+N,WAETwH,QAAS,SAAU7E,GACfoE,EAAMlJ,GAAG2J,QAAQ1O,KAAK7G,KAAM0Q,GAC5B1Q,KAAK+N,WAETA,QAAS,WACL/N,KAAKye,cACLze,KAAKqe,MAAMU,YAGfL,EAAW9e,EAAMC,QACjBC,KAAM,SAAUsN,EAASnM,GACrBjB,KAAKoN,QAAUA,EACfpN,KAAKqN,aAAapM,GAClBjB,KAAKgf,KAAO,GAAIC,IAEpBhe,SACIie,SAAU,IACVhB,YACI,IACA,IACA,KAEJiB,YAAa,IAEjB9S,OAAQ,SAAUA,GACdrM,KAAKof,QAAU/S,GAEnBS,OAAQ,SAAUA,GACd9M,KAAK6N,QAAUf,GAEnB0R,WAAY,SAAUF,GAClBte,KAAKqf,YAAcf,GAEvB1Q,KAAM,SAAUA,GACZ5N,KAAK8N,MAAQF,GAEjB0R,iBAAkB,SAAU5L,GACxB,MAAO,IAAIvB,GAAM9K,EAAKkY,MAAM7L,EAAMjO,EAAIzF,KAAKiB,QAAQie,UAAW7X,EAAKkY,MAAM7L,EAAMnD,EAAIvQ,KAAKiB,QAAQie,YAEpGM,UAAW,WACP,GAAIzf,GAAOC,KAAKD,OAAQ0f,EAAiBzf,KAAKsf,iBAAiBtf,KAAK6N,QAAQ3B,IAAKA,EAAKlM,KAAK6N,QAAQ3B,GAAIwH,EAAQ1T,KAAK0f,aAAaD,GAAgBnL,WAAWpI,EAAGzG,GAAIyG,EAAGqE,EACtK,QACI9K,EAAG4B,EAAKsY,MAAMtY,EAAKE,IAAImM,EAAMjO,GAAK1F,EAAKlB,OAASmB,KAAKiB,QAAQie,UAC7D3O,EAAGlJ,EAAKsY,MAAMtY,EAAKE,IAAImM,EAAMnD,GAAKxQ,EAAKjB,QAAUkB,KAAKiB,QAAQie,YAGtEnf,KAAM,WACF,GAAImM,GAAKlM,KAAK6N,QAAQ3B,GAAIC,EAAKnM,KAAK6N,QAAQ1B,GAAIyT,EAAOzT,EAAGnD,QAAQsL,WAAWpI,EAAGzG,GAAIyG,EAAGqE,EACvF,QACI1R,MAAO+gB,EAAKna,EACZ3G,OAAQ8gB,EAAKrP,IAGrBmP,aAAc,SAAUG,GACpB,GAAIpa,GAAIoa,EAAMpa,EAAG8K,EAAIsP,EAAMtP,CAC3B,OAAO,IAAI4B,GAAM1M,EAAIzF,KAAKiB,QAAQie,SAAU3O,EAAIvQ,KAAKiB,QAAQie,WAEjEY,cAAe,WACX,GAAI5B,GAAale,KAAKiB,QAAQid,UAC9B,OAAOA,GAAWle,KAAK+f,iBAAmB7B,EAAWxf,SAEzDgX,QAAS,WACL1V,KAAKoN,QAAQgB,QACbpO,KAAKgf,KAAK5Q,SAEdwH,MAAO,WACH5V,KAAKgf,KAAKpJ,QACV5V,KAAK+f,eAAiB,EACtB/f,KAAK+e,UAETA,OAAQ,WACJ,GAAsFiB,GAAMva,EAAG8K,EAA3FxQ,EAAOC,KAAKwf,YAAaC,EAAiBzf,KAAKsf,iBAAiBtf,KAAK6N,QAAQ3B,GACjF,KAAKzG,EAAI,EAAGA,EAAI1F,EAAK0F,EAAGA,IACpB,IAAK8K,EAAI,EAAGA,EAAIxQ,EAAKwQ,EAAGA,IACpByP,EAAOhgB,KAAKigB,YACRxa,EAAGga,EAAeha,EAAIA,EACtB8K,EAAGkP,EAAelP,EAAIA,IAErByP,EAAK7J,SACN6J,EAAKzR,QAKrB0R,WAAY,SAAUC,GAAV,GACJjf,GAAUjB,KAAKmgB,YAAYD,GAC3BF,EAAOhgB,KAAKgf,KAAKpe,IAAIZ,KAAKof,QAASne,EAIvC,OAHqC,KAAjC+e,EAAK5S,QAAQwC,SAASlR,QACtBsB,KAAKoN,QAAQkB,OAAO0R,EAAK5S,SAEtB4S,GAEXG,YAAa,SAAUD,GACnB,GAAIL,GAAQ7f,KAAKogB,UAAUF,GAAexM,EAAQ1T,KAAK0f,aAAaQ,GAAe5B,EAASte,KAAKqf,YAAagB,EAAS3M,EAAM1K,QAAQsL,WAAWgK,EAAO7Y,GAAI6Y,EAAO/N,EAClK,QACIsP,MAAOA,EACPK,aAAcA,EACdxM,MAAOA,EACP2M,OAAQtC,EAAWsC,GACnBzS,KAAM5N,KAAK8N,MACX/N,KAAMC,KAAKiB,QAAQie,SACnBoB,UAAWtgB,KAAK8f,gBAChBX,YAAanf,KAAKiB,QAAQke,YAC1BoB,iBAAkBvgB,KAAKiB,QAAQsf,mBAGvCH,UAAW,SAAUP,GACjB,GAAIW,GAAWnZ,EAAK0K,IAAI,EAAG/R,KAAK8N;AAChC,OACIrI,EAAGzF,KAAKygB,UAAUZ,EAAMpa,EAAG+a,GAC3BjQ,EAAG6B,EAAMyN,EAAMtP,EAAG,EAAGiQ,EAAW,KAGxCC,UAAW,SAAUpgB,EAAOmgB,GACxB,GAAIE,GAAYrZ,EAAKE,IAAIlH,GAASmgB,CAMlC,OAJIngB,GADAA,GAAS,EACDqgB,EAEAF,GAA0B,IAAdE,EAAkBF,EAAWE,MAKzDC,EAAY/gB,EAAMC,QAClBC,KAAM,SAAUqG,EAAIlF,GAChBjB,KAAKmG,GAAKA,EACVnG,KAAKmW,SAAU,EACfnW,KAAKqN,aAAapM,GAClBjB,KAAKe,gBACLf,KAAKuO,QAETtN,SACIke,YAAa,GACboB,iBAAkB,IAEtBxf,cAAe,WACXf,KAAKoN,QAAU5P,EAAE,8DAAkEuX,KAC/ElW,MAAOmB,KAAKiB,QAAQlB,KACpBjB,OAAQkB,KAAKiB,QAAQlB,OACtB2P,GAAG,QAASJ,EAAM,SAAUoB,GACvB1Q,KAAK4gB,WACLlQ,EAAEmQ,OAAOC,aAAa,MAAO9gB,KAAK4gB,YAElClQ,EAAEmQ,OAAOE,gBAAgB,QAE9B/gB,QAEPuO,KAAM,WAAA,GAIEyS,GAHA5T,EAAUpN,KAAKoN,QAAQ,EAC3BA,GAAQnO,MAAMgiB,IAAMte,EAAW3C,KAAKiB,QAAQof,OAAO9P,GACnDnD,EAAQnO,MAAMiiB,KAAOve,EAAW3C,KAAKiB,QAAQof,OAAO5a,GAChDub,EAAMhhB,KAAKghB,MACXA,GACA5T,EAAQ0T,aAAa,MAAOE,GAEhC5T,EAAQnO,MAAMkiB,WAAa,UAC3BnhB,KAAKmW,SAAU,GAEnB3H,KAAM,WACFxO,KAAKoN,QAAQ,GAAGnO,MAAMkiB,WAAa,SACnCnhB,KAAKmW,SAAU,GAEnB6K,IAAK,WACD,GAAII,GAAYlU,EAASlN,KAAKiB,QAAQke,YACtC,OAAOiC,GAAUphB,KAAKqhB,eAE1BT,SAAU,WACN,GAAIQ,GAAYlU,EAASlN,KAAKiB,QAAQsf,iBACtC,OAAOa,GAAUphB,KAAKqhB,eAE1BA,WAAY,WACR,GAAIpgB,GAAUjB,KAAKiB,OACnB,QACI2M,KAAM3M,EAAQ2M,KACd0S,UAAWrf,EAAQqf,UACnBgB,EAAGrgB,EAAQ2M,KACXnI,EAAGxE,EAAQ4e,MAAMpa,EACjB8K,EAAGtP,EAAQ4e,MAAMtP,EACjBgR,EAAGtgB,EAAQqf,UACXkB,QAASvgB,EAAQugB,QACjBC,EAAGxgB,EAAQugB,QACXE,QAASzgB,EAAQygB,QACjB/b,EAAG1E,EAAQygB,UAGnBhM,QAAS,WACD1V,KAAKoN,UACLpN,KAAKoN,QAAQc,SACblO,KAAKoN,QAAU,SAIvB6R,EAAWrf,EAAMC,QACjBC,KAAM,WACFE,KAAK2hB,WAET1gB,SAAWmb,QAAS,KACpBxb,IAAK,SAAUyL,EAAQpL,GAInB,MAHIjB,MAAK2hB,OAAOjjB,QAAUsB,KAAKiB,QAAQmb,SACnCpc,KAAK4hB,QAAQvV,GAEVrM,KAAK6hB,QAAQ5gB,IAExBmN,MAAO,WAAA,GAEM5P,GADL8O,EAAQtN,KAAK2hB,MACjB,KAASnjB,EAAI,EAAGA,EAAI8O,EAAM5O,OAAQF,IAC9B8O,EAAM9O,GAAGkX,SAEb1V,MAAK2hB,WAET/L,MAAO,WAAA,GAEMpX,GADL8O,EAAQtN,KAAK2hB,MACjB,KAASnjB,EAAI,EAAGA,EAAI8O,EAAM5O,OAAQF,IAC9B8O,EAAM9O,GAAGgQ,QAGjBqT,QAAS,SAAU5gB,GAAV,GAED+e,GAEKxhB,EAHL8O,EAAQtN,KAAK2hB,OAEbxb,EAAKxG,EAAKrB,QAAQ2C,GAAAA,EAAQyS,OAAmBzS,GAAAA,EAAQof,QAAoBpf,EAAQ2M,KAAO3M,EAAQke,YACpG,KAAS3gB,EAAI,EAAGA,EAAI8O,EAAM5O,OAAQF,IAC9B,GAAI8O,EAAM9O,GAAG2H,KAAOA,EAAI,CACpB6Z,EAAO1S,EAAM9O,EACb,OASR,MANIwhB,GACAA,EAAKzR,QAELyR,EAAO,GAAIW,GAAUxa,EAAIlF,GACzBjB,KAAK2hB,OAAOxjB,KAAK6hB,IAEdA,GAEX4B,QAAS,SAAUvV,GAAV,GAII7N,GACDsjB,EAJJxU,EAAQtN,KAAK2hB,OACbI,KACAlC,IACJ,KAASrhB,EAAI,EAAGA,EAAI8O,EAAM5O,OAAQF,IAC1BsjB,EAAOxU,EAAM9O,GAAGyC,QAAQyS,MAAMvK,WAAWkD,GACzCyV,EAAOC,IAAYzU,EAAM9O,GAAG2X,UAC5B0J,EAAQrhB,EACRujB,EAAUD,EAGdjC,UACAvS,EAAMuS,GAAOnK,UACbpI,EAAM0U,OAAOnC,EAAO,MAOhCzd,GAAW2F,GACPzH,KACIiW,QACIyJ,KAAM/B,EACNA,UAAWA,EACX0C,UAAWA,EACX1B,SAAUA,EACVP,SAAUA,OAIxBjf,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,2BAA4B,2BAA4BD,IACjE,YACG,SAAUE,EAAG2J,GAAb,GACOzH,GAAQD,OAAOC,MAAOqI,EAAUrI,EAAMqI,QAAS3F,EAAa1C,EAAM0C,WAAY6F,EAAUvI,EAAMsI,QAAQrI,KAAKsI,QAASb,EAASW,EAAQzH,IAAI8G,OAAQkB,EAAWP,EAAQzH,IAAIgI,SAAU2V,EAAYlW,EAAQzH,IAAIiW,OAAO0H,UAAWS,EAAW3W,EAAQzH,IAAIiW,OAAOmI,SAC1PuD,EAAYhE,EAAUpe,QACtBC,KAAM,SAAUQ,EAAKW,GACjBjB,KAAKiB,QAAQihB,QAAUliB,KAAKmiB,UAAY,oDACxClE,EAAUrS,GAAG9L,KAAK+G,KAAK7G,KAAMM,EAAKW,GAClCjB,KAAKoiB,YAAc5kB,EAAE8R,MAAMtP,KAAKoiB,YAAapiB,MAC7CA,KAAKqiB,kBAETphB,SAAWqhB,WAAY,QACvBD,eAAgB,WACZ,GAAIphB,GAAUjB,KAAKiB,OACnB,KAAKA,EAAQhD,IACT,KAAUskB,OAAM,uCAEpB/kB,GAAEglB,MACExB,IAAK/f,EAAQihB,QAAUjhB,EAAQqhB,WAC/B7L,MACIgM,OAAQ,OACRjW,QAAS,mBACTvO,IAAKgD,EAAQhD,IACbykB,UAAW1iB,KAAKmiB,WAEpBnI,KAAM,MACN8B,SAAU,QACV6G,MAAO,QACPC,QAAS5iB,KAAKoiB,eAGtBD,QAAS,SAAUU,GAEf,MADAA,GAAQA,GAASpjB,OAAOyZ,SAAS4J,SACC,UAA3BD,EAAMjlB,QAAQ,IAAK,IAAkB,QAAU,QAE1DwkB,YAAa,SAAU3L,GAAV,GAEDsM,GAKA9hB,CANJwV,IAAQA,EAAKuM,aAAatkB,SACtBqkB,EAAW/iB,KAAK+iB,SAAWtM,EAAKuM,aAAa,GAAGC,UAAU,GAC9D7gB,EAAWpC,KAAKqe,MAAMpd,SAClBke,YAAa4D,EAASG,SAAStlB,QAAQ,cAAe,kBAAkBA,QAAQ,YAAa,gBAAgBA,QAAQ,YAAa,gBAClIsgB,WAAY6E,EAASI,qBAErBliB,EAAUjB,KAAKiB,QACdgH,EAAQhH,EAAQ0N,WACjB1N,EAAQ0N,QAAUoU,EAASK,SAE1Bnb,EAAQhH,EAAQ2N,WACjB3N,EAAQ2N,QAAUmU,EAASM,SAE/BrjB,KAAKsjB,kBAC+B,SAAhCtjB,KAAKoN,QAAQ2H,IAAI,YACjB/U,KAAKqV,WAIjB+I,UAAW,WACP,MAAOmF,IAEXD,gBAAiB,WAAA,GAGLhW,GAES9O,EACDyP,EACKsC,EACDtB,EAPhBa,EAAO9P,KAAKM,IAAIgW,WACpB,IAAIxG,IACIxC,EAAQtN,KAAK+iB,SAASS,kBAEtB,IAAShlB,EAAI,EAAGA,EAAI8O,EAAM5O,OAAQF,IAE9B,IADIyP,EAAOX,EAAM9O,GACR+R,EAAI,EAAGA,EAAItC,EAAKwV,cAAc/kB,OAAQ6R,IACvCtB,EAAOhB,EAAKwV,cAAclT,GAC9BT,EAAK9B,KACDtQ,KAAMuQ,EAAKqI,YACX3H,QAASM,EAAKmU,QACdxU,QAASK,EAAKoU,QACdvW,OAAQ,GAAI1F,GAAO,GAAIkB,GAAS2G,EAAKyU,KAAK,GAAIzU,EAAKyU,KAAK,IAAK,GAAIpb,GAAS2G,EAAKyU,KAAK,GAAIzU,EAAKyU,KAAK,QAO1HpB,WAAY,SAAUjiB,GAClB,MAAIA,IACAL,KAAKiB,QAAQqhB,WAAajiB,EAC1BL,KAAKM,IAAIgW,YAAYnI,QACrBnO,KAAKqiB,iBAFLriB,GAIOA,KAAKiB,QAAQqhB,cAI5BiB,EAAW7E,EAAS7e,QACpBoB,SAAWygB,QAAS,SACpBvB,YAAa,SAAUD,GACnB,GAAIjf,GAAUyd,EAAS9S,GAAGuU,YAAYtZ,KAAK7G,KAAMkgB,EAGjD,OAFAjf,GAAQygB,QAAU1hB,KAAKiB,QAAQygB,QAC/BzgB,EAAQugB,QAAUxhB,KAAK2jB,YAAY3jB,KAAKogB,UAAUF,IAC3Cjf,GAEX0iB,YAAa,SAAU9D,GACnB,GAAkB+D,GAAOC,EAAMrlB,EAA3BslB,EAAU,EACd,KAAKtlB,EAAIwB,KAAK8N,MAAOtP,EAAI,EAAGA,IACxBolB,EAAQ,EACRC,EAAO,GAAKrlB,EAAI,EACS,KAApBqhB,EAAMpa,EAAIoe,IACXD,IAEqB,KAApB/D,EAAMtP,EAAIsT,KACXD,GAAS,GAEbE,GAAWF,CAEf,OAAOE,KAGf1hB,GAAW2F,GACPzH,KACIiW,QACIwN,KAAM9B,EACNA,UAAWA,EACXsB,SAAUA,OAIxB9jB,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,6BACH,0BACA,uBACA,aACA,iBACDD,IACL,YACG,SAAUE,EAAG2J,GAAb,GACO6c,GAAMljB,SAAUuG,EAAOC,KAAM2c,EAAUzmB,EAAE0mB,QAAS5U,EAAQ9R,EAAE8R,MAAO5P,EAAQD,OAAOC,MAAOE,EAAQF,EAAME,MAAO4W,EAAa9W,EAAM+W,KAAKD,WAAY2N,EAAUzkB,EAAMuN,GAAGkX,QAASpc,EAAUrI,EAAMqI,QAAS3F,EAAa1C,EAAM0C,WAAY9B,EAAMyH,EAAQzH,IAAKgI,EAAWhI,EAAIgI,SAAUwM,EAAQxU,EAAIiW,OAAOzB,MACrSsP,EAActP,EAAMjV,QACpBC,KAAM,SAAUQ,EAAKW,GACjB6T,EAAMlJ,GAAG9L,KAAK+G,KAAK7G,KAAMM,EAAKW,GAC9BjB,KAAKqkB,aAAe/U,EAAMtP,KAAKqkB,aAAcrkB,MAC7CA,KAAKoN,QAAQsC,GAAG,QAAS,YAAa1P,KAAKqkB,cAC3CrkB,KAAKsN,SACLtN,KAAKsX,mBAET5B,QAAS,WACLZ,EAAMlJ,GAAG8J,QAAQ7O,KAAK7G,MACtBA,KAAKoN,QAAQiD,IAAI,QAAS,YAAarQ,KAAKqkB,cAC5CrkB,KAAKwX,WAAWnB,OAAO,SAAUrW,KAAKyX,aACtCzX,KAAKmO,SAETlN,SACI+T,OAAQ,IACRuC,UAAU,EACVC,cACAyE,cAAe,WACfqI,WAAY,SAEhBtW,IAAK,SAAUuW,GACX,IAAI/mB,EAAEgnB,QAAQD,GAKV,MAAOvkB,MAAKykB,QAAQF,EAJpB,KAAK,GAAI/lB,GAAI,EAAGA,EAAI+lB,EAAI7lB,OAAQF,IAC5BwB,KAAKykB,QAAQF,EAAI/lB,KAM7B0P,OAAQ,SAAU/L,GACdA,EAAOuT,SACP,IAAImK,GAAQoE,EAAQ9hB,EAAQnC,KAAKsN,MAC7BuS,OACA7f,KAAKsN,MAAM0U,OAAOnC,EAAO,IAGjC1R,MAAO,WACH,IAAK,GAAI3P,GAAI,EAAGA,EAAIwB,KAAKsN,MAAM5O,OAAQF,IACnCwB,KAAKsN,MAAM9O,GAAGkX,SAElB1V,MAAKsN,UAEToX,OAAQ,SAAUviB,GAAV,GAIIwW,GAHJ5P,EAAM5G,EAAO+W,UACbnQ,KACA5G,EAAOwiB,OAAO3kB,KAAKM,IAAIiZ,eAAexQ,IAClC4P,GACAxW,OAAQA,EACR4W,MAAO/Y,MAEXA,KAAKM,IAAIkQ,QAAQ,iBAAkBmI,KAG3CtD,OAAQ,WAAA,GAEA/H,GACK9O,CAAT,KAFAsW,EAAMlJ,GAAGyJ,OAAOxO,KAAK7G,MACjBsN,EAAQtN,KAAKsN,MACR9O,EAAI,EAAGA,EAAI8O,EAAM5O,OAAQF,IAC9BwB,KAAK0kB,OAAOpX,EAAM9O,KAG1B4X,KAAM,SAAUnV,EAASkY,GAAnB,GAGER,GAIAC,EANAzW,EAAS7B,EAAIskB,OAAO1e,OAAOjF,EAASjB,KAAKiB,QAO7C,IANAkB,EAAOgX,SAAWA,EACdR,GACAxW,OAAQA,EACR4W,MAAO/Y,MAEP4Y,EAAY5Y,KAAKM,IAAIkQ,QAAQ,gBAAiBmI,IAC7CC,EAED,MADA5Y,MAAKgO,IAAI7L,GACFA,GAGfuV,cAAe,SAAUF,GACjBxX,KAAKwX,YACLxX,KAAKwX,WAAWnB,OAAO,SAAUrW,KAAKyX,aAE1CzX,KAAKwX,WAAa9X,EAAM+W,KAAKD,WAAWtQ,OAAOsR,GAC/CxX,KAAKwX,WAAWpB,KAAK,SAAUpW,KAAKyX,aAChCzX,KAAKiB,QAAQsW,UACbvX,KAAKwX,WAAWG,SAGxB8M,QAAS,SAAUF,GACf,GAAIpiB,GAASyiB,EAAO1e,OAAOqe,EAAKvkB,KAAKiB,QAErC,OADAkB,GAAO0iB,MAAM7kB,MACNmC,GAEXmV,gBAAiB,WACb,GAAIW,GAAYjY,KAAKiB,QAAQuW,UAC7BxX,MAAKyX,YAAcnI,EAAMtP,KAAKyX,YAAazX,MAC3CA,KAAKwX,WAAahB,EAAWtQ,OAAO+R,GAAW7B,KAAK,SAAUpW,KAAKyX,aAC/DQ,GAAajY,KAAKiB,QAAQsW,UAC1BvX,KAAKwX,WAAWG,SAGxBF,YAAa,SAAU/G,GACnB1Q,KAAK8X,MAAMpH,EAAEwH,OAAOC,SAExBL,MAAO,SAAUrB,GAAV,GAGCqO,GACAC,EACKvmB,EACD2a,CADR,KAJAnZ,KAAK6X,MAAQpB,EACbzW,KAAKmO,QACD2W,EAAcplB,EAAMqc,OAAO/b,KAAKiB,QAAQgb,eACxC8I,EAAWrlB,EAAMqc,OAAO/b,KAAKiB,QAAQqjB,YAChC9lB,EAAI,EAAGA,EAAIiY,EAAK/X,OAAQF,IACzB2a,EAAW1C,EAAKjY,GACpBwB,KAAKoW,MACD8C,SAAU4L,EAAY3L,GACtB6L,MAAOD,EAAS5L,IACjBA,IAGXkL,aAAc,SAAU3T,GACpB,GAAIiI,IACAxW,OAAQ3E,EAAEkT,EAAEmQ,QAAQpK,KAAK,eACzBsC,MAAO/Y,KAEXA,MAAKM,IAAIkQ,QAAQ,cAAemI,MAGpCiM,EAAShlB,EAAMC,QACfC,KAAM,SAAUmB,GACZjB,KAAKiB,QAAUA,OAEnB4jB,MAAO,SAAUjV,GACb5P,KAAK+Y,MAAQnJ,EAAOqJ,SAAWrJ,EAC/B5P,KAAK+Y,MAAMzL,MAAMnP,KAAK6B,MACtBA,KAAK+Y,MAAM2L,OAAO1kB,OAEtBkZ,SAAU,SAAU7Y,GAChB,MAAIA,IACAL,KAAKiB,QAAQiY,SAAW5Q,EAASpC,OAAO7F,GAAOwI,UAC3C7I,KAAK+Y,OACL/Y,KAAK+Y,MAAM2L,OAAO1kB,MAEfA,MAEAsI,EAASpC,OAAOlG,KAAKiB,QAAQiY,WAG5CyL,OAAQ,SAAUjR,GACd1T,KAAK+e,SACL/e,KAAKoN,QAAQ2H,KACTmM,KAAM7Z,EAAKe,MAAMsL,EAAMjO,GACvBwb,IAAK5Z,EAAKe,MAAMsL,EAAMnD,KAEtBvQ,KAAKilB,SAAWjlB,KAAKilB,QAAQC,OAC7BllB,KAAKilB,QAAQC,MAAMC,aAG3B3W,KAAM,WACExO,KAAKoN,UACLpN,KAAKoN,QAAQc,SACblO,KAAKoN,QAAU,MAEfpN,KAAKilB,UACLjlB,KAAKilB,QAAQvP,UACb1V,KAAKilB,QAAU,OAGvBvP,QAAS,WACL1V,KAAK+Y,MAAQ,KACb/Y,KAAKwO,QAETuQ,OAAQ,WAAA,GAEI9d,GACA8X,CAFH/Y,MAAKoN,UACFnM,EAAUjB,KAAKiB,QACf8X,EAAQ/Y,KAAK+Y,MACjB/Y,KAAKoN,QAAU5P,EAAEwmB,EAAIjjB,cAAc,SAASwM,SAAS,8BAAgC7N,EAAMqD,UAAU9B,EAAQoX,OAAS,QAAQvI,KAAK,QAAS7O,EAAQ+jB,OAAOlV,KAAK7O,EAAQmkB,gBAAkB3O,KAAK,cAAezW,MAAM+U,IAAI,SAAU9T,EAAQ+T,QACtO+D,GACAA,EAAM3L,QAAQkB,OAAOtO,KAAKoN,SAE9BpN,KAAKqlB,kBAGbA,cAAe,WAAA,GAKHnY,GAEIoY,EANRnjB,EAASnC,KACTglB,EAAQ7iB,EAAOlB,QAAQ+jB,MACvB/jB,EAAUkB,EAAOlB,QAAQgkB,WACzBhkB,IAAWkjB,IACPjX,EAAWjM,EAAQiM,SACnBA,IACIoY,EAAkB5lB,EAAMwN,SAASA,GACrCjM,EAAQskB,QAAU,SAAU7U,GAGxB,MAFAA,GAAEwI,SAAW/W,EAAO+W,WACpBxI,EAAEvO,OAASA,EACJmjB,EAAgB5U,MAG3BsU,GAAS/jB,EAAQskB,SAAWtkB,EAAQukB,cACpCxlB,KAAKilB,QAAU,GAAId,GAAQnkB,KAAKoN,QAASnM,GACzCjB,KAAKilB,QAAQ9iB,OAASnC,SAKtC4kB,GAAO1e,OAAS,SAAUqe,EAAKkB,GAC3B,MAAIlB,aAAeK,GACRL,EAEJ,GAAIK,GAAOxiB,KAAeqjB,EAAUlB,KAE/CniB,EAAW2F,GACPzH,KACIiW,QACIpU,OAAQiiB,EACRA,YAAaA,GAEjBQ,OAAQA,MAGlBnlB,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,oBACH,kBACA,wBACDD,IACL,YACG,SAAUE,EAAG2J,GAAb,GACO6c,GAAMljB,SAAUuG,EAAOC,KAAMM,EAAMP,EAAKO,IAAKmK,EAAM1K,EAAK0K,IAAKzC,EAAQ9R,EAAE8R,MAAO5P,EAAQD,OAAOC,MAAOsN,EAAStN,EAAMuN,GAAGD,OAAQ5K,EAAa1C,EAAM0C,WAAY2F,EAAUrI,EAAMqI,QAASkF,EAAKlF,EAAQkF,GAAIgF,EAAIvS,EAAMwS,SAAUC,EAAQF,EAAEE,MAAO7R,EAAMyH,EAAQzH,IAAK8G,EAAS9G,EAAI8G,OAAQkB,EAAWhI,EAAIgI,SAAU2L,EAAW3T,EAAI4T,IAAID,SAAUtU,EAAOD,EAAMC,KAAMiD,EAAYjD,EAAKiD,UAAWob,EAActe,EAAMsI,QAAQrI,KAAMsI,EAAU+V,EAAY/V,QAASmK,EAAQ4L,EAAY3L,WAAYhK,EAAiB2V,EAAY3V,eACvfqd,EAAa,KAAMC,EAAW,GAAKC,EAAkB,IAAMC,EAAa,4BAA6BC,EAAsB,EAAGC,EAAoB,EAClJC,EAAMhZ,EAAOnN,QACbC,KAAM,SAAUsN,EAASnM,GACrBvB,EAAMgW,QAAQtI,GACdJ,EAAOpB,GAAG9L,KAAK+G,KAAK7G,KAAMoN,GAC1BpN,KAAKqN,aAAapM,GAClBjB,KAAKoW,KAAKpW,KAAKmQ,OAAQlP,GACvBjB,KAAKkU,IAAM,GAAID,GACfjU,KAAKoN,QAAQG,SAASmY,EAAa1lB,KAAKiB,QAAQuM,KAAKhI,eAAeuP,IAAI,WAAY,YAAY3G,QAAQE,OAAO0V,EAAIjjB,cAAc,QACjIf,KAAKqf,YAAcrf,KAAKimB,aACxBjmB,KAAKkmB,gBACLlmB,KAAKmmB,eACLnmB,KAAKomB,gBACLpmB,KAAKqmB,cACLrmB,KAAKqV,SACLrV,KAAKsmB,YAAchX,EAAMtP,KAAKsmB,YAAatmB,MAC3CA,KAAKoN,QAAQgJ,KAAKyP,EAAY7lB,KAAKsmB,cAEvCrlB,SACIuM,KAAM,MACN+Y,UACIjQ,aAAa,EACbkQ,WAAatW,QAAS,KACtBtC,MAAM,GAEV2I,UACAkQ,eACIpO,OACIpZ,OACI8b,MAAQ2L,MAAO,QACfC,QACID,MAAO,OACP7nB,MAAO,MAInBif,QACI7e,OACI8b,MACI2L,MAAO,OACPzR,QAAS,IAEb0R,QACID,MAAO,OACP7nB,MAAO,MAInBsD,QACIkW,MAAO,YACP4M,SAAW2B,SAAU,SAG7Bva,QACI,EACA,GAEJuB,KAAM,EACNuO,QAAS,IACTxN,QAAS,EACTC,QAAS,GACTqK,WACA4N,gBACIxO,MAAO,YACP4M,SAAW2B,SAAU,QAEzBE,YAAY,GAEhB3W,QACI,cACA,QACA,iBACA,cACA,gBACA,MACA,SACA,QACA,aACA,eACA,sBACA,kBACA,kBACA,UACA,aAEJuF,QAAS,WACL1V,KAAK+mB,SAASrR,UACV1V,KAAKwmB,WACLxmB,KAAKwmB,UAAU9Q,UAEf1V,KAAKsW,aACLtW,KAAKsW,YAAYZ,UAEjB1V,KAAKgnB,aACLhnB,KAAKgnB,YAAYtR,UAErB1V,KAAKiZ,QAAQvD,SACb,KAAK,GAAIlX,GAAI,EAAGA,EAAIwB,KAAKuW,OAAO7X,OAAQF,IACpCwB,KAAKuW,OAAO/X,GAAGkX,SAEnB1I,GAAOpB,GAAG8J,QAAQ7O,KAAK7G,OAE3B4N,KAAM,SAAUqZ,GACZ,GAAIhmB,GAAUjB,KAAKiB,OACnB,OAAIgH,GAAQgf,IACRA,EAAQ5f,EAAKe,MAAMgK,EAAM6U,EAAOhmB,EAAQ0N,QAAS1N,EAAQ2N,UACrD3N,EAAQ2M,OAASqZ,IACjBhmB,EAAQ2M,KAAOqZ,EACfjnB,KAAKqV,UAEFrV,MAEAiB,EAAQ2M,MAGvBvB,OAAQ,SAAUA,GACd,MAAIA,IACArM,KAAKiB,QAAQoL,OAAS/D,EAASpC,OAAOmG,GAAQxD,UAC9C7I,KAAKqV,SACErV,MAEAsI,EAASpC,OAAOlG,KAAKiB,QAAQoL,SAG5CS,OAAQ,SAAUA,GACd,MAAIA,IACA9M,KAAKknB,WAAWpa,GACT9M,MAEAA,KAAKmnB,cAGpBC,WAAY,SAAUnmB,GAClB+L,EAAOpB,GAAGwb,WAAWvgB,KAAK7G,KAAMiB,GAChCjB,KAAKqV,UAETkJ,gBAAiB,SAAUrF,EAAUtL,GACjC,GAAIqF,IAASjT,KAAKiB,QAAQ6lB,UAE1B,OADA5N,GAAW5Q,EAASpC,OAAOgT,GACpBlZ,KAAKkU,IAAIO,QAAQyE,EAAUlZ,KAAKqnB,WAAWzZ,GAAOqF,IAE7DqU,gBAAiB,SAAU5T,EAAO9F,GAC9B,GAAIqF,IAASjT,KAAKiB,QAAQ6lB,UAE1B,OADApT,GAAQvB,EAAMjM,OAAOwN,GACd1T,KAAKkU,IAAIQ,WAAWhB,EAAO1T,KAAKqnB,WAAWzZ,GAAOqF,IAE7DsG,eAAgB,SAAUL,GAAV,GAERoF,GACA5K,CACJ,OAHAwF,GAAW5Q,EAASpC,OAAOgT,GACvBoF,EAASte,KAAKue,gBAAgBve,KAAKqf,aACnC3L,EAAQ1T,KAAKue,gBAAgBrF,GAC1BxF,EAAM6T,cAAcjJ,EAAO/J,YAEtCiT,eAAgB,SAAU9T,EAAO9F,GAC7B,GAAI0Q,GAASte,KAAKue,gBAAgBve,KAAKimB,aAAcrY,EAGrD,OAFA8F,GAAQvB,EAAMjM,OAAOwN,GACrBA,EAAQA,EAAM1K,QAAQue,cAAcjJ,GAC7Bte,KAAKsnB,gBAAgB5T,EAAO9F,IAEvC6Z,YAAa,SAAU/W,GAAV,GACLgD,GACAjO,EACA8K,EAGImX,EAKAjO,EAPJ4G,EAASrgB,KAAKoN,QAAQiT,QAY1B,OAXI3P,GAAEjL,GAAKiL,EAAEH,GACLmX,EAAQ,WACZjiB,EAAIiL,EAAEjL,EAAEiiB,GAASrH,EAAOa,KACxB3Q,EAAIG,EAAEH,EAAEmX,GAASrH,EAAOY,IACxBvN,EAAQ,GAAIzB,GAAEE,MAAM1M,EAAG8K,KAEnBkJ,EAAQ/I,EAAEgJ,eAAiBhJ,EAC/BjL,EAAI4C,EAAeoR,EAAMkO,MAAOlO,EAAMmO,SAAWvH,EAAOa,KACxD3Q,EAAIlI,EAAeoR,EAAMoO,MAAOpO,EAAMqO,SAAWzH,EAAOY,IACxDvN,EAAQ,GAAIzB,GAAEE,MAAM1M,EAAG8K,IAEpBmD,GAEXqU,YAAa,SAAUrX,GACnB,GAAIsX,GAAShoB,KAAKynB,YAAY/W,EAC9B,OAAO1Q,MAAKuZ,eAAevZ,KAAKwnB,eAAeQ,KAEnDC,aAAc,SAAUvX,GACpB,MAAO1Q,MAAKue,gBAAgBve,KAAKkoB,gBAAgBxX,KAErDwX,gBAAiB,SAAUxX,GACvB,GAAIsX,GAAShoB,KAAKynB,YAAY/W,EAC9B,OAAO1Q,MAAKwnB,eAAeQ,IAE/BG,SAAU,WAAA,GACF/a,GAAUpN,KAAKoN,QACfmH,EAAQvU,KAAKqnB,aACbxoB,EAAQuO,EAAQvO,OAIpB,OAHKmB,MAAKiB,QAAQ6lB,aACdjoB,EAAQ+I,EAAI2M,EAAO1V,KAGnBA,MAAOA,EACPC,OAAQ8I,EAAI2M,EAAOnH,EAAQtO,YAGnCspB,aAAc,WAEV,MADApoB,MAAKqV,UACE,GAEXgT,WAAY,SAAU/J,EAAQ1Q,GAC1B,GAA4B0a,GAAxBvoB,EAAOC,KAAKmoB,UAMhB,OALA7J,GAASte,KAAKuoB,QAAUjgB,EAASpC,OAAOoY,GACxCgK,EAAUtoB,KAAKue,gBAAgBD,EAAQ1Q,GACvC0a,EAAQ7iB,GAAK1F,EAAKlB,MAAQ,EAC1BypB,EAAQ/X,GAAKxQ,EAAKjB,OAAS,EAC3BkB,KAAKiB,QAAQoL,OAASrM,KAAKsnB,gBAAgBgB,EAAS1a,GAAM/E,UACnD7I,MAEXimB,WAAY,SAAUuC,GAClB,GAA4BF,GAAxBvoB,EAAOC,KAAKmoB,UAOhB,QANIK,GAAexoB,KAAKuoB,UACpBD,EAAUtoB,KAAKue,gBAAgBve,KAAKqM,UACpCic,EAAQ7iB,GAAK1F,EAAKlB,MAAQ,EAC1BypB,EAAQ/X,GAAKxQ,EAAKjB,OAAS,EAC3BkB,KAAKuoB,QAAUvoB,KAAKsnB,gBAAgBgB,IAEjCtoB,KAAKuoB,SAEhBrB,WAAY,SAAUpa,GAAV,GAQJjO,GACAC,EACK8O,EACD0a,EACAG,EACAC,EACAC,EAbJC,EAAMxhB,EAAOlB,OAAO4G,GACpBX,EAAKyc,EAAIzc,GAAGnD,OAQhB,KAPIhJ,KAAKiB,QAAQ6lB,YAAc3a,EAAG3D,IAAM,GAAKsE,EAAOZ,GAAG1D,IAAM,IACzD2D,EAAG3D,IAAM,KAAO,IAAM2D,EAAG3D,MAE7BsE,EAAS,GAAI1F,GAAOwhB,EAAI1c,GAAIC,GAC5BnM,KAAKqM,OAAOS,EAAOT,UACfxN,EAAQmB,KAAKoN,QAAQvO,QACrBC,EAASkB,KAAKoN,QAAQtO,SACjB8O,EAAO5N,KAAKiB,QAAQ2N,QAAShB,GAAQ5N,KAAKiB,QAAQ0N,UACnD2Z,EAAUtoB,KAAKue,gBAAgBzR,EAAOZ,GAAI0B,GAC1C6a,EAAczoB,KAAKue,gBAAgBzR,EAAOX,GAAIyB,GAC9C8a,EAAarhB,EAAKE,IAAIkhB,EAAYhjB,EAAI6iB,EAAQ7iB,GAC9CkjB,EAActhB,EAAKE,IAAIkhB,EAAYlY,EAAI+X,EAAQ/X,KAC/CmY,GAAc7pB,GAAS8pB,GAAe7pB,IALsB8O,KASpE5N,KAAK4N,KAAKA,IAEduZ,WAAY,WAAA,GAMJhb,GALAD,EAAKlM,KAAKimB,aACVwC,EAAczoB,KAAKue,gBAAgBrS,GACnCnM,EAAOC,KAAKmoB,UAIhB,OAHAM,GAAYhjB,GAAK1F,EAAKlB,MACtB4pB,EAAYlY,GAAKxQ,EAAKjB,OAClBqN,EAAKnM,KAAKsnB,gBAAgBmB,GACvB,GAAIrhB,GAAO8E,EAAIC,IAE1B0c,YAAa,SAAUC,EAAO7B,GAC1BjnB,KAAKqoB,WAAWroB,KAAKsnB,gBAAgBwB,EAAO7B,GAAQA,GACpDjnB,KAAK4N,KAAKqZ,IAEdb,cAAe,WACX,GAAIG,GAAWvmB,KAAKiB,QAAQslB,QACxBtZ,GAAGE,aAAeoZ,EAASjQ,aAC3BtW,KAAK+oB,mBAAmBxC,EAASjQ,aAEhC5W,EAAMif,QAAQC,WACX3R,EAAGwC,WAAa8W,EAASC,WACzBxmB,KAAKgpB,iBAAiBzC,EAASC,WAE/BvZ,EAAGuE,aAAe+U,EAAS3Y,MAC3B5N,KAAKipB,mBAAmB1C,EAAS3Y,QAI7Csb,sBAAuB,SAAUjoB,EAASkoB,GAAnB,GACftmB,GAAM5B,EAAQ2lB,UAAYuC,EAC1BC,EAAc,IAAMxmB,EAAUC,GAAKjF,QAAQ,IAAK,KAChDsL,EAAO1L,EAAE,kBAAoB4rB,EAAappB,KAAKoN,QAInD,OAHoB,KAAhBlE,EAAKxK,SACLwK,EAAO1L,EAAE,SAAS+P,SAAS,kBAAoB3K,EAAUC,IAAMqS,SAASlV,KAAKoN,UAE1E5P,EAAE,SAAS0X,SAAShM,IAE/B6f,mBAAoB,SAAU9nB,GAC1B,GAAImM,GAAUpN,KAAKkpB,sBAAsBjoB,EAAS,cAClDjB,MAAKsW,YAAc,GAAIrJ,GAAGE,YAAYC,EAASnM,IAEnD+nB,iBAAkB,SAAU/nB,GAAV,GACVmM,GAAUpN,KAAKkpB,sBAAsBjoB,EAAS,WAC9CulB,EAAYxmB,KAAKwmB,UAAY,GAAIvZ,GAAGwC,UAAUrC,EAASnM,EAC3DjB,MAAKqpB,cAAgB/Z,EAAMtP,KAAKqpB,cAAerpB,MAC/CwmB,EAAUpQ,KAAK,MAAOpW,KAAKqpB,eAC3BrpB,KAAKspB,iBAAmBha,EAAMtP,KAAKspB,iBAAkBtpB,MACrDwmB,EAAUpQ,KAAK,SAAUpW,KAAKspB,mBAElCD,cAAe,SAAU3Y,GAAV,GACPpQ,GAAMN,KACN+mB,EAAWzmB,EAAIymB,SACfthB,EAAIshB,EAASwC,WAAa7Y,EAAEjL,EAC5B8K,EAAIwW,EAASyC,UAAY9Y,EAAEH,EAC3BkZ,EAASzpB,KAAK0pB,aACd5qB,EAASkB,KAAKoN,QAAQtO,SACtBD,EAAQmB,KAAKoN,QAAQvO,OACzB4G,GAAI2M,EAAM3M,EAAGgkB,EAAOhkB,EAAEmC,IAAK6hB,EAAOhkB,EAAEkC,IAAM9I,GAC1C0R,EAAI6B,EAAM7B,EAAGkZ,EAAOlZ,EAAE3I,IAAK6hB,EAAOlZ,EAAE5I,IAAM7I,GAC1CwB,EAAIymB,SAAS4C,IAAI,SAAU,SAAUjZ,GACjCpQ,EAAIspB,WAAWlZ,KAEnBpQ,EAAIymB,SAAS8C,UAAUpkB,GAAI8K,IAE/B+Y,iBAAkB,WACdtpB,KAAKqM,OAAOrM,KAAKiB,QAAQoL,SAE7B4c,mBAAoB,SAAUhoB,GAAV,GACZmM,GAAUpN,KAAKkpB,sBAAsBjoB,EAAS,WAC9C+lB,EAAchnB,KAAKgnB,YAAc,GAAI/Z,GAAGuE,YAAYpE,EAASnM,EACjEjB,MAAK8pB,mBAAqBxa,EAAMtP,KAAK8pB,mBAAoB9pB,MACzDgnB,EAAY5Q,KAAK,SAAUpW,KAAK8pB,qBAEpCA,mBAAoB,SAAUpZ,GACrB1Q,KAAKwQ,QAAQ,aAAekJ,cAAehJ,MAC5C1Q,KAAK4N,KAAK5N,KAAK4N,OAAS8C,EAAEiB,OAC1B3R,KAAKwQ,QAAQ,WAAakJ,cAAehJ,MAGjDwV,cAAe,WAAA,GACP6D,GAAWrqB,EAAMif,QAAQC,SAAWgH,EAAkBD,EACtDqE,EAAWhqB,KAAKiB,QAAQ+oB,YAAa,EACrCjD,EAAW/mB,KAAK+mB,SAAW,GAAIrnB,GAAMuqB,OAAOhd,GAAGid,SAASlqB,KAAKoN,QAAQ+M,SAAS,IAC9E4P,SAAUA,EACVI,mBAAoBrE,EACpBlY,KAAMoc,EACNI,qBAAqB,EACrBC,kBAAkB,GAEtBtD,GAAS3Q,KAAK,SAAU9G,EAAMtP,KAAKsqB,QAAStqB,OAC5C+mB,EAAS3Q,KAAK,YAAa9G,EAAMtP,KAAK4pB,WAAY5pB,OAClD+mB,EAASwD,WAAWnU,KAAK,eAAgB9G,EAAMtP,KAAKwqB,YAAaxqB,OACjE+mB,EAASwD,WAAWnU,KAAK,aAAc9G,EAAMtP,KAAKyqB,OAAQzqB,OAC1D+mB,EAASwD,WAAWnU,KAAK,YAAa9G,EAAMtP,KAAK0qB,WAAY1qB,OAC7D+mB,EAASwD,WAAWnU,KAAK,MAAO9G,EAAMtP,KAAK2qB,KAAM3qB,OACjDA,KAAKmV,cAAgB4R,EAAS5R,eAElCkR,YAAa,WAAA,GAEA7nB,GACDyC,EACA+Y,EACAyL,EACAmF,EALJC,EAAO7qB,KAAKiB,QAAQsV,OAAQA,EAASvW,KAAKuW,SAC9C,KAAS/X,EAAI,EAAGA,EAAIqsB,EAAKnsB,OAAQF,IACzByC,EAAU4pB,EAAKrsB,GACfwb,EAAO/Y,EAAQ+Y,MAAQ,QACvByL,EAAWzlB,KAAKiB,QAAQwlB,cAAczM,GACtC4Q,EAAO7iB,EAAQzH,IAAIiW,OAAOyD,GAC9BzD,EAAOpY,KAAK,GAAIysB,GAAK5qB,KAAMoC,KAAeqjB,EAAUxkB,MAG5DklB,aAAc,WACVnmB,KAAKiZ,QAAU,GAAI3Y,GAAIiW,OAAO6N,YAAYpkB,KAAMA,KAAKiB,QAAQ4lB,gBAC7D7mB,KAAKiZ,QAAQjL,IAAIhO,KAAKiB,QAAQgY,UAElCqR,QAAS,SAAU5Z,GAAV,GACD4N,GAASte,KAAKue,gBAAgBve,KAAKqf,aAAajX,QAChD4O,EAAUtG,EAAEwH,OAAOlB,QACnBqJ,EAAS,GAAIpO,GAAEE,MAAM6E,EAAQvR,EAAGuR,EAAQzG,GAAGgE,UAAUA,MAAM,EAAIyC,EAAQzC,MAC3E+J,GAAO7Y,GAAK4a,EAAO5a,EACnB6Y,EAAO/N,GAAK8P,EAAO9P,EACnBvQ,KAAK8qB,cAAgBzK,EACrBrgB,KAAKqoB,WAAWroB,KAAKsnB,gBAAgBhJ,IACrCte,KAAKwQ,QAAQ,OACTkJ,cAAehJ,EACf4N,OAAQte,KAAKimB,aACb5Z,OAAQrM,KAAKqM,YAGrBud,WAAY,SAAUlZ,GACb1Q,KAAK8qB,eAAkB9qB,KAAK+qB,iBAGjC/qB,KAAK8qB,cAAgB,KACrB9qB,KAAKgrB,UAAY,GAAIhkB,MACrBhH,KAAKwQ,QAAQ,UACTkJ,cAAehJ,EACf4N,OAAQte,KAAKimB,aACb5Z,OAAQrM,KAAKqM,aAGrB0e,aAAc,WACV,MAAO,IAAI/jB,OAAUhH,KAAKgrB,WAAa,GAAK,IAEhDR,YAAa,SAAU9Z,GACnB,GAAI1Q,KAAKwQ,QAAQ,aAAekJ,cAAehJ,IAAM,CACjD,GAAIua,GAAQva,EAAEwa,QAAQ,EAClBD,IACAA,EAAME,WAIlBV,OAAQ,SAAU/Z,GAAV,GACA6D,GAAQvU,KAAK+mB,SAAS/P,QAAQzC,MAC9B3G,EAAO5N,KAAKorB,aAAa7W,GACzB8W,EAAgB,GAAIpZ,GAAEE,MAAMzB,EAAErE,OAAO5G,EAAGiL,EAAErE,OAAOkE,GACjD+a,EAAiBtrB,KAAKwnB,eAAe6D,EAAezd,GACpD2d,EAAcvrB,KAAKue,gBAAgB+M,EAAgB1d,GACnD4d,EAAcD,EAAYjX,WAAW+W,EAAc5lB,GAAI4lB,EAAc9a,EACzEvQ,MAAK6oB,YAAY2C,EAAa5d,GAC9B5N,KAAKwQ,QAAQ,WAAakJ,cAAehJ,KAE7C0a,aAAc,SAAUK,GAAV,GACNlX,GAAQvU,KAAKqnB,aAAeoE,EAC5BC,EAAQnX,EAAQvU,KAAKiB,QAAQkb,QAC7BvO,EAAOvG,EAAK2K,IAAI0Z,GAASrkB,EAAK2K,IAAI,EACtC,OAAO3K,GAAKe,MAAMwF,IAEtByH,OAAQ,WACArV,KAAKsW,aACLtW,KAAKsW,YAAY3I,OAAO3N,KAAKqM,SAAUrM,KAAK4N,QAEhD5N,KAAKqf,YAAcrf,KAAKimB,YAAW,GACnCjmB,KAAK2rB,iBACL3rB,KAAKwQ,QAAQ,eACbxQ,KAAKwQ,QAAQ,UAEjBmb,eAAgB,WAAA,GAUR/d,GAGAge,EAIAC,EASI1D,EAzBJpB,EAAW/mB,KAAK+mB,SAChBthB,EAAIshB,EAAS+E,WAAWrmB,EACxB8K,EAAIwW,EAAS+E,WAAWvb,EACxBgE,EAAQvU,KAAKqnB,aACbnb,EAAKlM,KAAK8M,SAASZ,GACnBoc,EAAUtoB,KAAKue,gBAAgBrS,GAAI9D,OACvC2e,GAAS/P,QAAQ5O,OAAQ,EACzB2e,EAASnR,QACTmR,EAASwD,WAAWY,SAChBvd,EAAO5N,KAAK4N,OAChBmZ,EAAS+E,WAAWC,eAAiBha,EAAI,EAAG/R,KAAKiB,QAAQ0N,QAAUf,GACnEmZ,EAAS+E,WAAWE,SAAWja,EAAI,EAAG/R,KAAKiB,QAAQ2N,QAAUhB,GACzDge,GACAhkB,KAAM0gB,EAAQ7iB,EACdkC,IAAK4M,EAAQ+T,EAAQ7iB,GAErBomB,GACAjkB,KAAM0gB,EAAQ/X,EACd5I,IAAK4M,EAAQ+T,EAAQ/X,GAErBvQ,KAAKiB,QAAQ6lB,aACb8E,EAAQjkB,IAAM,GAAK4M,EACnBqX,EAAQhkB,KAAOgkB,EAAQjkB,KAEvB3H,KAAKiB,QAAQgrB,YAAa,IACtB9D,EAAWnoB,KAAKmoB,WACpByD,EAAQhkB,IAAMikB,EAAQjkB,IAAM,EAC5BgkB,EAAQjkB,IAAMwgB,EAAStpB,MACvBgtB,EAAQlkB,IAAMwgB,EAASrpB,QAE3B2G,EAAEymB,cACF3b,EAAE2b,cACFzmB,EAAE0mB,YAAYP,EAAQhkB,IAAKgkB,EAAQjkB,KACnC4I,EAAE4b,YAAYN,EAAQjkB,IAAKikB,EAAQlkB,KACnC3H,KAAK0pB,cACDjkB,EAAGmmB,EACHrb,EAAGsb,IAGXO,cAAe,WAAA,GAGF5tB,GACDyC,EACA+Y,EACAyL,EACAmF,EANJC,EAAO7qB,KAAKiB,QAAQsV,OAAQA,EAASvW,KAAKuW,UAAa8V,EAAarsB,KAAKqsB,UAE7E,KADAA,EAAWje,QACF5P,EAAI,EAAGA,EAAIqsB,EAAKnsB,OAAQF,IACzByC,EAAU4pB,EAAKrsB,GACfwb,EAAO/Y,EAAQ+Y,MAAQ,QACvByL,EAAWzlB,KAAKiB,QAAQwlB,cAAczM,GACtC4Q,EAAO7iB,EAAQzH,IAAIiW,OAAOyD,GAC9BzD,EAAOpY,KAAK,GAAIysB,GAAK5qB,KAAMoC,KAAeqjB,EAAUxkB,MAG5DomB,WAAY,SAAUzZ,GAElB,MADAA,GAAOvF,EAAeuF,EAAM5N,KAAKiB,QAAQ2M,MAClC5N,KAAKiB,QAAQkb,QAAUpK,EAAI,EAAGnE,IAEzC+c,KAAM,SAAUja,GACZ,GAAK1Q,KAAK+qB,eAAV,CAGA,GAAI/C,GAAShoB,KAAKynB,YAAY/W,EAC9B1Q,MAAKwQ,QAAQ,SACTkJ,cAAehJ,EACfwI,SAAUlZ,KAAKwnB,eAAeQ,OAGtC0C,WAAY,SAAUha,GAAV,GAII4b,GACAtE,EACA9O,EACAqT,EACAjO,EAPRrd,EAAUjB,KAAKiB,OACfA,GAAQ+oB,YAAa,IAChBhqB,KAAKwQ,QAAQ,aAAekJ,cAAehJ,MACxC4b,EAAStsB,KAAK4N,OAASmY,EACvBiC,EAAShoB,KAAKynB,YAAY/W,GAC1BwI,EAAWlZ,KAAKwnB,eAAeQ,GAC/BuE,EAAWvsB,KAAKue,gBAAgBrF,EAAUoT,GAC1ChO,EAASiO,EAASjY,WAAW0T,EAAOviB,GAAIuiB,EAAOzX,GACnDvQ,KAAK6oB,YAAYvK,EAAQgO,GACzBtsB,KAAKwQ,QAAQ,WAAakJ,cAAehJ,OAIrD4V,YAAa,SAAU5V,GAAV,GAELiB,GACA1Q,EACAurB,EACAF,EAGQtE,EACA9O,EACAqT,EACAjO,CAVZ5N,GAAEG,iBACEc,EAAQ5J,EAAQ0kB,QAAQ/b,GAAK,KAAS,EACtCzP,EAAUjB,KAAKiB,QACfurB,EAAWxsB,KAAK4N,OAChB0e,EAASla,EAAMoa,EAAW7a,EAAO1Q,EAAQ0N,QAAS1N,EAAQ2N,SAC1D3N,EAAQ+oB,YAAa,GAASsC,IAAWE,IACpCxsB,KAAKwQ,QAAQ,aAAekJ,cAAehJ,MACxCsX,EAAShoB,KAAKynB,YAAY/W,GAC1BwI,EAAWlZ,KAAKwnB,eAAeQ,GAC/BuE,EAAWvsB,KAAKue,gBAAgBrF,EAAUoT,GAC1ChO,EAASiO,EAASjY,WAAW0T,EAAOviB,GAAIuiB,EAAOzX,GACnDvQ,KAAK6oB,YAAYvK,EAAQgO,GACzBtsB,KAAKwQ,QAAQ,WAAakJ,cAAehJ,QAKzD3I,GAAQkF,GAAGiC,OAAO8W,IACpBvmB,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,qBACH,aACA,mBACA,gBACA,wBACA,oBACA,qBACA,uBACA,0BACA,wBACA,mBACA,kBACA,0BACA,2BACA,4BACA,0BACA,0BACA,4BACA,oBACDD,IACL,WAeE,MAAOmC,QAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dataviz.map.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('util/main', ['kendo.core'], f);\n}(function () {\n    (function () {\n        var kendo = window.kendo, deepExtend = kendo.deepExtend;\n        function sqr(value) {\n            return value * value;\n        }\n        var now = Date.now;\n        if (!now) {\n            now = function () {\n                return new Date().getTime();\n            };\n        }\n        function renderSize(size) {\n            if (typeof size !== 'string') {\n                size += 'px';\n            }\n            return size;\n        }\n        function renderPos(pos) {\n            var result = [];\n            if (pos) {\n                var parts = kendo.toHyphens(pos).split('-');\n                for (var i = 0; i < parts.length; i++) {\n                    result.push('k-pos-' + parts[i]);\n                }\n            }\n            return result.join(' ');\n        }\n        function arabicToRoman(n) {\n            var literals = {\n                1: 'i',\n                10: 'x',\n                100: 'c',\n                2: 'ii',\n                20: 'xx',\n                200: 'cc',\n                3: 'iii',\n                30: 'xxx',\n                300: 'ccc',\n                4: 'iv',\n                40: 'xl',\n                400: 'cd',\n                5: 'v',\n                50: 'l',\n                500: 'd',\n                6: 'vi',\n                60: 'lx',\n                600: 'dc',\n                7: 'vii',\n                70: 'lxx',\n                700: 'dcc',\n                8: 'viii',\n                80: 'lxxx',\n                800: 'dccc',\n                9: 'ix',\n                90: 'xc',\n                900: 'cm',\n                1000: 'm'\n            };\n            var values = [\n                1000,\n                900,\n                800,\n                700,\n                600,\n                500,\n                400,\n                300,\n                200,\n                100,\n                90,\n                80,\n                70,\n                60,\n                50,\n                40,\n                30,\n                20,\n                10,\n                9,\n                8,\n                7,\n                6,\n                5,\n                4,\n                3,\n                2,\n                1\n            ];\n            var roman = '';\n            while (n > 0) {\n                if (n < values[0]) {\n                    values.shift();\n                } else {\n                    roman += literals[values[0]];\n                    n -= values[0];\n                }\n            }\n            return roman;\n        }\n        function romanToArabic(r) {\n            r = r.toLowerCase();\n            var digits = {\n                i: 1,\n                v: 5,\n                x: 10,\n                l: 50,\n                c: 100,\n                d: 500,\n                m: 1000\n            };\n            var value = 0, prev = 0;\n            for (var i = 0; i < r.length; ++i) {\n                var v = digits[r.charAt(i)];\n                if (!v) {\n                    return null;\n                }\n                value += v;\n                if (v > prev) {\n                    value -= 2 * prev;\n                }\n                prev = v;\n            }\n            return value;\n        }\n        function memoize(f) {\n            var cache = Object.create(null);\n            return function () {\n                var id = '';\n                for (var i = arguments.length; --i >= 0;) {\n                    id += ':' + arguments[i];\n                }\n                return id in cache ? cache[id] : cache[id] = f.apply(this, arguments);\n            };\n        }\n        function isUnicodeLetter(ch) {\n            return RX_UNICODE_LETTER.test(ch);\n        }\n        function withExit(f, obj) {\n            try {\n                return f.call(obj, function (value) {\n                    throw new Return(value);\n                });\n            } catch (ex) {\n                if (ex instanceof Return) {\n                    return ex.value;\n                }\n                throw ex;\n            }\n            function Return(value) {\n                this.value = value;\n            }\n        }\n        deepExtend(kendo, {\n            util: {\n                now: now,\n                renderPos: renderPos,\n                renderSize: renderSize,\n                sqr: sqr,\n                romanToArabic: romanToArabic,\n                arabicToRoman: arabicToRoman,\n                memoize: memoize,\n                isUnicodeLetter: isUnicodeLetter,\n                withExit: withExit\n            }\n        });\n        var RX_UNICODE_LETTER = new RegExp('[\\\\u0041-\\\\u005A\\\\u0061-\\\\u007A\\\\u00AA\\\\u00B5\\\\u00BA\\\\u00C0-\\\\u00D6\\\\u00D8-\\\\u00F6\\\\u00F8-\\\\u02C1\\\\u02C6-\\\\u02D1\\\\u02E0-\\\\u02E4\\\\u02EC\\\\u02EE\\\\u0370-\\\\u0374\\\\u0376\\\\u0377\\\\u037A-\\\\u037D\\\\u037F\\\\u0386\\\\u0388-\\\\u038A\\\\u038C\\\\u038E-\\\\u03A1\\\\u03A3-\\\\u03F5\\\\u03F7-\\\\u0481\\\\u048A-\\\\u052F\\\\u0531-\\\\u0556\\\\u0559\\\\u0561-\\\\u0587\\\\u05D0-\\\\u05EA\\\\u05F0-\\\\u05F2\\\\u0620-\\\\u064A\\\\u066E\\\\u066F\\\\u0671-\\\\u06D3\\\\u06D5\\\\u06E5\\\\u06E6\\\\u06EE\\\\u06EF\\\\u06FA-\\\\u06FC\\\\u06FF\\\\u0710\\\\u0712-\\\\u072F\\\\u074D-\\\\u07A5\\\\u07B1\\\\u07CA-\\\\u07EA\\\\u07F4\\\\u07F5\\\\u07FA\\\\u0800-\\\\u0815\\\\u081A\\\\u0824\\\\u0828\\\\u0840-\\\\u0858\\\\u08A0-\\\\u08B2\\\\u0904-\\\\u0939\\\\u093D\\\\u0950\\\\u0958-\\\\u0961\\\\u0971-\\\\u0980\\\\u0985-\\\\u098C\\\\u098F\\\\u0990\\\\u0993-\\\\u09A8\\\\u09AA-\\\\u09B0\\\\u09B2\\\\u09B6-\\\\u09B9\\\\u09BD\\\\u09CE\\\\u09DC\\\\u09DD\\\\u09DF-\\\\u09E1\\\\u09F0\\\\u09F1\\\\u0A05-\\\\u0A0A\\\\u0A0F\\\\u0A10\\\\u0A13-\\\\u0A28\\\\u0A2A-\\\\u0A30\\\\u0A32\\\\u0A33\\\\u0A35\\\\u0A36\\\\u0A38\\\\u0A39\\\\u0A59-\\\\u0A5C\\\\u0A5E\\\\u0A72-\\\\u0A74\\\\u0A85-\\\\u0A8D\\\\u0A8F-\\\\u0A91\\\\u0A93-\\\\u0AA8\\\\u0AAA-\\\\u0AB0\\\\u0AB2\\\\u0AB3\\\\u0AB5-\\\\u0AB9\\\\u0ABD\\\\u0AD0\\\\u0AE0\\\\u0AE1\\\\u0B05-\\\\u0B0C\\\\u0B0F\\\\u0B10\\\\u0B13-\\\\u0B28\\\\u0B2A-\\\\u0B30\\\\u0B32\\\\u0B33\\\\u0B35-\\\\u0B39\\\\u0B3D\\\\u0B5C\\\\u0B5D\\\\u0B5F-\\\\u0B61\\\\u0B71\\\\u0B83\\\\u0B85-\\\\u0B8A\\\\u0B8E-\\\\u0B90\\\\u0B92-\\\\u0B95\\\\u0B99\\\\u0B9A\\\\u0B9C\\\\u0B9E\\\\u0B9F\\\\u0BA3\\\\u0BA4\\\\u0BA8-\\\\u0BAA\\\\u0BAE-\\\\u0BB9\\\\u0BD0\\\\u0C05-\\\\u0C0C\\\\u0C0E-\\\\u0C10\\\\u0C12-\\\\u0C28\\\\u0C2A-\\\\u0C39\\\\u0C3D\\\\u0C58\\\\u0C59\\\\u0C60\\\\u0C61\\\\u0C85-\\\\u0C8C\\\\u0C8E-\\\\u0C90\\\\u0C92-\\\\u0CA8\\\\u0CAA-\\\\u0CB3\\\\u0CB5-\\\\u0CB9\\\\u0CBD\\\\u0CDE\\\\u0CE0\\\\u0CE1\\\\u0CF1\\\\u0CF2\\\\u0D05-\\\\u0D0C\\\\u0D0E-\\\\u0D10\\\\u0D12-\\\\u0D3A\\\\u0D3D\\\\u0D4E\\\\u0D60\\\\u0D61\\\\u0D7A-\\\\u0D7F\\\\u0D85-\\\\u0D96\\\\u0D9A-\\\\u0DB1\\\\u0DB3-\\\\u0DBB\\\\u0DBD\\\\u0DC0-\\\\u0DC6\\\\u0E01-\\\\u0E30\\\\u0E32\\\\u0E33\\\\u0E40-\\\\u0E46\\\\u0E81\\\\u0E82\\\\u0E84\\\\u0E87\\\\u0E88\\\\u0E8A\\\\u0E8D\\\\u0E94-\\\\u0E97\\\\u0E99-\\\\u0E9F\\\\u0EA1-\\\\u0EA3\\\\u0EA5\\\\u0EA7\\\\u0EAA\\\\u0EAB\\\\u0EAD-\\\\u0EB0\\\\u0EB2\\\\u0EB3\\\\u0EBD\\\\u0EC0-\\\\u0EC4\\\\u0EC6\\\\u0EDC-\\\\u0EDF\\\\u0F00\\\\u0F40-\\\\u0F47\\\\u0F49-\\\\u0F6C\\\\u0F88-\\\\u0F8C\\\\u1000-\\\\u102A\\\\u103F\\\\u1050-\\\\u1055\\\\u105A-\\\\u105D\\\\u1061\\\\u1065\\\\u1066\\\\u106E-\\\\u1070\\\\u1075-\\\\u1081\\\\u108E\\\\u10A0-\\\\u10C5\\\\u10C7\\\\u10CD\\\\u10D0-\\\\u10FA\\\\u10FC-\\\\u1248\\\\u124A-\\\\u124D\\\\u1250-\\\\u1256\\\\u1258\\\\u125A-\\\\u125D\\\\u1260-\\\\u1288\\\\u128A-\\\\u128D\\\\u1290-\\\\u12B0\\\\u12B2-\\\\u12B5\\\\u12B8-\\\\u12BE\\\\u12C0\\\\u12C2-\\\\u12C5\\\\u12C8-\\\\u12D6\\\\u12D8-\\\\u1310\\\\u1312-\\\\u1315\\\\u1318-\\\\u135A\\\\u1380-\\\\u138F\\\\u13A0-\\\\u13F4\\\\u1401-\\\\u166C\\\\u166F-\\\\u167F\\\\u1681-\\\\u169A\\\\u16A0-\\\\u16EA\\\\u16EE-\\\\u16F8\\\\u1700-\\\\u170C\\\\u170E-\\\\u1711\\\\u1720-\\\\u1731\\\\u1740-\\\\u1751\\\\u1760-\\\\u176C\\\\u176E-\\\\u1770\\\\u1780-\\\\u17B3\\\\u17D7\\\\u17DC\\\\u1820-\\\\u1877\\\\u1880-\\\\u18A8\\\\u18AA\\\\u18B0-\\\\u18F5\\\\u1900-\\\\u191E\\\\u1950-\\\\u196D\\\\u1970-\\\\u1974\\\\u1980-\\\\u19AB\\\\u19C1-\\\\u19C7\\\\u1A00-\\\\u1A16\\\\u1A20-\\\\u1A54\\\\u1AA7\\\\u1B05-\\\\u1B33\\\\u1B45-\\\\u1B4B\\\\u1B83-\\\\u1BA0\\\\u1BAE\\\\u1BAF\\\\u1BBA-\\\\u1BE5\\\\u1C00-\\\\u1C23\\\\u1C4D-\\\\u1C4F\\\\u1C5A-\\\\u1C7D\\\\u1CE9-\\\\u1CEC\\\\u1CEE-\\\\u1CF1\\\\u1CF5\\\\u1CF6\\\\u1D00-\\\\u1DBF\\\\u1E00-\\\\u1F15\\\\u1F18-\\\\u1F1D\\\\u1F20-\\\\u1F45\\\\u1F48-\\\\u1F4D\\\\u1F50-\\\\u1F57\\\\u1F59\\\\u1F5B\\\\u1F5D\\\\u1F5F-\\\\u1F7D\\\\u1F80-\\\\u1FB4\\\\u1FB6-\\\\u1FBC\\\\u1FBE\\\\u1FC2-\\\\u1FC4\\\\u1FC6-\\\\u1FCC\\\\u1FD0-\\\\u1FD3\\\\u1FD6-\\\\u1FDB\\\\u1FE0-\\\\u1FEC\\\\u1FF2-\\\\u1FF4\\\\u1FF6-\\\\u1FFC\\\\u2071\\\\u207F\\\\u2090-\\\\u209C\\\\u2102\\\\u2107\\\\u210A-\\\\u2113\\\\u2115\\\\u2119-\\\\u211D\\\\u2124\\\\u2126\\\\u2128\\\\u212A-\\\\u212D\\\\u212F-\\\\u2139\\\\u213C-\\\\u213F\\\\u2145-\\\\u2149\\\\u214E\\\\u2160-\\\\u2188\\\\u2C00-\\\\u2C2E\\\\u2C30-\\\\u2C5E\\\\u2C60-\\\\u2CE4\\\\u2CEB-\\\\u2CEE\\\\u2CF2\\\\u2CF3\\\\u2D00-\\\\u2D25\\\\u2D27\\\\u2D2D\\\\u2D30-\\\\u2D67\\\\u2D6F\\\\u2D80-\\\\u2D96\\\\u2DA0-\\\\u2DA6\\\\u2DA8-\\\\u2DAE\\\\u2DB0-\\\\u2DB6\\\\u2DB8-\\\\u2DBE\\\\u2DC0-\\\\u2DC6\\\\u2DC8-\\\\u2DCE\\\\u2DD0-\\\\u2DD6\\\\u2DD8-\\\\u2DDE\\\\u2E2F\\\\u3005-\\\\u3007\\\\u3021-\\\\u3029\\\\u3031-\\\\u3035\\\\u3038-\\\\u303C\\\\u3041-\\\\u3096\\\\u309D-\\\\u309F\\\\u30A1-\\\\u30FA\\\\u30FC-\\\\u30FF\\\\u3105-\\\\u312D\\\\u3131-\\\\u318E\\\\u31A0-\\\\u31BA\\\\u31F0-\\\\u31FF\\\\u3400-\\\\u4DB5\\\\u4E00-\\\\u9FCC\\\\uA000-\\\\uA48C\\\\uA4D0-\\\\uA4FD\\\\uA500-\\\\uA60C\\\\uA610-\\\\uA61F\\\\uA62A\\\\uA62B\\\\uA640-\\\\uA66E\\\\uA67F-\\\\uA69D\\\\uA6A0-\\\\uA6EF\\\\uA717-\\\\uA71F\\\\uA722-\\\\uA788\\\\uA78B-\\\\uA78E\\\\uA790-\\\\uA7AD\\\\uA7B0\\\\uA7B1\\\\uA7F7-\\\\uA801\\\\uA803-\\\\uA805\\\\uA807-\\\\uA80A\\\\uA80C-\\\\uA822\\\\uA840-\\\\uA873\\\\uA882-\\\\uA8B3\\\\uA8F2-\\\\uA8F7\\\\uA8FB\\\\uA90A-\\\\uA925\\\\uA930-\\\\uA946\\\\uA960-\\\\uA97C\\\\uA984-\\\\uA9B2\\\\uA9CF\\\\uA9E0-\\\\uA9E4\\\\uA9E6-\\\\uA9EF\\\\uA9FA-\\\\uA9FE\\\\uAA00-\\\\uAA28\\\\uAA40-\\\\uAA42\\\\uAA44-\\\\uAA4B\\\\uAA60-\\\\uAA76\\\\uAA7A\\\\uAA7E-\\\\uAAAF\\\\uAAB1\\\\uAAB5\\\\uAAB6\\\\uAAB9-\\\\uAABD\\\\uAAC0\\\\uAAC2\\\\uAADB-\\\\uAADD\\\\uAAE0-\\\\uAAEA\\\\uAAF2-\\\\uAAF4\\\\uAB01-\\\\uAB06\\\\uAB09-\\\\uAB0E\\\\uAB11-\\\\uAB16\\\\uAB20-\\\\uAB26\\\\uAB28-\\\\uAB2E\\\\uAB30-\\\\uAB5A\\\\uAB5C-\\\\uAB5F\\\\uAB64\\\\uAB65\\\\uABC0-\\\\uABE2\\\\uAC00-\\\\uD7A3\\\\uD7B0-\\\\uD7C6\\\\uD7CB-\\\\uD7FB\\\\uF900-\\\\uFA6D\\\\uFA70-\\\\uFAD9\\\\uFB00-\\\\uFB06\\\\uFB13-\\\\uFB17\\\\uFB1D\\\\uFB1F-\\\\uFB28\\\\uFB2A-\\\\uFB36\\\\uFB38-\\\\uFB3C\\\\uFB3E\\\\uFB40\\\\uFB41\\\\uFB43\\\\uFB44\\\\uFB46-\\\\uFBB1\\\\uFBD3-\\\\uFD3D\\\\uFD50-\\\\uFD8F\\\\uFD92-\\\\uFDC7\\\\uFDF0-\\\\uFDFB\\\\uFE70-\\\\uFE74\\\\uFE76-\\\\uFEFC\\\\uFF21-\\\\uFF3A\\\\uFF41-\\\\uFF5A\\\\uFF66-\\\\uFFBE\\\\uFFC2-\\\\uFFC7\\\\uFFCA-\\\\uFFCF\\\\uFFD2-\\\\uFFD7\\\\uFFDA-\\\\uFFDC]');\n    }());\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/location', [\n        'kendo.drawing',\n        'util/main'\n    ], f);\n}(function () {\n    (function ($, undefined) {\n        var math = Math, abs = math.abs, atan = math.atan, atan2 = math.atan2, cos = math.cos, max = math.max, min = math.min, sin = math.sin, tan = math.tan, kendo = window.kendo, Class = kendo.Class, dataviz = kendo.dataviz, deepExtend = kendo.deepExtend, util = kendo.drawing.util, defined = util.defined, deg = util.deg, rad = util.rad, round = util.round, valueOrDefault = util.valueOrDefault, sqr = kendo.util.sqr;\n        var Location = Class.extend({\n            init: function (lat, lng) {\n                if (arguments.length === 1) {\n                    this.lat = lat[0];\n                    this.lng = lat[1];\n                } else {\n                    this.lat = lat;\n                    this.lng = lng;\n                }\n            },\n            DISTANCE_ITERATIONS: 100,\n            DISTANCE_CONVERGENCE: 1e-12,\n            DISTANCE_PRECISION: 2,\n            FORMAT: '{0:N6},{1:N6}',\n            toArray: function () {\n                return [\n                    this.lat,\n                    this.lng\n                ];\n            },\n            equals: function (loc) {\n                return loc && loc.lat === this.lat && loc.lng === this.lng;\n            },\n            clone: function () {\n                return new Location(this.lat, this.lng);\n            },\n            round: function (precision) {\n                this.lng = round(this.lng, precision);\n                this.lat = round(this.lat, precision);\n                return this;\n            },\n            wrap: function () {\n                this.lng = this.lng % 180;\n                this.lat = this.lat % 90;\n                return this;\n            },\n            distanceTo: function (dest, datum) {\n                return this.greatCircleTo(dest, datum).distance;\n            },\n            destination: function (distance, bearing, datum) {\n                bearing = rad(bearing);\n                datum = datum || dataviz.map.datums.WGS84;\n                var fromLat = rad(this.lat);\n                var fromLng = rad(this.lng);\n                var dToR = distance / kendo.dataviz.map.datums.WGS84.a;\n                var lat = math.asin(sin(fromLat) * cos(dToR) + cos(fromLat) * sin(dToR) * cos(bearing));\n                var lng = fromLng + atan2(sin(bearing) * sin(dToR) * cos(fromLat), cos(dToR) - sin(fromLat) * sin(lat));\n                return new Location(deg(lat), deg(lng));\n            },\n            greatCircleTo: function (dest, datum) {\n                dest = Location.create(dest);\n                datum = datum || dataviz.map.datums.WGS84;\n                if (!dest || this.clone().round(8).equals(dest.clone().round(8))) {\n                    return {\n                        distance: 0,\n                        azimuthFrom: 0,\n                        azimuthTo: 0\n                    };\n                }\n                var a = datum.a;\n                var b = datum.b;\n                var f = datum.f;\n                var L = rad(dest.lng - this.lng);\n                var U1 = atan((1 - f) * tan(rad(this.lat)));\n                var sinU1 = sin(U1);\n                var cosU1 = cos(U1);\n                var U2 = atan((1 - f) * tan(rad(dest.lat)));\n                var sinU2 = sin(U2);\n                var cosU2 = cos(U2);\n                var lambda = L;\n                var prevLambda;\n                var i = this.DISTANCE_ITERATIONS;\n                var converged = false;\n                var sinLambda;\n                var cosLambda;\n                var sino;\n                var cosA2;\n                var coso;\n                var cos2om;\n                var sigma;\n                while (!converged && i-- > 0) {\n                    sinLambda = sin(lambda);\n                    cosLambda = cos(lambda);\n                    sino = math.sqrt(sqr(cosU2 * sinLambda) + sqr(cosU1 * sinU2 - sinU1 * cosU2 * cosLambda));\n                    coso = sinU1 * sinU2 + cosU1 * cosU2 * cosLambda;\n                    sigma = atan2(sino, coso);\n                    var sinA = cosU1 * cosU2 * sinLambda / sino;\n                    cosA2 = 1 - sqr(sinA);\n                    cos2om = 0;\n                    if (cosA2 !== 0) {\n                        cos2om = coso - 2 * sinU1 * sinU2 / cosA2;\n                    }\n                    prevLambda = lambda;\n                    var C = f / 16 * cosA2 * (4 + f * (4 - 3 * cosA2));\n                    lambda = L + (1 - C) * f * sinA * (sigma + C * sino * (cos2om + C * coso * (-1 + 2 * sqr(cos2om))));\n                    converged = abs(lambda - prevLambda) <= this.DISTANCE_CONVERGENCE;\n                }\n                var u2 = cosA2 * (sqr(a) - sqr(b)) / sqr(b);\n                var A = 1 + u2 / 16384 * (4096 + u2 * (-768 + u2 * (320 - 175 * u2)));\n                var B = u2 / 1024 * (256 + u2 * (-128 + u2 * (74 - 47 * u2)));\n                var deltao = B * sino * (cos2om + B / 4 * (coso * (-1 + 2 * sqr(cos2om)) - B / 6 * cos2om * (-3 + 4 * sqr(sino)) * (-3 + 4 * sqr(cos2om))));\n                var azimuthFrom = atan2(cosU2 * sinLambda, cosU1 * sinU2 - sinU1 * cosU2 * cosLambda);\n                var azimuthTo = atan2(cosU1 * sinLambda, -sinU1 * cosU2 + cosU1 * sinU2 * cosLambda);\n                return {\n                    distance: round(b * A * (sigma - deltao), this.DISTANCE_PRECISION),\n                    azimuthFrom: deg(azimuthFrom),\n                    azimuthTo: deg(azimuthTo)\n                };\n            }\n        });\n        Location.fn.toString = function () {\n            return kendo.format(this.FORMAT, this.lat, this.lng);\n        };\n        Location.fromLngLat = function (ll) {\n            return new Location(ll[1], ll[0]);\n        };\n        Location.fromLatLng = function (ll) {\n            return new Location(ll[0], ll[1]);\n        };\n        Location.create = function (a, b) {\n            if (defined(a)) {\n                if (a instanceof Location) {\n                    return a.clone();\n                } else if (arguments.length === 1 && a.length === 2) {\n                    return Location.fromLatLng(a);\n                } else {\n                    return new Location(a, b);\n                }\n            }\n        };\n        var Extent = Class.extend({\n            init: function (nw, se) {\n                nw = Location.create(nw);\n                se = Location.create(se);\n                if (nw.lng + 180 > se.lng + 180 && nw.lat + 90 < se.lat + 90) {\n                    this.se = nw;\n                    this.nw = se;\n                } else {\n                    this.se = se;\n                    this.nw = nw;\n                }\n            },\n            contains: function (loc) {\n                var nw = this.nw, se = this.se, lng = valueOrDefault(loc.lng, loc[1]), lat = valueOrDefault(loc.lat, loc[0]);\n                return loc && lng + 180 >= nw.lng + 180 && lng + 180 <= se.lng + 180 && lat + 90 >= se.lat + 90 && lat + 90 <= nw.lat + 90;\n            },\n            center: function () {\n                var nw = this.nw;\n                var se = this.se;\n                var lng = nw.lng + (se.lng - nw.lng) / 2;\n                var lat = nw.lat + (se.lat - nw.lat) / 2;\n                return new Location(lat, lng);\n            },\n            containsAny: function (locs) {\n                var result = false;\n                for (var i = 0; i < locs.length; i++) {\n                    result = result || this.contains(locs[i]);\n                }\n                return result;\n            },\n            include: function (loc) {\n                var nw = this.nw, se = this.se, lng = valueOrDefault(loc.lng, loc[1]), lat = valueOrDefault(loc.lat, loc[0]);\n                nw.lng = min(nw.lng, lng);\n                nw.lat = max(nw.lat, lat);\n                se.lng = max(se.lng, lng);\n                se.lat = min(se.lat, lat);\n            },\n            includeAll: function (locs) {\n                for (var i = 0; i < locs.length; i++) {\n                    this.include(locs[i]);\n                }\n            },\n            edges: function () {\n                var nw = this.nw, se = this.se;\n                return {\n                    nw: this.nw,\n                    ne: new Location(nw.lat, se.lng),\n                    se: this.se,\n                    sw: new Location(se.lat, nw.lng)\n                };\n            },\n            toArray: function () {\n                var nw = this.nw, se = this.se;\n                return [\n                    nw,\n                    new Location(nw.lat, se.lng),\n                    se,\n                    new Location(se.lat, nw.lng)\n                ];\n            },\n            overlaps: function (extent) {\n                return this.containsAny(extent.toArray()) || extent.containsAny(this.toArray());\n            }\n        });\n        Extent.World = new Extent([\n            90,\n            -180\n        ], [\n            -90,\n            180\n        ]);\n        Extent.create = function (a, b) {\n            if (a instanceof Extent) {\n                return a;\n            } else if (a && b) {\n                return new Extent(a, b);\n            } else if (a && a.length === 4 && !b) {\n                return new Extent([\n                    a[0],\n                    a[1]\n                ], [\n                    a[2],\n                    a[3]\n                ]);\n            }\n        };\n        deepExtend(dataviz, {\n            map: {\n                Extent: Extent,\n                Location: Location\n            }\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/attribution', ['kendo.drawing'], f);\n}(function () {\n    (function () {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, template = kendo.template, util = kendo.drawing.util, valueOrDefault = util.valueOrDefault, defined = util.defined;\n        var Attribution = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this._initOptions(options);\n                this.items = [];\n                this.element.addClass('k-widget k-attribution');\n            },\n            options: {\n                name: 'Attribution',\n                separator: '&nbsp;|&nbsp;',\n                itemTemplate: '#= text #'\n            },\n            filter: function (extent, zoom) {\n                this._extent = extent;\n                this._zoom = zoom;\n                this._render();\n            },\n            add: function (item) {\n                if (defined(item)) {\n                    if (typeof item === 'string') {\n                        item = { text: item };\n                    }\n                    this.items.push(item);\n                    this._render();\n                }\n            },\n            remove: function (text) {\n                var result = [];\n                for (var i = 0; i < this.items.length; i++) {\n                    var item = this.items[i];\n                    if (item.text !== text) {\n                        result.push(item);\n                    }\n                }\n                this.items = result;\n                this._render();\n            },\n            clear: function () {\n                this.items = [];\n                this.element.empty();\n            },\n            _render: function () {\n                var result = [];\n                var itemTemplate = template(this.options.itemTemplate);\n                for (var i = 0; i < this.items.length; i++) {\n                    var item = this.items[i];\n                    var text = this._itemText(item);\n                    if (text !== '') {\n                        result.push(itemTemplate({ text: text }));\n                    }\n                }\n                if (result.length > 0) {\n                    this.element.empty().append(result.join(this.options.separator)).show();\n                } else {\n                    this.element.hide();\n                }\n            },\n            _itemText: function (item) {\n                var text = '';\n                var inZoomLevel = this._inZoomLevel(item.minZoom, item.maxZoom);\n                var inArea = this._inArea(item.extent);\n                if (inZoomLevel && inArea) {\n                    text += item.text;\n                }\n                return text;\n            },\n            _inZoomLevel: function (min, max) {\n                var result = true;\n                min = valueOrDefault(min, -Number.MAX_VALUE);\n                max = valueOrDefault(max, Number.MAX_VALUE);\n                result = this._zoom > min && this._zoom < max;\n                return result;\n            },\n            _inArea: function (area) {\n                var result = true;\n                if (area) {\n                    result = area.contains(this._extent);\n                }\n                return result;\n            }\n        });\n        kendo.dataviz.ui.plugin(Attribution);\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/navigator', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        var kendo = window.kendo;\n        var Widget = kendo.ui.Widget;\n        var keys = kendo.keys;\n        var proxy = $.proxy;\n        var NS = '.kendoNavigator';\n        function button(dir) {\n            return kendo.format('<button class=\"k-button k-navigator-{0}\" aria-label=\"move {0}\">' + '<span class=\"k-icon k-i-arrow-60-{0}\"/>' + '</button>', dir);\n        }\n        var BUTTONS = button('up') + button('right') + button('down') + button('left');\n        var Navigator = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this._initOptions(options);\n                this.element.addClass('k-widget k-header k-shadow k-navigator').append(BUTTONS).on('click' + NS, '.k-button', proxy(this, '_click'));\n                var parentElement = this.element.parent().closest('[' + kendo.attr('role') + ']');\n                this._keyroot = parentElement.length > 0 ? parentElement : this.element;\n                this._tabindex(this._keyroot);\n                this._keydown = proxy(this._keydown, this);\n                this._keyroot.on('keydown', this._keydown);\n            },\n            options: {\n                name: 'Navigator',\n                panStep: 1\n            },\n            events: ['pan'],\n            dispose: function () {\n                this._keyroot.off('keydown', this._keydown);\n            },\n            _pan: function (x, y) {\n                var panStep = this.options.panStep;\n                this.trigger('pan', {\n                    x: x * panStep,\n                    y: y * panStep\n                });\n            },\n            _click: function (e) {\n                var x = 0;\n                var y = 0;\n                var button = $(e.currentTarget);\n                if (button.is('.k-navigator-up')) {\n                    y = 1;\n                } else if (button.is('.k-navigator-down')) {\n                    y = -1;\n                } else if (button.is('.k-navigator-right')) {\n                    x = 1;\n                } else if (button.is('.k-navigator-left')) {\n                    x = -1;\n                }\n                this._pan(x, y);\n                e.preventDefault();\n            },\n            _keydown: function (e) {\n                switch (e.which) {\n                case keys.UP:\n                    this._pan(0, 1);\n                    e.preventDefault();\n                    break;\n                case keys.DOWN:\n                    this._pan(0, -1);\n                    e.preventDefault();\n                    break;\n                case keys.RIGHT:\n                    this._pan(1, 0);\n                    e.preventDefault();\n                    break;\n                case keys.LEFT:\n                    this._pan(-1, 0);\n                    e.preventDefault();\n                    break;\n                }\n            }\n        });\n        kendo.dataviz.ui.plugin(Navigator);\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/zoom', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        var kendo = window.kendo;\n        var Widget = kendo.ui.Widget;\n        var keys = kendo.keys;\n        var proxy = $.proxy;\n        function button(dir, iconClass) {\n            return kendo.format('<button class=\"k-button k-zoom-{0}\" title=\"zoom-{0}\" aria-label=\"zoom-{0}\"><span class=\"k-icon {1}\"></span></button>', dir, iconClass);\n        }\n        var NS = '.kendoZoomControl';\n        var BUTTONS = button('in', 'k-i-plus') + button('out', 'k-i-minus');\n        var PLUS = 187;\n        var MINUS = 189;\n        var FF_PLUS = 61;\n        var FF_MINUS = 173;\n        var ZoomControl = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this._initOptions(options);\n                this.element.addClass('k-widget k-zoom-control k-button-wrap k-buttons-horizontal k-button-group k-group-horizontal').append(BUTTONS).on('click' + NS, '.k-button', proxy(this, '_click'));\n                var parentElement = this.element.parent().closest('[' + kendo.attr('role') + ']');\n                this._keyroot = parentElement.length > 0 ? parentElement : this.element;\n                this._tabindex(this._keyroot);\n                this._keydown = proxy(this._keydown, this);\n                this._keyroot.on('keydown', this._keydown);\n            },\n            options: {\n                name: 'ZoomControl',\n                zoomStep: 1\n            },\n            events: ['change'],\n            _change: function (dir) {\n                var zoomStep = this.options.zoomStep;\n                this.trigger('change', { delta: dir * zoomStep });\n            },\n            _click: function (e) {\n                var button = $(e.currentTarget);\n                var dir = 1;\n                if (button.is('.k-zoom-out')) {\n                    dir = -1;\n                }\n                this._change(dir);\n                e.preventDefault();\n            },\n            _keydown: function (e) {\n                switch (e.which) {\n                case keys.NUMPAD_PLUS:\n                case PLUS:\n                case FF_PLUS:\n                    this._change(1);\n                    break;\n                case keys.NUMPAD_MINUS:\n                case MINUS:\n                case FF_MINUS:\n                    this._change(-1);\n                    break;\n                }\n            }\n        });\n        kendo.dataviz.ui.plugin(ZoomControl);\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/crs', [\n        'dataviz/map/location',\n        'kendo.drawing'\n    ], f);\n}(function () {\n    (function ($, undefined) {\n        var math = Math, atan = math.atan, exp = math.exp, pow = math.pow, sin = math.sin, log = math.log, tan = math.tan, kendo = window.kendo, Class = kendo.Class, dataviz = kendo.dataviz, deepExtend = kendo.deepExtend, g = kendo.geometry, Point = g.Point, map = dataviz.map, Location = map.Location, util = kendo.drawing.util, rad = util.rad, deg = util.deg, limit = util.limitValue;\n        var PI = math.PI, PI_DIV_2 = PI / 2, PI_DIV_4 = PI / 4, DEG_TO_RAD = PI / 180;\n        var WGS84 = {\n            a: 6378137,\n            b: 6356752.314245179,\n            f: 0.0033528106647474805,\n            e: 0.08181919084262149\n        };\n        var Mercator = Class.extend({\n            init: function (options) {\n                this._initOptions(options);\n            },\n            MAX_LNG: 180,\n            MAX_LAT: 85.0840590501,\n            INVERSE_ITERATIONS: 15,\n            INVERSE_CONVERGENCE: 1e-12,\n            options: {\n                centralMeridian: 0,\n                datum: WGS84\n            },\n            forward: function (loc, clamp) {\n                var proj = this, options = proj.options, datum = options.datum, r = datum.a, lng0 = options.centralMeridian, lat = limit(loc.lat, -proj.MAX_LAT, proj.MAX_LAT), lng = clamp ? limit(loc.lng, -proj.MAX_LNG, proj.MAX_LNG) : loc.lng, x = rad(lng - lng0) * r, y = proj._projectLat(lat);\n                return new Point(x, y);\n            },\n            _projectLat: function (lat) {\n                var datum = this.options.datum, ecc = datum.e, r = datum.a, y = rad(lat), ts = tan(PI_DIV_4 + y / 2), con = ecc * sin(y), p = pow((1 - con) / (1 + con), ecc / 2);\n                return r * log(ts * p);\n            },\n            inverse: function (point, clamp) {\n                var proj = this, options = proj.options, datum = options.datum, r = datum.a, lng0 = options.centralMeridian, lng = point.x / (DEG_TO_RAD * r) + lng0, lat = limit(proj._inverseY(point.y), -proj.MAX_LAT, proj.MAX_LAT);\n                if (clamp) {\n                    lng = limit(lng, -proj.MAX_LNG, proj.MAX_LNG);\n                }\n                return new Location(lat, lng);\n            },\n            _inverseY: function (y) {\n                var proj = this, datum = proj.options.datum, r = datum.a, ecc = datum.e, ecch = ecc / 2, ts = exp(-y / r), phi = PI_DIV_2 - 2 * atan(ts), i;\n                for (i = 0; i <= proj.INVERSE_ITERATIONS; i++) {\n                    var con = ecc * sin(phi), p = pow((1 - con) / (1 + con), ecch), dphi = PI_DIV_2 - 2 * atan(ts * p) - phi;\n                    phi += dphi;\n                    if (math.abs(dphi) <= proj.INVERSE_CONVERGENCE) {\n                        break;\n                    }\n                }\n                return deg(phi);\n            }\n        });\n        var SphericalMercator = Mercator.extend({\n            MAX_LAT: 85.0511287798,\n            _projectLat: function (lat) {\n                var r = this.options.datum.a, y = rad(lat), ts = tan(PI_DIV_4 + y / 2);\n                return r * log(ts);\n            },\n            _inverseY: function (y) {\n                var r = this.options.datum.a, ts = exp(-y / r);\n                return deg(PI_DIV_2 - 2 * atan(ts));\n            }\n        });\n        var Equirectangular = Class.extend({\n            forward: function (loc) {\n                return new Point(loc.lng, loc.lat);\n            },\n            inverse: function (point) {\n                return new Location(point.y, point.x);\n            }\n        });\n        var EPSG3857 = Class.extend({\n            init: function () {\n                var crs = this, proj = crs._proj = new SphericalMercator();\n                var c = this.c = 2 * PI * proj.options.datum.a;\n                this._tm = g.transform().translate(0.5, 0.5).scale(1 / c, -1 / c);\n                this._itm = g.transform().scale(c, -c).translate(-0.5, -0.5);\n            },\n            toPoint: function (loc, scale, clamp) {\n                var point = this._proj.forward(loc, clamp);\n                return point.transform(this._tm).scale(scale || 1);\n            },\n            toLocation: function (point, scale, clamp) {\n                point = point.clone().scale(1 / (scale || 1)).transform(this._itm);\n                return this._proj.inverse(point, clamp);\n            }\n        });\n        var EPSG3395 = Class.extend({\n            init: function () {\n                this._proj = new Mercator();\n            },\n            toPoint: function (loc) {\n                return this._proj.forward(loc);\n            },\n            toLocation: function (point) {\n                return this._proj.inverse(point);\n            }\n        });\n        var EPSG4326 = Class.extend({\n            init: function () {\n                this._proj = new Equirectangular();\n            },\n            toPoint: function (loc) {\n                return this._proj.forward(loc);\n            },\n            toLocation: function (point) {\n                return this._proj.inverse(point);\n            }\n        });\n        deepExtend(dataviz, {\n            map: {\n                crs: {\n                    EPSG3395: EPSG3395,\n                    EPSG3857: EPSG3857,\n                    EPSG4326: EPSG4326\n                },\n                datums: { WGS84: WGS84 },\n                projections: {\n                    Equirectangular: Equirectangular,\n                    Mercator: Mercator,\n                    SphericalMercator: SphericalMercator\n                }\n            }\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/layers/base', [\n        'kendo.core',\n        'dataviz/map/location'\n    ], f);\n}(function () {\n    (function ($, undefined) {\n        var proxy = $.proxy, kendo = window.kendo, Class = kendo.Class, dataviz = kendo.dataviz, deepExtend = kendo.deepExtend, Extent = dataviz.map.Extent, util = kendo.drawing.util, defined = util.defined;\n        var Layer = Class.extend({\n            init: function (map, options) {\n                this._initOptions(options);\n                this.map = map;\n                this.element = $('<div class=\\'k-layer\\'></div>').css({\n                    'zIndex': this.options.zIndex,\n                    'opacity': this.options.opacity\n                }).appendTo(map.scrollElement);\n                this._beforeReset = proxy(this._beforeReset, this);\n                this._reset = proxy(this._reset, this);\n                this._resize = proxy(this._resize, this);\n                this._panEnd = proxy(this._panEnd, this);\n                this._activate();\n                this._updateAttribution();\n            },\n            destroy: function () {\n                this._deactivate();\n            },\n            show: function () {\n                this.reset();\n                this._activate();\n                this._applyExtent(true);\n            },\n            hide: function () {\n                this._deactivate();\n                this._setVisibility(false);\n            },\n            reset: function () {\n                this._beforeReset();\n                this._reset();\n            },\n            _reset: function () {\n                this._applyExtent();\n            },\n            _beforeReset: $.noop,\n            _resize: $.noop,\n            _panEnd: function () {\n                this._applyExtent();\n            },\n            _applyExtent: function () {\n                var options = this.options;\n                var zoom = this.map.zoom();\n                var matchMinZoom = !defined(options.minZoom) || zoom >= options.minZoom;\n                var matchMaxZoom = !defined(options.maxZoom) || zoom <= options.maxZoom;\n                var extent = Extent.create(options.extent);\n                var inside = !extent || extent.overlaps(this.map.extent());\n                this._setVisibility(matchMinZoom && matchMaxZoom && inside);\n            },\n            _setVisibility: function (visible) {\n                this.element.css('display', visible ? '' : 'none');\n            },\n            _activate: function () {\n                var map = this.map;\n                map.bind('beforeReset', this._beforeReset);\n                map.bind('reset', this._reset);\n                map.bind('resize', this._resize);\n                map.bind('panEnd', this._panEnd);\n            },\n            _deactivate: function () {\n                var map = this.map;\n                map.unbind('beforeReset', this._beforeReset);\n                map.unbind('reset', this._reset);\n                map.unbind('resize', this._resize);\n                map.unbind('panEnd', this._panEnd);\n            },\n            _updateAttribution: function () {\n                var attr = this.map.attribution;\n                if (attr) {\n                    attr.add(this.options.attribution);\n                }\n            }\n        });\n        deepExtend(dataviz, { map: { layers: { Layer: Layer } } });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/layers/shape', [\n        'dataviz/map/layers/base',\n        'dataviz/map/location'\n    ], f);\n}(function () {\n    (function ($, undefined) {\n        var proxy = $.proxy, kendo = window.kendo, Class = kendo.Class, DataSource = kendo.data.DataSource, dataviz = kendo.dataviz, deepExtend = kendo.deepExtend, g = kendo.geometry, d = kendo.drawing, Group = d.Group, last = d.util.last, defined = d.util.defined, map = dataviz.map, Location = map.Location, Layer = map.layers.Layer;\n        var ShapeLayer = Layer.extend({\n            init: function (map, options) {\n                this._pan = proxy(this._pan, this);\n                Layer.fn.init.call(this, map, options);\n                this.surface = d.Surface.create(this.element, {\n                    width: map.scrollElement.width(),\n                    height: map.scrollElement.height()\n                });\n                this._initRoot();\n                this.movable = new kendo.ui.Movable(this.surface.element);\n                this._markers = [];\n                this._click = this._handler('shapeClick');\n                this.surface.bind('click', this._click);\n                this._mouseenter = this._handler('shapeMouseEnter');\n                this.surface.bind('mouseenter', this._mouseenter);\n                this._mouseleave = this._handler('shapeMouseLeave');\n                this.surface.bind('mouseleave', this._mouseleave);\n                this._initDataSource();\n            },\n            options: { autoBind: true },\n            destroy: function () {\n                Layer.fn.destroy.call(this);\n                this.surface.destroy();\n                this.dataSource.unbind('change', this._dataChange);\n            },\n            setDataSource: function (dataSource) {\n                if (this.dataSource) {\n                    this.dataSource.unbind('change', this._dataChange);\n                }\n                this.dataSource = kendo.data.DataSource.create(dataSource);\n                this.dataSource.bind('change', this._dataChange);\n                if (this.options.autoBind) {\n                    this.dataSource.fetch();\n                }\n            },\n            _reset: function () {\n                Layer.fn._reset.call(this);\n                this._translateSurface();\n                if (this._data) {\n                    this._load(this._data);\n                }\n            },\n            _initRoot: function () {\n                this._root = new Group();\n                this.surface.draw(this._root);\n            },\n            _beforeReset: function () {\n                this.surface.clear();\n                this._initRoot();\n            },\n            _resize: function () {\n                this.surface.size(this.map.size());\n            },\n            _initDataSource: function () {\n                var dsOptions = this.options.dataSource;\n                this._dataChange = proxy(this._dataChange, this);\n                this.dataSource = DataSource.create(dsOptions).bind('change', this._dataChange);\n                if (dsOptions && this.options.autoBind) {\n                    this.dataSource.fetch();\n                }\n            },\n            _dataChange: function (e) {\n                this._data = e.sender.view();\n                this._load(this._data);\n            },\n            _load: function (data) {\n                this._clearMarkers();\n                if (!this._loader) {\n                    this._loader = new GeoJSONLoader(this.map, this.options.style, this);\n                }\n                var container = new Group();\n                for (var i = 0; i < data.length; i++) {\n                    var shape = this._loader.parse(data[i]);\n                    if (shape) {\n                        container.append(shape);\n                    }\n                }\n                this._root.clear();\n                this._root.append(container);\n            },\n            shapeCreated: function (shape) {\n                var cancelled = false;\n                if (shape instanceof d.Circle) {\n                    cancelled = defined(this._createMarker(shape));\n                }\n                if (!cancelled) {\n                    var args = {\n                        layer: this,\n                        shape: shape\n                    };\n                    cancelled = this.map.trigger('shapeCreated', args);\n                }\n                return cancelled;\n            },\n            featureCreated: function (e) {\n                e.layer = this;\n                this.map.trigger('shapeFeatureCreated', e);\n            },\n            _createMarker: function (shape) {\n                var marker = this.map.markers.bind({ location: shape.location }, shape.dataItem);\n                if (marker) {\n                    this._markers.push(marker);\n                }\n                return marker;\n            },\n            _clearMarkers: function () {\n                for (var i = 0; i < this._markers.length; i++) {\n                    this.map.markers.remove(this._markers[i]);\n                }\n                this._markers = [];\n            },\n            _pan: function () {\n                if (!this._panning) {\n                    this._panning = true;\n                    this.surface.suspendTracking();\n                }\n            },\n            _panEnd: function (e) {\n                Layer.fn._panEnd.call(this, e);\n                this._translateSurface();\n                this.surface.resumeTracking();\n                this._panning = false;\n            },\n            _translateSurface: function () {\n                var map = this.map;\n                var nw = map.locationToView(map.extent().nw);\n                if (this.surface.translate) {\n                    this.surface.translate(nw);\n                    this.movable.moveTo({\n                        x: nw.x,\n                        y: nw.y\n                    });\n                }\n            },\n            _handler: function (event) {\n                var layer = this;\n                return function (e) {\n                    if (e.element) {\n                        var args = {\n                            layer: layer,\n                            shape: e.element,\n                            originalEvent: e.originalEvent\n                        };\n                        layer.map.trigger(event, args);\n                    }\n                };\n            },\n            _activate: function () {\n                Layer.fn._activate.call(this);\n                this.map.bind('pan', this._pan);\n            },\n            _deactivate: function () {\n                Layer.fn._deactivate.call(this);\n                this.map.unbind('pan', this._pan);\n            }\n        });\n        var GeoJSONLoader = Class.extend({\n            init: function (locator, defaultStyle, observer) {\n                this.observer = observer;\n                this.locator = locator;\n                this.style = defaultStyle;\n            },\n            parse: function (item) {\n                var root = new Group();\n                var unwrap = true;\n                if (item.type === 'Feature') {\n                    unwrap = false;\n                    this._loadGeometryTo(root, item.geometry, item);\n                    this._featureCreated(root, item);\n                } else {\n                    this._loadGeometryTo(root, item, item);\n                }\n                if (unwrap && root.children.length < 2) {\n                    root = root.children[0];\n                }\n                return root;\n            },\n            _shapeCreated: function (shape) {\n                var cancelled = false;\n                if (this.observer && this.observer.shapeCreated) {\n                    cancelled = this.observer.shapeCreated(shape);\n                }\n                return cancelled;\n            },\n            _featureCreated: function (group, dataItem) {\n                if (this.observer && this.observer.featureCreated) {\n                    this.observer.featureCreated({\n                        group: group,\n                        dataItem: dataItem,\n                        properties: dataItem.properties\n                    });\n                }\n            },\n            _loadGeometryTo: function (container, geometry, dataItem) {\n                var coords = geometry.coordinates;\n                var i;\n                var path;\n                switch (geometry.type) {\n                case 'LineString':\n                    path = this._loadPolygon(container, [coords], dataItem);\n                    this._setLineFill(path);\n                    break;\n                case 'MultiLineString':\n                    for (i = 0; i < coords.length; i++) {\n                        path = this._loadPolygon(container, [coords[i]], dataItem);\n                        this._setLineFill(path);\n                    }\n                    break;\n                case 'Polygon':\n                    this._loadPolygon(container, coords, dataItem);\n                    break;\n                case 'MultiPolygon':\n                    for (i = 0; i < coords.length; i++) {\n                        this._loadPolygon(container, coords[i], dataItem);\n                    }\n                    break;\n                case 'Point':\n                    this._loadPoint(container, coords, dataItem);\n                    break;\n                case 'MultiPoint':\n                    for (i = 0; i < coords.length; i++) {\n                        this._loadPoint(container, coords[i], dataItem);\n                    }\n                    break;\n                }\n            },\n            _setLineFill: function (path) {\n                var segments = path.segments;\n                if (segments.length < 4 || !segments[0].anchor().equals(last(segments).anchor())) {\n                    path.options.fill = null;\n                }\n            },\n            _loadShape: function (container, shape) {\n                if (!this._shapeCreated(shape)) {\n                    container.append(shape);\n                }\n                return shape;\n            },\n            _loadPolygon: function (container, rings, dataItem) {\n                var shape = this._buildPolygon(rings);\n                shape.dataItem = dataItem;\n                return this._loadShape(container, shape);\n            },\n            _buildPolygon: function (rings) {\n                var type = rings.length > 1 ? d.MultiPath : d.Path;\n                var path = new type(this.style);\n                for (var i = 0; i < rings.length; i++) {\n                    for (var j = 0; j < rings[i].length; j++) {\n                        var point = this.locator.locationToView(Location.fromLngLat(rings[i][j]));\n                        if (j === 0) {\n                            path.moveTo(point.x, point.y);\n                        } else {\n                            path.lineTo(point.x, point.y);\n                        }\n                    }\n                }\n                return path;\n            },\n            _loadPoint: function (container, coords, dataItem) {\n                var location = Location.fromLngLat(coords);\n                var point = this.locator.locationToView(location);\n                var circle = new g.Circle(point, 10);\n                var shape = new d.Circle(circle, this.style);\n                shape.dataItem = dataItem;\n                shape.location = location;\n                return this._loadShape(container, shape);\n            }\n        });\n        deepExtend(kendo.data, {\n            schemas: {\n                geojson: {\n                    type: 'json',\n                    data: function (data) {\n                        if (data.type === 'FeatureCollection') {\n                            return data.features;\n                        }\n                        if (data.type === 'GeometryCollection') {\n                            return data.geometries;\n                        }\n                        return data;\n                    }\n                }\n            },\n            transports: { geojson: { read: { dataType: 'json' } } }\n        });\n        deepExtend(dataviz, {\n            map: {\n                layers: {\n                    shape: ShapeLayer,\n                    ShapeLayer: ShapeLayer\n                },\n                GeoJSONLoader: GeoJSONLoader\n            }\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/layers/bubble', ['dataviz/map/layers/shape'], f);\n}(function () {\n    (function ($, undefined) {\n        var kendo = window.kendo, getter = kendo.getter, dataviz = kendo.dataviz, deepExtend = kendo.deepExtend, g = kendo.geometry, d = kendo.drawing, util = d.util, defined = util.defined, map = dataviz.map, Location = map.Location, ShapeLayer = map.layers.ShapeLayer;\n        var BubbleLayer = ShapeLayer.extend({\n            options: {\n                autoBind: true,\n                locationField: 'location',\n                valueField: 'value',\n                minSize: 0,\n                maxSize: 100,\n                scale: 'sqrt',\n                symbol: 'circle'\n            },\n            _load: function (data) {\n                this.surface.clear();\n                if (data.length === 0) {\n                    return;\n                }\n                var opt = this.options;\n                var getValue = getter(opt.valueField);\n                data = data.slice(0);\n                data.sort(function (a, b) {\n                    return getValue(b) - getValue(a);\n                });\n                var scaleType = this._scaleType();\n                var scale;\n                for (var i = 0; i < data.length; i++) {\n                    var dataItem = data[i];\n                    var location = getter(opt.locationField)(dataItem);\n                    var value = getter(opt.valueField)(dataItem);\n                    if (defined(location) && defined(value)) {\n                        if (!scale) {\n                            scale = new scaleType([\n                                0,\n                                value\n                            ], [\n                                opt.minSize,\n                                opt.maxSize\n                            ]);\n                        }\n                        location = Location.create(location);\n                        var center = this.map.locationToView(location);\n                        var size = scale.map(value);\n                        var symbol = this._createSymbol({\n                            center: center,\n                            size: size,\n                            style: opt.style,\n                            dataItem: dataItem,\n                            location: location\n                        });\n                        symbol.dataItem = dataItem;\n                        symbol.location = location;\n                        symbol.value = value;\n                        this._drawSymbol(symbol);\n                    }\n                }\n            },\n            _scaleType: function () {\n                var scale = this.options.scale;\n                if (kendo.isFunction(scale)) {\n                    return scale;\n                }\n                return dataviz.map.scales[scale];\n            },\n            _createSymbol: function (args) {\n                var symbol = this.options.symbol;\n                if (!kendo.isFunction(symbol)) {\n                    symbol = dataviz.map.symbols[symbol];\n                }\n                return symbol(args);\n            },\n            _drawSymbol: function (shape) {\n                var args = {\n                    layer: this,\n                    shape: shape\n                };\n                var cancelled = this.map.trigger('shapeCreated', args);\n                if (!cancelled) {\n                    this.surface.draw(shape);\n                }\n            }\n        });\n        var SqrtScale = kendo.Class.extend({\n            init: function (domain, range) {\n                this._domain = domain;\n                this._range = range;\n                var domainRange = Math.sqrt(domain[1]) - Math.sqrt(domain[0]);\n                var outputRange = range[1] - range[0];\n                this._ratio = outputRange / domainRange;\n            },\n            map: function (value) {\n                var rel = (Math.sqrt(value) - Math.sqrt(this._domain[0])) * this._ratio;\n                return this._range[0] + rel;\n            }\n        });\n        var Symbols = {\n            circle: function (args) {\n                var geo = new g.Circle(args.center, args.size / 2);\n                return new d.Circle(geo, args.style);\n            },\n            square: function (args) {\n                var path = new d.Path(args.style);\n                var halfSize = args.size / 2;\n                var center = args.center;\n                path.moveTo(center.x - halfSize, center.y - halfSize).lineTo(center.x + halfSize, center.y - halfSize).lineTo(center.x + halfSize, center.y + halfSize).lineTo(center.x - halfSize, center.y + halfSize).close();\n                return path;\n            }\n        };\n        deepExtend(dataviz, {\n            map: {\n                layers: {\n                    bubble: BubbleLayer,\n                    BubbleLayer: BubbleLayer\n                },\n                scales: { sqrt: SqrtScale },\n                symbols: Symbols\n            }\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/layers/tile', [\n        'dataviz/map/layers/base',\n        'dataviz/map/location'\n    ], f);\n}(function () {\n    (function ($, undefined) {\n        var math = Math, proxy = $.proxy, kendo = window.kendo, Class = kendo.Class, template = kendo.template, dataviz = kendo.dataviz, deepExtend = kendo.deepExtend, g = kendo.geometry, Point = g.Point, Layer = dataviz.map.layers.Layer, util = kendo.util, renderSize = util.renderSize, drawingUtil = kendo.drawing.util, round = drawingUtil.round, limit = drawingUtil.limitValue;\n        var TileLayer = Layer.extend({\n            init: function (map, options) {\n                Layer.fn.init.call(this, map, options);\n                if (typeof this.options.subdomains === 'string') {\n                    this.options.subdomains = this.options.subdomains.split('');\n                }\n                var viewType = this._viewType();\n                this._view = new viewType(this.element, this.options);\n            },\n            destroy: function () {\n                Layer.fn.destroy.call(this);\n                this._view.destroy();\n                this._view = null;\n            },\n            _beforeReset: function () {\n                var map = this.map;\n                var origin = map.locationToLayer(map.extent().nw).round();\n                this._view.viewOrigin(origin);\n            },\n            _reset: function () {\n                Layer.fn._reset.call(this);\n                this._updateView();\n                this._view.reset();\n            },\n            _viewType: function () {\n                return TileView;\n            },\n            _activate: function () {\n                Layer.fn._activate.call(this);\n                if (!kendo.support.mobileOS) {\n                    if (!this._pan) {\n                        this._pan = kendo.throttle(proxy(this._render, this), 100);\n                    }\n                    this.map.bind('pan', this._pan);\n                }\n            },\n            _deactivate: function () {\n                Layer.fn._deactivate.call(this);\n                if (this._pan) {\n                    this.map.unbind('pan', this._pan);\n                }\n            },\n            _updateView: function () {\n                var view = this._view, map = this.map, extent = map.extent(), extentToPoint = {\n                        nw: map.locationToLayer(extent.nw).round(),\n                        se: map.locationToLayer(extent.se).round()\n                    };\n                view.center(map.locationToLayer(map.center()));\n                view.extent(extentToPoint);\n                view.zoom(map.zoom());\n            },\n            _resize: function () {\n                this._render();\n            },\n            _panEnd: function (e) {\n                Layer.fn._panEnd.call(this, e);\n                this._render();\n            },\n            _render: function () {\n                this._updateView();\n                this._view.render();\n            }\n        });\n        var TileView = Class.extend({\n            init: function (element, options) {\n                this.element = element;\n                this._initOptions(options);\n                this.pool = new TilePool();\n            },\n            options: {\n                tileSize: 256,\n                subdomains: [\n                    'a',\n                    'b',\n                    'c'\n                ],\n                urlTemplate: ''\n            },\n            center: function (center) {\n                this._center = center;\n            },\n            extent: function (extent) {\n                this._extent = extent;\n            },\n            viewOrigin: function (origin) {\n                this._viewOrigin = origin;\n            },\n            zoom: function (zoom) {\n                this._zoom = zoom;\n            },\n            pointToTileIndex: function (point) {\n                return new Point(math.floor(point.x / this.options.tileSize), math.floor(point.y / this.options.tileSize));\n            },\n            tileCount: function () {\n                var size = this.size(), firstTileIndex = this.pointToTileIndex(this._extent.nw), nw = this._extent.nw, point = this.indexToPoint(firstTileIndex).translate(-nw.x, -nw.y);\n                return {\n                    x: math.ceil((math.abs(point.x) + size.width) / this.options.tileSize),\n                    y: math.ceil((math.abs(point.y) + size.height) / this.options.tileSize)\n                };\n            },\n            size: function () {\n                var nw = this._extent.nw, se = this._extent.se, diff = se.clone().translate(-nw.x, -nw.y);\n                return {\n                    width: diff.x,\n                    height: diff.y\n                };\n            },\n            indexToPoint: function (index) {\n                var x = index.x, y = index.y;\n                return new Point(x * this.options.tileSize, y * this.options.tileSize);\n            },\n            subdomainText: function () {\n                var subdomains = this.options.subdomains;\n                return subdomains[this.subdomainIndex++ % subdomains.length];\n            },\n            destroy: function () {\n                this.element.empty();\n                this.pool.empty();\n            },\n            reset: function () {\n                this.pool.reset();\n                this.subdomainIndex = 0;\n                this.render();\n            },\n            render: function () {\n                var size = this.tileCount(), firstTileIndex = this.pointToTileIndex(this._extent.nw), tile, x, y;\n                for (x = 0; x < size.x; x++) {\n                    for (y = 0; y < size.y; y++) {\n                        tile = this.createTile({\n                            x: firstTileIndex.x + x,\n                            y: firstTileIndex.y + y\n                        });\n                        if (!tile.visible) {\n                            tile.show();\n                        }\n                    }\n                }\n            },\n            createTile: function (currentIndex) {\n                var options = this.tileOptions(currentIndex);\n                var tile = this.pool.get(this._center, options);\n                if (tile.element.parent().length === 0) {\n                    this.element.append(tile.element);\n                }\n                return tile;\n            },\n            tileOptions: function (currentIndex) {\n                var index = this.wrapIndex(currentIndex), point = this.indexToPoint(currentIndex), origin = this._viewOrigin, offset = point.clone().translate(-origin.x, -origin.y);\n                return {\n                    index: index,\n                    currentIndex: currentIndex,\n                    point: point,\n                    offset: roundPoint(offset),\n                    zoom: this._zoom,\n                    size: this.options.tileSize,\n                    subdomain: this.subdomainText(),\n                    urlTemplate: this.options.urlTemplate,\n                    errorUrlTemplate: this.options.errorUrlTemplate\n                };\n            },\n            wrapIndex: function (index) {\n                var boundary = math.pow(2, this._zoom);\n                return {\n                    x: this.wrapValue(index.x, boundary),\n                    y: limit(index.y, 0, boundary - 1)\n                };\n            },\n            wrapValue: function (value, boundary) {\n                var remainder = math.abs(value) % boundary;\n                if (value >= 0) {\n                    value = remainder;\n                } else {\n                    value = boundary - (remainder === 0 ? boundary : remainder);\n                }\n                return value;\n            }\n        });\n        var ImageTile = Class.extend({\n            init: function (id, options) {\n                this.id = id;\n                this.visible = true;\n                this._initOptions(options);\n                this.createElement();\n                this.show();\n            },\n            options: {\n                urlTemplate: '',\n                errorUrlTemplate: ''\n            },\n            createElement: function () {\n                this.element = $('<img style=\\'position: absolute; display: block;\\' alt=\\'\\' />').css({\n                    width: this.options.size,\n                    height: this.options.size\n                }).on('error', proxy(function (e) {\n                    if (this.errorUrl()) {\n                        e.target.setAttribute('src', this.errorUrl());\n                    } else {\n                        e.target.removeAttribute('src');\n                    }\n                }, this));\n            },\n            show: function () {\n                var element = this.element[0];\n                element.style.top = renderSize(this.options.offset.y);\n                element.style.left = renderSize(this.options.offset.x);\n                var url = this.url();\n                if (url) {\n                    element.setAttribute('src', url);\n                }\n                element.style.visibility = 'visible';\n                this.visible = true;\n            },\n            hide: function () {\n                this.element[0].style.visibility = 'hidden';\n                this.visible = false;\n            },\n            url: function () {\n                var urlResult = template(this.options.urlTemplate);\n                return urlResult(this.urlOptions());\n            },\n            errorUrl: function () {\n                var urlResult = template(this.options.errorUrlTemplate);\n                return urlResult(this.urlOptions());\n            },\n            urlOptions: function () {\n                var options = this.options;\n                return {\n                    zoom: options.zoom,\n                    subdomain: options.subdomain,\n                    z: options.zoom,\n                    x: options.index.x,\n                    y: options.index.y,\n                    s: options.subdomain,\n                    quadkey: options.quadkey,\n                    q: options.quadkey,\n                    culture: options.culture,\n                    c: options.culture\n                };\n            },\n            destroy: function () {\n                if (this.element) {\n                    this.element.remove();\n                    this.element = null;\n                }\n            }\n        });\n        var TilePool = Class.extend({\n            init: function () {\n                this._items = [];\n            },\n            options: { maxSize: 100 },\n            get: function (center, options) {\n                if (this._items.length >= this.options.maxSize) {\n                    this._remove(center);\n                }\n                return this._create(options);\n            },\n            empty: function () {\n                var items = this._items;\n                for (var i = 0; i < items.length; i++) {\n                    items[i].destroy();\n                }\n                this._items = [];\n            },\n            reset: function () {\n                var items = this._items;\n                for (var i = 0; i < items.length; i++) {\n                    items[i].hide();\n                }\n            },\n            _create: function (options) {\n                var items = this._items;\n                var tile;\n                var id = util.hashKey(options.point.toString() + options.offset.toString() + options.zoom + options.urlTemplate);\n                for (var i = 0; i < items.length; i++) {\n                    if (items[i].id === id) {\n                        tile = items[i];\n                        break;\n                    }\n                }\n                if (tile) {\n                    tile.show();\n                } else {\n                    tile = new ImageTile(id, options);\n                    this._items.push(tile);\n                }\n                return tile;\n            },\n            _remove: function (center) {\n                var items = this._items;\n                var maxDist = -1;\n                var index = -1;\n                for (var i = 0; i < items.length; i++) {\n                    var dist = items[i].options.point.distanceTo(center);\n                    if (dist > maxDist && !items[i].visible) {\n                        index = i;\n                        maxDist = dist;\n                    }\n                }\n                if (index !== -1) {\n                    items[index].destroy();\n                    items.splice(index, 1);\n                }\n            }\n        });\n        function roundPoint(point) {\n            return new Point(round(point.x), round(point.y));\n        }\n        deepExtend(dataviz, {\n            map: {\n                layers: {\n                    tile: TileLayer,\n                    TileLayer: TileLayer,\n                    ImageTile: ImageTile,\n                    TilePool: TilePool,\n                    TileView: TileView\n                }\n            }\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/layers/bing', ['dataviz/map/layers/tile'], f);\n}(function () {\n    (function ($, undefined) {\n        var kendo = window.kendo, dataviz = kendo.dataviz, deepExtend = kendo.deepExtend, defined = kendo.drawing.util.defined, Extent = dataviz.map.Extent, Location = dataviz.map.Location, TileLayer = dataviz.map.layers.TileLayer, TileView = dataviz.map.layers.TileView;\n        var BingLayer = TileLayer.extend({\n            init: function (map, options) {\n                this.options.baseUrl = this._scheme() + '://dev.virtualearth.net/REST/v1/Imagery/Metadata/';\n                TileLayer.fn.init.call(this, map, options);\n                this._onMetadata = $.proxy(this._onMetadata, this);\n                this._fetchMetadata();\n            },\n            options: { imagerySet: 'road' },\n            _fetchMetadata: function () {\n                var options = this.options;\n                if (!options.key) {\n                    throw new Error('Bing tile layer: API key is required');\n                }\n                $.ajax({\n                    url: options.baseUrl + options.imagerySet,\n                    data: {\n                        output: 'json',\n                        include: 'ImageryProviders',\n                        key: options.key,\n                        uriScheme: this._scheme()\n                    },\n                    type: 'get',\n                    dataType: 'jsonp',\n                    jsonp: 'jsonp',\n                    success: this._onMetadata\n                });\n            },\n            _scheme: function (proto) {\n                proto = proto || window.location.protocol;\n                return proto.replace(':', '') === 'https' ? 'https' : 'http';\n            },\n            _onMetadata: function (data) {\n                if (data && data.resourceSets.length) {\n                    var resource = this.resource = data.resourceSets[0].resources[0];\n                    deepExtend(this._view.options, {\n                        urlTemplate: resource.imageUrl.replace('{subdomain}', '#= subdomain #').replace('{quadkey}', '#= quadkey #').replace('{culture}', '#= culture #'),\n                        subdomains: resource.imageUrlSubdomains\n                    });\n                    var options = this.options;\n                    if (!defined(options.minZoom)) {\n                        options.minZoom = resource.zoomMin;\n                    }\n                    if (!defined(options.maxZoom)) {\n                        options.maxZoom = resource.zoomMax;\n                    }\n                    this._addAttribution();\n                    if (this.element.css('display') !== 'none') {\n                        this._reset();\n                    }\n                }\n            },\n            _viewType: function () {\n                return BingView;\n            },\n            _addAttribution: function () {\n                var attr = this.map.attribution;\n                if (attr) {\n                    var items = this.resource.imageryProviders;\n                    if (items) {\n                        for (var i = 0; i < items.length; i++) {\n                            var item = items[i];\n                            for (var y = 0; y < item.coverageAreas.length; y++) {\n                                var area = item.coverageAreas[y];\n                                attr.add({\n                                    text: item.attribution,\n                                    minZoom: area.zoomMin,\n                                    maxZoom: area.zoomMax,\n                                    extent: new Extent(new Location(area.bbox[2], area.bbox[1]), new Location(area.bbox[0], area.bbox[3]))\n                                });\n                            }\n                        }\n                    }\n                }\n            },\n            imagerySet: function (value) {\n                if (value) {\n                    this.options.imagerySet = value;\n                    this.map.attribution.clear();\n                    this._fetchMetadata();\n                } else {\n                    return this.options.imagerySet;\n                }\n            }\n        });\n        var BingView = TileView.extend({\n            options: { culture: 'en-US' },\n            tileOptions: function (currentIndex) {\n                var options = TileView.fn.tileOptions.call(this, currentIndex);\n                options.culture = this.options.culture;\n                options.quadkey = this.tileQuadKey(this.wrapIndex(currentIndex));\n                return options;\n            },\n            tileQuadKey: function (index) {\n                var quadKey = '', digit, mask, i;\n                for (i = this._zoom; i > 0; i--) {\n                    digit = 0;\n                    mask = 1 << i - 1;\n                    if ((index.x & mask) !== 0) {\n                        digit++;\n                    }\n                    if ((index.y & mask) !== 0) {\n                        digit += 2;\n                    }\n                    quadKey += digit;\n                }\n                return quadKey;\n            }\n        });\n        deepExtend(dataviz, {\n            map: {\n                layers: {\n                    bing: BingLayer,\n                    BingLayer: BingLayer,\n                    BingView: BingView\n                }\n            }\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/layers/marker', [\n        'dataviz/map/layers/base',\n        'dataviz/map/location',\n        'kendo.data',\n        'kendo.tooltip'\n    ], f);\n}(function () {\n    (function ($, undefined) {\n        var doc = document, math = Math, indexOf = $.inArray, proxy = $.proxy, kendo = window.kendo, Class = kendo.Class, DataSource = kendo.data.DataSource, Tooltip = kendo.ui.Tooltip, dataviz = kendo.dataviz, deepExtend = kendo.deepExtend, map = dataviz.map, Location = map.Location, Layer = map.layers.Layer;\n        var MarkerLayer = Layer.extend({\n            init: function (map, options) {\n                Layer.fn.init.call(this, map, options);\n                this._markerClick = proxy(this._markerClick, this);\n                this.element.on('click', '.k-marker', this._markerClick);\n                this.items = [];\n                this._initDataSource();\n            },\n            destroy: function () {\n                Layer.fn.destroy.call(this);\n                this.element.off('click', '.k-marker', this._markerClick);\n                this.dataSource.unbind('change', this._dataChange);\n                this.clear();\n            },\n            options: {\n                zIndex: 1000,\n                autoBind: true,\n                dataSource: {},\n                locationField: 'location',\n                titleField: 'title'\n            },\n            add: function (arg) {\n                if ($.isArray(arg)) {\n                    for (var i = 0; i < arg.length; i++) {\n                        this._addOne(arg[i]);\n                    }\n                } else {\n                    return this._addOne(arg);\n                }\n            },\n            remove: function (marker) {\n                marker.destroy();\n                var index = indexOf(marker, this.items);\n                if (index > -1) {\n                    this.items.splice(index, 1);\n                }\n            },\n            clear: function () {\n                for (var i = 0; i < this.items.length; i++) {\n                    this.items[i].destroy();\n                }\n                this.items = [];\n            },\n            update: function (marker) {\n                var loc = marker.location();\n                if (loc) {\n                    marker.showAt(this.map.locationToView(loc));\n                    var args = {\n                        marker: marker,\n                        layer: this\n                    };\n                    this.map.trigger('markerActivate', args);\n                }\n            },\n            _reset: function () {\n                Layer.fn._reset.call(this);\n                var items = this.items;\n                for (var i = 0; i < items.length; i++) {\n                    this.update(items[i]);\n                }\n            },\n            bind: function (options, dataItem) {\n                var marker = map.Marker.create(options, this.options);\n                marker.dataItem = dataItem;\n                var args = {\n                    marker: marker,\n                    layer: this\n                };\n                var cancelled = this.map.trigger('markerCreated', args);\n                if (!cancelled) {\n                    this.add(marker);\n                    return marker;\n                }\n            },\n            setDataSource: function (dataSource) {\n                if (this.dataSource) {\n                    this.dataSource.unbind('change', this._dataChange);\n                }\n                this.dataSource = kendo.data.DataSource.create(dataSource);\n                this.dataSource.bind('change', this._dataChange);\n                if (this.options.autoBind) {\n                    this.dataSource.fetch();\n                }\n            },\n            _addOne: function (arg) {\n                var marker = Marker.create(arg, this.options);\n                marker.addTo(this);\n                return marker;\n            },\n            _initDataSource: function () {\n                var dsOptions = this.options.dataSource;\n                this._dataChange = proxy(this._dataChange, this);\n                this.dataSource = DataSource.create(dsOptions).bind('change', this._dataChange);\n                if (dsOptions && this.options.autoBind) {\n                    this.dataSource.fetch();\n                }\n            },\n            _dataChange: function (e) {\n                this._load(e.sender.view());\n            },\n            _load: function (data) {\n                this._data = data;\n                this.clear();\n                var getLocation = kendo.getter(this.options.locationField);\n                var getTitle = kendo.getter(this.options.titleField);\n                for (var i = 0; i < data.length; i++) {\n                    var dataItem = data[i];\n                    this.bind({\n                        location: getLocation(dataItem),\n                        title: getTitle(dataItem)\n                    }, dataItem);\n                }\n            },\n            _markerClick: function (e) {\n                var args = {\n                    marker: $(e.target).data('kendoMarker'),\n                    layer: this\n                };\n                this.map.trigger('markerClick', args);\n            }\n        });\n        var Marker = Class.extend({\n            init: function (options) {\n                this.options = options || {};\n            },\n            addTo: function (parent) {\n                this.layer = parent.markers || parent;\n                this.layer.items.push(this);\n                this.layer.update(this);\n            },\n            location: function (value) {\n                if (value) {\n                    this.options.location = Location.create(value).toArray();\n                    if (this.layer) {\n                        this.layer.update(this);\n                    }\n                    return this;\n                } else {\n                    return Location.create(this.options.location);\n                }\n            },\n            showAt: function (point) {\n                this.render();\n                this.element.css({\n                    left: math.round(point.x),\n                    top: math.round(point.y)\n                });\n                if (this.tooltip && this.tooltip.popup) {\n                    this.tooltip.popup._position();\n                }\n            },\n            hide: function () {\n                if (this.element) {\n                    this.element.remove();\n                    this.element = null;\n                }\n                if (this.tooltip) {\n                    this.tooltip.destroy();\n                    this.tooltip = null;\n                }\n            },\n            destroy: function () {\n                this.layer = null;\n                this.hide();\n            },\n            render: function () {\n                if (!this.element) {\n                    var options = this.options;\n                    var layer = this.layer;\n                    this.element = $(doc.createElement('span')).addClass('k-marker k-icon k-i-marker-' + kendo.toHyphens(options.shape || 'pin')).attr('title', options.title).attr(options.attributes || {}).data('kendoMarker', this).css('zIndex', options.zIndex);\n                    if (layer) {\n                        layer.element.append(this.element);\n                    }\n                    this.renderTooltip();\n                }\n            },\n            renderTooltip: function () {\n                var marker = this;\n                var title = marker.options.title;\n                var options = marker.options.tooltip || {};\n                if (options && Tooltip) {\n                    var template = options.template;\n                    if (template) {\n                        var contentTemplate = kendo.template(template);\n                        options.content = function (e) {\n                            e.location = marker.location();\n                            e.marker = marker;\n                            return contentTemplate(e);\n                        };\n                    }\n                    if (title || options.content || options.contentUrl) {\n                        this.tooltip = new Tooltip(this.element, options);\n                        this.tooltip.marker = this;\n                    }\n                }\n            }\n        });\n        Marker.create = function (arg, defaults) {\n            if (arg instanceof Marker) {\n                return arg;\n            }\n            return new Marker(deepExtend({}, defaults, arg));\n        };\n        deepExtend(dataviz, {\n            map: {\n                layers: {\n                    marker: MarkerLayer,\n                    MarkerLayer: MarkerLayer\n                },\n                Marker: Marker\n            }\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/map/main', [\n        'dataviz/map/crs',\n        'dataviz/map/location'\n    ], f);\n}(function () {\n    (function ($, undefined) {\n        var doc = document, math = Math, min = math.min, pow = math.pow, proxy = $.proxy, kendo = window.kendo, Widget = kendo.ui.Widget, deepExtend = kendo.deepExtend, dataviz = kendo.dataviz, ui = dataviz.ui, g = kendo.geometry, Point = g.Point, map = dataviz.map, Extent = map.Extent, Location = map.Location, EPSG3857 = map.crs.EPSG3857, util = kendo.util, renderPos = util.renderPos, drawingUtil = kendo.drawing.util, defined = drawingUtil.defined, limit = drawingUtil.limitValue, valueOrDefault = drawingUtil.valueOrDefault;\n        var CSS_PREFIX = 'k-', FRICTION = 0.9, FRICTION_MOBILE = 0.93, MOUSEWHEEL = 'DOMMouseScroll mousewheel', VELOCITY_MULTIPLIER = 5, DEFAULT_ZOOM_RATE = 1;\n        var Map = Widget.extend({\n            init: function (element, options) {\n                kendo.destroy(element);\n                Widget.fn.init.call(this, element);\n                this._initOptions(options);\n                this.bind(this.events, options);\n                this.crs = new EPSG3857();\n                this.element.addClass(CSS_PREFIX + this.options.name.toLowerCase()).css('position', 'relative').empty().append(doc.createElement('div'));\n                this._viewOrigin = this._getOrigin();\n                this._initScroller();\n                this._initMarkers();\n                this._initControls();\n                this._initLayers();\n                this._reset();\n                this._mousewheel = proxy(this._mousewheel, this);\n                this.element.bind(MOUSEWHEEL, this._mousewheel);\n            },\n            options: {\n                name: 'Map',\n                controls: {\n                    attribution: true,\n                    navigator: { panStep: 100 },\n                    zoom: true\n                },\n                layers: [],\n                layerDefaults: {\n                    shape: {\n                        style: {\n                            fill: { color: '#fff' },\n                            stroke: {\n                                color: '#aaa',\n                                width: 0.5\n                            }\n                        }\n                    },\n                    bubble: {\n                        style: {\n                            fill: {\n                                color: '#fff',\n                                opacity: 0.5\n                            },\n                            stroke: {\n                                color: '#aaa',\n                                width: 0.5\n                            }\n                        }\n                    },\n                    marker: {\n                        shape: 'pinTarget',\n                        tooltip: { position: 'top' }\n                    }\n                },\n                center: [\n                    0,\n                    0\n                ],\n                zoom: 3,\n                minSize: 256,\n                minZoom: 1,\n                maxZoom: 19,\n                markers: [],\n                markerDefaults: {\n                    shape: 'pinTarget',\n                    tooltip: { position: 'top' }\n                },\n                wraparound: true\n            },\n            events: [\n                'beforeReset',\n                'click',\n                'markerActivate',\n                'markerClick',\n                'markerCreated',\n                'pan',\n                'panEnd',\n                'reset',\n                'shapeClick',\n                'shapeCreated',\n                'shapeFeatureCreated',\n                'shapeMouseEnter',\n                'shapeMouseLeave',\n                'zoomEnd',\n                'zoomStart'\n            ],\n            destroy: function () {\n                this.scroller.destroy();\n                if (this.navigator) {\n                    this.navigator.destroy();\n                }\n                if (this.attribution) {\n                    this.attribution.destroy();\n                }\n                if (this.zoomControl) {\n                    this.zoomControl.destroy();\n                }\n                this.markers.destroy();\n                for (var i = 0; i < this.layers.length; i++) {\n                    this.layers[i].destroy();\n                }\n                Widget.fn.destroy.call(this);\n            },\n            zoom: function (level) {\n                var options = this.options;\n                if (defined(level)) {\n                    level = math.round(limit(level, options.minZoom, options.maxZoom));\n                    if (options.zoom !== level) {\n                        options.zoom = level;\n                        this._reset();\n                    }\n                    return this;\n                } else {\n                    return options.zoom;\n                }\n            },\n            center: function (center) {\n                if (center) {\n                    this.options.center = Location.create(center).toArray();\n                    this._reset();\n                    return this;\n                } else {\n                    return Location.create(this.options.center);\n                }\n            },\n            extent: function (extent) {\n                if (extent) {\n                    this._setExtent(extent);\n                    return this;\n                } else {\n                    return this._getExtent();\n                }\n            },\n            setOptions: function (options) {\n                Widget.fn.setOptions.call(this, options);\n                this._reset();\n            },\n            locationToLayer: function (location, zoom) {\n                var clamp = !this.options.wraparound;\n                location = Location.create(location);\n                return this.crs.toPoint(location, this._layerSize(zoom), clamp);\n            },\n            layerToLocation: function (point, zoom) {\n                var clamp = !this.options.wraparound;\n                point = Point.create(point);\n                return this.crs.toLocation(point, this._layerSize(zoom), clamp);\n            },\n            locationToView: function (location) {\n                location = Location.create(location);\n                var origin = this.locationToLayer(this._viewOrigin);\n                var point = this.locationToLayer(location);\n                return point.translateWith(origin.scale(-1));\n            },\n            viewToLocation: function (point, zoom) {\n                var origin = this.locationToLayer(this._getOrigin(), zoom);\n                point = Point.create(point);\n                point = point.clone().translateWith(origin);\n                return this.layerToLocation(point, zoom);\n            },\n            eventOffset: function (e) {\n                var point;\n                var x;\n                var y;\n                var offset = this.element.offset();\n                if (e.x || e.y) {\n                    var field = 'location';\n                    x = e.x[field] - offset.left;\n                    y = e.y[field] - offset.top;\n                    point = new g.Point(x, y);\n                } else {\n                    var event = e.originalEvent || e;\n                    x = valueOrDefault(event.pageX, event.clientX) - offset.left;\n                    y = valueOrDefault(event.pageY, event.clientY) - offset.top;\n                    point = new g.Point(x, y);\n                }\n                return point;\n            },\n            eventToView: function (e) {\n                var cursor = this.eventOffset(e);\n                return this.locationToView(this.viewToLocation(cursor));\n            },\n            eventToLayer: function (e) {\n                return this.locationToLayer(this.eventToLocation(e));\n            },\n            eventToLocation: function (e) {\n                var cursor = this.eventOffset(e);\n                return this.viewToLocation(cursor);\n            },\n            viewSize: function () {\n                var element = this.element;\n                var scale = this._layerSize();\n                var width = element.width();\n                if (!this.options.wraparound) {\n                    width = min(scale, width);\n                }\n                return {\n                    width: width,\n                    height: min(scale, element.height())\n                };\n            },\n            exportVisual: function () {\n                this._reset();\n                return false;\n            },\n            _setOrigin: function (origin, zoom) {\n                var size = this.viewSize(), topLeft;\n                origin = this._origin = Location.create(origin);\n                topLeft = this.locationToLayer(origin, zoom);\n                topLeft.x += size.width / 2;\n                topLeft.y += size.height / 2;\n                this.options.center = this.layerToLocation(topLeft, zoom).toArray();\n                return this;\n            },\n            _getOrigin: function (invalidate) {\n                var size = this.viewSize(), topLeft;\n                if (invalidate || !this._origin) {\n                    topLeft = this.locationToLayer(this.center());\n                    topLeft.x -= size.width / 2;\n                    topLeft.y -= size.height / 2;\n                    this._origin = this.layerToLocation(topLeft);\n                }\n                return this._origin;\n            },\n            _setExtent: function (extent) {\n                var raw = Extent.create(extent);\n                var se = raw.se.clone();\n                if (this.options.wraparound && se.lng < 0 && extent.nw.lng > 0) {\n                    se.lng = 180 + (180 + se.lng);\n                }\n                extent = new Extent(raw.nw, se);\n                this.center(extent.center());\n                var width = this.element.width();\n                var height = this.element.height();\n                for (var zoom = this.options.maxZoom; zoom >= this.options.minZoom; zoom--) {\n                    var topLeft = this.locationToLayer(extent.nw, zoom);\n                    var bottomRight = this.locationToLayer(extent.se, zoom);\n                    var layerWidth = math.abs(bottomRight.x - topLeft.x);\n                    var layerHeight = math.abs(bottomRight.y - topLeft.y);\n                    if (layerWidth <= width && layerHeight <= height) {\n                        break;\n                    }\n                }\n                this.zoom(zoom);\n            },\n            _getExtent: function () {\n                var nw = this._getOrigin();\n                var bottomRight = this.locationToLayer(nw);\n                var size = this.viewSize();\n                bottomRight.x += size.width;\n                bottomRight.y += size.height;\n                var se = this.layerToLocation(bottomRight);\n                return new Extent(nw, se);\n            },\n            _zoomAround: function (pivot, level) {\n                this._setOrigin(this.layerToLocation(pivot, level), level);\n                this.zoom(level);\n            },\n            _initControls: function () {\n                var controls = this.options.controls;\n                if (ui.Attribution && controls.attribution) {\n                    this._createAttribution(controls.attribution);\n                }\n                if (!kendo.support.mobileOS) {\n                    if (ui.Navigator && controls.navigator) {\n                        this._createNavigator(controls.navigator);\n                    }\n                    if (ui.ZoomControl && controls.zoom) {\n                        this._createZoomControl(controls.zoom);\n                    }\n                }\n            },\n            _createControlElement: function (options, defaultPos) {\n                var pos = options.position || defaultPos;\n                var posSelector = '.' + renderPos(pos).replace(' ', '.');\n                var wrap = $('.k-map-controls' + posSelector, this.element);\n                if (wrap.length === 0) {\n                    wrap = $('<div>').addClass('k-map-controls ' + renderPos(pos)).appendTo(this.element);\n                }\n                return $('<div>').appendTo(wrap);\n            },\n            _createAttribution: function (options) {\n                var element = this._createControlElement(options, 'bottomRight');\n                this.attribution = new ui.Attribution(element, options);\n            },\n            _createNavigator: function (options) {\n                var element = this._createControlElement(options, 'topLeft');\n                var navigator = this.navigator = new ui.Navigator(element, options);\n                this._navigatorPan = proxy(this._navigatorPan, this);\n                navigator.bind('pan', this._navigatorPan);\n                this._navigatorCenter = proxy(this._navigatorCenter, this);\n                navigator.bind('center', this._navigatorCenter);\n            },\n            _navigatorPan: function (e) {\n                var map = this;\n                var scroller = map.scroller;\n                var x = scroller.scrollLeft + e.x;\n                var y = scroller.scrollTop - e.y;\n                var bounds = this._virtualSize;\n                var height = this.element.height();\n                var width = this.element.width();\n                x = limit(x, bounds.x.min, bounds.x.max - width);\n                y = limit(y, bounds.y.min, bounds.y.max - height);\n                map.scroller.one('scroll', function (e) {\n                    map._scrollEnd(e);\n                });\n                map.scroller.scrollTo(-x, -y);\n            },\n            _navigatorCenter: function () {\n                this.center(this.options.center);\n            },\n            _createZoomControl: function (options) {\n                var element = this._createControlElement(options, 'topLeft');\n                var zoomControl = this.zoomControl = new ui.ZoomControl(element, options);\n                this._zoomControlChange = proxy(this._zoomControlChange, this);\n                zoomControl.bind('change', this._zoomControlChange);\n            },\n            _zoomControlChange: function (e) {\n                if (!this.trigger('zoomStart', { originalEvent: e })) {\n                    this.zoom(this.zoom() + e.delta);\n                    this.trigger('zoomEnd', { originalEvent: e });\n                }\n            },\n            _initScroller: function () {\n                var friction = kendo.support.mobileOS ? FRICTION_MOBILE : FRICTION;\n                var zoomable = this.options.zoomable !== false;\n                var scroller = this.scroller = new kendo.mobile.ui.Scroller(this.element.children(0), {\n                    friction: friction,\n                    velocityMultiplier: VELOCITY_MULTIPLIER,\n                    zoom: zoomable,\n                    mousewheelScrolling: false,\n                    supportDoubleTap: true\n                });\n                scroller.bind('scroll', proxy(this._scroll, this));\n                scroller.bind('scrollEnd', proxy(this._scrollEnd, this));\n                scroller.userEvents.bind('gesturestart', proxy(this._scaleStart, this));\n                scroller.userEvents.bind('gestureend', proxy(this._scale, this));\n                scroller.userEvents.bind('doubleTap', proxy(this._doubleTap, this));\n                scroller.userEvents.bind('tap', proxy(this._tap, this));\n                this.scrollElement = scroller.scrollElement;\n            },\n            _initLayers: function () {\n                var defs = this.options.layers, layers = this.layers = [];\n                for (var i = 0; i < defs.length; i++) {\n                    var options = defs[i];\n                    var type = options.type || 'shape';\n                    var defaults = this.options.layerDefaults[type];\n                    var impl = dataviz.map.layers[type];\n                    layers.push(new impl(this, deepExtend({}, defaults, options)));\n                }\n            },\n            _initMarkers: function () {\n                this.markers = new map.layers.MarkerLayer(this, this.options.markerDefaults);\n                this.markers.add(this.options.markers);\n            },\n            _scroll: function (e) {\n                var origin = this.locationToLayer(this._viewOrigin).round();\n                var movable = e.sender.movable;\n                var offset = new g.Point(movable.x, movable.y).scale(-1).scale(1 / movable.scale);\n                origin.x += offset.x;\n                origin.y += offset.y;\n                this._scrollOffset = offset;\n                this._setOrigin(this.layerToLocation(origin));\n                this.trigger('pan', {\n                    originalEvent: e,\n                    origin: this._getOrigin(),\n                    center: this.center()\n                });\n            },\n            _scrollEnd: function (e) {\n                if (!this._scrollOffset || !this._panComplete()) {\n                    return;\n                }\n                this._scrollOffset = null;\n                this._panEndTS = new Date();\n                this.trigger('panEnd', {\n                    originalEvent: e,\n                    origin: this._getOrigin(),\n                    center: this.center()\n                });\n            },\n            _panComplete: function () {\n                return new Date() - (this._panEndTS || 0) > 50;\n            },\n            _scaleStart: function (e) {\n                if (this.trigger('zoomStart', { originalEvent: e })) {\n                    var touch = e.touches[1];\n                    if (touch) {\n                        touch.cancel();\n                    }\n                }\n            },\n            _scale: function (e) {\n                var scale = this.scroller.movable.scale;\n                var zoom = this._scaleToZoom(scale);\n                var gestureCenter = new g.Point(e.center.x, e.center.y);\n                var centerLocation = this.viewToLocation(gestureCenter, zoom);\n                var centerPoint = this.locationToLayer(centerLocation, zoom);\n                var originPoint = centerPoint.translate(-gestureCenter.x, -gestureCenter.y);\n                this._zoomAround(originPoint, zoom);\n                this.trigger('zoomEnd', { originalEvent: e });\n            },\n            _scaleToZoom: function (scaleDelta) {\n                var scale = this._layerSize() * scaleDelta;\n                var tiles = scale / this.options.minSize;\n                var zoom = math.log(tiles) / math.log(2);\n                return math.round(zoom);\n            },\n            _reset: function () {\n                if (this.attribution) {\n                    this.attribution.filter(this.center(), this.zoom());\n                }\n                this._viewOrigin = this._getOrigin(true);\n                this._resetScroller();\n                this.trigger('beforeReset');\n                this.trigger('reset');\n            },\n            _resetScroller: function () {\n                var scroller = this.scroller;\n                var x = scroller.dimensions.x;\n                var y = scroller.dimensions.y;\n                var scale = this._layerSize();\n                var nw = this.extent().nw;\n                var topLeft = this.locationToLayer(nw).round();\n                scroller.movable.round = true;\n                scroller.reset();\n                scroller.userEvents.cancel();\n                var zoom = this.zoom();\n                scroller.dimensions.forcedMinScale = pow(2, this.options.minZoom - zoom);\n                scroller.dimensions.maxScale = pow(2, this.options.maxZoom - zoom);\n                var xBounds = {\n                    min: -topLeft.x,\n                    max: scale - topLeft.x\n                };\n                var yBounds = {\n                    min: -topLeft.y,\n                    max: scale - topLeft.y\n                };\n                if (this.options.wraparound) {\n                    xBounds.max = 20 * scale;\n                    xBounds.min = -xBounds.max;\n                }\n                if (this.options.pannable === false) {\n                    var viewSize = this.viewSize();\n                    xBounds.min = yBounds.min = 0;\n                    xBounds.max = viewSize.width;\n                    yBounds.max = viewSize.height;\n                }\n                x.makeVirtual();\n                y.makeVirtual();\n                x.virtualSize(xBounds.min, xBounds.max);\n                y.virtualSize(yBounds.min, yBounds.max);\n                this._virtualSize = {\n                    x: xBounds,\n                    y: yBounds\n                };\n            },\n            _renderLayers: function () {\n                var defs = this.options.layers, layers = this.layers = [], scrollWrap = this.scrollWrap;\n                scrollWrap.empty();\n                for (var i = 0; i < defs.length; i++) {\n                    var options = defs[i];\n                    var type = options.type || 'shape';\n                    var defaults = this.options.layerDefaults[type];\n                    var impl = dataviz.map.layers[type];\n                    layers.push(new impl(this, deepExtend({}, defaults, options)));\n                }\n            },\n            _layerSize: function (zoom) {\n                zoom = valueOrDefault(zoom, this.options.zoom);\n                return this.options.minSize * pow(2, zoom);\n            },\n            _tap: function (e) {\n                if (!this._panComplete()) {\n                    return;\n                }\n                var cursor = this.eventOffset(e);\n                this.trigger('click', {\n                    originalEvent: e,\n                    location: this.viewToLocation(cursor)\n                });\n            },\n            _doubleTap: function (e) {\n                var options = this.options;\n                if (options.zoomable !== false) {\n                    if (!this.trigger('zoomStart', { originalEvent: e })) {\n                        var toZoom = this.zoom() + DEFAULT_ZOOM_RATE;\n                        var cursor = this.eventOffset(e);\n                        var location = this.viewToLocation(cursor);\n                        var postZoom = this.locationToLayer(location, toZoom);\n                        var origin = postZoom.translate(-cursor.x, -cursor.y);\n                        this._zoomAround(origin, toZoom);\n                        this.trigger('zoomEnd', { originalEvent: e });\n                    }\n                }\n            },\n            _mousewheel: function (e) {\n                e.preventDefault();\n                var delta = dataviz.mwDelta(e) > 0 ? -1 : 1;\n                var options = this.options;\n                var fromZoom = this.zoom();\n                var toZoom = limit(fromZoom + delta, options.minZoom, options.maxZoom);\n                if (options.zoomable !== false && toZoom !== fromZoom) {\n                    if (!this.trigger('zoomStart', { originalEvent: e })) {\n                        var cursor = this.eventOffset(e);\n                        var location = this.viewToLocation(cursor);\n                        var postZoom = this.locationToLayer(location, toZoom);\n                        var origin = postZoom.translate(-cursor.x, -cursor.y);\n                        this._zoomAround(origin, toZoom);\n                        this.trigger('zoomEnd', { originalEvent: e });\n                    }\n                }\n            }\n        });\n        dataviz.ui.plugin(Map);\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.dataviz.map', [\n        'kendo.data',\n        'kendo.userevents',\n        'kendo.tooltip',\n        'kendo.mobile.scroller',\n        'kendo.draganddrop',\n        'kendo.dataviz.core',\n        'dataviz/map/location',\n        'dataviz/map/attribution',\n        'dataviz/map/navigator',\n        'dataviz/map/zoom',\n        'dataviz/map/crs',\n        'dataviz/map/layers/base',\n        'dataviz/map/layers/shape',\n        'dataviz/map/layers/bubble',\n        'dataviz/map/layers/tile',\n        'dataviz/map/layers/bing',\n        'dataviz/map/layers/marker',\n        'dataviz/map/main'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dataviz.map',\n        name: 'Map',\n        category: 'dataviz',\n        description: 'The Kendo DataViz Map displays spatial data',\n        depends: [\n            'data',\n            'userevents',\n            'tooltip',\n            'dataviz.core',\n            'drawing',\n            'mobile.scroller'\n        ]\n    };\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}