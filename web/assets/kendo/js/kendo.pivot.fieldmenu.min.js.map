{"version": 3, "sources": ["kendo.pivot.fieldmenu.js"], "names": ["f", "define", "$", "undefined", "removeExpr", "expressions", "name", "idx", "length", "result", "field", "push", "findFilters", "filter", "member", "operator", "filterOperator", "filters", "checkNodes", "nodes", "values", "value", "split", "checked", "inArray", "uniqueName", "checkedNodeIds", "checkedNodes", "level", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "view", "kendo", "window", "ui", "MENU", "proxy", "NS", "Widget", "FILTER_ITEM", "ARIA_LABEL", "PivotFieldMenu", "extend", "init", "element", "options", "fn", "call", "this", "_dataSource", "_layout", "notify", "events", "filterable", "sortable", "messages", "info", "sortAscending", "sortDescending", "filterFields", "include", "title", "clear", "ok", "cancel", "operators", "contains", "doesnotcontain", "startswith", "endswith", "eq", "neq", "wrapper", "template", "MENUTEMPLATE", "ns", "menu", "target", "orientation", "showOn", "closeOnClick", "open", "_menuOpen", "select", "_select", "copyAnchorStyles", "data", "_createWindow", "_initFilterForm", "filterForm", "find", "filterProxy", "_filter", "_filterOperator", "DropDownList", "_filterValue", "_updateFilterAriaLabel", "on", "_reset", "_setFilterForm", "expression", "val", "_clearFilters", "dataSource", "splice", "indexOf", "_convert", "schema", "model", "fields", "currentMember", "type", "parseFloat", "parseJSON", "e", "that", "preventDefault", "close", "selected<PERSON><PERSON><PERSON>", "selectedOperatorName", "attr", "_sort", "dir", "sort", "setDataSource", "PivotDataSource", "create", "includeWindow", "WINDOWTEMPLATE", "_applyIncludes", "_closeWindow", "Window", "visible", "resizable", "_windowOpen", "resultExpression", "tree<PERSON>iew", "rootChecked", "existingExpression", "join", "_treeViewDataSource", "HierarchicalDataSource", "id", "item", "parseInt", "childrenCardinality", "transport", "read", "restrictions", "node", "get", "memberUniqueName", "replace", "treeOp", "levelUniqueName", "schemaMembers", "done", "success", "fail", "error", "_createTreeView", "TreeView", "autoBind", "dataTextField", "checkboxes", "check<PERSON><PERSON><PERSON><PERSON>", "dataBound", "progress", "event", "closest", "not", "kendoWindow", "hasClass", "center", "destroy", "LABELMENUTEMPLATE", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,yBACH,kBACA,aACA,eACA,iBACA,sBACDD,IACL,WAyXE,MA3WC,UAAUE,EAAGC,GAmTV,QAASC,GAAWC,EAAaC,GAAjC,GAEaC,GAASC,EADdC,IACJ,KAASF,EAAM,EAAGC,EAASH,EAAYG,OAAQD,EAAMC,EAAQD,IACrDF,EAAYE,GAAKG,QAAUJ,GAC3BG,EAAOE,KAAKN,EAAYE,GAGhC,OAAOE,GAEX,QAASG,GAAYC,EAAQC,EAAQC,GAArC,GAKQR,GACAE,EACAD,EACAQ,CAPJ,KAAKH,EACD,QAOJ,KALAA,EAASA,EAAOI,QACZV,EAAM,EACNE,KACAD,EAASK,EAAOL,OAEbD,EAAMC,EAAQD,IACjBS,EAAiBH,EAAON,GAAKQ,UACvBA,GAA+B,OAAnBC,IAA2BA,IAAmBD,GAAaF,EAAON,GAAKG,QAAUI,GAC/FL,EAAOE,KAAKE,EAAON,GAG3B,OAAOE,GAEX,QAASS,GAAWL,EAAQC,EAAQK,GAChC,GAAIC,GAAQb,EAAM,EAAGC,EAASW,EAAMX,MAEpC,IADAK,EAASD,EAAYC,EAAQC,EAAQ,MAAM,GAOvC,IADAM,EAASP,EAAOQ,MAAMC,MAAM,KACrBf,EAAMC,EAAQD,IACjBY,EAAMZ,GAAKgB,QAAUrB,EAAEsB,QAAQL,EAAMZ,GAAKkB,WAAYL,IAAW,MANrE,MAAOb,EAAMC,EAAQD,IACjBY,EAAMZ,GAAKgB,SAAU,EASjC,QAASG,GAAeP,EAAOQ,GAC3B,GAAIpB,GAAKC,EAASW,EAAMX,MACxB,KAAKD,EAAM,EAAGA,EAAMC,EAAQD,IACpBY,EAAMZ,GAAKgB,SAAkC,IAAvBJ,EAAMZ,GAAKqB,SACjCD,EAAahB,KAAKQ,EAAMZ,GAAKkB,YAE7BN,EAAMZ,GAAKsB,aACXH,EAAeP,EAAMZ,GAAKuB,SAASC,OAAQJ,GAlW1D,GACOK,GAAQC,OAAOD,MACfE,EAAKF,EAAME,GACXC,EAAO,mBACPC,EAAQlC,EAAEkC,MACVC,EAAK,uBACLC,EAASJ,EAAGI,OACZC,EAAc,gBACdC,EAAa,aACbC,EAAiBH,EAAOI,QACxBC,KAAM,SAAUC,EAASC,GACrBP,EAAOQ,GAAGH,KAAKI,KAAKC,KAAMJ,EAASC,GACnCG,KAAKC,cACLD,KAAKE,UACLlB,EAAMmB,OAAOH,OAEjBI,UACAP,SACIvC,KAAM,iBACNO,OAAQ,KACRwC,YAAY,EACZC,UAAU,EACVC,UACIC,KAAM,8BACNC,cAAe,iBACfC,eAAgB,kBAChBC,aAAc,gBACd9C,OAAQ,SACR+C,QAAS,oBACTC,MAAO,oBACPC,MAAO,QACPC,GAAI,KACJC,OAAQ,SACRC,WACIC,SAAU,WACVC,eAAgB,mBAChBC,WAAY,cACZC,SAAU,YACVC,GAAI,cACJC,IAAK,qBAIjBrB,QAAS,WACL,GAAIL,GAAUG,KAAKH,OACnBG,MAAKwB,QAAUtE,EAAE8B,EAAMyC,SAASC,IAC5BC,GAAI3C,EAAM2C,GACVtB,WAAYR,EAAQQ,WACpBC,SAAUT,EAAQS,SAClBC,SAAUV,EAAQU,YAEtBP,KAAK4B,KAAO5B,KAAKwB,QAAQrC,IACrBtB,OAAQgC,EAAQhC,OAChBgE,OAAQ7B,KAAKJ,QACbkC,YAAa,WACbC,OAAQ,QACRC,cAAc,EACdC,KAAM7C,EAAMY,KAAKkC,UAAWlC,MAC5BmC,OAAQ/C,EAAMY,KAAKoC,QAASpC,MAC5BqC,kBAAkB,IACnBC,KAAKnD,GACRa,KAAKuC,gBACD1C,EAAQQ,YACRL,KAAKwC,mBAGbA,gBAAiB,WAAA,GACTC,GAAazC,KAAK4B,KAAKhC,QAAQ8C,KAAK,IAAMnD,GAC1CoD,EAAcvD,EAAMY,KAAK4C,QAAS5C,KACtCA,MAAK6C,gBAAkB,GAAI7D,GAAME,GAAG4D,aAAaL,EAAWC,KAAK,WACjE1C,KAAK+C,aAAeN,EAAWC,KAAK,cACpC1C,KAAKgD,yBACLP,EAAWQ,GAAG,SAAW5D,EAAIsD,GAAaM,GAAG,QAAU5D,EAAI,mBAAoBsD,GAAaM,GAAG,QAAU5D,EAAI,kBAAmBD,EAAMY,KAAKkD,OAAQlD,QAEvJmD,eAAgB,SAAUC,GAAV,GACRpF,GAAiBgC,KAAK6C,gBACtB9E,EAAW,GACXM,EAAQ,EACR+E,KACArF,EAAWqF,EAAWrF,SACtBM,EAAQ+E,EAAW/E,OAEvBL,EAAeK,MAAMN,GAChBC,EAAeK,SAChBL,EAAemE,OAAO,GAE1BnC,KAAK+C,aAAaM,IAAIhF,IAE1BiF,cAAe,SAAUxF,GAAV,GAEPT,GAEAG,EAHAK,EAASmC,KAAKuD,WAAW1F,aAEzBN,EAAM,CAIV,KAFAM,EAAOI,QAAUJ,EAAOI,YACxBZ,EAAcO,EAAYC,EAAQC,GAC7BN,EAASH,EAAYG,OAAQD,EAAMC,EAAQD,IAC5CM,EAAOI,QAAQuF,OAAO3F,EAAOI,QAAQwF,QAAQpG,EAAYE,IAAO,EAEpE,OAAOM,IAEX6F,SAAU,SAAUrF,GAAV,GACFsF,GAAS3D,KAAKuD,WAAW1D,QAAQ8D,OACjCjG,IAAUiG,EAAOC,WAAaC,YAAc7D,KAAK8D,cAQrD,OAPIpG,KACmB,WAAfA,EAAMqG,KACN1F,EAAQ2F,WAAW3F,GACG,YAAfX,EAAMqG,OACb1F,IAAgBnB,EAAE+G,UAAU5F,KAG7BA,GAEXuE,QAAS,SAAUsB,GAAV,GAQDd,GAKAvF,EAZAsG,EAAOnE,KACP3B,EAAQ8F,EAAKT,SAASS,EAAKpB,aAAaM,MAE5C,OADAa,GAAEE,iBACY,KAAV/F,GACA8F,EAAKvC,KAAKyC,QACV,IAEAjB,GACA1F,MAAOyG,EAAKL,cACZ/F,SAAUoG,EAAKtB,gBAAgBxE,QAC/BA,MAAOA,GAEPR,EAASsG,EAAKb,cAAca,EAAKL,eACrCjG,EAAOI,QAAQN,KAAKyF,GACpBe,EAAKZ,WAAW1F,OAAOA,GACvBsG,EAAKvC,KAAKyC,QARNjB,IAURJ,uBAAwB,WAAA,GAChBP,GAAazC,KAAK4B,KAAKhC,QAAQ8C,KAAK,IAAMnD,GAC1C+E,EAAmBtE,KAAK6C,gBAAgBxE,QACxCkG,EAAuBvE,KAAKH,QAAQU,SAASU,UAAUqD,EAC3D7B,GAAWC,KAAK,UAAU8B,KAAKhF,EAAY+E,IAE/CrB,OAAQ,SAAUgB,GAAV,GACAC,GAAOnE,KACPnC,EAASsG,EAAKb,cAAca,EAAKL,cACrCI,GAAEE,iBACGvG,EAAOI,QAAQ,KAChBJ,MAEJsG,EAAKZ,WAAW1F,OAAOA,GACvBsG,EAAKhB,eAAe,MACpBgB,EAAKvC,KAAKyC,SAEdI,MAAO,SAAUC,GAAV,GACChH,GAAQsC,KAAK8D,cACbzG,EAAc2C,KAAKuD,WAAWoB,UAClCtH,GAAcD,EAAWC,EAAaK,GACtCL,EAAYM,MACRD,MAAOA,EACPgH,IAAKA,IAET1E,KAAKuD,WAAWoB,KAAKtH,GACrB2C,KAAK4B,KAAKyC,SAEdO,cAAe,SAAUrB,GACrBvD,KAAKH,QAAQ0D,WAAaA,EAC1BvD,KAAKC,eAETA,YAAa,WACTD,KAAKuD,WAAavE,EAAMsD,KAAKuC,gBAAgBC,OAAO9E,KAAKH,QAAQ0D,aAErEhB,cAAe,WACX,GAAIhC,GAAWP,KAAKH,QAAQU,QAC5BP,MAAK+E,cAAgB7H,EAAE8B,EAAMyC,SAASuD,IAAkBzE,SAAUA,KAAa0C,GAAG,QAAU5D,EAAI,eAAgBD,EAAMY,KAAKiF,eAAgBjF,OAAOiD,GAAG,QAAU5D,EAAI,mBAAoBD,EAAMY,KAAKkF,aAAclF,OAChNA,KAAK+E,cAAgB,GAAI7F,GAAGiG,OAAOnF,KAAK+E,eACpClE,MAAON,EAASM,MAChBuE,SAAS,EACTC,WAAW,EACXpD,KAAM7C,EAAMY,KAAKsF,YAAatF,SAGtCiF,eAAgB,SAAUf,GAAV,GAERqB,GADA5G,KAEAI,EAAOiB,KAAKwF,SAASjC,WAAWxE,OAChC0G,EAAc1G,EAAK,GAAGR,QACtBV,EAASmC,KAAKuD,WAAW1F,SACzB6H,EAAqB9H,EAAYC,EAAQmC,KAAK8D,cAAe,MAAM,EACvEpF,GAAeK,EAAMJ,GACjB+G,IACID,GACA5H,EAAOI,QAAQuF,OAAO3F,EAAOI,QAAQwF,QAAQiC,GAAqB,GAC7D7H,EAAOI,QAAQT,SAChBK,OAGJ6H,EAAmBrH,MAAQM,EAAagH,KAAK,KAEjDJ,EAAmB1H,GAEnBc,EAAanB,SACR+H,GAAqBE,IACtBF,GACI7H,MAAOsC,KAAK8D,cACZ/F,SAAU,KACVM,MAAOM,EAAagH,KAAK,MAEzB9H,IACAA,EAAOI,QAAQN,KAAK4H,GACpBA,EAAmB1H,KAI3B0H,GACAvF,KAAKuD,WAAW1F,OAAO0H,GAE3BvF,KAAKkF,aAAahB,IAEtBgB,aAAc,SAAUhB,GACpBA,EAAEE,iBACFpE,KAAK+E,cAAcV,SAEvBuB,oBAAqB,WACjB,GAAIzB,GAAOnE,IACX,OAAOhB,GAAMsD,KAAKuD,uBAAuBf,QACrCnB,QACIC,OACIkC,GAAI,aACJjH,YAAa,SAAUkH,GACnB,MAAOC,UAASD,EAAKE,oBAAqB,IAAM,KAI5DC,WACIC,KAAM,SAAUtG,GAAV,GACEuG,MACAC,EAAOlC,EAAKqB,SAASjC,WAAW+C,IAAIzG,EAAQyC,KAAK7D,YACjDnB,EAAOuC,EAAQyC,KAAK7D,UACnBnB,IAGD8I,EAAaG,iBAAmBF,EAAK5H,WAAW+H,QAAQ,MAAO,SAC/DJ,EAAaK,OAAS,GAHtBL,EAAaM,gBAAkBvC,EAAKL,cAAgB,WAKxDK,EAAKZ,WAAWoD,cAAcP,GAAcQ,KAAK,SAAUtE,GACvDpE,EAAWiG,EAAKZ,WAAW1F,SAAUsG,EAAKL,cAAexB,GACzDzC,EAAQgH,QAAQvE,KACjBwE,KAAKjH,EAAQkH,YAKhCC,gBAAiB,SAAUpH,GACvB,GAAIuE,GAAOnE,IACXmE,GAAKqB,SAAW,GAAItG,GAAG+H,SAASrH,GAC5BsH,UAAU,EACV3D,WAAYY,EAAKyB,sBACjBuB,cAAe,UACf1F,SAAU,2CACV2F,YAAcC,eAAe,GAC7BC,UAAW,WACPpI,EAAGqI,SAASpD,EAAKY,cAAcnF,SAAS,OAIpDsC,UAAW,SAAUgC,GACjB,GAAKA,EAAEsD,MAAP,CAGA,GAAIhD,GAAOxF,EAAMwF,KAAK,OACtBxE,MAAK8D,cAAgB5G,EAAEgH,EAAEsD,MAAM3F,QAAQ4F,QAAQ,IAAMjD,EAAO,KAAKA,KAAKA,GAClExE,KAAKH,QAAQQ,YACbL,KAAKmD,eAAevF,EAAYoC,KAAKuD,WAAW1F,SAAUmC,KAAK8D,eAAe,MAGtF1B,QAAS,SAAU8B,GACf,GAAI6B,GAAO7I,EAAEgH,EAAE6B,KACf7I,GAAE,0BAA0BwK,IAAI1H,KAAK+E,cAAcnF,SAAS+H,YAAY,SACpE5B,EAAK6B,SAAS,kBACd5H,KAAK+E,cAAc8C,SAAS5F,OACrB8D,EAAK6B,SAAS,cACrB5H,KAAKyE,MAAM,OACJsB,EAAK6B,SAAS,eACrB5H,KAAKyE,MAAM,QACJsB,EAAK6B,SAASrI,IACrBS,KAAKgD,0BAGbsC,YAAa,WACJtF,KAAKwF,UACNxF,KAAKgH,gBAAgBhH,KAAK+E,cAAcnF,QAAQ8C,KAAK,gBAEzDxD,EAAGqI,SAASvH,KAAK+E,cAAcnF,SAAS,GACxCI,KAAKwF,SAASjC,WAAW4C,QAE7B2B,QAAS,WACLxI,EAAOQ,GAAGgI,QAAQ/H,KAAKC,MACnBA,KAAK4B,OACL5B,KAAK4B,KAAKkG,UACV9H,KAAK4B,KAAO,MAEZ5B,KAAKwF,WACLxF,KAAKwF,SAASsC,UACd9H,KAAKwF,SAAW,MAEhBxF,KAAK+E,gBACL/E,KAAK+E,cAAc+C,UACnB9H,KAAK+E,cAAgB,MAEzB/E,KAAKwB,QAAU,KACfxB,KAAKJ,QAAU,QAsDnBmI,EAAoB,uTAAsWvI,EAAa,2MACvYkC,EAAe,yjBAA6rBnC,EAAc,wGAAwIwI,EAAoB,4BACt3B/C,EAAiB,4TACrB9F,GAAG8I,OAAOvI,IACZR,OAAOD,MAAMiJ,QACRhJ,OAAOD,OACE,kBAAV/B,SAAwBA,OAAOiL,IAAMjL,OAAS,SAAUkL,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.pivot.fieldmenu.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.pivot.fieldmenu', [\n        'kendo.pivotgrid',\n        'kendo.menu',\n        'kendo.window',\n        'kendo.treeview',\n        'kendo.dropdownlist'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'pivot.fieldmenu',\n        name: 'PivotFieldMenu',\n        category: 'web',\n        description: 'The PivotFieldMenu widget allows the user to filter on fields displayed in PivotGrid',\n        depends: [\n            'menu',\n            'window',\n            'treeview',\n            'dropdownlist'\n        ],\n        advanced: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo;\n        var ui = kendo.ui;\n        var MENU = 'kendoContextMenu';\n        var proxy = $.proxy;\n        var NS = '.kendoPivotFieldMenu';\n        var Widget = ui.Widget;\n        var FILTER_ITEM = 'k-filter-item';\n        var ARIA_LABEL = 'aria-label';\n        var PivotFieldMenu = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this._dataSource();\n                this._layout();\n                kendo.notify(this);\n            },\n            events: [],\n            options: {\n                name: 'PivotFieldMenu',\n                filter: null,\n                filterable: true,\n                sortable: true,\n                messages: {\n                    info: 'Show items with value that:',\n                    sortAscending: 'Sort Ascending',\n                    sortDescending: 'Sort Descending',\n                    filterFields: 'Fields Filter',\n                    filter: 'Filter',\n                    include: 'Include Fields...',\n                    title: 'Fields to include',\n                    clear: 'Clear',\n                    ok: 'OK',\n                    cancel: 'Cancel',\n                    operators: {\n                        contains: 'Contains',\n                        doesnotcontain: 'Does not contain',\n                        startswith: 'Starts with',\n                        endswith: 'Ends with',\n                        eq: 'Is equal to',\n                        neq: 'Is not equal to'\n                    }\n                }\n            },\n            _layout: function () {\n                var options = this.options;\n                this.wrapper = $(kendo.template(MENUTEMPLATE)({\n                    ns: kendo.ns,\n                    filterable: options.filterable,\n                    sortable: options.sortable,\n                    messages: options.messages\n                }));\n                this.menu = this.wrapper[MENU]({\n                    filter: options.filter,\n                    target: this.element,\n                    orientation: 'vertical',\n                    showOn: 'click',\n                    closeOnClick: false,\n                    open: proxy(this._menuOpen, this),\n                    select: proxy(this._select, this),\n                    copyAnchorStyles: false\n                }).data(MENU);\n                this._createWindow();\n                if (options.filterable) {\n                    this._initFilterForm();\n                }\n            },\n            _initFilterForm: function () {\n                var filterForm = this.menu.element.find('.' + FILTER_ITEM);\n                var filterProxy = proxy(this._filter, this);\n                this._filterOperator = new kendo.ui.DropDownList(filterForm.find('select'));\n                this._filterValue = filterForm.find('.k-textbox');\n                this._updateFilterAriaLabel();\n                filterForm.on('submit' + NS, filterProxy).on('click' + NS, '.k-button-filter', filterProxy).on('click' + NS, '.k-button-clear', proxy(this._reset, this));\n            },\n            _setFilterForm: function (expression) {\n                var filterOperator = this._filterOperator;\n                var operator = '';\n                var value = '';\n                if (expression) {\n                    operator = expression.operator;\n                    value = expression.value;\n                }\n                filterOperator.value(operator);\n                if (!filterOperator.value()) {\n                    filterOperator.select(0);\n                }\n                this._filterValue.val(value);\n            },\n            _clearFilters: function (member) {\n                var filter = this.dataSource.filter() || {};\n                var expressions;\n                var idx = 0;\n                var length;\n                filter.filters = filter.filters || [];\n                expressions = findFilters(filter, member);\n                for (length = expressions.length; idx < length; idx++) {\n                    filter.filters.splice(filter.filters.indexOf(expressions[idx]), 1);\n                }\n                return filter;\n            },\n            _convert: function (value) {\n                var schema = this.dataSource.options.schema;\n                var field = ((schema.model || {}).fields || {})[this.currentMember];\n                if (field) {\n                    if (field.type === 'number') {\n                        value = parseFloat(value);\n                    } else if (field.type === 'boolean') {\n                        value = Boolean($.parseJSON(value));\n                    }\n                }\n                return value;\n            },\n            _filter: function (e) {\n                var that = this;\n                var value = that._convert(that._filterValue.val());\n                e.preventDefault();\n                if (value === '') {\n                    that.menu.close();\n                    return;\n                }\n                var expression = {\n                    field: that.currentMember,\n                    operator: that._filterOperator.value(),\n                    value: value\n                };\n                var filter = that._clearFilters(that.currentMember);\n                filter.filters.push(expression);\n                that.dataSource.filter(filter);\n                that.menu.close();\n            },\n            _updateFilterAriaLabel: function () {\n                var filterForm = this.menu.element.find('.' + FILTER_ITEM);\n                var selectedOperator = this._filterOperator.value();\n                var selectedOperatorName = this.options.messages.operators[selectedOperator];\n                filterForm.find('select').attr(ARIA_LABEL, selectedOperatorName);\n            },\n            _reset: function (e) {\n                var that = this;\n                var filter = that._clearFilters(that.currentMember);\n                e.preventDefault();\n                if (!filter.filters[0]) {\n                    filter = {};\n                }\n                that.dataSource.filter(filter);\n                that._setFilterForm(null);\n                that.menu.close();\n            },\n            _sort: function (dir) {\n                var field = this.currentMember;\n                var expressions = this.dataSource.sort() || [];\n                expressions = removeExpr(expressions, field);\n                expressions.push({\n                    field: field,\n                    dir: dir\n                });\n                this.dataSource.sort(expressions);\n                this.menu.close();\n            },\n            setDataSource: function (dataSource) {\n                this.options.dataSource = dataSource;\n                this._dataSource();\n            },\n            _dataSource: function () {\n                this.dataSource = kendo.data.PivotDataSource.create(this.options.dataSource);\n            },\n            _createWindow: function () {\n                var messages = this.options.messages;\n                this.includeWindow = $(kendo.template(WINDOWTEMPLATE)({ messages: messages })).on('click' + NS, '.k-button-ok', proxy(this._applyIncludes, this)).on('click' + NS, '.k-button-cancel', proxy(this._closeWindow, this));\n                this.includeWindow = new ui.Window(this.includeWindow, {\n                    title: messages.title,\n                    visible: false,\n                    resizable: false,\n                    open: proxy(this._windowOpen, this)\n                });\n            },\n            _applyIncludes: function (e) {\n                var checkedNodes = [];\n                var resultExpression;\n                var view = this.treeView.dataSource.view();\n                var rootChecked = view[0].checked;\n                var filter = this.dataSource.filter();\n                var existingExpression = findFilters(filter, this.currentMember, 'in')[0];\n                checkedNodeIds(view, checkedNodes);\n                if (existingExpression) {\n                    if (rootChecked) {\n                        filter.filters.splice(filter.filters.indexOf(existingExpression), 1);\n                        if (!filter.filters.length) {\n                            filter = {};\n                        }\n                    } else {\n                        existingExpression.value = checkedNodes.join(',');\n                    }\n                    resultExpression = filter;\n                }\n                if (checkedNodes.length) {\n                    if (!resultExpression && !rootChecked) {\n                        resultExpression = {\n                            field: this.currentMember,\n                            operator: 'in',\n                            value: checkedNodes.join(',')\n                        };\n                        if (filter) {\n                            filter.filters.push(resultExpression);\n                            resultExpression = filter;\n                        }\n                    }\n                }\n                if (resultExpression) {\n                    this.dataSource.filter(resultExpression);\n                }\n                this._closeWindow(e);\n            },\n            _closeWindow: function (e) {\n                e.preventDefault();\n                this.includeWindow.close();\n            },\n            _treeViewDataSource: function () {\n                var that = this;\n                return kendo.data.HierarchicalDataSource.create({\n                    schema: {\n                        model: {\n                            id: 'uniqueName',\n                            hasChildren: function (item) {\n                                return parseInt(item.childrenCardinality, 10) > 0;\n                            }\n                        }\n                    },\n                    transport: {\n                        read: function (options) {\n                            var restrictions = {};\n                            var node = that.treeView.dataSource.get(options.data.uniqueName);\n                            var name = options.data.uniqueName;\n                            if (!name) {\n                                restrictions.levelUniqueName = that.currentMember + '.[(ALL)]';\n                            } else {\n                                restrictions.memberUniqueName = node.uniqueName.replace(/\\&/g, '&amp;');\n                                restrictions.treeOp = 1;\n                            }\n                            that.dataSource.schemaMembers(restrictions).done(function (data) {\n                                checkNodes(that.dataSource.filter(), that.currentMember, data);\n                                options.success(data);\n                            }).fail(options.error);\n                        }\n                    }\n                });\n            },\n            _createTreeView: function (element) {\n                var that = this;\n                that.treeView = new ui.TreeView(element, {\n                    autoBind: false,\n                    dataSource: that._treeViewDataSource(),\n                    dataTextField: 'caption',\n                    template: '#: data.item.caption || data.item.name #',\n                    checkboxes: { checkChildren: true },\n                    dataBound: function () {\n                        ui.progress(that.includeWindow.element, false);\n                    }\n                });\n            },\n            _menuOpen: function (e) {\n                if (!e.event) {\n                    return;\n                }\n                var attr = kendo.attr('name');\n                this.currentMember = $(e.event.target).closest('[' + attr + ']').attr(attr);\n                if (this.options.filterable) {\n                    this._setFilterForm(findFilters(this.dataSource.filter(), this.currentMember)[0]);\n                }\n            },\n            _select: function (e) {\n                var item = $(e.item);\n                $('.k-pivot-filter-window').not(this.includeWindow.element).kendoWindow('close');\n                if (item.hasClass('k-include-item')) {\n                    this.includeWindow.center().open();\n                } else if (item.hasClass('k-sort-asc')) {\n                    this._sort('asc');\n                } else if (item.hasClass('k-sort-desc')) {\n                    this._sort('desc');\n                } else if (item.hasClass(FILTER_ITEM)) {\n                    this._updateFilterAriaLabel();\n                }\n            },\n            _windowOpen: function () {\n                if (!this.treeView) {\n                    this._createTreeView(this.includeWindow.element.find('.k-treeview'));\n                }\n                ui.progress(this.includeWindow.element, true);\n                this.treeView.dataSource.read();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                if (this.menu) {\n                    this.menu.destroy();\n                    this.menu = null;\n                }\n                if (this.treeView) {\n                    this.treeView.destroy();\n                    this.treeView = null;\n                }\n                if (this.includeWindow) {\n                    this.includeWindow.destroy();\n                    this.includeWindow = null;\n                }\n                this.wrapper = null;\n                this.element = null;\n            }\n        });\n        function removeExpr(expressions, name) {\n            var result = [];\n            for (var idx = 0, length = expressions.length; idx < length; idx++) {\n                if (expressions[idx].field !== name) {\n                    result.push(expressions[idx]);\n                }\n            }\n            return result;\n        }\n        function findFilters(filter, member, operator) {\n            if (!filter) {\n                return [];\n            }\n            filter = filter.filters;\n            var idx = 0;\n            var result = [];\n            var length = filter.length;\n            var filterOperator;\n            for (; idx < length; idx++) {\n                filterOperator = filter[idx].operator;\n                if ((!operator && filterOperator !== 'in' || filterOperator === operator) && filter[idx].field === member) {\n                    result.push(filter[idx]);\n                }\n            }\n            return result;\n        }\n        function checkNodes(filter, member, nodes) {\n            var values, idx = 0, length = nodes.length;\n            filter = findFilters(filter, member, 'in')[0];\n            if (!filter) {\n                for (; idx < length; idx++) {\n                    nodes[idx].checked = true;\n                }\n            } else {\n                values = filter.value.split(',');\n                for (; idx < length; idx++) {\n                    nodes[idx].checked = $.inArray(nodes[idx].uniqueName, values) >= 0;\n                }\n            }\n        }\n        function checkedNodeIds(nodes, checkedNodes) {\n            var idx, length = nodes.length;\n            for (idx = 0; idx < length; idx++) {\n                if (nodes[idx].checked && nodes[idx].level() !== 0) {\n                    checkedNodes.push(nodes[idx].uniqueName);\n                }\n                if (nodes[idx].hasChildren) {\n                    checkedNodeIds(nodes[idx].children.view(), checkedNodes);\n                }\n            }\n        }\n        var LABELMENUTEMPLATE = '<div class=\"k-filterable k-content\" tabindex=\"-1\" data-role=\"fieldmenu\">' + '<form class=\"k-filter-menu\">' + '<div>' + '<div class=\"k-filter-help-text\">#=messages.info#</div>' + '<select>' + '#for(var op in messages.operators){#' + '<option value=\"#=op#\">#=messages.operators[op]#</option>' + '#}#' + '</select>' + '<input class=\"k-textbox\" type=\"text\" ' + ARIA_LABEL + '=\"#=messages.filter#\" />' + '<div>' + '<a class=\"k-button k-primary k-button-filter\" href=\"\\\\#\">#=messages.filter#</a>' + '<a class=\"k-button k-button-clear\" href=\"\\\\#\">#=messages.clear#</a>' + '</div>' + '</div>' + '</form>' + '</div>';\n        var MENUTEMPLATE = '<ul class=\"k-pivot-fieldmenu\">' + '# if (sortable) {#' + '<li class=\"k-item k-sort-asc\">' + '<span class=\"k-link\">' + '<span class=\"k-icon k-i-sort-asc-sm\"></span>' + '${messages.sortAscending}' + '</span>' + '</li>' + '<li class=\"k-item k-sort-desc\">' + '<span class=\"k-link\">' + '<span class=\"k-icon k-i-sort-desc-sm\"></span>' + '${messages.sortDescending}' + '</span>' + '</li>' + '# if (filterable) {#' + '<li class=\"k-separator\"></li>' + '# } #' + '# } #' + '# if (filterable) {#' + '<li class=\"k-item k-include-item\">' + '<span class=\"k-link\">' + '<span class=\"k-icon k-i-filter\"></span>' + '${messages.include}' + '</span>' + '</li>' + '<li class=\"k-separator\"></li>' + '<li class=\"k-item ' + FILTER_ITEM + '\">' + '<span class=\"k-link\">' + '<span class=\"k-icon k-i-filter\"></span>' + '${messages.filterFields}' + '</span>' + '<ul>' + '<li>' + LABELMENUTEMPLATE + '</li>' + '</ul>' + '</li>' + '# } #' + '</ul>';\n        var WINDOWTEMPLATE = '<div class=\"k-popup-edit-form k-pivot-filter-window\"><div class=\"k-edit-form-container\">' + '<div class=\"k-treeview\"></div>' + '<div class=\"k-edit-buttons k-state-default\">' + '<a class=\"k-button k-primary k-button-ok\" href=\"\\\\#\">' + '${messages.ok}' + '</a>' + '<a class=\"k-button k-button-cancel\" href=\"\\\\#\">' + '${messages.cancel}' + '</a>' + '</div></div>';\n        ui.plugin(PivotFieldMenu);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}