/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("util/text-metrics.min",["kendo.core.min"],t)}(function(){!function(t){function e(t){return(t+"").replace(h,s)}function i(t){var e,i=[];for(e in t)i.push(e+t[e]);return i.sort().join("")}function n(t){var e,i=2166136261;for(e=0;e<t.length;++e)i+=(i<<1)+(i<<4)+(i<<7)+(i<<8)+(i<<24),i^=t.charCodeAt(e);return i>>>0}function o(){return{width:0,height:0,baseline:0}}function r(t,e,i){return u.current.measure(t,e,i)}var a,h,s,l,d,u;window.kendo.util=window.kendo.util||{},a=kendo.Class.extend({init:function(t){this._size=t,this._length=0,this._map={}},put:function(t,e){var i=this._map,n={key:t,value:e};i[t]=n,this._head?(this._tail.newer=n,n.older=this._tail,this._tail=n):this._head=this._tail=n,this._length>=this._size?(i[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(t){var e=this._map[t];if(e)return e===this._head&&e!==this._tail&&(this._head=e.newer,this._head.older=null),e!==this._tail&&(e.older&&(e.older.newer=e.newer,e.newer.older=e.older),e.older=this._tail,e.newer=null,this._tail.newer=e,this._tail=e),e.value}}),h=/\r?\n|\r|\t/g,s=" ",l={baselineMarkerSize:1},"undefined"!=typeof document&&(d=document.createElement("div"),d.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),u=kendo.Class.extend({init:function(e){this._cache=new a(1e3),this.options=t.extend({},l,e)},measure:function(t,r,a){var h,s,l,u,c,f,p,m,g;if(void 0===a&&(a={}),!t)return o();if(h=i(r),s=n(t+h),l=this._cache.get(s))return l;u=o(),c=a.box||d,f=this._baselineMarker().cloneNode(!1);for(p in r)m=r[p],void 0!==m&&(c.style[p]=m);return g=a.normalizeText!==!1?e(t):t+"",c.textContent=g,c.appendChild(f),document.body.appendChild(c),g.length&&(u.width=c.offsetWidth-this.options.baselineMarkerSize,u.height=c.offsetHeight,u.baseline=f.offsetTop+this.options.baselineMarkerSize),u.width>0&&u.height>0&&this._cache.put(s,u),c.parentNode.removeChild(c),u},_baselineMarker:function(){var t=document.createElement("div");return t.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",t}}),u.current=new u,kendo.deepExtend(kendo.util,{LRUCache:a,TextMetrics:u,measureText:r,objectKey:i,hashKey:n,normalizeText:e})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("kendo.dataviz.treemap.min",["kendo.data.min","kendo.userevents.min","kendo.dataviz.themes.min"],t)}(function(){return function(t,e){function i(t,e){if(null===e)return e;var i=b(t,!0);return i(e)}function n(t){return typeof t!==R}function o(t,e,i){var n,o,s=h(t),l=h(e),d=u(t)-u(e)<0,c=[];for(c.push(t),n=0;n<i;n++)o={r:r(s.r,l.r,n,i,d),g:r(s.g,l.g,n,i,d),b:r(s.b,l.b,n,i,d)},c.push(a(o));return c.push(e),c}function r(t,e,i,n,o){var r,a=f.min(f.abs(t),f.abs(e)),h=f.max(f.abs(t),f.abs(e)),s=(h-a)/(n+1),l=s*(i+1);return r=o?a+l:h-l}function a(t){return"#"+s(t.r)+s(t.g)+s(t.b)}function h(t){t=t.replace("#","");var e=l(t);return{r:d(e.r),g:d(e.g),b:d(e.b)}}function s(t){var e=f.round(t).toString(16).toUpperCase();return 1===e.length&&(e="0"+e),e}function l(t){var e=t.length,i={};return 3===e?(i.r=t[0],i.g=t[1],i.b=t[2]):(i.r=t.substring(0,2),i.g=t.substring(2,4),i.b=t.substring(4,6)),i}function d(t){return parseInt(t.toString(16),16)}function u(t){var e=0;return t&&(t=h(t),e=f.sqrt(.241*t.r*t.r+.691*t.g*t.g+.068*t.b*t.b)),e}function c(t){var e=f.pow(10,4);return f.round(t*e)/e}var f=Math,p=t.proxy,m=t.isArray,g=window.kendo,v=g._outerHeight,_=g._outerWidth,w=g.Class,y=g.ui.Widget,k=g.template,x=g.deepExtend,C=g.data.HierarchicalDataSource,b=g.getter,z=g.dataviz,S=".kendoTreeMap",I="change",T="dataBound",H="itemCreated",M=Number.MAX_VALUE,A="mouseover"+S,B="mouseleave"+S,R="undefined",D=y.extend({init:function(e,i){g.destroy(e),t(e).empty(),y.fn.init.call(this,e,i),this.wrapper=this.element,this._initTheme(this.options),this.element.addClass("k-widget k-treemap"),this._setLayout(),this._originalOptions=x({},this.options),this._initDataSource(),this._attachEvents(),g.notify(this,z.ui)},options:{name:"TreeMap",theme:"default",autoBind:!0,textField:"text",valueField:"value",colorField:"color"},events:[T,H],_initTheme:function(t){var e=this,i=z.ui.themes||{},n=((t||{}).theme||"").toLowerCase(),o=(i[n]||{}).treeMap;e.options=x({},o,t)},_attachEvents:function(){this.element.on(A,p(this._mouseover,this)).on(B,p(this._mouseleave,this)),this._resizeHandler=p(this.resize,this,!1),g.onResize(this._resizeHandler)},_setLayout:function(){"horizontal"===this.options.type?(this._layout=new U((!1)),this._view=new E(this,this.options)):"vertical"===this.options.type?(this._layout=new U((!0)),this._view=new E(this,this.options)):(this._layout=new F,this._view=new L(this,this.options))},_initDataSource:function(){var t=this,e=t.options,i=e.dataSource;t._dataChangeHandler=p(t._onDataChange,t),t.dataSource=C.create(i).bind(I,t._dataChangeHandler),i&&t.options.autoBind&&t.dataSource.fetch()},setDataSource:function(t){var e=this;e.dataSource.unbind(I,e._dataChangeHandler),e.dataSource=t.bind(I,e._dataChangeHandler),t&&e.options.autoBind&&e.dataSource.fetch()},_onDataChange:function(t){var e,i,n,o,r=t.node,a=t.items,h=this.options;if(r){if(a.length){for(n=this._getByUid(r.uid),n.children=[],a=new g.data.Query(a)._sortForGrouping(h.valueField,"desc"),i=0;i<a.length;i++)e=a[i],n.children.push(this._wrapItem(e));o=this._view.htmlSize(n),this._layout.compute(n.children,n.coord,o),this._setColors(n.children),this._view.render(n)}}else this._cleanItems(),this.element.empty(),e=this._wrapItem(a[0]),this._layout.createRoot(e,_(this.element),v(this.element),"vertical"===this.options.type),this._view.createRoot(e),this._root=e,this._colorIdx=0;for(i=0;i<a.length;i++)a[i].load();r&&this.trigger(T,{node:r})},_cleanItems:function(){var t=this;t.angular("cleanup",function(){return{elements:t.element.find(".k-leaf div,.k-treemap-title,.k-treemap-title-vertical")}})},_setColors:function(t){var e,i,r,a,h=this.options.colors,s=this._colorIdx,l=h[s%h.length];for(m(l)&&(e=o(l[0],l[1],t.length)),r=!1,a=0;a<t.length;a++)i=t[a],n(i.color)||(i.color=e?e[a]:l),i.dataItem.hasChildren||(r=!0);r&&this._colorIdx++},_contentSize:function(t){this.view.renderHeight(t)},_wrapItem:function(t){var e={};return n(this.options.valueField)&&(e.value=i(this.options.valueField,t)),n(this.options.colorField)&&(e.color=i(this.options.colorField,t)),n(this.options.textField)&&(e.text=i(this.options.textField,t)),e.level=t.level(),e.dataItem=t,e},_getByUid:function(t){for(var e,i=[this._root];i.length;){if(e=i.pop(),e.dataItem.uid===t)return e;e.children&&(i=i.concat(e.children))}},dataItem:function(e){var i=t(e).attr(g.attr("uid")),n=this.dataSource;return n&&n.getByUid(i)},findByUid:function(t){return this.element.find(".k-treemap-tile["+g.attr("uid")+"='"+t+"']")},_mouseover:function(e){var i=t(e.target);i.hasClass("k-leaf")&&(this._removeActiveState(),i.removeClass("k-state-hover").addClass("k-state-hover"))},_removeActiveState:function(){this.element.find(".k-state-hover").removeClass("k-state-hover")},_mouseleave:function(){this._removeActiveState()},destroy:function(){y.fn.destroy.call(this),this.element.off(S),this.dataSource&&this.dataSource.unbind(I,this._dataChangeHandler),this._root=null,g.unbindResize(this._resizeHandler),g.destroy(this.element)},items:function(){return t()},getSize:function(){return g.dimensions(this.element)},_resize:function(){var t,e,i=this._root;i&&(t=this.element,e=t.children(),i.coord.width=_(t),i.coord.height=v(t),e.css({width:i.coord.width,height:i.coord.height}),this._resizeItems(i,e))},_resizeItems:function(t,e){var i,n,o,r;if(t.children&&t.children.length)for(i=e.children(".k-treemap-wrap").children(),this._layout.compute(t.children,t.coord,{text:this._view.titleSize(t,e)}),r=0;r<t.children.length;r++)n=t.children[r],o=i.filter("["+g.attr("uid")+"='"+n.dataItem.uid+"']"),this._view.setItemSize(n,o),this._resizeItems(n,o)},setOptions:function(t){var i=t.dataSource;t.dataSource=e,this._originalOptions=x(this._originalOptions,t),this.options=x({},this._originalOptions),this._setLayout(),this._initTheme(this.options),y.fn._setEvents.call(this,t),i&&this.setDataSource(C.create(i)),this.options.autoBind&&this.dataSource.fetch()}}),F=w.extend({createRoot:function(t,e,i){t.coord={width:e,height:i,top:0,left:0}},leaf:function(t){return!t.children},layoutChildren:function(t,e){var i,n,o,r,a=e.width*e.height,h=0,s=[];for(i=0;i<t.length;i++)s[i]=parseFloat(t[i].value),h+=s[i];for(i=0;i<s.length;i++)t[i].area=a*s[i]/h;n=this.layoutHorizontal()?e.height:e.width,o=[t[0]],r=t.slice(1),this.squarify(r,o,n,e)},squarify:function(t,e,i,n){this.computeDim(t,e,i,n)},computeDim:function(t,i,n,o){var r,a,h;return t.length+i.length==1?(r=1==t.length?t:i,this.layoutLast(r,n,o),e):(t.length>=2&&0===i.length&&(i=[t[0]],t=t.slice(1)),0===t.length?(i.length>0&&this.layoutRow(i,n,o),e):(a=t[0],this.worstAspectRatio(i,n)>=this.worstAspectRatio([a].concat(i),n)?this.computeDim(t.slice(1),i.concat([a]),n,o):(h=this.layoutRow(i,n,o),this.computeDim(t,[],h.dim,h)),e))},layoutLast:function(t,e,i){t[0].coord=i},layoutRow:function(t,e,i){return this.layoutHorizontal()?this.layoutV(t,e,i):this.layoutH(t,e,i)},orientation:"h",layoutVertical:function(){return"v"===this.orientation},layoutHorizontal:function(){return"h"===this.orientation},layoutChange:function(){this.orientation=this.layoutVertical()?"h":"v"},worstAspectRatio:function(t,e){var i,n,o,r,a;if(!t||0===t.length)return M;for(i=0,n=0,o=M,r=0;r<t.length;r++)a=t[r].area,i+=a,o=o<a?o:a,n=n>a?n:a;return f.max(e*e*n/(i*i),i*i/(e*e*o))},compute:function(t,e,i){if(e.width>=e.height&&this.layoutHorizontal()||this.layoutChange(),t&&t.length>0){var n={width:e.width,height:e.height-i.text,top:0,left:0};this.layoutChildren(t,n)}},layoutV:function(t,e,i){var n,o,r,a=this._totalArea(t),h=0;for(e=c(a/e),n=0;n<t.length;n++)o=c(t[n].area/e),t[n].coord={height:o,width:e,top:i.top+h,left:i.left},h+=o;return r={height:i.height,width:i.width-e,top:i.top,left:i.left+e},r.dim=f.min(r.width,r.height),r.dim!=r.height&&this.layoutChange(),r},layoutH:function(t,e,i){var n,o,r=this._totalArea(t),a=c(r/e),h=i.top,s=0;for(n=0;n<t.length;n++)t[n].coord={height:a,width:c(t[n].area/a),top:h,left:i.left+s},s+=t[n].coord.width;return o={height:i.height-a,width:i.width,top:i.top+a,left:i.left},o.dim=f.min(o.width,o.height),o.dim!=o.width&&this.layoutChange(),o},_totalArea:function(t){var e,i=0;for(e=0;e<t.length;e++)i+=t[e].area;return i}}),L=w.extend({init:function(e,i){this.options=x({},this.options,i),this.treeMap=e,this.element=t(e.element),this.offset=0},titleSize:function(t,e){var i=e.children(".k-treemap-title");return i.height()||0},htmlSize:function(t){var e,i,n=this._getByUid(t.dataItem.uid),o={text:0};return t.children&&(this._clean(n),e=this._getText(t),e&&(i=this._createTitle(t),n.append(i),this._compile(i,t.dataItem),o.text=i.height()),n.append(this._createWrap()),this.offset=(_(n)-n.innerWidth())/2),o},_compile:function(t,e){this.treeMap.angular("compile",function(){return{elements:t,data:[{dataItem:e}]}})},_getByUid:function(t){return this.element.find(".k-treemap-tile["+g.attr("uid")+"='"+t+"']")},render:function(t){var e,i,n,o,r=this._getByUid(t.dataItem.uid),a=t.children;if(a)for(e=r.find(".k-treemap-wrap"),i=0;i<a.length;i++)n=a[i],o=this._createLeaf(n),e.append(o),this._compile(o.children(),n.dataItem),this.treeMap.trigger(H,{element:o})},createRoot:function(t){var e=this._createLeaf(t);this.element.append(e),this._compile(e.children(),t.dataItem),this.treeMap.trigger(H,{element:e})},_clean:function(t){this.treeMap.angular("cleanup",function(){return{elements:t.children(":not(.k-treemap-wrap)")}}),t.css("background-color",""),t.removeClass("k-leaf"),t.removeClass("k-inverse"),t.empty()},_createLeaf:function(e){return this._createTile(e).css("background-color",e.color).addClass("k-leaf").toggleClass("k-inverse",this._tileColorBrightness(e)>180).toggle(0!==e.value).append(t("<div></div>").html(this._getText(e)))},_createTile:function(e){var i=t("<div class='k-treemap-tile'></div>");return this.setItemSize(e,i),n(e.dataItem)&&n(e.dataItem.uid)&&i.attr(g.attr("uid"),e.dataItem.uid),i},_itemCoordinates:function(t){var e={width:t.coord.width,height:t.coord.height,left:t.coord.left,top:t.coord.top};return e.width+=e.left&&this.offset?2*this.offset:this.offset,e.height+=e.top?2*this.offset:this.offset,e},setItemSize:function(t,e){var i=this._itemCoordinates(t);e.css({width:i.width,height:i.height,left:i.left,top:i.top})},_getText:function(t){var e=t.text;return this.options.template&&(e=this._renderTemplate(t)),e},_renderTemplate:function(t){var e=k(this.options.template);return e({dataItem:t.dataItem,text:t.text})},_createTitle:function(e){return t("<div class='k-treemap-title'></div>").append(t("<div></div>").html(this._getText(e)))},_createWrap:function(){return t("<div class='k-treemap-wrap'></div>")},_tileColorBrightness:function(t){return u(t.color)}}),U=w.extend({createRoot:function(t,e,i,n){t.coord={width:e,height:i,top:0,left:0},t.vertical=n},init:function(t){this.vertical=t,this.quotient=t?1:0},compute:function(t,e,i){var n,o,r;t.length>0&&(n=e.width,o=e.height,this.vertical?o-=i.text:n-=i.text,r={width:n,height:o,top:0,left:0},this.layoutChildren(t,r))},layoutChildren:function(t,e){var i,n,o=e.width*e.height,r=0,a=[];for(i=0;i<t.length;i++)n=t[i],a[i]=parseFloat(t[i].value),r+=a[i],n.vertical=this.vertical;for(i=0;i<a.length;i++)t[i].area=o*a[i]/r;this.sliceAndDice(t,e)},sliceAndDice:function(t,e){var i=this._totalArea(t);t[0].level%2===this.quotient?this.layoutHorizontal(t,e,i):this.layoutVertical(t,e,i)},layoutHorizontal:function(t,e,i){var n,o,r,a=0;for(n=0;n<t.length;n++)o=t[n],r=o.area/(i/e.width),o.coord={height:e.height,width:r,top:e.top,left:e.left+a},a+=r},layoutVertical:function(t,e,i){var n,o,r,a=0;for(n=0;n<t.length;n++)o=t[n],r=o.area/(i/e.height),o.coord={height:r,width:e.width,top:e.top+a,left:e.left},a+=r},_totalArea:function(t){var e,i=0;for(e=0;e<t.length;e++)i+=t[e].area;return i}}),E=L.extend({htmlSize:function(t){var e,i,n=this._getByUid(t.dataItem.uid),o={text:0,offset:0};return t.children&&(this._clean(n),e=this._getText(t),e&&(i=this._createTitle(t),n.append(i),this._compile(i,t.dataItem),o.text=t.vertical?i.height():i.width()),n.append(this._createWrap()),this.offset=(_(n)-n.innerWidth())/2),o},titleSize:function(t,e){var i;return i=t.vertical?e.children(".k-treemap-title").height():e.children(".k-treemap-title-vertical").width(),i||0},_createTitle:function(e){var i;return i=t(e.vertical?"<div class='k-treemap-title'></div>":"<div class='k-treemap-title-vertical'></div>"),i.append(t("<div></div>").html(this._getText(e)))}});z.ui.plugin(D)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()});
//# sourceMappingURL=kendo.dataviz.treemap.min.js.map
