/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.view.min",["kendo.core.min","kendo.binder.min","kendo.fx.min"],e)}(function(){return function(e,t){function n(e){if(!e)return{};var t=e.match(R)||[];return{type:t[1],direction:t[3],reverse:"reverse"===t[5]}}var i=window.kendo,r=i.attr,o=i.ui,a=i.attrValue,s=i.directiveSelector,d=i.Observable,c=i.ui.Widget,h=i.roleSelector,l="SCRIPT",f="init",w="transitionStart",u="transitionEnd",p="show",v="hide",g="attach",m="detach",_=/unrecognized expression/,y=/<body[^>]*>(([\u000a\u000d\u2028\u2029]|.)*)<\/body>/i,V="loadStart",C="loadComplete",S="showStart",b="sameViewRequested",x="viewShow",E="viewTypeDetermined",T="after",I={content:"k-content",view:"k-view",stretchedView:"k-stretched-view",widget:"k-widget",header:"k-header",footer:"k-footer"},O=i.ui.Widget.extend({init:function(e,t){var n=this;t=t||{},n.id=i.guid(),d.fn.init.call(n),n._initOptions(t),n.content=e,n.options.renderOnInit&&c.fn.init.call(n,n._createElement(),t),n.options.wrapInSections&&n._renderSections(),n.tagName=t.tagName||"div",n.model=t.model,n._wrap=t.wrap!==!1,this._evalTemplate=t.evalTemplate||!1,n._fragments={},n.bind([f,p,v,w,u],t)},options:{name:"View",renderOnInit:!1,wrapInSections:!1,detachOnHide:!0,detachOnDestroy:!0},render:function(t){var n=this,r=!n.element;return r&&(n.element=n._createElement()),t&&e(t).append(n.element),r&&(i.bind(n.element,n.model),n.trigger(f)),t&&(n._eachFragment(g),n.trigger(p)),n.element},clone:function(){return new k(this)},triggerBeforeShow:function(){return!0},triggerBeforeHide:function(){return!0},showStart:function(){var e=this,t=e.render();t&&t.css("display",""),this.trigger(p,{view:this})},showEnd:function(){},hideEnd:function(){this.hide()},beforeTransition:function(e){this.trigger(w,{type:e})},afterTransition:function(e){this.trigger(u,{type:e})},hide:function(){this.options.detachOnHide&&(this._eachFragment(m),e(this.element).detach()),this.trigger(v)},destroy:function(){var e=this,t=e.element;t&&(c.fn.destroy.call(e),i.unbind(t),i.destroy(t),e.options.detachOnDestroy&&t.remove())},purge:function(){var t=this;t.destroy(),e(t.element).add(t.content).add(t.wrapper).off().remove()},fragments:function(t){e.extend(this._fragments,t)},_eachFragment:function(e){for(var t in this._fragments)this._fragments[t][e](this,t)},_createElement:function(){var t,n,r,o=this,a="<"+o.tagName+" />";try{n=e(document.getElementById(o.content)||o.content),n[0].tagName===l&&(n=n.html())}catch(s){_.test(s.message)&&(n=o.content)}return"string"==typeof n?(n=n.replace(/^\s+|\s+$/g,""),o._evalTemplate&&(n=i.template(n)(o.model||{})),t=e(a).append(n),o._wrap||(t=t.contents())):(t=n,o._evalTemplate&&(r=e(i.template(e("<div />").append(t.clone(!0)).html())(o.model||{})),e.contains(document,t[0])&&t.replaceWith(r),t=r),o._wrap&&(t=t.wrapAll(a).parent())),t},_renderSections:function(){var e=this;e.options.wrapInSections&&(e._wrapper(),e._createContent(),e._createHeader(),e._createFooter())},_wrapper:function(){var e,t=this,n=t.content;t.wrapper=n.is(h("view"))?t.content:n.wrap("<div data-"+i.ns+'stretch="true" data-'+i.ns+'role="view" data-'+i.ns+'init-widgets="false"></div>').parent(),e=t.wrapper,e.attr("id",t.id),e.addClass(I.view),e.addClass(I.widget),e.attr("role","view")},_createContent:function(){var t,n=this,i=e(n.wrapper),o=h("content");i.children(o)[0]||(t=i.children().filter(function(){var t=e(this);if(!t.is(h("header"))&&!t.is(h("footer")))return t}),t.wrap("<div "+r("role")+'="content"></div>')),this.contentElement=i.children(h("content")),this.contentElement.addClass(I.stretchedView).addClass(I.content)},_createHeader:function(){var e=this,t=e.wrapper;this.header=t.children(h("header")).addClass(I.header)},_createFooter:function(){var e=this,t=e.wrapper;this.footer=t.children(h("footer")).addClass(I.footer)}}),k=i.Class.extend({init:function(t){e.extend(this,{element:t.element.clone(!0),transition:t.transition,id:t.id}),t.element.parent().append(this.element)},hideEnd:function(){this.element.remove()},beforeTransition:e.noop,afterTransition:e.noop}),H=O.extend({init:function(e,t){O.fn.init.call(this,e,t),this.containers={}},container:function(e){var t=this.containers[e];return t||(t=this._createContainer(e),this.containers[e]=t),t},showIn:function(e,t,n){this.container(e).show(t,n)},_createContainer:function(e){var t,n=this.render(),i=n.find(e);if(!i.length&&n.is(e)){if(!n.is(e))throw Error("can't find a container with the specified "+e+" selector");i=n}return t=new F(i),t.bind("accepted",function(e){e.view.render(i)}),t}}),B=O.extend({attach:function(e,t){e.element.find(t).replaceWith(this.render())},detach:function(){}}),R=/^(\w+)(:(\w+))?( (\w+))?$/,F=d.extend({init:function(e){d.fn.init.call(this),this.container=e,this.history=[],this.view=null,this.running=!1},after:function(){this.running=!1,this.trigger("complete",{view:this.view}),this.trigger("after")},end:function(){this.view.showEnd(),this.previous.hideEnd(),this.after()},show:function(e,t,r){if(!e.triggerBeforeShow()||this.view&&!this.view.triggerBeforeHide())return this.trigger("after"),!1;r=r||e.id;var o=this,a=e===o.view?e.clone():o.view,s=o.history,d=s[s.length-2]||{},c=d.id===r,h=t||(c?s[s.length-1].transition:e.transition),l=n(h);return o.running&&o.effect.stop(),"none"===h&&(h=null),o.trigger("accepted",{view:e}),o.view=e,o.previous=a,o.running=!0,c?s.pop():s.push({id:r,transition:h}),a?(h&&i.effects.enabled?(e.element.addClass("k-fx-hidden"),e.showStart(),c&&!t&&(l.reverse=!l.reverse),o.effect=i.fx(e.element).replace(a.element,l.type).beforeTransition(function(){e.beforeTransition("show"),a.beforeTransition("hide")}).afterTransition(function(){e.afterTransition("show"),a.afterTransition("hide")}).direction(l.direction).setReverse(l.reverse),o.effect.run().then(function(){o.end()})):(e.showStart(),o.end()),!0):(e.showStart(),e.showEnd(),o.after(),!0)},destroy:function(){var e=this,t=e.view;t&&t.destroy&&t.destroy()}}),L=d.extend({init:function(t){var n,r,o=this;d.fn.init.call(o),o.options=t,e.extend(o,t),o.sandbox=e("<div />"),r=o.container,n=o._hideViews(r),o.rootView=n.first(),o.layouts={},o.viewContainer=new i.ViewContainer(o.container),o.viewContainer.bind("accepted",function(e){e.view.params=o.params}),o.viewContainer.bind("complete",function(e){o.trigger(x,{view:e.view})}),o.viewContainer.bind(T,function(){o.trigger(T)}),this.bind(this.events,t)},events:[S,T,x,V,C,b,E],destroy:function(){var e,t=this,n=t.viewContainer;i.destroy(t.container);for(e in t.layouts)this.layouts[e].destroy();n&&n.destroy()},view:function(){return this.viewContainer.view},showView:function(e,t,n){if(e=e.replace(RegExp("^"+this.remoteViewURLPrefix),""),""===e&&this.remoteViewURLPrefix&&(e="/"),e.replace(/^#/,"")===this.url)return this.trigger(b),!1;this.trigger(S);var r=this,o=r._findViewElement(e),a=i.widgetInstance(o);return r.url=e.replace(/^#/,""),r.params=n,a&&a.reload&&(a.purge(),o=[]),this.trigger(E,{remote:0===o.length,url:e}),!o[0]||(a||(a=r._createView(o)),r.viewContainer.show(a,t,e))},append:function(e,t){var n,i,o=this.sandbox,a=(t||"").split("?")[0],s=this.container;return y.test(e)&&(e=RegExp.$1),o[0].innerHTML=e,s.append(o.children("script, style")),n=this._hideViews(o),i=n.first(),i.length||(n=i=o.wrapInner("<div data-role=view />").children()),a&&i.hide().attr(r("url"),a),s.append(n),this._createView(i)},_locate:function(e){return this.$angular?s(e):h(e)},_findViewElement:function(e){var t,n=e.split("?")[0];return n?(t=this.container.children("["+r("url")+"='"+n+"']"),t[0]||n.indexOf("/")!==-1||(t=this.container.children("#"===n.charAt(0)?n:"#"+n)),t[0]||(t=this._findViewElementById(e)),t):this.rootView},_findViewElementById:function(e){var t=this.container.children("[id='"+e+"']");return t},_createView:function(e){return this._createSpaView(e)},_createMobileView:function(e){return i.initWidget(e,{defaultTransition:this.transition,loader:this.loader,container:this.container,getLayout:this.getLayoutProxy,modelScope:this.modelScope,reload:a(e,"reload")},o.roles)},_createSpaView:function(e){var t=(this.options||{}).viewOptions||{};return new i.View(e,{renderOnInit:t.renderOnInit,wrap:t.wrap||!1,wrapInSections:t.wrapInSections,detachOnHide:t.detachOnHide,detachOnDestroy:t.detachOnDestroy})},_hideViews:function(e){return e.children(this._locate("view")).hide()}});i.ViewEngine=L,i.ViewContainer=F,i.Fragment=B,i.Layout=H,i.View=O,i.ViewClone=k}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.view.min.js.map
