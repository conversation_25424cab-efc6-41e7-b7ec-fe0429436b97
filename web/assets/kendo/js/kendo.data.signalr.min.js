/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.data.signalr.min",["kendo.data.min"],t)}(function(){return function(t){function n(t){return t&&r(t.done)&&r(t.fail)}function e(t){return t&&r(t.then)&&r(t["catch"])}var o=window.kendo,r=o.isFunction,i=o.data.RemoteTransport.extend({init:function(t){var r,i=t&&t.signalr?t.signalr:{},s=i.promise;if(!s)throw Error('The "promise" option must be set.');if(!n(s)&&!e(s))throw Error('The "promise" option must be a Promise.');if(this.promise=s,r=i.hub,!r)throw Error('The "hub" option must be set.');if("function"!=typeof r.on||"function"!=typeof r.invoke)throw Error('The "hub" option is not a valid SignalR hub proxy.');this.hub=r,o.data.RemoteTransport.fn.init.call(this,t)},push:function(t){var n=this.options.signalr.client||{};n.create&&this.hub.on(n.create,t.pushCreate),n.update&&this.hub.on(n.update,t.pushUpdate),n.destroy&&this.hub.on(n.destroy,t.pushDestroy)},_crud:function(r,i){var s,a,u=this.hub,h=this.promise,d=this.options.signalr.server;if(!d||!d[i])throw Error(o.format('The "server.{0}" option must be set.',i));s=[d[i]],a=this.parameterMap(r.data,i),t.isEmptyObject(a)||s.push(a),n(h)?h.done(function(){u.invoke.apply(u,s).done(r.success).fail(r.error)}):e(h)&&h.then(function(){u.invoke.apply(u,s).then(r.success)["catch"](r.error)})},read:function(t){this._crud(t,"read")},create:function(t){this._crud(t,"create")},update:function(t){this._crud(t,"update")},destroy:function(t){this._crud(t,"destroy")}});t.extend(!0,o.data,{transports:{signalr:i}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,n,e){(e||n)()});
//# sourceMappingURL=kendo.data.signalr.min.js.map
