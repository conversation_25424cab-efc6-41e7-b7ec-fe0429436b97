{"version": 3, "sources": ["kendo.pdfviewer.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "undefined", "Page", "NS", "ui", "proxy", "drawing", "Widget", "progress", "SCROLL", "RENDER", "OPEN", "ERROR", "FOCUS", "WHITECOLOR", "TABINDEX", "PROCESSORS", "pdfjs", "dpl", "styles", "viewer", "scroller", "PDFViewer", "element", "that", "fn", "call", "_wrapper", "toolbar", "_renderToolbar", "wrapper", "on", "_focus", "_initProcessor", "_renderPageContainer", "_loadDocument", "_tabindex", "notify", "events", "name", "view", "type", "pdfjsProcessing", "file", "dplProcessing", "read", "url", "dataType", "pageField", "upload", "saveField", "download", "loadOnDemand", "items", "page", "defaultPageSize", "messages", "defaultFileName", "open", "exportAs", "pager", "first", "previous", "next", "last", "of", "pages", "errorMessages", "notSupported", "parseError", "notFound", "dialogs", "exportAsDialog", "title", "pdf", "png", "svg", "labels", "fileName", "saveAsType", "okText", "save", "cancel", "addClass", "_resizeHandler", "onResize", "resize", "e", "focus", "pageContainer", "preventDefault", "processingOptions", "processingLib", "processor", "pdfviewer", "toolbarOptions", "resizable", "action", "execute", "bind", "toolbarElement", "appendTo", "<PERSON><PERSON><PERSON>", "_initErrorDialog", "dialogInstance", "_errorDialog", "ErrorDialog", "_dialog", "attr", "append", "_triggerError", "dialog", "trigger", "content", "message", "_renderPages", "pagesData", "viewerPage", "pageData", "total", "_renderBlankPage", "number", "_scroll", "_blankPage", "_updatePager", "pageNumber", "setOptions", "_resize", "ratio", "loadedPagesHeight", "containerWidth", "clientWidth", "containerHeight", "clientHeight", "_visiblePagesCount", "for<PERSON>ach", "previousPage", "prevPageTop", "prevPageHeight", "containerScrollHeight", "scrollHeight", "containerScrollTop", "scrollTop", "containerOffsetTop", "offset", "top", "pageNum", "pageIndex", "pageToLoad", "currentPage", "currentPageTop", "currentPageHeight", "scrollDirection", "_prevScrollTop", "_preventScroll", "Math", "abs", "floor", "_loadVisiblePages", "commandOptions", "command", "exec", "fetchDocument", "done", "_clearPages", "activatePage", "loadPage", "load", "currentScrollTop", "position", "pagesCount", "minVisiblePageNum", "maxVisiblePageNum", "min", "fromFile", "_updateDocument", "exportImage", "background", "rootGroup", "Group", "Path", "fromRect", "geometry", "Rect", "fill", "color", "stroke", "group", "data", "saveAs", "dataURI", "proxyURL", "forceProxy", "proxyTarget", "exportSVG", "destroy", "unbindResize", "_saveDialog", "_upload", "unbind", "off", "empty", "plugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,mBACH,wCACA,sCACA,qBACA,kBACA,qBACA,uBACDD,IACL,WA8ZE,MAjZC,UAAUE,EAAGkF,GAAb,GACiIC,GAA1HC,EAAK,kBAAmBlD,EAAQD,OAAOC,MAAOmD,EAAKnD,EAAMmD,GAAIC,EAAQtF,EAAEsF,MAAOjD,EAASrC,EAAEqC,OAAQkD,EAAUrD,EAAMqD,QAAeC,EAASH,EAAGG,OAAQC,EAAWvD,EAAMmD,GAAGI,SAAUC,EAAS,SAAUC,EAAS,SAAUC,EAAO,OAAQC,EAAQ,QAASC,EAAQ,QAASC,EAAa,UAAWC,EAAW,WAAYC,GACpTC,MAAO,QACPC,IAAK,OACNC,GACCC,OAAQ,wBACRC,SAAU,4BAEdC,EAAYf,EAAOnD,QACnBC,KAAM,SAAUkE,EAAS/C,GACrB,GAAIgD,GAAOjE,IACXgD,GAAOkB,GAAGpE,KAAKqE,KAAKF,EAAMD,EAAStE,EAAM0C,cAAepC,KAAKiB,QAASA,IACtEgD,EAAKG,WACDH,EAAKhD,QAAQoD,SACbJ,EAAKK,iBAETL,EAAKM,QAAQC,GAAGlB,EAAOR,EAAMmB,EAAKQ,OAAQR,IAC1CA,EAAKS,eAAezD,OACpBgD,EAAKU,uBACLV,EAAKW,gBACLX,EAAKY,YACLnF,EAAMoF,OAAOb,EAAMvE,EAAMmD,KAE7BkC,QACI5B,EACAC,EACAC,GAEJpC,SACI+D,KAAM,YACNC,MAAQC,KAAM,UACdC,iBAAmBC,KAAM,MACzBC,eACIC,MACIC,IAAK,KACLL,KAAM,MACNM,SAAU,OACVC,UAAW,cAEfC,QACIH,IAAK,KACLI,UAAW,QAEfC,UAAYL,IAAK,MACjBM,cAAc,GAElBxB,SAAWyB,UACXjH,MAAO,IACPC,OAAQ,KACRiH,KAAM,EACNC,iBACInH,MAAO,IACPC,OAAQ,MAEZmH,UACIC,gBAAiB,WACjB7B,SACI8B,KAAM,OACNC,SAAU,SACVR,SAAU,WACVS,OACIC,MAAO,uBACPC,SAAU,0BACVC,KAAM,sBACNC,KAAM,sBACNC,GAAI,WACJX,KAAM,OACNY,MAAO,UAGfC,eACIC,aAAc,0BACdC,WAAY,6BACZC,SAAU,sBAEdC,SACIC,gBACIC,MAAO,YACPhB,gBAAiB,WACjBiB,IAAK,kCACLC,IAAK,mCACLC,IAAK,kCACLC,QACIC,SAAU,YACVC,WAAY,UACZzB,KAAM,SAGd0B,OAAQ,KACRC,KAAM,OACNC,OAAQ,YAIpBvD,SAAU,WACN,GAAIH,GAAOjE,KAAMiB,EAAUgD,EAAKhD,OAChCgD,GAAKM,QAAUN,EAAKD,QACpBC,EAAKM,QAAQ1F,MAAMoC,EAAQpC,OAAOC,OAAOmC,EAAQnC,QAAQ8I,SAAShE,EAAOC,QACzEI,EAAK4D,eAAiBnI,EAAMoI,SAAS,WACjC7D,EAAK8D,YAGbtD,OAAQ,SAAUuD,GACVhI,KAAKqE,QACLrE,KAAKqE,QAAQE,QAAQ0D,QAErBjI,KAAKkI,cAAcD,QAEvBD,EAAEG,kBAENzD,eAAgB,SAAUzD,GACtB,GAAiBmH,GAAbnE,EAAOjE,IACXoI,GAAoBnH,EAAQoE,cAAgBpB,EAAKhD,QAAQoE,cAAgBpB,EAAKhD,QAAQkE,gBACtFlB,EAAKoE,cAAgBpH,EAAQoE,cAAgB5B,EAAWE,IAAMF,EAAWC,MACzEO,EAAKqE,UAAY,GAAI5I,GAAM6I,UAAUtE,EAAKoE,eAAeC,UAAUF,EAAmBnE,GACtFtB,EAAOjD,EAAM6I,UAAUtE,EAAKoE,eAAe1F,MAE/C2B,eAAgB,WAAA,GACRL,GAAOjE,KAAMiB,EAAUgD,EAAKhD,QAC5BuH,GACAnC,OAASJ,SAAUhF,EAAQgF,SAAS5B,QAAQgC,OAC5CoC,WAAW,EACX3C,MAAO7E,EAAQoD,QAAQyB,MACvBjH,MAAOoC,EAAQpC,MACf6J,OAAQzE,EAAK0E,QAAQC,KAAK3E,GAC1BgC,SAAUhF,EAAQgF,SAAS5B,SAE3BwE,EAAiBrL,EAAE,UACvBqL,GAAeC,SAAS7E,EAAKD,SAC7BC,EAAKI,QAAU,GAAI3E,GAAM6I,UAAUQ,QAAQF,EAAgBL,IAE/DQ,iBAAkB,SAAU/H,GAAV,GAINgI,GAHJhF,EAAOjE,IAMX,OALKiE,GAAKiF,eACNjI,EAAUpB,EAAOoB,GAAWgF,SAAUhC,EAAKhD,QAAQgF,WAC/CgD,EAAiB,GAAIvJ,GAAM6I,UAAUvB,QAAQmC,YAAYlI,GAC7DgD,EAAKiF,aAAeD,EAAeG,SAEhCnF,EAAKiF,cAEhBvE,qBAAsB,WAClB,GAAIV,GAAOjE,IACNiE,GAAKiE,gBACNjE,EAAKiE,cAAgB1K,EAAE,WACvByG,EAAKiE,cAAcN,SAAShE,EAAOE,UACnCG,EAAKiE,cAAcmB,KAAK7F,EAAU,GAClCS,EAAKM,QAAQ+E,OAAOrF,EAAKiE,iBAGjCqB,cAAe,SAAUtI,GACrB,GAAIuI,GAASxJ,KAAKgJ,kBAClBnJ,GAAOoB,GAAWuI,OAAQA,IACtBxJ,KAAKkI,eACLjF,EAASjD,KAAKkI,eAAe,GAE7BlI,KAAKyJ,QAAQpG,EAAOpC,IAGxBuI,EAAOrD,OAAOuD,QAAQzI,EAAQ0I,UAElCC,aAAc,WAAA,GACiCC,GAOlCrL,EACDsL,EAAYC,EARhB9F,EAAOjE,KAAMc,EAAWmD,EAAKnD,QAEjC,IADAmD,EAAK0C,UACA7F,IAAaA,EAASkJ,MAEvB,MADA/F,GAAKgG,mBACL,CAGJ,KADAJ,EAAY/I,EAAS6F,MACZnI,EAAI,EAAGA,GAAKsC,EAASkJ,MAAOxL,IACjBuL,GACRzB,UAAWrE,EAAKqE,UAChB4B,OAAQ1L,GAEZqL,GAAaA,EAAUnL,SACvBqL,EAAWlK,EAAOkK,EAAUF,EAAUrL,EAAI,KAE9CsL,EAAa,GAAInH,GAAKoH,EAAU9F,GAChCA,EAAK0C,MAAMxI,KAAK2L,GAChB7F,EAAKiE,cAAcoB,OAAOQ,EAAW9F,QAErCC,GAAK0C,MAAMjI,OAAS,GACpBuF,EAAKiE,cAAc1D,GAAGtB,EAASN,EAAIE,EAAMmB,EAAKkG,QAASlG,KAG/DgG,iBAAkB,WACdjK,KAAKoK,WAAa,GAAIzH,GAAK3C,KAAKiB,QAAQ+E,gBAAiBhG,MACzDA,KAAKkI,cAAcoB,OAAOtJ,KAAKoK,WAAWpG,SAC1ChE,KAAKqK,aAAa,EAAG,IAEzBA,aAAc,SAAUC,EAAYN,GAC3BhK,KAAKqE,SAAYrE,KAAKqE,QAAQgC,OAGnCrG,KAAKqE,QAAQgC,MAAMkE,YACfxE,KAAMuE,EACNN,MAAOA,KAGfQ,QAAS,WAAA,GAKDC,GAJAxG,EAAOjE,KAGP0K,EAAoB,EAFpBC,EAIa1G,EAAKiE,cAAc,GAAG0C,YAHnCC,EAIc5G,EAAKiE,cAAc,GAAG4C,YACxC,OAAK7G,GAAK0C,OAAU1C,EAAK0C,MAAMjI,QAO3BuF,EAAKI,SACLJ,EAAKI,QAAQ0D,QAAO,GAExB9D,EAAK8G,mBAAqB,EAC1B9G,EAAK0C,MAAMqE,QAAQ,SAAUjF,GACzB0E,EAAQE,EAAiB5E,EAAK/B,QAAQnF,QACtCkH,EAAKgC,OAAO0C,GACZC,GAAqB3E,EAAK/B,QAAQlF,SAC9B4L,EAAoBG,GAAmB9E,EAAKuE,WAAa,GACzDrG,EAAK8G,uBATb,IANQ9G,EAAKmG,aACLK,EAAQE,EAAiB1G,EAAKmG,WAAWpG,QAAQnF,QACjDoF,EAAKmG,WAAWrC,OAAO0C,IAE3B,IAeRN,QAAS,WACL,GAA6ec,GAAcC,EAAaC,EAApgBlH,EAAOjE,KAAMoL,EAAwBnH,EAAKiE,cAAc,GAAGmD,aAAcR,EAAkB5G,EAAKiE,cAAcpJ,SAAUwM,EAAqBrH,EAAKiE,cAAcqD,YAAaC,EAAqBvH,EAAKiE,cAAcuD,SAASC,IAAKC,EAAU1H,EAAKhD,QAAQ8E,KAAM6F,EAAYD,EAAU,EAAG3B,EAAQ/F,EAAK0C,MAAMjI,OAAQmN,EAAaF,EAASG,EAAc7H,EAAK0C,MAAMiF,GAAYG,EAAiBD,EAAY9H,QAAQyH,SAASC,IAAMF,EAAoBQ,EAAoBF,EAAY9H,QAAQlF,SAAqDmN,EAAkBX,EAAqBrH,EAAKiI,eAAiB,EAAI,IACzlB,OAAIjI,GAAKkI,gBACLlI,EAAKkI,gBAAiB,EACtB,IAEAF,OAAyBhI,EAAK0C,MAAMiF,EAAYK,KAChDhB,EAAehH,EAAK0C,MAAMiF,EAAY3H,EAAK8G,qBAAuB9G,EAAK0C,MAAMiF,EAAYK,GACzFf,EAAcD,EAAajH,QAAQyH,SAASC,IAAMF,EAClDL,EAAiBF,EAAajH,QAAQlF,UAEtCsN,KAAKC,IAAIf,GAAsBrH,EAAKiI,gBAAkB,IAAMrB,EAC5DgB,EAAaO,KAAKE,MAAMhB,GAAsB,GAAKF,EAAwBpB,KAAW,EAC/E+B,EAAiB,GAAKK,KAAKC,IAAIN,IAAmBC,EAAoB,GAAyB,IAApBC,EAClFJ,IACOZ,GAAgBmB,KAAKC,IAAInB,IAAgBC,EAAiB,GACjEU,IAEAF,IAAYE,GAAcA,GAAc,GAAKA,GAAc7B,IAC3D/F,EAAKhD,QAAQ8E,KAAO8F,EACpB5H,EAAKsI,oBACLtI,EAAKoG,aAAawB,EAAY7B,IAElC/F,EAAKiI,eAAiBZ,EAjBtB,IAmBJ3C,QAAS,SAAU1H,GAAV,GACDuL,GAAiB3M,GAASgE,OAAQ7D,MAAQiB,EAAQA,SAClDwL,EAAU,GAAI/M,GAAM6I,UAAUtH,EAAQwL,SAASD,EACnDC,GAAQC,QAEZ9H,cAAe,WAAA,GACPX,GAAOjE,KACP+F,EAAO9B,EAAKhD,QAAQ8E,IACxB9C,GAASgB,EAAKiE,eAAe,GAC7BjE,EAAKqE,UAAUqE,gBAAgBC,KAAK,SAAU9L,GAC1CmD,EAAK4I,cACL5I,EAAKnD,SAAWA,EAChBmD,EAAK2F,eACL3F,EAAK8D,QAAO,GACRjH,IACAiF,EAAOA,GAAQ,GAAKA,GAAQjF,EAASkJ,MAAQjE,EAAO,EACpD9B,EAAK6I,aAAa/G,IAEtB9C,EAASgB,EAAKiE,eAAe,MAGrC6E,SAAU,SAAU7C,GAChB,GAAInE,GAAO/F,KAAK2G,OAAS3G,KAAK2G,MAAMuD,EAAS,EACzCnE,IACAA,EAAKiH,QAGbF,aAAc,SAAU5C,GAAV,GACNnE,GAAO/F,KAAK2G,OAAS3G,KAAK2G,MAAMuD,EAAS,GACzC+C,EAAmBjN,KAAKkI,cAAcqD,WACrCxF,KAGL/F,KAAKiB,QAAQ8E,KAAOmE,EACpBlK,KAAKuM,oBACLvM,KAAKmM,gBAAiB,EACtBnM,KAAKkI,cAAcqD,UAAU0B,EAAmBlH,EAAK/B,QAAQkJ,WAAWxB,KACxE1L,KAAKqK,aAAaH,EAAQlK,KAAK2G,MAAMjI,UAEzC6N,kBAAmB,WAAA,GAIN/N,GAHL2O,EAAanN,KAAK2G,OAAS3G,KAAK2G,MAAMjI,OACtC0O,EAAoBpN,KAAKiB,QAAQ8E,KACjCsH,EAAoBjB,KAAKkB,IAAIF,EAAoBpN,KAAK+K,mBAAoBoC,EAC9E,KAAS3O,EAAI4O,EAAmB5O,GAAK6O,EAAmB7O,IACpDwB,KAAK+M,SAASvO,IAGtB+O,SAAU,SAAUnI,GAChBpF,KAAKsI,UAAUkF,gBAAgBpI,GAC/BpF,KAAK4E,iBAET6I,YAAa,SAAUxM,GAAV,GAMLyM,GALAzJ,EAAOjE,KACPsK,EAAarJ,EAAQ8E,KACrBA,EAAO9B,EAAK0C,MAAM2D,EAAa,IAAMrG,EAAKmG,WAC1CuD,EAAY,GAAI5K,GAAQ6K,KAC5B7H,GAAKiH,OACDU,EAAahO,EAAMqD,QAAQ8K,KAAKC,SAAS,GAAIpO,GAAMqO,SAASC,MAC5D,EACA,IAEAjI,EAAKlH,MACLkH,EAAKjH,UAELmP,MAAQC,MAAO3K,GACf4K,OAAQ,OAEZlL,EAASgB,EAAKiE,eAAe,GAC7ByF,EAAUrE,OAAOoE,EAAY3H,EAAKqI,OAClCrL,EAAQ0K,YAAYE,GAAWf,KAAK,SAAUyB,GAC1CpL,EAASgB,EAAKiE,eAAe,GAC7BxI,EAAM4O,QACFC,QAASF,EACT9G,SAAUtG,EAAQsG,SAClBiH,SAAUvN,EAAQuN,UAAY,GAC9BC,WAAYxN,EAAQwN,WACpBC,YAAazN,EAAQyN,iBAIjCC,UAAW,SAAU1N,GAAV,GACHgD,GAAOjE,KACPsK,EAAarJ,EAAQ8E,KACrBA,EAAO9B,EAAK0C,MAAM2D,EAAa,IAAMrG,EAAKmG,UAC9CnH,GAASgB,EAAKiE,eAAe,GAC7BnC,EAAKiH,OACLjK,EAAQ4L,UAAU5I,EAAKqI,OAAOxB,KAAK,SAAUyB,GACzCpL,EAASgB,EAAKiE,eAAe,GAC7BxI,EAAM4O,QACFC,QAASF,EACT9G,SAAUtG,EAAQsG,SAClBiH,SAAUvN,EAAQuN,UAAY,GAC9BC,WAAYxN,EAAQwN,WACpBC,YAAazN,EAAQyN,iBAIjCnE,WAAY,SAAUtJ,GAClB,GAAIgD,GAAOjE,MACPiB,EAAQkE,iBAAmBlE,EAAQoE,gBACnCpB,EAAKS,iBAETzD,EAAUzD,EAAEqC,OAAOoE,EAAKhD,QAASA,GACjC+B,EAAOkB,GAAGqG,WAAWpG,KAAKF,EAAMhD,GAC5BA,EAAQ8E,MACR9B,EAAK6I,aAAa7L,EAAQ8E,MAE1B9E,EAAQpC,OACRoF,EAAKD,QAAQnF,MAAMoC,EAAQpC,OAE3BoC,EAAQnC,QACRmF,EAAKD,QAAQlF,OAAOmC,EAAQnC,SAGpC8P,QAAS,WACLlP,EAAMmP,aAAa7O,KAAK6H,gBACpB7H,KAAKkJ,cACLlJ,KAAKkJ,aAAa0F,UAElB5O,KAAK8O,aACL9O,KAAK8O,YAAYF,UAEjB5O,KAAK+O,SACL/O,KAAK+O,QAAQH,UAEb5O,KAAKqE,UACLrE,KAAKqE,QAAQ2K,SACbhP,KAAKqE,QAAQuK,UACb5O,KAAKqE,QAAU,MAEfrE,KAAK2G,OAAS3G,KAAK2G,MAAMjI,SACzBsB,KAAK2G,MAAMqE,QAAQ,SAAUjF,GACzBA,EAAK6I,YAET5O,KAAK2G,UAET3G,KAAKkI,cAAc+G,IAAIrM,GACvBI,EAAOkB,GAAG0K,QAAQzK,KAAKnE,OAE3B6M,YAAa,WACT7M,KAAK2G,SACL3G,KAAKc,SAAW,KAChBd,KAAKiB,QAAQ8E,KAAO,EACpB/F,KAAKkI,cAAcgH,QACnBlP,KAAKkI,cAAc+G,IAAI/L,EAASN,GAChC5C,KAAKkI,cAAcqD,UAAU,KAGrC1I,GAAGsM,OAAOpL,IACZtE,OAAOC,MAAM2C,QACR5C,OAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.pdfviewer.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.pdfviewer', [\n        'pdf-viewer/processors/pdfjs-processor',\n        'pdf-viewer/processors/dpl-processor',\n        'pdf-viewer/toolbar',\n        'pdf-viewer/page',\n        'pdf-viewer/dialogs',\n        'pdf-viewer/commands'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'pdfviewer',\n        name: 'PDFViewer',\n        category: 'web',\n        description: 'PDFViewer to display pdfs in the browser',\n        depends: [\n            'core',\n            'window',\n            'dialog',\n            'toolbar'\n        ]\n    };\n    (function ($, undefined) {\n        var NS = '.kendoPDFViewer', kendo = window.kendo, ui = kendo.ui, proxy = $.proxy, extend = $.extend, drawing = kendo.drawing, Page, Widget = ui.Widget, progress = kendo.ui.progress, SCROLL = 'scroll', RENDER = 'render', OPEN = 'open', ERROR = 'error', FOCUS = 'focus', WHITECOLOR = '#ffffff', TABINDEX = 'tabindex', PROCESSORS = {\n                pdfjs: 'pdfjs',\n                dpl: 'dpl'\n            }, styles = {\n                viewer: 'k-pdf-viewer k-widget',\n                scroller: 'k-canvas k-list-scroller'\n            };\n        var PDFViewer = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, kendo.deepExtend({}, this.options, options));\n                that._wrapper();\n                if (that.options.toolbar) {\n                    that._renderToolbar();\n                }\n                that.wrapper.on(FOCUS, proxy(that._focus, that));\n                that._initProcessor(options || {});\n                that._renderPageContainer();\n                that._loadDocument();\n                that._tabindex();\n                kendo.notify(that, kendo.ui);\n            },\n            events: [\n                RENDER,\n                OPEN,\n                ERROR\n            ],\n            options: {\n                name: 'PDFViewer',\n                view: { type: 'canvas' },\n                pdfjsProcessing: { file: null },\n                dplProcessing: {\n                    read: {\n                        url: null,\n                        type: 'GET',\n                        dataType: 'json',\n                        pageField: 'pageNumber'\n                    },\n                    upload: {\n                        url: null,\n                        saveField: 'file'\n                    },\n                    download: { url: null },\n                    loadOnDemand: false\n                },\n                toolbar: { items: [] },\n                width: 1000,\n                height: 1200,\n                page: 1,\n                defaultPageSize: {\n                    width: 794,\n                    height: 1123\n                },\n                messages: {\n                    defaultFileName: 'Document',\n                    toolbar: {\n                        open: 'Open',\n                        exportAs: 'Export',\n                        download: 'Download',\n                        pager: {\n                            first: 'Go to the first page',\n                            previous: 'Go to the previous page',\n                            next: 'Go to the next page',\n                            last: 'Go to the last page',\n                            of: ' of {0} ',\n                            page: 'page',\n                            pages: 'pages'\n                        }\n                    },\n                    errorMessages: {\n                        notSupported: 'Only pdf files allowed.',\n                        parseError: 'PDF file fails to process.',\n                        notFound: 'File is not found.'\n                    },\n                    dialogs: {\n                        exportAsDialog: {\n                            title: 'Export...',\n                            defaultFileName: 'Document',\n                            pdf: 'Portable Document Format (.pdf)',\n                            png: 'Portable Network Graphics (.png)',\n                            svg: 'Scalable Vector Graphics (.svg)',\n                            labels: {\n                                fileName: 'File name',\n                                saveAsType: 'Save as',\n                                page: 'Page'\n                            }\n                        },\n                        okText: 'OK',\n                        save: 'Save',\n                        cancel: 'Cancel'\n                    }\n                }\n            },\n            _wrapper: function () {\n                var that = this, options = that.options;\n                that.wrapper = that.element;\n                that.wrapper.width(options.width).height(options.height).addClass(styles.viewer);\n                that._resizeHandler = kendo.onResize(function () {\n                    that.resize();\n                });\n            },\n            _focus: function (e) {\n                if (this.toolbar) {\n                    this.toolbar.wrapper.focus();\n                } else {\n                    this.pageContainer.focus();\n                }\n                e.preventDefault();\n            },\n            _initProcessor: function (options) {\n                var that = this, processingOptions;\n                processingOptions = options.dplProcessing ? that.options.dplProcessing : that.options.pdfjsProcessing;\n                that.processingLib = options.dplProcessing ? PROCESSORS.dpl : PROCESSORS.pdfjs;\n                that.processor = new kendo.pdfviewer[that.processingLib].processor(processingOptions, that);\n                Page = kendo.pdfviewer[that.processingLib].Page;\n            },\n            _renderToolbar: function () {\n                var that = this, options = that.options;\n                var toolbarOptions = {\n                    pager: { messages: options.messages.toolbar.pager },\n                    resizable: true,\n                    items: options.toolbar.items,\n                    width: options.width,\n                    action: that.execute.bind(that),\n                    messages: options.messages.toolbar\n                };\n                var toolbarElement = $('<div />');\n                toolbarElement.appendTo(that.element);\n                that.toolbar = new kendo.pdfviewer.Toolbar(toolbarElement, toolbarOptions);\n            },\n            _initErrorDialog: function (options) {\n                var that = this;\n                if (!that._errorDialog) {\n                    options = extend(options, { messages: that.options.messages });\n                    var dialogInstance = new kendo.pdfviewer.dialogs.ErrorDialog(options);\n                    that._errorDialog = dialogInstance._dialog;\n                }\n                return that._errorDialog;\n            },\n            _renderPageContainer: function () {\n                var that = this;\n                if (!that.pageContainer) {\n                    that.pageContainer = $('<div />');\n                    that.pageContainer.addClass(styles.scroller);\n                    that.pageContainer.attr(TABINDEX, 0);\n                    that.wrapper.append(that.pageContainer);\n                }\n            },\n            _triggerError: function (options) {\n                var dialog = this._initErrorDialog();\n                extend(options, { dialog: dialog });\n                if (this.pageContainer) {\n                    progress(this.pageContainer, false);\n                }\n                if (this.trigger(ERROR, options)) {\n                    return;\n                }\n                dialog.open().content(options.message);\n            },\n            _renderPages: function () {\n                var that = this, document = that.document, pagesData;\n                that.pages = [];\n                if (!document || !document.total) {\n                    that._renderBlankPage();\n                    return;\n                }\n                pagesData = document.pages;\n                for (var i = 1; i <= document.total; i++) {\n                    var viewerPage, pageData = {\n                            processor: that.processor,\n                            number: i\n                        };\n                    if (pagesData && pagesData.length) {\n                        pageData = extend(pageData, pagesData[i - 1]);\n                    }\n                    viewerPage = new Page(pageData, that);\n                    that.pages.push(viewerPage);\n                    that.pageContainer.append(viewerPage.element);\n                }\n                if (that.pages.length > 1) {\n                    that.pageContainer.on(SCROLL + NS, proxy(that._scroll, that));\n                }\n            },\n            _renderBlankPage: function () {\n                this._blankPage = new Page(this.options.defaultPageSize, this);\n                this.pageContainer.append(this._blankPage.element);\n                this._updatePager(1, 1);\n            },\n            _updatePager: function (pageNumber, total) {\n                if (!this.toolbar || !this.toolbar.pager) {\n                    return;\n                }\n                this.toolbar.pager.setOptions({\n                    page: pageNumber,\n                    total: total\n                });\n            },\n            _resize: function () {\n                var that = this;\n                var containerWidth;\n                var containerHeight;\n                var loadedPagesHeight = 0;\n                var ratio;\n                containerWidth = that.pageContainer[0].clientWidth;\n                containerHeight = that.pageContainer[0].clientHeight;\n                if (!that.pages || !that.pages.length) {\n                    if (that._blankPage) {\n                        ratio = containerWidth / that._blankPage.element.width();\n                        that._blankPage.resize(ratio);\n                    }\n                    return;\n                }\n                if (that.toolbar) {\n                    that.toolbar.resize(true);\n                }\n                that._visiblePagesCount = 1;\n                that.pages.forEach(function (page) {\n                    ratio = containerWidth / page.element.width();\n                    page.resize(ratio);\n                    loadedPagesHeight += page.element.height();\n                    if (loadedPagesHeight < containerHeight && page.pageNumber > 1) {\n                        that._visiblePagesCount++;\n                    }\n                });\n            },\n            _scroll: function () {\n                var that = this, containerScrollHeight = that.pageContainer[0].scrollHeight, containerHeight = that.pageContainer.height(), containerScrollTop = that.pageContainer.scrollTop(), containerOffsetTop = that.pageContainer.offset().top, pageNum = that.options.page, pageIndex = pageNum - 1, total = that.pages.length, pageToLoad = pageNum, currentPage = that.pages[pageIndex], currentPageTop = currentPage.element.offset().top - containerOffsetTop, currentPageHeight = currentPage.element.height(), previousPage, prevPageTop, prevPageHeight, scrollDirection = containerScrollTop - that._prevScrollTop > 0 ? 1 : -1;\n                if (that._preventScroll) {\n                    that._preventScroll = false;\n                    return;\n                }\n                if (scrollDirection == -1 && that.pages[pageIndex + scrollDirection]) {\n                    previousPage = that.pages[pageIndex - that._visiblePagesCount] || that.pages[pageIndex + scrollDirection];\n                    prevPageTop = previousPage.element.offset().top - containerOffsetTop;\n                    prevPageHeight = previousPage.element.height();\n                }\n                if (Math.abs(containerScrollTop - (that._prevScrollTop || 0)) > containerHeight) {\n                    pageToLoad = Math.floor(containerScrollTop * (1 / (containerScrollHeight / total))) + 1;\n                } else if (currentPageTop < 0 && Math.abs(currentPageTop) >= currentPageHeight / 2 && scrollDirection === 1) {\n                    pageToLoad++;\n                } else if (previousPage && Math.abs(prevPageTop) <= prevPageHeight / 2) {\n                    pageToLoad--;\n                }\n                if (pageNum !== pageToLoad && pageToLoad >= 1 && pageToLoad <= total) {\n                    that.options.page = pageToLoad;\n                    that._loadVisiblePages();\n                    that._updatePager(pageToLoad, total);\n                }\n                that._prevScrollTop = containerScrollTop;\n            },\n            execute: function (options) {\n                var commandOptions = extend({ viewer: this }, options.options);\n                var command = new kendo.pdfviewer[options.command](commandOptions);\n                command.exec();\n            },\n            _loadDocument: function () {\n                var that = this;\n                var page = that.options.page;\n                progress(that.pageContainer, true);\n                that.processor.fetchDocument().done(function (document) {\n                    that._clearPages();\n                    that.document = document;\n                    that._renderPages();\n                    that.resize(true);\n                    if (document) {\n                        page = page >= 1 && page <= document.total ? page : 1;\n                        that.activatePage(page);\n                    }\n                    progress(that.pageContainer, false);\n                });\n            },\n            loadPage: function (number) {\n                var page = this.pages && this.pages[number - 1];\n                if (page) {\n                    page.load();\n                }\n            },\n            activatePage: function (number) {\n                var page = this.pages && this.pages[number - 1];\n                var currentScrollTop = this.pageContainer.scrollTop();\n                if (!page) {\n                    return;\n                }\n                this.options.page = number;\n                this._loadVisiblePages();\n                this._preventScroll = true;\n                this.pageContainer.scrollTop(currentScrollTop + page.element.position().top);\n                this._updatePager(number, this.pages.length);\n            },\n            _loadVisiblePages: function () {\n                var pagesCount = this.pages && this.pages.length;\n                var minVisiblePageNum = this.options.page;\n                var maxVisiblePageNum = Math.min(minVisiblePageNum + this._visiblePagesCount, pagesCount);\n                for (var i = minVisiblePageNum; i <= maxVisiblePageNum; i++) {\n                    this.loadPage(i);\n                }\n            },\n            fromFile: function (file) {\n                this.processor._updateDocument(file);\n                this._loadDocument();\n            },\n            exportImage: function (options) {\n                var that = this;\n                var pageNumber = options.page;\n                var page = that.pages[pageNumber - 1] || that._blankPage;\n                var rootGroup = new drawing.Group();\n                page.load();\n                var background = kendo.drawing.Path.fromRect(new kendo.geometry.Rect([\n                    0,\n                    0\n                ], [\n                    page.width,\n                    page.height\n                ]), {\n                    fill: { color: WHITECOLOR },\n                    stroke: null\n                });\n                progress(that.pageContainer, true);\n                rootGroup.append(background, page.group);\n                drawing.exportImage(rootGroup).done(function (data) {\n                    progress(that.pageContainer, false);\n                    kendo.saveAs({\n                        dataURI: data,\n                        fileName: options.fileName,\n                        proxyURL: options.proxyURL || '',\n                        forceProxy: options.forceProxy,\n                        proxyTarget: options.proxyTarget\n                    });\n                });\n            },\n            exportSVG: function (options) {\n                var that = this;\n                var pageNumber = options.page;\n                var page = that.pages[pageNumber - 1] || that._blankPage;\n                progress(that.pageContainer, true);\n                page.load();\n                drawing.exportSVG(page.group).done(function (data) {\n                    progress(that.pageContainer, false);\n                    kendo.saveAs({\n                        dataURI: data,\n                        fileName: options.fileName,\n                        proxyURL: options.proxyURL || '',\n                        forceProxy: options.forceProxy,\n                        proxyTarget: options.proxyTarget\n                    });\n                });\n            },\n            setOptions: function (options) {\n                var that = this;\n                if (options.pdfjsProcessing || options.dplProcessing) {\n                    that._initProcessor();\n                }\n                options = $.extend(that.options, options);\n                Widget.fn.setOptions.call(that, options);\n                if (options.page) {\n                    that.activatePage(options.page);\n                }\n                if (options.width) {\n                    that.element.width(options.width);\n                }\n                if (options.height) {\n                    that.element.height(options.height);\n                }\n            },\n            destroy: function () {\n                kendo.unbindResize(this._resizeHandler);\n                if (this._errorDialog) {\n                    this._errorDialog.destroy();\n                }\n                if (this._saveDialog) {\n                    this._saveDialog.destroy();\n                }\n                if (this._upload) {\n                    this._upload.destroy();\n                }\n                if (this.toolbar) {\n                    this.toolbar.unbind();\n                    this.toolbar.destroy();\n                    this.toolbar = null;\n                }\n                if (this.pages && this.pages.length) {\n                    this.pages.forEach(function (page) {\n                        page.destroy();\n                    });\n                    this.pages = [];\n                }\n                this.pageContainer.off(NS);\n                Widget.fn.destroy.call(this);\n            },\n            _clearPages: function () {\n                this.pages = [];\n                this.document = null;\n                this.options.page = 1;\n                this.pageContainer.empty();\n                this.pageContainer.off(SCROLL + NS);\n                this.pageContainer.scrollTop(0);\n            }\n        });\n        ui.plugin(PDFViewer);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}