{"version": 3, "sources": ["kendo.mobile.shim.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "ui", "mobile", "Popup", "SHIM", "HIDE", "Widget", "<PERSON><PERSON>", "extend", "init", "element", "options", "that", "this", "app", "application", "os", "support", "mobileOS", "osname", "name", "ioswp", "skin", "bb", "align", "position", "effect", "shim", "handler", "hide", "fn", "call", "className", "addClass", "modal", "on", "document", "body", "append", "popup", "anchor", "appendTo", "origin", "animation", "open", "effects", "duration", "close", "e", "prevented", "_apiCall", "trigger", "preventDefault", "deactivate", "show", "notify", "events", "destroy", "kendoD<PERSON>roy", "remove", "_hide", "contains", "children", "target", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,eAAgBD,IAC/C,WAuFE,MA9EC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAMG,OAAOD,GAAIE,EAAQJ,EAAME,GAAGE,MAAOC,EAAO,yBAA0BC,EAAO,OAAQC,EAASL,EAAGK,OAChIC,EAAOD,EAAOE,QACdC,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOC,KAAMC,EAAMf,EAAMG,OAAOa,YAAaC,EAAKjB,EAAMkB,QAAQC,SAAUC,EAASL,EAAMA,EAAIE,GAAGI,KAAOJ,EAAKA,EAAGI,KAAO,MAAOC,EAAmB,QAAXF,GAA+B,OAAXA,KAAoBL,GAAMA,EAAIE,GAAGM,KAAeC,EAAgB,eAAXJ,EAAyBK,EAAQb,EAAQa,QAAUH,EAAQ,gBAAkBE,EAAK,eAAiB,iBAAkBE,EAAWd,EAAQc,WAAaJ,EAAQ,gBAAkBE,EAAK,eAAiB,iBAAkBG,EAASf,EAAQe,SAAWL,EAAQ,aAAeE,EAAK,eAAiB,WAAYI,EAAO9B,EAAEO,GAAMwB,QAAQhB,GAAMiB,MAC3hBvB,GAAOwB,GAAGrB,KAAKsB,KAAKnB,EAAMF,EAASC,GACnCC,EAAKe,KAAOA,EACZjB,EAAUE,EAAKF,QACfC,EAAUC,EAAKD,QACXA,EAAQqB,WACRpB,EAAKe,KAAKM,SAAStB,EAAQqB,WAE1BrB,EAAQuB,OACTtB,EAAKe,KAAKQ,GAAG,OAAQ,UAExBrB,EAAMA,EAAIJ,QAAUb,EAAEuC,SAASC,OAAOC,OAAOX,GAC9Cf,EAAK2B,MAAQ,GAAIpC,GAAMS,EAAKF,SACxB8B,OAAQb,EACRO,OAAO,EACPO,SAAUd,EACVe,OAAQlB,EACRC,SAAUA,EACVkB,WACIC,MACIC,QAASnB,EACToB,SAAUnC,EAAQmC,UAEtBC,OAASD,SAAUnC,EAAQmC,WAE/BC,MAAO,SAAUC,GACb,GAAIC,IAAY,CACXrC,GAAKsC,WACND,EAAYrC,EAAKuC,QAAQ9C,IAEzB4C,GACAD,EAAEI,iBAENxC,EAAKsC,UAAW,GAEpBG,WAAY,WACR1B,EAAKE,QAETe,KAAM,WACFjB,EAAK2B,UAGbvD,EAAMwD,OAAO3C,IAEjB4C,QAASnD,GACTM,SACIS,KAAM,OACNc,OAAO,EACPV,MAAO1B,EACP2B,SAAU3B,EACV4B,OAAQ5B,EACRgD,SAAU,KAEdQ,KAAM,WACFzC,KAAK0B,MAAMK,QAEff,KAAM,WACFhB,KAAKqC,UAAW,EAChBrC,KAAK0B,MAAMQ,SAEfU,QAAS,WACLnD,EAAOwB,GAAG2B,QAAQ1B,KAAKlB,MACvBA,KAAKc,KAAK+B,eACV7C,KAAK0B,MAAMkB,UACX5C,KAAKc,KAAKgC,UAEdC,MAAO,SAAUZ,GACRA,GAAMnD,EAAEgE,SAAShD,KAAKc,KAAKmC,WAAWA,SAAS,YAAY,GAAId,EAAEe,SAClElD,KAAK0B,MAAMQ,UAIvB9C,GAAG+D,OAAOzD,IACZP,OAAOD,MAAMkE,QACRjE,OAAOD,OACE,kBAAVH,SAAwBA,OAAOsE,IAAMtE,OAAS,SAAUuE,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.shim.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.shim', ['kendo.popup'], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.shim',\n        name: 'Shim',\n        category: 'mobile',\n        description: 'Mobile Shim',\n        depends: ['popup'],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.mobile.ui, Popup = kendo.ui.Popup, SHIM = '<div class=\"km-shim\"/>', HIDE = 'hide', Widget = ui.Widget;\n        var Shim = Widget.extend({\n            init: function (element, options) {\n                var that = this, app = kendo.mobile.application, os = kendo.support.mobileOS, osname = app ? app.os.name : os ? os.name : 'ios', ioswp = osname === 'ios' || osname === 'wp' || (app ? app.os.skin : false), bb = osname === 'blackberry', align = options.align || (ioswp ? 'bottom center' : bb ? 'center right' : 'center center'), position = options.position || (ioswp ? 'bottom center' : bb ? 'center right' : 'center center'), effect = options.effect || (ioswp ? 'slideIn:up' : bb ? 'slideIn:left' : 'fade:in'), shim = $(SHIM).handler(that).hide();\n                Widget.fn.init.call(that, element, options);\n                that.shim = shim;\n                element = that.element;\n                options = that.options;\n                if (options.className) {\n                    that.shim.addClass(options.className);\n                }\n                if (!options.modal) {\n                    that.shim.on('down', '_hide');\n                }\n                (app ? app.element : $(document.body)).append(shim);\n                that.popup = new Popup(that.element, {\n                    anchor: shim,\n                    modal: true,\n                    appendTo: shim,\n                    origin: align,\n                    position: position,\n                    animation: {\n                        open: {\n                            effects: effect,\n                            duration: options.duration\n                        },\n                        close: { duration: options.duration }\n                    },\n                    close: function (e) {\n                        var prevented = false;\n                        if (!that._apiCall) {\n                            prevented = that.trigger(HIDE);\n                        }\n                        if (prevented) {\n                            e.preventDefault();\n                        }\n                        that._apiCall = false;\n                    },\n                    deactivate: function () {\n                        shim.hide();\n                    },\n                    open: function () {\n                        shim.show();\n                    }\n                });\n                kendo.notify(that);\n            },\n            events: [HIDE],\n            options: {\n                name: 'Shim',\n                modal: false,\n                align: undefined,\n                position: undefined,\n                effect: undefined,\n                duration: 200\n            },\n            show: function () {\n                this.popup.open();\n            },\n            hide: function () {\n                this._apiCall = true;\n                this.popup.close();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.shim.kendoDestroy();\n                this.popup.destroy();\n                this.shim.remove();\n            },\n            _hide: function (e) {\n                if (!e || !$.contains(this.shim.children().children('.k-popup')[0], e.target)) {\n                    this.popup.close();\n                }\n            }\n        });\n        ui.plugin(Shim);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}