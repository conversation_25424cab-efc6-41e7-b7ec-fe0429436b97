{"version": 3, "sources": ["kendo.tooltip.js"], "names": ["f", "define", "$", "undefined", "restoreTitle", "element", "length", "restoreTitleAttributeForElement", "parent", "title", "data", "kendo", "ns", "attr", "removeData", "saveTitleAttributeForElement", "saveTitleAttributes", "is", "window", "Widget", "ui", "Popup", "isFunction", "isPlainObject", "extend", "proxy", "DOCUMENT", "document", "isLocalUrl", "ARIAIDSUFFIX", "DESCRIBEDBY", "SHOW", "HIDE", "ERROR", "CONTENTLOAD", "REQUESTSTART", "KCONTENTFRAME", "TEMPLATE", "IFRAMETEMPLATE", "template", "NS", "POSITIONS", "bottom", "origin", "position", "top", "left", "collision", "right", "center", "REVERSE", "DIRCLASSES", "DIMENSIONS", "horizontal", "offset", "size", "vertical", "DEFAULTCONTENT", "e", "target", "<PERSON><PERSON><PERSON>", "init", "options", "axis", "that", "this", "fn", "call", "match", "dimensions", "_documentKeyDownHandler", "_documentKeyDown", "on", "showOn", "filter", "_showOn", "_isShownOnMouseEnter", "_isShownOnClick", "_mouseenter", "autoHide", "_mouseleave", "_isShownOnFocus", "_blur", "name", "content", "showAfter", "hideAfter", "callout", "width", "height", "animation", "open", "effects", "duration", "close", "hide", "events", "currentTarget", "_show", "clearTimeout", "timeout", "setTimeout", "_appendContent", "iframe", "contentOptions", "showIframe", "url", "trigger", "find", "src", "html", "off", "show", "empty", "progress", "_ajaxRequest", "sender", "angular", "elements", "j<PERSON><PERSON><PERSON>", "ajax", "type", "dataType", "cache", "error", "xhr", "status", "success", "keyCode", "keys", "ESC", "refresh", "popup", "anchor", "current", "_initPopup", "kendoStop", "one", "removeAttr", "_hovered", "wrapper", "dir", "autosize", "activate", "ariaId", "id", "_positionCallout", "_offset", "copyAnchorStyles", "css", "arrow", "_closeButtonClick", "preventDefault", "_closePopup", "anchorOffset", "elementOffset", "cssClass", "flipped", "offsetAmount", "removeClass", "addClass", "destroy", "isTopLeft", "isFlipped", "direction", "marginRule", "_outerWidth", "plugin", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,iBACH,aACA,cACA,YACDD,IACL,WAgXE,MA/VC,UAAUE,EAAGC,GAgDV,QAASC,GAAaC,GAClB,KAAOA,EAAQC,SACPC,EAAgCF,IAGpCA,EAAUA,EAAQG,SAG1B,QAASD,GAAgCF,GACrC,GAAII,GAAQJ,EAAQK,KAAKC,EAAMC,GAAK,QACpC,IAAIH,EAGA,MAFAJ,GAAQQ,KAAK,QAASJ,GACtBJ,EAAQS,WAAWH,EAAMC,GAAK,UACvB,EAGf,QAASG,GAA6BV,GAClC,GAAII,GAAQJ,EAAQQ,KAAK,QACzB,IAAIJ,EAGA,MAFAJ,GAAQK,KAAKC,EAAMC,GAAK,QAASH,GACjCJ,EAAQQ,KAAK,QAAS,KACf,EAGf,QAASG,GAAoBX,GACzB,KAAOA,EAAQC,SAAWD,EAAQY,GAAG,UAC7BF,EAA6BV,IAGjCA,EAAUA,EAAQG,SA7E7B,GACOG,GAAQO,OAAOP,MAAOQ,EAASR,EAAMS,GAAGD,OAAQE,EAAQV,EAAMS,GAAGC,MAAOC,EAAaX,EAAMW,WAAYC,EAAgBrB,EAAEqB,cAAeC,EAAStB,EAAEsB,OAAQC,EAAQvB,EAAEuB,MAAOC,EAAWxB,EAAEyB,UAAWC,EAAajB,EAAMiB,WAAYC,EAAe,aAAcC,EAAc,mBAAoBC,EAAO,OAAQC,EAAO,OAAQC,EAAQ,QAASC,EAAc,cAAeC,EAAe,eAAgBC,EAAgB,kBAAmBC,EAAW,0TAA0UC,EAAiB3B,EAAM4B,SAAS,kCAAuCH,EAAgB,wFAA2GI,EAAK,gBAAiBC,GACz9BC,QACIC,OAAQ,gBACRC,SAAU,cAEdC,KACIF,OAAQ,aACRC,SAAU,iBAEdE,MACIH,OAAQ,cACRC,SAAU,eACVG,UAAW,YAEfC,OACIL,OAAQ,eACRC,SAAU,cACVG,UAAW,YAEfE,QACIL,SAAU,gBACVD,OAAQ,kBAEbO,GACCL,IAAO,SACPH,OAAU,MACVI,KAAQ,QACRE,MAAS,OACTC,OAAU,UACXE,GACCT,OAAQ,IACRG,IAAK,IACLC,KAAM,IACNE,MAAO,IACPC,OAAQ,KACTG,GACCC,YACIC,OAAQ,MACRC,KAAM,eAEVC,UACIF,OAAQ,OACRC,KAAM,eAEXE,EAAiB,SAAUC,GAC1B,MAAOA,GAAEC,OAAOjD,KAAKC,EAAMC,GAAK,UAkCpCgD,EAAUzC,EAAOK,QACjBqC,KAAM,SAAUxD,EAASyD,GACrB,GAAiBC,GAAbC,EAAOC,IACX9C,GAAO+C,GAAGL,KAAKM,KAAKH,EAAM3D,EAASyD,GACnCC,EAAOC,EAAKF,QAAQlB,SAASwB,MAAM,cAAgB,aAAe,WAClEJ,EAAKK,WAAajB,EAAWW,GAC7BC,EAAKM,wBAA0B7C,EAAMuC,EAAKO,iBAAkBP,GAC5DA,EAAK3D,QAAQmE,GAAGR,EAAKF,QAAQW,OAASjC,EAAIwB,EAAKF,QAAQY,OAAQjD,EAAMuC,EAAKW,QAASX,KAC/EC,KAAKW,wBAA0BX,KAAKY,oBACpCb,EAAK3D,QAAQmE,GAAG,aAAehC,EAAIwB,EAAKF,QAAQY,OAAQjD,EAAMuC,EAAKc,YAAad,IAEhFC,KAAKH,QAAQiB,UAAYd,KAAKW,wBAC9BZ,EAAK3D,QAAQmE,GAAG,aAAehC,EAAIwB,EAAKF,QAAQY,OAAQjD,EAAMuC,EAAKgB,YAAahB,IAEhFC,KAAKH,QAAQiB,UAAYd,KAAKgB,mBAC9BjB,EAAK3D,QAAQmE,GAAG,OAAShC,EAAIwB,EAAKF,QAAQY,OAAQjD,EAAMuC,EAAKkB,MAAOlB,KAG5EF,SACIqB,KAAM,UACNT,OAAQ,GACRU,QAAS3B,EACT4B,UAAW,IACXC,UAAW,IACXC,SAAS,EACTjC,OAAQ,EACRV,SAAU,SACV6B,OAAQ,aACRM,UAAU,EACVS,MAAO,KACPC,OAAQ,KACRC,WACIC,MACIC,QAAS,UACTC,SAAU,GAEdC,OACID,SAAU,GACVE,MAAM,KAIlBC,QACIjE,EACAC,EACAE,EACAD,EACAE,GAEJ8C,gBAAiB,WACb,MAAOhB,MAAKH,QAAQW,QAAUR,KAAKH,QAAQW,OAAOL,MAAM,UAE5DQ,qBAAsB,WAClB,MAAOX,MAAKH,QAAQW,QAAUR,KAAKH,QAAQW,OAAOL,MAAM,eAE5DS,gBAAiB,WACb,MAAOZ,MAAKH,QAAQW,QAAUR,KAAKH,QAAQW,OAAOL,MAAM,UAE5DU,YAAa,SAAUpB,GACnB1C,EAAoBd,EAAEwD,EAAEuC,iBAE5BtB,QAAS,SAAUjB,GAAV,GACDM,GAAOC,KACPgC,EAAgB/F,EAAEwD,EAAEuC,cACpBjC,GAAKa,oBAAsBb,EAAKY,uBAChCZ,EAAKkC,MAAMD,GACJjC,EAAKiB,mBACZjE,EAAoBiF,GACpBjC,EAAKkC,MAAMD,KAEXE,aAAanC,EAAKoC,SAClBpC,EAAKoC,QAAUC,WAAW,WACtBrC,EAAKkC,MAAMD,IACZjC,EAAKF,QAAQuB,aAGxBiB,eAAgB,SAAU3C,GACtB,GAAkH4C,GAA9GvC,EAAOC,KAAMuC,EAAiBxC,EAAKF,QAAQsB,QAAS/E,EAAU2D,EAAKoB,QAASqB,EAAazC,EAAKF,QAAQyC,MACtGhF,GAAciF,IAAmBA,EAAeE,KAC1C,UAAY1C,GAAKF,UACnB2C,GAAc7E,EAAW4E,EAAeE,MAE5C1C,EAAK2C,QAAQxE,GACT2B,QAAS0C,EACT7C,OAAQA,IAEP8C,GAKDpG,EAAQ0F,OACRQ,EAASlG,EAAQuG,KAAK,IAAMxE,GAAe,GACvCmE,EACAA,EAAOM,IAAML,EAAeE,KAAOH,EAAOM,IAE1CxG,EAAQyG,KAAKxE,GAAiB8C,QAASoB,KAE3CnG,EAAQuG,KAAK,IAAMxE,GAAe2E,IAAI,OAASvE,GAAIgC,GAAG,OAAShC,EAAI,WAC/DwB,EAAK2C,QAAQzE,GACb7B,EAAQ2G,WAbZ3G,EAAQ4G,QACRtG,EAAMS,GAAG8F,SAAS7G,GAAS,GAC3B2D,EAAKmD,aAAaX,KAcfA,GAAkBlF,EAAWkF,IACpCA,EAAiBA,GACbY,OAAQnD,KACRN,OAAQA,IAEZtD,EAAQyG,KAAKN,GAAkB,KAE/BnG,EAAQyG,KAAKN,GAEjBxC,EAAKqD,QAAQ,UAAW,WACpB,OAASC,SAAUjH,MAG3B8G,aAAc,SAAUrD,GACpB,GAAIE,GAAOC,IACXsD,QAAOC,KAAKhG,GACRiG,KAAM,MACNC,SAAU,OACVC,OAAO,EACPC,MAAO,SAAUC,EAAKC,GAClBnH,EAAMS,GAAG8F,SAASlD,EAAKoB,SAAS,GAChCpB,EAAK2C,QAAQ1E,GACT6F,OAAQA,EACRD,IAAKA,KAGbE,QAAStG,EAAM,SAAUf,GACrBC,EAAMS,GAAG8F,SAASlD,EAAKoB,SAAS,GAChCpB,EAAKoB,QAAQ0B,KAAKpG,GAClBsD,EAAK2C,QAAQzE,IACd8B,IACJF,KAEPS,iBAAkB,SAAUb,GACpBA,EAAEsE,UAAYrH,EAAMsH,KAAKC,KACzBjE,KAAK8B,QAGboC,QAAS,WACL,GAAInE,GAAOC,KAAMmE,EAAQpE,EAAKoE,KAC1BA,IAASA,EAAMtE,QAAQuE,QACvBrE,EAAKsC,eAAe8B,EAAMtE,QAAQuE,SAG1CtC,KAAM,WACE9B,KAAKmE,OACLnE,KAAKmE,MAAMtC,SAGnBkB,KAAM,SAAUrD,GACZA,EAASA,GAAUM,KAAK5D,QACxBW,EAAoB2C,GACpBM,KAAKiC,MAAMvC,IAEfuC,MAAO,SAAUvC,GACb,GAAIK,GAAOC,KAAMqE,EAAUtE,EAAKL,QAC3BK,GAAKoE,OACNpE,EAAKuE,aAELD,GAAWA,EAAQ,IAAM3E,EAAO,KAChCK,EAAKoE,MAAMtC,QACX9B,EAAKoE,MAAM/H,QAAQmI,WAAU,GAAM,IAElCF,GAAWA,EAAQ,IAAM3E,EAAO,KACjCK,EAAKsC,eAAe3C,GACpBK,EAAKoE,MAAMtE,QAAQuE,OAAS1E,GAEhCK,EAAKoE,MAAMK,IAAI,aAAc,WACzBrI,EAAauD,GACbA,EAAO+E,WAAW5G,GAClBmC,KAAK5D,QAAQqI,WAAW,MAAM7H,KAAK,eAAe,GAClDa,EAASqF,IAAI,UAAYvE,EAAIwB,EAAKM,2BAEtCN,EAAKoE,MAAMO,UAAW,EACtB3E,EAAKoE,MAAMzC,QAEf4C,WAAY,WACR,GAAIvE,GAAOC,KAAMH,EAAUE,EAAKF,QAAS8E,EAAU1I,EAAES,EAAM4B,SAASF,IAC5DkD,QAASzB,EAAQyB,SAAgC,WAArBzB,EAAQlB,SACpCiG,IAAK1F,EAAWW,EAAQlB,UACxBmC,SAAUjB,EAAQiB,WAE1Bf,GAAKoE,MAAQ,GAAI/G,GAAMuH,EAASpH,GAC5BsH,UAAU,EACVC,SAAU,WACN,GAAIV,GAASpE,KAAKH,QAAQuE,OAAQW,EAASX,EAAO,GAAGY,IAAMjF,EAAK3D,QAAQ,GAAG4I,EACvED,KACAX,EAAOxH,KAAKiB,EAAakH,EAASnH,GAClCoC,KAAK5D,QAAQQ,KAAK,KAAMmI,EAASnH,IAEjCiC,EAAQyB,QACRvB,EAAKkF,mBAELlF,EAAKmF,QAAQnF,EAAKF,QAAQlB,SAAUoB,EAAKF,QAAQR,QAErDW,KAAK5D,QAAQqI,WAAW,eACxBhH,EAAS8C,GAAG,UAAYhC,EAAIwB,EAAKM,yBACjCN,EAAK2C,QAAQ5E,GACbiC,EAAKoE,MAAMO,SAAWxI,GAE1B2F,MAAO,WACH9B,EAAK2C,QAAQ3E,IAEjBoH,kBAAkB,EAClB1D,UAAW5B,EAAQ4B,WACpBjD,EAAUqB,EAAQlB,YACrBgG,EAAQS,KACJ7D,MAAO1B,EAAQ0B,MACfC,OAAQ3B,EAAQ2B,SAEpBzB,EAAKoB,QAAUwD,EAAQhC,KAAK,sBAC5B5C,EAAKsF,MAAQV,EAAQhC,KAAK,cACtB9C,EAAQiB,UAAYd,KAAKW,uBACzBgE,EAAQpE,GAAG,aAAehC,EAAIf,EAAMuC,EAAKgB,YAAahB,IAEtD4E,EAAQpE,GAAG,QAAUhC,EAAI,oBAAqBf,EAAMuC,EAAKuF,kBAAmBvF,KAGpFuF,kBAAmB,SAAU7F,GACzBA,EAAE8F,iBACFvF,KAAK8B,QAETf,YAAa,SAAUtB,GACnB,GAAIM,GAAOC,IACXkC,cAAanC,EAAKoC,SAClBpC,EAAKoC,QAAUC,WAAW,WACtBrC,EAAKyF,YAAY/F,EAAEuC,gBACpBjC,EAAKF,QAAQwB,YAEpBJ,MAAO,SAAUxB,GACbO,KAAKwF,YAAY/F,EAAEuC,gBAEvBwD,YAAa,SAAU9F,GACfM,KAAKmE,QAAUnE,KAAKmE,MAAMO,SAC1B1E,KAAKmE,MAAMtC,QAEX1F,EAAaF,EAAEyD,KAGvBA,OAAQ,WACJ,MAAIM,MAAKmE,MACEnE,KAAKmE,MAAMtE,QAAQuE,OAEvB,MAEXa,iBAAkB,WACd,GAAIlF,GAAOC,KAAMrB,EAAWoB,EAAKF,QAAQlB,SAAUyB,EAAaL,EAAKK,WAAYf,EAASe,EAAWf,OAAQ8E,EAAQpE,EAAKoE,MAAOC,EAASD,EAAMtE,QAAQuE,OAAQqB,EAAexJ,EAAEmI,GAAQ/E,SAAUqG,EAAgBzJ,EAAEkI,EAAM/H,SAASiD,SAAUsG,EAAWzG,EAAWiF,EAAMyB,QAAU3G,EAAQN,GAAYA,GAAWkH,EAAeJ,EAAapG,GAAUqG,EAAcrG,GAAUpD,EAAEmI,GAAQhE,EAAWd,QAAU,CAChZS,GAAKmF,QAAQvG,EAAUoB,EAAKF,QAAQR,QACpCU,EAAKsF,MAAMS,YAAY,mDAAmDC,SAAS,aAAeJ,GAAUP,IAAI/F,EAAQwG,IAE5HG,QAAS,WACL,GAAI7B,GAAQnE,KAAKmE,KACbA,KACAA,EAAM/H,QAAQ0G,IAAIvE,GAClB4F,EAAM6B,WAEV9D,aAAalC,KAAKmC,SAClBnC,KAAK5D,QAAQ0G,IAAIvE,GACjBd,EAASqF,IAAI,UAAYvE,EAAIyB,KAAKK,yBAClCnD,EAAO+C,GAAG+F,QAAQ9F,KAAKF,OAE3BkF,QAAS,SAAUvG,EAAUkH,GACzB,GAAI9F,GAAOC,KAAMiG,EAAwB,OAAZtH,GAAiC,QAAZA,EAAoBuH,EAAYnG,EAAKoE,MAAMyB,QAASO,EAAYF,GAAaC,IAAcD,IAAcC,EAAY,KAAQE,EAAaH,EAAY,UAAYtH,EAAW,UAAYM,EAAQN,GAAWU,EAAS3C,EAAM2J,YAAYtG,EAAKsF,OAAS,EAAIQ,CAC3S9F,GAAKoE,MAAMQ,QAAQS,IAAIgB,EAAY/G,EAAS8G,EAAY,QAGhEzJ,GAAMS,GAAGmJ,OAAO3G,IAClB1C,OAAOP,MAAM4G,QACRrG,OAAOP,OACE,kBAAVV,SAAwBA,OAAOuK,IAAMvK,OAAS,SAAUwK,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.tooltip.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.tooltip', [\n        'kendo.core',\n        'kendo.popup',\n        'kendo.fx'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'tooltip',\n        name: 'Tooltip',\n        category: 'web',\n        description: 'The Tooltip widget displays a popup hint for a given html element.',\n        depends: [\n            'core',\n            'popup'\n        ],\n        features: [{\n                id: 'tooltip-fx',\n                name: 'Animation',\n                description: 'Support for animation',\n                depends: ['fx']\n            }]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, Popup = kendo.ui.Popup, isFunction = kendo.isFunction, isPlainObject = $.isPlainObject, extend = $.extend, proxy = $.proxy, DOCUMENT = $(document), isLocalUrl = kendo.isLocalUrl, ARIAIDSUFFIX = '_tt_active', DESCRIBEDBY = 'aria-describedby', SHOW = 'show', HIDE = 'hide', ERROR = 'error', CONTENTLOAD = 'contentLoad', REQUESTSTART = 'requestStart', KCONTENTFRAME = 'k-content-frame', TEMPLATE = '<div role=\"tooltip\" class=\"k-widget k-tooltip#if (!autoHide) {# k-tooltip-closable#}#\">#if (!autoHide) {# <div class=\"k-tooltip-button\"><a href=\"\\\\#\" class=\"k-icon k-i-close\" title=\"Close\"></a></div> #}#' + '<div class=\"k-tooltip-content\"></div>' + '#if (callout){ #<div class=\"k-callout k-callout-#=dir#\"></div>#}#' + '</div>', IFRAMETEMPLATE = kendo.template('<iframe frameborder=\\'0\\' class=\\'' + KCONTENTFRAME + '\\' ' + 'src=\\'#= content.url #\\'>' + 'This page requires frames in order to show content' + '</iframe>'), NS = '.kendoTooltip', POSITIONS = {\n                bottom: {\n                    origin: 'bottom center',\n                    position: 'top center'\n                },\n                top: {\n                    origin: 'top center',\n                    position: 'bottom center'\n                },\n                left: {\n                    origin: 'center left',\n                    position: 'center right',\n                    collision: 'fit flip'\n                },\n                right: {\n                    origin: 'center right',\n                    position: 'center left',\n                    collision: 'fit flip'\n                },\n                center: {\n                    position: 'center center',\n                    origin: 'center center'\n                }\n            }, REVERSE = {\n                'top': 'bottom',\n                'bottom': 'top',\n                'left': 'right',\n                'right': 'left',\n                'center': 'center'\n            }, DIRCLASSES = {\n                bottom: 'n',\n                top: 's',\n                left: 'e',\n                right: 'w',\n                center: 'n'\n            }, DIMENSIONS = {\n                'horizontal': {\n                    offset: 'top',\n                    size: 'outerHeight'\n                },\n                'vertical': {\n                    offset: 'left',\n                    size: 'outerWidth'\n                }\n            }, DEFAULTCONTENT = function (e) {\n                return e.target.data(kendo.ns + 'title');\n            };\n        function restoreTitle(element) {\n            while (element.length) {\n                if (restoreTitleAttributeForElement(element)) {\n                    break;\n                }\n                element = element.parent();\n            }\n        }\n        function restoreTitleAttributeForElement(element) {\n            var title = element.data(kendo.ns + 'title');\n            if (title) {\n                element.attr('title', title);\n                element.removeData(kendo.ns + 'title');\n                return true;\n            }\n        }\n        function saveTitleAttributeForElement(element) {\n            var title = element.attr('title');\n            if (title) {\n                element.data(kendo.ns + 'title', title);\n                element.attr('title', '');\n                return true;\n            }\n        }\n        function saveTitleAttributes(element) {\n            while (element.length && !element.is('body')) {\n                if (saveTitleAttributeForElement(element)) {\n                    break;\n                }\n                element = element.parent();\n            }\n        }\n        var Tooltip = Widget.extend({\n            init: function (element, options) {\n                var that = this, axis;\n                Widget.fn.init.call(that, element, options);\n                axis = that.options.position.match(/left|right/) ? 'horizontal' : 'vertical';\n                that.dimensions = DIMENSIONS[axis];\n                that._documentKeyDownHandler = proxy(that._documentKeyDown, that);\n                that.element.on(that.options.showOn + NS, that.options.filter, proxy(that._showOn, that));\n                if (this._isShownOnMouseEnter() || this._isShownOnClick()) {\n                    that.element.on('mouseenter' + NS, that.options.filter, proxy(that._mouseenter, that));\n                }\n                if (this.options.autoHide && this._isShownOnMouseEnter()) {\n                    that.element.on('mouseleave' + NS, that.options.filter, proxy(that._mouseleave, that));\n                }\n                if (this.options.autoHide && this._isShownOnFocus()) {\n                    that.element.on('blur' + NS, that.options.filter, proxy(that._blur, that));\n                }\n            },\n            options: {\n                name: 'Tooltip',\n                filter: '',\n                content: DEFAULTCONTENT,\n                showAfter: 100,\n                hideAfter: 100,\n                callout: true,\n                offset: 0,\n                position: 'bottom',\n                showOn: 'mouseenter',\n                autoHide: true,\n                width: null,\n                height: null,\n                animation: {\n                    open: {\n                        effects: 'fade:in',\n                        duration: 0\n                    },\n                    close: {\n                        duration: 40,\n                        hide: true\n                    }\n                }\n            },\n            events: [\n                SHOW,\n                HIDE,\n                CONTENTLOAD,\n                ERROR,\n                REQUESTSTART\n            ],\n            _isShownOnFocus: function () {\n                return this.options.showOn && this.options.showOn.match(/focus/);\n            },\n            _isShownOnMouseEnter: function () {\n                return this.options.showOn && this.options.showOn.match(/mouseenter/);\n            },\n            _isShownOnClick: function () {\n                return this.options.showOn && this.options.showOn.match(/click/);\n            },\n            _mouseenter: function (e) {\n                saveTitleAttributes($(e.currentTarget));\n            },\n            _showOn: function (e) {\n                var that = this;\n                var currentTarget = $(e.currentTarget);\n                if (that._isShownOnClick() && !that._isShownOnMouseEnter()) {\n                    that._show(currentTarget);\n                } else if (that._isShownOnFocus()) {\n                    saveTitleAttributes(currentTarget);\n                    that._show(currentTarget);\n                } else {\n                    clearTimeout(that.timeout);\n                    that.timeout = setTimeout(function () {\n                        that._show(currentTarget);\n                    }, that.options.showAfter);\n                }\n            },\n            _appendContent: function (target) {\n                var that = this, contentOptions = that.options.content, element = that.content, showIframe = that.options.iframe, iframe;\n                if (isPlainObject(contentOptions) && contentOptions.url) {\n                    if (!('iframe' in that.options)) {\n                        showIframe = !isLocalUrl(contentOptions.url);\n                    }\n                    that.trigger(REQUESTSTART, {\n                        options: contentOptions,\n                        target: target\n                    });\n                    if (!showIframe) {\n                        element.empty();\n                        kendo.ui.progress(element, true);\n                        that._ajaxRequest(contentOptions);\n                    } else {\n                        element.hide();\n                        iframe = element.find('.' + KCONTENTFRAME)[0];\n                        if (iframe) {\n                            iframe.src = contentOptions.url || iframe.src;\n                        } else {\n                            element.html(IFRAMETEMPLATE({ content: contentOptions }));\n                        }\n                        element.find('.' + KCONTENTFRAME).off('load' + NS).on('load' + NS, function () {\n                            that.trigger(CONTENTLOAD);\n                            element.show();\n                        });\n                    }\n                } else if (contentOptions && isFunction(contentOptions)) {\n                    contentOptions = contentOptions({\n                        sender: this,\n                        target: target\n                    });\n                    element.html(contentOptions || '');\n                } else {\n                    element.html(contentOptions);\n                }\n                that.angular('compile', function () {\n                    return { elements: element };\n                });\n            },\n            _ajaxRequest: function (options) {\n                var that = this;\n                jQuery.ajax(extend({\n                    type: 'GET',\n                    dataType: 'html',\n                    cache: false,\n                    error: function (xhr, status) {\n                        kendo.ui.progress(that.content, false);\n                        that.trigger(ERROR, {\n                            status: status,\n                            xhr: xhr\n                        });\n                    },\n                    success: proxy(function (data) {\n                        kendo.ui.progress(that.content, false);\n                        that.content.html(data);\n                        that.trigger(CONTENTLOAD);\n                    }, that)\n                }, options));\n            },\n            _documentKeyDown: function (e) {\n                if (e.keyCode === kendo.keys.ESC) {\n                    this.hide();\n                }\n            },\n            refresh: function () {\n                var that = this, popup = that.popup;\n                if (popup && popup.options.anchor) {\n                    that._appendContent(popup.options.anchor);\n                }\n            },\n            hide: function () {\n                if (this.popup) {\n                    this.popup.close();\n                }\n            },\n            show: function (target) {\n                target = target || this.element;\n                saveTitleAttributes(target);\n                this._show(target);\n            },\n            _show: function (target) {\n                var that = this, current = that.target();\n                if (!that.popup) {\n                    that._initPopup();\n                }\n                if (current && current[0] != target[0]) {\n                    that.popup.close();\n                    that.popup.element.kendoStop(true, true);\n                }\n                if (!current || current[0] != target[0]) {\n                    that._appendContent(target);\n                    that.popup.options.anchor = target;\n                }\n                that.popup.one('deactivate', function () {\n                    restoreTitle(target);\n                    target.removeAttr(DESCRIBEDBY);\n                    this.element.removeAttr('id').attr('aria-hidden', true);\n                    DOCUMENT.off('keydown' + NS, that._documentKeyDownHandler);\n                });\n                that.popup._hovered = true;\n                that.popup.open();\n            },\n            _initPopup: function () {\n                var that = this, options = that.options, wrapper = $(kendo.template(TEMPLATE)({\n                        callout: options.callout && options.position !== 'center',\n                        dir: DIRCLASSES[options.position],\n                        autoHide: options.autoHide\n                    }));\n                that.popup = new Popup(wrapper, extend({\n                    autosize: true,\n                    activate: function () {\n                        var anchor = this.options.anchor, ariaId = anchor[0].id || that.element[0].id;\n                        if (ariaId) {\n                            anchor.attr(DESCRIBEDBY, ariaId + ARIAIDSUFFIX);\n                            this.element.attr('id', ariaId + ARIAIDSUFFIX);\n                        }\n                        if (options.callout) {\n                            that._positionCallout();\n                        } else {\n                            that._offset(that.options.position, that.options.offset);\n                        }\n                        this.element.removeAttr('aria-hidden');\n                        DOCUMENT.on('keydown' + NS, that._documentKeyDownHandler);\n                        that.trigger(SHOW);\n                        that.popup._hovered = undefined;\n                    },\n                    close: function () {\n                        that.trigger(HIDE);\n                    },\n                    copyAnchorStyles: false,\n                    animation: options.animation\n                }, POSITIONS[options.position]));\n                wrapper.css({\n                    width: options.width,\n                    height: options.height\n                });\n                that.content = wrapper.find('.k-tooltip-content');\n                that.arrow = wrapper.find('.k-callout');\n                if (options.autoHide && this._isShownOnMouseEnter()) {\n                    wrapper.on('mouseleave' + NS, proxy(that._mouseleave, that));\n                } else {\n                    wrapper.on('click' + NS, '.k-tooltip-button', proxy(that._closeButtonClick, that));\n                }\n            },\n            _closeButtonClick: function (e) {\n                e.preventDefault();\n                this.hide();\n            },\n            _mouseleave: function (e) {\n                var that = this;\n                clearTimeout(that.timeout);\n                that.timeout = setTimeout(function () {\n                    that._closePopup(e.currentTarget);\n                }, that.options.hideAfter);\n            },\n            _blur: function (e) {\n                this._closePopup(e.currentTarget);\n            },\n            _closePopup: function (target) {\n                if (this.popup && !this.popup._hovered) {\n                    this.popup.close();\n                } else {\n                    restoreTitle($(target));\n                }\n            },\n            target: function () {\n                if (this.popup) {\n                    return this.popup.options.anchor;\n                }\n                return null;\n            },\n            _positionCallout: function () {\n                var that = this, position = that.options.position, dimensions = that.dimensions, offset = dimensions.offset, popup = that.popup, anchor = popup.options.anchor, anchorOffset = $(anchor).offset(), elementOffset = $(popup.element).offset(), cssClass = DIRCLASSES[popup.flipped ? REVERSE[position] : position], offsetAmount = anchorOffset[offset] - elementOffset[offset] + $(anchor)[dimensions.size]() / 2;\n                that._offset(position, that.options.offset);\n                that.arrow.removeClass('k-callout-n k-callout-s k-callout-w k-callout-e').addClass('k-callout-' + cssClass).css(offset, offsetAmount);\n            },\n            destroy: function () {\n                var popup = this.popup;\n                if (popup) {\n                    popup.element.off(NS);\n                    popup.destroy();\n                }\n                clearTimeout(this.timeout);\n                this.element.off(NS);\n                DOCUMENT.off('keydown' + NS, this._documentKeyDownHandler);\n                Widget.fn.destroy.call(this);\n            },\n            _offset: function (position, offsetAmount) {\n                var that = this, isTopLeft = position == 'top' || position == 'left', isFlipped = that.popup.flipped, direction = isTopLeft && isFlipped || !isTopLeft && !isFlipped ? 1 : -1, marginRule = isTopLeft ? 'margin-' + position : 'margin-' + REVERSE[position], offset = kendo._outerWidth(that.arrow) / 2 + offsetAmount;\n                that.popup.wrapper.css(marginRule, offset * direction + 'px');\n            }\n        });\n        kendo.ui.plugin(Tooltip);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}