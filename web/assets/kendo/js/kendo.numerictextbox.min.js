/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.numerictextbox.min",["kendo.core.min","kendo.userevents.min"],e)}(function(){return function(e,t){function n(e,t){var n="k-i-arrow-"+("increase"===e?"60-up":"60-down");return'<span unselectable="on" class="k-link k-link-'+e+'" aria-label="'+t+'" title="'+t+'"><span unselectable="on" class="'+H+" "+n+'"></span></span>'}function a(e,t){var n=(""+parseFloat(e,10)).split(E);return n[1]&&(n[1]=n[1].substring(0,t)),n.join(E)}var r=window.kendo,o=r.caret,i=r.keys,s=r.ui,l=s.Widget,u=r._activeElement,p=r._extractFormat,d=r.parseFloat,c=r.support.placeholder,_=r.getCulture,f="change",m="disabled",v="readonly",x="k-input",g="spin",h=".kendoNumericTextBox",w="touchend",y="mouseleave"+h,k="mouseenter"+h+" "+y,b="k-state-default",A="k-state-focused",C="k-state-hover",T="focus",E=".",H="k-icon",W="k-state-selected",O="k-state-disabled",D="k-state-invalid",I="aria-disabled",R=/^(-)?(\d*)$/,F=null,S=e.proxy,N=e.extend,j=l.extend({init:function(n,a){var o,i,s,u,d,c,_=this,f=a&&a.step!==t;l.fn.init.call(_,n,a),a=_.options,n=_.element.on("focusout"+h,S(_._focusout,_)).attr("role","spinbutton"),a.placeholder=a.placeholder||n.attr("placeholder"),o=_.min(n.attr("min")),i=_.max(n.attr("max")),s=_._parse(n.attr("step")),a.min===F&&o!==F&&(a.min=o),a.max===F&&i!==F&&(a.max=i),f||s===F||(a.step=s),_._initialOptions=N({},a),c=n.attr("type"),_._reset(),_._wrapper(),_._arrows(),_._validation(),_._input(),r.support.mobileOS?_._text.on(w+h+" "+T+h,function(){r.support.browser.edge?_._text.one(T+h,function(){_._toggleText(!1),n.focus()}):(_._toggleText(!1),n.focus())}):_._text.on(T+h,S(_._click,_)),n.attr("aria-valuemin",a.min!==F?a.min*a.factor:a.min).attr("aria-valuemax",a.max!==F?a.max*a.factor:a.max),a.format=p(a.format),u=a.value,u==F&&(u="number"==c?parseFloat(n.val()):n.val()),_.value(u),d=n.is("[disabled]")||e(_.element).parents("fieldset").is(":disabled"),d?_.enable(!1):_.readonly(n.is("[readonly]")),_.angular("compile",function(){return{elements:_._text.get()}}),r.notify(_)},options:{name:"NumericTextBox",decimals:F,restrictDecimals:!1,min:F,max:F,value:F,step:1,round:!0,culture:"",format:"n",spinners:!0,placeholder:"",factor:1,upArrowText:"Increase value",downArrowText:"Decrease value"},events:[f,g],_editable:function(e){var t=this,n=t.element,a=e.disable,r=e.readonly,o=t._text.add(n),i=t._inputWrapper.off(k);t._toggleText(!0),t._upArrowEventHandler.unbind("press"),t._downArrowEventHandler.unbind("press"),n.off("keydown"+h).off("keypress"+h).off("keyup"+h).off("paste"+h),r||a?(i.addClass(a?O:b).removeClass(a?b:O),o.attr(m,a).attr(v,r).attr(I,a)):(i.addClass(b).removeClass(O).on(k,t._toggleHover),o.removeAttr(m).removeAttr(v).attr(I,!1),t._upArrowEventHandler.bind("press",function(e){e.preventDefault(),t._spin(1),t._upArrow.addClass(W)}),t._downArrowEventHandler.bind("press",function(e){e.preventDefault(),t._spin(-1),t._downArrow.addClass(W)}),t.element.on("keydown"+h,S(t._keydown,t)).on("keypress"+h,S(t._keypress,t)).on("keyup"+h,S(t._keyup,t)).on("paste"+h,S(t._paste,t)))},readonly:function(e){this._editable({readonly:e===t||e,disable:!1})},enable:function(e){this._editable({readonly:!1,disable:!(e=e===t||e)})},setOptions:function(e){var n=this;l.fn.setOptions.call(n,e),n._arrowsWrap.toggle(n.options.spinners),n._inputWrapper.toggleClass("k-expand-padding",!n.options.spinners),n._text.prop("placeholder",n.options.placeholder),n._placeholder(n.options.placeholder),n.element.attr({"aria-valuemin":n.options.min!==F?n.options.min*n.options.factor:n.options.min,"aria-valuemax":n.options.max!==F?n.options.max*n.options.factor:n.options.max}),n.options.format=p(n.options.format),e.value!==t&&n.value(e.value)},destroy:function(){var e=this;e.element.add(e._text).add(e._upArrow).add(e._downArrow).add(e._inputWrapper).off(h),e._upArrowEventHandler.destroy(),e._downArrowEventHandler.destroy(),e._form&&e._form.off("reset",e._resetHandler),l.fn.destroy.call(e)},min:function(e){return this._option("min",e)},max:function(e){return this._option("max",e)},step:function(e){return this._option("step",e)},value:function(e){var n,a=this;return e===t?a._value:(e=a._parse(e),n=a._adjust(e),e===n&&(a._update(e),a._old=a._value),t)},focus:function(){this._focusin()},_adjust:function(e){var t=this,n=t.options,a=n.min,r=n.max;return e===F?e:(a!==F&&e<a?e=a:r!==F&&e>r&&(e=r),e)},_arrows:function(){var t,a=this,o=function(){clearTimeout(a._spinning),t.removeClass(W)},i=a.options,s=i.spinners,l=a.element;t=l.siblings("."+H),t[0]||(t=e(n("increase",i.upArrowText)+n("decrease",i.downArrowText)).insertAfter(l),a._arrowsWrap=t.wrapAll('<span class="k-select"/>').parent()),s||(t.parent().toggle(s),a._inputWrapper.addClass("k-expand-padding")),a._upArrow=t.eq(0),a._upArrowEventHandler=new r.UserEvents(a._upArrow,{release:o}),a._downArrow=t.eq(1),a._downArrowEventHandler=new r.UserEvents(a._downArrow,{release:o})},_validation:function(){var t=this,n=t.element;t._validationIcon=e("<span class='"+H+" k-i-warning'></span>").hide().insertAfter(n)},_blur:function(){var e=this;e._toggleText(!0),e._change(e.element.val())},_click:function(e){var t=this;clearTimeout(t._focusing),t._focusing=setTimeout(function(){var n,a,r,i=e.target,s=o(i)[0],l=i.value.substring(0,s),u=t._format(t.options.format),p=u[","],d=0;p&&(a=RegExp("\\"+p,"g"),r=RegExp("(^(-)$)|(^(-)?([\\d\\"+p+"]+)(\\"+u[E]+")?(\\d+)?)")),r&&(n=r.exec(l)),n&&(d=n[0].replace(a,"").length,l.indexOf("(")!=-1&&t._value<0&&d++),t._focusin(),o(t.element[0],d)})},_change:function(e){var t=this,n=t.options.factor;n&&1!==n&&(e=r.parseFloat(e),null!==e&&(e/=n)),t._update(e),e=t._value,t._old!=e&&(t._old=e,t._typing||t.element.trigger(f),t.trigger(f)),t._typing=!1},_culture:function(e){return e||_(this.options.culture)},_focusin:function(){var e=this;e._inputWrapper.addClass(A),e._toggleText(!1),e.element[0].focus()},_focusout:function(){var e=this;clearTimeout(e._focusing),e._inputWrapper.removeClass(A).removeClass(C),e._blur(),e._removeInvalidState()},_format:function(e,t){var n=this._culture(t).numberFormat;return e=e.toLowerCase(),e.indexOf("c")>-1?n=n.currency:e.indexOf("p")>-1&&(n=n.percent),n},_input:function(){var t,n=this,a=n.options,r="k-formatted-value",o=n.element.addClass(x).show()[0],i=o.accessKey,s=n.wrapper;t=s.find(E+r),t[0]||(t=e('<input type="text"/>').insertBefore(o).addClass(r));try{o.setAttribute("type","text")}catch(l){o.type="text"}t[0].title=o.title,t[0].tabIndex=o.tabIndex,t[0].style.cssText=o.style.cssText,t.prop("placeholder",a.placeholder),i&&(t.attr("accesskey",i),o.accessKey=""),n._text=t.addClass(o.className).attr({role:"spinbutton","aria-valuemin":a.min!==F?a.min*a.factor:a.min,"aria-valuemax":a.max!==F?a.max*a.factor:a.max,autocomplete:"off"})},_keydown:function(e){var t=this,n=e.keyCode;t._key=n,n==i.DOWN?t._step(-1):n==i.UP?t._step(1):n==i.ENTER?t._change(t.element.val()):n!=i.TAB&&(t._typing=!0)},_keypress:function(e){var t,n,a,r,s,l,u,p,d,c,_;0===e.which||e.metaKey||e.ctrlKey||e.keyCode===i.BACKSPACE||e.keyCode===i.ENTER||(t=this,n=t.options.min,a=t.element,r=o(a),s=r[0],l=r[1],u=String.fromCharCode(e.which),p=t._format(t.options.format),d=t._key===i.NUMPAD_DOT,c=a.val(),d&&(u=p[E]),c=c.substring(0,s)+u+c.substring(l),_=t._numericRegex(p).test(c),_&&d?(a.val(c),o(a,s+u.length),e.preventDefault()):(null!==n&&n>=0&&"-"===c.charAt(0)||!_)&&(t._addInvalidState(),e.preventDefault()),t._key=0)},_keyup:function(){this._removeInvalidState()},_addInvalidState:function(){var e=this;e._inputWrapper.addClass(D),e._validationIcon.show()},_removeInvalidState:function(){var e=this;e._inputWrapper.removeClass(D),e._validationIcon.hide()},_numericRegex:function(e){var t=this,n=e[E],a=t.options.decimals,r="*";return n===E&&(n="\\"+n),a===F&&(a=e.decimals),0===a&&t.options.restrictDecimals?R:(t.options.restrictDecimals&&(r="{0,"+a+"}"),t._separator!==n&&(t._separator=n,t._floatRegExp=RegExp("^(-)?(((\\d+("+n+"\\d"+r+")?)|("+n+"\\d"+r+")))?$")),t._floatRegExp)},_paste:function(e){var t=this,n=e.target,a=n.value,r=t._format(t.options.format);setTimeout(function(){var e=t._parse(n.value);e===F?t._update(a):(n.value=(""+e).replace(E,r[E]),t._adjust(e)===e&&t._numericRegex(r).test(n.value)||t._update(a))})},_option:function(e,n){var a=this,r=a.element,o=a.options;return n===t?o[e]:(n=a._parse(n),(n||"step"!==e)&&(o[e]=n,r.add(a._text).attr("aria-value"+e,n),r.attr(e,n)),t)},_spin:function(e,t){var n=this;t=t||500,clearTimeout(n._spinning),n._spinning=setTimeout(function(){n._spin(e,50)},t),n._step(e)},_step:function(e){var t=this,n=t.element,a=t._value,r=t._parse(n.val())||0,o=t.options.decimals||2;u()!=n[0]&&t._focusin(),t.options.factor&&r&&(r/=t.options.factor),r=+(r+t.options.step*e).toFixed(o),r=t._adjust(r),t._update(r),t._typing=!1,a!==r&&t.trigger(g)},_toggleHover:function(t){e(t.currentTarget).toggleClass(C,"mouseenter"===t.type)},_toggleText:function(e){var t=this;t._text.toggle(e),t.element.toggle(!e)},_parse:function(e,t){return d(e,this._culture(t),this.options.format)},_round:function(e,t){var n=this.options.round?r._round:a;return n(e,t)},_update:function(e){var t,n=this,a=n.options,o=a.factor,i=a.format,s=a.decimals,l=n._culture(),u=n._format(i,l);s===F&&(s=u.decimals),e=n._parse(e,l),t=e!==F,t&&(e=parseFloat(n._round(e,s),10)),n._value=e=n._adjust(e),n._placeholder(r.toString(e,i,l)),t?(o&&(e=parseFloat(n._round(e*o,s),10)),e=""+e,e.indexOf("e")!==-1&&(e=n._round(+e,s)),e=e.replace(E,u[E])):e=null,n.element.val(e),n.element.add(n._text).attr("aria-valuenow",e)},_placeholder:function(e){var t=this._text;t.val(e),c||e||t.val(this.options.placeholder),t.attr("title",this.element.attr("title")||t.val())},_wrapper:function(){var t,n=this,a=n.element,r=a[0];t=a.parents(".k-numerictextbox"),t.is("span.k-numerictextbox")||(t=a.hide().wrap('<span class="k-numeric-wrap k-state-default" />').parent(),t=t.wrap("<span/>").parent()),t[0].style.cssText=r.style.cssText,r.style.width="",n.wrapper=t.addClass("k-widget k-numerictextbox").addClass(r.className).css("display",""),n._inputWrapper=e(t[0].firstChild)},_reset:function(){var t=this,n=t.element,a=n.attr("form"),r=a?e("#"+a):n.closest("form");r[0]&&(t._resetHandler=function(){setTimeout(function(){t.value(n[0].value),t.max(t._initialOptions.max),t.min(t._initialOptions.min)})},t._form=r.on("reset",t._resetHandler))}});s.plugin(j)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.numerictextbox.min.js.map
