/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.scrollview.min",["kendo.fx.min","kendo.data.min","kendo.draganddrop.min"],e)}(function(){return function(e,t){function i(e){return"k-"+e}var n,a,s,o,r,h,g,l,c,p=window.kendo,d=p.ui,u=e.proxy,f=p.effects.Transition,v=p.ui.Pane,m=p.ui.PaneDimensions,w=d.DataBoundWidget,_=p.data.DataSource,P=Math,S=P.abs,x=P.ceil,y=P.round,b=P.max,R=P.min,T=P.floor,k="change",C="click",z="changing",V="refresh",E="primary",O="scrollview-page",D="function",q="itemChange",H="cleanup",M=3,A=-1,B=0,N=1,F=-1,W=0,I=1,L=p.Observable.extend({init:function(e){var t=this;this.dataSource=e,this.pendingRequestArray=[],this.initialFetch=!1,this.useRanges=e.options.serverPaging,p.Observable.fn.init.call(this),e.bind("change",function(){t._change()})},_change:function(){this.trigger("reset",{offset:this.offset})},page:function(e,t){var i=this;this.useRanges||(this.dataSource.page(e+1),t?t(i.dataSource.view()):i.trigger("page",{page:e})),this.useRanges&&this.dataSource.range(e*this.dataSource.pageSize(),this.dataSource.pageSize(),function(){t?t(i.dataSource.view()):i.trigger("page",{page:e})})},scrollTo:function(e){var t=Math.ceil(this.dataSource.total()/this.dataSource.pageSize()||1),i=e-1,n=i-1,a=e,s=t>0&&e+1>=t?-1:e+1,o=t>0&&s+1>=t?-1:s+1;s>=0&&this.pendingRequestArray.push(s),i>=0&&this.pendingRequestArray.push(i),n>=0&&this.pendingRequestArray.push(n),o>=0&&this.pendingRequestArray.push(o),this.page(a)},getViewData:function(){var e,t,i=this.dataSource.view();if(this.dataSource.options.pageSize>1)for(e=[],t=0;t<i.length;t++)e.push(i[t]);else e=i[0];return e},destroy:function(){var e=this;e.dataSource.unbind(),e.dataSource=null}});p.ui.ScrollViewDataReader=L,n=p.Class.extend({init:function(t){var n=this,a=e("<ul class='"+i("scrollview-nav")+"'/>"),s=e("<div class='"+i("scrollview-nav-wrap")+"'></div>");s.append(a),t._navigationContainer.append(s),this._changeProxy=u(n,"_change"),this._refreshProxy=u(n,"_refresh"),t.bind(k,this._changeProxy),t.bind(V,this._refreshProxy),a.on(C,"li.k-link",u(this._click,t)),e.extend(n,{element:a,scrollView:t})},items:function(){return this.element.children()},_refresh:function(e){var t,n="";for(t=0;t<e.pageCount;t++)n+='<li class="k-link"></li>';this.element.html(n),this.items().eq(e.page).addClass(i(E)),this.scrollView._toggleNavigation({currentPage:e.page})},_change:function(e){var t,n,a,s,o,r;e.isDefaultPrevented()||(t=this.scrollView._navigationContainer.find(".k-scrollview-nav"),n=this.scrollView.element.width(),a=(n-t.width())/2,s=t.find("li.k-link:eq(0)").outerWidth(!0)/2,this.items().removeClass(i(E)).eq(e.nextPage).addClass(i(E)),o=this.items().eq(e.nextPage).length>0?this.items().eq(e.nextPage).position().left:0,(o>n/2||o<t.scrollLeft()+n/2)&&(r=0,r=o>n/2?t.scrollLeft()+o-n/2:t.scrollLeft()-(n/2-o),r+=a+s,t.animate({scrollLeft:r},300)),this.scrollView._toggleNavigation({currentPage:e.currentPage,nextPage:e.nextPage}))},_click:function(t){var i=e(t.currentTarget).index();this.scrollTo(i)},destroy:function(){this.scrollView.unbind(k,this._changeProxy),this.scrollView.unbind(V,this._refreshProxy),this.element.off(C),this.element.remove()}}),p.ui.ScrollViewPager=n,a="transitionEnd",s="dragStart",o="dragEnd",r=p.Observable.extend({init:function(t,i){var n,r,h,g,l,c,d=this;p.Observable.fn.init.call(this),this.element=t,this.container=t.parent(),n=new p.ui.Movable(d.element),r=new f({axis:"x",movable:n,onEnd:function(){d.trigger(a)}}),h=new p.UserEvents(t,{fastTap:!0,start:function(e){2*S(e.x.velocity)>=S(e.y.velocity)?h.capture():h.cancel(),d.trigger(s,e),r.cancel()},allowSelection:!0,end:function(e){d.trigger(o,e)}}),g=new m({element:d.element,container:d.container}),l=g.x,l.bind(k,function(){d.trigger(k)}),c=new v({dimensions:g,userEvents:h,movable:n,elastic:!0}),e.extend(d,{duration:i&&i.duration||1,movable:n,transition:r,userEvents:h,dimensions:g,dimension:l,pane:c}),this.bind([a,s,o,k],i)},size:function(){return{width:this.dimensions.x.getSize(),height:this.dimensions.y.getSize()}},total:function(){return this.dimension.getTotal()},offset:function(){return-this.movable.x},updateDimension:function(){this.dimension.update(!0)},refresh:function(){this.dimensions.refresh(),this.dimensions.y.enabled=!1},moveTo:function(e){this.movable.moveAxis("x",-e)},transitionTo:function(e,t,i){i?this.moveTo(-e):this.transition.moveTo({location:e,duration:this.duration,ease:t})},destroy:function(){var e=this;e.userEvents.destroy(),e.unbind(),e.movable=e.tansition=e.dimensions=e.dimension=e.pane=null,e.element.remove()}}),p.ui.ScrollViewElasticPane=r,h=p.Observable.extend({init:function(e,t,i){var n=this;p.Observable.fn.init.call(this),n.element=e,n.pane=t,n._getPages(),this.page=0,this.pageSize=i.pageSize||1,this.contentHeight=i.contentHeight,this.enablePager=i.enablePager,this.pagerOverlay=i.pagerOverlay},scrollTo:function(e,i){var n=this;(e!=n.page||i)&&(n.trigger("resize",{currentPage:this.page,nextPage:e,data:t})||(n.page=e,n.pane.transitionTo(-e*n.pane.size().width,f.easeOutExpo,i)))},paneMoved:function(e,i,n,a){var s,o,r,h=this,g=h.pane,l=g.size().width*h.pageSize,c=y,p=i?f.easeOutBack:f.easeOutExpo;return e===F?c=x:e===I&&(c=T),o=c(g.offset()/l),o<0||o>=h.pageCount?(r=o<0?0:-this.page*this.pane.size().width,this.pane.transitionTo(r,p,a)):(s=b(h.minSnap,R(-o*l,h.maxSnap)),o!=h.page&&n&&n({currentPage:h.page,nextPage:o})&&(s=-h.page*g.size().width),g.transitionTo(s,p,a),t)},updatePage:function(){var e=this.pane,t=y(e.offset()/e.size().width);return t!=this.page&&(this.page=t,!0)},forcePageUpdate:function(){return this.updatePage()},resizeTo:function(e){var t,i,n=this.pane,a=e.width;this.pageElements.width(a),"100%"===this.contentHeight&&(t=this.element.parent().height(),this.enablePager===!0&&(i=this.element.parent().find("ul.k-scrollview-nav"),!this.pagerOverlay&&i.length&&(t-=p._outerHeight(i,!0))),this.element.css("height",t),this.pageElements.css("height",t)),n.updateDimension(),this._paged||(this.page=T(n.offset()/a)),this.scrollTo(this.page,!0,!0),this.pageCount=T(n.total()/a),this.minSnap=-(this.pageCount-1)*a,this.maxSnap=0},_getPages:function(){this.pageElements=this.element.find(p.roleSelector("page")),this._paged=this.pageElements.length>0},destroy:function(){var e=this;e.pane=null,e.element.remove()}}),p.ui.ScrollViewContent=h,g=p.Observable.extend({init:function(e,t,i){var n=this;p.Observable.fn.init.call(this),n.element=e,n.pane=t,n.options=i,n._templates(),n.page=i.page||0,n.pages=[],n._initPages(),n.resizeTo(n.pane.size()),n.pane.dimension.forceEnabled()},setDataSource:function(e){this.dataSource=_.create(e),this._dataReader(),this._pendingPageRefresh=!1,this._pendingWidgetRefresh=!1},_viewShow:function(){var e=this;e._pendingWidgetRefresh&&(setTimeout(function(){e._resetPages()},0),e._pendingWidgetRefresh=!1)},_dataReader:function(){this.dataReader=new L(this.dataSource),this._pageProxy=u(this,"_onPage"),this._resetProxy=u(this,"_onReset"),this.dataReader.bind({page:this._pageProxy,reset:this._resetProxy})},_templates:function(){var e=this.options.template,t=this.options.emptyTemplate,i={},n={};typeof e===D&&(i.template=e,e="#=this.template(data)#"),this.template=u(p.template(e),i),typeof t===D&&(n.emptyTemplate=t,t="#=this.emptyTemplate(data)#"),this.emptyTemplate=u(p.template(t),n)},_initPages:function(){var e,t,i=this.pages,n=this.element;for(t=0;t<M;t++)e=new l(n),i.push(e);this.pane.updateDimension()},resizeTo:function(e){var t,i,n,a=this.pages,s=this.pane;for(t=0;t<a.length;t++)a[t].setWidth(e.width);"auto"===this.options.contentHeight?this.element.css("height",this.pages[1].element.height()):"100%"===this.options.contentHeight?(i=this.element.parent().height(),this.options.enablePager===!0&&(n=this.element.parent().find("ul.k-scrollview-nav"),!this.options.pagerOverlay&&n.length&&(i-=p._outerHeight(n,!0))),this.element.css("height",i),a[0].element.css("height",i),a[1].element.css("height",i),a[2].element.css("height",i)):this.options.contentHeight&&(a[0].element.css("height",this.options.contentHeight),a[1].element.css("height",this.options.contentHeight),a[2].element.css("height",this.options.contentHeight)),s.updateDimension(),this._repositionPages(),this.width=e.width},scrollTo:function(e,i,n){var a=this,s=a.dataReader;(e!=a.page||i)&&s.page(e,function(o){return n?(s.scrollTo(e),t):(a.trigger("resize",{currentPage:a.page,nextPage:e,data:o})||(i?a.page=e:(s.pagerScroll=e>a.page?-1:1,a.page=e+s.pagerScroll),s.scrollTo(e)),t)})},paneMoved:function(e,i,n,a){var s,o,r,h=this,g=h.pane,l=g.size().width,c=g.offset(),d=Math.abs(c)>=l/3,u=i?p.effects.Transition.easeOutBack:p.effects.Transition.easeOutExpo,f=!!h.dataSource.options.serverPaging&&h.page+2>h.pageCount,v=0;e===I?0!==h.page&&(v=-1):e!==F||f?c>0&&d&&!f?v=1:c<0&&d&&0!==h.page&&(v=-1):v=1,s=h.page,v&&(s=v>0?s+1:s-1,h instanceof p.ui.VirtualScrollViewContent?(h.dataReader.page(s),o=h.dataReader.getViewData()):o=t,o instanceof Array||(o=[o]),r=h.pages?h.pages[1].element:t),n&&h.page!=s&&n({currentPage:h.page,nextPage:s,element:r,data:o})&&(v=0),0===v?h._cancelMove(u,a):v===-1?h._moveBackward(a):1===v&&h._moveForward(a)},updatePage:function(){var e=this.pages;return 0!==this.pane.offset()&&(this.pane.offset()>0?(e.push(this.pages.shift()),this.page++,this.page+2<this.pageCount&&this.dataReader.pendingRequestArray.push(this.page+2),this.page+1<this.pageCount&&this.dataReader.page(this.page+1),this.page+1==this.pageCount&&this.setPageContent(this.pages[2],null)):(e.unshift(this.pages.pop()),this.page--,this.page-2>=0&&this.dataReader.pendingRequestArray.push(this.page-2),this.page-1>=0&&this.dataReader.page(this.page-1)),this._repositionPages(),this._resetMovable(),!0)},forcePageUpdate:function(){var e=this.pane.offset(),t=3*this.pane.size().width/4;return S(e)>t&&this.updatePage()},_resetMovable:function(){this.pane.moveTo(0)},_moveForward:function(e){this.pane.transitionTo(-this.width,p.effects.Transition.easeOutExpo,e)},_moveBackward:function(e){this.pane.transitionTo(this.width,p.effects.Transition.easeOutExpo,e)},_cancelMove:function(e,t){this.pane.transitionTo(0,e,t)},_resetPages:function(){this.page=this.options.page||0,this._repositionPages(),this.trigger("reset")},_onPage:function(e){if(e.page>=this.pageCount&&this.setPageContent(this.pages[2],null),this.page==e.page?!this.dataReader.pagerScroll||0===this.dataReader.pagerScroll&&this.dataReader.initialFetch?this.setPageContent(this.pages[1],this.dataReader.getViewData()):(this.dataReader.pagerScroll<0?this._moveForward():this._moveBackward(),this.dataReader.pagerScroll=0,this.setPageContent(this.pages[1],this.dataReader.getViewData())):this.page+1==e.page?this.setPageContent(this.pages[2],this.dataReader.getViewData()):this.page-1==e.page&&this.setPageContent(this.pages[0],this.dataReader.getViewData()),this.dataReader.pendingRequestArray.length>0&&this.dataReader.initialFetch){var t=this.dataReader.pendingRequestArray.shift();this.dataReader.page(t)}},_onReset:function(){this.pageCount=x(this.dataSource.total()/this.dataSource.pageSize())},_repositionPages:function(){var e=this.pages;e[0].position(A),e[1].position(B),e[2].position(N)},setPageContent:function(e,i){var n=this.template,a=this.emptyTemplate;e.content(null!==i&&i!==t?n(i):a({}))},destroy:function(){var e,t=this,i=t.pages;for(t.dataReader.unbind(),t.dataSource.unbind(),t.dataReader=t.dataSource=t.pane=null,e=0;e<i.length;e++)i[e].destroy();t.element.remove()}}),p.ui.VirtualScrollViewContent=g,l=p.Class.extend({init:function(t){this.element=e("<li class='"+i(O)+"'></li>"),this.width=t.width(),this.element.width(this.width),t.append(this.element)},content:function(e){this.element.html(e)},position:function(e){this.element.css("transform","translate3d("+this.width*e+"px, 0, 0)")},setWidth:function(e){this.width=e,this.element.width(e)},destroy:function(){var e=this;e.element.remove(),e.element=null}}),p.ui.VirtualPage=l,c=w.extend({init:function(e,t){var a,s,o=this;w.fn.init.call(o,e,t),t=o.options,e=o.element,p.stripWhitespace(e[0]),e.wrapInner(0===e.children().length?"<ul class='k-scrollview-wrap'/>":"<div class='k-scrollview-wrap'/>"),e.addClass("k-widget "+i("scrollview")),o._initNavigation(),this.options.enablePager?(this.pager=new n(this),this.options.pagerOverlay&&e.addClass(i("scrollview-overlay"))):(this._changeProxy=u(o,"_toggleNavigation"),this.bind(k,this._changeProxy)),o.inner=e.children().first(),o.page=0,o.inner.css("height",t.contentHeight),o.pane=new r(o.inner,{duration:this.options.duration,transitionEnd:u(this,"_transitionEnd"),dragStart:u(this,"_dragStart"),dragEnd:u(this,"_dragEnd"),change:u(this,V)}),o.bind("resize",function(){o.pane.refresh()}),o.page=t.page,a=0===this.inner.children().length,s=a?new g(o.inner,o.pane,t):new h(o.inner,o.pane,t),s.page=o.page,s.bind("reset",function(){this._pendingPageRefresh=!1,o.trigger(V,{pageCount:s.pageCount,page:s.page}),o._toggleNavigation({currentPage:s.page,nextPage:s.page})}),s.bind("resize",function(e){var t=s.page,i=e.nextPage;t!=i&&(e._defaultPrevented=o.trigger(k,{currentPage:s.page,nextPage:e.nextPage,data:e.data})),o._toggleNavigation({currentPage:s.page,nextPage:e.nextPage})}),s.bind(q,function(e){o.trigger(q,e),o.angular("compile",function(){return{elements:e.item,data:[{dataItem:e.data}]}})}),s.bind(H,function(e){o.angular("cleanup",function(){return{elements:e.item}})}),o._content=s,o.setDataSource(t.dataSource),this.viewInit(),this.viewShow()},options:{name:"ScrollView",page:0,duration:400,velocityThreshold:.8,contentHeight:"auto",pageSize:1,bounceVelocityThreshold:1.6,enablePager:!0,enableNavigationButtons:!0,pagerOverlay:!0,autoBind:!0,template:"",emptyTemplate:""},events:[z,k,V],destroy:function(){w.fn.destroy.call(this),this._content.destroy(),this.pane.destroy(),this.pager&&this.pager.destroy(),this.inner=null,p.destroy(this.element)},viewInit:function(){this.options.autoBind&&this._content.scrollTo(this._content.page,!0,!0)},viewShow:function(){this.pane.refresh()},refresh:function(){var e=this._content,t=this.options;e.resizeTo(this.pane.size()),this.page=e.page,(e instanceof h||e.dataReader.initialFetch)&&(t.enablePager?this.trigger(V,{pageCount:e.pageCount,page:e.page}):this.trigger(k,{pageCount:e.pageCount,currentPage:e.page}))},content:function(e){this.element.children().first().html(e),this._content._getPages(),this.pane.refresh()},scrollTo:function(e,t,i){this._content.scrollTo(e,t,i)},prev:function(){var e=this,i=e._content.page-1;e._content instanceof g?e._content.paneMoved(I,t,function(t){return e.trigger(k,t)}):i>-1&&e.scrollTo(i)},next:function(){var e=this,i=e._content.page+1;e._content instanceof g?e._content.paneMoved(F,t,function(t){return e.trigger(k,t)}):i<e._content.pageCount&&e.scrollTo(i)},setDataSource:function(e){var t,i=this;this._content instanceof g&&(t=!e,e instanceof _?(e.options.pageSize=e.options.pageSize||1,this.dataSource=e=new _(e.options)):this.dataSource=_.create(e),this._content.setDataSource(this.dataSource),this.options.autoBind&&!t&&this.dataSource.fetch(function(){i._content.dataReader.initialFetch=!0,i.scrollTo(i._content.page,!0,!0),i._content.trigger("reset")}))},items:function(){return this.element.find(".k-"+O)},_dragStart:function(){this._content.forcePageUpdate()},_dragEnd:function(e){var t=this,i=e.x.velocity,n=this.options.velocityThreshold,a=W,s=S(i)>this.options.bounceVelocityThreshold;i>n?a=I:i<-n&&(a=F),this._content.paneMoved(a,s,function(e){return t.trigger(k,e)})},_transitionEnd:function(){this._content.updatePage()},_initNavigation:function(){var t=this,i=t._navigationContainer=e("<div class='k-scrollview-elements'></div>"),n=e('<a class="k-scrollview-prev"><span class="k-icon k-i-arrowhead-w"></span></a>').hide(),a=e('<a class="k-scrollview-next"><span class="k-icon k-i-arrowhead-e"></span></a>').hide();i.append(n),i.append(a),t.element.append(i),i.on(C,"a.k-scrollview-prev",u(t.prev,t)),i.on(C,"a.k-scrollview-next",u(t.next,t))},_toggleNavigation:function(e){var t=e.nextPage||0===e.nextPage?e.nextPage:e.currentPage,i=this._navigationContainer,n=i.find(">a.k-scrollview-prev"),a=i.find(">a.k-scrollview-next");n.hide(),a.hide(),(t||0===t)&&(0!==t&&n.show(),t!=this._content.pageCount-1&&a.show())}}),d.plugin(c)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()});
//# sourceMappingURL=kendo.scrollview.min.js.map
