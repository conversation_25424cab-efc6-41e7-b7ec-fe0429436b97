/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.pivotgrid.min",["kendo.dom.min","kendo.data.min"],e)}(function(){return function(e,t){function r(e){var r="string"==typeof e?[{name:e}]:e,n="[object Array]"===we.call(r)?r:r!==t?[r]:[];return Se(n,function(e){return"string"==typeof e?{name:e}:{name:e.name,type:e.type}})}function n(e){var r="string"==typeof e?[{name:[e],expand:!1}]:e,n="[object Array]"===we.call(r)?r:r!==t?[r]:[];return Se(n,function(e){return"string"==typeof e?{name:[e],expand:!1}:{name:"[object Array]"===we.call(e.name)?e.name.slice():[e.name],expand:e.expand}})}function s(e){return e.indexOf(" ")!==-1&&(e='["'+e+'"]'),e}function a(e,t,r,n){var s,i,o,l;if(r||(r=t),n||(n=0),l=r.members[n],l&&!l.measure){if(o=l.children,i=o.length,r===t?e[de.stringify([l.name])]=!!i:i&&(e[de.stringify(oe(r,n))]=!0),i)for(s=0;s<i;s++)a(e,t,o[s],n);a(e,t,r,n+1)}}function i(t){var r,n,s={};t.length&&a(s,t[0]),r=[];for(n in s)r.push({name:e.parseJSON(n),expand:s[n]});return r}function o(e,t){var r,n,s,a,i=t.tuples||[],o=i[0];if(o&&e.length<o.members.length)for(r=o.members,n=0;n<r.length;n++)if(!r[n].measure){for(s=!1,a=0;a<e.length;a++)if(0===U(e[a]).indexOf(r[n].hierarchy)){s=!0;break}s||e.push({name:[r[n].name],expand:!1})}}function l(e){var t,r=[],n=e.members;for(t=0;t<n.length;t++)n[t].measure||r.push({name:[n[t].name],expand:n[t].children.length>0});return r}function u(e,t,r){var s,a;return e=e||{},o(t,e),r.length>1&&t.push({name:Ae,measure:!0,children:n(r)}),s={members:t},e.tuples&&(a=N(e.tuples,s),a.tuple&&(t=l(a.tuple))),t}function c(e){var t=de.getter(e.field,!0);return function(r,n){return e.aggregate(t(r.dataItem),n,r)}}function h(e){return"number"==typeof e&&!isNaN(e)}function m(e){return e&&e.getTime}function d(e){return e[e.length]={value:"",fmtValue:"",ordinal:e.length},e}function p(e,t,r){if(e.tuples.length<x(t.tuples,r))return t}function f(e,t,r,n,s){var a,i,o,l=e.length,u=x(t,n),c=n.length||1;for(i=0;i<r;i++)for(a=0;a<l;a++)o=_(e[a],t)*c,o+=a%c,s[i*l+a].ordinal=i*u+o}function g(e,t,r,n,s){var a,i,o,l=e.length,u=n.length||1;for(i=0;i<l;i++)for(o=_(e[i],t),o*=u,o+=i%u,a=0;a<r;a++)s[i*r+a].ordinal=o*r+a}function _(e,t){return N(t,e).index}function x(e,t){var r,n,s;if(!e.length)return 0;for(r=e.slice(),n=r.shift(),s=1;n;)n.members?[].push.apply(r,n.members):n.children&&(n.measure||(s+=n.children.length),[].push.apply(r,n.children)),n=r.shift();return t.length&&(s*=t.length),s}function v(e){return e||(e={tuples:[]}),e.tuples||(e.tuples=[]),e}function w(e,t,r){var n,s,a,i;if(!e)return 0;for(n=Math.max(r.length,1),s=e.members.slice(0,t),a=s.shift(),i=n;a;)a.name===Ae?i+=n-1:a.children?[].push.apply(s,a.children):(i++,[].push.apply(s,a.members)),a=s.shift();return i}function b(e,t,r){var n,s,a,i,o,l;if(!t[0])return{parsedRoot:null,tuples:e,memberIndex:0,index:0};if(n=N(e,t[0]),!n.tuple)return{parsedRoot:null,tuples:t,memberIndex:0,index:0};if(s=n.tuple.members,a=t[0].members,i=-1,s.length!==a.length)return{parsedRoot:null,tuples:t,memberIndex:0,index:0};for(o=0,l=s.length;o<l;o++)!s[o].measure&&a[o].children[0]&&(i==-1&&a[o].children.length&&(i=o),s[o].children=a[o].children);return r=Math.max(r.length,1),{parsedRoot:n.tuple,index:n.index*r,memberIndex:i,tuples:e}}function S(e,t){var r,n,s=!0;for(e=e.members,t=t.members,r=0,n=e.length;r<n;r++)e[r].measure||t[r].measure||(s=s&&U(e[r])===U(t[r]));return s}function N(e,t){var r,n,s,a,i,o,l,u=0;for(r=0,n=e.length;r<n;r++){if(s=e[r],S(s,t))return{tuple:s,index:u};for(u++,i=0,o=s.members.length;i<o;i++)if(l=s.members[i],!l.measure&&(a=N(l.children,t),u+=a.index,a.tuple))return{tuple:a.tuple,index:u}}return{index:u}}function E(e,t){var r,n,s,a="";for(n=0,s=e.length;n<s;n++)r=e[n],a+=r.name,t[a]||(t[a]=r)}function I(e,t){var r,n,s,a,i=e.members,o="",l="";for(r=0,n=i.length;r<n;r++){if(s=i[r],a){if(t[o+s.name]){o+=s.name,a=t[o];continue}return t[o+s.parentName]?t[o+s.parentName]:t[l+s.parentName]?t[l+s.parentName]:t[l]}if(o+=s.name,a=t[s.parentName],!a&&(a=t[o],!a))return null;a&&(l+=a.name)}return a}function M(e,t){var r,n,s,a;if(0===t.length)return-1;for(r=t[0],n=e.members,s=0,a=n.length;s<a;s++)if(n[s].name==r.name)return s}function A(r,n){if(!(n<0)){var s={name:Ae,measure:!0,children:[e.extend({members:[],dataIndex:r.dataIndex},r.members[n])]};r.members.splice(n,1,s),r.dataIndex=t}}function T(e,t){var r,n,s,a,i,o;if(e.length<1)return[];for(r=[],n={},s=M(e[0],t),a=0;a<e.length;a++)i=e[a],i.dataIndex=a,A(i,s),o=I(i,n),o?o.children.push(s<0||!o.measure?i:i.members[s].children[0]):r.push(i),E(i.members,n);return r}function y(e,t){var r,n,s,a,i,o,l,u,c;if(!e||!e.length)return t;for(r=[],n=C(e),s=n.length,a=Math.max(t.length/s,1),i=0;i<s;i++)for(l=a*i,u=a*n[i],o=0;o<a;o++)c=parseInt(u+o,10),r[parseInt(l+o,10)]=t[c]||{value:"",fmtValue:"",ordinal:c};return r}function R(e,t){var r,n,s,a,i,o,l,u;if(!e||!e.length)return t;for(r=[],n=C(e),s=n.length,a=Math.max(t.length/s,1),o=0;o<a;o++)for(l=s*o,i=0;i<s;i++)u=n[i]+l,r[l+i]=t[u]||{value:"",fmtValue:"",ordinal:u};return r}function C(e){var r,n,s,a,i,o,l;for(e=e.slice(),r=[],n=e.shift();n;){for(n.dataIndex!==t&&r.push(n.dataIndex),i=0,s=0,a=n.members.length;s<a;s++)l=n.members[s],o=l.children,l.measure?[].splice.apply(e,[0,0].concat(o)):[].splice.apply(e,[i,0].concat(o)),i+=o.length;n=e.shift()}return r}function k(e){var t=e.split(".");return t.length>2?t[0]+"."+t[1]:e}function D(e,t){var r=e.length-1,n=e[r],s=H(t,n);return s&&s.dir?n="ORDER("+n+".Children,"+s.field+".CurrentMember.MEMBER_CAPTION,"+s.dir+")":n+=".Children",e[r]=n,e}function H(e,t){for(var r=0,n=e.length;r<n;r++)if(0===t.indexOf(e[r].field))return e[r];return null}function P(e){var t,r="CROSSJOIN({";return e.length>2?(t=e.pop(),r+=P(e)):(r+=e.shift(),t=e.pop()),r+="},{",r+=t,r+="})"}function L(e,t){var r=e.slice(0);return t.length>1&&r.push("{"+O(t).join(",")+"}"),P(r)}function O(e){for(var r,n=0,s=e.length,a=[];n<s;n++)r=e[n],a.push(r.name!==t?r.name:r);return a}function U(e){return e=e.name||e,"[object Array]"===we.call(e)&&(e=e[e.length-1]),e}function F(e){for(var t=e.length,r=[],n=0;n<t;n++)r.push(e[n].name[0]);return r}function B(e,t){var r,n,s,a=0,i=e.length,o=t.length;for(t=t.slice(0);a<i;a++)for(r=e[a],s=0;s<o;s++)if(n=k(t[s]),r.indexOf(n)!==-1){t[s]=r;break}return{names:t,expandedIdx:s,uniquePath:t.slice(0,s+1).join("")}}function q(e){for(var t,r,n,s,a,i,o=[],l=[],u=[],c=0,h=e.length;c<h;c++)if(t=e[c],s=t.name,i=!1,"[object Array]"!==we.call(s)&&(t.name=s=[s]),s.length>1)l.push(t);else{for(a=k(s[0]),r=0,n=u.length;r<n;r++)if(0===u[r].name[0].indexOf(a)){i=!0;break}i||u.push(t),t.expand&&o.push(t)}return o=o.concat(l),{root:u,expanded:o}}function z(e,t,r){var n,s,a,i,o,l,u,c,h="";if(e=e||[],n=q(e),s=n.root,a=F(s),i=[],n=n.expanded,o=n.length,l=0,c=[],a.length>1||t.length>1){for(i.push(L(a,t));l<o;l++)u=D(n[l].name,r),c=B(u,a).names,i.push(L(c,t));h+=i.join(",")}else{for(;l<o;l++)u=D(n[l].name,r),c.push(u[0]);h+=a.concat(c).join(",")}return h}function G(e){var t="",r=e.value,n=e.field,s=e.operator;return"in"==s?(t+="{",t+=r,t+="}"):(t+="neq"==s||"doesnotcontain"==s?"-":"",t+="Filter(",t+=n+".MEMBERS",t+=de.format(K[s],n,r),t+=")"),t}function W(e,t){var r,n,s="",a=e.filters,i=a.length;for(n=i-1;n>=0;n--)r="SELECT (",r+=G(a[n]),r+=") ON 0",n==i-1?(r+=" FROM ["+t+"]",s=r):s=r+" FROM ( "+s+" )";return s}function j(e,t,r){var n,s,a="";if(t){a+="<"+e+">";for(s in t)n=t[s],r&&(s=s.replace(/([A-Z]+(?=$|[A-Z][a-z])|[A-Z]?[a-z]+)/g,"$1_").toUpperCase().replace(/_$/,"")),a+="<"+s+">"+n+"</"+s+">";a+="</"+e+">"}else a+="<"+e+"/>";return a}function Y(e){if(null==e)return[];var t=we.call(e);return"[object Array]"!==t?[e]:e}function V(e){var t,r,n,s,a={tuples:[]},i=Y(de.getter("Tuples.Tuple",!0)(e)),o=de.getter("Caption['#text']"),l=de.getter("UName['#text']"),u=de.getter("LName['#text']"),c=de.getter("LNum['#text']"),h=de.getter("CHILDREN_CARDINALITY['#text']",!0),m=de.getter("['@Hierarchy']"),d=de.getter("PARENT_UNIQUE_NAME['#text']",!0);for(t=0;t<i.length;t++){for(r=[],n=Y(i[t].Member),s=0;s<n.length;s++)r.push({children:[],caption:o(n[s]),name:l(n[s]),levelName:u(n[s]),levelNum:c(n[s]),hasChildren:parseInt(h(n[s]),10)>0,parentName:d(n[s]),hierarchy:m(n[s])});a.tuples.push({members:r})}return a}var K,Q,$,J,X,Z,ee,te,re,ne,se,ae,ie,oe,le,ue,ce,he,me,de=window.kendo,pe=de.ui,fe=de.Class,ge=pe.Widget,_e=de.data.DataSource,xe=de._outerWidth,ve=de._outerHeight,we={}.toString,be=function(e){return e},Se=e.map,Ne=e.extend,Ee=de.isFunction,Ie="change",Me="error",Ae="Measures",Te="progress",ye="stateReset",Re="auto",Ce="<div/>",ke=".kendoPivotGrid",De="__row_total__",He="dataBinding",Pe="dataBound",Le="expandMember",Oe="collapseMember",Ue="k-i-collapse",Fe="k-i-expand",Be="<span>#: data.member.caption || data.member.name #</span>",qe='<span class="k-icon k-i-kpi-status-#=data.dataItem.value > 0 ? "open" : data.dataItem.value < 0 ? "deny" : "hold"#" title="#:data.dataItem.value#"></span>',ze='<span class="k-icon k-i-kpi-trend-#=data.dataItem.value > 0 ? "increase" : data.dataItem.value < 0 ? "decrease" : "equal"#" title="#:data.dataItem.value#"></span>',Ge='#= data.dataItem ? kendo.htmlEncode(data.dataItem.fmtValue || data.dataItem.value) || "&nbsp;" : "&nbsp;" #',We='<table class="k-pivot-layout"><tr><td><div class="k-pivot-rowheaders"></div></td><td><div class="k-pivot-table k-state-default"></div></td></tr></table>',je="rows",Ye="columns",Ve={sum:function(e,t){var r=t.accumulator;return h(r)?h(e)&&(r+=e):r=e,r},count:function(e,t){return(t.accumulator||0)+1},average:{aggregate:function(e,r){var n=r.accumulator;return r.count===t&&(r.count=0),h(n)?h(e)&&(n+=e):n=e,h(e)&&r.count++,n},result:function(e){var t=e.accumulator;return h(t)&&(t/=e.count),t}},max:function(e,t){var r=t.accumulator;return h(r)||m(r)||(r=e),r<e&&(h(e)||m(e))&&(r=e),r},min:function(e,t){var r=t.accumulator;return h(r)||m(r)||(r=e),r>e&&(h(e)||m(e))&&(r=e),r}},Ke=fe.extend({init:function(e){this.options=Ne({},this.options,e),this.dimensions=this._normalizeDescriptors("field",this.options.dimensions),this.measures=this._normalizeDescriptors("name",this.options.measures)},_normalizeDescriptors:function(e,t){var r,n,s,a;if(t=t||{},r={},"[object Array]"===we.call(t)){for(s=0,a=t.length;s<a;s++)n=t[s],"string"==typeof n?r[n]={}:n[e]&&(r[n[e]]=n);t=r}return t},_rootTuples:function(e,r){var n,s,a,i,o=r.length||1,l=this.dimensions||[],u=0,c=e.length,h=[],m=[];if(c||r.length){for(u=0;u<o;u++){for(n={members:[]},i=0;i<c;i++)s=e[i],a=s.split("&"),n.members[n.members.length]={children:[],caption:(l[s]||{}).caption||"All",name:s,levelName:s,levelNum:"0",hasChildren:!0,parentName:a.length>1?a[0]:t,hierarchy:s};o>1&&(n.members[n.members.length]={children:[],caption:r[u].caption,name:r[u].descriptor.name,levelName:"MEASURES",levelNum:"0",hasChildren:!1,parentName:t,hierarchy:"MEASURES"}),h[h.length]=n}m.push(De)}return{keys:m,tuples:h}},_expandedTuples:function(e,r,n){var s,a,i,o,l,u,c,h,m,d,p,f=n.length||1,g=this.dimensions||[],_=[],x=[];for(i in e){for(o=e[i],c=this._findExpandedMember(r,o.uniquePath),l=_[c.index]||[],u=x[c.index]||[],h=c.member.names,s=0;s<f;s++){for(a={members:[]},p=0;p<h.length;p++)p===c.member.expandedIdx?(a.members[a.members.length]={children:[],caption:o.value,name:o.name,hasChildren:!1,levelNum:1,levelName:o.parentName+o.name,parentName:o.parentName,hierarchy:o.parentName+o.name},0===s&&u.push(oe(a,p).join(""))):(d=h[p],m=d.split("&"),a.members[a.members.length]={children:[],caption:(g[d]||{}).caption||"All",name:d,levelName:d,levelNum:"0",hasChildren:!0,parentName:m.length>1?m[0]:t,hierarchy:d});f>1&&(a.members[a.members.length]={children:[],caption:n[s].caption,name:n[s].descriptor.name,levelName:"MEASURES",levelNum:"0",hasChildren:!0,parentName:t,hierarchy:"MEASURES"}),l[l.length]=a}_[c.index]=l,x[c.index]=u}return{keys:x,tuples:_}},_findExpandedMember:function(e,t){for(var r=0;r<e.length;r++)if(e[r].uniquePath===t)return{member:e[r],index:r}},_asTuples:function(e,t,r){var n,s;return r=r||[],n=this._rootTuples(t.root,r),s=this._expandedTuples(e,t.expanded,r),{keys:[].concat.apply(n.keys,s.keys),tuples:[].concat.apply(n.tuples,s.tuples)}},_measuresInfo:function(e,t){for(var r,n,s=0,a=e&&e.length,i=[],o={},l={},u=this.measures||{};s<a;s++)n=e[s].descriptor.name,r=u[n]||{},i.push(n),r.result&&(o[n]=r.result),r.format&&(l[n]=r.format);return{names:i,formats:l,resultFuncs:o,rowAxis:t}},_toDataArray:function(e,t,r,n){var s,a,i,o,l,u,c,h,m,d,p=[],f=1,g=[],_=r.length||1,x=n.length||1;for(t.rowAxis?(g=t.names,f=g.length):d=t.names,i=0;i<_;i++)for(c=e[r[i]||De],u=0;u<f;u++)for(t.rowAxis&&(d=[g[u]]),o=0;o<x;o++)for(m=n[o]||De,h=c.items[m],s=m===De?c.aggregates:h?h.aggregates:{},l=0;l<d.length;l++)a=d[l],this._addData(p,s[a],t.formats[a],t.resultFuncs[a]);return p},_addData:function(e,t,r,n){var s,a="";t&&(t=n?n(t):t.accumulator,a=r?de.format(r,t):t),s=e.length,e[s]={ordinal:s,value:t||"",fmtValue:a}},_matchDescriptors:function(e,r,n){for(var s,a,i,o,l=r.names,u=r.expandedIdx;u>0;)if(s=l[--u].split("&"),s.length>1&&(a=s[0],i=s[1],o=n[a](e),o=o!==t&&null!==o?""+o:o,o!=i))return!1;return!0},_calculateAggregate:function(e,t,r){var n,s,a,i={};for(a=0;a<e.length;a++)s=e[a].descriptor.name,n=r.aggregates[s]||{},n.accumulator=e[a].aggregator(t,n),i[s]=n;return i},_processColumns:function(e,r,n,s,a,i,o,l){for(var u,c,h,m,d,p,f,g,_=a.dataItem,x=0;x<r.length;x++)c=r[x],this._matchDescriptors(_,c,n)&&(g=c.names.slice(0,c.expandedIdx).join(""),p=c.names[c.expandedIdx],u=n[p](_),u=u!==t&&null!==u?""+u:u,f=p,p=p+"&"+u,d=g+p,h=s[d]||{index:o.columnIndex,parentName:f,name:p,uniquePath:g+f,value:u},m=i.items[d]||{aggregates:{}},i.items[d]={index:h.index,aggregates:this._calculateAggregate(e,a,m)},l&&(s[d]||o.columnIndex++,s[d]=h))},_measureAggregators:function(e){var t,r,n,s,a,i,o=e.measures||[],l=this.measures||{},u=[];if(o.length){for(n=0,s=o.length;n<s;n++)if(t=o[n],r=l[t.name],a=null,r){if(i=r.aggregate,"string"==typeof i){if(a=Ve[i.toLowerCase()],!a)throw Error("There is no such aggregate function");r.aggregate=a.aggregate||a,r.result=a.result}u.push({descriptor:t,caption:r.caption,result:r.result,aggregator:c(r)})}}else u.push({descriptor:{name:"default"},caption:"default",aggregator:function(){return 1}});return u},_buildGetters:function(e){var t,r,n,a={};for(n=0;n<e.length;n++)r=e[n],t=r.split("&"),t.length>1?a[t[0]]=de.getter(t[0],!0):a[r]=de.getter(s(r),!0);return a},_parseDescriptors:function(e){var t,r=q(e),n=F(r.root),s=r.expanded,a=[];for(t=0;t<s.length;t++)a.push(B(s[t].name,n));return{root:n,expanded:a}},_filter:function(e,t){var r,n,s;if(!t)return e;for(n=0,s=t.filters;n<s.length;n++)r=s[n],"in"===r.operator&&(s[n]=this._normalizeFilter(r));return new de.data.Query(e).filter(t).data},_normalizeFilter:function(e){var t,r=e.value.split(","),n=[];if(!r.length)return r;for(t=0;t<r.length;t++)n.push({field:e.field,operator:"eq",value:r[t]});return{logic:"or",filters:n}},process:function(e,r){var s,a,i,o,l,u,c,h,m,d,p,f,g,_,x,v,w,b,S,N,E,I,M,A,T,y,R,C,k,D;if(e=e||[],r=r||{},e=this._filter(e,r.filter),s=r.measures||[],a="rows"===r.measuresAxis,i=r.columns||[],o=r.rows||[],!i.length&&o.length&&(!s.length||s.length&&a)&&(i=o,o=[],a=!1),i.length||o.length||(a=!1),!i.length&&s.length&&(i=n(r.measures)),i=this._parseDescriptors(i),o=this._parseDescriptors(o),l={},u={},c={},m={columnIndex:0},d=this._measureAggregators(r),p=this._buildGetters(i.root),f=this._buildGetters(o.root),g=!1,_=i.expanded,x=o.expanded,b=0!==x.length,k=e.length,D=0,i.root.length||o.root.length)for(g=!0,D=0;D<k;D++)for(v=e[D],w={dataItem:v,index:D},I=l[De]||{items:{},aggregates:{}},this._processColumns(d,_,p,u,w,I,m,!b),I.aggregates=this._calculateAggregate(d,w,I),l[De]=I,S=0;S<x.length;S++)N=x[S],this._matchDescriptors(v,N,f)?(A=N.names.slice(0,N.expandedIdx).join(""),E=N.names[N.expandedIdx],T=E,h=f[E](v),h=h!==t?""+h:h,E=E+"&"+h,M=A+E,c[M]={uniquePath:A+T,parentName:T,name:E,value:h},y=l[M]||{items:{},aggregates:{}},this._processColumns(d,_,p,u,w,y,m,!0),y.aggregates=this._calculateAggregate(d,w,y),l[M]=y):this._processColumns(d,_,p,u,w,{items:{},aggregates:{}},m,!0);return g&&k?(!(d.length>1)||r.columns&&r.columns.length||(i={root:[],expanded:[]}),R=this._asTuples(u,i,a?[]:d),C=this._asTuples(c,o,a?d:[]),u=R.tuples,c=C.tuples,l=this._toDataArray(l,this._measuresInfo(d,a),C.keys,R.keys)):l=u=c=[],{axes:{columns:{tuples:u},rows:{tuples:c}},data:l}}}),Qe=fe.extend({init:function(e,t){this.transport=t,this.options=t.options||{},this.transport.discover||Ee(e.discover)&&(this.discover=e.discover)},read:function(e){return this.transport.read(e)},update:function(e){return this.transport.update(e)},create:function(e){return this.transport.create(e)},destroy:function(e){return this.transport.destroy(e)},discover:function(e){return this.transport.discover?this.transport.discover(e):(e.success({}),t)},catalog:function(r){var n,s=this.options||{};return r===t?(s.connection||{}).catalog:(n=s.connection||{},n.catalog=r,this.options.connection=n,e.extend(this.transport.options,{connection:n}),t)},cube:function(e){var r,n=this.options||{};return e===t?(n.connection||{}).cube:(r=n.connection||{},r.cube=e,this.options.connection=r,Ne(!0,this.transport.options,{connection:r}),t)}}),$e=_e.extend({init:function(t){var s,a=((t||{}).schema||{}).cube,i="columns",o={axes:be,cubes:be,catalogs:be,measures:be,dimensions:be,hierarchies:be,levels:be,members:be};a&&(o=e.extend(o,this._cubeSchema(a)),this.cubeBuilder=new Ke(a)),_e.fn.init.call(this,Ne(!0,{},{schema:o},t)),this.transport=new Qe(this.options.transport||{},this.transport),this._columns=n(this.options.columns),this._rows=n(this.options.rows),s=this.options.measures||[],"[object Object]"===we.call(s)&&(i=s.axis||"columns",s=s.values||[]),this._measures=r(s),this._measuresAxis=i,this._skipNormalize=0,this._axes={}},_cubeSchema:function(t){return{dimensions:function(){var e,r=[],n=t.dimensions;for(e in n)r.push({name:e,caption:n[e].caption||e,uniqueName:e,defaultHierarchy:e,type:1});return t.measures&&r.push({name:Ae,caption:Ae,uniqueName:Ae,type:2}),r},hierarchies:function(){return[]},measures:function(){var e,r=[],n=t.measures;for(e in n)r.push({name:e,caption:e,uniqueName:e,aggregator:e});return r},members:e.proxy(function(e,r){var n,a,i=r.levelUniqueName||r.memberUniqueName,o=this.options.schema.data,l=Ee(o)?o:de.getter(o,!0),u=this.options.data&&l(this.options.data)||this._rawData||[],c=[],h=0,m={};if(i&&(i=i.split(".")[0]),!r.treeOp)return c.push({caption:t.dimensions[i].caption||i,childrenCardinality:"1",dimensionUniqueName:i,hierarchyUniqueName:i,levelUniqueName:i,name:i,uniqueName:i}),c;for(n=de.getter(s(i),!0);h<u.length;h++)a=n(u[h]),!a&&0!==a||m[a]||(m[a]=!0,c.push({caption:a,childrenCardinality:"0",dimensionUniqueName:i,hierarchyUniqueName:i,levelUniqueName:i,name:a,uniqueName:a}));return c},this)}},options:{serverSorting:!0,serverPaging:!0,serverFiltering:!0,serverGrouping:!0,serverAggregates:!0},catalog:function(e){return e===t?this.transport.catalog():(this.transport.catalog(e),this._mergeState({}),this._axes={},this.data([]),t)},cube:function(e){return e===t?this.transport.cube():(this.transport.cube(e),this._axes={},this._mergeState({}),this.data([]),t)},axes:function(){return this._axes},columns:function(e){return e===t?this._columns:(this._skipNormalize+=1,this._clearAxesData=!0,this._columns=n(e),this.query({columns:e,rows:this.rowsAxisDescriptors(),measures:this.measures(),sort:this.sort(),filter:this.filter()}),t)},rows:function(e){return e===t?this._rows:(this._skipNormalize+=1,this._clearAxesData=!0,this._rows=n(e),this.query({columns:this.columnsAxisDescriptors(),rows:e,measures:this.measures(),sort:this.sort(),filter:this.filter()}),t)},measures:function(e){return e===t?this._measures:(this._skipNormalize+=1,this._clearAxesData=!0,this.query({columns:this.columnsAxisDescriptors(),rows:this.rowsAxisDescriptors(),measures:r(e),sort:this.sort(),filter:this.filter()}),t)},measuresAxis:function(){return this._measuresAxis||"columns"},_expandPath:function(e,t){var r,s,a,i="columns"===t?"columns":"rows",o="columns"===t?"rows":"columns",l=n(e),c=U(l[l.length-1]);for(this._lastExpanded=i,l=u(this.axes()[i],l,this.measures()),r=0;r<l.length;r++)if(s=U(l[r]),s===c){if(l[r].expand)return;l[r].expand=!0}else l[r].expand=!1;a={},a[i]=l,a[o]=this._descriptorsForAxis(o),this._query(a)},_descriptorsForAxis:function(e){var t=this.axes(),r=this[e]()||[];return t&&t[e]&&t[e].tuples&&t[e].tuples[0]&&(r=i(t[e].tuples||[])),r},columnsAxisDescriptors:function(){return this._descriptorsForAxis("columns")},rowsAxisDescriptors:function(){return this._descriptorsForAxis("rows")},_process:function(e,t){this._view=e,t=t||{},t.items=t.items||this._view,this.trigger(Ie,t)},_query:function(e){var t=this;return e||(this._skipNormalize+=1,this._clearAxesData=!0),t.query(Ne({},{page:t.page(),pageSize:t.pageSize(),sort:t.sort(),filter:t.filter(),group:t.group(),aggregate:t.aggregate(),columns:this.columnsAxisDescriptors(),rows:this.rowsAxisDescriptors(),measures:this.measures()},e))},query:function(t){var r=this._mergeState(t);return this._data.length&&this.cubeBuilder?(this._params(r),this._updateLocalData(this._pristineData),e.Deferred().resolve().promise()):this.read(r)},_mergeState:function(e){return e=_e.fn._mergeState.call(this,e),e!==t&&(this._measures=r(e.measures),e.columns?e.columns=n(e.columns):e.columns||(this._columns=[]),e.rows?e.rows=n(e.rows):e.rows||(this._rows=[])),e},filter:function(e){return e===t?this._filter:(this._skipNormalize+=1,this._clearAxesData=!0,this._query({filter:e,page:1}),t)},expandColumn:function(e){this._expandPath(e,"columns")},expandRow:function(e){this._expandPath(e,"rows")},success:function(e){var t;this.cubeBuilder&&(t=(this.reader.data(e)||[]).slice(0)),_e.fn.success.call(this,e),t&&(this._pristineData=t)},_processResult:function(e,t){var r,n,s,a,i,o,l,u,c,h,m;return this.cubeBuilder&&(r=this.cubeBuilder.process(e,this._requestData),e=r.data,t=r.axes),u=this.columns(),c=this.rows(),h=t.columns&&t.columns.tuples,u.length||!c.length||!h||!this._rowMeasures().length&&this.measures().length||(t={columns:{},rows:t.columns}),u.length||c.length||"rows"!==this.measuresAxis()||!h||(t={columns:{},rows:t.columns}),this._axes={columns:v(this._axes.columns),rows:v(this._axes.rows)},t={columns:v(t.columns),rows:v(t.rows)},n=this._normalizeTuples(t.columns.tuples,this._axes.columns.tuples,u,this._columnMeasures()),s=this._normalizeTuples(t.rows.tuples,this._axes.rows.tuples,c,this._rowMeasures()),this._skipNormalize>0&&(this._skipNormalize-=1),this.cubeBuilder||(e=this._normalizeData({columnsLength:t.columns.tuples.length,rowsLength:t.rows.tuples.length,columnIndexes:n,rowIndexes:s,data:e})),"rows"==this._lastExpanded?(a=t.columns.tuples,o=this._columnMeasures(),i=p(t.columns,this._axes.columns,o),i&&(l="columns",t.columns=i,f(a,i.tuples,t.rows.tuples.length,o,e),this.cubeBuilder||(e=this._normalizeData({columnsLength:x(t.columns.tuples,o),rowsLength:t.rows.tuples.length,data:e})))):"columns"==this._lastExpanded&&(a=t.rows.tuples,o=this._rowMeasures(),i=p(t.rows,this._axes.rows,o),i&&(l="rows",t.rows=i,g(a,i.tuples,t.columns.tuples.length,o,e),this.cubeBuilder||(e=this._normalizeData({columnsLength:x(t.rows.tuples,o),rowsLength:t.columns.tuples.length,data:e})))),this._lastExpanded=null,m=this._mergeAxes(t,e,l),this._axes=m.axes,m.data},_readData:function(e){var t=this.reader.axes(e),r=this.reader.data(e);return this.cubeBuilder&&(this._rawData=r),this._processResult(r,t)},_createTuple:function(e,t,r){var n,s,a,i,o,l,u,c,h=e.members,m=h.length,d={members:[]},p=0;for(t&&(m-=1);p<m;p++)c=h[p],s=+c.levelNum,a=c.name,i=c.parentName,u=c.caption||a,o=c.hasChildren,l=c.hierarchy,n=c.levelName,r&&(u="All",0===s?i=c.name:s-=1,o=!0,a=l=n=i),d.members.push({name:a,children:[],caption:u,levelName:n,levelNum:""+s,hasChildren:o,hierarchy:l,parentName:r?"":i});return t&&d.members.push({name:t.name,children:[]}),d},_hasRoot:function(e,t,r){var n,s,a,i,o,l,u;if(t.length)return N(t,e).tuple;for(n=e.members,i=!0,l=0,u=n.length;l<u;l++)if(s=n[l],o=+s.levelNum||0,a=r[l],!(0===o||a&&s.name===U(a))){i=!1;break}return i},_mergeAxes:function(e,t,r){var n,s,a,i,o,l=this._columnMeasures(),u=this._rowMeasures(),c=this.axes(),h=x(c.rows.tuples,u),m=e.rows.tuples.length,d=x(c.columns.tuples,l),p=e.columns.tuples.length;return"columns"==r?(p=d,s=e.columns.tuples):(s=T(e.columns.tuples,l),t=R(s,t)),a=b(c.columns.tuples,s,l),"rows"==r?(m=x(e.rows.tuples,u),s=e.rows.tuples):(s=T(e.rows.tuples,u),t=y(s,t)),i=b(c.rows.tuples,s,u),c.columns.tuples=a.tuples,c.rows.tuples=i.tuples,d!==x(c.columns.tuples,l)?(n=a.index+w(a.parsedRoot,a.memberIndex,l),o=d+p,t=this._mergeColumnData(t,n,m,p,o)):h!==x(c.rows.tuples,u)&&(n=i.index+w(i.parsedRoot,i.memberIndex,u),t=this._mergeRowData(t,n,m,p)),0===c.columns.tuples.length&&0===c.rows.tuples.length&&(t=[]),{axes:c,data:t}},_mergeColumnData:function(e,t,r,n,s){var a,i,o,l=this.data().toJSON(),u=0,c=Math.max(this._columnMeasures().length,1);for(r=Math.max(r,1),l.length>0&&(u=c,s-=c),a=0;a<r;a++)i=t+a*s,o=e.splice(0,n),o.splice(0,u),[].splice.apply(l,[i,0].concat(o));return l},_mergeRowData:function(e,t,r,n){var s,a,i,o=this.data().toJSON(),l=Math.max(this._rowMeasures().length,1);for(n=Math.max(n,1),o.length>0&&(r-=l,e.splice(0,n*l)),s=0;s<r;s++)i=e.splice(0,n),a=t*n+s*n,[].splice.apply(o,[a,0].concat(i));return o},_columnMeasures:function(){var e=this.measures(),t=[];return"columns"===this.measuresAxis()&&(0===this.columns().length?t=e:e.length>1&&(t=e)),t},_rowMeasures:function(){var e=this.measures(),t=[];return"rows"===this.measuresAxis()&&(0===this.rows().length?t=e:e.length>1&&(t=e)),t},_updateLocalData:function(e,t){this.cubeBuilder&&(t&&(this._requestData=t),e=this._processResult(e)),this._data=this._observe(e),this._ranges=[],this._addRange(this._data),this._total=this._data.length,this._pristineTotal=this._total,this._process(this._data)},data:function(e){var r=this;return e===t?r._data:(this._pristineData=e.slice(0),this._updateLocalData(e,{columns:this.columns(),rows:this.rows(),measures:this.measures()}),t)},_normalizeTuples:function(e,t,r,n){var s,a,i,o=n.length||1,l=0,u=[],c={},h=0;if(e.length){if(this._skipNormalize<=0&&!this._hasRoot(e[0],t,r)){for(this._skipNormalize=0;l<o;l++)u.push(this._createTuple(e[0],n[l],!0)),c[l]=l;e.splice.apply(e,[0,e.length].concat(u).concat(e)),l=o}if(n.length)for(i=s=e[l],a=s.members.length-1;s;){if(h>=o&&(h=0),s.members[a].name!==n[h].name&&(e.splice(l,0,this._createTuple(s,n[h])),c[l]=l),l+=1,h+=1,s=e[l],o>h&&(!s||le(i,a-1)!==le(s,a-1))){for(;h<o;h++)e.splice(l,0,this._createTuple(i,n[h])),c[l]=l,l+=1;s=e[l]}i=s}return c}},_addMissingDataItems:function(e,r){for(;r.rowIndexes[parseInt(e.length/r.columnsLength,10)]!==t;)for(var n=0;n<r.columnsLength;n++)e=d(e);for(;r.columnIndexes[e.length%r.columnsLength]!==t;)e=d(e);return e},_normalizeOrdinals:function(e,t,r){var n=r.lastOrdinal;if(!t)return d(e);if(t.ordinal-n>1)for(n+=1;n<t.ordinal&&e.length<r.length;)e=this._addMissingDataItems(d(e),r),n+=1;return t.ordinal=e.length,e[e.length]=t,e},_normalizeData:function(e){var t,r,n,s=e.data,a=0,i=[];if(e.lastOrdinal=0,e.columnIndexes=e.columnIndexes||{},e.rowIndexes=e.rowIndexes||{},e.columnsLength=e.columnsLength||1,e.rowsLength=e.rowsLength||1,e.length=e.columnsLength*e.rowsLength,n=e.length,s.length===n)return s;for(;i.length<n;)t=s[a++],t&&(r=t.ordinal),i=this._normalizeOrdinals(this._addMissingDataItems(i,e),t,e),e.lastOrdinal=r;return i},discover:function(t,r){var n=this,s=n.transport;return e.Deferred(function(e){s.discover(Ne({success:function(t){t=n.reader.parse(t),n._handleCustomErrors(t)||(r&&(t=r(t)),e.resolve(t))},error:function(t,r,s){e.reject(t),n.error(t,r,s)}},t))}).promise().done(function(){n.trigger("schemaChange")})},schemaMeasures:function(){var e=this;return e.discover({data:{command:"schemaMeasures",restrictions:{catalogName:e.transport.catalog(),cubeName:e.transport.cube()}}},function(t){return e.reader.measures(t)})},schemaKPIs:function(){var e=this;return e.discover({data:{command:"schemaKPIs",restrictions:{catalogName:e.transport.catalog(),cubeName:e.transport.cube()}}},function(t){return e.reader.kpis(t)})},schemaDimensions:function(){var e=this;return e.discover({data:{command:"schemaDimensions",restrictions:{catalogName:e.transport.catalog(),cubeName:e.transport.cube()}}},function(t){return e.reader.dimensions(t)})},schemaHierarchies:function(e){var t=this;return t.discover({data:{command:"schemaHierarchies",restrictions:{catalogName:t.transport.catalog(),cubeName:t.transport.cube(),dimensionUniqueName:e}}},function(e){return t.reader.hierarchies(e)})},schemaLevels:function(e){var t=this;return t.discover({data:{command:"schemaLevels",restrictions:{catalogName:t.transport.catalog(),cubeName:t.transport.cube(),hierarchyUniqueName:e}}},function(e){return t.reader.levels(e)})},schemaCubes:function(){var e=this;return e.discover({data:{command:"schemaCubes",restrictions:{catalogName:e.transport.catalog()}}},function(t){return e.reader.cubes(t)})},schemaCatalogs:function(){var e=this;return e.discover({data:{command:"schemaCatalogs"}},function(t){return e.reader.catalogs(t)})},schemaMembers:function(e){var t=this,r=function(e){return function(r){return t.reader.members(r,e)}}(e);return t.discover({data:{command:"schemaMembers",restrictions:Ne({catalogName:t.transport.catalog(),cubeName:t.transport.cube()},e)}},r)},_params:function(e){this._clearAxesData&&(this._axes={},this._data=this._observe([]),this._clearAxesData=!1,this.trigger(ye));var t=_e.fn._params.call(this,e);return t=Ne({measures:this.measures(),measuresAxis:this.measuresAxis(),columns:this.columns(),rows:this.rows()},t),this.cubeBuilder&&(this._requestData=t),t}});$e.create=function(e){e=e&&e.push?{data:e}:e;var t=e||{},r=t.data;if(t.data=r,!(t instanceof $e)&&t instanceof de.data.DataSource)throw Error("Incorrect DataSource type. Only PivotDataSource instances are supported");return t instanceof $e?t:new $e(t)},K={contains:', InStr({0}.CurrentMember.MEMBER_CAPTION,"{1}") > 0',doesnotcontain:', InStr({0}.CurrentMember.MEMBER_CAPTION,"{1}")',startswith:', Left({0}.CurrentMember.MEMBER_CAPTION,Len("{1}"))="{1}"',endswith:', Right({0}.CurrentMember.MEMBER_CAPTION,Len("{1}"))="{1}"',eq:', {0}.CurrentMember.MEMBER_CAPTION = "{1}"',neq:', {0}.CurrentMember.MEMBER_CAPTION = "{1}"'},Q={schemaCubes:"MDSCHEMA_CUBES",schemaCatalogs:"DBSCHEMA_CATALOGS",schemaMeasures:"MDSCHEMA_MEASURES",schemaDimensions:"MDSCHEMA_DIMENSIONS",schemaHierarchies:"MDSCHEMA_HIERARCHIES",schemaLevels:"MDSCHEMA_LEVELS",schemaMembers:"MDSCHEMA_MEMBERS",schemaKPIs:"MDSCHEMA_KPIS"},$={read:function(e){var t,r,n,s,a,i='<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"><Header/><Body><Execute xmlns="urn:schemas-microsoft-com:xml-analysis"><Command><Statement>';return i+="SELECT NON EMPTY {",t=e.columns||[],r=e.rows||[],n=e.measures||[],s="rows"===e.measuresAxis,a=e.sort||[],!t.length&&r.length&&(!n.length||n.length&&s)&&(t=r,r=[],s=!1),t.length||r.length||(s=!1),t.length?i+=z(t,s?[]:n,a):n.length&&!s&&(i+=O(n).join(",")),i+="} DIMENSION PROPERTIES CHILDREN_CARDINALITY, PARENT_UNIQUE_NAME ON COLUMNS",(r.length||s&&n.length>1)&&(i+=", NON EMPTY {",i+=r.length?z(r,s?n:[],a):O(n).join(","),i+="} DIMENSION PROPERTIES CHILDREN_CARDINALITY, PARENT_UNIQUE_NAME ON ROWS"),e.filter?(i+=" FROM ",i+="(",i+=W(e.filter,e.connection.cube),i+=")"):i+=" FROM ["+e.connection.cube+"]",1==n.length&&t.length&&(i+=" WHERE ("+O(n).join(",")+")"),i+="</Statement></Command><Properties><PropertyList><Catalog>"+e.connection.catalog+"</Catalog><Format>Multidimensional</Format></PropertyList></Properties></Execute></Body></Envelope>",i.replace(/\&/g,"&amp;")},discover:function(t){t=t||{};var r='<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"><Header/><Body><Discover xmlns="urn:schemas-microsoft-com:xml-analysis">';return r+="<RequestType>"+(Q[t.command]||t.command)+"</RequestType>",r+="<Restrictions>"+j("RestrictionList",t.restrictions,!0)+"</Restrictions>",t.connection&&t.connection.catalog&&(t.properties=e.extend({},{
Catalog:t.connection.catalog},t.properties)),r+="<Properties>"+j("PropertyList",t.properties)+"</Properties>",r+="</Discover></Body></Envelope>"}},J=de.data.RemoteTransport.extend({init:function(e){var t=e;e=this.options=Ne(!0,{},this.options,e),de.data.RemoteTransport.call(this,e),Ee(t.discover)?this.discover=t.discover:"string"==typeof t.discover?this.options.discover={url:t.discover}:t.discover||(this.options.discover=this.options.read)},setup:function(t,r){return t.data=t.data||{},e.extend(!0,t.data,{connection:this.options.connection}),de.data.RemoteTransport.fn.setup.call(this,t,r)},options:{read:{dataType:"text",contentType:"text/xml",type:"POST"},discover:{dataType:"text",contentType:"text/xml",type:"POST"},parameterMap:function(e,t){return $[t](e,t)}},discover:function(t){return e.ajax(this.setup(t,"discover"))}}),X={cubes:{name:de.getter("CUBE_NAME['#text']",!0),caption:de.getter("CUBE_CAPTION['#text']",!0),description:de.getter("DESCRIPTION['#text']",!0),type:de.getter("CUBE_TYPE['#text']",!0)},catalogs:{name:de.getter("CATALOG_NAME['#text']",!0),description:de.getter("DESCRIPTION['#text']",!0)},measures:{name:de.getter("MEASURE_NAME['#text']",!0),caption:de.getter("MEASURE_CAPTION['#text']",!0),uniqueName:de.getter("MEASURE_UNIQUE_NAME['#text']",!0),description:de.getter("DESCRIPTION['#text']",!0),aggregator:de.getter("MEASURE_AGGREGATOR['#text']",!0),groupName:de.getter("MEASUREGROUP_NAME['#text']",!0),displayFolder:de.getter("MEASURE_DISPLAY_FOLDER['#text']",!0),defaultFormat:de.getter("DEFAULT_FORMAT_STRING['#text']",!0)},kpis:{name:de.getter("KPI_NAME['#text']",!0),caption:de.getter("KPI_CAPTION['#text']",!0),value:de.getter("KPI_VALUE['#text']",!0),goal:de.getter("KPI_GOAL['#text']",!0),status:de.getter("KPI_STATUS['#text']",!0),trend:de.getter("KPI_TREND['#text']",!0),statusGraphic:de.getter("KPI_STATUS_GRAPHIC['#text']",!0),trendGraphic:de.getter("KPI_TREND_GRAPHIC['#text']",!0),description:de.getter("KPI_DESCRIPTION['#text']",!0),groupName:de.getter("MEASUREGROUP_NAME['#text']",!0)},dimensions:{name:de.getter("DIMENSION_NAME['#text']",!0),caption:de.getter("DIMENSION_CAPTION['#text']",!0),description:de.getter("DESCRIPTION['#text']",!0),uniqueName:de.getter("DIMENSION_UNIQUE_NAME['#text']",!0),defaultHierarchy:de.getter("DEFAULT_HIERARCHY['#text']",!0),type:de.getter("DIMENSION_TYPE['#text']",!0)},hierarchies:{name:de.getter("HIERARCHY_NAME['#text']",!0),caption:de.getter("HIERARCHY_CAPTION['#text']",!0),description:de.getter("DESCRIPTION['#text']",!0),uniqueName:de.getter("HIERARCHY_UNIQUE_NAME['#text']",!0),dimensionUniqueName:de.getter("DIMENSION_UNIQUE_NAME['#text']",!0),displayFolder:de.getter("HIERARCHY_DISPLAY_FOLDER['#text']",!0),origin:de.getter("HIERARCHY_ORIGIN['#text']",!0),defaultMember:de.getter("DEFAULT_MEMBER['#text']",!0)},levels:{name:de.getter("LEVEL_NAME['#text']",!0),caption:de.getter("LEVEL_CAPTION['#text']",!0),description:de.getter("DESCRIPTION['#text']",!0),uniqueName:de.getter("LEVEL_UNIQUE_NAME['#text']",!0),dimensionUniqueName:de.getter("DIMENSION_UNIQUE_NAME['#text']",!0),displayFolder:de.getter("LEVEL_DISPLAY_FOLDER['#text']",!0),orderingProperty:de.getter("LEVEL_ORDERING_PROPERTY['#text']",!0),origin:de.getter("LEVEL_ORIGIN['#text']",!0),hierarchyUniqueName:de.getter("HIERARCHY_UNIQUE_NAME['#text']",!0)},members:{name:de.getter("MEMBER_NAME['#text']",!0),caption:de.getter("MEMBER_CAPTION['#text']",!0),uniqueName:de.getter("MEMBER_UNIQUE_NAME['#text']",!0),dimensionUniqueName:de.getter("DIMENSION_UNIQUE_NAME['#text']",!0),hierarchyUniqueName:de.getter("HIERARCHY_UNIQUE_NAME['#text']",!0),levelUniqueName:de.getter("LEVEL_UNIQUE_NAME['#text']",!0),childrenCardinality:de.getter("CHILDREN_CARDINALITY['#text']",!0)}},Z=["axes","catalogs","cubes","dimensions","hierarchies","levels","measures"],ee=de.data.XmlDataReader.extend({init:function(e){de.data.XmlDataReader.call(this,e),this._extend(e)},_extend:function(e){for(var t,r,n=0,s=Z.length;n<s;n++)t=Z[n],r=e[t],r&&r!==be&&(this[t]=r)},parse:function(e){var t=de.data.XmlDataReader.fn.parse(e.replace(/<(\/?)(\w|-)+:/g,"<$1"));return de.getter("['Envelope']['Body']",!0)(t)},errors:function(e){var t=de.getter("['Fault']",!0)(e);return t?[{faultstring:de.getter("faultstring['#text']",!0)(t),faultcode:de.getter("faultcode['#text']",!0)(t)}]:null},axes:function(e){var t,r,n,s;for(e=de.getter('ExecuteResponse["return"].root',!0)(e),t=Y(de.getter("Axes.Axis",!0)(e)),n={columns:{},rows:{}},s=0;s<t.length;s++)r=t[s],"sliceraxis"!==r["@name"].toLowerCase()&&(n.columns.tuples?n.rows=V(r):n.columns=V(r));return n},data:function(e){var t,r,n,s,a,i;for(e=de.getter('ExecuteResponse["return"].root',!0)(e),t=Y(de.getter("CellData.Cell",!0)(e)),r=[],n=de.getter("['@CellOrdinal']"),s=de.getter("Value['#text']"),a=de.getter("FmtValue['#text']"),i=0;i<t.length;i++)r.push({value:s(t[i]),fmtValue:a(t[i]),ordinal:parseInt(n(t[i]),10)});return r},_mapSchema:function(e,t){var r,n,s,a,i;for(e=de.getter('DiscoverResponse["return"].root',!0)(e),r=Y(de.getter("row",!0)(e)),n=[],s=0;s<r.length;s++){a={};for(i in t)a[i]=t[i](r[s]);n.push(a)}return n},measures:function(e){return this._mapSchema(e,X.measures)},kpis:function(e){return this._mapSchema(e,X.kpis)},hierarchies:function(e){return this._mapSchema(e,X.hierarchies)},levels:function(e){return this._mapSchema(e,X.levels)},dimensions:function(e){return this._mapSchema(e,X.dimensions)},cubes:function(e){return this._mapSchema(e,X.cubes)},catalogs:function(e){return this._mapSchema(e,X.catalogs)},members:function(e){return this._mapSchema(e,X.members)}}),Ne(!0,de.data,{PivotDataSource:$e,XmlaTransport:J,XmlaDataReader:ee,PivotCubeBuilder:Ke,transports:{xmla:J},readers:{xmla:ee}}),te=function(e,t){if(!e)return null;for(var r=0,n=e.length;r<n;r++)if(e[r].field===t)return e[r];return null},re=function(e,t){var r,n,s=[];for(r=0,n=e.length;r<n;r++)e[r].field!==t&&s.push(e[r]);return s},de.ui.PivotSettingTarget=ge.extend({init:function(t,r){var n=this;ge.fn.init.call(n,t,r),n.element.addClass("k-pivot-setting"),n.dataSource=de.data.PivotDataSource.create(r.dataSource),n._refreshHandler=e.proxy(n.refresh,n),n.dataSource.first(Ie,n._refreshHandler),r.template||(n.options.template="<div data-"+de.ns+'name="${data.name || data}">${data.name || data}'+(n.options.enabled?'<a class="k-button k-button-icon k-bare"><span class="k-icon k-i-close k-setting-delete"></span></a>':"")+"</div>"),n.template=de.template(n.options.template),n.emptyTemplate=de.template(n.options.emptyTemplate),n._sortable(),n.element.on("click"+ke,".k-button,.k-item",function(t){var r=e(t.target),s=r.closest("["+de.attr("name")+"]").attr(de.attr("name"));s&&(r.hasClass("k-i-close")?n.remove(s):n.options.sortable&&r[0]===t.currentTarget&&n.sort({field:s,dir:r.find(".k-i-sort-asc-sm")[0]?"desc":"asc"}))}),(r.filterable||r.sortable)&&(n.fieldMenu=new pe.PivotFieldMenu(n.element,{messages:n.options.messages.fieldMenu,filter:".k-setting-fieldmenu",filterable:r.filterable,sortable:r.sortable,dataSource:n.dataSource})),n.refresh()},options:{name:"PivotSettingTarget",template:null,filterable:!1,sortable:!1,emptyTemplate:"<div class='k-empty'>${data}</div>",setting:"columns",enabled:!0,messages:{empty:"Drop Fields Here"}},setDataSource:function(e){this.dataSource.unbind(Ie,this._refreshHandler),this.dataSource=this.options.dataSource=e,this.fieldMenu&&this.fieldMenu.setDataSource(e),e.first(Ie,this._refreshHandler),this.refresh()},_sortable:function(){var e=this;e.options.enabled&&(this.sortable=this.element.kendoSortable({connectWith:this.options.connectWith,hint:e.options.hint,cursor:"move",start:function(e){e.item.focus().blur()},change:function(t){var r=t.item.attr(de.attr("name"));"receive"==t.action?e.add(r):"remove"==t.action?e.remove(r):"sort"==t.action&&e.move(r,t.newIndex)}}).data("kendoSortable"))},_indexOf:function(e,t){var r,n,s=-1;for(r=0,n=t.length;r<n;r++)if(U(t[r])===e){s=r;break}return s},_isKPI:function(e){return"kpi"===e.type||e.measure},validate:function(e){var t,r,n=2==e.type||"aggregator"in e||this._isKPI(e);return n?"measures"===this.options.setting:"measures"===this.options.setting?n:(t=this.dataSource[this.options.setting](),r=e.defaultHierarchy||e.uniqueName,!(this._indexOf(r,t)>-1)&&(t=this.dataSource["columns"===this.options.setting?"rows":"columns"](),!(this._indexOf(r,t)>-1)))},add:function(t){var r,n,s=this.dataSource[this.options.setting]();for(t=e.isArray(t)?t.slice(0):[t],r=0,n=t.length;r<n;r++)this._indexOf(t[r],s)!==-1&&(t.splice(r,1),r-=1,n-=1);t.length&&(s=s.concat(t),this.dataSource[this.options.setting](s))},move:function(e,t){var r=this.dataSource[this.options.setting](),n=this._indexOf(e,r);n>-1&&(e=r.splice(n,1)[0],r.splice(t,0,e),this.dataSource[this.options.setting](r))},remove:function(e){var t=this.dataSource[this.options.setting](),r=this._indexOf(e,t),n=this.dataSource.sort(),s=this.dataSource.filter();r>-1&&(s&&(s.filters=re(s.filters,e),this.dataSource._filter.filters=s.filters,s.filters.length||(this.dataSource._filter=null)),n&&(n=re(n,e),this.dataSource._sort=n),t.splice(r,1),this.dataSource[this.options.setting](t))},sort:function(e){var t=this.options.sortable,r=t===!0||t.allowUnsort,n=r&&"asc"===e.dir,s=this.dataSource.sort()||[],a=re(s,e.field);n&&s.length!==a.length&&(e=null),e&&a.push(e),this.dataSource.sort(a)},refresh:function(){var e,r="",n=this.dataSource[this.options.setting](),s=n.length,a=0;if(s)for(;a<s;a++)e=n[a],e=e.name===t?{name:e}:e,r+=this.template(Ne({sortIcon:this._sortIcon(e.name)},e));else r=this.emptyTemplate(this.options.messages.empty);this.element.html(r)},destroy:function(){ge.fn.destroy.call(this),this.dataSource.unbind(Ie,this._refreshHandler),this.element.off(ke),this.sortable&&this.sortable.destroy(),this.fieldMenu&&this.fieldMenu.destroy(),this.element=null,this._refreshHandler=null},_sortIcon:function(e){var t=this.dataSource.sort(),r=te(t,U(e)),n="";return r&&(n="k-i-sort-"+r.dir),n}}),ne=ge.extend({init:function(r,n){var s,a,i=this;ge.fn.init.call(i,r,n),i._dataSource(),i._bindConfigurator(),i._wrapper(),i._createLayout(),i._columnBuilder=s=new ue,i._rowBuilder=a=new ce,i._contentBuilder=new he,i._templates(),i.columnsHeader.add(i.rowsHeader).on("click","span.k-icon",function(){var r,n,o,l,u=e(this),c=s,h="expandColumn",m=u.attr(de.attr("path")),d={axis:"columns",path:e.parseJSON(m)};u.parent().is("td")&&(c=a,h="expandRow",d.axis="rows"),n=u.hasClass(Ue),o=c.metadata[m],l=o.expanded===t,r=n?Oe:Le,d.childrenLoaded=o.maxChildren>o.children,i.trigger(r,d)||(c.metadata[m].expanded=!n,u.toggleClass(Ue,!n).toggleClass(Fe,n),!n&&l?i.dataSource[h](d.path):i.refresh())}),i._scrollable(),i.options.autoBind&&i.dataSource.fetch(),de.notify(i)},events:[He,Pe,Le,Oe],options:{name:"PivotGrid",autoBind:!0,reorderable:!0,filterable:!1,sortable:!1,height:null,columnWidth:100,configurator:"",columnHeaderTemplate:null,rowHeaderTemplate:null,dataCellTemplate:null,kpiStatusTemplate:null,kpiTrendTemplate:null,messages:{measureFields:"Drop Data Fields Here",columnFields:"Drop Column Fields Here",rowFields:"Drop Rows Fields Here"}},_templates:function(){var e=this.options.columnHeaderTemplate,t=this.options.rowHeaderTemplate,r=this.options.dataCellTemplate,n=this.options.kpiStatusTemplate,s=this.options.kpiTrendTemplate;this._columnBuilder.template=de.template(e||Be,{useWithBlock:!!e}),this._contentBuilder.dataTemplate=de.template(r||Ge,{useWithBlock:!!r}),this._contentBuilder.kpiStatusTemplate=de.template(n||qe,{useWithBlock:!!n}),this._contentBuilder.kpiTrendTemplate=de.template(s||ze,{useWithBlock:!!s}),this._rowBuilder.template=de.template(t||Be,{useWithBlock:!!t})},_bindConfigurator:function(){var t=this.options.configurator;t&&e(t).kendoPivotConfigurator("setDataSource",this.dataSource)},cellInfoByElement:function(t){return t=e(t),this.cellInfo(t.index(),t.parent("tr").index())},cellInfo:function(e,t){var r,n=this._contentBuilder,s=n.columnIndexes[e||0],a=n.rowIndexes[t||0];return s&&a?(r=a.index*n.rowLength+s.index,{columnTuple:s.tuple,rowTuple:a.tuple,measure:s.measure||a.measure,dataItem:this.dataSource.view()[r]}):null},setDataSource:function(e){this.options.dataSource=e,this._dataSource(),this.measuresTarget&&this.measuresTarget.setDataSource(e),this.rowsTarget&&this.rowsTarget.setDataSource(e),this.columnsTarget&&this.columnsTarget.setDataSource(e),this._bindConfigurator(),this.options.autoBind&&e.fetch()},setOptions:function(e){ge.fn.setOptions.call(this,e),this._templates()},destroy:function(){ge.fn.destroy.call(this),clearTimeout(this._headerReflowTimeout)},_dataSource:function(){var t=this,r=t.options.dataSource;r=e.isArray(r)?{data:r}:r,t.dataSource&&this._refreshHandler?t.dataSource.unbind(Ie,t._refreshHandler).unbind(ye,t._stateResetHandler).unbind(Te,t._progressHandler).unbind(Me,t._errorHandler):(t._refreshHandler=e.proxy(t.refresh,t),t._progressHandler=e.proxy(t._requestStart,t),t._stateResetHandler=e.proxy(t._stateReset,t),t._errorHandler=e.proxy(t._error,t)),t.dataSource=de.data.PivotDataSource.create(r).bind(Ie,t._refreshHandler).bind(Te,t._progressHandler).bind(ye,t._stateResetHandler).bind(Me,t._errorHandler)},_error:function(){this._progress(!1)},_requestStart:function(){this._progress(!0)},_stateReset:function(){this._columnBuilder.reset(),this._rowBuilder.reset()},_wrapper:function(){var e=this.options.height;this.wrapper=this.element.addClass("k-widget k-pivot"),e&&this.wrapper.css("height",e)},_measureFields:function(){this.measureFields=e(Ce).addClass("k-pivot-toolbar k-header k-settings-measures"),this.measuresTarget=this._createSettingTarget(this.measureFields,{setting:"measures",messages:{empty:this.options.messages.measureFields}})},_createSettingTarget:function(t,r){var n='<span tabindex="0" class="k-button" data-'+de.ns+'name="${data.name}">${data.name}',s=r.sortable,a="";return s&&(a+="#if (data.sortIcon) {#",a+='<span class="k-icon ${data.sortIcon}-sm"></span>',a+="#}#"),(r.filterable||s)&&(a+='<span class="k-icon k-i-more-vertical k-setting-fieldmenu"></span>'),this.options.reorderable&&(a+='<span class="k-icon k-i-close k-setting-delete"></span>'),a&&(n+='<span class="k-field-actions">'+a+"</span>"),n+="</span>",new de.ui.PivotSettingTarget(t,e.extend({template:n,emptyTemplate:'<span class="k-empty">${data}</span>',enabled:this.options.reorderable,dataSource:this.dataSource},r))},_initSettingTargets:function(){this.columnsTarget=this._createSettingTarget(this.columnFields,{connectWith:this.rowFields,setting:"columns",filterable:this.options.filterable,sortable:this.options.sortable,messages:{empty:this.options.messages.columnFields,fieldMenu:this.options.messages.fieldMenu}}),this.rowsTarget=this._createSettingTarget(this.rowFields,{connectWith:this.columnFields,setting:"rows",filterable:this.options.filterable,sortable:this.options.sortable,messages:{empty:this.options.messages.rowFields,fieldMenu:this.options.messages.fieldMenu}})},_createLayout:function(){var t=this,r=e(We),n=r.find(".k-pivot-rowheaders"),s=r.find(".k-pivot-table"),a=e(Ce).addClass("k-grid k-widget");t._measureFields(),t.columnFields=e(Ce).addClass("k-pivot-toolbar k-header k-settings-columns"),t.rowFields=e(Ce).addClass("k-pivot-toolbar k-header k-settings-rows"),t.columnsHeader=e('<div class="k-grid-header-wrap" />').wrap('<div class="k-grid-header" />'),t.columnsHeader.parent().css("padding-right",de.support.scrollbar()),t.rowsHeader=e('<div class="k-grid k-widget k-alt"/>'),t.content=e('<div class="k-grid-content" />'),n.append(t.measureFields),n.append(t.rowFields),n.append(t.rowsHeader),a.append(t.columnsHeader.parent()),a.append(t.content),s.append(t.columnFields),s.append(a),t.wrapper.append(r),t.columnsHeaderTree=new de.dom.Tree(t.columnsHeader[0]),t.rowsHeaderTree=new de.dom.Tree(t.rowsHeader[0]),t.contentTree=new de.dom.Tree(t.content[0]),t._initSettingTargets()},_progress:function(e){de.ui.progress(this.wrapper,e)},_resize:function(){this.content[0].firstChild&&(this._setSectionsWidth(),this._setSectionsHeight(),this._setContentWidth(),this._setContentHeight(),this._columnHeaderReflow())},_columnHeaderReflow:function(){var e=this.columnsHeader.children("table");de.support.browser.mozilla&&(clearTimeout(this._headerReflowTimeout),e.css("table-layout","auto"),this._headerReflowTimeout=setTimeout(function(){e.css("table-layout","")}))},_setSectionsWidth:function(){var e=this.rowsHeader,t=e.parent(".k-pivot-rowheaders").width(Re),r=Math.max(xe(this.measureFields),xe(this.rowFields));r=Math.max(e.children("table").width(),r),t.width(r)},_setSectionsHeight:function(){var e=this.measureFields.height(Re).height(),t=this.columnFields.height(Re).height(),r=this.rowFields.height(Re).innerHeight(),n=this.columnsHeader.height(Re).innerHeight(),s=r-this.rowFields.height(),a=t>e?t:e,i=n>r?n:r;this.measureFields.height(a),this.columnFields.height(a),this.rowFields.height(i-s),this.columnsHeader.height(i)},_setContentWidth:function(){var e=this.content.find("table"),t=this.columnsHeader.children("table"),r=e.children("colgroup").children().length,n=r*this.options.columnWidth,s=Math.ceil(n/this.content.width()*100);s<100&&(s=100),e.add(t).css("width",s+"%"),this._resetColspan(t)},_setContentHeight:function(){var e=this,r=e.content,n=e.rowsHeader,s=e.wrapper.innerHeight(),a=de.support.scrollbar(),i=r[0].offsetHeight===r[0].clientHeight,o=e.options.height;if(e.wrapper.is(":visible")){if(!s||!o)return i&&(a=0),r.height("auto"),n.height(r.height()-a),t;s-=ve(e.columnFields),s-=ve(e.columnsHeader.parent()),s<=2*a&&(s=2*a+1,i||(s+=a)),r.height(s),i&&(a=0),n.height(s-a)}},_resetColspan:function(e){var r=this,n=e.children("tbody").children(":first").children(":first");r._colspan===t&&(r._colspan=n.attr("colspan")),n.attr("colspan",1),clearTimeout(r._layoutTimeout),r._layoutTimeout=setTimeout(function(){n.attr("colspan",r._colspan),r._colspan=t})},_axisMeasures:function(e){var t=[],r=this.dataSource,n=r.measures(),s=n.length>1||n[0]&&n[0].type;return r.measuresAxis()===e&&(0===r[e]().length||s)&&(t=n),t},items:function(){return[]},refresh:function(){var e,t=this,r=t.dataSource,n=r.axes(),s=(n.columns||{}).tuples||[],a=(n.rows||{}).tuples||[],i=t._columnBuilder,o=t._rowBuilder,l={},u={};t.trigger(He,{action:"rebind"})||(i.measures=t._axisMeasures(Ye),o.measures=t._axisMeasures(je),t.columnsHeaderTree.render(i.build(s)),t.rowsHeaderTree.render(o.build(a)),l={indexes:i._indexes,measures:i.measures,metadata:i.metadata},u={indexes:o._indexes,measures:o.measures,metadata:o.metadata},t.contentTree.render(t._contentBuilder.build(r.view(),l,u)),t._resize(),t.touchScroller?t.touchScroller.contentResized():(e=de.touchScroller(t.content),e&&e.movable&&(t.touchScroller=e,e.movable.bind("change",function(e){t.columnsHeader.scrollLeft(-e.sender.x),t.rowsHeader.scrollTop(-e.sender.y)}))),t._progress(!1),t.trigger(Pe))},_scrollable:function(){var t=this,r=t.columnsHeader,n=t.rowsHeader;t.content.scroll(function(){r.scrollLeft(this.scrollLeft),n.scrollTop(this.scrollTop)}),n.bind("DOMMouseScroll"+ke+" mousewheel"+ke,e.proxy(t._wheelScroll,t))},_wheelScroll:function(t){var r,n;t.ctrlKey||(r=de.wheelDeltaY(t),n=this.content.scrollTop(),r&&(t.preventDefault(),e(t.currentTarget).one("wheel"+ke,!1),this.rowsHeader.scrollTop(n+-r),this.content.scrollTop(n+-r)))}}),se=de.dom.element,ae=de.dom.html,ie=function(e,t){return{maxChildren:0,children:0,maxMembers:0,members:0,measures:1,levelNum:e,parentMember:0!==t}},oe=function(e,t){for(var r=[],n=0;n<=t;n++)r.push(e.members[n].name);return r},le=function(e,t){for(var r="",n=0;n<=t;n++)r+=e.members[n].name;return r},ue=fe.extend({init:function(){this.measures=1,this.metadata={}},build:function(e){var t=this._tbody(e),r=this._colGroup();return[se("table",null,[r,t])]},reset:function(){this.metadata={}},_colGroup:function(){for(var e=this._rowLength(),t=[],r=0;r<e;r++)t.push(se("col",null));return se("colgroup",null,t)},_tbody:function(e){var t=e[0];return this.map={},this.rows=[],this.rootTuple=t,this._indexes=[],t?(this._buildRows(t,0),this._normalize()):this.rows.push(se("tr",null,[se("th",null,[ae("&nbsp;")])])),se("tbody",null,this.rows)},_normalize:function(){for(var e,t,r,n,s,a=this.rows,i=a.length,o=0;o<i;o++)if(e=a[o],1!==e.rowSpan)for(n=e.children,r=0,t=n.length;r<t;r++)s=n[r],s.tupleAll&&(s.attr.rowSpan=e.rowSpan)},_rowIndex:function(e){for(var t=this.rows,r=t.length,n=0;n<r&&t[n]!==e;n++);return n},_rowLength:function(){var e=this.rows[0]?this.rows[0].children:[],t=e.length,r=0,n=0;if(t)for(;n<t;n++)r+=e[n].attr.colSpan||1;return r||(r=this.measures),r},_row:function(e,t,r){var n,s,a=this.rootTuple.members[t].name,i=e.members[t].levelNum,o=a+i,l=this.map,u=l[o];return u?(u.notFirst=!1,u.parentMember&&u.parentMember===r||(u.parentMember=r,u.collapsed=0,u.colSpan=0)):(u=se("tr",null,[]),u.parentMember=r,u.collapsed=0,u.colSpan=0,u.rowSpan=1,l[o]=u,n=l[a+(+i-1)],n&&(s=n.children,u.notFirst=!(!s[1]||s[1].attr.className.indexOf("k-alt")!==-1)||n.notFirst),this.rows.splice(this._rowIndex(n)+1,0,u)),u},_measures:function(e,t,r){var n,s,a,i=this.map,o=i.measureRow;for(o||(o=se("tr",null,[]),i.measureRow=o,this.rows.push(o)),s=0,a=e.length;s<a;s++)n=e[s],o.children.push(this._cell(r||"",[this._content(n,t)],n));return a},_content:function(e,t){return ae(this.template({member:e,tuple:t}))},_cell:function(e,t,r){var n=se("th",{className:"k-header"+e},t);return n.value=r.caption||r.name,n},_buildRows:function(e,r,n){var s,a,i,o,l,u,c,h,m,d,p=e.members,f=p[r],g=p[r+1],_=[],x=0,v=0,w=0;if(f.measure)return this._measures(f.children,e),t;if(h=de.stringify(oe(e,r)),s=this._row(e,r,n),i=f.children,o=i.length,m=this.metadata[h],m||(this.metadata[h]=m=ie(+f.levelNum,r),m.rootLevelNum=+this.rootTuple.members[r].levelNum),this._indexes.push({path:h,tuple:e}),f.hasChildren&&(m.expanded===!1&&(v=m.maxChildren,s.collapsed+=v,m.children=0,o=0),c={className:"k-icon "+(o?Ue:Fe)},c[de.attr("path")]=h,_.push(se("span",c))),_.push(this._content(f,e)),l=this._cell(s.notFirst?" k-first":"",_,f),s.children.push(l),s.colSpan+=1,o){for(u=this._cell(" k-alt",[this._content(f,e)],f),s.children.push(u);x<o;x++)a=this._buildRows(i[x],r,f);d=a.colSpan,v=a.collapsed,l.attr.colSpan=d,m.children=d,m.members=1,s.colSpan+=d,s.collapsed+=v,s.rowSpan=a.rowSpan+1,g&&(g.measure?d=this._measures(g.children,e," k-alt"):(a=this._buildRows(e,r+1),d=a.colSpan,s.collapsed+=a.collapsed,w=a.collapsed),u.attr.colSpan=d,d-=1,m.members+=d,s.colSpan+=d)}else g&&(g.measure?d=this._measures(g.children,e):(a=this._buildRows(e,r+1),d=a.colSpan,s.collapsed+=a.collapsed,w=a.collapsed),m.members=d,d>1&&(l.attr.colSpan=d,s.colSpan+=d-1));return m.maxMembers<m.members+w&&(m.maxMembers=m.members+w),i=m.children+v,m.maxChildren<i&&(m.maxChildren=i),(u||l).tupleAll=!0,s}}),ce=fe.extend({init:function(){this.metadata={}},build:function(e){var t=this._tbody(e),r=this._colGroup();return[se("table",null,[r,t])]},reset:function(){this.metadata={}},_rowLength:function(){for(var e=this.rows[0].children,t=0,r=0,n=e[r];n;)t+=n.attr.colSpan||1,n=e[++r];return t},_colGroup:function(){for(var e=this._rowLength(),t=[],r=0;r<e;r++)t.push(se("col",null));return se("colgroup",null,t)},_tbody:function(e){var t=e[0];return this.rootTuple=t,this.rows=[],this.map={},this._indexes=[],t?(this._buildRows(t,0),this._normalize()):this.rows.push(se("tr",null,[se("td",null,[ae("&nbsp;")])])),se("tbody",null,this.rows)},_normalize:function(){for(var e,t,r,n,s=this.rows,a=s.length,i=0,o=this.rootTuple.members,l=o[0].name,u=o.length,c=0,h=this.map;i<a;i++)for(e=s[i],c=0;c<u;c++)r=this[o[c].name],t=e.colSpan["dim"+c],t&&t.colSpan<r&&(t.attr.colSpan=r-t.colSpan+1);e=h[l],n=h[l+"all"],e&&(e.children[0].attr.className="k-first"),n&&(n.children[0].attr.className+=" k-first")},_row:function(e){var t=se("tr",null,e);return t.rowSpan=1,t.colSpan={},this.rows.push(t),t},_content:function(e,t){return ae(this.template({member:e,tuple:t}))},_cell:function(e,t,r){var n=se("td",{className:e},t);return n.value=r.caption||r.name,n},_buildRows:function(e,t){var r,n,s,a,i,o,l,u,c,h=this.map,m=e.members,d=m[t],p=m[t+1],f=d.children,g=f.length,_=+d.levelNum,x=this.rootTuple.members[t].name,v=oe(e,t-1).join(""),w=+this.rootTuple.members[t].levelNum,b=v+(w===_?"":d.parentName||""),S=h[b+"all"]||h[b],N=_+1,E=[];if(!S||S.hasChild?S=this._row():S.hasChild=!0,d.measure){for(l=S.allCell?"k-grid-footer":"",S.children.push(this._cell(l,[this._content(f[0],e)],f[0])),S.rowSpan=g,c=1;c<g;c++)this._row([this._cell(l,[this._content(f[c],e)],f[c])]);return S}if(h[v+d.name]=S,r=de.stringify(oe(e,t)),o=this.metadata[r],o||(this.metadata[r]=o=ie(_,t),o.rootLevelNum=w),this._indexes.push({path:r,tuple:e}),d.hasChildren&&(o.expanded===!1&&(g=0,o.children=0),u={className:"k-icon "+(g?Ue:Fe)},u[de.attr("path")]=r,E.push(se("span",u))),E.push(this._content(d,e)),l=S.allCell&&!g?"k-grid-footer":"",n=this._cell(l,E,d),n.colSpan=N,S.children.push(n),S.colSpan["dim"+t]=n,(!this[x]||this[x]<N)&&(this[x]=N),g){for(S.allCell=!1,S.hasChild=!1,c=0;c<g;c++)a=this._buildRows(f[c],t),S!==a&&(S.rowSpan+=a.rowSpan);S.rowSpan>1&&(n.attr.rowSpan=S.rowSpan),o.children=S.rowSpan,s=this._cell("k-grid-footer",[this._content(d,e)],d),s.colSpan=N,i=this._row([s]),i.colSpan["dim"+t]=s,i.allCell=!0,h[v+d.name+"all"]=i,p&&(a=this._buildRows(e,t+1),s.attr.rowSpan=a.rowSpan),S.rowSpan+=i.rowSpan,o.members=i.rowSpan}else p&&(S.hasChild=!1,this._buildRows(e,t+1),(s||n).attr.rowSpan=S.rowSpan,o.members=S.rowSpan);return o.maxChildren<o.children&&(o.maxChildren=o.children),o.maxMembers<o.members&&(o.maxMembers=o.members),S}}),he=fe.extend({init:function(){this.columnAxis={},this.rowAxis={}},build:function(e,r,n){var s,a,i=r.indexes[0],o=r.metadata[i?i.path:t];return this.columnAxis=r,this.rowAxis=n,this.data=e,this.rowLength=o?o.maxChildren+o.maxMembers:r.measures.length||1,this.rowLength||(this.rowLength=1),s=this._tbody(),a=this._colGroup(),[se("table",null,[a,s])]},_colGroup:function(){var e=this.columnAxis.measures.length||1,t=[],r=0;for(this.rows[0]&&(e=this.rows[0].children.length);r<e;r++)t.push(se("col",null));return se("colgroup",null,t)},_tbody:function(){return this.rows=[],this.data[0]?(this.columnIndexes=this._indexes(this.columnAxis,this.rowLength),this.rowIndexes=this._indexes(this.rowAxis,Math.ceil(this.data.length/this.rowLength)),this._buildRows()):this.rows.push(se("tr",null,[se("td",null,[ae("&nbsp;")])])),se("tbody",null,this.rows)},_indexes:function(e,r){var n,s,a,i,o,l,u=[],c=e.indexes,h=e.metadata,m=e.measures,d=m.length||1,p=0,f=0,g=0,_=c.length;if(!_){for(a=0;a<d;a++)u[a]={index:a,measure:m[a],tuple:null};return u}for(;g<_;g++){if(n=c[g],s=h[n.path],o=s.children+s.members,l=0,o&&(o-=d),s.expanded===!1&&s.children!==s.maxChildren&&(l=s.maxChildren),s.parentMember&&s.levelNum===s.rootLevelNum&&(o=-1),o>-1){for(a=0;a<d;a++)i=o+a,s.children||(i+=f),u[o+f+a]={children:o,index:p,measure:m[a],tuple:n.tuple},p+=1;for(;u[f]!==t;)f+=1}if(f===r)break;p+=l}return u},_buildRows:function(){for(var e,t=this.rowIndexes,r=t.length,n=0;n<r;n++)e=t[n],e&&this.rows.push(this._buildRow(e))},_buildRow:function(e){for(var r,n,s,a,i,o,l,u=e.index*this.rowLength,c=this.columnIndexes,h=c.length,m=[],d=0;d<h;d++)r=c[d],r!==t&&(i={},r.children&&(i.className="k-alt"),a="",o=this.data[u+r.index],l=r.measure||e.measure,n={columnTuple:r.tuple,rowTuple:e.tuple,measure:l,dataItem:o},""!==o.value&&l&&l.type&&("status"===l.type?a=this.kpiStatusTemplate(n):"trend"===l.type&&(a=this.kpiTrendTemplate(n))),a||(a=this.dataTemplate(n)),s=se("td",i,[ae(a)]),s.value=o.value,m.push(s));return i={},e.children&&(i.className="k-grid-footer"),se("tr",i,m)}}),pe.plugin(ne),de.PivotExcelExporter=de.Class.extend({init:function(e){this.options=e,this.widget=e.widget,this.dataSource=this.widget.dataSource},_columns:function(){var e,t=this.widget.columnsHeaderTree.children[0],r=this.widget.rowsHeaderTree.children[0],n=t.children[0].children.length,s=r.children[0].children.length,a=this.widget.options.columnWidth,i=[];if(s&&this.dataSource.data()[0])for(e=0;e<s;e++)i.push({autoWidth:!0});for(e=0;e<n;e++)i.push({autoWidth:!1,width:a});return i},_cells:function(e,t,r){for(var n,s,a,i,o,l=[],u=0,c=e.length;u<c;u++){for(s=[],a=e[u].children,n=a.length,i=0;i<n;i++)o=a[i],s.push({background:"#7a7a7a",color:"#fff",value:o.value,colSpan:o.attr.colSpan||1,rowSpan:o.attr.rowSpan||1});r&&r(s,u),l.push({cells:s,type:t})}return l},_rows:function(){var e,t,r=this.widget.columnsHeaderTree.children[0],n=this.widget.rowsHeaderTree.children[0],s=r.children[0].children.length,a=n.children[0].children.length,i=r.children[1].children,o=n.children[1].children,l=this.widget.contentTree.children[0].children[1].children,u=this._cells(i,"header");return a&&u[0].cells.splice(0,0,{background:"#7a7a7a",color:"#fff",value:"",colSpan:a,rowSpan:i.length}),e=function(e,t){for(var r,n,a=0,i=l[t].children;a<s;a++)r=i[a],n=+r.value,isNaN(n)&&(n=r.value),e.push({background:"#dfdfdf",color:"#333",value:n,colSpan:1,rowSpan:1})},t=this._cells(o,"data",e),u.concat(t)},_freezePane:function(){var e=this.widget.columnsHeaderTree.children[0],t=this.widget.rowsHeaderTree.children[0],r=t.children[0].children.length,n=e.children[1].children;return{colSplit:r,rowSplit:n.length}},workbook:function(){var t;return this.dataSource.view()[0]?(t=e.Deferred(),t.resolve()):t=this.dataSource.fetch(),t.then(e.proxy(function(){return{sheets:[{columns:this._columns(),rows:this._rows(),freezePane:this._freezePane(),filter:null}]}},this))}}),me={extend:function(t){t.events.push("excelExport"),t.options.excel=e.extend(t.options.excel,this.options),t.saveAsExcel=this.saveAsExcel},options:{proxyURL:"",filterable:!1,fileName:"Export.xlsx"},saveAsExcel:function(){var t=this.options.excel||{},r=new de.PivotExcelExporter({widget:this});r.workbook().then(e.proxy(function(e){if(!this.trigger("excelExport",{workbook:e})){var r=new de.ooxml.Workbook(e);r.toDataURLAsync().then(function(r){de.saveAs({dataURI:r,fileName:e.fileName||t.fileName,proxyURL:t.proxyURL,forceProxy:t.forceProxy})})}},this))}},de.PivotExcelMixin=me,de.ooxml&&de.ooxml.Workbook&&me.extend(ne.prototype),de.PDFMixin&&(de.PDFMixin.extend(ne.prototype),ne.fn._drawPDF=function(){return this._drawPDFShadow({width:this.wrapper.width()},{avoidLinks:this.options.pdf.avoidLinks})})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,r){(r||t)()});
//# sourceMappingURL=kendo.pivotgrid.min.js.map
