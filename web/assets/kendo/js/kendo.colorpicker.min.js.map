{"version": 3, "sources": ["kendo.colorpicker.js"], "names": ["f", "define", "$", "parseInt", "undefined", "triggerEvent", "self", "type", "color", "parseColor", "equals", "_value", "a", "toCssRgba", "to<PERSON>s", "trigger", "value", "relative", "array", "element", "delta", "n", "pos", "Array", "prototype", "slice", "call", "length", "indexOf", "preventDefault", "ev", "bind", "callback", "obj", "apply", "arguments", "kendo", "window", "ui", "Widget", "Color", "KEYS", "keys", "BACKGROUNDCOLOR", "ITEMSELECTEDCLASS", "SIMPLEPALETTE", "WEBPALETTE", "WHITE", "MESSAGES", "cancel", "noColor", "clearColor", "previewInput", "NS", "CLICK_NS", "KEYDOWN_NS", "browser", "support", "isIE8", "msie", "version", "ColorSelector", "extend", "init", "options", "ariaId", "that", "this", "fn", "_tabIndex", "attr", "_ariaId", "_standalone", "_triggerSelect", "_triggerChange", "name", "events", "_updateUI", "opacity", "enable", "wrapper", "remove", "append", "_onEnable", "_select", "nohooks", "prev", "destroy", "off", "find", "noop", "_selectOnHide", "_cancel", "ColorPalette", "colors", "tileSize", "width", "height", "palette", "columns", "split", "isArray", "map", "x", "_selectedID", "guid", "addClass", "_template", "id", "on", "currentTarget", "css", "_keydown", "test", "parseFloat", "Error", "focus", "is", "removeAttr", "e", "selected", "items", "current", "filter", "get", "keyCode", "LEFT", "RIGHT", "DOWN", "UP", "ENTER", "ESC", "_current", "ex", "item", "removeClass", "each", "c", "template", "FlatColorPicker", "messages", "_hueElements", "_selectedColor", "_colorAsText", "_sliders", "_hsvArea", "val", "input", "autoupdate", "setTimeout", "end", "_clearedColor", "_getHSV", "_applyIEFilter", "_hueSlider", "_opacitySlider", "_hsvRect", "_hsvHandle", "buttons", "preview", "clearButton", "track", "url", "currentStyle", "backgroundImage", "replace", "style", "hue<PERSON><PERSON><PERSON>", "opacityChange", "hue<PERSON><PERSON><PERSON>", "opacitySlider", "kendoSlider", "min", "max", "tickPlacement", "showButtons", "slide", "change", "data", "update", "y", "offset", "dx", "left", "dy", "top", "rw", "rh", "_svChange", "hsvRect", "hsvHandle", "_hsvEvents", "UserEvents", "global", "press", "getOffset", "location", "start", "move", "handle", "prop", "d", "shift<PERSON>ey", "hue", "h", "ctrl<PERSON>ey", "F2", "select", "s", "v", "rect", "handlePosition", "position", "fromHSV", "dontChangeInput", "toDisplay", "toHSV", "ColorPicker", "content", "label", "accesskey", "hide", "after", "appendTo", "closest", "add", "click", "open", "isDefaultPrevented", "toggle", "_popup", "_selector", "innerWrapper", "children", "arrow", "toolIcon", "close", "ARIATemplate", "_getPopup", "selOptions", "_closing", "_noColorIcon", "_isInputTypeColor", "el", "tagName", "formattedValue", "_ariaTemplate", "key", "visible", "selectorType", "selector", "popup", "document", "body", "kendoPopup", "anchor", "adjustSize", "kendoButton", "icon", "selectorColor", "activate", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBACH,aACA,cACA,cACA,eACA,mBACA,gBACDD,IACL,WAowBE,MArvBC,UAAUE,EAAGC,EAAUC,GAkGpB,QAASC,GAAaC,EAAMC,EAAMC,GAC9BA,EAAQC,EAAWD,GACfA,IAAUA,EAAME,OAAOJ,EAAKE,WAChB,UAARD,IACAD,EAAKK,OAASH,GAGdA,EADW,GAAXA,EAAMI,EACEJ,EAAMK,YAENL,EAAMM,QAElBR,EAAKS,QAAQR,GAAQS,MAAOR,KA4WpC,QAASS,GAASC,EAAOC,EAASC,GAAlC,GAEQC,GACAC,CACJ,OAHAJ,GAAQK,MAAMC,UAAUC,MAAMC,KAAKR,GAC/BG,EAAIH,EAAMS,OACVL,EAAMJ,EAAMU,QAAQT,GACpBG,EAAM,EACCF,EAAQ,EAAIF,EAAMG,EAAI,GAAKH,EAAM,IAE5CI,GAAOF,EACHE,EAAM,EACNA,GAAOD,EAEPC,GAAOD,EAEJH,EAAMI,IAmQjB,QAASO,GAAeC,GACpBA,EAAGD,iBAEP,QAASE,GAAKC,EAAUC,GACpB,MAAO,YACH,MAAOD,GAASE,MAAMD,EAAKE,YA9uBtC,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAASD,EAAGC,OAAQ9B,EAAa2B,EAAM3B,WAAY+B,EAAQJ,EAAMI,MAAOC,EAAOL,EAAMM,KAAMC,EAAkB,mBAAoBC,EAAoB,mBAAoBC,EAAgB,8IAA+IC,EAAa,0+CAA2+CC,EAAQ,UAAWC,GACp3Dd,MAAO,QACPe,OAAQ,SACRC,QAAS,WACTC,WAAY,cACZC,aAAc,0BACfC,EAAK,mBAAoBC,EAAW,QAAUD,EAAIE,EAAa,UAAYF,EAAIG,EAAUpB,EAAMqB,QAAQD,QAASE,EAAQF,EAAQG,MAAQH,EAAQI,QAAU,EAC7JC,EAAgBtB,EAAOuB,QACvBC,KAAM,SAAU5C,EAAS6C,GACrB,GAAiBC,GAAbC,EAAOC,IACX5B,GAAO6B,GAAGL,KAAKrC,KAAKwC,EAAM/C,EAAS6C,GACnC7C,EAAU+C,EAAK/C,QACf6C,EAAUE,EAAKF,QACfE,EAAKvD,OAASqD,EAAQhD,MAAQP,EAAWuD,EAAQhD,OACjDkD,EAAKG,UAAYlD,EAAQmD,KAAK,aAAe,EAC7CL,EAASC,EAAKK,QAAUP,EAAQC,OAC5BA,GACA9C,EAAQmD,KAAK,kBAAmBL,GAEhCD,EAAQQ,cACRN,EAAKO,eAAiBP,EAAKQ,iBAGnCV,SACIW,KAAM,gBACN3D,MAAO,KACPwD,aAAa,GAEjBI,QACI,SACA,SACA,UAEJpE,MAAO,SAAUQ,GAKb,MAJIA,KAAUZ,IACV+D,KAAKxD,OAASF,EAAWO,GACzBmD,KAAKU,UAAUV,KAAKxD,SAEjBwD,KAAKxD,QAEhBK,MAAO,SAAUR,GASb,MARAA,GAAQ2D,KAAK3D,MAAMA,GACfA,IAEIA,EADA2D,KAAKH,QAAQc,QACLtE,EAAMK,YAENL,EAAMM,SAGfN,GAAS,MAEpBuE,OAAQ,SAAUA,GACW,IAArB5C,UAAUR,SACVoD,GAAS,GAEb7E,EAAE,sBAAuBiE,KAAKa,SAASC,SAClCF,GACDZ,KAAKa,QAAQE,OAAO,0CAExBf,KAAKgB,UAAUJ,IAEnBK,QAAS,SAAU5E,EAAO6E,GACtB,GAAIC,GAAOnB,KAAKxD,MAChBH,GAAQ2D,KAAK3D,MAAMA,GACd6E,IACDlB,KAAKhD,QAAQJ,QAAQ,UAChBP,EAAME,OAAO4E,GAENnB,KAAKK,aACbL,KAAKpD,QAAQ,UAFboD,KAAKpD,QAAQ,UAAYC,MAAOmD,KAAKnD,YAMjDyD,eAAgB,SAAUjE,GACtBH,EAAa8D,KAAM,SAAU3D,IAEjCkE,eAAgB,SAAUlE,GACtBH,EAAa8D,KAAM,SAAU3D,IAEjC+E,QAAS,WACDpB,KAAKhD,SACLgD,KAAKhD,QAAQqE,IAAInC,GAEjBc,KAAKa,SACLb,KAAKa,QAAQQ,IAAInC,GAAIoC,KAAK,KAAKD,IAAInC,GAEvCc,KAAKa,QAAU,KACfzC,EAAO6B,GAAGmB,QAAQ7D,KAAKyC,OAE3BU,UAAW3E,EAAEwF,KACbC,cAAe,WACX,MAAO,OAEXC,QAAS,WACLzB,KAAKpD,QAAQ,aAiBjB8E,EAAehC,EAAcC,QAC7BC,KAAM,SAAU5C,EAAS6C,GAAnB,GAKE8B,GAyBAC,EAA6BC,EAAOC,EA7BpC/B,EAAOC,IA8BX,IA7BAN,EAAcO,GAAGL,KAAKrC,KAAKwC,EAAM/C,EAAS6C,GAC1C7C,EAAU+C,EAAKc,QAAUd,EAAK/C,QAC9B6C,EAAUE,EAAKF,QACX8B,EAAS9B,EAAQkC,QACP,WAAVJ,GACAA,EAAShD,EACTkB,EAAQmC,QAAU,IACD,SAAVL,IACPA,EAASjD,GAEQ,gBAAViD,KACPA,EAASA,EAAOM,MAAM,MAEtBlG,EAAEmG,QAAQP,KACVA,EAAS5F,EAAEoG,IAAIR,EAAQ,SAAUS,GAC7B,MAAO9F,GAAW8F,MAG1BrC,EAAKsC,aAAexC,EAAQC,QAAU7B,EAAMqE,QAAU,YACtDtF,EAAQuF,SAAS,2BAA2BpC,KAAK,OAAQ,QAAQA,KAAK,gBAAiB,QAAQY,OAAOhF,EAAEgE,EAAKyC,WACzGb,OAAQA,EACRK,QAASnC,EAAQmC,QACjBJ,SAAU/B,EAAQ+B,SAClB/E,MAAOkD,EAAKvD,OACZiG,GAAI5C,EAAQC,WACX4C,GAAGvD,EAAU,UAAW,SAAUxB,GACnCoC,EAAKkB,QAAQlF,EAAE4B,EAAGgF,eAAeC,IAAIpE,MACtC2B,KAAK,WAAYJ,EAAKG,WAAWwC,GAAGtD,EAAYxB,EAAKmC,EAAK8C,SAAU9C,IACnE6B,EAAW/B,EAAQ+B,SACT,CACV,GAAI,gBAAgBkB,WAAYlB,IAC5BC,EAAQC,EAASiB,WAAWnB,OACzB,CAAA,GAAuB,gBAAZA,GAId,KAAUoB,OAAM,gDAHhBnB,GAAQkB,WAAWnB,EAASC,OAC5BC,EAASiB,WAAWnB,EAASE,QAIjC9E,EAAQsE,KAAK,WAAWsB,KACpBf,MAAOA,EACPC,OAAQA,MAIpBmB,MAAO,WACCjD,KAAKa,UAAYb,KAAKa,QAAQqC,GAAG,wBACjClD,KAAKa,QAAQoC,SAGrBpD,SACIW,KAAM,eACNwB,QAAS,GACTJ,SAAU,KACVG,QAAS,SAEbf,UAAW,SAAUJ,GACbA,EACAZ,KAAKa,QAAQV,KAAK,WAAYH,KAAKE,WAEnCF,KAAKa,QAAQsC,WAAW,aAGhCN,SAAU,SAAUO,GAAV,GACFC,GAqBQhH,EArBEwE,EAAUb,KAAKa,QAASyC,EAAQzC,EAAQS,KAAK,WAAYiC,EAAUD,EAAME,OAAO,IAAM/E,GAAmBgF,IAAI,GAAIC,EAAUN,EAAEM,OAiB3I,IAhBIA,GAAWpF,EAAKqF,KAChBN,EAAWvG,EAASwG,EAAOC,MACpBG,GAAWpF,EAAKsF,MACvBP,EAAWvG,EAASwG,EAAOC,EAAS,GAC7BG,GAAWpF,EAAKuF,KACvBR,EAAWvG,EAASwG,EAAOC,EAASvD,KAAKH,QAAQmC,SAC1C0B,GAAWpF,EAAKwF,GACvBT,EAAWvG,EAASwG,EAAOC,GAAUvD,KAAKH,QAAQmC,SAC3C0B,GAAWpF,EAAKyF,OACvBrG,EAAe0F,GACXG,GACAvD,KAAKiB,QAAQlF,EAAEwH,GAASX,IAAIpE,KAEzBkF,GAAWpF,EAAK0F,KACvBhE,KAAKyB,UAEL4B,EAAU,CACV3F,EAAe0F,GACfpD,KAAKiE,SAASZ,EACd,KACQhH,EAAQC,EAAW+G,EAAST,IAAIpE,IACpCwB,KAAKM,eAAejE,GACtB,MAAO6H,OAIjBD,SAAU,SAAUE,GAChBnE,KAAKa,QAAQS,KAAK,IAAM7C,GAAmB2F,YAAY3F,GAAmB0B,KAAK,iBAAiB,GAAOgD,WAAW,MAClHpH,EAAEoI,GAAM5B,SAAS9D,GAAmB0B,KAAK,iBAAiB,GAAMA,KAAK,KAAMH,KAAKqC,aAChFrC,KAAKhD,QAAQmG,WAAW,yBAAyBhD,KAAK,wBAAyBH,KAAKqC,cAExF3B,UAAW,SAAUrE,GACjB,GAAI8H,GAAO,IACXnE,MAAKa,QAAQS,KAAK,WAAW+C,KAAK,WAC9B,GAAIC,GAAIhI,EAAWP,EAAEiE,MAAM4C,IAAIpE,GAC/B,IAAI8F,GAAKA,EAAE/H,OAAOF,GAEd,MADA8H,GAAOnE,MACA,IAGfA,KAAKiE,SAASE,IAElB3B,UAAWvE,EAAMsG,SAAS,qaAA0c9F,EAAoB,2EAExf+F,EAAkB9E,EAAcC,QAChCC,KAAM,SAAU5C,EAAS6C,GACrB,GAAIE,GAAOC,IACXN,GAAcO,GAAGL,KAAKrC,KAAKwC,EAAM/C,EAAS6C,GAC1CA,EAAUE,EAAKF,QACfA,EAAQ4E,SAAW5E,EAAQA,QAAU9D,EAAE4D,OAAOI,EAAKF,QAAQ4E,SAAU5E,EAAQA,QAAQ4E,UAAY1E,EAAKF,QAAQ4E,SAC9GzH,EAAU+C,EAAK/C,QACf+C,EAAKc,QAAU7D,EAAQuF,SAAS,8BAA8BxB,OAAOhB,EAAKyC,UAAU3C,IACpFE,EAAK2E,aAAe3I,EAAE,2DAA4DiB,GAClF+C,EAAK4E,eAAiB5I,EAAE,4BAA6BiB,GACrD+C,EAAK6E,aAAe7I,EAAE,sBAAuBiB,GAC7C+C,EAAK8E,WACL9E,EAAK+E,WACL/E,EAAKW,UAAUX,EAAKvD,QAAUF,EAAW,SACzCU,EAAQsE,KAAK,uBAAuBoB,GAAGtD,EAAY,SAAUzB,GAAV,GAInCtB,GACA0I,EAJRC,EAAQhF,IACZ,IAAIrC,EAAG+F,SAAWpF,EAAKyF,MACnB,IACQ1H,EAAQC,EAAW0I,EAAMnI,OACzBkI,EAAMhF,EAAK1D,QACf0D,EAAKkB,QAAQ5E,EAAOA,EAAME,OAAOwI,IACnC,MAAOb,GACLnI,EAAEiJ,GAAOzC,SAAS,qBAEfxC,GAAKF,QAAQoF,YACpBC,WAAW,WACP,GAAI7I,GAAQC,EAAW0I,EAAMnI,OAAO,EAChCR,IACA0D,EAAKW,UAAUrE,GAAO,IAE3B,MAER8I,MAAMzC,GAAGvD,EAAU,2BAA4B,WAC1CY,EAAKF,QAAQuF,cACbrF,EAAKnD,QAAQ,UAEbmD,EAAKkB,QAAQlB,EAAKsF,aAEvB3C,GAAGvD,EAAU,4BAA6B,WACzCY,EAAKW,UAAUX,EAAK1D,SACpB0D,EAAK0B,YAELlC,GACAQ,EAAKuF,kBAGblE,QAAS,WACLpB,KAAKuF,WAAWnE,UACZpB,KAAKwF,gBACLxF,KAAKwF,eAAepE,UAExBpB,KAAKuF,WAAavF,KAAKwF,eAAiBxF,KAAKyF,SAAWzF,KAAK0F,WAAa1F,KAAK0E,aAAe1E,KAAK2E,eAAiB3E,KAAK4E,aAAe,KACxIlF,EAAcO,GAAGmB,QAAQ7D,KAAKyC,OAElCH,SACIW,KAAM,kBACNG,SAAS,EACTgF,SAAS,EACTX,OAAO,EACPY,SAAS,EACTC,aAAa,EACbZ,YAAY,EACZR,SAAU5F,GAEdyG,eAAgB,WACZ,GAAIQ,GAAQ9F,KAAKhD,QAAQsE,KAAK,iCAAiC,GAAIyE,EAAMD,EAAME,aAAaC,eAC5FF,GAAMA,EAAIG,QAAQ,4BAA6B,IAC/CJ,EAAMK,MAAM3C,OAAS,2DAA8DuC,EAAM,4BAE7FlB,SAAU,WAEN,QAASuB,GAAUhD,GACfrD,EAAKW,UAAUX,EAAKsF,QAAQjC,EAAEvG,MAAO,KAAM,KAAM,OAWrD,QAASwJ,GAAcjD,GACnBrD,EAAKW,UAAUX,EAAKsF,QAAQ,KAAM,KAAM,KAAMjC,EAAEvG,MAAQ,MAd5D,GAAIkD,GAAOC,KAAMhD,EAAU+C,EAAK/C,QAASsJ,EAAYtJ,EAAQsE,KAAK,iBAAkBiF,EAAgBvJ,EAAQsE,KAAK,yBAIjHgF,GAAUnG,KAAK,aAAc,kBAC7BJ,EAAKwF,WAAae,EAAUE,aACxBC,IAAK,EACLC,IAAK,IACLC,cAAe,OACfC,aAAa,EACbC,MAAOT,EACPU,OAAQV,IACTW,KAAK,eAIRR,EAAcpG,KAAK,aAAc,WACjCJ,EAAKyF,eAAiBe,EAAcC,aAChCC,IAAK,EACLC,IAAK,IACLC,cAAe,OACfC,aAAa,EACbC,MAAOR,EACPS,OAAQT,IACTU,KAAK,gBAEZjC,SAAU,WAEN,QAASkC,GAAO5E,EAAG6E,GACf,GAAIC,GAASlH,KAAKkH,OAAQC,EAAK/E,EAAI8E,EAAOE,KAAMC,EAAKJ,EAAIC,EAAOI,IAAKC,EAAKvH,KAAK6B,MAAO2F,EAAKxH,KAAK8B,MAChGqF,GAAKA,EAAK,EAAI,EAAIA,EAAKI,EAAKA,EAAKJ,EACjCE,EAAKA,EAAK,EAAI,EAAIA,EAAKG,EAAKA,EAAKH,EACjCtH,EAAK0H,UAAUN,EAAKI,EAAI,EAAIF,EAAKG,GALrC,GAAIzH,GAAOC,KAAMhD,EAAU+C,EAAK/C,QAAS0K,EAAU1K,EAAQsE,KAAK,oBAAqBqG,EAAYD,EAAQpG,KAAK,iBAAiBnB,KAAK,WAAY,GAAGuC,GAAGtD,EAAYxB,EAAKmC,EAAK8C,SAAU9C,GAOtLA,GAAK6H,WAAa,GAAI3J,GAAM4J,WAAWH,GACnCI,QAAQ,EACRC,MAAO,SAAU3E,GACbpD,KAAKkH,OAASjJ,EAAM+J,UAAUN,GAC9B1H,KAAK6B,MAAQ6F,EAAQ7F,QACrB7B,KAAK8B,OAAS4F,EAAQ5F,SACtB6F,EAAU1E,QACV+D,EAAOzJ,KAAKyC,KAAMoD,EAAEhB,EAAE6F,SAAU7E,EAAE6D,EAAEgB,WAExCC,MAAO,WACHR,EAAQnF,SAAS,cACjBoF,EAAU1E,SAEdkF,KAAM,SAAU/E,GACZA,EAAE1F,iBACFsJ,EAAOzJ,KAAKyC,KAAMoD,EAAEhB,EAAE6F,SAAU7E,EAAE6D,EAAEgB,WAExC9C,IAAK,WACDuC,EAAQtD,YAAY,iBAG5BrE,EAAK0F,SAAWiC,EAChB3H,EAAK2F,WAAaiC,GAEtB3G,UAAW,SAAUJ,GACjBZ,KAAKuF,WAAW3E,OAAOA,GACnBZ,KAAKwF,gBACLxF,KAAKwF,eAAe5E,OAAOA,GAE/BZ,KAAKa,QAAQS,KAAK,SAASnB,KAAK,YAAaS,EAC7C,IAAIwH,GAASpI,KAAKyF,SAASnE,KAAK,gBAC5BV,GACAwH,EAAOjI,KAAK,WAAYH,KAAKE,WAE7BkI,EAAOjF,WAAW,aAG1BN,SAAU,SAAUlF,GAEhB,QAASwK,GAAKE,EAAMC,GAChB,GAAIhE,GAAIvE,EAAKsF,SACbf,GAAE+D,IAASC,GAAK3K,EAAG4K,SAAW,IAAO,KACjCjE,EAAE+D,GAAQ,IACV/D,EAAE+D,GAAQ,GAEV/D,EAAE+D,GAAQ,IACV/D,EAAE+D,GAAQ,GAEdtI,EAAKW,UAAU4D,GACf5G,EAAeC,GAEnB,QAAS6K,GAAIF,GACT,GAAIhE,GAAIvE,EAAKsF,SACbf,GAAEmE,GAAKH,GAAK3K,EAAG4K,SAAW,EAAI,GAC1BjE,EAAEmE,EAAI,IACNnE,EAAEmE,EAAI,GAENnE,EAAEmE,EAAI,MACNnE,EAAEmE,EAAI,KAEV1I,EAAKW,UAAU4D,GACf5G,EAAeC,GAvBnB,GAAIoC,GAAOC,IAyBX,QAAQrC,EAAG+F,SACX,IAAKpF,GAAKqF,KACFhG,EAAG+K,QACHF,MAEAL,EAAK,OAET,MACJ,KAAK7J,GAAKsF,MACFjG,EAAG+K,QACHF,EAAI,GAEJL,EAAK,IAAK,EAEd,MACJ,KAAK7J,GAAKwF,GACNqE,EAAKxK,EAAG+K,SAAW3I,EAAKyF,eAAiB,IAAM,IAAK,EACpD,MACJ,KAAKlH,GAAKuF,KACNsE,EAAKxK,EAAG+K,SAAW3I,EAAKyF,eAAiB,IAAM,OAC/C,MACJ,KAAKlH,GAAKyF,MACNhE,EAAKkB,QAAQlB,EAAKsF,UAClB,MACJ,KAAK/G,GAAKqK,GACN5I,EAAKc,QAAQS,KAAK,uBAAuB2B,QAAQ2F,QACjD,MACJ,KAAKtK,GAAK0F,IACNjE,EAAK0B,YAIbwB,MAAO,WACHjD,KAAK0F,WAAWzC,SAEpBoC,QAAS,SAAUoD,EAAGI,EAAGC,EAAGrM,GACxB,GAAIsM,GAAO/I,KAAKyF,SAAU5D,EAAQkH,EAAKlH,QAASC,EAASiH,EAAKjH,SAAUkH,EAAiBhJ,KAAK0F,WAAWuD,UAazG,OAZS,OAALR,IACAA,EAAIzI,KAAKuF,WAAW1I,SAEf,MAALgM,IACAA,EAAIG,EAAe5B,KAAOvF,GAErB,MAALiH,IACAA,EAAI,EAAIE,EAAe1B,IAAMxF,GAExB,MAALrF,IACAA,EAAIuD,KAAKwF,eAAiBxF,KAAKwF,eAAe3I,QAAU,IAAM,GAE3DwB,EAAM6K,QAAQT,EAAGI,EAAGC,EAAGrM,IAElCgL,UAAW,SAAUoB,EAAGC,GACpB,GAAIzM,GAAQ2D,KAAKqF,QAAQ,KAAMwD,EAAGC,EAAG,KACrC9I,MAAKU,UAAUrE,IAEnBqE,UAAW,SAAUrE,EAAO8M,GACxB,GAAIpJ,GAAOC,KAAM+I,EAAOhJ,EAAK0F,QACxBpJ,KAGL2D,KAAK4E,aAAazE,KAAK,QAASJ,EAAKF,QAAQ4E,SAASxF,cACtDe,KAAK4E,aAAaR,YAAY,iBAC9BrE,EAAK4E,eAAe/B,IAAIpE,EAAiBnC,EAAM+M,aAC1CD,GACDpJ,EAAK6E,aAAaG,IAAIhF,EAAKyF,eAAiBnJ,EAAMK,YAAcL,EAAMM,SAE1EoD,EAAKO,eAAejE,GACpBA,EAAQA,EAAMgN,QACdtJ,EAAK2F,WAAW9C,KACZwE,KAAM/K,EAAMwM,EAAIE,EAAKlH,QAAU,KAC/ByF,KAAM,EAAIjL,EAAMyM,GAAKC,EAAKjH,SAAW,OAEzC/B,EAAK2E,aAAa9B,IAAIpE,EAAiBH,EAAM6K,QAAQ7M,EAAMoM,EAAG,EAAG,EAAG,GAAG9L,SACvEoD,EAAKwF,WAAW1I,MAAMR,EAAMoM,GACxB1I,EAAKyF,gBACLzF,EAAKyF,eAAe3I,MAAM,IAAMR,EAAMI,KAG9C+E,cAAe,WACX,MAAOxB,MAAKH,QAAQ8F,QAAU,KAAO3F,KAAKqF,WAE9C7C,UAAWvE,EAAMsG,SAAS,y+BAiB1B+E,EAAclL,EAAOuB,QACrBC,KAAM,SAAU5C,EAAS6C,GAAnB,GAKEhD,GAOA0M,EAIIC,EACA/G,EAWJgH,EA3BA1J,EAAOC,IACX5B,GAAO6B,GAAGL,KAAKrC,KAAKwC,EAAM/C,EAAS6C,GACnCA,EAAUE,EAAKF,QACf7C,EAAU+C,EAAK/C,QACXH,EAAQG,EAAQmD,KAAK,UAAYnD,EAAQ+H,MAEzClI,EADAA,EACQP,EAAWO,GAAO,GAElBP,EAAWuD,EAAQhD,OAAO,GAEtCkD,EAAKvD,OAASqD,EAAQhD,MAAQA,EAC1B0M,EAAUxJ,EAAKc,QAAU9E,EAAEgE,EAAKyC,UAAU3C,IAC9C7C,EAAQ0M,OAAOC,MAAMJ,GACjBvM,EAAQkG,GAAG,WACXlG,EAAQ4M,SAASL,GACbC,EAAQxM,EAAQ6M,QAAQ,SACxBpH,EAAKzF,EAAQmD,KAAK,MAClBsC,IACA+G,EAAQA,EAAMM,IAAI,cAAgBrH,EAAK,OAE3C+G,EAAMO,MAAM,SAAUpM,GAClBoC,EAAKiK,OACLrM,EAAGD,oBAGXqC,EAAKG,UAAYlD,EAAQmD,KAAK,aAAe,EAC7CJ,EAAKa,QAAQ5D,EAAQmD,KAAK,aACtBsJ,EAAYzM,EAAQmD,KAAK,aACzBsJ,IACAzM,EAAQmD,KAAK,YAAa,MAC1BoJ,EAAQpJ,KAAK,YAAasJ,IAE9B1J,EAAKnC,KAAK,WAAY,SAAUD,GACvBA,EAAGsM,sBACJlK,EAAKmK,WAGbnK,EAAKW,UAAU7D,IAEnBuE,QAAS,WACLpB,KAAKa,QAAQQ,IAAInC,GAAIoC,KAAK,KAAKD,IAAInC,GAC/Bc,KAAKmK,SACLnK,KAAKoK,UAAUhJ,UACfpB,KAAKmK,OAAO/I,WAEhBpB,KAAKoK,UAAYpK,KAAKmK,OAASnK,KAAKa,QAAU,KAC9CzC,EAAO6B,GAAGmB,QAAQ7D,KAAKyC,OAE3BY,OAAQ,SAAUA,GACd,GAAIb,GAAOC,KAAMa,EAAUd,EAAKc,QAASwJ,EAAexJ,EAAQyJ,SAAS,kBAAmBC,EAAQF,EAAa/I,KAAK,YAC7F,KAArBtD,UAAUR,SACVoD,GAAS,GAEbb,EAAK/C,QAAQmD,KAAK,YAAaS,GAC/BC,EAAQV,KAAK,iBAAkBS,GAC/B2J,EAAMlJ,IAAInC,GAAIwD,GAAG,YAAcxD,EAAIxB,GACnCmD,EAAQ0B,SAAS,oBAAoBY,WAAW,YAAY2G,IAAI,IAAKjJ,GAASQ,IAAInC,GAC9E0B,EACAC,EAAQuD,YAAY,oBAAoBjE,KAAK,WAAYJ,EAAKG,WAAWwC,GAAG,aAAexD,EAAI,WAC3FmL,EAAa9H,SAAS,mBACvBG,GAAG,aAAexD,EAAI,WACrBmL,EAAajG,YAAY,mBAC1B1B,GAAG,QAAUxD,EAAI,WAChBmL,EAAa9H,SAAS,qBACvBG,GAAG,OAASxD,EAAI,WACfmL,EAAajG,YAAY,qBAC1B1B,GAAGtD,EAAYxB,EAAKmC,EAAK8C,SAAU9C,IAAO2C,GAAGvD,EAAU,YAAavB,EAAKmC,EAAKmK,OAAQnK,IAAO2C,GAAGvD,EAAUY,EAAKF,QAAQ2K,SAAW,eAAiB,oBAAqB,WACvKzK,EAAKnD,QAAQ,cAGjBmD,EAAK0K,SAGbjI,UAAWvE,EAAMsG,SAAS,2dAC1B1E,SACIW,KAAM,cACNuB,QAAS,KACTC,QAAS,GACTwI,SAAU,KACV3N,MAAO,KACP4H,SAAU5F,EACV8B,SAAS,EACTgF,SAAS,EACTC,SAAS,EACTC,aAAa,EACb6E,aAAc,2CAElBjK,QACI,WACA,SACA,SACA,OACA,SAEJuJ,KAAM,WACGhK,KAAKhD,QAAQqL,KAAK,aACnBrI,KAAK2K,YAAYX,QAGzBS,MAAO,WACH,GAAIG,GAAa5K,KAAKoK,WAAapK,KAAKoK,UAAUvK,WAClD+K,GAAWC,UAAW,EACtB7K,KAAK2K,YAAYF,cACVG,GAAWC,UAEtBX,OAAQ,WACClK,KAAKhD,QAAQqL,KAAK,aACnBrI,KAAK2K,YAAYT,UAGzBY,aAAc,WACV,MAAO9K,MAAKa,QAAQS,KAAK,0DAE7BjF,MAAOqD,EAAcO,GAAG5D,MACxBQ,MAAO6C,EAAcO,GAAGpD,MACxBoE,QAASvB,EAAcO,GAAGgB,QAC1BX,eAAgBZ,EAAcO,GAAGK,eACjCyK,kBAAmB,WACf,GAAIC,GAAKhL,KAAKhD,QAAQ,EACtB,OAAO,WAAW8F,KAAKkI,EAAGC,UAAY,WAAWnI,KAAKkI,EAAG5O,OAE7DsE,UAAW,SAAU7D,GACjB,GAAIqO,GAAiB,EACjBrO,KAEIqO,EADAlL,KAAK+K,qBAAkC,GAAXlO,EAAMJ,EACjBI,EAAMF,QAENE,EAAMH,YAE3BsD,KAAKhD,QAAQ+H,IAAImG,IAEhBlL,KAAKmL,gBACNnL,KAAKmL,cAAgBlN,EAAMsG,SAASvE,KAAKH,QAAQ6K,eAErD1K,KAAKa,QAAQV,KAAK,aAAcH,KAAKmL,cAAcD,IACnDlL,KAAKM,eAAezD,GACpBmD,KAAKa,QAAQS,KAAK,qBAAqBsB,IAAIpE,EAAiB3B,EAAQA,EAAMuM,YAAcxK,GACxFoB,KAAK8K,eAAeI,EAAiB,OAAS,WAElDrI,SAAU,SAAUlF,GAChB,GAAIyN,GAAMzN,EAAG+F,OACT1D,MAAK2K,YAAYU,WACbD,GAAO9M,EAAK0F,IACZhE,KAAKoK,UAAU3I,UAEfzB,KAAKoK,UAAUvH,SAASlF,GAE5BD,EAAeC,IACRyN,GAAO9M,EAAKyF,OAASqH,GAAO9M,EAAKuF,OACxC7D,KAAKgK,OACLtM,EAAeC,KAGvBgN,UAAW,WAAA,GAGC9K,GACAyL,EAUA7I,EACA8I,EAdJxL,EAAOC,KAAMwL,EAAQzL,EAAKoK,MAkG9B,OAjGKqB,KACG3L,EAAUE,EAAKF,QAGfyL,EADAzL,EAAQkC,QACOL,EAEA8C,EAEnB3E,EAAQQ,aAAc,QACfR,GAAQ+I,aACR/I,GAAQiH,aACRjH,GAAQf,OACX2D,EAAKxE,EAAMqE,OACXiJ,EAAWxL,EAAKqK,UAAY,GAAIkB,GAAavP,EAAE,YAAc0G,EAAK,OAAOmH,SAAS6B,SAASC,MAAO7L,GACtGE,EAAKc,QAAQV,KAAK,YAAasC,GAC/B1C,EAAKoK,OAASqB,EAAQD,EAAS1K,QAAQ8K,YACnCC,OAAQ7L,EAAKc,QACbgL,YACIhK,MAAO,EACPC,OAAQ,KAEbiF,KAAK,cACRwE,EAASvO,QAAQsE,KAAK,kBAAkBwK,aACpCC,KAAM,cACNhC,MAAO,SAAU3G,GACbmI,EAAS1L,QAAQuF,eAAgB,EACjCrF,EAAKlD,MAAM,MACXkD,EAAK/C,QAAQ+H,IAAI,MACjBhF,EAAKW,UAAU,MACf6K,EAAS3G,aAAaG,IAAI,IAC1BwG,EAAS7F,WAAW9C,KAChB0E,IAAK,MACLF,KAAM,QAEVmE,EAAS5G,eAAe/B,IAAIpE,EAAiBI,GAC7CmB,EAAKnD,QAAQ,UAAYC,MAAOkD,EAAKlD,UACrCuG,EAAE1F,oBAGV6N,EAAS3N,MACLgL,OAAQ,SAAUjL,GACdoC,EAAKW,UAAUpE,EAAWqB,EAAGd,cACtB0O,GAAS1L,QAAQuF,eAE5B0B,OAAQ,WACCyE,EAAS1L,QAAQuF,eAClBrF,EAAKkB,QAAQsK,EAASlP,SAE1B0D,EAAK0K,SAET3L,OAAQ,WACAyM,EAAS1L,QAAQuF,gBAAkBrF,EAAKlD,SAAW0O,EAAS1O,SAC5DkD,EAAKkB,QAAQsK,EAASlP,SAAS,GAEnC0D,EAAK0K,WAGbe,EAAM5N,MACF6M,MAAO,SAAU9M,GAAV,GAMCtB,GACA2P,EACAnP,EACAgD,CARJ,OAAIE,GAAKnD,QAAQ,UACbe,EAAGD,iBACH,IAEJqC,EAAKc,QAAQyJ,SAAS,kBAAkBlG,YAAY,mBAChD/H,EAAQkP,EAAS/J,gBACjBwK,EAAgBT,EAAS1O,QACzBA,EAAQkD,EAAKlD,QACbgD,EAAU0L,EAAS1L,QAClBxD,EAWQwD,EAAQuF,gBAAkBvI,GACnCkD,EAAKkB,QAAQ5E,IAXb6I,WAAW,WACHnF,EAAKc,UAAYd,EAAKc,QAAQqC,GAAG,wBACjCnD,EAAKc,QAAQoC,WAGhBpD,EAAQgL,UAAYhL,EAAQuF,gBAAkBvI,GAASmP,EACxDjM,EAAKkB,QAAQ+K,GAAe,GAE5BjM,EAAKW,UAAUX,EAAK1D,UAd5B0D,IAoBJiK,KAAM,SAAUrM,GACRoC,EAAKnD,QAAQ,QACbe,EAAGD,iBAEHqC,EAAKc,QAAQyJ,SAAS,kBAAkB/H,SAAS,oBAGzD0J,SAAU,WACNV,EAAStK,QAAQlB,EAAK1D,SAAS,GAC/BkP,EAAStI,QACTlD,EAAKc,QAAQyJ,SAAS,kBAAkB/H,SAAS,uBAItDiJ,IAWfrN,GAAG+N,OAAOxK,GACVvD,EAAG+N,OAAO1H,GACVrG,EAAG+N,OAAO5C,IACZ6C,OAAQnQ,UACHkC,OAAOD,OACE,kBAAVnC,SAAwBA,OAAOsQ,IAAMtQ,OAAS,SAAUuQ,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.colorpicker.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.colorpicker', [\n        'kendo.core',\n        'kendo.color',\n        'kendo.popup',\n        'kendo.slider',\n        'kendo.userevents',\n        'kendo.button'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'colorpicker',\n        name: 'Color tools',\n        category: 'web',\n        description: 'Color selection widgets',\n        depends: [\n            'core',\n            'color',\n            'popup',\n            'slider',\n            'userevents',\n            'button'\n        ]\n    };\n    (function ($, parseInt, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, Widget = ui.Widget, parseColor = kendo.parseColor, Color = kendo.Color, KEYS = kendo.keys, BACKGROUNDCOLOR = 'background-color', ITEMSELECTEDCLASS = 'k-state-selected', SIMPLEPALETTE = '000000,7f7f7f,880015,ed1c24,ff7f27,fff200,22b14c,00a2e8,3f48cc,a349a4,ffffff,c3c3c3,b97a57,ffaec9,ffc90e,efe4b0,b5e61d,99d9ea,7092be,c8bfe7', WEBPALETTE = 'FFFFFF,FFCCFF,FF99FF,FF66FF,FF33FF,FF00FF,CCFFFF,CCCCFF,CC99FF,CC66FF,CC33FF,CC00FF,99FFFF,99CCFF,9999FF,9966FF,9933FF,9900FF,FFFFCC,FFCCCC,FF99CC,FF66CC,FF33CC,FF00CC,CCFFCC,CCCCCC,CC99CC,CC66CC,CC33CC,CC00CC,99FFCC,99CCCC,9999CC,9966CC,9933CC,9900CC,FFFF99,FFCC99,FF9999,FF6699,FF3399,FF0099,CCFF99,CCCC99,CC9999,CC6699,CC3399,CC0099,99FF99,99CC99,999999,996699,993399,990099,FFFF66,FFCC66,FF9966,FF6666,FF3366,FF0066,CCFF66,CCCC66,CC9966,CC6666,CC3366,CC0066,99FF66,99CC66,999966,996666,993366,990066,FFFF33,FFCC33,FF9933,FF6633,FF3333,FF0033,CCFF33,CCCC33,CC9933,CC6633,CC3333,CC0033,99FF33,99CC33,999933,996633,993333,990033,FFFF00,FFCC00,FF9900,FF6600,FF3300,FF0000,CCFF00,CCCC00,CC9900,CC6600,CC3300,CC0000,99FF00,99CC00,999900,996600,993300,990000,66FFFF,66CCFF,6699FF,6666FF,6633FF,6600FF,33FFFF,33CCFF,3399FF,3366FF,3333FF,3300FF,00FFFF,00CCFF,0099FF,0066FF,0033FF,0000FF,66FFCC,66CCCC,6699CC,6666CC,6633CC,6600CC,33FFCC,33CCCC,3399CC,3366CC,3333CC,3300CC,00FFCC,00CCCC,0099CC,0066CC,0033CC,0000CC,66FF99,66CC99,669999,666699,663399,660099,33FF99,33CC99,339999,336699,333399,330099,00FF99,00CC99,009999,006699,003399,000099,66FF66,66CC66,669966,666666,663366,660066,33FF66,33CC66,339966,336666,333366,330066,00FF66,00CC66,009966,006666,003366,000066,66FF33,66CC33,669933,666633,663333,660033,33FF33,33CC33,339933,336633,333333,330033,00FF33,00CC33,009933,006633,003333,000033,66FF00,66CC00,669900,666600,663300,660000,33FF00,33CC00,339900,336600,333300,330000,00FF00,00CC00,009900,006600,003300,000000', WHITE = '#ffffff', MESSAGES = {\n                apply: 'Apply',\n                cancel: 'Cancel',\n                noColor: 'no color',\n                clearColor: 'Clear color',\n                previewInput: 'Color Hexadecimal Code'\n            }, NS = '.kendoColorTools', CLICK_NS = 'click' + NS, KEYDOWN_NS = 'keydown' + NS, browser = kendo.support.browser, isIE8 = browser.msie && browser.version < 9;\n        var ColorSelector = Widget.extend({\n            init: function (element, options) {\n                var that = this, ariaId;\n                Widget.fn.init.call(that, element, options);\n                element = that.element;\n                options = that.options;\n                that._value = options.value = parseColor(options.value);\n                that._tabIndex = element.attr('tabIndex') || 0;\n                ariaId = that._ariaId = options.ariaId;\n                if (ariaId) {\n                    element.attr('aria-labelledby', ariaId);\n                }\n                if (options._standalone) {\n                    that._triggerSelect = that._triggerChange;\n                }\n            },\n            options: {\n                name: 'ColorSelector',\n                value: null,\n                _standalone: true\n            },\n            events: [\n                'change',\n                'select',\n                'cancel'\n            ],\n            color: function (value) {\n                if (value !== undefined) {\n                    this._value = parseColor(value);\n                    this._updateUI(this._value);\n                }\n                return this._value;\n            },\n            value: function (color) {\n                color = this.color(color);\n                if (color) {\n                    if (this.options.opacity) {\n                        color = color.toCssRgba();\n                    } else {\n                        color = color.toCss();\n                    }\n                }\n                return color || null;\n            },\n            enable: function (enable) {\n                if (arguments.length === 0) {\n                    enable = true;\n                }\n                $('.k-disabled-overlay', this.wrapper).remove();\n                if (!enable) {\n                    this.wrapper.append('<div class=\\'k-disabled-overlay\\'></div>');\n                }\n                this._onEnable(enable);\n            },\n            _select: function (color, nohooks) {\n                var prev = this._value;\n                color = this.color(color);\n                if (!nohooks) {\n                    this.element.trigger('change');\n                    if (!color.equals(prev)) {\n                        this.trigger('change', { value: this.value() });\n                    } else if (!this._standalone) {\n                        this.trigger('cancel');\n                    }\n                }\n            },\n            _triggerSelect: function (color) {\n                triggerEvent(this, 'select', color);\n            },\n            _triggerChange: function (color) {\n                triggerEvent(this, 'change', color);\n            },\n            destroy: function () {\n                if (this.element) {\n                    this.element.off(NS);\n                }\n                if (this.wrapper) {\n                    this.wrapper.off(NS).find('*').off(NS);\n                }\n                this.wrapper = null;\n                Widget.fn.destroy.call(this);\n            },\n            _updateUI: $.noop,\n            _selectOnHide: function () {\n                return null;\n            },\n            _cancel: function () {\n                this.trigger('cancel');\n            }\n        });\n        function triggerEvent(self, type, color) {\n            color = parseColor(color);\n            if (color && !color.equals(self.color())) {\n                if (type == 'change') {\n                    self._value = color;\n                }\n                if (color.a != 1) {\n                    color = color.toCssRgba();\n                } else {\n                    color = color.toCss();\n                }\n                self.trigger(type, { value: color });\n            }\n        }\n        var ColorPalette = ColorSelector.extend({\n            init: function (element, options) {\n                var that = this;\n                ColorSelector.fn.init.call(that, element, options);\n                element = that.wrapper = that.element;\n                options = that.options;\n                var colors = options.palette;\n                if (colors == 'websafe') {\n                    colors = WEBPALETTE;\n                    options.columns = 18;\n                } else if (colors == 'basic') {\n                    colors = SIMPLEPALETTE;\n                }\n                if (typeof colors == 'string') {\n                    colors = colors.split(',');\n                }\n                if ($.isArray(colors)) {\n                    colors = $.map(colors, function (x) {\n                        return parseColor(x);\n                    });\n                }\n                that._selectedID = (options.ariaId || kendo.guid()) + '_selected';\n                element.addClass('k-widget k-colorpalette').attr('role', 'grid').attr('aria-readonly', 'true').append($(that._template({\n                    colors: colors,\n                    columns: options.columns,\n                    tileSize: options.tileSize,\n                    value: that._value,\n                    id: options.ariaId\n                }))).on(CLICK_NS, '.k-item', function (ev) {\n                    that._select($(ev.currentTarget).css(BACKGROUNDCOLOR));\n                }).attr('tabIndex', that._tabIndex).on(KEYDOWN_NS, bind(that._keydown, that));\n                var tileSize = options.tileSize, width, height;\n                if (tileSize) {\n                    if (/number|string/.test(typeof tileSize)) {\n                        width = height = parseFloat(tileSize);\n                    } else if (typeof tileSize == 'object') {\n                        width = parseFloat(tileSize.width);\n                        height = parseFloat(tileSize.height);\n                    } else {\n                        throw new Error('Unsupported value for the \\'tileSize\\' argument');\n                    }\n                    element.find('.k-item').css({\n                        width: width,\n                        height: height\n                    });\n                }\n            },\n            focus: function () {\n                if (this.wrapper && !this.wrapper.is('[unselectable=\\'on\\']')) {\n                    this.wrapper.focus();\n                }\n            },\n            options: {\n                name: 'ColorPalette',\n                columns: 10,\n                tileSize: null,\n                palette: 'basic'\n            },\n            _onEnable: function (enable) {\n                if (enable) {\n                    this.wrapper.attr('tabIndex', this._tabIndex);\n                } else {\n                    this.wrapper.removeAttr('tabIndex');\n                }\n            },\n            _keydown: function (e) {\n                var selected, wrapper = this.wrapper, items = wrapper.find('.k-item'), current = items.filter('.' + ITEMSELECTEDCLASS).get(0), keyCode = e.keyCode;\n                if (keyCode == KEYS.LEFT) {\n                    selected = relative(items, current, -1);\n                } else if (keyCode == KEYS.RIGHT) {\n                    selected = relative(items, current, 1);\n                } else if (keyCode == KEYS.DOWN) {\n                    selected = relative(items, current, this.options.columns);\n                } else if (keyCode == KEYS.UP) {\n                    selected = relative(items, current, -this.options.columns);\n                } else if (keyCode == KEYS.ENTER) {\n                    preventDefault(e);\n                    if (current) {\n                        this._select($(current).css(BACKGROUNDCOLOR));\n                    }\n                } else if (keyCode == KEYS.ESC) {\n                    this._cancel();\n                }\n                if (selected) {\n                    preventDefault(e);\n                    this._current(selected);\n                    try {\n                        var color = parseColor(selected.css(BACKGROUNDCOLOR));\n                        this._triggerSelect(color);\n                    } catch (ex) {\n                    }\n                }\n            },\n            _current: function (item) {\n                this.wrapper.find('.' + ITEMSELECTEDCLASS).removeClass(ITEMSELECTEDCLASS).attr('aria-selected', false).removeAttr('id');\n                $(item).addClass(ITEMSELECTEDCLASS).attr('aria-selected', true).attr('id', this._selectedID);\n                this.element.removeAttr('aria-activedescendant').attr('aria-activedescendant', this._selectedID);\n            },\n            _updateUI: function (color) {\n                var item = null;\n                this.wrapper.find('.k-item').each(function () {\n                    var c = parseColor($(this).css(BACKGROUNDCOLOR));\n                    if (c && c.equals(color)) {\n                        item = this;\n                        return false;\n                    }\n                });\n                this._current(item);\n            },\n            _template: kendo.template('<table class=\"k-palette k-reset\" role=\"presentation\"><tr role=\"row\">' + '# for (var i = 0; i < colors.length; ++i) { #' + '# var selected = colors[i].equals(value); #' + '# if (i && i % columns == 0) { # </tr><tr role=\"row\"> # } #' + '<td role=\"gridcell\" unselectable=\"on\" style=\"background-color:#= colors[i].toCss() #\"' + '#= selected ? \" aria-selected=true\" : \"\" # ' + '#=(id && i === 0) ? \"id=\\\\\"\"+id+\"\\\\\" \" : \"\" # ' + 'class=\"k-item#= selected ? \" ' + ITEMSELECTEDCLASS + '\" : \"\" #\" ' + 'aria-label=\"#= colors[i].toCss() #\"></td>' + '# } #' + '</tr></table>')\n        });\n        var FlatColorPicker = ColorSelector.extend({\n            init: function (element, options) {\n                var that = this;\n                ColorSelector.fn.init.call(that, element, options);\n                options = that.options;\n                options.messages = options.options ? $.extend(that.options.messages, options.options.messages) : that.options.messages;\n                element = that.element;\n                that.wrapper = element.addClass('k-widget k-flatcolorpicker').append(that._template(options));\n                that._hueElements = $('.k-hsv-rectangle, .k-transparency-slider .k-slider-track', element);\n                that._selectedColor = $('.k-selected-color-display', element);\n                that._colorAsText = $('input.k-color-value', element);\n                that._sliders();\n                that._hsvArea();\n                that._updateUI(that._value || parseColor('#f00'));\n                element.find('input.k-color-value').on(KEYDOWN_NS, function (ev) {\n                    var input = this;\n                    if (ev.keyCode == KEYS.ENTER) {\n                        try {\n                            var color = parseColor(input.value);\n                            var val = that.color();\n                            that._select(color, color.equals(val));\n                        } catch (ex) {\n                            $(input).addClass('k-state-error');\n                        }\n                    } else if (that.options.autoupdate) {\n                        setTimeout(function () {\n                            var color = parseColor(input.value, true);\n                            if (color) {\n                                that._updateUI(color, true);\n                            }\n                        }, 10);\n                    }\n                }).end().on(CLICK_NS, '.k-controls button.apply', function () {\n                    if (that.options._clearedColor) {\n                        that.trigger('change');\n                    } else {\n                        that._select(that._getHSV());\n                    }\n                }).on(CLICK_NS, '.k-controls button.cancel', function () {\n                    that._updateUI(that.color());\n                    that._cancel();\n                });\n                if (isIE8) {\n                    that._applyIEFilter();\n                }\n            },\n            destroy: function () {\n                this._hueSlider.destroy();\n                if (this._opacitySlider) {\n                    this._opacitySlider.destroy();\n                }\n                this._hueSlider = this._opacitySlider = this._hsvRect = this._hsvHandle = this._hueElements = this._selectedColor = this._colorAsText = null;\n                ColorSelector.fn.destroy.call(this);\n            },\n            options: {\n                name: 'FlatColorPicker',\n                opacity: false,\n                buttons: false,\n                input: true,\n                preview: true,\n                clearButton: false,\n                autoupdate: true,\n                messages: MESSAGES\n            },\n            _applyIEFilter: function () {\n                var track = this.element.find('.k-hue-slider .k-slider-track')[0], url = track.currentStyle.backgroundImage;\n                url = url.replace(/^url\\([\\'\\\"]?|[\\'\\\"]?\\)$/g, '');\n                track.style.filter = 'progid:DXImageTransform.Microsoft.AlphaImageLoader(src=\\'' + url + '\\', sizingMethod=\\'scale\\')';\n            },\n            _sliders: function () {\n                var that = this, element = that.element, hueSlider = element.find('.k-hue-slider'), opacitySlider = element.find('.k-transparency-slider');\n                function hueChange(e) {\n                    that._updateUI(that._getHSV(e.value, null, null, null));\n                }\n                hueSlider.attr('aria-label', 'hue saturation');\n                that._hueSlider = hueSlider.kendoSlider({\n                    min: 0,\n                    max: 360,\n                    tickPlacement: 'none',\n                    showButtons: false,\n                    slide: hueChange,\n                    change: hueChange\n                }).data('kendoSlider');\n                function opacityChange(e) {\n                    that._updateUI(that._getHSV(null, null, null, e.value / 100));\n                }\n                opacitySlider.attr('aria-label', 'opacity');\n                that._opacitySlider = opacitySlider.kendoSlider({\n                    min: 0,\n                    max: 100,\n                    tickPlacement: 'none',\n                    showButtons: false,\n                    slide: opacityChange,\n                    change: opacityChange\n                }).data('kendoSlider');\n            },\n            _hsvArea: function () {\n                var that = this, element = that.element, hsvRect = element.find('.k-hsv-rectangle'), hsvHandle = hsvRect.find('.k-draghandle').attr('tabIndex', 0).on(KEYDOWN_NS, bind(that._keydown, that));\n                function update(x, y) {\n                    var offset = this.offset, dx = x - offset.left, dy = y - offset.top, rw = this.width, rh = this.height;\n                    dx = dx < 0 ? 0 : dx > rw ? rw : dx;\n                    dy = dy < 0 ? 0 : dy > rh ? rh : dy;\n                    that._svChange(dx / rw, 1 - dy / rh);\n                }\n                that._hsvEvents = new kendo.UserEvents(hsvRect, {\n                    global: true,\n                    press: function (e) {\n                        this.offset = kendo.getOffset(hsvRect);\n                        this.width = hsvRect.width();\n                        this.height = hsvRect.height();\n                        hsvHandle.focus();\n                        update.call(this, e.x.location, e.y.location);\n                    },\n                    start: function () {\n                        hsvRect.addClass('k-dragging');\n                        hsvHandle.focus();\n                    },\n                    move: function (e) {\n                        e.preventDefault();\n                        update.call(this, e.x.location, e.y.location);\n                    },\n                    end: function () {\n                        hsvRect.removeClass('k-dragging');\n                    }\n                });\n                that._hsvRect = hsvRect;\n                that._hsvHandle = hsvHandle;\n            },\n            _onEnable: function (enable) {\n                this._hueSlider.enable(enable);\n                if (this._opacitySlider) {\n                    this._opacitySlider.enable(enable);\n                }\n                this.wrapper.find('input').attr('disabled', !enable);\n                var handle = this._hsvRect.find('.k-draghandle');\n                if (enable) {\n                    handle.attr('tabIndex', this._tabIndex);\n                } else {\n                    handle.removeAttr('tabIndex');\n                }\n            },\n            _keydown: function (ev) {\n                var that = this;\n                function move(prop, d) {\n                    var c = that._getHSV();\n                    c[prop] += d * (ev.shiftKey ? 0.01 : 0.05);\n                    if (c[prop] < 0) {\n                        c[prop] = 0;\n                    }\n                    if (c[prop] > 1) {\n                        c[prop] = 1;\n                    }\n                    that._updateUI(c);\n                    preventDefault(ev);\n                }\n                function hue(d) {\n                    var c = that._getHSV();\n                    c.h += d * (ev.shiftKey ? 1 : 5);\n                    if (c.h < 0) {\n                        c.h = 0;\n                    }\n                    if (c.h > 359) {\n                        c.h = 359;\n                    }\n                    that._updateUI(c);\n                    preventDefault(ev);\n                }\n                switch (ev.keyCode) {\n                case KEYS.LEFT:\n                    if (ev.ctrlKey) {\n                        hue(-1);\n                    } else {\n                        move('s', -1);\n                    }\n                    break;\n                case KEYS.RIGHT:\n                    if (ev.ctrlKey) {\n                        hue(1);\n                    } else {\n                        move('s', 1);\n                    }\n                    break;\n                case KEYS.UP:\n                    move(ev.ctrlKey && that._opacitySlider ? 'a' : 'v', 1);\n                    break;\n                case KEYS.DOWN:\n                    move(ev.ctrlKey && that._opacitySlider ? 'a' : 'v', -1);\n                    break;\n                case KEYS.ENTER:\n                    that._select(that._getHSV());\n                    break;\n                case KEYS.F2:\n                    that.wrapper.find('input.k-color-value').focus().select();\n                    break;\n                case KEYS.ESC:\n                    that._cancel();\n                    break;\n                }\n            },\n            focus: function () {\n                this._hsvHandle.focus();\n            },\n            _getHSV: function (h, s, v, a) {\n                var rect = this._hsvRect, width = rect.width(), height = rect.height(), handlePosition = this._hsvHandle.position();\n                if (h == null) {\n                    h = this._hueSlider.value();\n                }\n                if (s == null) {\n                    s = handlePosition.left / width;\n                }\n                if (v == null) {\n                    v = 1 - handlePosition.top / height;\n                }\n                if (a == null) {\n                    a = this._opacitySlider ? this._opacitySlider.value() / 100 : 1;\n                }\n                return Color.fromHSV(h, s, v, a);\n            },\n            _svChange: function (s, v) {\n                var color = this._getHSV(null, s, v, null);\n                this._updateUI(color);\n            },\n            _updateUI: function (color, dontChangeInput) {\n                var that = this, rect = that._hsvRect;\n                if (!color) {\n                    return;\n                }\n                this._colorAsText.attr('title', that.options.messages.previewInput);\n                this._colorAsText.removeClass('k-state-error');\n                that._selectedColor.css(BACKGROUNDCOLOR, color.toDisplay());\n                if (!dontChangeInput) {\n                    that._colorAsText.val(that._opacitySlider ? color.toCssRgba() : color.toCss());\n                }\n                that._triggerSelect(color);\n                color = color.toHSV();\n                that._hsvHandle.css({\n                    left: color.s * rect.width() + 'px',\n                    top: (1 - color.v) * rect.height() + 'px'\n                });\n                that._hueElements.css(BACKGROUNDCOLOR, Color.fromHSV(color.h, 1, 1, 1).toCss());\n                that._hueSlider.value(color.h);\n                if (that._opacitySlider) {\n                    that._opacitySlider.value(100 * color.a);\n                }\n            },\n            _selectOnHide: function () {\n                return this.options.buttons ? null : this._getHSV();\n            },\n            _template: kendo.template('# if (preview) { #' + '<div class=\"k-selected-color\"><div class=\"k-selected-color-display\"><div class=\"k-color-input\"><input class=\"k-color-value\" ' + '# if (clearButton && !_standalone) { #' + 'placeholder=\"#: messages.noColor #\" ' + '# } #' + '#= !data.input ? \\'style=\"visibility: hidden;\"\\' : \"\" #>' + '# if (clearButton && !_standalone) { #' + '<span class=\"k-clear-color k-button k-bare\" title=\"#: messages.clearColor #\"></span>' + '# } #' + '</div></div></div>' + '# } #' + '# if (clearButton && !_standalone && !preview) { #' + '<div class=\"k-clear-color-container\"><span class=\"k-clear-color k-button k-bare\">#: messages.clearColor #</span></div>' + '# } #' + '<div class=\"k-hsv-rectangle\"><div class=\"k-hsv-gradient\"></div><div class=\"k-draghandle\"></div></div>' + '<input class=\"k-hue-slider\" />' + '# if (opacity) { #' + '<input class=\"k-transparency-slider\" />' + '# } #' + '# if (buttons) { #' + '<div unselectable=\"on\" class=\"k-controls\"><button class=\"k-button k-primary apply\">#: messages.apply #</button> <button class=\"k-button cancel\">#: messages.cancel #</button></div>' + '# } #')\n        });\n        function relative(array, element, delta) {\n            array = Array.prototype.slice.call(array);\n            var n = array.length;\n            var pos = array.indexOf(element);\n            if (pos < 0) {\n                return delta < 0 ? array[n - 1] : array[0];\n            }\n            pos += delta;\n            if (pos < 0) {\n                pos += n;\n            } else {\n                pos %= n;\n            }\n            return array[pos];\n        }\n        var ColorPicker = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                element = that.element;\n                var value = element.attr('value') || element.val();\n                if (value) {\n                    value = parseColor(value, true);\n                } else {\n                    value = parseColor(options.value, true);\n                }\n                that._value = options.value = value;\n                var content = that.wrapper = $(that._template(options));\n                element.hide().after(content);\n                if (element.is('input')) {\n                    element.appendTo(content);\n                    var label = element.closest('label');\n                    var id = element.attr('id');\n                    if (id) {\n                        label = label.add('label[for=\"' + id + '\"]');\n                    }\n                    label.click(function (ev) {\n                        that.open();\n                        ev.preventDefault();\n                    });\n                }\n                that._tabIndex = element.attr('tabIndex') || 0;\n                that.enable(!element.attr('disabled'));\n                var accesskey = element.attr('accesskey');\n                if (accesskey) {\n                    element.attr('accesskey', null);\n                    content.attr('accesskey', accesskey);\n                }\n                that.bind('activate', function (ev) {\n                    if (!ev.isDefaultPrevented()) {\n                        that.toggle();\n                    }\n                });\n                that._updateUI(value);\n            },\n            destroy: function () {\n                this.wrapper.off(NS).find('*').off(NS);\n                if (this._popup) {\n                    this._selector.destroy();\n                    this._popup.destroy();\n                }\n                this._selector = this._popup = this.wrapper = null;\n                Widget.fn.destroy.call(this);\n            },\n            enable: function (enable) {\n                var that = this, wrapper = that.wrapper, innerWrapper = wrapper.children('.k-picker-wrap'), arrow = innerWrapper.find('.k-select');\n                if (arguments.length === 0) {\n                    enable = true;\n                }\n                that.element.attr('disabled', !enable);\n                wrapper.attr('aria-disabled', !enable);\n                arrow.off(NS).on('mousedown' + NS, preventDefault);\n                wrapper.addClass('k-state-disabled').removeAttr('tabIndex').add('*', wrapper).off(NS);\n                if (enable) {\n                    wrapper.removeClass('k-state-disabled').attr('tabIndex', that._tabIndex).on('mouseenter' + NS, function () {\n                        innerWrapper.addClass('k-state-hover');\n                    }).on('mouseleave' + NS, function () {\n                        innerWrapper.removeClass('k-state-hover');\n                    }).on('focus' + NS, function () {\n                        innerWrapper.addClass('k-state-focused');\n                    }).on('blur' + NS, function () {\n                        innerWrapper.removeClass('k-state-focused');\n                    }).on(KEYDOWN_NS, bind(that._keydown, that)).on(CLICK_NS, '.k-select', bind(that.toggle, that)).on(CLICK_NS, that.options.toolIcon ? '.k-tool-icon' : '.k-selected-color', function () {\n                        that.trigger('activate');\n                    });\n                } else {\n                    that.close();\n                }\n            },\n            _template: kendo.template('<span role=\"textbox\" aria-haspopup=\"true\" class=\"k-widget k-colorpicker\">' + '<span class=\"k-picker-wrap k-state-default\">' + '# if (toolIcon) { #' + '<span class=\"k-icon k-tool-icon #= toolIcon #\">' + '<span class=\"k-selected-color\"></span>' + '</span>' + '# } else { #' + '<span class=\"k-selected-color\"><span class=\"k-icon k-i-line\" style=\"display: none;\"></span></span>' + '# } #' + '<span class=\"k-select\" unselectable=\"on\" aria-label=\"select\">' + '<span class=\"k-icon k-i-arrow-60-down\"></span>' + '</span>' + '</span>' + '</span>'),\n            options: {\n                name: 'ColorPicker',\n                palette: null,\n                columns: 10,\n                toolIcon: null,\n                value: null,\n                messages: MESSAGES,\n                opacity: false,\n                buttons: true,\n                preview: true,\n                clearButton: false,\n                ARIATemplate: 'Current selected color is #=data || \"\"#'\n            },\n            events: [\n                'activate',\n                'change',\n                'select',\n                'open',\n                'close'\n            ],\n            open: function () {\n                if (!this.element.prop('disabled')) {\n                    this._getPopup().open();\n                }\n            },\n            close: function () {\n                var selOptions = this._selector && this._selector.options || {};\n                selOptions._closing = true;\n                this._getPopup().close();\n                delete selOptions._closing;\n            },\n            toggle: function () {\n                if (!this.element.prop('disabled')) {\n                    this._getPopup().toggle();\n                }\n            },\n            _noColorIcon: function () {\n                return this.wrapper.find('.k-picker-wrap > .k-selected-color > .k-icon.k-i-line');\n            },\n            color: ColorSelector.fn.color,\n            value: ColorSelector.fn.value,\n            _select: ColorSelector.fn._select,\n            _triggerSelect: ColorSelector.fn._triggerSelect,\n            _isInputTypeColor: function () {\n                var el = this.element[0];\n                return /^input$/i.test(el.tagName) && /^color$/i.test(el.type);\n            },\n            _updateUI: function (value) {\n                var formattedValue = '';\n                if (value) {\n                    if (this._isInputTypeColor() || value.a == 1) {\n                        formattedValue = value.toCss();\n                    } else {\n                        formattedValue = value.toCssRgba();\n                    }\n                    this.element.val(formattedValue);\n                }\n                if (!this._ariaTemplate) {\n                    this._ariaTemplate = kendo.template(this.options.ARIATemplate);\n                }\n                this.wrapper.attr('aria-label', this._ariaTemplate(formattedValue));\n                this._triggerSelect(value);\n                this.wrapper.find('.k-selected-color').css(BACKGROUNDCOLOR, value ? value.toDisplay() : WHITE);\n                this._noColorIcon()[formattedValue ? 'hide' : 'show']();\n            },\n            _keydown: function (ev) {\n                var key = ev.keyCode;\n                if (this._getPopup().visible()) {\n                    if (key == KEYS.ESC) {\n                        this._selector._cancel();\n                    } else {\n                        this._selector._keydown(ev);\n                    }\n                    preventDefault(ev);\n                } else if (key == KEYS.ENTER || key == KEYS.DOWN) {\n                    this.open();\n                    preventDefault(ev);\n                }\n            },\n            _getPopup: function () {\n                var that = this, popup = that._popup;\n                if (!popup) {\n                    var options = that.options;\n                    var selectorType;\n                    if (options.palette) {\n                        selectorType = ColorPalette;\n                    } else {\n                        selectorType = FlatColorPicker;\n                    }\n                    options._standalone = false;\n                    delete options.select;\n                    delete options.change;\n                    delete options.cancel;\n                    var id = kendo.guid();\n                    var selector = that._selector = new selectorType($('<div id=\"' + id + '\"/>').appendTo(document.body), options);\n                    that.wrapper.attr('aria-owns', id);\n                    that._popup = popup = selector.wrapper.kendoPopup({\n                        anchor: that.wrapper,\n                        adjustSize: {\n                            width: 5,\n                            height: 0\n                        }\n                    }).data('kendoPopup');\n                    selector.element.find('.k-clear-color').kendoButton({\n                        icon: 'reset-color',\n                        click: function (e) {\n                            selector.options._clearedColor = true;\n                            that.value(null);\n                            that.element.val(null);\n                            that._updateUI(null);\n                            selector._colorAsText.val('');\n                            selector._hsvHandle.css({\n                                top: '0px',\n                                left: '0px'\n                            });\n                            selector._selectedColor.css(BACKGROUNDCOLOR, WHITE);\n                            that.trigger('change', { value: that.value() });\n                            e.preventDefault();\n                        }\n                    });\n                    selector.bind({\n                        select: function (ev) {\n                            that._updateUI(parseColor(ev.value));\n                            delete selector.options._clearedColor;\n                        },\n                        change: function () {\n                            if (!selector.options._clearedColor) {\n                                that._select(selector.color());\n                            }\n                            that.close();\n                        },\n                        cancel: function () {\n                            if (selector.options._clearedColor && !that.value() && selector.value()) {\n                                that._select(selector.color(), true);\n                            }\n                            that.close();\n                        }\n                    });\n                    popup.bind({\n                        close: function (ev) {\n                            if (that.trigger('close')) {\n                                ev.preventDefault();\n                                return;\n                            }\n                            that.wrapper.children('.k-picker-wrap').removeClass('k-state-focused');\n                            var color = selector._selectOnHide();\n                            var selectorColor = selector.value();\n                            var value = that.value();\n                            var options = selector.options;\n                            if (!color) {\n                                setTimeout(function () {\n                                    if (that.wrapper && !that.wrapper.is('[unselectable=\\'on\\']')) {\n                                        that.wrapper.focus();\n                                    }\n                                });\n                                if (!options._closing && options._clearedColor && !value && selectorColor) {\n                                    that._select(selectorColor, true);\n                                } else {\n                                    that._updateUI(that.color());\n                                }\n                            } else if (!(options._clearedColor && !value)) {\n                                that._select(color);\n                            }\n                        },\n                        open: function (ev) {\n                            if (that.trigger('open')) {\n                                ev.preventDefault();\n                            } else {\n                                that.wrapper.children('.k-picker-wrap').addClass('k-state-focused');\n                            }\n                        },\n                        activate: function () {\n                            selector._select(that.color(), true);\n                            selector.focus();\n                            that.wrapper.children('.k-picker-wrap').addClass('k-state-focused');\n                        }\n                    });\n                }\n                return popup;\n            }\n        });\n        function preventDefault(ev) {\n            ev.preventDefault();\n        }\n        function bind(callback, obj) {\n            return function () {\n                return callback.apply(obj, arguments);\n            };\n        }\n        ui.plugin(ColorPalette);\n        ui.plugin(FlatColorPicker);\n        ui.plugin(ColorPicker);\n    }(jQuery, parseInt));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}