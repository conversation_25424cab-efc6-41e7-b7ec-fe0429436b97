/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(o,define){define("util/text-metrics.min",["kendo.core.min"],o)}(function(){!function(o){function r(o){return(o+"").replace(n,l)}function e(o){var r,e=[];for(r in o)e.push(r+o[r]);return e.sort().join("")}function t(o){var r,e=2166136261;for(r=0;r<o.length;++r)e+=(e<<1)+(e<<4)+(e<<7)+(e<<8)+(e<<24),e^=o.charCodeAt(r);return e>>>0}function d(){return{width:0,height:0,baseline:0}}function a(o,r,e){return c.current.measure(o,r,e)}var s,n,l,i,u,c;window.kendo.util=window.kendo.util||{},s=kendo.Class.extend({init:function(o){this._size=o,this._length=0,this._map={}},put:function(o,r){var e=this._map,t={key:o,value:r};e[o]=t,this._head?(this._tail.newer=t,t.older=this._tail,this._tail=t):this._head=this._tail=t,this._length>=this._size?(e[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(o){var r=this._map[o];if(r)return r===this._head&&r!==this._tail&&(this._head=r.newer,this._head.older=null),r!==this._tail&&(r.older&&(r.older.newer=r.newer,r.newer.older=r.older),r.older=this._tail,r.newer=null,this._tail.newer=r,this._tail=r),r.value}}),n=/\r?\n|\r|\t/g,l=" ",i={baselineMarkerSize:1},"undefined"!=typeof document&&(u=document.createElement("div"),u.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),c=kendo.Class.extend({init:function(r){this._cache=new s(1e3),this.options=o.extend({},i,r)},measure:function(o,a,s){var n,l,i,c,w,C,g,h,p;if(void 0===s&&(s={}),!o)return d();if(n=e(a),l=t(o+n),i=this._cache.get(l))return i;c=d(),w=s.box||u,C=this._baselineMarker().cloneNode(!1);for(g in a)h=a[g],void 0!==h&&(w.style[g]=h);return p=s.normalizeText!==!1?r(o):o+"",w.textContent=p,w.appendChild(C),document.body.appendChild(w),p.length&&(c.width=w.offsetWidth-this.options.baselineMarkerSize,c.height=w.offsetHeight,c.baseline=C.offsetTop+this.options.baselineMarkerSize),c.width>0&&c.height>0&&this._cache.put(l,c),w.parentNode.removeChild(w),c},_baselineMarker:function(){var o=document.createElement("div");return o.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",o}}),c.current=new c,kendo.deepExtend(kendo.util,{LRUCache:s,TextMetrics:c,measureText:a,objectKey:e,hashKey:t,normalizeText:r})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(o,r,e){(e||r)()}),function(o,define){define("kendo.dataviz.qrcode.min",["kendo.dataviz.core.min","kendo.drawing.min"],o)}(function(){return function(o,r){function e(o){return parseInt(o,2)}function t(o,r){var e=(+o).toString(2);return e.length<r&&(e=Array(r-e.length+1).join(0)+e),e}function d(o,r){for(var e=[],t=0;t<o.length;)e.push(o.substring(t,t+r)),t+=r;return e}function a(o,r,e,t){for(var d=0;d<o.length;d++)o[d][e][t]=r}function s(o,r,e,t){for(var d=0;d<ko.length;d++)o[d][e][t]=ko[d](e,t)?1^r:parseInt(r,10)}function n(){var o,r;for(r=1;r<255;r++)o=2*so[r-1],o>255&&(o=285^o),so[r]=o,ao[o]=r;o=2*so[r-1]^285,so[r]=o,so[-1]=0}function l(){var o,r,e,t=68;for(o=2;o<=t;o++)r=no[o-1],e=[o,0],no[o]=D(r,e)}function i(o,e){var t=[],d=o.length-1;do t[d]=so[(o[d]+e)%255],d--;while(o[d]!==r);return t}function u(o,r){return parseInt(o.charAt(r),10)}function c(o,r,e,t,d){r[o][t]=(r[o][t]<<1^d)%128,r[o][t]==fo&&(e[o]+=40)}function w(o,r,e,t,d,a){e[o][a]==t?d[o][a]++:(e[o][a]=t,d[o][a]>=5&&(r[o]+=3+d[o][a]-5),d[o][a]=1)}function C(o,r){var e=Math.floor(o/r*100),t=e%5,d=Math.abs(e-t-50),a=Math.abs(e+5-t-50),s=10*Math.min(d/5,a/5);return s}var g,h,p,f,k,B,D,P,v,M,m,_,L,Q,S,x,E,H,A,I,b,R,T,y,O,U,V,z,N,F,Z,K,j,W,G=window.kendo,$=o.extend,q=G.drawing,X=G.dataviz,J=G.ui.Widget,Y=X.Box2D,oo="0000",ro="numeric",eo="alphanumeric",to="byte",ao={1:0},so={0:1},no=[[1,0],[1,25,0]],lo={15:20,16:20,18:24,19:24,22:20,24:22,26:24,28:20,30:20,31:24,32:28,33:24,36:18,37:22,39:20,40:24},io=[{L:{groups:[[1,19]],totalDataCodewords:19,errorCodewordsPerBlock:7},M:{groups:[[1,16]],totalDataCodewords:16,errorCodewordsPerBlock:10},Q:{groups:[[1,13]],totalDataCodewords:13,errorCodewordsPerBlock:13},H:{groups:[[1,9]],totalDataCodewords:9,errorCodewordsPerBlock:17}},{L:{groups:[[1,34]],totalDataCodewords:34,errorCodewordsPerBlock:10},M:{groups:[[1,28]],totalDataCodewords:28,errorCodewordsPerBlock:16},Q:{groups:[[1,22]],totalDataCodewords:22,errorCodewordsPerBlock:22},H:{groups:[[1,16]],totalDataCodewords:16,errorCodewordsPerBlock:28}},{L:{groups:[[1,55]],totalDataCodewords:55,errorCodewordsPerBlock:15},M:{groups:[[1,44]],totalDataCodewords:44,errorCodewordsPerBlock:26},Q:{groups:[[2,17]],totalDataCodewords:34,errorCodewordsPerBlock:18},H:{groups:[[2,13]],totalDataCodewords:26,errorCodewordsPerBlock:22}},{L:{groups:[[1,80]],totalDataCodewords:80,errorCodewordsPerBlock:20},M:{groups:[[2,32]],totalDataCodewords:64,errorCodewordsPerBlock:18},Q:{groups:[[2,24]],totalDataCodewords:48,errorCodewordsPerBlock:26},H:{groups:[[4,9]],totalDataCodewords:36,errorCodewordsPerBlock:16}},{L:{groups:[[1,108]],totalDataCodewords:108,errorCodewordsPerBlock:26},M:{groups:[[2,43]],totalDataCodewords:86,errorCodewordsPerBlock:24},Q:{groups:[[2,15],[2,16]],totalDataCodewords:62,errorCodewordsPerBlock:18},H:{groups:[[2,11],[2,12]],totalDataCodewords:46,errorCodewordsPerBlock:22}},{L:{groups:[[2,68]],totalDataCodewords:136,errorCodewordsPerBlock:18},M:{groups:[[4,27]],totalDataCodewords:108,errorCodewordsPerBlock:16},Q:{groups:[[4,19]],totalDataCodewords:76,errorCodewordsPerBlock:24},H:{groups:[[4,15]],totalDataCodewords:60,errorCodewordsPerBlock:28}},{L:{groups:[[2,78]],totalDataCodewords:156,errorCodewordsPerBlock:20},M:{groups:[[4,31]],totalDataCodewords:124,errorCodewordsPerBlock:18},Q:{groups:[[2,14],[4,15]],totalDataCodewords:88,errorCodewordsPerBlock:18},H:{groups:[[4,13],[1,14]],totalDataCodewords:66,errorCodewordsPerBlock:26}},{L:{groups:[[2,97]],totalDataCodewords:194,errorCodewordsPerBlock:24},M:{groups:[[2,38],[2,39]],totalDataCodewords:154,errorCodewordsPerBlock:22},Q:{groups:[[4,18],[2,19]],totalDataCodewords:110,errorCodewordsPerBlock:22},H:{groups:[[4,14],[2,15]],totalDataCodewords:86,errorCodewordsPerBlock:26}},{L:{groups:[[2,116]],totalDataCodewords:232,errorCodewordsPerBlock:30},M:{groups:[[3,36],[2,37]],totalDataCodewords:182,errorCodewordsPerBlock:22},Q:{groups:[[4,16],[4,17]],totalDataCodewords:132,errorCodewordsPerBlock:20},H:{groups:[[4,12],[4,13]],totalDataCodewords:100,errorCodewordsPerBlock:24}},{L:{groups:[[2,68],[2,69]],totalDataCodewords:274,errorCodewordsPerBlock:18},M:{groups:[[4,43],[1,44]],totalDataCodewords:216,errorCodewordsPerBlock:26},Q:{groups:[[6,19],[2,20]],totalDataCodewords:154,errorCodewordsPerBlock:24},H:{groups:[[6,15],[2,16]],totalDataCodewords:122,errorCodewordsPerBlock:28}},{L:{groups:[[4,81]],totalDataCodewords:324,errorCodewordsPerBlock:20},M:{groups:[[1,50],[4,51]],totalDataCodewords:254,errorCodewordsPerBlock:30},Q:{groups:[[4,22],[4,23]],totalDataCodewords:180,errorCodewordsPerBlock:28},H:{groups:[[3,12],[8,13]],totalDataCodewords:140,errorCodewordsPerBlock:24}},{L:{groups:[[2,92],[2,93]],totalDataCodewords:370,errorCodewordsPerBlock:24},M:{groups:[[6,36],[2,37]],totalDataCodewords:290,errorCodewordsPerBlock:22},Q:{groups:[[4,20],[6,21]],totalDataCodewords:206,errorCodewordsPerBlock:26},H:{groups:[[7,14],[4,15]],totalDataCodewords:158,errorCodewordsPerBlock:28}},{L:{groups:[[4,107]],totalDataCodewords:428,errorCodewordsPerBlock:26},M:{groups:[[8,37],[1,38]],totalDataCodewords:334,errorCodewordsPerBlock:22},Q:{groups:[[8,20],[4,21]],totalDataCodewords:244,errorCodewordsPerBlock:24},H:{groups:[[12,11],[4,12]],totalDataCodewords:180,errorCodewordsPerBlock:22}},{L:{groups:[[3,115],[1,116]],totalDataCodewords:461,errorCodewordsPerBlock:30},M:{groups:[[4,40],[5,41]],totalDataCodewords:365,errorCodewordsPerBlock:24},Q:{groups:[[11,16],[5,17]],totalDataCodewords:261,errorCodewordsPerBlock:20},H:{groups:[[11,12],[5,13]],totalDataCodewords:197,errorCodewordsPerBlock:24}},{L:{groups:[[5,87],[1,88]],totalDataCodewords:523,errorCodewordsPerBlock:22},M:{groups:[[5,41],[5,42]],totalDataCodewords:415,errorCodewordsPerBlock:24},Q:{groups:[[5,24],[7,25]],totalDataCodewords:295,errorCodewordsPerBlock:30},H:{groups:[[11,12],[7,13]],totalDataCodewords:223,errorCodewordsPerBlock:24}},{L:{groups:[[5,98],[1,99]],totalDataCodewords:589,errorCodewordsPerBlock:24},M:{groups:[[7,45],[3,46]],totalDataCodewords:453,errorCodewordsPerBlock:28},Q:{groups:[[15,19],[2,20]],totalDataCodewords:325,errorCodewordsPerBlock:24},H:{groups:[[3,15],[13,16]],totalDataCodewords:253,errorCodewordsPerBlock:30}},{L:{groups:[[1,107],[5,108]],totalDataCodewords:647,errorCodewordsPerBlock:28},M:{groups:[[10,46],[1,47]],totalDataCodewords:507,errorCodewordsPerBlock:28},Q:{groups:[[1,22],[15,23]],totalDataCodewords:367,errorCodewordsPerBlock:28},H:{groups:[[2,14],[17,15]],totalDataCodewords:283,errorCodewordsPerBlock:28}},{L:{groups:[[5,120],[1,121]],totalDataCodewords:721,errorCodewordsPerBlock:30},M:{groups:[[9,43],[4,44]],totalDataCodewords:563,errorCodewordsPerBlock:26},Q:{groups:[[17,22],[1,23]],totalDataCodewords:397,errorCodewordsPerBlock:28},H:{groups:[[2,14],[19,15]],totalDataCodewords:313,errorCodewordsPerBlock:28}},{L:{groups:[[3,113],[4,114]],totalDataCodewords:795,errorCodewordsPerBlock:28},M:{groups:[[3,44],[11,45]],totalDataCodewords:627,errorCodewordsPerBlock:26},Q:{groups:[[17,21],[4,22]],totalDataCodewords:445,errorCodewordsPerBlock:26},H:{groups:[[9,13],[16,14]],totalDataCodewords:341,errorCodewordsPerBlock:26}},{L:{groups:[[3,107],[5,108]],totalDataCodewords:861,errorCodewordsPerBlock:28},M:{groups:[[3,41],[13,42]],totalDataCodewords:669,errorCodewordsPerBlock:26},Q:{groups:[[15,24],[5,25]],totalDataCodewords:485,errorCodewordsPerBlock:30},H:{groups:[[15,15],[10,16]],totalDataCodewords:385,errorCodewordsPerBlock:28}},{L:{groups:[[4,116],[4,117]],totalDataCodewords:932,errorCodewordsPerBlock:28},M:{groups:[[17,42]],totalDataCodewords:714,errorCodewordsPerBlock:26},Q:{groups:[[17,22],[6,23]],totalDataCodewords:512,errorCodewordsPerBlock:28},H:{groups:[[19,16],[6,17]],totalDataCodewords:406,errorCodewordsPerBlock:30}},{L:{groups:[[2,111],[7,112]],totalDataCodewords:1006,errorCodewordsPerBlock:28},M:{groups:[[17,46]],totalDataCodewords:782,errorCodewordsPerBlock:28},Q:{groups:[[7,24],[16,25]],totalDataCodewords:568,errorCodewordsPerBlock:30},H:{groups:[[34,13]],totalDataCodewords:442,errorCodewordsPerBlock:24}},{L:{groups:[[4,121],[5,122]],totalDataCodewords:1094,errorCodewordsPerBlock:30},M:{groups:[[4,47],[14,48]],totalDataCodewords:860,errorCodewordsPerBlock:28},Q:{groups:[[11,24],[14,25]],totalDataCodewords:614,errorCodewordsPerBlock:30},H:{groups:[[16,15],[14,16]],totalDataCodewords:464,errorCodewordsPerBlock:30}},{L:{groups:[[6,117],[4,118]],totalDataCodewords:1174,errorCodewordsPerBlock:30},M:{groups:[[6,45],[14,46]],totalDataCodewords:914,errorCodewordsPerBlock:28},Q:{groups:[[11,24],[16,25]],totalDataCodewords:664,errorCodewordsPerBlock:30},H:{groups:[[30,16],[2,17]],totalDataCodewords:514,errorCodewordsPerBlock:30}},{L:{groups:[[8,106],[4,107]],totalDataCodewords:1276,errorCodewordsPerBlock:26},M:{groups:[[8,47],[13,48]],totalDataCodewords:1e3,errorCodewordsPerBlock:28},Q:{groups:[[7,24],[22,25]],totalDataCodewords:718,errorCodewordsPerBlock:30},H:{groups:[[22,15],[13,16]],totalDataCodewords:538,errorCodewordsPerBlock:30}},{L:{groups:[[10,114],[2,115]],totalDataCodewords:1370,errorCodewordsPerBlock:28},M:{groups:[[19,46],[4,47]],totalDataCodewords:1062,errorCodewordsPerBlock:28},Q:{groups:[[28,22],[6,23]],totalDataCodewords:754,errorCodewordsPerBlock:28},H:{groups:[[33,16],[4,17]],totalDataCodewords:596,errorCodewordsPerBlock:30}},{L:{groups:[[8,122],[4,123]],totalDataCodewords:1468,errorCodewordsPerBlock:30},M:{groups:[[22,45],[3,46]],totalDataCodewords:1128,errorCodewordsPerBlock:28},Q:{groups:[[8,23],[26,24]],totalDataCodewords:808,errorCodewordsPerBlock:30},H:{groups:[[12,15],[28,16]],totalDataCodewords:628,errorCodewordsPerBlock:30}},{L:{groups:[[3,117],[10,118]],totalDataCodewords:1531,errorCodewordsPerBlock:30},M:{groups:[[3,45],[23,46]],totalDataCodewords:1193,errorCodewordsPerBlock:28},Q:{groups:[[4,24],[31,25]],totalDataCodewords:871,errorCodewordsPerBlock:30},H:{groups:[[11,15],[31,16]],totalDataCodewords:661,errorCodewordsPerBlock:30}},{L:{groups:[[7,116],[7,117]],totalDataCodewords:1631,errorCodewordsPerBlock:30},M:{groups:[[21,45],[7,46]],totalDataCodewords:1267,errorCodewordsPerBlock:28},Q:{groups:[[1,23],[37,24]],totalDataCodewords:911,errorCodewordsPerBlock:30},H:{groups:[[19,15],[26,16]],totalDataCodewords:701,errorCodewordsPerBlock:30}},{L:{groups:[[5,115],[10,116]],totalDataCodewords:1735,errorCodewordsPerBlock:30},M:{groups:[[19,47],[10,48]],totalDataCodewords:1373,errorCodewordsPerBlock:28},Q:{groups:[[15,24],[25,25]],totalDataCodewords:985,errorCodewordsPerBlock:30},H:{groups:[[23,15],[25,16]],totalDataCodewords:745,errorCodewordsPerBlock:30}},{L:{groups:[[13,115],[3,116]],totalDataCodewords:1843,errorCodewordsPerBlock:30},M:{groups:[[2,46],[29,47]],totalDataCodewords:1455,errorCodewordsPerBlock:28},Q:{groups:[[42,24],[1,25]],totalDataCodewords:1033,errorCodewordsPerBlock:30},H:{groups:[[23,15],[28,16]],totalDataCodewords:793,errorCodewordsPerBlock:30}},{L:{groups:[[17,115]],totalDataCodewords:1955,errorCodewordsPerBlock:30},M:{groups:[[10,46],[23,47]],totalDataCodewords:1541,errorCodewordsPerBlock:28},Q:{groups:[[10,24],[35,25]],totalDataCodewords:1115,errorCodewordsPerBlock:30},H:{groups:[[19,15],[35,16]],totalDataCodewords:845,errorCodewordsPerBlock:30}},{L:{groups:[[17,115],[1,116]],totalDataCodewords:2071,errorCodewordsPerBlock:30},M:{groups:[[14,46],[21,47]],totalDataCodewords:1631,errorCodewordsPerBlock:28},Q:{groups:[[29,24],[19,25]],totalDataCodewords:1171,errorCodewordsPerBlock:30},H:{groups:[[11,15],[46,16]],totalDataCodewords:901,errorCodewordsPerBlock:30}},{L:{groups:[[13,115],[6,116]],totalDataCodewords:2191,errorCodewordsPerBlock:30},M:{groups:[[14,46],[23,47]],totalDataCodewords:1725,errorCodewordsPerBlock:28},Q:{groups:[[44,24],[7,25]],totalDataCodewords:1231,errorCodewordsPerBlock:30},H:{groups:[[59,16],[1,17]],totalDataCodewords:961,errorCodewordsPerBlock:30}},{L:{groups:[[12,121],[7,122]],totalDataCodewords:2306,errorCodewordsPerBlock:30},M:{groups:[[12,47],[26,48]],totalDataCodewords:1812,errorCodewordsPerBlock:28},Q:{groups:[[39,24],[14,25]],totalDataCodewords:1286,errorCodewordsPerBlock:30},H:{groups:[[22,15],[41,16]],totalDataCodewords:986,errorCodewordsPerBlock:30}},{L:{groups:[[6,121],[14,122]],totalDataCodewords:2434,errorCodewordsPerBlock:30},M:{groups:[[6,47],[34,48]],totalDataCodewords:1914,errorCodewordsPerBlock:28},Q:{groups:[[46,24],[10,25]],totalDataCodewords:1354,errorCodewordsPerBlock:30},H:{groups:[[2,15],[64,16]],totalDataCodewords:1054,errorCodewordsPerBlock:30}},{L:{groups:[[17,122],[4,123]],totalDataCodewords:2566,errorCodewordsPerBlock:30},M:{groups:[[29,46],[14,47]],totalDataCodewords:1992,errorCodewordsPerBlock:28},Q:{groups:[[49,24],[10,25]],totalDataCodewords:1426,errorCodewordsPerBlock:30},H:{groups:[[24,15],[46,16]],totalDataCodewords:1096,errorCodewordsPerBlock:30}},{L:{groups:[[4,122],[18,123]],totalDataCodewords:2702,errorCodewordsPerBlock:30},M:{groups:[[13,46],[32,47]],totalDataCodewords:2102,errorCodewordsPerBlock:28},Q:{groups:[[48,24],[14,25]],totalDataCodewords:1502,errorCodewordsPerBlock:30},H:{groups:[[42,15],[32,16]],totalDataCodewords:1142,errorCodewordsPerBlock:30}},{L:{groups:[[20,117],[4,118]],totalDataCodewords:2812,errorCodewordsPerBlock:30},M:{groups:[[40,47],[7,48]],totalDataCodewords:2216,errorCodewordsPerBlock:28},Q:{groups:[[43,24],[22,25]],totalDataCodewords:1582,errorCodewordsPerBlock:30},H:{groups:[[10,15],[67,16]],totalDataCodewords:1222,errorCodewordsPerBlock:30}},{L:{groups:[[19,118],[6,119]],totalDataCodewords:2956,errorCodewordsPerBlock:30},M:{groups:[[18,47],[31,48]],totalDataCodewords:2334,errorCodewordsPerBlock:28},Q:{groups:[[34,24],[34,25]],totalDataCodewords:1666,errorCodewordsPerBlock:30},H:{groups:[[20,15],[61,16]],totalDataCodewords:1276,errorCodewordsPerBlock:30}}],uo=[1,0,1,1,1],co=[1,0,1],wo={L:"01",M:"00",Q:"11",H:"10"},Co="101010000010010",go="10100110111",ho="1111100100101",po=["11101100","00010001"],fo=93,ko=[function(o,r){return(o+r)%2===0},function(o){return o%2===0},function(o,r){return r%3===0},function(o,r){return(o+r)%3===0},function(o,r){return(Math.floor(o/2)+Math.floor(r/3))%2===0},function(o,r){return o*r%2+o*r%3===0},function(o,r){return(o*r%2+o*r%3)%2===0},function(o,r){return((o+r)%2+o*r%3)%2===0}],Bo=/^\d+/,Do="A-Z0-9 $%*+./:-",Po="A-Z $%*+./:-",vo=RegExp("^["+Po+"]+"),Mo=RegExp("^["+Do+"]+"),mo=RegExp("^[^"+Do+"]+"),_o=8,Lo=5,Qo=8,So=17,xo=9,Eo=16,Ho=Math.round,Ao=G.Class.extend({getVersionIndex:function(o){return o<10?0:o>26?2:1},getBitsCharacterCount:function(o){var r=this;return r.bitsInCharacterCount[r.getVersionIndex(o||40)]},getModeCountString:function(o,r){var e=this;return e.modeIndicator+t(o,e.getBitsCharacterCount(r))},encode:function(){},getStringBitsLength:function(){},getValue:function(){},modeIndicator:"",bitsInCharacterCount:[]}),Io={};Io[ro]=Ao.extend({bitsInCharacterCount:[10,12,14],modeIndicator:"0001",getValue:function(o){return parseInt(o,10)},encode:function(o,r){var e,a=this,s=d(o,3),n=a.getModeCountString(o.length,r);for(e=0;e<s.length-1;e++)n+=t(s[e],10);return n+t(s[e],1+3*s[e].length)},getStringBitsLength:function(o,r){var e=o%3;return 4+this.getBitsCharacterCount(r)+10*Math.floor(o/3)+3*e+(0===e?0:1)}}),Io[eo]=Ao.extend({characters:{0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,G:16,H:17,I:18,J:19,K:20,L:21,M:22,N:23,O:24,P:25,Q:26,R:27,S:28,T:29,U:30,V:31,W:32,X:33,Y:34,Z:35," ":36,$:37,"%":38,"*":39,"+":40,"-":41,".":42,"/":43,":":44},bitsInCharacterCount:[9,11,13],modeIndicator:"0010",getValue:function(o){return this.characters[o]},encode:function(o,r){var e,a,s=this,n=d(o,2),l=s.getModeCountString(o.length,r);for(a=0;a<n.length-1;a++)e=45*s.getValue(n[a].charAt(0))+s.getValue(n[a].charAt(1)),l+=t(e,11);return e=2==n[a].length?45*s.getValue(n[a].charAt(0))+s.getValue(n[a].charAt(1)):s.getValue(n[a].charAt(0)),l+t(e,1+5*n[a].length)},getStringBitsLength:function(o,r){return 4+this.getBitsCharacterCount(r)+11*Math.floor(o/2)+6*(o%2)}}),Io[to]=Ao.extend({bitsInCharacterCount:[8,16,16],modeIndicator:"0100",getValue:function(o){var r=o.charCodeAt(0);if(r<=127||160<=r&&r<=255)return r;throw Error("Unsupported character: "+o)},encode:function(o,r){var e,d=this,a=d.getModeCountString(o.length,r);for(e=0;e<o.length;e++)a+=t(d.getValue(o.charAt(e)),8);return a},getStringBitsLength:function(o,r){return 4+this.getBitsCharacterCount(r)+8*o}}),g={};for(h in Io)g[h]=new Io[h];p=function(o){var e=this,t=o.length-1,d=o.length-1,a=d,s=-1,n=0;e.move=function(){t+=s*n,n^=1,d=a-n},e.getNextCell=function(){for(;o[t][d]!==r;)e.move(),(t<0||t>=o.length)&&(s=-s,a-=8!=a?2:3,d=a,t=s<0?o.length-1:0);return{row:t,column:d}},e.getNextRemainderCell=function(){if(e.move(),o[t][d]===r)return{row:t,column:d}}},f=function(o,r){var e,t,d,a,n,l,i=new p(o[0]);for(a=0;a<r.length;a++)for(e=r[a],t=0;e.length>0;){for(n=0;n<e.length;n++)for(l=0;l<8;l++)d=i.getNextCell(),s(o,e[n][t].charAt(l),d.row,d.column);for(t++;e[0]&&t==e[0].length;)e.splice(0,1)}for(;d=i.getNextRemainderCell();)s(o,0,d.row,d.column)},k=function(o,r){for(var e=8*r,t=0,d=0;o.length<e&&t<oo.length;)o+=oo.charAt(t++);for(o.length%8!==0&&(o+=Array(9-o.length%8).join("0"));o.length<e;)o+=po[d],d^=1;return o},B=function(o,r){var e,t=[],d=o.length-2;for(e=d;e>=0;e--)t[e]=o[e]^r[e];return t},D=function(o,e){var t,d,a=[];for(t=0;t<o.length;t++)for(d=0;d<e.length;d++)a[t+d]=a[t+d]===r?(o[t]+(e[d]>=0?e[d]:0))%255:ao[so[a[t+d]]^so[(o[t]+e[d])%255]];return a},n(),l(),P=function(o,r){var e,d,a=no[r-1],s=Array(r).concat(o),n=Array(s.length-a.length).concat(a),l=o.length,u=[];for(d=0;d<l;d++)e=i(n,ao[s[s.length-1]]),n.splice(0,1),s=B(e,s);for(d=s.length-1;d>=0;d--)u[r-1-d]=t(s[d],8);return u},v=function(o,r){var t,d,a,s,n,l,i,u,c=0,w=[],C=[],g=r.groups;for(l=0;l<g.length;l++)for(a=g[l][0],i=0;i<a;i++){for(d=g[l][1],t=[],s=[],u=1;u<=d;u++)n=o.substring(c,c+8),t.push(n),s[d-u]=e(n),c+=8;w.push(t),C.push(P(s,r.errorCodewordsPerBlock))}return[w,C]},M=function(o,r,e,t,d){var a,s,n=Bo.exec(o),l=n?n[0]:"",i=vo.exec(o),u=i?i[0]:"",c=Mo.exec(o),w=c?c[0]:"";return l&&(l.length>=r||o.length==l.length||l.length>=e&&!Mo.test(o.charAt(l.length)))?(a=ro,s=l):w&&(o.length==w.length||w.length>=t||d==eo)?(a=eo,s=l||u):(a=to,s=w?w+mo.exec(o.substring(w.length))[0]:mo.exec(o)[0]),{mode:a,modeString:s}},m=function(o){var r,e,t=[],d=0;for(t.push(M(o,_o,Lo,Qo,r)),r=t[0].mode,o=o.substr(t[0].modeString.length);o.length>0;)e=M(o,So,xo,Eo,r),e.mode!=r?(r=e.mode,t.push(e),d++):t[d].modeString+=e.modeString,o=o.substr(e.modeString.length);return t},_=function(o){var r,e,t=0;for(e=0;e<o.length;e++)r=g[o[e].mode],t+=r.getStringBitsLength(o[e].modeString.length);return Math.ceil(t/8)},L=function(o,r){var e=0,t=io.length-1,d=Math.floor(io.length/2);do o<io[d][r].totalDataCodewords?t=d:e=d,d=e+Math.floor((t-e)/2);while(t-e>1);return o<=io[e][r].totalDataCodewords?d+1:t+1},Q=function(o,r){var e,t,d="";for(t=0;t<o.length;t++)e=g[o[t].mode],d+=e.encode(o[t].modeString,r);return d},S=function(o){var r,t,d=e(o),a="";if(0===d)return"101010000010010";for(r=x(e(o),go,15),t=0;t<r.length;t++)a+=r.charAt(t)^Co.charAt(t);return a},x=function(o,r,d){var a=e(r),s=r.length-1,n=o<<s,l=d-s,i=t(o,l),u=E(n,a);return u=i+t(u,s)},E=function(o,r){var e=r.toString(2).length,t=o.toString(2).length;do o^=r<<t-e,t=o.toString(2).length;while(t>=e);return o},H=function(o){var r,e,t=[],d=17+4*o;for(r=0;r<ko.length;r++)for(t[r]=Array(d),e=0;e<d;e++)t[r][e]=Array(d);return t},A=function(o,r){var e,t,d=o[0],s=0,n=r.length;for(e=0,t=8;e<=8;e++)6!==e&&a(o,u(r,n-1-s++),e,t);for(e=8,t=7;t>=0;t--)6!==t&&a(o,u(r,n-1-s++),e,t);for(s=0,t=d.length-1,e=8;t>=d.length-8;t--)a(o,u(r,n-1-s++),e,t);for(a(o,1,d.length-8,8),e=d.length-7,t=8;e<d.length;e++)a(o,u(r,n-1-s++),e,t)},I=function(o){return x(o,ho,18)},b=function(o,r){var e,t,d,s,n=o[0],l=n.length,i=0,c=l-11,w=l-11,C=0;for(s=0;s<r.length;s++)e=Math.floor(s/3),t=s%3,d=u(r,r.length-s-1),a(o,d,i+e,c+t),a(o,d,w+t,C+e)},R=function(o,r,e,t){var d,s,n,l=r.length+2,i=r.length+1;for(s=0;s<r.length;s++)for(n=s;n<l-s;n++)d=r[s],a(o,d,e+n,t+s),a(o,d,e+s,t+n),a(o,d,e+i-n,t+i-s),a(o,d,e+i-s,t+i-n)},T=function(o,r,e,t){var d=e,s=t,n=o[0];do a(o,0,d,t),a(o,0,e,s),d+=r[0],s+=r[1];while(d>=0&&d<n.length)},y=function(o){var r=o[0].length;R(o,uo,0,0),T(o,[-1,-1],7,7),R(o,uo,r-7,0),T(o,[1,-1],r-8,7),R(o,uo,0,r-7),T(o,[-1,1],7,r-8)},O=function(o,e){var t,d,a,s,n,l,i,u,c;if(!(e<2)){for(t=o[0],d=t.length,a=Math.floor(e/7),s=[6],i=0,(n=lo[e])?l=(d-13-n)/a:n=l=(d-13)/(a+1),s.push(s[i++]+n);s[i]+l<d;)s.push(s[i++]+l);for(u=0;u<s.length;u++)for(c=0;c<s.length;c++)t[s[u]][s[c]]===r&&R(o,co,s[u]-2,s[c]-2)}},U=function(o){var r,e=6,t=6,d=1,s=o[0].length;for(r=8;r<s-8;r++)a(o,d,e,r),a(o,d,r,t),d^=1},V=function(o){var r,e,t,d,a,s,n,l=[],i=[],u=[],g=[],h=[],p=0,f=1,k=o[0].length;for(e=0;e<o.length;e++)l[e]=0,u[e]=0,h[e]=[0,0],g[e]=[0,0],i[e]=[];for(e=0;e<k;e++)for(t=0;t<k;t++)for(d=0;d<o.length;d++)r=o[d],u[d]+=parseInt(r[e][t],10),i[d][p]===r[e][t]&&e+1<k&&t-1>=0&&r[e+1][t]==i[d][p]&&r[e+1][t-1]==i[d][p]&&(l[d]+=3),c(d,g,l,p,r[e][t]),c(d,g,l,f,r[t][e]),w(d,l,i,r[e][t],h,p),w(d,l,i,r[t][e],h,f);for(a=k*k,n=Number.MAX_VALUE,e=0;e<l.length;e++)l[e]+=C(u[e],a),l[e]<n&&(n=l[e],s=e);return s},z=function(o,r){this.dataString=o,this.version=r},N=function(){this.getEncodingResult=function(o,r){var e=m(o),t=_(e),d=L(t,r),a=Q(e,d);return new z(a,d)}},F=function(){this.mode=g[this.encodingMode]},F.fn=F.prototype={encodingMode:to,utfBOM:"111011111011101110111111",initialModeCountStringLength:20,getEncodingResult:function(o,r){var e=this,t=e.encode(o),d=e.getDataCodewordsCount(t),a=L(d,r),s=e.mode.getModeCountString(t.length/8,a)+t;return new z(s,a)},getDataCodewordsCount:function(o){var r=this,e=o.length,t=Math.ceil((r.initialModeCountStringLength+e)/8);return t},encode:function(o){var r,e=this,t=e.utfBOM;for(r=0;r<o.length;r++)t+=e.encodeCharacter(o.charCodeAt(r));return t},encodeCharacter:function(o){var r,e,d=this.getBytesCount(o),a=d-1,s="";if(1==d)s=t(o,8);else{for(r=8-d,e=0;e<a;e++)s=t(o>>6*e&63|128,8)+s;s=(o>>6*a|255>>r<<r).toString(2)+s}return s},getBytesCount:function(o){var r,e=this.ranges;for(r=0;r<e.length;r++)if(o<e[r])return r+1},ranges:[128,2048,65536,2097152,67108864]},Z=function(o){return o&&o.toLowerCase().indexOf("utf_8")>=0?new F:new N},K=function(o,r,e){var d,a,s,n=new Z(e),l=n.getEncodingResult(o,r),i=l.version,u=io[i-1][r],c=k(l.dataString,u.totalDataCodewords),w=v(c,u),C=H(i);return y(C),O(C,i),U(C),i>=7&&b(C,t(0,18)),A(C,t(0,15)),f(C,w),d=V(C),a=C[d],i>=7&&b([a],I(i)),s=wo[r]+t(d,3),A([a],S(s)),a},j={DEFAULT_SIZE:200,QUIET_ZONE_LENGTH:4,DEFAULT_ERROR_CORRECTION_LEVEL:"L",DEFAULT_BACKGROUND:"#fff",DEFAULT_DARK_MODULE_COLOR:"#000",MIN_BASE_UNIT_SIZE:1},W=J.extend({init:function(r,e){var t=this;J.fn.init.call(t,r,e),t.element=o(r),t.wrapper=t.element,t.element.addClass("k-qrcode"),t.surfaceWrap=o("<div />").css("position","relative").appendTo(this.element),t.surface=q.Surface.create(t.surfaceWrap,{type:t.options.renderAs}),t.setOptions(e)},redraw:function(){var o=this._getSize();this.surfaceWrap.css({width:o,height:o}),this.surface.clear(),this.createVisual(),this.surface.draw(this.visual)},getSize:function(){return G.dimensions(this.element)},_resize:function(){this.redraw()},createVisual:function(){this.visual=this._render()},exportVisual:function(){return this._render()},_render:function(){var o,r,e,t,d,a,s,n=this,l=n._value,i=n.options.border||{},u=n.options.padding||0,c=i.width||0;return i.width=c,s=new q.Group,l&&(e=K(l,n.options.errorCorrection,n.options.encoding),t=n._getSize(),a=t-2*(c+u),o=n._calculateBaseUnit(a,e.length),d=e.length*o,r=c+u+(a-d)/2,s.append(n._renderBackground(t,i)),s.append(n._renderMatrix(e,o,r))),s},_getSize:function(){var o,r,e,t=this;return t.options.size?o=parseInt(t.options.size,10):(r=t.element,e=Math.min(r.width(),r.height()),o=e>0?e:j.DEFAULT_SIZE),o},_calculateBaseUnit:function(o,r){var e=Math.floor(o/r);if(e<j.MIN_BASE_UNIT_SIZE)throw Error("Insufficient size.");return e*r>=o&&e-1>=j.MIN_BASE_UNIT_SIZE&&e--,e},_renderMatrix:function(o,r,e){var t,d,a,s,n,l,i,u,c=new q.MultiPath({fill:{color:this.options.color},stroke:null});for(t=0;t<o.length;t++)for(d=e+t*r,a=0;a<o.length;){for(;0===o[t][a]&&a<o.length;)a++;if(a<o.length){for(s=a;1==o[t][a];)a++;n=Ho(e+s*r),l=Ho(d),i=Ho(e+a*r),u=Ho(d+r),c.moveTo(n,l).lineTo(n,u).lineTo(i,u).lineTo(i,l).close()}}return c},_renderBackground:function(o,r){var e=new Y(0,0,o,o).unpad(r.width/2);return q.Path.fromRect(e.toRect(),{fill:{color:this.options.background},stroke:{color:r.color,width:r.width}})},setOptions:function(o){var e=this;o=o||{},e.options=$(e.options,o),o.value!==r&&(e._value=e.options.value+""),e.redraw()},value:function(o){var e=this;return o===r?e._value:(e._value=o+"",e.redraw(),r)},options:{name:"QRCode",renderAs:"svg",encoding:"ISO_8859_1",value:"",errorCorrection:j.DEFAULT_ERROR_CORRECTION_LEVEL,background:j.DEFAULT_BACKGROUND,color:j.DEFAULT_DARK_MODULE_COLOR,size:"",padding:0,border:{color:"",width:0}}}),X.ExportMixin.extend(W.fn),X.ui.plugin(W),G.deepExtend(X,{QRCode:W,QRCodeDefaults:j,QRCodeFunctions:{FreeCellVisitor:p,fillData:f,padDataString:k,generateErrorCodewords:P,xorPolynomials:B,getBlocks:v,multiplyPolynomials:D,chooseMode:M,getModes:m,getDataCodewordsCount:_,getVersion:L,getDataString:Q,encodeFormatInformation:S,encodeBCH:x,dividePolynomials:E,initMatrices:H,addFormatInformation:A,encodeVersionInformation:I,addVersionInformation:b,addCentricPattern:R,addFinderSeparator:T,addFinderPatterns:y,addAlignmentPatterns:O,addTimingFunctions:U,scoreMaskMatrixes:V,encodeData:K,UTF8Encoder:F},QRCodeFields:{modes:g,powersOfTwo:ao,powersOfTwoResult:so,generatorPolynomials:no}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(o,r,e){(e||r)()});
//# sourceMappingURL=kendo.dataviz.qrcode.min.js.map
