{"version": 3, "sources": ["kendo.filtercell.js"], "names": ["f", "define", "$", "undefined", "isNonValueFilter", "filter", "operator", "inArray", "nonValueOperators", "findFilterForField", "field", "i", "result", "filters", "isPlainObject", "hasOwnProperty", "isArray", "length", "removeFiltersForField", "expression", "grep", "removeDuplicates", "dataSelector", "dataTextField", "getter", "kendo", "e", "item", "text", "items", "index", "seen", "push", "window", "ui", "DataSource", "data", "Widget", "CHANGE", "BOOL", "ENUM", "STRING", "EQ", "NEQ", "proxy", "<PERSON><PERSON><PERSON>ell", "extend", "init", "element", "options", "wrapper", "that", "dataSource", "viewModel", "passedOptions", "first", "type", "operators", "input", "suggestDataSource", "fields", "target", "addClass", "this", "appendTo", "attr", "fn", "call", "model", "reader", "values", "_parse", "value", "parse", "defaultOperator", "observable", "operatorVisible", "val", "get", "_clearInProgress", "bind", "updateDsFilter", "initSuggestDataSource", "inputWidth", "width", "_getColumnTitle", "_setInputType", "showOperators", "_createOperatorDropDown", "css", "_createClearIcon", "template", "setAutoCompleteSource", "setComboBoxSource", "_refreshUI", "_refresh<PERSON><PERSON><PERSON>", "radioInput", "inputName", "labelTrue", "labelFalse", "_angularItems", "suggestionOperator", "delay", "<PERSON><PERSON><PERSON><PERSON>", "remove", "guid", "messages", "isTrue", "append", "clone", "isFalse", "column", "title", "prop", "dropdown", "operatorDropDown", "kendoDropDownList", "dataValueField", "open", "popup", "valuePrimitive", "aria<PERSON><PERSON><PERSON>", "find", "removeClass", "customDataSource", "group", "create", "_pageSize", "autoComplete", "setDataSource", "comboBox", "manuallyUpdatingVM", "set", "currentFilter", "prevented", "mergeResult", "toJSON", "logic", "trigger", "_merge", "idx", "clear", "html", "click", "clearFilter", "action", "elements", "closest", "angular", "destroy", "filterModel", "unbind", "events", "name", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "isnullorempty", "isnotnullorempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,oBACH,qBACA,mBACA,uBACA,iBACA,sBACDD,IACL,WAsaE,MA9ZC,UAAUE,EAAGC,GASV,QAASC,GAAiBC,GACtB,GAAIC,GAA6B,gBAAXD,GAAsBA,EAASA,EAAOC,QAC5D,OAAOJ,GAAEK,QAAQD,EAAUE,MAE/B,QAASC,GAAmBJ,EAAQK,GAApC,GAYaC,GACDC,EAZJC,IACJ,IAAIX,EAAEY,cAAcT,GAChB,GAAIA,EAAOU,eAAe,WACtBF,EAAUR,EAAOQ,YACd,IAAIR,EAAOK,OAASA,EACvB,MAAOL,EAMf,KAHIH,EAAEc,QAAQX,KACVQ,EAAUR,GAELM,EAAI,EAAGA,EAAIE,EAAQI,OAAQN,IAEhC,GADIC,EAASH,EAAmBI,EAAQF,GAAID,GAExC,MAAOE,GAInB,QAASM,GAAsBC,EAAYT,GACnCS,EAAWN,UACXM,EAAWN,QAAUX,EAAEkB,KAAKD,EAAWN,QAAS,SAAUR,GAEtD,MADAa,GAAsBb,EAAQK,GAC1BL,EAAOQ,QACAR,EAAOQ,QAAQI,OAEfZ,EAAOK,OAASA,KAKvC,QAASW,GAAiBC,EAAcC,GACpC,GAAIC,GAASC,EAAMD,OAAOD,GAAe,EACzC,OAAO,UAAUG,GAEb,IAFG,GAGKC,GAAuBC,EAF3BC,EAAQP,EAAaI,GAAId,KAAakB,EAAQ,EAAGC,KAC9CD,EAAQD,EAAMZ,QACbU,EAAOE,EAAMC,KAAUF,EAAOJ,EAAOG,GACpCI,EAAKhB,eAAea,KACrBhB,EAAOoB,KAAKL,GACZI,EAAKH,IAAQ,EAGrB,OAAOhB,IAvDlB,GACOa,GAAQQ,OAAOR,MAAOS,EAAKT,EAAMS,GAAIC,EAAaV,EAAMW,KAAKD,WAAYE,EAASH,EAAGG,OAAQC,EAAS,SAAUC,EAAO,UAAWC,EAAO,QAASC,EAAS,SAAUC,EAAK,cAAeC,EAAM,kBAAmBC,EAAQ1C,EAAE0C,MAAOpC,GAC/N,SACA,YACA,UACA,aACA,gBACA,oBAmDJqC,EAAaR,EAAOS,QACpBC,KAAM,SAAUC,EAASC,GAAnB,GAEEC,GACAC,EAAaC,EAAYC,EAAWC,EAAyBC,EAAOC,EAAMC,EAAsDC,EAChIC,EAYAC,EACAC,EAkBInD,CAVR,IAxBAsC,EAAU9C,EAAE8C,GAASc,SAAS,gBAC1BZ,EAAUa,KAAKb,QAAUhD,EAAE,WAAW8D,SAAShB,GAC/CG,EAAOY,KAA6BT,EAAgBL,EAAsBQ,EAAYN,EAAKM,UAAYR,EAAQQ,cAAiBC,EAAQP,EAAKO,MAAQxD,EAAE,YAAY+D,KAAKxC,EAAMwC,KAAK,QAAS,gBAAgBD,SAASd,GACrNS,EAAoBV,EAAUA,EAAQU,kBAAoB,KAC1DA,IACAV,EAAU/C,EAAE4C,UAAWG,GAAWU,wBAEtCtB,EAAO6B,GAAGnB,KAAKoB,KAAKhB,EAAMH,EAAQ,GAAIC,GAClCU,IACAR,EAAKF,QAAQU,kBAAoBA,GAErCV,EAAUE,EAAKF,QACfG,EAAaD,EAAKC,WAAaH,EAAQG,WACvCD,EAAKiB,MAAQhB,EAAWiB,OAAOD,MAC/BZ,EAAOP,EAAQO,KAAOf,EAClBmB,EAASnC,EAAMD,OAAO,uBAAuB,GAAM4B,OACnDS,EAASD,EAAOX,EAAQvC,OACxBmD,GAAUA,EAAOL,OACjBA,EAAOP,EAAQO,KAAOK,EAAOL,MAE7BP,EAAQqB,SACRrB,EAAQO,KAAOA,EAAOhB,GAE1BiB,EAAYA,EAAUD,IAASP,EAAQQ,UAAUD,IAC5CF,EAAchD,SACf,IAAKiD,IAASE,GAAW,CACrBR,EAAQ3C,SAAWiD,CACnB,OAGRJ,EAAKoB,OAAS,SAAUC,GACpB,MAAgB,OAATA,EAAgBA,EAAQ,GAAKA,GAEpCrB,EAAKiB,OAASjB,EAAKiB,MAAMR,SACrBlD,EAAQyC,EAAKiB,MAAMR,OAAOX,EAAQvC,OAClCA,GACIA,EAAM+D,QACNtB,EAAKoB,OAAS3B,EAAMlC,EAAM+D,MAAO/D,KAI7CyC,EAAKuB,gBAAkBzB,EAAQ3C,SAC/B6C,EAAKE,UAAYA,EAAY5B,EAAMkD,YAC/BrE,SAAU2C,EAAQ3C,SAClBkE,MAAO,KACPI,gBAAiB,WACb,GAAIC,GAAMd,KAAKe,IAAI,QACnB,OAAe,QAARD,GAAgBA,IAAQ1E,GAAoB,aAAP0E,GAAsBzE,EAAiB2D,KAAKe,IAAI,eAAiB3B,EAAK4B,oBAG1H1B,EAAU2B,KAAK1C,EAAQM,EAAMO,EAAK8B,eAAgB9B,IAC9CK,GAAQf,GACRU,EAAK+B,sBAAsBjC,GAEJ,OAAvBA,EAAQkC,aACRzB,EAAMI,SAAS,iBACfJ,EAAM0B,MAAMnC,EAAQkC,aAExBzB,EAAMO,KAAK,aAAcd,EAAKkC,mBAC9BlC,EAAKmC,cAAcrC,EAASO,GACxBA,GAAQjB,GAAQU,EAAQsC,iBAAkB,EAC1CpC,EAAKqC,wBAAwB/B,IAE7BvD,EAAE,6BAA6BuF,IAAI,UAAW,QAAQ7D,KAAK,MAAMoC,SAASd,GAC1EA,EAAQY,SAAS,sBAErBX,EAAKuC,mBACLjE,EAAMuD,KAAKjB,KAAKb,QAASG,GACrBG,GAAQf,IACHQ,EAAQ0C,UACTxC,EAAKyC,yBAGTpC,GAAQhB,GACRW,EAAK0C,kBAAkB1C,EAAKF,QAAQqB,QAExCnB,EAAK2C,aACL3C,EAAK4C,gBAAkBnD,EAAMO,EAAK2C,WAAY3C,GAC9CA,EAAKC,WAAW4B,KAAK1C,EAAQa,EAAK4C,kBAEtCT,cAAe,SAAUrC,EAASO,GAAnB,GAcHwC,GACA9C,EACA+C,EACAC,EAEAC,EAlBJhD,EAAOY,KAAML,EAAQP,EAAKO,KACC,mBAApBT,GAAQ0C,UACf1C,EAAQ0C,SAASxB,KAAKhB,EAAKE,WACvBL,QAASG,EAAKO,MACdN,WAAYD,EAAKQ,oBAErBR,EAAKiD,cAAc,YACZ5C,GAAQf,EACfiB,EAAMO,KAAKxC,EAAMwC,KAAK,QAAS,gBAAgBA,KAAKxC,EAAMwC,KAAK,cAAehB,EAAQ1B,eAAiB0B,EAAQvC,OAAOuD,KAAKxC,EAAMwC,KAAK,UAAWhB,EAAQoD,oBAAoBpC,KAAKxC,EAAMwC,KAAK,SAAUhB,EAAQqD,OAAOrC,KAAKxC,EAAMwC,KAAK,cAAehB,EAAQsD,WAAWtC,KAAKxC,EAAMwC,KAAK,oBAAoB,GAC7R,QAART,EACPE,EAAMO,KAAKxC,EAAMwC,KAAK,QAAS,cACxBT,GAAQjB,GACfmB,EAAM8C,SACFR,EAAa9F,EAAE,yBACfgD,EAAUC,EAAKD,QACf+C,EAAYxE,EAAMgF,OAClBP,EAAYhG,EAAE,YAAY0B,KAAKqB,EAAQyD,SAASC,QAAQC,OAAOZ,GACnEA,EAAW/B,KAAKxC,EAAMwC,KAAK,QAAS,iBAAiBA,KAAK,OAAQgC,GAAWpB,IAAI,QAC7EsB,EAAaD,EAAUW,QAAQjF,KAAKqB,EAAQyD,SAASI,SACzDd,EAAWa,QAAQhC,IAAI,SAASb,SAASmC,GACzCjD,EAAQ0D,QACJV,EACAC,KAEW,UAAR3C,EACPE,EAAMO,KAAKxC,EAAMwC,KAAK,QAAS,kBAAkBA,KAAK,QAASd,EAAKkC,mBAC7D7B,GAAQhB,GACfkB,EAAMO,KAAKxC,EAAMwC,KAAK,QAAS,YAAYA,KAAKxC,EAAMwC,KAAK,cAAe,QAAQA,KAAKxC,EAAMwC,KAAK,YAAY,GAAMA,KAAKxC,EAAMwC,KAAK,UAAW,YAAYA,KAAKxC,EAAMwC,KAAK,eAAgB,SAASA,KAAKxC,EAAMwC,KAAK,oBAAoB,IAGhPoB,gBAAiB,WACb,GAAI0B,GAAShD,KAAKd,QAAQ8D,MAC1B,OAAOA,GAASA,EAAOC,OAASD,EAAOrG,MAAQ,IAEnD8E,wBAAyB,SAAU/B,GAAV,GAEZwD,GAMLC,EAPArF,KAAYwB,EAAYU,KAAKV,SACjC,KAAS4D,IAAQxD,GACb5B,EAAMG,MACFJ,KAAM6B,EAAUwD,GAChBzC,MAAOyC,GAGXC,GAAWhH,EAAE,sCAAwCuB,EAAMwC,KAAK,QAAU,wBAAwBD,SAASD,KAAKb,SACpHa,KAAKoD,iBAAmBD,EAASE,mBAC7BhE,WAAYvB,EACZN,cAAe,OACf8F,eAAgB,QAChBC,KAAM,WACFvD,KAAKwD,MAAMvE,QAAQoC,MAAM,MAE7BoC,gBAAgB,IACjBpF,KAAK,qBACRiB,EAAU2B,KAAK,SAAU,WACrB,GAAIyC,GAAYhE,EAAUJ,EAAU/C,SACpC4G,GAASjD,KAAK,aAAcwD,KAEhC1D,KAAKoD,iBAAiBjE,QAAQwE,KAAK,sBAAsBC,YAAY,qBAAqB7D,SAAS,eAEvGoB,sBAAuB,SAAUjC,GAC7B,GAAIU,GAAoBV,EAAQU,iBAC1BA,aAA6BxB,MAC1Bc,EAAQ2E,kBAAoBjE,IAC7BA,EAAkBkE,MAAQ1H,GAE9BwD,EAAoBI,KAAKJ,kBAAoBxB,EAAW2F,OAAOnE,IAE9DV,EAAQ2E,mBACTjE,EAAkBoE,UAAY5H,EAC9BwD,EAAkBU,OAAOjC,KAAOf,EAAiBsC,EAAkBU,OAAOjC,KAAM2B,KAAKd,QAAQvC,QAEjGqD,KAAKJ,kBAAoBA,GAE7BiC,sBAAuB,WACnB,GAAIoC,GAAejE,KAAKL,MAAMtB,KAAK,oBAC/B4F,IACAA,EAAaC,cAAclE,KAAKJ,oBAGxCkC,kBAAmB,SAAUvB,GAAV,GACXlB,GAAajB,EAAW2F,QAAS1F,KAAMkC,IACvC4D,EAAWnE,KAAKL,MAAMtB,KAAK,gBAC3B8F,IACAA,EAASD,cAAc7E,IAG/B0C,WAAY,WACR,GAAI3C,GAAOY,KAAM1D,EAASI,EAAmB0C,EAAKC,WAAW/C,SAAU0D,KAAKd,QAAQvC,WAAc2C,EAAYF,EAAKE,SACnHF,GAAKgF,oBAAqB,EAC1B9H,EAASH,EAAE4C,QAAO,KAAUzC,GACxB8C,EAAKF,QAAQO,MAAQjB,GACjBc,EAAUmB,QAAUnE,EAAOmE,OAC3BrB,EAAKD,QAAQwE,KAAK,UAAUT,KAAK,WAAW,GAGhD5G,EAAOC,UACP+C,EAAU+E,IAAI,WAAY/H,EAAOC,UAErC+C,EAAU+E,IAAI,QAAS/H,EAAOmE,OAC9BrB,EAAKgF,oBAAqB,GAE9BlD,eAAgB,SAAUvD,GAAV,GAKR2G,GACAlH,EAIAmH,EAiBAC,EA1BApF,EAAOY,KAAMK,EAAQjB,EAAKE,SAC1BF,GAAKgF,oBAAiC,YAAXzG,EAAEhB,OAAuB0D,EAAMI,QAAUrE,IAAcC,EAAiBgE,IAAqB,YAAX1C,EAAEhB,OAAuByC,EAAK4B,kBAAoC,OAAhBX,EAAMI,QAGrK6D,EAAgBnI,EAAE4C,UAAWK,EAAKE,UAAUmF,UAAY9H,MAAOyC,EAAKF,QAAQvC,QAC5ES,GACAsH,MAAO,MACP5H,YAEAyH,GAAY,GACZD,EAAc7D,QAAUrE,GAAqC,OAAxBkI,EAAc7D,OAAkBpE,EAAiBiI,KAAmBtE,KAAKgB,oBAC9G5D,EAAWN,QAAQmB,KAAKqG,GACxBC,EAAYnF,EAAKuF,QAAQpG,GACrBjC,OAAQc,EACRT,MAAOyC,EAAKF,QAAQvC,UAGxByC,EAAK4B,kBAA4C,OAAxBsD,EAAc7D,SACvC8D,EAAYnF,EAAKuF,QAAQpG,GACrBjC,OAAQ,KACRK,MAAOyC,EAAKF,QAAQvC,SAGxB4H,IAGAC,EAAcpF,EAAKwF,OAAOxH,GAE1BgC,EAAKC,WAAW/C,OADhBkI,EAAY1H,QAAQI,OACGsH,SAK/BI,OAAQ,SAAUxH,GACd,GAAkFd,GAG3EuI,EAAK3H,EAHRkC,EAAOY,KAAM0E,EAAQtH,EAAWsH,OAAS,MAAO5H,EAAUM,EAAWN,QAAiBD,EAASuC,EAAKC,WAAW/C,WAC3GQ,WACA4H,MAAO,MAGf,KADAvH,EAAsBN,EAAQuC,EAAKF,QAAQvC,OACtCkI,EAAM,EAAG3H,EAASJ,EAAQI,OAAQ2H,EAAM3H,EAAQ2H,IACjDvI,EAASQ,EAAQ+H,GACjBvI,EAAOmE,MAAQrB,EAAKoB,OAAOlE,EAAOmE,MAyBtC,OAvBA3D,GAAUX,EAAEkB,KAAKP,EAAS,SAAUR,GAChC,MAAwB,KAAjBA,EAAOmE,OAAiC,OAAjBnE,EAAOmE,OAAkBpE,EAAiBC,KAExEQ,EAAQI,SACJL,EAAOC,QAAQI,QACfE,EAAWN,QAAUA,EACA,QAAjBD,EAAO6H,QACP7H,EAAOC,UACC4H,MAAO7H,EAAO6H,MACd5H,QAASD,EAAOC,UAExBD,EAAO6H,MAAQ,OAGf7H,EAAOC,QAAQmB,KADfnB,EAAQI,OAAS,EACGE,EAEAN,EAAQ,MAGhCD,EAAOC,QAAUA,EACjBD,EAAO6H,MAAQA,IAGhB7H,GAEX8E,iBAAkB,WACd,GAAIvC,GAAOY,IACX7D,GAAE,gEAAsEiD,EAAKF,QAAQyD,SAASmC,MAAQ,MAAM5E,KAAK,aAAcd,EAAKF,QAAQyD,SAASmC,OAAO5E,KAAKxC,EAAMwC,KAAK,QAAS,2BAA2B6E,KAAK,2CAA6CC,MAAMnG,EAAMO,EAAK6F,YAAa7F,IAAOa,SAASb,EAAKD,UAEzT8F,YAAa,WACTjF,KAAKgB,kBAAmB,EACpB3E,EAAiB2D,KAAKV,UAAU/C,WAChCyD,KAAKV,UAAU+E,IAAI,WAAYrE,KAAKW,iBAExCX,KAAKV,UAAU+E,IAAI,QAAS,MAC5BrE,KAAKgB,kBAAmB,GAE5BqB,cAAe,SAAU6C,GAAV,GACPC,GAAWnF,KAAKb,QAAQiG,QAAQ,MAAMrE,MACtCiC,EAAShD,KAAKd,QAAQ8D,MAC1BhD,MAAKqF,QAAQH,EAAQ,WACjB,OACIC,SAAUA,EACV9G,OAAS2E,OAAQA,QAI7BsC,QAAS,WACL,GAAIlG,GAAOY,IACXZ,GAAKmG,YAAc,KACnBnG,EAAKgE,iBAAmB,KACxBhE,EAAKiD,cAAc,WACfjD,EAAK4C,kBACL5C,EAAKC,WAAW4B,KAAK1C,EAAQa,EAAK4C,iBAClC5C,EAAK4C,gBAAkB,MAE3BtE,EAAM8H,OAAOpG,EAAKH,SAClBX,EAAO6B,GAAGmF,QAAQlF,KAAKhB,GACvB1B,EAAM4H,QAAQlG,EAAKH,UAEvBwG,QAASlH,GACTW,SACIwG,KAAM,aACNnD,MAAO,IACPC,UAAW,EACXpB,WAAY,KACZb,OAAQnE,EACRyH,kBAAkB,EAClBlH,MAAO,GACPa,cAAe,GACfiC,KAAM,SACNG,kBAAmB,KACnB0C,mBAAoB,aACpB/F,SAAU,KACViF,eAAe,EACfI,SAAU,KACVe,UACIC,OAAQ,UACRG,QAAS,WACTzG,OAAQ,SACRwI,MAAO,QACPvI,SAAU,YAEdmD,WACIiG,QACIC,GAAIjH,EACJkH,IAAKjH,EACLkH,WAAY,cACZC,SAAU,WACVC,eAAgB,mBAChBC,SAAU,YACVC,OAAQ,UACRC,UAAW,cACXC,QAAS,WACTC,WAAY,eACZC,cAAe,eACfC,iBAAkB,aAEtBC,QACIZ,GAAIjH,EACJkH,IAAKjH,EACL6H,IAAK,8BACLC,GAAI,kBACJC,IAAK,2BACLC,GAAI,eACJV,OAAQ,UACRC,UAAW,eAEfU,MACIjB,GAAIjH,EACJkH,IAAKjH,EACL6H,IAAK,uBACLC,GAAI,WACJC,IAAK,wBACLC,GAAI,YACJV,OAAQ,UACRC,UAAW,eAEfW,OACIlB,GAAIjH,EACJkH,IAAKjH,EACLsH,OAAQ,UACRC,UAAW,kBAK3BhI,GAAG4I,OAAOjI,IACZZ,OAAOR,MAAMsJ,QACR9I,OAAOR,OACE,kBAAVxB,SAAwBA,OAAO+K,IAAM/K,OAAS,SAAUgL,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.filtercell.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.filtercell', [\n        'kendo.autocomplete',\n        'kendo.datepicker',\n        'kendo.numerictextbox',\n        'kendo.combobox',\n        'kendo.dropdownlist'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'filtercell',\n        name: 'Row filter',\n        category: 'framework',\n        depends: ['autocomplete'],\n        advanced: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, DataSource = kendo.data.DataSource, Widget = ui.Widget, CHANGE = 'change', BOOL = 'boolean', ENUM = 'enums', STRING = 'string', EQ = 'Is equal to', NEQ = 'Is not equal to', proxy = $.proxy, nonValueOperators = [\n                'isnull',\n                'isnotnull',\n                'isempty',\n                'isnotempty',\n                'isnullorempty',\n                'isnotnullorempty'\n            ];\n        function isNonValueFilter(filter) {\n            var operator = typeof filter === 'string' ? filter : filter.operator;\n            return $.inArray(operator, nonValueOperators) > -1;\n        }\n        function findFilterForField(filter, field) {\n            var filters = [];\n            if ($.isPlainObject(filter)) {\n                if (filter.hasOwnProperty('filters')) {\n                    filters = filter.filters;\n                } else if (filter.field == field) {\n                    return filter;\n                }\n            }\n            if ($.isArray(filter)) {\n                filters = filter;\n            }\n            for (var i = 0; i < filters.length; i++) {\n                var result = findFilterForField(filters[i], field);\n                if (result) {\n                    return result;\n                }\n            }\n        }\n        function removeFiltersForField(expression, field) {\n            if (expression.filters) {\n                expression.filters = $.grep(expression.filters, function (filter) {\n                    removeFiltersForField(filter, field);\n                    if (filter.filters) {\n                        return filter.filters.length;\n                    } else {\n                        return filter.field != field;\n                    }\n                });\n            }\n        }\n        function removeDuplicates(dataSelector, dataTextField) {\n            var getter = kendo.getter(dataTextField, true);\n            return function (e) {\n                var items = dataSelector(e), result = [], index = 0, seen = {};\n                while (index < items.length) {\n                    var item = items[index++], text = getter(item);\n                    if (!seen.hasOwnProperty(text)) {\n                        result.push(item);\n                        seen[text] = true;\n                    }\n                }\n                return result;\n            };\n        }\n        var FilterCell = Widget.extend({\n            init: function (element, options) {\n                element = $(element).addClass('k-filtercell');\n                var wrapper = this.wrapper = $('<span/>').appendTo(element);\n                var that = this, dataSource, viewModel, passedOptions = options, first, type, operators = that.operators = options.operators || {}, input = that.input = $('<input/>').attr(kendo.attr('bind'), 'value: value').appendTo(wrapper);\n                var suggestDataSource = options ? options.suggestDataSource : null;\n                if (suggestDataSource) {\n                    options = $.extend({}, options, { suggestDataSource: {} });\n                }\n                Widget.fn.init.call(that, element[0], options);\n                if (suggestDataSource) {\n                    that.options.suggestDataSource = suggestDataSource;\n                }\n                options = that.options;\n                dataSource = that.dataSource = options.dataSource;\n                that.model = dataSource.reader.model;\n                type = options.type = STRING;\n                var fields = kendo.getter('reader.model.fields', true)(dataSource) || {};\n                var target = fields[options.field];\n                if (target && target.type) {\n                    type = options.type = target.type;\n                }\n                if (options.values) {\n                    options.type = type = ENUM;\n                }\n                operators = operators[type] || options.operators[type];\n                if (!passedOptions.operator) {\n                    for (first in operators) {\n                        options.operator = first;\n                        break;\n                    }\n                }\n                that._parse = function (value) {\n                    return value != null ? value + '' : value;\n                };\n                if (that.model && that.model.fields) {\n                    var field = that.model.fields[options.field];\n                    if (field) {\n                        if (field.parse) {\n                            that._parse = proxy(field.parse, field);\n                        }\n                    }\n                }\n                that.defaultOperator = options.operator;\n                that.viewModel = viewModel = kendo.observable({\n                    operator: options.operator,\n                    value: null,\n                    operatorVisible: function () {\n                        var val = this.get('value');\n                        return val !== null && val !== undefined && val != 'undefined' || isNonValueFilter(this.get('operator')) && !that._clearInProgress;\n                    }\n                });\n                viewModel.bind(CHANGE, proxy(that.updateDsFilter, that));\n                if (type == STRING) {\n                    that.initSuggestDataSource(options);\n                }\n                if (options.inputWidth !== null) {\n                    input.addClass('k-sized-input');\n                    input.width(options.inputWidth);\n                }\n                input.attr('aria-label', that._getColumnTitle());\n                that._setInputType(options, type);\n                if (type != BOOL && options.showOperators !== false) {\n                    that._createOperatorDropDown(operators);\n                } else {\n                    $('<div unselectable=\"on\" />').css('display', 'none').text('eq').appendTo(wrapper);\n                    wrapper.addClass('k-operator-hidden');\n                }\n                that._createClearIcon();\n                kendo.bind(this.wrapper, viewModel);\n                if (type == STRING) {\n                    if (!options.template) {\n                        that.setAutoCompleteSource();\n                    }\n                }\n                if (type == ENUM) {\n                    that.setComboBoxSource(that.options.values);\n                }\n                that._refreshUI();\n                that._refreshHandler = proxy(that._refreshUI, that);\n                that.dataSource.bind(CHANGE, that._refreshHandler);\n            },\n            _setInputType: function (options, type) {\n                var that = this, input = that.input;\n                if (typeof options.template == 'function') {\n                    options.template.call(that.viewModel, {\n                        element: that.input,\n                        dataSource: that.suggestDataSource\n                    });\n                    that._angularItems('compile');\n                } else if (type == STRING) {\n                    input.attr(kendo.attr('role'), 'autocomplete').attr(kendo.attr('text-field'), options.dataTextField || options.field).attr(kendo.attr('filter'), options.suggestionOperator).attr(kendo.attr('delay'), options.delay).attr(kendo.attr('min-length'), options.minLength).attr(kendo.attr('value-primitive'), true);\n                } else if (type == 'date') {\n                    input.attr(kendo.attr('role'), 'datepicker');\n                } else if (type == BOOL) {\n                    input.remove();\n                    var radioInput = $('<input type=\\'radio\\'/>');\n                    var wrapper = that.wrapper;\n                    var inputName = kendo.guid();\n                    var labelTrue = $('<label/>').text(options.messages.isTrue).append(radioInput);\n                    radioInput.attr(kendo.attr('bind'), 'checked:value').attr('name', inputName).val('true');\n                    var labelFalse = labelTrue.clone().text(options.messages.isFalse);\n                    radioInput.clone().val('false').appendTo(labelFalse);\n                    wrapper.append([\n                        labelTrue,\n                        labelFalse\n                    ]);\n                } else if (type == 'number') {\n                    input.attr(kendo.attr('role'), 'numerictextbox').attr('title', that._getColumnTitle());\n                } else if (type == ENUM) {\n                    input.attr(kendo.attr('role'), 'combobox').attr(kendo.attr('text-field'), 'text').attr(kendo.attr('suggest'), true).attr(kendo.attr('filter'), 'contains').attr(kendo.attr('value-field'), 'value').attr(kendo.attr('value-primitive'), true);\n                }\n            },\n            _getColumnTitle: function () {\n                var column = this.options.column;\n                return column ? column.title || column.field : '';\n            },\n            _createOperatorDropDown: function (operators) {\n                var items = [], viewModel = this.viewModel;\n                for (var prop in operators) {\n                    items.push({\n                        text: operators[prop],\n                        value: prop\n                    });\n                }\n                var dropdown = $('<input class=\"k-dropdown-operator\" ' + kendo.attr('bind') + '=\"value: operator\"/>').appendTo(this.wrapper);\n                this.operatorDropDown = dropdown.kendoDropDownList({\n                    dataSource: items,\n                    dataTextField: 'text',\n                    dataValueField: 'value',\n                    open: function () {\n                        this.popup.element.width(150);\n                    },\n                    valuePrimitive: true\n                }).data('kendoDropDownList');\n                viewModel.bind('change', function () {\n                    var ariaLabel = operators[viewModel.operator];\n                    dropdown.attr('aria-label', ariaLabel);\n                });\n                this.operatorDropDown.wrapper.find('.k-i-arrow-60-down').removeClass('k-i-arrow-60-down').addClass('k-i-filter');\n            },\n            initSuggestDataSource: function (options) {\n                var suggestDataSource = options.suggestDataSource;\n                if (!(suggestDataSource instanceof DataSource)) {\n                    if (!options.customDataSource && suggestDataSource) {\n                        suggestDataSource.group = undefined;\n                    }\n                    suggestDataSource = this.suggestDataSource = DataSource.create(suggestDataSource);\n                }\n                if (!options.customDataSource) {\n                    suggestDataSource._pageSize = undefined;\n                    suggestDataSource.reader.data = removeDuplicates(suggestDataSource.reader.data, this.options.field);\n                }\n                this.suggestDataSource = suggestDataSource;\n            },\n            setAutoCompleteSource: function () {\n                var autoComplete = this.input.data('kendoAutoComplete');\n                if (autoComplete) {\n                    autoComplete.setDataSource(this.suggestDataSource);\n                }\n            },\n            setComboBoxSource: function (values) {\n                var dataSource = DataSource.create({ data: values });\n                var comboBox = this.input.data('kendoComboBox');\n                if (comboBox) {\n                    comboBox.setDataSource(dataSource);\n                }\n            },\n            _refreshUI: function () {\n                var that = this, filter = findFilterForField(that.dataSource.filter(), this.options.field) || {}, viewModel = that.viewModel;\n                that.manuallyUpdatingVM = true;\n                filter = $.extend(true, {}, filter);\n                if (that.options.type == BOOL) {\n                    if (viewModel.value !== filter.value) {\n                        that.wrapper.find(':radio').prop('checked', false);\n                    }\n                }\n                if (filter.operator) {\n                    viewModel.set('operator', filter.operator);\n                }\n                viewModel.set('value', filter.value);\n                that.manuallyUpdatingVM = false;\n            },\n            updateDsFilter: function (e) {\n                var that = this, model = that.viewModel;\n                if (that.manuallyUpdatingVM || e.field == 'operator' && model.value === undefined && !isNonValueFilter(model) || e.field == 'operator' && that._clearInProgress && model.value !== null) {\n                    return;\n                }\n                var currentFilter = $.extend({}, that.viewModel.toJSON(), { field: that.options.field });\n                var expression = {\n                    logic: 'and',\n                    filters: []\n                };\n                var prevented = false;\n                if (currentFilter.value !== undefined && currentFilter.value !== null || isNonValueFilter(currentFilter) && !this._clearInProgress) {\n                    expression.filters.push(currentFilter);\n                    prevented = that.trigger(CHANGE, {\n                        filter: expression,\n                        field: that.options.field\n                    });\n                }\n                if (that._clearInProgress || currentFilter.value === null) {\n                    prevented = that.trigger(CHANGE, {\n                        filter: null,\n                        field: that.options.field\n                    });\n                }\n                if (prevented) {\n                    return;\n                }\n                var mergeResult = that._merge(expression);\n                if (mergeResult.filters.length) {\n                    that.dataSource.filter(mergeResult);\n                } else {\n                    that.dataSource.filter({});\n                }\n            },\n            _merge: function (expression) {\n                var that = this, logic = expression.logic || 'and', filters = expression.filters, filter, result = that.dataSource.filter() || {\n                        filters: [],\n                        logic: 'and'\n                    }, idx, length;\n                removeFiltersForField(result, that.options.field);\n                for (idx = 0, length = filters.length; idx < length; idx++) {\n                    filter = filters[idx];\n                    filter.value = that._parse(filter.value);\n                }\n                filters = $.grep(filters, function (filter) {\n                    return filter.value !== '' && filter.value !== null || isNonValueFilter(filter);\n                });\n                if (filters.length) {\n                    if (result.filters.length) {\n                        expression.filters = filters;\n                        if (result.logic !== 'and') {\n                            result.filters = [{\n                                    logic: result.logic,\n                                    filters: result.filters\n                                }];\n                            result.logic = 'and';\n                        }\n                        if (filters.length > 1) {\n                            result.filters.push(expression);\n                        } else {\n                            result.filters.push(filters[0]);\n                        }\n                    } else {\n                        result.filters = filters;\n                        result.logic = logic;\n                    }\n                }\n                return result;\n            },\n            _createClearIcon: function () {\n                var that = this;\n                $('<button type=\\'button\\' class=\\'k-button k-button-icon\\' title = ' + that.options.messages.clear + '/>').attr('aria-label', that.options.messages.clear).attr(kendo.attr('bind'), 'visible:operatorVisible').html('<span class=\\'k-icon k-i-filter-clear\\'/>').click(proxy(that.clearFilter, that)).appendTo(that.wrapper);\n            },\n            clearFilter: function () {\n                this._clearInProgress = true;\n                if (isNonValueFilter(this.viewModel.operator)) {\n                    this.viewModel.set('operator', this.defaultOperator);\n                }\n                this.viewModel.set('value', null);\n                this._clearInProgress = false;\n            },\n            _angularItems: function (action) {\n                var elements = this.wrapper.closest('th').get();\n                var column = this.options.column;\n                this.angular(action, function () {\n                    return {\n                        elements: elements,\n                        data: [{ column: column }]\n                    };\n                });\n            },\n            destroy: function () {\n                var that = this;\n                that.filterModel = null;\n                that.operatorDropDown = null;\n                that._angularItems('cleanup');\n                if (that._refreshHandler) {\n                    that.dataSource.bind(CHANGE, that._refreshHandler);\n                    that._refreshHandler = null;\n                }\n                kendo.unbind(that.element);\n                Widget.fn.destroy.call(that);\n                kendo.destroy(that.element);\n            },\n            events: [CHANGE],\n            options: {\n                name: 'FilterCell',\n                delay: 200,\n                minLength: 1,\n                inputWidth: null,\n                values: undefined,\n                customDataSource: false,\n                field: '',\n                dataTextField: '',\n                type: 'string',\n                suggestDataSource: null,\n                suggestionOperator: 'startswith',\n                operator: 'eq',\n                showOperators: true,\n                template: null,\n                messages: {\n                    isTrue: 'is true',\n                    isFalse: 'is false',\n                    filter: 'Filter',\n                    clear: 'Clear',\n                    operator: 'Operator'\n                },\n                operators: {\n                    string: {\n                        eq: EQ,\n                        neq: NEQ,\n                        startswith: 'Starts with',\n                        contains: 'Contains',\n                        doesnotcontain: 'Does not contain',\n                        endswith: 'Ends with',\n                        isnull: 'Is null',\n                        isnotnull: 'Is not null',\n                        isempty: 'Is empty',\n                        isnotempty: 'Is not empty',\n                        isnullorempty: 'Has no value',\n                        isnotnullorempty: 'Has value'\n                    },\n                    number: {\n                        eq: EQ,\n                        neq: NEQ,\n                        gte: 'Is greater than or equal to',\n                        gt: 'Is greater than',\n                        lte: 'Is less than or equal to',\n                        lt: 'Is less than',\n                        isnull: 'Is null',\n                        isnotnull: 'Is not null'\n                    },\n                    date: {\n                        eq: EQ,\n                        neq: NEQ,\n                        gte: 'Is after or equal to',\n                        gt: 'Is after',\n                        lte: 'Is before or equal to',\n                        lt: 'Is before',\n                        isnull: 'Is null',\n                        isnotnull: 'Is not null'\n                    },\n                    enums: {\n                        eq: EQ,\n                        neq: NEQ,\n                        isnull: 'Is null',\n                        isnotnull: 'Is not null'\n                    }\n                }\n            }\n        });\n        ui.plugin(FilterCell);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}