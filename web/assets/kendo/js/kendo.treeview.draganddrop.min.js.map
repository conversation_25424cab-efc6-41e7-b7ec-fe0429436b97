{"version": 3, "sources": ["kendo.treeview.draganddrop.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "ui", "proxy", "extend", "VISIBILITY", "KSTATEHOVER", "INPUTSELECTOR", "HierarchicalDragAndDrop", "Class", "init", "element", "options", "this", "hovered", "dragstart", "noop", "drag", "drop", "dragend", "_draggable", "Draggable", "ignore", "filter", "autoScroll", "cursorOffset", "left", "top", "support", "mobileOS", "zoomLevel", "hint", "_hint", "dragcancel", "$angular", "hintText", "_removeTouchHover", "touch", "find", "removeClass", "_hintStatus", "newStatus", "statusElement", "className", "trim", "replace", "e", "source", "currentTarget", "closest", "itemSelector", "preventDefault", "dropHint", "reorderable", "css", "appendTo", "hoveredItem", "itemHeight", "itemTop", "itemContent", "delta", "insertOnTop", "insertOnBottom", "<PERSON><PERSON><PERSON><PERSON>", "itemData", "position", "status", "target", "drop<PERSON>ar<PERSON>", "eventTarget", "container", "allowedContainers", "length", "contains", "itemFromTarget", "item", "_outerHeight", "content", "getOffset", "y", "location", "_lastHover", "toggleClass", "dropHintContainer", "first", "last", "originalEvent", "pageY", "pageX", "x", "substring", "setStatus", "value", "indexOf", "remove", "destination", "eventArgs", "dropPrevented", "dropPositionFrom", "valid", "<PERSON><PERSON><PERSON><PERSON>", "newValid", "dropped", "destroy", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,8BACH,aACA,qBACDD,IACL,WAwME,MA7LC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MACfE,EAAKF,EAAME,GACXC,EAAQL,EAAEK,MACVC,EAASN,EAAEM,OACXC,EAAa,aACbC,EAAc,gBACdC,EAAgB,6KACpBL,GAAGM,wBAA0BR,EAAMS,MAAML,QACrCM,KAAM,SAAUC,EAASC,GACrBC,KAAKF,QAAUA,EACfE,KAAKC,QAAUH,EACfE,KAAKD,QAAUR,GACXW,UAAWjB,EAAEkB,KACbC,KAAMnB,EAAEkB,KACRE,KAAMpB,EAAEkB,KACRG,QAASrB,EAAEkB,MACZJ,GACHC,KAAKO,WAAa,GAAIlB,GAAGmB,UAAUV,GAC/BW,OAAQf,EACRgB,OAAQX,EAAQW,OAChBC,WAAYZ,EAAQY,WACpBC,cACIC,KAAM,GACNC,IAAK3B,EAAM4B,QAAQC,aAAiB7B,EAAM4B,QAAQE,YAAc,IAEpEC,KAAM5B,EAAMU,KAAKmB,MAAOnB,MACxBE,UAAWZ,EAAMU,KAAKE,UAAWF,MACjCoB,WAAY9B,EAAMU,KAAKoB,WAAYpB,MACnCI,KAAMd,EAAMU,KAAKI,KAAMJ,MACvBM,QAAShB,EAAMU,KAAKM,QAASN,MAC7BqB,SAAUtB,EAAQsB,YAG1BF,MAAO,SAAUrB,GACb,MAAO,0EAAqFE,KAAKD,QAAQuB,SAASxB,GAAW,UAEjIyB,kBAAmB,WACXpC,EAAM4B,QAAQS,OAASxB,KAAKC,UAC5BD,KAAKC,QAAQwB,KAAK,IAAMhC,GAAaiC,YAAYjC,GACjDO,KAAKC,SAAU,IAGvB0B,YAAa,SAAUC,GACnB,GAAIC,GAAgB7B,KAAKO,WAAWW,KAAKO,KAAK,kBAAkB,EAChE,OAAIG,IACAC,EAAcC,UAAY,wBAA0BF,EAApDC,GAEO5C,EAAE8C,KAAKF,EAAcC,UAAUE,QAAQ,4BAA6B,MAGnF9B,UAAW,SAAU+B,GACjBjC,KAAKkC,OAASD,EAAEE,cAAcC,QAAQpC,KAAKD,QAAQsC,cAC/CrC,KAAKD,QAAQG,UAAUF,KAAKkC,SAC5BD,EAAEK,iBAGFtC,KAAKuC,SADLvC,KAAKD,QAAQyC,YACGvD,EAAE,4CAA8CwD,IAAIjD,EAAY,UAAUkD,SAAS1C,KAAKF,SAExFb,KAGxBmB,KAAM,SAAU6B,GAAV,GAKEU,GAAaC,EAAYC,EAASC,EAAaC,EAC/CC,EAAaC,EAAgBC,EAC7BC,EAAUC,EAAUC,EANpBtD,EAAUC,KAAKD,QACfmC,EAASlC,KAAKkC,OACdoB,EAAStD,KAAKuD,WAAatE,EAAEE,EAAMqE,YAAYvB,IAC/CwB,EAAYH,EAAOlB,QAAQrC,EAAQ2D,kBAIlCD,GAAUE,OAGJzB,EAAO,IAAMoB,EAAO,IAAMvD,EAAQ6D,SAAS1B,EAAO,GAAIoB,EAAO,IACpED,EAAS,cAETA,EAAS,oBACTF,EAAWpD,EAAQ8D,eAAeP,GAClCX,EAAcQ,EAASW,KACnBnB,EAAYgB,QACZ3D,KAAKuB,oBACLqB,EAAazD,EAAM4E,aAAapB,GAChCG,EAAcK,EAASa,QACnBjE,EAAQyC,aACRO,EAAQH,GAAcE,EAAYa,OAAS,EAAI,EAAI,GACnDd,EAAU1D,EAAM8E,UAAUtB,GAAa7B,IACvCkC,EAAcf,EAAEiC,EAAEC,SAAWtB,EAAUE,EACvCE,EAAiBJ,EAAUD,EAAaG,EAAQd,EAAEiC,EAAEC,SACpDjB,EAAWJ,EAAYa,SAAWX,IAAgBC,IAElDC,GAAW,EACXF,GAAc,EACdC,GAAiB,GAErBjD,KAAKC,UAAUiD,GAAWO,EAC1BzD,KAAKuC,SAASE,IAAIjD,EAAY0D,EAAW,SAAW,WAChDlD,KAAKoE,YAAcpE,KAAKoE,WAAW,IAAMtB,EAAY,IACrD9C,KAAKoE,WAAW1C,YAAYjC,GAEhCO,KAAKoE,WAAatB,EAAYuB,YAAY5E,EAAayD,GACnDA,EACAG,EAAS,YAETD,EAAWT,EAAYS,WACvBA,EAAStC,KAAOkC,EAAc,EAAIJ,EAClC5C,KAAKuC,SAASE,IAAIW,GAAUJ,EAAc,YAAc,YAAYjD,EAAQuE,kBAAkB3B,IAC1FK,GAAeG,EAASoB,QACxBlB,EAAS,iBAETJ,GAAkBE,EAASqB,OAC3BnB,EAAS,qBAGVC,EAAO,IAAMtD,KAAKuC,SAAS,KAC9BvC,KAAKoE,YACLpE,KAAKoE,WAAW1C,YAAYjC,GAK5B4D,EAHCpE,EAAE2E,SAAS5D,KAAKF,QAAQ,GAAI2D,EAAU,IAG9B,aAFA,cA/CjBJ,EAAS,aACTrD,KAAKuB,qBAoDTvB,KAAKD,QAAQK,MACTqE,cAAexC,EAAEwC,cACjBvC,OAAQA,EACRoB,OAAQA,EACRoB,MAAOzC,EAAEiC,EAAEC,SACXQ,MAAO1C,EAAE2C,EAAET,SACXd,OAAQA,EAAOwB,UAAU,GACzBC,UAAW,SAAUC,GACjB1B,EAAS0B,KAGoB,IAAjC1B,EAAO2B,QAAQ,eACfhF,KAAKuC,SAASE,IAAIjD,EAAY,UAElCQ,KAAK2B,YAAY0B,IAErBjC,WAAY,WACRpB,KAAKuC,SAAS0C,UAElB3E,QAAS,SAAU2B,GACf,GAA6CiD,GAAqEC,EAAWC,EAAzHhC,EAAW,OAAQlB,EAASlC,KAAKkC,OAAqBK,EAAWvC,KAAKuC,SAAUgB,EAAavD,KAAKuD,UA2BtG,OA1BgC,WAA5BhB,EAASE,IAAIjD,IACb4D,EAAWpD,KAAKD,QAAQsF,iBAAiB9C,GACzC2C,EAAc3C,EAASH,QAAQpC,KAAKD,QAAQsC,eACrCkB,IACP2B,EAAc3B,EAAWnB,QAAQpC,KAAKD,QAAQsC,cACzC6C,EAAYvB,SACbuB,EAAc3B,EAAWnB,QAAQpC,KAAKD,QAAQ2D,qBAGtDyB,GACIV,cAAexC,EAAEwC,cACjBvC,OAAQA,EAAO,GACfgD,YAAaA,EAAY,GACzBI,MAA6B,cAAtBtF,KAAK2B,cACZ4D,SAAU,SAAUC,GAChBxF,KAAKsF,MAAQE,GAEjBjC,WAAYA,EAAW,GACvBH,SAAUA,GAEdgC,EAAgBpF,KAAKD,QAAQM,KAAK8E,GAClC5C,EAAS0C,SACTjF,KAAKuB,oBACDvB,KAAKoE,YACLpE,KAAKoE,WAAW1C,YAAYjC,IAE3B0F,EAAUG,OAASF,GACpBpF,KAAKO,WAAWkF,QAAUN,EAAUG,MACpC,IAEJtF,KAAKO,WAAWkF,SAAU,EAC1BzF,KAAKD,QAAQO,SACTmE,cAAexC,EAAEwC,cACjBvC,OAAQA,EACRgD,YAAaA,EACb9B,SAAUA,IALdpD,IAQJ0F,QAAS,WACL1F,KAAKoE,WAAapE,KAAKC,QAAU,KACjCD,KAAKO,WAAWmF,cAG1BtG,OAAOD,MAAMwG,QACRvG,OAAOD,OACE,kBAAVH,SAAwBA,OAAO4G,IAAM5G,OAAS,SAAU6G,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.treeview.draganddrop.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.treeview.draganddrop', [\n        'kendo.data',\n        'kendo.draganddrop'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'treeview.draganddrop',\n        name: 'Hierarchical Drag & Drop',\n        category: 'framework',\n        depends: [\n            'core',\n            'draganddrop'\n        ],\n        advanced: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo;\n        var ui = kendo.ui;\n        var proxy = $.proxy;\n        var extend = $.extend;\n        var VISIBILITY = 'visibility';\n        var KSTATEHOVER = 'k-state-hover';\n        var INPUTSELECTOR = 'input,a:not(.k-in),textarea,.k-multiselect-wrap,select,button,a.k-button>.k-icon,button.k-button>.k-icon,span.k-icon.k-i-arrow-60-right,span.k-icon.k-i-arrow-45-down-right';\n        ui.HierarchicalDragAndDrop = kendo.Class.extend({\n            init: function (element, options) {\n                this.element = element;\n                this.hovered = element;\n                this.options = extend({\n                    dragstart: $.noop,\n                    drag: $.noop,\n                    drop: $.noop,\n                    dragend: $.noop\n                }, options);\n                this._draggable = new ui.Draggable(element, {\n                    ignore: INPUTSELECTOR,\n                    filter: options.filter,\n                    autoScroll: options.autoScroll,\n                    cursorOffset: {\n                        left: 10,\n                        top: kendo.support.mobileOS ? -40 / kendo.support.zoomLevel() : 10\n                    },\n                    hint: proxy(this._hint, this),\n                    dragstart: proxy(this.dragstart, this),\n                    dragcancel: proxy(this.dragcancel, this),\n                    drag: proxy(this.drag, this),\n                    dragend: proxy(this.dragend, this),\n                    $angular: options.$angular\n                });\n            },\n            _hint: function (element) {\n                return '<div class=\\'k-header k-drag-clue\\'>' + '<span class=\\'k-icon k-drag-status\\' />' + this.options.hintText(element) + '</div>';\n            },\n            _removeTouchHover: function () {\n                if (kendo.support.touch && this.hovered) {\n                    this.hovered.find('.' + KSTATEHOVER).removeClass(KSTATEHOVER);\n                    this.hovered = false;\n                }\n            },\n            _hintStatus: function (newStatus) {\n                var statusElement = this._draggable.hint.find('.k-drag-status')[0];\n                if (newStatus) {\n                    statusElement.className = 'k-icon k-drag-status ' + newStatus;\n                } else {\n                    return $.trim(statusElement.className.replace(/(p|k)-(icon|drag-status)/g, ''));\n                }\n            },\n            dragstart: function (e) {\n                this.source = e.currentTarget.closest(this.options.itemSelector);\n                if (this.options.dragstart(this.source)) {\n                    e.preventDefault();\n                }\n                if (this.options.reorderable) {\n                    this.dropHint = $('<div class=\\'k-icon k-i-drag-and-drop\\' />').css(VISIBILITY, 'hidden').appendTo(this.element);\n                } else {\n                    this.dropHint = $();\n                }\n            },\n            drag: function (e) {\n                var options = this.options;\n                var source = this.source;\n                var target = this.dropTarget = $(kendo.eventTarget(e));\n                var container = target.closest(options.allowedContainers);\n                var hoveredItem, itemHeight, itemTop, itemContent, delta;\n                var insertOnTop, insertOnBottom, addChild;\n                var itemData, position, status;\n                if (!container.length) {\n                    status = 'k-i-cancel';\n                    this._removeTouchHover();\n                } else if (source[0] == target[0] || options.contains(source[0], target[0])) {\n                    status = 'k-i-cancel';\n                } else {\n                    status = 'k-i-insert-middle';\n                    itemData = options.itemFromTarget(target);\n                    hoveredItem = itemData.item;\n                    if (hoveredItem.length) {\n                        this._removeTouchHover();\n                        itemHeight = kendo._outerHeight(hoveredItem);\n                        itemContent = itemData.content;\n                        if (options.reorderable) {\n                            delta = itemHeight / (itemContent.length > 0 ? 4 : 2);\n                            itemTop = kendo.getOffset(hoveredItem).top;\n                            insertOnTop = e.y.location < itemTop + delta;\n                            insertOnBottom = itemTop + itemHeight - delta < e.y.location;\n                            addChild = itemContent.length && !insertOnTop && !insertOnBottom;\n                        } else {\n                            addChild = true;\n                            insertOnTop = false;\n                            insertOnBottom = false;\n                        }\n                        this.hovered = addChild ? container : false;\n                        this.dropHint.css(VISIBILITY, addChild ? 'hidden' : 'visible');\n                        if (this._lastHover && this._lastHover[0] != itemContent[0]) {\n                            this._lastHover.removeClass(KSTATEHOVER);\n                        }\n                        this._lastHover = itemContent.toggleClass(KSTATEHOVER, addChild);\n                        if (addChild) {\n                            status = 'k-i-plus';\n                        } else {\n                            position = hoveredItem.position();\n                            position.top += insertOnTop ? 0 : itemHeight;\n                            this.dropHint.css(position)[insertOnTop ? 'prependTo' : 'appendTo'](options.dropHintContainer(hoveredItem));\n                            if (insertOnTop && itemData.first) {\n                                status = 'k-i-insert-up';\n                            }\n                            if (insertOnBottom && itemData.last) {\n                                status = 'k-i-insert-down';\n                            }\n                        }\n                    } else if (target[0] != this.dropHint[0]) {\n                        if (this._lastHover) {\n                            this._lastHover.removeClass(KSTATEHOVER);\n                        }\n                        if (!$.contains(this.element[0], container[0])) {\n                            status = 'k-i-plus';\n                        } else {\n                            status = 'k-i-cancel';\n                        }\n                    }\n                }\n                this.options.drag({\n                    originalEvent: e.originalEvent,\n                    source: source,\n                    target: target,\n                    pageY: e.y.location,\n                    pageX: e.x.location,\n                    status: status.substring(2),\n                    setStatus: function (value) {\n                        status = value;\n                    }\n                });\n                if (status.indexOf('k-i-insert') !== 0) {\n                    this.dropHint.css(VISIBILITY, 'hidden');\n                }\n                this._hintStatus(status);\n            },\n            dragcancel: function () {\n                this.dropHint.remove();\n            },\n            dragend: function (e) {\n                var position = 'over', source = this.source, destination, dropHint = this.dropHint, dropTarget = this.dropTarget, eventArgs, dropPrevented;\n                if (dropHint.css(VISIBILITY) == 'visible') {\n                    position = this.options.dropPositionFrom(dropHint);\n                    destination = dropHint.closest(this.options.itemSelector);\n                } else if (dropTarget) {\n                    destination = dropTarget.closest(this.options.itemSelector);\n                    if (!destination.length) {\n                        destination = dropTarget.closest(this.options.allowedContainers);\n                    }\n                }\n                eventArgs = {\n                    originalEvent: e.originalEvent,\n                    source: source[0],\n                    destination: destination[0],\n                    valid: this._hintStatus() != 'k-i-cancel',\n                    setValid: function (newValid) {\n                        this.valid = newValid;\n                    },\n                    dropTarget: dropTarget[0],\n                    position: position\n                };\n                dropPrevented = this.options.drop(eventArgs);\n                dropHint.remove();\n                this._removeTouchHover();\n                if (this._lastHover) {\n                    this._lastHover.removeClass(KSTATEHOVER);\n                }\n                if (!eventArgs.valid || dropPrevented) {\n                    this._draggable.dropped = eventArgs.valid;\n                    return;\n                }\n                this._draggable.dropped = true;\n                this.options.dragend({\n                    originalEvent: e.originalEvent,\n                    source: source,\n                    destination: destination,\n                    position: position\n                });\n            },\n            destroy: function () {\n                this._lastHover = this.hovered = null;\n                this._draggable.destroy();\n            }\n        });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}