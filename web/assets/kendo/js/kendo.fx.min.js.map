{"version": 3, "sources": ["kendo.fx.js"], "names": ["f", "define", "$", "undefined", "parseInteger", "value", "parseInt", "parseCSS", "element", "property", "css", "keys", "obj", "propertyName", "acc", "push", "strip3DTransforms", "properties", "key", "transformProps", "indexOf", "transform2d", "normalizeCSS", "lowerKey", "isTransformed", "transformation", "cssValues", "toLowerCase", "transforms", "support", "hasHW3D", "length", "TRANSFORM", "join", "animationProperty", "transform", "match", "computed", "NONE", "RegExp", "matrix3dRegExp", "translateXRegExp", "test", "parseFloat", "Math", "atan2", "capitalize", "word", "char<PERSON>t", "toUpperCase", "substring", "createEffect", "name", "definition", "effectClass", "Effect", "extend", "directions", "prototype", "fx", "Element", "direction", "opt1", "opt2", "opt3", "this", "each", "idx", "theDirection", "createToggleEffect", "defaultStart", "defaultEnd", "IN_OUT", "startValue", "_startValue", "endValue", "_endValue", "shouldHide", "_shouldHide", "prepare", "start", "end", "that", "out", "_direction", "startDataValue", "data", "startDataValueIsSet", "isNaN", "_reverse", "clipInHalf", "container", "vertical", "kendo", "size", "HEIGHT", "WIDTH", "CLIPS", "replace", "curProxy", "EffectSet", "FOUR_DIRECTIONS", "TRANSFER_START_STATE", "ROTATIONS", "RESTORE_OVERFLOW", "IGNORE_TRANSITION_EVENT_SELECTOR", "Animation", "Transition", "window", "effects", "proxy", "browser", "transitions", "scaleProperties", "scale", "scalex", "scaley", "scale3d", "translateProperties", "translate", "translatex", "<PERSON>y", "translate3d", "hasZoom", "document", "documentElement", "style", "zoom", "cssParamsRegExp", "oldEffectsRegExp", "singleEffectRegExp", "unitRegExp", "transform2units", "rotate", "skew", "cssPrefix", "round", "BLANK", "PX", "AUTO", "HIDDEN", "ORIGIN", "ABORT_ID", "OVERFLOW", "TRANSLATE", "POSITION", "COMPLETE_CALLBACK", "TRANSITION", "BACKFACE", "PERSPECTIVE", "DEFAULT_PERSPECTIVE", "TRANSFORM_PERSPECTIVE", "left", "reverse", "transition", "modifier", "right", "down", "up", "top", "bottom", "in", "horizontal", "fn", "kendoStop", "clearQueue", "gotoEnd", "stopQueue", "stop", "val", "transformValue", "cssText", "step", "elem", "now", "cur", "prop", "apply", "arguments", "toggleClass", "classes", "options", "add", "split", "exclusive", "duration", "ease", "setTimeout", "parseEffects", "input", "mirror", "redirectedEffect", "resolved", "$1", "$2", "effect", "effectBody", "timeoutID", "stopTransitionCalled", "stopTransition", "delay", "oldKeys", "complete", "clearTimeout", "removeData", "dequeue", "call", "speeds", "merge", "hasOwnProperty", "uniqueSort", "height", "unique", "event", "one", "task<PERSON>eys", "retainPosition", "completeCallback", "getComputedStyles", "Class", "init", "restore", "run", "jdx", "target", "children", "<PERSON><PERSON><PERSON><PERSON>", "effectName", "deferred", "Deferred", "done", "setReverse", "setOptions", "addRestoreProperties", "is", "display", "reset", "setup", "animate", "resolve", "promise", "i", "restoreCallback", "hide", "teardown", "effectSet", "parsedEffects", "elements", "useTransition", "queue", "show", "multiple", "params", "single", "position", "isFixed", "originalPosition", "dX", "dY", "currentValue", "marginLeft", "marginTop", "msie", "animatedPromise", "_additionalEffects", "play", "additional", "_duration", "compositeRun", "concat", "_complete", "noop", "divisor", "tmp", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "offset", "previous", "previousDivisor", "dir", "slideIn", "margin", "origin", "axis", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "real<PERSON>ength", "overflow", "appendTo", "body", "outerBox", "box", "innerBox", "currentScale", "fillScale", "transform<PERSON><PERSON>in", "x", "y", "_container", "rotation", "zIndex", "_clipInHalf", "clip", "append", "face", "_face", "temporary", "addClass", "opacity", "back", "temp", "reverseDirection", "faceClone", "clone", "removeAttr", "backClone", "staticPage", "turningPage", "transformStyle", "find", "remove", "mobileOS", "android", "_before", "_after", "transitionClass", "_previous", "_transitionClass", "Error", "beforeTransition", "callback", "afterTransition", "_both", "_element", "_containerClass", "containerClass", "e", "removeClass", "off", "completeProxy", "isAbsolute", "originalOverflow", "parents", "filter", "first", "both", "parent", "on", "animationFrame", "_tickProxy", "_tick", "_started", "tick", "onEnd", "onCancel", "enabled", "cancel", "timePassed", "min", "Date", "startDate", "moveTo", "movable", "initial", "delta", "location", "_easeProxy", "moveAxis", "easeOutExpo", "t", "b", "c", "d", "pow", "easeOutBack", "s", "result", "width", "inner", "outer", "fitScale", "max", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,YAAa,cAAeD,IACrC,WA2pCE,MAnpCC,UAAUE,EAAGC,GA4KV,QAASC,GAAaC,GAClB,MAAOC,UAASD,EAAO,IAE3B,QAASE,GAASC,EAASC,GACvB,MAAOL,GAAaI,EAAQE,IAAID,IAEpC,QAASE,GAAKC,GAAd,GAEaC,GADLC,IACJ,KAASD,IAAgBD,GACrBE,EAAIC,KAAKF,EAEb,OAAOC,GAEX,QAASE,GAAkBC,GACvB,IAAK,GAAIC,KAAOD,GACRE,EAAeC,QAAQF,QAAcG,EAAYD,QAAQF,cAClDD,GAAWC,EAG1B,OAAOD,GAEX,QAASK,GAAad,EAASS,GAC3B,GAAyCM,GAAUL,EAAKb,EAAOmB,EAA3DC,KAAqBC,IACzB,KAAKR,IAAOD,GACRM,EAAWL,EAAIS,cACfH,EAAgBI,GAAcT,EAAeC,QAAQG,QAChDM,EAAQC,SAAWN,GAAiBH,EAAYD,QAAQG,aAClDN,GAAWC,IAElBb,EAAQY,EAAWC,GACfM,EACAC,EAAeV,KAAKG,EAAM,IAAMb,EAAQ,KAExCqB,EAAUR,GAAOb,EAO7B,OAHIoB,GAAeM,SACfL,EAAUM,IAAaP,EAAeQ,KAAK,MAExCP,EA2DX,QAASQ,GAAkB1B,EAASC,GAApC,GAEY0B,GAIAC,EAAyEC,CALjF,OAAIT,IACIO,EAAY3B,EAAQE,IAAIsB,IACxBG,GAAaG,EACM,SAAZ7B,EAAsB,EAAI,GAEjC2B,EAAQD,EAAUC,MAAUG,OAAO9B,EAAW,0BAA2B4B,EAAW,EACpFD,EACAC,EAAWjC,EAAagC,EAAM,KAE9BA,EAAQD,EAAUC,MAAMI,KACpB,EACA,EACA,EACA,EACA,GAEJ/B,EAAWA,EAASkB,cAChBc,EAAiBC,KAAKjC,GACtB4B,EAAWM,WAAWP,EAAM,GAAKA,EAAM,IACpB,cAAZ3B,EACP4B,EAAWM,WAAWP,EAAM,GAAKA,EAAM,IACpB,SAAZ3B,EACP4B,EAAWM,WAAWP,EAAM,IACT,UAAZ3B,IACP4B,EAAWM,WAAWC,KAAKC,MAAMT,EAAM,GAAIA,EAAM,OAGlDC,IAEAM,WAAWnC,EAAQE,IAAID,IAkVtC,QAASqC,GAAWC,GAChB,MAAOA,GAAKC,OAAO,GAAGC,cAAgBF,EAAKG,UAAU,GAEzD,QAASC,GAAaC,EAAMC,GACxB,GAAIC,GAAcC,EAAOC,OAAOH,GAAaI,EAAaH,EAAYI,UAAUD,UAChFE,GAAGb,EAAWM,IAASE,EACvBK,EAAGC,QAAQF,UAAUN,GAAQ,SAAUS,EAAWC,EAAMC,EAAMC,GAC1D,MAAO,IAAIV,GAAYW,KAAKzD,QAASqD,EAAWC,EAAMC,EAAMC,IAEhEE,EAAKT,EAAY,SAAUU,EAAKC,GAC5BT,EAAGC,QAAQF,UAAUN,EAAON,EAAWsB,IAAiB,SAAUN,EAAMC,EAAMC,GAC1E,MAAO,IAAIV,GAAYW,KAAKzD,QAAS4D,EAAcN,EAAMC,EAAMC,MAsD3E,QAASK,GAAmBjB,EAAM3C,EAAU6D,EAAcC,GACtDpB,EAAaC,GACTK,WAAYe,EACZC,WAAY,SAAUpE,GAElB,MADA4D,MAAKS,YAAcrE,EACZ4D,MAEXU,SAAU,SAAUtE,GAEhB,MADA4D,MAAKW,UAAYvE,EACV4D,MAEXY,WAAY,WACR,MAAOZ,MAAKa,aAEhBC,QAAS,SAAUC,EAAOC,GACtB,GAAiBR,GAAYE,EAAzBO,EAAOjB,KAA4BkB,EAA0B,QAApBlB,KAAKmB,WAAsBC,EAAiBH,EAAK1E,QAAQ8E,KAAK7E,GAAW8E,IAAwBC,MAAMH,IAAmBA,GAAkBf,EAErLG,GADAc,EACaF,EACsB,IAArBpB,KAAKS,YACNT,KAAKS,YAELS,EAAMb,EAAeC,EAGlCI,EAD0B,IAAnBV,KAAKW,UACDX,KAAKW,UAELO,EAAMZ,EAAaD,EAE9BL,KAAKwB,UACLT,EAAMvE,GAAYkE,EAClBM,EAAIxE,GAAYgE,IAEhBO,EAAMvE,GAAYgE,EAClBQ,EAAIxE,GAAYkE,GAEpBO,EAAKJ,YAAcG,EAAIxE,KAAc8D,KAgHjD,QAASmB,GAAWC,EAAW9B,GAC3B,GAAI+B,GAAWC,EAAMpC,WAAWI,GAAW+B,SAAUE,EAAOH,EAAUC,EAAWG,EAASC,KAAW,EAAI,IACzG,OAAOC,GAAMpC,GAAWqC,QAAQ,QAASJ,GAr1BhD,GAyHWK,GAuLJC,EAuNA7C,EAuIA8C,EAKG7B,EAyIH8B,EA+BAL,EAMAM,EA+IAC,EACAC,EA2FAC,EA0CAC,EArlCAd,EAAQe,OAAOf,MAAOlC,EAAKkC,EAAMgB,QAAS3C,EAAOhE,EAAEgE,KAAMV,EAAStD,EAAEsD,OAAQsD,EAAQ5G,EAAE4G,MAAOjF,EAAUgE,EAAMhE,QAASkF,EAAUlF,EAAQkF,QAASnF,EAAaC,EAAQD,WAAYoF,EAAcnF,EAAQmF,YAAaC,GACjNC,MAAO,EACPC,OAAQ,EACRC,OAAQ,EACRC,QAAS,GACVC,GACCC,UAAW,EACXC,WAAY,EACZC,WAAY,EACZC,YAAa,GACdC,EAAyD,IAAxCC,SAASC,gBAAgBC,MAAMC,OAAyBnG,EAAYY,EAAiB,iGAAkGwF,EAAkB,oDAAqDvF,EAAmB,gBAAiBwF,EAAmB,0BAA2BC,EAAqB,qBAAsBC,EAAa,SAAUhH,GACla,cACA,SACA,UACA,UACA,UACA,WACA,QACA,SACA,SACA,SACA,UACA,OACA,QACA,QACA,YACA,aACA,aACA,aACA,cACA,SACA,YACDE,GACC,SACA,QACA,SACA,SACA,OACA,QACA,QACA,YACA,aACA,aACA,UACD+G,GACCC,OAAU,MACVnB,MAAO,GACPoB,KAAM,KACNf,UAAW,MACZgB,EAAY3G,EAAWlB,IAAK8H,EAAQ5F,KAAK4F,MAAOC,EAAQ,GAAIC,EAAK,KAAMpG,EAAO,OAAQqG,EAAO,OAAQ3C,EAAQ,QAASD,EAAS,SAAU6C,EAAS,SAAUC,GAAS,SAAUC,GAAW,UAAWC,GAAW,WAAYC,GAAY,YAAaC,GAAW,WAAYC,GAAoB,mBAAoBC,GAAaZ,EAAY,aAAcvG,GAAYuG,EAAY,YAAaa,GAAWb,EAAY,sBAAuBc,GAAcd,EAAY,cAAee,GAAsB,SAAUC,GAAwB,eAAiBD,GAAsB,IAAK7F,IAC1jB+F,MACIC,QAAS,QACThJ,SAAU,OACViJ,WAAY,aACZ9D,UAAU,EACV+D,aAEJC,OACIH,QAAS,OACThJ,SAAU,OACViJ,WAAY,aACZ9D,UAAU,EACV+D,SAAU,GAEdE,MACIJ,QAAS,KACThJ,SAAU,MACViJ,WAAY,aACZ9D,UAAU,EACV+D,SAAU,GAEdG,IACIL,QAAS,OACThJ,SAAU,MACViJ,WAAY,aACZ9D,UAAU,EACV+D,aAEJI,KAAON,QAAS,UAChBO,QAAUP,QAAS,OACnBQ,MACIR,QAAS,MACTE,aAEJxE,KACIsE,QAAS,KACTE,SAAU,GAEd/D,UAAY6D,QAAS,YACrBS,YAAcT,QAAS,cAE/B5D,GAAMpC,WAAaA,GACnBD,EAAOtD,EAAEiK,IACLC,UAAW,SAAUC,EAAYC,GAC7B,MAAItD,GACOrD,EAAG4G,UAAUtG,KAAMoG,IAAc,EAAOC,IAAW,GAEnDrG,KAAKuG,KAAKH,EAAYC,MAIrC1I,IAAeoF,IACf9C,EAAK7C,EAAa,SAAU8C,EAAK9D,GAC7BH,EAAEiK,GAAG9J,GAAS,SAAUoK,GACpB,GAAkB,IAAPA,EACP,MAAOvI,GAAkB+B,KAAM5D,EAE/B,IAAI6E,GAAOhF,EAAE+D,MAAM,GAAIyG,EAAiBrK,EAAQ,IAAMoK,EAAMrC,EAAgB/H,EAAM6F,QAAQiC,EAAY,KAAO,GAOjH,OANQjD,GAAK4C,MAAM6C,QAAQvJ,QAAQY,QAC3B9B,EAAE+D,MAAMvD,IAAIsB,GAAW0I,GAEvBxF,EAAK4C,MAAM6C,QAAUzF,EAAK4C,MAAM6C,QAAQzE,QAAY3D,OAAOlC,EAAQ,YAAa,KAAMqK,GAGvFzG,MAEX/D,EAAEyD,GAAGiH,KAAKvK,GAAS,SAAUsD,GACzBzD,EAAEyD,EAAGkH,MAAMxK,GAAOsD,EAAGmH,QAGzB3E,EAAWjG,EAAEyD,GAAGD,UAAUqH,IAC9B7K,EAAEyD,GAAGD,UAAUqH,IAAM,WACjB,MAAI1J,GAAYD,QAAQ6C,KAAK+G,UAClBrI,WAAWzC,EAAE+D,KAAK4G,MAAM5G,KAAK+G,SAEjC7E,EAAS8E,MAAMhH,KAAMiH,aAGpCrF,EAAMsF,YAAc,SAAU3K,EAAS4K,EAASC,EAASC,GAkBrD,MAjBIF,KACAA,EAAUA,EAAQG,MAAM,KACpBvE,IACAqE,EAAU7H,GACNgI,UAAW,MACXC,SAAU,IACVC,KAAM,YACPL,GACH7K,EAAQE,IAAIyI,GAAYkC,EAAQG,UAAY,IAAMH,EAAQI,SAAW,MAAQJ,EAAQK,MACrFC,WAAW,WACPnL,EAAQE,IAAIyI,GAAY,IAAIzI,IAAIqF,IACjCsF,EAAQI,WAEfvH,EAAKkH,EAAS,SAAUjH,EAAK9D,GACzBG,EAAQ2K,YAAY9K,EAAOiL,MAG5B9K,GAEXqF,EAAM+F,aAAe,SAAUC,EAAOC,GAClC,GAAIjF,KAoBJ,OAnBqB,gBAAVgF,GACP3H,EAAK2H,EAAMN,MAAM,KAAM,SAAUpH,EAAK9D,GAClC,GAAI0L,IAAoB7D,EAAmBxF,KAAKrC,GAAQ2L,EAAW3L,EAAM6F,QAAQ+B,EAAkB,SAAU7F,EAAO6J,EAAIC,GAChH,MAAOD,GAAK,IAAMC,EAAGvK,gBACrBwK,EAASH,EAAST,MAAM,KAAM1H,EAAYsI,EAAO,GAAIC,IACzDD,GAAOpK,OAAS,IAChBqK,EAAWvI,UAAYiI,GAAUC,EAAmBtI,GAAWI,GAAW4F,QAAU5F,GAExFgD,EAAQsF,EAAO,IAAMC,IAGzBlI,EAAK2H,EAAO,SAAU1H,GAClB,GAAIN,GAAYI,KAAKJ,SACjBA,IAAaiI,IAAW5D,EAAmBxF,KAAKyB,KAChDF,KAAKJ,UAAYJ,GAAWI,GAAW4F,SAE3C5C,EAAQ1C,GAAOF,OAGhB4C,GA4CPG,GACAxD,EAAOG,GACH+F,WAAY,SAAUlJ,EAASS,EAAYoK,GAA/B,GACJ3K,GAAsD2L,EAOtDC,EACAC,EARKC,EAAQ,EAAGC,EAAUjM,EAAQ8E,KAAK,WAC3C+F,GAAU7H,GACNiI,SAAU,IACVC,KAAM,WACNgB,SAAU,KACVlB,UAAW,OACZH,GACCiB,GAAuB,EACvBC,EAAiB,WACZD,IACDA,GAAuB,EACnBD,IACAM,aAAaN,GACbA,EAAY,MAEhB7L,EAAQoM,WAAW9D,IAAU+D,UAAUnM,IAAIyI,GAAY,IAAIzI,IAAIyI,IAC/DkC,EAAQqB,SAASI,KAAKtM,KAG9B6K,EAAQI,SAAWvL,EAAEyD,GAAKzD,EAAEyD,GAAGoJ,OAAO1B,EAAQI,WAAaJ,EAAQI,SAAWJ,EAAQI,SACtF/K,EAAMY,EAAad,EAASS,GAC5Bf,EAAE8M,MAAMP,EAAS9L,EAAKD,IAClBR,EAAE+M,eAAe,cACjBzM,EAAQ8E,KAAK,OAAQpF,EAAEgN,WAAWT,IAAUU,SAE5C3M,EAAQ8E,KAAK,OAAQpF,EAAEkN,OAAOX,IAAUU,SAE5C3M,EAAQE,IAAIyI,GAAYkC,EAAQG,UAAY,IAAMH,EAAQI,SAAW,MAAQJ,EAAQK,MAAMhL,IAAIyI,IAC/F3I,EAAQE,IAAIA,GAAKA,IAAIsB,IACjBgF,EAAYqG,QACZ7M,EAAQ8M,IAAItG,EAAYqG,MAAOd,GACN,IAArBlB,EAAQI,WACRe,EAAQ,MAGhBH,EAAYV,WAAWY,EAAgBlB,EAAQI,SAAWe,GAC1DhM,EAAQ8E,KAAKwD,GAAUuD,GACvB7L,EAAQ8E,KAAK4D,GAAmBqD,IAEpChC,UAAW,SAAU/J,EAAS6J,EAAYC,GACtC,GAAI5I,GAAW6L,EAAW/M,EAAQ8E,KAAK,QAASkI,GAAkBlD,GAAWiD,EAAUE,EAAmBjN,EAAQ8E,KAAK4D,GAUvH,OATIsE,KACA9L,EAAYmE,EAAM6H,kBAAkBlN,EAAQ,GAAI+M,IAEhDE,GACAA,IAEAD,GACAhN,EAAQE,IAAIgB,GAETlB,EAAQoM,WAAW,QAAQpC,KAAKH,MAqC/CjE,EAAYP,EAAM8H,MAAMnK,QACxBoK,KAAM,SAAUpN,EAAS6K,GACrB,GAAInG,GAAOjB,IACXiB,GAAK1E,QAAUA,EACf0E,EAAK2B,WACL3B,EAAKmG,QAAUA,EACfnG,EAAK2I,YAETC,IAAK,SAAUjH,GAAV,GACgBsF,GAAQhI,EAAK4J,EAA6HC,EAAQC,EAAUC,EAepKC,EAfLjJ,EAAOjB,KAAwBlC,EAAS8E,EAAQ9E,OAAQvB,EAAU0E,EAAK1E,QAAS6K,EAAUnG,EAAKmG,QAAS+C,EAAWlO,EAAEmO,WAAYrJ,KAAYC,IAIjJ,KAHAC,EAAK2B,QAAUA,EACfuH,EAASE,KAAKpO,EAAE4G,MAAM5B,EAAM,aAC5B1E,EAAQ8E,KAAK,aAAa,GACrBnB,EAAM,EAAGA,EAAMpC,EAAQoC,IAOxB,IANAgI,EAAStF,EAAQ1C,GACjBgI,EAAOoC,WAAWlD,EAAQ5B,SAC1B0C,EAAOqC,WAAWnD,GAClBnG,EAAKuJ,qBAAqBtC,EAAO0B,SACjC1B,EAAOpH,QAAQC,EAAOC,GACtBgJ,EAAW9B,EAAO8B,WACbF,EAAM,EAAGG,EAAiBD,EAASlM,OAAQgM,EAAMG,EAAgBH,IAClEE,EAASF,GAAKtC,SAASJ,EAAQI,UAAUqC,KAGjD,KAASK,IAAc9C,GAAQxE,QAC3BrD,EAAOyB,EAAKoG,EAAQxE,QAAQsH,GAAYlN,WAgB5C,KAdKT,EAAQkO,GAAG,aACZlL,EAAOwB,GAAS2J,QAASnO,EAAQ8E,KAAK,eAAiB,UAEvD1D,IAAeyJ,EAAQuD,QACvBZ,EAASxN,EAAQ8E,KAAK,mBAClB0I,IACAhJ,EAAQxB,EAAOwK,EAAQhJ,KAG/BA,EAAQ1D,EAAad,EAASwE,GAC1BpD,IAAeoF,IACfhC,EAAQhE,EAAkBgE,IAE9BxE,EAAQE,IAAIsE,GAAOtE,IAAIsB,IAClBmC,EAAM,EAAGA,EAAMpC,EAAQoC,IACxB0C,EAAQ1C,GAAK0K,OAOjB,OALIxD,GAAQuC,MACRvC,EAAQuC,OAEZpN,EAAQ8E,KAAK,kBAAmBL,GAChCtB,EAAGmL,QAAQtO,EAASyE,EAAKzB,KAAW6H,GAAWqB,SAAU0B,EAASW,WAC3DX,EAASY,WAEpBxE,KAAM,WACFtK,EAAE+D,KAAKzD,SAAS4J,WAAU,GAAM,IAEpCqE,qBAAsB,SAAUZ,GAE5B,IADA,GAA4BxN,GAAxBG,EAAUyD,KAAKzD,QAAgByO,EAAI,EAAGlN,EAAS8L,EAAQ9L,OACpDkN,EAAIlN,EAAQkN,IACf5O,EAAQwN,EAAQoB,GAChBhL,KAAK4J,QAAQ9M,KAAKV,GACbG,EAAQ8E,KAAKjF,IACdG,EAAQ8E,KAAKjF,EAAOG,EAAQE,IAAIL,KAI5C6O,gBAAiB,WAAA,GAEJD,GAAOlN,EACR1B,EAFJG,EAAUyD,KAAKzD,OACnB,KAASyO,EAAI,EAAGlN,EAASkC,KAAK4J,QAAQ9L,OAAQkN,EAAIlN,EAAQkN,IAClD5O,EAAQ4D,KAAK4J,QAAQoB,GACzBzO,EAAQE,IAAIL,EAAOG,EAAQ8E,KAAKjF,KAGxCqM,SAAU,WACN,GAAIxH,GAAOjB,KAAME,EAAM,EAAG3D,EAAU0E,EAAK1E,QAAS6K,EAAUnG,EAAKmG,QAASxE,EAAU3B,EAAK2B,QAAS9E,EAAS8E,EAAQ9E,MASnH,KARAvB,EAAQoM,WAAW,aAAaC,UAC5BxB,EAAQ8D,MACR3O,EAAQ8E,KAAK,aAAc9E,EAAQE,IAAI,YAAYyO,OAEvDlL,KAAKiL,kBACDvH,IAAY/F,GACZ+J,WAAWzL,EAAE4G,MAAM7C,KAAM,mBAAoB,GAE1CE,EAAMpC,EAAQoC,IACjB0C,EAAQ1C,GAAKiL,UAEb/D,GAAQoC,kBACRpC,EAAQoC,iBAAiBjN,MAIrCmD,EAAGqL,QAAU,SAAUxO,EAAS6K,GAAnB,GACS/H,GAA+G6I,EAExHgC,EAFLtH,KAA2BwI,EAAY,GAAIjJ,GAAU5F,EAAS6K,GAAUiE,EAAgBzJ,EAAM+F,aAAaP,EAAQxE,QACvHwE,GAAQxE,QAAUyI,CAClB,KAASnB,IAAcmB,GACnBhM,EAAcK,EAAGb,EAAWqL,IACxB7K,IACA6I,EAAS,GAAI7I,GAAY9C,EAAS8O,EAAcnB,GAAYtK,WAC5DgD,EAAQ9F,KAAKoL,GAGjBtF,GAAQ,GACRwI,EAAUvB,IAAIjH,IAETrG,EAAQkO,GAAG,aACZlO,EAAQE,KAAMiO,QAASnO,EAAQ8E,KAAK,eAAiB,UAAW5E,IAAI,WAEpE2K,EAAQuC,MACRvC,EAAQuC,OAEZpN,EAAQqM,UACRwC,EAAU3C,aAGlBlJ,EAAOG,GACHmL,QAAS,SAAUS,EAAUtO,EAAYoK,GACrC,GAAImE,GAAgBnE,EAAQ3B,cAAe,QACpC2B,GAAQ3B,WACX1C,GAAe,cAAgBrD,IAAM6L,EACrC7L,EAAG+F,WAAW6F,EAAUtO,EAAYoK,GAEhCzJ,EACA2N,EAAST,QAAQ9N,EAAkBC,IAC/BwO,OAAO,EACPC,MAAM,EACNP,MAAM,EACN1D,SAAUJ,EAAQI,SAClBiB,SAAUrB,EAAQqB,WAGtB6C,EAASrL,KAAK,WACV,GAAI1D,GAAUN,EAAE+D,MAAO0L,IACvBzL,GAAK/C,EAAgB,SAAUgD,EAAK9D,GAAf,GACbuP,GAEIC,EAQQC,EAAkCC,EAgBlCC,EAGIC,EAAoDC,EA7B5DC,EAAelP,EAAaA,EAAWZ,GAAS,IAAM,IAC9D8P,KACIN,EAAS5O,EACTZ,IAAS4G,IAAmBhG,EAAWZ,KAAWF,GAClDyP,EAASO,EAAa/N,MAAM4F,GACxBpG,GACA4B,EAAOqM,GAAU3I,OAAQ0I,EAAO,MAGhCvP,IAASiH,IAAuBrG,EAAWZ,KAAWF,IAClD2P,EAAWtP,EAAQE,IAAIuI,IAAW8G,EAAsB,YAAZD,GAAsC,SAAZA,EACrEtP,EAAQ8E,KAAK0D,MACV+G,EACAvP,EAAQ8E,KAAK0D,IACTe,IAAKxJ,EAASC,EAAS,QAAU,EACjCgJ,KAAMjJ,EAASC,EAAS,SAAW,EACnCwJ,OAAQzJ,EAASC,EAAS,UAC1BoJ,MAAOrJ,EAASC,EAAS,WAG7BA,EAAQ8E,KAAK0D,IACTe,IAAKxJ,EAASC,EAAS,cAAgB,EACvCgJ,KAAMjJ,EAASC,EAAS,eAAiB,KAIjDwP,EAAmBxP,EAAQ8E,KAAK0D,IACpC4G,EAASO,EAAa/N,MAAM4F,GACxB4H,IACIK,EAAK5P,GAAS2I,GAAY,IAAM,GAAS4G,EAAO,GAAIM,EAAK7P,GAAS2I,GAAY,KAAO4G,EAAO,IAAMA,EAAO,GACzGG,GACKvK,MAAMwK,EAAiBpG,OAKnBpE,MAAMyK,IACPzM,EAAOqM,GAAUrG,KAAMwG,EAAiBxG,KAAOyG,IAL9CzK,MAAMyK,IACPzM,EAAOqM,GAAUjG,MAAOoG,EAAiBpG,MAAQqG,IAOpDzK,MAAMwK,EAAiBhG,QAKnBxE,MAAM0K,IACP1M,EAAOqM,GAAU9F,IAAKiG,EAAiBjG,IAAMmG,IAL5C1K,MAAM0K,IACP1M,EAAOqM,GAAU7F,OAAQgG,EAAiBhG,OAASkG,MAQtD1K,MAAMyK,IACPzM,EAAOqM,GAAUO,WAAYJ,EAAiBxG,KAAOyG,IAEpDzK,MAAM0K,IACP1M,EAAOqM,GAAUQ,UAAWL,EAAiBjG,IAAMmG,QAMlEtO,GAAuB,SAATvB,GAAoBA,IAASwP,UACrCA,GAAOxP,GAEdwP,GACArM,EAAOmM,EAAUE,MAIzB9I,EAAQuJ,YACDX,GAASzI,MAEpB1G,EAAQsO,QAAQa,GACZF,OAAO,EACPC,MAAM,EACNP,MAAM,EACN1D,SAAUJ,EAAQI,SAClBiB,SAAUrB,EAAQqB,gBAO1C/I,EAAG4M,gBAAkB5M,EAAGqL,QACpBzL,EAASsC,EAAM8H,MAAMnK,QACrBoK,KAAM,SAAUpN,EAASqD,GACrB,GAAIqB,GAAOjB,IACXiB,GAAK1E,QAAUA,EACf0E,EAAKE,WAAavB,EAClBqB,EAAKmG,WACLnG,EAAKsL,sBACAtL,EAAK2I,UACN3I,EAAK2I,aAGbpE,QAAS,WAEL,MADAxF,MAAKwB,UAAW,EACTxB,KAAK6J,OAEhB2C,KAAM,WAEF,MADAxM,MAAKwB,UAAW,EACTxB,KAAK6J,OAEhBxC,IAAK,SAAUoF,GAEX,MADAzM,MAAKuM,mBAAmBzP,KAAK2P,GACtBzM,MAEXJ,UAAW,SAAUxD,GAEjB,MADA4D,MAAKmB,WAAa/E,EACX4D,MAEXwH,SAAU,SAAUA,GAEhB,MADAxH,MAAK0M,UAAYlF,EACVxH,MAEX2M,aAAc,WACV,GAAI1L,GAAOjB,KAAMoL,EAAY,GAAIjJ,GAAUlB,EAAK1E,SACxCiJ,QAASvE,EAAKO,SACdgG,SAAUvG,EAAKyL,YACf9J,EAAU3B,EAAKsL,mBAAmBK,QAAQ3L,GAClD,OAAOmK,GAAUvB,IAAIjH,IAEzBiH,IAAK,WACD,GAAI7J,KAAKuM,oBAAsBvM,KAAKuM,mBAAmB,GACnD,MAAOvM,MAAK2M,cAEhB,IAAmGvQ,GAAsD2N,EAArJ9I,EAAOjB,KAAMzD,EAAU0E,EAAK1E,QAAS2D,EAAM,EAAG0J,EAAU3I,EAAK2I,QAAS9L,EAAS8L,EAAQ9L,OAAeqM,EAAWlO,EAAEmO,WAAYrJ,KAAYC,KAAkBgJ,EAAW/I,EAAK+I,WAAYC,EAAiBD,EAASlM,MAGvN,KAFAqM,EAASE,KAAKpO,EAAE4G,MAAM5B,EAAM,cAC5B1E,EAAQ8E,KAAK,aAAa,GACrBnB,EAAM,EAAGA,EAAMpC,EAAQoC,IACxB9D,EAAQwN,EAAQ1J,GACX3D,EAAQ8E,KAAKjF,IACdG,EAAQ8E,KAAKjF,EAAOG,EAAQE,IAAIL,GAGxC,KAAK8D,EAAM,EAAGA,EAAM+J,EAAgB/J,IAChC8J,EAAS9J,GAAKsH,SAASvG,EAAKyL,WAAW7C,KAuB3C,OArBA5I,GAAKH,QAAQC,EAAOC,GACfzE,EAAQkO,GAAG,aACZlL,EAAOwB,GAAS2J,QAASnO,EAAQ8E,KAAK,eAAiB,UAEvD1D,IACAoM,EAASxN,EAAQ8E,KAAK,mBAClB0I,IACAhJ,EAAQxB,EAAOwK,EAAQhJ,KAG/BA,EAAQ1D,EAAad,EAASwE,GAC1BpD,IAAeoF,IACfhC,EAAQhE,EAAkBgE,IAE9BxE,EAAQE,IAAIsE,GAAOtE,IAAIsB,IACvBkD,EAAK2J,QACLrO,EAAQ8E,KAAK,kBAAmBL,GAChCtB,EAAGmL,QAAQtO,EAASyE,GAChBwG,SAAUvG,EAAKyL,UACfjE,SAAU0B,EAASW,UAEhBX,EAASY,WAEpBxE,KAAM,WACF,GAAIrG,GAAM,EAAG8J,EAAWhK,KAAKgK,WAAYC,EAAiBD,EAASlM,MACnE,KAAKoC,EAAM,EAAGA,EAAM+J,EAAgB/J,IAChC8J,EAAS9J,GAAKqG,MAGlB,OADAtK,GAAE+D,KAAKzD,SAAS4J,WAAU,GAAM,GACzBnG,MAEXiL,gBAAiB,WAAA,GAEJD,GAAOlN,EACR1B,EAFJG,EAAUyD,KAAKzD,OACnB,KAASyO,EAAI,EAAGlN,EAASkC,KAAK4J,QAAQ9L,OAAQkN,EAAIlN,EAAQkN,IAClD5O,EAAQ4D,KAAK4J,QAAQoB,GACzBzO,EAAQE,IAAIL,EAAOG,EAAQ8E,KAAKjF,KAGxCyQ,UAAW,WACP,GAAI5L,GAAOjB,KAAMzD,EAAU0E,EAAK1E,OAChCA,GAAQoM,WAAW,aAAaC,UAChC3H,EAAKgK,kBACDhK,EAAKL,cACLrE,EAAQ8E,KAAK,aAAc9E,EAAQE,IAAI,YAAYyO,OAEnDxH,IAAY/F,GACZ+J,WAAWzL,EAAE4G,MAAM5B,EAAM,mBAAoB,GAEjDA,EAAKkK,YAETZ,WAAY,SAAUnD,GAClB7H,GAAO,EAAMS,KAAKoH,QAASA,IAE/B4C,SAAU,WACN,UAEJpJ,WAAY3E,EAAE6Q,KACdlC,MAAO3O,EAAE6Q,KACThM,QAAS7E,EAAE6Q,KACX3B,SAAUlP,EAAE6Q,KACZtN,cACA8K,WAAY,SAAU9E,GAElB,MADAxF,MAAKwB,SAAWgE,EACTxF,QAkBXoC,GACI,OACA,QACA,KACA,QACD7B,GACC,KACA,OAERrB,EAAa,WACTM,WAAY4C,EACZ2K,QAAS,SAAU3Q,GAEf,MADA4D,MAAKoH,QAAQ2F,QAAU3Q,EAChB4D,MAEXc,QAAS,SAAUC,EAAOC,GACtB,GAAiBgM,GAAb/L,EAAOjB,KAAWzD,EAAU0E,EAAK1E,QAAS0Q,EAAarL,EAAMsL,YAAaC,EAAcvL,EAAMwL,aAAcxN,EAAYJ,GAAWyB,EAAKE,YAAakM,GAAUzN,EAAU8F,UAAY9F,EAAU+B,SAAWwL,EAAY5Q,GAAW0Q,EAAW1Q,IAAWiE,EAAa6M,GAAUpM,EAAKmG,SAAWnG,EAAKmG,QAAQ2F,SAAW,GAAKtI,EAAI/D,EAAW,KAC1UO,GAAKO,WACLwL,EAAMjM,EACNA,EAAQC,EACRA,EAAMgM,GAENrP,GACAoD,EAAMnB,EAAU6F,YAAcjF,EAC9BQ,EAAIpB,EAAU6F,YAAc/E,IAE5BK,EAAMnB,EAAUpD,UAAYgE,EAC5BQ,EAAIpB,EAAUpD,UAAYkE,MAItCxB,EAAa,QACTM,WAAY4C,EACZuH,KAAM,SAAUpN,EAASqD,EAAW0N,GAChChO,EAAOG,UAAUkK,KAAKd,KAAK7I,KAAMzD,EAASqD,GAC1CI,KAAKoH,SAAYkG,SAAUA,IAE/BC,gBAAiB,SAAUnR,GAEvB,MADA4D,MAAKoH,QAAQmG,gBAAkBnR,EACxB4D,MAEXgK,SAAU,WAAA,GACF/I,GAAOjB,KAAMwF,EAAUvE,EAAKO,SAAU8L,EAAWrM,EAAKmG,QAAQkG,SAAUP,EAAU9L,EAAKmG,QAAQmG,iBAAmB,EAAGC,EAAMvM,EAAKE,WAChI6I,GAAYpI,EAAMlC,GAAGuB,EAAK1E,SAASkR,QAAQD,GAAKlD,WAAW9E,GAI/D,OAHI8H,IACAtD,EAASlN,KAAK8E,EAAMlC,GAAG4N,GAAUG,QAAQjO,GAAWgO,GAAKhI,SAASuH,QAAQA,GAASzC,YAAY9E,IAE5FwE,KA0Cf5J,EAAmB,OAAQ,UAAW,EAAG,GACzCA,EAAmB,OAAQ,QAAS,EAAG,KACvClB,EAAa,eACT4B,QAAS,SAAUC,EAAOC,GACtB,GAAyH0M,GAArHzM,EAAOjB,KAAMzD,EAAU0E,EAAK1E,QAAS6K,EAAUnG,EAAKmG,QAASuG,EAASpR,EAAQ8E,KAAKuD,IAASyI,EAASjG,EAAQiG,OAAgB7H,EAAUvE,EAAKO,QAC3IgE,IAAsB,OAAXmI,GACZpR,EAAQ8E,KAAKuD,GAAQlG,WAAWnC,EAAQE,IAAI,UAAY2K,EAAQwG,QAEpEF,EAASnR,EAAQ8E,KAAKuD,KAAW,EACjC5D,EAAI,UAAYoG,EAAQwG,MAASpI,EAA4BkI,EAAlBA,EAASL,KAG5DnO,EAAa,WACT4B,QAAS,SAAUC,EAAOC,GACtB,GAAIC,GAAOjB,KAAMzD,EAAU0E,EAAK1E,QAAS6K,EAAUnG,EAAKmG,QAASiG,EAASjG,EAAQiG,OAAO/F,MAAM,KAAM9B,EAAUvE,EAAKO,QAChH7D,IACAqD,EAAIuC,WAAciC,EAAsB,EAAZ6H,EAAO,GACnCrM,EAAIwC,WAAcgC,EAAsB,EAAZ6H,EAAO,KAEnCrM,EAAIuE,KAAQC,EAAsB,EAAZ6H,EAAO,GAC7BrM,EAAI8E,IAAON,EAAsB,EAAZ6H,EAAO,IAEhC9Q,EAAQE,IAAI,WAGpByC,EAAa,UACTM,YACI,aACA,YAEJoK,SAAU9E,IACVhE,QAAS,SAAUC,EAAOC,GACtB,GAAIC,GAAOjB,KAAMzD,EAAU0E,EAAK1E,QAAS6K,EAAUnG,EAAKmG,QAAS5B,EAAUvE,EAAKO,SAAUhF,EAA+B,aAApByE,EAAKE,WAA4BW,EAASC,EAAO8L,EAAYtR,EAAQ,GAAGsH,MAAMrH,GAAWsR,EAAYvR,EAAQ8E,KAAK7E,GAAWsB,EAASY,WAAWoP,GAAaD,GAAYE,EAAaxJ,EAAMhI,EAAQE,IAAID,EAAUkI,GAAMlI,KAC9TuE,GAAMiN,SAAWrJ,EACjB7G,EAASsJ,GAAWA,EAAQuD,MAAQoD,GAAcjQ,EAASA,GAAUiQ,EACrE/M,EAAIxE,IAAagJ,EAAU,EAAI1H,GAAU2G,EACzC1D,EAAMvE,IAAagJ,EAAU1H,EAAS,GAAK2G,EACvCqJ,IAAc5R,GACdK,EAAQ8E,KAAK7E,EAAUqR,IAG/BjN,WAAY,WACR,MAAOZ,MAAKwB,UAEhB2J,SAAU,WACN,GAAIlK,GAAOjB,KAAMzD,EAAU0E,EAAK1E,QAASC,EAA+B,aAApByE,EAAKE,WAA4BW,EAASC,EAAOjE,EAASvB,EAAQ8E,KAAK7E,EACvHsB,IAAU4G,GAAQ5G,IAAW0G,GAC7BkD,WAAW,WACPnL,EAAQE,IAAID,EAAUkI,GAAMjI,IAAID,IACjC,MAIX6F,GACAwJ,SAAU,WACVM,WAAY,EACZC,UAAW,EACXnJ,MAAO,GAEX/D,EAAa,YACTyK,KAAM,SAAUpN,EAASwN,GACrB/J,KAAKzD,QAAUA,EACfyD,KAAKoH,SAAY2C,OAAQA,GACzB/J,KAAK4J,YAETgB,MAAO,WACH5K,KAAKzD,QAAQ0R,SAAStK,SAASuK,OAEnCpN,QAAS,SAAUC,EAAOC,GACtB,GAAIC,GAAOjB,KAAMzD,EAAU0E,EAAK1E,QAAS4R,EAAWzO,EAAG0O,IAAI7R,GAAU8R,EAAW3O,EAAG0O,IAAInN,EAAKmG,QAAQ2C,QAASuE,EAAerQ,EAAkB1B,EAAS,SAAU0G,EAAQvD,EAAG6O,UAAUF,EAAUF,GAAWK,EAAkB9O,EAAG8O,gBAAgBH,EAAUF,EAC1P5O,GAAOwB,EAAOsB,GACdrB,EAAIiC,MAAQ,EACZ1G,EAAQE,IAAIsB,GAAW,YAAYtB,IAAIsB,IACvCxB,EAAQE,IAAIsB,GAAW,SAAWuQ,EAAe,KACjDvN,EAAM+E,IAAMqI,EAASrI,IACrB/E,EAAMwE,KAAO4I,EAAS5I,KACtBxE,EAAMyN,gBAAkBA,EAAgBC,EAAIhK,EAAK,IAAM+J,EAAgBE,EAAIjK,EACvExD,EAAKO,SACLT,EAAMkC,MAAQA,EAEdjC,EAAIiC,MAAQA,KAIpBjB,GACA8D,IAAK,6BACLC,OAAQ,6BACRR,KAAM,6BACNI,MAAO,8BAEPrD,GACAwD,KACI/E,MAAO,gBACPC,IAAK,mBAET+E,QACIhF,MAAO,mBACPC,IAAK,iBAETuE,MACIxE,MAAO,gBACPC,IAAK,oBAET2E,OACI5E,MAAO,kBACPC,IAAK,kBAOb9B,EAAa,eACTM,WAAY4C,EACZuH,KAAM,SAAUpN,EAASqD,EAAW8B,GAChCpC,EAAOG,UAAUkK,KAAKd,KAAK7I,KAAMzD,EAASqD,GAC1CI,KAAK2O,WAAajN,GAEtBZ,QAAS,SAAUC,EAAOC,GACtB,GAAIC,GAAOjB,KAAMwF,EAAUvE,EAAKO,SAAU5B,EAAY4F,EAAUhG,GAAWyB,EAAKE,YAAYqE,QAAUvE,EAAKE,WAAYyN,EAAWtM,EAAU1C,EAC5ImB,GAAM8N,OAAS,EACX5N,EAAK6N,cACL/N,EAAMgO,KAAOtN,EAAWR,EAAK0N,WAAY/M,EAAMpC,WAAWI,GAAW4F,UAEzEzE,EAAMoE,IAAYR,EAClB3D,EAAIjD,IAAauH,IAAyBE,EAAUoJ,EAAS7N,MAAQ6N,EAAS5N,KAC9ED,EAAMhD,IAAauH,IAAyBE,EAAUoJ,EAAS5N,IAAM4N,EAAS7N,QAElF6J,MAAO,WACH5K,KAAK2O,WAAWK,OAAOhP,KAAKzD,UAEhC0S,KAAM,SAAU7S,GAEZ,MADA4D,MAAKkP,MAAQ9S,EACN4D,MAEXY,WAAY,WACR,GAAIK,GAAOjB,KAAMwF,EAAUvE,EAAKO,SAAUyN,EAAOhO,EAAKiO,KACtD,OAAO1J,KAAYyJ,IAASzJ,GAAWyJ,GAE3CxN,WAAY,SAAUrF,GAElB,MADA4D,MAAK8O,YAAc1S,EACZ4D,MAEXmP,UAAW,WAEP,MADAnP,MAAKzD,QAAQ6S,SAAS,aACfpP,QAGfd,EAAa,cACTM,WAAY4C,EACZuH,KAAM,SAAUpN,EAASqD,EAAW8B,GAChCpC,EAAOG,UAAUkK,KAAKd,KAAK7I,KAAMzD,EAASqD,GAC1CI,KAAK2O,WAAajN,GAEtBkI,SAAU,QACV9I,QAAS,SAAUC,EAAOC,GACtB,GAAIC,GAAOjB,KAAMJ,EAAYqB,EAAKO,SAAWhC,GAAWyB,EAAKE,YAAYqE,QAAUvE,EAAKE,UACxFJ,GAAMgO,KAAOtN,EAAWR,EAAK0N,WAAY/O,GACzCmB,EAAMsO,QAAU,KAChBrO,EAAIqO,QAAU,GAElBzO,WAAY,WACR,GAAIK,GAAOjB,KAAMwF,EAAUvE,EAAKO,SAAUyN,EAAOhO,EAAKiO,KACtD,OAAO1J,KAAYyJ,IAASzJ,GAAWyJ,GAE3CA,KAAM,SAAU7S,GAEZ,MADA4D,MAAKkP,MAAQ9S,EACN4D,QAGfd,EAAa,YACTM,YACI,aACA,YAEJmK,KAAM,SAAUpN,EAASqD,EAAWqP,EAAMK,GACtChQ,EAAOG,UAAUkK,KAAKd,KAAK7I,KAAMzD,EAASqD,GAC1CI,KAAKoH,WACLpH,KAAKoH,QAAQ6H,KAAOA,EACpBjP,KAAKoH,QAAQkI,KAAOA,GAExBtF,SAAU,WACN,GAAyLuF,GAArLtO,EAAOjB,KAAMoH,EAAUnG,EAAKmG,QAASxH,EAAgC,eAApBqB,EAAKE,WAA8B,OAAS,MAAOqO,EAAmB5N,EAAMpC,WAAWI,GAAW4F,QAASA,EAAUvE,EAAKO,SAAgBiO,EAAYrI,EAAQ6H,KAAKS,OAAM,GAAMC,WAAW,MAAOC,EAAYxI,EAAQkI,KAAKI,OAAM,GAAMC,WAAW,MAAOpT,EAAU0E,EAAK1E,OAM5T,OALIiJ,KACA+J,EAAO3P,EACPA,EAAY4P,EACZA,EAAmBD,IAGnB3N,EAAMlC,GAAG0H,EAAQ6H,MAAMY,WAAWjQ,EAAWrD,GAAS0S,MAAK,GAAM3E,WAAW9E,GAC5E5D,EAAMlC,GAAG0H,EAAQkI,MAAMO,WAAWL,EAAkBjT,GAAS+N,WAAW9E,GACxE5D,EAAMlC,GAAG+P,GAAWK,YAAYlQ,EAAWrD,GAAS0S,MAAK,GAAMxN,YAAW,GAAM0N,YAAY7E,WAAW9E,GACvG5D,EAAMlC,GAAGkQ,GAAWE,YAAYN,EAAkBjT,GAASkF,YAAW,GAAM0N,YAAY7E,WAAW9E,KAG3G1E,QAAS,SAAUC,EAAOC,GACtBD,EAAMqE,IAAeC,GACrBtE,EAAMgP,eAAiB,cACvBhP,EAAMsO,QAAU,KAChBrO,EAAIqO,QAAU,GAElBlE,SAAU,WACNnL,KAAKzD,QAAQyT,KAAK,cAAcC,YAGxC/Q,EAAa,QACTM,YACI,aACA,YAEJmK,KAAM,SAAUpN,EAASqD,EAAWqP,EAAMK,GACtChQ,EAAOG,UAAUkK,KAAKd,KAAK7I,KAAMzD,EAASqD,GAC1CI,KAAKoH,WACLpH,KAAKoH,QAAQ6H,KAAOA,EACpBjP,KAAKoH,QAAQkI,KAAOA,GAExBtF,SAAU,WACN,GAAyLuF,GAArLtO,EAAOjB,KAAMoH,EAAUnG,EAAKmG,QAASxH,EAAgC,eAApBqB,EAAKE,WAA8B,OAAS,MAAOqO,EAAmB5N,EAAMpC,WAAWI,GAAW4F,QAASA,EAAUvE,EAAKO,SAAgBjF,EAAU0E,EAAK1E,OAM9M,OALIiJ,KACA+J,EAAO3P,EACPA,EAAY4P,EACZA,EAAmBD,IAGnB3N,EAAMlC,GAAG0H,EAAQ6H,MAAMa,YAAYlQ,EAAWrD,GAAS0S,MAAK,GAAM3E,WAAW9E,GAC7E5D,EAAMlC,GAAG0H,EAAQkI,MAAMQ,YAAYN,EAAkBjT,GAAS+N,WAAW9E,KAGjF1E,QAAS,SAAUC,GACfA,EAAMqE,IAAeC,GACrBtE,EAAMgP,eAAiB,iBAG3BxN,GAAoB3E,EAAQsS,SAASC,QACrC3N,EAAmC,+CACvCtD,EAAa,WACTkR,QAASnU,EAAE6Q,KACXuD,OAAQpU,EAAE6Q,KACVnD,KAAM,SAAUpN,EAAS+Q,EAAUgD,GAC/BhR,EAAOG,UAAUkK,KAAKd,KAAK7I,KAAMzD,GACjCyD,KAAKuQ,UAAYtU,EAAEqR,GACnBtN,KAAKwQ,iBAAmBF,GAE5B9I,SAAU,WACN,KAAUiJ,OAAM,kIAEpBC,iBAAkB,SAAUC,GAExB,MADA3Q,MAAKoQ,QAAUO,EACR3Q,MAEX4Q,gBAAiB,SAAUD,GAEvB,MADA3Q,MAAKqQ,OAASM,EACP3Q,MAEX6Q,MAAO,WACH,MAAO5U,KAAIoL,IAAIrH,KAAK8Q,UAAUzJ,IAAIrH,KAAKuQ,YAE3CQ,gBAAiB,WACb,GAAInR,GAAYI,KAAKmB,WAAY6P,EAAiB,wBAA0BhR,KAAKwQ,gBAOjF,OANI5Q,KACAoR,GAAkB,SAAWpR,GAE7BI,KAAKwB,WACLwP,GAAkB,iBAEfA,GAEXvI,SAAU,SAAUwI,GAChB,MAAKjR,KAAKmK,UAAY8G,GAAKhV,EAAEgV,EAAElH,QAAQU,GAAGjI,IAA1C,CAGA,GAAId,GAAY1B,KAAK0B,SACrBA,GAAUwP,YAAY,YAAYA,YAAYlR,KAAK+Q,mBAAmBI,IAAIpO,EAAYqG,MAAOpJ,KAAKoR,eAClGpR,KAAKuQ,UAAUrF,OAAOgG,YAAY,gBAClClR,KAAKzD,QAAQ2U,YAAY,aACrB3O,GACAb,EAAUjF,IAAIqI,GAAU,IAEvB9E,KAAKqR,YACNrR,KAAK6Q,QAAQpU,IAAIuI,GAAU,IAE/BhF,KAAKmK,SAASW,gBACP9K,MAAKmK,WAEhBN,IAAK,WACD,GAAI7J,KAAKuM,oBAAsBvM,KAAKuM,mBAAmB,GACnD,MAAOvM,MAAK2M,cAEhB,IAA8N2E,GAA1NrQ,EAAOjB,KAAMzD,EAAU0E,EAAK1E,QAAS+Q,EAAWrM,EAAKsP,UAAW7O,EAAYnF,EAAQgV,UAAUC,OAAOlE,EAASiE,WAAWE,QAASC,EAAOzQ,EAAK4P,QAAS1G,EAAWlO,EAAEmO,WAAY2B,EAAmBxP,EAAQE,IAAIuI,GA+BnN,OA9BKtD,GAAU5D,SACX4D,EAAYnF,EAAQoV,UAExB3R,KAAK0B,UAAYA,EACjB1B,KAAKmK,SAAWA,EAChBnK,KAAKqR,WAAiC,YAApBtF,EACb/L,KAAKqR,YACNK,EAAKjV,IAAIuI,GAAU,YAEnBzC,IACA+O,EAAmB5P,EAAUjF,IAAIqI,IACjCpD,EAAUjF,IAAIqI,GAAU,WAEvB/B,GAGDxG,EAAQ6S,SAAS,eACjB1N,EAAU0N,SAASpP,KAAK+Q,mBACxB/Q,KAAKoR,cAAgBnV,EAAE4G,MAAM7C,KAAM,YACnC0B,EAAUkQ,GAAG7O,EAAYqG,MAAOpJ,KAAKoR,eACrCxP,EAAMiQ,eAAe,WACjBtV,EAAQ2U,YAAY,eAAe9B,SAAS,aAC5C9B,EAAS7Q,IAAI,UAAW,IAAI2S,SAAS,gBACrCnO,EAAKmP,QAAQ9C,EAAU/Q,GACvBqF,EAAMiQ,eAAe,WACjBnQ,EAAUwP,YAAY,cAAc9B,SAAS,YAC7CnO,EAAKoP,OAAO/C,EAAU/Q,QAZ9ByD,KAAKyI,WAgBF0B,EAASY,WAEpBxE,KAAM,WACFvG,KAAKyI,cAGThG,EAAYb,EAAM8H,MAAMnK,QACxBoK,KAAM,WACF,GAAI1I,GAAOjB,IACXiB,GAAK6Q,WAAajP,EAAM5B,EAAK8Q,MAAO9Q,GACpCA,EAAK+Q,UAAW,GAEpBC,KAAMhW,EAAE6Q,KACRzC,KAAMpO,EAAE6Q,KACRoF,MAAOjW,EAAE6Q,KACTqF,SAAUlW,EAAE6Q,KACZ/L,MAAO,WACEf,KAAKoS,YAGLpS,KAAKqK,OAINrK,KAAKkS,SAHLlS,KAAKgS,UAAW,EAChBpQ,EAAMiQ,eAAe7R,KAAK8R,eAKlCM,QAAS,WACL,OAAO,GAEXC,OAAQ,WACJrS,KAAKgS,UAAW,EAChBhS,KAAKmS,YAETJ,MAAO,WACH,GAAI9Q,GAAOjB,IACNiB,GAAK+Q,WAGV/Q,EAAKgR,OACAhR,EAAKoJ,QAGNpJ,EAAK+Q,UAAW,EAChB/Q,EAAKiR,SAHLtQ,EAAMiQ,eAAe5Q,EAAK6Q,gBAOlCpP,EAAaD,EAAUlD,QACvBoK,KAAM,SAAUvC,GACZ,GAAInG,GAAOjB,IACXT,GAAO0B,EAAMmG,GACb3E,EAAUyD,GAAGyD,KAAKd,KAAK5H,IAE3BoJ,KAAM,WACF,MAAOrK,MAAKsS,cAAgBtS,KAAKwH,UAErC8K,WAAY,WACR,MAAO3T,MAAK4T,IAAIvS,KAAKwH,SAAU,GAAIgL,MAASxS,KAAKyS,YAErDC,OAAQ,SAAUtL,GACd,GAAInG,GAAOjB,KAAM2S,EAAU1R,EAAK0R,OAChC1R,GAAK2R,QAAUD,EAAQ1R,EAAK2M,MAC5B3M,EAAK4R,MAAQzL,EAAQ0L,SAAW7R,EAAK2R,QACrC3R,EAAKuG,SAAsC,gBAApBJ,GAAQI,SAAuBJ,EAAQI,SAAW,IACzEvG,EAAKgR,KAAOhR,EAAK8R,WAAW3L,EAAQK,MACpCxG,EAAKwR,UAAY,GAAID,MACrBvR,EAAKF,SAETgS,WAAY,SAAUtL,GAClB,GAAIxG,GAAOjB,IACX,OAAO,YACHiB,EAAK0R,QAAQK,SAAS/R,EAAK2M,KAAMnG,EAAKxG,EAAKqR,aAAcrR,EAAK2R,QAAS3R,EAAK4R,MAAO5R,EAAKuG,eAIpGjI,EAAOmD,GACHuQ,YAAa,SAAUC,EAAGC,EAAGC,EAAGC,GAC5B,MAAOH,IAAKG,EAAIF,EAAIC,EAAIA,IAAMzU,KAAK2U,IAAI,MAASJ,EAAIG,GAAK,GAAKF,GAElEI,YAAa,SAAUL,EAAGC,EAAGC,EAAGC,EAAGG,GAE/B,MADAA,GAAI,QACGJ,IAAMF,EAAIA,EAAIG,EAAI,GAAKH,IAAMM,EAAI,GAAKN,EAAIM,GAAK,GAAKL,KAGnEzT,EAAG+C,UAAYA,EACf/C,EAAGgD,WAAaA,EAChBhD,EAAGR,aAAeA,EAClBQ,EAAG0O,IAAM,SAAU7R,GACfA,EAAUN,EAAEM,EACZ,IAAIkX,GAASlX,EAAQ8Q,QAGrB,OAFAoG,GAAOC,MAAQ9R,EAAMsL,YAAY3Q,GACjCkX,EAAOvK,OAAStH,EAAMwL,aAAa7Q,GAC5BkX,GAEX/T,EAAG8O,gBAAkB,SAAUmF,EAAOC,GAClC,GAAInF,IAAKkF,EAAMpO,KAAOqO,EAAMrO,MAAQqO,EAAMF,OAASE,EAAMF,MAAQC,EAAMD,OAAQhF,GAAKiF,EAAM7N,IAAM8N,EAAM9N,KAAO8N,EAAM1K,QAAU0K,EAAM1K,OAASyK,EAAMzK,OAClJ,QACIuF,EAAGlN,MAAMkN,GAAK,EAAIA,EAClBC,EAAGnN,MAAMmN,GAAK,EAAIA,IAG1BhP,EAAG6O,UAAY,SAAUoF,EAAOC,GAC5B,MAAOjV,MAAK4T,IAAIoB,EAAMD,MAAQE,EAAMF,MAAOC,EAAMzK,OAAS0K,EAAM1K,SAEpExJ,EAAGmU,SAAW,SAAUF,EAAOC,GAC3B,MAAOjV,MAAKmV,IAAIH,EAAMD,MAAQE,EAAMF,MAAOC,EAAMzK,OAAS0K,EAAM1K,UAEtEvG,OAAOf,MAAMmS,QACRpR,OAAOf,OACE,kBAAV5F,SAAwBA,OAAOgY,IAAMhY,OAAS,SAAUiY,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.fx.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.fx', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'fx',\n        name: 'Effects',\n        category: 'framework',\n        description: 'Required for animation effects in all Kendo UI widgets.',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, fx = kendo.effects, each = $.each, extend = $.extend, proxy = $.proxy, support = kendo.support, browser = support.browser, transforms = support.transforms, transitions = support.transitions, scaleProperties = {\n                scale: 0,\n                scalex: 0,\n                scaley: 0,\n                scale3d: 0\n            }, translateProperties = {\n                translate: 0,\n                translatex: 0,\n                translatey: 0,\n                translate3d: 0\n            }, hasZoom = typeof document.documentElement.style.zoom !== 'undefined' && !transforms, matrix3dRegExp = /matrix3?d?\\s*\\(.*,\\s*([\\d\\.\\-]+)\\w*?,\\s*([\\d\\.\\-]+)\\w*?,\\s*([\\d\\.\\-]+)\\w*?,\\s*([\\d\\.\\-]+)\\w*?/i, cssParamsRegExp = /^(-?[\\d\\.\\-]+)?[\\w\\s]*,?\\s*(-?[\\d\\.\\-]+)?[\\w\\s]*/i, translateXRegExp = /translatex?$/i, oldEffectsRegExp = /(zoom|fade|expand)(\\w+)/, singleEffectRegExp = /(zoom|fade|expand)/, unitRegExp = /[xy]$/i, transformProps = [\n                'perspective',\n                'rotate',\n                'rotatex',\n                'rotatey',\n                'rotatez',\n                'rotate3d',\n                'scale',\n                'scalex',\n                'scaley',\n                'scalez',\n                'scale3d',\n                'skew',\n                'skewx',\n                'skewy',\n                'translate',\n                'translatex',\n                'translatey',\n                'translatez',\n                'translate3d',\n                'matrix',\n                'matrix3d'\n            ], transform2d = [\n                'rotate',\n                'scale',\n                'scalex',\n                'scaley',\n                'skew',\n                'skewx',\n                'skewy',\n                'translate',\n                'translatex',\n                'translatey',\n                'matrix'\n            ], transform2units = {\n                'rotate': 'deg',\n                scale: '',\n                skew: 'px',\n                translate: 'px'\n            }, cssPrefix = transforms.css, round = Math.round, BLANK = '', PX = 'px', NONE = 'none', AUTO = 'auto', WIDTH = 'width', HEIGHT = 'height', HIDDEN = 'hidden', ORIGIN = 'origin', ABORT_ID = 'abortId', OVERFLOW = 'overflow', TRANSLATE = 'translate', POSITION = 'position', COMPLETE_CALLBACK = 'completeCallback', TRANSITION = cssPrefix + 'transition', TRANSFORM = cssPrefix + 'transform', BACKFACE = cssPrefix + 'backface-visibility', PERSPECTIVE = cssPrefix + 'perspective', DEFAULT_PERSPECTIVE = '1500px', TRANSFORM_PERSPECTIVE = 'perspective(' + DEFAULT_PERSPECTIVE + ')', directions = {\n                left: {\n                    reverse: 'right',\n                    property: 'left',\n                    transition: 'translatex',\n                    vertical: false,\n                    modifier: -1\n                },\n                right: {\n                    reverse: 'left',\n                    property: 'left',\n                    transition: 'translatex',\n                    vertical: false,\n                    modifier: 1\n                },\n                down: {\n                    reverse: 'up',\n                    property: 'top',\n                    transition: 'translatey',\n                    vertical: true,\n                    modifier: 1\n                },\n                up: {\n                    reverse: 'down',\n                    property: 'top',\n                    transition: 'translatey',\n                    vertical: true,\n                    modifier: -1\n                },\n                top: { reverse: 'bottom' },\n                bottom: { reverse: 'top' },\n                'in': {\n                    reverse: 'out',\n                    modifier: -1\n                },\n                out: {\n                    reverse: 'in',\n                    modifier: 1\n                },\n                vertical: { reverse: 'vertical' },\n                horizontal: { reverse: 'horizontal' }\n            };\n        kendo.directions = directions;\n        extend($.fn, {\n            kendoStop: function (clearQueue, gotoEnd) {\n                if (transitions) {\n                    return fx.stopQueue(this, clearQueue || false, gotoEnd || false);\n                } else {\n                    return this.stop(clearQueue, gotoEnd);\n                }\n            }\n        });\n        if (transforms && !transitions) {\n            each(transform2d, function (idx, value) {\n                $.fn[value] = function (val) {\n                    if (typeof val == 'undefined') {\n                        return animationProperty(this, value);\n                    } else {\n                        var that = $(this)[0], transformValue = value + '(' + val + transform2units[value.replace(unitRegExp, '')] + ')';\n                        if (that.style.cssText.indexOf(TRANSFORM) == -1) {\n                            $(this).css(TRANSFORM, transformValue);\n                        } else {\n                            that.style.cssText = that.style.cssText.replace(new RegExp(value + '\\\\(.*?\\\\)', 'i'), transformValue);\n                        }\n                    }\n                    return this;\n                };\n                $.fx.step[value] = function (fx) {\n                    $(fx.elem)[value](fx.now);\n                };\n            });\n            var curProxy = $.fx.prototype.cur;\n            $.fx.prototype.cur = function () {\n                if (transform2d.indexOf(this.prop) != -1) {\n                    return parseFloat($(this.elem)[this.prop]());\n                }\n                return curProxy.apply(this, arguments);\n            };\n        }\n        kendo.toggleClass = function (element, classes, options, add) {\n            if (classes) {\n                classes = classes.split(' ');\n                if (transitions) {\n                    options = extend({\n                        exclusive: 'all',\n                        duration: 400,\n                        ease: 'ease-out'\n                    }, options);\n                    element.css(TRANSITION, options.exclusive + ' ' + options.duration + 'ms ' + options.ease);\n                    setTimeout(function () {\n                        element.css(TRANSITION, '').css(HEIGHT);\n                    }, options.duration);\n                }\n                each(classes, function (idx, value) {\n                    element.toggleClass(value, add);\n                });\n            }\n            return element;\n        };\n        kendo.parseEffects = function (input, mirror) {\n            var effects = {};\n            if (typeof input === 'string') {\n                each(input.split(' '), function (idx, value) {\n                    var redirectedEffect = !singleEffectRegExp.test(value), resolved = value.replace(oldEffectsRegExp, function (match, $1, $2) {\n                            return $1 + ':' + $2.toLowerCase();\n                        }), effect = resolved.split(':'), direction = effect[1], effectBody = {};\n                    if (effect.length > 1) {\n                        effectBody.direction = mirror && redirectedEffect ? directions[direction].reverse : direction;\n                    }\n                    effects[effect[0]] = effectBody;\n                });\n            } else {\n                each(input, function (idx) {\n                    var direction = this.direction;\n                    if (direction && mirror && !singleEffectRegExp.test(idx)) {\n                        this.direction = directions[direction].reverse;\n                    }\n                    effects[idx] = this;\n                });\n            }\n            return effects;\n        };\n        function parseInteger(value) {\n            return parseInt(value, 10);\n        }\n        function parseCSS(element, property) {\n            return parseInteger(element.css(property));\n        }\n        function keys(obj) {\n            var acc = [];\n            for (var propertyName in obj) {\n                acc.push(propertyName);\n            }\n            return acc;\n        }\n        function strip3DTransforms(properties) {\n            for (var key in properties) {\n                if (transformProps.indexOf(key) != -1 && transform2d.indexOf(key) == -1) {\n                    delete properties[key];\n                }\n            }\n            return properties;\n        }\n        function normalizeCSS(element, properties) {\n            var transformation = [], cssValues = {}, lowerKey, key, value, isTransformed;\n            for (key in properties) {\n                lowerKey = key.toLowerCase();\n                isTransformed = transforms && transformProps.indexOf(lowerKey) != -1;\n                if (!support.hasHW3D && isTransformed && transform2d.indexOf(lowerKey) == -1) {\n                    delete properties[key];\n                } else {\n                    value = properties[key];\n                    if (isTransformed) {\n                        transformation.push(key + '(' + value + ')');\n                    } else {\n                        cssValues[key] = value;\n                    }\n                }\n            }\n            if (transformation.length) {\n                cssValues[TRANSFORM] = transformation.join(' ');\n            }\n            return cssValues;\n        }\n        if (transitions) {\n            extend(fx, {\n                transition: function (element, properties, options) {\n                    var css, delay = 0, oldKeys = element.data('keys') || [], timeoutID;\n                    options = extend({\n                        duration: 200,\n                        ease: 'ease-out',\n                        complete: null,\n                        exclusive: 'all'\n                    }, options);\n                    var stopTransitionCalled = false;\n                    var stopTransition = function () {\n                        if (!stopTransitionCalled) {\n                            stopTransitionCalled = true;\n                            if (timeoutID) {\n                                clearTimeout(timeoutID);\n                                timeoutID = null;\n                            }\n                            element.removeData(ABORT_ID).dequeue().css(TRANSITION, '').css(TRANSITION);\n                            options.complete.call(element);\n                        }\n                    };\n                    options.duration = $.fx ? $.fx.speeds[options.duration] || options.duration : options.duration;\n                    css = normalizeCSS(element, properties);\n                    $.merge(oldKeys, keys(css));\n                    if ($.hasOwnProperty('uniqueSort')) {\n                        element.data('keys', $.uniqueSort(oldKeys)).height();\n                    } else {\n                        element.data('keys', $.unique(oldKeys)).height();\n                    }\n                    element.css(TRANSITION, options.exclusive + ' ' + options.duration + 'ms ' + options.ease).css(TRANSITION);\n                    element.css(css).css(TRANSFORM);\n                    if (transitions.event) {\n                        element.one(transitions.event, stopTransition);\n                        if (options.duration !== 0) {\n                            delay = 500;\n                        }\n                    }\n                    timeoutID = setTimeout(stopTransition, options.duration + delay);\n                    element.data(ABORT_ID, timeoutID);\n                    element.data(COMPLETE_CALLBACK, stopTransition);\n                },\n                stopQueue: function (element, clearQueue, gotoEnd) {\n                    var cssValues, taskKeys = element.data('keys'), retainPosition = !gotoEnd && taskKeys, completeCallback = element.data(COMPLETE_CALLBACK);\n                    if (retainPosition) {\n                        cssValues = kendo.getComputedStyles(element[0], taskKeys);\n                    }\n                    if (completeCallback) {\n                        completeCallback();\n                    }\n                    if (retainPosition) {\n                        element.css(cssValues);\n                    }\n                    return element.removeData('keys').stop(clearQueue);\n                }\n            });\n        }\n        function animationProperty(element, property) {\n            if (transforms) {\n                var transform = element.css(TRANSFORM);\n                if (transform == NONE) {\n                    return property == 'scale' ? 1 : 0;\n                }\n                var match = transform.match(new RegExp(property + '\\\\s*\\\\(([\\\\d\\\\w\\\\.]+)')), computed = 0;\n                if (match) {\n                    computed = parseInteger(match[1]);\n                } else {\n                    match = transform.match(matrix3dRegExp) || [\n                        0,\n                        0,\n                        0,\n                        0,\n                        0\n                    ];\n                    property = property.toLowerCase();\n                    if (translateXRegExp.test(property)) {\n                        computed = parseFloat(match[3] / match[2]);\n                    } else if (property == 'translatey') {\n                        computed = parseFloat(match[4] / match[2]);\n                    } else if (property == 'scale') {\n                        computed = parseFloat(match[2]);\n                    } else if (property == 'rotate') {\n                        computed = parseFloat(Math.atan2(match[2], match[1]));\n                    }\n                }\n                return computed;\n            } else {\n                return parseFloat(element.css(property));\n            }\n        }\n        var EffectSet = kendo.Class.extend({\n            init: function (element, options) {\n                var that = this;\n                that.element = element;\n                that.effects = [];\n                that.options = options;\n                that.restore = [];\n            },\n            run: function (effects) {\n                var that = this, effect, idx, jdx, length = effects.length, element = that.element, options = that.options, deferred = $.Deferred(), start = {}, end = {}, target, children, childrenLength;\n                that.effects = effects;\n                deferred.done($.proxy(that, 'complete'));\n                element.data('animating', true);\n                for (idx = 0; idx < length; idx++) {\n                    effect = effects[idx];\n                    effect.setReverse(options.reverse);\n                    effect.setOptions(options);\n                    that.addRestoreProperties(effect.restore);\n                    effect.prepare(start, end);\n                    children = effect.children();\n                    for (jdx = 0, childrenLength = children.length; jdx < childrenLength; jdx++) {\n                        children[jdx].duration(options.duration).run();\n                    }\n                }\n                for (var effectName in options.effects) {\n                    extend(end, options.effects[effectName].properties);\n                }\n                if (!element.is(':visible')) {\n                    extend(start, { display: element.data('olddisplay') || 'block' });\n                }\n                if (transforms && !options.reset) {\n                    target = element.data('targetTransform');\n                    if (target) {\n                        start = extend(target, start);\n                    }\n                }\n                start = normalizeCSS(element, start);\n                if (transforms && !transitions) {\n                    start = strip3DTransforms(start);\n                }\n                element.css(start).css(TRANSFORM);\n                for (idx = 0; idx < length; idx++) {\n                    effects[idx].setup();\n                }\n                if (options.init) {\n                    options.init();\n                }\n                element.data('targetTransform', end);\n                fx.animate(element, end, extend({}, options, { complete: deferred.resolve }));\n                return deferred.promise();\n            },\n            stop: function () {\n                $(this.element).kendoStop(true, true);\n            },\n            addRestoreProperties: function (restore) {\n                var element = this.element, value, i = 0, length = restore.length;\n                for (; i < length; i++) {\n                    value = restore[i];\n                    this.restore.push(value);\n                    if (!element.data(value)) {\n                        element.data(value, element.css(value));\n                    }\n                }\n            },\n            restoreCallback: function () {\n                var element = this.element;\n                for (var i = 0, length = this.restore.length; i < length; i++) {\n                    var value = this.restore[i];\n                    element.css(value, element.data(value));\n                }\n            },\n            complete: function () {\n                var that = this, idx = 0, element = that.element, options = that.options, effects = that.effects, length = effects.length;\n                element.removeData('animating').dequeue();\n                if (options.hide) {\n                    element.data('olddisplay', element.css('display')).hide();\n                }\n                this.restoreCallback();\n                if (hasZoom && !transforms) {\n                    setTimeout($.proxy(this, 'restoreCallback'), 0);\n                }\n                for (; idx < length; idx++) {\n                    effects[idx].teardown();\n                }\n                if (options.completeCallback) {\n                    options.completeCallback(element);\n                }\n            }\n        });\n        fx.promise = function (element, options) {\n            var effects = [], effectClass, effectSet = new EffectSet(element, options), parsedEffects = kendo.parseEffects(options.effects), effect;\n            options.effects = parsedEffects;\n            for (var effectName in parsedEffects) {\n                effectClass = fx[capitalize(effectName)];\n                if (effectClass) {\n                    effect = new effectClass(element, parsedEffects[effectName].direction);\n                    effects.push(effect);\n                }\n            }\n            if (effects[0]) {\n                effectSet.run(effects);\n            } else {\n                if (!element.is(':visible')) {\n                    element.css({ display: element.data('olddisplay') || 'block' }).css('display');\n                }\n                if (options.init) {\n                    options.init();\n                }\n                element.dequeue();\n                effectSet.complete();\n            }\n        };\n        extend(fx, {\n            animate: function (elements, properties, options) {\n                var useTransition = options.transition !== false;\n                delete options.transition;\n                if (transitions && 'transition' in fx && useTransition) {\n                    fx.transition(elements, properties, options);\n                } else {\n                    if (transforms) {\n                        elements.animate(strip3DTransforms(properties), {\n                            queue: false,\n                            show: false,\n                            hide: false,\n                            duration: options.duration,\n                            complete: options.complete\n                        });\n                    } else {\n                        elements.each(function () {\n                            var element = $(this), multiple = {};\n                            each(transformProps, function (idx, value) {\n                                var params, currentValue = properties ? properties[value] + ' ' : null;\n                                if (currentValue) {\n                                    var single = properties;\n                                    if (value in scaleProperties && properties[value] !== undefined) {\n                                        params = currentValue.match(cssParamsRegExp);\n                                        if (transforms) {\n                                            extend(single, { scale: +params[0] });\n                                        }\n                                    } else {\n                                        if (value in translateProperties && properties[value] !== undefined) {\n                                            var position = element.css(POSITION), isFixed = position == 'absolute' || position == 'fixed';\n                                            if (!element.data(TRANSLATE)) {\n                                                if (isFixed) {\n                                                    element.data(TRANSLATE, {\n                                                        top: parseCSS(element, 'top') || 0,\n                                                        left: parseCSS(element, 'left') || 0,\n                                                        bottom: parseCSS(element, 'bottom'),\n                                                        right: parseCSS(element, 'right')\n                                                    });\n                                                } else {\n                                                    element.data(TRANSLATE, {\n                                                        top: parseCSS(element, 'marginTop') || 0,\n                                                        left: parseCSS(element, 'marginLeft') || 0\n                                                    });\n                                                }\n                                            }\n                                            var originalPosition = element.data(TRANSLATE);\n                                            params = currentValue.match(cssParamsRegExp);\n                                            if (params) {\n                                                var dX = value == TRANSLATE + 'y' ? +null : +params[1], dY = value == TRANSLATE + 'y' ? +params[1] : +params[2];\n                                                if (isFixed) {\n                                                    if (!isNaN(originalPosition.right)) {\n                                                        if (!isNaN(dX)) {\n                                                            extend(single, { right: originalPosition.right - dX });\n                                                        }\n                                                    } else {\n                                                        if (!isNaN(dX)) {\n                                                            extend(single, { left: originalPosition.left + dX });\n                                                        }\n                                                    }\n                                                    if (!isNaN(originalPosition.bottom)) {\n                                                        if (!isNaN(dY)) {\n                                                            extend(single, { bottom: originalPosition.bottom - dY });\n                                                        }\n                                                    } else {\n                                                        if (!isNaN(dY)) {\n                                                            extend(single, { top: originalPosition.top + dY });\n                                                        }\n                                                    }\n                                                } else {\n                                                    if (!isNaN(dX)) {\n                                                        extend(single, { marginLeft: originalPosition.left + dX });\n                                                    }\n                                                    if (!isNaN(dY)) {\n                                                        extend(single, { marginTop: originalPosition.top + dY });\n                                                    }\n                                                }\n                                            }\n                                        }\n                                    }\n                                    if (!transforms && value != 'scale' && value in single) {\n                                        delete single[value];\n                                    }\n                                    if (single) {\n                                        extend(multiple, single);\n                                    }\n                                }\n                            });\n                            if (browser.msie) {\n                                delete multiple.scale;\n                            }\n                            element.animate(multiple, {\n                                queue: false,\n                                show: false,\n                                hide: false,\n                                duration: options.duration,\n                                complete: options.complete\n                            });\n                        });\n                    }\n                }\n            }\n        });\n        fx.animatedPromise = fx.promise;\n        var Effect = kendo.Class.extend({\n            init: function (element, direction) {\n                var that = this;\n                that.element = element;\n                that._direction = direction;\n                that.options = {};\n                that._additionalEffects = [];\n                if (!that.restore) {\n                    that.restore = [];\n                }\n            },\n            reverse: function () {\n                this._reverse = true;\n                return this.run();\n            },\n            play: function () {\n                this._reverse = false;\n                return this.run();\n            },\n            add: function (additional) {\n                this._additionalEffects.push(additional);\n                return this;\n            },\n            direction: function (value) {\n                this._direction = value;\n                return this;\n            },\n            duration: function (duration) {\n                this._duration = duration;\n                return this;\n            },\n            compositeRun: function () {\n                var that = this, effectSet = new EffectSet(that.element, {\n                        reverse: that._reverse,\n                        duration: that._duration\n                    }), effects = that._additionalEffects.concat([that]);\n                return effectSet.run(effects);\n            },\n            run: function () {\n                if (this._additionalEffects && this._additionalEffects[0]) {\n                    return this.compositeRun();\n                }\n                var that = this, element = that.element, idx = 0, restore = that.restore, length = restore.length, value, deferred = $.Deferred(), start = {}, end = {}, target, children = that.children(), childrenLength = children.length;\n                deferred.done($.proxy(that, '_complete'));\n                element.data('animating', true);\n                for (idx = 0; idx < length; idx++) {\n                    value = restore[idx];\n                    if (!element.data(value)) {\n                        element.data(value, element.css(value));\n                    }\n                }\n                for (idx = 0; idx < childrenLength; idx++) {\n                    children[idx].duration(that._duration).run();\n                }\n                that.prepare(start, end);\n                if (!element.is(':visible')) {\n                    extend(start, { display: element.data('olddisplay') || 'block' });\n                }\n                if (transforms) {\n                    target = element.data('targetTransform');\n                    if (target) {\n                        start = extend(target, start);\n                    }\n                }\n                start = normalizeCSS(element, start);\n                if (transforms && !transitions) {\n                    start = strip3DTransforms(start);\n                }\n                element.css(start).css(TRANSFORM);\n                that.setup();\n                element.data('targetTransform', end);\n                fx.animate(element, end, {\n                    duration: that._duration,\n                    complete: deferred.resolve\n                });\n                return deferred.promise();\n            },\n            stop: function () {\n                var idx = 0, children = this.children(), childrenLength = children.length;\n                for (idx = 0; idx < childrenLength; idx++) {\n                    children[idx].stop();\n                }\n                $(this.element).kendoStop(true, true);\n                return this;\n            },\n            restoreCallback: function () {\n                var element = this.element;\n                for (var i = 0, length = this.restore.length; i < length; i++) {\n                    var value = this.restore[i];\n                    element.css(value, element.data(value));\n                }\n            },\n            _complete: function () {\n                var that = this, element = that.element;\n                element.removeData('animating').dequeue();\n                that.restoreCallback();\n                if (that.shouldHide()) {\n                    element.data('olddisplay', element.css('display')).hide();\n                }\n                if (hasZoom && !transforms) {\n                    setTimeout($.proxy(that, 'restoreCallback'), 0);\n                }\n                that.teardown();\n            },\n            setOptions: function (options) {\n                extend(true, this.options, options);\n            },\n            children: function () {\n                return [];\n            },\n            shouldHide: $.noop,\n            setup: $.noop,\n            prepare: $.noop,\n            teardown: $.noop,\n            directions: [],\n            setReverse: function (reverse) {\n                this._reverse = reverse;\n                return this;\n            }\n        });\n        function capitalize(word) {\n            return word.charAt(0).toUpperCase() + word.substring(1);\n        }\n        function createEffect(name, definition) {\n            var effectClass = Effect.extend(definition), directions = effectClass.prototype.directions;\n            fx[capitalize(name)] = effectClass;\n            fx.Element.prototype[name] = function (direction, opt1, opt2, opt3) {\n                return new effectClass(this.element, direction, opt1, opt2, opt3);\n            };\n            each(directions, function (idx, theDirection) {\n                fx.Element.prototype[name + capitalize(theDirection)] = function (opt1, opt2, opt3) {\n                    return new effectClass(this.element, theDirection, opt1, opt2, opt3);\n                };\n            });\n        }\n        var FOUR_DIRECTIONS = [\n                'left',\n                'right',\n                'up',\n                'down'\n            ], IN_OUT = [\n                'in',\n                'out'\n            ];\n        createEffect('slideIn', {\n            directions: FOUR_DIRECTIONS,\n            divisor: function (value) {\n                this.options.divisor = value;\n                return this;\n            },\n            prepare: function (start, end) {\n                var that = this, tmp, element = that.element, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, direction = directions[that._direction], offset = -direction.modifier * (direction.vertical ? outerHeight(element) : outerWidth(element)), startValue = offset / (that.options && that.options.divisor || 1) + PX, endValue = '0px';\n                if (that._reverse) {\n                    tmp = start;\n                    start = end;\n                    end = tmp;\n                }\n                if (transforms) {\n                    start[direction.transition] = startValue;\n                    end[direction.transition] = endValue;\n                } else {\n                    start[direction.property] = startValue;\n                    end[direction.property] = endValue;\n                }\n            }\n        });\n        createEffect('tile', {\n            directions: FOUR_DIRECTIONS,\n            init: function (element, direction, previous) {\n                Effect.prototype.init.call(this, element, direction);\n                this.options = { previous: previous };\n            },\n            previousDivisor: function (value) {\n                this.options.previousDivisor = value;\n                return this;\n            },\n            children: function () {\n                var that = this, reverse = that._reverse, previous = that.options.previous, divisor = that.options.previousDivisor || 1, dir = that._direction;\n                var children = [kendo.fx(that.element).slideIn(dir).setReverse(reverse)];\n                if (previous) {\n                    children.push(kendo.fx(previous).slideIn(directions[dir].reverse).divisor(divisor).setReverse(!reverse));\n                }\n                return children;\n            }\n        });\n        function createToggleEffect(name, property, defaultStart, defaultEnd) {\n            createEffect(name, {\n                directions: IN_OUT,\n                startValue: function (value) {\n                    this._startValue = value;\n                    return this;\n                },\n                endValue: function (value) {\n                    this._endValue = value;\n                    return this;\n                },\n                shouldHide: function () {\n                    return this._shouldHide;\n                },\n                prepare: function (start, end) {\n                    var that = this, startValue, endValue, out = this._direction === 'out', startDataValue = that.element.data(property), startDataValueIsSet = !(isNaN(startDataValue) || startDataValue == defaultStart);\n                    if (startDataValueIsSet) {\n                        startValue = startDataValue;\n                    } else if (typeof this._startValue !== 'undefined') {\n                        startValue = this._startValue;\n                    } else {\n                        startValue = out ? defaultStart : defaultEnd;\n                    }\n                    if (typeof this._endValue !== 'undefined') {\n                        endValue = this._endValue;\n                    } else {\n                        endValue = out ? defaultEnd : defaultStart;\n                    }\n                    if (this._reverse) {\n                        start[property] = endValue;\n                        end[property] = startValue;\n                    } else {\n                        start[property] = startValue;\n                        end[property] = endValue;\n                    }\n                    that._shouldHide = end[property] === defaultEnd;\n                }\n            });\n        }\n        createToggleEffect('fade', 'opacity', 1, 0);\n        createToggleEffect('zoom', 'scale', 1, 0.01);\n        createEffect('slideMargin', {\n            prepare: function (start, end) {\n                var that = this, element = that.element, options = that.options, origin = element.data(ORIGIN), offset = options.offset, margin, reverse = that._reverse;\n                if (!reverse && origin === null) {\n                    element.data(ORIGIN, parseFloat(element.css('margin-' + options.axis)));\n                }\n                margin = element.data(ORIGIN) || 0;\n                end['margin-' + options.axis] = !reverse ? margin + offset : margin;\n            }\n        });\n        createEffect('slideTo', {\n            prepare: function (start, end) {\n                var that = this, element = that.element, options = that.options, offset = options.offset.split(','), reverse = that._reverse;\n                if (transforms) {\n                    end.translatex = !reverse ? offset[0] : 0;\n                    end.translatey = !reverse ? offset[1] : 0;\n                } else {\n                    end.left = !reverse ? offset[0] : 0;\n                    end.top = !reverse ? offset[1] : 0;\n                }\n                element.css('left');\n            }\n        });\n        createEffect('expand', {\n            directions: [\n                'horizontal',\n                'vertical'\n            ],\n            restore: [OVERFLOW],\n            prepare: function (start, end) {\n                var that = this, element = that.element, options = that.options, reverse = that._reverse, property = that._direction === 'vertical' ? HEIGHT : WIDTH, setLength = element[0].style[property], oldLength = element.data(property), length = parseFloat(oldLength || setLength), realLength = round(element.css(property, AUTO)[property]());\n                start.overflow = HIDDEN;\n                length = options && options.reset ? realLength || length : length || realLength;\n                end[property] = (reverse ? 0 : length) + PX;\n                start[property] = (reverse ? length : 0) + PX;\n                if (oldLength === undefined) {\n                    element.data(property, setLength);\n                }\n            },\n            shouldHide: function () {\n                return this._reverse;\n            },\n            teardown: function () {\n                var that = this, element = that.element, property = that._direction === 'vertical' ? HEIGHT : WIDTH, length = element.data(property);\n                if (length == AUTO || length === BLANK) {\n                    setTimeout(function () {\n                        element.css(property, AUTO).css(property);\n                    }, 0);\n                }\n            }\n        });\n        var TRANSFER_START_STATE = {\n            position: 'absolute',\n            marginLeft: 0,\n            marginTop: 0,\n            scale: 1\n        };\n        createEffect('transfer', {\n            init: function (element, target) {\n                this.element = element;\n                this.options = { target: target };\n                this.restore = [];\n            },\n            setup: function () {\n                this.element.appendTo(document.body);\n            },\n            prepare: function (start, end) {\n                var that = this, element = that.element, outerBox = fx.box(element), innerBox = fx.box(that.options.target), currentScale = animationProperty(element, 'scale'), scale = fx.fillScale(innerBox, outerBox), transformOrigin = fx.transformOrigin(innerBox, outerBox);\n                extend(start, TRANSFER_START_STATE);\n                end.scale = 1;\n                element.css(TRANSFORM, 'scale(1)').css(TRANSFORM);\n                element.css(TRANSFORM, 'scale(' + currentScale + ')');\n                start.top = outerBox.top;\n                start.left = outerBox.left;\n                start.transformOrigin = transformOrigin.x + PX + ' ' + transformOrigin.y + PX;\n                if (that._reverse) {\n                    start.scale = scale;\n                } else {\n                    end.scale = scale;\n                }\n            }\n        });\n        var CLIPS = {\n            top: 'rect(auto auto $size auto)',\n            bottom: 'rect($size auto auto auto)',\n            left: 'rect(auto $size auto auto)',\n            right: 'rect(auto auto auto $size)'\n        };\n        var ROTATIONS = {\n            top: {\n                start: 'rotatex(0deg)',\n                end: 'rotatex(180deg)'\n            },\n            bottom: {\n                start: 'rotatex(-180deg)',\n                end: 'rotatex(0deg)'\n            },\n            left: {\n                start: 'rotatey(0deg)',\n                end: 'rotatey(-180deg)'\n            },\n            right: {\n                start: 'rotatey(180deg)',\n                end: 'rotatey(0deg)'\n            }\n        };\n        function clipInHalf(container, direction) {\n            var vertical = kendo.directions[direction].vertical, size = container[vertical ? HEIGHT : WIDTH]() / 2 + 'px';\n            return CLIPS[direction].replace('$size', size);\n        }\n        createEffect('turningPage', {\n            directions: FOUR_DIRECTIONS,\n            init: function (element, direction, container) {\n                Effect.prototype.init.call(this, element, direction);\n                this._container = container;\n            },\n            prepare: function (start, end) {\n                var that = this, reverse = that._reverse, direction = reverse ? directions[that._direction].reverse : that._direction, rotation = ROTATIONS[direction];\n                start.zIndex = 1;\n                if (that._clipInHalf) {\n                    start.clip = clipInHalf(that._container, kendo.directions[direction].reverse);\n                }\n                start[BACKFACE] = HIDDEN;\n                end[TRANSFORM] = TRANSFORM_PERSPECTIVE + (reverse ? rotation.start : rotation.end);\n                start[TRANSFORM] = TRANSFORM_PERSPECTIVE + (reverse ? rotation.end : rotation.start);\n            },\n            setup: function () {\n                this._container.append(this.element);\n            },\n            face: function (value) {\n                this._face = value;\n                return this;\n            },\n            shouldHide: function () {\n                var that = this, reverse = that._reverse, face = that._face;\n                return reverse && !face || !reverse && face;\n            },\n            clipInHalf: function (value) {\n                this._clipInHalf = value;\n                return this;\n            },\n            temporary: function () {\n                this.element.addClass('temp-page');\n                return this;\n            }\n        });\n        createEffect('staticPage', {\n            directions: FOUR_DIRECTIONS,\n            init: function (element, direction, container) {\n                Effect.prototype.init.call(this, element, direction);\n                this._container = container;\n            },\n            restore: ['clip'],\n            prepare: function (start, end) {\n                var that = this, direction = that._reverse ? directions[that._direction].reverse : that._direction;\n                start.clip = clipInHalf(that._container, direction);\n                start.opacity = 0.999;\n                end.opacity = 1;\n            },\n            shouldHide: function () {\n                var that = this, reverse = that._reverse, face = that._face;\n                return reverse && !face || !reverse && face;\n            },\n            face: function (value) {\n                this._face = value;\n                return this;\n            }\n        });\n        createEffect('pageturn', {\n            directions: [\n                'horizontal',\n                'vertical'\n            ],\n            init: function (element, direction, face, back) {\n                Effect.prototype.init.call(this, element, direction);\n                this.options = {};\n                this.options.face = face;\n                this.options.back = back;\n            },\n            children: function () {\n                var that = this, options = that.options, direction = that._direction === 'horizontal' ? 'left' : 'top', reverseDirection = kendo.directions[direction].reverse, reverse = that._reverse, temp, faceClone = options.face.clone(true).removeAttr('id'), backClone = options.back.clone(true).removeAttr('id'), element = that.element;\n                if (reverse) {\n                    temp = direction;\n                    direction = reverseDirection;\n                    reverseDirection = temp;\n                }\n                return [\n                    kendo.fx(options.face).staticPage(direction, element).face(true).setReverse(reverse),\n                    kendo.fx(options.back).staticPage(reverseDirection, element).setReverse(reverse),\n                    kendo.fx(faceClone).turningPage(direction, element).face(true).clipInHalf(true).temporary().setReverse(reverse),\n                    kendo.fx(backClone).turningPage(reverseDirection, element).clipInHalf(true).temporary().setReverse(reverse)\n                ];\n            },\n            prepare: function (start, end) {\n                start[PERSPECTIVE] = DEFAULT_PERSPECTIVE;\n                start.transformStyle = 'preserve-3d';\n                start.opacity = 0.999;\n                end.opacity = 1;\n            },\n            teardown: function () {\n                this.element.find('.temp-page').remove();\n            }\n        });\n        createEffect('flip', {\n            directions: [\n                'horizontal',\n                'vertical'\n            ],\n            init: function (element, direction, face, back) {\n                Effect.prototype.init.call(this, element, direction);\n                this.options = {};\n                this.options.face = face;\n                this.options.back = back;\n            },\n            children: function () {\n                var that = this, options = that.options, direction = that._direction === 'horizontal' ? 'left' : 'top', reverseDirection = kendo.directions[direction].reverse, reverse = that._reverse, temp, element = that.element;\n                if (reverse) {\n                    temp = direction;\n                    direction = reverseDirection;\n                    reverseDirection = temp;\n                }\n                return [\n                    kendo.fx(options.face).turningPage(direction, element).face(true).setReverse(reverse),\n                    kendo.fx(options.back).turningPage(reverseDirection, element).setReverse(reverse)\n                ];\n            },\n            prepare: function (start) {\n                start[PERSPECTIVE] = DEFAULT_PERSPECTIVE;\n                start.transformStyle = 'preserve-3d';\n            }\n        });\n        var RESTORE_OVERFLOW = !support.mobileOS.android;\n        var IGNORE_TRANSITION_EVENT_SELECTOR = '.km-touch-scrollbar, .km-actionsheet-wrapper';\n        createEffect('replace', {\n            _before: $.noop,\n            _after: $.noop,\n            init: function (element, previous, transitionClass) {\n                Effect.prototype.init.call(this, element);\n                this._previous = $(previous);\n                this._transitionClass = transitionClass;\n            },\n            duration: function () {\n                throw new Error('The replace effect does not support duration setting; the effect duration may be customized through the transition class rule');\n            },\n            beforeTransition: function (callback) {\n                this._before = callback;\n                return this;\n            },\n            afterTransition: function (callback) {\n                this._after = callback;\n                return this;\n            },\n            _both: function () {\n                return $().add(this._element).add(this._previous);\n            },\n            _containerClass: function () {\n                var direction = this._direction, containerClass = 'k-fx k-fx-start k-fx-' + this._transitionClass;\n                if (direction) {\n                    containerClass += ' k-fx-' + direction;\n                }\n                if (this._reverse) {\n                    containerClass += ' k-fx-reverse';\n                }\n                return containerClass;\n            },\n            complete: function (e) {\n                if (!this.deferred || e && $(e.target).is(IGNORE_TRANSITION_EVENT_SELECTOR)) {\n                    return;\n                }\n                var container = this.container;\n                container.removeClass('k-fx-end').removeClass(this._containerClass()).off(transitions.event, this.completeProxy);\n                this._previous.hide().removeClass('k-fx-current');\n                this.element.removeClass('k-fx-next');\n                if (RESTORE_OVERFLOW) {\n                    container.css(OVERFLOW, '');\n                }\n                if (!this.isAbsolute) {\n                    this._both().css(POSITION, '');\n                }\n                this.deferred.resolve();\n                delete this.deferred;\n            },\n            run: function () {\n                if (this._additionalEffects && this._additionalEffects[0]) {\n                    return this.compositeRun();\n                }\n                var that = this, element = that.element, previous = that._previous, container = element.parents().filter(previous.parents()).first(), both = that._both(), deferred = $.Deferred(), originalPosition = element.css(POSITION), originalOverflow;\n                if (!container.length) {\n                    container = element.parent();\n                }\n                this.container = container;\n                this.deferred = deferred;\n                this.isAbsolute = originalPosition == 'absolute';\n                if (!this.isAbsolute) {\n                    both.css(POSITION, 'absolute');\n                }\n                if (RESTORE_OVERFLOW) {\n                    originalOverflow = container.css(OVERFLOW);\n                    container.css(OVERFLOW, 'hidden');\n                }\n                if (!transitions) {\n                    this.complete();\n                } else {\n                    element.addClass('k-fx-hidden');\n                    container.addClass(this._containerClass());\n                    this.completeProxy = $.proxy(this, 'complete');\n                    container.on(transitions.event, this.completeProxy);\n                    kendo.animationFrame(function () {\n                        element.removeClass('k-fx-hidden').addClass('k-fx-next');\n                        previous.css('display', '').addClass('k-fx-current');\n                        that._before(previous, element);\n                        kendo.animationFrame(function () {\n                            container.removeClass('k-fx-start').addClass('k-fx-end');\n                            that._after(previous, element);\n                        });\n                    });\n                }\n                return deferred.promise();\n            },\n            stop: function () {\n                this.complete();\n            }\n        });\n        var Animation = kendo.Class.extend({\n            init: function () {\n                var that = this;\n                that._tickProxy = proxy(that._tick, that);\n                that._started = false;\n            },\n            tick: $.noop,\n            done: $.noop,\n            onEnd: $.noop,\n            onCancel: $.noop,\n            start: function () {\n                if (!this.enabled()) {\n                    return;\n                }\n                if (!this.done()) {\n                    this._started = true;\n                    kendo.animationFrame(this._tickProxy);\n                } else {\n                    this.onEnd();\n                }\n            },\n            enabled: function () {\n                return true;\n            },\n            cancel: function () {\n                this._started = false;\n                this.onCancel();\n            },\n            _tick: function () {\n                var that = this;\n                if (!that._started) {\n                    return;\n                }\n                that.tick();\n                if (!that.done()) {\n                    kendo.animationFrame(that._tickProxy);\n                } else {\n                    that._started = false;\n                    that.onEnd();\n                }\n            }\n        });\n        var Transition = Animation.extend({\n            init: function (options) {\n                var that = this;\n                extend(that, options);\n                Animation.fn.init.call(that);\n            },\n            done: function () {\n                return this.timePassed() >= this.duration;\n            },\n            timePassed: function () {\n                return Math.min(this.duration, new Date() - this.startDate);\n            },\n            moveTo: function (options) {\n                var that = this, movable = that.movable;\n                that.initial = movable[that.axis];\n                that.delta = options.location - that.initial;\n                that.duration = typeof options.duration == 'number' ? options.duration : 300;\n                that.tick = that._easeProxy(options.ease);\n                that.startDate = new Date();\n                that.start();\n            },\n            _easeProxy: function (ease) {\n                var that = this;\n                return function () {\n                    that.movable.moveAxis(that.axis, ease(that.timePassed(), that.initial, that.delta, that.duration));\n                };\n            }\n        });\n        extend(Transition, {\n            easeOutExpo: function (t, b, c, d) {\n                return t == d ? b + c : c * (-Math.pow(2, -10 * t / d) + 1) + b;\n            },\n            easeOutBack: function (t, b, c, d, s) {\n                s = 1.70158;\n                return c * ((t = t / d - 1) * t * ((s + 1) * t + s) + 1) + b;\n            }\n        });\n        fx.Animation = Animation;\n        fx.Transition = Transition;\n        fx.createEffect = createEffect;\n        fx.box = function (element) {\n            element = $(element);\n            var result = element.offset();\n            result.width = kendo._outerWidth(element);\n            result.height = kendo._outerHeight(element);\n            return result;\n        };\n        fx.transformOrigin = function (inner, outer) {\n            var x = (inner.left - outer.left) * outer.width / (outer.width - inner.width), y = (inner.top - outer.top) * outer.height / (outer.height - inner.height);\n            return {\n                x: isNaN(x) ? 0 : x,\n                y: isNaN(y) ? 0 : y\n            };\n        };\n        fx.fillScale = function (inner, outer) {\n            return Math.min(inner.width / outer.width, inner.height / outer.height);\n        };\n        fx.fitScale = function (inner, outer) {\n            return Math.max(inner.width / outer.width, inner.height / outer.height);\n        };\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}