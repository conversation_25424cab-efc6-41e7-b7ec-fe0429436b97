/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.splitter.min",["kendo.resizable.min"],e)}(function(){return function(e,t){function n(e){return m.test(e)}function i(e){return k.test(e)||/^\d+$/.test(e)}function r(e){return!n(e)&&!i(e)}function s(e,t){var i=parseInt(e,10);return n(e)&&(i=Math.floor(i*t/100)),i}function a(e,n){return function(i,r){var s,a=this.element.find(i).data(T);return 1==arguments.length?a[e]:(a[e]=r,n&&(s=this.element.data("kendo"+this.options.name),s.resize(!0)),t)}}function o(e){var t=this,n=e.orientation;t.owner=e,t._element=e.element,t.orientation=n,u(t,n===P?p:l),t._resizable=new d.ui.Resizable(e.element,{orientation:n,handle:".k-splitbar-draggable-"+n+"[data-marker="+e._marker+"]",hint:f(t._createHint,t),start:f(t._start,t),max:f(t._max,t),min:f(t._min,t),invalidClass:"k-restricted-size-"+n,resizeend:f(t._stop,t)})}var l,p,d=window.kendo,c=d.ui,h=d.keys,u=e.extend,f=e.proxy,g=c.Widget,k=/^\d+(\.\d+)?px$/i,m=/^\d+(\.\d+)?%$/i,v=".kendoSplitter",_="expand",z="collapse",x="contentLoad",y="error",w="resize",b="layoutChange",P="horizontal",C="vertical",S="mouseenter",A="click",T="pane",D="mouseleave",E="k-state-focused",B="k-"+T,H="."+B,I=g.extend({init:function(e,t){var n,i=this;g.fn.init.call(i,e,t),i.wrapper=i.element,i.options.orientation&&(n=i.options.orientation.toLowerCase()!=C),i.orientation=n?P:C,i._dimension=n?"width":"height",i._keys={decrease:n?h.LEFT:h.UP,increase:n?h.RIGHT:h.DOWN},i._resizeStep=10,i._marker=d.guid().substring(0,8),i._initPanes(),i.resizing=new o(i),i.element.triggerHandler("init"+v)},events:[_,z,x,y,w,b],_addOverlays:function(){this._panes().append("<div class='k-splitter-overlay k-overlay' />")},_removeOverlays:function(){this._panes().children(".k-splitter-overlay").remove()},_attachEvents:function(){var t=this,n=t.options.orientation;t.element.children(".k-splitbar-draggable-"+n).on("keydown"+v,f(t._keydown,t)).on("mousedown"+v,function(e){e.currentTarget.focus()}).on("focus"+v,function(t){e(t.currentTarget).addClass(E)}).on("blur"+v,function(n){e(n.currentTarget).removeClass(E),t.resizing&&t.resizing.end()}).on(S+v,function(){e(this).addClass("k-splitbar-"+t.orientation+"-hover")}).on(D+v,function(){e(this).removeClass("k-splitbar-"+t.orientation+"-hover")}).on("mousedown"+v,f(t._addOverlays,t)).end().children(".k-splitbar").on("dblclick"+v,f(t._togglePane,t)).children(".k-collapse-next, .k-collapse-prev").on(A+v,t._arrowClick(z)).end().children(".k-expand-next, .k-expand-prev").on(A+v,t._arrowClick(_)).end().end(),e(window).on("resize"+v+t._marker,f(t.resize,t,!1)),e(document).on("mouseup"+v+t._marker,f(t._removeOverlays,t))},_detachEvents:function(){var t=this;t.element.children(".k-splitbar-draggable-"+t.orientation).off(v).end().children(".k-splitbar").off("dblclick"+v).children(".k-collapse-next, .k-collapse-prev, .k-expand-next, .k-expand-prev").off(v),e(window).off(v+t._marker),e(document).off(v+t._marker)},options:{name:"Splitter",orientation:P,panes:[]},destroy:function(){g.fn.destroy.call(this),this._detachEvents(),this.resizing&&this.resizing.destroy(),d.destroy(this.element),this.wrapper=this.element=null},_keydown:function(t){var n,i=this,r=t.keyCode,s=i.resizing,a=e(t.currentTarget),o=i._keys,l=r===o.increase,p=r===o.decrease;l||p?(t.ctrlKey?(n=a[p?"next":"prev"](),s&&s.isResizing()&&s.end(),n[i._dimension]()?i._triggerAction(z,a[p?"prev":"next"]()):i._triggerAction(_,n)):s&&s.move((p?-1:1)*i._resizeStep,a),t.preventDefault()):r===h.HOME?(s.move(-s._maxPosition,a),t.preventDefault()):r===h.END?(s.move(s._maxPosition,a),t.preventDefault()):r===h.ENTER&&s&&(s.end(),t.preventDefault())},_initPanes:function(){var e=this.options.panes||[],t=this;this.element.addClass("k-widget").addClass("k-splitter").children().each(function(n,i){"script"!=i.nodeName.toLowerCase()&&t._initPane(i,e[n])}),this.resize()},_initPane:function(t,n){t=e(t).attr("role","group").addClass(B),t.data(T,n?n:{}).toggleClass("k-scrollable",!n||n.scrollable!==!1),this.ajaxRequest(t)},ajaxRequest:function(e,t,n){var i,r=this;e=r.element.find(e),i=e.data(T),t=t||i.contentUrl,t&&(e.append("<span class='k-icon k-i-loading k-pane-loading' />"),d.isLocalUrl(t)?jQuery.ajax({url:t,data:n||{},type:"GET",dataType:"html",success:function(t){r.angular("cleanup",function(){return{elements:e.get()}}),e.html(t),r.angular("compile",function(){return{elements:e.get()}}),r.trigger(x,{pane:e[0]})},error:function(t,n){r.trigger(y,{pane:e[0],status:n,xhr:t})}}):e.removeClass("k-scrollable").html("<iframe src='"+t+"' frameborder='0' class='k-content-frame'>This page requires frames in order to show content</iframe>"))},_triggerAction:function(e,t){this.trigger(e,{pane:t[0]})||this[e](t[0])},_togglePane:function(t){var n,i=this,r=e(t.target);r.closest(".k-splitter")[0]==i.element[0]&&(n=r.children(".k-icon:not(.k-resize-handle)"),1===n.length&&(n.is(".k-collapse-prev")?i._triggerAction(z,r.prev()):n.is(".k-collapse-next")?i._triggerAction(z,r.next()):n.is(".k-expand-prev")?i._triggerAction(_,r.prev()):n.is(".k-expand-next")&&i._triggerAction(_,r.next())))},_arrowClick:function(t){var n=this;return function(i){var r,s=e(i.target);s.closest(".k-splitter")[0]==n.element[0]&&(r=s.is(".k-"+t+"-prev")?s.parent().prev():s.parent().next(),n._triggerAction(t,r))}},_updateSplitBar:function(e,t,n){var i=function(e,t){return t?"<div class='k-icon "+e+"' />":""},r=this.orientation,s=t.resizable!==!1&&n.resizable!==!1,a=t.collapsible,o=t.collapsed,l=n.collapsible,p=n.collapsed;e.addClass("k-splitbar k-state-default k-splitbar-"+r).attr("role","separator").attr("aria-expanded",!(o||p)).removeClass("k-splitbar-"+r+"-hover").toggleClass("k-splitbar-draggable-"+r,s&&!o&&!p).toggleClass("k-splitbar-static-"+r,!s&&!a&&!l).html(i("k-collapse-prev k-i-arrow-60-up",a&&!o&&!p&&r==C)+i("k-collapse-prev k-i-arrow-60-left",a&&!o&&!p&&r==P)+i("k-expand-prev k-i-arrow-60-down",a&&o&&!p&&r==C)+i("k-expand-prev k-i-arrow-60-right",a&&o&&!p&&r==P)+i("k-resize-handle k-i-hbar",s&&r==C)+i("k-resize-handle k-i-vbar",s&&r==P)+i("k-collapse-next k-i-arrow-60-down",l&&!p&&!o&&r==C)+i("k-collapse-next k-i-arrow-60-right",l&&!p&&!o&&r==P)+i("k-expand-next k-i-arrow-60-up",l&&p&&!o&&r==C)+i("k-expand-next k-i-arrow-60-left",l&&p&&!o&&r==P)),s||a||l||e.removeAttr("tabindex")},_updateSplitBars:function(){var t=this;this.element.children(".k-splitbar").each(function(){var n=e(this),i=n.prevAll(H).first().data(T),r=n.nextAll(H).first().data(T);r&&t._updateSplitBar(n,i,r)})},_removeSplitBars:function(){this.element.children(".k-splitbar").remove()},_panes:function(){return this.element?this.element.children(H):e()},_resize:function(){var n,i,a,o,l,p,c,h,u,f,g=this,k=g.element,m=k.children(H),v=g.orientation==P,_=k.children(".k-splitbar"),z=_.length,x=v?"width":"height",y=k[x]();g.wrapper.addClass("k-splitter-resizing"),0===z?(z=m.length-1,m.slice(0,z).after("<div tabindex='0' class='k-splitbar' data-marker='"+g._marker+"' />"),g._updateSplitBars(),_=k.children(".k-splitbar")):g._updateSplitBars(),_.each(function(){y-=this[v?"offsetWidth":"offsetHeight"]}),n=0,i=0,a=e(),m.css({position:"absolute",top:0})[x](function(){var o,l=e(this),p=l.data(T)||{};if(l.removeClass("k-state-collapsed"),p.collapsed)o=p.collapsedSize?s(p.collapsedSize,y):0,l.css("overflow","hidden").addClass("k-state-collapsed");else{if(r(p.size))return a=a.add(this),t;o=s(p.size,y)}return i++,n+=o,o}),y-=n,o=a.length,l=Math.floor(y/o),a.slice(0,o-1).css(x,l).end().eq(o-1).css(x,y-(o-1)*l),p=0,c=v?"height":"width",h=v?"left":"top",u=v?"offsetWidth":"offsetHeight",0===o&&(f=m.filter(function(){return!(e(this).data(T)||{}).collapsed}).last(),f[x](y+f[0][u])),k.children().css(c,k[c]()).each(function(e,t){"script"!=t.tagName.toLowerCase()&&(t.style[h]=Math.floor(p)+"px",p+=t[u])}),g._detachEvents(),g._attachEvents(),g.wrapper.removeClass("k-splitter-resizing"),d.resize(m),g.trigger(b)},toggle:function(e,n){var i,r=this;e=r.element.find(e),i=e.data(T),(n||i.collapsible)&&(1==arguments.length&&(n=i.collapsed!==t&&i.collapsed),i.collapsed=!n,i.collapsed?e.css("overflow","hidden"):e.css("overflow",""),r.resize(!0))},collapse:function(e){this.toggle(e,!1)},expand:function(e){this.toggle(e,!0)},_addPane:function(e,t,n){var i=this;return n.length&&(i.options.panes.splice(t,0,e),i._initPane(n,e),i._removeSplitBars(),i.resize(!0)),n},append:function(t){t=t||{};var n=this,i=e("<div />").appendTo(n.element);return n._addPane(t,n.options.panes.length,i)},insertBefore:function(t,n){n=e(n),t=t||{};var i=this,r=i.wrapper.children(".k-pane").index(n),s=e("<div />").insertBefore(e(n));return i._addPane(t,r,s)},insertAfter:function(t,n){n=e(n),t=t||{};var i=this,r=i.wrapper.children(".k-pane").index(n),s=e("<div />").insertAfter(e(n));return i._addPane(t,r+1,s)},remove:function(t){var n=this;return t=n.wrapper.find(t),t.length&&(d.destroy(t),t.each(function(t,i){n.options.panes.splice(n.wrapper.children(".k-pane").index(i),1),e(i).remove()}),n._removeSplitBars(),n.options.panes.length&&n.resize(!0)),n},size:a("size",!0),min:a("min"),max:a("max")});c.plugin(I),l={sizingProperty:"height",sizingDomProperty:"offsetHeight",alternateSizingProperty:"width",positioningProperty:"top",mousePositioningProperty:"pageY"},p={sizingProperty:"width",sizingDomProperty:"offsetWidth",alternateSizingProperty:"height",positioningProperty:"left",mousePositioningProperty:"pageX"},o.prototype={press:function(e){this._resizable.press(e)},move:function(e,t){this.pressed||(this.press(t),this.pressed=!0),this._resizable.target||this._resizable.press(t),this._resizable.move(e)},end:function(){this._resizable.end(),this.pressed=!1},destroy:function(){this._resizable.destroy(),this._resizable=this._element=this.owner=null},isResizing:function(){return this._resizable.resizing},_createHint:function(t){var n=this;return e("<div class='k-ghost-splitbar k-ghost-splitbar-"+n.orientation+" k-state-default' />").css(n.alternateSizingProperty,t[n.alternateSizingProperty]())},_start:function(t){var n=this,r=e(t.currentTarget),s=r.prev(),a=r.next(),o=s.data(T),l=a.data(T),p=parseInt(s[0].style[n.positioningProperty],10),d=parseInt(a[0].style[n.positioningProperty],10)+a[0][n.sizingDomProperty]-r[0][n.sizingDomProperty],c=parseInt(n._element.css(n.sizingProperty),10),h=function(e){var t=parseInt(e,10);return(i(e)?t:c*t/100)||0},u=h(o.min),f=h(o.max)||d-p,g=h(l.min),k=h(l.max)||d-p;n.previousPane=s,n.nextPane=a,n._maxPosition=Math.min(d-g,p+f),n._minPosition=Math.max(p+u,d-k)},_max:function(){return this._maxPosition},_min:function(){return this._minPosition},_stop:function(t){var n,i,s,a,o,l,p,c,h=this,u=e(t.currentTarget),f=h.owner;return f._panes().children(".k-splitter-overlay").remove(),t.keyCode!==d.keys.ESC&&(n=t.position,i=u.prev(),s=u.next(),a=i.data(T),o=s.data(T),l=n-parseInt(i[0].style[h.positioningProperty],10),p=parseInt(s[0].style[h.positioningProperty],10)+s[0][h.sizingDomProperty]-n-u[0][h.sizingDomProperty],c=h._element.children(H).filter(function(){return r(e(this).data(T).size)}).length,(!r(a.size)||c>1)&&(r(a.size)&&c--,a.size=l+"px"),(!r(o.size)||c>1)&&(o.size=p+"px"),f.resize(!0)),!1}}}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.splitter.min.js.map
