{"version": 3, "sources": ["kendo.chat.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "Widget", "ui", "extend", "proxy", "DOT", "NS", "keys", "SEND_ICON", "TOGGLE_ICON", "messageBoxStyles", "input", "button", "buttonFlat", "buttonIcon", "buttonSend", "buttonToggle", "iconAdd", "hidden", "ChatMessageBox", "init", "element", "options", "fn", "call", "this", "_wrapper", "_attachEvents", "_typing", "events", "messages", "placeholder", "destroy", "off", "remove", "empty", "styles", "inputId", "guid", "addClass", "html", "attr", "appendTo", "toolbar", "toggleable", "buttons", "append", "on", "_keydown", "_input", "_inputFocusout", "_buttonClick", "_toggleToolbar", "currentValue", "val", "start", "length", "_triggerTyping", "e", "key", "keyCode", "ENTER", "preventDefault", "_sendMessage", "args", "value", "text", "trigger", "ev", "originalEvent", "chat", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "DATA_K_BUTTON_NAME", "SCROLL_LEFT_NAME", "SCROLL_RIGHT_NAME", "VISIBLE", "DEFAULT_ANIMATION", "effects", "duration", "NO_ANIMATION", "expand", "show", "collapse", "hide", "toolbarStyles", "buttonList", "scrollButton", "scrollButtonLeft", "scrollButtonRight", "scrollButtonLeftIcon", "scrollButtonRightIcon", "ChatToolBar", "toolbarOptions", "buttonsDefined", "name", "_createButtonList", "scrollable", "buttonsWidth", "width", "_initScrolling", "_setupAnimation", "toggle", "_onClick", "i", "that", "_createButton", "btnOptions", "buttonElm", "data", "iconClass", "prepend", "target", "closest", "is", "_scrolling", "_scroll", "class", "_refreshScrollButtons", "commandName", "buttonWidth", "maxScrollSize", "scrollAmmount", "currentScroll", "currentScrollLeft", "scrollValue", "Math", "min", "max", "scrollLeft", "isNaN", "parseInt", "animation", "defaultExpandAnimation", "defaultCollapseAnimation", "reverse", "_animationComplete", "round", "scrollWidth", "clientWidth", "children", "last", "outerWidth", "skipAnimation", "complete", "kendoStop", "kendoAnimate", "Component", "Calendar", "viewStyles", "ChatView", "SPACE", "MESSAGE_GROUP_TEMPLATE", "template", "SELF_MESSAGE_GROUP_TEMPLATE", "TEXT_MESSAGE_TEMPLATE", "TYPING_INDICATOR_TEMPLATE", "SUGGESTED_ACTIONS_TEMPLATE", "HERO_CARD_TEMPLATE", "Templates", "Components", "registerTemplate", "templateName", "getTemplate", "registerComponent", "componentName", "component", "getComponent", "Class", "view", "kendoCalendar", "change", "toString", "type", "wrapper", "messageList", "messageListContent", "messageTime", "messageGroup", "message", "only", "first", "middle", "author", "avatar", "noAvatar", "self", "iconButton", "buttonPrimary", "scrollButtonIcon", "scrollButtonIconLeft", "scrollButtonIconRight", "typingIndicator", "typingIndicatorBubble", "bubble", "suggestedActions", "suggestedAction", "cardWrapper", "cardDeckScrollWrap", "cardDeck", "cardList", "card", "cardRich", "cardBody", "cardImage", "cardTitle", "cardSubtitle", "cardActions", "cardActionsVertical", "cardAction", "selected", "_list", "_lastSender", "typingParticipants", "_scrollable", "isTyping", "areTyping", "and", "_scrollDraggable", "list", "_listClick", "_messageClick", "_suggestedActionClick", "_cardActionClick", "_scrollButtonClick", "currentTarget", "scrollToLeft", "find", "scrollContainer", "siblings", "lastCard", "<PERSON><PERSON><PERSON><PERSON>", "renderMessage", "sender", "timestamp", "Date", "bubbleElement", "_renderTemplate", "_renderBubble", "_removeTypingParticipant", "id", "renderSuggestedActions", "_removeSuggestedActions", "_scrollToBottom", "renderAttachments", "cardElement", "_renderAttachmentWrapper", "attachmentLayout", "cardContainer", "attachments", "contentType", "content", "_removeTypingIndicator", "renderComponent", "componentType", "layout", "buttonLeft", "buttonRight", "_renderScrollButton", "directionClass", "targetElement", "hasClass", "parents", "_clearSelection", "messageType", "group", "_getMessageGroup", "_appendToGroup", "templateOptions", "_getMessageGroupTemplate", "appendTarget", "url", "iconUrl", "isOwnMessage", "user", "messageElement", "childrenCount", "indicator", "filter", "removeClass", "_renderTypingIndicator", "indicatorList", "participants", "_addTypingParticipant", "_composeTypingParticipantsText", "found", "push", "splice", "typingAction", "typingText", "map", "join", "replace", "trimRight", "selectedClass", "scrollTop", "prop", "chatStyles", "canvas", "viewWrapper", "messageBoxWrapper", "toolbarBoxWrapper", "Cha<PERSON>", "_events", "_user", "_view", "_messageBox", "_toolbar", "notify", "setOptions", "_setEvents", "messageBox", "unbind", "_resetToolbarButtons", "getUser", "height", "uiElements", "css", "bind", "postMessage", "toggleToolbar", "postArgs", "from", "renderUserTypingIndicator", "clearUserTypingIndicator", "removeTypingIndicator", "plugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,mBAAoB,cAAeD,IAC5C,WA6GE,MA5GC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MACfE,EAASF,EAAMG,GAAGD,OAClBE,EAASN,EAAEM,OACXC,EAAQP,EAAEO,MACVC,EAAM,IACNC,EAAK,aACLC,EAAOR,EAAMQ,KACbC,EAAY,gXACZC,EAAc,0hBACdC,GACAC,MAAO,UACPC,OAAQ,WACRC,WAAY,SACZC,WAAY,gBACZC,WAAY,gBACZC,aAAc,kBACdC,QAAS,iBACTC,OAAQ,YAERC,EAAiBlB,EAAOE,QACxBiB,KAAM,SAAUC,EAASC,GACrBrB,EAAOsB,GAAGH,KAAKI,KAAKC,KAAMJ,EAASC,GACnCG,KAAKC,WACLD,KAAKE,gBACLF,KAAKG,SAAU,GAEnBC,UACAP,SAAWQ,UAAYC,YAAa,sBACpCC,QAAS,WACL/B,EAAOsB,GAAGS,QAAQR,KAAKC,MACnBA,KAAKd,QACLc,KAAKd,MAAMsB,IAAI3B,GACfmB,KAAKd,MAAMuB,SACXT,KAAKd,MAAQ,MAEjBc,KAAKJ,QAAQY,IAAI3B,GACjBmB,KAAKJ,QAAQc,SAEjBT,SAAU,WAAA,GACFU,GAASjB,EAAeiB,OACxBd,EAAUG,KAAKH,QACfQ,EAAWR,EAAQQ,SACnBO,EAAU,WAAatC,EAAMuC,MACjCzC,GAAE,WAAW0C,SAASH,EAAOlB,QAAQsB,KAAKV,EAASC,aAAaU,KAAK,MAAOJ,GAASK,SAASjB,KAAKJ,SACnGI,KAAKd,MAAQd,EAAE,uBAAyB0C,SAASH,EAAOzB,OAAO8B,KAAK,KAAMJ,GAASI,KAAK,cAAeX,EAASC,aAAaW,SAASjB,KAAKJ,SACvIC,EAAQqB,SAAWrB,EAAQqB,QAAQC,YAActB,EAAQqB,QAAQE,SACjEhD,EAAE,YAAY0C,SAASH,EAAOxB,QAAQ2B,SAASH,EAAOvB,YAAY0B,SAASH,EAAOtB,YAAYyB,SAASH,EAAOpB,cAAcyB,KAAK,OAAQ,UAAUK,OAAOjD,EAAEY,IAAciC,SAASjB,KAAKJ,SAE5LxB,EAAE,YAAY0C,SAASH,EAAOxB,QAAQ2B,SAASH,EAAOvB,YAAY0B,SAASH,EAAOtB,YAAYyB,SAASH,EAAOrB,YAAY+B,OAAOjD,EAAEW,IAAYkC,SAASjB,KAAKJ,UAEjKM,cAAe,WACX,GAAIS,GAASjB,EAAeiB,MAC5BX,MAAKd,MAAMoC,GAAG,UAAYzC,EAAIF,EAAMqB,KAAKuB,SAAUvB,OAAOsB,GAAG,QAAUzC,EAAIF,EAAMqB,KAAKwB,OAAQxB,OAAOsB,GAAG,WAAazC,EAAIF,EAAMqB,KAAKyB,eAAgBzB,OACpJA,KAAKJ,QAAQ0B,GAAG,QAAUzC,EAAID,EAAM+B,EAAOrB,WAAYX,EAAMqB,KAAK0B,aAAc1B,OAChFA,KAAKJ,QAAQ0B,GAAG,QAAUzC,EAAID,EAAM+B,EAAOpB,aAAcZ,EAAMqB,KAAK2B,eAAgB3B,QAExFwB,OAAQ,WAAA,GACAI,GAAe5B,KAAKd,MAAM2C,MAC1BC,EAAQF,EAAaG,OAAS,CAClC/B,MAAKgC,eAAeF,IAExBP,SAAU,SAAUU,GAChB,GAAIC,GAAMD,EAAEE,OACZ,QAAQD,GACR,IAAKpD,GAAKsD,MACNH,EAAEI,iBACFrC,KAAKsC,iBAIbZ,aAAc,SAAUO,GACpBA,EAAEI,iBACFrC,KAAKsC,gBAETA,aAAc,WAAA,GAMNC,GALAC,EAAQxC,KAAKd,MAAM2C,KAClBW,GAAMT,SAGX/B,KAAKgC,gBAAe,GAChBO,GAASE,KAAMD,GACnBxC,KAAK0C,QAAQ,cAAeH,GAC5BvC,KAAKd,MAAM2C,IAAI,MAEnBJ,eAAgB,WACZzB,KAAKgC,gBAAe,IAExBA,eAAgB,SAAUF,GAClBA,EACK9B,KAAKG,UACNH,KAAK0C,QAAQ,kBACb1C,KAAKG,SAAU,GAGfH,KAAKG,UACLH,KAAK0C,QAAQ,gBACb1C,KAAKG,SAAU,IAI3BwB,eAAgB,SAAUgB,GACtB3C,KAAK0C,QAAQ,iBAAmBE,cAAeD,MAGvDjE,IAAO,EAAMgB,GAAkBiB,OAAQ1B,IACvCP,EAAOJ,GAASuE,MAAQnD,eAAgBA,MAC1CnB,OAAOD,MAAMwE,QACRvE,OAAOD,OACE,kBAAVH,SAAwBA,OAAO4E,IAAM5E,OAAS,SAAU6E,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAU/E,EAAGC,QACVA,OAAO,gBAAiB,cAAeD,IACzC,WA+LE,MA9LC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MACfE,EAASF,EAAMG,GAAGD,OAClBE,EAASN,EAAEM,OACXC,EAAQP,EAAEO,MACVC,EAAM,IACNC,EAAK,aACLsE,EAAqB,cACrBC,EAAmB,wBACnBC,EAAoB,yBACpBC,EAAU,WACVC,GACAC,QAAS,kBACTC,SAAU,KAEVC,GACAC,QAAUC,MAAM,GAChBC,UAAYC,MAAM,IAElBC,GACA5E,OAAQ,WACRC,WAAY,SACZ4E,WAAY,gBACZC,aAAc,kBACdC,iBAAkB,uBAClBC,kBAAmB,wBACnBC,qBAAsB,gCACtBC,sBAAuB,iCACvBhF,WAAY,iBAEZiF,EAAc9F,EAAOE,QACrBiB,KAAM,SAAUC,EAASC,GAAnB,GAEE0E,GACAC,CAFJ3E,GAAUnB,KAAWmB,GAAW4E,KAAM,gBAClCF,EAAiB1E,EAAQqB,QACzBsD,EAAiBD,EAAenD,SAAWmD,EAAenD,QAAQW,OACtEvD,EAAOsB,GAAGH,KAAKI,KAAKC,KAAMJ,EAASC,GAC/B2E,GACAxE,KAAK0E,oBAELF,GAAkBD,EAAeI,YAAc3E,KAAK4E,eAAiB5E,KAAKJ,QAAQiF,SAClF7E,KAAK8E,iBAET9E,KAAK+E,kBACDR,EAAepD,YACfnB,KAAKgF,QAAO,GAEhBhF,KAAKJ,QAAQ0B,GAAG,QAAUzC,EAAIF,EAAMqB,KAAKiF,SAAUjF,QAEvDI,QAAS,SACTG,QAAS,WACL/B,EAAOsB,GAAGS,QAAQR,KAAKC,MACvBA,KAAKJ,QAAQY,IAAI3B,GACjBmB,KAAKJ,QAAQc,SAEjBgE,kBAAmB,WAAA,GAKNQ,GACD/F,EALJgG,EAAOnF,KACPW,EAAS2D,EAAY3D,OACrBS,EAAU+D,EAAKtF,QAAQqB,QAAQE,QAC/B4C,EAAa5F,EAAE,eAAkBuC,EAAOqD,WAAa,WACzD,KAASkB,EAAI,EAAGA,EAAI9D,EAAQW,OAAQmD,IAC5B/F,EAASgG,EAAKC,cAAchE,EAAQ8D,IACxClB,EAAW3C,OAAOlC,EAEtB6E,GAAW/C,SAASjB,KAAKJ,SACzBI,KAAKgE,WAAaA,GAEtBoB,cAAe,SAAUC,GAAV,GACP1E,GAAS2D,EAAY3D,OACrB2E,EAAYlH,EAAE,WASlB,OAR0B,gBAAfiH,KACPA,GAAeZ,KAAMY,IAEzBC,EAAUtE,KAAKqE,EAAWrE,UAAYA,KAAK,QAASqE,EAAW5C,MAAMzB,KAAK,OAAQ,UAAUF,SAASuE,EAAWZ,MAAMc,KAAKpC,EAAoBkC,EAAWZ,MAAM3D,SAASH,EAAOxB,QAC5KkG,EAAWG,YACXF,EAAUxE,SAASH,EAAOtB,YAC1BiG,EAAUG,QAAQ,gBAAmBJ,EAAWG,UAAY,cAEzDF,GAEXL,SAAU,SAAUtC,GAAV,GACFhC,GAAS2D,EAAY3D,OACrB+E,EAAStH,EAAEuE,EAAG+C,QAAQC,QAAQ/G,EAAM+B,EAAOxB,OAC3CuG,GAAOE,GAAGhH,EAAM+B,EAAOsD,gBAAkBjE,KAAK6F,YAC9C7F,KAAK8F,QAAQJ,EAAOH,KAAKpC,IAEzBuC,EAAOH,KAAKpC,IACZnD,KAAK0C,QAAQ,SACTvD,OAAQuG,EAAO,GACfjB,KAAMiB,EAAOH,KAAKpC,GAClBP,cAAeD,KAI3BmC,eAAgB,WACZ,GAAInE,GAAS2D,EAAY3D,MACzBX,MAAKkE,iBAAmBlE,KAAKoF,eACzBX,KAAMrB,EACNoC,UAAW7E,EAAOyD,qBAClBpD,MAAQ+E,QAASpF,EAAOsD,aAAe,IAAMtD,EAAOuD,oBAExDlE,KAAKmE,kBAAoBnE,KAAKoF,eAC1BX,KAAMpB,EACNmC,UAAW7E,EAAO0D,sBAClBrD,MAAQ+E,QAASpF,EAAOsD,aAAe,IAAMtD,EAAOwD,qBAExDnE,KAAKJ,QAAQ6F,QAAQzF,KAAKkE,kBAC1BlE,KAAKJ,QAAQyB,OAAOrB,KAAKmE,mBACzBnE,KAAKgG,wBACLhG,KAAKJ,QAAQ0B,GAAG,UAAYzC,EAAIF,EAAMqB,KAAKgG,sBAAuBhG,QAEtE8F,QAAS,SAAUG,GAAV,GACDd,GAAOnF,KACPkG,EAAcf,EAAKe,cACnBC,EAAgBnG,KAAKmG,gBACrBC,EAAgBH,IAAgB7C,EAAmB8C,KAAmBA,EACtEG,EAAgBrG,KAAKsG,oBACrBC,EAAcF,EAAgBD,CAClCG,GAAcC,KAAKC,IAAID,KAAKE,IAAIH,EAAa,GAAIJ,GAC7CF,IAAgB7C,GAAoB6C,IAAgB5C,IAGxD8B,EAAKnB,WAAW2C,WAAWJ,GAC3BpB,EAAKa,sBAAsBO,KAE/BP,sBAAuB,SAAUxD,GAAV,GACf2D,GAAgBnG,KAAKmG,gBACrBG,EAAoB9D,IAAUnE,GAAauI,MAAMC,SAASrE,EAAO,KAAOxC,KAAKsG,oBAAsB9D,GAClGxC,KAAKkE,kBAAqBlE,KAAKmE,qBAGpCnE,KAAKkE,iBAAiBc,OAA6B,IAAtBsB,GAC7BtG,KAAKmE,kBAAkBa,OAAOsB,IAAsBH,KAExDpB,gBAAiB,WAAA,GACT+B,GAAY9G,KAAKH,QAAQqB,QAAQ4F,UACjCC,EAAyBrI,KAAW6E,GACpCyD,EAA2BtI,GAC3BuI,SAAS,EACTnD,MAAM,GACPP,EAECuD,GADAA,KAAc,EACFpI,GAAO,KAAUgF,GAEjBhF,GAAO,GACfiF,OAAQoD,EACRlD,SAAUmD,GACXF,GAEP9G,KAAKH,QAAQqB,QAAQ4F,UAAYA,GAErCI,mBAAoB,WAChBlH,KAAKgG,yBAETM,kBAAmB,WACf,MAAOE,MAAKW,MAAMnH,KAAKgE,WAAW2C,eAEtCR,cAAe,WACX,MAAOK,MAAKW,MAAMnH,KAAKgE,WAAW,GAAGoD,YAAcpH,KAAKgE,WAAW,GAAGqD,cAE1EjG,QAAS,WACL,GAAIT,GAAS2D,EAAY3D,MACzB,OAAOX,MAAKgE,WAAahE,KAAKgE,WAAWsD,SAAS1I,EAAM+B,EAAOxB,QAAU,MAE7E+G,YAAa,WACT,MAAOM,MAAKW,MAAMnH,KAAKoB,UAAUmG,OAAOC,YAAW,KAEvD5C,aAAc,WACV,GAAIC,GAAQ,CAIZ,OAHI7E,MAAKoB,YACLyD,EAAQ7E,KAAKkG,cAAgBlG,KAAKoB,UAAUW,QAEzC8C,GAEXG,OAAQ,SAAUyC,GACd,GAAIX,GAAY9G,KAAKH,QAAQqB,QAAQ4F,SACjCW,KACAX,EAAYpI,GAAO,KAAUgF,IAEjCoD,EAAUnD,OAAO+D,SAAW/I,EAAMqB,KAAKkH,mBAAoBlH,MAC3D8G,EAAUjD,SAAS6D,SAAW/I,EAAMqB,KAAKkH,mBAAoBlH,MACzDA,KAAKJ,QAAQgG,GAAGtC,GAChBtD,KAAKJ,QAAQ+H,YAAYC,aAAad,EAAUjD,UAEhD7D,KAAKJ,QAAQ+H,YAAYC,aAAad,EAAUnD,UAI5DjF,IAAO,EAAM4F,GAAe3D,OAAQoD,IACpCrF,EAAOJ,EAAMuE,MAAQyB,YAAaA,KACpC/F,OAAOD,MAAMwE,QACRvE,OAAOD,OACE,kBAAVH,SAAwBA,OAAO4E,IAAM5E,OAAS,SAAU6E,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAU/E,EAAGC,QACVA,OAAO,aACH,aACA,qBACDD,IACL,WA4XE,MA3XC,UAAUE,EAAGC,GAAb,GAoCOwJ,GAUAC,EAgBAC,EAyCAC,EAtGA1J,EAAQC,OAAOD,MACfE,EAASF,EAAMG,GAAGD,OAClBE,EAASN,EAAEM,OACXC,EAAQP,EAAEO,MACVC,EAAM,IACNqJ,EAAQ,IACRpJ,EAAK,aACLqJ,EAAyB5J,EAAM6J,SAAS,yMACxCC,EAA8B9J,EAAM6J,SAAS,4KAC7CE,EAAwB/J,EAAM6J,SAAS,0LACvCG,EAA4BhK,EAAM6J,SAAS,wRAC3CI,EAA6BjK,EAAM6J,SAAS,mOAC5CK,EAAqBlK,EAAM6J,SAAS,+zBACxCzJ,GAAOJ,EAAMuE,MACT4F,aACAC,gBAEJpK,EAAMuE,KAAK8F,iBAAmB,SAAUC,EAAcT,GAClD7J,EAAMuE,KAAK4F,UAAUG,GAAgBtK,EAAM6J,SAASA,IAExD7J,EAAMuE,KAAKgG,YAAc,SAAUD,GAC/B,MAAOtK,GAAMuE,KAAK4F,UAAUG,IAAiBP,GAEjD/J,EAAMuE,KAAK8F,iBAAiB,OAAQN,GACpC/J,EAAMuE,KAAK8F,iBAAiB,UAAWN,GACvC/J,EAAMuE,KAAK8F,iBAAiB,SAAUL,GACtChK,EAAMuE,KAAK8F,iBAAiB,kBAAmBJ,GAC/CjK,EAAMuE,KAAK8F,iBAAiB,WAAYH,GACxClK,EAAMuE,KAAK8F,iBAAiB,sCAAuCH,GACnElK,EAAMuE,KAAKiG,kBAAoB,SAAUC,EAAeC,GACpD1K,EAAMuE,KAAK6F,WAAWK,GAAiBC,GAE3C1K,EAAMuE,KAAKoG,aAAe,SAAUF,GAChC,MAAOzK,GAAMuE,KAAK6F,WAAWK,IAAkB,MAE/ClB,EAAYvJ,EAAMuE,KAAKgF,UAAYvJ,EAAM4K,MAAMxK,QAC/CiB,KAAM,SAAUE,EAASsJ,GACrBnJ,KAAKJ,QAAUxB,EAAE,eACjB4B,KAAKH,QAAUA,EACfG,KAAKmJ,KAAOA,GAEhB5I,QAAS,WACLjC,EAAMiC,QAAQP,KAAKJ,YAGvBkI,EAAWD,EAAUnJ,QACrBiB,KAAM,SAAUE,EAASsJ,GACrBtB,EAAU/H,GAAGH,KAAKI,KAAKC,KAAMH,EAASsJ,GACtCnJ,KAAKJ,QAAQwJ,eACTC,OAAQ,WACJF,EAAKzG,QAAQ,mBACTD,KAAMnE,EAAMgL,SAAStJ,KAAKwC,QAAS,KACnC+G,KAAM,gBAKtBhJ,QAAS,eAGbjC,EAAMuE,KAAKiG,kBAAkB,WAAYhB,GACrCC,GACAyB,QAAS,kBACTC,YAAa,YACbC,mBAAoB,yBACpBC,YAAa,iBACbC,aAAc,kBACdC,QAAS,YACTC,KAAM,SACNC,MAAO,UACPC,OAAQ,WACRzC,KAAM,SACN0C,OAAQ,WACRC,OAAQ,WACRC,SAAU,cACVC,KAAM,QACNjL,OAAQ,WACRkL,WAAY,gBACZC,cAAe,mBACfC,iBAAkB,SAClBC,qBAAsB,yBACtBC,sBAAuB,0BACvBC,gBAAiB,qBACjBC,sBAAuB,4BACvBC,OAAQ,WACRC,iBAAkB,kBAClBC,gBAAiB,gBACjBC,YAAa,mBACbC,mBAAoB,yBACpBC,SAAU,cACVC,SAAU,cACVC,KAAM,SACNC,SAAU,mBACVC,SAAU,cACVC,UAAW,eACXC,UAAW,eACXC,aAAc,kBACdC,YAAa,iBACbC,oBAAqB,0BACrBC,WAAY,gBACZC,SAAU,oBAEV5D,EAAW1J,EAAMuE,KAAKmF,SAAWxJ,EAAOE,QACxCiB,KAAM,SAAUC,EAASC,GACrBrB,EAAOsB,GAAGH,KAAKI,KAAKC,KAAMJ,EAASC,GACnCG,KAAK6L,QACL7L,KAAK8L,YAAc,KACnB9L,KAAK+L,sBACL/L,KAAKE,gBACLF,KAAKgM,eAET5L,UACAP,SACIQ,UACI4L,SAAU,cACVC,UAAW,eACXC,IAAK,UAGb5L,QAAS,WACL/B,EAAOsB,GAAGS,QAAQR,KAAKC,MACnBA,KAAKoM,kBACLpM,KAAKoM,iBAAiB7L,UAE1BP,KAAKJ,QAAQc,QACbV,KAAKJ,QAAQY,IAAI3B,GACjBmB,KAAKqM,KAAO,KACZrM,KAAK8L,YAAc,MAEvBD,MAAO,WACH,GAAI9D,GAAaC,EAASrH,MAC1BX,MAAKJ,QAAQkB,SAASiH,EAAW0B,aAAazI,KAAK,YAAa,UAChEhB,KAAKqM,KAAOjO,EAAE,SAAS0C,SAASiH,EAAW2B,oBAAoBzI,SAASjB,KAAKJ,UAEjFM,cAAe,WACX,GAAIS,GAASqH,EAASrH,MACtBX,MAAKJ,QAAQ0B,GAAG,QAAUzC,EAAIF,EAAMqB,KAAKsM,WAAYtM,OAAOsB,GAAG,QAAUzC,EAAID,EAAM+B,EAAOkJ,QAASlL,EAAMqB,KAAKuM,cAAevM,OAAOsB,GAAG,QAAUzC,EAAID,EAAM+B,EAAOmK,gBAAiBnM,EAAMqB,KAAKwM,sBAAuBxM,OAAOsB,GAAG,QAAUzC,EAAID,EAAM+B,EAAOgL,WAAa1D,EAAQrJ,EAAM+B,EAAOxB,OAAQR,EAAMqB,KAAKyM,iBAAkBzM,QAErUgM,YAAa,WACT,GAAIjE,GAAaC,EAASrH,MAC1BX,MAAKJ,QAAQ0B,GAAG,QAAUzC,EAAID,EAAMmJ,EAAWiD,mBAAqB/C,EAAQrJ,EAAMmJ,EAAW5I,OAAQR,EAAMqB,KAAK0M,mBAAoB1M,QAExI0M,mBAAoB,SAAUzK,GAAV,GACZ8F,GAAaC,EAASrH,OACtBxB,EAASf,EAAE6D,EAAE0K,eACbC,EAA6E,IAA9DzN,EAAO0N,KAAKjO,EAAMmJ,EAAWyC,sBAAsBzI,OAClE+K,EAAkB3N,EAAO4N,SAASnO,EAAMmJ,EAAWkD,UACnD+B,EAAWF,EAAgBD,KAAKjO,EAAMmJ,EAAWoD,MAAM5D,OACvD0F,EAAYD,EAASxF,YAAW,EAEhCsF,GAAgBnG,WADhBiG,EAC2BE,EAAgBnG,aAAesG,EAE/BH,EAAgBnG,aAAesG,IAGlEpE,YAAa,SAAUD,GACnB,MAAOtK,GAAMuE,KAAKgG,YAAYD,IAElCK,aAAc,SAAUM,GACpB,MAAOjL,GAAMuE,KAAKoG,aAAaM,IAEnC2D,cAAe,SAAUrD,EAASsD,GACzBtD,EAAQuD,YACTvD,EAAQuD,UAAY,GAAIC,OAEvBxD,EAAQpH,OACToH,EAAQpH,KAAO,GAEnB,IAAI6K,GAAgBtN,KAAKuN,gBAAgB1D,EAAQN,KAAMM,EACvD7J,MAAKwN,cAAc3D,EAAQN,KAAM+D,EAAeH,GAC5B,UAAhBtD,EAAQN,KACJvJ,KAAK+L,mBAAmBhK,OAAS,GACjC/B,KAAKyN,yBAAyBN,GAGlCnN,KAAK8L,YAAcqB,EAAOO,IAGlCC,uBAAwB,SAAU9C,GAC9B7K,KAAK4N,yBACL,IAAIhO,GAAUI,KAAKuN,gBAAgB,mBAAqB1C,iBAAkBA,GAC1E7K,MAAKqM,KAAKhL,OAAOzB,GACjBI,KAAK6N,mBAETC,kBAAmB,SAAUjO,GAAV,GAONqF,GACD6I,EAPJvE,EAAUxJ,KAAKgO,yBAAyBnO,EAAQoO,kBAChDC,EAA6C,aAA7BrO,EAAQoO,iBAAkCzE,EAAQqD,KAAKjO,EAAMoJ,EAASrH,OAAOsK,UAAYzB,EACzG2E,EAActO,EAAQsO,WAC1B,IAAKA,EAAYpM,OAAjB,CAGA,IAASmD,EAAI,EAAGA,EAAIiJ,EAAYpM,OAAQmD,IAChC6I,EAAc/N,KAAKuN,gBAAgBY,EAAYjJ,GAAGkJ,YAAaD,EAAYjJ,GAAGmJ,SAClFH,EAAc7M,OAAO0M,EAEzB/N,MAAK4N,0BACL5N,KAAKsO,yBACLtO,KAAKqM,KAAKhL,OAAOmI,GACjBxJ,KAAK8L,YAAc,OAEvByC,gBAAiB,SAAUhF,GAAV,GACTiF,GAAgBxO,KAAKiJ,aAAaM,GAClCP,EAAY,GAAIwF,MAAkBxO,KACtCA,MAAKqM,KAAKhL,OAAO2H,EAAUpJ,SAC3BI,KAAK6N,mBAETG,yBAA0B,SAAUS,GAAV,GAKdC,GAGAC,EAPJ5G,EAAaC,EAASrH,OACtB6I,EAAUpL,EAAE,QAWhB,OAVe,aAAXqQ,GACAjF,EAAQ1I,SAASiH,EAAWiD,oBACxB0D,EAAa1O,KAAK4O,oBAAoB7G,EAAWyC,sBACrDhB,EAAQnI,OAAOqN,GACflF,EAAQnI,OAAOjD,EAAE,SAAS0C,SAASiH,EAAWkD,WAC1C0D,EAAc3O,KAAK4O,oBAAoB7G,EAAW0C,uBACtDjB,EAAQnI,OAAOsN,IAEfnF,EAAQ1I,SAASiH,EAAWmD,UAEzB1B,GAEXoF,oBAAqB,SAAUC,GAC3B,GAAI9G,GAAaC,EAASrH,MAC1B,OAAOvC,GAAE,YAAY0C,SAASiH,EAAW5I,QAAQ2B,SAASiH,EAAWsC,YAAYhJ,OAAOjD,EAAE,UAAU0C,SAASiH,EAAWwC,kBAAkBzJ,SAAS+N,KAEvJjB,wBAAyB,WACrB5N,KAAKqM,KAAKQ,KAAKjO,EAAMoJ,EAASrH,OAAOkK,kBAAkBpK,UAE3D6L,WAAY,SAAUrK,GAAV,GACJtB,GAASqH,EAASrH,OAClBmO,EAAgB1Q,EAAE6D,EAAEyD,OACpBoJ,GAAcC,SAASpO,EAAOkJ,UAAYiF,EAAcE,QAAQpQ,EAAM+B,EAAOkJ,SAAS9H,QAG1F/B,KAAKiP,mBAET1C,cAAe,SAAUtK,GACrBjC,KAAKiP,kBACL7Q,EAAE6D,EAAE0K,eAAe7L,SAASkH,EAASrH,OAAOiL,WAEhDY,sBAAuB,SAAUvK,GAC7B,GAAIQ,GAAOrE,EAAE6D,EAAEyD,QAAQH,KAAK,UAAY,EACxCvF,MAAK0C,QAAQ,eAAiBD,KAAMA,IACpCzC,KAAK4N,2BAETnB,iBAAkB,SAAUxK,GACxB,GAAIQ,GAAOrE,EAAE6D,EAAEyD,QAAQH,KAAK,UAAY,EACxCvF,MAAK0C,QAAQ,eAAiBD,KAAMA,KAExC+K,cAAe,SAAU0B,EAAa5B,EAAeH,GACjDnN,KAAK4N,0BACL5N,KAAKsO,wBACL,IAAIa,GAAQnP,KAAKoP,iBAAiBjC,EAAQ+B,EAC1ClP,MAAKqP,eAAeF,EAAO7B,EAAe4B,GAC1ClP,KAAK6N,mBAETN,gBAAiB,SAAUhE,EAAM1J,GAAhB,GAETD,GAEIoJ,EAGAb,EACAmH,EAPJd,EAAgBxO,KAAKiJ,aAAaM,EAUtC,OARIiF,IACIxF,EAAY,GAAIwF,GAAc3O,EAASG,MAC3CJ,EAAUoJ,EAAUpJ,UAEhBuI,EAAWnI,KAAK6I,YAAYU,GAC5B+F,EAAkB5Q,GAAO,KAAUmB,GAAWc,OAAQqH,EAASrH,SACnEf,EAAUxB,EAAE+J,EAASmH,KAElB1P,GAEXwP,iBAAkB,SAAUjC,EAAQ+B,GAAlB,GAIVC,GAHApH,EAAaC,EAASrH,OACtBwH,EAAWnI,KAAKuP,yBAAyBpC,EAAQ+B,GACjDM,EAA8B,UAAfN,EAA0BlP,KAAKJ,QAAUI,KAAKqM,IAEjE,OAAIc,GAAOO,KAAO1N,KAAK8L,aAAoC,OAArB9L,KAAK8L,aAAwC,WAAhBoD,IAC/DC,EAAQnP,KAAKqM,KAAKQ,KAAKjO,EAAMmJ,EAAW6B,cAAcrC,OAClD4H,EAAMpN,QACCoN,EAGR/Q,EAAE+J,GACL1F,KAAM0K,EAAO1I,KACbgL,IAAKtC,EAAOuC,QACZ/O,OAAQoH,KACR9G,SAASuO,IAEjBD,yBAA0B,SAAUpC,EAAQ+B,GAAlB,GAClBS,GAAexC,EAAOO,KAAO1N,KAAKH,QAAQ+P,KAAKlC,GAC/CvF,EAAWwH,EAAevH,EAA8BF,CAI5D,OAHmB,UAAfgH,IACA/G,EAAWG,GAERH,GAEXkH,eAAgB,SAAUF,EAAOU,EAAgBX,GAAjC,GACRnH,GAAaC,EAASrH,OACtB2G,EAAW6H,EAAMtC,KAAKjO,EAAMmJ,EAAW8B,SACvCiG,EAAgBxI,EAASvF,OACzBgO,EAAY/P,KAAKJ,QAAQiN,KAAKjO,EAAMmJ,EAAW2C,gBAC/CqF,GAAUhO,QAAyB,UAAfmN,IAGxBW,EAAe/O,SAA2B,IAAlBgP,EAAsB/H,EAAW+B,KAAO/B,EAAWR,MAC3ED,EAAS0I,OAAOpR,EAAMmJ,EAAW+B,MAAMmG,YAAYlI,EAAW+B,MAAMhJ,SAASiH,EAAWgC,OACxFzC,EAAS0I,OAAOpR,EAAMmJ,EAAWR,MAAM0I,YAAYlI,EAAWR,MAAMzG,SAASiH,EAAWiC,QACxFmF,EAAM9N,OAAOwO,KAEjBK,uBAAwB,SAAU/C,GAC9B,GAA2EgD,GAAeC,EAAtFL,EAAY/P,KAAKJ,QAAQiN,KAAKjO,EAAMmJ,EAAW4C,sBACnD3K,MAAKqQ,sBAAsBlD,GACvB4C,EAAUhO,QACVqO,EAAepQ,KAAKsQ,+BAA+BtQ,KAAK+L,oBACxDoE,EAAgBJ,EAAUlD,KAAKjO,EAAMmJ,EAAWkC,QAAQF,QACxDoG,EAAc1N,KAAK2N,IAEnBhS,EAAEkK,GACE7F,KAAM0K,EAAO1I,KAAOzE,KAAKH,QAAQQ,SAAS4L,SAC1CtL,OAAQoH,KACR9G,SAASjB,KAAKJ,SAEtBI,KAAK6N,mBAETwC,sBAAuB,SAAUlD,GAAV,GAEVjI,GADLqL,GAAQ,CACZ,KAASrL,EAAI,EAAGA,EAAIlF,KAAK+L,mBAAmBhK,OAAQmD,GAAK,EACrD,GAAIlF,KAAK+L,mBAAmB7G,GAAGwI,IAAMP,EAAOO,GAAI,CAC5C6C,GAAQ,CACR,OAGHA,GACDvQ,KAAK+L,mBAAmByE,KAAKrD,IAGrCM,yBAA0B,SAAUN,GAAV,GACqDgD,GAAeC,EAE7ElL,EAFT6K,EAAY/P,KAAKJ,QAAQiN,KAAKjO,EAAMmJ,EAAW4C,sBACnD,IAAIoF,EAAUhO,OAAQ,CAClB,IAASmD,EAAI,EAAGA,EAAIlF,KAAK+L,mBAAmBhK,OAAQmD,GAAK,EACjDlF,KAAK+L,mBAAmB7G,GAAGwI,IAAMP,EAAOO,IACxC1N,KAAK+L,mBAAmB0E,OAAOvL,EAAG,EAG1CkL,GAAepQ,KAAKsQ,+BAA+BtQ,KAAK+L,oBACnC,KAAjBqE,EACAL,EAAUtP,UAEV0P,EAAgBJ,EAAUlD,KAAKjO,EAAMmJ,EAAWkC,QAAQF,QACxDoG,EAAc1N,KAAK2N,MAI/BE,+BAAgC,SAAUF,GACtC,GAAI/P,GAAWL,KAAKH,QAAQQ,SAAUqQ,EAAsC,GAAvBN,EAAarO,OAAc1B,EAAS4L,SAAW5L,EAAS6L,UAAWyE,EAAa,EACrI,OAA4B,KAAxBP,EAAarO,OACN4O,EAEXA,EAAa3Q,KAAK+L,mBAAmB6E,IAAI,SAAU3G,GAC/C,MAAOA,GAAOxF,OACfoM,KAAK,MAAMC,QAAQ,cAAezQ,EAAS8L,IAAI4E,aAAeL,GAGrEpC,uBAAwB,WACpB,GAAIyB,GAAY/P,KAAKJ,QAAQiN,KAAKjO,EAAMmJ,EAAW4C,sBAC/CoF,GAAUhO,SACV/B,KAAK+L,sBACLgE,EAAUtP,WAGlBwO,gBAAiB,WACb,GAAI+B,GAAgBhJ,EAASrH,OAAOiL,QACpC5L,MAAKJ,QAAQiN,KAAKjO,EAAMoS,GAAef,YAAYe,IAEvDnD,gBAAiB,WACb7N,KAAKJ,QAAQqR,UAAUjR,KAAKJ,QAAQsR,KAAK,oBAGjDxS,GAAO,EAAMsJ,GAAYrH,OAAQoH,KACnCxJ,OAAOD,MAAMwE,QACRvE,OAAOD,OACE,kBAAVH,SAAwBA,OAAO4E,IAAM5E,OAAS,SAAU6E,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAU/E,EAAGC,QACVA,OAAO,cACH,kBACA,eACA,aACDD,IACL,WA+ME,MApMC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MACfE,EAASF,EAAMG,GAAGD,OAClBE,EAASN,EAAEM,OACXE,EAAM,IACNuS,GACA3H,QAAS,kBACT4H,OAAQ,gBACRC,YAAa,iBACbC,kBAAmB,gBACnBC,kBAAmB,iBAEnBC,EAAOhT,EAAOE,QACdiB,KAAM,SAAUC,EAASC,EAASO,GAC9B5B,EAAOsB,GAAGH,KAAKI,KAAKC,KAAMJ,EAASC,GAC/BO,IACAJ,KAAKyR,QAAUrR,GAEnBJ,KAAK0R,QACL1R,KAAKC,WACLD,KAAK2R,QACL3R,KAAK4R,cACD/R,GAAWA,EAAQqB,SAAWrB,EAAQqB,QAAQE,SAC9CpB,KAAK6R,WAETvT,EAAMwT,OAAO9R,OAEjBI,QACI,cACA,YACA,OACA,cACA,cACA,aAEJP,SACI+P,MACInL,KAAM,OACNiL,QAAS,IAEbjL,KAAM,OACNpE,UAAYC,YAAa,qBACzBY,SAAS,GAEb6Q,WAAY,SAAUlS,GAClBG,KAAKgS,WAAWnS,GAChBzB,EAAEM,QAAO,EAAMsB,KAAKH,QAASA,GACzBG,KAAKkB,SAAW,WAAarB,KAC7BG,KAAKkB,QAAQX,UACbP,KAAKkB,QAAU,MAEflB,KAAKiS,aACLjS,KAAKiS,WAAWC,SAChBlS,KAAKiS,WAAW1R,UAChBP,KAAKiS,WAAa,MAEtBjS,KAAK4R,cACD,WAAa/R,KACbG,KAAKmS,qBAAqBtS,GAC1BG,KAAK6R,aAGbM,qBAAsB,SAAUtS,GAC5B,GAAI0R,GAAoBvR,KAAKwJ,QAAQqD,KAAKjO,EAAMuS,EAAWI,kBACtDA,GAAkB3L,GAAG,aACtB2L,EAAkB3N,OAElB,WAAa/D,GAAQqB,UACrBlB,KAAKH,QAAQqB,QAAQE,QAAUvB,EAAQqB,QAAQE,UAGvDb,QAAS,WACDP,KAAKmJ,OACLnJ,KAAKmJ,KAAK+I,SACVlS,KAAKmJ,KAAK5I,UACVP,KAAKmJ,KAAO,MAEZnJ,KAAKiS,aACLjS,KAAKiS,WAAWC,SAChBlS,KAAKiS,WAAW1R,UAChBP,KAAKiS,WAAa,MAElBjS,KAAKkB,UACLlB,KAAKkB,QAAQX,UACbP,KAAKkB,QAAU,MAEnB1C,EAAOsB,GAAGS,QAAQR,KAAKC,OAE3B0R,MAAO,WACH1R,KAAKH,QAAQ+P,KAAKlC,GAAKpP,EAAMuC,QAEjCuR,QAAS,WACL,MAAO1T,IAAO,KAAUsB,KAAKH,QAAQ+P,OAEzC3P,SAAU,WAAA,GACFkR,GAAaK,EAAK7Q,OAClBd,EAAUG,KAAKH,QACfwS,EAASxS,EAAQwS,OACjBxN,EAAQhF,EAAQgF,MAChByN,EAAa,eAAkBnB,EAAWE,YAAc,uBAAgCF,EAAWG,kBAAoB,uBAAgCH,EAAWI,kBAAoB,+CAC1LvR,MAAKwJ,QAAUxJ,KAAKJ,QAAQkB,SAASqQ,EAAW3H,SAASnI,OAAOiR,GAC5DzS,EAAQqB,SAAWrB,EAAQqB,QAAQE,SAAWvB,EAAQqB,QAAQE,QAAQW,QACtE/B,KAAKwJ,QAAQqD,KAAKjO,EAAMuS,EAAWI,mBAAmB3N,OAEtDyO,GACArS,KAAKwJ,QAAQ6I,OAAOA,GAEpBxN,GACA7E,KAAKwJ,QAAQ+I,IAAI,YAAa1N,IAGtC8M,MAAO,WAAA,GACCxM,GAAOnF,KACPmR,EAAaK,EAAK7Q,OAClBd,EAAUnB,GAAO,KAAUsB,KAAKH,SAChCD,EAAUI,KAAKwJ,QAAQqD,KAAKjO,EAAMuS,EAAWE,YAAc,GAC/DrR,MAAKmJ,KAAO,GAAI7K,GAAMuE,KAAKmF,SAASpI,EAASC,GAC7CG,KAAKmJ,KAAKqJ,KAAK,cAAe,SAAUjQ,GACpC4C,EAAKzC,QAAQ,cAAeH,GAC5B4C,EAAKsN,YAAYlQ,EAAKE,SAG9BmP,YAAa,WAAA,GACLzM,GAAOnF,KACPmR,EAAaK,EAAK7Q,OAClBd,EAAUnB,GAAO,KAAUsB,KAAKH,SAChCD,EAAUI,KAAKwJ,QAAQqD,KAAKjO,EAAMuS,EAAWG,kBAAoB,GACrEtR,MAAKiS,WAAa,GAAI3T,GAAMuE,KAAKnD,eAAeE,EAASC,GACzDG,KAAKiS,WAAWO,KAAK,cAAe,SAAUjQ,GAC1C4C,EAAKzC,QAAQ,cAAeH,KAC7BiQ,KAAK,YAAa,SAAUjQ,GAC3B4C,EAAKzC,QAAQ,YAAaH,KAC3BiQ,KAAK,cAAe,SAAUjQ,GAC7B4C,EAAKzC,QAAQ,cAAeH,GAC5B4C,EAAKsN,YAAYlQ,EAAKE,QACvB+P,KAAK,gBAAiB,WACrBrN,EAAKuN,mBAGbb,SAAU,WAAA,GACF1M,GAAOnF,KACPmR,EAAaK,EAAK7Q,OAClBd,EAAUnB,GAAO,KAAUyG,EAAKtF,SAChCD,EAAUuF,EAAKqE,QAAQqD,KAAKjO,EAAMuS,EAAWI,kBAAoB,GACjE1R,GAAQqB,QAAQyD,aAAetG,IAC/B2B,KAAKH,QAAQqB,QAAQyD,WAAa9E,EAAQqB,QAAQyD,YAAa,GAE/D9E,EAAQqB,QAAQC,aAAe9C,IAC/B2B,KAAKH,QAAQqB,QAAQC,WAAatB,EAAQqB,QAAQC,YAAa,GAEnEgE,EAAKjE,QAAU,GAAI5C,GAAMuE,KAAKyB,YAAY1E,EAASC,GACnDsF,EAAKjE,QAAQsR,KAAK,QAAS,SAAU7P,GACjCwC,EAAKzC,QAAQ,aACTyK,OAAQhI,EACRV,KAAM9B,EAAG8B,KACTtF,OAAQwD,EAAGxD,OACX8S,WAAY9M,EAAK8M,WAAW/S,MAAM,GAClC0D,cAAeD,EAAGC,mBAI9B6P,YAAa,SAAU5I,GACnB,GAAI8I,GAAWjU,GAAO,MAClB+D,KAAMoH,EACNN,KAAM,UACN6D,UAAW,GAAIC,MACfuF,KAAM5S,KAAKoS,WAEfpS,MAAK0C,QAAQ,OAAQiQ,GACrB3S,KAAKkN,cAAcyF,EAAUA,EAASC,OAE1C1F,cAAe,SAAUrD,EAASsD,GAC9BnN,KAAKmJ,KAAK+D,cAAcrD,EAASsD,IAErCQ,uBAAwB,SAAU9C,GAC9B7K,KAAKmJ,KAAKwE,uBAAuB9C,IAErCiD,kBAAmB,SAAUjO,EAASsN,GAClCnN,KAAKmJ,KAAK2E,kBAAkBjO,EAASsN,IAEzCuF,cAAe,SAAUjL,GACrBzH,KAAKkB,QAAQ8D,OAAOyC,IAExBoL,0BAA2B,SAAU1F,GACjCnN,KAAKmJ,KAAK+G,uBAAuB/C,IAErC2F,yBAA0B,SAAU3F,GAChCnN,KAAKmJ,KAAKsE,yBAAyBN,IAEvC4F,sBAAuB,WACnB/S,KAAKmJ,KAAKmF,2BAGlBhQ,GAAMG,GAAGuU,OAAOxB,GAChB9S,GAAO,EAAM8S,GAAQ7Q,OAAQwQ,KAC/B5S,OAAOD,MAAMwE,QACRvE,OAAOD,OACE,kBAAVH,SAAwBA,OAAO4E,IAAM5E,OAAS,SAAU6E,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.chat.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('chat/messageBox', ['kendo.core'], f);\n}(function () {\n    (function ($, undefined) {\n        var kendo = window.kendo;\n        var Widget = kendo.ui.Widget;\n        var extend = $.extend;\n        var proxy = $.proxy;\n        var DOT = '.';\n        var NS = '.kendoChat';\n        var keys = kendo.keys;\n        var SEND_ICON = '<svg version=\"1.1\" ixmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 16 16\" xml:space=\"preserve\"><path d=\"M0,14.3c-0.1,0.6,0.3,0.8,0.8,0.6l14.8-6.5c0.5-0.2,0.5-0.6,0-0.8L0.8,1.1C0.3,0.9-0.1,1.1,0,1.7l0.7,4.2C0.8,6.5,1.4,7,1.9,7.1l8.8,0.8c0.6,0.1,0.6,0.1,0,0.2L1.9,8.9C1.4,9,0.8,9.5,0.7,10.1L0,14.3z\"/></svg>';\n        var TOGGLE_ICON = '<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 512 512\" style=\"enable-background:new 0 0 512 512;\" xml:space=\"preserve\"><g>   <path d=\"M128,240c0-26.4-21.6-48-48-48s-48,21.6-48,48s21.6,48,48,48S128,266.4,128,240z\"/>   <path d=\"M192,240c0,26.4,21.6,48,48,48c26.4,0,48-21.6,48-48s-21.6-48-48-48C213.6,192,192,213.6,192,240z\"/>   <path d=\"M352,240c0,26.4,21.6,48,48,48c26.4,0,48-21.6,48-48s-21.6-48-48-48C373.6,192,352,213.6,352,240z\"/></g></svg>';\n        var messageBoxStyles = {\n            input: 'k-input',\n            button: 'k-button',\n            buttonFlat: 'k-flat',\n            buttonIcon: 'k-button-icon',\n            buttonSend: 'k-button-send',\n            buttonToggle: 'k-button-toggle',\n            iconAdd: 'k-icon k-i-add',\n            hidden: 'k-hidden'\n        };\n        var ChatMessageBox = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this._wrapper();\n                this._attachEvents();\n                this._typing = false;\n            },\n            events: [],\n            options: { messages: { placeholder: 'Type a message...' } },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                if (this.input) {\n                    this.input.off(NS);\n                    this.input.remove();\n                    this.input = null;\n                }\n                this.element.off(NS);\n                this.element.empty();\n            },\n            _wrapper: function () {\n                var styles = ChatMessageBox.styles;\n                var options = this.options;\n                var messages = options.messages;\n                var inputId = 'inputId_' + kendo.guid();\n                $('<label>').addClass(styles.hidden).html(messages.placeholder).attr('for', inputId).appendTo(this.element);\n                this.input = $('<input type=\\'text\\'>').addClass(styles.input).attr('id', inputId).attr('placeholder', messages.placeholder).appendTo(this.element);\n                if (options.toolbar && options.toolbar.toggleable && options.toolbar.buttons) {\n                    $('<button>').addClass(styles.button).addClass(styles.buttonFlat).addClass(styles.buttonIcon).addClass(styles.buttonToggle).attr('type', 'button').append($(TOGGLE_ICON)).appendTo(this.element);\n                }\n                $('<button>').addClass(styles.button).addClass(styles.buttonFlat).addClass(styles.buttonIcon).addClass(styles.buttonSend).append($(SEND_ICON)).appendTo(this.element);\n            },\n            _attachEvents: function () {\n                var styles = ChatMessageBox.styles;\n                this.input.on('keydown' + NS, proxy(this._keydown, this)).on('input' + NS, proxy(this._input, this)).on('focusout' + NS, proxy(this._inputFocusout, this));\n                this.element.on('click' + NS, DOT + styles.buttonSend, proxy(this._buttonClick, this));\n                this.element.on('click' + NS, DOT + styles.buttonToggle, proxy(this._toggleToolbar, this));\n            },\n            _input: function () {\n                var currentValue = this.input.val();\n                var start = currentValue.length > 0;\n                this._triggerTyping(start);\n            },\n            _keydown: function (e) {\n                var key = e.keyCode;\n                switch (key) {\n                case keys.ENTER:\n                    e.preventDefault();\n                    this._sendMessage();\n                    break;\n                }\n            },\n            _buttonClick: function (e) {\n                e.preventDefault();\n                this._sendMessage();\n            },\n            _sendMessage: function () {\n                var value = this.input.val();\n                if (!value.length) {\n                    return;\n                }\n                this._triggerTyping(false);\n                var args = { text: value };\n                this.trigger('sendMessage', args);\n                this.input.val('');\n            },\n            _inputFocusout: function () {\n                this._triggerTyping(false);\n            },\n            _triggerTyping: function (start) {\n                if (start) {\n                    if (!this._typing) {\n                        this.trigger('typingStart', {});\n                        this._typing = true;\n                    }\n                } else {\n                    if (this._typing) {\n                        this.trigger('typingEnd', {});\n                        this._typing = false;\n                    }\n                }\n            },\n            _toggleToolbar: function (ev) {\n                this.trigger('toggleToolbar', { originalEvent: ev });\n            }\n        });\n        extend(true, ChatMessageBox, { styles: messageBoxStyles });\n        extend(kendo, { chat: { ChatMessageBox: ChatMessageBox } });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('chat/toolbar', ['kendo.core'], f);\n}(function () {\n    (function ($, undefined) {\n        var kendo = window.kendo;\n        var Widget = kendo.ui.Widget;\n        var extend = $.extend;\n        var proxy = $.proxy;\n        var DOT = '.';\n        var NS = '.kendoChat';\n        var DATA_K_BUTTON_NAME = 'kButtonName';\n        var SCROLL_LEFT_NAME = 'chatToolbarScrollLeft';\n        var SCROLL_RIGHT_NAME = 'chatToolbarScrollRight';\n        var VISIBLE = ':visible';\n        var DEFAULT_ANIMATION = {\n            effects: 'expand:vertical',\n            duration: 200\n        };\n        var NO_ANIMATION = {\n            expand: { show: true },\n            collapse: { hide: true }\n        };\n        var toolbarStyles = {\n            button: 'k-button',\n            buttonFlat: 'k-flat',\n            buttonList: 'k-button-list',\n            scrollButton: 'k-scroll-button',\n            scrollButtonLeft: 'k-scroll-button-left',\n            scrollButtonRight: 'k-scroll-button-right',\n            scrollButtonLeftIcon: 'k-icon k-i-arrow-chevron-left',\n            scrollButtonRightIcon: 'k-icon k-i-arrow-chevron-right',\n            buttonIcon: 'k-button-icon'\n        };\n        var ChatToolBar = Widget.extend({\n            init: function (element, options) {\n                options = extend({}, options, { name: 'ChatToolbar' });\n                var toolbarOptions = options.toolbar;\n                var buttonsDefined = toolbarOptions.buttons && toolbarOptions.buttons.length;\n                Widget.fn.init.call(this, element, options);\n                if (buttonsDefined) {\n                    this._createButtonList();\n                }\n                if (buttonsDefined && toolbarOptions.scrollable && this.buttonsWidth() > this.element.width()) {\n                    this._initScrolling();\n                }\n                this._setupAnimation();\n                if (toolbarOptions.toggleable) {\n                    this.toggle(true);\n                }\n                this.element.on('click' + NS, proxy(this._onClick, this));\n            },\n            events: ['click'],\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.element.off(NS);\n                this.element.empty();\n            },\n            _createButtonList: function () {\n                var that = this;\n                var styles = ChatToolBar.styles;\n                var buttons = that.options.toolbar.buttons;\n                var buttonList = $('<div class=\\'' + styles.buttonList + '\\'></div>');\n                for (var i = 0; i < buttons.length; i++) {\n                    var button = that._createButton(buttons[i]);\n                    buttonList.append(button);\n                }\n                buttonList.appendTo(this.element);\n                this.buttonList = buttonList;\n            },\n            _createButton: function (btnOptions) {\n                var styles = ChatToolBar.styles;\n                var buttonElm = $('<button>');\n                if (typeof btnOptions === 'string') {\n                    btnOptions = { name: btnOptions };\n                }\n                buttonElm.attr(btnOptions.attr || {}).attr('title', btnOptions.text).attr('type', 'button').addClass(btnOptions.name).data(DATA_K_BUTTON_NAME, btnOptions.name).addClass(styles.button);\n                if (btnOptions.iconClass) {\n                    buttonElm.addClass(styles.buttonIcon);\n                    buttonElm.prepend('<span class=\\'' + btnOptions.iconClass + '\\'></span>');\n                }\n                return buttonElm;\n            },\n            _onClick: function (ev) {\n                var styles = ChatToolBar.styles;\n                var target = $(ev.target).closest(DOT + styles.button);\n                if (target.is(DOT + styles.scrollButton) && !this._scrolling) {\n                    this._scroll(target.data(DATA_K_BUTTON_NAME));\n                }\n                if (target.data(DATA_K_BUTTON_NAME)) {\n                    this.trigger('click', {\n                        button: target[0],\n                        name: target.data(DATA_K_BUTTON_NAME),\n                        originalEvent: ev\n                    });\n                }\n            },\n            _initScrolling: function () {\n                var styles = ChatToolBar.styles;\n                this.scrollButtonLeft = this._createButton({\n                    name: SCROLL_LEFT_NAME,\n                    iconClass: styles.scrollButtonLeftIcon,\n                    attr: { 'class': styles.scrollButton + ' ' + styles.scrollButtonLeft }\n                });\n                this.scrollButtonRight = this._createButton({\n                    name: SCROLL_RIGHT_NAME,\n                    iconClass: styles.scrollButtonRightIcon,\n                    attr: { 'class': styles.scrollButton + ' ' + styles.scrollButtonRight }\n                });\n                this.element.prepend(this.scrollButtonLeft);\n                this.element.append(this.scrollButtonRight);\n                this._refreshScrollButtons();\n                this.element.on('keydown' + NS, proxy(this._refreshScrollButtons, this));\n            },\n            _scroll: function (commandName) {\n                var that = this;\n                var buttonWidth = that.buttonWidth();\n                var maxScrollSize = this.maxScrollSize();\n                var scrollAmmount = commandName === SCROLL_LEFT_NAME ? buttonWidth * -1 : buttonWidth;\n                var currentScroll = this.currentScrollLeft();\n                var scrollValue = currentScroll + scrollAmmount;\n                scrollValue = Math.min(Math.max(scrollValue, 0), maxScrollSize);\n                if (commandName !== SCROLL_LEFT_NAME && commandName !== SCROLL_RIGHT_NAME) {\n                    return;\n                }\n                that.buttonList.scrollLeft(scrollValue);\n                that._refreshScrollButtons(scrollValue);\n            },\n            _refreshScrollButtons: function (value) {\n                var maxScrollSize = this.maxScrollSize();\n                var currentScrollLeft = value === undefined || isNaN(parseInt(value, 10)) ? this.currentScrollLeft() : value;\n                if (!this.scrollButtonLeft && !this.scrollButtonRight) {\n                    return;\n                }\n                this.scrollButtonLeft.toggle(currentScrollLeft !== 0);\n                this.scrollButtonRight.toggle(currentScrollLeft !== maxScrollSize);\n            },\n            _setupAnimation: function () {\n                var animation = this.options.toolbar.animation;\n                var defaultExpandAnimation = extend({}, DEFAULT_ANIMATION);\n                var defaultCollapseAnimation = extend({\n                    reverse: true,\n                    hide: true\n                }, DEFAULT_ANIMATION);\n                if (animation === false) {\n                    animation = extend(true, {}, NO_ANIMATION);\n                } else {\n                    animation = extend(true, {\n                        expand: defaultExpandAnimation,\n                        collapse: defaultCollapseAnimation\n                    }, animation);\n                }\n                this.options.toolbar.animation = animation;\n            },\n            _animationComplete: function () {\n                this._refreshScrollButtons();\n            },\n            currentScrollLeft: function () {\n                return Math.round(this.buttonList.scrollLeft());\n            },\n            maxScrollSize: function () {\n                return Math.round(this.buttonList[0].scrollWidth - this.buttonList[0].clientWidth);\n            },\n            buttons: function () {\n                var styles = ChatToolBar.styles;\n                return this.buttonList ? this.buttonList.children(DOT + styles.button) : null;\n            },\n            buttonWidth: function () {\n                return Math.round(this.buttons().last().outerWidth(true));\n            },\n            buttonsWidth: function () {\n                var width = 0;\n                if (this.buttons()) {\n                    width = this.buttonWidth() * this.buttons().length;\n                }\n                return width;\n            },\n            toggle: function (skipAnimation) {\n                var animation = this.options.toolbar.animation;\n                if (skipAnimation) {\n                    animation = extend(true, {}, NO_ANIMATION);\n                }\n                animation.expand.complete = proxy(this._animationComplete, this);\n                animation.collapse.complete = proxy(this._animationComplete, this);\n                if (this.element.is(VISIBLE)) {\n                    this.element.kendoStop().kendoAnimate(animation.collapse);\n                } else {\n                    this.element.kendoStop().kendoAnimate(animation.expand);\n                }\n            }\n        });\n        extend(true, ChatToolBar, { styles: toolbarStyles });\n        extend(kendo.chat, { ChatToolBar: ChatToolBar });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('chat/view', [\n        'kendo.core',\n        'kendo.draganddrop'\n    ], f);\n}(function () {\n    (function ($, undefined) {\n        var kendo = window.kendo;\n        var Widget = kendo.ui.Widget;\n        var extend = $.extend;\n        var proxy = $.proxy;\n        var DOT = '.';\n        var SPACE = ' ';\n        var NS = '.kendoChat';\n        var MESSAGE_GROUP_TEMPLATE = kendo.template('<div #:text# class=\"#=styles.messageGroup# #= url ? \"\" : styles.noAvatar #\">' + '<p class=\"#=styles.author#\">#:text#</p>' + '# if (url) { #' + '<img src=\"#=url#\" alt=\"#:text#\" class=\"#=styles.avatar#\">' + '# } #' + '</div>');\n        var SELF_MESSAGE_GROUP_TEMPLATE = kendo.template('<div me class=\"#=styles.messageGroup# #=styles.self# #= url ? \"\" : styles.noAvatar #\">' + '# if (url) { #' + '<img src=\"#=url#\" alt=\"#:text#\" class=\"#=styles.avatar#\">' + '# } #' + '</div>');\n        var TEXT_MESSAGE_TEMPLATE = kendo.template('<div class=\"#=styles.message#\">' + '<time class=\"#=styles.messageTime#\">#= kendo.toString(kendo.parseDate(timestamp), \"HH:mm:ss\") #</time>' + '<div class=\"#=styles.bubble#\">#:text#</div>' + '</div>');\n        var TYPING_INDICATOR_TEMPLATE = kendo.template('<div class=\"#=styles.messageListContent# #=styles.typingIndicatorBubble#\">' + '<p class=\"#=styles.author#\">#:text#</p>' + '<div class=\"#=styles.message#\">' + '<div class=\"#=styles.bubble#\">' + '<div class=\"#=styles.typingIndicator#\">' + '<span></span><span></span><span></span>' + '</div>' + '</div>' + '</div>' + '</div>');\n        var SUGGESTED_ACTIONS_TEMPLATE = kendo.template('<div class=\"#=styles.suggestedActions#\">' + '# for (var i = 0; i < suggestedActions.length; i++) { #' + '<span class=\"#=styles.suggestedAction#\" data-value=\"#:suggestedActions[i].value#\">#:suggestedActions[i].title#</span>' + '# } #' + '</div>');\n        var HERO_CARD_TEMPLATE = kendo.template('<div class=\"#=styles.card# #=styles.cardRich#\">' + '# if (typeof images !== \"undefined\" && images.length > 0) { #' + '<img src=\"#:images[0].url#\" alt=\"#:images[0].alt#\" class=\"#=styles.cardImage#\" />' + '# } #' + '<div class=\"#=styles.cardBody#\">' + '# if (typeof title !== \"undefined\") { #' + '<h5 class=\"#=styles.cardTitle#\">#:title#</h5>' + '# } #' + '# if (typeof subtitle !== \"undefined\") { #' + '<h6 class=\"#=styles.cardSubtitle#\">#:subtitle#</h6>' + '# } #' + '# if (typeof text !== \"undefined\") { #' + '<p>#:text#</p>' + '# } #' + '</div>' + '# if (typeof buttons !== \"undefined\" && buttons.length > 0) { #' + '<div class=\"#=styles.cardActions# #=styles.cardActionsVertical#\">' + '# for (var i = 0; i < buttons.length; i++) { #' + '<span class=\"#=styles.cardAction#\"><span class=\"#=styles.button# #=styles.buttonPrimary#\" data-value=\"#:buttons[i].value#\">#:buttons[i].title#</span></span>' + '# } #' + '</div>' + '# } #' + '</div>');\n        extend(kendo.chat, {\n            Templates: {},\n            Components: {}\n        });\n        kendo.chat.registerTemplate = function (templateName, template) {\n            kendo.chat.Templates[templateName] = kendo.template(template);\n        };\n        kendo.chat.getTemplate = function (templateName) {\n            return kendo.chat.Templates[templateName] || TEXT_MESSAGE_TEMPLATE;\n        };\n        kendo.chat.registerTemplate('text', TEXT_MESSAGE_TEMPLATE);\n        kendo.chat.registerTemplate('message', TEXT_MESSAGE_TEMPLATE);\n        kendo.chat.registerTemplate('typing', TYPING_INDICATOR_TEMPLATE);\n        kendo.chat.registerTemplate('suggestedAction', SUGGESTED_ACTIONS_TEMPLATE);\n        kendo.chat.registerTemplate('heroCard', HERO_CARD_TEMPLATE);\n        kendo.chat.registerTemplate('application/vnd.microsoft.card.hero', HERO_CARD_TEMPLATE);\n        kendo.chat.registerComponent = function (componentName, component) {\n            kendo.chat.Components[componentName] = component;\n        };\n        kendo.chat.getComponent = function (componentName) {\n            return kendo.chat.Components[componentName] || null;\n        };\n        var Component = kendo.chat.Component = kendo.Class.extend({\n            init: function (options, view) {\n                this.element = $('<div></div>');\n                this.options = options;\n                this.view = view;\n            },\n            destroy: function () {\n                kendo.destroy(this.element);\n            }\n        });\n        var Calendar = Component.extend({\n            init: function (options, view) {\n                Component.fn.init.call(this, options, view);\n                this.element.kendoCalendar({\n                    change: function () {\n                        view.trigger('suggestedAction', {\n                            text: kendo.toString(this.value(), 'd'),\n                            type: 'message'\n                        });\n                    }\n                });\n            },\n            destroy: function () {\n            }\n        });\n        kendo.chat.registerComponent('calendar', Calendar);\n        var viewStyles = {\n            wrapper: 'k-widget k-chat',\n            messageList: 'k-avatars',\n            messageListContent: 'k-message-list-content',\n            messageTime: 'k-message-time',\n            messageGroup: 'k-message-group',\n            message: 'k-message',\n            only: 'k-only',\n            first: 'k-first',\n            middle: 'k-middle',\n            last: 'k-last',\n            author: 'k-author',\n            avatar: 'k-avatar',\n            noAvatar: 'k-no-avatar',\n            self: 'k-alt',\n            button: 'k-button',\n            iconButton: 'k-button-icon',\n            buttonPrimary: 'k-flat k-primary',\n            scrollButtonIcon: 'k-icon',\n            scrollButtonIconLeft: 'k-i-arrow-chevron-left',\n            scrollButtonIconRight: 'k-i-arrow-chevron-right',\n            typingIndicator: 'k-typing-indicator',\n            typingIndicatorBubble: 'k-typing-indicator-bubble',\n            bubble: 'k-bubble',\n            suggestedActions: 'k-quick-replies',\n            suggestedAction: 'k-quick-reply',\n            cardWrapper: 'k-card-container',\n            cardDeckScrollWrap: 'k-card-deck-scrollwrap',\n            cardDeck: 'k-card-deck',\n            cardList: 'k-card-list',\n            card: 'k-card',\n            cardRich: 'k-card-type-rich',\n            cardBody: 'k-card-body',\n            cardImage: 'k-card-image',\n            cardTitle: 'k-card-title',\n            cardSubtitle: 'k-card-subtitle',\n            cardActions: 'k-card-actions',\n            cardActionsVertical: 'k-card-actions-vertical',\n            cardAction: 'k-card-action',\n            selected: 'k-state-selected'\n        };\n        var ChatView = kendo.chat.ChatView = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this._list();\n                this._lastSender = null;\n                this.typingParticipants = [];\n                this._attachEvents();\n                this._scrollable();\n            },\n            events: [],\n            options: {\n                messages: {\n                    isTyping: ' is typing.',\n                    areTyping: ' are typing.',\n                    and: ' and '\n                }\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                if (this._scrollDraggable) {\n                    this._scrollDraggable.destroy();\n                }\n                this.element.empty();\n                this.element.off(NS);\n                this.list = null;\n                this._lastSender = null;\n            },\n            _list: function () {\n                var viewStyles = ChatView.styles;\n                this.element.addClass(viewStyles.messageList).attr('aria-live', 'polite');\n                this.list = $('<div>').addClass(viewStyles.messageListContent).appendTo(this.element);\n            },\n            _attachEvents: function () {\n                var styles = ChatView.styles;\n                this.element.on('click' + NS, proxy(this._listClick, this)).on('click' + NS, DOT + styles.message, proxy(this._messageClick, this)).on('click' + NS, DOT + styles.suggestedAction, proxy(this._suggestedActionClick, this)).on('click' + NS, DOT + styles.cardAction + SPACE + DOT + styles.button, proxy(this._cardActionClick, this));\n            },\n            _scrollable: function () {\n                var viewStyles = ChatView.styles;\n                this.element.on('click' + NS, DOT + viewStyles.cardDeckScrollWrap + SPACE + DOT + viewStyles.button, proxy(this._scrollButtonClick, this));\n            },\n            _scrollButtonClick: function (e) {\n                var viewStyles = ChatView.styles;\n                var button = $(e.currentTarget);\n                var scrollToLeft = button.find(DOT + viewStyles.scrollButtonIconLeft).length !== 0;\n                var scrollContainer = button.siblings(DOT + viewStyles.cardDeck);\n                var lastCard = scrollContainer.find(DOT + viewStyles.card).last();\n                var cardWidth = lastCard.outerWidth(true);\n                if (scrollToLeft) {\n                    scrollContainer.scrollLeft(scrollContainer.scrollLeft() - cardWidth);\n                } else {\n                    scrollContainer.scrollLeft(scrollContainer.scrollLeft() + cardWidth);\n                }\n            },\n            getTemplate: function (templateName) {\n                return kendo.chat.getTemplate(templateName);\n            },\n            getComponent: function (type) {\n                return kendo.chat.getComponent(type);\n            },\n            renderMessage: function (message, sender) {\n                if (!message.timestamp) {\n                    message.timestamp = new Date();\n                }\n                if (!message.text) {\n                    message.text = '';\n                }\n                var bubbleElement = this._renderTemplate(message.type, message);\n                this._renderBubble(message.type, bubbleElement, sender);\n                if (message.type == 'typing') {\n                    if (this.typingParticipants.length > 0) {\n                        this._removeTypingParticipant(sender);\n                    }\n                } else {\n                    this._lastSender = sender.id;\n                }\n            },\n            renderSuggestedActions: function (suggestedActions) {\n                this._removeSuggestedActions();\n                var element = this._renderTemplate('suggestedAction', { suggestedActions: suggestedActions });\n                this.list.append(element);\n                this._scrollToBottom();\n            },\n            renderAttachments: function (options) {\n                var wrapper = this._renderAttachmentWrapper(options.attachmentLayout);\n                var cardContainer = options.attachmentLayout === 'carousel' ? wrapper.find(DOT + ChatView.styles.cardDeck) : wrapper;\n                var attachments = options.attachments;\n                if (!attachments.length) {\n                    return;\n                }\n                for (var i = 0; i < attachments.length; i++) {\n                    var cardElement = this._renderTemplate(attachments[i].contentType, attachments[i].content);\n                    cardContainer.append(cardElement);\n                }\n                this._removeSuggestedActions();\n                this._removeTypingIndicator();\n                this.list.append(wrapper);\n                this._lastSender = null;\n            },\n            renderComponent: function (type) {\n                var componentType = this.getComponent(type);\n                var component = new componentType({}, this);\n                this.list.append(component.element);\n                this._scrollToBottom();\n            },\n            _renderAttachmentWrapper: function (layout) {\n                var viewStyles = ChatView.styles;\n                var wrapper = $('<div>');\n                if (layout === 'carousel') {\n                    wrapper.addClass(viewStyles.cardDeckScrollWrap);\n                    var buttonLeft = this._renderScrollButton(viewStyles.scrollButtonIconLeft);\n                    wrapper.append(buttonLeft);\n                    wrapper.append($('<div>').addClass(viewStyles.cardDeck));\n                    var buttonRight = this._renderScrollButton(viewStyles.scrollButtonIconRight);\n                    wrapper.append(buttonRight);\n                } else {\n                    wrapper.addClass(viewStyles.cardList);\n                }\n                return wrapper;\n            },\n            _renderScrollButton: function (directionClass) {\n                var viewStyles = ChatView.styles;\n                return $('<button>').addClass(viewStyles.button).addClass(viewStyles.iconButton).append($('<span>').addClass(viewStyles.scrollButtonIcon).addClass(directionClass));\n            },\n            _removeSuggestedActions: function () {\n                this.list.find(DOT + ChatView.styles.suggestedActions).remove();\n            },\n            _listClick: function (e) {\n                var styles = ChatView.styles;\n                var targetElement = $(e.target);\n                if (targetElement.hasClass(styles.message) || targetElement.parents(DOT + styles.message).length) {\n                    return;\n                }\n                this._clearSelection();\n            },\n            _messageClick: function (e) {\n                this._clearSelection();\n                $(e.currentTarget).addClass(ChatView.styles.selected);\n            },\n            _suggestedActionClick: function (e) {\n                var text = $(e.target).data('value') || '';\n                this.trigger('actionClick', { text: text });\n                this._removeSuggestedActions();\n            },\n            _cardActionClick: function (e) {\n                var text = $(e.target).data('value') || '';\n                this.trigger('actionClick', { text: text });\n            },\n            _renderBubble: function (messageType, bubbleElement, sender) {\n                this._removeSuggestedActions();\n                this._removeTypingIndicator();\n                var group = this._getMessageGroup(sender, messageType);\n                this._appendToGroup(group, bubbleElement, messageType);\n                this._scrollToBottom();\n            },\n            _renderTemplate: function (type, options) {\n                var componentType = this.getComponent(type);\n                var element;\n                if (componentType) {\n                    var component = new componentType(options, this);\n                    element = component.element;\n                } else {\n                    var template = this.getTemplate(type);\n                    var templateOptions = extend(true, {}, options, { styles: ChatView.styles });\n                    element = $(template(templateOptions));\n                }\n                return element;\n            },\n            _getMessageGroup: function (sender, messageType) {\n                var viewStyles = ChatView.styles;\n                var template = this._getMessageGroupTemplate(sender, messageType);\n                var appendTarget = messageType == 'typing' ? this.element : this.list;\n                var group;\n                if (sender.id === this._lastSender && this._lastSender !== null && messageType !== 'typing') {\n                    group = this.list.find(DOT + viewStyles.messageGroup).last();\n                    if (group.length) {\n                        return group;\n                    }\n                }\n                return $(template({\n                    text: sender.name,\n                    url: sender.iconUrl,\n                    styles: viewStyles\n                })).appendTo(appendTarget);\n            },\n            _getMessageGroupTemplate: function (sender, messageType) {\n                var isOwnMessage = sender.id === this.options.user.id;\n                var template = isOwnMessage ? SELF_MESSAGE_GROUP_TEMPLATE : MESSAGE_GROUP_TEMPLATE;\n                if (messageType == 'typing') {\n                    template = TYPING_INDICATOR_TEMPLATE;\n                }\n                return template;\n            },\n            _appendToGroup: function (group, messageElement, messageType) {\n                var viewStyles = ChatView.styles;\n                var children = group.find(DOT + viewStyles.message);\n                var childrenCount = children.length;\n                var indicator = this.element.find(DOT + viewStyles.typingIndicator);\n                if (indicator.length && messageType == 'typing') {\n                    return;\n                }\n                messageElement.addClass(childrenCount === 0 ? viewStyles.only : viewStyles.last);\n                children.filter(DOT + viewStyles.only).removeClass(viewStyles.only).addClass(viewStyles.first);\n                children.filter(DOT + viewStyles.last).removeClass(viewStyles.last).addClass(viewStyles.middle);\n                group.append(messageElement);\n            },\n            _renderTypingIndicator: function (sender) {\n                var indicator = this.element.find(DOT + viewStyles.typingIndicatorBubble), indicatorList, participants;\n                this._addTypingParticipant(sender);\n                if (indicator.length) {\n                    participants = this._composeTypingParticipantsText(this.typingParticipants);\n                    indicatorList = indicator.find(DOT + viewStyles.author).first();\n                    indicatorList.text(participants);\n                } else {\n                    $(TYPING_INDICATOR_TEMPLATE({\n                        text: sender.name + this.options.messages.isTyping,\n                        styles: viewStyles\n                    })).appendTo(this.element);\n                }\n                this._scrollToBottom();\n            },\n            _addTypingParticipant: function (sender) {\n                var found = false;\n                for (var i = 0; i < this.typingParticipants.length; i += 1) {\n                    if (this.typingParticipants[i].id == sender.id) {\n                        found = true;\n                        break;\n                    }\n                }\n                if (!found) {\n                    this.typingParticipants.push(sender);\n                }\n            },\n            _removeTypingParticipant: function (sender) {\n                var indicator = this.element.find(DOT + viewStyles.typingIndicatorBubble), indicatorList, participants;\n                if (indicator.length) {\n                    for (var i = 0; i < this.typingParticipants.length; i += 1) {\n                        if (this.typingParticipants[i].id == sender.id) {\n                            this.typingParticipants.splice(i, 1);\n                        }\n                    }\n                    participants = this._composeTypingParticipantsText(this.typingParticipants);\n                    if (participants === '') {\n                        indicator.remove();\n                    } else {\n                        indicatorList = indicator.find(DOT + viewStyles.author).first();\n                        indicatorList.text(participants);\n                    }\n                }\n            },\n            _composeTypingParticipantsText: function (participants) {\n                var messages = this.options.messages, typingAction = participants.length == 1 ? messages.isTyping : messages.areTyping, typingText = '';\n                if (participants.length === 0) {\n                    return typingText;\n                }\n                typingText = this.typingParticipants.map(function (author) {\n                    return author.name;\n                }).join(', ').replace(/,(?!.*,)/gim, messages.and.trimRight()) + typingAction;\n                return typingText;\n            },\n            _removeTypingIndicator: function () {\n                var indicator = this.element.find(DOT + viewStyles.typingIndicatorBubble);\n                if (indicator.length) {\n                    this.typingParticipants = [];\n                    indicator.remove();\n                }\n            },\n            _clearSelection: function () {\n                var selectedClass = ChatView.styles.selected;\n                this.element.find(DOT + selectedClass).removeClass(selectedClass);\n            },\n            _scrollToBottom: function () {\n                this.element.scrollTop(this.element.prop('scrollHeight'));\n            }\n        });\n        extend(true, ChatView, { styles: viewStyles });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.chat', [\n        'chat/messageBox',\n        'chat/toolbar',\n        'chat/view'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'chat',\n        name: 'Chat',\n        category: 'web',\n        description: 'The Chat component.',\n        depends: [\n            'core',\n            'draganddrop'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo;\n        var Widget = kendo.ui.Widget;\n        var extend = $.extend;\n        var DOT = '.';\n        var chatStyles = {\n            wrapper: 'k-widget k-chat',\n            canvas: 'k-chat-canvas',\n            viewWrapper: 'k-message-list',\n            messageBoxWrapper: 'k-message-box',\n            toolbarBoxWrapper: 'k-toolbar-box'\n        };\n        var Chat = Widget.extend({\n            init: function (element, options, events) {\n                Widget.fn.init.call(this, element, options);\n                if (events) {\n                    this._events = events;\n                }\n                this._user();\n                this._wrapper();\n                this._view();\n                this._messageBox();\n                if (options && options.toolbar && options.toolbar.buttons) {\n                    this._toolbar();\n                }\n                kendo.notify(this);\n            },\n            events: [\n                'typingStart',\n                'typingEnd',\n                'post',\n                'sendMessage',\n                'actionClick',\n                'toolClick'\n            ],\n            options: {\n                user: {\n                    name: 'User',\n                    iconUrl: ''\n                },\n                name: 'Chat',\n                messages: { placeholder: 'Type a message...' },\n                toolbar: false\n            },\n            setOptions: function (options) {\n                this._setEvents(options);\n                $.extend(true, this.options, options);\n                if (this.toolbar && 'toolbar' in options) {\n                    this.toolbar.destroy();\n                    this.toolbar = null;\n                }\n                if (this.messageBox) {\n                    this.messageBox.unbind();\n                    this.messageBox.destroy();\n                    this.messageBox = null;\n                }\n                this._messageBox();\n                if ('toolbar' in options) {\n                    this._resetToolbarButtons(options);\n                    this._toolbar();\n                }\n            },\n            _resetToolbarButtons: function (options) {\n                var toolbarBoxWrapper = this.wrapper.find(DOT + chatStyles.toolbarBoxWrapper);\n                if (!toolbarBoxWrapper.is(':visible')) {\n                    toolbarBoxWrapper.show();\n                }\n                if ('buttons' in options.toolbar) {\n                    this.options.toolbar.buttons = options.toolbar.buttons;\n                }\n            },\n            destroy: function () {\n                if (this.view) {\n                    this.view.unbind();\n                    this.view.destroy();\n                    this.view = null;\n                }\n                if (this.messageBox) {\n                    this.messageBox.unbind();\n                    this.messageBox.destroy();\n                    this.messageBox = null;\n                }\n                if (this.toolbar) {\n                    this.toolbar.destroy();\n                    this.toolbar = null;\n                }\n                Widget.fn.destroy.call(this);\n            },\n            _user: function () {\n                this.options.user.id = kendo.guid();\n            },\n            getUser: function () {\n                return extend(true, {}, this.options.user);\n            },\n            _wrapper: function () {\n                var chatStyles = Chat.styles;\n                var options = this.options;\n                var height = options.height;\n                var width = options.width;\n                var uiElements = '<div class=\\'' + chatStyles.viewWrapper + '\\'></div>' + '<div class=\\'' + chatStyles.messageBoxWrapper + '\\'></div>' + '<div class=\\'' + chatStyles.toolbarBoxWrapper + '\\' role=\\'toolbar\\' style=\\'display:none;\\'></div>';\n                this.wrapper = this.element.addClass(chatStyles.wrapper).append(uiElements);\n                if (options.toolbar && options.toolbar.buttons && options.toolbar.buttons.length) {\n                    this.wrapper.find(DOT + chatStyles.toolbarBoxWrapper).show();\n                }\n                if (height) {\n                    this.wrapper.height(height);\n                }\n                if (width) {\n                    this.wrapper.css('max-width', width);\n                }\n            },\n            _view: function () {\n                var that = this;\n                var chatStyles = Chat.styles;\n                var options = extend(true, {}, this.options);\n                var element = this.wrapper.find(DOT + chatStyles.viewWrapper + '');\n                this.view = new kendo.chat.ChatView(element, options);\n                this.view.bind('actionClick', function (args) {\n                    that.trigger('actionClick', args);\n                    that.postMessage(args.text);\n                });\n            },\n            _messageBox: function () {\n                var that = this;\n                var chatStyles = Chat.styles;\n                var options = extend(true, {}, this.options);\n                var element = this.wrapper.find(DOT + chatStyles.messageBoxWrapper + '');\n                this.messageBox = new kendo.chat.ChatMessageBox(element, options);\n                this.messageBox.bind('typingStart', function (args) {\n                    that.trigger('typingStart', args);\n                }).bind('typingEnd', function (args) {\n                    that.trigger('typingEnd', args);\n                }).bind('sendMessage', function (args) {\n                    that.trigger('sendMessage', args);\n                    that.postMessage(args.text);\n                }).bind('toggleToolbar', function () {\n                    that.toggleToolbar();\n                });\n            },\n            _toolbar: function () {\n                var that = this;\n                var chatStyles = Chat.styles;\n                var options = extend(true, {}, that.options);\n                var element = that.wrapper.find(DOT + chatStyles.toolbarBoxWrapper + '');\n                if (options.toolbar.scrollable === undefined) {\n                    this.options.toolbar.scrollable = options.toolbar.scrollable = true;\n                }\n                if (options.toolbar.toggleable === undefined) {\n                    this.options.toolbar.toggleable = options.toolbar.toggleable = false;\n                }\n                that.toolbar = new kendo.chat.ChatToolBar(element, options);\n                that.toolbar.bind('click', function (ev) {\n                    that.trigger('toolClick', {\n                        sender: that,\n                        name: ev.name,\n                        button: ev.button,\n                        messageBox: that.messageBox.input[0],\n                        originalEvent: ev.originalEvent\n                    });\n                });\n            },\n            postMessage: function (message) {\n                var postArgs = extend(true, {}, {\n                    text: message,\n                    type: 'message',\n                    timestamp: new Date(),\n                    from: this.getUser()\n                });\n                this.trigger('post', postArgs);\n                this.renderMessage(postArgs, postArgs.from);\n            },\n            renderMessage: function (message, sender) {\n                this.view.renderMessage(message, sender);\n            },\n            renderSuggestedActions: function (suggestedActions) {\n                this.view.renderSuggestedActions(suggestedActions);\n            },\n            renderAttachments: function (options, sender) {\n                this.view.renderAttachments(options, sender);\n            },\n            toggleToolbar: function (skipAnimation) {\n                this.toolbar.toggle(skipAnimation);\n            },\n            renderUserTypingIndicator: function (sender) {\n                this.view._renderTypingIndicator(sender);\n            },\n            clearUserTypingIndicator: function (sender) {\n                this.view._removeTypingParticipant(sender);\n            },\n            removeTypingIndicator: function () {\n                this.view._removeTypingIndicator();\n            }\n        });\n        kendo.ui.plugin(Chat);\n        extend(true, Chat, { styles: chatStyles });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}