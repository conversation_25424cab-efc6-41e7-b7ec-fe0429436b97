{"version": 3, "sources": ["kendo.data.xml.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "isArray", "isPlainObject", "map", "each", "extend", "getter", "Class", "XmlDataReader", "init", "options", "base", "id", "idField", "xmlParse", "that", "this", "total", "model", "parse", "errors", "serialize", "data", "modelBase", "Model", "fields", "field", "value", "isFunction", "xpathToMember", "parseInt", "modelInstance", "result", "evaluate", "record", "_parse", "xml", "call", "length", "parseDOM", "element", "parsedNode", "node", "nodeType", "nodeName", "member", "attribute", "idx", "attributes", "attributeCount", "nodeValue", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "push", "expression", "intermediateResult", "members", "split", "shift", "join", "apply", "documentElement", "tree", "parseXML", "raw", "replace", "indexOf", "readers", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,kBAAmB,cAAeD,IAC3C,WAkLE,MA1KC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAUJ,EAAEI,QAASC,EAAgBL,EAAEK,cAAeC,EAAMN,EAAEM,IAAKC,EAAOP,EAAEO,KAAMC,EAASR,EAAEQ,OAAQC,EAASP,EAAMO,OAAQC,EAAQR,EAAMQ,MAChKC,EAAgBD,EAAMF,QACtBI,KAAM,SAAUC,GAAV,GAIUC,GAaAC,EAEIC,EAsDRC,EAxEJC,EAAOC,KAAMC,EAAQP,EAAQO,MAAOC,EAAQR,EAAQQ,MAAOC,EAAQT,EAAQS,MAAOC,EAASV,EAAQU,OAAQC,EAAYX,EAAQW,UAAWC,EAAOZ,EAAQY,IACzJJ,KACIhB,EAAcgB,KACVP,EAAOD,EAAQa,WAAaxB,EAAMuB,KAAKE,MACvCN,EAAMO,QACNrB,EAAKc,EAAMO,OAAQ,SAAUC,EAAOC,GAC5BzB,EAAcyB,IAAUA,EAAMD,MACzB7B,EAAE+B,WAAWD,EAAMD,SACpBC,EAAQtB,EAAOsB,GAASD,MAAOX,EAAKT,OAAOqB,EAAMD,UAGrDC,GAAUD,MAAOX,EAAKT,OAAOqB,IAEjCT,EAAMO,OAAOC,GAASC,IAG1Bf,EAAKM,EAAMN,GACXA,IACIC,KACJA,EAAQE,EAAKc,cAAcjB,GAAI,KAAWc,MAAOX,EAAKT,OAAOM,IAC7DM,EAAMO,OAASpB,EAAOQ,EAASK,EAAMO,QACrCP,EAAMN,GAAKG,EAAKc,cAAcjB,IAElCM,EAAQP,EAAKf,OAAOsB,IAExBH,EAAKG,MAAQA,GAEbD,IACoB,gBAATA,IACPA,EAAQF,EAAKT,OAAOW,GACpBF,EAAKE,MAAQ,SAAUK,GACnB,MAAOQ,UAASb,EAAMK,GAAO,MAEV,kBAATL,KACdF,EAAKE,MAAQA,IAGjBG,IACqB,gBAAVA,IACPA,EAASL,EAAKT,OAAOc,GACrBL,EAAKK,OAAS,SAAUE,GACpB,MAAOF,GAAOE,IAAS,OAEH,kBAAVF,KACdL,EAAKK,OAASA,IAGlBE,IACmB,gBAARA,IACPA,EAAOP,EAAKc,cAAcP,GAC1BP,EAAKO,KAAO,SAAUK,GAClB,GAAyCI,GAArCC,EAASjB,EAAKkB,SAASN,EAAOL,EAElC,OADAU,GAAS/B,EAAQ+B,GAAUA,GAAUA,GACjCjB,EAAKG,OAASA,EAAMO,QACpBM,EAAgB,GAAIhB,GAAKG,MAClBf,EAAI6B,EAAQ,SAAUL,GACzB,GAAIA,EAAO,CACP,GAAiBD,GAAbQ,IACJ,KAAKR,IAASR,GAAMO,OAChBS,EAAOR,GAASK,EAAcI,OAAOT,EAAOR,EAAMO,OAAOC,GAAOA,MAAMC,GAE1E,OAAOO,OAIZF,IAEW,kBAARV,KACdP,EAAKO,KAAOA,IAGA,kBAATH,KACHL,EAAWC,EAAKI,MACpBJ,EAAKI,MAAQ,SAAUG,GACnB,GAAIc,GAAMjB,EAAMkB,KAAKtB,EAAMO,EAC3B,OAAOR,GAASuB,KAAKtB,EAAMqB,KAGX,kBAAbf,KACPN,EAAKM,UAAYA,IAGzBJ,MAAO,SAAUe,GACb,MAAOhB,MAAKM,KAAKU,GAAQM,QAE7BlB,OAAQ,SAAUE,GACd,MAAOA,GAAOA,EAAKF,OAAS,MAEhCC,UAAW,SAAUC,GACjB,MAAOA,IAEXiB,SAAU,SAAUC,GAChB,GAAiBC,GAAYC,EAAMC,EAAUC,EAAUC,EAAQC,EAAgFC,EAA3If,KAAsEgB,EAAaR,EAAQQ,WAAYC,EAAiBD,EAAWV,MACvI,KAAKS,EAAM,EAAGA,EAAME,EAAgBF,IAChCD,EAAYE,EAAWD,GACvBf,EAAO,IAAMc,EAAUF,UAAYE,EAAUI,SAEjD,KAAKR,EAAOF,EAAQW,WAAYT,EAAMA,EAAOA,EAAKU,YAC9CT,EAAWD,EAAKC,SACC,IAAbA,GAA+B,IAAbA,EAClBX,EAAO,SAAWU,EAAKQ,UACH,IAAbP,IACPF,EAAazB,KAAKuB,SAASG,GAC3BE,EAAWF,EAAKE,SAChBC,EAASb,EAAOY,GACZ3C,EAAQ4C,GACRA,EAAOQ,KAAKZ,GAEZI,EADOA,IAAW/C,GAEd+C,EACAJ,GAGKA,EAEbT,EAAOY,GAAYC,EAG3B,OAAOb,IAEXC,SAAU,SAAUN,EAAO2B,GAEvB,IADA,GAAqCT,GAAQb,EAAQM,EAAQiB,EAAoBR,EAA7ES,EAAUF,EAAWG,MAAM,KACxBZ,EAASW,EAAQE,SAEpB,GADA/B,EAAQA,EAAMkB,GACV5C,EAAQ0B,GAAQ,CAGhB,IAFAK,KACAsB,EAAaE,EAAQG,KAAK,KACrBZ,EAAM,EAAGT,EAASX,EAAMW,OAAQS,EAAMT,EAAQS,IAC/CQ,EAAqBvC,KAAKiB,SAASN,EAAMoB,GAAMO,GAC/CC,EAAqBtD,EAAQsD,GAAsBA,GAAsBA,GACzEvB,EAAOqB,KAAKO,MAAM5B,EAAQuB,EAE9B,OAAOvB,GAGf,MAAOL,IAEXR,MAAO,SAAUiB,GACb,GAAIyB,GAAiBC,EAAM9B,IAI3B,OAHA6B,GAAkBzB,EAAIyB,iBAAmBhE,EAAEkE,SAAS3B,GAAKyB,gBACzDC,EAAO9C,KAAKuB,SAASsB,GACrB7B,EAAO6B,EAAgBjB,UAAYkB,EAC5B9B,GAEXH,cAAe,SAAUgB,EAAQmB,GAC7B,MAAKnB,IAGLA,EAASA,EAAOoB,QAAQ,MAAO,IAAIA,QAAQ,MAAO,KAC9CpB,EAAOqB,QAAQ,MAAQ,EAChBrB,EAAOoB,QAAQ,WAAYD,EAAM,KAAO,UAE/CnB,EAAOqB,QAAQ,WAAa,EACrBrB,EAAOoB,QAAQ,gBAAiBD,EAAM,QAAU,aAEpDnB,GATI,IAWfvC,OAAQ,SAAUuC,GACd,MAAOvC,GAAOU,KAAKa,cAAcgB,IAAS,KAGlDhD,GAAEQ,QAAO,EAAMN,EAAMuB,MACjBd,cAAeA,EACf2D,SAAW/B,IAAK5B,MAEtBR,OAAOD,MAAMqE,QACRpE,OAAOD,OACE,kBAAVH,SAAwBA,OAAOyE,IAAMzE,OAAS,SAAU0E,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.data.xml.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.data.xml', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'data.xml',\n        name: 'XML',\n        category: 'framework',\n        depends: ['core'],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, isArray = $.isArray, isPlainObject = $.isPlainObject, map = $.map, each = $.each, extend = $.extend, getter = kendo.getter, Class = kendo.Class;\n        var XmlDataReader = Class.extend({\n            init: function (options) {\n                var that = this, total = options.total, model = options.model, parse = options.parse, errors = options.errors, serialize = options.serialize, data = options.data;\n                if (model) {\n                    if (isPlainObject(model)) {\n                        var base = options.modelBase || kendo.data.Model;\n                        if (model.fields) {\n                            each(model.fields, function (field, value) {\n                                if (isPlainObject(value) && value.field) {\n                                    if (!$.isFunction(value.field)) {\n                                        value = extend(value, { field: that.getter(value.field) });\n                                    }\n                                } else {\n                                    value = { field: that.getter(value) };\n                                }\n                                model.fields[field] = value;\n                            });\n                        }\n                        var id = model.id;\n                        if (id) {\n                            var idField = {};\n                            idField[that.xpathToMember(id, true)] = { field: that.getter(id) };\n                            model.fields = extend(idField, model.fields);\n                            model.id = that.xpathToMember(id);\n                        }\n                        model = base.define(model);\n                    }\n                    that.model = model;\n                }\n                if (total) {\n                    if (typeof total == 'string') {\n                        total = that.getter(total);\n                        that.total = function (data) {\n                            return parseInt(total(data), 10);\n                        };\n                    } else if (typeof total == 'function') {\n                        that.total = total;\n                    }\n                }\n                if (errors) {\n                    if (typeof errors == 'string') {\n                        errors = that.getter(errors);\n                        that.errors = function (data) {\n                            return errors(data) || null;\n                        };\n                    } else if (typeof errors == 'function') {\n                        that.errors = errors;\n                    }\n                }\n                if (data) {\n                    if (typeof data == 'string') {\n                        data = that.xpathToMember(data);\n                        that.data = function (value) {\n                            var result = that.evaluate(value, data), modelInstance;\n                            result = isArray(result) ? result : [result];\n                            if (that.model && model.fields) {\n                                modelInstance = new that.model();\n                                return map(result, function (value) {\n                                    if (value) {\n                                        var record = {}, field;\n                                        for (field in model.fields) {\n                                            record[field] = modelInstance._parse(field, model.fields[field].field(value));\n                                        }\n                                        return record;\n                                    }\n                                });\n                            }\n                            return result;\n                        };\n                    } else if (typeof data == 'function') {\n                        that.data = data;\n                    }\n                }\n                if (typeof parse == 'function') {\n                    var xmlParse = that.parse;\n                    that.parse = function (data) {\n                        var xml = parse.call(that, data);\n                        return xmlParse.call(that, xml);\n                    };\n                }\n                if (typeof serialize == 'function') {\n                    that.serialize = serialize;\n                }\n            },\n            total: function (result) {\n                return this.data(result).length;\n            },\n            errors: function (data) {\n                return data ? data.errors : null;\n            },\n            serialize: function (data) {\n                return data;\n            },\n            parseDOM: function (element) {\n                var result = {}, parsedNode, node, nodeType, nodeName, member, attribute, attributes = element.attributes, attributeCount = attributes.length, idx;\n                for (idx = 0; idx < attributeCount; idx++) {\n                    attribute = attributes[idx];\n                    result['@' + attribute.nodeName] = attribute.nodeValue;\n                }\n                for (node = element.firstChild; node; node = node.nextSibling) {\n                    nodeType = node.nodeType;\n                    if (nodeType === 3 || nodeType === 4) {\n                        result['#text'] = node.nodeValue;\n                    } else if (nodeType === 1) {\n                        parsedNode = this.parseDOM(node);\n                        nodeName = node.nodeName;\n                        member = result[nodeName];\n                        if (isArray(member)) {\n                            member.push(parsedNode);\n                        } else if (member !== undefined) {\n                            member = [\n                                member,\n                                parsedNode\n                            ];\n                        } else {\n                            member = parsedNode;\n                        }\n                        result[nodeName] = member;\n                    }\n                }\n                return result;\n            },\n            evaluate: function (value, expression) {\n                var members = expression.split('.'), member, result, length, intermediateResult, idx;\n                while (member = members.shift()) {\n                    value = value[member];\n                    if (isArray(value)) {\n                        result = [];\n                        expression = members.join('.');\n                        for (idx = 0, length = value.length; idx < length; idx++) {\n                            intermediateResult = this.evaluate(value[idx], expression);\n                            intermediateResult = isArray(intermediateResult) ? intermediateResult : [intermediateResult];\n                            result.push.apply(result, intermediateResult);\n                        }\n                        return result;\n                    }\n                }\n                return value;\n            },\n            parse: function (xml) {\n                var documentElement, tree, result = {};\n                documentElement = xml.documentElement || $.parseXML(xml).documentElement;\n                tree = this.parseDOM(documentElement);\n                result[documentElement.nodeName] = tree;\n                return result;\n            },\n            xpathToMember: function (member, raw) {\n                if (!member) {\n                    return '';\n                }\n                member = member.replace(/^\\//, '').replace(/\\//g, '.');\n                if (member.indexOf('@') >= 0) {\n                    return member.replace(/\\.?(@.*)/, raw ? '$1' : '[\"$1\"]');\n                }\n                if (member.indexOf('text()') >= 0) {\n                    return member.replace(/(\\.?text\\(\\))/, raw ? '#text' : '[\"#text\"]');\n                }\n                return member;\n            },\n            getter: function (member) {\n                return getter(this.xpathToMember(member), true);\n            }\n        });\n        $.extend(true, kendo.data, {\n            XmlDataReader: XmlDataReader,\n            readers: { xml: XmlDataReader }\n        });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}