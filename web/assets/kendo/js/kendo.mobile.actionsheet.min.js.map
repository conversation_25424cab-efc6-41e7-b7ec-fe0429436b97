{"version": 3, "sources": ["kendo.mobile.actionsheet.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "support", "ui", "mobile", "<PERSON><PERSON>", "Popup", "Widget", "OPEN", "CLOSE", "COMMAND", "BUTTONS", "CONTEXT_DATA", "WRAP", "cancelTemplate", "template", "ActionSheet", "extend", "init", "element", "options", "ShimClass", "tablet", "type", "that", "this", "os", "mobileOS", "fn", "call", "addClass", "append", "cancel", "wrap", "on", "preventDefault", "view", "bind", "destroy", "wrapper", "parent", "shim", "modal", "ios", "majorVersion", "className", "popup", "_closeProxy", "proxy", "_shimHideProxy", "onResize", "notify", "events", "name", "height", "open", "target", "context", "show", "close", "hide", "openFor", "data", "trigger", "unbindResize", "_click", "e", "currentTarget", "action", "actionData", "$angular", "isDefaultPrevented", "injector", "get", "getter", "_close", "_shimHide", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,4BACH,uBACA,qBACDD,IACL,WA0HE,MA/GC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAUF,EAAME,QAASC,EAAKH,EAAMI,OAAOD,GAAIE,EAAOF,EAAGE,KAAMC,EAAQH,EAAGG,MAAOC,EAASJ,EAAGI,OAAQC,EAAO,OAAQC,EAAQ,QAASC,EAAU,UAAWC,EAAU,OAAQC,EAAe,qBAAsBC,EAAO,yCAA0CC,EAAiBd,EAAMe,SAAS,sEACxTC,EAAcT,EAAOU,QACrBC,KAAM,SAAUC,EAASC,GACrB,GAAiBC,GAAWC,EAAQC,EAAhCC,EAAOC,KAA+BC,EAAKxB,EAAQyB,QACvDpB,GAAOqB,GAAGV,KAAKW,KAAKL,EAAML,EAASC,GACnCA,EAAUI,EAAKJ,QACfG,EAAOH,EAAQG,KACfJ,EAAUK,EAAKL,QAEXG,EADS,SAATC,EACSG,GAAMA,EAAGJ,OAEA,WAATC,EAEbF,EAAYC,EAAShB,EAAQD,EACzBe,EAAQN,iBACRA,EAAiBd,EAAMe,SAASK,EAAQN,iBAE5CK,EAAQW,SAAS,kBAAkBC,OAAOjB,GAAiBkB,OAAQR,EAAKJ,QAAQY,UAAWC,KAAKpB,GAAMqB,GAAG,KAAMvB,EAAS,UAAUuB,GAAG,QAASvB,EAASX,EAAMmC,gBAC7JX,EAAKY,OAAOC,KAAK,UAAW,WACxBb,EAAKc,YAETd,EAAKe,QAAUpB,EAAQqB,SAASV,SAASP,EAAO,mBAAqBA,EAAO,IAC5EC,EAAKiB,KAAO,GAAIpB,GAAUG,EAAKe,QAASzC,EAAEmB,QACtCyB,MAAOhB,EAAGiB,KAAOjB,EAAGkB,aAAe,EACnCC,UAAW,uBACZrB,EAAKJ,QAAQ0B,QAChBtB,EAAKuB,YAAcjD,EAAEkD,MAAMxB,EAAM,UACjCA,EAAKyB,eAAiBnD,EAAEkD,MAAMxB,EAAM,aACpCA,EAAKiB,KAAKJ,KAAK,OAAQb,EAAKyB,gBACxB3B,GACAtB,EAAMkD,SAAS1B,EAAKuB,aAExB/C,EAAMmD,OAAO3B,EAAMrB,IAEvBiD,QACI5C,EACAC,EACAC,GAEJU,SACIiC,KAAM,cACNrB,OAAQ,SACRT,KAAM,OACNuB,OAASQ,OAAQ,SAErBC,KAAM,SAAUC,EAAQC,GACpB,GAAIjC,GAAOC,IACXD,GAAKgC,OAAS1D,EAAE0D,GAChBhC,EAAKiC,QAAUA,EACfjC,EAAKiB,KAAKiB,KAAKF,IAEnBG,MAAO,WACHlC,KAAKgC,QAAUhC,KAAK+B,OAAS,KAC7B/B,KAAKgB,KAAKmB,QAEdC,QAAS,SAAUL,GACf,GAAIhC,GAAOC,KAAMgC,EAAUD,EAAOM,KAAKlD,EACvCY,GAAK+B,KAAKC,EAAQC,GAClBjC,EAAKuC,QAAQvD,GACTgD,OAAQA,EACRC,QAASA,KAGjBnB,QAAS,WACL/B,EAAOqB,GAAGU,QAAQT,KAAKJ,MACvBzB,EAAMgE,aAAavC,KAAKsB,aACxBtB,KAAKgB,KAAKH,WAEd2B,OAAQ,SAAUC,GAAV,GAIAC,GACAC,EAEIC,EAGGC,CATPJ,GAAEK,uBAGFJ,EAAgBrE,EAAEoE,EAAEC,eACpBC,EAASD,EAAcL,KAAK,UAC5BM,IACIC,GACIb,OAAQ/B,KAAK+B,OACbC,QAAShC,KAAKgC,SACfa,EAAW7C,KAAKL,QAAQkD,SAC3BA,EACA7C,KAAKN,QAAQqD,WAAWC,IAAI,UAAUL,GAAQE,EAAS,IAAID,GAE3DrE,EAAM0E,OAAON,GAAQnE,QAAQoE,IAGrC5C,KAAKsC,QAAQrD,GACT8C,OAAQ/B,KAAK+B,OACbC,QAAShC,KAAKgC,QACdU,cAAeA,IAEnBD,EAAE/B,iBACFV,KAAKkD,WAETC,UAAW,SAAUV,GACZzC,KAAKsC,QAAQtD,GAGdyD,EAAE/B,iBAFFV,KAAKgC,QAAUhC,KAAK+B,OAAS,MAKrCmB,OAAQ,SAAUT,GACTzC,KAAKsC,QAAQtD,GAGdyD,EAAE/B,iBAFFV,KAAKkC,UAMjBxD,GAAG0E,OAAO7D,IACZf,OAAOD,MAAM8E,QACR7E,OAAOD,OACE,kBAAVH,SAAwBA,OAAOkF,IAAMlF,OAAS,SAAUmF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.actionsheet.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.actionsheet', [\n        'kendo.mobile.popover',\n        'kendo.mobile.shim'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.actionsheet',\n        name: 'ActionSheet',\n        category: 'mobile',\n        description: 'The mobile ActionSheet widget displays a set of choices related to a task the user initiates.',\n        depends: [\n            'mobile.popover',\n            'mobile.shim'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, support = kendo.support, ui = kendo.mobile.ui, Shim = ui.Shim, Popup = ui.Popup, Widget = ui.Widget, OPEN = 'open', CLOSE = 'close', COMMAND = 'command', BUTTONS = 'li>a', CONTEXT_DATA = 'actionsheetContext', WRAP = '<div class=\"km-actionsheet-wrapper\" />', cancelTemplate = kendo.template('<li class=\"km-actionsheet-cancel\"><a href=\"\\\\#\">#:cancel#</a></li>');\n        var ActionSheet = Widget.extend({\n            init: function (element, options) {\n                var that = this, ShimClass, tablet, type, os = support.mobileOS;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                type = options.type;\n                element = that.element;\n                if (type === 'auto') {\n                    tablet = os && os.tablet;\n                } else {\n                    tablet = type === 'tablet';\n                }\n                ShimClass = tablet ? Popup : Shim;\n                if (options.cancelTemplate) {\n                    cancelTemplate = kendo.template(options.cancelTemplate);\n                }\n                element.addClass('km-actionsheet').append(cancelTemplate({ cancel: that.options.cancel })).wrap(WRAP).on('up', BUTTONS, '_click').on('click', BUTTONS, kendo.preventDefault);\n                that.view().bind('destroy', function () {\n                    that.destroy();\n                });\n                that.wrapper = element.parent().addClass(type ? ' km-actionsheet-' + type : '');\n                that.shim = new ShimClass(that.wrapper, $.extend({\n                    modal: os.ios && os.majorVersion < 7,\n                    className: 'km-actionsheet-root'\n                }, that.options.popup));\n                that._closeProxy = $.proxy(that, '_close');\n                that._shimHideProxy = $.proxy(that, '_shimHide');\n                that.shim.bind('hide', that._shimHideProxy);\n                if (tablet) {\n                    kendo.onResize(that._closeProxy);\n                }\n                kendo.notify(that, ui);\n            },\n            events: [\n                OPEN,\n                CLOSE,\n                COMMAND\n            ],\n            options: {\n                name: 'ActionSheet',\n                cancel: 'Cancel',\n                type: 'auto',\n                popup: { height: 'auto' }\n            },\n            open: function (target, context) {\n                var that = this;\n                that.target = $(target);\n                that.context = context;\n                that.shim.show(target);\n            },\n            close: function () {\n                this.context = this.target = null;\n                this.shim.hide();\n            },\n            openFor: function (target) {\n                var that = this, context = target.data(CONTEXT_DATA);\n                that.open(target, context);\n                that.trigger(OPEN, {\n                    target: target,\n                    context: context\n                });\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                kendo.unbindResize(this._closeProxy);\n                this.shim.destroy();\n            },\n            _click: function (e) {\n                if (e.isDefaultPrevented()) {\n                    return;\n                }\n                var currentTarget = $(e.currentTarget);\n                var action = currentTarget.data('action');\n                if (action) {\n                    var actionData = {\n                            target: this.target,\n                            context: this.context\n                        }, $angular = this.options.$angular;\n                    if ($angular) {\n                        this.element.injector().get('$parse')(action)($angular[0])(actionData);\n                    } else {\n                        kendo.getter(action)(window)(actionData);\n                    }\n                }\n                this.trigger(COMMAND, {\n                    target: this.target,\n                    context: this.context,\n                    currentTarget: currentTarget\n                });\n                e.preventDefault();\n                this._close();\n            },\n            _shimHide: function (e) {\n                if (!this.trigger(CLOSE)) {\n                    this.context = this.target = null;\n                } else {\n                    e.preventDefault();\n                }\n            },\n            _close: function (e) {\n                if (!this.trigger(CLOSE)) {\n                    this.close();\n                } else {\n                    e.preventDefault();\n                }\n            }\n        });\n        ui.plugin(ActionSheet);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}