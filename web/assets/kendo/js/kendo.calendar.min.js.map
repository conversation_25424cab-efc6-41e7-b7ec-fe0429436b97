{"version": 3, "sources": ["kendo.calendar.js"], "names": ["f", "define", "$", "undefined", "title", "date", "min", "max", "modular", "end", "start", "getFullYear", "minYear", "maxYear", "view", "options", "data", "idx", "setter", "build", "weekNumberBuild", "length", "cells", "isWeekColumnVisible", "cellsPerRow", "perRow", "otherMonth", "lastDayOfMonth", "weekNumber", "weekNumberTemplate", "content", "cellTemplate", "empty", "emptyCellTemplate", "otherMonthTemplate", "otherMonthCellTemplate", "html", "createDate", "getMonth", "getDate", "adjustDST", "disableDates", "cssClass", "indexOf", "OTHERMONTH", "isInRange", "compare", "date1", "date2", "modifier", "year1", "result", "get<PERSON><PERSON>y", "today", "DATE", "restrictValue", "value", "shiftArray", "array", "slice", "concat", "setDate", "multiplier", "setFullYear", "daysBetweenTwoDates", "startDate", "endDate", "temp", "fromDateUTC", "endDateUTC", "calendar", "views", "Date", "UTC", "Math", "ceil", "kendo", "MS_PER_DAY", "addDaysToArray", "numberOfDays", "fromDate", "i", "nextDay", "getTime", "push", "mousetoggle", "e", "disabled", "this", "hasClass", "toggleClass", "HOVER", "MOUSEENTER", "type", "FOCUS", "prevent", "preventDefault", "year", "month", "dateObject", "getCalendarInfo", "culture", "getCulture", "calendars", "standard", "normalize", "depth", "format", "extractFormat", "patterns", "d", "isNaN", "MONTH", "dates", "makeUnselectable", "element", "isIE8", "find", "attr", "addClassToViewContainer", "current<PERSON>iew", "addClass", "inArray", "isEqualDatePart", "value1", "value2", "isEqualMonth", "getDisabledExpr", "option", "isFunction", "isArray", "createDisabledExpr", "noop", "convertDatesArray", "setHours", "body", "callback", "day", "index", "disabledDates", "days", "searchExpression", "toLowerCase", "Function", "isEqualDate", "oldValue", "newValue", "toDateObject", "link", "VALUE", "split", "window", "support", "ui", "Widget", "keys", "parse", "parseDate", "weekInYear", "Selectable", "_extractFormat", "template", "transitions", "transitionOrigin", "css", "useWithBlock", "browser", "msie", "version", "outerWidth", "_outerWidth", "ns", "CLICK", "KEYDOWN_NS", "ID", "MIN", "LEFT", "SLIDE", "CENTURY", "CHANGE", "NAVIGATE", "DISABLED", "FOCUSED", "OTHERMONTHCLASS", "OUTOFRANGE", "TODAY", "CELLSELECTOR", "CELLSELECTORVALID", "WEEKCOLUMNSELECTOR", "SELECTED", "BLUR", "FOCUS_WITH_NS", "touch", "MOUSEENTER_WITH_NS", "MOUSELEAVE", "MS_PER_MINUTE", "PREVARROW", "NEXTARROW", "ARIA_DISABLED", "ARIA_SELECTED", "ARIA_LABEL", "proxy", "extend", "decade", "century", "Calendar", "init", "id", "that", "fn", "call", "wrapper", "url", "unescape", "_templates", "_selectable", "_header", "_viewWrapper", "_footer", "footer", "on", "_move", "currentTarget", "<PERSON><PERSON><PERSON><PERSON>", "href", "_view", "name", "selectable", "_click", "_focusView", "focusOnNav", "_cellID", "_isMultipleSelection", "first", "closest", "last", "_lastActive", "selectRange", "event", "_current", "_value", "_class", "_selectDates", "_index", "_addClassProxy", "_active", "_cell", "todayString", "toDateString", "_cellByDate", "_removeClassProxy", "removeClass", "selectDates", "notify", "animation", "horizontal", "effects", "reverse", "duration", "divisor", "vertical", "messages", "weekColumnHeader", "events", "setOptions", "navigate", "destroy", "_today", "off", "_title", "_destroySelectable", "_table", "current", "focus", "table", "_bindTable", "trigger", "_option", "navigateToPast", "_navigate", "navigateToFuture", "navigateUp", "navigateDown", "from", "old", "currentValue", "future", "to", "replace", "_oldTable", "kendoStop", "_changeView", "_animate", "_focus", "_visualizeSelectedDatesInView", "validSelectedDates", "datesUnique", "map", "filter", "position", "time", "grep", "_validateValue", "selectedDates", "each", "clear", "_selectElement", "selectableOptions", "parseOptions", "multiple", "aria", "inputSelectors", "change", "_onSelect", "relatedTarget", "_onRelatedTarget", "target", "is", "eventArgs", "ctrl<PERSON>ey", "metaKey", "_toggleSelection", "_cellsBySelector", "_deselect", "_addSelectedCellsToArray", "shift<PERSON>ey", "_rangeSelection", "currentCell", "toDateCell", "daysDifference", "toDate", "selector", "item", "currentDateIndex", "Number", "splice", "_dateInView", "firstDateInView", "lastDateInView", "_isNavigatable", "cellIndex", "cell", "isDisabled", "method", "focusedDate", "key", "keyCode", "isRtl", "RIGHT", "UP", "DOWN", "SPACEBAR", "HOME", "END", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "ENTER", "_keyboardToggleSelection", "_nextNavigatable", "_keyboardRangeSelection", "PAGEUP", "PAGEDOWN", "_unselect", "navigatableDate", "active", "viewWrapper", "children", "parent", "remove", "append", "insertAfter", "_horizontal", "viewWidth", "add", "width", "wrap", "float", "margin-left", "complete", "unwrap", "kendoAnimate", "_vertical", "insertBefore", "left", "parseInt", "top", "height", "className", "disabledDate", "removeAttribute", "appendTo", "show", "toString", "_toggle", "hide", "links", "eq", "arrow", "firstDayCurrentMonth", "has", "isBigger", "toggle", "isTodayDisabled", "_todayClick", "footerTemplate", "plugin", "firstDayOfMonth", "firstVisibleDay", "calendarInfo", "firstDay", "getDay", "setTime", "tzOffsetBefore", "getTimezoneOffset", "resultDATE", "tzOffsetDiff", "months", "names", "navigateUrl", "showHeader", "hasUrl", "currentCalendar", "firstDayIdx", "shortNames", "namesShort", "linkClass", "dateString", "join", "currentDate", "timeOffset", "abs", "month1", "month2", "year2", "hours", "namesAbbr", "setMonth", "viewsEnum", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,kBACH,aACA,oBACDD,IACL,WAm2CE,MAx1CC,UAAUE,EAAGC,GA+nCV,QAASC,GAAMC,EAAMC,EAAKC,EAAKC,GAC3B,GAA0FC,GAAtFC,EAAQL,EAAKM,cAAeC,EAAUN,EAAIK,cAAeE,EAAUN,EAAII,aAS3E,OARAD,IAAgBA,EAAQF,EACxBC,EAAMC,GAASF,EAAU,GACrBE,EAAQE,IACRF,EAAQE,GAERH,EAAMI,IACNJ,EAAMI,GAEHH,EAAQ,IAAMD,EAEzB,QAASK,GAAKC,GACV,GAAaC,GAATC,EAAM,EAASX,EAAMS,EAAQT,IAAKC,EAAMQ,EAAQR,IAAKG,EAAQK,EAAQL,MAAOQ,EAASH,EAAQG,OAAQC,EAAQJ,EAAQI,MAAOC,EAAkBL,EAAQK,gBAAiBC,EAASN,EAAQO,OAAS,GAAIC,EAAsBR,EAAQQ,oBAAqBC,EAAcT,EAAQU,QAAU,EAAGC,EAAaX,EAAQW,WAAYC,EAAiBZ,EAAQY,eAAgBC,EAAab,EAAQa,YAAcC,EAAoBC,EAAUf,EAAQe,SAAWC,EAAcC,EAAQjB,EAAQiB,OAASC,EAAmBC,EAAqBnB,EAAQoB,wBAA0BA,EAAwBC,EAAOrB,EAAQqB,MAAQ,sGAI7lB,KAHIb,IACAa,GAAQR,EAAWR,EAAgBV,KAEhCO,EAAMI,EAAQJ,IACbA,EAAM,GAAKA,EAAMO,IAAgB,IACjCY,GAAQ,uBACJb,IACAa,GAAkDR,EAA1CF,IAAehB,IAAUiB,EAA4BP,EAAgBV,IAAuBkB,WAAY,aAGxHlB,EAAQ2B,EAAW3B,EAAMC,cAAeD,EAAM4B,WAAY5B,EAAM6B,WAChEC,EAAU9B,EAAO,GACjBM,EAAOG,EAAMT,EAAOO,EAAKF,EAAQ0B,cACjCL,GAAQpB,EAAK0B,SAASC,QAAQC,UAAuBlB,EAAwCmB,EAAUnC,EAAOJ,EAAKC,GAAOuB,EAAQd,GAAQgB,EAAMhB,GAA9EkB,EAAmBlB,GACrFE,EAAOR,EAAO,EAElB,OAAO0B,GAAO,wBAElB,QAASU,GAAQC,EAAOC,EAAOC,GAC3B,GAAIC,GAAQH,EAAMpC,cAAeD,EAAQsC,EAAMrC,cAAeF,EAAMC,EAAOyC,EAAS,CAUpF,OATIF,KACAvC,GAAgBA,EAAQuC,EACxBxC,EAAMC,EAAQA,EAAQuC,EAAWA,EAAW,GAE5CC,EAAQzC,EACR0C,EAAS,EACFD,EAAQxC,IACfyC,MAEGA,EAEX,QAASC,KACL,GAAIC,GAAQ,GAAIC,GAChB,OAAO,IAAIA,IAAKD,EAAM1C,cAAe0C,EAAMf,WAAYe,EAAMd,WAEjE,QAASgB,GAAcC,EAAOlD,EAAKC,GAC/B,GAAI8C,GAAQD,GASZ,OARII,KACAH,EAAQ,GAAIC,MAAME,KAElBlD,EAAM+C,EACNA,EAAQ,GAAIC,MAAMhD,IACXC,EAAM8C,IACbA,EAAQ,GAAIC,MAAM/C,KAEf8C,EAEX,QAASR,GAAUxC,EAAMC,EAAKC,GAC1B,OAAQF,IAASC,IAAQD,IAASE,EAEtC,QAASkD,GAAWC,EAAOzC,GACvB,MAAOyC,GAAMC,MAAM1C,GAAK2C,OAAOF,EAAMC,MAAM,EAAG1C,IAElD,QAAS4C,GAAQxD,EAAMmD,EAAOM,GAC1BN,EAAQA,YAAiBF,IAAOE,EAAM7C,cAAgBN,EAAKM,cAAgBmD,EAAaN,EACxFnD,EAAK0D,YAAYP,GAErB,QAASQ,GAAoBC,EAAWC,GAAxC,GAEYC,GAIJC,EACAC,CACJ,QAPKH,GAAWD,IACRE,GAAQF,EACZK,EAASC,MAAM,GAAGV,QAAQI,EAAWC,GACrCI,EAASC,MAAM,GAAGV,QAAQK,EAAS,GAAIM,MAAKL,KAE5CC,EAAcI,KAAKC,IAAIR,EAAUtD,cAAesD,EAAU3B,WAAY2B,EAAU1B,WAChF8B,EAAaG,KAAKC,IAAIP,EAAQvD,cAAeuD,EAAQ5B,WAAY4B,EAAQ3B,WACtEmC,KAAKC,OAAON,GAAcD,GAAeQ,EAAMvE,KAAKwE,YAE/D,QAASC,GAAepB,EAAOqB,EAAcC,EAAUvC,GAAvD,GACawC,GACDC,CADR,KAASD,EAAI,EAAGA,GAAKF,EAAcE,IAC3BC,EAAU,GAAIV,MAAKQ,EAASG,WAChCD,EAAU,GAAIV,MAAKU,EAAQrB,QAAQqB,EAAQ3C,UAAY0C,IAClDxC,EAAayC,IACdxB,EAAM0B,KAAKF,GAIvB,QAASG,GAAYC,GACjB,GAAIC,GAAWrF,EAAEsF,MAAMC,SAAS,mBAC3BF,IACDrF,EAAEsF,MAAME,YAAYC,GAAOC,GAAWjD,QAAQ2C,EAAEO,UAAcP,EAAEO,MAAQC,IAGhF,QAASC,GAAQT,GACbA,EAAEU,iBAEN,QAAS3D,GAAW4D,EAAMC,EAAO7F,GAC7B,GAAI8F,GAAa,GAAI7C,IAAK2C,EAAMC,EAAO7F,EAEvC,OADA8F,GAAWpC,YAAYkC,EAAMC,EAAO7F,GAC7B8F,EAEX,QAASC,GAAgBC,GACrB,MAAOC,GAAWD,GAASE,UAAUC,SAEzC,QAASC,GAAU1F,GACf,GAAIL,GAAQ6D,GAAMxD,EAAQL,OAAQgG,EAAQnC,GAAMxD,EAAQ2F,OAAQL,EAAUC,EAAWvF,EAAQsF,QAC7FtF,GAAQ4F,OAASC,EAAc7F,EAAQ4F,QAAUN,EAAQE,UAAUC,SAASK,SAASC,GACjFC,MAAMrG,KACNA,EAAQ,EACRK,EAAQL,MAAQsG,KAEhBN,IAAUvG,GAAauG,EAAQhG,KAC/BK,EAAQ2F,MAAQM,IAEE,OAAlBjG,EAAQkG,QACRlG,EAAQkG,UAGhB,QAASC,GAAiBC,GAClBC,GACAD,EAAQE,KAAK,KAAKC,KAAK,eAAgB,MAG/C,QAASC,GAAwBJ,EAASK,GACtCL,EAAQM,SAAS,KAAOD,GAE5B,QAASE,GAAQrH,EAAM4G,GACnB,IAAK,GAAIhC,GAAI,EAAG5D,EAAS4F,EAAM5F,OAAQ4D,EAAI5D,EAAQ4D,IAC/C,GAAI5E,KAAU4G,EAAMhC,GAChB,OAAO,CAGf,QAAO,EAEX,QAAS0C,GAAgBC,EAAQC,GAC7B,QAAID,IACOA,EAAOjH,gBAAkBkH,EAAOlH,eAAiBiH,EAAOtF,aAAeuF,EAAOvF,YAAcsF,EAAOrF,YAAcsF,EAAOtF,WAIvI,QAASuF,GAAaF,EAAQC,GAC1B,QAAID,IACOA,EAAOjH,gBAAkBkH,EAAOlH,eAAiBiH,EAAOtF,aAAeuF,EAAOvF,YAI7F,QAASyF,GAAgBC,GACrB,MAAIpD,GAAMqD,WAAWD,GACVA,EAEP9H,EAAEgI,QAAQF,GACHG,EAAmBH,GAEvB9H,EAAEkI,KAEb,QAASC,GAAkBpB,GAA3B,GAEahC,GADL9B,IACJ,KAAS8B,EAAI,EAAGA,EAAIgC,EAAM5F,OAAQ4D,IAC9B9B,EAAOiC,KAAK6B,EAAMhC,GAAGqD,SAAS,EAAG,EAAG,EAAG,GAE3C,OAAOnF,GAEX,QAASgF,GAAmBlB,GAA5B,GACQsB,GAAMC,EAaGvD,EACDwD,EACAC,EAfQC,KAAoBC,GAChC,KACA,KACA,KACA,KACA,KACA,KACA,MACDC,EAAmB,gDAC1B,IAAI5B,EAAM,YAAc3D,IACpBqF,EAAgBN,EAAkBpB,GAClCsB,EAAO,8EAAgFI,EAAgB,WAAaE,MACjH,CACH,IAAS5D,EAAI,EAAGA,EAAIgC,EAAM5F,OAAQ4D,IAC1BwD,EAAMxB,EAAMhC,GAAGtB,MAAM,EAAG,GAAGmF,cAC3BJ,EAAQxI,EAAEwH,QAAQe,EAAKG,GACvBF,MACAC,EAAcvD,KAAKsD,EAG3BH,GAAO,kEAAoEI,EAAgB,WAAaE,EAG5G,MADAL,GAAeO,SAAS,OAAQR,GAGpC,QAASS,GAAYC,EAAUC,GAK3B,MAJID,aAAoBzE,OAAQ0E,YAAoB1E,QAChDyE,EAAWA,EAAS9D,UACpB+D,EAAWA,EAAS/D,WAEjB8D,IAAaC,EAExB,QAASC,GAAaC,GAClB,GAAI5F,GAAQtD,EAAEkJ,GAAM9B,KAAK1C,EAAM0C,KAAK+B,KAAQC,MAAM,IAElD,OADA9F,GAAQnB,EAAWmB,EAAM,GAAIA,EAAM,GAAIA,EAAM,IAv0CpD,GAw1BOc,GAv1BAM,EAAQ2E,OAAO3E,MAAO4E,EAAU5E,EAAM4E,QAASC,EAAK7E,EAAM6E,GAAIC,EAASD,EAAGC,OAAQC,EAAO/E,EAAM+E,KAAMC,EAAQhF,EAAMiF,UAAWrH,EAAYoC,EAAMvE,KAAKmC,UAAWsH,EAAalF,EAAMvE,KAAKyJ,WAAYC,EAAanF,EAAM6E,GAAGM,WAAYnD,EAAgBhC,EAAMoF,eAAgBC,EAAWrF,EAAMqF,SAAU3D,EAAa1B,EAAM0B,WAAY4D,EAActF,EAAM4E,QAAQU,YAAaC,EAAmBD,EAAcA,EAAYE,IAAM,mBAAqB,GAAIrI,EAAekI,EAAS,mJAAqJI,cAAc,IAAUpI,EAAoBgI,EAAS,0EAA4EI,cAAc,IAAUlI,EAAyB8H,EAAS,0DAA4DI,cAAc,IAAUxI,EAAqBoI,EAAS,+CAAiDI,cAAc,IAAUC,EAAU1F,EAAM4E,QAAQc,QAASlD,EAAQkD,EAAQC,MAAQD,EAAQE,QAAU,EAAGC,EAAa7F,EAAM8F,YAAaC,EAAK,iBAAkBC,EAAQ,QAAUD,EAAIE,EAAa,UAAYF,EAAIG,EAAK,KAAMC,EAAM,MAAOC,GAAO,OAAQC,GAAQ,UAAWjE,GAAQ,QAASkE,GAAU,UAAWC,GAAS,SAAUC,GAAW,WAAY/B,GAAQ,QAAS1D,GAAQ,gBAAiB0F,GAAW,mBAAoBC,GAAU,kBAAmB1I,GAAa,gBAAiB2I,GAAkB,WAAa3I,GAAa,IAAK4I,GAAa,iBAAkBC,GAAQ,cAAeC,GAAe,kBAAmBC,GAAoB,wBAA0BN,GAAW,UAAYG,GAAa,IAAKI,GAAqB,wBAAyBC,GAAW,mBAAoBC,GAAO,OAASnB,EAAI7E,GAAQ,QAASiG,GAAgBjG,GAAQ6E,EAAI/E,GAAa4D,EAAQwC,MAAQ,aAAe,aAAcC,GAAqBzC,EAAQwC,MAAQ,aAAerB,EAAK,aAAeA,EAAIuB,GAAa1C,EAAQwC,MAAQ,WAAarB,EAAK,aAAeA,EAAK,aAAeA,EAAIwB,GAAgB,IAAOtH,GAAa,MAAUuH,GAAY,aAAcC,GAAY,aAAcC,GAAgB,gBAAiBC,GAAgB,gBAAiBC,GAAa,aAAcC,GAAQvM,EAAEuM,MAAOC,GAASxM,EAAEwM,OAAQpJ,GAAOkB,KAAMD,IAC1tE2B,MAAO,EACPD,KAAM,EACN0G,OAAQ,EACRC,QAAS,GAEbC,GAAWnD,EAAOgD,QAClBI,KAAM,SAAU3F,EAASpG,GACrB,GAAiByC,GAAOuJ,EAApBC,EAAOxH,IACXkE,GAAOuD,GAAGH,KAAKI,KAAKF,EAAM7F,EAASpG,GACnCoG,EAAU6F,EAAKG,QAAUH,EAAK7F,QAC9BpG,EAAUiM,EAAKjM,QACfA,EAAQqM,IAAM7D,OAAO8D,SAAStM,EAAQqM,KACtCJ,EAAKjM,QAAQ0B,aAAesF,EAAgBiF,EAAKjM,QAAQ0B,cACzDuK,EAAKM,aACLN,EAAKO,cACLP,EAAKQ,UACLR,EAAKS,eACLT,EAAKU,QAAQV,EAAKW,QAClBZ,EAAK5F,EAAQM,SAAS,wBAA0B1G,EAAQa,WAAa,iBAAmB,KAAKgM,GAAG3B,GAAqB,IAAMC,GAAYR,GAAcrG,GAAauI,GAAG/C,EAAY,kBAAmB4B,GAAMO,EAAKa,MAAOb,IAAOY,GAAGhD,EAAOc,GAAc,SAAUpG,GAC3P,GAAI8D,GAAO9D,EAAEwI,cAAcC,WAAYvK,EAAQ2F,EAAaC,EACxDA,GAAK4E,KAAKrL,QAAQ,UAClB2C,EAAEU,iBAEiB,SAAnBgH,EAAKiB,MAAMC,MAAmBlB,EAAKjM,QAAQ0B,aAAae,IAGrC,SAAnBwJ,EAAKiB,MAAMC,MAAyC,UAAtBnN,EAAQoN,YACtCnB,EAAKoB,OAAOlO,EAAEkJ,MAEnBwE,GAAG,UAAYjD,EAAI,6BAA8B,WAChDqC,EAAKqB,WAAWrB,EAAKjM,QAAQuN,cAAe,KAC7ChH,KAAKwD,GACJiC,IACAC,EAAKuB,QAAUxB,EAAK,kBAEpBC,EAAKwB,wBAA0BxB,EAAKjM,QAAQa,YAC5CuF,EAAQyG,GAAGhD,EAAOgB,GAAoB,SAAUtG,GAC5C,GAAImJ,GAAQvO,EAAEoF,EAAEwI,eAAeY,QAAQ,MAAMrH,KAAKsE,IAAmB8C,QAASE,EAAO3B,EAAKmB,WAAWS,YAAc1O,EAAEoF,EAAEwI,eAAeY,QAAQ,MAAMrH,KAAKsE,IAAmBgD,MAC5K3B,GAAKmB,WAAWU,YAAYJ,EAAOE,GAAQG,MAAOxJ,IAClD0H,EAAK+B,SAAW/B,EAAKgC,OAAS7F,EAAawF,EAAKtH,KAAK,MACrD2F,EAAKiC,OAAO3D,GAAS0B,EAAK+B,YAGlCtI,EAAU1F,GACVyC,EAAQoG,EAAM7I,EAAQyC,MAAOzC,EAAQ4F,OAAQ5F,EAAQsF,SACrD2G,EAAKkC,gBACLlC,EAAKmC,OAAS5K,GAAMxD,EAAQL,OAC5BsM,EAAK+B,SAAW,GAAIzL,MAAMC,EAAcC,EAAOzC,EAAQT,IAAKS,EAAQR,OACpEyM,EAAKoC,eAAiB,WAElB,GADApC,EAAKqC,SAAU,EACXrC,EAAKsC,MAAM7J,SAAS4F,IAAW,CAC/B,GAAIkE,GAAcvC,EAAKiB,MAAMuB,aAAapM,IAC1C4J,GAAKsC,MAAQtC,EAAKyC,YAAYF,GAElCvC,EAAKsC,MAAM7H,SAAS6D,KAExB0B,EAAK0C,kBAAoB,WACrB1C,EAAKqC,SAAU,EACfrC,EAAKsC,MAAMK,YAAYrE,KAE3B0B,EAAKxJ,MAAMA,GACPwJ,EAAKwB,wBAA0BzN,EAAQ6O,YAAYvO,OAAS,GAC5D2L,EAAK4C,YAAY7O,EAAQ6O,aAE7BhL,EAAMiL,OAAO7C,IAEjBjM,SACImN,KAAM,WACN1K,MAAO,KACPlD,IAAK,GAAIgD,IAAK,KAAM,EAAG,GACvB/C,IAAK,GAAI+C,IAAK,KAAM,GAAI,IACxB2D,SACAxE,aAAc,KACd2K,IAAK,GACL/G,QAAS,GACTsH,OAAQ,GACRhH,OAAQ,GACRT,SACAtE,YAAY,EACZuM,WAAY,SACZyB,eACAlP,MAAOsG,GACPN,MAAOM,GACP8I,WACIC,YACIC,QAAS/E,GACTgF,SAAS,EACTC,SAAU,IACVC,QAAS,GAEbC,UACIJ,QAAS,SACTE,SAAU,MAGlBG,UAAYC,iBAAkB,KAElCC,QACIpF,GACAC,IAEJoF,WAAY,SAAUzP,GAClB,GAAIiM,GAAOxH,IACXiB,GAAU1F,GACVA,EAAQ0B,aAAesF,EAAgBhH,EAAQ0B,cAC/CiH,EAAOuD,GAAGuD,WAAWtD,KAAKF,EAAMjM,GAChCiM,EAAKM,aACLN,EAAKO,cACLP,EAAKS,eACLT,EAAKU,QAAQV,EAAKW,QAClBX,EAAKmC,OAAS5K,GAAMyI,EAAKjM,QAAQL,OACjCsM,EAAKyD,WACD1P,EAAQa,YACRoL,EAAK7F,QAAQM,SAAS,kBAG9BiJ,QAAS,WACL,GAAI1D,GAAOxH,KAAMnC,EAAQ2J,EAAK2D,MAC9B3D,GAAK7F,QAAQyJ,IAAIjG,GACjBqC,EAAK6D,OAAOD,IAAIjG,GAChBqC,EAAKZ,IAAWwE,IAAIjG,GACpBqC,EAAKX,IAAWuE,IAAIjG,GACpBqC,EAAK8D,qBACLlM,EAAM8L,QAAQ1D,EAAK+D,QACf1N,GACAuB,EAAM8L,QAAQrN,EAAMuN,IAAIjG,IAE5BjB,EAAOuD,GAAGyD,QAAQxD,KAAKF,IAE3BgE,QAAS,WACL,MAAOxL,MAAKuJ,UAEhBjO,KAAM,WACF,MAAO0E,MAAKyI,OAEhBgD,MAAO,SAAUC,GACbA,EAAQA,GAAS1L,KAAKuL,OACtBvL,KAAK2L,WAAWD,GAChBA,EAAME,QAAQ,UAElB9Q,IAAK,SAAUkD,GACX,MAAOgC,MAAK6L,QAAQtG,EAAKvH,IAE7BjD,IAAK,SAAUiD,GACX,MAAOgC,MAAK6L,QAAQ,MAAO7N,IAE/B8N,eAAgB,WACZ9L,KAAK+L,UAAUnF,QAEnBoF,iBAAkB,WACdhM,KAAK+L,UAAUlF,GAAW,IAE9BoF,WAAY,WACR,GAAIzE,GAAOxH,KAAMkD,EAAQsE,EAAKmC,MAC1BnC,GAAK6D,OAAOpL,SAAS4F,KAGzB2B,EAAKyD,SAASzD,EAAK+B,WAAYrG,IAEnCgJ,aAAc,SAAUlO,GACpB,GAAIwJ,GAAOxH,KAAMkD,EAAQsE,EAAKmC,OAAQzI,EAAQsG,EAAKjM,QAAQ2F,KAC3D,IAAKlD,EAGL,MAAIkF,KAAUnE,GAAMmC,IACXsC,EAAYgE,EAAKgC,OAAQhC,EAAK+B,WAAc/F,EAAYgE,EAAKgC,OAAQxL,KACtEwJ,EAAKxJ,MAAMA,GACXwJ,EAAKoE,QAAQjG,KAEjB,IAEJ6B,EAAKyD,SAASjN,IAASkF,GAAvBsE,IAEJyD,SAAU,SAAUjN,EAAO1C,GAAjB,GAEFkM,GAAajM,EAAwBsF,EAA2B/F,EAAmBC,EAAmBH,EAAqBuR,EAAoBC,EAAsBC,EAA8BC,EAA0C1B,EAAuD2B,EAAIvK,EAAa1E,EAASyC,EA8C1TyM,CA/CRlR,GAAOiG,MAAMjG,GAAQyD,GAAMzD,GAAQA,EAC/BkM,EAAOxH,KAAMzE,EAAUiM,EAAKjM,QAASsF,EAAUtF,EAAQsF,QAAS/F,EAAMS,EAAQT,IAAKC,EAAMQ,EAAQR,IAAKH,EAAQ4M,EAAK6D,OAAQc,EAAO3E,EAAK+D,OAAQa,EAAM5E,EAAKiF,UAAWJ,EAAe7E,EAAK+B,SAAU+C,EAAStO,IAAUA,GAASqO,EAAczB,EAAWtP,IAASX,GAAaW,IAASkM,EAAKmC,OAC3R3L,IACDA,EAAQqO,GAEZ7E,EAAK+B,SAAWvL,EAAQ,GAAIF,MAAMC,EAAcC,EAAOlD,EAAKC,KACxDO,IAASX,EACTW,EAAOkM,EAAKmC,OAEZnC,EAAKmC,OAASrO,EAElBkM,EAAKiB,MAAQzG,EAAclD,EAASC,MAAMzD,GAC1CgC,EAAU0E,EAAY1E,QACtByC,EAAWzE,IAASyD,GAAM2G,IAC1B9K,EAAMsF,YAAY2F,GAAU9F,GAAU+B,KAAKgF,GAAe/G,GAC1DA,EAAWzC,EAAQU,EAAOlD,GAAO,EACjC0M,EAAKZ,IAAW1G,YAAY2F,GAAU9F,GAAU+B,KAAKgF,GAAe/G,GAChEyH,EAAKZ,IAAW3G,SAAS4F,KACzB2B,EAAKZ,IAAWuD,YAAYhK,IAEhCJ,EAAWzC,EAAQU,EAAOjD,MAC1ByM,EAAKX,IAAW3G,YAAY2F,GAAU9F,GAAU+B,KAAKgF,GAAe/G,GAChEyH,EAAKX,IAAW5G,SAAS4F,KACzB2B,EAAKX,IAAWsD,YAAYhK,IAE5BgM,GAAQC,GAAOA,EAAI5Q,KAAK,eACxB4Q,EAAIM,WAAU,GAAM,GACpBP,EAAKO,WAAU,GAAM,IAEzBlF,EAAKiF,UAAYN,EACZA,IAAQ3E,EAAKmF,cACd/R,EAAMgC,KAAKoF,EAAYpH,MAAMoD,EAAOlD,EAAKC,EAAK8F,IAC9C2G,EAAK+D,OAASgB,EAAK7R,EAAEsH,EAAY1F,QAAQ4K,IACrCpM,IAAKA,EACLC,IAAKA,EACLF,KAAMmD,EACN4J,IAAKrM,EAAQqM,IACbnG,MAAOlG,EAAQkG,MACfN,OAAQ5F,EAAQ4F,OAChBjF,YAAY,EACZ2E,QAASA,EACT5D,aAAc1B,EAAQ0B,aACtBlB,oBAAqBR,EAAQa,WAC7ByO,SAAUtP,EAAQsP,UACnBrD,EAAKxF,EAAY0G,SACpB3G,EAAwBwK,EAAIvK,EAAY0G,MACxChH,EAAiB6K,GACbC,EAAUL,GAAQA,EAAK3Q,KAAK,WAAa+Q,EAAG/Q,KAAK,SACrDgM,EAAKoF,UACDT,KAAMA,EACNI,GAAIA,EACJ3B,SAAUA,EACV0B,OAAQA,EACRE,QAASA,IAEbhF,EAAKoE,QAAQhG,IACb4B,EAAKqF,OAAO7O,IAEZ1C,IAASyD,GAAMxD,EAAQ2F,QAAUsG,EAAKkC,aAAa7N,OAAS,GAC5D2L,EAAKsF,gCAEuB,WAA5BtF,EAAKjM,QAAQoN,YACTrN,IAASyD,GAAMxD,EAAQ2F,QAAUsG,EAAKgC,SAAWhC,EAAKjM,QAAQ0B,aAAauK,EAAKgC,SAChFhC,EAAKiC,OAAO,mBAAoBjC,EAAKgC,QAG7ChC,EAAKiC,OAAO3D,GAAS9H,IAChBmO,GAAQ3E,EAAKsC,OACdtC,EAAKsC,MAAMK,YAAYrE,IAE3B0B,EAAKmF,aAAc,GAEvBvC,YAAa,SAAU3I,GACnB,GAAiBsL,GAAoBC,EAAjCxF,EAAOxH,IACX,OAAIyB,KAAU9G,EACH6M,EAAKkC,cAEhBsD,EAAcvL,EAAMwL,IAAI,SAAUpS,GAC9B,MAAOA,GAAK8E,YACbuN,OAAO,SAAUrS,EAAMsS,EAAUjP,GAChC,MAAOA,GAAMf,QAAQtC,KAAUsS,IAChCF,IAAI,SAAUG,GACb,MAAO,IAAIpO,MAAKoO,KAEpBL,EAAqBrS,EAAE2S,KAAKL,EAAa,SAAUhP,GAC/C,GAAIA,EACA,OAAQwJ,EAAK8F,eAAe,GAAItO,MAAKhB,EAAM8E,SAAS,EAAG,EAAG,EAAG,QAAU9E,IAG/EwJ,EAAKkC,aAAeqD,EAAmBlR,OAAS,EAAIkR,EAA4C,IAAvBC,EAAYnR,OAAemR,EAAcxF,EAAKkC,aACvHlC,EAAKsF,gCAbLE,IAeJhP,MAAO,SAAUA,GAAV,GAOKnD,GANJ2M,EAAOxH,KAAMoM,EAAM5E,EAAKiB,MAAOnN,EAAOkM,EAAKiB,KAC/C,OAAIzK,KAAUrD,EACH6M,EAAKgC,QAEhBxL,EAAQwJ,EAAK8F,eAAetP,GACxBA,GAASwJ,EAAKwB,yBACVnO,EAAO,GAAImE,QAAMhB,IACrBnD,EAAKiI,SAAS,EAAG,EAAG,EAAG,GACvB0E,EAAKkC,cAAgB7O,GACrB2M,EAAKmB,WAAWS,YAAc,MAE9BgD,GAAiB,OAAVpO,GAAkBwJ,EAAKsC,MAC9BtC,EAAKsC,MAAMK,YAAY9D,KAEvBmB,EAAKmF,aAAe3O,GAAS1C,GAA+C,IAAvCA,EAAKgC,QAAQU,EAAOwJ,EAAK+B,UAC9D/B,EAAKyD,SAASjN,IAXlBA,IAcJsP,eAAgB,SAAUtP,GACtB,GAAIwJ,GAAOxH,KAAMzE,EAAUiM,EAAKjM,QAAST,EAAMS,EAAQT,IAAKC,EAAMQ,EAAQR,GAgB1E,OAfc,QAAViD,IACAwJ,EAAK+B,SAAW1M,EAAW2K,EAAK+B,SAASpO,cAAeqM,EAAK+B,SAASzM,WAAY0K,EAAK+B,SAASxM,YAEpGiB,EAAQoG,EAAMpG,EAAOzC,EAAQ4F,OAAQ5F,EAAQsF,SAC/B,OAAV7C,IACAA,EAAQ,GAAIF,MAAME,IACbX,EAAUW,EAAOlD,EAAKC,KACvBiD,EAAQ,OAGF,OAAVA,GAAmBwJ,EAAKjM,QAAQ0B,aAAa,GAAI+B,QAAMhB,KAEhDwJ,EAAKgC,SAAW7O,IACvB6M,EAAKgC,OAAS,MAFdhC,EAAKgC,OAASxL,EAIXwJ,EAAKgC,QAEhBsD,8BAA+B,WAAA,GAOvBhR,GANA0L,EAAOxH,KACPuN,IACJ7S,GAAE8S,KAAKhG,EAAKkC,aAAc,SAAUxG,EAAOlF,GACvCuP,EAAcnO,EAAMN,SAASC,MAAM,GAAGiL,aAAahM,IAAUA,IAEjEwJ,EAAKmB,WAAW8E,QACZ3R,EAAQ0L,EAAK+D,OAAO1J,KAAKqE,IAAcgH,OAAO,SAAUhK,EAAOvB,GAC/D,MAAO4L,GAAc7S,EAAEiH,EAAQ4G,YAAYzG,KAAK1C,EAAM0C,KAAK+B,QAE3D/H,EAAMD,OAAS,GACf2L,EAAKmB,WAAW+E,eAAe5R,GAAO,IAG9CkN,qBAAsB,WAClB,GAAIxB,GAAOxH,IACX,OAAmC,aAA5BwH,EAAKjM,QAAQoN,YAExBZ,YAAa,WAAA,GAKLY,GAAsCgF,EAJtCnG,EAAOxH,IACNwH,GAAKwB,yBAGNL,EAAanB,EAAKjM,QAAQoN,WAAYgF,EAAoBpJ,EAAWqJ,aAAajF,GAClFgF,EAAkBE,UAClBrG,EAAK7F,QAAQG,KAAK,uBAAwB,QAE9C0F,EAAKmB,WAAa,GAAIpE,GAAWiD,EAAKG,SAClCmG,MAAM,EACNC,eAAgB,0IAChBF,SAAUF,EAAkBE,SAC5BX,OAAQ,uBAAyB/G,GACjC6H,OAAQ/G,GAAMO,EAAKyG,UAAWzG,GAC9B0G,cAAejH,GAAMO,EAAK2G,iBAAkB3G,OAGpD2G,iBAAkB,SAAUC,GACxB,GAAI5G,GAAOxH,IACPwH,GAAKmB,WAAWpN,QAAQsS,UAAYO,EAAOC,GAAGlI,MAC9CqB,EAAK+B,SAAW5F,EAAayK,EAAOvM,KAAK,MACzC2F,EAAKiC,OAAO3D,GAASnC,EAAayK,EAAOvM,KAAK,SAGtDoM,UAAW,SAAUnO,GACjB,GAAI0H,GAAOxH,KAAMsO,EAAYxO,EAAG6N,EAAoBpJ,EAAWqJ,aAAapG,EAAKjM,QAAQoN,WACzF,OAAKgF,GAAkBE,UAQnBS,EAAUhF,MAAMiF,SAAWD,EAAUhF,MAAMkF,QACvC9T,EAAE4T,EAAUhF,MAAMhB,eAAe+F,GAAGlI,IACpCqB,EAAKiH,iBAAiB/T,EAAE4T,EAAUhF,MAAMhB,iBAExCd,EAAKkH,iBAAiBvI,IAAmBqH,KAAK,SAAUtK,EAAOvB,GAC3D,GAAI3D,GAAQ2F,EAAajJ,EAAEiH,GAASE,KAAK,KACzC2F,GAAKmH,UAAU3Q,KAEnBwJ,EAAKoH,4BAEFN,EAAUhF,MAAMuF,SACvBrH,EAAKsH,gBAAgBtH,EAAKsC,OACnBpP,EAAE4T,EAAUhF,MAAMhB,eAAe+F,GAAGnI,IAC3CsB,EAAKxJ,MAAM2F,EAAajJ,EAAE4T,EAAUhF,MAAMhB,eAAezG,KAAK,QAE9D2F,EAAKkC,gBACLlC,EAAKoH,4BAETpH,EAAKoE,QAAQjG,IAlBb,IAPQjL,EAAE4T,EAAUhF,MAAMhB,eAAe+F,GAAG,QAAU3T,EAAE4T,EAAUhF,MAAMhB,eAAerI,SAAS,oBACxFvF,EAAE4T,EAAUhF,MAAMhB,eAAerG,SAAS,oBAE1CuF,EAAKoB,OAAOlO,EAAE4T,EAAUhF,MAAMhB,eAAezG,KAAK,MAEtD,IAsBRyJ,mBAAoB,WAChB,GAAI9D,GAAOxH,IACPwH,GAAKmB,aACLnB,EAAKmB,WAAWuC,UAChB1D,EAAKmB,WAAa,OAG1B8F,iBAAkB,SAAUM,GACxB,GAAIvH,GAAOxH,KAAMnF,EAAO8I,EAAaoL,EAAYlN,KAAK,KAClDkN,GAAY9O,SAAS,oBACrBuH,EAAKkC,aAAa9J,KAAK/E,GAEvB2M,EAAKmH,UAAU9T,IAGvBiU,gBAAiB,SAAUE,EAAYvQ,GACnC,GAA+IwQ,GAA3IzH,EAAOxH,KAAMR,EAAWf,GAAakF,EAAa6D,EAAKmB,WAAW3K,QAAQiL,QAAQpH,KAAK,MAAOqN,EAASvL,EAAaqL,EAAWnN,KAAK,KACpI2F,GAAKmB,WAAWS,aAAe5B,EAAKgC,OACpChK,EAAWgI,EAAKmB,WAAWS,YAAczF,EAAa6D,EAAKmB,WAAWS,YAAYvH,KAAK,MAAQ,GAAI7C,QAAMwI,EAAKgC,SAE9GhC,EAAKmB,WAAWS,YAAc3K,EAAY+I,EAAKyC,YAAYzC,EAAKiB,MAAMuB,aAAavL,GAAY0H,IAAqBqB,EAAKmB,WAAW3K,QAAQiL,QAEhJzB,EAAKkC,gBACLuF,EAAiBzQ,EAAoBgB,EAAU0P,GAC/C5P,EAAekI,EAAKkC,aAAcuF,EAAgBzP,EAAUgI,EAAKjM,QAAQ0B,cACzEuK,EAAKsF,iCAET4B,iBAAkB,SAAUS,GACxB,GAAI3H,GAAOxH,IACX,OAAOwH,GAAK+D,OAAO1J,KAAKsN,IAE5BP,yBAA0B,WACtB,GAAIpH,GAAOxH,IACXwH,GAAKmB,WAAW3K,QAAQwP,KAAK,SAAUtK,EAAOkM,GAC1C,GAAIvU,GAAO8I,EAAajJ,EAAE0U,EAAK7G,YAC1Bf,GAAKjM,QAAQ0B,aAAapC,IAC3B2M,EAAKkC,aAAa9J,KAAK/E,MAInC8T,UAAW,SAAU9T,GAAV,GACH2M,GAAOxH,KACPqP,EAAmB7H,EAAKkC,aAAauD,IAAIqC,QAAQnS,SAAStC,EAC1DwU,QACA7H,EAAKkC,aAAa6F,OAAOF,EAAkB,IAGnDG,YAAa,SAAU3U,GACnB,GAAI2M,GAAOxH,KAAMyP,EAAkB9L,EAAa6D,EAAKkH,iBAAiBvI,GAAoB,UAAUtE,KAAK,MAAO6N,EAAiB/L,EAAa6D,EAAKkH,iBAAiBvI,GAAoB,SAAStE,KAAK,KACtM,QAAQhH,IAAS6U,IAAmB7U,IAAS4U,GAEjDE,eAAgB,SAAUtD,EAAcuD,GAAxB,GAGRC,GACA3M,EAHAsE,EAAOxH,KACP8P,EAAatI,EAAKjM,QAAQ0B,YAG9B,OAAuB,SAAnBuK,EAAKiB,MAAMC,MACHoH,EAAWzD,IAEnBnJ,EAAQsE,EAAKG,QAAQ9F,KAAK,IAAMiE,IAAS5C,QACzC2M,EAAOrI,EAAKG,QAAQ9F,KAAK,qBAAuBqB,EAAQ0M,GAAa,KAC9DC,EAAKxB,GAAGlI,MAAuB2J,EAAWzD,KAGzDhE,MAAO,SAAUvI,GAAV,GAC2Q9B,GAAOuC,EAASwP,EAAQpR,EAyC1RqR,EAzCRxI,EAAOxH,KAAMzE,EAAUiM,EAAKjM,QAAS0U,EAAMnQ,EAAEoQ,QAAS5U,EAAOkM,EAAKiB,MAAOvF,EAAQsE,EAAKmC,OAAQ7O,EAAM0M,EAAKjM,QAAQT,IAAKC,EAAMyM,EAAKjM,QAAQR,IAAKsR,EAAe,GAAIvO,MAAM0J,EAAK+B,WAAW4G,EAAQ/Q,EAAM4E,QAAQmM,MAAM3I,EAAKG,SAAUmI,EAAatI,EAAKjM,QAAQ0B,YAyGhQ,OAxGI6C,GAAEsO,SAAW5G,EAAK+D,OAAO,KACzB/D,EAAKqC,SAAU,GAEfoG,GAAO9L,EAAKiM,QAAUD,GAASF,GAAO9L,EAAKqB,MAAQ2K,GACnDnS,EAAQ,EACRuC,GAAU,GACH0P,GAAO9L,EAAKqB,OAAS2K,GAASF,GAAO9L,EAAKiM,OAASD,GAC1DnS,KACAuC,GAAU,GACH0P,GAAO9L,EAAKkM,IACnBrS,EAAkB,IAAVkF,QACR3C,GAAU,GACH0P,GAAO9L,EAAKmM,MACnBtS,EAAkB,IAAVkF,EAAc,EAAI,EAC1B3C,GAAU,GACH0P,GAAO9L,EAAKoM,UACnBvS,EAAQ,EACRuC,GAAU,GACH0P,GAAO9L,EAAKqM,MAAQP,GAAO9L,EAAKsM,MACvCV,EAASE,GAAO9L,EAAKqM,KAAO,QAAU,OACtC7R,EAAOrD,EAAKyU,GAAQ1D,GACpBA,EAAe,GAAIvO,IAAKa,EAAKxD,cAAewD,EAAK7B,WAAY6B,EAAK5B,UAAWsP,EAAaqE,WAAYrE,EAAasE,aAActE,EAAauE,aAAcvE,EAAawE,mBACzKxE,EAAa9N,YAAYI,EAAKxD,eAC9BoF,GAAU,GAEVT,EAAEyO,SAAWzO,EAAE0O,QACXyB,GAAO9L,EAAKiM,QAAUD,GAASF,GAAO9L,EAAKqB,MAAQ2K,GACnD3I,EAAKwE,mBACLzL,GAAU,GACH0P,GAAO9L,EAAKqB,OAAS2K,GAASF,GAAO9L,EAAKiM,OAASD,GAC1D3I,EAAKsE,iBACLvL,GAAU,GACH0P,GAAO9L,EAAKkM,IACnB7I,EAAKyE,aACL1L,GAAU,GACH0P,GAAO9L,EAAKmM,MACnB9I,EAAKoB,OAAOlO,EAAE8M,EAAKsC,MAAM,GAAGvB,aAC5BhI,GAAU,GACF0P,GAAO9L,EAAK2M,OAASb,GAAO9L,EAAKoM,WAAa/I,EAAKwB,yBAC3DxB,EAAKuJ,yBAAyBjR,GAC1BkQ,EAAcrM,EAAajJ,EAAE8M,EAAKsC,MAAM,IAAIjI,KAAK,MACrD2F,EAAKiC,OAAO3D,GAASkK,IAElBlQ,EAAE+O,UACL7Q,IAAUrD,GAAaoV,KAClBA,GACDzU,EAAK+C,QAAQgO,EAAcrO,GAE1BX,EAAUgP,EAAcvR,EAAKC,KAC9BsR,EAAetO,EAAcsO,EAAc9Q,EAAQT,IAAKS,EAAQR,MAEhE+U,EAAWzD,KACXA,EAAe7E,EAAKwJ,iBAAiB3E,EAAcrO,IAEvDlD,EAAM+B,EAAW/B,EAAIK,cAAeL,EAAIgC,WAAYhC,EAAIiC,WACpDyK,EAAKwB,uBACLxB,EAAKyJ,wBAAwBnR,EAAGuM,GAEhC7E,EAAKqF,OAAOR,KAIhB4D,GAAO9L,EAAK2M,OAASb,GAAO9L,EAAKoM,UAChB,SAAbjV,EAAKoN,MAAmBlB,EAAKwB,wBAC7BxB,EAAKxJ,MAAM2F,EAAajJ,EAAE8M,EAAKsC,MAAMjI,KAAK,QAC1C2F,EAAKmB,WAAWS,YAAc1O,EAAE8M,EAAKsC,MAAM,IAC3CtC,EAAKoE,QAAQjG,KAEb6B,EAAKoB,OAAOlO,EAAE8M,EAAKsC,MAAM,GAAGvB,aAEhChI,GAAU,GACH0P,GAAO9L,EAAK+M,QACnB3Q,GAAU,EACViH,EAAKsE,kBACEmE,GAAO9L,EAAKgN,WACnB5Q,GAAU,EACViH,EAAKwE,qBAELhO,GAAS+R,KACJA,GACDzU,EAAK+C,QAAQgO,EAAcrO,GAE/BlD,EAAM+B,EAAW/B,EAAIK,cAAeL,EAAIgC,WAAYhC,EAAIiC,WACnDM,EAAUgP,EAAcvR,EAAKC,KAC9BsR,EAAetO,EAAcsO,EAAc9Q,EAAQT,IAAKS,EAAQR,MAE/DyM,EAAKmI,eAAetD,EAAcrO,KACnCqO,EAAe7E,EAAKwJ,iBAAiB3E,EAAcrO,IAEnDwJ,EAAKwB,uBACAxB,EAAKgI,YAAYnD,IAGlB7E,EAAK+B,SAAW8C,EAChB7E,EAAKiC,OAAO3D,GAASuG,IAHrB7E,EAAKyD,SAASoB,GAMlB7E,EAAKqF,OAAOR,KAIpB9L,GACAT,EAAEU,iBAECgH,EAAK+B,UAEhB0H,wBAAyB,SAAU3H,EAAO+C,GACtC,GAAiB7M,GAAUyP,EAAvBzH,EAAOxH,IACX,OAAKwH,GAAKgI,YAAYnD,IAWtB7E,EAAKmB,WAAWpN,QAAQ2R,OAAS1F,EAAKG,QAAQ9F,KAAK,SAAShG,OAAS,IAAMwQ,GAAgB7E,EAAK+B,SAAW,uBAAyBpD,GAAoB,uBAAyBA,GACjLqB,EAAKiC,OAAO3D,GAASuG,GACrB7E,EAAK+B,SAAW8C,EAChB7E,EAAKsH,gBAAgBtH,EAAKyC,YAAYzC,EAAKiB,MAAMuB,aAAaqC,GAAelG,IAAoBkG,GACjG7E,EAAKoE,QAAQjG,IACb6B,EAAKmB,WAAWpN,QAAQ2R,OAAS,uBAAyB/G,GAL1DqB,IAVIA,EAAKkC,gBACLlK,EAAWgI,EAAKmB,WAAWS,YAAczF,EAAa6D,EAAKmB,WAAWS,YAAYvH,KAAK,MAAQwK,EAC/F4C,EAAiBzQ,EAAoBgB,EAAU,GAAIR,QAAMqN,KACzD/M,EAAekI,EAAKkC,aAAcuF,EAAgBzP,EAAUgI,EAAKjM,QAAQ0B,cACzEuK,EAAKyD,SAASoB,GACd7E,EAAK+B,SAAW8C,EAChB7E,EAAKmB,WAAWS,YAAc5B,EAAKmB,WAAWS,aAAe5B,EAAKyC,YAAYzC,EAAKiB,MAAMuB,aAAaqC,GAAelG,IACrHqB,EAAKoE,QAAQjG,IACb,IASRoL,yBAA0B,SAAUzH,GAChC,GAAI9B,GAAOxH,IACXsJ,GAAMhB,cAAgBd,EAAKsC,MAAM,GACjCtC,EAAKmB,WAAWS,YAAc1O,EAAE8M,EAAKsC,MAAM,IACvCpP,EAAE8M,EAAKsC,MAAM,IAAI7J,SAASoG,KAC1BmB,EAAKmB,WAAWyI,UAAU1W,EAAE8M,EAAKsC,MAAM,KACvCtC,EAAKmB,WAAWiD,QAAQjG,IAAU2D,MAAOA,KAEzC9B,EAAKmB,WAAW3K,MAAMtD,EAAE8M,EAAKsC,MAAM,KAAOR,MAAOA,KAGzD0H,iBAAkB,SAAU3E,EAAcrO,GACtC,GAAIwJ,GAAOxH,KAAMD,GAAW,EAAMzE,EAAOkM,EAAKiB,MAAO3N,EAAM0M,EAAKjM,QAAQT,IAAKC,EAAMyM,EAAKjM,QAAQR,IAAK+U,EAAatI,EAAKjM,QAAQ0B,aAAcoU,EAAkB,GAAIrS,MAAKqN,EAAa1M,UAErL,KADArE,EAAK+C,QAAQgT,GAAkBrT,GACxB+B,GAAU,CAEb,GADAzE,EAAK+C,QAAQgO,EAAcrO,IACtBX,EAAUgP,EAAcvR,EAAKC,GAAM,CACpCsR,EAAegF,CACf,OAEJtR,EAAW+P,EAAWzD,GAE1B,MAAOA,IAEXO,SAAU,SAAUrR,GAAV,GACFiM,GAAOxH,KACPmM,EAAO5Q,EAAQ4Q,KACfI,EAAKhR,EAAQgR,GACb+E,EAAS9J,EAAKqC,QACd0H,EAAc/J,EAAK7F,QAAQ6P,SAAS,mBACnCrF,GAGMA,EAAKsF,SAASjW,KAAK,cAC1B2Q,EAAKf,IAAIjG,GACTgH,EAAKsF,SAAS/E,WAAU,GAAM,GAAMgF,SACpCvF,EAAKuF,SACLH,EAAYI,OAAOpF,GACnB/E,EAAKqB,WAAWyI,KACRnF,EAAKkC,GAAG,aAAe7G,EAAKjM,QAAQ+O,aAAc,GAAS/O,EAAQiR,SAC3ED,EAAGqF,YAAYzF,GACfA,EAAKf,IAAIjG,GAAIuM,SACblK,EAAKqB,WAAWyI,IAEhB9J,EAAKjM,EAAQqP,SAAW,YAAc,eAAeuB,EAAMI,EAAIhR,EAAQ+Q,SAbvEiF,EAAYI,OAAOpF,GACnB/E,EAAKmE,WAAWY,KAexBsF,YAAa,SAAU1F,EAAMI,EAAID,GAC7B,GAAI9E,GAAOxH,KAAMsR,EAAS9J,EAAKqC,QAASU,EAAa/C,EAAKjM,QAAQ+O,UAAUC,WAAYC,EAAUD,EAAWC,QAASsH,EAAY7M,EAAWkH,EACzI3B,IAAWA,EAAQrN,QAAQsI,UAC3B0G,EAAK4F,IAAIxF,GAAI3H,KAAMoN,MAAOF,IAC1B3F,EAAK8F,KAAK,UACVzK,EAAKqB,WAAWyI,EAAQnF,GACxBA,EAAKsF,SAAS7M,KACVuI,SAAU,WACV6E,MAAmB,EAAZF,EACPI,QAAS1M,GACT2M,cAAe7F,EAAS,GAAKwF,IAEjCvF,EAAGD,EAAS,cAAgB,gBAAgBH,GAC5CjF,GAAOqD,GACHC,QAAS/E,GAAQ,KAAO6G,EAAS,QAAU9G,IAC3C4M,SAAU,WACNjG,EAAKf,IAAIjG,GAAIuM,SACblK,EAAKiF,UAAY,KACjBF,EAAG8F,SACH7K,EAAKqB,WAAWyI,MAGxBnF,EAAKsF,SAAS/E,WAAU,GAAM,GAAM4F,aAAa/H,KAGzDgI,UAAW,SAAUpG,EAAMI,GACvB,GAAgHsD,GAAM1C,EAAlH3F,EAAOxH,KAAM4K,EAAWpD,EAAKjM,QAAQ+O,UAAUM,SAAUJ,EAAUI,EAASJ,QAAS8G,EAAS9J,EAAKqC,OACnGW,IAAWA,EAAQrN,QAAQ,cAC3BoP,EAAGiG,aAAarG,GAChBA,EAAKvH,KACDuI,SAAU,WACV6E,MAAOzF,EAAGyF,UAEVrN,IACAkL,EAAOrI,EAAKyC,YAAYzC,EAAKiB,MAAMuB,aAAaxC,EAAK+B,WACrD4D,EAAW0C,EAAK1C,WAChBA,EAAWA,EAASsF,KAAOC,SAAS7C,EAAKmC,QAAU,EAAG,IAAM,OAAc7E,EAASwF,IAAMD,SAAS7C,EAAK+C,SAAW,EAAG,IAAM,MAC3HrG,EAAG3H,IAAID,EAAkBwI,IAE7BhB,EAAKO,WAAU,GAAM,GAAM4F,cACvB9H,QAAS,UACTE,SAAU,IACV0H,SAAU,WACNjG,EAAKf,IAAIjG,GAAIuM,SACblK,EAAKiF,UAAY,KACjBjF,EAAKqB,WAAWyI,MAGxB/E,EAAGG,WAAU,GAAM,GAAM4F,aAAa1H,KAG9CX,YAAa,SAAUjM,EAAOmR,GAC1B,MAAOnP,MAAKuL,OAAO1J,KAAKsN,EAAWA,EAAW,WAAa/R,GAAa,KAAK8P,OAAO,WAChF,MAAOxS,GAAEsF,KAAKuI,YAAYzG,KAAK1C,EAAM0C,KAAK+B,OAAY7F,KAG9DyL,OAAQ,SAAUoJ,EAAWhY,GACzB,GAA8FiY,GAA1FtL,EAAOxH,KAAMuH,EAAKC,EAAKuB,QAAS8G,EAAOrI,EAAKsC,MAAO9L,EAAQwJ,EAAKiB,MAAMuB,aAAanP,EACnFgV,IAAQA,EAAKhU,SACbgU,EAAK,GAAGkD,gBAAgBhM,IACxB8I,EAAK,GAAGkD,gBAAgB/L,IACxB6I,EAAK,GAAGkD,gBAAgBzN,IAExBzK,GAA2B,SAAnB2M,EAAKiB,MAAMC,OACnBoK,EAAetL,EAAKjM,QAAQ0B,aAAapC,IAE7C2M,EAAKkH,iBAAiBlH,EAAKwB,uBAAyB9C,GAAe,WAAa9I,GAAa,KAAK+M,YAAY0I,GAC9GhD,EAAOrI,EAAKyC,YAAYjM,EAAkC,YAA3BwJ,EAAKjM,QAAQoN,WAA2BzC,GAAe,WAAa9I,GAAa,KAAK0E,KAAKiF,IAAe,IACrI8L,IAAc/M,KAAY0B,EAAKqC,SAAWrC,EAAKjM,QAAQuN,cAAe,GAASgK,KAC/ED,EAAY,IAEhBhD,EAAK5N,SAAS4Q,GACVhD,EAAK,KACLrI,EAAKsC,MAAQ+F,GAEbtI,IACAsI,EAAK/N,KAAKwD,EAAIiC,GACdC,EAAK+D,OAAO,GAAGwH,gBAAgB,yBAC/BvL,EAAK+D,OAAOzJ,KAAK,wBAAyByF,KAGlDoE,WAAY,SAAUD,GAClBA,EAAMtD,GAAG7B,GAAevG,KAAK4J,gBAAgBxB,GAAG9B,GAAMtG,KAAKkK,oBAE/DtB,OAAQ,SAAUhF,GACd,GAAI4D,GAAOxH,KAAMzE,EAAUiM,EAAKjM,QAAS8Q,EAAe,GAAIrN,QAAMwI,EAAK+B,WAAWvL,EAAQ2F,EAAaC,EACvG5G,GAAUgB,EAAO,GACM,SAAnBwJ,EAAKiB,MAAMC,MAAmBlB,EAAKjM,QAAQ0B,aAAae,KACxDA,EAAQwJ,EAAKgC,QAEjBhC,EAAKiB,MAAMpK,QAAQgO,EAAcrO,GACjCwJ,EAAK0E,aAAanO,EAAcsO,EAAc9Q,EAAQT,IAAKS,EAAQR,OAEvE8R,OAAQ,SAAU7O,GACd,GAAIwJ,GAAOxH,KAAM1E,EAAOkM,EAAKiB,KACc,KAAvCnN,EAAKgC,QAAQU,EAAOwJ,EAAK+B,UACzB/B,EAAKyD,SAASjN,IAEdwJ,EAAK+B,SAAWvL,EAChBwJ,EAAKiC,OAAO3D,GAAS9H,KAG7B6K,WAAY,SAAUyI,EAAQ5F,GACtB4F,GACAtR,KAAKyL,MAAMC,IAGnBzD,aAAc,WAAA,GACNT,GAAOxH,KACP2B,EAAU6F,EAAK7F,QACf4P,EAAc5P,EAAQ6P,SAAS,mBAC9BD,GAAY,KACbA,EAAc7W,EAAE,mCAAqCkX,YAAYjQ,EAAQE,KAAK,gBAGtFqG,QAAS,SAAUzD,GACf,GAAI+C,GAAOxH,KAAMnC,EAAQD,IAAY+D,EAAU6F,EAAK7F,QAASwG,EAASxG,EAAQE,KAAK,YACnF,OAAK4C,IAKA0D,EAAO,KACRA,EAASzN,EAAE,2EAA2EsY,SAASrR,IAEnG6F,EAAK2D,OAAShD,EAAO8K,OAAOpR,KAAK,WAAWjF,KAAK6H,EAAS5G,IAAQiE,KAAK,QAAS1C,EAAM8T,SAASrV,EAAO,IAAK2J,EAAKjM,QAAQsF,UACxH2G,EAAK2L,UAJL,IAJI3L,EAAK2L,SAAQ,GACbhL,EAAOiL,OACP,IAQRpL,QAAS,WACL,GAAyCqL,GAArC7L,EAAOxH,KAAM2B,EAAU6F,EAAK7F,OAC3BA,GAAQE,KAAK,aAAa,IAC3BF,EAAQ/E,KAAK,6EAAoFoK,GAAa,sNAAkOA,GAAa,qEAEjWqM,EAAQ1R,EAAQE,KAAK,WAAWuG,GAAG3B,GAAqB,IAAMC,GAAa,IAAMH,GAAgB,IAAMD,GAAMzG,GAAauI,GAAG,QAAS,WAClI,OAAO,IAEXZ,EAAK6D,OAASgI,EAAMC,GAAG,GAAGlL,GAAGhD,EAAO,WAChCoC,EAAKqC,QAAUrC,EAAKjM,QAAQuN,cAAe,EAC3CtB,EAAKyE,eAETzE,EAAKZ,IAAayM,EAAMC,GAAG,GAAGlL,GAAGhD,EAAO,WACpCoC,EAAKqC,QAAUrC,EAAKjM,QAAQuN,cAAe,EAC3CtB,EAAKsE,mBAETtE,EAAKX,IAAawM,EAAMC,GAAG,GAAGlL,GAAGhD,EAAO,WACpCoC,EAAKqC,QAAUrC,EAAKjM,QAAQuN,cAAe,EAC3CtB,EAAKwE,sBAGbD,UAAW,SAAUwH,EAAO9V,GAAjB,GAGC+V,GAFJhM,EAAOxH,KAAMkD,EAAQsE,EAAKmC,OAAS,EAAG0C,EAAe,GAAIvO,MAAM0J,EAAK+B,UACpE/B,GAAKwB,yBACDwK,EAAuBhM,EAAK+D,OAAO1J,KAAK,+CAA+C4R,IAAI,WAAWxK,QAC1GoD,EAAe1I,EAAa6P,EAAqB3R,KAAK,MACtD2F,EAAK+B,SAAW,GAAIvK,QAAMqN,KAE9BkH,EAAQ/L,EAAK+L,GACRA,EAAMtT,SAAS4F,MACZ3C,EAAQ,EACRmJ,EAAa9N,YAAY8N,EAAalR,cAAgB,IAAMsC,GAE5DqB,EAASC,MAAMmE,GAAO7E,QAAQgO,EAAc5O,GAEhD+J,EAAKyD,SAASoB,KAGtBR,QAAS,SAAUrJ,EAAQxE,GACvB,GAAsF0V,GAAlFlM,EAAOxH,KAAMzE,EAAUiM,EAAKjM,QAAS8Q,EAAe7E,EAAKgC,QAAUhC,EAAK+B,QAC5E,OAAIvL,KAAUrD,EACHY,EAAQiH,IAEnBxE,EAAQoG,EAAMpG,EAAOzC,EAAQ4F,OAAQ5F,EAAQsF,SACxC7C,IAGLzC,EAAQiH,GAAU,GAAI1E,MAAME,IAExB0V,EADAlR,IAAW+C,EACAvH,EAAQqO,EAERA,EAAerO,GAE1B0V,GAAYpR,EAAa+J,EAAcrO,MACnC0V,IACAlM,EAAKgC,OAAS,MAElBhC,EAAKmF,aAAc,GAElBnF,EAAKmF,cACNnF,EAAKmF,eAAiBpR,EAAQmF,MAAMpE,UAAWf,EAAQmF,MAAMlE,QAEjEgL,EAAKyD,SAASzD,EAAKgC,QACnBhC,EAAK2L,WApBLnV,IAsBJmV,QAAS,SAAUQ,GACf,GAAInM,GAAOxH,KAAMzE,EAAUiM,EAAKjM,QAASqY,EAAkBpM,EAAKjM,QAAQ0B,aAAaW,KAAagG,EAAO4D,EAAK2D,MAC1GwI,KAAWhZ,IACXgZ,EAAStW,EAAUO,IAAYrC,EAAQT,IAAKS,EAAQR,MAEpD6I,IACAA,EAAKwH,IAAIhG,GACLuO,IAAWC,EACXhQ,EAAK3B,SAASgE,IAAOkE,YAAYtE,IAAUuC,GAAGhD,EAAO6B,GAAMO,EAAKqM,YAAarM,IAE7E5D,EAAKuG,YAAYlE,IAAOhE,SAAS4D,IAAUuC,GAAGhD,EAAO7E,KAIjEsT,YAAa,SAAU/T,GACnB,GAAI0H,GAAOxH,KAAMkB,EAAQnC,GAAMyI,EAAKjM,QAAQ2F,OAAQnB,EAAWyH,EAAKjM,QAAQ0B,aAAcY,EAAQD,GAClGkC,GAAEU,iBACET,EAASlC,KAGoC,IAA7C2J,EAAKiB,MAAMnL,QAAQkK,EAAK+B,SAAU1L,IAAgB2J,EAAKmC,QAAUzI,IACjEsG,EAAKmF,aAAc,GAEnBnF,EAAKwB,yBACLxB,EAAKkC,cAAgB7L,GACrB2J,EAAKmB,WAAWS,YAAc,MAElC5B,EAAKgC,OAAS3L,EACd2J,EAAKyD,SAASpN,EAAOqD,GACrBsG,EAAKoE,QAAQjG,MAEjBmC,WAAY,WACR,GAAIN,GAAOxH,KAAMzE,EAAUiM,EAAKjM,QAAS4M,EAAS5M,EAAQ4M,OAAQzH,EAAQnF,EAAQmF,MAAOpE,EAAUoE,EAAMpE,QAASF,EAAasE,EAAMtE,WAAYI,EAAQkE,EAAMlE,MAAOsX,EAAiB,+BAAiCvY,EAAQsF,QAAU,MAC1O2G,GAAK9G,OACDpE,QAASmI,EAAS,2GAA6GrF,EAAM0C,KAAK+B,IAAS,gDAAkDvH,GAAW,iBAAmB,aAAeuI,eAAgBvI,IAClQE,MAAOiI,EAAS,wBAA0BjI,GAAS,UAAY,SAAWqI,eAAgBrI,IAC1FJ,WAAYqI,EAAS,sBAAwBrI,GAAc,wBAA0B,SAAWyI,eAAgBzI,KAEhH+L,GAAUA,KAAW,IACrB2L,EAAiB3L,GAErBX,EAAKW,OAASA,KAAW,EAAQ1D,EAASqP,GAAkBjP,cAAc,IAAW,OAG7FZ,GAAG8P,OAAO1M,IACNvI,GACAkV,gBAAiB,SAAUnZ,GACvB,MAAOgC,GAAWhC,EAAKM,cAAeN,EAAKiC,WAAY,IAE3DmX,gBAAiB,SAAUpZ,EAAMqZ,GAC7BA,EAAeA,GAAgB9U,EAAMyB,UAAU/B,QAC/C,IAAIqV,GAAWD,EAAaC,SAAUF,EAAkB,GAAInW,IAAKjD,EAAKM,cAAeN,EAAKiC,WAAY,EAAGjC,EAAK6V,WAAY7V,EAAK8V,aAAc9V,EAAK+V,aAAc/V,EAAKgW,kBAErK,KADAoD,EAAgB1V,YAAY1D,EAAKM,eAC1B8Y,EAAgBG,UAAYD,GAC/BrV,EAASuV,QAAQJ,KAAsB5U,GAE3C,OAAO4U,IAEXI,QAAS,SAAUxZ,EAAMuS,GACrB,GAAIkH,GAAiBzZ,EAAK0Z,oBAAqBC,EAAa,GAAI1W,IAAKjD,EAAK8E,UAAYyN,GAAOqH,EAAeD,EAAWD,oBAAsBD,CAC7IzZ,GAAKwZ,QAAQG,EAAW7U,UAAY8U,EAAe9N,KAEvD5H,QAEQ2J,KAAMlH,GACN5G,MAAO,SAAUC,EAAMC,EAAKC,EAAK8F,GAC7B,MAAOD,GAAgBC,GAAS6T,OAAOC,MAAM9Z,EAAKiC,YAAc,IAAMjC,EAAKM,eAE/EmB,QAAS,SAAUf,GACf,GAAIiM,GAAOxH,KAAMvE,EAAM,EAAGX,EAAMS,EAAQT,IAAKC,EAAMQ,EAAQR,IAAKF,EAAOU,EAAQV,KAAM4G,EAAQlG,EAAQkG,MAAON,EAAS5F,EAAQ4F,OAAQN,EAAUtF,EAAQsF,QAAS+T,EAAcrZ,EAAQqM,IAAKiN,EAAatZ,EAAQsZ,WAAY3Y,EAAaX,EAAQW,WAAYH,EAAsBR,EAAQQ,oBAAqB+Y,EAASF,GAAenT,EAAM,GAAIsT,EAAkBnU,EAAgBC,GAAUmU,EAAcD,EAAgBZ,SAAU/Q,EAAO2R,EAAgB3R,KAAMuR,EAAQ1W,EAAWmF,EAAKuR,MAAOK,GAAcC,EAAahX,EAAWmF,EAAK8R,WAAYF,GAAc9Z,EAAQ4D,EAASmV,gBAAgBpZ,EAAMka,GAAkBf,EAAkBxM,EAAKyB,MAAMpO,GAAOsB,EAAiBqL,EAAK2B,KAAKtO,GAAOmP,EAAexC,EAAKwC,aAAcnM,EAAQD,IAAYhB,EAAO,iFAAmFoN,EAAa9O,GAAS,IAS50B,KAPI0B,GADAiY,EACQ,mCAAqC7U,KAAKpF,MAAMC,EAAMC,EAAKC,EAAK8F,GAAW,mCAE3E,yBAER9E,IACAa,GAAQ,iCAAmCrB,EAAQsP,SAASC,iBAAmB,SAE5ErP,EAAM,EAAGA,IACZmB,GAAQ,0BAA4B+X,EAAMlZ,GAAO,KAAOwZ,EAAWxZ,GAAO,OAI9E,OAFAuB,GAAUa,EAAO,GACjBA,GAASA,EACFvC,GACHQ,MAAO,GACPG,OAAQ,EACRW,KAAMA,GAAQ,sCACd1B,MAAOA,EACPa,oBAAqBA,EACrBK,WAAYb,EAAQa,WACpBtB,IAAK+B,EAAW/B,EAAIK,cAAeL,EAAIgC,WAAYhC,EAAIiC,WACvDhC,IAAK8B,EAAW9B,EAAII,cAAeJ,EAAI+B,WAAY/B,EAAIgC,WACvDb,WAAYA,EACZI,QAASf,EAAQe,QACjBH,eAAgBA,EAChBK,MAAOjB,EAAQiB,MACfd,OAAQ8L,EAAKnJ,QACbpB,aAAc1B,EAAQ0B,aACtBtB,MAAO,SAAUd,EAAMY,EAAKwB,GACxB,GAAIC,MAAe+F,EAAMpI,EAAKuZ,SAAUe,EAAY,GAAIvN,EAAM,GAiB9D,QAhBI/M,EAAOmZ,GAAmBnZ,EAAOsB,IACjCe,EAAS0C,KAAKxC,IAEdH,EAAapC,IACbqC,EAAS0C,KAAKiG,KAEbhL,IAASgD,GACVX,EAAS0C,KAAK,WAEN,IAARqD,GAAqB,IAARA,GACb/F,EAAS0C,KAAK,aAEdkV,GAAU5S,GAASrH,EAAM4G,KACzBmG,EAAMgN,EAAYpI,QAAQ,MAAOpN,EAAM8T,SAASrY,EAAMsG,EAAQN,IAC9DsU,EAAY,mBAGZta,KAAMA,EACN4G,MAAOA,EACP0D,GAAI/F,EAAM+F,GACVvK,MAAOwE,EAAM8T,SAASrY,EAAM,IAAKgG,GACjC7C,MAAOnD,EAAKkC,UACZqY,WAAYpL,EAAanP,GACzBqC,SAAUA,EAAS,GAAK,WAAaA,EAASmY,KAAK,KAAO,IAAM,GAChEF,UAAWA,EACXvN,IAAKA,IAGbhM,gBAAiB,SAAUf,GACvB,OACIuB,WAAYkI,EAAWzJ,EAAMuE,EAAMyB,UAAU/B,SAASqV,UACtDmB,YAAaza,OAK7BoO,MAAO,SAAUpO,GACb,MAAOiE,GAASkV,gBAAgBnZ,IAEpCsO,KAAM,SAAUtO,GACZ,GAAIsO,GAAOtM,EAAWhC,EAAKM,cAAeN,EAAKiC,WAAa,EAAG,GAAImM,EAAQnK,EAASkV,gBAAgBnZ,GAAO0a,EAAarW,KAAKsW,IAAIrM,EAAKoL,oBAAsBtL,EAAMsL,oBAIlK,OAHIgB,IACApM,EAAKrG,SAASmG,EAAMyH,WAAa6E,EAAa,IAE3CpM,GAEX7L,QAAS,SAAUC,EAAOC,GACtB,GAAIG,GAAQ8X,EAASlY,EAAMT,WAAYY,EAAQH,EAAMpC,cAAeua,EAASlY,EAAMV,WAAY6Y,EAAQnY,EAAMrC,aAQ7G,OANIwC,GADAD,EAAQiY,EACC,EACFjY,EAAQiY,KAGNF,GAAUC,EAAS,EAAID,EAASC,EAAS,MAI1DrX,QAAS,SAAUxD,EAAMmD,GACrB,GAAI4X,GAAQ/a,EAAK6V,UACb1S,aAAiBF,IACjBjD,EAAK0D,YAAYP,EAAM7C,cAAe6C,EAAMlB,WAAYkB,EAAMjB,WAE9D+B,EAASuV,QAAQxZ,EAAMmD,EAAQqB,IAEnCrC,EAAUnC,EAAM+a,IAEpB5L,aAAc,SAAUnP,GACpB,MAAOA,GAAKM,cAAgB,IAAMN,EAAKiC,WAAa,IAAMjC,EAAKkC,aAInE2L,KAAM,OACN9N,MAAO,SAAUC,GACb,MAAOA,GAAKM,eAEhBmB,QAAS,SAAUf,GACf,GAAIsa,GAAYjV,EAAgBrF,EAAQsF,SAAS6T,OAAOmB,UAAW7L,EAAehK,KAAKgK,aAAclP,EAAMS,EAAQT,IAAKC,EAAMQ,EAAQR,IAAK6B,EAAO,EAMlJ,OALIrB,GAAQsZ,aACRjY,GAAQ,gHACRA,GAAQoD,KAAKpF,MAAMW,EAAQV,MAC3B+B,GAAQ,oCAELtB,GACHR,IAAK+B,EAAW/B,EAAIK,cAAeL,EAAIgC,WAAY,GACnD/B,IAAK8B,EAAW9B,EAAII,cAAeJ,EAAI+B,WAAY,GACnD5B,MAAO2B,EAAWtB,EAAQV,KAAKM,cAAe,EAAG,GACjDyB,KAAMA,EACNlB,OAAQsE,KAAK3B,QACb1C,MAAO,SAAUd,GACb,OACImD,MAAO6X,EAAUhb,EAAKiC,YACtBqI,GAAI/F,EAAM+F,GACViQ,WAAYpL,EAAanP,GACzBqC,SAAU,QAK1B+L,MAAO,SAAUpO,GACb,MAAOgC,GAAWhC,EAAKM,cAAe,EAAGN,EAAKkC,YAElDoM,KAAM,SAAUtO,GACZ,MAAOgC,GAAWhC,EAAKM,cAAe,GAAIN,EAAKkC,YAEnDO,QAAS,SAAUC,EAAOC,GACtB,MAAOF,GAAQC,EAAOC,IAE1Ba,QAAS,SAAUxD,EAAMmD,GACrB,GAAI0C,GAAOkV,EAAQ/a,EAAK6V,UACpB1S,aAAiBF,KACjB4C,EAAQ1C,EAAMlB,WACdjC,EAAK0D,YAAYP,EAAM7C,cAAeuF,EAAO7F,EAAKkC,WAC9C2D,IAAU7F,EAAKiC,YACfjC,EAAKwD,QAAQ,KAGjBqC,EAAQ7F,EAAKiC,WAAakB,EAC1BnD,EAAKib,SAASpV,GACVA,EAAQ,KACRA,GAAS,IAETA,EAAQ,GAAK7F,EAAKiC,YAAc4D,GAChC7F,EAAKwD,QAAQ,IAGrBrB,EAAUnC,EAAM+a,IAEpB5L,aAAc,SAAUnP,GACpB,MAAOA,GAAKM,cAAgB,IAAMN,EAAKiC,WAAa,QAIxD4L,KAAM,SACN9N,MAAO,SAAUC,EAAMC,EAAKC,GACxB,MAAOH,GAAMC,EAAMC,EAAKC,EAAK,KAEjCuB,QAAS,SAAUf,GACf,GAAIkF,GAAOlF,EAAQV,KAAKM,cAAe6O,EAAehK,KAAKgK,aAAcpN,EAAO,EAMhF,OALIrB,GAAQsZ,aACRjY,GAAQ,gHACRA,GAAQoD,KAAKpF,MAAMW,EAAQV,KAAMU,EAAQT,IAAKS,EAAQR,KACtD6B,GAAQ,oCAELtB,GACHJ,MAAO2B,EAAW4D,EAAOA,EAAO,GAAK,EAAG,EAAG,GAC3C3F,IAAK+B,EAAWtB,EAAQT,IAAIK,cAAe,EAAG,GAC9CJ,IAAK8B,EAAWtB,EAAQR,IAAII,cAAe,EAAG,GAC9Ce,WAAYX,EAAQW,WACpBU,KAAMA,EACNlB,OAAQsE,KAAK3B,QACb1C,MAAO,SAAUd,EAAMY,GACnB,OACIuC,MAAOnD,EAAKM,cACZgK,GAAI/F,EAAM+F,GACViQ,WAAYpL,EAAanP,GACzBqC,SAAkB,IAARzB,GAAoB,IAAPA,EAAYsK,GAAkB,QAKrEkD,MAAO,SAAUpO,GACb,GAAI4F,GAAO5F,EAAKM,aAChB,OAAO0B,GAAW4D,EAAOA,EAAO,GAAI5F,EAAKiC,WAAYjC,EAAKkC,YAE9DoM,KAAM,SAAUtO,GACZ,GAAI4F,GAAO5F,EAAKM,aAChB,OAAO0B,GAAW4D,EAAOA,EAAO,GAAK,EAAG5F,EAAKiC,WAAYjC,EAAKkC,YAElEO,QAAS,SAAUC,EAAOC,GACtB,MAAOF,GAAQC,EAAOC,EAAO,KAEjCa,QAAS,SAAUxD,EAAMmD,GACrBK,EAAQxD,EAAMmD,EAAO,IAEzBgM,aAAc,SAAUnP,GACpB,MAAOA,GAAKM,cAAgB,UAIhCuN,KAAMhD,GACN9K,MAAO,SAAUC,EAAMC,EAAKC,GACxB,MAAOH,GAAMC,EAAMC,EAAKC,EAAK,MAEjCuB,QAAS,SAAUf,GACf,GAAIkF,GAAOlF,EAAQV,KAAKM,cAAeL,EAAMS,EAAQT,IAAIK,cAAeJ,EAAMQ,EAAQR,IAAII,cAAe6O,EAAehK,KAAKgK,aAAc5O,EAAUN,EAAKO,EAAUN,EAAK6B,EAAO,EAWhL,OAVAxB,IAAoBA,EAAU,GAC9BC,GAAoBA,EAAU,GAC1BA,EAAUD,EAAU,KACpBC,EAAUD,EAAU,GAEpBG,EAAQsZ,aACRjY,GAAQ,gHACRA,GAAQoD,KAAKpF,MAAMW,EAAQV,KAAMU,EAAQT,IAAKS,EAAQR,KACtD6B,GAAQ,oCAELtB,GACHJ,MAAO2B,EAAW4D,EAAOA,EAAO,IAAM,GAAI,EAAG,GAC7C3F,IAAK+B,EAAWzB,EAAS,EAAG,GAC5BL,IAAK8B,EAAWxB,EAAS,EAAG,GAC5Ba,WAAYX,EAAQW,WACpBU,KAAMA,EACNlB,OAAQsE,KAAK3B,QACb1C,MAAO,SAAUd,EAAMY,GACnB,GAAIP,GAAQL,EAAKM,cAAeF,EAAMC,EAAQ,CAO9C,OANIA,GAAQJ,IACRI,EAAQJ,GAERG,EAAMF,IACNE,EAAMF,IAGNoK,GAAI/F,EAAM+F,GACVnH,MAAO9C,EAAQ,MAAQD,EACvBma,WAAYpL,EAAanP,GACzBqC,SAAkB,IAARzB,GAAoB,IAAPA,EAAYsK,GAAkB,QAKrEkD,MAAO,SAAUpO,GACb,GAAI4F,GAAO5F,EAAKM,aAChB,OAAO0B,GAAW4D,EAAOA,EAAO,IAAK5F,EAAKiC,WAAYjC,EAAKkC,YAE/DoM,KAAM,SAAUtO,GACZ,GAAI4F,GAAO5F,EAAKM,aAChB,OAAO0B,GAAW4D,EAAOA,EAAO,IAAM,GAAI5F,EAAKiC,WAAYjC,EAAKkC,YAEpEO,QAAS,SAAUC,EAAOC,GACtB,MAAOF,GAAQC,EAAOC,EAAO,MAEjCa,QAAS,SAAUxD,EAAMmD,GACrBK,EAAQxD,EAAMmD,EAAO,KAEzBgM,aAAc,SAAUnP,GACpB,GAAI4F,GAAO5F,EAAKM,aAChB,OAAOsF,GAAOA,EAAO,GAAK,WAgN1C3B,EAASqD,gBAAkBA,EAC3BrD,EAAS0E,YAAcA,EACvB1E,EAAS4C,iBAAmBA,EAC5B5C,EAASf,cAAgBA,EACzBe,EAASzB,UAAYA,EACrByB,EAASiD,wBAA0BA,EACnCjD,EAASmC,UAAYA,EACrBnC,EAASiX,UAAYhX,GACrBD,EAASiB,SAAWwC,EACpBzD,EAAS6E,aAAeA,EACxB7E,EAASlB,SAAWA,EACpBkB,EAASjC,WAAaA,EACtBuC,EAAMN,SAAWA,GACnBiF,OAAO3E,MAAM4W,QACRjS,OAAO3E,OACE,kBAAV3E,SAAwBA,OAAOwb,IAAMxb,OAAS,SAAUyb,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.calendar.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.calendar', [\n        'kendo.core',\n        'kendo.selectable'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'calendar',\n        name: 'Calendar',\n        category: 'web',\n        description: 'The Calendar widget renders a graphical calendar that supports navigation and selection.',\n        depends: [\n            'core',\n            'selectable'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, support = kendo.support, ui = kendo.ui, Widget = ui.Widget, keys = kendo.keys, parse = kendo.parseDate, adjustDST = kendo.date.adjustDST, weekInYear = kendo.date.weekInYear, Selectable = kendo.ui.Selectable, extractFormat = kendo._extractFormat, template = kendo.template, getCulture = kendo.getCulture, transitions = kendo.support.transitions, transitionOrigin = transitions ? transitions.css + 'transform-origin' : '', cellTemplate = template('<td#=data.cssClass# role=\"gridcell\"><a tabindex=\"-1\" class=\"k-link\" href=\"\\\\#\" data-#=data.ns#value=\"#=data.dateString#\">#=data.value#</a></td>', { useWithBlock: false }), emptyCellTemplate = template('<td role=\"gridcell\" class=\"k-out-of-range\"><a class=\"k-link\"></a></td>', { useWithBlock: false }), otherMonthCellTemplate = template('<td role=\"gridcell\" class=\"k-out-of-range\">&nbsp;</td>', { useWithBlock: false }), weekNumberTemplate = template('<td class=\"k-alt\">#= data.weekNumber #</td>', { useWithBlock: false }), browser = kendo.support.browser, isIE8 = browser.msie && browser.version < 9, outerWidth = kendo._outerWidth, ns = '.kendoCalendar', CLICK = 'click' + ns, KEYDOWN_NS = 'keydown' + ns, ID = 'id', MIN = 'min', LEFT = 'left', SLIDE = 'slideIn', MONTH = 'month', CENTURY = 'century', CHANGE = 'change', NAVIGATE = 'navigate', VALUE = 'value', HOVER = 'k-state-hover', DISABLED = 'k-state-disabled', FOCUSED = 'k-state-focused', OTHERMONTH = 'k-other-month', OTHERMONTHCLASS = ' class=\"' + OTHERMONTH + '\"', OUTOFRANGE = 'k-out-of-range', TODAY = 'k-nav-today', CELLSELECTOR = 'td:has(.k-link)', CELLSELECTORVALID = 'td:has(.k-link):not(.' + DISABLED + '):not(.' + OUTOFRANGE + ')', WEEKCOLUMNSELECTOR = 'td:not(:has(.k-link))', SELECTED = 'k-state-selected', BLUR = 'blur' + ns, FOCUS = 'focus', FOCUS_WITH_NS = FOCUS + ns, MOUSEENTER = support.touch ? 'touchstart' : 'mouseenter', MOUSEENTER_WITH_NS = support.touch ? 'touchstart' + ns : 'mouseenter' + ns, MOUSELEAVE = support.touch ? 'touchend' + ns + ' touchmove' + ns : 'mouseleave' + ns, MS_PER_MINUTE = 60000, MS_PER_DAY = 86400000, PREVARROW = '_prevArrow', NEXTARROW = '_nextArrow', ARIA_DISABLED = 'aria-disabled', ARIA_SELECTED = 'aria-selected', ARIA_LABEL = 'aria-label', proxy = $.proxy, extend = $.extend, DATE = Date, views = {\n                month: 0,\n                year: 1,\n                decade: 2,\n                century: 3\n            };\n        var Calendar = Widget.extend({\n            init: function (element, options) {\n                var that = this, value, id;\n                Widget.fn.init.call(that, element, options);\n                element = that.wrapper = that.element;\n                options = that.options;\n                options.url = window.unescape(options.url);\n                that.options.disableDates = getDisabledExpr(that.options.disableDates);\n                that._templates();\n                that._selectable();\n                that._header();\n                that._viewWrapper();\n                that._footer(that.footer);\n                id = element.addClass('k-widget k-calendar ' + (options.weekNumber ? ' k-week-number' : '')).on(MOUSEENTER_WITH_NS + ' ' + MOUSELEAVE, CELLSELECTOR, mousetoggle).on(KEYDOWN_NS, 'table.k-content', proxy(that._move, that)).on(CLICK, CELLSELECTOR, function (e) {\n                    var link = e.currentTarget.firstChild, value = toDateObject(link);\n                    if (link.href.indexOf('#') != -1) {\n                        e.preventDefault();\n                    }\n                    if (that._view.name == 'month' && that.options.disableDates(value)) {\n                        return;\n                    }\n                    if (that._view.name != 'month' || options.selectable == 'single') {\n                        that._click($(link));\n                    }\n                }).on('mouseup' + ns, 'table.k-content, .k-footer', function () {\n                    that._focusView(that.options.focusOnNav !== false);\n                }).attr(ID);\n                if (id) {\n                    that._cellID = id + '_cell_selected';\n                }\n                if (that._isMultipleSelection() && that.options.weekNumber) {\n                    element.on(CLICK, WEEKCOLUMNSELECTOR, function (e) {\n                        var first = $(e.currentTarget).closest('tr').find(CELLSELECTORVALID).first(), last = that.selectable._lastActive = $(e.currentTarget).closest('tr').find(CELLSELECTORVALID).last();\n                        that.selectable.selectRange(first, last, { event: e });\n                        that._current = that._value = toDateObject(last.find('a'));\n                        that._class(FOCUSED, that._current);\n                    });\n                }\n                normalize(options);\n                value = parse(options.value, options.format, options.culture);\n                that._selectDates = [];\n                that._index = views[options.start];\n                that._current = new DATE(+restrictValue(value, options.min, options.max));\n                that._addClassProxy = function () {\n                    that._active = true;\n                    if (that._cell.hasClass(DISABLED)) {\n                        var todayString = that._view.toDateString(getToday());\n                        that._cell = that._cellByDate(todayString);\n                    }\n                    that._cell.addClass(FOCUSED);\n                };\n                that._removeClassProxy = function () {\n                    that._active = false;\n                    that._cell.removeClass(FOCUSED);\n                };\n                that.value(value);\n                if (that._isMultipleSelection() && options.selectDates.length > 0) {\n                    that.selectDates(options.selectDates);\n                }\n                kendo.notify(that);\n            },\n            options: {\n                name: 'Calendar',\n                value: null,\n                min: new DATE(1900, 0, 1),\n                max: new DATE(2099, 11, 31),\n                dates: [],\n                disableDates: null,\n                url: '',\n                culture: '',\n                footer: '',\n                format: '',\n                month: {},\n                weekNumber: false,\n                selectable: 'single',\n                selectDates: [],\n                start: MONTH,\n                depth: MONTH,\n                animation: {\n                    horizontal: {\n                        effects: SLIDE,\n                        reverse: true,\n                        duration: 500,\n                        divisor: 2\n                    },\n                    vertical: {\n                        effects: 'zoomIn',\n                        duration: 400\n                    }\n                },\n                messages: { weekColumnHeader: '' }\n            },\n            events: [\n                CHANGE,\n                NAVIGATE\n            ],\n            setOptions: function (options) {\n                var that = this;\n                normalize(options);\n                options.disableDates = getDisabledExpr(options.disableDates);\n                Widget.fn.setOptions.call(that, options);\n                that._templates();\n                that._selectable();\n                that._viewWrapper();\n                that._footer(that.footer);\n                that._index = views[that.options.start];\n                that.navigate();\n                if (options.weekNumber) {\n                    that.element.addClass('k-week-number');\n                }\n            },\n            destroy: function () {\n                var that = this, today = that._today;\n                that.element.off(ns);\n                that._title.off(ns);\n                that[PREVARROW].off(ns);\n                that[NEXTARROW].off(ns);\n                that._destroySelectable();\n                kendo.destroy(that._table);\n                if (today) {\n                    kendo.destroy(today.off(ns));\n                }\n                Widget.fn.destroy.call(that);\n            },\n            current: function () {\n                return this._current;\n            },\n            view: function () {\n                return this._view;\n            },\n            focus: function (table) {\n                table = table || this._table;\n                this._bindTable(table);\n                table.trigger('focus');\n            },\n            min: function (value) {\n                return this._option(MIN, value);\n            },\n            max: function (value) {\n                return this._option('max', value);\n            },\n            navigateToPast: function () {\n                this._navigate(PREVARROW, -1);\n            },\n            navigateToFuture: function () {\n                this._navigate(NEXTARROW, 1);\n            },\n            navigateUp: function () {\n                var that = this, index = that._index;\n                if (that._title.hasClass(DISABLED)) {\n                    return;\n                }\n                that.navigate(that._current, ++index);\n            },\n            navigateDown: function (value) {\n                var that = this, index = that._index, depth = that.options.depth;\n                if (!value) {\n                    return;\n                }\n                if (index === views[depth]) {\n                    if (!isEqualDate(that._value, that._current) || !isEqualDate(that._value, value)) {\n                        that.value(value);\n                        that.trigger(CHANGE);\n                    }\n                    return;\n                }\n                that.navigate(value, --index);\n            },\n            navigate: function (value, view) {\n                view = isNaN(view) ? views[view] : view;\n                var that = this, options = that.options, culture = options.culture, min = options.min, max = options.max, title = that._title, from = that._table, old = that._oldTable, currentValue = that._current, future = value && +value > +currentValue, vertical = view !== undefined && view !== that._index, to, currentView, compare, disabled;\n                if (!value) {\n                    value = currentValue;\n                }\n                that._current = value = new DATE(+restrictValue(value, min, max));\n                if (view === undefined) {\n                    view = that._index;\n                } else {\n                    that._index = view;\n                }\n                that._view = currentView = calendar.views[view];\n                compare = currentView.compare;\n                disabled = view === views[CENTURY];\n                title.toggleClass(DISABLED, disabled).attr(ARIA_DISABLED, disabled);\n                disabled = compare(value, min) < 1;\n                that[PREVARROW].toggleClass(DISABLED, disabled).attr(ARIA_DISABLED, disabled);\n                if (that[PREVARROW].hasClass(DISABLED)) {\n                    that[PREVARROW].removeClass(HOVER);\n                }\n                disabled = compare(value, max) > -1;\n                that[NEXTARROW].toggleClass(DISABLED, disabled).attr(ARIA_DISABLED, disabled);\n                if (that[NEXTARROW].hasClass(DISABLED)) {\n                    that[NEXTARROW].removeClass(HOVER);\n                }\n                if (from && old && old.data('animating')) {\n                    old.kendoStop(true, true);\n                    from.kendoStop(true, true);\n                }\n                that._oldTable = from;\n                if (!from || that._changeView) {\n                    title.html(currentView.title(value, min, max, culture));\n                    that._table = to = $(currentView.content(extend({\n                        min: min,\n                        max: max,\n                        date: value,\n                        url: options.url,\n                        dates: options.dates,\n                        format: options.format,\n                        otherMonth: true,\n                        culture: culture,\n                        disableDates: options.disableDates,\n                        isWeekColumnVisible: options.weekNumber,\n                        messages: options.messages\n                    }, that[currentView.name])));\n                    addClassToViewContainer(to, currentView.name);\n                    makeUnselectable(to);\n                    var replace = from && from.data('start') === to.data('start');\n                    that._animate({\n                        from: from,\n                        to: to,\n                        vertical: vertical,\n                        future: future,\n                        replace: replace\n                    });\n                    that.trigger(NAVIGATE);\n                    that._focus(value);\n                }\n                if (view === views[options.depth] && that._selectDates.length > 0) {\n                    that._visualizeSelectedDatesInView();\n                }\n                if (that.options.selectable === 'single') {\n                    if (view === views[options.depth] && that._value && !that.options.disableDates(that._value)) {\n                        that._class('k-state-selected', that._value);\n                    }\n                }\n                that._class(FOCUSED, value);\n                if (!from && that._cell) {\n                    that._cell.removeClass(FOCUSED);\n                }\n                that._changeView = true;\n            },\n            selectDates: function (dates) {\n                var that = this, validSelectedDates, datesUnique;\n                if (dates === undefined) {\n                    return that._selectDates;\n                }\n                datesUnique = dates.map(function (date) {\n                    return date.getTime();\n                }).filter(function (date, position, array) {\n                    return array.indexOf(date) === position;\n                }).map(function (time) {\n                    return new Date(time);\n                });\n                validSelectedDates = $.grep(datesUnique, function (value) {\n                    if (value) {\n                        return +that._validateValue(new Date(value.setHours(0, 0, 0, 0))) === +value;\n                    }\n                });\n                that._selectDates = validSelectedDates.length > 0 ? validSelectedDates : datesUnique.length === 0 ? datesUnique : that._selectDates;\n                that._visualizeSelectedDatesInView();\n            },\n            value: function (value) {\n                var that = this, old = that._view, view = that._view;\n                if (value === undefined) {\n                    return that._value;\n                }\n                value = that._validateValue(value);\n                if (value && that._isMultipleSelection()) {\n                    var date = new Date(+value);\n                    date.setHours(0, 0, 0, 0);\n                    that._selectDates = [date];\n                    that.selectable._lastActive = null;\n                }\n                if (old && value === null && that._cell) {\n                    that._cell.removeClass(SELECTED);\n                } else {\n                    that._changeView = !value || view && view.compare(value, that._current) !== 0;\n                    that.navigate(value);\n                }\n            },\n            _validateValue: function (value) {\n                var that = this, options = that.options, min = options.min, max = options.max;\n                if (value === null) {\n                    that._current = createDate(that._current.getFullYear(), that._current.getMonth(), that._current.getDate());\n                }\n                value = parse(value, options.format, options.culture);\n                if (value !== null) {\n                    value = new DATE(+value);\n                    if (!isInRange(value, min, max)) {\n                        value = null;\n                    }\n                }\n                if (value === null || !that.options.disableDates(new Date(+value))) {\n                    that._value = value;\n                } else if (that._value === undefined) {\n                    that._value = null;\n                }\n                return that._value;\n            },\n            _visualizeSelectedDatesInView: function () {\n                var that = this;\n                var selectedDates = {};\n                $.each(that._selectDates, function (index, value) {\n                    selectedDates[kendo.calendar.views[0].toDateString(value)] = value;\n                });\n                that.selectable.clear();\n                var cells = that._table.find(CELLSELECTOR).filter(function (index, element) {\n                    return selectedDates[$(element.firstChild).attr(kendo.attr(VALUE))];\n                });\n                if (cells.length > 0) {\n                    that.selectable._selectElement(cells, true);\n                }\n            },\n            _isMultipleSelection: function () {\n                var that = this;\n                return that.options.selectable === 'multiple';\n            },\n            _selectable: function () {\n                var that = this;\n                if (!that._isMultipleSelection()) {\n                    return;\n                }\n                var selectable = that.options.selectable, selectableOptions = Selectable.parseOptions(selectable);\n                if (selectableOptions.multiple) {\n                    that.element.attr('aria-multiselectable', 'true');\n                }\n                that.selectable = new Selectable(that.wrapper, {\n                    aria: true,\n                    inputSelectors: 'input,textarea,.k-multiselect-wrap,select,button,.k-button>span,.k-button>img,span.k-icon.k-i-arrow-60-down,span.k-icon.k-i-arrow-60-up',\n                    multiple: selectableOptions.multiple,\n                    filter: 'table.k-month:eq(0) ' + CELLSELECTORVALID,\n                    change: proxy(that._onSelect, that),\n                    relatedTarget: proxy(that._onRelatedTarget, that)\n                });\n            },\n            _onRelatedTarget: function (target) {\n                var that = this;\n                if (that.selectable.options.multiple && target.is(CELLSELECTORVALID)) {\n                    that._current = toDateObject(target.find('a'));\n                    that._class(FOCUSED, toDateObject(target.find('a')));\n                }\n            },\n            _onSelect: function (e) {\n                var that = this, eventArgs = e, selectableOptions = Selectable.parseOptions(that.options.selectable);\n                if (!selectableOptions.multiple) {\n                    if ($(eventArgs.event.currentTarget).is('td') && !$(eventArgs.event.currentTarget).hasClass('k-state-selected')) {\n                        $(eventArgs.event.currentTarget).addClass('k-state-selected');\n                    } else {\n                        that._click($(eventArgs.event.currentTarget).find('a'));\n                    }\n                    return;\n                }\n                if (eventArgs.event.ctrlKey || eventArgs.event.metaKey) {\n                    if ($(eventArgs.event.currentTarget).is(CELLSELECTORVALID)) {\n                        that._toggleSelection($(eventArgs.event.currentTarget));\n                    } else {\n                        that._cellsBySelector(CELLSELECTORVALID).each(function (index, element) {\n                            var value = toDateObject($(element).find('a'));\n                            that._deselect(value);\n                        });\n                        that._addSelectedCellsToArray();\n                    }\n                } else if (eventArgs.event.shiftKey) {\n                    that._rangeSelection(that._cell);\n                } else if ($(eventArgs.event.currentTarget).is(CELLSELECTOR)) {\n                    that.value(toDateObject($(eventArgs.event.currentTarget).find('a')));\n                } else {\n                    that._selectDates = [];\n                    that._addSelectedCellsToArray();\n                }\n                that.trigger(CHANGE);\n            },\n            _destroySelectable: function () {\n                var that = this;\n                if (that.selectable) {\n                    that.selectable.destroy();\n                    that.selectable = null;\n                }\n            },\n            _toggleSelection: function (currentCell) {\n                var that = this, date = toDateObject(currentCell.find('a'));\n                if (currentCell.hasClass('k-state-selected')) {\n                    that._selectDates.push(date);\n                } else {\n                    that._deselect(date);\n                }\n            },\n            _rangeSelection: function (toDateCell, startDate) {\n                var that = this, fromDate = startDate || toDateObject(that.selectable.value().first().find('a')), toDate = toDateObject(toDateCell.find('a')), daysDifference;\n                if (that.selectable._lastActive || that._value) {\n                    fromDate = that.selectable._lastActive ? toDateObject(that.selectable._lastActive.find('a')) : new Date(+that._value);\n                } else {\n                    that.selectable._lastActive = startDate ? that._cellByDate(that._view.toDateString(startDate), CELLSELECTORVALID) : that.selectable.value().first();\n                }\n                that._selectDates = [];\n                daysDifference = daysBetweenTwoDates(fromDate, toDate);\n                addDaysToArray(that._selectDates, daysDifference, fromDate, that.options.disableDates);\n                that._visualizeSelectedDatesInView();\n            },\n            _cellsBySelector: function (selector) {\n                var that = this;\n                return that._table.find(selector);\n            },\n            _addSelectedCellsToArray: function () {\n                var that = this;\n                that.selectable.value().each(function (index, item) {\n                    var date = toDateObject($(item.firstChild));\n                    if (!that.options.disableDates(date)) {\n                        that._selectDates.push(date);\n                    }\n                });\n            },\n            _deselect: function (date) {\n                var that = this;\n                var currentDateIndex = that._selectDates.map(Number).indexOf(+date);\n                if (currentDateIndex != -1) {\n                    that._selectDates.splice(currentDateIndex, 1);\n                }\n            },\n            _dateInView: function (date) {\n                var that = this, firstDateInView = toDateObject(that._cellsBySelector(CELLSELECTORVALID + ':first').find('a')), lastDateInView = toDateObject(that._cellsBySelector(CELLSELECTORVALID + ':last').find('a'));\n                return +date <= +lastDateInView && +date >= +firstDateInView;\n            },\n            _isNavigatable: function (currentValue, cellIndex) {\n                var that = this;\n                var isDisabled = that.options.disableDates;\n                var cell;\n                var index;\n                if (that._view.name == 'month') {\n                    return !isDisabled(currentValue);\n                } else {\n                    index = that.wrapper.find('.' + FOCUSED).index();\n                    cell = that.wrapper.find('.k-content td:eq(' + (index + cellIndex) + ')');\n                    return cell.is(CELLSELECTORVALID) || !isDisabled(currentValue);\n                }\n            },\n            _move: function (e) {\n                var that = this, options = that.options, key = e.keyCode, view = that._view, index = that._index, min = that.options.min, max = that.options.max, currentValue = new DATE(+that._current), isRtl = kendo.support.isRtl(that.wrapper), isDisabled = that.options.disableDates, value, prevent, method, temp;\n                if (e.target === that._table[0]) {\n                    that._active = true;\n                }\n                if (key == keys.RIGHT && !isRtl || key == keys.LEFT && isRtl) {\n                    value = 1;\n                    prevent = true;\n                } else if (key == keys.LEFT && !isRtl || key == keys.RIGHT && isRtl) {\n                    value = -1;\n                    prevent = true;\n                } else if (key == keys.UP) {\n                    value = index === 0 ? -7 : -4;\n                    prevent = true;\n                } else if (key == keys.DOWN) {\n                    value = index === 0 ? 7 : 4;\n                    prevent = true;\n                } else if (key == keys.SPACEBAR) {\n                    value = 0;\n                    prevent = true;\n                } else if (key == keys.HOME || key == keys.END) {\n                    method = key == keys.HOME ? 'first' : 'last';\n                    temp = view[method](currentValue);\n                    currentValue = new DATE(temp.getFullYear(), temp.getMonth(), temp.getDate(), currentValue.getHours(), currentValue.getMinutes(), currentValue.getSeconds(), currentValue.getMilliseconds());\n                    currentValue.setFullYear(temp.getFullYear());\n                    prevent = true;\n                }\n                if (e.ctrlKey || e.metaKey) {\n                    if (key == keys.RIGHT && !isRtl || key == keys.LEFT && isRtl) {\n                        that.navigateToFuture();\n                        prevent = true;\n                    } else if (key == keys.LEFT && !isRtl || key == keys.RIGHT && isRtl) {\n                        that.navigateToPast();\n                        prevent = true;\n                    } else if (key == keys.UP) {\n                        that.navigateUp();\n                        prevent = true;\n                    } else if (key == keys.DOWN) {\n                        that._click($(that._cell[0].firstChild));\n                        prevent = true;\n                    } else if ((key == keys.ENTER || key == keys.SPACEBAR) && that._isMultipleSelection()) {\n                        that._keyboardToggleSelection(e);\n                        var focusedDate = toDateObject($(that._cell[0]).find('a'));\n                        that._class(FOCUSED, focusedDate);\n                    }\n                } else if (e.shiftKey) {\n                    if (value !== undefined || method) {\n                        if (!method) {\n                            view.setDate(currentValue, value);\n                        }\n                        if (!isInRange(currentValue, min, max)) {\n                            currentValue = restrictValue(currentValue, options.min, options.max);\n                        }\n                        if (isDisabled(currentValue)) {\n                            currentValue = that._nextNavigatable(currentValue, value);\n                        }\n                        min = createDate(min.getFullYear(), min.getMonth(), min.getDate());\n                        if (that._isMultipleSelection()) {\n                            that._keyboardRangeSelection(e, currentValue);\n                        } else {\n                            that._focus(currentValue);\n                        }\n                    }\n                } else {\n                    if (key == keys.ENTER || key == keys.SPACEBAR) {\n                        if (view.name == 'month' && that._isMultipleSelection()) {\n                            that.value(toDateObject($(that._cell.find('a'))));\n                            that.selectable._lastActive = $(that._cell[0]);\n                            that.trigger(CHANGE);\n                        } else {\n                            that._click($(that._cell[0].firstChild));\n                        }\n                        prevent = true;\n                    } else if (key == keys.PAGEUP) {\n                        prevent = true;\n                        that.navigateToPast();\n                    } else if (key == keys.PAGEDOWN) {\n                        prevent = true;\n                        that.navigateToFuture();\n                    }\n                    if (value || method) {\n                        if (!method) {\n                            view.setDate(currentValue, value);\n                        }\n                        min = createDate(min.getFullYear(), min.getMonth(), min.getDate());\n                        if (!isInRange(currentValue, min, max)) {\n                            currentValue = restrictValue(currentValue, options.min, options.max);\n                        }\n                        if (!that._isNavigatable(currentValue, value)) {\n                            currentValue = that._nextNavigatable(currentValue, value);\n                        }\n                        if (that._isMultipleSelection()) {\n                            if (!that._dateInView(currentValue)) {\n                                that.navigate(currentValue);\n                            } else {\n                                that._current = currentValue;\n                                that._class(FOCUSED, currentValue);\n                            }\n                        } else {\n                            that._focus(currentValue);\n                        }\n                    }\n                }\n                if (prevent) {\n                    e.preventDefault();\n                }\n                return that._current;\n            },\n            _keyboardRangeSelection: function (event, currentValue) {\n                var that = this, fromDate, daysDifference;\n                if (!that._dateInView(currentValue)) {\n                    that._selectDates = [];\n                    fromDate = that.selectable._lastActive ? toDateObject(that.selectable._lastActive.find('a')) : currentValue;\n                    daysDifference = daysBetweenTwoDates(fromDate, new Date(+currentValue));\n                    addDaysToArray(that._selectDates, daysDifference, fromDate, that.options.disableDates);\n                    that.navigate(currentValue);\n                    that._current = currentValue;\n                    that.selectable._lastActive = that.selectable._lastActive || that._cellByDate(that._view.toDateString(currentValue), CELLSELECTORVALID);\n                    that.trigger(CHANGE);\n                    return;\n                }\n                that.selectable.options.filter = that.wrapper.find('table').length > 1 && +currentValue > +that._current ? 'table.k-month:eq(1) ' + CELLSELECTORVALID : 'table.k-month:eq(0) ' + CELLSELECTORVALID;\n                that._class(FOCUSED, currentValue);\n                that._current = currentValue;\n                that._rangeSelection(that._cellByDate(that._view.toDateString(currentValue), CELLSELECTORVALID), currentValue);\n                that.trigger(CHANGE);\n                that.selectable.options.filter = 'table.k-month:eq(0) ' + CELLSELECTORVALID;\n            },\n            _keyboardToggleSelection: function (event) {\n                var that = this;\n                event.currentTarget = that._cell[0];\n                that.selectable._lastActive = $(that._cell[0]);\n                if ($(that._cell[0]).hasClass(SELECTED)) {\n                    that.selectable._unselect($(that._cell[0]));\n                    that.selectable.trigger(CHANGE, { event: event });\n                } else {\n                    that.selectable.value($(that._cell[0]), { event: event });\n                }\n            },\n            _nextNavigatable: function (currentValue, value) {\n                var that = this, disabled = true, view = that._view, min = that.options.min, max = that.options.max, isDisabled = that.options.disableDates, navigatableDate = new Date(currentValue.getTime());\n                view.setDate(navigatableDate, -value);\n                while (disabled) {\n                    view.setDate(currentValue, value);\n                    if (!isInRange(currentValue, min, max)) {\n                        currentValue = navigatableDate;\n                        break;\n                    }\n                    disabled = isDisabled(currentValue);\n                }\n                return currentValue;\n            },\n            _animate: function (options) {\n                var that = this;\n                var from = options.from;\n                var to = options.to;\n                var active = that._active;\n                var viewWrapper = that.element.children('.k-calendar-view');\n                if (!from) {\n                    viewWrapper.append(to);\n                    that._bindTable(to);\n                } else if (from.parent().data('animating')) {\n                    from.off(ns);\n                    from.parent().kendoStop(true, true).remove();\n                    from.remove();\n                    viewWrapper.append(to);\n                    that._focusView(active);\n                } else if (!from.is(':visible') || that.options.animation === false || options.replace) {\n                    to.insertAfter(from);\n                    from.off(ns).remove();\n                    that._focusView(active);\n                } else {\n                    that[options.vertical ? '_vertical' : '_horizontal'](from, to, options.future);\n                }\n            },\n            _horizontal: function (from, to, future) {\n                var that = this, active = that._active, horizontal = that.options.animation.horizontal, effects = horizontal.effects, viewWidth = outerWidth(from);\n                if (effects && effects.indexOf(SLIDE) != -1) {\n                    from.add(to).css({ width: viewWidth });\n                    from.wrap('<div/>');\n                    that._focusView(active, from);\n                    from.parent().css({\n                        position: 'relative',\n                        width: viewWidth * 2,\n                        'float': LEFT,\n                        'margin-left': future ? 0 : -viewWidth\n                    });\n                    to[future ? 'insertAfter' : 'insertBefore'](from);\n                    extend(horizontal, {\n                        effects: SLIDE + ':' + (future ? 'right' : LEFT),\n                        complete: function () {\n                            from.off(ns).remove();\n                            that._oldTable = null;\n                            to.unwrap();\n                            that._focusView(active);\n                        }\n                    });\n                    from.parent().kendoStop(true, true).kendoAnimate(horizontal);\n                }\n            },\n            _vertical: function (from, to) {\n                var that = this, vertical = that.options.animation.vertical, effects = vertical.effects, active = that._active, cell, position;\n                if (effects && effects.indexOf('zoom') != -1) {\n                    to.insertBefore(from);\n                    from.css({\n                        position: 'absolute',\n                        width: to.width()\n                    });\n                    if (transitionOrigin) {\n                        cell = that._cellByDate(that._view.toDateString(that._current));\n                        position = cell.position();\n                        position = position.left + parseInt(cell.width() / 2, 10) + 'px' + ' ' + (position.top + parseInt(cell.height() / 2, 10) + 'px');\n                        to.css(transitionOrigin, position);\n                    }\n                    from.kendoStop(true, true).kendoAnimate({\n                        effects: 'fadeOut',\n                        duration: 600,\n                        complete: function () {\n                            from.off(ns).remove();\n                            that._oldTable = null;\n                            that._focusView(active);\n                        }\n                    });\n                    to.kendoStop(true, true).kendoAnimate(vertical);\n                }\n            },\n            _cellByDate: function (value, selector) {\n                return this._table.find(selector ? selector : 'td:not(.' + OTHERMONTH + ')').filter(function () {\n                    return $(this.firstChild).attr(kendo.attr(VALUE)) === value;\n                });\n            },\n            _class: function (className, date) {\n                var that = this, id = that._cellID, cell = that._cell, value = that._view.toDateString(date), disabledDate;\n                if (cell && cell.length) {\n                    cell[0].removeAttribute(ARIA_SELECTED);\n                    cell[0].removeAttribute(ARIA_LABEL);\n                    cell[0].removeAttribute(ID);\n                }\n                if (date && that._view.name == 'month') {\n                    disabledDate = that.options.disableDates(date);\n                }\n                that._cellsBySelector(that._isMultipleSelection() ? CELLSELECTOR : 'td:not(.' + OTHERMONTH + ')').removeClass(className);\n                cell = that._cellByDate(value, that.options.selectable == 'multiple' ? CELLSELECTOR : 'td:not(.' + OTHERMONTH + ')').attr(ARIA_SELECTED, true);\n                if (className === FOCUSED && !that._active && that.options.focusOnNav !== false || disabledDate) {\n                    className = '';\n                }\n                cell.addClass(className);\n                if (cell[0]) {\n                    that._cell = cell;\n                }\n                if (id) {\n                    cell.attr(ID, id);\n                    that._table[0].removeAttribute('aria-activedescendant');\n                    that._table.attr('aria-activedescendant', id);\n                }\n            },\n            _bindTable: function (table) {\n                table.on(FOCUS_WITH_NS, this._addClassProxy).on(BLUR, this._removeClassProxy);\n            },\n            _click: function (link) {\n                var that = this, options = that.options, currentValue = new Date(+that._current), value = toDateObject(link);\n                adjustDST(value, 0);\n                if (that._view.name == 'month' && that.options.disableDates(value)) {\n                    value = that._value;\n                }\n                that._view.setDate(currentValue, value);\n                that.navigateDown(restrictValue(currentValue, options.min, options.max));\n            },\n            _focus: function (value) {\n                var that = this, view = that._view;\n                if (view.compare(value, that._current) !== 0) {\n                    that.navigate(value);\n                } else {\n                    that._current = value;\n                    that._class(FOCUSED, value);\n                }\n            },\n            _focusView: function (active, table) {\n                if (active) {\n                    this.focus(table);\n                }\n            },\n            _viewWrapper: function () {\n                var that = this;\n                var element = that.element;\n                var viewWrapper = element.children('.k-calendar-view');\n                if (!viewWrapper[0]) {\n                    viewWrapper = $('<div class=\\'k-calendar-view\\' />').insertAfter(element.find('.k-header'));\n                }\n            },\n            _footer: function (template) {\n                var that = this, today = getToday(), element = that.element, footer = element.find('.k-footer');\n                if (!template) {\n                    that._toggle(false);\n                    footer.hide();\n                    return;\n                }\n                if (!footer[0]) {\n                    footer = $('<div class=\"k-footer\"><a href=\"#\" class=\"k-link k-nav-today\"></a></div>').appendTo(element);\n                }\n                that._today = footer.show().find('.k-link').html(template(today)).attr('title', kendo.toString(today, 'D', that.options.culture));\n                that._toggle();\n            },\n            _header: function () {\n                var that = this, element = that.element, links;\n                if (!element.find('.k-header')[0]) {\n                    element.html('<div class=\"k-header\">' + '<a href=\"#\" role=\"button\" class=\"k-link k-nav-prev\" ' + ARIA_LABEL + '=\"Previous\"><span class=\"k-icon k-i-arrow-60-left\"></span></a>' + '<a href=\"#\" role=\"button\" aria-live=\"assertive\" aria-atomic=\"true\" class=\"k-link k-nav-fast\"></a>' + '<a href=\"#\" role=\"button\" class=\"k-link k-nav-next\" ' + ARIA_LABEL + '=\"Next\"><span class=\"k-icon k-i-arrow-60-right\"></span></a>' + '</div>');\n                }\n                links = element.find('.k-link').on(MOUSEENTER_WITH_NS + ' ' + MOUSELEAVE + ' ' + FOCUS_WITH_NS + ' ' + BLUR, mousetoggle).on('click', function () {\n                    return false;\n                });\n                that._title = links.eq(1).on(CLICK, function () {\n                    that._active = that.options.focusOnNav !== false;\n                    that.navigateUp();\n                });\n                that[PREVARROW] = links.eq(0).on(CLICK, function () {\n                    that._active = that.options.focusOnNav !== false;\n                    that.navigateToPast();\n                });\n                that[NEXTARROW] = links.eq(2).on(CLICK, function () {\n                    that._active = that.options.focusOnNav !== false;\n                    that.navigateToFuture();\n                });\n            },\n            _navigate: function (arrow, modifier) {\n                var that = this, index = that._index + 1, currentValue = new DATE(+that._current);\n                if (that._isMultipleSelection()) {\n                    var firstDayCurrentMonth = that._table.find('td:not(.k-other-month):not(.k-out-of-range)').has('.k-link').first();\n                    currentValue = toDateObject(firstDayCurrentMonth.find('a'));\n                    that._current = new Date(+currentValue);\n                }\n                arrow = that[arrow];\n                if (!arrow.hasClass(DISABLED)) {\n                    if (index > 3) {\n                        currentValue.setFullYear(currentValue.getFullYear() + 100 * modifier);\n                    } else {\n                        calendar.views[index].setDate(currentValue, modifier);\n                    }\n                    that.navigate(currentValue);\n                }\n            },\n            _option: function (option, value) {\n                var that = this, options = that.options, currentValue = that._value || that._current, isBigger;\n                if (value === undefined) {\n                    return options[option];\n                }\n                value = parse(value, options.format, options.culture);\n                if (!value) {\n                    return;\n                }\n                options[option] = new DATE(+value);\n                if (option === MIN) {\n                    isBigger = value > currentValue;\n                } else {\n                    isBigger = currentValue > value;\n                }\n                if (isBigger || isEqualMonth(currentValue, value)) {\n                    if (isBigger) {\n                        that._value = null;\n                    }\n                    that._changeView = true;\n                }\n                if (!that._changeView) {\n                    that._changeView = !!(options.month.content || options.month.empty);\n                }\n                that.navigate(that._value);\n                that._toggle();\n            },\n            _toggle: function (toggle) {\n                var that = this, options = that.options, isTodayDisabled = that.options.disableDates(getToday()), link = that._today;\n                if (toggle === undefined) {\n                    toggle = isInRange(getToday(), options.min, options.max);\n                }\n                if (link) {\n                    link.off(CLICK);\n                    if (toggle && !isTodayDisabled) {\n                        link.addClass(TODAY).removeClass(DISABLED).on(CLICK, proxy(that._todayClick, that));\n                    } else {\n                        link.removeClass(TODAY).addClass(DISABLED).on(CLICK, prevent);\n                    }\n                }\n            },\n            _todayClick: function (e) {\n                var that = this, depth = views[that.options.depth], disabled = that.options.disableDates, today = getToday();\n                e.preventDefault();\n                if (disabled(today)) {\n                    return;\n                }\n                if (that._view.compare(that._current, today) === 0 && that._index == depth) {\n                    that._changeView = false;\n                }\n                if (that._isMultipleSelection()) {\n                    that._selectDates = [today];\n                    that.selectable._lastActive = null;\n                }\n                that._value = today;\n                that.navigate(today, depth);\n                that.trigger(CHANGE);\n            },\n            _templates: function () {\n                var that = this, options = that.options, footer = options.footer, month = options.month, content = month.content, weekNumber = month.weekNumber, empty = month.empty, footerTemplate = '#= kendo.toString(data,\"D\",\"' + options.culture + '\") #';\n                that.month = {\n                    content: template('<td#=data.cssClass# role=\"gridcell\"><a tabindex=\"-1\" class=\"k-link#=data.linkClass#\" href=\"#=data.url#\" ' + kendo.attr(VALUE) + '=\"#=data.dateString#\" title=\"#=data.title#\">' + (content || '#=data.value#') + '</a></td>', { useWithBlock: !!content }),\n                    empty: template('<td role=\"gridcell\">' + (empty || '&nbsp;') + '</td>', { useWithBlock: !!empty }),\n                    weekNumber: template('<td class=\"k-alt\">' + (weekNumber || '#= data.weekNumber #') + '</td>', { useWithBlock: !!weekNumber })\n                };\n                if (footer && footer !== true) {\n                    footerTemplate = footer;\n                }\n                that.footer = footer !== false ? template(footerTemplate, { useWithBlock: false }) : null;\n            }\n        });\n        ui.plugin(Calendar);\n        var calendar = {\n            firstDayOfMonth: function (date) {\n                return createDate(date.getFullYear(), date.getMonth(), 1);\n            },\n            firstVisibleDay: function (date, calendarInfo) {\n                calendarInfo = calendarInfo || kendo.culture().calendar;\n                var firstDay = calendarInfo.firstDay, firstVisibleDay = new DATE(date.getFullYear(), date.getMonth(), 1, date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n                firstVisibleDay.setFullYear(date.getFullYear());\n                while (firstVisibleDay.getDay() != firstDay) {\n                    calendar.setTime(firstVisibleDay, -1 * MS_PER_DAY);\n                }\n                return firstVisibleDay;\n            },\n            setTime: function (date, time) {\n                var tzOffsetBefore = date.getTimezoneOffset(), resultDATE = new DATE(date.getTime() + time), tzOffsetDiff = resultDATE.getTimezoneOffset() - tzOffsetBefore;\n                date.setTime(resultDATE.getTime() + tzOffsetDiff * MS_PER_MINUTE);\n            },\n            views: [\n                {\n                    name: MONTH,\n                    title: function (date, min, max, culture) {\n                        return getCalendarInfo(culture).months.names[date.getMonth()] + ' ' + date.getFullYear();\n                    },\n                    content: function (options) {\n                        var that = this, idx = 0, min = options.min, max = options.max, date = options.date, dates = options.dates, format = options.format, culture = options.culture, navigateUrl = options.url, showHeader = options.showHeader, otherMonth = options.otherMonth, isWeekColumnVisible = options.isWeekColumnVisible, hasUrl = navigateUrl && dates[0], currentCalendar = getCalendarInfo(culture), firstDayIdx = currentCalendar.firstDay, days = currentCalendar.days, names = shiftArray(days.names, firstDayIdx), shortNames = shiftArray(days.namesShort, firstDayIdx), start = calendar.firstVisibleDay(date, currentCalendar), firstDayOfMonth = that.first(date), lastDayOfMonth = that.last(date), toDateString = that.toDateString, today = getToday(), html = '<table tabindex=\"0\" role=\"grid\" class=\"k-content\" cellspacing=\"0\" data-start=\"' + toDateString(start) + '\">';\n                        if (showHeader) {\n                            html += '<caption class=\"k-month-header\">' + this.title(date, min, max, culture) + '</caption><thead><tr role=\"row\">';\n                        } else {\n                            html += '<thead><tr role=\"row\">';\n                        }\n                        if (isWeekColumnVisible) {\n                            html += '<th scope=\"col\" class=\"k-alt\">' + options.messages.weekColumnHeader + '</th>';\n                        }\n                        for (; idx < 7; idx++) {\n                            html += '<th scope=\"col\" title=\"' + names[idx] + '\">' + shortNames[idx] + '</th>';\n                        }\n                        adjustDST(today, 0);\n                        today = +today;\n                        return view({\n                            cells: 42,\n                            perRow: 7,\n                            html: html += '</tr></thead><tbody><tr role=\"row\">',\n                            start: start,\n                            isWeekColumnVisible: isWeekColumnVisible,\n                            weekNumber: options.weekNumber,\n                            min: createDate(min.getFullYear(), min.getMonth(), min.getDate()),\n                            max: createDate(max.getFullYear(), max.getMonth(), max.getDate()),\n                            otherMonth: otherMonth,\n                            content: options.content,\n                            lastDayOfMonth: lastDayOfMonth,\n                            empty: options.empty,\n                            setter: that.setDate,\n                            disableDates: options.disableDates,\n                            build: function (date, idx, disableDates) {\n                                var cssClass = [], day = date.getDay(), linkClass = '', url = '#';\n                                if (date < firstDayOfMonth || date > lastDayOfMonth) {\n                                    cssClass.push(OTHERMONTH);\n                                }\n                                if (disableDates(date)) {\n                                    cssClass.push(DISABLED);\n                                }\n                                if (+date === today) {\n                                    cssClass.push('k-today');\n                                }\n                                if (day === 0 || day === 6) {\n                                    cssClass.push('k-weekend');\n                                }\n                                if (hasUrl && inArray(+date, dates)) {\n                                    url = navigateUrl.replace('{0}', kendo.toString(date, format, culture));\n                                    linkClass = ' k-action-link';\n                                }\n                                return {\n                                    date: date,\n                                    dates: dates,\n                                    ns: kendo.ns,\n                                    title: kendo.toString(date, 'D', culture),\n                                    value: date.getDate(),\n                                    dateString: toDateString(date),\n                                    cssClass: cssClass[0] ? ' class=\"' + cssClass.join(' ') + '\"' : '',\n                                    linkClass: linkClass,\n                                    url: url\n                                };\n                            },\n                            weekNumberBuild: function (date) {\n                                return {\n                                    weekNumber: weekInYear(date, kendo.culture().calendar.firstDay),\n                                    currentDate: date\n                                };\n                            }\n                        });\n                    },\n                    first: function (date) {\n                        return calendar.firstDayOfMonth(date);\n                    },\n                    last: function (date) {\n                        var last = createDate(date.getFullYear(), date.getMonth() + 1, 0), first = calendar.firstDayOfMonth(date), timeOffset = Math.abs(last.getTimezoneOffset() - first.getTimezoneOffset());\n                        if (timeOffset) {\n                            last.setHours(first.getHours() + timeOffset / 60);\n                        }\n                        return last;\n                    },\n                    compare: function (date1, date2) {\n                        var result, month1 = date1.getMonth(), year1 = date1.getFullYear(), month2 = date2.getMonth(), year2 = date2.getFullYear();\n                        if (year1 > year2) {\n                            result = 1;\n                        } else if (year1 < year2) {\n                            result = -1;\n                        } else {\n                            result = month1 == month2 ? 0 : month1 > month2 ? 1 : -1;\n                        }\n                        return result;\n                    },\n                    setDate: function (date, value) {\n                        var hours = date.getHours();\n                        if (value instanceof DATE) {\n                            date.setFullYear(value.getFullYear(), value.getMonth(), value.getDate());\n                        } else {\n                            calendar.setTime(date, value * MS_PER_DAY);\n                        }\n                        adjustDST(date, hours);\n                    },\n                    toDateString: function (date) {\n                        return date.getFullYear() + '/' + date.getMonth() + '/' + date.getDate();\n                    }\n                },\n                {\n                    name: 'year',\n                    title: function (date) {\n                        return date.getFullYear();\n                    },\n                    content: function (options) {\n                        var namesAbbr = getCalendarInfo(options.culture).months.namesAbbr, toDateString = this.toDateString, min = options.min, max = options.max, html = '';\n                        if (options.showHeader) {\n                            html += '<table tabindex=\"0\" role=\"grid\" class=\"k-content k-meta-view\" cellspacing=\"0\"><caption class=\"k-meta-header\">';\n                            html += this.title(options.date);\n                            html += '</caption><tbody><tr role=\"row\">';\n                        }\n                        return view({\n                            min: createDate(min.getFullYear(), min.getMonth(), 1),\n                            max: createDate(max.getFullYear(), max.getMonth(), 1),\n                            start: createDate(options.date.getFullYear(), 0, 1),\n                            html: html,\n                            setter: this.setDate,\n                            build: function (date) {\n                                return {\n                                    value: namesAbbr[date.getMonth()],\n                                    ns: kendo.ns,\n                                    dateString: toDateString(date),\n                                    cssClass: ''\n                                };\n                            }\n                        });\n                    },\n                    first: function (date) {\n                        return createDate(date.getFullYear(), 0, date.getDate());\n                    },\n                    last: function (date) {\n                        return createDate(date.getFullYear(), 11, date.getDate());\n                    },\n                    compare: function (date1, date2) {\n                        return compare(date1, date2);\n                    },\n                    setDate: function (date, value) {\n                        var month, hours = date.getHours();\n                        if (value instanceof DATE) {\n                            month = value.getMonth();\n                            date.setFullYear(value.getFullYear(), month, date.getDate());\n                            if (month !== date.getMonth()) {\n                                date.setDate(0);\n                            }\n                        } else {\n                            month = date.getMonth() + value;\n                            date.setMonth(month);\n                            if (month > 11) {\n                                month -= 12;\n                            }\n                            if (month > 0 && date.getMonth() != month) {\n                                date.setDate(0);\n                            }\n                        }\n                        adjustDST(date, hours);\n                    },\n                    toDateString: function (date) {\n                        return date.getFullYear() + '/' + date.getMonth() + '/1';\n                    }\n                },\n                {\n                    name: 'decade',\n                    title: function (date, min, max) {\n                        return title(date, min, max, 10);\n                    },\n                    content: function (options) {\n                        var year = options.date.getFullYear(), toDateString = this.toDateString, html = '';\n                        if (options.showHeader) {\n                            html += '<table tabindex=\"0\" role=\"grid\" class=\"k-content k-meta-view\" cellspacing=\"0\"><caption class=\"k-meta-header\">';\n                            html += this.title(options.date, options.min, options.max);\n                            html += '</caption><tbody><tr role=\"row\">';\n                        }\n                        return view({\n                            start: createDate(year - year % 10 - 1, 0, 1),\n                            min: createDate(options.min.getFullYear(), 0, 1),\n                            max: createDate(options.max.getFullYear(), 0, 1),\n                            otherMonth: options.otherMonth,\n                            html: html,\n                            setter: this.setDate,\n                            build: function (date, idx) {\n                                return {\n                                    value: date.getFullYear(),\n                                    ns: kendo.ns,\n                                    dateString: toDateString(date),\n                                    cssClass: idx === 0 || idx == 11 ? OTHERMONTHCLASS : ''\n                                };\n                            }\n                        });\n                    },\n                    first: function (date) {\n                        var year = date.getFullYear();\n                        return createDate(year - year % 10, date.getMonth(), date.getDate());\n                    },\n                    last: function (date) {\n                        var year = date.getFullYear();\n                        return createDate(year - year % 10 + 9, date.getMonth(), date.getDate());\n                    },\n                    compare: function (date1, date2) {\n                        return compare(date1, date2, 10);\n                    },\n                    setDate: function (date, value) {\n                        setDate(date, value, 1);\n                    },\n                    toDateString: function (date) {\n                        return date.getFullYear() + '/0/1';\n                    }\n                },\n                {\n                    name: CENTURY,\n                    title: function (date, min, max) {\n                        return title(date, min, max, 100);\n                    },\n                    content: function (options) {\n                        var year = options.date.getFullYear(), min = options.min.getFullYear(), max = options.max.getFullYear(), toDateString = this.toDateString, minYear = min, maxYear = max, html = '';\n                        minYear = minYear - minYear % 10;\n                        maxYear = maxYear - maxYear % 10;\n                        if (maxYear - minYear < 10) {\n                            maxYear = minYear + 9;\n                        }\n                        if (options.showHeader) {\n                            html += '<table tabindex=\"0\" role=\"grid\" class=\"k-content k-meta-view\" cellspacing=\"0\"><caption class=\"k-meta-header\">';\n                            html += this.title(options.date, options.min, options.max);\n                            html += '</caption><tbody><tr role=\"row\">';\n                        }\n                        return view({\n                            start: createDate(year - year % 100 - 10, 0, 1),\n                            min: createDate(minYear, 0, 1),\n                            max: createDate(maxYear, 0, 1),\n                            otherMonth: options.otherMonth,\n                            html: html,\n                            setter: this.setDate,\n                            build: function (date, idx) {\n                                var start = date.getFullYear(), end = start + 9;\n                                if (start < min) {\n                                    start = min;\n                                }\n                                if (end > max) {\n                                    end = max;\n                                }\n                                return {\n                                    ns: kendo.ns,\n                                    value: start + ' - ' + end,\n                                    dateString: toDateString(date),\n                                    cssClass: idx === 0 || idx == 11 ? OTHERMONTHCLASS : ''\n                                };\n                            }\n                        });\n                    },\n                    first: function (date) {\n                        var year = date.getFullYear();\n                        return createDate(year - year % 100, date.getMonth(), date.getDate());\n                    },\n                    last: function (date) {\n                        var year = date.getFullYear();\n                        return createDate(year - year % 100 + 99, date.getMonth(), date.getDate());\n                    },\n                    compare: function (date1, date2) {\n                        return compare(date1, date2, 100);\n                    },\n                    setDate: function (date, value) {\n                        setDate(date, value, 10);\n                    },\n                    toDateString: function (date) {\n                        var year = date.getFullYear();\n                        return year - year % 10 + '/0/1';\n                    }\n                }\n            ]\n        };\n        function title(date, min, max, modular) {\n            var start = date.getFullYear(), minYear = min.getFullYear(), maxYear = max.getFullYear(), end;\n            start = start - start % modular;\n            end = start + (modular - 1);\n            if (start < minYear) {\n                start = minYear;\n            }\n            if (end > maxYear) {\n                end = maxYear;\n            }\n            return start + '-' + end;\n        }\n        function view(options) {\n            var idx = 0, data, min = options.min, max = options.max, start = options.start, setter = options.setter, build = options.build, weekNumberBuild = options.weekNumberBuild, length = options.cells || 12, isWeekColumnVisible = options.isWeekColumnVisible, cellsPerRow = options.perRow || 4, otherMonth = options.otherMonth, lastDayOfMonth = options.lastDayOfMonth, weekNumber = options.weekNumber || weekNumberTemplate, content = options.content || cellTemplate, empty = options.empty || emptyCellTemplate, otherMonthTemplate = options.otherMonthCellTemplate || otherMonthCellTemplate, html = options.html || '<table tabindex=\"0\" role=\"grid\" class=\"k-content k-meta-view\" cellspacing=\"0\"><tbody><tr role=\"row\">';\n            if (isWeekColumnVisible) {\n                html += weekNumber(weekNumberBuild(start));\n            }\n            for (; idx < length; idx++) {\n                if (idx > 0 && idx % cellsPerRow === 0) {\n                    html += '</tr><tr role=\"row\">';\n                    if (isWeekColumnVisible) {\n                        html += otherMonth || +start <= +lastDayOfMonth ? weekNumber(weekNumberBuild(start)) : weekNumber({ weekNumber: '&nbsp;' });\n                    }\n                }\n                start = createDate(start.getFullYear(), start.getMonth(), start.getDate());\n                adjustDST(start, 0);\n                data = build(start, idx, options.disableDates);\n                html += data.cssClass.indexOf(OTHERMONTH) !== -1 && !otherMonth ? otherMonthTemplate(data) : isInRange(start, min, max) ? content(data) : empty(data);\n                setter(start, 1);\n            }\n            return html + '</tr></tbody></table>';\n        }\n        function compare(date1, date2, modifier) {\n            var year1 = date1.getFullYear(), start = date2.getFullYear(), end = start, result = 0;\n            if (modifier) {\n                start = start - start % modifier;\n                end = start - start % modifier + modifier - 1;\n            }\n            if (year1 > end) {\n                result = 1;\n            } else if (year1 < start) {\n                result = -1;\n            }\n            return result;\n        }\n        function getToday() {\n            var today = new DATE();\n            return new DATE(today.getFullYear(), today.getMonth(), today.getDate());\n        }\n        function restrictValue(value, min, max) {\n            var today = getToday();\n            if (value) {\n                today = new DATE(+value);\n            }\n            if (min > today) {\n                today = new DATE(+min);\n            } else if (max < today) {\n                today = new DATE(+max);\n            }\n            return today;\n        }\n        function isInRange(date, min, max) {\n            return +date >= +min && +date <= +max;\n        }\n        function shiftArray(array, idx) {\n            return array.slice(idx).concat(array.slice(0, idx));\n        }\n        function setDate(date, value, multiplier) {\n            value = value instanceof DATE ? value.getFullYear() : date.getFullYear() + multiplier * value;\n            date.setFullYear(value);\n        }\n        function daysBetweenTwoDates(startDate, endDate) {\n            if (+endDate < +startDate) {\n                var temp = +startDate;\n                calendar.views[0].setDate(startDate, endDate);\n                calendar.views[0].setDate(endDate, new Date(temp));\n            }\n            var fromDateUTC = Date.UTC(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n            var endDateUTC = Date.UTC(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n            return Math.ceil((+endDateUTC - +fromDateUTC) / kendo.date.MS_PER_DAY);\n        }\n        function addDaysToArray(array, numberOfDays, fromDate, disableDates) {\n            for (var i = 0; i <= numberOfDays; i++) {\n                var nextDay = new Date(fromDate.getTime());\n                nextDay = new Date(nextDay.setDate(nextDay.getDate() + i));\n                if (!disableDates(nextDay)) {\n                    array.push(nextDay);\n                }\n            }\n        }\n        function mousetoggle(e) {\n            var disabled = $(this).hasClass('k-state-disabled');\n            if (!disabled) {\n                $(this).toggleClass(HOVER, MOUSEENTER.indexOf(e.type) > -1 || e.type == FOCUS);\n            }\n        }\n        function prevent(e) {\n            e.preventDefault();\n        }\n        function createDate(year, month, date) {\n            var dateObject = new DATE(year, month, date);\n            dateObject.setFullYear(year, month, date);\n            return dateObject;\n        }\n        function getCalendarInfo(culture) {\n            return getCulture(culture).calendars.standard;\n        }\n        function normalize(options) {\n            var start = views[options.start], depth = views[options.depth], culture = getCulture(options.culture);\n            options.format = extractFormat(options.format || culture.calendars.standard.patterns.d);\n            if (isNaN(start)) {\n                start = 0;\n                options.start = MONTH;\n            }\n            if (depth === undefined || depth > start) {\n                options.depth = MONTH;\n            }\n            if (options.dates === null) {\n                options.dates = [];\n            }\n        }\n        function makeUnselectable(element) {\n            if (isIE8) {\n                element.find('*').attr('unselectable', 'on');\n            }\n        }\n        function addClassToViewContainer(element, currentView) {\n            element.addClass('k-' + currentView);\n        }\n        function inArray(date, dates) {\n            for (var i = 0, length = dates.length; i < length; i++) {\n                if (date === +dates[i]) {\n                    return true;\n                }\n            }\n            return false;\n        }\n        function isEqualDatePart(value1, value2) {\n            if (value1) {\n                return value1.getFullYear() === value2.getFullYear() && value1.getMonth() === value2.getMonth() && value1.getDate() === value2.getDate();\n            }\n            return false;\n        }\n        function isEqualMonth(value1, value2) {\n            if (value1) {\n                return value1.getFullYear() === value2.getFullYear() && value1.getMonth() === value2.getMonth();\n            }\n            return false;\n        }\n        function getDisabledExpr(option) {\n            if (kendo.isFunction(option)) {\n                return option;\n            }\n            if ($.isArray(option)) {\n                return createDisabledExpr(option);\n            }\n            return $.noop;\n        }\n        function convertDatesArray(dates) {\n            var result = [];\n            for (var i = 0; i < dates.length; i++) {\n                result.push(dates[i].setHours(0, 0, 0, 0));\n            }\n            return result;\n        }\n        function createDisabledExpr(dates) {\n            var body, callback, disabledDates = [], days = [\n                    'su',\n                    'mo',\n                    'tu',\n                    'we',\n                    'th',\n                    'fr',\n                    'sa'\n                ], searchExpression = 'if (found) {' + ' return true ' + '} else {' + 'return false' + '}';\n            if (dates[0] instanceof DATE) {\n                disabledDates = convertDatesArray(dates);\n                body = 'var found = date && window.kendo.jQuery.inArray(date.setHours(0, 0, 0, 0),[' + disabledDates + ']) > -1;' + searchExpression;\n            } else {\n                for (var i = 0; i < dates.length; i++) {\n                    var day = dates[i].slice(0, 2).toLowerCase();\n                    var index = $.inArray(day, days);\n                    if (index > -1) {\n                        disabledDates.push(index);\n                    }\n                }\n                body = 'var found = date && window.kendo.jQuery.inArray(date.getDay(),[' + disabledDates + ']) > -1;' + searchExpression;\n            }\n            callback = new Function('date', body);\n            return callback;\n        }\n        function isEqualDate(oldValue, newValue) {\n            if (oldValue instanceof Date && newValue instanceof Date) {\n                oldValue = oldValue.getTime();\n                newValue = newValue.getTime();\n            }\n            return oldValue === newValue;\n        }\n        function toDateObject(link) {\n            var value = $(link).attr(kendo.attr(VALUE)).split('/');\n            value = createDate(value[0], value[1], value[2]);\n            return value;\n        }\n        calendar.isEqualDatePart = isEqualDatePart;\n        calendar.isEqualDate = isEqualDate;\n        calendar.makeUnselectable = makeUnselectable;\n        calendar.restrictValue = restrictValue;\n        calendar.isInRange = isInRange;\n        calendar.addClassToViewContainer = addClassToViewContainer;\n        calendar.normalize = normalize;\n        calendar.viewsEnum = views;\n        calendar.disabled = getDisabledExpr;\n        calendar.toDateObject = toDateObject;\n        calendar.getToday = getToday;\n        calendar.createDate = createDate;\n        kendo.calendar = calendar;\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}