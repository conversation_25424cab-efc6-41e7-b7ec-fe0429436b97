/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("util/text-metrics.min",["kendo.core.min"],e)}(function(){!function(e){function t(e){return(e+"").replace(s,l)}function i(e){var t,i=[];for(t in e)i.push(t+e[t]);return i.sort().join("")}function n(e){var t,i=2166136261;for(t=0;t<e.length;++t)i+=(i<<1)+(i<<4)+(i<<7)+(i<<8)+(i<<24),i^=e.charCodeAt(t);return i>>>0}function a(){return{width:0,height:0,baseline:0}}function o(e,t,i){return u.current.measure(e,t,i)}var r,s,l,d,c,u;window.kendo.util=window.kendo.util||{},r=kendo.Class.extend({init:function(e){this._size=e,this._length=0,this._map={}},put:function(e,t){var i=this._map,n={key:e,value:t};i[e]=n,this._head?(this._tail.newer=n,n.older=this._tail,this._tail=n):this._head=this._tail=n,this._length>=this._size?(i[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(e){var t=this._map[e];if(t)return t===this._head&&t!==this._tail&&(this._head=t.newer,this._head.older=null),t!==this._tail&&(t.older&&(t.older.newer=t.newer,t.newer.older=t.older),t.older=this._tail,t.newer=null,this._tail.newer=t,this._tail=t),t.value}}),s=/\r?\n|\r|\t/g,l=" ",d={baselineMarkerSize:1},"undefined"!=typeof document&&(c=document.createElement("div"),c.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),u=kendo.Class.extend({init:function(t){this._cache=new r(1e3),this.options=e.extend({},d,t)},measure:function(e,o,r){var s,l,d,u,h,p,f,v,m;if(void 0===r&&(r={}),!e)return a();if(s=i(o),l=n(e+s),d=this._cache.get(l))return d;u=a(),h=r.box||c,p=this._baselineMarker().cloneNode(!1);for(f in o)v=o[f],void 0!==v&&(h.style[f]=v);return m=r.normalizeText!==!1?t(e):e+"",h.textContent=m,h.appendChild(p),document.body.appendChild(h),m.length&&(u.width=h.offsetWidth-this.options.baselineMarkerSize,u.height=h.offsetHeight,u.baseline=p.offsetTop+this.options.baselineMarkerSize),u.width>0&&u.height>0&&this._cache.put(l,u),h.parentNode.removeChild(h),u},_baselineMarker:function(){var e=document.createElement("div");return e.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",e}}),u.current=new u,kendo.deepExtend(kendo.util,{LRUCache:r,TextMetrics:u,measureText:o,objectKey:i,hashKey:n,normalizeText:t})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("kendo.scheduler.min",["kendo.dropdownlist.min","kendo.editable.min","kendo.multiselect.min","kendo.window.min","kendo.datetimepicker.min","kendo.scheduler.recurrence.min","kendo.scheduler.view.min","kendo.scheduler.dayview.min","kendo.scheduler.agendaview.min","kendo.scheduler.monthview.min","kendo.scheduler.timelineview.min","kendo.dialog.min","kendo.pane.min","kendo.pdf.min","kendo.switch.min"],e)}(function(){return function(e,t){function i(e,t){return t=t||"",e.startTimezone&&(t=e.startTimezone,e.endTimezone&&(t+=" | "+e.endTimezone)),t}function n(e,t){var i=t.timezone;i&&(e[H.attr("timezone")]=i)}function a(e,t){var i,n=t.model.fields[t.field].validation;n&&(i=n.validDateValidator,i&&Q(i)&&i.message&&(e[H.attr("validDate-msg")]=i.message))}function o(e,t){var i,n=t.model.fields[t.field].validation;n&&(i=n.dateCompare,i&&Q(i)&&i.message&&(e[H.attr("dateCompare-msg")]=i.message))}function r(e,t){return function(i){return i=e(i),l(i,"apply",t),i||[]}}function s(e,t){return function(i){return i&&("[object Array]"===J.call(i)||i instanceof H.data.ObservableArray||(i=[i])),l(i,"remove",t,!0),i=e(i),i||[]}}function l(e,t,i,n){var a,o,r;for(e=e||[],o=0,r=e.length;o<r;o++)a=e[o],n?a.startTimezone||a.endTimezone?i?(a.start=H.timezone.convert(a.start,a.startTimezone||a.endTimezone,i),a.end=H.timezone.convert(a.end,a.endTimezone||a.startTimezone,i),a.start=H.timezone[t](a.start,i),a.end=H.timezone[t](a.end,i)):(a.start=H.timezone[t](a.start,a.startTimezone||a.endTimezone),a.end=H.timezone[t](a.end,a.endTimezone||a.startTimezone)):i&&(a.start=H.timezone[t](a.start,i),a.end=H.timezone[t](a.end,i)):a.startTimezone||a.endTimezone?(a.start=H.timezone[t](a.start,a.startTimezone||a.endTimezone),a.end=H.timezone[t](a.end,a.endTimezone||a.startTimezone),i&&(a.start=H.timezone.convert(a.start,a.startTimezone||a.endTimezone,i),a.end=H.timezone.convert(a.end,a.endTimezone||a.startTimezone,i))):i&&(a.start=H.timezone[t](a.start,i),a.end=H.timezone[t](a.end,i)),n&&delete a.uid;return e}function d(e,t){for(var i,n=e.length,a=0;a<n;a++)if(i=e[a],i.uid===t)return i}function c(e,t,i){return e=i?H.timezone.convert(e,t,i):H.timezone.remove(e,t)}function u(e){var t,i;return!(e.filter("[name=start]").length&&e.filter("[title=Start]").length||e.filter("[name=end]").length&&e.filter("[title=End]").length)||(i=H.widgetInstance(e,H.ui),i?(t=H.parseDate(e.val(),i.options.format),!!t&&i.value()):(t=H.parseDate(e.val()),!!t))}function h(e){var t,i,n,a,o,r,s,l,d,u,h,p;return!(e.filter("[name=end]").length&&(t=e.closest(".k-scheduler-edit-form"),i=t.find("[name=start]:visible"),n=t.find("[name=end]:visible"),n[0]&&i[0]&&(r=H.widgetInstance(i,H.ui),s=H.widgetInstance(n,H.ui),l=t.data("kendoEditable"),d=l?l.options.model:null,r&&s?(a=r.value(),o=s.value()):(a=H.parseDate(i.val()),o=H.parseDate(n.val())),a&&o)))||(d&&(u=i.attr(H.attr("timezone")),h=d.startTimezone,p=d.endTimezone,h=h||p,p=p||h,h&&(a=c(a,h,u),o=c(o,p,u))),a<=o)}function p(e,t,i,n){for(var a=e.length,o=[],r=0;r<a;r++)o=o.concat(e[r].expand(t,i,n));return o}function f(e){return delete e.name,delete e.prefix,delete e.remove,delete e.edit,delete e.add,delete e.navigate,e}function v(t,i){var n,a,o=(t.fields||t)[i],r=["url","email","number","date","boolean"],s=o?o.validation:{},l=H.attr("type"),d=e.inArray,c={};for(n in s)a=s[n],d(n,r)>=0?c[l]=n:H.isFunction(a)||(c[n]=Q(a)?a.value||n:a),c[H.attr(n+"-msg")]=a.message;return c}function m(t,i){var n=v(i,t.field);return function(a){e(H.format('<select data-{0}bind="value:{1}" title="'+i.title+'">',H.ns,t.field)).appendTo(a).attr(n).kendoDropDownList({dataTextField:t.dataTextField,dataValueField:t.dataValueField,dataSource:t.dataSource,valuePrimitive:t.valuePrimitive,optionLabel:"None",template:H.format('<span class="k-scheduler-mark" style="background-color:#= data.{0}?{0}:"none" #"></span>#={1}#',t.dataColorField,t.dataTextField)})}}function g(t,i){var n=v(i,t.field);return function(i){var a,o,r="",s=t.dataSource.view();for(a=0,o=s.length;a<o;a++)r+=H.format('<option value="{0}">{1}</option>',H.getter(t.dataValueField)(s[a]),H.getter(t.dataTextField)(s[a]));e(H.format('<select data-{0}bind="value:{1}">{2}</select>',H.ns,t.field,r,t.valuePrimitive)).appendTo(i).attr(n)}}function _(t){var i=v(t.model,t.field);return function(t,n){e('<textarea name="description" class="k-textbox" title="'+n.title+'"/>').attr(i).appendTo(t)}}function k(t,i){var n=v(i,t.field);return function(i){e(H.format('<select data-{0}bind="value:{1}">',H.ns,t.field)).appendTo(i).attr(n).kendoMultiSelect({dataTextField:t.dataTextField,dataValueField:t.dataValueField,dataSource:t.dataSource,valuePrimitive:t.valuePrimitive,itemTemplate:H.format('<span class="k-scheduler-mark" style="background-color:#= data.{0}?{0}:"none" #"></span>#={1}#',t.dataColorField,t.dataTextField),tagTemplate:H.format('<span class="k-scheduler-mark" style="background-color:#= data.{0}?{0}:"none" #"></span>#={1}#',t.dataColorField,t.dataTextField)})}}function w(t,i){var n=v(i,t.field);return function(i){var a,o,r="",s=t.dataSource.view();for(a=0,o=s.length;a<o;a++)r+=H.format('<option value="{0}">{1}</option>',H.getter(t.dataValueField)(s[a]),H.getter(t.dataTextField)(s[a]));e(H.format('<select data-{0}bind="value:{1}" multiple="multiple" data-{0}value-primitive="{3}">{2}</select>',H.ns,t.field,r,t.valuePrimitive)).appendTo(i).attr(n)}}function b(e,t){var i,n=e.end.getTime()-e.start.getTime(),a=new Date(e.start.getTime());return H.date.setTime(a,t),i=new Date(a.getTime()),H.date.setTime(i,n,!0),{start:a,end:i}}var y,z,T,D,S,x,E,C,R,P,M,V,H=window.kendo,B=H.date,I=B.MS_PER_DAY,A=B.getDate,W=H.date.getMilliseconds,O=H.recurrence,F=e.extend({F10:121},H.keys),N=H.ui,L=N.Widget,U=N.DataBoundWidget,K="string",q=N.Popup,Y=N.Calendar,j=H.data.DataSource,Q=e.isPlainObject,$=e.extend,X=e.proxy,J=Object.prototype.toString,Z=e.isArray,G=".kendoScheduler",ee="click",te="mousedown",ie=H.support.pointers?"pointerdown":"touchstart",ne=H.support.pointers?"pointermove":"touchmove",ae=H.support.pointers?"pointerup":"touchend",oe=H.support.mousemove,re="change",se="progress",le="error",de="cancel",ce="remove",ue="resetSeries",he="save",pe="add",fe="edit",ve="k-state-focused",me="k-state-expanded",ge=".k-scheduler-views",_e="k-event-inverse",ke=/(?:value:start|value:end)(?:,|$)/,we=A(new Date),be=",",ye=/\;/g,ze="recurrenceException",Te="Are you sure you want to delete this event?",De="Do you want to delete only this event occurrence or the whole series?",Se="Do you want to edit only this event occurrence or the whole series?",xe="Are you sure you want to delete this event occurrence?",Ee="Are you sure you want to reset the whole series?",Ce="Are you sure you want to delete the whole series?",Re='<a class="k-button #=className#" #=attr# href="\\#">#=text#</a>',Pe=H.template('<li class="k-current-view" data-#=ns#name="#=view#"><a role="button" href="\\#" class="k-link">${views[view].title}</a></li>'),Me=H.template('<div class="k-floatwrap k-header k-scheduler-toolbar"># if (pdf) { #<ul class="k-reset k-scheduler-tools"><li><a role="button" href="\\#" class="k-button k-pdf"><span class="k-icon k-i-file-pdf"></span>${messages.pdf}</a></li></ul># } #<ul class="k-reset k-scheduler-navigation"><li class="k-state-default k-header k-nav-today"><a role="button" href="\\#" class="k-link" title="${messages.today}">${messages.today}</a></li><li class="k-state-default k-header k-nav-prev"><a role="button" href="\\#" class="k-link" title="${messages.previous}" aria-label="${messages.previous}"><span class="k-icon k-i-arrow-60-left" style="pointer-events: none"></span></a></li><li class="k-state-default k-header k-nav-next"><a role="button" href="\\#" class="k-link" title="${messages.next}" aria-label="${messages.next}"><span class="k-icon k-i-arrow-60-right" style="pointer-events: none"></span></a></li><li class="k-state-default k-nav-current"><a role="button" href="\\#" class="k-link"><span class="k-icon k-i-calendar"></span><span class="k-sm-date-format" data-#=ns#bind="text: formattedShortDate"></span><span class="k-lg-date-format" data-#=ns#bind="text: formattedDate"></span></a></li></ul>#if(viewsCount === 1){#<a role="button" data-#=ns#name="#=view#" href="\\#" class="k-link k-scheduler-refresh"><span class="k-icon k-i-reload"></span></a>#}else{#<ul class="k-reset k-header k-scheduler-views">#for(var view in views){#<li class="k-state-default k-view-#= view.toLowerCase() #" data-#=ns#name="#=view#"><a role="button" href="\\#" class="k-link">${views[view].title}</a></li>#}#</ul>#}#</div>'),Ve=H.template('<div class="k-header k-scheduler-toolbar"><ul class="k-reset k-scheduler-tools"># if (pdf) { #<li><a role="button" href="\\#" class="k-button k-pdf"><span class="k-icon k-i-file-pdf"></span></a></li># } #<li><a role="button" href="\\#" class="k-button k-nav-calendar"><span class="k-icon k-i-calendar"></span></a></li># if (editable) { #<li><a role="button" href="\\#" class="k-button k-create-event"><span class="k-icon k-i-plus"></span></a></li># } #</ul>#if(viewsCount === 1){#<a role="button" data-#=ns#name="#=view#" href="\\#" class="k-link k-scheduler-refresh"><span class="k-icon k-i-reload"></span></a>#}else{#<select class="k-scheduler-mobile-views">#for(var view in views){#<option class="k-state-default k-view-#= view.toLowerCase() #" value="#=view#">${views[view].title}</option>#}#</select>#}#</div><div class="k-header k-scheduler-toolbar"><ul class="k-reset k-header k-scheduler-navigation"><li class="k-state-default k-nav-prev"><a role="button" href="\\#" class="k-link"><span class="k-icon k-i-arrow-chevron-left"></span></a></li><li class="k-state-default k-nav-current"><span class="k-m-date-format" data-#=ns#bind="text: formattedMobileDate"></span><span class="k-y-date-format" data-#=ns#bind="text: formattedYear"></span></li><li class="k-state-default k-nav-next"><a role="button" href="\\#" class="k-link"><span class="k-icon k-i-arrow-chevron-right"></span></a></li></ul></div>'),He=function(t,i){var r={name:i.field,title:i.title},s=i.model.isAllDay,l=H.attr("validate")+"='"+!s+"'",d=H.attr("validate")+"='"+s+"'";n(r,i),a(r,i),o(r,i),e('<input type="datetime-local" required '+H.attr("type")+'="datetime-local" '+H.attr("bind")+'="value:'+i.field+', invisible:isAllDay" '+l+"/>").attr(r).appendTo(t),e('<input type="date" required '+H.attr("type")+'="date" '+H.attr("bind")+'="value:'+i.field+',visible:isAllDay" '+d+"/>").attr(r).appendTo(t),e("<span "+H.attr("for")+'="'+i.field+'" class="k-invalid-msg"/>').hide().appendTo(t)},Be=function(t,i){var r={name:i.field,title:i.title},s=i.model.isAllDay,l=H.attr("validate")+"='"+!s+"' ",d=H.attr("validate")+"='"+s+"' ";n(r,i),a(r,i),o(r,i),e('<input type="text" required '+H.attr("type")+'="date" '+H.attr("role")+'="datetimepicker" '+H.attr("bind")+'="value:'+i.field+',invisible:isAllDay" '+l+"/>").attr(r).appendTo(t),e('<input type="text" required '+H.attr("type")+'="date" '+H.attr("role")+'="datepicker" '+H.attr("bind")+'="value:'+i.field+',visible:isAllDay" '+d+"/>").attr(r).appendTo(t),e("<span "+H.attr("bind")+'="text: '+i.field+'Timezone"></span>').appendTo(t),"end"===i.field&&e("<span "+H.attr("bind")+'="text: startTimezone, invisible: endTimezone"></span>').appendTo(t),e("<span "+H.attr("for")+'="'+i.field+'" class="k-invalid-msg"/>').hide().appendTo(t)},Ie=function(t,i){e("<div "+H.attr("bind")+'="value:'+i.field+'" />').attr({name:i.field}).appendTo(t).kendoRecurrenceEditor({start:i.model.start,timezone:i.timezone,messages:i.messages})},Ae=function(t,i){e("<div "+H.attr("bind")+'="value:'+i.field+'" />').attr({name:i.field}).appendTo(t).kendoMobileRecurrenceEditor({start:i.model.start,timezone:i.timezone,messages:i.messages,pane:i.pane,value:i.model[i.field]})},We=function(t,i){e('<input type="checkbox" data-role="switch"'+H.attr("bind")+'="value:'+i.field+'" />').appendTo(t)},Oe=function(t,n){var a=i(n.model,n.messages.noTimezone);e('<span class="k-timezone-label"></span>').text(a).appendTo(t),e('<span class="k-icon k-i-arrow-chevron-right"></span>').appendTo(t),t.closest("li.k-item label").click(n.click)},Fe=function(t,i){e('<a href="#" class="k-button" data-bind="invisible:isAllDay">'+i.messages.timezoneEditorButton+"</a>").click(i.click).appendTo(t)},Ne=function(t,i){e('<div class="k-mobiletimezoneeditor" '+H.attr("bind")+'="value:'+i.field+'" />').attr({name:i.field}).appendTo(t).kendoMobileTimezoneEditor({optionLabel:i.noTimezone})},Le=function(i,n){var a=n.visible||n.visible===t;e("<div "+H.attr("bind")+'="value:'+n.field+'" />').attr({name:n.field}).toggle(a).appendTo(i).kendoTimezoneEditor({optionLabel:n.noTimezone,title:n.title})},Ue=H.Class.extend({init:function(t,i){var n=t.timezone;this.reader=i,i.model&&(this.model=i.model),this.timezone=n,this.data=r(e.proxy(this.data,this),n),this.serialize=s(e.proxy(this.serialize,this),n)},errors:function(e){return this.reader.errors(e)},parse:function(e){return this.reader.parse(e)},data:function(e){return this.reader.data(e)},total:function(e){return this.reader.total(e)},groups:function(e){return this.reader.groups(e)},aggregates:function(e){return this.reader.aggregates(e)},serialize:function(e){return this.reader.serialize(e)}}),Ke=H.data.Model.define({init:function(e){var t=this;H.data.Model.fn.init.call(t,e),t._defaultId=t.defaults[t.idField]},_time:function(e){var t=this[e],i="_"+e+"Time";return this[i]?this[i]-H.date.toUtcTime(H.date.getDate(t)):W(t)},_date:function(e){var t="_"+e+"Time";return this[t]?this[t]-this._time(e):H.date.getDate(this[e])},clone:function(t,i){var n=this.uid,a=new this.constructor(e.extend({},this.toJSON(),t));return i||(a.uid=n),a},duration:function(){var e=this.end,t=this.start,i=(e.getTimezoneOffset()-t.getTimezoneOffset())*H.date.MS_PER_MINUTE;return e-t-i},expand:function(e,t,i){return O?O.expand(this,e,t,i):[this]},update:function(e){for(var t in e)this.set(t,e[t]);this._startTime&&this.set("_startTime",H.date.toUtcTime(this.start)),this._endTime&&this.set("_endTime",H.date.toUtcTime(this.end))},isMultiDay:function(){return this.isAllDay||this.duration()>=H.date.MS_PER_DAY},isException:function(){return!this.isNew()&&this.recurrenceId},isOccurrence:function(){return this.isNew()&&this.recurrenceId},isRecurring:function(){return!(!this.recurrenceRule&&!this.recurrenceId)},isRecurrenceHead:function(){return!(!this.id||!this.recurrenceRule)},toOccurrence:function(t){return t=e.extend(t,{recurrenceException:null,recurrenceRule:null,recurrenceId:this.id||this.recurrenceId}),t[this.idField]=this.defaults[this.idField],this.clone(t,!0)},toJSON:function(){var e=H.data.Model.fn.toJSON.call(this);return e.uid=this.uid,delete e._startTime,delete e._endTime,e},shouldSerialize:function(e){return H.data.Model.fn.shouldSerialize.call(this,e)&&"_defaultId"!==e},set:function(e,t){var i,n,a,o=this.isAllDay||!1;H.data.Model.fn.set.call(this,e,t),"isAllDay"==e&&t!=o&&(i=H.date.getDate(this.start),n=new Date(this.end),a=H.date.getMilliseconds(n),0===a&&t&&(a=I),this.set("start",i),t===!0?(H.date.setTime(n,-a),n<i&&(n=i)):H.date.setTime(n,I-a),this.set("end",n))},id:"id",fields:{id:{type:"number"},title:{defaultValue:"",type:"string"},start:{type:"date",validation:{required:!0,validDate:{value:u}}},startTimezone:{type:"string"},end:{type:"date",validation:{required:!0,validDate:{value:u},dateCompare:{value:h}}},endTimezone:{type:"string"},recurrenceRule:{defaultValue:"",type:"string"},recurrenceException:{defaultValue:"",type:"string"},isAllDay:{type:"boolean",defaultValue:!1},description:{type:"string"}}}),qe=j.extend({init:function(e){j.fn.init.call(this,$(!0,{},{schema:{modelBase:Ke,model:Ke}},e)),this.reader=new Ue(this.options.schema,this.reader)},expand:function(e,t){var i,n=this.view(),a={};return e&&t&&(i=t.getTimezoneOffset(),t=new Date(t.getTime()+I-1),t.getTimezoneOffset()!==i&&(t=H.timezone.apply(t,i)),a={logic:"or",filters:[{logic:"and",filters:[{field:"start",operator:"gte",value:e},{field:"end",operator:"gte",value:e},{field:"start",operator:"lte",value:t}]},{logic:"and",filters:[{field:"start",operator:"lte",value:new Date(e.getTime()+I-1)},{field:"end",operator:"gte",value:e}]}]},n=new H.data.Query(p(n,e,t,this.reader.timezone)).filter(a).toArray()),n},cancelChanges:function(e){e&&e.isOccurrence()&&this._removeExceptionDate(e),j.fn.cancelChanges.call(this,e)},insert:function(e,t){if(t){if(!(t instanceof Ke)){var i=t;t=this._createNewModel(),t.accept(i)}return(!this._pushCreated&&t.isRecurrenceHead()||t.recurrenceId)&&(t=t.recurrenceId?t:t.toOccurrence(),this._addExceptionDate(t)),j.fn.insert.call(this,e,t)}},pushCreate:function(e){this._pushCreated=!0,j.fn.pushCreate.call(this,e),this._pushCreated=!1},remove:function(e){return e.isRecurrenceHead()?this._removeExceptions(e):e.isRecurring()&&this._addExceptionDate(e),j.fn.remove.call(this,e)},_removeExceptions:function(e){for(var t=this.data().slice(0),i=t.shift(),n=e.id;i;)i.recurrenceId===n&&j.fn.remove.call(this,i),i=t.shift();e.set(ze,"")},_removeExceptionDate:function(e){var t,i,n,a;e.recurrenceId&&(t=this.get(e.recurrenceId),t&&(i=e.defaults.start,n=RegExp("(\\"+be+"?)"+O.toExceptionString(i,this.reader.timezone)),a=(t.recurrenceException||"").replace(ye,be).replace(/\,$/,""),n.test(a)?t.set(ze,a.replace(n,"")):(i=e.start,n=RegExp("(\\"+be+"?)"+O.toExceptionString(i,this.reader.timezone)),t.set(ze,a.replace(n,"")))))},_addExceptionDate:function(e){var t,i=e.start,n=this.reader.timezone,a=this.get(e.recurrenceId),o=(a.recurrenceException||"").replace(ye,be).replace(/\,$/,"");O.isException(o,i,n)||(t=O.toExceptionString(i,n),e.defaults.start=i,a.set(ze,o+(o&&t?be:"")+t))}});qe.create=function(e){(Z(e)||e instanceof H.data.ObservableArray)&&(e={data:e});var t=e||{},i=t.data;if(t.data=i,!(t instanceof qe)&&t instanceof H.data.DataSource)throw Error("Incorrect DataSource type. Only SchedulerDataSource instances are supported");return t instanceof qe?t:new qe(t)},$(!0,H.data,{SchedulerDataSource:qe,SchedulerDataReader:Ue,SchedulerEvent:Ke}),y={update:{text:"Save",className:"k-primary k-scheduler-update"},canceledit:{text:"Cancel",className:"k-scheduler-cancel"},destroy:{text:"Delete",imageClass:"k-i-close",className:"k-primary k-scheduler-delete",iconClass:"k-icon"}},z={mobile:{dateRange:He,timezonePopUp:Oe,timezone:Ne,recurrence:Ae,description:_,multipleResources:w,resources:g,isAllDay:We},desktop:{dateRange:Be,timezonePopUp:Fe,timezone:Le,recurrence:Ie,description:_,multipleResources:k,resources:m}},T=H.Observable.extend({init:function(e,t){H.Observable.fn.init.call(this),this.element=e,this.options=$(!0,{},this.options,t),this.createButton=this.options.createButton,this.toggleDateValidationHandler=X(this._toggleDateValidation,this)},_toggleDateValidation:function(t){if("isAllDay"==t.field){var i,n,a,o=this.container,r=this.editable.options.model.isAllDay,s=H.attr("bind");o.find("["+s+"*=end],["+s+"*=start]").each(function(){i=e(this),ke.test(i.attr(s))&&(n=i.is("["+H.attr("role")+"=datetimepicker],[type*=datetime]"),a=r!==n,i.attr(H.attr("validate"),a))})}},fields:function(e,t){var i,n,a=this,o=a.options.messages,r=a.options.timezone,s=function(e){e.preventDefault(),a._initTimezoneEditor(t,this)},l=[{field:"title",title:o.editor.title},{field:"start",title:o.editor.start,editor:e.dateRange,timezone:r},{field:"end",title:o.editor.end,editor:e.dateRange,timezone:r},{field:"isAllDay",title:o.editor.allDayEvent,editor:e.isAllDay}];H.timezone.windows_zones&&(l.push({field:"timezone",title:o.editor.timezone,editor:e.timezonePopUp,click:s,messages:o.editor,model:t}),l.push({field:"startTimezone",title:o.editor.startTimezone,editor:e.timezone,noTimezone:o.editor.noTimezone}),l.push({field:"endTimezone",title:o.editor.endTimezone,editor:e.timezone,noTimezone:o.editor.noTimezone})),t.recurrenceId||l.push({field:"recurrenceRule",title:o.editor.repeat,editor:e.recurrence,timezone:r,messages:o.recurrenceEditor,pane:this.pane}),"description"in t&&l.push({field:"description",title:o.editor.description,editor:e.description({model:t,field:"description"})});for(i=0;i<this.options.resources.length;i++)n=this.options.resources[i],l.push({field:n.field,title:n.title,editor:n.multiple?e.multipleResources(n,t):e.resources(n,t)});return l},end:function(){return this.editable.end()},_buildDesktopEditTemplate:function(e,t,i){var n,a,o,r,s=this.options.messages,l=$({},H.Template,this.options.templateSettings),d=l.paramName,c="";for(n=0,a=t.length;n<a;n++)o=t[n],"startTimezone"===o.field&&(c+='<div class="k-popup-edit-form k-scheduler-edit-form k-scheduler-timezones" style="display:none">',c+='<div class="k-edit-form-container">',c+='<div class="k-edit-label"></div>',c+='<div class="k-edit-field"><label class="k-check"><input class="k-timezone-toggle" type="checkbox" />'+s.editor.separateTimezones+"</label></div>"),c+='<div class="k-edit-label"><label for="'+o.field+'">'+(o.title||o.field||"")+"</label></div>",!e.editable||e.editable(o.field)?(i.push(o),c+="<div "+H.attr("container-for")+'="'+o.field+'" class="k-edit-field"></div>'):(r="#:",o.field?(o=H.expr(o.field,d),r+=o+"==null?'':"+o):r+="''",r+="#",r=H.template(r,l),c+='<div class="k-edit-field">'+r(e)+"</div>"),"endTimezone"===o.field&&(c+=this._createEndTimezoneButton());return c},_buildMobileEditTemplate:function(e,t,i){var n,a,o,r,s=this.options.messages,l=$({},H.Template,this.options.templateSettings),d=l.paramName,c="";for(c+="<ul>",n=0,a=t.length;n<a;n++)o=t[n],"timezone"!==o.field&&"recurrenceRule"!==o.field||(c+="</ul><ul>"),"startTimezone"===o.field&&(c+='<div class="k-popup-edit-form k-scheduler-edit-form k-scheduler-timezones" style="display:none">',c+='<ul><li class="k-item"><label class="k-label">',c+='<span class="k-item-title">'+s.editor.separateTimezones+"</span>",c+='<input class="k-timezone-toggle" data-role="switch" type="checkbox">',c+="</label></li>"),!e.editable||e.editable(o.field)?(c+='<li class="k-item">',c+="timezone"===o.field?'<label class="k-label" data-bind="css: { k-state-disabled: isAllDay }">':'<label class="k-label">',c+='<span class="k-item-title">'+(o.title||o.field||"")+"</span>",i.push(o),c+="<div "+H.attr("container-for")+'="'+o.field+'"></div>'):(r="#:",c+='<li class="k-item">',c+='<label class="k-label k-no-click">',c+='<span class="k-item-title">'+(o.title||o.field||"")+"</span>",o.field?(o=H.expr(o.field,d),r+=o+"==null?'':"+o):r+="''",r+="#",r=H.template(r,l),c+='<span class="k-no-editor">'+r(e)+"</span>"),c+="</label></li>","recurrenceRule"===o.field&&(c+="</ul><ul>"),"endTimezone"===o.field&&(c+=this._createEndTimezoneButton());return c+="</ul>"},_buildEditTemplate:function(e,t,i,n){var a=$({},H.Template,this.options.templateSettings),o=this.options.editable.template,r="";return o?(typeof o===K&&(o=window.unescape(o)),r+=H.template(o,a)(e)):r+=n?'<div data-role="content">'+this._buildMobileEditTemplate(e,t,i)+"</div>":this._buildDesktopEditTemplate(e,t,i),r},_createEndTimezoneButton:function(){return"</ul></div>"},_revertTimezones:function(e){e.set("startTimezone",this._startTimezone),e.set("endTimezone",this._endTimezone),delete this._startTimezone,delete this._endTimezone}}),D=T.extend({init:function(){T.fn.init.apply(this,arguments),this.pane=H.Pane.wrap(this.element,{viewEngine:{viewOptions:{renderOnInit:!0,wrap:!1,wrapInSections:!0,detachOnHide:!1,detachOnDestroy:!1}}}),this.pane.element.parent().css("height",this.options.height),this.view=this.pane.view()},options:{animations:{left:"slide",right:"slide:right"}},destroy:function(){this.close(),this.unbind(),this.pane.destroy()},_initTimezoneEditor:function(t){var n,a=this,o=a.pane,r=a.options.messages,s=a.timezoneView,l=s?s.content.find(".k-scheduler-timezones"):a.container.find(".k-scheduler-timezones"),d=l.find("input.k-timezone-toggle").data("kendoSwitch"),c=l.find("li.k-item:not(.k-zonepicker):last"),u=function(e){if("startTimezone"===e.field){var i=t.startTimezone;d.enable(i),i||(c.hide(),t.set("endTimezone",""),d.value(!1))}};a._startTimezone=t.startTimezone||"",a._endTimezone=t.endTimezone||"",s||(n='<div data-role="view" class="k-popup-edit-form k-scheduler-edit-form k-mobile-list"><div data-role="header" class="k-header"><a href="\\#" class="k-header-cancel k-scheduler-cancel k-link" title="'+r.cancel+'"aria-label="'+r.cancel+'"><span class="k-icon k-i-arrow-chevron-left"></span></a>'+r.editor.timezoneTitle+'<a href="\\#" class="k-header-done k-scheduler-update k-link" title="'+r.save+'" aria-label="'+r.save+'"><span class="k-icon k-i-check"></span></a></div><div data-role="content"></div>',this.timezoneView=s=o.append(n),s.contentElement.append(l.show()),s.element.on(ee+G,".k-scheduler-cancel, .k-scheduler-update",function(n){var s,l;n.preventDefault(),n.stopPropagation(),e(this).hasClass("k-scheduler-cancel")&&a._revertTimezones(t),t.unbind("change",u),s=a._editPane,l=i(t,r.editor.noTimezone),s.content.find(".k-timezone-label").text(l),o.navigate(s,a.options.animations.right)}),d.bind("change",function(e){c.toggle(e.checked),t.set("endTimezone","")}),t.bind("change",u)),d.value(!!t.endTimezone),d.enable(!!t.startTimezone),t.endTimezone?c.show():c.hide(),o.navigate(s,a.options.animations.left)},showDialog:function(t){var i=t.buttons.map(function(e){return{text:e.text,action:e.click}});i.push({text:this.options.messages.cancel,primary:!0}),e("<div />").appendTo(document.body).kendoDialog({close:function(){this.destroy()},modal:{preventScroll:!0},closable:!1,title:!1,content:t.text,actions:i})},editEvent:function(t){var i,n,a,o,r,s,l,d=this.pane,c="",u=this.options.messages,h=u.save,p=u.destroy,f=u.cancel,v=u.editor.editorTitle,m=u.resetSeries;return c+='<div data-role="view" class="k-popup-edit-form k-scheduler-edit-form k-mobile-list"'+H.attr("uid")+'="'+t.uid+'"><div data-role="header" class="k-header"><a href="\\#" class="k-header-cancel k-scheduler-cancel k-link" title="'+f+'"aria-label="'+f+'"><span class="k-icon k-i-arrow-chevron-left"></span></a>'+v+'<a href="\\#" class="k-header-done k-scheduler-update k-link" title="'+h+'" aria-label="'+h+'"><span class="k-icon k-i-check"></span></a></div>',i=this.fields(z.mobile,t),n=this,a=[],c+=this._buildEditTemplate(t,i,a,!0),c+="</div>",o=d.append(c),!t.isNew()&&this.options.editable&&this.options.editable.destroy!==!1&&t.isRecurrenceHead()&&t.recurrenceException&&(r='<ul class="k-edit-buttons"><li class="k-item"><span href="#" class="k-scheduler-resetSeries k-label" aria-label="'+m+'">'+m+"</span></li></ul>",o.contentElement.append(r)),!t.isNew()&&this.options.editable&&this.options.editable.destroy!==!1&&(s='<ul class="k-edit-buttons"><li class="k-item"><span href="#" class="k-scheduler-delete k-label" aria-label="'+p+'">'+p+"</span></li></ul>",o.contentElement.append(s)),this._editPane=o,l=this.container=o.element,this.editable=l.kendoEditable({fields:a,model:t,clearContainer:!1,target:n.options.target,validateOnBlur:!0}).data("kendoEditable"),this.trigger("edit",{container:l,model:t})?this.trigger("cancel",{container:l,model:t}):(l.on(ee+G,"a.k-scheduler-edit, a.k-scheduler-cancel, a.k-scheduler-update, span.k-scheduler-delete, span.k-scheduler-resetSeries",function(i){var a,o;i.preventDefault(),i.stopPropagation(),a=e(this),a.hasClass("k-scheduler-edit")?d.navigate(this._editPane,n.options.animations.right):(o="cancel",a.hasClass("k-scheduler-update")?o="save":a.hasClass("k-scheduler-delete")?o="remove":a.hasClass("k-scheduler-resetSeries")&&(o=ue),n.trigger(o,{container:l,model:t}))}),d.navigate(o,n.options.animations.left),t.bind("change",n.toggleDateValidationHandler)),this.editable},_views:function(){return this.pane.element.find(H.roleSelector("view")).not(this.view.element)},close:function(){var e,t,i,n;if(this.container){for(this.pane.navigate("",this.options.animations.right),e=this._views(),i=0,n=e.length;i<n;i++)t=e.eq(i).data("kendoView"),t&&t.purge();e.remove(),this.container=null,this.editable&&(this.editable.options.model.unbind("change",this.toggleDateValidationHandler),this.editable.destroy(),this.editable=null),this.timezoneView=null}}}),S=T.extend({destroy:function(){this.close(),this.unbind()},editEvent:function(t){var i,n,a,o=this,r=o.options.editable,s="<div "+H.attr("uid")+'="'+t.uid+'" class="k-popup-edit-form k-scheduler-edit-form"><div class="k-edit-form-container">',l=o.options.messages,d=l.save,c=l.cancel,u=l.destroy,h=l.resetSeries,p=this.fields(z.desktop,t),f=[];return s+=this._buildEditTemplate(t,p,f,!1),n=Q(r)?r.window:{},s+='<div class="k-edit-buttons k-state-default">',s+=this.createButton({name:"update",text:d,attr:i})+this.createButton({name:"canceledit",text:c,attr:i}),!t.isNew()&&r.destroy!==!1&&t.isRecurrenceHead()&&t.recurrenceException&&(s+=this.createButton({name:"resetSeries",text:h,attr:i})),t.isNew()||r.destroy===!1||(s+=this.createButton({name:"delete",text:u,attr:i})),s+="</div></div></div>",a=this.container=e(s).appendTo(o.element).eq(0).kendoWindow($({modal:!0,resizable:!1,draggable:!0,title:l.editor.editorTitle,visible:!1,close:function(e){e.userTriggered&&o.trigger(de,{container:a,model:t})&&e.preventDefault()}},n)),o.editable=a.kendoEditable({fields:f,model:t,clearContainer:!1,validateOnBlur:!0,target:o.options.target}).data("kendoEditable"),o.trigger(fe,{container:a,model:t})?o.trigger(de,{container:a,model:t}):(a.data("kendoWindow").center().open(),a.on(ee+G,"a.k-scheduler-cancel",function(e){e.preventDefault(),e.stopPropagation(),o.trigger(de,{container:a,model:t})}),a.on(ee+G,"a.k-scheduler-update",function(e){e.preventDefault(),e.stopPropagation(),
o.trigger("save",{container:a,model:t})}),a.on(ee+G,"a.k-scheduler-delete",function(e){e.preventDefault(),e.stopPropagation(),o.trigger(ce,{container:a,model:t})}),a.on(ee+G,"a.k-scheduler-resetSeries",function(e){e.preventDefault(),e.stopPropagation(),o.trigger(ue,{container:a,model:t})}),H.cycleForm(a),t.bind("change",o.toggleDateValidationHandler)),o.editable},close:function(){var e=this,t=function(){e.editable&&(e.editable.options.model.unbind("change",e.toggleDateValidationHandler),e.editable.destroy(),e.editable=null,e.container=null),e.popup&&(e.popup.destroy(),e.popup=null)};e.editable?(e._timezonePopup&&e._timezonePopup.data("kendoWindow")&&(e._timezonePopup.data("kendoWindow").destroy(),e._timezonePopup=null),e.container.is(":visible")?e.container.data("kendoWindow").bind("deactivate",t).close():t()):t()},_createEndTimezoneButton:function(){var e=this.options.messages,t="";return t+='<div class="k-edit-buttons k-state-default">',t+=this.createButton({name:"savetimezone",text:e.save})+this.createButton({name:"canceltimezone",text:e.cancel}),t+="</div></div></div>"},showDialog:function(t){var i,n,a,o=H.format("<div class='k-popup-edit-form'><div class='k-edit-form-container'><p class='k-popup-message'>{0}</p>",t.text);for(o+='<div class="k-edit-buttons k-state-default">',i=0;i<t.buttons.length;i++)o+=this.createButton(t.buttons[i]);o+="</div></div></div>",n=this.element,this.popup&&this.popup.destroy(),a=this.popup=e(o).appendTo(n).eq(0).on(ee,".k-button",function(i){i.preventDefault(),a.close();var n=e(i.currentTarget).index();t.buttons[n].click()}).kendoWindow({modal:!0,resizable:!1,draggable:!1,title:t.title,visible:!1,close:function(){this.destroy(),n.focus()}}).getKendoWindow(),a.center().open()},_initTimezoneEditor:function(e,t){var i,n=this,a=n.container.find(".k-scheduler-timezones"),o=a.find("input.k-timezone-toggle"),r=a.find(".k-edit-label:last").add(a.find(".k-edit-field:last")),s=a.find(".k-scheduler-savetimezone"),l=a.find(".k-scheduler-canceltimezone"),d=n._timezonePopup,c=function(t){if("startTimezone"===t.field){var i=e.startTimezone;o.prop("disabled",!i),i||(r.hide(),e.set("endTimezone",""),o.prop("checked",!1))}};n._startTimezone=e.startTimezone,n._endTimezone=e.endTimezone,d||(n._timezonePopup=d=a.kendoWindow({modal:!0,resizable:!1,draggable:!0,title:n.options.messages.editor.timezoneEditorTitle,visible:!1,close:function(i){e.unbind("change",c),i.userTriggered&&n._revertTimezones(e),t&&t.focus()}}),o.click(function(){r.toggle(o.prop("checked")),e.set("endTimezone","")}),s.click(function(e){e.preventDefault(),i.close()}),l.click(function(t){t.preventDefault(),n._revertTimezones(e),i.close()}),e.bind("change",c)),o.prop("checked",e.endTimezone).prop("disabled",!e.startTimezone),e.endTimezone?r.show():r.hide(),i=d.data("kendoWindow"),i.center().open()}}),x=U.extend({init:function(t,i){var n=this;L.fn.init.call(n,t,i),n.options.views&&n.options.views.length||(n.options.views=["day","week"]),n.resources=[],n._initModel(),n._wrapper(),n._views(),n._toolbar(),n._dataSource(),n._resources(),n._resizeHandler=function(){n.resize()},n.wrapper.on(te+G+" selectstart"+G,function(t){e(t.target).is(":kendoFocusable")||t.preventDefault()}),n.options.editable&&n.options.editable.resize!==!1&&n._resizable(),n._movable(),n._bindResize(),n.options.messages&&n.options.messages.recurrence&&(O.options=n.options.messages.recurrence),n._selectable(),n._touchHandlers(),n._ariaId=H.guid(),n._createEditor()},_bindResize:function(){e(window).on("resize"+G,this._resizeHandler)},_unbindResize:function(){e(window).off("resize"+G,this._resizeHandler)},dataItems:function(){var i,n,a,o,r,s=this,l=s.items(),d=s._data,c=e.map(l,function(t){return e(t).attr("data-uid")}),u={},h=c.length;for(i=0;i<h;i++)u[c[i]]=null;for(a=d.length,i=0;i<a;i++)o=d[i],u[o.uid]!==t&&(u[o.uid]=o);r=[];for(n in u)r.push(u[n]);return r},_isMobile:function(){var e=this.options;return e.mobile===!0&&H.support.mobileOS||"phone"===e.mobile||"tablet"===e.mobile},_isTouch:function(e){return/touch/.test(e.type)||e.originalEvent&&/touch/.test(e.originalEvent.pointerType)},_isInverseColor:function(e){return e.hasClass(_e)},_groupsByResource:function(e,t,i,n,a){var o,r,s,l,d,c,u;if(i||(i=[]),o=e[0]){for(s=o.dataSource.view(),l=0,d=0;d<s.length;d++)c=H.getter(o.dataValueField)(s[d]),u=t+l+d,r=this._groupsByResource(e.slice(1),u,i,c,o.field),r[o.field]=c,l=r.groupIndex,a&&n&&(r[a]=n),1===e.length&&(r.groupIndex=t+d,i.push(r));return r}return{}},data:function(){return this._data},select:function(i){var n,a,o,r,s,l,d,c,u,h,p=this,f=p.view(),v=p._selection,m=f.groups;if(i===t)return o=f._selectedSlots,v?(v&&v.events&&(a=p._selectedEvents()),{start:v.start,end:v.end,events:a,slots:o,resources:f._resourceBySlot(v)}):[];if(!i)return p._selection=null,p._old=null,f.clearSelection(),t;if(e.isArray(i)&&(i={events:i.splice(0)}),i.resources){s=[],l=[],f.groupedResources&&p._groupsByResource(f.groupedResources,0,l);for(r in i.resources)s.push({field:r,operator:"eq",value:i.resources[r]});n=new H.data.Query(l).filter(s).toArray()}return i.events&&i.events.length?(p._selectEvents(i.events,n),p._select(),t):(m&&i.start&&i.end&&(d=A(f._startDate),c=H.date.addDays(A(f._endDate),1),i.start<c&&d<=i.end&&(u=n&&n.length?m[n[0].groupIndex]:m[0],u.timeSlotCollectionCount()||(i.isAllDay=!0),h=u.ranges(i.start,i.end,i.isAllDay,!1),h.length&&(p._selection={start:H.timezone.toLocalDate(h[0].start.start),end:H.timezone.toLocalDate(h[h.length-1].end.end),groupIndex:h[0].start.groupIndex,index:h[0].start.index,isAllDay:h[0].start.isDaySlot,events:[]},p._select()))),t)},_selectEvents:function(e,t){var i,n,a,o,r,s,l,d,c=this,u=c.view(),h=u.groups,p=e.length,f=t&&t.length;for(i=0;i<p;i++)if(h&&f){for(n=h[t[0].groupIndex],a=[],o=n.timeSlotCollectionCount(),r=n.daySlotCollectionCount(),s=0;s<o;s++)a=a.concat(n.getTimeSlotCollection(s).events());for(l=0;l<r;l++)a=a.concat(n.getDaySlotCollection(l).events());a=new H.data.Query(a).filter({field:"element[0].getAttribute('data-uid')",operator:"eq",value:e[i]}).toArray(),a[0]&&c._createSelection(a[0].element)}else d=u.element.find(H.format(".k-event[data-uid={0}], .k-task[data-uid={0}]",e[i])),d.length&&c._createSelection(d[0])},_touchHandlers:function(){var t,i,n,a,o,r=this,s=r.wrapper,l=e.proxy(r._touchMove,r);s.on(ie+G,".k-scheduler-header-all-day td, .k-scheduler-content td, .k-event",function(e){var n=r.wrapper.find(".k-scheduler-content");r._isTouch(e)&&(n.stop(!0,!1),r._touchPosX=t=r._tapPosition(e,"X"),r._touchPosY=i=r._tapPosition(e,"Y"),r._userTouched=!0,r.view()._scrolling=!1,o=Date.now(),s.on(ne+G,".k-scheduler-header-all-day td, .k-scheduler-content td, .k-event",l))}),s.on(ae+G,".k-scheduler-header-all-day td, .k-scheduler-content td, .k-event",function(e){var d,c,u;r._isTouch(e)&&(d=Date.now()-o,c=r.wrapper.find(".k-scheduler-content"),u=-r._amplitude*(3e3/d),n=r._tapPosition(e,"X"),a=r._tapPosition(e,"Y"),r._dragging||(r.options.selectable&&(Math.abs(n-t)<=10||Math.abs(a-i)<=10)&&r._mouseDownSelection(e),!H.support.kineticScrollNeeded&&d<200&&Math.abs(n-t)>10&&c.animate({scrollTop:c[0].scrollTop+u}),s.off(ne+G,".k-scheduler-header-all-day td, .k-scheduler-content td",l)))})},_selectable:function(){var t,i=this,n=i.wrapper;i.options.selectable&&(i._tabindex(),n.on(te+G,".k-scheduler-header-all-day td, .k-scheduler-content td, .k-event",function(e){i._isTouch(e)||i._mouseDownSelection(e)}),t=e.proxy(i._mouseMove,i),n.on(te+G,".k-scheduler-header-all-day td, .k-scheduler-content td",function(e){var a=e.which,o=e.button,r=a&&3===a||o&&2==o;i._isTouch(e)||r||n.on(oe+G,".k-scheduler-header-all-day td, .k-scheduler-content td",t)}),n.on("mouseup"+G+" mousecancel"+G,function(){n.off(oe+G,".k-scheduler-header-all-day td, .k-scheduler-content td",t)}),n.on("focus"+G,function(){i._selection||i._userTouched||i._selectFirstSlot(),i._select()}),n.on("focusout"+G,function(t){i._ctrlKey=i._shiftKey=!1,i.toolbar.find("ul > li").removeClass(ve),e(t.relatedTarget).closest(ge).length||i.toolbar.find(ge).removeClass(me)}),n.on("keydown"+G,X(i._keydown,i)),n.on("keyup"+G,function(e){i._ctrlKey=e.ctrlKey,i._shiftKey=e.shiftKey}))},_mouseDownSelection:function(e){var t=e.which,i=e.button,n=t&&3===t||i&&2==i;n||(e.ctrlKey&&(this._ctrlKey=e.ctrlKey),e.shiftKey&&(this._shiftKey=e.shiftKey),this._createSelection(e.currentTarget)),H._activeElement()!==this.wrapper.get(0)?H.focusElement(this.wrapper):this._select(),this.toolbar.find("ul > li").removeClass(ve)},_selectFirstSlot:function(){this._createSelection(this.wrapper.find(".k-scheduler-content").find("td:first"))},_select:function(){var t,i,n,a,o,r=this,s=r.view(),l=r.wrapper,d=s.current(),c=r._selection,u=r._old?r._old.selection:null,h=r._old?r._old.eventsLength:null;if(c&&(d&&(d.removeAttribute("id"),d.removeAttribute("aria-label"),l.removeAttr("aria-activedescendant")),s.select(c),d=s.current(),d&&(u!==d||c.events&&h!==c.events.length))){if(t=e(d).data("uid"),r._old&&t&&t===e(r._old.selection).data("uid")&&c.events&&r._old.eventsLength===c.events.length)return;n=c,a=r._selectedEvents(),o=s._selectedSlots,a[0]?(n=a[0]||c,i=H.format(r.options.messages.ariaEventLabel,n.title,n.start,n.start)):i=H.format(r.options.messages.ariaSlotLabel,n.start,n.end),d.setAttribute("id",r._ariaId),d.setAttribute("aria-label",i),l.attr("aria-activedescendant",r._ariaId),r._old={selection:d,eventsLength:a.length},r.trigger("change",{start:c.start,end:c.end,events:a,slots:o,resources:s._resourceBySlot(c)})}},_selectedEvents:function(){for(var e,t=this._selection.events,i=t.length,n=0,a=[];n<i;n++)e=this.occurrenceByUid(t[n]),e&&a.push(e);return a},_tapPosition:function(e,t){return/touch/.test(e.type)?(e.originalEvent||e).changedTouches[0]["page"+t]:e["page"+t]},_touchMove:function(e){var t=this,i=t.wrapper.find(".k-scheduler-content"),n=i[0].scrollHeight>i[0].clientHeight,a=i[0].scrollWidth>i[0].clientWidth,o=t._tapPosition(e,"Y"),r=t._tapPosition(e,"X"),s=i[0].scrollTop-Math.round(o-t._touchPosY),l=i[0].scrollLeft-Math.round(r-t._touchPosX),d=n&&Math.abs(o-t._touchPosY)>10,c=a&&Math.abs(o-t._touchPosY)>10;t._dragging||H.support.kineticScrollNeeded||!t._isTouch(e)||(d||c)&&(t._amplitude=Math.round(o-t._touchPosY),t._touchPosY=o,t._touchPosX=r,i.animate({scrollTop:s,scrollLeft:l},0),t.view()._scrolling=!0)},_mouseMove:function(t){var i=this;clearTimeout(i._moveTimer),i._isTouch(t)||(i._moveTimer=setTimeout(function(){var n,a,o,r=i.view(),s=i._selection;s&&(n=r.selectionByElement(e(t.currentTarget)),n&&s.groupIndex===n.groupIndex&&(a=n.startDate(),o=n.endDate(),a>=s.end?s.backward=!1:o<=s.start&&(s.backward=!0),s.backward?s.start=a:s.end=o,i._select()))},5))},_viewByIndex:function(e){var t,i=this.views;for(t in i){if(!e)return t;e--}},_keydown:function(i){var n,a,o,r,s,l,d=this,c=i.keyCode,u=d.view(),h=u.options.editable,p=d._selection,f=e.extend(p),v=16===c||18===c||17===c||91===c||92===c,m=".k-scheduler-tools > li,.k-scheduler-navigation > li,.k-scheduler-views > li.k-state-selected:visible, .k-scheduler-views > li.k-current-view:visible",g=d.toolbar.find(m),_=d.toolbar.find(ge),k=e(i.target).closest(ge).length||d.toolbar.find(".k-scheduler-views .k-state-focused").length,w=_.children().index(d.toolbar.find("."+ve)),b=H.support.isRtl(d.element),y=b?-1:1;if(w==-1&&(w=_.children().index(d.toolbar.find(".k-state-selected"))),d._ctrlKey=i.ctrlKey,d._shiftKey=i.shiftKey,c===F.F10)return d.toolbar.find("ul > li:first").focus().addClass(ve),i.preventDefault(),t;if(c===F.TAB){if(d.toolbar.find("."+ve).length)return a=g.index(d.toolbar.find("."+ve)),a===-1&&d._focusedView&&(a=g.index(d.toolbar.find(".k-scheduler-views > .k-state-selected"))),o=i.shiftKey?g[a-1]:g[a+1],d.toolbar.find("."+ve).removeClass(ve),o?(e(o).addClass(ve).focus(),d._focusedView=null,i.preventDefault(),t):(d.element.focus(),i.preventDefault(),t)}else if(c===F.ENTER||c===F.SPACEBAR){if(k&&d._focusedView&&!d._focusedView.hasClass("k-state-selected"))return r=d._focusedView.data().name,d.trigger("navigate",{view:r,action:"changeView",date:d.date()})||(d.view(r),_.removeClass(me),d.toolbar.find(".k-current-view:visible").length&&(e(document.activeElement).blur(),d.toolbar.find(".k-current-view:visible").addClass(ve).find(".k-link").focus())),i.preventDefault(),t;if(d.toolbar.find("."+ve+":visible").length)return d.toolbar.find("."+ve+":visible").click(),i.preventDefault(),t}else if(i.altKey&&c===F.DOWN){if(d.toolbar.find("."+ve+":visible").length)return d.toolbar.find("."+ve+":visible").click(),i.preventDefault(),t}else{if(c===F.RIGHT&&k)return e(d.toolbar.find("."+ve)).removeClass(ve),d._focusedView=e(b?w-1===0?_.children(":not(.k-current-view):last"):_.children()[w+1*y]:w+1===_.children().length?_.children(":not(.k-current-view):first"):_.children()[w+1*y]),d._focusedView.focus().addClass(ve),i.preventDefault(),t;if(c===F.LEFT&&k)return e(d.toolbar.find("."+ve)).removeClass(ve),d._focusedView=e(b?w+1===_.children().length?_.children(":not(.k-current-view):first"):_.children()[w-1*y]:w-1===0?_.children(":not(.k-current-view):last"):_.children()[w-1*y]),d._focusedView.focus().addClass(ve),i.preventDefault(),t;if(c===F.DOWN&&d.toolbar.find(ge).hasClass(me))return d.toolbar.find("."+ve).removeClass(ve),n=d._focusedView?_.find(d._focusedView).index():_.children(".k-scheduler-views > .k-state-selected").index(),d._focusedView=e(n+1===_.children().length?_.children(":not(.k-current-view):first"):_.children()[n+1*y]),d._focusedView.focus().addClass(ve),i.preventDefault(),t;if(c===F.UP&&d.toolbar.find(ge).hasClass(me))return d.toolbar.find("."+ve).removeClass(ve),n=d._focusedView?_.find(d._focusedView).index():_.children(".k-scheduler-views > .k-state-selected").index(),d._focusedView=e(n-1===0?_.children(":not(.k-current-view):last"):_.children()[n-1*y]),d._focusedView.focus().addClass(ve),i.preventDefault(),t;if(i.altKey&&c===F.DOWN&&d.toolbar.find(".k-nav-current").hasClass(ve))return d._showCalendar(),i.preventDefault(),t;if(c===F.ESC&&d.popup&&d.popup.visible())return d.popup.close(),i.preventDefault(),t;if(c===F.ESC&&d.toolbar.find(ge).hasClass(me))return d.toolbar.find(ge).removeClass(me),d.toolbar.find(ge).children().removeClass(ve),d._focusedView=null,d.toolbar.find(".k-current-view").focus().addClass(ve),i.preventDefault(),t}if(!v){if(!p)return d._selectFirstSlot(),d._select(),d.element.focus(),t;c===F.TAB?u.moveToEvent(p,i.shiftKey)&&(d._select(),d.element.focus(),i.preventDefault()):c===F.ENTER||c===F.SPACEBAR?p.events.length&&h?h.update!==!1&&d.editEvent(p.events[0]):h&&h.create!==!1&&(p.isAllDay&&(p=e.extend({},p,{end:H.date.addDays(p.end,-1)})),i.preventDefault(),d.addEvent($({},p,u._resourceBySlot(p)))):c===F.DELETE&&h!==!1&&h.destroy!==!1?d.removeEvent(p.events[0]):c>=49&&c<=57?(s=d._viewByIndex(c-49),s&&!d.trigger("navigate",{view:s,action:"changeView",date:d.date()})&&d.view(s)):u.move(p,c,i.shiftKey)&&(u.inRange(p)?d._select():(l=d.date().getTime()>p.start.getTime()?"previous":"next",d.trigger("navigate",{view:d._selectedViewName,action:l,date:p.start})?(p.start=f.start,p.end=f.end):d.date(p.start)),d.toolbar.find("ul > li").removeClass(ve),i.preventDefault()),d._adjustSelectedDate()}},_createSelection:function(t){var i,n,a=this._selection;t=e(t),t.is(".k-event")&&(i=t.attr(H.attr("uid")),a&&a.events.indexOf(i)!==-1&&!this._ctrlKey)||(a&&(this._ctrlKey||this._shiftKey)||(a=this._selection={events:[],groupIndex:0}),n=this.view().selectionByElement(t),n&&(a.groupIndex=n.groupIndex||0),i&&(n=d(this._data,i)),n&&n.uid&&(i=[n.uid]),this._updateSelection(n,i),this._adjustSelectedDate())},_updateSelection:function(e,i,n){var a,o,r,s=this._selection;e&&s&&(a=this.view(),e.uid&&(e=a._updateEventForSelection(e)),this._shiftKey&&s.start&&s.end?(o=e.end<s.end,s.end=e.endDate?e.endDate():e.end,o&&a._timeSlotInterval&&H.date.setTime(s.end,-a._timeSlotInterval())):(s.start=e.startDate?e.startDate():e.start,s.end=e.endDate?e.endDate():e.end),s.isAllDay="isDaySlot"in e?e.isDaySlot:e.isAllDay,null!==n&&n!==t&&(s.groupIndex=n),s.index=e.index,this._ctrlKey?(r=i&&i.length?s.events.indexOf(i[0]):-1,r>-1?s.events.splice(r,1):s.events=s.events.concat(i||[])):s.events=i||[])},options:{name:"Scheduler",date:we,editable:!0,autoBind:!0,snap:!0,mobile:!1,timezone:"",allDaySlot:!0,min:new Date(1900,0,1),max:new Date(2099,11,31),toolbar:null,workWeekStart:1,workWeekEnd:5,showWorkHours:!1,startTime:we,endTime:we,currentTimeMarker:{updateInterval:1e4,useLocalTimezone:!0},footer:{},messages:{today:"Today",pdf:"Export to PDF",save:"Save",cancel:"Cancel",destroy:"Delete",resetSeries:"Reset Series",deleteWindowTitle:"Delete event",next:"Next",previous:"Previous",ariaSlotLabel:"Selected from {0:t} to {1:t}",ariaEventLabel:"{0} on {1:D} at {2:t}",views:{day:"Day",week:"Week",workWeek:"Work Week",agenda:"Agenda",month:"Month",timeline:"Timeline",timelineWeek:"Timeline Week",timelineWorkWeek:"Timeline Work Week",timelineMonth:"Timeline Month"},recurrenceMessages:{deleteWindowTitle:"Delete Recurring Item",resetSeriesWindowTitle:"Reset Series",deleteWindowOccurrence:"Delete current occurrence",deleteWindowSeries:"Delete the series",editWindowTitle:"Edit Recurring Item",editWindowOccurrence:"Edit current occurrence",editWindowSeries:"Edit the series"},editable:{confirmation:Te},editor:{title:"Title",start:"Start",end:"End",allDayEvent:"All day event",description:"Description",repeat:"Repeat",timezone:"Timezone",startTimezone:"Start timezone",endTimezone:"End timezone",separateTimezones:"Use separate start and end time zones",timezoneEditorTitle:"Timezones",timezoneEditorButton:"Time zone",timezoneTitle:"Time zones",noTimezone:"No timezone",editorTitle:"Event"}},height:null,width:null,resources:[],group:{resources:[],orientation:"horizontal"},views:[],selectable:!1},events:[ce,fe,de,he,"add","dataBinding","dataBound","moveStart","move","moveEnd","resizeStart","resize","resizeEnd","navigate","change"],destroy:function(){var t,i,n,a=this;if(L.fn.destroy.call(a),a.dataSource&&(a.dataSource.unbind(re,a._refreshHandler),a.dataSource.unbind(se,a._progressHandler),a.dataSource.unbind(le,a._errorHandler)),a._resourceRefreshHandler)for(i=0;i<a.resources.length;i++)n=a.resources[i].dataSource,n.unbind(re,a._resourceRefreshHandler),n.unbind(se,a._resourceProgressHandler),n.unbind(le,a._resourceErrorHandler);a.calendar&&(a.calendar.destroy(),a.popup.destroy()),a.view()&&a.view().destroy(),a._editor&&a._editor.destroy(),this._moveDraggable&&this._moveDraggable.destroy(),this._resizeDraggable&&this._resizeDraggable.destroy(),t=a.element.add(a.wrapper).add(a.toolbar).add(a.popup),t.off(G),clearTimeout(a._moveTimer),a._model=null,a.toolbar=null,a.element=null,e(window).off("resize"+G,a._resizeHandler),H.destroy(a.wrapper)},setDataSource:function(e){this.options.dataSource=e,this._dataSource(),this.options.autoBind&&e.fetch?e.fetch():Z(e)&&this.view(this._selectedView)},items:function(){var e=this.wrapper.find(".k-scheduler-content"),t=this.view();return t&&"agenda"===t.options.name?e.find(".k-task"):e.find(".k-event").add(this.wrapper.find(".k-scheduler-header-wrap").find(".k-scheduler-header-all-day").siblings())},_movable:function(){var i,n,a,o,r,s,l,d,c,u,h=this,p=0,f=[],v=[],m=h._isMobile(),g=h.options.editable&&h.options.editable.move!==!1,_=h.options.editable&&h.options.editable.resize!==!1;(g||_&&m)&&(h._dragging=!1,m&&H.support.mobileOS.android&&(p=5),h._moveDraggable=new H.ui.Draggable(h.element,{distance:p,filter:".k-event",ignore:".k-resize-handle",holdToDrag:m,autoScroll:!0}),g&&h._moveDraggable.bind("dragstart",function(e){var r,p,v,m,g=h.view(),_=e.currentTarget,k=h._isTouch(e);if(h._dragging=!0,!g.options.editable||g.options.editable.move===!1)return h._dragging=!1,e.preventDefault(),t;if(k&&!_.hasClass("k-event-active"))return h._dragging=!1,h.element.find(".k-event-active").removeClass("k-event-active"),e.preventDefault(),t;if(s=h.occurrenceByUid(_.attr(H.attr("uid"))),l=s.clone(),u=s.clone(),l.update(g._eventOptionsForMove(l)),l.inverseColor=h._isInverseColor(_),f=[],h._selection)for(r=h._selection.events,p=0;p<r.length;p++)v=h.occurrenceByUid(r[p]).clone(),m=this.element.find('div.k-event[data-uid="'+v.uid+'"]').eq(0),v.update(g._eventOptionsForMove(v)),m.length&&(v.inverseColor=h._isInverseColor(m)),f.push(v);else f.push(l);i=g._slotByPosition(e.x.startLocation,e.y.startLocation),a=g._resourceBySlot(i),c=o=i.startOffset(e.x.startLocation,e.y.startLocation,h.options.snap),n=i,d=i,i&&!h.trigger("moveStart",{event:s})||e.preventDefault()}).bind("drag",function(t){var a,u,p,m,g,_=h.view(),k=_._slotByPosition(t.x.location,t.y.location);if(k){if(r=k.startOffset(t.x.location,t.y.location,h.options.snap),k.isDaySlot!==i.isDaySlot)if(k.isDaySlot!==d.isDaySlot)for(m=e(i.element).index(),g=e(k.element).parent().children().eq(m),i=_._slotByPosition(g.offset().left,g.offset().top),o=i.startOffset(t.x.location,t.y.location,!0),v=f.map(function(e){return e.clone()}),p=0;p<f.length;p++)f[p].isAllDay!=k.isDaySlot&&(f[p].isAllDay=k.isDaySlot,f[p].end=H.date.getDate(f[p].start),f[p].start=H.date.getDate(f[p].start),k.isDaySlot||(H.date.setTime(f[p].start,H.date.getMilliseconds(_.startTime())),H.date.setTime(f[p].end,H.date.getMilliseconds(_.startTime())+_._timeSlotInterval())));else i=e.extend(!0,{},d),o=c,f=v;for(a=r-o,p=0;p<f.length;p++)_._updateMoveHint(f[p],k.groupIndex,a);if(u=b(l,a),h.trigger("move",{event:s,slot:{element:k.element,start:k.startDate(),end:k.endDate(),isDaySlot:k.isDaySlot},resources:_._resourceBySlot(k),start:u.start,end:u.end}))for(p=0;p<f.length;p++)_._updateMoveHint(f[p],k.groupIndex,a);else n=k}}).bind("dragend",function(t){var i,c,u,p,m,g,_,k,w,y;if(h.view()._removeMoveHint(),i=r-o,c=b(l,i),u=c.start,p=c.end,h._dragging=!1,m=h.view()._resourceBySlot(n),g=h.trigger("moveEnd",{event:s,slot:{element:n.element,start:n.startDate(),end:n.endDate()},start:u,end:p,resources:m}),!g&&(s.start.getTime()!==u.getTime()||s.end.getTime()!==p.getTime()||d.isDaySlot!==n.isDaySlot||H.stringify(m)!==H.stringify(a))){for(h._isMultiDrag=f.length>1,_=0;_<f.length;_++)k=f[_],c=b(k,i),w=h.view()._eventOptionsForMove(k),y=e.extend({isAllDay:k.isAllDay,start:c.start,end:c.end},w,m),h._updateEvent(null,k,y);h._isMultiDrag&&(h.dataSource.sync(),h._isMultiDrag=!1)}t.currentTarget.removeClass("k-event-active"),this.cancelHold(),f=[],v=[]}).bind("dragcancel",function(){h.view()._removeMoveHint(),this.cancelHold(),f=[],v=[]}),h._moveDraggable.bind("hold",function(e){h._isTouch(e)&&(h.element.find(".k-event-active").removeClass("k-event-active"),h.options.selectable&&h._createSelection(e.currentTarget),e.currentTarget.addClass("k-event-active"))}))},_resizable:function(){function t(e){var t,i={"k-resize-e":"east","k-resize-w":"west","k-resize-n":"north","k-resize-s":"south"};for(t in i)if(e.hasClass(t))return i[t]}var i,n,a,o,r,s=this,l=0;s._isMobile()&&H.support.mobileOS.android&&(l=5),s._resizeDraggable=new H.ui.Draggable(s.element,{distance:l,filter:".k-resize-handle",autoScroll:!0,dragstart:function(t){var l=e(t.currentTarget),d=l.closest(".k-event"),c=d.attr(H.attr("uid")),u=s.view();s._dragging=!0,a=s.occurrenceByUid(c),o=a.clone(),u._updateEventForResize(o),r=u._slotByPosition(t.x.startLocation,t.y.startLocation),s.trigger("resizeStart",{event:a})&&t.preventDefault(),i=H.date.toUtcTime(o.start),n=H.date.toUtcTime(o.end)},drag:function(l){var d,c,u,h,p,f;r&&(d=e(l.currentTarget),c=t(d),u=s.view(),h=u._slotByPosition(l.x.location,l.y.location),h&&r.groupIndex==h.groupIndex&&(r=h,p=i,f=n,"south"==c?!r.isDaySlot&&r.end-H.date.toUtcTime(o.start)>=u._timeSlotInterval()&&(n=o.isAllDay?r.startOffset(l.x.location,l.y.location,s.options.snap):r.endOffset(l.x.location,l.y.location,s.options.snap)):"north"==c?!r.isDaySlot&&H.date.toUtcTime(o.end)-r.start>=u._timeSlotInterval()&&(i=r.startOffset(l.x.location,l.y.location,s.options.snap)):"east"==c?r.isDaySlot&&H.date.toUtcTime(H.date.getDate(r.endDate()))>=H.date.toUtcTime(H.date.getDate(o.start))?n=o.isAllDay?r.startOffset(l.x.location,l.y.location,s.options.snap):r.endOffset(l.x.location,l.y.location,s.options.snap):!r.isDaySlot&&r.end-H.date.toUtcTime(o.start)>=u._timeSlotInterval()&&(n=r.endOffset(l.x.location,l.y.location,s.options.snap)):"west"==c&&(r.isDaySlot&&H.date.toUtcTime(H.date.getDate(o.end))>=H.date.toUtcTime(H.date.getDate(r.startDate()))?i=r.startOffset(l.x.location,l.y.location,s.options.snap):!r.isDaySlot&&H.date.toUtcTime(o.end)-r.start>=u._timeSlotInterval()&&(i=r.startOffset(l.x.location,l.y.location,s.options.snap))),s.trigger("resize",{event:a,slot:{element:r.element,start:r.startDate(),end:r.endDate()},start:H.timezone.toLocalDate(i),end:H.timezone.toLocalDate(n),resources:u._resourceBySlot(r)})?(i=p,n=f):u._updateResizeHint(o,r.groupIndex,i,n)))},dragend:function(l){var d,c=e(l.currentTarget),u=new Date(o.start.getTime()),h=new Date(o.end.getTime()),p=t(c);s._dragging=!1,s.view()._removeResizeHint(),"south"==p?h=H.timezone.toLocalDate(n):"north"==p?u=H.timezone.toLocalDate(i):"east"==p?h=r.isDaySlot?H.date.getDate(H.timezone.toLocalDate(n)):H.timezone.toLocalDate(n):"west"==p&&(r.isDaySlot?(u=new Date(H.timezone.toLocalDate(i)),u.setHours(0),u.setMinutes(0)):u=H.timezone.toLocalDate(i)),d=s.trigger("resizeEnd",{event:a,slot:{element:r.element,start:r.startDate(),end:r.endDate()},start:u,end:h,resources:s.view()._resourceBySlot(r)}),!d&&h.getTime()>=u.getTime()&&(o.start.getTime()==u.getTime()&&o.end.getTime()==h.getTime()||(s.view()._updateEventForResize(a),s._updateEvent(p,a,{start:u,end:h}))),r=null,a=null},dragcancel:function(){s._dragging=!1,s.view()._removeResizeHint(),r=null,a=null}})},_updateEvent:function(e,t,i){var n,a=this,o=function(e,t){try{a._preventRefresh=!0,e.update(i),a._convertDates(e)}finally{a._preventRefresh=!1}a.trigger(he,{event:e})||(t&&t(),a._isMultiDrag||a.dataSource.sync())},r=function(e){return e.recurrenceRule?a.dataSource.getByUid(e.uid):a.dataSource.get(e.recurrenceId)},s=function(){var n,s,l=r(t);"south"!=e&&"north"!=e||(i.start&&(n=H.date.getDate(l.start),H.date.setTime(n,W(i.start)),i.start=n),i.end&&(s=H.date.getDate(l.end),H.date.setTime(s,W(i.end)),i.end=s)),a.dataSource._removeExceptions(l),o(l)},l=function(){var e=r(t),i=function(){a._convertDates(e),a._selection&&a._selection.events.push(s)},n=e.toOccurrence({start:t.start,end:t.end}),s=n.uid;o(a.dataSource.add(n),i)};t.recurrenceRule||t.isOccurrence()?(n=a.options.messages.recurrenceMessages,a._showRecurringDialog(t,l,s,{title:n.editWindowTitle,text:n.editRecurring?n.editRecurring:Se,occurrenceText:n.editWindowOccurrence,seriesText:n.editWindowSeries})):o(a.dataSource.getByUid(t.uid))},_modelForContainer:function(t){return t=e(t).closest("["+H.attr("uid")+"]"),this.dataSource.getByUid(t.attr(H.attr("uid")))},showDialog:function(e){this._editor.showDialog(e)},focus:function(){this.wrapper.focus()},_confirmation:function(e,t,i){var n,a,o,r,s,l=this.options.editable;l===!0||l.confirmation!==!1?(n=this.options.messages,a=n.deleteWindowTitle,o=typeof l.confirmation===K?l.confirmation:n.editable.confirmation,this._isEditorOpened()&&t.isRecurring()&&(r=this.options.messages.recurrenceMessages,a=r.deleteWindowTitle,o=t.isException()?r.deleteRecurringConfirmation?r.deleteRecurringConfirmation:xe:r.deleteSeriesConfirmation?r.deleteSeriesConfirmation:Ce,i&&(a=r.resetSeriesWindowTitle,o=r.resetSeriesConfirmation?r.resetSeriesConfirmation:Ee)),s=[{name:"destroy",text:i?n.resetSeries:n.destroy,click:function(){e()}}],this._isMobile()&&H.Pane||s.push({name:"canceledit",text:n.cancel,click:function(){e(!0)}}),this._unbindResize(),this.showDialog({model:t,text:o,title:a,buttons:s}),this._bindResize()):e()},addEvent:function(e){var t,i,n=this._editor.editable,a=this.dataSource;e=e||{},i=this.trigger("add",{event:e}),!i&&(n&&n.end()||!n)&&(this.cancelEvent(),e&&e.toJSON&&(e=e.toJSON()),t=a.add(e),t&&(this.cancelEvent(),this._editEvent(t)))},saveEvent:function(){var e,t,i,n=this._editor;n&&(e=n.editable,t=n.container,i=this._modelForContainer(t),t&&e&&e.end()&&!this.trigger(he,{container:t,event:i})&&(i.dirty||i.isOccurrence()||this._convertDates(i,"remove"),this.dataSource.sync()))},cancelEvent:function(){var e,t=this._editor,i=t.container;i&&(e=this._modelForContainer(i),e&&e.isOccurrence()&&(this._convertDates(e,"remove"),this._convertDates(this.dataSource.get(e.recurrenceId),"remove")),this.dataSource.cancelChanges(e),t.close())},editEvent:function(e){var t="string"==typeof e?this.occurrenceByUid(e):e;t&&(this.cancelEvent(),t.isRecurring()?this._editRecurringDialog(t):this._editEvent(t))},_editEvent:function(e){this._preventRefresh=!0,this._unbindResize(),this._createPopupEditor(e),this._bindResize()},_editRecurringDialog:function(e){var t=this,i=function(){e.isException()?t._editEvent(e):t.addEvent(e)},n=function(){e.recurrenceId&&(e=t.dataSource.get(e.recurrenceId)),t._editEvent(e)},a=t.options.messages.recurrenceMessages;t._showRecurringDialog(e,i,n,{title:a.editWindowTitle,text:a.editRecurring?a.editRecurring:Se,occurrenceText:a.editWindowOccurrence,seriesText:a.editWindowSeries})},_showRecurringDialog:function(e,t,i,n){var a=this.options.editable,o=Q(a)?a.editRecurringMode:"dialog";"occurrence"===o||this._isMultiDrag?t():"series"===o?i():(this._unbindResize(),this.showDialog({model:e,title:n.title,text:n.text,buttons:[{text:n.occurrenceText,click:t},{text:n.seriesText,click:i}]}),this._bindResize())},_createButton:function(e){var t=e.template||Re,i=typeof e===K?e:e.name||e.text,n={className:"k-scheduler-"+(i||"").replace(/\s/g,""),text:i,attr:""};if(!(i||Q(e)&&e.template))throw Error("Custom commands should have name specified");return Q(e)?(e.className&&(e.className+=" "+n.className),"edit"===i&&Q(e.text)&&(e=$(!0,{},e),e.text=e.text.edit),n=$(!0,n,y[i],e)):n=$(!0,n,y[i]),H.template(t)(n)},_convertDates:function(e,t){var i=this.dataSource.reader.timezone,n=e.startTimezone,a=e.endTimezone,o=e.start,r=e.start;t=t||"apply",n=n||a,a=a||n,n&&(i?"apply"===t?(o=H.timezone.convert(e.start,i,n),r=H.timezone.convert(e.end,i,a)):(o=H.timezone.convert(e.start,n,i),r=H.timezone.convert(e.end,a,i)):(o=H.timezone[t](e.start,n),r=H.timezone[t](e.end,a)),e._set("start",o),e._set("end",r))},_createEditor:function(){var e,i=this;e=i._editor=this._isMobile()&&H.Pane?new D(this.wrapper,$({},this.options,{target:this,timezone:i.dataSource.reader.timezone,resources:i.resources,createButton:X(this._createButton,this)})):new S(this.wrapper,$({},this.options,{target:this,createButton:X(this._createButton,this),timezone:i.dataSource.reader.timezone,resources:i.resources})),e.bind("cancel",function(e){return i.trigger("cancel",{container:e.container,event:e.model})?(e.preventDefault(),t):(i._preventRefresh=!1,i.cancelEvent(),i._attemptRefresh&&i.refresh(),i.focus(),t)}),e.bind("edit",function(e){i.trigger(fe,{container:e.container,event:e.model})&&e.preventDefault()}),e.bind("save",function(){i._preventRefresh=!1,i.saveEvent()}),e.bind("remove",function(e){i._preventRefresh=!1,i.removeEvent(e.model)}),e.bind("resetSeries",function(e){i._confirmation(function(t){i._preventRefresh=!1,t||(i.dataSource._removeExceptions(e.model),i.saveEvent())},e.model,!0)})},_createPopupEditor:function(e){var t=this._editor;e.isNew()&&!e.isOccurrence()||(e.isOccurrence()&&this._convertDates(e.recurrenceId?this.dataSource.get(e.recurrenceId):e),this._convertDates(e)),this.editable=t.editEvent(e)},removeEvent:function(e){var t=this,i="string"==typeof e?t.occurrenceByUid(e):e;i&&(i.isRecurring()?t._deleteRecurringDialog(i):t._confirmation(function(e){e||t._removeEvent(i)},i))},occurrenceByUid:function(e){var t=this.dataSource.getByUid(e);return t||(t=d(this._data,e)),t},occurrencesInRange:function(e,t){return new H.data.Query(this._data).filter({logic:"or",filters:[{logic:"and",filters:[{field:"start",operator:"gte",value:e},{field:"end",operator:"gte",value:e},{field:"start",operator:"lt",value:t}]},{logic:"and",filters:[{field:"start",operator:"lte",value:e},{field:"end",operator:"gt",value:e}]}]}).toArray()},_removeEvent:function(e){this.trigger(ce,{event:e})||this.dataSource.remove(e)&&this.dataSource.sync();
},_deleteRecurringDialog:function(e){var t,i,n,a,o,r=this,s=e,l=r.options.editable,d=Q(l)?l.editRecurringMode:"dialog",c=function(){var e=s.recurrenceId?s:s.toOccurrence(),t=r.dataSource.get(e.recurrenceId);r._convertDates(t),r._removeEvent(e)},u=function(){s.recurrenceId&&(s=r.dataSource.get(s.recurrenceId)),r._removeEvent(s)};("dialog"!=d||r._isEditorOpened())&&(t=function(){r._confirmation(function(e){e||c()},s)},i=function(){r._confirmation(function(e){e||u()},s)}),n=i||u,a=t||c,r._isEditorOpened()?e.isException()?a():n():(o=r.options.messages.recurrenceMessages,r._showRecurringDialog(e,a,n,{title:o.deleteWindowTitle,text:o.deleteRecurring?o.deleteRecurring:De,occurrenceText:o.deleteWindowOccurrence,seriesText:o.deleteWindowSeries}))},_isEditorOpened:function(){return!!this._editor.container},_unbindView:function(e){var t=this;t.angular("cleanup",function(){return{elements:t.items()}}),e.destroy()},_bindView:function(e){var t=this;t.options.editable&&(t._viewRemoveHandler&&e.unbind(ce,t._viewRemoveHandler),t._viewRemoveHandler=function(e){t.removeEvent(e.uid)},e.bind(ce,t._viewRemoveHandler),t._viewAddHandler&&e.unbind(pe,t._viewAddHandler),t._viewAddHandler=function(e){t.addEvent(e.eventInfo)},e.bind(pe,this._viewAddHandler),t._viewEditHandler&&e.unbind(fe,t._viewEditHandler),t._viewEditHandler=function(e){t.editEvent(e.uid)},e.bind(fe,this._viewEditHandler)),t._viewNavigateHandler&&e.unbind("navigate",t._viewNavigateHandler),t._viewNavigateHandler=function(e){var i,n;e.view&&(i="isWorkDay"in e,n=i?"changeWorkDay":"changeView",t.trigger("navigate",{view:e.view,isWorkDay:e.isWorkDay,action:n,date:e.date})||(i&&(t._workDayMode=e.isWorkDay),t._selectView(e.view),t.date(e.date)))},e.bind("navigate",t._viewNavigateHandler),t._viewActivateHandler&&e.unbind("activate",t._viewActivateHandler),t._viewActivateHandler=function(){var e=this;t._selection&&(e.constrainSelection(t._selection),t._select(),t._adjustSelectedDate())},e.bind("activate",t._viewActivateHandler)},_selectView:function(e){var t,i,n,a,o=this;e&&o.views[e]&&(o._selectedView&&o._unbindView(o._selectedView),o._selectedView=o._renderView(e),o._selectedViewName=e,o._viewsCount>1&&!o._isMobile()?(t=Pe({views:o.views,view:e,ns:H.ns}),i=o.toolbar.find(".k-scheduler-views li:first-child"),i.is(".k-current-view")?i.replaceWith(t):o.toolbar.find(".k-scheduler-views").prepend(t),n=o.toolbar.find(".k-scheduler-views li").removeClass("k-state-selected"),n.end().find(".k-view-"+e.replace(/\./g,"\\.").toLowerCase()).addClass("k-state-selected")):(a=o.toolbar.find(".k-scheduler-mobile-views"),a.find("[value="+e.replace(/\./g,"\\.")+"]").prop("selected","selected")))},view:function(e){var i=this;return e?(i._selectView(e),i.rebind(),t):i._selectedView},viewName:function(){return this.view().name},_renderView:function(e){var t=this._initializeView(e);return this._bindView(t),(H.support.mouseAndTouchPresent||H.support.pointers)&&(t.content.css("-ms-touch-action","pinch-zoom"),t.content.css("touch-action","pinch-zoom")),this._model.set("formattedDate",t.dateForTitle()),this._model.set("formattedShortDate",t.shortDateForTitle()),this._model.set("formattedMobileDate",t.mobileDateForTitle?t.mobileDateForTitle():t.shortDateForTitle()),this._model.set("formattedYear",H.format("{0:yyyy}",t.startDate())),t},resize:function(e){var t=this.getSize(),i=this._size,n=this.view();n&&n.groups&&(!e&&i&&t.width===i.width&&t.height===i.height||(this.refresh({action:"resize"}),this._size=t))},_adjustSelectedDate:function(){var e=this._model.selectedDate,t=this._selection,i=t.start;i&&!H.date.isInDateRange(e,A(i),A(t.end))&&e.setFullYear(i.getFullYear(),i.getMonth(),i.getDate())},_initializeView:function(e){var t,i,n=this.views[e];if(n){if(t=Q(n),i=n.type,typeof i===K&&(i=H.getter(n.type)(window)),!i)throw Error("There is no such view");n=new i(this.wrapper,f($(!0,{},this.options,t?n:{},{resources:this.resources,date:this.date(),startTime:H.parseDate(this.options.startTime),endTime:H.parseDate(this.options.endTime),showWorkHours:this._workDayMode})))}return n},_views:function(){var e,t,i,n,a,o,r,s,l,d=this.options.views;for(this.views={},this._viewsCount=0,r=0,s=d.length;r<s;r++)l=!1,e=d[r],n=Q(e),n?(o=a=e.type?e.type:e,typeof o!==K&&(a=e.name||e.title,l=!0)):o=a=e,t=E[a],t&&!l&&(e.type=t.type,t.title=this.options.messages.views[a],"day"===t.type?t.messages={allDay:this.options.messages.allDay}:"agenda"===t.type&&(t.messages={event:this.options.messages.event,date:this.options.messages.date,time:this.options.messages.time})),e=$({title:a},t,n?e:{}),a&&(this.views[a]=e,this._viewsCount++,i&&!e.selected||(i=a));i&&(this._selectedViewName=i)},rebind:function(){this.dataSource.fetch()},_dataSource:function(){var e=this,t=e.options,i=t.dataSource;i=Z(i)?{data:i}:i,!t.timezone||i instanceof qe?i instanceof qe&&(t.timezone=i.options.schema?i.options.schema.timezone:""):i=$(!0,i,{schema:{timezone:t.timezone}}),e.dataSource&&e._refreshHandler?e.dataSource.unbind(re,e._refreshHandler).unbind(se,e._progressHandler).unbind(le,e._errorHandler):(e._refreshHandler=X(e.refresh,e),e._progressHandler=X(e._requestStart,e),e._errorHandler=X(e._error,e)),e.dataSource=H.data.SchedulerDataSource.create(i).bind(re,e._refreshHandler).bind(se,e._progressHandler).bind(le,e._errorHandler),e.options.dataSource=e.dataSource},_error:function(){this._progress(!1)},_requestStart:function(){this._progress(!0)},_progress:function(e){var t=this.element.find(".k-scheduler-content");H.ui.progress(t,e)},_resources:function(){var t,i,n,a,o,r=this,s=r.options.resources,l=[];for(t=0;t<s.length;t++){if(i=s[t],n=i.field,a=i.name||n,o=i.dataSource,!n||!o)throw Error('The "field" and "dataSource" options of the scheduler resource are mandatory.');r.resources.push({field:n,name:a,title:i.title||n,dataTextField:i.dataTextField||"text",dataValueField:i.dataValueField||"value",dataColorField:i.dataColorField||"color",valuePrimitive:null==i.valuePrimitive||i.valuePrimitive,multiple:i.multiple||!1,dataSource:r._resourceDataSource(o,a,l)})}r.options.autoBind?e.when.apply(null,l).then(function(){r.view(r._selectedViewName)}):r._selectView(r._selectedViewName)},_resourceDataSource:function(e,t,i){var n=this,a=Z(e)?{data:e}:e,o=H.data.DataSource.create(a);return n.options.autoBind?i.push(o.fetch(function(){n._bindResourceEvents(this,t)})):n._bindResourceEvents(o,t),o},_bindResourceEvents:function(e,t){var i=this,n=i.options.group&&i.options.group.resources.length,a=n&&i.options.group.resources.indexOf(t)>-1;!i._resourceRefreshHandler&&a&&(i._resourceRefreshHandler=X(i._refreshResource,i),i._resourceProgressHandler=X(i._requestStart,i),i._resourceErrorHandler=X(i._error,i)),a&&e.bind(re,i._resourceRefreshHandler).bind(se,i._resourceProgressHandler).bind(le,i._resourceErrorHandler)},_refreshResource:function(){var e=this;e.view(e._selectedViewName)},_initModel:function(){var e=this;e._model=H.observable({selectedDate:new Date(this.options.date),formattedDate:"",formattedShortDate:""}),e._model.bind("change",function(t){"selectedDate"===t.field&&e.view(e._selectedViewName)})},_wrapper:function(){var e=this,t=e.options,i=t.height,n=t.width;e.wrapper=e.element.addClass("k-widget k-scheduler k-floatwrap").attr("role","grid").attr("aria-multiselectable",!0),e._isMobile()&&e.wrapper.addClass("k-scheduler-mobile"),i&&e.wrapper.height(i),n&&e.wrapper.width(n)},date:function(e){return null!=e&&A(e)>=A(this.options.min)&&A(e)<=A(this.options.max)&&this._model.set("selectedDate",e),A(this._model.get("selectedDate"))},_toolbar:function(){var i,n,a=this,o=a.options,r=[];o.toolbar&&(r=e.isArray(o.toolbar)?o.toolbar:[o.toolbar]),i=this._isMobile()?Ve:Me,n=e(i({messages:o.messages,pdf:e.grep(r,function(e){return"pdf"==e||"pdf"==e.name}).length>0,ns:H.ns,view:a._selectedViewName,views:a.views,viewsCount:a._viewsCount,editable:a.options.editable})),a.wrapper.append(n),a.toolbar=n,H.bind(a.toolbar,a._model),n.on(ee+G,".k-pdf",function(e){e.preventDefault(),a.saveAsPDF()}),n.on(ee+G,".k-create-event",function(e){e.preventDefault(),a.addEvent()}),n.on(ee+G,".k-nav-calendar",function(e){e.preventDefault(),a._showCalendar(e.target)}),n.on(ee+G,".k-scheduler-navigation li",function(i){var n,o=e(this),r=new Date(a.date()),s="",l=new Date,d=a.options.timezone;if(i.preventDefault(),o.hasClass("k-nav-today"))s="today",d?(n=H.timezone.offset(l,d),r=H.timezone.convert(l,l.getTimezoneOffset(),n)):r=l;else if(o.hasClass("k-nav-next"))s="next",r=a.view().nextDate();else if(o.hasClass("k-nav-prev"))s="previous",r=a.view().previousDate();else if(o.hasClass("k-nav-current")&&!a._isMobile())return a._showCalendar(),t;a.trigger("navigate",{view:a._selectedViewName,action:s,date:r})||a.date(r)}),n.on(ee+G,".k-scheduler-views li:not(.k-current-view), .k-scheduler-refresh",function(t){t.preventDefault();var i=e(this).attr(H.attr("name"));a.trigger("navigate",{view:i,action:"changeView",date:a.date()})||(a.view(i),a.element.find("."+me).removeClass(me))}),n.on(ee+G,".k-scheduler-views li.k-current-view",function(t){t.preventDefault(),a.element.find(".k-scheduler-views").toggleClass(me),e(document).on(te+G,function(t){0===e(t.target).closest(".k-scheduler-views").length&&(a.element.find("."+me).removeClass(me),e(document).off(ee+G))})}),n.find(".k-scheduler-mobile-views").on("change",function(e){a.view(e.target.value)}),n.find("li").hover(function(){e(this).addClass("k-state-hover")},function(){e(this).removeClass("k-state-hover")})},_showCalendar:function(t){var i=this,n=t||i.toolbar.find(".k-nav-current"),a=e('<div class="k-calendar-container"><div class="k-scheduler-calendar"/></div>');i.popup||(i.popup=new q(a,{anchor:n,activate:function(){i.popup&&i.calendar&&(i.popup._toggleResize(!1),i.calendar.element.find("table").focus(),i.popup._toggleResize(!0))},open:function(){i.calendar||(i.calendar=new Y(this.element.find(".k-scheduler-calendar"),{change:function(){var e=this.value();i.trigger("navigate",{view:i._selectedViewName,action:"changeDate",date:e})||(i.date(e),i.popup.close()),i._isMobile||(i._selectedView.element.focus(),i.toolbar.find(".k-nav-current").focus().addClass(ve))},min:i.options.min,max:i.options.max})),i.calendar.element.on("keydown"+G,function(e){e.keyCode!==F.ESC&&e.keyCode!==F.TAB||(i.popup.close(),i._selectedView.element.focus(),i.toolbar.find(".k-nav-current").focus().addClass(ve))}),i.calendar.value(i.date())},copyAnchorStyles:!1})),i.popup.open()},refresh:function(e){var i=this,n=this.view(),a=e&&"itemchange"===e.action&&(this._editor.editable||this._preventRefresh)||"signalr"===this.dataSource.options.type&&this._preventRefresh;if(this._progress(!1),this.angular("cleanup",function(){return{elements:i.items()}}),e=e||{},n)return a?(this._attemptRefresh="signalr"===this.dataSource.options.type,t):(this.trigger("dataBinding",{action:e.action||"rebind",index:e.index,items:e.items})||(e&&"resize"===e.action||!this._editor||this._editor.close(),this._data=this.dataSource.expand(n.startDate(),n.visibleEndDate()),n.refreshLayout(),n.render(this._data),this.trigger("dataBound"),this._attemptRefresh=!1),t)},slotByPosition:function(e,t){var i,n=this.view();return n._slotByPosition?(i=n._slotByPosition(e,t),i?{startDate:i.startDate(),endDate:i.endDate(),groupIndex:i.groupIndex,element:i.element,isDaySlot:i.isDaySlot}:null):null},slotByElement:function(t){var i=e(t).offset();return this.slotByPosition(i.left,i.top)},resourcesBySlot:function(e){return this.view()._resourceBySlot(e)}}),E={day:{type:"kendo.ui.DayView"},week:{type:"kendo.ui.WeekView"},workWeek:{type:"kendo.ui.WorkWeekView"},agenda:{type:"kendo.ui.AgendaView"},month:{type:"kendo.ui.MonthView"},timeline:{type:"kendo.ui.TimelineView"},timelineWeek:{type:"kendo.ui.TimelineWeekView"},timelineWorkWeek:{type:"kendo.ui.TimelineWorkWeekView"},timelineMonth:{type:"kendo.ui.TimelineMonthView"}},N.plugin(x),H.PDFMixin&&(H.PDFMixin.extend(x.prototype),C="k-scheduler-pdf-export",x.fn._drawPDF=function(t){var i,n,a,o=this.wrapper,r=o[0].style.cssText;return o.css({width:o.width(),height:o.height()}),o.addClass(C),i=this,n=new e.Deferred,a=o.find(".k-scheduler-content").find("table").css("table-layout","auto"),setTimeout(function(){a.css("table-layout","fixed"),i.resize(!0),i._drawPDFShadow({},{avoidLinks:i.options.pdf.avoidLinks}).done(function(e){var i={page:e,pageNumber:1,progress:1,totalPages:1};t.notify(i),n.resolve(i.page)}).fail(function(e){n.reject(e)}).always(function(){o[0].style.cssText=r,o.removeClass(C),i.resize(!0),i.resize(!0)})}),n}),R=L.extend({init:function(e,t){var i=this,n=H.timezone.windows_zones;if(!n||!H.timezone.zones_titles)throw Error("kendo.timezones.min.js is not included.");L.fn.init.call(i,e,t),i.wrapper=i.element,i._zonesQuery=new H.data.Query(n),i._zoneTitleId=H.guid(),i._zoneTitlePicker(),i._zonePicker(),i._zoneTitle.bind("cascade",function(){this.value()||i._zone.wrapper.hide()}),i._zone.bind("cascade",function(){i._value=this.value(),i.trigger("change")}),i.value(i.options.value)},options:{name:"TimezoneEditor",value:"",optionLabel:"No timezone"},events:["change"],_zoneTitlePicker:function(){var t=this,i=e('<input id="'+t._zoneTitleId+'" aria-label="'+t.options.title+'"/>').appendTo(t.wrapper);t._zoneTitle=new H.ui.DropDownList(i,{dataSource:H.timezone.zones_titles,dataValueField:"other_zone",dataTextField:"name",optionLabel:t.options.optionLabel})},_zonePicker:function(){var t=this,i=e('<input aria-label="'+t.options.title+'"/>').appendTo(this.wrapper);t._zone=new H.ui.DropDownList(i,{dataValueField:"zone",dataTextField:"territory",dataSource:t._zonesQuery.data,cascadeFrom:t._zoneTitleId,dataBound:function(){t._value=this.value(),this.wrapper.toggle(this.dataSource.view().length>1)}}),t._zone.wrapper.hide()},destroy:function(){L.fn.destroy.call(this),H.destroy(this.wrapper)},value:function(e){var i,n=this;return e===t?n._value:(i=n._zonesQuery.filter({field:"zone",operator:"eq",value:e}).data[0],i?(n._zoneTitle.value(i.other_zone),n._zone.value(i.zone)):n._zoneTitle.select(0),t)}}),N.plugin(R),P=H.template('<option value="#=other_zone#">#=name#</option>'),M=H.template('<option value="#=zone#">#=territory#</option>'),V=L.extend({init:function(e,t){var i=this,n=H.timezone.windows_zones;if(!n||!H.timezone.zones_titles)throw Error("kendo.timezones.min.js is not included.");L.fn.init.call(i,e,t),i.wrapper=i.element,i._zonesQuery=new H.data.Query(n),i._zoneTitlePicker(),i._zonePicker(),i.value(i.options.value)},options:{name:"MobileTimezoneEditor",optionLabel:"No timezone",value:""},events:["change"],_bindZones:function(e){var t=e?this._filter(e):[];this._zone.html(this._options(t,M))},_filter:function(e){return this._zonesQuery.filter({field:"other_zone",operator:"eq",value:e}).data},_options:function(e,t,i){var n=0,a="",o=e.length;for(i&&(a+=t({other_zone:"",name:i}));n<o;n++)a+=t(e[n]);return a},_zoneTitlePicker:function(){var t=this,i=t._options(H.timezone.zones_titles,P,t.options.optionLabel);t._zoneTitle=e("<select>"+i+"</select>").appendTo(t.wrapper).change(function(){var e=this.value,i=t._zonePickerLabel,n=i.find("select");t._bindZones(e),e&&n.children().length>1?i.show():i.hide(),t._value=t._zone[0].value,t.trigger("change")})},_zonePicker:function(){var t=this;t._zonePickerLabel=e("<li class='k-item k-zonepicker' style='display:none'><label class='k-label'><span class='k-item-title'></span><div></div></label></li>"),t._zone=e("<select></select>").appendTo(t._zonePickerLabel.find("div")).change(function(){t._value=this.value,t.trigger("change")}),this.wrapper.closest(".k-item").after(t._zonePickerLabel),t._bindZones(t._zoneTitle.val()),t._value=t._zone[0].value},destroy:function(){L.fn.destroy.call(this),H.destroy(this.wrapper)},value:function(e){var i,n=this,a=n._zone,o="",r="";return e===t?n._value:(i=n._zonesQuery.filter({field:"zone",operator:"eq",value:e}).data[0],i&&(r=i.zone,o=i.other_zone),n._zoneTitle.val(o),n._bindZones(o),a.val(r),r=a[0].value,r&&a.children.length>1?n._zonePickerLabel.show():n._zonePickerLabel.hide(),n._value=r,t)}}),N.plugin(V)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()});
//# sourceMappingURL=kendo.scheduler.min.js.map
