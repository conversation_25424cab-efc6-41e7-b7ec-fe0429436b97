{"version": 3, "sources": ["kendo.pane.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "roleSelector", "ui", "Widget", "ViewEngine", "View", "extend", "NAVIGATE", "VIEW_SHOW", "SAME_VIEW_REQUESTED", "OS", "support", "mobileOS", "SKIP_TRANSITION_ON_BACK_BUTTON", "ios", "appMode", "flatVersion", "BACK", "DOT", "classNames", "pane", "paneWrapper", "collapsiblePane", "vertical", "Pane", "init", "element", "options", "that", "this", "fn", "call", "addClass", "collapsible", "history", "historyCallback", "url", "params", "backButtonPressed", "transition", "viewEngine", "showView", "_historyNavigate", "length", "pop", "push", "parseQueryStringParams", "_historyReplace", "container", "modelScope", "rootNeeded", "initial", "serverNavigation", "remoteViewURLPrefix", "root", "layout", "$angular", "showStart", "closeActiveDialogs", "after", "viewShow", "e", "trigger", "loadStart", "loadComplete", "sameViewRequested", "viewTypeDetermined", "remote", "_setPortraitWidth", "onResize", "dialogs", "find", "filter", "each", "widgetInstance", "close", "navigateToInitial", "navigate", "name", "<PERSON><PERSON><PERSON><PERSON>", "events", "append", "html", "destroy", "id", "replace", "view", "width", "mobile", "application", "is", "css", "wrap", "paneContainer", "ns", "parent", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,cAAe,cAAeD,IACvC,WAqKE,MA5JC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAeF,EAAME,aAAcC,EAAKH,EAAMG,GAAIC,EAASD,EAAGC,OAAQC,EAAaL,EAAMK,WAAYC,EAAON,EAAMM,KAAMC,EAAST,EAAES,OAAQC,EAAW,WAAYC,EAAY,WAAYC,EAAsB,oBAAqBC,EAAKX,EAAMY,QAAQC,SAAUC,EAAiCH,EAAGI,MAAQJ,EAAGK,SAAWL,EAAGM,aAAe,IAAKC,EAAO,SAChXC,EAAM,IACNC,GACAC,KAAM,SACNC,YAAa,iBACbC,gBAAiB,qBACjBC,SAAU,cAEVC,EAAOrB,EAAOG,QACdmB,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOC,IACX1B,GAAO2B,GAAGL,KAAKM,KAAKH,EAAMF,EAASC,GACnCA,EAAUC,EAAKD,QACfD,EAAUE,EAAKF,QACfA,EAAQM,SAASb,EAAWC,MACxBQ,EAAKD,QAAQM,aACbP,EAAQM,SAASb,EAAWG,iBAEhCO,KAAKK,WACLL,KAAKM,gBAAkB,SAAUC,EAAKC,EAAQC,GAC1C,GAAIC,GAAaX,EAAKW,UAKtB,OAJAX,GAAKW,WAAa,KACd1B,GAAkCyB,IAClCC,EAAa,QAEVX,EAAKY,WAAWC,SAASL,EAAKG,EAAYF,IAErDR,KAAKa,iBAAmB,SAAUN,GAC9B,GAAIA,IAAQnB,EAAM,CACd,GAA4B,IAAxBW,EAAKM,QAAQS,OACb,MAEJf,GAAKM,QAAQU,MACbR,EAAMR,EAAKM,QAAQN,EAAKM,QAAQS,OAAS,OAErCP,aAAe/B,KACf+B,EAAM,IAEVR,EAAKM,QAAQW,KAAKT,EAEtBR,GAAKO,gBAAgBC,EAAKrC,EAAM+C,uBAAuBV,KAE3DP,KAAKkB,gBAAkB,SAAUX,GAC7B,GAAIC,GAAStC,EAAM+C,uBAAuBV,EAC1CR,GAAKM,QAAQN,EAAKM,QAAQS,OAAS,GAAKP,EACxCR,EAAKO,gBAAgBC,EAAKC,IAE9BT,EAAKY,WAAa,GAAIpC,GAAWE,MAC7B0C,UAAWtB,EACXa,WAAYZ,EAAQY,WACpBU,WAAYtB,EAAQsB,WACpBC,YAAavB,EAAQwB,QACrBC,iBAAkBzB,EAAQyB,iBAC1BC,oBAAqB1B,EAAQ2B,MAAQ,GACrCC,OAAQ5B,EAAQ4B,OAChBC,SAAU7B,EAAQ6B,SAClBC,UAAW,WACP7B,EAAK8B,sBAETC,MAAO,aAEPC,SAAU,SAAUC,GAChBjC,EAAKkC,QAAQtD,EAAWqD,IAE5BE,UAAW,aAEXC,aAAc,aAEdC,kBAAmB,WACfrC,EAAKkC,QAAQrD,IAEjByD,mBAAoB,SAAUL,GACrBA,EAAEM,QAAWvC,EAAKD,QAAQyB,kBAC3BxB,EAAKkC,QAAQvD,GAAY6B,IAAKyB,EAAEzB,QAGzCP,KAAKF,QAAQa,aAChBX,KAAKuC,oBACLrE,EAAMsE,SAAS,WACXzC,EAAKwC,uBAGbV,mBAAoB,WAChB,GAAIY,GAAUzC,KAAKH,QAAQ6C,KAAKtE,EAAa,kCAAkCuE,OAAO,WACtFF,GAAQG,KAAK,WACT1E,EAAM2E,eAAe7E,EAAEgC,MAAO3B,GAAIyE,WAG1CC,kBAAmB,WACf,GAAIzB,GAAUtB,KAAKF,QAAQwB,OAI3B,OAHIA,IACAtB,KAAKgD,SAAS1B,GAEXA,GAEXxB,SACImD,KAAM,OACNC,cAAe,GACfxC,WAAY,GACZgB,OAAQ,GACRtB,aAAa,EACbkB,QAAS,KACTF,WAAYjD,QAEhBgF,QACIzE,EACAC,EACAC,GAEJwE,OAAQ,SAAUC,GACd,MAAOrD,MAAKW,WAAWyC,OAAOC,IAElCC,QAAS,WACL,GAAIvD,GAAOC,IACX1B,GAAO2B,GAAGqD,QAAQpD,KAAKH,GACnBA,EAAKY,YACLZ,EAAKY,WAAW2C,WAGxBN,SAAU,SAAUzC,EAAKG,GACjBH,YAAe/B,KACf+B,EAAMA,EAAIgD,IAEdvD,KAAKU,WAAaA,EAClBV,KAAKa,iBAAiBN,IAE1BiD,QAAS,SAAUjD,EAAKG,GAChBH,YAAe/B,KACf+B,EAAMA,EAAIgD,IAEdvD,KAAKU,WAAaA,EAClBV,KAAKkB,gBAAgBX,IAEzBkD,KAAM,WACF,MAAOzD,MAAKW,WAAW8C,QAE3BlB,kBAAmB,WACf,GAAImB,GAAOR,EAAgBlD,KAAKF,QAAQoD,aACpCA,KACAQ,EAAQxF,EAAMyF,OAAOC,YAAY/D,QAAQgE,GAAGxE,EAAMC,EAAWI,UAAYwD,EAAgB,OACzFlD,KAAKH,QAAQiE,IAAI,QAASJ,MAItC/D,GAAKoE,KAAO,SAAUlE,EAASC,GAAnB,GAIJkE,GACAzE,CAEJ,OANKM,GAAQgE,GAAGzF,EAAa,WACzByB,EAAUA,EAAQkE,KAAK,aAAe7F,EAAM+F,GAAK,0CAA0CC,UAE3FF,EAAgBnE,EAAQkE,KAAK,eAAiBzE,EAAWE,YAAc,gCAAgC0E,SACvG3E,EAAO,GAAII,GAAKqE,EAAelE,GACnCP,EAAKyD,SAAS,IACPzD,GAEXrB,EAAMyB,KAAOA,GACfxB,OAAOD,MAAMiG,QACRhG,OAAOD,OACE,kBAAVH,SAAwBA,OAAOqG,IAAMrG,OAAS,SAAUsG,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.pane.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.pane', ['kendo.view'], f);\n}(function () {\n    var __meta__ = {\n        id: 'pane',\n        name: 'Pane',\n        category: 'web',\n        description: 'Pane',\n        depends: ['view'],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, roleSelector = kendo.roleSelector, ui = kendo.ui, Widget = ui.Widget, ViewEngine = kendo.ViewEngine, View = kendo.View, extend = $.extend, NAVIGATE = 'navigate', VIEW_SHOW = 'viewShow', SAME_VIEW_REQUESTED = 'sameViewRequested', OS = kendo.support.mobileOS, SKIP_TRANSITION_ON_BACK_BUTTON = OS.ios && !OS.appMode && OS.flatVersion >= 700, BACK = '#:back';\n        var DOT = '.';\n        var classNames = {\n            pane: 'k-pane',\n            paneWrapper: 'k-pane-wrapper',\n            collapsiblePane: 'k-collapsible-pane',\n            vertical: 'k-vertical'\n        };\n        var Pane = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                element = that.element;\n                element.addClass(classNames.pane);\n                if (that.options.collapsible) {\n                    element.addClass(classNames.collapsiblePane);\n                }\n                this.history = [];\n                this.historyCallback = function (url, params, backButtonPressed) {\n                    var transition = that.transition;\n                    that.transition = null;\n                    if (SKIP_TRANSITION_ON_BACK_BUTTON && backButtonPressed) {\n                        transition = 'none';\n                    }\n                    return that.viewEngine.showView(url, transition, params);\n                };\n                this._historyNavigate = function (url) {\n                    if (url === BACK) {\n                        if (that.history.length === 1) {\n                            return;\n                        }\n                        that.history.pop();\n                        url = that.history[that.history.length - 1];\n                    } else {\n                        if (url instanceof View) {\n                            url = '';\n                        }\n                        that.history.push(url);\n                    }\n                    that.historyCallback(url, kendo.parseQueryStringParams(url));\n                };\n                this._historyReplace = function (url) {\n                    var params = kendo.parseQueryStringParams(url);\n                    that.history[that.history.length - 1] = url;\n                    that.historyCallback(url, params);\n                };\n                that.viewEngine = new ViewEngine(extend({}, {\n                    container: element,\n                    transition: options.transition,\n                    modelScope: options.modelScope,\n                    rootNeeded: !options.initial,\n                    serverNavigation: options.serverNavigation,\n                    remoteViewURLPrefix: options.root || '',\n                    layout: options.layout,\n                    $angular: options.$angular,\n                    showStart: function () {\n                        that.closeActiveDialogs();\n                    },\n                    after: function () {\n                    },\n                    viewShow: function (e) {\n                        that.trigger(VIEW_SHOW, e);\n                    },\n                    loadStart: function () {\n                    },\n                    loadComplete: function () {\n                    },\n                    sameViewRequested: function () {\n                        that.trigger(SAME_VIEW_REQUESTED);\n                    },\n                    viewTypeDetermined: function (e) {\n                        if (!e.remote || !that.options.serverNavigation) {\n                            that.trigger(NAVIGATE, { url: e.url });\n                        }\n                    }\n                }, this.options.viewEngine));\n                this._setPortraitWidth();\n                kendo.onResize(function () {\n                    that._setPortraitWidth();\n                });\n            },\n            closeActiveDialogs: function () {\n                var dialogs = this.element.find(roleSelector('actionsheet popover modalview')).filter(':visible');\n                dialogs.each(function () {\n                    kendo.widgetInstance($(this), ui).close();\n                });\n            },\n            navigateToInitial: function () {\n                var initial = this.options.initial;\n                if (initial) {\n                    this.navigate(initial);\n                }\n                return initial;\n            },\n            options: {\n                name: 'Pane',\n                portraitWidth: '',\n                transition: '',\n                layout: '',\n                collapsible: false,\n                initial: null,\n                modelScope: window\n            },\n            events: [\n                NAVIGATE,\n                VIEW_SHOW,\n                SAME_VIEW_REQUESTED\n            ],\n            append: function (html) {\n                return this.viewEngine.append(html);\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                if (that.viewEngine) {\n                    that.viewEngine.destroy();\n                }\n            },\n            navigate: function (url, transition) {\n                if (url instanceof View) {\n                    url = url.id;\n                }\n                this.transition = transition;\n                this._historyNavigate(url);\n            },\n            replace: function (url, transition) {\n                if (url instanceof View) {\n                    url = url.id;\n                }\n                this.transition = transition;\n                this._historyReplace(url);\n            },\n            view: function () {\n                return this.viewEngine.view();\n            },\n            _setPortraitWidth: function () {\n                var width, portraitWidth = this.options.portraitWidth;\n                if (portraitWidth) {\n                    width = kendo.mobile.application.element.is(DOT + classNames.vertical) ? portraitWidth : 'auto';\n                    this.element.css('width', width);\n                }\n            }\n        });\n        Pane.wrap = function (element, options) {\n            if (!element.is(roleSelector('view'))) {\n                element = element.wrap('<div data-' + kendo.ns + 'role=\"view\" data-stretch=\"true\"></div>').parent();\n            }\n            var paneContainer = element.wrap('<div class=\"' + classNames.paneWrapper + ' k-widget\"><div></div></div>').parent();\n            var pane = new Pane(paneContainer, options);\n            pane.navigate('');\n            return pane;\n        };\n        kendo.Pane = Pane;\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}