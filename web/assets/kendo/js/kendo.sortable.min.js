/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.sortable.min",["kendo.draganddrop.min"],e)}(function(){return function(e,t){function n(t,n){try{return e.contains(t,n)||t==n}catch(i){return!1}}function i(e){return e.clone()}function r(e){return e.clone().removeAttr("id").css("visibility","hidden")}var o=window.kendo,s=o.ui.Widget,l=o._outerWidth,a=o._outerHeight,h="start",d="beforeMove",c="move",u="end",g="change",f="cancel",p="sort",m="remove",_="receive",v=">*",x=-1,y=s.extend({init:function(e,t){var n=this;s.fn.init.call(n,e,t),n.options.placeholder||(n.options.placeholder=r),n.options.hint||(n.options.hint=i),n.draggable=n._createDraggable()},events:[h,d,c,u,g,f],options:{name:"Sortable",hint:null,placeholder:null,filter:v,holdToDrag:!1,disabled:null,container:null,connectWith:null,handler:null,cursorOffset:null,axis:null,ignore:null,autoScroll:!1,cursor:"auto",moveOnDragEnter:!1},destroy:function(){this.draggable.destroy(),s.fn.destroy.call(this)},_createDraggable:function(){var t=this,n=t.element,i=t.options;return new o.ui.Draggable(n,{filter:i.filter,hint:o.isFunction(i.hint)?i.hint:e(i.hint),holdToDrag:i.holdToDrag,container:i.container?e(i.container):null,cursorOffset:i.cursorOffset,axis:i.axis,ignore:i.ignore,autoScroll:i.autoScroll,dragstart:e.proxy(t._dragstart,t),dragcancel:e.proxy(t._dragcancel,t),drag:e.proxy(t._drag,t),dragend:e.proxy(t._dragend,t)})},_dragstart:function(t){var n=this.draggedElement=t.currentTarget,i=this.options.disabled,r=this.options.handler,s=this.options.placeholder,l=this.placeholder=e(o.isFunction(s)?s.call(this,n):s);i&&n.is(i)?t.preventDefault():r&&!e(t.initialTarget).is(r)?t.preventDefault():this.trigger(h,{item:n,draggableEvent:t})?t.preventDefault():(n.css("display","none"),n.before(l),this._setCursor())},_dragcancel:function(){this._cancel(),this.trigger(f,{item:this.draggedElement}),this._resetCursor()},_drag:function(n){var i,r,o,s,l,a=this.draggedElement,h=this._findTarget(n),d={left:n.x.location,top:n.y.location},c={x:n.x.delta,y:n.y.delta},u=this.options.axis,g=this.options.moveOnDragEnter,f={item:a,list:this,draggableEvent:n};if("x"===u||"y"===u)return this._movementByAxis(u,d,c[u],f),t;if(h){if(i=this._getElementCenter(h.element),r={left:Math.round(d.left-i.left),top:Math.round(d.top-i.top)},e.extend(f,{target:h.element}),h.appendToBottom)return this._movePlaceholder(h,null,f),t;if(h.appendAfterHidden&&this._movePlaceholder(h,"next",f),this._isFloating(h.element)?c.x<0&&g||!g&&r.left<0?o="prev":(c.x>0&&g||!g&&r.left>0)&&(o="next"):c.y<0&&g||!g&&r.top<0?o="prev":(c.y>0&&g||!g&&r.top>0)&&(o="next"),o){for(l="prev"===o?jQuery.fn.prev:jQuery.fn.next,s=l.call(h.element);s.length&&!s.is(":visible");)s=l.call(s);s[0]!=this.placeholder[0]&&this._movePlaceholder(h,o,f)}}},_dragend:function(n){var i,r,o,s,l=this.placeholder,a=this.draggedElement,h=this.indexOf(a),d=this.indexOf(l),c=this.options.connectWith;return this._resetCursor(),o={action:p,item:a,oldIndex:h,newIndex:d,draggableEvent:n},d>=0?r=this.trigger(u,o):(i=l.parents(c).getKendoSortable(),o.action=m,s=e.extend({},o,{action:_,oldIndex:x,newIndex:i.indexOf(l)}),r=!(!this.trigger(u,o)&&!i.trigger(u,s))),r||d===h?(this._cancel(),t):(l.replaceWith(a),a.show(),this.draggable.dropped=!0,o={action:this.indexOf(a)!=x?p:m,item:a,oldIndex:h,newIndex:this.indexOf(a),draggableEvent:n},this.trigger(g,o),i&&(s=e.extend({},o,{action:_,oldIndex:x,newIndex:i.indexOf(a)}),i.trigger(g,s)),t)},_findTarget:function(n){var i,r,o=this._findElementUnderCursor(n),s=this.options.connectWith;return e.contains(this.element[0],o)?(i=this.items(),r=i.filter(o)[0]||i.has(o)[0],r?{element:e(r),sortable:this}:null):this.element[0]==o&&this._isEmpty()?{element:this.element,sortable:this,appendToBottom:!0}:this.element[0]==o&&this._isLastHidden()?(r=this.items().eq(0),{element:r,sortable:this,appendAfterHidden:!0}):s?this._searchConnectedTargets(o,n):t},_findElementUnderCursor:function(e){var t=o.elementUnderCursor(e),i=e.sender;return n(i.hint[0],t)&&(i.hint.hide(),t=o.elementUnderCursor(e),t||(t=o.elementUnderCursor(e)),i.hint.show()),t},_searchConnectedTargets:function(t,n){var i,r,o,s,l=e(this.options.connectWith);for(s=0;s<l.length;s++)if(i=l.eq(s).getKendoSortable(),e.contains(l[s],t)){if(i)return r=i.items(),o=r.filter(t)[0]||r.has(t)[0],o?(i.placeholder=this.placeholder,{element:e(o),sortable:i}):null}else if(l[s]==t){if(i&&i._isEmpty())return{element:l.eq(s),sortable:i,appendToBottom:!0};if(this._isCursorAfterLast(i,n))return o=i.items().last(),{element:o,sortable:i}}},_isCursorAfterLast:function(e,t){var n,i,r=e.items().last(),s={left:t.x.location,top:t.y.location};return n=o.getOffset(r),n.top+=a(r),n.left+=l(r),i=this._isFloating(r)?n.left-s.left:n.top-s.top,i<0},_movementByAxis:function(t,n,i,r){var o,s="x"===t?n.left:n.top,l=i<0?this.placeholder.prev():this.placeholder.next(),a=this.items();l.length&&!l.is(":visible")&&(l=i<0?l.prev():l.next()),a.filter(l).length&&(e.extend(r,{target:l}),o=this._getElementCenter(l),o&&(o="x"===t?o.left:o.top),l.length&&i<0&&s-o<0?this._movePlaceholder({element:l,sortable:this},"prev",r):l.length&&i>0&&s-o>0&&this._movePlaceholder({element:l,sortable:this},"next",r))},_movePlaceholder:function(e,t,n){var i=this.placeholder;e.sortable.trigger(d,n)||(t?"prev"===t?e.element.before(i):"next"===t&&e.element.after(i):e.element.append(i),e.sortable.trigger(c,n))},_setCursor:function(){var t,n=this.options.cursor;n&&"auto"!==n&&(t=e(document.body),this._originalCursorType=t.css("cursor"),t.css({cursor:n}),this._cursorStylesheet||(this._cursorStylesheet=e("<style>* { cursor: "+n+" !important; }</style>")),this._cursorStylesheet.appendTo(t))},_resetCursor:function(){this._originalCursorType&&(e(document.body).css("cursor",this._originalCursorType),this._originalCursorType=null,this._cursorStylesheet.remove())},_getElementCenter:function(e){var t=e.length?o.getOffset(e):null;return t&&(t.top+=a(e)/2,t.left+=l(e)/2),t},_isFloating:function(e){return/left|right/.test(e.css("float"))||/inline|table-cell/.test(e.css("display"))},_cancel:function(){this.draggedElement.show(),this.placeholder.remove()},_items:function(){var e,t=this.options.filter;return e=t?this.element.find(t):this.element.children()},indexOf:function(e){var t=this._items(),n=this.placeholder,i=this.draggedElement;return n&&e[0]==n[0]?t.not(i).index(e):t.not(n).index(e)},items:function(){var e=this.placeholder,t=this._items();return e&&(t=t.not(e)),t},_isEmpty:function(){return!this.items().length},_isLastHidden:function(){return 1===this.items().length&&this.items().is(":hidden")}});o.ui.plugin(y)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.sortable.min.js.map
