{"version": 3, "sources": ["kendo.mobile.splitview.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "ui", "mobile", "Widget", "EXPANED_PANE_SHIM", "View", "SplitView", "extend", "init", "element", "options", "pane", "modalViews", "that", "this", "fn", "call", "_id", "$angular", "_overlay", "_layout", "_style", "children", "_locate", "each", "idx", "compileMobileDirective", "panes", "_paramsHistory", "directiveSelector", "push", "content", "roleSelector", "initWidget", "roles", "expandedPaneShim", "appendTo", "_shimUserEvents", "UserEvents", "fastTap", "tap", "collapsePanes", "selectors", "name", "style", "expandPanes", "addClass", "removeClass", "transition", "attrValue", "prototype", "header", "add", "footer", "styles", "split", "showStart", "css", "inited", "_invokeNgController", "initial", "navigateToInitial", "navigate", "trigger", "view", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,0BAA2B,qBAAsBD,IAC1D,WA2GE,MAnGC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAMG,OAAOD,GAAIE,EAASF,EAAGE,OAAQC,EAAoB,wCAA2CC,EAAOJ,EAAGI,KACzIC,EAAYD,EAAKE,QACjBC,KAAM,SAAUC,EAASC,GACrB,GAAiBC,GAAMC,EAAnBC,EAAOC,IACXX,GAAOY,GAAGP,KAAKQ,KAAKH,EAAMJ,EAASC,GACnCD,EAAUI,EAAKJ,QACfZ,EAAEU,OAAOM,EAAMH,GACfG,EAAKI,MACAJ,EAAKH,QAAQQ,SAIdL,EAAKM,YAHLN,EAAKO,UACLP,EAAKM,YAITN,EAAKQ,SACLT,EAAaH,EAAQa,SAAST,EAAKU,QAAQ,cACtCV,EAAKH,QAAQQ,SAGdN,EAAWY,KAAK,SAAUC,EAAKhB,GAC3BV,EAAM2B,uBAAuB7B,EAAEY,GAAUC,EAAQQ,SAAS,MAH9DnB,EAAMG,OAAOM,KAAKI,GAMtBC,EAAKc,SACLd,EAAKe,kBACAf,EAAKH,QAAQQ,UAMdL,EAAKJ,QAAQa,SAASvB,EAAM8B,kBAAkB,SAASL,KAAK,WACxDb,EAAOZ,EAAM2B,uBAAuB7B,EAAEiB,MAAOJ,EAAQQ,SAAS,IAC9DL,EAAKc,MAAMG,KAAKnB,KAEpBE,EAAKJ,QAAQa,SAASvB,EAAM8B,kBAAkB,kBAAkBL,KAAK,WACjEzB,EAAM2B,uBAAuB7B,EAAEiB,MAAOJ,EAAQQ,SAAS,OAV3DL,EAAKkB,QAAQT,SAASvB,EAAMiC,aAAa,SAASR,KAAK,WACnDb,EAAOZ,EAAMkC,WAAWnB,QAAUb,EAAGiC,OACrCrB,EAAKc,MAAMG,KAAKnB,KAWxBE,EAAKsB,iBAAmBtC,EAAEO,GAAmBgC,SAASvB,EAAKJ,SAC3DI,EAAKwB,gBAAkB,GAAItC,GAAMuC,WAAWzB,EAAKsB,kBAC7CI,SAAS,EACTC,IAAK,WACD3B,EAAK4B,oBAIjBlB,QAAS,SAAUmB,GACf,MAAO5B,MAAKJ,QAAQQ,SAAWnB,EAAM8B,kBAAkBa,GAAa3C,EAAMiC,aAAaU,IAE3FhC,SACIiC,KAAM,YACNC,MAAO,cAEXC,YAAa,WACT/B,KAAKL,QAAQqC,SAAS,0BAE1BL,cAAe,WACX3B,KAAKL,QAAQsC,YAAY,0BAE7B3B,QAAS,WACL,GAAIP,GAAOC,KAAML,EAAUI,EAAKJ,OAChCI,GAAKmC,WAAajD,EAAMkD,UAAUxC,EAAS,cAC3CV,EAAMG,OAAOD,GAAGI,KAAK6C,UAAU9B,QAAQJ,KAAKF,MAC5Cf,EAAMG,OAAOM,KAAKM,KAAKqC,OAAOC,IAAItC,KAAKuC,SACvCxC,EAAKJ,QAAQqC,SAAS,gBACtBjC,EAAKkB,QAAQe,SAAS,qBAE1BzB,OAAQ,WACJ,GAAwDiC,GAApDV,EAAQ9B,KAAKJ,QAAQkC,MAAOnC,EAAUK,KAAKL,OAC3CmC,KACAU,EAASV,EAAMW,MAAM,KACrB1D,EAAE2B,KAAK8B,EAAQ,WACX7C,EAAQqC,SAAS,YAAchC,UAI3C0C,UAAW,WACP,GAAI3C,GAAOC,IACXD,GAAKJ,QAAQgD,IAAI,UAAW,IACvB5C,EAAK6C,OAWN5C,KAAK6C,uBAVL9C,EAAK6C,QAAS,EACd7D,EAAE2B,KAAKX,EAAKc,MAAO,WACXb,KAAKJ,QAAQkD,QACb9C,KAAK+C,oBAEL/C,KAAKgD,SAAS,MAGtBjD,EAAKkD,QAAQ,QAAUC,KAAMnD,KAIjCA,EAAKkD,QAAQ,QAAUC,KAAMnD,MAGrCZ,GAAGgE,OAAO3D,IACZN,OAAOD,MAAMmE,QACRlE,OAAOD,OACE,kBAAVH,SAAwBA,OAAOuE,IAAMvE,OAAS,SAAUwE,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.splitview.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.splitview', ['kendo.mobile.pane'], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.splitview',\n        name: 'SplitView',\n        category: 'mobile',\n        description: 'The mobile SplitView is a tablet-specific view that consists of two or more mobile Pane widgets.',\n        depends: ['mobile.pane']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.mobile.ui, Widget = ui.Widget, EXPANED_PANE_SHIM = '<div class=\\'km-expanded-pane-shim\\' />', View = ui.View;\n        var SplitView = View.extend({\n            init: function (element, options) {\n                var that = this, pane, modalViews;\n                Widget.fn.init.call(that, element, options);\n                element = that.element;\n                $.extend(that, options);\n                that._id();\n                if (!that.options.$angular) {\n                    that._layout();\n                    that._overlay();\n                } else {\n                    that._overlay();\n                }\n                that._style();\n                modalViews = element.children(that._locate('modalview'));\n                if (!that.options.$angular) {\n                    kendo.mobile.init(modalViews);\n                } else {\n                    modalViews.each(function (idx, element) {\n                        kendo.compileMobileDirective($(element), options.$angular[0]);\n                    });\n                }\n                that.panes = [];\n                that._paramsHistory = [];\n                if (!that.options.$angular) {\n                    that.content.children(kendo.roleSelector('pane')).each(function () {\n                        pane = kendo.initWidget(this, {}, ui.roles);\n                        that.panes.push(pane);\n                    });\n                } else {\n                    that.element.children(kendo.directiveSelector('pane')).each(function () {\n                        pane = kendo.compileMobileDirective($(this), options.$angular[0]);\n                        that.panes.push(pane);\n                    });\n                    that.element.children(kendo.directiveSelector('header footer')).each(function () {\n                        kendo.compileMobileDirective($(this), options.$angular[0]);\n                    });\n                }\n                that.expandedPaneShim = $(EXPANED_PANE_SHIM).appendTo(that.element);\n                that._shimUserEvents = new kendo.UserEvents(that.expandedPaneShim, {\n                    fastTap: true,\n                    tap: function () {\n                        that.collapsePanes();\n                    }\n                });\n            },\n            _locate: function (selectors) {\n                return this.options.$angular ? kendo.directiveSelector(selectors) : kendo.roleSelector(selectors);\n            },\n            options: {\n                name: 'SplitView',\n                style: 'horizontal'\n            },\n            expandPanes: function () {\n                this.element.addClass('km-expanded-splitview');\n            },\n            collapsePanes: function () {\n                this.element.removeClass('km-expanded-splitview');\n            },\n            _layout: function () {\n                var that = this, element = that.element;\n                that.transition = kendo.attrValue(element, 'transition');\n                kendo.mobile.ui.View.prototype._layout.call(this);\n                kendo.mobile.init(this.header.add(this.footer));\n                that.element.addClass('km-splitview');\n                that.content.addClass('km-split-content');\n            },\n            _style: function () {\n                var style = this.options.style, element = this.element, styles;\n                if (style) {\n                    styles = style.split(' ');\n                    $.each(styles, function () {\n                        element.addClass('km-split-' + this);\n                    });\n                }\n            },\n            showStart: function () {\n                var that = this;\n                that.element.css('display', '');\n                if (!that.inited) {\n                    that.inited = true;\n                    $.each(that.panes, function () {\n                        if (this.options.initial) {\n                            this.navigateToInitial();\n                        } else {\n                            this.navigate('');\n                        }\n                    });\n                    that.trigger('init', { view: that });\n                } else {\n                    this._invokeNgController();\n                }\n                that.trigger('show', { view: that });\n            }\n        });\n        ui.plugin(SplitView);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}