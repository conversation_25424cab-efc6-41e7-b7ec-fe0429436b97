{"version": 3, "sources": ["kendo.scheduler.timelineview.js"], "names": ["f", "define", "$", "undefined", "toInvariantTime", "date", "staticDate", "Date", "setTime", "getMilliseconds", "getWorkDays", "options", "workDays", "dayIndex", "workWeekStart", "workWeekEnd", "Math", "abs", "push", "setColspan", "columnLevel", "i", "count", "columns", "length", "colspan", "collidingEvents", "elements", "left", "right", "idx", "startPosition", "overlaps", "endPosition", "rectLeft", "rectRight", "eventsForSlot", "event", "events", "TimelineView", "kendo", "window", "ui", "SchedulerView", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "extend", "proxy", "getDate", "MS_PER_DAY", "MS_PER_MINUTE", "CURRENT_TIME_MARKER_CLASS", "CURRENT_TIME_MARKER_ARROW_CLASS", "SCHEDULER_HEADER_WRAP_CLASS", "INVERSE_COLOR_CLASS", "BORDER_SIZE_COEFF", "NS", "EVENT_TEMPLATE", "template", "DATA_HEADER_TEMPLATE", "EVENT_WRAPPER_STRING", "TimelineGroupedView", "Class", "init", "view", "this", "_view", "_getTimeSlotByPosition", "x", "y", "groupIndex", "group", "groups", "timeSlotByPosition", "_hideHeaders", "timesHeader", "find", "hide", "<PERSON><PERSON><PERSON><PERSON>", "_setColspan", "timeColumn", "_createRowsLayout", "resources", "rows", "groupHeaderTemplate", "_createVerticalColumnsLayout", "_createColumnsLayout", "_getRowCount", "_groupCount", "_getGroupsCount", "_addContent", "dates", "columnCount", "groupsCount", "rowCount", "start", "end", "slotTemplate", "isVerticalGrouped", "rowIdx", "groupIdx", "html", "appendRow", "tmplDate", "content", "classes", "_resourceBySlot", "isToday", "workDayStart", "workDayEnd", "_isWorkDay", "_forTimeRange", "_addTimeSlotsCollections", "groupCount", "datesCount", "tableRows", "interval", "isVerticallyGrouped", "rowMultiplier", "time", "rowIndex", "cellMultiplier", "cells", "cellsPerGroup", "cellsPerDay", "dateIndex", "cellOffset", "cellIndex", "floor", "children", "startTime", "_addTimeSlotToCollection", "_getVerticalGroupCount", "_getVerticalRowCount", "eventGroups", "maxRowCount", "_isVerticallyGrouped", "_renderEvent", "eventGroup", "adjustedEvent", "range", "container", "eventObject", "element", "_createEventElement", "occurrence", "head", "tail", "appendTo", "css", "top", "height", "eventHeight", "_startTime", "_endTime", "uid", "slotRange", "offsetTop", "_inverseEventColor", "addContinuousEvent", "isAllDay", "_arrangeRows", "_verticalCountForLevel", "level", "_rowCountForLevel", "_horizontalCountForLevel", "_columnCountForLevel", "_updateCurrentVerticalTimeMarker", "ranges", "currentTime", "elementHtml", "headerWrap", "round", "innerRect", "getTime", "timesTableMarker", "prependTo", "addClass", "_adjustLeftPosition", "prev", "position", "width", "scrollHeight", "_changeGroup", "_prevGroupSlot", "slot", "isDay", "collection", "_collection", "last", "_nextGroupSlot", "first", "_verticalSlots", "selection", "reverse", "_verticalMethod", "_normalizeVerticalSelection", "_horizontalSlots", "method", "startSlot", "endSlot", "multiple", "result", "_isHorizontallyGrouped", "_changeVerticalViewPeriod", "_changeHorizontalViewPeriod", "slots", "shift", "_changeViewPeriod", "_updateDirection", "_createMoveHint", "rect", "hint", "snap", "offsetHeight", "inverseColor", "_appendMoveHint", "_isRtl", "scrollWidth", "clientWidth", "TimelineGroupedByDateView", "times", "eq", "_createDateLayout", "subColumns", "isMajorTickColumn", "isMiddleColumn", "isLastSlotColumn", "minorTickColumns", "workDateIndex", "tempStart", "minorTickCount", "msMajorInterval", "majorTick", "msInterval", "_dates", "startIndex", "endIndex", "currentSlot", "date<PERSON><PERSON><PERSON>", "eventObjects", "index", "_timeSlotCollections", "_slots", "timeSlotRanges", "firstTimesCell", "lastTimesCell", "markerTopPosition", "timesTableMarkerCss", "previous", "_changeDate", "collections", "_getCollections", "tempSlot", "slotIdx", "startEnd", "offsetLeft", "offsetWidth", "scheduler", "that", "fn", "call", "_<PERSON><PERSON>iew", "_getGroupedView", "title", "name", "_workDays", "_templates", "_editable", "calculateDateRange", "_groups", "_currentTime", "_isGroupedByDate", "_getNextEventIndexBySlot", "sortedEvents", "tempIndex", "startDate", "_getSelectedSlot", "pad", "_getSortedEvents", "uniqueAllEvents", "sort", "second", "isDaySlot", "_currentTimeMarkerUpdater", "_updateCurrentTimeMarker", "timezone", "timezoneOffset", "currentGroup", "utcCurrentTime", "slotElement", "remove", "_isInDateSlot", "currentTimeMarker", "useLocalTimezone", "dataSource", "schema", "offset", "convert", "getTimezoneOffset", "orientation", "toUtcTime", "slotByStartDate", "setUpdateTimer", "markerOptions", "updateInterval", "_currentTimeUpdateTimer", "setInterval", "editable", "_isMobile", "_touchEditable", "_mouseEditable", "on", "e", "trigger", "closest", "attr", "preventDefault", "create", "resourceInfo", "_slotByPosition", "pageX", "pageY", "eventInfo", "endDate", "update", "threshold", "support", "mobileOS", "android", "_addUserEvents", "UserEvents", "useClickAsTap", "browser", "edge", "filter", "tap", "_scrolling", "location", "_editUserEvents", "eventElement", "touchElement", "target", "touch", "initialTouch", "hasClass", "mozilla", "scrollLeft", "msie", "webkit", "scrollTop", "ceil", "selectedDateFormat", "selectedShortDateFormat", "selectedMobileDateFormat", "today", "endTime", "showWorkHours", "event<PERSON>in<PERSON><PERSON>th", "columnWidth", "majorTimeHeaderTemplate", "eventTemplate", "dateHeaderTemplate", "footer", "command", "messages", "defaultRowText", "showFullDay", "showWorkDay", "settings", "Template", "templateSettings", "_eventTmpl", "_render", "_startDate", "_endDate", "_calculateSlotRanges", "createLayout", "_layout", "_content", "_footer", "_setContentWidth", "refreshLayout", "th", "currentTarget", "contentWidth", "contentTable", "min<PERSON><PERSON><PERSON>", "calculatedWidth", "add", "slotRanges", "rangeStart", "rangeEnd", "slotStartTime", "slotEndTime", "_slotRanges", "min", "max", "action", "verticalByDate", "msMin", "msMax", "majorTickDivider", "isLastMajorSlot", "minorTickIndex", "timeColumns", "text", "groupedView", "minorTickSlots", "className", "minorTicks", "middleColumn", "lastSlotColumn", "minorSlotsCount", "slice", "groupedResources", "_groupOrientation", "isWorkDay", "currentDate", "columnLevels", "rowLevel", "rowLevels", "day", "getDay", "append", "_addResourceView", "addTimeSlotCollection", "addDays", "_timeSlotGroups", "_timeSlotInterval", "cell", "getTimeSlotCollection", "UTC", "getFullYear", "getMonth", "setAttribute", "addTimeSlot", "visibleEndDate", "nextDate", "nextDay", "previousDate", "previousDay", "render", "eventsByResource", "_headerColumnCount", "data", "Query", "field", "dir", "toArray", "_eventsByResource", "_renderEvents", "_setRowsHeight", "_positionEvents", "eventsForGroup", "eventUid", "eventIndex", "isArray", "_positionEvent", "rowsCount", "rowHeight", "timesRow", "row", "eventBottomOffset", "_getBottomRowOffset", "verticalGroupCount", "_refreshSlots", "minOffset", "maxOffset", "isMobile", "slotsCollection", "lastSlot", "offsetRight", "refresh", "itemIdx", "value", "eventsFilteredByResource", "resource", "_resourceValue", "operator", "groupEqFilter", "_isInTimeSlot", "slotIndex", "_adjustEvent", "eventStartTime", "_time", "eventEndTime", "adjustedStartDate", "adjustedEndDate", "clone", "isMultiDayEvent", "duration", "_continuousEvents", "showDelete", "destroy", "resizable", "resize", "eventStartDate", "eventEndDate", "eventResources", "apply", "ns", "singleDay", "angular", "dataItem", "rowEvents", "j", "event<PERSON>ength", "addEvent", "createRows", "_updateEventForSelection", "_eventOptionsForMove", "_updateEventForResize", "set", "_updateMoveHint", "distance", "rangeIndex", "clonedEvent", "<PERSON><PERSON><PERSON><PERSON>", "_removeMoveHint", "_moveHint", "_updateResizeHint", "startRect", "format", "_removeResizeHint", "_createResizeHint", "_resizeHint", "toString", "toLocalDate", "selectionByElement", "vertical", "collectionIndex", "backward", "prevGroupSlot", "nextGroupSlot", "newStart", "newEnd", "_isInRange", "move", "key", "handled", "keys", "DOWN", "UP", "LEFT", "RIGHT", "off", "clearInterval", "TimelineWeekView", "selectedDate", "dayOfWeek", "calendarInfo", "firstDay", "TimelineWorkWeekView", "weekStart", "TimelineMonthView", "firstDayOfMonth", "lastDayOfMonth", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,gCAAiC,wBAAyBD,IACnE,WAq3DE,MA52DC,UAAUE,EAAGC,GAGV,QAASC,GAAgBC,GACrB,GAAIC,GAAa,GAAIC,MAAK,KAAM,EAAG,EAAG,EAAG,EAAG,EAE5C,OADAC,GAAQF,EAAYG,EAAgBJ,IAC7BC,EAEX,QAASI,GAAYC,GAArB,GACQC,MACAC,EAAWF,EAAQG,cAAgB,EACnCC,EAAcC,KAAKC,IAAIN,EAAQI,YAAc,EAEjD,KADAH,EAASM,KAAKL,GACPE,GAAeF,GACdA,EAAW,EACXA,GAAY,EAEZA,IAEJD,EAASM,KAAKL,EAElB,OAAOD,GAEX,QAASO,GAAWC,GAApB,GAGiBC,GAFTC,EAAQ,CACZ,IAAIF,EAAYG,QAAS,CACrB,IAASF,EAAI,EAAGA,EAAID,EAAYG,QAAQC,OAAQH,IAC5CC,GAASH,EAAWC,EAAYG,QAAQF,GAG5C,OADAD,GAAYK,QAAUH,EACfA,EAGP,MADAF,GAAYK,QAAU,EACf,EAGf,QAASC,GAAgBC,EAAUC,EAAMC,GACrC,GAAIC,GAAKC,EAAeC,EAAUC,CAClC,KAAKH,EAAMH,EAASH,OAAS,EAAGM,GAAO,EAAGA,IACtCC,EAAgBJ,EAASG,GAAKI,SAC9BD,EAAcN,EAASG,GAAKK,UAC5BH,EAAWD,GAAiBH,GAAQK,GAAeL,GAC/CI,GAAYD,GAAiBH,GAAQK,GAAeJ,GAASD,GAAQG,GAAiBF,GAASE,KAC3FA,EAAgBH,IAChBA,EAAOG,GAEPE,EAAcJ,IACdA,EAAQI,GAIpB,OAAOG,GAAcT,EAAUC,EAAMC,GAEzC,QAASO,GAAcT,EAAUC,EAAMC,GAAvC,GAEaC,GACDO,EAFJC,IACJ,KAASR,EAAM,EAAGA,EAAMH,EAASH,OAAQM,IACjCO,GACAH,SAAUP,EAASG,GAAKI,SACxBC,UAAWR,EAASG,GAAKK,YAEzBE,EAAMH,SAAWN,GAAQS,EAAMF,UAAYP,GAAQS,EAAMH,UAAYN,GAAQS,EAAMF,WAAaN,IAChGS,EAAOpB,KAAKS,EAASG,GAG7B,OAAOQ,GAhEd,GAumBOC,GAtmBAC,EAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIlC,EAAUgC,EAAMnC,KAAKG,QAASmC,EAAgBD,EAAGC,cAAeC,EAAaJ,EAAMK,YAAaC,EAAcN,EAAMO,aAAcC,EAAS9C,EAAE8C,OAAQC,EAAQ/C,EAAE+C,MAAOC,EAAUV,EAAMnC,KAAK6C,QAASzC,EAAkB+B,EAAMnC,KAAKI,gBAAiB0C,EAAaX,EAAMnC,KAAK8C,WAAYC,EAAgBZ,EAAMnC,KAAK+C,cAAeC,EAA4B,iBAAkBC,EAAkC,uBAAwBC,EAA8B,0BAA2BC,EAAsB,kBAAmBC,EAAoB,MAAQC,EAAK,qBAChlBC,EAAiBnB,EAAMoB,SAAS,wJAAmKC,EAAuBrB,EAAMoB,SAAS,wEAA6EE,EAAuB,i6BAgE7UC,EAAsBvB,EAAMwB,MAAMhB,QAClCiB,KAAM,SAAUC,GACZC,KAAKC,MAAQF,GAEjBG,uBAAwB,SAAUC,EAAGC,EAAGC,GACpC,GAAIC,GAAQN,KAAKC,MAAMM,OAAOF,EAC9B,OAAOC,GAAME,mBAAmBL,EAAGC,IAEvCK,aAAc,WACV,GAAIV,GAAOC,KAAKC,KAChBF,GAAKW,YAAYC,KAAK,iBAAiBC,OACvCb,EAAKc,YAAYF,KAAK,iBAAiBC,QAE3CE,YAAa,SAAUC,GACnB/D,EAAW+D,IAEfC,kBAAmB,SAAUC,EAAWC,EAAMC,GAC1C,GAAIpB,GAAOC,KAAKC,KAChB,OAAOF,GAAKiB,kBAAkBC,EAAWC,EAAMC,IAEnDC,6BAA8B,SAAUH,EAAWC,EAAMC,EAAqB/D,GAC1E,MAAOA,IAEXiE,qBAAsB,SAAUJ,EAAW7D,EAAS+D,GAChD,GAAIpB,GAAOC,KAAKC,KAChB,OAAOF,GAAKsB,qBAAqBJ,EAAW7D,EAAS+D,IAEzDG,aAAc,WACV,GAAIvB,GAAOC,KAAKC,KAChB,OAAOF,GAAKwB,eAEhBC,gBAAiB,WACb,MAAO,IAEXC,YAAa,SAAUC,EAAOC,EAAaC,EAAaC,EAAUC,EAAOC,EAAKC,EAAcC,GAA/E,GA6BAC,GAEIC,EACIxE,EAASN,EA/BtB0C,EAAOC,KAAKC,MACZmC,EAAO,GACP5F,EAAUuD,EAAKvD,QACf6F,EAAY,SAAUnG,GAAV,GAGRoG,GAFAC,EAAU,GACVC,EAAU,GAEVvB,EAAY,SAAUZ,GACtB,MAAO,YACH,MAAON,GAAK0C,iBAAkBpC,WAAYA,KAiBlD,OAdIhC,GAAMnC,KAAKwG,QAAQhB,EAAM/D,MACzB6E,GAAW,YAEXnE,EAAMnC,KAAKI,gBAAgBJ,GAAQmC,EAAMnC,KAAKI,gBAAgBE,EAAQmG,eAAiBtE,EAAMnC,KAAKI,gBAAgBJ,IAASmC,EAAMnC,KAAKI,gBAAgBE,EAAQoG,cAAgB7C,EAAK8C,WAAWnB,EAAM/D,OACpM6E,GAAW,mBAEfD,GAAW,OAAqB,KAAZC,EAAiB,WAAaA,EAAU,IAAM,IAAM,IACxEF,EAAWjE,EAAMnC,KAAK6C,QAAQ2C,EAAM/D,IACpCU,EAAMnC,KAAKG,QAAQiG,EAAUjE,EAAMnC,KAAKI,gBAAgBJ,IACxDqG,GAAWP,GACP9F,KAAMoG,EACNrB,UAAWA,EAAUgB,EAAoBC,EAASC,KAEtDI,GAAW,QAGf,KAASL,EAAS,EAAGA,EAASL,EAAUK,IAAU,CAE9C,IADAE,GAAQ,OACCD,EAAW,EAAGA,EAAWP,EAAaO,IAC3C,IAASxE,EAAM,EAAGN,EAASsE,EAAahE,EAAMN,EAAQM,IAClDyE,GAAQrC,EAAK+C,cAAchB,EAAOC,EAAKM,EAG/CD,IAAQ,QAEZ,MAAOA,IAEXW,yBAA0B,SAAUC,EAAYC,EAAYC,EAAWC,EAAUC,GAAvD,GAMb/C,GACDgD,EACA/C,EACAgD,EAIAC,EACAC,EAIAC,EACAC,EACAC,EACKC,EACDC,EAEKC,EAvBb/D,EAAOC,KAAKC,MACZ4B,EAAWqB,EAAU7F,MAIzB,KAHI+F,IACAvB,EAAWhF,KAAKkH,MAAMlC,EAAWmB,IAE5B3C,EAAa,EAAGA,EAAa2C,EAAY3C,IAe9C,IAdIgD,EAAgB,EAChB/C,EAAQP,EAAKQ,OAAOF,GAEpB+C,IACAC,EAAgBhD,GAEhBkD,EAAWF,EAAgBxB,EAC3B2B,EAAiB,EAChBJ,IACDI,EAAiBnD,GAEjBoD,EAAQP,EAAUK,GAAUS,SAC5BN,EAAgBD,EAAMpG,QAAW+F,EAAmC,EAAbJ,GACvDW,EAAcD,EAAgBT,EACzBW,EAAY,EAAGA,EAAYX,EAAYW,IAG5C,IAFIC,EAAaD,EAAYD,EAAcD,EAAgBF,EAC3DF,EAAOhH,EAAgB,GAAIF,QAAM2D,EAAKkE,eAC7BH,EAAY,EAAGA,EAAYH,EAAaG,IAC7C/D,EAAKmE,yBAAyB5D,EAAOmD,EAAOK,EAAWD,EAAYD,EAAWN,EAAMH,GACpFG,GAAQH,GAKxBgB,uBAAwB,SAAUvC,GAC9B,MAAOA,IAEXwC,qBAAsB,SAAUC,EAAahE,EAAYiE,GACrD,GAAIvE,GAAOC,KAAKC,KAChB,OAAOF,GAAKwE,uBAAyBF,EAAYhE,GAAYiE,YAAcA,GAE/EE,aAAc,SAAUC,EAAYvG,EAAOwG,EAAepE,EAAOqE,EAAOC,GAA1D,GAQNC,GAPA9E,EAAOC,KAAKC,MACZ6E,EACM/E,EAAKgF,oBAAoBL,EAAcM,WAAY9G,EAAOyG,EAAMM,MAAQP,EAAcO,KAAMN,EAAMO,MAAQR,EAAcQ,KAClIJ,GAAQK,SAASP,GAAWQ,KACxBC,IAAK,EACLC,OAAQvF,EAAKvD,QAAQ+I,cAErBV,GACA/C,MAAO4C,EAAcM,WAAWQ,YAAcd,EAAcM,WAAWlD,MACvEC,IAAK2C,EAAcM,WAAWS,UAAYf,EAAcM,WAAWjD,IACnE+C,QAASA,EACTY,IAAKxH,EAAMwH,IACXC,UAAWhB,EACXpB,SAAU,EACVqC,UAAW,GAEfnB,EAAWtG,OAAOD,EAAMwH,KAAOb,EAC/B9E,EAAK8F,mBAAmBf,GACxB/E,EAAK+F,mBAAmBxF,EAAOqE,EAAOG,EAAS5G,EAAM6H,UACrDhG,EAAKiG,aAAanB,EAAaF,EAAOF,IAE1CwB,uBAAwB,SAAUC,GAC9B,GAAInG,GAAOC,KAAKC,KAChB,OAAOF,GAAKoG,kBAAkBD,IAElCE,yBAA0B,SAAUF,GAChC,GAAInG,GAAOC,KAAKC,KAChB,OAAOF,GAAKsG,qBAAqBH,IAErCI,iCAAkC,SAAUC,EAAQC,GAAlB,GAC1BzG,GAAOC,KAAKC,MACZwG,EAAc,eAAkBvH,EAA4B,WAC5DwH,EAAa3G,EAAKc,YAAYF,KAAK,IAAMvB,GACzC3B,EAAOZ,KAAK8J,MAAMJ,EAAO,GAAGK,UAAUJ,EAAa,GAAIpK,MAAKoK,EAAYK,UAAY,IAAI,GAAOpJ,MAC/FqJ,EAAmB/K,EAAE0K,GAAaM,UAAUL,GAAYM,SAAS7H,EAAkC,QACvG2H,GAAiB1B,KACb3H,KAAMsC,EAAKkH,oBAAoBxJ,EAAOgB,EAAWqI,GAAoBxH,EAAoB,GACzF+F,IAAKqB,EAAW/F,KAAK,WAAWuG,OAAOC,WAAW9B,MAEtDtJ,EAAE0K,GAAaM,UAAUhH,EAAKwC,SAAS6C,KACnC3H,KAAMsC,EAAKkH,oBAAoBxJ,GAC/B2J,MAAO,MACP9B,OAAQvF,EAAKwC,QAAQ,GAAG8E,aAAe,EACvChC,IAAK,KAGbiC,aAAc,WACV,MAAOtL,IAEXuL,eAAgB,SAAUC,EAAMlH,EAAOmH,GAAvB,GAKJC,GAJJ3H,EAAOC,KAAKC,KAChB,OAAIF,GAAKwE,uBACEiD,GAEHE,EAAapH,EAAMqH,YAAY,EAAGF,GAC/BC,EAAWE,SAG1BC,eAAgB,SAAUL,EAAMlH,EAAOmH,GAAvB,GAKJC,GAJJ3H,EAAOC,KAAKC,KAChB,OAAIF,GAAKwE,uBACEiD,GAEHE,EAAapH,EAAMqH,YAAY,EAAGF,GAC/BC,EAAWI,UAG1BC,eAAgB,SAAUC,EAAWC,GACjC,GAAIlI,GAAOC,KAAKC,KAChB,OAAOF,GAAKuH,aAAaU,EAAWC,IAExCC,gBAAiB,SAAUD,GACvB,MAAOA,GAAU,WAAa,aAElCE,4BAA6B,WACzB,MAAOnM,IAEXoM,iBAAkB,SAAUJ,EAAW1H,EAAO+H,EAAQC,EAAWC,EAASC,EAAUP,GAAlE,GACVlI,GAAOC,KAAKC,MACZwI,IAMJ,OALAA,GAAOH,UAAYhI,EAAM+H,GAAQC,GACjCG,EAAOF,QAAUjI,EAAM+H,GAAQE,GAC1BC,IAAYzI,EAAK2I,0BAA8BD,EAAOH,WAAcG,EAAOF,UAC5EE,EAAOH,UAAYG,EAAOF,QAAUxI,EAAKuH,aAAaU,EAAWC,IAE9DQ,GAEXE,0BAA2B,WACvB,OAAO,GAEXC,4BAA6B,SAAUC,EAAOC,EAAOd,EAAWC,GAC5D,GAAIlI,GAAOC,KAAKC,KAChB,SAAM4I,EAAMP,WAAcO,EAAMN,SAAaO,IAAS/I,EAAKgJ,kBAAkBf,EAAWC,GAAS,KAKrGe,iBAAkB,SAAUhB,EAAWzB,EAAQuC,EAAOb,GAClD,GAAIlI,GAAOC,KAAKC,KAChBF,GAAKiJ,iBAAiBhB,EAAWzB,EAAQuC,EAAOb,GAAS,IAE7DgB,gBAAiB,SAAUtE,EAAOD,GAAjB,GAKTwE,GACA9B,EAIA3J,EACA2H,EAVArF,EAAOC,KAAKC,MACZqI,EAAY3D,EAAM7C,MAClBqH,EAAOpJ,EAAKgF,oBAAoBL,EAAcM,WAAYN,EAAcM,YAAY,GAAO,EAC/FmE,GAAKnC,SAAS,qBACVkC,EAAOvE,EAAMiC,UAAUlC,EAAcM,WAAWlD,MAAO4C,EAAcM,WAAWjD,IAAKhC,EAAKvD,QAAQ4M,MAClGhC,EAAQ8B,EAAKxL,MAAQwL,EAAKzL,KAAO,EACjC2J,EAAQ,IACRA,EAAQ,GAER3J,EAAOsC,EAAKkH,oBAAoBiC,EAAKzL,MACrC2H,GACA3H,KAAMA,EACN4H,IAAKiD,EAAU1C,UACfN,OAAQgD,EAAUe,aAAe,EACjCjC,MAAOA,GAEX+B,EAAK/D,IAAIA,GACLV,EAAcM,WAAWsE,cACzBH,EAAKnC,SAAS3H,GAElBU,EAAKwJ,gBAAgBJ,IAEzBlC,oBAAqB,SAAUxJ,GAC3B,GAAIsC,GAAOC,KAAKC,KAIhB,OAHIF,GAAKyJ,SACL/L,GAAQsC,EAAKwC,QAAQ,GAAGkH,YAAc1J,EAAKwC,QAAQ,GAAGmH,aAEnDjM,KAGXkM,EAA4BtL,EAAMwB,MAAMhB,QACxCiB,KAAM,SAAUC,GACZC,KAAKC,MAAQF,GAEjBG,uBAAwB,SAAUC,EAAGC,EAAGC,GACpC,GAAIC,GAAQN,KAAKC,MAAMM,OAAOF,EAC9B,OAAOC,GAAME,mBAAmBL,EAAGC,GAAG,IAE1CK,aAAc,WACV,GAAIV,GAAOC,KAAKC,KACXF,GAAKwE,uBAINxE,EAAK6J,MAAMjJ,KAAK,WAAWC,QAH3Bb,EAAKW,YAAYC,KAAK,YAAYkJ,GAAG,GAAGjJ,OACxCb,EAAKc,YAAYF,KAAK,YAAYkJ,GAAG,GAAGjJ,SAKhDE,YAAa,aAEbE,kBAAmB,SAAUC,EAAWC,EAAMC,EAAqB/D,GAC/D,GAAI2C,GAAOC,KAAKC,KAChB,OAAOF,GAAK+J,kBAAkB1M,EAAS,MAAM,IAEjDgE,6BAA8B,SAAUH,EAAWC,EAAMC,GACrD,GAAIpB,GAAOC,KAAKC,KAChB,OAAOF,GAAKsB,qBAAqBJ,EAAW,KAAME,IAEtDE,qBAAsB,SAAUJ,EAAW7D,EAAS+D,EAAqB4I,GACrE,GAAIhK,GAAOC,KAAKC,KAChB,OAAOF,GAAKsB,qBAAqBJ,EAAW7D,EAAS+D,EAAqB4I,GAAY,IAE1FzI,aAAc,SAAU4E,GACpB,GAAInG,GAAOC,KAAKC,KAChB,OAAOF,GAAKoG,kBAAkBD,IAElC1E,gBAAiB,WACb,GAAIzB,GAAOC,KAAKC,KAChB,OAAOF,GAAKwB,eAEhBE,YAAa,SAAUC,EAAOC,EAAaC,EAAaC,EAAUC,EAAOC,EAAKC,EAAcC,GAA/E,GA8BoL2B,GACpL1B,EAOIvE,EAASN,EArClB0C,EAAOC,KAAKC,MACZmC,EAAO,GACP5F,EAAUuD,EAAKvD,QACf6F,EAAY,SAAUnG,EAAM8N,EAAmBC,EAAgBC,EAAkBC,EAAkBhI,GAAvF,GAGRG,GAFAC,EAAU,GACVC,EAAU,GAEV4H,EAAgBrK,EAAKwE,uBAAyBX,EAAYjG,EAC1DsD,EAAY,SAAUZ,GACtB,MAAO,YACH,MAAON,GAAK0C,iBAAkBpC,WAAYA,KAiBlD,OAdIhC,GAAMnC,KAAKwG,QAAQhB,EAAM/D,MACzB6E,GAAW,YAEXnE,EAAMnC,KAAKI,gBAAgBJ,GAAQmC,EAAMnC,KAAKI,gBAAgBE,EAAQmG,eAAiBtE,EAAMnC,KAAKI,gBAAgBJ,IAASmC,EAAMnC,KAAKI,gBAAgBE,EAAQoG,cAAgB7C,EAAK8C,WAAWnB,EAAM0I,OACpM5H,GAAW,mBAEfD,GAAW,OAAqB,KAAZC,EAAiB,WAAaA,EAAU,IAAM,IAAM,IACxEF,EAAWjE,EAAMnC,KAAK6C,QAAQ2C,EAAM/D,IACpCU,EAAMnC,KAAKG,QAAQiG,EAAUjE,EAAMnC,KAAKI,gBAAgBJ,IACxDqG,GAAWP,GACP9F,KAAMoG,EACNrB,UAAWA,EAAUkB,KAEzBI,GAAW,SAGX8H,EAAY,GAAIjO,MAAK0F,GAAQwI,EAAiBvK,EAAKvD,QAAQ8N,eAAgBC,EAAkBxK,EAAKvD,QAAQgO,UAAYvL,EAAewL,EAAaF,EAAkBD,GAAkB,CAC1L,KAASpI,EAAS,EAAGA,EAASL,EAAUK,IAAU,CAO9C,IANAE,GAAQ,OACJF,GAAUL,EAAW9B,EAAK2K,OAAOrN,UAAY,IAC7CuG,EAAY1B,GAAUL,EAAW9B,EAAK2K,OAAOrN,QAC7CgN,EAAY,GAAIjO,MAAK2D,EAAK2K,OAAO9G,IACjCvF,EAAMnC,KAAKG,QAAQgO,EAAWhM,EAAMnC,KAAKI,gBAAgBwF,KAEpDnE,EAAM,EAAGN,EAASsE,EAAahE,EAAMN,EAAQM,IAElD,GADAyE,GAAQrC,EAAK+C,cAAcuH,EAAWtI,EAAKM,EAAWJ,EAAmBL,GACrEK,EAAmB,CACnB5F,EAAQgO,EAAWI,GAAY,EAC/B,OAGRrI,GAAQ,QAEZ,MAAOA,IAEXW,yBAA0B,SAAUC,EAAYC,EAAYC,EAAWC,EAAUC,GAAvD,GAMbQ,GACDP,EACAC,EAIAC,EACAC,EACAC,EACAC,EACAC,EACAE,EAEKC,EAQIzD,EACDC,EA3BZP,EAAOC,KAAKC,MACZ4B,EAAWqB,EAAU7F,MAIzB,KAHI+F,IACAvB,GAAsBoB,GAEjBW,EAAY,EAAGA,EAAYX,EAAYW,IAa5C,IAZIP,EAAgB,EAEhBD,IACAC,EAAgBO,GAEhBL,EAAWF,EAAgBxB,EAC3B2B,EAAiB,EACjBC,EAAQP,EAAUK,GAAUS,SAC5BN,EAAgBN,EAAsBvB,EAAW4B,EAAMpG,QAAU4F,EAAaD,GAC9EW,EAAcF,EAAMpG,OAAS4F,EAEjCK,EAAOhH,EAAgB,GAAIF,QAAM2D,EAAKkE,eAC7BH,EAAY,EAAGA,EAAYJ,EAAeI,IAAa,CAQ5D,IAPKV,GAIDS,EAAa,EACbJ,EAAQP,EAAUY,EAAYJ,EAAgBE,GAAWI,WAJzDH,EAAaD,EAAYD,EAAcX,EAAac,EACpDN,KAKKnD,EAAa,EAAGA,EAAa2C,EAAY3C,IAC1CC,EAAQP,EAAKQ,OAAOF,GACxBN,EAAKmE,yBAAyB5D,EAAOmD,EAAOpD,EAAYwD,EAAYD,EAAWN,EAAMH,EAEzFG,IAAQH,IAIpBgB,uBAAwB,WACpB,GAAIpE,GAAOC,KAAKC,KAChB,OAAOF,GAAKwC,QAAQ5B,KAAK,MAAMtD,QAEnC+G,qBAAsB,SAAUC,EAAahE,EAAYiE,GACrD,MAAOA,IAEXE,aAAc,SAAUC,EAAYvG,EAAOwG,EAAepE,EAAOqE,EAAOC,EAAW+F,EAAYC,GAAjF,GAEN9F,GAEK5H,EAMD2N,EACAC,EACAjG,EAXJ9E,EAAOC,KAAKC,MAEZ8K,IACJ,KAAS7N,EAAIyH,EAAM7C,MAAMkJ,MAAO9N,GAAKyH,EAAM5C,IAAIiJ,MAAO9N,IAClD4H,EAAU/E,EAAKgF,oBAAoBL,EAAcM,WAAY9G,EAAOhB,IAAM0N,EAAU1N,IAAMyN,GAC1F7F,EAAQK,SAASP,GAAWQ,KACxBC,IAAK,EACLC,OAAQvF,EAAKvD,QAAQ+I,cAErBsF,EAAcvK,EAAM2K,qBAAqB,GAAGC,OAAOhO,GACnD4N,EAAYxK,EAAM6K,eAAeN,EAAY/I,MAAO+I,EAAY9I,KAAK,GAAO,GAC5E8C,GACA/C,MAAO5E,IAAMyN,EAAajG,EAAcM,WAAWQ,YAAcd,EAAcM,WAAWlD,MAAQ+I,EAAY/I,MAC9GC,IAAK7E,IAAM0N,EAAWlG,EAAcM,WAAWS,UAAYf,EAAcM,WAAWjD,IAAM8I,EAAY9I,IACtG+C,QAASA,EACTY,IAAKxH,EAAMwH,IACXC,UAAWmF,EACXvH,SAAU,EACVqC,UAAW,GAEfnB,EAAWtG,OAAOD,EAAMwH,KAAOb,EAC/BkG,EAAahO,KAAK8H,GAClB9E,EAAK+F,mBAAmBxF,EAAOwK,EAAWhG,EAAS5G,EAAM6H,UACzDhG,EAAKiG,aAAanB,EAAaiG,EAAWrG,EAE9CA,GAAWtG,OAAOD,EAAMwH,KAAOqF,GAEnC9E,uBAAwB,SAAUC,GAC9B,GAAInG,GAAOC,KAAKC,KAChB,OAAOF,GAAKsG,qBAAqBH,IAErCE,yBAA0B,SAAUF,EAAOjJ,GACvC,GAAI8C,GAAOC,KAAKC,KAChB,OAAOF,GAAKsG,qBAAqBpJ,GAAe8C,EAAKsG,qBAAqB,IAE9EC,iCAAkC,SAAUC,EAAQC,GAAlB,GAC1BzG,GAAOC,KAAKC,MACZmL,EAAiBrL,EAAK6J,MAAMjJ,KAAK,qBACjC0K,EAAgBtL,EAAK6J,MAAMjJ,KAAK,oBAChC8F,EAAc,eAAkBvH,EAA4B,WAC5D4H,EAAmB/K,EAAE0K,GAAaM,UAAUhH,EAAK6J,OACjD0B,EAAoBzO,KAAK8J,MAAMJ,EAAO,GAAGK,UAAUJ,EAAa,GAAIpK,MAAKoK,EAAYK,UAAY,IAAI,GAAOxB,KAC5GkG,IACAvL,MAAKwJ,QACL+B,EAAoB7N,MAAQ0N,EAAejE,WAAW1J,KAAOkB,EAAYyM,GAAkBzM,EAAY0M,GACvGvE,EAAiBE,SAAS7H,EAAkC,WAE5DoM,EAAoB9N,KAAO4N,EAAclE,WAAW1J,KACpDqJ,EAAiBE,SAAS7H,EAAkC,WAEhEoM,EAAoBlG,IAAMiG,EAAoB7M,EAAWqI,GAAoBxH,EAAoB,EACjGwH,EAAiB1B,IAAImG,GACrBxP,EAAE0K,GAAaM,UAAUhH,EAAKwC,SAAS6C,KACnCC,IAAKiG,EACLhG,OAAQ,MACR5H,MAAO,MACP0J,MAAOrH,EAAKwC,QAAQ,GAAGkH,YACvBhM,KAAM,KAGd6J,aAAc,SAAUU,EAAWwD,EAAUhE,GACzC,GAAIzH,GAAOC,KAAKC,KACXuH,KACDQ,EAAU3H,WAAamL,EAAWzL,EAAKQ,OAAOlD,OAAS,EAAI,IAGnEkK,eAAgB,SAAUC,GACtB,MAAOA,IAEXK,eAAgB,SAAUL,GACtB,MAAOA,IAEXiE,YAAa,SAAUzD,EAAWC,EAAST,GAA9B,GAGLkE,GAAaV,EAFbjL,EAAOC,KAAKC,MACZK,EAAQP,EAAKQ,OAAOyH,EAAU3H,WAElC,IAAI4H,GAGA,GAFAyD,EAAcpL,EAAMqL,iBAAgB,GACpCX,EAAQxD,EAAKwD,MAAQ,EACjBA,GAAS,EACT,MAAOU,GAAY,GAAGR,OAAOF,OAKjC,IAFAU,EAAcpL,EAAMqL,iBAAgB,GACpCX,EAAQxD,EAAKwD,MAAQ,EACjBU,EAAY,IAAMA,EAAY,GAAGR,OAAOF,GACxC,MAAOU,GAAY,GAAGR,OAAOF,IAIzCjD,eAAgB,SAAUC,EAAWC,EAAST,GAC1C,MAAOxH,MAAKyL,YAAYzD,EAAWC,EAAST,IAEhDU,gBAAiB,SAAUD,EAASO,GAChC,MAAIA,GACOP,EAAU,SAAW,WAErBA,EAAU,WAAa,aAGtCE,4BAA6B,SAAUH,EAAWzB,EAAQ0B,EAASO,GAC/D,GAAIzI,GAAOC,KAAKC,KAChB,OAAKuI,GAGExM,EAFI+D,EAAKoI,4BAA4BH,EAAWzB,EAAQ0B,IAInEG,iBAAkB,SAAUJ,EAAW1H,EAAO+H,EAAQC,EAAWC,EAASC,EAAUP,GAAlE,GACVlI,GAAOC,KAAKC,MACZ2L,EAAW7L,EAAKuH,aAAaU,EAAWC,GACxCQ,IASJ,OARKmD,GAMDnD,EAAOH,UAAYG,EAAOF,QAAUqD,EAL/B7L,EAAKwE,yBACNkE,EAAOH,UAAYhI,EAAM+H,GAAQC,GACjCG,EAAOF,QAAUjI,EAAM+H,GAAQE,IAKhCE,GAEXE,0BAA2B,SAAUE,EAAOC,EAAOd,EAAWC,GAC1D,GAAIlI,GAAOC,KAAKC,KAChB,SAAM4I,EAAMP,WAAcO,EAAMN,SAAaO,IAAS/I,EAAKgJ,kBAAkBf,EAAWC,EAASlI,EAAKwE,0BAK1GqE,4BAA6B,SAAUC,EAAOC,EAAOd,EAAWC,GAC5D,GAAIlI,GAAOC,KAAKC,KAChB,QAAIF,EAAKwE,0BAGHsE,EAAMP,WAAcO,EAAMN,SAAaO,IAAS/I,EAAKgJ,kBAAkBf,EAAWC,GAAS,KAKrGe,iBAAkB,SAAUhB,EAAWzB,EAAQuC,EAAOb,GAClD,GAAIlI,GAAOC,KAAKC,KAChBF,GAAKiJ,iBAAiBhB,EAAWzB,EAAQuC,EAAOb,GAAUlI,EAAKwE,yBAEnE0E,gBAAiB,SAAUtE,EAAOD,GAAjB,GAIJmH,GACDrE,EACA2B,EAEA/D,EAPJrF,EAAOC,KAAKC,MACZqI,EAAY3D,EAAM7C,MAClBgK,EAAWnH,EAAM5C,GACrB,KAAS8J,EAAUvD,EAAU0C,MAAOa,GAAWC,EAASd,MAAOa,IACvDrE,EAAO7C,EAAM+C,WAAWwD,OAAOW,GAC/B1C,EAAOpJ,EAAKgF,oBAAoBL,EAAcM,WAAYN,EAAcM,YAAY,GAAO,GAC/FmE,EAAKnC,SAAS,qBACV5B,GACA3H,KAAM+J,EAAKuE,WAAa,EACxB1G,IAAKmC,EAAK5B,UACVN,OAAQvF,EAAKvD,QAAQ+I,YACrB6B,MAAOI,EAAKwE,aAEhB7C,EAAK/D,IAAIA,GACLV,EAAcM,WAAWsE,cACzBH,EAAKnC,SAAS3H,GAElBU,EAAKwJ,gBAAgBJ,IAG7BlC,oBAAqB,SAAUxJ,GAC3B,GAAIsC,GAAOC,KAAKC,KAIhB,OAHIF,GAAKyJ,SAAWzJ,EAAKwE,yBACrB9G,GAAQsC,EAAKwC,QAAQ,GAAGkH,YAAc1J,EAAKwC,QAAQ,GAAGyJ,aAEnDvO,IAGfY,GAAME,GAAG0N,UAAUrM,oBAAsBA,EACzCvB,EAAME,GAAG0N,UAAUtC,0BAA4BA,EAC3CvL,EAAeI,EAAcK,QAC7BiB,KAAM,SAAUgF,EAAStI,GACrB,GAAI0P,GAAOlM,IACXxB,GAAc2N,GAAGrM,KAAKsM,KAAKF,EAAMpH,EAAStI,GAC1C0P,EAAKG,aAAeH,EAAKI,kBACzBJ,EAAKK,MAAQL,EAAK1P,QAAQ+P,OAASL,EAAK1P,QAAQgQ,KAChDN,EAAKO,UAAYlQ,EAAY2P,EAAK1P,SAClC0P,EAAKQ,aACLR,EAAKS,YACLT,EAAKU,qBACLV,EAAKW,UACLX,EAAKY,cAAa,IAEtBN,KAAM,WACNF,gBAAiB,WACb,MAAItM,MAAK+M,mBACE,GAAI1O,GAAME,GAAG0N,UAAUtC,0BAA0B3J,MAEjD,GAAI3B,GAAME,GAAG0N,UAAUrM,oBAAoBI,OAG1DgN,yBAA0B,SAAUxF,EAAMyF,EAAc5M,GAA9B,GAIlB6M,GACKhQ,CAJT,IAAI8C,KAAKuE,uBACL,MAAOlG,GAAME,GAAGC,cAAc2N,GAAGa,yBAAyBZ,KAAKpM,KAAMwH,EAAMyF,EAAc5M,EAG7F,KADI6M,EAAY,EACPhQ,EAAI,EAAGA,EAAI+P,EAAa5P,OAAQH,IACrC,GAAIsK,EAAK2F,YAAcF,EAAa/P,GAAG4E,MAAMqL,YACzCD,QADJ,CAIA,KAAI1F,EAAK2F,YAAYtG,YAAcoG,EAAa/P,GAAG4E,MAAMqL,YAAYtG,WAAaxG,EAAa4M,EAAa/P,GAAG4E,MAAMzB,YAIrH,KAHI6M,KAKR,MAAOA,IAEXE,iBAAkB,SAAU5F,EAAMyF,EAAc/O,EAAOP,EAAK0P,EAAKnG,GAC7D,MAAIlH,MAAKuE,uBACElG,EAAME,GAAGC,cAAc2N,GAAGiB,iBAAiBhB,KAAKpM,KAAMwH,EAAMyF,EAAc/O,EAAOP,EAAK0P,EAAKnG,GAE/FM,GAEX8F,iBAAkB,SAAUC,GACxB,MAAIvN,MAAKuE,uBACElG,EAAME,GAAGC,cAAc2N,GAAGmB,iBAAiBlB,KAAKpM,KAAMuN,GAE1DA,EAAgBC,KAAK,SAAU1F,EAAO2F,GACzC,GAAIhF,GAASX,EAAMhG,MAAMqL,YAAYtG,UAAY4G,EAAO3L,MAAMqL,YAAYtG,SAe1E,OAde,KAAX4B,IACIX,EAAMhG,MAAM4L,YAAcD,EAAO3L,MAAM4L,YACvCjF,OAECX,EAAMhG,MAAM4L,WAAaD,EAAO3L,MAAM4L,YACvCjF,EAAS,IAGF,IAAXA,IACAA,EAASX,EAAMhG,MAAMzB,WAAaoN,EAAO3L,MAAMzB,YAEpC,IAAXoI,IACAA,EAAS1M,EAAE+L,EAAMhD,SAASkG,QAAUjP,EAAE0R,EAAO3I,SAASkG,SAEnDvC,KAGfkF,0BAA2B,WACvB3N,KAAK4N,yBAAyB,GAAIxR,QAEtCwR,yBAA0B,SAAUpH,GAAV,GAYdqH,GAEIC,EAIRlM,EACKvB,EACD0N,EAIAC,EACAzH,EAIAmB,EACAuG,EAKQxH,EACAC,EACAjJ,EACAqJ,EArCZtK,EAAUwD,KAAKxD,OAInB,IAHAwD,KAAKa,YAAYF,KAAK,IAAMzB,GAA2BgP,SACvDlO,KAAK4J,MAAMjJ,KAAK,IAAMzB,GAA2BgP,SACjDlO,KAAKuC,QAAQ5B,KAAK,IAAMzB,GAA2BgP,SAC9ClO,KAAKmO,eACFrM,MAAO0E,EACPzE,IAAKyE,IAYb,IARIhK,EAAQ4R,kBAAkBC,oBAAqB,IAC3CR,EAAWrR,EAAQ8R,WAAW9R,QAAQ+R,OAAOV,SAC7CrR,EAAQ8R,YAAcT,IAClBC,EAAiBzP,EAAMwP,SAASW,OAAOhI,EAAaqH,GACxDrH,EAAcnI,EAAMwP,SAASY,QAAQjI,EAAaA,EAAYkI,oBAAqBZ,KAGvFlM,EAAepF,EAAQ8D,OAAsC,YAA7B9D,EAAQ8D,MAAMqO,YAAgC3O,KAAKO,OAAOlD,OAAhB,EACrEgD,EAAa,EAAGA,EAAauB,EAAavB,IAAc,CAE7D,GADI0N,EAAe/N,KAAKO,OAAOF,IAC1B0N,EACD,MAIJ,IAFIC,EAAiB3P,EAAMnC,KAAK0S,UAAUpI,GACtCD,EAASwH,EAAa5C,eAAe6C,EAAgBA,EAAiB,GACpD,IAAlBzH,EAAOlJ,OACP,MAEAqK,GAAanB,EAAO,GAAGmB,WACvBuG,EAAcvG,EAAWmH,gBAAgBrI,GACzCyH,IACIjO,KAAKuE,uBACLvE,KAAKqM,aAAa/F,iCAAiCC,EAAQC,IAEvDC,EAAc,eAAkBvH,EAA4B,WAC5DwH,EAAa1G,KAAKa,YAAYF,KAAK,IAAMvB,GACzC3B,EAAOZ,KAAK8J,MAAMJ,EAAO,GAAGK,UAAUJ,EAAa,GAAIpK,MAAKoK,EAAYK,UAAY,IAAI,GAAOpJ,MAC/FqJ,EAAmB/K,EAAE0K,GAAaM,UAAUL,GAAYM,SAAS7H,EAAkC,SACvG2H,EAAiB1B,KACb3H,KAAMuC,KAAKiH,oBAAoBxJ,EAAOgB,EAAWqI,GAAoBxH,EAAoB,GACzF+F,IAAKqB,EAAW/F,KAAK,WAAWuG,OAAOC,WAAW9B,MAEtDtJ,EAAE0K,GAAaM,UAAU/G,KAAKuC,SAAS6C,KACnC3H,KAAMuC,KAAKiH,oBAAoBxJ,GAC/B2J,MAAO,MACP9B,OAAQtF,KAAKuC,QAAQ,GAAG8E,aAAe,EACvChC,IAAK,QAMzB4B,oBAAqB,SAAUxJ,GAC3B,MAAOuC,MAAKqM,aAAapF,oBAAoBxJ,IAEjDqP,aAAc,SAAUgC,GAAV,GACN5C,GAAOlM,KACP+O,EAAgB7C,EAAK1P,QAAQ4R,iBAC7BW,MAAkB,GAASA,EAAcC,iBAAmBhT,IAC5DkQ,EAAKyB,4BACDmB,IACA5C,EAAK+C,wBAA0BC,YAAYpQ,EAAMkB,KAAK2N,0BAA2BzB,GAAO6C,EAAcC,mBAIlHrC,UAAW,WACH3M,KAAKxD,QAAQ2S,WACTnP,KAAKoP,YACLpP,KAAKqP,iBAELrP,KAAKsP,mBAIjBA,eAAgB,WACZ,GAAIpD,GAAOlM,IACXkM,GAAKpH,QAAQyK,GAAG,QAAUhQ,EAAI,6BAA8B,SAAUiQ,GAClEtD,EAAKuD,QAAQ,UAAY/J,IAAK3J,EAAEiE,MAAM0P,QAAQ,YAAYC,KAAKtR,EAAMsR,KAAK,UAC1EH,EAAEI,mBAEF1D,EAAK1P,QAAQ2S,SAASU,UAAW,GACjC3D,EAAKpH,QAAQyK,GAAG,WAAahQ,EAAI,0BAA2B,SAAUiQ,GAAV,GAGhDM,GAFJtI,EAAO0E,EAAK6D,gBAAgBP,EAAEQ,MAAOR,EAAES,MACvCzI,KACIsI,EAAe5D,EAAKzJ,gBAAgB+E,GACxC0E,EAAKuD,QAAQ,OACTS,UAAWrR,GACPiD,MAAO0F,EAAK2F,YACZpL,IAAKyF,EAAK2I,WACXL,MAGXN,EAAEI,mBAGN1D,EAAK1P,QAAQ2S,SAASiB,UAAW,GACjClE,EAAKpH,QAAQyK,GAAG,WAAahQ,EAAI,WAAY,SAAUiQ,GACnDtD,EAAKuD,QAAQ,QAAU/J,IAAK3J,EAAEiE,MAAM0P,QAAQ,YAAYC,KAAKtR,EAAMsR,KAAK,UACxEH,EAAEI,oBAIdP,eAAgB,WAAA,GACRnD,GAAOlM,KACPqQ,EAAY,CACZhS,GAAMiS,QAAQC,SAASC,UACvBH,EAAY,GAEZnE,EAAK1P,QAAQ2S,SAASU,UAAW,IACjC3D,EAAKuE,eAAiB,GAAIpS,GAAMqS,WAAWxE,EAAKpH,SAC5CuL,UAAWA,EACXM,eAAgBtS,EAAMiS,QAAQM,QAAQC,KACtCC,OAAQ,0BACRC,IAAK,SAAUvB,GAAV,GAIGrP,GACAC,EACAoH,EAEIsI,CAPJ5D,GAAK8E,aAGL7Q,EAAIqP,EAAErP,EAAE8Q,WAAajV,EAAYwT,EAAErP,EAAE8Q,SAAWzB,EAAErP,EAClDC,EAAIoP,EAAEpP,EAAE6Q,WAAajV,EAAYwT,EAAEpP,EAAE6Q,SAAWzB,EAAEpP,EAClDoH,EAAO0E,EAAK6D,gBAAgB5P,EAAGC,GAC/BoH,IACIsI,EAAe5D,EAAKzJ,gBAAgB+E,GACxC0E,EAAKuD,QAAQ,OACTS,UAAWrR,GACPiD,MAAO0F,EAAK2F,YACZpL,IAAKyF,EAAK2I,WACXL,MAGXN,EAAEI,sBAIV1D,EAAK1P,QAAQ2S,SAASiB,UAAW,IACjClE,EAAKgF,gBAAkB,GAAI7S,GAAMqS,WAAWxE,EAAKpH,SAC7CuL,UAAWA,EACXM,eAAgBtS,EAAMiS,QAAQM,QAAQC,KACtCC,OAAQ,WACRC,IAAK,SAAUvB,GAAV,GAIG2B,GACAC,CAJAlF,GAAK8E,aAGLG,EAAepV,EAAEyT,EAAE6B,QAAQ3B,QAAQ,YACnC0B,EAAerV,EAAEyT,EAAE8B,MAAMC,cACzBH,EAAaI,SAAS,aACtBtF,EAAKuD,QAAQ,UAAY/J,IAAKyL,EAAaxB,KAAKtR,EAAMsR,KAAK,UACnDwB,EAAaK,SAAS,mBAC9BtF,EAAKuD,QAAQ,QAAU/J,IAAKyL,EAAaxB,KAAKtR,EAAMsR,KAAK,UAE7DH,EAAEI,uBAKlBG,gBAAiB,SAAU5P,EAAGC,GAAb,GACToH,GAGAnH,EAIIuQ,EANJrO,EAAUvC,KAAKuC,QACfiM,EAASjM,EAAQiM,QAqBrB,KAnBArO,GAAKqO,EAAO/Q,KACZ2C,GAAKoO,EAAOnJ,IACRrF,KAAKwJ,QACDoH,EAAUvS,EAAMiS,QAAQM,QACxBA,EAAQa,SACRtR,GAAKoC,EAAQ,GAAGkH,YAAclH,EAAQ,GAAGyJ,YACzC7L,GAAKoC,EAAQ,GAAGmP,YACTd,EAAQe,MACfxR,GAAKoC,EAAQmP,aACbvR,GAAKoC,EAAQ,GAAGkH,YAAclH,EAAQ,GAAGyJ,aAClC4E,EAAQgB,SACfzR,GAAKoC,EAAQ,GAAGmP,aAGpBvR,GAAKoC,EAAQ,GAAGmP,WAEpBtR,GAAKmC,EAAQ,GAAGsP,UAChB1R,EAAItD,KAAKiV,KAAK3R,GACdC,EAAIvD,KAAKiV,KAAK1R,GACTC,EAAa,EAAGA,EAAaL,KAAKO,OAAOlD,OAAQgD,IAElD,GADAmH,EAAOxH,KAAKqM,aAAanM,uBAAuBC,EAAGC,EAAGC,GAElD,MAAOmH,EAGf,OAAO,OAEXhL,SACIgQ,KAAM,eACND,MAAO,WACPwF,mBAAoB,QACpBC,wBAAyB,QACzBC,yBAA0B,aAC1B/V,KAAMmC,EAAMnC,KAAKgW,QACjBjO,UAAW5F,EAAMnC,KAAKgW,QACtBC,QAAS9T,EAAMnC,KAAKgW,QACpBE,eAAe,EACf9H,eAAgB,EAChB6E,UAAU,EACVxM,aAAc,GAAIvG,MAAK,KAAM,EAAG,EAAG,EAAG,EAAG,GACzCwG,WAAY,GAAIxG,MAAK,KAAM,EAAG,EAAG,GAAI,EAAG,GACxCO,cAAe,EACfC,YAAa,EACb4N,UAAW,GACXjF,YAAa,GACb8M,cAAe,EACfC,YAAa,IACbnR,oBAAqB,UACrBoR,wBAAyB,+BACzBvQ,aAAc,SACdwQ,cAAehT,EACfiT,mBAAoB/S,EACpBgT,QAAUC,QAAS,WACnBvE,mBACIY,eAAgB,IAChBX,kBAAkB,GAEtBuE,UACIC,eAAgB,aAChBC,YAAa,gBACbC,YAAa,wBAGrB5U,QACI,SACA,MACA,QAEJuO,WAAY,WACR,GAAIlQ,GAAUwD,KAAKxD,QAASwW,EAAWnU,KAAWR,EAAM4U,SAAUzW,EAAQ0W,iBAC1ElT,MAAKwS,cAAgBxS,KAAKmT,WAAW3W,EAAQgW,cAAe7S,GAC5DK,KAAKuS,wBAA0BlU,EAAMoB,SAASjD,EAAQ+V,wBAAyBS,GAC/EhT,KAAKyS,mBAAqBpU,EAAMoB,SAASjD,EAAQiW,mBAAoBO,GACrEhT,KAAKgC,aAAe3D,EAAMoB,SAASjD,EAAQwF,aAAcgR,GACzDhT,KAAKmB,oBAAsB9C,EAAMoB,SAASjD,EAAQ2E,oBAAqB6R,IAE3EI,QAAS,SAAU1R,GACf,GAAIwK,GAAOlM,IACX0B,GAAQA,MACRwK,EAAKxB,OAAShJ,EACdwK,EAAKmH,WAAa3R,EAAM,GACxBwK,EAAKoH,SAAW5R,EAAMA,EAAMrE,OAAS,GAAK,GAC1C6O,EAAKqH,uBACLrH,EAAKsH,aAAatH,EAAKuH,QAAQ/R,IAC/BwK,EAAKwH,SAAShS,GACdwK,EAAKyH,UACLzH,EAAK0H,mBACL1H,EAAK2H,gBACL3H,EAAKrL,YAAY0O,GAAG,QAAUhQ,EAAI,aAAc,SAAUiQ,GAAV,GACxCsE,GAAK/X,EAAEyT,EAAEuE,eAAerE,QAAQ,MAChClI,EAAO0E,EAAK6D,gBAAgB+D,EAAGtF,SAAS/Q,KAAMyO,EAAK3J,QAAQiM,SAASnJ,IACxE6G,GAAKuD,QAAQ,YACT1P,KAAM,WACN7D,KAAMsL,EAAK2F,gBAGnBjB,EAAKG,aAAa5L,gBAEtBmT,iBAAkB,WAAA,GACVrR,GAAUvC,KAAKuC,QACfyR,EAAezR,EAAQ6E,QACvB6M,EAAejU,KAAKuC,QAAQ5B,KAAK,SACjCgB,EAAcsS,EAAatT,KAAK,YAAYqD,WAAW3G,OACvD6W,EAAW,IACXC,EAAkBxS,EAAc3B,KAAKxD,QAAQ8V,WAC7C0B,GAAeG,IACfD,EAAWrX,KAAKiV,KAAKqC,EAAkBH,EAAe,MAE1DC,EAAaG,IAAIpU,KAAKa,YAAYF,KAAK,UAAUyE,IAAI,QAAS8O,EAAW,MAE7EX,qBAAsB,WAAA,GAWdc,GACKnX,EACDoX,EAEAC,EAdJ7S,EAAQ1B,KAAK0K,OACb8J,EAAgBxU,KAAKiE,YACrBwQ,EAAczU,KAAKmS,SASvB,KARAsC,EAAcnY,EAAgBmY,GAC9BD,EAAgBlY,EAAgBkY,GAC5BC,IAAgBD,EAChBC,GAAezV,EAAa,EACrByV,EAAcD,IACrBC,GAAezV,GAEfqV,KACKnX,EAAI,EAAGA,EAAIwE,EAAMrE,OAAQH,IAC1BoX,EAAavV,EAAQ2C,EAAMxE,IAC/Bb,EAAQiY,EAAYE,GAChBD,EAAWxV,EAAQ2C,EAAMxE,IAC7Bb,EAAQkY,EAAUE,GAClBJ,EAAWtX,MACP+E,MAAOzD,EAAMnC,KAAK0S,UAAU0F,GAC5BvS,IAAK1D,EAAMnC,KAAK0S,UAAU2F,IAGlCvU,MAAK0U,YAAcL,GAEvBvR,cAAe,SAAU6R,EAAKC,EAAKC,EAAQC,EAAgBlT,GAA5C,GAGPsK,GAAa6I,EAA8BC,EAA8B1K,EAA8CC,EAA0DE,EAAoD3I,EAAwBnE,EAASN,EAAQ+E,EAa1Q6S,EACAjL,EACAC,EACAC,EACAC,EAEI+K,CAPZ,KAdAP,EAAM1Y,EAAgB0Y,GACtBC,EAAM3Y,EAAgB2Y,GAClB1I,EAAOlM,KAAM+U,EAAQzY,EAAgBqY,GAAMK,EAAQ1Y,EAAgBsY,GAAMtK,EAAiB4B,EAAK1P,QAAQ8N,eAAgBC,EAAkB2B,EAAK1P,QAAQgO,UAAYvL,EAAewL,EAAaF,EAAkBD,GAAkB,EAAGxI,EAAQ,GAAI1F,QAAMuY,IAAMhX,EAAM,EAAWyE,EAAO,GACzR/E,EAAS2B,EAAayL,EAClBsK,GAASC,IACLD,EAAQC,IACRA,GAAShW,GAEb3B,GAAU2X,EAAQD,GAAStK,GAE/BpN,EAASyX,EAAiB,EAAIjY,KAAK8J,MAAMtJ,GACrCuE,IACAvE,GAAkBuE,GAEfjE,EAAMN,EAAQM,IACbsX,EAAmBtX,GAAO4M,EAAkBE,GAC5CT,EAAyC,IAArBiL,EACpBhL,EAAiBgL,EAAmB3K,EAAiB,EACrDJ,EAAmB+K,IAAqB3K,EAAiB,EACzDH,EAAmBG,EACnBjN,EAASiN,IAAmB,IACxB4K,EAAkB7X,GAAUM,EAAM,GAAK2M,EACvCN,GAAqBkL,IACrB/K,EAAmB9M,EAASiN,IAGpClI,GAAQyS,EAAO/S,EAAOkI,EAAmBC,EAAgBC,EAAkBC,EAAkBxM,EAAMiE,GAC9FkT,IACGlT,EACIjE,EAAMiE,IAAgBA,EAAc,GACpCvF,EAAQyF,EAAO2I,GAAY,GAG/BpO,EAAQyF,EAAO2I,GAAY,GAIvC,OAAOrI,IAEXqR,QAAS,SAAU/R,GAAV,GAOIyT,GAmBAxX,EAOLsD,EAhCAmU,KACAhY,KACA8O,EAAOlM,KACPkB,IAAUmU,KAAMnJ,EAAK1P,QAAQoW,SAASC,iBACtCyC,EAAcpJ,EAAKG,aACnBkJ,IACJ,KAASJ,EAAiB,EAAGA,EAAiBjJ,EAAK1P,QAAQ8N,eAAgB6K,IACvEI,EAAexY,MACXsY,KAAM,UACNG,UAAW,SACXC,YAAY,GAepB,KAZAzV,KAAK8C,cAAcoJ,EAAKjI,YAAaiI,EAAKiG,UAAW,SAAUjW,EAAMsO,EAAWkL,EAAcC,EAAgBC,GAAzD,GAGzC7U,GAFJtB,EAAWyM,EAAKqG,uBAChB/H,KACIzJ,GACAsU,KAAM5V,GAAWvD,KAAMA,IACvBsZ,UAAWG,EAAiB,cAAgB,GAC5CvY,QAASmY,EAAeM,MAAM,EAAGD,IAErCN,EAAYxU,YAAYC,GACxBqU,EAAYrY,KAAKgE,MAGhBpD,EAAM,EAAGA,EAAM+D,EAAMrE,OAAQM,IAClCP,EAAQL,MACJsY,KAAMnJ,EAAKuG,oBAAqBvW,KAAMwF,EAAM/D,KAC5C6X,UAAW,cACXpY,QAASgY,EAAYS,MAAM,IAYnC,OATI5U,GAAYjB,KAAK8V,iBACjB7U,EAAU5D,SACuB,aAA7B2C,KAAK+V,qBACL7U,EAAOoU,EAAYtU,kBAAkBC,EAAW,KAAMjB,KAAKmB,oBAAqB/D,GAChFA,EAAUkY,EAAYlU,6BAA6BH,EAAW,KAAMjB,KAAKmB,oBAAqB/D,IAE9FA,EAAUkY,EAAYjU,qBAAqBJ,EAAW7D,EAAS4C,KAAKmB,oBAAqB/D,KAI7FA,QAASA,EACT8D,KAAMA,IAGdyS,QAAS,WAAA,GAGGvR,GACAuQ,EAoBAzG,EAvBJ1P,EAAUwD,KAAKxD,OACfA,GAAQkW,UAAW,IACftQ,EAAO,4CACPuQ,EAAUnW,EAAQkW,OAAOC,QACzB3S,KAAKoP,cACLhN,GAAQ,8EACRA,GAAQ5F,EAAQoW,SAASV,MAAQ,eAEjCS,GAAuB,YAAZA,EACP3S,KAAKoP,aACLhN,GAAQ,gFACRA,IAAS5F,EAAQ4V,cAAgB5V,EAAQoW,SAASE,YAActW,EAAQoW,SAASG,aAAe,gBAEhG3Q,GAAQ,gCACRA,GAAQ,oHACRA,IAAS5F,EAAQ4V,cAAgB5V,EAAQoW,SAASE,YAActW,EAAQoW,SAASG,aAAe,YAChG3Q,GAAQ,SAGZA,GAAQ,SAEZA,GAAQ,SACRpC,KAAK0S,OAAS3W,EAAEqG,GAAM+C,SAASnF,KAAK8E,SAChCoH,EAAOlM,KACXA,KAAK0S,OAAOnD,GAAG,QAAUhQ,EAAI,uBAAwB,SAAUiQ,GAC3DA,EAAEI,iBACF1D,EAAKuD,QAAQ,YACT1P,KAAMmM,EAAKM,MAAQhQ,EAAQgQ,KAC3BtQ,KAAMgQ,EAAKiB,YACX6I,WAAYxZ,EAAQ4V,kBAG5BpS,KAAK0S,OAAOnD,GAAG,QAAUhQ,EAAI,qBAAsB,SAAUiQ,GAAV,GAE3C3B,GACAgH,EACAoB,EACA/Z,EAEI4R,CANR0B,GAAEI,iBACE/B,EAAW3B,EAAK1P,QAAQqR,SACxBgH,EAAS,QACToB,EAAc,GAAI7Z,MAElByR,GACIC,EAAiBzP,EAAMwP,SAASW,OAAOyH,EAAapI,GACxD3R,EAAOmC,EAAMwP,SAASY,QAAQwH,EAAaA,EAAYvH,oBAAqBZ,IAE5E5R,EAAO+Z,EAEX/J,EAAKuD,QAAQ,YACT1P,KAAMmM,EAAKM,MAAQhQ,EAAQgQ,KAC3BqI,OAAQA,EACR3Y,KAAMA,QAKtBmK,qBAAsB,SAAUH,GAC5B,GAAIjJ,GAAc+C,KAAKkW,aAAahQ,EACpC,OAAOjJ,GAAcA,EAAYI,OAAS,GAE9C8I,kBAAmB,SAAUD,GACzB,GAAIiQ,GAAWnW,KAAKoW,UAAUlQ,EAC9B,OAAOiQ,GAAWA,EAAS9Y,OAAS,GAExCwF,WAAY,SAAU3G,GAAV,GAGCgB,GAFLmZ,EAAMna,EAAKoa,SACX7Z,EAAWuD,KAAKyM,SACpB,KAASvP,EAAI,EAAGA,EAAIT,EAASY,OAAQH,IACjC,GAAIT,EAASS,KAAOmZ,EAChB,OAAO,CAGf,QAAO,GAEX3C,SAAU,SAAUhS,GAAV,GACFwK,GAAOlM,KACP8B,EAAQoK,EAAKjI,YACblC,EAAM/B,KAAKmS,UACXvQ,EAAc,EACdC,EAAW,EACXF,EAAcD,EAAMrE,OACpB+E,EAAO,GACPnB,EAAYjB,KAAK8V,iBACjB9T,EAAehC,KAAKgC,aACpBC,GAAoB,CACpBhB,GAAU5D,SACV4E,EAAiD,aAA7BiK,EAAK6J,oBACrB9T,GACAJ,EAAWqK,EAAKG,aAAa/K,aAAatB,KAAKoW,UAAU/Y,OAAS,GAClEuE,EAAcsK,EAAKG,aAAa7K,mBAEhCI,EAAcsK,EAAK3K,eAG3Ba,GAAQ,UACRA,GAAQ8J,EAAKG,aAAa5K,YAAYC,EAAOC,EAAaC,EAAaC,EAAUC,EAAOC,EAAKC,EAAcC,GAC3GG,GAAQ,WACRpC,KAAKuC,QAAQ5B,KAAK,SAAS4V,OAAOnU,IAEtCyK,QAAS,WAAA,GAKIlP,GACDoC,EACA+B,EACAC,EACAkC,EACAkO,EATJnP,EAAahD,KAAKuB,cAClBG,EAAQ1B,KAAK0K,OACb/I,EAAcD,EAAMrE,MAExB,KADA2C,KAAKO,UACI5C,EAAM,EAAGA,EAAMqF,EAAYrF,IAC5BoC,EAAOC,KAAKwW,iBAAiB7Y,GAC7BmE,EAAQJ,EAAM,GACdK,EAAML,EAAMA,EAAMrE,OAAS,GAAK,GAChC4G,EAAY3H,EAAgB0D,KAAKiE,aACjCkO,EAAU7V,EAAgB0D,KAAKmS,WACjB,IAAdlO,GAAmBkO,GAAWlO,IAC9BnC,EAAQ/C,EAAQ+C,GAChBzF,EAAQyF,EAAOmC,GACflC,EAAMhD,EAAQgD,GACd1F,EAAQ0F,EAAKoQ,IAEjBpS,EAAK0W,sBAAsB3U,EAAOzD,EAAMnC,KAAKwa,QAAQ3U,EAAK,GAE9D/B,MAAK2W,gBAAgB3T,EAAYrB,IAErC+G,uBAAwB,WACpB,MAAO1I,MAAK8V,iBAAiBzY,QAAuC,eAA7B2C,KAAK+V,qBAEhDY,gBAAiB,SAAU3T,EAAYC,GAAtB,GACTE,GAAWnD,KAAK4W,oBAChBxT,EAAsBpD,KAAKuE,uBAC3BrB,EAAYlD,KAAKuC,QAAQ5B,KAAK,KAClCuC,GAAUyM,KAAK,OAAQ,OACvB3P,KAAKqM,aAAatJ,yBAAyBC,EAAYC,EAAYC,EAAWC,EAAUC,IAE5Fc,yBAA0B,SAAU5D,EAAOmD,EAAOK,EAAWD,EAAYD,EAAWN,EAAMH,GAAhE,GAClB0T,GAAOpT,EAAMK,EAAYD,GACzB6D,EAAapH,EAAMwW,sBAAsB,GACzCb,EAAcjW,KAAK0K,OAAO9G,GAC1B4C,EAAcpK,KAAK2a,IAAId,EAAYe,cAAef,EAAYgB,WAAYhB,EAAYlX,WACtF+C,EAAQ0E,EAAclD,EACtBvB,EAAMD,EAAQqB,CAClB0T,GAAKK,aAAa,OAAQ,YAC1BL,EAAKK,aAAa,iBAAiB,GACnCxP,EAAWyP,YAAYN,EAAM/U,EAAOC,GAAK,IAE7CoL,UAAW,WACP,MAAOnN,MAAKqT,YAEhBlD,QAAS,WACL,MAAOnQ,MAAKsT,UAEhB8D,eAAgB,WAAA,GACRnT,GAAY3H,EAAgB0D,KAAKiE,aACjCkO,EAAU7V,EAAgB0D,KAAKmS,WAC/BhC,EAAUnQ,KAAKmQ,SAInB,OAHkB,KAAdlM,GAAmBkO,GAAWlO,IAC9BkM,EAAU9R,EAAMnC,KAAKwa,QAAQvG,EAAS,IAEnCA,GAEXlM,UAAW,WACP,GAAIzH,GAAUwD,KAAKxD,OACnB,OAAOA,GAAQ4V,cAAgB5V,EAAQmG,aAAenG,EAAQyH,WAElEkO,QAAS,WACL,GAAI3V,GAAUwD,KAAKxD,OACnB,OAAOA,GAAQ4V,cAAgB5V,EAAQoG,WAAapG,EAAQ2V,SAEhEyE,kBAAmB,WACf,GAAIpa,GAAUwD,KAAKxD,OACnB,OAAOA,GAAQgO,UAAYhO,EAAQ8N,eAAiBrL,GAExDoY,SAAU,WACN,MAAOhZ,GAAMnC,KAAKob,QAAQtX,KAAKmQ,YAEnCoH,aAAc,WACV,MAAOlZ,GAAMnC,KAAKsb,YAAYxX,KAAKmN,cAEvCP,mBAAoB,WAChB5M,KAAKoT,SAASpT,KAAKxD,QAAQN,QAE/Bub,OAAQ,SAAUtZ,GAAV,GAcAuZ,GAEArT,EACAC,EACKjE,EACDoE,CADR,KAjBAzE,KAAK2X,mBAAqB,EAC1B3X,KAAK6M,UACL7M,KAAK8E,QAAQnE,KAAK,YAAYuN,SAC9B/P,EAAS,GAAIE,GAAMuZ,KAAKC,MAAM1Z,GAAQqP,OAE9BsK,MAAO,QACPC,IAAK,QAGLD,MAAO,MACPC,IAAK,UAEVC,UACCN,KACJ1X,KAAKiY,kBAAkB9Z,EAAQ6B,KAAK8V,iBAAkB4B,GAClDrT,KACAC,EAAc,EACTjE,EAAa,EAAGA,EAAaqX,EAAiBra,OAAQgD,IACvDoE,GACApE,WAAYA,EACZiE,YAAa,EACbnG,WAEJkG,EAAYtH,KAAK0H,GACjBzE,KAAKkY,cAAcR,EAAiBrX,GAAaA,EAAYoE,GACzDH,EAAcG,EAAWH,cACzBA,EAAcG,EAAWH,YAGjCtE,MAAKmY,eAAe9T,EAAaqT,EAAiBra,OAAQiH,GAC1DtE,KAAKoY,gBAAgB/T,EAAaqT,EAAiBra,QACnD2C,KAAK8M,cAAa,GAClB9M,KAAKyP,QAAQ,aAEjB2I,gBAAiB,SAAU/T,EAAazC,GAAvB,GACJvB,GACDgY,EACKC,EACDzT,EAES0T,CALrB,KAASlY,EAAa,EAAGA,EAAauB,EAAavB,IAAc,CACzDgY,EAAiBhU,EAAYhE,GAAYlC,MAC7C,KAASma,IAAYD,GAEjB,GADIxT,EAAcwT,EAAeC,GAC7Bvc,EAAEyc,QAAQ3T,GACV,IAAS0T,EAAa,EAAGA,EAAa1T,EAAYxH,OAAQkb,IACtDvY,KAAKyY,eAAe5T,EAAY0T,QAGpCvY,MAAKyY,eAAe5T,KAKpCsT,eAAgB,SAAU9T,EAAazC,EAAa0C,GAApC,GAMHjE,GACDqY,EAEAC,EACAC,EACAC,EAVJtT,EAAcvF,KAAKxD,QAAQ+I,YAAc,EACzCuT,EAAoB9Y,KAAK+Y,sBACzBzD,EAActV,KAAKqM,aACnB2M,EAAqB1D,EAAYnR,uBAAuBvC,EAE5D,KADAA,EAAc5B,KAAKuE,uBAAyByU,EAAqB,EACxD3Y,EAAa,EAAGA,EAAauB,EAAavB,IAC3CqY,EAAYpD,EAAYlR,qBAAqBC,EAAahE,EAAYiE,GAC1EoU,EAAYA,EAAYA,EAAY,EAChCC,GAAapT,EAAc,GAAKmT,EAAYI,EAC5CF,EAAW7c,EAAEiE,KAAK4J,MAAMjJ,KAAK,MAAMN,IACnCwY,EAAM9c,EAAEiE,KAAKuC,QAAQ5B,KAAK,MAAMN,IACpCuY,EAAStT,OAAOqT,GAChBE,EAAIvT,OAAOqT,EAEf3Y,MAAK4T,mBACL5T,KAAK6T,gBACL7T,KAAKiZ,iBAETF,oBAAqB,WAAA,GAGbG,GACAC,EAHAL,EAA+C,GAA3B9Y,KAAKxD,QAAQ+I,YACjC6T,EAAWpZ,KAAKoP,WAepB,OAZIgK,IACAF,EAAY,GACZC,EAAY,KAEZD,EAAY,GACZC,EAAY,IAEZL,EAAoBK,EACpBL,EAAoBK,EACbL,EAAoBI,IAC3BJ,EAAoBI,GAEjBJ,GAEXL,eAAgB,SAAU5T,GAAV,GASJwU,GACAC,EACAC,EAVJhU,EAAcvF,KAAKxD,QAAQ+I,YAAc,EACzC2D,EAAOrE,EAAYc,UAAUiB,UAAU/B,EAAY/C,MAAO+C,EAAY9C,KAAK,GAC3EtE,EAAOuC,KAAKiH,oBAAoBiC,EAAKzL,MACrC2J,EAAQ8B,EAAKxL,MAAQwL,EAAKzL,KAAO,CACjC2J,GAAQ,IACRA,EAAQ,GAERA,EAAQpH,KAAKxD,QAAQ6V,gBACjBgH,EAAkBxU,EAAYc,UAAU+B,WACxC4R,EAAWD,EAAgBnO,OAAOmO,EAAgBnO,OAAO7N,OAAS,GAClEkc,EAAcD,EAASvN,WAAauN,EAAStN,YACjD5E,EAAQpH,KAAKxD,QAAQ6V,cACjBkH,EAAc9b,EAAO2J,IACrBA,EAAQmS,EAAcrQ,EAAKzL,KAAO,IAG1CoH,EAAYC,QAAQM,KAChBC,IAAKR,EAAYc,UAAU7D,MAAM8D,UAAYf,EAAYtB,UAAYgC,EAAc,GAAK,KACxF9H,KAAMA,EACN2J,MAAOA,KAGf6R,cAAe,WACX,IAAK,GAAI5Y,GAAa,EAAGA,EAAaL,KAAKO,OAAOlD,OAAQgD,IACtDL,KAAKO,OAAOF,GAAYmZ,WAGhCvB,kBAAmB,SAAU9Z,EAAQ8C,EAAWwH,GAA7B,GAGP1I,GACK0Z,EACDC,EACAC,EALRC,EAAW3Y,EAAU,EACzB,IAAI2Y,EAEA,IADI7Z,EAAO6Z,EAAStL,WAAWvO,OACtB0Z,EAAU,EAAGA,EAAU1Z,EAAK1C,OAAQoc,IACrCC,EAAQ1Z,KAAK6Z,eAAeD,EAAU7Z,EAAK0Z,IAC3CE,EAA2B,GAAItb,GAAMuZ,KAAKC,MAAM1Z,GAAQ2S,QACxDgH,MAAO8B,EAAS9B,MAChBgC,SAAUtb,EAAcub,cAAcL,KACvC1B,UACC/W,EAAU5D,OAAS,EACnB2C,KAAKiY,kBAAkB0B,EAA0B1Y,EAAU4U,MAAM,GAAIpN,GAErEA,EAAO1L,KAAK4c,OAIpBlR,GAAO1L,KAAKoB,IAGpBgQ,cAAe,SAAUjQ,GAAV,GACP+F,GAAY/F,EAAM4D,MAClBqQ,EAAUjU,EAAM6D,IAChBuS,EAAavV,EAAQiB,KAAKqT,YAC1BkB,EAAWlW,EAAMnC,KAAKwa,QAAQ3X,EAAQiB,KAAKoX,kBAAmB,EAClE,OAAInT,GAAYsQ,GAAYD,GAAcnC,GAK9C6H,cAAe,SAAU9b,GAAV,GAOF+b,GANLhW,EAAY/F,EAAMsH,YAAcnH,EAAMnC,KAAK0S,UAAU1Q,EAAM4D,OAC3DqQ,EAAUjU,EAAMuH,UAAYpH,EAAMnC,KAAK0S,UAAU1Q,EAAM6D,KACvDsS,EAAarU,KAAK0U,WAItB,KAHIzQ,IAAckO,IACdA,GAAoB,GAEf8H,EAAY,EAAGA,EAAY5F,EAAWhX,OAAQ4c,IACnD,GAAIhW,EAAYoQ,EAAW4F,GAAWlY,KAAOsS,EAAW4F,GAAWnY,MAAQqQ,EACvE,OAAO,CAGf,QAAO,GAEX+H,aAAc,SAAUhc,GAAV,GASN8G,GARAlD,EAAQ5D,EAAM4D,MACdC,EAAM7D,EAAM6D,IACZoY,EAAiBjc,EAAMkc,MAAM,SAC7BC,EAAenc,EAAMkc,MAAM,OAC3BnW,EAAY3H,EAAgB0D,KAAKiE,aACjCkO,EAAU7V,EAAgB0D,KAAKmS,WAC/BmI,EAAoB,KACpBC,EAAkB,KAElBtV,GAAO,EACPC,GAAO,CAmDX,OAlDIhH,GAAM6H,WACNjE,EAAQ/C,EAAQ+C,GAChBqY,EAAiB,EACjBpY,EAAMhD,EAAQgD,GACdsY,EAAerb,EACfub,EAAkBlc,EAAMnC,KAAKwa,QAAQ3U,EAAK,IAE9B,IAAZoQ,IACAA,EAAUnT,GAEVmT,GAAWlO,GACPkW,EAAiBlW,GAAakW,GAAkBhI,IAChDmI,EAAoBvb,EAAQ+C,GAC5BzF,EAAQie,EAAmBrW,GAC3BiB,GAAO,GAEPmV,EAAelI,GAAWkI,GAAgBpW,IAC1CsW,EAAkBxb,EAAQgD,GAC1B1F,EAAQke,EAAiBpI,GACzBlN,GAAO,KAGPhB,EAAYkW,GACZG,EAAoBvb,EAAQ+C,GAC5BzF,EAAQie,EAAmBrW,GAC3BiB,GAAO,GACAiN,GAAWgI,IAClBG,EAAoBvb,EAAQ+C,GAC5BwY,EAAoBjc,EAAMnC,KAAKwa,QAAQ4D,EAAmB,GAC1Dje,EAAQie,EAAmBrW,GAC3BiB,GAAO,GAEPiN,EAAUkI,GACVE,EAAkBxb,EAAQgD,GAC1B1F,EAAQke,EAAiBpI,GACzBlN,GAAO,GACAhB,EAAYoW,IACnBE,EAAkBxb,EAAQgD,GAC1BwY,EAAkBlc,EAAMnC,KAAKwa,QAAQ6D,MACrCle,EAAQke,EAAiBpI,GACzBlN,GAAO,IAGfD,EAAa9G,EAAMsc,OACf1Y,MAAOwY,EAAoBA,EAAoBxY,EAC/CC,IAAKwY,EAAkBA,EAAkBxY,EACzCyD,WAAY8U,EAAoBjc,EAAMnC,KAAK0S,UAAU0L,GAAqBpc,EAAMsH,WAChFC,SAAU8U,EAAkBlc,EAAMnC,KAAK0S,UAAU2L,GAAmBrc,EAAMuH,SAC1EM,UAAU,KAGVf,WAAYA,EACZC,KAAMA,EACNC,KAAMA,IAGdgT,cAAe,SAAU/Z,EAAQkC,EAAYoE,GAA9B,GACPvG,GACAP,EACAN,EAIQod,EACA7V,EAEIF,EACApE,EAKIiG,EACA5B,EACAgG,EACAC,CAfpB,KAAKjN,EAAM,EAAGN,EAASc,EAAOd,OAAQM,EAAMN,EAAQM,IAChDO,EAAQC,EAAOR,GACXqC,KAAKmO,cAAcjQ,KACfuc,EAAkBvc,EAAM6H,UAAY7H,EAAMwc,YAAc1b,EACxD4F,EAAY5E,KAAKuC,SACjBkY,GAAmBza,KAAKga,cAAc9b,MAClCwG,EAAgB1E,KAAKka,aAAahc,GAClCoC,EAAQN,KAAKO,OAAOF,GACnBC,EAAMqa,oBACPra,EAAMqa,sBAEN3a,KAAKga,cAActV,EAAcM,cAC7BuB,EAASjG,EAAM+T,WAAW3P,EAAcM,YAAY,GACpDL,EAAQ4B,EAAO,GACfoE,EAAahG,EAAM7C,MAAMkJ,MACzBJ,EAAWjG,EAAM5C,IAAIiJ,MACzBhL,KAAKqM,aAAa7H,aAAaC,EAAYvG,EAAOwG,EAAepE,EAAOqE,EAAOC,EAAW+F,EAAYC,OAM1H9E,mBAAoB,SAAUxF,EAAOqE,EAAOG,EAASiB,GACjD,GAAI5H,GAASmC,EAAMqa,iBACnBxc,GAAOpB,MACH+H,QAASA,EACTiB,SAAUA,EACVL,IAAKZ,EAAQ6K,KAAKtR,EAAMsR,KAAK,QAC7B7N,MAAO6C,EAAM7C,MACbC,IAAK4C,EAAM5C,OAGnBgD,oBAAqB,SAAUC,EAAY9G,EAAO+G,EAAMC,GAAnC,GAmBb0S,GAcA9S,EAhCArF,EAAWO,KAAKwS,cAChBrD,EAAWnP,KAAKxD,QAAQ2S,SACxBiK,EAAWpZ,KAAKoP,YAChBwL,EAAazL,GAAYA,EAAS0L,WAAY,IAAUzB,EACxD0B,EAAY3L,GAAYA,EAAS4L,UAAW,EAC5CZ,EAAiBjc,EAAMkc,MAAM,SAC7BC,EAAenc,EAAMkc,MAAM,OAC3BY,EAAiB9c,EAAM4D,MACvBmZ,EAAe/c,EAAM6D,IACrBd,EAAYjB,KAAKkb,eAAehd,EA8BpC,OA7BIA,GAAMsH,YAAc2U,IAAmB9b,EAAMnC,KAAKI,gBAAgB4B,EAAM4D,SACxEkZ,EAAiB,GAAI5e,MAAK+d,GAC1Ba,EAAiB3c,EAAMwP,SAASsN,MAAMH,EAAgB,YAEtD9c,EAAMuH,UAAY4U,IAAiBhc,EAAMnC,KAAKI,gBAAgB4B,EAAM6D,OACpEkZ,EAAe,GAAI7e,MAAKie,GACxBY,EAAe5c,EAAMwP,SAASsN,MAAMF,EAAc,YAElDrD,EAAO/Y,MACPuc,GAAI/c,EAAM+c,GACVN,UAAWA,EACXF,WAAYA,EACZ3V,KAAMA,EACNC,KAAMA,EACNmW,UAAiC,GAAtBrb,KAAK0K,OAAOrN,OACvB4D,UAAWA,EACXqI,cAAc,EACdsJ,SAAU5S,KAAKxD,QAAQoW,UACxB1U,GACC4D,MAAOkZ,EACPjZ,IAAKkZ,IAELnW,EAAU/I,EAAE0D,EAASmY,IACzB5X,KAAKsb,QAAQ,UAAW,WACpB,OACI9d,SAAUsH,EACV8S,OAAS2D,SAAU3D,OAGpB9S,GAEXkB,aAAc,SAAUnB,EAAac,EAAWlB,GAAlC,GAoBNvD,GAIKvD,EAASN,EACVme,EACKC,EAAOC,EAzBhB/Q,EAAahF,EAAU7D,MAAMkJ,MAC7BJ,EAAWjF,EAAU5D,IAAIiJ,MACzB9B,EAAOrE,EAAYc,UAAUiB,UAAU/B,EAAY/C,MAAO+C,EAAY9C,KAAK,GAC3E/D,EAAYkL,EAAKxL,MAAQsC,KAAKxD,QAAQ6V,cACtClU,EAASZ,EAAgBoI,EAAUxH,SAAU+K,EAAKzL,KAAMO,EAmB5D,KAlBA2H,EAAUgW,UACN1B,UAAWtP,EACX7I,MAAO6I,EACP5I,IAAK6I,EACL7M,SAAUmL,EAAKzL,KACfO,UAAWA,EACX8G,QAASD,EAAYC,QACrBY,IAAKb,EAAYa,MAErBvH,EAAOpB,MACH+E,MAAO6I,EACP5I,IAAK6I,EACLlF,IAAKb,EAAYa,MAEjBxE,EAAO1C,EAAcod,WAAWzd,GAChCsG,EAAWH,YAAcpD,EAAK7D,SAC9BoH,EAAWH,YAAcpD,EAAK7D,QAEzBM,EAAM,EAAGN,EAAS6D,EAAK7D,OAAQM,EAAMN,EAAQM,IAElD,IADI6d,EAAYta,EAAKvD,GAAKQ,OACjBsd,EAAI,EAAGC,EAAcF,EAAUne,OAAQoe,EAAIC,EAAaD,IAC7DhX,EAAWtG,OAAOqd,EAAUC,GAAG/V,KAAKnC,SAAW5F,GAI3D4D,YAAa,WAAA,GACLN,GAAYjB,KAAK8V,iBACjBR,EAActV,KAAKqM,YACvB,OAAIpL,GAAU5D,OACuB,aAA7B2C,KAAK+V,oBACET,EAAYrP,uBAAuBhF,EAAU5D,OAAS,GAEtDiY,EAAYlP,yBAAyBnF,EAAU5D,OAAS,EAAG2C,KAAKkW,aAAa7Y,OAAS,GAG9F,GAEXwe,yBAA0B,SAAU3d,GAChC,GAAIwG,GAAgB1E,KAAKka,aAAahc,EAAMsc,QAC5C,OAAO9V,GAAcM,YAEzB8W,qBAAsB,SAAU5d,GAC5B,MAAIA,GAAM6H,UACGA,UAAU,OAI3BgW,sBAAuB,SAAU7d,GACzBA,EAAM6H,UACN7H,EAAM8d,IAAI,YAAY,IAG9BC,gBAAiB,SAAU/d,EAAOmC,EAAY6b,GAA7B,GAYDxX,GACA6B,EACK4V,EAbb7b,EAAQN,KAAKO,OAAOF,GACpB+b,EAAcle,EAAMsc,OACpB1Y,MAAO5D,EAAM4D,MACbC,IAAK7D,EAAM6D,MAEXsa,EAAeD,EAAY1B,UAI/B,IAHA0B,EAAYta,MAAQ,GAAI1F,MAAKggB,EAAYta,MAAM+E,UAAYqV,GAC3DE,EAAYra,IAAM,GAAI3F,OAAMggB,EAAYta,MAAQua,GAChDrc,KAAKsc,gBAAgBpe,EAAMwH,KACvB1F,KAAKmO,cAAciO,KACfA,EAAYrW,UAAYqW,EAAY1B,YAAc1b,GAAcgB,KAAKga,cAAcoC,IAGnF,IAFI1X,EAAgB1E,KAAKka,aAAakC,GAClC7V,EAASjG,EAAM+T,WAAW3P,EAAcM,YAAY,GAC/CmX,EAAa,EAAGA,EAAa5V,EAAOlJ,OAAQ8e,IACjDnc,KAAKqM,aAAapD,gBAAgB1C,EAAO4V,GAAazX,IAKtE6E,gBAAiB,SAAUJ,GACvBA,EAAKhE,SAASnF,KAAKuC,SACnBvC,KAAKuc,UAAYvc,KAAKuc,UAAUnI,IAAIjL,IAExCqT,kBAAmB,SAAUte,EAAOmC,EAAY4D,EAAWkO,GAAxC,GAINgK,GACDxX,EACA7C,EACA2a,EAEArV,EAESlK,EAOToI,EACA7H,EACA0L,EAGJuT,EACA9X,EAvBAtE,EAAQN,KAAKO,OAAOF,GACpBkG,EAASjG,EAAMiG,OAAOtC,EAAWkO,GAAS,GAAO,EAErD,KADAnS,KAAK2c,oBACIR,EAAa,EAAGA,EAAa5V,EAAOlJ,OAAQ8e,IAAc,CAM/D,GALIxX,EAAQ4B,EAAO4V,GACfra,EAAQ6C,EAAM2D,YACdmU,EAAY9X,EAAMiC,UAAU3C,EAAWkO,GAAS,GACpDsK,EAAUpX,IAAMvD,EAAM8D,UAClBwB,EAAQqV,EAAU/e,MAAQ+e,EAAUhf,KACpC2J,EAAQ,EACR,IAASlK,EAAI,EAAGA,EAAIyH,EAAMxG,SAASd,OAAQH,IACvC,GAAIyH,EAAMxG,SAASjB,GAAGwI,MAAQxH,EAAMwH,IAAK,CACrC0B,EAAQzC,EAAMxG,SAASjB,GAAGc,UAAYye,EAAUhf,IAChD,OAIR6H,EAASX,EAAM4D,UAAU3C,UAAY9D,EAAMuH,aAAeoT,EAAUpX,IACpE5H,EAAOuC,KAAKiH,oBAAoBwV,EAAUhf,MAC1C0L,EAAO3K,EAAc2N,GAAGyQ,kBAAkBxQ,KAAKpM,KAAMvC,EAAMgf,EAAUpX,IAAK+B,EAAO9B,GACrFtF,KAAK6c,YAAc7c,KAAK6c,YAAYzI,IAAIjL,GAExCuT,EAAS,IACT9X,EAAY5E,KAAKuC,QACrBvC,KAAK6c,YAAY1X,SAASP,GAC1B5E,KAAK6c,YAAYlc,KAAK,gCAAgC0U,KAAK,IAC3DrV,KAAK6c,YAAY/U,QAAQd,SAAS,WAAWrG,KAAK,gBAAgB0U,KAAKhX,EAAMye,SAASze,EAAMwP,SAASkP,YAAY9Y,GAAYyY,IAC7H1c,KAAK6c,YAAYjV,OAAOZ,SAAS,UAAUrG,KAAK,mBAAmB0U,KAAKhX,EAAMye,SAASze,EAAMwP,SAASkP,YAAY5K,GAAUuK,KAEhIM,mBAAoB,SAAUnG,GAC1B,GAAIrI,GAASqI,EAAKrI,QAClB,OAAOxO,MAAK+P,gBAAgBvB,EAAO/Q,KAAM+Q,EAAOnJ,MAEpD2D,iBAAkB,SAAUhB,EAAWzB,EAAQiC,EAAUP,EAASgV,GAAhD,GACV3U,GAAY/B,EAAO,GAAGzE,MACtByG,EAAUhC,EAAOA,EAAOlJ,OAAS,GAAG0E,GACpCyG,KAAayU,GACT3U,EAAU0C,QAAUzC,EAAQyC,OAAS1C,EAAU4U,kBAAoB3U,EAAQ2U,kBAC3ElV,EAAUmV,SAAWlV,IAIjCX,aAAc,SAAUU,EAAWwD,GAArB,GACNnD,GAASmD,EAAW,gBAAkB,gBACtChE,EAAOxH,KAAKqI,GAAQL,EAAUlG,MAAOkG,EAAU3H,YAAY,EAK/D,OAJImH,KACAQ,EAAU3H,YAAcmL,KAAgB,GAE5CxL,KAAKqM,aAAa/E,aAAaU,EAAWwD,EAAUhE,GAC7CA,GAEX4V,cAAe,SAAUlhB,EAAMmE,EAAYoH,GAA5B,GACPnH,GAAQN,KAAKO,OAAOF,GACpBmH,EAAOlH,EAAMiG,OAAOrK,EAAMA,EAAMuL,GAAO,GAAO,GAAG3F,KACrD,MAAIzB,GAAc,GAGlB,MAAOL,MAAKqM,aAAa9E,eAAeC,EAAMlH,EAAOmH,IAEzD4V,cAAe,SAAUnhB,EAAMmE,EAAYoH,GAA5B,GACPnH,GAAQN,KAAKO,OAAOF,GACpBmH,EAAOlH,EAAMiG,OAAOrK,EAAMA,EAAMuL,GAAO,GAAO,GAAG3F,KACrD,MAAIzB,GAAcL,KAAKO,OAAOlD,OAAS,GAGvC,MAAO2C,MAAKqM,aAAaxE,eAAeL,EAAMlH,EAAOmH,IAEzDM,eAAgB,SAAUC,EAAWzB,EAAQiC,EAAUP,GAAvC,GACRqN,GAActV,KAAKqM,aACnBhE,EAASiN,EAAYpN,gBAAgBD,EAASO,GAC9CF,EAAY/B,EAAO,GAAGzE,MACtByG,EAAUhC,EAAOA,EAAOlJ,OAAS,GAAG0E,IACpCzB,EAAQN,KAAKO,OAAOyH,EAAU3H,YAC9BmH,EAAO8N,EAAYnN,4BAA4BH,EAAWzB,EAAQ0B,EAASO,EAS/E,OARIhB,KACAc,EAAYC,EAAUf,GAE1Bc,EAAYhI,EAAM+H,GAAQC,GAC1BC,EAAUjI,EAAM+H,GAAQE,GACnBC,IAAYxI,KAAKuE,wBAA4B+D,GAAcC,IAC5DD,EAAYC,EAAU+M,EAAYvN,eAAeC,EAAWC,EAAST,KAGrEc,UAAWA,EACXC,QAASA,IAGjBH,iBAAkB,SAAUJ,EAAWzB,EAAQiC,EAAUP,GAAvC,GACVI,GAASJ,EAAU,SAAW,WAC9BK,EAAY/B,EAAO,GAAGzE,MACtByG,EAAUhC,EAAOA,EAAOlJ,OAAS,GAAG0E,IACpCzB,EAAQN,KAAKO,OAAOyH,EAAU3H,YAC9BoI,IAUJ,OATKD,IAGDC,EAAOH,UAAYhI,EAAM+H,GAAQC,GACjCG,EAAOF,QAAUjI,EAAM+H,GAAQE,GAC1BC,IAAYxI,KAAK0I,0BAA8BJ,GAAcC,IAC9DE,EAAOH,UAAYG,EAAOF,QAAUvI,KAAKsH,aAAaU,EAAWC,KALrEQ,EAASzI,KAAKqM,aAAajE,iBAAiBJ,EAAW1H,EAAO+H,EAAQC,EAAWC,EAASC,EAAUP,GAQjGQ,GAEXM,kBAAmB,SAAUf,EAAWC,GAArB,GAeXyS,GAdAxe,EAAO+L,EAAUjI,KAAKuX,eAAiBvX,KAAKqX,WAC5CvV,EAAQkG,EAAUlG,MAClBC,EAAMiG,EAAUjG,IAChBub,EACO,GAAIlhB,MAAKF,GADNqhB,EAEL,GAAInhB,MAAKF,EAClB,QAAI8D,KAAKwd,WAAWF,EAAUC,KAG9BvV,EAAUlG,MAAQwb,EAClBtV,EAAUjG,IAAMwb,EACZvd,KAAK0I,2BACLV,EAAU3H,WAAa4H,EAAUjI,KAAKO,OAAOlD,OAAS,EAAI,GAE1Dqd,EAAW3Y,EAAMD,EACjBmG,GACAlG,EAAMzF,EAAgB0D,KAAKmS,WAC3BpQ,EAAc,IAARA,EAAY/C,EAAa+C,EAC/B1F,EAAQ2L,EAAUlG,MAAOC,EAAM2Y,GAC/Bre,EAAQ2L,EAAUjG,IAAKA,KAEvBD,EAAQxF,EAAgB0D,KAAKiE;AAC7B5H,EAAQ2L,EAAUlG,MAAOA,GACzBzF,EAAQ2L,EAAUjG,IAAKD,EAAQ4Y,IAEnC1S,EAAU7J,WACH,IAEXsf,KAAM,SAAUzV,EAAW0V,EAAK5U,GAA1B,GAMER,GAAWC,EAASN,EAASY,EAsBrBsU,EA3BRQ,GAAU,EACVrd,EAAQN,KAAKO,OAAOyH,EAAU3H,YAC9Bud,EAAOvf,EAAMuf,KACbtI,EAActV,KAAKqM,aACnB9F,EAASjG,EAAMiG,OAAOyB,EAAUlG,MAAOkG,EAAUjG,KAAK,GAAO,EAEjE,IAAI2b,IAAQE,EAAKC,MAAQH,IAAQE,EAAKE,IAKlC,GAJAH,GAAU,EACV1V,EAAUyV,IAAQE,EAAKE,GACvBxI,EAAYtM,iBAAiBhB,EAAWzB,EAAQuC,EAAOb,GACvDY,EAAQ7I,KAAK+H,eAAeC,EAAWzB,EAAQuC,EAAOb,GAClDqN,EAAY3M,0BAA0BE,EAAOC,EAAOd,EAAWC,GAC/D,MAAO0V,OAER,KAAID,IAAQE,EAAKG,MAAQL,IAAQE,EAAKI,SACzCL,GAAU,EACV1V,EAAUyV,IAAQE,EAAKG,KACvB/d,KAAKgJ,iBAAiBhB,EAAWzB,EAAQuC,EAAOb,GAAS,GACzDY,EAAQ7I,KAAKoI,iBAAiBJ,EAAWzB,EAAQuC,EAAOb,GACpDqN,EAAY1M,4BAA4BC,EAAOC,EAAOd,EAAWC,IACjE,MAAO0V,EAmBf,OAhBIA,KACArV,EAAYO,EAAMP,UAClBC,EAAUM,EAAMN,QACZO,GACIqU,EAAWnV,EAAUmV,SACrBA,GAAY7U,EACZN,EAAUlG,MAAQwG,EAAU6E,aACpBgQ,GAAY5U,IACpBP,EAAUjG,IAAMwG,EAAQ4H,YAErB7H,GAAaC,IACpBP,EAAUlG,MAAQwG,EAAU6E,YAC5BnF,EAAUjG,IAAMwG,EAAQ4H,WAE5BnI,EAAU7J,WAEPwf,GAEX9C,QAAS,WACL,GAAI3O,GAAOlM,IACPkM,GAAKpH,SACLoH,EAAKpH,QAAQmZ,IAAI1e,GAEjB2M,EAAKwG,QACLxG,EAAKwG,OAAOxE,SAEZhC,EAAK+C,yBACLiP,cAAchS,EAAK+C,yBAEvBzQ,EAAc2N,GAAG0O,QAAQzO,KAAKpM,MAC1BA,KAAKoP,aAAelD,EAAK1P,QAAQ2S,WAC7BjD,EAAK1P,QAAQ2S,SAASU,UAAW,GACjC3D,EAAKuE,eAAeoK,UAEpB3O,EAAK1P,QAAQ2S,SAASiB,UAAW,GACjClE,EAAKgF,gBAAgB2J,cAKrChc,GAAO,EAAMN,GACTH,aAAcA,EACd+f,iBAAkB/f,EAAaS,QAC3BrC,SACIgQ,KAAM,mBACND,MAAO,gBACPwF,mBAAoB,gBACpBC,wBAAyB,gBACzBC,yBAA0B,sBAC1BzH,UAAW,KAEfgC,KAAM,eACNI,mBAAoB,WAChB,GAAoHjP,GAAKN,EAArH+gB,EAAepe,KAAKxD,QAAQN,KAAM4F,EAAQzD,EAAMnC,KAAKmiB,UAAUD,EAAcpe,KAAKse,eAAeC,aAA4B7c,IACjI,KAAK/D,EAAM,EAAGN,EAAS,EAAGM,EAAMN,EAAQM,IACpC+D,EAAM3E,KAAK+E,GACXA,EAAQzD,EAAMnC,KAAKob,QAAQxV,EAE/B9B,MAAKoT,QAAQ1R,MAGrB8c,qBAAsBpgB,EAAaS,QAC/BrC,SACIgQ,KAAM,uBACND,MAAO,qBACPwF,mBAAoB,gBACpBC,wBAAyB,gBACzBC,yBAA0B,sBAC1BzH,UAAW,KAEfgC,KAAM,mBACN6K,SAAU,WACN,GAAIoH,GAAYpgB,EAAMnC,KAAKmiB,UAAUhgB,EAAMnC,KAAKob,QAAQtX,KAAKmQ,WAAYnQ,KAAKse,eAAeC,SAAU,EACvG,OAAOlgB,GAAMnC,KAAKwa,QAAQ+H,EAAWze,KAAKyM,UAAU,KAExD8K,aAAc,WAAA,GACNkH,GAAYpgB,EAAMnC,KAAKmiB,UAAUre,KAAKmN,YAAanN,KAAKse,eAAeC,aACvE9hB,EAAWuD,KAAKyM,SACpB,OAAOpO,GAAMnC,KAAKwa,QAAQ+H,EAAWhiB,EAASA,EAASY,OAAS,GAAK,IAEzEuP,mBAAoB,WAEhB,IADA,GAAIwR,GAAepe,KAAKxD,QAAQN,KAAM4F,EAAQzD,EAAMnC,KAAKmiB,UAAUD,EAAcpe,KAAKxD,QAAQG,kBAAoBoF,EAAM1D,EAAMnC,KAAKmiB,UAAUvc,EAAO9B,KAAKxD,QAAQI,YAAa,GAAI8E,KAC3KI,GAASC,GACZL,EAAM3E,KAAK+E,GACXA,EAAQzD,EAAMnC,KAAKob,QAAQxV,EAE/B9B,MAAKoT,QAAQ1R,MAGrBgd,kBAAmBtgB,EAAaS,QAC5BrC,SACIgQ,KAAM,oBACND,MAAO,iBACPwF,mBAAoB,gBACpBC,wBAAyB,gBACzBrP,aAAc,GAAIvG,MAAK,KAAM,EAAG,EAAG,EAAG,EAAG,GACzCwG,WAAY,GAAIxG,MAAK,KAAM,EAAG,EAAG,GAAI,GAAI,IACzCsW,QAAQ,EACRlI,UAAW,KACXF,eAAgB,GAEpBkC,KAAM,gBACNI,mBAAoB,WAChB,GAAuIjP,GAAKN,EAAxI+gB,EAAepe,KAAKxD,QAAQN,KAAM4F,EAAQzD,EAAMnC,KAAKyiB,gBAAgBP,GAAerc,EAAM1D,EAAMnC,KAAK0iB,eAAeR,GAA4B1c,IACpJ,KAAK/D,EAAM,EAAGN,EAAS0E,EAAIhD,UAAWpB,EAAMN,EAAQM,IAChD+D,EAAM3E,KAAK+E,GACXA,EAAQzD,EAAMnC,KAAKob,QAAQxV,EAE/B9B,MAAKoT,QAAQ1R,SAI3BpD,OAAOD,MAAMwgB,QACRvgB,OAAOD,OACE,kBAAVvC,SAAwBA,OAAOgjB,IAAMhjB,OAAS,SAAUijB,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.scheduler.timelineview.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.scheduler.timelineview', ['kendo.scheduler.view'], f);\n}(function () {\n    var __meta__ = {\n        id: 'scheduler.timelineview',\n        name: 'Scheduler Timeline View',\n        category: 'web',\n        description: 'The Scheduler Timeline View',\n        depends: ['scheduler.view'],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, setTime = kendo.date.setTime, SchedulerView = ui.SchedulerView, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, extend = $.extend, proxy = $.proxy, getDate = kendo.date.getDate, getMilliseconds = kendo.date.getMilliseconds, MS_PER_DAY = kendo.date.MS_PER_DAY, MS_PER_MINUTE = kendo.date.MS_PER_MINUTE, CURRENT_TIME_MARKER_CLASS = 'k-current-time', CURRENT_TIME_MARKER_ARROW_CLASS = 'k-current-time-arrow', SCHEDULER_HEADER_WRAP_CLASS = 'k-scheduler-header-wrap', INVERSE_COLOR_CLASS = 'k-event-inverse', BORDER_SIZE_COEFF = 0.8666, NS = '.kendoTimelineView';\n        var EVENT_TEMPLATE = kendo.template('<div>' + '<div class=\"k-event-template k-event-time\">#:kendo.format(\"{0:t} - {1:t}\", start, end)#</div>' + '<div class=\"k-event-template\">${title}</div></div>'), DATA_HEADER_TEMPLATE = kendo.template('<span class=\\'k-link k-nav-day\\'>#=kendo.format(\\'{0:m}\\', date)#</span>'), EVENT_WRAPPER_STRING = '<div role=\"gridcell\" aria-selected=\"false\" ' + 'data-#=ns#uid=\"#=uid#\"' + '#if (resources[0]) { #' + 'style=\"background-color:#=resources[0].color#; border-color: #=resources[0].color#\"' + 'class=\"k-event\" ' + '#} else {#' + 'class=\"k-event\"' + '#}#' + '>' + '<span class=\"k-event-actions\">' + '# if(data.tail) {#' + '<span class=\"k-icon k-i-arrow-60-left\"></span>' + '#}#' + '# if(data.isException()) {#' + '<span class=\"k-icon k-i-non-recurrence\"></span>' + '# } else if(data.isRecurring()) {#' + '<span class=\"k-icon k-i-reload\"></span>' + '# } #' + '</span>' + '{0}' + '<span class=\"k-event-actions\">' + '#if (showDelete) {#' + '<a href=\"\\\\#\" class=\"k-link k-event-delete\" title=\"${data.messages.destroy}\" aria-label=\"${data.messages.destroy}\"><span class=\"k-icon k-i-close\"></span></a>' + '#}#' + '# if(data.head) {#' + '<span class=\"k-icon k-i-arrow-60-right\"></span>' + '#}#' + '</span>' + '#if(resizable && !data.tail){#' + '<span class=\"k-resize-handle k-resize-w\"></span>' + '#}#' + '#if(resizable && !data.head){#' + '<span class=\"k-resize-handle k-resize-e\"></span>' + '#}#' + '</div>';\n        function toInvariantTime(date) {\n            var staticDate = new Date(1980, 1, 1, 0, 0, 0);\n            setTime(staticDate, getMilliseconds(date));\n            return staticDate;\n        }\n        function getWorkDays(options) {\n            var workDays = [];\n            var dayIndex = options.workWeekStart % 7;\n            var workWeekEnd = Math.abs(options.workWeekEnd % 7);\n            workDays.push(dayIndex);\n            while (workWeekEnd != dayIndex) {\n                if (dayIndex > 6) {\n                    dayIndex -= 7;\n                } else {\n                    dayIndex++;\n                }\n                workDays.push(dayIndex);\n            }\n            return workDays;\n        }\n        function setColspan(columnLevel) {\n            var count = 0;\n            if (columnLevel.columns) {\n                for (var i = 0; i < columnLevel.columns.length; i++) {\n                    count += setColspan(columnLevel.columns[i]);\n                }\n                columnLevel.colspan = count;\n                return count;\n            } else {\n                columnLevel.colspan = 1;\n                return 1;\n            }\n        }\n        function collidingEvents(elements, left, right) {\n            var idx, startPosition, overlaps, endPosition;\n            for (idx = elements.length - 1; idx >= 0; idx--) {\n                startPosition = elements[idx].rectLeft;\n                endPosition = elements[idx].rectRight;\n                overlaps = startPosition <= left && endPosition >= left;\n                if (overlaps || startPosition >= left && endPosition <= right || left <= startPosition && right >= startPosition) {\n                    if (startPosition < left) {\n                        left = startPosition;\n                    }\n                    if (endPosition > right) {\n                        right = endPosition;\n                    }\n                }\n            }\n            return eventsForSlot(elements, left, right);\n        }\n        function eventsForSlot(elements, left, right) {\n            var events = [];\n            for (var idx = 0; idx < elements.length; idx++) {\n                var event = {\n                    rectLeft: elements[idx].rectLeft,\n                    rectRight: elements[idx].rectRight\n                };\n                if (event.rectLeft < left && event.rectRight > left || event.rectLeft >= left && event.rectRight <= right) {\n                    events.push(elements[idx]);\n                }\n            }\n            return events;\n        }\n        var TimelineGroupedView = kendo.Class.extend({\n            init: function (view) {\n                this._view = view;\n            },\n            _getTimeSlotByPosition: function (x, y, groupIndex) {\n                var group = this._view.groups[groupIndex];\n                return group.timeSlotByPosition(x, y);\n            },\n            _hideHeaders: function () {\n                var view = this._view;\n                view.timesHeader.find('table tr:last').hide();\n                view.datesHeader.find('table tr:last').hide();\n            },\n            _setColspan: function (timeColumn) {\n                setColspan(timeColumn);\n            },\n            _createRowsLayout: function (resources, rows, groupHeaderTemplate) {\n                var view = this._view;\n                return view._createRowsLayout(resources, rows, groupHeaderTemplate);\n            },\n            _createVerticalColumnsLayout: function (resources, rows, groupHeaderTemplate, columns) {\n                return columns;\n            },\n            _createColumnsLayout: function (resources, columns, groupHeaderTemplate) {\n                var view = this._view;\n                return view._createColumnsLayout(resources, columns, groupHeaderTemplate);\n            },\n            _getRowCount: function () {\n                var view = this._view;\n                return view._groupCount();\n            },\n            _getGroupsCount: function () {\n                return 1;\n            },\n            _addContent: function (dates, columnCount, groupsCount, rowCount, start, end, slotTemplate, isVerticalGrouped) {\n                var view = this._view;\n                var html = '';\n                var options = view.options;\n                var appendRow = function (date) {\n                    var content = '';\n                    var classes = '';\n                    var tmplDate;\n                    var resources = function (groupIndex) {\n                        return function () {\n                            return view._resourceBySlot({ groupIndex: groupIndex });\n                        };\n                    };\n                    if (kendo.date.isToday(dates[idx])) {\n                        classes += 'k-today';\n                    }\n                    if (kendo.date.getMilliseconds(date) < kendo.date.getMilliseconds(options.workDayStart) || kendo.date.getMilliseconds(date) >= kendo.date.getMilliseconds(options.workDayEnd) || !view._isWorkDay(dates[idx])) {\n                        classes += ' k-nonwork-hour';\n                    }\n                    content += '<td' + (classes !== '' ? ' class=\"' + classes + '\"' : '') + '>';\n                    tmplDate = kendo.date.getDate(dates[idx]);\n                    kendo.date.setTime(tmplDate, kendo.date.getMilliseconds(date));\n                    content += slotTemplate({\n                        date: tmplDate,\n                        resources: resources(isVerticalGrouped ? rowIdx : groupIdx)\n                    });\n                    content += '</td>';\n                    return content;\n                };\n                for (var rowIdx = 0; rowIdx < rowCount; rowIdx++) {\n                    html += '<tr>';\n                    for (var groupIdx = 0; groupIdx < groupsCount; groupIdx++) {\n                        for (var idx = 0, length = columnCount; idx < length; idx++) {\n                            html += view._forTimeRange(start, end, appendRow);\n                        }\n                    }\n                    html += '</tr>';\n                }\n                return html;\n            },\n            _addTimeSlotsCollections: function (groupCount, datesCount, tableRows, interval, isVerticallyGrouped) {\n                var view = this._view;\n                var rowCount = tableRows.length;\n                if (isVerticallyGrouped) {\n                    rowCount = Math.floor(rowCount / groupCount);\n                }\n                for (var groupIndex = 0; groupIndex < groupCount; groupIndex++) {\n                    var rowMultiplier = 0;\n                    var group = view.groups[groupIndex];\n                    var time;\n                    if (isVerticallyGrouped) {\n                        rowMultiplier = groupIndex;\n                    }\n                    var rowIndex = rowMultiplier * rowCount;\n                    var cellMultiplier = 0;\n                    if (!isVerticallyGrouped) {\n                        cellMultiplier = groupIndex;\n                    }\n                    var cells = tableRows[rowIndex].children;\n                    var cellsPerGroup = cells.length / (!isVerticallyGrouped ? groupCount : 1);\n                    var cellsPerDay = cellsPerGroup / datesCount;\n                    for (var dateIndex = 0; dateIndex < datesCount; dateIndex++) {\n                        var cellOffset = dateIndex * cellsPerDay + cellsPerGroup * cellMultiplier;\n                        time = getMilliseconds(new Date(+view.startTime()));\n                        for (var cellIndex = 0; cellIndex < cellsPerDay; cellIndex++) {\n                            view._addTimeSlotToCollection(group, cells, cellIndex, cellOffset, dateIndex, time, interval);\n                            time += interval;\n                        }\n                    }\n                }\n            },\n            _getVerticalGroupCount: function (groupsCount) {\n                return groupsCount;\n            },\n            _getVerticalRowCount: function (eventGroups, groupIndex, maxRowCount) {\n                var view = this._view;\n                return view._isVerticallyGrouped() ? eventGroups[groupIndex].maxRowCount : maxRowCount;\n            },\n            _renderEvent: function (eventGroup, event, adjustedEvent, group, range, container) {\n                var view = this._view;\n                var element;\n                element = view._createEventElement(adjustedEvent.occurrence, event, range.head || adjustedEvent.head, range.tail || adjustedEvent.tail);\n                element.appendTo(container).css({\n                    top: 0,\n                    height: view.options.eventHeight\n                });\n                var eventObject = {\n                    start: adjustedEvent.occurrence._startTime || adjustedEvent.occurrence.start,\n                    end: adjustedEvent.occurrence._endTime || adjustedEvent.occurrence.end,\n                    element: element,\n                    uid: event.uid,\n                    slotRange: range,\n                    rowIndex: 0,\n                    offsetTop: 0\n                };\n                eventGroup.events[event.uid] = eventObject;\n                view._inverseEventColor(element);\n                view.addContinuousEvent(group, range, element, event.isAllDay);\n                view._arrangeRows(eventObject, range, eventGroup);\n            },\n            _verticalCountForLevel: function (level) {\n                var view = this._view;\n                return view._rowCountForLevel(level);\n            },\n            _horizontalCountForLevel: function (level) {\n                var view = this._view;\n                return view._columnCountForLevel(level);\n            },\n            _updateCurrentVerticalTimeMarker: function (ranges, currentTime) {\n                var view = this._view;\n                var elementHtml = '<div class=\\'' + CURRENT_TIME_MARKER_CLASS + '\\'></div>';\n                var headerWrap = view.datesHeader.find('.' + SCHEDULER_HEADER_WRAP_CLASS);\n                var left = Math.round(ranges[0].innerRect(currentTime, new Date(currentTime.getTime() + 1), false).left);\n                var timesTableMarker = $(elementHtml).prependTo(headerWrap).addClass(CURRENT_TIME_MARKER_ARROW_CLASS + '-down');\n                timesTableMarker.css({\n                    left: view._adjustLeftPosition(left - outerWidth(timesTableMarker) * BORDER_SIZE_COEFF / 2),\n                    top: headerWrap.find('tr:last').prev().position().top\n                });\n                $(elementHtml).prependTo(view.content).css({\n                    left: view._adjustLeftPosition(left),\n                    width: '1px',\n                    height: view.content[0].scrollHeight - 1,\n                    top: 0\n                });\n            },\n            _changeGroup: function () {\n                return undefined;\n            },\n            _prevGroupSlot: function (slot, group, isDay) {\n                var view = this._view;\n                if (view._isVerticallyGrouped()) {\n                    return slot;\n                } else {\n                    var collection = group._collection(0, isDay);\n                    return collection.last();\n                }\n            },\n            _nextGroupSlot: function (slot, group, isDay) {\n                var view = this._view;\n                if (view._isVerticallyGrouped()) {\n                    return slot;\n                } else {\n                    var collection = group._collection(0, isDay);\n                    return collection.first();\n                }\n            },\n            _verticalSlots: function (selection, reverse) {\n                var view = this._view;\n                return view._changeGroup(selection, reverse);\n            },\n            _verticalMethod: function (reverse) {\n                return reverse ? 'leftSlot' : 'rightSlot';\n            },\n            _normalizeVerticalSelection: function () {\n                return undefined;\n            },\n            _horizontalSlots: function (selection, group, method, startSlot, endSlot, multiple, reverse) {\n                var view = this._view;\n                var result = {};\n                result.startSlot = group[method](startSlot);\n                result.endSlot = group[method](endSlot);\n                if (!multiple && view._isHorizontallyGrouped() && (!result.startSlot || !result.endSlot)) {\n                    result.startSlot = result.endSlot = view._changeGroup(selection, reverse);\n                }\n                return result;\n            },\n            _changeVerticalViewPeriod: function () {\n                return false;\n            },\n            _changeHorizontalViewPeriod: function (slots, shift, selection, reverse) {\n                var view = this._view;\n                if ((!slots.startSlot || !slots.endSlot) && !shift && view._changeViewPeriod(selection, reverse, false)) {\n                    return true;\n                }\n                return false;\n            },\n            _updateDirection: function (selection, ranges, shift, reverse) {\n                var view = this._view;\n                view._updateDirection(selection, ranges, shift, reverse, true);\n            },\n            _createMoveHint: function (range, adjustedEvent) {\n                var view = this._view;\n                var startSlot = range.start;\n                var hint = view._createEventElement(adjustedEvent.occurrence, adjustedEvent.occurrence, false, false);\n                hint.addClass('k-event-drag-hint');\n                var rect = range.innerRect(adjustedEvent.occurrence.start, adjustedEvent.occurrence.end, view.options.snap);\n                var width = rect.right - rect.left - 2;\n                if (width < 0) {\n                    width = 0;\n                }\n                var left = view._adjustLeftPosition(rect.left);\n                var css = {\n                    left: left,\n                    top: startSlot.offsetTop,\n                    height: startSlot.offsetHeight - 2,\n                    width: width\n                };\n                hint.css(css);\n                if (adjustedEvent.occurrence.inverseColor) {\n                    hint.addClass(INVERSE_COLOR_CLASS);\n                }\n                view._appendMoveHint(hint);\n            },\n            _adjustLeftPosition: function (left) {\n                var view = this._view;\n                if (view._isRtl) {\n                    left -= view.content[0].scrollWidth - view.content[0].clientWidth;\n                }\n                return left;\n            }\n        });\n        var TimelineGroupedByDateView = kendo.Class.extend({\n            init: function (view) {\n                this._view = view;\n            },\n            _getTimeSlotByPosition: function (x, y, groupIndex) {\n                var group = this._view.groups[groupIndex];\n                return group.timeSlotByPosition(x, y, true);\n            },\n            _hideHeaders: function () {\n                var view = this._view;\n                if (!view._isVerticallyGrouped()) {\n                    view.timesHeader.find('table tr').eq(2).hide();\n                    view.datesHeader.find('table tr').eq(2).hide();\n                } else {\n                    view.times.find('.k-last').hide();\n                }\n            },\n            _setColspan: function () {\n            },\n            _createRowsLayout: function (resources, rows, groupHeaderTemplate, columns) {\n                var view = this._view;\n                return view._createDateLayout(columns, null, true);\n            },\n            _createVerticalColumnsLayout: function (resources, rows, groupHeaderTemplate) {\n                var view = this._view;\n                return view._createColumnsLayout(resources, null, groupHeaderTemplate);\n            },\n            _createColumnsLayout: function (resources, columns, groupHeaderTemplate, subColumns) {\n                var view = this._view;\n                return view._createColumnsLayout(resources, columns, groupHeaderTemplate, subColumns, true);\n            },\n            _getRowCount: function (level) {\n                var view = this._view;\n                return view._rowCountForLevel(level);\n            },\n            _getGroupsCount: function () {\n                var view = this._view;\n                return view._groupCount();\n            },\n            _addContent: function (dates, columnCount, groupsCount, rowCount, start, end, slotTemplate, isVerticalGrouped) {\n                var view = this._view;\n                var html = '';\n                var options = view.options;\n                var appendRow = function (date, isMajorTickColumn, isMiddleColumn, isLastSlotColumn, minorTickColumns, groupIdx) {\n                    var content = '';\n                    var classes = '';\n                    var tmplDate;\n                    var workDateIndex = view._isVerticallyGrouped() ? dateIndex : idx;\n                    var resources = function (groupIndex) {\n                        return function () {\n                            return view._resourceBySlot({ groupIndex: groupIndex });\n                        };\n                    };\n                    if (kendo.date.isToday(dates[idx])) {\n                        classes += 'k-today';\n                    }\n                    if (kendo.date.getMilliseconds(date) < kendo.date.getMilliseconds(options.workDayStart) || kendo.date.getMilliseconds(date) >= kendo.date.getMilliseconds(options.workDayEnd) || !view._isWorkDay(dates[workDateIndex])) {\n                        classes += ' k-nonwork-hour';\n                    }\n                    content += '<td' + (classes !== '' ? ' class=\"' + classes + '\"' : '') + '>';\n                    tmplDate = kendo.date.getDate(dates[idx]);\n                    kendo.date.setTime(tmplDate, kendo.date.getMilliseconds(date));\n                    content += slotTemplate({\n                        date: tmplDate,\n                        resources: resources(groupIdx)\n                    });\n                    content += '</td>';\n                    return content;\n                };\n                var tempStart = new Date(start), minorTickCount = view.options.minorTickCount, msMajorInterval = view.options.majorTick * MS_PER_MINUTE, msInterval = msMajorInterval / minorTickCount || 1, dateIndex;\n                for (var rowIdx = 0; rowIdx < rowCount; rowIdx++) {\n                    html += '<tr>';\n                    if (rowIdx % (rowCount / view._dates.length) === 0) {\n                        dateIndex = rowIdx / (rowCount / view._dates.length);\n                        tempStart = new Date(view._dates[dateIndex]);\n                        kendo.date.setTime(tempStart, kendo.date.getMilliseconds(start));\n                    }\n                    for (var idx = 0, length = columnCount; idx < length; idx++) {\n                        html += view._forTimeRange(tempStart, end, appendRow, isVerticalGrouped, groupsCount);\n                        if (isVerticalGrouped) {\n                            setTime(tempStart, msInterval, false);\n                            break;\n                        }\n                    }\n                    html += '</tr>';\n                }\n                return html;\n            },\n            _addTimeSlotsCollections: function (groupCount, datesCount, tableRows, interval, isVerticallyGrouped) {\n                var view = this._view;\n                var rowCount = tableRows.length;\n                if (isVerticallyGrouped) {\n                    rowCount = rowCount / datesCount;\n                }\n                for (var dateIndex = 0; dateIndex < datesCount; dateIndex++) {\n                    var rowMultiplier = 0;\n                    var time;\n                    if (isVerticallyGrouped) {\n                        rowMultiplier = dateIndex;\n                    }\n                    var rowIndex = rowMultiplier * rowCount;\n                    var cellMultiplier = 0;\n                    var cells = tableRows[rowIndex].children;\n                    var cellsPerGroup = isVerticallyGrouped ? rowCount : cells.length / (datesCount * groupCount);\n                    var cellsPerDay = cells.length / datesCount;\n                    var cellOffset;\n                    time = getMilliseconds(new Date(+view.startTime()));\n                    for (var cellIndex = 0; cellIndex < cellsPerGroup; cellIndex++) {\n                        if (!isVerticallyGrouped) {\n                            cellOffset = dateIndex * cellsPerDay + groupCount * cellIndex;\n                            cellMultiplier++;\n                        } else {\n                            cellOffset = 0;\n                            cells = tableRows[cellIndex + cellsPerGroup * dateIndex].children;\n                        }\n                        for (var groupIndex = 0; groupIndex < groupCount; groupIndex++) {\n                            var group = view.groups[groupIndex];\n                            view._addTimeSlotToCollection(group, cells, groupIndex, cellOffset, dateIndex, time, interval);\n                        }\n                        time += interval;\n                    }\n                }\n            },\n            _getVerticalGroupCount: function () {\n                var view = this._view;\n                return view.content.find('tr').length;\n            },\n            _getVerticalRowCount: function (eventGroups, groupIndex, maxRowCount) {\n                return maxRowCount;\n            },\n            _renderEvent: function (eventGroup, event, adjustedEvent, group, range, container, startIndex, endIndex) {\n                var view = this._view;\n                var element;\n                var eventObjects = [];\n                for (var i = range.start.index; i <= range.end.index; i++) {\n                    element = view._createEventElement(adjustedEvent.occurrence, event, i !== endIndex, i !== startIndex);\n                    element.appendTo(container).css({\n                        top: 0,\n                        height: view.options.eventHeight\n                    });\n                    var currentSlot = group._timeSlotCollections[0]._slots[i];\n                    var dateRange = group.timeSlotRanges(currentSlot.start, currentSlot.end, false)[0];\n                    var eventObject = {\n                        start: i === startIndex ? adjustedEvent.occurrence._startTime || adjustedEvent.occurrence.start : currentSlot.start,\n                        end: i === endIndex ? adjustedEvent.occurrence._endTime || adjustedEvent.occurrence.end : currentSlot.end,\n                        element: element,\n                        uid: event.uid,\n                        slotRange: dateRange,\n                        rowIndex: 0,\n                        offsetTop: 0\n                    };\n                    eventGroup.events[event.uid] = eventObject;\n                    eventObjects.push(eventObject);\n                    view.addContinuousEvent(group, dateRange, element, event.isAllDay);\n                    view._arrangeRows(eventObject, dateRange, eventGroup);\n                }\n                eventGroup.events[event.uid] = eventObjects;\n            },\n            _verticalCountForLevel: function (level) {\n                var view = this._view;\n                return view._columnCountForLevel(level);\n            },\n            _horizontalCountForLevel: function (level, columnLevel) {\n                var view = this._view;\n                return view._columnCountForLevel(columnLevel) / view._columnCountForLevel(2);\n            },\n            _updateCurrentVerticalTimeMarker: function (ranges, currentTime) {\n                var view = this._view;\n                var firstTimesCell = view.times.find('tr:first th:first');\n                var lastTimesCell = view.times.find('tr:first th:last');\n                var elementHtml = '<div class=\\'' + CURRENT_TIME_MARKER_CLASS + '\\'></div>';\n                var timesTableMarker = $(elementHtml).prependTo(view.times);\n                var markerTopPosition = Math.round(ranges[0].innerRect(currentTime, new Date(currentTime.getTime() + 1), false).top);\n                var timesTableMarkerCss = {};\n                if (this._isRtl) {\n                    timesTableMarkerCss.right = firstTimesCell.position().left + outerHeight(firstTimesCell) - outerHeight(lastTimesCell);\n                    timesTableMarker.addClass(CURRENT_TIME_MARKER_ARROW_CLASS + '-left');\n                } else {\n                    timesTableMarkerCss.left = lastTimesCell.position().left;\n                    timesTableMarker.addClass(CURRENT_TIME_MARKER_ARROW_CLASS + '-right');\n                }\n                timesTableMarkerCss.top = markerTopPosition - outerWidth(timesTableMarker) * BORDER_SIZE_COEFF / 2;\n                timesTableMarker.css(timesTableMarkerCss);\n                $(elementHtml).prependTo(view.content).css({\n                    top: markerTopPosition,\n                    height: '1px',\n                    right: '1px',\n                    width: view.content[0].scrollWidth,\n                    left: 0\n                });\n            },\n            _changeGroup: function (selection, previous, slot) {\n                var view = this._view;\n                if (!slot) {\n                    selection.groupIndex = previous ? view.groups.length - 1 : 0;\n                }\n            },\n            _prevGroupSlot: function (slot) {\n                return slot;\n            },\n            _nextGroupSlot: function (slot) {\n                return slot;\n            },\n            _changeDate: function (selection, reverse, slot) {\n                var view = this._view;\n                var group = view.groups[selection.groupIndex];\n                var collections, index;\n                if (reverse) {\n                    collections = group._getCollections(false);\n                    index = slot.index - 1;\n                    if (index >= 0) {\n                        return collections[0]._slots[index];\n                    }\n                } else {\n                    collections = group._getCollections(false);\n                    index = slot.index + 1;\n                    if (collections[0] && collections[0]._slots[index]) {\n                        return collections[0]._slots[index];\n                    }\n                }\n            },\n            _verticalSlots: function (selection, reverse, slot) {\n                return this._changeDate(selection, reverse, slot);\n            },\n            _verticalMethod: function (reverse, multiple) {\n                if (multiple) {\n                    return reverse ? 'upSlot' : 'downSlot';\n                } else {\n                    return reverse ? 'leftSlot' : 'rightSlot';\n                }\n            },\n            _normalizeVerticalSelection: function (selection, ranges, reverse, multiple) {\n                var view = this._view;\n                if (!multiple) {\n                    return view._normalizeVerticalSelection(selection, ranges, reverse);\n                }\n                return undefined;\n            },\n            _horizontalSlots: function (selection, group, method, startSlot, endSlot, multiple, reverse) {\n                var view = this._view;\n                var tempSlot = view._changeGroup(selection, reverse);\n                var result = {};\n                if (!tempSlot) {\n                    if (!view._isVerticallyGrouped()) {\n                        result.startSlot = group[method](startSlot);\n                        result.endSlot = group[method](endSlot);\n                    }\n                } else {\n                    result.startSlot = result.endSlot = tempSlot;\n                }\n                return result;\n            },\n            _changeVerticalViewPeriod: function (slots, shift, selection, reverse) {\n                var view = this._view;\n                if ((!slots.startSlot || !slots.endSlot) && !shift && view._changeViewPeriod(selection, reverse, view._isVerticallyGrouped())) {\n                    return true;\n                }\n                return false;\n            },\n            _changeHorizontalViewPeriod: function (slots, shift, selection, reverse) {\n                var view = this._view;\n                if (view._isVerticallyGrouped()) {\n                    return false;\n                }\n                if ((!slots.startSlot || !slots.endSlot) && !shift && view._changeViewPeriod(selection, reverse, false)) {\n                    return true;\n                }\n                return false;\n            },\n            _updateDirection: function (selection, ranges, shift, reverse) {\n                var view = this._view;\n                view._updateDirection(selection, ranges, shift, reverse, !view._isVerticallyGrouped());\n            },\n            _createMoveHint: function (range, adjustedEvent) {\n                var view = this._view;\n                var startSlot = range.start;\n                var startEnd = range.end;\n                for (var slotIdx = startSlot.index; slotIdx <= startEnd.index; slotIdx++) {\n                    var slot = range.collection._slots[slotIdx];\n                    var hint = view._createEventElement(adjustedEvent.occurrence, adjustedEvent.occurrence, false, false);\n                    hint.addClass('k-event-drag-hint');\n                    var css = {\n                        left: slot.offsetLeft + 2,\n                        top: slot.offsetTop,\n                        height: view.options.eventHeight,\n                        width: slot.offsetWidth\n                    };\n                    hint.css(css);\n                    if (adjustedEvent.occurrence.inverseColor) {\n                        hint.addClass(INVERSE_COLOR_CLASS);\n                    }\n                    view._appendMoveHint(hint);\n                }\n            },\n            _adjustLeftPosition: function (left) {\n                var view = this._view;\n                if (view._isRtl && !view._isVerticallyGrouped()) {\n                    left -= view.content[0].scrollWidth - view.content[0].offsetWidth;\n                }\n                return left;\n            }\n        });\n        kendo.ui.scheduler.TimelineGroupedView = TimelineGroupedView;\n        kendo.ui.scheduler.TimelineGroupedByDateView = TimelineGroupedByDateView;\n        var TimelineView = SchedulerView.extend({\n            init: function (element, options) {\n                var that = this;\n                SchedulerView.fn.init.call(that, element, options);\n                that._groupedView = that._getGroupedView();\n                that.title = that.options.title || that.options.name;\n                that._workDays = getWorkDays(that.options);\n                that._templates();\n                that._editable();\n                that.calculateDateRange();\n                that._groups();\n                that._currentTime(true);\n            },\n            name: 'timeline',\n            _getGroupedView: function () {\n                if (this._isGroupedByDate()) {\n                    return new kendo.ui.scheduler.TimelineGroupedByDateView(this);\n                } else {\n                    return new kendo.ui.scheduler.TimelineGroupedView(this);\n                }\n            },\n            _getNextEventIndexBySlot: function (slot, sortedEvents, groupIndex) {\n                if (this._isVerticallyGrouped()) {\n                    return kendo.ui.SchedulerView.fn._getNextEventIndexBySlot.call(this, slot, sortedEvents, groupIndex);\n                }\n                var tempIndex = 0;\n                for (var i = 0; i < sortedEvents.length; i++) {\n                    if (slot.startDate() > sortedEvents[i].start.startDate()) {\n                        tempIndex++;\n                        continue;\n                    }\n                    if (slot.startDate().getTime() === sortedEvents[i].start.startDate().getTime() && groupIndex > sortedEvents[i].start.groupIndex) {\n                        tempIndex++;\n                        continue;\n                    }\n                    break;\n                }\n                return tempIndex;\n            },\n            _getSelectedSlot: function (slot, sortedEvents, event, idx, pad, prev) {\n                if (this._isVerticallyGrouped()) {\n                    return kendo.ui.SchedulerView.fn._getSelectedSlot.call(this, slot, sortedEvents, event, idx, pad, prev);\n                }\n                return slot;\n            },\n            _getSortedEvents: function (uniqueAllEvents) {\n                if (this._isVerticallyGrouped()) {\n                    return kendo.ui.SchedulerView.fn._getSortedEvents.call(this, uniqueAllEvents);\n                }\n                return uniqueAllEvents.sort(function (first, second) {\n                    var result = first.start.startDate().getTime() - second.start.startDate().getTime();\n                    if (result === 0) {\n                        if (first.start.isDaySlot && !second.start.isDaySlot) {\n                            result = -1;\n                        }\n                        if (!first.start.isDaySlot && second.start.isDaySlot) {\n                            result = 1;\n                        }\n                    }\n                    if (result === 0) {\n                        result = first.start.groupIndex - second.start.groupIndex;\n                    }\n                    if (result === 0) {\n                        result = $(first.element).index() - $(second.element).index();\n                    }\n                    return result;\n                });\n            },\n            _currentTimeMarkerUpdater: function () {\n                this._updateCurrentTimeMarker(new Date());\n            },\n            _updateCurrentTimeMarker: function (currentTime) {\n                var options = this.options;\n                this.datesHeader.find('.' + CURRENT_TIME_MARKER_CLASS).remove();\n                this.times.find('.' + CURRENT_TIME_MARKER_CLASS).remove();\n                this.content.find('.' + CURRENT_TIME_MARKER_CLASS).remove();\n                if (!this._isInDateSlot({\n                        start: currentTime,\n                        end: currentTime\n                    })) {\n                    return;\n                }\n                if (options.currentTimeMarker.useLocalTimezone === false) {\n                    var timezone = options.dataSource.options.schema.timezone;\n                    if (options.dataSource && timezone) {\n                        var timezoneOffset = kendo.timezone.offset(currentTime, timezone);\n                        currentTime = kendo.timezone.convert(currentTime, currentTime.getTimezoneOffset(), timezoneOffset);\n                    }\n                }\n                var groupsCount = !options.group || options.group.orientation == 'vertical' ? 1 : this.groups.length;\n                for (var groupIndex = 0; groupIndex < groupsCount; groupIndex++) {\n                    var currentGroup = this.groups[groupIndex];\n                    if (!currentGroup) {\n                        return;\n                    }\n                    var utcCurrentTime = kendo.date.toUtcTime(currentTime);\n                    var ranges = currentGroup.timeSlotRanges(utcCurrentTime, utcCurrentTime + 1);\n                    if (ranges.length === 0) {\n                        return;\n                    }\n                    var collection = ranges[0].collection;\n                    var slotElement = collection.slotByStartDate(currentTime);\n                    if (slotElement) {\n                        if (this._isVerticallyGrouped()) {\n                            this._groupedView._updateCurrentVerticalTimeMarker(ranges, currentTime);\n                        } else {\n                            var elementHtml = '<div class=\\'' + CURRENT_TIME_MARKER_CLASS + '\\'></div>';\n                            var headerWrap = this.datesHeader.find('.' + SCHEDULER_HEADER_WRAP_CLASS);\n                            var left = Math.round(ranges[0].innerRect(currentTime, new Date(currentTime.getTime() + 1), false).left);\n                            var timesTableMarker = $(elementHtml).prependTo(headerWrap).addClass(CURRENT_TIME_MARKER_ARROW_CLASS + '-down');\n                            timesTableMarker.css({\n                                left: this._adjustLeftPosition(left - outerWidth(timesTableMarker) * BORDER_SIZE_COEFF / 2),\n                                top: headerWrap.find('tr:last').prev().position().top\n                            });\n                            $(elementHtml).prependTo(this.content).css({\n                                left: this._adjustLeftPosition(left),\n                                width: '1px',\n                                height: this.content[0].scrollHeight - 1,\n                                top: 0\n                            });\n                        }\n                    }\n                }\n            },\n            _adjustLeftPosition: function (left) {\n                return this._groupedView._adjustLeftPosition(left);\n            },\n            _currentTime: function (setUpdateTimer) {\n                var that = this;\n                var markerOptions = that.options.currentTimeMarker;\n                if (markerOptions !== false && markerOptions.updateInterval !== undefined) {\n                    that._currentTimeMarkerUpdater();\n                    if (setUpdateTimer) {\n                        that._currentTimeUpdateTimer = setInterval(proxy(this._currentTimeMarkerUpdater, that), markerOptions.updateInterval);\n                    }\n                }\n            },\n            _editable: function () {\n                if (this.options.editable) {\n                    if (this._isMobile()) {\n                        this._touchEditable();\n                    } else {\n                        this._mouseEditable();\n                    }\n                }\n            },\n            _mouseEditable: function () {\n                var that = this;\n                that.element.on('click' + NS, '.k-event a:has(.k-i-close)', function (e) {\n                    that.trigger('remove', { uid: $(this).closest('.k-event').attr(kendo.attr('uid')) });\n                    e.preventDefault();\n                });\n                if (that.options.editable.create !== false) {\n                    that.element.on('dblclick' + NS, '.k-scheduler-content td', function (e) {\n                        var slot = that._slotByPosition(e.pageX, e.pageY);\n                        if (slot) {\n                            var resourceInfo = that._resourceBySlot(slot);\n                            that.trigger('add', {\n                                eventInfo: extend({\n                                    start: slot.startDate(),\n                                    end: slot.endDate()\n                                }, resourceInfo)\n                            });\n                        }\n                        e.preventDefault();\n                    });\n                }\n                if (that.options.editable.update !== false) {\n                    that.element.on('dblclick' + NS, '.k-event', function (e) {\n                        that.trigger('edit', { uid: $(this).closest('.k-event').attr(kendo.attr('uid')) });\n                        e.preventDefault();\n                    });\n                }\n            },\n            _touchEditable: function () {\n                var that = this;\n                var threshold = 0;\n                if (kendo.support.mobileOS.android) {\n                    threshold = 5;\n                }\n                if (that.options.editable.create !== false) {\n                    that._addUserEvents = new kendo.UserEvents(that.element, {\n                        threshold: threshold,\n                        useClickAsTap: !kendo.support.browser.edge,\n                        filter: '.k-scheduler-content td',\n                        tap: function (e) {\n                            if (that._scrolling) {\n                                return;\n                            }\n                            var x = e.x.location !== undefined ? e.x.location : e.x;\n                            var y = e.y.location !== undefined ? e.y.location : e.y;\n                            var slot = that._slotByPosition(x, y);\n                            if (slot) {\n                                var resourceInfo = that._resourceBySlot(slot);\n                                that.trigger('add', {\n                                    eventInfo: extend({\n                                        start: slot.startDate(),\n                                        end: slot.endDate()\n                                    }, resourceInfo)\n                                });\n                            }\n                            e.preventDefault();\n                        }\n                    });\n                }\n                if (that.options.editable.update !== false) {\n                    that._editUserEvents = new kendo.UserEvents(that.element, {\n                        threshold: threshold,\n                        useClickAsTap: !kendo.support.browser.edge,\n                        filter: '.k-event',\n                        tap: function (e) {\n                            if (that._scrolling) {\n                                return;\n                            }\n                            var eventElement = $(e.target).closest('.k-event');\n                            var touchElement = $(e.touch.initialTouch);\n                            if (touchElement.hasClass('k-i-close')) {\n                                that.trigger('remove', { uid: eventElement.attr(kendo.attr('uid')) });\n                            } else if (!eventElement.hasClass('k-event-active')) {\n                                that.trigger('edit', { uid: eventElement.attr(kendo.attr('uid')) });\n                            }\n                            e.preventDefault();\n                        }\n                    });\n                }\n            },\n            _slotByPosition: function (x, y) {\n                var slot;\n                var content = this.content;\n                var offset = content.offset();\n                var groupIndex;\n                x -= offset.left;\n                y -= offset.top;\n                if (this._isRtl) {\n                    var browser = kendo.support.browser;\n                    if (browser.mozilla) {\n                        x += content[0].scrollWidth - content[0].offsetWidth;\n                        x += content[0].scrollLeft;\n                    } else if (browser.msie) {\n                        x -= content.scrollLeft();\n                        x += content[0].scrollWidth - content[0].offsetWidth;\n                    } else if (browser.webkit) {\n                        x += content[0].scrollLeft;\n                    }\n                } else {\n                    x += content[0].scrollLeft;\n                }\n                y += content[0].scrollTop;\n                x = Math.ceil(x);\n                y = Math.ceil(y);\n                for (groupIndex = 0; groupIndex < this.groups.length; groupIndex++) {\n                    slot = this._groupedView._getTimeSlotByPosition(x, y, groupIndex);\n                    if (slot) {\n                        return slot;\n                    }\n                }\n                return null;\n            },\n            options: {\n                name: 'TimelineView',\n                title: 'Timeline',\n                selectedDateFormat: '{0:D}',\n                selectedShortDateFormat: '{0:d}',\n                selectedMobileDateFormat: '{0:MMM dd}',\n                date: kendo.date.today(),\n                startTime: kendo.date.today(),\n                endTime: kendo.date.today(),\n                showWorkHours: false,\n                minorTickCount: 2,\n                editable: true,\n                workDayStart: new Date(1980, 1, 1, 8, 0, 0),\n                workDayEnd: new Date(1980, 1, 1, 17, 0, 0),\n                workWeekStart: 1,\n                workWeekEnd: 5,\n                majorTick: 60,\n                eventHeight: 25,\n                eventMinWidth: 0,\n                columnWidth: 100,\n                groupHeaderTemplate: '#=text#',\n                majorTimeHeaderTemplate: '#=kendo.toString(date, \\'t\\')#',\n                slotTemplate: '&nbsp;',\n                eventTemplate: EVENT_TEMPLATE,\n                dateHeaderTemplate: DATA_HEADER_TEMPLATE,\n                footer: { command: 'workDay' },\n                currentTimeMarker: {\n                    updateInterval: 10000,\n                    useLocalTimezone: true\n                },\n                messages: {\n                    defaultRowText: 'All events',\n                    showFullDay: 'Show full day',\n                    showWorkDay: 'Show business hours'\n                }\n            },\n            events: [\n                'remove',\n                'add',\n                'edit'\n            ],\n            _templates: function () {\n                var options = this.options, settings = extend({}, kendo.Template, options.templateSettings);\n                this.eventTemplate = this._eventTmpl(options.eventTemplate, EVENT_WRAPPER_STRING);\n                this.majorTimeHeaderTemplate = kendo.template(options.majorTimeHeaderTemplate, settings);\n                this.dateHeaderTemplate = kendo.template(options.dateHeaderTemplate, settings);\n                this.slotTemplate = kendo.template(options.slotTemplate, settings);\n                this.groupHeaderTemplate = kendo.template(options.groupHeaderTemplate, settings);\n            },\n            _render: function (dates) {\n                var that = this;\n                dates = dates || [];\n                that._dates = dates;\n                that._startDate = dates[0];\n                that._endDate = dates[dates.length - 1 || 0];\n                that._calculateSlotRanges();\n                that.createLayout(that._layout(dates));\n                that._content(dates);\n                that._footer();\n                that._setContentWidth();\n                that.refreshLayout();\n                that.datesHeader.on('click' + NS, '.k-nav-day', function (e) {\n                    var th = $(e.currentTarget).closest('th');\n                    var slot = that._slotByPosition(th.offset().left, that.content.offset().top);\n                    that.trigger('navigate', {\n                        view: 'timeline',\n                        date: slot.startDate()\n                    });\n                });\n                that._groupedView._hideHeaders();\n            },\n            _setContentWidth: function () {\n                var content = this.content;\n                var contentWidth = content.width();\n                var contentTable = this.content.find('table');\n                var columnCount = contentTable.find('tr:first').children().length;\n                var minWidth = 100;\n                var calculatedWidth = columnCount * this.options.columnWidth;\n                if (contentWidth < calculatedWidth) {\n                    minWidth = Math.ceil(calculatedWidth / contentWidth * 100);\n                }\n                contentTable.add(this.datesHeader.find('table')).css('width', minWidth + '%');\n            },\n            _calculateSlotRanges: function () {\n                var dates = this._dates;\n                var slotStartTime = this.startTime();\n                var slotEndTime = this.endTime();\n                slotEndTime = getMilliseconds(slotEndTime);\n                slotStartTime = getMilliseconds(slotStartTime);\n                if (slotEndTime === slotStartTime) {\n                    slotEndTime += MS_PER_DAY - 1;\n                } else if (slotEndTime < slotStartTime) {\n                    slotEndTime += MS_PER_DAY;\n                }\n                var slotRanges = [];\n                for (var i = 0; i < dates.length; i++) {\n                    var rangeStart = getDate(dates[i]);\n                    setTime(rangeStart, slotStartTime);\n                    var rangeEnd = getDate(dates[i]);\n                    setTime(rangeEnd, slotEndTime);\n                    slotRanges.push({\n                        start: kendo.date.toUtcTime(rangeStart),\n                        end: kendo.date.toUtcTime(rangeEnd)\n                    });\n                }\n                this._slotRanges = slotRanges;\n            },\n            _forTimeRange: function (min, max, action, verticalByDate, groupsCount) {\n                min = toInvariantTime(min);\n                max = toInvariantTime(max);\n                var that = this, msMin = getMilliseconds(min), msMax = getMilliseconds(max), minorTickCount = that.options.minorTickCount, msMajorInterval = that.options.majorTick * MS_PER_MINUTE, msInterval = msMajorInterval / minorTickCount || 1, start = new Date(+min), idx = 0, length, html = '';\n                length = MS_PER_DAY / msInterval;\n                if (msMin != msMax) {\n                    if (msMin > msMax) {\n                        msMax += MS_PER_DAY;\n                    }\n                    length = (msMax - msMin) / msInterval;\n                }\n                length = verticalByDate ? 1 : Math.round(length);\n                if (groupsCount) {\n                    length = length * groupsCount;\n                }\n                for (; idx < length; idx++) {\n                    var majorTickDivider = idx % (msMajorInterval / msInterval);\n                    var isMajorTickColumn = majorTickDivider === 0;\n                    var isMiddleColumn = majorTickDivider < minorTickCount - 1;\n                    var isLastSlotColumn = majorTickDivider === minorTickCount - 1;\n                    var minorTickColumns = minorTickCount;\n                    if (length % minorTickCount !== 0) {\n                        var isLastMajorSlot = length - (idx + 1) < minorTickCount;\n                        if (isMajorTickColumn && isLastMajorSlot) {\n                            minorTickColumns = length % minorTickCount;\n                        }\n                    }\n                    html += action(start, isMajorTickColumn, isMiddleColumn, isLastSlotColumn, minorTickColumns, idx % groupsCount);\n                    if (!verticalByDate) {\n                        if (groupsCount) {\n                            if (idx % groupsCount === groupsCount - 1) {\n                                setTime(start, msInterval, false);\n                            }\n                        } else {\n                            setTime(start, msInterval, false);\n                        }\n                    }\n                }\n                return html;\n            },\n            _layout: function (dates) {\n                var timeColumns = [];\n                var columns = [];\n                var that = this;\n                var rows = [{ text: that.options.messages.defaultRowText }];\n                var groupedView = that._groupedView;\n                var minorTickSlots = [];\n                for (var minorTickIndex = 0; minorTickIndex < that.options.minorTickCount; minorTickIndex++) {\n                    minorTickSlots.push({\n                        text: '&#8203;',\n                        className: 'k-last',\n                        minorTicks: true\n                    });\n                }\n                this._forTimeRange(that.startTime(), that.endTime(), function (date, majorTick, middleColumn, lastSlotColumn, minorSlotsCount) {\n                    var template = that.majorTimeHeaderTemplate;\n                    if (majorTick) {\n                        var timeColumn = {\n                            text: template({ date: date }),\n                            className: lastSlotColumn ? 'k-slot-cell' : '',\n                            columns: minorTickSlots.slice(0, minorSlotsCount)\n                        };\n                        groupedView._setColspan(timeColumn);\n                        timeColumns.push(timeColumn);\n                    }\n                });\n                for (var idx = 0; idx < dates.length; idx++) {\n                    columns.push({\n                        text: that.dateHeaderTemplate({ date: dates[idx] }),\n                        className: 'k-slot-cell',\n                        columns: timeColumns.slice(0)\n                    });\n                }\n                var resources = this.groupedResources;\n                if (resources.length) {\n                    if (this._groupOrientation() === 'vertical') {\n                        rows = groupedView._createRowsLayout(resources, null, this.groupHeaderTemplate, columns);\n                        columns = groupedView._createVerticalColumnsLayout(resources, null, this.groupHeaderTemplate, columns);\n                    } else {\n                        columns = groupedView._createColumnsLayout(resources, columns, this.groupHeaderTemplate, columns);\n                    }\n                }\n                return {\n                    columns: columns,\n                    rows: rows\n                };\n            },\n            _footer: function () {\n                var options = this.options;\n                if (options.footer !== false) {\n                    var html = '<div class=\"k-header k-scheduler-footer\">';\n                    var command = options.footer.command;\n                    if (this._isMobile()) {\n                        html += '<span class=\"k-state-default k-scheduler-today\"><a href=\"#\" class=\"k-link\">';\n                        html += options.messages.today + '</a></span>';\n                    }\n                    if (command && command === 'workDay') {\n                        if (this._isMobile()) {\n                            html += '<span class=\"k-state-default k-scheduler-fullday\"><a href=\"#\" class=\"k-link\">';\n                            html += (options.showWorkHours ? options.messages.showFullDay : options.messages.showWorkDay) + '</a></span>';\n                        } else {\n                            html += '<ul class=\"k-reset k-header\">';\n                            html += '<li class=\"k-state-default k-scheduler-fullday\"><a href=\"#\" class=\"k-link\"><span class=\"k-icon k-i-clock\"></span>';\n                            html += (options.showWorkHours ? options.messages.showFullDay : options.messages.showWorkDay) + '</a></li>';\n                            html += '</ul>';\n                        }\n                    } else {\n                        html += '&nbsp;';\n                    }\n                    html += '</div>';\n                    this.footer = $(html).appendTo(this.element);\n                    var that = this;\n                    this.footer.on('click' + NS, '.k-scheduler-fullday', function (e) {\n                        e.preventDefault();\n                        that.trigger('navigate', {\n                            view: that.name || options.name,\n                            date: that.startDate(),\n                            isWorkDay: !options.showWorkHours\n                        });\n                    });\n                    this.footer.on('click' + NS, '.k-scheduler-today', function (e) {\n                        e.preventDefault();\n                        var timezone = that.options.timezone;\n                        var action = 'today';\n                        var currentDate = new Date();\n                        var date;\n                        if (timezone) {\n                            var timezoneOffset = kendo.timezone.offset(currentDate, timezone);\n                            date = kendo.timezone.convert(currentDate, currentDate.getTimezoneOffset(), timezoneOffset);\n                        } else {\n                            date = currentDate;\n                        }\n                        that.trigger('navigate', {\n                            view: that.name || options.name,\n                            action: action,\n                            date: date\n                        });\n                    });\n                }\n            },\n            _columnCountForLevel: function (level) {\n                var columnLevel = this.columnLevels[level];\n                return columnLevel ? columnLevel.length : 0;\n            },\n            _rowCountForLevel: function (level) {\n                var rowLevel = this.rowLevels[level];\n                return rowLevel ? rowLevel.length : 0;\n            },\n            _isWorkDay: function (date) {\n                var day = date.getDay();\n                var workDays = this._workDays;\n                for (var i = 0; i < workDays.length; i++) {\n                    if (workDays[i] === day) {\n                        return true;\n                    }\n                }\n                return false;\n            },\n            _content: function (dates) {\n                var that = this;\n                var start = that.startTime();\n                var end = this.endTime();\n                var groupsCount = 1;\n                var rowCount = 1;\n                var columnCount = dates.length;\n                var html = '';\n                var resources = this.groupedResources;\n                var slotTemplate = this.slotTemplate;\n                var isVerticalGrouped = false;\n                if (resources.length) {\n                    isVerticalGrouped = that._groupOrientation() === 'vertical';\n                    if (isVerticalGrouped) {\n                        rowCount = that._groupedView._getRowCount(this.rowLevels.length - 1);\n                        groupsCount = that._groupedView._getGroupsCount();\n                    } else {\n                        groupsCount = that._groupCount();\n                    }\n                }\n                html += '<tbody>';\n                html += that._groupedView._addContent(dates, columnCount, groupsCount, rowCount, start, end, slotTemplate, isVerticalGrouped);\n                html += '</tbody>';\n                this.content.find('table').append(html);\n            },\n            _groups: function () {\n                var groupCount = this._groupCount();\n                var dates = this._dates;\n                var columnCount = dates.length;\n                this.groups = [];\n                for (var idx = 0; idx < groupCount; idx++) {\n                    var view = this._addResourceView(idx);\n                    var start = dates[0];\n                    var end = dates[dates.length - 1 || 0];\n                    var startTime = getMilliseconds(this.startTime());\n                    var endTime = getMilliseconds(this.endTime());\n                    if (startTime !== 0 && endTime <= startTime) {\n                        start = getDate(start);\n                        setTime(start, startTime);\n                        end = getDate(end);\n                        setTime(end, endTime);\n                    }\n                    view.addTimeSlotCollection(start, kendo.date.addDays(end, 1));\n                }\n                this._timeSlotGroups(groupCount, columnCount);\n            },\n            _isHorizontallyGrouped: function () {\n                return this.groupedResources.length && this._groupOrientation() === 'horizontal';\n            },\n            _timeSlotGroups: function (groupCount, datesCount) {\n                var interval = this._timeSlotInterval();\n                var isVerticallyGrouped = this._isVerticallyGrouped();\n                var tableRows = this.content.find('tr');\n                tableRows.attr('role', 'row');\n                this._groupedView._addTimeSlotsCollections(groupCount, datesCount, tableRows, interval, isVerticallyGrouped);\n            },\n            _addTimeSlotToCollection: function (group, cells, cellIndex, cellOffset, dateIndex, time, interval) {\n                var cell = cells[cellIndex + cellOffset];\n                var collection = group.getTimeSlotCollection(0);\n                var currentDate = this._dates[dateIndex];\n                var currentTime = Date.UTC(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());\n                var start = currentTime + time;\n                var end = start + interval;\n                cell.setAttribute('role', 'gridcell');\n                cell.setAttribute('aria-selected', false);\n                collection.addTimeSlot(cell, start, end, true);\n            },\n            startDate: function () {\n                return this._startDate;\n            },\n            endDate: function () {\n                return this._endDate;\n            },\n            visibleEndDate: function () {\n                var startTime = getMilliseconds(this.startTime());\n                var endTime = getMilliseconds(this.endTime());\n                var endDate = this.endDate();\n                if (startTime !== 0 && endTime <= startTime) {\n                    endDate = kendo.date.addDays(endDate, 1);\n                }\n                return endDate;\n            },\n            startTime: function () {\n                var options = this.options;\n                return options.showWorkHours ? options.workDayStart : options.startTime;\n            },\n            endTime: function () {\n                var options = this.options;\n                return options.showWorkHours ? options.workDayEnd : options.endTime;\n            },\n            _timeSlotInterval: function () {\n                var options = this.options;\n                return options.majorTick / options.minorTickCount * MS_PER_MINUTE;\n            },\n            nextDate: function () {\n                return kendo.date.nextDay(this.endDate());\n            },\n            previousDate: function () {\n                return kendo.date.previousDay(this.startDate());\n            },\n            calculateDateRange: function () {\n                this._render([this.options.date]);\n            },\n            render: function (events) {\n                this._headerColumnCount = 0;\n                this._groups();\n                this.element.find('.k-event').remove();\n                events = new kendo.data.Query(events).sort([\n                    {\n                        field: 'start',\n                        dir: 'asc'\n                    },\n                    {\n                        field: 'end',\n                        dir: 'desc'\n                    }\n                ]).toArray();\n                var eventsByResource = [];\n                this._eventsByResource(events, this.groupedResources, eventsByResource);\n                var eventGroups = [];\n                var maxRowCount = 0;\n                for (var groupIndex = 0; groupIndex < eventsByResource.length; groupIndex++) {\n                    var eventGroup = {\n                        groupIndex: groupIndex,\n                        maxRowCount: 0,\n                        events: {}\n                    };\n                    eventGroups.push(eventGroup);\n                    this._renderEvents(eventsByResource[groupIndex], groupIndex, eventGroup);\n                    if (maxRowCount < eventGroup.maxRowCount) {\n                        maxRowCount = eventGroup.maxRowCount;\n                    }\n                }\n                this._setRowsHeight(eventGroups, eventsByResource.length, maxRowCount);\n                this._positionEvents(eventGroups, eventsByResource.length);\n                this._currentTime(false);\n                this.trigger('activate');\n            },\n            _positionEvents: function (eventGroups, groupsCount) {\n                for (var groupIndex = 0; groupIndex < groupsCount; groupIndex++) {\n                    var eventsForGroup = eventGroups[groupIndex].events;\n                    for (var eventUid in eventsForGroup) {\n                        var eventObject = eventsForGroup[eventUid];\n                        if ($.isArray(eventObject)) {\n                            for (var eventIndex = 0; eventIndex < eventObject.length; eventIndex++) {\n                                this._positionEvent(eventObject[eventIndex]);\n                            }\n                        } else {\n                            this._positionEvent(eventObject);\n                        }\n                    }\n                }\n            },\n            _setRowsHeight: function (eventGroups, groupsCount, maxRowCount) {\n                var eventHeight = this.options.eventHeight + 2;\n                var eventBottomOffset = this._getBottomRowOffset();\n                var groupedView = this._groupedView;\n                var verticalGroupCount = groupedView._getVerticalGroupCount(groupsCount);\n                groupsCount = this._isVerticallyGrouped() ? verticalGroupCount : 1;\n                for (var groupIndex = 0; groupIndex < groupsCount; groupIndex++) {\n                    var rowsCount = groupedView._getVerticalRowCount(eventGroups, groupIndex, maxRowCount);\n                    rowsCount = rowsCount ? rowsCount : 1;\n                    var rowHeight = (eventHeight + 2) * rowsCount + eventBottomOffset;\n                    var timesRow = $(this.times.find('tr')[groupIndex]);\n                    var row = $(this.content.find('tr')[groupIndex]);\n                    timesRow.height(rowHeight);\n                    row.height(rowHeight);\n                }\n                this._setContentWidth();\n                this.refreshLayout();\n                this._refreshSlots();\n            },\n            _getBottomRowOffset: function () {\n                var eventBottomOffset = this.options.eventHeight * 0.5;\n                var isMobile = this._isMobile();\n                var minOffset;\n                var maxOffset;\n                if (isMobile) {\n                    minOffset = 30;\n                    maxOffset = 60;\n                } else {\n                    minOffset = 15;\n                    maxOffset = 30;\n                }\n                if (eventBottomOffset > maxOffset) {\n                    eventBottomOffset = maxOffset;\n                } else if (eventBottomOffset < minOffset) {\n                    eventBottomOffset = minOffset;\n                }\n                return eventBottomOffset;\n            },\n            _positionEvent: function (eventObject) {\n                var eventHeight = this.options.eventHeight + 2;\n                var rect = eventObject.slotRange.innerRect(eventObject.start, eventObject.end, false);\n                var left = this._adjustLeftPosition(rect.left);\n                var width = rect.right - rect.left - 2;\n                if (width < 0) {\n                    width = 0;\n                }\n                if (width < this.options.eventMinWidth) {\n                    var slotsCollection = eventObject.slotRange.collection;\n                    var lastSlot = slotsCollection._slots[slotsCollection._slots.length - 1];\n                    var offsetRight = lastSlot.offsetLeft + lastSlot.offsetWidth;\n                    width = this.options.eventMinWidth;\n                    if (offsetRight < left + width) {\n                        width = offsetRight - rect.left - 2;\n                    }\n                }\n                eventObject.element.css({\n                    top: eventObject.slotRange.start.offsetTop + eventObject.rowIndex * (eventHeight + 2) + 'px',\n                    left: left,\n                    width: width\n                });\n            },\n            _refreshSlots: function () {\n                for (var groupIndex = 0; groupIndex < this.groups.length; groupIndex++) {\n                    this.groups[groupIndex].refresh();\n                }\n            },\n            _eventsByResource: function (events, resources, result) {\n                var resource = resources[0];\n                if (resource) {\n                    var view = resource.dataSource.view();\n                    for (var itemIdx = 0; itemIdx < view.length; itemIdx++) {\n                        var value = this._resourceValue(resource, view[itemIdx]);\n                        var eventsFilteredByResource = new kendo.data.Query(events).filter({\n                            field: resource.field,\n                            operator: SchedulerView.groupEqFilter(value)\n                        }).toArray();\n                        if (resources.length > 1) {\n                            this._eventsByResource(eventsFilteredByResource, resources.slice(1), result);\n                        } else {\n                            result.push(eventsFilteredByResource);\n                        }\n                    }\n                } else {\n                    result.push(events);\n                }\n            },\n            _isInDateSlot: function (event) {\n                var startTime = event.start;\n                var endTime = event.end;\n                var rangeStart = getDate(this._startDate);\n                var rangeEnd = kendo.date.addDays(getDate(this.visibleEndDate()), 1);\n                if (startTime < rangeEnd && rangeStart <= endTime) {\n                    return true;\n                }\n                return false;\n            },\n            _isInTimeSlot: function (event) {\n                var startTime = event._startTime || kendo.date.toUtcTime(event.start);\n                var endTime = event._endTime || kendo.date.toUtcTime(event.end);\n                var slotRanges = this._slotRanges;\n                if (startTime === endTime) {\n                    endTime = endTime + 1;\n                }\n                for (var slotIndex = 0; slotIndex < slotRanges.length; slotIndex++) {\n                    if (startTime < slotRanges[slotIndex].end && slotRanges[slotIndex].start < endTime) {\n                        return true;\n                    }\n                }\n                return false;\n            },\n            _adjustEvent: function (event) {\n                var start = event.start;\n                var end = event.end;\n                var eventStartTime = event._time('start');\n                var eventEndTime = event._time('end');\n                var startTime = getMilliseconds(this.startTime());\n                var endTime = getMilliseconds(this.endTime());\n                var adjustedStartDate = null;\n                var adjustedEndDate = null;\n                var occurrence;\n                var head = false;\n                var tail = false;\n                if (event.isAllDay) {\n                    start = getDate(start);\n                    eventStartTime = 0;\n                    end = getDate(end);\n                    eventEndTime = MS_PER_DAY;\n                    adjustedEndDate = kendo.date.addDays(end, 1);\n                }\n                if (endTime === 0) {\n                    endTime = MS_PER_DAY;\n                }\n                if (endTime <= startTime) {\n                    if (eventStartTime < startTime && eventStartTime >= endTime) {\n                        adjustedStartDate = getDate(start);\n                        setTime(adjustedStartDate, startTime);\n                        tail = true;\n                    }\n                    if (eventEndTime > endTime && eventEndTime <= startTime) {\n                        adjustedEndDate = getDate(end);\n                        setTime(adjustedEndDate, endTime);\n                        head = true;\n                    }\n                } else {\n                    if (startTime > eventStartTime) {\n                        adjustedStartDate = getDate(start);\n                        setTime(adjustedStartDate, startTime);\n                        tail = true;\n                    } else if (endTime <= eventStartTime) {\n                        adjustedStartDate = getDate(start);\n                        adjustedStartDate = kendo.date.addDays(adjustedStartDate, 1);\n                        setTime(adjustedStartDate, startTime);\n                        tail = true;\n                    }\n                    if (endTime < eventEndTime) {\n                        adjustedEndDate = getDate(end);\n                        setTime(adjustedEndDate, endTime);\n                        head = true;\n                    } else if (startTime > eventEndTime) {\n                        adjustedEndDate = getDate(end);\n                        adjustedEndDate = kendo.date.addDays(adjustedEndDate, -1);\n                        setTime(adjustedEndDate, endTime);\n                        head = true;\n                    }\n                }\n                occurrence = event.clone({\n                    start: adjustedStartDate ? adjustedStartDate : start,\n                    end: adjustedEndDate ? adjustedEndDate : end,\n                    _startTime: adjustedStartDate ? kendo.date.toUtcTime(adjustedStartDate) : event._startTime,\n                    _endTime: adjustedEndDate ? kendo.date.toUtcTime(adjustedEndDate) : event._endTime,\n                    isAllDay: false\n                });\n                return {\n                    occurrence: occurrence,\n                    head: head,\n                    tail: tail\n                };\n            },\n            _renderEvents: function (events, groupIndex, eventGroup) {\n                var event;\n                var idx;\n                var length;\n                for (idx = 0, length = events.length; idx < length; idx++) {\n                    event = events[idx];\n                    if (this._isInDateSlot(event)) {\n                        var isMultiDayEvent = event.isAllDay || event.duration() >= MS_PER_DAY;\n                        var container = this.content;\n                        if (isMultiDayEvent || this._isInTimeSlot(event)) {\n                            var adjustedEvent = this._adjustEvent(event);\n                            var group = this.groups[groupIndex];\n                            if (!group._continuousEvents) {\n                                group._continuousEvents = [];\n                            }\n                            if (this._isInTimeSlot(adjustedEvent.occurrence)) {\n                                var ranges = group.slotRanges(adjustedEvent.occurrence, false);\n                                var range = ranges[0];\n                                var startIndex = range.start.index;\n                                var endIndex = range.end.index;\n                                this._groupedView._renderEvent(eventGroup, event, adjustedEvent, group, range, container, startIndex, endIndex);\n                            }\n                        }\n                    }\n                }\n            },\n            addContinuousEvent: function (group, range, element, isAllDay) {\n                var events = group._continuousEvents;\n                events.push({\n                    element: element,\n                    isAllDay: isAllDay,\n                    uid: element.attr(kendo.attr('uid')),\n                    start: range.start,\n                    end: range.end\n                });\n            },\n            _createEventElement: function (occurrence, event, head, tail) {\n                var template = this.eventTemplate;\n                var editable = this.options.editable;\n                var isMobile = this._isMobile();\n                var showDelete = editable && editable.destroy !== false && !isMobile;\n                var resizable = editable && editable.resize !== false;\n                var eventStartTime = event._time('start');\n                var eventEndTime = event._time('end');\n                var eventStartDate = event.start;\n                var eventEndDate = event.end;\n                var resources = this.eventResources(event);\n                if (event._startTime && eventStartTime !== kendo.date.getMilliseconds(event.start)) {\n                    eventStartDate = new Date(eventStartTime);\n                    eventStartDate = kendo.timezone.apply(eventStartDate, 'Etc/UTC');\n                }\n                if (event._endTime && eventEndTime !== kendo.date.getMilliseconds(event.end)) {\n                    eventEndDate = new Date(eventEndTime);\n                    eventEndDate = kendo.timezone.apply(eventEndDate, 'Etc/UTC');\n                }\n                var data = extend({}, {\n                    ns: kendo.ns,\n                    resizable: resizable,\n                    showDelete: showDelete,\n                    head: head,\n                    tail: tail,\n                    singleDay: this._dates.length == 1,\n                    resources: resources,\n                    inverseColor: false,\n                    messages: this.options.messages\n                }, event, {\n                    start: eventStartDate,\n                    end: eventEndDate\n                });\n                var element = $(template(data));\n                this.angular('compile', function () {\n                    return {\n                        elements: element,\n                        data: [{ dataItem: data }]\n                    };\n                });\n                return element;\n            },\n            _arrangeRows: function (eventObject, slotRange, eventGroup) {\n                var startIndex = slotRange.start.index;\n                var endIndex = slotRange.end.index;\n                var rect = eventObject.slotRange.innerRect(eventObject.start, eventObject.end, false);\n                var rectRight = rect.right + this.options.eventMinWidth;\n                var events = collidingEvents(slotRange.events(), rect.left, rectRight);\n                slotRange.addEvent({\n                    slotIndex: startIndex,\n                    start: startIndex,\n                    end: endIndex,\n                    rectLeft: rect.left,\n                    rectRight: rectRight,\n                    element: eventObject.element,\n                    uid: eventObject.uid\n                });\n                events.push({\n                    start: startIndex,\n                    end: endIndex,\n                    uid: eventObject.uid\n                });\n                var rows = SchedulerView.createRows(events);\n                if (eventGroup.maxRowCount < rows.length) {\n                    eventGroup.maxRowCount = rows.length;\n                }\n                for (var idx = 0, length = rows.length; idx < length; idx++) {\n                    var rowEvents = rows[idx].events;\n                    for (var j = 0, eventLength = rowEvents.length; j < eventLength; j++) {\n                        eventGroup.events[rowEvents[j].uid].rowIndex = idx;\n                    }\n                }\n            },\n            _groupCount: function () {\n                var resources = this.groupedResources;\n                var groupedView = this._groupedView;\n                if (resources.length) {\n                    if (this._groupOrientation() === 'vertical') {\n                        return groupedView._verticalCountForLevel(resources.length - 1);\n                    } else {\n                        return groupedView._horizontalCountForLevel(resources.length - 1, this.columnLevels.length - 1);\n                    }\n                }\n                return 1;\n            },\n            _updateEventForSelection: function (event) {\n                var adjustedEvent = this._adjustEvent(event.clone());\n                return adjustedEvent.occurrence;\n            },\n            _eventOptionsForMove: function (event) {\n                if (event.isAllDay) {\n                    return { isAllDay: false };\n                }\n                return {};\n            },\n            _updateEventForResize: function (event) {\n                if (event.isAllDay) {\n                    event.set('isAllDay', false);\n                }\n            },\n            _updateMoveHint: function (event, groupIndex, distance) {\n                var group = this.groups[groupIndex];\n                var clonedEvent = event.clone({\n                    start: event.start,\n                    end: event.end\n                });\n                var eventDuraton = clonedEvent.duration();\n                clonedEvent.start = new Date(clonedEvent.start.getTime() + distance);\n                clonedEvent.end = new Date(+clonedEvent.start + eventDuraton);\n                this._removeMoveHint(event.uid);\n                if (this._isInDateSlot(clonedEvent)) {\n                    if (clonedEvent.isAllDay || clonedEvent.duration() >= MS_PER_DAY || this._isInTimeSlot(clonedEvent)) {\n                        var adjustedEvent = this._adjustEvent(clonedEvent);\n                        var ranges = group.slotRanges(adjustedEvent.occurrence, false);\n                        for (var rangeIndex = 0; rangeIndex < ranges.length; rangeIndex++) {\n                            this._groupedView._createMoveHint(ranges[rangeIndex], adjustedEvent);\n                        }\n                    }\n                }\n            },\n            _appendMoveHint: function (hint) {\n                hint.appendTo(this.content);\n                this._moveHint = this._moveHint.add(hint);\n            },\n            _updateResizeHint: function (event, groupIndex, startTime, endTime) {\n                var group = this.groups[groupIndex];\n                var ranges = group.ranges(startTime, endTime, false, false);\n                this._removeResizeHint();\n                for (var rangeIndex = 0; rangeIndex < ranges.length; rangeIndex++) {\n                    var range = ranges[rangeIndex];\n                    var start = range.startSlot();\n                    var startRect = range.innerRect(startTime, endTime, false);\n                    startRect.top = start.offsetTop;\n                    var width = startRect.right - startRect.left;\n                    if (width < 0) {\n                        for (var i = 0; i < range.events().length; i++) {\n                            if (range.events()[i].uid === event.uid) {\n                                width = range.events()[i].rectRight - startRect.left;\n                                break;\n                            }\n                        }\n                    }\n                    var height = range.endSlot().offsetTop + start.offsetHeight - startRect.top;\n                    var left = this._adjustLeftPosition(startRect.left);\n                    var hint = SchedulerView.fn._createResizeHint.call(this, left, startRect.top, width, height);\n                    this._resizeHint = this._resizeHint.add(hint);\n                }\n                var format = 't';\n                var container = this.content;\n                this._resizeHint.appendTo(container);\n                this._resizeHint.find('.k-label-top,.k-label-bottom').text('');\n                this._resizeHint.first().addClass('k-first').find('.k-label-top').text(kendo.toString(kendo.timezone.toLocalDate(startTime), format));\n                this._resizeHint.last().addClass('k-last').find('.k-label-bottom').text(kendo.toString(kendo.timezone.toLocalDate(endTime), format));\n            },\n            selectionByElement: function (cell) {\n                var offset = cell.offset();\n                return this._slotByPosition(offset.left, offset.top);\n            },\n            _updateDirection: function (selection, ranges, multiple, reverse, vertical) {\n                var startSlot = ranges[0].start;\n                var endSlot = ranges[ranges.length - 1].end;\n                if (multiple && !vertical) {\n                    if (startSlot.index === endSlot.index && startSlot.collectionIndex === endSlot.collectionIndex) {\n                        selection.backward = reverse;\n                    }\n                }\n            },\n            _changeGroup: function (selection, previous) {\n                var method = previous ? 'prevGroupSlot' : 'nextGroupSlot';\n                var slot = this[method](selection.start, selection.groupIndex, false);\n                if (slot) {\n                    selection.groupIndex += previous ? -1 : 1;\n                }\n                this._groupedView._changeGroup(selection, previous, slot);\n                return slot;\n            },\n            prevGroupSlot: function (date, groupIndex, isDay) {\n                var group = this.groups[groupIndex];\n                var slot = group.ranges(date, date, isDay, false)[0].start;\n                if (groupIndex <= 0) {\n                    return;\n                }\n                return this._groupedView._prevGroupSlot(slot, group, isDay);\n            },\n            nextGroupSlot: function (date, groupIndex, isDay) {\n                var group = this.groups[groupIndex];\n                var slot = group.ranges(date, date, isDay, false)[0].start;\n                if (groupIndex >= this.groups.length - 1) {\n                    return;\n                }\n                return this._groupedView._nextGroupSlot(slot, group, isDay);\n            },\n            _verticalSlots: function (selection, ranges, multiple, reverse) {\n                var groupedView = this._groupedView;\n                var method = groupedView._verticalMethod(reverse, multiple);\n                var startSlot = ranges[0].start;\n                var endSlot = ranges[ranges.length - 1].end;\n                var group = this.groups[selection.groupIndex];\n                var slot = groupedView._normalizeVerticalSelection(selection, ranges, reverse, multiple);\n                if (slot) {\n                    startSlot = endSlot = slot;\n                }\n                startSlot = group[method](startSlot);\n                endSlot = group[method](endSlot);\n                if (!multiple && this._isVerticallyGrouped() && (!startSlot || !endSlot)) {\n                    startSlot = endSlot = groupedView._verticalSlots(selection, reverse, slot);\n                }\n                return {\n                    startSlot: startSlot,\n                    endSlot: endSlot\n                };\n            },\n            _horizontalSlots: function (selection, ranges, multiple, reverse) {\n                var method = reverse ? 'upSlot' : 'downSlot';\n                var startSlot = ranges[0].start;\n                var endSlot = ranges[ranges.length - 1].end;\n                var group = this.groups[selection.groupIndex];\n                var result = {};\n                if (!multiple) {\n                    result = this._groupedView._horizontalSlots(selection, group, method, startSlot, endSlot, multiple, reverse);\n                } else {\n                    result.startSlot = group[method](startSlot);\n                    result.endSlot = group[method](endSlot);\n                    if (!multiple && this._isHorizontallyGrouped() && (!startSlot || !endSlot)) {\n                        result.startSlot = result.endSlot = this._changeGroup(selection, reverse);\n                    }\n                }\n                return result;\n            },\n            _changeViewPeriod: function (selection, reverse) {\n                var date = reverse ? this.previousDate() : this.nextDate();\n                var start = selection.start;\n                var end = selection.end;\n                var newStart, newEnd;\n                newStart = new Date(date);\n                newEnd = new Date(date);\n                if (this._isInRange(newStart, newEnd)) {\n                    return false;\n                }\n                selection.start = newStart;\n                selection.end = newEnd;\n                if (this._isHorizontallyGrouped()) {\n                    selection.groupIndex = reverse ? this.groups.length - 1 : 0;\n                }\n                var duration = end - start;\n                if (reverse) {\n                    end = getMilliseconds(this.endTime());\n                    end = end === 0 ? MS_PER_DAY : end;\n                    setTime(selection.start, end - duration);\n                    setTime(selection.end, end);\n                } else {\n                    start = getMilliseconds(this.startTime());\n                    setTime(selection.start, start);\n                    setTime(selection.end, start + duration);\n                }\n                selection.events = [];\n                return true;\n            },\n            move: function (selection, key, shift) {\n                var handled = false;\n                var group = this.groups[selection.groupIndex];\n                var keys = kendo.keys;\n                var groupedView = this._groupedView;\n                var ranges = group.ranges(selection.start, selection.end, false, false);\n                var startSlot, endSlot, reverse, slots;\n                if (key === keys.DOWN || key === keys.UP) {\n                    handled = true;\n                    reverse = key === keys.UP;\n                    groupedView._updateDirection(selection, ranges, shift, reverse);\n                    slots = this._verticalSlots(selection, ranges, shift, reverse);\n                    if (groupedView._changeVerticalViewPeriod(slots, shift, selection, reverse)) {\n                        return handled;\n                    }\n                } else if (key === keys.LEFT || key === keys.RIGHT) {\n                    handled = true;\n                    reverse = key === keys.LEFT;\n                    this._updateDirection(selection, ranges, shift, reverse, false);\n                    slots = this._horizontalSlots(selection, ranges, shift, reverse);\n                    if (groupedView._changeHorizontalViewPeriod(slots, shift, selection, reverse)) {\n                        return handled;\n                    }\n                }\n                if (handled) {\n                    startSlot = slots.startSlot;\n                    endSlot = slots.endSlot;\n                    if (shift) {\n                        var backward = selection.backward;\n                        if (backward && startSlot) {\n                            selection.start = startSlot.startDate();\n                        } else if (!backward && endSlot) {\n                            selection.end = endSlot.endDate();\n                        }\n                    } else if (startSlot && endSlot) {\n                        selection.start = startSlot.startDate();\n                        selection.end = endSlot.endDate();\n                    }\n                    selection.events = [];\n                }\n                return handled;\n            },\n            destroy: function () {\n                var that = this;\n                if (that.element) {\n                    that.element.off(NS);\n                }\n                if (that.footer) {\n                    that.footer.remove();\n                }\n                if (that._currentTimeUpdateTimer) {\n                    clearInterval(that._currentTimeUpdateTimer);\n                }\n                SchedulerView.fn.destroy.call(this);\n                if (this._isMobile() && that.options.editable) {\n                    if (that.options.editable.create !== false) {\n                        that._addUserEvents.destroy();\n                    }\n                    if (that.options.editable.update !== false) {\n                        that._editUserEvents.destroy();\n                    }\n                }\n            }\n        });\n        extend(true, ui, {\n            TimelineView: TimelineView,\n            TimelineWeekView: TimelineView.extend({\n                options: {\n                    name: 'TimelineWeekView',\n                    title: 'Timeline Week',\n                    selectedDateFormat: '{0:D} - {1:D}',\n                    selectedShortDateFormat: '{0:d} - {1:d}',\n                    selectedMobileDateFormat: '{0:MMM dd} - {1:dd}',\n                    majorTick: 120\n                },\n                name: 'timelineWeek',\n                calculateDateRange: function () {\n                    var selectedDate = this.options.date, start = kendo.date.dayOfWeek(selectedDate, this.calendarInfo().firstDay, -1), idx, length, dates = [];\n                    for (idx = 0, length = 7; idx < length; idx++) {\n                        dates.push(start);\n                        start = kendo.date.nextDay(start);\n                    }\n                    this._render(dates);\n                }\n            }),\n            TimelineWorkWeekView: TimelineView.extend({\n                options: {\n                    name: 'TimelineWorkWeekView',\n                    title: 'Timeline Work Week',\n                    selectedDateFormat: '{0:D} - {1:D}',\n                    selectedShortDateFormat: '{0:d} - {1:d}',\n                    selectedMobileDateFormat: '{0:MMM dd} - {1:dd}',\n                    majorTick: 120\n                },\n                name: 'timelineWorkWeek',\n                nextDate: function () {\n                    var weekStart = kendo.date.dayOfWeek(kendo.date.nextDay(this.endDate()), this.calendarInfo().firstDay, 1);\n                    return kendo.date.addDays(weekStart, this._workDays[0]);\n                },\n                previousDate: function () {\n                    var weekStart = kendo.date.dayOfWeek(this.startDate(), this.calendarInfo().firstDay, -1);\n                    var workDays = this._workDays;\n                    return kendo.date.addDays(weekStart, workDays[workDays.length - 1] - 7);\n                },\n                calculateDateRange: function () {\n                    var selectedDate = this.options.date, start = kendo.date.dayOfWeek(selectedDate, this.options.workWeekStart, -1), end = kendo.date.dayOfWeek(start, this.options.workWeekEnd, 1), dates = [];\n                    while (start <= end) {\n                        dates.push(start);\n                        start = kendo.date.nextDay(start);\n                    }\n                    this._render(dates);\n                }\n            }),\n            TimelineMonthView: TimelineView.extend({\n                options: {\n                    name: 'TimelineMonthView',\n                    title: 'Timeline Month',\n                    selectedDateFormat: '{0:D} - {1:D}',\n                    selectedShortDateFormat: '{0:d} - {1:d}',\n                    workDayStart: new Date(1980, 1, 1, 0, 0, 0),\n                    workDayEnd: new Date(1980, 1, 1, 23, 59, 59),\n                    footer: false,\n                    majorTick: 1440,\n                    minorTickCount: 1\n                },\n                name: 'timelineMonth',\n                calculateDateRange: function () {\n                    var selectedDate = this.options.date, start = kendo.date.firstDayOfMonth(selectedDate), end = kendo.date.lastDayOfMonth(selectedDate), idx, length, dates = [];\n                    for (idx = 0, length = end.getDate(); idx < length; idx++) {\n                        dates.push(start);\n                        start = kendo.date.nextDay(start);\n                    }\n                    this._render(dates);\n                }\n            })\n        });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}