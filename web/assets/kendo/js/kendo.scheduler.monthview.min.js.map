{"version": 3, "sources": ["kendo.scheduler.monthview.js"], "names": ["f", "define", "$", "shiftArray", "array", "idx", "slice", "concat", "firstVisibleMonthDay", "date", "calendarInfo", "firstDay", "firstVisibleDay", "Date", "getFullYear", "getMonth", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "getDay", "kendo", "setTime", "MS_PER_DAY", "isInDateRange", "value", "min", "max", "msValue", "msMin", "msMax", "window", "ui", "SchedulerView", "NS", "extend", "getDate", "NUMBER_OF_ROWS", "NUMBER_OF_COLUMNS", "INVERSE_COLOR_CLASS", "DAY_TEMPLATE", "template", "EVENT_WRAPPER_STRING", "EVENT_TEMPLATE", "MORE_BUTTON_TEMPLATE", "MonthGroupedView", "Class", "init", "view", "this", "_view", "_verticalRowCountForLevel", "level", "_rowCountForLevel", "_horizontalGroupCountForLevel", "_columnCountForLevel", "_getCalendarRowsLength", "cellsPerRow", "cellCount", "_createRows", "start", "startIdx", "horizontalGroupCount", "verticalGroupIndex", "groupIdx", "isVerticallyGrouped", "_isVerticallyGrouped", "html", "_createRow", "_adjustStartDate", "addDays", "_getContent", "content", "startDate", "resources", "_getTimeSlotByPosition", "x", "y", "groupIndex", "group", "groups", "daySlotByPosition", "_nextSlotStartDate", "nextDay", "_createRowsLayout", "rows", "groupHeaderTemplate", "_createVerticalColumnsLayout", "columns", "_createColumnsLayout", "_verticalGroupCount", "_horizontalGroupCount", "_columnOffsetForResource", "_positionEvent", "event", "range", "rangeCount", "end", "rangeIndex", "occurrence", "isMobile", "_isMobile", "endDate", "clone", "head", "tail", "_positionMobileEvent", "_createEventElement", "_addDaySlotCollections", "groupCount", "tableRows", "rowMultiplier", "rowIndex", "collection", "tableRow", "cells", "cellMultiplier", "cellIndex", "cell", "columnCount", "rowCount", "addDaySlotCollection", "children", "setAttribute", "addDaySlot", "_changePeriodGroupIndex", "reverse", "length", "_createResizeHint", "left", "startSlot", "offsetLeft", "top", "offsetTop", "width", "innerWidth", "height", "clientHeight", "hint", "fn", "call", "_appendResizeHint", "_createMoveHint", "endSlot", "css", "firstChildHeight", "options", "eventHeight", "index", "addClass", "inverseColor", "_appendMoveHint", "MonthGroupedByDateView", "verticalStart", "dateIdx", "_groupCount", "isLastRow", "cellIdx", "_createDateLayout", "dataIndex", "obj", "resource", "configuration", "data", "dataSource", "text", "htmlEncode", "getter", "dataTextField", "color", "dataColorField", "field", "title", "name", "dataValueField", "className", "push", "subColumns", "i", "currentSlot", "date<PERSON><PERSON><PERSON>", "startIndex", "endIndex", "_slots", "daySlotR<PERSON><PERSON>", "dateIndex", "currentTableIndex", "currentCellIndex", "currentGroupIndex", "_daySlotCollections", "vertical", "selectionGroupIndex", "slotIdx", "slot", "offsetWidth", "offsetHeight", "scheduler", "<PERSON><PERSON>ie<PERSON>", "element", "that", "_<PERSON><PERSON>iew", "_getGroupedView", "_templates", "_editable", "_renderLayout", "_groups", "_isGroupedByDate", "_updateDirection", "selection", "ranges", "multiple", "isSameSlot", "isSameCollection", "updateDirection", "collectionIndex", "backward", "_changeDate", "previous", "collections", "slotIndex", "_getCollections", "daySlotCollectionCount", "_getNextHorizontalRange", "method", "horizontalRange", "isVertical", "_getNextVerticalRange", "verticalRange", "_changeViewPeriod", "newStart", "newEnd", "pad", "_isInRange", "events", "_continuousSlot", "continuousSlot", "_changeGroupContinuously", "lastGroupIndex", "_normalizeHorizontalSelection", "_normalizeVerticalSelection", "settings", "Template", "templateSettings", "eventTemplate", "_eventTmpl", "dayTemplate", "dateForTitle", "format", "selectedDateFormat", "_firstDayOfMonth", "_lastDay<PERSON><PERSON>Month", "shortDateForTitle", "selectedShortDateFormat", "mobileDateForTitle", "selectedMobileDateFormat", "nextDate", "previousDate", "previousDay", "_startDate", "_endDate", "firstDayOfMonth", "lastDayOfMonth", "createLayout", "_layout", "_content", "refreshLayout", "on", "e", "offset", "currentTarget", "_slotByPosition", "preventDefault", "trigger", "_footer", "editable", "_touchEditable", "_mouseEditable", "uid", "closest", "attr", "create", "resourceInfo", "_resourceBySlot", "eventInfo", "isAllDay", "update", "threshold", "support", "mobileOS", "android", "_addUserEvents", "UserEvents", "useClickAsTap", "browser", "edge", "filter", "tap", "_scrolling", "target", "selectionByElement", "columnLevel", "columnLevels", "rowLevel", "rowLevels", "verticalGroupIdx", "verticalGroupCount", "groupedView", "groupedResources", "_createCalendar", "find", "calendarRowsLength", "rowIdx", "weekStartDates", "_slotIndices", "_weekStartDates", "classes", "isToday", "getTime", "inner", "weekDayNames", "days", "namesShort", "map", "names", "showDelete", "destroy", "resizable", "resize", "ns", "eventResources", "messages", "angular", "elements", "dataItem", "_isInDateSlot", "slotStart", "firstSlot", "slotEnd", "lastSlot", "startTime", "toUtcTime", "endTime", "_slotIndex", "slotRange", "eventCount", "container", "collidingEvents", "createRows", "at", "firstChildTop", "append<PERSON><PERSON><PERSON>", "addEvent", "_continuousEvents", "rightOffset", "rowEvents", "eventTop", "j", "event<PERSON>ength", "Math", "style", "more", "clientWidth", "appendTo", "_inverseEventColor", "scrollTop", "scrollLeft", "ceil", "_resizeHint", "add", "_updateResizeHint", "_removeResizeHint", "first", "toString", "timezone", "toLocalDate", "last", "_updateMoveHint", "distance", "duration", "_removeMoveHint", "_moveHint", "getElementsByTagName", "_addResourceView", "MS_PER_HOUR", "floor", "moreButtonHeight", "render", "remove", "Query", "sort", "dir", "toArray", "_renderGroups", "_renderEvents", "rangeStart", "rangeEnd", "slotRanges", "setDate", "middle", "itemIdx", "tmp", "_resourceValue", "operator", "groupEqFilter", "table", "removeClass", "off", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,6BAA8B,wBAAyBD,IAChE,WAwkCE,MA/jCC,UAAUE,GA+iCP,QAASC,GAAWC,EAAOC,GACvB,MAAOD,GAAME,MAAMD,GAAKE,OAAOH,EAAME,MAAM,EAAGD,IAElD,QAASG,GAAqBC,EAAMC,GAEhC,IADA,GAAIC,GAAWD,EAAaC,SAAUC,EAAkB,GAAIC,MAAKJ,EAAKK,cAAeL,EAAKM,WAAY,EAAGN,EAAKO,WAAYP,EAAKQ,aAAcR,EAAKS,aAAcT,EAAKU,mBAC9JP,EAAgBQ,UAAYT,GAC/BU,EAAMZ,KAAKa,QAAQV,KAAsBW,EAE7C,OAAOX,GAEX,QAASY,GAAcC,EAAOC,EAAKC,GAC/B,GAA8BC,GAA1BC,EAAQH,EAAKI,EAAQH,CAEzB,OADAC,GAAUH,EACHG,GAAWC,GAASD,GAAWE,EA5jC7C,GACOT,GAAQU,OAAOV,MAAOW,EAAKX,EAAMW,GAAIC,EAAgBD,EAAGC,cAAeC,EAAK,kBAAmBC,EAASjC,EAAEiC,OAAQC,EAAUf,EAAMZ,KAAK2B,QAASb,EAAaF,EAAMZ,KAAKc,WAAYc,EAAiB,EAAGC,EAAoB,EAAGC,EAAsB,kBAAmBC,EAAenB,EAAMoB,SAAS,uEAAwEC,EAAuB,i+BAAuoCC,EAAiBtB,EAAMoB,SAAS,oGAC5iDG,EAAuBvB,EAAMoB,SAAS,mHACtCI,EAAmBxB,EAAMyB,MAAMX,QAC/BY,KAAM,SAAUC,GACZC,KAAKC,MAAQF,GAEjBG,0BAA2B,SAAUC,GACjC,GAAIJ,GAAOC,KAAKC,KAChB,OAAOF,GAAKK,kBAAkBD,IAElCE,8BAA+B,SAAUF,GACrC,GAAIJ,GAAOC,KAAKC,KAChB,OAAOF,GAAKO,qBAAqBH,IAErCI,uBAAwB,SAAUC,EAAaC,GAC3C,MAAOA,GAAYD,GAEvBE,YAAa,SAAUC,EAAOC,EAAUC,EAAsBC,GAAjD,GAKAC,GAJLhB,EAAOC,KAAKC,MACZO,EAAcnB,EACd2B,EAAsBjB,EAAKkB,uBAC3BC,EAAO,EACX,KAASH,EAAW,EAAGA,EAAWF,EAAsBE,IACpDG,GAAQnB,EAAKoB,WAAWR,EAAOC,EAAUJ,EAAaQ,EAAsBF,EAAqBC,EAErG,OAAOG,IAEXE,iBAAkB,SAAUT,GACxB,MAAOvC,GAAMZ,KAAK6D,QAAQV,EAAOtB,IAErCiC,YAAa,SAAUC,EAASC,EAAWC,GACvC,MAAOF,IACH/D,KAAMgE,EACNC,UAAWA,KAGnBC,uBAAwB,SAAUC,EAAGC,EAAGC,GACpC,GAAIC,GAAQ9B,KAAKC,MAAM8B,OAAOF,EAC9B,OAAOC,GAAME,kBAAkBL,EAAGC,IAEtCK,mBAAoB,SAAUT,GAC1B,MAAOpD,GAAMZ,KAAK0E,QAAQV,IAE9BW,kBAAmB,SAAUV,EAAWW,EAAMC,GAC1C,GAAItC,GAAOC,KAAKC,KAChB,OAAOF,GAAKoC,kBAAkBV,EAAWW,EAAMC,IAEnDC,6BAA8B,SAAUb,EAAWW,EAAMC,EAAqBE,GAC1E,MAAOA,IAEXC,qBAAsB,SAAUf,EAAWc,EAASF,GAChD,GAAItC,GAAOC,KAAKC,KAChB,OAAOF,GAAKyC,qBAAqBf,EAAWc,EAASF,IAEzDI,oBAAqB,SAAUtC,GAC3B,GAAIJ,GAAOC,KAAKC,KAChB,OAAOF,GAAKK,kBAAkBD,IAElCuC,sBAAuB,SAAUvC,GAC7B,GAAIJ,GAAOC,KAAKC,KAChB,OAAOF,GAAKO,qBAAqBH,GAASJ,EAAK4C,yBAAyBxC,IAE5EyC,eAAgB,SAAUC,EAAOf,EAAOgB,EAAOC,EAAYpC,EAAOqC,EAAKC,GAAvD,GAaRC,GAZAnD,EAAOC,KAAKC,MACZkD,EAAWpD,EAAKqD,WAChBL,GAAa,IACM,IAAfE,EACAD,EAAMF,EAAME,IAAIK,UACTJ,GAAcF,EAAa,EAClCpC,EAAQmC,EAAMnC,MAAMa,aAEpBb,EAAQmC,EAAMnC,MAAMa,YACpBwB,EAAMF,EAAME,IAAIK,YAGpBH,EAAaL,EAAMS,OACnB3C,MAAOA,EACPqC,IAAKA,EACLO,KAAMT,EAAMS,KACZC,KAAMV,EAAMU,OAEZL,EACApD,EAAK0D,qBAAqBX,EAAO/C,EAAK2D,oBAAoBR,GAAapB,GAEvE/B,EAAK6C,eAAeE,EAAO/C,EAAK2D,oBAAoBR,GAAapB,IAGzE6B,uBAAwB,SAAUC,EAAYC,EAAWrC,GAAjC,GAIXK,GACDpB,EACAqD,EAIKC,EACDjC,EACAkC,EACAC,EACAC,EACAC,EAKKC,EACDC,EApBZtE,EAAOC,KAAKC,MACZqE,EAAcjF,EACdkF,EAAWnF,CACf,KAASyC,EAAa,EAAGA,EAAa+B,EAAY/B,IAM9C,IALIpB,EAAY,EACZqD,EAAgB,EAChB/D,EAAKkB,yBACL6C,EAAgBjC,GAEXkC,EAAWD,EAAgBS,EAAUR,GAAYD,EAAgB,GAAKS,EAAUR,IAUrF,IATIjC,EAAQ/B,EAAKgC,OAAOF,GACpBmC,EAAalC,EAAM0C,qBAAqBpG,EAAMZ,KAAK6D,QAAQG,EAAWf,GAAYrC,EAAMZ,KAAK6D,QAAQG,EAAWf,EAAY6D,IAC5HL,EAAWJ,EAAUE,GACrBG,EAAQD,EAASQ,SACjBN,EAAiB,EACrBF,EAASS,aAAa,OAAQ,OACzB3E,EAAKkB,yBACNkD,EAAiBtC,GAEZuC,EAAYD,EAAiBG,EAAaF,GAAaD,EAAiB,GAAKG,EAAaF,IAC3FC,EAAOH,EAAME,GACjBrE,EAAK4E,WAAWX,EAAYK,EAAM7C,EAAWf,GAC7CA,KAKhBmE,wBAAyB,SAAUC,GAC/B,GAAI9E,GAAOC,KAAKC,KAChB,OAAO4E,GAAU9E,EAAKgC,OAAO+C,OAAS,EAAI,GAE9CC,kBAAmB,SAAUjC,GAAV,GACX/C,GAAOC,KAAKC,MACZ+E,EAAOlC,EAAMmC,YAAYC,WACzBC,EAAMrC,EAAMnC,MAAMyE,UAClBC,EAAQvC,EAAMwC,aACdC,EAASzC,EAAMnC,MAAM6E,aAAe,EACpCC,EAAOzG,EAAc0G,GAAGX,kBAAkBY,KAAK5F,EAAMiF,EAAMG,EAAKE,EAAOE,EAC3ExF,GAAK6F,kBAAkBH,IAE3BI,gBAAiB,SAAU/C,EAAOD,GAAjB,GACT9C,GAAOC,KAAKC,MACZgF,EAAYnC,EAAMmC,YAClBa,EAAUhD,EAAMgD,UAChBL,EAAO1F,EAAK2D,oBAAoBb,EAAMS,OACtCC,KAAMT,EAAMS,KACZC,KAAMV,EAAMU,OAEhBiC,GAAKM,KACDf,KAAMC,EAAUC,WAAa,EAC7BC,IAAKF,EAAUG,UAAYH,EAAUe,iBACrCT,OAAQxF,EAAKkG,QAAQC,YACrBb,MAAOvC,EAAMwC,cAAgBL,EAAUkB,QAAUL,EAAQK,MAAQ,EAAI,KAEzEV,EAAKW,SAAS,qBACVvD,EAAMwD,cACNZ,EAAKW,SAAS9G,GAElBS,EAAKuG,gBAAgBb,MAGzBc,EAAyBnI,EAAMyB,MAAMX,QACrCY,KAAM,SAAUC,GACZC,KAAKC,MAAQF,GAEjBG,0BAA2B,WACvB,MAAO,IAEXG,8BAA+B,SAAUF,GACrC,GAAIJ,GAAOC,KAAKC,KAChB,OAAOF,GAAKO,qBAAqBH,EAAQ,GAAKd,GAElDqB,YAAa,SAAUC,EAAOC,EAAUC,GAA3B,GAOD2F,GACA5C,EAPJ7D,EAAOC,KAAKC,MACZO,EAAcnB,EACd2B,EAAsBjB,EAAKkB,uBAC3BC,EAAO,GACPuF,EAAU,CACd,IAAIzF,EAAqB,CAGrB,IAFIwF,EAAgB,GAAI5I,MAAK+C,GACzBiD,EAAa7D,EAAK2G,cACjBD,EAASA,EAAUrH,EAAgBqH,IACpCvF,GAAQnB,EAAKoB,WAAWqF,EAAe5F,EAAUgD,EAAY6C,GAC7DD,EAAgBpI,EAAMZ,KAAK6D,QAAQmF,EAAehG,EAEtDG,GAAQvC,EAAMZ,KAAK0E,QAAQvB,OACxB,CACH,IAAK8F,EAASA,EAAUjG,EAAaiG,IACjCvF,GAAQnB,EAAKoB,WAAWR,EAAOC,EAAUC,EAAsB4F,GAC/D9F,EAAQvC,EAAMZ,KAAK0E,QAAQvB,EAE/BA,GAAQvC,EAAMZ,KAAK6D,QAAQV,EAAOH,GAEtC,MAAOU,IAEXE,iBAAkB,SAAUT,EAAOgG,GAAjB,GACV5G,GAAOC,KAAKC,MACZe,EAAsBjB,EAAKkB,sBAC/B,OAAID,GACI2F,EACOvI,EAAMZ,KAAK6D,QAAQV,EAAOtB,GAAqBD,EAAiB,GAAK,GAErEhB,EAAMZ,KAAK0E,QAAQvB,GAG3BvC,EAAMZ,KAAK6D,QAAQV,EAAOtB,IAErCiC,YAAa,SAAUC,EAASC,EAAWC,EAAWmF,GAClD,MAAgB,KAAZA,EACOrF,GACH/D,KAAMgE,EACNC,UAAWA,IAGZ,IAEXC,uBAAwB,SAAUC,EAAGC,EAAGC,GACpC,GAAIC,GAAQ9B,KAAKC,MAAM8B,OAAOF,EAC9B,OAAOC,GAAME,kBAAkBL,EAAGC,GAAG,IAEzCK,mBAAoB,SAAUT,GAC1B,MAAOA,IAEXjB,uBAAwB,WAAA,GAChBR,GAAOC,KAAKC,MACZe,EAAsBjB,EAAKkB,sBAC/B,OAAOD,GAAsB3B,EAAoBD,GAErD+C,kBAAmB,SAAUV,EAAWW,EAAMC,EAAqBE,GAC/D,GAAIxC,GAAOC,KAAKC,KAChB,OAAOF,GAAK8G,kBAAkBtE,EAAS,MAAM,IAEjDD,6BAA8B,SAAUb,EAAWW,EAAMC,GAA3B,GAKjByE,GACDC,EALJhH,EAAOC,KAAKC,MACZ+G,EAAWvF,EAAU,GACrBwF,KACAC,EAAOF,EAASG,WAAWpH,MAC/B,KAAS+G,EAAY,EAAGA,EAAYI,EAAKpC,OAAS1F,EAAgB0H,IAC1DC,GACAK,KAAM/E,GACF+E,KAAMhJ,EAAMiJ,WAAWjJ,EAAMkJ,OAAON,EAASO,eAAeL,EAAKJ,EAAYI,EAAKpC,UAClF0C,MAAOpJ,EAAMkJ,OAAON,EAASS,gBAAgBP,EAAKJ,EAAYI,EAAKpC,SACnE4C,MAAOV,EAASU,MAChBC,MAAOX,EAASW,MAChBC,KAAMZ,EAASY,KACfpJ,MAAOJ,EAAMkJ,OAAON,EAASa,gBAAgBX,EAAKJ,EAAYI,EAAKpC,WAEvEgD,UAAW,eAEff,EAAIxE,QAAUxC,EAAKyC,qBAAqBf,EAAUpE,MAAM,GAAI,KAAMgF,GAClE4E,EAAcc,KAAKhB,EAEvB,OAAOE,IAEXzE,qBAAsB,SAAUf,EAAWc,EAASF,EAAqB2F,GACrE,GAAIjI,GAAOC,KAAKC,KAChB,OAAOF,GAAKyC,qBAAqBf,EAAWc,EAASF,EAAqB2F,GAAY,IAE1FvF,oBAAqB,SAAUtC,GAC3B,GAAIJ,GAAOC,KAAKC,KAChB,OAAOF,GAAKO,qBAAqBH,GAASf,GAE9CsD,sBAAuB,SAAUvC,GAC7B,GAAIJ,GAAOC,KAAKC,KAChB,OAAOF,GAAKO,qBAAqBH,GAASd,GAE9CuD,eAAgB,SAAUC,EAAOf,EAAOgB,EAAOC,EAAYpC,EAAOqC,GAAlD,GAKHiF,GACDC,EACAC,EACAjF,EAPJnD,EAAOC,KAAKC,MACZmI,EAAatF,EAAMnC,MAAMwF,MACzBkC,EAAWvF,EAAME,IAAImD,MACrBhD,EAAWpD,EAAKqD,WACpB,KAAS6E,EAAInF,EAAMnC,MAAMwF,MAAO8B,GAAKnF,EAAME,IAAImD,MAAO8B,IAC9CC,EAAcpF,EAAMkB,WAAWsE,OAAOL,GACtCE,EAAYrG,EAAMyG,cAAcL,EAAYvH,MAAOuH,EAAYvH,OAAO,GAAM,GAC5EuC,EAAaL,EAAMS,OACnB3C,MAAOsH,IAAMG,EAAazH,EAAQuH,EAAY1G,YAC9CwB,IAAKiF,IAAMI,EAAWrF,EAAMkF,EAAY7E,UACxCE,KAAM0E,IAAMI,GAAYvF,EAAMS,KAC9BC,KAAMyE,IAAMG,GAActF,EAAMU,OAEhCL,EACApD,EAAK0D,qBAAqB0E,EAAWpI,EAAK2D,oBAAoBR,GAAapB,GAE3E/B,EAAK6C,eAAeuF,EAAWpI,EAAK2D,oBAAoBR,GAAapB,IAIjF6B,uBAAwB,SAAUC,EAAYC,EAAWrC,GAAjC,GAKXgH,GACIzE,EACDlC,EACA4G,EACAxE,EACAC,EACAC,EAKKC,EACD3D,EACAiI,EACArE,EACAsE,EACA7G,EACAkC,EArBZjE,EAAOC,KAAKC,MACZqE,EAAcjF,EACdkF,EAAWnF,EACX4B,EAAsBjB,EAAKkB,sBAC/B,KAASuH,EAAY,EAAGA,EAAYlE,EAAakE,IAC7C,IAASzE,EAAW,EAAGA,EAAWQ,EAAUR,IAUxC,IATIlC,EAAa,EACb4G,EAAoBzH,EAAsBwH,EAAYzE,EACtDE,EAAWJ,EAAU4E,GACrBvE,EAAQD,EAASQ,SACjBN,EAAiB,EACrBF,EAASS,aAAa,OAAQ,OACzB3E,EAAKkB,yBACNkD,EAAiBqE,GAEZpE,EAAYD,EAAiBP,EAAYQ,GAAaD,EAAiB,GAAKP,EAAYQ,IACzF3D,EAAYsD,EAAWO,EAAckE,EACrCE,EAAmB1H,EAAsBoD,EAAYL,EAAWH,EAAaQ,EAC7EC,EAAOH,EAAMwE,GACbC,EAAoB3H,EAAsBoD,EAAYvC,EACtDC,EAAQ/B,EAAKgC,OAAO4G,GAGpB3E,EADc,IAAdwE,EACa1G,EAAM0C,qBAAqBpG,EAAMZ,KAAK6D,QAAQG,EAAWf,GAAYrC,EAAMZ,KAAK6D,QAAQG,EAAWf,EAAY6D,IAE/GxC,EAAM8G,oBAAoB7E,GAE3ChE,EAAK4E,WAAWX,EAAYK,EAAM7C,EAAWf,GAC7CoB,KAKhB+C,wBAAyB,SAAUC,EAASgE,EAAUC,GAClD,GAAI/I,GAAOC,KAAKC,KAChB,OAAI4I,IAAY9I,EAAKkB,uBACV4D,EAAU9E,EAAKgC,OAAO+C,OAAS,EAAI,EAEvCgE,GAEX/D,kBAAmB,SAAUjC,GAAV,GAEXkC,GAAMG,EAAKE,EAAOE,EAAQE,EASjBsD,EACDC,EAXRjJ,EAAOC,KAAKC,KAEhB,IAAIF,EAAKkB,uBACL+D,EAAOlC,EAAMmC,YAAYC,WACzBC,EAAMrC,EAAMnC,MAAMyE,UAClBC,EAAQvC,EAAMmC,YAAYgE,YAC1B1D,EAASzC,EAAMgD,UAAUV,UAAYtC,EAAMmC,YAAYiE,aAAepG,EAAMmC,YAAYG,UAAY,EACpGK,EAAOzG,EAAc0G,GAAGX,kBAAkBY,KAAK5F,EAAMiF,EAAMG,EAAKE,EAAOE,GACvExF,EAAK6F,kBAAkBH,OAEvB,KAASsD,EAAUjG,EAAMmC,YAAYkB,MAAO4C,GAAWjG,EAAMgD,UAAUK,MAAO4C,IACtEC,EAAOlG,EAAMkB,WAAWsE,OAAOS,GACnC/D,EAAOgE,EAAK9D,WACZC,EAAM6D,EAAK5D,UACXC,EAAQ2D,EAAKC,YACb1D,EAASyD,EAAKE,aAAe,EAC7BzD,EAAOzG,EAAc0G,GAAGX,kBAAkBY,KAAK5F,EAAMiF,EAAMG,EAAKE,EAAOE,GACvExF,EAAK6F,kBAAkBH,IAInCI,gBAAiB,SAAU/C,EAAOD,GAAjB,GAIJkG,GACDC,EACAvD,EALJ1F,EAAOC,KAAKC,MACZgF,EAAYnC,EAAMmC,YAClBa,EAAUhD,EAAMgD,SACpB,KAASiD,EAAU9D,EAAUkB,MAAO4C,GAAWjD,EAAQK,MAAO4C,IACtDC,EAAOlG,EAAMkB,WAAWsE,OAAOS,GAC/BtD,EAAO1F,EAAK2D,oBAAoBb,EAAMS,OACtCC,KAAMT,EAAMS,KACZC,KAAMV,EAAMU,QAEhBiC,EAAKM,KACDf,KAAMgE,EAAK9D,WACXC,IAAK6D,EAAK5D,UAAY4D,EAAKhD,iBAC3BT,OAAQxF,EAAKkG,QAAQC,YACrBb,MAAO2D,EAAKC,YAAc,IAE9BxD,EAAKW,SAAS,qBACVvD,EAAMwD,cACNZ,EAAKW,SAAS9G,GAElBS,EAAKuG,gBAAgBb,KAIjCrH,GAAMW,GAAGoK,UAAUvJ,iBAAmBA,EACtCxB,EAAMW,GAAGoK,UAAU5C,uBAAyBA,EAC5CxH,EAAGqK,UAAYpK,EAAcE,QACzBY,KAAM,SAAUuJ,EAASpD,GACrB,GAAIqD,GAAOtJ,IACXhB,GAAc0G,GAAG5F,KAAK6F,KAAK2D,EAAMD,EAASpD,GAC1CqD,EAAKC,aAAeD,EAAKE,kBACzBF,EAAK3B,MAAQ2B,EAAKrD,QAAQ0B,MAC1B2B,EAAKG,aACLH,EAAKI,YACLJ,EAAKK,cAAcL,EAAKrD,QAAQzI,MAChC8L,EAAKM,WAEThC,KAAM,QACN4B,gBAAiB,WACb,MAAIxJ,MAAK6J,mBACE,GAAIzL,GAAMW,GAAGoK,UAAU5C,uBAAuBvG,MAE9C,GAAI5B,GAAMW,GAAGoK,UAAUvJ,iBAAiBI,OAGvD8J,iBAAkB,SAAUC,EAAWC,EAAQC,EAAUpF,EAASgE,GAAhD,GAEN5D,GACAa,EACAoE,EACAC,EACAC,CALJH,KACIhF,EAAY+E,EAAO,GAAGrJ,MACtBmF,EAAUkE,EAAOA,EAAOlF,OAAS,GAAG9B,IACpCkH,EAAajF,EAAUkB,QAAUL,EAAQK,MACzCgE,EAAmBlF,EAAUoF,kBAAoBvE,EAAQuE,gBAGzDD,EADAvB,EACkBqB,GAAcC,GAAoBA,EAElCD,GAAcC,EAEhCC,IACAL,EAAUO,SAAWzF,KAIjC0F,YAAa,SAAUR,EAAWf,EAAMwB,GAA3B,GAELC,GAAatE,EAUTuE,EAXJ5I,EAAQ9B,KAAK+B,OAAOgI,EAAUlI,WAElC,IAAI2I,GAGA,GAFAC,EAAc3I,EAAM6I,gBAAgB7I,EAAM8I,0BAC1CzE,EAAQ6C,EAAKqB,gBAAkB,EAC3BlE,GAAS,EACT,MAAOsE,GAAYtE,GAAOmC,OAAOmC,EAAYtE,GAAOmC,OAAOxD,OAAS,OAMxE,IAHA2F,EAAc3I,EAAM6I,gBAAgB7I,EAAM8I,0BAC1CzE,EAAQ6C,EAAKqB,gBAAkB,EAC3BK,EAAY,EACZD,EAAYtE,IAAUsE,EAAYtE,GAAOmC,OAAOoC,GAChD,MAAOD,GAAYtE,GAAOmC,OAAOoC,IAI7CG,wBAAyB,SAAU/I,EAAOgJ,EAAQC,GAC9C,GAAIC,GAAahL,KAAKiB,sBAGtB,OAFA8J,GAAgB9F,UAAYnD,EAAMgJ,GAAQC,EAAgB9F,UAAW+F,GACrED,EAAgBjF,QAAUhE,EAAMgJ,GAAQC,EAAgBjF,QAASkF,GAC1DD,GAEXE,sBAAuB,SAAUnJ,EAAOgJ,EAAQI,EAAejB,GAC3D,GAAIe,GAAahL,KAAKiB,wBAA0BjB,KAAK6J,kBAGrD,OAFAqB,GAAcjG,UAAYnD,EAAMgJ,GAAQI,EAAcjG,UAAWgF,EAAUe,GAC3EE,EAAcpF,QAAUhE,EAAMgJ,GAAQI,EAAcpF,QAASmE,EAAUe,GAChEE,GAEXC,kBAAmB,SAAUpB,EAAWlF,EAASgE,GAA9B,GAEXuC,GAAUC,EADVC,EAAMzC,EAAW,EAAI,CAOzB,OALIhE,KACAyG,OAEJF,EAAWhN,EAAMZ,KAAK6D,QAAQ0I,EAAUpJ,MAAO2K,GAC/CD,EAASjN,EAAMZ,KAAK6D,QAAQ0I,EAAU/G,IAAKsI,IACvCtL,KAAKuL,WAAWH,EAAUC,KAG9BtB,EAAUpJ,MAAQyK,EAClBrB,EAAU/G,IAAMqI,IACXxC,GAAYA,GAAY7I,KAAKiB,0BAC9B8I,EAAUlI,WAAa7B,KAAKuJ,aAAa3E,wBAAwBC,EAASgE,EAAUkB,EAAUlI,aAElGkI,EAAUyB,WACH,IAEXC,gBAAiB,SAAU1B,EAAWC,EAAQnF,GAA7B,GACTsB,GAAQ4D,EAAUO,SAAW,EAAIN,EAAOlF,OAAS,EACjDhD,EAAQ9B,KAAK+B,OAAOgI,EAAUlI,WAClC,OAAOC,GAAM4J,eAAe1B,EAAO7D,GAAOxF,MAAOkE,IAErD8G,yBAA0B,SAAU5B,EAAW2B,EAAgBzB,EAAUpF,GAA/C,GAEdhD,GACA+J,EACA/C,EACA/G,CAaR,OAjBKmI,KACGpI,EAAakI,EAAUlI,WACvB+J,EAAiB5L,KAAK+B,OAAO+C,OAAS,EACtC+D,EAAW7I,KAAKiB,uBAChBa,EAAQ9B,KAAK+B,OAAOF,IACnB6J,GAAkB7C,GACnB6C,EAAiB5J,EAAM+C,EAAU,WAAa,eAC9ChD,GAAcgD,KAAe,GACtB6G,IAAmB7C,IAC1BhH,EAAagD,EAAU+G,EAAiB,IAExC/J,EAAa,GAAKA,EAAa+J,KAC/B/J,EAAagD,EAAU+G,EAAiB,EACxCF,EAAiB,MAErB3B,EAAUlI,WAAaA,GAEpB6J,GAEXG,8BAA+B,SAAU9B,EAAWC,EAAQnF,GACxD,GAAImE,EAMJ,OAJIA,GADAnE,EACOmF,EAAO,GAAGrJ,MAEVqJ,EAAOA,EAAOlF,OAAS,GAAG9B,KAIzC8I,4BAA6B,SAAU/B,EAAWC,GAC9C,GAAIhB,EAMJ,OAJIA,GADAe,EAAUO,SACHN,EAAO,GAAGrJ,MAEVqJ,EAAOA,EAAOlF,OAAS,GAAG9B,KAIzCyG,WAAY,WACR,GAAIxD,GAAUjG,KAAKiG,QAAS8F,EAAW7M,KAAWd,EAAM4N,SAAU/F,EAAQgG,iBAC1EjM,MAAKkM,cAAgBlM,KAAKmM,WAAWlG,EAAQiG,cAAezM,GAC5DO,KAAKoM,YAAchO,EAAMoB,SAASyG,EAAQmG,YAAaL,GACvD/L,KAAKqC,oBAAsBjE,EAAMoB,SAASyG,EAAQ5D,oBAAqB0J,IAE3EM,aAAc,WACV,MAAOjO,GAAMkO,OAAOtM,KAAKiG,QAAQsG,mBAAoBvM,KAAKwM,iBAAkBxM,KAAKyM,kBAErFC,kBAAmB,WACf,MAAOtO,GAAMkO,OAAOtM,KAAKiG,QAAQ0G,wBAAyB3M,KAAKwM,iBAAkBxM,KAAKyM,kBAE1FG,mBAAoB,WAChB,MAAOxO,GAAMkO,OAAOtM,KAAKiG,QAAQ4G,yBAA0B7M,KAAKwM,iBAAkBxM,KAAKyM,kBAE3FK,SAAU,WACN,MAAO1O,GAAMZ,KAAK0E,QAAQlC,KAAKyM,kBAEnCM,aAAc,WACV,MAAO3O,GAAMZ,KAAKwP,YAAYhN,KAAKwM,mBAEvChL,UAAW,WACP,MAAOxB,MAAKiN,YAEhB5J,QAAS,WACL,MAAOrD,MAAKkN,UAEhBvD,cAAe,SAAUnM,GACrB,GAAI8L,GAAOtJ,IACXA,MAAKwM,iBAAmBpO,EAAMZ,KAAK2P,gBAAgB3P,GACnDwC,KAAKyM,gBAAkBrO,EAAMZ,KAAK4P,eAAe5P,GACjDwC,KAAKiN,WAAa1P,EAAqBC,EAAMwC,KAAKvC,gBAClDuC,KAAKqN,aAAarN,KAAKsN,WACvBtN,KAAKuN,WACLvN,KAAKwN,gBACLxN,KAAKuB,QAAQkM,GAAG,QAAUxO,EAAI,4BAA6B,SAAUyO,GAAV,GACnDC,GAAS1Q,EAAEyQ,EAAEE,eAAeD,SAC5B3E,EAAOM,EAAKuE,gBAAgBF,EAAO3I,KAAM2I,EAAOxI,IACpDuI,GAAEI,iBACFxE,EAAKyE,QAAQ,YACThO,KAAM,MACNvC,KAAMwL,EAAKxH,gBAGnBxB,KAAKgO,WAETtE,UAAW,WACH1J,KAAKiG,QAAQgI,WACTjO,KAAKoD,YACLpD,KAAKkO,iBAELlO,KAAKmO,mBAIjBA,eAAgB,WACZ,GAAI7E,GAAOtJ,IACXsJ,GAAKD,QAAQoE,GAAG,QAAUxO,EAAI,oDAAqD,SAAUyO,GACzFpE,EAAKyE,QAAQ,UAAYK,IAAKnR,EAAE+C,MAAMqO,QAAQ,YAAYC,KAAKlQ,EAAMkQ,KAAK,UAC1EZ,EAAEI,mBAEFxE,EAAKrD,QAAQgI,SAASM,UAAW,GACjCjF,EAAKD,QAAQoE,GAAG,WAAaxO,EAAI,iDAAkD,SAAUyO,GAAV,GAIvEc,GAHJb,EAAS1Q,EAAEyQ,EAAEE,eAAeD,SAC5B3E,EAAOM,EAAKuE,gBAAgBF,EAAO3I,KAAM2I,EAAOxI,IAChD6D,KACIwF,EAAelF,EAAKmF,gBAAgBzF,GACxCM,EAAKyE,QAAQ,OACTW,UAAWxP,GACPyP,UAAU,EACVhO,MAAOqI,EAAKxH,YACZwB,IAAKgG,EAAKxH,aACXgN,MAGXd,EAAEI,mBAGNxE,EAAKrD,QAAQgI,SAASW,UAAW,GACjCtF,EAAKD,QAAQoE,GAAG,WAAaxO,EAAI,kCAAmC,SAAUyO,GAC1EpE,EAAKyE,QAAQ,QAAUK,IAAKnR,EAAE+C,MAAMqO,QAAQ,YAAYC,KAAKlQ,EAAMkQ,KAAK,UACxEZ,EAAEI,oBAIdI,eAAgB,WAAA,GACR5E,GAAOtJ,KACP6O,EAAY,CACZzQ,GAAM0Q,QAAQC,SAASC,UACvBH,EAAY,GAEZvF,EAAKrD,QAAQgI,SAASM,UAAW,IACjCjF,EAAK2F,eAAiB,GAAI7Q,GAAM8Q,WAAW5F,EAAKD,SAC5CwF,UAAWA,EACXM,eAAgB/Q,EAAM0Q,QAAQM,QAAQC,KACtCC,OAAQ,iDACRC,IAAK,SAAU7B,GAAV,GAIGC,GACA3E,EAEIwF,CANJlF,GAAKkG,aAGL7B,EAAS1Q,EAAEyQ,EAAE+B,QAAQ9B,SACrB3E,EAAOM,EAAKuE,gBAAgBF,EAAO3I,KAAM2I,EAAOxI,KAChD6D,IACIwF,EAAelF,EAAKmF,gBAAgBzF,GACxCM,EAAKyE,QAAQ,OACTW,UAAWxP,GACPyP,UAAU,EACVhO,MAAOqI,EAAKxH,YACZwB,IAAKgG,EAAKxH,aACXgN,MAGXd,EAAEI,uBAKlB4B,mBAAoB,SAAUrL,GAC1B,GAAIsJ,GAAS1Q,EAAEoH,GAAMsJ,QACrB,OAAO3N,MAAK6N,gBAAgBF,EAAO3I,KAAM2I,EAAOxI,MAEpD7E,qBAAsB,SAAUH,GAC5B,GAAIwP,GAAc3P,KAAK4P,aAAazP,EACpC,OAAOwP,GAAcA,EAAY7K,OAAS,GAE9C1E,kBAAmB,SAAUD,GACzB,GAAI0P,GAAW7P,KAAK8P,UAAU3P,EAC9B,OAAO0P,GAAWA,EAAS/K,OAAS,GAExCyI,SAAU,WAAA,GAUGwC,GATL7O,EAAO,UACP8O,EAAqB,EACrBC,EAAcjQ,KAAKuJ,aACnB9H,EAAYzB,KAAKkQ,gBAMrB,KALIzO,EAAUqD,QACN9E,KAAKiB,yBACL+O,EAAqBC,EAAY/P,0BAA0BuB,EAAUqD,OAAS,IAG7EiL,EAAmB,EAAGA,EAAmBC,EAAoBD,IAClE7O,GAAQlB,KAAKmQ,gBAAgBJ,EAEjC7O,IAAQ,WACRlB,KAAKuB,QAAQ6O,KAAK,SAASlP,KAAKA,IAEpCiP,gBAAiB,SAAUrP,GAAV,GAgBTuP,GACKC,EAGD1P,EAnBJD,EAAQX,KAAKwB,YACbf,EAAYpB,EAAoBD,EAChCoB,EAAcnB,EACdkR,GAAkB5P,GAClBO,EAAO,GACPL,EAAuB,EACvBG,EAAsBhB,KAAKiB,uBAC3BgP,EAAcjQ,KAAKuJ,aACnB9H,EAAYzB,KAAKkQ,gBAQrB,KAPIzO,EAAUqD,SACL9D,IACDH,EAAuBoP,EAAY5P,8BAA8BoB,EAAUqD,OAAS,KAG5F9E,KAAKwQ,gBACDH,EAAqBJ,EAAY1P,uBAAuBC,EAAaC,GAChE6P,EAAS,EAAGA,EAASD,EAAoBC,IAC9CpP,GAAQ,OACRqP,EAAexI,KAAKpH,GAChBC,EAAW0P,EAAS9P,EACxBU,GAAQ+O,EAAYvP,YAAYC,EAAOC,EAAUC,EAAsBC,GACvEH,EAAQsP,EAAY7O,iBAAiBT,EAAO2P,IAAWD,EAAqB,GAC5EnP,GAAQ,OAIZ,OAFAlB,MAAKyQ,gBAAkBF,EACvBvQ,KAAKkN,SAAW9O,EAAMZ,KAAKwP,YAAYrM,GAChCO,GAEXC,WAAY,SAAUK,EAAWZ,EAAUJ,EAAaqB,GAA5C,GAWC+E,GAVL0C,EAAOtJ,KACPvB,EAAM6K,EAAKkD,iBACX9N,EAAM4K,EAAKmD,gBACXlL,EAAU+H,EAAK8C,YACfsE,EAAU,GACVxP,EAAO,GACP+O,EAAcjQ,KAAKuJ,aACnB9H,EAAY,WACZ,MAAO6H,GAAKmF,iBAAkB5M,WAAYA,IAE9C,KAAS+E,EAAU,EAAGA,EAAUpG,EAAaoG,IACzC8J,EAAU,GACNtS,EAAMZ,KAAKmT,QAAQnP,KACnBkP,GAAW,WAEVtS,EAAMZ,KAAKe,cAAciD,EAAW/C,EAAKC,KAC1CgS,GAAW,kBAEfxP,GAAQ,OACQ,KAAZwP,IACAxP,GAAQ,UAAYwP,EAAU,KAElCxP,GAAQ,IACRA,GAAQ+O,EAAY3O,YAAYC,EAASC,EAAWC,EAAWmF,GAC/D1F,GAAQ,QACRoI,EAAKkH,aAAarR,EAAQqC,GAAWoP,WAAahQ,EAAWgG,EAC7DpF,EAAYyO,EAAYhO,mBAAmBT,EAE/C,OAAON,IAEXoM,QAAS,WAAA,GAUDlL,GAIQyO,EACKzT,EAdbK,EAAeuC,KAAKvC,eACpBqT,EAAe9Q,KAAKoD,YAAc3F,EAAasT,KAAKC,WAAWC,IAAI,SAAUrJ,GAC7E,MAAOA,GAAK,KACXnK,EAAasT,KAAKG,MACnBA,EAAQhU,EAAW4T,EAAcrT,EAAaC,UAC9C6E,EAAUtF,EAAEgU,IAAIC,EAAO,SAAU1S,GACjC,OAAS4I,KAAM5I,KAEfiD,EAAYzB,KAAKkQ,iBAEjBD,EAAcjQ,KAAKuJ,YACvB,IAAI9H,EAAUqD,OACV,GAAI9E,KAAKiB,uBAAwB,CAE7B,IADI4P,KACKzT,EAAM,EAAGA,EAAM,EAAGA,IACvByT,EAAM9I,MACFX,KAAM,oBACNU,UAAW,wBAGnB1F,GAAO6N,EAAY9N,kBAAkBV,EAAWoP,EAAO7Q,KAAKqC,oBAAqBE,GACjFA,EAAU0N,EAAY3N,6BAA6Bb,EAAWoP,EAAO7Q,KAAKqC,oBAAqBE,OAE/FA,GAAU0N,EAAYzN,qBAAqBf,EAAWc,EAASvC,KAAKqC,oBAAqBE,EAGjG,QACIA,QAASA,EACTH,KAAMA,IAGdsB,oBAAqB,SAAUb,GAAV,GAUbwG,GATApD,EAAUjG,KAAKiG,QACfgI,EAAWhI,EAAQgI,SACnB9K,EAAWnD,KAAKoD,WAcpB,OAbAP,GAAMsO,WAAalD,GAAYA,EAASmD,WAAY,IAAUjO,EAC9DN,EAAMwO,UAAYpD,GAAYA,EAASqD,UAAW,IAAUnO,EAC5DN,EAAM0O,GAAKnT,EAAMmT,GACjB1O,EAAMpB,UAAYzB,KAAKwR,eAAe3O,GACtCA,EAAMwD,cAAe,EACrBxD,EAAM4O,SAAWxL,EAAQwL,WAAcL,QAAS,UAC5C/H,EAAUpM,EAAE+C,KAAKkM,cAAcrJ,IACnC7C,KAAK0R,QAAQ,UAAW,WACpB,OACIC,SAAUtI,EACVnC,OAAS0K,SAAU/O,OAGpBwG,GAEXwI,cAAe,SAAUhP,GAAV,GACPd,GAAS/B,KAAK+B,OAAO,GACrB+P,EAAY/P,EAAOgQ,YAAYpR,MAC/BqR,EAAUjQ,EAAOkQ,WAAWjP,IAAM,EAClCkP,EAAY9T,EAAMZ,KAAK2U,UAAUtP,EAAMlC,OACvCyR,EAAUhU,EAAMZ,KAAK2U,UAAUtP,EAAMG,IACzC,QAAQzE,EAAc2T,EAAWJ,EAAWE,IAAYzT,EAAc6T,EAASN,EAAWE,IAAYzT,EAAcuT,EAAWI,EAAWE,IAAY7T,EAAcyT,EAASE,EAAWE,OAAe7T,EAAc6T,EAASN,EAAWA,IAAcvT,EAAc6T,EAASF,EAAWA,IAAcrP,EAAM8L,WAEjT0D,WAAY,SAAU7U,GAClB,MAAOwC,MAAKwQ,aAAarR,EAAQ3B,GAAMoT,YAE3CnN,qBAAsB,SAAU6O,EAAWjJ,EAASvH,GAA9B,GAKdsG,GACAC,EACAkK,EACA/G,EAMApJ,EACA4G,EACAwJ,EAfAvN,EAAYqN,EAAU3R,KACtB2R,GAAU3R,MAAMuE,WAAaoN,EAAUtP,IAAIkC,aAC3CD,EAAYqN,EAAUtP,KAEtBoF,EAAakK,EAAU3R,MAAMwF,MAC7BkC,EAAWD,EACXmK,EAAa,EACb/G,EAASxM,EAAcyT,gBAAgBH,EAAU9G,SAAUpD,EAAYC,GAC3EmD,EAAOzD,MACHsB,QAASA,EACT1I,MAAOyH,EACPpF,IAAKqF,IAELjG,EAAOpD,EAAc0T,WAAWlH,GAChCxC,EAAOsJ,EAAUtO,WAAW2O,GAAGvK,GAC/BoK,EAAYxJ,EAAKwJ,UAChBA,IACDA,EAAYvV,EAAEmB,EAAMkO,OAAO,uEAAwErH,EAAUG,UAAYH,EAAU2N,cAAgB3N,EAAUe,iBAAmB,KAAMf,EAAUC,WAAa,KAAMD,EAAUgE,YAAc,OAC3OD,EAAKwJ,UAAYA,EACjBxS,KAAKuB,QAAQ,GAAGsR,YAAYL,EAAU,KAEtCpQ,EAAK0C,QAAUyN,IACfD,EAAUQ,UACNzJ,QAASA,EACT1I,MAAOyH,EACPpF,IAAKqF,EACLxG,WAAYoD,EAAUpD,aAE1BC,EAAMiR,kBAAkBhL,MACpBsB,QAASA,EACT+E,IAAK/E,EAAQiF,KAAKlQ,EAAMkQ,KAAK,QAC7B3N,MAAO2R,EAAU3R,MACjBqC,IAAKsP,EAAUtP,MAEnBwP,EAAU,GAAGK,YAAYxJ,EAAQ,MAGzCzG,eAAgB,SAAU0P,EAAWjJ,EAASvH,GAA9B,GAMRsG,GACAC,EACAkK,EACA/G,EACAwH,EAMA5Q,EACKhF,EAAS0H,EACVmO,EACAC,EACKC,EAAOC,EAKP1I,EACD1G,EACAgF,EA1BR9C,EAAclG,KAAKiG,QAAQC,YAC3BjB,EAAYqN,EAAU3R,KAe1B,KAdI2R,EAAU3R,MAAMuE,WAAaoN,EAAUtP,IAAIkC,aAC3CD,EAAYqN,EAAUtP,KAEtBoF,EAAakK,EAAU3R,MAAMwF,MAC7BkC,EAAWiK,EAAUtP,IAAImD,MACzBoM,EAAatN,EAAUsN,WACvB/G,EAASxM,EAAcyT,gBAAgBH,EAAU9G,SAAUpD,EAAYC,GACvE2K,EAAc5K,IAAeC,EAAW,EAAI,EAChDmD,EAAOzD,MACHsB,QAASA,EACT1I,MAAOyH,EACPpF,IAAKqF,IAELjG,EAAOpD,EAAc0T,WAAWlH,GAC3BpO,EAAM,EAAG0H,EAASuO,KAAK5U,IAAI2D,EAAK0C,OAAQyN,GAAanV,EAAM0H,EAAQ1H,IAGxE,IAFI6V,EAAY7Q,EAAKhF,GAAKoO,OACtB0H,EAAWjO,EAAUG,UAAYH,EAAU2N,cAAgB3N,EAAUe,iBAAmB5I,EAAM8I,EAAc,EAAI9I,EAAM,KACjH+V,EAAI,EAAGC,EAAcH,EAAUnO,OAAQqO,EAAIC,EAAaD,IAC7DF,EAAUE,GAAG9J,QAAQ,GAAGiK,MAAMnO,IAAM+N,CAG5C,IAAI9Q,EAAK0C,OAASyN,EACd,IAAS7H,EAAYtC,EAAYsC,GAAarC,EAAUqC,IAChD1G,EAAasO,EAAUtO,WACvBgF,EAAOhF,EAAW2O,GAAGjI,GACrB1B,EAAKuK,OAGTvK,EAAKuK,KAAOtW,EAAE0C,GACV4R,GAAInT,EAAMmT,GACV5Q,MAAO+J,EACP1H,IAAK0H,EACLrF,MAAO2D,EAAKwK,YAAc,EAC1BxO,KAAMgE,EAAK9D,WAAa,EACxBC,IAAK6D,EAAK5D,UAAY4D,EAAK4J,cAAgB5J,EAAKhD,iBAAmBuM,EAAarM,EAAc,EAAIqM,KAEtGvS,KAAKuB,QAAQ,GAAGsR,YAAY7J,EAAKuK,KAAK,SAG1CjB,GAAUQ,UACNzJ,QAASA,EACT1I,MAAOyH,EACPpF,IAAKqF,EACLxG,WAAYoD,EAAUpD,aAE1BwH,EAAQ,GAAGiK,MAAMjO,MAAQiN,EAAUhN,aAAe0N,EAAc,KAChE3J,EAAQ,GAAGiK,MAAMtO,KAAOC,EAAUC,WAAa,EAAI,KACnDmE,EAAQ,GAAGiK,MAAM/N,OAASW,EAAc,KACxCpE,EAAMiR,kBAAkBhL,MACpBsB,QAASA,EACT+E,IAAK/E,EAAQiF,KAAKlQ,EAAMkQ,KAAK,QAC7B3N,MAAO2R,EAAU3R,MACjBqC,IAAKsP,EAAUtP,MAEnBqG,EAAQoK,SAASzT,KAAKuB,SACtBvB,KAAK0T,mBAAmBrK,IAGhCwE,gBAAiB,SAAUlM,EAAGC,GAAb,GAQJC,GACDmH,EARJ2E,EAAS3N,KAAKuB,QAAQoM,QAO1B,KANAhM,GAAKgM,EAAO3I,KACZpD,GAAK+L,EAAOxI,IACZvD,GAAK5B,KAAKuB,QAAQ,GAAGoS,UACrBhS,GAAK3B,KAAKuB,QAAQ,GAAGqS,WACrBjS,EAAI0R,KAAKQ,KAAKlS,GACdC,EAAIyR,KAAKQ,KAAKjS,GACLC,EAAa,EAAGA,EAAa7B,KAAK+B,OAAO+C,OAAQjD,IAEtD,GADImH,EAAOhJ,KAAKuJ,aAAa7H,uBAAuBC,EAAGC,EAAGC,GAEtD,MAAOmH,EAGf,OAAO,OAEXpD,kBAAmB,SAAUH,GACzBA,EAAKgO,SAASzT,KAAKuB,SACnBvB,KAAK8T,YAAc9T,KAAK8T,YAAYC,IAAItO,IAE5CuO,kBAAmB,SAAUnR,EAAOhB,EAAYqQ,EAAWE,GAAxC,GAEXtQ,GACAkI,EACK/G,CAAT,KAHAjD,KAAKiU,oBACDnS,EAAQ9B,KAAK+B,OAAOF,GACpBmI,EAASlI,EAAMkI,OAAOkI,EAAWE,GAAS,EAAMvP,EAAM8L,UACjD1L,EAAa,EAAGA,EAAa+G,EAAOlF,OAAQ7B,IACjDjD,KAAKuJ,aAAaxE,kBAAkBiF,EAAO/G,GAE/CjD,MAAK8T,YAAY1D,KAAK,gCAAgChJ,KAAK,IAC3DpH,KAAK8T,YAAYI,QAAQ9N,SAAS,WAAWgK,KAAK,gBAAgBhJ,KAAKhJ,EAAM+V,SAAS/V,EAAMgW,SAASC,YAAYnC,GAAY,SAC7HlS,KAAK8T,YAAYQ,OAAOlO,SAAS,UAAUgK,KAAK,mBAAmBhJ,KAAKhJ,EAAM+V,SAAS/V,EAAMgW,SAASC,YAAYjC,GAAU,UAEhImC,gBAAiB,SAAU1R,EAAOhB,EAAY2S,GAA7B,GAMJvR,GALLtC,EAAQvC,EAAMZ,KAAK2U,UAAUtP,EAAMlC,OAAS6T,EAC5CxR,EAAMrC,EAAQkC,EAAM4R,WACpB3S,EAAQ9B,KAAK+B,OAAOF,GACpBmI,EAASlI,EAAMkI,OAAOrJ,EAAOqC,GAAK,EAAMH,EAAM8L,SAElD,KADA3O,KAAK0U,gBAAgB7R,EAAMuL,KAClBnL,EAAa,EAAGA,EAAa+G,EAAOlF,OAAQ7B,IACjDjD,KAAKuJ,aAAa1D,gBAAgBmE,EAAO/G,GAAaJ,IAG9DyD,gBAAiB,SAAUb,GACvBA,EAAKgO,SAASzT,KAAKuB,SACnBvB,KAAK2U,UAAY3U,KAAK2U,UAAUZ,IAAItO,IAExCmE,QAAS,WAAA,GAKIxM,GAJLwG,EAAa5D,KAAK0G,cAClB7C,EAAY7D,KAAKuB,QAAQ,GAAGqT,qBAAqB,MACjDpT,EAAYxB,KAAKwB,WAErB,KADAxB,KAAK+B,UACI3E,EAAM,EAAGA,EAAMwG,EAAYxG,IAChC4C,KAAK6U,iBAAiBzX,EAE1B4C,MAAKuJ,aAAa5F,uBAAuBC,EAAYC,EAAWrC,IAEpEmD,WAAY,SAAUX,EAAYK,EAAM7C,EAAWf,GAAvC,GAUJ8R,GATA/M,EAAenB,EAAKmB,aACpBQ,EAAmB3B,EAAKI,SAASK,OAAST,EAAKI,SAAS,GAAGyE,aAAe,EAAI,EAC9EvI,EAAQvC,EAAMZ,KAAK6D,QAAQG,EAAWf,GACtCuC,EAAM5E,EAAMZ,KAAKc,UACjBkD,GAAUzD,aAAe4C,EAAM5C,aAC/BiF,IAAQxB,EAAUzD,WAAa4C,EAAM5C,YAAcK,EAAMZ,KAAKsX,aAElEnU,EAAQvC,EAAMZ,KAAK2U,UAAUxR,GAC7BqC,GAAOrC,EACH4R,EAAac,KAAK0B,OAAOvP,EAAeQ,EAAmBhG,KAAKiG,QAAQ+O,mBAAqBhV,KAAKiG,QAAQC,YAAc,IAC5H7B,EAAKK,aAAa,OAAQ,YAC1BL,EAAKK,aAAa,iBAAiB,GACnCV,EAAWW,WAAWN,EAAM1D,EAAOqC,EAAKuP,IAE5C0C,OAAQ,SAAUzJ,GACdxL,KAAKuB,QAAQkD,SAAS,+CAA+CyQ,SACrElV,KAAK4J,UACL4B,EAAS,GAAIpN,GAAM8I,KAAKiO,MAAM3J,GAAQ4J,OAE9B1N,MAAO,QACP2N,IAAK,QAGL3N,MAAO,MACP2N,IAAK,UAEVC,SACH,IAAI7T,GAAYzB,KAAKkQ,gBACjBzO,GAAUqD,OACV9E,KAAKuV,cAAc/J,EAAQ/J,EAAW,EAAG,GAEzCzB,KAAKwV,cAAchK,EAAQ,GAE/BxL,KAAKwN,gBACLxN,KAAK+N,QAAQ,aAEjByH,cAAe,SAAUhK,EAAQ3J,GAAlB,GACPgB,GACAzF,EACA0H,EACAhC,EACAnC,EACAqC,EAIQlB,EACA/B,EACAoD,EAIA6G,EACAjH,EAKI0S,EACAC,EACAtK,EACAC,EAEIlD,EAYClF,CAhCrB,KAAK7F,EAAM,EAAG0H,EAAS0G,EAAO1G,OAAQ1H,EAAM0H,EAAQ1H,IAEhD,GADAyF,EAAQ2I,EAAOpO,GACX4C,KAAK6R,cAAchP,GASnB,GARIf,EAAQ9B,KAAK+B,OAAOF,GACpB9B,EAAOC,KAAKuJ,aAAatJ,MACzBkD,EAAWpD,EAAKqD,YACftB,EAAMiR,oBACPjR,EAAMiR,sBAEN/I,EAASlI,EAAM6T,WAAW9S,GAAO,GACjCE,EAAaiH,EAAOlF,OACpB3B,EAQA,IAPAL,EAAQkH,EAAO,GACfrJ,EAAQmC,EAAMnC,MAAMA,MACpBqC,EAAMF,EAAME,IAAIA,IACZyS,EAAa,GAAI7X,MAAKkF,EAAMnC,MAAMA,OAClC+U,EAAW1L,EAAOA,EAAOlF,OAAS,GAAG9B,IAAIA,IACzCoI,EAAW,GAAIxN,MAAK6X,GACpBpK,EAAS,GAAIzN,MAAKoF,GACfyS,EAAW7E,WAAa8E,GAAY7S,EAAMG,KAAO5E,EAAMgW,SAASC,YAAYoB,IAAe5S,EAAMlC,OAASvC,EAAMgW,SAASC,YAAYqB,IACpIvN,EAAYrG,EAAMyG,cAAc6C,EAASwF,UAAWvF,EAAOuF,WAAW,GAAM,GAChFvF,EAAOuK,QAAQvK,EAAOlM,UAAY,GAClCiM,EAASwK,QAAQxK,EAASjM,UAAY,GAClCgJ,IACAA,EAAU5E,KAAO,KACjB4E,EAAU0N,OAAS,KACnB1N,EAAU3E,KAAO,KACjBxD,KAAKuJ,aAAa3G,eAAeC,EAAOf,EAAOqG,EAAW,EAAGxH,EAAOqC,EAAK,IAE7EyS,EAAarX,EAAMZ,KAAK6D,QAAQoU,EAAY,OAGhD,KAASxS,EAAa,EAAGA,EAAaF,EAAYE,IAC9CH,EAAQkH,EAAO/G,GACftC,EAAQkC,EAAMlC,MACdqC,EAAMH,EAAMG,IACZhD,KAAKuJ,aAAa3G,eAAeC,EAAOf,EAAOgB,EAAOC,EAAYpC,EAAOqC,EAAKC,IAMlGsS,cAAe,SAAU/J,EAAQ/J,EAAWkM,EAAQgC,GAArC,GAGH5P,GACK+V,EACDtX,EACAuX,EALR/O,EAAWvF,EAAU,EACzB,IAAIuF,EAEA,IADIjH,EAAOiH,EAASG,WAAWpH,OACtB+V,EAAU,EAAGA,EAAU/V,EAAK+E,OAAQgR,IACrCtX,EAAQwB,KAAKgW,eAAehP,EAAUjH,EAAK+V,IAC3CC,EAAM,GAAI3X,GAAM8I,KAAKiO,MAAM3J,GAAQ8D,QACnC5H,MAAOV,EAASU,MAChBuO,SAAUjX,EAAckX,cAAc1X,KACvC8W,UACC7T,EAAUqD,OAAS,EACnB6I,EAAS3N,KAAKuV,cAAcQ,EAAKtU,EAAUpE,MAAM,GAAIsQ,IAAUgC,EAAc,GAE7E3P,KAAKwV,cAAcO,EAAKpI,IAIpC,OAAOA,IAEXjH,YAAa,WAAA,GACLjF,GAAYzB,KAAKkQ,iBACjBD,EAAcjQ,KAAKuJ,YACvB,OAAI9H,GAAUqD,OACN9E,KAAKiB,uBACEgP,EAAYxN,oBAAoBhB,EAAUqD,OAAS,GAEnDmL,EAAYvN,sBAAsBjB,EAAUqD,QAGpD,GAEXnC,yBAA0B,SAAUwD,GAChC,MAAOnG,MAAKM,qBAAqB6F,GAASnG,KAAKM,qBAAqB6F,EAAQ,IAEhFiL,QAAS,WACDpR,KAAKmW,OACLnW,KAAKmW,MAAMC,YAAY,yBAEvBpW,KAAKuB,SACLvB,KAAKuB,QAAQ8U,IAAIpX,GAEjBe,KAAKqJ,SACLrJ,KAAKqJ,QAAQgN,IAAIpX,GAErBD,EAAc0G,GAAG0L,QAAQzL,KAAK3F,MAC1BA,KAAKoD,aAAepD,KAAKiG,QAAQgI,UAC7BjO,KAAKiG,QAAQgI,SAASM,UAAW,GACjCvO,KAAKiP,eAAemC,WAIhC5F,QACI,SACA,MACA,OACA,YAEJvF,SACI0B,MAAO,QACPC,KAAM,QACN1B,YAAa,GACb8O,iBAAkB,GAClB/G,UAAU,EACV1B,mBAAoB,QACpBI,wBAAyB,QACzBE,yBAA0B,WAC1BxK,oBAAqB,UACrB+J,YAAa7M,EACb2M,cAAexM,MAkBzBZ,OAAOV,MAAMkY,QACRxX,OAAOV,OACE,kBAAVpB,SAAwBA,OAAOuZ,IAAMvZ,OAAS,SAAUwZ,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.scheduler.monthview.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.scheduler.monthview', ['kendo.scheduler.view'], f);\n}(function () {\n    var __meta__ = {\n        id: 'scheduler.monthview',\n        name: 'Scheduler Month View',\n        category: 'web',\n        description: 'The Scheduler Month View',\n        depends: ['scheduler.view'],\n        hidden: true\n    };\n    (function ($) {\n        var kendo = window.kendo, ui = kendo.ui, SchedulerView = ui.SchedulerView, NS = '.kendoMonthView', extend = $.extend, getDate = kendo.date.getDate, MS_PER_DAY = kendo.date.MS_PER_DAY, NUMBER_OF_ROWS = 6, NUMBER_OF_COLUMNS = 7, INVERSE_COLOR_CLASS = 'k-event-inverse', DAY_TEMPLATE = kendo.template('<span class=\"k-link k-nav-day\">#:kendo.toString(date, \"dd\")#</span>'), EVENT_WRAPPER_STRING = '<div role=\"gridcell\" aria-selected=\"false\" data-#=ns#uid=\"#=uid#\"' + '#if (resources[0]) { #' + 'style=\"background-color:#=resources[0].color #; border-color: #=resources[0].color#\"' + 'class=\"k-event\"' + '#} else {#' + 'class=\"k-event\"' + '#}#' + '>' + '<span class=\"k-event-actions\">' + '# if(data.tail || data.middle) {#' + '<span class=\"k-icon k-i-arrow-60-left\"></span>' + '#}#' + '# if(data.isException()) {#' + '<span class=\"k-icon k-i-non-recurrence\"></span>' + '# } else if(data.isRecurring()) {#' + '<span class=\"k-icon k-i-reload\"></span>' + '#}#' + '</span>' + '{0}' + '<span class=\"k-event-actions\">' + '#if (showDelete) {#' + '<a href=\"\\\\#\" class=\"k-link k-event-delete\" title=\"${data.messages.destroy}\" aria-label=\"${data.messages.destroy}\"><span class=\"k-icon k-i-close\"></span></a>' + '#}#' + '# if(data.head || data.middle) {#' + '<span class=\"k-icon k-i-arrow-60-right\"></span>' + '#}#' + '</span>' + '# if(resizable && !data.tail && !data.middle) {#' + '<span class=\"k-resize-handle k-resize-w\"></span>' + '#}#' + '# if(resizable && !data.head && !data.middle) {#' + '<span class=\"k-resize-handle k-resize-e\"></span>' + '#}#' + '</div>', EVENT_TEMPLATE = kendo.template('<div title=\"#=title.replace(/\"/g,\"&\\\\#34;\")#\">' + '<div class=\"k-event-template\">#:title#</div>' + '</div>');\n        var MORE_BUTTON_TEMPLATE = kendo.template('<div style=\"width:#=width#px;left:#=left#px;top:#=top#px\" class=\"k-more-events k-button\"><span>...</span></div>');\n        var MonthGroupedView = kendo.Class.extend({\n            init: function (view) {\n                this._view = view;\n            },\n            _verticalRowCountForLevel: function (level) {\n                var view = this._view;\n                return view._rowCountForLevel(level);\n            },\n            _horizontalGroupCountForLevel: function (level) {\n                var view = this._view;\n                return view._columnCountForLevel(level);\n            },\n            _getCalendarRowsLength: function (cellsPerRow, cellCount) {\n                return cellCount / cellsPerRow;\n            },\n            _createRows: function (start, startIdx, horizontalGroupCount, verticalGroupIndex) {\n                var view = this._view;\n                var cellsPerRow = NUMBER_OF_COLUMNS;\n                var isVerticallyGrouped = view._isVerticallyGrouped();\n                var html = '';\n                for (var groupIdx = 0; groupIdx < horizontalGroupCount; groupIdx++) {\n                    html += view._createRow(start, startIdx, cellsPerRow, isVerticallyGrouped ? verticalGroupIndex : groupIdx);\n                }\n                return html;\n            },\n            _adjustStartDate: function (start) {\n                return kendo.date.addDays(start, NUMBER_OF_COLUMNS);\n            },\n            _getContent: function (content, startDate, resources) {\n                return content({\n                    date: startDate,\n                    resources: resources\n                });\n            },\n            _getTimeSlotByPosition: function (x, y, groupIndex) {\n                var group = this._view.groups[groupIndex];\n                return group.daySlotByPosition(x, y);\n            },\n            _nextSlotStartDate: function (startDate) {\n                return kendo.date.nextDay(startDate);\n            },\n            _createRowsLayout: function (resources, rows, groupHeaderTemplate) {\n                var view = this._view;\n                return view._createRowsLayout(resources, rows, groupHeaderTemplate);\n            },\n            _createVerticalColumnsLayout: function (resources, rows, groupHeaderTemplate, columns) {\n                return columns;\n            },\n            _createColumnsLayout: function (resources, columns, groupHeaderTemplate) {\n                var view = this._view;\n                return view._createColumnsLayout(resources, columns, groupHeaderTemplate);\n            },\n            _verticalGroupCount: function (level) {\n                var view = this._view;\n                return view._rowCountForLevel(level);\n            },\n            _horizontalGroupCount: function (level) {\n                var view = this._view;\n                return view._columnCountForLevel(level) / view._columnOffsetForResource(level);\n            },\n            _positionEvent: function (event, group, range, rangeCount, start, end, rangeIndex) {\n                var view = this._view;\n                var isMobile = view._isMobile();\n                if (rangeCount > 1) {\n                    if (rangeIndex === 0) {\n                        end = range.end.endDate();\n                    } else if (rangeIndex == rangeCount - 1) {\n                        start = range.start.startDate();\n                    } else {\n                        start = range.start.startDate();\n                        end = range.end.endDate();\n                    }\n                }\n                var occurrence = event.clone({\n                    start: start,\n                    end: end,\n                    head: range.head,\n                    tail: range.tail\n                });\n                if (isMobile) {\n                    view._positionMobileEvent(range, view._createEventElement(occurrence), group);\n                } else {\n                    view._positionEvent(range, view._createEventElement(occurrence), group);\n                }\n            },\n            _addDaySlotCollections: function (groupCount, tableRows, startDate) {\n                var view = this._view;\n                var columnCount = NUMBER_OF_COLUMNS;\n                var rowCount = NUMBER_OF_ROWS;\n                for (var groupIndex = 0; groupIndex < groupCount; groupIndex++) {\n                    var cellCount = 0;\n                    var rowMultiplier = 0;\n                    if (view._isVerticallyGrouped()) {\n                        rowMultiplier = groupIndex;\n                    }\n                    for (var rowIndex = rowMultiplier * rowCount; rowIndex < (rowMultiplier + 1) * rowCount; rowIndex++) {\n                        var group = view.groups[groupIndex];\n                        var collection = group.addDaySlotCollection(kendo.date.addDays(startDate, cellCount), kendo.date.addDays(startDate, cellCount + columnCount));\n                        var tableRow = tableRows[rowIndex];\n                        var cells = tableRow.children;\n                        var cellMultiplier = 0;\n                        tableRow.setAttribute('role', 'row');\n                        if (!view._isVerticallyGrouped()) {\n                            cellMultiplier = groupIndex;\n                        }\n                        for (var cellIndex = cellMultiplier * columnCount; cellIndex < (cellMultiplier + 1) * columnCount; cellIndex++) {\n                            var cell = cells[cellIndex];\n                            view.addDaySlot(collection, cell, startDate, cellCount);\n                            cellCount++;\n                        }\n                    }\n                }\n            },\n            _changePeriodGroupIndex: function (reverse) {\n                var view = this._view;\n                return reverse ? view.groups.length - 1 : 0;\n            },\n            _createResizeHint: function (range) {\n                var view = this._view;\n                var left = range.startSlot().offsetLeft;\n                var top = range.start.offsetTop;\n                var width = range.innerWidth();\n                var height = range.start.clientHeight - 2;\n                var hint = SchedulerView.fn._createResizeHint.call(view, left, top, width, height);\n                view._appendResizeHint(hint);\n            },\n            _createMoveHint: function (range, event) {\n                var view = this._view;\n                var startSlot = range.startSlot();\n                var endSlot = range.endSlot();\n                var hint = view._createEventElement(event.clone({\n                    head: range.head,\n                    tail: range.tail\n                }));\n                hint.css({\n                    left: startSlot.offsetLeft + 2,\n                    top: startSlot.offsetTop + startSlot.firstChildHeight,\n                    height: view.options.eventHeight,\n                    width: range.innerWidth() - (startSlot.index !== endSlot.index ? 5 : 4)\n                });\n                hint.addClass('k-event-drag-hint');\n                if (event.inverseColor) {\n                    hint.addClass(INVERSE_COLOR_CLASS);\n                }\n                view._appendMoveHint(hint);\n            }\n        });\n        var MonthGroupedByDateView = kendo.Class.extend({\n            init: function (view) {\n                this._view = view;\n            },\n            _verticalRowCountForLevel: function () {\n                return 1;\n            },\n            _horizontalGroupCountForLevel: function (level) {\n                var view = this._view;\n                return view._columnCountForLevel(level + 1) / NUMBER_OF_COLUMNS;\n            },\n            _createRows: function (start, startIdx, horizontalGroupCount) {\n                var view = this._view;\n                var cellsPerRow = NUMBER_OF_COLUMNS;\n                var isVerticallyGrouped = view._isVerticallyGrouped();\n                var html = '';\n                var dateIdx = 0;\n                if (isVerticallyGrouped) {\n                    var verticalStart = new Date(start);\n                    var groupCount = view._groupCount();\n                    for (dateIdx; dateIdx < NUMBER_OF_ROWS; dateIdx++) {\n                        html += view._createRow(verticalStart, startIdx, groupCount, dateIdx);\n                        verticalStart = kendo.date.addDays(verticalStart, cellsPerRow);\n                    }\n                    start = kendo.date.nextDay(start);\n                } else {\n                    for (dateIdx; dateIdx < cellsPerRow; dateIdx++) {\n                        html += view._createRow(start, startIdx, horizontalGroupCount, dateIdx);\n                        start = kendo.date.nextDay(start);\n                    }\n                    start = kendo.date.addDays(start, cellsPerRow);\n                }\n                return html;\n            },\n            _adjustStartDate: function (start, isLastRow) {\n                var view = this._view;\n                var isVerticallyGrouped = view._isVerticallyGrouped();\n                if (isVerticallyGrouped) {\n                    if (isLastRow) {\n                        return kendo.date.addDays(start, NUMBER_OF_COLUMNS * (NUMBER_OF_ROWS - 1) + 1);\n                    } else {\n                        return kendo.date.nextDay(start);\n                    }\n                }\n                return kendo.date.addDays(start, NUMBER_OF_COLUMNS);\n            },\n            _getContent: function (content, startDate, resources, cellIdx) {\n                if (cellIdx === 0) {\n                    return content({\n                        date: startDate,\n                        resources: resources\n                    });\n                }\n                return '';\n            },\n            _getTimeSlotByPosition: function (x, y, groupIndex) {\n                var group = this._view.groups[groupIndex];\n                return group.daySlotByPosition(x, y, true);\n            },\n            _nextSlotStartDate: function (startDate) {\n                return startDate;\n            },\n            _getCalendarRowsLength: function () {\n                var view = this._view;\n                var isVerticallyGrouped = view._isVerticallyGrouped();\n                return isVerticallyGrouped ? NUMBER_OF_COLUMNS : NUMBER_OF_ROWS;\n            },\n            _createRowsLayout: function (resources, rows, groupHeaderTemplate, columns) {\n                var view = this._view;\n                return view._createDateLayout(columns, null, false);\n            },\n            _createVerticalColumnsLayout: function (resources, rows, groupHeaderTemplate) {\n                var view = this._view;\n                var resource = resources[0];\n                var configuration = [];\n                var data = resource.dataSource.view();\n                for (var dataIndex = 0; dataIndex < data.length * NUMBER_OF_ROWS; dataIndex++) {\n                    var obj = {\n                        text: groupHeaderTemplate({\n                            text: kendo.htmlEncode(kendo.getter(resource.dataTextField)(data[dataIndex % data.length])),\n                            color: kendo.getter(resource.dataColorField)(data[dataIndex % data.length]),\n                            field: resource.field,\n                            title: resource.title,\n                            name: resource.name,\n                            value: kendo.getter(resource.dataValueField)(data[dataIndex % data.length])\n                        }),\n                        className: 'k-slot-cell'\n                    };\n                    obj.columns = view._createColumnsLayout(resources.slice(1), null, groupHeaderTemplate);\n                    configuration.push(obj);\n                }\n                return configuration;\n            },\n            _createColumnsLayout: function (resources, columns, groupHeaderTemplate, subColumns) {\n                var view = this._view;\n                return view._createColumnsLayout(resources, columns, groupHeaderTemplate, subColumns, true);\n            },\n            _verticalGroupCount: function (level) {\n                var view = this._view;\n                return view._columnCountForLevel(level) / NUMBER_OF_ROWS;\n            },\n            _horizontalGroupCount: function (level) {\n                var view = this._view;\n                return view._columnCountForLevel(level) / NUMBER_OF_COLUMNS;\n            },\n            _positionEvent: function (event, group, range, rangeCount, start, end) {\n                var view = this._view;\n                var startIndex = range.start.index;\n                var endIndex = range.end.index;\n                var isMobile = view._isMobile();\n                for (var i = range.start.index; i <= range.end.index; i++) {\n                    var currentSlot = range.collection._slots[i];\n                    var dateRange = group.daySlotRanges(currentSlot.start, currentSlot.start, true)[0];\n                    var occurrence = event.clone({\n                        start: i === startIndex ? start : currentSlot.startDate(),\n                        end: i === endIndex ? end : currentSlot.endDate(),\n                        head: i !== endIndex || range.head,\n                        tail: i !== startIndex || range.tail\n                    });\n                    if (isMobile) {\n                        view._positionMobileEvent(dateRange, view._createEventElement(occurrence), group);\n                    } else {\n                        view._positionEvent(dateRange, view._createEventElement(occurrence), group);\n                    }\n                }\n            },\n            _addDaySlotCollections: function (groupCount, tableRows, startDate) {\n                var view = this._view;\n                var columnCount = NUMBER_OF_COLUMNS;\n                var rowCount = NUMBER_OF_ROWS;\n                var isVerticallyGrouped = view._isVerticallyGrouped();\n                for (var dateIndex = 0; dateIndex < columnCount; dateIndex++) {\n                    for (var rowIndex = 0; rowIndex < rowCount; rowIndex++) {\n                        var groupIndex = 0;\n                        var currentTableIndex = isVerticallyGrouped ? dateIndex : rowIndex;\n                        var tableRow = tableRows[currentTableIndex];\n                        var cells = tableRow.children;\n                        var cellMultiplier = 0;\n                        tableRow.setAttribute('role', 'row');\n                        if (!view._isVerticallyGrouped()) {\n                            cellMultiplier = dateIndex;\n                        }\n                        for (var cellIndex = cellMultiplier * groupCount; cellIndex < (cellMultiplier + 1) * groupCount; cellIndex++) {\n                            var cellCount = rowIndex * columnCount + dateIndex;\n                            var currentCellIndex = isVerticallyGrouped ? cellIndex + rowIndex * groupCount : cellIndex;\n                            var cell = cells[currentCellIndex];\n                            var currentGroupIndex = isVerticallyGrouped ? cellIndex : groupIndex;\n                            var group = view.groups[currentGroupIndex];\n                            var collection;\n                            if (dateIndex === 0) {\n                                collection = group.addDaySlotCollection(kendo.date.addDays(startDate, cellCount), kendo.date.addDays(startDate, cellCount + columnCount));\n                            } else {\n                                collection = group._daySlotCollections[rowIndex];\n                            }\n                            view.addDaySlot(collection, cell, startDate, cellCount);\n                            groupIndex++;\n                        }\n                    }\n                }\n            },\n            _changePeriodGroupIndex: function (reverse, vertical, selectionGroupIndex) {\n                var view = this._view;\n                if (vertical && view._isVerticallyGrouped()) {\n                    return reverse ? view.groups.length - 1 : 0;\n                }\n                return selectionGroupIndex;\n            },\n            _createResizeHint: function (range) {\n                var view = this._view;\n                var left, top, width, height, hint;\n                if (view._isVerticallyGrouped()) {\n                    left = range.startSlot().offsetLeft;\n                    top = range.start.offsetTop;\n                    width = range.startSlot().offsetWidth;\n                    height = range.endSlot().offsetTop + range.startSlot().offsetHeight - range.startSlot().offsetTop - 2;\n                    hint = SchedulerView.fn._createResizeHint.call(view, left, top, width, height);\n                    view._appendResizeHint(hint);\n                } else {\n                    for (var slotIdx = range.startSlot().index; slotIdx <= range.endSlot().index; slotIdx++) {\n                        var slot = range.collection._slots[slotIdx];\n                        left = slot.offsetLeft;\n                        top = slot.offsetTop;\n                        width = slot.offsetWidth;\n                        height = slot.offsetHeight - 2;\n                        hint = SchedulerView.fn._createResizeHint.call(view, left, top, width, height);\n                        view._appendResizeHint(hint);\n                    }\n                }\n            },\n            _createMoveHint: function (range, event) {\n                var view = this._view;\n                var startSlot = range.startSlot();\n                var endSlot = range.endSlot();\n                for (var slotIdx = startSlot.index; slotIdx <= endSlot.index; slotIdx++) {\n                    var slot = range.collection._slots[slotIdx];\n                    var hint = view._createEventElement(event.clone({\n                        head: range.head,\n                        tail: range.tail\n                    }));\n                    hint.css({\n                        left: slot.offsetLeft,\n                        top: slot.offsetTop + slot.firstChildHeight,\n                        height: view.options.eventHeight,\n                        width: slot.offsetWidth - 2\n                    });\n                    hint.addClass('k-event-drag-hint');\n                    if (event.inverseColor) {\n                        hint.addClass(INVERSE_COLOR_CLASS);\n                    }\n                    view._appendMoveHint(hint);\n                }\n            }\n        });\n        kendo.ui.scheduler.MonthGroupedView = MonthGroupedView;\n        kendo.ui.scheduler.MonthGroupedByDateView = MonthGroupedByDateView;\n        ui.MonthView = SchedulerView.extend({\n            init: function (element, options) {\n                var that = this;\n                SchedulerView.fn.init.call(that, element, options);\n                that._groupedView = that._getGroupedView();\n                that.title = that.options.title;\n                that._templates();\n                that._editable();\n                that._renderLayout(that.options.date);\n                that._groups();\n            },\n            name: 'month',\n            _getGroupedView: function () {\n                if (this._isGroupedByDate()) {\n                    return new kendo.ui.scheduler.MonthGroupedByDateView(this);\n                } else {\n                    return new kendo.ui.scheduler.MonthGroupedView(this);\n                }\n            },\n            _updateDirection: function (selection, ranges, multiple, reverse, vertical) {\n                if (multiple) {\n                    var startSlot = ranges[0].start;\n                    var endSlot = ranges[ranges.length - 1].end;\n                    var isSameSlot = startSlot.index === endSlot.index;\n                    var isSameCollection = startSlot.collectionIndex === endSlot.collectionIndex;\n                    var updateDirection;\n                    if (vertical) {\n                        updateDirection = isSameSlot && isSameCollection || isSameCollection;\n                    } else {\n                        updateDirection = isSameSlot && isSameCollection;\n                    }\n                    if (updateDirection) {\n                        selection.backward = reverse;\n                    }\n                }\n            },\n            _changeDate: function (selection, slot, previous) {\n                var group = this.groups[selection.groupIndex];\n                var collections, index;\n                if (previous) {\n                    collections = group._getCollections(group.daySlotCollectionCount());\n                    index = slot.collectionIndex - 1;\n                    if (index >= 0) {\n                        return collections[index]._slots[collections[index]._slots.length - 1];\n                    }\n                } else {\n                    collections = group._getCollections(group.daySlotCollectionCount());\n                    index = slot.collectionIndex + 1;\n                    var slotIndex = 0;\n                    if (collections[index] && collections[index]._slots[slotIndex]) {\n                        return collections[index]._slots[slotIndex];\n                    }\n                }\n            },\n            _getNextHorizontalRange: function (group, method, horizontalRange) {\n                var isVertical = this._isVerticallyGrouped();\n                horizontalRange.startSlot = group[method](horizontalRange.startSlot, isVertical);\n                horizontalRange.endSlot = group[method](horizontalRange.endSlot, isVertical);\n                return horizontalRange;\n            },\n            _getNextVerticalRange: function (group, method, verticalRange, multiple) {\n                var isVertical = this._isVerticallyGrouped() && this._isGroupedByDate();\n                verticalRange.startSlot = group[method](verticalRange.startSlot, multiple, isVertical);\n                verticalRange.endSlot = group[method](verticalRange.endSlot, multiple, isVertical);\n                return verticalRange;\n            },\n            _changeViewPeriod: function (selection, reverse, vertical) {\n                var pad = vertical ? 7 : 1;\n                var newStart, newEnd;\n                if (reverse) {\n                    pad *= -1;\n                }\n                newStart = kendo.date.addDays(selection.start, pad);\n                newEnd = kendo.date.addDays(selection.end, pad);\n                if (this._isInRange(newStart, newEnd)) {\n                    return false;\n                }\n                selection.start = newStart;\n                selection.end = newEnd;\n                if (!vertical || vertical && this._isVerticallyGrouped()) {\n                    selection.groupIndex = this._groupedView._changePeriodGroupIndex(reverse, vertical, selection.groupIndex);\n                }\n                selection.events = [];\n                return true;\n            },\n            _continuousSlot: function (selection, ranges, reverse) {\n                var index = selection.backward ? 0 : ranges.length - 1;\n                var group = this.groups[selection.groupIndex];\n                return group.continuousSlot(ranges[index].start, reverse);\n            },\n            _changeGroupContinuously: function (selection, continuousSlot, multiple, reverse) {\n                if (!multiple) {\n                    var groupIndex = selection.groupIndex;\n                    var lastGroupIndex = this.groups.length - 1;\n                    var vertical = this._isVerticallyGrouped();\n                    var group = this.groups[groupIndex];\n                    if (!continuousSlot && vertical) {\n                        continuousSlot = group[reverse ? 'lastSlot' : 'firstSlot']();\n                        groupIndex += reverse ? -1 : 1;\n                    } else if (continuousSlot && !vertical) {\n                        groupIndex = reverse ? lastGroupIndex : 0;\n                    }\n                    if (groupIndex < 0 || groupIndex > lastGroupIndex) {\n                        groupIndex = reverse ? lastGroupIndex : 0;\n                        continuousSlot = null;\n                    }\n                    selection.groupIndex = groupIndex;\n                }\n                return continuousSlot;\n            },\n            _normalizeHorizontalSelection: function (selection, ranges, reverse) {\n                var slot;\n                if (reverse) {\n                    slot = ranges[0].start;\n                } else {\n                    slot = ranges[ranges.length - 1].end;\n                }\n                return slot;\n            },\n            _normalizeVerticalSelection: function (selection, ranges) {\n                var slot;\n                if (selection.backward) {\n                    slot = ranges[0].start;\n                } else {\n                    slot = ranges[ranges.length - 1].end;\n                }\n                return slot;\n            },\n            _templates: function () {\n                var options = this.options, settings = extend({}, kendo.Template, options.templateSettings);\n                this.eventTemplate = this._eventTmpl(options.eventTemplate, EVENT_WRAPPER_STRING);\n                this.dayTemplate = kendo.template(options.dayTemplate, settings);\n                this.groupHeaderTemplate = kendo.template(options.groupHeaderTemplate, settings);\n            },\n            dateForTitle: function () {\n                return kendo.format(this.options.selectedDateFormat, this._firstDayOfMonth, this._lastDayOfMonth);\n            },\n            shortDateForTitle: function () {\n                return kendo.format(this.options.selectedShortDateFormat, this._firstDayOfMonth, this._lastDayOfMonth);\n            },\n            mobileDateForTitle: function () {\n                return kendo.format(this.options.selectedMobileDateFormat, this._firstDayOfMonth, this._lastDayOfMonth);\n            },\n            nextDate: function () {\n                return kendo.date.nextDay(this._lastDayOfMonth);\n            },\n            previousDate: function () {\n                return kendo.date.previousDay(this._firstDayOfMonth);\n            },\n            startDate: function () {\n                return this._startDate;\n            },\n            endDate: function () {\n                return this._endDate;\n            },\n            _renderLayout: function (date) {\n                var that = this;\n                this._firstDayOfMonth = kendo.date.firstDayOfMonth(date);\n                this._lastDayOfMonth = kendo.date.lastDayOfMonth(date);\n                this._startDate = firstVisibleMonthDay(date, this.calendarInfo());\n                this.createLayout(this._layout());\n                this._content();\n                this.refreshLayout();\n                this.content.on('click' + NS, '.k-nav-day,.k-more-events', function (e) {\n                    var offset = $(e.currentTarget).offset();\n                    var slot = that._slotByPosition(offset.left, offset.top);\n                    e.preventDefault();\n                    that.trigger('navigate', {\n                        view: 'day',\n                        date: slot.startDate()\n                    });\n                });\n                this._footer();\n            },\n            _editable: function () {\n                if (this.options.editable) {\n                    if (this._isMobile()) {\n                        this._touchEditable();\n                    } else {\n                        this._mouseEditable();\n                    }\n                }\n            },\n            _mouseEditable: function () {\n                var that = this;\n                that.element.on('click' + NS, '.k-scheduler-monthview .k-event a:has(.k-i-close)', function (e) {\n                    that.trigger('remove', { uid: $(this).closest('.k-event').attr(kendo.attr('uid')) });\n                    e.preventDefault();\n                });\n                if (that.options.editable.create !== false) {\n                    that.element.on('dblclick' + NS, '.k-scheduler-monthview .k-scheduler-content td', function (e) {\n                        var offset = $(e.currentTarget).offset();\n                        var slot = that._slotByPosition(offset.left, offset.top);\n                        if (slot) {\n                            var resourceInfo = that._resourceBySlot(slot);\n                            that.trigger('add', {\n                                eventInfo: extend({\n                                    isAllDay: true,\n                                    start: slot.startDate(),\n                                    end: slot.startDate()\n                                }, resourceInfo)\n                            });\n                        }\n                        e.preventDefault();\n                    });\n                }\n                if (that.options.editable.update !== false) {\n                    that.element.on('dblclick' + NS, '.k-scheduler-monthview .k-event', function (e) {\n                        that.trigger('edit', { uid: $(this).closest('.k-event').attr(kendo.attr('uid')) });\n                        e.preventDefault();\n                    });\n                }\n            },\n            _touchEditable: function () {\n                var that = this;\n                var threshold = 0;\n                if (kendo.support.mobileOS.android) {\n                    threshold = 5;\n                }\n                if (that.options.editable.create !== false) {\n                    that._addUserEvents = new kendo.UserEvents(that.element, {\n                        threshold: threshold,\n                        useClickAsTap: !kendo.support.browser.edge,\n                        filter: '.k-scheduler-monthview .k-scheduler-content td',\n                        tap: function (e) {\n                            if (that._scrolling) {\n                                return;\n                            }\n                            var offset = $(e.target).offset();\n                            var slot = that._slotByPosition(offset.left, offset.top);\n                            if (slot) {\n                                var resourceInfo = that._resourceBySlot(slot);\n                                that.trigger('add', {\n                                    eventInfo: extend({\n                                        isAllDay: true,\n                                        start: slot.startDate(),\n                                        end: slot.startDate()\n                                    }, resourceInfo)\n                                });\n                            }\n                            e.preventDefault();\n                        }\n                    });\n                }\n            },\n            selectionByElement: function (cell) {\n                var offset = $(cell).offset();\n                return this._slotByPosition(offset.left, offset.top);\n            },\n            _columnCountForLevel: function (level) {\n                var columnLevel = this.columnLevels[level];\n                return columnLevel ? columnLevel.length : 0;\n            },\n            _rowCountForLevel: function (level) {\n                var rowLevel = this.rowLevels[level];\n                return rowLevel ? rowLevel.length : 0;\n            },\n            _content: function () {\n                var html = '<tbody>';\n                var verticalGroupCount = 1;\n                var groupedView = this._groupedView;\n                var resources = this.groupedResources;\n                if (resources.length) {\n                    if (this._isVerticallyGrouped()) {\n                        verticalGroupCount = groupedView._verticalRowCountForLevel(resources.length - 1);\n                    }\n                }\n                for (var verticalGroupIdx = 0; verticalGroupIdx < verticalGroupCount; verticalGroupIdx++) {\n                    html += this._createCalendar(verticalGroupIdx);\n                }\n                html += '</tbody>';\n                this.content.find('table').html(html);\n            },\n            _createCalendar: function (verticalGroupIndex) {\n                var start = this.startDate();\n                var cellCount = NUMBER_OF_COLUMNS * NUMBER_OF_ROWS;\n                var cellsPerRow = NUMBER_OF_COLUMNS;\n                var weekStartDates = [start];\n                var html = '';\n                var horizontalGroupCount = 1;\n                var isVerticallyGrouped = this._isVerticallyGrouped();\n                var groupedView = this._groupedView;\n                var resources = this.groupedResources;\n                if (resources.length) {\n                    if (!isVerticallyGrouped) {\n                        horizontalGroupCount = groupedView._horizontalGroupCountForLevel(resources.length - 1);\n                    }\n                }\n                this._slotIndices = {};\n                var calendarRowsLength = groupedView._getCalendarRowsLength(cellsPerRow, cellCount);\n                for (var rowIdx = 0; rowIdx < calendarRowsLength; rowIdx++) {\n                    html += '<tr>';\n                    weekStartDates.push(start);\n                    var startIdx = rowIdx * cellsPerRow;\n                    html += groupedView._createRows(start, startIdx, horizontalGroupCount, verticalGroupIndex);\n                    start = groupedView._adjustStartDate(start, rowIdx === calendarRowsLength - 1);\n                    html += '</tr>';\n                }\n                this._weekStartDates = weekStartDates;\n                this._endDate = kendo.date.previousDay(start);\n                return html;\n            },\n            _createRow: function (startDate, startIdx, cellsPerRow, groupIndex) {\n                var that = this;\n                var min = that._firstDayOfMonth;\n                var max = that._lastDayOfMonth;\n                var content = that.dayTemplate;\n                var classes = '';\n                var html = '';\n                var groupedView = this._groupedView;\n                var resources = function () {\n                    return that._resourceBySlot({ groupIndex: groupIndex });\n                };\n                for (var cellIdx = 0; cellIdx < cellsPerRow; cellIdx++) {\n                    classes = '';\n                    if (kendo.date.isToday(startDate)) {\n                        classes += 'k-today';\n                    }\n                    if (!kendo.date.isInDateRange(startDate, min, max)) {\n                        classes += ' k-other-month';\n                    }\n                    html += '<td ';\n                    if (classes !== '') {\n                        html += 'class=\"' + classes + '\"';\n                    }\n                    html += '>';\n                    html += groupedView._getContent(content, startDate, resources, cellIdx);\n                    html += '</td>';\n                    that._slotIndices[getDate(startDate).getTime()] = startIdx + cellIdx;\n                    startDate = groupedView._nextSlotStartDate(startDate);\n                }\n                return html;\n            },\n            _layout: function () {\n                var calendarInfo = this.calendarInfo();\n                var weekDayNames = this._isMobile() ? calendarInfo.days.namesShort.map(function (name) {\n                    return name[0];\n                }) : calendarInfo.days.names;\n                var names = shiftArray(weekDayNames, calendarInfo.firstDay);\n                var columns = $.map(names, function (value) {\n                    return { text: value };\n                });\n                var resources = this.groupedResources;\n                var rows;\n                var groupedView = this._groupedView;\n                if (resources.length) {\n                    if (this._isVerticallyGrouped()) {\n                        var inner = [];\n                        for (var idx = 0; idx < 6; idx++) {\n                            inner.push({\n                                text: '<div>&nbsp;</div>',\n                                className: 'k-hidden k-slot-cell'\n                            });\n                        }\n                        rows = groupedView._createRowsLayout(resources, inner, this.groupHeaderTemplate, columns);\n                        columns = groupedView._createVerticalColumnsLayout(resources, inner, this.groupHeaderTemplate, columns);\n                    } else {\n                        columns = groupedView._createColumnsLayout(resources, columns, this.groupHeaderTemplate, columns);\n                    }\n                }\n                return {\n                    columns: columns,\n                    rows: rows\n                };\n            },\n            _createEventElement: function (event) {\n                var options = this.options;\n                var editable = options.editable;\n                var isMobile = this._isMobile();\n                event.showDelete = editable && editable.destroy !== false && !isMobile;\n                event.resizable = editable && editable.resize !== false && !isMobile;\n                event.ns = kendo.ns;\n                event.resources = this.eventResources(event);\n                event.inverseColor = false;\n                event.messages = options.messages || { destroy: 'Delete' };\n                var element = $(this.eventTemplate(event));\n                this.angular('compile', function () {\n                    return {\n                        elements: element,\n                        data: [{ dataItem: event }]\n                    };\n                });\n                return element;\n            },\n            _isInDateSlot: function (event) {\n                var groups = this.groups[0];\n                var slotStart = groups.firstSlot().start;\n                var slotEnd = groups.lastSlot().end - 1;\n                var startTime = kendo.date.toUtcTime(event.start);\n                var endTime = kendo.date.toUtcTime(event.end);\n                return (isInDateRange(startTime, slotStart, slotEnd) || isInDateRange(endTime, slotStart, slotEnd) || isInDateRange(slotStart, startTime, endTime) || isInDateRange(slotEnd, startTime, endTime)) && (!isInDateRange(endTime, slotStart, slotStart) || isInDateRange(endTime, startTime, startTime) || event.isAllDay);\n            },\n            _slotIndex: function (date) {\n                return this._slotIndices[getDate(date).getTime()];\n            },\n            _positionMobileEvent: function (slotRange, element, group) {\n                var startSlot = slotRange.start;\n                if (slotRange.start.offsetLeft > slotRange.end.offsetLeft) {\n                    startSlot = slotRange.end;\n                }\n                var startIndex = slotRange.start.index;\n                var endIndex = startIndex;\n                var eventCount = 3;\n                var events = SchedulerView.collidingEvents(slotRange.events(), startIndex, endIndex);\n                events.push({\n                    element: element,\n                    start: startIndex,\n                    end: endIndex\n                });\n                var rows = SchedulerView.createRows(events);\n                var slot = slotRange.collection.at(startIndex);\n                var container = slot.container;\n                if (!container) {\n                    container = $(kendo.format('<div class=\"k-events-container\" style=\"top:{0};left:{1};width:{2}\"/>', startSlot.offsetTop + startSlot.firstChildTop + startSlot.firstChildHeight + 'px', startSlot.offsetLeft + 'px', startSlot.offsetWidth + 'px'));\n                    slot.container = container;\n                    this.content[0].appendChild(container[0]);\n                }\n                if (rows.length <= eventCount) {\n                    slotRange.addEvent({\n                        element: element,\n                        start: startIndex,\n                        end: endIndex,\n                        groupIndex: startSlot.groupIndex\n                    });\n                    group._continuousEvents.push({\n                        element: element,\n                        uid: element.attr(kendo.attr('uid')),\n                        start: slotRange.start,\n                        end: slotRange.end\n                    });\n                    container[0].appendChild(element[0]);\n                }\n            },\n            _positionEvent: function (slotRange, element, group) {\n                var eventHeight = this.options.eventHeight;\n                var startSlot = slotRange.start;\n                if (slotRange.start.offsetLeft > slotRange.end.offsetLeft) {\n                    startSlot = slotRange.end;\n                }\n                var startIndex = slotRange.start.index;\n                var endIndex = slotRange.end.index;\n                var eventCount = startSlot.eventCount;\n                var events = SchedulerView.collidingEvents(slotRange.events(), startIndex, endIndex);\n                var rightOffset = startIndex !== endIndex ? 5 : 4;\n                events.push({\n                    element: element,\n                    start: startIndex,\n                    end: endIndex\n                });\n                var rows = SchedulerView.createRows(events);\n                for (var idx = 0, length = Math.min(rows.length, eventCount); idx < length; idx++) {\n                    var rowEvents = rows[idx].events;\n                    var eventTop = startSlot.offsetTop + startSlot.firstChildTop + startSlot.firstChildHeight + idx * eventHeight + 3 * idx + 'px';\n                    for (var j = 0, eventLength = rowEvents.length; j < eventLength; j++) {\n                        rowEvents[j].element[0].style.top = eventTop;\n                    }\n                }\n                if (rows.length > eventCount) {\n                    for (var slotIndex = startIndex; slotIndex <= endIndex; slotIndex++) {\n                        var collection = slotRange.collection;\n                        var slot = collection.at(slotIndex);\n                        if (slot.more) {\n                            continue;\n                        }\n                        slot.more = $(MORE_BUTTON_TEMPLATE({\n                            ns: kendo.ns,\n                            start: slotIndex,\n                            end: slotIndex,\n                            width: slot.clientWidth - 2,\n                            left: slot.offsetLeft + 2,\n                            top: slot.offsetTop + slot.firstChildTop + slot.firstChildHeight + eventCount * eventHeight + 3 * eventCount\n                        }));\n                        this.content[0].appendChild(slot.more[0]);\n                    }\n                } else {\n                    slotRange.addEvent({\n                        element: element,\n                        start: startIndex,\n                        end: endIndex,\n                        groupIndex: startSlot.groupIndex\n                    });\n                    element[0].style.width = slotRange.innerWidth() - rightOffset + 'px';\n                    element[0].style.left = startSlot.offsetLeft + 2 + 'px';\n                    element[0].style.height = eventHeight + 'px';\n                    group._continuousEvents.push({\n                        element: element,\n                        uid: element.attr(kendo.attr('uid')),\n                        start: slotRange.start,\n                        end: slotRange.end\n                    });\n                    element.appendTo(this.content);\n                    this._inverseEventColor(element);\n                }\n            },\n            _slotByPosition: function (x, y) {\n                var offset = this.content.offset();\n                x -= offset.left;\n                y -= offset.top;\n                y += this.content[0].scrollTop;\n                x += this.content[0].scrollLeft;\n                x = Math.ceil(x);\n                y = Math.ceil(y);\n                for (var groupIndex = 0; groupIndex < this.groups.length; groupIndex++) {\n                    var slot = this._groupedView._getTimeSlotByPosition(x, y, groupIndex);\n                    if (slot) {\n                        return slot;\n                    }\n                }\n                return null;\n            },\n            _appendResizeHint: function (hint) {\n                hint.appendTo(this.content);\n                this._resizeHint = this._resizeHint.add(hint);\n            },\n            _updateResizeHint: function (event, groupIndex, startTime, endTime) {\n                this._removeResizeHint();\n                var group = this.groups[groupIndex];\n                var ranges = group.ranges(startTime, endTime, true, event.isAllDay);\n                for (var rangeIndex = 0; rangeIndex < ranges.length; rangeIndex++) {\n                    this._groupedView._createResizeHint(ranges[rangeIndex]);\n                }\n                this._resizeHint.find('.k-label-top,.k-label-bottom').text('');\n                this._resizeHint.first().addClass('k-first').find('.k-label-top').text(kendo.toString(kendo.timezone.toLocalDate(startTime), 'M/dd'));\n                this._resizeHint.last().addClass('k-last').find('.k-label-bottom').text(kendo.toString(kendo.timezone.toLocalDate(endTime), 'M/dd'));\n            },\n            _updateMoveHint: function (event, groupIndex, distance) {\n                var start = kendo.date.toUtcTime(event.start) + distance;\n                var end = start + event.duration();\n                var group = this.groups[groupIndex];\n                var ranges = group.ranges(start, end, true, event.isAllDay);\n                this._removeMoveHint(event.uid);\n                for (var rangeIndex = 0; rangeIndex < ranges.length; rangeIndex++) {\n                    this._groupedView._createMoveHint(ranges[rangeIndex], event);\n                }\n            },\n            _appendMoveHint: function (hint) {\n                hint.appendTo(this.content);\n                this._moveHint = this._moveHint.add(hint);\n            },\n            _groups: function () {\n                var groupCount = this._groupCount();\n                var tableRows = this.content[0].getElementsByTagName('tr');\n                var startDate = this.startDate();\n                this.groups = [];\n                for (var idx = 0; idx < groupCount; idx++) {\n                    this._addResourceView(idx);\n                }\n                this._groupedView._addDaySlotCollections(groupCount, tableRows, startDate);\n            },\n            addDaySlot: function (collection, cell, startDate, cellCount) {\n                var clientHeight = cell.clientHeight;\n                var firstChildHeight = cell.children.length ? cell.children[0].offsetHeight + 3 : 0;\n                var start = kendo.date.addDays(startDate, cellCount);\n                var end = kendo.date.MS_PER_DAY;\n                if (startDate.getHours() !== start.getHours()) {\n                    end += (startDate.getHours() - start.getHours()) * kendo.date.MS_PER_HOUR;\n                }\n                start = kendo.date.toUtcTime(start);\n                end += start;\n                var eventCount = Math.floor((clientHeight - firstChildHeight - this.options.moreButtonHeight) / (this.options.eventHeight + 3));\n                cell.setAttribute('role', 'gridcell');\n                cell.setAttribute('aria-selected', false);\n                collection.addDaySlot(cell, start, end, eventCount);\n            },\n            render: function (events) {\n                this.content.children('.k-event,.k-more-events,.k-events-container').remove();\n                this._groups();\n                events = new kendo.data.Query(events).sort([\n                    {\n                        field: 'start',\n                        dir: 'asc'\n                    },\n                    {\n                        field: 'end',\n                        dir: 'desc'\n                    }\n                ]).toArray();\n                var resources = this.groupedResources;\n                if (resources.length) {\n                    this._renderGroups(events, resources, 0, 1);\n                } else {\n                    this._renderEvents(events, 0);\n                }\n                this.refreshLayout();\n                this.trigger('activate');\n            },\n            _renderEvents: function (events, groupIndex) {\n                var event;\n                var idx;\n                var length;\n                var range;\n                var start;\n                var end;\n                for (idx = 0, length = events.length; idx < length; idx++) {\n                    event = events[idx];\n                    if (this._isInDateSlot(event)) {\n                        var group = this.groups[groupIndex];\n                        var view = this._groupedView._view;\n                        var isMobile = view._isMobile();\n                        if (!group._continuousEvents) {\n                            group._continuousEvents = [];\n                        }\n                        var ranges = group.slotRanges(event, true);\n                        var rangeCount = ranges.length;\n                        if (isMobile) {\n                            range = ranges[0];\n                            start = range.start.start;\n                            end = range.end.end;\n                            var rangeStart = new Date(range.start.start);\n                            var rangeEnd = ranges[ranges.length - 1].end.end;\n                            var newStart = new Date(rangeStart);\n                            var newEnd = new Date(end);\n                            while (rangeStart.getTime() <= rangeEnd && event.end >= kendo.timezone.toLocalDate(rangeStart) && event.start <= kendo.timezone.toLocalDate(rangeEnd)) {\n                                var dateRange = group.daySlotRanges(newStart.getTime(), newEnd.getTime(), true)[0];\n                                newEnd.setDate(newEnd.getDate() + 1);\n                                newStart.setDate(newStart.getDate() + 1);\n                                if (dateRange) {\n                                    dateRange.head = null;\n                                    dateRange.middle = null;\n                                    dateRange.tail = null;\n                                    this._groupedView._positionEvent(event, group, dateRange, 1, start, end, 0);\n                                }\n                                rangeStart = kendo.date.addDays(rangeStart, 1);\n                            }\n                        } else {\n                            for (var rangeIndex = 0; rangeIndex < rangeCount; rangeIndex++) {\n                                range = ranges[rangeIndex];\n                                start = event.start;\n                                end = event.end;\n                                this._groupedView._positionEvent(event, group, range, rangeCount, start, end, rangeIndex);\n                            }\n                        }\n                    }\n                }\n            },\n            _renderGroups: function (events, resources, offset, columnLevel) {\n                var resource = resources[0];\n                if (resource) {\n                    var view = resource.dataSource.view();\n                    for (var itemIdx = 0; itemIdx < view.length; itemIdx++) {\n                        var value = this._resourceValue(resource, view[itemIdx]);\n                        var tmp = new kendo.data.Query(events).filter({\n                            field: resource.field,\n                            operator: SchedulerView.groupEqFilter(value)\n                        }).toArray();\n                        if (resources.length > 1) {\n                            offset = this._renderGroups(tmp, resources.slice(1), offset++, columnLevel + 1);\n                        } else {\n                            this._renderEvents(tmp, offset++);\n                        }\n                    }\n                }\n                return offset;\n            },\n            _groupCount: function () {\n                var resources = this.groupedResources;\n                var groupedView = this._groupedView;\n                if (resources.length) {\n                    if (this._isVerticallyGrouped()) {\n                        return groupedView._verticalGroupCount(resources.length - 1);\n                    } else {\n                        return groupedView._horizontalGroupCount(resources.length);\n                    }\n                }\n                return 1;\n            },\n            _columnOffsetForResource: function (index) {\n                return this._columnCountForLevel(index) / this._columnCountForLevel(index - 1);\n            },\n            destroy: function () {\n                if (this.table) {\n                    this.table.removeClass('k-scheduler-monthview');\n                }\n                if (this.content) {\n                    this.content.off(NS);\n                }\n                if (this.element) {\n                    this.element.off(NS);\n                }\n                SchedulerView.fn.destroy.call(this);\n                if (this._isMobile() && this.options.editable) {\n                    if (this.options.editable.create !== false) {\n                        this._addUserEvents.destroy();\n                    }\n                }\n            },\n            events: [\n                'remove',\n                'add',\n                'edit',\n                'navigate'\n            ],\n            options: {\n                title: 'Month',\n                name: 'month',\n                eventHeight: 25,\n                moreButtonHeight: 13,\n                editable: true,\n                selectedDateFormat: '{0:y}',\n                selectedShortDateFormat: '{0:y}',\n                selectedMobileDateFormat: '{0:MMMM}',\n                groupHeaderTemplate: '#=text#',\n                dayTemplate: DAY_TEMPLATE,\n                eventTemplate: EVENT_TEMPLATE\n            }\n        });\n        function shiftArray(array, idx) {\n            return array.slice(idx).concat(array.slice(0, idx));\n        }\n        function firstVisibleMonthDay(date, calendarInfo) {\n            var firstDay = calendarInfo.firstDay, firstVisibleDay = new Date(date.getFullYear(), date.getMonth(), 0, date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n            while (firstVisibleDay.getDay() != firstDay) {\n                kendo.date.setTime(firstVisibleDay, -1 * MS_PER_DAY);\n            }\n            return firstVisibleDay;\n        }\n        function isInDateRange(value, min, max) {\n            var msMin = min, msMax = max, msValue;\n            msValue = value;\n            return msValue >= msMin && msValue <= msMax;\n        }\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}