{"version": 3, "sources": ["kendo.sortable.js"], "names": ["f", "define", "$", "undefined", "containsOrEqualTo", "parent", "child", "contains", "e", "defaultHint", "element", "clone", "defaultPlaceholder", "removeAttr", "css", "kendo", "window", "Widget", "ui", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "START", "BEFORE_MOVE", "MOVE", "END", "CHANGE", "CANCEL", "ACTION_SORT", "ACTION_REMOVE", "ACTION_RECEIVE", "DEFAULT_FILTER", "MISSING_INDEX", "Sortable", "extend", "init", "options", "that", "this", "fn", "call", "placeholder", "hint", "draggable", "_createDraggable", "events", "name", "filter", "holdToDrag", "disabled", "container", "connectWith", "handler", "cursorOffset", "axis", "ignore", "autoScroll", "cursor", "moveOnDragEnter", "destroy", "Draggable", "isFunction", "dragstart", "proxy", "_dragstart", "dragcancel", "_dragcancel", "drag", "_drag", "dragend", "_dragend", "draggedElement", "currentTarget", "_placeholder", "is", "preventDefault", "initialTarget", "trigger", "item", "draggableEvent", "before", "_setCursor", "_cancel", "_resetCursor", "targetCenter", "offsetDelta", "direction", "sibling", "getSibling", "target", "_findTarget", "left", "x", "location", "top", "y", "axisDelta", "delta", "eventData", "list", "_movementByAxis", "_getElementCenter", "Math", "round", "appendToBottom", "_movePlaceholder", "appendAfterHidden", "_isFloating", "j<PERSON><PERSON><PERSON>", "prev", "next", "length", "connectedList", "isDefaultPrevented", "connectedListEventData", "draggedIndex", "indexOf", "placeholderIndex", "action", "oldIndex", "newIndex", "parents", "getKendoSortable", "replaceWith", "show", "dropped", "items", "node", "_findElementUnderCursor", "has", "sortable", "_isEmpty", "_isLastHidden", "eq", "_searchConnectedTargets", "elementUnderCursor", "sender", "hide", "sortableInstance", "i", "connected", "_isCursorAfterLast", "last", "lastItemOffset", "lastItem", "getOffset", "cursorPosition", "after", "append", "body", "document", "_originalCursorType", "_cursor<PERSON><PERSON><PERSON><PERSON><PERSON>", "appendTo", "remove", "center", "test", "_items", "find", "children", "not", "index", "plugin", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,kBAAmB,qBAAsBD,IAClD,WA8YE,MAvYC,UAAUE,EAAGC,GAEV,QAASC,GAAkBC,EAAQC,GAC/B,IACI,MAAOJ,GAAEK,SAASF,EAAQC,IAAUD,GAAUC,EAChD,MAAOE,GACL,OAAO,GAGf,QAASC,GAAYC,GACjB,MAAOA,GAAQC,QAEnB,QAASC,GAAmBF,GACxB,MAAOA,GAAQC,QAAQE,WAAW,MAAMC,IAAI,aAAc,UAbjE,GACOC,GAAQC,OAAOD,MAAOE,EAASF,EAAMG,GAAGD,OAAQE,EAAaJ,EAAMK,YAAaC,EAAcN,EAAMO,aAAcC,EAAQ,QAASC,EAAc,aAAcC,EAAO,OAAQC,EAAM,MAAOC,EAAS,SAAUC,EAAS,SAAUC,EAAc,OAAQC,EAAgB,SAAUC,EAAiB,UAAWC,EAAiB,KAAMC,KAcpUC,EAAWjB,EAAOkB,QAClBC,KAAM,SAAU1B,EAAS2B,GACrB,GAAIC,GAAOC,IACXtB,GAAOuB,GAAGJ,KAAKK,KAAKH,EAAM5B,EAAS2B,GAC9BC,EAAKD,QAAQK,cACdJ,EAAKD,QAAQK,YAAc9B,GAE1B0B,EAAKD,QAAQM,OACdL,EAAKD,QAAQM,KAAOlC,GAExB6B,EAAKM,UAAYN,EAAKO,oBAE1BC,QACIvB,EACAC,EACAC,EACAC,EACAC,EACAC,GAEJS,SACIU,KAAM,WACNJ,KAAM,KACND,YAAa,KACbM,OAAQhB,EACRiB,YAAY,EACZC,SAAU,KACVC,UAAW,KACXC,YAAa,KACbC,QAAS,KACTC,aAAc,KACdC,KAAM,KACNC,OAAQ,KACRC,YAAY,EACZC,OAAQ,OACRC,iBAAiB,GAErBC,QAAS,WACLrB,KAAKK,UAAUgB,UACf3C,EAAOuB,GAAGoB,QAAQnB,KAAKF,OAE3BM,iBAAkB,WACd,GAAIP,GAAOC,KAAM7B,EAAU4B,EAAK5B,QAAS2B,EAAUC,EAAKD,OACxD,OAAO,IAAItB,GAAMG,GAAG2C,UAAUnD,GAC1BsC,OAAQX,EAAQW,OAChBL,KAAM5B,EAAM+C,WAAWzB,EAAQM,MAAQN,EAAQM,KAAOzC,EAAEmC,EAAQM,MAChEM,WAAYZ,EAAQY,WACpBE,UAAWd,EAAQc,UAAYjD,EAAEmC,EAAQc,WAAa,KACtDG,aAAcjB,EAAQiB,aACtBC,KAAMlB,EAAQkB,KACdC,OAAQnB,EAAQmB,OAChBC,WAAYpB,EAAQoB,WACpBM,UAAW7D,EAAE8D,MAAM1B,EAAK2B,WAAY3B,GACpC4B,WAAYhE,EAAE8D,MAAM1B,EAAK6B,YAAa7B,GACtC8B,KAAMlE,EAAE8D,MAAM1B,EAAK+B,MAAO/B,GAC1BgC,QAASpE,EAAE8D,MAAM1B,EAAKiC,SAAUjC,MAGxC2B,WAAY,SAAUzD,GAClB,GAAIgE,GAAiBjC,KAAKiC,eAAiBhE,EAAEiE,cAAevB,EAAWX,KAAKF,QAAQa,SAAUG,EAAUd,KAAKF,QAAQgB,QAASqB,EAAenC,KAAKF,QAAQK,YAAaA,EAAcH,KAAKG,YAA+CxC,EAAjCa,EAAM+C,WAAWY,GAAkBA,EAAajC,KAAKF,KAAMiC,GAAqBE,EACpRxB,IAAYsB,EAAeG,GAAGzB,GAC9B1C,EAAEoE,iBACKvB,IAAYnD,EAAEM,EAAEqE,eAAeF,GAAGtB,GACzC7C,EAAEoE,iBAEErC,KAAKuC,QAAQvD,GACTwD,KAAMP,EACNQ,eAAgBxE,IAEpBA,EAAEoE,kBAEFJ,EAAe1D,IAAI,UAAW,QAC9B0D,EAAeS,OAAOvC,GACtBH,KAAK2C,eAIjBf,YAAa,WACT5B,KAAK4C,UACL5C,KAAKuC,QAAQlD,GAAUmD,KAAMxC,KAAKiC,iBAClCjC,KAAK6C,gBAETf,MAAO,SAAU7D,GACb,GAAwE6E,GAGjEC,EAGAC,EAAWC,EAASC,EANvBjB,EAAiBjC,KAAKiC,eAAgBkB,EAASnD,KAAKoD,YAAYnF,GAAkB8C,GAC9EsC,KAAMpF,EAAEqF,EAAEC,SACVC,IAAKvF,EAAEwF,EAAEF,UACGG,GACZJ,EAAGrF,EAAEqF,EAAEK,MACPF,EAAGxF,EAAEwF,EAAEE,OACwB3C,EAAOhB,KAAKF,QAAQkB,KAAMI,EAAkBpB,KAAKF,QAAQsB,gBAAiBwC,GACzGpB,KAAMP,EACN4B,KAAM7D,KACNyC,eAAgBxE,EAExB,IAAa,MAAT+C,GAAyB,MAATA,EAEhB,MADAhB,MAAK8D,gBAAgB9C,EAAMD,EAAc2C,EAAU1C,GAAO4C,GAC1D,CAEJ,IAAIT,EAAQ,CAOR,GANAL,EAAe9C,KAAK+D,kBAAkBZ,EAAOhF,SAC7C4E,GACIM,KAAMW,KAAKC,MAAMlD,EAAasC,KAAOP,EAAaO,MAClDG,IAAKQ,KAAKC,MAAMlD,EAAayC,IAAMV,EAAaU,MAEpD7F,EAAEiC,OAAOgE,GAAaT,OAAQA,EAAOhF,UACjCgF,EAAOe,eAEP,MADAlE,MAAKmE,iBAAiBhB,EAAQ,KAAMS,GACpC,CAkBJ,IAhBIT,EAAOiB,mBACPpE,KAAKmE,iBAAiBhB,EAAQ,OAAQS,GAEtC5D,KAAKqE,YAAYlB,EAAOhF,SACpBuF,EAAUJ,EAAI,GAAKlC,IAAoBA,GAAmB2B,EAAYM,KAAO,EAC7EL,EAAY,QACLU,EAAUJ,EAAI,GAAKlC,IAAoBA,GAAmB2B,EAAYM,KAAO,KACpFL,EAAY,QAGZU,EAAUD,EAAI,GAAKrC,IAAoBA,GAAmB2B,EAAYS,IAAM,EAC5ER,EAAY,QACLU,EAAUD,EAAI,GAAKrC,IAAoBA,GAAmB2B,EAAYS,IAAM,KACnFR,EAAY,QAGhBA,EAAW,CAGX,IAFAE,EAA2B,SAAdF,EAAuBsB,OAAOrE,GAAGsE,KAAOD,OAAOrE,GAAGuE,KAC/DvB,EAAUC,EAAWhD,KAAKiD,EAAOhF,SAC1B8E,EAAQwB,SAAWxB,EAAQb,GAAG,aACjCa,EAAUC,EAAWhD,KAAK+C,EAE1BA,GAAQ,IAAMjD,KAAKG,YAAY,IAC/BH,KAAKmE,iBAAiBhB,EAAQH,EAAWY,MAKzD5B,SAAU,SAAU/D,GAChB,GAA6MyG,GAAeC,EAAoBf,EAAWgB,EAAvPzE,EAAcH,KAAKG,YAAa8B,EAAiBjC,KAAKiC,eAAgB4C,EAAe7E,KAAK8E,QAAQ7C,GAAiB8C,EAAmB/E,KAAK8E,QAAQ3E,GAAcU,EAAcb,KAAKF,QAAQe,WAqBhM,OApBAb,MAAK6C,eACLe,GACIoB,OAAQ1F,EACRkD,KAAMP,EACNgD,SAAUJ,EACVK,SAAUH,EACVtC,eAAgBxE,GAEhB8G,GAAoB,EACpBJ,EAAqB3E,KAAKuC,QAAQpD,EAAKyE,IAEvCc,EAAgBvE,EAAYgF,QAAQtE,GAAauE,mBACjDxB,EAAUoB,OAASzF,EACnBqF,EAAyBjH,EAAEiC,UAAWgE,GAClCoB,OAAQxF,EACRyF,SAAUvF,EACVwF,SAAUR,EAAcI,QAAQ3E,KAEpCwE,KAAwB3E,KAAKuC,QAAQpD,EAAKyE,KAAec,EAAcnC,QAAQpD,EAAKyF,KAEpFD,GAAsBI,IAAqBF,GAC3C7E,KAAK4C,UACL,IAEJzC,EAAYkF,YAAYpD,GACxBA,EAAeqD,OACftF,KAAKK,UAAUkF,SAAU,EACzB3B,GACIoB,OAAQhF,KAAK8E,QAAQ7C,IAAmBvC,EAAgBJ,EAAcC,EACtEiD,KAAMP,EACNgD,SAAUJ,EACVK,SAAUlF,KAAK8E,QAAQ7C,GACvBQ,eAAgBxE,GAEpB+B,KAAKuC,QAAQnD,EAAQwE,GACjBc,IACAE,EAAyBjH,EAAEiC,UAAWgE,GAClCoB,OAAQxF,EACRyF,SAAUvF,EACVwF,SAAUR,EAAcI,QAAQ7C,KAEpCyC,EAAcnC,QAAQnD,EAAQwF,IAjBlCzE,IAoBJiD,YAAa,SAAUnF,GACnB,GAA+CuH,GAA+CC,EAA1FtH,EAAU6B,KAAK0F,wBAAwBzH,GAAW4C,EAAcb,KAAKF,QAAQe,WACjF,OAAIlD,GAAEK,SAASgC,KAAK7B,QAAQ,GAAIA,IAC5BqH,EAAQxF,KAAKwF,QACbC,EAAOD,EAAM/E,OAAOtC,GAAS,IAAMqH,EAAMG,IAAIxH,GAAS,GAC/CsH,GACHtH,QAASR,EAAE8H,GACXG,SAAU5F,MACV,MACGA,KAAK7B,QAAQ,IAAMA,GAAW6B,KAAK6F,YAEtC1H,QAAS6B,KAAK7B,QACdyH,SAAU5F,KACVkE,gBAAgB,GAEblE,KAAK7B,QAAQ,IAAMA,GAAW6B,KAAK8F,iBAC1CL,EAAOzF,KAAKwF,QAAQO,GAAG,IAEnB5H,QAASsH,EACTG,SAAU5F,KACVoE,mBAAmB,IAEhBvD,EACAb,KAAKgG,wBAAwB7H,EAASF,GAD1C,GAIXyH,wBAAyB,SAAUzH,GAC/B,GAAIgI,GAAqBzH,EAAMyH,mBAAmBhI,GAAIoC,EAAYpC,EAAEiI,MASpE,OARIrI,GAAkBwC,EAAUD,KAAK,GAAI6F,KACrC5F,EAAUD,KAAK+F,OACfF,EAAqBzH,EAAMyH,mBAAmBhI,GACzCgI,IACDA,EAAqBzH,EAAMyH,mBAAmBhI,IAElDoC,EAAUD,KAAKkF,QAEZW,GAEXD,wBAAyB,SAAU7H,EAASF,GAAnB,GACwBmI,GAAkBZ,EAAOC,EAC7DY,EADLC,EAAY3I,EAAEqC,KAAKF,QAAQe,YAC/B,KAASwF,EAAI,EAAGA,EAAIC,EAAU7B,OAAQ4B,IAElC,GADAD,EAAmBE,EAAUP,GAAGM,GAAGjB,mBAC/BzH,EAAEK,SAASsI,EAAUD,GAAIlI,IACzB,GAAIiI,EAGA,MAFAZ,GAAQY,EAAiBZ,QACzBC,EAAOD,EAAM/E,OAAOtC,GAAS,IAAMqH,EAAMG,IAAIxH,GAAS,GAClDsH,GACAW,EAAiBjG,YAAcH,KAAKG,aAEhChC,QAASR,EAAE8H,GACXG,SAAUQ,IAGP,SAGZ,IAAIE,EAAUD,IAAMlI,EAAS,CAChC,GAAIiI,GAAoBA,EAAiBP,WACrC,OACI1H,QAASmI,EAAUP,GAAGM,GACtBT,SAAUQ,EACVlC,gBAAgB,EAEjB,IAAIlE,KAAKuG,mBAAmBH,EAAkBnI,GAEjD,MADAwH,GAAOW,EAAiBZ,QAAQgB,QAE5BrI,QAASsH,EACTG,SAAUQ,KAM9BG,mBAAoB,SAAUX,EAAU3H,GACpC,GAGOwI,GAAgB9C,EAHnB+C,EAAWd,EAASJ,QAAQgB,OAAQzF,GAChCsC,KAAMpF,EAAEqF,EAAEC,SACVC,IAAKvF,EAAEwF,EAAEF,SAUjB,OARAkD,GAAiBjI,EAAMmI,UAAUD,GACjCD,EAAejD,KAAO1E,EAAY4H,GAClCD,EAAepD,MAAQzE,EAAW8H,GAE9B/C,EADA3D,KAAKqE,YAAYqC,GACTD,EAAepD,KAAOtC,EAAasC,KAEnCoD,EAAejD,IAAMzC,EAAayC,IAEvCG,EAAQ,GAEnBG,gBAAiB,SAAU9C,EAAMD,EAAc4C,EAAOC,GAClD,GAAwKd,GAApK8D,EAA0B,MAAT5F,EAAeD,EAAasC,KAAOtC,EAAayC,IAAKL,EAASQ,EAAQ,EAAI3D,KAAKG,YAAYoE,OAASvE,KAAKG,YAAYqE,OAAQgB,EAAQxF,KAAKwF,OAC3JrC,GAAOsB,SAAWtB,EAAOf,GAAG,cAC5Be,EAASQ,EAAQ,EAAIR,EAAOoB,OAASpB,EAAOqB,QAE3CgB,EAAM/E,OAAO0C,GAAQsB,SAG1B9G,EAAEiC,OAAOgE,GAAaT,OAAQA,IAC9BL,EAAe9C,KAAK+D,kBAAkBZ,GAClCL,IACAA,EAAwB,MAAT9B,EAAe8B,EAAaO,KAAOP,EAAaU,KAE/DL,EAAOsB,QAAUd,EAAQ,GAAKiD,EAAiB9D,EAAe,EAC9D9C,KAAKmE,kBACDhG,QAASgF,EACTyC,SAAU5F,MACX,OAAQ4D,GACJT,EAAOsB,QAAUd,EAAQ,GAAKiD,EAAiB9D,EAAe,GACrE9C,KAAKmE,kBACDhG,QAASgF,EACTyC,SAAU5F,MACX,OAAQ4D,KAGnBO,iBAAkB,SAAUhB,EAAQH,EAAWY,GAC3C,GAAIzD,GAAcH,KAAKG,WAClBgD,GAAOyC,SAASrD,QAAQtD,EAAa2E,KACjCZ,EAEoB,SAAdA,EACPG,EAAOhF,QAAQuE,OAAOvC,GACD,SAAd6C,GACPG,EAAOhF,QAAQ0I,MAAM1G,GAJrBgD,EAAOhF,QAAQ2I,OAAO3G,GAM1BgD,EAAOyC,SAASrD,QAAQrD,EAAM0E,KAGtCjB,WAAY,WACR,GAAkCoE,GAA9B5F,EAASnB,KAAKF,QAAQqB,MACtBA,IAAqB,SAAXA,IACV4F,EAAOpJ,EAAEqJ,SAASD,MAClB/G,KAAKiH,oBAAsBF,EAAKxI,IAAI,UACpCwI,EAAKxI,KAAM4C,OAAUA,IAChBnB,KAAKkH,oBACNlH,KAAKkH,kBAAoBvJ,EAAE,sBAAwBwD,EAAS,2BAEhEnB,KAAKkH,kBAAkBC,SAASJ,KAGxClE,aAAc,WACN7C,KAAKiH,sBACLtJ,EAAEqJ,SAASD,MAAMxI,IAAI,SAAUyB,KAAKiH,qBACpCjH,KAAKiH,oBAAsB,KAC3BjH,KAAKkH,kBAAkBE,WAG/BrD,kBAAmB,SAAU5F,GACzB,GAAIkJ,GAASlJ,EAAQsG,OAASjG,EAAMmI,UAAUxI,GAAW,IAKzD,OAJIkJ,KACAA,EAAO7D,KAAO1E,EAAYX,GAAW,EACrCkJ,EAAOhE,MAAQzE,EAAWT,GAAW,GAElCkJ,GAEXhD,YAAa,SAAU7B,GACnB,MAAO,aAAa8E,KAAK9E,EAAKjE,IAAI,WAAa,oBAAoB+I,KAAK9E,EAAKjE,IAAI,aAErFqE,QAAS,WACL5C,KAAKiC,eAAeqD,OACpBtF,KAAKG,YAAYiH,UAErBG,OAAQ,WACJ,GAAkC/B,GAA9B/E,EAAST,KAAKF,QAAQW,MAM1B,OAJI+E,GADA/E,EACQT,KAAK7B,QAAQqJ,KAAK/G,GAElBT,KAAK7B,QAAQsJ,YAI7B3C,QAAS,SAAU3G,GACf,GAAIqH,GAAQxF,KAAKuH,SAAUpH,EAAcH,KAAKG,YAAa8B,EAAiBjC,KAAKiC,cACjF,OAAI9B,IAAehC,EAAQ,IAAMgC,EAAY,GAClCqF,EAAMkC,IAAIzF,GAAgB0F,MAAMxJ,GAEhCqH,EAAMkC,IAAIvH,GAAawH,MAAMxJ,IAG5CqH,MAAO,WACH,GAAIrF,GAAcH,KAAKG,YAAaqF,EAAQxF,KAAKuH,QAIjD,OAHIpH,KACAqF,EAAQA,EAAMkC,IAAIvH,IAEfqF,GAEXK,SAAU,WACN,OAAQ7F,KAAKwF,QAAQf,QAEzBqB,cAAe,WACX,MAA+B,KAAxB9F,KAAKwF,QAAQf,QAAgBzE,KAAKwF,QAAQpD,GAAG,aAG5D5D,GAAMG,GAAGiJ,OAAOjI,IAClBlB,OAAOD,MAAM8F,QACR7F,OAAOD,OACE,kBAAVd,SAAwBA,OAAOmK,IAAMnK,OAAS,SAAUoK,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.sortable.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.sortable', ['kendo.draganddrop'], f);\n}(function () {\n    var __meta__ = {\n        id: 'sortable',\n        name: 'Sortable',\n        category: 'framework',\n        depends: ['draganddrop']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, START = 'start', BEFORE_MOVE = 'beforeMove', MOVE = 'move', END = 'end', CHANGE = 'change', CANCEL = 'cancel', ACTION_SORT = 'sort', ACTION_REMOVE = 'remove', ACTION_RECEIVE = 'receive', DEFAULT_FILTER = '>*', MISSING_INDEX = -1;\n        function containsOrEqualTo(parent, child) {\n            try {\n                return $.contains(parent, child) || parent == child;\n            } catch (e) {\n                return false;\n            }\n        }\n        function defaultHint(element) {\n            return element.clone();\n        }\n        function defaultPlaceholder(element) {\n            return element.clone().removeAttr('id').css('visibility', 'hidden');\n        }\n        var Sortable = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                if (!that.options.placeholder) {\n                    that.options.placeholder = defaultPlaceholder;\n                }\n                if (!that.options.hint) {\n                    that.options.hint = defaultHint;\n                }\n                that.draggable = that._createDraggable();\n            },\n            events: [\n                START,\n                BEFORE_MOVE,\n                MOVE,\n                END,\n                CHANGE,\n                CANCEL\n            ],\n            options: {\n                name: 'Sortable',\n                hint: null,\n                placeholder: null,\n                filter: DEFAULT_FILTER,\n                holdToDrag: false,\n                disabled: null,\n                container: null,\n                connectWith: null,\n                handler: null,\n                cursorOffset: null,\n                axis: null,\n                ignore: null,\n                autoScroll: false,\n                cursor: 'auto',\n                moveOnDragEnter: false\n            },\n            destroy: function () {\n                this.draggable.destroy();\n                Widget.fn.destroy.call(this);\n            },\n            _createDraggable: function () {\n                var that = this, element = that.element, options = that.options;\n                return new kendo.ui.Draggable(element, {\n                    filter: options.filter,\n                    hint: kendo.isFunction(options.hint) ? options.hint : $(options.hint),\n                    holdToDrag: options.holdToDrag,\n                    container: options.container ? $(options.container) : null,\n                    cursorOffset: options.cursorOffset,\n                    axis: options.axis,\n                    ignore: options.ignore,\n                    autoScroll: options.autoScroll,\n                    dragstart: $.proxy(that._dragstart, that),\n                    dragcancel: $.proxy(that._dragcancel, that),\n                    drag: $.proxy(that._drag, that),\n                    dragend: $.proxy(that._dragend, that)\n                });\n            },\n            _dragstart: function (e) {\n                var draggedElement = this.draggedElement = e.currentTarget, disabled = this.options.disabled, handler = this.options.handler, _placeholder = this.options.placeholder, placeholder = this.placeholder = kendo.isFunction(_placeholder) ? $(_placeholder.call(this, draggedElement)) : $(_placeholder);\n                if (disabled && draggedElement.is(disabled)) {\n                    e.preventDefault();\n                } else if (handler && !$(e.initialTarget).is(handler)) {\n                    e.preventDefault();\n                } else {\n                    if (this.trigger(START, {\n                            item: draggedElement,\n                            draggableEvent: e\n                        })) {\n                        e.preventDefault();\n                    } else {\n                        draggedElement.css('display', 'none');\n                        draggedElement.before(placeholder);\n                        this._setCursor();\n                    }\n                }\n            },\n            _dragcancel: function () {\n                this._cancel();\n                this.trigger(CANCEL, { item: this.draggedElement });\n                this._resetCursor();\n            },\n            _drag: function (e) {\n                var draggedElement = this.draggedElement, target = this._findTarget(e), targetCenter, cursorOffset = {\n                        left: e.x.location,\n                        top: e.y.location\n                    }, offsetDelta, axisDelta = {\n                        x: e.x.delta,\n                        y: e.y.delta\n                    }, direction, sibling, getSibling, axis = this.options.axis, moveOnDragEnter = this.options.moveOnDragEnter, eventData = {\n                        item: draggedElement,\n                        list: this,\n                        draggableEvent: e\n                    };\n                if (axis === 'x' || axis === 'y') {\n                    this._movementByAxis(axis, cursorOffset, axisDelta[axis], eventData);\n                    return;\n                }\n                if (target) {\n                    targetCenter = this._getElementCenter(target.element);\n                    offsetDelta = {\n                        left: Math.round(cursorOffset.left - targetCenter.left),\n                        top: Math.round(cursorOffset.top - targetCenter.top)\n                    };\n                    $.extend(eventData, { target: target.element });\n                    if (target.appendToBottom) {\n                        this._movePlaceholder(target, null, eventData);\n                        return;\n                    }\n                    if (target.appendAfterHidden) {\n                        this._movePlaceholder(target, 'next', eventData);\n                    }\n                    if (this._isFloating(target.element)) {\n                        if (axisDelta.x < 0 && moveOnDragEnter || !moveOnDragEnter && offsetDelta.left < 0) {\n                            direction = 'prev';\n                        } else if (axisDelta.x > 0 && moveOnDragEnter || !moveOnDragEnter && offsetDelta.left > 0) {\n                            direction = 'next';\n                        }\n                    } else {\n                        if (axisDelta.y < 0 && moveOnDragEnter || !moveOnDragEnter && offsetDelta.top < 0) {\n                            direction = 'prev';\n                        } else if (axisDelta.y > 0 && moveOnDragEnter || !moveOnDragEnter && offsetDelta.top > 0) {\n                            direction = 'next';\n                        }\n                    }\n                    if (direction) {\n                        getSibling = direction === 'prev' ? jQuery.fn.prev : jQuery.fn.next;\n                        sibling = getSibling.call(target.element);\n                        while (sibling.length && !sibling.is(':visible')) {\n                            sibling = getSibling.call(sibling);\n                        }\n                        if (sibling[0] != this.placeholder[0]) {\n                            this._movePlaceholder(target, direction, eventData);\n                        }\n                    }\n                }\n            },\n            _dragend: function (e) {\n                var placeholder = this.placeholder, draggedElement = this.draggedElement, draggedIndex = this.indexOf(draggedElement), placeholderIndex = this.indexOf(placeholder), connectWith = this.options.connectWith, connectedList, isDefaultPrevented, eventData, connectedListEventData;\n                this._resetCursor();\n                eventData = {\n                    action: ACTION_SORT,\n                    item: draggedElement,\n                    oldIndex: draggedIndex,\n                    newIndex: placeholderIndex,\n                    draggableEvent: e\n                };\n                if (placeholderIndex >= 0) {\n                    isDefaultPrevented = this.trigger(END, eventData);\n                } else {\n                    connectedList = placeholder.parents(connectWith).getKendoSortable();\n                    eventData.action = ACTION_REMOVE;\n                    connectedListEventData = $.extend({}, eventData, {\n                        action: ACTION_RECEIVE,\n                        oldIndex: MISSING_INDEX,\n                        newIndex: connectedList.indexOf(placeholder)\n                    });\n                    isDefaultPrevented = !(!this.trigger(END, eventData) && !connectedList.trigger(END, connectedListEventData));\n                }\n                if (isDefaultPrevented || placeholderIndex === draggedIndex) {\n                    this._cancel();\n                    return;\n                }\n                placeholder.replaceWith(draggedElement);\n                draggedElement.show();\n                this.draggable.dropped = true;\n                eventData = {\n                    action: this.indexOf(draggedElement) != MISSING_INDEX ? ACTION_SORT : ACTION_REMOVE,\n                    item: draggedElement,\n                    oldIndex: draggedIndex,\n                    newIndex: this.indexOf(draggedElement),\n                    draggableEvent: e\n                };\n                this.trigger(CHANGE, eventData);\n                if (connectedList) {\n                    connectedListEventData = $.extend({}, eventData, {\n                        action: ACTION_RECEIVE,\n                        oldIndex: MISSING_INDEX,\n                        newIndex: connectedList.indexOf(draggedElement)\n                    });\n                    connectedList.trigger(CHANGE, connectedListEventData);\n                }\n            },\n            _findTarget: function (e) {\n                var element = this._findElementUnderCursor(e), items, connectWith = this.options.connectWith, node;\n                if ($.contains(this.element[0], element)) {\n                    items = this.items();\n                    node = items.filter(element)[0] || items.has(element)[0];\n                    return node ? {\n                        element: $(node),\n                        sortable: this\n                    } : null;\n                } else if (this.element[0] == element && this._isEmpty()) {\n                    return {\n                        element: this.element,\n                        sortable: this,\n                        appendToBottom: true\n                    };\n                } else if (this.element[0] == element && this._isLastHidden()) {\n                    node = this.items().eq(0);\n                    return {\n                        element: node,\n                        sortable: this,\n                        appendAfterHidden: true\n                    };\n                } else if (connectWith) {\n                    return this._searchConnectedTargets(element, e);\n                }\n            },\n            _findElementUnderCursor: function (e) {\n                var elementUnderCursor = kendo.elementUnderCursor(e), draggable = e.sender;\n                if (containsOrEqualTo(draggable.hint[0], elementUnderCursor)) {\n                    draggable.hint.hide();\n                    elementUnderCursor = kendo.elementUnderCursor(e);\n                    if (!elementUnderCursor) {\n                        elementUnderCursor = kendo.elementUnderCursor(e);\n                    }\n                    draggable.hint.show();\n                }\n                return elementUnderCursor;\n            },\n            _searchConnectedTargets: function (element, e) {\n                var connected = $(this.options.connectWith), sortableInstance, items, node;\n                for (var i = 0; i < connected.length; i++) {\n                    sortableInstance = connected.eq(i).getKendoSortable();\n                    if ($.contains(connected[i], element)) {\n                        if (sortableInstance) {\n                            items = sortableInstance.items();\n                            node = items.filter(element)[0] || items.has(element)[0];\n                            if (node) {\n                                sortableInstance.placeholder = this.placeholder;\n                                return {\n                                    element: $(node),\n                                    sortable: sortableInstance\n                                };\n                            } else {\n                                return null;\n                            }\n                        }\n                    } else if (connected[i] == element) {\n                        if (sortableInstance && sortableInstance._isEmpty()) {\n                            return {\n                                element: connected.eq(i),\n                                sortable: sortableInstance,\n                                appendToBottom: true\n                            };\n                        } else if (this._isCursorAfterLast(sortableInstance, e)) {\n                            node = sortableInstance.items().last();\n                            return {\n                                element: node,\n                                sortable: sortableInstance\n                            };\n                        }\n                    }\n                }\n            },\n            _isCursorAfterLast: function (sortable, e) {\n                var lastItem = sortable.items().last(), cursorOffset = {\n                        left: e.x.location,\n                        top: e.y.location\n                    }, lastItemOffset, delta;\n                lastItemOffset = kendo.getOffset(lastItem);\n                lastItemOffset.top += outerHeight(lastItem);\n                lastItemOffset.left += outerWidth(lastItem);\n                if (this._isFloating(lastItem)) {\n                    delta = lastItemOffset.left - cursorOffset.left;\n                } else {\n                    delta = lastItemOffset.top - cursorOffset.top;\n                }\n                return delta < 0 ? true : false;\n            },\n            _movementByAxis: function (axis, cursorOffset, delta, eventData) {\n                var cursorPosition = axis === 'x' ? cursorOffset.left : cursorOffset.top, target = delta < 0 ? this.placeholder.prev() : this.placeholder.next(), items = this.items(), targetCenter;\n                if (target.length && !target.is(':visible')) {\n                    target = delta < 0 ? target.prev() : target.next();\n                }\n                if (!items.filter(target).length) {\n                    return;\n                }\n                $.extend(eventData, { target: target });\n                targetCenter = this._getElementCenter(target);\n                if (targetCenter) {\n                    targetCenter = axis === 'x' ? targetCenter.left : targetCenter.top;\n                }\n                if (target.length && delta < 0 && cursorPosition - targetCenter < 0) {\n                    this._movePlaceholder({\n                        element: target,\n                        sortable: this\n                    }, 'prev', eventData);\n                } else if (target.length && delta > 0 && cursorPosition - targetCenter > 0) {\n                    this._movePlaceholder({\n                        element: target,\n                        sortable: this\n                    }, 'next', eventData);\n                }\n            },\n            _movePlaceholder: function (target, direction, eventData) {\n                var placeholder = this.placeholder;\n                if (!target.sortable.trigger(BEFORE_MOVE, eventData)) {\n                    if (!direction) {\n                        target.element.append(placeholder);\n                    } else if (direction === 'prev') {\n                        target.element.before(placeholder);\n                    } else if (direction === 'next') {\n                        target.element.after(placeholder);\n                    }\n                    target.sortable.trigger(MOVE, eventData);\n                }\n            },\n            _setCursor: function () {\n                var cursor = this.options.cursor, body;\n                if (cursor && cursor !== 'auto') {\n                    body = $(document.body);\n                    this._originalCursorType = body.css('cursor');\n                    body.css({ 'cursor': cursor });\n                    if (!this._cursorStylesheet) {\n                        this._cursorStylesheet = $('<style>* { cursor: ' + cursor + ' !important; }</style>');\n                    }\n                    this._cursorStylesheet.appendTo(body);\n                }\n            },\n            _resetCursor: function () {\n                if (this._originalCursorType) {\n                    $(document.body).css('cursor', this._originalCursorType);\n                    this._originalCursorType = null;\n                    this._cursorStylesheet.remove();\n                }\n            },\n            _getElementCenter: function (element) {\n                var center = element.length ? kendo.getOffset(element) : null;\n                if (center) {\n                    center.top += outerHeight(element) / 2;\n                    center.left += outerWidth(element) / 2;\n                }\n                return center;\n            },\n            _isFloating: function (item) {\n                return /left|right/.test(item.css('float')) || /inline|table-cell/.test(item.css('display'));\n            },\n            _cancel: function () {\n                this.draggedElement.show();\n                this.placeholder.remove();\n            },\n            _items: function () {\n                var filter = this.options.filter, items;\n                if (filter) {\n                    items = this.element.find(filter);\n                } else {\n                    items = this.element.children();\n                }\n                return items;\n            },\n            indexOf: function (element) {\n                var items = this._items(), placeholder = this.placeholder, draggedElement = this.draggedElement;\n                if (placeholder && element[0] == placeholder[0]) {\n                    return items.not(draggedElement).index(element);\n                } else {\n                    return items.not(placeholder).index(element);\n                }\n            },\n            items: function () {\n                var placeholder = this.placeholder, items = this._items();\n                if (placeholder) {\n                    items = items.not(placeholder);\n                }\n                return items;\n            },\n            _isEmpty: function () {\n                return !this.items().length;\n            },\n            _isLastHidden: function () {\n                return this.items().length === 1 && this.items().is(':hidden');\n            }\n        });\n        kendo.ui.plugin(Sortable);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}