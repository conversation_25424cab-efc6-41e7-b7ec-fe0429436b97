{"version": 3, "sources": ["kendo.scrollview.js"], "names": ["f", "define", "$", "undefined", "className", "name", "Pager", "TRANSITION_END", "DRAG_START", "DRAG_END", "ElasticPane", "<PERSON><PERSON><PERSON>iew<PERSON><PERSON><PERSON>", "VirtualScrollViewContent", "Page", "ScrollView", "kendo", "window", "ui", "proxy", "Transition", "effects", "Pane", "PaneDimensions", "Widget", "DataBoundWidget", "DataSource", "data", "math", "Math", "abs", "ceil", "round", "max", "min", "floor", "CHANGE", "CLICK", "CHANGING", "REFRESH", "CURRENT_PAGE_CLASS", "VIRTUAL_PAGE_CLASS", "FUNCTION", "ITEM_CHANGE", "CLEANUP", "VIRTUAL_PAGE_COUNT", "LEFT_PAGE", "CETER_PAGE", "RIGHT_PAGE", "LEFT_SWIPE", "NUDGE", "RIGHT_SWIPE", "ScrollViewDataReader", "Observable", "extend", "init", "dataSource", "that", "this", "pendingRequestArray", "initialFetch", "useRanges", "options", "serverPaging", "fn", "call", "bind", "_change", "trigger", "offset", "page", "callback", "view", "range", "pageSize", "scrollTo", "pageCount", "total", "prevPage", "prevPrefetch", "currentPage", "nextPage", "nextPrefetch", "push", "getViewData", "index", "length", "destroy", "unbind", "Class", "scrollView", "element", "navigationWrapElement", "append", "_navigationContainer", "_changeProxy", "_refreshProxy", "on", "_click", "items", "children", "_refresh", "e", "idx", "pageHTML", "html", "eq", "addClass", "_toggleNavigation", "innerNavigationContainer", "scrollViewWidth", "containerOffset", "pageWidth", "itemOffset", "translate", "isDefaultPrevented", "find", "width", "outerWidth", "removeClass", "position", "left", "scrollLeft", "animate", "newPage", "currentTarget", "off", "remove", "ScrollViewPager", "movable", "transition", "userEvents", "dimensions", "dimension", "pane", "container", "parent", "Movable", "axis", "onEnd", "UserEvents", "fastTap", "start", "x", "velocity", "y", "capture", "cancel", "allowSelection", "end", "elastic", "duration", "size", "getSize", "height", "getTotal", "updateDimension", "update", "refresh", "enabled", "moveTo", "moveAxis", "transitionTo", "ease", "instant", "location", "tansition", "ScrollViewElasticPane", "_getPages", "contentHeight", "enablePager", "pagerOverlay", "easeOutExpo", "paneMoved", "swipeType", "bounce", "snap", "approx", "easeOutBack", "minSnap", "maxSnap", "updatePage", "forcePageUpdate", "resizeTo", "containerHeight", "pager", "pageElements", "_outerHeight", "css", "_paged", "roleSelector", "_templates", "pages", "_initPages", "forceEnabled", "setDataSource", "create", "_dataReader", "_pendingPageRefresh", "_pendingWidgetRefresh", "_viewShow", "setTimeout", "_resetPages", "dataReader", "_pageProxy", "_resetProxy", "reset", "template", "emptyTemplate", "templateProxy", "emptyTemplateProxy", "i", "<PERSON><PERSON><PERSON><PERSON>", "_repositionPages", "silent", "pagerScroll", "thresholdPassed", "isEndReached", "delta", "Array", "_cancelMove", "_moveBackward", "_moveForward", "shift", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unshift", "pop", "_resetMovable", "threshold", "_onPage", "item", "_onReset", "content", "theContent", "VirtualPage", "empty", "stripWhitespace", "wrapInner", "_initNavigation", "inner", "first", "transitionEnd", "dragStart", "dragEnd", "change", "_defaultPrevented", "angular", "elements", "dataItem", "_content", "viewInit", "viewShow", "velocityThreshold", "bounceVelocityThreshold", "enableNavigationButtons", "autoBind", "events", "prev", "eventData", "next", "emptyDataSource", "fetch", "_dragStart", "_dragEnd", "_transitionEnd", "navigationContainer", "prevArrow", "hide", "nextArrow", "show", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,oBACH,WACA,aACA,qBACDD,IACL,WA03BE,MA92BC,UAAUE,EAAGC,GAEV,QAASC,GAAUC,GACf,MAAO,KAAOA,EAHrB,GAiFOC,GAgEAC,EAAkCC,EAA0BC,EAC5DC,EAuGAC,EA+FAC,EA6QAC,EAwBAC,EA5nBAC,EAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAAQhB,EAAEgB,MAAOC,EAAaJ,EAAMK,QAAQD,WAAYE,EAAON,EAAME,GAAGI,KAAMC,EAAiBP,EAAME,GAAGK,eAAgBC,EAASN,EAAGO,gBAAiBC,EAAaV,EAAMW,KAAKD,WAAYE,EAAOC,KAAMC,EAAMF,EAAKE,IAAKC,EAAOH,EAAKG,KAAMC,EAAQJ,EAAKI,MAAOC,EAAML,EAAKK,IAAKC,EAAMN,EAAKM,IAAKC,EAAQP,EAAKO,MAAOC,EAAS,SAAUC,EAAQ,QAASC,EAAW,WAAYC,EAAU,UAAWC,EAAqB,UAAWC,EAAqB,kBAAmBC,EAAW,WAAYC,EAAc,aAAcC,EAAU,UAAWC,EAAqB,EAAGC,KAAgBC,EAAa,EAAGC,EAAa,EAAGC,KAAiBC,EAAQ,EAAGC,EAAc,EAIvqBC,EAAuBpC,EAAMqC,WAAWC,QACxCC,KAAM,SAAUC,GACZ,GAAIC,GAAOC,IACXA,MAAKF,WAAaA,EAClBE,KAAKC,uBACLD,KAAKE,cAAe,EACpBF,KAAKG,UAAYL,EAAWM,QAAQC,aACpC/C,EAAMqC,WAAWW,GAAGT,KAAKU,KAAKP,MAC9BF,EAAWU,KAAK,SAAU,WACtBT,EAAKU,aAGbA,QAAS,WACLT,KAAKU,QAAQ,SAAWC,OAAQX,KAAKW,UAEzCC,KAAM,SAAUA,EAAMC,GAClB,GAAId,GAAOC,IACNA,MAAKG,YACNH,KAAKF,WAAWc,KAAKA,EAAO,GACxBC,EACAA,EAASd,EAAKD,WAAWgB,QAEzBf,EAAKW,QAAQ,QAAUE,KAAMA,KAGjCZ,KAAKG,WACLH,KAAKF,WAAWiB,MAAMH,EAAOZ,KAAKF,WAAWkB,WAAYhB,KAAKF,WAAWkB,WAAY,WAC7EH,EACAA,EAASd,EAAKD,WAAWgB,QAEzBf,EAAKW,QAAQ,QAAUE,KAAMA,OAK7CK,SAAU,SAAUL,GAAV,GACFM,GAAY/C,KAAKE,KAAK2B,KAAKF,WAAWqB,QAAUnB,KAAKF,WAAWkB,YAAc,GAC9EI,EAAWR,EAAO,EAClBS,EAAeD,EAAW,EAC1BE,EAAcV,EACdW,EAAWL,EAAY,GAAKN,EAAO,GAAKM,KAAiBN,EAAO,EAChEY,EAAeN,EAAY,GAAKK,EAAW,GAAKL,KAAiBK,EAAW,CAC5EA,IAAY,GACZvB,KAAKC,oBAAoBwB,KAAKF,GAE9BH,GAAY,GACZpB,KAAKC,oBAAoBwB,KAAKL,GAE9BC,GAAgB,GAChBrB,KAAKC,oBAAoBwB,KAAKJ,GAE9BG,GAAgB,GAChBxB,KAAKC,oBAAoBwB,KAAKD,GAElCxB,KAAKY,KAAKU,IAEdI,YAAa,WAAA,GAELzD,GAGS0D,EAJTb,EAAOd,KAAKF,WAAWgB,MAE3B,IAAId,KAAKF,WAAWM,QAAQY,SAAW,EAEnC,IADA/C,KACS0D,EAAQ,EAAGA,EAAQb,EAAKc,OAAQD,IACrC1D,EAAKwD,KAAKX,EAAKa,QAGnB1D,GAAO6C,EAAK,EAEhB,OAAO7C,IAEX4D,QAAS,WACL,GAAI9B,GAAOC,IACXD,GAAKD,WAAWgC,SAChB/B,EAAKD,WAAa,OAG1BxC,GAAME,GAAGkC,qBAAuBA,EAC5B7C,EAAQS,EAAMyE,MAAMnC,QACpBC,KAAM,SAAUmC,GACZ,GAAIjC,GAAOC,KAAMiC,EAAUxF,EAAE,cAAiBE,EAAU,kBAAoB,OAASuF,EAAwBzF,EAAE,eAAkBE,EAAU,uBAAyB,WACpKuF,GAAsBC,OAAOF,GAC7BD,EAAWI,qBAAqBD,OAAOD,GACvClC,KAAKqC,aAAe5E,EAAMsC,EAAM,WAChCC,KAAKsC,cAAgB7E,EAAMsC,EAAM,YACjCiC,EAAWxB,KAAK9B,EAAQsB,KAAKqC,cAC7BL,EAAWxB,KAAK3B,EAASmB,KAAKsC,eAC9BL,EAAQM,GAAG5D,EAAO,YAAalB,EAAMuC,KAAKwC,OAAQR,IAClDvF,EAAEmD,OAAOG,GACLkC,QAASA,EACTD,WAAYA,KAGpBS,MAAO,WACH,MAAOzC,MAAKiC,QAAQS,YAExBC,SAAU,SAAUC,GAAV,GAEGC,GADLC,EAAW,EACf,KAASD,EAAM,EAAGA,EAAMD,EAAE1B,UAAW2B,IACjCC,GAAY,0BAEhB9C,MAAKiC,QAAQc,KAAKD,GAClB9C,KAAKyC,QAAQO,GAAGJ,EAAEhC,MAAMqC,SAAStG,EAAUmC,IAC3CkB,KAAKgC,WAAWkB,mBAAoB5B,YAAasB,EAAEhC,QAEvDH,QAAS,SAAUmC,GAAV,GAIDO,GACAC,EACAC,EACAC,EAEAC,EAEIC,CAVJZ,GAAEa,uBAGFN,EAA2BnD,KAAKgC,WAAWI,qBAAqBsB,KAAK,qBACrEN,EAAkBpD,KAAKgC,WAAWC,QAAQ0B,QAC1CN,GAAmBD,EAAkBD,EAAyBQ,SAAW,EACzEL,EAAYH,EAAyBO,KAAK,mBAAmBE,YAAW,GAAQ,EACpF5D,KAAKyC,QAAQoB,YAAYlH,EAAUmC,IAAqBkE,GAAGJ,EAAErB,UAAU0B,SAAStG,EAAUmC,IACtFyE,EAAavD,KAAKyC,QAAQO,GAAGJ,EAAErB,UAAUK,OAAS,EAAI5B,KAAKyC,QAAQO,GAAGJ,EAAErB,UAAUuC,WAAWC,KAAO,GACpGR,EAAaH,EAAkB,GAAKG,EAAaJ,EAAyBa,aAAeZ,EAAkB,KACvGI,EAAY,EAEZA,EADAD,EAAaH,EAAkB,EACnBD,EAAyBa,aAAeT,EAAaH,EAAkB,EAEvED,EAAyBa,cAAgBZ,EAAkB,EAAIG,GAE/EC,GAAaH,EAAkBC,EAC/BH,EAAyBc,SAAUD,WAAcR,GAAa,MAElExD,KAAKgC,WAAWkB,mBACZ5B,YAAasB,EAAEtB,YACfC,SAAUqB,EAAErB,aAGpBiB,OAAQ,SAAUI,GACd,GAAIsB,GAAUzH,EAAEmG,EAAEuB,eAAexC,OACjC3B,MAAKiB,SAASiD,IAElBrC,QAAS,WACL7B,KAAKgC,WAAWF,OAAOpD,EAAQsB,KAAKqC,cACpCrC,KAAKgC,WAAWF,OAAOjD,EAASmB,KAAKsC,eACrCtC,KAAKiC,QAAQmC,IAAIzF,GACjBqB,KAAKiC,QAAQoC,YAGrB/G,EAAME,GAAG8G,gBAAkBzH,EACvBC,EAAiB,gBAAiBC,EAAa,YAAaC,EAAW,UACvEC,EAAcK,EAAMqC,WAAWC,QAC/BC,KAAM,SAAUoC,EAAS7B,GAAnB,GAKEmE,GAASC,EAAYC,EAAYC,EAAYC,EAAWC,EAJxD7E,EAAOC,IACX1C,GAAMqC,WAAWW,GAAGT,KAAKU,KAAKP,MAC9BA,KAAKiC,QAAUA,EACfjC,KAAK6E,UAAY5C,EAAQ6C,SAEzBP,EAAU,GAAIjH,GAAME,GAAGuH,QAAQhF,EAAKkC,SACpCuC,EAAa,GAAI9G,IACbsH,KAAM,IACNT,QAASA,EACTU,MAAO,WACHlF,EAAKW,QAAQ5D,MAGrB2H,EAAa,GAAInH,GAAM4H,WAAWjD,GAC9BkD,SAAS,EACTC,MAAO,SAAUxC,GACW,EAApBxE,EAAIwE,EAAEyC,EAAEC,WAAiBlH,EAAIwE,EAAE2C,EAAED,UACjCb,EAAWe,UAEXf,EAAWgB,SAEf1F,EAAKW,QAAQ3D,EAAY6F,GACzB4B,EAAWiB,UAEfC,gBAAgB,EAChBC,IAAK,SAAU/C,GACX7C,EAAKW,QAAQ1D,EAAU4F,MAG/B8B,EAAa,GAAI7G,IACboE,QAASlC,EAAKkC,QACd4C,UAAW9E,EAAK8E,YAEpBF,EAAYD,EAAWW,EACvBV,EAAUnE,KAAK9B,EAAQ,WACnBqB,EAAKW,QAAQhC,KAEjBkG,EAAO,GAAIhH,IACP8G,WAAYA,EACZD,WAAYA,EACZF,QAASA,EACTqB,SAAS,IAEbnJ,EAAEmD,OAAOG,GACL8F,SAAUzF,GAAWA,EAAQyF,UAAY,EACzCtB,QAASA,EACTC,WAAYA,EACZC,WAAYA,EACZC,WAAYA,EACZC,UAAWA,EACXC,KAAMA,IAEV5E,KAAKQ,MACD1D,EACAC,EACAC,EACA0B,GACD0B,IAEP0F,KAAM,WACF,OACInC,MAAO3D,KAAK0E,WAAWW,EAAEU,UACzBC,OAAQhG,KAAK0E,WAAWa,EAAEQ,YAGlC5E,MAAO,WACH,MAAOnB,MAAK2E,UAAUsB,YAE1BtF,OAAQ,WACJ,OAAQX,KAAKuE,QAAQc,GAEzBa,gBAAiB,WACblG,KAAK2E,UAAUwB,QAAO,IAE1BC,QAAS,WACLpG,KAAK0E,WAAW0B,UAChBpG,KAAK0E,WAAWa,EAAEc,SAAU,GAEhCC,OAAQ,SAAU3F,GACdX,KAAKuE,QAAQgC,SAAS,KAAM5F,IAEhC6F,aAAc,SAAU7F,EAAQ8F,EAAMC,GAC9BA,EACA1G,KAAKsG,QAAQ3F,GAEbX,KAAKwE,WAAW8B,QACZK,SAAUhG,EACVkF,SAAU7F,KAAK6F,SACfY,KAAMA,KAIlB5E,QAAS,WACL,GAAI9B,GAAOC,IACXD,GAAK0E,WAAW5C,UAChB9B,EAAK+B,SACL/B,EAAKwE,QAAUxE,EAAK6G,UAAY7G,EAAK2E,WAAa3E,EAAK4E,UAAY5E,EAAK6E,KAAO,KAC/E7E,EAAKkC,QAAQoC,YAGrB/G,EAAME,GAAGqJ,sBAAwB5J,EAC7BC,EAAoBI,EAAMqC,WAAWC,QACrCC,KAAM,SAAUoC,EAAS2C,EAAMxE,GAC3B,GAAIL,GAAOC,IACX1C,GAAMqC,WAAWW,GAAGT,KAAKU,KAAKP,MAC9BD,EAAKkC,QAAUA,EACflC,EAAK6E,KAAOA,EACZ7E,EAAK+G,YACL9G,KAAKY,KAAO,EACZZ,KAAKgB,SAAWZ,EAAQY,UAAY,EACpChB,KAAK+G,cAAgB3G,EAAQ2G,cAC7B/G,KAAKgH,YAAc5G,EAAQ4G,YAC3BhH,KAAKiH,aAAe7G,EAAQ6G,cAEhChG,SAAU,SAAUL,EAAM8F,GACtB,GAAI3G,GAAOC,MACPY,GAAQb,EAAKa,MAAS8F,KAGrB3G,EAAKW,QAAQ,UACVY,YAAatB,KAAKY,KAClBW,SAAUX,EACV3C,KAAMvB,MAEVqD,EAAKa,KAAOA,EACZb,EAAK6E,KAAK4B,cAAc5F,EAAOb,EAAK6E,KAAKkB,OAAOnC,MAAOjG,EAAWwJ,YAAaR,MAGvFS,UAAW,SAAUC,EAAWC,EAAQxG,EAAU6F,GAAvC,GACwJY,GAAM/F,EAQ7JqF,EARJ7G,EAAOC,KAAM4E,EAAO7E,EAAK6E,KAAMjB,EAAQiB,EAAKkB,OAAOnC,MAAQ5D,EAAKiB,SAAUuG,EAASjJ,EAAOmI,EAAOY,EAAS3J,EAAW8J,YAAc9J,EAAWwJ,WAOlJ,OANIE,KAAc7H,EACdgI,EAASlJ,EACF+I,IAAc3H,IACrB8H,EAAS9I,GAEb8C,EAAWgG,EAAO3C,EAAKjE,SAAWgD,GAC9BpC,EAAW,GAAKA,GAAYxB,EAAKmB,WAC7B0F,EAAYrF,EAAW,EAAI,GAAKvB,KAAKY,KAAOZ,KAAK4E,KAAKkB,OAAOnC,MAC1D3D,KAAK4E,KAAK4B,aAAaI,EAAWH,EAAMC,KAEnDY,EAAO/I,EAAIwB,EAAK0H,QAASjJ,GAAK+C,EAAWoC,EAAO5D,EAAK2H,UACjDnG,GAAYxB,EAAKa,MACbC,GAAYA,GACRS,YAAavB,EAAKa,KAClBW,SAAUA,MAEd+F,GAAQvH,EAAKa,KAAOgE,EAAKkB,OAAOnC,OAGxCiB,EAAK4B,aAAac,EAAMb,EAAMC,GAT9BY,IAWJK,WAAY,WACR,GAAI/C,GAAO5E,KAAK4E,KAAMhE,EAAOtC,EAAMsG,EAAKjE,SAAWiE,EAAKkB,OAAOnC,MAC/D,OAAI/C,IAAQZ,KAAKY,OACbZ,KAAKY,KAAOA,GACL,IAIfgH,gBAAiB,WACb,MAAO5H,MAAK2H,cAEhBE,SAAU,SAAU/B,GAAV,GAIEgC,GAEIC,EALRnD,EAAO5E,KAAK4E,KAAMjB,EAAQmC,EAAKnC,KACnC3D,MAAKgI,aAAarE,MAAMA,GACG,SAAvB3D,KAAK+G,gBACDe,EAAkB9H,KAAKiC,QAAQ6C,SAASkB,SACxChG,KAAKgH,eAAgB,IACjBe,EAAQ/H,KAAKiC,QAAQ6C,SAASpB,KAAK,wBAClC1D,KAAKiH,cAAgBc,EAAMnG,SAC5BkG,GAAmBxK,EAAM2K,aAAaF,GAAO,KAGrD/H,KAAKiC,QAAQiG,IAAI,SAAUJ,GAC3B9H,KAAKgI,aAAaE,IAAI,SAAUJ,IAEpClD,EAAKsB,kBACAlG,KAAKmI,SACNnI,KAAKY,KAAOnC,EAAMmG,EAAKjE,SAAWgD,IAEtC3D,KAAKiB,SAASjB,KAAKY,MAAM,GAAM,GAC/BZ,KAAKkB,UAAYzC,EAAMmG,EAAKzD,QAAUwC,GACtC3D,KAAKyH,UAAYzH,KAAKkB,UAAY,GAAKyC,EACvC3D,KAAK0H,QAAU,GAEnBZ,UAAW,WACP9G,KAAKgI,aAAehI,KAAKiC,QAAQyB,KAAKpG,EAAM8K,aAAa,SACzDpI,KAAKmI,OAASnI,KAAKgI,aAAapG,OAAS,GAE7CC,QAAS,WACL,GAAI9B,GAAOC,IACXD,GAAK6E,KAAO,KACZ7E,EAAKkC,QAAQoC,YAGrB/G,EAAME,GAAGN,kBAAoBA,EACzBC,EAA2BG,EAAMqC,WAAWC,QAC5CC,KAAM,SAAUoC,EAAS2C,EAAMxE,GAC3B,GAAIL,GAAOC,IACX1C,GAAMqC,WAAWW,GAAGT,KAAKU,KAAKP,MAC9BD,EAAKkC,QAAUA,EACflC,EAAK6E,KAAOA,EACZ7E,EAAKK,QAAUA,EACfL,EAAKsI,aACLtI,EAAKa,KAAOR,EAAQQ,MAAQ,EAC5Bb,EAAKuI,SACLvI,EAAKwI,aACLxI,EAAK8H,SAAS9H,EAAK6E,KAAKkB,QACxB/F,EAAK6E,KAAKD,UAAU6D,gBAExBC,cAAe,SAAU3I,GACrBE,KAAKF,WAAa9B,EAAW0K,OAAO5I,GACpCE,KAAK2I,cACL3I,KAAK4I,qBAAsB,EAC3B5I,KAAK6I,uBAAwB,GAEjCC,UAAW,WACP,GAAI/I,GAAOC,IACPD,GAAK8I,wBACLE,WAAW,WACPhJ,EAAKiJ,eACN,GACHjJ,EAAK8I,uBAAwB,IAGrCF,YAAa,WACT3I,KAAKiJ,WAAa,GAAIvJ,GAAqBM,KAAKF,YAChDE,KAAKkJ,WAAazL,EAAMuC,KAAM,WAC9BA,KAAKmJ,YAAc1L,EAAMuC,KAAM,YAC/BA,KAAKiJ,WAAWzI,MACZI,KAAQZ,KAAKkJ,WACbE,MAASpJ,KAAKmJ,eAGtBd,WAAY,WACR,GAAIgB,GAAWrJ,KAAKI,QAAQiJ,SAAUC,EAAgBtJ,KAAKI,QAAQkJ,cAAeC,KAAoBC,WAC3FH,KAAarK,IACpBuK,EAAcF,SAAWA,EACzBA,EAAW,0BAEfrJ,KAAKqJ,SAAW5L,EAAMH,EAAM+L,SAASA,GAAWE,SACrCD,KAAkBtK,IACzBwK,EAAmBF,cAAgBA,EACnCA,EAAgB,+BAEpBtJ,KAAKsJ,cAAgB7L,EAAMH,EAAM+L,SAASC,GAAgBE,IAE9DjB,WAAY,WAAA,GACwC3H,GACvC6I,EADLnB,EAAQtI,KAAKsI,MAAOrG,EAAUjC,KAAKiC,OACvC,KAASwH,EAAI,EAAGA,EAAItK,EAAoBsK,IACpC7I,EAAO,GAAIxD,GAAK6E,GAChBqG,EAAM7G,KAAKb,EAEfZ,MAAK4E,KAAKsB,mBAEd2B,SAAU,SAAU/B,GAAV,GAEG2D,GAMD3B,EAEIC,EATRO,EAAQtI,KAAKsI,MAAO1D,EAAO5E,KAAK4E,IACpC,KAAS6E,EAAI,EAAGA,EAAInB,EAAM1G,OAAQ6H,IAC9BnB,EAAMmB,GAAGC,SAAS5D,EAAKnC,MAEQ,UAA/B3D,KAAKI,QAAQ2G,cACb/G,KAAKiC,QAAQiG,IAAI,SAAUlI,KAAKsI,MAAM,GAAGrG,QAAQ+D,UACX,SAA/BhG,KAAKI,QAAQ2G,eAChBe,EAAkB9H,KAAKiC,QAAQ6C,SAASkB,SACxChG,KAAKI,QAAQ4G,eAAgB,IACzBe,EAAQ/H,KAAKiC,QAAQ6C,SAASpB,KAAK,wBAClC1D,KAAKI,QAAQ6G,cAAgBc,EAAMnG,SACpCkG,GAAmBxK,EAAM2K,aAAaF,GAAO,KAGrD/H,KAAKiC,QAAQiG,IAAI,SAAUJ,GAC3BQ,EAAM,GAAGrG,QAAQiG,IAAI,SAAUJ,GAC/BQ,EAAM,GAAGrG,QAAQiG,IAAI,SAAUJ,GAC/BQ,EAAM,GAAGrG,QAAQiG,IAAI,SAAUJ,IACxB9H,KAAKI,QAAQ2G,gBACpBuB,EAAM,GAAGrG,QAAQiG,IAAI,SAAUlI,KAAKI,QAAQ2G,eAC5CuB,EAAM,GAAGrG,QAAQiG,IAAI,SAAUlI,KAAKI,QAAQ2G,eAC5CuB,EAAM,GAAGrG,QAAQiG,IAAI,SAAUlI,KAAKI,QAAQ2G,gBAEhDnC,EAAKsB,kBACLlG,KAAK2J,mBACL3J,KAAK2D,MAAQmC,EAAKnC,OAEtB1C,SAAU,SAAUL,EAAM8F,EAASkD,GAAzB,GACF7J,GAAOC,KACPiJ,EAAalJ,EAAKkJ,YAClBrI,GAAQb,EAAKa,MAAS8F,IAG1BuC,EAAWrI,KAAKA,EAAM,SAAU3C,GAC5B,MAAI2L,IACAX,EAAWhI,SAASL,GACpB,IAECb,EAAKW,QAAQ,UACVY,YAAavB,EAAKa,KAClBW,SAAUX,EACV3C,KAAMA,MAELyI,EAID3G,EAAKa,KAAOA,GAHZqI,EAAWY,YAAcjJ,EAAOb,EAAKa,QAAY,EACjDb,EAAKa,KAAOA,EAAOqI,EAAWY,aAIlCZ,EAAWhI,SAASL,IAXxB,MAeRuG,UAAW,SAAUC,EAAWC,EAAQxG,EAAU6F,GAC9C,GAAkUnF,GAAqBtD,EAAMgE,EAAzVlC,EAAOC,KAAM4E,EAAO7E,EAAK6E,KAAMjB,EAAQiB,EAAKkB,OAAOnC,MAAOhD,EAASiE,EAAKjE,SAAUmJ,EAAkB3L,KAAKC,IAAIuC,IAAWgD,EAAQ,EAAG8C,EAAOY,EAAS/J,EAAMK,QAAQD,WAAW8J,YAAclK,EAAMK,QAAQD,WAAWwJ,YAAa6C,IAAehK,EAAKD,WAAWM,QAAQC,cAAeN,EAAKa,KAAO,EAAIb,EAAKmB,UAA6B8I,EAAQ,CAChV5C,KAAc3H,EACI,IAAdM,EAAKa,OACLoJ,MAEG5C,IAAc7H,GAAewK,EAE7BpJ,EAAS,GAAMmJ,IAAoBC,EAC1CC,EAAQ,EACDrJ,EAAS,GAAKmJ,GACH,IAAd/J,EAAKa,OACLoJ,MALJA,EAAQ,EAQZzI,EAAWxB,EAAKa,KACZoJ,IACAzI,EAAWyI,EAAQ,EAAIzI,EAAW,EAAIA,EAAW,EAC7CxB,YAAgBzC,GAAME,GAAGL,0BACzB4C,EAAKkJ,WAAWrI,KAAKW,GACrBtD,EAAO8B,EAAKkJ,WAAWvH,eAEvBzD,EAAOvB,EAELuB,YAAgBgM,SAClBhM,GAAQA,IAEZgE,EAAUlC,EAAKuI,MAAQvI,EAAKuI,MAAM,GAAGrG,QAAUvF,GAE/CmE,GAAYd,EAAKa,MAAQW,GAAYV,GACjCS,YAAavB,EAAKa,KAClBW,SAAUA,EACVU,QAASA,EACThE,KAAMA,MAEV+L,EAAQ,GAEE,IAAVA,EACAjK,EAAKmK,YAAYzD,EAAMC,GAChBsD,OACPjK,EAAKoK,cAAczD,GACF,IAAVsD,GACPjK,EAAKqK,aAAa1D,IAG1BiB,WAAY,WACR,GAAIW,GAAQtI,KAAKsI,KACjB,OAA2B,KAAvBtI,KAAK4E,KAAKjE,WAGVX,KAAK4E,KAAKjE,SAAW,GACrB2H,EAAM7G,KAAKzB,KAAKsI,MAAM+B,SACtBrK,KAAKY,OACDZ,KAAKY,KAAO,EAAIZ,KAAKkB,WACrBlB,KAAKiJ,WAAWhJ,oBAAoBwB,KAAKzB,KAAKY,KAAO,GAErDZ,KAAKY,KAAO,EAAIZ,KAAKkB,WACrBlB,KAAKiJ,WAAWrI,KAAKZ,KAAKY,KAAO,GAEjCZ,KAAKY,KAAO,GAAKZ,KAAKkB,WACtBlB,KAAKsK,eAAetK,KAAKsI,MAAM,GAAI,QAGvCA,EAAMiC,QAAQvK,KAAKsI,MAAMkC,OACzBxK,KAAKY,OACDZ,KAAKY,KAAO,GAAK,GACjBZ,KAAKiJ,WAAWhJ,oBAAoBwB,KAAKzB,KAAKY,KAAO,GAErDZ,KAAKY,KAAO,GAAK,GACjBZ,KAAKiJ,WAAWrI,KAAKZ,KAAKY,KAAO,IAGzCZ,KAAK2J,mBACL3J,KAAKyK,iBACE,IAEX7C,gBAAiB,WACb,GAAIjH,GAASX,KAAK4E,KAAKjE,SAAU+J,EAAqC,EAAzB1K,KAAK4E,KAAKkB,OAAOnC,MAAY,CAC1E,OAAIvF,GAAIuC,GAAU+J,GACP1K,KAAK2H,cAIpB8C,cAAe,WACXzK,KAAK4E,KAAK0B,OAAO,IAErB8D,aAAc,SAAU1D,GACpB1G,KAAK4E,KAAK4B,cAAcxG,KAAK2D,MAAOrG,EAAMK,QAAQD,WAAWwJ,YAAaR,IAE9EyD,cAAe,SAAUzD,GACrB1G,KAAK4E,KAAK4B,aAAaxG,KAAK2D,MAAOrG,EAAMK,QAAQD,WAAWwJ,YAAaR,IAE7EwD,YAAa,SAAUzD,EAAMC,GACzB1G,KAAK4E,KAAK4B,aAAa,EAAGC,EAAMC,IAEpCsC,YAAa,WACThJ,KAAKY,KAAOZ,KAAKI,QAAQQ,MAAQ,EACjCZ,KAAK2J,mBACL3J,KAAKU,QAAQ,UAEjBiK,QAAS,SAAU/H,GAqBf,GApBIA,EAAEhC,MAAQZ,KAAKkB,WACflB,KAAKsK,eAAetK,KAAKsI,MAAM,GAAI,MAEnCtI,KAAKY,MAAQgC,EAAEhC,MACVZ,KAAKiJ,WAAWY,aAA+C,IAAhC7J,KAAKiJ,WAAWY,aAAqB7J,KAAKiJ,WAAW/I,aACrFF,KAAKsK,eAAetK,KAAKsI,MAAM,GAAItI,KAAKiJ,WAAWvH,gBAE/C1B,KAAKiJ,WAAWY,YAAc,EAC9B7J,KAAKoK,eAELpK,KAAKmK,gBAETnK,KAAKiJ,WAAWY,YAAc,EAC9B7J,KAAKsK,eAAetK,KAAKsI,MAAM,GAAItI,KAAKiJ,WAAWvH,gBAEhD1B,KAAKY,KAAO,GAAKgC,EAAEhC,KAC1BZ,KAAKsK,eAAetK,KAAKsI,MAAM,GAAItI,KAAKiJ,WAAWvH,eAC5C1B,KAAKY,KAAO,GAAKgC,EAAEhC,MAC1BZ,KAAKsK,eAAetK,KAAKsI,MAAM,GAAItI,KAAKiJ,WAAWvH,eAEnD1B,KAAKiJ,WAAWhJ,oBAAoB2B,OAAS,GAAK5B,KAAKiJ,WAAW/I,aAAc,CAChF,GAAI0K,GAAO5K,KAAKiJ,WAAWhJ,oBAAoBoK,OAC/CrK,MAAKiJ,WAAWrI,KAAKgK,KAG7BC,SAAU,WACN7K,KAAKkB,UAAY7C,EAAK2B,KAAKF,WAAWqB,QAAUnB,KAAKF,WAAWkB,aAEpE2I,iBAAkB,WACd,GAAIrB,GAAQtI,KAAKsI,KACjBA,GAAM,GAAGxE,SAAS1E,GAClBkJ,EAAM,GAAGxE,SAASzE,GAClBiJ,EAAM,GAAGxE,SAASxE,IAEtBgL,eAAgB,SAAU1J,EAAM3C,GAC5B,GAAIoL,GAAWrJ,KAAKqJ,SAAUC,EAAgBtJ,KAAKsJ,aAE/C1I,GAAKkK,QADI,OAAT7M,GAAiBA,IAASvB,EACb2M,EAASpL,GAETqL,QAGrBzH,QAAS,WAAA,GAMIF,GALL5B,EAAOC,KACPsI,EAAQvI,EAAKuI,KAIjB,KAHAvI,EAAKkJ,WAAWnH,SAChB/B,EAAKD,WAAWgC,SAChB/B,EAAKkJ,WAAalJ,EAAKD,WAAaC,EAAK6E,KAAO,KACvCjD,EAAQ,EAAGA,EAAQ2G,EAAM1G,OAAQD,IACtC2G,EAAM3G,GAAOE,SAEjB9B,GAAKkC,QAAQoC,YAGrB/G,EAAME,GAAGL,yBAA2BA,EAChCC,EAAOE,EAAMyE,MAAMnC,QACnBC,KAAM,SAAUgF,GACZ7E,KAAKiC,QAAUxF,EAAE,cAAiBE,EAAUoC,GAAsB,WAClEiB,KAAK2D,MAAQkB,EAAUlB,QACvB3D,KAAKiC,QAAQ0B,MAAM3D,KAAK2D,OACxBkB,EAAU1C,OAAOnC,KAAKiC,UAE1B6I,QAAS,SAAUC,GACf/K,KAAKiC,QAAQc,KAAKgI,IAEtBjH,SAAU,SAAUA,GAChB9D,KAAKiC,QAAQiG,IAAI,YAAa,eAAiBlI,KAAK2D,MAAQG,EAAW,cAE3E4F,SAAU,SAAU/F,GAChB3D,KAAK2D,MAAQA,EACb3D,KAAKiC,QAAQ0B,MAAMA,IAEvB9B,QAAS,WACL,GAAI9B,GAAOC,IACXD,GAAKkC,QAAQoC,SACbtE,EAAKkC,QAAU,QAGvB3E,EAAME,GAAGwN,YAAc5N,EACnBC,EAAaS,EAAO8B,QACpBC,KAAM,SAAUoC,EAAS7B,GAAnB,GAoCE6K,GACAH,EApCA/K,EAAOC,IACXlC,GAAOwC,GAAGT,KAAKU,KAAKR,EAAMkC,EAAS7B,GACnCA,EAAUL,EAAKK,QACf6B,EAAUlC,EAAKkC,QACf3E,EAAM4N,gBAAgBjJ,EAAQ,IAE1BA,EAAQkJ,UADsB,IAA9BlJ,EAAQS,WAAWd,OACD,kCAEA,oCAEtBK,EAAQgB,SAAS,YAActG,EAAU,eACzCoD,EAAKqL,kBACDpL,KAAKI,QAAQ4G,aACbhH,KAAK+H,MAAQ,GAAIlL,GAAMmD,MACnBA,KAAKI,QAAQ6G,cACbhF,EAAQgB,SAAStG,EAAU,yBAG/BqD,KAAKqC,aAAe5E,EAAMsC,EAAM,qBAChCC,KAAKQ,KAAK9B,EAAQsB,KAAKqC,eAE3BtC,EAAKsL,MAAQpJ,EAAQS,WAAW4I,QAChCvL,EAAKa,KAAO,EACZb,EAAKsL,MAAMnD,IAAI,SAAU9H,EAAQ2G,eACjChH,EAAK6E,KAAO,GAAI3H,GAAY8C,EAAKsL,OAC7BxF,SAAU7F,KAAKI,QAAQyF,SACvB0F,cAAe9N,EAAMuC,KAAM,kBAC3BwL,UAAW/N,EAAMuC,KAAM,cACvByL,QAAShO,EAAMuC,KAAM,YACrB0L,OAAQjO,EAAMuC,KAAMnB,KAExBkB,EAAKS,KAAK,SAAU,WAChBT,EAAK6E,KAAKwB,YAEdrG,EAAKa,KAAOR,EAAQQ,KAChBqK,EAAyC,IAAjCjL,KAAKqL,MAAM3I,WAAWd,OAC9BkJ,EAAUG,EAAQ,GAAI9N,GAAyB4C,EAAKsL,MAAOtL,EAAK6E,KAAMxE,GAAW,GAAIlD,GAAkB6C,EAAKsL,MAAOtL,EAAK6E,KAAMxE,GAClI0K,EAAQlK,KAAOb,EAAKa,KACpBkK,EAAQtK,KAAK,QAAS,WAClBR,KAAK4I,qBAAsB,EAC3B7I,EAAKW,QAAQ7B,GACTqC,UAAW4J,EAAQ5J,UACnBN,KAAMkK,EAAQlK,OAElBb,EAAKmD,mBACD5B,YAAawJ,EAAQlK,KACrBW,SAAUuJ,EAAQlK,SAG1BkK,EAAQtK,KAAK,SAAU,SAAUoC,GAAV,GACftB,GAAcwJ,EAAQlK,KACtBW,EAAWqB,EAAErB,QACbD,IAAeC,IACfqB,EAAE+I,kBAAoB5L,EAAKW,QAAQhC,GAC/B4C,YAAawJ,EAAQlK,KACrBW,SAAUqB,EAAErB,SACZtD,KAAM2E,EAAE3E,QAGhB8B,EAAKmD,mBACD5B,YAAawJ,EAAQlK,KACrBW,SAAUqB,EAAErB,aAGpBuJ,EAAQtK,KAAKvB,EAAa,SAAU2D,GAChC7C,EAAKW,QAAQzB,EAAa2D,GAC1B7C,EAAK6L,QAAQ,UAAW,WACpB,OACIC,SAAUjJ,EAAEgI,KACZ3M,OAAS6N,SAAUlJ,EAAE3E,YAIjC6M,EAAQtK,KAAKtB,EAAS,SAAU0D,GAC5B7C,EAAK6L,QAAQ,UAAW,WACpB,OAASC,SAAUjJ,EAAEgI,UAG7B7K,EAAKgM,SAAWjB,EAChB/K,EAAK0I,cAAcrI,EAAQN,YAC3BE,KAAKgM,WACLhM,KAAKiM,YAET7L,SACIxD,KAAM,aACNgE,KAAM,EACNiF,SAAU,IACVqG,kBAAmB,GACnBnF,cAAe,OACf/F,SAAU,EACVmL,wBAAyB,IACzBnF,aAAa,EACboF,yBAAyB,EACzBnF,cAAc,EACdoF,UAAU,EACVhD,SAAU,GACVC,cAAe,IAEnBgD,QACI1N,EACAF,EACAG,GAEJgD,QAAS,WACL/D,EAAOwC,GAAGuB,QAAQtB,KAAKP,MACvBA,KAAK+L,SAASlK,UACd7B,KAAK4E,KAAK/C,UACN7B,KAAK+H,OACL/H,KAAK+H,MAAMlG,UAEf7B,KAAKqL,MAAQ,KACb/N,EAAMuE,QAAQ7B,KAAKiC,UAEvB+J,SAAU,WACFhM,KAAKI,QAAQiM,UACbrM,KAAK+L,SAAS9K,SAASjB,KAAK+L,SAASnL,MAAM,GAAM,IAGzDqL,SAAU,WACNjM,KAAK4E,KAAKwB,WAEdA,QAAS,WAAA,GACD0E,GAAU9K,KAAK+L,SACf3L,EAAUJ,KAAKI,OACnB0K,GAAQjD,SAAS7H,KAAK4E,KAAKkB,QAC3B9F,KAAKY,KAAOkK,EAAQlK,MAChBkK,YAAmB5N,IAAqB4N,EAAQ7B,WAAW/I,gBACvDE,EAAQ4G,YACRhH,KAAKU,QAAQ7B,GACTqC,UAAW4J,EAAQ5J,UACnBN,KAAMkK,EAAQlK,OAGlBZ,KAAKU,QAAQhC,GACTwC,UAAW4J,EAAQ5J,UACnBI,YAAawJ,EAAQlK,SAKrCkK,QAAS,SAAU/H,GACf/C,KAAKiC,QAAQS,WAAW4I,QAAQvI,KAAKA,GACrC/C,KAAK+L,SAASjF,YACd9G,KAAK4E,KAAKwB,WAEdnF,SAAU,SAAUL,EAAM8F,EAASkD,GAC/B5J,KAAK+L,SAAS9K,SAASL,EAAM8F,EAASkD,IAE1C2C,KAAM,WACF,GAAIxM,GAAOC,KAAMoB,EAAWrB,EAAKgM,SAASnL,KAAO,CAC7Cb,GAAKgM,mBAAoB5O,GACzB4C,EAAKgM,SAAS5E,UAAU1H,EAAa/C,EAAW,SAAU8P,GACtD,MAAOzM,GAAKW,QAAQhC,EAAQ8N,KAEzBpL,MACPrB,EAAKkB,SAASG,IAGtBqL,KAAM,WACF,GAAI1M,GAAOC,KAAMuB,EAAWxB,EAAKgM,SAASnL,KAAO,CAC7Cb,GAAKgM,mBAAoB5O,GACzB4C,EAAKgM,SAAS5E,UAAU5H,EAAY7C,EAAW,SAAU8P,GACrD,MAAOzM,GAAKW,QAAQhC,EAAQ8N,KAEzBjL,EAAWxB,EAAKgM,SAAS7K,WAChCnB,EAAKkB,SAASM,IAGtBkH,cAAe,SAAU3I,GAAV,GAKP4M,GAJA3M,EAAOC,IACLA,MAAK+L,mBAAoB5O,KAG3BuP,GAAmB5M,EACnBA,YAAsB9B,IACtB8B,EAAWM,QAAQY,SAAWlB,EAAWM,QAAQY,UAAY,EAC7DhB,KAAKF,WAAaA,EAAa,GAAI9B,GAAW8B,EAAWM,UAEzDJ,KAAKF,WAAa9B,EAAW0K,OAAO5I,GAExCE,KAAK+L,SAAStD,cAAczI,KAAKF,YAC7BE,KAAKI,QAAQiM,WAAaK,GAC1B1M,KAAKF,WAAW6M,MAAM,WAClB5M,EAAKgM,SAAS9C,WAAW/I,cAAe,EACxCH,EAAKkB,SAASlB,EAAKgM,SAASnL,MAAM,GAAM,GACxCb,EAAKgM,SAASrL,QAAQ,aAIlC+B,MAAO,WACH,MAAOzC,MAAKiC,QAAQyB,KAAK,MAAQ3E,IAErC6N,WAAY,WACR5M,KAAK+L,SAASnE,mBAElBiF,SAAU,SAAUjK,GAChB,GAAI7C,GAAOC,KAAMsF,EAAW1C,EAAEyC,EAAEC,SAAU4G,EAAoBlM,KAAKI,QAAQ8L,kBAAmB9E,EAAY5H,EAAO6H,EAASjJ,EAAIkH,GAAYtF,KAAKI,QAAQ+L,uBACnJ7G,GAAW4G,EACX9E,EAAY3H,EACL6F,GAAY4G,IACnB9E,EAAY7H,GAEhBS,KAAK+L,SAAS5E,UAAUC,EAAWC,EAAQ,SAAUmF,GACjD,MAAOzM,GAAKW,QAAQhC,EAAQ8N,MAGpCM,eAAgB,WACZ9M,KAAK+L,SAASpE,cAElByD,gBAAiB,WAAA,GACTrL,GAAOC,KACP+M,EAAsBhN,EAAKqC,qBAAuB3F,EAAE,6CACpDuQ,EAAYvQ,EAAE,iFAAiFwQ,OAC/FC,EAAYzQ,EAAE,iFAAiFwQ,MACnGF,GAAoB5K,OAAO6K,GAC3BD,EAAoB5K,OAAO+K,GAC3BnN,EAAKkC,QAAQE,OAAO4K,GACpBA,EAAoBxK,GAAG5D,EAAO,sBAAuBlB,EAAMsC,EAAKwM,KAAMxM,IACtEgN,EAAoBxK,GAAG5D,EAAO,sBAAuBlB,EAAMsC,EAAK0M,KAAM1M,KAE1EmD,kBAAmB,SAAUN,GAAV,GACXhC,GAAOgC,EAAErB,UAA2B,IAAfqB,EAAErB,SAAiBqB,EAAErB,SAAWqB,EAAEtB,YACvDyL,EAAsB/M,KAAKoC,qBAC3B4K,EAAYD,EAAoBrJ,KAAK,wBACrCwJ,EAAYH,EAAoBrJ,KAAK,uBACzCsJ,GAAUC,OACVC,EAAUD,QACNrM,GAAiB,IAATA,KACK,IAATA,GACAoM,EAAUG,OAEVvM,GAAQZ,KAAK+L,SAAS7K,UAAY,GAClCgM,EAAUC,WAK1B3P,EAAG4P,OAAO/P,IACZE,OAAOD,MAAM+P,QACR9P,OAAOD,OACE,kBAAVd,SAAwBA,OAAO8Q,IAAM9Q,OAAS,SAAU+Q,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.scrollview.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.scrollview', [\n        'kendo.fx',\n        'kendo.data',\n        'kendo.draganddrop'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'scrollview',\n        name: 'ScrollView',\n        category: 'web',\n        description: 'The Kendo ScrollView widget is used to scroll content wider than the device screen.',\n        depends: [\n            'fx',\n            'data',\n            'draganddrop'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, proxy = $.proxy, Transition = kendo.effects.Transition, Pane = kendo.ui.Pane, PaneDimensions = kendo.ui.PaneDimensions, Widget = ui.DataBoundWidget, DataSource = kendo.data.DataSource, math = Math, abs = math.abs, ceil = math.ceil, round = math.round, max = math.max, min = math.min, floor = math.floor, CHANGE = 'change', CLICK = 'click', CHANGING = 'changing', REFRESH = 'refresh', CURRENT_PAGE_CLASS = 'primary', VIRTUAL_PAGE_CLASS = 'scrollview-page', FUNCTION = 'function', ITEM_CHANGE = 'itemChange', CLEANUP = 'cleanup', VIRTUAL_PAGE_COUNT = 3, LEFT_PAGE = -1, CETER_PAGE = 0, RIGHT_PAGE = 1, LEFT_SWIPE = -1, NUDGE = 0, RIGHT_SWIPE = 1;\n        function className(name) {\n            return 'k-' + name;\n        }\n        var ScrollViewDataReader = kendo.Observable.extend({\n            init: function (dataSource) {\n                var that = this;\n                this.dataSource = dataSource;\n                this.pendingRequestArray = [];\n                this.initialFetch = false;\n                this.useRanges = dataSource.options.serverPaging;\n                kendo.Observable.fn.init.call(this);\n                dataSource.bind('change', function () {\n                    that._change();\n                });\n            },\n            _change: function () {\n                this.trigger('reset', { offset: this.offset });\n            },\n            page: function (page, callback) {\n                var that = this;\n                if (!this.useRanges) {\n                    this.dataSource.page(page + 1);\n                    if (callback) {\n                        callback(that.dataSource.view());\n                    } else {\n                        that.trigger('page', { page: page });\n                    }\n                }\n                if (this.useRanges) {\n                    this.dataSource.range(page * this.dataSource.pageSize(), this.dataSource.pageSize(), function () {\n                        if (callback) {\n                            callback(that.dataSource.view());\n                        } else {\n                            that.trigger('page', { page: page });\n                        }\n                    });\n                }\n            },\n            scrollTo: function (page) {\n                var pageCount = Math.ceil(this.dataSource.total() / this.dataSource.pageSize() || 1);\n                var prevPage = page - 1;\n                var prevPrefetch = prevPage - 1;\n                var currentPage = page;\n                var nextPage = pageCount > 0 && page + 1 >= pageCount ? -1 : page + 1;\n                var nextPrefetch = pageCount > 0 && nextPage + 1 >= pageCount ? -1 : nextPage + 1;\n                if (nextPage >= 0) {\n                    this.pendingRequestArray.push(nextPage);\n                }\n                if (prevPage >= 0) {\n                    this.pendingRequestArray.push(prevPage);\n                }\n                if (prevPrefetch >= 0) {\n                    this.pendingRequestArray.push(prevPrefetch);\n                }\n                if (nextPrefetch >= 0) {\n                    this.pendingRequestArray.push(nextPrefetch);\n                }\n                this.page(currentPage);\n            },\n            getViewData: function () {\n                var view = this.dataSource.view();\n                var data;\n                if (this.dataSource.options.pageSize > 1) {\n                    data = [];\n                    for (var index = 0; index < view.length; index++) {\n                        data.push(view[index]);\n                    }\n                } else {\n                    data = view[0];\n                }\n                return data;\n            },\n            destroy: function () {\n                var that = this;\n                that.dataSource.unbind();\n                that.dataSource = null;\n            }\n        });\n        kendo.ui.ScrollViewDataReader = ScrollViewDataReader;\n        var Pager = kendo.Class.extend({\n            init: function (scrollView) {\n                var that = this, element = $('<ul class=\\'' + className('scrollview-nav') + '\\'/>'), navigationWrapElement = $('<div class=\\'' + className('scrollview-nav-wrap') + '\\'></div>');\n                navigationWrapElement.append(element);\n                scrollView._navigationContainer.append(navigationWrapElement);\n                this._changeProxy = proxy(that, '_change');\n                this._refreshProxy = proxy(that, '_refresh');\n                scrollView.bind(CHANGE, this._changeProxy);\n                scrollView.bind(REFRESH, this._refreshProxy);\n                element.on(CLICK, 'li.k-link', proxy(this._click, scrollView));\n                $.extend(that, {\n                    element: element,\n                    scrollView: scrollView\n                });\n            },\n            items: function () {\n                return this.element.children();\n            },\n            _refresh: function (e) {\n                var pageHTML = '';\n                for (var idx = 0; idx < e.pageCount; idx++) {\n                    pageHTML += '<li class=\"k-link\"></li>';\n                }\n                this.element.html(pageHTML);\n                this.items().eq(e.page).addClass(className(CURRENT_PAGE_CLASS));\n                this.scrollView._toggleNavigation({ currentPage: e.page });\n            },\n            _change: function (e) {\n                if (e.isDefaultPrevented()) {\n                    return;\n                }\n                var innerNavigationContainer = this.scrollView._navigationContainer.find('.k-scrollview-nav');\n                var scrollViewWidth = this.scrollView.element.width();\n                var containerOffset = (scrollViewWidth - innerNavigationContainer.width()) / 2;\n                var pageWidth = innerNavigationContainer.find('li.k-link:eq(0)').outerWidth(true) / 2;\n                this.items().removeClass(className(CURRENT_PAGE_CLASS)).eq(e.nextPage).addClass(className(CURRENT_PAGE_CLASS));\n                var itemOffset = this.items().eq(e.nextPage).length > 0 ? this.items().eq(e.nextPage).position().left : 0;\n                if (itemOffset > scrollViewWidth / 2 || itemOffset < innerNavigationContainer.scrollLeft() + scrollViewWidth / 2) {\n                    var translate = 0;\n                    if (itemOffset > scrollViewWidth / 2) {\n                        translate = innerNavigationContainer.scrollLeft() + itemOffset - scrollViewWidth / 2;\n                    } else {\n                        translate = innerNavigationContainer.scrollLeft() - (scrollViewWidth / 2 - itemOffset);\n                    }\n                    translate += containerOffset + pageWidth;\n                    innerNavigationContainer.animate({ 'scrollLeft': translate }, 300);\n                }\n                this.scrollView._toggleNavigation({\n                    currentPage: e.currentPage,\n                    nextPage: e.nextPage\n                });\n            },\n            _click: function (e) {\n                var newPage = $(e.currentTarget).index();\n                this.scrollTo(newPage);\n            },\n            destroy: function () {\n                this.scrollView.unbind(CHANGE, this._changeProxy);\n                this.scrollView.unbind(REFRESH, this._refreshProxy);\n                this.element.off(CLICK);\n                this.element.remove();\n            }\n        });\n        kendo.ui.ScrollViewPager = Pager;\n        var TRANSITION_END = 'transitionEnd', DRAG_START = 'dragStart', DRAG_END = 'dragEnd';\n        var ElasticPane = kendo.Observable.extend({\n            init: function (element, options) {\n                var that = this;\n                kendo.Observable.fn.init.call(this);\n                this.element = element;\n                this.container = element.parent();\n                var movable, transition, userEvents, dimensions, dimension, pane;\n                movable = new kendo.ui.Movable(that.element);\n                transition = new Transition({\n                    axis: 'x',\n                    movable: movable,\n                    onEnd: function () {\n                        that.trigger(TRANSITION_END);\n                    }\n                });\n                userEvents = new kendo.UserEvents(element, {\n                    fastTap: true,\n                    start: function (e) {\n                        if (abs(e.x.velocity) * 2 >= abs(e.y.velocity)) {\n                            userEvents.capture();\n                        } else {\n                            userEvents.cancel();\n                        }\n                        that.trigger(DRAG_START, e);\n                        transition.cancel();\n                    },\n                    allowSelection: true,\n                    end: function (e) {\n                        that.trigger(DRAG_END, e);\n                    }\n                });\n                dimensions = new PaneDimensions({\n                    element: that.element,\n                    container: that.container\n                });\n                dimension = dimensions.x;\n                dimension.bind(CHANGE, function () {\n                    that.trigger(CHANGE);\n                });\n                pane = new Pane({\n                    dimensions: dimensions,\n                    userEvents: userEvents,\n                    movable: movable,\n                    elastic: true\n                });\n                $.extend(that, {\n                    duration: options && options.duration || 1,\n                    movable: movable,\n                    transition: transition,\n                    userEvents: userEvents,\n                    dimensions: dimensions,\n                    dimension: dimension,\n                    pane: pane\n                });\n                this.bind([\n                    TRANSITION_END,\n                    DRAG_START,\n                    DRAG_END,\n                    CHANGE\n                ], options);\n            },\n            size: function () {\n                return {\n                    width: this.dimensions.x.getSize(),\n                    height: this.dimensions.y.getSize()\n                };\n            },\n            total: function () {\n                return this.dimension.getTotal();\n            },\n            offset: function () {\n                return -this.movable.x;\n            },\n            updateDimension: function () {\n                this.dimension.update(true);\n            },\n            refresh: function () {\n                this.dimensions.refresh();\n                this.dimensions.y.enabled = false;\n            },\n            moveTo: function (offset) {\n                this.movable.moveAxis('x', -offset);\n            },\n            transitionTo: function (offset, ease, instant) {\n                if (instant) {\n                    this.moveTo(-offset);\n                } else {\n                    this.transition.moveTo({\n                        location: offset,\n                        duration: this.duration,\n                        ease: ease\n                    });\n                }\n            },\n            destroy: function () {\n                var that = this;\n                that.userEvents.destroy();\n                that.unbind();\n                that.movable = that.tansition = that.dimensions = that.dimension = that.pane = null;\n                that.element.remove();\n            }\n        });\n        kendo.ui.ScrollViewElasticPane = ElasticPane;\n        var ScrollViewContent = kendo.Observable.extend({\n            init: function (element, pane, options) {\n                var that = this;\n                kendo.Observable.fn.init.call(this);\n                that.element = element;\n                that.pane = pane;\n                that._getPages();\n                this.page = 0;\n                this.pageSize = options.pageSize || 1;\n                this.contentHeight = options.contentHeight;\n                this.enablePager = options.enablePager;\n                this.pagerOverlay = options.pagerOverlay;\n            },\n            scrollTo: function (page, instant) {\n                var that = this;\n                if (page == that.page && !instant) {\n                    return;\n                }\n                if (!that.trigger('resize', {\n                        currentPage: this.page,\n                        nextPage: page,\n                        data: undefined\n                    })) {\n                    that.page = page;\n                    that.pane.transitionTo(-page * that.pane.size().width, Transition.easeOutExpo, instant);\n                }\n            },\n            paneMoved: function (swipeType, bounce, callback, instant) {\n                var that = this, pane = that.pane, width = pane.size().width * that.pageSize, approx = round, ease = bounce ? Transition.easeOutBack : Transition.easeOutExpo, snap, nextPage;\n                if (swipeType === LEFT_SWIPE) {\n                    approx = ceil;\n                } else if (swipeType === RIGHT_SWIPE) {\n                    approx = floor;\n                }\n                nextPage = approx(pane.offset() / width);\n                if (nextPage < 0 || nextPage >= that.pageCount) {\n                    var tansition = nextPage < 0 ? 0 : -this.page * this.pane.size().width;\n                    return this.pane.transitionTo(tansition, ease, instant);\n                }\n                snap = max(that.minSnap, min(-nextPage * width, that.maxSnap));\n                if (nextPage != that.page) {\n                    if (callback && callback({\n                            currentPage: that.page,\n                            nextPage: nextPage\n                        })) {\n                        snap = -that.page * pane.size().width;\n                    }\n                }\n                pane.transitionTo(snap, ease, instant);\n            },\n            updatePage: function () {\n                var pane = this.pane, page = round(pane.offset() / pane.size().width);\n                if (page != this.page) {\n                    this.page = page;\n                    return true;\n                }\n                return false;\n            },\n            forcePageUpdate: function () {\n                return this.updatePage();\n            },\n            resizeTo: function (size) {\n                var pane = this.pane, width = size.width;\n                this.pageElements.width(width);\n                if (this.contentHeight === '100%') {\n                    var containerHeight = this.element.parent().height();\n                    if (this.enablePager === true) {\n                        var pager = this.element.parent().find('ul.k-scrollview-nav');\n                        if (!this.pagerOverlay && pager.length) {\n                            containerHeight -= kendo._outerHeight(pager, true);\n                        }\n                    }\n                    this.element.css('height', containerHeight);\n                    this.pageElements.css('height', containerHeight);\n                }\n                pane.updateDimension();\n                if (!this._paged) {\n                    this.page = floor(pane.offset() / width);\n                }\n                this.scrollTo(this.page, true, true);\n                this.pageCount = floor(pane.total() / width);\n                this.minSnap = -(this.pageCount - 1) * width;\n                this.maxSnap = 0;\n            },\n            _getPages: function () {\n                this.pageElements = this.element.find(kendo.roleSelector('page'));\n                this._paged = this.pageElements.length > 0;\n            },\n            destroy: function () {\n                var that = this;\n                that.pane = null;\n                that.element.remove();\n            }\n        });\n        kendo.ui.ScrollViewContent = ScrollViewContent;\n        var VirtualScrollViewContent = kendo.Observable.extend({\n            init: function (element, pane, options) {\n                var that = this;\n                kendo.Observable.fn.init.call(this);\n                that.element = element;\n                that.pane = pane;\n                that.options = options;\n                that._templates();\n                that.page = options.page || 0;\n                that.pages = [];\n                that._initPages();\n                that.resizeTo(that.pane.size());\n                that.pane.dimension.forceEnabled();\n            },\n            setDataSource: function (dataSource) {\n                this.dataSource = DataSource.create(dataSource);\n                this._dataReader();\n                this._pendingPageRefresh = false;\n                this._pendingWidgetRefresh = false;\n            },\n            _viewShow: function () {\n                var that = this;\n                if (that._pendingWidgetRefresh) {\n                    setTimeout(function () {\n                        that._resetPages();\n                    }, 0);\n                    that._pendingWidgetRefresh = false;\n                }\n            },\n            _dataReader: function () {\n                this.dataReader = new ScrollViewDataReader(this.dataSource);\n                this._pageProxy = proxy(this, '_onPage');\n                this._resetProxy = proxy(this, '_onReset');\n                this.dataReader.bind({\n                    'page': this._pageProxy,\n                    'reset': this._resetProxy\n                });\n            },\n            _templates: function () {\n                var template = this.options.template, emptyTemplate = this.options.emptyTemplate, templateProxy = {}, emptyTemplateProxy = {};\n                if (typeof template === FUNCTION) {\n                    templateProxy.template = template;\n                    template = '#=this.template(data)#';\n                }\n                this.template = proxy(kendo.template(template), templateProxy);\n                if (typeof emptyTemplate === FUNCTION) {\n                    emptyTemplateProxy.emptyTemplate = emptyTemplate;\n                    emptyTemplate = '#=this.emptyTemplate(data)#';\n                }\n                this.emptyTemplate = proxy(kendo.template(emptyTemplate), emptyTemplateProxy);\n            },\n            _initPages: function () {\n                var pages = this.pages, element = this.element, page;\n                for (var i = 0; i < VIRTUAL_PAGE_COUNT; i++) {\n                    page = new Page(element);\n                    pages.push(page);\n                }\n                this.pane.updateDimension();\n            },\n            resizeTo: function (size) {\n                var pages = this.pages, pane = this.pane;\n                for (var i = 0; i < pages.length; i++) {\n                    pages[i].setWidth(size.width);\n                }\n                if (this.options.contentHeight === 'auto') {\n                    this.element.css('height', this.pages[1].element.height());\n                } else if (this.options.contentHeight === '100%') {\n                    var containerHeight = this.element.parent().height();\n                    if (this.options.enablePager === true) {\n                        var pager = this.element.parent().find('ul.k-scrollview-nav');\n                        if (!this.options.pagerOverlay && pager.length) {\n                            containerHeight -= kendo._outerHeight(pager, true);\n                        }\n                    }\n                    this.element.css('height', containerHeight);\n                    pages[0].element.css('height', containerHeight);\n                    pages[1].element.css('height', containerHeight);\n                    pages[2].element.css('height', containerHeight);\n                } else if (this.options.contentHeight) {\n                    pages[0].element.css('height', this.options.contentHeight);\n                    pages[1].element.css('height', this.options.contentHeight);\n                    pages[2].element.css('height', this.options.contentHeight);\n                }\n                pane.updateDimension();\n                this._repositionPages();\n                this.width = size.width;\n            },\n            scrollTo: function (page, instant, silent) {\n                var that = this;\n                var dataReader = that.dataReader;\n                if (page == that.page && !instant) {\n                    return;\n                }\n                dataReader.page(page, function (data) {\n                    if (silent) {\n                        dataReader.scrollTo(page);\n                        return;\n                    }\n                    if (!that.trigger('resize', {\n                            currentPage: that.page,\n                            nextPage: page,\n                            data: data\n                        })) {\n                        if (!instant) {\n                            dataReader.pagerScroll = page > that.page ? -1 : 1;\n                            that.page = page + dataReader.pagerScroll;\n                        } else {\n                            that.page = page;\n                        }\n                        dataReader.scrollTo(page);\n                    }\n                });\n            },\n            paneMoved: function (swipeType, bounce, callback, instant) {\n                var that = this, pane = that.pane, width = pane.size().width, offset = pane.offset(), thresholdPassed = Math.abs(offset) >= width / 3, ease = bounce ? kendo.effects.Transition.easeOutBack : kendo.effects.Transition.easeOutExpo, isEndReached = that.dataSource.options.serverPaging ? that.page + 2 > that.pageCount : false, nextPage, delta = 0, data, element;\n                if (swipeType === RIGHT_SWIPE) {\n                    if (that.page !== 0) {\n                        delta = -1;\n                    }\n                } else if (swipeType === LEFT_SWIPE && !isEndReached) {\n                    delta = 1;\n                } else if (offset > 0 && (thresholdPassed && !isEndReached)) {\n                    delta = 1;\n                } else if (offset < 0 && thresholdPassed) {\n                    if (that.page !== 0) {\n                        delta = -1;\n                    }\n                }\n                nextPage = that.page;\n                if (delta) {\n                    nextPage = delta > 0 ? nextPage + 1 : nextPage - 1;\n                    if (that instanceof kendo.ui.VirtualScrollViewContent) {\n                        that.dataReader.page(nextPage);\n                        data = that.dataReader.getViewData();\n                    } else {\n                        data = undefined;\n                    }\n                    if (!(data instanceof Array)) {\n                        data = [data];\n                    }\n                    element = that.pages ? that.pages[1].element : undefined;\n                }\n                if (callback && that.page != nextPage && callback({\n                        currentPage: that.page,\n                        nextPage: nextPage,\n                        element: element,\n                        data: data\n                    })) {\n                    delta = 0;\n                }\n                if (delta === 0) {\n                    that._cancelMove(ease, instant);\n                } else if (delta === -1) {\n                    that._moveBackward(instant);\n                } else if (delta === 1) {\n                    that._moveForward(instant);\n                }\n            },\n            updatePage: function () {\n                var pages = this.pages;\n                if (this.pane.offset() === 0) {\n                    return false;\n                }\n                if (this.pane.offset() > 0) {\n                    pages.push(this.pages.shift());\n                    this.page++;\n                    if (this.page + 2 < this.pageCount) {\n                        this.dataReader.pendingRequestArray.push(this.page + 2);\n                    }\n                    if (this.page + 1 < this.pageCount) {\n                        this.dataReader.page(this.page + 1);\n                    }\n                    if (this.page + 1 == this.pageCount) {\n                        this.setPageContent(this.pages[2], null);\n                    }\n                } else {\n                    pages.unshift(this.pages.pop());\n                    this.page--;\n                    if (this.page - 2 >= 0) {\n                        this.dataReader.pendingRequestArray.push(this.page - 2);\n                    }\n                    if (this.page - 1 >= 0) {\n                        this.dataReader.page(this.page - 1);\n                    }\n                }\n                this._repositionPages();\n                this._resetMovable();\n                return true;\n            },\n            forcePageUpdate: function () {\n                var offset = this.pane.offset(), threshold = this.pane.size().width * 3 / 4;\n                if (abs(offset) > threshold) {\n                    return this.updatePage();\n                }\n                return false;\n            },\n            _resetMovable: function () {\n                this.pane.moveTo(0);\n            },\n            _moveForward: function (instant) {\n                this.pane.transitionTo(-this.width, kendo.effects.Transition.easeOutExpo, instant);\n            },\n            _moveBackward: function (instant) {\n                this.pane.transitionTo(this.width, kendo.effects.Transition.easeOutExpo, instant);\n            },\n            _cancelMove: function (ease, instant) {\n                this.pane.transitionTo(0, ease, instant);\n            },\n            _resetPages: function () {\n                this.page = this.options.page || 0;\n                this._repositionPages();\n                this.trigger('reset');\n            },\n            _onPage: function (e) {\n                if (e.page >= this.pageCount) {\n                    this.setPageContent(this.pages[2], null);\n                }\n                if (this.page == e.page) {\n                    if (!this.dataReader.pagerScroll || this.dataReader.pagerScroll === 0 && this.dataReader.initialFetch) {\n                        this.setPageContent(this.pages[1], this.dataReader.getViewData());\n                    } else {\n                        if (this.dataReader.pagerScroll < 0) {\n                            this._moveForward();\n                        } else {\n                            this._moveBackward();\n                        }\n                        this.dataReader.pagerScroll = 0;\n                        this.setPageContent(this.pages[1], this.dataReader.getViewData());\n                    }\n                } else if (this.page + 1 == e.page) {\n                    this.setPageContent(this.pages[2], this.dataReader.getViewData());\n                } else if (this.page - 1 == e.page) {\n                    this.setPageContent(this.pages[0], this.dataReader.getViewData());\n                }\n                if (this.dataReader.pendingRequestArray.length > 0 && this.dataReader.initialFetch) {\n                    var item = this.dataReader.pendingRequestArray.shift();\n                    this.dataReader.page(item);\n                }\n            },\n            _onReset: function () {\n                this.pageCount = ceil(this.dataSource.total() / this.dataSource.pageSize());\n            },\n            _repositionPages: function () {\n                var pages = this.pages;\n                pages[0].position(LEFT_PAGE);\n                pages[1].position(CETER_PAGE);\n                pages[2].position(RIGHT_PAGE);\n            },\n            setPageContent: function (page, data) {\n                var template = this.template, emptyTemplate = this.emptyTemplate;\n                if (data !== null && data !== undefined) {\n                    page.content(template(data));\n                } else {\n                    page.content(emptyTemplate({}));\n                }\n            },\n            destroy: function () {\n                var that = this;\n                var pages = that.pages;\n                that.dataReader.unbind();\n                that.dataSource.unbind();\n                that.dataReader = that.dataSource = that.pane = null;\n                for (var index = 0; index < pages.length; index++) {\n                    pages[index].destroy();\n                }\n                that.element.remove();\n            }\n        });\n        kendo.ui.VirtualScrollViewContent = VirtualScrollViewContent;\n        var Page = kendo.Class.extend({\n            init: function (container) {\n                this.element = $('<li class=\\'' + className(VIRTUAL_PAGE_CLASS) + '\\'></li>');\n                this.width = container.width();\n                this.element.width(this.width);\n                container.append(this.element);\n            },\n            content: function (theContent) {\n                this.element.html(theContent);\n            },\n            position: function (position) {\n                this.element.css('transform', 'translate3d(' + this.width * position + 'px, 0, 0)');\n            },\n            setWidth: function (width) {\n                this.width = width;\n                this.element.width(width);\n            },\n            destroy: function () {\n                var that = this;\n                that.element.remove();\n                that.element = null;\n            }\n        });\n        kendo.ui.VirtualPage = Page;\n        var ScrollView = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                element = that.element;\n                kendo.stripWhitespace(element[0]);\n                if (element.children().length === 0) {\n                    element.wrapInner('<ul class=\\'k-scrollview-wrap\\'/>');\n                } else {\n                    element.wrapInner('<div class=\\'k-scrollview-wrap\\'/>');\n                }\n                element.addClass('k-widget ' + className('scrollview'));\n                that._initNavigation();\n                if (this.options.enablePager) {\n                    this.pager = new Pager(this);\n                    if (this.options.pagerOverlay) {\n                        element.addClass(className('scrollview-overlay'));\n                    }\n                } else {\n                    this._changeProxy = proxy(that, '_toggleNavigation');\n                    this.bind(CHANGE, this._changeProxy);\n                }\n                that.inner = element.children().first();\n                that.page = 0;\n                that.inner.css('height', options.contentHeight);\n                that.pane = new ElasticPane(that.inner, {\n                    duration: this.options.duration,\n                    transitionEnd: proxy(this, '_transitionEnd'),\n                    dragStart: proxy(this, '_dragStart'),\n                    dragEnd: proxy(this, '_dragEnd'),\n                    change: proxy(this, REFRESH)\n                });\n                that.bind('resize', function () {\n                    that.pane.refresh();\n                });\n                that.page = options.page;\n                var empty = this.inner.children().length === 0;\n                var content = empty ? new VirtualScrollViewContent(that.inner, that.pane, options) : new ScrollViewContent(that.inner, that.pane, options);\n                content.page = that.page;\n                content.bind('reset', function () {\n                    this._pendingPageRefresh = false;\n                    that.trigger(REFRESH, {\n                        pageCount: content.pageCount,\n                        page: content.page\n                    });\n                    that._toggleNavigation({\n                        currentPage: content.page,\n                        nextPage: content.page\n                    });\n                });\n                content.bind('resize', function (e) {\n                    var currentPage = content.page;\n                    var nextPage = e.nextPage;\n                    if (currentPage != nextPage) {\n                        e._defaultPrevented = that.trigger(CHANGE, {\n                            currentPage: content.page,\n                            nextPage: e.nextPage,\n                            data: e.data\n                        });\n                    }\n                    that._toggleNavigation({\n                        currentPage: content.page,\n                        nextPage: e.nextPage\n                    });\n                });\n                content.bind(ITEM_CHANGE, function (e) {\n                    that.trigger(ITEM_CHANGE, e);\n                    that.angular('compile', function () {\n                        return {\n                            elements: e.item,\n                            data: [{ dataItem: e.data }]\n                        };\n                    });\n                });\n                content.bind(CLEANUP, function (e) {\n                    that.angular('cleanup', function () {\n                        return { elements: e.item };\n                    });\n                });\n                that._content = content;\n                that.setDataSource(options.dataSource);\n                this.viewInit();\n                this.viewShow();\n            },\n            options: {\n                name: 'ScrollView',\n                page: 0,\n                duration: 400,\n                velocityThreshold: 0.8,\n                contentHeight: 'auto',\n                pageSize: 1,\n                bounceVelocityThreshold: 1.6,\n                enablePager: true,\n                enableNavigationButtons: true,\n                pagerOverlay: true,\n                autoBind: true,\n                template: '',\n                emptyTemplate: ''\n            },\n            events: [\n                CHANGING,\n                CHANGE,\n                REFRESH\n            ],\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this._content.destroy();\n                this.pane.destroy();\n                if (this.pager) {\n                    this.pager.destroy();\n                }\n                this.inner = null;\n                kendo.destroy(this.element);\n            },\n            viewInit: function () {\n                if (this.options.autoBind) {\n                    this._content.scrollTo(this._content.page, true, true);\n                }\n            },\n            viewShow: function () {\n                this.pane.refresh();\n            },\n            refresh: function () {\n                var content = this._content;\n                var options = this.options;\n                content.resizeTo(this.pane.size());\n                this.page = content.page;\n                if (content instanceof ScrollViewContent || content.dataReader.initialFetch) {\n                    if (options.enablePager) {\n                        this.trigger(REFRESH, {\n                            pageCount: content.pageCount,\n                            page: content.page\n                        });\n                    } else {\n                        this.trigger(CHANGE, {\n                            pageCount: content.pageCount,\n                            currentPage: content.page\n                        });\n                    }\n                }\n            },\n            content: function (html) {\n                this.element.children().first().html(html);\n                this._content._getPages();\n                this.pane.refresh();\n            },\n            scrollTo: function (page, instant, silent) {\n                this._content.scrollTo(page, instant, silent);\n            },\n            prev: function () {\n                var that = this, prevPage = that._content.page - 1;\n                if (that._content instanceof VirtualScrollViewContent) {\n                    that._content.paneMoved(RIGHT_SWIPE, undefined, function (eventData) {\n                        return that.trigger(CHANGE, eventData);\n                    });\n                } else if (prevPage > -1) {\n                    that.scrollTo(prevPage);\n                }\n            },\n            next: function () {\n                var that = this, nextPage = that._content.page + 1;\n                if (that._content instanceof VirtualScrollViewContent) {\n                    that._content.paneMoved(LEFT_SWIPE, undefined, function (eventData) {\n                        return that.trigger(CHANGE, eventData);\n                    });\n                } else if (nextPage < that._content.pageCount) {\n                    that.scrollTo(nextPage);\n                }\n            },\n            setDataSource: function (dataSource) {\n                var that = this;\n                if (!(this._content instanceof VirtualScrollViewContent)) {\n                    return;\n                }\n                var emptyDataSource = !dataSource;\n                if (dataSource instanceof DataSource) {\n                    dataSource.options.pageSize = dataSource.options.pageSize || 1;\n                    this.dataSource = dataSource = new DataSource(dataSource.options);\n                } else {\n                    this.dataSource = DataSource.create(dataSource);\n                }\n                this._content.setDataSource(this.dataSource);\n                if (this.options.autoBind && !emptyDataSource) {\n                    this.dataSource.fetch(function () {\n                        that._content.dataReader.initialFetch = true;\n                        that.scrollTo(that._content.page, true, true);\n                        that._content.trigger('reset');\n                    });\n                }\n            },\n            items: function () {\n                return this.element.find('.k-' + VIRTUAL_PAGE_CLASS);\n            },\n            _dragStart: function () {\n                this._content.forcePageUpdate();\n            },\n            _dragEnd: function (e) {\n                var that = this, velocity = e.x.velocity, velocityThreshold = this.options.velocityThreshold, swipeType = NUDGE, bounce = abs(velocity) > this.options.bounceVelocityThreshold;\n                if (velocity > velocityThreshold) {\n                    swipeType = RIGHT_SWIPE;\n                } else if (velocity < -velocityThreshold) {\n                    swipeType = LEFT_SWIPE;\n                }\n                this._content.paneMoved(swipeType, bounce, function (eventData) {\n                    return that.trigger(CHANGE, eventData);\n                });\n            },\n            _transitionEnd: function () {\n                this._content.updatePage();\n            },\n            _initNavigation: function () {\n                var that = this;\n                var navigationContainer = that._navigationContainer = $('<div class=\\'k-scrollview-elements\\'></div>');\n                var prevArrow = $('<a class=\"k-scrollview-prev\"><span class=\"k-icon k-i-arrowhead-w\"></span></a>').hide();\n                var nextArrow = $('<a class=\"k-scrollview-next\"><span class=\"k-icon k-i-arrowhead-e\"></span></a>').hide();\n                navigationContainer.append(prevArrow);\n                navigationContainer.append(nextArrow);\n                that.element.append(navigationContainer);\n                navigationContainer.on(CLICK, 'a.k-scrollview-prev', proxy(that.prev, that));\n                navigationContainer.on(CLICK, 'a.k-scrollview-next', proxy(that.next, that));\n            },\n            _toggleNavigation: function (e) {\n                var page = e.nextPage || e.nextPage === 0 ? e.nextPage : e.currentPage;\n                var navigationContainer = this._navigationContainer;\n                var prevArrow = navigationContainer.find('>a.k-scrollview-prev');\n                var nextArrow = navigationContainer.find('>a.k-scrollview-next');\n                prevArrow.hide();\n                nextArrow.hide();\n                if (page || page === 0) {\n                    if (page !== 0) {\n                        prevArrow.show();\n                    }\n                    if (page != this._content.pageCount - 1) {\n                        nextArrow.show();\n                    }\n                }\n            }\n        });\n        ui.plugin(ScrollView);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}