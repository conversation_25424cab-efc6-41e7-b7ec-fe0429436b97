{"version": 3, "sources": ["kendo.binder.js"], "names": ["f", "define", "$", "undefined", "dataSourceBinding", "bindingName", "fieldName", "setter", "Binder", "extend", "init", "widget", "bindings", "options", "that", "this", "fn", "call", "element", "_dataBinding", "proxy", "dataBinding", "_dataBound", "dataBound", "_itemChange", "itemChange", "e", "bindElement", "item", "data", "_ns", "ns", "concat", "_parents", "idx", "length", "items", "removedItems", "unbindElementTree", "kendo", "ui", "all", "dataviz", "mobile", "splice", "inArray", "unshift", "rolesFromNamespaces", "view", "parents", "addedItems", "dataSource", "hds", "HierarchicalDataSource", "addedDataItems", "flatView", "refresh", "source", "select", "multiselect", "dropdowntree", "action", "destroy", "bind", "get", "DataSource", "_dataSource", "Select", "MultiSelect", "DropDownTree", "treeview", "value", "retrievePrimitiveValues", "dataValueField", "unbind", "bindingTargetForRole", "roles", "initWidget", "WidgetBindingTarget", "parseBindings", "token", "colonIndex", "key", "tokens", "result", "match", "keyValueRegExp", "indexOf", "substring", "char<PERSON>t", "createBindings", "type", "binding", "role", "childrenCopy", "deep", "target", "children", "getAttribute", "unbindElement", "replace", "whiteSpaceRegExp", "parseOptions", "textField", "valueField", "template", "valueUpdate", "CHANGE", "valuePrimitive", "autoBind", "BindingTarget", "Binding", "TemplateBinding", "click", "events", "attr", "style", "EventBinding", "css", "kendoBindingTarget", "dom", "object", "node", "slice", "arguments", "observable", "nodeType", "destroyWidget", "<PERSON><PERSON><PERSON><PERSON>", "deleteExpando", "removeAttribute", "widgetInstance", "FUNCTION", "destroyWidgets", "unbind<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notify", "namespace", "values", "ObservableArray", "ObservableObject", "TypedBinder", "arraySplice", "window", "Observable", "toString", "binders", "Class", "VALUE", "SOURCE", "EVENTS", "CHECKED", "CSS", "a", "document", "createElement", "test", "path", "dependencies", "_access", "field", "_change", "change", "parent", "dependency", "ch", "trigger", "start", "stop", "index", "lastIndexOf", "currentSource", "set", "getter", "handler", "render", "html", "attribute", "dataType", "toLowerCase", "parsedValue", "_parseValue", "parseDate", "parseFloat", "setAttribute", "classes", "className", "hasClass", "addClass", "removeClass", "enabled", "readonly", "disabled", "handlers", "off", "on", "text", "dataFormat", "visible", "display", "invisible", "innerHTML", "eventName", "_initChange", "fetch", "add", "remove", "container", "nodeName", "tBodies", "append<PERSON><PERSON><PERSON>", "format", "child", "clone", "cloneNode", "reference", "insertBefore", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "input", "checked", "i", "Date", "push", "val", "selectedIndex", "option", "selected", "attributes", "specified", "valueIndex", "sourceValue", "String", "apply", "optionIndex", "optionValue", "found", "check", "range", "end", "_range", "wrapper", "enable", "first", "_valueIsObservableObject", "_valueIsObservableArray", "_source", "dataItem", "valueLength", "sourceItem", "dataTextField", "isArray", "isObservableObject", "cascadeFrom", "listView", "bound", "_preselect", "<PERSON><PERSON><PERSON><PERSON>", "j", "old", "same", "removeIndex", "newValue", "oldValues", "selectedNode", "nonPrimitiveValues", "_isMultipleSelection", "_getAllChecked", "newValues", "selected<PERSON><PERSON><PERSON>", "gantt", "dataItems", "_isBound", "scheduler", "elements", "grid", "arr", "add<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "shifted", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "hasValue", "hasSource", "hasEvents", "hasChecked", "has<PERSON>s", "widgetBinding", "specificBinders", "applyBinding", "name", "binder", "Error", "observableHierarchy", "array", "recursiveRead", "_initC<PERSON><PERSON>n", "create", "_data", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,gBACH,aACA,cACDD,IACL,WAihDE,MAtgDC,UAAUE,EAAGC,GA4oBV,QAASC,GAAkBC,EAAaC,EAAWC,GAC/C,MAAOC,GAAOC,QACVC,KAAM,SAAUC,EAAQC,EAAUC,GAC9B,GAAIC,GAAOC,IACXP,GAAOQ,GAAGN,KAAKO,KAAKH,EAAMH,EAAOO,QAAQ,GAAIN,EAAUC,GACvDC,EAAKH,OAASA,EACdG,EAAKK,aAAeC,EAAMN,EAAKO,YAAaP,GAC5CA,EAAKQ,WAAaF,EAAMN,EAAKS,UAAWT,GACxCA,EAAKU,YAAcJ,EAAMN,EAAKW,WAAYX,IAE9CW,WAAY,SAAUC,GAClBC,EAAYD,EAAEE,KAAK,GAAIF,EAAEG,KAAMd,KAAKe,IAAIJ,EAAEK,KAAML,EAAEG,MAAMG,OAAOjB,KAAKH,SAASP,GAAa4B,cAE9FZ,YAAa,SAAUK,GACnB,GAAIQ,GAAKC,EAAQxB,EAASI,KAAKJ,OAAQyB,EAAQV,EAAEW,cAAgB1B,EAAOyB,OACxE,KAAKF,EAAM,EAAGC,EAASC,EAAMD,OAAQD,EAAMC,EAAQD,IAC/CI,EAAkBF,EAAMF,IAAM,IAGtCJ,IAAK,SAAUC,GACXA,EAAKA,GAAMQ,EAAMC,EACjB,IAAIC,IACAF,EAAMC,GACND,EAAMG,QAAQF,GACdD,EAAMI,OAAOH,GAIjB,OAFAC,GAAIG,OAAO1C,EAAE2C,QAAQd,EAAIU,GAAM,GAC/BA,EAAIK,QAAQf,GACLQ,EAAMQ,oBAAoBN,IAErClB,UAAW,SAAUG,GACjB,GAAIQ,GAAKC,EAAsGa,EAAMC,EAApGtC,EAASI,KAAKJ,OAAQyB,EAAQV,EAAEwB,YAAcvC,EAAOyB,QAASe,EAAaxC,EAAOL,GAA2B8C,EAAMb,EAAMV,KAAKwB,sBAC/I,MAAID,GAAOD,YAAsBC,KAG7BhB,EAAMD,OAGN,IAFAa,EAAOtB,EAAE4B,gBAAkBH,EAAWI,WACtCN,EAAUlC,KAAKH,SAASP,GAAa4B,WAChCC,EAAM,EAAGC,EAASa,EAAKb,OAAQD,EAAMC,EAAQD,IAC1CE,EAAMF,IACNP,EAAYS,EAAMF,GAAMc,EAAKd,GAAMnB,KAAKe,IAAIJ,EAAEK,KAAMiB,EAAKd,IAAMF,OAAOiB,KAKtFO,QAAS,SAAU9B,GACf,GAAiB+B,GAA8BC,EAAQC,EAAaC,EAAhE9C,EAAOC,KAAcJ,EAASG,EAAKH,MACvCe,GAAIA,MACCA,EAAEmC,SACH/C,EAAKgD,UACLnD,EAAOoD,KAAK,cAAejD,EAAKK,cAChCR,EAAOoD,KAAK,YAAajD,EAAKQ,YAC9BX,EAAOoD,KAAK,aAAcjD,EAAKU,aAC/BiC,EAAS3C,EAAKF,SAASP,GAAa2D,MAChCrD,EAAOL,YAAsBiC,GAAMV,KAAKoC,YAActD,EAAOL,IAAcmD,IACvEA,YAAkBlB,GAAMV,KAAKoC,WAC7BtD,EAAOJ,GAAQkD,GACRA,GAAUA,EAAOS,YACxBvD,EAAOJ,GAAQkD,EAAOS,cAEtBR,EAASnB,EAAMC,GAAG2B,QAAUxD,YAAkB4B,GAAMC,GAAG2B,OACvDR,EAAcpB,EAAMC,GAAG4B,aAAezD,YAAkB4B,GAAMC,GAAG4B,YACjER,EAAerB,EAAMC,GAAG6B,cAAgB1D,YAAkB4B,GAAMC,GAAG6B,aAC9DT,EAGDjD,EAAO2D,SAAShE,GAAWuB,KAAK4B,GAFhC9C,EAAOL,GAAWuB,KAAK4B,GAIvB3C,EAAKF,SAAS2D,QAAUb,GAAUC,IAClChD,EAAO4D,MAAMC,EAAwB1D,EAAKF,SAAS2D,MAAMP,MAAOrD,EAAOE,QAAQ4D,qBAMnGX,QAAS,WACL,GAAInD,GAASI,KAAKJ,MAClBA,GAAO+D,OAAO,cAAe3D,KAAKI,cAClCR,EAAO+D,OAAO,YAAa3D,KAAKO,YAChCX,EAAO+D,OAAO,aAAc3D,KAAKS,gBAomB7C,QAASmD,GAAqBzD,EAAS0D,GACnC,GAAIjE,GAAS4B,EAAMsC,WAAW3D,KAAa0D,EAC3C,IAAIjE,EACA,MAAO,IAAImE,GAAoBnE,GAIvC,QAASoE,GAAchB,GACnB,GAAiB7B,GAAKC,EAAQ6C,EAAOC,EAAYC,EAAKX,EAAOY,EAAzDC,IAEJ,KADAD,EAASpB,EAAKsB,MAAMC,GACfpD,EAAM,EAAGC,EAASgD,EAAOhD,OAAQD,EAAMC,EAAQD,IAChD8C,EAAQG,EAAOjD,GACf+C,EAAaD,EAAMO,QAAQ,KAC3BL,EAAMF,EAAMQ,UAAU,EAAGP,GACzBV,EAAQS,EAAMQ,UAAUP,EAAa,GACd,KAAnBV,EAAMkB,OAAO,KACblB,EAAQQ,EAAcR,IAE1Ba,EAAOF,GAAOX,CAElB,OAAOa,GAEX,QAASM,GAAe9E,EAAU6C,EAAQkC,GACtC,GAAIC,GAASR,IACb,KAAKQ,IAAWhF,GACZwE,EAAOQ,GAAW,GAAID,GAAKlC,EAAQ7C,EAASgF,GAEhD,OAAOR,GAEX,QAASzD,GAAYT,EAASuC,EAAQmB,EAAO3B,GAA7C,GAIQ4C,GAA0D3D,EAAK6B,EAA0D+B,EAAmBC,EAAanF,EAAUC,EAAcmF,EAqDjLC,CAxDJ,IAAK/E,IAAWA,EAAQgF,aAAa,QAAU3D,EAAMR,GAAK,UAGtD8D,EAAO3E,EAAQgF,aAAa,QAAU3D,EAAMR,GAAK,QAAcgC,EAAO7C,EAAQgF,aAAa,QAAU3D,EAAMR,GAAK,QAAS+D,KAAmBC,GAAO,EAAgBlF,KACvKoC,EAAUA,IAAYQ,IAClBoC,GAAQ9B,IACRoC,EAAcjF,GAAS,GAEvB2E,IACAG,EAASrB,EAAqBzD,EAAS0D,IAEvCb,IACAA,EAAOgB,EAAchB,EAAKqC,QAAQC,EAAkB,KAC/CL,IACDnF,EAAU0B,EAAM+D,aAAapF,GACzBqF,UAAW,GACXC,WAAY,GACZC,SAAU,GACVC,YAAaC,EACbC,gBAAgB,EAChBC,UAAU,GACXpD,GACH5C,EAAQ+D,MAAQA,EAChBoB,EAAS,GAAIc,GAAc5F,EAASL,IAExCmF,EAAOvC,OAASA,EAChB7C,EAAW8E,EAAe3B,EAAMd,EAAS8D,GACrClG,EAAQ4F,WACR7F,EAAS6F,SAAW,GAAIO,GAAgB/D,EAAS,GAAIpC,EAAQ4F,WAE7D7F,EAASqG,QACTlD,EAAKmD,OAASnD,EAAKmD,WACnBnD,EAAKmD,OAAOD,MAAQlD,EAAKkD,MACzBrG,EAASqG,MAAMnD,gBACRlD,GAASqG,OAEhBrG,EAAS6C,SACTsC,GAAO,GAEPhC,EAAKoD,OACLvG,EAASuG,KAAOzB,EAAe3B,EAAKoD,KAAMlE,EAAS8D,IAEnDhD,EAAKqD,QACLxG,EAASwG,MAAQ1B,EAAe3B,EAAKqD,MAAOnE,EAAS8D,IAErDhD,EAAKmD,SACLtG,EAASsG,OAASxB,EAAe3B,EAAKmD,OAAQjE,EAASoE,IAEvDtD,EAAKuD,MACL1G,EAAS0G,IAAM5B,EAAe3B,EAAKuD,IAAKrE,EAAS8D,IAErDf,EAAOjC,KAAKnD,IAEZoF,IACA9E,EAAQqG,mBAAqBvB,GAE7BC,EAAW/E,EAAQ+E,SACnBF,GAAQE,GAAU,CAClB,IAAK/D,EAAM,EAAGA,EAAM+D,EAAS9D,OAAQD,IACjC4D,EAAa5D,GAAO+D,EAAS/D,EAEjC,KAAKA,EAAM,EAAGA,EAAM4D,EAAa3D,OAAQD,IACrCP,EAAYmE,EAAa5D,GAAMuB,EAAQmB,EAAO3B,IAI1D,QAASc,GAAKyD,EAAKC,GACf,GAAIvF,GAAKC,EAAQuF,EAAM9C,EAAQrC,EAAMQ,uBAAuB4E,MAAM1G,KAAK2G,UAAW,GAGlF,KAFAH,EAASlF,EAAMsF,WAAWJ,GAC1BD,EAAMtH,EAAEsH,GACHtF,EAAM,EAAGC,EAASqF,EAAIrF,OAAQD,EAAMC,EAAQD,IAC7CwF,EAAOF,EAAItF,GACW,IAAlBwF,EAAKI,UACLnG,EAAY+F,EAAMD,EAAQ7C,GAItC,QAASuB,GAAcjF,EAAS6G,GAAhC,GAaYpH,GAZJqH,EAAgB9G,EAAQqG,kBACxBS,KACAA,EAAclE,UACVmE,QACO/G,GAAQqG,mBACRrG,EAAQgH,gBACfhH,EAAQgH,gBAAgB,sBAExBhH,EAAQqG,mBAAqB,MAGjCQ,IACIpH,EAAS4B,EAAM4F,eAAejI,EAAEgB,IAChCP,SAAiBA,GAAOmD,UAAYsE,GACpCzH,EAAOmD,WAInB,QAASxB,GAAkBpB,EAASmH,GAChClC,EAAcjF,EAASmH,GACvBC,EAAsBpH,EAASmH,GAEnC,QAASC,GAAsBpH,EAASmH,GAAxC,GAGiBnG,GAASC,EAFlB8D,EAAW/E,EAAQ+E,QACvB,IAAIA,EACA,IAAS/D,EAAM,EAAGC,EAAS8D,EAAS9D,OAAQD,EAAMC,EAAQD,IACtDI,EAAkB2D,EAAS/D,GAAMmG,GAI7C,QAAS3D,GAAO8C,GACZ,GAAItF,GAAKC,CAET,KADAqF,EAAMtH,EAAEsH,GACHtF,EAAM,EAAGC,EAASqF,EAAIrF,OAAQD,EAAMC,EAAQD,IAC7CI,EAAkBkF,EAAItF,IAAM,GAGpC,QAASqG,GAAO5H,EAAQ6H,GACpB,GAAItH,GAAUP,EAAOO,QAAS8G,EAAgB9G,EAAQ,GAAGqG,kBACrDS,IACAjE,EAAK7C,EAAS8G,EAAcvE,OAAQ+E,GAG5C,QAAShE,GAAwBD,EAAOiC,GAAxC,GAGQrE,GACAP,EAHA6G,KACAvG,EAAM,CAGV,KAAKsE,EACD,MAAOjC,EAEX,IAAIA,YAAiBmE,GAAiB,CAClC,IAAKvG,EAASoC,EAAMpC,OAAQD,EAAMC,EAAQD,IACtCN,EAAO2C,EAAMrC,GACbuG,EAAOvG,GAAON,EAAKoC,IAAMpC,EAAKoC,IAAIwC,GAAc5E,EAAK4E,EAEzDjC,GAAQkE,MACDlE,aAAiBoE,KACxBpE,EAAQA,EAAMP,IAAIwC,GAEtB,OAAOjC,GAv+Cd,GAUOwC,GAsHAM,EAaAL,EAcAxG,EAiBAoI,EAmhCAC,EAsCA/B,EAmEAhC,EA6BAQ,EAA0De,EAp0C1D9D,EAAQuG,OAAOvG,MAAOwG,EAAaxG,EAAMwG,WAAYJ,EAAmBpG,EAAMV,KAAK8G,iBAAkBD,EAAkBnG,EAAMV,KAAK6G,gBAAiBM,KAAcA,SAAUC,KAAcC,EAAQ3G,EAAM2G,MAAO9H,EAAQlB,EAAEkB,MAAO+H,EAAQ,QAASC,EAAS,SAAUC,EAAS,SAAUC,EAAU,UAAWC,EAAM,MAAOtB,GAAgB,EAAMG,EAAW,WAAYzB,EAAS,UACjX,WACG,GAAI6C,GAAIC,SAASC,cAAc,IAC/B,WACWF,GAAEG,KACX,MAAOjI,GACLuG,GAAgB,MAGpBlB,EAAUgC,EAAWtI,QACrBC,KAAM,SAAUuC,EAAS2G,GACrB,GAAI9I,GAAOC,IACXgI,GAAW/H,GAAGN,KAAKO,KAAKH,GACxBA,EAAK2C,OAASR,EAAQ,GACtBnC,EAAKmC,QAAUA,EACfnC,EAAK8I,KAAOA,EACZ9I,EAAK+I,gBACL/I,EAAK+I,aAAaD,IAAQ,EAC1B9I,EAAK+G,WAAa/G,EAAK2C,iBAAkBsF,GACzCjI,EAAKgJ,QAAU,SAAUpI,GACrBZ,EAAK+I,aAAanI,EAAEqI,QAAS,GAE7BjJ,EAAK+G,aACL/G,EAAKkJ,QAAU,SAAUtI,GACrBZ,EAAKmJ,OAAOvI,IAEhBZ,EAAK2C,OAAOM,KAAK4C,EAAQ7F,EAAKkJ,WAGtC/H,SAAU,WAAA,GAIEiI,GAHJjH,EAAUlC,KAAKkC,QACfsB,EAAQxD,KAAKiD,KAOjB,OANIO,IAAgC,kBAAhBA,GAAM2F,SAClBA,EAAS3F,EAAM2F,SACfhK,EAAE2C,QAAQqH,EAAQjH,GAAW,IAC7BA,GAAWiH,GAAQlI,OAAOiB,KAG3BA,GAEXgH,OAAQ,SAAUvI,GACd,GAAIyI,GAAYC,EAAIL,EAAQrI,EAAEqI,MAAOjJ,EAAOC,IAC5C,IAAkB,SAAdD,EAAK8I,KACL9I,EAAKuJ,QAAQ1D,EAAQjF,OAErB,KAAKyI,IAAcrJ,GAAK+I,aACpB,GAAkC,IAA9BM,EAAW5E,QAAQwE,KACnBK,EAAKD,EAAW1E,OAAOsE,EAAM5H,SACxBiI,GAAa,MAAPA,GAAqB,MAAPA,GAAY,CACjCtJ,EAAKuJ,QAAQ1D,EAAQjF,EACrB,SAMpB4I,MAAO,SAAU7G,GACbA,EAAOM,KAAK,MAAOhD,KAAK+I,UAE5BS,KAAM,SAAU9G,GACZA,EAAOiB,OAAO,MAAO3D,KAAK+I,UAE9B9F,IAAK,WACD,GAAIlD,GAAOC,KAAM0C,EAAS3C,EAAK2C,OAAQ+G,EAAQ,EAAGZ,EAAO9I,EAAK8I,KAAMxE,EAAS3B,CAC7E,KAAK3C,EAAK+G,WACN,MAAOzC,EAIX,KAFAtE,EAAKwJ,MAAMxJ,EAAK2C,QAChB2B,EAAS3B,EAAOO,IAAI4F,GACbxE,IAAWjF,GAAasD,GAC3BA,EAAS3C,EAAKmC,UAAUuH,GACpB/G,YAAkBkF,KAClBvD,EAAS3B,EAAOO,IAAI4F,GAG5B,IAAIxE,IAAWjF,EAEX,IADAsD,EAAS3C,EAAK2C,OACP2B,IAAWjF,GAAasD,GAC3BA,EAASA,EAAOyG,SACZzG,YAAkBkF,KAClBvD,EAAS3B,EAAOO,IAAI4F,GAsBhC,OAlBsB,kBAAXxE,KACPoF,EAAQZ,EAAKa,YAAY,KACrBD,EAAQ,IACR/G,EAASA,EAAOO,IAAI4F,EAAKpE,UAAU,EAAGgF,KAE1C1J,EAAKwJ,MAAM7G,GAEP2B,EADA3B,IAAW3C,EAAK2C,OACP2B,EAAOnE,KAAKwC,EAAQ3C,EAAK2C,QAEzB2B,EAAOnE,KAAKwC,GAEzB3C,EAAKyJ,KAAK9G,IAEVA,GAAUA,IAAW3C,EAAK2C,SAC1B3C,EAAK4J,cAAgBjH,EACrBA,EAAOiB,OAAOiC,EAAQ7F,EAAKkJ,SAASjG,KAAK4C,EAAQ7F,EAAKkJ,UAE1DlJ,EAAKyJ,KAAKzJ,EAAK2C,QACR2B,GAEXuF,IAAK,SAAUpG,GAAV,GACGd,GAAS1C,KAAK2J,eAAiB3J,KAAK0C,OACpCsG,EAAQxH,EAAMqI,OAAO7J,KAAK6I,MAAMnG,EACf,mBAAVsG,GACHtG,IAAW1C,KAAK0C,OAChBsG,EAAM9I,KAAKwC,EAAQ1C,KAAK0C,OAAQc,GAEhCwF,EAAM9I,KAAKwC,EAAQc,GAGvBd,EAAOkH,IAAI5J,KAAK6I,KAAMrF,IAG9BT,QAAS,WACD/C,KAAK8G,aACL9G,KAAK0C,OAAOiB,OAAOiC,EAAQ5F,KAAKiJ,SAC5BjJ,KAAK2J,eACL3J,KAAK2J,cAAchG,OAAOiC,EAAQ5F,KAAKiJ,UAG/CjJ,KAAK2D,YAGT2C,EAAeN,EAAQtG,QACvBuD,IAAK,WACD,GAAuD6G,GAAnDpH,EAAS1C,KAAK0C,OAAQmG,EAAO7I,KAAK6I,KAAMY,EAAQ,CAEpD,KADAK,EAAUpH,EAAOO,IAAI4F,IACbiB,GAAWpH,GACfA,EAAS1C,KAAKkC,UAAUuH,GACpB/G,YAAkBkF,KAClBkC,EAAUpH,EAAOO,IAAI4F,GAG7B,OAAOxI,GAAMyJ,EAASpH,MAG1BuD,EAAkBD,EAAQtG,QAC1BC,KAAM,SAAU+C,EAAQmG,EAAMnD,GAC1B,GAAI3F,GAAOC,IACXgG,GAAQ/F,GAAGN,KAAKO,KAAKH,EAAM2C,EAAQmG,GACnC9I,EAAK2F,SAAWA,GAEpBqE,OAAQ,SAAUvG,GACd,GAAIwG,EAIJ,OAHAhK,MAAKuJ,MAAMvJ,KAAK0C,QAChBsH,EAAOxI,EAAMuI,OAAO/J,KAAK0F,SAAUlC,GACnCxD,KAAKwJ,KAAKxJ,KAAK0C,QACRsH,KAGXvK,EAAS0I,EAAMzI,QACfC,KAAM,SAAUQ,EAASN,EAAUC,GAC/BE,KAAKG,QAAUA,EACfH,KAAKH,SAAWA,EAChBG,KAAKF,QAAUA,GAEnBkD,KAAM,SAAU6B,EAASoF,GACrB,GAAIlK,GAAOC,IACX6E,GAAUoF,EAAYpF,EAAQoF,GAAapF,EAC3CA,EAAQ7B,KAAK4C,EAAQ,SAAUjF,GAC3BZ,EAAK0C,QAAQwH,GAAatJ,KAE9BZ,EAAK0C,QAAQwH,IAEjBlH,QAAS,eAGT8E,EAAcpI,EAAOC,QACrBwK,SAAU,WACN,GAAIA,GAAWlK,KAAKG,QAAQgF,aAAa,cAAgBnF,KAAKG,QAAQyE,MAAQ,MAC9E,OAAOsF,GAASC,eAEpBC,YAAa,WACT,MAAOpK,MAAKqK,YAAYrK,KAAKG,QAAQqD,MAAOxD,KAAKkK,aAErDG,YAAa,SAAU7G,EAAO0G,GAkB1B,MAjBgB,QAAZA,EACA1G,EAAQhC,EAAM8I,UAAU9G,EAAO,cACZ,kBAAZ0G,EACP1G,EAAQhC,EAAM8I,UAAU9G,GACpB,sBACA,qBAEe,UAAZ0G,EACP1G,EAAQhC,EAAM+I,WAAW/G,GACN,WAAZ0G,IACP1G,EAAQA,EAAM2G,cAEV3G,EAD4B,OAA5BhC,EAAM+I,WAAW/G,KACDhC,EAAM+I,WAAW/G,GAED,SAAxBA,EAAM2G,eAGf3G,KAGf0E,EAAQ9B,KAAO3G,EAAOC,QAClB+C,QAAS,SAAU0B,GACfnE,KAAKG,QAAQqK,aAAarG,EAAKnE,KAAKH,SAASuG,KAAKjC,GAAKlB,UAG/DiF,EAAQ3B,IAAM9G,EAAOC,QACjBC,KAAM,SAAUQ,EAASN,EAAUC,GAC/BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMG,EAASN,EAAUC,GAC7CE,KAAKyK,YAEThI,QAAS,SAAUiI,GACf,GAAIvK,GAAUhB,EAAEa,KAAKG,SAAU0E,EAAU7E,KAAKH,SAAS0G,IAAImE,GAAYC,EAAW3K,KAAKyK,QAAQC,GAAa7F,EAAQ5B,KAChH0H,GACAxK,EAAQyK,SAASF,GAEjBvK,EAAQ0K,YAAYH,MAIhCxC,EAAQ7B,MAAQ5G,EAAOC,QACnB+C,QAAS,SAAU0B,GACfnE,KAAKG,QAAQkG,MAAMlC,GAAOnE,KAAKH,SAASwG,MAAMlC,GAAKlB,OAAS,MAGpEiF,EAAQ4C,QAAUrL,EAAOC,QACrB+C,QAAS,WACDzC,KAAKH,SAASiL,QAAQ7H,MACtBjD,KAAKG,QAAQgH,gBAAgB,YAE7BnH,KAAKG,QAAQqK,aAAa,WAAY,eAIlDtC,EAAQ6C,SAAWtL,EAAOC,QACtB+C,QAAS,WACDzC,KAAKH,SAASkL,SAAS9H,MACvBjD,KAAKG,QAAQqK,aAAa,WAAY,YAEtCxK,KAAKG,QAAQgH,gBAAgB,eAIzCe,EAAQ8C,SAAWvL,EAAOC,QACtB+C,QAAS,WACDzC,KAAKH,SAASmL,SAAS/H,MACvBjD,KAAKG,QAAQqK,aAAa,WAAY,YAEtCxK,KAAKG,QAAQgH,gBAAgB,eAIzCe,EAAQ/B,OAAS1G,EAAOC,QACpBC,KAAM,SAAUQ,EAASN,EAAUC,GAC/BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMG,EAASN,EAAUC,GAC7CE,KAAKiL,aAETxI,QAAS,SAAU0B,GACf,GAAIhE,GAAUhB,EAAEa,KAAKG,SAAU0E,EAAU7E,KAAKH,SAASsG,OAAOhC,GAAM2F,EAAU9J,KAAKiL,SAAS9G,EACxF2F,IACA3J,EAAQ+K,IAAI/G,EAAK2F,GAErBA,EAAU9J,KAAKiL,SAAS9G,GAAOU,EAAQ5B,MACvC9C,EAAQgL,GAAGhH,EAAKU,EAAQnC,OAAQoH,IAEpC/G,QAAS,WACL,GAA+B+G,GAA3B3J,EAAUhB,EAAEa,KAAKG,QACrB,KAAK2J,IAAW9J,MAAKiL,SACjB9K,EAAQ+K,IAAIpB,EAAS9J,KAAKiL,SAASnB,OAI/C5B,EAAQkD,KAAO3L,EAAOC,QAClB+C,QAAS,WAAA,GACD2I,GAAOpL,KAAKH,SAASuL,KAAKnI,MAC1BoI,EAAarL,KAAKG,QAAQgF,aAAa,gBAAkB,EACjD,OAARiG,IACAA,EAAO,IAEXjM,EAAEa,KAAKG,SAASiL,KAAK5J,EAAMyG,SAASmD,EAAMC,OAGlDnD,EAAQoD,QAAU7L,EAAOC,QACrB+C,QAAS,WAEDzC,KAAKG,QAAQkG,MAAMkF,QADnBvL,KAAKH,SAASyL,QAAQrI,MACO,GAEA,UAIzCiF,EAAQsD,UAAY/L,EAAOC,QACvB+C,QAAS,WAIDzC,KAAKG,QAAQkG,MAAMkF,QAHlBvL,KAAKH,SAAS2L,UAAUvI,MAGI,OAFA,MAMzCiF,EAAQ8B,KAAOvK,EAAOC,QAClB+C,QAAS,WACLzC,KAAKG,QAAQsL,UAAYzL,KAAKH,SAASmK,KAAK/G,SAGpDiF,EAAQ1E,MAAQqE,EAAYnI,QACxBC,KAAM,SAAUQ,EAASN,EAAUC,GAC/B+H,EAAY5H,GAAGN,KAAKO,KAAKF,KAAMG,EAASN,EAAUC,GAClDE,KAAKiJ,QAAU5I,EAAML,KAAKkJ,OAAQlJ,MAClCA,KAAK0L,UAAY5L,EAAQ6F,aAAeC,EACxCzG,EAAEa,KAAKG,SAASgL,GAAGnL,KAAK0L,UAAW1L,KAAKiJ,SACxCjJ,KAAK2L,aAAc,GAEvBzC,OAAQ,WACJlJ,KAAK2L,YAAc3L,KAAK0L,WAAa9F,EACrC5F,KAAKH,SAASuI,GAAOwB,IAAI5J,KAAKoK,eAC9BpK,KAAK2L,aAAc,GAEvBlJ,QAAS,WAAA,GAEGe,GAIAoB,CALH5E,MAAK2L,cACFnI,EAAQxD,KAAKH,SAASuI,GAAOnF,MACpB,MAATO,IACAA,EAAQ,IAERoB,EAAO5E,KAAKkK,WACJ,QAARtF,EACApB,EAAQhC,EAAMyG,SAASzE,EAAO,cACf,kBAARoB,IACPpB,EAAQhC,EAAMyG,SAASzE,EAAO,wBAElCxD,KAAKG,QAAQqD,MAAQA,GAEzBxD,KAAK2L,aAAc,GAEvB5I,QAAS,WACL5D,EAAEa,KAAKG,SAAS+K,IAAIlL,KAAK0L,UAAW1L,KAAKiJ,YAGjDf,EAAQxF,OAASjD,EAAOC,QACpBC,KAAM,SAAUQ,EAASN,EAAUC,GAC/BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMG,EAASN,EAAUC,EAC7C,IAAI4C,GAAS1C,KAAKH,SAAS6C,OAAOO,KAC9BP,aAAkBlB,GAAMV,KAAKoC,YAAcpD,EAAQgG,YAAa,GAChEpD,EAAOkJ,SAGfnJ,QAAS,SAAU9B,GACf,GAAIZ,GAAOC,KAAM0C,EAAS3C,EAAKF,SAAS6C,OAAOO,KAC3CP,aAAkBiF,IAAmBjF,YAAkBlB,GAAMV,KAAKoC,YAClEvC,EAAIA,MACY,OAAZA,EAAEmC,OACF/C,EAAK8L,IAAIlL,EAAE8I,MAAO9I,EAAEU,OACD,UAAZV,EAAEmC,OACT/C,EAAK+L,OAAOnL,EAAE8I,MAAO9I,EAAEU,OACJ,cAAZV,EAAEmC,QACT/C,EAAKgK,UAGThK,EAAKgK,UAGbgC,UAAW,WACP,GAAI5L,GAAUH,KAAKG,OAOnB,OANsC,SAAlCA,EAAQ6L,SAAS7B,gBACZhK,EAAQ8L,QAAQ,IACjB9L,EAAQ+L,YAAYxD,SAASC,cAAc,UAE/CxI,EAAUA,EAAQ8L,QAAQ,IAEvB9L,GAEXuF,SAAU,WACN,GAAI5F,GAAUE,KAAKF,QAAS4F,EAAW5F,EAAQ4F,SAAUsG,EAAWhM,KAAK+L,YAAYC,SAAS7B,aAiB9F,OAhBKzE,KAGOA,EAFQ,UAAZsG,EACIlM,EAAQ2F,YAAc3F,EAAQ0F,UACnBhE,EAAM2K,OAAO,yCAA0CrM,EAAQ2F,YAAc3F,EAAQ0F,UAAW1F,EAAQ0F,WAAa1F,EAAQ2F,YAE7H,2BAEI,SAAZuG,EACI,4BACQ,MAAZA,GAAgC,MAAZA,EAChB,mBAEA,UAEftG,EAAWlE,EAAMkE,SAASA,IAEvBA,GAEXmG,IAAK,SAAUpC,EAAOpI,GAClB,GAAgCa,GAASf,EAAKC,EAAQgL,EAAlDjM,EAAUH,KAAK+L,YAA0CM,EAAQlM,EAAQmM,WAAU,GAAQC,EAAYpM,EAAQ+E,SAASuE,EAE5H,IADAtK,EAAEkN,GAAOrC,KAAKxI,EAAMuI,OAAO/J,KAAK0F,WAAYrE,IACxCgL,EAAMnH,SAAS9D,OAEf,IADAc,EAAUlC,KAAKH,SAAS6C,OAAOxB,WAC1BC,EAAM,EAAGC,EAASC,EAAMD,OAAQD,EAAMC,EAAQD,IAC/CiL,EAAQC,EAAMnH,SAAS,GACvB/E,EAAQqM,aAAaJ,EAAOG,GAAa,MACzC3L,EAAYwL,EAAO/K,EAAMF,GAAMnB,KAAKF,QAAQ+D,OAAQxC,EAAMF,IAAMF,OAAOiB,KAInF4J,OAAQ,SAAUrC,EAAOpI,GAAjB,GACAF,GAEIiL,EAFCjM,EAAUH,KAAK+L,WACxB,KAAK5K,EAAM,EAAGA,EAAME,EAAMD,OAAQD,IAC1BiL,EAAQjM,EAAQ+E,SAASuE,GAC7BlI,EAAkB6K,GAAO,GACrBA,EAAMK,YAActM,GACpBA,EAAQuM,YAAYN,IAIhCrC,OAAQ,WACJ,GAAyC7H,GAASf,EAAKC,EAAnDsB,EAAS1C,KAAKH,SAAS6C,OAAOO,MAA6B9C,EAAUH,KAAK+L,YAAarG,EAAW1F,KAAK0F,UAC3G,IAAc,MAAVhD,EASJ,GANIA,YAAkBlB,GAAMV,KAAKoC,aAC7BR,EAASA,EAAOT,QAEdS,YAAkBiF,IAA8C,mBAA1BM,EAAS/H,KAAKwC,KACtDA,GAAUA,IAEV1C,KAAKH,SAAS6F,UAGd,GAFA6B,EAAsBpH,GAAS,GAC/BhB,EAAEgB,GAAS6J,KAAKhK,KAAKH,SAAS6F,SAASqE,OAAOrH,IAC1CvC,EAAQ+E,SAAS9D,OAEjB,IADAc,EAAUlC,KAAKH,SAAS6C,OAAOxB,WAC1BC,EAAM,EAAGC,EAASsB,EAAOtB,OAAQD,EAAMC,EAAQD,IAChDP,EAAYT,EAAQ+E,SAAS/D,GAAMuB,EAAOvB,GAAMnB,KAAKF,QAAQ+D,OAAQnB,EAAOvB,IAAMF,OAAOiB,QAIjG/C,GAAEgB,GAAS6J,KAAKxI,EAAMuI,OAAOrE,EAAUhD,OAInDwF,EAAQyE,OACJC,QAAS/E,EAAYnI,QACjBC,KAAM,SAAUQ,EAASN,EAAUC,GAC/B+H,EAAY5H,GAAGN,KAAKO,KAAKF,KAAMG,EAASN,EAAUC,GAClDE,KAAKiJ,QAAU5I,EAAML,KAAKkJ,OAAQlJ,MAClCb,EAAEa,KAAKG,SAAS+I,OAAOlJ,KAAKiJ,UAEhCC,OAAQ,WAAA,GAOIxG,GACA+G,EAIaoD,EAXjB1M,EAAUH,KAAKG,QACfqD,EAAQxD,KAAKwD,OACjB,IAAoB,SAAhBrD,EAAQyE,KACRpB,EAAQxD,KAAKoK,cACbpK,KAAKH,SAAS0I,GAASqB,IAAIpG,OACxB,IAAoB,YAAhBrD,EAAQyE,KAGf,GAFIlC,EAAS1C,KAAKH,SAAS0I,GAAStF,MAEhCP,YAAkBiF,GAAiB,CAEnC,GADAnE,EAAQxD,KAAKoK,cACT5G,YAAiBsJ,OACjB,IAASD,EAAI,EAAGA,EAAInK,EAAOtB,OAAQyL,IAC/B,GAAInK,EAAOmK,YAAcC,QAASpK,EAAOmK,MAAQrJ,EAAO,CACpDiG,EAAQoD,CACR,YAIRpD,GAAQ/G,EAAO8B,QAAQhB,EAEvBiG,MACA/G,EAAOb,OAAO4H,EAAO,GAErB/G,EAAOqK,KAAKvJ,OAGhBxD,MAAKH,SAAS0I,GAASqB,IAAIpG,IAIvCf,QAAS,WAAA,GAIOgH,GAGSoD,EANjBrJ,EAAQxD,KAAKH,SAAS0I,GAAStF,MAAOP,EAASc,EAAOoB,EAAO5E,KAAKkK,WAAY/J,EAAUH,KAAKG,OACjG,IAAoB,YAAhBA,EAAQyE,KACR,GAAIlC,YAAkBiF,GAAiB,CAGnC,GAFI8B,KACJjG,EAAQxD,KAAKoK,cACT5G,YAAiBsJ,OACjB,IAASD,EAAI,EAAGA,EAAInK,EAAOtB,OAAQyL,IAC/B,GAAInK,EAAOmK,YAAcC,QAASpK,EAAOmK,MAAQrJ,EAAO,CACpDiG,EAAQoD,CACR,YAIRpD,GAAQ/G,EAAO8B,QAAQhB,EAE3BrD,GAAQyM,QAAUnD,GAAS,MAE3BtJ,GAAQyM,QAAUlK,MAEC,SAAhBvC,EAAQyE,OACH,QAARA,EACApB,EAAQhC,EAAMyG,SAASzE,EAAO,cACf,kBAARoB,IACPpB,EAAQhC,EAAMyG,SAASzE,EAAO,wBAG9BrD,EAAQyM,QADE,OAAVpJ,GAAmC,IAAVA,GAAyBrD,EAAQqD,QAAUA,GAAAA,IAOhFA,MAAO,WACH,GAAIrD,GAAUH,KAAKG,QAASqD,EAAQrD,EAAQqD,KAI5C,OAHoB,YAAhBrD,EAAQyE,OACRpB,EAAQrD,EAAQyM,SAEbpJ,GAEXT,QAAS,WACL5D,EAAEa,KAAKG,SAAS+K,IAAItF,EAAQ5F,KAAKiJ,aAI7Cf,EAAQvF,QACJD,OAAQwF,EAAQxF,OAAOhD,QACnB+C,QAAS,SAAU9B,GAAV,GAYeqM,GAXhBjN,EAAOC,KAAM0C,EAAS3C,EAAKF,SAAS6C,OAAOO,KAC3CP,aAAkBiF,IAAmBjF,YAAkBlB,GAAMV,KAAKoC,YAClEvC,EAAIA,MACY,OAAZA,EAAEmC,OACF/C,EAAK8L,IAAIlL,EAAE8I,MAAO9I,EAAEU,OACD,UAAZV,EAAEmC,OACT/C,EAAK+L,OAAOnL,EAAE8I,MAAO9I,EAAEU,OACJ,cAAZV,EAAEmC,QAA0BnC,EAAEmC,SAAW1D,IAChDW,EAAKgK,SACDhK,EAAKF,SAAS2D,OACVzD,EAAKF,SAAS2D,QACVwJ,EAAMvJ,EAAwB1D,EAAKF,SAAS2D,MAAMP,MAAO9D,EAAEY,EAAKI,SAASW,KAAK,eACtE,OAARkM,EACAjN,EAAKI,QAAQ8M,iBAEblN,EAAKI,QAAQqD,MAAQwJ,KAMrCjN,EAAKgK,YAIjBvG,MAAOqE,EAAYnI,QACfC,KAAM,SAAUsF,EAAQpF,EAAUC,GAC9B+H,EAAY5H,GAAGN,KAAKO,KAAKF,KAAMiF,EAAQpF,EAAUC,GACjDE,KAAKiJ,QAAU5I,EAAML,KAAKkJ,OAAQlJ,MAClCb,EAAEa,KAAKG,SAAS+I,OAAOlJ,KAAKiJ,UAEhCmB,YAAa,WAAA,GAGL5G,GAAO0J,EAAQ/L,EAAKC,EAFpB8I,EAAWlK,KAAKkK,WAChBxC,IAEJ,KAAKvG,EAAM,EAAGC,EAASpB,KAAKG,QAAQL,QAAQsB,OAAQD,EAAMC,EAAQD,IAC9D+L,EAASlN,KAAKG,QAAQL,QAAQqB,GAC1B+L,EAAOC,WACP3J,EAAQ0J,EAAOE,WAAW5J,MAEtBA,EADAA,GAASA,EAAM6J,UACPH,EAAO1J,MAEP0J,EAAO9B,KAEnB1D,EAAOqF,KAAK/M,KAAKqK,YAAY7G,EAAO0G,IAG5C,OAAOxC,IAEXwB,OAAQ,WAAA,GACqCxG,GAAiHwK,EAAQI,EAAY9J,EAAOrC,EAAKC,EAwB1KmM,EACAjJ,EAzBZoD,KAAavH,EAAUH,KAAKG,QAAiB6I,EAAQhJ,KAAKF,QAAQ2F,YAAczF,KAAKF,QAAQ0F,UAAWK,EAAiB7F,KAAKF,QAAQ+F,cAC1I,KAAK1E,EAAM,EAAGC,EAASjB,EAAQL,QAAQsB,OAAQD,EAAMC,EAAQD,IACzD+L,EAAS/M,EAAQL,QAAQqB,GACrB+L,EAAOC,WACP3J,EAAQ0J,EAAOE,WAAW5J,MAEtBA,EADAA,GAASA,EAAM6J,UACPH,EAAO1J,MAEP0J,EAAO9B,KAGf1D,EAAOqF,KADP/D,EACYxF,EAEAxD,KAAKqK,YAAY7G,EAAOxD,KAAKkK,aAIrD,IAAIlB,EAKA,IAJAtG,EAAS1C,KAAKH,SAAS6C,OAAOO,MAC1BP,YAAkBlB,GAAMV,KAAKoC,aAC7BR,EAASA,EAAOT,QAEfqL,EAAa,EAAGA,EAAa5F,EAAOtG,OAAQkM,IAC7C,IAAKnM,EAAM,EAAGC,EAASsB,EAAOtB,OAAQD,EAAMC,EAAQD,IAGhD,GAFIoM,EAAc7K,EAAOvB,GAAK8B,IAAI+F,GAC9B1E,EAAeiJ,EAAPC,KAAwB9F,EAAO4F,GAChC,CACP5F,EAAO4F,GAAc5K,EAAOvB,EAC5B,OAKhBqC,EAAQxD,KAAKH,SAASuI,GAAOnF,MACzBO,YAAiBmE,GACjBnE,EAAM3B,OAAO4L,MAAMjK,GACf,EACAA,EAAMpC,QACRH,OAAOyG,IAIT1H,KAAKH,SAASuI,GAAOwB,IAHb/D,KAAmBrC,YAAiBoE,IAA8B,OAAVpE,GAAkBA,IAAUpE,IAAc4J,EAGjFtB,EAAO,GAAGzE,IAAI+F,GAFdtB,EAAO,KAKxCjF,QAAS,WAAA,GACDiL,GAAsNC,EAKjNL,EALQnN,EAAUH,KAAKG,QAASL,EAAUK,EAAQL,QAAS0D,EAAQxD,KAAKH,SAASuI,GAAOnF,MAAOyE,EAASlE,EAAOwF,EAAQhJ,KAAKF,QAAQ2F,YAAczF,KAAKF,QAAQ0F,UAAWoI,GAAQ,EAAOhJ,EAAO5E,KAAKkK,UAK9M,KAJMxC,YAAkBC,KACpBD,EAAS,GAAIC,IAAiBnE,KAElCrD,EAAQ8M,iBACCK,EAAa,EAAGA,EAAa5F,EAAOtG,OAAQkM,IAUjD,IATA9J,EAAQkE,EAAO4F,GACXtE,GAASxF,YAAiBoE,KAC1BpE,EAAQA,EAAMP,IAAI+F,IAEV,QAARpE,EACApB,EAAQhC,EAAMyG,SAASP,EAAO4F,GAAa,cAC5B,kBAAR1I,IACPpB,EAAQhC,EAAMyG,SAASP,EAAO4F,GAAa,wBAE1CI,EAAc,EAAGA,EAAc5N,EAAQsB,OAAQsM,IAChDC,EAAc7N,EAAQ4N,GAAalK,MACf,KAAhBmK,GAAgC,KAAVnK,IACtBmK,EAAc7N,EAAQ4N,GAAatC,MAE1B,MAAT5H,GAAiBmK,GAAenK,GAAAA,IAChC1D,EAAQ4N,GAAaP,UAAW,EAChCS,GAAQ,IAKxB7K,QAAS,WACL5D,EAAEa,KAAKG,SAAS+K,IAAItF,EAAQ5F,KAAKiJ,aAuF7Cf,EAAQtI,QACJuG,OAAQ1G,EAAOC,QACXC,KAAM,SAAUC,EAAQC,EAAUC,GAC9BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAAOO,QAAQ,GAAIN,EAAUC,GACvDE,KAAKJ,OAASA,EACdI,KAAKiL,aAETxI,QAAS,SAAU0B,GACf,GAAIU,GAAU7E,KAAKH,SAASsG,OAAOhC,GAAM2F,EAAU9J,KAAKiL,SAAS9G,EAC7D2F,IACA9J,KAAKJ,OAAO+D,OAAOQ,EAAK2F,GAE5BA,EAAUjF,EAAQ5B,MAClBjD,KAAKiL,SAAS9G,GAAO,SAAUxD,GAC3BA,EAAEG,KAAO+D,EAAQnC,OACjBoH,EAAQnJ,GACJA,EAAEG,OAAS+D,EAAQnC,cACZ/B,GAAEG,MAGjBd,KAAKJ,OAAOoD,KAAKmB,EAAKnE,KAAKiL,SAAS9G,KAExCpB,QAAS,WACL,GAAI+G,EACJ,KAAKA,IAAW9J,MAAKiL,SACjBjL,KAAKJ,OAAO+D,OAAOmG,EAAS9J,KAAKiL,SAASnB,OAItD8C,QAASnN,EAAOC,QACZC,KAAM,SAAUC,EAAQC,EAAUC,GAC9BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAAOO,QAAQ,GAAIN,EAAUC,GACvDE,KAAKJ,OAASA,EACdI,KAAKiJ,QAAU5I,EAAML,KAAKkJ,OAAQlJ,MAClCA,KAAKJ,OAAOoD,KAAK4C,EAAQ5F,KAAKiJ,UAElCC,OAAQ,WACJlJ,KAAKH,SAAS0I,GAASqB,IAAI5J,KAAKwD,UAEpCf,QAAS,WACLzC,KAAKJ,OAAOiO,MAAM7N,KAAKH,SAAS0I,GAAStF,SAAU,IAEvDO,MAAO,WACH,GAAIrD,GAAUH,KAAKG,QAASqD,EAAQrD,EAAQqD,KAI5C,OAHa,MAATA,GAA0B,OAATA,GAAuC,YAArBxD,KAAKG,QAAQyE,OAChDpB,EAAQrD,EAAQyM,SAEbpJ,GAEXT,QAAS,WACL/C,KAAKJ,OAAO+D,OAAOiC,EAAQ5F,KAAKiJ,YAGxCM,MAAO9J,EAAOC,QACVC,KAAM,SAAUC,EAAQC,EAAUC,GAC9BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAAOO,QAAQ,GAAIN,EAAUC,GACvDE,KAAKiJ,QAAU5I,EAAML,KAAKkJ,OAAQlJ,MAClCA,KAAKJ,OAASA,EACdI,KAAKJ,OAAOoD,KAAK4C,EAAQ5F,KAAKiJ,UAElCC,OAAQ,WACJlJ,KAAKH,SAAS0J,MAAMK,IAAI5J,KAAKJ,OAAOkO,QAAQvE,QAEhD9G,QAAS,WAAA,GACD1C,GAAOC,KACPuJ,EAAQvJ,KAAKH,SAAS0J,MAAMtG,MAC5B8K,EAAMhO,EAAKH,OAAOoO,OAASjO,EAAKH,OAAOoO,OAAOD,IAAM,IACxD/N,MAAKJ,OAAOkO,OACRvE,MAAOA,EACPwE,IAAKA,KAGbhL,QAAS,WACL/C,KAAKJ,OAAO+D,OAAOiC,EAAQ5F,KAAKiJ,YAGxC8E,IAAKtO,EAAOC,QACRC,KAAM,SAAUC,EAAQC,EAAUC,GAC9BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAAOO,QAAQ,GAAIN,EAAUC,GACvDE,KAAKiJ,QAAU5I,EAAML,KAAKkJ,OAAQlJ,MAClCA,KAAKJ,OAASA,EACdI,KAAKJ,OAAOoD,KAAK4C,EAAQ5F,KAAKiJ,UAElCC,OAAQ,WACJlJ,KAAKH,SAASkO,IAAInE,IAAI5J,KAAKJ,OAAOkO,QAAQC,MAE9CtL,QAAS,WAAA,GACD1C,GAAOC,KACP+N,EAAM/N,KAAKH,SAASkO,IAAI9K,MACxBsG,EAAQxJ,EAAKH,OAAOoO,OAASjO,EAAKH,OAAOoO,OAAOzE,MAAQ,IAC5DvJ,MAAKJ,OAAOkO,OACRvE,MAAOA,EACPwE,IAAKA,KAGbhL,QAAS,WACL/C,KAAKJ,OAAO+D,OAAOiC,EAAQ5F,KAAKiJ,YAGxCqC,QAAS7L,EAAOC,QACZC,KAAM,SAAUC,EAAQC,EAAUC,GAC9BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAAOO,QAAQ,GAAIN,EAAUC,GACvDE,KAAKJ,OAASA,GAElB6C,QAAS,WACL,GAAI6I,GAAUtL,KAAKH,SAASyL,QAAQrI,KACpCjD,MAAKJ,OAAOqO,QAAQ,GAAG5H,MAAMkF,QAAUD,EAAU,GAAK,UAG9DE,UAAW/L,EAAOC,QACdC,KAAM,SAAUC,EAAQC,EAAUC,GAC9BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAAOO,QAAQ,GAAIN,EAAUC,GACvDE,KAAKJ,OAASA,GAElB6C,QAAS,WACL,GAAI+I,GAAYxL,KAAKH,SAAS2L,UAAUvI,KACxCjD,MAAKJ,OAAOqO,QAAQ,GAAG5H,MAAMkF,QAAUC,EAAY,OAAS,MAGpEV,QAASrL,EAAOC,QACZC,KAAM,SAAUC,EAAQC,EAAUC,GAC9BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAAOO,QAAQ,GAAIN,EAAUC,GACvDE,KAAKJ,OAASA,GAElB6C,QAAS,WACDzC,KAAKJ,OAAOsO,QACZlO,KAAKJ,OAAOsO,OAAOlO,KAAKH,SAASiL,QAAQ7H,UAIrD+H,SAAUvL,EAAOC,QACbC,KAAM,SAAUC,EAAQC,EAAUC,GAC9BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAAOO,QAAQ,GAAIN,EAAUC,GACvDE,KAAKJ,OAASA,GAElB6C,QAAS,WACDzC,KAAKJ,OAAOsO,QACZlO,KAAKJ,OAAOsO,QAAQlO,KAAKH,SAASmL,SAAS/H,UAIvDP,OAAQrD,EAAkB,SAAU,aAAc,iBAClDmE,MAAO/D,EAAOC,QACVC,KAAM,SAAUC,EAAQC,EAAUC,GAC9BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAAOO,QAAQ,GAAIN,EAAUC,GACvDE,KAAKJ,OAASA,EACdI,KAAKiJ,QAAU9J,EAAEkB,MAAML,KAAKkJ,OAAQlJ,MACpCA,KAAKJ,OAAOuO,MAAMvI,EAAQ5F,KAAKiJ,QAC/B,IAAIzF,GAAQxD,KAAKH,SAAS2D,MAAMP,KAChCjD,MAAKoO,0BAA4BtO,EAAQ+F,iBAA4B,MAATrC,GAAiBA,YAAiBoE,IAC9F5H,KAAKqO,wBAA0B7K,YAAiBmE,GAChD3H,KAAK2L,aAAc,GAEvB2C,QAAS,WACL,GAAI5L,EACJ,OAAI1C,MAAKJ,OAAO2O,WACZ7L,EAAS1C,KAAKJ,OAAO2O,WACjB7L,GAAUA,YAAkBkF,KACpBlF,IAGZ1C,KAAKH,SAAS6C,SACdA,EAAS1C,KAAKH,SAAS6C,OAAOO,SAE7BP,GAAUA,YAAkBlB,GAAMV,KAAKoC,cACxCR,EAAS1C,KAAKJ,OAAOwC,WAAWI,YAE7BE,IAEXwG,OAAQ,WACJ,GAA6MoE,GAAYkB,EAA0BC,EAAYlB,EAAapM,EAAKC,EAAQsB,EAArRc,EAAQxD,KAAKJ,OAAO4D,QAASwF,EAAQhJ,KAAKF,QAAQ4D,gBAAkB1D,KAAKF,QAAQ4O,cAAeC,EAAmC,mBAAzB1G,EAAS/H,KAAKsD,GAA6BoL,EAAqB5O,KAAKoO,yBAAmD1G,IAEtO,IADA1H,KAAK2L,aAAc,EACf3C,EACA,GAAc,KAAVxF,IAAiBoL,GAAsB5O,KAAKF,QAAQ+F,gBACpDrC,EAAQ,SACL,CAMH,IALAd,EAAS1C,KAAKsO,UACVK,IACAH,EAAchL,EAAMpC,OACpBsG,EAASlE,EAAMoD,MAAM,IAEpBzF,EAAM,EAAGC,EAASsB,EAAOtB,OAAQD,EAAMC,EAAQD,IAGhD,GAFAsN,EAAa/L,EAAOvB,GACpBoM,EAAckB,EAAWxL,IAAI+F,GACzB2F,GACA,IAAKrB,EAAa,EAAGA,EAAakB,EAAalB,IAC3C,GAAIC,GAAe7F,EAAO4F,GAAa,CACnC5F,EAAO4F,GAAcmB,CACrB,YAGL,IAAIlB,GAAe/J,EAAO,CAC7BA,EAAQoL,EAAqBH,EAAalB,CAC1C,OAGJ7F,EAAO,KAEHlE,EADAxD,KAAKqO,wBACG3G,EACDkH,IAAuB5F,EACtBtB,EAAO,GAEPA,EAAO,GAAGzE,IAAI+F,IAKtChJ,KAAKH,SAAS2D,MAAMoG,IAAIpG,GACxBxD,KAAK2L,aAAc,GAEvBlJ,QAAS,WAAA,GAEG7C,GACAE,EACA0F,EACAC,EACAjC,EACA4H,EACAjK,EAASC,EACTsG,CARR,KAAK1H,KAAK2L,YAAa,CAYnB,GAXI/L,EAASI,KAAKJ,OACdE,EAAUF,EAAOE,QACjB0F,EAAY1F,EAAQ4O,cACpBjJ,EAAa3F,EAAQ4D,gBAAkB8B,EACvChC,EAAQxD,KAAKH,SAAS2D,MAAMP,MAC5BmI,EAAOtL,EAAQsL,MAAQ,GACvBjK,EAAM,EACNuG,KACAlE,IAAUpE,IACVoE,EAAQ,MAERiC,EACA,GAAIjC,YAAiBmE,GAAiB,CAClC,IAAKvG,EAASoC,EAAMpC,OAAQD,EAAMC,EAAQD,IACtCuG,EAAOvG,GAAOqC,EAAMrC,GAAK8B,IAAIwC,EAEjCjC,GAAQkE,MACDlE,aAAiBoE,KACxBwD,EAAO5H,EAAMP,IAAIuC,GACjBhC,EAAQA,EAAMP,IAAIwC,GAGtB3F,GAAQgG,YAAa,GAAUhG,EAAQ+O,cAAejP,EAAOkP,UAAalP,EAAOkP,SAASC,QAU1FnP,EAAO4D,MAAMA,IATTgC,IAAcC,GAAe2F,IAC7BA,EAAO5H,GAEN4H,IAAS5H,GAAmB,IAAVA,IAAgB1D,EAAQ+F,eAG3CjG,EAAOoP,WAAWxL,EAAO4H,GAFzBxL,EAAO4D,MAAMA,IAQzBxD,KAAK2L,aAAc,GAEvB5I,QAAS,WACL/C,KAAKJ,OAAO+D,OAAOiC,EAAQ5F,KAAKiJ,YAGxCpG,cACIW,MAAO/D,EAAOC,QACVC,KAAM,SAAUC,EAAQC,EAAUC,GAC9BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAAOO,QAAQ,GAAIN,EAAUC,GACvDE,KAAKJ,OAASA,EACdI,KAAKiJ,QAAU9J,EAAEkB,MAAML,KAAKkJ,OAAQlJ,MACpCA,KAAKJ,OAAOuO,MAAMvI,EAAQ5F,KAAKiJ,SAC/BjJ,KAAK2L,aAAc,GAEvBzC,OAAQ,WAAA,GAMI4C,GACAmD,EACApC,EAAOqC,EACPC,EACAC,EACAC,EACAC,EACA1B,EAZJ7N,EAAOC,KAAMuP,EAAYxP,EAAKF,SAASuI,GAAOnF,MAAO4C,EAAiB9F,EAAKD,QAAQ+F,eAAgB2J,EAAezP,EAAKH,OAAO2D,SAASZ,SAAU8M,EAAqB1P,EAAKH,OAAO8P,uBAAyB3P,EAAKH,OAAO+P,iBAAmB5P,EAAKH,OAAO2D,SAASgL,SAASiB,IAAiBzP,EAAKH,OAAO4D,QAASoM,EAAY/J,GAAkB9F,EAAKH,OAAOE,QAAQgG,YAAa,EAAQ/F,EAAKH,OAAO4D,QAAUiM,EAC3YzG,EAAQhJ,KAAKF,QAAQ4D,gBAAkB1D,KAAKF,QAAQ4O,aAGxD,IAFAkB,EAAYA,EAAUhJ,MAAQgJ,EAAUhJ,MAAM,GAAKgJ,EACnD7P,EAAK4L,aAAc,EACf4D,YAAqB5H,GAAiB,CAStC,IARImE,KACAmD,EAAYW,EAAUxO,OACtByL,EAAI,EAAGqC,EAAI,EACXC,EAAMI,EAAU1C,GAChBuC,GAAO,EAIJD,IAAQ/P,GAAW,CAEtB,IADAwO,GAAQ,EACHsB,EAAI,EAAGA,EAAID,EAAWC,IAQvB,GAPIrJ,EACAuJ,EAAOQ,EAAUV,IAAMC,GAEvBG,EAAWM,EAAUV,GACrBI,EAAWA,EAASrM,IAAMqM,EAASrM,IAAI+F,GAASsG,EAChDF,EAAOE,IAAaH,EAAIlM,IAAMkM,EAAIlM,IAAI+F,GAASmG,IAE/CC,EAAM,CACNQ,EAAU/N,OAAOqN,EAAG,GACpBD,GAAa,EACbrB,GAAQ,CACR,OAGHA,EAKDf,GAAK,GAJLf,EAAOiB,KAAKoC,GACZrH,EAAYyH,EAAW1C,EAAG,GAC1BwC,EAAcxC,GAIlBsC,EAAMI,EAAU1C,GAEpB/E,EAAYyH,EAAWA,EAAUnO,OAAQ,EAAGwO,GACxC9D,EAAO1K,QACPmO,EAAUjG,QAAQ,UACdxG,OAAQ,SACRzB,MAAOyK,EACPrC,MAAO4F,IAGXO,EAAUxO,QACVmO,EAAUjG,QAAQ,UACdxG,OAAQ,MACRzB,MAAOuO,EACPnG,MAAO8F,EAAUnO,OAAS,QAIlCrB,GAAKF,SAASuI,GAAOwB,IAAIgG,EAE7B7P,GAAK4L,aAAc,GAEvBlJ,QAAS,WACL,IAAKzC,KAAK2L,YAAa,CACnB,GAAqKvK,GAAqByO,EAAtL/P,EAAUE,KAAKF,QAASF,EAASI,KAAKJ,OAAQoJ,EAAQlJ,EAAQ4D,gBAAkB5D,EAAQ4O,cAAelL,EAAQxD,KAAKH,SAAS2D,MAAMP,MAAOnC,EAAO0C,EAAOrC,EAAM,EAAWuG,IAC7K,IAAIsB,EACA,GAAIxF,YAAiBmE,GAAiB,CAClC,IAAKvG,EAASoC,EAAMpC,OAAQD,EAAMC,EAAQD,IACtC0O,EAAgBrM,EAAMrC,GACtBuG,EAAOvG,GAAO0O,EAAc5M,IAAM4M,EAAc5M,IAAI+F,GAAS6G,CAEjErM,GAAQkE,MACDlE,aAAiBoE,KACxBpE,EAAQA,EAAMP,IAAI+F,GAGtBlJ,GAAQgG,YAAa,GAAShG,EAAQ+F,kBAAmB,EACzDjG,EAAOoP,WAAWlO,EAAM0C,GAExB5D,EAAO4D,MAAMA,KAIzBT,QAAS,WACL/C,KAAKJ,OAAO+D,OAAOiC,EAAQ5F,KAAKiJ,aAI5C6G,OAAShH,aAAczJ,EAAkB,eAAgB,eAAgB,8BACzEuD,aACIY,MAAO/D,EAAOC,QACVC,KAAM,SAAUC,EAAQC,EAAUC,GAC9BL,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAAOO,QAAQ,GAAIN,EAAUC,GACvDE,KAAKJ,OAASA,EACdI,KAAKiJ,QAAU9J,EAAEkB,MAAML,KAAKkJ,OAAQlJ,MACpCA,KAAKJ,OAAOuO,MAAMvI,EAAQ5F,KAAKiJ,SAC/BjJ,KAAK2L,aAAc,GAEvBzC,OAAQ,WAAA,GAMI4C,GACAmD,EACApC,EAAOqC,EACPC,EACAC,EACAC,EACAC,EACA1B,EAZJ7N,EAAOC,KAAMuP,EAAYxP,EAAKF,SAASuI,GAAOnF,MAAO4C,EAAiB9F,EAAKD,QAAQ+F,eAAgB+J,EAAY/J,EAAiB9F,EAAKH,OAAO4D,QAAUzD,EAAKH,OAAOmQ,YAClK/G,EAAQhJ,KAAKF,QAAQ4D,gBAAkB1D,KAAKF,QAAQ4O,aAGxD,IAFAkB,EAAYA,EAAUhJ,MAAM,GAC5B7G,EAAK4L,aAAc,EACf4D,YAAqB5H,GAAiB,CAStC,IARImE,KACAmD,EAAYW,EAAUxO,OACtByL,EAAI,EAAGqC,EAAI,EACXC,EAAMI,EAAU1C,GAChBuC,GAAO,EAIJD,IAAQ/P,GAAW,CAEtB,IADAwO,GAAQ,EACHsB,EAAI,EAAGA,EAAID,EAAWC,IAQvB,GAPIrJ,EACAuJ,EAAOQ,EAAUV,IAAMC,GAEvBG,EAAWM,EAAUV,GACrBI,EAAWA,EAASrM,IAAMqM,EAASrM,IAAI+F,GAASsG,EAChDF,EAAOE,IAAaH,EAAIlM,IAAMkM,EAAIlM,IAAI+F,GAASmG,IAE/CC,EAAM,CACNQ,EAAU/N,OAAOqN,EAAG,GACpBD,GAAa,EACbrB,GAAQ,CACR,OAGHA,EAKDf,GAAK,GAJLf,EAAOiB,KAAKoC,GACZrH,EAAYyH,EAAW1C,EAAG,GAC1BwC,EAAcxC,GAIlBsC,EAAMI,EAAU1C,GAEpB/E,EAAYyH,EAAWA,EAAUnO,OAAQ,EAAGwO,GACxC9D,EAAO1K,QACPmO,EAAUjG,QAAQ,UACdxG,OAAQ,SACRzB,MAAOyK,EACPrC,MAAO4F,IAGXO,EAAUxO,QACVmO,EAAUjG,QAAQ,UACdxG,OAAQ,MACRzB,MAAOuO,EACPnG,MAAO8F,EAAUnO,OAAS,QAIlCrB,GAAKF,SAASuI,GAAOwB,IAAIgG,EAE7B7P,GAAK4L,aAAc,GAEvBlJ,QAAS,WACL,IAAKzC,KAAK2L,YAAa,CACnB,GAAqKvK,GAAqByO,EAAtL/P,EAAUE,KAAKF,QAASF,EAASI,KAAKJ,OAAQoJ,EAAQlJ,EAAQ4D,gBAAkB5D,EAAQ4O,cAAelL,EAAQxD,KAAKH,SAAS2D,MAAMP,MAAOnC,EAAO0C,EAAOrC,EAAM,EAAWuG,IAI7K,IAHIlE,IAAUpE,IACVoE,EAAQ,MAERwF,EACA,GAAIxF,YAAiBmE,GAAiB,CAClC,IAAKvG,EAASoC,EAAMpC,OAAQD,EAAMC,EAAQD,IACtC0O,EAAgBrM,EAAMrC,GACtBuG,EAAOvG,GAAO0O,EAAc5M,IAAM4M,EAAc5M,IAAI+F,GAAS6G,CAEjErM,GAAQkE,MACDlE,aAAiBoE,KACxBpE,EAAQA,EAAMP,IAAI+F,GAGtBlJ,GAAQgG,YAAa,GAAShG,EAAQ+F,kBAAmB,GAASjG,EAAOoQ,WAGzEpQ,EAAO4D,MAAMA,GAFb5D,EAAOoP,WAAWlO,EAAM0C,KAMpCT,QAAS,WACL/C,KAAKJ,OAAO+D,OAAOiC,EAAQ5F,KAAKiJ,aAI5CgH,WACIvN,OAAQrD,EAAkB,SAAU,aAAc,iBAAiBK,QAC/Dc,UAAW,SAAUG,GAAV,GACHQ,GACAC,EAGAN,EAAMoB,EAFNtC,EAASI,KAAKJ,OACdsQ,EAAWvP,EAAEwB,YAAcvC,EAAOyB,OAEtC,IAAI6O,EAAS9O,OAGT,IAFAN,EAAOH,EAAE4B,gBAAkB3C,EAAOmQ,YAClC7N,EAAUlC,KAAKH,SAAS6C,OAAOxB,WAC1BC,EAAM,EAAGC,EAASN,EAAKM,OAAQD,EAAMC,EAAQD,IAC9CP,EAAYsP,EAAS/O,GAAML,EAAKK,GAAMnB,KAAKe,IAAIJ,EAAEK,KAAMF,EAAKK,IAAMF,OAAOiB,QAM7FiO,MACIzN,OAAQrD,EAAkB,SAAU,aAAc,iBAAiBK,QAC/Dc,UAAW,SAAUG,GACjB,GAAIQ,GAAKC,EAAyEc,EAASpB,EAA1ElB,EAASI,KAAKJ,OAAQsQ,EAAWvP,EAAEwB,YAAcvC,EAAOyB,OACzE,IAAI6O,EAAS9O,OAGT,IAFAN,EAAOH,EAAE4B,gBAAkB3C,EAAOmQ,YAClC7N,EAAUlC,KAAKH,SAAS6C,OAAOxB,WAC1BC,EAAM,EAAGC,EAASN,EAAKM,OAAQD,EAAMC,EAAQD,IAC9CP,EAAYsP,EAAS/O,GAAML,EAAKK,GAAMnB,KAAKe,IAAIJ,EAAEK,KAAMF,EAAKK,IAAMF,OAAOiB,SAO7F4F,EAAc,SAAUsI,EAAKjP,EAAK2K,EAAQD,GAA5B,GAGVwE,GACAC,EACAC,EACAC,EACA/G,CACJ,IAPAoC,EAAMA,MACNC,EAASA,GAAU,EACfuE,EAAYxE,EAAIzK,OAChBkP,EAAYF,EAAIhP,OAChBmP,KAAa3J,MAAM1G,KAAKkQ,EAAKjP,EAAM2K,GACnC0E,EAAgBD,EAAQnP,OAExBiP,EAAW,CAGX,IAFAA,EAAYlP,EAAMkP,EAClB5G,EAAQ,EACDtI,EAAMkP,EAAWlP,IACpBiP,EAAIjP,GAAO0K,EAAIpC,GACfA,GAEJ2G,GAAIhP,OAASiP,MACV,IAAIvE,EAGP,IAFAsE,EAAIhP,OAASD,EACb2K,GAAU3K,EACHA,EAAM2K,SACFsE,KAAMtE,EAGrB,IAAI0E,EAAe,CAGf,IAFAA,EAAgBrP,EAAMqP,EACtB/G,EAAQ,EACDtI,EAAMqP,EAAerP,IACxBiP,EAAIjP,GAAOoP,EAAQ9G,GACnBA,GAEJ2G,GAAIhP,OAASoP,EAGjB,IADArP,EAAMiP,EAAIhP,OACHD,EAAMmP,SACFF,GAAIjP,GACXA,KAGJ4E,EAAgBoC,EAAMzI,QACtBC,KAAM,SAAUsF,EAAQnF,GACpBE,KAAKiF,OAASA,EACdjF,KAAKF,QAAUA,EACfE,KAAKyQ,cAETzN,KAAM,SAAUnD,GACZ,GAAIsE,GAAKuM,EAAUC,EAAWC,EAAWC,EAAYC,EAAQC,EAAgB/Q,eAAgB+D,GAAqBiN,EAAkBhR,KAAKkI,SACzI,KAAK/D,IAAOtE,GACJsE,GAAOiE,EACPsI,GAAW,EACJvM,GAAOkE,EACdsI,GAAY,EACLxM,GAAOmE,GAAWyI,EAElB5M,GAAOoE,EACdsI,GAAa,EACN1M,GAAOqE,EACdsI,GAAS,EAET9Q,KAAKiR,aAAa9M,EAAKtE,EAAUmR,GANjCJ,GAAY,CAShBD,IACA3Q,KAAKiR,aAAa5I,EAAQxI,EAAUmR,GAEpCN,GACA1Q,KAAKiR,aAAa7I,EAAOvI,EAAUmR,GAEnCH,GACA7Q,KAAKiR,aAAa1I,EAAS1I,EAAUmR,GAErCJ,IAAcG,GACd/Q,KAAKiR,aAAa3I,EAAQzI,EAAUmR,GAEpCF,IAAWC,GACX/Q,KAAKiR,aAAazI,EAAK3I,EAAUmR,IAGzC9I,QAAS,WACL,MAAOA,GAAQlI,KAAKiF,OAAO+G,SAAS7B,oBAExC8G,aAAc,SAAUC,EAAMrR,EAAUmR,GACpC,GAAiF/G,GAA7EkH,EAASH,EAAgBE,IAAShJ,EAAQgJ,GAAOT,EAAYzQ,KAAKyQ,UAAsB5L,EAAUhF,EAASqR,EAC/G,IAAIC,EAGA,GAFAA,EAAS,GAAIA,GAAOnR,KAAKiF,OAAQpF,EAAUG,KAAKF,SAChD2Q,EAAU1D,KAAKoE,GACXtM,YAAmBmB,GACnBmL,EAAOnO,KAAK6B,GACZ4L,EAAU1D,KAAKlI,OAEf,KAAKoF,IAAapF,GACdsM,EAAOnO,KAAK6B,EAASoF,GACrBwG,EAAU1D,KAAKlI,EAAQoF,QAG5B,IAAa,aAATiH,EACP,KAAUE,OAAM,OAASF,EAAO,oCAAsClR,KAAKiF,OAAO+G,SAAS7B,cAAgB,aAGnHpH,QAAS,WACL,GAAI5B,GAAKC,EAAQqP,EAAYzQ,KAAKyQ,SAClC,KAAKtP,EAAM,EAAGC,EAASqP,EAAUrP,OAAQD,EAAMC,EAAQD,IACnDsP,EAAUtP,GAAK4B,aAIvBgB,EAAsBgC,EAAcrG,QACpCwI,QAAS,WACL,MAAOA,GAAQtI,OAAOI,KAAKiF,OAAOnF,QAAQoR,KAAK/G,oBAEnD8G,aAAc,SAAUC,EAAMrR,EAAUmR,GACpC,GAAwF/G,GAApFkH,EAASH,EAAgBE,IAAShJ,EAAQtI,OAAOsR,GAAOT,EAAYzQ,KAAKyQ,UAAsB5L,EAAUhF,EAASqR,EACtH,KAAIC,EAaA,KAAUC,OAAM,OAASF,EAAO,oCAAsClR,KAAKiF,OAAOnF,QAAQoR,KAAO,UAVjG,IAFAC,EAAS,GAAIA,GAAOnR,KAAKiF,OAAQpF,EAAUG,KAAKiF,OAAOnF,SACvD2Q,EAAU1D,KAAKoE,GACXtM,YAAmBmB,GACnBmL,EAAOnO,KAAK6B,GACZ4L,EAAU1D,KAAKlI,OAEf,KAAKoF,IAAapF,GACdsM,EAAOnO,KAAK6B,EAASoF,GACrBwG,EAAU1D,KAAKlI,EAAQoF,OAcvC1F,EAAiB,wCAAyCe,EAAmB,MAoKjF9D,EAAMmC,OAASA,EACfnC,EAAMwB,KAAOA,EACbxB,EAAMV,KAAKoH,QAAUA,EACrB1G,EAAMV,KAAKrB,OAASA,EACpB+B,EAAMgG,OAASA,EACfhG,EAAMsF,WAAa,SAAUJ,GAIzB,MAHMA,aAAkBkB,KACpBlB,EAAS,GAAIkB,GAAiBlB,IAE3BA,GAEXlF,EAAM6P,oBAAsB,SAAUC,GAElC,QAASC,GAAczQ,GACnB,GAAI+L,GAAG3H,CACP,KAAK2H,EAAI,EAAGA,EAAI/L,EAAKM,OAAQyL,IACzB/L,EAAK+L,GAAG2E,gBACRtM,EAAWpE,EAAK+L,GAAG3H,SACnBA,EAAS0G,QACT9K,EAAK+L,GAAGxL,MAAQ6D,EAASpE,OACzByQ,EAAczQ,EAAK+L,GAAGxL,OAR9B,GAAIe,GAAaZ,EAAMV,KAAKwB,uBAAuBmP,OAAOH,EAc1D,OAHAlP,GAAWwJ,QACX2F,EAAcnP,EAAWtB,QACzBsB,EAAWsP,MAAMvO,YAAcf,EACxBA,EAAWsP,QAExB3J,OAAOvG,MAAMmQ,QACR5J,OAAOvG,OACE,kBAAVtC,SAAwBA,OAAO0S,IAAM1S,OAAS,SAAU2S,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.binder.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.binder', [\n        'kendo.core',\n        'kendo.data'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'binder',\n        name: 'MVVM',\n        category: 'framework',\n        description: 'Model View ViewModel (MVVM) is a design pattern which helps developers separate the Model (the data) from the View (the UI).',\n        depends: [\n            'core',\n            'data'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Observable = kendo.Observable, ObservableObject = kendo.data.ObservableObject, ObservableArray = kendo.data.ObservableArray, toString = {}.toString, binders = {}, Class = kendo.Class, proxy = $.proxy, VALUE = 'value', SOURCE = 'source', EVENTS = 'events', CHECKED = 'checked', CSS = 'css', deleteExpando = true, FUNCTION = 'function', CHANGE = 'change';\n        (function () {\n            var a = document.createElement('a');\n            try {\n                delete a.test;\n            } catch (e) {\n                deleteExpando = false;\n            }\n        }());\n        var Binding = Observable.extend({\n            init: function (parents, path) {\n                var that = this;\n                Observable.fn.init.call(that);\n                that.source = parents[0];\n                that.parents = parents;\n                that.path = path;\n                that.dependencies = {};\n                that.dependencies[path] = true;\n                that.observable = that.source instanceof Observable;\n                that._access = function (e) {\n                    that.dependencies[e.field] = true;\n                };\n                if (that.observable) {\n                    that._change = function (e) {\n                        that.change(e);\n                    };\n                    that.source.bind(CHANGE, that._change);\n                }\n            },\n            _parents: function () {\n                var parents = this.parents;\n                var value = this.get();\n                if (value && typeof value.parent == 'function') {\n                    var parent = value.parent();\n                    if ($.inArray(parent, parents) < 0) {\n                        parents = [parent].concat(parents);\n                    }\n                }\n                return parents;\n            },\n            change: function (e) {\n                var dependency, ch, field = e.field, that = this;\n                if (that.path === 'this') {\n                    that.trigger(CHANGE, e);\n                } else {\n                    for (dependency in that.dependencies) {\n                        if (dependency.indexOf(field) === 0) {\n                            ch = dependency.charAt(field.length);\n                            if (!ch || ch === '.' || ch === '[') {\n                                that.trigger(CHANGE, e);\n                                break;\n                            }\n                        }\n                    }\n                }\n            },\n            start: function (source) {\n                source.bind('get', this._access);\n            },\n            stop: function (source) {\n                source.unbind('get', this._access);\n            },\n            get: function () {\n                var that = this, source = that.source, index = 0, path = that.path, result = source;\n                if (!that.observable) {\n                    return result;\n                }\n                that.start(that.source);\n                result = source.get(path);\n                while (result === undefined && source) {\n                    source = that.parents[++index];\n                    if (source instanceof ObservableObject) {\n                        result = source.get(path);\n                    }\n                }\n                if (result === undefined) {\n                    source = that.source;\n                    while (result === undefined && source) {\n                        source = source.parent();\n                        if (source instanceof ObservableObject) {\n                            result = source.get(path);\n                        }\n                    }\n                }\n                if (typeof result === 'function') {\n                    index = path.lastIndexOf('.');\n                    if (index > 0) {\n                        source = source.get(path.substring(0, index));\n                    }\n                    that.start(source);\n                    if (source !== that.source) {\n                        result = result.call(source, that.source);\n                    } else {\n                        result = result.call(source);\n                    }\n                    that.stop(source);\n                }\n                if (source && source !== that.source) {\n                    that.currentSource = source;\n                    source.unbind(CHANGE, that._change).bind(CHANGE, that._change);\n                }\n                that.stop(that.source);\n                return result;\n            },\n            set: function (value) {\n                var source = this.currentSource || this.source;\n                var field = kendo.getter(this.path)(source);\n                if (typeof field === 'function') {\n                    if (source !== this.source) {\n                        field.call(source, this.source, value);\n                    } else {\n                        field.call(source, value);\n                    }\n                } else {\n                    source.set(this.path, value);\n                }\n            },\n            destroy: function () {\n                if (this.observable) {\n                    this.source.unbind(CHANGE, this._change);\n                    if (this.currentSource) {\n                        this.currentSource.unbind(CHANGE, this._change);\n                    }\n                }\n                this.unbind();\n            }\n        });\n        var EventBinding = Binding.extend({\n            get: function () {\n                var source = this.source, path = this.path, index = 0, handler;\n                handler = source.get(path);\n                while (!handler && source) {\n                    source = this.parents[++index];\n                    if (source instanceof ObservableObject) {\n                        handler = source.get(path);\n                    }\n                }\n                return proxy(handler, source);\n            }\n        });\n        var TemplateBinding = Binding.extend({\n            init: function (source, path, template) {\n                var that = this;\n                Binding.fn.init.call(that, source, path);\n                that.template = template;\n            },\n            render: function (value) {\n                var html;\n                this.start(this.source);\n                html = kendo.render(this.template, value);\n                this.stop(this.source);\n                return html;\n            }\n        });\n        var Binder = Class.extend({\n            init: function (element, bindings, options) {\n                this.element = element;\n                this.bindings = bindings;\n                this.options = options;\n            },\n            bind: function (binding, attribute) {\n                var that = this;\n                binding = attribute ? binding[attribute] : binding;\n                binding.bind(CHANGE, function (e) {\n                    that.refresh(attribute || e);\n                });\n                that.refresh(attribute);\n            },\n            destroy: function () {\n            }\n        });\n        var TypedBinder = Binder.extend({\n            dataType: function () {\n                var dataType = this.element.getAttribute('data-type') || this.element.type || 'text';\n                return dataType.toLowerCase();\n            },\n            parsedValue: function () {\n                return this._parseValue(this.element.value, this.dataType());\n            },\n            _parseValue: function (value, dataType) {\n                if (dataType == 'date') {\n                    value = kendo.parseDate(value, 'yyyy-MM-dd');\n                } else if (dataType == 'datetime-local') {\n                    value = kendo.parseDate(value, [\n                        'yyyy-MM-ddTHH:mm:ss',\n                        'yyyy-MM-ddTHH:mm'\n                    ]);\n                } else if (dataType == 'number') {\n                    value = kendo.parseFloat(value);\n                } else if (dataType == 'boolean') {\n                    value = value.toLowerCase();\n                    if (kendo.parseFloat(value) !== null) {\n                        value = Boolean(kendo.parseFloat(value));\n                    } else {\n                        value = value.toLowerCase() === 'true';\n                    }\n                }\n                return value;\n            }\n        });\n        binders.attr = Binder.extend({\n            refresh: function (key) {\n                this.element.setAttribute(key, this.bindings.attr[key].get());\n            }\n        });\n        binders.css = Binder.extend({\n            init: function (element, bindings, options) {\n                Binder.fn.init.call(this, element, bindings, options);\n                this.classes = {};\n            },\n            refresh: function (className) {\n                var element = $(this.element), binding = this.bindings.css[className], hasClass = this.classes[className] = binding.get();\n                if (hasClass) {\n                    element.addClass(className);\n                } else {\n                    element.removeClass(className);\n                }\n            }\n        });\n        binders.style = Binder.extend({\n            refresh: function (key) {\n                this.element.style[key] = this.bindings.style[key].get() || '';\n            }\n        });\n        binders.enabled = Binder.extend({\n            refresh: function () {\n                if (this.bindings.enabled.get()) {\n                    this.element.removeAttribute('disabled');\n                } else {\n                    this.element.setAttribute('disabled', 'disabled');\n                }\n            }\n        });\n        binders.readonly = Binder.extend({\n            refresh: function () {\n                if (this.bindings.readonly.get()) {\n                    this.element.setAttribute('readonly', 'readonly');\n                } else {\n                    this.element.removeAttribute('readonly');\n                }\n            }\n        });\n        binders.disabled = Binder.extend({\n            refresh: function () {\n                if (this.bindings.disabled.get()) {\n                    this.element.setAttribute('disabled', 'disabled');\n                } else {\n                    this.element.removeAttribute('disabled');\n                }\n            }\n        });\n        binders.events = Binder.extend({\n            init: function (element, bindings, options) {\n                Binder.fn.init.call(this, element, bindings, options);\n                this.handlers = {};\n            },\n            refresh: function (key) {\n                var element = $(this.element), binding = this.bindings.events[key], handler = this.handlers[key];\n                if (handler) {\n                    element.off(key, handler);\n                }\n                handler = this.handlers[key] = binding.get();\n                element.on(key, binding.source, handler);\n            },\n            destroy: function () {\n                var element = $(this.element), handler;\n                for (handler in this.handlers) {\n                    element.off(handler, this.handlers[handler]);\n                }\n            }\n        });\n        binders.text = Binder.extend({\n            refresh: function () {\n                var text = this.bindings.text.get();\n                var dataFormat = this.element.getAttribute('data-format') || '';\n                if (text == null) {\n                    text = '';\n                }\n                $(this.element).text(kendo.toString(text, dataFormat));\n            }\n        });\n        binders.visible = Binder.extend({\n            refresh: function () {\n                if (this.bindings.visible.get()) {\n                    this.element.style.display = '';\n                } else {\n                    this.element.style.display = 'none';\n                }\n            }\n        });\n        binders.invisible = Binder.extend({\n            refresh: function () {\n                if (!this.bindings.invisible.get()) {\n                    this.element.style.display = '';\n                } else {\n                    this.element.style.display = 'none';\n                }\n            }\n        });\n        binders.html = Binder.extend({\n            refresh: function () {\n                this.element.innerHTML = this.bindings.html.get();\n            }\n        });\n        binders.value = TypedBinder.extend({\n            init: function (element, bindings, options) {\n                TypedBinder.fn.init.call(this, element, bindings, options);\n                this._change = proxy(this.change, this);\n                this.eventName = options.valueUpdate || CHANGE;\n                $(this.element).on(this.eventName, this._change);\n                this._initChange = false;\n            },\n            change: function () {\n                this._initChange = this.eventName != CHANGE;\n                this.bindings[VALUE].set(this.parsedValue());\n                this._initChange = false;\n            },\n            refresh: function () {\n                if (!this._initChange) {\n                    var value = this.bindings[VALUE].get();\n                    if (value == null) {\n                        value = '';\n                    }\n                    var type = this.dataType();\n                    if (type == 'date') {\n                        value = kendo.toString(value, 'yyyy-MM-dd');\n                    } else if (type == 'datetime-local') {\n                        value = kendo.toString(value, 'yyyy-MM-ddTHH:mm:ss');\n                    }\n                    this.element.value = value;\n                }\n                this._initChange = false;\n            },\n            destroy: function () {\n                $(this.element).off(this.eventName, this._change);\n            }\n        });\n        binders.source = Binder.extend({\n            init: function (element, bindings, options) {\n                Binder.fn.init.call(this, element, bindings, options);\n                var source = this.bindings.source.get();\n                if (source instanceof kendo.data.DataSource && options.autoBind !== false) {\n                    source.fetch();\n                }\n            },\n            refresh: function (e) {\n                var that = this, source = that.bindings.source.get();\n                if (source instanceof ObservableArray || source instanceof kendo.data.DataSource) {\n                    e = e || {};\n                    if (e.action == 'add') {\n                        that.add(e.index, e.items);\n                    } else if (e.action == 'remove') {\n                        that.remove(e.index, e.items);\n                    } else if (e.action != 'itemchange') {\n                        that.render();\n                    }\n                } else {\n                    that.render();\n                }\n            },\n            container: function () {\n                var element = this.element;\n                if (element.nodeName.toLowerCase() == 'table') {\n                    if (!element.tBodies[0]) {\n                        element.appendChild(document.createElement('tbody'));\n                    }\n                    element = element.tBodies[0];\n                }\n                return element;\n            },\n            template: function () {\n                var options = this.options, template = options.template, nodeName = this.container().nodeName.toLowerCase();\n                if (!template) {\n                    if (nodeName == 'select') {\n                        if (options.valueField || options.textField) {\n                            template = kendo.format('<option value=\"#:{0}#\">#:{1}#</option>', options.valueField || options.textField, options.textField || options.valueField);\n                        } else {\n                            template = '<option>#:data#</option>';\n                        }\n                    } else if (nodeName == 'tbody') {\n                        template = '<tr><td>#:data#</td></tr>';\n                    } else if (nodeName == 'ul' || nodeName == 'ol') {\n                        template = '<li>#:data#</li>';\n                    } else {\n                        template = '#:data#';\n                    }\n                    template = kendo.template(template);\n                }\n                return template;\n            },\n            add: function (index, items) {\n                var element = this.container(), parents, idx, length, child, clone = element.cloneNode(false), reference = element.children[index];\n                $(clone).html(kendo.render(this.template(), items));\n                if (clone.children.length) {\n                    parents = this.bindings.source._parents();\n                    for (idx = 0, length = items.length; idx < length; idx++) {\n                        child = clone.children[0];\n                        element.insertBefore(child, reference || null);\n                        bindElement(child, items[idx], this.options.roles, [items[idx]].concat(parents));\n                    }\n                }\n            },\n            remove: function (index, items) {\n                var idx, element = this.container();\n                for (idx = 0; idx < items.length; idx++) {\n                    var child = element.children[index];\n                    unbindElementTree(child, true);\n                    if (child.parentNode == element) {\n                        element.removeChild(child);\n                    }\n                }\n            },\n            render: function () {\n                var source = this.bindings.source.get(), parents, idx, length, element = this.container(), template = this.template();\n                if (source == null) {\n                    return;\n                }\n                if (source instanceof kendo.data.DataSource) {\n                    source = source.view();\n                }\n                if (!(source instanceof ObservableArray) && toString.call(source) !== '[object Array]') {\n                    source = [source];\n                }\n                if (this.bindings.template) {\n                    unbindElementChildren(element, true);\n                    $(element).html(this.bindings.template.render(source));\n                    if (element.children.length) {\n                        parents = this.bindings.source._parents();\n                        for (idx = 0, length = source.length; idx < length; idx++) {\n                            bindElement(element.children[idx], source[idx], this.options.roles, [source[idx]].concat(parents));\n                        }\n                    }\n                } else {\n                    $(element).html(kendo.render(template, source));\n                }\n            }\n        });\n        binders.input = {\n            checked: TypedBinder.extend({\n                init: function (element, bindings, options) {\n                    TypedBinder.fn.init.call(this, element, bindings, options);\n                    this._change = proxy(this.change, this);\n                    $(this.element).change(this._change);\n                },\n                change: function () {\n                    var element = this.element;\n                    var value = this.value();\n                    if (element.type == 'radio') {\n                        value = this.parsedValue();\n                        this.bindings[CHECKED].set(value);\n                    } else if (element.type == 'checkbox') {\n                        var source = this.bindings[CHECKED].get();\n                        var index;\n                        if (source instanceof ObservableArray) {\n                            value = this.parsedValue();\n                            if (value instanceof Date) {\n                                for (var i = 0; i < source.length; i++) {\n                                    if (source[i] instanceof Date && +source[i] === +value) {\n                                        index = i;\n                                        break;\n                                    }\n                                }\n                            } else {\n                                index = source.indexOf(value);\n                            }\n                            if (index > -1) {\n                                source.splice(index, 1);\n                            } else {\n                                source.push(value);\n                            }\n                        } else {\n                            this.bindings[CHECKED].set(value);\n                        }\n                    }\n                },\n                refresh: function () {\n                    var value = this.bindings[CHECKED].get(), source = value, type = this.dataType(), element = this.element;\n                    if (element.type == 'checkbox') {\n                        if (source instanceof ObservableArray) {\n                            var index = -1;\n                            value = this.parsedValue();\n                            if (value instanceof Date) {\n                                for (var i = 0; i < source.length; i++) {\n                                    if (source[i] instanceof Date && +source[i] === +value) {\n                                        index = i;\n                                        break;\n                                    }\n                                }\n                            } else {\n                                index = source.indexOf(value);\n                            }\n                            element.checked = index >= 0;\n                        } else {\n                            element.checked = source;\n                        }\n                    } else if (element.type == 'radio') {\n                        if (type == 'date') {\n                            value = kendo.toString(value, 'yyyy-MM-dd');\n                        } else if (type == 'datetime-local') {\n                            value = kendo.toString(value, 'yyyy-MM-ddTHH:mm:ss');\n                        }\n                        if (value !== null && typeof value !== 'undefined' && element.value === value.toString()) {\n                            element.checked = true;\n                        } else {\n                            element.checked = false;\n                        }\n                    }\n                },\n                value: function () {\n                    var element = this.element, value = element.value;\n                    if (element.type == 'checkbox') {\n                        value = element.checked;\n                    }\n                    return value;\n                },\n                destroy: function () {\n                    $(this.element).off(CHANGE, this._change);\n                }\n            })\n        };\n        binders.select = {\n            source: binders.source.extend({\n                refresh: function (e) {\n                    var that = this, source = that.bindings.source.get();\n                    if (source instanceof ObservableArray || source instanceof kendo.data.DataSource) {\n                        e = e || {};\n                        if (e.action == 'add') {\n                            that.add(e.index, e.items);\n                        } else if (e.action == 'remove') {\n                            that.remove(e.index, e.items);\n                        } else if (e.action == 'itemchange' || e.action === undefined) {\n                            that.render();\n                            if (that.bindings.value) {\n                                if (that.bindings.value) {\n                                    var val = retrievePrimitiveValues(that.bindings.value.get(), $(that.element).data('valueField'));\n                                    if (val === null) {\n                                        that.element.selectedIndex = -1;\n                                    } else {\n                                        that.element.value = val;\n                                    }\n                                }\n                            }\n                        }\n                    } else {\n                        that.render();\n                    }\n                }\n            }),\n            value: TypedBinder.extend({\n                init: function (target, bindings, options) {\n                    TypedBinder.fn.init.call(this, target, bindings, options);\n                    this._change = proxy(this.change, this);\n                    $(this.element).change(this._change);\n                },\n                parsedValue: function () {\n                    var dataType = this.dataType();\n                    var values = [];\n                    var value, option, idx, length;\n                    for (idx = 0, length = this.element.options.length; idx < length; idx++) {\n                        option = this.element.options[idx];\n                        if (option.selected) {\n                            value = option.attributes.value;\n                            if (value && value.specified) {\n                                value = option.value;\n                            } else {\n                                value = option.text;\n                            }\n                            values.push(this._parseValue(value, dataType));\n                        }\n                    }\n                    return values;\n                },\n                change: function () {\n                    var values = [], element = this.element, source, field = this.options.valueField || this.options.textField, valuePrimitive = this.options.valuePrimitive, option, valueIndex, value, idx, length;\n                    for (idx = 0, length = element.options.length; idx < length; idx++) {\n                        option = element.options[idx];\n                        if (option.selected) {\n                            value = option.attributes.value;\n                            if (value && value.specified) {\n                                value = option.value;\n                            } else {\n                                value = option.text;\n                            }\n                            if (field) {\n                                values.push(value);\n                            } else {\n                                values.push(this._parseValue(value, this.dataType()));\n                            }\n                        }\n                    }\n                    if (field) {\n                        source = this.bindings.source.get();\n                        if (source instanceof kendo.data.DataSource) {\n                            source = source.view();\n                        }\n                        for (valueIndex = 0; valueIndex < values.length; valueIndex++) {\n                            for (idx = 0, length = source.length; idx < length; idx++) {\n                                var sourceValue = source[idx].get(field);\n                                var match = String(sourceValue) === values[valueIndex];\n                                if (match) {\n                                    values[valueIndex] = source[idx];\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                    value = this.bindings[VALUE].get();\n                    if (value instanceof ObservableArray) {\n                        value.splice.apply(value, [\n                            0,\n                            value.length\n                        ].concat(values));\n                    } else if (!valuePrimitive && (value instanceof ObservableObject || value === null || value === undefined || !field)) {\n                        this.bindings[VALUE].set(values[0]);\n                    } else {\n                        this.bindings[VALUE].set(values[0].get(field));\n                    }\n                },\n                refresh: function () {\n                    var optionIndex, element = this.element, options = element.options, value = this.bindings[VALUE].get(), values = value, field = this.options.valueField || this.options.textField, found = false, type = this.dataType(), optionValue;\n                    if (!(values instanceof ObservableArray)) {\n                        values = new ObservableArray([value]);\n                    }\n                    element.selectedIndex = -1;\n                    for (var valueIndex = 0; valueIndex < values.length; valueIndex++) {\n                        value = values[valueIndex];\n                        if (field && value instanceof ObservableObject) {\n                            value = value.get(field);\n                        }\n                        if (type == 'date') {\n                            value = kendo.toString(values[valueIndex], 'yyyy-MM-dd');\n                        } else if (type == 'datetime-local') {\n                            value = kendo.toString(values[valueIndex], 'yyyy-MM-ddTHH:mm:ss');\n                        }\n                        for (optionIndex = 0; optionIndex < options.length; optionIndex++) {\n                            optionValue = options[optionIndex].value;\n                            if (optionValue === '' && value !== '') {\n                                optionValue = options[optionIndex].text;\n                            }\n                            if (value != null && optionValue == value.toString()) {\n                                options[optionIndex].selected = true;\n                                found = true;\n                            }\n                        }\n                    }\n                },\n                destroy: function () {\n                    $(this.element).off(CHANGE, this._change);\n                }\n            })\n        };\n        function dataSourceBinding(bindingName, fieldName, setter) {\n            return Binder.extend({\n                init: function (widget, bindings, options) {\n                    var that = this;\n                    Binder.fn.init.call(that, widget.element[0], bindings, options);\n                    that.widget = widget;\n                    that._dataBinding = proxy(that.dataBinding, that);\n                    that._dataBound = proxy(that.dataBound, that);\n                    that._itemChange = proxy(that.itemChange, that);\n                },\n                itemChange: function (e) {\n                    bindElement(e.item[0], e.data, this._ns(e.ns), [e.data].concat(this.bindings[bindingName]._parents()));\n                },\n                dataBinding: function (e) {\n                    var idx, length, widget = this.widget, items = e.removedItems || widget.items();\n                    for (idx = 0, length = items.length; idx < length; idx++) {\n                        unbindElementTree(items[idx], false);\n                    }\n                },\n                _ns: function (ns) {\n                    ns = ns || kendo.ui;\n                    var all = [\n                        kendo.ui,\n                        kendo.dataviz.ui,\n                        kendo.mobile.ui\n                    ];\n                    all.splice($.inArray(ns, all), 1);\n                    all.unshift(ns);\n                    return kendo.rolesFromNamespaces(all);\n                },\n                dataBound: function (e) {\n                    var idx, length, widget = this.widget, items = e.addedItems || widget.items(), dataSource = widget[fieldName], view, parents, hds = kendo.data.HierarchicalDataSource;\n                    if (hds && dataSource instanceof hds) {\n                        return;\n                    }\n                    if (items.length) {\n                        view = e.addedDataItems || dataSource.flatView();\n                        parents = this.bindings[bindingName]._parents();\n                        for (idx = 0, length = view.length; idx < length; idx++) {\n                            if (items[idx]) {\n                                bindElement(items[idx], view[idx], this._ns(e.ns), [view[idx]].concat(parents));\n                            }\n                        }\n                    }\n                },\n                refresh: function (e) {\n                    var that = this, source, widget = that.widget, select, multiselect, dropdowntree;\n                    e = e || {};\n                    if (!e.action) {\n                        that.destroy();\n                        widget.bind('dataBinding', that._dataBinding);\n                        widget.bind('dataBound', that._dataBound);\n                        widget.bind('itemChange', that._itemChange);\n                        source = that.bindings[bindingName].get();\n                        if (widget[fieldName] instanceof kendo.data.DataSource && widget[fieldName] != source) {\n                            if (source instanceof kendo.data.DataSource) {\n                                widget[setter](source);\n                            } else if (source && source._dataSource) {\n                                widget[setter](source._dataSource);\n                            } else {\n                                select = kendo.ui.Select && widget instanceof kendo.ui.Select;\n                                multiselect = kendo.ui.MultiSelect && widget instanceof kendo.ui.MultiSelect;\n                                dropdowntree = kendo.ui.DropDownTree && widget instanceof kendo.ui.DropDownTree;\n                                if (!dropdowntree) {\n                                    widget[fieldName].data(source);\n                                } else {\n                                    widget.treeview[fieldName].data(source);\n                                }\n                                if (that.bindings.value && (select || multiselect)) {\n                                    widget.value(retrievePrimitiveValues(that.bindings.value.get(), widget.options.dataValueField));\n                                }\n                            }\n                        }\n                    }\n                },\n                destroy: function () {\n                    var widget = this.widget;\n                    widget.unbind('dataBinding', this._dataBinding);\n                    widget.unbind('dataBound', this._dataBound);\n                    widget.unbind('itemChange', this._itemChange);\n                }\n            });\n        }\n        binders.widget = {\n            events: Binder.extend({\n                init: function (widget, bindings, options) {\n                    Binder.fn.init.call(this, widget.element[0], bindings, options);\n                    this.widget = widget;\n                    this.handlers = {};\n                },\n                refresh: function (key) {\n                    var binding = this.bindings.events[key], handler = this.handlers[key];\n                    if (handler) {\n                        this.widget.unbind(key, handler);\n                    }\n                    handler = binding.get();\n                    this.handlers[key] = function (e) {\n                        e.data = binding.source;\n                        handler(e);\n                        if (e.data === binding.source) {\n                            delete e.data;\n                        }\n                    };\n                    this.widget.bind(key, this.handlers[key]);\n                },\n                destroy: function () {\n                    var handler;\n                    for (handler in this.handlers) {\n                        this.widget.unbind(handler, this.handlers[handler]);\n                    }\n                }\n            }),\n            checked: Binder.extend({\n                init: function (widget, bindings, options) {\n                    Binder.fn.init.call(this, widget.element[0], bindings, options);\n                    this.widget = widget;\n                    this._change = proxy(this.change, this);\n                    this.widget.bind(CHANGE, this._change);\n                },\n                change: function () {\n                    this.bindings[CHECKED].set(this.value());\n                },\n                refresh: function () {\n                    this.widget.check(this.bindings[CHECKED].get() === true);\n                },\n                value: function () {\n                    var element = this.element, value = element.value;\n                    if (value == 'on' || value == 'off' || this.element.type == 'checkbox') {\n                        value = element.checked;\n                    }\n                    return value;\n                },\n                destroy: function () {\n                    this.widget.unbind(CHANGE, this._change);\n                }\n            }),\n            start: Binder.extend({\n                init: function (widget, bindings, options) {\n                    Binder.fn.init.call(this, widget.element[0], bindings, options);\n                    this._change = proxy(this.change, this);\n                    this.widget = widget;\n                    this.widget.bind(CHANGE, this._change);\n                },\n                change: function () {\n                    this.bindings.start.set(this.widget.range().start);\n                },\n                refresh: function () {\n                    var that = this;\n                    var start = this.bindings.start.get();\n                    var end = that.widget._range ? that.widget._range.end : null;\n                    this.widget.range({\n                        start: start,\n                        end: end\n                    });\n                },\n                destroy: function () {\n                    this.widget.unbind(CHANGE, this._change);\n                }\n            }),\n            end: Binder.extend({\n                init: function (widget, bindings, options) {\n                    Binder.fn.init.call(this, widget.element[0], bindings, options);\n                    this._change = proxy(this.change, this);\n                    this.widget = widget;\n                    this.widget.bind(CHANGE, this._change);\n                },\n                change: function () {\n                    this.bindings.end.set(this.widget.range().end);\n                },\n                refresh: function () {\n                    var that = this;\n                    var end = this.bindings.end.get();\n                    var start = that.widget._range ? that.widget._range.start : null;\n                    this.widget.range({\n                        start: start,\n                        end: end\n                    });\n                },\n                destroy: function () {\n                    this.widget.unbind(CHANGE, this._change);\n                }\n            }),\n            visible: Binder.extend({\n                init: function (widget, bindings, options) {\n                    Binder.fn.init.call(this, widget.element[0], bindings, options);\n                    this.widget = widget;\n                },\n                refresh: function () {\n                    var visible = this.bindings.visible.get();\n                    this.widget.wrapper[0].style.display = visible ? '' : 'none';\n                }\n            }),\n            invisible: Binder.extend({\n                init: function (widget, bindings, options) {\n                    Binder.fn.init.call(this, widget.element[0], bindings, options);\n                    this.widget = widget;\n                },\n                refresh: function () {\n                    var invisible = this.bindings.invisible.get();\n                    this.widget.wrapper[0].style.display = invisible ? 'none' : '';\n                }\n            }),\n            enabled: Binder.extend({\n                init: function (widget, bindings, options) {\n                    Binder.fn.init.call(this, widget.element[0], bindings, options);\n                    this.widget = widget;\n                },\n                refresh: function () {\n                    if (this.widget.enable) {\n                        this.widget.enable(this.bindings.enabled.get());\n                    }\n                }\n            }),\n            disabled: Binder.extend({\n                init: function (widget, bindings, options) {\n                    Binder.fn.init.call(this, widget.element[0], bindings, options);\n                    this.widget = widget;\n                },\n                refresh: function () {\n                    if (this.widget.enable) {\n                        this.widget.enable(!this.bindings.disabled.get());\n                    }\n                }\n            }),\n            source: dataSourceBinding('source', 'dataSource', 'setDataSource'),\n            value: Binder.extend({\n                init: function (widget, bindings, options) {\n                    Binder.fn.init.call(this, widget.element[0], bindings, options);\n                    this.widget = widget;\n                    this._change = $.proxy(this.change, this);\n                    this.widget.first(CHANGE, this._change);\n                    var value = this.bindings.value.get();\n                    this._valueIsObservableObject = !options.valuePrimitive && (value == null || value instanceof ObservableObject);\n                    this._valueIsObservableArray = value instanceof ObservableArray;\n                    this._initChange = false;\n                },\n                _source: function () {\n                    var source;\n                    if (this.widget.dataItem) {\n                        source = this.widget.dataItem();\n                        if (source && source instanceof ObservableObject) {\n                            return [source];\n                        }\n                    }\n                    if (this.bindings.source) {\n                        source = this.bindings.source.get();\n                    }\n                    if (!source || source instanceof kendo.data.DataSource) {\n                        source = this.widget.dataSource.flatView();\n                    }\n                    return source;\n                },\n                change: function () {\n                    var value = this.widget.value(), field = this.options.dataValueField || this.options.dataTextField, isArray = toString.call(value) === '[object Array]', isObservableObject = this._valueIsObservableObject, valueIndex, valueLength, values = [], sourceItem, sourceValue, idx, length, source;\n                    this._initChange = true;\n                    if (field) {\n                        if (value === '' && (isObservableObject || this.options.valuePrimitive)) {\n                            value = null;\n                        } else {\n                            source = this._source();\n                            if (isArray) {\n                                valueLength = value.length;\n                                values = value.slice(0);\n                            }\n                            for (idx = 0, length = source.length; idx < length; idx++) {\n                                sourceItem = source[idx];\n                                sourceValue = sourceItem.get(field);\n                                if (isArray) {\n                                    for (valueIndex = 0; valueIndex < valueLength; valueIndex++) {\n                                        if (sourceValue == values[valueIndex]) {\n                                            values[valueIndex] = sourceItem;\n                                            break;\n                                        }\n                                    }\n                                } else if (sourceValue == value) {\n                                    value = isObservableObject ? sourceItem : sourceValue;\n                                    break;\n                                }\n                            }\n                            if (values[0]) {\n                                if (this._valueIsObservableArray) {\n                                    value = values;\n                                } else if (isObservableObject || !field) {\n                                    value = values[0];\n                                } else {\n                                    value = values[0].get(field);\n                                }\n                            }\n                        }\n                    }\n                    this.bindings.value.set(value);\n                    this._initChange = false;\n                },\n                refresh: function () {\n                    if (!this._initChange) {\n                        var widget = this.widget;\n                        var options = widget.options;\n                        var textField = options.dataTextField;\n                        var valueField = options.dataValueField || textField;\n                        var value = this.bindings.value.get();\n                        var text = options.text || '';\n                        var idx = 0, length;\n                        var values = [];\n                        if (value === undefined) {\n                            value = null;\n                        }\n                        if (valueField) {\n                            if (value instanceof ObservableArray) {\n                                for (length = value.length; idx < length; idx++) {\n                                    values[idx] = value[idx].get(valueField);\n                                }\n                                value = values;\n                            } else if (value instanceof ObservableObject) {\n                                text = value.get(textField);\n                                value = value.get(valueField);\n                            }\n                        }\n                        if (options.autoBind === false && !options.cascadeFrom && widget.listView && !widget.listView.bound()) {\n                            if (textField === valueField && !text) {\n                                text = value;\n                            }\n                            if (!text && (value || value === 0) && options.valuePrimitive) {\n                                widget.value(value);\n                            } else {\n                                widget._preselect(value, text);\n                            }\n                        } else {\n                            widget.value(value);\n                        }\n                    }\n                    this._initChange = false;\n                },\n                destroy: function () {\n                    this.widget.unbind(CHANGE, this._change);\n                }\n            }),\n            dropdowntree: {\n                value: Binder.extend({\n                    init: function (widget, bindings, options) {\n                        Binder.fn.init.call(this, widget.element[0], bindings, options);\n                        this.widget = widget;\n                        this._change = $.proxy(this.change, this);\n                        this.widget.first(CHANGE, this._change);\n                        this._initChange = false;\n                    },\n                    change: function () {\n                        var that = this, oldValues = that.bindings[VALUE].get(), valuePrimitive = that.options.valuePrimitive, selectedNode = that.widget.treeview.select(), nonPrimitiveValues = that.widget._isMultipleSelection() ? that.widget._getAllChecked() : that.widget.treeview.dataItem(selectedNode) || that.widget.value(), newValues = valuePrimitive || that.widget.options.autoBind === false ? that.widget.value() : nonPrimitiveValues;\n                        var field = this.options.dataValueField || this.options.dataTextField;\n                        newValues = newValues.slice ? newValues.slice(0) : newValues;\n                        that._initChange = true;\n                        if (oldValues instanceof ObservableArray) {\n                            var remove = [];\n                            var newLength = newValues.length;\n                            var i = 0, j = 0;\n                            var old = oldValues[i];\n                            var same = false;\n                            var removeIndex;\n                            var newValue;\n                            var found;\n                            while (old !== undefined) {\n                                found = false;\n                                for (j = 0; j < newLength; j++) {\n                                    if (valuePrimitive) {\n                                        same = newValues[j] == old;\n                                    } else {\n                                        newValue = newValues[j];\n                                        newValue = newValue.get ? newValue.get(field) : newValue;\n                                        same = newValue == (old.get ? old.get(field) : old);\n                                    }\n                                    if (same) {\n                                        newValues.splice(j, 1);\n                                        newLength -= 1;\n                                        found = true;\n                                        break;\n                                    }\n                                }\n                                if (!found) {\n                                    remove.push(old);\n                                    arraySplice(oldValues, i, 1);\n                                    removeIndex = i;\n                                } else {\n                                    i += 1;\n                                }\n                                old = oldValues[i];\n                            }\n                            arraySplice(oldValues, oldValues.length, 0, newValues);\n                            if (remove.length) {\n                                oldValues.trigger('change', {\n                                    action: 'remove',\n                                    items: remove,\n                                    index: removeIndex\n                                });\n                            }\n                            if (newValues.length) {\n                                oldValues.trigger('change', {\n                                    action: 'add',\n                                    items: newValues,\n                                    index: oldValues.length - 1\n                                });\n                            }\n                        } else {\n                            that.bindings[VALUE].set(newValues);\n                        }\n                        that._initChange = false;\n                    },\n                    refresh: function () {\n                        if (!this._initChange) {\n                            var options = this.options, widget = this.widget, field = options.dataValueField || options.dataTextField, value = this.bindings.value.get(), data = value, idx = 0, length, values = [], selectedValue;\n                            if (field) {\n                                if (value instanceof ObservableArray) {\n                                    for (length = value.length; idx < length; idx++) {\n                                        selectedValue = value[idx];\n                                        values[idx] = selectedValue.get ? selectedValue.get(field) : selectedValue;\n                                    }\n                                    value = values;\n                                } else if (value instanceof ObservableObject) {\n                                    value = value.get(field);\n                                }\n                            }\n                            if (options.autoBind === false && options.valuePrimitive !== true) {\n                                widget._preselect(data, value);\n                            } else {\n                                widget.value(value);\n                            }\n                        }\n                    },\n                    destroy: function () {\n                        this.widget.unbind(CHANGE, this._change);\n                    }\n                })\n            },\n            gantt: { dependencies: dataSourceBinding('dependencies', 'dependencies', 'setDependenciesDataSource') },\n            multiselect: {\n                value: Binder.extend({\n                    init: function (widget, bindings, options) {\n                        Binder.fn.init.call(this, widget.element[0], bindings, options);\n                        this.widget = widget;\n                        this._change = $.proxy(this.change, this);\n                        this.widget.first(CHANGE, this._change);\n                        this._initChange = false;\n                    },\n                    change: function () {\n                        var that = this, oldValues = that.bindings[VALUE].get(), valuePrimitive = that.options.valuePrimitive, newValues = valuePrimitive ? that.widget.value() : that.widget.dataItems();\n                        var field = this.options.dataValueField || this.options.dataTextField;\n                        newValues = newValues.slice(0);\n                        that._initChange = true;\n                        if (oldValues instanceof ObservableArray) {\n                            var remove = [];\n                            var newLength = newValues.length;\n                            var i = 0, j = 0;\n                            var old = oldValues[i];\n                            var same = false;\n                            var removeIndex;\n                            var newValue;\n                            var found;\n                            while (old !== undefined) {\n                                found = false;\n                                for (j = 0; j < newLength; j++) {\n                                    if (valuePrimitive) {\n                                        same = newValues[j] == old;\n                                    } else {\n                                        newValue = newValues[j];\n                                        newValue = newValue.get ? newValue.get(field) : newValue;\n                                        same = newValue == (old.get ? old.get(field) : old);\n                                    }\n                                    if (same) {\n                                        newValues.splice(j, 1);\n                                        newLength -= 1;\n                                        found = true;\n                                        break;\n                                    }\n                                }\n                                if (!found) {\n                                    remove.push(old);\n                                    arraySplice(oldValues, i, 1);\n                                    removeIndex = i;\n                                } else {\n                                    i += 1;\n                                }\n                                old = oldValues[i];\n                            }\n                            arraySplice(oldValues, oldValues.length, 0, newValues);\n                            if (remove.length) {\n                                oldValues.trigger('change', {\n                                    action: 'remove',\n                                    items: remove,\n                                    index: removeIndex\n                                });\n                            }\n                            if (newValues.length) {\n                                oldValues.trigger('change', {\n                                    action: 'add',\n                                    items: newValues,\n                                    index: oldValues.length - 1\n                                });\n                            }\n                        } else {\n                            that.bindings[VALUE].set(newValues);\n                        }\n                        that._initChange = false;\n                    },\n                    refresh: function () {\n                        if (!this._initChange) {\n                            var options = this.options, widget = this.widget, field = options.dataValueField || options.dataTextField, value = this.bindings.value.get(), data = value, idx = 0, length, values = [], selectedValue;\n                            if (value === undefined) {\n                                value = null;\n                            }\n                            if (field) {\n                                if (value instanceof ObservableArray) {\n                                    for (length = value.length; idx < length; idx++) {\n                                        selectedValue = value[idx];\n                                        values[idx] = selectedValue.get ? selectedValue.get(field) : selectedValue;\n                                    }\n                                    value = values;\n                                } else if (value instanceof ObservableObject) {\n                                    value = value.get(field);\n                                }\n                            }\n                            if (options.autoBind === false && options.valuePrimitive !== true && !widget._isBound()) {\n                                widget._preselect(data, value);\n                            } else {\n                                widget.value(value);\n                            }\n                        }\n                    },\n                    destroy: function () {\n                        this.widget.unbind(CHANGE, this._change);\n                    }\n                })\n            },\n            scheduler: {\n                source: dataSourceBinding('source', 'dataSource', 'setDataSource').extend({\n                    dataBound: function (e) {\n                        var idx;\n                        var length;\n                        var widget = this.widget;\n                        var elements = e.addedItems || widget.items();\n                        var data, parents;\n                        if (elements.length) {\n                            data = e.addedDataItems || widget.dataItems();\n                            parents = this.bindings.source._parents();\n                            for (idx = 0, length = data.length; idx < length; idx++) {\n                                bindElement(elements[idx], data[idx], this._ns(e.ns), [data[idx]].concat(parents));\n                            }\n                        }\n                    }\n                })\n            },\n            grid: {\n                source: dataSourceBinding('source', 'dataSource', 'setDataSource').extend({\n                    dataBound: function (e) {\n                        var idx, length, widget = this.widget, elements = e.addedItems || widget.items(), parents, data;\n                        if (elements.length) {\n                            data = e.addedDataItems || widget.dataItems();\n                            parents = this.bindings.source._parents();\n                            for (idx = 0, length = data.length; idx < length; idx++) {\n                                bindElement(elements[idx], data[idx], this._ns(e.ns), [data[idx]].concat(parents));\n                            }\n                        }\n                    }\n                })\n            }\n        };\n        var arraySplice = function (arr, idx, remove, add) {\n            add = add || [];\n            remove = remove || 0;\n            var addLength = add.length;\n            var oldLength = arr.length;\n            var shifted = [].slice.call(arr, idx + remove);\n            var shiftedLength = shifted.length;\n            var index;\n            if (addLength) {\n                addLength = idx + addLength;\n                index = 0;\n                for (; idx < addLength; idx++) {\n                    arr[idx] = add[index];\n                    index++;\n                }\n                arr.length = addLength;\n            } else if (remove) {\n                arr.length = idx;\n                remove += idx;\n                while (idx < remove) {\n                    delete arr[--remove];\n                }\n            }\n            if (shiftedLength) {\n                shiftedLength = idx + shiftedLength;\n                index = 0;\n                for (; idx < shiftedLength; idx++) {\n                    arr[idx] = shifted[index];\n                    index++;\n                }\n                arr.length = shiftedLength;\n            }\n            idx = arr.length;\n            while (idx < oldLength) {\n                delete arr[idx];\n                idx++;\n            }\n        };\n        var BindingTarget = Class.extend({\n            init: function (target, options) {\n                this.target = target;\n                this.options = options;\n                this.toDestroy = [];\n            },\n            bind: function (bindings) {\n                var key, hasValue, hasSource, hasEvents, hasChecked, hasCss, widgetBinding = this instanceof WidgetBindingTarget, specificBinders = this.binders();\n                for (key in bindings) {\n                    if (key == VALUE) {\n                        hasValue = true;\n                    } else if (key == SOURCE) {\n                        hasSource = true;\n                    } else if (key == EVENTS && !widgetBinding) {\n                        hasEvents = true;\n                    } else if (key == CHECKED) {\n                        hasChecked = true;\n                    } else if (key == CSS) {\n                        hasCss = true;\n                    } else {\n                        this.applyBinding(key, bindings, specificBinders);\n                    }\n                }\n                if (hasSource) {\n                    this.applyBinding(SOURCE, bindings, specificBinders);\n                }\n                if (hasValue) {\n                    this.applyBinding(VALUE, bindings, specificBinders);\n                }\n                if (hasChecked) {\n                    this.applyBinding(CHECKED, bindings, specificBinders);\n                }\n                if (hasEvents && !widgetBinding) {\n                    this.applyBinding(EVENTS, bindings, specificBinders);\n                }\n                if (hasCss && !widgetBinding) {\n                    this.applyBinding(CSS, bindings, specificBinders);\n                }\n            },\n            binders: function () {\n                return binders[this.target.nodeName.toLowerCase()] || {};\n            },\n            applyBinding: function (name, bindings, specificBinders) {\n                var binder = specificBinders[name] || binders[name], toDestroy = this.toDestroy, attribute, binding = bindings[name];\n                if (binder) {\n                    binder = new binder(this.target, bindings, this.options);\n                    toDestroy.push(binder);\n                    if (binding instanceof Binding) {\n                        binder.bind(binding);\n                        toDestroy.push(binding);\n                    } else {\n                        for (attribute in binding) {\n                            binder.bind(binding, attribute);\n                            toDestroy.push(binding[attribute]);\n                        }\n                    }\n                } else if (name !== 'template') {\n                    throw new Error('The ' + name + ' binding is not supported by the ' + this.target.nodeName.toLowerCase() + ' element');\n                }\n            },\n            destroy: function () {\n                var idx, length, toDestroy = this.toDestroy;\n                for (idx = 0, length = toDestroy.length; idx < length; idx++) {\n                    toDestroy[idx].destroy();\n                }\n            }\n        });\n        var WidgetBindingTarget = BindingTarget.extend({\n            binders: function () {\n                return binders.widget[this.target.options.name.toLowerCase()] || {};\n            },\n            applyBinding: function (name, bindings, specificBinders) {\n                var binder = specificBinders[name] || binders.widget[name], toDestroy = this.toDestroy, attribute, binding = bindings[name];\n                if (binder) {\n                    binder = new binder(this.target, bindings, this.target.options);\n                    toDestroy.push(binder);\n                    if (binding instanceof Binding) {\n                        binder.bind(binding);\n                        toDestroy.push(binding);\n                    } else {\n                        for (attribute in binding) {\n                            binder.bind(binding, attribute);\n                            toDestroy.push(binding[attribute]);\n                        }\n                    }\n                } else {\n                    throw new Error('The ' + name + ' binding is not supported by the ' + this.target.options.name + ' widget');\n                }\n            }\n        });\n        function bindingTargetForRole(element, roles) {\n            var widget = kendo.initWidget(element, {}, roles);\n            if (widget) {\n                return new WidgetBindingTarget(widget);\n            }\n        }\n        var keyValueRegExp = /[A-Za-z0-9_\\-]+:(\\{([^}]*)\\}|[^,}]+)/g, whiteSpaceRegExp = /\\s/g;\n        function parseBindings(bind) {\n            var result = {}, idx, length, token, colonIndex, key, value, tokens;\n            tokens = bind.match(keyValueRegExp);\n            for (idx = 0, length = tokens.length; idx < length; idx++) {\n                token = tokens[idx];\n                colonIndex = token.indexOf(':');\n                key = token.substring(0, colonIndex);\n                value = token.substring(colonIndex + 1);\n                if (value.charAt(0) == '{') {\n                    value = parseBindings(value);\n                }\n                result[key] = value;\n            }\n            return result;\n        }\n        function createBindings(bindings, source, type) {\n            var binding, result = {};\n            for (binding in bindings) {\n                result[binding] = new type(source, bindings[binding]);\n            }\n            return result;\n        }\n        function bindElement(element, source, roles, parents) {\n            if (!element || element.getAttribute('data-' + kendo.ns + 'stop')) {\n                return;\n            }\n            var role = element.getAttribute('data-' + kendo.ns + 'role'), idx, bind = element.getAttribute('data-' + kendo.ns + 'bind'), childrenCopy = [], deep = true, bindings, options = {}, target;\n            parents = parents || [source];\n            if (role || bind) {\n                unbindElement(element, false);\n            }\n            if (role) {\n                target = bindingTargetForRole(element, roles);\n            }\n            if (bind) {\n                bind = parseBindings(bind.replace(whiteSpaceRegExp, ''));\n                if (!target) {\n                    options = kendo.parseOptions(element, {\n                        textField: '',\n                        valueField: '',\n                        template: '',\n                        valueUpdate: CHANGE,\n                        valuePrimitive: false,\n                        autoBind: true\n                    }, source);\n                    options.roles = roles;\n                    target = new BindingTarget(element, options);\n                }\n                target.source = source;\n                bindings = createBindings(bind, parents, Binding);\n                if (options.template) {\n                    bindings.template = new TemplateBinding(parents, '', options.template);\n                }\n                if (bindings.click) {\n                    bind.events = bind.events || {};\n                    bind.events.click = bind.click;\n                    bindings.click.destroy();\n                    delete bindings.click;\n                }\n                if (bindings.source) {\n                    deep = false;\n                }\n                if (bind.attr) {\n                    bindings.attr = createBindings(bind.attr, parents, Binding);\n                }\n                if (bind.style) {\n                    bindings.style = createBindings(bind.style, parents, Binding);\n                }\n                if (bind.events) {\n                    bindings.events = createBindings(bind.events, parents, EventBinding);\n                }\n                if (bind.css) {\n                    bindings.css = createBindings(bind.css, parents, Binding);\n                }\n                target.bind(bindings);\n            }\n            if (target) {\n                element.kendoBindingTarget = target;\n            }\n            var children = element.children;\n            if (deep && children) {\n                for (idx = 0; idx < children.length; idx++) {\n                    childrenCopy[idx] = children[idx];\n                }\n                for (idx = 0; idx < childrenCopy.length; idx++) {\n                    bindElement(childrenCopy[idx], source, roles, parents);\n                }\n            }\n        }\n        function bind(dom, object) {\n            var idx, length, node, roles = kendo.rolesFromNamespaces([].slice.call(arguments, 2));\n            object = kendo.observable(object);\n            dom = $(dom);\n            for (idx = 0, length = dom.length; idx < length; idx++) {\n                node = dom[idx];\n                if (node.nodeType === 1) {\n                    bindElement(node, object, roles);\n                }\n            }\n        }\n        function unbindElement(element, destroyWidget) {\n            var bindingTarget = element.kendoBindingTarget;\n            if (bindingTarget) {\n                bindingTarget.destroy();\n                if (deleteExpando) {\n                    delete element.kendoBindingTarget;\n                } else if (element.removeAttribute) {\n                    element.removeAttribute('kendoBindingTarget');\n                } else {\n                    element.kendoBindingTarget = null;\n                }\n            }\n            if (destroyWidget) {\n                var widget = kendo.widgetInstance($(element));\n                if (widget && typeof widget.destroy === FUNCTION) {\n                    widget.destroy();\n                }\n            }\n        }\n        function unbindElementTree(element, destroyWidgets) {\n            unbindElement(element, destroyWidgets);\n            unbindElementChildren(element, destroyWidgets);\n        }\n        function unbindElementChildren(element, destroyWidgets) {\n            var children = element.children;\n            if (children) {\n                for (var idx = 0, length = children.length; idx < length; idx++) {\n                    unbindElementTree(children[idx], destroyWidgets);\n                }\n            }\n        }\n        function unbind(dom) {\n            var idx, length;\n            dom = $(dom);\n            for (idx = 0, length = dom.length; idx < length; idx++) {\n                unbindElementTree(dom[idx], false);\n            }\n        }\n        function notify(widget, namespace) {\n            var element = widget.element, bindingTarget = element[0].kendoBindingTarget;\n            if (bindingTarget) {\n                bind(element, bindingTarget.source, namespace);\n            }\n        }\n        function retrievePrimitiveValues(value, valueField) {\n            var values = [];\n            var idx = 0;\n            var length;\n            var item;\n            if (!valueField) {\n                return value;\n            }\n            if (value instanceof ObservableArray) {\n                for (length = value.length; idx < length; idx++) {\n                    item = value[idx];\n                    values[idx] = item.get ? item.get(valueField) : item[valueField];\n                }\n                value = values;\n            } else if (value instanceof ObservableObject) {\n                value = value.get(valueField);\n            }\n            return value;\n        }\n        kendo.unbind = unbind;\n        kendo.bind = bind;\n        kendo.data.binders = binders;\n        kendo.data.Binder = Binder;\n        kendo.notify = notify;\n        kendo.observable = function (object) {\n            if (!(object instanceof ObservableObject)) {\n                object = new ObservableObject(object);\n            }\n            return object;\n        };\n        kendo.observableHierarchy = function (array) {\n            var dataSource = kendo.data.HierarchicalDataSource.create(array);\n            function recursiveRead(data) {\n                var i, children;\n                for (i = 0; i < data.length; i++) {\n                    data[i]._initChildren();\n                    children = data[i].children;\n                    children.fetch();\n                    data[i].items = children.data();\n                    recursiveRead(data[i].items);\n                }\n            }\n            dataSource.fetch();\n            recursiveRead(dataSource.data());\n            dataSource._data._dataSource = dataSource;\n            return dataSource._data;\n        };\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}