{"version": 3, "sources": ["kendo.mobile.tabstrip.js"], "names": ["f", "define", "$", "undefined", "createBadge", "value", "kendo", "window", "ui", "mobile", "Widget", "ACTIVE_STATE_CLASS", "SELECT", "TabStrip", "extend", "init", "element", "options", "that", "this", "fn", "call", "container", "bind", "proxy", "addClass", "find", "each", "_buildButton", "eq", "selectedIndex", "on", "events", "switchTo", "url", "tab", "path", "tabs", "idx", "length", "isNaN", "_setActiveItem", "href", "replace", "indexOf", "switchByFullUrl", "clear", "currentItem", "removeClass", "children", "badge", "item", "tabstrip", "get", "insertAfter", "html", "empty", "remove", "_release", "e", "which", "currentTarget", "trigger", "preventDefault", "button", "icon", "attrValue", "image", "iconSpan", "attr", "contents", "not", "wrapAll", "prependTo", "prepend", "refresh", "view", "id", "name", "enable", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,yBAA0B,cAAeD,IAClD,WAqHE,MA7GC,UAAUE,EAAGC,GAEV,QAASC,GAAYC,GACjB,MAAOH,GAAE,0BAA4BG,EAAQ,WAHpD,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAMG,OAAOD,GAAIE,EAASF,EAAGE,OAAQC,EAAqB,kBAAmBC,EAAS,SAIjHC,EAAWH,EAAOI,QAClBC,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOC,IACXT,GAAOU,GAAGL,KAAKM,KAAKH,EAAMF,EAASC,GACnCC,EAAKI,YAAYC,KAAK,OAAQrB,EAAEsB,MAAML,KAAM,YAC5CD,EAAKF,QAAQS,SAAS,eAAeC,KAAK,KAAKC,KAAKT,EAAKU,cAAcC,GAAGX,EAAKD,QAAQa,eAAeL,SAASd,GAC/GO,EAAKF,QAAQe,GAAG,OAAQ,IAAK,aAEjCC,QAASpB,GACTqB,SAAU,SAAUC,GAChB,GAAmCC,GAAKC,EAApCC,EAAOlB,KAAKH,QAAQU,KAAK,KAAiBY,EAAM,EAAGC,EAASF,EAAKE,MACrE,KAAIC,MAAMN,GAWN,MADAf,MAAKsB,eAAeJ,EAAKR,GAAGK,KACrB,CAVP,MAAOI,EAAMC,EAAQD,IAGjB,GAFAH,EAAME,EAAKC,GACXF,EAAOD,EAAIO,KAAKC,QAAQ,gBAAiB,MACrCP,EAAKQ,QAAQV,EAAKE,EAAKG,OAASL,EAAIK,aAEpC,MADApB,MAAKsB,eAAevC,EAAEiC,KACf,CAOnB,QAAO,GAEXU,gBAAiB,SAAUX,GACvB,GAAIC,EACJA,GAAMhB,KAAKH,QAAQU,KAAK,YAAeQ,EAAM,MAC7Cf,KAAKsB,eAAeN,IAExBW,MAAO,WACH3B,KAAK4B,cAAcC,YAAYrC,IAEnCoC,YAAa,WACT,MAAO5B,MAAKH,QAAQiC,SAAS,IAAMtC,IAEvCuC,MAAO,SAAUC,EAAM9C,GACnB,GAA6B6C,GAAzBE,EAAWjC,KAAKH,OAMpB,OALKwB,OAAMW,KACPA,EAAOC,EAASH,WAAWI,IAAIF,IAEnCA,EAAOC,EAAS1B,KAAKyB,GACrBD,EAAQhD,EAAEiD,EAAKzB,KAAK,aAAa,IAAMtB,EAAYC,GAAOiD,YAAYH,EAAKF,SAAS,cAChF5C,GAAmB,IAAVA,GACT6C,EAAMK,KAAKlD,GACJc,MAEPd,KAAU,GACV6C,EAAMM,QAAQC,SACPtC,MAEJ+B,EAAMK,QAEjBG,SAAU,SAAUC,GAChB,KAAIA,EAAEC,MAAQ,GAAd,CAGA,GAAI1C,GAAOC,KAAMgC,EAAOjD,EAAEyD,EAAEE,cACxBV,GAAK,KAAOjC,EAAK6B,cAAc,KAG/B7B,EAAK4C,QAAQlD,GAAUuC,KAAMA,IAC7BQ,EAAEI,iBAEF7C,EAAKuB,eAAeU,MAG5BV,eAAgB,SAAUU,GACjBA,EAAK,KAGVhC,KAAK2B,QACLK,EAAK1B,SAASd,KAElBiB,aAAc,WACV,GAAIoC,GAAS9D,EAAEiB,MAAO8C,EAAO3D,EAAM4D,UAAUF,EAAQ,QAASd,EAAQ5C,EAAM4D,UAAUF,EAAQ,SAAUG,EAAQH,EAAOtC,KAAK,OAAQ0C,EAAWlE,EAAE,0BACjJ8D,GAAOvC,SAAS,aAAa4C,KAAK/D,EAAM+D,KAAK,QAAS,OAAOC,WAAWC,IAAIJ,GAAOK,QAAQ,2BACvFL,EAAM,GACNA,EAAM1C,SAAS,YAAYgD,UAAUT,IAErCA,EAAOU,QAAQN,GACXH,IACAG,EAAS3C,SAAS,MAAQwC,IACtBf,GAAmB,IAAVA,IACT9C,EAAY8C,GAAOI,YAAYc,MAK/CO,QAAS,SAAUhB,GACf,GAAIzB,GAAMyB,EAAEiB,KAAKC,EACb3C,KAAQf,KAAKc,SAAS0B,EAAEiB,KAAKC,KAC7B1D,KAAKc,SAASC,IAGtBjB,SACI6D,KAAM,WACNhD,cAAe,EACfiD,QAAQ,IAGhBvE,GAAGwE,OAAOnE,IACZN,OAAOD,MAAM2E,QACR1E,OAAOD,OACE,kBAAVL,SAAwBA,OAAOiF,IAAMjF,OAAS,SAAUkF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.tabstrip.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.tabstrip', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.tabstrip',\n        name: 'TabStrip',\n        category: 'mobile',\n        description: 'The mobile TabStrip widget is used inside a mobile view or layout footer element to display an application-wide group of navigation buttons.',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.mobile.ui, Widget = ui.Widget, ACTIVE_STATE_CLASS = 'km-state-active', SELECT = 'select';\n        function createBadge(value) {\n            return $('<span class=\"km-badge\">' + value + '</span>');\n        }\n        var TabStrip = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                that.container().bind('show', $.proxy(this, 'refresh'));\n                that.element.addClass('km-tabstrip').find('a').each(that._buildButton).eq(that.options.selectedIndex).addClass(ACTIVE_STATE_CLASS);\n                that.element.on('down', 'a', '_release');\n            },\n            events: [SELECT],\n            switchTo: function (url) {\n                var tabs = this.element.find('a'), tab, path, idx = 0, length = tabs.length;\n                if (isNaN(url)) {\n                    for (; idx < length; idx++) {\n                        tab = tabs[idx];\n                        path = tab.href.replace(/(\\#.+)(\\?.+)$/, '$1');\n                        if (path.indexOf(url, path.length - url.length) !== -1) {\n                            this._setActiveItem($(tab));\n                            return true;\n                        }\n                    }\n                } else {\n                    this._setActiveItem(tabs.eq(url));\n                    return true;\n                }\n                return false;\n            },\n            switchByFullUrl: function (url) {\n                var tab;\n                tab = this.element.find('a[href$=\\'' + url + '\\']');\n                this._setActiveItem(tab);\n            },\n            clear: function () {\n                this.currentItem().removeClass(ACTIVE_STATE_CLASS);\n            },\n            currentItem: function () {\n                return this.element.children('.' + ACTIVE_STATE_CLASS);\n            },\n            badge: function (item, value) {\n                var tabstrip = this.element, badge;\n                if (!isNaN(item)) {\n                    item = tabstrip.children().get(item);\n                }\n                item = tabstrip.find(item);\n                badge = $(item.find('.km-badge')[0] || createBadge(value).insertAfter(item.children('.km-icon')));\n                if (value || value === 0) {\n                    badge.html(value);\n                    return this;\n                }\n                if (value === false) {\n                    badge.empty().remove();\n                    return this;\n                }\n                return badge.html();\n            },\n            _release: function (e) {\n                if (e.which > 1) {\n                    return;\n                }\n                var that = this, item = $(e.currentTarget);\n                if (item[0] === that.currentItem()[0]) {\n                    return;\n                }\n                if (that.trigger(SELECT, { item: item })) {\n                    e.preventDefault();\n                } else {\n                    that._setActiveItem(item);\n                }\n            },\n            _setActiveItem: function (item) {\n                if (!item[0]) {\n                    return;\n                }\n                this.clear();\n                item.addClass(ACTIVE_STATE_CLASS);\n            },\n            _buildButton: function () {\n                var button = $(this), icon = kendo.attrValue(button, 'icon'), badge = kendo.attrValue(button, 'badge'), image = button.find('img'), iconSpan = $('<span class=\"km-icon\"/>');\n                button.addClass('km-button').attr(kendo.attr('role'), 'tab').contents().not(image).wrapAll('<span class=\"km-text\"/>');\n                if (image[0]) {\n                    image.addClass('km-image').prependTo(button);\n                } else {\n                    button.prepend(iconSpan);\n                    if (icon) {\n                        iconSpan.addClass('km-' + icon);\n                        if (badge || badge === 0) {\n                            createBadge(badge).insertAfter(iconSpan);\n                        }\n                    }\n                }\n            },\n            refresh: function (e) {\n                var url = e.view.id;\n                if (url && !this.switchTo(e.view.id)) {\n                    this.switchTo(url);\n                }\n            },\n            options: {\n                name: 'TabStrip',\n                selectedIndex: 0,\n                enable: true\n            }\n        });\n        ui.plugin(TabStrip);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}