/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.mobile.listview.min",["kendo.data.min","kendo.userevents.min","kendo.mobile.button.min"],t)}(function(){return function(t,e){function i(){return this.nodeType===_.TEXT_NODE&&this.nodeValue.match(G)}function s(t,e){e&&!t[0].querySelector(".km-icon")&&t.prepend('<span class="km-icon km-'+e+'"/>')}function n(t){s(t,I(t,"icon")),s(t,I(t.children(x),"icon"))}function o(t){var e=t.parent(),n=t.add(e.children(g.roleSelector("detailbutton"))),o=e.contents().not(n).not(i);o.length||(t.addClass("km-listview-link").attr(g.attr("role"),"listview-link"),s(t,I(e,"icon")),s(t,I(t,"icon")))}function r(t){if(t[0].querySelector("input[type=checkbox],input[type=radio]")){var e=t.parent();e.contents().not(t).not(function(){return 3==this.nodeType})[0]||(t.addClass("km-listview-label"),t.children("[type=checkbox],[type=radio]").addClass("km-widget km-icon km-check"))}}function l(e,i){t(e).css("transform","translate3d(0px, "+i+"px, 0px)")}var a,h,d,c,u,f,p,m,g=window.kendo,_=window.Node,v=g.mobile,b=v.ui,w=g._outerHeight,k=g.data.DataSource,S=b.DataBoundWidget,y=".km-list > li, > li:not(.km-group-container)",T=".km-listview-link, .km-listview-label",x="["+g.attr("icon")+"]",C=t.proxy,I=g.attrValue,H="km-group-title",D="km-state-active",B='<div class="'+H+'"><div class="km-text"></div></div>',z=g.template('<li><div class="'+H+'"><div class="km-text">#= this.headerTemplate(data) #</div></div><ul>#= kendo.render(this.template, data.items)#</ul></li>'),V='<div class="km-listview-wrapper" />',F=g.template('<form class="km-filter-form"><div class="km-filter-wrap"><input type="search" placeholder="#=placeholder#"/><a href="\\#" class="km-filter-reset" title="Clear"><span class="km-icon km-clear"></span><span class="km-text">Clear</span></a></div></form>'),L=".kendoMobileListView",R="styled",E="dataBound",M="dataBinding",O="itemChange",P="click",A="change",U="progress",q="function",G=/^\s+$/,j=/button/,N=g.Class.extend({init:function(t){var e,i,s=t.scroller();s&&(this.options=t.options,this.element=t.element,this.scroller=t.scroller(),this._shouldFixHeaders(),e=this,i=function(){e._cacheHeaders()},t.bind("resize",i),t.bind(R,i),t.bind(E,i),this._scrollHandler=function(t){e._fixHeader(t)},s.bind("scroll",this._scrollHandler))},destroy:function(){var t=this;t.scroller&&t.scroller.unbind("scroll",t._scrollHandler)},_fixHeader:function(e){if(this.fixedHeaders){var i,s,n,o=0,r=this.scroller,l=this.headers,a=e.scrollTop;do{if(i=l[o++],!i){n=t("<div />");break}s=i.offset,n=i.header}while(s+1>a);this.currentHeader!=o&&(r.fixedContainer.html(n.clone()),this.currentHeader=o)}},_shouldFixHeaders:function(){this.fixedHeaders="group"===this.options.type&&this.options.fixedHeaders},_cacheHeaders:function(){if(this._shouldFixHeaders(),this.fixedHeaders){var e=[],i=this.scroller.scrollTop;this.element.find("."+H).each(function(s,n){n=t(n),e.unshift({offset:n.position().top+i,header:n})}),this.headers=e,this._fixHeader({scrollTop:i})}}}),W=function(){return{page:1}},$=g.Class.extend({init:function(t){var e=this,i=t.options,s=t.scroller(),n=i.pullParameters||W;this.listView=t,this.scroller=s,t.bind("_dataSource",function(t){e.setDataSource(t.dataSource)}),s.setOptions({pullToRefresh:!0,pull:function(){e._pulled||(e._pulled=!0,e.dataSource.read(n.call(t,e._first)))},messages:{pullTemplate:i.messages.pullTemplate,releaseTemplate:i.messages.releaseTemplate,refreshTemplate:i.messages.refreshTemplate}})},setDataSource:function(t){var e=this;this._first=t.view()[0],this.dataSource=t,t.bind("change",function(){e._change()}),t.bind("error",function(){e._change()})},_change:function(){var t,e=this.scroller,i=this.dataSource;this._pulled&&e.pullHandled(),!this._pulled&&this._first||(t=i.view(),t[0]&&(this._first=t[0])),this._pulled=!1}}),Q=g.Observable.extend({init:function(t){var e=this;g.Observable.fn.init.call(e),e.buffer=t.buffer,e.height=t.height,e.item=t.item,e.items=[],e.footer=t.footer,e.buffer.bind("reset",function(){e.refresh()})},refresh:function(){for(var t,e,i,s,n=this.buffer,o=this.items,r=!1;o.length;)o.pop().destroy();for(this.offset=n.offset,t=this.item,s=0;s<n.viewSize;s++){if(s===n.total()){r=!0;break}i=t(this.content(this.offset+o.length)),i.below(e),e=i,o.push(i)}this.itemCount=o.length,this.trigger("reset"),this._resize(),r&&this.trigger("endReached")},totalHeight:function(){if(!this.items[0])return 0;var t=this,e=t.items,i=e[0].top,s=e[e.length-1].bottom,n=(s-i)/t.itemCount,o=t.buffer.length-t.offset-t.itemCount;return(this.footer?this.footer.height:0)+s+o*n},batchUpdate:function(t){var e,i,s=this.height(),n=this.items,o=this.offset;if(n[0]){if(this.lastDirection)for(;n[n.length-1].bottom>t+2*s&&0!==this.offset;)this.offset--,e=n.pop(),e.update(this.content(this.offset)),e.above(n[0]),n.unshift(e);else for(;n[0].top<t-s;){if(i=this.offset+this.itemCount,i===this.buffer.total()){this.trigger("endReached");break}if(i===this.buffer.length)break;e=n.shift(),e.update(this.content(this.offset+this.itemCount)),e.below(n[n.length-1]),n.push(e),this.offset++}o!==this.offset&&this._resize()}},update:function(t){var e,i,s,n,o=this,r=this.items,l=this.height(),a=this.itemCount,h=l/2,d=(this.lastTop||0)>t,c=t-h,u=t+l+h;r[0]&&(this.lastTop=t,this.lastDirection=d,d?r[0].top>c&&r[r.length-1].bottom>u+h&&this.offset>0&&(this.offset--,e=r.pop(),i=r[0],e.update(this.content(this.offset)),r.unshift(e),e.above(i),o._resize()):r[r.length-1].bottom<u&&r[0].top<c-h&&(n=this.offset+a,n===this.buffer.total()?this.trigger("endReached"):n!==this.buffer.length&&(e=r.shift(),s=r[r.length-1],r.push(e),e.update(this.content(this.offset+this.itemCount)),o.offset++,e.below(s),o._resize())))},content:function(t){return this.buffer.at(t)},destroy:function(){this.unbind()},_resize:function(){var t=this.items,e=0,i=0,s=t[0],n=t[t.length-1];s&&(e=s.top,i=n.bottom),this.trigger("resize",{top:e,bottom:i}),this.footer&&this.footer.below(n)}});g.mobile.ui.VirtualList=Q,a=g.Class.extend({init:function(e,i){var s=e.append([i],!0)[0],n=s.offsetHeight;t.extend(this,{top:0,element:s,listView:e,height:n,bottom:n})},update:function(t){this.element=this.listView.setDataItem(this.element,t)},above:function(t){t&&(this.height=this.element.offsetHeight,this.top=t.top-this.height,this.bottom=t.top,l(this.element,this.top))},below:function(t){t&&(this.height=this.element.offsetHeight,this.top=t.bottom,this.bottom=this.top+this.height,l(this.element,this.top))},destroy:function(){g.destroy(this.element),t(this.element).remove()}}),h='<div><span class="km-icon"></span><span class="km-loading-left"></span><span class="km-loading-right"></span></div>',d=g.Class.extend({init:function(e){this.element=t('<li class="km-load-more km-scroller-refresh" style="display: none"></li>').appendTo(e.element),this._loadIcon=t(h).appendTo(this.element)},enable:function(){this.element.show(),this.height=w(this.element,!0)},disable:function(){this.element.hide(),this.height=0},below:function(t){t&&(this.top=t.bottom,this.bottom=this.height+this.top,l(this.element,this.top))}}),c=d.extend({init:function(e,i){this._loadIcon=t(h).hide(),this._loadButton=t('<a class="km-load">'+e.options.messages.loadMoreText+"</a>").hide(),this.element=t('<li class="km-load-more" style="display: none"></li>').append(this._loadIcon).append(this._loadButton).appendTo(e.element);var s=this;this._loadButton.kendoMobileButton().data("kendoMobileButton").bind("click",function(){s._hideShowButton(),i.next()}),i.bind("resize",function(){s._showLoadButton()}),this.height=w(this.element,!0),this.disable()},_hideShowButton:function(){this._loadButton.hide(),this.element.addClass("km-scroller-refresh"),this._loadIcon.css("display","block")},_showLoadButton:function(){this._loadButton.show(),this.element.removeClass("km-scroller-refresh"),this._loadIcon.hide()}}),u=g.Class.extend({init:function(t){var e=this;this.chromeHeight=w(t.wrapper.children().not(t.element)),this.listView=t,this.scroller=t.scroller(),this.options=t.options,t.bind("_dataSource",function(t){e.setDataSource(t.dataSource,t.empty)}),t.bind("resize",function(){e.list.items.length&&(e.scroller.reset(),e.buffer.range(0),e.list.refresh())}),this.scroller.makeVirtual(),this._scroll=function(t){e.list.update(t.scrollTop)},this.scroller.bind("scroll",this._scroll),this._scrollEnd=function(t){e.list.batchUpdate(t.scrollTop)},this.scroller.bind("scrollEnd",this._scrollEnd)},destroy:function(){this.list.unbind(),this.buffer.unbind(),this.scroller.unbind("scroll",this._scroll),this.scroller.unbind("scrollEnd",this._scrollEnd)},setDataSource:function(e,i){var s,n,o,r,l=this,h=this.options,u=this.listView,f=u.scroller(),p=h.loadMore;if(this.dataSource=e,s=e.pageSize()||h.virtualViewSize,!s&&!i)throw Error("the DataSource does not have page size configured. Page Size setting is mandatory for the mobile listview virtual scrolling to work as expected.");this.buffer&&this.buffer.destroy(),n=new g.data.Buffer(e,Math.floor(s/2),p),o=p?new c(u,n):new d(u),this.list&&this.list.destroy(),r=new Q({buffer:n,footer:o,item:function(t){return new a(u,t)},height:function(){return f.height()}}),r.bind("resize",function(){l.updateScrollerSize(),u.updateSize()}),r.bind("reset",function(){l.footer.enable()}),r.bind("endReached",function(){o.disable(),l.updateScrollerSize()}),n.bind("expand",function(){r.lastDirection=!1,r.batchUpdate(f.scrollTop)}),t.extend(this,{buffer:n,scroller:f,list:r,footer:o})},updateScrollerSize:function(){this.scroller.virtualSize(0,this.list.totalHeight()+this.chromeHeight)},refresh:function(){this.list.refresh()},reset:function(){this.buffer.range(0),this.list.refresh()}}),f=g.Class.extend({init:function(t){var e,i=this;this.listView=t,this.options=t.options,e=this,this._refreshHandler=function(t){e.refresh(t)},this._progressHandler=function(){t.showLoading()},t.bind("_dataSource",function(t){i.setDataSource(t.dataSource)})},destroy:function(){this._unbindDataSource()},reset:function(){},refresh:function(t){var i,s,n,o,r,l,a,h=t&&t.action,d=t&&t.items,c=this.listView,u=this.dataSource,f=this.options.appendOnRefresh,p=u.view(),m=u.group(),g=m&&m[0];return"itemchange"===h?(c._hasBindingTarget()||(i=c.findByDataItem(d)[0],i&&c.setDataItem(i,d[0])),e):(r="add"===h&&!g||f&&!c._filter,l="remove"===h&&!g,r?s=[]:l&&(s=c.findByDataItem(d)),c.trigger(M,{action:h||"rebind",items:d,removedItems:s,index:t&&t.index})?(this._shouldShowLoading()&&c.hideLoading(),e):("add"!==h||g?"remove"!==h||g?g?c.replaceGrouped(p):f&&!c._filter?(n=c.prepend(p),o=p):c.replace(p):(n=[],c.remove(d)):(a=p.indexOf(d[0]),a>-1&&(n=c.insertAt(d,a),o=d)),this._shouldShowLoading()&&c.hideLoading(),c.trigger(E,{ns:b,addedItems:n,addedDataItems:o}),e))},setDataSource:function(t){this.dataSource&&this._unbindDataSource(),this.dataSource=t,t.bind(A,this._refreshHandler),this._shouldShowLoading()&&this.dataSource.bind(U,this._progressHandler)},_unbindDataSource:function(){this.dataSource.unbind(A,this._refreshHandler).unbind(U,this._progressHandler)},_shouldShowLoading:function(){var t=this.options;return!t.pullToRefresh&&!t.loadMore&&!t.endlessScroll}}),p=g.Class.extend({init:function(e){var i=this,s=e.options.filterable,n="change paste",o=this;this.listView=e,this.options=s,e.element.before(F({placeholder:s.placeholder||"Search..."})),s.autoFilter!==!1&&(n+=" keyup"),this.element=e.wrapper.find(".km-search-form"),this.searchInput=e.wrapper.find("input[type=search]").closest("form").on("submit"+L,function(t){t.preventDefault()}).end().on("focus"+L,function(){i._oldFilter=i.searchInput.val()}).on(n.split(" ").join(L+" ")+L,C(this._filterChange,this)),this.clearButton=e.wrapper.find(".km-filter-reset").on(P,C(this,"_clearFilter")).hide(),this._dataSourceChange=t.proxy(this._refreshInput,this),e.bind("_dataSource",function(t){t.dataSource.bind("change",o._dataSourceChange)})},_refreshInput:function(){var t=this.listView.dataSource.filter(),e=this.listView._filter.searchInput;e.val(t&&t.filters[0].field===this.listView.options.filterable.field?t.filters[0].value:"")},_search:function(t){this._filter=!0,this.clearButton[t?"show":"hide"](),this.listView.dataSource.filter(t)},_filterChange:function(t){var e=this;"paste"==t.type&&this.options.autoFilter!==!1?setTimeout(function(){e._applyFilter()},1):this._applyFilter()},_applyFilter:function(){var t=this.options,e=this.searchInput.val(),i=e.length?{field:t.field,operator:t.operator||"startswith",ignoreCase:t.ignoreCase,value:e}:null;e!==this._oldFilter&&(this._oldFilter=e,this._search(i))},_clearFilter:function(t){this.searchInput.val(""),this._search(null),t.preventDefault()}}),m=S.extend({init:function(e,i){var s=this;S.fn.init.call(this,e,i),e=this.element,i=this.options,i.scrollTreshold&&(i.scrollThreshold=i.scrollTreshold),e.on("down",T,"_highlight").on("move up cancel",T,"_dim"),this._userEvents=new g.UserEvents(e,{fastTap:!0,filter:y,allowSelection:!0,tap:function(t){s._click(t)}}),e.css("-ms-touch-action","auto"),e.wrap(V),this.wrapper=this.element.parent(),this._headerFixer=new N(this),this._itemsCache={},this._templates(),this.virtual=i.endlessScroll||i.loadMore,this._style(),this.options.$angular&&(this.virtual||this.options.pullToRefresh)?setTimeout(t.proxy(this,"_start")):this._start()},_start:function(){var t=this.options;this.options.filterable&&(this._filter=new p(this)),this._itemBinder=this.virtual?new u(this):new f(this),this.options.pullToRefresh&&(this._pullToRefreshHandler=new $(this)),this.setDataSource(t.dataSource),this._enhanceItems(this.items()),g.notify(this,b)},events:[P,M,E,O],options:{name:"ListView",style:"",type:"flat",autoBind:!0,fixedHeaders:!1,template:"#:data#",headerTemplate:'<span class="km-text">#:value#</span>',appendOnRefresh:!1,loadMore:!1,endlessScroll:!1,scrollThreshold:30,pullToRefresh:!1,messages:{loadMoreText:"Press to load more",pullTemplate:"Pull to refresh",releaseTemplate:"Release to refresh",refreshTemplate:"Refreshing"},pullOffset:140,filterable:!1,virtualViewSize:null},refresh:function(){this._itemBinder.refresh()},reset:function(){this._itemBinder.reset()},setDataSource:function(t){var e=!t;this.dataSource=k.create(t),this.trigger("_dataSource",{dataSource:this.dataSource,empty:e}),this.options.autoBind&&!e&&(this.items().remove(),this.dataSource.fetch())},destroy:function(){S.fn.destroy.call(this),g.destroy(this.element),this._userEvents.destroy(),this._itemBinder&&this._itemBinder.destroy(),this._headerFixer&&this._headerFixer.destroy(),this.element.unwrap(),delete this.element,delete this.wrapper,delete this._userEvents},items:function(){return"group"===this.options.type?this.element.find(".km-list").children():this.element.children().not(".km-load-more")},scroller:function(){return this._scrollerInstance||(this._scrollerInstance=this.element.closest(".km-scroll-wrapper").data("kendoMobileScroller")),this._scrollerInstance},showLoading:function(){var t=this.view();t&&t.loader&&t.loader.show()},hideLoading:function(){var t=this.view();t&&t.loader&&t.loader.hide()},insertAt:function(t,e,i){var s=this;return s._renderItems(t,function(n){if(0===e?s.element.prepend(n):e===-1?s.element.append(n):s.items().eq(e-1).after(n),i)for(var o=0;o<n.length;o++)s.trigger(O,{item:n.eq(o),data:t[o],ns:b})})},append:function(t,e){return this.insertAt(t,-1,e)},prepend:function(t,e){return this.insertAt(t,0,e)},replace:function(t){return this.options.type="flat",this._angularItems("cleanup"),g.destroy(this.element.children()),this.element.empty(),this._userEvents.cancel(),this._style(),this.insertAt(t,0)},replaceGrouped:function(e){this.options.type="group",this._angularItems("cleanup"),this.element.empty();var i=t(g.render(this.groupTemplate,e));this._enhanceItems(i.children("ul").children("li")),this.element.append(i),v.init(i),this._style(),this._angularItems("compile")},remove:function(t){var e=this.findByDataItem(t);this.angular("cleanup",function(){return{elements:e}}),g.destroy(e),e.remove()},findByDataItem:function(t){var e,i,s=[];for(e=0,i=t.length;e<i;e++)s[e]="[data-"+g.ns+"uid="+t[e].uid+"]";return this.element.find(s.join(","))},setDataItem:function(e,i){var s=this,n=function(n){var o=t(n[0]);g.destroy(e),s.angular("cleanup",function(){return{elements:[t(e)]}}),t(e).replaceWith(o),s.trigger(O,{item:o,data:i,ns:b})};return this._renderItems([i],n)[0]},updateSize:function(){this._size=this.getSize()},_renderItems:function(e,i){var s=t(g.render(this.template,e));return i(s),this.angular("compile",function(){return{elements:s,data:e.map(function(t){return{dataItem:t}})}}),v.init(s),this._enhanceItems(s),s},_dim:function(t){this._toggle(t,!1)},_highlight:function(t){this._toggle(t,!0)},_toggle:function(e,i){if(!(e.which>1)){var s=t(e.currentTarget),n=s.parent(),o=I(s,"role")||"",r=!o.match(j),l=e.isDefaultPrevented();r&&n.toggleClass(D,i&&!l)}},_templates:function(){var t=this.options.template,e=this.options.headerTemplate,i=' data-uid="#=arguments[0].uid || ""#"',s={},n={};typeof t===q&&(s.template=t,t="#=this.template(data)#"),this.template=C(g.template("<li"+i+">"+t+"</li>"),s),n.template=this.template,typeof e===q&&(n._headerTemplate=e,e="#=this._headerTemplate(data)#"),n.headerTemplate=g.template(e),this.groupTemplate=C(z,n)},_click:function(e){if(!(e.event.which>1||e.event.isDefaultPrevented())){var i,s=e.target,n=t(e.event.target),o=n.closest(g.roleSelector("button","detailbutton","backbutton")),r=g.widgetInstance(o,b),l=s.attr(g.attr("uid"));l&&(i=this.dataSource.getByUid(l)),this.trigger(P,{target:n,item:s,dataItem:i,button:r})&&e.preventDefault()}},_styleGroups:function(){var e=this.element.children();e.children("ul").addClass("km-list"),e.each(function(){var e=t(this),i=e.contents().first();e.addClass("km-group-container"),i.is("ul")||i.is("div."+H)||i.wrap(B)})},_style:function(){var t=this.options,e="group"===t.type,i=this.element,s="inset"===t.style;i.addClass("km-listview").toggleClass("km-list",!e).toggleClass("km-virtual-list",this.virtual).toggleClass("km-listinset",!e&&s).toggleClass("km-listgroup",e&&!s).toggleClass("km-listgroupinset",e&&s),i.parents(".km-listview")[0]||i.closest(".km-content").toggleClass("km-insetcontent",s),e&&this._styleGroups(),this.trigger(R)},_enhanceItems:function(e){e.each(function(){var e,i=t(this),s=!1;i.children().each(function(){e=t(this),e.is("a")?(o(e),s=!0):e.is("label")&&(r(e),s=!0)}),s||n(i)})}}),b.plugin(m)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()});
//# sourceMappingURL=kendo.mobile.listview.min.js.map
