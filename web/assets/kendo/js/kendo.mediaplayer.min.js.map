{"version": 3, "sources": ["kendo.mediaplayer.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "END", "PAUSE", "PLAY", "READY", "TIMECHANGE", "VOLUMECHANGE", "FULLSCREEN_ENTER", "FULLSCREEN_EXIT", "MUTE", "LOW_VOLUME", "HIGH_VOLUME", "VIDEO_QUALITY", "STATE_PLAY", "STATE_PAUSE", "TITLEBAR", "TITLE", "TOOLBARWRAP", "TOOLBAR", "SLIDER", "VOLUME_SLIDER", "MEDIA", "OVERLAY", "YTPLAYER", "DOT", "ui", "ns", "baseTime", "Date", "timeZoneSec", "getTimezoneOffset", "Widget", "isArray", "timeFormats", "shortTime", "longTime", "template", "proxy", "keys", "templates", "htmlPlayer", "titleBar", "toolBar", "youtubePlayer", "toolBarTime", "slider", "volumeSlider", "qualityDropDown", "toolTip", "MediaPlayer", "extend", "init", "element", "options", "this", "wrapper", "fn", "call", "addClass", "_currentIndex", "_createTitlebar", "_createToolbar", "_createDropDown", "_createSlider", "_createVolumeSlider", "_timers", "_aria", "_navigatable", "fullScreen", "media", "notify", "events", "name", "autoPlay", "autoRepeat", "volume", "mute", "navigatable", "forwardSeek", "messages", "pause", "play", "unmute", "quality", "fullscreen", "_msToTime", "ms", "time", "getTime", "setSeconds", "_timeToSec", "curTime", "_titleBar", "find", "length", "append", "sliderElement", "_slider", "_sliderDragChangeHandler", "_sliderDragChange", "_sliderDraggingHandler", "_sliderDragging", "Slide<PERSON>", "smallStep", "tickPlacement", "showButtons", "change", "slide", "tooltip", "dragHandleTitle", "volumeSliderElement", "_volumeSlider", "_volumeDraggingHandler", "_volumeDragging", "_volumeChangeHandler", "_volumeChange", "width", "min", "max", "value", "enabled", "_resetTime", "_youTubeVideo", "_ytmedia", "seekTo", "_media", "currentTime", "_mediaTimeUpdate", "grep", "_toolBar", "items", "e", "_currentUrl", "source", "url", "_isYouTubeUrl", "match", "_setPlayerUrl", "initialized", "oldPlayer", "stop", "toggle", "_initializePlayer", "_videoOverlay", "hide", "loadVideoById", "_getMediaId", "_playStateToggle", "cueVideoById", "show", "remove", "attr", "toolBarElement", "_toolbarClickHandler", "_toolbarClick", "<PERSON><PERSON><PERSON><PERSON>", "click", "resizable", "type", "attributes", "class", "icon", "off", "before", "_volumeButton", "_fullscreenButton", "_currentTimeElement", "_durationElement", "_playButton", "_playButtonSpan", "wrapAll", "dropDownElement", "data", "_dropDownSelectHandler", "_dropDownSelect", "_dropDown", "DropDownList", "dataTextField", "dataValueField", "popup", "position", "origin", "appendTo", "animation", "open", "effects", "duration", "select", "setDataSource", "removeClass", "list", "item", "index", "muted", "target", "children", "first", "isPaused", "hasClass", "_isInFullScreen", "_isDragging", "that", "sender", "tzOffset", "_sliderChangeFired", "_seekBarLastPosition", "setTimeout", "trigger", "_preventPlay", "_changeVolumeButtonImage", "volumeButton", "volumeElement", "cssClass", "substring", "lastIndexOf", "timeInMs", "getCurrentTime", "text", "toString", "_timeFormat", "isPlaying", "is", "_mediaEnded", "_mediaPlay", "_mediaReady", "_mediaDurationChange", "durationTime", "getDuration", "getHours", "setOptions", "_isFirstRun", "_createYoutubePlayer", "_mediaTimeUpdateHandler", "_mediaDurationChangeHandler", "prepend", "_ytPlayer", "css", "height", "YT", "Player", "_configurePlayer", "onYouTubeIframeAPIReadyRegister", "getScript", "onYouTubeIframeAPIReady", "i", "_youtubeApiReady", "_poll", "callback", "interval", "context", "clearTimeout", "callLater", "player", "vars", "autoplay", "wmode", "controls", "rel", "showinfo", "_onYouTubePlayerReady", "onYouTubePlayerReady", "_onPlayerStateChangeHandler", "_onPlayerStateChange", "onPlayerStateChange", "videoId", "playerVars", "onReady", "onStateChange", "event", "getIframe", "style", "_updateTitle", "titlebar", "title", "_paused", "setVolume", "_uiDisplay", "result", "regExp", "_mouseClick", "_mouseMoveHandler", "_mouseMove", "_mouseInHandler", "_mouseIn", "_mouseOutHandler", "_mouseOut", "on", "_createHtmlPlayer", "_mouseClickHanlder", "_mediaEndedHandler", "_mediaCanPlayHandler", "_mediaPlayHandler", "after", "ontimeupdate", "ondurationchange", "oncanplay", "onplay", "onended", "loop", "_mouseIdle", "state", "animationSpeed", "uiElements", "add", "parent", "fadeIn", "fadeOut", "focus", "destroy", "_keyDownHandler", "_fullscreen<PERSON><PERSON>ler", "_youtubeApiReadyHandler", "src", "_mouseMoveTimer", "seek", "seconds", "playVideo", "support", "mobileOS", "stopVideo", "pauseVideo", "toolbar", "dropdown", "enterFullScreen", "get", "requestFullscreen", "webkitRequestFullscreen", "mozRequestFullScreen", "msRequestFullscreen", "document", "cancelFullscreen", "webkitCancelFullScreen", "mozCancelFullScreen", "msCancelFullscreen", "exitFullscreen", "msExitFullscreen", "resize", "_volume", "currentState", "isMuted", "unMute", "getVolume", "isEnded", "getPlayerState", "ended", "_mediaData", "_fullscreen", "_keyDown", "isFullScreen", "mozFullScreen", "webkitIsFullScreen", "fsButton", "preventDefault", "keyCode", "SPACEBAR", "ENTER", "ESC", "_error", "_progress", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBACH,eACA,gBACA,qBACA,iBACDD,IACL,WAo3BE,MAv2BC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAM,MAAOC,EAAQ,QAASC,EAAO,OAAQC,EAAQ,QAASC,EAAa,aAAcC,EAAe,eAAgBC,EAAmB,kBAAmBC,EAAkB,uBAAwBC,EAAO,iBAAkBC,EAAa,kBAAmBC,EAAc,gBAAiBC,EAAgB,wBAAyBC,EAAa,WAAYC,EAAc,YAAaC,EAAW,yBAA0BC,EAAQ,UAAWC,EAAc,6BAA8BC,EAAU,wBAAyBC,EAAS,wBAAyBC,EAAgB,uBAAwBC,EAAQ,sBAAuBC,EAAU,wBAAyBC,EAAW,mBAAoBC,EAAM,IAAKC,EAAK1B,EAAM0B,GAAIC,EAAK,oBAAqBC,EAAW,GAAIC,MAAK,KAAM,EAAG,GAAIC,EAA6C,GAA/BF,EAASG,oBAA0BC,EAAShC,EAAM0B,GAAGM,OAAQC,EAAUnC,EAAEmC,QAASC,GACt3BC,UAAW,QACXC,SAAU,YACXC,EAAWrC,EAAMqC,SAAUC,EAAQxC,EAAEwC,MAAOC,EAAOvC,EAAMuC,KAAMC,GAC9DC,WAAY,iBAAoBnB,EAAQ,cACxCoB,SAAUL,EAAS,eAAkBrB,EAAW,iCAAuCC,EAAQ,8BAC/F0B,QAAS,eAAkBzB,EAAc,iBAAqBC,EAAU,iBACxEyB,cAAe,eAAkBpB,EAAW,YAC5CqB,YAAa,iHACbC,OAAQ,iBAAoB1B,EAAS,iCACrC2B,aAAc,iBAAoB1B,EAAgB,qBAClD2B,gBAAiB,iBAAoBnC,EAAgB,6BACrDoC,QAAS,oDAEbC,EAAclB,EAAOmB,QACrBC,KAAM,SAAUC,EAASC,GACrBC,KAAKC,QAAU1D,EAAEuD,GACjBrB,EAAOyB,GAAGL,KAAKM,KAAKH,KAAMF,EAASC,GACnCC,KAAKC,QAAQG,SAAS,0BACtBL,EAAUC,KAAKD,QACfC,KAAKK,cAAgB,EACrBL,KAAKM,kBACLN,KAAKO,iBACLP,KAAKQ,kBACLR,KAAKS,gBACLT,KAAKU,sBACLV,KAAKW,WACLX,KAAKY,QACLZ,KAAKa,eACDd,EAAQe,YACRd,KAAKc,YAAW,GAEhBf,EAAQgB,OACRf,KAAKe,MAAMf,KAAKD,QAAQgB,OAE5BtE,EAAMuE,OAAOhB,OAEjBiB,QACItE,EACAC,EACAC,EACAC,EACAC,EACAC,GAEJ+C,SACImB,KAAM,cACNC,UAAU,EACVC,YAAY,EACZC,OAAQ,IACRP,YAAY,EACZQ,MAAM,EACNC,aAAa,EACbC,aAAa,EACbT,MAAO,KACPU,UACIC,MAAS,QACTC,KAAQ,OACRL,KAAQ,OACRM,OAAU,SACVC,QAAW,UACXC,WAAc,gBAGtBC,UAAW,SAAUC,GACjB,GAAIC,GAAO,GAAI3D,MAAKD,EAAS6D,UAE7B,OADAD,GAAKE,WAAWH,GACTC,GAEXG,WAAY,SAAUH,GAClB,GAAII,GAAU,GAAI/D,MAAK2D,GAAMC,SAC7B,OAAOG,GAAU,KAErB/B,gBAAiB,WACbN,KAAKsC,UAAYtC,KAAKC,QAAQsC,KAAKrE,EAAMT,GACX,IAA1BuC,KAAKsC,UAAUE,SACfxC,KAAKC,QAAQwC,OAAOxD,EAAUE,UAC9Ba,KAAKsC,UAAYtC,KAAKC,QAAQsC,KAAKrE,EAAMT,KAGjDgD,cAAe,WACX,GAAIiC,GAAgB1C,KAAKC,QAAQsC,KAAKrE,EAAML,EACvCmC,MAAK2C,UACN3C,KAAK4C,yBAA2B7D,EAAMiB,KAAK6C,kBAAmB7C,MAC9DA,KAAK8C,uBAAyB/D,EAAMiB,KAAK+C,gBAAiB/C,MAC1D0C,EAAgB1C,KAAKC,QAAQsC,KAAKrE,EAAML,GACxCmC,KAAK2C,QAAU,GAAIxE,GAAG6E,OAAON,EAAc,IACvCO,UAAW,IACXC,cAAe,OACfC,aAAa,EACbC,OAAQpD,KAAK4C,yBACbS,MAAOrD,KAAK8C,uBACZQ,SAAWxE,SAAUG,EAAUS,SAC/B6D,gBAAiB,OAI7B7C,oBAAqB,WACjB,GAAI8C,GAAsBxD,KAAKC,QAAQsC,KAAKrE,EAAMJ,EAC7CkC,MAAKyD,gBACNzD,KAAK0D,uBAAyB3E,EAAMiB,KAAK2D,gBAAiB3D,MAC1DA,KAAK4D,qBAAuB7E,EAAMiB,KAAK6D,cAAe7D,MACtDwD,EAAoBM,MAAM,IAC1B9D,KAAKyD,cAAgB,GAAItF,GAAG6E,OAAOQ,EAAoB,IACnDP,UAAW,EACXc,IAAK,EACLC,IAAK,IACLC,MAAOjE,KAAKD,QAAQsB,OACpBgC,MAAOrD,KAAK0D,uBACZN,OAAQpD,KAAK4D,qBACbV,cAAe,OACfC,aAAa,EACbG,SAAWY,SAAS,GACpBX,gBAAiB,OAI7BY,WAAY,WACJnE,KAAKoE,cACLpE,KAAKqE,SAASC,OAAO,GAAG,GAExBtE,KAAKuE,OAAOC,YAAc,EAE9BxE,KAAKyE,mBACLlI,EAAEmI,KAAK1E,KAAK2E,SAAS5E,QAAQ6E,MAAO,SAAUC,GAC1C,QAASA,EAAE/F,WACZA,SAAWG,EAAUK,aAE5BwF,YAAa,WACT,GAAI/D,GAAQf,KAAKe,OACjB,OAAOrC,GAAQqC,EAAMgE,QAAUhE,EAAMgE,OAAO/E,KAAKK,eAAe2E,IAAMjE,EAAMgE,QAEhFE,cAAe,WACX,QAASjF,KAAK8E,cAAcI,MAAM,2BAEtCC,cAAe,WAAA,GAQPC,GAPAC,EAAYrF,KAAKoE,aACrBpE,MAAKsF,OACLtF,KAAKoE,cAAgBpE,KAAKiF,gBACtBI,IAAcrF,KAAKoE,gBACnBpE,KAAKC,QAAQsC,KAAKrE,EAAMD,GAAUsH,SAClCvF,KAAKC,QAAQsC,KAAKrE,EAAMH,GAAOwH,UAE/BH,EAAcpF,KAAKuE,QAAUvE,KAAKqE,SACtCrE,KAAKwF,oBACDJ,IACApF,KAAKsB,KAAKtB,KAAKsB,QACftB,KAAKqB,OAAOrB,KAAKqB,WAEhBrB,KAAKoE,cAOCpE,KAAKqE,WACRrE,KAAKyF,eACLzF,KAAKyF,cAAcC,OAEnB1F,KAAKD,QAAQoB,UACbnB,KAAKqE,SAASsB,cAAc3F,KAAK4F,eACjC5F,KAAK6F,kBAAiB,KAEtB7F,KAAKqE,SAASyB,aAAa9F,KAAK4F,eAChC5F,KAAK6F,kBAAiB,MAf1B7F,KAAKyF,cAAcM,OACnB/F,KAAKC,QAAQsC,KAAKrE,EAAMH,EAAQ,aAAaiI,SAC7ChG,KAAKC,QAAQsC,KAAKrE,EAAMH,GAAOkI,KAAK,MAAOjG,KAAK8E,eAC5C9E,KAAKD,QAAQoB,UACbnB,KAAK2B,SAejBpB,eAAgB,WACZ,GAAI2F,GAAiBlG,KAAKC,QAAQsC,KAAKrE,EAAMN,EACf,KAA1BsI,EAAe1D,SACfxC,KAAKmG,qBAAuBpH,EAAMiB,KAAKoG,cAAepG,MACtDA,KAAKC,QAAQwC,OAAOxD,EAAUG,SAC9B8G,EAAiBlG,KAAKC,QAAQsC,KAAKrE,EAAMN,GACzCsI,EAAepC,MAAM9D,KAAKC,QAAQsC,KAAKrE,EAAMH,GAAO+F,SACpD9D,KAAK2E,SAAW,GAAIxG,GAAGkI,QAAQH,GAC3BI,MAAOtG,KAAKmG,qBACZI,WAAW,EACX3B,QAEQ4B,KAAM,SACNC,YAAcC,QAAS,iBACvBC,KAAM,SAGN7H,SAAUG,EAAUK,YACpBmH,YAAcC,QAAS,oCAGvBF,KAAM,YACNC,YAAcC,QAAS,sBAGvBF,KAAM,SACNC,YAAcC,QAAS,mBACvBC,KAAM,cAGN7H,SAAUG,EAAUO,aACpBiH,YAAcC,QAAS,+BAGvB5H,SAAUG,EAAUQ,gBACpBgH,YAAcC,QAAS,gCAGvBF,KAAM,SACNC,YAAcC,QAAS,uBACvBC,KAAM,kBAIlB3G,KAAK2E,SAAS1E,QAAQ2G,IAAI,WAC1BV,EAAeW,OAAO5H,EAAUM,QAChCS,KAAK8G,cAAgBZ,EAAe3D,KAAK,oBACzCvC,KAAK+G,kBAAoBb,EAAe3D,KAAK,wBAC7CvC,KAAK8G,cAAcb,KAAK,QAASjG,KAAKD,QAAQuB,KAAOtB,KAAKD,QAAQ0B,SAASG,OAAS5B,KAAKD,QAAQ0B,SAASH,MAC1GtB,KAAK8G,cAAcb,KAAK,aAAcjG,KAAKD,QAAQuB,KAAOtB,KAAKD,QAAQ0B,SAASG,OAAS5B,KAAKD,QAAQ0B,SAASH,MAC/GtB,KAAK+G,kBAAkBd,KAAK,QAASjG,KAAKD,QAAQ0B,SAASK,YAC3D9B,KAAK+G,kBAAkBd,KAAK,aAAcjG,KAAKD,QAAQ0B,SAASK,YAChEoE,EAAepC,MAAM,QACrB9D,KAAKgH,oBAAsBd,EAAe3D,KAAK,8BAC/CvC,KAAKiH,iBAAmBf,EAAe3D,KAAK,2BAC5CvC,KAAKkH,YAAchB,EAAe3D,KAAK,kBACvCvC,KAAKmH,gBAAkBnH,KAAKkH,YAAY3E,KAAK,aACzCvC,KAAKD,QAAQoB,UACbnB,KAAK6F,kBAAiB,GAE1BtJ,GACIyD,KAAK8G,cAAc,GACnBZ,EAAe3D,KAAK,8BAA8B,GAClD2D,EAAe3D,KAAK,+BAA+B,GACnDvC,KAAK+G,kBAAkB,KACxBK,QAAQ,iCACXlB,EAAe3D,KAAK,aAAanC,SAAS,YAGlDI,gBAAiB,WAAA,GACT6G,GAAkBrH,KAAKC,QAAQsC,KAAKrE,EAAMZ,GAC1CyD,EAAQf,KAAKe,OACwC,KAA9CsG,EAAgBC,KAAK,uBAC5BtH,KAAKuH,uBAAyBxI,EAAMiB,KAAKwH,gBAAiBxH,MAC1DA,KAAKyH,UAAY,GAAItJ,GAAGuJ,aAAaL,GACjCM,cAAe,UACfC,eAAgB,MAChBC,OACIC,SAAU,SACVC,OAAQ,MACRC,SAAUhI,KAAKC,SAEnBgI,WACIC,MACIC,QAAS,aACTC,SAAU,IAGlBC,OAAQrI,KAAKuH,yBAEbxG,GAASrC,EAAQqC,EAAMgE,UACvB/E,KAAKyH,UAAUa,cAAcvH,EAAMgE,QACnC/E,KAAKyH,UAAUY,OAAO,IAE1BrI,KAAKyH,UAAUxH,QAAQG,SAAS,mBAChCJ,KAAKyH,UAAUxH,QAAQgG,KAAK,QAASjG,KAAKD,QAAQ0B,SAASI,SAAS6D,OACpE1F,KAAKyH,UAAUxH,QAAQsC,KAAK,0BAA0BgG,YAAY,qBAAqBnI,SAAS,iBAChGJ,KAAKyH,UAAUe,KAAKpI,SAAS,oBAGrCoH,gBAAiB,SAAU3C,GACnB7E,KAAKK,gBAAkBwE,EAAE4D,KAAKC,UAC9B1I,KAAKK,cAAgBwE,EAAE4D,KAAKC,QAC5B1I,KAAKmF,kBAGbiB,cAAe,SAAUvB,GAAV,GAuBH8D,GAtBJC,EAASrM,EAAEsI,EAAE+D,QAAQC,WAAWC,QAChCC,EAAWH,EAAOI,SAASzL,EAC1ByC,MAAKe,WAGN6H,EAAOI,SAASzL,IAAeqL,EAAOI,SAASxL,MAC3CuL,EACA/I,KAAK2B,OAEL3B,KAAK0B,UAGTkH,EAAOI,SAAS/L,IAAqB2L,EAAOI,SAAS9L,MACjD8C,KAAKiJ,iBACLL,EAAOL,YAAYrL,GAAiBkD,SAASnD,GAC7C+C,KAAKc,YAAW,KAEhB8H,EAAOL,YAAYtL,GAAkBmD,SAASlD,GAC9C8C,KAAKc,YAAW,MAGpB8H,EAAOI,SAAS7L,IAASyL,EAAOI,SAAS5L,IAAewL,EAAOI,SAAS3L,MACpEsL,EAAQ3I,KAAKsB,OACjBtB,KAAKsB,MAAMqH,MAGnB5F,gBAAiB,WACR/C,KAAKe,UAGVf,KAAKkJ,aAAc,IAEvBrG,kBAAmB,SAAUgC,GAAV,GACXsE,GAAOnJ,KACPT,EAASsF,EAAEuE,OACXC,EAAyB,IAAd9K,CACVyB,MAAKe,UAGVoI,EAAKG,oBAAqB,EAC1BH,EAAKD,aAAc,GACdlJ,KAAKD,QAAQyB,aAAejC,EAAO0E,QAAUjE,KAAKuJ,qBACnDC,WAAW,WACPjK,EAAO0E,MAAMkF,EAAKI,uBACnB,GACIvJ,KAAKoE,cACZ+E,EAAK9E,SAASC,OAAO6E,EAAK/G,WAAWyC,EAAEZ,MAAQoF,IAE/CF,EAAK5E,OAAOC,YAAc2E,EAAK/G,WAAWyC,EAAEZ,MAAQoF,GAExDF,EAAKM,QAAQ1M,GACboM,EAAKO,cAAe,IAExBC,yBAA0B,SAAUtI,GAAV,GAClBuI,GAAe5J,KAAK8G,cACpB+C,EAAgBD,EAAarH,KAAK,QAClCuH,EAAWD,EAAc5D,KAAK,QAClC6D,GAAWA,EAASC,UAAU,EAAGD,EAASE,YAAY,MACvC,IAAX3I,GACAwI,EAAc5D,KAAK,QAAS6D,EAAW,IAAM3M,GAC7CyM,EAAa3D,KAAK,QAASjG,KAAKD,QAAQ0B,SAASG,QACjDgI,EAAa3D,KAAK,aAAcjG,KAAKD,QAAQ0B,SAASG,SAC/CP,EAAS,GAAKA,EAAS,IAC9BwI,EAAc5D,KAAK,QAAS6D,EAAW,IAAM1M,GAC7CwM,EAAa3D,KAAK,QAASjG,KAAKD,QAAQ0B,SAASH,MACjDsI,EAAa3D,KAAK,aAAcjG,KAAKD,QAAQ0B,SAASH,QAEtDuI,EAAc5D,KAAK,QAAS6D,EAAW,IAAMzM,GAC7CuM,EAAa3D,KAAK,QAASjG,KAAKD,QAAQ0B,SAASH,MACjDsI,EAAa3D,KAAK,aAAcjG,KAAKD,QAAQ0B,SAASH,QAG9DqC,gBAAiB,SAAUkB,GAClB7E,KAAKe,UAGVf,KAAKqB,OAAOwD,EAAEZ,OACdjE,KAAK2J,yBAAyB9E,EAAEZ,OAChCjE,KAAKyJ,QAAQzM,KAEjB6G,cAAe,SAAUgB,GAChB7E,KAAKe,UAGVf,KAAKqB,OAAOwD,EAAEZ,OACdjE,KAAK2J,yBAAyB9E,EAAEZ,OAChCjE,KAAKyJ,QAAQzM,KAEjByH,iBAAkB,WAAA,GAGVwF,GAFAzF,EAAcxE,KAAKoE,cAAgBpE,KAAKqE,SAAS6F,iBAAmBlK,KAAKuE,OAAOC,WAQpF,OAPAA,GAAcA,EAAcA,EAAc,EACtCyF,EAAWjK,KAAK+B,UAAUyC,GAC9BxE,KAAKgH,oBAAoBmD,KAAK1N,EAAM2N,SAASH,EAAUjK,KAAKqK,cACvDrK,KAAKkJ,cACNlJ,KAAKuJ,qBAAqD,KAA7B/E,EAAcjG,GAC3CyB,KAAK2C,QAAQsB,MAAMjE,KAAKuJ,uBAErBvJ,KAAKsK,aAEhBzE,iBAAkB,SAAUlE,GACJ,IAATA,IACPA,EAAO3B,KAAKmH,gBAAgBoD,GAAGrM,EAAMX,IAErCoE,GACA3B,KAAKmH,gBAAgBoB,YAAYhL,GAAY6C,SAAS5C,GACtDwC,KAAKkH,YAAYjB,KAAK,QAASjG,KAAKD,QAAQ0B,SAASC,OACrD1B,KAAKkH,YAAYjB,KAAK,aAAcjG,KAAKD,QAAQ0B,SAASC,SAE1D1B,KAAKmH,gBAAgBoB,YAAY/K,GAAa4C,SAAS7C,GACvDyC,KAAKkH,YAAYjB,KAAK,QAASjG,KAAKD,QAAQ0B,SAASE,MACrD3B,KAAKkH,YAAYjB,KAAK,aAAcjG,KAAKD,QAAQ0B,SAASE,QAGlE6I,YAAa,WACTxK,KAAK6F,kBAAiB,GACtB7F,KAAKgH,oBAAoBmD,KAAK1N,EAAM2N,SAASpK,KAAK+B,UAAU,GAAI/B,KAAKqK,cACrErK,KAAK2C,QAAQsB,MAA0B,KAAnB,EAAI1F,IACxByB,KAAKyJ,QAAQ9M,IAEjB8N,WAAY,WACRzK,KAAKyJ,QAAQ5M,IAEjB6N,YAAa,WACT1K,KAAKyJ,QAAQ3M,IAEjB6N,qBAAsB,WAClB,GAAIC,GAAe5K,KAAK+B,UAAU/B,KAAKoE,cAAgBpE,KAAKqE,SAASwG,cAAgB7K,KAAKuE,OAAO6D,SACjGpI,MAAKqK,YAA0C,IAA5BO,EAAaE,WAAmBnM,EAAYC,UAAYD,EAAYE,SACvFmB,KAAKiH,iBAAiBkD,KAAK1N,EAAM2N,SAASQ,EAAc5K,KAAKqK,cAC7DrK,KAAK2C,QAAQoI,YACThH,IAAK1F,EAAS6D,UACd8B,IAAK4G,EAAa1I,YAEjBlC,KAAKgL,cACNhL,KAAKmE,aACLnE,KAAKgL,aAAc,IAG3BC,qBAAsB,WAClBjL,KAAKkL,wBAA0BnM,EAAMiB,KAAKyE,iBAAkBzE,MAC5DA,KAAKmL,4BAA8BpM,EAAMiB,KAAK2K,qBAAsB3K,MACpEA,KAAKC,QAAQmL,QAAQnM,EAAUI,eAC/BW,KAAKqL,UAAYrL,KAAKC,QAAQsC,KAAKrE,EAAMD,GAAU,GACnD1B,EAAEyD,KAAKqL,WAAWC,KACdxH,MAAO9D,KAAKC,QAAQ6D,QACpByH,OAAQvL,KAAKC,QAAQsL,WAEpB7O,OAAO8O,IAAO9O,OAAO8O,GAAGC,OAgBzBzL,KAAK0L,oBAfAhP,OAAOiP,kCACRjP,OAAOiP,mCACPpP,EAAEqP,UAAU,sCACZlP,OAAOmP,wBAA0B,WAC7B,GAAInP,OAAOiP,gCACP,IAAK,GAAIG,GAAI,EAAGA,EAAIpP,OAAOiP,gCAAgCnJ,OAAQsJ,IAC/DpP,OAAOiP,gCAAgCG,GAAGC,kBAGlDrP,QAAOiP,gCAAgCnJ,OAAS,EAChD9F,OAAOiP,gCAAkCnP,IAGjDE,OAAOiP,gCAAgCjP,OAAOiP,gCAAgCnJ,QAAUxC,OAKhGgM,MAAO,SAAU9K,EAAM+K,EAAUC,EAAUC,GACvC,GAAIhD,GAAOnJ,IAWX,OAV2B,QAAvBmJ,EAAKxI,QAAQO,IACbkL,aAAajD,EAAKxI,QAAQO,IAE9BiI,EAAKxI,QAAQO,GAAQsI,WAAW,SAAU2C,GACtC,MAAO,SAASE,KACRJ,EAAS9L,KAAKgM,KACdhD,EAAKxI,QAAQO,GAAQsI,WAAW6C,EAAWH,MAGrDC,GAAUD,IAGhBH,iBAAkB,WACd/L,KAAK0L,oBAETA,iBAAkB,WAAA,GAYVY,GAXAC,GACAC,UAAaxM,KAAKD,QAAQoB,SAC1BsL,MAAS,cACTC,SAAY,EACZC,IAAO,EACPC,SAAY,EAEhB5M,MAAK6M,sBAAwB9N,EAAMiB,KAAK6M,sBAAuB7M,MAC/DtD,OAAOoQ,qBAAuB9M,KAAK6M,sBACnC7M,KAAK+M,4BAA8BhO,EAAMiB,KAAKgN,qBAAsBhN,MACpEtD,OAAOuQ,oBAAsBjN,KAAKgN,qBAC9BV,EAAS,GAAI5P,QAAO8O,GAAGC,OAAOzL,KAAKC,QAAQsC,KAAKrE,EAAMD,GAAU,IAChEsN,OAAQvL,KAAKC,QAAQsL,SACrBzH,MAAO9D,KAAKC,QAAQ6D,QACpBoJ,QAASlN,KAAK4F,cACduH,WAAYZ,EACZtL,QACImM,QAAWpN,KAAK6M,sBAChBQ,cAAiBrN,KAAK+M,gCAIlCF,sBAAuB,SAAUS,GAC7BtN,KAAKqE,SAAWiJ,EAAM1E,OACtB5I,KAAKqE,SAASkJ,YAAYC,MAAM1J,MAAQ,OACxC9D,KAAKqE,SAASkJ,YAAYC,MAAMjC,OAAS,OACzCvL,KAAKoE,eAAgB,EACrBpE,KAAKmL,8BACDnL,KAAKD,QAAQoB,UACbnB,KAAK6F,kBAAiB,GACtB7F,KAAKqE,SAASsB,cAAc3F,KAAK4F,gBAEjC5F,KAAKqE,SAASyB,aAAa9F,KAAK4F,eAEhC5F,KAAKD,QAAQuB,MACbtB,KAAKsB,MAAK,GAEdtB,KAAKyJ,QAAQ3M,IAEjB2Q,aAAc,WACVzN,KAAK0N,WAAWvD,KAAKnK,KAAKe,QAAQ4M,OAAS3N,KAAKe,QAAQgE,SAE5DiI,qBAAsB,SAAUM,GACT,IAAfA,EAAMhG,MACNtH,KAAK2C,QAAQsB,MAAM,GACnBjE,KAAK4N,SAAU,EACf5N,KAAK6F,kBAAiB,GACtB7F,KAAKyJ,QAAQ9M,GACTqD,KAAKD,QAAQqB,YACbpB,KAAK2B,QAEa,IAAf2L,EAAMhG,MACbtH,KAAK2K,uBACL3K,KAAKqE,SAASwJ,UAAU7N,KAAKqB,UACzBrB,KAAKsJ,mBACLtJ,KAAKsJ,oBAAqB,EAE1BtJ,KAAK8N,YAAW,GAEpB9N,KAAKyJ,QAAQ5M,GACbmD,KAAK6F,kBAAiB,GACtB7F,KAAKgM,MAAM,WAAYhM,KAAKyE,iBAAkB,IAAKzE,MACnDA,KAAK4N,SAAU,GACO,IAAfN,EAAMhG,OACRtH,KAAK4N,UACN5N,KAAK8N,YAAW,GAChB9N,KAAK6F,kBAAiB,GACtB7F,KAAKyJ,QAAQ7M,GACboD,KAAK4N,SAAU,KAI3BhI,YAAa,WAAA,GACLmI,GAAS/N,KAAK8E,cACdkJ,EAAS,8EACT9I,EAAQ6I,EAAO7I,MAAM8I,EAIzB,OAHI9I,IAA6B,KAApBA,EAAM,GAAG1C,SAClBuL,EAAS7I,EAAM,IAEZ6I,GAEXE,YAAa,WACLjO,KAAK+I,WACL/I,KAAK2B,OAEL3B,KAAK0B,SAGb8D,kBAAmB,WACVxF,KAAKkO,oBACNlO,KAAKkO,kBAAoBnP,EAAMiB,KAAKmO,WAAYnO,MAChDA,KAAKoO,gBAAkBrP,EAAMiB,KAAKqO,SAAUrO,MAC5CA,KAAKsO,iBAAmBvP,EAAMiB,KAAKuO,UAAWvO,MAC9CzD,EAAEyD,KAAKC,SAASuO,GAAG,aAAepQ,EAAI4B,KAAKoO,iBAAiBI,GAAG,aAAepQ,EAAI4B,KAAKsO,kBAAkBE,GAAG,YAAcpQ,EAAI4B,KAAKkO,qBAElIlO,KAAKqE,UAAYrE,KAAKoE,cACvBpE,KAAKiL,uBACGjL,KAAKuE,QAAWvE,KAAKoE,eAC7BpE,KAAKyO,qBAGbA,kBAAmB,WACVzO,KAAKyF,gBACNzF,KAAK0O,mBAAqB3P,EAAMiB,KAAKiO,YAAajO,MAClDA,KAAKC,QAAQwC,OAAO,eAAkBzE,EAAU,YAChDgC,KAAKyF,cAAgBzF,KAAKC,QAAQsC,KAAK,0BAA0BiM,GAAG,QAAUpQ,EAAI4B,KAAK0O,qBAE3F1O,KAAKkL,wBAA0BnM,EAAMiB,KAAKyE,iBAAkBzE,MAC5DA,KAAKmL,4BAA8BpM,EAAMiB,KAAK2K,qBAAsB3K,MACpEA,KAAK2O,mBAAqB5P,EAAMiB,KAAKwK,YAAaxK,MAClDA,KAAK4O,qBAAuB7P,EAAMiB,KAAK0K,YAAa1K,MACpDA,KAAK6O,kBAAoB9P,EAAMiB,KAAKyK,WAAYzK,MAChDA,KAAKyF,cAAcqJ,MAAM7P,EAAUC,YACnCc,KAAKuE,OAASvE,KAAKC,QAAQsC,KAAKrE,EAAMH,GAAO,GAC7CxB,EAAEyD,KAAKuE,QAAQ+G,KACXxH,MAAO,OACPyH,OAAQ,SAERvL,KAAKD,QAAQuB,MACbtB,KAAKsB,MAAK,GAEdtB,KAAKuE,OAAOwK,aAAe/O,KAAKkL,wBAChClL,KAAKuE,OAAOyK,iBAAmBhP,KAAKmL,4BACpCnL,KAAKuE,OAAO0K,UAAYjP,KAAK4O,qBAC7B5O,KAAKuE,OAAO2K,OAASlP,KAAK6O,kBAC1B7O,KAAKuE,OAAO4K,QAAUnP,KAAK2O,mBAC3B3O,KAAKuE,OAAO6K,KAAOpP,KAAKD,QAAQqB,YAEpCiN,SAAU,WACNrO,KAAK8N,YAAW,IAEpBS,UAAW,WACPvO,KAAKgM,MAAM,YAAahM,KAAKqP,WAAY,IAAMrP,OAEnDqP,WAAY,WAER,MADArP,MAAK8N,YAAW,IACT,GAEXK,WAAY,WACFnO,KAAKsC,UAAUiI,GAAG,cAAgBvK,KAAK2E,SAAS7E,QAAQyK,GAAG,cAAgBvK,KAAK2C,QAAQ1C,QAAQsK,GAAG,cACrGvK,KAAK8N,YAAW,GAEpB9N,KAAKgM,MAAM,YAAahM,KAAKqP,WAAY,IAAMrP,OAEnD8N,WAAY,SAAUwB,GAAV,GACJC,GAAiB,OACjBC,EAAaxP,KAAKsC,UAAUmN,IAAIzP,KAAK2E,SAAS7E,QAAQ4P,SACtDJ,GACAE,EAAWG,OAAOJ,IAElBC,EAAWI,QAAQL,GACfvP,KAAKD,QAAQwB,aACbvB,KAAKC,QAAQ4P,UAIzB9E,WAAY,SAAUhL,GAClBtB,EAAOyB,GAAG6K,WAAW5K,KAAKH,KAAMD,IAEpC+P,QAAS,WACLrR,EAAOyB,GAAG4P,QAAQ3P,KAAKH,MAClBA,KAAK+I,YACN/I,KAAK0B,QAET1B,KAAKF,QAAQ8G,IAAIxI,GACjB4B,KAAKF,QAAQyC,KAAKrE,EAAMF,GAAS4I,IAAIxI,GACrC4B,KAAKW,QAAU,KACfX,KAAKkO,kBAAoB,KACzBlO,KAAKsO,iBAAmB,KACxBtO,KAAKoO,gBAAkB,KACvBpO,KAAK0O,mBAAqB,KAC1B1O,KAAK+P,gBAAkB,KACvB/P,KAAKgQ,mBAAqB,KAC1BhQ,KAAKmG,qBAAuB,KAC5BnG,KAAK4C,yBAA2B,KAChC5C,KAAK8C,uBAAyB,KAC9B9C,KAAK0D,uBAAyB,KAC9B1D,KAAK4D,qBAAuB,KAC5B5D,KAAKiQ,wBAA0B,KAC/BjQ,KAAK6M,sBAAwB,KAC7B7M,KAAK+M,4BAA8B,KACnC/M,KAAKuH,uBAAyB,KAC1BvH,KAAKoE,cACLpE,KAAKqE,SAASyL,WAEd9P,KAAKuE,OAAOwK,aAAe/O,KAAKkL,wBAA0B,KAC1DlL,KAAKuE,OAAOyK,iBAAmBhP,KAAKmL,4BAA8B,KAClEnL,KAAKuE,OAAO0K,UAAYjP,KAAK4O,qBAAuB,KACpD5O,KAAKuE,OAAO2K,OAASlP,KAAK6O,kBAAoB,KAC9C7O,KAAKuE,OAAO4K,QAAUnP,KAAK2O,mBAAqB,KAChD3O,KAAKuE,OAAO2L,IAAM,GAClBlQ,KAAKuE,OAAOyB,UAEhBhG,KAAKmQ,gBAAkB,KACvB/D,aAAapM,KAAKmQ,iBAClB1T,EAAMqT,QAAQ9P,KAAKF,UAEvBsQ,KAAM,SAAUpO,GACZ,GAAkB,IAAPA,EACP,MAAO,KAAOhC,KAAKoE,cAAgBpE,KAAKqE,SAAS6F,iBAAmBlK,KAAKuE,OAASvE,KAAKuE,OAAOC,YAAc,CAEhH,IAAI6L,GAAUrO,EAAK,GAUnB,OATIhC,MAAKoE,cACDiM,EAAU,GAAKrQ,KAAKqE,SAASwG,cAAgB,EAC7C7K,KAAKqE,SAASC,OAAOtE,KAAKqE,SAASwG,cAAgB,EAAI,GAAG,GAE1D7K,KAAKqE,SAASC,OAAO+L,GAAS,GAGlCrQ,KAAKuE,OAAOC,YAAc6L,EAEvBrQ,MAEX2B,KAAM,WAWF,MAVI3B,MAAKoE,cACLpE,KAAKqE,SAASiM,aAEV7T,EAAM8T,QAAQC,UACdxQ,KAAK8N,YAAW,GAEpB9N,KAAKuE,OAAO5C,QAEhB3B,KAAK4N,SAAU,EACf5N,KAAK6F,kBAAiB,GACf7F,MAEXsF,KAAM,WAYF,MAXItF,MAAKoE,eAAiBpE,KAAKqE,SAC3BrE,KAAKqE,SAASoM,YACPzQ,KAAKuE,SAAWvE,KAAKoE,gBACxB3H,EAAM8T,QAAQC,UACdxQ,KAAK8N,YAAW,GAEpB9N,KAAKuE,OAAO7C,QACZ1B,KAAKuE,OAAOC,YAAc,GAE9BxE,KAAK4N,SAAU,EACf5N,KAAK6F,kBAAiB,GACf7F,MAEX0B,MAAO,WAYH,MAXI1B,MAAKoE,cACLpE,KAAKqE,SAASqM,cAEVjU,EAAM8T,QAAQC,UACdxQ,KAAK8N,YAAW,GAEpB9N,KAAKuE,OAAO7C,SAEhB1B,KAAK4N,SAAU,EACf5N,KAAK6F,kBAAiB,GACtB7F,KAAKyJ,QAAQ7M,GACNoD,MAEX2Q,QAAS,WACL,MAAO3Q,MAAK2E,UAEhBiM,SAAU,WACN,MAAO5Q,MAAKyH,WAEhBiG,SAAU,WACN,MAAO1N,MAAKsC,WAEhBxB,WAAY,SAAU+P,GAClB,GAA+B,IAApBA,EACP,MAAO7Q,MAAKiJ,kBAAmB,CAEnC,IAAInJ,GAAUE,KAAKF,QAAQgR,IAAI,EAC3BD,IACA7Q,KAAKF,QAAQM,SAAS,4BAClBN,EAAQiR,kBACRjR,EAAQiR,oBACDjR,EAAQkR,wBACflR,EAAQkR,0BACDlR,EAAQmR,qBACfnR,EAAQmR,uBACDnR,EAAQoR,qBACfpR,EAAQoR,sBAEZlR,KAAKiJ,iBAAkB,IAEnBkI,SAASC,iBACTD,SAASC,mBACFD,SAASE,uBAChBF,SAASE,yBACFF,SAASG,oBAChBH,SAASG,sBACFH,SAASI,mBAChBJ,SAASI,qBACFJ,SAASK,eAChBL,SAASK,iBACFL,SAASM,kBAChBN,SAASM,mBAEbzR,KAAKF,QAAQyI,YAAY,4BACzBvI,KAAKiJ,iBAAkB,GAE3BjJ,KAAK2C,QAAQ+O,UAEjBrQ,OAAQ,SAAU4C,GACd,MAAqB,KAAVA,EACwB,IAAjBjE,KAAK2R,QAA0B3R,KAAK2R,QAAU3R,KAAK2R,QAAU3R,KAAKD,QAAQsB,QAE5FrB,KAAK2R,QAAU1N,EACfjE,KAAKsB,KAAK2C,GAAS,GACfjE,KAAKoE,cACLpE,KAAKqE,SAASwJ,UAAU7N,KAAK2R,SAE7B3R,KAAKuE,OAAOlD,OAASrB,KAAK2R,QAAU,IAExC3R,KAAKyD,cAAcQ,MAAMA,GAPzBjE,IASJsB,KAAM,SAAUqH,GACZ,GAAIiJ,GAAe5R,KAAKoE,cAAgBpE,KAAKqE,UAAYrE,KAAKqE,SAASwN,UAAY7R,KAAKuE,QAAUvE,KAAKuE,OAAOoE,KAC9G,OAAqB,KAAVA,GAAyBA,IAAUiJ,EACnCA,GAEP5R,KAAKoE,cACDuE,EACA3I,KAAKqE,SAAS/C,OAEdtB,KAAKqE,SAASyN,SAGlB9R,KAAKuE,OAAOoE,MAAQA,EAGpB3I,KAAKyD,cAAcQ,MADnB0E,EACyB,EAEA3I,KAAKuE,QAA+B,IAArBvE,KAAKuE,OAAOlD,QAAgBrB,KAAKqE,UAAYrE,KAAKqE,SAAS0N,aAEvG/R,KAAKyJ,QAAQzM,GACbgD,KAAK2J,yBAAyB3J,KAAKyD,cAAcQ,SAfjD,IAiBJ+N,QAAS,WACL,MAAIhS,MAAKoE,cACqC,IAAnCpE,KAAKqE,SAAS4N,iBAEdjS,KAAKuE,OAAO2N,OAG3BnR,MAAO,SAAUkD,GACb,GAAI2M,GAAW5Q,KAAK4Q,UACpB,OAAqB,KAAV3M,EAC2B,IAApBjE,KAAKmS,WAA6BnS,KAAKmS,WAAanS,KAAKmS,WAAanS,KAAKD,QAAQgB,OAEjGrC,EAAQuF,EAAMc,SACd6L,EAAStI,cAAcrE,EAAMc,QAC7B6L,EAAS3Q,QAAQ8F,QAEjB6K,EAAS3Q,QAAQyF,OAErB1F,KAAKmS,WAAalO,EAClBjE,KAAKyN,eACLzN,KAAKmF,gBARL,IAUJ4D,SAAU,WACN,MAAO/I,MAAK4N,SAEhBtD,UAAW,WACP,OAAQtK,KAAKgS,YAAchS,KAAK4N,SAEpChN,MAAO,WACHZ,KAAKC,QAAQgG,KAAK,OAAQ,WAE9BpF,aAAc,WACVb,KAAKgQ,mBAAqBjR,EAAMiB,KAAKoS,YAAapS,MAClDzD,EAAE4U,UAAU3C,GAAG,8DAAgEpQ,EAAI4B,KAAKgQ,oBACpFhQ,KAAKD,QAAQwB,cACbvB,KAAKC,QAAQgG,KAAK,WAAY,GAC9BjG,KAAK+P,gBAAkBhR,EAAMiB,KAAKqS,SAAUrS,MAC5CA,KAAKC,QAAQuO,GAAG,UAAYpQ,EAAI4B,KAAK+P,mBAG7CqC,YAAa,WACT,GAAIE,GAAenB,SAASrQ,YAAcqQ,SAASoB,eAAiBpB,SAASqB,kBAC7ExS,MAAK8N,YAAW,GAChB9N,KAAK2C,QAAQ+O,SACRY,IACDtS,KAAKC,QAAQsC,KAAK,iCAAiCgG,YAAYrL,GAAiBkD,SAASnD,GACzF+C,KAAKc,YAAW,KAGxBuR,SAAU,SAAUxN,GAAV,GAEF4N,GAWI9J,CAZR9D,GAAE6N,iBACED,EAAWzS,KAAKC,QAAQsC,KAAK,iCAC7BsC,EAAE8N,UAAY3T,EAAK4T,SACf5S,KAAKsK,YACLtK,KAAK0B,QAEL1B,KAAK2B,OAEFkD,EAAE8N,UAAY3T,EAAK6T,OAAU7S,KAAKiJ,gBAGpB,KAAdpE,EAAE8N,SACLhK,EAAQ3I,KAAKsB,OACjBtB,KAAKsB,MAAMqH,IACJ9D,EAAE8N,UAAY3T,EAAK8T,KAAO9S,KAAKiJ,kBACtCwJ,EAASlK,YAAYrL,GAAiBkD,SAASnD,GAC/C+C,KAAKc,YAAW,KAPhB2R,EAASlK,YAAYtL,GAAkBmD,SAASlD,GAChD8C,KAAKc,YAAW,KASxBiS,OAAQ,aAERC,UAAW,cAGf7U,GAAG8U,OAAOtT,IACZjD,OAAOD,MAAMyW,QACRxW,OAAOD,OACG,kBAAXH,SAAyBA,OAAO6W,IAAM7W,OAAS,SAAU8W,EAAIC,EAAIC,IACtEA,GAAMD", "file": "kendo.mediaplayer.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mediaplayer', [\n        'kendo.slider',\n        'kendo.toolbar',\n        'kendo.dropdownlist',\n        'kendo.tooltip'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mediaplayer',\n        name: 'MediaPlayer',\n        category: 'web',\n        description: '',\n        depends: [\n            'slider',\n            'toolbar',\n            'dropdownlist',\n            'tooltip'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, END = 'end', PAUSE = 'pause', PLAY = 'play', READY = 'ready', TIMECHANGE = 'timeChange', VOLUMECHANGE = 'volumeChange', FULLSCREEN_ENTER = 'k-i-full-screen', FULLSCREEN_EXIT = 'k-i-full-screen-exit', MUTE = 'k-i-volume-off', LOW_VOLUME = 'k-i-volume-down', HIGH_VOLUME = 'k-i-volume-up', VIDEO_QUALITY = 'k-mediaplayer-quality', STATE_PLAY = 'k-i-play', STATE_PAUSE = 'k-i-pause', TITLEBAR = 'k-mediaplayer-titlebar', TITLE = 'k-title', TOOLBARWRAP = 'k-mediaplayer-toolbar-wrap', TOOLBAR = 'k-mediaplayer-toolbar', SLIDER = 'k-mediaplayer-seekbar', VOLUME_SLIDER = 'k-mediaplayer-volume', MEDIA = 'k-mediaplayer-media', OVERLAY = 'k-mediaplayer-overlay', YTPLAYER = 'k-mediaplayer-yt', DOT = '.', ui = kendo.ui, ns = '.kendoMediaPlayer', baseTime = new Date(1970, 0, 1), timeZoneSec = baseTime.getTimezoneOffset() * 60, Widget = kendo.ui.Widget, isArray = $.isArray, timeFormats = {\n                shortTime: 'mm:ss',\n                longTime: 'HH:mm:ss'\n            }, template = kendo.template, proxy = $.proxy, keys = kendo.keys, templates = {\n                htmlPlayer: '<video class=\\'' + MEDIA + '\\'> </video>',\n                titleBar: template('<div class=\\'' + TITLEBAR + '\\' role=\\'heading\\'><span class=\\'' + TITLE + '\\'>Video Title</span></div>'),\n                toolBar: '<div class=\\'' + TOOLBARWRAP + '\\'><div class=\\'' + TOOLBAR + '\\'></div></div>',\n                youtubePlayer: '<div class=\\'' + YTPLAYER + '\\'> </div>',\n                toolBarTime: '<span class=\\'k-mediaplayer-currenttime\\'>00:00:00</span> / <span class=\\'k-mediaplayer-duration\\'>00:00:00</span>',\n                slider: '<input class=\\'' + SLIDER + '\\' value=\\'0\\' title=\\'seekbar\\' />',\n                volumeSlider: '<input class=\\'' + VOLUME_SLIDER + '\\' title=\\'volume\\'/>',\n                qualityDropDown: '<input class=\\'' + VIDEO_QUALITY + '\\' title=\\'video quality\\' />',\n                toolTip: '#= kendo.toString(new Date(value), \\'HH:mm:ss\\') #'\n            };\n        var MediaPlayer = Widget.extend({\n            init: function (element, options) {\n                this.wrapper = $(element);\n                Widget.fn.init.call(this, element, options);\n                this.wrapper.addClass('k-mediaplayer k-widget');\n                options = this.options;\n                this._currentIndex = 0;\n                this._createTitlebar();\n                this._createToolbar();\n                this._createDropDown();\n                this._createSlider();\n                this._createVolumeSlider();\n                this._timers = {};\n                this._aria();\n                this._navigatable();\n                if (options.fullScreen) {\n                    this.fullScreen(true);\n                }\n                if (options.media) {\n                    this.media(this.options.media);\n                }\n                kendo.notify(this);\n            },\n            events: [\n                END,\n                PAUSE,\n                PLAY,\n                READY,\n                TIMECHANGE,\n                VOLUMECHANGE\n            ],\n            options: {\n                name: 'MediaPlayer',\n                autoPlay: false,\n                autoRepeat: false,\n                volume: 100,\n                fullScreen: false,\n                mute: false,\n                navigatable: false,\n                forwardSeek: true,\n                media: null,\n                messages: {\n                    'pause': 'Pause',\n                    'play': 'Play',\n                    'mute': 'Mute',\n                    'unmute': 'Unmute',\n                    'quality': 'Quality',\n                    'fullscreen': 'Full Screen'\n                }\n            },\n            _msToTime: function (ms) {\n                var time = new Date(baseTime.getTime());\n                time.setSeconds(ms);\n                return time;\n            },\n            _timeToSec: function (time) {\n                var curTime = new Date(time).getTime();\n                return curTime / 1000;\n            },\n            _createTitlebar: function () {\n                this._titleBar = this.wrapper.find(DOT + TITLEBAR);\n                if (this._titleBar.length === 0) {\n                    this.wrapper.append(templates.titleBar);\n                    this._titleBar = this.wrapper.find(DOT + TITLEBAR);\n                }\n            },\n            _createSlider: function () {\n                var sliderElement = this.wrapper.find(DOT + SLIDER);\n                if (!this._slider) {\n                    this._sliderDragChangeHandler = proxy(this._sliderDragChange, this);\n                    this._sliderDraggingHandler = proxy(this._sliderDragging, this);\n                    sliderElement = this.wrapper.find(DOT + SLIDER);\n                    this._slider = new ui.Slider(sliderElement[0], {\n                        smallStep: 1000,\n                        tickPlacement: 'none',\n                        showButtons: false,\n                        change: this._sliderDragChangeHandler,\n                        slide: this._sliderDraggingHandler,\n                        tooltip: { template: templates.toolTip },\n                        dragHandleTitle: ''\n                    });\n                }\n            },\n            _createVolumeSlider: function () {\n                var volumeSliderElement = this.wrapper.find(DOT + VOLUME_SLIDER);\n                if (!this._volumeSlider) {\n                    this._volumeDraggingHandler = proxy(this._volumeDragging, this);\n                    this._volumeChangeHandler = proxy(this._volumeChange, this);\n                    volumeSliderElement.width(87);\n                    this._volumeSlider = new ui.Slider(volumeSliderElement[0], {\n                        smallStep: 1,\n                        min: 0,\n                        max: 100,\n                        value: this.options.volume,\n                        slide: this._volumeDraggingHandler,\n                        change: this._volumeChangeHandler,\n                        tickPlacement: 'none',\n                        showButtons: false,\n                        tooltip: { enabled: false },\n                        dragHandleTitle: ''\n                    });\n                }\n            },\n            _resetTime: function () {\n                if (this._youTubeVideo) {\n                    this._ytmedia.seekTo(0, true);\n                } else {\n                    this._media.currentTime = 0;\n                }\n                this._mediaTimeUpdate();\n                $.grep(this._toolBar.options.items, function (e) {\n                    return !!e.template;\n                }).template = templates.toolBarTime;\n            },\n            _currentUrl: function () {\n                var media = this.media();\n                return isArray(media.source) ? media.source[this._currentIndex].url : media.source;\n            },\n            _isYouTubeUrl: function () {\n                return !!this._currentUrl().match('youtube.com/|youtu.be/');\n            },\n            _setPlayerUrl: function () {\n                var oldPlayer = this._youTubeVideo;\n                this.stop();\n                this._youTubeVideo = this._isYouTubeUrl();\n                if (oldPlayer !== this._youTubeVideo) {\n                    this.wrapper.find(DOT + YTPLAYER).toggle();\n                    this.wrapper.find(DOT + MEDIA).toggle();\n                }\n                var initialized = this._media || this._ytmedia;\n                this._initializePlayer();\n                if (initialized) {\n                    this.mute(this.mute());\n                    this.volume(this.volume());\n                }\n                if (!this._youTubeVideo) {\n                    this._videoOverlay.show();\n                    this.wrapper.find(DOT + MEDIA + ' > source').remove();\n                    this.wrapper.find(DOT + MEDIA).attr('src', this._currentUrl());\n                    if (this.options.autoPlay) {\n                        this.play();\n                    }\n                } else if (this._ytmedia) {\n                    if (this._videoOverlay) {\n                        this._videoOverlay.hide();\n                    }\n                    if (this.options.autoPlay) {\n                        this._ytmedia.loadVideoById(this._getMediaId());\n                        this._playStateToggle(true);\n                    } else {\n                        this._ytmedia.cueVideoById(this._getMediaId());\n                        this._playStateToggle(true);\n                    }\n                }\n            },\n            _createToolbar: function () {\n                var toolBarElement = this.wrapper.find(DOT + TOOLBAR);\n                if (toolBarElement.length === 0) {\n                    this._toolbarClickHandler = proxy(this._toolbarClick, this);\n                    this.wrapper.append(templates.toolBar);\n                    toolBarElement = this.wrapper.find(DOT + TOOLBAR);\n                    toolBarElement.width(this.wrapper.find(DOT + MEDIA).width());\n                    this._toolBar = new ui.ToolBar(toolBarElement, {\n                        click: this._toolbarClickHandler,\n                        resizable: false,\n                        items: [\n                            {\n                                type: 'button',\n                                attributes: { 'class': 'k-play-button' },\n                                icon: 'play'\n                            },\n                            {\n                                template: templates.toolBarTime,\n                                attributes: { 'class': 'k-mediaplayer-currenttime-wrap' }\n                            },\n                            {\n                                type: 'separator',\n                                attributes: { 'class': 'k-toolbar-spacer' }\n                            },\n                            {\n                                type: 'button',\n                                attributes: { 'class': 'k-volume-button' },\n                                icon: 'volume-up'\n                            },\n                            {\n                                template: templates.volumeSlider,\n                                attributes: { 'class': 'k-mediaplayer-volume-wrap' }\n                            },\n                            {\n                                template: templates.qualityDropDown,\n                                attributes: { 'class': 'k-mediaplayer-quality-wrap' }\n                            },\n                            {\n                                type: 'button',\n                                attributes: { 'class': 'k-fullscreen-button' },\n                                icon: 'full-screen'\n                            }\n                        ]\n                    });\n                    this._toolBar.wrapper.off('keydown');\n                    toolBarElement.before(templates.slider);\n                    this._volumeButton = toolBarElement.find('.k-volume-button');\n                    this._fullscreenButton = toolBarElement.find('.k-fullscreen-button');\n                    this._volumeButton.attr('title', this.options.mute ? this.options.messages.unmute : this.options.messages.mute);\n                    this._volumeButton.attr('aria-label', this.options.mute ? this.options.messages.unmute : this.options.messages.mute);\n                    this._fullscreenButton.attr('title', this.options.messages.fullscreen);\n                    this._fullscreenButton.attr('aria-label', this.options.messages.fullscreen);\n                    toolBarElement.width('auto');\n                    this._currentTimeElement = toolBarElement.find('.k-mediaplayer-currenttime');\n                    this._durationElement = toolBarElement.find('.k-mediaplayer-duration');\n                    this._playButton = toolBarElement.find('.k-play-button');\n                    this._playButtonSpan = this._playButton.find('.k-i-play');\n                    if (this.options.autoPlay) {\n                        this._playStateToggle(true);\n                    }\n                    $([\n                        this._volumeButton[0],\n                        toolBarElement.find('.k-mediaplayer-volume-wrap')[0],\n                        toolBarElement.find('.k-mediaplayer-quality-wrap')[0],\n                        this._fullscreenButton[0]\n                    ]).wrapAll('<div class=\\'k-align-right\\' />');\n                    toolBarElement.find('.k-button').addClass('k-bare');\n                }\n            },\n            _createDropDown: function () {\n                var dropDownElement = this.wrapper.find(DOT + VIDEO_QUALITY);\n                var media = this.media();\n                if (typeof dropDownElement.data('kendoDropDownList') === 'undefined') {\n                    this._dropDownSelectHandler = proxy(this._dropDownSelect, this);\n                    this._dropDown = new ui.DropDownList(dropDownElement, {\n                        dataTextField: 'quality',\n                        dataValueField: 'url',\n                        popup: {\n                            position: 'bottom',\n                            origin: 'top',\n                            appendTo: this.wrapper\n                        },\n                        animation: {\n                            open: {\n                                effects: 'slideIn:up',\n                                duration: 1\n                            }\n                        },\n                        select: this._dropDownSelectHandler\n                    });\n                    if (media && isArray(media.source)) {\n                        this._dropDown.setDataSource(media.source);\n                        this._dropDown.select(0);\n                    }\n                    this._dropDown.wrapper.addClass('k-button k-bare');\n                    this._dropDown.wrapper.attr('title', this.options.messages.quality).hide();\n                    this._dropDown.wrapper.find('span.k-i-arrow-60-down').removeClass('k-i-arrow-60-down').addClass('k-icon k-i-hd');\n                    this._dropDown.list.addClass('k-quality-list');\n                }\n            },\n            _dropDownSelect: function (e) {\n                if (this._currentIndex !== e.item.index()) {\n                    this._currentIndex = e.item.index();\n                    this._setPlayerUrl();\n                }\n            },\n            _toolbarClick: function (e) {\n                var target = $(e.target).children().first();\n                var isPaused = target.hasClass(STATE_PLAY);\n                if (!this.media()) {\n                    return;\n                }\n                if (target.hasClass(STATE_PLAY) || target.hasClass(STATE_PAUSE)) {\n                    if (isPaused) {\n                        this.play();\n                    } else {\n                        this.pause();\n                    }\n                }\n                if (target.hasClass(FULLSCREEN_ENTER) || target.hasClass(FULLSCREEN_EXIT)) {\n                    if (this._isInFullScreen) {\n                        target.removeClass(FULLSCREEN_EXIT).addClass(FULLSCREEN_ENTER);\n                        this.fullScreen(false);\n                    } else {\n                        target.removeClass(FULLSCREEN_ENTER).addClass(FULLSCREEN_EXIT);\n                        this.fullScreen(true);\n                    }\n                }\n                if (target.hasClass(MUTE) || target.hasClass(LOW_VOLUME) || target.hasClass(HIGH_VOLUME)) {\n                    var muted = this.mute();\n                    this.mute(!muted);\n                }\n            },\n            _sliderDragging: function () {\n                if (!this.media()) {\n                    return;\n                }\n                this._isDragging = true;\n            },\n            _sliderDragChange: function (e) {\n                var that = this;\n                var slider = e.sender;\n                var tzOffset = timeZoneSec * 1000;\n                if (!this.media()) {\n                    return;\n                }\n                that._sliderChangeFired = true;\n                that._isDragging = false;\n                if (!this.options.forwardSeek && slider.value() > this._seekBarLastPosition) {\n                    setTimeout(function () {\n                        slider.value(that._seekBarLastPosition);\n                    }, 1);\n                } else if (this._youTubeVideo) {\n                    that._ytmedia.seekTo(that._timeToSec(e.value - tzOffset));\n                } else {\n                    that._media.currentTime = that._timeToSec(e.value - tzOffset);\n                }\n                that.trigger(TIMECHANGE);\n                that._preventPlay = true;\n            },\n            _changeVolumeButtonImage: function (volume) {\n                var volumeButton = this._volumeButton;\n                var volumeElement = volumeButton.find('span');\n                var cssClass = volumeElement.attr('class');\n                cssClass = cssClass.substring(0, cssClass.lastIndexOf(' '));\n                if (volume === 0) {\n                    volumeElement.attr('class', cssClass + ' ' + MUTE);\n                    volumeButton.attr('title', this.options.messages.unmute);\n                    volumeButton.attr('aria-label', this.options.messages.unmute);\n                } else if (volume > 0 && volume < 51) {\n                    volumeElement.attr('class', cssClass + ' ' + LOW_VOLUME);\n                    volumeButton.attr('title', this.options.messages.mute);\n                    volumeButton.attr('aria-label', this.options.messages.mute);\n                } else {\n                    volumeElement.attr('class', cssClass + ' ' + HIGH_VOLUME);\n                    volumeButton.attr('title', this.options.messages.mute);\n                    volumeButton.attr('aria-label', this.options.messages.mute);\n                }\n            },\n            _volumeDragging: function (e) {\n                if (!this.media()) {\n                    return;\n                }\n                this.volume(e.value);\n                this._changeVolumeButtonImage(e.value);\n                this.trigger(VOLUMECHANGE);\n            },\n            _volumeChange: function (e) {\n                if (!this.media()) {\n                    return;\n                }\n                this.volume(e.value);\n                this._changeVolumeButtonImage(e.value);\n                this.trigger(VOLUMECHANGE);\n            },\n            _mediaTimeUpdate: function () {\n                var currentTime = this._youTubeVideo ? this._ytmedia.getCurrentTime() : this._media.currentTime;\n                currentTime = currentTime ? currentTime : 0;\n                var timeInMs = this._msToTime(currentTime);\n                this._currentTimeElement.text(kendo.toString(timeInMs, this._timeFormat));\n                if (!this._isDragging) {\n                    this._seekBarLastPosition = (currentTime + timeZoneSec) * 1000;\n                    this._slider.value(this._seekBarLastPosition);\n                }\n                return this.isPlaying();\n            },\n            _playStateToggle: function (play) {\n                if (typeof play === 'undefined') {\n                    play = this._playButtonSpan.is(DOT + STATE_PLAY);\n                }\n                if (play) {\n                    this._playButtonSpan.removeClass(STATE_PLAY).addClass(STATE_PAUSE);\n                    this._playButton.attr('title', this.options.messages.pause);\n                    this._playButton.attr('aria-label', this.options.messages.pause);\n                } else {\n                    this._playButtonSpan.removeClass(STATE_PAUSE).addClass(STATE_PLAY);\n                    this._playButton.attr('title', this.options.messages.play);\n                    this._playButton.attr('aria-label', this.options.messages.play);\n                }\n            },\n            _mediaEnded: function () {\n                this._playStateToggle(false);\n                this._currentTimeElement.text(kendo.toString(this._msToTime(0), this._timeFormat));\n                this._slider.value((0 + timeZoneSec) * 1000);\n                this.trigger(END);\n            },\n            _mediaPlay: function () {\n                this.trigger(PLAY);\n            },\n            _mediaReady: function () {\n                this.trigger(READY);\n            },\n            _mediaDurationChange: function () {\n                var durationTime = this._msToTime(this._youTubeVideo ? this._ytmedia.getDuration() : this._media.duration);\n                this._timeFormat = durationTime.getHours() === 0 ? timeFormats.shortTime : timeFormats.longTime;\n                this._durationElement.text(kendo.toString(durationTime, this._timeFormat));\n                this._slider.setOptions({\n                    min: baseTime.getTime(),\n                    max: durationTime.getTime()\n                });\n                if (!this._isFirstRun) {\n                    this._resetTime();\n                    this._isFirstRun = true;\n                }\n            },\n            _createYoutubePlayer: function () {\n                this._mediaTimeUpdateHandler = proxy(this._mediaTimeUpdate, this);\n                this._mediaDurationChangeHandler = proxy(this._mediaDurationChange, this);\n                this.wrapper.prepend(templates.youtubePlayer);\n                this._ytPlayer = this.wrapper.find(DOT + YTPLAYER)[0];\n                $(this._ytPlayer).css({\n                    width: this.wrapper.width(),\n                    height: this.wrapper.height()\n                });\n                if (!window.YT || !window.YT.Player) {\n                    if (!window.onYouTubeIframeAPIReadyRegister) {\n                        window.onYouTubeIframeAPIReadyRegister = [];\n                        $.getScript('https://www.youtube.com/iframe_api');\n                        window.onYouTubeIframeAPIReady = function () {\n                            if (window.onYouTubeIframeAPIReadyRegister) {\n                                for (var i = 0; i < window.onYouTubeIframeAPIReadyRegister.length; i++) {\n                                    window.onYouTubeIframeAPIReadyRegister[i]._youtubeApiReady();\n                                }\n                            }\n                            window.onYouTubeIframeAPIReadyRegister.length = 0;\n                            window.onYouTubeIframeAPIReadyRegister = undefined;\n                        };\n                    }\n                    window.onYouTubeIframeAPIReadyRegister[window.onYouTubeIframeAPIReadyRegister.length] = this;\n                } else {\n                    this._configurePlayer();\n                }\n            },\n            _poll: function (name, callback, interval, context) {\n                var that = this;\n                if (that._timers[name] !== null) {\n                    clearTimeout(that._timers[name]);\n                }\n                that._timers[name] = setTimeout(function (context) {\n                    return function callLater() {\n                        if (callback.call(context)) {\n                            that._timers[name] = setTimeout(callLater, interval);\n                        }\n                    };\n                }(context), interval);\n                return that._timers[name];\n            },\n            _youtubeApiReady: function () {\n                this._configurePlayer();\n            },\n            _configurePlayer: function () {\n                var vars = {\n                    'autoplay': +this.options.autoPlay,\n                    'wmode': 'transparent',\n                    'controls': 0,\n                    'rel': 0,\n                    'showinfo': 0\n                };\n                this._onYouTubePlayerReady = proxy(this._onYouTubePlayerReady, this);\n                window.onYouTubePlayerReady = this._onYouTubePlayerReady;\n                this._onPlayerStateChangeHandler = proxy(this._onPlayerStateChange, this);\n                window.onPlayerStateChange = this._onPlayerStateChange;\n                var player = new window.YT.Player(this.wrapper.find(DOT + YTPLAYER)[0], {\n                    height: this.wrapper.height(),\n                    width: this.wrapper.width(),\n                    videoId: this._getMediaId(),\n                    playerVars: vars,\n                    events: {\n                        'onReady': this._onYouTubePlayerReady,\n                        'onStateChange': this._onPlayerStateChangeHandler\n                    }\n                });\n            },\n            _onYouTubePlayerReady: function (event) {\n                this._ytmedia = event.target;\n                this._ytmedia.getIframe().style.width = '100%';\n                this._ytmedia.getIframe().style.height = '100%';\n                this._youTubeVideo = true;\n                this._mediaDurationChangeHandler();\n                if (this.options.autoPlay) {\n                    this._playStateToggle(true);\n                    this._ytmedia.loadVideoById(this._getMediaId());\n                } else {\n                    this._ytmedia.cueVideoById(this._getMediaId());\n                }\n                if (this.options.mute) {\n                    this.mute(true);\n                }\n                this.trigger(READY);\n            },\n            _updateTitle: function () {\n                this.titlebar().text(this.media().title || this.media().source);\n            },\n            _onPlayerStateChange: function (event) {\n                if (event.data === 0) {\n                    this._slider.value(0);\n                    this._paused = false;\n                    this._playStateToggle(true);\n                    this.trigger(END);\n                    if (this.options.autoRepeat) {\n                        this.play();\n                    }\n                } else if (event.data === 1) {\n                    this._mediaDurationChange();\n                    this._ytmedia.setVolume(this.volume());\n                    if (this._sliderChangeFired) {\n                        this._sliderChangeFired = false;\n                    } else {\n                        this._uiDisplay(false);\n                    }\n                    this.trigger(PLAY);\n                    this._playStateToggle(true);\n                    this._poll('progress', this._mediaTimeUpdate, 500, this);\n                    this._paused = false;\n                } else if (event.data === 2) {\n                    if (!this._paused) {\n                        this._uiDisplay(true);\n                        this._playStateToggle(false);\n                        this.trigger(PAUSE);\n                        this._paused = true;\n                    }\n                }\n            },\n            _getMediaId: function () {\n                var result = this._currentUrl();\n                var regExp = /^.*((youtu.be\\/)|(v\\/)|(\\/u\\/\\w\\/)|(embed\\/)|(watch\\?))\\??v?=?([^#\\&\\?]*).*/;\n                var match = result.match(regExp);\n                if (match && match[7].length === 11) {\n                    result = match[7];\n                }\n                return result;\n            },\n            _mouseClick: function () {\n                if (this.isPaused()) {\n                    this.play();\n                } else {\n                    this.pause();\n                }\n            },\n            _initializePlayer: function () {\n                if (!this._mouseMoveHandler) {\n                    this._mouseMoveHandler = proxy(this._mouseMove, this);\n                    this._mouseInHandler = proxy(this._mouseIn, this);\n                    this._mouseOutHandler = proxy(this._mouseOut, this);\n                    $(this.wrapper).on('mouseenter' + ns, this._mouseInHandler).on('mouseleave' + ns, this._mouseOutHandler).on('mousemove' + ns, this._mouseMoveHandler);\n                }\n                if (!this._ytmedia && this._youTubeVideo) {\n                    this._createYoutubePlayer();\n                } else if (!this._media && !this._youTubeVideo) {\n                    this._createHtmlPlayer();\n                }\n            },\n            _createHtmlPlayer: function () {\n                if (!this._videoOverlay) {\n                    this._mouseClickHanlder = proxy(this._mouseClick, this);\n                    this.wrapper.append('<div class=\\'' + OVERLAY + '\\'></div>');\n                    this._videoOverlay = this.wrapper.find('.k-mediaplayer-overlay').on('click' + ns, this._mouseClickHanlder);\n                }\n                this._mediaTimeUpdateHandler = proxy(this._mediaTimeUpdate, this);\n                this._mediaDurationChangeHandler = proxy(this._mediaDurationChange, this);\n                this._mediaEndedHandler = proxy(this._mediaEnded, this);\n                this._mediaCanPlayHandler = proxy(this._mediaReady, this);\n                this._mediaPlayHandler = proxy(this._mediaPlay, this);\n                this._videoOverlay.after(templates.htmlPlayer);\n                this._media = this.wrapper.find(DOT + MEDIA)[0];\n                $(this._media).css({\n                    width: '100%',\n                    height: '100%'\n                });\n                if (this.options.mute) {\n                    this.mute(true);\n                }\n                this._media.ontimeupdate = this._mediaTimeUpdateHandler;\n                this._media.ondurationchange = this._mediaDurationChangeHandler;\n                this._media.oncanplay = this._mediaCanPlayHandler;\n                this._media.onplay = this._mediaPlayHandler;\n                this._media.onended = this._mediaEndedHandler;\n                this._media.loop = this.options.autoRepeat;\n            },\n            _mouseIn: function () {\n                this._uiDisplay(true);\n            },\n            _mouseOut: function () {\n                this._poll('mouseIdle', this._mouseIdle, 3000, this);\n            },\n            _mouseIdle: function () {\n                this._uiDisplay(false);\n                return false;\n            },\n            _mouseMove: function () {\n                if (!(this._titleBar.is(':animated') || this._toolBar.element.is(':animated') || this._slider.wrapper.is(':animated'))) {\n                    this._uiDisplay(true);\n                }\n                this._poll('mouseIdle', this._mouseIdle, 3000, this);\n            },\n            _uiDisplay: function (state) {\n                var animationSpeed = 'slow';\n                var uiElements = this._titleBar.add(this._toolBar.element.parent());\n                if (state) {\n                    uiElements.fadeIn(animationSpeed);\n                } else {\n                    uiElements.fadeOut(animationSpeed);\n                    if (this.options.navigatable) {\n                        this.wrapper.focus();\n                    }\n                }\n            },\n            setOptions: function (options) {\n                Widget.fn.setOptions.call(this, options);\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                if (!this.isPaused()) {\n                    this.pause();\n                }\n                this.element.off(ns);\n                this.element.find(DOT + OVERLAY).off(ns);\n                this._timers = null;\n                this._mouseMoveHandler = null;\n                this._mouseOutHandler = null;\n                this._mouseInHandler = null;\n                this._mouseClickHanlder = null;\n                this._keyDownHandler = null;\n                this._fullscreenHandler = null;\n                this._toolbarClickHandler = null;\n                this._sliderDragChangeHandler = null;\n                this._sliderDraggingHandler = null;\n                this._volumeDraggingHandler = null;\n                this._volumeChangeHandler = null;\n                this._youtubeApiReadyHandler = null;\n                this._onYouTubePlayerReady = null;\n                this._onPlayerStateChangeHandler = null;\n                this._dropDownSelectHandler = null;\n                if (this._youTubeVideo) {\n                    this._ytmedia.destroy();\n                } else {\n                    this._media.ontimeupdate = this._mediaTimeUpdateHandler = null;\n                    this._media.ondurationchange = this._mediaDurationChangeHandler = null;\n                    this._media.oncanplay = this._mediaCanPlayHandler = null;\n                    this._media.onplay = this._mediaPlayHandler = null;\n                    this._media.onended = this._mediaEndedHandler = null;\n                    this._media.src = '';\n                    this._media.remove();\n                }\n                this._mouseMoveTimer = null;\n                clearTimeout(this._mouseMoveTimer);\n                kendo.destroy(this.element);\n            },\n            seek: function (ms) {\n                if (typeof ms === 'undefined') {\n                    return 1000 * this._youTubeVideo ? this._ytmedia.getCurrentTime() : this._media ? this._media.currentTime : 0;\n                }\n                var seconds = ms / 1000;\n                if (this._youTubeVideo) {\n                    if (seconds + 3 >= this._ytmedia.getDuration() | 0) {\n                        this._ytmedia.seekTo(this._ytmedia.getDuration() - 3 | 0, true);\n                    } else {\n                        this._ytmedia.seekTo(seconds, true);\n                    }\n                } else {\n                    this._media.currentTime = seconds;\n                }\n                return this;\n            },\n            play: function () {\n                if (this._youTubeVideo) {\n                    this._ytmedia.playVideo();\n                } else {\n                    if (kendo.support.mobileOS) {\n                        this._uiDisplay(false);\n                    }\n                    this._media.play();\n                }\n                this._paused = false;\n                this._playStateToggle(true);\n                return this;\n            },\n            stop: function () {\n                if (this._youTubeVideo && this._ytmedia) {\n                    this._ytmedia.stopVideo();\n                } else if (this._media && !this._youTubeVideo) {\n                    if (kendo.support.mobileOS) {\n                        this._uiDisplay(true);\n                    }\n                    this._media.pause();\n                    this._media.currentTime = 0;\n                }\n                this._paused = true;\n                this._playStateToggle(false);\n                return this;\n            },\n            pause: function () {\n                if (this._youTubeVideo) {\n                    this._ytmedia.pauseVideo();\n                } else {\n                    if (kendo.support.mobileOS) {\n                        this._uiDisplay(true);\n                    }\n                    this._media.pause();\n                }\n                this._paused = true;\n                this._playStateToggle(false);\n                this.trigger(PAUSE);\n                return this;\n            },\n            toolbar: function () {\n                return this._toolBar;\n            },\n            dropdown: function () {\n                return this._dropDown;\n            },\n            titlebar: function () {\n                return this._titleBar;\n            },\n            fullScreen: function (enterFullScreen) {\n                if (typeof enterFullScreen === 'undefined') {\n                    return this._isInFullScreen || false;\n                }\n                var element = this.element.get(0);\n                if (enterFullScreen) {\n                    this.element.addClass('k-mediaplayer-fullscreen');\n                    if (element.requestFullscreen) {\n                        element.requestFullscreen();\n                    } else if (element.webkitRequestFullscreen) {\n                        element.webkitRequestFullscreen();\n                    } else if (element.mozRequestFullScreen) {\n                        element.mozRequestFullScreen();\n                    } else if (element.msRequestFullscreen) {\n                        element.msRequestFullscreen();\n                    }\n                    this._isInFullScreen = true;\n                } else {\n                    if (document.cancelFullscreen) {\n                        document.cancelFullscreen();\n                    } else if (document.webkitCancelFullScreen) {\n                        document.webkitCancelFullScreen();\n                    } else if (document.mozCancelFullScreen) {\n                        document.mozCancelFullScreen();\n                    } else if (document.msCancelFullscreen) {\n                        document.msCancelFullscreen();\n                    } else if (document.exitFullscreen) {\n                        document.exitFullscreen();\n                    } else if (document.msExitFullscreen) {\n                        document.msExitFullscreen();\n                    }\n                    this.element.removeClass('k-mediaplayer-fullscreen');\n                    this._isInFullScreen = false;\n                }\n                this._slider.resize();\n            },\n            volume: function (value) {\n                if (typeof value === 'undefined') {\n                    return typeof this._volume !== 'undefined' ? this._volume : this._volume = this.options.volume;\n                }\n                this._volume = value;\n                this.mute(value <= 0);\n                if (this._youTubeVideo) {\n                    this._ytmedia.setVolume(this._volume);\n                } else {\n                    this._media.volume = this._volume / 100;\n                }\n                this._volumeSlider.value(value);\n            },\n            mute: function (muted) {\n                var currentState = this._youTubeVideo ? this._ytmedia && this._ytmedia.isMuted() : this._media && this._media.muted;\n                if (typeof muted === 'undefined' || muted === currentState) {\n                    return currentState;\n                }\n                if (this._youTubeVideo) {\n                    if (muted) {\n                        this._ytmedia.mute();\n                    } else {\n                        this._ytmedia.unMute();\n                    }\n                } else {\n                    this._media.muted = muted;\n                }\n                if (muted) {\n                    this._volumeSlider.value(0);\n                } else {\n                    this._volumeSlider.value(this._media && this._media.volume * 100 || this._ytmedia && this._ytmedia.getVolume());\n                }\n                this.trigger(VOLUMECHANGE);\n                this._changeVolumeButtonImage(this._volumeSlider.value());\n            },\n            isEnded: function () {\n                if (this._youTubeVideo) {\n                    return this._ytmedia.getPlayerState() === 0;\n                } else {\n                    return this._media.ended;\n                }\n            },\n            media: function (value) {\n                var dropdown = this.dropdown();\n                if (typeof value === 'undefined') {\n                    return typeof this._mediaData !== 'undefined' ? this._mediaData : this._mediaData = this.options.media;\n                }\n                if (isArray(value.source)) {\n                    dropdown.setDataSource(value.source);\n                    dropdown.wrapper.show();\n                } else {\n                    dropdown.wrapper.hide();\n                }\n                this._mediaData = value;\n                this._updateTitle();\n                this._setPlayerUrl();\n            },\n            isPaused: function () {\n                return this._paused;\n            },\n            isPlaying: function () {\n                return !this.isEnded() && !this._paused;\n            },\n            _aria: function () {\n                this.wrapper.attr('role', 'region');\n            },\n            _navigatable: function () {\n                this._fullscreenHandler = proxy(this._fullscreen, this);\n                $(document).on('webkitfullscreenchange mozfullscreenchange fullscreenchange' + ns, this._fullscreenHandler);\n                if (this.options.navigatable) {\n                    this.wrapper.attr('tabIndex', 0);\n                    this._keyDownHandler = proxy(this._keyDown, this);\n                    this.wrapper.on('keydown' + ns, this._keyDownHandler);\n                }\n            },\n            _fullscreen: function () {\n                var isFullScreen = document.fullScreen || document.mozFullScreen || document.webkitIsFullScreen;\n                this._uiDisplay(true);\n                this._slider.resize();\n                if (!isFullScreen) {\n                    this.wrapper.find('span[class*=\"k-i-fullscreen\"]').removeClass(FULLSCREEN_EXIT).addClass(FULLSCREEN_ENTER);\n                    this.fullScreen(false);\n                }\n            },\n            _keyDown: function (e) {\n                e.preventDefault();\n                var fsButton = this.wrapper.find('span[class*=\"k-i-fullscreen\"]');\n                if (e.keyCode === keys.SPACEBAR) {\n                    if (this.isPlaying()) {\n                        this.pause();\n                    } else {\n                        this.play();\n                    }\n                } else if (e.keyCode === keys.ENTER && !this._isInFullScreen) {\n                    fsButton.removeClass(FULLSCREEN_ENTER).addClass(FULLSCREEN_EXIT);\n                    this.fullScreen(true);\n                } else if (e.keyCode === 77) {\n                    var muted = this.mute();\n                    this.mute(!muted);\n                } else if (e.keyCode === keys.ESC && this._isInFullScreen) {\n                    fsButton.removeClass(FULLSCREEN_EXIT).addClass(FULLSCREEN_ENTER);\n                    this.fullScreen(false);\n                }\n            },\n            _error: function () {\n            },\n            _progress: function () {\n            }\n        });\n        ui.plugin(MediaPlayer);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define === 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}