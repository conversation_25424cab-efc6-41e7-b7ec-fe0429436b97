{"version": 3, "sources": ["kendo.columnmenu.js"], "names": ["f", "define", "$", "undefined", "trim", "text", "replace", "toHash", "arr", "key", "idx", "len", "current", "result", "length", "leafColumns", "columns", "concat", "push", "attrEquals", "attrName", "attrValue", "kendo", "attr", "insertElementAt", "index", "element", "container", "insertAfter", "children", "eq", "prepend", "window", "ui", "proxy", "extend", "grep", "map", "inArray", "ACTIVE", "ASC", "DESC", "CHANGE", "INIT", "OPEN", "SELECT", "POPUP", "FILTERMENU", "MENU", "NS", "Widget", "ColumnMenu", "init", "options", "link", "that", "this", "fn", "call", "owner", "dataSource", "field", "title", "find", "addClass", "messages", "settings", "on", "_click", "wrapper", "_refresh<PERSON><PERSON><PERSON>", "refresh", "bind", "_init", "pane", "_isMobile", "_createMobileMenu", "_createMenu", "_muteAngularRebind", "_angularItems", "_sort", "_columns", "_filter", "_lockColumns", "trigger", "events", "name", "sortAscending", "sortDescending", "filter", "column", "columnVisibility", "clear", "cancel", "done", "lock", "unlock", "sortable", "filterable", "animations", "left", "html", "template", "uid", "guid", "ns", "_ownerColumns", "showColumns", "lockedColumns", "popup", "anchor", "open", "_open", "activate", "_activate", "close", "closeCallback", "data", "menu", "orientation", "closeOnClick", "_updateMenuItems", "viewElement", "mobileTemplate", "view", "append", "state", "MobileMenu", "columnMenu", "e", "stopPropagation", "preventDefault", "_applyChanges", "_cancelChanges", "_updateLockedColumns", "initialSort", "action", "angular", "items", "closest", "col", "_originalObject", "elements", "destroy", "filterMenu", "unbind", "_updateColumnsMenuHandler", "_updateColumnsLockedStateHandler", "off", "purge", "is", "navigate", "toggle", "_setMenuItemsVisibility", "_reorderMenuItems", "_eachRenderedMenuItem", "renderedListElement", "matchesMedia", "hide", "show", "renderedList", "callback", "duplcateColumns", "duplicateColumnIndex", "fieldValue", "currentColumn", "i", "first", "filterByTitle", "containerElement", "tagName", "filterCallback", "matchTitle", "titleAttr", "duplicateColumns", "JSON", "stringify", "next", "not", "each", "keyCode", "keys", "ESC", "focus", "menuColumns", "original<PERSON>ield", "hidden", "locked", "dir", "item", "hasClass", "parent", "removeClass", "_sortDataSource", "compare", "sort", "allowUnsort", "mode", "splice", "_updateColumnsMenu", "_updateColumnsLockedState", "input", "showColumn", "hideColumn", "checked", "checkboxes", "switchWidget", "fieldAttr", "lockedAttr", "columnsInMenu", "visibleFields", "visibleDataFields", "lockedCount", "nonLockedCount", "columnsNotInMenu", "hiddenColumnsNotInMenu", "prop", "enable", "check", "widget", "multi", "checkSource", "appendToElement", "values", "change", "lockColumn", "unlockColumn", "lockItem", "unlockItem", "descriptor", "_filterExist", "filters", "found", "_createCheckBoxes", "target", "currentTarget", "_updateSelectedItems", "el", "otherItem", "otherItemId", "id", "force", "selectedItems", "hasOwnProperty", "kendoSwitch", "unchecked", "sender", "_destroyCheckBoxes", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,oBACH,cACA,mBACA,cACDD,IACL,WA0yBE,MA9xBC,UAAUE,EAAGC,GAEV,QAASC,GAAKC,GACV,MAAOH,GAAEE,KAAKC,GAAMC,QAAQ,WAAY,IAE5C,QAASC,GAAOC,EAAKC,GAArB,GAEQC,GAAKC,EAAKC,EADVC,IAEJ,KAAKH,EAAM,EAAGC,EAAMH,EAAIM,OAAQJ,EAAMC,EAAKD,IACvCE,EAAUJ,EAAIE,GACdG,EAAOD,EAAQH,IAAQG,CAE3B,OAAOC,GAEX,QAASE,GAAYC,GAArB,GAEaN,GADLG,IACJ,KAASH,EAAM,EAAGA,EAAMM,EAAQF,OAAQJ,IAC/BM,EAAQN,GAAKM,QAIlBH,EAASA,EAAOI,OAAOF,EAAYC,EAAQN,GAAKM,UAH5CH,EAAOK,KAAKF,EAAQN,GAK5B,OAAOG,GAEX,QAASM,GAAWC,EAAUC,GAC1B,MAAO,IAAMC,EAAMC,KAAKH,GAAY,MAASC,GAAa,IAAIf,QAAQ,KAAM,KAAO,KAEvF,QAASkB,GAAgBC,EAAOC,EAASC,GACjCF,EAAQ,EACRC,EAAQE,YAAYD,EAAUE,WAAWC,GAAGL,EAAQ,IAEpDE,EAAUI,QAAQL,GAhC7B,GACOJ,GAAQU,OAAOV,MAAOW,EAAKX,EAAMW,GAAIC,EAAQhC,EAAEgC,MAAOC,EAASjC,EAAEiC,OAAQC,EAAOlC,EAAEkC,KAAMC,EAAMnC,EAAEmC,IAAKC,EAAUpC,EAAEoC,QAASC,EAAS,mBAAoBC,EAAM,MAAOC,EAAO,OAAQC,EAAS,SAAUC,EAAO,OAAQC,EAAO,OAAQC,EAAS,SAAUC,EAAQ,aAAcC,EAAa,kBAAmBC,EAAO,YAAaC,EAAK,mBAAoBC,EAASjB,EAAGiB,OAkCtWC,EAAaD,EAAOf,QACpBiB,KAAM,SAAU1B,EAAS2B,GACrB,GAAiBC,GAAbC,EAAOC,IACXN,GAAOO,GAAGL,KAAKM,KAAKH,EAAM7B,EAAS2B,GACnC3B,EAAU6B,EAAK7B,QACf2B,EAAUE,EAAKF,QACfE,EAAKI,MAAQN,EAAQM,MACrBJ,EAAKK,WAAaP,EAAQO,WAC1BL,EAAKM,MAAQnC,EAAQH,KAAKD,EAAMC,KAAK,UACrCgC,EAAKO,MAAQpC,EAAQH,KAAKD,EAAMC,KAAK,UACrC+B,EAAO5B,EAAQqC,KAAK,yBACfT,EAAK,KACNA,EAAO5B,EAAQsC,SAAS,eAAejC,QAAQ,mDAAqDsB,EAAQY,SAASC,SAAW,iBAAmBb,EAAQY,SAASC,SAAW,wDAAwDH,KAAK,0BAEhPR,EAAKD,KAAOA,EAAK/B,KAAK,eAAgB4C,GAAG,QAAUlB,EAAIf,EAAMqB,EAAKa,OAAQb,IAC1EA,EAAKc,QAAUnE,EAAE,gCACjBqD,EAAKe,gBAAkBpC,EAAMqB,EAAKgB,QAAShB,GAC3CA,EAAKK,WAAWY,KAAK9B,EAAQa,EAAKe,kBAEtCG,MAAO,WACH,GAAIlB,GAAOC,IACXD,GAAKmB,KAAOnB,EAAKF,QAAQqB,KACrBnB,EAAKmB,OACLnB,EAAKoB,WAAY,GAEjBpB,EAAKoB,UACLpB,EAAKqB,oBAELrB,EAAKsB,cAETtB,EAAKI,MAAMmB,mBAAmB,WAC1BvB,EAAKwB,cAAc,aAEvBxB,EAAKyB,QACLzB,EAAK0B,WACL1B,EAAK2B,UACL3B,EAAK4B,eACL5B,EAAK6B,QAAQzC,GACTkB,MAAON,EAAKM,MACZlC,UAAW4B,EAAKc,WAGxBgB,QACI1C,EACAC,EACA,OACA,aAEJS,SACIiC,KAAM,aACNrB,UACIsB,cAAe,iBACfC,eAAgB,kBAChBC,OAAQ,SACRC,OAAQ,SACR1E,QAAS,UACT2E,iBAAkB,oBAClBC,MAAO,QACPC,OAAQ,SACRC,KAAM,OACN5B,SAAU,uBACV6B,KAAM,OACNC,OAAQ,UAEZP,OAAQ,GACRzE,SAAS,EACTiF,UAAU,EACVC,YAAY,EACZC,YAAcC,KAAM,UAExBvB,YAAa,WACT,GAAItB,GAAOC,KAAMH,EAAUE,EAAKF,OAChCE,GAAKc,QAAQgC,KAAK/E,EAAMgF,SAASA,IAC7BC,IAAKjF,EAAMkF,OACXC,GAAInF,EAAMmF,GACVxC,SAAUZ,EAAQY,SAClBgC,SAAU5C,EAAQ4C,SAClBC,WAAY7C,EAAQ6C,WACpBlF,QAASuC,EAAKmD,gBACdC,YAAatD,EAAQrC,QACrB4F,cAAevD,EAAQuD,iBAE3BrD,EAAKsD,MAAQtD,EAAKc,QAAQvB,IACtBgE,OAAQvD,EAAKD,KACbyD,KAAM7E,EAAMqB,EAAKyD,MAAOzD,GACxB0D,SAAU/E,EAAMqB,EAAK2D,UAAW3D,GAChC4D,MAAO,WACC5D,EAAKF,QAAQ+D,eACb7D,EAAKF,QAAQ+D,cAAc7D,EAAK7B,YAGzC2F,KAAKvE,GACRS,EAAK+D,KAAO/D,EAAKc,QAAQxC,WAAWmB,IAChCuE,YAAa,WACbC,cAAc,EACdT,KAAM,WACFxD,EAAKkE,sBAEVJ,KAAKrE,IAEZ4B,kBAAmB,WAAA,GAuBX8C,GAtBAnE,EAAOC,KAAMH,EAAUE,EAAKF,QAC5BgD,EAAO/E,EAAMgF,SAASqB,IACtBlB,GAAInF,EAAMmF,GACV5C,MAAON,EAAKM,MACZC,MAAOP,EAAKO,OAASP,EAAKM,MAC1BI,SAAUZ,EAAQY,SAClBgC,SAAU5C,EAAQ4C,SAClBC,WAAY7C,EAAQ6C,WACpBlF,QAASuC,EAAKmD,gBACdC,YAAatD,EAAQrC,QACrB4F,cAAevD,EAAQuD,eAE3BrD,GAAKqE,KAAOrE,EAAKmB,KAAKmD,OAAOxB,GAC7B9C,EAAKqE,KAAKE,OAAU9G,YACpBuC,EAAKc,QAAUd,EAAKqE,KAAKlG,QAAQqC,KAAK,kBACtCR,EAAK+D,KAAO,GAAIS,GAAWxE,EAAKc,QAAQxC,YACpC6C,KAAMnB,EAAKmB,KACXsD,WAAYzE,IAEhBA,EAAK+D,KAAK5F,QAAQyC,GAAG,gBAAkBlB,EAAI,SAAUgF,GACjDA,EAAEC,oBAEFR,EAAcnE,EAAKqE,KAAKvD,SAAWd,EAAKqE,KAAKvD,QAAQ,GAAKd,EAAKqE,KAAKvD,QAAUd,EAAKqE,KAAKlG,QAC5FgG,EAAYvD,GAAG,QAAS,iBAAkB,SAAU8D,GAChDA,EAAEE,iBACF5E,EAAK+D,KAAKc,gBACV7E,EAAK+D,KAAKe,gBAAe,GACzB9E,EAAK4D,UAETO,EAAYvD,GAAG,QAAS,mBAAoB,SAAU8D,GAClDA,EAAEE,iBACF5E,EAAK+D,KAAKe,gBAAe,GACzB9E,EAAK4D,UAET5D,EAAKqE,KAAKpD,KAAK,OAAQ,WACnB,GAAIoD,GAAOrE,EAAKqE,OAAU5G,WACtBuC,GAAKF,QAAQuD,eACbrD,EAAK+E,uBAELV,EAAKlG,QAAQqC,KAAK,gCAAgCjD,OAClD8G,EAAKE,MAAMS,YAAc,MAClBX,EAAKlG,QAAQqC,KAAK,iCAAiCjD,SAC1D8G,EAAKE,MAAMS,YAAc,WAIrCxD,cAAe,SAAUyD,GACrB,GAAIjF,GAAOC,IACXD,GAAKkF,QAAQD,EAAQ,WAAA,GACbE,GAAQnF,EAAKc,QAAQN,KAAK,yBAA2BzC,EAAMC,KAAK,SAAW,KAAKc,IAAI,WACpF,MAAOnC,GAAEsD,MAAMmF,QAAQ,QAEvBtB,EAAOhF,EAAIkB,EAAKmD,gBAAiB,SAAUkC,GAC3C,OAASlD,OAAQkD,EAAIC,kBAEzB,QACIC,SAAUJ,EACVrB,KAAMA,MAIlB0B,QAAS,WACL,GAAIxF,GAAOC,IACXD,GAAKwB,cAAc,WACnB7B,EAAOO,GAAGsF,QAAQrF,KAAKH,GACnBA,EAAKyF,YACLzF,EAAKyF,WAAWD,UAEhBxF,EAAKe,iBACLf,EAAKK,WAAWqF,OAAOvG,EAAQa,EAAKe,iBAEpCf,EAAKF,QAAQrC,SAAWuC,EAAKI,QACzBJ,EAAK2F,4BACL3F,EAAKI,MAAMsF,OAAO,aAAc1F,EAAK2F,2BACrC3F,EAAKI,MAAMsF,OAAO,aAAc1F,EAAK2F,4BAErC3F,EAAK4F,mCACL5F,EAAKI,MAAMsF,OAAO,aAAc1F,EAAK4F,kCACrC5F,EAAKI,MAAMsF,OAAO,eAAgB1F,EAAK4F,oCAG3C5F,EAAK+D,OACL/D,EAAK+D,KAAK5F,QAAQ0H,IAAInG,GACtBM,EAAK+D,KAAKyB,WAEdxF,EAAKc,QAAQ+E,IAAInG,GACbM,EAAKsD,OACLtD,EAAKsD,MAAMkC,UAEXxF,EAAKqE,MACLrE,EAAKqE,KAAKyB,QAEd9F,EAAKD,KAAK8F,IAAInG,GACdM,EAAKI,MAAQ,KACbJ,EAAKc,QAAU,KACfd,EAAK7B,QAAU,MAEnByF,MAAO,WACH3D,KAAK8D,KAAKH,QACN3D,KAAKqD,QACLrD,KAAKqD,MAAMM,QACX3D,KAAKqD,MAAMnF,QAAQ0H,IAAI,UAAYnG,KAG3CmB,OAAQ,SAAU6D,GAAV,GAIA5E,GAHAE,EAAOC,IACXyE,GAAEE,iBACFF,EAAEC,kBACE7E,EAAUG,KAAKH,QACfA,EAAQoC,QAAUjC,KAAK9B,QAAQ4H,IAAIjG,EAAQoC,UAG1CjC,KAAKqD,OAAUrD,KAAKkB,KAGrBnB,EAAKkE,mBAFLjE,KAAKiB,QAILjB,KAAKmB,UACLnB,KAAKkB,KAAK6E,SAAS/F,KAAKoE,KAAMpE,KAAKH,QAAQ8C,WAAWC,MAEtD5C,KAAKqD,MAAM2C,WAGnB/B,iBAAkB,WACd,GAAIlE,GAAOC,IACXD,GAAKkG,0BACLlG,EAAKmG,qBAETD,wBAAyB,WACrB,GAAIlG,GAAOC,IACXD,GAAKoG,sBAAsB,SAAUlI,EAAOiE,EAAQkE,GAC5ClE,EAAOmE,gBAAiB,EACxBD,EAAoBE,OAEpBF,EAAoBG,UAIhCL,kBAAmB,WACf,GAAInG,GAAOC,IACXD,GAAKoG,sBAAsB,SAAUlI,EAAOiE,EAAQkE,EAAqBI,GACjEJ,EAAoB,IAAMA,EAAoBnI,UAAYA,GAC1DD,EAAgBC,EAAOmI,EAAqBI,MAIxDL,sBAAuB,SAAUM,GAAV,GAEfL,GACAM,EACAC,EACAC,EACAC,EA+BKC,EApCL/G,EAAOC,KAMPxC,EAAUoB,EAAKrB,EAAYwC,EAAKI,MAAM3C,SAAU,SAAU4H,GAC1D,GAAI/H,IAAS,EAAMiD,EAAQ1D,EAAKwI,EAAI9E,OAAS,GAI7C,OAHI8E,GAAItB,QAAS,IAAUsB,EAAI/E,OAAUC,EAAMhD,UAC3CD,GAAS,GAENA,IACRwB,IAAI,SAAUuG,GACb,OACI/E,MAAO+E,EAAI/E,MACXC,MAAO8E,EAAI9E,MACX+F,aAAcjB,EAAIiB,gBAGtBG,EAAezG,EAAKoB,WAAapB,EAAKqE,KAAO1H,EAAEqD,EAAKqE,KAAKlG,SAASqC,KAAK,mBAAmBlC,SAAS,MAAQ3B,EAAEqD,EAAKc,SAASN,KAAK,iBAAiBwG,QACjJC,EAAgB,SAAUC,EAAkBC,EAASjJ,GACrD,MAAOgJ,GAAiB1G,KAAK2G,GAASjF,OAAO,WACzC,MAAOkF,GAAe3J,EAAQS,GAAQvB,EAAEsD,MAAMnD,WAGlDsK,EAAiB,SAAUjF,EAAQrF,GACnC,MAAOuK,GAAWlF,EAAQrF,IAE1BuK,EAAa,SAAUlF,EAAQmF,GAC/B,MAAOnF,GAAO5B,MAAQ+G,IAAcnF,EAAO5B,MAAQ+G,IAAcnF,EAAO7B,OAExEiH,EAAmB,SAAUrJ,GAC7B,MAAOW,GAAKpB,EAAS,SAAU4H,GAC3B,MAAOmC,MAAKC,UAAUhK,EAAQS,KAAWsJ,KAAKC,UAAUpC,KAGhE,KAAS0B,EAAI,EAAGA,EAAItJ,EAAQF,OAAQwJ,IAChCD,EAAgBrJ,EAAQsJ,GACxBJ,EAAkBY,EAAiBR,GACnCH,EAAuBjK,EAAEoC,QAAQ+H,EAAeH,GAChDN,EAAsBY,EAAcR,EAAc,OAAQM,GAC1DV,EAAsBpG,KAAKmB,UAAYiF,EAAoBqB,OAASrB,EACpEQ,EAAaC,EAAcxG,MAAQwG,EAAcxG,MAAQwG,EAAcvG,MACvE8F,EAAsBA,EAAoB7F,KAAK5C,EAAW,QAASiJ,IAAazB,QAAQ,MAAM7G,GAAGqI,GACjGF,EAASK,EAAGD,EAAeT,EAAqBI,IAGxDhD,MAAO,WACH,GAAIzD,GAAOC,IACXtD,GAAE,kBAAkBgL,IAAI3H,EAAKc,SAAS8G,KAAK,WACvCjL,EAAEsD,MAAM6D,KAAKvE,GAAOqE,UAExB5D,EAAKsD,MAAMnF,QAAQyC,GAAG,UAAYlB,EAAI,SAAUgF,GACxCA,EAAEmD,SAAW9J,EAAM+J,KAAKC,KACxB/H,EAAK4D,UAGT5D,EAAKF,QAAQuD,eACbrD,EAAK+E,wBAGbpB,UAAW,WACP1D,KAAK8D,KAAK5F,QAAQ6J,QAClB/H,KAAK4B,QAAQxC,GACTiB,MAAOL,KAAKK,MACZlC,UAAW6B,KAAKa,WAGxBqC,cAAe,WACX,GAAI1F,GAAUD,EAAYyC,KAAKG,MAAM3C,SAAUwK,EAAcpJ,EAAKpB,EAAS,SAAU4H,GAC7E,GAAI/H,IAAS,EAAMiD,EAAQ1D,EAAKwI,EAAI9E,OAAS,GAI7C,OAHI8E,GAAItB,QAAS,IAAUsB,EAAI/E,OAAUC,EAAMhD,UAC3CD,GAAS,GAENA,GAEf,OAAOwB,GAAImJ,EAAa,SAAU5C,GAC9B,OACI6C,cAAe7C,EAAI/E,MACnBA,MAAO+E,EAAI/E,OAAS+E,EAAI9E,MACxBA,MAAO8E,EAAI9E,OAAS8E,EAAI/E,MACxB6H,OAAQ9C,EAAI8C,OACZ7B,aAAcjB,EAAIiB,aAClBpI,MAAOa,EAAQsG,EAAK5H,GACpB2K,SAAU/C,EAAI+C,OACd9C,gBAAiBD,MAI7B5D,MAAO,WACH,GAAIzB,GAAOC,IACPD,GAAKF,QAAQ4C,WACb1C,EAAKgB,UACLhB,EAAK+D,KAAK9C,KAAK3B,EAAQ,SAAUoF,GAC7B,GAAsB2D,GAAlBC,EAAO3L,EAAE+H,EAAE4D,KACXA,GAAKC,SAAS,cACdF,EAAMpJ,EACCqJ,EAAKC,SAAS,iBACrBF,EAAMnJ,GAELmJ,IAGLC,EAAKE,SAAShI,KAAK,YAAc6H,GAAOpJ,EAAMC,EAAOD,IAAMwJ,YAAYzJ,GACvEgB,EAAK0I,gBAAgBJ,EAAMD,GACtBrI,EAAKoB,WACNpB,EAAK4D,aAKrB8E,gBAAiB,SAAUJ,EAAMD,GAAhB,GACwIlL,GAAKI,EAAtJyC,EAAOC,KAAMyC,EAAW1C,EAAKF,QAAQ4C,SAAUiG,EAA+B,OAArBjG,EAASiG,QAAmB/L,EAAY8F,EAASiG,QAAStI,EAAaL,EAAKK,WAAyBuI,EAAOvI,EAAWuI,WAChLH,EAAcH,EAAKC,SAASvJ,IAAW0D,GAAYA,EAASmG,eAAgB,CAEhF,IADAR,EAAOI,EAAoB7L,EAANyL,GACjBrI,EAAK6B,QAAQ,QACT+G,MACItI,MAAON,EAAKM,MACZ+H,IAAKA,EACLM,QAASA,KAJrB,CAcA,GALIF,EACAH,EAAKG,YAAYzJ,GAEjBsJ,EAAK7H,SAASzB,GAEI,aAAlB0D,EAASoG,KAAqB,CAC9B,IAAK3L,EAAM,EAAGI,EAASqL,EAAKrL,OAAQJ,EAAMI,EAAQJ,IAC9C,GAAIyL,EAAKzL,GAAKmD,QAAUN,EAAKM,MAAO,CAChCsI,EAAKG,OAAO5L,EAAK,EACjB,OAGRyL,EAAKjL,MACD2C,MAAON,EAAKM,MACZ+H,IAAKA,EACLM,QAASA,QAGbC,KACQtI,MAAON,EAAKM,MACZ+H,IAAKA,EACLM,QAASA,GAGrBtI,GAAWuI,KAAKA,KAEpBlH,SAAU,WACN,GAAI1B,GAAOC,IACPD,GAAKF,QAAQrC,UACbuC,EAAKgJ,qBACLhJ,EAAK2F,0BAA4BhH,EAAMqB,EAAKgJ,mBAAoBhJ,GAChEA,EAAKI,MAAMa,MACP,aACA,cACDjB,EAAK2F,2BACR3F,EAAK4F,iCAAmCjH,EAAMqB,EAAKiJ,0BAA2BjJ,GAC9EA,EAAKI,MAAMa,MACP,eACA,cACDjB,EAAK4F,kCACR5F,EAAK+D,KAAK9C,KAAK3B,EAAQ,SAAUoF,GAC7B,GAAsBwE,GAAO/G,EAAzBmG,EAAO3L,EAAE+H,EAAE4D,MAAsB7K,EAAUoB,EAAKrB,EAAYwC,EAAKI,MAAM3C,SAAU,SAAU4H,GACvF,GAAI/H,IAAS,EAAMiD,EAAQ1D,EAAKwI,EAAI9E,OAAS,GAI7C,OAHI8E,GAAItB,QAAS,IAAUsB,EAAI/E,OAAUC,EAAMhD,UAC3CD,GAAS,GAENA,GAEX0C,GAAKoB,WACLsD,EAAEE,iBAED0D,EAAKE,SAASpD,QAAQ,qBAAqB,KAGhD8D,EAAQZ,EAAK9H,KAAK,aACd0I,EAAMlL,KAAK,cAGfmE,EAAS1E,EAAQ6K,EAAKpK,SAClBiE,EAAOgG,UAAW,EAClBnI,EAAKI,MAAM+I,WAAWhH,GAEtBnC,EAAKI,MAAMgJ,WAAWjH,SAKtC6G,mBAAoB,WAAA,GACZ7L,GAAKI,EAAQF,EAASgM,EAASjB,EAqB/BkB,EACAC,EArBAC,EAAYzL,EAAMC,KAAK,SAAUyL,EAAa1L,EAAMC,KAAK,UAAW0L,EAAgB7K,EAAKrB,EAAYyC,KAAKG,MAAM3C,SAAU,SAAU4H,GAChI,GAAI/H,IAAS,EAAMiD,EAAQ1D,EAAKwI,EAAI9E,OAAS,GAI7C,OAHI8E,GAAItB,QAAS,IAAUsB,EAAI/E,OAAUC,EAAMhD,UAC3CD,GAAS,GAENA,IACPqM,EAAgB9K,EAAKoB,KAAKkD,gBAAiB,SAAU7C,GACrD,OAAQA,EAAM6H,QAAU7H,EAAMgG,gBAAiB,IAC/CsD,EAAoB/K,EAAK8K,EAAe,SAAUrJ,GAClD,MAAOA,GAAM4H,gBACb2B,EAAchL,EAAK+K,EAAmB,SAAUvE,GAChD,MAAOA,GAAI+C,UAAW,IACvB7K,OAAQuM,EAAiBjL,EAAK+K,EAAmB,SAAUvE,GAC1D,MAAOA,GAAI+C,UAAW,IACvB7K,OAAQwM,EAAmBlL,EAAKoB,KAAKG,MAAM3C,QAAS,SAAU4H,GAC7D,MAAOA,GAAItB,QAAS,IACpBiG,EAAyBnL,EAAKkL,EAAkB,SAAU1E,GAC1D,MAAOA,GAAI8C,QAKnB,KAHAlI,KAAKa,QAAQN,KAAK,6BAA+BxC,KAAK,gBAAgB,GAClEsL,EAAarJ,KAAKa,QAAQN,KAAK,yBAA2BgJ,EAAY,KAAKS,KAAK,YAAY,GAAOA,KAAK,WAAW,GAElH9M,EAAM,EAAGI,EAAS+L,EAAW/L,OAAQJ,EAAMI,EAAQJ,IACpDE,EAAUiM,EAAW/K,GAAGpB,GACxBiL,EAAsC,SAA7B/K,EAAQW,KAAKyL,GACtBJ,GAAU,EACVE,EAAelM,EAAQyG,KAAK,eAC5BuF,GAAWK,EAAcvM,GAAKgL,QAAUuB,EAAcvM,GAAKmJ,gBAAiB,EAC5EjJ,EAAQ4M,KAAK,UAAWZ,GACpBE,IACAA,EAAaW,QAAO,GACpBX,EAAaY,MAAMd,IAEvBhM,EAAQ+H,QAAQ,6BAA+BpH,KAAK,eAAgBqL,GAChEA,IACmB,GAAfQ,GAAoBzB,IACpB/K,EAAQ4M,KAAK,YAAY,GACrBV,GACAA,EAAaW,QAAO,IAGK,IAA5BH,EAAiBxM,QAAgBwM,EAAiBxM,SAAWyM,EAAuBzM,QAA6B,GAAlBuM,GAAwB1B,IACxH/K,EAAQ4M,KAAK,YAAY,GACrBV,GACAA,EAAaW,QAAO,MAMxCjB,0BAA2B,WAAA,GACnB9L,GAAKI,EAAQF,EAAS8E,EACtBqH,EAAYzL,EAAMC,KAAK,SACvByL,EAAa1L,EAAMC,KAAK,UACxBP,EAAUT,EAAOiD,KAAKkD,gBAAiB,SACvCmG,EAAarJ,KAAKa,QAAQN,KAAK,uCACnC,KAAKrD,EAAM,EAAGI,EAAS+L,EAAW/L,OAAQJ,EAAMI,EAAQJ,IACpDE,EAAUiM,EAAW/K,GAAGpB,GACxBgF,EAAS1E,EAAQJ,EAAQW,KAAKwL,IAC1BrH,GACA9E,EAAQW,KAAKyL,EAAYtH,EAAOiG,OAGxCnI,MAAK+I,sBAETrH,QAAS,WACL,GAAI3B,GAAOC,KAAMmK,EAAS5K,EAAYM,EAAUE,EAAKF,OACjDA,GAAQ6C,cAAe,IACnB7C,EAAQ6C,WAAW0H,QACnBD,EAAS,wBACLtK,EAAQ6C,WAAWtC,aACnBP,EAAQ6C,WAAW2H,YAAcxK,EAAQ6C,WAAWtC,iBAC7CP,GAAQ6C,WAAWtC,aAGlCL,EAAKyF,WAAazF,EAAKc,QAAQN,KAAK,iBAAiB4J,GAAQxL,GAAO,MAChE2L,iBAAiB,EACjBlK,WAAYP,EAAQO,WACpBmK,OAAQ1K,EAAQ0K,OAChBlK,MAAON,EAAKM,MACZC,MAAOP,EAAKO,MACZkK,OAAQ,SAAU/F,GACV1E,EAAK6B,QAAQ,aACTK,OAAQwC,EAAExC,OACV5B,MAAOoE,EAAEpE,SAEboE,EAAEE,mBAGX9E,EAAQ6C,aAAamB,KAAKsG,GACzBpK,EAAKoB,WACLpB,EAAK+D,KAAK9C,KAAK3B,EAAQ,SAAUoF,GAC7B,GAAI4D,GAAO3L,EAAE+H,EAAE4D,KACXA,GAAKC,SAAS,kBACdvI,EAAKmB,KAAK6E,SAAShG,EAAKyF,WAAWpB,KAAMrE,EAAKF,QAAQ8C,WAAWC,UAMrFjB,aAAc,WACV,GAAI5B,GAAOC,IACXD,GAAK+D,KAAK9C,KAAK3B,EAAQ,SAAUoF,GAC7B,GAAI4D,GAAO3L,EAAE+H,EAAE4D,KACXA,GAAKC,SAAS,WACdvI,EAAKI,MAAMsK,WAAW1K,EAAKM,OACtBN,EAAKoB,WACNpB,EAAK4D,SAEF0E,EAAKC,SAAS,cACrBvI,EAAKI,MAAMuK,aAAa3K,EAAKM,OACxBN,EAAKoB,WACNpB,EAAK4D,YAKrBmB,qBAAsB,WAAA,GASdqD,GACA7K,EAGAqN,EACAC,EAbAvK,EAAQL,KAAKK,MACb7C,EAAUwC,KAAKG,MAAM3C,QACrB0E,EAAStD,EAAKpB,EAAS,SAAU0E,GACjC,MAAOA,GAAO7B,OAASA,GAAS6B,EAAO5B,OAASD,IACjD,EACE6B,KAGDiG,EAASjG,EAAOiG,UAAW,EAC3B7K,EAASsB,EAAKpB,EAAS,SAAU0E,GACjC,OAAQA,EAAOgG,SAAWhG,EAAOiG,QAAUA,IAAWjG,EAAOiG,SAAWA,KACzE7K,OACCqN,EAAW3K,KAAKa,QAAQN,KAAK,WAAWiI,YAAY,oBACpDoC,EAAa5K,KAAKa,QAAQN,KAAK,aAAaiI,YAAY,qBACxDL,GAAoB,GAAV7K,IACVqN,EAASnK,SAAS,oBAEjB2H,GAAoB,GAAV7K,GACXsN,EAAWpK,SAAS,oBAExBR,KAAKgJ,8BAETjI,QAAS,WACL,GAA8D8J,GAAgC3N,EAAKI,EAA/FyC,EAAOC,KAAM2I,EAAO5I,EAAKF,QAAQO,WAAWuI,WAA0BtI,EAAQN,EAAKM,KAEvF,KADAN,EAAKc,QAAQN,KAAK,6BAA6BiI,YAAYzJ,GACtD7B,EAAM,EAAGI,EAASqL,EAAKrL,OAAQJ,EAAMI,EAAQJ,IAC9C2N,EAAalC,EAAKzL,GACdmD,GAASwK,EAAWxK,OACpBN,EAAKc,QAAQN,KAAK,WAAasK,EAAWzC,KAAK5H,SAASzB,EAGhEgB,GAAKD,KAAKC,EAAK+K,aAAa/K,EAAKK,WAAW6B,UAAY,WAAa,eAAe,mBAExF6I,aAAc,SAAUC,GAAV,GAEN9I,GAKK/E,EAASI,EANd0N,GAAQ,CAEZ,IAAKD,EAAL,CAIA,IADAA,EAAUA,EAAQA,QACT7N,EAAM,EAAGI,EAASyN,EAAQzN,OAAQJ,EAAMI,EAAQJ,IACrD+E,EAAS8I,EAAQ7N,GACb+E,EAAO5B,OAASL,KAAKK,MACrB2K,GAAQ,EACD/I,EAAO8I,UACdC,EAAQA,GAAShL,KAAK8K,aAAa7I,GAG3C,OAAO+I,OAGXlI,EAAW,2jDACXqB,EAAiB,qnEACjBI,EAAa7E,EAAOf,QACpBiB,KAAM,SAAU1B,EAAS2B,GACrB,GAAIE,GAAOC,IACXN,GAAOO,GAAGL,KAAKM,KAAKH,EAAM7B,EAAS2B,GACnCE,EAAKkL,oBACLlL,EAAK7B,QAAQyC,GAAG,QAAUlB,EAAI,0EAA2E,WAE7GoC,QAASxC,GACTuB,OAAQ,SAAU6D,GACd,GAAI1E,GAAOC,IAIX,OAHKtD,GAAE+H,EAAEyG,QAAQpF,GAAG,oBAChBrB,EAAEE,iBAEFjI,EAAE+H,EAAEyG,QAAQ5C,SAAS,YACrBvI,EAAK8E,gBAAe,GACpB,GAEAnI,EAAE+H,EAAEyG,QAAQ5C,SAAS,iBACrBvI,EAAK8E,gBAAe,GACpB9E,EAAK6B,QAAQvC,GAAUgJ,KAAM5D,EAAE0G,gBAC/B,IAEJpL,EAAKqL,qBAAqB3G,EAAE0G,eAA5BpL,IAEJqL,qBAAsB,SAAUC,GAAV,GAcVjD,GACAkD,EACAC,EAfJxL,EAAOC,KACPqI,EAAO3L,EAAE2O,GACT/G,EAAQvE,EAAKF,QAAQ2E,WAAWJ,KAAKE,QAAW9G,YAChDgO,EAAKnD,EAAK2B,KAAK,KACf3B,GAAKC,SAAS,mBAIdhE,EAAMkH,IADNlH,EAAMkH,IAKNnD,EAAKC,SAAS,eAAiBD,EAAKC,SAAS,kBAIzCD,EAAKC,SAAS,eACdF,EAAM,MACNkD,EAAYvL,EAAK7B,QAAQqC,KAAK,kBAE9B6H,EAAM,OACNkD,EAAYvL,EAAK7B,QAAQqC,KAAK,gBAElCgL,EAAcD,EAAUtB,KAAK,MACzB5B,IAAQ9D,EAAMS,aAAgBsD,EAAKC,SAAS,sBAC5ChE,EAAMkH,IAAM,GAEZlH,EAAMiH,KACNjH,EAAMiH,IAAe,GAEzBD,EAAU9C,YAAYzJ,IAEtBsJ,EAAKC,SAASvJ,GACdsJ,EAAKG,YAAYzJ,GAEjBsJ,EAAK7H,SAASzB,KAGtB8F,eAAgB,SAAU4G,GAAV,GASJC,GACKzO,EAGOoL,EAKPvB,EAjBT/G,EAAOC,KACP8D,EAAO/D,EAAKF,QAAQ2E,WACpBJ,EAAON,EAAKM,KACZE,EAAQF,EAAKE,QAAW9G,YACxBA,EAAU8G,EAAM9G,OAGpB,IAFAuC,EAAK7B,QAAQqC,KAAK,IAAMxB,GAAQyJ,YAAYzJ,GAC5C+E,EAAK/C,UACD0K,EAAO,CACHC,IACJ,KAASzO,IAAOO,GACRA,EAAQmO,eAAe1O,IACnBO,EAAQP,MAAS,IACboL,EAAOjE,EAAKlG,QAAQqC,KAAK,IAAMtD,GACnCyO,EAAchO,KAAK2K,EAAK,IAIpC,KAASvB,EAAI4E,EAAcpO,OAAS,EAAGwJ,GAAK,EAAGA,IAC3C/G,EAAK6B,QAAQvC,GAAUgJ,KAAMqD,EAAc5E,IAE3ChD,GAAKjE,QAAQuD,eACbU,EAAKgB,uBAGb/E,EAAKF,QAAQ2E,WAAWJ,KAAKE,OAAU9G,aAE3CoH,cAAe,WAAA,GAIF3H,GAGOoL,EANZtI,EAAOC,KACPoE,EAAOrE,EAAKF,QAAQ2E,WAAWJ,KAC/BE,EAAQF,EAAKE,QAAW9G,WAC5B,KAASP,IAAOqH,GACRA,EAAMqH,eAAe1O,IACT,gBAARA,GAAiC,YAARA,GAAqBqH,EAAMrH,MAAS,IACzDoL,EAAOjE,EAAKlG,QAAQqC,KAAK,IAAMtD,GAC/BoL,EAAKC,SAASvJ,GACdsJ,EAAKG,YAAYzJ,GAEjBsJ,EAAK7H,SAASzB,GAElBgB,EAAK6B,QAAQvC,GAAUgJ,KAAMA,EAAK,OAKlD4C,kBAAmB,WACf,GAAIlL,GAAOC,IACXD,GAAK7B,QAAQqC,KAAK,mBAAmBA,KAAK,qBAAuBqL,aAC7DnL,UACI2I,QAAS,GACTyC,UAAW,IAEfrB,OAAQ,SAAU/F,GAAV,GACA4D,GAAO5D,EAAEqH,OAAO5N,QAAQiH,QAAQ,WAChCb,EAAQvE,EAAKF,QAAQ2E,WAAWJ,KAAKE,QAAW9G,YAChDgO,EAAKnD,EAAK2B,KAAK,KAEf1F,GAAM9G,QAAQgO,IADdlH,EAAM9G,QAAQgO,GAKlBzL,EAAK6B,QAAQvC,GAAUgJ,KAAMA,QAIzC0D,mBAAoB,WAAA,GAGZzC,GACKxC,EAHL/G,EAAOC,KACPsF,EAAWvF,EAAK7B,QAAQqC,KAAK,mBAAmBA,KAAK,oBAEzD,KAASuG,EAAI,EAAGA,EAAIxB,EAAShI,OAAQwJ,IACjCwC,EAAehE,EAAShH,GAAGwI,GAAGjD,KAAK,eAC/ByF,GACAA,EAAa/D,WAIzB5B,MAAO,WACH3D,KAAKH,QAAQqB,KAAK6E,SAAS,KAE/BR,QAAS,WACL,GAAIxF,GAAOC,IACXN,GAAOO,GAAGsF,QAAQrF,KAAKH,GACvBA,EAAK7B,QAAQ0H,IAAInG,GACjBM,EAAKgM,uBAGbtN,GAAGuN,OAAOrM,IACZnB,OAAOV,MAAMmO,QACRzN,OAAOV,OACE,kBAAVrB,SAAwBA,OAAOyP,IAAMzP,OAAS,SAAU0P,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.columnmenu.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.columnmenu', [\n        'kendo.popup',\n        'kendo.filtermenu',\n        'kendo.menu'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'columnmenu',\n        name: 'Column Menu',\n        category: 'framework',\n        depends: [\n            'popup',\n            'filtermenu',\n            'menu'\n        ],\n        advanced: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, proxy = $.proxy, extend = $.extend, grep = $.grep, map = $.map, inArray = $.inArray, ACTIVE = 'k-state-selected', ASC = 'asc', DESC = 'desc', CHANGE = 'change', INIT = 'init', OPEN = 'open', SELECT = 'select', POPUP = 'kendoPopup', FILTERMENU = 'kendoFilterMenu', MENU = 'kendoMenu', NS = '.kendoColumnMenu', Widget = ui.Widget;\n        function trim(text) {\n            return $.trim(text).replace(/&nbsp;/gi, '');\n        }\n        function toHash(arr, key) {\n            var result = {};\n            var idx, len, current;\n            for (idx = 0, len = arr.length; idx < len; idx++) {\n                current = arr[idx];\n                result[current[key]] = current;\n            }\n            return result;\n        }\n        function leafColumns(columns) {\n            var result = [];\n            for (var idx = 0; idx < columns.length; idx++) {\n                if (!columns[idx].columns) {\n                    result.push(columns[idx]);\n                    continue;\n                }\n                result = result.concat(leafColumns(columns[idx].columns));\n            }\n            return result;\n        }\n        function attrEquals(attrName, attrValue) {\n            return '[' + kendo.attr(attrName) + '=\\'' + (attrValue || '').replace(/'/g, '\"') + '\\']';\n        }\n        function insertElementAt(index, element, container) {\n            if (index > 0) {\n                element.insertAfter(container.children().eq(index - 1));\n            } else {\n                container.prepend(element);\n            }\n        }\n        var ColumnMenu = Widget.extend({\n            init: function (element, options) {\n                var that = this, link;\n                Widget.fn.init.call(that, element, options);\n                element = that.element;\n                options = that.options;\n                that.owner = options.owner;\n                that.dataSource = options.dataSource;\n                that.field = element.attr(kendo.attr('field'));\n                that.title = element.attr(kendo.attr('title'));\n                link = element.find('.k-header-column-menu');\n                if (!link[0]) {\n                    link = element.addClass('k-with-icon').prepend('<a class=\"k-header-column-menu\" href=\"#\" title=\"' + options.messages.settings + '\" aria-label=\"' + options.messages.settings + '\"><span class=\"k-icon k-i-more-vertical\"></span></a>').find('.k-header-column-menu');\n                }\n                that.link = link.attr('tabindex', -1).on('click' + NS, proxy(that._click, that));\n                that.wrapper = $('<div class=\"k-column-menu\"/>');\n                that._refreshHandler = proxy(that.refresh, that);\n                that.dataSource.bind(CHANGE, that._refreshHandler);\n            },\n            _init: function () {\n                var that = this;\n                that.pane = that.options.pane;\n                if (that.pane) {\n                    that._isMobile = true;\n                }\n                if (that._isMobile) {\n                    that._createMobileMenu();\n                } else {\n                    that._createMenu();\n                }\n                that.owner._muteAngularRebind(function () {\n                    that._angularItems('compile');\n                });\n                that._sort();\n                that._columns();\n                that._filter();\n                that._lockColumns();\n                that.trigger(INIT, {\n                    field: that.field,\n                    container: that.wrapper\n                });\n            },\n            events: [\n                INIT,\n                OPEN,\n                'sort',\n                'filtering'\n            ],\n            options: {\n                name: 'ColumnMenu',\n                messages: {\n                    sortAscending: 'Sort Ascending',\n                    sortDescending: 'Sort Descending',\n                    filter: 'Filter',\n                    column: 'Column',\n                    columns: 'Columns',\n                    columnVisibility: 'Column Visibility',\n                    clear: 'Clear',\n                    cancel: 'Cancel',\n                    done: 'Done',\n                    settings: 'Edit Column Settings',\n                    lock: 'Lock',\n                    unlock: 'Unlock'\n                },\n                filter: '',\n                columns: true,\n                sortable: true,\n                filterable: true,\n                animations: { left: 'slide' }\n            },\n            _createMenu: function () {\n                var that = this, options = that.options;\n                that.wrapper.html(kendo.template(template)({\n                    uid: kendo.guid(),\n                    ns: kendo.ns,\n                    messages: options.messages,\n                    sortable: options.sortable,\n                    filterable: options.filterable,\n                    columns: that._ownerColumns(),\n                    showColumns: options.columns,\n                    lockedColumns: options.lockedColumns\n                }));\n                that.popup = that.wrapper[POPUP]({\n                    anchor: that.link,\n                    open: proxy(that._open, that),\n                    activate: proxy(that._activate, that),\n                    close: function () {\n                        if (that.options.closeCallback) {\n                            that.options.closeCallback(that.element);\n                        }\n                    }\n                }).data(POPUP);\n                that.menu = that.wrapper.children()[MENU]({\n                    orientation: 'vertical',\n                    closeOnClick: false,\n                    open: function () {\n                        that._updateMenuItems();\n                    }\n                }).data(MENU);\n            },\n            _createMobileMenu: function () {\n                var that = this, options = that.options;\n                var html = kendo.template(mobileTemplate)({\n                    ns: kendo.ns,\n                    field: that.field,\n                    title: that.title || that.field,\n                    messages: options.messages,\n                    sortable: options.sortable,\n                    filterable: options.filterable,\n                    columns: that._ownerColumns(),\n                    showColumns: options.columns,\n                    lockedColumns: options.lockedColumns\n                });\n                that.view = that.pane.append(html);\n                that.view.state = { columns: {} };\n                that.wrapper = that.view.element.find('.k-column-menu');\n                that.menu = new MobileMenu(that.wrapper.children(), {\n                    pane: that.pane,\n                    columnMenu: that\n                });\n                that.menu.element.on('transitionend' + NS, function (e) {\n                    e.stopPropagation();\n                });\n                var viewElement = that.view.wrapper && that.view.wrapper[0] ? that.view.wrapper : that.view.element;\n                viewElement.on('click', '.k-header-done', function (e) {\n                    e.preventDefault();\n                    that.menu._applyChanges();\n                    that.menu._cancelChanges(false);\n                    that.close();\n                });\n                viewElement.on('click', '.k-header-cancel', function (e) {\n                    e.preventDefault();\n                    that.menu._cancelChanges(true);\n                    that.close();\n                });\n                that.view.bind('show', function () {\n                    var view = that.view || { columns: {} };\n                    if (that.options.lockedColumns) {\n                        that._updateLockedColumns();\n                    }\n                    if (view.element.find('.k-sort-asc.k-state-selected').length) {\n                        view.state.initialSort = 'asc';\n                    } else if (view.element.find('.k-sort-desc.k-state-selected').length) {\n                        view.state.initialSort = 'desc';\n                    }\n                });\n            },\n            _angularItems: function (action) {\n                var that = this;\n                that.angular(action, function () {\n                    var items = that.wrapper.find('.k-columns-item input[' + kendo.attr('field') + ']').map(function () {\n                        return $(this).closest('li');\n                    });\n                    var data = map(that._ownerColumns(), function (col) {\n                        return { column: col._originalObject };\n                    });\n                    return {\n                        elements: items,\n                        data: data\n                    };\n                });\n            },\n            destroy: function () {\n                var that = this;\n                that._angularItems('cleanup');\n                Widget.fn.destroy.call(that);\n                if (that.filterMenu) {\n                    that.filterMenu.destroy();\n                }\n                if (that._refreshHandler) {\n                    that.dataSource.unbind(CHANGE, that._refreshHandler);\n                }\n                if (that.options.columns && that.owner) {\n                    if (that._updateColumnsMenuHandler) {\n                        that.owner.unbind('columnShow', that._updateColumnsMenuHandler);\n                        that.owner.unbind('columnHide', that._updateColumnsMenuHandler);\n                    }\n                    if (that._updateColumnsLockedStateHandler) {\n                        that.owner.unbind('columnLock', that._updateColumnsLockedStateHandler);\n                        that.owner.unbind('columnUnlock', that._updateColumnsLockedStateHandler);\n                    }\n                }\n                if (that.menu) {\n                    that.menu.element.off(NS);\n                    that.menu.destroy();\n                }\n                that.wrapper.off(NS);\n                if (that.popup) {\n                    that.popup.destroy();\n                }\n                if (that.view) {\n                    that.view.purge();\n                }\n                that.link.off(NS);\n                that.owner = null;\n                that.wrapper = null;\n                that.element = null;\n            },\n            close: function () {\n                this.menu.close();\n                if (this.popup) {\n                    this.popup.close();\n                    this.popup.element.off('keydown' + NS);\n                }\n            },\n            _click: function (e) {\n                var that = this;\n                e.preventDefault();\n                e.stopPropagation();\n                var options = this.options;\n                if (options.filter && this.element.is(!options.filter)) {\n                    return;\n                }\n                if (!this.popup && !this.pane) {\n                    this._init();\n                } else {\n                    that._updateMenuItems();\n                }\n                if (this._isMobile) {\n                    this.pane.navigate(this.view, this.options.animations.left);\n                } else {\n                    this.popup.toggle();\n                }\n            },\n            _updateMenuItems: function () {\n                var that = this;\n                that._setMenuItemsVisibility();\n                that._reorderMenuItems();\n            },\n            _setMenuItemsVisibility: function () {\n                var that = this;\n                that._eachRenderedMenuItem(function (index, column, renderedListElement) {\n                    if (column.matchesMedia === false) {\n                        renderedListElement.hide();\n                    } else {\n                        renderedListElement.show();\n                    }\n                });\n            },\n            _reorderMenuItems: function () {\n                var that = this;\n                that._eachRenderedMenuItem(function (index, column, renderedListElement, renderedList) {\n                    if (renderedListElement[0] && renderedListElement.index() !== index) {\n                        insertElementAt(index, renderedListElement, renderedList);\n                    }\n                });\n            },\n            _eachRenderedMenuItem: function (callback) {\n                var that = this;\n                var renderedListElement;\n                var duplcateColumns;\n                var duplicateColumnIndex;\n                var fieldValue;\n                var currentColumn;\n                var columns = grep(leafColumns(that.owner.columns), function (col) {\n                    var result = true, title = trim(col.title || '');\n                    if (col.menu === false || !col.field && !title.length) {\n                        result = false;\n                    }\n                    return result;\n                }).map(function (col) {\n                    return {\n                        field: col.field,\n                        title: col.title,\n                        matchesMedia: col.matchesMedia\n                    };\n                });\n                var renderedList = that._isMobile && that.view ? $(that.view.element).find('.k-columns-item').children('ul') : $(that.wrapper).find('.k-menu-group').first();\n                var filterByTitle = function (containerElement, tagName, index) {\n                    return containerElement.find(tagName).filter(function () {\n                        return filterCallback(columns[index], $(this).text());\n                    });\n                };\n                var filterCallback = function (column, text) {\n                    return matchTitle(column, text);\n                };\n                var matchTitle = function (column, titleAttr) {\n                    return column.title ? titleAttr === column.title : titleAttr === column.field;\n                };\n                var duplicateColumns = function (index) {\n                    return grep(columns, function (col) {\n                        return JSON.stringify(columns[index]) == JSON.stringify(col);\n                    });\n                };\n                for (var i = 0; i < columns.length; i++) {\n                    currentColumn = columns[i];\n                    duplcateColumns = duplicateColumns(i);\n                    duplicateColumnIndex = $.inArray(currentColumn, duplcateColumns);\n                    renderedListElement = filterByTitle(renderedList, 'span', i);\n                    renderedListElement = this._isMobile ? renderedListElement.next() : renderedListElement;\n                    fieldValue = currentColumn.field ? currentColumn.field : currentColumn.title;\n                    renderedListElement = renderedListElement.find(attrEquals('field', fieldValue)).closest('li').eq(duplicateColumnIndex);\n                    callback(i, currentColumn, renderedListElement, renderedList);\n                }\n            },\n            _open: function () {\n                var that = this;\n                $('.k-column-menu').not(that.wrapper).each(function () {\n                    $(this).data(POPUP).close();\n                });\n                that.popup.element.on('keydown' + NS, function (e) {\n                    if (e.keyCode == kendo.keys.ESC) {\n                        that.close();\n                    }\n                });\n                if (that.options.lockedColumns) {\n                    that._updateLockedColumns();\n                }\n            },\n            _activate: function () {\n                this.menu.element.focus();\n                this.trigger(OPEN, {\n                    field: this.field,\n                    container: this.wrapper\n                });\n            },\n            _ownerColumns: function () {\n                var columns = leafColumns(this.owner.columns), menuColumns = grep(columns, function (col) {\n                        var result = true, title = trim(col.title || '');\n                        if (col.menu === false || !col.field && !title.length) {\n                            result = false;\n                        }\n                        return result;\n                    });\n                return map(menuColumns, function (col) {\n                    return {\n                        originalField: col.field,\n                        field: col.field || col.title,\n                        title: col.title || col.field,\n                        hidden: col.hidden,\n                        matchesMedia: col.matchesMedia,\n                        index: inArray(col, columns),\n                        locked: !!col.locked,\n                        _originalObject: col\n                    };\n                });\n            },\n            _sort: function () {\n                var that = this;\n                if (that.options.sortable) {\n                    that.refresh();\n                    that.menu.bind(SELECT, function (e) {\n                        var item = $(e.item), dir;\n                        if (item.hasClass('k-sort-asc')) {\n                            dir = ASC;\n                        } else if (item.hasClass('k-sort-desc')) {\n                            dir = DESC;\n                        }\n                        if (!dir) {\n                            return;\n                        }\n                        item.parent().find('.k-sort-' + (dir == ASC ? DESC : ASC)).removeClass(ACTIVE);\n                        that._sortDataSource(item, dir);\n                        if (!that._isMobile) {\n                            that.close();\n                        }\n                    });\n                }\n            },\n            _sortDataSource: function (item, dir) {\n                var that = this, sortable = that.options.sortable, compare = sortable.compare === null ? undefined : sortable.compare, dataSource = that.dataSource, idx, length, sort = dataSource.sort() || [];\n                var removeClass = item.hasClass(ACTIVE) && sortable && sortable.allowUnsort !== false;\n                dir = !removeClass ? dir : undefined;\n                if (that.trigger('sort', {\n                        sort: {\n                            field: that.field,\n                            dir: dir,\n                            compare: compare\n                        }\n                    })) {\n                    return;\n                }\n                if (removeClass) {\n                    item.removeClass(ACTIVE);\n                } else {\n                    item.addClass(ACTIVE);\n                }\n                if (sortable.mode === 'multiple') {\n                    for (idx = 0, length = sort.length; idx < length; idx++) {\n                        if (sort[idx].field === that.field) {\n                            sort.splice(idx, 1);\n                            break;\n                        }\n                    }\n                    sort.push({\n                        field: that.field,\n                        dir: dir,\n                        compare: compare\n                    });\n                } else {\n                    sort = [{\n                            field: that.field,\n                            dir: dir,\n                            compare: compare\n                        }];\n                }\n                dataSource.sort(sort);\n            },\n            _columns: function () {\n                var that = this;\n                if (that.options.columns) {\n                    that._updateColumnsMenu();\n                    that._updateColumnsMenuHandler = proxy(that._updateColumnsMenu, that);\n                    that.owner.bind([\n                        'columnHide',\n                        'columnShow'\n                    ], that._updateColumnsMenuHandler);\n                    that._updateColumnsLockedStateHandler = proxy(that._updateColumnsLockedState, that);\n                    that.owner.bind([\n                        'columnUnlock',\n                        'columnLock'\n                    ], that._updateColumnsLockedStateHandler);\n                    that.menu.bind(SELECT, function (e) {\n                        var item = $(e.item), input, column, columns = grep(leafColumns(that.owner.columns), function (col) {\n                                var result = true, title = trim(col.title || '');\n                                if (col.menu === false || !col.field && !title.length) {\n                                    result = false;\n                                }\n                                return result;\n                            });\n                        if (that._isMobile) {\n                            e.preventDefault();\n                        }\n                        if (!item.parent().closest('li.k-columns-item')[0]) {\n                            return;\n                        }\n                        input = item.find(':checkbox');\n                        if (input.attr('disabled')) {\n                            return;\n                        }\n                        column = columns[item.index()];\n                        if (column.hidden === true) {\n                            that.owner.showColumn(column);\n                        } else {\n                            that.owner.hideColumn(column);\n                        }\n                    });\n                }\n            },\n            _updateColumnsMenu: function () {\n                var idx, length, current, checked, locked;\n                var fieldAttr = kendo.attr('field'), lockedAttr = kendo.attr('locked'), columnsInMenu = grep(leafColumns(this.owner.columns), function (col) {\n                        var result = true, title = trim(col.title || '');\n                        if (col.menu === false || !col.field && !title.length) {\n                            result = false;\n                        }\n                        return result;\n                    }), visibleFields = grep(this._ownerColumns(), function (field) {\n                        return !field.hidden && field.matchesMedia !== false;\n                    }), visibleDataFields = grep(visibleFields, function (field) {\n                        return field.originalField;\n                    }), lockedCount = grep(visibleDataFields, function (col) {\n                        return col.locked === true;\n                    }).length, nonLockedCount = grep(visibleDataFields, function (col) {\n                        return col.locked !== true;\n                    }).length, columnsNotInMenu = grep(this.owner.columns, function (col) {\n                        return col.menu === false;\n                    }), hiddenColumnsNotInMenu = grep(columnsNotInMenu, function (col) {\n                        return col.hidden;\n                    });\n                this.wrapper.find('[role=\\'menuitemcheckbox\\']').attr('aria-checked', false);\n                var checkboxes = this.wrapper.find('.k-columns-item input[' + fieldAttr + ']').prop('disabled', false).prop('checked', false);\n                var switchWidget;\n                for (idx = 0, length = checkboxes.length; idx < length; idx++) {\n                    current = checkboxes.eq(idx);\n                    locked = current.attr(lockedAttr) === 'true';\n                    checked = false;\n                    switchWidget = current.data('kendoSwitch');\n                    checked = !columnsInMenu[idx].hidden && columnsInMenu[idx].matchesMedia !== false;\n                    current.prop('checked', checked);\n                    if (switchWidget) {\n                        switchWidget.enable(true);\n                        switchWidget.check(checked);\n                    }\n                    current.closest('[role=\\'menuitemcheckbox\\']').attr('aria-checked', checked);\n                    if (checked) {\n                        if (lockedCount == 1 && locked) {\n                            current.prop('disabled', true);\n                            if (switchWidget) {\n                                switchWidget.enable(false);\n                            }\n                        }\n                        if ((columnsNotInMenu.length === 0 || columnsNotInMenu.length === hiddenColumnsNotInMenu.length) && nonLockedCount == 1 && !locked) {\n                            current.prop('disabled', true);\n                            if (switchWidget) {\n                                switchWidget.enable(false);\n                            }\n                        }\n                    }\n                }\n            },\n            _updateColumnsLockedState: function () {\n                var idx, length, current, column;\n                var fieldAttr = kendo.attr('field');\n                var lockedAttr = kendo.attr('locked');\n                var columns = toHash(this._ownerColumns(), 'field');\n                var checkboxes = this.wrapper.find('.k-columns-item input[type=checkbox]');\n                for (idx = 0, length = checkboxes.length; idx < length; idx++) {\n                    current = checkboxes.eq(idx);\n                    column = columns[current.attr(fieldAttr)];\n                    if (column) {\n                        current.attr(lockedAttr, column.locked);\n                    }\n                }\n                this._updateColumnsMenu();\n            },\n            _filter: function () {\n                var that = this, widget = FILTERMENU, options = that.options;\n                if (options.filterable !== false) {\n                    if (options.filterable.multi) {\n                        widget = 'kendoFilterMultiCheck';\n                        if (options.filterable.dataSource) {\n                            options.filterable.checkSource = options.filterable.dataSource;\n                            delete options.filterable.dataSource;\n                        }\n                    }\n                    that.filterMenu = that.wrapper.find('.k-filterable')[widget](extend(true, {}, {\n                        appendToElement: true,\n                        dataSource: options.dataSource,\n                        values: options.values,\n                        field: that.field,\n                        title: that.title,\n                        change: function (e) {\n                            if (that.trigger('filtering', {\n                                    filter: e.filter,\n                                    field: e.field\n                                })) {\n                                e.preventDefault();\n                            }\n                        }\n                    }, options.filterable)).data(widget);\n                    if (that._isMobile) {\n                        that.menu.bind(SELECT, function (e) {\n                            var item = $(e.item);\n                            if (item.hasClass('k-filter-item')) {\n                                that.pane.navigate(that.filterMenu.view, that.options.animations.left);\n                            }\n                        });\n                    }\n                }\n            },\n            _lockColumns: function () {\n                var that = this;\n                that.menu.bind(SELECT, function (e) {\n                    var item = $(e.item);\n                    if (item.hasClass('k-lock')) {\n                        that.owner.lockColumn(that.field);\n                        if (!that._isMobile) {\n                            that.close();\n                        }\n                    } else if (item.hasClass('k-unlock')) {\n                        that.owner.unlockColumn(that.field);\n                        if (!that._isMobile) {\n                            that.close();\n                        }\n                    }\n                });\n            },\n            _updateLockedColumns: function () {\n                var field = this.field;\n                var columns = this.owner.columns;\n                var column = grep(columns, function (column) {\n                    return column.field == field || column.title == field;\n                })[0];\n                if (!column) {\n                    return;\n                }\n                var locked = column.locked === true;\n                var length = grep(columns, function (column) {\n                    return !column.hidden && (column.locked && locked || !column.locked && !locked);\n                }).length;\n                var lockItem = this.wrapper.find('.k-lock').removeClass('k-state-disabled');\n                var unlockItem = this.wrapper.find('.k-unlock').removeClass('k-state-disabled');\n                if (locked || length == 1) {\n                    lockItem.addClass('k-state-disabled');\n                }\n                if (!locked || length == 1) {\n                    unlockItem.addClass('k-state-disabled');\n                }\n                this._updateColumnsLockedState();\n            },\n            refresh: function () {\n                var that = this, sort = that.options.dataSource.sort() || [], descriptor, field = that.field, idx, length;\n                that.wrapper.find('.k-sort-asc, .k-sort-desc').removeClass(ACTIVE);\n                for (idx = 0, length = sort.length; idx < length; idx++) {\n                    descriptor = sort[idx];\n                    if (field == descriptor.field) {\n                        that.wrapper.find('.k-sort-' + descriptor.dir).addClass(ACTIVE);\n                    }\n                }\n                that.link[that._filterExist(that.dataSource.filter()) ? 'addClass' : 'removeClass']('k-state-active');\n            },\n            _filterExist: function (filters) {\n                var found = false;\n                var filter;\n                if (!filters) {\n                    return;\n                }\n                filters = filters.filters;\n                for (var idx = 0, length = filters.length; idx < length; idx++) {\n                    filter = filters[idx];\n                    if (filter.field == this.field) {\n                        found = true;\n                    } else if (filter.filters) {\n                        found = found || this._filterExist(filter);\n                    }\n                }\n                return found;\n            }\n        });\n        var template = '<ul id=\"#=uid#\">' + '#if(sortable){#' + '<li class=\"k-item k-sort-asc\"><span class=\"k-link\"><span class=\"k-icon k-i-sort-asc-sm\"></span>${messages.sortAscending}</span></li>' + '<li class=\"k-item k-sort-desc\"><span class=\"k-link\"><span class=\"k-icon k-i-sort-desc-sm\"></span>${messages.sortDescending}</span></li>' + '#if(showColumns || filterable){#' + '<li class=\"k-separator\" role=\"presentation\"></li>' + '#}#' + '#}#' + '#if(showColumns){#' + '<li class=\"k-item k-columns-item\" aria-haspopup=\"true\"><span class=\"k-link\"><span class=\"k-icon k-i-columns\"></span>${messages.columns}</span><ul>' + '#for (var idx = 0; idx < columns.length; idx++) {#' + '<li role=\"menuitemcheckbox\" aria-checked=\"false\" #=columns[idx].matchesMedia === false ? \"style=\\'display:none;\\'\" : \"\"#><input type=\"checkbox\" title=\"#=columns[idx].title#\" data-#=ns#field=\"#=columns[idx].field.replace(/\"/g,\"&\\\\#34;\")#\" data-#=ns#index=\"#=columns[idx].index#\" data-#=ns#locked=\"#=columns[idx].locked#\"/>#=columns[idx].title#</li>' + '#}#' + '</ul></li>' + '#if(filterable || lockedColumns){#' + '<li class=\"k-separator\" role=\"presentation\"></li>' + '#}#' + '#}#' + '#if(filterable){#' + '<li class=\"k-item k-filter-item\" aria-haspopup=\"true\"><span class=\"k-link\"><span class=\"k-icon k-i-filter\"></span>${messages.filter}</span><ul>' + '<li><div class=\"k-filterable\"></div></li>' + '</ul></li>' + '#if(lockedColumns){#' + '<li class=\"k-separator\" role=\"presentation\"></li>' + '#}#' + '#}#' + '#if(lockedColumns){#' + '<li class=\"k-item k-lock\"><span class=\"k-link\"><span class=\"k-icon k-i-lock\"></span>${messages.lock}</span></li>' + '<li class=\"k-item k-unlock\"><span class=\"k-link\"><span class=\"k-icon k-i-unlock\"></span>${messages.unlock}</span></li>' + '#}#' + '</ul>';\n        var mobileTemplate = '<div data-#=ns#role=\"view\" class=\"k-grid-column-menu\">' + '<div data-#=ns#role=\"header\" class=\"k-header\">' + '<a href=\"\\\\#\" class=\"k-header-cancel k-link\" title=\"#=messages.cancel#\" ' + 'aria-label=\"#=messages.cancel#\"><span class=\"k-icon k-i-arrow-chevron-left\"></span></a>' + '${messages.settings}' + '<a href=\"\\\\#\" class=\"k-header-done k-link\" title=\"#=messages.done#\" ' + 'aria-label=\"#=messages.done#\"><span class=\"k-icon k-i-check\"></span></a>' + '</div>' + '<div class=\"k-column-menu k-mobile-list\">' + '<ul>' + '<li>' + '<span class=\"k-list-title\">#=messages.column#: ${title}</span>' + '<ul>' + '#if(sortable){#' + '<li id=\"#=kendo.guid()#\" class=\"k-item k-sort-asc\"><span class=\"k-link\"><span class=\"k-icon k-i-sort-asc-sm\"></span><span class=\"k-item-title\">${messages.sortAscending}</span></span></li>' + '<li id=\"#=kendo.guid()#\" class=\"k-item k-sort-desc\"><span class=\"k-link\"><span class=\"k-icon k-i-sort-desc-sm\"></span><span class=\"k-item-title\">${messages.sortDescending}</span></span></li>' + '#}#' + '#if(lockedColumns){#' + '<li id=\"#=kendo.guid()#\" class=\"k-item k-lock\"><span class=\"k-link\"><span class=\"k-icon k-i-lock\"></span><span class=\"k-item-title\">${messages.lock}</span></span></li>' + '<li id=\"#=kendo.guid()#\" class=\"k-item k-unlock\"><span class=\"k-link\"><span class=\"k-icon k-i-unlock\"></span><span class=\"k-item-title\">${messages.unlock}</span></span></li>' + '#}#' + '#if(filterable){#' + '<li id=\"#=kendo.guid()#\" class=\"k-item k-filter-item\">' + '<span class=\"k-link k-filterable\">' + '<span class=\"k-icon k-i-filter\"></span>' + '<span class=\"k-item-title\">${messages.filter}</span></span>' + '</li>' + '#}#' + '</ul>' + '</li>' + '#if(showColumns){#' + '<li class=\"k-columns-item\"><span class=\"k-list-title\">${messages.columnVisibility}</span>' + '<ul>' + '#for (var idx = 0; idx < columns.length; idx++) {#' + '<li id=\"#=kendo.guid()#\" class=\"k-item\">' + '<span class=\"k-item-title\">' + '#=columns[idx].title#' + '</span>' + '<input type=\"checkbox\" title=\"#=columns[idx].title#\" ' + ' data-#=ns#field=\"#=columns[idx].field.replace(/\"/g,\"&\\\\#34;\")#\"' + ' data-#=ns#index=\"#=columns[idx].index#\"' + ' data-#=ns#locked=\"#=columns[idx].locked#\"/>' + '</li>' + '#}#' + '</ul>' + '</li>' + '#}#' + '<li class=\"k-item k-clear-wrap\">' + '<span class=\"k-label k-clear\" title=\"#=messages.clear#\" ' + 'aria-label=\"#=messages.clear#\">#=messages.clear#</span>' + '</li>' + '</ul>' + '</div>' + '</div>';\n        var MobileMenu = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                that._createCheckBoxes();\n                that.element.on('click' + NS, 'li.k-item:not(.k-separator):not(.k-state-disabled):not(:has(.k-switch))', '_click');\n            },\n            events: [SELECT],\n            _click: function (e) {\n                var that = this;\n                if (!$(e.target).is('[type=checkbox]')) {\n                    e.preventDefault();\n                }\n                if ($(e.target).hasClass('k-clear')) {\n                    that._cancelChanges(true);\n                    return;\n                }\n                if ($(e.target).hasClass('k-filterable')) {\n                    that._cancelChanges(true);\n                    that.trigger(SELECT, { item: e.currentTarget });\n                    return;\n                }\n                that._updateSelectedItems(e.currentTarget);\n            },\n            _updateSelectedItems: function (el) {\n                var that = this;\n                var item = $(el);\n                var state = that.options.columnMenu.view.state || { columns: {} };\n                var id = item.prop('id');\n                if (item.hasClass('k-filter-item')) {\n                    return;\n                }\n                if (state[id]) {\n                    state[id] = false;\n                } else {\n                    state[id] = true;\n                }\n                if (item.hasClass('k-sort-asc') || item.hasClass('k-sort-desc')) {\n                    var dir;\n                    var otherItem;\n                    var otherItemId;\n                    if (item.hasClass('k-sort-asc')) {\n                        dir = 'asc';\n                        otherItem = that.element.find('.k-sort-desc');\n                    } else {\n                        dir = 'desc';\n                        otherItem = that.element.find('.k-sort-asc');\n                    }\n                    otherItemId = otherItem.prop('id');\n                    if (dir === state.initialSort && !item.hasClass('k-state-selected')) {\n                        state[id] = false;\n                    }\n                    if (state[otherItemId]) {\n                        state[otherItemId] = false;\n                    }\n                    otherItem.removeClass(ACTIVE);\n                }\n                if (item.hasClass(ACTIVE)) {\n                    item.removeClass(ACTIVE);\n                } else {\n                    item.addClass(ACTIVE);\n                }\n            },\n            _cancelChanges: function (force) {\n                var that = this;\n                var menu = that.options.columnMenu;\n                var view = menu.view;\n                var state = view.state || { columns: {} };\n                var columns = state.columns;\n                that.element.find('.' + ACTIVE).removeClass(ACTIVE);\n                menu.refresh();\n                if (force) {\n                    var selectedItems = [];\n                    for (var key in columns) {\n                        if (columns.hasOwnProperty(key)) {\n                            if (columns[key] === true) {\n                                var item = view.element.find('#' + key);\n                                selectedItems.push(item[0]);\n                            }\n                        }\n                    }\n                    for (var i = selectedItems.length - 1; i >= 0; i--) {\n                        that.trigger(SELECT, { item: selectedItems[i] });\n                    }\n                    if (menu.options.lockedColumns) {\n                        menu._updateLockedColumns();\n                    }\n                }\n                that.options.columnMenu.view.state = { columns: {} };\n            },\n            _applyChanges: function () {\n                var that = this;\n                var view = that.options.columnMenu.view;\n                var state = view.state || { columns: {} };\n                for (var key in state) {\n                    if (state.hasOwnProperty(key)) {\n                        if (key !== 'initialSort' && key !== 'columns' && state[key] === true) {\n                            var item = view.element.find('#' + key);\n                            if (item.hasClass(ACTIVE)) {\n                                item.removeClass(ACTIVE);\n                            } else {\n                                item.addClass(ACTIVE);\n                            }\n                            that.trigger(SELECT, { item: item[0] });\n                        }\n                    }\n                }\n            },\n            _createCheckBoxes: function () {\n                var that = this;\n                that.element.find('.k-columns-item').find('[type=\\'checkbox\\']').kendoSwitch({\n                    messages: {\n                        checked: '',\n                        unchecked: ''\n                    },\n                    change: function (e) {\n                        var item = e.sender.element.closest('.k-item');\n                        var state = that.options.columnMenu.view.state || { columns: {} };\n                        var id = item.prop('id');\n                        if (state.columns[id]) {\n                            state.columns[id] = false;\n                        } else {\n                            state.columns[id] = true;\n                        }\n                        that.trigger(SELECT, { item: item });\n                    }\n                });\n            },\n            _destroyCheckBoxes: function () {\n                var that = this;\n                var elements = that.element.find('.k-columns-item').find('[type=\\'checkbox\\']');\n                var switchWidget;\n                for (var i = 0; i < elements.length; i++) {\n                    switchWidget = elements.eq(i).data('kendoSwitch');\n                    if (switchWidget) {\n                        switchWidget.destroy();\n                    }\n                }\n            },\n            close: function () {\n                this.options.pane.navigate('');\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that.element.off(NS);\n                that._destroyCheckBoxes();\n            }\n        });\n        ui.plugin(ColumnMenu);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}