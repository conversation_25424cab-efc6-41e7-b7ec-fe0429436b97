{"version": 3, "sources": ["kendo.numerictextbox.js"], "names": ["f", "define", "$", "undefined", "buttonHtml", "direction", "text", "className", "CLASS_ICON", "truncate", "value", "precision", "parts", "parseFloat", "split", "POINT", "substring", "join", "kendo", "window", "caret", "keys", "ui", "Widget", "activeElement", "_activeElement", "extractFormat", "_extractFormat", "parse", "placeholderSupported", "support", "placeholder", "getCulture", "CHANGE", "DISABLED", "READONLY", "INPUT", "SPIN", "ns", "TOUCHEND", "MOUSELEAVE", "HOVEREVENTS", "DEFAULT", "FOCUSED", "HOVER", "FOCUS", "SELECTED", "STATEDISABLED", "STATE_INVALID", "ARIA_DISABLED", "INTEGER_REGEXP", "NULL", "proxy", "extend", "NumericTextBox", "init", "element", "options", "min", "max", "step", "disabled", "inputType", "that", "this", "isStep", "fn", "call", "on", "_focusout", "attr", "_parse", "_initialOptions", "_reset", "_wrapper", "_arrows", "_validation", "_input", "mobileOS", "_text", "browser", "edge", "one", "_toggleText", "focus", "_click", "factor", "format", "val", "is", "parents", "enable", "readonly", "angular", "elements", "get", "notify", "name", "decimals", "restrictDecimals", "round", "culture", "spinners", "upArrowText", "downArrowText", "events", "_editable", "disable", "add", "wrapper", "_inputWrapper", "off", "_upArrowEventHandler", "unbind", "_downArrowEventHandler", "addClass", "removeClass", "_toggleHover", "removeAttr", "bind", "e", "preventDefault", "_spin", "_upArrow", "_downArrow", "_keydown", "_keypress", "_keyup", "_paste", "setOptions", "_arrowsWrap", "toggle", "toggleClass", "prop", "_placeholder", "aria-valuemin", "aria-valuemax", "destroy", "_form", "_reset<PERSON><PERSON><PERSON>", "_option", "adjusted", "_value", "_adjust", "_update", "_old", "_focusin", "arrows", "_release", "clearTimeout", "_spinning", "siblings", "insertAfter", "wrapAll", "parent", "eq", "UserEvents", "release", "_validationIcon", "hide", "_blur", "_change", "_focusing", "setTimeout", "result", "groupRegExp", "extractRegExp", "input", "target", "idx", "_format", "group", "caretPosition", "RegExp", "exec", "replace", "length", "indexOf", "_typing", "trigger", "_culture", "_removeInvalidState", "numberFormat", "toLowerCase", "currency", "percent", "CLASSNAME", "show", "accessKey", "find", "insertBefore", "setAttribute", "type", "title", "tabIndex", "style", "cssText", "role", "autocomplete", "key", "keyCode", "_key", "DOWN", "_step", "UP", "ENTER", "TAB", "selection", "selectionStart", "selectionEnd", "character", "isNumPadDecimal", "<PERSON><PERSON><PERSON><PERSON>", "which", "metaKey", "ctrl<PERSON>ey", "BACKSPACE", "String", "fromCharCode", "NUMPAD_DOT", "_numericRegex", "test", "char<PERSON>t", "_addInvalidState", "separator", "fractionRule", "_separator", "_floatRegExp", "option", "timeout", "originalValue", "toFixed", "currentTarget", "_round", "rounder", "isNotNull", "toString", "DOMElement", "wrap", "width", "css", "<PERSON><PERSON><PERSON><PERSON>", "formId", "form", "closest", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,wBACH,aACA,oBACDD,IACL,WA+hBE,MAphBC,UAAUE,EAAGC,GAugBV,QAASC,GAAWC,EAAWC,GAC3B,GAAIC,GAAY,cAA8B,aAAdF,EAA2B,QAAU,UACrE,OAAO,gDAAkDA,EAAY,iBAAmBC,EAAO,YAAcA,EAAO,oCAA2CE,EAAa,IAAMD,EAAY,mBAElM,QAASE,GAASC,EAAOC,GACrB,GAAIC,IAAQC,GAAAA,WAAWH,EAAO,KAAeI,MAAMC,EAInD,OAHIH,GAAM,KACNA,EAAM,GAAKA,EAAM,GAAGI,UAAU,EAAGL,IAE9BC,EAAMK,KAAKF,GAhhBzB,GACOG,GAAQC,OAAOD,MAAOE,EAAQF,EAAME,MAAOC,EAAOH,EAAMG,KAAMC,EAAKJ,EAAMI,GAAIC,EAASD,EAAGC,OAAQC,EAAgBN,EAAMO,eAAgBC,EAAgBR,EAAMS,eAAgBC,EAAQV,EAAML,WAAYgB,EAAuBX,EAAMY,QAAQC,YAAaC,EAAad,EAAMc,WAAYC,EAAS,SAAUC,EAAW,WAAYC,EAAW,WAAYC,EAAQ,UAAWC,EAAO,OAAQC,EAAK,uBAAwBC,EAAW,WAAYC,EAAa,aAAeF,EAAIG,EAAc,aAAeH,EAAK,IAAME,EAAYE,EAAU,kBAAmBC,EAAU,kBAAmBC,EAAQ,gBAAiBC,EAAQ,QAAS9B,EAAQ,IAAKP,EAAa,SAAUsC,EAAW,mBAAoBC,EAAgB,mBAAoBC,EAAgB,kBAAmBC,EAAgB,gBAAiBC,EAAiB,cAAeC,EAAO,KAAMC,EAAQlD,EAAEkD,MAAOC,EAASnD,EAAEmD,OAC31BC,EAAiB/B,EAAO8B,QACxBE,KAAM,SAAUC,EAASC,GAAnB,GAC+DC,GAAKC,EAAKC,EAAMlD,EAAOmD,EACpFC,EADAC,EAAOC,KAAMC,EAASR,GAAWA,EAAQG,OAASzD,CAEtDoB,GAAO2C,GAAGX,KAAKY,KAAKJ,EAAMP,EAASC,GACnCA,EAAUM,EAAKN,QACfD,EAAUO,EAAKP,QAAQY,GAAG,WAAa9B,EAAIc,EAAMW,EAAKM,UAAWN,IAAOO,KAAK,OAAQ,cACrFb,EAAQ1B,YAAc0B,EAAQ1B,aAAeyB,EAAQc,KAAK,eAC1DZ,EAAMK,EAAKL,IAAIF,EAAQc,KAAK,QAC5BX,EAAMI,EAAKJ,IAAIH,EAAQc,KAAK,QAC5BV,EAAOG,EAAKQ,OAAOf,EAAQc,KAAK,SAC5Bb,EAAQC,MAAQP,GAAQO,IAAQP,IAChCM,EAAQC,IAAMA,GAEdD,EAAQE,MAAQR,GAAQQ,IAAQR,IAChCM,EAAQE,IAAMA,GAEbM,GAAUL,IAAST,IACpBM,EAAQG,KAAOA,GAEnBG,EAAKS,gBAAkBnB,KAAWI,GAClCK,EAAYN,EAAQc,KAAK,QACzBP,EAAKU,SACLV,EAAKW,WACLX,EAAKY,UACLZ,EAAKa,cACLb,EAAKc,SACA3D,EAAMY,QAAQgD,SAGff,EAAKgB,MAAMX,GAAG7B,EAAWD,EAAK,IAAMO,EAAQP,EAAI,WACxCpB,EAAMY,QAAQkD,QAAQC,KACtBlB,EAAKgB,MAAMG,IAAIrC,EAAQP,EAAI,WACvByB,EAAKoB,aAAY,GACjB3B,EAAQ4B,WAGZrB,EAAKoB,aAAY,GACjB3B,EAAQ4B,WAVhBrB,EAAKgB,MAAMX,GAAGvB,EAAQP,EAAIc,EAAMW,EAAKsB,OAAQtB,IAcjDP,EAAQc,KAAK,gBAAiBb,EAAQC,MAAQP,EAAOM,EAAQC,IAAMD,EAAQ6B,OAAS7B,EAAQC,KAAKY,KAAK,gBAAiBb,EAAQE,MAAQR,EAAOM,EAAQE,IAAMF,EAAQ6B,OAAS7B,EAAQE,KACrLF,EAAQ8B,OAAS7D,EAAc+B,EAAQ8B,QACvC7E,EAAQ+C,EAAQ/C,MACZA,GAASyC,IAELzC,EADa,UAAboD,EACQjD,WAAW2C,EAAQgC,OAEnBhC,EAAQgC,OAGxBzB,EAAKrD,MAAMA,GACXmD,EAAWL,EAAQiC,GAAG,eAAiBvF,EAAE6D,EAAKP,SAASkC,QAAQ,YAAYD,GAAG,aAC1E5B,EACAE,EAAK4B,QAAO,GAEZ5B,EAAK6B,SAASpC,EAAQiC,GAAG,eAE7B1B,EAAK8B,QAAQ,UAAW,WACpB,OAASC,SAAU/B,EAAKgB,MAAMgB,SAElC7E,EAAM8E,OAAOjC,IAEjBN,SACIwC,KAAM,iBACNC,SAAU/C,EACVgD,kBAAkB,EAClBzC,IAAKP,EACLQ,IAAKR,EACLzC,MAAOyC,EACPS,KAAM,EACNwC,OAAO,EACPC,QAAS,GACTd,OAAQ,IACRe,UAAU,EACVvE,YAAa,GACbuD,OAAQ,EACRiB,YAAa,iBACbC,cAAe,kBAEnBC,QACIxE,EACAI,GAEJqE,UAAW,SAAUjD,GACjB,GAAIM,GAAOC,KAAMR,EAAUO,EAAKP,QAASmD,EAAUlD,EAAQkD,QAASf,EAAWnC,EAAQmC,SAAUtF,EAAOyD,EAAKgB,MAAM6B,IAAIpD,GAAUqD,EAAU9C,EAAK+C,cAAcC,IAAItE,EAClKsB,GAAKoB,aAAY,GACjBpB,EAAKiD,qBAAqBC,OAAO,SACjClD,EAAKmD,uBAAuBD,OAAO,SACnCzD,EAAQuD,IAAI,UAAYzE,GAAIyE,IAAI,WAAazE,GAAIyE,IAAI,QAAUzE,GAAIyE,IAAI,QAAUzE,GAC5EsD,GAAae,GAedE,EAAQM,SAASR,EAAU5D,EAAgBL,GAAS0E,YAAYT,EAAUjE,EAAUK,GACpFzC,EAAKgE,KAAKpC,EAAUyE,GAASrC,KAAKnC,EAAUyD,GAAUtB,KAAKrB,EAAe0D,KAf1EE,EAAQM,SAASzE,GAAS0E,YAAYrE,GAAeqB,GAAG3B,EAAasB,EAAKsD,cAC1E/G,EAAKgH,WAAWpF,GAAUoF,WAAWnF,GAAUmC,KAAKrB,GAAe,GACnEc,EAAKiD,qBAAqBO,KAAK,QAAS,SAAUC,GAC9CA,EAAEC,iBACF1D,EAAK2D,MAAM,GACX3D,EAAK4D,SAASR,SAASrE,KAE3BiB,EAAKmD,uBAAuBK,KAAK,QAAS,SAAUC,GAChDA,EAAEC,iBACF1D,EAAK2D,UACL3D,EAAK6D,WAAWT,SAASrE,KAE7BiB,EAAKP,QAAQY,GAAG,UAAY9B,EAAIc,EAAMW,EAAK8D,SAAU9D,IAAOK,GAAG,WAAa9B,EAAIc,EAAMW,EAAK+D,UAAW/D,IAAOK,GAAG,QAAU9B,EAAIc,EAAMW,EAAKgE,OAAQhE,IAAOK,GAAG,QAAU9B,EAAIc,EAAMW,EAAKiE,OAAQjE,MAMpM6B,SAAU,SAAUA,GAChB5B,KAAK0C,WACDd,SAAUA,IAAazF,GAAmByF,EAC1Ce,SAAS,KAGjBhB,OAAQ,SAAUA,GACd3B,KAAK0C,WACDd,UAAU,EACVe,UAAWhB,EAASA,IAAWxF,GAAmBwF,MAG1DsC,WAAY,SAAUxE,GAClB,GAAIM,GAAOC,IACXzC,GAAO2C,GAAG+D,WAAW9D,KAAKJ,EAAMN,GAChCM,EAAKmE,YAAYC,OAAOpE,EAAKN,QAAQ6C,UACrCvC,EAAK+C,cAAcsB,YAAY,oBAAqBrE,EAAKN,QAAQ6C,UACjEvC,EAAKgB,MAAMsD,KAAK,cAAetE,EAAKN,QAAQ1B,aAC5CgC,EAAKuE,aAAavE,EAAKN,QAAQ1B,aAC/BgC,EAAKP,QAAQc,MACTiE,gBAAiBxE,EAAKN,QAAQC,MAAQP,EAAOY,EAAKN,QAAQC,IAAMK,EAAKN,QAAQ6B,OAASvB,EAAKN,QAAQC,IACnG8E,gBAAiBzE,EAAKN,QAAQE,MAAQR,EAAOY,EAAKN,QAAQE,IAAMI,EAAKN,QAAQ6B,OAASvB,EAAKN,QAAQE,MAEvGI,EAAKN,QAAQ8B,OAAS7D,EAAcqC,EAAKN,QAAQ8B,QAC7C9B,EAAQ/C,QAAUP,GAClB4D,EAAKrD,MAAM+C,EAAQ/C,QAG3B+H,QAAS,WACL,GAAI1E,GAAOC,IACXD,GAAKP,QAAQoD,IAAI7C,EAAKgB,OAAO6B,IAAI7C,EAAK4D,UAAUf,IAAI7C,EAAK6D,YAAYhB,IAAI7C,EAAK+C,eAAeC,IAAIzE,GACjGyB,EAAKiD,qBAAqByB,UAC1B1E,EAAKmD,uBAAuBuB,UACxB1E,EAAK2E,OACL3E,EAAK2E,MAAM3B,IAAI,QAAShD,EAAK4E,eAEjCpH,EAAO2C,GAAGuE,QAAQtE,KAAKJ,IAE3BL,IAAK,SAAUhD,GACX,MAAOsD,MAAK4E,QAAQ,MAAOlI,IAE/BiD,IAAK,SAAUjD,GACX,MAAOsD,MAAK4E,QAAQ,MAAOlI,IAE/BkD,KAAM,SAAUlD,GACZ,MAAOsD,MAAK4E,QAAQ,OAAQlI,IAEhCA,MAAO,SAAUA,GACb,GAAiBmI,GAAb9E,EAAOC,IACX,OAAItD,KAAUP,EACH4D,EAAK+E,QAEhBpI,EAAQqD,EAAKQ,OAAO7D,GACpBmI,EAAW9E,EAAKgF,QAAQrI,GACpBA,IAAUmI,IAGd9E,EAAKiF,QAAQtI,GACbqD,EAAKkF,KAAOlF,EAAK+E,QANjBpI,IAQJ0E,MAAO,WACHpB,KAAKkF,YAETH,QAAS,SAAUrI,GACf,GAAIqD,GAAOC,KAAMP,EAAUM,EAAKN,QAASC,EAAMD,EAAQC,IAAKC,EAAMF,EAAQE,GAC1E,OAAIjD,KAAUyC,EACHzC,GAEPgD,IAAQP,GAAQzC,EAAQgD,EACxBhD,EAAQgD,EACDC,IAAQR,GAAQzC,EAAQiD,IAC/BjD,EAAQiD,GAELjD,IAEXiE,QAAS,WACL,GAAiBwE,GAAbpF,EAAOC,KAAcoF,EAAW,WAC5BC,aAAatF,EAAKuF,WAClBH,EAAO/B,YAAYtE,IACpBW,EAAUM,EAAKN,QAAS6C,EAAW7C,EAAQ6C,SAAU9C,EAAUO,EAAKP,OAC3E2F,GAAS3F,EAAQ+F,SAAS,IAAM/I,GAC3B2I,EAAO,KACRA,EAASjJ,EAAEE,EAAW,WAAYqD,EAAQ8C,aAAenG,EAAW,WAAYqD,EAAQ+C,gBAAgBgD,YAAYhG,GACpHO,EAAKmE,YAAciB,EAAOM,QAAQ,4BAA4BC,UAE7DpD,IACD6C,EAAOO,SAASvB,OAAO7B,GACvBvC,EAAK+C,cAAcK,SAAS,qBAEhCpD,EAAK4D,SAAWwB,EAAOQ,GAAG,GAC1B5F,EAAKiD,qBAAuB,GAAI9F,GAAM0I,WAAW7F,EAAK4D,UAAYkC,QAAST,IAC3ErF,EAAK6D,WAAauB,EAAOQ,GAAG,GAC5B5F,EAAKmD,uBAAyB,GAAIhG,GAAM0I,WAAW7F,EAAK6D,YAAciC,QAAST,KAEnFxE,YAAa,WAAA,GACLb,GAAOC,KACPR,EAAUO,EAAKP,OACnBO,GAAK+F,gBAAkB5J,EAAE,gBAAmBM,EAAa,yBAA0BuJ,OAAOP,YAAYhG,IAE1GwG,MAAO,WACH,GAAIjG,GAAOC,IACXD,GAAKoB,aAAY,GACjBpB,EAAKkG,QAAQlG,EAAKP,QAAQgC,QAE9BH,OAAQ,SAAUmC,GACd,GAAIzD,GAAOC,IACXqF,cAAatF,EAAKmG,WAClBnG,EAAKmG,UAAYC,WAAW,WACxB,GAAqJC,GAAQC,EAAaC,EAAtKC,EAAQ/C,EAAEgD,OAAQC,EAAMrJ,EAAMmJ,GAAO,GAAI7J,EAAQ6J,EAAM7J,MAAMM,UAAU,EAAGyJ,GAAMlF,EAASxB,EAAK2G,QAAQ3G,EAAKN,QAAQ8B,QAASoF,EAAQpF,EAAO,KAA0CqF,EAAgB,CACrMD,KACAN,EAAkBQ,OAAO,KAAOF,EAAO,KACvCL,EAAoBO,OAAO,wBAA0BF,EAAQ,SAAWpF,EAAOxE,GAAS,eAExFuJ,IACAF,EAASE,EAAcQ,KAAKpK,IAE5B0J,IACAQ,EAAgBR,EAAO,GAAGW,QAAQV,EAAa,IAAIW,OAC/CtK,EAAMuK,QAAQ,UAAclH,EAAK+E,OAAS,GAC1C8B,KAGR7G,EAAKmF,WACL9H,EAAM2C,EAAKP,QAAQ,GAAIoH,MAG/BX,QAAS,SAAUvJ,GACf,GAAIqD,GAAOC,KAAMsB,EAASvB,EAAKN,QAAQ6B,MACnCA,IAAqB,IAAXA,IACV5E,EAAQQ,EAAML,WAAWH,GACX,OAAVA,IACAA,GAAgB4E,IAGxBvB,EAAKiF,QAAQtI,GACbA,EAAQqD,EAAK+E,OACT/E,EAAKkF,MAAQvI,IACbqD,EAAKkF,KAAOvI,EACPqD,EAAKmH,SACNnH,EAAKP,QAAQ2H,QAAQlJ,GAEzB8B,EAAKoH,QAAQlJ,IAEjB8B,EAAKmH,SAAU,GAEnBE,SAAU,SAAU/E,GAChB,MAAOA,IAAWrE,EAAWgC,KAAKP,QAAQ4C,UAE9C6C,SAAU,WACN,GAAInF,GAAOC,IACXD,GAAK+C,cAAcK,SAASxE,GAC5BoB,EAAKoB,aAAY,GACjBpB,EAAKP,QAAQ,GAAG4B,SAEpBf,UAAW,WACP,GAAIN,GAAOC,IACXqF,cAAatF,EAAKmG,WAClBnG,EAAK+C,cAAcM,YAAYzE,GAASyE,YAAYxE,GACpDmB,EAAKiG,QACLjG,EAAKsH,uBAETX,QAAS,SAAUnF,EAAQc,GACvB,GAAIiF,GAAetH,KAAKoH,SAAS/E,GAASiF,YAO1C,OANA/F,GAASA,EAAOgG,cACZhG,EAAO0F,QAAQ,QACfK,EAAeA,EAAaE,SACrBjG,EAAO0F,QAAQ,UACtBK,EAAeA,EAAaG,SAEzBH,GAEXzG,OAAQ,WACJ,GAAmLvE,GAA/KyD,EAAOC,KAAMP,EAAUM,EAAKN,QAASiI,EAAY,oBAAqBlI,EAAUO,EAAKP,QAAQ2D,SAAS/E,GAAOuJ,OAAO,GAAIC,EAAYpI,EAAQoI,UAAW/E,EAAU9C,EAAK8C,OAC1KvG,GAAOuG,EAAQgF,KAAK9K,EAAQ2K,GACvBpL,EAAK,KACNA,EAAOJ,EAAE,wBAAwB4L,aAAatI,GAAS2D,SAASuE,GAEpE,KACIlI,EAAQuI,aAAa,OAAQ,QAC/B,MAAOvE,GACLhE,EAAQwI,KAAO,OAEnB1L,EAAK,GAAG2L,MAAQzI,EAAQyI,MACxB3L,EAAK,GAAG4L,SAAW1I,EAAQ0I,SAC3B5L,EAAK,GAAG6L,MAAMC,QAAU5I,EAAQ2I,MAAMC,QACtC9L,EAAK+H,KAAK,cAAe5E,EAAQ1B,aAC7B6J,IACAtL,EAAKgE,KAAK,YAAasH,GACvBpI,EAAQoI,UAAY,IAExB7H,EAAKgB,MAAQzE,EAAK6G,SAAS3D,EAAQjD,WAAW+D,MAC1C+H,KAAQ,aACR9D,gBAAiB9E,EAAQC,MAAQP,EAAOM,EAAQC,IAAMD,EAAQ6B,OAAS7B,EAAQC,IAC/E8E,gBAAiB/E,EAAQE,MAAQR,EAAOM,EAAQE,IAAMF,EAAQ6B,OAAS7B,EAAQE,IAC/E2I,aAAgB,SAGxBzE,SAAU,SAAUL,GAChB,GAAIzD,GAAOC,KAAMuI,EAAM/E,EAAEgF,OACzBzI,GAAK0I,KAAOF,EACRA,GAAOlL,EAAKqL,KACZ3I,EAAK4I,UACEJ,GAAOlL,EAAKuL,GACnB7I,EAAK4I,MAAM,GACJJ,GAAOlL,EAAKwL,MACnB9I,EAAKkG,QAAQlG,EAAKP,QAAQgC,OACnB+G,GAAOlL,EAAKyL,MACnB/I,EAAKmH,SAAU,IAGvBpD,UAAW,SAAUN,GAAV,GAIHzD,GACAL,EACAF,EACAuJ,EACAC,EACAC,EACAC,EACA5B,EACA6B,EACAzM,EACA0M,CAbY,KAAZ5F,EAAE6F,OAAe7F,EAAE8F,SAAW9F,EAAE+F,SAAW/F,EAAEgF,UAAYnL,EAAKmM,WAAahG,EAAEgF,UAAYnL,EAAKwL,QAG9F9I,EAAOC,KACPN,EAAMK,EAAKN,QAAQC,IACnBF,EAAUO,EAAKP,QACfuJ,EAAY3L,EAAMoC,GAClBwJ,EAAiBD,EAAU,GAC3BE,EAAeF,EAAU,GACzBG,EAAYO,OAAOC,aAAalG,EAAE6F,OAClC/B,EAAevH,EAAK2G,QAAQ3G,EAAKN,QAAQ8B,QACzC4H,EAAkBpJ,EAAK0I,OAASpL,EAAKsM,WACrCjN,EAAQ8C,EAAQgC,MAEhB2H,IACAD,EAAY5B,EAAavK,IAE7BL,EAAQA,EAAMM,UAAU,EAAGgM,GAAkBE,EAAYxM,EAAMM,UAAUiM,GACzEG,EAAUrJ,EAAK6J,cAActC,GAAcuC,KAAKnN,GAC5C0M,GAAWD,GACX3J,EAAQgC,IAAI9E,GACZU,EAAMoC,EAASwJ,EAAiBE,EAAUlC,QAC1CxD,EAAEC,mBACa,OAAR/D,GAAgBA,GAAO,GAAyB,MAApBhD,EAAMoN,OAAO,KAAeV,KAC/DrJ,EAAKgK,mBACLvG,EAAEC,kBAEN1D,EAAK0I,KAAO,IAEhB1E,OAAQ,WACJ/D,KAAKqH,uBAET0C,iBAAkB,WACd,GAAIhK,GAAOC,IACXD,GAAK+C,cAAcK,SAASnE,GAC5Be,EAAK+F,gBAAgB6B,QAEzBN,oBAAqB,WACjB,GAAItH,GAAOC,IACXD,GAAK+C,cAAcM,YAAYpE,GAC/Be,EAAK+F,gBAAgBC,QAEzB6D,cAAe,SAAUtC,GAAV,GACPvH,GAAOC,KACPgK,EAAY1C,EAAavK,GACzBJ,EAAYoD,EAAKN,QAAQyC,SACzB+H,EAAe,GAOnB,OANID,KAAcjN,IACdiN,EAAY,KAAOA,GAEnBrN,IAAcwC,IACdxC,EAAY2K,EAAapF,UAEX,IAAdvF,GAAmBoD,EAAKN,QAAQ0C,iBACzBjD,GAEPa,EAAKN,QAAQ0C,mBACb8H,EAAe,MAAQtN,EAAY,KAEnCoD,EAAKmK,aAAeF,IACpBjK,EAAKmK,WAAaF,EAClBjK,EAAKoK,aAAmBtD,OAAO,gBAAkBmD,EAAY,MAAQC,EAAe,QAAUD,EAAY,MAAQC,EAAe,UAE9HlK,EAAKoK,eAEhBnG,OAAQ,SAAUR,GAAV,GACAzD,GAAOC,KACPR,EAAUgE,EAAEgD,OACZ9J,EAAQ8C,EAAQ9C,MAChB4K,EAAevH,EAAK2G,QAAQ3G,EAAKN,QAAQ8B,OAC7C4E,YAAW,WACP,GAAIC,GAASrG,EAAKQ,OAAOf,EAAQ9C,MAC7B0J,KAAWjH,EACXY,EAAKiF,QAAQtI,IAEb8C,EAAQ9C,OAAQ0J,GAAAA,GAAkBW,QAAQhK,EAAOuK,EAAavK,IAC1DgD,EAAKgF,QAAQqB,KAAYA,GAAWrG,EAAK6J,cAActC,GAAcuC,KAAKrK,EAAQ9C,QAClFqD,EAAKiF,QAAQtI,OAK7BkI,QAAS,SAAUwF,EAAQ1N,GACvB,GAAIqD,GAAOC,KAAMR,EAAUO,EAAKP,QAASC,EAAUM,EAAKN,OACxD,OAAI/C,KAAUP,EACHsD,EAAQ2K,IAEnB1N,EAAQqD,EAAKQ,OAAO7D,IACfA,GAAoB,SAAX0N,KAGd3K,EAAQ2K,GAAU1N,EAClB8C,EAAQoD,IAAI7C,EAAKgB,OAAOT,KAAK,aAAe8J,EAAQ1N,GACpD8C,EAAQc,KAAK8J,EAAQ1N,IANrBA,IAQJgH,MAAO,SAAU9D,EAAMyK,GACnB,GAAItK,GAAOC,IACXqK,GAAUA,GAAW,IACrBhF,aAAatF,EAAKuF,WAClBvF,EAAKuF,UAAYa,WAAW,WACxBpG,EAAK2D,MAAM9D,EAAM,KAClByK,GACHtK,EAAK4I,MAAM/I,IAEf+I,MAAO,SAAU/I,GACb,GAAIG,GAAOC,KAAMR,EAAUO,EAAKP,QAAS8K,EAAgBvK,EAAK+E,OAAQpI,EAAQqD,EAAKQ,OAAOf,EAAQgC,QAAU,EAAG7E,EAAYoD,EAAKN,QAAQyC,UAAY,CAChJ1E,MAAmBgC,EAAQ,IAC3BO,EAAKmF,WAELnF,EAAKN,QAAQ6B,QAAU5E,IACvBA,GAAgBqD,EAAKN,QAAQ6B,QAEjC5E,IAAUA,EAAQqD,EAAKN,QAAQG,KAAOA,GAAM2K,QAAQ5N,GACpDD,EAAQqD,EAAKgF,QAAQrI,GACrBqD,EAAKiF,QAAQtI,GACbqD,EAAKmH,SAAU,EACXoD,IAAkB5N,GAClBqD,EAAKoH,QAAQ9I,IAGrBgF,aAAc,SAAUG,GACpBtH,EAAEsH,EAAEgH,eAAepG,YAAYxF,EAAkB,eAAX4E,EAAEwE,OAE5C7G,YAAa,SAAUgD,GACnB,GAAIpE,GAAOC,IACXD,GAAKgB,MAAMoD,OAAOA,GAClBpE,EAAKP,QAAQ2E,QAAQA,IAEzB5D,OAAQ,SAAU7D,EAAO2F,GACrB,MAAOzE,GAAMlB,EAAOsD,KAAKoH,SAAS/E,GAAUrC,KAAKP,QAAQ8B,SAE7DkJ,OAAQ,SAAU/N,EAAOC,GACrB,GAAI+N,GAAU1K,KAAKP,QAAQ2C,MAAQlF,EAAMuN,OAAShO,CAClD,OAAOiO,GAAQhO,EAAOC,IAE1BqI,QAAS,SAAUtI,GACf,GAAiMiO,GAA7L5K,EAAOC,KAAMP,EAAUM,EAAKN,QAAS6B,EAAS7B,EAAQ6B,OAAQC,EAAS9B,EAAQ8B,OAAQW,EAAWzC,EAAQyC,SAAUG,EAAUtC,EAAKqH,WAAYE,EAAevH,EAAK2G,QAAQnF,EAAQc,EACnLH,KAAa/C,IACb+C,EAAWoF,EAAapF,UAE5BxF,EAAQqD,EAAKQ,OAAO7D,EAAO2F,GAC3BsI,EAAYjO,IAAUyC,EAClBwL,IACAjO,EAAQG,WAAWkD,EAAK0K,OAAO/N,EAAOwF,GAAW,KAErDnC,EAAK+E,OAASpI,EAAQqD,EAAKgF,QAAQrI,GACnCqD,EAAKuE,aAAapH,EAAM0N,SAASlO,EAAO6E,EAAQc,IAC5CsI,GACIrJ,IACA5E,EAAQG,WAAWkD,EAAK0K,OAAO/N,EAAQ4E,EAAQY,GAAW,KAE9DxF,EAAQA,GAAAA,EACJA,EAAMuK,QAAQ,YACdvK,EAAQqD,EAAK0K,QAAQ/N,EAAOwF,IAEhCxF,EAAQA,EAAMqK,QAAQhK,EAAOuK,EAAavK,KAE1CL,EAAQ,KAEZqD,EAAKP,QAAQgC,IAAI9E,GACjBqD,EAAKP,QAAQoD,IAAI7C,EAAKgB,OAAOT,KAAK,gBAAiB5D,IAEvD4H,aAAc,SAAU5H,GACpB,GAAI6J,GAAQvG,KAAKe,KACjBwF,GAAM/E,IAAI9E,GACLmB,GAAyBnB,GAC1B6J,EAAM/E,IAAIxB,KAAKP,QAAQ1B,aAE3BwI,EAAMjG,KAAK,QAASN,KAAKR,QAAQc,KAAK,UAAYiG,EAAM/E,QAE5Dd,SAAU,WACN,GAAkEmC,GAA9D9C,EAAOC,KAAMR,EAAUO,EAAKP,QAASqL,EAAarL,EAAQ,EAC9DqD,GAAUrD,EAAQkC,QAAQ,qBACrBmB,EAAQpB,GAAG,2BACZoB,EAAUrD,EAAQuG,OAAO+E,KAAK,mDAAmDpF,SACjF7C,EAAUA,EAAQiI,KAAK,WAAWpF,UAEtC7C,EAAQ,GAAGsF,MAAMC,QAAUyC,EAAW1C,MAAMC,QAC5CyC,EAAW1C,MAAM4C,MAAQ,GACzBhL,EAAK8C,QAAUA,EAAQM,SAAS,6BAA6BA,SAAS0H,EAAWtO,WAAWyO,IAAI,UAAW,IAC3GjL,EAAK+C,cAAgB5G,EAAE2G,EAAQ,GAAGoI,aAEtCxK,OAAQ,WACJ,GAAIV,GAAOC,KAAMR,EAAUO,EAAKP,QAAS0L,EAAS1L,EAAQc,KAAK,QAAS6K,EAAOD,EAAShP,EAAE,IAAMgP,GAAU1L,EAAQ4L,QAAQ,OACtHD,GAAK,KACLpL,EAAK4E,cAAgB,WACjBwB,WAAW,WACPpG,EAAKrD,MAAM8C,EAAQ,GAAG9C,OACtBqD,EAAKJ,IAAII,EAAKS,gBAAgBb,KAC9BI,EAAKL,IAAIK,EAAKS,gBAAgBd,QAGtCK,EAAK2E,MAAQyG,EAAK/K,GAAG,QAASL,EAAK4E,kBAe/CrH,GAAG+N,OAAO/L,IACZnC,OAAOD,MAAMoO,QACRnO,OAAOD,OACE,kBAAVjB,SAAwBA,OAAOsP,IAAMtP,OAAS,SAAUuP,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.numerictextbox.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.numerictextbox', [\n        'kendo.core',\n        'kendo.userevents'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'numerictextbox',\n        name: 'NumericTextBox',\n        category: 'web',\n        description: 'The NumericTextBox widget can format and display numeric, percentage or currency textbox.',\n        depends: [\n            'core',\n            'userevents'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, caret = kendo.caret, keys = kendo.keys, ui = kendo.ui, Widget = ui.Widget, activeElement = kendo._activeElement, extractFormat = kendo._extractFormat, parse = kendo.parseFloat, placeholderSupported = kendo.support.placeholder, getCulture = kendo.getCulture, CHANGE = 'change', DISABLED = 'disabled', READONLY = 'readonly', INPUT = 'k-input', SPIN = 'spin', ns = '.kendoNumericTextBox', TOUCHEND = 'touchend', MOUSELEAVE = 'mouseleave' + ns, HOVEREVENTS = 'mouseenter' + ns + ' ' + MOUSELEAVE, DEFAULT = 'k-state-default', FOCUSED = 'k-state-focused', HOVER = 'k-state-hover', FOCUS = 'focus', POINT = '.', CLASS_ICON = 'k-icon', SELECTED = 'k-state-selected', STATEDISABLED = 'k-state-disabled', STATE_INVALID = 'k-state-invalid', ARIA_DISABLED = 'aria-disabled', INTEGER_REGEXP = /^(-)?(\\d*)$/, NULL = null, proxy = $.proxy, extend = $.extend;\n        var NumericTextBox = Widget.extend({\n            init: function (element, options) {\n                var that = this, isStep = options && options.step !== undefined, min, max, step, value, disabled;\n                var inputType;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                element = that.element.on('focusout' + ns, proxy(that._focusout, that)).attr('role', 'spinbutton');\n                options.placeholder = options.placeholder || element.attr('placeholder');\n                min = that.min(element.attr('min'));\n                max = that.max(element.attr('max'));\n                step = that._parse(element.attr('step'));\n                if (options.min === NULL && min !== NULL) {\n                    options.min = min;\n                }\n                if (options.max === NULL && max !== NULL) {\n                    options.max = max;\n                }\n                if (!isStep && step !== NULL) {\n                    options.step = step;\n                }\n                that._initialOptions = extend({}, options);\n                inputType = element.attr('type');\n                that._reset();\n                that._wrapper();\n                that._arrows();\n                that._validation();\n                that._input();\n                if (!kendo.support.mobileOS) {\n                    that._text.on(FOCUS + ns, proxy(that._click, that));\n                } else {\n                    that._text.on(TOUCHEND + ns + ' ' + FOCUS + ns, function () {\n                        if (kendo.support.browser.edge) {\n                            that._text.one(FOCUS + ns, function () {\n                                that._toggleText(false);\n                                element.focus();\n                            });\n                        } else {\n                            that._toggleText(false);\n                            element.focus();\n                        }\n                    });\n                }\n                element.attr('aria-valuemin', options.min !== NULL ? options.min * options.factor : options.min).attr('aria-valuemax', options.max !== NULL ? options.max * options.factor : options.max);\n                options.format = extractFormat(options.format);\n                value = options.value;\n                if (value == NULL) {\n                    if (inputType == 'number') {\n                        value = parseFloat(element.val());\n                    } else {\n                        value = element.val();\n                    }\n                }\n                that.value(value);\n                disabled = element.is('[disabled]') || $(that.element).parents('fieldset').is(':disabled');\n                if (disabled) {\n                    that.enable(false);\n                } else {\n                    that.readonly(element.is('[readonly]'));\n                }\n                that.angular('compile', function () {\n                    return { elements: that._text.get() };\n                });\n                kendo.notify(that);\n            },\n            options: {\n                name: 'NumericTextBox',\n                decimals: NULL,\n                restrictDecimals: false,\n                min: NULL,\n                max: NULL,\n                value: NULL,\n                step: 1,\n                round: true,\n                culture: '',\n                format: 'n',\n                spinners: true,\n                placeholder: '',\n                factor: 1,\n                upArrowText: 'Increase value',\n                downArrowText: 'Decrease value'\n            },\n            events: [\n                CHANGE,\n                SPIN\n            ],\n            _editable: function (options) {\n                var that = this, element = that.element, disable = options.disable, readonly = options.readonly, text = that._text.add(element), wrapper = that._inputWrapper.off(HOVEREVENTS);\n                that._toggleText(true);\n                that._upArrowEventHandler.unbind('press');\n                that._downArrowEventHandler.unbind('press');\n                element.off('keydown' + ns).off('keypress' + ns).off('keyup' + ns).off('paste' + ns);\n                if (!readonly && !disable) {\n                    wrapper.addClass(DEFAULT).removeClass(STATEDISABLED).on(HOVEREVENTS, that._toggleHover);\n                    text.removeAttr(DISABLED).removeAttr(READONLY).attr(ARIA_DISABLED, false);\n                    that._upArrowEventHandler.bind('press', function (e) {\n                        e.preventDefault();\n                        that._spin(1);\n                        that._upArrow.addClass(SELECTED);\n                    });\n                    that._downArrowEventHandler.bind('press', function (e) {\n                        e.preventDefault();\n                        that._spin(-1);\n                        that._downArrow.addClass(SELECTED);\n                    });\n                    that.element.on('keydown' + ns, proxy(that._keydown, that)).on('keypress' + ns, proxy(that._keypress, that)).on('keyup' + ns, proxy(that._keyup, that)).on('paste' + ns, proxy(that._paste, that));\n                } else {\n                    wrapper.addClass(disable ? STATEDISABLED : DEFAULT).removeClass(disable ? DEFAULT : STATEDISABLED);\n                    text.attr(DISABLED, disable).attr(READONLY, readonly).attr(ARIA_DISABLED, disable);\n                }\n            },\n            readonly: function (readonly) {\n                this._editable({\n                    readonly: readonly === undefined ? true : readonly,\n                    disable: false\n                });\n            },\n            enable: function (enable) {\n                this._editable({\n                    readonly: false,\n                    disable: !(enable = enable === undefined ? true : enable)\n                });\n            },\n            setOptions: function (options) {\n                var that = this;\n                Widget.fn.setOptions.call(that, options);\n                that._arrowsWrap.toggle(that.options.spinners);\n                that._inputWrapper.toggleClass('k-expand-padding', !that.options.spinners);\n                that._text.prop('placeholder', that.options.placeholder);\n                that._placeholder(that.options.placeholder);\n                that.element.attr({\n                    'aria-valuemin': that.options.min !== NULL ? that.options.min * that.options.factor : that.options.min,\n                    'aria-valuemax': that.options.max !== NULL ? that.options.max * that.options.factor : that.options.max\n                });\n                that.options.format = extractFormat(that.options.format);\n                if (options.value !== undefined) {\n                    that.value(options.value);\n                }\n            },\n            destroy: function () {\n                var that = this;\n                that.element.add(that._text).add(that._upArrow).add(that._downArrow).add(that._inputWrapper).off(ns);\n                that._upArrowEventHandler.destroy();\n                that._downArrowEventHandler.destroy();\n                if (that._form) {\n                    that._form.off('reset', that._resetHandler);\n                }\n                Widget.fn.destroy.call(that);\n            },\n            min: function (value) {\n                return this._option('min', value);\n            },\n            max: function (value) {\n                return this._option('max', value);\n            },\n            step: function (value) {\n                return this._option('step', value);\n            },\n            value: function (value) {\n                var that = this, adjusted;\n                if (value === undefined) {\n                    return that._value;\n                }\n                value = that._parse(value);\n                adjusted = that._adjust(value);\n                if (value !== adjusted) {\n                    return;\n                }\n                that._update(value);\n                that._old = that._value;\n            },\n            focus: function () {\n                this._focusin();\n            },\n            _adjust: function (value) {\n                var that = this, options = that.options, min = options.min, max = options.max;\n                if (value === NULL) {\n                    return value;\n                }\n                if (min !== NULL && value < min) {\n                    value = min;\n                } else if (max !== NULL && value > max) {\n                    value = max;\n                }\n                return value;\n            },\n            _arrows: function () {\n                var that = this, arrows, _release = function () {\n                        clearTimeout(that._spinning);\n                        arrows.removeClass(SELECTED);\n                    }, options = that.options, spinners = options.spinners, element = that.element;\n                arrows = element.siblings('.' + CLASS_ICON);\n                if (!arrows[0]) {\n                    arrows = $(buttonHtml('increase', options.upArrowText) + buttonHtml('decrease', options.downArrowText)).insertAfter(element);\n                    that._arrowsWrap = arrows.wrapAll('<span class=\"k-select\"/>').parent();\n                }\n                if (!spinners) {\n                    arrows.parent().toggle(spinners);\n                    that._inputWrapper.addClass('k-expand-padding');\n                }\n                that._upArrow = arrows.eq(0);\n                that._upArrowEventHandler = new kendo.UserEvents(that._upArrow, { release: _release });\n                that._downArrow = arrows.eq(1);\n                that._downArrowEventHandler = new kendo.UserEvents(that._downArrow, { release: _release });\n            },\n            _validation: function () {\n                var that = this;\n                var element = that.element;\n                that._validationIcon = $('<span class=\\'' + CLASS_ICON + ' k-i-warning\\'></span>').hide().insertAfter(element);\n            },\n            _blur: function () {\n                var that = this;\n                that._toggleText(true);\n                that._change(that.element.val());\n            },\n            _click: function (e) {\n                var that = this;\n                clearTimeout(that._focusing);\n                that._focusing = setTimeout(function () {\n                    var input = e.target, idx = caret(input)[0], value = input.value.substring(0, idx), format = that._format(that.options.format), group = format[','], result, groupRegExp, extractRegExp, caretPosition = 0;\n                    if (group) {\n                        groupRegExp = new RegExp('\\\\' + group, 'g');\n                        extractRegExp = new RegExp('(^(-)$)|(^(-)?([\\\\d\\\\' + group + ']+)(\\\\' + format[POINT] + ')?(\\\\d+)?)');\n                    }\n                    if (extractRegExp) {\n                        result = extractRegExp.exec(value);\n                    }\n                    if (result) {\n                        caretPosition = result[0].replace(groupRegExp, '').length;\n                        if (value.indexOf('(') != -1 && that._value < 0) {\n                            caretPosition++;\n                        }\n                    }\n                    that._focusin();\n                    caret(that.element[0], caretPosition);\n                });\n            },\n            _change: function (value) {\n                var that = this, factor = that.options.factor;\n                if (factor && factor !== 1) {\n                    value = kendo.parseFloat(value);\n                    if (value !== null) {\n                        value = value / factor;\n                    }\n                }\n                that._update(value);\n                value = that._value;\n                if (that._old != value) {\n                    that._old = value;\n                    if (!that._typing) {\n                        that.element.trigger(CHANGE);\n                    }\n                    that.trigger(CHANGE);\n                }\n                that._typing = false;\n            },\n            _culture: function (culture) {\n                return culture || getCulture(this.options.culture);\n            },\n            _focusin: function () {\n                var that = this;\n                that._inputWrapper.addClass(FOCUSED);\n                that._toggleText(false);\n                that.element[0].focus();\n            },\n            _focusout: function () {\n                var that = this;\n                clearTimeout(that._focusing);\n                that._inputWrapper.removeClass(FOCUSED).removeClass(HOVER);\n                that._blur();\n                that._removeInvalidState();\n            },\n            _format: function (format, culture) {\n                var numberFormat = this._culture(culture).numberFormat;\n                format = format.toLowerCase();\n                if (format.indexOf('c') > -1) {\n                    numberFormat = numberFormat.currency;\n                } else if (format.indexOf('p') > -1) {\n                    numberFormat = numberFormat.percent;\n                }\n                return numberFormat;\n            },\n            _input: function () {\n                var that = this, options = that.options, CLASSNAME = 'k-formatted-value', element = that.element.addClass(INPUT).show()[0], accessKey = element.accessKey, wrapper = that.wrapper, text;\n                text = wrapper.find(POINT + CLASSNAME);\n                if (!text[0]) {\n                    text = $('<input type=\"text\"/>').insertBefore(element).addClass(CLASSNAME);\n                }\n                try {\n                    element.setAttribute('type', 'text');\n                } catch (e) {\n                    element.type = 'text';\n                }\n                text[0].title = element.title;\n                text[0].tabIndex = element.tabIndex;\n                text[0].style.cssText = element.style.cssText;\n                text.prop('placeholder', options.placeholder);\n                if (accessKey) {\n                    text.attr('accesskey', accessKey);\n                    element.accessKey = '';\n                }\n                that._text = text.addClass(element.className).attr({\n                    'role': 'spinbutton',\n                    'aria-valuemin': options.min !== NULL ? options.min * options.factor : options.min,\n                    'aria-valuemax': options.max !== NULL ? options.max * options.factor : options.max,\n                    'autocomplete': 'off'\n                });\n            },\n            _keydown: function (e) {\n                var that = this, key = e.keyCode;\n                that._key = key;\n                if (key == keys.DOWN) {\n                    that._step(-1);\n                } else if (key == keys.UP) {\n                    that._step(1);\n                } else if (key == keys.ENTER) {\n                    that._change(that.element.val());\n                } else if (key != keys.TAB) {\n                    that._typing = true;\n                }\n            },\n            _keypress: function (e) {\n                if (e.which === 0 || e.metaKey || e.ctrlKey || e.keyCode === keys.BACKSPACE || e.keyCode === keys.ENTER) {\n                    return;\n                }\n                var that = this;\n                var min = that.options.min;\n                var element = that.element;\n                var selection = caret(element);\n                var selectionStart = selection[0];\n                var selectionEnd = selection[1];\n                var character = String.fromCharCode(e.which);\n                var numberFormat = that._format(that.options.format);\n                var isNumPadDecimal = that._key === keys.NUMPAD_DOT;\n                var value = element.val();\n                var isValid;\n                if (isNumPadDecimal) {\n                    character = numberFormat[POINT];\n                }\n                value = value.substring(0, selectionStart) + character + value.substring(selectionEnd);\n                isValid = that._numericRegex(numberFormat).test(value);\n                if (isValid && isNumPadDecimal) {\n                    element.val(value);\n                    caret(element, selectionStart + character.length);\n                    e.preventDefault();\n                } else if (min !== null && min >= 0 && value.charAt(0) === '-' || !isValid) {\n                    that._addInvalidState();\n                    e.preventDefault();\n                }\n                that._key = 0;\n            },\n            _keyup: function () {\n                this._removeInvalidState();\n            },\n            _addInvalidState: function () {\n                var that = this;\n                that._inputWrapper.addClass(STATE_INVALID);\n                that._validationIcon.show();\n            },\n            _removeInvalidState: function () {\n                var that = this;\n                that._inputWrapper.removeClass(STATE_INVALID);\n                that._validationIcon.hide();\n            },\n            _numericRegex: function (numberFormat) {\n                var that = this;\n                var separator = numberFormat[POINT];\n                var precision = that.options.decimals;\n                var fractionRule = '*';\n                if (separator === POINT) {\n                    separator = '\\\\' + separator;\n                }\n                if (precision === NULL) {\n                    precision = numberFormat.decimals;\n                }\n                if (precision === 0 && that.options.restrictDecimals) {\n                    return INTEGER_REGEXP;\n                }\n                if (that.options.restrictDecimals) {\n                    fractionRule = '{0,' + precision + '}';\n                }\n                if (that._separator !== separator) {\n                    that._separator = separator;\n                    that._floatRegExp = new RegExp('^(-)?(((\\\\d+(' + separator + '\\\\d' + fractionRule + ')?)|(' + separator + '\\\\d' + fractionRule + ')))?$');\n                }\n                return that._floatRegExp;\n            },\n            _paste: function (e) {\n                var that = this;\n                var element = e.target;\n                var value = element.value;\n                var numberFormat = that._format(that.options.format);\n                setTimeout(function () {\n                    var result = that._parse(element.value);\n                    if (result === NULL) {\n                        that._update(value);\n                    } else {\n                        element.value = result.toString().replace(POINT, numberFormat[POINT]);\n                        if (that._adjust(result) !== result || !that._numericRegex(numberFormat).test(element.value)) {\n                            that._update(value);\n                        }\n                    }\n                });\n            },\n            _option: function (option, value) {\n                var that = this, element = that.element, options = that.options;\n                if (value === undefined) {\n                    return options[option];\n                }\n                value = that._parse(value);\n                if (!value && option === 'step') {\n                    return;\n                }\n                options[option] = value;\n                element.add(that._text).attr('aria-value' + option, value);\n                element.attr(option, value);\n            },\n            _spin: function (step, timeout) {\n                var that = this;\n                timeout = timeout || 500;\n                clearTimeout(that._spinning);\n                that._spinning = setTimeout(function () {\n                    that._spin(step, 50);\n                }, timeout);\n                that._step(step);\n            },\n            _step: function (step) {\n                var that = this, element = that.element, originalValue = that._value, value = that._parse(element.val()) || 0, precision = that.options.decimals || 2;\n                if (activeElement() != element[0]) {\n                    that._focusin();\n                }\n                if (that.options.factor && value) {\n                    value = value / that.options.factor;\n                }\n                value = +(value + that.options.step * step).toFixed(precision);\n                value = that._adjust(value);\n                that._update(value);\n                that._typing = false;\n                if (originalValue !== value) {\n                    that.trigger(SPIN);\n                }\n            },\n            _toggleHover: function (e) {\n                $(e.currentTarget).toggleClass(HOVER, e.type === 'mouseenter');\n            },\n            _toggleText: function (toggle) {\n                var that = this;\n                that._text.toggle(toggle);\n                that.element.toggle(!toggle);\n            },\n            _parse: function (value, culture) {\n                return parse(value, this._culture(culture), this.options.format);\n            },\n            _round: function (value, precision) {\n                var rounder = this.options.round ? kendo._round : truncate;\n                return rounder(value, precision);\n            },\n            _update: function (value) {\n                var that = this, options = that.options, factor = options.factor, format = options.format, decimals = options.decimals, culture = that._culture(), numberFormat = that._format(format, culture), isNotNull;\n                if (decimals === NULL) {\n                    decimals = numberFormat.decimals;\n                }\n                value = that._parse(value, culture);\n                isNotNull = value !== NULL;\n                if (isNotNull) {\n                    value = parseFloat(that._round(value, decimals), 10);\n                }\n                that._value = value = that._adjust(value);\n                that._placeholder(kendo.toString(value, format, culture));\n                if (isNotNull) {\n                    if (factor) {\n                        value = parseFloat(that._round(value * factor, decimals), 10);\n                    }\n                    value = value.toString();\n                    if (value.indexOf('e') !== -1) {\n                        value = that._round(+value, decimals);\n                    }\n                    value = value.replace(POINT, numberFormat[POINT]);\n                } else {\n                    value = null;\n                }\n                that.element.val(value);\n                that.element.add(that._text).attr('aria-valuenow', value);\n            },\n            _placeholder: function (value) {\n                var input = this._text;\n                input.val(value);\n                if (!placeholderSupported && !value) {\n                    input.val(this.options.placeholder);\n                }\n                input.attr('title', this.element.attr('title') || input.val());\n            },\n            _wrapper: function () {\n                var that = this, element = that.element, DOMElement = element[0], wrapper;\n                wrapper = element.parents('.k-numerictextbox');\n                if (!wrapper.is('span.k-numerictextbox')) {\n                    wrapper = element.hide().wrap('<span class=\"k-numeric-wrap k-state-default\" />').parent();\n                    wrapper = wrapper.wrap('<span/>').parent();\n                }\n                wrapper[0].style.cssText = DOMElement.style.cssText;\n                DOMElement.style.width = '';\n                that.wrapper = wrapper.addClass('k-widget k-numerictextbox').addClass(DOMElement.className).css('display', '');\n                that._inputWrapper = $(wrapper[0].firstChild);\n            },\n            _reset: function () {\n                var that = this, element = that.element, formId = element.attr('form'), form = formId ? $('#' + formId) : element.closest('form');\n                if (form[0]) {\n                    that._resetHandler = function () {\n                        setTimeout(function () {\n                            that.value(element[0].value);\n                            that.max(that._initialOptions.max);\n                            that.min(that._initialOptions.min);\n                        });\n                    };\n                    that._form = form.on('reset', that._resetHandler);\n                }\n            }\n        });\n        function buttonHtml(direction, text) {\n            var className = 'k-i-arrow-' + (direction === 'increase' ? '60-up' : '60-down');\n            return '<span unselectable=\"on\" class=\"k-link k-link-' + direction + '\" aria-label=\"' + text + '\" title=\"' + text + '\">' + '<span unselectable=\"on\" class=\"' + CLASS_ICON + ' ' + className + '\"></span>' + '</span>';\n        }\n        function truncate(value, precision) {\n            var parts = parseFloat(value, 10).toString().split(POINT);\n            if (parts[1]) {\n                parts[1] = parts[1].substring(0, precision);\n            }\n            return parts.join(POINT);\n        }\n        ui.plugin(NumericTextBox);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}