{"version": 3, "sources": ["kendo.dataviz.treemap.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "undefined", "getField", "field", "row", "getter", "defined", "UNDEFINED", "colorsByLength", "min", "max", "rgbColor", "minRGBtoDecimal", "rgbToDecimal", "maxRGBtoDecimal", "<PERSON><PERSON><PERSON><PERSON>", "colorBrightness", "colors", "r", "colorByIndex", "g", "b", "buildColorFromRGB", "index", "color", "minColor", "math", "abs", "maxColor", "step", "currentStep", "decimalToRgb", "colorToRGB", "rgbToHex", "number", "result", "round", "toString", "toUpperCase", "colorLength", "substring", "rgb", "parseInt", "brightness", "sqrt", "power", "pow", "Math", "proxy", "isArray", "outerHeight", "_outerHeight", "outerWidth", "_outerWidth", "Widget", "ui", "template", "HierarchicalDataSource", "data", "dataviz", "NS", "CHANGE", "DATA_BOUND", "ITEM_CREATED", "MAX_VALUE", "Number", "MOUSEOVER_NS", "MOUSELEAVE_NS", "TreeMap", "element", "destroy", "empty", "fn", "call", "wrapper", "_initTheme", "addClass", "_setLayout", "_originalOptions", "_initDataSource", "_attachEvents", "notify", "name", "theme", "autoBind", "textField", "valueField", "colorField", "events", "that", "themes", "themeName", "toLowerCase", "themeOptions", "treeMap", "on", "_mouseover", "_mouseleave", "_resizeHandler", "resize", "onResize", "type", "_layout", "SliceAndDice", "_view", "SliceAndDiceView", "Squarified", "SquarifiedView", "dataSource", "_dataChangeHandler", "_onDataChange", "create", "bind", "fetch", "setDataSource", "unbind", "e", "item", "root", "htmlSize", "node", "items", "_getByUid", "uid", "children", "Query", "_sortForGrouping", "_wrapItem", "compute", "coord", "_setColors", "render", "_cleanItems", "createRoot", "_root", "_colorIdx", "load", "trigger", "angular", "elements", "find", "colorRange", "leafNodes", "colorIdx", "dataItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_contentSize", "view", "renderHeight", "wrap", "level", "pop", "concat", "attr", "getByUid", "findByUid", "target", "hasClass", "_removeActiveState", "removeClass", "off", "unbindResize", "getSize", "dimensions", "_resize", "rootElement", "css", "_resizeItems", "child", "childElement", "idx", "titleSize", "filter", "setItemSize", "setOptions", "_setEvents", "top", "left", "leaf", "tree", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minimumSideValue", "firstElement", "tail", "parentArea", "totalArea", "itemsArea", "parseFloat", "area", "layoutHorizontal", "slice", "squarify", "initElement", "computeDim", "newCoords", "layoutLast", "layoutRow", "worstAspectRatio", "dim", "w", "layoutV", "layoutH", "orientation", "layoutVertical", "layoutChange", "areaSum", "maxArea", "minArea", "rootCoord", "newRootCoord", "ans", "_totalArea", "total", "offset", "title", "_clean", "_getText", "_createTitle", "append", "_compile", "_createWrap", "innerWidth", "rootWrap", "htmlElement", "_createLeaf", "_createTile", "toggleClass", "_tileColorBrightness", "toggle", "html", "tile", "_itemCoordinates", "coordinates", "_renderTemplate", "titleTemplate", "vertical", "quotient", "sliceAndDice", "plugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,yBACH,aACA,mBACA,wBACDD,IACL,WAswBE,MA1vBC,UAAUE,EAAGkF,GAgqBV,QAASC,GAASC,EAAOC,GACrB,GAAY,OAARA,EACA,MAAOA,EAEX,IAAIjC,GAAMkC,EAAOF,GAAO,EACxB,OAAOhC,GAAIiC,GAEf,QAASE,GAAQ1C,GACb,aAAcA,KAAU2C,EAE5B,QAASC,GAAeC,EAAKC,EAAKzE,GAAlC,GAMaF,GACD4E,EANJC,EAAkBC,EAAaJ,GAC/BK,EAAkBD,EAAaH,GAC/BK,EAAWC,EAAgBP,GAAOO,EAAgBN,GAAO,EACzDO,IAEJ,KADAA,EAAOvF,KAAK+E,GACH1E,EAAI,EAAGA,EAAIE,EAAQF,IACpB4E,GACAO,EAAGC,EAAaP,EAAgBM,EAAGJ,EAAgBI,EAAGnF,EAAGE,EAAQ8E,GACjEK,EAAGD,EAAaP,EAAgBQ,EAAGN,EAAgBM,EAAGrF,EAAGE,EAAQ8E,GACjEM,EAAGF,EAAaP,EAAgBS,EAAGP,EAAgBO,EAAGtF,EAAGE,EAAQ8E,IAErEE,EAAOvF,KAAK4F,EAAkBX,GAGlC,OADAM,GAAOvF,KAAKgF,GACLO,EAEX,QAASE,GAAaV,EAAKC,EAAKa,EAAOtF,EAAQ8E,GAA/C,GAKQS,GAJAC,EAAWC,EAAKjB,IAAIiB,EAAKC,IAAIlB,GAAMiB,EAAKC,IAAIjB,IAC5CkB,EAAWF,EAAKhB,IAAIgB,EAAKC,IAAIlB,GAAMiB,EAAKC,IAAIjB,IAC5CmB,GAAQD,EAAWH,IAAaxF,EAAS,GACzC6F,EAAcD,GAAQN,EAAQ,EAOlC,OAJIC,GADAT,EACQU,EAAWK,EAEXF,EAAWE,EAI3B,QAASR,GAAkBE,GACvB,MAAO,IAAMO,EAAaP,EAAMN,GAAKa,EAAaP,EAAMJ,GAAKW,EAAaP,EAAMH,GAEpF,QAASR,GAAaW,GAClBA,EAAQA,EAAMrG,QAAQ,IAAK,GAC3B,IAAIwF,GAAWqB,EAAWR,EAC1B,QACIN,EAAGe,EAAStB,EAASO,GACrBE,EAAGa,EAAStB,EAASS,GACrBC,EAAGY,EAAStB,EAASU,IAG7B,QAASU,GAAaG,GAClB,GAAIC,GAAST,EAAKU,MAAMF,GAAQG,SAAS,IAAIC,aAI7C,OAHsB,KAAlBH,EAAOlG,SACPkG,EAAS,IAAMA,GAEZA,EAEX,QAASH,GAAWR,GAApB,GACQe,GAAcf,EAAMvF,OACpB0E,IAUJ,OAToB,KAAhB4B,GACA5B,EAASO,EAAIM,EAAM,GACnBb,EAASS,EAAII,EAAM,GACnBb,EAASU,EAAIG,EAAM,KAEnBb,EAASO,EAAIM,EAAMgB,UAAU,EAAG,GAChC7B,EAASS,EAAII,EAAMgB,UAAU,EAAG,GAChC7B,EAASU,EAAIG,EAAMgB,UAAU,EAAG,IAE7B7B,EAEX,QAASsB,GAASQ,GACd,MAAOC,UAASD,EAAIJ,SAAS,IAAK,IAEtC,QAASrB,GAAgBQ,GACrB,GAAImB,GAAa,CAKjB,OAJInB,KACAA,EAAQX,EAAaW,GACrBmB,EAAajB,EAAKkB,KAAK,KAAQpB,EAAMN,EAAIM,EAAMN,EAAI,KAAQM,EAAMJ,EAAII,EAAMJ,EAAI,KAAQI,EAAMH,EAAIG,EAAMH,IAEpGsB,EAEX,QAASP,GAAMxE,GACX,GAAIiF,GAAQnB,EAAKoB,IAAI,GAAI,EACzB,OAAOpB,GAAKU,MAAMxE,EAAQiF,GAASA,EAtvB1C,GACOnB,GAAOqB,KAAMC,EAAQjI,EAAEiI,MAAOC,EAAUlI,EAAEkI,QAAShG,EAAQD,OAAOC,MAAOiG,EAAcjG,EAAMkG,aAAcC,EAAanG,EAAMoG,YAAalG,EAAQF,EAAME,MAAOmG,EAASrG,EAAMsG,GAAGD,OAAQE,EAAWvG,EAAMuG,SAAU7D,EAAa1C,EAAM0C,WAAY8D,EAAyBxG,EAAMyG,KAAKD,uBAAwBpD,EAASpD,EAAMoD,OAAQsD,EAAU1G,EAAM0G,QACvVC,EAAK,gBAAiBC,EAAS,SAAUC,EAAa,YAAaC,EAAe,cAAeC,EAAYC,OAAOD,UAAWE,EAAe,YAAcN,EAAIO,EAAgB,aAAeP,EAAIrD,EAAY,YAC/M6D,EAAUd,EAAOlG,QACjBC,KAAM,SAAUgH,EAAS7F,GACrBvB,EAAMqH,QAAQD,GACdtJ,EAAEsJ,GAASE,QACXjB,EAAOkB,GAAGnH,KAAKoH,KAAKlH,KAAM8G,EAAS7F,GACnCjB,KAAKmH,QAAUnH,KAAK8G,QACpB9G,KAAKoH,WAAWpH,KAAKiB,SACrBjB,KAAK8G,QAAQO,SAAS,sBACtBrH,KAAKsH,aACLtH,KAAKuH,iBAAmBnF,KAAepC,KAAKiB,SAC5CjB,KAAKwH,kBACLxH,KAAKyH,gBACL/H,EAAMgI,OAAO1H,KAAMoG,EAAQJ,KAE/B/E,SACI0G,KAAM,UACNC,MAAO,UACPC,UAAU,EACVC,UAAW,OACXC,WAAY,QACZC,WAAY,SAEhBC,QACI1B,EACAC,GAEJY,WAAY,SAAUnG,GAClB,GAAIiH,GAAOlI,KAAMmI,EAAS/B,EAAQJ,GAAGmC,WAAcC,IAAcnH,OAAe2G,OAAS,IAAIS,cAAeC,GAAgBH,EAAOC,QAAkBG,OACrJL,GAAKjH,QAAUmB,KAAekG,EAAcrH,IAEhDwG,cAAe,WACXzH,KAAK8G,QAAQ0B,GAAG7B,EAAclB,EAAMzF,KAAKyI,WAAYzI,OAAOwI,GAAG5B,EAAenB,EAAMzF,KAAK0I,YAAa1I,OACtGA,KAAK2I,eAAiBlD,EAAMzF,KAAK4I,OAAQ5I,MAAM,GAC/CN,EAAMmJ,SAAS7I,KAAK2I,iBAExBrB,WAAY,WACkB,eAAtBtH,KAAKiB,QAAQ6H,MACb9I,KAAK+I,QAAU,GAAIC,KAAa,IAChChJ,KAAKiJ,MAAQ,GAAIC,GAAiBlJ,KAAMA,KAAKiB,UAChB,aAAtBjB,KAAKiB,QAAQ6H,MACpB9I,KAAK+I,QAAU,GAAIC,KAAa,IAChChJ,KAAKiJ,MAAQ,GAAIC,GAAiBlJ,KAAMA,KAAKiB,WAE7CjB,KAAK+I,QAAU,GAAII,GACnBnJ,KAAKiJ,MAAQ,GAAIG,GAAepJ,KAAMA,KAAKiB,WAGnDuG,gBAAiB,WACb,GAAIU,GAAOlI,KAAMiB,EAAUiH,EAAKjH,QAASoI,EAAapI,EAAQoI,UAC9DnB,GAAKoB,mBAAqB7D,EAAMyC,EAAKqB,cAAerB,GACpDA,EAAKmB,WAAanD,EAAuBsD,OAAOH,GAAYI,KAAKnD,EAAQ4B,EAAKoB,oBAC1ED,GACInB,EAAKjH,QAAQ4G,UACbK,EAAKmB,WAAWK,SAI5BC,cAAe,SAAUN,GACrB,GAAInB,GAAOlI,IACXkI,GAAKmB,WAAWO,OAAOtD,EAAQ4B,EAAKoB,oBACpCpB,EAAKmB,WAAaA,EAAWI,KAAKnD,EAAQ4B,EAAKoB,oBAC3CD,GACInB,EAAKjH,QAAQ4G,UACbK,EAAKmB,WAAWK,SAI5BH,cAAe,SAAUM,GAAV,GAIPC,GAAMtL,EAWEuL,EAOAC,EArBRC,EAAOJ,EAAEI,KACTC,EAAQL,EAAEK,MACVjJ,EAAUjB,KAAKiB,OAEnB,IAAKgJ,GASD,GAAIC,EAAMxL,OAAQ,CAId,IAHIqL,EAAO/J,KAAKmK,UAAUF,EAAKG,KAC/BL,EAAKM,YACLH,EAAQ,GAAIxK,GAAMyG,KAAKmE,MAAMJ,GAAOK,iBAAiBtJ,EAAQ8G,WAAY,QACpEvJ,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IAC1BsL,EAAOI,EAAM1L,GACbuL,EAAKM,SAASlM,KAAK6B,KAAKwK,UAAUV,GAElCE,GAAWhK,KAAKiJ,MAAMe,SAASD,GACnC/J,KAAK+I,QAAQ0B,QAAQV,EAAKM,SAAUN,EAAKW,MAAOV,GAChDhK,KAAK2K,WAAWZ,EAAKM,UACrBrK,KAAKiJ,MAAM2B,OAAOb,QAnBtB/J,MAAK6K,cACL7K,KAAK8G,QAAQE,QACb8C,EAAO9J,KAAKwK,UAAUN,EAAM,IAC5BlK,KAAK+I,QAAQ+B,WAAWhB,EAAMjE,EAAW7F,KAAK8G,SAAUnB,EAAY3F,KAAK8G,SAAgC,aAAtB9G,KAAKiB,QAAQ6H,MAChG9I,KAAKiJ,MAAM6B,WAAWhB,GACtB9J,KAAK+K,MAAQjB,EACb9J,KAAKgL,UAAY,CAgBrB,KAAKxM,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IAC1B0L,EAAM1L,GAAGyM,MAEThB,IACAjK,KAAKkL,QAAQ3E,GAAc0D,KAAMA,KAGzCY,YAAa,WACT,GAAI3C,GAAOlI,IACXkI,GAAKiD,QAAQ,UAAW,WACpB,OAASC,SAAUlD,EAAKpB,QAAQuE,KAAK,8DAG7CV,WAAY,SAAUT,GAAV,GAIJoB,GAAYxB,EAIZyB,EACK/M,EARLkF,EAAS1D,KAAKiB,QAAQyC,OACtB8H,EAAWxL,KAAKgL,UAChB/G,EAAQP,EAAO8H,EAAW9H,EAAOhF,OAMrC,KAJIgH,EAAQzB,KACRqH,EAAarI,EAAegB,EAAM,GAAIA,EAAM,GAAIiG,EAAMxL,SAEtD6M,GAAY,EACP/M,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IAC9BsL,EAAOI,EAAM1L,GACRuE,EAAQ+G,EAAK7F,SAEV6F,EAAK7F,MADLqH,EACaA,EAAW9M,GAEXyF,GAGhB6F,EAAK2B,SAASC,cACfH,GAAY,EAGhBA,IACAvL,KAAKgL,aAGbW,aAAc,SAAU5B,GACpB/J,KAAK4L,KAAKC,aAAa9B,IAE3BS,UAAW,SAAUV,GACjB,GAAIgC,KAYJ,OAXI/I,GAAQ/C,KAAKiB,QAAQ8G,cACrB+D,EAAKzL,MAAQsC,EAAS3C,KAAKiB,QAAQ8G,WAAY+B,IAE/C/G,EAAQ/C,KAAKiB,QAAQ+G,cACrB8D,EAAK7H,MAAQtB,EAAS3C,KAAKiB,QAAQ+G,WAAY8B,IAE/C/G,EAAQ/C,KAAKiB,QAAQ6G,aACrBgE,EAAKpO,KAAOiF,EAAS3C,KAAKiB,QAAQ6G,UAAWgC,IAEjDgC,EAAKC,MAAQjC,EAAKiC,QAClBD,EAAKL,SAAW3B,EACTgC,GAEX3B,UAAW,SAAUC,GAGjB,IAHO,GAEHN,GADAI,GAASlK,KAAK+K,OAEXb,EAAMxL,QAAQ,CAEjB,GADAoL,EAAOI,EAAM8B,MACTlC,EAAK2B,SAASrB,MAAQA,EACtB,MAAON,EAEPA,GAAKO,WACLH,EAAQA,EAAM+B,OAAOnC,EAAKO,aAItCoB,SAAU,SAAUxB,GAChB,GAAIG,GAAM5M,EAAEyM,GAAMiC,KAAKxM,EAAMwM,KAAK,QAAS7C,EAAarJ,KAAKqJ,UAC7D,OAAOA,IAAcA,EAAW8C,SAAS/B,IAE7CgC,UAAW,SAAUhC,GACjB,MAAOpK,MAAK8G,QAAQuE,KAAK,mBAAqB3L,EAAMwM,KAAK,OAAS,KAAQ9B,EAAM,OAEpF3B,WAAY,SAAUoB,GAClB,GAAIwC,GAAS7O,EAAEqM,EAAEwC,OACbA,GAAOC,SAAS,YAChBtM,KAAKuM,qBACLF,EAAOG,YAAY,iBAAiBnF,SAAS,mBAGrDkF,mBAAoB,WAChBvM,KAAK8G,QAAQuE,KAAK,kBAAkBmB,YAAY,kBAEpD9D,YAAa,WACT1I,KAAKuM,sBAETxF,QAAS,WACLhB,EAAOkB,GAAGF,QAAQG,KAAKlH,MACvBA,KAAK8G,QAAQ2F,IAAIpG,GACbrG,KAAKqJ,YACLrJ,KAAKqJ,WAAWO,OAAOtD,EAAQtG,KAAKsJ,oBAExCtJ,KAAK+K,MAAQ,KACbrL,EAAMgN,aAAa1M,KAAK2I,gBACxBjJ,EAAMqH,QAAQ/G,KAAK8G,UAEvBoD,MAAO,WACH,MAAO1M,MAEXmP,QAAS,WACL,MAAOjN,GAAMkN,WAAW5M,KAAK8G,UAEjC+F,QAAS,WAAA,GAGG/F,GACAgG,EAHJ/C,EAAO/J,KAAK+K,KACZhB,KACIjD,EAAU9G,KAAK8G,QACfgG,EAAchG,EAAQuD,WAC1BN,EAAKW,MAAM7L,MAAQgH,EAAWiB,GAC9BiD,EAAKW,MAAM5L,OAAS6G,EAAYmB,GAChCgG,EAAYC,KACRlO,MAAOkL,EAAKW,MAAM7L,MAClBC,OAAQiL,EAAKW,MAAM5L,SAEvBkB,KAAKgN,aAAajD,EAAM+C,KAGhCE,aAAc,SAAUjD,EAAMjD,GAAhB,GAEFsE,GACA6B,EAAOC,EAEFC,CAJb,IAAIpD,EAAKM,UAAYN,EAAKM,SAAS3L,OAI/B,IAHI0M,EAAWtE,EAAQuD,SAAS,mBAAmBA,WAEnDrK,KAAK+I,QAAQ0B,QAAQV,EAAKM,SAAUN,EAAKW,OAAShN,KAAMsC,KAAKiJ,MAAMmE,UAAUrD,EAAMjD,KAC1EqG,EAAM,EAAGA,EAAMpD,EAAKM,SAAS3L,OAAQyO,IAC1CF,EAAQlD,EAAKM,SAAS8C,GACtBD,EAAe9B,EAASiC,OAAO,IAAM3N,EAAMwM,KAAK,OAAS,KAAQe,EAAMxB,SAASrB,IAAM,MACtFpK,KAAKiJ,MAAMqE,YAAYL,EAAOC,GAC9BlN,KAAKgN,aAAaC,EAAOC,IAIrCK,WAAY,SAAUtM,GAClB,GAAIoI,GAAapI,EAAQoI,UACzBpI,GAAQoI,WAAa3G,EACrB1C,KAAKuH,iBAAmBnF,EAAWpC,KAAKuH,iBAAkBtG,GAC1DjB,KAAKiB,QAAUmB,KAAepC,KAAKuH,kBACnCvH,KAAKsH,aACLtH,KAAKoH,WAAWpH,KAAKiB,SACrB8E,EAAOkB,GAAGuG,WAAWtG,KAAKlH,KAAMiB,GAC5BoI,GACArJ,KAAK2J,cAAczD,EAAuBsD,OAAOH,IAEjDrJ,KAAKiB,QAAQ4G,UACb7H,KAAKqJ,WAAWK,WAIxBP,EAAavJ,EAAMC,QACnBiL,WAAY,SAAUf,EAAMlL,EAAOC,GAC/BiL,EAAKW,OACD7L,MAAOA,EACPC,OAAQA,EACR2O,IAAK,EACLC,KAAM,IAGdC,KAAM,SAAUC,GACZ,OAAQA,EAAKvD,UAEjBwD,eAAgB,SAAU3D,EAAOQ,GAAjB,GAEuBlM,GAQ/BsP,EACAC,EACAC,EAXAC,EAAavD,EAAM7L,MAAQ6L,EAAM5L,OACjCoP,EAAY,EAAGC,IACnB,KAAK3P,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IAC1B2P,EAAU3P,GAAK4P,WAAWlE,EAAM1L,GAAG6B,OACnC6N,GAAaC,EAAU3P,EAE3B,KAAKA,EAAI,EAAGA,EAAI2P,EAAUzP,OAAQF,IAC9B0L,EAAM1L,GAAG6P,KAAOJ,EAAaE,EAAU3P,GAAK0P,CAE5CJ,GAAmB9N,KAAKsO,mBAAqB5D,EAAM5L,OAAS4L,EAAM7L,MAClEkP,GAAgB7D,EAAM,IACtB8D,EAAO9D,EAAMqE,MAAM,GACvBvO,KAAKwO,SAASR,EAAMD,EAAcD,EAAkBpD,IAExD8D,SAAU,SAAUR,EAAMS,EAAa5P,EAAO6L,GAC1C1K,KAAK0O,WAAWV,EAAMS,EAAa5P,EAAO6L,IAE9CgE,WAAY,SAAUV,EAAMS,EAAa5P,EAAO6L,GAApC,GAEA5D,GAcJiH,EAIIY,CAnBR,OAAIX,GAAKtP,OAAS+P,EAAY/P,QAAU,GAChCoI,EAAyB,GAAfkH,EAAKtP,OAAcsP,EAAOS,EACxCzO,KAAK4O,WAAW9H,EAASjI,EAAO6L,GAChC,IAEAsD,EAAKtP,QAAU,GAA4B,IAAvB+P,EAAY/P,SAChC+P,GAAeT,EAAK,IACpBA,EAAOA,EAAKO,MAAM,IAEF,IAAhBP,EAAKtP,QACD+P,EAAY/P,OAAS,GACrBsB,KAAK6O,UAAUJ,EAAa5P,EAAO6L,GAEvC,IAEAqD,EAAeC,EAAK,GACpBhO,KAAK8O,iBAAiBL,EAAa5P,IAAUmB,KAAK8O,kBAAkBf,GAAc9B,OAAOwC,GAAc5P,GACvGmB,KAAK0O,WAAWV,EAAKO,MAAM,GAAIE,EAAYxC,QAAQ8B,IAAgBlP,EAAO6L,IAEtEiE,EAAY3O,KAAK6O,UAAUJ,EAAa5P,EAAO6L,GACnD1K,KAAK0O,WAAWV,KAAUW,EAAUI,IAAKJ,IALzCZ,KAQRa,WAAY,SAAU1E,EAAO8E,EAAGtE,GAC5BR,EAAM,GAAGQ,MAAQA,GAErBmE,UAAW,SAAU3E,EAAOrL,EAAO6L,GAC/B,MAAI1K,MAAKsO,mBACEtO,KAAKiP,QAAQ/E,EAAOrL,EAAO6L,GAE3B1K,KAAKkP,QAAQhF,EAAOrL,EAAO6L,IAG1CyE,YAAa,IACbC,eAAgB,WACZ,MAA4B,MAArBpP,KAAKmP,aAEhBb,iBAAkB,WACd,MAA4B,MAArBtO,KAAKmP,aAEhBE,aAAc,WACVrP,KAAKmP,YAAcnP,KAAKoP,iBAAmB,IAAM,KAErDN,iBAAkB,SAAU5E,EAAOrL,GAAjB,GAIVyQ,GAAaC,EAAaC,EACrBhR,EACD6P,CALR,KAAKnE,GAA0B,IAAjBA,EAAMxL,OAChB,MAAO+H,EAGX,KADI6I,EAAU,EAAGC,EAAU,EAAGC,EAAU/I,EAC/BjI,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IAC1B6P,EAAOnE,EAAM1L,GAAG6P,KACpBiB,GAAWjB,EACXmB,EAAUA,EAAUnB,EAAOmB,EAAUnB,EACrCkB,EAAUA,EAAUlB,EAAOkB,EAAUlB,CAEzC,OAAOlK,GAAKhB,IAAItE,EAAQA,EAAQ0Q,GAAWD,EAAUA,GAAUA,EAAUA,GAAWzQ,EAAQA,EAAQ2Q,KAExG/E,QAAS,SAAUJ,EAAUoF,EAAWzF,GAIpC,GAHMyF,EAAU5Q,OAAS4Q,EAAU3Q,QAAUkB,KAAKsO,oBAC9CtO,KAAKqP,eAELhF,GAAYA,EAAS3L,OAAS,EAAG,CACjC,GAAIgR,IACA7Q,MAAO4Q,EAAU5Q,MACjBC,OAAQ2Q,EAAU3Q,OAASkL,EAAStM,KACpC+P,IAAK,EACLC,KAAM,EAEV1N,MAAK6N,eAAexD,EAAUqF,KAGtCT,QAAS,SAAU/E,EAAOrL,EAAO6L,GAAxB,GAGIlM,GACDM,EASJ6Q,EAZAzB,EAAYlO,KAAK4P,WAAW1F,GAAQuD,EAAM,CAE9C,KADA5O,EAAQgG,EAAMqJ,EAAYrP,GACjBL,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IAC1BM,EAAS+F,EAAMqF,EAAM1L,GAAG6P,KAAOxP,GACnCqL,EAAM1L,GAAGkM,OACL5L,OAAQA,EACRD,MAAOA,EACP4O,IAAK/C,EAAM+C,IAAMA,EACjBC,KAAMhD,EAAMgD,MAEhBD,GAAO3O,CAYX,OAVI6Q,IACA7Q,OAAQ4L,EAAM5L,OACdD,MAAO6L,EAAM7L,MAAQA,EACrB4O,IAAK/C,EAAM+C,IACXC,KAAMhD,EAAMgD,KAAO7O,GAEvB8Q,EAAIZ,IAAM5K,EAAKjB,IAAIyM,EAAI9Q,MAAO8Q,EAAI7Q,QAC9B6Q,EAAIZ,KAAOY,EAAI7Q,QACfkB,KAAKqP,eAEFM,GAEXT,QAAS,SAAUhF,EAAOrL,EAAO6L,GAAxB,GAGIlM,GASLmR,EAXAzB,EAAYlO,KAAK4P,WAAW1F,GAC5BpL,EAAS+F,EAAMqJ,EAAYrP,GAAQ4O,EAAM/C,EAAM+C,IAAKC,EAAO,CAC/D,KAASlP,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IAC9B0L,EAAM1L,GAAGkM,OACL5L,OAAQA,EACRD,MAAOgG,EAAMqF,EAAM1L,GAAG6P,KAAOvP,GAC7B2O,IAAKA,EACLC,KAAMhD,EAAMgD,KAAOA,GAEvBA,GAAQxD,EAAM1L,GAAGkM,MAAM7L,KAY3B,OAVI8Q,IACA7Q,OAAQ4L,EAAM5L,OAASA,EACvBD,MAAO6L,EAAM7L,MACb4O,IAAK/C,EAAM+C,IAAM3O,EACjB4O,KAAMhD,EAAMgD,MAEhBiC,EAAIZ,IAAM5K,EAAKjB,IAAIyM,EAAI9Q,MAAO8Q,EAAI7Q,QAC9B6Q,EAAIZ,KAAOY,EAAI9Q,OACfmB,KAAKqP,eAEFM,GAEXC,WAAY,SAAU1F,GAAV,GAEC1L,GADLqR,EAAQ,CACZ,KAASrR,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IAC9BqR,GAAS3F,EAAM1L,GAAG6P,IAEtB,OAAOwB,MAGXzG,EAAiBxJ,EAAMC,QACvBC,KAAM,SAAUyI,EAAStH,GACrBjB,KAAKiB,QAAUmB,KAAepC,KAAKiB,QAASA,GAC5CjB,KAAKuI,QAAUA,EACfvI,KAAK8G,QAAUtJ,EAAE+K,EAAQzB,SACzB9G,KAAK8P,OAAS,GAElB1C,UAAW,SAAUtD,EAAMhD,GACvB,GAAIpJ,GAAOoJ,EAAQuD,SAAS,mBAC5B,OAAO3M,GAAKoB,UAAY,GAE5BkL,SAAU,SAAUD,GAAV,GAKErM,GAEIqS,EANRjD,EAAc9M,KAAKmK,UAAUJ,EAAK0B,SAASrB,KAC3CJ,GAAatM,KAAM,EAavB,OAZIqM,GAAKM,WACLrK,KAAKgQ,OAAOlD,GACRpP,EAAOsC,KAAKiQ,SAASlG,GACrBrM,IACIqS,EAAQ/P,KAAKkQ,aAAanG,GAC9B+C,EAAYqD,OAAOJ,GACnB/P,KAAKoQ,SAASL,EAAOhG,EAAK0B,UAC1BzB,EAAStM,KAAOqS,EAAMjR,UAE1BgO,EAAYqD,OAAOnQ,KAAKqQ,eACxBrQ,KAAK8P,QAAUjK,EAAWiH,GAAeA,EAAYwD,cAAgB,GAElEtG,GAEXoG,SAAU,SAAUtJ,EAAS2E,GACzBzL,KAAKuI,QAAQ4C,QAAQ,UAAW,WAC5B,OACIC,SAAUtE,EACVX,OAASsF,SAAUA,QAI/BtB,UAAW,SAAUC,GACjB,MAAOpK,MAAK8G,QAAQuE,KAAK,mBAAqB3L,EAAMwM,KAAK,OAAS,KAAQ9B,EAAM,OAEpFQ,OAAQ,SAAUb,GAAV,GAIIwG,GACK/R,EACDmP,EACA6C,EANR1D,EAAc9M,KAAKmK,UAAUJ,EAAK0B,SAASrB,KAC3CC,EAAWN,EAAKM,QACpB,IAAIA,EAEA,IADIkG,EAAWzD,EAAYzB,KAAK,mBACvB7M,EAAI,EAAGA,EAAI6L,EAAS3L,OAAQF,IAC7BmP,EAAOtD,EAAS7L,GAChBgS,EAAcxQ,KAAKyQ,YAAY9C,GACnC4C,EAASJ,OAAOK,GAChBxQ,KAAKoQ,SAASI,EAAYnG,WAAYsD,EAAKlC,UAC3CzL,KAAKuI,QAAQ2C,QAAQ1E,GAAgBM,QAAS0J,KAI1D1F,WAAY,SAAUf,GAClB,GAAIyG,GAAcxQ,KAAKyQ,YAAY1G,EACnC/J,MAAK8G,QAAQqJ,OAAOK,GACpBxQ,KAAKoQ,SAASI,EAAYnG,WAAYN,EAAK0B,UAC3CzL,KAAKuI,QAAQ2C,QAAQ1E,GAAgBM,QAAS0J,KAElDR,OAAQ,SAAUjG,GACd/J,KAAKuI,QAAQ4C,QAAQ,UAAW,WAC5B,OAASC,SAAUrB,EAAKM,SAAS,4BAErCN,EAAKgD,IAAI,mBAAoB,IAC7BhD,EAAKyC,YAAY,UACjBzC,EAAKyC,YAAY,aACjBzC,EAAK/C,SAETyJ,YAAa,SAAU3G,GACnB,MAAO9J,MAAK0Q,YAAY5G,GAAMiD,IAAI,mBAAoBjD,EAAK7F,OAAOoD,SAAS,UAAUsJ,YAAY,YAAa3Q,KAAK4Q,qBAAqB9G,GAAQ,KAAK+G,OAAsB,IAAf/G,EAAKzJ,OAAa8P,OAAO3S,EAAE,eAAesT,KAAK9Q,KAAKiQ,SAASnG,MAE7N4G,YAAa,SAAU5G,GACnB,GAAIiH,GAAOvT,EAAE,qCAKb,OAJAwC,MAAKsN,YAAYxD,EAAMiH,GACnBhO,EAAQ+G,EAAK2B,WAAa1I,EAAQ+G,EAAK2B,SAASrB,MAChD2G,EAAK7E,KAAKxM,EAAMwM,KAAK,OAAQpC,EAAK2B,SAASrB,KAExC2G,GAEXC,iBAAkB,SAAUlH,GACxB,GAAImH,IACApS,MAAOiL,EAAKY,MAAM7L,MAClBC,OAAQgL,EAAKY,MAAM5L,OACnB4O,KAAM5D,EAAKY,MAAMgD,KACjBD,IAAK3D,EAAKY,MAAM+C,IAYpB,OATIwD,GAAYpS,OADZoS,EAAYvD,MAAQ1N,KAAK8P,OACU,EAAd9P,KAAK8P,OAEL9P,KAAK8P,OAG1BmB,EAAYnS,QADZmS,EAAYxD,IACwB,EAAdzN,KAAK8P,OAEL9P,KAAK8P,OAExBmB,GAEX3D,YAAa,SAAUxD,EAAMhD,GACzB,GAAImK,GAAcjR,KAAKgR,iBAAiBlH,EACxChD,GAAQiG,KACJlO,MAAOoS,EAAYpS,MACnBC,OAAQmS,EAAYnS,OACpB4O,KAAMuD,EAAYvD,KAClBD,IAAKwD,EAAYxD,OAGzBwC,SAAU,SAAUnG,GAChB,GAAIpM,GAAOoM,EAAKpM,IAIhB,OAHIsC,MAAKiB,QAAQgF,WACbvI,EAAOsC,KAAKkR,gBAAgBpH,IAEzBpM,GAEXwT,gBAAiB,SAAUpH,GACvB,GAAIqH,GAAgBlL,EAASjG,KAAKiB,QAAQgF,SAC1C,OAAOkL,IACH1F,SAAU3B,EAAK2B,SACf/N,KAAMoM,EAAKpM,QAGnBwS,aAAc,SAAUpG,GACpB,MAAOtM,GAAE,uCAAyC2S,OAAO3S,EAAE,eAAesT,KAAK9Q,KAAKiQ,SAASnG,MAEjGuG,YAAa,WACT,MAAO7S,GAAE,uCAEboT,qBAAsB,SAAU9G,GAC5B,MAAOrG,GAAgBqG,EAAK7F,UAGhC+E,EAAepJ,EAAMC,QACrBiL,WAAY,SAAUf,EAAMlL,EAAOC,EAAQsS,GACvCrH,EAAKW,OACD7L,MAAOA,EACPC,OAAQA,EACR2O,IAAK,EACLC,KAAM,GAEV3D,EAAKqH,SAAWA,GAEpBtR,KAAM,SAAUsR,GACZpR,KAAKoR,SAAWA,EAChBpR,KAAKqR,SAAWD,EAAW,EAAI,GAEnC3G,QAAS,SAAUJ,EAAUoF,EAAWzF,GAA/B,GAEGnL,GACAC,EAMA4Q,CARJrF,GAAS3L,OAAS,IACdG,EAAQ4Q,EAAU5Q,MAClBC,EAAS2Q,EAAU3Q,OACnBkB,KAAKoR,SACLtS,GAAUkL,EAAStM,KAEnBmB,GAASmL,EAAStM,KAElBgS,GACA7Q,MAAOA,EACPC,OAAQA,EACR2O,IAAK,EACLC,KAAM,GAEV1N,KAAK6N,eAAexD,EAAUqF,KAGtC7B,eAAgB,SAAU3D,EAAOQ,GAAjB,GAIRlM,GAEIsL,EALJmE,EAAavD,EAAM7L,MAAQ6L,EAAM5L,OACjCoP,EAAY,EACZC,IAEJ,KAAK3P,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IACtBsL,EAAOI,EAAM1L,GACjB2P,EAAU3P,GAAK4P,WAAWlE,EAAM1L,GAAG6B,OACnC6N,GAAaC,EAAU3P,GACvBsL,EAAKsH,SAAWpR,KAAKoR,QAEzB,KAAK5S,EAAI,EAAGA,EAAI2P,EAAUzP,OAAQF,IAC9B0L,EAAM1L,GAAG6P,KAAOJ,EAAaE,EAAU3P,GAAK0P,CAEhDlO,MAAKsR,aAAapH,EAAOQ,IAE7B4G,aAAc,SAAUpH,EAAOQ,GAC3B,GAAIwD,GAAYlO,KAAK4P,WAAW1F,EAC5BA,GAAM,GAAG6B,MAAQ,IAAM/L,KAAKqR,SAC5BrR,KAAKsO,iBAAiBpE,EAAOQ,EAAOwD,GAEpClO,KAAKoP,eAAelF,EAAOQ,EAAOwD,IAG1CI,iBAAkB,SAAUpE,EAAOQ,EAAOwD,GAAxB,GAEL1P,GACDsL,EACAjL,EAHJ6O,EAAO,CACX,KAASlP,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IAC1BsL,EAAOI,EAAM1L,GACbK,EAAQiL,EAAKuE,MAAQH,EAAYxD,EAAM7L,OAC3CiL,EAAKY,OACD5L,OAAQ4L,EAAM5L,OACdD,MAAOA,EACP4O,IAAK/C,EAAM+C,IACXC,KAAMhD,EAAMgD,KAAOA,GAEvBA,GAAQ7O,GAGhBuQ,eAAgB,SAAUlF,EAAOQ,EAAOwD,GAAxB,GAEH1P,GACDsL,EACAhL,EAHJ2O,EAAM,CACV,KAASjP,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IAC1BsL,EAAOI,EAAM1L,GACbM,EAASgL,EAAKuE,MAAQH,EAAYxD,EAAM5L,QAC5CgL,EAAKY,OACD5L,OAAQA,EACRD,MAAO6L,EAAM7L,MACb4O,IAAK/C,EAAM+C,IAAMA,EACjBC,KAAMhD,EAAMgD,MAEhBD,GAAO3O,GAGf8Q,WAAY,SAAU1F,GAAV,GAEC1L,GADLqR,EAAQ,CACZ,KAASrR,EAAI,EAAGA,EAAI0L,EAAMxL,OAAQF,IAC9BqR,GAAS3F,EAAM1L,GAAG6P,IAEtB,OAAOwB,MAGX3G,EAAmBE,EAAevJ,QAClCmK,SAAU,SAAUD,GAAV,GAQErM,GAEIqS,EATRjD,EAAc9M,KAAKmK,UAAUJ,EAAK0B,SAASrB,KAC3CJ,GACAtM,KAAM,EACNoS,OAAQ,EAkBZ,OAhBI/F,GAAKM,WACLrK,KAAKgQ,OAAOlD,GACRpP,EAAOsC,KAAKiQ,SAASlG,GACrBrM,IACIqS,EAAQ/P,KAAKkQ,aAAanG,GAC9B+C,EAAYqD,OAAOJ,GACnB/P,KAAKoQ,SAASL,EAAOhG,EAAK0B,UAEtBzB,EAAStM,KADTqM,EAAKqH,SACWrB,EAAMjR,SAENiR,EAAMlR,SAG9BiO,EAAYqD,OAAOnQ,KAAKqQ,eACxBrQ,KAAK8P,QAAUjK,EAAWiH,GAAeA,EAAYwD,cAAgB,GAElEtG,GAEXoD,UAAW,SAAUtD,EAAMhD,GACvB,GAAI/G,EAMJ,OAJIA,GADA+J,EAAKsH,SACEtK,EAAQuD,SAAS,oBAAoBvL,SAErCgI,EAAQuD,SAAS,6BAA6BxL,QAElDkB,GAAQ,GAEnBmQ,aAAc,SAAUpG,GACpB,GAAIiG,EAMJ,OAJIA,GAAQvS,EADRsM,EAAKsH,SACK,sCAEA,gDAEPrB,EAAMI,OAAO3S,EAAE,eAAesT,KAAK9Q,KAAKiQ,SAASnG,OA2FhE1D,GAAQJ,GAAGuL,OAAO1K,IACpBpH,OAAOC,MAAM2C,QACR5C,OAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dataviz.treemap.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.dataviz.treemap', [\n        'kendo.data',\n        'kendo.userevents',\n        'kendo.dataviz.themes'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dataviz.treeMap',\n        name: 'TreeMap',\n        category: 'dataviz',\n        description: 'The Kendo DataViz TreeMap',\n        depends: [\n            'data',\n            'userevents',\n            'dataviz.themes'\n        ]\n    };\n    (function ($, undefined) {\n        var math = Math, proxy = $.proxy, isArray = $.isArray, kendo = window.kendo, outerHeight = kendo._outerHeight, outerWidth = kendo._outerWidth, Class = kendo.Class, Widget = kendo.ui.Widget, template = kendo.template, deepExtend = kendo.deepExtend, HierarchicalDataSource = kendo.data.HierarchicalDataSource, getter = kendo.getter, dataviz = kendo.dataviz;\n        var NS = '.kendoTreeMap', CHANGE = 'change', DATA_BOUND = 'dataBound', ITEM_CREATED = 'itemCreated', MAX_VALUE = Number.MAX_VALUE, MOUSEOVER_NS = 'mouseover' + NS, MOUSELEAVE_NS = 'mouseleave' + NS, UNDEFINED = 'undefined';\n        var TreeMap = Widget.extend({\n            init: function (element, options) {\n                kendo.destroy(element);\n                $(element).empty();\n                Widget.fn.init.call(this, element, options);\n                this.wrapper = this.element;\n                this._initTheme(this.options);\n                this.element.addClass('k-widget k-treemap');\n                this._setLayout();\n                this._originalOptions = deepExtend({}, this.options);\n                this._initDataSource();\n                this._attachEvents();\n                kendo.notify(this, dataviz.ui);\n            },\n            options: {\n                name: 'TreeMap',\n                theme: 'default',\n                autoBind: true,\n                textField: 'text',\n                valueField: 'value',\n                colorField: 'color'\n            },\n            events: [\n                DATA_BOUND,\n                ITEM_CREATED\n            ],\n            _initTheme: function (options) {\n                var that = this, themes = dataviz.ui.themes || {}, themeName = ((options || {}).theme || '').toLowerCase(), themeOptions = (themes[themeName] || {}).treeMap;\n                that.options = deepExtend({}, themeOptions, options);\n            },\n            _attachEvents: function () {\n                this.element.on(MOUSEOVER_NS, proxy(this._mouseover, this)).on(MOUSELEAVE_NS, proxy(this._mouseleave, this));\n                this._resizeHandler = proxy(this.resize, this, false);\n                kendo.onResize(this._resizeHandler);\n            },\n            _setLayout: function () {\n                if (this.options.type === 'horizontal') {\n                    this._layout = new SliceAndDice(false);\n                    this._view = new SliceAndDiceView(this, this.options);\n                } else if (this.options.type === 'vertical') {\n                    this._layout = new SliceAndDice(true);\n                    this._view = new SliceAndDiceView(this, this.options);\n                } else {\n                    this._layout = new Squarified();\n                    this._view = new SquarifiedView(this, this.options);\n                }\n            },\n            _initDataSource: function () {\n                var that = this, options = that.options, dataSource = options.dataSource;\n                that._dataChangeHandler = proxy(that._onDataChange, that);\n                that.dataSource = HierarchicalDataSource.create(dataSource).bind(CHANGE, that._dataChangeHandler);\n                if (dataSource) {\n                    if (that.options.autoBind) {\n                        that.dataSource.fetch();\n                    }\n                }\n            },\n            setDataSource: function (dataSource) {\n                var that = this;\n                that.dataSource.unbind(CHANGE, that._dataChangeHandler);\n                that.dataSource = dataSource.bind(CHANGE, that._dataChangeHandler);\n                if (dataSource) {\n                    if (that.options.autoBind) {\n                        that.dataSource.fetch();\n                    }\n                }\n            },\n            _onDataChange: function (e) {\n                var node = e.node;\n                var items = e.items;\n                var options = this.options;\n                var item, i;\n                if (!node) {\n                    this._cleanItems();\n                    this.element.empty();\n                    item = this._wrapItem(items[0]);\n                    this._layout.createRoot(item, outerWidth(this.element), outerHeight(this.element), this.options.type === 'vertical');\n                    this._view.createRoot(item);\n                    this._root = item;\n                    this._colorIdx = 0;\n                } else {\n                    if (items.length) {\n                        var root = this._getByUid(node.uid);\n                        root.children = [];\n                        items = new kendo.data.Query(items)._sortForGrouping(options.valueField, 'desc');\n                        for (i = 0; i < items.length; i++) {\n                            item = items[i];\n                            root.children.push(this._wrapItem(item));\n                        }\n                        var htmlSize = this._view.htmlSize(root);\n                        this._layout.compute(root.children, root.coord, htmlSize);\n                        this._setColors(root.children);\n                        this._view.render(root);\n                    }\n                }\n                for (i = 0; i < items.length; i++) {\n                    items[i].load();\n                }\n                if (node) {\n                    this.trigger(DATA_BOUND, { node: node });\n                }\n            },\n            _cleanItems: function () {\n                var that = this;\n                that.angular('cleanup', function () {\n                    return { elements: that.element.find('.k-leaf div,.k-treemap-title,.k-treemap-title-vertical') };\n                });\n            },\n            _setColors: function (items) {\n                var colors = this.options.colors;\n                var colorIdx = this._colorIdx;\n                var color = colors[colorIdx % colors.length];\n                var colorRange, item;\n                if (isArray(color)) {\n                    colorRange = colorsByLength(color[0], color[1], items.length);\n                }\n                var leafNodes = false;\n                for (var i = 0; i < items.length; i++) {\n                    item = items[i];\n                    if (!defined(item.color)) {\n                        if (colorRange) {\n                            item.color = colorRange[i];\n                        } else {\n                            item.color = color;\n                        }\n                    }\n                    if (!item.dataItem.hasChildren) {\n                        leafNodes = true;\n                    }\n                }\n                if (leafNodes) {\n                    this._colorIdx++;\n                }\n            },\n            _contentSize: function (root) {\n                this.view.renderHeight(root);\n            },\n            _wrapItem: function (item) {\n                var wrap = {};\n                if (defined(this.options.valueField)) {\n                    wrap.value = getField(this.options.valueField, item);\n                }\n                if (defined(this.options.colorField)) {\n                    wrap.color = getField(this.options.colorField, item);\n                }\n                if (defined(this.options.textField)) {\n                    wrap.text = getField(this.options.textField, item);\n                }\n                wrap.level = item.level();\n                wrap.dataItem = item;\n                return wrap;\n            },\n            _getByUid: function (uid) {\n                var items = [this._root];\n                var item;\n                while (items.length) {\n                    item = items.pop();\n                    if (item.dataItem.uid === uid) {\n                        return item;\n                    }\n                    if (item.children) {\n                        items = items.concat(item.children);\n                    }\n                }\n            },\n            dataItem: function (node) {\n                var uid = $(node).attr(kendo.attr('uid')), dataSource = this.dataSource;\n                return dataSource && dataSource.getByUid(uid);\n            },\n            findByUid: function (uid) {\n                return this.element.find('.k-treemap-tile[' + kendo.attr('uid') + '=\\'' + uid + '\\']');\n            },\n            _mouseover: function (e) {\n                var target = $(e.target);\n                if (target.hasClass('k-leaf')) {\n                    this._removeActiveState();\n                    target.removeClass('k-state-hover').addClass('k-state-hover');\n                }\n            },\n            _removeActiveState: function () {\n                this.element.find('.k-state-hover').removeClass('k-state-hover');\n            },\n            _mouseleave: function () {\n                this._removeActiveState();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.element.off(NS);\n                if (this.dataSource) {\n                    this.dataSource.unbind(CHANGE, this._dataChangeHandler);\n                }\n                this._root = null;\n                kendo.unbindResize(this._resizeHandler);\n                kendo.destroy(this.element);\n            },\n            items: function () {\n                return $();\n            },\n            getSize: function () {\n                return kendo.dimensions(this.element);\n            },\n            _resize: function () {\n                var root = this._root;\n                if (root) {\n                    var element = this.element;\n                    var rootElement = element.children();\n                    root.coord.width = outerWidth(element);\n                    root.coord.height = outerHeight(element);\n                    rootElement.css({\n                        width: root.coord.width,\n                        height: root.coord.height\n                    });\n                    this._resizeItems(root, rootElement);\n                }\n            },\n            _resizeItems: function (root, element) {\n                if (root.children && root.children.length) {\n                    var elements = element.children('.k-treemap-wrap').children();\n                    var child, childElement;\n                    this._layout.compute(root.children, root.coord, { text: this._view.titleSize(root, element) });\n                    for (var idx = 0; idx < root.children.length; idx++) {\n                        child = root.children[idx];\n                        childElement = elements.filter('[' + kendo.attr('uid') + '=\\'' + child.dataItem.uid + '\\']');\n                        this._view.setItemSize(child, childElement);\n                        this._resizeItems(child, childElement);\n                    }\n                }\n            },\n            setOptions: function (options) {\n                var dataSource = options.dataSource;\n                options.dataSource = undefined;\n                this._originalOptions = deepExtend(this._originalOptions, options);\n                this.options = deepExtend({}, this._originalOptions);\n                this._setLayout();\n                this._initTheme(this.options);\n                Widget.fn._setEvents.call(this, options);\n                if (dataSource) {\n                    this.setDataSource(HierarchicalDataSource.create(dataSource));\n                }\n                if (this.options.autoBind) {\n                    this.dataSource.fetch();\n                }\n            }\n        });\n        var Squarified = Class.extend({\n            createRoot: function (root, width, height) {\n                root.coord = {\n                    width: width,\n                    height: height,\n                    top: 0,\n                    left: 0\n                };\n            },\n            leaf: function (tree) {\n                return !tree.children;\n            },\n            layoutChildren: function (items, coord) {\n                var parentArea = coord.width * coord.height;\n                var totalArea = 0, itemsArea = [], i;\n                for (i = 0; i < items.length; i++) {\n                    itemsArea[i] = parseFloat(items[i].value);\n                    totalArea += itemsArea[i];\n                }\n                for (i = 0; i < itemsArea.length; i++) {\n                    items[i].area = parentArea * itemsArea[i] / totalArea;\n                }\n                var minimumSideValue = this.layoutHorizontal() ? coord.height : coord.width;\n                var firstElement = [items[0]];\n                var tail = items.slice(1);\n                this.squarify(tail, firstElement, minimumSideValue, coord);\n            },\n            squarify: function (tail, initElement, width, coord) {\n                this.computeDim(tail, initElement, width, coord);\n            },\n            computeDim: function (tail, initElement, width, coord) {\n                if (tail.length + initElement.length == 1) {\n                    var element = tail.length == 1 ? tail : initElement;\n                    this.layoutLast(element, width, coord);\n                    return;\n                }\n                if (tail.length >= 2 && initElement.length === 0) {\n                    initElement = [tail[0]];\n                    tail = tail.slice(1);\n                }\n                if (tail.length === 0) {\n                    if (initElement.length > 0) {\n                        this.layoutRow(initElement, width, coord);\n                    }\n                    return;\n                }\n                var firstElement = tail[0];\n                if (this.worstAspectRatio(initElement, width) >= this.worstAspectRatio([firstElement].concat(initElement), width)) {\n                    this.computeDim(tail.slice(1), initElement.concat([firstElement]), width, coord);\n                } else {\n                    var newCoords = this.layoutRow(initElement, width, coord);\n                    this.computeDim(tail, [], newCoords.dim, newCoords);\n                }\n            },\n            layoutLast: function (items, w, coord) {\n                items[0].coord = coord;\n            },\n            layoutRow: function (items, width, coord) {\n                if (this.layoutHorizontal()) {\n                    return this.layoutV(items, width, coord);\n                } else {\n                    return this.layoutH(items, width, coord);\n                }\n            },\n            orientation: 'h',\n            layoutVertical: function () {\n                return this.orientation === 'v';\n            },\n            layoutHorizontal: function () {\n                return this.orientation === 'h';\n            },\n            layoutChange: function () {\n                this.orientation = this.layoutVertical() ? 'h' : 'v';\n            },\n            worstAspectRatio: function (items, width) {\n                if (!items || items.length === 0) {\n                    return MAX_VALUE;\n                }\n                var areaSum = 0, maxArea = 0, minArea = MAX_VALUE;\n                for (var i = 0; i < items.length; i++) {\n                    var area = items[i].area;\n                    areaSum += area;\n                    minArea = minArea < area ? minArea : area;\n                    maxArea = maxArea > area ? maxArea : area;\n                }\n                return math.max(width * width * maxArea / (areaSum * areaSum), areaSum * areaSum / (width * width * minArea));\n            },\n            compute: function (children, rootCoord, htmlSize) {\n                if (!(rootCoord.width >= rootCoord.height && this.layoutHorizontal())) {\n                    this.layoutChange();\n                }\n                if (children && children.length > 0) {\n                    var newRootCoord = {\n                        width: rootCoord.width,\n                        height: rootCoord.height - htmlSize.text,\n                        top: 0,\n                        left: 0\n                    };\n                    this.layoutChildren(children, newRootCoord);\n                }\n            },\n            layoutV: function (items, width, coord) {\n                var totalArea = this._totalArea(items), top = 0;\n                width = round(totalArea / width);\n                for (var i = 0; i < items.length; i++) {\n                    var height = round(items[i].area / width);\n                    items[i].coord = {\n                        height: height,\n                        width: width,\n                        top: coord.top + top,\n                        left: coord.left\n                    };\n                    top += height;\n                }\n                var ans = {\n                    height: coord.height,\n                    width: coord.width - width,\n                    top: coord.top,\n                    left: coord.left + width\n                };\n                ans.dim = math.min(ans.width, ans.height);\n                if (ans.dim != ans.height) {\n                    this.layoutChange();\n                }\n                return ans;\n            },\n            layoutH: function (items, width, coord) {\n                var totalArea = this._totalArea(items);\n                var height = round(totalArea / width), top = coord.top, left = 0;\n                for (var i = 0; i < items.length; i++) {\n                    items[i].coord = {\n                        height: height,\n                        width: round(items[i].area / height),\n                        top: top,\n                        left: coord.left + left\n                    };\n                    left += items[i].coord.width;\n                }\n                var ans = {\n                    height: coord.height - height,\n                    width: coord.width,\n                    top: coord.top + height,\n                    left: coord.left\n                };\n                ans.dim = math.min(ans.width, ans.height);\n                if (ans.dim != ans.width) {\n                    this.layoutChange();\n                }\n                return ans;\n            },\n            _totalArea: function (items) {\n                var total = 0;\n                for (var i = 0; i < items.length; i++) {\n                    total += items[i].area;\n                }\n                return total;\n            }\n        });\n        var SquarifiedView = Class.extend({\n            init: function (treeMap, options) {\n                this.options = deepExtend({}, this.options, options);\n                this.treeMap = treeMap;\n                this.element = $(treeMap.element);\n                this.offset = 0;\n            },\n            titleSize: function (item, element) {\n                var text = element.children('.k-treemap-title');\n                return text.height() || 0;\n            },\n            htmlSize: function (root) {\n                var rootElement = this._getByUid(root.dataItem.uid);\n                var htmlSize = { text: 0 };\n                if (root.children) {\n                    this._clean(rootElement);\n                    var text = this._getText(root);\n                    if (text) {\n                        var title = this._createTitle(root);\n                        rootElement.append(title);\n                        this._compile(title, root.dataItem);\n                        htmlSize.text = title.height();\n                    }\n                    rootElement.append(this._createWrap());\n                    this.offset = (outerWidth(rootElement) - rootElement.innerWidth()) / 2;\n                }\n                return htmlSize;\n            },\n            _compile: function (element, dataItem) {\n                this.treeMap.angular('compile', function () {\n                    return {\n                        elements: element,\n                        data: [{ dataItem: dataItem }]\n                    };\n                });\n            },\n            _getByUid: function (uid) {\n                return this.element.find('.k-treemap-tile[' + kendo.attr('uid') + '=\\'' + uid + '\\']');\n            },\n            render: function (root) {\n                var rootElement = this._getByUid(root.dataItem.uid);\n                var children = root.children;\n                if (children) {\n                    var rootWrap = rootElement.find('.k-treemap-wrap');\n                    for (var i = 0; i < children.length; i++) {\n                        var leaf = children[i];\n                        var htmlElement = this._createLeaf(leaf);\n                        rootWrap.append(htmlElement);\n                        this._compile(htmlElement.children(), leaf.dataItem);\n                        this.treeMap.trigger(ITEM_CREATED, { element: htmlElement });\n                    }\n                }\n            },\n            createRoot: function (root) {\n                var htmlElement = this._createLeaf(root);\n                this.element.append(htmlElement);\n                this._compile(htmlElement.children(), root.dataItem);\n                this.treeMap.trigger(ITEM_CREATED, { element: htmlElement });\n            },\n            _clean: function (root) {\n                this.treeMap.angular('cleanup', function () {\n                    return { elements: root.children(':not(.k-treemap-wrap)') };\n                });\n                root.css('background-color', '');\n                root.removeClass('k-leaf');\n                root.removeClass('k-inverse');\n                root.empty();\n            },\n            _createLeaf: function (item) {\n                return this._createTile(item).css('background-color', item.color).addClass('k-leaf').toggleClass('k-inverse', this._tileColorBrightness(item) > 180).toggle(item.value !== 0).append($('<div></div>').html(this._getText(item)));\n            },\n            _createTile: function (item) {\n                var tile = $('<div class=\\'k-treemap-tile\\'></div>');\n                this.setItemSize(item, tile);\n                if (defined(item.dataItem) && defined(item.dataItem.uid)) {\n                    tile.attr(kendo.attr('uid'), item.dataItem.uid);\n                }\n                return tile;\n            },\n            _itemCoordinates: function (item) {\n                var coordinates = {\n                    width: item.coord.width,\n                    height: item.coord.height,\n                    left: item.coord.left,\n                    top: item.coord.top\n                };\n                if (coordinates.left && this.offset) {\n                    coordinates.width += this.offset * 2;\n                } else {\n                    coordinates.width += this.offset;\n                }\n                if (coordinates.top) {\n                    coordinates.height += this.offset * 2;\n                } else {\n                    coordinates.height += this.offset;\n                }\n                return coordinates;\n            },\n            setItemSize: function (item, element) {\n                var coordinates = this._itemCoordinates(item);\n                element.css({\n                    width: coordinates.width,\n                    height: coordinates.height,\n                    left: coordinates.left,\n                    top: coordinates.top\n                });\n            },\n            _getText: function (item) {\n                var text = item.text;\n                if (this.options.template) {\n                    text = this._renderTemplate(item);\n                }\n                return text;\n            },\n            _renderTemplate: function (item) {\n                var titleTemplate = template(this.options.template);\n                return titleTemplate({\n                    dataItem: item.dataItem,\n                    text: item.text\n                });\n            },\n            _createTitle: function (item) {\n                return $('<div class=\\'k-treemap-title\\'></div>').append($('<div></div>').html(this._getText(item)));\n            },\n            _createWrap: function () {\n                return $('<div class=\\'k-treemap-wrap\\'></div>');\n            },\n            _tileColorBrightness: function (item) {\n                return colorBrightness(item.color);\n            }\n        });\n        var SliceAndDice = Class.extend({\n            createRoot: function (root, width, height, vertical) {\n                root.coord = {\n                    width: width,\n                    height: height,\n                    top: 0,\n                    left: 0\n                };\n                root.vertical = vertical;\n            },\n            init: function (vertical) {\n                this.vertical = vertical;\n                this.quotient = vertical ? 1 : 0;\n            },\n            compute: function (children, rootCoord, htmlSize) {\n                if (children.length > 0) {\n                    var width = rootCoord.width;\n                    var height = rootCoord.height;\n                    if (this.vertical) {\n                        height -= htmlSize.text;\n                    } else {\n                        width -= htmlSize.text;\n                    }\n                    var newRootCoord = {\n                        width: width,\n                        height: height,\n                        top: 0,\n                        left: 0\n                    };\n                    this.layoutChildren(children, newRootCoord);\n                }\n            },\n            layoutChildren: function (items, coord) {\n                var parentArea = coord.width * coord.height;\n                var totalArea = 0;\n                var itemsArea = [];\n                var i;\n                for (i = 0; i < items.length; i++) {\n                    var item = items[i];\n                    itemsArea[i] = parseFloat(items[i].value);\n                    totalArea += itemsArea[i];\n                    item.vertical = this.vertical;\n                }\n                for (i = 0; i < itemsArea.length; i++) {\n                    items[i].area = parentArea * itemsArea[i] / totalArea;\n                }\n                this.sliceAndDice(items, coord);\n            },\n            sliceAndDice: function (items, coord) {\n                var totalArea = this._totalArea(items);\n                if (items[0].level % 2 === this.quotient) {\n                    this.layoutHorizontal(items, coord, totalArea);\n                } else {\n                    this.layoutVertical(items, coord, totalArea);\n                }\n            },\n            layoutHorizontal: function (items, coord, totalArea) {\n                var left = 0;\n                for (var i = 0; i < items.length; i++) {\n                    var item = items[i];\n                    var width = item.area / (totalArea / coord.width);\n                    item.coord = {\n                        height: coord.height,\n                        width: width,\n                        top: coord.top,\n                        left: coord.left + left\n                    };\n                    left += width;\n                }\n            },\n            layoutVertical: function (items, coord, totalArea) {\n                var top = 0;\n                for (var i = 0; i < items.length; i++) {\n                    var item = items[i];\n                    var height = item.area / (totalArea / coord.height);\n                    item.coord = {\n                        height: height,\n                        width: coord.width,\n                        top: coord.top + top,\n                        left: coord.left\n                    };\n                    top += height;\n                }\n            },\n            _totalArea: function (items) {\n                var total = 0;\n                for (var i = 0; i < items.length; i++) {\n                    total += items[i].area;\n                }\n                return total;\n            }\n        });\n        var SliceAndDiceView = SquarifiedView.extend({\n            htmlSize: function (root) {\n                var rootElement = this._getByUid(root.dataItem.uid);\n                var htmlSize = {\n                    text: 0,\n                    offset: 0\n                };\n                if (root.children) {\n                    this._clean(rootElement);\n                    var text = this._getText(root);\n                    if (text) {\n                        var title = this._createTitle(root);\n                        rootElement.append(title);\n                        this._compile(title, root.dataItem);\n                        if (root.vertical) {\n                            htmlSize.text = title.height();\n                        } else {\n                            htmlSize.text = title.width();\n                        }\n                    }\n                    rootElement.append(this._createWrap());\n                    this.offset = (outerWidth(rootElement) - rootElement.innerWidth()) / 2;\n                }\n                return htmlSize;\n            },\n            titleSize: function (item, element) {\n                var size;\n                if (item.vertical) {\n                    size = element.children('.k-treemap-title').height();\n                } else {\n                    size = element.children('.k-treemap-title-vertical').width();\n                }\n                return size || 0;\n            },\n            _createTitle: function (item) {\n                var title;\n                if (item.vertical) {\n                    title = $('<div class=\\'k-treemap-title\\'></div>');\n                } else {\n                    title = $('<div class=\\'k-treemap-title-vertical\\'></div>');\n                }\n                return title.append($('<div></div>').html(this._getText(item)));\n            }\n        });\n        function getField(field, row) {\n            if (row === null) {\n                return row;\n            }\n            var get = getter(field, true);\n            return get(row);\n        }\n        function defined(value) {\n            return typeof value !== UNDEFINED;\n        }\n        function colorsByLength(min, max, length) {\n            var minRGBtoDecimal = rgbToDecimal(min);\n            var maxRGBtoDecimal = rgbToDecimal(max);\n            var isDarker = colorBrightness(min) - colorBrightness(max) < 0;\n            var colors = [];\n            colors.push(min);\n            for (var i = 0; i < length; i++) {\n                var rgbColor = {\n                    r: colorByIndex(minRGBtoDecimal.r, maxRGBtoDecimal.r, i, length, isDarker),\n                    g: colorByIndex(minRGBtoDecimal.g, maxRGBtoDecimal.g, i, length, isDarker),\n                    b: colorByIndex(minRGBtoDecimal.b, maxRGBtoDecimal.b, i, length, isDarker)\n                };\n                colors.push(buildColorFromRGB(rgbColor));\n            }\n            colors.push(max);\n            return colors;\n        }\n        function colorByIndex(min, max, index, length, isDarker) {\n            var minColor = math.min(math.abs(min), math.abs(max));\n            var maxColor = math.max(math.abs(min), math.abs(max));\n            var step = (maxColor - minColor) / (length + 1);\n            var currentStep = step * (index + 1);\n            var color;\n            if (isDarker) {\n                color = minColor + currentStep;\n            } else {\n                color = maxColor - currentStep;\n            }\n            return color;\n        }\n        function buildColorFromRGB(color) {\n            return '#' + decimalToRgb(color.r) + decimalToRgb(color.g) + decimalToRgb(color.b);\n        }\n        function rgbToDecimal(color) {\n            color = color.replace('#', '');\n            var rgbColor = colorToRGB(color);\n            return {\n                r: rgbToHex(rgbColor.r),\n                g: rgbToHex(rgbColor.g),\n                b: rgbToHex(rgbColor.b)\n            };\n        }\n        function decimalToRgb(number) {\n            var result = math.round(number).toString(16).toUpperCase();\n            if (result.length === 1) {\n                result = '0' + result;\n            }\n            return result;\n        }\n        function colorToRGB(color) {\n            var colorLength = color.length;\n            var rgbColor = {};\n            if (colorLength === 3) {\n                rgbColor.r = color[0];\n                rgbColor.g = color[1];\n                rgbColor.b = color[2];\n            } else {\n                rgbColor.r = color.substring(0, 2);\n                rgbColor.g = color.substring(2, 4);\n                rgbColor.b = color.substring(4, 6);\n            }\n            return rgbColor;\n        }\n        function rgbToHex(rgb) {\n            return parseInt(rgb.toString(16), 16);\n        }\n        function colorBrightness(color) {\n            var brightness = 0;\n            if (color) {\n                color = rgbToDecimal(color);\n                brightness = math.sqrt(0.241 * color.r * color.r + 0.691 * color.g * color.g + 0.068 * color.b * color.b);\n            }\n            return brightness;\n        }\n        function round(value) {\n            var power = math.pow(10, 4);\n            return math.round(value * power) / power;\n        }\n        dataviz.ui.plugin(TreeMap);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}