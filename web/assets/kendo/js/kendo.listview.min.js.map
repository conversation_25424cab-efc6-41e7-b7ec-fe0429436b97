{"version": 3, "sources": ["kendo.listview.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "CHANGE", "CANCEL", "DATABOUND", "DATABINDING", "Widget", "ui", "keys", "FOCUSSELECTOR", "PROGRESS", "ERROR", "FOCUSED", "SELECTED", "KEDITITEM", "EDIT", "REMOVE", "SAVE", "MOUSEDOWN", "CLICK", "TOUCHSTART", "NS", "proxy", "activeElement", "_activeElement", "progress", "DataSource", "data", "ListView", "DataBoundWidget", "extend", "init", "element", "options", "that", "this", "isArray", "dataSource", "fn", "call", "wrapper", "id", "_itemId", "_element", "_dataSource", "_templates", "_navigatable", "_selectable", "_pageable", "_crud<PERSON>and<PERSON>", "_scrollable", "autoBind", "fetch", "notify", "events", "name", "selectable", "navigatable", "height", "template", "altTemplate", "editTemplate", "setOptions", "destroy", "_item", "action", "children", "items", "dataItem", "attr", "uid", "closest", "getByUid", "setDataSource", "_unbindDataSource", "unbind", "_refresh<PERSON><PERSON><PERSON>", "_progress<PERSON><PERSON><PERSON>", "_error<PERSON><PERSON><PERSON>", "refresh", "_progress", "_error", "create", "bind", "toggle", "opacity", "addClass", "css", "e", "item", "idx", "length", "view", "html", "active", "endlessAppend", "_endlessFetchInProgress", "index", "_skipRerenderItemsCount", "scrollable", "_hasB<PERSON>ingTarget", "editable", "filter", "angular", "elements", "replaceWith", "eq", "trigger", "_angularItems", "_destroyEditable", "append", "not", "_focusNext", "current", "next", "settings", "pagerId", "pageable", "isPlainObject", "pager", "Pager", "multi", "Selectable", "parseOptions", "multiple", "aria", "change", "on", "keyCode", "SPACEBAR", "target", "currentTarget", "preventDefault", "ctrl<PERSON>ey", "hasClass", "removeClass", "clear", "value", "originalPageSize", "overflow-y", "position", "-webkit-overflow-scrolling", "_endlessPageSize", "pageSize", "off", "scrollTop", "clientHeight", "scrollHeight", "total", "endless", "candidate", "_current", "removeAttr", "_scrollTo", "container", "scrollDirectionFunc", "UseJQueryoffset", "SCROLL", "direction", "dimension", "elementOffset", "offset", "toLowerCase", "elementDimension", "containerScrollAmount", "containerDimension", "clickCallback", "is", "focusElement", "_tabindex", "focusAgain", "key", "canHandle", "isTextBox", "editItem", "find", "ESC", "ENTER", "UP", "LEFT", "prev", "DOWN", "RIGHT", "PAGEUP", "page", "PAGEDOWN", "HOME", "END", "blur", "save", "one", "edit", "cancel", "clearSelection", "select", "first", "_modelFromElement", "_closeEditable", "kendoEditable", "model", "clearContainer", "errorTemplate", "end", "sync", "remove", "cancelChanges", "hide", "add", "indexOf", "insert", "mousedownNs", "touchstartNs", "clickNs", "setTimeout", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,kBACH,aACA,iBACA,oBACDD,IACL,WA4jBE,MAtiBC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAS,SAAUC,EAAS,SAAUC,EAAY,YAAaC,EAAc,cAAeC,EAASN,EAAMO,GAAGD,OAAQE,EAAOR,EAAMQ,KAAMC,EAAgB,0BAA2BC,EAAW,WAAYC,EAAQ,QAASC,EAAU,kBAAmBC,EAAW,mBAAoBC,EAAY,cAAeC,EAAO,OAAQC,EAAS,SAAUC,EAAO,OAAQC,EAAY,YAAaC,EAAQ,QAASC,EAAa,aAAcC,EAAK,iBAAkBC,EAAQxB,EAAEwB,MAAOC,EAAgBvB,EAAMwB,eAAgBC,EAAWzB,EAAMO,GAAGkB,SAAUC,EAAa1B,EAAM2B,KAAKD,WACnkBE,EAAW5B,EAAMO,GAAGsB,gBAAgBC,QACpCC,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOC,IACXF,GAAUnC,EAAEsC,QAAQH,IAAaI,WAAYJ,GAAYA,EACzD3B,EAAOgC,GAAGP,KAAKQ,KAAKL,EAAMF,EAASC,GACnCA,EAAUC,EAAKD,QACfC,EAAKM,QAAUR,EAAUE,EAAKF,QAC1BA,EAAQ,GAAGS,KACXP,EAAKQ,QAAUV,EAAQ,GAAGS,GAAK,cAEnCP,EAAKS,WACLT,EAAKU,cACLV,EAAKW,aACLX,EAAKY,eACLZ,EAAKa,cACLb,EAAKc,YACLd,EAAKe,gBACLf,EAAKgB,cACDhB,EAAKD,QAAQkB,UACbjB,EAAKG,WAAWe,QAEpBpD,EAAMqD,OAAOnB,IAEjBoB,QACIpD,EACAC,EACAE,EACAD,EACAW,EACAC,EACAC,GAEJgB,SACIsB,KAAM,WACNJ,UAAU,EACVK,YAAY,EACZC,aAAa,EACbC,OAAQ,KACRC,SAAU,GACVC,YAAa,GACbC,aAAc,IAElBC,WAAY,SAAU7B,GAClB3B,EAAOgC,GAAGwB,WAAWvB,KAAKJ,KAAMF,GAChCE,KAAKU,aACDV,KAAKqB,aACLrB,KAAKqB,WAAWO,UAChB5B,KAAKqB,WAAa,MAEtBrB,KAAKY,eAETF,WAAY,WACR,GAAIZ,GAAUE,KAAKF,OACnBE,MAAKwB,SAAW3D,EAAM2D,SAAS1B,EAAQ0B,UAAY,IACnDxB,KAAKyB,YAAc5D,EAAM2D,SAAS1B,EAAQ2B,aAAe3B,EAAQ0B,UACjExB,KAAK0B,aAAe7D,EAAM2D,SAAS1B,EAAQ4B,cAAgB,KAE/DG,MAAO,SAAUC,GACb,MAAO9B,MAAKH,QAAQkC,WAAWD,MAEnCE,MAAO,WACH,MAAOhC,MAAKH,QAAQkC,YAExBE,SAAU,SAAUpC,GAAV,GACFqC,GAAOrE,EAAMqE,KAAK,OAClBC,EAAMxE,EAAEkC,GAASuC,QAAQ,IAAMF,EAAO,KAAKA,KAAKA,EACpD,OAAOlC,MAAKE,WAAWmC,SAASF,IAEpCG,cAAe,SAAUpC,GACrBF,KAAKF,QAAQI,WAAaA,EAC1BF,KAAKS,cACDT,KAAKF,QAAQkB,UACbd,EAAWe,SAGnBsB,kBAAmB,WACf,GAAIxC,GAAOC,IACXD,GAAKG,WAAWsC,OAAOzE,EAAQgC,EAAK0C,iBAAiBD,OAAOjE,EAAUwB,EAAK2C,kBAAkBF,OAAOhE,EAAOuB,EAAK4C,gBAEpHlC,YAAa,WACT,GAAIV,GAAOC,IACPD,GAAKG,YAAcH,EAAK0C,gBACxB1C,EAAKwC,qBAELxC,EAAK0C,gBAAkBtD,EAAMY,EAAK6C,QAAS7C,GAC3CA,EAAK2C,iBAAmBvD,EAAMY,EAAK8C,UAAW9C,GAC9CA,EAAK4C,cAAgBxD,EAAMY,EAAK+C,OAAQ/C,IAE5CA,EAAKG,WAAaX,EAAWwD,OAAOhD,EAAKD,QAAQI,YAAY8C,KAAKjF,EAAQgC,EAAK0C,iBAAiBO,KAAKzE,EAAUwB,EAAK2C,kBAAkBM,KAAKxE,EAAOuB,EAAK4C,gBAE3JE,UAAW,SAAUI,GACjB,GAAIpD,GAAUG,KAAKH,OACnBP,GAASO,EAASoD,GAAUC,SAAS,KAEzCJ,OAAQ,WACJxD,EAASU,KAAKH,SAAS,IAE3BW,SAAU,WACN,GAAIe,GAASvB,KAAKF,QAAQyB,MAC1BvB,MAAKH,QAAQsD,SAAS,uBAAuBjB,KAAK,OAAQ,WACtDX,GACAvB,KAAKH,QAAQuD,IAAI,SAAU7B,IAGnCqB,QAAS,SAAUS,GACf,GAAgD7D,GAAMwC,EAAOsB,EAAiBC,EAAKC,EAA/EzD,EAAOC,KAAMyD,EAAO1D,EAAKG,WAAWuD,OAA2BC,EAAO,GAAiBlC,EAAWzB,EAAKyB,SAAUC,EAAc1B,EAAK0B,YAAakC,EAASvE,IAAiBwE,EAAgB7D,EAAK8D,wBAAyBC,EAAQF,EAAgB7D,EAAKgE,wBAA0B,EAAGC,EAAajE,EAAKD,QAAQkE,UAEjT,IADAX,EAAIA,MACa,eAAbA,EAAEvB,OAwBF,MAvBK/B,GAAKkE,qBAAwBlE,EAAKmE,WACnC1E,EAAO6D,EAAErB,MAAM,GACfsB,EAAOvD,EAAKiC,QAAQmC,OAAO,IAAMtG,EAAMqE,KAAK,OAAS,IAAM1C,EAAK2C,IAAM,KAClEmB,EAAKE,OAAS,IACdD,EAAMD,EAAKQ,QACX/D,EAAKqE,QAAQ,UAAW,WACpB,OAASC,UAAWf,MAExBA,EAAKgB,YAAY9C,EAAShC,IAC1B8D,EAAOvD,EAAKiC,QAAQuC,GAAGhB,GACvBD,EAAKpB,KAAKrE,EAAMqE,KAAK,OAAQ1C,EAAK2C,KAClCpC,EAAKqE,QAAQ,UAAW,WACpB,OACIC,UAAWf,GACX9D,OAASyC,SAAUzC,OAG3BO,EAAKyE,QAAQ,cACTlB,KAAMA,EACN9D,KAAMA,MAIlB,CAEJ,KAAIO,EAAKyE,QAAQtG,GACT4D,OAAQuB,EAAEvB,QAAU,SACpBE,MAAOqB,EAAErB,MACT8B,MAAOT,EAAES,QAHjB,CAWA,IAJA/D,EAAK0E,cAAc,WACdb,GACD7D,EAAK2E,mBAEJnB,EAAMO,EAAON,EAASC,EAAKD,OAAQD,EAAMC,EAAQD,IAE9CG,GADAH,EAAM,EACE9B,EAAYgC,EAAKF,IAEjB/B,EAASiC,EAAKF,GAS9B,KANIK,EACA7D,EAAKF,QAAQ8E,OAAOjB,GAEpB3D,EAAKF,QAAQ6D,KAAKA,GAEtB1B,EAAQjC,EAAKiC,QAAQ4C,IAAI,mBACpBrB,EAAMO,EAAON,EAASC,EAAKD,OAAQD,EAAMC,EAAQD,IAClDvB,EAAMuC,GAAGhB,GAAKrB,KAAKrE,EAAMqE,KAAK,OAAQuB,EAAKF,GAAKpB,KAAKD,KAAK,OAAQ,UAAUA,KAAK,gBAAiB,QAElGnC,GAAKF,QAAQ,KAAO8D,GAAU5D,EAAKD,QAAQwB,cACvCvB,EAAK8E,WACL9E,EAAK+E,QAAQ/E,EAAK+E,UAAUC,QAEvBf,GACDjE,EAAK+E,QAAQ9C,EAAMuC,GAAG,KAIlCxE,EAAK0E,cAAc,WACnB1E,EAAK8C,WAAU,GACf9C,EAAK8D,wBAA0B,KAC/B9D,EAAKyE,QAAQvG,GACT6D,OAAQuB,EAAEvB,QAAU,SACpBE,MAAOqB,EAAErB,MACT8B,MAAOT,EAAES,UAGjBjD,UAAW,WACP,GAAmDmE,GAAUC,EAAzDlF,EAAOC,KAAMkF,EAAWnF,EAAKD,QAAQoF,QACrCvH,GAAEwH,cAAcD,KAChBD,EAAUC,EAASD,QACnBD,EAAWrH,EAAEgC,UAAWuF,GACpBhF,WAAYH,EAAKG,WACjB+E,QAAS,OAEblF,EAAKqF,MAAQ,GAAIvH,GAAMO,GAAGiH,MAAM1H,EAAE,IAAMsH,GAAUD,KAG1DpE,YAAa,WACT,GAAiB0E,GAAOR,EAApB/E,EAAOC,KAAsBqB,EAAatB,EAAKD,QAAQuB,WAAYC,EAAcvB,EAAKD,QAAQwB,WAC9FD,KACAiE,EAAQzH,EAAMO,GAAGmH,WAAWC,aAAanE,GAAYoE,SACrD1F,EAAKsB,WAAa,GAAIxD,GAAMO,GAAGmH,WAAWxF,EAAKF,SAC3C6F,MAAM,EACND,SAAUH,EACVnB,OAAQ7F,EACRqH,OAAQ,WACJ5F,EAAKyE,QAAQzG,MAGjBuD,GACAvB,EAAKF,QAAQ+F,GAAG,UAAY1G,EAAI,SAAUmE,GACtC,GAAIA,EAAEwC,UAAYxH,EAAKyH,SAAU,CAK7B,GAJAhB,EAAU/E,EAAK+E,UACXzB,EAAE0C,QAAU1C,EAAE2C,eACd3C,EAAE4C,iBAEFX,EACA,GAAKjC,EAAE6C,SAGH,GAAIpB,GAAWA,EAAQqB,SAASzH,GAE5B,MADAoG,GAAQsB,YAAY1H,GACpB,MAJJqB,GAAKsB,WAAWgF,YAQpBtG,GAAKsB,WAAWgF,OAEpBtG,GAAKsB,WAAWiF,MAAMxB,QAM1C/D,YAAa,WAAA,GAUGwF,GATRxG,EAAOC,KACPgE,EAAajE,EAAKD,QAAQkE,UAC1BA,KACAjE,EAAKF,QAAQuD,KACToD,aAAc,SACdC,SAAY,WACZC,6BAA8B,UAEf,YAAf1C,IACIuC,EAAmBxG,EAAK4G,iBAAmB5G,EAAKG,WAAWJ,QAAQ8G,SACvE7G,EAAKF,QAAQgH,IAAI,SAAW3H,GAAI0G,GAAG,SAAW1G,EAAI,WAC1Cc,KAAK8G,UAAY9G,KAAK+G,aAAe/G,KAAKgH,oBAAwBjH,EAAK8D,yBAA2B9D,EAAK4G,iBAAmB5G,EAAKG,WAAW+G,UAC1IlH,EAAKgE,wBAA0BhE,EAAK4G,iBACpC5G,EAAK4G,iBAAmB5G,EAAKgE,wBAA0BwC,EACvDxG,EAAKG,WAAWJ,QAAQoH,SAAU,EAClCnH,EAAK8D,yBAA0B,EAC/B9D,EAAKG,WAAW0G,SAAS7G,EAAK4G,wBAMlD7B,QAAS,SAAUqC,GACf,GAAIpH,GAAOC,KAAMH,EAAUE,EAAKF,QAASiF,EAAU/E,EAAKqH,SAAU9G,EAAKP,EAAKQ,OAC5E,OAAI4G,KAAcvJ,EACPkH,GAEPA,GAAWA,EAAQ,KACfA,EAAQ,GAAGxE,KAAOA,GAClBwE,EAAQuC,WAAW,MAEvBvC,EAAQsB,YAAY3H,GACpBoB,EAAQwH,WAAW,0BAEnBF,GAAaA,EAAU,KACvB7G,EAAK6G,EAAU,GAAG7G,IAAMA,EACxBP,EAAKuH,UAAUH,EAAU,IACzBtH,EAAQqC,KAAK,wBAAyB5B,GACtC6G,EAAUhE,SAAS1E,GAASyD,KAAK,KAAM5B,IAE3CP,EAAKqH,SAAWD,EAbhB,IAeJG,UAAW,SAAUzH,GAAV,GACU0H,GAObC,EAPAzH,EAAOC,KAAiByH,GAAkB,EAAOC,EAAS,QAC1B,SAAhC3H,EAAKM,QAAQ+C,IAAI,aAAyBrD,EAAKM,QAAQ+C,IAAI,aAAesE,GAAU3H,EAAKM,QAAQ+C,IAAI,eAAiBsE,EACtHH,EAAYxH,EAAKM,QAAQ,IAEzBkH,EAAYzJ,OACZ2J,GAAkB,GAElBD,EAAsB,SAAUG,EAAWC,GAC3C,GAAIC,GAAgBJ,EAAkB9J,EAAEkC,GAASiI,SAASH,EAAUI,eAAiBlI,EAAQ,SAAW8H,GAAYK,EAAmBnI,EAAQ,SAAW+H,GAAYK,EAAwBtK,EAAE4J,GAAWG,EAASC,KAAcO,EAAqBvK,EAAE4J,GAAWK,EAAUG,gBAC1QF,GAAgBG,EAAmBC,EAAwBC,EAC3DvK,EAAE4J,GAAWG,EAASC,GAAWE,EAAgBG,EAAmBE,GAC7DL,EAAgBI,GACvBtK,EAAE4J,GAAWG,EAASC,GAAWE,IAGzCL,EAAoB,MAAO,UAC3BA,EAAoB,OAAQ,UAEhC7G,aAAc,WACV,GAAIZ,GAAOC,KAAMsB,EAAcvB,EAAKD,QAAQwB,YAAazB,EAAUE,EAAKF,QAASsI,EAAgB,SAAU9E,GACnGtD,EAAK+E,QAAQnH,EAAE0F,EAAE2C,gBACZrI,EAAE0F,EAAE0C,QAAQqC,GAAG,wCAChBvK,EAAMwK,aAAaxI,GAG3ByB,KACAvB,EAAKuI,YACLzI,EAAQ+F,GAAG,QAAU1G,EAAI,WACrB,GAAI4F,GAAU/E,EAAKqH,QACdtC,IAAYA,EAAQsD,GAAG,cACxBtD,EAAU/E,EAAK8B,MAAM,UAEzB9B,EAAK+E,QAAQA,KACdc,GAAG,WAAa1G,EAAI,WACfa,EAAKqH,UACLrH,EAAKqH,SAAShB,YAAY3H,KAE/BmH,GAAG,UAAY1G,EAAI,SAAUmE,GAAV,GACsQE,GAmD5QgF,EAnDRC,EAAMnF,EAAEwC,QAASf,EAAU/E,EAAK+E,UAAWiB,EAASpI,EAAE0F,EAAE0C,QAAS0C,GAAa1C,EAAOqC,GAAG,sCAAuCM,EAAY3C,EAAOqC,GAAG,mBAAoBnC,EAAiBpI,EAAMoI,eAAgB0C,EAAW9I,EAAQ+I,KAAK,IAAMjK,GAAYgF,EAASvE,IAAsB4E,EAAajE,EAAKD,QAAQkE,UACvT,OAAKyE,IAAcC,GAAarK,EAAKwK,KAAOL,GAAOE,GAAarK,EAAKwK,KAAOL,GAAOnK,EAAKyK,OAASN,GAGjG,GAAInK,EAAK0K,KAAOP,GAAOnK,EAAK2K,OAASR,EAC7B1D,GAAWA,EAAQ,KACnBA,EAAUA,EAAQmE,QAElBnE,GAAWA,EAAQ,GACnB/E,EAAK+E,QAAQA,GACLd,GACRjE,EAAK+E,QAAQ/E,EAAK8B,MAAM,SAE5BoE,EAAe5C,OACZ,IAAIhF,EAAK6K,OAASV,GAAOnK,EAAK8K,QAAUX,EACvCxE,EACgC,YAA5BjE,EAAKD,QAAQkE,YAA6Bc,EAAQC,OAAOvB,QAIzDsB,EAAUA,EAAQC,OACdD,GAAWA,EAAQ,IACnB/E,EAAK+E,QAAQA,KALjB/E,EAAKF,QAAQ,GAAGiH,UAAY/G,EAAKF,QAAQ,GAAGmH,aAC5CjH,EAAK8E,YAAa,IAQtBC,EAAUA,EAAQC,OAClBhF,EAAK+E,QAASA,GAAYA,EAAQ,GAA2BA,EAAtB/E,EAAK8B,MAAM,WAEtDoE,EAAe5C,OACZ,IAAIhF,EAAK+K,SAAWZ,EACvBzI,EAAK+E,QAAQ,MACb/E,EAAKG,WAAWmJ,KAAKtJ,EAAKG,WAAWmJ,OAAS,GAC9CpD,EAAe5C,OACZ,IAAIhF,EAAKiL,WAAad,EACzBzI,EAAK+E,QAAQ,MACb/E,EAAKG,WAAWmJ,KAAKtJ,EAAKG,WAAWmJ,OAAS,GAC9CpD,EAAe5C,OACZ,IAAIhF,EAAKkL,OAASf,EACrBzI,EAAK+E,QAAQ/E,EAAK8B,MAAM,UACxBoE,EAAe5C,OACZ,IAAIhF,EAAKmL,MAAQhB,EACpBzI,EAAK+E,QAAQ/E,EAAK8B,MAAM,SACxBoE,EAAe5C,OACZ,IAAIhF,EAAKyK,QAAUN,EACE,IAApBG,EAASnF,SAAiBiF,GAAaC,IACvCnF,EAAMxD,EAAKiC,QAAQ8B,MAAM6E,GACrBhF,GACAA,EAAO8F,OAEX1J,EAAK2J,OACDnB,EAAa,WACbxI,EAAKF,QAAQ2E,QAAQ,SACrBzE,EAAK+E,QAAQ/E,EAAKiC,QAAQuC,GAAGhB,KAEjCxD,EAAK4J,IAAI,YAAapB,IACe,KAA9BxI,EAAKD,QAAQ4B,cACpB3B,EAAK6J,KAAK9E,OAEX,IAAIzG,EAAKwK,MAAQL,EAAK,CAEzB,GADAG,EAAW9I,EAAQ+I,KAAK,IAAMjK,GACN,IAApBgK,EAASnF,OACT,MAEJD,GAAMxD,EAAKiC,QAAQ8B,MAAM6E,GACzB5I,EAAK8J,SACL9J,EAAKF,QAAQ2E,QAAQ,SACrBzE,EAAK+E,QAAQ/E,EAAKiC,QAAQuC,GAAGhB,OAGrC1D,EAAQ+F,GAAG7G,EAAYG,EAAK,IAAMD,EAAaC,EAAIZ,EAAea,EAAMgJ,EAAepI,MAG/F+J,eAAgB,WACZ,GAAI/J,GAAOC,IACXD,GAAKsB,WAAWgF,QAChBtG,EAAKyE,QAAQzG,IAEjBgM,OAAQ,SAAU/H,GACd,GAAIjC,GAAOC,KAAMqB,EAAatB,EAAKsB,UAEnC,OADAW,GAAQrE,EAAEqE,GACNA,EAAMwB,QACDnC,EAAWvB,QAAQ2F,WACpBpE,EAAWgF,QACXrE,EAAQA,EAAMgI,SAElB3I,EAAWiF,MAAMtE,GACjB,GAEGX,EAAWiF,SAEtB5B,iBAAkB,WACd,GAAI3E,GAAOC,IACPD,GAAKmE,WACLnE,EAAKmE,SAAStC,gBACP7B,GAAKmE,WAGpB+F,kBAAmB,SAAUpK,GACzB,GAAIsC,GAAMtC,EAAQqC,KAAKrE,EAAMqE,KAAK,OAClC,OAAOlC,MAAKE,WAAWmC,SAASF,IAEpC+H,eAAgB,WACZ,GAA2C1K,GAAM8D,EAAMQ,EAAnD/D,EAAOC,KAAMkE,EAAWnE,EAAKmE,SAA6B1C,EAAWzB,EAAKyB,QAwB9E,OAvBI0C,KACIA,EAASrE,QAAQiE,QAAU,IAC3BtC,EAAWzB,EAAK0B,aAEpB1B,EAAKqE,QAAQ,UAAW,WACpB,OAASC,UAAWH,EAASrE,YAEjCL,EAAOO,EAAKkK,kBAAkB/F,EAASrE,SACvCE,EAAK2E,mBACLZ,EAAQI,EAASrE,QAAQiE,QACzBI,EAASrE,QAAQyE,YAAY9C,EAAShC,IACtC8D,EAAOvD,EAAKiC,QAAQuC,GAAGT,GACvBR,EAAKpB,KAAKrE,EAAMqE,KAAK,OAAQ1C,EAAK2C,KAC9BpC,EAAKkE,qBACLpG,EAAMmF,KAAKM,EAAM9D,GAErBO,EAAKqE,QAAQ,UAAW,WACpB,OACIC,UAAWf,GACX9D,OAASyC,SAAUzC,SAIxB,GAEXoK,KAAM,SAAUtG,GACZ,GAAsDiE,GAA2BzD,EAA7E/D,EAAOC,KAAMR,EAAOO,EAAKkK,kBAAkB3G,GAAkBnB,EAAM3C,EAAK2C,GAC5EpC,GAAK8J,SACLvG,EAAOvD,EAAKiC,QAAQmC,OAAO,IAAMtG,EAAMqE,KAAK,OAAS,IAAMC,EAAM,KACjE2B,EAAQR,EAAKQ,QACbR,EAAKgB,YAAYvE,EAAK2B,aAAalC,IACnC+H,EAAYxH,EAAKiC,QAAQuC,GAAGT,GAAOX,SAASxE,GAAWuD,KAAKrE,EAAMqE,KAAK,OAAQ1C,EAAK2C,KACpFpC,EAAKmE,SAAWqD,EAAU4C,eACtBC,MAAO5K,EACP6K,gBAAgB,EAChBC,eAAe,EACfvE,OAAQhG,IACTP,KAAK,iBACRO,EAAKyE,QAAQ5F,GACTwL,MAAO5K,EACP8D,KAAMiE,KAGdmC,KAAM,WAAA,GACyCU,GAIvC7C,EAJAxH,EAAOC,KAAMkE,EAAWnE,EAAKmE,QAC5BA,KAGDqD,EAAYrD,EAASrE,QACzBuK,EAAQrK,EAAKkK,kBAAkB1C,GAC3BrD,EAASqG,QAAUxK,EAAKyE,QAAQ1F,GAC5BsL,MAAOA,EACP9G,KAAMiE,MAEVxH,EAAKmK,iBACLnK,EAAKG,WAAWsK,UAGxBC,OAAQ,SAAUnH,GACd,GAAIvD,GAAOC,KAAME,EAAaH,EAAKG,WAAYV,EAAOO,EAAKkK,kBAAkB3G,EACzEvD,GAAKmE,WACLhE,EAAWwK,cAAc3K,EAAKkK,kBAAkBlK,EAAKmE,SAASrE,UAC9DE,EAAKmK,kBAEJnK,EAAKyE,QAAQ3F,GACVuL,MAAO5K,EACP8D,KAAMA,MAEVA,EAAKqH,OACLzK,EAAWuK,OAAOjL,GAClBU,EAAWsK,SAGnBI,IAAK,WACD,GAAiB3I,GAAblC,EAAOC,KAAgBE,EAAaH,EAAKG,WAAY4D,EAAQ5D,EAAW2K,SAAS3K,EAAWuD,YAAc,GAC1GK,GAAQ,IACRA,EAAQ,GAEZ/D,EAAK8J,SACL5H,EAAW/B,EAAW4K,OAAOhH,MAC7B/D,EAAK6J,KAAK7J,EAAKF,QAAQ+I,KAAK,cAAiB3G,EAASE,IAAM,QAEhE0H,OAAQ,WAAA,GAGItC,GACA6C,EAHJrK,EAAOC,KAAME,EAAaH,EAAKG,UAC/BH,GAAKmE,WACDqD,EAAYxH,EAAKmE,SAASrE,QAC1BuK,EAAQrK,EAAKkK,kBAAkB1C,GAC9BxH,EAAKyE,QAAQxG,GACVoM,MAAOA,EACP7C,UAAWA,MAEfrH,EAAWwK,cAAcN,GACzBrK,EAAKmK,oBAIjBpJ,cAAe,WACX,GAAIf,GAAOC,KAAM+K,EAAchM,EAAYG,EAAI8L,EAAe/L,EAAaC,EAAI+L,EAAUjM,EAAQE,CACjGa,GAAKF,QAAQ+F,GAAGmF,EAAc,IAAMC,EAAc,iBAAkB,SAAU3H,GAC1EA,EAAE4C,gBACF,IAAI3C,GAAO3F,EAAEqC,MAAMoC,QAAQ,IAAMvE,EAAMqE,KAAK,OAAS,IACrDgJ,YAAW,WACPnL,EAAK6J,KAAKtG,OAGlBvD,EAAKF,QAAQ+F,GAAGmF,EAAc,IAAMC,EAAc,mBAAoB,SAAU3H,GAC5EA,EAAE4C,gBACF,IAAI3C,GAAO3F,EAAEqC,MAAMoC,QAAQ,IAAMvE,EAAMqE,KAAK,OAAS,IACrDgJ,YAAW,WACPnL,EAAK0K,OAAOnH,OAGpBvD,EAAKF,QAAQ+F,GAAGqF,EAAS,mBAAoB,SAAU5H,GACnDtD,EAAK2J,OACLrG,EAAE4C,mBAENlG,EAAKF,QAAQ+F,GAAGqF,EAAS,mBAAoB,SAAU5H,GACnDtD,EAAK8J,SACLxG,EAAE4C,oBAGVrE,QAAS,WACL,GAAI7B,GAAOC,IACX7B,GAAOgC,GAAGyB,QAAQxB,KAAKL,GACvBA,EAAKwC,oBACLxC,EAAK2E,mBACL3E,EAAKF,QAAQgH,IAAI3H,GACjBa,EAAK8D,wBAA0B9D,EAAK4G,iBAAmB5G,EAAKgE,wBAA0BhE,EAAK8E,WAAa,KACpG9E,EAAKqF,OACLrF,EAAKqF,MAAMxD,UAEf/D,EAAM+D,QAAQ7B,EAAKF,WAG3BhC,GAAMO,GAAG+M,OAAO1L,IAClB3B,OAAOD,MAAMuN,QACRtN,OAAOD,OACE,kBAAVH,SAAwBA,OAAO2N,IAAM3N,OAAS,SAAU4N,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.listview.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.listview', [\n        'kendo.data',\n        'kendo.editable',\n        'kendo.selectable'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'listview',\n        name: 'ListView',\n        category: 'web',\n        description: 'The ListView widget offers rich support for interacting with data.',\n        depends: ['data'],\n        features: [\n            {\n                id: 'listview-editing',\n                name: 'Editing',\n                description: 'Support for record editing',\n                depends: ['editable']\n            },\n            {\n                id: 'listview-selection',\n                name: 'Selection',\n                description: 'Support for selection',\n                depends: ['selectable']\n            }\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, CHANGE = 'change', CANCEL = 'cancel', DATABOUND = 'dataBound', DATABINDING = 'dataBinding', Widget = kendo.ui.Widget, keys = kendo.keys, FOCUSSELECTOR = '>*:not(.k-loading-mask)', PROGRESS = 'progress', ERROR = 'error', FOCUSED = 'k-state-focused', SELECTED = 'k-state-selected', KEDITITEM = 'k-edit-item', EDIT = 'edit', REMOVE = 'remove', SAVE = 'save', MOUSEDOWN = 'mousedown', CLICK = 'click', TOUCHSTART = 'touchstart', NS = '.kendoListView', proxy = $.proxy, activeElement = kendo._activeElement, progress = kendo.ui.progress, DataSource = kendo.data.DataSource;\n        var ListView = kendo.ui.DataBoundWidget.extend({\n            init: function (element, options) {\n                var that = this;\n                options = $.isArray(options) ? { dataSource: options } : options;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                that.wrapper = element = that.element;\n                if (element[0].id) {\n                    that._itemId = element[0].id + '_lv_active';\n                }\n                that._element();\n                that._dataSource();\n                that._templates();\n                that._navigatable();\n                that._selectable();\n                that._pageable();\n                that._crudHandlers();\n                that._scrollable();\n                if (that.options.autoBind) {\n                    that.dataSource.fetch();\n                }\n                kendo.notify(that);\n            },\n            events: [\n                CHANGE,\n                CANCEL,\n                DATABINDING,\n                DATABOUND,\n                EDIT,\n                REMOVE,\n                SAVE\n            ],\n            options: {\n                name: 'ListView',\n                autoBind: true,\n                selectable: false,\n                navigatable: false,\n                height: null,\n                template: '',\n                altTemplate: '',\n                editTemplate: ''\n            },\n            setOptions: function (options) {\n                Widget.fn.setOptions.call(this, options);\n                this._templates();\n                if (this.selectable) {\n                    this.selectable.destroy();\n                    this.selectable = null;\n                }\n                this._selectable();\n            },\n            _templates: function () {\n                var options = this.options;\n                this.template = kendo.template(options.template || '');\n                this.altTemplate = kendo.template(options.altTemplate || options.template);\n                this.editTemplate = kendo.template(options.editTemplate || '');\n            },\n            _item: function (action) {\n                return this.element.children()[action]();\n            },\n            items: function () {\n                return this.element.children();\n            },\n            dataItem: function (element) {\n                var attr = kendo.attr('uid');\n                var uid = $(element).closest('[' + attr + ']').attr(attr);\n                return this.dataSource.getByUid(uid);\n            },\n            setDataSource: function (dataSource) {\n                this.options.dataSource = dataSource;\n                this._dataSource();\n                if (this.options.autoBind) {\n                    dataSource.fetch();\n                }\n            },\n            _unbindDataSource: function () {\n                var that = this;\n                that.dataSource.unbind(CHANGE, that._refreshHandler).unbind(PROGRESS, that._progressHandler).unbind(ERROR, that._errorHandler);\n            },\n            _dataSource: function () {\n                var that = this;\n                if (that.dataSource && that._refreshHandler) {\n                    that._unbindDataSource();\n                } else {\n                    that._refreshHandler = proxy(that.refresh, that);\n                    that._progressHandler = proxy(that._progress, that);\n                    that._errorHandler = proxy(that._error, that);\n                }\n                that.dataSource = DataSource.create(that.options.dataSource).bind(CHANGE, that._refreshHandler).bind(PROGRESS, that._progressHandler).bind(ERROR, that._errorHandler);\n            },\n            _progress: function (toggle) {\n                var element = this.element;\n                progress(element, toggle, { opacity: true });\n            },\n            _error: function () {\n                progress(this.element, false);\n            },\n            _element: function () {\n                var height = this.options.height;\n                this.element.addClass('k-widget k-listview').attr('role', 'listbox');\n                if (height) {\n                    this.element.css('height', height);\n                }\n            },\n            refresh: function (e) {\n                var that = this, view = that.dataSource.view(), data, items, item, html = '', idx, length, template = that.template, altTemplate = that.altTemplate, active = activeElement(), endlessAppend = that._endlessFetchInProgress, index = endlessAppend ? that._skipRerenderItemsCount : 0, scrollable = that.options.scrollable;\n                e = e || {};\n                if (e.action === 'itemchange') {\n                    if (!that._hasBindingTarget() && !that.editable) {\n                        data = e.items[0];\n                        item = that.items().filter('[' + kendo.attr('uid') + '=' + data.uid + ']');\n                        if (item.length > 0) {\n                            idx = item.index();\n                            that.angular('cleanup', function () {\n                                return { elements: [item] };\n                            });\n                            item.replaceWith(template(data));\n                            item = that.items().eq(idx);\n                            item.attr(kendo.attr('uid'), data.uid);\n                            that.angular('compile', function () {\n                                return {\n                                    elements: [item],\n                                    data: [{ dataItem: data }]\n                                };\n                            });\n                            that.trigger('itemChange', {\n                                item: item,\n                                data: data\n                            });\n                        }\n                    }\n                    return;\n                }\n                if (that.trigger(DATABINDING, {\n                        action: e.action || 'rebind',\n                        items: e.items,\n                        index: e.index\n                    })) {\n                    return;\n                }\n                that._angularItems('cleanup');\n                if (!endlessAppend) {\n                    that._destroyEditable();\n                }\n                for (idx = index, length = view.length; idx < length; idx++) {\n                    if (idx % 2) {\n                        html += altTemplate(view[idx]);\n                    } else {\n                        html += template(view[idx]);\n                    }\n                }\n                if (endlessAppend) {\n                    that.element.append(html);\n                } else {\n                    that.element.html(html);\n                }\n                items = that.items().not('.k-loading-mask');\n                for (idx = index, length = view.length; idx < length; idx++) {\n                    items.eq(idx).attr(kendo.attr('uid'), view[idx].uid).attr('role', 'option').attr('aria-selected', 'false');\n                }\n                if (that.element[0] === active && that.options.navigatable) {\n                    if (that._focusNext) {\n                        that.current(that.current().next());\n                    } else {\n                        if (!scrollable) {\n                            that.current(items.eq(0));\n                        }\n                    }\n                }\n                that._angularItems('compile');\n                that._progress(false);\n                that._endlessFetchInProgress = null;\n                that.trigger(DATABOUND, {\n                    action: e.action || 'rebind',\n                    items: e.items,\n                    index: e.index\n                });\n            },\n            _pageable: function () {\n                var that = this, pageable = that.options.pageable, settings, pagerId;\n                if ($.isPlainObject(pageable)) {\n                    pagerId = pageable.pagerId;\n                    settings = $.extend({}, pageable, {\n                        dataSource: that.dataSource,\n                        pagerId: null\n                    });\n                    that.pager = new kendo.ui.Pager($('#' + pagerId), settings);\n                }\n            },\n            _selectable: function () {\n                var that = this, multi, current, selectable = that.options.selectable, navigatable = that.options.navigatable;\n                if (selectable) {\n                    multi = kendo.ui.Selectable.parseOptions(selectable).multiple;\n                    that.selectable = new kendo.ui.Selectable(that.element, {\n                        aria: true,\n                        multiple: multi,\n                        filter: FOCUSSELECTOR,\n                        change: function () {\n                            that.trigger(CHANGE);\n                        }\n                    });\n                    if (navigatable) {\n                        that.element.on('keydown' + NS, function (e) {\n                            if (e.keyCode === keys.SPACEBAR) {\n                                current = that.current();\n                                if (e.target == e.currentTarget) {\n                                    e.preventDefault();\n                                }\n                                if (multi) {\n                                    if (!e.ctrlKey) {\n                                        that.selectable.clear();\n                                    } else {\n                                        if (current && current.hasClass(SELECTED)) {\n                                            current.removeClass(SELECTED);\n                                            return;\n                                        }\n                                    }\n                                } else {\n                                    that.selectable.clear();\n                                }\n                                that.selectable.value(current);\n                            }\n                        });\n                    }\n                }\n            },\n            _scrollable: function () {\n                var that = this;\n                var scrollable = that.options.scrollable;\n                if (scrollable) {\n                    that.element.css({\n                        'overflow-y': 'scroll',\n                        'position': 'relative',\n                        '-webkit-overflow-scrolling': 'touch'\n                    });\n                    if (scrollable === 'endless') {\n                        var originalPageSize = that._endlessPageSize = that.dataSource.options.pageSize;\n                        that.element.off('scroll' + NS).on('scroll' + NS, function () {\n                            if (this.scrollTop + this.clientHeight - this.scrollHeight >= -15 && !that._endlessFetchInProgress && that._endlessPageSize < that.dataSource.total()) {\n                                that._skipRerenderItemsCount = that._endlessPageSize;\n                                that._endlessPageSize = that._skipRerenderItemsCount + originalPageSize;\n                                that.dataSource.options.endless = true;\n                                that._endlessFetchInProgress = true;\n                                that.dataSource.pageSize(that._endlessPageSize);\n                            }\n                        });\n                    }\n                }\n            },\n            current: function (candidate) {\n                var that = this, element = that.element, current = that._current, id = that._itemId;\n                if (candidate === undefined) {\n                    return current;\n                }\n                if (current && current[0]) {\n                    if (current[0].id === id) {\n                        current.removeAttr('id');\n                    }\n                    current.removeClass(FOCUSED);\n                    element.removeAttr('aria-activedescendant');\n                }\n                if (candidate && candidate[0]) {\n                    id = candidate[0].id || id;\n                    that._scrollTo(candidate[0]);\n                    element.attr('aria-activedescendant', id);\n                    candidate.addClass(FOCUSED).attr('id', id);\n                }\n                that._current = candidate;\n            },\n            _scrollTo: function (element) {\n                var that = this, container, UseJQueryoffset = false, SCROLL = 'scroll';\n                if (that.wrapper.css('overflow') == 'auto' || that.wrapper.css('overflow') == SCROLL || that.wrapper.css('overflow-y') == SCROLL) {\n                    container = that.wrapper[0];\n                } else {\n                    container = window;\n                    UseJQueryoffset = true;\n                }\n                var scrollDirectionFunc = function (direction, dimension) {\n                    var elementOffset = UseJQueryoffset ? $(element).offset()[direction.toLowerCase()] : element['offset' + direction], elementDimension = element['client' + dimension], containerScrollAmount = $(container)[SCROLL + direction](), containerDimension = $(container)[dimension.toLowerCase()]();\n                    if (elementOffset + elementDimension > containerScrollAmount + containerDimension) {\n                        $(container)[SCROLL + direction](elementOffset + elementDimension - containerDimension);\n                    } else if (elementOffset < containerScrollAmount) {\n                        $(container)[SCROLL + direction](elementOffset);\n                    }\n                };\n                scrollDirectionFunc('Top', 'Height');\n                scrollDirectionFunc('Left', 'Width');\n            },\n            _navigatable: function () {\n                var that = this, navigatable = that.options.navigatable, element = that.element, clickCallback = function (e) {\n                        that.current($(e.currentTarget));\n                        if (!$(e.target).is(':button,a,:input,a>.k-icon,textarea')) {\n                            kendo.focusElement(element);\n                        }\n                    };\n                if (navigatable) {\n                    that._tabindex();\n                    element.on('focus' + NS, function () {\n                        var current = that._current;\n                        if (!current || !current.is(':visible')) {\n                            current = that._item('first');\n                        }\n                        that.current(current);\n                    }).on('focusout' + NS, function () {\n                        if (that._current) {\n                            that._current.removeClass(FOCUSED);\n                        }\n                    }).on('keydown' + NS, function (e) {\n                        var key = e.keyCode, current = that.current(), target = $(e.target), canHandle = !target.is(':button,textarea,a,a>.t-icon,input'), isTextBox = target.is(':text,:password'), preventDefault = kendo.preventDefault, editItem = element.find('.' + KEDITITEM), active = activeElement(), idx, scrollable = that.options.scrollable;\n                        if (!canHandle && !isTextBox && keys.ESC != key || isTextBox && keys.ESC != key && keys.ENTER != key) {\n                            return;\n                        }\n                        if (keys.UP === key || keys.LEFT === key) {\n                            if (current && current[0]) {\n                                current = current.prev();\n                            }\n                            if (current && current[0]) {\n                                that.current(current);\n                            } else if (!scrollable) {\n                                that.current(that._item('last'));\n                            }\n                            preventDefault(e);\n                        } else if (keys.DOWN === key || keys.RIGHT === key) {\n                            if (scrollable) {\n                                if (that.options.scrollable === 'endless' && !current.next().length) {\n                                    that.element[0].scrollTop = that.element[0].scrollHeight;\n                                    that._focusNext = true;\n                                } else {\n                                    current = current.next();\n                                    if (current && current[0]) {\n                                        that.current(current);\n                                    }\n                                }\n                            } else {\n                                current = current.next();\n                                that.current(!current || !current[0] ? that._item('first') : current);\n                            }\n                            preventDefault(e);\n                        } else if (keys.PAGEUP === key) {\n                            that.current(null);\n                            that.dataSource.page(that.dataSource.page() - 1);\n                            preventDefault(e);\n                        } else if (keys.PAGEDOWN === key) {\n                            that.current(null);\n                            that.dataSource.page(that.dataSource.page() + 1);\n                            preventDefault(e);\n                        } else if (keys.HOME === key) {\n                            that.current(that._item('first'));\n                            preventDefault(e);\n                        } else if (keys.END === key) {\n                            that.current(that._item('last'));\n                            preventDefault(e);\n                        } else if (keys.ENTER === key) {\n                            if (editItem.length !== 0 && (canHandle || isTextBox)) {\n                                idx = that.items().index(editItem);\n                                if (active) {\n                                    active.blur();\n                                }\n                                that.save();\n                                var focusAgain = function () {\n                                    that.element.trigger('focus');\n                                    that.current(that.items().eq(idx));\n                                };\n                                that.one('dataBound', focusAgain);\n                            } else if (that.options.editTemplate !== '') {\n                                that.edit(current);\n                            }\n                        } else if (keys.ESC === key) {\n                            editItem = element.find('.' + KEDITITEM);\n                            if (editItem.length === 0) {\n                                return;\n                            }\n                            idx = that.items().index(editItem);\n                            that.cancel();\n                            that.element.trigger('focus');\n                            that.current(that.items().eq(idx));\n                        }\n                    });\n                    element.on(MOUSEDOWN + NS + ' ' + TOUCHSTART + NS, FOCUSSELECTOR, proxy(clickCallback, that));\n                }\n            },\n            clearSelection: function () {\n                var that = this;\n                that.selectable.clear();\n                that.trigger(CHANGE);\n            },\n            select: function (items) {\n                var that = this, selectable = that.selectable;\n                items = $(items);\n                if (items.length) {\n                    if (!selectable.options.multiple) {\n                        selectable.clear();\n                        items = items.first();\n                    }\n                    selectable.value(items);\n                    return;\n                }\n                return selectable.value();\n            },\n            _destroyEditable: function () {\n                var that = this;\n                if (that.editable) {\n                    that.editable.destroy();\n                    delete that.editable;\n                }\n            },\n            _modelFromElement: function (element) {\n                var uid = element.attr(kendo.attr('uid'));\n                return this.dataSource.getByUid(uid);\n            },\n            _closeEditable: function () {\n                var that = this, editable = that.editable, data, item, index, template = that.template;\n                if (editable) {\n                    if (editable.element.index() % 2) {\n                        template = that.altTemplate;\n                    }\n                    that.angular('cleanup', function () {\n                        return { elements: [editable.element] };\n                    });\n                    data = that._modelFromElement(editable.element);\n                    that._destroyEditable();\n                    index = editable.element.index();\n                    editable.element.replaceWith(template(data));\n                    item = that.items().eq(index);\n                    item.attr(kendo.attr('uid'), data.uid);\n                    if (that._hasBindingTarget()) {\n                        kendo.bind(item, data);\n                    }\n                    that.angular('compile', function () {\n                        return {\n                            elements: [item],\n                            data: [{ dataItem: data }]\n                        };\n                    });\n                }\n                return true;\n            },\n            edit: function (item) {\n                var that = this, data = that._modelFromElement(item), container, uid = data.uid, index;\n                that.cancel();\n                item = that.items().filter('[' + kendo.attr('uid') + '=' + uid + ']');\n                index = item.index();\n                item.replaceWith(that.editTemplate(data));\n                container = that.items().eq(index).addClass(KEDITITEM).attr(kendo.attr('uid'), data.uid);\n                that.editable = container.kendoEditable({\n                    model: data,\n                    clearContainer: false,\n                    errorTemplate: false,\n                    target: that\n                }).data('kendoEditable');\n                that.trigger(EDIT, {\n                    model: data,\n                    item: container\n                });\n            },\n            save: function () {\n                var that = this, editable = that.editable, model;\n                if (!editable) {\n                    return;\n                }\n                var container = editable.element;\n                model = that._modelFromElement(container);\n                if (editable.end() && !that.trigger(SAVE, {\n                        model: model,\n                        item: container\n                    })) {\n                    that._closeEditable();\n                    that.dataSource.sync();\n                }\n            },\n            remove: function (item) {\n                var that = this, dataSource = that.dataSource, data = that._modelFromElement(item);\n                if (that.editable) {\n                    dataSource.cancelChanges(that._modelFromElement(that.editable.element));\n                    that._closeEditable();\n                }\n                if (!that.trigger(REMOVE, {\n                        model: data,\n                        item: item\n                    })) {\n                    item.hide();\n                    dataSource.remove(data);\n                    dataSource.sync();\n                }\n            },\n            add: function () {\n                var that = this, dataItem, dataSource = that.dataSource, index = dataSource.indexOf((dataSource.view() || [])[0]);\n                if (index < 0) {\n                    index = 0;\n                }\n                that.cancel();\n                dataItem = dataSource.insert(index, {});\n                that.edit(that.element.find('[data-uid=\\'' + dataItem.uid + '\\']'));\n            },\n            cancel: function () {\n                var that = this, dataSource = that.dataSource;\n                if (that.editable) {\n                    var container = that.editable.element;\n                    var model = that._modelFromElement(container);\n                    if (!that.trigger(CANCEL, {\n                            model: model,\n                            container: container\n                        })) {\n                        dataSource.cancelChanges(model);\n                        that._closeEditable();\n                    }\n                }\n            },\n            _crudHandlers: function () {\n                var that = this, mousedownNs = MOUSEDOWN + NS, touchstartNs = TOUCHSTART + NS, clickNs = CLICK + NS;\n                that.element.on(mousedownNs + ' ' + touchstartNs, '.k-edit-button', function (e) {\n                    e.preventDefault();\n                    var item = $(this).closest('[' + kendo.attr('uid') + ']');\n                    setTimeout(function () {\n                        that.edit(item);\n                    });\n                });\n                that.element.on(mousedownNs + ' ' + touchstartNs, '.k-delete-button', function (e) {\n                    e.preventDefault();\n                    var item = $(this).closest('[' + kendo.attr('uid') + ']');\n                    setTimeout(function () {\n                        that.remove(item);\n                    });\n                });\n                that.element.on(clickNs, '.k-update-button', function (e) {\n                    that.save();\n                    e.preventDefault();\n                });\n                that.element.on(clickNs, '.k-cancel-button', function (e) {\n                    that.cancel();\n                    e.preventDefault();\n                });\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that._unbindDataSource();\n                that._destroyEditable();\n                that.element.off(NS);\n                that._endlessFetchInProgress = that._endlessPageSize = that._skipRerenderItemsCount = that._focusNext = null;\n                if (that.pager) {\n                    that.pager.destroy();\n                }\n                kendo.destroy(that.element);\n            }\n        });\n        kendo.ui.plugin(ListView);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}