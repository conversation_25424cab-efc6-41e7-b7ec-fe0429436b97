{"version": 3, "sources": ["kendo.dataviz.sparkline.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "hide", "children", "idx", "child", "state", "display", "show", "wrapNumber", "dataviz", "isNumber", "constants", "Chart", "elementSize", "TOP_OFFSET", "SharedTooltip$1", "DEAULT_BAR_WIDTH", "DEAULT_BULLET_WIDTH", "NO_CROSSHAIR", "Sparkline", "SharedTooltip", "_slotAnchor", "coords", "slot", "point", "axis", "<PERSON><PERSON><PERSON>", "categoryAxis", "vertical", "align", "horizontal", "Point", "x2", "center", "y", "x", "_defaultAnchor", "BAR", "BULLET", "_setElementClass", "element", "addClass", "_initElement", "fn", "call", "_initialWidth", "Math", "floor", "_resize", "childNodes", "_modelOptions", "chartOptions", "stage", "_surfaceWrap", "displayState", "space", "innerHTML", "_autoWidth", "transitions", "chartArea", "inline", "surface", "resize", "_createPlotArea", "skipSeries", "_calculateWidth", "<PERSON><PERSON><PERSON>", "firstSeries", "pointsCount", "margin", "getSpacing", "charts", "total", "series", "type", "PIE", "categoriesCount", "isStacked", "inArray", "COLUMN", "VERTICAL_BULLET", "seriesOptions", "max", "pointWidth", "left", "right", "_createSharedTooltip", "_plotArea", "normalizeOptions", "userOptions", "isArray", "seriesDefaults", "data", "crosshair", "visible", "setDefaultOptions", "axisDefaults", "majorGridLines", "valueAxis", "narrowRange", "area", "line", "bar", "stack", "padding", "overlay", "gradient", "highlight", "border", "markers", "tooltip", "shared", "legend", "panes", "clip", "SparklineTooltip", "ui", "KendoSparkline", "ChartInstanceObserver", "ObservableArray", "_createChart", "themeOptions", "_instance", "observer", "sender", "rtl", "_isRtl", "_createTooltip", "name", "plugin", "<PERSON><PERSON><PERSON>", "animation", "duration", "_hideElement", "remove"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,qCAAsC,uBAAwBD,IACvE,YACG,WAwCG,QAASoF,GAAKC,GAAd,GAEaC,GACDC,EAFJC,IACJ,KAASF,EAAM,EAAGA,EAAMD,EAASjE,OAAQkE,IACjCC,EAAQF,EAASC,GACrBE,EAAMF,GAAOC,EAAM5D,MAAM8D,QACzBF,EAAM5D,MAAM8D,QAAU,MAE1B,OAAOD,GAEX,QAASE,GAAKL,EAAUG,GACpB,IAAK,GAAIF,GAAM,EAAGA,EAAMD,EAASjE,OAAQkE,IACrCD,EAASC,GAAK3D,MAAM8D,QAAUD,EAAMF,GAG5C,QAASK,GAAW5C,GAChB,MAAO6C,GAAQC,SAAS9C,IAAUA,GAASA,EAvDlD,GAEO6C,GACAE,EACAC,EACAC,EACAlB,EACAmB,EACAC,EA0BAC,EACAC,EACAC,EAqBAC,CAxDJnE,QAAOC,MAAMwD,QAAUzD,OAAOC,MAAMwD,YAChCA,EAAUxD,MAAMwD,QAChBE,EAAYF,EAAQE,UACpBC,EAAQH,EAAQG,MAChBC,EAAcJ,EAAQI,YACtBlB,EAAac,EAAQd,WACrBmB,KACAC,EAAkBN,EAAQW,cAAchE,QACxCiE,YAAa,SAAUC,EAAQC,GAAlB,GAULC,GATAC,EAAOlE,KAAKmE,SAASC,aACrBC,EAAWH,EAAKjD,QAAQoD,SACxBC,EAAQD,GACRE,WAAY,OACZF,SAAU,WAEVE,WAAY,SACZF,SAAU,SAQd,OAJIJ,GADAI,EACQ,GAAInB,GAAQsB,MAAMxE,KAAKmE,SAAS3C,IAAIiD,GAAIT,EAAKU,SAASC,GAEtD,GAAIzB,GAAQsB,MAAMR,EAAKU,SAASE,EAAGrB,IAG3CU,MAAOA,EACPK,MAAOA,IAGfO,eAAgB,SAAUZ,EAAOD,GAC7B,MAAOhE,MAAK8D,eAAgBE,MAGhCP,EAAmB,IACnBC,EAAsB,IACtBC,GACAP,EAAU0B,IACV1B,EAAU2B,QAmBVnB,EAAYP,EAAMxD,QAClBmF,iBAAkB,SAAUC,GACxB/B,EAAQgC,SAASD,EAAS,gBAE9BE,aAAc,SAAUF,GACpB5B,EAAM+B,GAAGD,aAAaE,KAAKrF,KAAMiF,GACjCjF,KAAKsF,cAAgBC,KAAKC,MAAMlC,EAAY2B,GAASpG,QAEzD4G,QAAS,WAAA,GACDR,GAAUjF,KAAKiF,QACfnC,EAAQJ,EAAKuC,EAAQS,WACzB1F,MAAKsF,cAAgBC,KAAKC,MAAMlC,EAAY2B,GAASpG,OACrDmE,EAAKiC,EAAQS,WAAY5C,GACzBO,EAAM+B,GAAGK,QAAQJ,KAAKrF,OAE1B2F,cAAe,WAAA,GAOP1E,GANA2E,EAAe5F,KAAKiB,QACpB4E,EAAQ7F,KAAK8F,eACbC,EAAerD,EAAKmD,EAAMH,YAC1BM,EAAQlF,SAASC,cAAc,OAoBnC,OAnBAiF,GAAMC,UAAY,SAClBJ,EAAMjE,YAAYoE,GACd/E,EAAUmB,GACVvD,MAAOmB,KAAKkG,WACZpH,OAAQwE,EAAYuC,GAAO/G,OAC3BqH,YAAaP,EAAaO,aAC3BP,EAAaQ,WACZC,QAAQ,EACR/B,OAAO,IAEXhB,EAAYuC,GACRhH,MAAOoC,EAAQpC,MACfC,OAAQmC,EAAQnC,SAEpB+G,EAAM3D,YAAY8D,GAClBhD,EAAK6C,EAAMH,WAAYK,GACnB/F,KAAKsG,SACLtG,KAAKsG,QAAQC,SAEVtF,GAEX6E,aAAc,WACV,IAAK9F,KAAK6F,MAAO,CACb,GAAIA,GAAQ7F,KAAK6F,MAAQ/E,SAASC,cAAc,OAChDf,MAAKiF,QAAQrD,YAAYiE,GAE7B,MAAO7F,MAAK6F,OAEhBW,gBAAiB,SAAUC,GACvB,GAAItC,GAAWd,EAAM+B,GAAGoB,gBAAgBnB,KAAKrF,KAAMyG,EAEnD,OADAzG,MAAKkG,WAAalG,KAAKsF,eAAiBtF,KAAK0G,gBAAgBvC,GACtDA,GAEXuC,gBAAiB,SAAUvC,GAAV,GAMJ3F,GACDmI,EACAC,EAaAxC,EAEIyC,EAOR9G,EA7BAkB,EAAUjB,KAAKiB,QACf6F,EAAS5D,EAAQ6D,WAAW9F,EAAQmF,UAAUU,QAC9CE,EAAS7C,EAAS6C,OAClBnB,EAAQ7F,KAAK8F,eACbmB,EAAQ,CACZ,KAASzI,EAAI,EAAGA,EAAIwI,EAAOtI,OAAQF,IAG/B,GAFImI,EAAeK,EAAOxI,GACtBoI,GAAeD,EAAa1F,QAAQiG,YAAc,GACtD,CAGA,GAAIN,EAAYO,OAAS/D,EAAU0B,IAC/B,MAAOrB,EAEX,IAAImD,EAAYO,OAAS/D,EAAU2B,OAC/B,MAAOrB,EAEX,IAAIkD,EAAYO,OAAS/D,EAAUgE,IAC/B,MAAO9D,GAAYuC,GAAO/G,MAE1BsF,GAAeuC,EAAavC,aAC5BA,IACIyC,EAAczC,EAAaiD,oBAAsBV,EAAa1F,QAAQqG,WAAapE,EAAQqE,QAAQX,EAAYO,MAC/G/D,EAAUoE,OACVpE,EAAUqE,kBACTd,EAAae,cAAchJ,OAAS,GACzCuI,EAAQ1B,KAAKoC,IAAIV,EAAOJ,IAOhC,MAJI9G,GAAOkH,EAAQhG,EAAQ2G,WACvB7H,EAAO,IACPA,GAAQ+G,EAAOe,KAAOf,EAAOgB,OAE1B/H,GAEXgI,qBAAsB,SAAU9G,GAC5B,MAAO,IAAIuC,GAAgBxD,KAAKgI,UAAW/G,MAGnD2C,EAAUqE,iBAAmB,SAAUC,GACnC,GAAIjH,GAAUgC,EAAWiF,EAazB,OAXIjH,GADAiC,EAAQiF,QAAQlH,IACJmH,gBAAkBC,KAAMpH,IAE1BmB,KAAenB,GAExBA,EAAQiG,SACTjG,EAAQiG,SAAYmB,KAAMpF,EAAWhC,EAAQoH,SAEjDjG,EAAWnB,GAAWmH,gBAAkBjB,KAAMlG,EAAQkG,SAClDjE,EAAQqE,QAAQtG,EAAQiG,OAAO,GAAGC,KAAMxD,IAAiBT,EAAQqE,QAAQtG,EAAQmH,eAAejB,KAAMxD,MACtG1C,EAAUmB,MAAiBgC,cAAgBkE,WAAaC,SAAS,KAAatH,IAE3EA,GAEXiC,EAAQsF,kBAAkB5E,GACtBwC,WAAaU,OAAQ,GACrB2B,cACIF,SAAS,EACTG,gBAAkBH,SAAS,GAC3BI,WAAaC,aAAa,IAE9BR,gBACIjB,KAAM,OACN0B,MAAQC,MAAQjK,MAAO,KACvBkK,KAAOC,OAAO,GACdC,QAAS,EACTpK,MAAO,GACPqK,SAAWC,SAAU,MACrBC,WAAab,SAAS,GACtBc,QAAUxK,MAAO,GACjByK,SACIvJ,KAAM,EACNwI,SAAS,IAGjBgB,SACIhB,SAAS,EACTiB,QAAQ,GAEZpF,cACIkE,WACIC,SAAS,EACTgB,SAAWhB,SAAS,KAG5BkB,QAAUlB,SAAS,GACnBpC,aAAa,EACbyB,WAAY,EACZ8B,QAAUC,MAAM,MAEpBjK,MAAM0C,WAAW1C,MAAMwD,SAAWU,UAAWA,QAEjC,kBAAVrG,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,+BAAgC,qCAAsCD,IAC/E,YACG,SAAUE,GAAV,GA+DOoM,GA9DA1G,EAAUxD,MAAMwD,QAChBG,EAAQH,EAAQ2G,GAAGxG,MACnByG,EAAiB5G,EAAQU,UACzBmG,EAAwB7G,EAAQ6G,sBAChClK,EAASrC,EAAEqC,OACX+D,EAAYP,EAAMxD,QAClBC,KAAM,SAAUmF,EAASiD,GACrB,GAAIjH,GAAUiH,CACVjH,aAAmBvB,OAAM2I,KAAK2B,kBAC9B/I,GAAYmH,gBAAkBC,KAAMpH,KAExCoC,EAAM+B,GAAGtF,KAAKuF,KAAKrF,KAAMiF,EAAS6E,EAAe7B,iBAAiBhH,KAEtEgJ,aAAc,SAAUhJ,EAASiJ,GAC7BlK,KAAKmK,UAAY,GAAIL,GAAe9J,KAAKiF,QAAQ,GAAIhE,EAASiJ,GAC1DE,SAAU,GAAIL,GAAsB/J,MACpCqK,OAAQrK,KACRsK,IAAKtK,KAAKuK,YAGlBC,eAAgB,WACZ,MAAO,IAAIZ,GAAiB5J,KAAKiF,QAASpF,KAAWG,KAAKiB,QAAQsI,SAAWe,IAAKtK,KAAKuK,aAE3FtJ,SACIwJ,KAAM,YACNrE,WAAaU,OAAQ,GACrB2B,cACIF,SAAS,EACTG,gBAAkBH,SAAS,GAC3BI,WAAaC,aAAa,IAE9BR,gBACIjB,KAAM,OACN0B,MAAQC,MAAQjK,MAAO,KACvBkK,KAAOC,OAAO,GACdC,QAAS,EACTpK,MAAO,GACPqK,SAAWC,SAAU,MACrBC,WAAab,SAAS,GACtBc,QAAUxK,MAAO,GACjByK,SACIvJ,KAAM,EACNwI,SAAS,IAGjBgB,SACIhB,SAAS,EACTiB,QAAQ,GAEZpF,cACIkE,WACIC,SAAS,EACTgB,SAAWhB,SAAS,KAG5BkB,QAAUlB,SAAS,GACnBpC,aAAa,EACbyB,WAAY,EACZ8B,QAAUC,MAAM,MAGxBzG,GAAQ2G,GAAGa,OAAO9G,GACdgG,EAAmB1G,EAAQyH,QAAQ9K,QACnCoB,SAAW2J,WAAaC,SAAU,IAClCC,aAAc,WACN9K,KAAKiF,SACLjF,KAAKiF,QAAQvC,OAAOqI,YAIhC7H,EAAQ0G,iBAAmBA,GAC7BnK,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,2BACH,oCACA,+BACDD,IACL,aAQkB,kBAAVC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dataviz.sparkline.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/sparkline/kendo-sparkline', ['kendo.dataviz.chart'], f);\n}(function () {\n    (function () {\n        window.kendo.dataviz = window.kendo.dataviz || {};\n        var dataviz = kendo.dataviz;\n        var constants = dataviz.constants;\n        var Chart = dataviz.Chart;\n        var elementSize = dataviz.elementSize;\n        var deepExtend = dataviz.deepExtend;\n        var TOP_OFFSET = -2;\n        var SharedTooltip$1 = dataviz.SharedTooltip.extend({\n            _slotAnchor: function (coords, slot) {\n                var axis = this.plotArea.categoryAxis;\n                var vertical = axis.options.vertical;\n                var align = vertical ? {\n                    horizontal: 'left',\n                    vertical: 'center'\n                } : {\n                    horizontal: 'center',\n                    vertical: 'bottom'\n                };\n                var point;\n                if (vertical) {\n                    point = new dataviz.Point(this.plotArea.box.x2, slot.center().y);\n                } else {\n                    point = new dataviz.Point(slot.center().x, TOP_OFFSET);\n                }\n                return {\n                    point: point,\n                    align: align\n                };\n            },\n            _defaultAnchor: function (point, slot) {\n                return this._slotAnchor({}, slot);\n            }\n        });\n        var DEAULT_BAR_WIDTH = 150;\n        var DEAULT_BULLET_WIDTH = 150;\n        var NO_CROSSHAIR = [\n            constants.BAR,\n            constants.BULLET\n        ];\n        function hide(children) {\n            var state = [];\n            for (var idx = 0; idx < children.length; idx++) {\n                var child = children[idx];\n                state[idx] = child.style.display;\n                child.style.display = 'none';\n            }\n            return state;\n        }\n        function show(children, state) {\n            for (var idx = 0; idx < children.length; idx++) {\n                children[idx].style.display = state[idx];\n            }\n        }\n        function wrapNumber(value) {\n            return dataviz.isNumber(value) ? [value] : value;\n        }\n        var Sparkline = Chart.extend({\n            _setElementClass: function (element) {\n                dataviz.addClass(element, 'k-sparkline');\n            },\n            _initElement: function (element) {\n                Chart.fn._initElement.call(this, element);\n                this._initialWidth = Math.floor(elementSize(element).width);\n            },\n            _resize: function () {\n                var element = this.element;\n                var state = hide(element.childNodes);\n                this._initialWidth = Math.floor(elementSize(element).width);\n                show(element.childNodes, state);\n                Chart.fn._resize.call(this);\n            },\n            _modelOptions: function () {\n                var chartOptions = this.options;\n                var stage = this._surfaceWrap();\n                var displayState = hide(stage.childNodes);\n                var space = document.createElement('span');\n                space.innerHTML = '&nbsp;';\n                stage.appendChild(space);\n                var options = deepExtend({\n                    width: this._autoWidth,\n                    height: elementSize(stage).height,\n                    transitions: chartOptions.transitions\n                }, chartOptions.chartArea, {\n                    inline: true,\n                    align: false\n                });\n                elementSize(stage, {\n                    width: options.width,\n                    height: options.height\n                });\n                stage.removeChild(space);\n                show(stage.childNodes, displayState);\n                if (this.surface) {\n                    this.surface.resize();\n                }\n                return options;\n            },\n            _surfaceWrap: function () {\n                if (!this.stage) {\n                    var stage = this.stage = document.createElement('span');\n                    this.element.appendChild(stage);\n                }\n                return this.stage;\n            },\n            _createPlotArea: function (skipSeries) {\n                var plotArea = Chart.fn._createPlotArea.call(this, skipSeries);\n                this._autoWidth = this._initialWidth || this._calculateWidth(plotArea);\n                return plotArea;\n            },\n            _calculateWidth: function (plotArea) {\n                var options = this.options;\n                var margin = dataviz.getSpacing(options.chartArea.margin);\n                var charts = plotArea.charts;\n                var stage = this._surfaceWrap();\n                var total = 0;\n                for (var i = 0; i < charts.length; i++) {\n                    var currentChart = charts[i];\n                    var firstSeries = (currentChart.options.series || [])[0];\n                    if (!firstSeries) {\n                        continue;\n                    }\n                    if (firstSeries.type === constants.BAR) {\n                        return DEAULT_BAR_WIDTH;\n                    }\n                    if (firstSeries.type === constants.BULLET) {\n                        return DEAULT_BULLET_WIDTH;\n                    }\n                    if (firstSeries.type === constants.PIE) {\n                        return elementSize(stage).height;\n                    }\n                    var categoryAxis = currentChart.categoryAxis;\n                    if (categoryAxis) {\n                        var pointsCount = categoryAxis.categoriesCount() * (!currentChart.options.isStacked && dataviz.inArray(firstSeries.type, [\n                            constants.COLUMN,\n                            constants.VERTICAL_BULLET\n                        ]) ? currentChart.seriesOptions.length : 1);\n                        total = Math.max(total, pointsCount);\n                    }\n                }\n                var size = total * options.pointWidth;\n                if (size > 0) {\n                    size += margin.left + margin.right;\n                }\n                return size;\n            },\n            _createSharedTooltip: function (options) {\n                return new SharedTooltip$1(this._plotArea, options);\n            }\n        });\n        Sparkline.normalizeOptions = function (userOptions) {\n            var options = wrapNumber(userOptions);\n            if (dataviz.isArray(options)) {\n                options = { seriesDefaults: { data: options } };\n            } else {\n                options = deepExtend({}, options);\n            }\n            if (!options.series) {\n                options.series = [{ data: wrapNumber(options.data) }];\n            }\n            deepExtend(options, { seriesDefaults: { type: options.type } });\n            if (dataviz.inArray(options.series[0].type, NO_CROSSHAIR) || dataviz.inArray(options.seriesDefaults.type, NO_CROSSHAIR)) {\n                options = deepExtend({}, { categoryAxis: { crosshair: { visible: false } } }, options);\n            }\n            return options;\n        };\n        dataviz.setDefaultOptions(Sparkline, {\n            chartArea: { margin: 2 },\n            axisDefaults: {\n                visible: false,\n                majorGridLines: { visible: false },\n                valueAxis: { narrowRange: true }\n            },\n            seriesDefaults: {\n                type: 'line',\n                area: { line: { width: 0.5 } },\n                bar: { stack: true },\n                padding: 2,\n                width: 0.5,\n                overlay: { gradient: null },\n                highlight: { visible: false },\n                border: { width: 0 },\n                markers: {\n                    size: 2,\n                    visible: false\n                }\n            },\n            tooltip: {\n                visible: true,\n                shared: true\n            },\n            categoryAxis: {\n                crosshair: {\n                    visible: true,\n                    tooltip: { visible: false }\n                }\n            },\n            legend: { visible: false },\n            transitions: false,\n            pointWidth: 5,\n            panes: [{ clip: false }]\n        });\n        kendo.deepExtend(kendo.dataviz, { Sparkline: Sparkline });\n    }());\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/sparkline/sparkline', ['dataviz/sparkline/kendo-sparkline'], f);\n}(function () {\n    (function ($) {\n        var dataviz = kendo.dataviz;\n        var Chart = dataviz.ui.Chart;\n        var KendoSparkline = dataviz.Sparkline;\n        var ChartInstanceObserver = dataviz.ChartInstanceObserver;\n        var extend = $.extend;\n        var Sparkline = Chart.extend({\n            init: function (element, userOptions) {\n                var options = userOptions;\n                if (options instanceof kendo.data.ObservableArray) {\n                    options = { seriesDefaults: { data: options } };\n                }\n                Chart.fn.init.call(this, element, KendoSparkline.normalizeOptions(options));\n            },\n            _createChart: function (options, themeOptions) {\n                this._instance = new KendoSparkline(this.element[0], options, themeOptions, {\n                    observer: new ChartInstanceObserver(this),\n                    sender: this,\n                    rtl: this._isRtl()\n                });\n            },\n            _createTooltip: function () {\n                return new SparklineTooltip(this.element, extend({}, this.options.tooltip, { rtl: this._isRtl() }));\n            },\n            options: {\n                name: 'Sparkline',\n                chartArea: { margin: 2 },\n                axisDefaults: {\n                    visible: false,\n                    majorGridLines: { visible: false },\n                    valueAxis: { narrowRange: true }\n                },\n                seriesDefaults: {\n                    type: 'line',\n                    area: { line: { width: 0.5 } },\n                    bar: { stack: true },\n                    padding: 2,\n                    width: 0.5,\n                    overlay: { gradient: null },\n                    highlight: { visible: false },\n                    border: { width: 0 },\n                    markers: {\n                        size: 2,\n                        visible: false\n                    }\n                },\n                tooltip: {\n                    visible: true,\n                    shared: true\n                },\n                categoryAxis: {\n                    crosshair: {\n                        visible: true,\n                        tooltip: { visible: false }\n                    }\n                },\n                legend: { visible: false },\n                transitions: false,\n                pointWidth: 5,\n                panes: [{ clip: false }]\n            }\n        });\n        dataviz.ui.plugin(Sparkline);\n        var SparklineTooltip = dataviz.Tooltip.extend({\n            options: { animation: { duration: 0 } },\n            _hideElement: function () {\n                if (this.element) {\n                    this.element.hide().remove();\n                }\n            }\n        });\n        dataviz.SparklineTooltip = SparklineTooltip;\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.dataviz.sparkline', [\n        'dataviz/sparkline/kendo-sparkline',\n        'dataviz/sparkline/sparkline'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dataviz.sparkline',\n        name: 'Sparkline',\n        category: 'dataviz',\n        description: 'Sparkline widgets.',\n        depends: ['dataviz.chart']\n    };\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}