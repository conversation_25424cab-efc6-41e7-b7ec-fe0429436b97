{"version": 3, "sources": ["kendo.gantt.timeline.js"], "names": ["f", "define", "$", "trimOptions", "options", "name", "prefix", "views", "getWorkDays", "workDays", "dayIndex", "workWeekStart", "push", "workWeekEnd", "blurActiveElement", "activeElement", "kendo", "_activeElement", "nodeName", "toLowerCase", "blur", "timelineStyles", "GanttTimeline", "Widget", "ui", "kendoDomElement", "dom", "element", "kendoTextElement", "text", "kendoHtmlElement", "html", "isPlainObject", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "extend", "proxy", "browser", "support", "isRtl", "keys", "Query", "data", "STRING", "NS", "CLICK", "DBLCLICK", "MOUSEMOVE", "MOUSEENTER", "MOUSELEAVE", "KEYDOWN", "DOT", "TIME_HEADER_TEMPLATE", "template", "DAY_HEADER_TEMPLATE", "WEEK_HEADER_TEMPLATE", "MONTH_HEADER_TEMPLATE", "YEAR_HEADER_TEMPLATE", "RESIZE_HINT", "RESIZE_TOOLTIP_TEMPLATE", "PERCENT_RESIZE_TOOLTIP_TEMPLATE", "TASK_TOOLTIP_TEMPLATE", "SIZE_CALCULATION_TEMPLATE", "defaultViews", "day", "type", "week", "month", "year", "viewStyles", "alt", "reset", "nonWorking", "header", "gridHeader", "gridHeaderWrap", "gridContent", "tasksWrapper", "rowsTable", "columnsTable", "tasksTable", "dependenciesWrapper", "resource", "resourceAlt", "task", "taskSingle", "taskMilestone", "taskSummary", "taskWrap", "taskMilestoneWrap", "resourcesWrap", "taskDot", "taskDotStart", "taskDotEnd", "taskDragHandle", "taskContent", "taskTemplate", "taskActions", "taskDelete", "taskComplete", "taskDetails", "taskDetailsPercent", "link", "icon", "iconDelete", "taskResizeHandle", "taskResizeHandleWest", "taskResizeHandleEast", "taskSummaryProgress", "taskSummaryComplete", "line", "lineHorizontal", "lineVertical", "arrowWest", "arrowEast", "dragHint", "dependencyHint", "tooltipWrapper", "tooltipContent", "tooltipCallout", "callout", "marquee", "marqueeColor", "GanttView", "init", "fn", "call", "this", "title", "find", "styles", "content", "contentWidth", "width", "_workDays", "_headerTree", "headerTree", "_taskTree", "taskTree", "_taskTemplate", "Template", "templateSettings", "_dependencyTree", "dependencyTree", "_taskCoordinates", "_currentTime", "destroy", "clearTimeout", "_tooltipTimeout", "headerRow", "_dragHint", "_resizeHint", "_resizeTooltip", "_taskTooltip", "_percentCompleteResizeTooltip", "showWorkHours", "showWorkDays", "workDayStart", "Date", "workDayEnd", "hourSpan", "slotSize", "currentTimeMarker", "updateInterval", "renderLayout", "_slots", "_createSlots", "_tableWidth", "_calculateTableWidth", "createLayout", "_layout", "_slotDimensions", "_adjustHeight", "height", "rows", "headers", "_headers", "colgroup", "_colgroup", "tree", "table", "style", "render", "last", "slot", "i", "length", "children", "slots", "_timeSlots", "offsetLeft", "offsetWidth", "tasks", "contentTable", "totalHeight", "taskCount", "_rowsTable", "_columnsTable", "_tasksTable", "calculatedSize", "row", "_contentHeight", "_rowHeight", "undefined", "_renderCurrentTime", "rowCount", "attributes", "className", "_createTable", "slotSpan", "cells", "slotsCount", "totalSpan", "span", "colspan", "isNonWorking", "cell", "position", "resourcesPosition", "resourceStyle", "l", "coordinates", "size", "_calculateMilestoneWidth", "<PERSON><PERSON><PERSON><PERSON>", "Math", "round", "resourcesField", "resourcesMargin", "_calculateResourcesMargin", "taskBorderWidth", "_calculateTaskBorderWidth", "addCoordinates", "rowIndex", "taskLeft", "left", "taskRight", "isMilestone", "id", "start", "end", "_taskPosition", "borderWidth", "_renderTask", "max", "clientWidth", "_renderResources", "tbody", "cols", "currentSpan", "tableWidth", "maxSpan", "boundingClientRect", "milestone", "append", "getBoundingClientRect", "right", "remove", "margin", "wrapper", "parseInt", "css", "computedStyle", "getComputedStyles", "parseFloat", "taskWrapper", "taskElement", "progressHandleOffset", "editable", "wrapClassName", "dragHandleStyle", "taskWrapAttr", "summary", "_renderSummary", "_renderMilestone", "_renderSingleTask", "dependencyCreate", "dragPercentComplete", "update", "percentComplete", "progressWidth", "task<PERSON><PERSON><PERSON><PERSON>", "href", "aria-label", "resize", "data-uid", "uid", "resources", "color", "get", "reverse", "startLeft", "_offset", "endLeft", "date", "startOffset", "slotDuration", "startIndex", "slotOffset", "_slotIndex", "field", "value", "middle", "startIdx", "endIdx", "slice", "ceil", "_timeByPosition", "x", "snap", "snapToEnd", "duration", "_slotByPosition", "offset", "getTime", "slotIndex", "_renderDependencies", "dependencies", "elements", "apply", "_renderDependency", "dependency", "method", "predecessor", "predecessorId", "successor", "successorId", "attr", "_renderFF", "from", "to", "lines", "_dependencyFF", "_arrow", "_renderSS", "_renderFS", "_dependencyFS", "_renderSF", "delta", "that", "top", "dir", "overlap", "arrowOverlap", "rowHeight", "minLineWidth", "fromTop", "floor", "toTop", "addHorizontal", "_line", "addVertical", "abs", "minLineHeight", "minDistance", "direction", "j", "count", "_createDragHint", "clone", "addClass", "parent", "_updateDragHint", "_removeDragHint", "_createResizeHint", "tooltipHeight", "tooltipTop", "taskTop", "messages", "format", "resizeTooltipFormat", "_resizeTooltipWidth", "_resizeTooltipTop", "_updateResizeHint", "resizeStart", "tooltipLeft", "tablesWidth", "scrollbar", "tooltipWidth", "tableOffset", "min-width", "appendTo", "_removeResizeHint", "_updatePercentCompleteTooltip", "tooltip", "tooltipMiddle", "arrow", "arrowHeight", "_removePercentCompleteTooltip", "_updateDependencyDragHint", "useVML", "_removeDependencyDragHint", "_creteVmlDependencyDragHint", "_creteDependencyDragHint", "deltaX", "deltaY", "y", "sqrt", "angle", "atan", "PI", "transform-origin", "-ms-transform-origin", "-webkit-transform-origin", "transform", "-ms-transform", "-webkit-transform", "hint", "outerHTML", "_createTaskTooltip", "mouseLeft", "contentOffset", "contentScrollLeft", "scrollLeft", "parents", "first", "rowOffset", "scrollTop", "_removeTaskTooltip", "_scrollTo", "elementLeft", "elementWidth", "elementRight", "closest", "rowTop", "rowBottom", "contentTop", "contentHeight", "contentBottom", "contentLeft", "contentRight", "scrollbarWidth", "_scrollToDate", "viewStart", "viewEnd", "columnLevels", "level", "column", "headerText", "levelIndex", "levelCount", "columnIndex", "columnCount", "_hours", "slotEnd", "isWorkHour", "hours", "getHours", "setHours", "_days", "isWorkDay", "nextDay", "_isWorkDay", "_weeks", "daySlots", "firstDay", "calendarInfo", "dayOfWeek", "addDays", "_months", "endMonth", "firstDayOfMonth", "setMonth", "getMonth", "_years", "monthSpan", "_slotHeaders", "columns", "getDay", "getCulture", "calendars", "standard", "currentTime", "_getCurrentTime", "timeOffset", "tablesWrap", "markerOptions", "_currentTimeUpdateTimer", "setInterval", "GanttDayView", "timeHeaderTemplate", "dayHeaderTemplate", "range", "optionsRange", "getDate", "getMilliseconds", "daySlot", "hourSlots", "GanttWeekView", "weekHeaderTemplate", "endDay", "rangeEnd", "setDate", "GanttMonthView", "monthHeaderTemplate", "lastDayOfMonth", "GanttYearView", "yearHeaderTemplate", "monthSlots", "each", "index", "gridContentWrap", "taskWrapActive", "hovered", "selected", "origin", "_wrapper", "_domTrees", "_views", "_selectable", "_draggable", "_resizable", "_percentResizeDraggable", "_createDependencyDraggable", "_attachEvents", "_tooltip", "selectable", "clearInterval", "_unbind<PERSON>iew", "_selected<PERSON><PERSON><PERSON>", "_moveDraggable", "_resizeDraggable", "_percentDraggable", "_dependencyDraggable", "touch", "off", "calculateSize", "calculatedRowHeight", "calculatedCellHeight", "_calculatedSize", "Tree", "view", "isSettings", "defaultView", "_selected<PERSON>iew<PERSON>ame", "_select<PERSON><PERSON>w", "trigger", "action", "_initializeView", "_viewByIndex", "newRange", "newDate", "getter", "window", "Error", "_range", "startOrder", "endOrder", "sort", "toArray", "_render", "_tasks", "_taskByUid", "currentStart", "cleanUp", "dragInProgress", "move", "Draggable", "distance", "filter", "holdToDrag", "mobileOS", "ignore", "bind", "e", "currentTarget", "preventDefault", "location", "throttle", "updateHintDate", "userEvents", "currentEnd", "hasClass", "taskElementOffset", "timelineOffset", "originalPercent<PERSON>idth", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentPercentComplete", "updateElement", "siblings", "initialDelta", "currentWidth", "min", "originalHandle", "startX", "startY", "<PERSON><PERSON><PERSON><PERSON>", "hoveredTask", "msie", "version", "removeClass", "to<PERSON><PERSON><PERSON><PERSON>", "toggleClass", "document", "namespaces", "add", "elementOffset", "tablesOffset", "target", "currentX", "currentY", "elementUnderCursor", "fromStart", "toStart", "on", "stopPropagation", "ctrl<PERSON>ey", "elementFromPoint", "clientX", "clientY", "click", "selectDependency", "clearSelection", "select", "_tabindex", "selectedDependency", "keyCode", "DELETE", "dependencyDestroy", "kendoTouch", "doubletap", "currentMousePosition", "tooltipOptions", "mouseMoveHandler", "visible", "relatedTarget", "currentPosition", "client", "setTimeout", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,wBACH,YACA,cACA,qBACDD,IACL,WAwnEE,MA3mEC,UAAUE,GAuCP,QAASC,GAAYC,GAIjB,aAHOA,GAAQC,WACRD,GAAQE,aACRF,GAAQG,MACRH,EAEX,QAASI,GAAYJ,GAArB,GACQK,MACAC,EAAWN,EAAQO,aAEvB,KADAF,EAASG,KAAKF,GACPN,EAAQS,aAAeH,GACtBA,EAAW,EACXA,GAAY,EAEZA,IAEJD,EAASG,KAAKF,EAElB,OAAOD,GAEX,QAASK,KACL,GAAIC,GAAgBC,MAAMC,gBACtBF,IAA0D,SAAzCA,EAAcG,SAASC,eACxCjB,EAAEa,GAAeK,OA9D5B,GAy4COC,GAwBAC,EAh6CAC,EAASP,MAAMQ,GAAGD,OAClBE,EAAkBT,MAAMU,IAAIC,QAC5BC,EAAmBZ,MAAMU,IAAIG,KAC7BC,EAAmBd,MAAMU,IAAIK,KAC7BC,EAAgB9B,EAAE8B,cAClBC,EAAajB,MAAMkB,YACnBC,EAAcnB,MAAMoB,aACpBC,EAASnC,EAAEmC,OACXC,EAAQpC,EAAEoC,MACVC,EAAUvB,MAAMwB,QAAQD,QACxBE,GAAQ,EACRC,EAAO1B,MAAM0B,KACbC,EAAQ3B,MAAM4B,KAAKD,MACnBE,EAAS,SACTC,EAAK,sBACLC,EAAQ,QACRC,EAAW,WACXC,EAAY,YACZC,EAAa,aACbC,EAAa,aACbC,EAAU,UACVC,EAAM,IACNC,EAAuBtC,MAAMuC,SAAS,iCACtCC,EAAsBxC,MAAMuC,SAAS,wCACrCE,EAAuBzC,MAAMuC,SAAS,qGACtCG,EAAwB1C,MAAMuC,SAAS,mCACvCI,EAAuB3C,MAAMuC,SAAS,oCACtCK,EAAc5C,MAAMuC,SAAS,mFAC7BM,EAA0B7C,MAAMuC,SAAS,6OACzCO,EAAkC9C,MAAMuC,SAAS,6LACjDQ,EAAwB/C,MAAMuC,SAAS,uYACvCS,EAA4B,wGAC5BC,GACAC,KAAOC,KAAM,yBACbC,MAAQD,KAAM,0BACdE,OAASF,KAAM,2BACfG,MAAQH,KAAM,2BA4BdI,GACAC,IAAK,QACLC,MAAO,UACPC,WAAY,iBACZC,OAAQ,WACRC,WAAY,gBACZC,eAAgB,qBAChBC,YAAa,iBACbC,aAAc,iBACdC,UAAW,eACXC,aAAc,kBACdC,WAAY,gBACZC,oBAAqB,uBACrBC,SAAU,aACVC,YAAa,mBACbC,KAAM,SACNC,WAAY,gBACZC,cAAe,mBACfC,YAAa,iBACbC,SAAU,cACVC,kBAAmB,mBACnBC,cAAe,mBACfC,QAAS,aACTC,aAAc,eACdC,WAAY,aACZC,eAAgB,oBAChBC,YAAa,iBACbC,aAAc,kBACdC,YAAa,iBACbC,WAAY,gBACZC,aAAc,kBACdC,YAAa,iBACbC,mBAAoB,aACpBC,KAAM,SACNC,KAAM,SACNC,WAAY,YACZC,iBAAkB,kBAClBC,qBAAsB,aACtBC,qBAAsB,aACtBC,oBAAqB,0BACrBC,oBAAqB,0BACrBC,KAAM,SACNC,eAAgB,WAChBC,aAAc,WACdC,UAAW,YACXC,UAAW,YACXC,SAAU,cACVC,eAAgB,oBAChBC,eAAgB,6CAChBC,eAAgB,oBAChBC,eAAgB,wBAChBC,QAAS,YACTC,QAAS,4BACTC,aAAc,mBAEdC,EAAY7G,MAAMQ,GAAGqG,UAAYtG,EAAOc,QACxCyF,KAAM,SAAUnG,EAASvB,GACrBmB,EAAOwG,GAAGD,KAAKE,KAAKC,KAAMtG,EAASvB,GACnC6H,KAAKC,MAAQD,KAAK7H,QAAQ8H,OAASD,KAAK7H,QAAQC,KAChD4H,KAAKtD,OAASsD,KAAKtG,QAAQwG,KAAK9E,EAAMwE,EAAUO,OAAOxD,YACvDqD,KAAKI,QAAUJ,KAAKtG,QAAQwG,KAAK9E,EAAMwE,EAAUO,OAAOtD,aACxDmD,KAAKK,aAAeL,KAAKI,QAAQE,QACjCN,KAAKO,UAAYhI,EAAYyH,KAAK7H,SAClC6H,KAAKQ,YAAcrI,EAAQsI,WAC3BT,KAAKU,UAAYvI,EAAQwI,SACzBX,KAAKY,cAAgBzI,EAAQ8F,aAAelF,MAAMuC,SAASnD,EAAQ8F,aAAc7D,KAAWrB,MAAM8H,SAAU1I,EAAQ2I,mBAAqB,KACzId,KAAKe,gBAAkB5I,EAAQ6I,eAC/BhB,KAAKiB,oBACLjB,KAAKkB,gBAETC,QAAS,WACL7H,EAAOwG,GAAGqB,QAAQpB,KAAKC,MACvBoB,aAAapB,KAAKqB,iBAClBrB,KAAKsB,UAAY,KACjBtB,KAAKtD,OAAS,KACdsD,KAAKI,QAAU,KACfJ,KAAKuB,UAAY,KACjBvB,KAAKwB,YAAc,KACnBxB,KAAKyB,eAAiB,KACtBzB,KAAK0B,aAAe,KACpB1B,KAAK2B,8BAAgC,KACrC3B,KAAKQ,YAAc,KACnBR,KAAKU,UAAY,KACjBV,KAAKe,gBAAkB,MAE3B5I,SACIyJ,eAAe,EACfC,cAAc,EACdC,aAAc,GAAIC,MAAK,KAAM,EAAG,EAAG,EAAG,EAAG,GACzCC,WAAY,GAAID,MAAK,KAAM,EAAG,EAAG,GAAI,EAAG,GACxCrJ,cAAe,EACfE,YAAa,EACbqJ,SAAU,EACVC,SAAU,IACVC,mBAAqBC,eAAgB,MAEzCC,aAAc,WACVrC,KAAKsC,OAAStC,KAAKuC,eACnBvC,KAAKwC,YAAcxC,KAAKyC,uBACxBzC,KAAK0C,aAAa1C,KAAK2C,WACvB3C,KAAK4C,kBACL5C,KAAK6C,gBACL7C,KAAKI,QAAQF,KAAK9E,EAAMwE,EAAUO,OAAOjD,qBAAqBoD,MAAMN,KAAKwC,cAE7EK,cAAe,WACP7C,KAAKI,SACLJ,KAAKI,QAAQ0C,OAAO9C,KAAKtG,QAAQoJ,SAAW5I,EAAY8F,KAAKtD,UAGrEgG,aAAc,SAAUK,GAAV,GACNC,GAAUhD,KAAKiD,SAASF,GACxBG,EAAWlD,KAAKmD,YAChBC,EAAOpD,KAAKQ,YACZ9D,EAASlD,EAAgB,QAAS,KAAMwJ,GACxCK,EAAQ7J,EAAgB,SAAW8J,OAAShD,MAAON,KAAKwC,YAAc,QACtEU,EACAxG,GAEJ0G,GAAKG,QAAQF,IACbrD,KAAKsB,UAAYtB,KAAKtD,OAAOwD,KAAK,kBAAkBsD,QAExDZ,gBAAiB,WAAA,GAGTa,GACA/G,EACKgH,EAAOC,EAJZX,EAAUhD,KAAKsB,UAAU,GAAGsC,SAC5BC,EAAQ7D,KAAK8D,YAGjB,KAASJ,EAAI,EAAGC,EAASX,EAAQW,OAAQD,EAAIC,EAAQD,IACjDhH,EAASsG,EAAQU,GACjBD,EAAOI,EAAMH,GACbD,EAAKM,WAAarH,EAAOqH,WACzBN,EAAKO,YAActH,EAAOsH,aAGlCT,OAAQ,SAAUU,GAAV,GAGAC,GAMAC,EARAC,EAAYH,EAAMN,OAClBxD,EAASP,EAAUO,OAEnBpD,EAAYiD,KAAKqE,WAAWD,GAC5BpH,EAAegD,KAAKsE,cAAcF,GAClCnH,EAAa+C,KAAKuE,YAAYN,GAC9B9B,EAAoBnC,KAAK7H,QAAQgK,kBACjCqC,EAAiBxE,KAAK7H,QAAQqM,cAElCxE,MAAKU,UAAU6C,QACXxG,EACAC,EACAC,IAEJiH,EAAelE,KAAKI,QAAQF,KAAK9E,EAAM+E,EAAOpD,WAC1CyH,IACAL,EAAcK,EAAeC,IAAMR,EAAMN,OACzC3D,KAAKI,QAAQF,KAAK9E,EAAM+E,EAAOlD,YAAY6F,OAAOqB,GAClDD,EAAapB,OAAOqB,IAExBnE,KAAK0E,eAAiBR,EAAapB,SACnC9C,KAAK2E,WAAaH,EAAiBA,EAAeC,IAAMzE,KAAK0E,eAAiBR,EAAahE,KAAK,MAAMyD,OACtG3D,KAAKI,QAAQF,KAAK9E,EAAM+E,EAAOnD,cAAc8F,OAAO9C,KAAK0E,gBACrDvC,KAAsB,GAA8CyC,SAArCzC,EAAkBC,gBACjDpC,KAAK6E,sBAGbR,WAAY,SAAUS,GAAV,GAEJL,GAMKf,EAPLX,KAEA5C,EAASP,EAAUO,OACnB4E,GACA,MACEC,UAAW7E,EAAO5D,KAExB,KAASmH,EAAI,EAAGA,EAAIoB,EAAUpB,IAC1Be,EAAMjL,EAAgB,KAAMuL,EAAWrB,EAAI,IAAKlK,EAAgB,KAAM,MAAOG,EAAiB,SAC9FoJ,EAAKpK,KAAK8L,EAEd,OAAOzE,MAAKiF,aAAa,EAAGlC,GAAQiC,UAAW7E,EAAOpD,aAE1DuH,cAAe,WAAA,GAEPG,GAIAhB,EACAyB,EAEAH,EACKrB,EATLyB,KAEAhF,EAASP,EAAUO,OACnB0D,EAAQ7D,KAAK8D,aACbsB,EAAavB,EAAMF,OAGnB0B,EAAY,CAEhB,KAAS3B,EAAI,EAAGA,EAAI0B,EAAY1B,IAC5BD,EAAOI,EAAMH,GACbqB,KACAG,EAAWzB,EAAK6B,KAChBD,GAAaH,EACI,IAAbA,IACAH,EAAWQ,QAAUL,GAErBzB,EAAK+B,eACLT,EAAWC,UAAY7E,EAAO1D,YAElC0I,EAAMxM,KAAKa,EAAgB,KAAMuL,GAAapL,EAAiB,OAGnE,OADA8K,GAAMjL,EAAgB,KAAM,KAAM2L,GAC3BnF,KAAKiF,aAAaI,GAAYZ,IAAQO,UAAW7E,EAAOnD,gBAEnEuH,YAAa,SAAUN,GAAV,GAELQ,GACAgB,EACAC,EACArI,EAWAsI,EAGAC,EAgBKlC,EAAOmC,EAlCZ9C,KAKA5C,EAASP,EAAUO,OACnB2F,EAAc9F,KAAKiB,oBACnB8E,EAAO/F,KAAKgG,2BACZC,EAAiBC,KAAKC,MAAMJ,EAAKzF,OACjC8F,EAAiBpG,KAAK7H,QAAQiO,eAC9BpB,GACA7E,EAAOhD,SACPgD,EAAO/C,aAEPoH,EAAiBxE,KAAK7H,QAAQqM,eAE9B6B,EAAkBrG,KAAKsG,4BACvBC,EAAkBvG,KAAKwG,4BAEvBC,EAAiB,SAAUC,GAAV,GACbC,GAEOjB,EAASkB,KADhBC,EAEQF,EAAWjB,EAASpF,KAC5BjD,GAAKyJ,gBACLH,GAAYV,EAAiB,EAC7BY,EAAYF,EAAWV,GAE3BH,EAAYzI,EAAK0J,KACbC,MAAOL,EACPM,IAAKJ,EACLH,SAAUA,GAGlB,KAAShD,EAAI,EAAGmC,EAAI5B,EAAMN,OAAQD,EAAImC,EAAGnC,IACrCrG,EAAO4G,EAAMP,GACbgC,EAAW1F,KAAKkH,cAAc7J,GAC9BqI,EAASyB,YAAcZ,EACvB9B,EAAMjL,EAAgB,KAAM,MAC5BiM,EAAOjM,EAAgB,MACnB6D,EAAK2J,OAAShH,KAAKiH,KAAO5J,EAAK4J,KAAOjH,KAAKgH,QAC3CvB,EAAK7B,SAASjL,KAAKqH,KAAKoH,YAAYnD,EAAMP,GAAIgC,IAC1CrI,EAAK+I,IAAmB/I,EAAK+I,GAAgBzC,SAEzCgC,EADAnL,EACoBwF,KAAKwC,YAAckD,EAASkB,KAE5BV,KAAKmB,IAAI3B,EAASpF,OAASyF,EAAKuB,YAAa,GAAK5B,EAASkB,KAEnFhB,GAAkBtF,MAAON,KAAKwC,aAAemD,EAAoBU,GAAmB,MACpFT,EAAcpL,EAAQ,QAAU,QAAUmL,EAAoB,KAC1DnB,IACAoB,EAAc9C,OAAS0B,EAAeiB,KAAO,MAEjDA,EAAK7B,SAASjL,KAAKa,EAAgB,OAC/BwL,UAAW7E,EAAOxC,cAClB2F,MAAOsC,GACR5F,KAAKuH,iBAAiBlK,EAAK+I,GAAiBpB,EAAUtB,EAAI,OAEjE+C,EAAe/C,IAEnBe,EAAIb,SAASjL,KAAK8M,GAClB1C,EAAKpK,KAAK8L,EAEd,OAAOzE,MAAKiF,aAAa,EAAGlC,GAAQiC,UAAWpF,EAAUO,OAAOlD,cAEpEgI,aAAc,SAAUM,EAASxC,EAAM5C,GAAzB,GAEN+C,GACAsE,EACK9D,EAHL+D,IAGJ,KAAS/D,EAAI,EAAGA,EAAI6B,EAAS7B,IACzB+D,EAAK9O,KAAKa,EAAgB,OAQ9B,OANA0J,GAAW1J,EAAgB,WAAY,KAAMiO,GAC7CD,EAAQhO,EAAgB,QAAS,KAAMuJ,GAClC5C,EAAOmD,QACRnD,EAAOmD,UAEXnD,EAAOmD,MAAMhD,MAAQN,KAAKwC,YAAc,KACjChJ,EAAgB,QAAS2G,GAC5B+C,EACAsE,KAGR/E,qBAAsB,WAAA,GAIdiF,GACAC,EACKjE,EAAOC,EALZE,EAAQ7D,KAAK8D,aACb8D,EAAU,EACVvC,EAAY,CAGhB,KAAS3B,EAAI,EAAGC,EAASE,EAAMF,OAAQD,EAAIC,EAAQD,IAC/CgE,EAAc7D,EAAMH,GAAG4B,KACvBD,GAAaqC,EACTA,EAAcE,IACdA,EAAUF,EAIlB,OADAC,GAAazB,KAAKC,MAAMd,EAAYrF,KAAK7H,QAAQ+J,SAAW0F,IAGhE5B,yBAA0B,WAAA,GAClBD,GAGA8B,EAFA7C,EAAYpF,EAAUO,OAAO9C,KAAO,IAAMuC,EAAUO,OAAO5C,cAC3DuK,EAAY7P,EAAE,eAAkB+M,EAAY,oDAShD,OAPAhF,MAAKI,QAAQ2H,OAAOD,GACpBD,EAAqBC,EAAU,GAAGE,wBAClCjC,GACIzF,MAASuH,EAAmBI,MAAQJ,EAAmBjB,KACvDU,YAAeQ,EAAU,GAAGR,aAEhCQ,EAAUI,SACHnC,GAEXO,0BAA2B,WAAA,GACnB6B,GACAC,EAAUnQ,EAAE,eAAkB2H,EAAUO,OAAOxC,cAAgB,oDAInE,OAHAqC,MAAKI,QAAQ2H,OAAOK,GACpBD,EAASE,SAASD,EAAQE,IAAI9N,EAAQ,eAAiB,eAAgB,IACvE4N,EAAQF,SACDC,GAEX3B,0BAA2B,WAAA,GACnBlG,GAGAiI,EAFAvD,EAAYpF,EAAUO,OAAO9C,KAAO,IAAMuC,EAAUO,OAAO7C,WAC3DD,EAAOpF,EAAE,eAAkB+M,EAAY,oDAM3C,OAJAhF,MAAKI,QAAQ2H,OAAO1K,GACpBkL,EAAgBxP,MAAMyP,kBAAkBnL,EAAK,IAAK,sBAClDiD,EAAQmI,WAAWF,EAAc,qBAAsB,IACvDlL,EAAK6K,SACE5H,GAEX8G,YAAa,SAAU/J,EAAMqI,GAAhB,GACLgD,GACAC,EAEAC,EADAC,EAAW7I,KAAK7H,QAAQ0Q,SAExBlC,EAAWjB,EAASkB,KACpBzG,EAASP,EAAUO,OACnB2I,EAAgB3I,EAAO1C,SACvB+G,EAAiBxE,KAAK7H,QAAQqM,eAC9BuE,KACAC,GACAhE,UAAW8D,EACXxF,OAASsD,KAAMD,EAAW,MA0B9B,OAxBInC,KACAwE,EAAa1F,MAAMR,OAAS0B,EAAeiB,KAAO,MAElDpI,EAAK4L,QACLN,EAAc3I,KAAKkJ,eAAe7L,EAAMqI,GACjCrI,EAAKyJ,eACZ6B,EAAc3I,KAAKmJ,iBAAiB9L,EAAMqI,GAC1CsD,EAAahE,WAAa,IAAM7E,EAAOzC,mBAEvCiL,EAAc3I,KAAKoJ,kBAAkB/L,EAAMqI,GAE/CgD,EAAclP,EAAgB,MAAOwP,GAAeL,IAChDE,GAAYA,EAASQ,oBAAqB,IAC1CX,EAAY9E,SAASjL,KAAKa,EAAgB,OAASwL,UAAW7E,EAAOvC,QAAU,IAAMuC,EAAOtC,gBAC5F6K,EAAY9E,SAASjL,KAAKa,EAAgB,OAASwL,UAAW7E,EAAOvC,QAAU,IAAMuC,EAAOrC,eAE3FT,EAAK4L,SAAY5L,EAAKyJ,gBAAiB+B,GAAYA,EAASS,uBAAwB,GAAST,EAASU,UAAW,GAAgC,OAAvBvJ,KAAKY,gBAChIgI,EAAuB1C,KAAKC,MAAMT,EAASpF,MAAQjD,EAAKmM,iBACxDT,EAAgBvO,EAAQ,QAAU,QAAUoO,EAAuB,KACnEF,EAAY9E,SAASjL,KAAKa,EAAgB,OACtCwL,UAAW7E,EAAOpC,eAClBuF,MAAOyF,MAGRL,GAEXU,kBAAmB,SAAU/L,EAAMqI,GAAhB,GAIX1H,GAWAoC,EAeA1G,EA7BAyG,EAASP,EAAUO,OACnBsJ,EAAgBvD,KAAKC,MAAMT,EAASpF,MAAQjD,EAAKmM,iBACjDE,KAEAb,EAAW7I,KAAK7H,QAAQ0Q,QA8B5B,OA7B2B,QAAvB7I,KAAKY,cACL5C,EAAcnE,EAAiBmG,KAAKY,cAAcvD,KAElDW,EAAcrE,EAAiB0D,EAAK4C,OACpCyJ,EAAa/Q,KAAKa,EAAgB,OAC9BwL,UAAW7E,EAAO/B,aAClBkF,OAAShD,MAAOmJ,EAAgB,UAGpCrJ,EAAU5G,EAAgB,OAASwL,UAAW7E,EAAOnC,cAAgBxE,EAAgB,OAASwL,UAAW7E,EAAOlC,eAAiBD,MACrI0L,EAAa/Q,KAAKyH,GACdyI,IACIA,EAAS1H,WAAY,GACrBf,EAAQwD,SAASjL,KAAKa,EAAgB,QAAUwL,UAAW7E,EAAOjC,cAAgB1E,EAAgB,KAC1FwL,UAAW7E,EAAO5B,KAAO,IAAM4B,EAAOhC,WACtCwL,KAAM,IACNC,aAAc,WACdpQ,EAAgB,QAAUwL,UAAW7E,EAAO3B,KAAO,IAAM2B,EAAO1B,kBAExEoK,EAASgB,UAAW,GAAShB,EAASU,UAAW,IACjDnJ,EAAQwD,SAASjL,KAAKa,EAAgB,QAAUwL,UAAW7E,EAAOzB,iBAAmB,IAAMyB,EAAOxB,wBAClGyB,EAAQwD,SAASjL,KAAKa,EAAgB,QAAUwL,UAAW7E,EAAOzB,iBAAmB,IAAMyB,EAAOvB,0BAGtGlF,EAAUF,EAAgB,OAC1BwL,UAAW7E,EAAO9C,KAAO,IAAM8C,EAAO7C,WACtCwM,WAAYzM,EAAK0M,IACjBzG,OAAShD,MAAO4F,KAAKmB,IAAI3B,EAASpF,MAA+B,EAAvBoF,EAASyB,YAAiB,GAAK,OAC1EuC,IAGPP,iBAAkB,SAAU9L,GAAV,GACV8C,GAASP,EAAUO,OACnBzG,EAAUF,EAAgB,OAC1BwL,UAAW7E,EAAO9C,KAAO,IAAM8C,EAAO5C,cACtCuM,WAAYzM,EAAK0M,KAErB,OAAOrQ,IAEXwP,eAAgB,SAAU7L,EAAMqI,GAAhB,GACRvF,GAASP,EAAUO,OACnBsJ,EAAgBvD,KAAKC,MAAMT,EAASpF,MAAQjD,EAAKmM,iBACjD9P,EAAUF,EAAgB,OAC1BwL,UAAW7E,EAAO9C,KAAO,IAAM8C,EAAO3C,YACtCsM,WAAYzM,EAAK0M,IACjBzG,OAAShD,MAAOoF,EAASpF,MAAQ,QACjC9G,EAAgB,OACZwL,UAAW7E,EAAOtB,oBAClByE,OAAShD,MAAOmJ,EAAgB,QAChCjQ,EAAgB,OACZwL,UAAW7E,EAAOrB,oBAClBwE,OAAShD,MAAOoF,EAASpF,MAAQ,WAE7C,OAAO5G,IAEX6N,iBAAkB,SAAUyC,EAAWhF,GAArB,GAEV7H,GACKuG,EAAOC,EAFZC,IAEJ,KAASF,EAAI,EAAGC,EAASqG,EAAUrG,OAAQD,EAAIC,EAAQD,IACnDvG,EAAW6M,EAAUtG,GACrBE,EAASjL,KAAKa,EAAgB,QAC1BwL,UAAWA,EACX1B,OAAS2G,MAAS9M,EAAS+M,IAAI,YAC/BvQ,EAAiBwD,EAAS+M,IAAI,WAKtC,OAHI1P,IACAoJ,EAASuG,UAENvG,GAEXsD,cAAe,SAAU7J,GAAV,GACP8I,GAAQD,KAAKC,MACbiE,EAAYjE,EAAMnG,KAAKqK,QAAQ7P,EAAQ6C,EAAK4J,IAAM5J,EAAK2J,QACvDsD,EAAUnE,EAAMnG,KAAKqK,QAAQ7P,EAAQ6C,EAAK2J,MAAQ3J,EAAK4J,KAC3D,QACIL,KAAMwD,EACN9J,MAAOgK,EAAUF,IAGzBC,QAAS,SAAUE,GAAV,GAED9G,GACA+G,EACAC,EAEAC,EALA7G,EAAQ7D,KAAK8D,aAIb6G,EAAa,CAEjB,OAAK9G,GAAMF,QAGX+G,EAAa1K,KAAK4K,WAAW,QAASL,GACtC9G,EAAOI,EAAM6G,GACTjH,EAAKwD,IAAMsD,EACXI,EAAalH,EAAKO,YACXP,EAAKuD,OAASuD,IACrBC,EAAcD,EAAO9G,EAAKuD,MAC1ByD,EAAehH,EAAKwD,IAAMxD,EAAKuD,MAC/B2D,EAAaH,EAAcC,EAAehH,EAAKO,aAE/CxJ,IACAmQ,EAAalH,EAAKO,YAAc,EAAI2G,GAEjClH,EAAKM,WAAa4G,GAdd,GAgBfC,WAAY,SAAUC,EAAOC,EAAOX,GAAxB,GAIJY,GAHAlH,EAAQ7D,KAAK8D,aACbkH,EAAW,EACXC,EAASpH,EAAMF,OAAS,CAExBwG,KACAtG,KAAWqH,MAAMnL,KAAK8D,GAAOsG,UAEjC,GACIY,GAAS7E,KAAKiF,MAAMF,EAASD,GAAY,GACrCnH,EAAMkH,GAAQF,GAASC,EACvBE,EAAWD,GAEPA,IAAWE,GACXF,IAEJE,EAASF,SAERC,IAAaC,EAItB,OAHId,KACAa,EAAWnH,EAAMF,OAAS,EAAIqH,GAE3BA,GAEXI,gBAAiB,SAAUC,EAAGC,EAAMC,GAAnB,GAKTxH,GACAyH,EACAb,EANAlH,EAAOzD,KAAKyL,gBAAgBJ,EAChC,OAAIC,GACOC,EAAY9H,EAAKwD,IAAMxD,EAAKuD,OAEnCjD,EAAasH,EAAIpT,EAAEmD,EAAMwE,EAAUO,OAAOlD,YAAYyO,SAAS9E,KAC/D4E,EAAW/H,EAAKwD,IAAMxD,EAAKuD,MAC3B2D,EAAa5G,EAAaN,EAAKM,WAC/BvJ,IACAmQ,EAAalH,EAAKO,YAAc2G,GAE7B,GAAI5I,MAAK0B,EAAKuD,MAAM2E,UAAYH,GAAYb,EAAalH,EAAKO,gBAEzEyH,gBAAiB,SAAUJ,GAAV,GACTtH,GAAasH,EAAIpT,EAAEmD,EAAMwE,EAAUO,OAAOlD,YAAYyO,SAAS9E,KAC/DgF,EAAY5L,KAAK4K,WAAW,aAAc7G,EAAYvJ,EAC1D,OAAOwF,MAAK8D,aAAa8H,IAE7BC,oBAAqB,SAAUC,GAAV,GAGRpI,GAAOmC,EAFZkG,KACA3I,EAAOpD,KAAKe,eAChB,KAAS2C,EAAI,EAAGmC,EAAIiG,EAAanI,OAAQD,EAAImC,EAAGnC,IAC5CqI,EAASpT,KAAKqT,MAAMD,EAAU/L,KAAKiM,kBAAkBH,EAAapI,IAEtEN,GAAKG,OAAOwI,IAEhBE,kBAAmB,SAAUC,GAAV,GAGXH,GACAI,EAWKzI,EAAOC,EAdZyI,EAAcpM,KAAKiB,iBAAiBiL,EAAWG,eAC/CC,EAAYtM,KAAKiB,iBAAiBiL,EAAWK,YAGjD,KAAKH,IAAgBE,EACjB,QASJ,KAPAH,EAAS,WACL,KACA,KACA,KACA,MACF3R,EAAQ,EAAI0R,EAAWhQ,KAAOgQ,EAAWhQ,MAC3C6P,EAAW/L,KAAKmM,GAAQC,EAAaE,GAC5B5I,EAAI,EAAGC,EAASoI,EAASpI,OAAQD,EAAIC,EAAQD,IAClDqI,EAASrI,GAAG8I,KAAK,YAAcN,EAAWnC,GAE9C,OAAOgC,IAEXU,UAAW,SAAUC,EAAMC,GACvB,GAAIC,GAAQ5M,KAAK6M,cAAcH,EAAMC,GAAI,EAEzC,OADAC,GAAMA,EAAMjJ,OAAS,GAAGC,SAAS,GAAK5D,KAAK8M,QAAO,GAC3CF,GAEXG,UAAW,SAAUL,EAAMC,GACvB,GAAIC,GAAQ5M,KAAK6M,cAAcF,EAAID,GAAM,EAEzC,OADAE,GAAM,GAAGhJ,SAAS,GAAK5D,KAAK8M,QAAO,GAC5BF,EAAMzC,WAEjB6C,UAAW,SAAUN,EAAMC,GACvB,GAAIC,GAAQ5M,KAAKiN,cAAcP,EAAMC,GAAI,EAEzC,OADAC,GAAMA,EAAMjJ,OAAS,GAAGC,SAAS,GAAK5D,KAAK8M,QAAO,GAC3CF,GAEXM,UAAW,SAAUR,EAAMC,GACvB,GAAIC,GAAQ5M,KAAKiN,cAAcN,EAAID,GAAM,EAEzC,OADAE,GAAM,GAAGhJ,SAAS,GAAK5D,KAAK8M,QAAO,GAC5BF,EAAMzC,WAEjB0C,cAAe,SAAUH,EAAMC,EAAIxC,GAApB,GAQPgD,GAPAC,EAAOpN,KACP4M,KACAhG,EAAO,EACPyG,EAAM,EACN/M,EAAQ,EACRwC,EAAS,EACTwK,EAAMnD,EAAU,QAAU,MAE1BoD,EAAU,EACVC,EAAe,EACfC,EAAYzN,KAAK2E,WACjB+I,EAAe,GACfC,EAAUjB,EAAKhG,SAAW+G,EAAYvH,KAAK0H,MAAMH,EAAY,GAAK,EAClEI,EAAQlB,EAAGjG,SAAW+G,EAAYvH,KAAK0H,MAAMH,EAAY,GAAK,EAC9DtN,EAASP,EAAUO,OACnB2N,EAAgB,WAChBlB,EAAMjU,KAAKyU,EAAKW,MAAM5N,EAAOpB,KAAO,IAAMoB,EAAOnB,gBAC7C4H,KAAMA,EAAO,KACbyG,IAAKA,EAAM,KACX/M,MAAOA,EAAQ,SAGnB0N,EAAc,WACdpB,EAAMjU,KAAKyU,EAAKW,MAAM5N,EAAOpB,KAAO,IAAMoB,EAAOlB,cAC7C2H,KAAMA,EAAO,KACbyG,IAAKA,EAAM,KACXvK,OAAQA,EAAS,QAmCzB,OAhCA8D,GAAO8F,EAAKY,GACZD,EAAMM,EACNrN,EAAQoN,EACRP,EAAQR,EAAGW,GAAOZ,EAAKY,GACnBH,EAAQ,IAAMhD,IACd7J,EAAQ4F,KAAK+H,IAAId,GAASO,GAE1BvD,GACAvD,GAAQtG,EACRA,GAASkN,EACTM,MAEAA,IACAlH,GAAQtG,EAAQiN,GAEhBM,EAAQR,GACRvK,EAASuK,EAAMQ,EACf/K,GAAUyK,EACVF,EAAMQ,EACNG,MAEAlL,EAAS+K,EAAQR,EACjBvK,GAAUyK,EACVS,IACAX,GAAOvK,EAASyK,GAEpBjN,EAAQ4F,KAAK+H,IAAIrH,EAAO+F,EAAGW,IACtBnD,IACD7J,GAASkN,EACT5G,GAAQtG,GAEZwN,IACOlB,GAEXK,cAAe,SAAUP,EAAMC,EAAIxC,GAApB,GACPiD,GAAOpN,KACP4M,KACAhG,EAAO,EACPyG,EAAM,EACN/M,EAAQ,EACRwC,EAAS,EACT2K,EAAYzN,KAAK2E,WACjBuJ,EAAgBhI,KAAK0H,MAAMH,EAAY,GACvCC,EAAe,GACfS,EAAc,EAAIT,EAClBP,EAAQR,EAAG3F,MAAQ0F,EAAKzF,IACxBsG,EAAU,EACVC,EAAe,EACfG,EAAUjB,EAAKhG,SAAW+G,EAAYvH,KAAK0H,MAAMH,EAAY,GAAK,EAClEI,EAAQlB,EAAGjG,SAAW+G,EAAYvH,KAAK0H,MAAMH,EAAY,GAAK,EAC9DtN,EAASP,EAAUO,OACnB2N,EAAgB,WAChBlB,EAAMjU,KAAKyU,EAAKW,MAAM5N,EAAOpB,KAAO,IAAMoB,EAAOnB,gBAC7C4H,KAAMA,EAAO,KACbyG,IAAKA,EAAM,KACX/M,MAAOA,EAAQ,SAGnB0N,EAAc,WACdpB,EAAMjU,KAAKyU,EAAKW,MAAM5N,EAAOpB,KAAO,IAAMoB,EAAOlB,cAC7C2H,KAAMA,EAAO,KACbyG,IAAKA,EAAM,KACXvK,OAAQA,EAAS,QA+CzB,OA5CA8D,GAAO8F,EAAKzF,IACZoG,EAAMM,EACNrN,EAAQoN,EACJvD,IACAvD,GAAQ4G,EACJL,EAAQgB,IACR7N,EAAQ6M,GAASO,EAAeH,IAEpCjN,GAASkN,GAEbM,IACAlH,GAAQtG,EAAQiN,EACZJ,GAASgB,IACTrL,EAASqH,EAAUjE,KAAK+H,IAAIJ,EAAQF,GAAWO,EAAgBA,EAC3DL,EAAQF,GACRN,GAAOvK,EACPA,GAAUyK,EACVS,MAEAA,IACAX,GAAOvK,GAEXxC,EAAQoM,EAAKzF,IAAM0F,EAAG3F,MAAQmH,EAC1B7N,EAAQoN,IACRpN,EAAQoN,GAEZ9G,GAAQtG,EAAQiN,EAChBO,KAEAD,EAAQF,GACR7K,EAASuK,EAAMQ,EACfR,EAAMQ,EACN/K,GAAUyK,EACVS,MAEAlL,EAAS+K,EAAQR,EACjBW,IACAX,GAAOvK,GAEXxC,EAAQqM,EAAG3F,MAAQJ,EACduD,IACD7J,GAASkN,GAEbM,IACOlB,GAEXmB,MAAO,SAAU/I,EAAW7E,GACxB,MAAO3G,GAAgB,OACnBwL,UAAWA,EACX1B,MAAOnD,KAGf2M,OAAQ,SAAUsB,GACd,MAAO5U,GAAgB,QAAUwL,UAAWoJ,EAAYxO,EAAUO,OAAOjB,UAAYU,EAAUO,OAAOhB,aAE1GgE,UAAW,WAAA,GAIEO,GACI2K,EAAO1K,EAJhBE,EAAQ7D,KAAK8D,aACbwK,EAAQzK,EAAMF,OACd8D,IACJ,KAAS/D,EAAI,EAAGA,EAAI4K,EAAO5K,IACvB,IAAS2K,EAAI,EAAG1K,EAASE,EAAMH,GAAG4B,KAAM+I,EAAI1K,EAAQ0K,IAChD5G,EAAK9O,KAAKa,EAAgB,OAGlC,OAAOA,GAAgB,WAAY,KAAMiO,IAE7C8G,gBAAiB,SAAU7U,GACvBsG,KAAKuB,UAAY7H,EAAQ8U,QAAQC,SAAS7O,EAAUO,OAAOf,UAAUkJ,IAAI,SAAU,QACnF5O,EAAQgV,SAAS3G,OAAO/H,KAAKuB,YAEjCoN,gBAAiB,SAAU3H,GACvB,GAAIJ,GAAO5G,KAAKqK,QAAQrD,EACxBhH,MAAKuB,UAAU+G,KAAM1B,KAAQA,KAEjCgI,gBAAiB,WACb5O,KAAKuB,UAAU2G,SACflI,KAAKuB,UAAY,MAErBsN,kBAAmB,SAAUxR,GAAV,GAGXyR,GACAC,EAHA5O,EAASP,EAAUO,OACnB6O,EAAUhP,KAAKiB,iBAAiB5D,EAAK0J,IAAIL,SAAW1G,KAAK2E,WAGzDxM,EAAU6H,KAAK7H,QACf8W,EAAW9W,EAAQ8W,QACvBjP,MAAKwB,YAAcvJ,EAAE0D,GAAcwE,OAAQA,KAAWmI,KAClD+E,IAAO,EACPvK,OAAU9C,KAAK0E,iBAEnB1E,KAAKI,QAAQ2H,OAAO/H,KAAKwB,aACzBxB,KAAKyB,eAAiBxJ,EAAE2D,GACpBuE,OAAQA,EACR6G,MAAO3J,EAAK2J,MACZC,IAAK5J,EAAK4J,IACVgI,SAAUA,EAAS3W,MACnB4W,OAAQ/W,EAAQgX,uBAChB7G,KACA+E,IAAO,EACPzG,KAAQ,IAEZ5G,KAAKI,QAAQ2H,OAAO/H,KAAKyB,gBACzBzB,KAAKoP,oBAAsBpV,EAAWgG,KAAKyB,gBAC3CqN,EAAgB5U,EAAY8F,KAAKyB,gBACjCsN,EAAaC,EAAUF,EACnBC,EAAa,IACbA,EAAaC,EAAUhP,KAAK2E,YAEhC3E,KAAKqP,kBAAoBN,GAE7BO,kBAAmB,SAAUtI,EAAOC,EAAKsI,GAAtB,GACX3I,GAAO5G,KAAKqK,QAAQ7P,EAAQyM,EAAMD,GAClCiB,EAAQjI,KAAKqK,QAAQ7P,EAAQwM,EAAQC,GACrC3G,EAAQ2H,EAAQrB,EAChB4I,EAAcD,IAAgB/U,EAAQoM,EAAOqB,EAC7CwH,EAAczP,KAAKwC,YAAczJ,MAAMwB,QAAQmV,YAC/CC,EAAe3P,KAAKoP,oBACpBjX,EAAU6H,KAAK7H,QACf8W,EAAW9W,EAAQ8W,SACnBW,EAAc3X,EAAEmD,EAAMwE,EAAUO,OAAOlD,YAAYyO,SAAS9E,KAAO3O,EAAEmD,EAAMwE,EAAUO,OAAOrD,cAAc4O,SAAS9E,IACnHpM,KACAoM,GAAQgJ,GAEZ5P,KAAKwB,YAAY8G,KACb1B,KAAQA,EACRtG,MAASA,IAETN,KAAKyB,gBACLzB,KAAKyB,eAAeyG,SAExBsH,GAAetJ,KAAKC,MAAMwJ,EAAe,GACrCH,EAAc,EACdA,EAAc,EACPA,EAAcG,EAAeF,IACpCD,EAAcC,EAAcE,GAE5BnV,IACAgV,GAAeI,GAEnB5P,KAAKyB,eAAiBxJ,EAAE2D,GACpBuE,OAAQP,EAAUO,OAClB6G,MAAOA,EACPC,IAAKA,EACLgI,SAAUA,EAAS3W,MACnB4W,OAAQ/W,EAAQgX,uBAChB7G,KACA+E,IAAOrN,KAAKqP,kBACZzI,KAAQ4I,EACRK,YAAaF,IACdG,SAAS9P,KAAKI,UAErB2P,kBAAmB,WACf/P,KAAKwB,YAAY0G,SACjBlI,KAAKwB,YAAc,KACnBxB,KAAKyB,eAAeyG,SACpBlI,KAAKyB,eAAiB,MAE1BuO,8BAA+B,SAAU3C,EAAKzG,EAAMhN,GAArB,GAEvBqW,GAIAC,EACAC,EACAC,CAPJpQ,MAAKqQ,gCACDJ,EAAUjQ,KAAK2B,8BAAgC1J,EAAE4D,GACjDsE,OAAQP,EAAUO,OAClBvG,KAAMA,KACNkW,SAAS9P,KAAKtG,SACdwW,EAAgBhK,KAAKC,MAAMnM,EAAWiW,GAAW,GACjDE,EAAQF,EAAQ/P,KAAK9E,EAAMwE,EAAUO,OAAOV,SAC5C2Q,EAAclK,KAAKC,MAAMnM,EAAWmW,GAAS,GACjDF,EAAQ3H,KACJ+E,IAAOA,GAAOnT,EAAY+V,GAAWG,GACrCxJ,KAAQA,EAAOsJ,IAEnBC,EAAM7H,IAAI,OAAQ4H,EAAgBE,IAEtCC,8BAA+B,WACvBrQ,KAAK2B,+BACL3B,KAAK2B,8BAA8BuG,SAEvClI,KAAK2B,8BAAgC,MAEzC2O,0BAA2B,SAAU5D,EAAMC,EAAI4D,GAC3CvQ,KAAKwQ,4BACDD,EACAvQ,KAAKyQ,4BAA4B/D,EAAMC,GAEvC3M,KAAK0Q,yBAAyBhE,EAAMC,IAG5C+D,yBAA0B,SAAUhE,EAAMC,GAAhB,GAClBxM,GAASP,EAAUO,OACnBwQ,EAAShE,EAAGtB,EAAIqB,EAAKrB,EACrBuF,EAASjE,EAAGkE,EAAInE,EAAKmE,EACrBvQ,EAAQ4F,KAAK4K,KAAKH,EAASA,EAASC,EAASA,GAC7CG,EAAQ7K,KAAK8K,KAAKJ,EAASD,EAC3BA,GAAS,IACTI,GAAS7K,KAAK+K,IAElBhZ,EAAE,eAAkBkI,EAAOpB,KAAO,IAAMoB,EAAOnB,eAAiB,IAAMmB,EAAOd,eAAiB,YAAaiJ,KACvG+E,IAAOX,EAAKmE,EACZjK,KAAQ8F,EAAKrB,EACb/K,MAASA,EACT4Q,mBAAoB,OACpBC,uBAAwB,OACxBC,2BAA4B,OAC5BC,UAAa,UAAYN,EAAQ,OACjCO,gBAAiB,UAAYP,EAAQ,OACrCQ,oBAAqB,UAAYR,EAAQ,SAC1CjB,SAAS9P,KAAKI,UAErBqQ,4BAA6B,SAAU/D,EAAMC,GACzC,GAAI6E,GAAOvZ,EAAE,qBAAwB2H,EAAUO,OAAOd,eAAiB,kGAA4GqN,EAAKrB,EAAI,MAAQqB,EAAKmE,EAAI,WAAelE,EAAGtB,EAAI,MAAQsB,EAAGkE,EAAI,oBAA0Bf,SAAS9P,KAAKI,QAC1RoR,GAAK,GAAGC,UAAYD,EAAK,GAAGC,WAEhCjB,0BAA2B,WACvBxQ,KAAKI,QAAQF,KAAK9E,EAAMwE,EAAUO,OAAOd,gBAAgB6I,UAE7DwJ,mBAAoB,SAAUrU,EAAM3D,EAASiY,GAAzB,GAaZhC,GAZAxP,EAASP,EAAUO,OACnBhI,EAAU6H,KAAK7H,QACfiI,EAAUJ,KAAKI,QACfwR,EAAgBxR,EAAQsL,SACxBrL,EAAeD,EAAQE,QACvBuR,EAAoB9Y,MAAM+Y,WAAW1R,GACrCqE,EAAMxM,EAAEyB,GAASqY,QAAQ,MAAMC,QAC/BC,EAAYxN,EAAIiH,SAChBpQ,EAAWnD,EAAQ8X,SAAW9X,EAAQ8X,QAAQ3U,SAAWvC,MAAMuC,SAASnD,EAAQ8X,QAAQ3U,UAAYQ,EACpG8K,EAAOpM,EAAQmX,GAAaC,EAAchL,KAAOiL,EAAoB9Y,MAAMwB,QAAQmV,aAAeiC,GAAaC,EAAchL,KAAOiL,GACpIxE,EAAM4E,EAAU5E,IAAMnT,EAAYuK,GAAOmN,EAAcvE,IAAMjN,EAAQ8R,YACrEjC,EAAUjQ,KAAK0B,aAAezJ,EAAE,wCAA0CkI,EAAOb,eAAiB,kBAAyBa,EAAOnC,YAAc,iBAEpJiS,GAAQ3H,KACJ1B,KAAQA,EACRyG,IAAOA,IACRyC,SAAS1P,GAASF,KAAK9E,EAAM+E,EAAOnC,aAAa+J,OAAOzM,GACvD6E,OAAQA,EACR9C,KAAMA,EACN4R,SAAU9W,EAAQ8W,SAAS3W,SAE3B4B,EAAY+V,GAAWgC,EAAU5E,IAAMuE,EAAcvE,KACrD4C,EAAQ3H,IAAI,MAAO2J,EAAU5E,IAAMuE,EAAcvE,IAAMnT,EAAY+V,GAAW7P,EAAQ8R,aAE1FvC,EAAe3V,EAAWiW,GACtBN,EAAe/I,EAAOiL,EAAoBxR,IAC1CuG,GAAQ+I,EACJ/I,EAAOiL,IACPjL,EAAOiL,EAAoBxR,GAAgBsP,EAAe,KAE9DM,EAAQ3H,IAAI,OAAQ1B,KAG5BuL,mBAAoB,WACZnS,KAAK0B,cACL1B,KAAK0B,aAAawG,SAEtBlI,KAAK0B,aAAe,MAExB0Q,UAAW,SAAU1Y,GAAV,GACH2Y,GAAc3Y,EAAQgS,SAAS9E,KAC/B0L,EAAe5Y,EAAQ4G,QACvBiS,EAAeF,EAAcC,EAC7B7N,EAAM/K,EAAQ8Y,QAAQ,MACtBC,EAAShO,EAAIiH,SAAS2B,IACtBI,EAAYhJ,EAAI3B,SAChB4P,EAAYD,EAAShF,EACrBrN,EAAUJ,KAAKI,QACfwR,EAAgBxR,EAAQsL,SACxBiH,EAAaf,EAAcvE,IAC3BuF,EAAgBxS,EAAQ0C,SACxB+P,EAAgBF,EAAaC,EAC7BE,EAAclB,EAAchL,KAC5BvG,EAAeD,EAAQE,QACvByS,EAAeD,EAAczS,EAC7B2S,EAAiBja,MAAMwB,QAAQmV,WAC/B+C,GAASE,EACTvS,EAAQ8R,UAAU9R,EAAQ8R,aAAeO,EAASE,IAC3CD,EAAYG,GACnBzS,EAAQ8R,UAAU9R,EAAQ8R,aAAeQ,EAAYM,EAAiBH,IAEtER,EAAcS,GAAeR,EAAejS,GAAgBkS,EAAeQ,GAAgBR,EAAeQ,GAAgBT,EAAejS,EACzID,EAAQ0R,WAAW1R,EAAQ0R,cAAgBS,EAAeS,EAAiBD,KACpER,EAAeQ,GAAgBT,EAAejS,GAAgBgS,EAAcS,GAAeT,EAAcS,GAAeR,EAAejS,IAC9ID,EAAQ0R,WAAW1R,EAAQ0R,cAAgBO,EAAcS,KAGjEG,cAAe,SAAU1I,GAAV,GAGPmB,GAFAwH,EAAYlT,KAAKgH,MACjBmM,EAAUnT,KAAKiH,GAEfsD,IAAQ2I,GAAa3I,EAAO4I,IAC5BzH,EAAS1L,KAAKqK,QAAQE,GAClBxR,MAAMwB,QAAQC,MAAMwF,KAAKtG,WACzBgS,EAAS1L,KAAKwC,YAAckJ,GAEhC3S,MAAM+Y,WAAW9R,KAAKI,QAASsL,KAGvC5H,WAAY,WACR,MAAK9D,MAAKsC,QAAWtC,KAAKsC,OAAOqB,OAG1B3D,KAAKsC,OAAOtC,KAAKsC,OAAOqB,OAAS,OAE5CV,SAAU,SAAUmQ,GAAV,GAEFC,GACArQ,EACAsQ,EACAC,EAEKC,EAAgBC,EAGZC,EAAiBC,EAT1B5Q,KAKA5C,EAASP,EAAUO,MACvB,KAASqT,EAAa,EAAGC,EAAaL,EAAazP,OAAQ6P,EAAaC,EAAYD,IAAc,CAG9F,IAFAH,EAAQD,EAAaI,GACrBxQ,KACS0Q,EAAc,EAAGC,EAAcN,EAAM1P,OAAQ+P,EAAcC,EAAaD,IAC7EJ,EAASD,EAAMK,GACfH,EAAa1Z,EAAiByZ,EAAO1Z,MACrCoJ,EAAQrK,KAAKa,EAAgB,MACzB+L,QAAS+N,EAAOhO,KAChBN,UAAW7E,EAAOzD,QAAU4W,EAAO9N,aAAe,IAAMrF,EAAO1D,WAAa,MAC5E8W,IAERxQ,GAAKpK,KAAKa,EAAgB,KAAM,KAAMwJ,IAE1C,MAAOD,IAEX6Q,OAAQ,SAAU5M,EAAOC,GAAjB,GACA4M,GAKAC,EACAC,EALAlQ,KACA1L,EAAU6H,KAAK7H,QACf2J,EAAe3J,EAAQ2J,aAAakS,WACpChS,EAAa7J,EAAQ6J,WAAWgS,WAGhC/R,EAAW9J,EAAQ8J,QAMvB,KALA+E,EAAQ,GAAIjF,MAAKiF,GACjBC,EAAM,GAAIlF,MAAKkF,GACX9O,EAAQyJ,eACRoF,EAAMiN,SAASnS,GAEZkF,EAAQC,GACX4M,EAAU,GAAI9R,MAAKiF,GACnB+M,EAAQF,EAAQG,WAChBF,EAAaC,GAASjS,GAAgBiS,EAAQ/R,EAC9C6R,EAAQI,SAASJ,EAAQG,WAAa/R,GAClC8R,GAASF,EAAQG,YACjBH,EAAQI,SAASJ,EAAQG,WAAa,EAAI/R,GAEzC9J,EAAQyJ,gBAAiBkS,GAC1BjQ,EAAMlL,MACFqO,MAAOA,EACPC,IAAK4M,EACLrO,cAAesO,EACfxO,KAAM,IAGd0B,EAAQ6M,CAEZ,OAAOhQ,IAEXqQ,MAAO,SAAUlN,EAAOC,GAAjB,GACC4M,GAEAM,EADAtQ,IAIJ,KAFAmD,EAAQ,GAAIjF,MAAKiF,GACjBC,EAAM,GAAIlF,MAAKkF,GACRD,EAAQC,GACX4M,EAAU5M,EAAMlO,MAAMwR,KAAK6J,QAAQpN,GAASC,EAAMlO,MAAMwR,KAAK6J,QAAQpN,GACrEmN,EAAYnU,KAAKqU,WAAWrN,GACvBhH,KAAK7H,QAAQ0J,eAAgBsS,GAC9BtQ,EAAMlL,MACFqO,MAAOA,EACPC,IAAK4M,EACLrO,cAAe2O,EACf7O,KAAM,IAGd0B,EAAQ6M,CAEZ,OAAOhQ,IAEXyQ,OAAQ,SAAUtN,EAAOC,GAAjB,GACA4M,GAGAU,EACAjP,EAHAzB,KACA2Q,EAAWxU,KAAKyU,eAAeD,QAKnC,KAFAxN,EAAQ,GAAIjF,MAAKiF,GACjBC,EAAM,GAAIlF,MAAKkF,GACRD,EAAQC,GACX4M,EAAU9a,MAAMwR,KAAKmK,UAAU3b,MAAMwR,KAAKoK,QAAQ3N,EAAO,GAAIwN,EAAU,GACnEX,EAAU5M,IACV4M,EAAU5M,GAEdsN,EAAWvU,KAAKkU,MAAMlN,EAAO6M,GAC7BvO,EAAOiP,EAAS5Q,OACZ2B,EAAO,GACPzB,EAAMlL,MACFqO,MAAOuN,EAAS,GAAGvN,MACnBC,IAAKsN,EAASjP,EAAO,GAAG2B,IACxB3B,KAAMA,IAGd0B,EAAQ6M,CAEZ,OAAOhQ,IAEX+Q,QAAS,SAAU5N,EAAOC,GAAjB,GACD4M,GACAgB,EAEAN,EACAjP,EAFAzB,IAKJ,KAFAmD,EAAQ,GAAIjF,MAAKiF,GACjBC,EAAM,GAAIlF,MAAKkF,GACRD,EAAQC,GACX4M,EAAU,GAAI9R,MAAKiF,GACnB6N,EAAW9b,MAAMwR,KAAKuK,gBAAgB,GAAI/S,MAAK8R,EAAQkB,SAASlB,EAAQmB,WAAa,KACrFnB,EAAU5M,EAAM4N,EAAW5N,EAAM4N,EACjCN,EAAWvU,KAAKkU,MAAMlN,EAAO6M,GAC7BvO,EAAOiP,EAAS5Q,OACZ2B,EAAO,GACPzB,EAAMlL,MACFqO,MAAOuN,EAAS,GAAGvN,MACnBC,IAAKsN,EAASjP,EAAO,GAAG2B,IACxB3B,KAAMA,IAGd0B,EAAQ6M,CAEZ,OAAOhQ,IAEXoR,OAAQ,SAAUjO,EAAOC,GAAjB,GACA4M,GACAqB,EACAL,EACAhR,IAGJ,KAFAmD,EAAQ,GAAIjF,MAAKiF,GACjBC,EAAM,GAAIlF,MAAKkF,GACRD,EAAQC,GACX4M,EAAU,GAAI9R,MAAKiF,GACnB6M,EAAU9a,MAAMwR,KAAKuK,gBAAgB,GAAI/S,MAAK8R,EAAQkB,SAAS,MAC3DlB,GAAW5M,IACX4M,EAAU5M,GAEd4N,EAAWhB,EAAQmB,YAAc,GACjCE,EAAYL,EAAW7N,EAAMgO,WAC7BnR,EAAMlL,MACFqO,MAAOA,EACPC,IAAK4M,EACLvO,KAAM4P,IAEVlO,EAAQ6M,CAEZ,OAAOhQ,IAEXsR,aAAc,SAAUtR,EAAOvI,GAAjB,GAENmI,GACKC,EAAOmC,EAFZuP,IAEJ,KAAS1R,EAAI,EAAGmC,EAAIhC,EAAMF,OAAQD,EAAImC,EAAGnC,IACrCD,EAAOI,EAAMH,GACb0R,EAAQzc,MACJiB,KAAM0B,EAASmI,GACf+B,eAAgB/B,EAAK+B,aACrBF,KAAM7B,EAAK6B,MAGnB,OAAO8P,IAEXf,WAAY,SAAU9J,GAAV,GAGC7G,GAAOmC,EAFZ5J,EAAMsO,EAAK8K,SACX7c,EAAWwH,KAAKO,SACpB,KAASmD,EAAI,EAAGmC,EAAIrN,EAASmL,OAAQD,EAAImC,EAAGnC,IACxC,GAAIlL,EAASkL,KAAOzH,EAChB,OAAO,CAGf,QAAO,GAEXwY,aAAc,WACV,MAAO1b,OAAMuc,aAAaC,UAAUC,UAExC3Q,mBAAoB,WAAA,GAOZpB,GANAgS,EAAczV,KAAK0V,kBACnBC,EAAa3V,KAAKqK,QAAQoL,GAC1B/b,EAAUzB,EAAE,sCACZqE,EAAasD,EAAUO,OACvByV,EAAa3d,EAAEmD,EAAMkB,EAAWQ,cAChCG,EAAahF,EAAEmD,EAAMkB,EAAWW,WAE/B+C,MAAKI,SAAYJ,KAAK8D,aAAaH,SAGxC3D,KAAKI,QAAQF,KAAK,mBAAmBgI,SACrCzE,EAAOzD,KAAK8D,aAAa9D,KAAK4K,WAAW,QAAS6K,IAC9CA,EAAchS,EAAKuD,OAASyO,EAAchS,EAAKwD,MAG/C2O,EAAWjS,QAAU1G,EAAW0G,SAChCgS,GAAc1Y,EAAWyO,SAAS9E,KAAOgP,EAAWlK,SAAS9E,MAEjElN,EAAQ4O,KACJ1B,KAAM+O,EAAa,KACnBtI,IAAK,MACL/M,MAAO,MACPwC,OAAQ9C,KAAK0E,eAAiB,OAC/BoL,SAAS9P,KAAKI,YAErBsV,gBAAiB,WACb,MAAO,IAAI3T,OAEfb,aAAc,WACV,GAAI2U,GAAgB7V,KAAK7H,QAAQgK,iBAC7B0T,MAAkB,GAA0CjR,SAAjCiR,EAAczT,iBACzCpC,KAAK6E,qBACL7E,KAAK8V,wBAA0BC,YAAY1b,EAAM2F,KAAK6E,mBAAoB7E,MAAO6V,EAAczT,mBAI3GhI,IAAO,EAAMwF,GAAaO,OAAQ7D,IAClCvD,MAAMQ,GAAGyc,aAAepW,EAAUxF,QAC9BhC,KAAM,MACND,SACI8d,mBAAoB5a,EACpB6a,kBAAmB3a,EACnB4T,oBAAqB,sBAEzBgH,MAAO,SAAUA,GACb,GAAIC,GAAepW,KAAK7H,QAAQge,KAChCnW,MAAKgH,MAAQjO,MAAMwR,KAAK8L,QAAQF,EAAMnP,OACtChH,KAAKiH,IAAMlO,MAAMwR,KAAK8L,QAAQF,EAAMlP,MAChClO,MAAMwR,KAAK+L,gBAAgBH,EAAMlP,KAAO,GAAKjH,KAAKiH,IAAI0E,YAAc3L,KAAKgH,MAAM2E,aAC/E3L,KAAKiH,IAAMlO,MAAMwR,KAAKoK,QAAQ3U,KAAKiH,IAAK,IAExCmP,GAAgBA,EAAapP,QAC7BhH,KAAKgH,MAAQjO,MAAMwR,KAAK8L,QAAQD,EAAapP,OAC7ChH,KAAKgH,MAAMiN,SAASmC,EAAapP,MAAMgN,aAEvCoC,GAAgBA,EAAanP,MAC7BjH,KAAKiH,IAAMlO,MAAMwR,KAAK8L,QAAQD,EAAanP,KAC3CjH,KAAKiH,IAAIgN,SAASmC,EAAanP,IAAI+M,cAG3CzR,aAAc,WAAA,GAENgU,GAEAxC,EAIKrQ,EAAOmC,EAHZhC,KAJA0Q,EAKOvU,KAAKkU,MAAMlU,KAAKgH,MAAOhH,KAAKiH,KAHnCuP,IAKJ,KAAS9S,EAAI,EAAGmC,EAAI0O,EAAS5Q,OAAQD,EAAImC,EAAGnC,IACxC6S,EAAUhC,EAAS7Q,GACnBqQ,EAAQ/T,KAAK4T,OAAO2C,EAAQvP,MAAOuP,EAAQtP,KAC3CsP,EAAQjR,KAAOyO,EAAMpQ,OACrB6S,EAAU7d,KAAKqT,MAAMwK,EAAWzC,EAIpC,OAFAlQ,GAAMlL,KAAK4b,GACX1Q,EAAMlL,KAAK6d,GACJ3S,GAEXlB,QAAS,WAAA,GACDI,MACA5K,EAAU6H,KAAK7H,OAGnB,OAFA4K,GAAKpK,KAAKqH,KAAKmV,aAAanV,KAAKsC,OAAO,GAAIvJ,MAAMuC,SAASnD,EAAQ+d,qBACnEnT,EAAKpK,KAAKqH,KAAKmV,aAAanV,KAAKsC,OAAO,GAAIvJ,MAAMuC,SAASnD,EAAQ8d,sBAC5DlT,KAGfhK,MAAMQ,GAAGkd,cAAgB7W,EAAUxF,QAC/BhC,KAAM,OACND,SACI+d,kBAAmB3a,EACnBmb,mBAAoBlb,EACpB2T,oBAAqB,sBAEzBgH,MAAO,SAAUA,GAAV,GAKCQ,GAJAP,EAAepW,KAAK7H,QAAQge,MAC5B1B,EAAezU,KAAKyU,eACpBD,EAAWC,EAAaD,SACxBoC,EAAWT,EAAMlP,GAEjBuN,KAAaoC,EAASvB,UACtBuB,EAASC,QAAQD,EAASP,UAAY,GAE1CrW,KAAKgH,MAAQjO,MAAMwR,KAAK8L,QAAQtd,MAAMwR,KAAKmK,UAAUyB,EAAMnP,MAAOwN,OAClExU,KAAKiH,IAAMlO,MAAMwR,KAAK8L,QAAQtd,MAAMwR,KAAKmK,UAAUkC,EAAUpC,EAAU,IACnE4B,GAAgBA,EAAapP,QAC7BhH,KAAKgH,MAAQjO,MAAMwR,KAAK8L,QAAQD,EAAapP,QAE7CoP,GAAgBA,EAAanP,MAC7B0P,EAAS,GAAI5U,MAAKqU,EAAanP,KAE3BjH,KAAKiH,IADLlO,MAAMwR,KAAK8L,QAAQM,GAAUP,EAAanP,IAC/BlO,MAAMwR,KAAK8L,QAAQ,GAAItU,MAAK4U,EAAOE,QAAQF,EAAON,UAAY,KAE9Dtd,MAAMwR,KAAK8L,QAAQM,KAI1CpU,aAAc,WACV,GAAIsB,KAGJ,OAFAA,GAAMlL,KAAKqH,KAAKsU,OAAOtU,KAAKgH,MAAOhH,KAAKiH,MACxCpD,EAAMlL,KAAKqH,KAAKkU,MAAMlU,KAAKgH,MAAOhH,KAAKiH,MAChCpD,GAEXlB,QAAS,WAAA,GACDI,MACA5K,EAAU6H,KAAK7H,OAGnB,OAFA4K,GAAKpK,KAAKqH,KAAKmV,aAAanV,KAAKsC,OAAO,GAAIvJ,MAAMuC,SAASnD,EAAQue,sBACnE3T,EAAKpK,KAAKqH,KAAKmV,aAAanV,KAAKsC,OAAO,GAAIvJ,MAAMuC,SAASnD,EAAQ+d,qBAC5DnT,KAGfhK,MAAMQ,GAAGud,eAAiBlX,EAAUxF,QAChChC,KAAM,QACND,SACIue,mBAAoBlb,EACpBub,oBAAqBtb,EACrB0T,oBAAqB,qBAEzBgH,MAAO,SAAUA,GAAV,GAECQ,GADAP,EAAepW,KAAK7H,QAAQge,KAEhCnW,MAAKgH,MAAQjO,MAAMwR,KAAKuK,gBAAgBqB,EAAMnP,OAC9ChH,KAAKiH,IAAMlO,MAAMwR,KAAKoK,QAAQ5b,MAAMwR,KAAK8L,QAAQtd,MAAMwR,KAAKyM,eAAeb,EAAMlP,MAAO,GACpFmP,GAAgBA,EAAapP,QAC7BhH,KAAKgH,MAAQjO,MAAMwR,KAAK8L,QAAQD,EAAapP,QAE7CoP,GAAgBA,EAAanP,MAC7B0P,EAAS,GAAI5U,MAAKqU,EAAanP,KAE3BjH,KAAKiH,IADLlO,MAAMwR,KAAK8L,QAAQM,GAAUP,EAAanP,IAC/BlO,MAAMwR,KAAK8L,QAAQ,GAAItU,MAAK4U,EAAOE,QAAQF,EAAON,UAAY,KAE9Dtd,MAAMwR,KAAK8L,QAAQM,KAI1CpU,aAAc,WACV,GAAIsB,KAGJ,OAFAA,GAAMlL,KAAKqH,KAAK4U,QAAQ5U,KAAKgH,MAAOhH,KAAKiH,MACzCpD,EAAMlL,KAAKqH,KAAKsU,OAAOtU,KAAKgH,MAAOhH,KAAKiH,MACjCpD,GAEXlB,QAAS,WAAA,GACDI,MACA5K,EAAU6H,KAAK7H,OAGnB,OAFA4K,GAAKpK,KAAKqH,KAAKmV,aAAanV,KAAKsC,OAAO,GAAIvJ,MAAMuC,SAASnD,EAAQ4e,uBACnEhU,EAAKpK,KAAKqH,KAAKmV,aAAanV,KAAKsC,OAAO,GAAIvJ,MAAMuC,SAASnD,EAAQue,sBAC5D3T,KAGfhK,MAAMQ,GAAG0d,cAAgBrX,EAAUxF,QAC/BhC,KAAM,OACND,SACI+e,mBAAoBxb,EACpBqb,oBAAqBtb,EACrB0T,oBAAqB,qBAEzBgH,MAAO,SAAUA,GAAV,GAECrB,GADAsB,EAAepW,KAAK7H,QAAQge,KAEhCnW,MAAKgH,MAAQjO,MAAMwR,KAAKuK,gBAAgB,GAAI/S,MAAKoU,EAAMnP,MAAM+N,SAAS,KACtE/U,KAAKiH,IAAMlO,MAAMwR,KAAKuK,gBAAgB,GAAI/S,MAAKoU,EAAMlP,IAAI8N,SAAS,MAC9DqB,GAAgBA,EAAapP,QAC7BhH,KAAKgH,MAAQjO,MAAMwR,KAAKuK,gBAAgBsB,EAAapP,QAErDoP,GAAgBA,EAAanP,MAC7B6N,EAAkB/b,MAAMwR,KAAKuK,gBAAgBsB,EAAanP,KAC1DjH,KAAKiH,IAAMlO,MAAMwR,KAAK8L,QAAQ,GAAItU,MAAK+S,EAAgBC,SAASD,EAAgBE,WAAa,OAGrGzS,aAAc,WAAA,GACNsB,MACAsT,EAAanX,KAAK4U,QAAQ5U,KAAKgH,MAAOhH,KAAKiH,IAM/C,OALAhP,GAAEkf,GAAYC,KAAK,SAAUC,EAAO5T,GAChCA,EAAK6B,KAAO,IAEhBzB,EAAMlL,KAAKqH,KAAKiV,OAAOjV,KAAKgH,MAAOhH,KAAKiH,MACxCpD,EAAMlL,KAAKwe,GACJtT,GAEXlB,QAAS,WAAA,GACDI,MACA5K,EAAU6H,KAAK7H,OAGnB,OAFA4K,GAAKpK,KAAKqH,KAAKmV,aAAanV,KAAKsC,OAAO,GAAIvJ,MAAMuC,SAASnD,EAAQ+e,sBACnEnU,EAAKpK,KAAKqH,KAAKmV,aAAanV,KAAKsC,OAAO,GAAIvJ,MAAMuC,SAASnD,EAAQ4e,uBAC5DhU,KAGX3J,GACAgP,QAAS,6BACTzL,WAAY,gBACZC,eAAgB,qBAChBC,YAAa,iBACbya,gBAAiB,iBACjBxa,aAAc,iBACdI,oBAAqB,uBACrBG,KAAM,SACN0B,KAAM,SACNL,iBAAkB,kBAClBC,qBAAsB,aACtBZ,eAAgB,oBAChBK,aAAc,kBACdD,WAAY,gBACZoZ,eAAgB,qBAChB9Z,SAAU,cACVG,QAAS,aACTC,aAAc,eACdC,WAAY,aACZ0Z,QAAS,gBACTC,SAAU,mBACVC,OAAQ,YAERre,EAAgBN,MAAMQ,GAAGF,cAAgBC,EAAOc,QAChDyF,KAAM,SAAUnG,EAASvB,GACrBmB,EAAOwG,GAAGD,KAAKE,KAAKC,KAAMtG,EAASvB,GAC9B6H,KAAK7H,QAAQG,OAAU0H,KAAK7H,QAAQG,MAAMqL,SAC3C3D,KAAK7H,QAAQG,OACT,MACA,OACA,UAGRkC,EAAQzB,MAAMwB,QAAQC,MAAMd,GAC5BsG,KAAK2X,WACL3X,KAAK4X,YACL5X,KAAK6X,SACL7X,KAAK8X,cACL9X,KAAK+X,aACL/X,KAAKgY,aACLhY,KAAKiY,0BACLjY,KAAKkY,6BACLlY,KAAKmY,gBACLnY,KAAKoY,YAETjgB,SACIC,KAAM,gBACN6W,UACI3W,OACI2D,IAAK,MACLE,KAAM,OACNC,MAAO,QACPC,KAAM,OACN2K,MAAO,QACPC,IAAK,QAGbqE,MAAM,EACN+M,YAAY,EACZxP,UAAU,GAEd1H,QAAS,WACL7H,EAAOwG,GAAGqB,QAAQpB,KAAKC,MACvBoB,aAAapB,KAAKqB,iBACdrB,KAAK8V,yBACLwC,cAActY,KAAK8V,yBAEvB9V,KAAKuY,YAAYvY,KAAKwY,eAClBxY,KAAKyY,gBACLzY,KAAKyY,eAAetX,UAEpBnB,KAAK0Y,kBACL1Y,KAAK0Y,iBAAiBvX,UAEtBnB,KAAK2Y,mBACL3Y,KAAK2Y,kBAAkBxX,UAEvBnB,KAAK4Y,sBACL5Y,KAAK4Y,qBAAqBzX,UAE1BnB,KAAK6Y,OACL7Y,KAAK6Y,MAAM1X,UAEfnB,KAAKQ,YAAc,KACnBR,KAAKU,UAAY,KACjBV,KAAKe,gBAAkB,KACvBf,KAAKoI,QAAQ0Q,IAAIje,GACjB9B,MAAMoI,QAAQnB,KAAKoI,UAEvBuP,SAAU,WAAA,GACFxX,GAAS9G,EAAc8G,OACvBiN,EAAOpN,KACP7H,EAAU6H,KAAK7H,QACf4gB,EAAgB,WAAA,GAGZC,GACAC,EAHAxL,QAAmBtV,GAAQsV,YAAc7S,EAASzC,EAAQsV,UAAYtV,EAAQsV,UAAY,KAC1FpK,EAAQpL,EAAEc,MAAMmW,OAAOnT,EAA2B0R,IAGlDrN,EAAUgN,EAAKhF,QAAQlI,KAAK9E,EAAM+E,EAAOrD,aAK7C,OAJAsD,GAAQ2H,OAAO1E,GACf2V,EAAsB9e,EAAYmJ,EAAMnD,KAAK,OAC7C+Y,EAAuB5V,EAAMnD,KAAK,MAAM4C,SACxCO,EAAM6E,UAEFzD,IAAOuU,EACPvT,KAAQwT,GAGhBjZ,MAAKoI,QAAUpI,KAAKtG,QAAQ+U,SAAStO,EAAOiI,SAASL,OAAO,eAAkB5H,EAAOxD,WAAa,iBAAqBwD,EAAOvD,eAAiB,kBAAmBmL,OAAO,eAAkB5H,EAAOmX,gBAAkB,iBAAqBnX,EAAOrD,aAAe,uBAA2BqD,EAAOjD,oBAAsB,kBACnT/E,EAAQsV,YACRzN,KAAKkZ,gBAAkBH,MAG/BnB,UAAW,WAAA,GACHzX,GAAS9G,EAAc8G,OACvBiD,EAAOrK,MAAMU,IAAI0f,KACjB/Q,EAAUpI,KAAKoI,OACnBpI,MAAKQ,YAAc,GAAI4C,GAAKgF,EAAQlI,KAAK9E,EAAM+E,EAAOvD,gBAAgB,IACtEoD,KAAKU,UAAY,GAAI0C,GAAKgF,EAAQlI,KAAK9E,EAAM+E,EAAOrD,cAAc,IAClEkD,KAAKe,gBAAkB,GAAIqC,GAAKgF,EAAQlI,KAAK9E,EAAM+E,EAAOjD,qBAAqB,KAEnF2a,OAAQ,WAAA,GAEAuB,GACAC,EACAjhB,EACAkhB,EACA7B,EAEK/T,EAAOmC,EAPZvN,EAAQ0H,KAAK7H,QAAQG,KAOzB,KADA0H,KAAK1H,SACIoL,EAAI,EAAGmC,EAAIvN,EAAMqL,OAAQD,EAAImC,EAAGnC,IACrC0V,EAAO9gB,EAAMoL,GACb2V,EAAatf,EAAcqf,GACvBC,GAAcD,EAAKf,cAAe,IAGtCjgB,EAAOihB,EAAkC,gBAAdD,GAAKld,KAAoBkd,EAAKnZ,MAAQmZ,EAAKld,KAAOkd,EAC7EE,EAActd,EAAa5D,GACvBkhB,IACID,IACAD,EAAKld,KAAOod,EAAYpd,MAE5Bod,EAAYrZ,MAAQD,KAAK7H,QAAQ8W,SAAS3W,MAAMF,IAEpDghB,EAAOhf,GAAS6F,MAAO7H,GAAQkhB,EAAaD,EAAaD,MACrDhhB,IACA4H,KAAK1H,MAAMF,GAAQghB,EACd3B,IAAY2B,EAAK3B,WAClBA,EAAWrf,IAInBqf,KACAzX,KAAKuZ,kBAAoB9B,IAGjC2B,KAAM,SAAUhhB,GAQZ,MAPIA,KACA4H,KAAKwZ,YAAYphB,GACjB4H,KAAKyZ,QAAQ,YACTL,KAAMhhB,EACNshB,OAAQ,gBAGT1Z,KAAKwY,eAEhBgB,YAAa,SAAUphB,GACfA,GAAQ4H,KAAK1H,MAAMF,KACf4H,KAAKwY,eACLxY,KAAKuY,YAAYvY,KAAKwY,eAE1BxY,KAAKwY,cAAgBxY,KAAK2Z,gBAAgBvhB,GAC1C4H,KAAKuZ,kBAAoBnhB,IAGjCwhB,aAAc,SAAUvC,GAAV,GACN+B,GACA9gB,EAAQ0H,KAAK1H,KACjB,KAAK8gB,IAAQ9gB,GAAO,CAChB,IAAK+e,EACD,MAAO+B,EAEX/B,OAGRsC,gBAAiB,SAAUvhB,GAAV,GAGL8D,GAKI2d,EAEAC,EATRV,EAAOpZ,KAAK1H,MAAMF,EACtB,IAAIghB,EAAM,CAKN,GAJIld,EAAOkd,EAAKld,KACI,gBAATA,KACPA,EAAOnD,MAAMghB,OAAOX,EAAKld,MAAM8d,UAE/B9d,EAcA,KAAU+d,OAAM,wBAbZJ,MACJzf,EAAOyf,EAAU7Z,KAAK7H,QAAQge,MAAOiD,EAAKjD,OACtC2D,EAAUV,EAAK7O,MAAQvK,KAAK7H,QAAQoS,KACxC6O,EAAO,GAAIld,GAAK8D,KAAKoI,QAASlQ,EAAYkC,GAAO,GAC7CqG,WAAYT,KAAKQ,YACjBG,SAAUX,KAAKU,UACfM,eAAgBhB,KAAKe,gBACrByD,eAAgBxE,KAAKkZ,iBACtBE,EAAMpZ,KAAK7H,SACVoS,KAAMuP,EACN3D,MAAO0D,MAMnB,MAAOT,IAEXb,YAAa,SAAUa,GACfA,GACAA,EAAKjY,WAGb+Y,OAAQ,SAAUjW,GAAV,GAeA+C,GACAC,EAfAkT,GACAtP,MAAO,QACPyC,IAAK,OAEL8M,GACAvP,MAAO,MACPyC,IAAK,OAET,OAAKrJ,IAAUA,EAAMN,QAMjBqD,EAAQ,GAAItM,GAAMuJ,GAAOoW,KAAKF,GAAYG,UAAU,GAAGtT,OAAS,GAAIjF,MACpEkF,EAAM,GAAIvM,GAAMuJ,GAAOoW,KAAKD,GAAUE,UAAU,GAAGrT,KAAO,GAAIlF,OAE9DiF,MAAO,GAAIjF,MAAKiF,GAChBC,IAAK,GAAIlF,MAAKkF,MARVD,MAAO,GAAIjF,MACXkF,IAAK,GAAIlF,QAUrBwY,QAAS,SAAUtW,GAAV,GACDmV,GAAOpZ,KAAKoZ,OACZjD,EAAQnW,KAAKka,OAAOjW,GACpBsG,EAAO6O,EAAKjhB,QAAQoS,IACxBvK,MAAKwa,OAASvW,EACdmV,EAAKjD,MAAMA,GACXiD,EAAK/W,eACL+W,EAAK7V,OAAOU,GACRsG,GACA6O,EAAKnG,cAAc1I,IAG3BsB,oBAAqB,SAAUC,GAC3B9L,KAAKoZ,OAAOvN,oBAAoBC,IAEpC2O,WAAY,SAAU1Q,GAAV,GAGJ1M,GACKqG,EAHLO,EAAQjE,KAAKwa,OACb7W,EAASM,EAAMN,MAEnB,KAASD,EAAI,EAAGA,EAAIC,EAAQD,IAExB,GADArG,EAAO4G,EAAMP,GACTrG,EAAK0M,MAAQA,EACb,MAAO1M,IAInB0a,WAAY,WAAA,GAEJre,GACA2D,EACAqd,EACAlQ,EAJA4C,EAAOpN,KAKPsL,EAAOtL,KAAK7H,QAAQmT,KACpBnL,EAAS9G,EAAc8G,OACvB0I,EAAW7I,KAAK7H,QAAQ0Q,SACxB8R,EAAU,WACVvN,EAAKgM,OAAOxK,kBACRlV,GACAA,EAAQ4O,IAAI,UAAW,GAE3B5O,EAAU,KACV2D,EAAO,KACP+P,EAAKwN,gBAAiB,EAErB/R,IAAYA,EAASgS,QAAS,GAAShS,EAASU,UAAW,IAGhEvJ,KAAKyY,eAAiB,GAAI1f,OAAMQ,GAAGuhB,UAAU9a,KAAKoI,SAC9C2S,SAAU,EACVC,OAAQ5f,EAAM+E,EAAO9C,KACrB4d,WAAYliB,MAAMwB,QAAQ2gB,SAC1BC,OAAQ/f,EAAM+E,EAAOzB,mBAEzBsB,KAAKyY,eAAe2C,KAAK,YAAa,SAAUC,GAC5C,GAAIjC,GAAOhM,EAAKgM,MAGhB,OAFA1f,GAAU2hB,EAAEC,cAAc5M,SAC1BrR,EAAO+P,EAAKqN,WAAWY,EAAEC,cAAc9O,KAAK,aACxCY,EAAKqM,QAAQ,aAAepc,KAAMA,QAClCge,GAAEE,kBAGNb,EAAerd,EAAK2J,MACpBwD,EAAc4O,EAAKhO,gBAAgBiQ,EAAEhQ,EAAEmQ,SAAUlQ,GAAQoP,EACzDtB,EAAK7K,gBAAgB7U,GACrBA,EAAQ4O,IAAI,UAAW,IACvBlH,aAAagM,EAAK/L,sBAClB+L,EAAKwN,gBAAiB,MACvBQ,KAAK,OAAQriB,MAAM0iB,SAAS,SAAUJ,GAAV,GAIvBjC,GACA7O,EACAmR,CALCtO,GAAKwN,iBAGNxB,EAAOhM,EAAKgM,OACZ7O,EAAO,GAAIxI,MAAKqX,EAAKhO,gBAAgBiQ,EAAEhQ,EAAEmQ,SAAUlQ,GAAQd,GAC3DkR,EAAiBnR,EAChB6C,EAAKqM,QAAQ,QACVpc,KAAMA,EACN2J,MAAOuD,MAEXmQ,EAAenQ,EACX/P,IACAkhB,EAAiB,GAAI3Z,MAAK2Y,EAAa/O,UAAYtO,EAAKmO,aAE5D4N,EAAKzK,gBAAgB+M,MAE1B,KAAKN,KAAK,UAAW,WACpBhO,EAAKqM,QAAQ,WACTpc,KAAMA,EACN2J,MAAO0T,IAEXC,MACDS,KAAK,aAAc,WAClBT,MACDgB,WAAWP,KAAK,SAAU,WACzBviB,QAGRmf,WAAY,WAAA,GAEJte,GACA2D,EACAqd,EACAkB,EACArM,EALAnC,EAAOpN,KAMPsL,EAAOtL,KAAK7H,QAAQmT,KACpBnL,EAAS9G,EAAc8G,OACvB0I,EAAW7I,KAAK7H,QAAQ0Q,SACxB8R,EAAU,WACVvN,EAAKgM,OAAOrJ,oBACZrW,EAAU,KACV2D,EAAO,KACP+P,EAAKwN,gBAAiB,EAErB/R,IAAYA,EAASgB,UAAW,GAAShB,EAASU,UAAW,IAGlEvJ,KAAK0Y,iBAAmB,GAAI3f,OAAMQ,GAAGuhB,UAAU9a,KAAKoI,SAChD2S,SAAU,EACVC,OAAQ5f,EAAM+E,EAAOzB,iBACrBuc,YAAY,IAEhBjb,KAAK0Y,iBAAiB0C,KAAK,YAAa,SAAUC,GAO9C,MANA9L,GAAc8L,EAAEC,cAAcO,SAAS1b,EAAOxB,sBAC1CnE,IACA+U,GAAeA,GAEnB7V,EAAU2hB,EAAEC,cAAc9I,QAAQpX,EAAM+E,EAAO9C,MAC/CA,EAAO+P,EAAKqN,WAAW/gB,EAAQ8S,KAAK,aAChCY,EAAKqM,QAAQ,eAAiBpc,KAAMA,QACpCge,GAAEE,kBAGNb,EAAerd,EAAK2J,MACpB4U,EAAave,EAAK4J,IAClBmG,EAAKgM,OAAOvK,kBAAkBxR,GAC9B+D,aAAagM,EAAK/L,sBAClB+L,EAAKwN,gBAAiB,MACvBQ,KAAK,OAAQriB,MAAM0iB,SAAS,SAAUJ,GAAV,GAIvBjC,GACA7O,CAJC6C,GAAKwN,iBAGNxB,EAAOhM,EAAKgM,OACZ7O,EAAO6O,EAAKhO,gBAAgBiQ,EAAEhQ,EAAEmQ,SAAUlQ,GAAOiE,GACjDA,EAEImL,EADAnQ,EAAOqR,EACQrR,EAEAqR,EAIfA,EADArR,EAAOmQ,EACMnQ,EAEAmQ,EAGhBtN,EAAKqM,QAAQ,UACVpc,KAAMA,EACN2J,MAAO0T,EACPzT,IAAK2U,KAETxC,EAAK9J,kBAAkBoL,EAAckB,EAAYrM,KAEtD,KAAK6L,KAAK,UAAW,WACpBhO,EAAKqM,QAAQ,aACTpc,KAAMA,EACNkS,YAAaA,EACbvI,MAAO0T,EACPzT,IAAK2U,IAETjB,MACDS,KAAK,aAAc,WAClBT,MACDgB,WAAWP,KAAK,SAAU,WACzBviB,QAGRof,wBAAyB;AAAA,GAEjB5a,GACAsL,EACAmT,EACAC,EACAC,EACAC,EACAC,EACAnN,EACAS,EAEArC,EAXAC,EAAOpN,KAUPG,EAAS9G,EAAc8G,OAEvB0I,EAAW7I,KAAK7H,QAAQ0Q,SACxB8R,EAAU,WACVvN,EAAKgM,OAAO/I,gCACZ1H,EAAc,KACdtL,EAAO,KACP+P,EAAKwN,gBAAiB,GAEtBuB,EAAgB,SAAU7b,GAC1BqI,EAAYzI,KAAK9E,EAAM+E,EAAO/B,cAAckC,MAAMA,GAAO2G,MAAMmV,SAAShhB,EAAM+E,EAAOpC,gBAAgBuK,IAAI9N,EAAQ,QAAU,OAAQ8F,GAElIuI,IAAYA,EAASS,uBAAwB,GAAST,EAASU,UAAW,IAG/EvJ,KAAK2Y,kBAAoB,GAAI5f,OAAMQ,GAAGuhB,UAAU9a,KAAKoI,SACjD2S,SAAU,EACVC,OAAQ5f,EAAM+E,EAAOpC,eACrBkd,YAAY,IAEhBjb,KAAK2Y,kBAAkByC,KAAK,YAAa,SAAUC,GAC/C,MAAIjO,GAAKqM,QAAQ,0BACb4B,GAAEE,kBAGN5S,EAAc0S,EAAEC,cAAcc,SAAShhB,EAAM+E,EAAO9C,MACpDA,EAAO+P,EAAKqN,WAAW9R,EAAY6D,KAAK,aACxC0P,EAAyB7e,EAAKmM,gBAC9BsS,EAAoBnT,EAAY+C,SAChCqQ,EAAiB/b,KAAKtG,QAAQgS,SAC9BsQ,EAAuBrT,EAAYzI,KAAK9E,EAAM+E,EAAO/B,cAAckC,QACnE2b,EAAkBjiB,EAAW2O,GAC7BvH,aAAagM,EAAK/L,sBAClB+L,EAAKwN,gBAAiB,MACvBQ,KAAK,OAAQriB,MAAM0iB,SAAS,SAAUJ,GACrC,GAAKjO,EAAKwN,eAAV,CAGAzN,EAAQ3S,GAAS6gB,EAAEhQ,EAAEgR,aAAehB,EAAEhQ,EAAEgR,YACxC,IAAIC,GAAepW,KAAKmB,IAAI,EAAGnB,KAAKqW,IAAIN,EAAiBD,EAAuB7O,GAChF+O,GAAyBhW,KAAKC,MAAMmW,EAAeL,EAAkB,KACrEE,EAAcG,GACdvN,EAAa+M,EAAkBzO,IAAM0O,EAAe1O,IACpDmC,EAAcsM,EAAkBlV,KAAO0V,EAAeP,EAAenV,KACjEpM,IACAgV,GAAeyM,EAAkB,EAAIK,GAEzClP,EAAKgM,OAAOpJ,8BAA8BjB,EAAYS,EAAa0M,KACpE,KAAKd,KAAK,UAAW,WACpBhO,EAAKqM,QAAQ,oBACTpc,KAAMA,EACNmM,gBAAiB0S,EAAyB,MAE9CvB,MACDS,KAAK,aAAc,WAClBe,EAAcH,GACdrB,MACDgB,WAAWP,KAAK,SAAU,WACzBviB,QAGRqf,2BAA4B,WAAA,GAEpBsE,GAGAC,EACAC,EALAtP,EAAOpN,KAEP2c,EAAgB1kB,IAChB2kB,EAAc3kB,IAGdsY,EAASjW,EAAQuiB,MAAQviB,EAAQwiB,QAAU,EAC3C3c,EAAS9G,EAAc8G,OACvB0I,EAAW7I,KAAK7H,QAAQ0Q,SACxB8R,EAAU,WACV6B,EAAelU,IAAI,UAAW,IAAIyU,YAAY5c,EAAOqX,SACrDgF,EAAe9N,SAASqO,YAAY5c,EAAOuX,QAC3C8E,EAAiB,KACjBQ,GAAc,GACdJ,EAAc3kB,IACd0kB,EAAgB1kB,IAChBmV,EAAKgM,OAAO5I,4BACZpD,EAAKwN,gBAAiB,GAEtBoC,EAAgB,SAAUlS,GACrB8R,EAAYf,SAAS1b,EAAOuX,UAC7BkF,EAAY1c,KAAK9E,EAAM+E,EAAOvC,SAAS0K,IAAI,UAAWwC,EAAQ,QAAU,IACxE6R,EAAcM,YAAY9c,EAAOqX,QAAS1M,IAG7CjC,IAAYA,EAASQ,oBAAqB,IAG3CkH,GAAU2M,SAASC,YACnBD,SAASC,WAAWC,IAAI,OAAQ,gCAAiC,gBAErEpd,KAAK4Y,qBAAuB,GAAI7f,OAAMQ,GAAGuhB,UAAU9a,KAAKoI,SACpD2S,SAAU,EACVC,OAAQ5f,EAAM+E,EAAOvC,QACrBqd,YAAY,IAEhBjb,KAAK4Y,qBAAqBwC,KAAK,YAAa,SAAUC,GAAV,GAOpCgC,GACAC,CAPJ,OAAIlQ,GAAKqM,QAAQ,2BACb4B,GAAEE,kBAGNiB,EAAiBnB,EAAEC,cAAchT,IAAI,UAAW,SAASmG,SAAStO,EAAOqX,SACzEgF,EAAe9N,SAASD,SAAStO,EAAOuX,QACpC2F,EAAgBb,EAAe9Q,SAC/B4R,EAAelQ,EAAKhF,QAAQlI,KAAK9E,EAAM+E,EAAOrD,cAAc4O,SAChE+Q,EAASvW,KAAKC,MAAMkX,EAAczW,KAAO0W,EAAa1W,KAAO1M,EAAYsiB,GAAkB,GAC3FE,EAASxW,KAAKC,MAAMkX,EAAchQ,IAAMiQ,EAAajQ,IAAMrT,EAAWwiB,GAAkB,GACxFpb,aAAagM,EAAK/L,sBAClB+L,EAAKwN,gBAAiB,MACvBQ,KAAK,OAAQriB,MAAM0iB,SAAS,SAAUJ,GAAV,GAKvBkC,GACAD,EACAE,EACAC,CAPCrQ,GAAKwN,iBAGVxN,EAAKgM,OAAO5I,4BACR+M,EAAStlB,EAAEc,MAAM2kB,mBAAmBrC,IACpCiC,EAAelQ,EAAKhF,QAAQlI,KAAK9E,EAAM+E,EAAOrD,cAAc4O,SAC5D8R,EAAWnC,EAAEhQ,EAAEmQ,SAAW8B,EAAa1W,KACvC6W,EAAWpC,EAAExK,EAAE2K,SAAW8B,EAAajQ,IAC3CD,EAAKgM,OAAO9I,2BACRjF,EAAGoR,EACH5L,EAAG6L,IAEHrR,EAAGmS,EACH3M,EAAG4M,GACJlN,GACHyM,GAAc,GACdL,EAAgBY,EAAO1B,SAAS1b,EAAOvC,SAAW2f,EAAStlB,IAC3D2kB,EAAcW,EAAO/K,QAAQpX,EAAM+E,EAAO1C,UAC1Cuf,GAAc,KACf,KAAK5B,KAAK,UAAW,WAAA,GAEZuC,GACAC,EACA1hB,EACAkQ,EACAE,CALJqQ,GAAchZ,SACVga,EAAYnB,EAAeX,SAAS1b,EAAOtC,cAC3C+f,EAAUjB,EAAcd,SAAS1b,EAAOtC,cACxC3B,EAAOyhB,EAAYC,EAAU,EAAI,EAAIA,EAAU,EAAI,EACnDxR,EAAcgB,EAAKqN,WAAW+B,EAAeJ,SAAShhB,EAAM+E,EAAO9C,MAAMmP,KAAK,aAC9EF,EAAYc,EAAKqN,WAAWkC,EAAcP,SAAShhB,EAAM+E,EAAO9C,MAAMmP,KAAK,aAC3EJ,IAAgBE,GAChBc,EAAKqM,QAAQ,qBACTvd,KAAMA,EACNkQ,YAAaA,EACbE,UAAWA,KAIvBqO,MACDS,KAAK,aAAc,WAClBT,MACDgB,WAAWP,KAAK,SAAU,WACzBviB,QAGRif,YAAa,WAAA,GACL1K,GAAOpN,KACPG,EAAS9G,EAAc8G,MACvBH,MAAK7H,QAAQkgB,YACbrY,KAAKoI,QAAQyV,GAAG/iB,EAAQD,EAAIO,EAAM+E,EAAO9C,KAAM,SAAUge,GACrDA,EAAEyC,kBACGzC,EAAE0C,QAGH3Q,EAAKqM,QAAQ,SAFbrM,EAAKqM,QAAQ,UAAY1P,IAAK9R,EAAE+H,MAAMwM,KAAK,gBAIhDqR,GAAG/iB,EAAQD,EAAIO,EAAM+E,EAAO1C,SAAU,SAAU4d,GAC/CA,EAAEyC,kBACF7lB,EAAE+H,MAAMsI,IAAI,UAAW,IACvB,IAAIiV,GAAStlB,EAAEilB,SAASc,iBAAiB3C,EAAE4C,QAAS5C,EAAE6C,SAClDX,GAAO1B,SAAS1b,EAAOpB,OACvBwe,EAAOY,QAEXlmB,EAAE+H,MAAMsI,IAAI,UAAW,MACxBuV,GAAG/iB,EAAQD,EAAIO,EAAM+E,EAAOrD,aAAc,WACrCsQ,EAAKgR,mBAAmBza,OAAS,EACjCyJ,EAAKiR,iBAELjR,EAAKqM,QAAQ,WAElBoE,GAAG/iB,EAAQD,EAAIO,EAAM+E,EAAOpB,KAAM,SAAUsc,GAC3CA,EAAEyC,kBACF1Q,EAAKgR,iBAAiBpe,SAIlCse,OAAQ,SAAUxT,GAAV,GACApR,GAAUsG,KAAKoI,QAAQlI,KAAK4K,GAC5B3K,EAAS9G,EAAc8G,MAC3B,OAAIzG,GAAQiK,QACR3D,KAAKqe,iBACL3kB,EAAQ+U,SAAStO,EAAOsX,eACpB1e,MAAMwB,QAAQ2gB,UACdxhB,EAAQgV,SAASD,SAAStO,EAAOoX,kBAIlCvX,KAAKoI,QAAQlI,KAAK9E,EAAM+E,EAAO9C,KAAOjC,EAAM+E,EAAOsX,WAE9D2G,iBAAkB,SAAUtT,GAAV,GAEVf,GADArQ,EAAUsG,KAAKoI,QAAQlI,KAAK4K,GAE5B3K,EAAS9G,EAAc8G,MAC3B,OAAIzG,GAAQiK,QACR3D,KAAKqe,iBACLre,KAAKyZ,QAAQ,SACb1P,EAAM9R,EAAEyB,GAAS8S,KAAK,gBACtBxM,MAAKoI,QAAQlI,KAAK9E,EAAM+E,EAAOpB,KAAO,cAAiBgL,EAAM,MAAO0E,SAAStO,EAAOsX,WAGjFzX,KAAKoI,QAAQlI,KAAK9E,EAAM+E,EAAOpB,KAAO3D,EAAM+E,EAAOsX,WAE9D4G,eAAgB,WACZ,GAAIle,GAAS9G,EAAc8G,MAC3BH,MAAKoI,QAAQlI,KAAK9E,EAAM+E,EAAOsX,UAAUsF,YAAY5c,EAAOsX,UACxD1e,MAAMwB,QAAQ2gB,UACdlb,KAAKoI,QAAQlI,KAAK9E,EAAM+E,EAAOoX,gBAAgBwF,YAAY5c,EAAOoX,iBAG1EY,cAAe,WAAA,GACP/K,GAAOpN,KACPG,EAAS9G,EAAc8G,OACvB0I,EAAW7I,KAAK7H,QAAQ0Q,QACxBA,KACA7I,KAAKue,YACLve,KAAKoI,QAAQyV,GAAG/iB,EAAQD,EAAIO,EAAM+E,EAAOhC,WAAY,SAAUkd,GAC3DjO,EAAKqM,QAAQ,cAAgB1P,IAAK9R,EAAE+H,MAAMwS,QAAQpX,EAAM+E,EAAO9C,MAAMmP,KAAK,cAC1E6O,EAAEyC,kBACFzC,EAAEE,mBACHsC,GAAG1iB,EAAUN,EAAI,SAAUwgB,GAAV,GACZmD,GACA3V,EAAWuE,EAAKjV,QAAQ0Q,QACxBwS,GAAEoD,UAAYhkB,EAAKikB,QAAU7V,GAAYA,EAAS8V,qBAAsB,IACxEH,EAAqBpR,EAAKgR,mBACtBI,EAAmB7a,SACnByJ,EAAKqM,QAAQ,oBAAsB1P,IAAKyU,EAAmBhS,KAAK,cAChEY,EAAKiR,qBAIZtlB,MAAMwB,QAAQ2gB,SASflb,KAAK6Y,MAAQ7Y,KAAKoI,QAAQwW,YACtB5D,OAAQ5f,EAAM+E,EAAO9C,KACrBwhB,UAAW,SAAUxD,GACbjO,EAAKjV,QAAQ0Q,SAASU,UAAW,GACjC6D,EAAKqM,QAAQ,YAAc1P,IAAK9R,EAAEojB,EAAExC,MAAMyC,eAAe9O,KAAK,iBAGvE7R,KAAK,cAfRqF,KAAKoI,QAAQyV,GAAG9iB,EAAWF,EAAIO,EAAM+E,EAAO9C,KAAM,SAAUge,GACpDjO,EAAKjV,QAAQ0Q,SAASU,UAAW,IACjC6D,EAAKqM,QAAQ,YAAc1P,IAAK9R,EAAE+H,MAAMwM,KAAK,cAC7C6O,EAAEyC,kBACFzC,EAAEE,sBAetBnD,SAAU,WAAA,GAIF0G,GAHA1R,EAAOpN,KACP+e,EAAiB/e,KAAK7H,QAAQ8X,QAC9B9P,EAAS9G,EAAc8G,OAEvB6e,EAAmB,SAAU3D,GAC7ByD,EAAuBzD,EAAE4C,QAEzBc,IAAkBA,EAAeE,WAAY,IAG5ClmB,MAAMwB,QAAQ2gB,UAiBflb,KAAKoI,QAAQyV,GAAG/iB,EAAQD,EAAIO,EAAM+E,EAAOhC,WAAY,SAAUkd,GAC3DA,EAAEyC,kBACF1Q,EAAKgM,OAAOjH,uBACb0L,GAAG3iB,EAAaL,EAAIO,EAAM+E,EAAO9C,KAAM,SAAUge,GAChD,GAAItJ,GAAU9Z,EAAEojB,EAAE6D,eAAenN,QAAQ3W,EAAM+E,EAAO1C,SAAUrC,EAAM+E,EAAO9C,KACtD,KAAnB0U,EAAQpO,QACRyJ,EAAKgM,OAAOjH,uBAGhBnS,KAAK6Y,OACL7Y,KAAK6Y,MAAMuC,KAAK,MAAO,SAAUC,GAAV,GACf3hB,GAAU2hB,EAAExC,MAAM0E,OAClBlgB,EAAO+P,EAAKqN,WAAWxiB,EAAEyB,GAAS8S,KAAK,aACvC2S,EAAkB9D,EAAExC,MAAMxN,EAAE+T,MAC5BhS,GAAKgM,OAAO1X,cACZ0L,EAAKgM,OAAOjH,qBAEhB/E,EAAKgM,OAAO1H,mBAAmBrU,EAAM3D,EAASylB,KAC/C/D,KAAK,YAAa,WACjBhO,EAAKgM,OAAOjH,wBAnCpBnS,KAAKoI,QAAQyV,GAAG5iB,EAAaJ,EAAIO,EAAM+E,EAAO9C,KAAM,WAAA,GAC5C3D,GAAUsG,KACV3C,EAAO+P,EAAKqN,WAAWxiB,EAAE+H,MAAMwM,KAAK,YACpCY,GAAKwN,iBAGTxN,EAAK/L,gBAAkBge,WAAW,WAC9BjS,EAAKgM,OAAO1H,mBAAmBrU,EAAM3D,EAASolB,IAC/C,KACH7mB,EAAE+H,MAAM6d,GAAG7iB,EAAWgkB,MACvBnB,GAAG3iB,EAAaL,EAAIO,EAAM+E,EAAO9C,KAAM,WACtC+D,aAAagM,EAAK/L,iBAClB+L,EAAKgM,OAAOjH,qBACZla,EAAE+H,MAAM8Y,IAAI9d,EAAWgkB,SA4BvC5kB,GAAO,EAAMf,GAAiB8G,OAAQ/G,KACxC4gB,OAAOjhB,MAAMumB,QACRtF,OAAOjhB,OACE,kBAAVf,SAAwBA,OAAOunB,IAAMvnB,OAAS,SAAUwnB,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.gantt.timeline.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.gantt.timeline', [\n        'kendo.dom',\n        'kendo.touch',\n        'kendo.draganddrop'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'gantt.timeline',\n        name: 'Gantt Timeline',\n        category: 'web',\n        description: 'The Gantt Timeline',\n        depends: [\n            'dom',\n            'touch',\n            'draganddrop'\n        ],\n        hidden: true\n    };\n    (function ($) {\n        var Widget = kendo.ui.Widget;\n        var kendoDomElement = kendo.dom.element;\n        var kendoTextElement = kendo.dom.text;\n        var kendoHtmlElement = kendo.dom.html;\n        var isPlainObject = $.isPlainObject;\n        var outerWidth = kendo._outerWidth;\n        var outerHeight = kendo._outerHeight;\n        var extend = $.extend;\n        var proxy = $.proxy;\n        var browser = kendo.support.browser;\n        var isRtl = false;\n        var keys = kendo.keys;\n        var Query = kendo.data.Query;\n        var STRING = 'string';\n        var NS = '.kendoGanttTimeline';\n        var CLICK = 'click';\n        var DBLCLICK = 'dblclick';\n        var MOUSEMOVE = 'mousemove';\n        var MOUSEENTER = 'mouseenter';\n        var MOUSELEAVE = 'mouseleave';\n        var KEYDOWN = 'keydown';\n        var DOT = '.';\n        var TIME_HEADER_TEMPLATE = kendo.template('#=kendo.toString(start, \\'t\\')#');\n        var DAY_HEADER_TEMPLATE = kendo.template('#=kendo.toString(start, \\'ddd M/dd\\')#');\n        var WEEK_HEADER_TEMPLATE = kendo.template('#=kendo.toString(start, \\'ddd M/dd\\')# - #=kendo.toString(kendo.date.addDays(end, -1), \\'ddd M/dd\\')#');\n        var MONTH_HEADER_TEMPLATE = kendo.template('#=kendo.toString(start, \\'MMM\\')#');\n        var YEAR_HEADER_TEMPLATE = kendo.template('#=kendo.toString(start, \\'yyyy\\')#');\n        var RESIZE_HINT = kendo.template('<div class=\"#=styles.marquee#\">' + '<div class=\"#=styles.marqueeColor#\"></div>' + '</div>');\n        var RESIZE_TOOLTIP_TEMPLATE = kendo.template('<div style=\"z-index: 100002;\" class=\"#=styles.tooltipWrapper#\">' + '<div class=\"#=styles.tooltipContent#\">' + '<div>#=messages.start#: #=kendo.toString(start, format)#</div>' + '<div>#=messages.end#: #=kendo.toString(end, format)#</div>' + '</div>' + '</div>');\n        var PERCENT_RESIZE_TOOLTIP_TEMPLATE = kendo.template('<div style=\"z-index: 100002;\" class=\"#=styles.tooltipWrapper#\" >' + '<div class=\"#=styles.tooltipContent#\">#=text#%</div>' + '<div class=\"#=styles.tooltipCallout#\" style=\"left:13px;\"></div>' + '</div>');\n        var TASK_TOOLTIP_TEMPLATE = kendo.template('<div class=\"#=kendo.htmlEncode(styles.taskDetails)#\">' + '<strong>#=kendo.htmlEncode(task.title)#</strong>' + '<div class=\"#=styles.taskDetailsPercent#\">#=kendo.toString(task.percentComplete, \"p0\")#</div>' + '<ul class=\"#=styles.reset#\">' + '<li>#=messages.start#: #=kendo.toString(task.start, \"h:mm tt ddd, MMM d\")#</li>' + '<li>#=messages.end#: #=kendo.toString(task.end, \"h:mm tt ddd, MMM d\")#</li>' + '</ul>' + '</div>');\n        var SIZE_CALCULATION_TEMPLATE = '<table style=\\'visibility: hidden;\\'>' + '<tbody>' + '<tr style=\\'height:{0}\\'>' + '<td>&nbsp;</td>' + '</tr>' + '</tbody>' + '</table>';\n        var defaultViews = {\n            day: { type: 'kendo.ui.GanttDayView' },\n            week: { type: 'kendo.ui.GanttWeekView' },\n            month: { type: 'kendo.ui.GanttMonthView' },\n            year: { type: 'kendo.ui.GanttYearView' }\n        };\n        function trimOptions(options) {\n            delete options.name;\n            delete options.prefix;\n            delete options.views;\n            return options;\n        }\n        function getWorkDays(options) {\n            var workDays = [];\n            var dayIndex = options.workWeekStart;\n            workDays.push(dayIndex);\n            while (options.workWeekEnd != dayIndex) {\n                if (dayIndex > 6) {\n                    dayIndex -= 7;\n                } else {\n                    dayIndex++;\n                }\n                workDays.push(dayIndex);\n            }\n            return workDays;\n        }\n        function blurActiveElement() {\n            var activeElement = kendo._activeElement();\n            if (activeElement && activeElement.nodeName.toLowerCase() !== 'body') {\n                $(activeElement).blur();\n            }\n        }\n        var viewStyles = {\n            alt: 'k-alt',\n            reset: 'k-reset',\n            nonWorking: 'k-nonwork-hour',\n            header: 'k-header',\n            gridHeader: 'k-grid-header',\n            gridHeaderWrap: 'k-grid-header-wrap',\n            gridContent: 'k-grid-content',\n            tasksWrapper: 'k-gantt-tables',\n            rowsTable: 'k-gantt-rows',\n            columnsTable: 'k-gantt-columns',\n            tasksTable: 'k-gantt-tasks',\n            dependenciesWrapper: 'k-gantt-dependencies',\n            resource: 'k-resource',\n            resourceAlt: 'k-resource k-alt',\n            task: 'k-task',\n            taskSingle: 'k-task-single',\n            taskMilestone: 'k-task-milestone',\n            taskSummary: 'k-task-summary',\n            taskWrap: 'k-task-wrap',\n            taskMilestoneWrap: 'k-milestone-wrap',\n            resourcesWrap: 'k-resources-wrap',\n            taskDot: 'k-task-dot',\n            taskDotStart: 'k-task-start',\n            taskDotEnd: 'k-task-end',\n            taskDragHandle: 'k-task-draghandle',\n            taskContent: 'k-task-content',\n            taskTemplate: 'k-task-template',\n            taskActions: 'k-task-actions',\n            taskDelete: 'k-task-delete',\n            taskComplete: 'k-task-complete',\n            taskDetails: 'k-task-details',\n            taskDetailsPercent: 'k-task-pct',\n            link: 'k-link',\n            icon: 'k-icon',\n            iconDelete: 'k-i-close',\n            taskResizeHandle: 'k-resize-handle',\n            taskResizeHandleWest: 'k-resize-w',\n            taskResizeHandleEast: 'k-resize-e',\n            taskSummaryProgress: 'k-task-summary-progress',\n            taskSummaryComplete: 'k-task-summary-complete',\n            line: 'k-line',\n            lineHorizontal: 'k-line-h',\n            lineVertical: 'k-line-v',\n            arrowWest: 'k-arrow-w',\n            arrowEast: 'k-arrow-e',\n            dragHint: 'k-drag-hint',\n            dependencyHint: 'k-dependency-hint',\n            tooltipWrapper: 'k-widget k-tooltip k-popup k-group k-reset',\n            tooltipContent: 'k-tooltip-content',\n            tooltipCallout: 'k-callout k-callout-s',\n            callout: 'k-callout',\n            marquee: 'k-marquee k-gantt-marquee',\n            marqueeColor: 'k-marquee-color'\n        };\n        var GanttView = kendo.ui.GanttView = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this.title = this.options.title || this.options.name;\n                this.header = this.element.find(DOT + GanttView.styles.gridHeader);\n                this.content = this.element.find(DOT + GanttView.styles.gridContent);\n                this.contentWidth = this.content.width();\n                this._workDays = getWorkDays(this.options);\n                this._headerTree = options.headerTree;\n                this._taskTree = options.taskTree;\n                this._taskTemplate = options.taskTemplate ? kendo.template(options.taskTemplate, extend({}, kendo.Template, options.templateSettings)) : null;\n                this._dependencyTree = options.dependencyTree;\n                this._taskCoordinates = {};\n                this._currentTime();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                clearTimeout(this._tooltipTimeout);\n                this.headerRow = null;\n                this.header = null;\n                this.content = null;\n                this._dragHint = null;\n                this._resizeHint = null;\n                this._resizeTooltip = null;\n                this._taskTooltip = null;\n                this._percentCompleteResizeTooltip = null;\n                this._headerTree = null;\n                this._taskTree = null;\n                this._dependencyTree = null;\n            },\n            options: {\n                showWorkHours: false,\n                showWorkDays: false,\n                workDayStart: new Date(1980, 1, 1, 8, 0, 0),\n                workDayEnd: new Date(1980, 1, 1, 17, 0, 0),\n                workWeekStart: 1,\n                workWeekEnd: 5,\n                hourSpan: 1,\n                slotSize: 100,\n                currentTimeMarker: { updateInterval: 10000 }\n            },\n            renderLayout: function () {\n                this._slots = this._createSlots();\n                this._tableWidth = this._calculateTableWidth();\n                this.createLayout(this._layout());\n                this._slotDimensions();\n                this._adjustHeight();\n                this.content.find(DOT + GanttView.styles.dependenciesWrapper).width(this._tableWidth);\n            },\n            _adjustHeight: function () {\n                if (this.content) {\n                    this.content.height(this.element.height() - outerHeight(this.header));\n                }\n            },\n            createLayout: function (rows) {\n                var headers = this._headers(rows);\n                var colgroup = this._colgroup();\n                var tree = this._headerTree;\n                var header = kendoDomElement('thead', null, headers);\n                var table = kendoDomElement('table', { style: { width: this._tableWidth + 'px' } }, [\n                    colgroup,\n                    header\n                ]);\n                tree.render([table]);\n                this.headerRow = this.header.find('table:first tr').last();\n            },\n            _slotDimensions: function () {\n                var headers = this.headerRow[0].children;\n                var slots = this._timeSlots();\n                var slot;\n                var header;\n                for (var i = 0, length = headers.length; i < length; i++) {\n                    header = headers[i];\n                    slot = slots[i];\n                    slot.offsetLeft = header.offsetLeft;\n                    slot.offsetWidth = header.offsetWidth;\n                }\n            },\n            render: function (tasks) {\n                var taskCount = tasks.length;\n                var styles = GanttView.styles;\n                var contentTable;\n                var rowsTable = this._rowsTable(taskCount);\n                var columnsTable = this._columnsTable(taskCount);\n                var tasksTable = this._tasksTable(tasks);\n                var currentTimeMarker = this.options.currentTimeMarker;\n                var calculatedSize = this.options.calculatedSize;\n                var totalHeight;\n                this._taskTree.render([\n                    rowsTable,\n                    columnsTable,\n                    tasksTable\n                ]);\n                contentTable = this.content.find(DOT + styles.rowsTable);\n                if (calculatedSize) {\n                    totalHeight = calculatedSize.row * tasks.length;\n                    this.content.find(DOT + styles.tasksTable).height(totalHeight);\n                    contentTable.height(totalHeight);\n                }\n                this._contentHeight = contentTable.height();\n                this._rowHeight = calculatedSize ? calculatedSize.row : this._contentHeight / contentTable.find('tr').length;\n                this.content.find(DOT + styles.columnsTable).height(this._contentHeight);\n                if (currentTimeMarker !== false && currentTimeMarker.updateInterval !== undefined) {\n                    this._renderCurrentTime();\n                }\n            },\n            _rowsTable: function (rowCount) {\n                var rows = [];\n                var row;\n                var styles = GanttView.styles;\n                var attributes = [\n                    null,\n                    { className: styles.alt }\n                ];\n                for (var i = 0; i < rowCount; i++) {\n                    row = kendoDomElement('tr', attributes[i % 2], [kendoDomElement('td', null, [kendoTextElement('\\xA0')])]);\n                    rows.push(row);\n                }\n                return this._createTable(1, rows, { className: styles.rowsTable });\n            },\n            _columnsTable: function () {\n                var cells = [];\n                var row;\n                var styles = GanttView.styles;\n                var slots = this._timeSlots();\n                var slotsCount = slots.length;\n                var slot;\n                var slotSpan;\n                var totalSpan = 0;\n                var attributes;\n                for (var i = 0; i < slotsCount; i++) {\n                    slot = slots[i];\n                    attributes = {};\n                    slotSpan = slot.span;\n                    totalSpan += slotSpan;\n                    if (slotSpan !== 1) {\n                        attributes.colspan = slotSpan;\n                    }\n                    if (slot.isNonWorking) {\n                        attributes.className = styles.nonWorking;\n                    }\n                    cells.push(kendoDomElement('td', attributes, [kendoTextElement('\\xA0')]));\n                }\n                row = kendoDomElement('tr', null, cells);\n                return this._createTable(totalSpan, [row], { className: styles.columnsTable });\n            },\n            _tasksTable: function (tasks) {\n                var rows = [];\n                var row;\n                var cell;\n                var position;\n                var task;\n                var styles = GanttView.styles;\n                var coordinates = this._taskCoordinates = {};\n                var size = this._calculateMilestoneWidth();\n                var milestoneWidth = Math.round(size.width);\n                var resourcesField = this.options.resourcesField;\n                var className = [\n                    styles.resource,\n                    styles.resourceAlt\n                ];\n                var calculatedSize = this.options.calculatedSize;\n                var resourcesPosition;\n                var resourcesMargin = this._calculateResourcesMargin();\n                var taskBorderWidth = this._calculateTaskBorderWidth();\n                var resourceStyle;\n                var addCoordinates = function (rowIndex) {\n                    var taskLeft;\n                    var taskRight;\n                    taskLeft = position.left;\n                    taskRight = taskLeft + position.width;\n                    if (task.isMilestone()) {\n                        taskLeft -= milestoneWidth / 2;\n                        taskRight = taskLeft + milestoneWidth;\n                    }\n                    coordinates[task.id] = {\n                        start: taskLeft,\n                        end: taskRight,\n                        rowIndex: rowIndex\n                    };\n                };\n                for (var i = 0, l = tasks.length; i < l; i++) {\n                    task = tasks[i];\n                    position = this._taskPosition(task);\n                    position.borderWidth = taskBorderWidth;\n                    row = kendoDomElement('tr', null);\n                    cell = kendoDomElement('td');\n                    if (task.start <= this.end && task.end >= this.start) {\n                        cell.children.push(this._renderTask(tasks[i], position));\n                        if (task[resourcesField] && task[resourcesField].length) {\n                            if (isRtl) {\n                                resourcesPosition = this._tableWidth - position.left;\n                            } else {\n                                resourcesPosition = Math.max(position.width || size.clientWidth, 0) + position.left;\n                            }\n                            resourceStyle = { width: this._tableWidth - (resourcesPosition + resourcesMargin) + 'px' };\n                            resourceStyle[isRtl ? 'right' : 'left'] = resourcesPosition + 'px';\n                            if (calculatedSize) {\n                                resourceStyle.height = calculatedSize.cell + 'px';\n                            }\n                            cell.children.push(kendoDomElement('div', {\n                                className: styles.resourcesWrap,\n                                style: resourceStyle\n                            }, this._renderResources(task[resourcesField], className[i % 2])));\n                        }\n                        addCoordinates(i);\n                    }\n                    row.children.push(cell);\n                    rows.push(row);\n                }\n                return this._createTable(1, rows, { className: GanttView.styles.tasksTable });\n            },\n            _createTable: function (colspan, rows, styles) {\n                var cols = [];\n                var colgroup;\n                var tbody;\n                for (var i = 0; i < colspan; i++) {\n                    cols.push(kendoDomElement('col'));\n                }\n                colgroup = kendoDomElement('colgroup', null, cols);\n                tbody = kendoDomElement('tbody', null, rows);\n                if (!styles.style) {\n                    styles.style = {};\n                }\n                styles.style.width = this._tableWidth + 'px';\n                return kendoDomElement('table', styles, [\n                    colgroup,\n                    tbody\n                ]);\n            },\n            _calculateTableWidth: function () {\n                var slots = this._timeSlots();\n                var maxSpan = 0;\n                var totalSpan = 0;\n                var currentSpan;\n                var tableWidth;\n                for (var i = 0, length = slots.length; i < length; i++) {\n                    currentSpan = slots[i].span;\n                    totalSpan += currentSpan;\n                    if (currentSpan > maxSpan) {\n                        maxSpan = currentSpan;\n                    }\n                }\n                tableWidth = Math.round(totalSpan * this.options.slotSize / maxSpan);\n                return tableWidth;\n            },\n            _calculateMilestoneWidth: function () {\n                var size;\n                var className = GanttView.styles.task + ' ' + GanttView.styles.taskMilestone;\n                var milestone = $('<div class=\\'' + className + '\\' style=\\'visibility: hidden; position: absolute\\'>');\n                var boundingClientRect;\n                this.content.append(milestone);\n                boundingClientRect = milestone[0].getBoundingClientRect();\n                size = {\n                    'width': boundingClientRect.right - boundingClientRect.left,\n                    'clientWidth': milestone[0].clientWidth\n                };\n                milestone.remove();\n                return size;\n            },\n            _calculateResourcesMargin: function () {\n                var margin;\n                var wrapper = $('<div class=\\'' + GanttView.styles.resourcesWrap + '\\' style=\\'visibility: hidden; position: absolute\\'>');\n                this.content.append(wrapper);\n                margin = parseInt(wrapper.css(isRtl ? 'margin-right' : 'margin-left'), 10);\n                wrapper.remove();\n                return margin;\n            },\n            _calculateTaskBorderWidth: function () {\n                var width;\n                var className = GanttView.styles.task + ' ' + GanttView.styles.taskSingle;\n                var task = $('<div class=\\'' + className + '\\' style=\\'visibility: hidden; position: absolute\\'>');\n                var computedStyle;\n                this.content.append(task);\n                computedStyle = kendo.getComputedStyles(task[0], ['border-left-width']);\n                width = parseFloat(computedStyle['border-left-width'], 10);\n                task.remove();\n                return width;\n            },\n            _renderTask: function (task, position) {\n                var taskWrapper;\n                var taskElement;\n                var editable = this.options.editable;\n                var progressHandleOffset;\n                var taskLeft = position.left;\n                var styles = GanttView.styles;\n                var wrapClassName = styles.taskWrap;\n                var calculatedSize = this.options.calculatedSize;\n                var dragHandleStyle = {};\n                var taskWrapAttr = {\n                    className: wrapClassName,\n                    style: { left: taskLeft + 'px' }\n                };\n                if (calculatedSize) {\n                    taskWrapAttr.style.height = calculatedSize.cell + 'px';\n                }\n                if (task.summary) {\n                    taskElement = this._renderSummary(task, position);\n                } else if (task.isMilestone()) {\n                    taskElement = this._renderMilestone(task, position);\n                    taskWrapAttr.className += ' ' + styles.taskMilestoneWrap;\n                } else {\n                    taskElement = this._renderSingleTask(task, position);\n                }\n                taskWrapper = kendoDomElement('div', taskWrapAttr, [taskElement]);\n                if (editable && editable.dependencyCreate !== false) {\n                    taskWrapper.children.push(kendoDomElement('div', { className: styles.taskDot + ' ' + styles.taskDotStart }));\n                    taskWrapper.children.push(kendoDomElement('div', { className: styles.taskDot + ' ' + styles.taskDotEnd }));\n                }\n                if (!task.summary && !task.isMilestone() && editable && editable.dragPercentComplete !== false && editable.update !== false && this._taskTemplate === null) {\n                    progressHandleOffset = Math.round(position.width * task.percentComplete);\n                    dragHandleStyle[isRtl ? 'right' : 'left'] = progressHandleOffset + 'px';\n                    taskWrapper.children.push(kendoDomElement('div', {\n                        className: styles.taskDragHandle,\n                        style: dragHandleStyle\n                    }));\n                }\n                return taskWrapper;\n            },\n            _renderSingleTask: function (task, position) {\n                var styles = GanttView.styles;\n                var progressWidth = Math.round(position.width * task.percentComplete);\n                var taskChildren = [];\n                var taskContent;\n                var editable = this.options.editable;\n                if (this._taskTemplate !== null) {\n                    taskContent = kendoHtmlElement(this._taskTemplate(task));\n                } else {\n                    taskContent = kendoTextElement(task.title);\n                    taskChildren.push(kendoDomElement('div', {\n                        className: styles.taskComplete,\n                        style: { width: progressWidth + 'px' }\n                    }));\n                }\n                var content = kendoDomElement('div', { className: styles.taskContent }, [kendoDomElement('div', { className: styles.taskTemplate }, [taskContent])]);\n                taskChildren.push(content);\n                if (editable) {\n                    if (editable.destroy !== false) {\n                        content.children.push(kendoDomElement('span', { className: styles.taskActions }, [kendoDomElement('a', {\n                                className: styles.link + ' ' + styles.taskDelete,\n                                href: '#',\n                                'aria-label': 'Delete'\n                            }, [kendoDomElement('span', { className: styles.icon + ' ' + styles.iconDelete })])]));\n                    }\n                    if (editable.resize !== false && editable.update !== false) {\n                        content.children.push(kendoDomElement('span', { className: styles.taskResizeHandle + ' ' + styles.taskResizeHandleWest }));\n                        content.children.push(kendoDomElement('span', { className: styles.taskResizeHandle + ' ' + styles.taskResizeHandleEast }));\n                    }\n                }\n                var element = kendoDomElement('div', {\n                    className: styles.task + ' ' + styles.taskSingle,\n                    'data-uid': task.uid,\n                    style: { width: Math.max(position.width - position.borderWidth * 2, 0) + 'px' }\n                }, taskChildren);\n                return element;\n            },\n            _renderMilestone: function (task) {\n                var styles = GanttView.styles;\n                var element = kendoDomElement('div', {\n                    className: styles.task + ' ' + styles.taskMilestone,\n                    'data-uid': task.uid\n                });\n                return element;\n            },\n            _renderSummary: function (task, position) {\n                var styles = GanttView.styles;\n                var progressWidth = Math.round(position.width * task.percentComplete);\n                var element = kendoDomElement('div', {\n                    className: styles.task + ' ' + styles.taskSummary,\n                    'data-uid': task.uid,\n                    style: { width: position.width + 'px' }\n                }, [kendoDomElement('div', {\n                        className: styles.taskSummaryProgress,\n                        style: { width: progressWidth + 'px' }\n                    }, [kendoDomElement('div', {\n                            className: styles.taskSummaryComplete,\n                            style: { width: position.width + 'px' }\n                        })])]);\n                return element;\n            },\n            _renderResources: function (resources, className) {\n                var children = [];\n                var resource;\n                for (var i = 0, length = resources.length; i < length; i++) {\n                    resource = resources[i];\n                    children.push(kendoDomElement('span', {\n                        className: className,\n                        style: { 'color': resource.get('color') }\n                    }, [kendoTextElement(resource.get('name'))]));\n                }\n                if (isRtl) {\n                    children.reverse();\n                }\n                return children;\n            },\n            _taskPosition: function (task) {\n                var round = Math.round;\n                var startLeft = round(this._offset(isRtl ? task.end : task.start));\n                var endLeft = round(this._offset(isRtl ? task.start : task.end));\n                return {\n                    left: startLeft,\n                    width: endLeft - startLeft\n                };\n            },\n            _offset: function (date) {\n                var slots = this._timeSlots();\n                var slot;\n                var startOffset;\n                var slotDuration;\n                var slotOffset = 0;\n                var startIndex;\n                if (!slots.length) {\n                    return 0;\n                }\n                startIndex = this._slotIndex('start', date);\n                slot = slots[startIndex];\n                if (slot.end < date) {\n                    slotOffset = slot.offsetWidth;\n                } else if (slot.start <= date) {\n                    startOffset = date - slot.start;\n                    slotDuration = slot.end - slot.start;\n                    slotOffset = startOffset / slotDuration * slot.offsetWidth;\n                }\n                if (isRtl) {\n                    slotOffset = slot.offsetWidth + 1 - slotOffset;\n                }\n                return slot.offsetLeft + slotOffset;\n            },\n            _slotIndex: function (field, value, reverse) {\n                var slots = this._timeSlots();\n                var startIdx = 0;\n                var endIdx = slots.length - 1;\n                var middle;\n                if (reverse) {\n                    slots = [].slice.call(slots).reverse();\n                }\n                do {\n                    middle = Math.ceil((endIdx + startIdx) / 2);\n                    if (slots[middle][field] < value) {\n                        startIdx = middle;\n                    } else {\n                        if (middle === endIdx) {\n                            middle--;\n                        }\n                        endIdx = middle;\n                    }\n                } while (startIdx !== endIdx);\n                if (reverse) {\n                    startIdx = slots.length - 1 - startIdx;\n                }\n                return startIdx;\n            },\n            _timeByPosition: function (x, snap, snapToEnd) {\n                var slot = this._slotByPosition(x);\n                if (snap) {\n                    return snapToEnd ? slot.end : slot.start;\n                }\n                var offsetLeft = x - $(DOT + GanttView.styles.tasksTable).offset().left;\n                var duration = slot.end - slot.start;\n                var slotOffset = offsetLeft - slot.offsetLeft;\n                if (isRtl) {\n                    slotOffset = slot.offsetWidth - slotOffset;\n                }\n                return new Date(slot.start.getTime() + duration * (slotOffset / slot.offsetWidth));\n            },\n            _slotByPosition: function (x) {\n                var offsetLeft = x - $(DOT + GanttView.styles.tasksTable).offset().left;\n                var slotIndex = this._slotIndex('offsetLeft', offsetLeft, isRtl);\n                return this._timeSlots()[slotIndex];\n            },\n            _renderDependencies: function (dependencies) {\n                var elements = [];\n                var tree = this._dependencyTree;\n                for (var i = 0, l = dependencies.length; i < l; i++) {\n                    elements.push.apply(elements, this._renderDependency(dependencies[i]));\n                }\n                tree.render(elements);\n            },\n            _renderDependency: function (dependency) {\n                var predecessor = this._taskCoordinates[dependency.predecessorId];\n                var successor = this._taskCoordinates[dependency.successorId];\n                var elements;\n                var method;\n                if (!predecessor || !successor) {\n                    return [];\n                }\n                method = '_render' + [\n                    'FF',\n                    'FS',\n                    'SF',\n                    'SS'\n                ][isRtl ? 3 - dependency.type : dependency.type];\n                elements = this[method](predecessor, successor);\n                for (var i = 0, length = elements.length; i < length; i++) {\n                    elements[i].attr['data-uid'] = dependency.uid;\n                }\n                return elements;\n            },\n            _renderFF: function (from, to) {\n                var lines = this._dependencyFF(from, to, false);\n                lines[lines.length - 1].children[0] = this._arrow(true);\n                return lines;\n            },\n            _renderSS: function (from, to) {\n                var lines = this._dependencyFF(to, from, true);\n                lines[0].children[0] = this._arrow(false);\n                return lines.reverse();\n            },\n            _renderFS: function (from, to) {\n                var lines = this._dependencyFS(from, to, false);\n                lines[lines.length - 1].children[0] = this._arrow(false);\n                return lines;\n            },\n            _renderSF: function (from, to) {\n                var lines = this._dependencyFS(to, from, true);\n                lines[0].children[0] = this._arrow(true);\n                return lines.reverse();\n            },\n            _dependencyFF: function (from, to, reverse) {\n                var that = this;\n                var lines = [];\n                var left = 0;\n                var top = 0;\n                var width = 0;\n                var height = 0;\n                var dir = reverse ? 'start' : 'end';\n                var delta;\n                var overlap = 2;\n                var arrowOverlap = 1;\n                var rowHeight = this._rowHeight;\n                var minLineWidth = 10;\n                var fromTop = from.rowIndex * rowHeight + Math.floor(rowHeight / 2) - 1;\n                var toTop = to.rowIndex * rowHeight + Math.floor(rowHeight / 2) - 1;\n                var styles = GanttView.styles;\n                var addHorizontal = function () {\n                    lines.push(that._line(styles.line + ' ' + styles.lineHorizontal, {\n                        left: left + 'px',\n                        top: top + 'px',\n                        width: width + 'px'\n                    }));\n                };\n                var addVertical = function () {\n                    lines.push(that._line(styles.line + ' ' + styles.lineVertical, {\n                        left: left + 'px',\n                        top: top + 'px',\n                        height: height + 'px'\n                    }));\n                };\n                left = from[dir];\n                top = fromTop;\n                width = minLineWidth;\n                delta = to[dir] - from[dir];\n                if (delta > 0 !== reverse) {\n                    width = Math.abs(delta) + minLineWidth;\n                }\n                if (reverse) {\n                    left -= width;\n                    width -= arrowOverlap;\n                    addHorizontal();\n                } else {\n                    addHorizontal();\n                    left += width - overlap;\n                }\n                if (toTop < top) {\n                    height = top - toTop;\n                    height += overlap;\n                    top = toTop;\n                    addVertical();\n                } else {\n                    height = toTop - top;\n                    height += overlap;\n                    addVertical();\n                    top += height - overlap;\n                }\n                width = Math.abs(left - to[dir]);\n                if (!reverse) {\n                    width -= arrowOverlap;\n                    left -= width;\n                }\n                addHorizontal();\n                return lines;\n            },\n            _dependencyFS: function (from, to, reverse) {\n                var that = this;\n                var lines = [];\n                var left = 0;\n                var top = 0;\n                var width = 0;\n                var height = 0;\n                var rowHeight = this._rowHeight;\n                var minLineHeight = Math.floor(rowHeight / 2);\n                var minLineWidth = 10;\n                var minDistance = 2 * minLineWidth;\n                var delta = to.start - from.end;\n                var overlap = 2;\n                var arrowOverlap = 1;\n                var fromTop = from.rowIndex * rowHeight + Math.floor(rowHeight / 2) - 1;\n                var toTop = to.rowIndex * rowHeight + Math.floor(rowHeight / 2) - 1;\n                var styles = GanttView.styles;\n                var addHorizontal = function () {\n                    lines.push(that._line(styles.line + ' ' + styles.lineHorizontal, {\n                        left: left + 'px',\n                        top: top + 'px',\n                        width: width + 'px'\n                    }));\n                };\n                var addVertical = function () {\n                    lines.push(that._line(styles.line + ' ' + styles.lineVertical, {\n                        left: left + 'px',\n                        top: top + 'px',\n                        height: height + 'px'\n                    }));\n                };\n                left = from.end;\n                top = fromTop;\n                width = minLineWidth;\n                if (reverse) {\n                    left += arrowOverlap;\n                    if (delta > minDistance) {\n                        width = delta - (minLineWidth - overlap);\n                    }\n                    width -= arrowOverlap;\n                }\n                addHorizontal();\n                left += width - overlap;\n                if (delta <= minDistance) {\n                    height = reverse ? Math.abs(toTop - fromTop) - minLineHeight : minLineHeight;\n                    if (toTop < fromTop) {\n                        top -= height;\n                        height += overlap;\n                        addVertical();\n                    } else {\n                        addVertical();\n                        top += height;\n                    }\n                    width = from.end - to.start + minDistance;\n                    if (width < minLineWidth) {\n                        width = minLineWidth;\n                    }\n                    left -= width - overlap;\n                    addHorizontal();\n                }\n                if (toTop < fromTop) {\n                    height = top - toTop;\n                    top = toTop;\n                    height += overlap;\n                    addVertical();\n                } else {\n                    height = toTop - top;\n                    addVertical();\n                    top += height;\n                }\n                width = to.start - left;\n                if (!reverse) {\n                    width -= arrowOverlap;\n                }\n                addHorizontal();\n                return lines;\n            },\n            _line: function (className, styles) {\n                return kendoDomElement('div', {\n                    className: className,\n                    style: styles\n                });\n            },\n            _arrow: function (direction) {\n                return kendoDomElement('span', { className: direction ? GanttView.styles.arrowWest : GanttView.styles.arrowEast });\n            },\n            _colgroup: function () {\n                var slots = this._timeSlots();\n                var count = slots.length;\n                var cols = [];\n                for (var i = 0; i < count; i++) {\n                    for (var j = 0, length = slots[i].span; j < length; j++) {\n                        cols.push(kendoDomElement('col'));\n                    }\n                }\n                return kendoDomElement('colgroup', null, cols);\n            },\n            _createDragHint: function (element) {\n                this._dragHint = element.clone().addClass(GanttView.styles.dragHint).css('cursor', 'move');\n                element.parent().append(this._dragHint);\n            },\n            _updateDragHint: function (start) {\n                var left = this._offset(start);\n                this._dragHint.css({ 'left': left });\n            },\n            _removeDragHint: function () {\n                this._dragHint.remove();\n                this._dragHint = null;\n            },\n            _createResizeHint: function (task) {\n                var styles = GanttView.styles;\n                var taskTop = this._taskCoordinates[task.id].rowIndex * this._rowHeight;\n                var tooltipHeight;\n                var tooltipTop;\n                var options = this.options;\n                var messages = options.messages;\n                this._resizeHint = $(RESIZE_HINT({ styles: styles })).css({\n                    'top': 0,\n                    'height': this._contentHeight\n                });\n                this.content.append(this._resizeHint);\n                this._resizeTooltip = $(RESIZE_TOOLTIP_TEMPLATE({\n                    styles: styles,\n                    start: task.start,\n                    end: task.end,\n                    messages: messages.views,\n                    format: options.resizeTooltipFormat\n                })).css({\n                    'top': 0,\n                    'left': 0\n                });\n                this.content.append(this._resizeTooltip);\n                this._resizeTooltipWidth = outerWidth(this._resizeTooltip);\n                tooltipHeight = outerHeight(this._resizeTooltip);\n                tooltipTop = taskTop - tooltipHeight;\n                if (tooltipTop < 0) {\n                    tooltipTop = taskTop + this._rowHeight;\n                }\n                this._resizeTooltipTop = tooltipTop;\n            },\n            _updateResizeHint: function (start, end, resizeStart) {\n                var left = this._offset(isRtl ? end : start);\n                var right = this._offset(isRtl ? start : end);\n                var width = right - left;\n                var tooltipLeft = resizeStart !== isRtl ? left : right;\n                var tablesWidth = this._tableWidth - kendo.support.scrollbar();\n                var tooltipWidth = this._resizeTooltipWidth;\n                var options = this.options;\n                var messages = options.messages;\n                var tableOffset = $(DOT + GanttView.styles.tasksTable).offset().left - $(DOT + GanttView.styles.tasksWrapper).offset().left;\n                if (isRtl) {\n                    left += tableOffset;\n                }\n                this._resizeHint.css({\n                    'left': left,\n                    'width': width\n                });\n                if (this._resizeTooltip) {\n                    this._resizeTooltip.remove();\n                }\n                tooltipLeft -= Math.round(tooltipWidth / 2);\n                if (tooltipLeft < 0) {\n                    tooltipLeft = 0;\n                } else if (tooltipLeft + tooltipWidth > tablesWidth) {\n                    tooltipLeft = tablesWidth - tooltipWidth;\n                }\n                if (isRtl) {\n                    tooltipLeft += tableOffset;\n                }\n                this._resizeTooltip = $(RESIZE_TOOLTIP_TEMPLATE({\n                    styles: GanttView.styles,\n                    start: start,\n                    end: end,\n                    messages: messages.views,\n                    format: options.resizeTooltipFormat\n                })).css({\n                    'top': this._resizeTooltipTop,\n                    'left': tooltipLeft,\n                    'min-width': tooltipWidth\n                }).appendTo(this.content);\n            },\n            _removeResizeHint: function () {\n                this._resizeHint.remove();\n                this._resizeHint = null;\n                this._resizeTooltip.remove();\n                this._resizeTooltip = null;\n            },\n            _updatePercentCompleteTooltip: function (top, left, text) {\n                this._removePercentCompleteTooltip();\n                var tooltip = this._percentCompleteResizeTooltip = $(PERCENT_RESIZE_TOOLTIP_TEMPLATE({\n                    styles: GanttView.styles,\n                    text: text\n                })).appendTo(this.element);\n                var tooltipMiddle = Math.round(outerWidth(tooltip) / 2);\n                var arrow = tooltip.find(DOT + GanttView.styles.callout);\n                var arrowHeight = Math.round(outerWidth(arrow) / 2);\n                tooltip.css({\n                    'top': top - (outerHeight(tooltip) + arrowHeight),\n                    'left': left - tooltipMiddle\n                });\n                arrow.css('left', tooltipMiddle - arrowHeight);\n            },\n            _removePercentCompleteTooltip: function () {\n                if (this._percentCompleteResizeTooltip) {\n                    this._percentCompleteResizeTooltip.remove();\n                }\n                this._percentCompleteResizeTooltip = null;\n            },\n            _updateDependencyDragHint: function (from, to, useVML) {\n                this._removeDependencyDragHint();\n                if (useVML) {\n                    this._creteVmlDependencyDragHint(from, to);\n                } else {\n                    this._creteDependencyDragHint(from, to);\n                }\n            },\n            _creteDependencyDragHint: function (from, to) {\n                var styles = GanttView.styles;\n                var deltaX = to.x - from.x;\n                var deltaY = to.y - from.y;\n                var width = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n                var angle = Math.atan(deltaY / deltaX);\n                if (deltaX < 0) {\n                    angle += Math.PI;\n                }\n                $('<div class=\\'' + styles.line + ' ' + styles.lineHorizontal + ' ' + styles.dependencyHint + '\\'></div>').css({\n                    'top': from.y,\n                    'left': from.x,\n                    'width': width,\n                    'transform-origin': '0% 0',\n                    '-ms-transform-origin': '0% 0',\n                    '-webkit-transform-origin': '0% 0',\n                    'transform': 'rotate(' + angle + 'rad)',\n                    '-ms-transform': 'rotate(' + angle + 'rad)',\n                    '-webkit-transform': 'rotate(' + angle + 'rad)'\n                }).appendTo(this.content);\n            },\n            _creteVmlDependencyDragHint: function (from, to) {\n                var hint = $('<kvml:line class=\\'' + GanttView.styles.dependencyHint + '\\' style=\\'position:absolute; top: 0px; left: 0px;\\' strokecolor=\\'black\\' strokeweight=\\'2px\\' from=\\'' + from.x + 'px,' + from.y + 'px\\' to=\\'' + to.x + 'px,' + to.y + 'px\\'' + '></kvml:line>').appendTo(this.content);\n                hint[0].outerHTML = hint[0].outerHTML;\n            },\n            _removeDependencyDragHint: function () {\n                this.content.find(DOT + GanttView.styles.dependencyHint).remove();\n            },\n            _createTaskTooltip: function (task, element, mouseLeft) {\n                var styles = GanttView.styles;\n                var options = this.options;\n                var content = this.content;\n                var contentOffset = content.offset();\n                var contentWidth = content.width();\n                var contentScrollLeft = kendo.scrollLeft(content);\n                var row = $(element).parents('tr').first();\n                var rowOffset = row.offset();\n                var template = options.tooltip && options.tooltip.template ? kendo.template(options.tooltip.template) : TASK_TOOLTIP_TEMPLATE;\n                var left = isRtl ? mouseLeft - (contentOffset.left + contentScrollLeft + kendo.support.scrollbar()) : mouseLeft - (contentOffset.left - contentScrollLeft);\n                var top = rowOffset.top + outerHeight(row) - contentOffset.top + content.scrollTop();\n                var tooltip = this._taskTooltip = $('<div style=\"z-index: 100002;\" class=\"' + styles.tooltipWrapper + '\" >' + '<div class=\"' + styles.taskContent + '\"></div></div>');\n                var tooltipWidth;\n                tooltip.css({\n                    'left': left,\n                    'top': top\n                }).appendTo(content).find(DOT + styles.taskContent).append(template({\n                    styles: styles,\n                    task: task,\n                    messages: options.messages.views\n                }));\n                if (outerHeight(tooltip) < rowOffset.top - contentOffset.top) {\n                    tooltip.css('top', rowOffset.top - contentOffset.top - outerHeight(tooltip) + content.scrollTop());\n                }\n                tooltipWidth = outerWidth(tooltip);\n                if (tooltipWidth + left - contentScrollLeft > contentWidth) {\n                    left -= tooltipWidth;\n                    if (left < contentScrollLeft) {\n                        left = contentScrollLeft + contentWidth - (tooltipWidth + 17);\n                    }\n                    tooltip.css('left', left);\n                }\n            },\n            _removeTaskTooltip: function () {\n                if (this._taskTooltip) {\n                    this._taskTooltip.remove();\n                }\n                this._taskTooltip = null;\n            },\n            _scrollTo: function (element) {\n                var elementLeft = element.offset().left;\n                var elementWidth = element.width();\n                var elementRight = elementLeft + elementWidth;\n                var row = element.closest('tr');\n                var rowTop = row.offset().top;\n                var rowHeight = row.height();\n                var rowBottom = rowTop + rowHeight;\n                var content = this.content;\n                var contentOffset = content.offset();\n                var contentTop = contentOffset.top;\n                var contentHeight = content.height();\n                var contentBottom = contentTop + contentHeight;\n                var contentLeft = contentOffset.left;\n                var contentWidth = content.width();\n                var contentRight = contentLeft + contentWidth;\n                var scrollbarWidth = kendo.support.scrollbar();\n                if (rowTop < contentTop) {\n                    content.scrollTop(content.scrollTop() + (rowTop - contentTop));\n                } else if (rowBottom > contentBottom) {\n                    content.scrollTop(content.scrollTop() + (rowBottom + scrollbarWidth - contentBottom));\n                }\n                if (elementLeft < contentLeft && elementWidth > contentWidth && elementRight < contentRight || elementRight > contentRight && elementWidth < contentWidth) {\n                    content.scrollLeft(content.scrollLeft() + (elementRight + scrollbarWidth - contentRight));\n                } else if (elementRight > contentRight && elementWidth > contentWidth && elementLeft > contentLeft || elementLeft < contentLeft && elementWidth < contentWidth) {\n                    content.scrollLeft(content.scrollLeft() + (elementLeft - contentLeft));\n                }\n            },\n            _scrollToDate: function (date) {\n                var viewStart = this.start;\n                var viewEnd = this.end;\n                var offset;\n                if (date >= viewStart && date < viewEnd) {\n                    offset = this._offset(date);\n                    if (kendo.support.isRtl(this.element)) {\n                        offset = this._tableWidth - offset;\n                    }\n                    kendo.scrollLeft(this.content, offset);\n                }\n            },\n            _timeSlots: function () {\n                if (!this._slots || !this._slots.length) {\n                    return [];\n                }\n                return this._slots[this._slots.length - 1];\n            },\n            _headers: function (columnLevels) {\n                var rows = [];\n                var level;\n                var headers;\n                var column;\n                var headerText;\n                var styles = GanttView.styles;\n                for (var levelIndex = 0, levelCount = columnLevels.length; levelIndex < levelCount; levelIndex++) {\n                    level = columnLevels[levelIndex];\n                    headers = [];\n                    for (var columnIndex = 0, columnCount = level.length; columnIndex < columnCount; columnIndex++) {\n                        column = level[columnIndex];\n                        headerText = kendoHtmlElement(column.text);\n                        headers.push(kendoDomElement('th', {\n                            colspan: column.span,\n                            className: styles.header + (column.isNonWorking ? ' ' + styles.nonWorking : '')\n                        }, [headerText]));\n                    }\n                    rows.push(kendoDomElement('tr', null, headers));\n                }\n                return rows;\n            },\n            _hours: function (start, end) {\n                var slotEnd;\n                var slots = [];\n                var options = this.options;\n                var workDayStart = options.workDayStart.getHours();\n                var workDayEnd = options.workDayEnd.getHours();\n                var isWorkHour;\n                var hours;\n                var hourSpan = options.hourSpan;\n                start = new Date(start);\n                end = new Date(end);\n                if (options.showWorkHours) {\n                    start.setHours(workDayStart);\n                }\n                while (start < end) {\n                    slotEnd = new Date(start);\n                    hours = slotEnd.getHours();\n                    isWorkHour = hours >= workDayStart && hours < workDayEnd;\n                    slotEnd.setHours(slotEnd.getHours() + hourSpan);\n                    if (hours == slotEnd.getHours()) {\n                        slotEnd.setHours(slotEnd.getHours() + 2 * hourSpan);\n                    }\n                    if (!options.showWorkHours || isWorkHour) {\n                        slots.push({\n                            start: start,\n                            end: slotEnd,\n                            isNonWorking: !isWorkHour,\n                            span: 1\n                        });\n                    }\n                    start = slotEnd;\n                }\n                return slots;\n            },\n            _days: function (start, end) {\n                var slotEnd;\n                var slots = [];\n                var isWorkDay;\n                start = new Date(start);\n                end = new Date(end);\n                while (start < end) {\n                    slotEnd = end < kendo.date.nextDay(start) ? end : kendo.date.nextDay(start);\n                    isWorkDay = this._isWorkDay(start);\n                    if (!this.options.showWorkDays || isWorkDay) {\n                        slots.push({\n                            start: start,\n                            end: slotEnd,\n                            isNonWorking: !isWorkDay,\n                            span: 1\n                        });\n                    }\n                    start = slotEnd;\n                }\n                return slots;\n            },\n            _weeks: function (start, end) {\n                var slotEnd;\n                var slots = [];\n                var firstDay = this.calendarInfo().firstDay;\n                var daySlots;\n                var span;\n                start = new Date(start);\n                end = new Date(end);\n                while (start < end) {\n                    slotEnd = kendo.date.dayOfWeek(kendo.date.addDays(start, 1), firstDay, 1);\n                    if (slotEnd > end) {\n                        slotEnd = end;\n                    }\n                    daySlots = this._days(start, slotEnd);\n                    span = daySlots.length;\n                    if (span > 0) {\n                        slots.push({\n                            start: daySlots[0].start,\n                            end: daySlots[span - 1].end,\n                            span: span\n                        });\n                    }\n                    start = slotEnd;\n                }\n                return slots;\n            },\n            _months: function (start, end) {\n                var slotEnd;\n                var endMonth;\n                var slots = [];\n                var daySlots;\n                var span;\n                start = new Date(start);\n                end = new Date(end);\n                while (start < end) {\n                    slotEnd = new Date(start);\n                    endMonth = kendo.date.firstDayOfMonth(new Date(slotEnd.setMonth(slotEnd.getMonth() + 1)));\n                    slotEnd = end < endMonth ? end : endMonth;\n                    daySlots = this._days(start, slotEnd);\n                    span = daySlots.length;\n                    if (span > 0) {\n                        slots.push({\n                            start: daySlots[0].start,\n                            end: daySlots[span - 1].end,\n                            span: span\n                        });\n                    }\n                    start = slotEnd;\n                }\n                return slots;\n            },\n            _years: function (start, end) {\n                var slotEnd;\n                var monthSpan;\n                var endMonth;\n                var slots = [];\n                start = new Date(start);\n                end = new Date(end);\n                while (start < end) {\n                    slotEnd = new Date(start);\n                    slotEnd = kendo.date.firstDayOfMonth(new Date(slotEnd.setMonth(12)));\n                    if (slotEnd >= end) {\n                        slotEnd = end;\n                    }\n                    endMonth = slotEnd.getMonth() || 12;\n                    monthSpan = endMonth - start.getMonth();\n                    slots.push({\n                        start: start,\n                        end: slotEnd,\n                        span: monthSpan\n                    });\n                    start = slotEnd;\n                }\n                return slots;\n            },\n            _slotHeaders: function (slots, template) {\n                var columns = [];\n                var slot;\n                for (var i = 0, l = slots.length; i < l; i++) {\n                    slot = slots[i];\n                    columns.push({\n                        text: template(slot),\n                        isNonWorking: !!slot.isNonWorking,\n                        span: slot.span\n                    });\n                }\n                return columns;\n            },\n            _isWorkDay: function (date) {\n                var day = date.getDay();\n                var workDays = this._workDays;\n                for (var i = 0, l = workDays.length; i < l; i++) {\n                    if (workDays[i] === day) {\n                        return true;\n                    }\n                }\n                return false;\n            },\n            calendarInfo: function () {\n                return kendo.getCulture().calendars.standard;\n            },\n            _renderCurrentTime: function () {\n                var currentTime = this._getCurrentTime();\n                var timeOffset = this._offset(currentTime);\n                var element = $('<div class=\\'k-current-time\\'></div>');\n                var viewStyles = GanttView.styles;\n                var tablesWrap = $(DOT + viewStyles.tasksWrapper);\n                var tasksTable = $(DOT + viewStyles.tasksTable);\n                var slot;\n                if (!this.content || !this._timeSlots().length) {\n                    return;\n                }\n                this.content.find('.k-current-time').remove();\n                slot = this._timeSlots()[this._slotIndex('start', currentTime)];\n                if (currentTime < slot.start || currentTime > slot.end) {\n                    return;\n                }\n                if (tablesWrap.length && tasksTable.length) {\n                    timeOffset += tasksTable.offset().left - tablesWrap.offset().left;\n                }\n                element.css({\n                    left: timeOffset + 'px',\n                    top: '0px',\n                    width: '1px',\n                    height: this._contentHeight + 'px'\n                }).appendTo(this.content);\n            },\n            _getCurrentTime: function () {\n                return new Date();\n            },\n            _currentTime: function () {\n                var markerOptions = this.options.currentTimeMarker;\n                if (markerOptions !== false && markerOptions.updateInterval !== undefined) {\n                    this._renderCurrentTime();\n                    this._currentTimeUpdateTimer = setInterval(proxy(this._renderCurrentTime, this), markerOptions.updateInterval);\n                }\n            }\n        });\n        extend(true, GanttView, { styles: viewStyles });\n        kendo.ui.GanttDayView = GanttView.extend({\n            name: 'day',\n            options: {\n                timeHeaderTemplate: TIME_HEADER_TEMPLATE,\n                dayHeaderTemplate: DAY_HEADER_TEMPLATE,\n                resizeTooltipFormat: 'h:mm tt ddd, MMM d'\n            },\n            range: function (range) {\n                var optionsRange = this.options.range;\n                this.start = kendo.date.getDate(range.start);\n                this.end = kendo.date.getDate(range.end);\n                if (kendo.date.getMilliseconds(range.end) > 0 || this.end.getTime() === this.start.getTime()) {\n                    this.end = kendo.date.addDays(this.end, 1);\n                }\n                if (optionsRange && optionsRange.start) {\n                    this.start = kendo.date.getDate(optionsRange.start);\n                    this.start.setHours(optionsRange.start.getHours());\n                }\n                if (optionsRange && optionsRange.end) {\n                    this.end = kendo.date.getDate(optionsRange.end);\n                    this.end.setHours(optionsRange.end.getHours());\n                }\n            },\n            _createSlots: function () {\n                var daySlots;\n                var daySlot;\n                var hourSlots;\n                var hours;\n                var slots = [];\n                daySlots = this._days(this.start, this.end);\n                hourSlots = [];\n                for (var i = 0, l = daySlots.length; i < l; i++) {\n                    daySlot = daySlots[i];\n                    hours = this._hours(daySlot.start, daySlot.end);\n                    daySlot.span = hours.length;\n                    hourSlots.push.apply(hourSlots, hours);\n                }\n                slots.push(daySlots);\n                slots.push(hourSlots);\n                return slots;\n            },\n            _layout: function () {\n                var rows = [];\n                var options = this.options;\n                rows.push(this._slotHeaders(this._slots[0], kendo.template(options.dayHeaderTemplate)));\n                rows.push(this._slotHeaders(this._slots[1], kendo.template(options.timeHeaderTemplate)));\n                return rows;\n            }\n        });\n        kendo.ui.GanttWeekView = GanttView.extend({\n            name: 'week',\n            options: {\n                dayHeaderTemplate: DAY_HEADER_TEMPLATE,\n                weekHeaderTemplate: WEEK_HEADER_TEMPLATE,\n                resizeTooltipFormat: 'h:mm tt ddd, MMM d'\n            },\n            range: function (range) {\n                var optionsRange = this.options.range;\n                var calendarInfo = this.calendarInfo();\n                var firstDay = calendarInfo.firstDay;\n                var rangeEnd = range.end;\n                var endDay;\n                if (firstDay === rangeEnd.getDay()) {\n                    rangeEnd.setDate(rangeEnd.getDate() + 7);\n                }\n                this.start = kendo.date.getDate(kendo.date.dayOfWeek(range.start, firstDay, -1));\n                this.end = kendo.date.getDate(kendo.date.dayOfWeek(rangeEnd, firstDay, 1));\n                if (optionsRange && optionsRange.start) {\n                    this.start = kendo.date.getDate(optionsRange.start);\n                }\n                if (optionsRange && optionsRange.end) {\n                    endDay = new Date(optionsRange.end);\n                    if (kendo.date.getDate(endDay) < optionsRange.end) {\n                        this.end = kendo.date.getDate(new Date(endDay.setDate(endDay.getDate() + 1)));\n                    } else {\n                        this.end = kendo.date.getDate(endDay);\n                    }\n                }\n            },\n            _createSlots: function () {\n                var slots = [];\n                slots.push(this._weeks(this.start, this.end));\n                slots.push(this._days(this.start, this.end));\n                return slots;\n            },\n            _layout: function () {\n                var rows = [];\n                var options = this.options;\n                rows.push(this._slotHeaders(this._slots[0], kendo.template(options.weekHeaderTemplate)));\n                rows.push(this._slotHeaders(this._slots[1], kendo.template(options.dayHeaderTemplate)));\n                return rows;\n            }\n        });\n        kendo.ui.GanttMonthView = GanttView.extend({\n            name: 'month',\n            options: {\n                weekHeaderTemplate: WEEK_HEADER_TEMPLATE,\n                monthHeaderTemplate: MONTH_HEADER_TEMPLATE,\n                resizeTooltipFormat: 'dddd, MMM d, yyyy'\n            },\n            range: function (range) {\n                var optionsRange = this.options.range;\n                var endDay;\n                this.start = kendo.date.firstDayOfMonth(range.start);\n                this.end = kendo.date.addDays(kendo.date.getDate(kendo.date.lastDayOfMonth(range.end)), 1);\n                if (optionsRange && optionsRange.start) {\n                    this.start = kendo.date.getDate(optionsRange.start);\n                }\n                if (optionsRange && optionsRange.end) {\n                    endDay = new Date(optionsRange.end);\n                    if (kendo.date.getDate(endDay) < optionsRange.end) {\n                        this.end = kendo.date.getDate(new Date(endDay.setDate(endDay.getDate() + 1)));\n                    } else {\n                        this.end = kendo.date.getDate(endDay);\n                    }\n                }\n            },\n            _createSlots: function () {\n                var slots = [];\n                slots.push(this._months(this.start, this.end));\n                slots.push(this._weeks(this.start, this.end));\n                return slots;\n            },\n            _layout: function () {\n                var rows = [];\n                var options = this.options;\n                rows.push(this._slotHeaders(this._slots[0], kendo.template(options.monthHeaderTemplate)));\n                rows.push(this._slotHeaders(this._slots[1], kendo.template(options.weekHeaderTemplate)));\n                return rows;\n            }\n        });\n        kendo.ui.GanttYearView = GanttView.extend({\n            name: 'year',\n            options: {\n                yearHeaderTemplate: YEAR_HEADER_TEMPLATE,\n                monthHeaderTemplate: MONTH_HEADER_TEMPLATE,\n                resizeTooltipFormat: 'dddd, MMM d, yyyy'\n            },\n            range: function (range) {\n                var optionsRange = this.options.range;\n                var firstDayOfMonth;\n                this.start = kendo.date.firstDayOfMonth(new Date(range.start.setMonth(0)));\n                this.end = kendo.date.firstDayOfMonth(new Date(range.end.setMonth(12)));\n                if (optionsRange && optionsRange.start) {\n                    this.start = kendo.date.firstDayOfMonth(optionsRange.start);\n                }\n                if (optionsRange && optionsRange.end) {\n                    firstDayOfMonth = kendo.date.firstDayOfMonth(optionsRange.end);\n                    this.end = kendo.date.getDate(new Date(firstDayOfMonth.setMonth(firstDayOfMonth.getMonth() + 1)));\n                }\n            },\n            _createSlots: function () {\n                var slots = [];\n                var monthSlots = this._months(this.start, this.end);\n                $(monthSlots).each(function (index, slot) {\n                    slot.span = 1;\n                });\n                slots.push(this._years(this.start, this.end));\n                slots.push(monthSlots);\n                return slots;\n            },\n            _layout: function () {\n                var rows = [];\n                var options = this.options;\n                rows.push(this._slotHeaders(this._slots[0], kendo.template(options.yearHeaderTemplate)));\n                rows.push(this._slotHeaders(this._slots[1], kendo.template(options.monthHeaderTemplate)));\n                return rows;\n            }\n        });\n        var timelineStyles = {\n            wrapper: 'k-timeline k-grid k-widget',\n            gridHeader: 'k-grid-header',\n            gridHeaderWrap: 'k-grid-header-wrap',\n            gridContent: 'k-grid-content',\n            gridContentWrap: 'k-grid-content',\n            tasksWrapper: 'k-gantt-tables',\n            dependenciesWrapper: 'k-gantt-dependencies',\n            task: 'k-task',\n            line: 'k-line',\n            taskResizeHandle: 'k-resize-handle',\n            taskResizeHandleWest: 'k-resize-w',\n            taskDragHandle: 'k-task-draghandle',\n            taskComplete: 'k-task-complete',\n            taskDelete: 'k-task-delete',\n            taskWrapActive: 'k-task-wrap-active',\n            taskWrap: 'k-task-wrap',\n            taskDot: 'k-task-dot',\n            taskDotStart: 'k-task-start',\n            taskDotEnd: 'k-task-end',\n            hovered: 'k-state-hover',\n            selected: 'k-state-selected',\n            origin: 'k-origin'\n        };\n        var GanttTimeline = kendo.ui.GanttTimeline = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                if (!this.options.views || !this.options.views.length) {\n                    this.options.views = [\n                        'day',\n                        'week',\n                        'month'\n                    ];\n                }\n                isRtl = kendo.support.isRtl(element);\n                this._wrapper();\n                this._domTrees();\n                this._views();\n                this._selectable();\n                this._draggable();\n                this._resizable();\n                this._percentResizeDraggable();\n                this._createDependencyDraggable();\n                this._attachEvents();\n                this._tooltip();\n            },\n            options: {\n                name: 'GanttTimeline',\n                messages: {\n                    views: {\n                        day: 'Day',\n                        week: 'Week',\n                        month: 'Month',\n                        year: 'Year',\n                        start: 'Start',\n                        end: 'End'\n                    }\n                },\n                snap: true,\n                selectable: true,\n                editable: true\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                clearTimeout(this._tooltipTimeout);\n                if (this._currentTimeUpdateTimer) {\n                    clearInterval(this._currentTimeUpdateTimer);\n                }\n                this._unbindView(this._selectedView);\n                if (this._moveDraggable) {\n                    this._moveDraggable.destroy();\n                }\n                if (this._resizeDraggable) {\n                    this._resizeDraggable.destroy();\n                }\n                if (this._percentDraggable) {\n                    this._percentDraggable.destroy();\n                }\n                if (this._dependencyDraggable) {\n                    this._dependencyDraggable.destroy();\n                }\n                if (this.touch) {\n                    this.touch.destroy();\n                }\n                this._headerTree = null;\n                this._taskTree = null;\n                this._dependencyTree = null;\n                this.wrapper.off(NS);\n                kendo.destroy(this.wrapper);\n            },\n            _wrapper: function () {\n                var styles = GanttTimeline.styles;\n                var that = this;\n                var options = this.options;\n                var calculateSize = function () {\n                    var rowHeight = typeof options.rowHeight === STRING ? options.rowHeight : options.rowHeight + 'px';\n                    var table = $(kendo.format(SIZE_CALCULATION_TEMPLATE, rowHeight));\n                    var calculatedRowHeight;\n                    var calculatedCellHeight;\n                    var content = that.wrapper.find(DOT + styles.tasksWrapper);\n                    content.append(table);\n                    calculatedRowHeight = outerHeight(table.find('tr'));\n                    calculatedCellHeight = table.find('td').height();\n                    table.remove();\n                    return {\n                        'row': calculatedRowHeight,\n                        'cell': calculatedCellHeight\n                    };\n                };\n                this.wrapper = this.element.addClass(styles.wrapper).append('<div class=\\'' + styles.gridHeader + '\\'><div class=\\'' + styles.gridHeaderWrap + '\\'></div></div>').append('<div class=\\'' + styles.gridContentWrap + '\\'><div class=\\'' + styles.tasksWrapper + '\\'></div><div class=\\'' + styles.dependenciesWrapper + '\\'></div></div>');\n                if (options.rowHeight) {\n                    this._calculatedSize = calculateSize();\n                }\n            },\n            _domTrees: function () {\n                var styles = GanttTimeline.styles;\n                var tree = kendo.dom.Tree;\n                var wrapper = this.wrapper;\n                this._headerTree = new tree(wrapper.find(DOT + styles.gridHeaderWrap)[0]);\n                this._taskTree = new tree(wrapper.find(DOT + styles.tasksWrapper)[0]);\n                this._dependencyTree = new tree(wrapper.find(DOT + styles.dependenciesWrapper)[0]);\n            },\n            _views: function () {\n                var views = this.options.views;\n                var view;\n                var isSettings;\n                var name;\n                var defaultView;\n                var selected;\n                this.views = {};\n                for (var i = 0, l = views.length; i < l; i++) {\n                    view = views[i];\n                    isSettings = isPlainObject(view);\n                    if (isSettings && view.selectable === false) {\n                        continue;\n                    }\n                    name = isSettings ? typeof view.type !== 'string' ? view.title : view.type : view;\n                    defaultView = defaultViews[name];\n                    if (defaultView) {\n                        if (isSettings) {\n                            view.type = defaultView.type;\n                        }\n                        defaultView.title = this.options.messages.views[name];\n                    }\n                    view = extend({ title: name }, defaultView, isSettings ? view : {});\n                    if (name) {\n                        this.views[name] = view;\n                        if (!selected || view.selected) {\n                            selected = name;\n                        }\n                    }\n                }\n                if (selected) {\n                    this._selectedViewName = selected;\n                }\n            },\n            view: function (name) {\n                if (name) {\n                    this._selectView(name);\n                    this.trigger('navigate', {\n                        view: name,\n                        action: 'changeView'\n                    });\n                }\n                return this._selectedView;\n            },\n            _selectView: function (name) {\n                if (name && this.views[name]) {\n                    if (this._selectedView) {\n                        this._unbindView(this._selectedView);\n                    }\n                    this._selectedView = this._initializeView(name);\n                    this._selectedViewName = name;\n                }\n            },\n            _viewByIndex: function (index) {\n                var view;\n                var views = this.views;\n                for (view in views) {\n                    if (!index) {\n                        return view;\n                    }\n                    index--;\n                }\n            },\n            _initializeView: function (name) {\n                var view = this.views[name];\n                if (view) {\n                    var type = view.type;\n                    if (typeof type === 'string') {\n                        type = kendo.getter(view.type)(window);\n                    }\n                    if (type) {\n                        var newRange = {};\n                        extend(newRange, this.options.range, view.range);\n                        var newDate = view.date || this.options.date;\n                        view = new type(this.wrapper, trimOptions(extend(true, {\n                            headerTree: this._headerTree,\n                            taskTree: this._taskTree,\n                            dependencyTree: this._dependencyTree,\n                            calculatedSize: this._calculatedSize\n                        }, view, this.options, {\n                            date: newDate,\n                            range: newRange\n                        })));\n                    } else {\n                        throw new Error('There is no such view');\n                    }\n                }\n                return view;\n            },\n            _unbindView: function (view) {\n                if (view) {\n                    view.destroy();\n                }\n            },\n            _range: function (tasks) {\n                var startOrder = {\n                    field: 'start',\n                    dir: 'asc'\n                };\n                var endOrder = {\n                    field: 'end',\n                    dir: 'desc'\n                };\n                if (!tasks || !tasks.length) {\n                    return {\n                        start: new Date(),\n                        end: new Date()\n                    };\n                }\n                var start = new Query(tasks).sort(startOrder).toArray()[0].start || new Date();\n                var end = new Query(tasks).sort(endOrder).toArray()[0].end || new Date();\n                return {\n                    start: new Date(start),\n                    end: new Date(end)\n                };\n            },\n            _render: function (tasks) {\n                var view = this.view();\n                var range = this._range(tasks);\n                var date = view.options.date;\n                this._tasks = tasks;\n                view.range(range);\n                view.renderLayout();\n                view.render(tasks);\n                if (date) {\n                    view._scrollToDate(date);\n                }\n            },\n            _renderDependencies: function (dependencies) {\n                this.view()._renderDependencies(dependencies);\n            },\n            _taskByUid: function (uid) {\n                var tasks = this._tasks;\n                var length = tasks.length;\n                var task;\n                for (var i = 0; i < length; i++) {\n                    task = tasks[i];\n                    if (task.uid === uid) {\n                        return task;\n                    }\n                }\n            },\n            _draggable: function () {\n                var that = this;\n                var element;\n                var task;\n                var currentStart;\n                var startOffset;\n                var snap = this.options.snap;\n                var styles = GanttTimeline.styles;\n                var editable = this.options.editable;\n                var cleanUp = function () {\n                    that.view()._removeDragHint();\n                    if (element) {\n                        element.css('opacity', 1);\n                    }\n                    element = null;\n                    task = null;\n                    that.dragInProgress = false;\n                };\n                if (!editable || editable.move === false || editable.update === false) {\n                    return;\n                }\n                this._moveDraggable = new kendo.ui.Draggable(this.wrapper, {\n                    distance: 0,\n                    filter: DOT + styles.task,\n                    holdToDrag: kendo.support.mobileOS,\n                    ignore: DOT + styles.taskResizeHandle\n                });\n                this._moveDraggable.bind('dragstart', function (e) {\n                    var view = that.view();\n                    element = e.currentTarget.parent();\n                    task = that._taskByUid(e.currentTarget.attr('data-uid'));\n                    if (that.trigger('moveStart', { task: task })) {\n                        e.preventDefault();\n                        return;\n                    }\n                    currentStart = task.start;\n                    startOffset = view._timeByPosition(e.x.location, snap) - currentStart;\n                    view._createDragHint(element);\n                    element.css('opacity', 0.5);\n                    clearTimeout(that._tooltipTimeout);\n                    that.dragInProgress = true;\n                }).bind('drag', kendo.throttle(function (e) {\n                    if (!that.dragInProgress) {\n                        return;\n                    }\n                    var view = that.view();\n                    var date = new Date(view._timeByPosition(e.x.location, snap) - startOffset);\n                    var updateHintDate = date;\n                    if (!that.trigger('move', {\n                            task: task,\n                            start: date\n                        })) {\n                        currentStart = date;\n                        if (isRtl) {\n                            updateHintDate = new Date(currentStart.getTime() + task.duration());\n                        }\n                        view._updateDragHint(updateHintDate);\n                    }\n                }, 15)).bind('dragend', function () {\n                    that.trigger('moveEnd', {\n                        task: task,\n                        start: currentStart\n                    });\n                    cleanUp();\n                }).bind('dragcancel', function () {\n                    cleanUp();\n                }).userEvents.bind('select', function () {\n                    blurActiveElement();\n                });\n            },\n            _resizable: function () {\n                var that = this;\n                var element;\n                var task;\n                var currentStart;\n                var currentEnd;\n                var resizeStart;\n                var snap = this.options.snap;\n                var styles = GanttTimeline.styles;\n                var editable = this.options.editable;\n                var cleanUp = function () {\n                    that.view()._removeResizeHint();\n                    element = null;\n                    task = null;\n                    that.dragInProgress = false;\n                };\n                if (!editable || editable.resize === false || editable.update === false) {\n                    return;\n                }\n                this._resizeDraggable = new kendo.ui.Draggable(this.wrapper, {\n                    distance: 0,\n                    filter: DOT + styles.taskResizeHandle,\n                    holdToDrag: false\n                });\n                this._resizeDraggable.bind('dragstart', function (e) {\n                    resizeStart = e.currentTarget.hasClass(styles.taskResizeHandleWest);\n                    if (isRtl) {\n                        resizeStart = !resizeStart;\n                    }\n                    element = e.currentTarget.closest(DOT + styles.task);\n                    task = that._taskByUid(element.attr('data-uid'));\n                    if (that.trigger('resizeStart', { task: task })) {\n                        e.preventDefault();\n                        return;\n                    }\n                    currentStart = task.start;\n                    currentEnd = task.end;\n                    that.view()._createResizeHint(task);\n                    clearTimeout(that._tooltipTimeout);\n                    that.dragInProgress = true;\n                }).bind('drag', kendo.throttle(function (e) {\n                    if (!that.dragInProgress) {\n                        return;\n                    }\n                    var view = that.view();\n                    var date = view._timeByPosition(e.x.location, snap, !resizeStart);\n                    if (resizeStart) {\n                        if (date < currentEnd) {\n                            currentStart = date;\n                        } else {\n                            currentStart = currentEnd;\n                        }\n                    } else {\n                        if (date > currentStart) {\n                            currentEnd = date;\n                        } else {\n                            currentEnd = currentStart;\n                        }\n                    }\n                    if (!that.trigger('resize', {\n                            task: task,\n                            start: currentStart,\n                            end: currentEnd\n                        })) {\n                        view._updateResizeHint(currentStart, currentEnd, resizeStart);\n                    }\n                }, 15)).bind('dragend', function () {\n                    that.trigger('resizeEnd', {\n                        task: task,\n                        resizeStart: resizeStart,\n                        start: currentStart,\n                        end: currentEnd\n                    });\n                    cleanUp();\n                }).bind('dragcancel', function () {\n                    cleanUp();\n                }).userEvents.bind('select', function () {\n                    blurActiveElement();\n                });\n            },\n            _percentResizeDraggable: function () {\n                var that = this;\n                var task;\n                var taskElement;\n                var taskElementOffset;\n                var timelineOffset;\n                var originalPercentWidth;\n                var maxPercentWidth;\n                var currentPercentComplete;\n                var tooltipTop;\n                var tooltipLeft;\n                var styles = GanttTimeline.styles;\n                var delta;\n                var editable = this.options.editable;\n                var cleanUp = function () {\n                    that.view()._removePercentCompleteTooltip();\n                    taskElement = null;\n                    task = null;\n                    that.dragInProgress = false;\n                };\n                var updateElement = function (width) {\n                    taskElement.find(DOT + styles.taskComplete).width(width).end().siblings(DOT + styles.taskDragHandle).css(isRtl ? 'right' : 'left', width);\n                };\n                if (!editable || editable.dragPercentComplete === false || editable.update === false) {\n                    return;\n                }\n                this._percentDraggable = new kendo.ui.Draggable(this.wrapper, {\n                    distance: 0,\n                    filter: DOT + styles.taskDragHandle,\n                    holdToDrag: false\n                });\n                this._percentDraggable.bind('dragstart', function (e) {\n                    if (that.trigger('percentResizeStart')) {\n                        e.preventDefault();\n                        return;\n                    }\n                    taskElement = e.currentTarget.siblings(DOT + styles.task);\n                    task = that._taskByUid(taskElement.attr('data-uid'));\n                    currentPercentComplete = task.percentComplete;\n                    taskElementOffset = taskElement.offset();\n                    timelineOffset = this.element.offset();\n                    originalPercentWidth = taskElement.find(DOT + styles.taskComplete).width();\n                    maxPercentWidth = outerWidth(taskElement);\n                    clearTimeout(that._tooltipTimeout);\n                    that.dragInProgress = true;\n                }).bind('drag', kendo.throttle(function (e) {\n                    if (!that.dragInProgress) {\n                        return;\n                    }\n                    delta = isRtl ? -e.x.initialDelta : e.x.initialDelta;\n                    var currentWidth = Math.max(0, Math.min(maxPercentWidth, originalPercentWidth + delta));\n                    currentPercentComplete = Math.round(currentWidth / maxPercentWidth * 100);\n                    updateElement(currentWidth);\n                    tooltipTop = taskElementOffset.top - timelineOffset.top;\n                    tooltipLeft = taskElementOffset.left + currentWidth - timelineOffset.left;\n                    if (isRtl) {\n                        tooltipLeft += maxPercentWidth - 2 * currentWidth;\n                    }\n                    that.view()._updatePercentCompleteTooltip(tooltipTop, tooltipLeft, currentPercentComplete);\n                }, 15)).bind('dragend', function () {\n                    that.trigger('percentResizeEnd', {\n                        task: task,\n                        percentComplete: currentPercentComplete / 100\n                    });\n                    cleanUp();\n                }).bind('dragcancel', function () {\n                    updateElement(originalPercentWidth);\n                    cleanUp();\n                }).userEvents.bind('select', function () {\n                    blurActiveElement();\n                });\n            },\n            _createDependencyDraggable: function () {\n                var that = this;\n                var originalHandle;\n                var hoveredHandle = $();\n                var hoveredTask = $();\n                var startX;\n                var startY;\n                var useVML = browser.msie && browser.version < 9;\n                var styles = GanttTimeline.styles;\n                var editable = this.options.editable;\n                var cleanUp = function () {\n                    originalHandle.css('display', '').removeClass(styles.hovered);\n                    originalHandle.parent().removeClass(styles.origin);\n                    originalHandle = null;\n                    toggleHandles(false);\n                    hoveredTask = $();\n                    hoveredHandle = $();\n                    that.view()._removeDependencyDragHint();\n                    that.dragInProgress = false;\n                };\n                var toggleHandles = function (value) {\n                    if (!hoveredTask.hasClass(styles.origin)) {\n                        hoveredTask.find(DOT + styles.taskDot).css('display', value ? 'block' : '');\n                        hoveredHandle.toggleClass(styles.hovered, value);\n                    }\n                };\n                if (!editable || editable.dependencyCreate === false) {\n                    return;\n                }\n                if (useVML && document.namespaces) {\n                    document.namespaces.add('kvml', 'urn:schemas-microsoft-com:vml', '#default#VML');\n                }\n                this._dependencyDraggable = new kendo.ui.Draggable(this.wrapper, {\n                    distance: 0,\n                    filter: DOT + styles.taskDot,\n                    holdToDrag: false\n                });\n                this._dependencyDraggable.bind('dragstart', function (e) {\n                    if (that.trigger('dependencyDragStart')) {\n                        e.preventDefault();\n                        return;\n                    }\n                    originalHandle = e.currentTarget.css('display', 'block').addClass(styles.hovered);\n                    originalHandle.parent().addClass(styles.origin);\n                    var elementOffset = originalHandle.offset();\n                    var tablesOffset = that.wrapper.find(DOT + styles.tasksWrapper).offset();\n                    startX = Math.round(elementOffset.left - tablesOffset.left + outerHeight(originalHandle) / 2);\n                    startY = Math.round(elementOffset.top - tablesOffset.top + outerWidth(originalHandle) / 2);\n                    clearTimeout(that._tooltipTimeout);\n                    that.dragInProgress = true;\n                }).bind('drag', kendo.throttle(function (e) {\n                    if (!that.dragInProgress) {\n                        return;\n                    }\n                    that.view()._removeDependencyDragHint();\n                    var target = $(kendo.elementUnderCursor(e));\n                    var tablesOffset = that.wrapper.find(DOT + styles.tasksWrapper).offset();\n                    var currentX = e.x.location - tablesOffset.left;\n                    var currentY = e.y.location - tablesOffset.top;\n                    that.view()._updateDependencyDragHint({\n                        x: startX,\n                        y: startY\n                    }, {\n                        x: currentX,\n                        y: currentY\n                    }, useVML);\n                    toggleHandles(false);\n                    hoveredHandle = target.hasClass(styles.taskDot) ? target : $();\n                    hoveredTask = target.closest(DOT + styles.taskWrap);\n                    toggleHandles(true);\n                }, 15)).bind('dragend', function () {\n                    if (hoveredHandle.length) {\n                        var fromStart = originalHandle.hasClass(styles.taskDotStart);\n                        var toStart = hoveredHandle.hasClass(styles.taskDotStart);\n                        var type = fromStart ? toStart ? 3 : 2 : toStart ? 1 : 0;\n                        var predecessor = that._taskByUid(originalHandle.siblings(DOT + styles.task).attr('data-uid'));\n                        var successor = that._taskByUid(hoveredHandle.siblings(DOT + styles.task).attr('data-uid'));\n                        if (predecessor !== successor) {\n                            that.trigger('dependencyDragEnd', {\n                                type: type,\n                                predecessor: predecessor,\n                                successor: successor\n                            });\n                        }\n                    }\n                    cleanUp();\n                }).bind('dragcancel', function () {\n                    cleanUp();\n                }).userEvents.bind('select', function () {\n                    blurActiveElement();\n                });\n            },\n            _selectable: function () {\n                var that = this;\n                var styles = GanttTimeline.styles;\n                if (this.options.selectable) {\n                    this.wrapper.on(CLICK + NS, DOT + styles.task, function (e) {\n                        e.stopPropagation();\n                        if (!e.ctrlKey) {\n                            that.trigger('select', { uid: $(this).attr('data-uid') });\n                        } else {\n                            that.trigger('clear');\n                        }\n                    }).on(CLICK + NS, DOT + styles.taskWrap, function (e) {\n                        e.stopPropagation();\n                        $(this).css('z-index', '0');\n                        var target = $(document.elementFromPoint(e.clientX, e.clientY));\n                        if (target.hasClass(styles.line)) {\n                            target.click();\n                        }\n                        $(this).css('z-index', '');\n                    }).on(CLICK + NS, DOT + styles.tasksWrapper, function () {\n                        if (that.selectDependency().length > 0) {\n                            that.clearSelection();\n                        } else {\n                            that.trigger('clear');\n                        }\n                    }).on(CLICK + NS, DOT + styles.line, function (e) {\n                        e.stopPropagation();\n                        that.selectDependency(this);\n                    });\n                }\n            },\n            select: function (value) {\n                var element = this.wrapper.find(value);\n                var styles = GanttTimeline.styles;\n                if (element.length) {\n                    this.clearSelection();\n                    element.addClass(styles.selected);\n                    if (kendo.support.mobileOS) {\n                        element.parent().addClass(styles.taskWrapActive);\n                    }\n                    return;\n                }\n                return this.wrapper.find(DOT + styles.task + DOT + styles.selected);\n            },\n            selectDependency: function (value) {\n                var element = this.wrapper.find(value);\n                var uid;\n                var styles = GanttTimeline.styles;\n                if (element.length) {\n                    this.clearSelection();\n                    this.trigger('clear');\n                    uid = $(element).attr('data-uid');\n                    this.wrapper.find(DOT + styles.line + '[data-uid=\\'' + uid + '\\']').addClass(styles.selected);\n                    return;\n                }\n                return this.wrapper.find(DOT + styles.line + DOT + styles.selected);\n            },\n            clearSelection: function () {\n                var styles = GanttTimeline.styles;\n                this.wrapper.find(DOT + styles.selected).removeClass(styles.selected);\n                if (kendo.support.mobileOS) {\n                    this.wrapper.find(DOT + styles.taskWrapActive).removeClass(styles.taskWrapActive);\n                }\n            },\n            _attachEvents: function () {\n                var that = this;\n                var styles = GanttTimeline.styles;\n                var editable = this.options.editable;\n                if (editable) {\n                    this._tabindex();\n                    this.wrapper.on(CLICK + NS, DOT + styles.taskDelete, function (e) {\n                        that.trigger('removeTask', { uid: $(this).closest(DOT + styles.task).attr('data-uid') });\n                        e.stopPropagation();\n                        e.preventDefault();\n                    }).on(KEYDOWN + NS, function (e) {\n                        var selectedDependency;\n                        var editable = that.options.editable;\n                        if (e.keyCode === keys.DELETE && editable && editable.dependencyDestroy !== false) {\n                            selectedDependency = that.selectDependency();\n                            if (selectedDependency.length) {\n                                that.trigger('removeDependency', { uid: selectedDependency.attr('data-uid') });\n                                that.clearSelection();\n                            }\n                        }\n                    });\n                    if (!kendo.support.mobileOS) {\n                        this.wrapper.on(DBLCLICK + NS, DOT + styles.task, function (e) {\n                            if (that.options.editable.update !== false) {\n                                that.trigger('editTask', { uid: $(this).attr('data-uid') });\n                                e.stopPropagation();\n                                e.preventDefault();\n                            }\n                        });\n                    } else {\n                        this.touch = this.wrapper.kendoTouch({\n                            filter: DOT + styles.task,\n                            doubletap: function (e) {\n                                if (that.options.editable.update !== false) {\n                                    that.trigger('editTask', { uid: $(e.touch.currentTarget).attr('data-uid') });\n                                }\n                            }\n                        }).data('kendoTouch');\n                    }\n                }\n            },\n            _tooltip: function () {\n                var that = this;\n                var tooltipOptions = this.options.tooltip;\n                var styles = GanttTimeline.styles;\n                var currentMousePosition;\n                var mouseMoveHandler = function (e) {\n                    currentMousePosition = e.clientX;\n                };\n                if (tooltipOptions && tooltipOptions.visible === false) {\n                    return;\n                }\n                if (!kendo.support.mobileOS) {\n                    this.wrapper.on(MOUSEENTER + NS, DOT + styles.task, function () {\n                        var element = this;\n                        var task = that._taskByUid($(this).attr('data-uid'));\n                        if (that.dragInProgress) {\n                            return;\n                        }\n                        that._tooltipTimeout = setTimeout(function () {\n                            that.view()._createTaskTooltip(task, element, currentMousePosition);\n                        }, 800);\n                        $(this).on(MOUSEMOVE, mouseMoveHandler);\n                    }).on(MOUSELEAVE + NS, DOT + styles.task, function () {\n                        clearTimeout(that._tooltipTimeout);\n                        that.view()._removeTaskTooltip();\n                        $(this).off(MOUSEMOVE, mouseMoveHandler);\n                    });\n                } else {\n                    this.wrapper.on(CLICK + NS, DOT + styles.taskDelete, function (e) {\n                        e.stopPropagation();\n                        that.view()._removeTaskTooltip();\n                    }).on(MOUSELEAVE + NS, DOT + styles.task, function (e) {\n                        var parents = $(e.relatedTarget).parents(DOT + styles.taskWrap, DOT + styles.task);\n                        if (parents.length === 0) {\n                            that.view()._removeTaskTooltip();\n                        }\n                    });\n                    if (this.touch) {\n                        this.touch.bind('tap', function (e) {\n                            var element = e.touch.target;\n                            var task = that._taskByUid($(element).attr('data-uid'));\n                            var currentPosition = e.touch.x.client;\n                            if (that.view()._taskTooltip) {\n                                that.view()._removeTaskTooltip();\n                            }\n                            that.view()._createTaskTooltip(task, element, currentPosition);\n                        }).bind('doubletap', function () {\n                            that.view()._removeTaskTooltip();\n                        });\n                    }\n                }\n            }\n        });\n        extend(true, GanttTimeline, { styles: timelineStyles });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}