{"version": 3, "sources": ["kendo.popup.js"], "names": ["f", "define", "$", "undefined", "contains", "container", "target", "stableSort", "tabKeyTrapNS", "focusableNodesSelector", "TabKeyTrap", "kendo", "window", "ui", "Widget", "Class", "support", "getOffset", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "OPEN", "CLOSE", "DEACTIVATE", "ACTIVATE", "CENTER", "LEFT", "RIGHT", "TOP", "BOTTOM", "ABSOLUTE", "HIDDEN", "BODY", "LOCATION", "POSITION", "VISIBLE", "EFFECTS", "ACTIVE", "ACTIVEBORDER", "ACTIVEBORDERREGEXP", "ACTIVECHILDREN", "MOUSEDOWN", "DOCUMENT_ELEMENT", "document", "documentElement", "proxy", "WINDOW", "SCROLL", "cssPrefix", "transitions", "css", "TRANSFORM", "extend", "NS", "styles", "Popup", "init", "element", "options", "parentPopup", "that", "this", "isRtl", "origin", "position", "fn", "call", "collisions", "collision", "split", "downEvent", "applyEventMap", "guid", "length", "push", "anchor", "closest", "filter", "appendTo", "body", "hide", "addClass", "toggleClass", "attr", "on", "_hovered", "e", "list", "find", "scrollArea", "parent", "is", "scrollTop", "originalEvent", "deltaY", "prop", "preventDefault", "wrapper", "animation", "open", "effects", "close", "complete", "overflow", "_activated", "_trigger", "_animationClose", "_mousedownProxy", "_mousedown", "_resizeProxy", "mobileOS", "android", "setTimeout", "_resize", "toggle<PERSON><PERSON><PERSON>", "toggleEvent", "toggle", "events", "name", "viewport", "copyAnchorStyles", "autosize", "modal", "adjustSize", "width", "height", "transition", "duration", "location", "data", "_hideDirClass", "_closing", "destroy", "off", "_toggleResize", "children", "removeData", "remove", "x", "y", "fixed", "isFixed", "isNaN", "parseInt", "mobile", "hasClass", "visible", "shift", "getComputedStyles", "wrap", "display", "flipped", "_position", "_openAnimation", "_showDirClass", "kendoStop", "kendoAnimate", "_location", "offset", "left", "top", "parseEffects", "direction", "match", "dir<PERSON><PERSON>", "removeClass", "directions", "reverse", "slideIn", "skipEffects", "openEffects", "closeEffects", "each", "popup", "size", "ev", "trigger", "type", "resize", "indexOf", "clearTimeout", "_resizeTimeout", "method", "eventNames", "ios", "scrollableParents", "_scrollableParents", "eventTarget", "_fit", "viewPortSize", "output", "_flip", "anchorSize", "boxSize", "parentsUntil", "index", "isScrollable", "sibling<PERSON><PERSON><PERSON>", "parents", "parentZIndex", "viewportOffset", "viewportWidth", "viewportHeight", "sign", "pos", "anchorParent", "offsets", "flipPos", "elementHeight", "wrapperHeight", "zoomLevel", "isWindow", "innerWidth", "origins", "toLowerCase", "positions", "zIndex", "idx", "docEl", "pageYOffset", "pageXOffset", "scrollLeft", "innerHeight", "scrollHeight", "clientHeight", "scrollbar", "siblings", "Math", "max", "_align", "offsetParent", "appendToOffset", "verticalOrigin", "horizontal<PERSON><PERSON>in", "verticalPosition", "horizontalPosition", "anchorOffset", "first", "anchorWidth", "anchorHeight", "round", "plugin", "autoApplyNS", "trap", "_keepInTrap", "removeTrap", "kendoD<PERSON>roy", "shouldTrap", "elements", "sortedElements", "next", "which", "isDefaultPrevented", "_focusableElements", "_sortFocusableElements", "_nextFocusable", "_focus", "i", "item", "tabIndex", "attrName", "sort", "prev", "setAttribute", "getAttribute", "removeAttr", "count", "current", "get", "shift<PERSON>ey", "nodeName", "contentWindow", "focus", "setSelectionRange", "_haveSelectionRange", "value", "elementType", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,eAAgB,cAAeD,IACxC,WA2jBE,MAnjBC,UAAUE,EAAGC,GASV,QAASC,GAASC,EAAWC,GACzB,SAAKD,IAAcC,KAGZD,IAAcC,GAAUJ,EAAEE,SAASC,EAAWC,IAb5D,GAmeOC,GACAC,EACAC,EACAC,EAreAC,EAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAASD,EAAGC,OAAQC,EAAQJ,EAAMI,MAAOC,EAAUL,EAAMK,QAASC,EAAYN,EAAMM,UAAWC,EAAaP,EAAMQ,YAAaC,EAAcT,EAAMU,aAAcC,EAAO,OAAQC,EAAQ,QAASC,EAAa,aAAcC,EAAW,WAAYC,EAAS,SAAUC,EAAO,OAAQC,EAAQ,QAASC,EAAM,MAAOC,EAAS,SAAUC,EAAW,WAAYC,EAAS,SAAUC,EAAO,OAAQC,EAAW,WAAYC,EAAW,WAAYC,EAAU,UAAWC,EAAU,UAAWC,EAAS,iBAAkBC,EAAe,iBAAkBC,EAAqB,uBAAwBC,EAAiB,4CAA6CC,EAAY,OAAQC,EAAmBzC,EAAE0C,SAASC,iBAAkBC,EAAQ5C,EAAE4C,MAAOC,EAAS7C,EAAEU,QAASoC,EAAS,SAAUC,EAAYjC,EAAQkC,YAAYC,IAAKC,EAAYH,EAAY,YAAaI,EAASnD,EAAEmD,OAAQC,EAAK,cAAeC,GAC54B,YACA,cACA,eACA,aACA,cACA,eAQJC,EAAQ1C,EAAOuC,QACfI,KAAM,SAAUC,EAASC,GACrB,GAAiBC,GAAbC,EAAOC,IACXH,GAAUA,MACNA,EAAQI,QACRJ,EAAQK,OAASL,EAAQK,QAAUlC,EAAS,IAAMF,EAClD+B,EAAQM,SAAWN,EAAQM,UAAYpC,EAAM,IAAMD,GAEvDd,EAAOoD,GAAGT,KAAKU,KAAKN,EAAMH,EAASC,GACnCD,EAAUG,EAAKH,QACfC,EAAUE,EAAKF,QACfE,EAAKO,WAAaT,EAAQU,UAAYV,EAAQU,UAAUC,MAAM,QAC9DT,EAAKU,UAAY5D,EAAM6D,cAAc9B,EAAW/B,EAAM8D,QACvB,IAA3BZ,EAAKO,WAAWM,QAChBb,EAAKO,WAAWO,KAAKd,EAAKO,WAAW,IAEzCR,EAAc1D,EAAE2D,EAAKF,QAAQiB,QAAQC,QAAQ,qBAAqBC,OAAO,sBACzEnB,EAAQoB,SAAW7E,EAAEA,EAAEyD,EAAQoB,UAAU,IAAMnB,EAAY,IAAMhB,SAASoC,MAC1EnB,EAAKH,QAAQuB,OAAOC,SAAS,2BAA2BC,YAAY,UAAWxB,EAAQI,OAAOZ,KAAMc,SAAUlC,IAAYgD,SAASpB,EAAQoB,UAAUK,KAAK,eAAe,GAAMC,GAAG,aAAe/B,EAAI,WACjMO,EAAKyB,UAAW,IACjBD,GAAG,QAAU/B,EAAI,SAAUiC,GAAV,GACZC,GAAOtF,EAAEqF,EAAEjF,QAAQmF,KAAK,WACxBC,EAAaF,EAAKG,QAClBH,GAAKd,QAAUc,EAAKI,GAAG,cAA2C,IAA3BF,EAAWG,aAAqBN,EAAEO,cAAcC,OAAS,GAAKL,EAAWG,cAAgBH,EAAWM,KAAK,gBAAkBN,EAAWM,KAAK,iBAAmBT,EAAEO,cAAcC,OAAS,IAC9NR,EAAEU,mBAEPZ,GAAG,aAAe/B,EAAI,WACrBO,EAAKyB,UAAW,IAEpBzB,EAAKqC,QAAUhG,IACXyD,EAAQwC,aAAc,IACtBxC,EAAQwC,WACJC,MAAQC,YACRC,OACIrB,MAAM,EACNoB,cAIZhD,EAAOM,EAAQwC,UAAUC,MACrBG,SAAU,WACN1C,EAAKqC,QAAQ/C,KAAMqD,SAAUpE,IAC7ByB,EAAK4C,YAAa,EAClB5C,EAAK6C,SAASjF,MAGtB4B,EAAOM,EAAQwC,UAAUG,OACrBC,SAAU,WACN1C,EAAK8C,qBAGb9C,EAAK+C,gBAAkB,SAAUrB,GAC7B1B,EAAKgD,WAAWtB,IAGhB1B,EAAKiD,aADL9F,EAAQ+F,SAASC,QACG,SAAUzB,GAC1B0B,WAAW,WACPpD,EAAKqD,QAAQ3B,IACd,MAGa,SAAUA,GAC1B1B,EAAKqD,QAAQ3B,IAGjB5B,EAAQwD,cACRjH,EAAEyD,EAAQwD,cAAc9B,GAAG1B,EAAQyD,YAAc9D,EAAIpD,EAAE4C,MAAMe,EAAKwD,OAAQxD,KAGlFyD,QACIhG,EACAG,EACAF,EACAC,GAEJmC,SACI4D,KAAM,QACNH,YAAa,QACbpD,OAAQlC,EAAS,IAAMH,EACvBsC,SAAUpC,EAAM,IAAMF,EACtBiD,OAAQ3C,EACR8C,SAAU,KACVV,UAAW,WACXmD,SAAU5G,OACV6G,kBAAkB,EAClBC,UAAU,EACVC,OAAO,EACPC,YACIC,MAAO,EACPC,OAAQ,GAEZ3B,WACIC,MACIC,QAAS,eACT0B,YAAY,EACZC,SAAU,KAEd1B,OACI0B,SAAU,IACV/C,MAAM,KAIlB0B,gBAAiB,WAAA,GACT9C,GAAOC,KACPmE,EAAWpE,EAAKqC,QAAQgC,KAAKhG,EACjC2B,GAAKqC,QAAQjB,OACTgD,GACApE,EAAKqC,QAAQ/C,IAAI8E,GAEjBpE,EAAKF,QAAQiB,QAAU3C,GACvB4B,EAAKsE,gBAETtE,EAAKuE,UAAW,EAChBvE,EAAK6C,SAASlF,IAElB6G,QAAS,WACL,GAAyE1C,GAArE9B,EAAOC,KAAMH,EAAUE,EAAKF,QAASD,EAAUG,EAAKH,QAAQ4E,IAAIhF,EACpExC,GAAOoD,GAAGmE,QAAQlE,KAAKN,GACnBF,EAAQwD,cACRjH,EAAEyD,EAAQwD,cAAcmB,IAAIhF,GAE3BK,EAAQgE,QACThF,EAAiB2F,IAAIzE,EAAKU,UAAWV,EAAK+C,iBAC1C/C,EAAK0E,eAAc,IAEvB5H,EAAM0H,QAAQxE,EAAKH,QAAQ8E,YAC3B9E,EAAQ+E,aACJ9E,EAAQoB,SAAS,KAAOnC,SAASoC,OACjCW,EAASjC,EAAQiC,OAAO,0BACpBA,EAAO,GACPA,EAAO+C,SAEPhF,EAAQgF,WAIpBtC,KAAM,SAAUuC,EAAGC,GACf,GAIuDzC,GAAWD,EAJ9DrC,EAAOC,KAAM+E,GACTC,SAAUC,MAAMC,SAASJ,EAAG,KAC5BD,EAAGA,EACHC,EAAGA,GACJlF,EAAUG,EAAKH,QAASC,EAAUE,EAAKF,QAA6BiB,EAAS1E,EAAEyD,EAAQiB,QAASqE,EAASvF,EAAQ,IAAMA,EAAQwF,SAAS,YAC/I,KAAKrF,EAAKsF,UAAW,CAOjB,GANIxF,EAAQ8D,mBACJwB,GAAuB,aAAb1F,EAAO,IACjBA,EAAO6F,QAEX1F,EAAQP,IAAIxC,EAAM0I,kBAAkBzE,EAAO,GAAIrB,KAE/CG,EAAQwE,KAAK,cAAgBrE,EAAK6C,SAASpF,GAC3C,MAEJuC,GAAK4C,YAAa,EACb9C,EAAQgE,QACThF,EAAiB2F,IAAIzE,EAAKU,UAAWV,EAAK+C,iBAAiBvB,GAAGxB,EAAKU,UAAWV,EAAK+C,iBACnF/C,EAAK0E,eAAc,GACnB1E,EAAK0E,eAAc,IAEvB1E,EAAKqC,QAAUA,EAAUvF,EAAM2I,KAAK5F,EAASC,EAAQ+D,UAAUvE,KAC3DqD,SAAUxE,EACVuH,QAAS,QACTtF,SAAUlC,IACXqD,KAAK,eAAe,GACnBpE,EAAQ+F,SAASC,SACjBd,EAAQ/C,IAAIC,EAAW,iBAE3B8C,EAAQ/C,IAAIhB,GACRjC,EAAEyD,EAAQoB,UAAU,IAAMnC,SAASoC,MACnCkB,EAAQ/C,IAAItB,EAAK,YAErBgC,EAAK2F,QAAU3F,EAAK4F,UAAUZ,GAC9B1C,EAAYtC,EAAK6F,iBACb/F,EAAQiB,QAAU3C,GAClB4B,EAAK8F,cAAcxD,GAEvBzC,EAAQwE,KAAK7F,EAAS8D,EAAUE,SAASuD,WAAU,GAAMC,aAAa1D,GAAWf,KAAK,eAAe,KAG7G0E,UAAW,SAAUhB,GAAV,GAC0D5C,GAoB7D6D,EApBAlG,EAAOC,KAAMJ,EAAUG,EAAKH,QAASC,EAAUE,EAAKF,QAAkBiB,EAAS1E,EAAEyD,EAAQiB,QAASqE,EAASvF,EAAQ,IAAMA,EAAQwF,SAAS,YAqB9I,OApBIvF,GAAQ8D,mBACJwB,GAAuB,aAAb1F,EAAO,IACjBA,EAAO6F,QAEX1F,EAAQP,IAAIxC,EAAM0I,kBAAkBzE,EAAO,GAAIrB,KAEnDM,EAAKqC,QAAUA,EAAUvF,EAAM2I,KAAK5F,EAASC,EAAQ+D,UAAUvE,KAC3DqD,SAAUxE,EACVuH,QAAS,QACTtF,SAAUlC,IAEVf,EAAQ+F,SAASC,SACjBd,EAAQ/C,IAAIC,EAAW,iBAE3B8C,EAAQ/C,IAAIhB,GACRjC,EAAEyD,EAAQoB,UAAU,IAAMnC,SAASoC,MACnCkB,EAAQ/C,IAAItB,EAAK,YAErBgC,EAAK4F,UAAUX,OACXiB,EAAS7D,EAAQ6D,UAEjBlC,MAAOlH,EAAMQ,YAAY+E,GACzB4B,OAAQnH,EAAMU,aAAa6E,GAC3B8D,KAAMD,EAAOC,KACbC,IAAKF,EAAOE,MAGpBP,eAAgB,WACZ,GAAIvD,GAAY9C,GAAO,KAAUS,KAAKH,QAAQwC,UAAUC,KAExD,OADAD,GAAUE,QAAU1F,EAAMuJ,aAAa/D,EAAUE,QAASvC,KAAK0F,SACxDrD,GAEXgC,cAAe,WAAA,GACPvD,GAAS1E,EAAE4D,KAAKH,QAAQiB,QACxBuF,IAAcvF,EAAOQ,KAAK,UAAY,IAAIgF,MAAM5H,KAChD,GACA,SACD,GACC6H,EAAW9H,EAAe,IAAM4H,CACpCvF,GAAO0F,YAAYD,GAAU7B,SAAS/F,GAAgB6H,YAAYhI,GAAQgI,YAAYD,GACtFvG,KAAKJ,QAAQ4G,YAAY/H,EAAe,IAAM5B,EAAM4J,WAAWJ,GAAWK,UAE9Eb,cAAe,SAAUxD,GAAV,GACPgE,GAAYhE,EAAUE,QAAQoE,QAAUtE,EAAUE,QAAQoE,QAAQN,UAAY,OAC9EE,EAAW9H,EAAe,IAAM4H,CACpCjK,GAAE4D,KAAKH,QAAQiB,QAAQM,SAASmF,GAAU7B,SAAS/F,GAAgByC,SAAS5C,GAAQ4C,SAASmF,GAC7FvG,KAAKJ,QAAQwB,SAAS3C,EAAe,IAAM5B,EAAM4J,WAAWJ,GAAWK,UAE3EvG,SAAU,WACFH,KAAKqF,YACLrF,KAAK0F,QAAU1F,KAAK2F,cAG5BpC,OAAQ,WACJ,GAAIxD,GAAOC,IACXD,GAAKA,EAAKsF,UAAY5H,EAAQD,MAElC6H,QAAS,WACL,MAAOrF,MAAKJ,QAAQkC,GAAG,IAAMxD,IAEjCkE,MAAO,SAAUoE,GACb,GAAyCpB,GAAMnD,EAAWwE,EAAaC,EAAnE/G,EAAOC,KAAMH,EAAUE,EAAKF,OAChC,IAAIE,EAAKsF,UAAW,CAGhB,GAFAG,EAAOzF,EAAKqC,QAAQ,GAAKrC,EAAKqC,QAAUvF,EAAM2I,KAAKzF,EAAKH,SAASuB,OACjEpB,EAAK0E,eAAc,GACf1E,EAAKuE,UAAYvE,EAAK6C,SAASnF,GAE/B,MADAsC,GAAK0E,eAAc,GACnB,CAEJ1E,GAAKH,QAAQ+B,KAAK,YAAYoF,KAAK,WAC/B,GAAIhH,GAAO3D,EAAE4D,MAAOgH,EAAQjH,EAAKqE,KAAK,aAClC4C,IACAA,EAAMxE,MAAMoE,KAGpB/H,EAAiB2F,IAAIzE,EAAKU,UAAWV,EAAK+C,iBACtC8D,EACAvE,GACIlB,MAAM,EACNoB,aAGJF,EAAY9C,GAAO,KAAUM,EAAQwC,UAAUG,OAC/CqE,EAAc9G,EAAKH,QAAQwE,KAAK7F,GAChCuI,EAAezE,EAAUE,SACpBuE,IAAiBjK,EAAMoK,KAAKH,IAAiBD,GAAehK,EAAMoK,KAAKJ,KACxExE,EAAUE,QAAUsE,EACpBxE,EAAUqE,SAAU,GAExB3G,EAAKuE,UAAW,GAEpBvE,EAAKH,QAAQkG,WAAU,GAAMxE,KAAK,eAAe,GACjDkE,EAAKnG,KAAMqD,SAAUxE,IAAUoD,KAAK,eAAe,GACnDvB,EAAKH,QAAQmG,aAAa1D,GACtBuE,GACA7G,EAAK8C,oBAIjBD,SAAU,SAAUsE,GAChB,MAAOlH,MAAKmH,QAAQD,GAAME,KAAMF,KAEpC9D,QAAS,SAAU3B,GACf,GAAI1B,GAAOC,IACP9C,GAAQmK,OAAOC,QAAQ7F,EAAE2F,YACzBG,aAAaxH,EAAKyH,gBAClBzH,EAAKyH,eAAiBrE,WAAW,WAC7BpD,EAAK4F,YACL5F,EAAKyH,eAAiB,MACvB,OAEEzH,EAAKyB,UAAYzB,EAAK4C,YAAc5C,EAAKH,QAAQwF,SAAS,sBAC3DrF,EAAKyC,SAIjBiC,cAAe,SAAUlB,GAAV,GACPkE,GAASlE,EAAS,KAAO,MACzBmE,EAAaxK,EAAQmK,MACnBnK,GAAQ+F,SAAS0E,KAAOzK,EAAQ+F,SAASC,UAC3CwE,GAAc,IAAMxI,GAEpBqE,IAAWvD,KAAK4H,oBAChB5H,KAAK4H,kBAAoB5H,KAAK6H,sBAE9B7H,KAAK4H,mBAAqB5H,KAAK4H,kBAAkBhH,QACjDZ,KAAK4H,kBAAkBH,GAAQvI,EAAQc,KAAKgD,cAEhD/D,EAAOwI,GAAQC,EAAY1H,KAAKgD,eAEpCD,WAAY,SAAUtB,GAClB,GAAI1B,GAAOC,KAAMzD,EAAYwD,EAAKH,QAAQ,GAAIC,EAAUE,EAAKF,QAASiB,EAAS1E,EAAEyD,EAAQiB,QAAQ,GAAIuC,EAAexD,EAAQwD,aAAc7G,EAASK,EAAMiL,YAAYrG,GAAIuF,EAAQ5K,EAAEI,GAAQuE,QAAQ,YAAaoE,EAAS6B,EAAMnF,SAASA,OAAO,YAAYjB,MAC3PoG,GAAQA,EAAM,IACT7B,GAAU6B,GAASA,IAAUjH,EAAKH,QAAQ,IAGF,YAAzCxD,EAAEqF,EAAEjF,QAAQuE,QAAQ,KAAKqD,KAAK,SAG7B9H,EAASC,EAAWC,IAAYF,EAASwE,EAAQtE,IAAa6G,GAAgB/G,EAASF,EAAEiH,GAAc,GAAI7G,IAC5GuD,EAAKyC,UAGbuF,KAAM,SAAU5H,EAAU8G,EAAMe,GAC5B,GAAIC,GAAS,CAOb,OANI9H,GAAW8G,EAAOe,IAClBC,EAASD,GAAgB7H,EAAW8G,IAEpC9G,EAAW,IACX8H,GAAU9H,GAEP8H,GAEXC,MAAO,SAAUjC,EAAQgB,EAAMkB,EAAYH,EAAc9H,EAAQC,EAAUiI,GACvE,GAAIH,GAAS,CAUb,OATAG,GAAUA,GAAWnB,EACjB9G,IAAaD,GAAUC,IAAavC,GAAUsC,IAAWtC,IACrDqI,EAASmC,EAAUJ,IACnBC,KAAYE,EAAalB,IAEzBhB,EAASgC,EAAS,IAClBA,GAAUE,EAAalB,IAGxBgB,GAEXJ,mBAAoB,WAChB,MAAOzL,GAAE4D,KAAKH,QAAQiB,QAAQuH,aAAa,QAAQrH,OAAO,SAAUsH,EAAO1I,GACvE,MAAO/C,GAAM0L,aAAa3I,MAGlC+F,UAAW,SAAUZ,GAAV,GACuYyD,GAAkBC,EAASC,EAAyE9H,EAAQ+H,EAAgBC,EAAeC,EAiBjhBC,EA2BJC,EAA6E9C,EAA6B+C,EAU1GC,EAA8B9E,EAA4BL,EAO1DoF,EACAC,EACAC,EA/DArJ,EAAOC,KAAMJ,EAAUG,EAAKH,QAASwC,EAAUrC,EAAKqC,QAASvC,EAAUE,EAAKF,QAAS6D,EAAWtH,EAAEyD,EAAQ6D,UAAW2F,EAAYnM,EAAQmM,YAAaC,KAAc5F,EAAS,IAAM5G,QAAUA,OAAOyM,YAAcF,GAAa,MAAOvI,EAAS1E,EAAEyD,EAAQiB,QAAS0I,EAAU3J,EAAQK,OAAOuJ,cAAcjJ,MAAM,KAAMkJ,EAAY7J,EAAQM,SAASsJ,cAAcjJ,MAAM,KAAMF,EAAaP,EAAKO,WAAqDqJ,EAAS,MAAOC,EAAM,EAAGC,EAAQ/K,SAASC,eAqBje,IAnBI4J,EADA9I,EAAQ6D,WAAa5G,QAEjBqJ,IAAKrJ,OAAOgN,aAAehL,SAASC,gBAAgBgD,WAAa,EACjEmE,KAAMpJ,OAAOiN,aAAejL,SAASC,gBAAgBiL,YAAc,GAGtDtG,EAASuC,SAE1BqD,GACAV,EAAgB9L,OAAOyM,WACvBV,EAAiB/L,OAAOmN,cAExBrB,EAAgBlF,EAASK,QACzB8E,EAAiBnF,EAASM,UAE1BsF,GAAYO,EAAMK,aAAeL,EAAMM,aAAe,IAClDrB,EAAOjJ,EAAQI,SAAa,EAChC2I,GAAiBE,EAAOjM,EAAMK,QAAQkN,aAE1C5B,EAAmB1H,EAAO2H,UAAUzH,OAAOoB,EAAQiI,YAC/C7B,EAAiB,GAEjB,GADAE,EAAe4B,KAAKC,KAAW/B,EAAiBnJ,IAAI,UAAY,GAE5DsK,EAASjB,EAAe,OAGxB,KADAD,EAAU3H,EAAOuH,aAAaG,GACzB5H,EAAS6H,EAAQ7H,OAAQgJ,EAAMhJ,EAAQgJ,IACxClB,GAAsBtM,EAAEqM,EAAQmB,IAAMvK,IAAI,UACtCqJ,GAAgBiB,EAASjB,IACzBiB,EAASjB,EAAe,GA6CxC,OAxCAtG,GAAQ/C,IAAI,SAAUsK,GAElBvH,EAAQ/C,IADR0F,GAASA,EAAMC,SAEXkB,KAAMnB,EAAMF,EACZsB,IAAKpB,EAAMD,GAGH/E,EAAKyK,OAAOhB,EAASE,IAEjCX,EAAM5L,EAAUiF,EAAS/D,EAAUyC,EAAO,KAAOsB,EAAQqI,eAAe,IAAKxE,EAAS9I,EAAUiF,GAAU4G,EAAelI,EAAO2J,eAAe5I,OAAO,4CACtJmH,EAAapI,SACbmI,EAAM5L,EAAUiF,EAAS/D,GAAU,GACnC4H,EAAS9I,EAAUiF,IAEvB6D,EAAOE,KAAOwC,EAAexC,IAC7BF,EAAOC,MAAQyC,EAAezC,KACzBnG,EAAKqC,QAAQgC,KAAKhG,IACnBgE,EAAQgC,KAAKhG,EAAUmB,KAAWwJ,IAElCE,EAAU1J,KAAW0G,GAAS9B,EAAW5E,KAAWwJ,GAAMjF,EAAajE,EAAQiE,WAC7D,QAAlBxD,EAAW,KACX6D,EAASgC,KAAOpG,EAAKgI,KAAKkB,EAAQ9C,IAAK7I,EAAY8E,GAAW0B,EAAWE,OAAQ6E,EAAiBQ,IAEhF,QAAlB/I,EAAW,KACX6D,EAAS+B,MAAQnG,EAAKgI,KAAKkB,EAAQ/C,KAAM9I,EAAWgF,GAAW0B,EAAWC,MAAO6E,EAAgBS,IAEjGH,EAAU3J,KAAW4E,GACrBgF,EAAgB7L,EAAYsC,GAC5BwJ,EAAgB9L,EAAY8E,IAC3BA,EAAQ4B,UAAYmF,IACrBC,GAAgCD,GAEd,SAAlB7I,EAAW,KACX6D,EAASgC,KAAOpG,EAAKmI,MAAMe,EAAQ9C,IAAKgD,EAAe7L,EAAYwD,GAAS+H,EAAiBQ,EAAWG,EAAQ,GAAIE,EAAU,GAAIN,IAEhH,SAAlB9I,EAAW,KACX6D,EAAS+B,MAAQnG,EAAKmI,MAAMe,EAAQ/C,KAAM9I,EAAWwC,GAAUxC,EAAW0D,GAAS8H,EAAgBS,EAAWG,EAAQ,GAAIE,EAAU,GAAItM,EAAWgF,KAEvJxC,EAAQP,IAAIhB,EAAUJ,GACtBmE,EAAQ/C,IAAI8E,GACLA,EAAS+B,MAAQgD,EAAQhD,MAAQ/B,EAASgC,KAAO+C,EAAQ/C,KAEpEqE,OAAQ,SAAUtK,EAAQC,GACtB,GAA6QuK,GAAzQ3K,EAAOC,KAAMJ,EAAUG,EAAKqC,QAAStB,EAAS1E,EAAE2D,EAAKF,QAAQiB,QAAS6J,EAAiBzK,EAAO,GAAI0K,EAAmB1K,EAAO,GAAI2K,EAAmB1K,EAAS,GAAI2K,EAAqB3K,EAAS,GAAI4K,EAAe5N,EAAU2D,GAASG,EAAW7E,EAAE2D,EAAKF,QAAQoB,UAA2B8C,EAAQ3G,EAAWwC,GAAUoE,EAAS1G,EAAYsC,IAAYtC,EAAYsC,EAAQ8E,WAAWsG,SAAUC,EAAc7N,EAAW0D,GAASoK,EAAe5N,EAAYwD,GAASqF,EAAM4E,EAAa5E,IAAKD,EAAO6E,EAAa7E,KAAMiF,EAAQb,KAAKa,KA8BzgB,OA7BIlK,GAAS,IAAMnC,SAASoC,OACxBwJ,EAAiBvN,EAAU8D,GAC3BkF,GAAOuE,EAAevE,IACtBD,GAAQwE,EAAexE,MAEvByE,IAAmB3M,IACnBmI,GAAO+E,GAEPP,IAAmB/M,IACnBuI,GAAOgF,EAAMD,EAAe,IAE5BL,IAAqB7M,IACrBmI,GAAOnC,GAEP6G,IAAqBjN,IACrBuI,GAAOgF,EAAMnH,EAAS,IAEtB4G,IAAqB9M,IACrBoI,GAAQ+E,GAERL,IAAqBhN,IACrBsI,GAAQiF,EAAMF,EAAc,IAE5BH,IAAuBhN,IACvBoI,GAAQnC,GAER+G,IAAuBlN,IACvBsI,GAAQiF,EAAMpH,EAAQ,KAGtBoC,IAAKA,EACLD,KAAMA,KAIlBnJ,GAAGqO,OAAO1L,GACNjD,EAAaI,EAAMK,QAAQT,WAC3BC,EAAe,kBACfC,EAAyB,8KACzBC,EAAaK,EAAMsC,QACnBI,KAAM,SAAUC,GACZI,KAAKJ,QAAUxD,EAAEwD,GACjBI,KAAKJ,QAAQyL,YAAY3O,IAE7B4O,KAAM,WACFtL,KAAKJ,QAAQ2B,GAAG,UAAWvC,EAAMgB,KAAKuL,YAAavL,QAEvDwL,WAAY,WACRxL,KAAKJ,QAAQ6L,aAAa/O,IAE9B6H,QAAS,WACLvE,KAAKJ,QAAQ6L,aAAa/O,GAC1BsD,KAAKJ,QAAUvD,GAEnBqP,WAAY,WACR,OAAO,GAEXH,YAAa,SAAU9J,GAAV,GAILkK,GACAC,EACAC,CALY,KAAZpK,EAAEqK,OAAgB9L,KAAK0L,eAAgBjK,EAAEsK,uBAGzCJ,EAAW3L,KAAKgM,qBAChBJ,EAAiB5L,KAAKiM,uBAAuBN,GAC7CE,EAAO7L,KAAKkM,eAAezK,EAAGmK,GAClC5L,KAAKmM,OAAON,GACZpK,EAAEU,mBAEN6J,mBAAoB,WAChB,GAAIL,GAAW3L,KAAKJ,QAAQ+B,KAAKhF,GAAwBqE,OAAO,SAAUoL,EAAGC,GACzE,MAAOA,GAAKC,UAAY,GAAKlQ,EAAEiQ,GAAMvK,GAAG,cAAgB1F,EAAEiQ,GAAMvK,GAAG,eAKvE,OAHI9B,MAAKJ,QAAQkC,GAAG,eAChB6J,EAAS9K,KAAKb,KAAKJ,QAAQ,IAExB+L,GAEXM,uBAAwB,SAAUN,GAAV,GAChBC,GAMIW,CASR,OAdI9P,GACAmP,EAAiBD,EAASa,KAAK,SAAUC,EAAMZ,GAC3C,MAAOY,GAAKH,SAAWT,EAAKS,YAG5BC,EAAW,YACfZ,EAAS5E,KAAK,SAAUqF,EAAGC,GACvBA,EAAKK,aAAaH,EAAUH,KAEhCR,EAAiBD,EAASa,KAAK,SAAUC,EAAMZ,GAC3C,MAAOY,GAAKH,WAAaT,EAAKS,SAAWpH,SAASuH,EAAKE,aAAaJ,GAAW,IAAMrH,SAAS2G,EAAKc,aAAaJ,GAAW,IAAME,EAAKH,SAAWT,EAAKS,WAE1JX,EAASiB,WAAWL,IAEjBX,GAEXM,eAAgB,SAAUzK,EAAGkK,GAAb,GACRkB,GAAQlB,EAAS/K,OACjBkM,EAAUnB,EAASrD,MAAM7G,EAAEjF,OAC/B,OAAOmP,GAASoB,KAAKD,GAAWrL,EAAEuL,YAAgB,IAAMH,IAE5DV,OAAQ,SAAUvM,GACd,MAAwB,UAApBA,EAAQqN,UACRrN,EAAQsN,cAAcpO,SAASoC,KAAKiM,QACpC,IAEJvN,EAAQuN,QACgB,SAApBvN,EAAQqN,UAAuBrN,EAAQwN,mBAAqBpN,KAAKqN,oBAAoBzN,IACrFA,EAAQwN,kBAAkB,EAAGxN,EAAQ0N,MAAM1M,QAF/ChB,IAKJyN,oBAAqB,SAAUzN,GAC3B,GAAI2N,GAAc3N,EAAQwH,KAAKqC,aAC/B,OAAuB,SAAhB8D,GAA0C,WAAhBA,GAA4C,QAAhBA,GAAyC,QAAhBA,GAAyC,aAAhBA,KAGvHxQ,EAAG2C,MAAM9C,WAAaA,GACxBE,OAAOD,MAAM2Q,QACR1Q,OAAOD,OACE,kBAAVV,SAAwBA,OAAOsR,IAAMtR,OAAS,SAAUuR,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.popup.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.popup', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'popup',\n        name: 'Pop-up',\n        category: 'framework',\n        depends: ['core'],\n        advanced: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, Widget = ui.Widget, Class = kendo.Class, support = kendo.support, getOffset = kendo.getOffset, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, OPEN = 'open', CLOSE = 'close', DEACTIVATE = 'deactivate', ACTIVATE = 'activate', CENTER = 'center', LEFT = 'left', RIGHT = 'right', TOP = 'top', BOTTOM = 'bottom', ABSOLUTE = 'absolute', HIDDEN = 'hidden', BODY = 'body', LOCATION = 'location', POSITION = 'position', VISIBLE = 'visible', EFFECTS = 'effects', ACTIVE = 'k-state-active', ACTIVEBORDER = 'k-state-border', ACTIVEBORDERREGEXP = /k-state-border-(\\w+)/, ACTIVECHILDREN = '.k-picker-wrap, .k-dropdown-wrap, .k-link', MOUSEDOWN = 'down', DOCUMENT_ELEMENT = $(document.documentElement), proxy = $.proxy, WINDOW = $(window), SCROLL = 'scroll', cssPrefix = support.transitions.css, TRANSFORM = cssPrefix + 'transform', extend = $.extend, NS = '.kendoPopup', styles = [\n                'font-size',\n                'font-family',\n                'font-stretch',\n                'font-style',\n                'font-weight',\n                'line-height'\n            ];\n        function contains(container, target) {\n            if (!container || !target) {\n                return false;\n            }\n            return container === target || $.contains(container, target);\n        }\n        var Popup = Widget.extend({\n            init: function (element, options) {\n                var that = this, parentPopup;\n                options = options || {};\n                if (options.isRtl) {\n                    options.origin = options.origin || BOTTOM + ' ' + RIGHT;\n                    options.position = options.position || TOP + ' ' + RIGHT;\n                }\n                Widget.fn.init.call(that, element, options);\n                element = that.element;\n                options = that.options;\n                that.collisions = options.collision ? options.collision.split(' ') : [];\n                that.downEvent = kendo.applyEventMap(MOUSEDOWN, kendo.guid());\n                if (that.collisions.length === 1) {\n                    that.collisions.push(that.collisions[0]);\n                }\n                parentPopup = $(that.options.anchor).closest('.k-popup,.k-group').filter(':not([class^=km-])');\n                options.appendTo = $($(options.appendTo)[0] || parentPopup[0] || document.body);\n                that.element.hide().addClass('k-popup k-group k-reset').toggleClass('k-rtl', !!options.isRtl).css({ position: ABSOLUTE }).appendTo(options.appendTo).attr('aria-hidden', true).on('mouseenter' + NS, function () {\n                    that._hovered = true;\n                }).on('wheel' + NS, function (e) {\n                    var list = $(e.target).find('.k-list');\n                    var scrollArea = list.parent();\n                    if (list.length && list.is(':visible') && (scrollArea.scrollTop() === 0 && e.originalEvent.deltaY < 0 || scrollArea.scrollTop() === scrollArea.prop('scrollHeight') - scrollArea.prop('offsetHeight') && e.originalEvent.deltaY > 0)) {\n                        e.preventDefault();\n                    }\n                }).on('mouseleave' + NS, function () {\n                    that._hovered = false;\n                });\n                that.wrapper = $();\n                if (options.animation === false) {\n                    options.animation = {\n                        open: { effects: {} },\n                        close: {\n                            hide: true,\n                            effects: {}\n                        }\n                    };\n                }\n                extend(options.animation.open, {\n                    complete: function () {\n                        that.wrapper.css({ overflow: VISIBLE });\n                        that._activated = true;\n                        that._trigger(ACTIVATE);\n                    }\n                });\n                extend(options.animation.close, {\n                    complete: function () {\n                        that._animationClose();\n                    }\n                });\n                that._mousedownProxy = function (e) {\n                    that._mousedown(e);\n                };\n                if (support.mobileOS.android) {\n                    that._resizeProxy = function (e) {\n                        setTimeout(function () {\n                            that._resize(e);\n                        }, 600);\n                    };\n                } else {\n                    that._resizeProxy = function (e) {\n                        that._resize(e);\n                    };\n                }\n                if (options.toggleTarget) {\n                    $(options.toggleTarget).on(options.toggleEvent + NS, $.proxy(that.toggle, that));\n                }\n            },\n            events: [\n                OPEN,\n                ACTIVATE,\n                CLOSE,\n                DEACTIVATE\n            ],\n            options: {\n                name: 'Popup',\n                toggleEvent: 'click',\n                origin: BOTTOM + ' ' + LEFT,\n                position: TOP + ' ' + LEFT,\n                anchor: BODY,\n                appendTo: null,\n                collision: 'flip fit',\n                viewport: window,\n                copyAnchorStyles: true,\n                autosize: false,\n                modal: false,\n                adjustSize: {\n                    width: 0,\n                    height: 0\n                },\n                animation: {\n                    open: {\n                        effects: 'slideIn:down',\n                        transition: true,\n                        duration: 200\n                    },\n                    close: {\n                        duration: 100,\n                        hide: true\n                    }\n                }\n            },\n            _animationClose: function () {\n                var that = this;\n                var location = that.wrapper.data(LOCATION);\n                that.wrapper.hide();\n                if (location) {\n                    that.wrapper.css(location);\n                }\n                if (that.options.anchor != BODY) {\n                    that._hideDirClass();\n                }\n                that._closing = false;\n                that._trigger(DEACTIVATE);\n            },\n            destroy: function () {\n                var that = this, options = that.options, element = that.element.off(NS), parent;\n                Widget.fn.destroy.call(that);\n                if (options.toggleTarget) {\n                    $(options.toggleTarget).off(NS);\n                }\n                if (!options.modal) {\n                    DOCUMENT_ELEMENT.off(that.downEvent, that._mousedownProxy);\n                    that._toggleResize(false);\n                }\n                kendo.destroy(that.element.children());\n                element.removeData();\n                if (options.appendTo[0] === document.body) {\n                    parent = element.parent('.k-animation-container');\n                    if (parent[0]) {\n                        parent.remove();\n                    } else {\n                        element.remove();\n                    }\n                }\n            },\n            open: function (x, y) {\n                var that = this, fixed = {\n                        isFixed: !isNaN(parseInt(y, 10)),\n                        x: x,\n                        y: y\n                    }, element = that.element, options = that.options, animation, wrapper, anchor = $(options.anchor), mobile = element[0] && element.hasClass('km-widget');\n                if (!that.visible()) {\n                    if (options.copyAnchorStyles) {\n                        if (mobile && styles[0] == 'font-size') {\n                            styles.shift();\n                        }\n                        element.css(kendo.getComputedStyles(anchor[0], styles));\n                    }\n                    if (element.data('animating') || that._trigger(OPEN)) {\n                        return;\n                    }\n                    that._activated = false;\n                    if (!options.modal) {\n                        DOCUMENT_ELEMENT.off(that.downEvent, that._mousedownProxy).on(that.downEvent, that._mousedownProxy);\n                        that._toggleResize(false);\n                        that._toggleResize(true);\n                    }\n                    that.wrapper = wrapper = kendo.wrap(element, options.autosize).css({\n                        overflow: HIDDEN,\n                        display: 'block',\n                        position: ABSOLUTE\n                    }).attr('aria-hidden', false);\n                    if (support.mobileOS.android) {\n                        wrapper.css(TRANSFORM, 'translatez(0)');\n                    }\n                    wrapper.css(POSITION);\n                    if ($(options.appendTo)[0] == document.body) {\n                        wrapper.css(TOP, '-10000px');\n                    }\n                    that.flipped = that._position(fixed);\n                    animation = that._openAnimation();\n                    if (options.anchor != BODY) {\n                        that._showDirClass(animation);\n                    }\n                    element.data(EFFECTS, animation.effects).kendoStop(true).kendoAnimate(animation).attr('aria-hidden', false);\n                }\n            },\n            _location: function (isFixed) {\n                var that = this, element = that.element, options = that.options, wrapper, anchor = $(options.anchor), mobile = element[0] && element.hasClass('km-widget');\n                if (options.copyAnchorStyles) {\n                    if (mobile && styles[0] == 'font-size') {\n                        styles.shift();\n                    }\n                    element.css(kendo.getComputedStyles(anchor[0], styles));\n                }\n                that.wrapper = wrapper = kendo.wrap(element, options.autosize).css({\n                    overflow: HIDDEN,\n                    display: 'block',\n                    position: ABSOLUTE\n                });\n                if (support.mobileOS.android) {\n                    wrapper.css(TRANSFORM, 'translatez(0)');\n                }\n                wrapper.css(POSITION);\n                if ($(options.appendTo)[0] == document.body) {\n                    wrapper.css(TOP, '-10000px');\n                }\n                that._position(isFixed || {});\n                var offset = wrapper.offset();\n                return {\n                    width: kendo._outerWidth(wrapper),\n                    height: kendo._outerHeight(wrapper),\n                    left: offset.left,\n                    top: offset.top\n                };\n            },\n            _openAnimation: function () {\n                var animation = extend(true, {}, this.options.animation.open);\n                animation.effects = kendo.parseEffects(animation.effects, this.flipped);\n                return animation;\n            },\n            _hideDirClass: function () {\n                var anchor = $(this.options.anchor);\n                var direction = ((anchor.attr('class') || '').match(ACTIVEBORDERREGEXP) || [\n                    '',\n                    'down'\n                ])[1];\n                var dirClass = ACTIVEBORDER + '-' + direction;\n                anchor.removeClass(dirClass).children(ACTIVECHILDREN).removeClass(ACTIVE).removeClass(dirClass);\n                this.element.removeClass(ACTIVEBORDER + '-' + kendo.directions[direction].reverse);\n            },\n            _showDirClass: function (animation) {\n                var direction = animation.effects.slideIn ? animation.effects.slideIn.direction : 'down';\n                var dirClass = ACTIVEBORDER + '-' + direction;\n                $(this.options.anchor).addClass(dirClass).children(ACTIVECHILDREN).addClass(ACTIVE).addClass(dirClass);\n                this.element.addClass(ACTIVEBORDER + '-' + kendo.directions[direction].reverse);\n            },\n            position: function () {\n                if (this.visible()) {\n                    this.flipped = this._position();\n                }\n            },\n            toggle: function () {\n                var that = this;\n                that[that.visible() ? CLOSE : OPEN]();\n            },\n            visible: function () {\n                return this.element.is(':' + VISIBLE);\n            },\n            close: function (skipEffects) {\n                var that = this, options = that.options, wrap, animation, openEffects, closeEffects;\n                if (that.visible()) {\n                    wrap = that.wrapper[0] ? that.wrapper : kendo.wrap(that.element).hide();\n                    that._toggleResize(false);\n                    if (that._closing || that._trigger(CLOSE)) {\n                        that._toggleResize(true);\n                        return;\n                    }\n                    that.element.find('.k-popup').each(function () {\n                        var that = $(this), popup = that.data('kendoPopup');\n                        if (popup) {\n                            popup.close(skipEffects);\n                        }\n                    });\n                    DOCUMENT_ELEMENT.off(that.downEvent, that._mousedownProxy);\n                    if (skipEffects) {\n                        animation = {\n                            hide: true,\n                            effects: {}\n                        };\n                    } else {\n                        animation = extend(true, {}, options.animation.close);\n                        openEffects = that.element.data(EFFECTS);\n                        closeEffects = animation.effects;\n                        if (!closeEffects && !kendo.size(closeEffects) && openEffects && kendo.size(openEffects)) {\n                            animation.effects = openEffects;\n                            animation.reverse = true;\n                        }\n                        that._closing = true;\n                    }\n                    that.element.kendoStop(true).attr('aria-hidden', true);\n                    wrap.css({ overflow: HIDDEN }).attr('aria-hidden', true);\n                    that.element.kendoAnimate(animation);\n                    if (skipEffects) {\n                        that._animationClose();\n                    }\n                }\n            },\n            _trigger: function (ev) {\n                return this.trigger(ev, { type: ev });\n            },\n            _resize: function (e) {\n                var that = this;\n                if (support.resize.indexOf(e.type) !== -1) {\n                    clearTimeout(that._resizeTimeout);\n                    that._resizeTimeout = setTimeout(function () {\n                        that._position();\n                        that._resizeTimeout = null;\n                    }, 50);\n                } else {\n                    if (!that._hovered || that._activated && that.element.hasClass('k-list-container')) {\n                        that.close();\n                    }\n                }\n            },\n            _toggleResize: function (toggle) {\n                var method = toggle ? 'on' : 'off';\n                var eventNames = support.resize;\n                if (!(support.mobileOS.ios || support.mobileOS.android)) {\n                    eventNames += ' ' + SCROLL;\n                }\n                if (toggle && !this.scrollableParents) {\n                    this.scrollableParents = this._scrollableParents();\n                }\n                if (this.scrollableParents && this.scrollableParents.length) {\n                    this.scrollableParents[method](SCROLL, this._resizeProxy);\n                }\n                WINDOW[method](eventNames, this._resizeProxy);\n            },\n            _mousedown: function (e) {\n                var that = this, container = that.element[0], options = that.options, anchor = $(options.anchor)[0], toggleTarget = options.toggleTarget, target = kendo.eventTarget(e), popup = $(target).closest('.k-popup'), mobile = popup.parent().parent('.km-shim').length;\n                popup = popup[0];\n                if (!mobile && popup && popup !== that.element[0]) {\n                    return;\n                }\n                if ($(e.target).closest('a').data('rel') === 'popover') {\n                    return;\n                }\n                if (!contains(container, target) && !contains(anchor, target) && !(toggleTarget && contains($(toggleTarget)[0], target))) {\n                    that.close();\n                }\n            },\n            _fit: function (position, size, viewPortSize) {\n                var output = 0;\n                if (position + size > viewPortSize) {\n                    output = viewPortSize - (position + size);\n                }\n                if (position < 0) {\n                    output = -position;\n                }\n                return output;\n            },\n            _flip: function (offset, size, anchorSize, viewPortSize, origin, position, boxSize) {\n                var output = 0;\n                boxSize = boxSize || size;\n                if (position !== origin && position !== CENTER && origin !== CENTER) {\n                    if (offset + boxSize > viewPortSize) {\n                        output += -(anchorSize + size);\n                    }\n                    if (offset + output < 0) {\n                        output += anchorSize + size;\n                    }\n                }\n                return output;\n            },\n            _scrollableParents: function () {\n                return $(this.options.anchor).parentsUntil('body').filter(function (index, element) {\n                    return kendo.isScrollable(element);\n                });\n            },\n            _position: function (fixed) {\n                var that = this, element = that.element, wrapper = that.wrapper, options = that.options, viewport = $(options.viewport), zoomLevel = support.zoomLevel(), isWindow = !!(viewport[0] == window && window.innerWidth && zoomLevel <= 1.02), anchor = $(options.anchor), origins = options.origin.toLowerCase().split(' '), positions = options.position.toLowerCase().split(' '), collisions = that.collisions, siblingContainer, parents, parentZIndex, zIndex = 10002, idx = 0, docEl = document.documentElement, length, viewportOffset, viewportWidth, viewportHeight;\n                if (options.viewport === window) {\n                    viewportOffset = {\n                        top: window.pageYOffset || document.documentElement.scrollTop || 0,\n                        left: window.pageXOffset || document.documentElement.scrollLeft || 0\n                    };\n                } else {\n                    viewportOffset = viewport.offset();\n                }\n                if (isWindow) {\n                    viewportWidth = window.innerWidth;\n                    viewportHeight = window.innerHeight;\n                } else {\n                    viewportWidth = viewport.width();\n                    viewportHeight = viewport.height();\n                }\n                if (isWindow && docEl.scrollHeight - docEl.clientHeight > 0) {\n                    var sign = options.isRtl ? -1 : 1;\n                    viewportWidth -= sign * kendo.support.scrollbar();\n                }\n                siblingContainer = anchor.parents().filter(wrapper.siblings());\n                if (siblingContainer[0]) {\n                    parentZIndex = Math.max(Number(siblingContainer.css('zIndex')), 0);\n                    if (parentZIndex) {\n                        zIndex = parentZIndex + 10;\n                    } else {\n                        parents = anchor.parentsUntil(siblingContainer);\n                        for (length = parents.length; idx < length; idx++) {\n                            parentZIndex = Number($(parents[idx]).css('zIndex'));\n                            if (parentZIndex && zIndex < parentZIndex) {\n                                zIndex = parentZIndex + 10;\n                            }\n                        }\n                    }\n                }\n                wrapper.css('zIndex', zIndex);\n                if (fixed && fixed.isFixed) {\n                    wrapper.css({\n                        left: fixed.x,\n                        top: fixed.y\n                    });\n                } else {\n                    wrapper.css(that._align(origins, positions));\n                }\n                var pos = getOffset(wrapper, POSITION, anchor[0] === wrapper.offsetParent()[0]), offset = getOffset(wrapper), anchorParent = anchor.offsetParent().parent('.k-animation-container,.k-popup,.k-group');\n                if (anchorParent.length) {\n                    pos = getOffset(wrapper, POSITION, true);\n                    offset = getOffset(wrapper);\n                }\n                offset.top -= viewportOffset.top;\n                offset.left -= viewportOffset.left;\n                if (!that.wrapper.data(LOCATION)) {\n                    wrapper.data(LOCATION, extend({}, pos));\n                }\n                var offsets = extend({}, offset), location = extend({}, pos), adjustSize = options.adjustSize;\n                if (collisions[0] === 'fit') {\n                    location.top += that._fit(offsets.top, outerHeight(wrapper) + adjustSize.height, viewportHeight / zoomLevel);\n                }\n                if (collisions[1] === 'fit') {\n                    location.left += that._fit(offsets.left, outerWidth(wrapper) + adjustSize.width, viewportWidth / zoomLevel);\n                }\n                var flipPos = extend({}, location);\n                var elementHeight = outerHeight(element);\n                var wrapperHeight = outerHeight(wrapper);\n                if (!wrapper.height() && elementHeight) {\n                    wrapperHeight = wrapperHeight + elementHeight;\n                }\n                if (collisions[0] === 'flip') {\n                    location.top += that._flip(offsets.top, elementHeight, outerHeight(anchor), viewportHeight / zoomLevel, origins[0], positions[0], wrapperHeight);\n                }\n                if (collisions[1] === 'flip') {\n                    location.left += that._flip(offsets.left, outerWidth(element), outerWidth(anchor), viewportWidth / zoomLevel, origins[1], positions[1], outerWidth(wrapper));\n                }\n                element.css(POSITION, ABSOLUTE);\n                wrapper.css(location);\n                return location.left != flipPos.left || location.top != flipPos.top;\n            },\n            _align: function (origin, position) {\n                var that = this, element = that.wrapper, anchor = $(that.options.anchor), verticalOrigin = origin[0], horizontalOrigin = origin[1], verticalPosition = position[0], horizontalPosition = position[1], anchorOffset = getOffset(anchor), appendTo = $(that.options.appendTo), appendToOffset, width = outerWidth(element), height = outerHeight(element) || outerHeight(element.children().first()), anchorWidth = outerWidth(anchor), anchorHeight = outerHeight(anchor), top = anchorOffset.top, left = anchorOffset.left, round = Math.round;\n                if (appendTo[0] != document.body) {\n                    appendToOffset = getOffset(appendTo);\n                    top -= appendToOffset.top;\n                    left -= appendToOffset.left;\n                }\n                if (verticalOrigin === BOTTOM) {\n                    top += anchorHeight;\n                }\n                if (verticalOrigin === CENTER) {\n                    top += round(anchorHeight / 2);\n                }\n                if (verticalPosition === BOTTOM) {\n                    top -= height;\n                }\n                if (verticalPosition === CENTER) {\n                    top -= round(height / 2);\n                }\n                if (horizontalOrigin === RIGHT) {\n                    left += anchorWidth;\n                }\n                if (horizontalOrigin === CENTER) {\n                    left += round(anchorWidth / 2);\n                }\n                if (horizontalPosition === RIGHT) {\n                    left -= width;\n                }\n                if (horizontalPosition === CENTER) {\n                    left -= round(width / 2);\n                }\n                return {\n                    top: top,\n                    left: left\n                };\n            }\n        });\n        ui.plugin(Popup);\n        var stableSort = kendo.support.stableSort;\n        var tabKeyTrapNS = 'kendoTabKeyTrap';\n        var focusableNodesSelector = 'a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, object, embed, [tabindex], *[contenteditable]';\n        var TabKeyTrap = Class.extend({\n            init: function (element) {\n                this.element = $(element);\n                this.element.autoApplyNS(tabKeyTrapNS);\n            },\n            trap: function () {\n                this.element.on('keydown', proxy(this._keepInTrap, this));\n            },\n            removeTrap: function () {\n                this.element.kendoDestroy(tabKeyTrapNS);\n            },\n            destroy: function () {\n                this.element.kendoDestroy(tabKeyTrapNS);\n                this.element = undefined;\n            },\n            shouldTrap: function () {\n                return true;\n            },\n            _keepInTrap: function (e) {\n                if (e.which !== 9 || !this.shouldTrap() || e.isDefaultPrevented()) {\n                    return;\n                }\n                var elements = this._focusableElements();\n                var sortedElements = this._sortFocusableElements(elements);\n                var next = this._nextFocusable(e, sortedElements);\n                this._focus(next);\n                e.preventDefault();\n            },\n            _focusableElements: function () {\n                var elements = this.element.find(focusableNodesSelector).filter(function (i, item) {\n                    return item.tabIndex >= 0 && $(item).is(':visible') && !$(item).is('[disabled]');\n                });\n                if (this.element.is('[tabindex]')) {\n                    elements.push(this.element[0]);\n                }\n                return elements;\n            },\n            _sortFocusableElements: function (elements) {\n                var sortedElements;\n                if (stableSort) {\n                    sortedElements = elements.sort(function (prev, next) {\n                        return prev.tabIndex - next.tabIndex;\n                    });\n                } else {\n                    var attrName = '__k_index';\n                    elements.each(function (i, item) {\n                        item.setAttribute(attrName, i);\n                    });\n                    sortedElements = elements.sort(function (prev, next) {\n                        return prev.tabIndex === next.tabIndex ? parseInt(prev.getAttribute(attrName), 10) - parseInt(next.getAttribute(attrName), 10) : prev.tabIndex - next.tabIndex;\n                    });\n                    elements.removeAttr(attrName);\n                }\n                return sortedElements;\n            },\n            _nextFocusable: function (e, elements) {\n                var count = elements.length;\n                var current = elements.index(e.target);\n                return elements.get((current + (e.shiftKey ? -1 : 1)) % count);\n            },\n            _focus: function (element) {\n                if (element.nodeName == 'IFRAME') {\n                    element.contentWindow.document.body.focus();\n                    return;\n                }\n                element.focus();\n                if (element.nodeName == 'INPUT' && element.setSelectionRange && this._haveSelectionRange(element)) {\n                    element.setSelectionRange(0, element.value.length);\n                }\n            },\n            _haveSelectionRange: function (element) {\n                var elementType = element.type.toLowerCase();\n                return elementType === 'text' || elementType === 'search' || elementType === 'url' || elementType === 'tel' || elementType === 'password';\n            }\n        });\n        ui.Popup.TabKeyTrap = TabKeyTrap;\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}