{"version": 3, "sources": ["kendo.groupable.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "Widget", "ui", "outerWidth", "_outerWidth", "kendoAttr", "attr", "extend", "each", "proxy", "isRtl", "DIR", "FIELD", "TITLE", "ASCENDING", "DESCENDING", "GROUP_SORT", "NS", "CHANGE", "indicatorTmpl", "template", "useWithBlock", "hint", "target", "title", "htmlEncode", "html", "prepend", "dropCue", "Groupable", "init", "element", "options", "draggable", "horizontalCuePosition", "that", "this", "group", "guid", "intializePositions", "_intializePositions", "dropCuePositions", "_dropCuePositions", "fn", "call", "support", "Draggable", "filter", "draggableElements", "groupContainer", "kendoDropTarget", "dragenter", "e", "_canDrag", "currentTarget", "find", "removeClass", "addClass", "css", "appendTo", "dragleave", "remove", "drop", "position", "targetElement", "field", "sourceIndicator", "indicator", "lastCuePosition", "length", "sortOptions", "sort", "data", "dir", "hasClass", "_dropCuePosition", "getOffset", "left", "parseInt", "_canDrop", "before", "buildIndicator", "after", "_setIndicatorSortOptions", "_change", "empty", "append", "kendoDraggable", "dragcancel", "_dragCancel", "dragstart", "marginLeft", "elementPosition", "dragend", "_dragEnd", "drag", "_drag", "on", "preventDefault", "_removeIndicator", "parent", "newDir", "bind", "marginRight", "allowDrag", "dataSource", "_refresh<PERSON><PERSON><PERSON>", "unbind", "refresh", "indicatorHtml", "groups", "fieldAttr", "titleAttr", "index", "compare", "_invalidateGroupContainer", "destroy", "off", "events", "name", "messages", "indicators", "grep", "item", "ns", "replace", "aggregates", "names", "idx", "map", "cell", "aggregate", "member", "split", "push", "toArray", "descriptors", "indicatorSortOptions", "removeData", "trigger", "right", "is", "Math", "ceil", "event", "x", "location", "source", "next", "result", "dropped", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,mBACH,aACA,qBACDD,IACL,WA8UE,MAnUC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAASF,EAAMG,GAAGD,OAAQE,EAAaJ,EAAMK,YAAaC,EAAYN,EAAMO,KAAMC,EAASV,EAAEU,OAAQC,EAAOX,EAAEW,KAAMC,EAAQZ,EAAEY,MAAOC,GAAQ,EAAOC,EAAM,MAAOC,EAAQ,QAASC,EAAQ,QAASC,EAAY,MAAOC,EAAa,OAAQC,EAAa,aAAcC,EAAK,kBAAmBC,EAAS,SAAUC,EAAgBpB,EAAMqB,SAAS,seAAghBC,cAAc,IAAUC,EAAO,SAAUC,GAC35B,GAAIC,GAAQD,EAAOjB,KAAKP,EAAMO,KAAK,SAInC,OAHIkB,KACAA,EAAQzB,EAAM0B,WAAWD,IAEtB3B,EAAE,qDAAqD6B,KAAKF,GAASD,EAAOjB,KAAKP,EAAMO,KAAK,WAAWqB,QAAQ,qDACvHC,EAAU/B,EAAE,sCACfgC,EAAY5B,EAAOM,QACnBuB,KAAM,SAAUC,EAASC,GACrB,GAAmGC,GAAWC,EAA1GC,EAAOC,KAAMC,EAAQtC,EAAMuC,OAAQC,EAAqB9B,EAAM0B,EAAKK,oBAAqBL,GAAyCM,EAAmBN,EAAKO,oBAC7JzC,GAAO0C,GAAGb,KAAKc,KAAKT,EAAMJ,EAASC,GACnCtB,EAAQX,EAAM8C,QAAQnC,MAAMqB,GAC5BG,EAAwBxB,EAAQ,QAAU,OAC1CyB,EAAKF,UAAYA,EAAYE,EAAKH,QAAQC,WAAa,GAAIlC,GAAMG,GAAG4C,UAAUX,EAAKJ,SAC/EgB,OAAQZ,EAAKH,QAAQgB,kBACrB1B,KAAMA,EACNe,MAAOA,IAEXF,EAAKc,eAAiBpD,EAAEsC,EAAKH,QAAQiB,eAAgBd,EAAKJ,SAASmB,iBAC/Db,MAAOJ,EAAUD,QAAQK,MACzBc,UAAW,SAAUC,GACbjB,EAAKkB,SAASD,EAAEnB,UAAUqB,iBAC1BF,EAAEnB,UAAUX,KAAKiC,KAAK,kBAAkBC,YAAY,cAAcC,SAAS,YAC3E7B,EAAQ8B,IAAIxB,EAAuB,GAAGyB,SAASxB,EAAKc,kBAG5DW,UAAW,SAAUR,GACjBA,EAAEnB,UAAUX,KAAKiC,KAAK,kBAAkBC,YAAY,YAAYC,SAAS,cACzE7B,EAAQiC,UAEZC,KAAM,SAAUV,GAAV,GACoSW,GAAlSC,EAAgBZ,EAAEnB,UAAUqB,cAAeW,EAAQD,EAAc1D,KAAKP,EAAMO,KAAK,UAAWkB,EAAQwC,EAAc1D,KAAKP,EAAMO,KAAK,UAAW4D,EAAkB/B,EAAKgC,UAAUF,GAAQxB,EAAmBN,EAAKO,kBAAmB0B,EAAkB3B,EAAiBA,EAAiB4B,OAAS,GAC9RC,EAAc/D,KAAW4B,EAAKH,QAAQuC,KAAMP,EAAcQ,KAAKxD,IAC/DyD,EAAMH,EAAYG,KACjBT,EAAcU,SAAS,sBAAyBvC,EAAKkB,SAASW,MAG/DI,GACAL,EAAW5B,EAAKwC,iBAAiB5E,EAAM6E,UAAUhD,GAASiD,KAAOC,SAASV,EAAgBrC,QAAQ2B,IAAI,cAAe,KAAOhD,KAAa,GAAKoE,SAASV,EAAgBrC,QAAQ2B,IAAI,eAAgB,KAC/LK,GAAY5B,EAAK4C,SAASlF,EAAEqE,GAAkBH,EAAShC,QAASgC,EAASc,QACrEd,EAASiB,OACTjB,EAAShC,QAAQiD,OAAOd,GAAmB/B,EAAK8C,eAAehB,EAAOzC,EAAOiD,IAE7EV,EAAShC,QAAQmD,MAAMhB,GAAmB/B,EAAK8C,eAAehB,EAAOzC,EAAOiD,IAEhFtC,EAAKgD,yBAAyBlB,EAAOK,GACrCnC,EAAKiD,aAGTjD,EAAKc,eAAeoC,QACpBlD,EAAKc,eAAeqC,OAAOnD,EAAK8C,eAAehB,EAAOzC,EAAOiD,IAC7DtC,EAAKgD,yBAAyBlB,EAAOK,GACrCnC,EAAKiD,eAGdG,gBACCxC,OAAQ,wBACRzB,KAAMA,EACNe,MAAOJ,EAAUD,QAAQK,MACzBmD,WAAY/E,EAAM0B,EAAKsD,YAAatD,GACpCuD,UAAW,SAAUtC,GACjB,GAAIrB,GAAUqB,EAAEE,cAAeqC,EAAab,SAAS/C,EAAQ2B,IAAI,cAAe,IAAKkC,EAAkB7D,EAAQgC,WAAYc,EAAOnE,EAAQkF,EAAgBf,KAAOc,EAAaC,EAAgBf,KAAO1E,EAAW4B,EAChNQ,KACAX,EAAQ8B,IAAI,OAAQmB,GAAMlB,SAASxB,EAAKc,gBACxCb,KAAKd,KAAKiC,KAAK,kBAAkBC,YAAY,cAAcC,SAAS,aAExEoC,QAAS,WACL1D,EAAK2D,SAAS1D,OAElB2D,KAAMtF,EAAM0B,EAAK6D,MAAO7D,KACzB8D,GAAG,QAAUhF,EAAI,YAAa,SAAUmC,GACvCA,EAAE8C,iBACF/D,EAAKgE,iBAAiBtG,EAAEuC,MAAMgE,YAC/BH,GAAG,QAAUhF,EAAI,UAAW,SAAUmC,GAAV,GACvBe,GAAYtE,EAAEuC,MAAMgE,SACpBC,EAASlC,EAAU7D,KAAKD,EAAUM,MAAUG,EAAYC,EAAaD,CACzEqD,GAAU7D,KAAKD,EAAUM,GAAM0F,GAC/BlE,EAAKiD,UACLhC,EAAE8C,mBAENjE,EAAUqE,MACN,UACA,aACA,YACA,SAEAT,QAAS,WACL1D,EAAK2D,SAAS1D,OAElBoD,WAAY/E,EAAM0B,EAAKsD,YAAatD,GACpCuD,UAAW,SAAUtC,GACjB,GAAIrB,GAASwE,EAAa1B,CAC1B,OAAK1C,GAAKH,QAAQwE,WAAcrE,EAAKkB,SAASD,EAAEE,gBAIhDf,IACIE,EAAiB4B,QACjBtC,EAAUU,EAAiBA,EAAiB4B,OAAS,GAAGtC,QACxDwE,EAAczB,SAAS/C,EAAQ2B,IAAI,eAAgB,IACnDmB,EAAO9C,EAAQgC,WAAWc,KAAO1E,EAAW4B,GAAWwE,GAEvD1B,EAAO,EANXtC,IAHIa,EAAE8C,iBACF,IAWRH,KAAMtF,EAAM0B,EAAK6D,MAAO7D,KAE5BA,EAAKsE,WAAatE,EAAKH,QAAQyE,WAC3BtE,EAAKsE,YAActE,EAAKuE,gBACxBvE,EAAKsE,WAAWE,OAAOzF,EAAQiB,EAAKuE,iBAEpCvE,EAAKuE,gBAAkBjG,EAAM0B,EAAKyE,QAASzE,GAE3CA,EAAKsE,aACLtE,EAAKsE,WAAWH,KAAK,SAAUnE,EAAKuE,iBACpCvE,EAAKyE,YAGbA,QAAS,WAAA,GAKDC,GAJA1E,EAAOC,KAAMqE,EAAatE,EAAKsE,WAC/BK,EAASL,EAAWpE,YACpB0E,EAAY1G,EAAUO,GACtBoG,EAAY3G,EAAUQ,EAEtBsB,GAAKc,iBACLd,EAAKc,eAAeoC,QACpB7E,EAAKsG,EAAQ,SAAUG,EAAO5E,GAAjB,GACL4B,GAAQ5B,EAAM4B,MACdQ,EAAMpC,EAAMoC,IACZ1C,EAAUI,EAAKJ,QAAQwB,KAAKpB,EAAKH,QAAQe,QAAQA,OAAO,WACxD,MAAOlD,GAAEuC,MAAM9B,KAAKyG,KAAe9C,GAEvC4C,GAAgB1E,EAAK8C,eAAehB,EAAOlC,EAAQzB,KAAK0G,GAAYvC,GACpEtC,EAAKc,eAAeqC,OAAOuB,GAC3B1E,EAAKgD,yBAAyBlB,EAAO1D,KAAW4B,EAAKH,QAAQuC,MACzDE,IAAKA,EACLyC,QAAS7E,EAAM6E,cAI3B/E,EAAKgF,6BAETC,QAAS,WACL,GAAIjF,GAAOC,IACXnC,GAAO0C,GAAGyE,QAAQxE,KAAKT,GACvBA,EAAKc,eAAeoE,IAAIpG,GACpBkB,EAAKc,eAAeuB,KAAK,oBACzBrC,EAAKc,eAAeuB,KAAK,mBAAmB4C,UAE5CjF,EAAKc,eAAeuB,KAAK,mBACzBrC,EAAKc,eAAeuB,KAAK,kBAAkB4C,UAE1CjF,EAAKH,QAAQC,WACdE,EAAKF,UAAUmF,UAEfjF,EAAKsE,YAActE,EAAKuE,kBACxBvE,EAAKsE,WAAWE,OAAO,SAAUxE,EAAKuE,iBACtCvE,EAAKuE,gBAAkB,MAE3BvE,EAAKc,eAAiBd,EAAKJ,QAAUI,EAAKF,UAAY,MAE1DqF,QAAS,UACTtF,SACIuF,KAAM,YACNxE,OAAQ,KACRC,kBAAmB,KACnBwE,UAAYnC,MAAO,iEACnBd,MACIE,IAAK3D,EACLoG,QAAS,OAGjB/C,UAAW,SAAUF,GACjB,GAAIwD,GAAa5H,EAAE,qBAAsBuC,KAAKa,eAC9C,OAAOpD,GAAE6H,KAAKD,EAAY,SAAUE,GAChC,MAAO9H,GAAE8H,GAAMrH,KAAKP,EAAMO,KAAK,YAAc2D,IAC9C,IAEPgB,eAAgB,SAAUhB,EAAOzC,EAAOiD,GAAxB,GACRtC,GAAOC,KACP+B,EAAYhD,GACZyG,GAAI7H,EAAM6H,GACV3D,MAAOA,EAAM4D,QAAQ,KAAM,KAC3BrG,MAAOA,EACPiD,IAAKA,IAAQtC,EAAKH,QAAQuC,UAAYE,KAAO3D,GAEjD,OAAOqD,IAEXgB,yBAA0B,SAAUlB,EAAOjC,GACvC,GAAImC,GAAYtE,EAAEuC,KAAK+B,UAAUF,GACjCE,GAAUK,KAAKxD,EAAYgB,IAE/B8F,WAAY,WAAA,GAEJC,GACAC,EACA3D,EAHAlC,EAAOC,IAIX,OAAOD,GAAKJ,QAAQwB,KAAKpB,EAAKH,QAAQe,QAAQkF,IAAI,WAC9C,GAAIC,GAAOrI,EAAEuC,MAAO+F,EAAYD,EAAK5H,KAAKP,EAAMO,KAAK,eAAgB8H,EAASF,EAAK5H,KAAKP,EAAMO,KAAK,SACnG,IAAI6H,GAA2B,KAAdA,EAGb,IAFAJ,EAAQI,EAAUE,MAAM,KACxBF,KACKH,EAAM,EAAG3D,EAAS0D,EAAM1D,OAAQ2D,EAAM3D,EAAQ2D,IAC/CG,EAAUG,MACNrE,MAAOmE,EACPD,UAAWJ,EAAMC,IAI7B,OAAOG,KACRI,WAEPC,YAAa,WACT,GAA4EvE,GAAxE9B,EAAOC,KAAMqF,EAAa5H,EAAE,qBAAsBsC,EAAKc,gBAAwB6E,EAAa3F,EAAK2F,YACrG,OAAOjI,GAAEoI,IAAIR,EAAY,SAAUE,GAAV,GAGjBrD,GACAmE,CACJ,OAJAd,GAAO9H,EAAE8H,GACT1D,EAAQ0D,EAAKrH,KAAKP,EAAMO,KAAK,UACzBgE,EAAcnC,EAAKH,QAAQuC,SAC3BkE,EAAuBd,EAAKnD,KAAKxD,QAEjCiD,MAAOA,EACPQ,IAAKkD,EAAKrH,KAAKP,EAAMO,KAAK,QAC1BwH,WAAYA,MACZZ,QAASuB,EAAqBvB,SAAW5C,EAAY4C,YAIjEf,iBAAkB,SAAUhC,GACxB,GAAIhC,GAAOC,IACX+B,GAAUkD,MACVlD,EAAUuE,aACVvE,EAAUN,SACV1B,EAAKgF,4BACLhF,EAAKiD,WAETA,QAAS,WAAA,GAGGoD,GAFJrG,EAAOC,IACX,IAAID,EAAKsE,WAAY,CAEjB,GADI+B,EAAcrG,EAAKqG,cACnBrG,EAAKwG,QAAQ,UAAY7B,OAAQ0B,IAEjC,MADArG,GAAKyE,UACL,CAEJzE,GAAKsE,WAAWpE,MAAMmG,KAG9B7D,iBAAkB,SAAUZ,GAAV,GAMVK,GAAiES,EAA6B+D,EAA+BjD,EAAsEY,EALnM9D,EAAmBL,KAAKM,iBAC5B,IAAKd,EAAQiH,GAAG,aAA2C,IAA5BpG,EAAiB4B,OAuBhD,MApBAN,GAAW+E,KAAKC,KAAKhF,GACjBK,EAAkB3B,EAAiBA,EAAiB4B,OAAS,GAAIQ,EAAOT,EAAgBS,KAAM+D,EAAQxE,EAAgBwE,MAAOjD,EAAab,SAASV,EAAgBrC,QAAQ2B,IAAI,cAAe,IAAK6C,EAAczB,SAASV,EAAgBrC,QAAQ2B,IAAI,eAAgB,IACtQK,GAAY6E,IAAUlI,GAASqD,EAAWc,GAAQnE,EAClDqD,GACIc,KAAMT,EAAgBrC,QAAQgC,WAAWc,MAASnE,GAA6DiF,EAArDxF,EAAWiE,EAAgBrC,SAAWwE,GAChGxE,QAASqC,EAAgBrC,QACzBiD,QAAQ,IAGZjB,EAAWlE,EAAE6H,KAAKjF,EAAkB,SAAUkF,GAC1C,MAAOA,GAAK9C,MAAQd,GAAYA,GAAY4D,EAAKiB,OAASlI,GAASqD,EAAW4D,EAAKiB,QACpF,GACC7E,IACAA,GACIc,KAAMnE,EAAQqD,EAAShC,QAAQgC,WAAWc,KAAO1E,EAAW4D,EAAShC,SAAWwE,EAAcxC,EAAShC,QAAQgC,WAAWc,KAAOc,EACjI5D,QAASgC,EAAShC,QAClBiD,QAAQ,KAIbjB,GAEXiC,MAAO,SAAUgD,GACb,GAAIjF,GAAW3B,KAAKuC,iBAAiBqE,EAAMC,EAAEC,SACzCnF,IACAnC,EAAQ8B,KACJmB,KAAMd,EAASc,KACf+D,MAAO,UAInBvF,SAAU,SAAUtB,GAChB,GAAIkC,GAAQlC,EAAQzB,KAAKP,EAAMO,KAAK,SACpC,OAAgD,SAAzCyB,EAAQzB,KAAKP,EAAMO,KAAK,eAA4B2D,IAAUlC,EAAQ2C,SAAS,uBAAyBtC,KAAK+B,UAAUF,KAElIc,SAAU,SAAUoE,EAAQ5H,EAAQwC,GAChC,GAAIqF,GAAOD,EAAOC,OAAQC,EAASF,EAAO,KAAO5H,EAAO,MAAQ6H,EAAK,IAAM7H,EAAO,KAAO6H,EAAK,KAAQ1I,GAASqD,EAAWqF,EAAKrF,WAAWc,MAAQnE,GAASqD,EAAWqF,EAAKrF,WAAWc,KACtL,OAAOwE,IAEXvD,SAAU,SAAU7D,GAChB,GAAIE,GAAOC,KAAM6B,EAAQhC,EAAUqB,cAAchD,KAAKP,EAAMO,KAAK,UAAW4D,EAAkB/B,EAAKgC,UAAUF,EACzGhC,KAAcE,EAAKH,QAAQC,YAAcA,EAAUqH,SAAWpF,GAC9D/B,EAAKgE,iBAAiBtG,EAAEqE,IAE5B/B,EAAKsD,eAETA,YAAa,WACT7D,EAAQiC,SACRzB,KAAKM,sBAETF,oBAAqB,WACjB,GAA4EqC,GAAxE1C,EAAOC,KAAMqF,EAAa5H,EAAE,qBAAsBsC,EAAKc,eAC3Dd,GAAKO,kBAAoB7C,EAAEoI,IAAIR,EAAY,SAAUE,GAGjD,MAFAA,GAAO9H,EAAE8H,GACT9C,EAAO9E,EAAM6E,UAAU+C,GAAM9C,MAEzBA,KAAMC,SAASD,EAAM,IACrB+D,MAAO9D,SAASD,EAAO1E,EAAWwH,GAAO,IACzC5F,QAAS4F,MAIrBR,0BAA2B,WACvB,GAAIlE,GAAiBb,KAAKa,cACtBA,IAAkBA,EAAe4F,GAAG,WACpC5F,EAAevB,KAAKU,KAAKJ,QAAQwF,SAASnC,SAItDtF,GAAMG,GAAGqJ,OAAO1H,IAClB7B,OAAOD,MAAMyJ,QACRxJ,OAAOD,OACE,kBAAVH,SAAwBA,OAAO6J,IAAM7J,OAAS,SAAU8J,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.groupable.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.groupable', [\n        'kendo.core',\n        'kendo.draganddrop'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'groupable',\n        name: 'Groupable',\n        category: 'framework',\n        depends: [\n            'core',\n            'draganddrop'\n        ],\n        advanced: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, outerWidth = kendo._outerWidth, kendoAttr = kendo.attr, extend = $.extend, each = $.each, proxy = $.proxy, isRtl = false, DIR = 'dir', FIELD = 'field', TITLE = 'title', ASCENDING = 'asc', DESCENDING = 'desc', GROUP_SORT = 'group-sort', NS = '.kendoGroupable', CHANGE = 'change', indicatorTmpl = kendo.template('<div class=\"k-group-indicator\" data-#=data.ns#field=\"${data.field}\" data-#=data.ns#title=\"${data.title || \"\"}\" data-#=data.ns#dir=\"${data.dir || \"asc\"}\">' + '<a href=\"\\\\#\" class=\"k-link\">' + '<span class=\"k-icon k-i-sort-${(data.dir || \"asc\") == \"asc\" ? \"asc-sm\" : \"desc-sm\"}\" title=\"(sorted ${(data.dir || \"asc\") == \"asc\" ? \"ascending\": \"descending\"})\"></span>' + '${data.title ? data.title: data.field}' + '</a>' + '<a class=\"k-button k-button-icon k-bare\">' + '<span class=\"k-icon k-i-close\"></span>' + '</a>' + '</div>', { useWithBlock: false }), hint = function (target) {\n                var title = target.attr(kendo.attr('title'));\n                if (title) {\n                    title = kendo.htmlEncode(title);\n                }\n                return $('<div class=\"k-header k-group-clue k-drag-clue\" />').html(title || target.attr(kendo.attr('field'))).prepend('<span class=\"k-icon k-drag-status k-i-cancel\" />');\n            }, dropCue = $('<div class=\"k-grouping-dropclue\"/>');\n        var Groupable = Widget.extend({\n            init: function (element, options) {\n                var that = this, group = kendo.guid(), intializePositions = proxy(that._intializePositions, that), draggable, horizontalCuePosition, dropCuePositions = that._dropCuePositions = [];\n                Widget.fn.init.call(that, element, options);\n                isRtl = kendo.support.isRtl(element);\n                horizontalCuePosition = isRtl ? 'right' : 'left';\n                that.draggable = draggable = that.options.draggable || new kendo.ui.Draggable(that.element, {\n                    filter: that.options.draggableElements,\n                    hint: hint,\n                    group: group\n                });\n                that.groupContainer = $(that.options.groupContainer, that.element).kendoDropTarget({\n                    group: draggable.options.group,\n                    dragenter: function (e) {\n                        if (that._canDrag(e.draggable.currentTarget)) {\n                            e.draggable.hint.find('.k-drag-status').removeClass('k-i-cancel').addClass('k-i-plus');\n                            dropCue.css(horizontalCuePosition, 0).appendTo(that.groupContainer);\n                        }\n                    },\n                    dragleave: function (e) {\n                        e.draggable.hint.find('.k-drag-status').removeClass('k-i-plus').addClass('k-i-cancel');\n                        dropCue.remove();\n                    },\n                    drop: function (e) {\n                        var targetElement = e.draggable.currentTarget, field = targetElement.attr(kendo.attr('field')), title = targetElement.attr(kendo.attr('title')), sourceIndicator = that.indicator(field), dropCuePositions = that._dropCuePositions, lastCuePosition = dropCuePositions[dropCuePositions.length - 1], position;\n                        var sortOptions = extend({}, that.options.sort, targetElement.data(GROUP_SORT));\n                        var dir = sortOptions.dir;\n                        if (!targetElement.hasClass('k-group-indicator') && !that._canDrag(targetElement)) {\n                            return;\n                        }\n                        if (lastCuePosition) {\n                            position = that._dropCuePosition(kendo.getOffset(dropCue).left + parseInt(lastCuePosition.element.css('marginLeft'), 10) * (isRtl ? -1 : 1) + parseInt(lastCuePosition.element.css('marginRight'), 10));\n                            if (position && that._canDrop($(sourceIndicator), position.element, position.left)) {\n                                if (position.before) {\n                                    position.element.before(sourceIndicator || that.buildIndicator(field, title, dir));\n                                } else {\n                                    position.element.after(sourceIndicator || that.buildIndicator(field, title, dir));\n                                }\n                                that._setIndicatorSortOptions(field, sortOptions);\n                                that._change();\n                            }\n                        } else {\n                            that.groupContainer.empty();\n                            that.groupContainer.append(that.buildIndicator(field, title, dir));\n                            that._setIndicatorSortOptions(field, sortOptions);\n                            that._change();\n                        }\n                    }\n                }).kendoDraggable({\n                    filter: 'div.k-group-indicator',\n                    hint: hint,\n                    group: draggable.options.group,\n                    dragcancel: proxy(that._dragCancel, that),\n                    dragstart: function (e) {\n                        var element = e.currentTarget, marginLeft = parseInt(element.css('marginLeft'), 10), elementPosition = element.position(), left = isRtl ? elementPosition.left - marginLeft : elementPosition.left + outerWidth(element);\n                        intializePositions();\n                        dropCue.css('left', left).appendTo(that.groupContainer);\n                        this.hint.find('.k-drag-status').removeClass('k-i-cancel').addClass('k-i-plus');\n                    },\n                    dragend: function () {\n                        that._dragEnd(this);\n                    },\n                    drag: proxy(that._drag, that)\n                }).on('click' + NS, '.k-button', function (e) {\n                    e.preventDefault();\n                    that._removeIndicator($(this).parent());\n                }).on('click' + NS, '.k-link', function (e) {\n                    var indicator = $(this).parent();\n                    var newDir = indicator.attr(kendoAttr(DIR)) === ASCENDING ? DESCENDING : ASCENDING;\n                    indicator.attr(kendoAttr(DIR), newDir);\n                    that._change();\n                    e.preventDefault();\n                });\n                draggable.bind([\n                    'dragend',\n                    'dragcancel',\n                    'dragstart',\n                    'drag'\n                ], {\n                    dragend: function () {\n                        that._dragEnd(this);\n                    },\n                    dragcancel: proxy(that._dragCancel, that),\n                    dragstart: function (e) {\n                        var element, marginRight, left;\n                        if (!that.options.allowDrag && !that._canDrag(e.currentTarget)) {\n                            e.preventDefault();\n                            return;\n                        }\n                        intializePositions();\n                        if (dropCuePositions.length) {\n                            element = dropCuePositions[dropCuePositions.length - 1].element;\n                            marginRight = parseInt(element.css('marginRight'), 10);\n                            left = element.position().left + outerWidth(element) + marginRight;\n                        } else {\n                            left = 0;\n                        }\n                    },\n                    drag: proxy(that._drag, that)\n                });\n                that.dataSource = that.options.dataSource;\n                if (that.dataSource && that._refreshHandler) {\n                    that.dataSource.unbind(CHANGE, that._refreshHandler);\n                } else {\n                    that._refreshHandler = proxy(that.refresh, that);\n                }\n                if (that.dataSource) {\n                    that.dataSource.bind('change', that._refreshHandler);\n                    that.refresh();\n                }\n            },\n            refresh: function () {\n                var that = this, dataSource = that.dataSource;\n                var groups = dataSource.group() || [];\n                var fieldAttr = kendoAttr(FIELD);\n                var titleAttr = kendoAttr(TITLE);\n                var indicatorHtml;\n                if (that.groupContainer) {\n                    that.groupContainer.empty();\n                    each(groups, function (index, group) {\n                        var field = group.field;\n                        var dir = group.dir;\n                        var element = that.element.find(that.options.filter).filter(function () {\n                            return $(this).attr(fieldAttr) === field;\n                        });\n                        indicatorHtml = that.buildIndicator(field, element.attr(titleAttr), dir);\n                        that.groupContainer.append(indicatorHtml);\n                        that._setIndicatorSortOptions(field, extend({}, that.options.sort, {\n                            dir: dir,\n                            compare: group.compare\n                        }));\n                    });\n                }\n                that._invalidateGroupContainer();\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that.groupContainer.off(NS);\n                if (that.groupContainer.data('kendoDropTarget')) {\n                    that.groupContainer.data('kendoDropTarget').destroy();\n                }\n                if (that.groupContainer.data('kendoDraggable')) {\n                    that.groupContainer.data('kendoDraggable').destroy();\n                }\n                if (!that.options.draggable) {\n                    that.draggable.destroy();\n                }\n                if (that.dataSource && that._refreshHandler) {\n                    that.dataSource.unbind('change', that._refreshHandler);\n                    that._refreshHandler = null;\n                }\n                that.groupContainer = that.element = that.draggable = null;\n            },\n            events: ['change'],\n            options: {\n                name: 'Groupable',\n                filter: 'th',\n                draggableElements: 'th',\n                messages: { empty: 'Drag a column header and drop it here to group by that column' },\n                sort: {\n                    dir: ASCENDING,\n                    compare: null\n                }\n            },\n            indicator: function (field) {\n                var indicators = $('.k-group-indicator', this.groupContainer);\n                return $.grep(indicators, function (item) {\n                    return $(item).attr(kendo.attr('field')) === field;\n                })[0];\n            },\n            buildIndicator: function (field, title, dir) {\n                var that = this;\n                var indicator = indicatorTmpl({\n                    ns: kendo.ns,\n                    field: field.replace(/\"/g, '\\''),\n                    title: title,\n                    dir: dir || (that.options.sort || {}).dir || ASCENDING\n                });\n                return indicator;\n            },\n            _setIndicatorSortOptions: function (field, options) {\n                var indicator = $(this.indicator(field));\n                indicator.data(GROUP_SORT, options);\n            },\n            aggregates: function () {\n                var that = this;\n                var names;\n                var idx;\n                var length;\n                return that.element.find(that.options.filter).map(function () {\n                    var cell = $(this), aggregate = cell.attr(kendo.attr('aggregates')), member = cell.attr(kendo.attr('field'));\n                    if (aggregate && aggregate !== '') {\n                        names = aggregate.split(',');\n                        aggregate = [];\n                        for (idx = 0, length = names.length; idx < length; idx++) {\n                            aggregate.push({\n                                field: member,\n                                aggregate: names[idx]\n                            });\n                        }\n                    }\n                    return aggregate;\n                }).toArray();\n            },\n            descriptors: function () {\n                var that = this, indicators = $('.k-group-indicator', that.groupContainer), field, aggregates = that.aggregates();\n                return $.map(indicators, function (item) {\n                    item = $(item);\n                    field = item.attr(kendo.attr('field'));\n                    var sortOptions = that.options.sort || {};\n                    var indicatorSortOptions = item.data(GROUP_SORT) || {};\n                    return {\n                        field: field,\n                        dir: item.attr(kendo.attr('dir')),\n                        aggregates: aggregates || [],\n                        compare: indicatorSortOptions.compare || sortOptions.compare\n                    };\n                });\n            },\n            _removeIndicator: function (indicator) {\n                var that = this;\n                indicator.off();\n                indicator.removeData();\n                indicator.remove();\n                that._invalidateGroupContainer();\n                that._change();\n            },\n            _change: function () {\n                var that = this;\n                if (that.dataSource) {\n                    var descriptors = that.descriptors();\n                    if (that.trigger('change', { groups: descriptors })) {\n                        that.refresh();\n                        return;\n                    }\n                    that.dataSource.group(descriptors);\n                }\n            },\n            _dropCuePosition: function (position) {\n                var dropCuePositions = this._dropCuePositions;\n                if (!dropCue.is(':visible') || dropCuePositions.length === 0) {\n                    return;\n                }\n                position = Math.ceil(position);\n                var lastCuePosition = dropCuePositions[dropCuePositions.length - 1], left = lastCuePosition.left, right = lastCuePosition.right, marginLeft = parseInt(lastCuePosition.element.css('marginLeft'), 10), marginRight = parseInt(lastCuePosition.element.css('marginRight'), 10);\n                if (position >= right && !isRtl || position < left && isRtl) {\n                    position = {\n                        left: lastCuePosition.element.position().left + (!isRtl ? outerWidth(lastCuePosition.element) + marginRight : -marginLeft),\n                        element: lastCuePosition.element,\n                        before: false\n                    };\n                } else {\n                    position = $.grep(dropCuePositions, function (item) {\n                        return item.left <= position && position <= item.right || isRtl && position > item.right;\n                    })[0];\n                    if (position) {\n                        position = {\n                            left: isRtl ? position.element.position().left + outerWidth(position.element) + marginRight : position.element.position().left - marginLeft,\n                            element: position.element,\n                            before: true\n                        };\n                    }\n                }\n                return position;\n            },\n            _drag: function (event) {\n                var position = this._dropCuePosition(event.x.location);\n                if (position) {\n                    dropCue.css({\n                        left: position.left,\n                        right: 'auto'\n                    });\n                }\n            },\n            _canDrag: function (element) {\n                var field = element.attr(kendo.attr('field'));\n                return element.attr(kendo.attr('groupable')) != 'false' && field && (element.hasClass('k-group-indicator') || !this.indicator(field));\n            },\n            _canDrop: function (source, target, position) {\n                var next = source.next(), result = source[0] !== target[0] && (!next[0] || target[0] !== next[0] || (!isRtl && position > next.position().left || isRtl && position < next.position().left));\n                return result;\n            },\n            _dragEnd: function (draggable) {\n                var that = this, field = draggable.currentTarget.attr(kendo.attr('field')), sourceIndicator = that.indicator(field);\n                if (draggable !== that.options.draggable && !draggable.dropped && sourceIndicator) {\n                    that._removeIndicator($(sourceIndicator));\n                }\n                that._dragCancel();\n            },\n            _dragCancel: function () {\n                dropCue.remove();\n                this._dropCuePositions = [];\n            },\n            _intializePositions: function () {\n                var that = this, indicators = $('.k-group-indicator', that.groupContainer), left;\n                that._dropCuePositions = $.map(indicators, function (item) {\n                    item = $(item);\n                    left = kendo.getOffset(item).left;\n                    return {\n                        left: parseInt(left, 10),\n                        right: parseInt(left + outerWidth(item), 10),\n                        element: item\n                    };\n                });\n            },\n            _invalidateGroupContainer: function () {\n                var groupContainer = this.groupContainer;\n                if (groupContainer && groupContainer.is(':empty')) {\n                    groupContainer.html(this.options.messages.empty);\n                }\n            }\n        });\n        kendo.ui.plugin(Groupable);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}