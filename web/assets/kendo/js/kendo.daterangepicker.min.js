/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.daterangepicker.min",["kendo.core.min","kendo.multiviewcalendar.min","kendo.datepicker.min"],e)}(function(){return function(e,t){function n(e){e.preventDefault()}var a,r=window.kendo,i=r.ui,s=r.keys,o=i.Widget,l="month",d="open",p="close",u="change",c="<div />",_="min",m="max",f=r.template,g=e.extend,h="id",b=r.support,v=b.mobileOS,w="k-state-selected",D="aria-expanded",I="aria-disabled",k="k-state-disabled",y="disabled",x="readonly",C="k-state-default",A="aria-hidden",V=".kendoDateRangePicker",T="click"+V,O="mousedown"+V,H=b.mouseAndTouchPresent?r.applyEventMap("up",V.slice(1)):T,R=e.proxy,F=r.parseDate,N=function(e){r.DateView.call(this,e)};N.prototype=Object.create(r.DateView.prototype),N.prototype._calendar=function(){var t,a=this,s=a.calendar,o=a.options;s||(t=e(c).attr(h,r.guid()).appendTo(a.popup.element).on(O,n).on(T,"td:has(.k-link)",R(a._click,a)),a.calendar=s=new i.MultiViewCalendar(t),a._setOptions(o),r.calendar.makeUnselectable(s.element),s.navigate(a._value||a._current,o.start),a.calendar.selectRange(a._range||o.range||{}))},N.prototype._setOptions=function(e){this.calendar.setOptions({focusOnNav:!1,change:e.change,culture:e.culture,dates:e.dates,depth:e.depth,footer:e.footer,format:e.format,selectable:e.selectable,max:e.max,min:e.min,month:e.month,weekNumber:e.weekNumber,start:e.start,disableDates:e.disableDates,range:e.range})},N.prototype.range=function(e){this._range=e,this.calendar&&(e.start||e.end?this.calendar.selectRange(e):this.calendar.rangeSelectable.clear())},N.prototype.move=function(e){var t=this,n=e.keyCode,a=t.calendar,r=e.ctrlKey&&n==s.DOWN||n==s.ENTER,i=!1;if(e.altKey)n==s.DOWN?(t.open(),e.preventDefault(),i=!0):n==s.UP&&(t.close(),e.preventDefault(),i=!0);else if(t.popup.visible()){if(n==s.ESC||r&&a._cell.hasClass(w))return t.close(),e.preventDefault(),!0;t._current=a._move(e,!0),i=!0}return i},N.prototype._click=function(e){v.ios||v.android&&"firefox"==v.browser?this._range&&this._range.end&&this.close():this._range&&null===this._range.end&&e.currentTarget.className.indexOf("k-state-selected")!==-1&&this.close()},r.DateRangeView=N,a=o.extend({init:function(e,t){var n,a,i=this;o.fn.init.call(i,e,t),e=i.element,t=i.options,t.disableDates=r.calendar.disabled(t.disableDates),t.min=F(e.attr("min"))||F(t.min),t.max=F(e.attr("max"))||F(t.max),i._initialOptions=g({},t),i._buildHTML(),i._range=i.options.range,i.dateView=new N(g({},t,{id:e.attr(h),anchor:i.wrapper,views:2,selectable:"range",range:i._range,change:function(){var e=this.selectRange();i.range(e),i.trigger(u),i._startDateInput.trigger(u),i._endDateInput.trigger(u)},close:function(e){i.trigger(p)?e.preventDefault():(i.wrapper.attr(D,!1),n.attr(A,!0))},open:function(e){i.trigger(d)?e.preventDefault():(i.wrapper.attr(D,!0),n.attr(A,!1),i._updateARIA())}})),n=i.dateView.div,i._ariaTemplate=f(this.options.ARIATemplate),i._reset(),i.wrapper.attr({role:"combobox","aria-expanded":!1,"aria-owns":i.dateView._dateViewID,autocomplete:"off"}),i._inputs.on(H+V,R(i._click,i)).on("keydown"+V,R(i._keydown,i)),i._initializeDateInputs(),a=e.is("[disabled]"),a?i.enable(!1):i.readonly(e.is("[readonly]"))},options:{name:"DateRangePicker",labels:!0,footer:"",format:"",culture:"",min:new Date(1900,0,1),max:new Date(2099,11,31),start:l,depth:l,animation:{},month:{},startField:"",endField:"",dates:[],disableDates:null,range:null,ARIATemplate:'Current focused date is #=kendo.toString(data.current, "D")#',weekNumber:!1,messages:{startLabel:"Start",endLabel:"End"}},events:[d,p,u],setOptions:function(e){var t=this;o.fn.setOptions.call(t,e),e=t.options,e.min=F(e.min),e.max=F(e.max),t._inputs.off(V),this._initializeDateInputs(),t.dateView.setOptions(e),t._range=e.range},_click:function(){var e=this;e._preventInputAction||e.dateView.popup.visible()||e.dateView.open()},_keydown:function(e){var n=this,a=n.dateView,r=!1;return n._preventInputAction?(e.stopImmediatePropagation(),t):(r=a.move(e),n._updateARIA(a._current),r&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),t)},_updateARIA:function(e){var t,n=this,a=n.dateView.calendar;n.element&&n.element.length&&n.element[0].removeAttribute("aria-activedescendant"),a&&(e&&!a._dateInViews(e)&&a.navigate(e),t=a._cellByDate(e||a.current()),a._focusCell(t),t.attr("aria-label",n._ariaTemplate({current:e||a.current()})),n.element.attr("aria-activedescendant",t.attr("id")))},_startChange:function(e){var t=this,n=e.sender,a=n.value(),r=t._endDateInput.value();t.options.disableDates(a)&&(e.sender.value(null),a=null),t.range({start:a,end:r}),t.trigger(u)},_endChange:function(e){var t=this,n=e.sender,a=n.value(),r=t._startDateInput.value();t.options.disableDates(a)&&(e.sender.value(null),a=null),t.range({start:r,end:a}),t.trigger(u)},_initializeDateInputs:function(){var e=this,t=e.options,n=t.range||{},a={footer:t.footer,format:t.format,culture:t.culture,min:t.min,max:t.max,start:t.start,startField:t.startField,endField:t.endField,depth:t.depth,animation:t.animation,month:t.month,dates:t.dates,disableDates:t.disableDates,ARIATemplate:t.ARIATemplate,weekNumber:t.weekNumber};e._startDateInput&&(e._startDateInput.destroy(),e._endDateInput.destroy(),e.wrapper.empty(),e._buildHTML(),e._inputs.on(H+V,R(e._click,e)).on("keydown"+V,R(e._keydown,e))),e._startDateInput=e._startInput.kendoDateInput(g(!0,a,{value:n.start})).getKendoDateInput(),e._endDateInput=e._endInput.kendoDateInput(g(!0,a,{value:n.end})).getKendoDateInput(),e._startChangeHandler=R(e._startChange,e),e._startDateInput.bind(u,e._startChangeHandler),e._endChangeHandler=R(e._endChange,e),e._endDateInput.bind(u,e._endChangeHandler)},_buildHTML:function(){var t=this,n=t.element;t.wrapper||(t.wrapper=n.addClass("k-widget k-daterangepicker")),t.options.labels?(e('<span class="k-textbox-container"><input/><label class="k-label">'+t.options.messages.startLabel+"</label></span>").appendTo(t.wrapper),e('<span>&nbsp;</span><span class="k-textbox-container"><input/><label class="k-label">'+t.options.messages.endLabel+"</label></span>").appendTo(t.wrapper)):e("<input/><span>&nbsp;</span><input/>").appendTo(t.wrapper),t._startInput=t.wrapper.find("input").eq(0),t._endInput=t.wrapper.find("input").eq(1),""!==t.options.startField&&(t._startInput.attr(r.attr("bind"),"value: "+t.options.startField),t._startInput.attr("name",t.options.startField)),""!==t.options.endField&&(t._endInput.attr(r.attr("bind"),"value: "+t.options.endField),t._endInput.attr("name",t.options.endField)),t._inputs=t._startInput.add(t._endInput)},_option:function(e,n){var a=this,r=a.options;return n===t?r[e]:(n=F(n,r.parseFormats,r.culture),n&&(r[e]=new Date((+n)),a.dateView[e](n)),t)},_reset:function(){var t=this,n=t.element,a=n.attr("form"),r=a?e("#"+a):n.closest("form");r[0]&&(t._resetHandler=function(){t.max(t._initialOptions.max),t.min(t._initialOptions.min)},t._form=r.on("reset",t._resetHandler))},_editable:function(t){var n=this,a=n._inputs,r=t.readonly,i=t.disable;r||i?(n.wrapper.addClass(i?k:C).removeClass(i?C:k),a.attr(y,i).attr(x,r).attr(I,i),n._preventInputAction=!0):(n.wrapper.addClass(C).removeClass(k),e.each(a,function(e,t){t.removeAttribute(y),t.removeAttribute(x)}),a.attr(I,!1),n._preventInputAction=!1)},destroy:function(){var e=this;e._startDateInput&&(e._startDateInput.unbind(u,e._startChangeHandler),e._startDateInput.destroy(),e._startChangeHandler=null),e._endDateInput&&(e._endDateInput.unbind(u,e._endChangeHandler),e._endDateInput.destroy(),e._endChangeHandler=null),e._form&&e._form.off("reset",e._resetHandler),e._inputs.off(V),e._inputs=null,e.dateView.destroy(),e.element.off(V),o.fn.destroy.call(e)},range:function(e){var n=this;return e===t?n._range:(n._range=e,n.dateView.range({start:null,end:null}),e||(n._startDateInput.value(null),n._endDateInput.value(null)),n._startDateInput.value(e.start?e.start:null),n._endDateInput.value(e.end?e.end:null),e.start&&!e.end?n.dateView.range({start:e.start,end:null}):e.start&&e.start&&+e.start<=+e.end&&n.dateView.range({start:e.start,end:e.end}),t)},open:function(){this.dateView.open()},close:function(){this.dateView.close()},min:function(e){return this._option(_,e)},max:function(e){return this._option(m,e)},readonly:function(e){this._startDateInput.readonly(e),this._endDateInput.readonly(e),this._editable({readonly:e===t||e,disable:!1})},enable:function(e){this._startDateInput.enable(e),this._endDateInput.enable(e),e||this.close(),this._editable({readonly:!1,disable:!(e=e===t||e)})}}),r.ui.plugin(a)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.daterangepicker.min.js.map
