/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("util/text-metrics.min",["kendo.core.min"],t)}(function(){!function(t){function e(t){return(t+"").replace(a,h)}function i(t){var e,i=[];for(e in t)i.push(e+t[e]);return i.sort().join("")}function n(t){var e,i=2166136261;for(e=0;e<t.length;++e)i+=(i<<1)+(i<<4)+(i<<7)+(i<<8)+(i<<24),i^=t.charCodeAt(e);return i>>>0}function o(){return{width:0,height:0,baseline:0}}function s(t,e,i){return l.current.measure(t,e,i)}var r,a,h,c,d,l;window.kendo.util=window.kendo.util||{},r=kendo.Class.extend({init:function(t){this._size=t,this._length=0,this._map={}},put:function(t,e){var i=this._map,n={key:t,value:e};i[t]=n,this._head?(this._tail.newer=n,n.older=this._tail,this._tail=n):this._head=this._tail=n,this._length>=this._size?(i[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(t){var e=this._map[t];if(e)return e===this._head&&e!==this._tail&&(this._head=e.newer,this._head.older=null),e!==this._tail&&(e.older&&(e.older.newer=e.newer,e.newer.older=e.older),e.older=this._tail,e.newer=null,this._tail.newer=e,this._tail=e),e.value}}),a=/\r?\n|\r|\t/g,h=" ",c={baselineMarkerSize:1},"undefined"!=typeof document&&(d=document.createElement("div"),d.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),l=kendo.Class.extend({init:function(e){this._cache=new r(1e3),this.options=t.extend({},c,e)},measure:function(t,s,r){var a,h,c,l,u,f,p,g,m;if(void 0===r&&(r={}),!t)return o();if(a=i(s),h=n(t+a),c=this._cache.get(h))return c;l=o(),u=r.box||d,f=this._baselineMarker().cloneNode(!1);for(p in s)g=s[p],void 0!==g&&(u.style[p]=g);return m=r.normalizeText!==!1?e(t):t+"",u.textContent=m,u.appendChild(f),document.body.appendChild(u),m.length&&(l.width=u.offsetWidth-this.options.baselineMarkerSize,l.height=u.offsetHeight,l.baseline=f.offsetTop+this.options.baselineMarkerSize),l.width>0&&l.height>0&&this._cache.put(h,l),u.parentNode.removeChild(u),l},_baselineMarker:function(){var t=document.createElement("div");return t.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",t}}),l.current=new l,kendo.deepExtend(kendo.util,{LRUCache:r,TextMetrics:l,measureText:s,objectKey:i,hashKey:n,normalizeText:e})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/diagram/utils.min",["kendo.core.min"],t)}(function(){!function(t,e){function i(t,i,n){function o(t){for(var e=1;t*e%1;)e*=10;return e}var s,r,a,h;if(e===t||e===i)return[];if(n&&l.sign(i-t)!=l.sign(n))throw"The sign of the increment should allow to reach the stop-value.";if(n=n||1,t=t||0,i=i||t,(i-t)/n===1/0)throw"Infinite range defined.";if(s=[],r=-1,h=o(Math.abs(n)),t*=h,i*=h,n*=h,t>i&&n>0&&(n=-n),n<0)for(;(a=t+n*++r)>=i;)s.push(a/h);else for(;(a=t+n*++r)<=i;)s.push(a/h);return s}function n(t,e){if(t==e)return 0;var i=e.x-t.x,n=t.y-e.y,o=Math.atan(i/n);return n>=0?i<0?o+2*Math.PI:o:o+Math.PI}var o,s,r=window.kendo,a=r.dataviz.diagram={},h=r.deepExtend,c=t.isArray,d=1e-6,l={};h(l,{isNearZero:function(t){return Math.abs(t)<d},isDefined:function(t){return e!==t},isUndefined:function(t){return e===t||null===t},isObject:function(t){return t===Object(t)},has:function(t,e){return Object.hasOwnProperty.call(t,e)},isString:function(t){return"[object String]"==Object.prototype.toString.call(t)},isBoolean:function(t){return"[object Boolean]"==Object.prototype.toString.call(t)},isType:function(t,e){return Object.prototype.toString.call(t)=="[object "+e+"]"},isNumber:function(t){return!isNaN(parseFloat(t))&&isFinite(t)},isEmpty:function(t){if(null===t)return!0;if(c(t)||l.isString(t))return 0===t.length;for(var e in t)if(l.has(t,e))return!1;return!0},simpleExtend:function(t,e){if(l.isObject(e))for(var i in e)t[i]=e[i]},initArray:function(t,e){var i,n=[];for(i=0;i<t;++i)n[i]=e;return n},serializePoints:function(t){var e,i,n=[];for(e=0;e<t.length;e++)i=t[e],n.push(i.x+";"+i.y);return n.join(";")},deserializePoints:function(t){var e,i=t.split(";"),n=[];if(i.length%2!==0)throw"Not an array of points.";for(e=0;e<i.length;e+=2)n.push(new a.Point(parseInt(i[e],10),parseInt(i[e+1],10)));return n},randomInteger:function(t,e){return parseInt(Math.floor(Math.random()*e)+t,10)},DFT:function(t,e){var i,n;if(e(t),t.childNodes)for(i=0;i<t.childNodes.length;i++)n=t.childNodes[i],this.DFT(n,e)},getMatrixAngle:function(t){return null===t||0===t.d?0:180*Math.atan2(t.b,t.d)/Math.PI},getMatrixScaling:function(t){var e=Math.sqrt(t.a*t.a+t.c*t.c),i=Math.sqrt(t.b*t.b+t.d*t.d);return[e,i]}}),l.sign=function(t){return t?t<0?-1:1:0},l.findAngle=function(t,e){return 180*n(t,e)/Math.PI},l.forEach=function(t,e,i){for(var n=0;n<t.length;n++)e.call(i,t[n],n,t)},l.any=function(t,e){for(var i=0;i<t.length;++i)if(e(t[i]))return t[i];return null},l.remove=function(t,e){for(var i;(i=l.indexOf(t,e))!==-1;)t.splice(i,1);return t},l.contains=function(t,e){return l.indexOf(t,e)!==-1},l.indexOf=function(e,i){return t.inArray(i,e)},l.fold=function(t,e,i,n){var o,s,r=arguments.length>2;for(o=0;o<t.length;o++)s=t[o],r?i=e.call(n,i,s,o,t):(i=s,r=!0);if(!r)throw"Reduce of empty array with no initial value";return i},l.find=function(t,e,i){var n;return l.any(t,function(t,o,s){return!!e.call(i,t,o,s)&&(n=t,!0)}),n},l.first=function(t,e,i){return 0===t.length?null:l.isUndefined(e)?t[0]:l.find(t,e,i)},l.insert=function(t,e,i){return t.splice(i,0,e),t},l.all=function(t,e,i){var n,o,s=!0;for(o=0;o<t.length&&(n=t[o],s=s&&e.call(i,n,o,t),s);o++);return s},l.clear=function(t){t.splice(0,t.length)},l.bisort=function(t,e,i){if(l.isUndefined(t))throw"First array is not specified.";if(l.isUndefined(e))throw"Second array is not specified.";if(t.length!=e.length)throw"The two arrays should have equal length";var n,o=[];for(n=0;n<t.length;n++)o.push({x:t[n],y:e[n]});for(o.sort(l.isUndefined(i)?function(t,e){return t.x-e.x}:function(t,e){return i(t.x,e.x)}),l.clear(t),l.clear(e),n=0;n<o.length;n++)t.push(o[n].x),e.push(o[n].y)},l.addRange=function(t,e){t.push.apply(t,e)},o={easeInOut:function(t){return-Math.cos(t*Math.PI)/2+.5}},s=r.Class.extend({init:function(){this.adapters=[],this.target=0,this.tick=0,this.interval=20,this.duration=800,this.lastTime=null,this.handlers=[];var t=this;this.transition=o.easeInOut,this.timerDelegate=function(){t.onTimerEvent()}},addAdapter:function(t){this.adapters.push(t)},onComplete:function(t){this.handlers.push(t)},removeHandler:function(e){this.handlers=t.grep(this.handlers,function(t){return t!==e})},trigger:function(){var t=this;this.handlers&&l.forEach(this.handlers,function(e){return e.call(null!==t.caller?t.caller:t)})},onStep:function(){},seekTo:function(t){this.seekFromTo(this.tick,t)},seekFromTo:function(t,e){this.target=Math.max(0,Math.min(1,e)),this.tick=Math.max(0,Math.min(1,t)),this.lastTime=(new Date).getTime(),this.intervalId||(this.intervalId=window.setInterval(this.timerDelegate,this.interval))},stop:function(){this.intervalId&&(window.clearInterval(this.intervalId),this.intervalId=null,this.trigger())},play:function(t){0!==this.adapters.length&&(null!==t&&(this.caller=t),this.initState(),this.seekFromTo(0,1))},reverse:function(){this.seekFromTo(1,0)},initState:function(){if(0!==this.adapters.length)for(var t=0;t<this.adapters.length;t++)this.adapters[t].initState()},propagate:function(){var t,e=this.transition(this.tick);for(t=0;t<this.adapters.length;t++)this.adapters[t].update(e)},onTimerEvent:function(){var t,e=(new Date).getTime(),i=e-this.lastTime;this.lastTime=e,t=i/this.duration*(this.tick<this.target?1:-1),Math.abs(t)>=Math.abs(this.tick-this.target)?this.tick=this.target:this.tick+=t;try{this.propagate()}finally{this.onStep.call(this),this.target==this.tick&&this.stop()}}}),r.deepExtend(a,{init:function(t){r.init(t,a.ui)},Utils:l,Range:i,Ticker:s})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/diagram/math.min",["dataviz/diagram/utils.min","kendo.dataviz.core.min"],t)}(function(){!function(t,e){function i(t){return Math.abs(t)<z}function n(t,e,n,o,s){var r,a,h,c,d=(e.x-t.x)*(o.y-n.y)-(e.y-t.y)*(o.x-n.x);if(!i(d)&&(r=(t.y-n.y)*(o.x-n.x)-(t.x-n.x)*(o.y-n.y),a=(t.y-n.y)*(e.x-t.x)-(t.x-n.x)*(e.y-t.y),h=r/d,c=a/d,!s||!(h<0||h>1||c<0||c>1)))return new D(t.x+h*(e.x-t.x),t.y+h*(e.y-t.y))}function o(t,e){var i,n,o;do i=2*Math.random()-1,n=2*Math.random()-1,o=i*i+n*n;while(!o||o>1);return t+e*i*Math.sqrt(-2*Math.log(o)/o)}function s(t){var e,i,n;for(M.isUndefined(t)&&(t=10),e="",i="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",n=t;n>0;--n)e+=i.charAt(Math.round(Math.random()*(i.length-1)));return e}var r,a,h,c,d,l,u,f,p,g,m,_,v,y,w,x,b=window.kendo,C=b.dataviz.diagram,S=b.Class,k=b.deepExtend,T=b.dataviz,M=C.Utils,D=T.Point2D,I=b.isFunction,P=M.contains,L=t.map,E=3,z=1e-6;k(D.fn,{plus:function(t){return new D(this.x+t.x,this.y+t.y)},minus:function(t){return new D(this.x-t.x,this.y-t.y)},offset:function(t){return new D(this.x-t,this.y-t)},times:function(t){return new D(this.x*t,this.y*t)},normalize:function(){return 0===this.length()?new D:this.times(1/this.length())},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},toString:function(){return"("+this.x+","+this.y+")"},lengthSquared:function(){return this.x*this.x+this.y*this.y},middleOf:function(t,e){return new D(e.x-t.x,e.y-t.y).times(.5).plus(t)},toPolar:function(t){var e,i,n,o=1;if(t&&(o=180/Math.PI),e=Math.atan2(Math.abs(this.y),Math.abs(this.x)),i=Math.PI/2,n=this.length(),0===this.x){if(0===this.y)return new l(0,0);if(this.y>0)return new l(n,o*i);if(this.y<0)return new l(n,3*o*i)}else if(this.x>0){if(0===this.y)return new l(n,0);if(this.y>0)return new l(n,o*e);if(this.y<0)return new l(n,o*(4*i-e))}else{if(0===this.y)return new l(n,2*i);if(this.y>0)return new l(n,o*(2*i-e));if(this.y<0)return new l(n,o*(2*i+e))}},isOnLine:function(t,e){var i,n,o,s,r;return t.x>e.x&&(i=e,e=t,t=i),n=new a(t.x,t.y).inflate(E,E),o=new a(e.x,e.y).inflate(E,E),!!n.union(o).contains(this)&&(t.x===e.x||t.y===e.y||(t.y<e.y?(s=n.x+(o.x-n.x)*(this.y-(n.y+n.height))/(o.y+o.height-(n.y+n.height)),r=n.x+n.width+(o.x+o.width-(n.x+n.width))*(this.y-n.y)/(o.y-n.y)):(s=n.x+(o.x-n.x)*(this.y-n.y)/(o.y-n.y),r=n.x+n.width+(o.x+o.width-(n.x+n.width))*(this.y-(n.y+n.height))/(o.y+o.height-(n.y+n.height))),this.x>s&&this.x<r))}}),k(D,{parse:function(t){var e=t.slice(1,t.length-1),i=e.split(","),n=parseInt(i[0],10),o=parseInt(i[1],10);if(!isNaN(n)&&!isNaN(o))return new D(n,o)}}),r=S.extend({init:function(t,e,i){this.point=t,this.left=e,this.right=i}}),a=S.extend({init:function(t,e,i,n){this.x=t||0,this.y=e||0,this.width=i||0,this.height=n||0},contains:function(t){return t.x>=this.x&&t.x<=this.x+this.width&&t.y>=this.y&&t.y<=this.y+this.height},inflate:function(t,i){return i===e&&(i=t),this.x-=t,this.y-=i,this.width+=2*t+1,this.height+=2*i+1,this},offset:function(t,e){var i=t,n=e;return t instanceof D&&(i=t.x,n=t.y),this.x+=i,this.y+=n,this},union:function(t){var e=Math.min(this.x,t.x),i=Math.min(this.y,t.y),n=Math.max(this.x+this.width,t.x+t.width),o=Math.max(this.y+this.height,t.y+t.height);return new a(e,i,n-e,o-i)},center:function(){return new D(this.x+this.width/2,this.y+this.height/2)},top:function(){return new D(this.x+this.width/2,this.y)},right:function(){return new D(this.x+this.width,this.y+this.height/2)},bottom:function(){return new D(this.x+this.width/2,this.y+this.height)},left:function(){return new D(this.x,this.y+this.height/2)},topLeft:function(){return new D(this.x,this.y)},topRight:function(){return new D(this.x+this.width,this.y)},bottomLeft:function(){return new D(this.x,this.y+this.height)},bottomRight:function(){return new D(this.x+this.width,this.y+this.height)},clone:function(){return new a(this.x,this.y,this.width,this.height)},isEmpty:function(){return!this.width&&!this.height},equals:function(t){return this.x===t.x&&this.y===t.y&&this.width===t.width&&this.height===t.height},rotatedBounds:function(t){var e=this.clone(),i=this.rotatedPoints(t),n=i[0],o=i[1],s=i[2],r=i[3];return e.x=Math.min(s.x,n.x,o.x,r.x),e.y=Math.min(s.y,n.y,o.y,r.y),e.width=Math.max(s.x,n.x,o.x,r.x)-e.x,e.height=Math.max(s.y,n.y,o.y,r.y)-e.y,e},rotatedPoints:function(t){var e=this,i=e.center(),n=e.bottomRight().rotate(i,360-t),o=e.topLeft().rotate(i,360-t),s=e.topRight().rotate(i,360-t),r=e.bottomLeft().rotate(i,360-t);return[o,s,n,r]},toString:function(t){return t=t||" ",this.x+t+this.y+t+this.width+t+this.height},scale:function(t,e,i,n,o){var s,r,a,h=this.topLeft(),c=this.center();h.rotate(c,360-o).rotate(n,o),s=i.minus(h),r=new D(s.x*t,s.y*e),a=s.minus(r),h=h.plus(a),h.rotate(n,360-o).rotate(c,o),this.x=h.x,this.y=h.y,this.width*=t,this.height*=e},zoom:function(t){return this.x*=t,this.y*=t,this.width*=t,this.height*=t,this},overlaps:function(t){var e=this.bottomRight(),i=t.bottomRight(),n=!(e.x<t.x||e.y<t.y||i.x<this.x||i.y<this.y);return n}}),h=S.extend({init:function(t,e){this.width=t,this.height=e}}),h.prototype.Empty=new h(0,0),a.toRect=function(t){return t instanceof a||(t=new a(t.x,t.y,t.width,t.height)),t},a.empty=function(){return new a(0,0,0,0)},a.fromPoints=function(t,e){if(isNaN(t.x)||isNaN(t.y)||isNaN(e.x)||isNaN(e.y))throw"Some values are NaN.";return new a(Math.min(t.x,e.x),Math.min(t.y,e.y),Math.abs(t.x-e.x),Math.abs(t.y-e.y))},c={lines:function(t,e,i,o){return n(t,e,i,o)},segments:function(t,e,i,o){return n(t,e,i,o,!0)},rectWithLine:function(t,e,i){return c.segments(e,i,t.topLeft(),t.topRight())||c.segments(e,i,t.topRight(),t.bottomRight())||c.segments(e,i,t.bottomLeft(),t.bottomRight())||c.segments(e,i,t.topLeft(),t.bottomLeft())},rects:function(t,e,i){var n,o,s=e.topLeft(),r=e.topRight(),a=e.bottomLeft(),h=e.bottomRight(),d=e.center();return i&&(s=s.rotate(d,i),r=r.rotate(d,i),a=a.rotate(d,i),h=h.rotate(d,i)),n=t.contains(s)||t.contains(r)||t.contains(a)||t.contains(h)||c.rectWithLine(t,s,r)||c.rectWithLine(t,s,a)||c.rectWithLine(t,r,h)||c.rectWithLine(t,a,h),n||(s=t.topLeft(),r=t.topRight(),a=t.bottomLeft(),h=t.bottomRight(),i&&(o=360-i,s=s.rotate(d,o),r=r.rotate(d,o),a=a.rotate(d,o),h=h.rotate(d,o)),n=e.contains(s)||e.contains(r)||e.contains(a)||e.contains(h)),n}},d=S.extend({init:function(t){this.container=a.toRect(t)},align:function(t,e){var i,n=e.toLowerCase().split(" ");for(i=0;i<n.length;i++)t=this._singleAlign(t,n[i]);return t},_singleAlign:function(t,e){return I(this[e])?this[e](t):t},left:function(t){return this._align(t,this._left)},center:function(t){return this._align(t,this._center)},right:function(t){return this._align(t,this._right)},stretch:function(t){return this._align(t,this._stretch)},top:function(t){return this._align(t,this._top)},middle:function(t){return this._align(t,this._middle)},bottom:function(t){return this._align(t,this._bottom)},_left:function(t,e){e.x=t.x},_center:function(t,e){e.x=(t.width-e.width)/2||0},_right:function(t,e){e.x=t.width-e.width},_top:function(t,e){e.y=t.y},_middle:function(t,e){e.y=(t.height-e.height)/2||0},_bottom:function(t,e){e.y=t.height-e.height},_stretch:function(t,e){e.x=0,e.y=0,e.height=t.height,e.width=t.width},_align:function(t,e){return t=a.toRect(t),e(this.container,t),t}}),l=S.extend({init:function(t,e){this.r=t,this.angle=e}}),u=S.extend({init:function(t,e,i,n,o,s){this.a=t||0,this.b=e||0,this.c=i||0,this.d=n||0,this.e=o||0,this.f=s||0},plus:function(t){this.a+=t.a,this.b+=t.b,this.c+=t.c,this.d+=t.d,this.e+=t.e,this.f+=t.f},minus:function(t){this.a-=t.a,this.b-=t.b,this.c-=t.c,this.d-=t.d,this.e-=t.e,this.f-=t.f},times:function(t){return new u(this.a*t.a+this.c*t.b,this.b*t.a+this.d*t.b,this.a*t.c+this.c*t.d,this.b*t.c+this.d*t.d,this.a*t.e+this.c*t.f+this.e,this.b*t.e+this.d*t.f+this.f)},apply:function(t){return new D(this.a*t.x+this.c*t.y+this.e,this.b*t.x+this.d*t.y+this.f)},applyRect:function(t){return a.fromPoints(this.apply(t.topLeft()),this.apply(t.bottomRight()))},toString:function(){return"matrix("+this.a+" "+this.b+" "+this.c+" "+this.d+" "+this.e+" "+this.f+")"}}),k(u,{fromSVGMatrix:function(t){var e=new u;return e.a=t.a,e.b=t.b,e.c=t.c,e.d=t.d,e.e=t.e,e.f=t.f,e},fromMatrixVector:function(t){var e=new u;return e.a=t.a,e.b=t.b,e.c=t.c,e.d=t.d,e.e=t.e,e.f=t.f,e},fromList:function(t){if(6!==t.length)throw"The given list should consist of six elements.";var e=new u;return e.a=t[0],e.b=t[1],e.c=t[2],e.d=t[3],e.e=t[4],e.f=t[5],e},translation:function(t,e){var i=new u;return i.a=1,i.b=0,i.c=0,i.d=1,i.e=t,i.f=e,i},unit:function(){return new u(1,0,0,1,0,0)},rotation:function(t,e,i){var n=new u;return n.a=Math.cos(t*Math.PI/180),n.b=Math.sin(t*Math.PI/180),n.c=-n.b,n.d=n.a,n.e=e-e*n.a+i*n.b||0,n.f=i-i*n.a-e*n.b||0,n},scaling:function(t,e){var i=new u;return i.a=t,i.b=0,i.c=0,i.d=e,i.e=0,i.f=0,i},parse:function(t){var e,i;if(t){if(t=t.trim(),"matrix"===t.slice(0,6).toLowerCase()){if(i=t.slice(7,t.length-1).trim(),e=i.split(","),6===e.length)return u.fromList(L(e,function(t){return parseFloat(t)}));if(e=i.split(" "),6===e.length)return u.fromList(L(e,function(t){return parseFloat(t)}))}if("("===t.slice(0,1)&&")"===t.slice(t.length-1)&&(t=t.substr(1,t.length-1)),t.indexOf(",")>0&&(e=t.split(","),6===e.length))return u.fromList(L(e,function(t){return parseFloat(t)}));if(t.indexOf(" ")>0&&(e=t.split(" "),6===e.length))return u.fromList(L(e,function(t){return parseFloat(t)}))}return e}}),f=S.extend({init:function(t,e,i,n,o,s){this.a=t||0,this.b=e||0,this.c=i||0,this.d=n||0,this.e=o||0,this.f=s||0},fromMatrix:function(t){var e=new f;return e.a=t.a,e.b=t.b,e.c=t.c,e.d=t.d,e.e=t.e,e.f=t.f,e}}),p={_distanceToLineSquared:function(t,e,i){function n(t,e){return(t.x-e.x)*(t.x-e.x)+(t.y-e.y)*(t.y-e.y)}if(e===i)return n(t,e);var o=i.x-e.x,s=i.y-e.y,r=(t.x-e.x)*o+(t.y-e.y)*s;return r<0?n(e,t):(r=(i.x-t.x)*o+(i.y-t.y)*s,r<0?n(i,t):(r=(i.x-t.x)*s-(i.y-t.y)*o,r*r/(o*o+s*s)))},distanceToLine:function(t,e,i){return Math.sqrt(this._distanceToLineSquared(t,e,i))},distanceToPolyline:function(t,e){var i,n,o,s,r=Number.MAX_VALUE;if(M.isUndefined(e)||0===e.length)return Number.MAX_VALUE;for(i=0;i<e.length-1;i++)n=e[i],o=e[i+1],s=this._distanceToLineSquared(t,n,o),s<r&&(r=s);return Math.sqrt(r)}},g=b.Class.extend({init:function(){this._buckets=[],this.length=0},add:function(t,e){var i=this._createGetBucket(t);return M.isDefined(e)&&(i.value=e),i},get:function(t){return this._bucketExists(t)?this._createGetBucket(t):null},set:function(t,e){this.add(t,e)},containsKey:function(t){return this._bucketExists(t)},remove:function(t){if(this._bucketExists(t)){var e=this._hash(t);return delete this._buckets[e],this.length--,t}},forEach:function(t){var e,i,n,o,s=this._hashes();for(e=0,i=s.length;e<i;e++)n=s[e],o=this._buckets[n],M.isUndefined(o)||t(o)},clone:function(){var t,e,i,n,o=new g,s=this._hashes();for(t=0,e=s.length;t<e;t++)i=s[t],n=this._buckets[i],M.isUndefined(n)||o.add(n.key,n.value);return o},_hashes:function(){var t,e=[];for(t in this._buckets)this._buckets.hasOwnProperty(t)&&e.push(t);return e},_bucketExists:function(t){var e=this._hash(t);return M.isDefined(this._buckets[e])},_createGetBucket:function(t){var e=this._hash(t),i=this._buckets[e];return M.isUndefined(i)&&(i={key:t},this._buckets[e]=i,this.length++),i},_hash:function(t){if(M.isNumber(t))return t;if(M.isString(t))return this._hashString(t);if(M.isObject(t))return this._objectHashId(t);throw"Unsupported key type."},_hashString:function(t){var e,i,n=0;if(0===t.length)return n;for(e=0;e<t.length;e++)i=t.charCodeAt(e),n=32*n-n+i;return n},_objectHashId:function(t){var e=t._hashId;return M.isUndefined(e)&&(e=s(),t._hashId=e),e}}),m=b.Observable.extend({init:function(e){var i,n=this;if(b.Observable.fn.init.call(n),this._hashTable=new g,this.length=0,M.isDefined(e))if(t.isArray(e))for(i=0;i<e.length;i++)this.add(e[i]);else e.forEach(function(t,e){this.add(t,e)},this)},add:function(t,e){var i=this._hashTable.get(t);i||(i=this._hashTable.add(t),this.length++,this.trigger("changed")),i.value=e},set:function(t,e){this.add(t,e)},get:function(t){var e=this._hashTable.get(t);if(e)return e.value;throw Error("Cannot find key "+t)},containsKey:function(t){return this._hashTable.containsKey(t)},remove:function(t){if(this.containsKey(t))return this.trigger("changed"),this.length--,this._hashTable.remove(t)},forEach:function(t,e){this._hashTable.forEach(function(i){t.call(e,i.key,i.value)})},forEachValue:function(t,e){this._hashTable.forEach(function(i){t.call(e,i.value)})},forEachKey:function(t,e){this._hashTable.forEach(function(i){t.call(e,i.key)})},keys:function(){var t=[];return this.forEachKey(function(e){t.push(e)}),t}}),_=b.Class.extend({init:function(){this._tail=null,this._head=null,this.length=0},enqueue:function(t){var e={value:t,next:null};this._head?(this._tail.next=e,this._tail=this._tail.next):(this._head=e,this._tail=this._head),this.length++},dequeue:function(){if(this.length<1)throw Error("The queue is empty.");var t=this._head.value;return this._head=this._head.next,this.length--,t},contains:function(t){for(var e=this._head;e;){if(e.value===t)return!0;e=e.next}return!1}}),v=b.Observable.extend({init:function(t){var e=this;b.Observable.fn.init.call(e),this._hashTable=new g,this.length=0,M.isDefined(t)&&(t instanceof g?t.forEach(function(t){this.add(t)}):t instanceof m&&t.forEach(function(t,e){this.add({key:t,value:e})},this))},contains:function(t){return this._hashTable.containsKey(t)},add:function(t){var e=this._hashTable.get(t);e||(this._hashTable.add(t,t),this.length++,this.trigger("changed"))},get:function(t){return this.contains(t)?this._hashTable.get(t).value:null},hash:function(t){return this._hashTable._hash(t)},remove:function(t){this.contains(t)&&(this._hashTable.remove(t),this.length--,this.trigger("changed"))},forEach:function(t,e){this._hashTable.forEach(function(e){t(e.value)},e)},toArray:function(){var t=[];return this.forEach(function(e){t.push(e)}),t}}),y=b.Class.extend({init:function(t,e){if(this.links=[],this.outgoing=[],this.incoming=[],this.weight=1,this.id=M.isDefined(t)?t:s(),M.isDefined(e)){this.associatedShape=e;var i=e.bounds();this.width=i.width,this.height=i.height,this.x=i.x,this.y=i.y}else this.associatedShape=null;this.data=null,this.type="Node",this.shortForm="Node '"+this.id+"'",this.isVirtual=!1},isIsolated:function(){return M.isEmpty(this.links)},bounds:function(t){return M.isDefined(t)?(this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,e):new C.Rect(this.x,this.y,this.width,this.height)},isLinkedTo:function(t){var e=this;return M.any(e.links,function(i){return i.getComplement(e)===t})},getChildren:function(){var t,e,i,n;if(0===this.outgoing.length)return[];for(t=[],e=0,i=this.outgoing.length;e<i;e++)n=this.outgoing[e],t.push(n.getComplement(this));return t},getParents:function(){var t,e,i,n;if(0===this.incoming.length)return[];for(t=[],e=0,i=this.incoming.length;e<i;e++)n=this.incoming[e],t.push(n.getComplement(this));return t},clone:function(){var t=new y;return M.isDefined(this.weight)&&(t.weight=this.weight),M.isDefined(this.balance)&&(t.balance=this.balance),M.isDefined(this.owner)&&(t.owner=this.owner),t.associatedShape=this.associatedShape,t.x=this.x,t.y=this.y,t.width=this.width,t.height=this.height,t},adjacentTo:function(t){return null!==this.isLinkedTo(t)},removeLink:function(t){t.source===this&&(M.remove(this.links,t),M.remove(this.outgoing,t),t.source=null),t.target===this&&(M.remove(this.links,t),M.remove(this.incoming,t),t.target=null)},hasLinkTo:function(t){return M.any(this.outgoing,function(e){return e.target===t})},degree:function(){return this.links.length},incidentWith:function(t){return P(this.links,t)},getLinksWith:function(t){return M.all(this.links,function(e){return e.getComplement(this)===t},this)},getNeighbors:function(){var t=[];return M.forEach(this.incoming,function(e){t.push(e.getComplement(this))},this),M.forEach(this.outgoing,function(e){t.push(e.getComplement(this))},this),t}}),w=b.Class.extend({init:function(t,e,i,n){if(M.isUndefined(t))throw"The source of the new link is not set.";if(M.isUndefined(e))throw"The target of the new link is not set.";var o,r;o=M.isString(t)?new y(t):t,r=M.isString(e)?new y(e):e,this.source=o,this.target=r,this.source.links.push(this),this.target.links.push(this),this.source.outgoing.push(this),this.target.incoming.push(this),this.id=M.isDefined(i)?i:s(),this.associatedConnection=M.isDefined(n)?n:null,this.type="Link",this.shortForm="Link '"+this.source.id+"->"+this.target.id+"'"},getComplement:function(t){if(this.source!==t&&this.target!==t)throw"The given node is not incident with this link.";return this.source===t?this.target:this.source},getCommonNode:function(t){return this.source===t.source||this.source===t.target?this.source:this.target===t.source||this.target===t.target?this.target:null},isBridging:function(t,e){return this.source===t&&this.target===e||this.source===e&&this.target===t},getNodes:function(){return[this.source,this.target]},incidentWith:function(t){return this.source===t||this.target===t},adjacentTo:function(t){return P(this.source.links,t)||P(this.target.links,t)},changeSource:function(t){M.remove(this.source.links,this),M.remove(this.source.outgoing,this),t.links.push(this),t.outgoing.push(this),this.source=t},changeTarget:function(t){M.remove(this.target.links,this),M.remove(this.target.incoming,this),t.links.push(this),t.incoming.push(this),this.target=t},changesNodes:function(t,e){this.source===t?this.changeSource(e):this.target===t&&this.changeTarget(e)},reverse:function(){var t=this.source,e=this.target;return this.source=e,M.remove(t.outgoing,this),this.source.outgoing.push(this),this.target=t,M.remove(e.incoming,this),this.target.incoming.push(this),this},directTo:function(t){if(this.source!==t&&this.target!==t)throw"The given node is not incident with this link.";this.target!==t&&this.reverse()},createReverseEdge:function(){var t=this.clone();return t.reverse(),t.reversed=!0,t},clone:function(){var t=new w(this.source,this.target);return t}}),x=b.Class.extend({init:function(t){this.links=[],this.nodes=[],this._nodeMap=new m,this.diagram=null,this._root=null,M.isDefined(t)?M.isString(t)?this.id=t:(this.diagram=t,this.id=t.id):this.id=s(),this.bounds=new a,this._hasCachedRelationships=!1,this.type="Graph"},cacheRelationships:function(t){var e,i,n;if(M.isUndefined(t)&&(t=!1),!this._hasCachedRelationships||t){for(e=0,i=this.nodes.length;e<i;e++)n=this.nodes[e],n.children=this.getChildren(n),n.parents=this.getParents(n);this._hasCachedRelationships=!0}},assignLevels:function(t,e,i){var n,o,s,r;if(!t)throw"Start node not specified.";for(M.isUndefined(e)&&(e=0),this.cacheRelationships(),M.isUndefined(i)&&(i=new m,M.forEach(this.nodes,function(t){i.add(t,!1)})),i.set(t,!0),t.level=e,n=t.children,o=0,s=n.length;o<s;o++)r=n[o],r&&!i.get(r)&&this.assignLevels(r,e+1,i)},root:function(t){if(M.isUndefined(t)){if(this._root)return this._root;var e=M.first(this.nodes,function(t){return 0===t.incoming.length});return e?e:M.first(this.nodes)}this._root=t},getConnectedComponents:function(){var t,e,i,n,o;for(this.componentIndex=0,this.setItemIndices(),t=M.initArray(this.nodes.length,-1),e=0;e<this.nodes.length;e++)t[e]===-1&&(this._collectConnectedNodes(t,e),this.componentIndex++);for(i=[],n=0;n<this.componentIndex;++n)i[n]=new x;for(n=0;n<t.length;++n)o=i[t[n]],o.addNodeAndOutgoings(this.nodes[n]);return i.sort(function(t,e){return e.nodes.length-t.nodes.length}),i},_collectConnectedNodes:function(t,e){t[e]=this.componentIndex;var i=this.nodes[e];M.forEach(i.links,function(e){var n=e.getComplement(i),o=n.index;t[o]===-1&&this._collectConnectedNodes(t,o)},this)},calcBounds:function(){var t,e,i,n;if(this.isEmpty())return this.bounds=new a;for(t=null,e=0,i=this.nodes.length;e<i;e++)n=this.nodes[e],t=t?t.union(n.bounds()):n.bounds();return this.bounds=t},getSpanningTree:function(t){var e,i,n,o,s,r,a,h,c,d,l,u,f=new x,p=new m;for(f.root=t.clone(),f.root.level=0,f.root.id=t.id,p.add(t,f.root),t.level=0,n=[],o=[],f._addNode(f.root),n.push(t),o.push(t),s=1;o.length>0;)for(r=o.pop(),a=0;a<r.links.length;a++)h=r.links[a],c=h.getComplement(r),P(n,c)||(c.level=r.level+1,s<c.level+1&&(s=c.level+1),P(o,c)||o.push(c),P(n,c)||n.push(c),p.containsKey(r)?e=p.get(r):(e=r.clone(),e.level=r.level,e.id=r.id,p.add(r,e)),p.containsKey(c)?i=p.get(c):(i=c.clone(),i.level=c.level,i.id=c.id,p.add(c,i)),d=new w(e,i),f.addLink(d));for(l=[],u=0;u<s;u++)l.push([]);return M.forEach(f.nodes,function(t){l[t.level].push(t)}),f.treeLevels=l,f.cacheRelationships(),f},takeRandomNode:function(e,i){if(M.isUndefined(e)&&(e=[]),M.isUndefined(i)&&(i=4),0===this.nodes.length)return null;if(1===this.nodes.length)return P(e,this.nodes[0])?null:this.nodes[0];var n=t.grep(this.nodes,function(t){return!P(e,t)&&t.degree()<=i});return M.isEmpty(n)?null:n[M.randomInteger(0,n.length)]},isEmpty:function(){return M.isEmpty(this.nodes)},isHealthy:function(){return M.all(this.links,function(t){return P(this.nodes,t.source)&&P(this.nodes,t.target)},this)},getParents:function(t){if(!this.hasNode(t))throw"The given node is not part of this graph.";return t.getParents()},getChildren:function(t){if(!this.hasNode(t))throw"The given node is not part of this graph.";return t.getChildren()},addLink:function(t,i,n){var o,s,r;if(M.isUndefined(t))throw"The source of the link is not defined.";if(M.isUndefined(i)){if(M.isDefined(t.type)&&"Link"===t.type)return this.addExistingLink(t),e;throw"The target of the link is not defined."}return o=this.getNode(t),M.isUndefined(o)&&(o=this.addNode(t)),s=this.getNode(i),M.isUndefined(s)&&(s=this.addNode(i)),r=new w(o,s),M.isDefined(n)&&(r.owner=n),this.links.push(r),r},removeAllLinks:function(){for(;this.links.length>0;){var t=this.links[0];this.removeLink(t)}},addExistingLink:function(t){var e,i;this.hasLink(t)||(this.links.push(t),this.hasNode(t.source.id)?(e=this.getNode(t.source.id),t.changeSource(e)):this.addNode(t.source),this.hasNode(t.target.id)?(i=this.getNode(t.target.id),t.changeTarget(i)):this.addNode(t.target))},hasLink:function(t){if(M.isString(t))return M.any(this.links,function(e){return e.id===t});if("Link"===t.type)return P(this.links,t);throw"The given object is neither an identifier nor a Link."},getNode:function(t){var e=t.id||t;if(this._nodeMap.containsKey(e))return this._nodeMap.get(e)},hasNode:function(t){var e=t.id||t;return this._nodeMap.containsKey(e)},_addNode:function(t){this.nodes.push(t),this._nodeMap.add(t.id,t)},_removeNode:function(t){M.remove(this.nodes,t),this._nodeMap.remove(t.id)},removeNode:function(t){var e,i,n,o,s=t;if(M.isString(t)&&(s=this.getNode(t)),!M.isDefined(s))throw"The identifier should be a Node or the Id (string) of a node.";for(e=s.links,s.links=[],i=0,n=e.length;i<n;i++)o=e[i],this.removeLink(o);this._removeNode(s)},areConnected:function(t,e){return M.any(this.links,function(i){return i.source==t&&i.target==e||i.source==e&&i.target==t})},removeLink:function(t){M.remove(this.links,t),M.remove(t.source.outgoing,t),M.remove(t.source.links,t),M.remove(t.target.incoming,t),M.remove(t.target.links,t)},addNode:function(t,e,i){var n=null;if(!M.isDefined(t))throw"No Node or identifier for a new Node is given.";if(M.isString(t)){if(this.hasNode(t))return this.getNode(t);n=new y(t)}else{if(this.hasNode(t))return this.getNode(t);n=t}return M.isDefined(e)&&n.bounds(e),M.isDefined(i)&&(n.owner=i),this._addNode(n),n},addNodeAndOutgoings:function(t){this.hasNode(t)||this._addNode(t);var e=t.outgoing;t.outgoing=[],M.forEach(e,function(t){this.addExistingLink(t)},this)},setItemIndices:function(){var t;for(t=0;t<this.nodes.length;++t)this.nodes[t].index=t;for(t=0;t<this.links.length;++t)this.links[t].index=t},clone:function(t){var e,i=new x,n=M.isDefined(t)&&t===!0;return n&&(i.nodeMap=new m,i.linkMap=new m),e=new m,M.forEach(this.nodes,function(t){var o=t.clone();
e.set(t,o),i._addNode(o),n&&i.nodeMap.set(o,t)}),M.forEach(this.links,function(t){if(e.containsKey(t.source)&&e.containsKey(t.target)){var o=i.addLink(e.get(t.source),e.get(t.target));n&&i.linkMap.set(o,t)}}),i},linearize:function(t){return x.Utils.linearize(this,t)},depthFirstTraversal:function(t,e){var i,n;if(M.isUndefined(t))throw"You need to supply a starting node.";if(M.isUndefined(e))throw"You need to supply an action.";if(!this.hasNode(t))throw"The given start-node is not part of this graph";i=this.getNode(t),n=[],this._dftIterator(i,e,n)},_dftIterator:function(t,e,i){var n,o,s,r;for(e(t),i.push(t),n=t.getChildren(),o=0,s=n.length;o<s;o++)r=n[o],P(i,r)||this._dftIterator(r,e,i)},breadthFirstTraversal:function(t,e){var i,n,o,s,r,a,h,c;if(M.isUndefined(t))throw"You need to supply a starting node.";if(M.isUndefined(e))throw"You need to supply an action.";if(!this.hasNode(t))throw"The given start-node is not part of this graph";for(i=this.getNode(t),n=new _,o=[],n.enqueue(i);n.length>0;)for(s=n.dequeue(),e(s),o.push(s),r=s.getChildren(),a=0,h=r.length;a<h;a++)c=r[a],P(o,c)||P(n,c)||n.enqueue(c)},_stronglyConnectedComponents:function(t,e,i,n,o,s,r){var a,h,c,d,l;for(i.add(e,r),n.add(e,r),r++,s.push(e),a=e.getChildren(),c=0,d=a.length;c<d;c++)h=a[c],i.containsKey(h)?P(s,h)&&n.add(e,Math.min(n.get(e),i.get(h))):(this._stronglyConnectedComponents(t,h,i,n,o,s,r),n.add(e,Math.min(n.get(e),n.get(h))));if(n.get(e)===i.get(e)){l=[];do h=s.pop(),l.push(h);while(h!==e);(!t||l.length>1)&&o.push(l)}},findCycles:function(t){var e,i,n,o,s,r,a;for(M.isUndefined(t)&&(t=!0),e=new m,i=new m,n=[],o=[],s=0,r=this.nodes.length;s<r;s++)a=this.nodes[s],e.containsKey(a)||this._stronglyConnectedComponents(t,a,e,i,n,o,0);return n},isAcyclic:function(){return M.isEmpty(this.findCycles())},isSubGraph:function(t){var e=t.linearize(),i=this.linearize();return M.all(e,function(t){return P(i,t)})},makeAcyclic:function(){var t,e,i,n,o,s,r,a,h,c,d,l,u,f,p,g,_,v,y,w,x,b,C,S,k,T,D,I,P,L,E,z;if(this.isEmpty()||this.nodes.length<=1||this.links.length<=1)return[];if(2==this.nodes.length){if(t=[],this.links.length>1)for(e=this.links[0],i=e.source,n=0,o=this.links.length;n<o;n++)s=this.links[n],s.source!=i&&(r=s.reverse(),t.push(r));return t}for(a=this.clone(!0),h=this.nodes.length,c=new m,d=function(t){return 0===t.outgoing.length?2-h:0===t.incoming.length?h-2:t.outgoing.length-t.incoming.length},l=function(t,e){var i=d(t,h);e.containsKey(i)||e.set(i,[]),e.get(i).push(t)},M.forEach(a.nodes,function(t){l(t,c)}),u=[],f=[];a.nodes.length>0;){if(c.containsKey(2-h))for(v=c.get(2-h);v.length>0;){for(g=v.pop(),y=0;y<g.links.length;y++)w=g.links[y],p=w.getComplement(g),_=d(p,h),M.remove(c.get(_),p),p.removeLink(w),l(p,c);a._removeNode(g),f.unshift(g)}if(c.containsKey(h-2))for(x=c.get(h-2);x.length>0;){for(p=x.pop(),b=0;b<p.links.length;b++)C=p.links[b],g=C.getComplement(p),_=d(g,h),M.remove(c.get(_),g),g.removeLink(C),l(g,c);u.push(p),a._removeNode(p)}if(a.nodes.length>0)for(S=h-3;S>2-h;S--)if(c.containsKey(S)&&c.get(S).length>0){for(k=c.get(S),T=k.pop(),D=0;D<T.links.length;D++)I=T.links[D],P=I.getComplement(T),_=d(P,h),M.remove(c.get(_),P),P.removeLink(I),l(P,c);u.push(T),a._removeNode(T);break}}for(u=u.concat(f),L=new m,E=0;E<this.nodes.length;E++)L.set(a.nodeMap.get(u[E]),E);return z=[],M.forEach(this.links,function(t){L.get(t.source)>L.get(t.target)&&(t.reverse(),z.push(t))}),z}}),x.Predefined={EightGraph:function(){return x.Utils.parse(["1->2","2->3","3->4","4->1","3->5","5->6","6->7","7->3"])},Mindmap:function(){return x.Utils.parse(["0->1","0->2","0->3","0->4","0->5","1->6","1->7","7->8","2->9","9->10","9->11","3->12","12->13","13->14","4->15","4->16","15->17","15->18","18->19","18->20","14->21","14->22","5->23","23->24","23->25","6->26"])},ThreeGraph:function(){return x.Utils.parse(["1->2","2->3","3->1"])},BinaryTree:function(t){return M.isUndefined(t)&&(t=5),x.Utils.createBalancedTree(t,2)},Linear:function(t){return M.isUndefined(t)&&(t=10),x.Utils.createBalancedTree(t,1)},Tree:function(t,e){return x.Utils.createBalancedTree(t,e)},Forest:function(t,e,i){return x.Utils.createBalancedForest(t,e,i)},Workflow:function(){return x.Utils.parse(["0->1","1->2","2->3","1->4","4->3","3->5","5->6","6->3","6->7","5->4"])},Grid:function(t,e){var i,n,o,s,r,a=new C.Graph;if(t<=0&&e<=0)return a;for(i=0;i<t+1;i++)for(n=null,o=0;o<e+1;o++)s=new y(""+i+"."+o),a.addNode(s),n&&a.addLink(n,s),i>0&&(r=a.getNode(""+(i-1)+"."+o),a.addLink(r,s)),n=s;return a}},x.Utils={parse:function(t){var e,i,n,o,s,r=new C.Graph,a=t.slice();for(i=0,n=a.length;i<n;i++){if(o=a[i],M.isString(o)){if(o.indexOf("->")<0)throw"The link should be specified as 'a->b'.";if(s=o.split("->"),2!=s.length)throw"The link should be specified as 'a->b'.";e=new w(s[0],s[1]),r.addLink(e)}if(M.isObject(o)){if(!e)throw"Specification found before Link definition.";b.deepExtend(e,o)}}return r},linearize:function(t,e){var i,n,o,s;if(M.isUndefined(t))throw"Expected an instance of a Graph object in slot one.";for(M.isUndefined(e)&&(e=!1),i=[],n=0,o=t.links.length;n<o;n++)s=t.links[n],i.push(s.source.id+"->"+s.target.id),e&&i.push({id:s.id});return i},_addShape:function(t,e,i,n){return M.isUndefined(e)&&(e=new C.Point(0,0)),M.isUndefined(i)&&(i=s()),n=b.deepExtend({width:20,height:20,id:i,radius:10,fill:"#778899",data:"circle",undoable:!1,x:e.x,y:e.y},n),t.addShape(n)},_addConnection:function(t,e,i,n){return t.connect(e,i,n)},createDiagramFromGraph:function(t,e,i,n){var o,s,r,h,c,d,l,u,f,p,g,m,_,v,y,w;if(M.isUndefined(t))throw"The diagram surface is undefined.";if(M.isUndefined(e))throw"No graph specification defined.";for(M.isUndefined(i)&&(i=!0),M.isUndefined(n)&&(n=!1),o=t.element.clientWidth||200,s=t.element.clientHeight||200,r=[],d=0,l=e.nodes.length;d<l;d++)h=e.nodes[d],u=h.position,M.isUndefined(u)&&(u=M.isDefined(h.x)&&M.isDefined(h.y)?new D(h.x,h.y):new D(M.randomInteger(10,o-20),M.randomInteger(10,s-20))),f={},"0"===h.id||n&&b.deepExtend(f,{width:150*Math.random()+20,height:80*Math.random()+50,data:"rectangle",fill:{color:"#778899"}}),c=this._addShape(t,u,h.id,f),p=c.bounds(),M.isDefined(p)&&(h.x=p.x,h.y=p.y,h.width=p.width,h.height=p.height),r[h.id]=c;for(g=0;g<e.links.length;g++)m=e.links[g],_=r[m.source.id],M.isUndefined(_)||(v=r[m.target.id],M.isUndefined(v)||this._addConnection(t,_,v,{id:m.id}));if(i)for(y=new t.SpringLayout(t),y.layoutGraph(e,{limitToView:!1}),w=0;w<e.nodes.length;w++)h=e.nodes[w],c=r[h.id],c.bounds(new a(h.x,h.y,h.width,h.height))},createBalancedTree:function(t,e){var i,n,o,s,r,a,h,c,d,l;if(M.isUndefined(t)&&(t=3),M.isUndefined(e)&&(e=3),i=new C.Graph,n=-1,o=[],t<=0||e<=0)return i;for(r=new y(""+ ++n),i.addNode(r),i.root=r,o.push(r),a=0;a<t;a++){for(s=[],h=0;h<o.length;h++)for(c=o[h],d=0;d<e;d++)l=new y(""+ ++n),i.addLink(c,l),s.push(l);o=s}return i},createBalancedForest:function(t,e,i){var n,o,s,r,a,h,c,d,l,u,f;if(M.isUndefined(t)&&(t=3),M.isUndefined(e)&&(e=3),M.isUndefined(i)&&(i=5),n=new C.Graph,o=-1,s=[],t<=0||e<=0||i<=0)return n;for(a=0;a<i;a++)for(h=new y(""+ ++o),n.addNode(h),s=[h],c=0;c<t;c++){for(r=[],d=0;d<s.length;d++)for(l=s[d],u=0;u<e;u++)f=new y(""+ ++o),n.addLink(l,f),r.push(f);s=r}return n},createRandomConnectedGraph:function(t,e,i){var n,o,s,r,a,h,c,d,l,u;if(M.isUndefined(t)&&(t=40),M.isUndefined(e)&&(e=4),M.isUndefined(i)&&(i=!1),n=new C.Graph,o=-1,t<=0)return n;if(s=new y(""+ ++o),n.addNode(s),1===t)return n;if(t>1){for(r=1;r<t&&(a=n.takeRandomNode([],e),a);r++)h=n.addNode(""+r),n.addLink(a,h);if(!i&&t>1)for(c=M.randomInteger(1,t),d=0;d<c;d++)l=n.takeRandomNode([],e),u=n.takeRandomNode([],e),l&&u&&!n.areConnected(l,u)&&n.addLink(l,u);return n}},randomDiagram:function(t,e,i,n,o){var s=b.dataviz.diagram.Graph.Utils.createRandomConnectedGraph(e,i,n);x.Utils.createDiagramFromGraph(t,s,!1,o)}},b.deepExtend(C,{init:function(t){b.init(t,C.ui)},Point:D,Intersect:c,Geometry:p,Rect:a,Size:h,RectAlign:d,Matrix:u,MatrixVector:f,normalVariable:o,randomId:s,Dictionary:m,HashTable:g,Queue:_,Set:v,Node:y,Link:w,Graph:x,PathDefiner:r})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/diagram/svg.min",["kendo.drawing.min","dataviz/diagram/math.min"],t)}(function(){!function(t,e){function i(t,e){var i,n,o,s=this.options,r=!1;for(o=0;o<e.length;o++)n=e[o],i=t[n],A(i)&&s[n]!==i&&(s[n]=i,r=!0);return r}function n(t){return{x:t.x||0,y:t.y||0,width:t.width||0,height:t.height||0}}function o(t){if(t){var e=t;return U(e)&&(e={color:e}),e.color&&(e.color=s(e.color)),e}}function s(t){var e;return e=t!=K?new F.Color(t).toHex():t}function r(t,e){var i=e.x-t.x,n=e.y-t.y,o=F.util.deg(Math.atan2(n,i));return o}function a(t,e){return new F.Segment(new H.Point(t,e))}function h(t){if(t)return new H.Rect([t.x,t.y],[t.width,t.height])}var c,d,l,u,f,p,g,m,_,v,y,w,x,b,C,S,k,T,M,D,I,P=window.kendo,L=P.dataviz.diagram,E=P.Class,z=P.deepExtend,B=L.Point,R=L.Rect,N=L.Matrix,O=L.Utils,A=O.isNumber,U=O.isString,V=L.MatrixVector,H=P.geometry,F=P.drawing,G=F.util.defined,q=t.inArray,K="transparent",W={none:"none",arrowStart:"ArrowStart",filledCircle:"FilledCircle",arrowEnd:"ArrowEnd"},X=360,Y="start",j="end",Q="width",J="height",Z="x",$="y";L.Markers=W,c=E.extend({init:function(t,e){this.x=t,this.y=e},toMatrix:function(){return N.scaling(this.x,this.y)},toString:function(){return P.format("scale({0},{1})",this.x,this.y)},invert:function(){return new c(1/this.x,1/this.y)}}),d=E.extend({init:function(t,e){this.x=t,this.y=e},toMatrixVector:function(){return new V(0,0,0,0,this.x,this.y)},toMatrix:function(){return N.translation(this.x,this.y)},toString:function(){return P.format("translate({0},{1})",this.x,this.y)},plus:function(t){this.x+=t.x,this.y+=t.y},times:function(t){this.x*=t,this.y*=t},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},normalize:function(){0!==this.Length&&this.times(1/this.length())},invert:function(){return new d((-this.x),(-this.y))}}),l=E.extend({init:function(t,e,i){this.x=e||0,this.y=i||0,this.angle=t},toString:function(){return this.x&&this.y?P.format("rotate({0},{1},{2})",this.angle,this.x,this.y):P.format("rotate({0})",this.angle)},toMatrix:function(){return N.rotation(this.angle,this.x,this.y)},center:function(){return new B(this.x,this.y)},invert:function(){return new l(X-this.angle,this.x,this.y)}}),l.ZERO=new l(0),l.create=function(t){return new l(t.angle,t.x,t.y)},l.parse=function(t){var e=t.slice(1,t.length-1).split(","),i=e[0],n=e[1],o=e[2],s=new l(i,n,o);return s},u=E.extend({init:function(t,i,n,o,s,r){this.translate=new d(t,i),n!==e&&o!==e&&(this.scale=new c(n,o)),s!==e&&(this.rotate=r?new l(s,r.x,r.y):new l(s))},toString:function(){var t=function(t){return t?""+t:""};return t(this.translate)+t(this.rotate)+t(this.scale)},render:function(t){t._transform=this,t._renderTransform()},toMatrix:function(){var t=N.unit();return this.translate&&(t=t.times(this.translate.toMatrix())),this.rotate&&(t=t.times(this.rotate.toMatrix())),this.scale&&(t=t.times(this.scale.toMatrix())),t},invert:function(){var t,i,n=this.rotate?this.rotate.invert():e,o=n?n.toMatrix():N.unit(),s=this.scale?this.scale.invert():e,r=s?s.toMatrix():N.unit(),a=new B((-this.translate.x),(-this.translate.y));return a=o.times(r).apply(a),t=new d(a.x,a.y),i=new u,i.translate=t,i.rotate=n,i.scale=s,i}}),f={_setScale:function(){var t=this.options,e=this._originWidth,i=this._originHeight,n=t.width/e,o=t.height/i;A(n)||(n=1),A(o)||(o=1),this._transform.scale=new c(n,o)},_setTranslate:function(){var t=this.options,e=t.x||0,i=t.y||0;this._transform.translate=new d(e,i)},_initSize:function(){var t=this.options,e=!1;t.autoSize!==!1&&(G(t.width)||G(t.height))&&(this._measure(!0),this._setScale(),e=!0),(G(t.x)||G(t.y))&&(this._setTranslate(),e=!0),e&&this._renderTransform()},_updateSize:function(t){var e=!1;return this.options.autoSize!==!1&&this._diffNumericOptions(t,[Q,J])&&(e=!0,this._measure(!0),this._setScale()),this._diffNumericOptions(t,[Z,$])&&(e=!0,this._setTranslate()),e&&this._renderTransform(),e}},p=E.extend({init:function(t){var e=this;e.options=z({},e.options,t),e.id=e.options.id,e._originSize=R.empty(),e._transform=new u},visible:function(t){return this.drawingContainer().visible(t)},redraw:function(t){t&&t.id&&(this.id=t.id)},position:function(t,i){var n=this.options;return G(t)?(G(i)?(n.x=t,n.y=i):t instanceof B&&(n.x=t.x,n.y=t.y),this._transform.translate=new d(n.x,n.y),this._renderTransform(),e):new B(n.x,n.y)},rotate:function(t,e){return G(t)&&(this._transform.rotate=new l(t,e.x,e.y),this._renderTransform()),this._transform.rotate||l.ZERO},drawingContainer:function(){return this.drawingElement},_renderTransform:function(){var t=this._transform.toMatrix();this.drawingContainer().transform(new H.Matrix(t.a,t.b,t.c,t.d,t.e,t.f))},_hover:function(){},_diffNumericOptions:i,_measure:function(t){var e,i,n;return!this._measured||t?(i=this._boundingBox()||new H.Rect,n=i.topLeft(),e=new R(n.x,n.y,i.width(),i.height()),this._originSize=e,this._originWidth=e.width,this._originHeight=e.height,this._measured=!0):e=this._originSize,e},_boundingBox:function(){return this.drawingElement.rawBBox()}}),g=p.extend({init:function(t){p.fn.init.call(this,t),t=this.options,t.fill=o(t.fill),t.stroke=o(t.stroke)},options:{stroke:{color:"gray",width:1},fill:{color:K}},fill:function(t,e){this._fill({color:s(t),opacity:e})},stroke:function(t,e,i){this._stroke({color:s(t),width:e,opacity:i})},redraw:function(t){var e,i;t&&(e=t.stroke,i=t.fill,e&&this._stroke(o(e)),i&&this._fill(o(i)),p.fn.redraw.call(this,t))},_hover:function(t){var e,i=this.drawingElement,n=this.options,s=n.hover;s&&s.fill&&(e=t?o(s.fill):n.fill,i.fill(e.color,e.opacity))},_stroke:function(t){var e,i=this.options;z(i,{stroke:t}),t=i.stroke,e=null,t.width>0&&(e={color:t.color,width:t.width,opacity:t.opacity,dashType:t.dashType}),this.drawingElement.options.set("stroke",e)},_fill:function(t){var e,i,n,o=this.options;z(o,{fill:t||{}}),e=o.fill,e.gradient?(i=e.gradient,n="radial"===i.type?F.RadialGradient:F.LinearGradient,this.drawingElement.fill(new n(i))):this.drawingElement.fill(e.color,e.opacity)}}),m=g.extend({init:function(t){t=this._textColor(t),g.fn.init.call(this,t),this._font(),this._initText(),this._initSize()},options:{fontSize:15,fontFamily:"sans-serif",stroke:{width:0},fill:{color:"black"},autoSize:!0},_initText:function(){var t=this.options;this.drawingElement=new F.Text(G(t.text)?t.text:"",new H.Point,{font:t.font}),this._fill(),this._stroke()},_textColor:function(t){return t&&t.color&&(t=z({},t,{fill:{color:t.color}})),t},_font:function(){var t,e=this.options;e.fontFamily&&G(e.fontSize)?(t=[],e.fontStyle&&t.push(e.fontStyle),e.fontWeight&&t.push(e.fontWeight),t.push(e.fontSize+(A(e.fontSize)?"px":"")),t.push(e.fontFamily),e.font=t.join(" ")):delete e.font},content:function(t){return this.drawingElement.content(t)},redraw:function(t){var e,i;t&&(e=!1,i=this.options,t=this._textColor(t),g.fn.redraw.call(this,t),(t.fontFamily||G(t.fontSize)||t.fontStyle||t.fontWeight)&&(z(i,{fontFamily:t.fontFamily,fontSize:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight}),this._font(),this.drawingElement.options.set("font",i.font),e=!0),t.text&&(this.content(t.text),e=!0),!this._updateSize(t)&&e&&this._initSize())}}),z(m.fn,f),_=g.extend({init:function(t){g.fn.init.call(this,t),this._initPath(),this._setPosition()},_setPosition:function(){var t=this.options,e=t.x,i=t.y;(G(e)||G(i))&&this.position(e||0,i||0)},redraw:function(t){t&&(g.fn.redraw.call(this,t),this._diffNumericOptions(t,[Q,J])&&this._drawPath(),this._diffNumericOptions(t,[Z,$])&&this._setPosition())},_initPath:function(){var t=this.options;this.drawingElement=new F.Path({stroke:t.stroke,closed:!0}),this._fill(),this._drawPath()},_drawPath:function(){var t=this.drawingElement,e=n(this.options),i=e.width,o=e.height;t.segments.elements([a(0,0),a(i,0),a(i,o),a(0,o)])}}),v=g.extend({init:function(t){g.fn.init.call(this,t);var e=this.options.anchor;this.anchor=new H.Point(e.x,e.y),this.createElement()},options:{stroke:{color:K,width:0},fill:{color:"black"}},_transformToPath:function(t,e){var i=e.transform();return t&&i&&(t=t.transformCopy(i)),t},redraw:function(t){t&&(t.position&&(this.options.position=t.position),g.fn.redraw.call(this,t))}}),y=v.extend({options:{radius:4,anchor:{x:0,y:0}},createElement:function(){var t=this.options;this.drawingElement=new F.Circle(new H.Circle(this.anchor,t.radius),{fill:t.fill,stroke:t.stroke})},positionMarker:function(t){var e,i,n=this.options,o=n.position,s=t.segments;e=o==Y?s[0]:s[s.length-1],e&&(i=this._transformToPath(e.anchor(),t),this.drawingElement.transform(H.transform().translate(i.x,i.y)))}}),w=v.extend({options:{path:"M 0 0 L 10 5 L 0 10 L 3 5 z",anchor:{x:10,y:5}},createElement:function(){var t=this.options;this.drawingElement=F.Path.parse(t.path,{fill:t.fill,stroke:t.stroke})},positionMarker:function(t){var e,i,n=this._linePoints(t),o=n.start,s=n.end,a=H.transform();o&&a.rotate(r(o,s),s),s&&(e=this.anchor,i=s.clone().translate(-e.x,-e.y),a.translate(i.x,i.y)),this.drawingElement.transform(a)},_linePoints:function(t){var e,i,n,o,s,r=this.options,a=t.segments;if(r.position==Y?(n=a[0],n&&(i=n.anchor(),e=n.controlOut(),o=a[1],!e&&o&&(e=o.anchor()))):(n=a[a.length-1],n&&(i=n.anchor(),e=n.controlIn(),s=a[a.length-2],!e&&s&&(e=s.anchor()))),i)return{start:this._transformToPath(e,t),end:this._transformToPath(i,t)}}}),x={_getPath:function(t){var e=this.drawingElement;if(e instanceof F.MultiPath&&(e=t==Y?e.paths[0]:e.paths[e.paths.length-1]),e&&e.segments.length)return e},_normalizeMarkerOptions:function(t){var e=t.startCap,i=t.endCap;U(e)&&(t.startCap={type:e}),U(i)&&(t.endCap={type:i})},_removeMarker:function(t){var e=this._markers[t];e&&(this.drawingContainer().remove(e.drawingElement),delete this._markers[t])},_createMarkers:function(){var t=this.options;this._normalizeMarkerOptions(t),this._markers={},this._markers[Y]=this._createMarker(t.startCap,Y),this._markers[j]=this._createMarker(t.endCap,j)},_createMarker:function(t,i){var n,o,s=(t||{}).type,r=this._getPath(i);return r?(s==W.filledCircle?n=y:s==W.arrowStart||s==W.arrowEnd?n=w:this._removeMarker(i),n?(o=new n(z({},t,{position:i})),o.positionMarker(r),this.drawingContainer().append(o.drawingElement),o):e):(this._removeMarker(i),e)},_positionMarker:function(t){var e,i=this._markers[t];i&&(e=this._getPath(t),e?i.positionMarker(e):this._removeMarker(t))},_capMap:{start:"startCap",end:"endCap"},_redrawMarker:function(t,e,i){var n,o,s,r,a;return this._normalizeMarkerOptions(i),n=this.options,o=this._capMap[e],s=(n[o]||{}).type,r=i[o],a=!1,r?(n[o]=z({},n[o],r),r.type&&s!=r.type?(this._removeMarker(e),this._markers[e]=this._createMarker(n[o],e),a=!0):this._markers[e]&&this._markers[e].redraw(r)):t&&!this._markers[e]&&n[o]&&(this._markers[e]=this._createMarker(n[o],e),a=!0),a},_redrawMarkers:function(t,e){!this._redrawMarker(t,Y,e)&&t&&this._positionMarker(Y),!this._redrawMarker(t,j,e)&&t&&this._positionMarker(j)}},b=g.extend({init:function(t){g.fn.init.call(this,t),this.container=new F.Group,this._createElements(),this._initSize()},options:{autoSize:!0},drawingContainer:function(){return this.container},data:function(t){var i=this.options;return t?(i.data!=t&&(i.data=t,this._setData(t),this._initSize(),this._redrawMarkers(!0,{})),e):i.data},redraw:function(t){var e,i;t&&(g.fn.redraw.call(this,t),e=this.options,i=t.data,G(i)&&e.data!=i?(e.data=i,this._setData(i),this._updateSize(t)||this._initSize(),this._redrawMarkers(!0,t)):(this._updateSize(t),this._redrawMarkers(!1,t)))},_createElements:function(){var t=this.options;this.drawingElement=F.Path.parse(t.data||"",{stroke:t.stroke}),this._fill(),this.container.append(this.drawingElement),this._createMarkers()},_setData:function(t){var e=this.drawingElement,i=F.Path.parse(t||""),n=i.paths.slice(0);i.paths.elements([]),e.paths.elements(n)}}),z(b.fn,f),z(b.fn,x),C=g.extend({init:function(t){g.fn.init.call(this,t),this.container=new F.Group,this._initPath(),this._createMarkers()},drawingContainer:function(){return this.container},redraw:function(t){var e,i;t&&(t=t||{},e=t.from,i=t.to,e&&(this.options.from=e),i&&(this.options.to=i),e||i?(this._drawPath(),this._redrawMarkers(!0,t)):this._redrawMarkers(!1,t),g.fn.redraw.call(this,t))},_initPath:function(){var t=this.options,e=this.drawingElement=new F.Path({stroke:t.stroke});this._fill(),this._drawPath(),this.container.append(e)},_drawPath:function(){var t=this.options,e=this.drawingElement,i=t.from||new B,n=t.to||new B;e.segments.elements([a(i.x,i.y),a(n.x,n.y)])}}),z(C.fn,x),S=g.extend({init:function(t){g.fn.init.call(this,t),this.container=new F.Group,this._initPath(),this._createMarkers()},drawingContainer:function(){return this.container},points:function(t){var i=this.options;return t?(i.points=t,this._updatePath(),e):i.points},redraw:function(t){if(t){var e=t.points;g.fn.redraw.call(this,t),e&&this._pointsDiffer(e)?(this.points(e),this._redrawMarkers(!0,t)):this._redrawMarkers(!1,t)}},_initPath:function(){var t=this.options;this.drawingElement=new F.Path({stroke:t.stroke}),this._fill(),this.container.append(this.drawingElement),t.points&&this._updatePath()},_pointsDiffer:function(t){var e,i=this.options.points,n=i.length!==t.length;if(!n)for(e=0;e<t.length;e++)if(i[e].x!==t[e].x||i[e].y!==t[e].y){n=!0;break}return n},_updatePath:function(){var t,e,i=this.drawingElement,n=this.options,o=n.points,s=[];for(e=0;e<o.length;e++)t=o[e],s.push(a(t.x,t.y));i.segments.elements(s)},options:{points:[]}}),z(S.fn,x),k=p.extend({init:function(t){p.fn.init.call(this,t),this._initImage()},redraw:function(t){t&&(t.source&&this.drawingElement.src(t.source),this._diffNumericOptions(t,[Q,J,Z,$])&&this.drawingElement.rect(this._rect()),p.fn.redraw.call(this,t))},_initImage:function(){var t=this.options,e=this._rect();this.drawingElement=new F.Image(t.source,e,{})},_rect:function(){var t=n(this.options),e=new H.Point(t.x,t.y),i=new H.Size(t.width,t.height);return new H.Rect(e,i)}}),T=p.extend({init:function(t){this.children=[],p.fn.init.call(this,t),this.drawingElement=new F.Group,this._initSize()},options:{autoSize:!1},append:function(t){this.drawingElement.append(t.drawingContainer()),this.children.push(t),this._childrenChange=!0},remove:function(t){this._remove(t)&&(this._childrenChange=!0)},_remove:function(t){var e=q(t,this.children);if(e>=0)return this.drawingElement.removeAt(e),this.children.splice(e,1),!0},clear:function(){this.drawingElement.clear(),this.children=[],this._childrenChange=!0},toFront:function(t){var e,i;for(i=0;i<t.length;i++)e=t[i],this._remove(e)&&this.append(e)},toBack:function(t){this._reorderChildren(t,0)},toIndex:function(t,e){this._reorderChildren(t,e)},_reorderChildren:function(t,e){var i,n,o,s,r,a=this.drawingElement,h=a.children.slice(0),c=this.children,d=A(e);for(i=0;i<t.length;i++)r=t[i],s=r.drawingContainer(),n=q(r,c),n>=0&&(h.splice(n,1),c.splice(n,1),o=d?e:e[i],h.splice(o,0,s),c.splice(o,0,r));a.clear(),a.append.apply(a,h)},redraw:function(t){t&&(this._childrenChange?(this._childrenChange=!1,this._updateSize(t)||this._initSize()):this._updateSize(t),p.fn.redraw.call(this,t))},_boundingBox:function(){var t,e,i,n,o=this.children;for(n=0;n<o.length;n++)e=o[n],e.visible()&&e._includeInBBox!==!1&&(i=e.drawingContainer().clippedBBox(null),i&&(t=t?H.Rect.union(t,i):i));return t}}),z(T.fn,f),M=T.extend({init:function(t,e){this.children=[],p.fn.init.call(this,e),this.drawingElement=new F.Layout(h(t),e),this._initSize()},rect:function(t){if(t)this.drawingElement.rect(h(t));else{var e=this.drawingElement.rect();if(e)return new R(e.origin.x,e.origin.y,e.size.width,e.size.height)}},reflow:function(){this.drawingElement.reflow()},redraw:function(t){P.deepExtend(this.drawingElement.options,t),T.fn.redraw.call(this,t)}}),D=g.extend({init:function(t){g.fn.init.call(this,t),this._initCircle(),this._initSize()},redraw:function(t){if(t){var e=this.options;t.center&&(z(e,{center:t.center}),this._center.move(e.center.x,e.center.y)),this._diffNumericOptions(t,["radius"])&&this._circle.setRadius(e.radius),this._updateSize(t),g.fn.redraw.call(this,t)}},_initCircle:function(){var t,e=this.options,i=e.width,n=e.height,o=e.radius;G(o)||(G(i)||(i=n),G(n)||(n=i),e.radius=o=Math.min(i,n)/2),t=e.center||{x:o,y:o},this._center=new H.Point(t.x,t.y),this._circle=new H.Circle(this._center,o),this.drawingElement=new F.Circle(this._circle,{stroke:e.stroke}),this._fill()}}),z(D.fn,f),I=E.extend({init:function(t,e){e=e||{},this.element=t,this.surface=F.Surface.create(t,e),P.isFunction(this.surface.translate)&&(this.translate=this._translate),this.drawingElement=new F.Group,this._viewBox=new R(0,0,e.width,e.height),this.size(this._viewBox)},bounds:function(){var t=this.drawingElement.clippedBBox();return new R(0,0,t.width(),t.height())},size:function(t){var e=this._viewBox;return G(t)&&(e.width=t.width,e.height=t.height,this.surface.setSize(t)),{width:e.width,height:e.height}},_translate:function(t,e){var i=this._viewBox;return G(t)&&G(e)&&(i.x=t,i.y=e,this.surface.translate({x:t,y:e})),{x:i.x,y:i.y}},draw:function(){this.surface.draw(this.drawingElement)},append:function(t){return this.drawingElement.append(t.drawingContainer()),this},remove:function(t){this.drawingElement.remove(t.drawingContainer())},insertBefore:function(){},clear:function(){this.drawingElement.clear()},destroy:function(e){this.surface.destroy(),e&&t(this.element).remove()}}),P.deepExtend(L,{init:function(t){P.init(t,L.ui)},diffNumericOptions:i,Element:p,Scale:c,Translation:d,Rotation:l,Circle:D,Group:T,Rectangle:_,Canvas:I,Path:b,Layout:M,Line:C,MarkerBase:v,ArrowMarker:w,CircleMarker:y,Polyline:S,CompositeTransform:u,TextBlock:m,Image:k,VisualBase:g})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/diagram/services.min",["kendo.drawing.min","dataviz/diagram/svg.min"],t)}(function(){!function(t,e){function i(t,e){return e.charCodeAt(0)==t||e.toUpperCase().charCodeAt(0)==t}function n(t,e){var i;return t.x==-1&&t.y==-1?i=e.bottomRight():1==t.x&&1==t.y?i=e.topLeft():t.x==-1&&1==t.y?i=e.topRight():1==t.x&&t.y==-1?i=e.bottomLeft():0===t.x&&t.y==-1?i=e.bottom():0===t.x&&1==t.y?i=e.top():1==t.x&&0===t.y?i=e.left():t.x==-1&&0===t.y&&(i=e.right()),i}function o(t){var e=t.options.editable;return e&&e.drag!==!1}function s(t,e){var i,n,o,s;for(s=0;s<t.connectors.length;s++)if(i=t.connectors[s],n=i.position(),o=new q(n.x,n.y),o.inflate(st,st),o.contains(e))return i}function r(t){return t.ctrlKey===!1&&t.altKey===!1&&t.shiftKey===!1}var a,h,c,d,l,u,f,p,g,m,_,v,y,w,x,b,C,S,k,T,M,D,I,P,L,E,z,B,R,N,O,A,U=window.kendo,V=U.dataviz,H=V.diagram,F=U.Class,G=H.Group,q=H.Rect,K=H.Rectangle,W=H.Utils,X=W.isUndefined,Y=H.Point,j=H.Circle,Q=H.Ticker,J=U.deepExtend,Z=U.ui.Movable,$=U.support.browser,tt=U.drawing.util,et=tt.defined,it=t.inArray,nt=t.proxy,ot={arrow:"default",grip:"pointer",cross:"pointer",add:"pointer",move:"move",select:"pointer",south:"s-resize",east:"e-resize",west:"w-resize",north:"n-resize",rowresize:"row-resize",colresize:"col-resize"},st=10,rt="Auto",at="Top",ht="Right",ct="Left",dt="Bottom",lt=10,ut=10,ft="dragStart",pt="drag",gt="dragEnd",mt="itemRotate",_t="itemBoundsChange",vt=5,yt=5,wt="mouseEnter",xt="mouseLeave",bt="zoomStart",Ct="zoomEnd",St=-2e4,kt=2e4,Tt=.9,Mt=.93,Dt=5,It="transparent",Pt="pan",Lt="rotated",Et="source",zt="target",Bt={"-1":Et,1:zt};H.Cursors=ot,a=U.Class.extend({init:function(t){this.layoutState=t,this.diagram=t.diagram},initState:function(){function t(t,e){var i=this.diagram.getShapeById(t);i&&(this.subjects.push(i),this.froms.push(i.bounds().topLeft()),this.tos.push(e.topLeft()))}this.froms=[],this.tos=[],this.subjects=[],this.layoutState.nodeMap.forEach(t,this)},update:function(t){if(!(this.subjects.length<=0))for(var e=0;e<this.subjects.length;e++)this.subjects[e].position(new Y(this.froms[e].x+(this.tos[e].x-this.froms[e].x)*t,this.froms[e].y+(this.tos[e].y-this.froms[e].y)*t))}}),h=F.extend({init:function(t,e,i){this.animate=!X(i)&&i,this._initialState=t,this._finalState=e,this.title="Diagram layout"},undo:function(){this.setState(this._initialState)},redo:function(){this.setState(this._finalState)},setState:function(t){var e,i=t.diagram;this.animate?(t.linkMap.forEach(function(t,e){var n=i.getShapeById(t);n.visible(!1),n&&n.points(e)}),e=new Q,e.addAdapter(new a(t)),e.onComplete(function(){t.linkMap.forEach(function(t){var e=i.getShapeById(t);e.visible(!0)})}),e.play()):(t.nodeMap.forEach(function(t,e){var n=i.getShapeById(t);n&&n.position(e.topLeft())}),t.linkMap.forEach(function(t,e){var n=i.getShapeById(t);n&&n.points(e)}))}}),c=F.extend({init:function(t){this.units=[],this.title="Composite unit",t!==e&&this.units.push(t)},add:function(t){this.units.push(t)},undo:function(){for(var t=0;t<this.units.length;t++)this.units[t].undo()},redo:function(){for(var t=0;t<this.units.length;t++)this.units[t].redo()}}),d=F.extend({init:function(t,e,i){this.item=t,this._redoSource=e,this._redoTarget=i,et(e)&&(this._undoSource=t.source()),et(i)&&(this._undoTarget=t.target()),this.title="Connection Editing"},undo:function(){this._undoSource!==e&&this.item._updateConnector(this._undoSource,"source"),this._undoTarget!==e&&this.item._updateConnector(this._undoTarget,"target"),this.item.updateModel()},redo:function(){this._redoSource!==e&&this.item._updateConnector(this._redoSource,"source"),this._redoTarget!==e&&this.item._updateConnector(this._redoTarget,"target"),this.item.updateModel()}}),l=F.extend({init:function(t,e,i){this.item=t,this._undoSource=e,this._undoTarget=i,this._redoSource=t.source(),this._redoTarget=t.target(),this.title="Connection Editing"},undo:function(){this.item._updateConnector(this._undoSource,"source"),this.item._updateConnector(this._undoTarget,"target"),this.item.updateModel()},redo:function(){this.item._updateConnector(this._redoSource,"source"),this.item._updateConnector(this._redoTarget,"target"),this.item.updateModel()}}),u=F.extend({init:function(t){this.connection=t,this.diagram=t.diagram,this.targetConnector=t.targetConnector,this.title="Delete connection"},undo:function(){this.diagram._addConnection(this.connection,!1)},redo:function(){this.diagram.remove(this.connection,!1)}}),f=F.extend({init:function(t){this.shape=t,this.diagram=t.diagram,this.title="Deletion"},undo:function(){this.diagram._addShape(this.shape,!1),this.shape.select(!1)},redo:function(){this.shape.select(!1),this.diagram.remove(this.shape,!1)}}),p=F.extend({init:function(t,e,i){var n,o;for(this.shapes=t,this.undoStates=e,this.title="Transformation",this.redoStates=[],this.adorner=i,n=0;n<this.shapes.length;n++)o=this.shapes[n],this.redoStates.push(o.bounds())},undo:function(){var t,e;for(t=0;t<this.shapes.length;t++)e=this.shapes[t],e.bounds(this.undoStates[t]),e.hasOwnProperty("layout")&&e.layout(e,this.redoStates[t],this.undoStates[t]),e.updateModel();this.adorner&&(this.adorner.refreshBounds(),this.adorner.refresh())},redo:function(){var t,e;for(t=0;t<this.shapes.length;t++)e=this.shapes[t],e.bounds(this.redoStates[t]),e.hasOwnProperty("layout")&&e.layout(e,this.undoStates[t],this.redoStates[t]),e.updateModel();this.adorner&&(this.adorner.refreshBounds(),this.adorner.refresh())}}),g=F.extend({init:function(t,e){this.connection=t,this.diagram=e,this.title="New connection"},undo:function(){this.diagram.remove(this.connection,!1)},redo:function(){this.diagram._addConnection(this.connection,!1)}}),m=F.extend({init:function(t,e){this.shape=t,this.diagram=e,this.title="New shape"},undo:function(){this.diagram.deselect(),this.diagram.remove(this.shape,!1)},redo:function(){this.diagram._addShape(this.shape,!1)}}),_=F.extend({init:function(t,e,i){this.initial=t,this.finalPos=e,this.diagram=i,this.title="Pan Unit"},undo:function(){this.diagram.pan(this.initial)},redo:function(){this.diagram.pan(this.finalPos)}}),v=F.extend({init:function(t,e,i){var n,o;for(this.shapes=e,this.undoRotates=i,this.title="Rotation",this.redoRotates=[],this.redoAngle=t._angle,this.adorner=t,this.center=t._innerBounds.center(),
n=0;n<this.shapes.length;n++)o=this.shapes[n],this.redoRotates.push(o.rotate().angle)},undo:function(){var t,e;for(t=0;t<this.shapes.length;t++)e=this.shapes[t],e.rotate(this.undoRotates[t],this.center,!1),e.hasOwnProperty("layout")&&e.layout(e),e.updateModel();this.adorner&&(this.adorner._initialize(),this.adorner.refresh())},redo:function(){var t,e;for(t=0;t<this.shapes.length;t++)e=this.shapes[t],e.rotate(this.redoRotates[t],this.center,!1),e.hasOwnProperty("layout")&&e.layout(e),e.updateModel();this.adorner&&(this.adorner._initialize(),this.adorner.refresh())}}),y=F.extend({init:function(t,e,i){this.diagram=t,this.indices=i,this.items=e,this.title="Rotate Unit"},undo:function(){this.diagram._toIndex(this.items,this.indices)},redo:function(){this.diagram.toFront(this.items,!1)}}),w=F.extend({init:function(t,e,i){this.diagram=t,this.indices=i,this.items=e,this.title="Rotate Unit"},undo:function(){this.diagram._toIndex(this.items,this.indices)},redo:function(){this.diagram.toBack(this.items,!1)}}),x=U.Observable.extend({init:function(t){U.Observable.fn.init.call(this,t),this.bind(this.events,t),this.stack=[],this.index=0,this.capacity=100},events:["undone","redone"],begin:function(){this.composite=new c},cancel:function(){this.composite=e},commit:function(t){this.composite.units.length>0&&this._restart(this.composite,t),this.composite=e},addCompositeItem:function(t){this.composite?this.composite.add(t):this.add(t)},add:function(t,e){this._restart(t,e)},pop:function(){this.index>0&&(this.stack.pop(),this.index--)},count:function(){return this.stack.length},undo:function(){this.index>0&&(this.index--,this.stack[this.index].undo(),this.trigger("undone"))},redo:function(){this.stack.length>0&&this.index<this.stack.length&&(this.stack[this.index].redo(),this.index++,this.trigger("redone"))},_restart:function(t,e){this.stack.splice(this.index,this.stack.length-this.index),this.stack.push(t),e!==!1?this.redo():this.index++,this.stack.length>this.capacity&&(this.stack.splice(0,this.stack.length-this.capacity),this.index=this.capacity)},clear:function(){this.stack=[],this.index=0}}),b=F.extend({init:function(t){this.toolService=t},start:function(){},move:function(){},end:function(){},tryActivate:function(){return!1},getCursor:function(){return ot.arrow}}),C=b.extend({init:function(e){var i,n,o,s,r=this,a=U.support.mobileOS?Mt:Tt;b.fn.init.call(r,e),i=r.toolService.diagram,n=i.canvas,o=i.scroller=r.scroller=t(i.scrollable).kendoMobileScroller({friction:a,velocityMultiplier:Dt,mousewheelScrolling:!1,zoom:!1,scroll:nt(r._move,r)}).data("kendoMobileScroller"),n.translate&&(r.movableCanvas=new Z(n.element)),s=function(t,e,i){t.makeVirtual(),t.virtualSize(e||St,i||kt)},s(o.dimensions.x),s(o.dimensions.y),o.disable()},tryActivate:function(t,e){var i=this.toolService,n=i.diagram.options.pannable,o=e.ctrlKey;return et(n.key)&&(o=n.key&&"none"!=n.key?e[n.key+"Key"]:r(e)&&!et(i.hoveredItem)),n!==!1&&o&&!et(i.hoveredAdorner)&&!et(i._hoveredConnector)},start:function(){this.scroller.enable()},move:function(){},_move:function(t){var e=this,i=e.toolService.diagram,n=i.canvas,o=new Y(t.scrollLeft,t.scrollTop);n.translate?(i._storePan(o.times(-1)),e.movableCanvas.moveTo(o),n.translate(o.x,o.y)):o=o.plus(i._pan.times(-1)),i.trigger(Pt,{pan:o})},end:function(){this.scroller.disable()},getCursor:function(){return ot.move}}),S=F.extend({init:function(t){this.toolService=t},tryActivate:function(){return!0},start:function(t,e){var i=this.toolService,n=i.diagram,o=i.hoveredItem;o&&(i.selectSingle(o,e),o.adorner&&(this.adorner=o.adorner,this.handle=this.adorner._hitTest(t))),this.handle||(this.handle=n._resizingAdorner._hitTest(t),this.handle&&(this.adorner=n._resizingAdorner)),this.adorner&&(this.adorner.isDragHandle(this.handle)&&n.trigger(ft,{shapes:this.adorner.shapes,connections:[]})?(i.startPoint=t,i.end(t)):this.adorner.start(t))},move:function(t){this.adorner&&(this.adorner.move(this.handle,t),this.adorner.isDragHandle(this.handle)&&this.toolService.diagram.trigger(pt,{shapes:this.adorner.shapes,connections:[]}))},end:function(){var t,i=this.toolService.diagram,n=this.adorner;n&&(n.isDragHandle(this.handle)&&i.trigger(gt,{shapes:n.shapes,connections:[]})?n.cancel():(t=n.stop(),t&&i.undoRedoService.add(t,!1))),this.adorner=e,this.handle=e},getCursor:function(t){return this.toolService.hoveredItem?this.toolService.hoveredItem._getCursor(t):ot.arrow}}),k=F.extend({init:function(t){this.toolService=t},tryActivate:function(t,e){var i=this.toolService,n=i.diagram.options.selectable,o=n&&n.multiple!==!1;return o&&(o=n.key&&"none"!=n.key?e[n.key+"Key"]:r(e)),o&&!et(i.hoveredItem)&&!et(i.hoveredAdorner)},start:function(t){var e=this.toolService.diagram;e.deselect(),e.selector.start(t)},move:function(t){var e=this.toolService.diagram;e.selector.move(t)},end:function(t,e){var i=this.toolService.diagram,n=this.toolService.hoveredItem,o=i.selector.bounds();n&&n.isSelected||e.ctrlKey||i.deselect(),o.isEmpty()||i.selectArea(o),i.selector.end()},getCursor:function(){return ot.arrow}}),T=F.extend({init:function(t){this.toolService=t,this.type="ConnectionTool"},tryActivate:function(){return this.toolService._hoveredConnector},start:function(t,e){var i=this.toolService,n=i.diagram,s=i._hoveredConnector,r=n._createConnection({},s._c,t);o(r)&&!n.trigger(ft,{shapes:[],connections:[r],connectionHandle:zt})&&n._addConnection(r)?(i._connectionManipulation(r,s._c.shape,!0),i._removeHover(),i.selectSingle(i.activeConnection,e),"touchmove"==e.type&&(n._cachedTouchTarget=s.visual)):(r.source(null),i.end(t))},move:function(t){var e=this.toolService,i=e.activeConnection;return i.target(t),e.diagram.trigger(pt,{shapes:[],connections:[i],connectionHandle:zt}),!0},end:function(t){var e,i=this.toolService,n=i.diagram,o=i.activeConnection,s=i.hoveredItem,r=i._hoveredConnector,a=n._cachedTouchTarget;o&&(e=r&&r._c!=o.sourceConnector?r._c:s&&s instanceof H.Shape?s.getConnector(rt)||s.getConnector(t):t,o.target(e),n.trigger(gt,{shapes:[],connections:[o],connectionHandle:zt})?(n.remove(o,!1),n.undoRedoService.pop()):(o.updateModel(),n._syncConnectionChanges()),i._connectionManipulation(),a&&(n._connectorsAdorner.visual.remove(a),n._cachedTouchTarget=null))},getCursor:function(){return ot.arrow}}),M=F.extend({init:function(t){this.toolService=t,this.type="ConnectionTool"},tryActivate:function(t,e){var i=this.toolService,n=i.diagram,o=n.options.selectable,s=i.hoveredItem,r=o!==!1&&s&&s.path&&!(s.isSelected&&e.ctrlKey);return r&&(this._c=s),r},start:function(t,e){var i,n,s,r=this.toolService,a=this._c;r.selectSingle(a,e),i=a.adorner,i&&(n=i._hitTest(t),s=Bt[n]),o(a)&&i&&!r.diagram.trigger(ft,{shapes:[],connections:[a],connectionHandle:s})?(this.handle=n,this.handleName=s,i.start(t)):(r.startPoint=t,r.end(t))},move:function(t){var e=this._c.adorner;if(o(this._c)&&e)return e.move(this.handle,t),this.toolService.diagram.trigger(pt,{shapes:[],connections:[this._c],connectionHandle:this.handleName}),!0},end:function(t){var e,i=this._c,n=i.adorner,s=this.toolService,r=s.diagram;n&&o(i)&&(e=n.stop(t),r.trigger(gt,{shapes:[],connections:[i],connectionHandle:this.handleName})?e.undo():(r.undoRedoService.add(e,!1),i.updateModel(),r._syncConnectionChanges()))},getCursor:function(){return ot.move}}),D=F.extend({init:function(t){this.diagram=t,this.tools=[new C(this),new M(this),new T(this),new k(this),new S(this)],this.activeTool=e},start:function(t,e){return e=J({},e),this.activeTool&&this.activeTool.end(t,e),this._updateHoveredItem(t),this._activateTool(t,e),this.activeTool.start(t,e),this._updateCursor(t),this.diagram.focus(),this.diagram.canvas.surface.suspendTracking(),this.startPoint=t,!0},move:function(t,e){e=J({},e);var i=!0;return this.activeTool&&(i=this.activeTool.move(t,e)),i&&this._updateHoveredItem(t),this._updateCursor(t),!0},end:function(t,i){return i=J({},i),this.activeTool&&this.activeTool.end(t,i),this.diagram.canvas.surface.resumeTracking(),this.activeTool=e,this._updateCursor(t),!0},keyDown:function(t,e){var n,o=this.diagram;if(e=J({ctrlKey:!1,metaKey:!1,altKey:!1},e),!e.ctrlKey&&!e.metaKey||e.altKey){if(46===t||8===t)return n=this.diagram._triggerRemove(o.select()),n.length&&(this.diagram.remove(n,!0),this.diagram._syncChanges(),this.diagram._destroyToolBar()),!0;if(27===t)return this._discardNewConnection(),o.deselect(),o._destroyToolBar(),!0}else{if(i(t,"a"))return o.selectAll(),o._destroyToolBar(),!0;if(i(t,"z"))return o.undo(),o._destroyToolBar(),!0;if(i(t,"y"))return o.redo(),o._destroyToolBar(),!0;i(t,"c")?(o.copy(),o._destroyToolBar()):i(t,"x")?(o.cut(),o._destroyToolBar()):i(t,"v")?(o.paste(),o._destroyToolBar()):i(t,"l")?(o.layout(),o._destroyToolBar()):i(t,"d")&&(o._destroyToolBar(),o.copy(),o.paste())}},wheel:function(t,e){var i=this.diagram,n=e.delta,o=i.zoom(),s=i.options,r=s.zoomRate,a={point:t,meta:e,zoom:o};if(!i.trigger(bt,a))return n<0?o+=r:o-=r,o=U.dataviz.round(Math.max(s.zoomMin,Math.min(s.zoomMax,o)),2),a.zoom=o,i.zoom(o,a),i.trigger(Ct,a),!0},setTool:function(t,e){t.toolService=this,this.tools[e]=t},selectSingle:function(t,e){var i,n=this.diagram,o=n.options.selectable;o&&!t.isSelected&&t.options.selectable!==!1&&(i=e.ctrlKey&&o.multiple!==!1,n.select(t,{addToSelection:i}))},_discardNewConnection:function(){this.newConnection&&(this.diagram.remove(this.newConnection),this.newConnection=e)},_activateTool:function(t,e){var i,n;for(i=0;i<this.tools.length;i++)if(n=this.tools[i],n.tryActivate(t,e)){this.activeTool=n;break}},_updateCursor:function(t){var e=this.diagram.element,i=this.activeTool?this.activeTool.getCursor(t):this.hoveredAdorner?this.hoveredAdorner._getCursor(t):this.hoveredItem?this.hoveredItem._getCursor(t):ot.arrow;e.css({cursor:i}),$.msie&&7==$.version&&(e[0].style.cssText=e[0].style.cssText)},_connectionManipulation:function(t,i,n){this.activeConnection=t,this.disabledShape=i,this.newConnection=n?this.activeConnection:e},_updateHoveredItem:function(t){var i=this._hitTest(t),n=this.diagram;i==this.hoveredItem||this.disabledShape&&i==this.disabledShape||(this.hoveredItem&&(n.trigger(xt,{item:this.hoveredItem}),this.hoveredItem._hover(!1)),i&&i.options.enable?(n.trigger(wt,{item:i}),this.hoveredItem=i,this.hoveredItem._hover(!0)):this.hoveredItem=e)},_removeHover:function(){this.hoveredItem&&(this.hoveredItem._hover(!1),this.hoveredItem=e)},_hitTest:function(t){var i,n,o,s,r=this.diagram;if(this._hoveredConnector&&(this._hoveredConnector._hover(!1),this._hoveredConnector=e),r._connectorsAdorner._visible&&(i=r._connectorsAdorner._hitTest(t)))return i;if(i=this.diagram._resizingAdorner._hitTest(t)){if(this.hoveredAdorner=r._resizingAdorner,0!==i.x||0!==i.y)return;i=e}else this.hoveredAdorner=e;if(!this.activeTool||"ConnectionTool"!==this.activeTool.type){for(s=[],o=0;o<r._selectedItems.length;o++)n=r._selectedItems[o],n instanceof H.Connection&&s.push(n);i=this._hitTestItems(s,t)}return i||this._hitTestElements(t)},_hitTestElements:function(t){var e,i,n,o,r=this.diagram,a=this._hitTestItems(r.shapes,t),h=this._hitTestItems(r.connections,t);return this.activeTool&&"ConnectionTool"==this.activeTool.type||!a||!h||s(a,t)||(i=r.mainLayer,n=it(a.visual,i.children),o=it(h.visual,i.children),e=n>o?a:h),e||a||h},_hitTestItems:function(t,e){var i,n,o;for(i=t.length-1;i>=0;i--)if(n=t[i],o=n._hitTest(e))return o}}),I=U.Class.extend({init:function(){}}),P=I.extend({init:function(t){var e=this;I.fn.init.call(e),this.connection=t},hitTest:function(t){var e=this.getBounds().inflate(st);return!!e.contains(t)&&H.Geometry.distanceToPolyline(t,this.connection.allPoints())<st},getBounds:function(){var t,e=this.connection.allPoints(),i=e[0],n=e[e.length-1],o=Math.max(i.x,n.x),s=Math.min(i.x,n.x),r=Math.min(i.y,n.y),a=Math.max(i.y,n.y);for(t=1;t<e.length-1;++t)o=Math.max(o,e[t].x),s=Math.min(s,e[t].x),r=Math.min(r,e[t].y),a=Math.max(a,e[t].y);return new q(s,r,o-s,a-r)}}),L=P.extend({init:function(t){var e=this;P.fn.init.call(e),this.connection=t},route:function(){}}),E=P.extend({SAME_SIDE_DISTANCE_RATIO:5,init:function(t){var e=this;P.fn.init.call(e),this.connection=t},routePoints:function(t,e,i,n){var o;return o=i&&n?this._connectorPoints(t,e,i,n):this._floatingPoints(t,e,i)},route:function(){var t=this.connection._resolvedSourceConnector,e=this.connection._resolvedTargetConnector,i=this.connection.sourcePoint(),n=this.connection.targetPoint(),o=this.routePoints(i,n,t,e);this.connection.points(o)},_connectorSides:[{name:"Top",axis:"y",boundsPoint:"topLeft",secondarySign:1},{name:"Left",axis:"x",boundsPoint:"topLeft",secondarySign:1},{name:"Bottom",axis:"y",boundsPoint:"bottomRight",secondarySign:-1},{name:"Right",axis:"x",boundsPoint:"bottomRight",secondarySign:-1}],_connectorSide:function(t,e){var i,n,o,s,r,a=t.position(),h=t.shape.bounds(Lt),c={topLeft:h.topLeft(),bottomRight:h.bottomRight()},d=this._connectorSides,l=tt.MAX_NUM;for(r=0;r<d.length;r++)s=d[r],o=s.axis,i=Math.round(Math.abs(a[o]-c[s.boundsPoint][o])),i<l?(l=i,n=s):i===l&&(a[o]-e[o])*s.secondarySign>(a[n.axis]-e[n.axis])*n.secondarySign&&(n=s);return n.name},_sameSideDistance:function(t){var e=t.shape.bounds(Lt);return Math.min(e.width,e.height)/this.SAME_SIDE_DISTANCE_RATIO},_connectorPoints:function(t,e,i,n){var o,s,r=this._connectorSide(i,e),a=this._connectorSide(n,t),h=e.x-t.x,c=e.y-t.y,d=this._sameSideDistance(i),l=[];return r===at||r==dt?a==at||a==dt?r==a?(s=r==at?Math.min(t.y,e.y)-d:Math.max(t.y,e.y)+d,l=[new Y(t.x,s),new Y(e.x,s)]):l=[new Y(t.x,t.y+c/2),new Y(e.x,t.y+c/2)]:l=[new Y(t.x,e.y)]:a==ct||a==ht?r==a?(o=r==ct?Math.min(t.x,e.x)-d:Math.max(t.x,e.x)+d,l=[new Y(o,t.y),new Y(o,e.y)]):l=[new Y(t.x+h/2,t.y),new Y(t.x+h/2,t.y+c)]:l=[new Y(e.x,t.y)],l},_floatingPoints:function(t,e,i){var n,o,s,r=i?this._connectorSide(i,e):null,a=this._startHorizontal(t,e,r),h=[t,t,e,e],c=e.x-t.x,d=e.y-t.y,l=h.length;for(s=1;s<l-1;++s)a?s%2!==0?(n=c/(l/2),o=0):(n=0,o=d/((l-1)/2)):s%2!==0?(n=0,o=d/(l/2)):(n=c/((l-1)/2),o=0),h[s]=new Y(h[s-1].x+n,h[s-1].y+o);return s--,h[l-2]=a&&s%2!==0||!a&&s%2===0?new Y(h[l-1].x,h[l-2].y):new Y(h[l-2].x,h[l-1].y),[h[1],h[2]]},_startHorizontal:function(t,e,i){var n;return n=null!==i&&(i===ht||i===ct)||Math.abs(t.x-e.x)>Math.abs(t.y-e.y)}}),z=F.extend({init:function(t,e){var i=this;i.diagram=t,i.options=J({},i.options,e),i.visual=new G,i.diagram._adorners.push(i)},refresh:function(){}}),B=z.extend({init:function(t,e){var i,n,o,s=this;s.connection=t,i=s.connection.diagram,s._ts=i.toolService,z.fn.init.call(s,i,e),n=s.connection.sourcePoint(),o=s.connection.targetPoint(),s.spVisual=new j(J(s.options.handles,{center:n})),s.epVisual=new j(J(s.options.handles,{center:o})),s.visual.append(s.spVisual),s.visual.append(s.epVisual)},options:{handles:{}},_getCursor:function(){return ot.move},start:function(t){switch(this.handle=this._hitTest(t),this.startPoint=t,this._initialSource=this.connection.source(),this._initialTarget=this.connection.target(),this.handle){case-1:this.connection.targetConnector&&this._ts._connectionManipulation(this.connection,this.connection.targetConnector.shape);break;case 1:this.connection.sourceConnector&&this._ts._connectionManipulation(this.connection,this.connection.sourceConnector.shape)}},move:function(t,e){switch(t){case-1:this.connection.source(e);break;case 1:this.connection.target(e);break;default:var i=e.minus(this.startPoint);this.startPoint=e,this.connection.sourceConnector||this.connection.source(this.connection.sourcePoint().plus(i)),this.connection.targetConnector||this.connection.target(this.connection.targetPoint().plus(i))}return this.refresh(),!0},stop:function(t){var i,n=this.diagram.toolService,o=n.hoveredItem;return i=n._hoveredConnector?n._hoveredConnector._c:o&&o instanceof H.Shape?o.getConnector(rt)||o.getConnector(t):t,this.handle===-1?this.connection.source(i):1===this.handle&&this.connection.target(i),this.handle=e,this._ts._connectionManipulation(),new l(this.connection,this._initialSource,this._initialTarget)},_hitTest:function(t){var e=this.connection.sourcePoint(),i=this.connection.targetPoint(),n=this.options.handles.width/2+st,o=this.options.handles.height/2+st,s=e.distanceTo(t),r=i.distanceTo(t),a=new q(e.x,e.y).inflate(n,o).contains(t),h=new q(i.x,i.y).inflate(n,o).contains(t),c=0;return a&&(!h||s<r)?c=-1:h&&(!a||r<s)&&(c=1),c},refresh:function(){this.spVisual.redraw({center:this.diagram.modelToLayer(this.connection.sourcePoint())}),this.epVisual.redraw({center:this.diagram.modelToLayer(this.connection.targetPoint())})}}),R=z.extend({init:function(t,e){var i=this;z.fn.init.call(i,t,e),i._refreshHandler=function(t){t.item==i.shape&&i.refresh()}},show:function(t){var e,i,n,o=this;for(o._visible=!0,o.shape=t,o.diagram.bind(_t,o._refreshHandler),e=t.connectors.length,o.connectors=[],o._clearVisual(),i=0;i<e;i++)n=new A(t.connectors[i]),o.connectors.push(n),o.visual.append(n.visual);o.visual.visible(!0),o.refresh()},_clearVisual:function(){var t=this;t.diagram._cachedTouchTarget?t._keepCachedTouchTarget():t.visual.clear()},_keepCachedTouchTarget:function(){var t,e=this,i=e.visual.children,n=i.length,o=it(e.diagram._cachedTouchTarget,i);for(t=n-1;t>=0;t--)t!=o&&e.visual.remove(i[t])},destroy:function(){var t=this;t.diagram.unbind(_t,t._refreshHandler),t.shape=e,t._visible=e,t.visual.visible(!1)},_hitTest:function(t){var e,i;for(i=0;i<this.connectors.length;i++)if(e=this.connectors[i],e._hitTest(t)){e._hover(!0),this.diagram.toolService._hoveredConnector=e;break}},refresh:function(){if(this.shape){var e=this.shape.bounds();e=this.diagram.modelToLayer(e),this.visual.position(e.topLeft()),t.each(this.connectors,function(){this.refresh()})}}}),N=z.extend({init:function(t,e){var i=this;z.fn.init.call(i,t,e),i._manipulating=!1,i.map=[],i.shapes=[],i._initSelection(),i._createHandles(),i.redraw(),i.diagram.bind("select",function(t){i._initialize(t.selected)}),i._refreshHandler=function(){i._internalChange||(i.refreshBounds(),i.refresh())},i._rotatedHandler=function(){1==i.shapes.length&&(i._angle=i.shapes[0].rotate().angle),i._refreshHandler()},i.diagram.bind(_t,i._refreshHandler).bind(mt,i._rotatedHandler),i.refreshBounds(),i.refresh()},options:{handles:{fill:{color:"#fff"},stroke:{color:"#282828"},height:7,width:7,hover:{fill:{color:"#282828"},stroke:{color:"#282828"}}},selectable:{stroke:{color:"#778899",width:1,dashType:"dash"},fill:{color:It}},offset:10},_initSelection:function(){var t=this,e=t.diagram,i=e.options.selectable,n=J({},t.options.selectable,i);t.rect=new K(n),t.visual.append(t.rect)},_resizable:function(){return this.options.editable&&this.options.editable.resize!==!1},_handleOptions:function(){return(this.options.editable.resize||{}).handles||this.options.handles},_createHandles:function(){var t,e,i,n;if(this._resizable())for(t=this._handleOptions(),n=-1;n<=1;n++)for(i=-1;i<=1;i++)0===n&&0===i||(e=new K(t),e.drawingElement._hover=nt(this._hover,this),this.map.push({x:n,y:i,visual:e}),this.visual.append(e))},bounds:function(t){return t?(this._innerBounds=t.clone(),this._bounds=this.diagram.modelToLayer(t).inflate(this.options.offset,this.options.offset),e):this._bounds},_hitTest:function(t){var e,i,n,o,s=this.diagram.modelToLayer(t),r=this.map.length;if(this._angle&&(s=s.clone().rotate(this._bounds.center(),this._angle)),this._resizable())for(e=0;e<r;e++)if(o=this.map[e],i=new Y(o.x,o.y),n=this._getHandleBounds(i),n.offset(this._bounds.x,this._bounds.y),n.contains(s))return i;if(this._bounds.contains(s))return new Y(0,0)},_getHandleBounds:function(t){if(this._resizable()){var e=this._handleOptions(),i=e.width,n=e.height,o=new q(0,0,i,n);return t.x<0?o.x=-i/2:0===t.x?o.x=Math.floor(this._bounds.width/2)-i/2:t.x>0&&(o.x=this._bounds.width+1-i/2),t.y<0?o.y=-n/2:0===t.y?o.y=Math.floor(this._bounds.height/2)-n/2:t.y>0&&(o.y=this._bounds.height+1-n/2),o}},_getCursor:function(t){var e,i=this._hitTest(t);if(i&&i.x>=-1&&i.x<=1&&i.y>=-1&&i.y<=1&&this._resizable()){if(e=this._angle,e&&(e=360-e,i.rotate(new Y(0,0),e),i=new Y(Math.round(i.x),Math.round(i.y))),i.x==-1&&i.y==-1)return"nw-resize";if(1==i.x&&1==i.y)return"se-resize";if(i.x==-1&&1==i.y)return"sw-resize";if(1==i.x&&i.y==-1)return"ne-resize";if(0===i.x&&i.y==-1)return"n-resize";if(0===i.x&&1==i.y)return"s-resize";if(1==i.x&&0===i.y)return"e-resize";if(i.x==-1&&0===i.y)return"w-resize"}return this._manipulating?ot.move:ot.select},_initialize:function(){var t,e,i=this,n=i.diagram.select();for(i.shapes=[],t=0;t<n.length;t++)e=n[t],e instanceof H.Shape&&(i.shapes.push(e),e._rotationOffset=new Y);i._angle=1==i.shapes.length?i.shapes[0].rotate().angle:0,i._startAngle=i._angle,i._rotates(),i._positions(),i.refreshBounds(),i.refresh(),i.redraw()},_rotates:function(){var t,e,i=this;for(i.initialRotates=[],t=0;t<i.shapes.length;t++)e=i.shapes[t],i.initialRotates.push(e.rotate().angle)},_positions:function(){var t,e,i=this;for(i.initialStates=[],t=0;t<i.shapes.length;t++)e=i.shapes[t],i.initialStates.push(e.bounds())},_hover:function(t,e){if(this._resizable()){var i=this._handleOptions(),n=i.hover,o=i.stroke,s=i.fill;t&&W.isDefined(n.stroke)&&(o=J({},o,n.stroke)),t&&W.isDefined(n.fill)&&(s=n.fill),e.stroke(o.color,o.width,o.opacity),e.fill(s.color,s.opacity)}},start:function(t){var e,i;for(this._sp=t,this._cp=t,this._lp=t,this._manipulating=!0,this._internalChange=!0,this.shapeStates=[],e=0;e<this.shapes.length;e++)i=this.shapes[e],this.shapeStates.push(i.bounds())},redraw:function(){var t,e,i=this._resizable();for(t=0;t<this.map.length;t++)e=this.map[t],e.visual.visible(i)},angle:function(t){return et(t)&&(this._angle=t),this._angle},rotate:function(){var t,e,i=this._innerBounds.center(),n=this.angle();for(this._internalChange=!0,t=0;t<this.shapes.length;t++)e=this.shapes[t],n=(n+this.initialRotates[t]-this._startAngle)%360,e.rotate(n,i);this.refresh()},move:function(t,i){var s,r,a,h,c,d,l,u,f,p,g,m,_,v,y=new Y,w=new Y,x=0;if(t.y===-2&&t.x===-1){for(h=this._innerBounds.center(),this._angle=this._truncateAngle(W.findAngle(h,i)),d=0;d<this.shapes.length;d++)c=this.shapes[d],l=(this._angle+this.initialRotates[d]-this._startAngle)%360,c.rotate(l,h),c.hasOwnProperty("layout")&&c.layout(c),this._rotating=!0;this.refresh()}else{if(this.shouldSnap()){if(m=this._truncateDistance(i.minus(this._lp)),0===m.x&&0===m.y)return this._cp=i,e;s=m,this._lp=new Y(this._lp.x+m.x,this._lp.y+m.y)}else s=i.minus(this._cp);for(this.isDragHandle(t)?(w=y=s,r=!0):(this._angle&&s.rotate(new Y(0,0),this._angle),t.x==-1?y.x=s.x:1==t.x&&(w.x=s.x),t.y==-1?y.y=s.y:1==t.y&&(w.y=s.y)),r||(f=n(t,this._innerBounds),p=(this._innerBounds.width+s.x*t.x)/this._innerBounds.width,g=(this._innerBounds.height+s.y*t.y)/this._innerBounds.height),d=0;d<this.shapes.length;d++){if(c=this.shapes[d],a=c.bounds(),r){if(!o(c))continue;u=this._displaceBounds(a,y,w,r)}else u=a.clone(),u.scale(p,g,f,this._innerBounds.center(),c.rotate().angle),_=u.center(),_.rotate(a.center(),-this._angle),u=new q(_.x-u.width/2,_.y-u.height/2,u.width,u.height);u.width>=c.options.minWidth&&u.height>=c.options.minHeight&&(v=a,c.bounds(u),c.hasOwnProperty("layout")&&c.layout(c,v,u),v.width===u.width&&v.height===u.height||c.rotate(c.rotate().angle),x+=1)}x&&(x==d?(u=this._displaceBounds(this._innerBounds,y,w,r),this.bounds(u)):this.refreshBounds(),this.refresh()),this._positions()}this._cp=i},isDragHandle:function(t){return 0===t.x&&0===t.y},cancel:function(){var t,i=this.shapes,n=this.shapeStates;for(t=0;t<i.length;t++)i[t].bounds(n[t]);this.refreshBounds(),this.refresh(),this._manipulating=e,this._internalChange=e,this._rotating=e},_truncatePositionToGuides:function(t){return this.diagram.ruler?this.diagram.ruler.truncatePositionToGuides(t):t},_truncateSizeToGuides:function(t){return this.diagram.ruler?this.diagram.ruler.truncateSizeToGuides(t):t},_truncateAngle:function(t){var e=this.snapOptions(),i=Math.max(e.angle||ut,yt);return e?Math.floor(t%360/i)*i:t%360},_truncateDistance:function(t){var e,i;return t instanceof H.Point?new H.Point(this._truncateDistance(t.x),this._truncateDistance(t.y)):(e=this.snapOptions()||{},i=Math.max(e.size||lt,vt),e?Math.floor(t/i)*i:t)},snapOptions:function(){var t=this.diagram.options.editable,e=((t||{}).drag||{}).snap||{};return e},shouldSnap:function(){var t=this.diagram.options.editable,e=(t||{}).drag,i=(e||{}).snap;return t!==!1&&e!==!1&&i!==!1},_displaceBounds:function(t,e,i,n){var o,s=t.topLeft().plus(e),r=t.bottomRight().plus(i),a=q.fromPoints(s,r);return n||(o=a.center(),o.rotate(t.center(),-this._angle),a=new q(o.x-a.width/2,o.y-a.height/2,a.width,a.height)),a},stop:function(){var t,i,n,o;if(this._cp!=this._sp)if(this._rotating)t=new v(this,this.shapes,this.initialRotates),this._rotating=!1;else if(this._diffStates()){if(this.diagram.ruler)for(i=0;i<this.shapes.length;i++)n=this.shapes[i],o=n.bounds(),o=this._truncateSizeToGuides(this._truncatePositionToGuides(o)),n.bounds(o),this.refreshBounds(),this.refresh();for(i=0;i<this.shapes.length;i++)n=this.shapes[i],n.updateModel();t=new p(this.shapes,this.shapeStates,this),this.diagram._syncShapeChanges()}return this._manipulating=e,this._internalChange=e,this._rotating=e,t},_diffStates:function(){var t,e=this.shapes,i=this.shapeStates;for(t=0;t<e.length;t++)if(!e[t].bounds().equals(i[t]))return!0;return!1},refreshBounds:function(){var t=1==this.shapes.length?this.shapes[0].bounds().clone():this.diagram.boundingBox(this.shapes,!0);this.bounds(t)},refresh:function(){var e,i,n,o,s=this;this.shapes.length>0?(i=this.bounds(),this.visual.visible(!0),this.visual.position(i.topLeft()),t.each(this.map,function(){e=s._getHandleBounds(new Y(this.x,this.y)),this.visual.position(e.topLeft())}),this.visual.position(i.topLeft()),n=new Y(i.width/2,i.height/2),this.visual.rotate(this._angle,n),this.rect.redraw({width:i.width,height:i.height}),this.rotationThumb&&(o=this.options.editable.rotate.thumb,this._rotationThumbBounds=new q(i.center().x,i.y+o.y,0,0).inflate(o.width),this.rotationThumb.redraw({x:i.width/2-o.width/2}))):this.visual.visible(!1)}}),O=F.extend({init:function(t){var e=t.options.selectable;this.options=J({},this.options,e),this.visual=new K(this.options),this.diagram=t},options:{stroke:{color:"#778899",width:1,dashType:"dash"},fill:{color:It}},start:function(t){this._sp=this._ep=t,this.refresh(),this.diagram._adorn(this,!0)},end:function(){this._sp=this._ep=e,this.diagram._adorn(this,!1)},bounds:function(t){return t&&(this._bounds=t),this._bounds},move:function(t){this._ep=t,this.refresh()},refresh:function(){if(this._sp){var t=q.fromPoints(this.diagram.modelToLayer(this._sp),this.diagram.modelToLayer(this._ep));this.bounds(q.fromPoints(this._sp,this._ep)),this.visual.position(t.topLeft()),this.visual.redraw({height:t.height+1,width:t.width+1})}}}),A=F.extend({init:function(t){this.options=J({},t.options),this._c=t,this.visual=new j(this.options),this.refresh()},_hover:function(t){var e=this.options,i=e.hover,n=e.stroke,o=e.fill;t&&W.isDefined(i.stroke)&&(n=J({},n,i.stroke)),t&&W.isDefined(i.fill)&&(o=i.fill),this.visual.redraw({stroke:n,fill:o})},refresh:function(){var t=this._c.shape.diagram.modelToView(this._c.position()),e=t.minus(this._c.shape.bounds("transformed").topLeft()),i=new q(t.x,t.y,0,0);i.inflate(this.options.width/2,this.options.height/2),this._visualBounds=i,this.visual.redraw({center:new Y(e.x,e.y)})},_hitTest:function(t){var e=this._c.shape.diagram.modelToView(t);return this._visualBounds.contains(e)}}),J(H,{CompositeUnit:c,TransformUnit:p,PanUndoUnit:_,AddShapeUnit:m,AddConnectionUnit:g,DeleteShapeUnit:f,DeleteConnectionUnit:u,ConnectionEditAdorner:B,ConnectionTool:T,ConnectorVisual:A,UndoRedoService:x,ResizingAdorner:N,Selector:O,ToolService:D,ConnectorsAdorner:R,LayoutUndoUnit:h,ConnectionEditUnit:d,ToFrontUnit:y,ToBackUnit:w,ConnectionRouterBase:I,PolylineRouter:L,CascadingRouter:E,SelectionTool:k,ScrollerTool:C,PointerTool:S,ConnectionEditTool:M,RotateUnit:v})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/diagram/layout.min",["dataviz/diagram/math.min"],t)}(function(){!function(t,e){var i=window.kendo,n=i.dataviz.diagram,o=n.Graph,s=n.Node,r=n.Link,a=i.deepExtend,h=n.Size,c=n.Rect,d=n.Dictionary,l=n.Set,u=n.Graph,f=n.Utils,p=n.Point,g=1e-6,m=Math.PI/180,_=f.contains,v=t.grep,y=i.Class.extend({defaultOptions:{type:"Tree",subtype:"Down",roots:null,animate:!1,limitToView:!1,friction:.9,nodeDistance:50,iterations:300,horizontalSeparation:90,verticalSeparation:50,underneathVerticalTopOffset:15,underneathHorizontalOffset:15,underneathVerticalSeparation:15,grid:{width:1500,offsetX:50,offsetY:50,componentSpacingX:20,componentSpacingY:20},layerSeparation:50,layeredIterations:2,startRadialAngle:0,endRadialAngle:360,radialSeparation:150,radialFirstLevelSeparation:200,keepComponentsInOneRadialLayout:!1,ignoreContainers:!0,layoutContainerChildren:!1,ignoreInvisible:!0,animateTransitions:!1},init:function(){},gridLayoutComponents:function(t){var e,i,n,o,s,r,a,h,c,d,l,u,g,m,_;if(!t)throw"No components supplied.";for(f.forEach(t,function(t){t.calcBounds()}),t.sort(function(t,e){return e.bounds.width-t.bounds.width}),e=this.options.grid.width,i=this.options.grid.componentSpacingX,n=this.options.grid.componentSpacingY,o=0,s=this.options.grid.offsetX,r=this.options.grid.offsetY,a=s,h=r,d=[],l=[];t.length>0;){for(a>=e&&(a=s,h+=o+n,o=0),u=t.pop(),this.moveToOffset(u,new p(a,h)),c=0;c<u.nodes.length;c++)l.push(u.nodes[c]);for(c=0;c<u.links.length;c++)d.push(u.links[c]);g=u.bounds,m=g.height,(m<=0||isNaN(m))&&(m=0),_=g.width,(_<=0||isNaN(_))&&(_=0),m>=o&&(o=m),a+=_+i}return{nodes:l,links:d}},moveToOffset:function(t,e){var i,n,o,s,r,a,h,d,l=t.bounds,u=e.x-l.x,f=e.y-l.y;for(i=0;i<t.nodes.length;i++)o=t.nodes[i],s=o.bounds(),0===s.width&&0===s.height&&0===s.x&&0===s.y&&(s=new c(0,0,0,0)),s.x+=u,s.y+=f,o.bounds(s);for(i=0;i<t.links.length;i++)if(r=t.links[i],r.points){for(a=[],h=r.points,n=0;n<h.length;n++)d=h[n],d.x+=u,d.y+=f,a.push(d);r.points=a}return this.currentHorizontalOffset+=l.width+this.options.grid.offsetX,new p(u,f)},transferOptions:function(t){this.options=i.deepExtend({},this.defaultOptions),f.isUndefined(t)||(this.options=i.deepExtend(this.options,t||{}))}}),w=i.Class.extend({init:function(t){this.nodeMap=new d,this.shapeMap=new d,this.nodes=[],this.edges=[],this.edgeMap=new d,this.finalNodes=[],this.finalLinks=[],this.ignoredConnections=[],this.ignoredShapes=[],this.hyperMap=new d,this.hyperTree=new o,this.finalGraph=null,this.diagram=t},convert:function(t){if(f.isUndefined(this.diagram))throw"No diagram to convert.";return this.options=i.deepExtend({ignoreInvisible:!0,ignoreContainers:!0,layoutContainerChildren:!1},t||{}),this.clear(),this._renormalizeShapes(),this._renormalizeConnections(),this.finalNodes=new d(this.nodes),this.finalLinks=new d(this.edges),this.finalGraph=new o,this.finalNodes.forEach(function(t){this.finalGraph.addNode(t)},this),this.finalLinks.forEach(function(t){this.finalGraph.addExistingLink(t)},this),this.finalGraph},mapConnection:function(t){return this.edgeMap.get(t.id)},mapShape:function(t){return this.nodeMap.get(t.id)},getEdge:function(t,e){return f.first(t.links,function(i){return i.getComplement(t)===e})},clear:function(){this.finalGraph=null,this.hyperTree=!this.options.ignoreContainers&&this.options.layoutContainerChildren?new u:null,this.hyperMap=!this.options.ignoreContainers&&this.options.layoutContainerChildren?new d:null,this.nodeMap=new d,this.shapeMap=new d,this.nodes=[],this.edges=[],this.edgeMap=new d,this.ignoredConnections=[],this.ignoredShapes=[],this.finalNodes=[],this.finalLinks=[]},listToRoot:function(t){var e=[],i=t.container;if(!i)return e;for(e.push(i);i.parentContainer;)i=i.parentContainer,e.push(i);return e.reverse(),e},firstNonIgnorableContainer:function(t){return t.isContainer&&!this._isIgnorableItem(t)?t:t.parentContainer?this.firstNonIgnorableContainer(t.parentContainer):null},isContainerConnection:function(t,e){return!(!t.isContainer||!this.isDescendantOf(t,e))||e.isContainer&&this.isDescendantOf(e,t)},isDescendantOf:function(t,e){var i,n,o,s;if(!t.isContainer)throw"Expecting a container.";
if(t===e)return!1;if(_(t.children,e))return!0;for(i=[],n=0,o=t.children.length;n<o;n++)s=t.children[n],s.isContainer&&this.isDescendantOf(s,e)&&i.push(s);return i.length>0},isIgnorableItem:function(t){return this.options.ignoreInvisible?(!t.isCollapsed||!this._isVisible(t))&&!(!t.isCollapsed&&this._isVisible(t)):t.isCollapsed&&!this._isTop(t)},isShapeMapped:function(t){return t.isCollapsed&&!this._isVisible(t)&&!this._isTop(t)},leastCommonAncestor:function(t,e){var i,n,o,s,r,a;if(!t)throw"Parameter should not be null.";if(!e)throw"Parameter should not be null.";if(!this.hyperTree)throw"No hypertree available.";if(i=this.listToRoot(t),n=this.listToRoot(e),o=null,f.isEmpty(i)||f.isEmpty(n))return this.hyperTree.root.data;for(s=i[0],r=n[0],a=0;s===r&&(o=i[a],a++,!(a>=i.length||a>=n.length));)s=i[a],r=n[a];return o?v(this.hyperTree.nodes,function(t){return t.data.container===o}):this.hyperTree.root.data},_isTop:function(t){return!t.parentContainer},_isVisible:function(t){return!!t.visible()&&(t.parentContainer?this._isVisible(t.parentContainer):t.visible())},_isCollapsed:function(t){return!(!t.isContainer||!t.isCollapsed)||t.parentContainer&&this._isCollapsed(t.parentContainer)},_renormalizeShapes:function(){var t,e,i,n;if(!this.options.ignoreContainers)throw"Containers are not supported yet, but stay tuned.";for(t=0,e=this.diagram.shapes.length;t<e;t++)i=this.diagram.shapes[t],this.options.ignoreInvisible&&!this._isVisible(i)||i.isContainer?this.ignoredShapes.push(i):(n=new s(i.id,i),n.isVirtual=!1,this.nodeMap.add(i.id,n),this.nodes.push(n))},_renormalizeConnections:function(){var t,e,i,n,o,s,a,h;if(0!==this.diagram.connections.length)for(t=0,e=this.diagram.connections.length;t<e;t++)if(i=this.diagram.connections[t],this.isIgnorableItem(i))this.ignoredConnections.push(i);else if(n=i.sourceConnector?i.sourceConnector.shape:null,o=i.targetConnector?i.targetConnector.shape:null,n&&o)if(!_(this.ignoredShapes,n)||this.shapeMap.containsKey(n))if(!_(this.ignoredShapes,o)||this.shapeMap.containsKey(o))if(this.shapeMap.containsKey(n)&&(n=this.shapeMap[n]),this.shapeMap.containsKey(o)&&(o=this.shapeMap[o]),s=this.mapShape(n),a=this.mapShape(o),s===a||this.areConnectedAlready(s,a))this.ignoredConnections.push(i);else{if(null===s||null===a)throw"A shape was not mapped to a node.";if(!this.options.ignoreContainers)throw"Containers are not supported yet, but stay tuned.";s.isVirtual||a.isVirtual?this.ignoredConnections.push(i):(h=new r(s,a,i.id,i),this.edgeMap.add(i.id,h),this.edges.push(h))}else this.ignoredConnections.push(i);else this.ignoredConnections.push(i);else this.ignoredConnections.push(i)},areConnectedAlready:function(t,e){return f.any(this.edges,function(i){return i.source===t&&i.target===e||i.source===e&&i.target===t})}}),x=y.extend({init:function(t){var e=this;if(y.fn.init.call(e),f.isUndefined(t))throw"Diagram is not specified.";this.diagram=t},layout:function(t){var e,i,o,s,r,a;if(this.transferOptions(t),e=new w(this.diagram),i=e.convert(t),!i.isEmpty()&&(o=i.getConnectedComponents(),!f.isEmpty(o))){for(s=0;s<o.length;s++)r=o[s],this.layoutGraph(r,t);return a=this.gridLayoutComponents(o),new n.LayoutState(this.diagram,a)}},layoutGraph:function(t,e){var i,n,o;for(f.isDefined(e)&&this.transferOptions(e),this.graph=t,i=9*this.options.nodeDistance,this.temperature=i,n=this._expectedBounds(),this.width=n.width,this.height=n.height,o=0;o<this.options.iterations;o++)this.refineStage=o>=5*this.options.iterations/6,this.tick(),this.temperature=this.refineStage?i/30:i*(1-o/(2*this.options.iterations))},tick:function(){var t,e,i;for(t=0;t<this.graph.nodes.length;t++)this._repulsion(this.graph.nodes[t]);for(t=0;t<this.graph.links.length;t++)this._attraction(this.graph.links[t]);for(t=0;t<this.graph.nodes.length;t++){if(e=this.graph.nodes[t],i=Math.sqrt(e.dx*e.dx+e.dy*e.dy),0===i)return;e.x+=Math.min(i,this.temperature)*e.dx/i,e.y+=Math.min(i,this.temperature)*e.dy/i,this.options.limitToView&&(e.x=Math.min(this.width,Math.max(e.width/2,e.x)),e.y=Math.min(this.height,Math.max(e.height/2,e.y)))}},_shake:function(t){var e=Math.random()*this.options.nodeDistance/4,i=2*Math.random()*Math.PI;t.x+=e*Math.cos(i),t.y-=e*Math.sin(i)},_InverseSquareForce:function(t,e,i){var n,o,s,r,a,h,c;return this.refineStage?(o=e.x-i.x,s=e.y-i.y,r=e.width/2,a=e.height/2,h=i.width/2,c=i.height/2,n=Math.pow(o,2)/Math.pow(r+h+this.options.nodeDistance,2)+Math.pow(s,2)/Math.pow(a+c+this.options.nodeDistance,2)):n=Math.pow(t,2)/Math.pow(this.options.nodeDistance,2),4*n/3},_SquareForce:function(t,e,i){return 1/this._InverseSquareForce(t,e,i)},_repulsion:function(t){t.dx=0,t.dy=0,f.forEach(this.graph.nodes,function(e){var i,n,o,s;if(e!==t){for(;t.x===e.x&&t.y===e.y;)this._shake(e);i=t.x-e.x,n=t.y-e.y,o=Math.sqrt(i*i+n*n),s=2*this._SquareForce(o,t,e),t.dx+=i/o*s,t.dy+=n/o*s}},this)},_attraction:function(t){var e,i,n,o,s,r,a=t.target,h=t.source;if(h!==a){for(;h.x===a.x&&h.y===a.y;)this._shake(a);e=h.x-a.x,i=h.y-a.y,n=Math.sqrt(e*e+i*i),o=5*this._InverseSquareForce(n,h,a),s=e/n*o,r=i/n*o,a.dx+=s,a.dy+=r,h.dx-=s,h.dy-=r}},_expectedBounds:function(){var t,e,i,n,o,s=this.graph.nodes.length,r=1.5,a=4;return 0===s?t:(t=f.fold(this.graph.nodes,function(t,e){var i=e.width*e.height;return i>0?t+=Math.sqrt(i):0},0,this),e=t/s,i=e*Math.ceil(Math.sqrt(s)),n=i*Math.sqrt(r),o=i/Math.sqrt(r),{width:n*a,height:o*a})}}),b=i.Class.extend({init:function(t){this.center=null,this.options=t},layout:function(t,e){if(this.graph=t,this.graph.nodes&&0!==this.graph.nodes.length){if(!_(this.graph.nodes,e))throw"The given root is not in the graph.";this.center=e,this.graph.cacheRelationships(),this.layoutSwitch()}},layoutLeft:function(t){var e,i,n,o,s,r,a,c;for(this.setChildrenDirection(this.center,"Left",!1),this.setChildrenLayout(this.center,"Default",!1),e=0,i=0,o=0;o<t.length;o++)s=t[o],s.TreeDirection="Left",r=this.measure(s,h.Empty),i=Math.max(i,r.Width),e+=r.height+this.options.verticalSeparation;for(e-=this.options.verticalSeparation,a=this.center.x-this.options.horizontalSeparation,n=this.center.y+(this.center.height-e)/2,o=0;o<t.length;o++)s=t[o],c=new p(a-s.Size.width,n),this.arrange(s,c),n+=s.Size.height+this.options.verticalSeparation},layoutRight:function(t){var e,i,n,o,s,r,a,c;for(this.setChildrenDirection(this.center,"Right",!1),this.setChildrenLayout(this.center,"Default",!1),e=0,i=0,o=0;o<t.length;o++)s=t[o],s.TreeDirection="Right",r=this.measure(s,h.Empty),i=Math.max(i,r.Width),e+=r.height+this.options.verticalSeparation;for(e-=this.options.verticalSeparation,a=this.center.x+this.options.horizontalSeparation+this.center.width,n=this.center.y+(this.center.height-e)/2,o=0;o<t.length;o++)s=t[o],c=new p(a,n),this.arrange(s,c),n+=s.Size.height+this.options.verticalSeparation},layoutUp:function(t){var e,i,n,o,s,r,a;for(this.setChildrenDirection(this.center,"Up",!1),this.setChildrenLayout(this.center,"Default",!1),e=0,o=0;o<t.length;o++)n=t[o],n.TreeDirection="Up",s=this.measure(n,h.Empty),e+=s.width+this.options.horizontalSeparation;for(e-=this.options.horizontalSeparation,r=this.center.x+this.center.width/2-e/2,o=0;o<t.length;o++)n=t[o],i=this.center.y-this.options.verticalSeparation-n.Size.height,a=new p(r,i),this.arrange(n,a),r+=n.Size.width+this.options.horizontalSeparation},layoutDown:function(t){var e,i,n,o,s,r,a;for(this.setChildrenDirection(this.center,"Down",!1),this.setChildrenLayout(this.center,"Default",!1),n=0,i=0;i<t.length;i++)e=t[i],e.treeDirection="Down",s=this.measure(e,h.Empty),n+=s.width+this.options.horizontalSeparation;for(n-=this.options.horizontalSeparation,r=this.center.x+this.center.width/2-n/2,o=this.center.y+this.options.verticalSeparation+this.center.height,i=0;i<t.length;i++)e=t[i],a=new p(r,o),this.arrange(e,a),r+=e.Size.width+this.options.horizontalSeparation},layoutRadialTree:function(){var t,e;if(this.setChildrenDirection(this.center,"Radial",!1),this.setChildrenLayout(this.center,"Default",!1),this.previousRoot=null,t=this.options.startRadialAngle*m,e=this.options.endRadialAngle*m,e<=t)throw"Final angle should not be less than the start angle.";this.maxDepth=0,this.origin=new p(this.center.x,this.center.y),this.calculateAngularWidth(this.center,0),this.maxDepth>0&&this.radialLayout(this.center,this.options.radialFirstLevelSeparation,t,e),this.center.Angle=e-t},tipOverTree:function(t,e){var i,n,o,s,r,a,c;for(f.isUndefined(e)&&(e=0),this.setChildrenDirection(this.center,"Down",!1),this.setChildrenLayout(this.center,"Default",!1),this.setChildrenLayout(this.center,"Underneath",!1,e),i=0,s=0;s<t.length;s++)o=t[s],o.TreeDirection="Down",r=this.measure(o,h.Empty),i+=r.width+this.options.horizontalSeparation;for(i-=this.options.horizontalSeparation,i-=t[t.length-1].width,i+=t[t.length-1].associatedShape.bounds().width,a=this.center.x+this.center.width/2-i/2,n=this.center.y+this.options.verticalSeparation+this.center.height,s=0;s<t.length;s++)o=t[s],c=new p(a,n),this.arrange(o,c),a+=o.Size.width+this.options.horizontalSeparation},calculateAngularWidth:function(t,e){var i,n,o,s,r,a,h;if(e>this.maxDepth&&(this.maxDepth=e),i=0,n=1e3,o=1e3,s=0===e?0:Math.sqrt(n*n+o*o)/e,t.children.length>0){for(r=0,a=t.children.length;r<a;r++)h=t.children[r],i+=this.calculateAngularWidth(h,e+1);i=Math.max(s,i)}else i=s;return t.sectorAngle=i,i},sortChildren:function(t){var e,i,n,o,s,r,a,h,c,d,l,u=0;if(t.parents.length>1)throw"Node is not part of a tree.";if(i=t.parents[0],i&&(n=new p(i.x,i.y),o=new p(t.x,t.y),u=this.normalizeAngle(Math.atan2(n.y-o.y,n.x-o.x))),s=t.children.length,0===s)return null;for(r=[],a=[],e=0;e<s;++e)h=t.children[e],c=new p(h.x,h.y),a[e]=e,r[e]=this.normalizeAngle(-u+Math.atan2(c.y-c.y,c.x-c.x));for(f.bisort(r,a),d=[],l=t.children,e=0;e<s;++e)d.push(l[a[e]]);return d},normalizeAngle:function(t){for(;t>2*Math.PI;)t-=2*Math.PI;for(;t<0;)t+=2*Math.PI;return t},radialLayout:function(t,e,i,n){var o,s,r,a,h,c=n-i,d=c/2,l=t.sectorAngle,u=0,f=this.sortChildren(t);for(o=0,s=f.length;o<s;o++)r=f[o],a=r,h=a.sectorAngle/l,r.children.length>0&&this.radialLayout(r,e+this.options.radialSeparation,i+u*c,i+(u+h)*c),this.setPolarLocation(r,e,i+u*c+h*d),a.angle=h*c,u+=h},setPolarLocation:function(t,e,i){t.x=this.origin.x+e*Math.cos(i),t.y=this.origin.y+e*Math.sin(i),t.BoundingRectangle=new c(t.x,t.y,t.width,t.height)},setChildrenDirection:function(t,e,i){var n=t.treeDirection;this.graph.depthFirstTraversal(t,function(t){t.treeDirection=e}),i||(t.treeDirection=n)},setChildrenLayout:function(t,e,i,n){f.isUndefined(n)&&(n=0);var o=t.childrenLayout;n>0?(this.graph.assignLevels(t),this.graph.depthFirstTraversal(t,function(t){t.level>=n+1&&(t.childrenLayout=e)})):(this.graph.depthFirstTraversal(t,function(t){t.childrenLayout=e}),i||(t.childrenLayout=o))},measure:function(t,e){var i,n,o,s,r,a,c,d=0,l=0,u=new h(0,0);if(!t)throw"";if(n=t.associatedShape.bounds(),o=n.width,s=n.height,1!==t.parents.length)throw"Node not in a spanning tree.";if(r=t.parents[0],"Undefined"===t.treeDirection&&(t.treeDirection=r.treeDirection),f.isEmpty(t.children))u=new h(Math.abs(o)<g?50:o,Math.abs(s)<g?25:s);else if(1===t.children.length){switch(t.treeDirection){case"Radial":i=this.measure(t.children[0],e),d=o+this.options.radialSeparation*Math.cos(t.AngleToParent)+i.width,l=s+Math.abs(this.options.radialSeparation*Math.sin(t.AngleToParent))+i.height;break;case"Left":case"Right":switch(t.childrenLayout){case"TopAlignedWithParent":break;case"BottomAlignedWithParent":break;case"Underneath":i=this.measure(t.children[0],e),d=o+i.width+this.options.underneathHorizontalOffset,l=s+this.options.underneathVerticalTopOffset+i.height;break;case"Default":i=this.measure(t.children[0],e),d=o+this.options.horizontalSeparation+i.width,l=Math.max(s,i.height);break;default:throw"Unhandled TreeDirection in the Radial layout measuring."}break;case"Up":case"Down":switch(t.childrenLayout){case"TopAlignedWithParent":case"BottomAlignedWithParent":break;case"Underneath":i=this.measure(t.children[0],e),d=Math.max(o,i.width+this.options.underneathHorizontalOffset),l=s+this.options.underneathVerticalTopOffset+i.height;break;case"Default":i=this.measure(t.children[0],e),l=s+this.options.verticalSeparation+i.height,d=Math.max(o,i.width);break;default:throw"Unhandled TreeDirection in the Down layout measuring."}break;default:throw"Unhandled TreeDirection in the layout measuring."}u=new h(d,l)}else{switch(t.treeDirection){case"Left":case"Right":switch(t.childrenLayout){case"TopAlignedWithParent":case"BottomAlignedWithParent":break;case"Underneath":for(d=o,l=s+this.options.underneathVerticalTopOffset,a=0;a<t.children.length;a++)c=t.children[a],i=this.measure(c,e),d=Math.max(d,i.width+this.options.underneathHorizontalOffset),l+=i.height+this.options.underneathVerticalSeparation;l-=this.options.underneathVerticalSeparation;break;case"Default":for(d=o,l=0,a=0;a<t.children.length;a++)c=t.children[a],i=this.measure(c,e),d=Math.max(d,o+this.options.horizontalSeparation+i.width),l+=i.height+this.options.verticalSeparation;l-=this.options.verticalSeparation;break;default:throw"Unhandled TreeDirection in the Right layout measuring."}break;case"Up":case"Down":switch(t.childrenLayout){case"TopAlignedWithParent":case"BottomAlignedWithParent":break;case"Underneath":for(d=o,l=s+this.options.underneathVerticalTopOffset,a=0;a<t.children.length;a++)c=t.children[a],i=this.measure(c,e),d=Math.max(d,i.width+this.options.underneathHorizontalOffset),l+=i.height+this.options.underneathVerticalSeparation;l-=this.options.underneathVerticalSeparation;break;case"Default":for(d=0,l=0,a=0;a<t.children.length;a++)c=t.children[a],i=this.measure(c,e),d+=i.width+this.options.horizontalSeparation,l=Math.max(l,i.height+this.options.verticalSeparation+s);d-=this.options.horizontalSeparation;break;default:throw"Unhandled TreeDirection in the Down layout measuring."}break;default:throw"Unhandled TreeDirection in the layout measuring."}u=new h(d,l)}return t.SectorAngle=Math.sqrt(d*d/4+l*l/4),t.Size=u,u},arrange:function(t,e){var i,n,o,s,r,a,h,d,l=t.associatedShape.bounds(),u=l.width,m=l.height;if(f.isEmpty(t.children))t.x=e.x,t.y=e.y,t.BoundingRectangle=new c(e.x,e.y,u,m);else switch(t.treeDirection){case"Left":switch(t.childrenLayout){case"TopAlignedWithParent":case"BottomAlignedWithParent":break;case"Underneath":for(d=e,t.x=d.x,t.y=d.y,t.BoundingRectangle=new c(t.x,t.y,t.width,t.height),h=e.y+m+this.options.underneathVerticalTopOffset,i=0;i<s.children.length;i++)s=s.children[i],a=d.x-s.associatedShape.width-this.options.underneathHorizontalOffset,n=new p(a,h),this.arrange(s,n),h+=s.Size.height+this.options.underneathVerticalSeparation;break;case"Default":for(d=new p(e.x+t.Size.width-u,e.y+(t.Size.height-m)/2),t.x=d.x,t.y=d.y,t.BoundingRectangle=new c(t.x,t.y,t.width,t.height),a=d.x-this.options.horizontalSeparation,h=e.y,i=0;i<t.children.length;i++)s=t.children[i],n=new p(a-s.Size.width,h),this.arrange(s,n),h+=s.Size.height+this.options.verticalSeparation;break;default:throw"Unsupported TreeDirection"}break;case"Right":switch(t.childrenLayout){case"TopAlignedWithParent":case"BottomAlignedWithParent":break;case"Underneath":for(d=e,t.x=d.x,t.y=d.y,t.BoundingRectangle=new c(t.x,t.y,t.width,t.height),a=e.x+u+this.options.underneathHorizontalOffset,h=e.y+m+this.options.underneathVerticalTopOffset,i=0;i<t.children.length;i++)s=t.children[i],n=new p(a,h),this.arrange(s,n),h+=s.Size.height+this.options.underneathVerticalSeparation;break;case"Default":for(d=new p(e.x,e.y+(t.Size.height-m)/2),t.x=d.x,t.y=d.y,t.BoundingRectangle=new c(t.x,t.y,t.width,t.height),a=e.x+u+this.options.horizontalSeparation,h=e.y,i=0;i<t.children.length;i++)s=t.children[i],n=new p(a,h),this.arrange(s,n),h+=s.Size.height+this.options.verticalSeparation;break;default:throw"Unsupported TreeDirection"}break;case"Up":if(d=new p(e.x+(t.Size.width-u)/2,e.y+t.Size.height-m),t.x=d.x,t.y=d.y,t.BoundingRectangle=new c(t.x,t.y,t.width,t.height),Math.abs(d.x-e.x)<g){for(r=0,i=0;i<t.children.length;i++)o=t.children[i],r+=o.Size.width+this.options.horizontalSeparation;r-=this.options.horizontalSeparation,a=e.x+(u-r)/2}else a=e.x;for(i=0;i<t.children.length;i++)s=t.children[i],h=d.y-this.options.verticalSeparation-s.Size.height,n=new p(a,h),this.arrange(s,n),a+=s.Size.width+this.options.horizontalSeparation;break;case"Down":switch(t.childrenLayout){case"TopAlignedWithParent":case"BottomAlignedWithParent":break;case"Underneath":for(d=e,t.x=d.x,t.y=d.y,t.BoundingRectangle=new c(t.x,t.y,t.width,t.height),a=e.x+this.options.underneathHorizontalOffset,h=e.y+m+this.options.underneathVerticalTopOffset,i=0;i<t.children.length;i++)s=t.children[i],n=new p(a,h),this.arrange(s,n),h+=s.Size.height+this.options.underneathVerticalSeparation;break;case"Default":if(d=new p(e.x+(t.Size.width-u)/2,e.y),t.x=d.x,t.y=d.y,t.BoundingRectangle=new c(t.x,t.y,t.width,t.height),Math.abs(d.x-e.x)<g){for(r=0,i=0;i<t.children.length;i++)o=t.children[i],r+=o.Size.width+this.options.horizontalSeparation;r-=this.options.horizontalSeparation,a=e.x+(u-r)/2}else a=e.x;for(i=0;i<t.children.length;i++)s=t.children[i],h=d.y+this.options.verticalSeparation+m,n=new p(a,h),this.arrange(s,n),a+=s.Size.width+this.options.horizontalSeparation;break;default:throw"Unsupported TreeDirection"}break;case"None":break;default:throw"Unsupported TreeDirection"}},layoutSwitch:function(){var t,e,i,n,o,s;if(this.center&&!f.isEmpty(this.center.children))switch(t=this.options.subtype,f.isUndefined(t)&&(t="Down"),s=this.center.children,t.toLowerCase()){case"radial":case"radialtree":this.layoutRadialTree();break;case"mindmaphorizontal":case"mindmap":e=this.center.children,1===this.center.children.length?this.layoutRight(e):(o=s.length/2,i=v(this.center.children,function(t){return f.indexOf(s,t)<o}),n=v(this.center.children,function(t){return f.indexOf(s,t)>=o}),this.layoutLeft(i),this.layoutRight(n));break;case"mindmapvertical":e=this.center.children,1===this.center.children.length?this.layoutDown(e):(o=s.length/2,i=v(this.center.children,function(t){return f.indexOf(s,t)<o}),n=v(this.center.children,function(t){return f.indexOf(s,t)>=o}),this.layoutUp(i),this.layoutDown(n));break;case"right":this.layoutRight(this.center.children);break;case"left":this.layoutLeft(this.center.children);break;case"up":case"bottom":this.layoutUp(this.center.children);break;case"down":case"top":this.layoutDown(this.center.children);break;case"tipover":case"tipovertree":if(this.options.tipOverTreeStartLevel<0)throw"The tip-over level should be a positive integer.";this.tipOverTree(this.center.children,this.options.tipOverTreeStartLevel);break;case"undefined":case"none":}}}),C=y.extend({init:function(t){var e=this;if(y.fn.init.call(e),f.isUndefined(t))throw"No diagram specified.";this.diagram=t},layout:function(t){var e,i;return this.transferOptions(t),e=new w(this.diagram),this.graph=e.convert(),i=this.layoutComponents(),new n.LayoutState(this.diagram,i)},layoutComponents:function(){var t,e,i,n,o,s,r,a;if(!this.graph.isEmpty()&&(t=this.graph.getConnectedComponents(),!f.isEmpty(t))){for(e=new b(this.options),i=[],n=0;n<t.length;n++){if(o=t[n],s=this.getTree(o),!s)throw"Failed to find a spanning tree for the component.";r=s.root,a=s.tree,e.layout(a,r),i.push(a)}return this.gridLayoutComponents(i)}},getTree:function(t){var e,i,n,o,s,r=null;if(this.options.roots&&this.options.roots.length>0)for(e=0,i=t.nodes.length;e<i;e++)for(n=t.nodes[e],o=0;o<this.options.roots.length;o++)if(s=this.options.roots[o],s===n.associatedShape){r=n;break}if(!r&&(r=t.root(),!r))throw"Unable to find a root for the tree.";return this.getTreeForRoot(t,r)},getTreeForRoot:function(t,e){var i=t.getSpanningTree(e);return f.isUndefined(i)||i.isEmpty()?null:{tree:i,root:i.root}}}),S=y.extend({init:function(t){var e=this;if(y.fn.init.call(e),f.isUndefined(t))throw"Diagram is not specified.";this.diagram=t},layout:function(t){var e,i,o,s,r,a;if(this.transferOptions(t),e=new w(this.diagram),i=e.convert(t),!i.isEmpty()&&(o=i.getConnectedComponents(),!f.isEmpty(o))){for(s=0;s<o.length;s++)r=o[s],this.layoutGraph(r,t);return a=this.gridLayoutComponents(o),new n.LayoutState(this.diagram,a)}},_initRuntimeProperties:function(){var t,e;for(t=0;t<this.graph.nodes.length;t++)e=this.graph.nodes[t],e.layer=-1,e.downstreamLinkCount=0,e.upstreamLinkCount=0,e.isVirtual=!1,e.uBaryCenter=0,e.dBaryCenter=0,e.upstreamPriority=0,e.downstreamPriority=0,e.gridPosition=0},_prepare:function(t){var e,i,n,o,s,r,a,h,c,l,u,p=[],g=new d,m=0;for(f.forEach(t.nodes,function(t){0===t.incoming.length&&(g.set(t,0),p.push(t))});p.length>0;)for(s=p.shift(),e=0;e<s.outgoing.length;e++)n=s.outgoing[e],r=n.target,o=g.containsKey(r)?Math.max(g.get(s)+1,g.get(r)):g.get(s)+1,g.set(r,o),o>m&&(m=o),_(p,r)||p.push(r);for(a=g.keys(),a.sort(function(t,e){var i=g.get(t),n=g.get(e);return f.sign(n-i)}),h=0;h<a.length;++h)if(c=a[h],l=Number.MAX_VALUE,0!==c.outgoing.length){for(i=0;i<c.outgoing.length;++i)n=c.outgoing[i],l=Math.min(l,g.get(n.target));l>1&&g.set(c,l-1)}for(this.layers=[],e=0;e<m+1;e++)u=[],u.linksTo={},this.layers.push(u);for(g.forEach(function(t,e){t.layer=e,this.layers[e].push(t)},this),i=0;i<this.layers.length;i++)for(u=this.layers[i],e=0;e<u.length;e++)u[e].gridPosition=e},layoutGraph:function(t,e){if(f.isUndefined(t))throw"No graph given or graph analysis of the diagram failed.";f.isDefined(e)&&this.transferOptions(e),this.graph=t,t.setItemIndices();var i=t.makeAcyclic();this._initRuntimeProperties(),this._prepare(t,e),this._dummify(),this._optimizeCrossings(),this._swapPairs(),this.arrangeNodes(),this._moveThingsAround(),this._dedummify(),f.forEach(i,function(t){t.points&&t.points.reverse()})},setMinDist:function(t,e,i){var n=t.layer,o=t.layerIndex;this.minDistances[n][o]=i},getMinDist:function(t,e){var i,n=0,o=t.layerIndex,s=e.layerIndex,r=t.layer,a=Math.min(o,s),h=Math.max(o,s);for(i=a;i<h;++i)n+=this.minDistances[r][i];return n},placeLeftToRight:function(t){var e,i,n,o,s,r,a,h,c,l,u=new d;for(n=0;n<this.layers.length;++n)if(o=t[n]){for(e=0;e<o.length;e++)i=o[e],u.containsKey(i)||this.placeLeft(i,u,n);for(s=Number.POSITIVE_INFINITY,e=0;e<o.length;e++)i=o[e],r=this.rightSibling(i),r&&this.nodeLeftClass.get(r)!==n&&(s=Math.min(s,u.get(r)-u.get(i)-this.getMinDist(i,r)));if(s===Number.POSITIVE_INFINITY){for(a=[],e=0;e<o.length;e++)for(i=o[e],h=[],f.addRange(h,this.upNodes.get(i)),f.addRange(h,this.downNodes.get(i)),c=0;c<h.length;c++)l=h[c],this.nodeLeftClass.get(l)<n&&a.push(u.get(l)-u.get(i));a.sort(),s=0===a.length?0:a.length%2===1?a[this.intDiv(a.length,2)]:(a[this.intDiv(a.length,2)-1]+a[this.intDiv(a.length,2)])/2}for(e=0;e<o.length;e++)i=o[e],u.set(i,u.get(i)+s)}return u},placeRightToLeft:function(t){var e,i,n,o,s,r,a,h,c,l,u=new d;for(n=0;n<this.layers.length;++n)if(o=t[n]){for(e=0;e<o.length;e++)i=o[e],u.containsKey(i)||this.placeRight(i,u,n);for(s=Number.NEGATIVE_INFINITY,e=0;e<o.length;e++)i=o[e],r=this.leftSibling(i),r&&this.nodeRightClass.get(r)!==n&&(s=Math.max(s,u.get(r)-u.get(i)+this.getMinDist(r,i)));if(s===Number.NEGATIVE_INFINITY){for(a=[],e=0;e<o.length;e++)for(i=o[e],h=[],f.addRange(h,this.upNodes.get(i)),f.addRange(h,this.downNodes.get(i)),c=0;c<h.length;c++)l=h[c],this.nodeRightClass.get(l)<n&&a.push(u.get(i)-u.get(l));a.sort(),s=0===a.length?0:a.length%2===1?a[this.intDiv(a.length,2)]:(a[this.intDiv(a.length,2)-1]+a[this.intDiv(a.length,2)])/2}for(e=0;e<o.length;e++)i=o[e],u.set(i,u.get(i)+s)}return u},_getLeftWing:function(){var t={value:null},e=this.computeClasses(t,1);return this.nodeLeftClass=t.value,e},_getRightWing:function(){var t={value:null},e=this.computeClasses(t,-1);return this.nodeRightClass=t.value,e},computeClasses:function(t,e){var i,n,o,s,r,a,h,c,l,u=0,f=t.value=new d;for(i=0;i<this.layers.length;++i)for(u=i,n=this.layers[i],o=1===e?0:n.length-1;0<=o&&o<n.length;o+=e)if(s=n[o],f.containsKey(s))u=f.get(s);else if(f.set(s,u),s.isVirtual)for(r=this._nodesInLink(s),a=0;a<r.length;a++)h=r[a],f.set(h,u);for(c=[],l=0;l<this.layers.length;l++)c.push(null);return f.forEach(function(t,e){null===c[e]&&(c[e]=[]),c[e].push(t)}),c},_isVerticalLayout:function(){return"up"===this.options.subtype.toLowerCase()||"down"===this.options.subtype.toLowerCase()||"vertical"===this.options.subtype.toLowerCase()},_isHorizontalLayout:function(){return"right"===this.options.subtype.toLowerCase()||"left"===this.options.subtype.toLowerCase()||"horizontal"===this.options.subtype.toLowerCase()},_isIncreasingLayout:function(){return"right"===this.options.subtype.toLowerCase()||"down"===this.options.subtype.toLowerCase()},_moveThingsAround:function(){function t(t,e){var i,n,o=Number.MIN_VALUE;for(i=0;i<t.length;++i)n=t[i],o=e._isVerticalLayout()?Math.max(o,n.height):Math.max(o,n.width);return o}var e,i,n,o,s,r,a,h,c,l,u,p,g,m,_,v,y,w,x,b,C,S,k,T,M,D,I;for(i=0;i<this.layers.length;++i)o=this.layers[i],o.sort(this._gridPositionComparer);for(this.minDistances=[],i=0;i<this.layers.length;++i)for(o=this.layers[i],this.minDistances[i]=[],s=0;s<o.length;++s)n=o[s],n.layerIndex=s,this.minDistances[i][s]=this.options.nodeDistance,s<o.length-1&&(this.minDistances[i][s]+=this._isVerticalLayout()?(n.width+o[s+1].width)/2:(n.height+o[s+1].height)/2);for(this.downNodes=new d,this.upNodes=new d,f.forEach(this.graph.nodes,function(t){this.downNodes.set(t,[]),this.upNodes.set(t,[])},this),f.forEach(this.graph.links,function(t){var e=t.source,i=t.target,n=null,o=null;e.layer>i.layer?(n=t.source,o=t.target):(o=t.source,n=t.target),this.downNodes.get(o).push(n),this.upNodes.get(n).push(o)},this),this.downNodes.forEachValue(function(t){t.sort(this._gridPositionComparer)},this),this.upNodes.forEachValue(function(t){t.sort(this._gridPositionComparer)},this),i=0;i<this.layers.length-1;++i)for(o=this.layers[i],r=0;r<o.length-1;r++)if(a=o[r],a.isVirtual&&(h=this.downNodes.get(a)[0],h.isVirtual))for(s=r+1;s<o.length;++s)n=o[s],n.isVirtual&&(c=this.downNodes.get(n)[0],c.isVirtual&&h.gridPosition>c.gridPosition&&(l=h.gridPosition,h.gridPosition=c.gridPosition,c.gridPosition=l,u=h.layerIndex,p=c.layerIndex,this.layers[i+1][u]=c,this.layers[i+1][p]=h,h.layerIndex=p,c.layerIndex=u));for(g=this._getLeftWing(),m=this._getRightWing(),_=this.placeLeftToRight(g),v=this.placeRightToLeft(m),y=new d,f.forEach(this.graph.nodes,function(t){y.set(t,(_.get(t)+v.get(t))/2)}),w=new d,x=new d,i=0;i<this.layers.length;++i)for(o=this.layers[i],b=-1,C=-1,s=0;s<o.length;++s)n=o[s],w.set(n,0),x.set(n,!1),n.isVirtual&&(b===-1?b=s:b===s-1?b=s:(C=s,w.set(o[b],0),y.get(n)-y.get(o[b])===this.getMinDist(o[b],n)?x.set(o[b],!0):x.set(o[b],!1),b=s));for(S=[1,-1],f.forEach(S,function(t){var i,n,o,s,r,a,h,c=1===t?0:this.layers.length-1;for(i=c;0<=i&&i<this.layers.length;i+=t){if(n=this.layers[i],o=this._firstVirtualNode(n),s=null,r=null,o!==-1)for(s=n[o],r=[],e=0;e<o;e++)r.push(n[e]);else s=null,r=n;if(r.length>0){for(this._sequencer(y,null,s,t,r),e=0;e<r.length-1;++e)this.setMinDist(r[e],r[e+1],y.get(r[e+1])-y.get(r[e]));s&&this.setMinDist(r[r.length-1],s,y.get(s)-y.get(r[r.length-1]))}for(;s;){if(a=this.nextVirtualNode(n,s)){if(w.get(s)===t){for(o=s.layerIndex,h=a.layerIndex,r=[],e=o+1;e<h;e++)r.push(n[e]);r.length>0&&this._sequencer(y,s,a,t,r),x.set(s,!0)}}else{for(o=s.layerIndex,r=[],e=o+1;e<n.length;e++)r.push(n[e]);if(r.length>0){for(this._sequencer(y,s,null,t,r),e=0;e<r.length-1;++e)this.setMinDist(r[e],r[e+1],y.get(r[e+1])-y.get(r[e]));this.setMinDist(s,r[0],y.get(r[0])-y.get(s))}}s=a}this.adjustDirections(i,t,w,x)}},this),k=this._isIncreasingLayout()?0:this.layers.length-1,T=function(t,e){return e._isIncreasingLayout()?t<e.layers.length:t>=0},M=this._isIncreasingLayout()?1:-1,D=0,e=k;T(e,this);e+=M){for(o=this.layers[e],I=t(o,this),s=0;s<o.length;++s)n=o[s],this._isVerticalLayout()?(n.x=y.get(n),n.y=D+I/2):(n.x=D+I/2,n.y=y.get(n));D+=this.options.layerSeparation+I}},adjustDirections:function(t,e,i,n){var o,s,r,a,h,c,d,l,u,f,p,g,m,_;if(!(t+e<0||t+e>=this.layers.length))for(o=null,s=null,r=this.layers[t+e],a=0;a<r.length;++a)if(h=r[a],h.isVirtual&&(c=this.getNeighborOnLayer(h,t),c.isVirtual)){if(o){for(d=n.get(s),l=this.layers[t],u=s.layerIndex,f=c.layerIndex,p=u+1;p<f;++p)l[p].isVirtual&&(d=d&&n.get(l[p]));if(d)for(i.set(o,e),g=o.layerIndex,m=h.layerIndex,_=g+1;_<m;++_)r[_].isVirtual&&i.set(r[_],e)}o=h,s=c}},getNeighborOnLayer:function(t,e){var i=this.upNodes.get(t)[0];return i.layer===e?i:(i=this.downNodes.get(t)[0],i.layer===e?i:null)},_sequencer:function(t,e,i,n,o){if(1===o.length&&this._sequenceSingle(t,e,i,n,o[0]),o.length>1){var s=o.length,r=this.intDiv(s,2);this._sequencer(t,e,i,n,o.slice(0,r)),this._sequencer(t,e,i,n,o.slice(r)),this.combineSequences(t,e,i,n,o)}},_sequenceSingle:function(t,e,i,n,o){var s=n===-1?this.downNodes.get(o):this.upNodes.get(o),r=s.length;0!==r&&(r%2===1?t.set(o,t.get(s[this.intDiv(r,2)])):t.set(o,(t.get(s[this.intDiv(r,2)-1])+t.get(s[this.intDiv(r,2)]))/2),e&&t.set(o,Math.max(t.get(o),t.get(e)+this.getMinDist(e,o))),i&&t.set(o,Math.min(t.get(o),t.get(i)-this.getMinDist(o,i))))},combineSequences:function(t,e,i,n,o){var s,r,a,h,c,d,l,u,f,p,g=o.length,m=this.intDiv(g,2),_=[];for(s=0;s<m;++s){for(r=0,h=n===-1?this.downNodes.get(o[s]):this.upNodes.get(o[s]),a=0;a<h.length;++a)c=h[a],t.get(c)>=t.get(o[s])?r++:(r--,_.push({k:t.get(c)+this.getMinDist(o[s],o[m-1]),v:2}));_.push({k:t.get(o[s])+this.getMinDist(o[s],o[m-1]),v:r})}for(e&&_.push({k:t.get(e)+this.getMinDist(e,o[m-1]),v:Number.MAX_VALUE}),_.sort(this._positionDescendingComparer),l=[],s=m;s<g;++s){for(r=0,h=n===-1?this.downNodes.get(o[s]):this.upNodes.get(o[s]),a=0;a<h.length;++a)c=h[a],t.get(c)<=t.get(o[s])?r++:(r--,l.push({k:t.get(c)-this.getMinDist(o[s],o[m]),v:2}));l.push({k:t.get(o[s])-this.getMinDist(o[s],o[m]),v:r})}for(i&&l.push({k:t.get(i)-this.getMinDist(i,o[m]),v:Number.MAX_VALUE}),l.sort(this._positionAscendingComparer),u=0,f=0,p=this.getMinDist(o[m-1],o[m]);t.get(o[m])-t.get(o[m-1])<p;)if(u<f){if(0===_.length){t.set(o[m-1],t.get(o[m])-p);break}d=_.shift(),u+=d.v,t.set(o[m-1],d.k),t.set(o[m-1],Math.max(t.get(o[m-1]),t.get(o[m])-p))}else{if(0===l.length){t.set(o[m],t.get(o[m-1])+p);break}d=l.shift(),f+=d.v,t.set(o[m],d.k),t.set(o[m],Math.min(t.get(o[m]),t.get(o[m-1])+p))}for(s=m-2;s>=0;s--)t.set(o[s],Math.min(t.get(o[s]),t.get(o[m-1])-this.getMinDist(o[s],o[m-1])));for(s=m+1;s<g;s++)t.set(o[s],Math.max(t.get(o[s]),t.get(o[m])+this.getMinDist(o[s],o[m])))},placeLeft:function(t,e,i){var n=Number.NEGATIVE_INFINITY;f.forEach(this._getComposite(t),function(t){var o=this.leftSibling(t);o&&this.nodeLeftClass.get(o)===this.nodeLeftClass.get(t)&&(e.containsKey(o)||this.placeLeft(o,e,i),n=Math.max(n,e.get(o)+this.getMinDist(o,t)))},this),n===Number.NEGATIVE_INFINITY&&(n=0),f.forEach(this._getComposite(t),function(t){e.set(t,n)})},placeRight:function(t,e,i){var n=Number.POSITIVE_INFINITY;f.forEach(this._getComposite(t),function(t){var o=this.rightSibling(t);o&&this.nodeRightClass.get(o)===this.nodeRightClass.get(t)&&(e.containsKey(o)||this.placeRight(o,e,i),n=Math.min(n,e.get(o)-this.getMinDist(t,o)))},this),n===Number.POSITIVE_INFINITY&&(n=0),f.forEach(this._getComposite(t),function(t){e.set(t,n)})},leftSibling:function(t){var e=this.layers[t.layer],i=t.layerIndex;return 0===i?null:e[i-1]},rightSibling:function(t){var e=this.layers[t.layer],i=t.layerIndex;return i===e.length-1?null:e[i+1]},_getComposite:function(t){return t.isVirtual?this._nodesInLink(t):[t]},arrangeNodes:function(){var t,e,i,n,o,s,r,a;for(e=0;e<this.layers.length;e++)for(n=this.layers[e],i=0;i<n.length;i++)o=n[i],o.upstreamPriority=o.upstreamLinkCount,o.downstreamPriority=o.downstreamLinkCount;for(s=2,r=0;r<s;r++){for(t=this.layers.length-1;t>=1;t--)this.layoutLayer(!1,t);for(t=0;t<this.layers.length-1;t++)this.layoutLayer(!0,t)}for(a=Number.MAX_VALUE,e=0;e<this.layers.length;e++)for(n=this.layers[e],i=0;i<n.length;i++)o=n[i],a=Math.min(a,o.gridPosition);if(a<0)for(e=0;e<this.layers.length;e++)for(n=this.layers[e],i=0;i<n.length;i++)o=n[i],o.gridPosition=o.gridPosition-a},layoutLayer:function(t,e){var i,n,o,s;for(n=t?this.layers[i=e+1]:this.layers[i=e-1],o=[],s=0;s<n.length;s++)o.push(n[s]);o.sort(function(t,e){var i=(t.upstreamPriority+t.downstreamPriority)/2,n=(e.upstreamPriority+e.downstreamPriority)/2;
return Math.abs(i-n)<1e-4?0:i<n?1:-1}),f.forEach(o,function(t){var e=t.gridPosition,i=this.calcBaryCenter(t),o=(t.upstreamPriority+t.downstreamPriority)/2;if(!(Math.abs(e-i)<1e-4||Math.abs(e-i)<.2501))if(e<i)for(;e<i&&this.moveRight(t,n,o);)e=t.gridPosition;else for(;e>i&&this.moveLeft(t,n,o);)e=t.gridPosition},this),i>0&&this.calcDownData(i-1),i<this.layers.length-1&&this.calcUpData(i+1)},moveRight:function(t,e,i){var n,o,s=f.indexOf(e,t);return s===e.length-1?(t.gridPosition=t.gridPosition+.5,!0):(n=e[s+1],o=(n.upstreamPriority+n.downstreamPriority)/2,n.gridPosition>t.gridPosition+1?(t.gridPosition=t.gridPosition+.5,!0):!(o>i||Math.abs(o-i)<1e-4)&&(!!this.moveRight(n,e,i)&&(t.gridPosition=t.gridPosition+.5,!0)))},moveLeft:function(t,e,i){var n,o,s=f.indexOf(e,t);return 0===s?(t.gridPosition=t.gridPosition-.5,!0):(n=e[s-1],o=(n.upstreamPriority+n.downstreamPriority)/2,n.gridPosition<t.gridPosition-1?(t.gridPosition=t.gridPosition-.5,!0):!(o>i||Math.abs(o-i)<1e-4)&&(!!this.moveLeft(n,e,i)&&(t.gridPosition=t.gridPosition-.5,!0)))},mapVirtualNode:function(t,e){this.nodeToLinkMap.set(t,e),this.linkToNodeMap.containsKey(e)||this.linkToNodeMap.set(e,[]),this.linkToNodeMap.get(e).push(t)},_nodesInLink:function(t){return this.linkToNodeMap.get(this.nodeToLinkMap.get(t))},_dummify:function(){var t,e,i,n,o,a,h,c,l,u,p,g,m,_,v,y,w,x,b,C;for(this.linkToNodeMap=new d,this.nodeToLinkMap=new d,l=this.graph.links.slice(0),u=this.layers,p=function(t,e,i){u[t].linksTo[e]=u[t].linksTo[e]||[],u[t].linksTo[e].push(i)},c=0;c<l.length;c++)if(g=l[c],m=g.source,_=g.target,v=m.layer,y=_.layer,w=m.gridPosition,x=_.gridPosition,b=(x-w)/Math.abs(y-v),C=m,v-y>1){for(h=v-1;h>y;h--){for(i=new s,i.x=m.x,i.y=m.y,i.width=m.width/100,i.height=m.height/100,t=u[h],e=(h-y)*b+w,e>t.length&&(e=t.length),w>=u[v].length-1&&x>=u[y].length-1?e=t.length:0===w&&0===x&&(e=0),i.layer=h,i.uBaryCenter=0,i.dBaryCenter=0,i.upstreamLinkCount=0,i.downstreamLinkCount=0,i.gridPosition=e,i.isVirtual=!0,f.insert(t,i,e),o=e+1;o<t.length;o++)n=t[o],n.gridPosition=n.gridPosition+1;a=new r(C,i),a.depthOfDumminess=0,p(h-1,h,a),C=i,this.graph._addNode(i),this.graph.addLink(a),i.index=this.graph.nodes.length-1,this.mapVirtualNode(i,g)}p(y-1,y,a),g.changeSource(C),g.depthOfDumminess=v-y-1}else if(v-y<-1){for(h=v+1;h<y;h++){for(i=new s,i.x=m.x,i.y=m.y,i.width=m.width/100,i.height=m.height/100,t=u[h],e=(h-v)*b+w,e>t.length&&(e=t.length),w>=u[v].length-1&&x>=u[y].length-1?e=t.length:0===w&&0===x&&(e=0),i.layer=h,i.uBaryCenter=0,i.dBaryCenter=0,i.upstreamLinkCount=0,i.downstreamLinkCount=0,i.gridPosition=e,i.isVirtual=!0,e&=e,f.insert(t,i,e),o=e+1;o<t.length;o++)n=t[o],n.gridPosition=n.gridPosition+1;a=new r(C,i),a.depthOfDumminess=0,p(h-1,h,a),C=i,this.graph._addNode(i),this.graph.addLink(a),i.index=this.graph.nodes.length-1,this.mapVirtualNode(i,g)}p(y-1,y,g),g.changeSource(C),g.depthOfDumminess=y-v-1}else p(v,y,g)},_dedummify:function(){for(var t,e,i,n,o,s,r,a,h=!0;h;)for(h=!1,t=0;t<this.graph.links.length;t++)if(e=this.graph.links[t],e.depthOfDumminess){for(i=[],i.unshift({x:e.target.x,y:e.target.y}),i.unshift({x:e.source.x,y:e.source.y}),n=e,o=e.depthOfDumminess,s=0;s<o;s++)r=n.source,a=r.incoming[0],i.unshift({x:a.source.x,y:a.source.y}),n=a;e.changeSource(n.source),e.depthOfDumminess=0,i.length>2?(i.splice(0,1),i.splice(i.length-1),e.points=i):e.points=[],h=!0;break}},_optimizeCrossings:function(){for(var t,e=-1,i=3,n=0;0!==e&&!(n++>i);){for(e=0,t=this.layers.length-1;t>=1;t--)e+=this.optimizeLayerCrossings(!1,t);for(t=0;t<this.layers.length-1;t++)e+=this.optimizeLayerCrossings(!0,t)}},calcUpData:function(t){var e,i,n,o,s,r,a,h,c;if(0!==t){for(e=this.layers[t],s=new l,r=this.layers[t-1],i=0;i<r.length;i++)s.add(r[i]);for(i=0;i<e.length;i++){for(a=e[i],h=0,c=0,n=0;n<a.incoming.length;n++)o=a.incoming[n],s.contains(o.source)&&(c++,h+=o.source.gridPosition);for(n=0;n<a.outgoing.length;n++)o=a.outgoing[n],s.contains(o.target)&&(c++,h+=o.target.gridPosition);c>0?(a.uBaryCenter=h/c,a.upstreamLinkCount=c):(a.uBaryCenter=i,a.upstreamLinkCount=0)}}},calcDownData:function(t){var e,i,n,o,s,r,a,h,c;if(t!==this.layers.length-1){for(e=this.layers[t],s=new l,r=this.layers[t+1],i=0;i<r.length;i++)s.add(r[i]);for(i=0;i<e.length;i++){for(a=e[i],h=0,c=0,n=0;n<a.incoming.length;n++)o=a.incoming[n],s.contains(o.source)&&(c++,h+=o.source.gridPosition);for(n=0;n<a.outgoing.length;n++)o=a.outgoing[n],s.contains(o.target)&&(c++,h+=o.target.gridPosition);c>0?(a.dBaryCenter=h/c,a.downstreamLinkCount=c):(a.dBaryCenter=i,a.downstreamLinkCount=0)}}},optimizeLayerCrossings:function(t,e){var i,n,o,s,r,a,h,c;for(n=t?this.layers[i=e+1]:this.layers[i=e-1],o=n.slice(0),t?this.calcUpData(i):this.calcDownData(i),s=this,n.sort(function(t,e){var i,n=s.calcBaryCenter(t),o=s.calcBaryCenter(e);return Math.abs(n-o)<1e-4?t.degree()===e.degree()?s.compareByIndex(t,e):t.degree()<e.degree()?1:-1:(i=1e3*(o-n),i>0?-1:i<0?1:s.compareByIndex(t,e))}),a=0,r=0;r<n.length;r++)n[r]!==o[r]&&a++;if(a>0)for(h=0,r=0;r<n.length;r++)c=n[r],c.gridPosition=h++;return a},_swapPairs:function(){for(var t,e,i,n,o,s,r,a,h,c,d,l,u,f,p,g,m,_=this.options.layeredIterations,v=0;;){if(v++>_)break;for(t=v%4<=1,e=v%4===1,i=t?0:this.layers.length-1;t?i<=this.layers.length-1:i>=0;i+=t?1:-1){for(n=this.layers[i],o=!1,s=!0,r=0,a=0;a<n.length-1;a++)h=0,c=0,d=0,s?(0!==i&&(h=this.countLinksCrossingBetweenTwoLayers(i-1,i)),i!==this.layers.length-1&&(c=this.countLinksCrossingBetweenTwoLayers(i,i+1)),t?h*=2:c*=2,d=h+c):d=r,0!==d&&(l=n[a],u=n[a+1],f=l.gridPosition,p=u.gridPosition,n[a]=u,n[a+1]=l,l.gridPosition=p,u.gridPosition=f,h=0,0!==i&&(h=this.countLinksCrossingBetweenTwoLayers(i-1,i)),c=0,i!==this.layers.length-1&&(c=this.countLinksCrossingBetweenTwoLayers(i,i+1)),t?h*=2:c*=2,g=h+c,m=!1,m=e?g>=d:g>d,m?(l=n[a],u=n[a+1],f=l.gridPosition,p=u.gridPosition,n[a]=u,n[a+1]=l,l.gridPosition=p,u.gridPosition=f,r=d,s=!1):(o=!0,s=!0));o&&(i!==this.layers.length-1&&this.calcUpData(i+1),0!==i&&this.calcDownData(i-1))}}},countLinksCrossingBetweenTwoLayers:function(t,e){var i,n,o,s,r,a,h,c,d,l,u,f,p=this.layers[t].linksTo[e],g=0,m=p.length;for(h=0;h<m;h++)for(i=p[h],c=h+1;c<m;c++)n=p[c],i.target.layer===e?(o=i.source,s=i.target):(o=i.target,s=i.source),n.target.layer===e?(r=n.source,a=n.target):(r=n.target,a=n.source),d=o.gridPosition,l=s.gridPosition,u=r.gridPosition,f=a.gridPosition,(d-u)*(l-f)<0&&g++;return g},calcBaryCenter:function(t){var e=t.upstreamLinkCount,i=t.downstreamLinkCount,n=t.uBaryCenter,o=t.dBaryCenter;return e>0&&i>0?(n+o)/2:e>0?n:i>0?o:0},_gridPositionComparer:function(t,e){return t.gridPosition<e.gridPosition?-1:t.gridPosition>e.gridPosition?1:0},_positionAscendingComparer:function(t,e){return t.k<e.k?-1:t.k>e.k?1:0},_positionDescendingComparer:function(t,e){return t.k<e.k?1:t.k>e.k?-1:0},_firstVirtualNode:function(t){for(var e=0;e<t.length;e++)if(t[e].isVirtual)return e;return-1},compareByIndex:function(t,e){var i=t.index,n=e.index;return i<n?1:i>n?-1:0},intDiv:function(t,e){return(t-t%e)/e},nextVirtualNode:function(t,e){var i,n=e.layerIndex;for(i=n+1;i<t.length;++i)if(t[i].isVirtual)return t[i];return null}}),k=i.Class.extend({init:function(t,e){if(f.isUndefined(t))throw"No diagram given";this.diagram=t,this.nodeMap=new d,this.linkMap=new d,this.capture(e?e:t)},capture:function(t){var e,i,o,s,r,a,h,d,l;if(t instanceof n.Graph){for(s=0;s<t.nodes.length;s++)e=t.nodes[s],o=e.associatedShape,this.nodeMap.set(o.visual.id,new c(e.x,e.y,e.width,e.height));for(s=0;s<t.links.length;s++)a=t.links[s],r=a.associatedConnection,this.linkMap.set(r.visual.id,a.points())}else if(t instanceof Array)for(i=t,s=0;s<i.length;s++)e=i[s],o=e.associatedShape,o&&this.nodeMap.set(o.visual.id,new c(e.x,e.y,e.width,e.height));else if(t.hasOwnProperty("links")&&t.hasOwnProperty("nodes")){for(i=t.nodes,h=t.links,s=0;s<i.length;s++)e=i[s],o=e.associatedShape,o&&this.nodeMap.set(o.visual.id,new c(e.x,e.y,e.width,e.height));for(s=0;s<h.length;s++)a=h[s],r=a.associatedConnection,r&&this.linkMap.set(r.visual.id,a.points)}else{for(d=this.diagram.shapes,l=this.diagram.connections,s=0;s<d.length;s++)o=d[s],this.nodeMap.set(o.visual.id,o.bounds());for(s=0;s<l.length;s++)r=l[s],this.linkMap.set(r.visual.id,r.points())}}});a(n,{init:function(t){i.init(t,n.ui)},SpringLayout:x,TreeLayout:C,GraphAdapter:w,LayeredLayout:S,LayoutBase:y,LayoutState:k})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/diagram/dom.min",["kendo.data.min","kendo.draganddrop.min","kendo.toolbar.min","kendo.editable.min","kendo.window.min","kendo.dropdownlist.min","kendo.dataviz.core.min","kendo.dataviz.themes.min","dataviz/diagram/svg.min","dataviz/diagram/services.min","dataviz/diagram/layout.min"],t)}(function(){!function(t,e){function i(t){var e=t.originalEvent,i=0;return e.wheelDelta?(i=-e.wheelDelta/40,i=i>0?xt.ceil(i):xt.floor(i)):e.detail&&(i=e.detail),i}function n(t){return t.options.name.toLowerCase()===zt.toLowerCase()}function o(t,e){var i,o,s,r,a=At;for(s=0;s<e.length;s++)o=e[s],n(o)||(r=t.distanceTo(o.position()),r<a&&(a=r,i=o));return i}function s(t,e){var i,n,o,s=[],r=t.drawingContainer().children,a=r.length;for(i=0;i<e.length;i++)for(n=e[i],o=0;o<a;o++)if(r[o]==n.drawingContainer()){s.push(o);break}return s}function r(t){var e={};return t=t||{},mt(t.text)&&null!==t.text&&(e.text=t.text),mt(t.x)&&null!==t.x&&(e.x=t.x),mt(t.y)&&null!==t.y&&(e.y=t.y),mt(t.width)&&null!==t.width&&(e.width=t.width),mt(t.height)&&null!==t.height&&(e.height=t.height),mt(t.type)&&null!==t.type&&(e.type=t.type),e}function a(t){var e={};return t=t||{},mt(t.text)&&null!==t.text&&(e.content=t.text),mt(t.type)&&null!==t.type&&(e.type=t.type),mt(t.from)&&null!==t.from&&(e.from=t.from),mt(t.fromConnector)&&null!==t.fromConnector&&(e.fromConnector=t.fromConnector),mt(t.fromX)&&null!==t.fromX&&(e.fromX=t.fromX),mt(t.fromY)&&null!==t.fromY&&(e.fromY=t.fromY),mt(t.to)&&null!==t.to&&(e.to=t.to),mt(t.toConnector)&&null!==t.toConnector&&(e.toConnector=t.toConnector),mt(t.toX)&&null!==t.toX&&(e.toX=t.toX),mt(t.toY)&&null!==t.toY&&(e.toY=t.toY),e}function h(e,i){var n,o=this.dataSource.reader.model;o&&(n=o.fn.fields.text?"text":o.idField,t("<input name='"+i.field+"' />").appendTo(e).kendoDropDownList({dataValueField:o.idField,dataTextField:n,dataSource:this.dataSource.data().toJSON(),optionLabel:" ",valuePrimitive:!0}))}function c(t){this.dataItem=t,this.callbacks=[]}function d(){this.items={}}function l(t){var e=t;return t instanceof kendo.data.Model&&(e=t.toJSON(),e[t.idField]=t._defaultId),e}function u(t){var e,i,n=[],o=[];for(i=0;i<t.length;i++)e=t[i],e instanceof x?o.push(e):n.push(e);return{shapes:o,connections:n}}function f(t,e){return t.reader.model?new t.reader.model(e):new kendo.data.ObservableObject(e)}function p(t,e){mt(e[t])&&e.set(t,null)}function g(t,e,i){var n,o;for(o=0;o<i.length;o++)n=i[o],e&&!mt(e[n])&&(e[n]=t[n])}function m(t){var e=t.drawingContainer().clippedBBox(null);0===e.origin.x&&0===e.origin.y||t.position(-e.origin.x,-e.origin.y)}function _(t){t.preventDefault()}var v,y,w,x,b,C,S,k,T,M,D,I,P=kendo.dataviz,L=kendo.drawing,E=kendo.geometry,z=P.diagram,B=kendo.ui.Widget,R=kendo.Class,N=t.proxy,O=kendo.deepExtend,A=kendo._outerWidth,U=kendo._outerHeight,V=t.extend,H=kendo.data.HierarchicalDataSource,F=z.Canvas,G=z.Group,q=z.Rectangle,K=z.Circle,W=z.CompositeTransform,X=z.Rect,Y=z.Path,j=z.DeleteShapeUnit,Q=z.DeleteConnectionUnit,J=z.TextBlock,Z=z.Image,$=z.Point,tt=z.Intersect,et=z.ConnectionEditAdorner,it=z.UndoRedoService,nt=z.ToolService,ot=z.Selector,st=z.ResizingAdorner,rt=z.ConnectorsAdorner,at=z.Cursors,ht=z.Utils,ct=kendo.Observable,dt=z.ToBackUnit,lt=z.ToFrontUnit,ut=z.PolylineRouter,ft=z.CascadingRouter,pt=ht.isUndefined,gt=ht.isDefined,mt=L.util.defined,_t=t.isArray,vt=kendo.isFunction,yt=ht.isString,wt=t.isPlainObject,xt=Math,bt=".kendoDiagram",Ct="cascading",St="itemBoundsChange",kt="change",Tt="click",Mt="drag",Dt="dragEnd",It="dragStart",Pt="mouseEnter",Lt="mouseLeave",Et="error",zt="Auto",Bt="Top",Rt="Right",Nt="Left",Ot="Bottom",At=9007199254740992,Ut="select",Vt="itemRotate",Ht="pan",Ft="zoomStart",Gt="zoomEnd",qt="none",Kt=600,Wt=600,Xt="rectangle",Yt=100,jt=100,Qt=20,Jt=20,Zt=0,$t="Yellow",te=Number.MAX_VALUE,ee=-Number.MAX_VALUE,ie="absolute",ne="transformed",oe="rotated",se="transparent",re="width",ae="height",he="x",ce="y",de="DOMMouseScroll"+bt+" mousewheel"+bt,le=.05,ue=5,fe='<a class="k-button k-button-icontext #=className#" href="\\#"><span class="#=iconClass# #=imageClass#"></span>#=text#</a>',pe=5;z.DefaultConnectors=[{name:Bt},{name:Ot},{name:Nt},{name:Rt},{name:zt,position:function(t){return t.getPosition("center")}}],v={cancel:{text:"Cancel",imageClass:"k-i-cancel",className:"k-diagram-cancel",iconClass:"k-icon"},update:{text:"Update",imageClass:"k-i-checkmark",className:"k-diagram-update",iconClass:"k-icon"}},z.shapeDefaults=function(t){var e={type:Xt,path:"",autoSize:!0,visual:null,x:Zt,y:Zt,minWidth:Qt,minHeight:Jt,width:Yt,height:jt,hover:{},editable:{connect:!0,tools:[]},connectors:z.DefaultConnectors,rotation:{angle:0}};return ht.simpleExtend(e,t),e},y=ct.extend({init:function(t){var e=this;e.dataItem=(t||{}).dataItem,ct.fn.init.call(e),e.options=O({id:z.randomId()},e.options,t),e.isSelected=!1,e.visual=new G({id:e.options.id,autoSize:e.options.autoSize}),e.id=e.options.id,e._template()},options:{hover:{},cursor:at.grip,content:{align:"center middle"},selectable:!0,serializable:!0,enable:!0},_getCursor:function(t){return this.adorner?this.adorner._getCursor(t):this.options.cursor},visible:function(t){return pt(t)?this.visual.visible():(this.visual.visible(t),e)},bounds:function(){},refresh:function(){this.visual.redraw()},position:function(t){this.options.x=t.x,this.options.y=t.y,this.visual.position(t)},toString:function(){return this.options.id},serialize:function(){var t=O({},{options:this.options});return this.dataItem&&(t.dataItem=""+this.dataItem),t},_content:function(t){var i,n,o;return t!==e&&(i=this.options,z.Utils.isString(t)?i.content.text=t:O(i.content,t),n=i.content,o=this._contentVisual,o?this._updateContentVisual(n):this._createContentVisual(n)),this.options.content.text},_createContentVisual:function(t){t.text&&(this._contentVisual=new J(t),this._contentVisual._includeInBBox=!1,this.visual.append(this._contentVisual))},_updateContentVisual:function(t){this._contentVisual.redraw(t)},_hitTest:function(t){var e=this.bounds();return this.visible()&&e.contains(t)&&this.options.enable},_template:function(){var t,e,i=this;i.options.content.template&&(t=i.dataItem||{},e=kendo.template(i.options.content.template,{paramName:"dataItem"}),i.options.content.text=e(t))},_canSelect:function(){return this.options.selectable!==!1},toJSON:function(){return{id:this.options.id}}}),w=R.extend({init:function(t,e){this.options=O({},this.options,e),this.connections=[],this.shape=t},options:{width:7,height:7,fill:{color:$t},hover:{}},position:function(){return this.options.position?this.options.position(this.shape):this.shape.getPosition(this.options.name)},toJSON:function(){return{shapeId:""+this.shape,connector:this.options.name}}}),w.parse=function(t,e){var i,n,o=e.split(":"),s=o[0],r=o[1]||zt;for(i=0;i<t.shapes.length;i++)if(n=t.shapes[i],n.options.id==s)return n.getConnector(r.trim())},x=y.extend({init:function(t,e){var i=this;y.fn.init.call(i,t),this.diagram=e,this.updateOptionsFromModel(),t=i.options,i.connectors=[],i.type=t.type,i.createShapeVisual(),i.updateBounds(),i.content(i.content()),i._createConnectors()},options:z.shapeDefaults(),_setOptionsFromModel:function(t){var e=r(t||this.dataItem);this.options=O({},this.options,e),this.redrawVisual()},updateOptionsFromModel:function(t,e){var i,n;this.diagram&&this.diagram._isEditable&&(i=r(t||this.dataItem),t&&e?P.inArray(e,["x","y","width","height"])?(n=this.bounds(),n[e]=t[e],this.bounds(n)):(this.options.visual?this._redrawVisual():i.type&&(this.options=O({},this.options,i),this._redrawVisual()),this.options.content&&(this._template(),this.content(this.options.content))):this.options=O({},this.options,i))},_redrawVisual:function(){this.visual.clear(),this._contentVisual=null,this.options.dataItem=this.dataItem,this.createShapeVisual(),this.updateBounds()},redrawVisual:function(){this._redrawVisual(),this.options.content&&(this._template(),this.content(this.options.content))},updateModel:function(t){var e,i,n=this.diagram;n&&n._isEditable&&(e=this._bounds,i=this.dataItem,i&&(n._suspendModelRefresh(),mt(i.x)&&e.x!==i.x&&i.set("x",e.x),mt(i.y)&&e.y!==i.y&&i.set("y",e.y),mt(i.width)&&e.width!==i.width&&i.set("width",e.width),mt(i.height)&&e.height!==i.height&&i.set("height",e.height),this.dataItem=i,n._resumeModelRefresh(),t&&n._syncShapeChanges()))},updateBounds:function(){var t=this.visual._measure(!0),e=this.options;this.bounds(new X(e.x,e.y,t.width,t.height)),this._rotate(),this._alignContent()},content:function(t){var e=this._content(t);return this._alignContent(),e},_alignContent:function(){var t,e,i,n,o,s=this.options.content||{},r=this._contentVisual;r&&s.align&&(t=this.visual._measure(),e=new z.RectAlign(t),i=r.drawingElement.bbox(null),n=new X(0,0,i.width(),i.height()),o=e.align(n,s.align),r.position(o.topLeft()))},_createConnectors:function(){var t,e,i=this.options,n=i.connectors.length,o=i.connectorDefaults;for(e=0;e<n;e++)t=new w(this,O({},o,i.connectors[e])),this.connectors.push(t)},bounds:function(t){var e,i;if(t)if(yt(t))switch(t){case ne:e=this._transformedBounds();break;case ie:e=this._transformedBounds(),i=this.diagram._pan,e.x+=i.x,e.y+=i.y;break;case oe:e=this._rotatedBounds();break;default:e=this._bounds}else this._setBounds(t),this._triggerBoundsChange(),this.diagram&&this.diagram._layouting||this.refreshConnections();else e=this._bounds;return e},_setBounds:function(t){var e=this.options,i=t.topLeft(),n=e.x=i.x,o=e.y=i.y,s=e.width=xt.max(t.width,e.minWidth),r=e.height=xt.max(t.height,e.minHeight);this._bounds=new X(n,o,s,r),this.visual.redraw({x:n,y:o,width:s,height:r})},position:function(t){return t?(this.bounds(new X(t.x,t.y,this._bounds.width,this._bounds.height)),e):this._bounds.topLeft()},clone:function(){var t=this.serialize();return t.options.id=z.randomId(),this.diagram&&this.diagram._isEditable&&mt(this.dataItem)&&(t.options.dataItem=l(this.dataItem)),new x(t.options)},select:function(t){var e,i,n=this.diagram;if(pt(t)&&(t=!0),this._canSelect()&&this.isSelected!=t)return e=[],i=[],this.isSelected=t,this.isSelected?(n._selectedItems.push(this),e.push(this)):(ht.remove(n._selectedItems,this),i.push(this)),n._internalSelection||n._selectionChanged(e,i),!0},rotate:function(t,i,n){var o,s,r,a,h=this.visual.rotate();return t!==e&&(n!==!1&&this.diagram&&this.diagram.undoRedoService&&t!==h.angle&&this.diagram.undoRedoService.add(new z.RotateUnit(this.diagram._resizingAdorner,[this],[h.angle]),!1),o=this.bounds(),s=new $(o.width/2,o.height/2),i&&(r=t-h.angle,a=o.center().rotate(i,360-r).minus(s),this._rotationOffset=this._rotationOffset.plus(a.minus(o.topLeft())),this.position(a)),this.visual.rotate(t,s),this.options.rotation.angle=t,this.diagram&&this.diagram._connectorsAdorner&&this.diagram._connectorsAdorner.refresh(),this.refreshConnections(),this.diagram&&this.diagram.trigger(Vt,{item:this})),h},connections:function(t){var e,i,n,o,s,r,a,h=[];for(e=0;e<this.connectors.length;e++)for(s=this.connectors[e],o=s.connections,i=0,o;i<o.length;i++)n=o[i],"out"==t?(r=n.source(),r.shape&&r.shape==this&&h.push(n)):"in"==t?(a=n.target(),a.shape&&a.shape==this&&h.push(n)):h.push(n);return h},refreshConnections:function(){t.each(this.connections(),function(){this.refresh()})},getConnector:function(t){var e,i;if(!yt(t))return t instanceof $?o(t,this.connectors):this.connectors.length?this.connectors[0]:null;for(t=t.toLocaleLowerCase(),e=0;e<this.connectors.length;e++)if(i=this.connectors[e],i.options.name.toLocaleLowerCase()==t)return i},getPosition:function(t){var e=this.bounds(),i=t.charAt(0).toLowerCase()+t.slice(1);return vt(e[i])?this._transformPoint(e[i]()):e.center()},redraw:function(t){var e,i;t&&(e=this.options,this.shapeVisual.redraw(this._visualOptions(t)),this._diffNumericOptions(t,[re,ae,he,ce])&&(this.bounds(new X(e.x,e.y,e.width,e.height)),i=!0),t.connectors&&(e.connectors=t.connectors,this._updateConnectors()),e=O(e,t),(t.rotation||i)&&this._rotate(),e.content&&this.content(e.content))},_updateConnectors:function(){var t,e,i,n,o=this.connections();for(this.connectors=[],this._createConnectors(),n=0;n<o.length;n++)t=o[n],e=t.source(),i=t.target(),e.shape&&e.shape===this?t.source(this.getConnector(e.options.name)||null):i.shape&&i.shape===this&&t.target(this.getConnector(i.options.name)||null),t.updateModel()},_diffNumericOptions:z.diffNumericOptions,_visualOptions:function(t){return{data:t.path,source:t.source,hover:t.hover,fill:t.fill,stroke:t.stroke}},_triggerBoundsChange:function(){this.diagram&&this.diagram.trigger(St,{item:this,bounds:this._bounds.clone()})},_transformPoint:function(t){var e=this.rotate(),i=this.bounds(),n=i.topLeft();return e.angle&&t.rotate(e.center().plus(n),360-e.angle),t},_transformedBounds:function(){var t=this.bounds(),e=t.topLeft(),i=t.bottomRight();return X.fromPoints(this.diagram.modelToView(e),this.diagram.modelToView(i))},_rotatedBounds:function(){var t=this.bounds().rotatedBounds(this.rotate().angle),e=t.topLeft(),i=t.bottomRight();return X.fromPoints(e,i)},_rotate:function(){var t=this.options.rotation;t&&t.angle&&this.rotate(t.angle),this._rotationOffset=new $},_hover:function(t){var e=this.options,i=e.hover,n=e.stroke,o=e.fill;t&&gt(i.stroke)&&(n=O({},n,i.stroke)),t&&gt(i.fill)&&(o=i.fill),this.shapeVisual.redraw({stroke:n,fill:o}),e.editable&&e.editable.connect&&this.diagram._showConnectors(this,t)},_hitTest:function(t){if(this.visible()){var e,i=this.bounds(),n=this.rotate().angle;if(t.isEmpty&&!t.isEmpty())return tt.rects(t,i,n?n:0);if(e=t.clone().rotate(i.center(),n),i.contains(e))return this}},toJSON:function(){return{shapeId:this.options.id}},createShapeVisual:function(){var t,e=this.options,i=this._visualOptions(e),n=e.visual,o=(e.type+"").toLocaleLowerCase();i.width=e.width,i.height=e.height,vt(n)?t=n.call(this,e):i.data?(t=new Y(i),m(t)):t="rectangle"==o?new q(i):"circle"==o?new K(i):"text"==o?new J(i):"image"==o?new Z(i):new Y(i),this.shapeVisual=t,this.visual.append(this.shapeVisual)}}),b=y.extend({init:function(t,e,i){var n=this;y.fn.init.call(n,i),this.updateOptionsFromModel(),this._initRouter(),n.path=new z.Polyline(n.options),n.path.fill(se),n.visual.append(n.path),n._sourcePoint=n._targetPoint=new $,n._setSource(t),n._setTarget(e),n.content(n.options.content),n.definers=[],mt(i)&&i.points&&n.points(i.points)},options:{hover:{stroke:{}},startCap:qt,endCap:qt,points:[],selectable:!0,fromConnector:zt,toConnector:zt},_setOptionsFromModel:function(t){this.updateOptionsFromModel(t||this.dataItem)},updateOptionsFromModel:function(t){var e,i,n,o;this.diagram&&this.diagram._isEditable&&(e=this.diagram._dataMap,i=a(t||this.dataItem),t?(mt(i.from)?(n=e[i.from],n&&mt(i.fromConnector)&&(n=n.getConnector(i.fromConnector)),this.source(n)):mt(i.fromX)&&mt(i.fromY)&&this.source(new $(i.fromX,i.fromY)),mt(i.to)?(o=e[i.to],o&&mt(i.toConnector)&&(o=o.getConnector(i.toConnector)),this.target(o)):mt(i.toX)&&mt(i.toY)&&this.target(new $(i.toX,i.toY)),mt(i.type)&&this.type()!==i.type&&(this.points([]),this.type(i.type)),this.dataItem=t,this._template(),this.redraw(this.options)):this.options=O({},i,this.options))},updateModel:function(t){if(this.diagram&&this.diagram._isEditable&&this.diagram.connectionsDataSource){var e=this.diagram.connectionsDataSource.getByUid(this.dataItem.uid);e&&(this.diagram._suspendModelRefresh(),mt(this.options.fromX)&&null!==this.options.fromX?(p("from",e),p("fromConnector",e),e.set("fromX",this.options.fromX),e.set("fromY",this.options.fromY)):(e.set("from",this.options.from),mt(e.fromConnector)&&e.set("fromConnector",this.sourceConnector?this.sourceConnector.options.name:null),p("fromX",e),p("fromY",e)),mt(this.options.toX)&&null!==this.options.toX?(p("to",e),p("toConnector",e),e.set("toX",this.options.toX),e.set("toY",this.options.toY)):(e.set("to",this.options.to),mt(e.toConnector)&&e.set("toConnector",this.targetConnector?this.targetConnector.options.name:null),p("toX",e),p("toY",e)),mt(this.options.type)&&mt(e.type)&&e.set("type",this.options.type),this.dataItem=e,this.diagram._resumeModelRefresh(),t&&this.diagram._syncConnectionChanges())}},sourcePoint:function(){return this._resolvedSourceConnector?this._resolvedSourceConnector.position():this._sourcePoint},_setSource:function(t){var i,n=t instanceof x,o=this.options.fromConnector||zt;n&&!t.getConnector(o)||(t!==e&&(this.from=t),this._removeFromSourceConnector(),null===t?this.sourceConnector&&(this._sourcePoint=(this._resolvedSourceConnector||this.sourceConnector).position(),this._clearSourceConnector(),this._setFromOptions(null,this._sourcePoint)):t instanceof w?(i=t.shape.dataItem,i&&this._setFromOptions(i.id),this.sourceConnector=t,this.sourceConnector.connections.push(this)):t instanceof $?(this._setFromOptions(null,t),this._sourcePoint=t,this.sourceConnector&&this._clearSourceConnector()):n&&(i=t.dataItem,i&&this._setFromOptions(i.id),this.sourceConnector=t.getConnector(o),this.sourceConnector.connections.push(this)))},source:function(t,e){return gt(t)&&(e&&this.diagram&&this.diagram.undoRedoService.addCompositeItem(new z.ConnectionEditUnit(this,t)),this._setSource(t),this.refresh()),this.sourceConnector?this.sourceConnector:this._sourcePoint},_setFromOptions:function(t,e){this.options.from=t,e?(this.options.fromX=e.x,this.options.fromY=e.y):(this.options.fromX=null,this.options.fromY=null)},sourceDefiner:function(t){if(!t)return this._sourceDefiner||(this._sourceDefiner=new z.PathDefiner(this.sourcePoint(),null,null)),this._sourceDefiner;if(!(t instanceof z.PathDefiner))throw"The sourceDefiner needs to be a PathDefiner.";t.left=null,this._sourceDefiner=t,this.source(t.point)},targetPoint:function(){return this._resolvedTargetConnector?this._resolvedTargetConnector.position():this._targetPoint},_setTarget:function(t){var i,n=t instanceof x,o=this.options.toConnector||zt;n&&!t.getConnector(o)||(t!==e&&(this.to=t),this._removeFromTargetConnector(),null===t?this.targetConnector&&(this._targetPoint=(this._resolvedTargetConnector||this.targetConnector).position(),this._clearTargetConnector(),this._setToOptions(null,this._targetPoint)):t instanceof w?(i=t.shape.dataItem,i&&this._setToOptions(i.id),this.targetConnector=t,this.targetConnector.connections.push(this)):t instanceof $?(this._setToOptions(null,t),this._targetPoint=t,this.targetConnector&&this._clearTargetConnector()):n&&(i=t.dataItem,i&&this._setToOptions(i.id),this.targetConnector=t.getConnector(o),this.targetConnector.connections.push(this)))},target:function(t,i){return gt(t)&&(i&&this.diagram&&this.diagram.undoRedoService.addCompositeItem(new z.ConnectionEditUnit(this,e,t)),this._setTarget(t),this.refresh()),this.targetConnector?this.targetConnector:this._targetPoint},_setToOptions:function(t,e){this.options.to=t,e?(this.options.toX=e.x,this.options.toY=e.y):(this.options.toX=null,this.options.toY=null)},targetDefiner:function(t){if(!t)return this._targetDefiner||(this._targetDefiner=new z.PathDefiner(this.targetPoint(),null,null)),this._targetDefiner;if(!(t instanceof z.PathDefiner))throw"The sourceDefiner needs to be a PathDefiner.";t.right=null,this._targetDefiner=t,this.target(t.point)},_updateConnectors:function(){this._updateConnector(this.source(),"source"),this._updateConnector(this.target(),"target")},_updateConnector:function(t,e){var i,n,o,s,r=this,a=r.diagram;t instanceof w&&!a.getShapeById(t.shape.id)?(i=t.shape.dataItem,n=t.options.name,o=function(){var o=a._dataMap[i.id];t=o.getConnector(n),r[e](t,!1),r.updateModel()},a._dataMap[i.id]?o():(s=a._inactiveShapeItems.getByUid(i.uid),s&&a._deferredConnectionUpdates.push(s.onActivate(o)))):r[e](t,!1)},content:function(t){var e=this._content(t);return mt(t)&&this._alignContent(),e},_createContentVisual:function(t){var e;return vt(t.visual)?e=t.visual.call(this,t):t.text&&(e=new J(t)),e&&(this._contentVisual=e,e._includeInBBox=!1,this.visual.append(e)),e},_updateContentVisual:function(t){vt(t.visual)?(this.visual.remove(this._contentVisual),this._createContentVisual(t)):this._contentVisual.redraw(t)},_alignContent:function(){var t,e,i,n,o,s,r,a,h,c,d,l,u,f,p,g;if(this._contentVisual){for(t=pe,e=this.allPoints(),i=xt.floor(e.length/2),n=i-1;n>0&&e[n].equals(e[i]);)n--,i++;o=e[i],s=e[n],r=this._contentVisual._measure(),a=r.width,h=r.height,c=e.length%2===0,d=s.distanceTo(o),c&&e.length>2&&d>0&&(s.y===o.y&&d<a||s.x===o.x&&d<h)&&(c=!1,t=0),c?(u=L.util.deg(xt.atan2(o.y-s.y,o.x-s.x)),l=new $((o.x-s.x)/2+s.x,(o.y-s.y)/2+s.y),90===xt.abs(u)?(l.x+=t,l.y-=h/2):u%180===0?(l.x-=a/2,l.y-=h+t):u<-90||0<u&&u<90?l.y-=h:(u<0||u>90)&&(l.x-=a,l.y-=h)):(f=xt.floor(e.length/2),l=e[f].clone(),s=e[f-1],o=e[f+1],p=s.x<=l.x&&o.x<=l.x?t:-r.width-t,g=s.y<=l.y&&o.y<=l.y?t:-r.height-t,l.x+=p,l.y+=g),this._contentVisual.position(l)}},select:function(t){var i,n,o=this.diagram;if(this._canSelect()&&this.isSelected!==t)return this.isSelected=t,i=[],n=[],this.isSelected?(this.adorner=new et(this,this.options.selection),o._adorn(this.adorner,!0),o._selectedItems.push(this),i.push(this)):this.adorner&&(o._adorn(this.adorner,!1),ht.remove(o._selectedItems,this),this.adorner=e,n.push(this)),this.adorner&&this.adorner.refresh(),o._internalSelection||o._selectionChanged(i,n),!0},bounds:function(t){return!t||yt(t)?this._bounds:(this._bounds=t,e)},type:function(t){var i=this.options;return t?(t!==i.type&&(i.type=t,this._initRouter(),this.refresh()),e):i.type},_initRouter:function(){var t=(this.options.type||"").toLowerCase();this._router=t==Ct?new ft(this):new ut(this)},points:function(t){var e,i,n,o;if(!t){if(n=[],gt(this.definers))for(o=0;o<this.definers.length;o++)n.push(this.definers[o].point);return n}for(this.definers=[],e=0;e<t.length;e++)if(i=t[e],i instanceof z.Point)this.definers.push(new z.PathDefiner(i));else{if(!i.hasOwnProperty("x")||!i.hasOwnProperty("y"))throw"A Connection point needs to be a Point or an object with x and y properties.";this.definers.push(new z.PathDefiner(new $(i.x,i.y)))}},allPoints:function(){var t,e=[this.sourcePoint()];if(this.definers)for(t=0;t<this.definers.length;t++)e.push(this.definers[t].point);return e.push(this.targetPoint()),e},refresh:function(){this._resolveConnectors(),this._refreshPath(),this._alignContent(),this.adorner&&this.adorner.refresh()},_resolveConnectors:function(){var t,e,i,s,r=this,a=r.source(),h=r.target();a instanceof $?t=a:a instanceof w&&(i=n(a)?a.shape.connectors:[a]),h instanceof $?e=h:h instanceof w&&(s=n(h)?h.shape.connectors:[h]),t?s&&(r._resolvedTargetConnector=o(t,s)):i&&(e?r._resolvedSourceConnector=o(e,i):s&&this._resolveAutoConnectors(i,s))},_resolveAutoConnectors:function(t,e){var i,o,s,r,a,h,c,d,l,u,f,p=At,g=At;for(l=0;l<t.length;l++)if(c=t[l],!n(c))for(s=c.position(),u=0;u<e.length;u++)d=e[u],n(d)||(r=d.position(),f=xt.round(s.distanceTo(r)),f<p&&this.diagram&&this._testRoutePoints(s,r,c,d)&&(p=f,i=c,o=d),f<g&&(a=c,h=d,g=f));i&&(a=i,h=o),this._resolvedSourceConnector=a,this._resolvedTargetConnector=h},_testRoutePoints:function(t,e,i,n){var o,s,r,a,h,c,d=this._router,l=!0;if(d instanceof ft)for(o=d.routePoints(t,e,i,n),h=this._getRouteExclude(t,e,i.shape,n.shape),o.unshift(t),o.push(e),c=1;c<o.length;c++)if(s=o[c-1],r=o[c],a=new X(xt.min(s.x,r.x),xt.min(s.y,r.y),xt.abs(s.x-r.x),xt.abs(s.y-r.y)),a.width>0&&(a.x++,a.width-=2),a.height>0&&(a.y++,a.height-=2),!a.isEmpty()&&this.diagram._shapesQuadTree.hitTestRect(a,h)){l=!1;break}return l},_getRouteExclude:function(t,e,i,n){var o=[];return this._isPointInsideShape(t,i)&&o.push(i),this._isPointInsideShape(e,n)&&o.push(n),o},_isPointInsideShape:function(t,e){var i,n,o,s=e.bounds(),r=e.rotate().angle,a=s.x,h=s.y;return i=t.clone().rotate(s.center(),r),n=i.x,o=i.y,n>a&&n<a+s.width&&o>h&&o<h+s.height},redraw:function(t){if(t){this.options=O({},this.options,t);
var e=this.options.points;mt(e)&&e.length>0&&(this.points(e),this._refreshPath()),(t&&t.content||t.text)&&this.content(t.content),this.path.redraw({fill:t.fill,stroke:t.stroke,startCap:t.startCap,endCap:t.endCap})}},clone:function(){var t=this.serialize();return this.diagram&&this.diagram._isEditable&&mt(this.dataItem)&&(t.options.dataItem=l(this.dataItem)),new b(this.from,this.to,t.options)},serialize:function(){var t=this.from.toJSON?this.from.toJSON:""+this.from,e=this.to.toJSON?this.to.toJSON:""+this.to,i=O({},{options:this.options,from:t,to:e});return mt(this.dataItem)&&(i.dataItem=""+this.dataItem),i.options.points=this.points(),i},_hitTest:function(t){if(this.visible()){var e=new $(t.x,t.y),i=this.sourcePoint(),n=this.targetPoint();if(t.isEmpty&&!t.isEmpty()&&t.contains(i)&&t.contains(n))return this;if(this._router.hitTest(e))return this}},_hover:function(t){var e=(this.options.stroke||{}).color;t&&gt(this.options.hover.stroke.color)&&(e=this.options.hover.stroke.color),this.path.redraw({stroke:{color:e}})},_refreshPath:function(){mt(this.path)&&(this._drawPath(),this.bounds(this._router.getBounds()))},_drawPath:function(){var t,e,i;this._router&&this._router.route(),t=this.sourcePoint(),e=this.targetPoint(),i=this.points(),this.path.redraw({points:[t].concat(i,[e])})},_clearSourceConnector:function(){this.sourceConnector=e,this._resolvedSourceConnector=e},_clearTargetConnector:function(){this.targetConnector=e,this._resolvedTargetConnector=e},_removeFromSourceConnector:function(){this.sourceConnector&&ht.remove(this.sourceConnector.connections,this)},_removeFromTargetConnector:function(){this.targetConnector&&ht.remove(this.targetConnector.connections,this)},toJSON:function(){var t,e,i,n=this;return n.from&&n.from.toJSON?t=n.from.toJSON():(i=n._sourcePoint,t={x:i.x,y:i.y}),n.to&&n.to.toJSON?e=n.to.toJSON():(i=n._targetPoint,e={x:i.x,y:i.y}),{from:t,to:e}}}),C=B.extend({init:function(t,e){var i=this;kendo.destroy(t),B.fn.init.call(i,t,e),i._initTheme(),i._initElements(),i._extendLayoutOptions(i.options),i._initDefaults(e),i._interactionDefaults(),i._initCanvas(),i.mainLayer=new G({id:"main-layer"}),i.canvas.append(i.mainLayer),i._shapesQuadTree=new I(i),i._pan=new $,i._adorners=[],i.adornerLayer=new G({id:"adorner-layer"}),i.canvas.append(i.adornerLayer),i._createHandlers(),i._initialize(),i._resizingAdorner=new st(i,{editable:i.options.editable}),i._connectorsAdorner=new rt(i),i._adorn(i._resizingAdorner,!0),i._adorn(i._connectorsAdorner,!0),i.selector=new ot(i),i._clipboard=[],i.pauseMouseHandlers=!1,i._fetchFreshData(),i._createGlobalToolBar(),i._createOptionElements(),i.zoom(i.options.zoom),i.canvas.draw()},options:{name:"Diagram",theme:"default",layout:"",zoomRate:.1,zoom:1,zoomMin:0,zoomMax:2,dataSource:{},draggable:!0,template:"",autoBind:!0,editable:{rotate:{},resize:{},text:!0,tools:[],drag:{snap:{size:10,angle:10}},remove:!0},pannable:{},selectable:{key:"none"},tooltip:{enabled:!0,format:"{0}"},copy:{enabled:!0,offsetX:20,offsetY:20},shapeDefaults:z.shapeDefaults({undoable:!0}),connectionDefaults:{editable:{tools:[]},type:Ct},shapes:[],connections:[]},events:[Gt,Ft,Ht,Ut,Vt,St,kt,Tt,Pt,Lt,"toolBarClick","save","cancel","edit","remove","add","dataBound",It,Mt,Dt],items:function(){return t()},_createGlobalToolBar:function(){var t,e=this.options.editable;e&&(t=e.tools,!this._isEditable||t===!1||t&&0!==t.length||(t=["createShape","undo","redo","rotateClockwise","rotateAnticlockwise"]),t&&t.length&&(this.toolBar=new S(this,{tools:t||{},click:N(this._toolBarClick,this),modal:!1}),this.toolBar.element.css({textAlign:"left"}),this.element.prepend(this.toolBar.element),this._resize()))},createShape:function(){var t,e,i,n,o,s;(this.editor&&this.editor.end()||!this.editor)&&(t=this.dataSource,e=t.view()||[],i=e.length,n=f(t,{}),o=this._createShape(n,{}),this.trigger("add",{shape:o})||(t.insert(i,n),s=this._inactiveShapeItems.getByUid(n.uid),s.element=o,this.edit(o)))},_createShape:function(t,e){e=O({},this.options.shapeDefaults,e),e.dataItem=t;var i=new x(e,this);return i},createConnection:function(){var t,e,i,n,o;(this.editor&&this.editor.end()||!this.editor)&&(t=this.connectionsDataSource,e=t.view()||[],i=e.length,n=f(t,{}),o=this._createConnection(n),this.trigger("add",{connection:o})||(this._connectionsDataMap[n.uid]=o,t.insert(i,n),this.addConnection(o,!1),this.edit(o)))},_createConnection:function(t,e,i){var n,o=O({},this.options.connectionDefaults);return o.dataItem=t,n=new b(e||new $,i||new $,o)},editModel:function(t,e){var i,n,o,s;if(this.cancelEdit(),o=this.options.editable,"shape"==e)i=o.shapeEditors,n=o.shapeTemplate;else{if("connection"!=e)return;s=N(h,this),i=O({},{from:s,to:s},o.connectionEditors),n=o.connectionTemplate}this.editor=new T(this.element,{update:N(this._update,this),cancel:N(this._cancel,this),model:t,type:e,target:this,editors:i,template:n}),this.trigger("edit",this._editArgs())},edit:function(t){if(t.dataItem){var e=t instanceof x?"shape":"connection";this.editModel(t.dataItem,e)}},cancelEdit:function(){this.editor&&(this._getEditDataSource().cancelChanges(this.editor.model),this._destroyEditor())},saveEdit:function(){this.editor&&this.editor.end()&&!this.trigger("save",this._editArgs())&&this._getEditDataSource().sync()},_update:function(){this.editor&&this.editor.end()&&!this.trigger("save",this._editArgs())&&(this._getEditDataSource().sync(),this._destroyEditor())},_cancel:function(){var t,e;this.editor&&!this.trigger("cancel",this._editArgs())&&(t=this.editor.model,this._getEditDataSource().cancelChanges(t),e=this._connectionsDataMap[t.uid]||this._dataMap[t.id],e&&e._setOptionsFromModel(t),this._destroyEditor())},_getEditDataSource:function(){return"shape"===this.editor.options.type?this.dataSource:this.connectionsDataSource},_editArgs:function(){var t={container:this.editor.wrapper};return t[this.editor.options.type]=this.editor.model,t},_destroyEditor:function(){this.editor&&(this.editor.close(),this.editor=null)},_initElements:function(){this.wrapper=this.element.empty().css("position","relative").attr("tabindex",0).addClass("k-widget k-diagram"),this.scrollable=t("<div />").appendTo(this.element)},_initDefaults:function(t){var e=this.options,i=e.editable,n=e.shapeDefaults,o=e.connectionDefaults,s=(t||{}).shapeDefaults;i===!1?(n.editable=!1,o.editable=!1):(g(i,n.editable,["drag","remove","connect"]),g(i,o.editable,["drag","remove"])),s&&s.connectors&&(e.shapeDefaults.connectors=s.connectors)},_interactionDefaults:function(){var t=this.options,e=t.selectable,i=t.pannable,n=kendo.support.mobileOS;e&&!mt(e.multiple)&&(t.selectable=O({multiple:!n},t.selectable)),i&&!mt(i.key)&&(t.pannable=O({key:n?"none":"ctrl"},t.pannable))},_initCanvas:function(){var e=t("<div class='k-layer'></div>").appendTo(this.scrollable)[0],i=this.viewport();this.canvas=new F(e,{width:i.width||Kt,height:i.height||Wt})},_createHandlers:function(){var t=this,e=t.element;e.on(de,N(t._wheel,t)).on("keydown"+bt,N(t._keydown,t)),t._userEvents=new kendo.UserEvents(this.scrollable,{multiTouch:!0,fastTap:!0,tap:N(t._tap,t),start:N(t._dragStart,t),move:N(t._drag,t),end:N(t._dragEnd,t),gesturestart:N(t._gestureStart,t),gesturechange:N(t._gestureChange,t),gestureend:N(t._gestureEnd,t),doubleTap:N(t._doubleTap,t),supportDoubleTap:!0}),t.toolService=new nt(t),this.scrollable.on("mouseover"+bt,N(t._mouseover,t)).on("mouseout"+bt,N(t._mouseout,t)).on("mousemove"+bt,N(t._mouseMove,t)).on("mousedown"+bt,N(t._mouseDown,t)).on("mouseup"+bt,N(t._mouseUp,t)),this._syncHandler=N(t._syncChanges,t),t._resizeHandler=N(t.resize,t,!1),kendo.onResize(t._resizeHandler),this.bind(Ft,N(t._destroyToolBar,t)),this.bind(Ht,N(t._destroyToolBar,t))},_dragStart:function(t){var e,i;this._pauseMouseHandlers=!0,e=this._eventPositions(t,!0),i=t.event,this.toolService.start(e,this._meta(i))&&(this._destroyToolBar(),i.preventDefault())},_drag:function(t){var e=this._eventPositions(t),i=t.event;this.toolService.move(e,this._meta(i))&&i.preventDefault()},_dragEnd:function(t){var e,i;this._pauseMouseHandlers=!1,e=this._eventPositions(t),i=t.event,this.toolService.end(e,this._meta(i))&&(this._createToolBar(),i.preventDefault())},_mouseMove:function(t){if(!this._pauseMouseHandlers){var e=this._eventPositions(t);this.toolService._updateHoveredItem(e),this.toolService._updateCursor(e)}},_mouseDown:function(){this._pauseMouseHandlers=!0},_mouseUp:function(){this._pauseMouseHandlers=!1},_tap:function(t){var e,i,n,o=this.toolService,s=this.options.selectable,r=this._eventPositions(t),a=this.focus();o._updateHoveredItem(r),o.hoveredItem?(e=o.hoveredItem,this.trigger("click",{item:e,point:r}),s&&e.options.selectable!==!1&&(i=s.multiple!==!1,n=kendo.support.mobileOS||this._meta(t.event).ctrlKey,e.isSelected?n?(this._destroyToolBar(),e.select(!1)):this._createToolBar(a):(this._destroyToolBar(),this.select(e,{addToSelection:i&&n}),this._createToolBar(a)))):s&&(this._destroyToolBar(),this.deselect())},_keydown:function(t){this.toolService.keyDown(t.keyCode,this._meta(t))&&t.preventDefault()},_wheel:function(t){var e=i(t),n=this._eventPositions(t),o=O(this._meta(t),{delta:e});this.toolService.wheel(n,o)&&t.preventDefault()},_meta:function(t){return{ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,shiftKey:t.shiftKey,type:t.type}},_eventPositions:function(t,e){var i,n,o;return t.touch?(n=e?"startLocation":"location",i=new $(t.x[n],t.y[n])):(o=t.originalEvent,i=new $(o.pageX,o.pageY)),this.documentToModel(i)},_gestureStart:function(t){var e,i;this._destroyToolBar(),this.scroller.disable(),e=this.documentToModel(new $(t.center.x,t.center.y)),i={point:e,zoom:this.zoom()},this.trigger(Ft,i)||(this._gesture=t,this._initialCenter=e)},_gestureChange:function(t){var e,i,n=this._gesture,o=this._initialCenter,s=this.documentToView(new $(t.center.x,t.center.y)),r=t.distance/n.distance,a=this._zoom,h=!1;xt.abs(r-1)>=le&&(this._zoom=a=this._getValidZoom(a*r),this.options.zoom=a,this._gesture=t,h=!0),e=o.times(a),i=s.minus(e),(h||this._pan.distanceTo(i)>=ue)&&(this._panTransform(i),this._updateAdorners()),t.preventDefault()},_doubleTap:function(t){var e=this,i=this._eventPositions(t),n=e.options,o=n.zoomRate,s=e.zoom()+o,r=this._meta(t),a={point:i,meta:r,zoom:s};e.trigger(Ft,a)||(s=kendo.dataviz.round(Math.max(n.zoomMin,Math.min(n.zoomMax,s)),2),a.zoom=s,e.zoom(s,a),e.trigger(Gt,a))},_gestureEnd:function(){this.options.pannable!==!1&&this.scroller.enable(),this.trigger(Gt,{point:this._initialCenter,zoom:this.zoom()})},_resize:function(){var t=this.viewport();this.canvas&&this.canvas.size(t),this.scrollable&&this.toolBar&&this.scrollable.height(t.height)},_mouseover:function(t){var e=t.target._kendoNode;e&&e.srcElement._hover&&e.srcElement._hover(!0,e.srcElement)},_mouseout:function(t){var e=t.target._kendoNode;e&&e.srcElement._hover&&e.srcElement._hover(!1,e.srcElement)},_initTheme:function(){var t,e=this,i=((e.options||{}).theme||"").toLowerCase(),n=P.ui.themes||{};t=P.SASS_THEMES.indexOf(i)!=-1?P.autoTheme().diagram:(n[i]||{}).diagram,e.options=O({},t,e.options),e.options.editable===!0&&O(e.options,{editable:(t||{}).editable})},_createOptionElements:function(){var t=this.options,e=t.shapes.length;e&&this._createShapes(),t.connections.length&&this._createConnections(),e&&t.layout&&this.layout(t.layout)},_createShapes:function(){var t,e,i=this,n=i.options,o=n.shapes;for(e=0;e<o.length;e++)t=o[e],i.addShape(t)},_createConnections:function(){var t,e,i,n,o=this,s=o.options,r=s.connectionDefaults,a=s.connections;for(n=0;n<a.length;n++)t=a[n],e=o._findConnectionTarget(t.from),i=o._findConnectionTarget(t.to),o.connect(e,i,O({},r,t))},_findConnectionTarget:function(t){var e,i,n;return t=t||{},e=this,i=yt(t)?t:t.shapeId||t.id,i?(n=e.getShapeById(i),t.connector&&(n=n.getConnector(t.connector))):n=new $(t.x||0,t.y||0),n},destroy:function(){var t=this;B.fn.destroy.call(t),this._userEvents&&this._userEvents.destroy(),kendo.unbindResize(t._resizeHandler),t.clear(),t.element.off(bt),t.scroller.wrapper.off(bt),t.canvas.destroy(!0),t.canvas=e,t._destroyEditor(),t.destroyScroller(),t._destroyGlobalToolBar(),t._destroyToolBar()},destroyScroller:function(){var t=this.scroller;t&&(t.destroy(),t.element.remove(),this.scroller=null)},save:function(){var t,e,i,n={shapes:[],connections:[]};for(t=0;t<this.shapes.length;t++)i=this.shapes[t],i.options.serializable&&n.shapes.push(i.options);for(t=0;t<this.connections.length;t++)e=this.connections[t],n.connections.push(O({},e.options,e.toJSON()));return n},focus:function(){if(!this.element.is(kendo._activeElement())){var t,e=this.element,i=e[0],n=[],o=[],s=document.documentElement;do i=i.parentNode,i.scrollHeight>i.clientHeight&&(n.push(i),o.push(i.scrollTop));while(i!=s);for(e.focus(),t=0;t<n.length;t++)n[t].scrollTop=o[t];return!0}},load:function(t){this.clear(),this.setOptions(t),this._createShapes(),this._createConnections()},setOptions:function(t){O(this.options,t)},clear:function(){var t=this;t.select(!1),t.mainLayer.clear(),t._shapesQuadTree.clear(),t._initialize()},connect:function(t,e,i){var n,o;return this.connectionsDataSource&&this._isEditable?(o=this.connectionsDataSource.add({}),n=this._connectionsDataMap[o.uid],n.source(t),n.target(e),n.redraw(i),n.updateModel()):(n=new b(t,e,O({},this.options.connectionDefaults,i)),this.addConnection(n)),n},connected:function(t,e){var i,n;for(i=0;i<this.connections.length;i++)if(n=this.connections[i],n.from==t&&n.to==e)return!0;return!1},addConnection:function(t,e){return e!==!1&&this.undoRedoService.add(new z.AddConnectionUnit(t,this),!1),t.diagram=this,t._setOptionsFromModel(),t.refresh(),this.mainLayer.append(t.visual),this.connections.push(t),this.trigger(kt,{added:[t],removed:[]}),t},_addConnection:function(t,e){var i,n=this.connectionsDataSource;if(n&&this._isEditable){if(i=f(n,l(t.dataItem)),t.dataItem=i,t.updateModel(),!this.trigger("add",{connection:t}))return this._connectionsDataMap[i.uid]=t,n.add(i),this.addConnection(t,e),t._updateConnectors(),t}else if(!this.trigger("add",{connection:t}))return this.addConnection(t,e),t._updateConnectors(),t},addShape:function(t,e){var i,n=this.options.shapeDefaults;if(t instanceof x)i=t;else{if(t instanceof kendo.Class)return;n=O({},n,t||{}),i=new x(n,this)}return e!==!1&&this.undoRedoService.add(new z.AddShapeUnit(i,this),!1),this.shapes.push(i),i.diagram!==this&&(this._shapesQuadTree.insert(i),i.diagram=this),this.mainLayer.append(i.visual),this.trigger(kt,{added:[i],removed:[]}),i},_addShape:function(t,e){var i,n,o=this,s=o.dataSource;if(s&&this._isEditable){if(i=f(s,l(t.dataItem)),t.dataItem=i,t.updateModel(),!this.trigger("add",{shape:t}))return this.dataSource.add(i),n=this._inactiveShapeItems.getByUid(i.uid),n.element=t,n.undoable=e,t}else if(!this.trigger("add",{shape:t}))return this.addShape(t,e)},remove:function(t,e){var i,n,o,s;for(t=_t(t)?t.slice(0):[t],i=u(t),n=i.shapes,o=i.connections,mt(e)||(e=!0),e&&this.undoRedoService.begin(),this._suspendModelRefresh(),s=n.length-1;s>=0;s--)this._removeItem(n[s],e,o);for(s=o.length-1;s>=0;s--)this._removeItem(o[s],e);this._resumeModelRefresh(),e&&this.undoRedoService.commit(!1),this.trigger(kt,{added:[],removed:t})},_removeShapeDataItem:function(t){this._isEditable&&(this.dataSource.remove(t.dataItem),delete this._dataMap[t.dataItem.id])},_removeConnectionDataItem:function(t){this._isEditable&&(this.connectionsDataSource.remove(t.dataItem),delete this._connectionsDataMap[t.dataItem.uid])},_triggerRemove:function(t){var e,i,n,o,s=[];for(o=0;o<t.length;o++)e=t[o],n=e.options.editable,i=e instanceof x?{shape:e}:{connection:e},n&&n.remove!==!1&&!this.trigger("remove",i)&&s.push(e);return s},undo:function(){this.undoRedoService.undo()},redo:function(){this.undoRedoService.redo()},select:function(t,e){if(!gt(t))return this._selectedItems;e=O({addToSelection:!1},e);var i,n,o=e.addToSelection,s=[],r=[];for(o||this.deselect(),this._internalSelection=!0,t instanceof Array?s=t:t instanceof y&&(s=[t]),i=0;i<s.length;i++)n=s[i],n.select(!0)&&r.push(n);this._selectionChanged(r,[]),this._internalSelection=!1},selectAll:function(){this.select(this.shapes.concat(this.connections))},selectArea:function(t){var e,i,n,o;if(this._internalSelection=!0,o=[],t instanceof X)for(i=this.shapes.concat(this.connections),e=0;e<i.length;e++)n=i[e],t&&!n._hitTest(t)||!n.options.enable||n.select(!0)&&o.push(n);this._selectionChanged(o,[]),this._internalSelection=!1},deselect:function(t){this._internalSelection=!0;var e,i,n=[],o=[];for(t instanceof Array?o=t:t instanceof y?o.push(t):gt(t)||(o=this._selectedItems.slice(0)),i=0;i<o.length;i++)e=o[i],e.select(!1)&&n.push(e);this._selectionChanged([],n),this._internalSelection=!1},toFront:function(t,e){var i,n,o;t||(t=this._selectedItems.slice()),i=this._getDiagramItems(t),!mt(e)||e?(n=s(this.mainLayer,i.visuals),o=new lt(this,t,n),this.undoRedoService.add(o)):(this.mainLayer.toFront(i.visuals),this._fixOrdering(i,!0))},toBack:function(t,e){var i,n,o;t||(t=this._selectedItems.slice()),i=this._getDiagramItems(t),!mt(e)||e?(n=s(this.mainLayer,i.visuals),o=new dt(this,t,n),this.undoRedoService.add(o)):(this.mainLayer.toBack(i.visuals),this._fixOrdering(i,!1))},bringIntoView:function(t,e){var i,n,o,s,r=this.viewport(),a=new z.RectAlign(r);0!==r.width&&0!==r.height&&(e=O({animate:!1,align:"center middle"},e),"none"==e.align&&(e.align="center middle"),t instanceof y?n=t.bounds(ne):_t(t)?n=this.boundingBox(t):t instanceof X&&(n=t.clone()),o=n.clone(),n.zoom(this._zoom),(n.width>r.width||n.height>r.height)&&(this._zoom=this._getValidZoom(xt.min(r.width/o.width,r.height/o.height)),n=o.clone().zoom(this._zoom)),this._zoomMainLayer(),i=n.clone(),a.align(n,e.align),s=n.topLeft().minus(i.topLeft()),this.pan(s.times(-1),e.animate))},alignShapes:function(t){var e,i,n,o,s,r,a;if(pt(t)&&(t="Left"),e=this.select(),0!==e.length){switch(t.toLowerCase()){case"left":case"top":i=te;break;case"right":case"bottom":i=ee}for(o=0;o<e.length;o++)if(n=e[o],n instanceof x)switch(t.toLowerCase()){case"left":i=xt.min(i,n.options.x);break;case"top":i=xt.min(i,n.options.y);break;case"right":i=xt.max(i,n.options.x);break;case"bottom":i=xt.max(i,n.options.y)}for(s=[],r=[],o=0;o<e.length;o++)if(n=e[o],n instanceof x)switch(r.push(n),s.push(n.bounds()),t.toLowerCase()){case"left":case"right":n.position(new $(i,n.options.y));break;case"top":case"bottom":n.position(new $(n.options.x,i))}a=new z.TransformUnit(r,s),this.undoRedoService.add(a,!1)}},zoom:function(t,e){var i,n,o,s;return t&&(i=e?e.point:new z.Point(0,0),t=this._zoom=this._getValidZoom(t),pt(i)||(i=new z.Point(xt.round(i.x),xt.round(i.y)),n=i.times(t),o=this.modelToView(i),s=o.minus(n),this._storePan(new z.Point(xt.round(s.x),xt.round(s.y)))),e&&(e.zoom=t),this._panTransform(),this.canvas.surface.hideTooltip(),this._updateAdorners()),this._zoom},_getPan:function(t){var e=this.canvas;return e.translate||(t=t.plus(this._pan)),t},pan:function(t,i){var n,o;return t instanceof $?(n=this,o=n.scroller,t=n._getPan(t),t=t.times(-1),i?o.animatedScrollTo(t.x,t.y,function(){n._updateAdorners()}):(o.scrollTo(t.x,t.y),n._updateAdorners()),e):this._pan.times(-1)},viewport:function(){var t=this.element,e=t.width(),i=t.height();return this.toolBar&&(i-=U(this.toolBar.element)),new X(0,0,e,i)},copy:function(){var t,e;if(this.options.copy.enabled)for(this._clipboard=[],this._copyOffset=1,t=0;t<this._selectedItems.length;t++)e=this._selectedItems[t],this._clipboard.push(e)},cut:function(){var t,e;if(this.options.copy.enabled){for(this._clipboard=[],this._copyOffset=0,t=0;t<this._selectedItems.length;t++)e=this._selectedItems[t],this._clipboard.push(e);this.remove(this._clipboard,!0)}},paste:function(){var t,e,i,n,o,s,r,a;if(this._clipboard.length>0){for(n={},o=u(this._clipboard),s=o.connections,r=o.shapes,a={x:this._copyOffset*this.options.copy.offsetX,y:this._copyOffset*this.options.copy.offsetY},this.deselect(),i=0;i<r.length;i++)t=r[i],e=t.clone(),n[t.id]=e,e.position(new $(t.options.x+a.x,t.options.y+a.y)),e.diagram=this,e=this._addShape(e),e&&e.select();for(i=0;i<s.length;i++)t=s[i],e=this._addConnection(t.clone()),e&&(this._updateCopiedConnection(e,t,"source",n,a),this._updateCopiedConnection(e,t,"target",n,a),e.select(!0),e.updateModel());this._syncChanges(),this._copyOffset+=1}},_updateCopiedConnection:function(t,e,i,n,o){var s,r,a,h=e[i](),c=this;h instanceof w&&n[h.shape.id]?(a=n[h.shape.id],c.getShapeById(a.id)?t[i](a.getConnector(h.options.name)):(r=c._inactiveShapeItems.getByUid(a.dataItem.uid),r&&(s=function(e){a=c._dataMap[e.id],t[i](a.getConnector(h.options.name)),t.updateModel()},c._deferredConnectionUpdates.push(r.onActivate(s))))):t[i](new $(e[i+"Point"]().x+o.x,e[i+"Point"]().y+o.y))},boundingBox:function(t,e){var i,n,o,s=X.empty(),r=gt(t)?this._getDiagramItems(t):{shapes:this.shapes};if(r.shapes.length>0)for(n=r.shapes[0],s=n.bounds(oe),o=1;o<r.shapes.length;o++)n=r.shapes[o],i=n.bounds(oe),e===!0&&(i.x-=n._rotationOffset.x,i.y-=n._rotationOffset.y),s=s.union(i);return s},_containerOffset:function(){var t=this.element.offset();return this.toolBar&&(t.top+=U(this.toolBar.element)),t},documentToView:function(t){var e=this._containerOffset();return new $(t.x-e.left,t.y-e.top)},viewToDocument:function(t){var e=this._containerOffset();return new $(t.x+e.left,t.y+e.top)},viewToModel:function(t){return this._transformWithMatrix(t,this._matrixInvert)},modelToView:function(t){return this._transformWithMatrix(t,this._matrix)},modelToLayer:function(t){return this._transformWithMatrix(t,this._layerMatrix)},layerToModel:function(t){return this._transformWithMatrix(t,this._layerMatrixInvert)},documentToModel:function(t){var e=this.documentToView(t);return this.canvas.translate||(e.x=e.x+this.scroller.scrollLeft,e.y=e.y+this.scroller.scrollTop),this.viewToModel(e)},modelToDocument:function(t){return this.viewToDocument(this.modelToView(t))},_transformWithMatrix:function(t,e){var i,n,o=t;return t instanceof $?e&&(o=e.apply(t)):(i=this._transformWithMatrix(t.topLeft(),e),n=this._transformWithMatrix(t.bottomRight(),e),o=X.fromPoints(i,n)),o},setDataSource:function(t){this.options.dataSource=t,this._dataSource(),this.options.autoBind&&this.dataSource.fetch()},setConnectionsDataSource:function(t){this.options.connectionsDataSource=t,this._connectionDataSource(),this.options.autoBind&&this.connectionsDataSource.fetch()},layout:function(t){var e,i,n,o,s;switch(this._layouting=!0,pt(t)&&(t=this.options.layout),e=pt(t)||pt(t.type)?"Tree":t.type,e.toLowerCase()){case"tree":i=new z.TreeLayout(this);break;case"layered":i=new z.LayeredLayout(this);break;case"forcedirected":case"force":case"spring":case"springembedder":i=new z.SpringLayout(this);break;default:throw"Layout algorithm '"+e+"' is not supported."}n=new z.LayoutState(this),o=i.layout(t),o&&(s=new z.LayoutUndoUnit(n,o,t?t.animate:null),this.undoRedoService.add(s)),this._layouting=!1,this._redrawConnections()},getShapeById:function(t){var e;return(e=ht.first(this.shapes,function(e){return e.visual.id===t}))?e:e=ht.first(this.connections,function(e){return e.visual.id===t})},getShapeByModelId:function(t){var e;return e=this._isEditable?this._dataMap[t]:ht.first(this.shapes,function(e){return(e.dataItem||{}).id===t})},getShapeByModelUid:function(t){var e;return e=this._isEditable?ht.first(this.shapes,function(e){return(e.dataItem||{}).uid===t}):this._dataMap[t]},getConnectionByModelId:function(t){var e;return this.connectionsDataSource&&(e=ht.first(this.connections,function(e){return(e.dataItem||{}).id===t})),e},getConnectionByModelUid:function(t){var e;return this.connectionsDataSource&&(e=this._connectionsDataMap[t]),e},_extendLayoutOptions:function(t){t.layout&&(t.layout=O({},z.LayoutBase.fn.defaultOptions||{},t.layout))},_selectionChanged:function(t,e){(t.length||e.length)&&this.trigger(Ut,{selected:t,deselected:e})},_getValidZoom:function(t){return xt.min(xt.max(t,this.options.zoomMin),this.options.zoomMax)},_panTransform:function(t){var e=this,i=t||e._pan;e.canvas.translate?(e.scroller.scrollTo(i.x,i.y),e._zoomMainLayer()):(e._storePan(i),e._transformMainLayer())},_finishPan:function(){this.trigger(Ht,{total:this._pan,delta:Number.NaN})},_storePan:function(t){this._pan=t,this._storeViewMatrix()},_zoomMainLayer:function(){var t=this._zoom,e=new W(0,0,t,t);e.render(this.mainLayer),this._storeLayerMatrix(e),this._storeViewMatrix()},_transformMainLayer:function(){var t=this._pan,e=this._zoom,i=new W(t.x,t.y,e,e);i.render(this.mainLayer),this._storeLayerMatrix(i),this._storeViewMatrix()},_storeLayerMatrix:function(t){this._layerMatrix=t.toMatrix(),this._layerMatrixInvert=t.invert().toMatrix()},_storeViewMatrix:function(){var t=this._pan,e=this._zoom,i=new W(t.x,t.y,e,e);this._matrix=i.toMatrix(),this._matrixInvert=i.invert().toMatrix()},_toIndex:function(t,e){var i=this._getDiagramItems(t);this.mainLayer.toIndex(i.visuals,e),this._fixOrdering(i,!1)},_fixOrdering:function(t,e){var i,n,o=e?this.shapes.length-1:0,s=e?this.connections.length-1:0;for(i=0;i<t.shapes.length;i++)n=t.shapes[i],ht.remove(this.shapes,n),ht.insert(this.shapes,n,o);for(i=0;i<t.cons.length;i++)n=t.cons[i],ht.remove(this.connections,n),ht.insert(this.connections,n,s)},_getDiagramItems:function(t){var e,i,n={},o=t;for(n.visuals=[],n.shapes=[],n.cons=[],t?_t(t)||(o=[t]):o=this._selectedItems.slice(),e=0;e<o.length;e++)i=o[e],i instanceof x?(n.shapes.push(i),n.visuals.push(i.visual)):i instanceof b&&(n.cons.push(i),n.visuals.push(i.visual));return n},_removeItem:function(t,e,i){t.select(!1),t instanceof x?(this._removeShapeDataItem(t),this._removeShape(t,e,i)):t instanceof b&&(this._removeConnectionDataItem(t),this._removeConnection(t,e)),this.mainLayer.remove(t.visual)},_removeShape:function(t,e,i){var n,o,s,r,a=[],h=[];for(this.toolService._removeHover(),e&&this.undoRedoService.addCompositeItem(new j(t)),ht.remove(this.shapes,t),this._shapesQuadTree.remove(t),n=0;n<t.connectors.length;n++)for(s=t.connectors[n],r=0;r<s.connections.length;r++)o=s.connections[r],i&&P.inArray(o,i)||(o.sourceConnector==s?a.push(o):o.targetConnector==s&&h.push(o));for(n=0;n<a.length;n++)a[n].source(null,e),a[n].updateModel();for(n=0;n<h.length;n++)h[n].target(null,e),h[n].updateModel()},_removeConnection:function(t,e){t.sourceConnector&&ht.remove(t.sourceConnector.connections,t),t.targetConnector&&ht.remove(t.targetConnector.connections,t),e&&this.undoRedoService.addCompositeItem(new Q(t)),ht.remove(this.connections,t)},_removeDataItems:function(t,e){var i,n,o,s;for(t=_t(t)?t:[t];t.length;)if(i=t.shift(),o=this._dataMap[i.uid],o&&(this._removeShapeConnections(o),this._removeItem(o,!1),delete this._dataMap[i.uid],e&&i.hasChildren&&i.loaded()))for(n=i.children.data(),s=0;s<n.length;s++)t.push(n[s])},_removeShapeConnections:function(t){var e,i=t.connections();if(i)for(e=0;e<i.length;e++)this._removeItem(i[e],!1)},_addDataItem:function(t,e){var i,n;if(mt(t))return(i=this._dataMap[t.id])?i:(n=O({},this.options.shapeDefaults),n.dataItem=t,i=new x(n,this),this.addShape(i,e!==!1),this._dataMap[t.id]=i,i)},_addDataItemByUid:function(t){var e,i;if(mt(t))return(e=this._dataMap[t.uid])?e:(i=O({},this.options.shapeDefaults),i.dataItem=t,e=new x(i,this),this.addShape(e),this._dataMap[t.uid]=e,e)},_addDataItems:function(t,e){var i,n,o,s,r;for(n=0;n<t.length;n++)i=t[n],o=this._addDataItemByUid(i),s=this._addDataItemByUid(e),s&&!this.connected(s,o)&&(r=this.connect(s,o))},_refreshSource:function(t){var e,i,n=this,o=t.node,s=t.action,r=t.items,a=n.options;if(t.field)for(e=0;e<r.length;e++)this._dataMap[r[e].uid]&&this._dataMap[r[e].uid].redrawVisual();else{if("remove"==s)this._removeDataItems(t.items,!0);else for(s&&"itemloaded"!==s||this._bindingRoots||(this._bindingRoots=!0,i=!0),s||o||n.clear(),this._addDataItems(r,o),e=0;e<r.length;e++)r[e].load();a.layout&&(i||"remove"==s||"add"==s)&&n.layout(a.layout),i&&(this.trigger("dataBound"),this._bindingRoots=!1)}},_addItem:function(t){t instanceof x?this.addShape(t):t instanceof b&&this.addConnection(t)},_createToolBar:function(t){var e,i,n,o,s,r,a,h,c,d=this.toolService.diagram;this.singleToolBar||1!==d.select().length||(e=d.select()[0],e&&e.options.editable!==!1&&(i=e.options.editable,n=i.tools,this._isEditable&&0===n.length&&(e instanceof x?n=["edit","rotateClockwise","rotateAnticlockwise"]:e instanceof b&&(n=["edit"]),i&&i.remove!==!1&&n.push("delete")),n&&n.length&&(o=20,this.singleToolBar=new S(d,{tools:n,click:N(this._toolBarClick,this),modal:!0,popupZIndex:parseInt(d.element.closest(".k-window").css("zIndex"),10)+10}),r=A(this.singleToolBar._popup.element),a=U(this.singleToolBar._popup.element),e instanceof x?(h=this.modelToView(e.bounds(oe)),s=new $(h.x,h.y).minus(new $((r-h.width)/2,a+o))):e instanceof b&&(c=this.modelToView(e.bounds()),s=new $(c.x,c.y).minus(new $((r-c.width-20)/2,a+o))),s?(this.canvas.translate||(s=s.minus(new $(this.scroller.scrollLeft,this.scroller.scrollTop))),s=this.viewToDocument(s),s=new $(xt.max(s.x,0),xt.max(s.y,0)),this.singleToolBar.showAt(s),t&&this.singleToolBar._popup.one("close",_)):this._destroyToolBar())))},_toolBarClick:function(t){this.trigger("toolBarClick",t),this._destroyToolBar()},_normalizePointZoom:function(t){return t.times(1/this.zoom())},_initialize:function(){this.shapes=[],this._selectedItems=[],this.connections=[],this._dataMap={},this._connectionsDataMap={},this._inactiveShapeItems=new d,this._deferredConnectionUpdates=[],this.undoRedoService=new it({undone:this._syncHandler,redone:this._syncHandler}),this.id=z.randomId()},_fetchFreshData:function(){var t=this;t._dataSource(),t._isEditable&&t._connectionDataSource(),t.options.autoBind&&(t._isEditable?(this._loadingShapes=!0,this._loadingConnections=!0,t.dataSource.fetch(),t.connectionsDataSource.fetch()):t.dataSource.fetch())},_dataSource:function(){var t,e;mt(this.options.connectionsDataSource)?(this._isEditable=!0,t=this.options.dataSource||{},e=_t(t)?{data:t}:t,this.dataSource&&this._shapesRefreshHandler?this.dataSource.unbind("change",this._shapesRefreshHandler).unbind("requestStart",this._shapesRequestStartHandler).unbind("error",this._shapesErrorHandler):(this._shapesRefreshHandler=N(this._refreshShapes,this),this._shapesRequestStartHandler=N(this._shapesRequestStart,this),this._shapesErrorHandler=N(this._error,this)),this.dataSource=kendo.data.DataSource.create(e).bind("change",this._shapesRefreshHandler).bind("requestStart",this._shapesRequestStartHandler).bind("error",this._shapesErrorHandler)):(this._treeDataSource(),this._isEditable=!1)},_connectionDataSource:function(){var t,e=this.options.connectionsDataSource;e&&(t=_t(e)?{data:e}:e,this.connectionsDataSource&&this._connectionsRefreshHandler?this.connectionsDataSource.unbind("change",this._connectionsRefreshHandler).unbind("requestStart",this._connectionsRequestStartHandler).unbind("error",this._connectionsErrorHandler):(this._connectionsRefreshHandler=N(this._refreshConnections,this),this._connectionsRequestStartHandler=N(this._connectionsRequestStart,this),this._connectionsErrorHandler=N(this._connectionsError,this)),this.connectionsDataSource=kendo.data.DataSource.create(t).bind("change",this._connectionsRefreshHandler).bind("requestStart",this._connectionsRequestStartHandler).bind("error",this._connectionsErrorHandler))},_shapesRequestStart:function(t){"read"==t.type&&(this._loadingShapes=!0)},_connectionsRequestStart:function(t){"read"==t.type&&(this._loadingConnections=!0)},_error:function(){this._loadingShapes=!1},_connectionsError:function(){this._loadingConnections=!1},_refreshShapes:function(t){"remove"===t.action?this._shouldRefresh()&&this._removeShapes(t.items):"itemchange"===t.action?this._shouldRefresh()&&this._updateShapes(t.items,t.field):"add"===t.action?this._inactiveShapeItems.add(t.items):"sync"===t.action?this._syncShapes(t.items):this.refresh()},_shouldRefresh:function(){return!this._suspended},_suspendModelRefresh:function(){this._suspended=(this._suspended||0)+1},_resumeModelRefresh:function(){this._suspended=xt.max((this._suspended||0)-1,0)},refresh:function(){this._loadingShapes=!1,this._loadingConnections||this._rebindShapesAndConnections();
},_rebindShapesAndConnections:function(){this.clear(),this._addShapes(this.dataSource.view()),this.connectionsDataSource&&this._addConnections(this.connectionsDataSource.view(),!1),this.options.layout?this.layout(this.options.layout):this._redrawConnections(),this.trigger("dataBound")},refreshConnections:function(){this._loadingConnections=!1,this._loadingShapes||this._rebindShapesAndConnections()},_redrawConnections:function(){var t,e=this.connections;for(t=0;t<e.length;t++)e[t].refresh()},_removeShapes:function(t){var e,i,n=this._dataMap;for(i=0;i<t.length;i++)e=t[i],n[e.id]&&(this.remove(n[e.id],!1),n[e.id]=null)},_syncShapes:function(){var t=this,e=t._inactiveShapeItems;e.forEach(function(i){var n=i.dataItem,o=i.element;n.isNew()||(o?(o._setOptionsFromModel(),t.addShape(o,i.undoable),t._dataMap[n.id]=o):t._addDataItem(n),i.activate(),e.remove(n))})},_updateShapes:function(t,e){var i,n,o;for(i=0;i<t.length;i++)n=t[i],o=this._dataMap[n.id],o&&o.updateOptionsFromModel(n,e)},_addShapes:function(t){for(var e=0;e<t.length;e++)this._addDataItem(t[e],!1)},_refreshConnections:function(t){"remove"===t.action?this._shouldRefresh()&&this._removeConnections(t.items):"add"===t.action?this._addConnections(t.items):"sync"===t.action||("itemchange"===t.action?this._shouldRefresh()&&this._updateConnections(t.items):this.refreshConnections())},_removeConnections:function(t){for(var e=0;e<t.length;e++)this.remove(this._connectionsDataMap[t[e].uid],!1),this._connectionsDataMap[t[e].uid]=null},_updateConnections:function(t){var e,i,n;for(e=0;e<t.length;e++)i=t[e],n=this._connectionsDataMap[i.uid],n.updateOptionsFromModel(i)},_addConnections:function(t,e){var i,n,o=t.length;for(i=0;i<o;i++)n=t[i],this._addConnectionDataItem(n,e)},_addConnectionDataItem:function(t,e){var i,n,o,s;this._connectionsDataMap[t.uid]||(i=this._validateConnector(t.from),mt(i)&&null!==i||(i=new $(t.fromX,t.fromY)),n=this._validateConnector(t.to),mt(n)&&null!==n||(n=new $(t.toX,t.toY)),mt(i)&&mt(n)&&(o=O({},this.options.connectionDefaults),o.dataItem=t,s=new b(i,n,o),this._connectionsDataMap[t.uid]=s,this.addConnection(s,e)))},_validateConnector:function(t){var e;return mt(t)&&null!==t&&(e=this._dataMap[t]),e},_treeDataSource:function(){var t=this,e=t.options,i=e.dataSource;if(i=_t(i)?{data:i}:i,i instanceof kendo.data.DataSource&&!(i instanceof kendo.data.HierarchicalDataSource))throw Error("Incorrect DataSource type. If a single dataSource instance is set to the diagram then it should be a HierarchicalDataSource. You should set only the options instead of an instance or a HierarchicalDataSource instance or supply connectionsDataSource as well.");i.fields||(i.fields=[{field:"text"},{field:"url"},{field:"spriteCssClass"},{field:"imageUrl"}]),t.dataSource&&t._refreshHandler&&t._unbindDataSource(),t._refreshHandler=N(t._refreshSource,t),t._errorHandler=N(t._error,t),t.dataSource=H.create(i).bind(kt,t._refreshHandler).bind(Et,t._errorHandler)},_unbindDataSource:function(){var t=this;t.dataSource.unbind(kt,t._refreshHandler).unbind(Et,t._errorHandler)},_adorn:function(t,i){i!==e&&t&&(i?(this._adorners.push(t),this.adornerLayer.append(t.visual)):(ht.remove(this._adorners,t),this.adornerLayer.remove(t.visual)))},_showConnectors:function(t,e){e?this._connectorsAdorner.show(t):this._connectorsAdorner.destroy()},_updateAdorners:function(){var t,e,i=this._adorners;for(t=0;t<i.length;t++)e=i[t],e.refreshBounds&&e.refreshBounds(),e.refresh()},_refresh:function(){for(var t=0;t<this.connections.length;t++)this.connections[t].refresh()},_destroyToolBar:function(){this.singleToolBar&&(this.singleToolBar.hide(),this.singleToolBar.destroy(),this.singleToolBar=null)},_destroyGlobalToolBar:function(){this.toolBar&&(this.toolBar.hide(),this.toolBar.destroy(),this.toolBar=null)},exportDOMVisual:function(){var t=this.canvas._viewBox,e=E.transform().translate(-t.x,-t.y),i=new E.Rect([0,0],[t.width,t.height]),n=L.Path.fromRect(i),o=new L.Group({transform:e}),s=new L.Group({clip:n}),r=this.canvas.drawingElement.children[0];return s.append(o),o.children.push(r),s},exportVisual:function(){var t=E.transform().scale(1/this._zoom),e=new L.Group({transform:t}),i=this.mainLayer.drawingElement;return e.children.push(i),e},_syncChanges:function(){this._syncShapeChanges(),this._syncConnectionChanges()},_syncShapeChanges:function(){this.dataSource&&this._isEditable&&this.dataSource.sync()},_syncConnectionChanges:function(){var e=this;e.connectionsDataSource&&e._isEditable&&(t.when.apply(t,e._deferredConnectionUpdates).then(function(){e.connectionsDataSource.sync()}),e.deferredConnectionUpdates=[])}}),P.ExportMixin.extend(C.fn,!0),kendo.PDFMixin&&kendo.PDFMixin.extend(C.fn),S=kendo.Observable.extend({init:function(t,e){kendo.Observable.fn.init.call(this),this.diagram=t,this.options=O({},this.options,e),this._tools=[],this.createToolBar(),this.createTools(),this.appendTools(),this.options.modal&&this.createPopup(),this.bind(this.events,e)},events:["click"],createPopup:function(){this.container=t("<div/>").append(this.element),this._popup=this.container.kendoPopup({}).getKendoPopup()},appendTools:function(){var t,e;for(t=0;t<this._tools.length;t++)e=this._tools[t],(e.buttons&&e.buttons.length||!mt(e.buttons))&&this._toolBar.add(e)},createToolBar:function(){this.element=t("<div/>"),this._toolBar=this.element.kendoToolBar({click:N(this.click,this),resizable:!1}).getKendoToolBar(),this.element.css("border","none")},createTools:function(){for(var t=0;t<this.options.tools.length;t++)this.createTool(this.options.tools[t])},createTool:function(t){wt(t)||(t={name:t});var e=t.name+"Tool";this[e]?this[e](t):this._tools.push(O({},t,{attributes:this._setAttributes({action:t.name})}))},showAt:function(t){var e=parseInt(this.options.popupZIndex,10);this._popup&&(this._popup.open(t.x,t.y),e&&this._popup.wrapper.css("zIndex",e))},hide:function(){this._popup&&this._popup.close()},newGroup:function(){return{type:"buttonGroup",buttons:[]}},editTool:function(){this._tools.push({icon:"edit",showText:"overflow",type:"button",text:"Edit",attributes:this._setAttributes({action:"edit"})})},deleteTool:function(){this._tools.push({icon:"close",showText:"overflow",type:"button",text:"Delete",attributes:this._setAttributes({action:"delete"})})},rotateAnticlockwiseTool:function(t){this._appendGroup("rotate"),this._rotateGroup.buttons.push({icon:"rotate-left",showText:"overflow",text:"RotateAnticlockwise",group:"rotate",attributes:this._setAttributes({action:"rotateAnticlockwise",step:t.step})})},rotateClockwiseTool:function(t){this._appendGroup("rotate"),this._rotateGroup.buttons.push({icon:"rotate-right",attributes:this._setAttributes({action:"rotateClockwise",step:t.step}),showText:"overflow",text:"RotateClockwise",group:"rotate"})},createShapeTool:function(){this._appendGroup("create"),this._createGroup.buttons.push({icon:"shape",showText:"overflow",text:"CreateShape",group:"create",attributes:this._setAttributes({action:"createShape"})})},createConnectionTool:function(){this._appendGroup("create"),this._createGroup.buttons.push({icon:"connector",showText:"overflow",text:"CreateConnection",group:"create",attributes:this._setAttributes({action:"createConnection"})})},undoTool:function(){this._appendGroup("history"),this._historyGroup.buttons.push({icon:"undo",showText:"overflow",text:"Undo",group:"history",attributes:this._setAttributes({action:"undo"})})},redoTool:function(){this._appendGroup("history"),this._historyGroup.buttons.push({icon:"redo",showText:"overflow",text:"Redo",group:"history",attributes:this._setAttributes({action:"redo"})})},_appendGroup:function(t){var e="_"+t+"Group";this[e]||(this[e]=this.newGroup(),this._tools.push(this[e]))},_setAttributes:function(t){var e={};return t.action&&(e[kendo.attr("action")]=t.action),t.step&&(e[kendo.attr("step")]=t.step),e},_getAttributes:function(t){var e,i={},n=t.attr(kendo.attr("action"));return n&&(i.action=n),e=t.attr(kendo.attr("step")),e&&(i.step=e),i},click:function(e){var i=this._getAttributes(t(e.target)),n=i.action;n&&this[n]&&this[n](i),this.trigger("click",this.eventData(n,e.target))},eventData:function(t,e){var i,n,o=this.selectedElements(),s=o.length,r=[],a=[];for(n=0;n<s;n++)i=o[n],i instanceof x?r.push(i):a.push(i);return{shapes:r,connections:a,action:t,target:e}},"delete":function(){var t=this.diagram,e=t._triggerRemove(this.selectedElements());e.length&&(this.diagram.remove(e,!0),this.diagram._syncChanges())},edit:function(){var t=this.selectedElements();1===t.length&&this.diagram.edit(t[0])},rotateClockwise:function(t){var e=parseFloat(t.step||90);this._rotate(e)},rotateAnticlockwise:function(t){var e=parseFloat(t.step||90);this._rotate(-e)},_rotate:function(t){var e=this.diagram._resizingAdorner;e.angle(e.angle()+t),e.rotate()},selectedElements:function(){return this.diagram.select()},createShape:function(){this.diagram.createShape()},createConnection:function(){this.diagram.createConnection()},undo:function(){this.diagram.undo()},redo:function(){this.diagram.redo()},destroy:function(){this.diagram=null,this.element=null,this.options=null,this._toolBar&&this._toolBar.destroy(),this._popup&&this._popup.destroy()}}),k=kendo.Observable.extend({init:function(t,e){kendo.Observable.fn.init.call(this),this.options=V(!0,{},this.options,e),this.element=t,this.model=this.options.model,this.fields=this._getFields(),this._initContainer(),this.createEditable()},options:{editors:{}},_initContainer:function(){this.wrapper=this.element},createEditable:function(){var t=this.options;this.editable=new kendo.ui.Editable(this.wrapper,{fields:this.fields,target:t.target,clearContainer:!1,model:this.model})},_isEditable:function(t){return this.model.editable&&this.model.editable(t)},_getFields:function(){var t,e,i,n=[],o=this.model.fields;for(t in o)e={},this._isEditable(t)&&(i=this.options.editors[t],i&&(e.editor=i),e.field=t,n.push(e));return n},end:function(){return this.editable.end()},destroy:function(){this.editable.destroy(),this.editable.element.find("["+kendo.attr("container-for")+"]").empty(),this.model=this.wrapper=this.element=this.columns=this.editable=null}}),T=k.extend({init:function(t,e){k.fn.init.call(this,t,e),this.bind(this.events,this.options),this.open()},events:["update","cancel"],options:{window:{modal:!0,resizable:!1,draggable:!0,title:"Edit",visible:!1}},_initContainer:function(){var e,i=this;this.wrapper=t('<div class="k-popup-edit-form"/>').attr(kendo.attr("uid"),this.model.uid),e="",this.options.template?(e+=this._renderTemplate(),this.fields=[]):e+=this._renderFields(),e+=this._renderButtons(),this.wrapper.append(t('<div class="k-edit-form-container"/>').append(e)),this.window=new kendo.ui.Window(this.wrapper.appendTo(this.element),this.options.window),this.window.bind("close",function(t){t.userTriggered&&(t.sender.element.focus(),i._cancelClick(t))}),this._attachButtonEvents()},_renderTemplate:function(){var t=this.options.template;return"string"==typeof t&&(t=window.unescape(t)),t=kendo.template(t)(this.model)},_renderFields:function(){var t,e,i="";for(t=0;t<this.fields.length;t++)e=this.fields[t],i+='<div class="k-edit-label"><label for="'+e.field+'">'+(e.field||"")+"</label></div>",this._isEditable(e.field)&&(i+="<div "+kendo.attr("container-for")+'="'+e.field+'" class="k-edit-field"></div>');return i},_renderButtons:function(){var t='<div class="k-edit-buttons k-state-default">';return t+=this._createButton("update"),t+=this._createButton("cancel"),t+="</div>"},_createButton:function(t){return kendo.template(fe)(v[t])},_attachButtonEvents:function(){this._cancelClickHandler=N(this._cancelClick,this),this.window.element.on(Tt+bt,"a.k-diagram-cancel",this._cancelClickHandler),this._updateClickHandler=N(this._updateClick,this),this.window.element.on(Tt+bt,"a.k-diagram-update",this._updateClickHandler)},_updateClick:function(t){t.preventDefault(),this.trigger("update")},_cancelClick:function(t){t.preventDefault(),this.trigger("cancel")},open:function(){this.window.center().open()},close:function(){this.window.bind("deactivate",N(this.destroy,this)).close()},destroy:function(){this.window.close().destroy(),this.window.element.off(Tt+bt,"a.k-diagram-cancel",this._cancelClickHandler),this.window.element.off(Tt+bt,"a.k-diagram-update",this._updateClickHandler),this._cancelClickHandler=null,this._editUpdateClickHandler=null,this.window=null,k.fn.destroy.call(this)}}),c.fn=c.prototype={onActivate:function(e){var i=t.Deferred();return this.callbacks.push({callback:e,deferred:i}),i},activate:function(){var t,e,i=this.callbacks;for(e=0;e<i.length;e++)t=this.callbacks[e],t.callback(this.dataItem),t.deferred.resolve();this.callbacks=[]}},d.fn=d.prototype={add:function(t){for(var e=0;e<t.length;e++)this.items[t[e].uid]=new c(t[e])},forEach:function(t){for(var e in this.items)t(this.items[e])},getByUid:function(t){return this.items[t]},remove:function(t){delete this.items[t.uid]}},M=R.extend({init:function(){this.shapes=[]},_add:function(t,e){this.shapes.push({bounds:e,shape:t}),t._quadNode=this},insert:function(t,e){this._add(t,e)},remove:function(t){var e,i=this.shapes,n=i.length;for(e=0;e<n;e++)if(i[e].shape===t){i.splice(e,1);break}},hitTestRect:function(t,e){var i,n=this.shapes,o=n.length;for(i=0;i<o;i++)if(this._testRect(n[i].shape,t)&&!P.inArray(n[i].shape,e))return!0},_testRect:function(t,e){var i,n=t.rotate().angle,o=t.bounds();return i=n?tt.rects(e,o,-n):o.overlaps(e)}}),D=M.extend({init:function(t){M.fn.init.call(this),this.children=[],this.rect=t},inBounds:function(t){var e=this.rect,i=e.bottomRight(),n=t.bottomRight(),o=e.x<=t.x&&e.y<=t.y&&n.x<=i.x&&n.y<=i.y;return o},overlapsBounds:function(t){return this.rect.overlaps(t)},insert:function(t,e){var i,n=!1,o=this.children,s=o.length;if(this.inBounds(e)){if(!s&&this.shapes.length<4)this._add(t,e);else{for(s||this._initChildren(),i=0;i<o.length;i++)if(o[i].insert(t,e)){n=!0;break}n||this._add(t,e)}n=!0}return n},_initChildren:function(){var t,e,i=this.rect,n=this.children,o=this.shapes,s=i.center(),r=i.width/2,a=i.height/2;for(n.push(new D(new X(i.x,i.y,r,a)),new D(new X(s.x,i.y,r,a)),new D(new X(i.x,s.y,r,a)),new D(new X(s.x,s.y,r,a))),e=o.length-1;e>=0;e--)for(t=0;t<n.length;t++)if(n[t].insert(o[e].shape,o[e].bounds)){o.splice(e,1);break}},hitTestRect:function(t,e){var i,n=this.children,o=n.length,s=!1;if(this.overlapsBounds(t))if(M.fn.hitTestRect.call(this,t,e))s=!0;else for(i=0;i<o;i++)if(n[i].hitTestRect(t,e)){s=!0;break}return s}}),I=R.extend({ROOT_SIZE:1e3,init:function(t){var e=N(this._boundsChange,this);t.bind(St,e),t.bind(Vt,e),this.initRoots()},initRoots:function(){this.rootMap={},this.root=new M},clear:function(){this.initRoots()},_boundsChange:function(t){t.item._quadNode&&t.item._quadNode.remove(t.item),this.insert(t.item)},insert:function(t){var e=t.bounds(oe),i=this.ROOT_SIZE,n=this.getSectors(e),o=n[0][0],s=n[1][0];this.inRoot(n)?this.root.insert(t,e):(this.rootMap[o]||(this.rootMap[o]={}),this.rootMap[o][s]||(this.rootMap[o][s]=new D(new X(o*i,s*i,i,i))),this.rootMap[o][s].insert(t,e))},remove:function(t){t._quadNode&&t._quadNode.remove(t)},inRoot:function(t){return t[0].length>1||t[1].length>1},getSectors:function(t){var e,i,n=this.ROOT_SIZE,o=t.bottomRight(),s=xt.floor(o.x/n),r=xt.floor(o.y/n),a=[[],[]];for(e=xt.floor(t.x/n);e<=s;e++)a[0].push(e);for(i=xt.floor(t.y/n);i<=r;i++)a[1].push(i);return a},hitTestRect:function(t,e){var i,n,o,s,r,a=this.getSectors(t);if(this.root.hitTestRect(t,e))return!0;for(i=0;i<a[0].length;i++)for(o=a[0][i],n=0;n<a[1].length;n++)if(s=a[1][n],r=(this.rootMap[o]||{})[s],r&&r.hitTestRect(t,e))return!0;return!1}}),P.ui.plugin(C),O(z,{Shape:x,Connection:b,Connector:w,DiagramToolBar:S,QuadNode:D,QuadRoot:M,ShapesQuadTree:I,PopupEditor:T})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("kendo.dataviz.diagram.min",["kendo.data.min","kendo.draganddrop.min","kendo.userevents.min","kendo.mobile.scroller.min","kendo.drawing.min","dataviz/diagram/utils.min","dataviz/diagram/math.min","dataviz/diagram/svg.min","dataviz/diagram/services.min","dataviz/diagram/layout.min","dataviz/diagram/dom.min"],t)}(function(){return window.kendo},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()});
//# sourceMappingURL=kendo.dataviz.diagram.min.js.map
