/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.mobile.scroller.min",["kendo.fx.min","kendo.draganddrop.min"],e)}(function(){return function(e,i){var n=window.kendo,t=n.mobile,s=n.effects,o=t.ui,l=e.proxy,a=e.extend,r=o.Widget,c=n.Class,h=n.ui.Movable,u=n.ui.Pane,d=n.ui.PaneDimensions,m=s.Transition,f=s.Animation,p=Math.abs,v=500,b=.7,x=.96,y=10,T=55,w=.5,g=5,_="km-scroller-release",E="km-scroller-refresh",C="pull",k="change",S="resize",z="scroll",M=2,A=f.extend({init:function(e){var i=this;f.fn.init.call(i),a(i,e),i.userEvents.bind("gestureend",l(i.start,i)),i.tapCapture.bind("press",l(i.cancel,i))},enabled:function(){return this.movable.scale<this.dimensions.minScale},done:function(){return this.dimensions.minScale-this.movable.scale<.01},tick:function(){var e=this.movable;e.scaleWith(1.1),this.dimensions.rescale(e.scale)},onEnd:function(){var e=this.movable;e.scaleTo(this.dimensions.minScale),this.dimensions.rescale(e.scale)}}),O=f.extend({init:function(e){var i=this;f.fn.init.call(i),a(i,e,{transition:new m({axis:e.axis,movable:e.movable,onEnd:function(){i._end()}})}),i.tapCapture.bind("press",function(){i.cancel()}),i.userEvents.bind("end",l(i.start,i)),i.userEvents.bind("gestureend",l(i.start,i)),i.userEvents.bind("tap",l(i.onEnd,i))},onCancel:function(){this.transition.cancel()},freeze:function(e){var i=this;i.cancel(),i._moveTo(e)},onEnd:function(){var e=this;e.paneAxis.outOfBounds()?e._snapBack():e._end()},done:function(){return p(this.velocity)<1},start:function(e){var i,n=this;n.dimension.enabled&&(n.paneAxis.outOfBounds()?n._snapBack():(i=e.touch.id===M?0:e.touch[n.axis].velocity,n.velocity=Math.max(Math.min(i*n.velocityMultiplier,T),-T),n.tapCapture.captureNext(),f.fn.start.call(n)))},tick:function(){var e=this,i=e.dimension,n=e.paneAxis.outOfBounds()?w:e.friction,t=e.velocity*=n,s=e.movable[e.axis]+t;!e.elastic&&i.outOfBounds(s)&&(s=Math.max(Math.min(s,i.max),i.min),e.velocity=0),e.movable.moveAxis(e.axis,s)},_end:function(){this.tapCapture.cancelCapture(),this.end()},_snapBack:function(){var e=this,i=e.dimension,n=e.movable[e.axis]>i.max?i.max:i.min;e._moveTo(n)},_moveTo:function(e){this.transition.moveTo({location:e,duration:v,ease:m.easeOutExpo})}}),H=f.extend({init:function(e){var i=this;n.effects.Animation.fn.init.call(this),a(i,e,{origin:{},destination:{},offset:{}})},tick:function(){this._updateCoordinates(),this.moveTo(this.origin)},done:function(){return p(this.offset.y)<g&&p(this.offset.x)<g},onEnd:function(){this.moveTo(this.destination),this.callback&&this.callback.call()},setCoordinates:function(e,i){this.offset={},this.origin=e,this.destination=i},setCallback:function(e){e&&n.isFunction(e)?this.callback=e:e=i},_updateCoordinates:function(){this.offset={x:(this.destination.x-this.origin.x)/4,y:(this.destination.y-this.origin.y)/4},this.origin={y:this.origin.y+this.offset.y,x:this.origin.x+this.offset.x}}}),B=c.extend({init:function(i){var n=this,t="x"===i.axis,s=e('<div class="km-touch-scrollbar km-'+(t?"horizontal":"vertical")+'-scrollbar" />');a(n,i,{element:s,elementSize:0,movable:new h(s),scrollMovable:i.movable,alwaysVisible:i.alwaysVisible,size:t?"width":"height"}),n.scrollMovable.bind(k,l(n.refresh,n)),n.container.append(s),i.alwaysVisible&&n.show()},refresh:function(){var e=this,i=e.axis,n=e.dimension,t=n.size,s=e.scrollMovable,o=t/n.total,l=Math.round(-s[i]*o),a=Math.round(t*o);o>=1?this.element.css("display","none"):this.element.css("display",""),l+a>t?a=t-l:l<0&&(a+=l,l=0),e.elementSize!=a&&(e.element.css(e.size,a+"px"),e.elementSize=a),e.movable.moveAxis(i,l)},show:function(){this.element.css({opacity:b,visibility:"visible"})},hide:function(){this.alwaysVisible||this.element.css({opacity:0})}}),R=r.extend({init:function(t,s){var o,c,m,f,v,b,x,y,T,w=this;return r.fn.init.call(w,t,s),t=w.element,(w._native=w.options.useNative&&n.support.hasNativeScrolling)?(t.addClass("km-native-scroller").prepend('<div class="km-scroll-header"/>'),a(w,{scrollElement:t,fixedContainer:t.children().first()}),i):(t.css("overflow","hidden").addClass("km-scroll-wrapper").wrapInner('<div class="km-scroll-container"/>').prepend('<div class="km-scroll-header"/>'),o=t.children().eq(1),c=new n.TapCapture(t),m=new h(o),f=new d({element:o,container:t,forcedEnabled:w.options.zoom}),v=this.options.avoidScrolling,b=new n.UserEvents(t,{touchAction:"pan-y",fastTap:!0,allowSelection:!0,preventDragEvent:!0,captureUpIfMoved:!0,multiTouch:w.options.zoom,supportDoubleTap:w.options.supportDoubleTap,start:function(i){f.refresh();var n=p(i.x.velocity),t=p(i.y.velocity),s=2*n>=t,o=e.contains(w.fixedContainer[0],i.event.target),l=2*t>=n;!o&&!v(i)&&w.enabled&&(f.x.enabled&&s||f.y.enabled&&l)?b.capture():b.cancel()}}),x=new u({movable:m,dimensions:f,userEvents:b,elastic:w.options.elastic}),y=new A({movable:m,dimensions:f,userEvents:b,tapCapture:c}),T=new H({moveTo:function(e){w.scrollTo(e.x,e.y)}}),m.bind(k,function(){w.scrollTop=-m.y,w.scrollLeft=-m.x,w.trigger(z,{scrollTop:w.scrollTop,scrollLeft:w.scrollLeft})}),w.options.mousewheelScrolling&&t.on("DOMMouseScroll mousewheel",l(this,"_wheelScroll")),a(w,{movable:m,dimensions:f,zoomSnapBack:y,animatedScroller:T,userEvents:b,pane:x,tapCapture:c,pulled:!1,enabled:!0,scrollElement:o,scrollTop:0,scrollLeft:0,fixedContainer:t.children().first()}),w._initAxis("x"),w._initAxis("y"),w._wheelEnd=function(){w._wheel=!1,w.userEvents.end(0,w._wheelY)},f.refresh(),w.options.pullToRefresh&&w._initPullToRefresh(),i)},_wheelScroll:function(e){this._wheel||(this._wheel=!0,this._wheelY=0,this.userEvents.press(0,this._wheelY)),clearTimeout(this._wheelTimeout),this._wheelTimeout=setTimeout(this._wheelEnd,50);var i=n.wheelDeltaY(e);i&&(this._wheelY+=i,this.userEvents.move(0,this._wheelY)),e.preventDefault()},makeVirtual:function(){this.dimensions.y.makeVirtual()},virtualSize:function(e,i){this.dimensions.y.virtualSize(e,i)},height:function(){return this.dimensions.y.size},scrollHeight:function(){return this.scrollElement[0].scrollHeight},scrollWidth:function(){return this.scrollElement[0].scrollWidth},options:{name:"Scroller",zoom:!1,pullOffset:140,visibleScrollHints:!1,elastic:!0,useNative:!1,mousewheelScrolling:!0,avoidScrolling:function(){return!1},pullToRefresh:!1,messages:{pullTemplate:"Pull to refresh",releaseTemplate:"Release to refresh",refreshTemplate:"Refreshing"}},events:[C,z,S],_resize:function(){this._native||this.contentResized()},setOptions:function(e){var i=this;r.fn.setOptions.call(i,e),e.pullToRefresh&&i._initPullToRefresh()},reset:function(){this._native?this.scrollElement.scrollTop(0):(this.movable.moveTo({x:0,y:0}),this._scale(1))},contentResized:function(){this.dimensions.refresh(),this.pane.x.outOfBounds()&&this.movable.moveAxis("x",this.dimensions.x.min),this.pane.y.outOfBounds()&&this.movable.moveAxis("y",this.dimensions.y.min)},zoomOut:function(){var e=this.dimensions;e.refresh(),this._scale(e.fitScale),this.movable.moveTo(e.centerCoordinates())},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},scrollTo:function(e,i){this._native?(this.scrollElement.scrollLeft(p(e)),this.scrollElement.scrollTop(p(i))):(this.dimensions.refresh(),this.movable.moveTo({x:e,y:i}))},animatedScrollTo:function(e,i,n){var t,s;this._native?this.scrollTo(e,i):(t={x:this.movable.x,y:this.movable.y},s={x:e,y:i},this.animatedScroller.setCoordinates(t,s),this.animatedScroller.setCallback(n),this.animatedScroller.start())},pullHandled:function(){var e=this;e.refreshHint.removeClass(E),e.hintContainer.html(e.pullTemplate({})),e.yinertia.onEnd(),e.xinertia.onEnd(),e.userEvents.cancel()},destroy:function(){r.fn.destroy.call(this),this.userEvents&&this.userEvents.destroy()},_scale:function(e){this.dimensions.rescale(e),this.movable.scaleTo(e)},_initPullToRefresh:function(){var e=this;e.dimensions.y.forceEnabled(),e.pullTemplate=n.template(e.options.messages.pullTemplate),e.releaseTemplate=n.template(e.options.messages.releaseTemplate),e.refreshTemplate=n.template(e.options.messages.refreshTemplate),e.scrollElement.prepend('<span class="km-scroller-pull"><span class="km-icon"></span><span class="km-loading-left"></span><span class="km-loading-right"></span><span class="km-template">'+e.pullTemplate({})+"</span></span>"),e.refreshHint=e.scrollElement.children().first(),e.hintContainer=e.refreshHint.children(".km-template"),e.pane.y.bind("change",l(e._paneChange,e)),e.userEvents.bind("end",l(e._dragEnd,e))},_dragEnd:function(){var e=this;e.pulled&&(e.pulled=!1,e.refreshHint.removeClass(_).addClass(E),e.hintContainer.html(e.refreshTemplate({})),e.yinertia.freeze(e.options.pullOffset/2),e.trigger("pull"))},_paneChange:function(){var e=this;e.movable.y/w>e.options.pullOffset?e.pulled||(e.pulled=!0,e.refreshHint.removeClass(E).addClass(_),e.hintContainer.html(e.releaseTemplate({}))):e.pulled&&(e.pulled=!1,e.refreshHint.removeClass(_),e.hintContainer.html(e.pullTemplate({})))},_initAxis:function(e){var i=this,n=i.movable,t=i.dimensions[e],s=i.tapCapture,o=i.pane[e],l=new B({axis:e,movable:n,dimension:t,container:i.element,alwaysVisible:i.options.visibleScrollHints});t.bind(k,function(){l.refresh()}),o.bind(k,function(){l.show()}),i[e+"inertia"]=new O({axis:e,paneAxis:o,movable:n,tapCapture:s,userEvents:i.userEvents,dimension:t,elastic:i.options.elastic,friction:i.options.friction||x,velocityMultiplier:i.options.velocityMultiplier||y,end:function(){l.hide(),i.trigger("scrollEnd",{axis:e,scrollTop:i.scrollTop,scrollLeft:i.scrollLeft})}})}});o.plugin(R)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,i,n){(n||i)()});
//# sourceMappingURL=kendo.mobile.scroller.min.js.map
