/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(o,define){define("util/text-metrics.min",["kendo.core.min"],o)}(function(){!function(o){function e(o){return(o+"").replace(i,n)}function r(o){var e,r=[];for(e in o)r.push(e+o[e]);return r.sort().join("")}function l(o){var e,r=2166136261;for(e=0;e<o.length;++e)r+=(r<<1)+(r<<4)+(r<<7)+(r<<8)+(r<<24),r^=o.charCodeAt(e);return r>>>0}function c(){return{width:0,height:0,baseline:0}}function a(o,e,r){return d.current.measure(o,e,r)}var t,i,n,s,f,d;window.kendo.util=window.kendo.util||{},t=kendo.Class.extend({init:function(o){this._size=o,this._length=0,this._map={}},put:function(o,e){var r=this._map,l={key:o,value:e};r[o]=l,this._head?(this._tail.newer=l,l.older=this._tail,this._tail=l):this._head=this._tail=l,this._length>=this._size?(r[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(o){var e=this._map[o];if(e)return e===this._head&&e!==this._tail&&(this._head=e.newer,this._head.older=null),e!==this._tail&&(e.older&&(e.older.newer=e.newer,e.newer.older=e.older),e.older=this._tail,e.newer=null,this._tail.newer=e,this._tail=e),e.value}}),i=/\r?\n|\r|\t/g,n=" ",s={baselineMarkerSize:1},"undefined"!=typeof document&&(f=document.createElement("div"),f.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),d=kendo.Class.extend({init:function(e){this._cache=new t(1e3),this.options=o.extend({},s,e)},measure:function(o,a,t){var i,n,s,d,b,h,u,k,g;if(void 0===t&&(t={}),!o)return c();if(i=r(a),n=l(o+i),s=this._cache.get(n))return s;d=c(),b=t.box||f,h=this._baselineMarker().cloneNode(!1);for(u in a)k=a[u],void 0!==k&&(b.style[u]=k);return g=t.normalizeText!==!1?e(o):o+"",b.textContent=g,b.appendChild(h),document.body.appendChild(b),g.length&&(d.width=b.offsetWidth-this.options.baselineMarkerSize,d.height=b.offsetHeight,d.baseline=h.offsetTop+this.options.baselineMarkerSize),d.width>0&&d.height>0&&this._cache.put(n,d),b.parentNode.removeChild(b),d},_baselineMarker:function(){var o=document.createElement("div");return o.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",o}}),d.current=new d,kendo.deepExtend(kendo.util,{LRUCache:t,TextMetrics:d,measureText:a,objectKey:r,hashKey:l,normalizeText:e})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(o,e,r){(r||e)()}),function(o,define){define("dataviz/themes/chart-base-theme.min",["kendo.dataviz.core.min"],o)}(function(){!function(){var o,e,r,l,c,a,t,i,n,s,f,d,b,h,u,k,g,m,v,p,D,w,C,x,y,B,z,T,j,L,_,A,G,P;window.kendo.dataviz=window.kendo.dataviz||{},o=1.5,e=.4,r="#000",l="Arial, Helvetica, sans-serif",c="11px "+l,a="12px "+l,t="16px "+l,i="transparent",n="#fff",s=function(){return{icon:{border:{width:1}},label:{font:a,padding:3},line:{length:10,width:2},visible:!0}},f=function(){return{labels:{font:a},notes:s(),title:{font:t,margin:5}}},d=function(){return{highlight:{markers:{border:{}}},line:{opacity:1,width:0},markers:{size:6,visible:!1},opacity:.4}},b=function(){return{highlight:{markers:{border:{}}},line:{opacity:1,width:0},markers:{size:6,visible:!1},opacity:.4}},h=function(){return{gap:o,spacing:e}},u=function(){return{outliersField:"",meanField:"",border:{_brightness:.8,width:1},downColor:n,gap:1,highlight:{border:{opacity:1,width:2},whiskers:{width:3},mean:{width:2},median:{width:2}},mean:{width:2},median:{width:2},spacing:.3,whiskers:{width:2}}},k=function(){return{border:{width:0},labels:{background:i},opacity:.6}},g=function(){return{gap:o,spacing:e,target:{color:"#ff0000"}}},m=function(){return{border:{_brightness:.8,width:1},downColor:n,gap:1,highlight:{border:{opacity:1,width:2},line:{width:2}},line:{color:r,width:1},spacing:.3}},v=function(){return{gap:o,spacing:e}},p=function(){return{margin:1}},D=function(){return{width:2}},w=function(){return{gap:1,highlight:{line:{opacity:1,width:3}},line:{width:1},spacing:.3}},C=function(){return{line:{opacity:1,width:0},markers:{size:6,visible:!1},opacity:.5}},x=function(){return{markers:{visible:!1},width:2}},y=function(){return{gap:o,spacing:e}},B=function(){return{gap:o,spacing:e}},z=function(){return{width:1}},T=function(){return{gap:.5,line:{color:r,width:1},spacing:e}},j=function(){return{labels:{background:"",color:"",padding:{top:5,bottom:5,left:7,right:7}}}},L=function(){return{labels:{background:"",color:"",padding:{top:5,bottom:5,left:7,right:7}}}},_=function(o){return{visible:!0,labels:{font:c},overlay:o.gradients?{}:{gradient:"none"},area:d(),rangeArea:b(),verticalRangeArea:b(),bar:h(),boxPlot:u(),bubble:k(),bullet:g(),candlestick:m(),column:v(),pie:j(),donut:p(),funnel:L(),horizontalWaterfall:T(),line:D(),notes:s(),ohlc:w(),radarArea:C(),radarLine:x(),polarArea:C(),polarLine:x(),rangeBar:y(),rangeColumn:B(),scatterLine:z(),verticalArea:d(),verticalBoxPlot:u(),verticalBullet:g(),verticalLine:D(),waterfall:T()}},A=function(){return{font:t}},G=function(){return{labels:{font:a}}},P=function(o){return void 0===o&&(o={}),{axisDefaults:f(),categoryAxis:{majorGridLines:{visible:!0}},navigator:{pane:{height:90,margin:{top:10}}},seriesDefaults:_(o),title:A(),legend:G()}},kendo.deepExtend(kendo.dataviz,{chartBaseTheme:P})}()},"function"==typeof define&&define.amd?define:function(o,e,r){(r||e)()}),function(o,define){define("dataviz/themes/auto-theme.min",["kendo.dataviz.core.min"],o)}(function(){!function(o){function e(e){function l(o,e){a(o,c(e,"backgroundColor"))}function c(o,e){return i.find(".k-var--"+o).css(e)}function a(o,e){for(var r=t,l=o.split("."),c=l.shift();l.length>0;)r=r[c]=r[c]||{},c=l.shift();r[c]=e}var t,i;return!e&&r?r:(t={chart:kendo.dataviz.chartBaseTheme()},i=o('<div style="display: none">  <div class="k-var--accent"></div>  <div class="k-var--accent-contrast"></div>  <div class="k-var--base"></div>  <div class="k-var--background"></div>  <div class="k-var--normal-background"></div>  <div class="k-var--normal-text-color"></div>  <div class="k-var--hover-background"></div>  <div class="k-var--hover-text-color"></div>  <div class="k-var--selected-background"></div>  <div class="k-var--selected-text-color"></div>  <div class="k-var--chart-error-bars-background"></div>  <div class="k-var--chart-notes-background"></div>  <div class="k-var--chart-notes-border"></div>  <div class="k-var--chart-notes-lines"></div>  <div class="k-var--chart-crosshair-background"></div>  <div class="k-var--chart-inactive"></div>  <div class="k-var--chart-major-lines"></div>  <div class="k-var--chart-minor-lines"></div>  <div class="k-var--chart-area-opacity"></div>  <div class="k-widget">      <div class="k-var--chart-font"></div>      <div class="k-var--chart-title-font"></div>      <div class="k-var--chart-label-font"></div>  </div>  <div class="k-var--series">    <div class="k-var--series-a"></div>    <div class="k-var--series-b"></div>    <div class="k-var--series-c"></div>    <div class="k-var--series-d"></div>    <div class="k-var--series-e"></div>    <div class="k-var--series-f"></div>  </div>  <div class="k-var--gauge-pointer"></div>  <div class="k-var--gauge-track"></div></div>').appendTo(document.body),function(){l("chart.axisDefaults.crosshair.color","chart-crosshair-background"),l("chart.axisDefaults.labels.color","normal-text-color"),l("chart.axisDefaults.line.color","chart-major-lines"),l("chart.axisDefaults.majorGridLines.color","chart-major-lines"),l("chart.axisDefaults.minorGridLines.color","chart-minor-lines"),l("chart.axisDefaults.notes.icon.background","chart-notes-background"),l("chart.axisDefaults.notes.icon.border.color","chart-notes-border"),l("chart.axisDefaults.notes.line.color","chart-notes-lines"),l("chart.axisDefaults.title.color","normal-text-color"),l("chart.chartArea.background","background"),l("chart.legend.inactiveItems.labels.color","chart-inactive"),l("chart.legend.inactiveItems.markers.color","chart-inactive"),l("chart.legend.labels.color","normal-text-color"),l("chart.seriesDefaults.boxPlot.downColor","chart-major-lines"),l("chart.seriesDefaults.boxPlot.mean.color","base"),l("chart.seriesDefaults.boxPlot.median.color","base"),l("chart.seriesDefaults.boxPlot.whiskers.color","accent"),l("chart.seriesDefaults.bullet.target.color","accent"),l("chart.seriesDefaults.candlestick.downColor","normal-text-color"),l("chart.seriesDefaults.candlestick.line.color","normal-text-color"),l("chart.seriesDefaults.errorBars.color","chart-error-bars-background"),l("chart.seriesDefaults.horizontalWaterfall.line.color","chart-major-lines"),l("chart.seriesDefaults.icon.border.color","chart-major-lines"),l("chart.seriesDefaults.labels.background","background"),l("chart.seriesDefaults.labels.color","normal-text-color"),l("chart.seriesDefaults.notes.icon.background","chart-notes-background"),l("chart.seriesDefaults.notes.icon.border.color","chart-notes-border"),l("chart.seriesDefaults.notes.line.color","chart-notes-lines"),l("chart.seriesDefaults.verticalBoxPlot.downColor","chart-major-lines"),l("chart.seriesDefaults.verticalBoxPlot.mean.color","base"),l("chart.seriesDefaults.verticalBoxPlot.median.color","base"),l("chart.seriesDefaults.verticalBoxPlot.whiskers.color","accent"),l("chart.seriesDefaults.verticalBullet.target.color","accent"),l("chart.seriesDefaults.waterfall.line.color","chart-major-lines"),l("chart.title.color","normal-text-color"),a("chart.seriesDefaults.labels.opacity",c("chart-area-opacity","opacity")),l("diagram.shapeDefaults.fill.color","accent"),l("diagram.shapeDefaults.content.color","accent-contrast"),l("diagram.shapeDefaults.connectorDefaults.fill.color","normal-text-color"),l("diagram.shapeDefaults.connectorDefaults.stroke.color","accent-contrast"),l("diagram.shapeDefaults.connectorDefaults.hover.fill.color","accent-contrast"),l("diagram.shapeDefaults.connectorDefaults.hover.stroke.color","normal-text-color"),l("diagram.editable.resize.handles.stroke.color","normal-text-color"),l("diagram.editable.resize.handles.fill.color","normal-background"),l("diagram.editable.resize.handles.hover.stroke.color","normal-text-color"),l("diagram.editable.resize.handles.hover.fill.color","normal-text-color"),l("diagram.selectable.stroke.color","normal-text-color"),l("diagram.connectionDefaults.stroke.color","normal-text-color"),l("diagram.connectionDefaults.content.color","normal-text-color"),l("diagram.connectionDefaults.selection.handles.fill.color","accent-contrast"),l("diagram.connectionDefaults.selection.handles.stroke.color","normal-text-color"),l("diagram.connectionDefaults.selection.stroke.color","normal-text-color"),l("gauge.pointer.color","gauge-pointer"),l("gauge.scale.labels.color","normal-text-color"),l("gauge.scale.minorTicks.color","normal-text-color"),l("gauge.scale.majorTicks.color","normal-text-color"),l("gauge.scale.line.color","normal-text-color"),l("gauge.scale.rangePlaceholderColor","gauge-track")}(),function(){function o(o){return c(o,"fontSize")+" "+c(o,"fontFamily")}var e=o("chart-font"),r=o("chart-title-font"),l=o("chart-label-font");a("chart.axisDefaults.labels.font",l),a("chart.axisDefaults.notes.label.font",e),a("chart.axisDefaults.title.font",e),a("chart.legend.labels.font",e),a("chart.seriesDefaults.labels.font",l),a("chart.seriesDefaults.notes.label.font",e),a("chart.title.font",r)}(),function(){function e(o){return o.toLowerCase().charCodeAt(0)-"a".charCodeAt(0)}function r(o){return e(o.match(/series-([a-z])$/)[1])}var l=o(".k-var--series div").toArray(),c=l.reduce(function(e,l){var c=r(l.className);return e[c]=o(l).css("backgroundColor"),e},[]);a("chart.seriesColors",c)}(),i.remove(),r=t,t)}var r;kendo.dataviz.autoTheme=e}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(o,e,r){(r||e)()}),function(o,define){define("dataviz/themes/themes.min",["dataviz/themes/chart-base-theme.min"],o)}(function(){return function(o){function e(e,r){return o.map(e,function(o,e){return[[o,r[e]]]})}var r=window.kendo,l=r.dataviz.ui,c=r.deepExtend,a="#000",t="Arial,Helvetica,sans-serif",i="12px "+t,n="#fff",s=r.dataviz.chartBaseTheme({gradients:!0}),f={scale:{labels:{font:i}}},d={shapeDefaults:{hover:{opacity:.2},stroke:{width:0}},editable:{resize:{handles:{width:7,height:7}}},selectable:{stroke:{width:1,dashType:"dot"}},connectionDefaults:{stroke:{width:2},selection:{handles:{width:8,height:8}},editable:{tools:["edit","delete"]}}},b=l.themes,h=l.registerTheme=function(o,e){var r,l={};l.chart=c({},s,e.chart),l.gauge=c({},f,e.gauge),l.diagram=c({},d,e.diagram),l.treeMap=c({},e.treeMap),r=l.chart.seriesDefaults,r.verticalLine=c({},r.line),r.verticalArea=c({},r.area),r.rangeArea=c({},r.area),r.verticalRangeArea=c({},r.rangeArea),r.verticalBoxPlot=c({},r.boxPlot),r.polarArea=c({},r.radarArea),r.polarLine=c({},r.radarLine),b[o]=l};h("black",{chart:{title:{color:n},legend:{labels:{color:n},inactiveItems:{labels:{color:"#919191"},markers:{color:"#919191"}}},seriesDefaults:{labels:{color:n},errorBars:{color:n},notes:{icon:{background:"#3b3b3b",border:{color:"#8e8e8e"}},label:{color:n},line:{color:"#8e8e8e"}},pie:{overlay:{gradient:"sharpBevel"}},donut:{overlay:{gradient:"sharpGlass"}},line:{markers:{background:"#3d3d3d"}},scatter:{markers:{background:"#3d3d3d"}},scatterLine:{markers:{background:"#3d3d3d"}},waterfall:{line:{color:"#8e8e8e"}},horizontalWaterfall:{line:{color:"#8e8e8e"}},candlestick:{downColor:"#555",line:{color:n},border:{_brightness:1.5,opacity:1},highlight:{border:{color:n,opacity:.2}}},ohlc:{line:{color:n}}},chartArea:{background:"#3d3d3d"},seriesColors:["#0081da","#3aafff","#99c900","#ffeb3d","#b20753","#ff4195"],axisDefaults:{line:{color:"#8e8e8e"},labels:{color:n},majorGridLines:{color:"#545454"},minorGridLines:{color:"#454545"},title:{color:n},crosshair:{color:"#8e8e8e"},notes:{icon:{background:"#3b3b3b",border:{color:"#8e8e8e"}},label:{color:n},line:{color:"#8e8e8e"}}}},gauge:{pointer:{color:"#0070e4"},scale:{rangePlaceholderColor:"#1d1d1d",labels:{color:n},minorTicks:{color:n},majorTicks:{color:n},line:{color:n}}},diagram:{shapeDefaults:{fill:{color:"#0066cc"},connectorDefaults:{fill:{color:n},stroke:{color:"#384049"},hover:{fill:{color:"#3d3d3d"},stroke:{color:"#efefef"}}},content:{color:n}},editable:{resize:{handles:{fill:{color:"#3d3d3d"},stroke:{color:n},hover:{fill:{color:n},stroke:{color:n}}}},rotate:{thumb:{stroke:{color:n},fill:{color:n}}}},selectable:{stroke:{color:n}},connectionDefaults:{stroke:{color:n},content:{color:n},selection:{handles:{fill:{color:"#3d3d3d"},stroke:{color:"#efefef"}}}}},treeMap:{colors:[["#0081da","#314b5c"],["#3aafff","#3c5464"],["#99c900","#4f5931"],["#ffeb3d","#64603d"],["#b20753","#543241"],["#ff4195","#643e4f"]]}}),h("blueopal",{chart:{title:{color:"#293135"},legend:{labels:{color:"#293135"},inactiveItems:{labels:{color:"#27A5BA"},markers:{color:"#27A5BA"}}},seriesDefaults:{labels:{color:a,background:n,opacity:.5},errorBars:{color:"#293135"},candlestick:{downColor:"#c4d0d5",line:{color:"#9aabb2"}},waterfall:{line:{color:"#9aabb2"}},horizontalWaterfall:{line:{color:"#9aabb2"}},notes:{icon:{background:"transparent",border:{color:"#9aabb2"}},label:{color:"#293135"},line:{color:"#9aabb2"}}},seriesColors:["#0069a5","#0098ee","#7bd2f6","#ffb800","#ff8517","#e34a00"],axisDefaults:{line:{color:"#9aabb2"},labels:{color:"#293135"},majorGridLines:{color:"#c4d0d5"},minorGridLines:{color:"#edf1f2"},title:{color:"#293135"},crosshair:{color:"#9aabb2"},notes:{icon:{background:"transparent",border:{color:"#9aabb2"}},label:{color:"#293135"},line:{color:"#9aabb2"}}}},gauge:{pointer:{color:"#005c83"},scale:{rangePlaceholderColor:"#daecf4",labels:{color:"#293135"},minorTicks:{color:"#293135"},majorTicks:{color:"#293135"},line:{color:"#293135"}}},diagram:{shapeDefaults:{fill:{color:"#7ec6e3"},connectorDefaults:{fill:{color:"#003f59"},stroke:{color:n},hover:{fill:{color:n},stroke:{color:"#003f59"}}},content:{color:"#293135"}},editable:{resize:{handles:{fill:{color:n},stroke:{color:"#003f59"},hover:{fill:{color:"#003f59"},stroke:{color:"#003f59"}}}},rotate:{thumb:{stroke:{color:"#003f59"},fill:{color:"#003f59"}}}},selectable:{stroke:{color:"#003f59"}},connectionDefaults:{stroke:{color:"#003f59"},content:{color:"#293135"},selection:{handles:{fill:{color:"#3d3d3d"},stroke:{color:"#efefef"}}}}},treeMap:{colors:[["#0069a5","#bad7e7"],["#0098ee","#b9e0f5"],["#7bd2f6","#ceeaf6"],["#ffb800","#e6e3c4"],["#ff8517","#e4d8c8"],["#e34a00","#ddccc2"]]}}),h("highcontrast",{chart:{title:{color:"#ffffff"},legend:{labels:{color:"#ffffff"},inactiveItems:{labels:{color:"#66465B"},markers:{color:"#66465B"}}},seriesDefaults:{labels:{color:"#ffffff"},errorBars:{color:"#ffffff"},notes:{icon:{background:"transparent",border:{color:"#ffffff"}},label:{color:"#ffffff"},line:{color:"#ffffff"}},pie:{overlay:{gradient:"sharpGlass"}},donut:{overlay:{gradient:"sharpGlass"}},line:{markers:{background:"#2c232b"}},scatter:{markers:{background:"#2c232b"}},scatterLine:{markers:{background:"#2c232b"}},area:{opacity:.5},waterfall:{line:{color:"#ffffff"}},horizontalWaterfall:{line:{color:"#ffffff"}},candlestick:{downColor:"#664e62",line:{color:"#ffffff"},border:{_brightness:1.5,opacity:1},highlight:{border:{color:"#ffffff",opacity:1}}},ohlc:{line:{color:"#ffffff"}}},chartArea:{background:"#2c232b"},seriesColors:["#a7008f","#ffb800","#3aafff","#99c900","#b20753","#ff4195"],axisDefaults:{line:{color:"#ffffff"},labels:{color:"#ffffff"},majorGridLines:{color:"#664e62"},minorGridLines:{color:"#4f394b"},title:{color:"#ffffff"},crosshair:{color:"#ffffff"},notes:{icon:{background:"transparent",border:{color:"#ffffff"}},label:{color:"#ffffff"},line:{color:"#ffffff"}}}},gauge:{pointer:{color:"#a7008f"},scale:{rangePlaceholderColor:"#2c232b",labels:{color:"#ffffff"},minorTicks:{color:"#2c232b"},majorTicks:{color:"#664e62"},line:{color:"#ffffff"}}},diagram:{shapeDefaults:{fill:{color:"#a7018f"},connectorDefaults:{fill:{color:n},stroke:{color:"#2c232b"},hover:{fill:{color:"#2c232b"},stroke:{color:n}}},content:{color:n}},editable:{resize:{handles:{fill:{color:"#2c232b"},stroke:{color:n},hover:{fill:{color:n},stroke:{color:n}}}},rotate:{thumb:{stroke:{color:n},fill:{color:n}}}},selectable:{stroke:{color:n}},connectionDefaults:{stroke:{color:n},content:{color:n},selection:{handles:{fill:{color:"#2c232b"},stroke:{color:n}}}}},treeMap:{colors:[["#a7008f","#451c3f"],["#ffb800","#564122"],["#3aafff","#2f3f55"],["#99c900","#424422"],["#b20753","#471d33"],["#ff4195","#562940"]]}}),h("default",{chart:{title:{color:"#8e8e8e"},legend:{labels:{color:"#232323"},inactiveItems:{labels:{color:"#919191"},markers:{color:"#919191"}}},seriesDefaults:{labels:{color:a,background:n,opacity:.5},errorBars:{color:"#232323"},candlestick:{downColor:"#dedede",line:{color:"#8d8d8d"}},waterfall:{line:{color:"#8e8e8e"}},horizontalWaterfall:{line:{color:"#8e8e8e"}},notes:{icon:{background:"transparent",border:{color:"#8e8e8e"}},label:{color:"#232323"},line:{color:"#8e8e8e"}}},seriesColors:["#ff6800","#a0a700","#ff8d00","#678900","#ffb53c","#396000"],axisDefaults:{line:{color:"#8e8e8e"},labels:{color:"#232323"},minorGridLines:{color:"#f0f0f0"},majorGridLines:{color:"#dfdfdf"},title:{color:"#232323"},crosshair:{color:"#8e8e8e"},notes:{icon:{background:"transparent",border:{color:"#8e8e8e"}},label:{color:"#232323"},line:{color:"#8e8e8e"}}}},gauge:{pointer:{color:"#ea7001"},scale:{rangePlaceholderColor:"#dedede",labels:{color:"#2e2e2e"},minorTicks:{color:"#2e2e2e"},majorTicks:{color:"#2e2e2e"},line:{color:"#2e2e2e"}}},diagram:{shapeDefaults:{fill:{color:"#e15613"},connectorDefaults:{fill:{color:"#282828"},stroke:{color:n},hover:{fill:{color:n},stroke:{color:"#282828"}}},content:{color:"#2e2e2e"}},editable:{resize:{handles:{fill:{color:n},stroke:{color:"#282828"},hover:{fill:{color:"#282828"},stroke:{color:"#282828"}}}},rotate:{thumb:{stroke:{color:"#282828"},fill:{color:"#282828"}}}},selectable:{stroke:{color:"#a7018f"}},connectionDefaults:{stroke:{color:"#282828"},content:{color:"#2e2e2e"},selection:{handles:{fill:{color:n},stroke:{color:"#282828"}}}}},treeMap:{colors:[["#ff6800","#edcfba"],["#a0a700","#dadcba"],["#ff8d00","#edd7ba"],["#678900","#cfd6ba"],["#ffb53c","#eddfc6"],["#396000","#c6ceba"]]}}),h("silver",{chart:{title:{color:"#4e5968"},legend:{labels:{color:"#4e5968"},inactiveItems:{labels:{color:"#B1BCC8"},markers:{color:"#B1BCC8"}}},seriesDefaults:{labels:{color:"#293135",background:"#eaeaec",opacity:.5},errorBars:{color:"#4e5968"},notes:{icon:{background:"transparent",border:{color:"#4e5968"}},label:{color:"#4e5968"},line:{color:"#4e5968"}},line:{markers:{background:"#eaeaec"}},scatter:{markers:{background:"#eaeaec"}},scatterLine:{markers:{background:"#eaeaec"}},pie:{connectors:{color:"#A6B1C0"}},donut:{connectors:{color:"#A6B1C0"}},waterfall:{line:{color:"#a6b1c0"}},horizontalWaterfall:{line:{color:"#a6b1c0"}},candlestick:{downColor:"#a6afbe"}},chartArea:{background:"#eaeaec"},seriesColors:["#007bc3","#76b800","#ffae00","#ef4c00","#a419b7","#430B62"],axisDefaults:{line:{color:"#a6b1c0"},labels:{color:"#4e5968"},majorGridLines:{color:"#dcdcdf"},minorGridLines:{color:"#eeeeef"},title:{color:"#4e5968"},crosshair:{color:"#a6b1c0"},notes:{icon:{background:"transparent",border:{color:"#4e5968"}},label:{color:"#4e5968"},line:{color:"#4e5968"}}}},gauge:{pointer:{color:"#0879c0"},scale:{rangePlaceholderColor:"#f3f3f4",labels:{color:"#515967"},minorTicks:{color:"#515967"},majorTicks:{color:"#515967"},line:{color:"#515967"}}},diagram:{shapeDefaults:{fill:{color:"#1c82c2"},connectorDefaults:{fill:{color:"#515967"},stroke:{color:n},hover:{fill:{color:n},stroke:{color:"#282828"}}},content:{color:"#515967"}},editable:{resize:{handles:{fill:{color:n},stroke:{color:"#515967"},hover:{fill:{color:"#515967"},stroke:{color:"#515967"}}}},rotate:{thumb:{stroke:{color:"#515967"},fill:{color:"#515967"}}}},selectable:{stroke:{color:"#515967"}},connectionDefaults:{stroke:{color:"#515967"},content:{color:"#515967"},selection:{handles:{fill:{color:n},stroke:{color:"#515967"}}}}},treeMap:{colors:[["#007bc3","#c2dbea"],["#76b800","#dae7c3"],["#ffae00","#f5e5c3"],["#ef4c00","#f2d2c3"],["#a419b7","#e3c7e8"],["#430b62","#d0c5d7"]]}}),h("metro",{chart:{title:{color:"#777777"},legend:{labels:{color:"#777777"},inactiveItems:{labels:{color:"#CBCBCB"},markers:{color:"#CBCBCB"}}},seriesDefaults:{labels:{color:a},errorBars:{color:"#777777"},notes:{icon:{background:"transparent",border:{color:"#777777"}},label:{color:"#777777"},line:{color:"#777777"}},candlestick:{downColor:"#c7c7c7",line:{color:"#787878"}},waterfall:{line:{color:"#c7c7c7"}},horizontalWaterfall:{line:{color:"#c7c7c7"}},overlay:{gradient:"none"},border:{_brightness:1}},seriesColors:["#8ebc00","#309b46","#25a0da","#ff6900","#e61e26","#d8e404","#16aba9","#7e51a1","#313131","#ed1691"],axisDefaults:{line:{color:"#c7c7c7"},labels:{color:"#777777"},minorGridLines:{color:"#c7c7c7"},majorGridLines:{color:"#c7c7c7"},title:{color:"#777777"},crosshair:{color:"#c7c7c7"},notes:{icon:{background:"transparent",border:{color:"#777777"}},label:{color:"#777777"},line:{color:"#777777"}}}},gauge:{pointer:{color:"#8ebc00"},scale:{rangePlaceholderColor:"#e6e6e6",labels:{color:"#777"},minorTicks:{color:"#777"},majorTicks:{color:"#777"},line:{color:"#777"}}},diagram:{shapeDefaults:{fill:{color:"#8ebc00"},connectorDefaults:{fill:{color:a},stroke:{color:n},hover:{fill:{color:n},stroke:{color:a}}},content:{color:"#777"}},editable:{resize:{handles:{fill:{color:n},stroke:{color:"#787878"},hover:{fill:{color:"#787878"},stroke:{color:"#787878"}}}},rotate:{thumb:{stroke:{color:"#787878"},fill:{color:"#787878"}}}},selectable:{stroke:{color:"#515967"}},connectionDefaults:{stroke:{color:"#787878"},content:{color:"#777"},selection:{handles:{fill:{color:n},stroke:{color:"#787878"}}}}},treeMap:{colors:[["#8ebc00","#e8f2cc"],["#309b46","#d6ebda"],["#25a0da","#d3ecf8"],["#ff6900","#ffe1cc"],["#e61e26","#fad2d4"],["#d8e404","#f7facd"],["#16aba9","#d0eeee"],["#7e51a1","#e5dcec"],["#313131","#d6d6d6"],["#ed1691","#fbd0e9"]]}}),h("metroblack",{chart:{title:{color:"#ffffff"},legend:{labels:{color:"#ffffff"},inactiveItems:{labels:{color:"#797979"},markers:{color:"#797979"}}},seriesDefaults:{border:{_brightness:1},labels:{color:"#ffffff"},errorBars:{color:"#ffffff"},notes:{icon:{background:"transparent",border:{color:"#cecece"}},label:{color:"#ffffff"},line:{color:"#cecece"}},line:{markers:{background:"#0e0e0e"}},bubble:{opacity:.6},scatter:{markers:{background:"#0e0e0e"}},scatterLine:{markers:{background:"#0e0e0e"}},candlestick:{downColor:"#828282",line:{color:"#ffffff"}},waterfall:{line:{color:"#cecece"}},horizontalWaterfall:{line:{color:"#cecece"}},overlay:{gradient:"none"}},chartArea:{background:"#0e0e0e"},seriesColors:["#00aba9","#309b46","#8ebc00","#ff6900","#e61e26","#d8e404","#25a0da","#7e51a1","#313131","#ed1691"],axisDefaults:{line:{color:"#cecece"},labels:{color:"#ffffff"},minorGridLines:{color:"#2d2d2d"},majorGridLines:{color:"#333333"},title:{color:"#ffffff"},crosshair:{color:"#cecece"},notes:{icon:{background:"transparent",border:{color:"#cecece"}},label:{color:"#ffffff"},line:{color:"#cecece"}}}},gauge:{pointer:{color:"#00aba9"},scale:{rangePlaceholderColor:"#2d2d2d",labels:{color:"#ffffff"},minorTicks:{color:"#333333"},majorTicks:{color:"#cecece"},line:{color:"#cecece"}}},diagram:{shapeDefaults:{fill:{color:"#00aba9"},connectorDefaults:{fill:{color:n},stroke:{color:"#0e0e0e"},hover:{fill:{color:"#0e0e0e"},stroke:{color:n}}},content:{color:n}},editable:{resize:{handles:{fill:{color:"#0e0e0e"},stroke:{color:"#787878"},hover:{fill:{color:"#787878"},stroke:{color:"#787878"}}}},rotate:{thumb:{stroke:{color:n},fill:{color:n}}}},selectable:{stroke:{color:"#787878"}},connectionDefaults:{stroke:{color:n},content:{color:n},selection:{handles:{fill:{color:"#0e0e0e"},stroke:{color:n}}}}},treeMap:{colors:[["#00aba9","#0b2d2d"],["#309b46","#152a19"],["#8ebc00","#28310b"],["#ff6900","#3e200b"],["#e61e26","#391113"],["#d8e404","#36390c"],["#25a0da","#132b37"],["#7e51a1","#241b2b"],["#313131","#151515"],["#ed1691","#3b1028"]]}}),h("moonlight",{chart:{title:{color:"#ffffff"},legend:{labels:{color:"#ffffff"},inactiveItems:{labels:{color:"#A1A7AB"},markers:{color:"#A1A7AB"}}},seriesDefaults:{labels:{color:"#ffffff"},errorBars:{color:"#ffffff"},notes:{icon:{background:"transparent",border:{color:"#8c909e"}},label:{color:"#ffffff"},line:{color:"#8c909e"}},pie:{overlay:{gradient:"sharpBevel"}},donut:{overlay:{gradient:"sharpGlass"}},line:{markers:{background:"#212a33"}},bubble:{opacity:.6},scatter:{markers:{background:"#212a33"}},scatterLine:{markers:{background:"#212a33"}},area:{opacity:.3},candlestick:{downColor:"#757d87",line:{color:"#ea9d06"},border:{_brightness:1.5,opacity:1},highlight:{border:{color:n,opacity:.2}}},waterfall:{line:{color:"#8c909e"}},horizontalWaterfall:{line:{color:"#8c909e"}},ohlc:{line:{color:"#ea9d06"}}},chartArea:{background:"#212a33"},seriesColors:["#ffca08","#ff710f","#ed2e24","#ff9f03","#e13c02","#a00201"],axisDefaults:{line:{color:"#8c909e"},minorTicks:{color:"#8c909e"},majorTicks:{color:"#8c909e"},labels:{color:"#ffffff"},majorGridLines:{color:"#3e424d"},minorGridLines:{color:"#2f3640"},title:{color:"#ffffff"},crosshair:{color:"#8c909e"},notes:{icon:{background:"transparent",border:{color:"#8c909e"}},label:{color:"#ffffff"},line:{color:"#8c909e"}}}},gauge:{pointer:{color:"#f4af03"},scale:{rangePlaceholderColor:"#2f3640",labels:{color:n},minorTicks:{color:"#8c909e"},majorTicks:{color:"#8c909e"},line:{color:"#8c909e"}}},diagram:{shapeDefaults:{fill:{color:"#f3ae03"},connectorDefaults:{fill:{color:n},stroke:{color:"#414550"},hover:{fill:{color:"#414550"},stroke:{color:n}}},content:{color:n}},editable:{resize:{handles:{fill:{color:"#414550"},stroke:{color:n},hover:{fill:{color:n},stroke:{color:n}}}},rotate:{thumb:{stroke:{color:n},fill:{color:n}}}},selectable:{stroke:{color:n}},connectionDefaults:{stroke:{color:n},content:{color:n},selection:{handles:{fill:{color:"#414550"},stroke:{color:n}}}}},treeMap:{colors:[["#ffca08","#4e4b2b"],["#ff710f","#4e392d"],["#ed2e24","#4b2c31"],["#ff9f03","#4e422a"],["#e13c02","#482e2a"],["#a00201","#3b232a"]]}}),h("uniform",{chart:{title:{color:"#686868"},legend:{labels:{color:"#686868"},inactiveItems:{labels:{color:"#B6B6B6"},markers:{color:"#B6B6B6"}}},seriesDefaults:{labels:{color:"#686868"},errorBars:{color:"#686868"},notes:{icon:{background:"transparent",border:{color:"#9e9e9e"}},label:{color:"#686868"},line:{color:"#9e9e9e"}},pie:{overlay:{gradient:"sharpBevel"}},donut:{overlay:{gradient:"sharpGlass"}},line:{markers:{background:"#ffffff"}},bubble:{opacity:.6},scatter:{markers:{background:"#ffffff"}},scatterLine:{markers:{background:"#ffffff"}},area:{opacity:.3},candlestick:{downColor:"#cccccc",line:{color:"#cccccc"},border:{_brightness:1.5,opacity:1},highlight:{border:{color:"#cccccc",opacity:.2}}},waterfall:{line:{color:"#9e9e9e"}},horizontalWaterfall:{line:{color:"#9e9e9e"}},ohlc:{line:{color:"#cccccc"}}},chartArea:{background:"#ffffff"},seriesColors:["#527aa3","#6f91b3","#8ca7c2","#a8bdd1","#c5d3e0","#e2e9f0"],axisDefaults:{line:{color:"#9e9e9e"},minorTicks:{color:"#aaaaaa"},majorTicks:{color:"#888888"},labels:{color:"#686868"},majorGridLines:{color:"#dadada"},minorGridLines:{color:"#e7e7e7"},title:{color:"#686868"},crosshair:{color:"#9e9e9e"},notes:{icon:{background:"transparent",border:{color:"#9e9e9e"}},label:{color:"#686868"},line:{color:"#9e9e9e"}}}},gauge:{pointer:{color:"#527aa3"},scale:{rangePlaceholderColor:"#e7e7e7",labels:{color:"#686868"},minorTicks:{color:"#aaaaaa"},majorTicks:{color:"#888888"},line:{color:"#9e9e9e"}}},diagram:{shapeDefaults:{fill:{color:"#d1d1d1"},connectorDefaults:{fill:{color:"#686868"},stroke:{color:n},hover:{fill:{color:n},stroke:{color:"#686868"}}},content:{color:"#686868"}},editable:{resize:{handles:{fill:{color:n},stroke:{color:"#686868"},hover:{fill:{color:"#686868"},stroke:{color:"#686868"}}}},rotate:{thumb:{stroke:{color:"#686868"},fill:{color:"#686868"}}}},selectable:{stroke:{color:"#686868"}},connectionDefaults:{stroke:{color:"#686868"},content:{color:"#686868"},selection:{handles:{fill:{color:n},stroke:{color:"#686868"}}}}},treeMap:{colors:[["#527aa3","#d0d8e1"],["#6f91b3","#d6dde4"],["#8ca7c2","#dce1e7"],["#a8bdd1","#e2e6ea"],["#c5d3e0","#e7eaed"],["#e2e9f0","#edeff0"]]}}),h("bootstrap",{chart:{title:{color:"#333333"},legend:{labels:{color:"#333333"},inactiveItems:{labels:{color:"#999999"},markers:{color:"#9A9A9A"}}},seriesDefaults:{labels:{color:"#333333"},overlay:{gradient:"none"},errorBars:{color:"#343434"},notes:{icon:{background:"#000000",border:{color:"#000000"}},label:{color:"#333333"},line:{color:"#000000"}},pie:{overlay:{gradient:"none"}},donut:{overlay:{gradient:"none"}},line:{markers:{background:"#ffffff"}},bubble:{opacity:.6},scatter:{markers:{background:"#ffffff"}},scatterLine:{markers:{background:"#ffffff"}},area:{opacity:.8},candlestick:{downColor:"#d0d0d0",line:{color:"#333333"},border:{_brightness:1.5,opacity:1},highlight:{border:{color:"#b8b8b8",opacity:.2}}},waterfall:{line:{color:"#cccccc"}},horizontalWaterfall:{line:{color:"#cccccc"}},ohlc:{line:{color:"#333333"}}},chartArea:{background:"#ffffff"},seriesColors:["#428bca","#5bc0de","#5cb85c","#f2b661","#e67d4a","#da3b36"],axisDefaults:{line:{color:"#cccccc"},minorTicks:{color:"#ebebeb"},majorTicks:{color:"#cccccc"},labels:{color:"#333333"},majorGridLines:{color:"#cccccc"},minorGridLines:{color:"#ebebeb"},title:{color:"#333333"},crosshair:{color:"#000000"},notes:{icon:{background:"#000000",border:{color:"#000000"}},label:{color:"#ffffff"},line:{color:"#000000"}}}},gauge:{pointer:{color:"#428bca"},scale:{rangePlaceholderColor:"#cccccc",labels:{color:"#333333"},minorTicks:{color:"#ebebeb"},majorTicks:{color:"#cccccc"},line:{color:"#cccccc"}}},diagram:{shapeDefaults:{fill:{color:"#428bca"},connectorDefaults:{fill:{color:"#333333"},stroke:{color:n},hover:{fill:{color:n},stroke:{color:"#333333"}}},content:{color:"#333333"}},editable:{resize:{handles:{
fill:{color:n},stroke:{color:"#333333"},hover:{fill:{color:"#333333"},stroke:{color:"#333333"}}}},rotate:{thumb:{stroke:{color:"#333333"},fill:{color:"#333333"}}}},selectable:{stroke:{color:"#333333"}},connectionDefaults:{stroke:{color:"#c4c4c4"},content:{color:"#333333"},selection:{handles:{fill:{color:n},stroke:{color:"#333333"}},stroke:{color:"#333333"}}}},treeMap:{colors:[["#428bca","#d1e0ec"],["#5bc0de","#d6eaf0"],["#5cb85c","#d6e9d6"],["#5cb85c","#f4e8d7"],["#e67d4a","#f2ddd3"],["#da3b36","#f0d0cf"]]}}),h("flat",{chart:{title:{color:"#4c5356"},legend:{labels:{color:"#4c5356"},inactiveItems:{labels:{color:"#CBCBCB"},markers:{color:"#CBCBCB"}}},seriesDefaults:{labels:{color:"#4c5356"},errorBars:{color:"#4c5356"},notes:{icon:{background:"transparent",border:{color:"#cdcdcd"}},label:{color:"#4c5356"},line:{color:"#cdcdcd"}},candlestick:{downColor:"#c7c7c7",line:{color:"#787878"}},area:{opacity:.9},waterfall:{line:{color:"#cdcdcd"}},horizontalWaterfall:{line:{color:"#cdcdcd"}},overlay:{gradient:"none"},border:{_brightness:1}},seriesColors:["#10c4b2","#ff7663","#ffb74f","#a2df53","#1c9ec4","#ff63a5","#1cc47b"],axisDefaults:{line:{color:"#cdcdcd"},labels:{color:"#4c5356"},minorGridLines:{color:"#cdcdcd"},majorGridLines:{color:"#cdcdcd"},title:{color:"#4c5356"},crosshair:{color:"#cdcdcd"},notes:{icon:{background:"transparent",border:{color:"#cdcdcd"}},label:{color:"#4c5356"},line:{color:"#cdcdcd"}}}},gauge:{pointer:{color:"#10c4b2"},scale:{rangePlaceholderColor:"#cdcdcd",labels:{color:"#4c5356"},minorTicks:{color:"#4c5356"},majorTicks:{color:"#4c5356"},line:{color:"#4c5356"}}},diagram:{shapeDefaults:{fill:{color:"#10c4b2"},connectorDefaults:{fill:{color:"#363940"},stroke:{color:n},hover:{fill:{color:n},stroke:{color:"#363940"}}},content:{color:"#4c5356"}},editable:{resize:{handles:{fill:{color:n},stroke:{color:"#363940"},hover:{fill:{color:"#363940"},stroke:{color:"#363940"}}}},rotate:{thumb:{stroke:{color:"#363940"},fill:{color:"#363940"}}}},selectable:{stroke:{color:"#363940"}},connectionDefaults:{stroke:{color:"#cdcdcd"},content:{color:"#4c5356"},selection:{handles:{fill:{color:n},stroke:{color:"#363940"}},stroke:{color:"#363940"}}}},treeMap:{colors:[["#10c4b2","#cff3f0"],["#ff7663","#ffe4e0"],["#ffb74f","#fff1dc"],["#a2df53","#ecf9dd"],["#1c9ec4","#d2ecf3"],["#ff63a5","#ffe0ed"],["#1cc47b","#d2f3e5"]]}}),h("material",{chart:{title:{color:"#444444"},legend:{labels:{color:"#444444"},inactiveItems:{labels:{color:"#CBCBCB"},markers:{color:"#CBCBCB"}}},seriesDefaults:{labels:{color:"#444444"},errorBars:{color:"#444444"},notes:{icon:{background:"transparent",border:{color:"#e5e5e5"}},label:{color:"#444444"},line:{color:"#e5e5e5"}},candlestick:{downColor:"#c7c7c7",line:{color:"#787878"}},area:{opacity:.9},waterfall:{line:{color:"#e5e5e5"}},horizontalWaterfall:{line:{color:"#e5e5e5"}},overlay:{gradient:"none"},border:{_brightness:1}},seriesColors:["#3f51b5","#03a9f4","#4caf50","#f9ce1d","#ff9800","#ff5722"],axisDefaults:{line:{color:"#e5e5e5"},labels:{color:"#444444"},minorGridLines:{color:"#e5e5e5"},majorGridLines:{color:"#e5e5e5"},title:{color:"#444444"},crosshair:{color:"#7f7f7f"},notes:{icon:{background:"transparent",border:{color:"#e5e5e5"}},label:{color:"#444444"},line:{color:"#e5e5e5"}}}},gauge:{pointer:{color:"#3f51b5"},scale:{rangePlaceholderColor:"#e5e5e5",labels:{color:"#444444"},minorTicks:{color:"#444444"},majorTicks:{color:"#444444"},line:{color:"#444444"}}},diagram:{shapeDefaults:{fill:{color:"#3f51b5"},connectorDefaults:{fill:{color:"#7f7f7f"},stroke:{color:n},hover:{fill:{color:n},stroke:{color:"#7f7f7f"}}},content:{color:"#444444"}},editable:{resize:{handles:{fill:{color:n},stroke:{color:"#444444"},hover:{fill:{color:"#444444"},stroke:{color:"#444444"}}}},rotate:{thumb:{stroke:{color:"#444444"},fill:{color:"#444444"}}}},selectable:{stroke:{color:"#444444"}},connectionDefaults:{stroke:{color:"#7f7f7f"},content:{color:"#444444"},selection:{handles:{fill:{color:n},stroke:{color:"#444444"}},stroke:{color:"#444444"}}}},treeMap:{colors:[["#3f51b5","#cff3f0"],["#03a9f4","#e5f6fe"],["#4caf50","#edf7ed"],["#f9ce1d","#fefae8"],["#ff9800","#fff4e5"],["#ff5722","#ffeee8"]]}}),h("materialblack",{chart:{title:{color:"#fff"},legend:{labels:{color:"#fff"},inactiveItems:{labels:{color:"#CBCBCB"},markers:{color:"#CBCBCB"}}},seriesDefaults:{labels:{color:"#fff"},errorBars:{color:"#fff"},notes:{icon:{background:"transparent",border:{color:"#e5e5e5"}},label:{color:"#fff"},line:{color:"#e5e5e5"}},candlestick:{downColor:"#c7c7c7",line:{color:"#787878"}},area:{opacity:.9},waterfall:{line:{color:"#4d4d4d"}},horizontalWaterfall:{line:{color:"#4d4d4d"}},overlay:{gradient:"none"},border:{_brightness:1}},chartArea:{background:"#1c1c1c"},seriesColors:["#3f51b5","#03a9f4","#4caf50","#f9ce1d","#ff9800","#ff5722"],axisDefaults:{line:{color:"#4d4d4d"},labels:{color:"#fff"},minorGridLines:{color:"#4d4d4d"},majorGridLines:{color:"#4d4d4d"},title:{color:"#fff"},crosshair:{color:"#7f7f7f"},notes:{icon:{background:"transparent",border:{color:"#4d4d4d"}},label:{color:"#fff"},line:{color:"#4d4d4d"}}}},gauge:{pointer:{color:"#3f51b5"},scale:{rangePlaceholderColor:"#4d4d4d",labels:{color:"#fff"},minorTicks:{color:"#fff"},majorTicks:{color:"#fff"},line:{color:"#fff"}}},diagram:{shapeDefaults:{fill:{color:"#3f51b5"},connectorDefaults:{fill:{color:"#7f7f7f"},stroke:{color:n},hover:{fill:{color:n},stroke:{color:"#7f7f7f"}}},content:{color:"#fff"}},editable:{resize:{handles:{fill:{color:n},stroke:{color:"#fff"},hover:{fill:{color:"#fff"},stroke:{color:"#fff"}}}},rotate:{thumb:{stroke:{color:"#fff"},fill:{color:"#fff"}}}},selectable:{stroke:{color:"#fff"}},connectionDefaults:{stroke:{color:"#7f7f7f"},content:{color:"#fff"},selection:{handles:{fill:{color:n},stroke:{color:"#fff"}},stroke:{color:"#fff"}}}},treeMap:{colors:[["#3f51b5","#cff3f0"],["#03a9f4","#e5f6fe"],["#4caf50","#edf7ed"],["#f9ce1d","#fefae8"],["#ff9800","#fff4e5"],["#ff5722","#ffeee8"]]}}),function(){function o(){return{icon:{background:"#007cc0",border:{color:"#007cc0"}},label:{color:"#ffffff"},line:{color:a}}}var r="#333333",l="#7f7f7f",c="#bdbdbd",a="#c8c8c8",t="#dddddd",i=["#008fd3","#99d101","#f39b02","#f05662","#c03c53","#acacac"],s=["#cbe8f5","#eaf5cb","#fceacc","#fbdcdf","#f2d7dc","#eeeeee"],f=i[0],d=n;h("fiori",{chart:{title:{color:r},legend:{labels:{color:r},inactiveItems:{labels:{color:l},markers:{color:l}}},seriesDefaults:{labels:{color:r},errorBars:{color:r},notes:o(),candlestick:{downColor:a,line:{color:c}},area:{opacity:.8},waterfall:{line:{color:a}},horizontalWaterfall:{line:{color:a}},overlay:{gradient:"none"},border:{_brightness:1}},seriesColors:i,axisDefaults:{line:{color:a},labels:{color:r},minorGridLines:{color:t},majorGridLines:{color:a},title:{color:r},crosshair:{color:l},notes:o()}},gauge:{pointer:{color:f},scale:{rangePlaceholderColor:a,labels:{color:r},minorTicks:{color:r},majorTicks:{color:r},line:{color:r}}},diagram:{shapeDefaults:{fill:{color:f},connectorDefaults:{fill:{color:r},stroke:{color:d},hover:{fill:{color:d},stroke:{color:r}}},content:{color:r}},editable:{resize:{handles:{fill:{color:d},stroke:{color:c},hover:{fill:{color:c},stroke:{color:c}}}},rotate:{thumb:{stroke:{color:c},fill:{color:c}}}},selectable:{stroke:{color:c}},connectionDefaults:{stroke:{color:c},content:{color:c},selection:{handles:{fill:{color:d},stroke:{color:c}},stroke:{color:c}}}},treeMap:{colors:e(i,s)}})}(),function(){function o(){return{icon:{background:"#00b0ff",border:{color:"#00b0ff"}},label:{color:"#ffffff"},line:{color:a}}}var r="#4e4e4e",l="#7f7f7f",c="#bdbdbd",a="#c8c8c8",t="#e5e5e5",i=["#0072c6","#5db2ff","#008a17","#82ba00","#ff8f32","#ac193d"],s=["#cbe2f3","#deeffe","#cbe7d0","#e5f0cb","#fee8d5","#eed0d7"],f=i[0],d=n;h("office365",{chart:{title:{color:r},legend:{labels:{color:r},inactiveItems:{labels:{color:l},markers:{color:l}}},seriesDefaults:{labels:{color:r},errorBars:{color:r},notes:o(),candlestick:{downColor:a,line:{color:c}},area:{opacity:.8},waterfall:{line:{color:a}},horizontalWaterfall:{line:{color:a}},overlay:{gradient:"none"},border:{_brightness:1}},seriesColors:i,axisDefaults:{line:{color:a},labels:{color:r},minorGridLines:{color:t},majorGridLines:{color:a},title:{color:r},crosshair:{color:l},notes:o()}},gauge:{pointer:{color:f},scale:{rangePlaceholderColor:a,labels:{color:r},minorTicks:{color:r},majorTicks:{color:r},line:{color:r}}},diagram:{shapeDefaults:{fill:{color:f},connectorDefaults:{fill:{color:r},stroke:{color:d},hover:{fill:{color:d},stroke:{color:r}}},content:{color:r}},editable:{resize:{handles:{fill:{color:d},stroke:{color:c},hover:{fill:{color:c},stroke:{color:c}}}},rotate:{thumb:{stroke:{color:c},fill:{color:c}}}},selectable:{stroke:{color:c}},connectionDefaults:{stroke:{color:c},content:{color:c},selection:{handles:{fill:{color:d},stroke:{color:c}},stroke:{color:c}}}},treeMap:{colors:e(i,s)}})}(),function(){function o(){return{icon:{background:"#007cc0",border:{color:"#007cc0"}},label:{color:"#ffffff"},line:{color:a}}}var r="#32364c",l="#7f7f7f",c="#bdbdbd",a="#dfe0e1",t="#dfe0e1",i=["#ff4350","#ff9ea5","#00acc1","#80deea","#ffbf46","#ffd78c"],s=["#ffd9dc","#ffeced","#cceef3","#e6f8fb","#fff2da","#fff7e8"],f=i[0],d=n;h("nova",{chart:{title:{color:r},legend:{labels:{color:r},inactiveItems:{labels:{color:l},markers:{color:l}}},seriesDefaults:{labels:{color:r},errorBars:{color:r},notes:o(),candlestick:{downColor:a,line:{color:c}},area:{opacity:.8},waterfall:{line:{color:a}},horizontalWaterfall:{line:{color:a}},overlay:{gradient:"none"},border:{_brightness:1}},seriesColors:i,axisDefaults:{line:{color:a},labels:{color:r},minorGridLines:{color:t},majorGridLines:{color:a},title:{color:r},crosshair:{color:r},notes:o()}},gauge:{pointer:{color:f},scale:{rangePlaceholderColor:a,labels:{color:r},minorTicks:{color:r},majorTicks:{color:r},line:{color:r}}},diagram:{shapeDefaults:{fill:{color:f},connectorDefaults:{fill:{color:r},stroke:{color:d},hover:{fill:{color:d},stroke:{color:r}}},content:{color:r}},editable:{resize:{handles:{fill:{color:d},stroke:{color:c},hover:{fill:{color:c},stroke:{color:c}}}},rotate:{thumb:{stroke:{color:c},fill:{color:c}}}},selectable:{stroke:{color:c}},connectionDefaults:{stroke:{color:c},content:{color:c},selection:{handles:{fill:{color:d},stroke:{color:c}},stroke:{color:c}}}},treeMap:{colors:e(i,s)}})}(),function(){var o=["#ff6358","#ffd246","#78d237","#28b4c8","#2d73f5","#aa46be"],r=["#ffd9dc","#ffeced","#cceef3","#e6f8fb","#fff2da","#fff7e8"];h("default-v2",{chart:{},gauge:{},diagram:{},treeMap:{colors:e(o,r)}}),b.sass=b["default-v2"]}(),function(){var o="#292b2c",r="rgba(0, 0, 0, .04)",l=["#0275d8","#5bc0de","#5cb85c","#f0ad4e","#e67d4a","#d9534f"],c=["#ffd9dc","#ffeced","#cceef3","#e6f8fb","#fff2da","#fff7e8"],a=l[0];h("bootstrap-v4",{chart:{},gauge:{pointer:{color:a},scale:{rangePlaceholderColor:r,labels:{color:o},minorTicks:{color:o},majorTicks:{color:o},line:{color:o}}},diagram:{},treeMap:{colors:e(l,c)}})}()}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(o,e,r){(r||e)()}),function(o,define){define("kendo.dataviz.themes.min",["kendo.dataviz.core.min","dataviz/themes/chart-base-theme.min","dataviz/themes/auto-theme.min","dataviz/themes/themes.min"],o)}(function(){},"function"==typeof define&&define.amd?define:function(o,e,r){(r||e)()});
//# sourceMappingURL=kendo.dataviz.themes.min.js.map
