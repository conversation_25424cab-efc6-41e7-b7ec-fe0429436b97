/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("util/text-metrics.min",["kendo.core.min"],t)}(function(){!function(t){function e(t){return(t+"").replace(s,h)}function r(t){var e,r=[];for(e in t)r.push(e+t[e]);return r.sort().join("")}function n(t){var e,r=2166136261;for(e=0;e<t.length;++e)r+=(r<<1)+(r<<4)+(r<<7)+(r<<8)+(r<<24),r^=t.charCodeAt(e);return r>>>0}function i(){return{width:0,height:0,baseline:0}}function o(t,e,r){return u.current.measure(t,e,r)}var a,s,h,c,f,u;window.kendo.util=window.kendo.util||{},a=kendo.Class.extend({init:function(t){this._size=t,this._length=0,this._map={}},put:function(t,e){var r=this._map,n={key:t,value:e};r[t]=n,this._head?(this._tail.newer=n,n.older=this._tail,this._tail=n):this._head=this._tail=n,this._length>=this._size?(r[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(t){var e=this._map[t];if(e)return e===this._head&&e!==this._tail&&(this._head=e.newer,this._head.older=null),e!==this._tail&&(e.older&&(e.older.newer=e.newer,e.newer.older=e.older),e.older=this._tail,e.newer=null,this._tail.newer=e,this._tail=e),e.value}}),s=/\r?\n|\r|\t/g,h=" ",c={baselineMarkerSize:1},"undefined"!=typeof document&&(f=document.createElement("div"),f.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),u=kendo.Class.extend({init:function(e){this._cache=new a(1e3),this.options=t.extend({},c,e)},measure:function(t,o,a){var s,h,c,u,d,l,p,g,m;if(void 0===a&&(a={}),!t)return i();if(s=r(o),h=n(t+s),c=this._cache.get(h))return c;u=i(),d=a.box||f,l=this._baselineMarker().cloneNode(!1);for(p in o)g=o[p],void 0!==g&&(d.style[p]=g);return m=a.normalizeText!==!1?e(t):t+"",d.textContent=m,d.appendChild(l),document.body.appendChild(d),m.length&&(u.width=d.offsetWidth-this.options.baselineMarkerSize,u.height=d.offsetHeight,u.baseline=l.offsetTop+this.options.baselineMarkerSize),u.width>0&&u.height>0&&this._cache.put(h,u),d.parentNode.removeChild(d),u},_baselineMarker:function(){var t=document.createElement("div");return t.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",t}}),u.current=new u,kendo.deepExtend(kendo.util,{LRUCache:a,TextMetrics:u,measureText:o,objectKey:r,hashKey:n,normalizeText:e})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,r){(r||e)()}),function(t,define){define("pdf/pako.min",["kendo.core.min"],t)}(function(){return function(){kendo.pdf=kendo.pdf||{},kendo.pdf.supportsDeflate=function(){return window.pako&&"function"==typeof window.pako.deflate},kendo.pdf.deflate=function(t){return window.pako.deflate(t)}}(),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,r){(r||e)()}),function(t,define){define("pdf/core.min",["pdf/pako.min","kendo.core.min","kendo.color.min","kendo.drawing.min"],t)}(function(){return function(t){function e(t){function e(){return k>=T}function r(){return k<T?t[k++]:0}function n(e){b(k),t[k++]=255&e,k>T&&(T=k)}function i(){return r()<<8|r()}function o(t){n(t>>8),n(t)}function a(){var t=i();return t>=32768?t-65536:t}function s(t){o(t<0?t+65536:t)}function h(){return 65536*i()+i()}function c(t){o(t>>>16&65535),o(65535&t)}function f(){var t=h();return t>=2147483648?t-4294967296:t}function u(t){c(t<0?t+4294967296:t)}function d(){return h()/65536}function l(t){c(Math.round(65536*t))}function p(){return f()/65536}function g(t){u(Math.round(65536*t))}function m(t){return y(t,r)}function w(t){return String.fromCharCode.apply(String,m(t))}function S(t){for(var e=0;e<t.length;++e)n(t.charCodeAt(e))}function y(t,e){for(var r=Array(t),n=0;n<t;++n)r[n]=e();return r}var b,x,_,v,C,k=0,T=0;return null==t?t=Lt?new Uint8Array(256):[]:T=t.length,b=Lt?function(e){if(e>=t.length){var r=new Uint8Array(Math.max(e+256,2*t.length));r.set(t,0),t=r}}:function(){},x=Lt?function(){return new Uint8Array(t.buffer,0,T)}:function(){return t},_=Lt?function(e){if("string"==typeof e)return S(e);var r=e.length;b(k+r),t.set(e,k),k+=r,k>T&&(T=k)}:function(t){if("string"==typeof t)return S(t);for(var e=0;e<t.length;++e)n(t[e])},v=Lt?function(e,r){if(t.buffer.slice)return new Uint8Array(t.buffer.slice(e,e+r));var n=new Uint8Array(r);return n.set(new Uint8Array(t.buffer,e,r)),n}:function(e,r){return t.slice(e,e+r)},C={eof:e,readByte:r,writeByte:n,readShort:i,writeShort:o,readLong:h,writeLong:c,readFixed:d,writeFixed:l,readShort_:a,writeShort_:s,readLong_:f,writeLong_:u,readFixed_:p,writeFixed_:g,read:m,write:_,readString:w,writeString:S,times:y,get:x,slice:v,offset:function(t){return null!=t?(k=t,C):k},skip:function(t){k+=t},toString:function(){throw Error("FIX CALLER.  BinaryStream is no longer convertible to string!")},length:function(){return T},saveExcursion:function(t){var e=k;try{return t()}finally{k=e}},writeBase64:function(t){window.atob?S(window.atob(t)):_(Mt.decode(t))},base64:function(){return Mt.encode(x())}}}function r(t){for(var e,r,n=[],i=0,o=t.length;i<o;)e=t.charCodeAt(i++),e>=55296&&e<=56319&&i<o?(r=t.charCodeAt(i++),56320==(64512&r)?n.push(((1023&e)<<10)+(1023&r)+65536):(n.push(e),i--)):n.push(e);return n}function n(t){return t.map(function(t){var e="";return t>65535&&(t-=65536,e+=String.fromCharCode(t>>>10&1023|55296),t=56320|1023&t),e+=String.fromCharCode(t)}).join("")}function i(t){var e,r=window.atob(t),n=new Uint8Array(r.length);for(e=0;e<r.length;e++)n[e]=r.charCodeAt(e);return n}function o(t){var e,r=new Uint8Array(t.length);for(e=0;e<t.length;e++)r[e]=t[e];return r}function a(t){return window.atob?i(t):o(Mt.decode(t))}function s(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function h(t){return Object.keys(t).sort(function(t,e){return t-e}).map(parseFloat)}function c(t){var e,r,n;for(this.raw=t,this.scalerType=t.readLong(),this.tableCount=t.readShort(),this.searchRange=t.readShort(),this.entrySelector=t.readShort(),this.rangeShift=t.readShort(),e=this.tables={},r=0;r<this.tableCount;++r)n={tag:t.readString(4),checksum:t.readLong(),offset:t.readLong(),length:t.readLong()},e[n.tag]=n}function f(t){function e(t,e){this.definition=e,this.length=e.length,this.offset=e.offset,this.file=t,this.rawData=t.raw,this.parse(t.raw)}e.prototype.raw=function(){return this.rawData.slice(this.offset,this.length)};for(var r in t)s(t,r)&&(e[r]=e.prototype[r]=t[r]);return e}function u(){var t,e="",r=jt+"";for(t=0;t<r.length;++t)e+=String.fromCharCode(r.charCodeAt(t)-48+65);return++jt,e}function d(t){this.font=t,this.subset={},this.unicodes={},this.ogid2ngid={0:0},this.ngid2ogid={0:0},this.ncid2ogid={},this.next=this.firstChar=1,this.nextGid=1,this.psName=u()+"+"+this.font.psName}function l(t,r){var n,i,o,a,s=this,h=s.contents=e(t);if("ttcf"==h.readString(4)){if(i=function(){h.offset(n),s.parse()},!r)throw Error("Must specify a name for TTC files");for(h.readLong(),o=h.readLong(),a=0;a<o;++a)if(n=h.readLong(),h.saveExcursion(i),s.psName==r)return;throw Error("Font "+r+" not found in collection")}h.offset(0),s.parse()}function p(){function t(){var e,r,i,o=arguments;for(e=0;e<arguments.length;++e){if(r=o[e],void 0===r)throw Error("Cannot output undefined to PDF");if(r instanceof A)r.beforeRender(t),r.render(t);else if($t(r))D(r,t);else if(T(r))F(r,t);else if("number"==typeof r){if(isNaN(r))throw Error("Cannot output NaN to PDF");i=r.toFixed(7),i.indexOf(".")>=0&&(i=i.replace(/\.?0+$/,"")),"-0"==i&&(i="0"),n.writeString(i)}else/string|boolean/.test(typeof r)?n.writeString(r+""):"function"==typeof r.get?n.write(r.get()):"object"==typeof r&&(r?t(new re(r)):n.writeString("null"))}}var r=0,n=e();return t.writeData=function(t){n.write(t)},t.withIndent=function(e){++r,e(t),--r},t.indent=function(){t(Ht,v("",2*r,"  ")),t.apply(null,arguments)},t.offset=function(){return n.offset()},t.toString=function(){throw Error("FIX CALLER")},t.get=function(){return n.get()},t.stream=function(){return n},t}function g(t,e){var r=t.beforeRender,n=t.render;t.beforeRender=function(){},t.render=function(t){t(e," 0 R")},t.renderFull=function(i){t._offset=i.offset(),i(e," 0 obj "),r.call(t,i),n.call(t,i),i(" endobj")}}function m(t){var e,r,n;if("function"!=typeof t&&(e=t,t=function(t,r){return t in e?e[t]:r}),r=t("paperSize",Xt.a4),!r)return{};if("string"==typeof r&&(r=Xt[r.toLowerCase()],null==r))throw Error("Unknown paper size");return r[0]=O(r[0]),r[1]=O(r[1]),t("landscape",!1)&&(r=[Math.max(r[0],r[1]),Math.min(r[0],r[1])]),n=t("margin"),n&&("string"==typeof n||"number"==typeof n?(n=O(n,0),n={left:n,top:n,right:n,bottom:n}):n={left:O(n.left,0),top:O(n.top,0),right:O(n.right,0),bottom:O(n.bottom,0)},t("addMargin")&&(r[0]+=n.left+n.right,r[1]+=n.top+n.bottom)),{paperSize:r,margin:n}}function w(t){function e(e,r){return t&&null!=t[e]?t[e]:r}var r,n,i,o,a=this,s=p(),h=0,c=[];a.getOption=e,a.attach=function(t){return c.indexOf(t)<0&&(g(t,++h),c.push(t)),t},a.pages=[],a.FONTS={},a.IMAGES={},a.GRAD_COL_FUNCTIONS={},a.GRAD_OPC_FUNCTIONS={},a.GRAD_COL={},a.GRAD_OPC={},r=a.attach(new ie),n=a.attach(new oe),e("autoPrint")&&(i={},i.JavaScript=new re({Names:[new Vt("JS"),a.attach(new re({S:P("JavaScript"),JS:new Vt("print(true);")}))]}),r.props.Names=new re(i)),r.setPages(n),o=a.attach(new re({Producer:new Vt(e("producer","Kendo UI PDF Generator")),Title:new Vt(e("title","")),Author:new Vt(e("author","")),Subject:new Vt(e("subject","")),Keywords:new Vt(e("keywords","")),Creator:new Vt(e("creator","Kendo UI PDF Generator")),CreationDate:e("date",new Date)})),a.addPage=function(t){var e,r,i,o=m(function(e,r){return t&&null!=t[e]?t[e]:r}),s=o.paperSize,h=o.margin,c=s[0],f=s[1];return h&&(c-=h.left+h.right,f-=h.top+h.bottom),e=new ne(p(),null,(!0)),r={Contents:a.attach(e),Parent:n,MediaBox:[0,0,s[0],s[1]]},i=new fe(a,r),i._content=e,n.addPage(a.attach(i)),i.transform(1,0,0,-1,0,s[1]),h&&(i.translate(h.left,h.top),i.rect(0,0,c,f),i.clip()),a.pages.push(i),i},a.render=function(){var t,e;for(s("%PDF-1.4",Ht,"%ÂÁÚÏÎ",Ht,Ht),t=0;t<c.length;++t)c[t].renderFull(s),s(Ht,Ht);for(e=s.offset(),s("xref",Ht,0," ",c.length+1,Ht),s("0000000000 65535 f ",Ht),t=0;t<c.length;++t)s(C(c[t]._offset,10)," 00000 n ",Ht);return s(Ht),s("trailer",Ht),s(new re({Size:c.length+1,Root:r,Info:o}),Ht,Ht),s("startxref",Ht,e,Ht),s("%%EOF",Ht),s.stream().offset(0)}}function S(t,e){function r(){window.console&&(window.console.error?window.console.error("Cannot load URL: %s",t):window.console.log("Cannot load URL: %s",t)),e(null)}var n,i;return qt.msie&&(n=/^data:.*?;base64,/i.exec(t))?void e(a(t.substr(n[0].length))):(i=new XMLHttpRequest,i.open("GET",t,!0),Lt&&(i.responseType="arraybuffer"),i.onload=function(){200==i.status||304==i.status?e(Lt?new Uint8Array(i.response):new window.VBArray(i.responseBody).toArray()):r()},i.onerror=r,void i.send(null))}function y(t,e){var r=Jt[t];r?e(r):S(t,function(r){if(null==r)throw Error("Cannot load font from "+t);var n=new l(r);Jt[t]=n,e(n)})}function b(){Zt={}}function x(t,r,n){function i(t){u.src=t,u.complete&&!qt.msie?s():(u.onload=s,u.onerror=a)}function o(){var i,o,s,c,f,d,l,p,g,m,w;r||(r={width:u.width,height:u.height}),i=document.createElement("canvas"),i.width=r.width,i.height=r.height,o=i.getContext("2d"),o.drawImage(u,0,0,r.width,r.height);try{s=o.getImageData(0,0,r.width,r.height)}catch(S){return void a()}finally{h&&URL.revokeObjectURL(h)}for(c=!1,f=e(),d=e(),l=s.data,p=0;p<l.length;)f.writeByte(l[p++]),f.writeByte(l[p++]),f.writeByte(l[p++]),g=l[p++],g<255&&(c=!0),d.writeByte(g);c?u=new G(r.width,r.height,f,d):(m=i.toDataURL("image/jpeg"),m=m.substr(m.indexOf(";base64,")+8),w=e(),w.writeBase64(m),u=new E(w)),n(Zt[t]=u)}function a(){n(Zt[t]="ERROR")}function s(){if(r&&(r.width>=u.width||r.height>=u.height)&&(r=null),!r&&c&&/^image\/jpe?g$/i.test(c.type)){var i=new FileReader;i.onload=function(){try{var r=new E(e(new Uint8Array(this.result)));URL.revokeObjectURL(h),n(Zt[t]=r)}catch(i){o()}},i.readAsArrayBuffer(c)}else o()}var h,c,f,u=Zt[t];u?n(u):(u=new Image,/^data:/i.test(t)||(u.crossOrigin="Anonymous"),Lt&&!/^data:/i.test(t)?(f=new XMLHttpRequest,f.onload=function(){c=f.response,h=URL.createObjectURL(c),i(h)},f.onerror=a,f.open("GET",t,!0),f.responseType="blob",f.send()):i(t))}function _(t){return function(e,r){function n(){0===--i&&r()}var i=e.length,o=i;if(0===i)return r();for(;o-- >0;)t(e[o],n)}}function v(t,e,r){for(;t.length<e;)t=r+t;return t}function C(t,e){return v(t+"",e,"0")}function k(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function T(t){return t instanceof Date}function D(t,e){e("["),t.length>0&&e.withIndent(function(){for(var r=0;r<t.length;++r)r>0&&r%8===0?e.indent(t[r]):e(" ",t[r])}),e(" ]")}function F(t,e){e("(D:",C(t.getUTCFullYear(),4),C(t.getUTCMonth()+1,2),C(t.getUTCDate(),2),C(t.getUTCHours(),2),C(t.getUTCMinutes(),2),C(t.getUTCSeconds(),2),"Z)")}function I(t){return t*(72/25.4)}function L(t){return I(10*t)}function M(t){return 72*t}function O(t,e){var r,n;if("number"==typeof t)return t;if("string"==typeof t&&(r=/^\s*([0-9.]+)\s*(mm|cm|in|pt)\s*$/.exec(t),r&&(n=parseFloat(r[1]),!isNaN(n))))return"pt"==r[2]?n:{mm:I,cm:L,"in":M}[r[2]](n);if(null!=e)return e;throw Error("Can't parse unit: "+t)}function A(){}function R(t,e,r){r||(r=A),t.prototype=new r;for(var n in e)k(e,n)&&(t.prototype[n]=e[n]);return t}function P(t){return k(ee,t)?ee[t]:ee[t]=new te(t)}function E(t){var e,r,n,i,o,a,s,h,c;if(t.offset(0),o=t.readShort(),65496!=o)throw Error("Invalid JPEG image");for(;!t.eof();){if(a=t.readByte(),255!=a)throw Error("Invalid JPEG image");if(s=t.readByte(),h=t.readShort(),ae.indexOf(s)>=0){i=t.readByte(),r=t.readShort(),e=t.readShort(),n=t.readByte();break}t.skip(h-2)}if(null==n)throw Error("Invalid JPEG image");switch(c={Type:P("XObject"),Subtype:P("Image"),Width:e,Height:r,BitsPerComponent:i,Filter:P("DCTDecode")},n){case 1:c.ColorSpace=P("DeviceGray");break;case 3:c.ColorSpace=P("DeviceRGB");break;case 4:c.ColorSpace=P("DeviceCMYK"),c.Decode=[1,0,1,0,1,0,1,0]}this.asStream=function(){t.offset(0);var e=new ne(t,c);return e._resourceName=P("I"+ ++Wt),e}}function G(t,e,r,n){this.asStream=function(i){var o=new ne(n,{Type:P("XObject"),Subtype:P("Image"),Width:t,Height:e,BitsPerComponent:8,ColorSpace:P("DeviceGray")},(!0)),a=new ne(r,{Type:P("XObject"),Subtype:P("Image"),Width:t,Height:e,BitsPerComponent:8,ColorSpace:P("DeviceRGB"),SMask:i.attach(o)},(!0));return a._resourceName=P("I"+ ++Wt),a}}function N(t){return t.map(function(t){return $t(t)?N(t):"number"==typeof t?(Math.round(1e3*t)/1e3).toFixed(3):t}).join(" ")}function B(t,e,r,n,i,o,a){var s=N([e,r,n,i,o,a]),h=t.GRAD_COL_FUNCTIONS[s];return h||(h=t.GRAD_COL_FUNCTIONS[s]=t.attach(new re({FunctionType:2,Domain:[0,1],Range:[0,1,0,1,0,1],N:1,C0:[e,r,n],C1:[i,o,a]}))),h}function z(t,e,r){var n=N([e,r]),i=t.GRAD_OPC_FUNCTIONS[n];return i||(i=t.GRAD_OPC_FUNCTIONS[n]=t.attach(new re({FunctionType:2,Domain:[0,1],Range:[0,1],N:1,C0:[e],C1:[r]}))),i}function U(t,e){function r(t){return 1==t.length?t[0]:{FunctionType:3,Functions:t,Domain:[0,1],Bounds:u,Encode:d}}var n,i,o,a,s,h=!1,c=[],f=[],u=[],d=[];for(n=1;n<e.length;++n)i=e[n-1],o=e[n],a=i.color,s=o.color,f.push(B(t,a.r,a.g,a.b,s.r,s.g,s.b)),(a.a<1||s.a<1)&&(h=!0),u.push(o.offset),d.push(0,1);if(h)for(n=1;n<e.length;++n)i=e[n-1],o=e[n],a=i.color,s=o.color,c.push(z(t,a.a,s.a));return u.pop(),{hasAlpha:h,colors:r(f),opacities:h?r(c):null}}function j(t,e,r,n,i,o){var a,s,h;return o||(h=[e].concat(n),r.forEach(function(t){h.push(t.offset,t.color.r,t.color.g,t.color.b)}),s=N(h),a=t.GRAD_COL[s]),a||(a=new re({Type:P("Shading"),ShadingType:e?3:2,ColorSpace:P("DeviceRGB"),Coords:n,Domain:[0,1],Function:i,Extend:[!0,!0]}),t.attach(a),a._resourceName="S"+ ++Wt,s&&(t.GRAD_COL[s]=a)),a}function q(t,e,r,n,i,o){var a,s,h;return o||(h=[e].concat(n),r.forEach(function(t){h.push(t.offset,t.color.a)}),s=N(h),a=t.GRAD_OPC[s]),a||(a=new re({Type:P("ExtGState"),AIS:!1,CA:1,ca:1,SMask:{Type:P("Mask"),S:P("Luminosity"),G:t.attach(new ne("/a0 gs /s0 sh",{Type:P("XObject"),Subtype:P("Form"),FormType:1,BBox:o?[o.left,o.top+o.height,o.left+o.width,o.top]:[0,1,1,0],Group:{Type:P("Group"),S:P("Transparency"),CS:P("DeviceGray"),I:!0},Resources:{ExtGState:{a0:{CA:1,ca:1}},Shading:{s0:{ColorSpace:P("DeviceGray"),Coords:n,Domain:[0,1],ShadingType:e?3:2,Function:i,Extend:[!0,!0]}}}}))}}),t.attach(a),a._resourceName="O"+ ++Wt,s&&(t.GRAD_OPC[s]=a)),a}function H(t,e,r){var n="radial"==e.type,i=U(t,e.stops),o=n?[e.start.x,e.start.y,e.start.r,e.end.x,e.end.y,e.end.r]:[e.start.x,e.start.y,e.end.x,e.end.y],a=j(t,n,e.stops,o,i.colors,e.userSpace&&r),s=i.hasAlpha?q(t,n,e.stops,o,i.opacities,e.userSpace&&r):null;return{hasAlpha:i.hasAlpha,shading:a,opacity:s}}function W(t){return t.replace(/^\s*(['"])(.*)\1\s*$/,"$2")}function X(t){var e,r=/^\s*((normal|italic)\s+)?((normal|small-caps)\s+)?((normal|bold|\d+)\s+)?(([0-9.]+)(px|pt))(\/(([0-9.]+)(px|pt)|normal))?\s+(.*?)\s*$/i,n=r.exec(t);return n?(e=n[8]?parseInt(n[8],10):12,{italic:n[2]&&"italic"==n[2].toLowerCase(),variant:n[4],bold:n[6]&&/bold|700/i.test(n[6]),fontSize:e,lineHeight:n[12]?"normal"==n[12]?e:parseInt(n[12],10):null,fontFamily:n[14].split(/\s*,\s*/g).map(W)}):{fontSize:12,fontFamily:"sans-serif"}}function J(t){function e(e){return t.bold&&(e+="|bold"),t.italic&&(e+="|italic"),e.toLowerCase()}var r,n,i,o=t.fontFamily;if(o instanceof Array)for(i=0;i<o.length&&(r=e(o[i]),!(n=ue[r]));++i);else n=ue[o.toLowerCase()];for(;"function"==typeof n;)n=n();return n||(n="Times-Roman"),n}function Z(t,e){t=t.toLowerCase(),ue[t]=function(){return ue[e]},ue[t+"|bold"]=function(){return ue[e+"|bold"]},ue[t+"|italic"]=function(){return ue[e+"|italic"]},ue[t+"|bold|italic"]=function(){return ue[e+"|bold|italic"]}}function K(t,e){if(1==arguments.length)for(var r in t)k(t,r)&&K(r,t[r]);else switch(t=t.toLowerCase(),ue[t]=e,t){case"dejavu sans":ue["sans-serif"]=e;break;case"dejavu sans|bold":ue["sans-serif|bold"]=e;break;case"dejavu sans|italic":ue["sans-serif|italic"]=e;break;case"dejavu sans|bold|italic":ue["sans-serif|bold|italic"]=e;break;case"dejavu serif":ue.serif=e;break;case"dejavu serif|bold":ue["serif|bold"]=e;break;case"dejavu serif|italic":ue["serif|italic"]=e;break;case"dejavu serif|bold|italic":ue["serif|bold|italic"]=e;break;case"dejavu mono":ue.monospace=e;break;case"dejavu mono|bold":ue["monospace|bold"]=e;break;case"dejavu mono|italic":ue["monospace|italic"]=e;break;case"dejavu mono|bold|italic":ue["monospace|bold|italic"]=e}}function Y(t,e){var r=t[0],n=t[1],i=t[2],o=t[3],a=t[4],s=t[5],h=e[0],c=e[1],f=e[2],u=e[3],d=e[4],l=e[5];return[r*h+n*f,r*c+n*u,i*h+o*f,i*c+o*u,a*h+s*f+d,a*c+s*u+l]}function $(t){return 1===t[0]&&0===t[1]&&0===t[2]&&1===t[3]&&0===t[4]&&0===t[5]}function V(t,e){function r(t,e,r){return r||(r=s),r.pdf&&null!=r.pdf[t]?r.pdf[t]:e}function n(){function n(t){var e,n,i,a,s,h=t.options,c=xt(t),f=c.bbox;t=c.root,e=r("paperSize",r("paperSize","auto"),h),n=!1,"auto"==e&&(f?(i=f.getSize(),e=[i.width,i.height],n=!0,a=f.getOrigin(),c=new Dt.Group,c.transform(new It.Matrix(1,0,0,1,(-a.x),(-a.y))),c.append(t),t=c):e="A4"),s=o.addPage({paperSize:e,margin:r("margin",r("margin"),h),addMargin:n,landscape:r("landscape",r("landscape",!1),h)}),nt(t,s,o)}if(!(--i>0)){var o=new w({producer:r("producer"),title:r("title"),author:r("author"),subject:r("subject"),keywords:r("keywords"),creator:r("creator"),date:r("date"),autoPrint:r("autoPrint")});h?t.children.forEach(n):n(t),e(o.render(),o)}}var i,o=[],a={},s=t.options,h=r("multiPage"),c=r("imgDPI");c&&b(),t.traverse(function(t){rt({Image:function(t){var e,r,n=t.src();c?(e=t.bbox().size,r=a[n],e={width:Math.ceil(e.width*c/72),height:Math.ceil(e.height*c/72)},r&&(e.width=Math.max(r.width,e.width),e.height=Math.max(r.height,e.height)),a[n]=e):a[n]=null},Text:function(t){var e=X(t.options.font),r=J(e);o.indexOf(r)<0&&o.push(r)}},t)}),i=2,Kt(o,n),Yt(a,n)}function Q(t,e){V(t,function(t){e("data:application/pdf;base64,"+t.base64())})}function tt(t,e){V(t,function(t){e(new window.Blob([t.get()],{type:"application/pdf"}))})}function et(e,r,n,i){window.Blob&&!kt.safari?tt(e,function(e){t.saveAs({dataURI:e,fileName:r}),i&&i(e)}):Q(e,function(e){t.saveAs({dataURI:e,fileName:r,proxyURL:n}),i&&i(e)})}function rt(t,e){var r=t[e.nodeType];return r?r.call.apply(r,arguments):e}function nt(t,e,r){var n,i,o;t.options._pdfDebug&&e.comment("BEGIN: "+t.options._pdfDebug),n=t.transform(),i=t.opacity(),e.save(),null!=i&&i<1&&e.setOpacity(i),it(t,e,r),ot(t,e,r),n&&(o=n.matrix(),e.transform(o.a,o.b,o.c,o.d,o.e,o.f)),at(t,e,r),rt({Path:dt,MultiPath:lt,Circle:pt,Arc:gt,Text:mt,Image:St,Group:wt,Rect:yt},t,e,r),e.restore(),t.options._pdfDebug&&e.comment("END: "+t.options._pdfDebug)}function it(t,e){var r,n,i,o,a,s,h=t.stroke&&t.stroke();if(h){if(r=h.color){if(r=bt(r),null==r)return;e.setStrokeColor(r.r,r.g,r.b),1!=r.a&&e.setStrokeOpacity(r.a)}if(n=h.width,null!=n){if(0===n)return;e.setLineWidth(n)}i=h.dashType,i&&e.setDashPattern(pe[i],0),o=h.lineCap,o&&e.setLineCap(ge[o]),a=h.lineJoin,a&&e.setLineJoin(me[a]),s=h.opacity,null!=s&&e.setStrokeOpacity(s)}}function ot(t,e){var r,n,i=t.fill&&t.fill();if(i&&!(i instanceof Dt.Gradient)){if(r=i.color){if(r=bt(r),null==r)return;e.setFillColor(r.r,r.g,r.b),1!=r.a&&e.setFillOpacity(r.a)}n=i.opacity,null!=n&&e.setFillOpacity(n)}}function at(t,e,r){var n=t.clip();n&&(ut(n,e,r),e.clip())}function st(t){return t&&(t instanceof Dt.Gradient||t.color&&!/^(none|transparent)$/i.test(t.color)&&(null==t.width||t.width>0)&&(null==t.opacity||t.opacity>0))}function ht(t,e,r,n){var i,o,a,s,h,c,f,u,d=t.fill();if(d instanceof Dt.Gradient)return n?e.clipStroke():e.clip(),i=d instanceof Dt.RadialGradient,i?(o={x:d.center().x,y:d.center().y,r:0},a={x:d.center().x,y:d.center().y,r:d.radius()}):(o={x:d.start().x,y:d.start().y},a={x:d.end().x,y:d.end().y}),s=d.stops.elements().map(function(t){var e,r=t.offset();return r=/%$/.test(r)?parseFloat(r)/100:parseFloat(r),e=bt(t.color()),e.a*=t.opacity(),{offset:r,color:e}}),s.unshift(s[0]),s.push(s[s.length-1]),h={userSpace:d.userSpace(),type:i?"radial":"linear",start:o,end:a,stops:s},c=t.rawBBox(),f=c.topLeft(),u=c.getSize(),c={left:f.x,top:f.y,width:u.width,height:u.height},e.gradient(h,c),!0}function ct(t,e,r){st(t.fill())&&st(t.stroke())?ht(t,e,r,!0)||e.fillStroke():st(t.fill())?ht(t,e,r,!1)||e.fill():st(t.stroke())?e.stroke():e.nop()}function ft(t,e){var r,n,i,o=t.segments;if(4==o.length&&t.options.closed){for(r=[],n=0;n<o.length;++n){if(o[n].controlIn())return!1;r[n]=o[n].anchor()}if(i=r[0].y==r[1].y&&r[1].x==r[2].x&&r[2].y==r[3].y&&r[3].x==r[0].x||r[0].x==r[1].x&&r[1].y==r[2].y&&r[2].x==r[3].x&&r[3].y==r[0].y)return e.rect(r[0].x,r[0].y,r[2].x-r[0].x,r[2].y-r[0].y),!0}}function ut(t,e,r){var n,i,o,a,s,h,c=t.segments;if(0!==c.length&&!ft(t,e,r)){for(i=0;i<c.length;++i)o=c[i],a=o.anchor(),n?(s=n.controlOut(),h=o.controlIn(),s&&h?e.bezier(s.x,s.y,h.x,h.y,a.x,a.y):e.lineTo(a.x,a.y)):e.moveTo(a.x,a.y),n=o;t.options.closed&&e.close()}}function dt(t,e,r){ut(t,e,r),ct(t,e,r)}function lt(t,e,r){var n,i=t.paths;for(n=0;n<i.length;++n)ut(i[n],e,r);ct(t,e,r)}function pt(t,e,r){var n=t.geometry();e.circle(n.center.x,n.center.y,n.radius),ct(t,e,r)}function gt(t,e,r){var n,i=t.geometry().curvePoints();for(e.moveTo(i[0].x,i[0].y),n=1;n<i.length;)e.bezier(i[n].x,i[n++].y,i[n].x,i[n++].y,i[n].x,i[n++].y);ct(t,e,r)}function mt(t,e){var r,n=X(t.options.font),i=t._position;t.fill()&&t.stroke()?r=le.fillAndStroke:t.fill()?r=le.fill:t.stroke()&&(r=le.stroke),e.transform(1,0,0,-1,i.x,i.y+n.fontSize),e.beginText(),e.setFont(J(n),n.fontSize),e.setTextRenderingMode(r),e.showText(t.content(),t._pdfRect?t._pdfRect.width():null),e.endText()}function wt(t,e,r){var n,i;for(t._pdfLink&&e.addLink(t._pdfLink.url,t._pdfLink),n=t.children,i=0;i<n.length;++i)nt(n[i],e,r)}function St(t,e){var r,n,i,o=t.src();o&&(r=t.rect(),n=r.getOrigin(),i=r.getSize(),e.transform(i.width,0,0,-i.height,n.x,n.y+i.height),e.drawImage(o))}function yt(t,e,r){var n=t.geometry();e.rect(n.origin.x,n.origin.y,n.size.width,n.size.height),ct(t,e,r)}function bt(e){var r=t.parseColor(e,!0);return r?r.toRGB():null}function xt(t){function e(t){return s=!0,t}function r(t){return t.visible()&&t.opacity()>0&&(st(t.fill())||st(t.stroke()))}function n(t){var e,r,n=[];for(e=0;e<t.length;++e)r=a(t[e]),null!=r&&n.push(r);return n}function i(t,e){var r,n=h,i=c;t.transform()&&(c=c.multiplyCopy(t.transform().matrix())),r=t.clip(),r&&(r=r.bbox(),r&&(r=r.bbox(c),h=h?It.Rect.intersect(h,r):r));try{return e()}finally{h=n,c=i}}function o(t){if(null==h)return!1;var e=t.rawBBox().bbox(c);return h&&e&&(e=It.Rect.intersect(e,h)),e}function a(a){return i(a,function(){if(!(a instanceof Dt.Group||a instanceof Dt.MultiPath)){var i=o(a);if(!i)return e(null);f=f?It.Rect.union(f,i):i}return rt({Path:function(t){return 0!==t.segments.length&&r(t)?t:e(null)},MultiPath:function(t){if(!r(t))return e(null);var i=new Dt.MultiPath(t.options);return i.paths=n(t.paths),0===i.paths.length?e(null):i},Circle:function(t){return r(t)?t:e(null)},Arc:function(t){return r(t)?t:e(null)},Text:function(t){return/\S/.test(t.content())&&r(t)?t:e(null)},Image:function(t){return t.visible()&&t.opacity()>0?t:e(null)},Group:function(r){var i=new Dt.Group(r.options);return i.children=n(r.children),i._pdfLink=r._pdfLink,r===t||0!==i.children.length||r._pdfLink?i:e(null)},Rect:function(t){return r(t)?t:e(null)}},a)})}var s,h=!1,c=It.Matrix.unit(),f=null;do s=!1,t=a(t);while(t&&s);return{root:t,bbox:f}}function _t(t,e){var r,n=Ft.createPromise();for(r in e)"margin"==r&&t.options.pdf&&t.options.pdf._ignoreMargin||t.options.set("pdf."+r,e[r]);return Q(t,n.resolve),n}function vt(t,e){var r,n=Ft.createPromise();for(r in e)"margin"==r&&t.options.pdf&&t.options.pdf._ignoreMargin||t.options.set("pdf."+r,e[r]);return window.Blob&&!kt.safari?tt(t,n.resolve):Q(t,n.resolve),n}var Ct,kt,Tt,Dt,Ft,It,Lt,Mt,Ot,At,Rt,Pt,Et,Gt,Nt,Bt,zt,Ut,jt,qt,Ht,Wt,Xt,Jt,Zt,Kt,Yt,$t,Vt,Qt,te,ee,re,ne,ie,oe,ae,se,he,ce,fe,ue,de,le,pe,ge,me;window.kendo.pdf=window.kendo.pdf||{},Ct=t.support,kt=Ct.browser,Tt=t.pdf,Dt=t.drawing,Ft=Dt.util,It=t.geometry,Lt="undefined"!=typeof Uint8Array&&t.support.browser&&(!t.support.browser.msie||t.support.browser.version>9),Mt=function(){var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return{decode:function(e){for(var r,n,i,o,a,s,h,c=e.replace(/[^A-Za-z0-9\+\/\=]/g,""),f=0,u=c.length,d=[];f<u;)r=t.indexOf(c.charAt(f++)),n=t.indexOf(c.charAt(f++)),i=t.indexOf(c.charAt(f++)),o=t.indexOf(c.charAt(f++)),a=r<<2|n>>>4,s=(15&n)<<4|i>>>2,h=(3&i)<<6|o,d.push(a),64!=i&&d.push(s),64!=o&&d.push(h);return d},encode:function(e){for(var r,n,i,o,a,s,h,c=0,f=e.length,u="";c<f;)r=e[c++],n=e[c++],i=e[c++],o=r>>>2,a=(3&r)<<4|n>>>4,s=(15&n)<<2|i>>>6,h=63&i,c-f==2?s=h=64:c-f==1&&(h=64),u+=t.charAt(o)+t.charAt(a)+t.charAt(s)+t.charAt(h);return u}}}(),c.prototype={readTable:function(t,e){var r=this.tables[t];if(!r)throw Error("Table "+t+" not found in directory");return this[t]=r.table=new e(this,r)},render:function(t){var r,n,i,o,a,h,c,f,u=this,d=Object.keys(t).length,l=Math.pow(2,Math.floor(Math.log(d)/Math.LN2)),p=16*l,g=Math.floor(Math.log(l)/Math.LN2),m=16*d-p,w=e();w.writeLong(this.scalerType),w.writeShort(d),w.writeShort(p),w.writeShort(g),w.writeShort(m),r=16*d,n=w.offset()+r,i=null,o=e();for(a in t)if(s(t,a))for(h=t[a],w.writeString(a),w.writeLong(u.checksum(h)),w.writeLong(n),w.writeLong(h.length),o.write(h),"head"==a&&(i=n),n+=h.length;n%4;)o.writeByte(0),n++;return w.write(o.get()),c=this.checksum(w.get()),f=2981146554-c,w.offset(i+8),w.writeLong(f),w.get()},checksum:function(t){t=e(t);for(var r=0;!t.eof();)r+=t.readLong();return 4294967295&r}},Ot=f({parse:function(t){t.offset(this.offset),this.version=t.readLong(),this.revision=t.readLong(),this.checkSumAdjustment=t.readLong(),this.magicNumber=t.readLong(),this.flags=t.readShort(),this.unitsPerEm=t.readShort(),this.created=t.read(8),this.modified=t.read(8),this.xMin=t.readShort_(),this.yMin=t.readShort_(),this.xMax=t.readShort_(),this.yMax=t.readShort_(),this.macStyle=t.readShort(),this.lowestRecPPEM=t.readShort(),this.fontDirectionHint=t.readShort_(),this.indexToLocFormat=t.readShort_(),this.glyphDataFormat=t.readShort_()},render:function(t){var r=e();return r.writeLong(this.version),r.writeLong(this.revision),r.writeLong(0),r.writeLong(this.magicNumber),r.writeShort(this.flags),r.writeShort(this.unitsPerEm),r.write(this.created),r.write(this.modified),r.writeShort_(this.xMin),r.writeShort_(this.yMin),r.writeShort_(this.xMax),r.writeShort_(this.yMax),r.writeShort(this.macStyle),r.writeShort(this.lowestRecPPEM),r.writeShort_(this.fontDirectionHint),r.writeShort_(t),r.writeShort_(this.glyphDataFormat),r.get()}}),At=f({parse:function(t){t.offset(this.offset);var e=this.file.head.indexToLocFormat;this.offsets=0===e?t.times(this.length/2,function(){return 2*t.readShort()}):t.times(this.length/4,t.readLong)},offsetOf:function(t){return this.offsets[t]},lengthOf:function(t){return this.offsets[t+1]-this.offsets[t]},render:function(t){var r,n=e(),i=t[t.length-1]>65535;for(r=0;r<t.length;++r)i?n.writeLong(t[r]):n.writeShort(t[r]/2);return{format:i?1:0,table:n.get()}}}),Rt=f({parse:function(t){t.offset(this.offset),this.version=t.readLong(),this.ascent=t.readShort_(),this.descent=t.readShort_(),this.lineGap=t.readShort_(),this.advanceWidthMax=t.readShort(),this.minLeftSideBearing=t.readShort_(),this.minRightSideBearing=t.readShort_(),this.xMaxExtent=t.readShort_(),this.caretSlopeRise=t.readShort_(),this.caretSlopeRun=t.readShort_(),this.caretOffset=t.readShort_(),t.skip(8),this.metricDataFormat=t.readShort_(),this.numOfLongHorMetrics=t.readShort()},render:function(t){var r=e();return r.writeLong(this.version),r.writeShort_(this.ascent),r.writeShort_(this.descent),r.writeShort_(this.lineGap),r.writeShort(this.advanceWidthMax),r.writeShort_(this.minLeftSideBearing),r.writeShort_(this.minRightSideBearing),r.writeShort_(this.xMaxExtent),r.writeShort_(this.caretSlopeRise),r.writeShort_(this.caretSlopeRun),r.writeShort_(this.caretOffset),r.write([0,0,0,0,0,0,0,0]),r.writeShort_(this.metricDataFormat),r.writeShort(t.length),r.get()}}),Pt=f({parse:function(t){t.offset(this.offset),this.version=t.readLong(),this.numGlyphs=t.readShort(),this.maxPoints=t.readShort(),this.maxContours=t.readShort(),this.maxComponentPoints=t.readShort(),this.maxComponentContours=t.readShort(),this.maxZones=t.readShort(),this.maxTwilightPoints=t.readShort(),this.maxStorage=t.readShort(),this.maxFunctionDefs=t.readShort(),this.maxInstructionDefs=t.readShort(),this.maxStackElements=t.readShort(),this.maxSizeOfInstructions=t.readShort(),this.maxComponentElements=t.readShort(),this.maxComponentDepth=t.readShort()},render:function(t){var r=e();return r.writeLong(this.version),r.writeShort(t.length),r.writeShort(this.maxPoints),r.writeShort(this.maxContours),r.writeShort(this.maxComponentPoints),r.writeShort(this.maxComponentContours),r.writeShort(this.maxZones),r.writeShort(this.maxTwilightPoints),r.writeShort(this.maxStorage),r.writeShort(this.maxFunctionDefs),r.writeShort(this.maxInstructionDefs),r.writeShort(this.maxStackElements),r.writeShort(this.maxSizeOfInstructions),r.writeShort(this.maxComponentElements),r.writeShort(this.maxComponentDepth),r.get()}}),Et=f({parse:function(t){var e,r,n;t.offset(this.offset),e=this.file,r=e.hhea,this.metrics=t.times(r.numOfLongHorMetrics,function(){return{advance:t.readShort(),lsb:t.readShort_()}}),n=e.maxp.numGlyphs-e.hhea.numOfLongHorMetrics,this.leftSideBearings=t.times(n,t.readShort_)},forGlyph:function(t){var e=this.metrics,r=e.length;return t<r?e[t]:{advance:e[r-1].advance,lsb:this.leftSideBearings[t-r]}},render:function(t){var r,n,i=this,o=e();for(r=0;r<t.length;++r)n=i.forGlyph(t[r]),o.writeShort(n.advance),o.writeShort_(n.lsb);return o.get()}}),Gt=function(){function t(t){this.raw=t}function r(t){var e,r,s;for(this.raw=t,e=this.glyphIds=[],r=this.idOffsets=[];;){if(s=t.readShort(),r.push(t.offset()),e.push(t.readShort()),!(s&o))break;
t.skip(s&n?4:2),s&h?t.skip(8):s&a?t.skip(4):s&i&&t.skip(2)}}var n,i,o,a,h;return t.prototype={compound:!1,render:function(){return this.raw.get()}},n=1,i=8,o=32,a=64,h=128,r.prototype={compound:!0,render:function(t){var r,n,i=this,o=e(this.raw.get());for(r=0;r<this.glyphIds.length;++r)n=i.glyphIds[r],o.offset(i.idOffsets[r]),o.writeShort(t[n]);return o.get()}},f({parse:function(){this.cache={}},glyphFor:function(n){var i,o,a,h,c,f,u,d,l,p,g,m=this.cache;return s(m,n)?m[n]:(i=this.file.loca,o=i.lengthOf(n),0===o?m[n]=null:(a=this.rawData,h=this.offset+i.offsetOf(n),c=e(a.slice(h,o)),f=c.readShort_(),u=c.readShort_(),d=c.readShort_(),l=c.readShort_(),p=c.readShort_(),g=m[n]=f==-1?new r(c):new t(c),g.numberOfContours=f,g.xMin=u,g.yMin=d,g.xMax=l,g.yMax=p,g))},render:function(t,r,n){var i,o,a,s=e(),h=[];for(i=0;i<r.length;++i)o=r[i],a=t[o],h.push(s.offset()),a&&s.write(a.render(n));return h.push(s.offset()),{table:s.get(),offsets:h}}})}(),Nt=function(){function t(t,e){this.text=t,this.length=t.length,this.platformID=e.platformID,this.platformSpecificID=e.platformSpecificID,this.languageID=e.languageID,this.nameID=e.nameID}return f({parse:function(e){var r,n,i,o,a,s,h;for(e.offset(this.offset),e.readShort(),r=e.readShort(),n=this.offset+e.readShort(),i=e.times(r,function(){return{platformID:e.readShort(),platformSpecificID:e.readShort(),languageID:e.readShort(),nameID:e.readShort(),length:e.readShort(),offset:e.readShort()+n}}),o=this.strings={},a=0;a<i.length;++a)s=i[a],e.offset(s.offset),h=e.readString(s.length),o[s.nameID]||(o[s.nameID]=[]),o[s.nameID].push(new t(h,s));this.postscriptEntry=o[6][0],this.postscriptName=this.postscriptEntry.text.replace(/[^\x20-\x7F]/g,"")},render:function(r){var n,i,o,a,h,c,f=this,u=this.strings,d=0;for(n in u)s(u,n)&&(d+=u[n].length);i=e(),o=e(),i.writeShort(0),i.writeShort(d),i.writeShort(6+12*d);for(n in u)if(s(u,n))for(a=6==n?[new t(r,f.postscriptEntry)]:u[n],h=0;h<a.length;++h)c=a[h],i.writeShort(c.platformID),i.writeShort(c.platformSpecificID),i.writeShort(c.languageID),i.writeShort(c.nameID),i.writeShort(c.length),i.writeShort(o.offset()),o.writeString(c.text);return i.write(o.get()),i.get()}})}(),Bt=function(){var t=".notdef .null nonmarkingreturn space exclam quotedbl numbersign dollar percent ampersand quotesingle parenleft parenright asterisk plus comma hyphen period slash zero one two three four five six seven eight nine colon semicolon less equal greater question at A B C D E F G H I J K L M N O P Q R S T U V W X Y Z bracketleft backslash bracketright asciicircum underscore grave a b c d e f g h i j k l m n o p q r s t u v w x y z braceleft bar braceright asciitilde Adieresis Aring Ccedilla Eacute Ntilde Odieresis Udieresis aacute agrave acircumflex adieresis atilde aring ccedilla eacute egrave ecircumflex edieresis iacute igrave icircumflex idieresis ntilde oacute ograve ocircumflex odieresis otilde uacute ugrave ucircumflex udieresis dagger degree cent sterling section bullet paragraph germandbls registered copyright trademark acute dieresis notequal AE Oslash infinity plusminus lessequal greaterequal yen mu partialdiff summation product pi integral ordfeminine ordmasculine Omega ae oslash questiondown exclamdown logicalnot radical florin approxequal Delta guillemotleft guillemotright ellipsis nonbreakingspace Agrave Atilde Otilde OE oe endash emdash quotedblleft quotedblright quoteleft quoteright divide lozenge ydieresis Ydieresis fraction currency guilsinglleft guilsinglright fi fl daggerdbl periodcentered quotesinglbase quotedblbase perthousand Acircumflex Ecircumflex Aacute Edieresis Egrave Iacute Icircumflex Idieresis Igrave Oacute Ocircumflex apple Ograve Uacute Ucircumflex Ugrave dotlessi circumflex tilde macron breve dotaccent ring cedilla hungarumlaut ogonek caron Lslash lslash Scaron scaron Zcaron zcaron brokenbar Eth eth Yacute yacute Thorn thorn minus multiply onesuperior twosuperior threesuperior onehalf onequarter threequarters franc Gbreve gbreve Idotaccent Scedilla scedilla Cacute cacute Ccaron ccaron dcroat".split(/\s+/g);return f({parse:function(t){var e,r,n=this;switch(t.offset(this.offset),this.format=t.readLong(),this.italicAngle=t.readFixed_(),this.underlinePosition=t.readShort_(),this.underlineThickness=t.readShort_(),this.isFixedPitch=t.readLong(),this.minMemType42=t.readLong(),this.maxMemType42=t.readLong(),this.minMemType1=t.readLong(),this.maxMemType1=t.readLong(),this.format){case 65536:case 196608:break;case 131072:for(e=t.readShort(),this.glyphNameIndex=t.times(e,t.readShort),this.names=[],r=this.offset+this.length;t.offset()<r;)n.names.push(t.readString(t.readByte()));break;case 151552:e=t.readShort(),this.offsets=t.read(e);break;case 262144:this.map=t.times(this.file.maxp.numGlyphs,t.readShort)}},glyphFor:function(e){switch(this.format){case 65536:return t[e]||".notdef";case 131072:var r=this.glyphNameIndex[e];return r<t.length?t[r]:this.names[r-t.length]||".notdef";case 151552:case 196608:return".notdef";case 262144:return this.map[e]||65535}},render:function(r){var n,i,o,a,s,h,c,f=this;if(196608==this.format)return this.raw();for(n=e(this.rawData.slice(this.offset,32)),n.writeLong(131072),n.offset(32),i=[],o=[],a=0;a<r.length;++a)s=r[a],h=f.glyphFor(s),c=t.indexOf(h),c>=0?i.push(c):(i.push(t.length+o.length),o.push(h));for(n.writeShort(r.length),a=0;a<i.length;++a)n.writeShort(i[a]);for(a=0;a<o.length;++a)n.writeByte(o[a].length),n.writeString(o[a]);return n.get()}})}(),zt=function(){function t(t,e,r){var n=this;n.platformID=t.readShort(),n.platformSpecificID=t.readShort(),n.offset=e+t.readLong(),t.saveExcursion(function(){var e,i,o,a,s,h,c,f,u,d,l,p,g,m,w,S,y;switch(t.offset(n.offset),n.format=t.readShort()){case 0:for(n.length=t.readShort(),n.language=t.readShort(),i=0;i<256;++i)r[i]=t.readByte();break;case 4:for(n.length=t.readShort(),n.language=t.readShort(),o=t.readShort()/2,t.skip(6),a=t.times(o,t.readShort),t.skip(2),s=t.times(o,t.readShort),h=t.times(o,t.readShort_),c=t.times(o,t.readShort),f=(n.length+n.offset-t.offset())/2,u=t.times(f,t.readShort),i=0;i<o;++i)for(d=s[i],l=a[i],e=d;e<=l;++e)0===c[i]?p=e+h[i]:(g=c[i]/2-(o-i)+(e-d),p=u[g]||0,0!==p&&(p+=h[i])),r[e]=65535&p;break;case 6:for(n.length=t.readShort(),n.language=t.readShort(),e=t.readShort(),m=t.readShort();m-- >0;)r[e++]=t.readShort();break;case 12:for(t.readShort(),n.length=t.readLong(),n.language=t.readLong(),w=t.readLong();w-- >0;)for(e=t.readLong(),S=t.readLong(),y=t.readLong();e<=S;)r[e++]=y++;break;default:window.console&&window.console.error("Unhandled CMAP format: "+n.format)}})}function r(t,r){function n(e){return r[t[e]]}var i,o,a,s,c,f,u,d,l,p,g,m,w,S,y,b,x,_=h(t),v=[],C=[],k=null,T=null;for(i=0;i<_.length;++i)o=_[i],a=n(o),s=a-o,null!=k&&s===T||(k&&C.push(k),v.push(o),T=s),k=o;for(k&&C.push(k),C.push(65535),v.push(65535),c=v.length,f=2*c,u=2*Math.pow(2,Math.floor(Math.log(c)/Math.LN2)),d=Math.log(u/2)/Math.LN2,l=f-u,p=[],g=[],m=[],i=0;i<c;++i){if(w=v[i],S=C[i],65535==w){p.push(0),g.push(0);break}if(y=n(w),w-y>=32768)for(p.push(0),g.push(2*(m.length+c-i)),b=w;b<=S;++b)m.push(n(b));else p.push(y-w),g.push(0)}return x=e(),x.writeShort(3),x.writeShort(1),x.writeLong(12),x.writeShort(4),x.writeShort(16+8*c+2*m.length),x.writeShort(0),x.writeShort(f),x.writeShort(u),x.writeShort(d),x.writeShort(l),C.forEach(x.writeShort),x.writeShort(0),v.forEach(x.writeShort),p.forEach(x.writeShort_),g.forEach(x.writeShort),m.forEach(x.writeShort),x.get()}return f({parse:function(e){var r,n=this,i=n.offset;e.offset(i),n.codeMap={},n.version=e.readShort(),r=e.readShort(),n.tables=e.times(r,function(){return new t(e,i,n.codeMap)})},render:function(t,n){var i=e();return i.writeShort(0),i.writeShort(1),i.write(r(t,n)),i.get()}})}(),Ut=f({parse:function(t){t.offset(this.offset),this.version=t.readShort(),this.averageCharWidth=t.readShort_(),this.weightClass=t.readShort(),this.widthClass=t.readShort(),this.type=t.readShort(),this.ySubscriptXSize=t.readShort_(),this.ySubscriptYSize=t.readShort_(),this.ySubscriptXOffset=t.readShort_(),this.ySubscriptYOffset=t.readShort_(),this.ySuperscriptXSize=t.readShort_(),this.ySuperscriptYSize=t.readShort_(),this.ySuperscriptXOffset=t.readShort_(),this.ySuperscriptYOffset=t.readShort_(),this.yStrikeoutSize=t.readShort_(),this.yStrikeoutPosition=t.readShort_(),this.familyClass=t.readShort_(),this.panose=t.times(10,t.readByte),this.charRange=t.times(4,t.readLong),this.vendorID=t.readString(4),this.selection=t.readShort(),this.firstCharIndex=t.readShort(),this.lastCharIndex=t.readShort(),this.version>0&&(this.ascent=t.readShort_(),this.descent=t.readShort_(),this.lineGap=t.readShort_(),this.winAscent=t.readShort(),this.winDescent=t.readShort(),this.codePageRange=t.times(2,t.readLong),this.version>1&&(this.xHeight=t.readShort(),this.capHeight=t.readShort(),this.defaultChar=t.readShort(),this.breakChar=t.readShort(),this.maxContext=t.readShort()))},render:function(){return this.raw()}}),jt=1e5,d.prototype={use:function(t){var e,n,i,o=this;return"string"==typeof t?r(t).reduce(function(t,e){return t+String.fromCharCode(o.use(e))},""):(e=o.unicodes[t],e||(e=o.next++,o.subset[e]=t,o.unicodes[t]=e,n=o.font.cmap.codeMap[t],n&&(o.ncid2ogid[e]=n,null==o.ogid2ngid[n]&&(i=o.nextGid++,o.ogid2ngid[n]=i,o.ngid2ogid[i]=n))),e)},encodeText:function(t){return this.use(t)},glyphIds:function(){return h(this.ogid2ngid)},glyphsFor:function(t,e){var r,n,i,o=this;for(e||(e={}),r=0;r<t.length;++r)n=t[r],e[n]||(i=e[n]=o.font.glyf.glyphFor(n),i&&i.compound&&o.glyphsFor(i.glyphIds,e));return e},render:function(){var t,e,r,n,i,o,a,c,f=this,u=this.glyphsFor(this.glyphIds());for(t in u)s(u,t)&&(t=parseInt(t,10),null==f.ogid2ngid[t]&&(e=f.nextGid++,f.ogid2ngid[t]=e,f.ngid2ogid[e]=t));return r=h(this.ngid2ogid),n=r.map(function(t){return this.ngid2ogid[t]},this),i=this.font,o=i.glyf.render(u,n,this.ogid2ngid),a=i.loca.render(o.offsets),this.lastChar=this.next-1,c={cmap:zt.render(this.ncid2ogid,this.ogid2ngid),glyf:o.table,loca:a.table,hmtx:i.hmtx.render(n),hhea:i.hhea.render(n),maxp:i.maxp.render(n),post:i.post.render(n),name:i.name.render(this.psName),head:i.head.render(a.format),"OS/2":i.os2.render()},this.font.directory.render(c)},cidToGidMap:function(){var t,r,n,i=this,o=e(),a=0;for(t=this.firstChar;t<this.next;++t){for(;a<t;)o.writeShort(0),a++;r=i.ncid2ogid[t],r?(n=i.ogid2ngid[r],o.writeShort(n)):o.writeShort(0),a++}return o.get()}},l.prototype={parse:function(){var t=this.directory=new c(this.contents);this.head=t.readTable("head",Ot),this.loca=t.readTable("loca",At),this.hhea=t.readTable("hhea",Rt),this.maxp=t.readTable("maxp",Pt),this.hmtx=t.readTable("hmtx",Et),this.glyf=t.readTable("glyf",Gt),this.name=t.readTable("name",Nt),this.post=t.readTable("post",Bt),this.cmap=t.readTable("cmap",zt),this.os2=t.readTable("OS/2",Ut),this.psName=this.name.postscriptName,this.ascent=this.os2.ascent||this.hhea.ascent,this.descent=this.os2.descent||this.hhea.descent,this.lineGap=this.os2.lineGap||this.hhea.lineGap,this.scale=1e3/this.head.unitsPerEm},widthOfGlyph:function(t){return this.hmtx.forGlyph(t).advance*this.scale},makeSubset:function(){return new d(this)}},qt=t.support.browser,Ht="\n",Wt=0,Xt={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],executive:[521.86,756],folio:[612,936],legal:[612,1008],letter:[612,792],tabloid:[792,1224]},Jt={"Times-Roman":!0,"Times-Bold":!0,"Times-Italic":!0,"Times-BoldItalic":!0,Helvetica:!0,"Helvetica-Bold":!0,"Helvetica-Oblique":!0,"Helvetica-BoldOblique":!0,Courier:!0,"Courier-Bold":!0,"Courier-Oblique":!0,"Courier-BoldOblique":!0,Symbol:!0,ZapfDingbats:!0},Zt={},Kt=_(y),Yt=function(t,e){function r(){0===--i&&e()}var n=Object.keys(t),i=n.length;return 0===i?e():void n.forEach(function(e){x(e,t[e],r)})},w.prototype={loadFonts:Kt,loadImages:Yt,getFont:function(t){var e=this.FONTS[t];if(!e){if(e=Jt[t],!e)throw Error("Font "+t+" has not been loaded");e=this.attach(e===!0?new se(t):new he(this,e)),this.FONTS[t]=e}return e},getImage:function(t){var e=this.IMAGES[t];if(!e){if(e=Zt[t],!e)throw Error("Image "+t+" has not been loaded");if("ERROR"===e)return null;e=this.IMAGES[t]=this.attach(e.asStream(this))}return e},getOpacityGS:function(t,e){var r,n,i,o=parseFloat(t).toFixed(3);return t=parseFloat(o),o+=e?"S":"F",r=this._opacityGSCache||(this._opacityGSCache={}),n=r[o],n||(i={Type:P("ExtGState")},e?i.CA=t:i.ca=t,n=this.attach(new re(i)),n._resourceName=P("GS"+ ++Wt),r[o]=n),n},dict:function(t){return new re(t)},name:function(t){return P(t)},stream:function(t,e){return new ne(e,t)}},$t=Array.isArray||function(t){return t instanceof Array},A.prototype.beforeRender=function(){},Vt=R(function(t){this.value=t},{render:function(t){var e,r="",n=this.value;for(e=0;e<n.length;++e)r+=String.fromCharCode(255&n.charCodeAt(e));t("(",r.replace(/([\(\)\\])/g,"\\$1"),")")},toString:function(){return this.value}}),Qt=R(function(t){this.value=t},{render:function(t){var e,r=this;for(t("<"),e=0;e<this.value.length;++e)t(C(r.value.charCodeAt(e).toString(16),4));t(">")}},Vt),te=R(function(t){this.name=t},{render:function(t){t("/"+this.escape())},escape:function(){return this.name.replace(/[^\x21-\x7E]/g,function(t){return"#"+C(t.charCodeAt(0).toString(16),2)})},toString:function(){return this.name}}),ee={},te.get=P,re=R(function(t){this.props=t},{render:function(t){var e=this.props,r=!0;t("<<"),t.withIndent(function(){for(var n in e)k(e,n)&&!/^_/.test(n)&&(r=!1,t.indent(P(n)," ",e[n]))}),r||t.indent(),t(">>")}}),ne=R(function(t,r,n){if("string"==typeof t){var i=e();i.write(t),t=i}this.data=t,this.props=r||{},this.compress=n},{render:function(t){var e=this.data.get(),r=this.props;this.compress&&Tt.supportsDeflate()&&(r.Filter?r.Filter instanceof Array||(r.Filter=[r.Filter]):r.Filter=[],r.Filter.unshift(P("FlateDecode")),e=Tt.deflate(e)),r.Length=e.length,t(new re(r)," stream",Ht),t.writeData(e),t(Ht,"endstream")}}),ie=R(function(){this.props={Type:P("Catalog")}},{setPages:function(t){this.props.Pages=t}},re),oe=R(function(){this.props={Type:P("Pages"),Kids:[],Count:0}},{addPage:function(t){this.props.Kids.push(t),this.props.Count++}},re),ae=[192,193,194,195,197,198,199,201,202,203,205,206,207],se=R(function(t){this.props={Type:P("Font"),Subtype:P("Type1"),BaseFont:P(t)},this._resourceName=P("F"+ ++Wt)},{encodeText:function(t){return new Vt(t+"")}},re),he=R(function(t,e,r){var n,i;r=this.props=r||{},r.Type=P("Font"),r.Subtype=P("Type0"),r.Encoding=P("Identity-H"),this._pdf=t,this._font=e,this._sub=e.makeSubset(),this._resourceName=P("F"+ ++Wt),n=e.head,this.name=e.psName,i=this.scale=e.scale,this.bbox=[n.xMin*i,n.yMin*i,n.xMax*i,n.yMax*i],this.italicAngle=e.post.italicAngle,this.ascent=e.ascent*i,this.descent=e.descent*i,this.lineGap=e.lineGap*i,this.capHeight=e.os2.capHeight||this.ascent,this.xHeight=e.os2.xHeight||0,this.stemV=0,this.familyClass=(e.os2.familyClass||0)>>8,this.isSerif=this.familyClass>=1&&this.familyClass<=7,this.isScript=10==this.familyClass,this.flags=(e.post.isFixedPitch?1:0)|(this.isSerif?2:0)|(this.isScript?8:0)|(0!==this.italicAngle?64:0)|32},{encodeText:function(t){return new Qt(this._sub.encodeText(t+""))},getTextWidth:function(t,e){var r,n,i=this,o=0,a=this._font.cmap.codeMap;for(r=0;r<e.length;++r)n=a[e.charCodeAt(r)],o+=i._font.widthOfGlyph(n||0);return o*t/1e3},beforeRender:function(){var t,r,n,i,o=this,a=o._sub,s=a.render(),h=new ne(e(s),{Length1:s.length},(!0)),c=o._pdf.attach(new re({Type:P("FontDescriptor"),FontName:P(o._sub.psName),FontBBox:o.bbox,Flags:o.flags,StemV:o.stemV,ItalicAngle:o.italicAngle,Ascent:o.ascent,Descent:o.descent,CapHeight:o.capHeight,XHeight:o.xHeight,FontFile2:o._pdf.attach(h)})),f=a.ncid2ogid,u=a.firstChar,d=a.lastChar,l=[];!function g(t,e){if(t<=d){var r=f[t];null==r?g(t+1):(e||l.push(t,e=[]),e.push(o._font.widthOfGlyph(r)),g(t+1,e))}}(u),t=new re({Type:P("Font"),Subtype:P("CIDFontType2"),BaseFont:P(o._sub.psName),CIDSystemInfo:new re({Registry:new Vt("Adobe"),Ordering:new Vt("Identity"),Supplement:0}),FontDescriptor:c,FirstChar:u,LastChar:d,DW:Math.round(o._font.widthOfGlyph(0)),W:l,CIDToGIDMap:o._pdf.attach(o._makeCidToGidMap())}),r=o.props,r.BaseFont=P(o._sub.psName),r.DescendantFonts=[o._pdf.attach(t)],n=new ce(u,d,a.subset),i=new ne(p(),null,(!0)),i.data(n),r.ToUnicode=o._pdf.attach(i)},_makeCidToGidMap:function(){return new ne(e(this._sub.cidToGidMap()),null,(!0))}},re),ce=R(function(t,e,r){this.firstChar=t,this.lastChar=e,this.map=r},{render:function(t){t.indent("/CIDInit /ProcSet findresource begin"),t.indent("12 dict begin"),t.indent("begincmap"),t.indent("/CIDSystemInfo <<"),t.indent("  /Registry (Adobe)"),t.indent("  /Ordering (UCS)"),t.indent("  /Supplement 0"),t.indent(">> def"),t.indent("/CMapName /Adobe-Identity-UCS def"),t.indent("/CMapType 2 def"),t.indent("1 begincodespacerange"),t.indent("  <0000><ffff>"),t.indent("endcodespacerange");var e=this;t.indent(e.lastChar-e.firstChar+1," beginbfchar"),t.withIndent(function(){var r,i,o,a;for(r=e.firstChar;r<=e.lastChar;++r){for(i=e.map[r],o=n([i]),t.indent("<",C(r.toString(16),4),">","<"),a=0;a<o.length;++a)t(C(o.charCodeAt(a).toString(16),4));t(">")}}),t.indent("endbfchar"),t.indent("endcmap"),t.indent("CMapName currentdict /CMap defineresource pop"),t.indent("end"),t.indent("end")}}),fe=R(function(t,e){this._pdf=t,this._rcount=0,this._textMode=!1,this._fontResources={},this._gsResources={},this._xResources={},this._patResources={},this._shResources={},this._opacity=1,this._matrix=[1,0,0,1,0,0],this._annotations=[],this._font=null,this._fontSize=null,this._contextStack=[],e=this.props=e||{},e.Type=P("Page"),e.ProcSet=[P("PDF"),P("Text"),P("ImageB"),P("ImageC"),P("ImageI")],e.Resources=new re({Font:new re(this._fontResources),ExtGState:new re(this._gsResources),XObject:new re(this._xResources),Pattern:new re(this._patResources),Shading:new re(this._shResources)}),e.Annots=this._annotations},{_out:function(){this._content.data.apply(null,arguments)},transform:function(t,e,r,n,i,o){$(arguments)||(this._matrix=Y(arguments,this._matrix),this._out(t," ",e," ",r," ",n," ",i," ",o," cm"),this._out(Ht))},translate:function(t,e){this.transform(1,0,0,1,t,e)},scale:function(t,e){this.transform(t,0,0,e,0,0)},rotate:function(t){var e=Math.cos(t),r=Math.sin(t);this.transform(e,r,-r,e,0,0)},beginText:function(){this._textMode=!0,this._out("BT",Ht)},endText:function(){this._textMode=!1,this._out("ET",Ht)},_requireTextMode:function(){if(!this._textMode)throw Error("Text mode required; call page.beginText() first")},_requireFont:function(){if(!this._font)throw Error("No font selected; call page.setFont() first")},setFont:function(t,e){this._requireTextMode(),null==t?t=this._font:t instanceof he||(t=this._pdf.getFont(t)),null==e&&(e=this._fontSize),this._fontResources[t._resourceName]=t,this._font=t,this._fontSize=e,this._out(t._resourceName," ",e," Tf",Ht)},setTextLeading:function(t){this._requireTextMode(),this._out(t," TL",Ht)},setTextRenderingMode:function(t){this._requireTextMode(),this._out(t," Tr",Ht)},showText:function(t,e){var r,n;this._requireFont(),t.length>1&&e&&this._font instanceof he&&(r=this._font.getTextWidth(this._fontSize,t),n=e/r*100,this._out(n," Tz ")),this._out(this._font.encodeText(t)," Tj",Ht)},showTextNL:function(t){this._requireFont(),this._out(this._font.encodeText(t)," '",Ht)},addLink:function(t,e){var r=this._toPage({x:e.left,y:e.bottom}),n=this._toPage({x:e.right,y:e.top});this._annotations.push(new re({Type:P("Annot"),Subtype:P("Link"),Rect:[r.x,r.y,n.x,n.y],Border:[0,0,0],A:new re({Type:P("Action"),S:P("URI"),URI:new Vt(t)})}))},setStrokeColor:function(t,e,r){this._out(t," ",e," ",r," RG",Ht)},setOpacity:function(t){this.setFillOpacity(t),this.setStrokeOpacity(t),this._opacity*=t},setStrokeOpacity:function(t){if(t<1){var e=this._pdf.getOpacityGS(this._opacity*t,!0);this._gsResources[e._resourceName]=e,this._out(e._resourceName," gs",Ht)}},setFillColor:function(t,e,r){this._out(t," ",e," ",r," rg",Ht)},setFillOpacity:function(t){if(t<1){var e=this._pdf.getOpacityGS(this._opacity*t,!1);this._gsResources[e._resourceName]=e,this._out(e._resourceName," gs",Ht)}},gradient:function(t,e){var r,n,i;this.save(),this.rect(e.left,e.top,e.width,e.height),this.clip(),t.userSpace||this.transform(e.width,0,0,e.height,e.left,e.top),r=H(this._pdf,t,e),n=r.shading._resourceName,this._shResources[n]=r.shading,r.hasAlpha&&(i=r.opacity._resourceName,this._gsResources[i]=r.opacity,this._out("/"+i+" gs ")),this._out("/"+n+" sh",Ht),this.restore()},setDashPattern:function(t,e){this._out(t," ",e," d",Ht)},setLineWidth:function(t){this._out(t," w",Ht)},setLineCap:function(t){this._out(t," J",Ht)},setLineJoin:function(t){this._out(t," j",Ht)},setMitterLimit:function(t){this._out(t," M",Ht)},save:function(){this._contextStack.push(this._context()),this._out("q",Ht)},restore:function(){this._out("Q",Ht),this._context(this._contextStack.pop())},moveTo:function(t,e){this._out(t," ",e," m",Ht)},lineTo:function(t,e){this._out(t," ",e," l",Ht)},bezier:function(t,e,r,n,i,o){this._out(t," ",e," ",r," ",n," ",i," ",o," c",Ht)},bezier1:function(t,e,r,n){this._out(t," ",e," ",r," ",n," y",Ht)},bezier2:function(t,e,r,n){this._out(t," ",e," ",r," ",n," v",Ht)},close:function(){this._out("h",Ht)},rect:function(t,e,r,n){this._out(t," ",e," ",r," ",n," re",Ht)},ellipse:function(t,e,r,n){function i(e){return t+e}function o(t){return e+t}var a=.5522847498307936;this.moveTo(i(0),o(n)),this.bezier(i(r*a),o(n),i(r),o(n*a),i(r),o(0)),this.bezier(i(r),o(-n*a),i(r*a),o(-n),i(0),o(-n)),this.bezier(i(-r*a),o(-n),i(-r),o(-n*a),i(-r),o(0)),this.bezier(i(-r),o(n*a),i(-r*a),o(n),i(0),o(n))},circle:function(t,e,r){this.ellipse(t,e,r,r)},stroke:function(){this._out("S",Ht)},nop:function(){this._out("n",Ht)},clip:function(){this._out("W n",Ht)},clipStroke:function(){this._out("W S",Ht)},closeStroke:function(){this._out("s",Ht)},fill:function(){this._out("f",Ht)},fillStroke:function(){this._out("B",Ht)},drawImage:function(t){var e=this._pdf.getImage(t);e&&(this._xResources[e._resourceName]=e,this._out(e._resourceName," Do",Ht))},comment:function(t){var e=this;t.split(/\r?\n/g).forEach(function(t){e._out("% ",t,Ht)})},_context:function(t){return null==t?{opacity:this._opacity,matrix:this._matrix}:(this._opacity=t.opacity,void(this._matrix=t.matrix))},_toPage:function(t){var e=this._matrix,r=e[0],n=e[1],i=e[2],o=e[3],a=e[4],s=e[5];return{x:r*t.x+i*t.y+a,y:n*t.x+o*t.y+s}}},re),ue={serif:"Times-Roman","serif|bold":"Times-Bold","serif|italic":"Times-Italic","serif|bold|italic":"Times-BoldItalic","sans-serif":"Helvetica","sans-serif|bold":"Helvetica-Bold","sans-serif|italic":"Helvetica-Oblique","sans-serif|bold|italic":"Helvetica-BoldOblique",monospace:"Courier","monospace|bold":"Courier-Bold","monospace|italic":"Courier-Oblique","monospace|bold|italic":"Courier-BoldOblique",zapfdingbats:"ZapfDingbats","zapfdingbats|bold":"ZapfDingbats","zapfdingbats|italic":"ZapfDingbats","zapfdingbats|bold|italic":"ZapfDingbats"},Z("Times New Roman","serif"),Z("Courier New","monospace"),Z("Arial","sans-serif"),Z("Helvetica","sans-serif"),Z("Verdana","sans-serif"),Z("Tahoma","sans-serif"),Z("Georgia","sans-serif"),Z("Monaco","monospace"),Z("Andale Mono","monospace"),de={fill:0,stroke:1,fillAndStroke:2,invisible:3,fillAndClip:4,strokeAndClip:5,fillStrokeClip:6,clip:7},le=de,pe={dash:[4],dashDot:[4,2,1,2],dot:[1,2],longDash:[8,2],longDashDot:[8,2,1,2],longDashDotDot:[8,2,1,2,1,2],solid:[]},ge={butt:0,round:1,square:2},me={miter:0,round:1,bevel:2},t.deepExtend(t.pdf,{Document:w,BinaryStream:e,defineFont:K,parseFontDef:X,getFontURL:J,loadFonts:Kt,loadImages:Yt,getPaperOptions:m,clearImageCache:b,TEXT_RENDERING_MODE:de,exportPDF:_t,exportPDFToBlob:vt,saveAs:et,toDataURL:Q,toBlob:tt,render:V}),t.drawing.exportPDF=t.pdf.exportPDF,t.drawing.pdf=t.pdf}(kendo),kendo},"function"==typeof define&&define.amd?define:function(t,e,r){(r||e)()}),function(t,define){define("pdf/mixins.min",["pdf/core.min"],t)}(function(){return function(t,e){kendo.PDFMixin={extend:function(t){t.events.push("pdfExport"),t.options.pdf=this.options,t.saveAsPDF=this.saveAsPDF,t._drawPDF=this._drawPDF,t._drawPDFShadow=this._drawPDFShadow},options:{fileName:"Export.pdf",proxyURL:"",paperSize:"auto",allPages:!1,landscape:!1,margin:null,title:null,author:null,subject:null,keywords:null,creator:"Kendo UI PDF Generator v."+kendo.version,date:null},saveAsPDF:function(){var e,r=new t.Deferred,n=r.promise(),i={promise:n};if(!this.trigger("pdfExport",i))return e=this.options.pdf,e.multiPage=e.multiPage||e.allPages,this._drawPDF(r).then(function(t){return kendo.drawing.exportPDF(t,e)}).done(function(t){kendo.saveAs({dataURI:t,fileName:e.fileName,proxyURL:e.proxyURL,forceProxy:e.forceProxy,proxyTarget:e.proxyTarget}),r.resolve()}).fail(function(t){r.reject(t)}),n},_drawPDF:function(e){var r=new t.Deferred;return kendo.drawing.drawDOM(this.wrapper).done(function(t){var n={page:t,pageNumber:1,progress:1,totalPages:1};e.notify(n),r.resolve(n.page)}).fail(function(t){r.reject(t)}),r},_drawPDFShadow:function(e,r){var n,i,o;return e=e||{},n=this.wrapper,i=t("<div class='k-pdf-export-shadow'>"),e.width&&i.css({width:e.width,overflow:"visible"}),n.before(i),i.append(e.content||n.clone(!0,!0)),o=t.Deferred(),setTimeout(function(){var t=kendo.drawing.drawDOM(i,r);t.always(function(){i.remove()}).then(function(){o.resolve.apply(o,arguments)}).fail(function(){o.reject.apply(o,arguments)}).progress(function(){o.progress.apply(o,arguments)})},15),o.promise()}}}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,r){(r||e)()}),function(t,define){define("kendo.pdf.min",["kendo.core.min","kendo.drawing.min","pdf/core.min","pdf/mixins.min"],t)}(function(){},"function"==typeof define&&define.amd?define:function(t,e,r){(r||e)()});
//# sourceMappingURL=kendo.pdf.min.js.map
