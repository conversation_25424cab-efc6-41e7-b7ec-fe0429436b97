/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.virtuallist.min",["kendo.data.min"],e)}(function(){return function(e,t){function i(e){return e[e.length-1]}function n(e){return e instanceof Array?e:[e]}function s(e){return"string"==typeof e||"number"==typeof e||"boolean"==typeof e}function r(e,t,i){return Math.ceil(e*t/i)}function a(e,t,i){var n=document.createElement(i||"div");return t&&(n.className=t),e.appendChild(n),n}function o(){var t,i=e('<div class="k-popup"><ul class="k-list"><li class="k-item"><li></ul></div>');return i.css({position:"absolute",left:"-200000px",visibility:"hidden"}),i.appendTo(document.body),t=parseFloat(I.getComputedStyles(i.find(".k-item")[0],["line-height"])["line-height"]),i.remove(),t}function l(e,t,i){return{down:e*i,up:e*(t-1-i)}}function u(e,t){var i=(e.listScreens-1-e.threshold)*t,n=e.threshold*t;return function(e,t,s){return t>s?t-e.top<i:0===e.top||t-e.top>n}}function h(e,t){return function(i){return t(e.scrollTop,i)}}function c(e){return function(t,i){return e(t.items,t.index,i),t}}function d(e,t){I.support.browser.msie&&I.support.browser.version<10?e.style.top=t+"px":(e.style.webkitTransform="translateY("+t+"px)",e.style.transform="translateY("+t+"px)")}function f(t,i){return function(n,s){for(var r=0,a=n.length;r<a;r++)t(n[r],s[r],i),s[r].item&&this.trigger(N,{item:e(n[r]),data:s[r].item,ns:I.ui})}}function p(e,t){var i;return t>0?(i=e.splice(0,t),e.push.apply(e,i)):(i=e.splice(t,-t),e.unshift.apply(e,i)),i}function g(i,n,s){var r=s.template;i=e(i),n.item||(r=s.placeholderTemplate),0===n.index&&this.header&&n.group&&this.header.html(s.fixedGroupTemplate(n.group)),this.angular("cleanup",function(){return{elements:[i]}}),i.attr("data-uid",n.item?n.item.uid:"").attr("data-offset-index",n.index),i.html(this.options.columns&&this.options.columns.length&&n.item?m(this.options,n.item,s):r(n.item||{})),i.toggleClass(F,n.current),i.toggleClass(E,n.selected),i.toggleClass("k-first",n.newGroup),i.toggleClass("k-last",n.isLastGroupedItem),i.toggleClass("k-loading-item",!n.item),0!==n.index&&n.newGroup&&e("<div class="+L+"></div>").appendTo(i).html(s.groupTemplate(n.group)),n.top!==t&&d(i[0],n.top),this.angular("compile",function(){return{elements:[i],data:[{dataItem:n.item,group:n.group,newGroup:n.newGroup}]}})}function m(e,t,i){var n,s,r,a,o="";for(n=0;n<e.columns.length;n++)s=e.columns[n].width,r=parseInt(s,10),a="",s&&(a+="style='width:",a+=r,a+=C.test(s)?"%":"px",a+=";'"),o+="<span class='k-cell' "+a+">",o+=i["column"+n](t),o+="</span>";return o}function _(e,t){var i,n,s,r,a=t.length,o=e.length,l=[],u=[];if(o)for(s=0;s<o;s++){for(i=e[s],n=!1,r=0;r<a;r++)if(i===t[r]){n=!0,l.push({index:s,item:i});break}n||u.push(i)}return{changed:l,unchanged:u}}function v(e){return e&&"resolved"!==e.state()}var I=window.kendo,x=I.ui,D=x.Widget,y=x.DataBoundWidget,S=e.proxy,C=/^\d+(\.\d+)?%$/i,k="k-virtual-wrap",b="k-virtual-list",w="k-virtual-content",T="k-list",H="k-group-header",V="k-virtual-item",B="k-item",G="k-height-container",L="k-group",E="k-state-selected",F="k-state-focused",M="k-state-hover",A="change",P="click",R="listBound",N="itemChange",O="activate",z="deactivate",j=".VirtualList",Q=y.extend({init:function(t,i){var s=this;s.bound(!1),s._fetching=!1,D.fn.init.call(s,t,i),s.options.itemHeight||(s.options.itemHeight=o()),i=s.options,s.element.addClass(T+" "+b).attr("role","listbox"),s.content=s.element.wrap("<div unselectable='on' class='"+w+"'></div>").parent(),s.wrapper=s.content.wrap("<div class='"+k+"'></div>").parent(),s.header=s.content.before("<div class='"+H+"'></div>").prev(),i.columns&&i.columns.length&&s.element.removeClass(T),s.element.on("mouseenter"+j,"li:not(.k-loading-item)",function(){e(this).addClass(M)}).on("mouseleave"+j,"li",function(){e(this).removeClass(M)}),s._values=n(s.options.value),s._selectedDataItems=[],s._selectedIndexes=[],s._rangesList={},s._promisesList=[],s._optionID=I.guid(),s._templates(),s.setDataSource(i.dataSource),s.content.on("scroll"+j,I.throttle(function(){s._renderItems(),s._triggerListBound()},i.delay)),s._selectable()},options:{name:"VirtualList",autoBind:!0,delay:100,height:null,listScreens:4,threshold:.5,itemHeight:null,oppositeBuffer:1,type:"flat",selectable:!1,value:[],dataValueField:null,template:"#:data#",placeholderTemplate:"loading...",groupTemplate:"#:data#",fixedGroupTemplate:"#:data#",mapValueTo:"index",valueMapper:null},events:[A,P,R,N,O,z],setOptions:function(e){D.fn.setOptions.call(this,e),this._selectProxy&&this.options.selectable===!1?this.element.off(P,"."+V,this._selectProxy):!this._selectProxy&&this.options.selectable&&this._selectable(),this._templates(),this.refresh()},items:function(){return e(this._items)},destroy:function(){this.wrapper.off(j),this.dataSource.unbind(A,this._refreshHandler),D.fn.destroy.call(this)},setDataSource:function(t){var i,n=this,s=t||{};s=e.isArray(s)?{data:s}:s,s=I.data.DataSource.create(s),n.dataSource?(n.dataSource.unbind(A,n._refreshHandler),n._clean(),n.bound(!1),n._deferValueSet=!0,i=n.value(),n.value([]),n.mute(function(){n.value(i)})):n._refreshHandler=e.proxy(n.refresh,n),n.dataSource=s.bind(A,n._refreshHandler),n.setDSFilter(s.filter()),0!==s.view().length?n.refresh():n.options.autoBind&&s.fetch()},skip:function(){return this.dataSource.currentRangeStart()},_triggerListBound:function(){var e=this,t=e.skip();e.bound()&&!e._selectingValue&&e._skip!==t&&(e._skip=t,e.trigger(R))},_getValues:function(t){var i=this._valueGetter;return e.map(t,function(e){return i(e)})},_highlightSelectedItems:function(){var e,t;for(e=0;e<this._selectedDataItems.length;e++)t=this._getElementByDataItem(this._selectedDataItems[e]),t.length&&t.addClass(E)},refresh:function(e){var t,i=this,n=e&&e.action,s="itemchange"===n,r=this.isFiltered();i._mute||(i._deferValueSet=!1,i._fetching?(i._renderItems&&i._renderItems(!0),i._triggerListBound()):(r&&i.focus(0),i._createList(),n||!i._values.length||r||i.options.skipUpdateOnBind||i._emptySearch?(i.bound(!0),i._highlightSelectedItems(),i._triggerListBound()):(i._selectingValue=!0,i.bound(!0),i.value(i._values,!0).done(function(){i._selectingValue=!1,i._triggerListBound()}))),(s||"remove"===n)&&(t=_(i._selectedDataItems,e.items),t.changed.length&&(s?i.trigger("selectedItemChange",{items:t.changed}):i.value(i._getValues(t.unchanged)))),i._fetching=!1)},removeAt:function(e){return this._selectedIndexes.splice(e,1),this._values.splice(e,1),{position:e,dataItem:this._selectedDataItems.splice(e,1)[0]}},setValue:function(e){this._values=n(e)},value:function(i,s){var r,a=this;return i===t?a._values.slice():(null===i&&(i=[]),i=n(i),a._valueDeferred&&"resolved"!==a._valueDeferred.state()||(a._valueDeferred=e.Deferred()),r="multiple"===a.options.selectable&&a.select().length&&i.length,!r&&i.length||a.select(-1),a._values=i,(a.bound()&&!a._mute&&!a._deferValueSet||s)&&a._prefetchByValue(i),a._valueDeferred)},_checkValuesOrder:function(e){if(this._removedAddedIndexes&&this._removedAddedIndexes.length===e.length){var t=this._removedAddedIndexes.slice();return this._removedAddedIndexes=null,t}return e},_prefetchByValue:function(e){var i,n,r,a=this,o=a._dataView,l=a._valueGetter,u=a.options.mapValueTo,h=!1,c=[];for(n=0;n<e.length;n++)for(r=0;r<o.length;r++)i=o[r].item,i&&(h=s(i)?e[n]===i:e[n]===l(i),h&&c.push(o[r].index));return c.length===e.length?(a._values=[],a.select(c),t):("function"==typeof a.options.valueMapper?a.options.valueMapper({value:"multiple"===this.options.selectable?e:e[0],success:function(e){"index"===u?a.mapValueToIndex(e):"dataItem"===u&&a.mapValueToDataItem(e)}}):a.value()[0]?(a._selectingValue=!1,a._triggerListBound()):a.select([-1]),t)},mapValueToIndex:function(e){if(e=e===t||e===-1||null===e?[]:n(e),e.length){var i=this._deselect([]).removed;i.length&&this._triggerChange(i,[])}else e=[-1];this.select(e)},mapValueToDataItem:function(i){var s,r,a,o;if(i=i===t||null===i?[]:n(i),i.length){for(s=e.map(this._selectedDataItems,function(e,t){return{index:t,dataItem:e}}),r=e.map(i,function(e,t){return{index:t,dataItem:e}}),this._selectedDataItems=i,this._selectedIndexes=[],a=0;a<this._selectedDataItems.length;a++)o=this._getElementByDataItem(this._selectedDataItems[a]),this._selectedIndexes.push(this._getIndecies(o)[0]),o.addClass(E);this._triggerChange(s,r),this._valueDeferred&&this._valueDeferred.resolve()}else this.select([-1])},deferredRange:function(t){var i=this.dataSource,n=this.itemCount,s=this._rangesList,r=e.Deferred(),a=[],o=Math.floor(t/n)*n,l=Math.ceil(t/n)*n,u=l===o?[l]:[o,l];return e.each(u,function(t,r){var o,l=r+n,u=s[r];u&&u.end===l?o=u.deferred:(o=e.Deferred(),s[r]={end:l,deferred:o},i._multiplePrefetch(r,n,function(){o.resolve()})),a.push(o)}),e.when.apply(e,a).then(function(){r.resolve()}),r},prefetch:function(t){var i=this,n=this.itemCount,s=!i._promisesList.length;return v(i._activeDeferred)||(i._activeDeferred=e.Deferred(),i._promisesList=[]),e.each(t,function(e,t){i._promisesList.push(i.deferredRange(i._getSkip(t,n)))}),s&&e.when.apply(e,i._promisesList).done(function(){i._promisesList=[],i._activeDeferred.resolve()}),i._activeDeferred},_findDataItem:function(e,t){var i,n;if("group"===this.options.type)for(n=0;n<e.length;n++){if(i=e[n].items,!(i.length<=t))return i[t];t-=i.length}return e[t]},_getRange:function(e,t){return this.dataSource._findRange(e,Math.min(e+t,this.dataSource.total()))},dataItemByIndex:function(t){var i=this,n=i.itemCount,s=i._getSkip(t,n),r=this._getRange(s,n);return i._getRange(s,n).length?("group"===i.options.type&&(I.ui.progress(e(i.wrapper),!0),i.mute(function(){i.dataSource.range(s,n,function(){I.ui.progress(e(i.wrapper),!1)}),r=i.dataSource.view()})),i._findDataItem(r,[t-s])):null},selectedDataItems:function(){return this._selectedDataItems.slice()},scrollWith:function(e){this.content.scrollTop(this.content.scrollTop()+e)},scrollTo:function(e){this.content.scrollTop(e)},scrollToIndex:function(e){this.scrollTo(e*this.options.itemHeight)},focus:function(n){var s,r,a,o,l,u,h=this.options.itemHeight,c=this._optionID,d=!0;if(n===t)return o=this.element.find("."+F),o.length?o:null;if("function"==typeof n)for(a=this.dataSource.flatView(),l=0;l<a.length;l++)if(n(a[l])){n=l;break}return n instanceof Array&&(n=i(n)),isNaN(n)?(s=e(n),r=parseInt(e(s).attr("data-offset-index"),10)):(r=n,s=this._getElementByIndex(r)),r===-1?(this.element.find("."+F).removeClass(F),this._focusedIndex=t,t):(s.length?(s.hasClass(F)&&(d=!1),this._focusedIndex!==t&&(o=this._getElementByIndex(this._focusedIndex),o.removeClass(F).removeAttr("id"),d&&this.trigger(z)),this._focusedIndex=r,s.addClass(F).attr("id",c),u=this._getElementLocation(r),"top"===u?this.scrollTo(r*h):"bottom"===u?this.scrollTo(r*h+h-this._screenHeight):"outScreen"===u&&this.scrollTo(r*h),d&&this.trigger(O)):(this._focusedIndex=r,this.items().removeClass(F),this.scrollToIndex(r)),t)},focusIndex:function(){return this._focusedIndex},focusFirst:function(){this.scrollTo(0),this.focus(0)},focusLast:function(){var e=this.dataSource.total();this.scrollTo(this.heightContainer.offsetHeight),this.focus(e-1)},focusPrev:function(){var e,t=this._focusedIndex;return!isNaN(t)&&t>0?(t-=1,this.focus(t),e=this.focus(),e&&e.hasClass("k-loading-item")&&(t+=1,this.focus(t)),t):(t=this.dataSource.total()-1,this.focus(t),t)},focusNext:function(){var e,t=this._focusedIndex,i=this.dataSource.total()-1;return!isNaN(t)&&t<i?(t+=1,this.focus(t),e=this.focus(),e&&e.hasClass("k-loading-item")&&(t-=1,this.focus(t)),t):(t=0,this.focus(t),t)},_triggerChange:function(e,t){e=e||[],t=t||[],(e.length||t.length)&&this.trigger(A,{removed:e,added:t})},select:function(n){var s,r,a,o,l,u,h=this,c="multiple"!==h.options.selectable,d=v(h._activeDeferred),f=this.isFiltered(),p=[];return n===t?h._selectedIndexes.slice():(h._selectDeferred&&"resolved"!==h._selectDeferred.state()||(h._selectDeferred=e.Deferred()),s=h._getIndecies(n),a=c&&!f&&i(s)===i(this._selectedIndexes),p=h._deselectCurrentValues(s),p.length||!s.length||a?(h._triggerChange(p),h._valueDeferred&&h._valueDeferred.resolve().promise(),h._selectDeferred.resolve().promise()):(1===s.length&&s[0]===-1&&(s=[]),r=s,l=h._deselect(s),p=l.removed,s=l.indices,c&&(d=!1,s.length&&(s=[i(s)])),u=function(){var e=h._select(s);(r.length===s.length||c)&&h.focus(s),h._triggerChange(p,e),h._valueDeferred&&h._valueDeferred.resolve(),h._selectDeferred.resolve()},o=h.prefetch(s),d||(o?o.done(u):u()),h._selectDeferred.promise()))},bound:function(e){return e===t?this._listCreated:(this._listCreated=e,t)},mute:function(e){this._mute=!0,S(e(),this),this._mute=!1},setDSFilter:function(t){this._lastDSFilter=e.extend({},t)},isFiltered:function(){return this._lastDSFilter||this.setDSFilter(this.dataSource.filter()),!I.data.Query.compareFilters(this.dataSource.filter(),this._lastDSFilter)},skipUpdate:e.noop,_getElementByIndex:function(t){return this.items().filter(function(i,n){return t===parseInt(e(n).attr("data-offset-index"),10)})},_getElementByDataItem:function(t){var i,n,r,a=this._dataView,o=this._valueGetter;for(r=0;r<a.length;r++)if(n=a[r].item&&s(a[r].item)?a[r].item===t:a[r].item&&t&&o(a[r].item)==o(t)){i=a[r];break}return i?this._getElementByIndex(i.index):e()},_clean:function(){this.result=t,this._lastScrollTop=t,this._skip=t,e(this.heightContainer).remove(),this.heightContainer=t,this.element.empty()},_height:function(){var e=!!this.dataSource.view().length,t=this.options.height,i=this.options.itemHeight,n=this.dataSource.total();return e?t/i>n&&(t=n*i):t=0,t},setScreenHeight:function(){var e=this._height();this.content.height(e),this._screenHeight=e},screenHeight:function(){return this._screenHeight},_getElementLocation:function(e){var t,i=this.content.scrollTop(),n=this._screenHeight,s=this.options.itemHeight,r=e*s,a=r+s,o=i+n;return t=r===i-s||a>i&&r<i?"top":r===o||r<o&&o<a?"bottom":r>=i&&r<=i+(n-s)?"inScreen":"outScreen"},_templates:function(){var e,t,i,n,s=this.options,r={template:s.template,placeholderTemplate:s.placeholderTemplate,groupTemplate:s.groupTemplate,fixedGroupTemplate:s.fixedGroupTemplate};if(s.columns)for(e=0;e<s.columns.length;e++)t=s.columns[e],i=t.field?""+t.field:"text",r["column"+e]=t.template||"#: "+i+"#";for(n in r)"function"!=typeof r[n]&&(r[n]=I.template(r[n]||""));this.templates=r},_generateItems:function(e,t){for(var i,n=[],s=this.options.itemHeight+"px";t-- >0;)i=document.createElement("li"),i.tabIndex=-1,i.className=V+" "+B,i.setAttribute("role","option"),i.style.height=s,i.style.minHeight=s,e.appendChild(i),n.push(i);return n},_saveInitialRanges:function(){var t,i=this.dataSource._ranges,n=e.Deferred();for(n.resolve(),this._rangesList={},t=0;t<i.length;t++)this._rangesList[i[t].start]={end:i[t].end,deferred:n}},_createList:function(){var t=this,i=t.content.get(0),n=t.options,s=t.dataSource;t.bound()&&t._clean(),t._saveInitialRanges(),t._buildValueGetter(),t.setScreenHeight(),t.itemCount=r(t._screenHeight,n.listScreens,n.itemHeight),t.itemCount>s.total()&&(t.itemCount=s.total()),t._items=t._generateItems(t.element[0],t.itemCount),t._setHeight(n.itemHeight*s.total()),t.options.type=(s.group()||[]).length?"group":"flat","flat"===t.options.type?t.header.hide():t.header.show(),t.getter=t._getter(function(){t._renderItems(!0)}),t._onScroll=function(e,i){var n=t._listItems(t.getter);return t._fixedHeader(e,n(e,i))},t._renderItems=t._whenChanged(h(i,t._onScroll),c(t._reorderList(t._items,e.proxy(g,t)))),t._renderItems(),t._calculateGroupPadding(t._screenHeight),t._calculateColumnsHeaderPadding()},_setHeight:function(e){var t,i,n=this.heightContainer;if(n?t=n.offsetHeight:n=this.heightContainer=a(this.content[0],G),e!==t)for(n.innerHTML="";e>0;)i=Math.min(e,25e4),a(n).style.height=i+"px",e-=i},_getter:function(){var e=null,t=this.dataSource,i=t.skip(),n=this.options.type,s=this.itemCount,r={};return t.pageSize()<s&&this.mute(function(){t.pageSize(s)}),function(a,o){var l,u,h,c,d,f,p,g,m=this;if(t.inRange(o,s)){if(i!==o&&this.mute(function(){t.range(o,s),i=o}),"group"===n){if(!r[o])for(u=r[o]=[],h=t.view(),c=0,d=h.length;c<d;c++)for(f=h[c],p=0,g=f.items.length;p<g;p++)u.push({item:f.items[p],group:f.value});l=r[o][a-o]}else l=t.view()[a-o];return l}return e!==o&&(e=o,i=o,m._getterDeferred&&m._getterDeferred.reject(),m._getterDeferred=m.deferredRange(o),m._getterDeferred.then(function(){var e=m._indexConstraint(m.content[0].scrollTop);m._getterDeferred=null,o<=e&&e<=o+s&&(m._fetching=!0,t.range(o,s))})),null}},_fixedHeader:function(e,t){var i,n,s=this.currentVisibleGroup,r=this.options.itemHeight,a=Math.floor((e-t.top)/r),o=t.items[a];return o&&o.item&&(i=o.group,i!==s&&(n=i||"",this.header.html(this.templates.fixedGroupTemplate(n)),this.currentVisibleGroup=i)),t},_itemMapper:function(e,t,i){var n,r,a=this.options.type,o=this.options.itemHeight,l=this._focusedIndex,u=!1,h=!1,c=!1,d=null,f=!1,p=this._valueGetter;if("group"===a&&(e&&(c=0===t||this._currentGroup!==!1&&this._currentGroup!==e.group,this._currentGroup=e.group),d=e?e.group:null,e=e?e.item:null),"dataItem"===this.options.mapValueTo&&this._selectedDataItems.length&&e){for(n=0;n<this._selectedDataItems.length;n++)if(f=p(this._selectedDataItems[n])===p(e)){u=!0;break}}else if(!this.isFiltered()&&i.length&&e)for(r=0;r<i.length;r++)if(f=s(e)?i[r]===e:i[r]===p(e)){i.splice(r,1),u=!0;break}return l===t&&(h=!0),{item:e?e:null,group:d,newGroup:c,selected:u,current:h,index:t,top:t*o}},_range:function(e){var t,i,n,s=this.itemCount,r=this._values.slice(),a=[];for(this._view={},this._currentGroup=!1,i=e,n=e+s;i<n;i++)t=this._itemMapper(this.getter(i,e),i,r),a[a.length-1]&&(a[a.length-1].isLastGroupedItem=t.newGroup),a.push(t),this._view[t.index]=t;return this._dataView=a,a},_getDataItemsCollection:function(e,t){var i=this._range(this._listIndex(e,t));return{index:i.length?i[0].index:0,top:i.length?i[0].top:0,items:i}},_listItems:function(){var t=this._screenHeight,i=this.options,n=u(i,t);return e.proxy(function(e,t){var i=this.result,s=this._lastScrollTop;return!t&&i&&n(i,e,s)||(i=this._getDataItemsCollection(e,s)),this._lastScrollTop=e,this.result=i,i},this)},_whenChanged:function(e,t){var i;return function(n){var s=e(n);s!==i&&(i=s,t(s,n))}},_reorderList:function(t,i){var n=this,s=t.length,r=-(1/0);return i=e.proxy(f(i,this.templates),this),function(e,a,o){var l,u,h=a-r;o||Math.abs(h)>=s?(l=t,u=e):(l=p(t,h),u=h>0?e.slice(-h):e.slice(0,-h)),i(l,u,n.bound()),r=a}},_bufferSizes:function(){var e=this.options;return l(this._screenHeight,e.listScreens,e.oppositeBuffer)},_indexConstraint:function(e){var t=this.itemCount,i=this.options.itemHeight,n=this.dataSource.total();return Math.min(Math.max(n-t,0),Math.max(0,Math.floor(e/i)))},_listIndex:function(e,t){var i,n=this._bufferSizes();return i=e-(e>t?n.down:n.up),this._indexConstraint(i)},_selectable:function(){this.options.selectable&&(this._selectProxy=e.proxy(this,"_clickHandler"),this.element.on(P+j,"."+V,this._selectProxy))},getElementIndex:function(e){return e instanceof jQuery?parseInt(e.attr("data-offset-index"),10):t},_getIndecies:function(e){var t,i,n,s=[];if("function"==typeof e)for(t=this.dataSource.flatView(),i=0;i<t.length;i++)if(e(t[i])){s.push(i);break}return"number"==typeof e&&s.push(e),n=this.getElementIndex(e),isNaN(n)||s.push(n),e instanceof Array&&(s=e),s},_deselect:function(i){var n,r,a,o,l,u,h,c=[],d=this._selectedIndexes,f=this._selectedDataItems,p=0,g=this.options.selectable,m=0,_=this._valueGetter,v=null;if(i=i.slice(),g!==!0&&i.length){if("multiple"===g)for(u=0;u<i.length;u++){if(v=null,p=e.inArray(i[u],d),r=this.dataItemByIndex(i[u]),p===-1&&r)for(h=0;h<f.length;h++)o=s(r)?f[h]===r:_(f[h])===_(r),o&&(a=this._getElementByIndex(i[u]),v=this._deselectSingleItem(a,h,i[u],m));else n=d[p],n!==t&&(a=this._getElementByIndex(n),v=this._deselectSingleItem(a,p,n,m));v&&(i.splice(u,1),c.push(v),m++,u--)}}else{for(l=0;l<d.length;l++)d[l]!==t?this._getElementByIndex(d[l]).removeClass(E):f[l]&&this._getElementByDataItem(f[l]).removeClass(E),c.push({index:d[l],position:l,dataItem:f[l]});this._values=[],this._selectedDataItems=[],this._selectedIndexes=[]}return{indices:i,removed:c}},_deselectSingleItem:function(e,t,i,n){var s;if(e.hasClass("k-state-selected"))return e.removeClass(E),this._values.splice(t,1),this._selectedIndexes.splice(t,1),s=this._selectedDataItems.splice(t,1)[0],{index:i,position:t+n,dataItem:s}},_deselectCurrentValues:function(t){var i,n,s,r,a=this.element[0].children,o=this._values,l=[],u=0;if("multiple"!==this.options.selectable||!this.isFiltered())return[];if(t[0]===-1)return e(a).removeClass("k-state-selected"),l=e.map(this._selectedDataItems.slice(0),function(e,t){return{dataItem:e,position:t}}),this._selectedIndexes=[],this._selectedDataItems=[],this._values=[],l;for(;u<t.length;u++){for(s=-1,n=t[u],this.dataItemByIndex(n)&&(i=this._valueGetter(this.dataItemByIndex(n))),r=0;r<o.length;r++)if(i==o[r]){s=r;break}s>-1&&(l.push(this.removeAt(s)),e(a[n]).removeClass("k-state-selected"))}return l},_getSkip:function(e,t){var i=e<t?1:Math.floor(e/t)+1;return(i-1)*t},_select:function(t){var i,n,r=this,a="multiple"!==this.options.selectable,o=this.dataSource,l=this.itemCount,u=this._valueGetter,h=[];return a&&(r._selectedIndexes=[],r._selectedDataItems=[],r._values=[]),n=o.skip(),e.each(t,function(e,t){var a=r._getSkip(t,l);r.mute(function(){o.range(a,l),i=r._findDataItem(o.view(),[t-a]),r._selectedIndexes.push(t),r._selectedDataItems.push(i),r._values.push(s(i)?i:u(i)),h.push({index:t,dataItem:i}),r._getElementByIndex(t).addClass(E),o.range(n,l)})}),r._values=r._checkValuesOrder(r._values),h},_clickHandler:function(t){var i=e(t.currentTarget);!t.isDefaultPrevented()&&i.attr("data-uid")&&this.trigger(P,{item:i})},_buildValueGetter:function(){this._valueGetter=I.getter(this.options.dataValueField)},_calculateGroupPadding:function(e){var t=this.items().first(),i=this.header,n=0;i[0]&&"none"!==i[0].style.display&&("auto"!==e&&(n=I.support.scrollbar()),n+=parseFloat(t.css("border-right-width"),10)+parseFloat(t.children(".k-group").css("right"),10),i.css("padding-right",n))},_calculateColumnsHeaderPadding:function(){var e,t,i,n;this.options.columns&&this.options.columns.length&&(e=I.support.isRtl(this.wrapper),t=I.support.scrollbar(),i=this.content.parent().parent().find(".k-grid-header"),n=this.dataSource.total(),i.css(e?"padding-left":"padding-right",n?t:0))}});I.ui.VirtualList=Q,I.ui.plugin(Q)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()});
//# sourceMappingURL=kendo.virtuallist.min.js.map
