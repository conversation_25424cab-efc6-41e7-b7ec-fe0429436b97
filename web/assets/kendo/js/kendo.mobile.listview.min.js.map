{"version": 3, "sources": ["kendo.mobile.listview.js"], "names": ["f", "define", "$", "undefined", "whitespace", "this", "nodeType", "Node", "TEXT_NODE", "nodeValue", "match", "whitespaceRegExp", "addIcon", "item", "icon", "querySelector", "prepend", "enhanceItem", "attrValue", "children", "ICON_SELECTOR", "enhanceLinkItem", "parent", "itemAndDetailButtons", "add", "kendo", "roleSelector", "otherNodes", "contents", "not", "length", "addClass", "attr", "enhanceCheckBoxItem", "label", "putAt", "element", "top", "css", "VirtualListViewItem", "LOAD_ICON", "VirtualListViewLoadingIndicator", "VirtualListViewPressToLoadMore", "VirtualListViewItemBinder", "ListViewItemBinder", "List<PERSON>iew<PERSON><PERSON>er", "ListView", "window", "mobile", "ui", "outerHeight", "_outerHeight", "DataSource", "data", "Widget", "DataBoundWidget", "ITEM_SELECTOR", "HIGHLIGHT_SELECTOR", "proxy", "GROUP_CLASS", "ACTIVE_CLASS", "GROUP_WRAPPER", "GROUP_TEMPLATE", "template", "WRAPPER", "SEARCH_TEMPLATE", "NS", "STYLED", "DATABOUND", "DATABINDING", "ITEM_CHANGE", "CLICK", "CHANGE", "PROGRESS", "FUNCTION", "buttonRegExp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Class", "extend", "init", "listView", "headerFixer", "cacheHeaders", "scroller", "options", "_shouldFixHeaders", "_cacheHeaders", "bind", "_<PERSON><PERSON><PERSON><PERSON>", "e", "_fixHeader", "destroy", "that", "unbind", "fixedHeaders", "headerPair", "offset", "header", "i", "headers", "scrollTop", "<PERSON><PERSON><PERSON><PERSON>", "fixedContainer", "html", "clone", "type", "find", "each", "_", "unshift", "position", "DEFAULT_PULL_PARAMETERS", "page", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handler", "pullParameters", "setDataSource", "dataSource", "setOptions", "pullToRefresh", "pull", "_pulled", "read", "call", "_first", "messages", "pullTemplate", "releaseTemplate", "refreshTemplate", "view", "_change", "pullHandled", "VirtualList", "Observable", "list", "fn", "buffer", "height", "items", "footer", "refresh", "itemConstructor", "prevItem", "idx", "endReached", "pop", "viewSize", "total", "content", "below", "push", "itemCount", "trigger", "_resize", "totalHeight", "bottom", "averageItemHeight", "remainingItemsCount", "batchUpdate", "nextIndex", "initialOffset", "lastDirection", "update", "above", "shift", "firstItem", "lastItem", "padding", "up", "lastTop", "topBorder", "bottomBorder", "index", "at", "dataItem", "append", "offsetHeight", "setDataItem", "remove", "appendTo", "_loadIcon", "enable", "show", "disable", "hide", "_loadButton", "loadMoreText", "loadMore", "kendoMobileButton", "_hideShowButton", "next", "_showLoadButton", "removeClass", "binder", "chromeHeight", "wrapper", "empty", "reset", "range", "makeVirtual", "_scroll", "_scrollEnd", "pageSize", "pressToLoadMore", "virtualViewSize", "Error", "<PERSON><PERSON><PERSON>", "Math", "floor", "updateScrollerSize", "updateSize", "virtualSize", "itemBinder", "_refresh<PERSON><PERSON><PERSON>", "_progress<PERSON><PERSON><PERSON>", "showLoading", "_unbindDataSource", "removedItems", "addedItems", "addedDataItems", "adding", "removing", "action", "dataItems", "prependOnRefresh", "appendOnRefresh", "groups", "group", "groupedMode", "_hasB<PERSON>ingTarget", "findByDataItem", "_filter", "_shouldShowLoading", "hideLoading", "replaceGrouped", "replace", "indexOf", "insertAt", "ns", "endlessScroll", "filter", "filterable", "events", "before", "placeholder", "autoFilter", "searchInput", "closest", "on", "preventDefault", "end", "_old<PERSON><PERSON>er", "val", "split", "join", "_filterChange", "clearButton", "_dataSourceChange", "_refreshInput", "appliedFilters", "filters", "field", "value", "_search", "expr", "setTimeout", "_applyFilter", "operator", "ignoreCase", "_clearFilter", "scrollTreshold", "scrollThreshold", "_userEvents", "UserEvents", "fastTap", "allowSelection", "tap", "_click", "wrap", "_headerFixer", "_itemsCache", "_templates", "virtual", "_style", "$angular", "_start", "_itemBinder", "_pullToRefreshHandler", "_enhanceItems", "notify", "name", "style", "autoBind", "headerTemplate", "pullOffset", "emptyDataSource", "create", "fetch", "unwrap", "_scrollerInstance", "loader", "trigger<PERSON>hange", "_renderItems", "eq", "after", "_angularItems", "cancel", "render", "groupTemplate", "angular", "elements", "selectors", "uid", "replaceItem", "newItem", "replaceWith", "_size", "getSize", "callback", "map", "_dim", "_toggle", "_highlight", "highlight", "which", "clicked", "currentTarget", "role", "plainItem", "prevented", "isDefaultPrevented", "toggleClass", "dataIDAttribute", "templateProxy", "groupTemplateProxy", "_headerTemplate", "event", "target", "buttonElement", "button", "widgetInstance", "id", "getByUid", "_styleGroups", "rootItems", "li", "groupHeader", "first", "is", "grouped", "inset", "parents", "child", "enhanced", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,yBACH,aACA,mBACA,uBACDD,IACL,WAu8BE,MA37BC,UAAUE,EAAGC,GAEV,QAASC,KACL,MAAOC,MAAKC,WAAaC,EAAKC,WAAaH,KAAKI,UAAUC,MAAMC,GAEpE,QAASC,GAAQC,EAAMC,GACfA,IAASD,EAAK,GAAGE,cAAc,aAC/BF,EAAKG,QAAQ,2BAA6BF,EAAO,OAGzD,QAASG,GAAYJ,GACjBD,EAAQC,EAAMK,EAAUL,EAAM,SAC9BD,EAAQC,EAAMK,EAAUL,EAAKM,SAASC,GAAgB,SAE1D,QAASC,GAAgBR,GACrB,GAAIS,GAAST,EAAKS,SAAUC,EAAuBV,EAAKW,IAAIF,EAAOH,SAASM,EAAMC,aAAa,kBAAmBC,EAAaL,EAAOM,WAAWC,IAAIN,GAAsBM,IAAIzB,EAC3KuB,GAAWG,SAGfjB,EAAKkB,SAAS,oBAAoBC,KAAKP,EAAMO,KAAK,QAAS,iBAC3DpB,EAAQC,EAAMK,EAAUI,EAAQ,SAChCV,EAAQC,EAAMK,EAAUL,EAAM,UAElC,QAASoB,GAAoBC,GACzB,GAAKA,EAAM,GAAGnB,cAAc,0CAA5B,CAGA,GAAIF,GAAOqB,EAAMZ,QACbT,GAAKe,WAAWC,IAAIK,GAAOL,IAAI,WAC3B,MAAwB,IAAjBxB,KAAKC,WACb,KAGP4B,EAAMH,SAAS,qBACfG,EAAMf,SAAS,gCAAgCY,SAAS,gCAE5D,QAASI,GAAMC,EAASC,GACpBnC,EAAEkC,GAASE,IAAI,YAAa,oBAAsBD,EAAM,YArC/D,GAsSOE,GAmCAC,EACAC,EAqBAC,EA2BAC,EAgGAC,EA+FAC,EAgEAC,EAxnBArB,EAAQsB,OAAOtB,MAAOlB,EAAOwC,OAAOxC,KAAMyC,EAASvB,EAAMuB,OAAQC,EAAKD,EAAOC,GAAIC,EAAczB,EAAM0B,aAAcC,EAAa3B,EAAM4B,KAAKD,WAAYE,EAASL,EAAGM,gBAAiBC,EAAgB,+CAAgDC,EAAqB,wCAAyCrC,EAAgB,IAAMK,EAAMO,KAAK,QAAU,IAAK0B,EAAQxD,EAAEwD,MAAOxC,EAAYO,EAAMP,UAAWyC,EAAc,iBAAkBC,EAAe,kBAAmBC,EAAgB,eAAiBF,EAAc,sCAAuCG,EAAiBrC,EAAMsC,SAAS,mBAAqBJ,EAAc,8HAA+HK,EAAU,sCAAuCC,EAAkBxC,EAAMsC,SAAS,6PAA8PG,EAAK,uBAAwBC,EAAS,SAAUC,EAAY,YAAaC,EAAc,cAAeC,EAAc,aAAcC,EAAQ,QAASC,EAAS,SAAUC,EAAW,WAAYC,EAAW,WAAY/D,EAAmB,QAASgE,EAAe,SAsCzzCC,EAAcnD,EAAMoD,MAAMC,QAC1BC,KAAM,SAAUC,GAAV,GASEC,GACAC,EATAC,EAAWH,EAASG,UACnBA,KAGL9E,KAAK+E,QAAUJ,EAASI,QACxB/E,KAAK+B,QAAU4C,EAAS5C,QACxB/B,KAAK8E,SAAWH,EAASG,WACzB9E,KAAKgF,oBACDJ,EAAc5E,KACd6E,EAAe,WACfD,EAAYK,iBAEhBN,EAASO,KAAK,SAAUL,GACxBF,EAASO,KAAKpB,EAAQe,GACtBF,EAASO,KAAKnB,EAAWc,GACzB7E,KAAKmF,eAAiB,SAAUC,GAC5BR,EAAYS,WAAWD,IAE3BN,EAASI,KAAK,SAAUlF,KAAKmF,kBAEjCG,QAAS,WACL,GAAIC,GAAOvF,IACPuF,GAAKT,UACLS,EAAKT,SAASU,OAAO,SAAUD,EAAKJ,iBAG5CE,WAAY,SAAUD,GAClB,GAAKpF,KAAKyF,aAAV,CAGA,GAAsFC,GAAYC,EAAQC,EAAtGC,EAAI,EAAGf,EAAW9E,KAAK8E,SAAUgB,EAAU9F,KAAK8F,QAASC,EAAYX,EAAEW,SAC3E,GAAG,CAEC,GADAL,EAAaI,EAAQD,MAChBH,EAAY,CACbE,EAAS/F,EAAE,UACX,OAEJ8F,EAASD,EAAWC,OACpBC,EAASF,EAAWE,aACfD,EAAS,EAAII,EAClB/F,MAAKgG,eAAiBH,IACtBf,EAASmB,eAAeC,KAAKN,EAAOO,SACpCnG,KAAKgG,cAAgBH,KAG7Bb,kBAAmB,WACfhF,KAAKyF,aAAqC,UAAtBzF,KAAK+E,QAAQqB,MAAoBpG,KAAK+E,QAAQU,cAEtER,cAAe,WAEX,GADAjF,KAAKgF,oBACAhF,KAAKyF,aAAV,CAGA,GAAIK,MAAcH,EAAS3F,KAAK8E,SAASiB,SACzC/F,MAAK+B,QAAQsE,KAAK,IAAM/C,GAAagD,KAAK,SAAUC,EAAGX,GACnDA,EAAS/F,EAAE+F,GACXE,EAAQU,SACJb,OAAQC,EAAOa,WAAWzE,IAAM2D,EAChCC,OAAQA,MAGhB5F,KAAK8F,QAAUA,EACf9F,KAAKqF,YAAaU,UAAWJ,QAGjCe,EAA0B,WAC1B,OAASC,KAAM,IAEfC,EAAiBxF,EAAMoD,MAAMC,QAC7BC,KAAM,SAAUC,GACZ,GAAIkC,GAAU7G,KAAM+E,EAAUJ,EAASI,QAASD,EAAWH,EAASG,WAAYgC,EAAiB/B,EAAQ+B,gBAAkBJ,CAC3H1G,MAAK2E,SAAWA,EAChB3E,KAAK8E,SAAWA,EAChBH,EAASO,KAAK,cAAe,SAAUE,GACnCyB,EAAQE,cAAc3B,EAAE4B,cAE5BlC,EAASmC,YACLC,eAAe,EACfC,KAAM,WACGN,EAAQO,UACTP,EAAQO,SAAU,EAClBP,EAAQG,WAAWK,KAAKP,EAAeQ,KAAK3C,EAAUkC,EAAQU,WAGtEC,UACIC,aAAc1C,EAAQyC,SAASC,aAC/BC,gBAAiB3C,EAAQyC,SAASE,gBAClCC,gBAAiB5C,EAAQyC,SAASG,oBAI9CZ,cAAe,SAAUC,GACrB,GAAIH,GAAU7G,IACdA,MAAKuH,OAASP,EAAWY,OAAO,GAChC5H,KAAKgH,WAAaA,EAClBA,EAAW9B,KAAK,SAAU,WACtB2B,EAAQgB,YAEZb,EAAW9B,KAAK,QAAS,WACrB2B,EAAQgB,aAGhBA,QAAS,WAAA,GAMGD,GALJ9C,EAAW9E,KAAK8E,SAAUkC,EAAahH,KAAKgH,UAC5ChH,MAAKoH,SACLtC,EAASgD,eAET9H,KAAKoH,SAAYpH,KAAKuH,SAClBK,EAAOZ,EAAWY,OAClBA,EAAK,KACL5H,KAAKuH,OAASK,EAAK,KAG3B5H,KAAKoH,SAAU,KAGnBW,EAAc3G,EAAM4G,WAAWvD,QAC/BC,KAAM,SAAUK,GACZ,GAAIkD,GAAOjI,IACXoB,GAAM4G,WAAWE,GAAGxD,KAAK4C,KAAKW,GAC9BA,EAAKE,OAASpD,EAAQoD,OACtBF,EAAKG,OAASrD,EAAQqD,OACtBH,EAAKzH,KAAOuE,EAAQvE,KACpByH,EAAKI,SACLJ,EAAKK,OAASvD,EAAQuD,OACtBL,EAAKE,OAAOjD,KAAK,QAAS,WACtB+C,EAAKM,aAGbA,QAAS,WAEL,IAFK,GAMDC,GAA6BC,EAAUjI,EAClCkI,EANLP,EAASnI,KAAKmI,OAAQE,EAAQrI,KAAKqI,MAAOM,GAAa,EACpDN,EAAM5G,QACT4G,EAAMO,MAAMtD,SAIhB,KAFAtF,KAAK2F,OAASwC,EAAOxC,OACjB6C,EAAkBxI,KAAKQ,KAClBkI,EAAM,EAAGA,EAAMP,EAAOU,SAAUH,IAAO,CAC5C,GAAIA,IAAQP,EAAOW,QAAS,CACxBH,GAAa,CACb,OAEJnI,EAAOgI,EAAgBxI,KAAK+I,QAAQ/I,KAAK2F,OAAS0C,EAAM5G,SACxDjB,EAAKwI,MAAMP,GACXA,EAAWjI,EACX6H,EAAMY,KAAKzI,GAEfR,KAAKkJ,UAAYb,EAAM5G,OACvBzB,KAAKmJ,QAAQ,SACbnJ,KAAKoJ,UACDT,GACA3I,KAAKmJ,QAAQ,eAGrBE,YAAa,WACT,IAAKrJ,KAAKqI,MAAM,GACZ,MAAO,EAEX,IAAIJ,GAAOjI,KAAMqI,EAAQJ,EAAKI,MAAOrG,EAAMqG,EAAM,GAAGrG,IAAKsH,EAASjB,EAAMA,EAAM5G,OAAS,GAAG6H,OAAQC,GAAqBD,EAAStH,GAAOiG,EAAKiB,UAAWM,EAAsBvB,EAAKE,OAAO1G,OAASwG,EAAKtC,OAASsC,EAAKiB,SACrN,QAAQlJ,KAAKsI,OAAStI,KAAKsI,OAAOF,OAAS,GAAKkB,EAASE,EAAsBD,GAEnFE,YAAa,SAAUzH,GAAV,GACuCxB,GAiBpCkJ,EAjBRtB,EAASpI,KAAKoI,SAAUC,EAAQrI,KAAKqI,MAAasB,EAAgB3J,KAAK2F,MAC3E,IAAK0C,EAAM,GAAX,CAGA,GAAIrI,KAAK4J,cACL,KAAOvB,EAAMA,EAAM5G,OAAS,GAAG6H,OAAStH,EAAe,EAAToG,GACtB,IAAhBpI,KAAK2F,QAGT3F,KAAK2F,SACLnF,EAAO6H,EAAMO,MACbpI,EAAKqJ,OAAO7J,KAAK+I,QAAQ/I,KAAK2F,SAC9BnF,EAAKsJ,MAAMzB,EAAM,IACjBA,EAAM7B,QAAQhG,OAGlB,MAAO6H,EAAM,GAAGrG,IAAMA,EAAMoG,GAAQ,CAEhC,GADIsB,EAAY1J,KAAK2F,OAAS3F,KAAKkJ,UAC/BQ,IAAc1J,KAAKmI,OAAOW,QAAS,CACnC9I,KAAKmJ,QAAQ,aACb,OAEJ,GAAIO,IAAc1J,KAAKmI,OAAO1G,OAC1B,KAEJjB,GAAO6H,EAAM0B,QACbvJ,EAAKqJ,OAAO7J,KAAK+I,QAAQ/I,KAAK2F,OAAS3F,KAAKkJ,YAC5C1I,EAAKwI,MAAMX,EAAMA,EAAM5G,OAAS,IAChC4G,EAAMY,KAAKzI,GACXR,KAAK2F,SAGTgE,IAAkB3J,KAAK2F,QACvB3F,KAAKoJ,YAGbS,OAAQ,SAAU7H,GAAV,GACiCxB,GAAMwJ,EAAWC,EAkB1CP,EAlBRzB,EAAOjI,KAAMqI,EAAQrI,KAAKqI,MAAkCD,EAASpI,KAAKoI,SAAUc,EAAYlJ,KAAKkJ,UAAWgB,EAAU9B,EAAS,EAAG+B,GAAMnK,KAAKoK,SAAW,GAAKpI,EAAKqI,EAAYrI,EAAMkI,EAASI,EAAetI,EAAMoG,EAAS8B,CAC9N7B,GAAM,KAGXrI,KAAKoK,QAAUpI,EACfhC,KAAK4J,cAAgBO,EACjBA,EACI9B,EAAM,GAAGrG,IAAMqI,GAAahC,EAAMA,EAAM5G,OAAS,GAAG6H,OAASgB,EAAeJ,GAAWlK,KAAK2F,OAAS,IACrG3F,KAAK2F,SACLnF,EAAO6H,EAAMO,MACboB,EAAY3B,EAAM,GAClB7H,EAAKqJ,OAAO7J,KAAK+I,QAAQ/I,KAAK2F,SAC9B0C,EAAM7B,QAAQhG,GACdA,EAAKsJ,MAAME,GACX/B,EAAKmB,WAGLf,EAAMA,EAAM5G,OAAS,GAAG6H,OAASgB,GAAgBjC,EAAM,GAAGrG,IAAMqI,EAAYH,IACxER,EAAY1J,KAAK2F,OAASuD,EAC1BQ,IAAc1J,KAAKmI,OAAOW,QAC1B9I,KAAKmJ,QAAQ,cACNO,IAAc1J,KAAKmI,OAAO1G,SACjCjB,EAAO6H,EAAM0B,QACbE,EAAW5B,EAAMA,EAAM5G,OAAS,GAChC4G,EAAMY,KAAKzI,GACXA,EAAKqJ,OAAO7J,KAAK+I,QAAQ/I,KAAK2F,OAAS3F,KAAKkJ,YAC5CjB,EAAKtC,SACLnF,EAAKwI,MAAMiB,GACXhC,EAAKmB,cAKrBL,QAAS,SAAUwB,GACf,MAAOvK,MAAKmI,OAAOqC,GAAGD,IAE1BjF,QAAS,WACLtF,KAAKwF,UAET4D,QAAS,WACL,GAAIf,GAAQrI,KAAKqI,MAAOrG,EAAM,EAAGsH,EAAS,EAAGU,EAAY3B,EAAM,GAAI4B,EAAW5B,EAAMA,EAAM5G,OAAS,EAC/FuI,KACAhI,EAAMgI,EAAUhI,IAChBsH,EAASW,EAASX,QAEtBtJ,KAAKmJ,QAAQ,UACTnH,IAAKA,EACLsH,OAAQA,IAERtJ,KAAKsI,QACLtI,KAAKsI,OAAOU,MAAMiB,KAI9B7I,GAAMuB,OAAOC,GAAGmF,YAAcA,EAC1B7F,EAAsBd,EAAMoD,MAAMC,QAClCC,KAAM,SAAUC,EAAU8F,GACtB,GAAI1I,GAAU4C,EAAS+F,QAAQD,IAAW,GAAM,GAAIrC,EAASrG,EAAQ4I,YACrE9K,GAAE4E,OAAOzE,MACLgC,IAAK,EACLD,QAASA,EACT4C,SAAUA,EACVyD,OAAQA,EACRkB,OAAQlB,KAGhByB,OAAQ,SAAUY,GACdzK,KAAK+B,QAAU/B,KAAK2E,SAASiG,YAAY5K,KAAK+B,QAAS0I,IAE3DX,MAAO,SAAUtJ,GACTA,IACAR,KAAKoI,OAASpI,KAAK+B,QAAQ4I,aAC3B3K,KAAKgC,IAAMxB,EAAKwB,IAAMhC,KAAKoI,OAC3BpI,KAAKsJ,OAAS9I,EAAKwB,IACnBF,EAAM9B,KAAK+B,QAAS/B,KAAKgC,OAGjCgH,MAAO,SAAUxI,GACTA,IACAR,KAAKoI,OAASpI,KAAK+B,QAAQ4I,aAC3B3K,KAAKgC,IAAMxB,EAAK8I,OAChBtJ,KAAKsJ,OAAStJ,KAAKgC,IAAMhC,KAAKoI,OAC9BtG,EAAM9B,KAAK+B,QAAS/B,KAAKgC,OAGjCsD,QAAS,WACLlE,EAAMkE,QAAQtF,KAAK+B,SACnBlC,EAAEG,KAAK+B,SAAS8I,YAGpB1I,EAAY,sHACZC,EAAkChB,EAAMoD,MAAMC,QAC9CC,KAAM,SAAUC,GACZ3E,KAAK+B,QAAUlC,EAAE,4EAA4EiL,SAASnG,EAAS5C,SAC/G/B,KAAK+K,UAAYlL,EAAEsC,GAAW2I,SAAS9K,KAAK+B,UAEhDiJ,OAAQ,WACJhL,KAAK+B,QAAQkJ,OACbjL,KAAKoI,OAASvF,EAAY7C,KAAK+B,SAAS,IAE5CmJ,QAAS,WACLlL,KAAK+B,QAAQoJ,OACbnL,KAAKoI,OAAS,GAElBY,MAAO,SAAUxI,GACTA,IACAR,KAAKgC,IAAMxB,EAAK8I,OAChBtJ,KAAKsJ,OAAStJ,KAAKoI,OAASpI,KAAKgC,IACjCF,EAAM9B,KAAK+B,QAAS/B,KAAKgC,SAIjCK,EAAiCD,EAAgCqC,QACjEC,KAAM,SAAUC,EAAUwD,GACtBnI,KAAK+K,UAAYlL,EAAEsC,GAAWgJ,OAC9BnL,KAAKoL,YAAcvL,EAAE,sBAAwB8E,EAASI,QAAQyC,SAAS6D,aAAe,QAAQF,OAC9FnL,KAAK+B,QAAUlC,EAAE,wDAAwD6K,OAAO1K,KAAK+K,WAAWL,OAAO1K,KAAKoL,aAAaN,SAASnG,EAAS5C,QAC3I,IAAIuJ,GAAWtL,IACfA,MAAKoL,YAAYG,oBAAoBvI,KAAK,qBAAqBkC,KAAK,QAAS,WACzEoG,EAASE,kBACTrD,EAAOsD,SAEXtD,EAAOjD,KAAK,SAAU,WAClBoG,EAASI,oBAEb1L,KAAKoI,OAASvF,EAAY7C,KAAK+B,SAAS,GACxC/B,KAAKkL,WAETM,gBAAiB,WACbxL,KAAKoL,YAAYD,OACjBnL,KAAK+B,QAAQL,SAAS,uBACtB1B,KAAK+K,UAAU9I,IAAI,UAAW,UAElCyJ,gBAAiB,WACb1L,KAAKoL,YAAYH,OACjBjL,KAAK+B,QAAQ4J,YAAY,uBACzB3L,KAAK+K,UAAUI,UAGnB7I,EAA4BlB,EAAMoD,MAAMC,QACxCC,KAAM,SAAUC,GACZ,GAAIiH,GAAS5L,IACbA,MAAK6L,aAAehJ,EAAY8B,EAASmH,QAAQhL,WAAWU,IAAImD,EAAS5C,UACzE/B,KAAK2E,SAAWA,EAChB3E,KAAK8E,SAAWH,EAASG,WACzB9E,KAAK+E,QAAUJ,EAASI,QACxBJ,EAASO,KAAK,cAAe,SAAUE,GACnCwG,EAAO7E,cAAc3B,EAAE4B,WAAY5B,EAAE2G,SAEzCpH,EAASO,KAAK,SAAU,WACf0G,EAAO3D,KAAKI,MAAM5G,SAGvBmK,EAAO9G,SAASkH,QAChBJ,EAAOzD,OAAO8D,MAAM,GACpBL,EAAO3D,KAAKM,aAEhBvI,KAAK8E,SAASoH,cACdlM,KAAKmM,QAAU,SAAU/G,GACrBwG,EAAO3D,KAAK4B,OAAOzE,EAAEW,YAEzB/F,KAAK8E,SAASI,KAAK,SAAUlF,KAAKmM,SAClCnM,KAAKoM,WAAa,SAAUhH,GACxBwG,EAAO3D,KAAKwB,YAAYrE,EAAEW,YAE9B/F,KAAK8E,SAASI,KAAK,YAAalF,KAAKoM,aAEzC9G,QAAS,WACLtF,KAAKiI,KAAKzC,SACVxF,KAAKmI,OAAO3C,SACZxF,KAAK8E,SAASU,OAAO,SAAUxF,KAAKmM,SACpCnM,KAAK8E,SAASU,OAAO,YAAaxF,KAAKoM,aAE3CrF,cAAe,SAAUC,EAAY+E,GAAtB,GAC8HM,GAAUlE,EAAQG,EAkBvJL,EAlBA2D,EAAS5L,KAAM+E,EAAU/E,KAAK+E,QAASJ,EAAW3E,KAAK2E,SAAUG,EAAWH,EAASG,WAAYwH,EAAkBvH,EAAQuG,QAG/H,IAFAtL,KAAKgH,WAAaA,EAClBqF,EAAWrF,EAAWqF,YAActH,EAAQwH,iBACvCF,IAAaN,EACd,KAAUS,OAAM,mJAEhBxM,MAAKmI,QACLnI,KAAKmI,OAAO7C,UAEhB6C,EAAS,GAAI/G,GAAM4B,KAAKyJ,OAAOzF,EAAY0F,KAAKC,MAAMN,EAAW,GAAIC,GAEjEhE,EADAgE,EACS,GAAIjK,GAA+BsC,EAAUwD,GAE7C,GAAI/F,GAAgCuC,GAE7C3E,KAAKiI,MACLjI,KAAKiI,KAAK3C,UAEV2C,EAAO,GAAIF,IACXI,OAAQA,EACRG,OAAQA,EACR9H,KAAM,SAAUiK,GACZ,MAAO,IAAIvI,GAAoByC,EAAU8F,IAE7CrC,OAAQ,WACJ,MAAOtD,GAASsD,YAGxBH,EAAK/C,KAAK,SAAU,WAChB0G,EAAOgB,qBACPjI,EAASkI,eAEb5E,EAAK/C,KAAK,QAAS,WACf0G,EAAOtD,OAAO0C,WAElB/C,EAAK/C,KAAK,aAAc,WACpBoD,EAAO4C,UACPU,EAAOgB,uBAEXzE,EAAOjD,KAAK,SAAU,WAClB+C,EAAK2B,eAAgB,EACrB3B,EAAKwB,YAAY3E,EAASiB,aAE9BlG,EAAE4E,OAAOzE,MACLmI,OAAQA,EACRrD,SAAUA,EACVmD,KAAMA,EACNK,OAAQA,KAGhBsE,mBAAoB,WAChB5M,KAAK8E,SAASgI,YAAY,EAAG9M,KAAKiI,KAAKoB,cAAgBrJ,KAAK6L,eAEhEtD,QAAS,WACLvI,KAAKiI,KAAKM,WAEdyD,MAAO,WACHhM,KAAKmI,OAAO8D,MAAM,GAClBjM,KAAKiI,KAAKM,aAGdhG,EAAqBnB,EAAMoD,MAAMC,QACjCC,KAAM,SAAUC,GAAV,GAIEoI,GAHAnB,EAAS5L,IACbA,MAAK2E,SAAWA,EAChB3E,KAAK+E,QAAUJ,EAASI,QACpBgI,EAAa/M,KACjBA,KAAKgN,gBAAkB,SAAU5H,GAC7B2H,EAAWxE,QAAQnD,IAEvBpF,KAAKiN,iBAAmB,WACpBtI,EAASuI,eAEbvI,EAASO,KAAK,cAAe,SAAUE,GACnCwG,EAAO7E,cAAc3B,EAAE4B,eAG/B1B,QAAS,WACLtF,KAAKmN,qBAETnB,MAAO,aAEPzD,QAAS,SAAUnD,GAAV,GACoP5E,GAUrP4M,EAAcC,EAAYC,EAC1BC,EACAC,EAkBIjD,EA9BJkD,EAASrI,GAAKA,EAAEqI,OAAQC,EAAYtI,GAAKA,EAAEiD,MAAO1D,EAAW3E,KAAK2E,SAAUqC,EAAahH,KAAKgH,WAAY2G,EAAmB3N,KAAK+E,QAAQ6I,gBAAiBhG,EAAOZ,EAAWY,OAAQiG,EAAS7G,EAAW8G,QAASC,EAAcF,GAAUA,EAAO,EACrP,OAAe,eAAXJ,GACK9I,EAASqJ,sBACVxN,EAAOmE,EAASsJ,eAAeP,GAAW,GACtClN,GACAmE,EAASiG,YAAYpK,EAAMkN,EAAU,KAG7C,IAGAH,EAAoB,QAAXE,IAAqBM,GAAeJ,IAAqBhJ,EAASuJ,QAC3EV,EAAsB,WAAXC,IAAwBM,EACnCR,EACAH,KACOI,IACPJ,EAAezI,EAASsJ,eAAeP,IAEvC/I,EAASwE,QAAQnF,GACbyJ,OAAQA,GAAU,SAClBpF,MAAOqF,EACPN,aAAcA,EACd7C,MAAOnF,GAAKA,EAAEmF,SAEdvK,KAAKmO,sBACLxJ,EAASyJ,cAEb,IAEW,QAAXX,GAAqBM,EAMH,WAAXN,GAAwBM,EAGxBA,EACPpJ,EAAS0J,eAAezG,GACjB+F,IAAqBhJ,EAASuJ,SACrCb,EAAa1I,EAAShE,QAAQiH,GAC9B0F,EAAiB1F,GAEjBjD,EAAS2J,QAAQ1G,IARjByF,KACA1I,EAASkG,OAAO6C,KAPZnD,EAAQ3C,EAAK2G,QAAQb,EAAU,IAC/BnD,OACA8C,EAAa1I,EAAS6J,SAASd,EAAWnD,GAC1C+C,EAAiBI,IAarB1N,KAAKmO,sBACLxJ,EAASyJ,cAEbzJ,EAASwE,QAAQpF,GACb0K,GAAI7L,EACJyK,WAAYA,EACZC,eAAgBA,IAvBpB,KA0BJvG,cAAe,SAAUC,GACjBhH,KAAKgH,YACLhH,KAAKmN,oBAETnN,KAAKgH,WAAaA,EAClBA,EAAW9B,KAAKf,EAAQnE,KAAKgN,iBACzBhN,KAAKmO,sBACLnO,KAAKgH,WAAW9B,KAAKd,EAAUpE,KAAKiN,mBAG5CE,kBAAmB,WACfnN,KAAKgH,WAAWxB,OAAOrB,EAAQnE,KAAKgN,iBAAiBxH,OAAOpB,EAAUpE,KAAKiN,mBAE/EkB,mBAAoB,WAChB,GAAIpJ,GAAU/E,KAAK+E,OACnB,QAAQA,EAAQmC,gBAAkBnC,EAAQuG,WAAavG,EAAQ2J,iBAGnElM,EAAiBpB,EAAMoD,MAAMC,QAC7BC,KAAM,SAAUC,GACZ,GAAIgK,GAAS3O,KAAM4O,EAAajK,EAASI,QAAQ6J,WAAYC,EAAS,eAAgBtJ,EAAOvF,IAC7FA,MAAK2E,SAAWA,EAChB3E,KAAK+E,QAAU6J,EACfjK,EAAS5C,QAAQ+M,OAAOlL,GAAkBmL,YAAaH,EAAWG,aAAe,eAC7EH,EAAWI,cAAe,IAC1BH,GAAU,UAEd7O,KAAK+B,QAAU4C,EAASmH,QAAQzF,KAAK,mBACrCrG,KAAKiP,YAActK,EAASmH,QAAQzF,KAAK,sBAAsB6I,QAAQ,QAAQC,GAAG,SAAWtL,EAAI,SAAUuB,GACvGA,EAAEgK,mBACHC,MAAMF,GAAG,QAAUtL,EAAI,WACtB8K,EAAOW,WAAaX,EAAOM,YAAYM,QACxCJ,GAAGN,EAAOW,MAAM,KAAKC,KAAK5L,EAAK,KAAOA,EAAIR,EAAMrD,KAAK0P,cAAe1P,OACvEA,KAAK2P,YAAchL,EAASmH,QAAQzF,KAAK,oBAAoB8I,GAAGjL,EAAOb,EAAMrD,KAAM,iBAAiBmL,OACpGnL,KAAK4P,kBAAoB/P,EAAEwD,MAAMrD,KAAK6P,cAAe7P,MACrD2E,EAASO,KAAK,cAAe,SAAUE,GACnCA,EAAE4B,WAAW9B,KAAK,SAAUK,EAAKqK,sBAGzCC,cAAe,WAAA,GACPC,GAAiB9P,KAAK2E,SAASqC,WAAW2H,SAC1CM,EAAcjP,KAAK2E,SAASuJ,QAAQe,WAIpCA,GAAYM,IAHXO,GAAkBA,EAAeC,QAAQ,GAAGC,QAAUhQ,KAAK2E,SAASI,QAAQ6J,WAAWoB,MAGxEF,EAAeC,QAAQ,GAAGE,MAF1B,KAKxBC,QAAS,SAAUC,GACfnQ,KAAKkO,SAAU,EACflO,KAAK2P,YAAYQ,EAAO,OAAS,UACjCnQ,KAAK2E,SAASqC,WAAW2H,OAAOwB,IAEpCT,cAAe,SAAUtK,GACrB,GAAIuJ,GAAS3O,IACC,UAAVoF,EAAEgB,MAAmBpG,KAAK+E,QAAQiK,cAAe,EACjDoB,WAAW,WACPzB,EAAO0B,gBACR,GAEHrQ,KAAKqQ,gBAGbA,aAAc,WACV,GAAItL,GAAU/E,KAAK+E,QAASkL,EAAQjQ,KAAKiP,YAAYM,MAAOY,EAAOF,EAAMxO,QACjEuO,MAAOjL,EAAQiL,MACfM,SAAUvL,EAAQuL,UAAY,aAC9BC,WAAYxL,EAAQwL,WACpBN,MAAOA,GACP,IACJA,KAAUjQ,KAAKsP,aAGnBtP,KAAKsP,WAAaW,EAClBjQ,KAAKkQ,QAAQC,KAEjBK,aAAc,SAAUpL,GACpBpF,KAAKiP,YAAYM,IAAI,IACrBvP,KAAKkQ,QAAQ,MACb9K,EAAEgK,oBAGN3M,EAAWQ,EAAOwB,QAClBC,KAAM,SAAU3C,EAASgD,GACrB,GAAIJ,GAAW3E,IACfiD,GAAOiF,GAAGxD,KAAK4C,KAAKtH,KAAM+B,EAASgD,GACnChD,EAAU/B,KAAK+B,QACfgD,EAAU/E,KAAK+E,QACXA,EAAQ0L,iBACR1L,EAAQ2L,gBAAkB3L,EAAQ0L,gBAEtC1O,EAAQoN,GAAG,OAAQ/L,EAAoB,cAAc+L,GAAG,iBAAkB/L,EAAoB,QAC9FpD,KAAK2Q,YAAc,GAAIvP,GAAMwP,WAAW7O,GACpC8O,SAAS,EACTlC,OAAQxL,EACR2N,gBAAgB,EAChBC,IAAK,SAAU3L,GACXT,EAASqM,OAAO5L,MAGxBrD,EAAQE,IAAI,mBAAoB,QAChCF,EAAQkP,KAAKtN,GACb3D,KAAK8L,QAAU9L,KAAK+B,QAAQd,SAC5BjB,KAAKkR,aAAe,GAAI3M,GAAYvE,MACpCA,KAAKmR,eACLnR,KAAKoR,aACLpR,KAAKqR,QAAUtM,EAAQ2J,eAAiB3J,EAAQuG,SAChDtL,KAAKsR,SACDtR,KAAK+E,QAAQwM,WAAavR,KAAKqR,SAAWrR,KAAK+E,QAAQmC,eACvDkJ,WAAWvQ,EAAEwD,MAAMrD,KAAM,WAEzBA,KAAKwR,UAGbA,OAAQ,WACJ,GAAIzM,GAAU/E,KAAK+E,OACf/E,MAAK+E,QAAQ6J,aACb5O,KAAKkO,QAAU,GAAI1L,GAAexC,OAGlCA,KAAKyR,YADLzR,KAAKqR,QACc,GAAI/O,GAA0BtC,MAE9B,GAAIuC,GAAmBvC,MAE1CA,KAAK+E,QAAQmC,gBACblH,KAAK0R,sBAAwB,GAAI9K,GAAe5G,OAEpDA,KAAK+G,cAAchC,EAAQiC,YAC3BhH,KAAK2R,cAAc3R,KAAKqI,SACxBjH,EAAMwQ,OAAO5R,KAAM4C,IAEvBiM,QACI3K,EACAF,EACAD,EACAE,GAEJc,SACI8M,KAAM,WACNC,MAAO,GACP1L,KAAM,OACN2L,UAAU,EACVtM,cAAc,EACd/B,SAAU,UACVsO,eAAgB,wCAChBpE,iBAAiB,EACjBtC,UAAU,EACVoD,eAAe,EACfgC,gBAAiB,GACjBxJ,eAAe,EACfM,UACI6D,aAAc,qBACd5D,aAAc,kBACdC,gBAAiB,qBACjBC,gBAAiB,cAErBsK,WAAY,IACZrD,YAAY,EACZrC,gBAAiB,MAErBhE,QAAS,WACLvI,KAAKyR,YAAYlJ,WAErByD,MAAO,WACHhM,KAAKyR,YAAYzF,SAErBjF,cAAe,SAAUC,GACrB,GAAIkL,IAAmBlL,CACvBhH,MAAKgH,WAAajE,EAAWoP,OAAOnL,GACpChH,KAAKmJ,QAAQ,eACTnC,WAAYhH,KAAKgH,WACjB+E,MAAOmG,IAEPlS,KAAK+E,QAAQgN,WAAaG,IAC1BlS,KAAKqI,QAAQwC,SACb7K,KAAKgH,WAAWoL,UAGxB9M,QAAS,WACLrC,EAAOiF,GAAG5C,QAAQgC,KAAKtH,MACvBoB,EAAMkE,QAAQtF,KAAK+B,SACnB/B,KAAK2Q,YAAYrL,UACbtF,KAAKyR,aACLzR,KAAKyR,YAAYnM,UAEjBtF,KAAKkR,cACLlR,KAAKkR,aAAa5L,UAEtBtF,KAAK+B,QAAQsQ,eACNrS,MAAK+B,cACL/B,MAAK8L,cACL9L,MAAK2Q,aAEhBtI,MAAO,WACH,MAA0B,UAAtBrI,KAAK+E,QAAQqB,KACNpG,KAAK+B,QAAQsE,KAAK,YAAYvF,WAE9Bd,KAAK+B,QAAQjB,WAAWU,IAAI,kBAG3CsD,SAAU,WAIN,MAHK9E,MAAKsS,oBACNtS,KAAKsS,kBAAoBtS,KAAK+B,QAAQmN,QAAQ,sBAAsBlM,KAAK,wBAEtEhD,KAAKsS,mBAEhBpF,YAAa,WACT,GAAItF,GAAO5H,KAAK4H,MACZA,IAAQA,EAAK2K,QACb3K,EAAK2K,OAAOtH,QAGpBmD,YAAa,WACT,GAAIxG,GAAO5H,KAAK4H,MACZA,IAAQA,EAAK2K,QACb3K,EAAK2K,OAAOpH,QAGpBqD,SAAU,SAAUd,EAAWnD,EAAOiI,GAClC,GAAI7N,GAAW3E,IACf,OAAO2E,GAAS8N,aAAa/E,EAAW,SAAUrF,GAQ9C,GAPc,IAAVkC,EACA5F,EAAS5C,QAAQpB,QAAQ0H,GAClBkC,OACP5F,EAAS5C,QAAQ2I,OAAOrC,GAExB1D,EAAS0D,QAAQqK,GAAGnI,EAAQ,GAAGoI,MAAMtK,GAErCmK,EACA,IAAK,GAAI3M,GAAI,EAAGA,EAAIwC,EAAM5G,OAAQoE,IAC9BlB,EAASwE,QAAQlF,GACbzD,KAAM6H,EAAMqK,GAAG7M,GACf7C,KAAM0K,EAAU7H,GAChB4I,GAAI7L,OAMxB8H,OAAQ,SAAUgD,EAAW8E,GACzB,MAAOxS,MAAKwO,SAASd,KAAe8E,IAExC7R,QAAS,SAAU+M,EAAW8E,GAC1B,MAAOxS,MAAKwO,SAASd,EAAW,EAAG8E,IAEvClE,QAAS,SAAUZ,GAOf,MANA1N,MAAK+E,QAAQqB,KAAO,OACpBpG,KAAK4S,cAAc,WACnBxR,EAAMkE,QAAQtF,KAAK+B,QAAQjB,YAC3Bd,KAAK+B,QAAQgK,QACb/L,KAAK2Q,YAAYkC,SACjB7S,KAAKsR,SACEtR,KAAKwO,SAASd,EAAW,IAEpCW,eAAgB,SAAUR,GACtB7N,KAAK+E,QAAQqB,KAAO,QACpBpG,KAAK4S,cAAc,WACnB5S,KAAK+B,QAAQgK,OACb,IAAI1D,GAAQxI,EAAEuB,EAAM0R,OAAO9S,KAAK+S,cAAelF,GAC/C7N,MAAK2R,cAActJ,EAAMvH,SAAS,MAAMA,SAAS,OACjDd,KAAK+B,QAAQ2I,OAAOrC,GACpB1F,EAAO+B,KAAK2D,GACZrI,KAAKsR,SACLtR,KAAK4S,cAAc,YAEvB/H,OAAQ,SAAU6C,GACd,GAAIrF,GAAQrI,KAAKiO,eAAeP,EAChC1N,MAAKgT,QAAQ,UAAW,WACpB,OAASC,SAAU5K,KAEvBjH,EAAMkE,QAAQ+C,GACdA,EAAMwC,UAEVoD,eAAgB,SAAUP,GAAV,GAEHhF,GAASjH,EADdyR,IACJ,KAASxK,EAAM,EAAGjH,EAASiM,EAAUjM,OAAQiH,EAAMjH,EAAQiH,IACvDwK,EAAUxK,GAAO,SAAWtH,EAAMqN,GAAK,OAASf,EAAUhF,GAAKyK,IAAM,GAEzE,OAAOnT,MAAK+B,QAAQsE,KAAK6M,EAAUzD,KAAK,OAE5C7E,YAAa,SAAUpK,EAAMiK,GACzB,GAAI9F,GAAW3E,KAAMoT,EAAc,SAAU/K,GACrC,GAAIgL,GAAUxT,EAAEwI,EAAM,GACtBjH,GAAMkE,QAAQ9E,GACdmE,EAASqO,QAAQ,UAAW,WACxB,OAASC,UAAWpT,EAAEW,OAE1BX,EAAEW,GAAM8S,YAAYD,GACpB1O,EAASwE,QAAQlF,GACbzD,KAAM6S,EACNrQ,KAAMyH,EACNgE,GAAI7L,IAGhB,OAAO5C,MAAKyS,cAAchI,GAAW2I,GAAa,IAEtDvG,WAAY,WACR7M,KAAKuT,MAAQvT,KAAKwT,WAEtBf,aAAc,SAAU/E,EAAW+F,GAC/B,GAAIpL,GAAQxI,EAAEuB,EAAM0R,OAAO9S,KAAK0D,SAAUgK,GAY1C,OAXA+F,GAASpL,GACTrI,KAAKgT,QAAQ,UAAW,WACpB,OACIC,SAAU5K,EACVrF,KAAM0K,EAAUgG,IAAI,SAAU1Q,GAC1B,OAASyH,SAAUzH,QAI/BL,EAAO+B,KAAK2D,GACZrI,KAAK2R,cAActJ,GACZA,GAEXsL,KAAM,SAAUvO,GACZpF,KAAK4T,QAAQxO,GAAG,IAEpByO,WAAY,SAAUzO,GAClBpF,KAAK4T,QAAQxO,GAAG,IAEpBwO,QAAS,SAAUxO,EAAG0O,GAClB,KAAI1O,EAAE2O,MAAQ,GAAd,CAGA,GAAIC,GAAUnU,EAAEuF,EAAE6O,eAAgBzT,EAAOwT,EAAQ/S,SAAUiT,EAAOrT,EAAUmT,EAAS,SAAW,GAAIG,GAAaD,EAAK7T,MAAMiE,GAAe8P,EAAYhP,EAAEiP,oBACrJF,IACA3T,EAAK8T,YAAY/Q,EAAcuQ,IAAcM,KAGrDhD,WAAY,WACR,GAAI1N,GAAW1D,KAAK+E,QAAQrB,SAAUsO,EAAiBhS,KAAK+E,QAAQiN,eAAgBuC,EAAkB,wCAAyCC,KAAoBC,WACxJ/Q,KAAaW,IACpBmQ,EAAc9Q,SAAWA,EACzBA,EAAW,0BAEf1D,KAAK0D,SAAWL,EAAMjC,EAAMsC,SAAS,MAAQ6Q,EAAkB,IAAM7Q,EAAW,SAAU8Q,GAC1FC,EAAmB/Q,SAAW1D,KAAK0D,eACxBsO,KAAmB3N,IAC1BoQ,EAAmBC,gBAAkB1C,EACrCA,EAAiB,iCAErByC,EAAmBzC,eAAiB5Q,EAAMsC,SAASsO,GACnDhS,KAAK+S,cAAgB1P,EAAMI,EAAgBgR,IAE/CzD,OAAQ,SAAU5L,GACd,KAAIA,EAAEuP,MAAMZ,MAAQ,GAAK3O,EAAEuP,MAAMN,sBAAjC,CAGA,GAAI5J,GAAUjK,EAAO4E,EAAEwP,OAAQA,EAAS/U,EAAEuF,EAAEuP,MAAMC,QAASC,EAAgBD,EAAO1F,QAAQ9N,EAAMC,aAAa,SAAU,eAAgB,eAAgByT,EAAS1T,EAAM2T,eAAeF,EAAejS,GAAKoS,EAAKxU,EAAKmB,KAAKP,EAAMO,KAAK,OAC/NqT,KACAvK,EAAWzK,KAAKgH,WAAWiO,SAASD,IAEpChV,KAAKmJ,QAAQjF,GACT0Q,OAAQA,EACRpU,KAAMA,EACNiK,SAAUA,EACVqK,OAAQA,KAEZ1P,EAAEgK,mBAGV8F,aAAc,WACV,GAAIC,GAAYnV,KAAK+B,QAAQjB,UAC7BqU,GAAUrU,SAAS,MAAMY,SAAS,WAClCyT,EAAU7O,KAAK,WACX,GAAI8O,GAAKvV,EAAEG,MAAOqV,EAAcD,EAAG7T,WAAW+T,OAC9CF,GAAG1T,SAAS,sBACP2T,EAAYE,GAAG,OAAUF,EAAYE,GAAG,OAASjS,IAClD+R,EAAYpE,KAAKzN,MAI7B8N,OAAQ,WACJ,GAAIvM,GAAU/E,KAAK+E,QAASyQ,EAA2B,UAAjBzQ,EAAQqB,KAAkBrE,EAAU/B,KAAK+B,QAAS0T,EAA0B,UAAlB1Q,EAAQ+M,KACxG/P,GAAQL,SAAS,eAAe4S,YAAY,WAAYkB,GAASlB,YAAY,kBAAmBtU,KAAKqR,SAASiD,YAAY,gBAAiBkB,GAAWC,GAAOnB,YAAY,eAAgBkB,IAAYC,GAAOnB,YAAY,oBAAqBkB,GAAWC,GACnP1T,EAAQ2T,QAAQ,gBAAgB,IACjC3T,EAAQmN,QAAQ,eAAeoF,YAAY,kBAAmBmB,GAE9DD,GACAxV,KAAKkV,eAETlV,KAAKmJ,QAAQrF,IAEjB6N,cAAe,SAAUtJ,GACrBA,EAAM/B,KAAK,WACP,GAAoBqP,GAAhBnV,EAAOX,EAAEG,MAAc4V,GAAW,CACtCpV,GAAKM,WAAWwF,KAAK,WACjBqP,EAAQ9V,EAAEG,MACN2V,EAAMJ,GAAG,MACTvU,EAAgB2U,GAChBC,GAAW,GACJD,EAAMJ,GAAG,WAChB3T,EAAoB+T,GACpBC,GAAW,KAGdA,GACDhV,EAAYJ,QAK5BoC,EAAGiT,OAAOpT,IACZC,OAAOtB,MAAM0U,QACRpT,OAAOtB,OACE,kBAAVxB,SAAwBA,OAAOmW,IAAMnW,OAAS,SAAUoW,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.listview.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.listview', [\n        'kendo.data',\n        'kendo.userevents',\n        'kendo.mobile.button'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.listview',\n        name: 'ListView',\n        category: 'mobile',\n        description: 'The Kendo Mobile ListView widget is used to display flat or grouped list of items.',\n        depends: [\n            'data',\n            'userevents',\n            'mobile.button'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Node = window.Node, mobile = kendo.mobile, ui = mobile.ui, outerHeight = kendo._outerHeight, DataSource = kendo.data.DataSource, Widget = ui.DataBoundWidget, ITEM_SELECTOR = '.km-list > li, > li:not(.km-group-container)', HIGHLIGHT_SELECTOR = '.km-listview-link, .km-listview-label', ICON_SELECTOR = '[' + kendo.attr('icon') + ']', proxy = $.proxy, attrValue = kendo.attrValue, GROUP_CLASS = 'km-group-title', ACTIVE_CLASS = 'km-state-active', GROUP_WRAPPER = '<div class=\"' + GROUP_CLASS + '\"><div class=\"km-text\"></div></div>', GROUP_TEMPLATE = kendo.template('<li><div class=\"' + GROUP_CLASS + '\"><div class=\"km-text\">#= this.headerTemplate(data) #</div></div><ul>#= kendo.render(this.template, data.items)#</ul></li>'), WRAPPER = '<div class=\"km-listview-wrapper\" />', SEARCH_TEMPLATE = kendo.template('<form class=\"km-filter-form\"><div class=\"km-filter-wrap\"><input type=\"search\" placeholder=\"#=placeholder#\"/><a href=\"\\\\#\" class=\"km-filter-reset\" title=\"Clear\"><span class=\"km-icon km-clear\"></span><span class=\"km-text\">Clear</span></a></div></form>'), NS = '.kendoMobileListView', STYLED = 'styled', DATABOUND = 'dataBound', DATABINDING = 'dataBinding', ITEM_CHANGE = 'itemChange', CLICK = 'click', CHANGE = 'change', PROGRESS = 'progress', FUNCTION = 'function', whitespaceRegExp = /^\\s+$/, buttonRegExp = /button/;\n        function whitespace() {\n            return this.nodeType === Node.TEXT_NODE && this.nodeValue.match(whitespaceRegExp);\n        }\n        function addIcon(item, icon) {\n            if (icon && !item[0].querySelector('.km-icon')) {\n                item.prepend('<span class=\"km-icon km-' + icon + '\"/>');\n            }\n        }\n        function enhanceItem(item) {\n            addIcon(item, attrValue(item, 'icon'));\n            addIcon(item, attrValue(item.children(ICON_SELECTOR), 'icon'));\n        }\n        function enhanceLinkItem(item) {\n            var parent = item.parent(), itemAndDetailButtons = item.add(parent.children(kendo.roleSelector('detailbutton'))), otherNodes = parent.contents().not(itemAndDetailButtons).not(whitespace);\n            if (otherNodes.length) {\n                return;\n            }\n            item.addClass('km-listview-link').attr(kendo.attr('role'), 'listview-link');\n            addIcon(item, attrValue(parent, 'icon'));\n            addIcon(item, attrValue(item, 'icon'));\n        }\n        function enhanceCheckBoxItem(label) {\n            if (!label[0].querySelector('input[type=checkbox],input[type=radio]')) {\n                return;\n            }\n            var item = label.parent();\n            if (item.contents().not(label).not(function () {\n                    return this.nodeType == 3;\n                })[0]) {\n                return;\n            }\n            label.addClass('km-listview-label');\n            label.children('[type=checkbox],[type=radio]').addClass('km-widget km-icon km-check');\n        }\n        function putAt(element, top) {\n            $(element).css('transform', 'translate3d(0px, ' + top + 'px, 0px)');\n        }\n        var HeaderFixer = kendo.Class.extend({\n            init: function (listView) {\n                var scroller = listView.scroller();\n                if (!scroller) {\n                    return;\n                }\n                this.options = listView.options;\n                this.element = listView.element;\n                this.scroller = listView.scroller();\n                this._shouldFixHeaders();\n                var headerFixer = this;\n                var cacheHeaders = function () {\n                    headerFixer._cacheHeaders();\n                };\n                listView.bind('resize', cacheHeaders);\n                listView.bind(STYLED, cacheHeaders);\n                listView.bind(DATABOUND, cacheHeaders);\n                this._scrollHandler = function (e) {\n                    headerFixer._fixHeader(e);\n                };\n                scroller.bind('scroll', this._scrollHandler);\n            },\n            destroy: function () {\n                var that = this;\n                if (that.scroller) {\n                    that.scroller.unbind('scroll', that._scrollHandler);\n                }\n            },\n            _fixHeader: function (e) {\n                if (!this.fixedHeaders) {\n                    return;\n                }\n                var i = 0, scroller = this.scroller, headers = this.headers, scrollTop = e.scrollTop, headerPair, offset, header;\n                do {\n                    headerPair = headers[i++];\n                    if (!headerPair) {\n                        header = $('<div />');\n                        break;\n                    }\n                    offset = headerPair.offset;\n                    header = headerPair.header;\n                } while (offset + 1 > scrollTop);\n                if (this.currentHeader != i) {\n                    scroller.fixedContainer.html(header.clone());\n                    this.currentHeader = i;\n                }\n            },\n            _shouldFixHeaders: function () {\n                this.fixedHeaders = this.options.type === 'group' && this.options.fixedHeaders;\n            },\n            _cacheHeaders: function () {\n                this._shouldFixHeaders();\n                if (!this.fixedHeaders) {\n                    return;\n                }\n                var headers = [], offset = this.scroller.scrollTop;\n                this.element.find('.' + GROUP_CLASS).each(function (_, header) {\n                    header = $(header);\n                    headers.unshift({\n                        offset: header.position().top + offset,\n                        header: header\n                    });\n                });\n                this.headers = headers;\n                this._fixHeader({ scrollTop: offset });\n            }\n        });\n        var DEFAULT_PULL_PARAMETERS = function () {\n            return { page: 1 };\n        };\n        var RefreshHandler = kendo.Class.extend({\n            init: function (listView) {\n                var handler = this, options = listView.options, scroller = listView.scroller(), pullParameters = options.pullParameters || DEFAULT_PULL_PARAMETERS;\n                this.listView = listView;\n                this.scroller = scroller;\n                listView.bind('_dataSource', function (e) {\n                    handler.setDataSource(e.dataSource);\n                });\n                scroller.setOptions({\n                    pullToRefresh: true,\n                    pull: function () {\n                        if (!handler._pulled) {\n                            handler._pulled = true;\n                            handler.dataSource.read(pullParameters.call(listView, handler._first));\n                        }\n                    },\n                    messages: {\n                        pullTemplate: options.messages.pullTemplate,\n                        releaseTemplate: options.messages.releaseTemplate,\n                        refreshTemplate: options.messages.refreshTemplate\n                    }\n                });\n            },\n            setDataSource: function (dataSource) {\n                var handler = this;\n                this._first = dataSource.view()[0];\n                this.dataSource = dataSource;\n                dataSource.bind('change', function () {\n                    handler._change();\n                });\n                dataSource.bind('error', function () {\n                    handler._change();\n                });\n            },\n            _change: function () {\n                var scroller = this.scroller, dataSource = this.dataSource;\n                if (this._pulled) {\n                    scroller.pullHandled();\n                }\n                if (this._pulled || !this._first) {\n                    var view = dataSource.view();\n                    if (view[0]) {\n                        this._first = view[0];\n                    }\n                }\n                this._pulled = false;\n            }\n        });\n        var VirtualList = kendo.Observable.extend({\n            init: function (options) {\n                var list = this;\n                kendo.Observable.fn.init.call(list);\n                list.buffer = options.buffer;\n                list.height = options.height;\n                list.item = options.item;\n                list.items = [];\n                list.footer = options.footer;\n                list.buffer.bind('reset', function () {\n                    list.refresh();\n                });\n            },\n            refresh: function () {\n                var buffer = this.buffer, items = this.items, endReached = false;\n                while (items.length) {\n                    items.pop().destroy();\n                }\n                this.offset = buffer.offset;\n                var itemConstructor = this.item, prevItem, item;\n                for (var idx = 0; idx < buffer.viewSize; idx++) {\n                    if (idx === buffer.total()) {\n                        endReached = true;\n                        break;\n                    }\n                    item = itemConstructor(this.content(this.offset + items.length));\n                    item.below(prevItem);\n                    prevItem = item;\n                    items.push(item);\n                }\n                this.itemCount = items.length;\n                this.trigger('reset');\n                this._resize();\n                if (endReached) {\n                    this.trigger('endReached');\n                }\n            },\n            totalHeight: function () {\n                if (!this.items[0]) {\n                    return 0;\n                }\n                var list = this, items = list.items, top = items[0].top, bottom = items[items.length - 1].bottom, averageItemHeight = (bottom - top) / list.itemCount, remainingItemsCount = list.buffer.length - list.offset - list.itemCount;\n                return (this.footer ? this.footer.height : 0) + bottom + remainingItemsCount * averageItemHeight;\n            },\n            batchUpdate: function (top) {\n                var height = this.height(), items = this.items, item, initialOffset = this.offset;\n                if (!items[0]) {\n                    return;\n                }\n                if (this.lastDirection) {\n                    while (items[items.length - 1].bottom > top + height * 2) {\n                        if (this.offset === 0) {\n                            break;\n                        }\n                        this.offset--;\n                        item = items.pop();\n                        item.update(this.content(this.offset));\n                        item.above(items[0]);\n                        items.unshift(item);\n                    }\n                } else {\n                    while (items[0].top < top - height) {\n                        var nextIndex = this.offset + this.itemCount;\n                        if (nextIndex === this.buffer.total()) {\n                            this.trigger('endReached');\n                            break;\n                        }\n                        if (nextIndex === this.buffer.length) {\n                            break;\n                        }\n                        item = items.shift();\n                        item.update(this.content(this.offset + this.itemCount));\n                        item.below(items[items.length - 1]);\n                        items.push(item);\n                        this.offset++;\n                    }\n                }\n                if (initialOffset !== this.offset) {\n                    this._resize();\n                }\n            },\n            update: function (top) {\n                var list = this, items = this.items, item, firstItem, lastItem, height = this.height(), itemCount = this.itemCount, padding = height / 2, up = (this.lastTop || 0) > top, topBorder = top - padding, bottomBorder = top + height + padding;\n                if (!items[0]) {\n                    return;\n                }\n                this.lastTop = top;\n                this.lastDirection = up;\n                if (up) {\n                    if (items[0].top > topBorder && items[items.length - 1].bottom > bottomBorder + padding && this.offset > 0) {\n                        this.offset--;\n                        item = items.pop();\n                        firstItem = items[0];\n                        item.update(this.content(this.offset));\n                        items.unshift(item);\n                        item.above(firstItem);\n                        list._resize();\n                    }\n                } else {\n                    if (items[items.length - 1].bottom < bottomBorder && items[0].top < topBorder - padding) {\n                        var nextIndex = this.offset + itemCount;\n                        if (nextIndex === this.buffer.total()) {\n                            this.trigger('endReached');\n                        } else if (nextIndex !== this.buffer.length) {\n                            item = items.shift();\n                            lastItem = items[items.length - 1];\n                            items.push(item);\n                            item.update(this.content(this.offset + this.itemCount));\n                            list.offset++;\n                            item.below(lastItem);\n                            list._resize();\n                        }\n                    }\n                }\n            },\n            content: function (index) {\n                return this.buffer.at(index);\n            },\n            destroy: function () {\n                this.unbind();\n            },\n            _resize: function () {\n                var items = this.items, top = 0, bottom = 0, firstItem = items[0], lastItem = items[items.length - 1];\n                if (firstItem) {\n                    top = firstItem.top;\n                    bottom = lastItem.bottom;\n                }\n                this.trigger('resize', {\n                    top: top,\n                    bottom: bottom\n                });\n                if (this.footer) {\n                    this.footer.below(lastItem);\n                }\n            }\n        });\n        kendo.mobile.ui.VirtualList = VirtualList;\n        var VirtualListViewItem = kendo.Class.extend({\n            init: function (listView, dataItem) {\n                var element = listView.append([dataItem], true)[0], height = element.offsetHeight;\n                $.extend(this, {\n                    top: 0,\n                    element: element,\n                    listView: listView,\n                    height: height,\n                    bottom: height\n                });\n            },\n            update: function (dataItem) {\n                this.element = this.listView.setDataItem(this.element, dataItem);\n            },\n            above: function (item) {\n                if (item) {\n                    this.height = this.element.offsetHeight;\n                    this.top = item.top - this.height;\n                    this.bottom = item.top;\n                    putAt(this.element, this.top);\n                }\n            },\n            below: function (item) {\n                if (item) {\n                    this.height = this.element.offsetHeight;\n                    this.top = item.bottom;\n                    this.bottom = this.top + this.height;\n                    putAt(this.element, this.top);\n                }\n            },\n            destroy: function () {\n                kendo.destroy(this.element);\n                $(this.element).remove();\n            }\n        });\n        var LOAD_ICON = '<div><span class=\"km-icon\"></span><span class=\"km-loading-left\"></span><span class=\"km-loading-right\"></span></div>';\n        var VirtualListViewLoadingIndicator = kendo.Class.extend({\n            init: function (listView) {\n                this.element = $('<li class=\"km-load-more km-scroller-refresh\" style=\"display: none\"></li>').appendTo(listView.element);\n                this._loadIcon = $(LOAD_ICON).appendTo(this.element);\n            },\n            enable: function () {\n                this.element.show();\n                this.height = outerHeight(this.element, true);\n            },\n            disable: function () {\n                this.element.hide();\n                this.height = 0;\n            },\n            below: function (item) {\n                if (item) {\n                    this.top = item.bottom;\n                    this.bottom = this.height + this.top;\n                    putAt(this.element, this.top);\n                }\n            }\n        });\n        var VirtualListViewPressToLoadMore = VirtualListViewLoadingIndicator.extend({\n            init: function (listView, buffer) {\n                this._loadIcon = $(LOAD_ICON).hide();\n                this._loadButton = $('<a class=\"km-load\">' + listView.options.messages.loadMoreText + '</a>').hide();\n                this.element = $('<li class=\"km-load-more\" style=\"display: none\"></li>').append(this._loadIcon).append(this._loadButton).appendTo(listView.element);\n                var loadMore = this;\n                this._loadButton.kendoMobileButton().data('kendoMobileButton').bind('click', function () {\n                    loadMore._hideShowButton();\n                    buffer.next();\n                });\n                buffer.bind('resize', function () {\n                    loadMore._showLoadButton();\n                });\n                this.height = outerHeight(this.element, true);\n                this.disable();\n            },\n            _hideShowButton: function () {\n                this._loadButton.hide();\n                this.element.addClass('km-scroller-refresh');\n                this._loadIcon.css('display', 'block');\n            },\n            _showLoadButton: function () {\n                this._loadButton.show();\n                this.element.removeClass('km-scroller-refresh');\n                this._loadIcon.hide();\n            }\n        });\n        var VirtualListViewItemBinder = kendo.Class.extend({\n            init: function (listView) {\n                var binder = this;\n                this.chromeHeight = outerHeight(listView.wrapper.children().not(listView.element));\n                this.listView = listView;\n                this.scroller = listView.scroller();\n                this.options = listView.options;\n                listView.bind('_dataSource', function (e) {\n                    binder.setDataSource(e.dataSource, e.empty);\n                });\n                listView.bind('resize', function () {\n                    if (!binder.list.items.length) {\n                        return;\n                    }\n                    binder.scroller.reset();\n                    binder.buffer.range(0);\n                    binder.list.refresh();\n                });\n                this.scroller.makeVirtual();\n                this._scroll = function (e) {\n                    binder.list.update(e.scrollTop);\n                };\n                this.scroller.bind('scroll', this._scroll);\n                this._scrollEnd = function (e) {\n                    binder.list.batchUpdate(e.scrollTop);\n                };\n                this.scroller.bind('scrollEnd', this._scrollEnd);\n            },\n            destroy: function () {\n                this.list.unbind();\n                this.buffer.unbind();\n                this.scroller.unbind('scroll', this._scroll);\n                this.scroller.unbind('scrollEnd', this._scrollEnd);\n            },\n            setDataSource: function (dataSource, empty) {\n                var binder = this, options = this.options, listView = this.listView, scroller = listView.scroller(), pressToLoadMore = options.loadMore, pageSize, buffer, footer;\n                this.dataSource = dataSource;\n                pageSize = dataSource.pageSize() || options.virtualViewSize;\n                if (!pageSize && !empty) {\n                    throw new Error('the DataSource does not have page size configured. Page Size setting is mandatory for the mobile listview virtual scrolling to work as expected.');\n                }\n                if (this.buffer) {\n                    this.buffer.destroy();\n                }\n                buffer = new kendo.data.Buffer(dataSource, Math.floor(pageSize / 2), pressToLoadMore);\n                if (pressToLoadMore) {\n                    footer = new VirtualListViewPressToLoadMore(listView, buffer);\n                } else {\n                    footer = new VirtualListViewLoadingIndicator(listView);\n                }\n                if (this.list) {\n                    this.list.destroy();\n                }\n                var list = new VirtualList({\n                    buffer: buffer,\n                    footer: footer,\n                    item: function (dataItem) {\n                        return new VirtualListViewItem(listView, dataItem);\n                    },\n                    height: function () {\n                        return scroller.height();\n                    }\n                });\n                list.bind('resize', function () {\n                    binder.updateScrollerSize();\n                    listView.updateSize();\n                });\n                list.bind('reset', function () {\n                    binder.footer.enable();\n                });\n                list.bind('endReached', function () {\n                    footer.disable();\n                    binder.updateScrollerSize();\n                });\n                buffer.bind('expand', function () {\n                    list.lastDirection = false;\n                    list.batchUpdate(scroller.scrollTop);\n                });\n                $.extend(this, {\n                    buffer: buffer,\n                    scroller: scroller,\n                    list: list,\n                    footer: footer\n                });\n            },\n            updateScrollerSize: function () {\n                this.scroller.virtualSize(0, this.list.totalHeight() + this.chromeHeight);\n            },\n            refresh: function () {\n                this.list.refresh();\n            },\n            reset: function () {\n                this.buffer.range(0);\n                this.list.refresh();\n            }\n        });\n        var ListViewItemBinder = kendo.Class.extend({\n            init: function (listView) {\n                var binder = this;\n                this.listView = listView;\n                this.options = listView.options;\n                var itemBinder = this;\n                this._refreshHandler = function (e) {\n                    itemBinder.refresh(e);\n                };\n                this._progressHandler = function () {\n                    listView.showLoading();\n                };\n                listView.bind('_dataSource', function (e) {\n                    binder.setDataSource(e.dataSource);\n                });\n            },\n            destroy: function () {\n                this._unbindDataSource();\n            },\n            reset: function () {\n            },\n            refresh: function (e) {\n                var action = e && e.action, dataItems = e && e.items, listView = this.listView, dataSource = this.dataSource, prependOnRefresh = this.options.appendOnRefresh, view = dataSource.view(), groups = dataSource.group(), groupedMode = groups && groups[0], item;\n                if (action === 'itemchange') {\n                    if (!listView._hasBindingTarget()) {\n                        item = listView.findByDataItem(dataItems)[0];\n                        if (item) {\n                            listView.setDataItem(item, dataItems[0]);\n                        }\n                    }\n                    return;\n                }\n                var removedItems, addedItems, addedDataItems;\n                var adding = action === 'add' && !groupedMode || prependOnRefresh && !listView._filter;\n                var removing = action === 'remove' && !groupedMode;\n                if (adding) {\n                    removedItems = [];\n                } else if (removing) {\n                    removedItems = listView.findByDataItem(dataItems);\n                }\n                if (listView.trigger(DATABINDING, {\n                        action: action || 'rebind',\n                        items: dataItems,\n                        removedItems: removedItems,\n                        index: e && e.index\n                    })) {\n                    if (this._shouldShowLoading()) {\n                        listView.hideLoading();\n                    }\n                    return;\n                }\n                if (action === 'add' && !groupedMode) {\n                    var index = view.indexOf(dataItems[0]);\n                    if (index > -1) {\n                        addedItems = listView.insertAt(dataItems, index);\n                        addedDataItems = dataItems;\n                    }\n                } else if (action === 'remove' && !groupedMode) {\n                    addedItems = [];\n                    listView.remove(dataItems);\n                } else if (groupedMode) {\n                    listView.replaceGrouped(view);\n                } else if (prependOnRefresh && !listView._filter) {\n                    addedItems = listView.prepend(view);\n                    addedDataItems = view;\n                } else {\n                    listView.replace(view);\n                }\n                if (this._shouldShowLoading()) {\n                    listView.hideLoading();\n                }\n                listView.trigger(DATABOUND, {\n                    ns: ui,\n                    addedItems: addedItems,\n                    addedDataItems: addedDataItems\n                });\n            },\n            setDataSource: function (dataSource) {\n                if (this.dataSource) {\n                    this._unbindDataSource();\n                }\n                this.dataSource = dataSource;\n                dataSource.bind(CHANGE, this._refreshHandler);\n                if (this._shouldShowLoading()) {\n                    this.dataSource.bind(PROGRESS, this._progressHandler);\n                }\n            },\n            _unbindDataSource: function () {\n                this.dataSource.unbind(CHANGE, this._refreshHandler).unbind(PROGRESS, this._progressHandler);\n            },\n            _shouldShowLoading: function () {\n                var options = this.options;\n                return !options.pullToRefresh && !options.loadMore && !options.endlessScroll;\n            }\n        });\n        var ListViewFilter = kendo.Class.extend({\n            init: function (listView) {\n                var filter = this, filterable = listView.options.filterable, events = 'change paste', that = this;\n                this.listView = listView;\n                this.options = filterable;\n                listView.element.before(SEARCH_TEMPLATE({ placeholder: filterable.placeholder || 'Search...' }));\n                if (filterable.autoFilter !== false) {\n                    events += ' keyup';\n                }\n                this.element = listView.wrapper.find('.km-search-form');\n                this.searchInput = listView.wrapper.find('input[type=search]').closest('form').on('submit' + NS, function (e) {\n                    e.preventDefault();\n                }).end().on('focus' + NS, function () {\n                    filter._oldFilter = filter.searchInput.val();\n                }).on(events.split(' ').join(NS + ' ') + NS, proxy(this._filterChange, this));\n                this.clearButton = listView.wrapper.find('.km-filter-reset').on(CLICK, proxy(this, '_clearFilter')).hide();\n                this._dataSourceChange = $.proxy(this._refreshInput, this);\n                listView.bind('_dataSource', function (e) {\n                    e.dataSource.bind('change', that._dataSourceChange);\n                });\n            },\n            _refreshInput: function () {\n                var appliedFilters = this.listView.dataSource.filter();\n                var searchInput = this.listView._filter.searchInput;\n                if (!appliedFilters || appliedFilters.filters[0].field !== this.listView.options.filterable.field) {\n                    searchInput.val('');\n                } else {\n                    searchInput.val(appliedFilters.filters[0].value);\n                }\n            },\n            _search: function (expr) {\n                this._filter = true;\n                this.clearButton[expr ? 'show' : 'hide']();\n                this.listView.dataSource.filter(expr);\n            },\n            _filterChange: function (e) {\n                var filter = this;\n                if (e.type == 'paste' && this.options.autoFilter !== false) {\n                    setTimeout(function () {\n                        filter._applyFilter();\n                    }, 1);\n                } else {\n                    this._applyFilter();\n                }\n            },\n            _applyFilter: function () {\n                var options = this.options, value = this.searchInput.val(), expr = value.length ? {\n                        field: options.field,\n                        operator: options.operator || 'startswith',\n                        ignoreCase: options.ignoreCase,\n                        value: value\n                    } : null;\n                if (value === this._oldFilter) {\n                    return;\n                }\n                this._oldFilter = value;\n                this._search(expr);\n            },\n            _clearFilter: function (e) {\n                this.searchInput.val('');\n                this._search(null);\n                e.preventDefault();\n            }\n        });\n        var ListView = Widget.extend({\n            init: function (element, options) {\n                var listView = this;\n                Widget.fn.init.call(this, element, options);\n                element = this.element;\n                options = this.options;\n                if (options.scrollTreshold) {\n                    options.scrollThreshold = options.scrollTreshold;\n                }\n                element.on('down', HIGHLIGHT_SELECTOR, '_highlight').on('move up cancel', HIGHLIGHT_SELECTOR, '_dim');\n                this._userEvents = new kendo.UserEvents(element, {\n                    fastTap: true,\n                    filter: ITEM_SELECTOR,\n                    allowSelection: true,\n                    tap: function (e) {\n                        listView._click(e);\n                    }\n                });\n                element.css('-ms-touch-action', 'auto');\n                element.wrap(WRAPPER);\n                this.wrapper = this.element.parent();\n                this._headerFixer = new HeaderFixer(this);\n                this._itemsCache = {};\n                this._templates();\n                this.virtual = options.endlessScroll || options.loadMore;\n                this._style();\n                if (this.options.$angular && (this.virtual || this.options.pullToRefresh)) {\n                    setTimeout($.proxy(this, '_start'));\n                } else {\n                    this._start();\n                }\n            },\n            _start: function () {\n                var options = this.options;\n                if (this.options.filterable) {\n                    this._filter = new ListViewFilter(this);\n                }\n                if (this.virtual) {\n                    this._itemBinder = new VirtualListViewItemBinder(this);\n                } else {\n                    this._itemBinder = new ListViewItemBinder(this);\n                }\n                if (this.options.pullToRefresh) {\n                    this._pullToRefreshHandler = new RefreshHandler(this);\n                }\n                this.setDataSource(options.dataSource);\n                this._enhanceItems(this.items());\n                kendo.notify(this, ui);\n            },\n            events: [\n                CLICK,\n                DATABINDING,\n                DATABOUND,\n                ITEM_CHANGE\n            ],\n            options: {\n                name: 'ListView',\n                style: '',\n                type: 'flat',\n                autoBind: true,\n                fixedHeaders: false,\n                template: '#:data#',\n                headerTemplate: '<span class=\"km-text\">#:value#</span>',\n                appendOnRefresh: false,\n                loadMore: false,\n                endlessScroll: false,\n                scrollThreshold: 30,\n                pullToRefresh: false,\n                messages: {\n                    loadMoreText: 'Press to load more',\n                    pullTemplate: 'Pull to refresh',\n                    releaseTemplate: 'Release to refresh',\n                    refreshTemplate: 'Refreshing'\n                },\n                pullOffset: 140,\n                filterable: false,\n                virtualViewSize: null\n            },\n            refresh: function () {\n                this._itemBinder.refresh();\n            },\n            reset: function () {\n                this._itemBinder.reset();\n            },\n            setDataSource: function (dataSource) {\n                var emptyDataSource = !dataSource;\n                this.dataSource = DataSource.create(dataSource);\n                this.trigger('_dataSource', {\n                    dataSource: this.dataSource,\n                    empty: emptyDataSource\n                });\n                if (this.options.autoBind && !emptyDataSource) {\n                    this.items().remove();\n                    this.dataSource.fetch();\n                }\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                kendo.destroy(this.element);\n                this._userEvents.destroy();\n                if (this._itemBinder) {\n                    this._itemBinder.destroy();\n                }\n                if (this._headerFixer) {\n                    this._headerFixer.destroy();\n                }\n                this.element.unwrap();\n                delete this.element;\n                delete this.wrapper;\n                delete this._userEvents;\n            },\n            items: function () {\n                if (this.options.type === 'group') {\n                    return this.element.find('.km-list').children();\n                } else {\n                    return this.element.children().not('.km-load-more');\n                }\n            },\n            scroller: function () {\n                if (!this._scrollerInstance) {\n                    this._scrollerInstance = this.element.closest('.km-scroll-wrapper').data('kendoMobileScroller');\n                }\n                return this._scrollerInstance;\n            },\n            showLoading: function () {\n                var view = this.view();\n                if (view && view.loader) {\n                    view.loader.show();\n                }\n            },\n            hideLoading: function () {\n                var view = this.view();\n                if (view && view.loader) {\n                    view.loader.hide();\n                }\n            },\n            insertAt: function (dataItems, index, triggerChange) {\n                var listView = this;\n                return listView._renderItems(dataItems, function (items) {\n                    if (index === 0) {\n                        listView.element.prepend(items);\n                    } else if (index === -1) {\n                        listView.element.append(items);\n                    } else {\n                        listView.items().eq(index - 1).after(items);\n                    }\n                    if (triggerChange) {\n                        for (var i = 0; i < items.length; i++) {\n                            listView.trigger(ITEM_CHANGE, {\n                                item: items.eq(i),\n                                data: dataItems[i],\n                                ns: ui\n                            });\n                        }\n                    }\n                });\n            },\n            append: function (dataItems, triggerChange) {\n                return this.insertAt(dataItems, -1, triggerChange);\n            },\n            prepend: function (dataItems, triggerChange) {\n                return this.insertAt(dataItems, 0, triggerChange);\n            },\n            replace: function (dataItems) {\n                this.options.type = 'flat';\n                this._angularItems('cleanup');\n                kendo.destroy(this.element.children());\n                this.element.empty();\n                this._userEvents.cancel();\n                this._style();\n                return this.insertAt(dataItems, 0);\n            },\n            replaceGrouped: function (groups) {\n                this.options.type = 'group';\n                this._angularItems('cleanup');\n                this.element.empty();\n                var items = $(kendo.render(this.groupTemplate, groups));\n                this._enhanceItems(items.children('ul').children('li'));\n                this.element.append(items);\n                mobile.init(items);\n                this._style();\n                this._angularItems('compile');\n            },\n            remove: function (dataItems) {\n                var items = this.findByDataItem(dataItems);\n                this.angular('cleanup', function () {\n                    return { elements: items };\n                });\n                kendo.destroy(items);\n                items.remove();\n            },\n            findByDataItem: function (dataItems) {\n                var selectors = [];\n                for (var idx = 0, length = dataItems.length; idx < length; idx++) {\n                    selectors[idx] = '[data-' + kendo.ns + 'uid=' + dataItems[idx].uid + ']';\n                }\n                return this.element.find(selectors.join(','));\n            },\n            setDataItem: function (item, dataItem) {\n                var listView = this, replaceItem = function (items) {\n                        var newItem = $(items[0]);\n                        kendo.destroy(item);\n                        listView.angular('cleanup', function () {\n                            return { elements: [$(item)] };\n                        });\n                        $(item).replaceWith(newItem);\n                        listView.trigger(ITEM_CHANGE, {\n                            item: newItem,\n                            data: dataItem,\n                            ns: ui\n                        });\n                    };\n                return this._renderItems([dataItem], replaceItem)[0];\n            },\n            updateSize: function () {\n                this._size = this.getSize();\n            },\n            _renderItems: function (dataItems, callback) {\n                var items = $(kendo.render(this.template, dataItems));\n                callback(items);\n                this.angular('compile', function () {\n                    return {\n                        elements: items,\n                        data: dataItems.map(function (data) {\n                            return { dataItem: data };\n                        })\n                    };\n                });\n                mobile.init(items);\n                this._enhanceItems(items);\n                return items;\n            },\n            _dim: function (e) {\n                this._toggle(e, false);\n            },\n            _highlight: function (e) {\n                this._toggle(e, true);\n            },\n            _toggle: function (e, highlight) {\n                if (e.which > 1) {\n                    return;\n                }\n                var clicked = $(e.currentTarget), item = clicked.parent(), role = attrValue(clicked, 'role') || '', plainItem = !role.match(buttonRegExp), prevented = e.isDefaultPrevented();\n                if (plainItem) {\n                    item.toggleClass(ACTIVE_CLASS, highlight && !prevented);\n                }\n            },\n            _templates: function () {\n                var template = this.options.template, headerTemplate = this.options.headerTemplate, dataIDAttribute = ' data-uid=\"#=arguments[0].uid || \"\"#\"', templateProxy = {}, groupTemplateProxy = {};\n                if (typeof template === FUNCTION) {\n                    templateProxy.template = template;\n                    template = '#=this.template(data)#';\n                }\n                this.template = proxy(kendo.template('<li' + dataIDAttribute + '>' + template + '</li>'), templateProxy);\n                groupTemplateProxy.template = this.template;\n                if (typeof headerTemplate === FUNCTION) {\n                    groupTemplateProxy._headerTemplate = headerTemplate;\n                    headerTemplate = '#=this._headerTemplate(data)#';\n                }\n                groupTemplateProxy.headerTemplate = kendo.template(headerTemplate);\n                this.groupTemplate = proxy(GROUP_TEMPLATE, groupTemplateProxy);\n            },\n            _click: function (e) {\n                if (e.event.which > 1 || e.event.isDefaultPrevented()) {\n                    return;\n                }\n                var dataItem, item = e.target, target = $(e.event.target), buttonElement = target.closest(kendo.roleSelector('button', 'detailbutton', 'backbutton')), button = kendo.widgetInstance(buttonElement, ui), id = item.attr(kendo.attr('uid'));\n                if (id) {\n                    dataItem = this.dataSource.getByUid(id);\n                }\n                if (this.trigger(CLICK, {\n                        target: target,\n                        item: item,\n                        dataItem: dataItem,\n                        button: button\n                    })) {\n                    e.preventDefault();\n                }\n            },\n            _styleGroups: function () {\n                var rootItems = this.element.children();\n                rootItems.children('ul').addClass('km-list');\n                rootItems.each(function () {\n                    var li = $(this), groupHeader = li.contents().first();\n                    li.addClass('km-group-container');\n                    if (!groupHeader.is('ul') && !groupHeader.is('div.' + GROUP_CLASS)) {\n                        groupHeader.wrap(GROUP_WRAPPER);\n                    }\n                });\n            },\n            _style: function () {\n                var options = this.options, grouped = options.type === 'group', element = this.element, inset = options.style === 'inset';\n                element.addClass('km-listview').toggleClass('km-list', !grouped).toggleClass('km-virtual-list', this.virtual).toggleClass('km-listinset', !grouped && inset).toggleClass('km-listgroup', grouped && !inset).toggleClass('km-listgroupinset', grouped && inset);\n                if (!element.parents('.km-listview')[0]) {\n                    element.closest('.km-content').toggleClass('km-insetcontent', inset);\n                }\n                if (grouped) {\n                    this._styleGroups();\n                }\n                this.trigger(STYLED);\n            },\n            _enhanceItems: function (items) {\n                items.each(function () {\n                    var item = $(this), child, enhanced = false;\n                    item.children().each(function () {\n                        child = $(this);\n                        if (child.is('a')) {\n                            enhanceLinkItem(child);\n                            enhanced = true;\n                        } else if (child.is('label')) {\n                            enhanceCheckBoxItem(child);\n                            enhanced = true;\n                        }\n                    });\n                    if (!enhanced) {\n                        enhanceItem(item);\n                    }\n                });\n            }\n        });\n        ui.plugin(ListView);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}