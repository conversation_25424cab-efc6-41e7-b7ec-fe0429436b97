/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.core.min",["jquery"],e)}(function(){return function(e,t,n){function i(){}function r(e,t){if(t)return"'"+e.split("'").join("\\'").split('\\"').join('\\\\\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t")+"'";var n=e.charAt(0),i=e.substring(1);return"="===n?"+("+i+")+":":"===n?"+$kendoHtmlEncode("+i+")+":";"+e+";$kendoOutput+="}function o(e,t,n){return e+="",t=t||2,n=t-e.length,n?j[t].substring(0,n)+e:e}function a(e){var t=e.css(_e.support.transitions.css+"box-shadow")||e.css("box-shadow"),n=t?t.match(Ee)||[0,0,0,0,0]:[0,0,0,0,0],i=Se.max(+n[3],+(n[4]||0));return{left:-n[1]+i,right:+n[1]+i,bottom:+n[2]+i}}function s(n,i){var r,o,a,s,c,u,d=Ce.browser,h=_e._outerWidth,f=_e._outerHeight,p=n.parent(),g=h(t);return p.removeClass("k-animation-container-sm"),p.hasClass("k-animation-container")?l(n,i):(o=n[0].style.width,a=n[0].style.height,s=Me.test(o),c=Me.test(a),u=n.hasClass("k-tooltip")||n.is(".k-menu-horizontal.k-context-menu"),r=s||c,!s&&(!i||i&&o||u)&&(o=i?h(n)+1:h(n)),(!c&&(!i||i&&a)||n.is(".k-menu-horizontal.k-context-menu"))&&(a=f(n)),n.wrap(e("<div/>").addClass("k-animation-container").css({width:o,height:a})),p=n.parent(),r&&n.css({width:"100%",height:"100%",boxSizing:"border-box",mozBoxSizing:"border-box",webkitBoxSizing:"border-box"})),g<h(p)&&(p.addClass("k-animation-container-sm"),l(n,i)),d.msie&&Se.floor(d.version)<=7&&(n.css({zoom:1}),n.children(".k-menu").width(n.width())),p}function l(e,t){var n,i=_e._outerWidth,r=_e._outerHeight,o=e.parent(".k-animation-container"),a=o[0].style;o.is(":hidden")&&o.css({display:"",position:""}),n=Me.test(a.width)||Me.test(a.height),n||o.css({width:t?i(e)+1:i(e),height:r(e),boxSizing:"content-box",mozBoxSizing:"content-box",webkitBoxSizing:"content-box"})}function c(e){var t=1,n=arguments.length;for(t=1;t<n;t++)u(e,arguments[t]);return e}function u(e,t){var n,i,r,o,a,s=_e.data.ObservableArray,l=_e.data.LazyObservableArray,c=_e.data.DataSource,d=_e.data.HierarchicalDataSource;for(n in t)i=t[n],r=typeof i,o=r===Ae&&null!==i?i.constructor:null,o&&o!==Array&&o!==s&&o!==l&&o!==c&&o!==d&&o!==RegExp?i instanceof Date?e[n]=new Date(i.getTime()):z(i.clone)?e[n]=i.clone():(a=e[n],e[n]=typeof a===Ae?a||{}:{},u(e[n],i)):r!==Ve&&(e[n]=i);return e}function d(e,t,i){for(var r in t)if(t.hasOwnProperty(r)&&t[r].test(e))return r;return i!==n?i:e}function h(e){return e.replace(/([a-z][A-Z])/g,function(e){return e.charAt(0)+"-"+e.charAt(1).toLowerCase()})}function f(e){return e.replace(/\-(\w)/g,function(e,t){return t.toUpperCase()})}function p(t,n){var i,r={};return document.defaultView&&document.defaultView.getComputedStyle?(i=document.defaultView.getComputedStyle(t,""),n&&e.each(n,function(e,t){r[t]=i.getPropertyValue(t)})):(i=t.currentStyle,n&&e.each(n,function(e,t){r[t]=i[f(t)]})),_e.size(r)||(r=i),r}function g(e){if(e&&e.className&&"string"==typeof e.className&&e.className.indexOf("k-auto-scrollable")>-1)return!0;var t=p(e,["overflow"]).overflow;return"auto"==t||"scroll"==t}function m(t,i){var r,o=Ce.browser.webkit,a=Ce.browser.mozilla,s=t instanceof e?t[0]:t;if(t)return r=Ce.isRtl(t),i===n?r&&o?s.scrollWidth-s.clientWidth-s.scrollLeft:Math.abs(s.scrollLeft):(s.scrollLeft=r&&o?s.scrollWidth-s.clientWidth-i:r&&a?-i:i,n)}function v(e){var t,n=0;for(t in e)e.hasOwnProperty(t)&&"toJSON"!=t&&n++;return n}function _(e,n,i){var r,o,a;return n||(n="offset"),r=e[n](),o={top:r.top,right:r.right,bottom:r.bottom,left:r.left},Ce.browser.msie&&(Ce.pointers||Ce.msPointers)&&!i&&(a=Ce.isRtl(e)?1:-1,o.top-=t.pageYOffset-document.documentElement.scrollTop,o.left-=t.pageXOffset+a*document.documentElement.scrollLeft),o}function y(e){var t={};return be("string"==typeof e?e.split(" "):e,function(e){t[e]=this}),t}function b(e){return new _e.effects.Element(e)}function w(e,t,n,i){return typeof e===Oe&&(z(t)&&(i=t,t=400,n=!1),z(n)&&(i=n,n=!1),typeof t===He&&(n=t,t=400),e={effects:e,duration:t,reverse:n,complete:i}),ye({effects:{},duration:400,reverse:!1,init:xe,teardown:xe,hide:!1},e,{completeCallback:e.complete,complete:xe})}function k(t,n,i,r,o){for(var a,s=0,l=t.length;s<l;s++)a=e(t[s]),a.queue(function(){q.promise(a,w(n,i,r,o))});return t}function x(e,t,n,i){return t&&(t=t.split(" "),be(t,function(t,n){e.toggleClass(n,i)})),e}function S(e){return(""+e).replace(U,"&amp;").replace(G,"&lt;").replace(J,"&gt;").replace(Q,"&quot;").replace(Y,"&#39;")}function T(e,t){var i;return 0===t.indexOf("data")&&(t=t.substring(4),t=t.charAt(0).toLowerCase()+t.substring(1)),t=t.replace(oe,"-$1"),i=e.getAttribute("data-"+_e.ns+t),null===i?i=n:"null"===i?i=null:"true"===i?i=!0:"false"===i?i=!1:Pe.test(i)&&"mask"!=t?i=parseFloat(i):ie.test(i)&&!re.test(i)&&(i=Function("return ("+i+")")()),i}function C(t,i,r){var o,a,s={},l=t.getAttribute("data-"+_e.ns+"role");for(o in i)a=T(t,o),a!==n&&(ne.test(o)&&"drawer"!=l&&("string"==typeof a?e("#"+a).length?a=_e.template(e("#"+a).html()):r&&(a=_e.template(r[a])):a=t.getAttribute(o)),s[o]=a);return s}function M(t,n){return e.contains(t,n)?-1:1}function D(){var t=e(this);return e.inArray(t.attr("data-"+_e.ns+"role"),["slider","rangeslider"])>-1||t.is(":visible")}function E(e,t){var n=e.nodeName.toLowerCase();return(/input|select|textarea|button|object/.test(n)?!e.disabled:"a"===n?e.href||t:t)&&P(e)}function P(t){return e.expr.pseudos.visible(t)&&!e(t).parents().addBack().filter(function(){return"hidden"===e.css(this,"visibility")}).length}function F(e,t){return new F.fn.init(e,t)}var O,z,A,I,H,V,R,N,B,$,L,j,W,q,U,G,Q,Y,J,X,Z,K,ee,te,ne,ie,re,oe,ae,se,le,ce,ue,de,he,fe,pe,ge,me,ve,_e=t.kendo=t.kendo||{cultures:{}},ye=e.extend,be=e.each,we=e.isArray,ke=e.proxy,xe=e.noop,Se=Math,Te=t.JSON||{},Ce={},Me=/%/,De=/\{(\d+)(:[^\}]+)?\}/g,Ee=/(\d+(?:\.?)\d*)px\s*(\d+(?:\.?)\d*)px\s*(\d+(?:\.?)\d*)px\s*(\d+)?/i,Pe=/^(\+|-?)\d+(\.?)\d*$/,Fe="function",Oe="string",ze="number",Ae="object",Ie="null",He="boolean",Ve="undefined",Re={},Ne={},Be=[].slice,$e=function(){var e,t,i,r,o,a,s=arguments[0]||{},l=1,c=arguments.length,u=!1;for("boolean"==typeof s&&(u=s,s=arguments[l]||{},l++),"object"==typeof s||jQuery.isFunction(s)||(s={}),l===c&&(s=this,l--);l<c;l++)if(null!=(o=arguments[l]))for(r in o)"filters"!=r&&"concat"!=r&&":"!=r&&(e=s[r],i=o[r],s!==i&&(u&&i&&(jQuery.isPlainObject(i)||(t=jQuery.isArray(i)))?(t?(t=!1,a=e&&jQuery.isArray(e)?e:[]):a=e&&jQuery.isPlainObject(e)?e:{},s[r]=$e(u,a,i)):i!==n&&(s[r]=i)));return s};_e.version="2019.2.619".replace(/^\s+|\s+$/g,""),i.extend=function(e){var t,n,i=function(){},r=this,o=e&&e.init?e.init:function(){r.apply(this,arguments)};i.prototype=r.prototype,n=o.fn=o.prototype=new i;for(t in e)n[t]=null!=e[t]&&e[t].constructor===Object?ye(!0,{},i.prototype[t],e[t]):e[t];return n.constructor=o,o.extend=r.extend,o},i.prototype._initOptions=function(e){this.options=c({},this.options,e)},z=_e.isFunction=function(e){return"function"==typeof e},A=function(){this._defaultPrevented=!0},I=function(){return this._defaultPrevented===!0},H=i.extend({init:function(){this._events={}},bind:function(e,t,i){var r,o,a,s,l,c=this,u=typeof e===Oe?[e]:e,d=typeof t===Fe;if(t===n){for(r in e)c.bind(r,e[r]);return c}for(r=0,o=u.length;r<o;r++)e=u[r],s=d?t:t[e],s&&(i&&(a=s,s=function(){c.unbind(e,s),a.apply(c,arguments)},s.original=a),l=c._events[e]=c._events[e]||[],l.push(s));return c},one:function(e,t){return this.bind(e,t,!0)},first:function(e,t){var n,i,r,o,a=this,s=typeof e===Oe?[e]:e,l=typeof t===Fe;for(n=0,i=s.length;n<i;n++)e=s[n],r=l?t:t[e],r&&(o=a._events[e]=a._events[e]||[],o.unshift(r));return a},trigger:function(e,t){var n,i,r=this,o=r._events[e];if(o){for(t=t||{},t.sender=r,t._defaultPrevented=!1,t.preventDefault=A,t.isDefaultPrevented=I,o=o.slice(),n=0,i=o.length;n<i;n++)o[n].call(r,t);return t._defaultPrevented===!0}return!1},unbind:function(e,t){var i,r=this,o=r._events[e];if(e===n)r._events={};else if(o)if(t)for(i=o.length-1;i>=0;i--)o[i]!==t&&o[i].original!==t||o.splice(i,1);else r._events[e]=[];return r}}),V=/^\w+/,R=/\$\{([^}]*)\}/g,N=/\\\}/g,B=/__CURLY__/g,$=/\\#/g,L=/__SHARP__/g,j=["","0","00","000","0000"],O={paramName:"data",useWithBlock:!0,render:function(e,t){var n,i,r="";for(n=0,i=t.length;n<i;n++)r+=e(t[n]);return r},compile:function(e,t){var n,i,o,a=ye({},this,t),s=a.paramName,l=s.match(V)[0],c=a.useWithBlock,u="var $kendoOutput, $kendoHtmlEncode = kendo.htmlEncode;";if(z(e))return e;for(u+=c?"with("+s+"){":"",u+="$kendoOutput=",i=e.replace(N,"__CURLY__").replace(R,"#=$kendoHtmlEncode($1)#").replace(B,"}").replace($,"__SHARP__").split("#"),o=0;o<i.length;o++)u+=r(i[o],o%2===0);u+=c?";}":";",u+="return $kendoOutput;",u=u.replace(L,"#");try{return n=Function(l,u),n._slotCount=Math.floor(i.length/2),n}catch(d){throw Error(_e.format("Invalid template:'{0}' Generated code:'{1}'",e,u))}}},function(){function e(e){return a.lastIndex=0,a.test(e)?'"'+e.replace(a,function(e){var t=s[e];return typeof t===Oe?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function t(o,a){var s,c,u,d,h,f,p=n,g=a[o];if(g&&typeof g===Ae&&typeof g.toJSON===Fe&&(g=g.toJSON(o)),typeof r===Fe&&(g=r.call(a,o,g)),f=typeof g,f===Oe)return e(g);if(f===ze)return isFinite(g)?g+"":Ie;if(f===He||f===Ie)return g+"";if(f===Ae){if(!g)return Ie;if(n+=i,h=[],"[object Array]"===l.apply(g)){for(d=g.length,s=0;s<d;s++)h[s]=t(s,g)||Ie;return u=0===h.length?"[]":n?"[\n"+n+h.join(",\n"+n)+"\n"+p+"]":"["+h.join(",")+"]",n=p,u}if(r&&typeof r===Ae)for(d=r.length,s=0;s<d;s++)typeof r[s]===Oe&&(c=r[s],u=t(c,g),u&&h.push(e(c)+(n?": ":":")+u));else for(c in g)Object.hasOwnProperty.call(g,c)&&(u=t(c,g),u&&h.push(e(c)+(n?": ":":")+u));return u=0===h.length?"{}":n?"{\n"+n+h.join(",\n"+n)+"\n"+p+"}":"{"+h.join(",")+"}",n=p,u}}var n,i,r,a=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,s={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},l={}.toString;typeof Date.prototype.toJSON!==Fe&&(Date.prototype.toJSON=function(){var e=this;return isFinite(e.valueOf())?o(e.getUTCFullYear(),4)+"-"+o(e.getUTCMonth()+1)+"-"+o(e.getUTCDate())+"T"+o(e.getUTCHours())+":"+o(e.getUTCMinutes())+":"+o(e.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(){return this.valueOf()}),typeof Te.stringify!==Fe&&(Te.stringify=function(e,o,a){var s;if(n="",i="",typeof a===ze)for(s=0;s<a;s+=1)i+=" ";else typeof a===Oe&&(i=a);if(r=o,o&&typeof o!==Fe&&(typeof o!==Ae||typeof o.length!==ze))throw Error("JSON.stringify");return t("",{"":e})})}(),function(){function t(e){if(e){if(e.numberFormat)return e;if(typeof e===Oe){var t=_e.cultures;return t[e]||t[e.split("-")[0]]||null}return null}return null}function i(e){return e&&(e=t(e)),e||_e.cultures.current}function r(e,t,r){r=i(r);var a=r.calendars.standard,s=a.days,l=a.months;return t=a.patterns[t]||t,t.replace(u,function(t){var i,r,c;return"d"===t?r=e.getDate():"dd"===t?r=o(e.getDate()):"ddd"===t?r=s.namesAbbr[e.getDay()]:"dddd"===t?r=s.names[e.getDay()]:"M"===t?r=e.getMonth()+1:"MM"===t?r=o(e.getMonth()+1):"MMM"===t?r=l.namesAbbr[e.getMonth()]:"MMMM"===t?r=l.names[e.getMonth()]:"yy"===t?r=o(e.getFullYear()%100):"yyyy"===t?r=o(e.getFullYear(),4):"h"===t?r=e.getHours()%12||12:"hh"===t?r=o(e.getHours()%12||12):"H"===t?r=e.getHours():"HH"===t?r=o(e.getHours()):"m"===t?r=e.getMinutes():"mm"===t?r=o(e.getMinutes()):"s"===t?r=e.getSeconds():"ss"===t?r=o(e.getSeconds()):"f"===t?r=Se.floor(e.getMilliseconds()/100):"ff"===t?(r=e.getMilliseconds(),r>99&&(r=Se.floor(r/10)),r=o(r)):"fff"===t?r=o(e.getMilliseconds(),3):"tt"===t?r=e.getHours()<12?a.AM[0]:a.PM[0]:"zzz"===t?(i=e.getTimezoneOffset(),c=i<0,r=(""+Se.abs(i/60)).split(".")[0],i=Se.abs(i)-60*r,r=(c?"+":"-")+o(r),r+=":"+o(i)):"zz"!==t&&"z"!==t||(r=e.getTimezoneOffset()/60,c=r<0,r=(""+Se.abs(r)).split(".")[0],r=(c?"+":"-")+("zz"===t?o(r):r)),r!==n?r:t.slice(1,t.length-1)})}function a(e,t,r){var o,a,c,u,b,w,k,x,S,T,C,M,D,E,P,F,O,z,A,I,H,V,R,N,B,$,L,j,W,q,U,G,Q,Y;if(r=i(r),o=r.numberFormat,a=o[g],c=o.decimals,u=o.pattern[0],b=[],C=e<0,F=p,O=p,U=-1,e===n)return p;if(!isFinite(e))return e;if(!t)return r.name.length?e.toLocaleString():""+e;if(T=d.exec(t)){if(t=T[1].toLowerCase(),k="c"===t,x="p"===t,(k||x)&&(o=k?o.currency:o.percent,a=o[g],c=o.decimals,w=o.symbol,u=o.pattern[C?0:1]),S=T[2],S&&(c=+S),"e"===t)return Q=S?e.toExponential(c):e.toExponential(),Q.replace(g,o[g]);if(x&&(e*=100),e=l(e,c),C=e<0,e=e.split(g),M=e[0],D=e[1],C&&(M=M.substring(1)),O=s(M,0,M.length,o),D&&(O+=a+D),"n"===t&&!C)return O;for(e=p,z=0,A=u.length;z<A;z++)I=u.charAt(z),e+="n"===I?O:"$"===I||"%"===I?w:I;return e}if((t.indexOf("'")>-1||t.indexOf('"')>-1||t.indexOf("\\")>-1)&&(t=t.replace(h,function(e){var t=e.charAt(0).replace("\\",""),n=e.slice(1).replace(t,"");return b.push(n),y})),t=t.split(";"),C&&t[1])t=t[1],V=!0;else if(0===e&&t[2]){if(t=t[2],t.indexOf(v)==-1&&t.indexOf(_)==-1)return t}else t=t[0];if(j=t.indexOf("%"),W=t.indexOf("$"),x=j!=-1,k=W!=-1,x&&(e*=100),k&&"\\"===t[W-1]&&(t=t.split("\\").join(""),k=!1),(k||x)&&(o=k?o.currency:o.percent,a=o[g],c=o.decimals,w=o.symbol),H=t.indexOf(m)>-1,H&&(t=t.replace(f,p)),R=t.indexOf(g),A=t.length,R!=-1)if(D=(""+e).split("e"),D=D[1]?l(e,Math.abs(D[1])):D[0],D=D.split(g)[1]||p,B=t.lastIndexOf(_)-R,N=t.lastIndexOf(v)-R,$=B>-1,L=N>-1,z=D.length,$||L||(t=t.substring(0,R)+t.substring(R+1),A=t.length,R=-1,z=0),$&&B>N)z=B;else if(N>B)if(L&&z>N){for(Y=l(e,N,C);Y.charAt(Y.length-1)===_&&N>0&&N>B;)N--,Y=l(e,N,C);z=N}else $&&z<B&&(z=B);if(e=l(e,z,C),N=t.indexOf(v),q=B=t.indexOf(_),U=N==-1&&B!=-1?B:N!=-1&&B==-1?N:N>B?B:N,N=t.lastIndexOf(v),B=t.lastIndexOf(_),G=N==-1&&B!=-1?B:N!=-1&&B==-1?N:N>B?N:B,U==A&&(G=U),U!=-1){for(O=(""+e).split(g),M=O[0],D=O[1]||p,E=M.length,P=D.length,C&&e*-1>=0&&(C=!1),e=t.substring(0,U),C&&!V&&(e+="-"),z=U;z<A;z++){if(I=t.charAt(z),R==-1){if(G-z<E){e+=M;break}}else if(B!=-1&&B<z&&(F=p),R-z<=E&&R-z>-1&&(e+=M,z=R),R===z){e+=(D?a:p)+D,z+=G-R+1;continue}I===_?(e+=I,F=I):I===v&&(e+=F)}if(H&&(e=s(e,U+(C&&!V?1:0),Math.max(G,E+U),o)),G>=U&&(e+=t.substring(G+1)),k||x){for(O=p,z=0,A=e.length;z<A;z++)I=e.charAt(z),O+="$"===I||"%"===I?w:I;e=O}if(A=b.length)for(z=0;z<A;z++)e=e.replace(y,b[z])}return e}var s,l,c,u=/dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|HH|H|hh|h|mm|m|fff|ff|f|tt|ss|s|zzz|zz|z|"[^"]*"|'[^']*'/g,d=/^(n|c|p|e)(\d*)$/i,h=/(\\.)|(['][^']*[']?)|(["][^"]*["]?)/g,f=/\,/g,p="",g=".",m=",",v="#",_="0",y="??",b="en-US",w={}.toString;_e.cultures["en-US"]={name:b,numberFormat:{pattern:["-n"],decimals:2,",":",",".":".",groupSize:[3],percent:{pattern:["-n %","n %"],decimals:2,",":",",".":".",groupSize:[3],symbol:"%"},currency:{name:"US Dollar",abbr:"USD",pattern:["($n)","$n"],decimals:2,",":",",".":".",groupSize:[3],symbol:"$"}},calendars:{standard:{days:{names:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],namesAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],namesShort:["Su","Mo","Tu","We","Th","Fr","Sa"]},months:{names:["January","February","March","April","May","June","July","August","September","October","November","December"],namesAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]},AM:["AM","am","AM"],PM:["PM","pm","PM"],patterns:{d:"M/d/yyyy",D:"dddd, MMMM dd, yyyy",F:"dddd, MMMM dd, yyyy h:mm:ss tt",g:"M/d/yyyy h:mm tt",G:"M/d/yyyy h:mm:ss tt",m:"MMMM dd",M:"MMMM dd",s:"yyyy'-'MM'-'ddTHH':'mm':'ss",t:"h:mm tt",T:"h:mm:ss tt",u:"yyyy'-'MM'-'dd HH':'mm':'ss'Z'",y:"MMMM, yyyy",Y:"MMMM, yyyy"},"/":"/",":":":",firstDay:0,twoDigitYearMax:2029}}},_e.culture=function(e){var i,r=_e.cultures;return e===n?r.current:(i=t(e)||r[b],i.calendar=i.calendars.standard,r.current=i,n)},_e.findCulture=t,_e.getCulture=i,_e.culture(b),s=function(e,t,i,r){var o,a,s,l,c,u,d=e.indexOf(r[g]),h=r.groupSize.slice(),f=h.shift();if(i=d!==-1?d:i+1,o=e.substring(t,i),a=o.length,a>=f){for(s=a,l=[];s>-1;)if(c=o.substring(s-f,s),c&&l.push(c),s-=f,u=h.shift(),f=u!==n?u:f,0===f){s>0&&l.push(o.substring(0,s));break}o=l.reverse().join(r[m]),e=e.substring(0,t)+o+e.substring(i)}return e},l=function(e,t,n){return t=t||0,e=(""+e).split("e"),e=Math.round(+(e[0]+"e"+(e[1]?+e[1]+t:t))),n&&(e=-e),e=(""+e).split("e"),e=+(e[0]+"e"+(e[1]?+e[1]-t:-t)),e.toFixed(Math.min(t,20))},c=function(e,t,i){if(t){if("[object Date]"===w.call(e))return r(e,t,i);if(typeof e===ze)return a(e,t,i)}return e!==n?e:""},_e.format=function(e){var t=arguments;return e.replace(De,function(e,n,i){var r=t[parseInt(n,10)+1];return c(r,i?i.substring(1):"")})},_e._extractFormat=function(e){return"{0:"===e.slice(0,3)&&(e=e.slice(3,e.length-1)),e},_e._activeElement=function(){try{return document.activeElement}catch(e){return document.documentElement.activeElement}},_e._round=l,_e._outerWidth=function(t,n){return e(t).outerWidth(n||!1)||0},_e._outerHeight=function(t,n){return e(t).outerHeight(n||!1)||0},_e.toString=c}(),function(){function t(e,t,n){return!(e>=t&&e<=n)}function i(e){return e.charAt(0)}function r(t){return e.map(t,i)}function o(e,t){t||23!==e.getHours()||e.setHours(e.getHours()+2)}function a(e){for(var t=0,n=e.length,i=[];t<n;t++)i[t]=(e[t]+"").toLowerCase();return i}function s(e){var t,n={};for(t in e)n[t]=a(e[t]);return n}function l(e,i,a,l){if(!e)return null;var c,u,d,h,f,m,v,_,y,w,k,x,S,T=function(e){for(var t=0;i[V]===e;)t++,V++;return t>0&&(V-=1),t},C=function(t){var n=b[t]||RegExp("^\\d{1,"+t+"}"),i=e.substr(R,t).match(n);return i?(i=i[0],R+=i.length,parseInt(i,10)):null},M=function(t,n){for(var i,r,o,a=0,s=t.length,l=0,c=0;a<s;a++)i=t[a],r=i.length,o=e.substr(R,r),n&&(o=o.toLowerCase()),o==i&&r>l&&(l=r,c=a);return l?(R+=l,c+1):null},D=function(){var t=!1;return e.charAt(R)===i[V]&&(R++,t=!0),t},E=a.calendars.standard,P=null,F=null,O=null,z=null,A=null,I=null,H=null,V=0,R=0,N=!1,B=new Date,$=E.twoDigitYearMax||2029,L=B.getFullYear();for(i||(i="d"),h=E.patterns[i],h&&(i=h),i=i.split(""),d=i.length;V<d;V++)if(c=i[V],N)"'"===c?N=!1:D();else if("d"===c){if(u=T("d"),E._lowerDays||(E._lowerDays=s(E.days)),null!==O&&u>2)continue;if(O=u<3?C(2):M(E._lowerDays[3==u?"namesAbbr":"names"],!0),null===O||t(O,1,31))return null}else if("M"===c){if(u=T("M"),E._lowerMonths||(E._lowerMonths=s(E.months)),F=u<3?C(2):M(E._lowerMonths[3==u?"namesAbbr":"names"],!0),null===F||t(F,1,12))return null;F-=1}else if("y"===c){if(u=T("y"),P=C(u),null===P)return null;2==u&&("string"==typeof $&&($=L+parseInt($,10)),P=L-L%100+P,P>$&&(P-=100))}else if("h"===c){if(T("h"),z=C(2),12==z&&(z=0),null===z||t(z,0,11))return null}else if("H"===c){if(T("H"),z=C(2),null===z||t(z,0,23))return null}else if("m"===c){if(T("m"),A=C(2),null===A||t(A,0,59))return null}else if("s"===c){if(T("s"),I=C(2),null===I||t(I,0,59))return null}else if("f"===c){if(u=T("f"),S=e.substr(R,u).match(b[3]),H=C(u),null!==H&&(H=parseFloat("0."+S[0],10),H=_e._round(H,3),H*=1e3),null===H||t(H,0,999))return null}else if("t"===c){if(u=T("t"),_=E.AM,y=E.PM,1===u&&(_=r(_),y=r(y)),f=M(y),!f&&!M(_))return null}else if("z"===c){if(m=!0,u=T("z"),"Z"===e.substr(R,1)){D();continue}if(v=e.substr(R,6).match(u>2?g:p),!v)return null;if(v=v[0].split(":"),w=v[0],k=v[1],!k&&w.length>3&&(R=w.length-2,k=w.substring(R),w=w.substring(0,R)),w=parseInt(w,10),t(w,-12,13))return null;if(u>2&&(k=v[0][0]+k,k=parseInt(k,10),isNaN(k)||t(k,-59,59)))return null}else if("'"===c)N=!0,D();else if(!D())return null;return l&&!/^\s*$/.test(e.substr(R))?null:(x=null!==z||null!==A||I||null,null===P&&null===F&&null===O&&x?(P=L,F=B.getMonth(),O=B.getDate()):(null===P&&(P=L),null===O&&(O=1)),f&&z<12&&(z+=12),m?(w&&(z+=-w),k&&(A+=-k),e=new Date(Date.UTC(P,F,O,z,A,I,H))):(e=new Date(P,F,O,z,A,I,H),o(e,z)),P<100&&e.setFullYear(P),e.getDate()!==O&&m===n?null:e)}function c(e){var t="-"===e.substr(0,1)?-1:1;return e=e.substring(1),e=60*parseInt(e.substr(0,2),10)+parseInt(e.substring(2),10),t*e}function u(e){var t,n,i,r=Se.max(_.length,y.length),o=e.calendar||e.calendars.standard,a=o.patterns,s=[];for(i=0;i<r;i++){for(t=_[i],n=0;n<t.length;n++)s.push(a[t[n]]);s=s.concat(y[i])}return s}function d(e,t,n,i){var r,o,a,s;if("[object Date]"===w.call(e))return e;if(r=0,o=null,e&&0===e.indexOf("/D")&&(o=m.exec(e)))return o=o[1],s=v.exec(o.substring(1)),o=new Date(parseInt(o,10)),s&&(s=c(s[0]),o=_e.timezone.apply(o,0),o=_e.timezone.convert(o,0,-1*s)),o;for(n=_e.getCulture(n),t||(t=u(n)),t=we(t)?t:[t],a=t.length;r<a;r++)if(o=l(e,t[r],n,i))return o;return o}var h=/\u00A0/g,f=/[eE][\-+]?[0-9]+/,p=/[+|\-]\d{1,2}/,g=/[+|\-]\d{1,2}:?\d{2}/,m=/^\/Date\((.*?)\)\/$/,v=/[+-]\d*/,_=[[],["G","g","F"],["D","d","y","m","T","t"]],y=[["yyyy-MM-ddTHH:mm:ss.fffffffzzz","yyyy-MM-ddTHH:mm:ss.fffffff","yyyy-MM-ddTHH:mm:ss.fffzzz","yyyy-MM-ddTHH:mm:ss.fff","ddd MMM dd yyyy HH:mm:ss","yyyy-MM-ddTHH:mm:sszzz","yyyy-MM-ddTHH:mmzzz","yyyy-MM-ddTHH:mmzz","yyyy-MM-ddTHH:mm:ss","yyyy-MM-dd HH:mm:ss","yyyy/MM/dd HH:mm:ss"],["yyyy-MM-ddTHH:mm","yyyy-MM-dd HH:mm","yyyy/MM/dd HH:mm"],["yyyy/MM/dd","yyyy-MM-dd","HH:mm:ss","HH:mm"]],b={2:/^\d{1,2}/,3:/^\d{1,3}/,4:/^\d{4}/},w={}.toString;_e.parseDate=function(e,t,n){return d(e,t,n,!1)},_e.parseExactDate=function(e,t,n){return d(e,t,n,!0)},_e.parseInt=function(e,t){var n=_e.parseFloat(e,t);return n&&(n=0|n),n},_e.parseFloat=function(e,t,n){if(!e&&0!==e)return null;if(typeof e===ze)return e;e=""+e,t=_e.getCulture(t);var i,r,o=t.numberFormat,a=o.percent,s=o.currency,l=s.symbol,c=a.symbol,u=e.indexOf("-");return f.test(e)?(e=parseFloat(e.replace(o["."],".")),isNaN(e)&&(e=null),e):u>0?null:(u=u>-1,e.indexOf(l)>-1||n&&n.toLowerCase().indexOf("c")>-1?(o=s,i=o.pattern[0].replace("$",l).split("n"),e.indexOf(i[0])>-1&&e.indexOf(i[1])>-1&&(e=e.replace(i[0],"").replace(i[1],""),u=!0)):e.indexOf(c)>-1&&(r=!0,o=a,l=c),e=e.replace("-","").replace(l,"").replace(h," ").split(o[","].replace(h," ")).join("").replace(o["."],"."),e=parseFloat(e),isNaN(e)?e=null:u&&(e*=-1),e&&r&&(e/=100),e)}}(),function(){var i,r,o,a,s,l,c,u,h,f;Ce._scrollbar=n,Ce.scrollbar=function(e){if(isNaN(Ce._scrollbar)||e){var t,n=document.createElement("div");return n.style.cssText="overflow:scroll;overflow-x:hidden;zoom:1;clear:both;display:block",n.innerHTML="&nbsp;",document.body.appendChild(n),Ce._scrollbar=t=n.offsetWidth-n.scrollWidth,document.body.removeChild(n),t}return Ce._scrollbar},Ce.isRtl=function(t){return e(t).closest(".k-rtl").length>0},i=document.createElement("table");try{i.innerHTML="<tr><td></td></tr>",Ce.tbodyInnerHtml=!0}catch(p){Ce.tbodyInnerHtml=!1}Ce.touch="ontouchstart"in t,r=document.documentElement.style,o=Ce.transitions=!1,a=Ce.transforms=!1,s="HTMLElement"in t?HTMLElement.prototype:[],Ce.hasHW3D="WebKitCSSMatrix"in t&&"m11"in new t.WebKitCSSMatrix||"MozPerspective"in r||"msPerspective"in r,Ce.cssFlexbox="flexWrap"in r||"WebkitFlexWrap"in r||"msFlexWrap"in r,be(["Moz","webkit","O","ms"],function(){var e,t=""+this,n=typeof i.style[t+"Transition"]===Oe;if(n||typeof i.style[t+"Transform"]===Oe)return e=t.toLowerCase(),a={css:"ms"!=e?"-"+e+"-":"",prefix:t,event:"o"===e||"webkit"===e?e:""},n&&(o=a,o.event=o.event?o.event+"TransitionEnd":"transitionend"),!1}),i=null,Ce.transforms=a,Ce.transitions=o,Ce.devicePixelRatio=t.devicePixelRatio===n?1:t.devicePixelRatio;try{Ce.screenWidth=t.outerWidth||t.screen?t.screen.availWidth:t.innerWidth,Ce.screenHeight=t.outerHeight||t.screen?t.screen.availHeight:t.innerHeight}catch(p){Ce.screenWidth=t.screen.availWidth,Ce.screenHeight=t.screen.availHeight}Ce.detectOS=function(e){var n,i,r=!1,o=[],a=!/mobile safari/i.test(e),s={wp:/(Windows Phone(?: OS)?)\s(\d+)\.(\d+(\.\d+)?)/,fire:/(Silk)\/(\d+)\.(\d+(\.\d+)?)/,android:/(Android|Android.*(?:Opera|Firefox).*?\/)\s*(\d+)\.?(\d+(\.\d+)?)?/,iphone:/(iPhone|iPod).*OS\s+(\d+)[\._]([\d\._]+)/,ipad:/(iPad).*OS\s+(\d+)[\._]([\d_]+)/,meego:/(MeeGo).+NokiaBrowser\/(\d+)\.([\d\._]+)/,webos:/(webOS)\/(\d+)\.(\d+(\.\d+)?)/,blackberry:/(BlackBerry|BB10).*?Version\/(\d+)\.(\d+(\.\d+)?)/,playbook:/(PlayBook).*?Tablet\s*OS\s*(\d+)\.(\d+(\.\d+)?)/,windows:/(MSIE)\s+(\d+)\.(\d+(\.\d+)?)/,tizen:/(tizen).*?Version\/(\d+)\.(\d+(\.\d+)?)/i,sailfish:/(sailfish).*rv:(\d+)\.(\d+(\.\d+)?).*firefox/i,ffos:/(Mobile).*rv:(\d+)\.(\d+(\.\d+)?).*Firefox/},l={ios:/^i(phone|pad|pod)$/i,android:/^android|fire$/i,blackberry:/^blackberry|playbook/i,windows:/windows/,wp:/wp/,flat:/sailfish|ffos|tizen/i,meego:/meego/},c={tablet:/playbook|ipad|fire/i},u={omini:/Opera\sMini/i,omobile:/Opera\sMobi/i,firefox:/Firefox|Fennec/i,mobilesafari:/version\/.*safari/i,ie:/MSIE|Windows\sPhone/i,chrome:/chrome|crios/i,webkit:/webkit/i};for(i in s)if(s.hasOwnProperty(i)&&(o=e.match(s[i]))){if("windows"==i&&"plugins"in navigator)return!1;r={},r.device=i,r.tablet=d(i,c,!1),r.browser=d(e,u,"default"),r.name=d(i,l),r[r.name]=!0,r.majorVersion=o[2],r.minorVersion=(o[3]||"0").replace("_","."),n=r.minorVersion.replace(".","").substr(0,2),r.flatVersion=r.majorVersion+n+Array(3-(n.length<3?n.length:2)).join("0"),r.cordova=typeof t.PhoneGap!==Ve||typeof t.cordova!==Ve,r.appMode=t.navigator.standalone||/file|local|wmapp/.test(t.location.protocol)||r.cordova,r.android&&(Ce.devicePixelRatio<1.5&&r.flatVersion<400||a)&&(Ce.screenWidth>800||Ce.screenHeight>800)&&(r.tablet=i);break}return r},l=Ce.mobileOS=Ce.detectOS(navigator.userAgent),Ce.wpDevicePixelRatio=l.wp?screen.width/320:0,Ce.hasNativeScrolling=!1,(l.ios||l.android&&l.majorVersion>2||l.wp)&&(Ce.hasNativeScrolling=l),Ce.delayedClick=function(){if(Ce.touch){if(l.ios)return!0;if(l.android)return!Ce.browser.chrome||!(Ce.browser.version<32)&&!(e("meta[name=viewport]").attr("content")||"").match(/user-scalable=no/i)}return!1},Ce.mouseAndTouchPresent=Ce.touch&&!(Ce.mobileOS.ios||Ce.mobileOS.android),Ce.detectBrowser=function(e){var t,n=!1,i=[],r={edge:/(edge)[ \/]([\w.]+)/i,webkit:/(chrome|crios)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,msie:/(msie\s|trident.*? rv:)([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(t in r)if(r.hasOwnProperty(t)&&(i=e.match(r[t]))){n={},n[t]=!0,n[i[1].toLowerCase().split(" ")[0].split("/")[0]]=!0,n.version=parseInt(document.documentMode||i[2],10);break}return n},Ce.browser=Ce.detectBrowser(navigator.userAgent),Ce.detectClipboardAccess=function(){var e={copy:!!document.queryCommandSupported&&document.queryCommandSupported("copy"),cut:!!document.queryCommandSupported&&document.queryCommandSupported("cut"),paste:!!document.queryCommandSupported&&document.queryCommandSupported("paste")};return Ce.browser.chrome&&(e.paste=!1,Ce.browser.version>=43&&(e.copy=!0,e.cut=!0)),e},Ce.clipboard=Ce.detectClipboardAccess(),Ce.zoomLevel=function(){var e,n,i;try{return e=Ce.browser,n=0,i=document.documentElement,e.msie&&11==e.version&&i.scrollHeight>i.clientHeight&&!Ce.touch&&(n=Ce.scrollbar()),Ce.touch?i.clientWidth/t.innerWidth:e.msie&&e.version>=10?((top||t).document.documentElement.offsetWidth+n)/(top||t).innerWidth:1}catch(r){return 1}},Ce.cssBorderSpacing=n!==r.borderSpacing&&!(Ce.browser.msie&&Ce.browser.version<8),function(t){var n="",i=e(document.documentElement),r=parseInt(t.version,10);t.msie?n="ie":t.mozilla?n="ff":t.safari?n="safari":t.webkit?n="webkit":t.opera?n="opera":t.edge&&(n="edge"),n&&(n="k-"+n+" k-"+n+r),Ce.mobileOS&&(n+=" k-mobile"),Ce.cssFlexbox||(n+=" k-no-flexbox"),i.addClass(n)}(Ce.browser),Ce.eventCapture=document.documentElement.addEventListener,c=document.createElement("input"),Ce.placeholder="placeholder"in c,Ce.propertyChangeEvent="onpropertychange"in c,Ce.input=function(){for(var e,t=["number","date","time","month","week","datetime","datetime-local"],n=t.length,i="test",r={},o=0;o<n;o++)e=t[o],c.setAttribute("type",e),c.value=i,r[e.replace("-","")]="text"!==c.type&&c.value!==i;return r}(),c.style.cssText="float:left;",Ce.cssFloat=!!c.style.cssFloat,c=null,Ce.stableSort=function(){var e,t=513,n=[{index:0,field:"b"}];for(e=1;e<t;e++)n.push({index:e,field:"a"});return n.sort(function(e,t){return e.field>t.field?1:e.field<t.field?-1:0}),1===n[0].index}(),Ce.matchesSelector=s.webkitMatchesSelector||s.mozMatchesSelector||s.msMatchesSelector||s.oMatchesSelector||s.matchesSelector||s.matches||function(t){for(var n=document.querySelectorAll?(this.parentNode||document).querySelectorAll(t)||[]:e(t),i=n.length;i--;)if(n[i]==this)return!0;return!1},Ce.matchMedia="matchMedia"in t,Ce.pushState=t.history&&t.history.pushState,u=document.documentMode,Ce.hashChange="onhashchange"in t&&!(Ce.browser.msie&&(!u||u<=8)),Ce.customElements="registerElement"in t.document,h=Ce.browser.chrome,f=Ce.browser.mozilla,Ce.msPointers=!h&&t.MSPointerEvent,Ce.pointers=!h&&!f&&t.PointerEvent,Ce.kineticScrollNeeded=l&&(Ce.touch||Ce.msPointers||Ce.pointers)}(),W={left:{reverse:"right"},right:{reverse:"left"},down:{reverse:"up"},up:{reverse:"down"},top:{reverse:"bottom"},bottom:{reverse:"top"},"in":{reverse:"out"},out:{reverse:"in"}},q={},e.extend(q,{enabled:!0,Element:function(t){this.element=e(t)},promise:function(e,t){e.is(":visible")||e.css({display:e.data("olddisplay")||"block"}).css("display"),t.hide&&e.data("olddisplay",e.css("display")).hide(),t.init&&t.init(),t.completeCallback&&t.completeCallback(e),e.dequeue()},disable:function(){this.enabled=!1,this.promise=this.promiseShim},enable:function(){this.enabled=!0,this.promise=this.animatedPromise}}),q.promiseShim=q.promise,"kendoAnimate"in e.fn||ye(e.fn,{kendoStop:function(e,t){return this.stop(e,t)},kendoAnimate:function(e,t,n,i){return k(this,e,t,n,i)},kendoAddClass:function(e,t){return _e.toggleClass(this,e,t,!0)},kendoRemoveClass:function(e,t){return _e.toggleClass(this,e,t,!1)},kendoToggleClass:function(e,t,n){return _e.toggleClass(this,e,t,n)}}),U=/&/g,G=/</g,Q=/"/g,Y=/'/g,J=/>/g,X=function(e){return e.target},Ce.touch&&(X=function(e){var t="originalEvent"in e?e.originalEvent.changedTouches:"changedTouches"in e?e.changedTouches:null;return t?document.elementFromPoint(t[0].clientX,t[0].clientY):e.target},be(["swipe","swipeLeft","swipeRight","swipeUp","swipeDown","doubleTap","tap"],function(t,n){e.fn[n]=function(e){return this.bind(n,e)}})),Ce.touch?Ce.mobileOS?(Ce.mousedown="touchstart",Ce.mouseup="touchend",Ce.mousemove="touchmove",Ce.mousecancel="touchcancel",Ce.click="touchend",Ce.resize="orientationchange"):(Ce.mousedown="mousedown touchstart",Ce.mouseup="mouseup touchend",Ce.mousemove="mousemove touchmove",Ce.mousecancel="mouseleave touchcancel",Ce.click="click",Ce.resize="resize"):Ce.pointers?(Ce.mousemove="pointermove",Ce.mousedown="pointerdown",Ce.mouseup="pointerup",Ce.mousecancel="pointercancel",Ce.click="pointerup",Ce.resize="orientationchange resize"):Ce.msPointers?(Ce.mousemove="MSPointerMove",Ce.mousedown="MSPointerDown",Ce.mouseup="MSPointerUp",Ce.mousecancel="MSPointerCancel",Ce.click="MSPointerUp",Ce.resize="orientationchange resize"):(Ce.mousemove="mousemove",Ce.mousedown="mousedown",Ce.mouseup="mouseup",Ce.mousecancel="mouseleave",Ce.click="click",Ce.resize="resize"),Z=function(e,t){var n,i,r,o,a=t||"d",s=1;for(i=0,r=e.length;i<r;i++)o=e[i],""!==o&&(n=o.indexOf("["),0!==n&&(n==-1?o="."+o:(s++,o="."+o.substring(0,n)+" || {})"+o.substring(n))),s++,a+=o+(i<r-1?" || {})":")"));return Array(s).join("(")+a},K=/^([a-z]+:)?\/\//i,ye(_e,{widgets:[],_widgetRegisteredCallbacks:[],ui:_e.ui||{},fx:_e.fx||b,effects:_e.effects||q,mobile:_e.mobile||{},data:_e.data||{},dataviz:_e.dataviz||{},drawing:_e.drawing||{},spreadsheet:{messages:{}},keys:{INSERT:45,DELETE:46,BACKSPACE:8,TAB:9,ENTER:13,ESC:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,END:35,HOME:36,SPACEBAR:32,PAGEUP:33,PAGEDOWN:34,F2:113,F10:121,F12:123,NUMPAD_PLUS:107,NUMPAD_MINUS:109,NUMPAD_DOT:110},support:_e.support||Ce,animate:_e.animate||k,ns:"",attr:function(e){return"data-"+_e.ns+e},getShadows:a,wrap:s,deepExtend:c,getComputedStyles:p,isScrollable:g,scrollLeft:m,size:v,toCamelCase:f,toHyphens:h,getOffset:_e.getOffset||_,parseEffects:_e.parseEffects||y,toggleClass:_e.toggleClass||x,directions:_e.directions||W,Observable:H,Class:i,Template:O,template:ke(O.compile,O),render:ke(O.render,O),stringify:ke(Te.stringify,Te),eventTarget:X,htmlEncode:S,isLocalUrl:function(e){return e&&!K.test(e)},expr:function(e,t,n){return e=e||"",typeof t==Oe&&(n=t,t=!1),n=n||"d",e&&"["!==e.charAt(0)&&(e="."+e),t?(e=e.replace(/"([^.]*)\.([^"]*)"/g,'"$1_$DOT$_$2"'),
e=e.replace(/'([^.]*)\.([^']*)'/g,"'$1_$DOT$_$2'"),e=Z(e.split("."),n),e=e.replace(/_\$DOT\$_/g,".")):e=n+e,e},getter:function(e,t){var n=e+t;return Re[n]=Re[n]||Function("d","return "+_e.expr(e,t))},setter:function(e){return Ne[e]=Ne[e]||Function("d,value",_e.expr(e)+"=value")},accessor:function(e){return{get:_e.getter(e),set:_e.setter(e)}},guid:function(){var e,t,n="";for(e=0;e<32;e++)t=16*Se.random()|0,8!=e&&12!=e&&16!=e&&20!=e||(n+="-"),n+=(12==e?4:16==e?3&t|8:t).toString(16);return n},roleSelector:function(e){return e.replace(/(\S+)/g,"["+_e.attr("role")+"=$1],").slice(0,-1)},directiveSelector:function(e){var t,n=e.split(" ");if(n)for(t=0;t<n.length;t++)"view"!=n[t]&&(n[t]=n[t].replace(/(\w*)(view|bar|strip|over)$/,"$1-$2"));return n.join(" ").replace(/(\S+)/g,"kendo-mobile-$1,").slice(0,-1)},triggeredByInput:function(e){return/^(label|input|textarea|select)$/i.test(e.target.tagName)},onWidgetRegistered:function(e){for(var t=0,n=_e.widgets.length;t<n;t++)e(_e.widgets[t]);_e._widgetRegisteredCallbacks.push(e)},logToConsole:function(e,i){var r=t.console;!_e.suppressLog&&n!==r&&r.log&&r[i||"log"](e)}}),ee=H.extend({init:function(e,t){var n,i=this;i.element=_e.jQuery(e).handler(i),i.angular("init",t),H.fn.init.call(i),n=t?t.dataSource:null,n&&(t=ye({},t,{dataSource:{}})),t=i.options=ye(!0,{},i.options,t),n&&(t.dataSource=n),i.element.attr(_e.attr("role"))||i.element.attr(_e.attr("role"),(t.name||"").toLowerCase()),i.element.data("kendo"+t.prefix+t.name,i),i.bind(i.events,t)},events:[],options:{prefix:""},_hasBindingTarget:function(){return!!this.element[0].kendoBindingTarget},_tabindex:function(e){e=e||this.wrapper;var t=this.element,n="tabindex",i=e.attr(n)||t.attr(n);t.removeAttr(n),e.attr(n,isNaN(i)?0:i)},setOptions:function(t){this._setEvents(t),e.extend(this.options,t)},_setEvents:function(e){for(var t,n=this,i=0,r=n.events.length;i<r;i++)t=n.events[i],n.options[t]&&e[t]&&n.unbind(t,n.options[t]);n.bind(n.events,e)},resize:function(e){var t=this.getSize(),n=this._size;(e||(t.width>0||t.height>0)&&(!n||t.width!==n.width||t.height!==n.height))&&(this._size=t,this._resize(t,e),this.trigger("resize",t))},getSize:function(){return _e.dimensions(this.element)},size:function(e){return e?(this.setSize(e),n):this.getSize()},setSize:e.noop,_resize:e.noop,destroy:function(){var e=this;e.element.removeData("kendo"+e.options.prefix+e.options.name),e.element.removeData("handler"),e.unbind()},_destroy:function(){this.destroy()},angular:function(){},_muteAngularRebind:function(e){this._muteRebind=!0,e.call(this),this._muteRebind=!1}}),te=ee.extend({dataItems:function(){return this.dataSource.flatView()},_angularItems:function(t){var n=this;n.angular(t,function(){return{elements:n.items(),data:e.map(n.dataItems(),function(e){return{dataItem:e}})}})}}),_e.dimensions=function(e,t){var n=e[0];return t&&e.css(t),{width:n.offsetWidth,height:n.offsetHeight}},_e.notify=xe,ne=/template$/i,ie=/^\s*(?:\{(?:.|\r\n|\n)*\}|\[(?:.|\r\n|\n)*\])\s*$/,re=/^\{(\d+)(:[^\}]+)?\}|^\[[A-Za-z_]+\]$/,oe=/([A-Z])/g,_e.initWidget=function(i,r,o){var a,s,l,c,u,d,h,f,p,g,m,v,_;if(o?o.roles&&(o=o.roles):o=_e.ui.roles,i=i.nodeType?i:i[0],d=i.getAttribute("data-"+_e.ns+"role")){p=d.indexOf(".")===-1,l=p?o[d]:_e.getter(d)(t),m=e(i).data(),v=l?"kendo"+l.fn.options.prefix+l.fn.options.name:"",g=p?RegExp("^kendo.*"+d+"$","i"):RegExp("^"+v+"$","i");for(_ in m)if(_.match(g)){if(_!==v)return m[_];a=m[_]}if(l){for(f=T(i,"dataSource"),r=e.extend({},C(i,l.fn.options),r),f&&(r.dataSource=typeof f===Oe?_e.getter(f)(t):f),c=0,u=l.fn.events.length;c<u;c++)s=l.fn.events[c],h=T(i,s),h!==n&&(r[s]=_e.getter(h)(t));return a?e.isEmptyObject(r)||a.setOptions(r):a=new l(i,r),a}}},_e.rolesFromNamespaces=function(e){var t,n,i=[];for(e[0]||(e=[_e.ui,_e.dataviz.ui]),t=0,n=e.length;t<n;t++)i[t]=e[t].roles;return ye.apply(null,[{}].concat(i.reverse()))},_e.init=function(t){var n=_e.rolesFromNamespaces(Be.call(arguments,1));e(t).find("[data-"+_e.ns+"role]").addBack().each(function(){_e.initWidget(this,{},n)})},_e.destroy=function(t){e(t).find("[data-"+_e.ns+"role]").addBack().each(function(){var t,n=e(this).data();for(t in n)0===t.indexOf("kendo")&&typeof n[t].destroy===Fe&&n[t].destroy()})},_e.resize=function(t,n){var i,r=e(t).find("[data-"+_e.ns+"role]").addBack().filter(D);r.length&&(i=e.makeArray(r),i.sort(M),e.each(i,function(){var t=_e.widgetInstance(e(this));t&&t.resize(n)}))},_e.parseOptions=C,ye(_e.ui,{Widget:ee,DataBoundWidget:te,roles:{},progress:function(t,n,i){var r,o,a,s,l,c=t.find(".k-loading-mask"),u=_e.support,d=u.browser;i=e.extend({},{width:"100%",height:"100%",top:t.scrollTop(),opacity:!1},i),l=i.opacity?"k-loading-mask k-opaque":"k-loading-mask",n?c.length||(r=u.isRtl(t),o=r?"right":"left",s=t.scrollLeft(),a=d.webkit&&r?t[0].scrollWidth-t.width()-2*s:0,c=e(_e.format("<div class='{0}'><span class='k-loading-text'>{1}</span><div class='k-loading-image'/><div class='k-loading-color'/></div>",l,_e.ui.progress.messages.loading)).width(i.width).height(i.height).css("top",i.top).css(o,Math.abs(s)+a).prependTo(t)):c&&c.remove()},plugin:function(t,i,r){var o,a,s,l,c=t.fn.options.name;for(i=i||_e.ui,r=r||"",i[c]=t,i.roles[c.toLowerCase()]=t,o="getKendo"+r+c,c="kendo"+r+c,a={name:c,widget:t,prefix:r||""},_e.widgets.push(a),s=0,l=_e._widgetRegisteredCallbacks.length;s<l;s++)_e._widgetRegisteredCallbacks[s](a);e.fn[c]=function(i){var r,o=this;return typeof i===Oe?(r=Be.call(arguments,1),this.each(function(){var t,a,s=e.data(this,c);if(!s)throw Error(_e.format("Cannot call method '{0}' of {1} before it is initialized",i,c));if(t=s[i],typeof t!==Fe)throw Error(_e.format("Cannot find method '{0}' of {1}",i,c));if(a=t.apply(s,r),a!==n)return o=a,!1})):this.each(function(){return new t(this,i)}),o},e.fn[c].widget=t,e.fn[o]=function(){return this.data(c)}}}),_e.ui.progress.messages={loading:"Loading..."},ae={bind:function(){return this},nullObject:!0,options:{}},se=ee.extend({init:function(e,t){ee.fn.init.call(this,e,t),this.element.autoApplyNS(),this.wrapper=this.element,this.element.addClass("km-widget")},destroy:function(){ee.fn.destroy.call(this),this.element.kendoDestroy()},options:{prefix:"Mobile"},events:[],view:function(){var e=this.element.closest(_e.roleSelector("view splitview modalview drawer"));return _e.widgetInstance(e,_e.mobile.ui)||ae},viewHasNativeScrolling:function(){var e=this.view();return e&&e.options.useNativeScrolling},container:function(){var e=this.element.closest(_e.roleSelector("view layout modalview drawer splitview"));return _e.widgetInstance(e.eq(0),_e.mobile.ui)||ae}}),ye(_e.mobile,{init:function(e){_e.init(e,_e.mobile.ui,_e.ui,_e.dataviz.ui)},appLevelNativeScrolling:function(){return _e.mobile.application&&_e.mobile.application.options&&_e.mobile.application.options.useNativeScrolling},roles:{},ui:{Widget:se,DataBoundWidget:te.extend(se.prototype),roles:{},plugin:function(e){_e.ui.plugin(e,_e.mobile.ui,"Mobile")}}}),c(_e.dataviz,{init:function(e){_e.init(e,_e.dataviz.ui)},ui:{roles:{},themes:{},views:[],plugin:function(e){_e.ui.plugin(e,_e.dataviz.ui)}},roles:{}}),_e.touchScroller=function(t,n){return n||(n={}),n.useNative=!0,e(t).map(function(t,i){return i=e(i),!(!Ce.kineticScrollNeeded||!_e.mobile.ui.Scroller||i.data("kendoMobileScroller"))&&(i.kendoMobileScroller(n),i.data("kendoMobileScroller"))})[0]},_e.preventDefault=function(e){e.preventDefault()},_e.widgetInstance=function(e,n){var i,r,o,a,s,l=e.data(_e.ns+"role"),c=[];if(l){if("content"===l&&(l="scroller"),"editortoolbar"===l&&(o=e.data("kendoEditorToolbar")))return o;if("view"===l)return e.data("kendoView");if(n)if(n[0])for(i=0,r=n.length;i<r;i++)c.push(n[i].roles[l]);else c.push(n.roles[l]);else c=[_e.ui.roles[l],_e.dataviz.ui.roles[l],_e.mobile.ui.roles[l]];for(l.indexOf(".")>=0&&(c=[_e.getter(l)(t)]),i=0,r=c.length;i<r;i++)if(a=c[i],a&&(s=e.data("kendo"+a.fn.options.prefix+a.fn.options.name)))return s}},_e.onResize=function(n){var i=n;return Ce.mobileOS.android&&(i=function(){setTimeout(n,600)}),e(t).on(Ce.resize,i),i},_e.unbindResize=function(n){e(t).off(Ce.resize,n)},_e.attrValue=function(e,t){return e.data(_e.ns+t)},_e.days={Sunday:0,Monday:1,Tuesday:2,Wednesday:3,Thursday:4,Friday:5,Saturday:6},e.extend(e.expr.pseudos,{kendoFocusable:function(t){var n=e.attr(t,"tabindex");return E(t,!isNaN(n)&&n>-1)}}),le=["mousedown","mousemove","mouseenter","mouseleave","mouseover","mouseout","mouseup","click"],ce="label, input, [data-rel=external]",ue={setupMouseMute:function(){var t,n=0,i=le.length,r=document.documentElement;if(!ue.mouseTrap&&Ce.eventCapture)for(ue.mouseTrap=!0,ue.bustClick=!1,ue.captureMouse=!1,t=function(t){ue.captureMouse&&("click"===t.type?ue.bustClick&&!e(t.target).is(ce)&&(t.preventDefault(),t.stopPropagation()):t.stopPropagation())};n<i;n++)r.addEventListener(le[n],t,!0)},muteMouse:function(e){ue.captureMouse=!0,e.data.bustClick&&(ue.bustClick=!0),clearTimeout(ue.mouseTrapTimeoutID)},unMuteMouse:function(){clearTimeout(ue.mouseTrapTimeoutID),ue.mouseTrapTimeoutID=setTimeout(function(){ue.captureMouse=!1,ue.bustClick=!1},400)}},de={down:"touchstart mousedown",move:"mousemove touchmove",up:"mouseup touchend touchcancel",cancel:"mouseleave touchcancel"},Ce.touch&&(Ce.mobileOS.ios||Ce.mobileOS.android)?de={down:"touchstart",move:"touchmove",up:"touchend touchcancel",cancel:"touchcancel"}:Ce.pointers?de={down:"pointerdown",move:"pointermove",up:"pointerup",cancel:"pointercancel pointerleave"}:Ce.msPointers&&(de={down:"MSPointerDown",move:"MSPointerMove",up:"MSPointerUp",cancel:"MSPointerCancel MSPointerLeave"}),!Ce.msPointers||"onmspointerenter"in t||e.each({MSPointerEnter:"MSPointerOver",MSPointerLeave:"MSPointerOut"},function(t,n){e.event.special[t]={delegateType:n,bindType:n,handle:function(t){var i,r=this,o=t.relatedTarget,a=t.handleObj;return o&&(o===r||e.contains(r,o))||(t.type=a.origType,i=a.handler.apply(this,arguments),t.type=n),i}}}),he=function(e){return de[e]||e},fe=/([^ ]+)/g,_e.applyEventMap=function(e,t){return e=e.replace(fe,he),t&&(e=e.replace(fe,"$1."+t)),e},pe=e.fn.on,$e(!0,F,e),F.fn=F.prototype=new e,F.fn.constructor=F,F.fn.init=function(t,n){return n&&n instanceof e&&!(n instanceof F)&&(n=F(n)),e.fn.init.call(this,t,n,ge)},F.fn.init.prototype=F.fn,ge=F(document),ye(F.fn,{handler:function(e){return this.data("handler",e),this},autoApplyNS:function(e){return this.data("kendoNS",e||_e.guid()),this},on:function(){var e,t,n,i,r,o,a=this,s=a.data("kendoNS");return 1===arguments.length?pe.call(a,arguments[0]):(e=a,t=Be.call(arguments),typeof t[t.length-1]===Ve&&t.pop(),n=t[t.length-1],i=_e.applyEventMap(t[0],s),Ce.mouseAndTouchPresent&&i.search(/mouse|click/)>-1&&this[0]!==document.documentElement&&(ue.setupMouseMute(),r=2===t.length?null:t[1],o=i.indexOf("click")>-1&&i.indexOf("touchend")>-1,pe.call(this,{touchstart:ue.muteMouse,touchend:ue.unMuteMouse},r,{bustClick:o})),typeof n===Oe&&(e=a.data("handler"),n=e[n],t[t.length-1]=function(t){n.call(e,t)}),t[0]=i,pe.apply(a,t),a)},kendoDestroy:function(e){return e=e||this.data("kendoNS"),e&&this.off("."+e),this}}),_e.jQuery=F,_e.eventMap=de,_e.timezone=function(){function e(e,t){var n,i,r,o=t[3],a=t[4],s=t[5],l=t[8];return l||(t[8]=l={}),l[e]?l[e]:(isNaN(a)?0===a.indexOf("last")?(n=new Date(Date.UTC(e,u[o]+1,1,s[0]-24,s[1],s[2],0)),i=d[a.substr(4,3)],r=n.getUTCDay(),n.setUTCDate(n.getUTCDate()+i-r-(i>r?7:0))):a.indexOf(">=")>=0&&(n=new Date(Date.UTC(e,u[o],a.substr(5),s[0],s[1],s[2],0)),i=d[a.substr(0,3)],r=n.getUTCDay(),n.setUTCDate(n.getUTCDate()+i-r+(i<r?7:0))):n=new Date(Date.UTC(e,u[o],a,s[0],s[1],s[2],0)),l[e]=n)}function t(t,n,i){var r,o,a,s;return(n=n[i])?(a=new Date(t).getUTCFullYear(),n=jQuery.grep(n,function(e){var t=e[0],n=e[1];return t<=a&&(n>=a||t==a&&"only"==n||"max"==n)}),n.push(t),n.sort(function(t,n){return"number"!=typeof t&&(t=+e(a,t)),"number"!=typeof n&&(n=+e(a,n)),t-n}),s=n[jQuery.inArray(t,n)-1]||n[n.length-1],isNaN(s)?s:null):(r=i.split(":"),o=0,r.length>1&&(o=60*r[0]+ +r[1]),[-1e6,"max","-","Jan",1,[0,0,0],o,"-"])}function n(e,t,n){var i,r,o,a=t[n];if("string"==typeof a&&(a=t[a]),!a)throw Error('Timezone "'+n+'" is either incorrect, or kendo.timezones.min.js is not included.');for(i=a.length-1;i>=0&&(r=a[i][3],!(r&&e>r));i--);if(o=a[i+1],!o)throw Error('Timezone "'+n+'" not found on '+e+".");return o}function i(e,i,r,o){typeof e!=ze&&(e=Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));var a=n(e,i,o);return{zone:a,rule:t(e,r,a[1])}}function r(e,t){var n,r,o;return"Etc/UTC"==t||"Etc/GMT"==t?0:(n=i(e,this.zones,this.rules,t),r=n.zone,o=n.rule,_e.parseFloat(o?r[0]-o[6]:r[0]))}function o(e,t){var n=i(e,this.zones,this.rules,t),r=n.zone,o=n.rule,a=r[2];return a.indexOf("/")>=0?a.split("/")[o&&+o[6]?1:0]:a.indexOf("%s")>=0?a.replace("%s",o&&"-"!=o[7]?o[7]:""):a}function a(e,t,n){var i,r,o,a=n;return typeof t==Oe&&(t=this.offset(e,t)),typeof n==Oe&&(n=this.offset(e,n)),r=e.getTimezoneOffset(),e=new Date(e.getTime()+6e4*(t-n)),o=e.getTimezoneOffset(),typeof a==Oe&&(a=this.offset(e,a)),i=o-r+(n-a),new Date(e.getTime()+6e4*i)}function s(e,t){return this.convert(e,e.getTimezoneOffset(),t)}function l(e,t){return this.convert(e,t,e.getTimezoneOffset())}function c(e){return this.apply(new Date(e),"Etc/UTC")}var u={Jan:0,Feb:1,Mar:2,Apr:3,May:4,Jun:5,Jul:6,Aug:7,Sep:8,Oct:9,Nov:10,Dec:11},d={Sun:0,Mon:1,Tue:2,Wed:3,Thu:4,Fri:5,Sat:6};return{zones:{},rules:{},offset:r,convert:a,apply:s,remove:l,abbr:o,toLocalDate:c}}(),_e.date=function(){function e(e,t){return 0===t&&23===e.getHours()&&(e.setHours(e.getHours()+2),!0)}function t(t,n,i){var r=t.getHours();i=i||1,n=(n-t.getDay()+7*i)%7,t.setDate(t.getDate()+n),e(t,r)}function i(e,n,i){return e=new Date(e),t(e,n,i),e}function r(e){return new Date(e.getFullYear(),e.getMonth(),1)}function o(e){var t=new Date(e.getFullYear(),e.getMonth()+1,0),n=r(e),i=Math.abs(t.getTimezoneOffset()-n.getTimezoneOffset());return i&&t.setHours(n.getHours()+i/60),t}function a(e,t){return 1!==t?p(i(e,t,-1),4):p(e,4-(e.getDay()||7))}function s(e,t){var n=new Date(e.getFullYear(),0,1,(-6)),i=a(e,t),r=i.getTime()-n.getTime(),o=Math.floor(r/w);return 1+Math.floor(o/7)}function l(e,t){var i,r,o;return t===n&&(t=_e.culture().calendar.firstDay),i=p(e,-7),r=p(e,7),o=s(e,t),0===o?s(i,t)+1:53===o&&s(r,t)>1?1:o}function c(t){return t=new Date(t.getFullYear(),t.getMonth(),t.getDate(),0,0,0),e(t,0),t}function u(e){return Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds())}function d(e){return y(e).getTime()-c(y(e))}function h(e,t,n){var i,r=d(t),o=d(n);return!e||r==o||(t>=n&&(n+=w),i=d(e),r>i&&(i+=w),o<r&&(o+=w),i>=r&&i<=o)}function f(e,t,n){var i,r=t.getTime(),o=n.getTime();return r>=o&&(o+=w),i=e.getTime(),i>=r&&i<=o}function p(t,n){var i=t.getHours();return t=new Date(t),g(t,n*w),e(t,i),t}function g(e,t,n){var i,r=e.getTimezoneOffset();e.setTime(e.getTime()+t),n||(i=e.getTimezoneOffset()-r,e.setTime(e.getTime()+i*b))}function m(t,n){return t=new Date(_e.date.getDate(t).getTime()+_e.date.getMilliseconds(n)),e(t,n.getHours()),t}function v(){return c(new Date)}function _(e){return c(e).getTime()==v().getTime()}function y(e){var t=new Date(1980,1,1,0,0,0);return e&&t.setHours(e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()),t}var b=6e4,w=864e5;return{adjustDST:e,dayOfWeek:i,setDayOfWeek:t,getDate:c,isInDateRange:f,isInTimeRange:h,isToday:_,nextDay:function(e){return p(e,1)},previousDay:function(e){return p(e,-1)},toUtcTime:u,MS_PER_DAY:w,MS_PER_HOUR:60*b,MS_PER_MINUTE:b,setTime:g,setHours:m,addDays:p,today:v,toInvariantTime:y,firstDayOfMonth:r,lastDayOfMonth:o,weekInYear:l,getMilliseconds:d}}(),_e.stripWhitespace=function(e){var t,n,i;if(document.createNodeIterator)for(t=document.createNodeIterator(e,NodeFilter.SHOW_TEXT,function(t){return t.parentNode==e?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT},!1);t.nextNode();)t.referenceNode&&!t.referenceNode.textContent.trim()&&t.referenceNode.parentNode.removeChild(t.referenceNode);else for(n=0;n<e.childNodes.length;n++)i=e.childNodes[n],3!=i.nodeType||/\S/.test(i.nodeValue)||(e.removeChild(i),n--),1==i.nodeType&&_e.stripWhitespace(i)},me=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.oRequestAnimationFrame||t.msRequestAnimationFrame||function(e){setTimeout(e,1e3/60)},_e.animationFrame=function(e){me.call(t,e)},ve=[],_e.queueAnimation=function(e){ve[ve.length]=e,1===ve.length&&_e.runNextAnimation()},_e.runNextAnimation=function(){_e.animationFrame(function(){ve[0]&&(ve.shift()(),ve[0]&&_e.runNextAnimation())})},_e.parseQueryStringParams=function(e){for(var t=e.split("?")[1]||"",n={},i=t.split(/&|=/),r=i.length,o=0;o<r;o+=2)""!==i[o]&&(n[decodeURIComponent(i[o])]=decodeURIComponent(i[o+1]));return n},_e.elementUnderCursor=function(e){if(n!==e.x.client)return document.elementFromPoint(e.x.client,e.y.client)},_e.wheelDeltaY=function(e){var t,i=e.originalEvent,r=i.wheelDeltaY;return i.wheelDelta?(r===n||r)&&(t=i.wheelDelta):i.detail&&i.axis===i.VERTICAL_AXIS&&(t=10*-i.detail),t},_e.throttle=function(e,t){var i,r,o=0;return!t||t<=0?e:(r=function(){function r(){e.apply(a,l),o=+new Date}var a=this,s=+new Date-o,l=arguments;return o?(i&&clearTimeout(i),s>t?r():i=setTimeout(r,t-s),n):r()},r.cancel=function(){clearTimeout(i)},r)},_e.caret=function(t,i,r){var o,a,s,l,c,u=i!==n;if(r===n&&(r=i),t[0]&&(t=t[0]),!u||!t.disabled){try{t.selectionStart!==n?u?(t.focus(),a=Ce.mobileOS,a.wp||a.android?setTimeout(function(){t.setSelectionRange(i,r)},0):t.setSelectionRange(i,r)):i=[t.selectionStart,t.selectionEnd]:document.selection&&(e(t).is(":visible")&&t.focus(),o=t.createTextRange(),u?(o.collapse(!0),o.moveStart("character",i),o.moveEnd("character",r-i),o.select()):(s=o.duplicate(),o.moveToBookmark(document.selection.createRange().getBookmark()),s.setEndPoint("EndToStart",o),l=s.text.length,c=l+o.text.length,i=[l,c]))}catch(d){i=[]}return i}},_e.compileMobileDirective=function(e,n){var i=t.angular;return e.attr("data-"+_e.ns+"role",e[0].tagName.toLowerCase().replace("kendo-mobile-","").replace("-","")),i.element(e).injector().invoke(["$compile",function(t){t(e)(n),/^\$(digest|apply)$/.test(n.$$phase)||n.$digest()}]),_e.widgetInstance(e,_e.mobile.ui)},_e.antiForgeryTokens=function(){var t={},i=e("meta[name=csrf-token],meta[name=_csrf]").attr("content"),r=e("meta[name=csrf-param],meta[name=_csrf_header]").attr("content");return e("input[name^='__RequestVerificationToken']").each(function(){t[this.name]=this.value}),r!==n&&i!==n&&(t[r]=i),t},_e.cycleForm=function(e){function t(e){var t=_e.widgetInstance(e);t&&t.focus?t.focus():e.focus()}var n=e.find("input, .k-widget").first(),i=e.find("button, .k-button").last();i.on("keydown",function(e){e.keyCode!=_e.keys.TAB||e.shiftKey||(e.preventDefault(),t(n))}),n.on("keydown",function(e){e.keyCode==_e.keys.TAB&&e.shiftKey&&(e.preventDefault(),t(i))})},_e.focusElement=function(n){var i=[],r=n.parentsUntil("body").filter(function(e,t){var n=_e.getComputedStyles(t,["overflow"]);return"visible"!==n.overflow}).add(t);r.each(function(t,n){i[t]=e(n).scrollTop()});try{n[0].setActive()}catch(o){n[0].focus()}r.each(function(t,n){e(n).scrollTop(i[t])})},_e.matchesMedia=function(e){var n=_e._bootstrapToMedia(e)||e;return Ce.matchMedia&&t.matchMedia(n).matches},_e._bootstrapToMedia=function(e){return{xs:"(max-width: 576px)",sm:"(min-width: 576px)",md:"(min-width: 768px)",lg:"(min-width: 992px)",xl:"(min-width: 1200px)"}[e]},function(){function n(t,n,i,r){var o,a,s=e("<form>").attr({action:i,method:"POST",target:r}),l=_e.antiForgeryTokens();l.fileName=n,o=t.split(";base64,"),l.contentType=o[0].replace("data:",""),l.base64=o[1];for(a in l)l.hasOwnProperty(a)&&e("<input>").attr({value:l[a],name:a,type:"hidden"}).appendTo(s);s.appendTo("body").submit().remove()}function i(e,t){var n,i,r,o,a,s=e;if("string"==typeof e){for(n=e.split(";base64,"),i=n[0],r=atob(n[1]),o=new Uint8Array(r.length),a=0;a<r.length;a++)o[a]=r.charCodeAt(a);s=new Blob([o.buffer],{type:i})}navigator.msSaveBlob(s,t)}function r(e,n){t.Blob&&e instanceof Blob&&(e=URL.createObjectURL(e)),o.download=n,o.href=e;var i=document.createEvent("MouseEvents");i.initMouseEvent("click",!0,!1,t,0,0,0,0,0,!1,!1,!1,!1,0,null),o.dispatchEvent(i),setTimeout(function(){URL.revokeObjectURL(e)})}var o=document.createElement("a"),a="download"in o&&!_e.support.browser.edge;_e.saveAs=function(e){var t=n;e.forceProxy||(a?t=r:navigator.msSaveBlob&&(t=i)),t(e.dataURI,e.fileName,e.proxyURL,e.proxyTarget)}}(),_e.proxyModelSetters=function(e){var t={};return Object.keys(e||{}).forEach(function(n){Object.defineProperty(t,n,{get:function(){return e[n]},set:function(t){e[n]=t,e.dirty=!0}})}),t}}(jQuery,window),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.fx.min",["kendo.core.min"],e)}(function(){return function(e,t){function n(e){return parseInt(e,10)}function i(e,t){return n(e.css(t))}function r(e){var t,n=[];for(t in e)n.push(t);return n}function o(e){for(var t in e)L.indexOf(t)!=-1&&j.indexOf(t)==-1&&delete e[t];return e}function a(e,t){var n,i,r,o,a=[],s={};for(i in t)n=i.toLowerCase(),o=F&&L.indexOf(n)!=-1,!E.hasHW3D&&o&&j.indexOf(n)==-1?delete t[i]:(r=t[i],o?a.push(i+"("+r+")"):s[i]=r);return a.length&&(s[se]=a.join(" ")),s}function s(e,t){var i,r,o;return F?(i=e.css(se),i==Y?"scale"==t?1:0:(r=i.match(RegExp(t+"\\s*\\(([\\d\\w\\.]+)")),o=0,r?o=n(r[1]):(r=i.match(H)||[0,0,0,0,0],t=t.toLowerCase(),R.test(t)?o=parseFloat(r[3]/r[2]):"translatey"==t?o=parseFloat(r[4]/r[2]):"scale"==t?o=parseFloat(r[2]):"rotate"==t&&(o=parseFloat(Math.atan2(r[2],r[1])))),o)):parseFloat(e.css(t))}function l(e){return e.charAt(0).toUpperCase()+e.substring(1)}function c(e,t){var n=p.extend(t),i=n.prototype.directions;T[l(e)]=n,T.Element.prototype[e]=function(e,t,i,r){return new n(this.element,e,t,i,r)},C(i,function(t,i){T.Element.prototype[e+l(i)]=function(e,t,r){return new n(this.element,i,e,t,r)}})}function u(e,n,i,r){c(e,{directions:m,startValue:function(e){return this._startValue=e,this},endValue:function(e){return this._endValue=e,this},shouldHide:function(){return this._shouldHide},prepare:function(e,o){var a,s,l=this,c="out"===this._direction,u=l.element.data(n),d=!(isNaN(u)||u==i);a=d?u:t!==this._startValue?this._startValue:c?i:r,s=t!==this._endValue?this._endValue:c?r:i,this._reverse?(e[n]=s,o[n]=a):(e[n]=a,o[n]=s),l._shouldHide=o[n]===r}})}function d(e,t){var n=S.directions[t].vertical,i=e[n?Z:X]()/2+"px";return _[t].replace("$size",i)}var h,f,p,g,m,v,_,y,b,w,k,x,S=window.kendo,T=S.effects,C=e.each,M=e.extend,D=e.proxy,E=S.support,P=E.browser,F=E.transforms,O=E.transitions,z={scale:0,scalex:0,scaley:0,scale3d:0},A={translate:0,translatex:0,translatey:0,translate3d:0},I=t!==document.documentElement.style.zoom&&!F,H=/matrix3?d?\s*\(.*,\s*([\d\.\-]+)\w*?,\s*([\d\.\-]+)\w*?,\s*([\d\.\-]+)\w*?,\s*([\d\.\-]+)\w*?/i,V=/^(-?[\d\.\-]+)?[\w\s]*,?\s*(-?[\d\.\-]+)?[\w\s]*/i,R=/translatex?$/i,N=/(zoom|fade|expand)(\w+)/,B=/(zoom|fade|expand)/,$=/[xy]$/i,L=["perspective","rotate","rotatex","rotatey","rotatez","rotate3d","scale","scalex","scaley","scalez","scale3d","skew","skewx","skewy","translate","translatex","translatey","translatez","translate3d","matrix","matrix3d"],j=["rotate","scale","scalex","scaley","skew","skewx","skewy","translate","translatex","translatey","matrix"],W={rotate:"deg",scale:"",skew:"px",translate:"px"},q=F.css,U=Math.round,G="",Q="px",Y="none",J="auto",X="width",Z="height",K="hidden",ee="origin",te="abortId",ne="overflow",ie="translate",re="position",oe="completeCallback",ae=q+"transition",se=q+"transform",le=q+"backface-visibility",ce=q+"perspective",ue="1500px",de="perspective("+ue+")",he={left:{reverse:"right",property:"left",transition:"translatex",vertical:!1,modifier:-1},right:{reverse:"left",property:"left",transition:"translatex",vertical:!1,modifier:1},down:{reverse:"up",property:"top",transition:"translatey",vertical:!0,modifier:1},up:{reverse:"down",property:"top",transition:"translatey",vertical:!0,modifier:-1},top:{reverse:"bottom"},bottom:{reverse:"top"},"in":{reverse:"out",modifier:-1},out:{reverse:"in",modifier:1},vertical:{reverse:"vertical"},horizontal:{reverse:"horizontal"}};S.directions=he,M(e.fn,{kendoStop:function(e,t){return O?T.stopQueue(this,e||!1,t||!1):this.stop(e,t)}}),F&&!O&&(C(j,function(n,i){e.fn[i]=function(n){if(t===n)return s(this,i);var r=e(this)[0],o=i+"("+n+W[i.replace($,"")]+")";return r.style.cssText.indexOf(se)==-1?e(this).css(se,o):r.style.cssText=r.style.cssText.replace(RegExp(i+"\\(.*?\\)","i"),o),this},e.fx.step[i]=function(t){e(t.elem)[i](t.now)}}),h=e.fx.prototype.cur,e.fx.prototype.cur=function(){return j.indexOf(this.prop)!=-1?parseFloat(e(this.elem)[this.prop]()):h.apply(this,arguments)}),S.toggleClass=function(e,t,n,i){return t&&(t=t.split(" "),O&&(n=M({exclusive:"all",duration:400,ease:"ease-out"},n),e.css(ae,n.exclusive+" "+n.duration+"ms "+n.ease),setTimeout(function(){e.css(ae,"").css(Z)},n.duration)),C(t,function(t,n){e.toggleClass(n,i)})),e},S.parseEffects=function(e,t){var n={};return"string"==typeof e?C(e.split(" "),function(e,i){var r=!B.test(i),o=i.replace(N,function(e,t,n){return t+":"+n.toLowerCase()}),a=o.split(":"),s=a[1],l={};a.length>1&&(l.direction=t&&r?he[s].reverse:s),n[a[0]]=l}):C(e,function(e){var i=this.direction;i&&t&&!B.test(e)&&(this.direction=he[i].reverse),n[e]=this}),n},O&&M(T,{transition:function(t,n,i){var o,s,l,c,u=0,d=t.data("keys")||[];i=M({duration:200,ease:"ease-out",complete:null,exclusive:"all"},i),l=!1,c=function(){l||(l=!0,s&&(clearTimeout(s),s=null),t.removeData(te).dequeue().css(ae,"").css(ae),i.complete.call(t))},i.duration=e.fx?e.fx.speeds[i.duration]||i.duration:i.duration,o=a(t,n),e.merge(d,r(o)),e.hasOwnProperty("uniqueSort")?t.data("keys",e.uniqueSort(d)).height():t.data("keys",e.unique(d)).height(),t.css(ae,i.exclusive+" "+i.duration+"ms "+i.ease).css(ae),t.css(o).css(se),O.event&&(t.one(O.event,c),0!==i.duration&&(u=500)),s=setTimeout(c,i.duration+u),t.data(te,s),t.data(oe,c)},stopQueue:function(e,t,n){var i,r=e.data("keys"),o=!n&&r,a=e.data(oe);return o&&(i=S.getComputedStyles(e[0],r)),a&&a(),o&&e.css(i),e.removeData("keys").stop(t)}}),f=S.Class.extend({init:function(e,t){var n=this;n.element=e,n.effects=[],n.options=t,n.restore=[]},run:function(t){var n,i,r,s,l,c,u,d=this,h=t.length,f=d.element,p=d.options,g=e.Deferred(),m={},v={};for(d.effects=t,g.done(e.proxy(d,"complete")),f.data("animating",!0),i=0;i<h;i++)for(n=t[i],n.setReverse(p.reverse),n.setOptions(p),d.addRestoreProperties(n.restore),n.prepare(m,v),l=n.children(),r=0,c=l.length;r<c;r++)l[r].duration(p.duration).run();for(u in p.effects)M(v,p.effects[u].properties);for(f.is(":visible")||M(m,{display:f.data("olddisplay")||"block"}),F&&!p.reset&&(s=f.data("targetTransform"),s&&(m=M(s,m))),m=a(f,m),F&&!O&&(m=o(m)),f.css(m).css(se),i=0;i<h;i++)t[i].setup();return p.init&&p.init(),f.data("targetTransform",v),T.animate(f,v,M({},p,{complete:g.resolve})),g.promise()},stop:function(){e(this.element).kendoStop(!0,!0)},addRestoreProperties:function(e){for(var t,n=this.element,i=0,r=e.length;i<r;i++)t=e[i],this.restore.push(t),n.data(t)||n.data(t,n.css(t))},restoreCallback:function(){var e,t,n,i=this.element;for(e=0,t=this.restore.length;e<t;e++)n=this.restore[e],i.css(n,i.data(n))},complete:function(){var t=this,n=0,i=t.element,r=t.options,o=t.effects,a=o.length;for(i.removeData("animating").dequeue(),r.hide&&i.data("olddisplay",i.css("display")).hide(),this.restoreCallback(),I&&!F&&setTimeout(e.proxy(this,"restoreCallback"),0);n<a;n++)o[n].teardown();r.completeCallback&&r.completeCallback(i)}}),T.promise=function(e,t){var n,i,r,o=[],a=new f(e,t),s=S.parseEffects(t.effects);t.effects=s;for(r in s)n=T[l(r)],n&&(i=new n(e,s[r].direction),o.push(i));o[0]?a.run(o):(e.is(":visible")||e.css({display:e.data("olddisplay")||"block"}).css("display"),t.init&&t.init(),e.dequeue(),a.complete())},M(T,{animate:function(n,r,a){var s=a.transition!==!1;delete a.transition,O&&"transition"in T&&s?T.transition(n,r,a):F?n.animate(o(r),{queue:!1,show:!1,hide:!1,duration:a.duration,complete:a.complete}):n.each(function(){var n=e(this),o={};C(L,function(e,a){var s,l,c,u,d,h,f,p=r?r[a]+" ":null;p&&(l=r,a in z&&r[a]!==t?(s=p.match(V),F&&M(l,{scale:+s[0]})):a in A&&r[a]!==t&&(c=n.css(re),u="absolute"==c||"fixed"==c,n.data(ie)||(u?n.data(ie,{top:i(n,"top")||0,left:i(n,"left")||0,bottom:i(n,"bottom"),right:i(n,"right")}):n.data(ie,{top:i(n,"marginTop")||0,left:i(n,"marginLeft")||0})),d=n.data(ie),s=p.match(V),s&&(h=a==ie+"y"?0:+s[1],f=a==ie+"y"?+s[1]:+s[2],u?(isNaN(d.right)?isNaN(h)||M(l,{left:d.left+h}):isNaN(h)||M(l,{right:d.right-h}),isNaN(d.bottom)?isNaN(f)||M(l,{top:d.top+f}):isNaN(f)||M(l,{bottom:d.bottom-f})):(isNaN(h)||M(l,{marginLeft:d.left+h}),isNaN(f)||M(l,{marginTop:d.top+f})))),!F&&"scale"!=a&&a in l&&delete l[a],l&&M(o,l))}),P.msie&&delete o.scale,n.animate(o,{queue:!1,show:!1,hide:!1,duration:a.duration,complete:a.complete})})}}),T.animatedPromise=T.promise,p=S.Class.extend({init:function(e,t){var n=this;n.element=e,n._direction=t,n.options={},n._additionalEffects=[],n.restore||(n.restore=[])},reverse:function(){return this._reverse=!0,this.run()},play:function(){return this._reverse=!1,this.run()},add:function(e){return this._additionalEffects.push(e),this},direction:function(e){return this._direction=e,this},duration:function(e){return this._duration=e,this},compositeRun:function(){var e=this,t=new f(e.element,{reverse:e._reverse,duration:e._duration}),n=e._additionalEffects.concat([e]);return t.run(n)},run:function(){if(this._additionalEffects&&this._additionalEffects[0])return this.compositeRun();var t,n,i=this,r=i.element,s=0,l=i.restore,c=l.length,u=e.Deferred(),d={},h={},f=i.children(),p=f.length;for(u.done(e.proxy(i,"_complete")),r.data("animating",!0),s=0;s<c;s++)t=l[s],r.data(t)||r.data(t,r.css(t));for(s=0;s<p;s++)f[s].duration(i._duration).run();return i.prepare(d,h),r.is(":visible")||M(d,{display:r.data("olddisplay")||"block"}),F&&(n=r.data("targetTransform"),n&&(d=M(n,d))),d=a(r,d),F&&!O&&(d=o(d)),r.css(d).css(se),i.setup(),r.data("targetTransform",h),T.animate(r,h,{duration:i._duration,complete:u.resolve}),u.promise()},stop:function(){var t=0,n=this.children(),i=n.length;for(t=0;t<i;t++)n[t].stop();return e(this.element).kendoStop(!0,!0),this},restoreCallback:function(){var e,t,n,i=this.element;for(e=0,t=this.restore.length;e<t;e++)n=this.restore[e],i.css(n,i.data(n))},_complete:function(){var t=this,n=t.element;n.removeData("animating").dequeue(),t.restoreCallback(),t.shouldHide()&&n.data("olddisplay",n.css("display")).hide(),I&&!F&&setTimeout(e.proxy(t,"restoreCallback"),0),t.teardown()},setOptions:function(e){M(!0,this.options,e)},children:function(){return[]},shouldHide:e.noop,setup:e.noop,prepare:e.noop,teardown:e.noop,directions:[],setReverse:function(e){return this._reverse=e,this}}),g=["left","right","up","down"],m=["in","out"],c("slideIn",{directions:g,divisor:function(e){return this.options.divisor=e,this},prepare:function(e,t){var n,i=this,r=i.element,o=S._outerWidth,a=S._outerHeight,s=he[i._direction],l=-s.modifier*(s.vertical?a(r):o(r)),c=l/(i.options&&i.options.divisor||1)+Q,u="0px";i._reverse&&(n=e,e=t,t=n),F?(e[s.transition]=c,t[s.transition]=u):(e[s.property]=c,t[s.property]=u)}}),c("tile",{directions:g,init:function(e,t,n){p.prototype.init.call(this,e,t),this.options={previous:n}},previousDivisor:function(e){return this.options.previousDivisor=e,this},children:function(){var e=this,t=e._reverse,n=e.options.previous,i=e.options.previousDivisor||1,r=e._direction,o=[S.fx(e.element).slideIn(r).setReverse(t)];return n&&o.push(S.fx(n).slideIn(he[r].reverse).divisor(i).setReverse(!t)),o}}),u("fade","opacity",1,0),u("zoom","scale",1,.01),c("slideMargin",{prepare:function(e,t){var n,i=this,r=i.element,o=i.options,a=r.data(ee),s=o.offset,l=i._reverse;l||null!==a||r.data(ee,parseFloat(r.css("margin-"+o.axis))),n=r.data(ee)||0,t["margin-"+o.axis]=l?n:n+s}}),c("slideTo",{prepare:function(e,t){
var n=this,i=n.element,r=n.options,o=r.offset.split(","),a=n._reverse;F?(t.translatex=a?0:o[0],t.translatey=a?0:o[1]):(t.left=a?0:o[0],t.top=a?0:o[1]),i.css("left")}}),c("expand",{directions:["horizontal","vertical"],restore:[ne],prepare:function(e,n){var i=this,r=i.element,o=i.options,a=i._reverse,s="vertical"===i._direction?Z:X,l=r[0].style[s],c=r.data(s),u=parseFloat(c||l),d=U(r.css(s,J)[s]());e.overflow=K,u=o&&o.reset?d||u:u||d,n[s]=(a?0:u)+Q,e[s]=(a?u:0)+Q,c===t&&r.data(s,l)},shouldHide:function(){return this._reverse},teardown:function(){var e=this,t=e.element,n="vertical"===e._direction?Z:X,i=t.data(n);i!=J&&i!==G||setTimeout(function(){t.css(n,J).css(n)},0)}}),v={position:"absolute",marginLeft:0,marginTop:0,scale:1},c("transfer",{init:function(e,t){this.element=e,this.options={target:t},this.restore=[]},setup:function(){this.element.appendTo(document.body)},prepare:function(e,t){var n=this,i=n.element,r=T.box(i),o=T.box(n.options.target),a=s(i,"scale"),l=T.fillScale(o,r),c=T.transformOrigin(o,r);M(e,v),t.scale=1,i.css(se,"scale(1)").css(se),i.css(se,"scale("+a+")"),e.top=r.top,e.left=r.left,e.transformOrigin=c.x+Q+" "+c.y+Q,n._reverse?e.scale=l:t.scale=l}}),_={top:"rect(auto auto $size auto)",bottom:"rect($size auto auto auto)",left:"rect(auto $size auto auto)",right:"rect(auto auto auto $size)"},y={top:{start:"rotatex(0deg)",end:"rotatex(180deg)"},bottom:{start:"rotatex(-180deg)",end:"rotatex(0deg)"},left:{start:"rotatey(0deg)",end:"rotatey(-180deg)"},right:{start:"rotatey(180deg)",end:"rotatey(0deg)"}},c("turningPage",{directions:g,init:function(e,t,n){p.prototype.init.call(this,e,t),this._container=n},prepare:function(e,t){var n=this,i=n._reverse,r=i?he[n._direction].reverse:n._direction,o=y[r];e.zIndex=1,n._clipInHalf&&(e.clip=d(n._container,S.directions[r].reverse)),e[le]=K,t[se]=de+(i?o.start:o.end),e[se]=de+(i?o.end:o.start)},setup:function(){this._container.append(this.element)},face:function(e){return this._face=e,this},shouldHide:function(){var e=this,t=e._reverse,n=e._face;return t&&!n||!t&&n},clipInHalf:function(e){return this._clipInHalf=e,this},temporary:function(){return this.element.addClass("temp-page"),this}}),c("staticPage",{directions:g,init:function(e,t,n){p.prototype.init.call(this,e,t),this._container=n},restore:["clip"],prepare:function(e,t){var n=this,i=n._reverse?he[n._direction].reverse:n._direction;e.clip=d(n._container,i),e.opacity=.999,t.opacity=1},shouldHide:function(){var e=this,t=e._reverse,n=e._face;return t&&!n||!t&&n},face:function(e){return this._face=e,this}}),c("pageturn",{directions:["horizontal","vertical"],init:function(e,t,n,i){p.prototype.init.call(this,e,t),this.options={},this.options.face=n,this.options.back=i},children:function(){var e,t=this,n=t.options,i="horizontal"===t._direction?"left":"top",r=S.directions[i].reverse,o=t._reverse,a=n.face.clone(!0).removeAttr("id"),s=n.back.clone(!0).removeAttr("id"),l=t.element;return o&&(e=i,i=r,r=e),[S.fx(n.face).staticPage(i,l).face(!0).setReverse(o),S.fx(n.back).staticPage(r,l).setReverse(o),S.fx(a).turningPage(i,l).face(!0).clipInHalf(!0).temporary().setReverse(o),S.fx(s).turningPage(r,l).clipInHalf(!0).temporary().setReverse(o)]},prepare:function(e,t){e[ce]=ue,e.transformStyle="preserve-3d",e.opacity=.999,t.opacity=1},teardown:function(){this.element.find(".temp-page").remove()}}),c("flip",{directions:["horizontal","vertical"],init:function(e,t,n,i){p.prototype.init.call(this,e,t),this.options={},this.options.face=n,this.options.back=i},children:function(){var e,t=this,n=t.options,i="horizontal"===t._direction?"left":"top",r=S.directions[i].reverse,o=t._reverse,a=t.element;return o&&(e=i,i=r,r=e),[S.fx(n.face).turningPage(i,a).face(!0).setReverse(o),S.fx(n.back).turningPage(r,a).setReverse(o)]},prepare:function(e){e[ce]=ue,e.transformStyle="preserve-3d"}}),b=!E.mobileOS.android,w=".km-touch-scrollbar, .km-actionsheet-wrapper",c("replace",{_before:e.noop,_after:e.noop,init:function(t,n,i){p.prototype.init.call(this,t),this._previous=e(n),this._transitionClass=i},duration:function(){throw Error("The replace effect does not support duration setting; the effect duration may be customized through the transition class rule")},beforeTransition:function(e){return this._before=e,this},afterTransition:function(e){return this._after=e,this},_both:function(){return e().add(this._element).add(this._previous)},_containerClass:function(){var e=this._direction,t="k-fx k-fx-start k-fx-"+this._transitionClass;return e&&(t+=" k-fx-"+e),this._reverse&&(t+=" k-fx-reverse"),t},complete:function(t){if(!(!this.deferred||t&&e(t.target).is(w))){var n=this.container;n.removeClass("k-fx-end").removeClass(this._containerClass()).off(O.event,this.completeProxy),this._previous.hide().removeClass("k-fx-current"),this.element.removeClass("k-fx-next"),b&&n.css(ne,""),this.isAbsolute||this._both().css(re,""),this.deferred.resolve(),delete this.deferred}},run:function(){if(this._additionalEffects&&this._additionalEffects[0])return this.compositeRun();var t,n=this,i=n.element,r=n._previous,o=i.parents().filter(r.parents()).first(),a=n._both(),s=e.Deferred(),l=i.css(re);return o.length||(o=i.parent()),this.container=o,this.deferred=s,this.isAbsolute="absolute"==l,this.isAbsolute||a.css(re,"absolute"),b&&(t=o.css(ne),o.css(ne,"hidden")),O?(i.addClass("k-fx-hidden"),o.addClass(this._containerClass()),this.completeProxy=e.proxy(this,"complete"),o.on(O.event,this.completeProxy),S.animationFrame(function(){i.removeClass("k-fx-hidden").addClass("k-fx-next"),r.css("display","").addClass("k-fx-current"),n._before(r,i),S.animationFrame(function(){o.removeClass("k-fx-start").addClass("k-fx-end"),n._after(r,i)})})):this.complete(),s.promise()},stop:function(){this.complete()}}),k=S.Class.extend({init:function(){var e=this;e._tickProxy=D(e._tick,e),e._started=!1},tick:e.noop,done:e.noop,onEnd:e.noop,onCancel:e.noop,start:function(){this.enabled()&&(this.done()?this.onEnd():(this._started=!0,S.animationFrame(this._tickProxy)))},enabled:function(){return!0},cancel:function(){this._started=!1,this.onCancel()},_tick:function(){var e=this;e._started&&(e.tick(),e.done()?(e._started=!1,e.onEnd()):S.animationFrame(e._tickProxy))}}),x=k.extend({init:function(e){var t=this;M(t,e),k.fn.init.call(t)},done:function(){return this.timePassed()>=this.duration},timePassed:function(){return Math.min(this.duration,new Date-this.startDate)},moveTo:function(e){var t=this,n=t.movable;t.initial=n[t.axis],t.delta=e.location-t.initial,t.duration="number"==typeof e.duration?e.duration:300,t.tick=t._easeProxy(e.ease),t.startDate=new Date,t.start()},_easeProxy:function(e){var t=this;return function(){t.movable.moveAxis(t.axis,e(t.timePassed(),t.initial,t.delta,t.duration))}}}),M(x,{easeOutExpo:function(e,t,n,i){return e==i?t+n:n*(-Math.pow(2,-10*e/i)+1)+t},easeOutBack:function(e,t,n,i,r){return r=1.70158,n*((e=e/i-1)*e*((r+1)*e+r)+1)+t}}),T.Animation=k,T.Transition=x,T.createEffect=c,T.box=function(t){t=e(t);var n=t.offset();return n.width=S._outerWidth(t),n.height=S._outerHeight(t),n},T.transformOrigin=function(e,t){var n=(e.left-t.left)*t.width/(t.width-e.width),i=(e.top-t.top)*t.height/(t.height-e.height);return{x:isNaN(n)?0:n,y:isNaN(i)?0:i}},T.fillScale=function(e,t){return Math.min(e.width/t.width,e.height/t.height)},T.fitScale=function(e,t){return Math.max(e.width/t.width,e.height/t.height)}}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.data.odata.min",["kendo.core.min"],e)}(function(){return function(e,t){function n(i,r){var o,a,s,l,c,u,d,h,f=[],p=i.logic||"and",m=i.filters;for(o=0,a=m.length;o<a;o++)i=m[o],s=i.field,d=i.value,u=i.operator,i.filters?i=n(i,r):(h=i.ignoreCase,s=s.replace(/\./g,"/"),i=b[u],r&&(i=w[u]),"isnullorempty"===u?i=g.format("{0} {1} null or {0} {1} ''",s,i):"isnotnullorempty"===u?i=g.format("{0} {1} null and {0} {1} ''",s,i):"isnull"===u||"isnotnull"===u?i=g.format("{0} {1} null",s,i):"isempty"===u||"isnotempty"===u?i=g.format("{0} {1} ''",s,i):i&&d!==t&&(l=e.type(d),"string"===l?(c="'{1}'",d=d.replace(/'/g,"''"),h===!0&&(s="tolower("+s+")")):"date"===l?r?(c="{1:yyyy-MM-ddTHH:mm:ss+00:00}",d=g.timezone.apply(d,"Etc/UTC")):c="datetime'{1:yyyy-MM-ddTHH:mm:ss}'":c="{1}",i.length>3?"substringof"!==i?c="{0}({2},"+c+")":(c="{0}("+c+",{2})","doesnotcontain"===u&&(r?(c="{0}({2},'{1}') eq -1",i="indexof"):c+=" eq false")):c="{2} {0} "+c,i=g.format(c,i,d,s))),f.push(i);return i=f.join(" "+p+" "),f.length>1&&(i="("+i+")"),i}function i(e){for(var t in e)0===t.indexOf("@odata")&&delete e[t]}function r(){return Math.floor(65536*(1+Math.random())).toString(16).substr(1)}function o(e){return e+r()+"-"+r()+"-"+r()}function a(e,t){var n=v+"--"+e;return t&&(n+="--"),n}function s(e,t,n,i){var r=e.options[i].url,o=g.format("{0} ",n);return y(r)?o+r(t):o+r}function l(e,t){var n="";return n+=a(e,!1),n+=v+"Content-Type: application/http",n+=v+"Content-Transfer-Encoding: binary",n+=v+"Content-ID: "+t}function c(e){var t="";return t+=v+"Content-Type: application/json;odata=minimalmetadata",t+=v+"Prefer: return=representation",t+=_+g.stringify(e)}function u(e,t,n,i,r,o){var a,u="";for(a=0;a<e.length;a++)u+=l(t,n),u+=_+s(r,e[a],r.options[i].type,i)+" HTTP/1.1",o||(u+=c(e[a])),u+=v,n++;return u}function d(e,t,n,i,r,o,s){var l="";return l+=h(t,n),l+=u(e,n,i,o,r,s),l+=a(n,!0),l+=v}function h(e,t){var n="";return n+="--"+e+v,n+="Content-Type: multipart/mixed; boundary="+t+v}function f(e,t){var n={},i=o("sf_batch_"),r="",s=0,l=e.options.batch.url,c=o("sf_changeset_");return n.type=e.options.batch.type,n.url=y(l)?l():l,n.headers={"Content-Type":"multipart/mixed; boundary="+i},t.updated.length&&(r+=d(t.updated,i,c,s,e,"update",!1),s+=t.updated.length,c=o("sf_changeset_")),t.destroyed.length&&(r+=d(t.destroyed,i,c,s,e,"destroy",!0),s+=t.destroyed.length,c=o("sf_changeset_")),t.created.length&&(r+=d(t.created,i,c,s,e,"create",!1)),r+=a(i,!0),n.data=r,n}function p(e){var t,n,i,r,o,a,s=e.match(/--changesetresponse_[a-z0-9-]+$/gm),l=0,c=[];for(c.push({models:[],passed:!0}),a=0;a<s.length;a++)r=s[a],r.lastIndexOf("--",r.length-1)?a<s.length-1&&c.push({models:[],passed:!0}):(l=l?e.indexOf(r,l+r.length):e.indexOf(r),t=e.substring(l,e.indexOf("--",l+1)),n=t.match(/^HTTP\/1\.\d (\d{3}) (.*)$/gm).pop(),i=g.parseFloat(n.match(/\d{3}/g).pop()),i>=200&&i<=299?(o=t.match(/\{.*\}/gm),o&&c[c.length-1].models.push(JSON.parse(o[0]))):c[c.length-1].passed=!1);return c}var g=window.kendo,m=e.extend,v="\r\n",_="\r\n\r\n",y=g.isFunction,b={eq:"eq",neq:"ne",gt:"gt",gte:"ge",lt:"lt",lte:"le",contains:"substringof",doesnotcontain:"substringof",endswith:"endswith",startswith:"startswith",isnull:"eq",isnotnull:"ne",isnullorempty:"eq",isnotnullorempty:"ne",isempty:"eq",isnotempty:"ne"},w=m({},b,{contains:"contains"}),k={pageSize:e.noop,page:e.noop,filter:function(e,t,i){t&&(t=n(t,i),t&&(e.$filter=t))},sort:function(t,n){var i=e.map(n,function(e){var t=e.field.replace(/\./g,"/");return"desc"===e.dir&&(t+=" desc"),t}).join(",");i&&(t.$orderby=i)},skip:function(e,t){t&&(e.$skip=t)},take:function(e,t){t&&(e.$top=t)}},x={read:{dataType:"jsonp"}};m(!0,g.data,{schemas:{odata:{type:"json",data:function(e){return e.d.results||[e.d]},total:"d.__count"}},transports:{odata:{read:{cache:!0,dataType:"jsonp",jsonp:"$callback"},update:{cache:!0,dataType:"json",contentType:"application/json",type:"PUT"},create:{cache:!0,dataType:"json",contentType:"application/json",type:"POST"},destroy:{cache:!0,dataType:"json",type:"DELETE"},parameterMap:function(e,t,n){var i,r,o,a;if(e=e||{},t=t||"read",a=(this.options||x)[t],a=a?a.dataType:"json","read"===t){i={$inlinecount:"allpages"},"json"!=a&&(i.$format="json");for(o in e)k[o]?k[o](i,e[o],n):i[o]=e[o]}else{if("json"!==a)throw Error("Only json dataType can be used for "+t+" operation.");if("destroy"!==t){for(o in e)r=e[o],"number"==typeof r&&(e[o]=r+"");i=g.stringify(e)}}return i}}}}),m(!0,g.data,{schemas:{"odata-v4":{type:"json",data:function(t){if(e.isArray(t)){for(var n=0;n<t.length;n++)i(t[n]);return t}return t=e.extend({},t),i(t),t.value?t.value:[t]},total:function(e){return e["@odata.count"]}}},transports:{"odata-v4":{batch:{type:"POST"},read:{cache:!0,dataType:"json"},update:{cache:!0,dataType:"json",contentType:"application/json;IEEE754Compatible=true",type:"PUT"},create:{cache:!0,dataType:"json",contentType:"application/json;IEEE754Compatible=true",type:"POST"},destroy:{cache:!0,dataType:"json",type:"DELETE"},parameterMap:function(e,t){var n=g.data.transports.odata.parameterMap(e,t,!0);return"read"==t&&(n.$count=!0,delete n.$inlinecount),n},submit:function(t){var n=this,i=f(n,t.data),r=t.data;(r.updated.length||r.destroyed.length||r.created.length)&&e.ajax(m(!0,{},{success:function(e){var n,i=p(e),o=0;r.updated.length&&(n=i[o],n.passed&&t.success(n.models.length?n.models:[],"update"),o++),r.destroyed.length&&(n=i[o],n.passed&&t.success([],"destroy"),o++),r.created.length&&(n=i[o],n.passed&&t.success(n.models,"create"))},error:function(e,n,i){t.error(e,n,i)}},i))}}}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.data.xml.min",["kendo.core.min"],e)}(function(){return function(e,t){var n=window.kendo,i=e.isArray,r=e.isPlainObject,o=e.map,a=e.each,s=e.extend,l=n.getter,c=n.Class,u=c.extend({init:function(t){var l,c,u,d,h=this,f=t.total,p=t.model,g=t.parse,m=t.errors,v=t.serialize,_=t.data;p&&(r(p)&&(l=t.modelBase||n.data.Model,p.fields&&a(p.fields,function(t,n){r(n)&&n.field?e.isFunction(n.field)||(n=s(n,{field:h.getter(n.field)})):n={field:h.getter(n)},p.fields[t]=n}),c=p.id,c&&(u={},u[h.xpathToMember(c,!0)]={field:h.getter(c)},p.fields=s(u,p.fields),p.id=h.xpathToMember(c)),p=l.define(p)),h.model=p),f&&("string"==typeof f?(f=h.getter(f),h.total=function(e){return parseInt(f(e),10)}):"function"==typeof f&&(h.total=f)),m&&("string"==typeof m?(m=h.getter(m),h.errors=function(e){return m(e)||null}):"function"==typeof m&&(h.errors=m)),_&&("string"==typeof _?(_=h.xpathToMember(_),h.data=function(e){var t,n=h.evaluate(e,_);return n=i(n)?n:[n],h.model&&p.fields?(t=new h.model,o(n,function(e){if(e){var n,i={};for(n in p.fields)i[n]=t._parse(n,p.fields[n].field(e));return i}})):n}):"function"==typeof _&&(h.data=_)),"function"==typeof g&&(d=h.parse,h.parse=function(e){var t=g.call(h,e);return d.call(h,t)}),"function"==typeof v&&(h.serialize=v)},total:function(e){return this.data(e).length},errors:function(e){return e?e.errors:null},serialize:function(e){return e},parseDOM:function(e){var n,r,o,a,s,l,c,u={},d=e.attributes,h=d.length;for(c=0;c<h;c++)l=d[c],u["@"+l.nodeName]=l.nodeValue;for(r=e.firstChild;r;r=r.nextSibling)o=r.nodeType,3===o||4===o?u["#text"]=r.nodeValue:1===o&&(n=this.parseDOM(r),a=r.nodeName,s=u[a],i(s)?s.push(n):s=s!==t?[s,n]:n,u[a]=s);return u},evaluate:function(e,t){for(var n,r,o,a,s,l=t.split(".");n=l.shift();)if(e=e[n],i(e)){for(r=[],t=l.join("."),s=0,o=e.length;s<o;s++)a=this.evaluate(e[s],t),a=i(a)?a:[a],r.push.apply(r,a);return r}return e},parse:function(t){var n,i,r={};return n=t.documentElement||e.parseXML(t).documentElement,i=this.parseDOM(n),r[n.nodeName]=i,r},xpathToMember:function(e,t){return e?(e=e.replace(/^\//,"").replace(/\//g,"."),e.indexOf("@")>=0?e.replace(/\.?(@.*)/,t?"$1":'["$1"]'):e.indexOf("text()")>=0?e.replace(/(\.?text\(\))/,t?"#text":'["#text"]'):e):""},getter:function(e){return l(this.xpathToMember(e),!0)}});e.extend(!0,n.data,{XmlDataReader:u,readers:{xml:u}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.data.min",["kendo.core.min","kendo.data.odata.min","kendo.data.xml.min"],e)}(function(){return function(e,t){function n(e,t,n,i){return function(r){var o,a={};for(o in r)a[o]=r[o];a.field=i?n+"."+r.field:n,t==Ae&&e._notifyChange&&e._notifyChange(a),e.trigger(t,a)}}function i(t,n){if(t===n)return!0;var r,o=e.type(t),a=e.type(n);if(o!==a)return!1;if("date"===o)return t.getTime()===n.getTime();if("object"!==o&&"array"!==o)return!1;for(r in t)if(!i(t[r],n[r]))return!1;return!0}function r(e,t){var n,i;for(i in e){if(n=e[i],me(n)&&n.field&&n.field===t)return n;if(n===t)return n}return null}function o(e){this.data=e||[]}function a(e,n){if(e){var i=typeof e===Me?{field:e,dir:n}:e,r=_e(i)?i:i!==t?[i]:[];return ye(r,function(e){return!!e.dir})}}function s(e){var t,n,i,r,o=e.filters;if(o)for(t=0,n=o.length;t<n;t++)i=o[t],r=i.operator,r&&typeof r===Me&&(i.operator=ne[r.toLowerCase()]||r),s(i)}function l(e){if(e&&!ve(e))return!_e(e)&&e.filters||(e={logic:"and",filters:_e(e)?e:[e]}),s(e),e}function c(e,t){return!e.logic&&!t.logic&&(e.field===t.field&&e.value===t.value&&e.operator===t.operator)}function u(e){return e=e||{},ve(e)?{logic:"and",filters:[]}:l(e)}function d(e,t){return t.logic||e.field>t.field?1:e.field<t.field?-1:0}function h(e,t){var n,i,r,o,a;if(e=u(e),t=u(t),e.logic!==t.logic)return!1;if(r=(e.filters||[]).slice(),o=(t.filters||[]).slice(),r.length!==o.length)return!1;for(r=r.sort(d),o=o.sort(d),a=0;a<r.length;a++)if(n=r[a],i=o[a],n.logic&&i.logic){if(!h(n,i))return!1}else if(!c(n,i))return!1;return!0}function f(e){return _e(e)?e:[e]}function p(e,n,i,r){var o=typeof e===Me?{field:e,dir:n,compare:i,skipItemSorting:r}:e,a=_e(o)?o:o!==t?[o]:[];return G(a,function(e){return{field:e.field,dir:e.dir||"asc",aggregates:e.aggregates,compare:e.compare,skipItemSorting:e.skipItemSorting}})}function g(e,t,n){var i,r=p(e,t,n);for(i=0;i<r.length;i++)delete r[i].compare;return r}function m(e){var t,n=_e(e)?e:[e];for(t=0;t<n.length;t++)if(n[t]&&Se(n[t].compare))return!0;return!1}function v(e,t){return e&&e.getTime&&t&&t.getTime?e.getTime()===t.getTime():e===t}function _(e,t,n,i,r,o){var a,s,l,c,u;for(t=t||[],c=t.length,a=0;a<c;a++)s=t[a],l=s.aggregate,u=s.field,e[u]=e[u]||{},o[u]=o[u]||{},o[u][l]=o[u][l]||{},e[u][l]=ie[l.toLowerCase()](e[u][l],n,xe.accessor(u),i,r,o[u][l])}function y(e){return"number"==typeof e&&!isNaN(e)}function b(e){return e&&e.getTime}function w(e){var t,n=e.length,i=Array(n);for(t=0;t<n;t++)i[t]=e[t].toJSON();return i}function k(e,t,n,i,r){var o,a,s,l,c,u={};for(l=0,c=e.length;l<c;l++){o=e[l];for(a in t)s=r[a],s&&s!==a&&(u[s]||(u[s]=xe.setter(s)),u[s](o,t[a](o)),delete o[a])}}function x(e,t,n,i,r){var o,a,s,l,c;for(l=0,c=e.length;l<c;l++){o=e[l];for(a in t)o[a]=n._parse(a,t[a](o)),s=r[a],s&&s!==a&&delete o[s]}}function S(e,t,n,i,r){var o,a,s,l;for(a=0,l=e.length;a<l;a++)o=e[a],s=i[o.field],s&&s!=o.field&&(o.field=s),o.value=n._parse(o.field,o.value),o.hasSubgroups?S(o.items,t,n,i,r):x(o.items,t,n,i,r)}function T(e,t,n,i,r,o){return function(a){return a=e(a),C(t,n,i,r,o)(a)}}function C(e,t,n,i,r){return function(o){return o&&!ve(n)&&("[object Array]"===Ke.call(o)||o instanceof nt||(o=[o]),t(o,n,new e,i,r)),o||[]}}function M(e,t){var n,i,r;if(t.items&&t.items.length)for(r=0;r<t.items.length;r++)n=e.items[r],i=t.items[r],n&&i?n.hasSubgroups?M(n,i):n.field&&n.value==i.value?n.items.push.apply(n.items,i.items):e.items.push.apply(e.items,[i]):i&&e.items.push.apply(e.items,[i])}function D(e,t,n,i){for(var r,o,a,s=0;t.length&&i&&(r=t[s],o=r.items,a=o.length,e&&e.field===r.field&&e.value===r.value?(e.hasSubgroups&&e.items.length?D(e.items[e.items.length-1],r.items,n,i):(o=o.slice(n,n+i),e.items=e.items.concat(o)),t.splice(s--,1)):r.hasSubgroups&&o.length?(D(r,o,n,i),r.items.length||t.splice(s--,1)):(o=o.slice(n,n+i),r.items=o,r.items.length||t.splice(s--,1)),0===o.length?n-=a:(n=0,i-=o.length),!(++s>=t.length)););s<t.length&&t.splice(s,t.length-s)}function E(e,t){var n,i,r,o,a=[],s=(e||[]).length,l=Se(t)?t:function(e,t){return e[t]};for(r=0;r<s;r++)if(n=l(e,r),n.hasSubgroups)a=a.concat(E(n.items));else for(i=n.items,o=0;o<i.length;o++)a.push(l(i,o));return a}function P(e){var t,n,i,r,o,a=[];for(t=0,n=e.length;t<n;t++)if(o=e.at(t),o.hasSubgroups)a=a.concat(P(o.items));else for(i=o.items,r=0;r<i.length;r++)a.push(i.at(r));return a}function F(e,t){var n,i,r;if(t)for(n=0,i=e.length;n<i;n++)r=e.at(n),r.hasSubgroups?F(r.items,t):r.items=new Q(r.items,t,r.items._events)}function O(e,t){for(var n=0;n<e.length;n++)if(e[n].hasSubgroups){if(O(e[n].items,t))return!0}else if(t(e[n].items,e[n]))return!0}function z(e,t,n,i){for(var r=0;r<e.length&&e[r].data!==t&&!A(e[r].data,n,i);r++);}function A(e,t,n){for(var i=0,r=e.length;i<r;i++){if(e[i]&&e[i].hasSubgroups)return A(e[i].items,t,n);if(e[i]===t||e[i]===n)return e[i]=n,!0}}function I(e,n,i,r,o){var a,s,l,c;for(a=0,s=e.length;a<s;a++)if(l=e[a],l&&!(l instanceof r))if(l.hasSubgroups===t||o){for(c=0;c<n.length;c++)if(n[c]===l){e[a]=n.at(c),z(i,n,l,e[a]);break}}else I(l.items,n,i,r,o)}function H(e,t){var n,i,r=e.length;for(i=0;i<r;i++)if(n=e[i],n.uid&&n.uid==t.uid)return e.splice(i,1),n}function V(e,t){return t?N(e,function(e){return e.uid&&e.uid==t.uid||e[t.idField]===t.id&&t.id!==t._defaultId}):-1}function R(e,t){return t?N(e,function(e){return e.uid==t.uid}):-1}function N(e,t){var n,i;for(n=0,i=e.length;n<i;n++)if(t(e[n]))return n;return-1}function B(e,t){var n,i;return e&&!ve(e)?(n=e[t],i=me(n)?n.from||n.field||t:e[t]||t,Se(i)?t:i):t}function $(e,t){var n,i,r,o={};for(r in e)"filters"!==r&&(o[r]=e[r]);if(e.filters)for(o.filters=[],n=0,i=e.filters.length;n<i;n++)o.filters[n]=$(e.filters[n],t);else o.field=B(t.fields,o.field);return o}function L(e,t){var n,i,r,o,a,s=[];for(n=0,i=e.length;n<i;n++){r={},o=e[n];for(a in o)r[a]=o[a];r.field=B(t.fields,r.field),r.aggregates&&_e(r.aggregates)&&(r.aggregates=L(r.aggregates,t)),s.push(r)}return s}function j(t,n){var i,r,o,a,s,l,c,u,d,h;for(t=e(t)[0],i=t.options,r=n[0],o=n[1],a=[],s=0,l=i.length;s<l;s++)d={},u=i[s],c=u.parentNode,c===t&&(c=null),u.disabled||c&&c.disabled||(c&&(d.optgroup=c.label),d[r.field]=u.text,h=u.attributes.value,h=h&&h.specified?u.value:u.text,d[o.field]=h,a.push(d));return a}function W(t,n){var i,r,o,a,s,l,c,u=e(t)[0].tBodies[0],d=u?u.rows:[],h=n.length,f=[];for(i=0,r=d.length;i<r;i++){for(s={},c=!0,a=d[i].cells,o=0;o<h;o++)l=a[o],"th"!==l.nodeName.toLowerCase()&&(c=!1,s[n[o].field]=l.innerHTML);c||f.push(s)}return f}function q(e){return function(){var t=this._data,n=le.fn[e].apply(this,Xe.call(arguments));return this._data!=t&&this._attachBubbleHandlers(),n}}function U(t,n){function i(e,t){return e.filter(t).add(e.find(t))}var r,o,a,s,l,c,u,d,h=e(t).children(),f=[],p=n[0].field,g=n[1]&&n[1].field,m=n[2]&&n[2].field,v=n[3]&&n[3].field;for(r=0,o=h.length;r<o;r++)a={_loaded:!0},s=h.eq(r),c=s[0].firstChild,d=s.children(),t=d.filter("ul"),d=d.filter(":not(ul)"),l=s.attr("data-id"),l&&(a.id=l),c&&(a[p]=3==c.nodeType?c.nodeValue:d.text()),g&&(a[g]=i(d,"a").attr("href")),v&&(a[v]=i(d,"img").attr("src")),m&&(u=i(d,".k-sprite").prop("className"),a[m]=u&&e.trim(u.replace("k-sprite",""))),t.length&&(a.items=U(t.eq(0),n)),"true"==s.attr("data-hasChildren")&&(a.hasChildren=!0),f.push(a);return f}var G,Q,Y,J,X,Z,K,ee,te,ne,ie,re,oe,ae,se,le,ce,ue,de,he,fe,pe=e.extend,ge=e.proxy,me=e.isPlainObject,ve=e.isEmptyObject,_e=e.isArray,ye=e.grep,be=e.ajax,we=e.each,ke=e.noop,xe=window.kendo,Se=xe.isFunction,Te=xe.Observable,Ce=xe.Class,Me="string",De="function",Ee="asc",Pe="create",Fe="read",Oe="update",ze="destroy",Ae="change",Ie="sync",He="get",Ve="error",Re="requestStart",Ne="progress",Be="requestEnd",$e=[Pe,Fe,Oe,ze],Le=function(e){return e},je=xe.getter,We=xe.stringify,qe=Math,Ue=[].push,Ge=[].join,Qe=[].pop,Ye=[].splice,Je=[].shift,Xe=[].slice,Ze=[].unshift,Ke={}.toString,et=xe.support.stableSort,tt=/^\/Date\((.*?)\)\/$/,nt=Te.extend({init:function(e,t){var n=this;n.type=t||Y,Te.fn.init.call(n),n.length=e.length,n.wrapAll(e,n)},at:function(e){return this[e]},toJSON:function(){var e,t,n=this.length,i=Array(n);for(e=0;e<n;e++)t=this[e],t instanceof Y&&(t=t.toJSON()),i[e]=t;return i},parent:ke,wrapAll:function(e,t){var n,i,r=this,o=function(){return r};for(t=t||[],n=0,i=e.length;n<i;n++)t[n]=r.wrap(e[n],o);return t},wrap:function(e,t){var n,i=this;return null!==e&&"[object Object]"===Ke.call(e)&&(n=e instanceof i.type||e instanceof Z,n||(e=e instanceof Y?e.toJSON():e,e=new i.type(e)),e.parent=t,e.bind(Ae,function(e){i.trigger(Ae,{field:e.field,node:e.node,index:e.index,items:e.items||[this],action:e.node?e.action||"itemloaded":"itemchange"})})),e},push:function(){var e,t=this.length,n=this.wrapAll(arguments);return e=Ue.apply(this,n),this.trigger(Ae,{action:"add",index:t,items:n}),e},slice:Xe,sort:[].sort,join:Ge,pop:function(){var e=this.length,t=Qe.apply(this);return e&&this.trigger(Ae,{action:"remove",index:e-1,items:[t]}),t},splice:function(e,t,n){var i,r,o,a=this.wrapAll(Xe.call(arguments,2));if(i=Ye.apply(this,[e,t].concat(a)),i.length)for(this.trigger(Ae,{action:"remove",index:e,items:i}),r=0,o=i.length;r<o;r++)i[r]&&i[r].children&&i[r].unbind(Ae);return n&&this.trigger(Ae,{action:"add",index:e,items:a}),i},shift:function(){var e=this.length,t=Je.apply(this);return e&&this.trigger(Ae,{action:"remove",index:0,items:[t]}),t},unshift:function(){var e,t=this.wrapAll(arguments);return e=Ze.apply(this,t),this.trigger(Ae,{action:"add",index:0,items:t}),e},indexOf:function(e){var t,n,i=this;for(t=0,n=i.length;t<n;t++)if(i[t]===e)return t;return-1},forEach:function(e,t){for(var n=0,i=this.length,r=t||window;n<i;n++)e.call(r,this[n],n,this)},map:function(e,t){for(var n=0,i=[],r=this.length,o=t||window;n<r;n++)i[n]=e.call(o,this[n],n,this);return i},reduce:function(e){var t,n=0,i=this.length;for(2==arguments.length?t=arguments[1]:n<i&&(t=this[n++]);n<i;n++)t=e(t,this[n],n,this);return t},reduceRight:function(e){var t,n=this.length-1;for(2==arguments.length?t=arguments[1]:n>0&&(t=this[n--]);n>=0;n--)t=e(t,this[n],n,this);return t},filter:function(e,t){for(var n,i=0,r=[],o=this.length,a=t||window;i<o;i++)n=this[i],e.call(a,n,i,this)&&(r[r.length]=n);return r},find:function(e,t){for(var n,i=0,r=this.length,o=t||window;i<r;i++)if(n=this[i],e.call(o,n,i,this))return n},every:function(e,t){for(var n,i=0,r=this.length,o=t||window;i<r;i++)if(n=this[i],!e.call(o,n,i,this))return!1;return!0},some:function(e,t){for(var n,i=0,r=this.length,o=t||window;i<r;i++)if(n=this[i],e.call(o,n,i,this))return!0;return!1},remove:function(e){var t=this.indexOf(e);t!==-1&&this.splice(t,1)},empty:function(){this.splice(0,this.length)}});"undefined"!=typeof Symbol&&Symbol.iterator&&!nt.prototype[Symbol.iterator]&&(nt.prototype[Symbol.iterator]=[][Symbol.iterator]),Q=nt.extend({init:function(e,t,n){Te.fn.init.call(this),this.type=t||Y,n&&(this._events=n);for(var i=0;i<e.length;i++)this[i]=e[i];this.length=i,this._parent=ge(function(){return this},this)},at:function(e){var t=this[e];return t instanceof this.type?t.parent=this._parent:t=this[e]=this.wrap(t,this._parent),t}}),Y=Te.extend({init:function(e){var t,n,i=this,r=function(){return i};Te.fn.init.call(this),this._handlers={};for(n in e)t=e[n],"object"==typeof t&&t&&!t.getTime&&"_"!=n.charAt(0)&&(t=i.wrap(t,n,r)),i[n]=t;i.uid=xe.guid()},shouldSerialize:function(e){return this.hasOwnProperty(e)&&"_handlers"!==e&&"_events"!==e&&typeof this[e]!==De&&"uid"!==e},forEach:function(e){for(var t in this)this.shouldSerialize(t)&&e(this[t],t)},toJSON:function(){var e,t,n={};for(t in this)this.shouldSerialize(t)&&(e=this[t],(e instanceof Y||e instanceof nt)&&(e=e.toJSON()),n[t]=e);return n},get:function(e){var t,n=this;return n.trigger(He,{field:e}),t="this"===e?n:xe.getter(e,!0)(n)},_set:function(e,t){var n,i,r,o=this,a=e.indexOf(".")>=0;if(a)for(n=e.split("."),i="";n.length>1;){if(i+=n.shift(),r=xe.getter(i,!0)(o),r instanceof Y)return r.set(n.join("."),t),a;i+="."}return xe.setter(e)(o,t),a},set:function(e,t){var n=this,i=!1,r=e.indexOf(".")>=0,o=xe.getter(e,!0)(n);return o!==t&&(o instanceof Te&&this._handlers[e]&&(this._handlers[e].get&&o.unbind(He,this._handlers[e].get),o.unbind(Ae,this._handlers[e].change)),i=n.trigger("set",{field:e,value:t}),i||(r||(t=n.wrap(t,e,function(){return n})),(!n._set(e,t)||e.indexOf("(")>=0||e.indexOf("[")>=0)&&n.trigger(Ae,{field:e}))),i},parent:ke,wrap:function(e,t,i){var r,o,a,s,l=this,c=Ke.call(e);return null==e||"[object Object]"!==c&&"[object Array]"!==c||(a=e instanceof nt,s=e instanceof le,"[object Object]"!==c||s||a?("[object Array]"===c||a||s)&&(a||s||(e=new nt(e)),o=n(l,Ae,t,!1),e.bind(Ae,o),l._handlers[t]={change:o}):(e instanceof Y||(e=new Y(e)),r=n(l,He,t,!0),e.bind(He,r),o=n(l,Ae,t,!0),e.bind(Ae,o),l._handlers[t]={get:r,change:o}),e.parent=i),e}}),J={number:function(e){return typeof e===Me&&"null"===e.toLowerCase()?null:xe.parseFloat(e)},date:function(e){return typeof e===Me&&"null"===e.toLowerCase()?null:xe.parseDate(e)},"boolean":function(e){return typeof e===Me?"null"===e.toLowerCase()?null:"true"===e.toLowerCase():null!=e?!!e:e},string:function(e){return typeof e===Me&&"null"===e.toLowerCase()?null:null!=e?e+"":e},"default":function(e){return e}},X={string:"",number:0,date:new Date,"boolean":!1,"default":""},Z=Y.extend({init:function(n){var i,r,o=this;if((!n||e.isEmptyObject(n))&&(n=e.extend({},o.defaults,n),o._initializers))for(i=0;i<o._initializers.length;i++)r=o._initializers[i],n[r]=o.defaults[r]();Y.fn.init.call(o,n),o.dirty=!1,o.dirtyFields={},o.idField&&(o.id=o.get(o.idField),o.id===t&&(o.id=o._defaultId))},shouldSerialize:function(e){return Y.fn.shouldSerialize.call(this,e)&&"uid"!==e&&!("id"!==this.idField&&"id"===e)&&"dirty"!==e&&"dirtyFields"!==e&&"_accessors"!==e},_parse:function(e,t){var n,i=this,o=e,a=i.fields||{};return e=a[e],e||(e=r(a,o)),e&&(n=e.parse,!n&&e.type&&(n=J[e.type.toLowerCase()])),n?n(t):t},_notifyChange:function(e){var t=e.action;"add"!=t&&"remove"!=t||(this.dirty=!0,this.dirtyFields[e.field]=!0)},editable:function(e){return e=(this.fields||{})[e],!e||e.editable!==!1},set:function(e,t,n){var r=this,o=r.dirty;r.editable(e)&&(t=r._parse(e,t),i(t,r.get(e))?r.trigger("equalSet",{field:e,value:t}):(r.dirty=!0,r.dirtyFields[e]=!0,Y.fn.set.call(r,e,t,n)&&!o&&(r.dirty=o,r.dirty||(r.dirtyFields[e]=!1))))},accept:function(e){var t,n,i=this,r=function(){return i};for(t in e)n=e[t],"_"!=t.charAt(0)&&(n=i.wrap(e[t],t,r)),i._set(t,n);i.idField&&(i.id=i.get(i.idField)),i.dirty=!1,i.dirtyFields={}},isNew:function(){return this.id===this._defaultId}}),Z.define=function(e,n){n===t&&(n=e,e=Z);var i,r,o,a,s,l,c,u,d=pe({defaults:{}},n),h={},f=d.id,p=[];if(f&&(d.idField=f),d.id&&delete d.id,f&&(d.defaults[f]=d._defaultId=""),"[object Array]"===Ke.call(d.fields)){for(l=0,c=d.fields.length;l<c;l++)o=d.fields[l],typeof o===Me?h[o]={}:o.field&&(h[o.field]=o);d.fields=h}for(r in d.fields)o=d.fields[r],a=o.type||"default",s=null,u=r,r=typeof o.field===Me?o.field:r,o.nullable||(s=d.defaults[u!==r?u:r]=o.defaultValue!==t?o.defaultValue:X[a.toLowerCase()],"function"==typeof s&&p.push(r)),n.id===r&&(d._defaultId=s),d.defaults[u!==r?u:r]=s,o.parse=o.parse||J[a];return p.length>0&&(d._initializers=p),i=e.extend(d),i.define=function(e){return Z.define(i,e)},d.fields&&(i.fields=d.fields,i.idField=d.idField),i},K={selector:function(e){return Se(e)?e:je(e)},compare:function(e){var t=this.selector(e);return function(e,n){return e=t(e),n=t(n),null==e&&null==n?0:null==e?-1:null==n?1:e.localeCompare?e.localeCompare(n):e>n?1:e<n?-1:0}},create:function(e){var t=e.compare||this.compare(e.field);return"desc"==e.dir?function(e,n){return t(n,e,!0)}:t},combine:function(e){return function(t,n){var i,r,o=e[0](t,n);for(i=1,r=e.length;i<r;i++)o=o||e[i](t,n);return o}}},ee=pe({},K,{asc:function(e){var t=this.selector(e);return function(e,n){var i=t(e),r=t(n);return i&&i.getTime&&r&&r.getTime&&(i=i.getTime(),r=r.getTime()),i===r?e.__position-n.__position:null==i?-1:null==r?1:i.localeCompare?i.localeCompare(r):i>r?1:-1}},desc:function(e){var t=this.selector(e);return function(e,n){var i=t(e),r=t(n);return i&&i.getTime&&r&&r.getTime&&(i=i.getTime(),r=r.getTime()),i===r?e.__position-n.__position:null==i?1:null==r?-1:r.localeCompare?r.localeCompare(i):i<r?1:-1}},create:function(e){return this[e.dir](e.field)}}),G=function(e,t){var n,i=e.length,r=Array(i);for(n=0;n<i;n++)r[n]=t(e[n],n,e);return r},te=function(){function e(e){return"string"==typeof e&&(e=e.replace(/[\r\n]+/g,"")),
JSON.stringify(e)}function t(t){return function(n,i,r,o){return i+="",r&&(n="("+n+" || '').toString()"+(o?".toLocaleLowerCase('"+o+"')":".toLowerCase()"),i=o?i.toLocaleLowerCase(o):i.toLowerCase()),t(n,e(i),r)}}function n(t,n,i,r,o){if(null!=i){if(typeof i===Me){var a=tt.exec(i);a?i=new Date((+a[1])):r?(i=e(o?i.toLocaleLowerCase(o):i.toLowerCase()),n="(("+n+" || '')+'')"+(o?".toLocaleLowerCase('"+o+"')":".toLowerCase()")):i=e(i)}i.getTime&&(n="("+n+"&&"+n+".getTime?"+n+".getTime():"+n+")",i=i.getTime())}return n+" "+t+" "+i}function i(e){var t,n,i,r;for(t="/^",n=!1,i=0;i<e.length;++i){if(r=e.charAt(i),n)t+="\\"+r;else{if("~"==r){n=!0;continue}t+="*"==r?".*":"?"==r?".":".+^$()[]{}|\\/\n\r\u2028\u2029 ".indexOf(r)>=0?"\\"+r:r}n=!1}return t+"$/"}return{quote:function(t){return t&&t.getTime?"new Date("+t.getTime()+")":e(t)},eq:function(e,t,i,r){return n("==",e,t,i,r)},neq:function(e,t,i,r){return n("!=",e,t,i,r)},gt:function(e,t,i){return n(">",e,t,i)},gte:function(e,t,i){return n(">=",e,t,i)},lt:function(e,t,i){return n("<",e,t,i)},lte:function(e,t,i){return n("<=",e,t,i)},startswith:t(function(e,t){return e+".lastIndexOf("+t+", 0) == 0"}),doesnotstartwith:t(function(e,t){return e+".lastIndexOf("+t+", 0) == -1"}),endswith:t(function(e,t){var n=t?t.length-2:0;return e+".indexOf("+t+", "+e+".length - "+n+") >= 0"}),doesnotendwith:t(function(e,t){var n=t?t.length-2:0;return e+".indexOf("+t+", "+e+".length - "+n+") < 0"}),contains:t(function(e,t){return e+".indexOf("+t+") >= 0"}),doesnotcontain:t(function(e,t){return e+".indexOf("+t+") == -1"}),matches:t(function(e,t){return t=t.substring(1,t.length-1),i(t)+".test("+e+")"}),doesnotmatch:t(function(e,t){return t=t.substring(1,t.length-1),"!"+i(t)+".test("+e+")"}),isempty:function(e){return e+" === ''"},isnotempty:function(e){return e+" !== ''"},isnull:function(e){return"("+e+" == null)"},isnotnull:function(e){return"("+e+" != null)"},isnullorempty:function(e){return"("+e+" === null) || ("+e+" === '')"},isnotnullorempty:function(e){return"("+e+" !== null) && ("+e+" !== '')"}}}(),o.filterExpr=function(e){var n,i,r,a,s,l,c=[],u={and:" && ",or:" || "},d=[],h=[],f=e.filters;for(n=0,i=f.length;n<i;n++)r=f[n],s=r.field,l=r.operator,r.filters?(a=o.filterExpr(r),r=a.expression.replace(/__o\[(\d+)\]/g,function(e,t){return t=+t,"__o["+(h.length+t)+"]"}).replace(/__f\[(\d+)\]/g,function(e,t){return t=+t,"__f["+(d.length+t)+"]"}),h.push.apply(h,a.operators),d.push.apply(d,a.fields)):(typeof s===De?(a="__f["+d.length+"](d)",d.push(s)):a=xe.expr(s),typeof l===De?(r="__o["+h.length+"]("+a+", "+te.quote(r.value)+")",h.push(l)):r=te[(l||"eq").toLowerCase()](a,r.value,r.ignoreCase===t||r.ignoreCase,e.accentFoldingFiltering)),c.push(r);return{expression:"("+c.join(u[e.logic])+")",fields:d,operators:h}},ne={"==":"eq",equals:"eq",isequalto:"eq",equalto:"eq",equal:"eq","!=":"neq",ne:"neq",notequals:"neq",isnotequalto:"neq",notequalto:"neq",notequal:"neq","<":"lt",islessthan:"lt",lessthan:"lt",less:"lt","<=":"lte",le:"lte",islessthanorequalto:"lte",lessthanequal:"lte",">":"gt",isgreaterthan:"gt",greaterthan:"gt",greater:"gt",">=":"gte",isgreaterthanorequalto:"gte",greaterthanequal:"gte",ge:"gte",notsubstringof:"doesnotcontain",isnull:"isnull",isempty:"isempty",isnotempty:"isnotempty"},o.normalizeFilter=l,o.compareFilters=h,o.prototype={toArray:function(){return this.data},range:function(e,t){return new o(this.data.slice(e,e+t))},skip:function(e){return new o(this.data.slice(e))},take:function(e){return new o(this.data.slice(0,e))},select:function(e){return new o(G(this.data,e))},order:function(e,t,n){var i={dir:t};return e&&(e.compare?i.compare=e.compare:i.field=e),new o(n?this.data.sort(K.create(i)):this.data.slice(0).sort(K.create(i)))},orderBy:function(e,t){return this.order(e,"asc",t)},orderByDescending:function(e,t){return this.order(e,"desc",t)},sort:function(e,t,n,i){var r,o,s=a(e,t),l=[];if(n=n||K,s.length){for(r=0,o=s.length;r<o;r++)l.push(n.create(s[r]));return this.orderBy({compare:n.combine(l)},i)}return this},filter:function(e){var t,n,i,r,a,s,c,u,d=this.data,h=[];if(e=l(e),!e||0===e.filters.length)return this;for(r=o.filterExpr(e),s=r.fields,c=r.operators,a=u=Function("d, __f, __o","return "+r.expression),(s.length||c.length)&&(u=function(e){return a(e,s,c)}),t=0,i=d.length;t<i;t++)n=d[t],u(n)&&h.push(n);return new o(h)},group:function(e,t){e=p(e||[]),t=t||this.data;var n,i=this,r=new o(i.data);return e.length>0&&(n=e[0],r=r.groupBy(n).select(function(i){var r=new o(t).filter([{field:i.field,operator:"eq",value:i.value,ignoreCase:!1}]);return{field:i.field,value:i.value,items:e.length>1?new o(i.items).group(e.slice(1),r.toArray()).toArray():i.items,hasSubgroups:e.length>1,aggregates:r.aggregate(n.aggregates)}})),r},groupBy:function(e){var t,n,i,r,a,s,l,c,u,d,h=this;if(ve(e)||!this.data.length)return new o([]);for(t=e.field,n=e.skipItemSorting?this.data:this._sortForGrouping(t,e.dir||"asc"),i=xe.accessor(t),a=i.get(n[0],t),s={field:t,value:a,items:[]},d=[s],c=0,u=n.length;c<u;c++)r=n[c],l=i.get(r,t),v(a,l)||(a=l,s={field:t,value:a,items:[]},d.push(s)),s.items.push(r);return d=h._sortGroups(d,e),new o(d)},_sortForGrouping:function(e,t){var n,i,r=this.data;if(!et){for(n=0,i=r.length;n<i;n++)r[n].__position=n;for(r=new o(r).sort(e,t,ee).toArray(),n=0,i=r.length;n<i;n++)delete r[n].__position;return r}return this.sort(e,t).toArray()},_sortGroups:function(e,t){var n=e;return t&&Se(t.compare)&&(n=new o(n).order({compare:t.compare},t.dir||Ee).toArray()),n},aggregate:function(e){var t,n,i={},r={};if(e&&e.length)for(t=0,n=this.data.length;t<n;t++)_(i,e,this.data[t],t,n,r);return i}},ie={sum:function(e,t,n){var i=n.get(t);return y(e)?y(i)&&(e+=i):e=i,e},count:function(e){return(e||0)+1},average:function(e,n,i,r,o,a){var s=i.get(n);return a.count===t&&(a.count=0),y(e)?y(s)&&(e+=s):e=s,y(s)&&a.count++,r==o-1&&y(e)&&(e/=a.count),e},max:function(e,t,n){var i=n.get(t);return y(e)||b(e)||(e=i),e<i&&(y(i)||b(i))&&(e=i),e},min:function(e,t,n){var i=n.get(t);return y(e)||b(e)||(e=i),e>i&&(y(i)||b(i))&&(e=i),e}},o.normalizeGroup=p,o.normalizeSort=a,o.process=function(e,n,i){var r,s,l,c,u,d,h,f,v,_,y,b;return n=n||{},r=n.group,s=m(p(r||[])),l=new o(e),c=g(r||[]),u=a(n.sort||[]),d=s?u:c.concat(u),v=n.filterCallback,_=n.filter,y=n.skip,b=n.take,d&&i&&(l=l.sort(d,t,t,i)),_&&(l=l.filter(_),v&&(l=v(l)),f=l.toArray().length),d&&!i&&(l=l.sort(d),r&&(e=l.toArray())),s?(l=l.group(r,e),y!==t&&b!==t&&(l=new o(E(l.toArray())).range(y,b),h=G(c,function(e){return pe({},e,{skipItemSorting:!0})}),l=l.group(h,e))):(y!==t&&b!==t&&(l=l.range(y,b)),r&&(l=l.group(r,e))),{total:f,data:l.toArray()}},re=Ce.extend({init:function(e){this.data=e.data},read:function(e){e.success(this.data)},update:function(e){e.success(e.data)},create:function(e){e.success(e.data)},destroy:function(e){e.success(e.data)}}),oe=Ce.extend({init:function(e){var t,n=this;e=n.options=pe({},n.options,e),we($e,function(t,n){typeof e[n]===Me&&(e[n]={url:e[n]})}),n.cache=e.cache?ae.create(e.cache):{find:ke,add:ke},t=e.parameterMap,e.submit&&(n.submit=e.submit),Se(e.push)&&(n.push=e.push),n.push||(n.push=Le),n.parameterMap=Se(t)?t:function(e){var n={};return we(e,function(e,i){e in t&&(e=t[e],me(e)&&(i=e.value(i),e=e.key)),n[e]=i}),n}},options:{parameterMap:Le},create:function(e){return be(this.setup(e,Pe))},read:function(n){var i,r,o,a=this,s=a.cache;n=a.setup(n,Fe),i=n.success||ke,r=n.error||ke,o=s.find(n.data),o!==t?i(o):(n.success=function(e){s.add(n.data,e),i(e)},e.ajax(n))},update:function(e){return be(this.setup(e,Oe))},destroy:function(e){return be(this.setup(e,ze))},setup:function(e,t){e=e||{};var n,i=this,r=i.options[t],o=Se(r.data)?r.data(e.data):r.data;return e=pe(!0,{},r,e),n=pe(!0,{},o,e.data),e.data=i.parameterMap(n,t),Se(e.url)&&(e.url=e.url(n)),e}}),ae=Ce.extend({init:function(){this._store={}},add:function(e,n){e!==t&&(this._store[We(e)]=n)},find:function(e){return this._store[We(e)]},clear:function(){this._store={}},remove:function(e){delete this._store[We(e)]}}),ae.create=function(e){var t={inmemory:function(){return new ae}};return me(e)&&Se(e.find)?e:e===!0?new ae:t[e]()},se=Ce.extend({init:function(e){var t,n,i,r,o,a,s,l,c,u,d,h,f,p,g=this;e=e||{};for(t in e)n=e[t],g[t]=typeof n===Me?je(n):n;r=e.modelBase||Z,me(g.model)&&(g.model=i=r.define(g.model)),o=ge(g.data,g),g._dataAccessFunction=o,g.model&&(a=ge(g.groups,g),s=ge(g.serialize,g),l={},c={},u={},d={},h=!1,i=g.model,i.fields&&(we(i.fields,function(e,t){var n;f=e,me(t)&&t.field?f=t.field:typeof t===Me&&(f=t),me(t)&&t.from&&(n=t.from),h=h||n&&n!==e||f!==e,p=n||f,c[e]=p.indexOf(".")!==-1?je(p,!0):je(p),u[e]=je(e),l[n||f]=e,d[e]=n||f}),!e.serialize&&h&&(g.serialize=T(s,i,k,u,l,d))),g._dataAccessFunction=o,g._wrapDataAccessBase=C(i,x,c,l,d),g.data=T(o,i,x,c,l,d),g.groups=T(a,i,S,c,l,d))},errors:function(e){return e?e.errors:null},parse:Le,data:Le,total:function(e){return e.length},groups:Le,aggregates:function(){return{}},serialize:function(e){return e}}),le=Te.extend({init:function(e){var n,i,r,o=this;e&&(i=e.data),e=o.options=pe({},o.options,e),o._map={},o._prefetch={},o._data=[],o._pristineData=[],o._ranges=[],o._view=[],o._pristineTotal=0,o._destroyed=[],o._pageSize=e.pageSize,o._page=e.page||(e.pageSize?1:t),o._sort=a(e.sort),o._filter=l(e.filter),o._group=p(e.group),o._aggregate=e.aggregate,o._total=e.total,o._shouldDetachObservableParents=!0,Te.fn.init.call(o),o.transport=ce.create(e,i,o),Se(o.transport.push)&&o.transport.push({pushCreate:ge(o._pushCreate,o),pushUpdate:ge(o._pushUpdate,o),pushDestroy:ge(o._pushDestroy,o)}),null!=e.offlineStorage&&("string"==typeof e.offlineStorage?(r=e.offlineStorage,o._storage={getItem:function(){return JSON.parse(localStorage.getItem(r))},setItem:function(e){localStorage.setItem(r,We(o.reader.serialize(e)))}}):o._storage=e.offlineStorage),o.reader=new xe.data.readers[e.schema.type||"json"](e.schema),n=o.reader.model||{},o._detachObservableParents(),o._data=o._observe(o._data),o._online=!0,o.bind(["push",Ve,Ae,Re,Ie,Be,Ne],e)},options:{data:null,schema:{modelBase:Z},offlineStorage:null,serverSorting:!1,serverPaging:!1,serverFiltering:!1,serverGrouping:!1,serverAggregates:!1,batch:!1,inPlaceSort:!1},clone:function(){return this},online:function(n){return n!==t?this._online!=n&&(this._online=n,n)?this.sync():e.Deferred().resolve().promise():this._online},offlineData:function(e){return null==this.options.offlineStorage?null:e!==t?this._storage.setItem(e):this._storage.getItem()||[]},_isServerGrouped:function(){var e=this.group()||[];return this.options.serverGrouping&&e.length},_pushCreate:function(e){this._push(e,"pushCreate")},_pushUpdate:function(e){this._push(e,"pushUpdate")},_pushDestroy:function(e){this._push(e,"pushDestroy")},_push:function(e,t){var n=this._readData(e);n||(n=e),this[t](n)},_flatData:function(e,t){if(e){if(this._isServerGrouped())return P(e);if(!t)for(var n=0;n<e.length;n++)e.at(n)}return e},parent:ke,get:function(e){var t,n,i=this._flatData(this._data,this.options.useRanges);for(t=0,n=i.length;t<n;t++)if(i[t].id==e)return i[t]},getByUid:function(e){return this._getByUid(e,this._data)},_getByUid:function(e,t){var n,i,r=this._flatData(t,this.options.useRanges);if(r)for(n=0,i=r.length;n<i;n++)if(r[n].uid==e)return r[n]},indexOf:function(e){return R(this._data,e)},at:function(e){return this._data.at(e)},data:function(e){var n,i=this;if(e===t){if(i._data)for(n=0;n<i._data.length;n++)i._data.at(n);return i._data}i._detachObservableParents(),i._data=this._observe(e),i._pristineData=e.slice(0),i._storeData(),i._ranges=[],i.trigger("reset"),i._addRange(i._data),i._total=i._data.length,i._pristineTotal=i._total,i._process(i._data)},view:function(e){return e===t?this._view:(this._view=this._observeView(e),t)},_observeView:function(e){var t,n=this;return I(e,n._data,n._ranges,n.reader.model||Y,n._isServerGrouped()),t=new Q(e,n.reader.model),t.parent=function(){return n.parent()},t},flatView:function(){var e=this.group()||[];return e.length?P(this._view):this._view},add:function(e){return this.insert(this._data.length,e)},_createNewModel:function(e){return this.reader.model?new this.reader.model(e):e instanceof Y?e:new Y(e)},insert:function(e,t){return t||(t=e,e=0),t instanceof Z||(t=this._createNewModel(t)),this._isServerGrouped()?this._data.splice(e,0,this._wrapInEmptyGroup(t)):this._data.splice(e,0,t),this._insertModelInRange(e,t),t},pushInsert:function(t,n){var i,r,o,a,s,l,c=this,u=c._getCurrentRangeSpan();n||(n=t,t=0),_e(n)||(n=[n]),i=[],r=this.options.autoSync,this.options.autoSync=!1;try{for(o=0;o<n.length;o++)a=n[o],s=this.insert(t,a),i.push(s),l=s.toJSON(),this._isServerGrouped()&&(l=this._wrapInEmptyGroup(l)),this._pristineData.push(l),u&&u.length&&e(u).last()[0].pristineData.push(l),t++}finally{this.options.autoSync=r}i.length&&this.trigger("push",{type:"create",items:i})},pushCreate:function(e){this.pushInsert(this._data.length,e)},pushUpdate:function(e){var t,n,i,r,o;for(_e(e)||(e=[e]),t=[],n=0;n<e.length;n++)i=e[n],r=this._createNewModel(i),o=this.get(r.id),o?(t.push(o),o.accept(i),o.trigger(Ae),this._updatePristineForModel(o,i)):this.pushCreate(i);t.length&&this.trigger("push",{type:"update",items:t})},pushDestroy:function(e){var t=this._removeItems(e);t.length&&this.trigger("push",{type:"destroy",items:t})},_removeItems:function(e,n){var i,r,o,a,s,l,c;_e(e)||(e=[e]),i=t===n||n,r=[],o=this.options.autoSync,this.options.autoSync=!1;try{for(a=0;a<e.length;a++)s=e[a],l=this._createNewModel(s),c=!1,this._eachItem(this._data,function(e){var t,n;for(t=0;t<e.length;t++)if(n=e.at(t),n.id===l.id){r.push(n),e.splice(t,1),c=!0;break}}),c&&i&&(this._removePristineForModel(l),this._destroyed.pop())}finally{this.options.autoSync=o}return r},remove:function(e){var t,n=this,i=n._isServerGrouped();return this._eachItem(n._data,function(r){if(t=H(r,e),t&&i)return t.isNew&&t.isNew()||n._destroyed.push(t),!0}),this._removeModelFromRanges(e),e},destroyed:function(){return this._destroyed},created:function(){var e,t,n=[],i=this._flatData(this._data,this.options.useRanges);for(e=0,t=i.length;e<t;e++)i[e].isNew&&i[e].isNew()&&n.push(i[e]);return n},updated:function(){var e,t,n=[],i=this._flatData(this._data,this.options.useRanges);for(e=0,t=i.length;e<t;e++)i[e].isNew&&!i[e].isNew()&&i[e].dirty&&n.push(i[e]);return n},sync:function(){var t,n=this,i=[],r=[],o=n._destroyed,a=e.Deferred().resolve().promise();if(n.online()){if(!n.reader.model)return a;i=n.created(),r=n.updated(),t=[],n.options.batch&&n.transport.submit?t=n._sendSubmit(i,r,o):(t.push.apply(t,n._send("create",i)),t.push.apply(t,n._send("update",r)),t.push.apply(t,n._send("destroy",o))),a=e.when.apply(null,t).then(function(){var e,t;for(e=0,t=arguments.length;e<t;e++)arguments[e]&&n._accept(arguments[e]);n._storeData(!0),n._syncEnd(),n._change({action:"sync"}),n.trigger(Ie)})}else n._storeData(!0),n._syncEnd(),n._change({action:"sync"});return a},_syncEnd:ke,cancelChanges:function(e){var t=this;e instanceof xe.data.Model?t._cancelModel(e):(t._destroyed=[],t._detachObservableParents(),t._data=t._observe(t._pristineData),t.options.serverPaging&&(t._total=t._pristineTotal),t._ranges=[],t._addRange(t._data,0),t._changesCanceled(),t._change(),t._markOfflineUpdatesAsDirty())},_changesCanceled:ke,_markOfflineUpdatesAsDirty:function(){var e=this;null!=e.options.offlineStorage&&e._eachItem(e._data,function(e){var t,n;for(t=0;t<e.length;t++)n=e.at(t),"update"!=n.__state__&&"create"!=n.__state__||(n.dirty=!0)})},hasChanges:function(){var e,t,n=this._flatData(this._data,this.options.useRanges);if(this._destroyed.length)return!0;for(e=0,t=n.length;e<t;e++)if(n[e].isNew&&n[e].isNew()||n[e].dirty)return!0;return!1},_accept:function(t){var n,i=this,r=t.models,o=t.response,a=0,s=i._isServerGrouped(),l=i._pristineData,c=t.type;if(i.trigger(Be,{response:o,type:c}),o&&!ve(o)){if(o=i.reader.parse(o),i._handleCustomErrors(o))return;o=i.reader.data(o),_e(o)||(o=[o])}else o=e.map(r,function(e){return e.toJSON()});for("destroy"===c&&(i._destroyed=[]),a=0,n=r.length;a<n;a++)"destroy"!==c?(r[a].accept(o[a]),"create"===c?l.push(s?i._wrapInEmptyGroup(r[a].toJSON()):o[a]):"update"===c&&i._updatePristineForModel(r[a],o[a])):i._removePristineForModel(r[a])},_updatePristineForModel:function(e,t){this._executeOnPristineForModel(e,function(e,n){xe.deepExtend(n[e],t)})},_executeOnPristineForModel:function(e,t){this._eachPristineItem(function(n){var i=V(n,e);if(i>-1)return t(i,n),!0})},_removePristineForModel:function(e){this._executeOnPristineForModel(e,function(e,t){t.splice(e,1)})},_readData:function(e){var t=this._isServerGrouped()?this.reader.groups:this.reader.data;return t.call(this.reader,e)},_eachPristineItem:function(e){var t=this,n=t.options,i=t._getCurrentRangeSpan();t._eachItem(t._pristineData,e),n.serverPaging&&n.useRanges&&we(i,function(n,i){t._eachItem(i.pristineData,e)})},_eachItem:function(e,t){e&&e.length&&(this._isServerGrouped()?O(e,t):t(e))},_pristineForModel:function(e){var t,n,i=function(i){if(n=V(i,e),n>-1)return t=i[n],!0};return this._eachPristineItem(i),t},_cancelModel:function(e){var t=this,n=this._pristineForModel(e);this._eachItem(this._data,function(i){var r=R(i,e);r>=0&&(!n||e.isNew()&&!n.__state__?(t._modelCanceled(e),i.splice(r,1),t._removeModelFromRanges(e)):(i[r].accept(n),"update"==n.__state__&&(i[r].dirty=!0)))})},_modelCanceled:ke,_submit:function(t,n){var i=this;i.trigger(Re,{type:"submit"}),i.trigger(Ne),i.transport.submit(pe({success:function(n,i){var r=e.grep(t,function(e){return e.type==i})[0];r&&r.resolve({response:n,models:r.models,type:i})},error:function(e,n,r){for(var o=0;o<t.length;o++)t[o].reject(e);i.error(e,n,r)}},n))},_sendSubmit:function(t,n,i){var r=this,o=[];return r.options.batch&&(t.length&&o.push(e.Deferred(function(e){e.type="create",e.models=t})),n.length&&o.push(e.Deferred(function(e){e.type="update",e.models=n})),i.length&&o.push(e.Deferred(function(e){e.type="destroy",e.models=i})),r._submit(o,{data:{created:r.reader.serialize(w(t)),updated:r.reader.serialize(w(n)),destroyed:r.reader.serialize(w(i))}})),o},_promise:function(t,n,i){var r=this;return e.Deferred(function(e){r.trigger(Re,{type:i}),r.trigger(Ne),r.transport[i].call(r.transport,pe({success:function(t){e.resolve({response:t,models:n,type:i})},error:function(t,n,i){e.reject(t),r.error(t,n,i)}},t))}).promise()},_send:function(e,t){var n,i,r=this,o=[],a=r.reader.serialize(w(t));if(r.options.batch)t.length&&o.push(r._promise({data:{models:a}},t,e));else for(n=0,i=t.length;n<i;n++)o.push(r._promise({data:a[n]},[t[n]],e));return o},read:function(t){var n=this,i=n._params(t),r=e.Deferred();return n._queueRequest(i,function(){var e=n.trigger(Re,{type:"read"});e?(n._dequeueRequest(),r.resolve(e)):(n.trigger(Ne),n._ranges=[],n.trigger("reset"),n.online()?n.transport.read({data:i,success:function(e){n._ranges=[],n.success(e,i),r.resolve()},error:function(){var e=Xe.call(arguments);n.error.apply(n,e),r.reject.apply(r,e)}}):null!=n.options.offlineStorage&&(n.success(n.offlineData(),i),r.resolve()))}),r.promise()},_readAggregates:function(e){return this.reader.aggregates(e)},success:function(e){var n,i,r,o,a,s,l,c,u,d,h,f=this,p=f.options;if(f.trigger(Be,{response:e,type:"read"}),f.online()){if(e=f.reader.parse(e),f._handleCustomErrors(e))return f._dequeueRequest(),t;f._total=f.reader.total(e),f._pageSize>f._total&&(f._pageSize=f._total,f.options.pageSize&&f.options.pageSize>f._pageSize&&(f._pageSize=f.options.pageSize)),f._aggregate&&p.serverAggregates&&(f._aggregateResult=f._readAggregates(e)),e=f._readData(e),f._destroyed=[]}else{for(e=f._readData(e),n=[],r={},o=f.reader.model,a=o?o.idField:"id",s=0;s<this._destroyed.length;s++)l=this._destroyed[s][a],r[l]=l;for(s=0;s<e.length;s++)c=e[s],u=c.__state__,"destroy"==u?r[c[a]]||this._destroyed.push(this._createNewModel(c)):n.push(c);e=n,f._total=e.length}if(f._pristineTotal=f._total,i=f._skip&&f._data.length&&f._skip<f._data.length,f.options.endless)for(i&&f._pristineData.splice(f._skip,f._pristineData.length),n=e.slice(0),d=0;d<n.length;d++)f._pristineData.push(n[d]);else f._pristineData=e.slice(0);if(f._detachObservableParents(),f.options.endless){for(f._data.unbind(Ae,f._changeHandler),f._isServerGrouped()&&f._data[f._data.length-1].value===e[0].value&&(M(f._data[f._data.length-1],e[0]),e.shift()),e=f._observe(e),i&&f._data.splice(f._skip,f._data.length),h=0;h<e.length;h++)f._data.push(e[h]);f._data.bind(Ae,f._changeHandler)}else f._data=f._observe(e);f._markOfflineUpdatesAsDirty(),f._storeData(),f._addRange(f._data),f._process(f._data),f._dequeueRequest()},_detachObservableParents:function(){if(this._data&&this._shouldDetachObservableParents)for(var e=0;e<this._data.length;e++)this._data[e].parent&&(this._data[e].parent=ke)},_storeData:function(e){function t(e){var n,i,r,o=[];for(n=0;n<e.length;n++)i=e.at(n),r=i.toJSON(),a&&i.items?r.items=t(i.items):(r.uid=i.uid,s&&(i.isNew()?r.__state__="create":i.dirty&&(r.__state__="update"))),o.push(r);return o}var n,i,r,o,a=this._isServerGrouped(),s=this.reader.model;if(null!=this.options.offlineStorage){for(n=t(this._data),i=[],r=0;r<this._destroyed.length;r++)o=this._destroyed[r].toJSON(),o.__state__="destroy",i.push(o);this.offlineData(n.concat(i)),e&&(this._pristineData=this.reader.reader?this.reader.reader._wrapDataAccessBase(n):this.reader._wrapDataAccessBase(n))}},_addRange:function(e,n){var i=this,r=t!==n?n:i._skip||0,o=r+i._flatData(e,!0).length;i._ranges.push({start:r,end:o,data:e,pristineData:e.toJSON(),timestamp:i._timeStamp()}),i._sortRanges()},_sortRanges:function(){this._ranges.sort(function(e,t){return e.start-t.start})},error:function(e,t,n){this._dequeueRequest(),this.trigger(Be,{}),this.trigger(Ve,{xhr:e,status:t,errorThrown:n})},_params:function(e){var t=this,n=pe({take:t.take(),skip:t.skip(),page:t.page(),pageSize:t.pageSize(),sort:t._sort,filter:t._filter,group:t._group,aggregate:t._aggregate},e);return t.options.serverPaging||(delete n.take,delete n.skip,delete n.page,delete n.pageSize),t.options.serverGrouping?t.reader.model&&n.group&&(n.group=L(n.group,t.reader.model)):delete n.group,t.options.serverFiltering?t.reader.model&&n.filter&&(n.filter=$(n.filter,t.reader.model)):delete n.filter,t.options.serverSorting?t.reader.model&&n.sort&&(n.sort=L(n.sort,t.reader.model)):delete n.sort,t.options.serverAggregates?t.reader.model&&n.aggregate&&(n.aggregate=L(n.aggregate,t.reader.model)):delete n.aggregate,n},_queueRequest:function(e,n){var i=this;i._requestInProgress?i._pending={callback:ge(n,i),options:e}:(i._requestInProgress=!0,i._pending=t,n())},_dequeueRequest:function(){var e=this;e._requestInProgress=!1,e._pending&&e._queueRequest(e._pending.options,e._pending.callback)},_handleCustomErrors:function(e){if(this.reader.errors){var t=this.reader.errors(e);if(t)return this.trigger(Ve,{xhr:null,status:"customerror",errorThrown:"custom error",errors:t}),!0}return!1},_shouldWrap:function(e){var t=this.reader.model;return!(!t||!e.length)&&!(e[0]instanceof t)},_observe:function(e){var t,n=this,i=n.reader.model;return n._shouldDetachObservableParents=!0,e instanceof nt?(n._shouldDetachObservableParents=!1,n._shouldWrap(e)&&(e.type=n.reader.model,e.wrapAll(e,e))):(t=n.pageSize()&&!n.options.serverPaging?Q:nt,e=new t(e,n.reader.model),e.parent=function(){return n.parent()}),n._isServerGrouped()&&F(e,i),!(n._changeHandler&&n._data&&n._data instanceof nt)||n.options.useRanges&&n.options.serverPaging?n._changeHandler=ge(n._change,n):n._data.unbind(Ae,n._changeHandler),e.bind(Ae,n._changeHandler)},_updateTotalForAction:function(e,t){var n=this,i=parseInt(n._total,10);y(n._total)||(i=parseInt(n._pristineTotal,10)),"add"===e?i+=t.length:"remove"===e?i-=t.length:"itemchange"===e||"sync"===e||n.options.serverPaging?"sync"===e&&(i=n._pristineTotal=parseInt(n._total,10)):i=n._pristineTotal,n._total=i},_change:function(e){var t,n,i,r=this,o=e?e.action:"";if("remove"===o)for(t=0,n=e.items.length;t<n;t++)e.items[t].isNew&&e.items[t].isNew()||r._destroyed.push(e.items[t]);!r.options.autoSync||"add"!==o&&"remove"!==o&&"itemchange"!==o?(r._updateTotalForAction(o,e?e.items:[]),r._process(r._data,e)):(i=function(t){"sync"===t.action&&(r.unbind("change",i),r._updateTotalForAction(o,e.items))},r.first("change",i),r.sync())},_calculateAggregates:function(e,t){t=t||{};var n=new o(e),i=t.aggregate,r=t.filter;return r&&(n=n.filter(r)),n.aggregate(i)},_process:function(e,n){var i,r=this,o={};r.options.serverPaging!==!0&&(o.skip=r._skip,o.take=r._take||r._pageSize,o.skip===t&&r._page!==t&&r._pageSize!==t&&(o.skip=(r._page-1)*r._pageSize),r.options.useRanges&&(o.skip=r.currentRangeStart())),r.options.serverSorting!==!0&&(o.sort=r._sort),r.options.serverFiltering!==!0&&(o.filter=r._filter),r.options.serverGrouping!==!0&&(o.group=r._group),r.options.serverAggregates!==!0&&(o.aggregate=r._aggregate),r.options.serverGrouping&&r._clearEmptyGroups(e),i=r._queryProcess(e,o),r.options.serverAggregates!==!0&&(r._aggregateResult=r._calculateAggregates(i.dataToAggregate||e,o)),r.view(i.data),r._setFilterTotal(i.total,!1),n=n||{},n.items=n.items||r._view,r.trigger(Ae,n)},_clearEmptyGroups:function(e){var t,n;for(t=e.length-1;t>=0;t--)n=e[t],n.hasSubgroups?this._clearEmptyGroups(n.items):n.items&&!n.items.length&&Ye.apply(n.parent(),[t,1])},_queryProcess:function(e,t){return this.options.inPlaceSort?o.process(e,t,this.options.inPlaceSort):o.process(e,t)},_mergeState:function(n){var i=this;return n!==t&&(i._pageSize=n.pageSize,i._page=n.page,i._sort=n.sort,i._filter=n.filter,i._group=n.group,i._aggregate=n.aggregate,i._skip=i._currentRangeStart=n.skip,i._take=n.take,i._skip===t&&(i._skip=i._currentRangeStart=i.skip(),n.skip=i.skip()),i._take===t&&i._pageSize!==t&&(i._take=i._pageSize,n.take=i._take),n.sort&&(i._sort=n.sort=a(n.sort)),n.filter&&(i._filter=n.filter=i.options.accentFoldingFiltering&&!e.isEmptyObject(n.filter)?e.extend({},l(n.filter),{accentFoldingFiltering:i.options.accentFoldingFiltering}):l(n.filter)),n.group&&(i._group=n.group=p(n.group)),n.aggregate&&(i._aggregate=n.aggregate=f(n.aggregate))),n},query:function(n){var i,r,o,a=this.options.serverSorting||this.options.serverPaging||this.options.serverFiltering||this.options.serverGrouping||this.options.serverAggregates;return a||(this._data===t||0===this._data.length)&&!this._destroyed.length?(this.options.endless&&(r=n.pageSize-this.pageSize(),r>0?(r=this.pageSize(),n.page=n.pageSize/r,n.pageSize=r):(n.page=1,this.options.endless=!1)),this.read(this._mergeState(n))):(o=this.trigger(Re,{type:"read"}),o||(this.trigger(Ne),i=this._queryProcess(this._data,this._mergeState(n)),this._setFilterTotal(i.total,!0),this._aggregateResult=this._calculateAggregates(i.dataToAggregate||this._data,n),this.view(i.data),this.trigger(Be,{type:"read"}),this.trigger(Ae,{items:i.data})),e.Deferred().resolve(o).promise())},_setFilterTotal:function(e,n){var i=this;i.options.serverFiltering||(e!==t?i._total=e:n&&(i._total=i._data.length))},fetch:function(e){var t=this,n=function(n){n!==!0&&Se(e)&&e.call(t)};return this._query().done(n)},_query:function(e){var t=this;return t.query(pe({},{page:t.page(),pageSize:t.pageSize(),sort:t.sort(),filter:t.filter(),group:t.group(),aggregate:t.aggregate()},e))},next:function(e){var t=this,n=t.page(),i=t.total();if(e=e||{},n&&!(i&&n+1>t.totalPages()))return t._skip=t._currentRangeStart=n*t.take(),n+=1,e.page=n,t._query(e),n},prev:function(e){var t=this,n=t.page();if(e=e||{},n&&1!==n)return t._skip=t._currentRangeStart=t._skip-t.take(),n-=1,e.page=n,t._query(e),n},page:function(e){var n,i=this;return e!==t?(e=qe.max(qe.min(qe.max(e,1),i.totalPages()),1),i._query(i._pageableQueryOptions({page:e})),t):(n=i.skip(),n!==t?qe.round((n||0)/(i.take()||1))+1:t)},pageSize:function(e){var n=this;return e!==t?(n._query(n._pageableQueryOptions({pageSize:e,page:1})),t):n.take()},sort:function(e){var n=this;return e!==t?(n._query({sort:e}),t):n._sort},filter:function(e){var n=this;return e===t?n._filter:(n.trigger("reset"),n._query({filter:e,page:1}),t)},group:function(e){var n=this;return e!==t?(n._query({group:e}),t):n._group},total:function(){return parseInt(this._total||0,10)},aggregate:function(e){var n=this;return e!==t?(n._query({aggregate:e}),t):n._aggregate},aggregates:function(){var e=this._aggregateResult;return ve(e)&&(e=this._emptyAggregates(this.aggregate())),e},_emptyAggregates:function(e){var t,n,i={};if(!ve(e))for(t={},_e(e)||(e=[e]),n=0;n<e.length;n++)t[e[n].aggregate]=0,i[e[n].field]=t;return i},_pageableQueryOptions:function(e){return e},_wrapInEmptyGroup:function(e){var t,n,i,r,o=this.group();for(i=o.length-1,r=0;i>=r;i--)n=o[i],t={value:e.get?e.get(n.field):e[n.field],field:n.field,items:t?[t]:[e],hasSubgroups:!!t,aggregates:this._emptyAggregates(n.aggregates)};return t},totalPages:function(){var e=this,t=e.pageSize()||e.total();return qe.ceil((e.total()||0)/t)},inRange:function(e,t){var n=this,i=qe.min(e+t,n.total());return!n.options.serverPaging&&n._data.length>0||n._findRange(e,i).length>0},lastRange:function(){var e=this._ranges;return e[e.length-1]||{start:0,end:0,data:[]}},firstItemUid:function(){var e=this._ranges;return e.length&&e[0].data.length&&e[0].data[0].uid},enableRequestsInProgress:function(){this._skipRequestsInProgress=!1},_timeStamp:function(){return(new Date).getTime()},range:function(e,n,i){this._currentRequestTimeStamp=this._timeStamp(),this._skipRequestsInProgress=!0,e=qe.min(e||0,this.total()),i=Se(i)?i:ke;var r,o=this,a=qe.max(qe.floor(e/n),0)*n,s=qe.min(a+n,o.total());return r=o._findRange(e,qe.min(e+n,o.total())),r.length||0===o.total()?(o._processRangeData(r,e,n,a,s),i(),t):(n!==t&&(o._rangeExists(a,s)?a<e&&o.prefetch(s,n,function(){o.range(e,n,i)}):o.prefetch(a,n,function(){e>a&&s<o.total()&&!o._rangeExists(s,qe.min(s+n,o.total()))?o.prefetch(s,n,function(){o.range(e,n,i)}):o.range(e,n,i)})),t)},_findRange:function(e,n){var i,r,o,s,l,c,u,d,h,f,p,m,v=this,_=v._ranges,y=[],b=v.options,w=b.serverSorting||b.serverPaging||b.serverFiltering||b.serverGrouping||b.serverAggregates;for(r=0,p=_.length;r<p;r++)if(i=_[r],e>=i.start&&e<=i.end){for(f=0,o=r;o<p;o++)if(i=_[o],h=v._flatData(i.data,!0),h.length&&e+f>=i.start&&(c=i.data,u=i.end,w||(b.inPlaceSort?d=v._queryProcess(i.data,{filter:v.filter()}):(m=g(v.group()||[]).concat(a(v.sort()||[])),d=v._queryProcess(i.data,{sort:m,filter:v.filter()})),h=c=d.data,d.total!==t&&(u=d.total)),s=0,e+f>i.start&&(s=e+f-i.start),l=h.length,u>n&&(l-=u-n),f+=l-s,y=v._mergeGroups(y,c,s,l),n<=i.end&&f==n-e))return y;break}return[]},_mergeGroups:function(e,t,n,i){if(this._isServerGrouped()){var r,o=t.toJSON();return e.length&&(r=e[e.length-1]),D(r,o,n,i),e.concat(o)}return e.concat(t.slice(n,i))},_processRangeData:function(e,n,i,r,o){var a,s,l,c,u=this;u._pending=t,u._skip=n>u.skip()?qe.min(o,(u.totalPages()-1)*u.take()):r,u._currentRangeStart=n,u._take=i,a=u.options.serverPaging,s=u.options.serverSorting,l=u.options.serverFiltering,c=u.options.serverAggregates;try{u.options.serverPaging=!0,u._isServerGrouped()||u.group()&&u.group().length||(u.options.serverSorting=!0),u.options.serverFiltering=!0,u.options.serverPaging=!0,u.options.serverAggregates=!0,a&&(u._detachObservableParents(),u._data=e=u._observe(e)),u._process(e)}finally{u.options.serverPaging=a,u.options.serverSorting=s,u.options.serverFiltering=l,u.options.serverAggregates=c}},skip:function(){var e=this;return e._skip===t?e._page!==t?(e._page-1)*(e.take()||1):t:e._skip},currentRangeStart:function(){return this._currentRangeStart||0},take:function(){return this._take||this._pageSize},_prefetchSuccessHandler:function(e,t,n,i){var r=this,o=r._timeStamp();return function(a){var s,l,c,u=!1,d={start:e,end:t,data:[],timestamp:r._timeStamp()};if(r._dequeueRequest(),r.trigger(Be,{response:a,type:"read"}),a=r.reader.parse(a),c=r._readData(a),c.length){for(s=0,l=r._ranges.length;s<l;s++)if(r._ranges[s].start===e){u=!0,d=r._ranges[s],d.pristineData=c,d.data=r._observe(c),d.end=d.start+r._flatData(d.data,!0).length,r._sortRanges();break}u||r._addRange(r._observe(c),e)}r._total=r.reader.total(a),(i||o>=r._currentRequestTimeStamp||!r._skipRequestsInProgress)&&(n&&c.length?n():r.trigger(Ae,{}));
}},prefetch:function(e,t,n){var i=this,r=qe.min(e+t,i.total()),o={take:t,skip:e,page:e/t+1,pageSize:t,sort:i._sort,filter:i._filter,group:i._group,aggregate:i._aggregate};i._rangeExists(e,r)?n&&n():(clearTimeout(i._timeout),i._timeout=setTimeout(function(){i._queueRequest(o,function(){i.trigger(Re,{type:"read"})?i._dequeueRequest():i.transport.read({data:i._params(o),success:i._prefetchSuccessHandler(e,r,n),error:function(){var e=Xe.call(arguments);i.error.apply(i,e)}})})},100))},_multiplePrefetch:function(e,t,n){var i=this,r=qe.min(e+t,i.total()),o={take:t,skip:e,page:e/t+1,pageSize:t,sort:i._sort,filter:i._filter,group:i._group,aggregate:i._aggregate};i._rangeExists(e,r)?n&&n():i.trigger(Re,{type:"read"})||i.transport.read({data:i._params(o),success:i._prefetchSuccessHandler(e,r,n,!0)})},_rangeExists:function(e,t){var n,i,r=this,o=r._ranges;for(n=0,i=o.length;n<i;n++)if(o[n].start<=e&&o[n].end>=t)return!0;return!1},_getCurrentRangeSpan:function(){var e,t,n=this,i=n._ranges,r=n.currentRangeStart(),o=r+(n.take()||0),a=[],s=i.length;for(t=0;t<s;t++)e=i[t],(e.start<=r&&e.end>=r||e.start>=r&&e.start<=o)&&a.push(e);return a},_removeModelFromRanges:function(e){var t,n,i,r=this;for(n=0,i=this._ranges.length;n<i;n++)t=this._ranges[n],r._removeModelFromRange(t,e);r._updateRangesLength()},_removeModelFromRange:function(e,t){this._eachItem(e.data,function(e){var n,i;for(n=0;n<e.length;n++)if(i=e[n],i.uid&&i.uid==t.uid){[].splice.call(e,n,1);break}})},_insertModelInRange:function(e,t){var n,i,r=this,o=r._ranges||[],a=o.length;for(i=0;i<a;i++)if(n=o[i],n.start<=e&&n.end>=e){r._getByUid(t.uid,n.data)||(r._isServerGrouped()?n.data.splice(e,0,r._wrapInEmptyGroup(t)):n.data.splice(e,0,t));break}r._updateRangesLength()},_updateRangesLength:function(){var e,t,n=this,i=n._ranges||[],r=i.length,o=!1,a=0,s=0;for(t=0;t<r;t++)e=i[t],s=n._flatData(e.data,!0).length-qe.abs(e.end-e.start),o||0===s?o&&(e.start+=a,e.end+=a):(o=!0,a=s,e.end+=a)}}),ce={},ce.create=function(t,n,i){var r,o=t.transport?e.extend({},t.transport):null;return o?(o.read=typeof o.read===Me?{url:o.read}:o.read,"jsdo"===t.type&&(o.dataSource=i),t.type&&(xe.data.transports=xe.data.transports||{},xe.data.schemas=xe.data.schemas||{},xe.data.transports[t.type]?me(xe.data.transports[t.type])?o=pe(!0,{},xe.data.transports[t.type],o):r=new xe.data.transports[t.type](pe(o,{data:n})):xe.logToConsole("Unknown DataSource transport type '"+t.type+"'.\nVerify that registration scripts for this type are included after Kendo UI on the page.","warn"),t.schema=pe(!0,{},xe.data.schemas[t.type],t.schema)),r||(r=Se(o.read)?o:new oe(o))):r=new re({data:t.data||[]}),r},le.create=function(e){(_e(e)||e instanceof nt)&&(e={data:e});var n,i,r,o=e||{},a=o.data,s=o.fields,l=o.table,c=o.select,u={};if(a||!s||o.transport||(l?a=W(l,s):c&&(a=j(c,s),o.group===t&&a[0]&&a[0].optgroup!==t&&(o.group="optgroup"))),xe.data.Model&&s&&(!o.schema||!o.schema.model)){for(n=0,i=s.length;n<i;n++)r=s[n],r.type&&(u[r.field]=r);ve(u)||(o.schema=pe(!0,o.schema,{model:{fields:u}}))}return o.data=a,c=null,o.select=null,l=null,o.table=null,o instanceof le?o:new le(o)},ue=Z.define({idField:"id",init:function(e){var t,n=this,i=n.hasChildren||e&&e.hasChildren,r="items",o={};xe.data.Model.fn.init.call(n,e),typeof n.children===Me&&(r=n.children),o={schema:{data:r,model:{hasChildren:i,id:n.idField,fields:n.fields}}},typeof n.children!==Me&&pe(o,n.children),o.data=e,i||(i=o.schema.data),typeof i===Me&&(i=xe.getter(i)),Se(i)&&(t=i.call(n,n),n.hasChildren=(!t||0!==t.length)&&!!t),n._childrenOptions=o,n.hasChildren&&n._initChildren(),n._loaded=!(!e||!e._loaded)},_initChildren:function(){var e,t,n,i=this;i.children instanceof de||(e=i.children=new de(i._childrenOptions),t=e.transport,n=t.parameterMap,t.parameterMap=function(e,t){return e[i.idField||"id"]=i.id,n&&(e=n(e,t)),e},e.parent=function(){return i},e.bind(Ae,function(e){e.node=e.node||i,i.trigger(Ae,e)}),e.bind(Ve,function(e){var t=i.parent();t&&(e.node=e.node||i,t.trigger(Ve,e))}),i._updateChildrenField())},append:function(e){this._initChildren(),this.loaded(!0),this.children.add(e)},hasChildren:!1,level:function(){for(var e=this.parentNode(),t=0;e&&e.parentNode;)t++,e=e.parentNode?e.parentNode():null;return t},_updateChildrenField:function(){var e=this._childrenOptions.schema.data;this[e||"items"]=this.children.data()},_childrenLoaded:function(){this._loaded=!0,this._updateChildrenField()},load:function(){var n,i,r={},o="_query";return this.hasChildren?(this._initChildren(),n=this.children,r[this.idField||"id"]=this.id,this._loaded||(n._data=t,o="read"),n.one(Ae,ge(this._childrenLoaded,this)),this._matchFilter&&(r.filter={field:"_matchFilter",operator:"eq",value:!0}),i=n[o](r)):this.loaded(!0),i||e.Deferred().resolve().promise()},parentNode:function(){var e=this.parent();return e.parent()},loaded:function(e){return e===t?this._loaded:(this._loaded=e,t)},shouldSerialize:function(e){return Z.fn.shouldSerialize.call(this,e)&&"children"!==e&&"_loaded"!==e&&"hasChildren"!==e&&"_childrenOptions"!==e}}),de=le.extend({init:function(e){var t=ue.define({children:e});e.filter&&!e.serverFiltering&&(this._hierarchicalFilter=e.filter,e.filter=null),le.fn.init.call(this,pe(!0,{},{schema:{modelBase:t,model:t}},e)),this._attachBubbleHandlers()},_attachBubbleHandlers:function(){var e=this;e._data.bind(Ve,function(t){e.trigger(Ve,t)})},read:function(e){var t=le.fn.read.call(this,e);return this._hierarchicalFilter&&(this._data&&this._data.length>0?this.filter(this._hierarchicalFilter):(this.options.filter=this._hierarchicalFilter,this._filter=l(this.options.filter),this._hierarchicalFilter=null)),t},remove:function(e){var t,n=e.parentNode(),i=this;return n&&n._initChildren&&(i=n.children),t=le.fn.remove.call(i,e),n&&!i.data().length&&(n.hasChildren=!1),t},success:q("success"),data:q("data"),insert:function(e,t){var n=this.parent();return n&&n._initChildren&&(n.hasChildren=!0,n._initChildren()),le.fn.insert.call(this,e,t)},filter:function(e){return e===t?this._filter:(!this.options.serverFiltering&&this._markHierarchicalQuery(e)&&(e={logic:"or",filters:[e,{field:"_matchFilter",operator:"equals",value:!0}]}),this.trigger("reset"),this._query({filter:e,page:1}),t)},_markHierarchicalQuery:function(t){var n,i,r,a,s,c=this.options.accentFoldingFiltering;return t=c?e.extend({},l(t),{accentFoldingFiltering:c}):l(t),t&&0!==t.filters.length?(n=o.filterExpr(t),r=n.fields,a=n.operators,i=s=Function("d, __f, __o","return "+n.expression),(r.length||a.length)&&(s=function(e){return i(e,r,a)}),this._updateHierarchicalFilter(s),!0):(this._updateHierarchicalFilter(function(){return!0}),!1)},_updateHierarchicalFilter:function(e){var t,n,i=this._data,r=!1;for(n=0;n<i.length;n++)t=i[n],t.hasChildren?(t._matchFilter=t.children._updateHierarchicalFilter(e),t._matchFilter||(t._matchFilter=e(t))):t._matchFilter=e(t),t._matchFilter&&(r=!0);return r},_find:function(e,t){var n,i,r,o,a=this._data;if(a){if(r=le.fn[e].call(this,t))return r;for(a=this._flatData(this._data),n=0,i=a.length;n<i;n++)if(o=a[n].children,o instanceof de&&(r=o[e](t)))return r}},get:function(e){return this._find("get",e)},getByUid:function(e){return this._find("getByUid",e)}}),de.create=function(e){e=e&&e.push?{data:e}:e;var t=e||{},n=t.data,i=t.fields,r=t.list;return n&&n._dataSource?n._dataSource:(n||!i||t.transport||r&&(n=U(r,i)),t.data=n,t instanceof de?t:new de(t))},he=xe.Observable.extend({init:function(e,t,n){xe.Observable.fn.init.call(this),this._prefetching=!1,this.dataSource=e,this.prefetch=!n;var i=this;e.bind("change",function(){i._change()}),e.bind("reset",function(){i._reset()}),this._syncWithDataSource(),this.setViewSize(t)},setViewSize:function(e){this.viewSize=e,this._recalculate()},at:function(e){var n=this.pageSize,i=!0;return e>=this.total()?(this.trigger("endreached",{index:e}),null):this.useRanges?this.useRanges?((e<this.dataOffset||e>=this.skip+n)&&(i=this.range(Math.floor(e/n)*n)),e===this.prefetchThreshold&&this._prefetch(),e===this.midPageThreshold?this.range(this.nextMidRange,!0):e===this.nextPageThreshold?this.range(this.nextFullRange):e===this.pullBackThreshold&&this.range(this.offset===this.skip?this.previousMidRange:this.previousFullRange),i?this.dataSource.at(e-this.dataOffset):(this.trigger("endreached",{index:e}),null)):t:this.dataSource.view()[e]},indexOf:function(e){return this.dataSource.data().indexOf(e)+this.dataOffset},total:function(){return parseInt(this.dataSource.total(),10)},next:function(){var e=this,t=e.pageSize,n=e.skip-e.viewSize+t,i=qe.max(qe.floor(n/t),0)*t;this.offset=n,this.dataSource.prefetch(i,t,function(){e._goToRange(n,!0)})},range:function(e,t){if(this.offset===e)return!0;var n=this,i=this.pageSize,r=qe.max(qe.floor(e/i),0)*i,o=this.dataSource;return t&&(r+=i),o.inRange(e,i)?(this.offset=e,this._recalculate(),this._goToRange(e),!0):!this.prefetch||(o.prefetch(r,i,function(){n.offset=e,n._recalculate(),n._goToRange(e,!0)}),!1)},syncDataSource:function(){var e=this.offset;this.offset=null,this.range(e)},destroy:function(){this.unbind()},_prefetch:function(){var e=this,t=this.pageSize,n=this.skip+t,i=this.dataSource;i.inRange(n,t)||this._prefetching||!this.prefetch||(this._prefetching=!0,this.trigger("prefetching",{skip:n,take:t}),i.prefetch(n,t,function(){e._prefetching=!1,e.trigger("prefetched",{skip:n,take:t})}))},_goToRange:function(e,t){this.offset===e&&(this.dataOffset=e,this._expanding=t,this.dataSource.range(e,this.pageSize),this.dataSource.enableRequestsInProgress())},_reset:function(){this._syncPending=!0},_change:function(){var e=this.dataSource;this.length=this.useRanges?e.lastRange().end:e.view().length,this._syncPending&&(this._syncWithDataSource(),this._recalculate(),this._syncPending=!1,this.trigger("reset",{offset:this.offset})),this.trigger("resize"),this._expanding&&this.trigger("expand"),delete this._expanding},_syncWithDataSource:function(){var e=this.dataSource;this._firstItemUid=e.firstItemUid(),this.dataOffset=this.offset=e.skip()||0,this.pageSize=e.pageSize(),this.useRanges=e.options.serverPaging},_recalculate:function(){var e=this.pageSize,t=this.offset,n=this.viewSize,i=Math.ceil(t/e)*e;this.skip=i,this.midPageThreshold=i+e-1,this.nextPageThreshold=i+n-1,this.prefetchThreshold=i+Math.floor(e/3*2),this.pullBackThreshold=this.offset-1,this.nextMidRange=i+e-n,this.nextFullRange=i,this.previousMidRange=t-n,this.previousFullRange=i-e}}),fe=xe.Observable.extend({init:function(e,t){var n=this;xe.Observable.fn.init.call(n),this.dataSource=e,this.batchSize=t,this._total=0,this.buffer=new he(e,3*t),this.buffer.bind({endreached:function(e){n.trigger("endreached",{index:e.index})},prefetching:function(e){n.trigger("prefetching",{skip:e.skip,take:e.take})},prefetched:function(e){n.trigger("prefetched",{skip:e.skip,take:e.take})},reset:function(){n._total=0,n.trigger("reset")},resize:function(){n._total=Math.ceil(this.length/n.batchSize),n.trigger("resize",{total:n.total(),offset:this.offset})}})},syncDataSource:function(){this.buffer.syncDataSource()},at:function(e){var t,n,i=this.buffer,r=e*this.batchSize,o=this.batchSize,a=[];for(i.offset>r&&i.at(i.offset-1),n=0;n<o&&(t=i.at(r+n),null!==t);n++)a.push(t);return a},total:function(){return this._total},destroy:function(){this.buffer.destroy(),this.unbind()}}),pe(!0,xe.data,{readers:{json:se},Query:o,DataSource:le,HierarchicalDataSource:de,Node:ue,ObservableObject:Y,ObservableArray:nt,LazyObservableArray:Q,LocalTransport:re,RemoteTransport:oe,Cache:ae,DataReader:se,Model:Z,Buffer:he,BatchBuffer:fe})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.data.signalr.min",["kendo.data.min"],e)}(function(){return function(e){function t(e){return e&&r(e.done)&&r(e.fail)}function n(e){return e&&r(e.then)&&r(e["catch"])}var i=window.kendo,r=i.isFunction,o=i.data.RemoteTransport.extend({init:function(e){var r,o=e&&e.signalr?e.signalr:{},a=o.promise;if(!a)throw Error('The "promise" option must be set.');if(!t(a)&&!n(a))throw Error('The "promise" option must be a Promise.');if(this.promise=a,r=o.hub,!r)throw Error('The "hub" option must be set.');if("function"!=typeof r.on||"function"!=typeof r.invoke)throw Error('The "hub" option is not a valid SignalR hub proxy.');this.hub=r,i.data.RemoteTransport.fn.init.call(this,e)},push:function(e){var t=this.options.signalr.client||{};t.create&&this.hub.on(t.create,e.pushCreate),t.update&&this.hub.on(t.update,e.pushUpdate),t.destroy&&this.hub.on(t.destroy,e.pushDestroy)},_crud:function(r,o){var a,s,l=this.hub,c=this.promise,u=this.options.signalr.server;if(!u||!u[o])throw Error(i.format('The "server.{0}" option must be set.',o));a=[u[o]],s=this.parameterMap(r.data,o),e.isEmptyObject(s)||a.push(s),t(c)?c.done(function(){l.invoke.apply(l,a).done(r.success).fail(r.error)}):n(c)&&c.then(function(){l.invoke.apply(l,a).then(r.success)["catch"](r.error)})},read:function(e){this._crud(e,"read")},create:function(e){this._crud(e,"create")},update:function(e){this._crud(e,"update")},destroy:function(e){this._crud(e,"destroy")}});e.extend(!0,i.data,{transports:{signalr:o}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.binder.min",["kendo.core.min","kendo.data.min"],e)}(function(){return function(e,t){function n(t,n,i){return v.extend({init:function(e,t,n){var i=this;v.fn.init.call(i,e.element[0],t,n),i.widget=e,i._dataBinding=F(i.dataBinding,i),i._dataBound=F(i.dataBound,i),i._itemChange=F(i.itemChange,i)},itemChange:function(e){a(e.item[0],e.data,this._ns(e.ns),[e.data].concat(this.bindings[t]._parents()))},dataBinding:function(e){var t,n,i=this.widget,r=e.removedItems||i.items();for(t=0,n=r.length;t<n;t++)c(r[t],!1)},_ns:function(t){t=t||S.ui;var n=[S.ui,S.dataviz.ui,S.mobile.ui];return n.splice(e.inArray(t,n),1),n.unshift(t),S.rolesFromNamespaces(n)},dataBound:function(e){var i,r,o,s,l=this.widget,c=e.addedItems||l.items(),u=l[n],d=S.data.HierarchicalDataSource;if(!(d&&u instanceof d)&&c.length)for(o=e.addedDataItems||u.flatView(),s=this.bindings[t]._parents(),i=0,r=o.length;i<r;i++)c[i]&&a(c[i],o[i],this._ns(e.ns),[o[i]].concat(s))},refresh:function(e){var r,o,a,s,l=this,c=l.widget;e=e||{},e.action||(l.destroy(),c.bind("dataBinding",l._dataBinding),c.bind("dataBound",l._dataBound),c.bind("itemChange",l._itemChange),r=l.bindings[t].get(),c[n]instanceof S.data.DataSource&&c[n]!=r&&(r instanceof S.data.DataSource?c[i](r):r&&r._dataSource?c[i](r._dataSource):(o=S.ui.Select&&c instanceof S.ui.Select,a=S.ui.MultiSelect&&c instanceof S.ui.MultiSelect,s=S.ui.DropDownTree&&c instanceof S.ui.DropDownTree,s?c.treeview[n].data(r):c[n].data(r),l.bindings.value&&(o||a)&&c.value(f(l.bindings.value.get(),c.options.dataValueField)))))},destroy:function(){var e=this.widget;e.unbind("dataBinding",this._dataBinding),e.unbind("dataBound",this._dataBound),e.unbind("itemChange",this._itemChange)}})}function i(e,t){var n=S.initWidget(e,{},t);if(n)return new w(n)}function r(e){var t,n,i,o,a,s,l,c={};for(l=e.match(k),t=0,n=l.length;t<n;t++)i=l[t],o=i.indexOf(":"),a=i.substring(0,o),s=i.substring(o+1),"{"==s.charAt(0)&&(s=r(s)),c[a]=s;return c}function o(e,t,n){var i,r={};for(i in e)r[i]=new n(t,e[i]);return r}function a(e,t,n,s){var c,u,d,h,f,v,_,y,w;if(e&&!e.getAttribute("data-"+S.ns+"stop")&&(c=e.getAttribute("data-"+S.ns+"role"),d=e.getAttribute("data-"+S.ns+"bind"),h=[],f=!0,_={},s=s||[t],(c||d)&&l(e,!1),c&&(y=i(e,n)),d&&(d=r(d.replace(x,"")),y||(_=S.parseOptions(e,{textField:"",valueField:"",template:"",valueUpdate:N,valuePrimitive:!1,autoBind:!0},t),_.roles=n,y=new b(e,_)),y.source=t,v=o(d,s,p),_.template&&(v.template=new m(s,"",_.template)),v.click&&(d.events=d.events||{},d.events.click=d.click,v.click.destroy(),delete v.click),v.source&&(f=!1),d.attr&&(v.attr=o(d.attr,s,p)),d.style&&(v.style=o(d.style,s,p)),d.events&&(v.events=o(d.events,s,g)),d.css&&(v.css=o(d.css,s,p)),y.bind(v)),y&&(e.kendoBindingTarget=y),w=e.children,f&&w)){for(u=0;u<w.length;u++)h[u]=w[u];for(u=0;u<h.length;u++)a(h[u],t,n,s)}}function s(t,n){var i,r,o,s=S.rolesFromNamespaces([].slice.call(arguments,2));for(n=S.observable(n),t=e(t),i=0,r=t.length;i<r;i++)o=t[i],1===o.nodeType&&a(o,n,s)}function l(t,n){var i,r=t.kendoBindingTarget;r&&(r.destroy(),V?delete t.kendoBindingTarget:t.removeAttribute?t.removeAttribute("kendoBindingTarget"):t.kendoBindingTarget=null),n&&(i=S.widgetInstance(e(t)),i&&typeof i.destroy===R&&i.destroy())}function c(e,t){l(e,t),u(e,t)}function u(e,t){var n,i,r=e.children;if(r)for(n=0,i=r.length;n<i;n++)c(r[n],t)}function d(t){var n,i;for(t=e(t),n=0,i=t.length;n<i;n++)c(t[n],!1)}function h(e,t){var n=e.element,i=n[0].kendoBindingTarget;i&&s(n,i.source,t)}function f(e,t){var n,i,r=[],o=0;if(!t)return e;if(e instanceof M){for(n=e.length;o<n;o++)i=e[o],r[o]=i.get?i.get(t):i[t];e=r}else e instanceof C&&(e=e.get(t));return e}var p,g,m,v,_,y,b,w,k,x,S=window.kendo,T=S.Observable,C=S.data.ObservableObject,M=S.data.ObservableArray,D={}.toString,E={},P=S.Class,F=e.proxy,O="value",z="source",A="events",I="checked",H="css",V=!0,R="function",N="change";!function(){var e=document.createElement("a");try{delete e.test}catch(t){V=!1}}(),p=T.extend({init:function(e,t){var n=this;T.fn.init.call(n),n.source=e[0],n.parents=e,n.path=t,n.dependencies={},n.dependencies[t]=!0,n.observable=n.source instanceof T,n._access=function(e){n.dependencies[e.field]=!0},n.observable&&(n._change=function(e){n.change(e)},n.source.bind(N,n._change))},_parents:function(){var t,n=this.parents,i=this.get();return i&&"function"==typeof i.parent&&(t=i.parent(),e.inArray(t,n)<0&&(n=[t].concat(n))),n},change:function(e){var t,n,i=e.field,r=this;if("this"===r.path)r.trigger(N,e);else for(t in r.dependencies)if(0===t.indexOf(i)&&(n=t.charAt(i.length),!n||"."===n||"["===n)){r.trigger(N,e);break}},start:function(e){e.bind("get",this._access)},stop:function(e){e.unbind("get",this._access)},get:function(){var e=this,n=e.source,i=0,r=e.path,o=n;if(!e.observable)return o;for(e.start(e.source),o=n.get(r);o===t&&n;)n=e.parents[++i],n instanceof C&&(o=n.get(r));if(o===t)for(n=e.source;o===t&&n;)n=n.parent(),n instanceof C&&(o=n.get(r));return"function"==typeof o&&(i=r.lastIndexOf("."),i>0&&(n=n.get(r.substring(0,i))),e.start(n),o=n!==e.source?o.call(n,e.source):o.call(n),e.stop(n)),n&&n!==e.source&&(e.currentSource=n,n.unbind(N,e._change).bind(N,e._change)),e.stop(e.source),o},set:function(e){var t=this.currentSource||this.source,n=S.getter(this.path)(t);"function"==typeof n?t!==this.source?n.call(t,this.source,e):n.call(t,e):t.set(this.path,e)},destroy:function(){this.observable&&(this.source.unbind(N,this._change),this.currentSource&&this.currentSource.unbind(N,this._change)),this.unbind()}}),g=p.extend({get:function(){var e,t=this.source,n=this.path,i=0;for(e=t.get(n);!e&&t;)t=this.parents[++i],t instanceof C&&(e=t.get(n));return F(e,t)}}),m=p.extend({init:function(e,t,n){var i=this;p.fn.init.call(i,e,t),i.template=n},render:function(e){var t;return this.start(this.source),t=S.render(this.template,e),this.stop(this.source),t}}),v=P.extend({init:function(e,t,n){this.element=e,this.bindings=t,this.options=n},bind:function(e,t){var n=this;e=t?e[t]:e,e.bind(N,function(e){n.refresh(t||e)}),n.refresh(t)},destroy:function(){}}),_=v.extend({dataType:function(){var e=this.element.getAttribute("data-type")||this.element.type||"text";return e.toLowerCase()},parsedValue:function(){return this._parseValue(this.element.value,this.dataType())},_parseValue:function(e,t){return"date"==t?e=S.parseDate(e,"yyyy-MM-dd"):"datetime-local"==t?e=S.parseDate(e,["yyyy-MM-ddTHH:mm:ss","yyyy-MM-ddTHH:mm"]):"number"==t?e=S.parseFloat(e):"boolean"==t&&(e=e.toLowerCase(),e=null!==S.parseFloat(e)?!!S.parseFloat(e):"true"===e.toLowerCase()),e}}),E.attr=v.extend({refresh:function(e){this.element.setAttribute(e,this.bindings.attr[e].get())}}),E.css=v.extend({init:function(e,t,n){v.fn.init.call(this,e,t,n),this.classes={}},refresh:function(t){var n=e(this.element),i=this.bindings.css[t],r=this.classes[t]=i.get();r?n.addClass(t):n.removeClass(t)}}),E.style=v.extend({refresh:function(e){this.element.style[e]=this.bindings.style[e].get()||""}}),E.enabled=v.extend({refresh:function(){this.bindings.enabled.get()?this.element.removeAttribute("disabled"):this.element.setAttribute("disabled","disabled")}}),E.readonly=v.extend({refresh:function(){this.bindings.readonly.get()?this.element.setAttribute("readonly","readonly"):this.element.removeAttribute("readonly")}}),E.disabled=v.extend({refresh:function(){this.bindings.disabled.get()?this.element.setAttribute("disabled","disabled"):this.element.removeAttribute("disabled")}}),E.events=v.extend({init:function(e,t,n){v.fn.init.call(this,e,t,n),this.handlers={}},refresh:function(t){var n=e(this.element),i=this.bindings.events[t],r=this.handlers[t];r&&n.off(t,r),r=this.handlers[t]=i.get(),n.on(t,i.source,r)},destroy:function(){var t,n=e(this.element);for(t in this.handlers)n.off(t,this.handlers[t])}}),E.text=v.extend({refresh:function(){var t=this.bindings.text.get(),n=this.element.getAttribute("data-format")||"";null==t&&(t=""),e(this.element).text(S.toString(t,n))}}),E.visible=v.extend({refresh:function(){this.element.style.display=this.bindings.visible.get()?"":"none"}}),E.invisible=v.extend({refresh:function(){this.element.style.display=this.bindings.invisible.get()?"none":""}}),E.html=v.extend({refresh:function(){this.element.innerHTML=this.bindings.html.get()}}),E.value=_.extend({init:function(t,n,i){_.fn.init.call(this,t,n,i),this._change=F(this.change,this),this.eventName=i.valueUpdate||N,e(this.element).on(this.eventName,this._change),this._initChange=!1},change:function(){this._initChange=this.eventName!=N,this.bindings[O].set(this.parsedValue()),this._initChange=!1},refresh:function(){var e,t;this._initChange||(e=this.bindings[O].get(),null==e&&(e=""),t=this.dataType(),"date"==t?e=S.toString(e,"yyyy-MM-dd"):"datetime-local"==t&&(e=S.toString(e,"yyyy-MM-ddTHH:mm:ss")),this.element.value=e),this._initChange=!1},destroy:function(){e(this.element).off(this.eventName,this._change)}}),E.source=v.extend({init:function(e,t,n){v.fn.init.call(this,e,t,n);var i=this.bindings.source.get();i instanceof S.data.DataSource&&n.autoBind!==!1&&i.fetch()},refresh:function(e){var t=this,n=t.bindings.source.get();n instanceof M||n instanceof S.data.DataSource?(e=e||{},"add"==e.action?t.add(e.index,e.items):"remove"==e.action?t.remove(e.index,e.items):"itemchange"!=e.action&&t.render()):t.render()},container:function(){var e=this.element;return"table"==e.nodeName.toLowerCase()&&(e.tBodies[0]||e.appendChild(document.createElement("tbody")),e=e.tBodies[0]),e},template:function(){var e=this.options,t=e.template,n=this.container().nodeName.toLowerCase();return t||(t="select"==n?e.valueField||e.textField?S.format('<option value="#:{0}#">#:{1}#</option>',e.valueField||e.textField,e.textField||e.valueField):"<option>#:data#</option>":"tbody"==n?"<tr><td>#:data#</td></tr>":"ul"==n||"ol"==n?"<li>#:data#</li>":"#:data#",t=S.template(t)),t},add:function(t,n){var i,r,o,s,l=this.container(),c=l.cloneNode(!1),u=l.children[t];if(e(c).html(S.render(this.template(),n)),c.children.length)for(i=this.bindings.source._parents(),r=0,o=n.length;r<o;r++)s=c.children[0],l.insertBefore(s,u||null),a(s,n[r],this.options.roles,[n[r]].concat(i))},remove:function(e,t){var n,i,r=this.container();for(n=0;n<t.length;n++)i=r.children[e],c(i,!0),i.parentNode==r&&r.removeChild(i)},render:function(){var t,n,i,r=this.bindings.source.get(),o=this.container(),s=this.template();if(null!=r)if(r instanceof S.data.DataSource&&(r=r.view()),r instanceof M||"[object Array]"===D.call(r)||(r=[r]),this.bindings.template){if(u(o,!0),e(o).html(this.bindings.template.render(r)),o.children.length)for(t=this.bindings.source._parents(),n=0,i=r.length;n<i;n++)a(o.children[n],r[n],this.options.roles,[r[n]].concat(t))}else e(o).html(S.render(s,r))}}),E.input={checked:_.extend({init:function(t,n,i){_.fn.init.call(this,t,n,i),this._change=F(this.change,this),e(this.element).change(this._change)},change:function(){var e,t,n,i=this.element,r=this.value();if("radio"==i.type)r=this.parsedValue(),this.bindings[I].set(r);else if("checkbox"==i.type)if(e=this.bindings[I].get(),e instanceof M){if(r=this.parsedValue(),r instanceof Date){for(n=0;n<e.length;n++)if(e[n]instanceof Date&&+e[n]===+r){t=n;break}}else t=e.indexOf(r);t>-1?e.splice(t,1):e.push(r)}else this.bindings[I].set(r)},refresh:function(){var e,n,i=this.bindings[I].get(),r=i,o=this.dataType(),a=this.element;if("checkbox"==a.type)if(r instanceof M){if(e=-1,i=this.parsedValue(),i instanceof Date){for(n=0;n<r.length;n++)if(r[n]instanceof Date&&+r[n]===+i){e=n;break}}else e=r.indexOf(i);a.checked=e>=0}else a.checked=r;else"radio"==a.type&&("date"==o?i=S.toString(i,"yyyy-MM-dd"):"datetime-local"==o&&(i=S.toString(i,"yyyy-MM-ddTHH:mm:ss")),a.checked=null!==i&&t!==i&&a.value===""+i)},value:function(){var e=this.element,t=e.value;return"checkbox"==e.type&&(t=e.checked),t},destroy:function(){e(this.element).off(N,this._change)}})},E.select={source:E.source.extend({refresh:function(n){var i,r=this,o=r.bindings.source.get();o instanceof M||o instanceof S.data.DataSource?(n=n||{},"add"==n.action?r.add(n.index,n.items):"remove"==n.action?r.remove(n.index,n.items):"itemchange"!=n.action&&n.action!==t||(r.render(),r.bindings.value&&r.bindings.value&&(i=f(r.bindings.value.get(),e(r.element).data("valueField")),null===i?r.element.selectedIndex=-1:r.element.value=i))):r.render()}}),value:_.extend({init:function(t,n,i){_.fn.init.call(this,t,n,i),this._change=F(this.change,this),e(this.element).change(this._change)},parsedValue:function(){var e,t,n,i,r=this.dataType(),o=[];for(n=0,i=this.element.options.length;n<i;n++)t=this.element.options[n],t.selected&&(e=t.attributes.value,e=e&&e.specified?t.value:t.text,o.push(this._parseValue(e,r)));return o},change:function(){var e,n,i,r,o,a,s,l,c=[],u=this.element,d=this.options.valueField||this.options.textField,h=this.options.valuePrimitive;for(o=0,a=u.options.length;o<a;o++)n=u.options[o],n.selected&&(r=n.attributes.value,r=r&&r.specified?n.value:n.text,c.push(d?r:this._parseValue(r,this.dataType())));if(d)for(e=this.bindings.source.get(),e instanceof S.data.DataSource&&(e=e.view()),i=0;i<c.length;i++)for(o=0,a=e.length;o<a;o++)if(s=e[o].get(d),l=s+""===c[i]){c[i]=e[o];break}r=this.bindings[O].get(),r instanceof M?r.splice.apply(r,[0,r.length].concat(c)):this.bindings[O].set(h||!(r instanceof C||null===r||r===t)&&d?c[0].get(d):c[0])},refresh:function(){var e,t,n,i=this.element,r=i.options,o=this.bindings[O].get(),a=o,s=this.options.valueField||this.options.textField,l=!1,c=this.dataType();for(a instanceof M||(a=new M([o])),i.selectedIndex=-1,n=0;n<a.length;n++)for(o=a[n],s&&o instanceof C&&(o=o.get(s)),"date"==c?o=S.toString(a[n],"yyyy-MM-dd"):"datetime-local"==c&&(o=S.toString(a[n],"yyyy-MM-ddTHH:mm:ss")),e=0;e<r.length;e++)t=r[e].value,""===t&&""!==o&&(t=r[e].text),null!=o&&t==""+o&&(r[e].selected=!0,l=!0)},destroy:function(){e(this.element).off(N,this._change)}})},E.widget={events:v.extend({init:function(e,t,n){v.fn.init.call(this,e.element[0],t,n),this.widget=e,this.handlers={}},refresh:function(e){var t=this.bindings.events[e],n=this.handlers[e];n&&this.widget.unbind(e,n),n=t.get(),this.handlers[e]=function(e){e.data=t.source,n(e),e.data===t.source&&delete e.data},this.widget.bind(e,this.handlers[e])},destroy:function(){var e;for(e in this.handlers)this.widget.unbind(e,this.handlers[e])}}),checked:v.extend({init:function(e,t,n){v.fn.init.call(this,e.element[0],t,n),this.widget=e,this._change=F(this.change,this),this.widget.bind(N,this._change)},change:function(){this.bindings[I].set(this.value())},refresh:function(){this.widget.check(this.bindings[I].get()===!0)},value:function(){var e=this.element,t=e.value;return"on"!=t&&"off"!=t&&"checkbox"!=this.element.type||(t=e.checked),t},destroy:function(){this.widget.unbind(N,this._change)}}),start:v.extend({init:function(e,t,n){v.fn.init.call(this,e.element[0],t,n),this._change=F(this.change,this),this.widget=e,this.widget.bind(N,this._change)},change:function(){this.bindings.start.set(this.widget.range().start)},refresh:function(){var e=this,t=this.bindings.start.get(),n=e.widget._range?e.widget._range.end:null;this.widget.range({start:t,end:n})},destroy:function(){this.widget.unbind(N,this._change)}}),end:v.extend({init:function(e,t,n){v.fn.init.call(this,e.element[0],t,n),this._change=F(this.change,this),this.widget=e,this.widget.bind(N,this._change)},change:function(){this.bindings.end.set(this.widget.range().end)},refresh:function(){var e=this,t=this.bindings.end.get(),n=e.widget._range?e.widget._range.start:null;this.widget.range({start:n,end:t})},destroy:function(){this.widget.unbind(N,this._change)}}),visible:v.extend({init:function(e,t,n){v.fn.init.call(this,e.element[0],t,n),this.widget=e},refresh:function(){var e=this.bindings.visible.get();this.widget.wrapper[0].style.display=e?"":"none"}}),invisible:v.extend({init:function(e,t,n){v.fn.init.call(this,e.element[0],t,n),this.widget=e},refresh:function(){var e=this.bindings.invisible.get();this.widget.wrapper[0].style.display=e?"none":""}}),enabled:v.extend({init:function(e,t,n){v.fn.init.call(this,e.element[0],t,n),this.widget=e},refresh:function(){this.widget.enable&&this.widget.enable(this.bindings.enabled.get())}}),disabled:v.extend({init:function(e,t,n){v.fn.init.call(this,e.element[0],t,n),this.widget=e},refresh:function(){this.widget.enable&&this.widget.enable(!this.bindings.disabled.get())}}),source:n("source","dataSource","setDataSource"),value:v.extend({init:function(t,n,i){v.fn.init.call(this,t.element[0],n,i),this.widget=t,this._change=e.proxy(this.change,this),this.widget.first(N,this._change);var r=this.bindings.value.get();this._valueIsObservableObject=!i.valuePrimitive&&(null==r||r instanceof C),this._valueIsObservableArray=r instanceof M,this._initChange=!1},_source:function(){var e;return this.widget.dataItem&&(e=this.widget.dataItem(),e&&e instanceof C)?[e]:(this.bindings.source&&(e=this.bindings.source.get()),(!e||e instanceof S.data.DataSource)&&(e=this.widget.dataSource.flatView()),e)},change:function(){var e,t,n,i,r,o,a,s=this.widget.value(),l=this.options.dataValueField||this.options.dataTextField,c="[object Array]"===D.call(s),u=this._valueIsObservableObject,d=[];if(this._initChange=!0,l)if(""===s&&(u||this.options.valuePrimitive))s=null;else{for(a=this._source(),c&&(t=s.length,d=s.slice(0)),r=0,o=a.length;r<o;r++)if(n=a[r],i=n.get(l),c){for(e=0;e<t;e++)if(i==d[e]){d[e]=n;break}}else if(i==s){s=u?n:i;break}d[0]&&(s=this._valueIsObservableArray?d:u||!l?d[0]:d[0].get(l))}this.bindings.value.set(s),this._initChange=!1},refresh:function(){var e,n,i,r,o,a,s,l,c;if(!this._initChange){if(e=this.widget,n=e.options,i=n.dataTextField,r=n.dataValueField||i,o=this.bindings.value.get(),a=n.text||"",s=0,c=[],o===t&&(o=null),r)if(o instanceof M){for(l=o.length;s<l;s++)c[s]=o[s].get(r);o=c}else o instanceof C&&(a=o.get(i),o=o.get(r));n.autoBind!==!1||n.cascadeFrom||!e.listView||e.listView.bound()?e.value(o):(i!==r||a||(a=o),a||!o&&0!==o||!n.valuePrimitive?e._preselect(o,a):e.value(o))}this._initChange=!1},destroy:function(){this.widget.unbind(N,this._change)}}),dropdowntree:{value:v.extend({init:function(t,n,i){v.fn.init.call(this,t.element[0],n,i),this.widget=t,this._change=e.proxy(this.change,this),this.widget.first(N,this._change),this._initChange=!1},change:function(){var e,n,i,r,o,a,s,l,c,u=this,d=u.bindings[O].get(),h=u.options.valuePrimitive,f=u.widget.treeview.select(),p=u.widget._isMultipleSelection()?u.widget._getAllChecked():u.widget.treeview.dataItem(f)||u.widget.value(),g=h||u.widget.options.autoBind===!1?u.widget.value():p,m=this.options.dataValueField||this.options.dataTextField;if(g=g.slice?g.slice(0):g,u._initChange=!0,d instanceof M){for(e=[],n=g.length,i=0,r=0,o=d[i],a=!1;o!==t;){for(c=!1,r=0;r<n;r++)if(h?a=g[r]==o:(l=g[r],l=l.get?l.get(m):l,a=l==(o.get?o.get(m):o)),
a){g.splice(r,1),n-=1,c=!0;break}c?i+=1:(e.push(o),y(d,i,1),s=i),o=d[i]}y(d,d.length,0,g),e.length&&d.trigger("change",{action:"remove",items:e,index:s}),g.length&&d.trigger("change",{action:"add",items:g,index:d.length-1})}else u.bindings[O].set(g);u._initChange=!1},refresh:function(){if(!this._initChange){var e,t,n=this.options,i=this.widget,r=n.dataValueField||n.dataTextField,o=this.bindings.value.get(),a=o,s=0,l=[];if(r)if(o instanceof M){for(e=o.length;s<e;s++)t=o[s],l[s]=t.get?t.get(r):t;o=l}else o instanceof C&&(o=o.get(r));n.autoBind===!1&&n.valuePrimitive!==!0?i._preselect(a,o):i.value(o)}},destroy:function(){this.widget.unbind(N,this._change)}})},gantt:{dependencies:n("dependencies","dependencies","setDependenciesDataSource")},multiselect:{value:v.extend({init:function(t,n,i){v.fn.init.call(this,t.element[0],n,i),this.widget=t,this._change=e.proxy(this.change,this),this.widget.first(N,this._change),this._initChange=!1},change:function(){var e,n,i,r,o,a,s,l,c,u=this,d=u.bindings[O].get(),h=u.options.valuePrimitive,f=h?u.widget.value():u.widget.dataItems(),p=this.options.dataValueField||this.options.dataTextField;if(f=f.slice(0),u._initChange=!0,d instanceof M){for(e=[],n=f.length,i=0,r=0,o=d[i],a=!1;o!==t;){for(c=!1,r=0;r<n;r++)if(h?a=f[r]==o:(l=f[r],l=l.get?l.get(p):l,a=l==(o.get?o.get(p):o)),a){f.splice(r,1),n-=1,c=!0;break}c?i+=1:(e.push(o),y(d,i,1),s=i),o=d[i]}y(d,d.length,0,f),e.length&&d.trigger("change",{action:"remove",items:e,index:s}),f.length&&d.trigger("change",{action:"add",items:f,index:d.length-1})}else u.bindings[O].set(f);u._initChange=!1},refresh:function(){if(!this._initChange){var e,n,i=this.options,r=this.widget,o=i.dataValueField||i.dataTextField,a=this.bindings.value.get(),s=a,l=0,c=[];if(a===t&&(a=null),o)if(a instanceof M){for(e=a.length;l<e;l++)n=a[l],c[l]=n.get?n.get(o):n;a=c}else a instanceof C&&(a=a.get(o));i.autoBind!==!1||i.valuePrimitive===!0||r._isBound()?r.value(a):r._preselect(s,a)}},destroy:function(){this.widget.unbind(N,this._change)}})},scheduler:{source:n("source","dataSource","setDataSource").extend({dataBound:function(e){var t,n,i,r,o=this.widget,s=e.addedItems||o.items();if(s.length)for(i=e.addedDataItems||o.dataItems(),r=this.bindings.source._parents(),t=0,n=i.length;t<n;t++)a(s[t],i[t],this._ns(e.ns),[i[t]].concat(r))}})},grid:{source:n("source","dataSource","setDataSource").extend({dataBound:function(e){var t,n,i,r,o=this.widget,s=e.addedItems||o.items();if(s.length)for(r=e.addedDataItems||o.dataItems(),i=this.bindings.source._parents(),t=0,n=r.length;t<n;t++)a(s[t],r[t],this._ns(e.ns),[r[t]].concat(i))}})}},y=function(e,t,n,i){var r,o,a,s,l;if(i=i||[],n=n||0,r=i.length,o=e.length,a=[].slice.call(e,t+n),s=a.length,r){for(r=t+r,l=0;t<r;t++)e[t]=i[l],l++;e.length=r}else if(n)for(e.length=t,n+=t;t<n;)delete e[--n];if(s){for(s=t+s,l=0;t<s;t++)e[t]=a[l],l++;e.length=s}for(t=e.length;t<o;)delete e[t],t++},b=P.extend({init:function(e,t){this.target=e,this.options=t,this.toDestroy=[]},bind:function(e){var t,n,i,r,o,a,s=this instanceof w,l=this.binders();for(t in e)t==O?n=!0:t==z?i=!0:t!=A||s?t==I?o=!0:t==H?a=!0:this.applyBinding(t,e,l):r=!0;i&&this.applyBinding(z,e,l),n&&this.applyBinding(O,e,l),o&&this.applyBinding(I,e,l),r&&!s&&this.applyBinding(A,e,l),a&&!s&&this.applyBinding(H,e,l)},binders:function(){return E[this.target.nodeName.toLowerCase()]||{}},applyBinding:function(e,t,n){var i,r=n[e]||E[e],o=this.toDestroy,a=t[e];if(r)if(r=new r(this.target,t,this.options),o.push(r),a instanceof p)r.bind(a),o.push(a);else for(i in a)r.bind(a,i),o.push(a[i]);else if("template"!==e)throw Error("The "+e+" binding is not supported by the "+this.target.nodeName.toLowerCase()+" element")},destroy:function(){var e,t,n=this.toDestroy;for(e=0,t=n.length;e<t;e++)n[e].destroy()}}),w=b.extend({binders:function(){return E.widget[this.target.options.name.toLowerCase()]||{}},applyBinding:function(e,t,n){var i,r=n[e]||E.widget[e],o=this.toDestroy,a=t[e];if(!r)throw Error("The "+e+" binding is not supported by the "+this.target.options.name+" widget");if(r=new r(this.target,t,this.target.options),o.push(r),a instanceof p)r.bind(a),o.push(a);else for(i in a)r.bind(a,i),o.push(a[i])}}),k=/[A-Za-z0-9_\-]+:(\{([^}]*)\}|[^,}]+)/g,x=/\s/g,S.unbind=d,S.bind=s,S.data.binders=E,S.data.Binder=v,S.notify=h,S.observable=function(e){return e instanceof C||(e=new C(e)),e},S.observableHierarchy=function(e){function t(e){var n,i;for(n=0;n<e.length;n++)e[n]._initChildren(),i=e[n].children,i.fetch(),e[n].items=i.data(),t(e[n].items)}var n=S.data.HierarchicalDataSource.create(e);return n.fetch(),t(n.data()),n._data._dataSource=n,n._data}}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.validator.min",["kendo.core.min"],e)}(function(){return function(e,t){function n(t){var n,i=l.ui.validator.ruleResolvers||{},r={};for(n in i)e.extend(!0,r,i[n].resolve(t));return r}function i(e){return e.replace(/&amp/g,"&amp;").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">")}function r(e){return e=(e+"").split("."),e.length>1?e[1].length:0}function o(t){return e(e.parseHTML?e.parseHTML(t):t)}function a(t,n){var i,r,o,a,s=e();for(o=0,a=t.length;o<a;o++)i=t[o],h.test(i.className)&&(r=i.getAttribute(l.attr("for")),r===n&&(s=s.add(i)));return s}var s,l=window.kendo,c=l.ui.Widget,u=".kendoValidator",d="k-invalid-msg",h=RegExp(d,"i"),f="k-invalid",p="k-valid",g=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/i,m=/^(https?|ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i,v=":input:not(:button,[type=submit],[type=reset],[disabled],[readonly])",_=":checkbox:not([disabled],[readonly])",y="[type=number],[type=range]",b="blur",w="name",k="form",x="novalidate",S="validate",T="change",C="validateInput",M=e.proxy,D=function(e,t){return"string"==typeof t&&(t=RegExp("^(?:"+t+")$")),t.test(e)},E=function(e,t,n){var i=e.val();return!e.filter(t).length||""===i||D(i,n)},P=function(e,t){return!!e.length&&null!=e[0].attributes[t]};l.ui.validator||(l.ui.validator={rules:{},messages:{}}),s=c.extend({init:function(t,i){var r=this,o=n(t),a="["+l.attr("validate")+"!=false]";i=i||{},i.rules=e.extend({},l.ui.validator.rules,o.rules,i.rules),i.messages=e.extend({},l.ui.validator.messages,o.messages,i.messages),c.fn.init.call(r,t,i),r._errorTemplate=l.template(r.options.errorTemplate),r.element.is(k)&&r.element.attr(x,x),r._inputSelector=v+a,r._checkboxSelector=_+a,r._errors={},r._attachEvents(),r._isValidated=!1},events:[S,T,C],options:{name:"Validator",errorTemplate:'<span class="k-widget k-tooltip k-tooltip-validation"><span class="k-icon k-i-warning"> </span> #=message#</span>',messages:{required:"{0} is required",pattern:"{0} is not valid",min:"{0} should be greater than or equal to {1}",max:"{0} should be smaller than or equal to {1}",step:"{0} is not valid",email:"{0} is not valid email",url:"{0} is not valid URL",date:"{0} is not valid date",dateCompare:"End date should be greater than or equal to the start date"},rules:{required:function(e){var t=e.filter("[type=checkbox]").length&&!e.is(":checked"),n=e.val();return!(P(e,"required")&&(!n||""===n||0===n.length||t))},pattern:function(e){return!e.filter("[type=text],[type=email],[type=url],[type=tel],[type=search],[type=password]").filter("[pattern]").length||""===e.val()||D(e.val(),e.attr("pattern"))},min:function(e){if(e.filter(y+",["+l.attr("type")+"=number]").filter("[min]").length&&""!==e.val()){var t=parseFloat(e.attr("min"))||0,n=l.parseFloat(e.val());return t<=n}return!0},max:function(e){if(e.filter(y+",["+l.attr("type")+"=number]").filter("[max]").length&&""!==e.val()){var t=parseFloat(e.attr("max"))||0,n=l.parseFloat(e.val());return t>=n}return!0},step:function(e){if(e.filter(y+",["+l.attr("type")+"=number]").filter("[step]").length&&""!==e.val()){var t,n=parseFloat(e.attr("min"))||0,i=parseFloat(e.attr("step"))||1,o=parseFloat(e.val()),a=r(i);return a?(t=Math.pow(10,a),Math.floor((o-n)*t)%(i*t)/Math.pow(100,a)===0):(o-n)%i===0}return!0},email:function(e){return E(e,"[type=email],["+l.attr("type")+"=email]",g)},url:function(e){return E(e,"[type=url],["+l.attr("type")+"=url]",m)},date:function(e){return!e.filter("[type^=date],["+l.attr("type")+"=date]").length||""===e.val()||null!==l.parseDate(e.val(),e.attr(l.attr("format")))}},validateOnBlur:!0},destroy:function(){c.fn.destroy.call(this),this.element.off(u)},value:function(){return!!this._isValidated&&0===this.errors().length},_submit:function(e){return!!this.validate()||(e.stopPropagation(),e.stopImmediatePropagation(),e.preventDefault(),!1)},_checkElement:function(e){var t=this.value();this.validateInput(e),this.value()!==t&&this.trigger(T)},_attachEvents:function(){var t=this;t.element.is(k)&&t.element.on("submit"+u,M(t._submit,t)),t.options.validateOnBlur&&(t.element.is(v)?(t.element.on(b+u,function(){t._checkElement(t.element)}),t.element.is(_)&&t.element.on("click"+u,function(){t._checkElement(t.element)})):(t.element.on(b+u,t._inputSelector,function(){t._checkElement(e(this))}),t.element.on("click"+u,t._checkboxSelector,function(){t._checkElement(e(this))})))},validate:function(){var e,t,n,i,r=!1,o=this.value();if(this._errors={},this.element.is(v))r=this.validateInput(this.element);else{for(i=!1,e=this.element.find(this._inputSelector),t=0,n=e.length;t<n;t++)this.validateInput(e.eq(t))||(i=!0);r=!i}return this.trigger(S,{valid:r}),o!==r&&this.trigger(T),r},validateInput:function(t){var n,r,a,s,c,u,h,g,m,v,_,y;return t=e(t),this._isValidated=!0,n=this,r=n._errorTemplate,a=n._checkValidity(t),s=a.valid,c="."+d,u=t.attr(w)||"",h=n._findMessageContainer(u).add(t.next(c).filter(function(){var t=e(this);return!t.filter("["+l.attr("for")+"]").length||t.attr(l.attr("for"))===u})).hide(),m=!t.attr("aria-invalid"),t.removeAttr("aria-invalid"),s?delete n._errors[u]:(g=n._extractMessage(t,a.key),n._errors[u]=g,v=o(r({message:i(g)})),_=h.attr("id"),n._decorateMessageContainer(v,u),_&&v.attr("id",_),h.replaceWith(v).length||v.insertAfter(t),v.show(),t.attr("aria-invalid",!0)),m!==s&&this.trigger(C,{valid:s,input:t}),t.toggleClass(f,!s),t.toggleClass(p,s),l.widgetInstance(t)&&(y=l.widgetInstance(t)._inputWrapper,y&&(y.toggleClass(f,!s),y.toggleClass(f,!s))),s},hideMessages:function(){var e=this,t="."+d,n=e.element;n.is(v)?n.next(t).hide():n.find(t).hide()},_findMessageContainer:function(t){var n,i,r,o=l.ui.validator.messageLocators,s=e();for(i=0,r=this.element.length;i<r;i++)s=s.add(a(this.element[i].getElementsByTagName("*"),t));for(n in o)s=s.add(o[n].locate(this.element,t));return s},_decorateMessageContainer:function(e,t){var n,i=l.ui.validator.messageLocators;e.addClass(d).attr(l.attr("for"),t||"");for(n in i)i[n].decorate(e,t);e.attr("role","alert")},_extractMessage:function(e,t){var n,i=this,r=i.options.messages[t],o=e.attr(w);return l.ui.Validator.prototype.options.messages[t]||(n=l.isFunction(r)?r(e):r),r=l.isFunction(r)?r(e):r,l.format(e.attr(l.attr(t+"-msg"))||e.attr("validationMessage")||n||e.attr("title")||r||"",o,e.attr(t)||e.attr(l.attr(t)))},_checkValidity:function(e){var t,n=this.options.rules;for(t in n)if(!n[t].call(this,e))return{valid:!1,key:t};return{valid:!0}},errors:function(){var e,t=[],n=this._errors;for(e in n)t.push(n[e]);return t}}),l.ui.plugin(s)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.router.min",["kendo.core.min"],e)}(function(){return function(e,t){function n(e,t){if(!t)return e;e+"/"===t&&(e=t);var n=RegExp("^"+t,"i");return n.test(e)||(e=t+"/"+e),f.protocol+"//"+(f.host+"/"+e).replace(/\/\/+/g,"/")}function i(e){return e?"#!":"#"}function r(e){var t=f.href;return"#!"===e&&t.indexOf("#")>-1&&t.indexOf("#!")<0?null:t.split(e)[1]||""}function o(e,t){return 0===t.indexOf(e)?t.substr(e.length).replace(/\/\//g,"/"):t}function a(e){return e.replace(/^(#)?/,"#")}function s(e){return e.replace(/^(#(!)?)?/,"#!")}var l=window.kendo,c="change",u="back",d="same",h=l.support,f=window.location,p=window.history,g=50,m=l.support.browser.msie,v=/^#*/,_=window.document,y=l.Class.extend({back:function(){m?setTimeout(function(){p.back()}):p.back()},forward:function(){m?setTimeout(function(){p.forward()}):p.forward()},length:function(){return p.length},replaceLocation:function(e){f.replace(e)}}),b=y.extend({init:function(e){this.root=e},navigate:function(e){p.pushState({},_.title,n(e,this.root))},replace:function(e){p.replaceState({},_.title,n(e,this.root))},normalize:function(e){return o(this.root,e)},current:function(){var e=f.pathname;return f.search&&(e+=f.search),o(this.root,e)},change:function(t){e(window).bind("popstate.kendo",t)},stop:function(){e(window).unbind("popstate.kendo")},normalizeCurrent:function(e){var t,o=e.root,a=f.pathname,s=r(i(e.hashBang));o===a+"/"&&(t=o),o===a&&s&&(t=n(s.replace(v,""),o)),t&&p.pushState({},_.title,t)}}),w=y.extend({init:function(e){this._id=l.guid(),this.prefix=i(e),this.fix=e?s:a},navigate:function(e){f.hash=this.fix(e)},replace:function(e){this.replaceLocation(this.fix(e))},normalize:function(e){return e.indexOf(this.prefix)<0?e:e.split(this.prefix)[1]},change:function(t){h.hashChange?e(window).on("hashchange."+this._id,t):this._interval=setInterval(t,g)},stop:function(){e(window).off("hashchange."+this._id),clearInterval(this._interval)},current:function(){return r(this.prefix)},normalizeCurrent:function(e){var t=f.pathname,n=e.root;return!(!e.pushState||n===t)&&(this.replaceLocation(n+this.prefix+o(n,t)),!0)}}),k=l.Observable.extend({start:function(t){if(t=t||{},this.bind([c,u,d],t),!this._started){this._started=!0,t.root=t.root||"/";var n,i=this.createAdapter(t);i.normalizeCurrent(t)||(n=i.current(),e.extend(this,{adapter:i,root:t.root,historyLength:i.length(),current:n,locations:[n]}),i.change(e.proxy(this,"_checkUrl")))}},createAdapter:function(e){return h.pushState&&e.pushState?new b(e.root):new w(e.hashBang)},stop:function(){this._started&&(this.adapter.stop(),this.unbind(c),this._started=!1)},change:function(e){this.bind(c,e)},replace:function(e,t){this._navigate(e,t,function(t){t.replace(e),this.locations[this.locations.length-1]=this.current})},navigate:function(e,n){return"#:back"===e?(this.backCalled=!0,this.adapter.back(),t):(this._navigate(e,n,function(t){t.navigate(e),this.locations.push(this.current)}),t)},_navigate:function(e,n,i){var r=this.adapter;return e=r.normalize(e),this.current===e||this.current===decodeURIComponent(e)?(this.trigger(d),t):(!n&&this.trigger(c,{url:e,decode:!1})||(this.current=e,i.call(this,r),this.historyLength=r.length()),t)},_checkUrl:function(){var e=this.adapter,n=e.current(),i=e.length(),r=this.historyLength===i,o=n===this.locations[this.locations.length-2]&&r,a=this.backCalled,s=this.current;return null===n||this.current===n||this.current===decodeURIComponent(n)||(this.historyLength=i,this.backCalled=!1,this.current=n,o&&this.trigger("back",{url:s,to:n})?(e.forward(),this.current=s,t):this.trigger(c,{url:n,backButtonPressed:!a})?(o?e.forward():(e.back(),this.historyLength--),this.current=s,t):(o?this.locations.pop():this.locations.push(n),t))}});l.History=k,l.History.HistoryAdapter=y,l.History.HashAdapter=w,l.History.PushStateAdapter=b,l.absoluteURL=n,l.history=new k}(window.kendo.jQuery),function(){function e(e,t){return t?e:"([^/]+)"}function t(t,n){return RegExp("^"+t.replace(p,"\\$&").replace(d,"(?:$1)?").replace(h,e).replace(f,"(.*?)")+"$",n?"i":"")}function n(e){return e.replace(/(\?.*)|(#.*)/g,"")}var i=window.kendo,r=i.history,o=i.Observable,a="init",s="routeMissing",l="change",c="back",u="same",d=/\((.*?)\)/g,h=/(\(\?)?:\w+/g,f=/\*\w+/g,p=/[\-{}\[\]+?.,\\\^$|#\s]/g,g=i.Class.extend({init:function(e,n,i){e instanceof RegExp||(e=t(e,i)),this.route=e,this._callback=n},callback:function(e,t,r){var o,a,s=0,l=i.parseQueryStringParams(e);if(l._back=t,e=n(e),o=this.route.exec(e).slice(1),a=o.length,r)for(;s<a;s++)void 0!==o[s]&&(o[s]=decodeURIComponent(o[s]));o.push(l),this._callback.apply(null,o)},worksWith:function(e,t,i){return!!this.route.test(n(e))&&(this.callback(e,t,i),!0)}}),m=o.extend({init:function(e){e||(e={}),o.fn.init.call(this),this.routes=[],this.pushState=e.pushState,this.hashBang=e.hashBang,this.root=e.root,this.ignoreCase=e.ignoreCase!==!1,this.bind([a,s,l,u,c],e)},destroy:function(){r.unbind(l,this._urlChangedProxy),r.unbind(u,this._sameProxy),r.unbind(c,this._backProxy),this.unbind()},start:function(){var e,t=this,n=function(){t._same()},i=function(e){t._back(e)},o=function(e){t._urlChanged(e)};r.start({same:n,change:o,back:i,pushState:t.pushState,hashBang:t.hashBang,root:t.root}),e={url:r.current||"/",preventDefault:$.noop},t.trigger(a,e)||t._urlChanged(e),this._urlChangedProxy=o,this._backProxy=i},route:function(e,t){this.routes.push(new g(e,t,this.ignoreCase))},navigate:function(e,t){i.history.navigate(e,t)},replace:function(e,t){i.history.replace(e,t)},_back:function(e){this.trigger(c,{url:e.url,to:e.to})&&e.preventDefault()},_same:function(){this.trigger(u)},_urlChanged:function(e){var t,n,r,o,a=e.url,c=!!e.decode,u=e.backButtonPressed;if(a||(a="/"),this.trigger(l,{url:e.url,params:i.parseQueryStringParams(e.url),backButtonPressed:u}))return void e.preventDefault();for(t=0,n=this.routes,o=n.length;t<o;t++)if(r=n[t],r.worksWith(a,u,c))return;this.trigger(s,{url:a,params:i.parseQueryStringParams(a),backButtonPressed:u})&&e.preventDefault()}});i.Router=m}(),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.view.min",["kendo.core.min","kendo.binder.min","kendo.fx.min"],e)}(function(){return function(e,t){function n(e){if(!e)return{};var t=e.match(z)||[];return{type:t[1],direction:t[3],reverse:"reverse"===t[5]}}var i=window.kendo,r=i.attr,o=i.ui,a=i.attrValue,s=i.directiveSelector,l=i.Observable,c=i.ui.Widget,u=i.roleSelector,d="SCRIPT",h="init",f="transitionStart",p="transitionEnd",g="show",m="hide",v="attach",_="detach",y=/unrecognized expression/,b=/<body[^>]*>(([\u000a\u000d\u2028\u2029]|.)*)<\/body>/i,w="loadStart",k="loadComplete",x="showStart",S="sameViewRequested",T="viewShow",C="viewTypeDetermined",M="after",D={content:"k-content",view:"k-view",stretchedView:"k-stretched-view",widget:"k-widget",header:"k-header",footer:"k-footer"},E=i.ui.Widget.extend({init:function(e,t){var n=this;t=t||{},n.id=i.guid(),l.fn.init.call(n),n._initOptions(t),n.content=e,n.options.renderOnInit&&c.fn.init.call(n,n._createElement(),t),n.options.wrapInSections&&n._renderSections(),n.tagName=t.tagName||"div",n.model=t.model,n._wrap=t.wrap!==!1,this._evalTemplate=t.evalTemplate||!1,n._fragments={},n.bind([h,g,m,f,p],t)},options:{name:"View",renderOnInit:!1,wrapInSections:!1,detachOnHide:!0,detachOnDestroy:!0},render:function(t){var n=this,r=!n.element;return r&&(n.element=n._createElement()),t&&e(t).append(n.element),r&&(i.bind(n.element,n.model),n.trigger(h)),t&&(n._eachFragment(v),n.trigger(g)),n.element},clone:function(){return new P(this)},triggerBeforeShow:function(){return!0},triggerBeforeHide:function(){return!0},showStart:function(){var e=this,t=e.render();t&&t.css("display",""),this.trigger(g,{view:this})},showEnd:function(){},hideEnd:function(){this.hide()},beforeTransition:function(e){this.trigger(f,{type:e})},afterTransition:function(e){this.trigger(p,{type:e})},hide:function(){this.options.detachOnHide&&(this._eachFragment(_),e(this.element).detach()),this.trigger(m)},destroy:function(){var e=this,t=e.element;t&&(c.fn.destroy.call(e),i.unbind(t),i.destroy(t),e.options.detachOnDestroy&&t.remove())},purge:function(){var t=this;t.destroy(),e(t.element).add(t.content).add(t.wrapper).off().remove()},fragments:function(t){e.extend(this._fragments,t)},_eachFragment:function(e){for(var t in this._fragments)this._fragments[t][e](this,t)},_createElement:function(){var t,n,r,o=this,a="<"+o.tagName+" />";try{n=e(document.getElementById(o.content)||o.content),n[0].tagName===d&&(n=n.html())}catch(s){y.test(s.message)&&(n=o.content)}return"string"==typeof n?(n=n.replace(/^\s+|\s+$/g,""),o._evalTemplate&&(n=i.template(n)(o.model||{})),t=e(a).append(n),o._wrap||(t=t.contents())):(t=n,o._evalTemplate&&(r=e(i.template(e("<div />").append(t.clone(!0)).html())(o.model||{})),e.contains(document,t[0])&&t.replaceWith(r),t=r),o._wrap&&(t=t.wrapAll(a).parent())),t},_renderSections:function(){var e=this;e.options.wrapInSections&&(e._wrapper(),e._createContent(),e._createHeader(),e._createFooter())},_wrapper:function(){var e,t=this,n=t.content;t.wrapper=n.is(u("view"))?t.content:n.wrap("<div data-"+i.ns+'stretch="true" data-'+i.ns+'role="view" data-'+i.ns+'init-widgets="false"></div>').parent(),e=t.wrapper,e.attr("id",t.id),e.addClass(D.view),e.addClass(D.widget),e.attr("role","view")},_createContent:function(){var t,n=this,i=e(n.wrapper),o=u("content");i.children(o)[0]||(t=i.children().filter(function(){var t=e(this);if(!t.is(u("header"))&&!t.is(u("footer")))return t}),t.wrap("<div "+r("role")+'="content"></div>')),this.contentElement=i.children(u("content")),this.contentElement.addClass(D.stretchedView).addClass(D.content)},_createHeader:function(){var e=this,t=e.wrapper;this.header=t.children(u("header")).addClass(D.header)},_createFooter:function(){var e=this,t=e.wrapper;this.footer=t.children(u("footer")).addClass(D.footer)}}),P=i.Class.extend({init:function(t){e.extend(this,{element:t.element.clone(!0),transition:t.transition,id:t.id}),t.element.parent().append(this.element)},hideEnd:function(){this.element.remove()},beforeTransition:e.noop,afterTransition:e.noop}),F=E.extend({init:function(e,t){E.fn.init.call(this,e,t),this.containers={}},container:function(e){var t=this.containers[e];return t||(t=this._createContainer(e),this.containers[e]=t),t},showIn:function(e,t,n){this.container(e).show(t,n)},_createContainer:function(e){var t,n=this.render(),i=n.find(e);if(!i.length&&n.is(e)){if(!n.is(e))throw Error("can't find a container with the specified "+e+" selector");i=n}return t=new A(i),t.bind("accepted",function(e){e.view.render(i)}),t}}),O=E.extend({attach:function(e,t){e.element.find(t).replaceWith(this.render())},detach:function(){}}),z=/^(\w+)(:(\w+))?( (\w+))?$/,A=l.extend({init:function(e){l.fn.init.call(this),this.container=e,this.history=[],this.view=null,this.running=!1},after:function(){this.running=!1,this.trigger("complete",{view:this.view}),this.trigger("after")},end:function(){this.view.showEnd(),this.previous.hideEnd(),this.after()},show:function(e,t,r){if(!e.triggerBeforeShow()||this.view&&!this.view.triggerBeforeHide())return this.trigger("after"),!1;r=r||e.id;var o=this,a=e===o.view?e.clone():o.view,s=o.history,l=s[s.length-2]||{},c=l.id===r,u=t||(c?s[s.length-1].transition:e.transition),d=n(u);return o.running&&o.effect.stop(),"none"===u&&(u=null),o.trigger("accepted",{view:e}),o.view=e,o.previous=a,o.running=!0,c?s.pop():s.push({id:r,transition:u}),a?(u&&i.effects.enabled?(e.element.addClass("k-fx-hidden"),e.showStart(),c&&!t&&(d.reverse=!d.reverse),o.effect=i.fx(e.element).replace(a.element,d.type).beforeTransition(function(){e.beforeTransition("show"),a.beforeTransition("hide")}).afterTransition(function(){e.afterTransition("show"),a.afterTransition("hide")}).direction(d.direction).setReverse(d.reverse),o.effect.run().then(function(){o.end()})):(e.showStart(),o.end()),!0):(e.showStart(),e.showEnd(),o.after(),!0)},destroy:function(){var e=this,t=e.view;t&&t.destroy&&t.destroy()}}),I=l.extend({init:function(t){var n,r,o=this;l.fn.init.call(o),o.options=t,e.extend(o,t),o.sandbox=e("<div />"),r=o.container,n=o._hideViews(r),o.rootView=n.first(),o.layouts={},o.viewContainer=new i.ViewContainer(o.container),o.viewContainer.bind("accepted",function(e){e.view.params=o.params}),o.viewContainer.bind("complete",function(e){o.trigger(T,{view:e.view})}),o.viewContainer.bind(M,function(){o.trigger(M)}),this.bind(this.events,t)},events:[x,M,T,w,k,S,C],destroy:function(){var e,t=this,n=t.viewContainer;i.destroy(t.container);for(e in t.layouts)this.layouts[e].destroy();n&&n.destroy()},view:function(){return this.viewContainer.view},showView:function(e,t,n){if(e=e.replace(RegExp("^"+this.remoteViewURLPrefix),""),""===e&&this.remoteViewURLPrefix&&(e="/"),e.replace(/^#/,"")===this.url)return this.trigger(S),!1;this.trigger(x);var r=this,o=r._findViewElement(e),a=i.widgetInstance(o);return r.url=e.replace(/^#/,""),r.params=n,a&&a.reload&&(a.purge(),o=[]),this.trigger(C,{remote:0===o.length,url:e}),!o[0]||(a||(a=r._createView(o)),r.viewContainer.show(a,t,e))},append:function(e,t){var n,i,o=this.sandbox,a=(t||"").split("?")[0],s=this.container;return b.test(e)&&(e=RegExp.$1),o[0].innerHTML=e,s.append(o.children("script, style")),n=this._hideViews(o),i=n.first(),i.length||(n=i=o.wrapInner("<div data-role=view />").children()),a&&i.hide().attr(r("url"),a),s.append(n),this._createView(i)},_locate:function(e){return this.$angular?s(e):u(e)},_findViewElement:function(e){var t,n=e.split("?")[0];return n?(t=this.container.children("["+r("url")+"='"+n+"']"),t[0]||n.indexOf("/")!==-1||(t=this.container.children("#"===n.charAt(0)?n:"#"+n)),t[0]||(t=this._findViewElementById(e)),t):this.rootView},_findViewElementById:function(e){var t=this.container.children("[id='"+e+"']");return t},_createView:function(e){return this._createSpaView(e)},_createMobileView:function(e){return i.initWidget(e,{defaultTransition:this.transition,loader:this.loader,container:this.container,getLayout:this.getLayoutProxy,modelScope:this.modelScope,reload:a(e,"reload")},o.roles)},_createSpaView:function(e){var t=(this.options||{}).viewOptions||{};return new i.View(e,{renderOnInit:t.renderOnInit,wrap:t.wrap||!1,wrapInSections:t.wrapInSections,detachOnHide:t.detachOnHide,detachOnDestroy:t.detachOnDestroy})},_hideViews:function(e){return e.children(this._locate("view")).hide()}});i.ViewEngine=I,i.ViewContainer=A,i.Fragment=O,i.Layout=F,i.View=E,i.ViewClone=P}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.userevents.min",["kendo.core.min"],e)}(function(){return function(e,t){function n(e,t){var n=e.x.location,i=e.y.location,r=t.x.location,o=t.y.location,a=n-r,s=i-o;return{center:{x:(n+r)/2,y:(i+o)/2},distance:Math.sqrt(a*a+s*s)}}function i(e){var t,n,i,r=[],o=e.originalEvent,s=e.currentTarget,l=0;if(e.api)r.push({id:2,event:e,target:e.target,currentTarget:e.target,location:e,type:"api"});else if(e.type.match(/touch/))for(n=o?o.changedTouches:[],t=n.length;l<t;l++)i=n[l],r.push({location:i,event:e,target:i.target,currentTarget:s,id:i.identifier,type:"touch"});else r.push(a.pointers||a.msPointers?{location:o,event:e,target:e.target,currentTarget:s,id:o.pointerId,type:"pointer"}:{id:1,event:e,target:e.target,currentTarget:s,location:e,type:"mouse"});return r}function r(e){for(var t=o.eventMap.up.split(" "),n=0,i=t.length;n<i;n++)e(t[n])}var o=window.kendo,a=o.support,s=o.Class,l=o.Observable,c=e.now,u=e.extend,d=a.mobileOS,h=d&&d.android,f=800,p=300,g=a.browser.msie?5:0,m="press",v="hold",_="select",y="start",b="move",w="end",k="cancel",x="tap",S="doubleTap",T="release",C="gesturestart",M="gesturechange",D="gestureend",E="gesturetap",P={api:0,touch:0,mouse:9,pointer:9},F=!a.touch||a.mouseAndTouchPresent,O=s.extend({init:function(e,t){var n=this;n.axis=e,n._updateLocationData(t),n.startLocation=n.location,n.velocity=n.delta=0,n.timeStamp=c()},move:function(e){var t=this,n=e["page"+t.axis],i=c(),r=i-t.timeStamp||1;!n&&h||(t.delta=n-t.location,t._updateLocationData(e),t.initialDelta=n-t.startLocation,t.velocity=t.delta/r,t.timeStamp=i)},_updateLocationData:function(e){var t=this,n=t.axis;t.location=e["page"+n],t.client=e["client"+n],t.screen=e["screen"+n]}}),z=s.extend({init:function(e,t,n){u(this,{x:new O("X",n.location),y:new O("Y",n.location),type:n.type,useClickAsTap:e.useClickAsTap,threshold:e.threshold||P[n.type],userEvents:e,target:t,currentTarget:n.currentTarget,initialTouch:n.target,id:n.id,pressEvent:n,_clicks:e._clicks,supportDoubleTap:e.supportDoubleTap,_moved:!1,_finished:!1})},press:function(){this._holdTimeout=setTimeout(e.proxy(this,"_hold"),this.userEvents.minHold),this._trigger(m,this.pressEvent)},_tap:function(e){var t=this;t.userEvents._clicks++,1==t.userEvents._clicks&&(t._clickTimeout=setTimeout(function(){1==t.userEvents._clicks?t._trigger(x,e):t._trigger(S,e),t.userEvents._clicks=0},p))},_hold:function(){this._trigger(v,this.pressEvent)},move:function(e){var t=this;if(!t._finished){if(t.x.move(e.location),t.y.move(e.location),!t._moved){if(t._withinIgnoreThreshold())return;if(A.current&&A.current!==t.userEvents)return t.dispose();t._start(e)}t._finished||t._trigger(b,e)}},end:function(e){this.endTime=c(),this._finished||(this._finished=!0,this._trigger(T,e),this._moved?this._trigger(w,e):this.useClickAsTap||(this.supportDoubleTap?this._tap(e):this._trigger(x,e)),clearTimeout(this._holdTimeout),this.dispose())},dispose:function(){var t=this.userEvents,n=t.touches;this._finished=!0,this.pressEvent=null,clearTimeout(this._holdTimeout),n.splice(e.inArray(this,n),1)},skip:function(){this.dispose()},cancel:function(){this.dispose()},isMoved:function(){return this._moved},_start:function(e){clearTimeout(this._holdTimeout),this.startTime=c(),this._moved=!0,this._trigger(y,e)},_trigger:function(e,t){var n=this,i=t.event,r={touch:n,x:n.x,y:n.y,target:n.target,event:i};n.userEvents.notify(e,r)&&i.preventDefault()},_withinIgnoreThreshold:function(){var e=this.x.initialDelta,t=this.y.initialDelta;return Math.sqrt(e*e+t*t)<=this.threshold}}),A=l.extend({init:function(t,n){var i,s,c,d,h=this,p=o.guid();n=n||{},i=h.filter=n.filter,h.threshold=n.threshold||g,h.minHold=n.minHold||f,h.touches=[],h._maxTouches=n.multiTouch?2:1,h.allowSelection=n.allowSelection,h.captureUpIfMoved=n.captureUpIfMoved,h.useClickAsTap=!n.fastTap&&!a.delayedClick(),h.eventNS=p,h._clicks=0,h.supportDoubleTap=n.supportDoubleTap,t=e(t).handler(h),l.fn.init.call(h),u(h,{element:t,surface:e(n.global&&F?t[0].ownerDocument.documentElement:n.surface||t),stopPropagation:n.stopPropagation,pressed:!1}),h.surface.handler(h).on(o.applyEventMap("move",p),"_move").on(o.applyEventMap("up cancel",p),"_end"),t.on(o.applyEventMap("down",p),i,"_start"),h.useClickAsTap&&t.on(o.applyEventMap("click",p),i,"_click"),(a.pointers||a.msPointers)&&(a.browser.version<11?(s="pinch-zoom double-tap-zoom",t.css("-ms-touch-action",n.touchAction&&"none"!=n.touchAction?s+" "+n.touchAction:s)):t.css("touch-action",n.touchAction||"none")),n.preventDragEvent&&t.on(o.applyEventMap("dragstart",p),o.preventDefault),t.on(o.applyEventMap("mousedown",p),i,{root:t},"_select"),h.captureUpIfMoved&&a.eventCapture&&(c=h.surface[0],d=e.proxy(h.preventIfMoving,h),r(function(e){c.addEventListener(e,d,!0)})),h.bind([m,v,x,S,y,b,w,T,k,C,M,D,E,_],n)},preventIfMoving:function(e){this._isMoved()&&e.preventDefault();
},destroy:function(){var e,t=this;t._destroyed||(t._destroyed=!0,t.captureUpIfMoved&&a.eventCapture&&(e=t.surface[0],r(function(n){e.removeEventListener(n,t.preventIfMoving)})),t.element.kendoDestroy(t.eventNS),t.surface.kendoDestroy(t.eventNS),t.element.removeData("handler"),t.surface.removeData("handler"),t._disposeAll(),t.unbind(),delete t.surface,delete t.element,delete t.currentTarget)},capture:function(){A.current=this},cancel:function(){this._disposeAll(),this.trigger(k)},notify:function(e,t){var i=this,r=i.touches;if(this._isMultiTouch()){switch(e){case b:e=M;break;case w:e=D;break;case x:e=E}u(t,{touches:r},n(r[0],r[1]))}return this.trigger(e,u(t,{type:e}))},press:function(e,t,n){this._apiCall("_start",e,t,n)},move:function(e,t){this._apiCall("_move",e,t)},end:function(e,t){this._apiCall("_end",e,t)},_isMultiTouch:function(){return this.touches.length>1},_maxTouchesReached:function(){return this.touches.length>=this._maxTouches},_disposeAll:function(){for(var e=this.touches;e.length>0;)e.pop().dispose()},_isMoved:function(){return e.grep(this.touches,function(e){return e.isMoved()}).length},_select:function(e){this.allowSelection&&!this.trigger(_,{event:e})||e.preventDefault()},_start:function(t){var n,r,o=this,a=0,s=o.filter,l=i(t),c=l.length,u=t.which;if(!(u&&u>1||o._maxTouchesReached()))for(A.current=null,o.currentTarget=t.currentTarget,o.stopPropagation&&t.stopPropagation();a<c&&!o._maxTouchesReached();a++)r=l[a],n=s?e(r.currentTarget):o.element,n.length&&(r=new z(o,n,r),o.touches.push(r),r.press(),o._isMultiTouch()&&o.notify("gesturestart",{}))},_move:function(e){this._eachTouch("move",e)},_end:function(e){this._eachTouch("end",e)},_click:function(t){var n={touch:{initialTouch:t.target,target:e(t.currentTarget),endTime:c(),x:{location:t.pageX,client:t.clientX},y:{location:t.pageY,client:t.clientY}},x:t.pageX,y:t.pageY,target:e(t.currentTarget),event:t,type:"tap"};this.trigger("tap",n)&&t.preventDefault()},_eachTouch:function(e,t){var n,r,o,a,s=this,l={},c=i(t),u=s.touches;for(n=0;n<u.length;n++)r=u[n],l[r.id]=r;for(n=0;n<c.length;n++)o=c[n],a=l[o.id],a&&a[e](o)},_apiCall:function(t,n,i,r){this[t]({api:!0,pageX:n,pageY:i,clientX:n,clientY:i,target:e(r||this.element)[0],stopPropagation:e.noop,preventDefault:e.noop})}});A.defaultThreshold=function(e){g=e},A.minHold=function(e){f=e},o.getTouches=i,o.touchDelta=n,o.UserEvents=A}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.draganddrop.min",["kendo.core.min","kendo.userevents.min"],e)}(function(){return function(e,t){function n(t,n){try{return e.contains(t,n)||t==n}catch(i){return!1}}function i(e,t){return parseInt(e.css(t),10)||0}function r(e,t){return Math.min(Math.max(e,t.min),t.max)}function o(e,t){var n=D(e),r=_._outerWidth,o=_._outerHeight,a=n.left+i(e,"borderLeftWidth")+i(e,"paddingLeft"),s=n.top+i(e,"borderTopWidth")+i(e,"paddingTop"),l=a+e.width()-r(t,!0),c=s+e.height()-o(t,!0);return{x:{min:a,max:l},y:{min:s,max:c}}}function a(n,i,r){for(var o,a,s=0,l=i&&i.length,c=r&&r.length;n&&n.parentNode;){for(s=0;s<l;s++)if(o=i[s],o.element[0]===n)return{target:o,targetElement:n};for(s=0;s<c;s++)if(a=r[s],e.contains(a.element[0],n)&&y.matchesSelector.call(n,a.options.filter))return{target:a,targetElement:n};n=n.parentNode}return t}function s(e,t){var n,i=t.options.group,r=e[i];if(x.fn.destroy.call(t),r.length>1){for(n=0;n<r.length;n++)if(r[n]==t){r.splice(n,1);break}}else r.length=0,delete e[i]}function l(e){var t,n,i,r=c()[0];return e[0]===r?(n=r.scrollTop,i=r.scrollLeft,{top:n,left:i,bottom:n+w.height(),right:i+w.width()}):(t=e.offset(),t.bottom=t.top+e.height(),t.right=t.left+e.width(),t)}function c(){return e(_.support.browser.edge||_.support.browser.safari?b.body:b.documentElement)}function u(t){var n,i=c();if(!t||t===b.body||t===b.documentElement)return i;for(n=e(t)[0];n&&!_.isScrollable(n)&&n!==b.body;)n=n.parentNode;return n===b.body?i:e(n)}function d(e,t,n){var i={x:0,y:0},r=50;return e-n.left<r?i.x=-(r-(e-n.left)):n.right-e<r&&(i.x=r-(n.right-e)),t-n.top<r?i.y=-(r-(t-n.top)):n.bottom-t<r&&(i.y=r-(n.bottom-t)),i}var h,f,p,g,m,v,_=window.kendo,y=_.support,b=window.document,w=e(window),k=_.Class,x=_.ui.Widget,S=_.Observable,T=_.UserEvents,C=e.proxy,M=e.extend,D=_.getOffset,E={},P={},F={},O=_.elementUnderCursor,z="keyup",A="change",I="dragstart",H="hold",V="drag",R="dragend",N="dragcancel",B="hintDestroyed",$="dragenter",L="dragleave",j="drop",W=S.extend({init:function(t,n){var i=this,r=t[0];i.capture=!1,r.addEventListener?(e.each(_.eventMap.down.split(" "),function(){r.addEventListener(this,C(i._press,i),!0)}),e.each(_.eventMap.up.split(" "),function(){r.addEventListener(this,C(i._release,i),!0)})):(e.each(_.eventMap.down.split(" "),function(){r.attachEvent(this,C(i._press,i))}),e.each(_.eventMap.up.split(" "),function(){r.attachEvent(this,C(i._release,i))})),S.fn.init.call(i),i.bind(["press","release"],n||{})},captureNext:function(){this.capture=!0},cancelCapture:function(){this.capture=!1},_press:function(e){var t=this;t.trigger("press"),t.capture&&e.preventDefault()},_release:function(e){var t=this;t.trigger("release"),t.capture&&(e.preventDefault(),t.cancelCapture())}}),q=S.extend({init:function(t){var n=this;S.fn.init.call(n),n.forcedEnabled=!1,e.extend(n,t),n.scale=1,n.horizontal?(n.measure="offsetWidth",n.scrollSize="scrollWidth",n.axis="x"):(n.measure="offsetHeight",n.scrollSize="scrollHeight",n.axis="y")},makeVirtual:function(){e.extend(this,{virtual:!0,forcedEnabled:!0,_virtualMin:0,_virtualMax:0})},virtualSize:function(e,t){this._virtualMin===e&&this._virtualMax===t||(this._virtualMin=e,this._virtualMax=t,this.update())},outOfBounds:function(e){return e>this.max||e<this.min},forceEnabled:function(){this.forcedEnabled=!0},getSize:function(){return this.container[0][this.measure]},getTotal:function(){return this.element[0][this.scrollSize]},rescale:function(e){this.scale=e},update:function(e){var t=this,n=t.virtual?t._virtualMax:t.getTotal(),i=n*t.scale,r=t.getSize();(0!==n||t.forcedEnabled)&&(t.max=t.virtual?-t._virtualMin:0,t.size=r,t.total=i,t.min=Math.min(t.max,r-i),t.minScale=r/n,t.centerOffset=(i-r)/2,t.enabled=t.forcedEnabled||i>r,e||t.trigger(A,t))}}),U=S.extend({init:function(e){var t=this;S.fn.init.call(t),t.x=new q(M({horizontal:!0},e)),t.y=new q(M({horizontal:!1},e)),t.container=e.container,t.forcedMinScale=e.minScale,t.maxScale=e.maxScale||100,t.bind(A,e)},rescale:function(e){this.x.rescale(e),this.y.rescale(e),this.refresh()},centerCoordinates:function(){return{x:Math.min(0,-this.x.centerOffset),y:Math.min(0,-this.y.centerOffset)}},refresh:function(){var e=this;e.x.update(),e.y.update(),e.enabled=e.x.enabled||e.y.enabled,e.minScale=e.forcedMinScale||Math.min(e.x.minScale,e.y.minScale),e.fitScale=Math.max(e.x.minScale,e.y.minScale),e.trigger(A)}}),G=S.extend({init:function(e){var t=this;M(t,e),S.fn.init.call(t)},outOfBounds:function(){return this.dimension.outOfBounds(this.movable[this.axis])},dragMove:function(e){var t=this,n=t.dimension,i=t.axis,r=t.movable,o=r[i]+e;n.enabled&&((o<n.min&&e<0||o>n.max&&e>0)&&(e*=t.resistance),r.translateAxis(i,e),t.trigger(A,t))}}),Q=k.extend({init:function(t){var n,i,r,o,a=this;M(a,{elastic:!0},t),r=a.elastic?.5:0,o=a.movable,a.x=n=new G({axis:"x",dimension:a.dimensions.x,resistance:r,movable:o}),a.y=i=new G({axis:"y",dimension:a.dimensions.y,resistance:r,movable:o}),a.userEvents.bind(["press","move","end","gesturestart","gesturechange"],{gesturestart:function(e){a.gesture=e,a.offset=a.dimensions.container.offset()},press:function(t){e(t.event.target).closest("a").is("[data-navigate-on-press=true]")&&t.sender.cancel()},gesturechange:function(e){var t,r,s,l=a.gesture,c=l.center,u=e.center,d=e.distance/l.distance,h=a.dimensions.minScale,f=a.dimensions.maxScale;o.scale<=h&&d<1&&(d+=.8*(1-d)),o.scale*d>=f&&(d=f/o.scale),r=o.x+a.offset.left,s=o.y+a.offset.top,t={x:(r-c.x)*d+u.x-r,y:(s-c.y)*d+u.y-s},o.scaleWith(d),n.dragMove(t.x),i.dragMove(t.y),a.dimensions.rescale(o.scale),a.gesture=e,e.preventDefault()},move:function(e){e.event.target.tagName.match(/textarea|input/i)||(n.dimension.enabled||i.dimension.enabled?(n.dragMove(e.x.delta),i.dragMove(e.y.delta),e.preventDefault()):e.touch.skip())},end:function(e){e.preventDefault()}})}}),Y=y.transitions.prefix+"Transform";f=y.hasHW3D?function(e,t,n){return"translate3d("+e+"px,"+t+"px,0) scale("+n+")"}:function(e,t,n){return"translate("+e+"px,"+t+"px) scale("+n+")"},p=S.extend({init:function(t){var n=this;S.fn.init.call(n),n.element=e(t),n.element[0].style.webkitTransformOrigin="left top",n.x=0,n.y=0,n.scale=1,n._saveCoordinates(f(n.x,n.y,n.scale))},translateAxis:function(e,t){this[e]+=t,this.refresh()},scaleTo:function(e){this.scale=e,this.refresh()},scaleWith:function(e){this.scale*=e,this.refresh()},translate:function(e){this.x+=e.x,this.y+=e.y,this.refresh()},moveAxis:function(e,t){this[e]=t,this.refresh()},moveTo:function(e){M(this,e),this.refresh()},refresh:function(){var e,t=this,n=t.x,i=t.y;t.round&&(n=Math.round(n),i=Math.round(i)),e=f(n,i,t.scale),e!=t.coordinates&&(_.support.browser.msie&&_.support.browser.version<10?(t.element[0].style.position="absolute",t.element[0].style.left=t.x+"px",t.element[0].style.top=t.y+"px"):t.element[0].style[Y]=e,t._saveCoordinates(e),t.trigger(A))},_saveCoordinates:function(e){this.coordinates=e}}),g=x.extend({init:function(e,t){var n,i=this;x.fn.init.call(i,e,t),n=i.options.group,n in P?P[n].push(i):P[n]=[i]},events:[$,L,j],options:{name:"DropTarget",group:"default"},destroy:function(){s(P,this)},_trigger:function(e,t){var n=this,i=E[n.options.group];if(i)return n.trigger(e,M({},t.event,{draggable:i,dropTarget:t.dropTarget}))},_over:function(e){this._trigger($,e)},_out:function(e){this._trigger(L,e)},_drop:function(e){var t=this,n=E[t.options.group];n&&(n.dropped=!t._trigger(j,e))}}),g.destroyGroup=function(e){var t,n=P[e]||F[e];if(n){for(t=0;t<n.length;t++)x.fn.destroy.call(n[t]);n.length=0,delete P[e],delete F[e]}},g._cache=P,m=g.extend({init:function(e,t){var n,i=this;x.fn.init.call(i,e,t),n=i.options.group,n in F?F[n].push(i):F[n]=[i]},destroy:function(){s(F,this)},options:{name:"DropTargetArea",group:"default",filter:null}}),v=x.extend({init:function(e,t){var n=this;x.fn.init.call(n,e,t),n._activated=!1,n.userEvents=new T(n.element,{global:!0,allowSelection:!0,filter:n.options.filter,threshold:n.options.distance,start:C(n._start,n),hold:C(n._hold,n),move:C(n._drag,n),end:C(n._end,n),cancel:C(n._cancel,n),select:C(n._select,n)}),n._afterEndHandler=C(n._afterEnd,n),n._captureEscape=C(n._captureEscape,n)},events:[H,I,V,R,N,B],options:{name:"Draggable",distance:_.support.touch?0:5,group:"default",cursorOffset:null,axis:null,container:null,filter:null,ignore:null,holdToDrag:!1,autoScroll:!1,dropped:!1},cancelHold:function(){this._activated=!1},_captureEscape:function(e){var t=this;e.keyCode===_.keys.ESC&&(t._trigger(N,{event:e}),t.userEvents.cancel())},_updateHint:function(t){var n,i=this,o=i.options,a=i.boundaries,s=o.axis,l=i.options.cursorOffset;l?n={left:t.x.location+l.left,top:t.y.location+l.top}:(i.hintOffset.left+=t.x.delta,i.hintOffset.top+=t.y.delta,n=e.extend({},i.hintOffset)),a&&(n.top=r(n.top,a.y),n.left=r(n.left,a.x)),"x"===s?delete n.top:"y"===s&&delete n.left,i.hint.css(n)},_shouldIgnoreTarget:function(t){var n=this.options.ignore;return n&&e(t).is(n)},_select:function(e){this._shouldIgnoreTarget(e.event.target)||e.preventDefault()},_start:function(n){var i,r=this,a=r.options,s=a.container?e(a.container):null,l=a.hint;return this._shouldIgnoreTarget(n.touch.initialTouch)||a.holdToDrag&&!r._activated?(r.userEvents.cancel(),t):(r.currentTarget=n.target,r.currentTargetOffset=D(r.currentTarget),l&&(r.hint&&r.hint.stop(!0,!0).remove(),r.hint=_.isFunction(l)?e(l.call(r,r.currentTarget)):l,i=D(r.currentTarget),r.hintOffset=i,r.hint.css({position:"absolute",zIndex:2e4,left:i.left,top:i.top}).appendTo(b.body),r.angular("compile",function(){r.hint.removeAttr("ng-repeat");for(var t=e(n.target);!t.data("$$kendoScope")&&t.length;)t=t.parent();return{elements:r.hint.get(),scopeFrom:t.data("$$kendoScope")}})),E[a.group]=r,r.dropped=!1,s&&(r.boundaries=o(s,r.hint)),e(b).on(z,r._captureEscape),r._trigger(I,n)&&(r.userEvents.cancel(),r._afterEnd()),r.userEvents.capture(),t)},_hold:function(e){this.currentTarget=e.target,this.options.holdToDrag&&this._trigger(H,e)?this.userEvents.cancel():this._activated=!0},_drag:function(t){var n,i;t.preventDefault(),n=this._elementUnderCursor(t),this.options.autoScroll&&this._cursorElement!==n&&(this._scrollableParent=u(n),this._cursorElement=n),this._lastEvent=t,this._processMovement(t,n),this.options.autoScroll&&this._scrollableParent[0]&&(i=d(t.x.location,t.y.location,l(this._scrollableParent)),this._scrollCompenstation=e.extend({},this.hintOffset),this._scrollVelocity=i,0===i.y&&0===i.x?(clearInterval(this._scrollInterval),this._scrollInterval=null):this._scrollInterval||(this._scrollInterval=setInterval(e.proxy(this,"_autoScroll"),50))),this.hint&&this._updateHint(t)},_processMovement:function(n,i){this._withDropTarget(i,function(i,r){if(!i)return h&&(h._trigger(L,M(n,{dropTarget:e(h.targetElement)})),h=null),t;if(h){if(r===h.targetElement)return;h._trigger(L,M(n,{dropTarget:e(h.targetElement)}))}i._trigger($,M(n,{dropTarget:e(r)})),h=M(i,{targetElement:r})}),this._trigger(V,M(n,{dropTarget:h,elementUnderCursor:i}))},_autoScroll:function(){var e,t,n,i,r,o,a,s,l=this._scrollableParent[0],u=this._scrollVelocity,d=this._scrollCompenstation;l&&(e=this._elementUnderCursor(this._lastEvent),this._processMovement(this._lastEvent,e),i=l===c()[0],i?(t=b.body.scrollHeight>w.height(),n=b.body.scrollWidth>w.width()):(t=l.offsetHeight<=l.scrollHeight,n=l.offsetWidth<=l.scrollWidth),r=l.scrollTop+u.y,o=t&&r>0&&r<l.scrollHeight,a=l.scrollLeft+u.x,s=n&&a>0&&a<l.scrollWidth,o&&(l.scrollTop+=u.y),s&&(l.scrollLeft+=u.x),this.hint&&i&&(s||o)&&(o&&(d.top+=u.y),s&&(d.left+=u.x),this.hint.css(d)))},_end:function(t){this._withDropTarget(this._elementUnderCursor(t),function(n,i){n&&(n._drop(M({},t,{dropTarget:e(i)})),h=null)}),this._cancel(this._trigger(R,t))},_cancel:function(e){var t=this;t._scrollableParent=null,this._cursorElement=null,clearInterval(this._scrollInterval),t._activated=!1,t.hint&&!t.dropped?setTimeout(function(){t.hint.stop(!0,!0),e?t._afterEndHandler():t.hint.animate(t.currentTargetOffset,"fast",t._afterEndHandler)},0):t._afterEnd()},_trigger:function(e,t){var n=this;return n.trigger(e,M({},t.event,{x:t.x,y:t.y,currentTarget:n.currentTarget,initialTarget:t.touch?t.touch.initialTouch:null,dropTarget:t.dropTarget,elementUnderCursor:t.elementUnderCursor}))},_elementUnderCursor:function(e){var t=O(e),i=this.hint;return i&&n(i[0],t)&&(i.hide(),t=O(e),t||(t=O(e)),i.show()),t},_withDropTarget:function(e,t){var n,i=this.options.group,r=P[i],o=F[i];(r&&r.length||o&&o.length)&&(n=a(e,r,o),n?t(n.target,n.targetElement):t())},destroy:function(){var e=this;x.fn.destroy.call(e),e._afterEnd(),e.userEvents.destroy(),this._scrollableParent=null,this._cursorElement=null,clearInterval(this._scrollInterval),e.currentTarget=null},_afterEnd:function(){var t=this;t.hint&&t.hint.remove(),delete E[t.options.group],t.trigger("destroy"),t.trigger(B),e(b).off(z,t._captureEscape)}}),_.ui.plugin(g),_.ui.plugin(m),_.ui.plugin(v),_.TapCapture=W,_.containerBoundaries=o,M(_.ui,{Pane:Q,PaneDimensions:U,Movable:p}),_.ui.Draggable.utils={autoScrollVelocity:d,scrollableViewPort:l,findScrollableParent:u}}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.popup.min",["kendo.core.min"],e)}(function(){return function(e,t){function n(t,n){return!(!t||!n)&&(t===n||e.contains(t,n))}var i,r,o,a,s=window.kendo,l=s.ui,c=l.Widget,u=s.Class,d=s.support,h=s.getOffset,f=s._outerWidth,p=s._outerHeight,g="open",m="close",v="deactivate",_="activate",y="center",b="left",w="right",k="top",x="bottom",S="absolute",T="hidden",C="body",M="location",D="position",E="visible",P="effects",F="k-state-active",O="k-state-border",z=/k-state-border-(\w+)/,A=".k-picker-wrap, .k-dropdown-wrap, .k-link",I="down",H=e(document.documentElement),V=e.proxy,R=e(window),N="scroll",B=d.transitions.css,$=B+"transform",L=e.extend,j=".kendoPopup",W=["font-size","font-family","font-stretch","font-style","font-weight","line-height"],q=c.extend({init:function(t,n){var i,r=this;n=n||{},n.isRtl&&(n.origin=n.origin||x+" "+w,n.position=n.position||k+" "+w),c.fn.init.call(r,t,n),t=r.element,n=r.options,r.collisions=n.collision?n.collision.split(" "):[],r.downEvent=s.applyEventMap(I,s.guid()),1===r.collisions.length&&r.collisions.push(r.collisions[0]),i=e(r.options.anchor).closest(".k-popup,.k-group").filter(":not([class^=km-])"),n.appendTo=e(e(n.appendTo)[0]||i[0]||document.body),r.element.hide().addClass("k-popup k-group k-reset").toggleClass("k-rtl",!!n.isRtl).css({position:S}).appendTo(n.appendTo).attr("aria-hidden",!0).on("mouseenter"+j,function(){r._hovered=!0}).on("wheel"+j,function(t){var n=e(t.target).find(".k-list"),i=n.parent();n.length&&n.is(":visible")&&(0===i.scrollTop()&&t.originalEvent.deltaY<0||i.scrollTop()===i.prop("scrollHeight")-i.prop("offsetHeight")&&t.originalEvent.deltaY>0)&&t.preventDefault()}).on("mouseleave"+j,function(){r._hovered=!1}),r.wrapper=e(),n.animation===!1&&(n.animation={open:{effects:{}},close:{hide:!0,effects:{}}}),L(n.animation.open,{complete:function(){r.wrapper.css({overflow:E}),r._activated=!0,r._trigger(_)}}),L(n.animation.close,{complete:function(){r._animationClose()}}),r._mousedownProxy=function(e){r._mousedown(e)},r._resizeProxy=d.mobileOS.android?function(e){setTimeout(function(){r._resize(e)},600)}:function(e){r._resize(e)},n.toggleTarget&&e(n.toggleTarget).on(n.toggleEvent+j,e.proxy(r.toggle,r))},events:[g,_,m,v],options:{name:"Popup",toggleEvent:"click",origin:x+" "+b,position:k+" "+b,anchor:C,appendTo:null,collision:"flip fit",viewport:window,copyAnchorStyles:!0,autosize:!1,modal:!1,adjustSize:{width:0,height:0},animation:{open:{effects:"slideIn:down",transition:!0,duration:200},close:{duration:100,hide:!0}}},_animationClose:function(){var e=this,t=e.wrapper.data(M);e.wrapper.hide(),t&&e.wrapper.css(t),e.options.anchor!=C&&e._hideDirClass(),e._closing=!1,e._trigger(v)},destroy:function(){var t,n=this,i=n.options,r=n.element.off(j);c.fn.destroy.call(n),i.toggleTarget&&e(i.toggleTarget).off(j),i.modal||(H.off(n.downEvent,n._mousedownProxy),n._toggleResize(!1)),s.destroy(n.element.children()),r.removeData(),i.appendTo[0]===document.body&&(t=r.parent(".k-animation-container"),t[0]?t.remove():r.remove())},open:function(t,n){var i,r,o=this,a={isFixed:!isNaN(parseInt(n,10)),x:t,y:n},l=o.element,c=o.options,u=e(c.anchor),h=l[0]&&l.hasClass("km-widget");if(!o.visible()){if(c.copyAnchorStyles&&(h&&"font-size"==W[0]&&W.shift(),l.css(s.getComputedStyles(u[0],W))),l.data("animating")||o._trigger(g))return;o._activated=!1,c.modal||(H.off(o.downEvent,o._mousedownProxy).on(o.downEvent,o._mousedownProxy),o._toggleResize(!1),o._toggleResize(!0)),o.wrapper=r=s.wrap(l,c.autosize).css({overflow:T,display:"block",position:S}).attr("aria-hidden",!1),d.mobileOS.android&&r.css($,"translatez(0)"),r.css(D),e(c.appendTo)[0]==document.body&&r.css(k,"-10000px"),o.flipped=o._position(a),i=o._openAnimation(),c.anchor!=C&&o._showDirClass(i),l.data(P,i.effects).kendoStop(!0).kendoAnimate(i).attr("aria-hidden",!1)}},_location:function(t){var n,i,r=this,o=r.element,a=r.options,l=e(a.anchor),c=o[0]&&o.hasClass("km-widget");return a.copyAnchorStyles&&(c&&"font-size"==W[0]&&W.shift(),o.css(s.getComputedStyles(l[0],W))),r.wrapper=n=s.wrap(o,a.autosize).css({overflow:T,display:"block",position:S}),d.mobileOS.android&&n.css($,"translatez(0)"),n.css(D),e(a.appendTo)[0]==document.body&&n.css(k,"-10000px"),r._position(t||{}),i=n.offset(),{width:s._outerWidth(n),height:s._outerHeight(n),left:i.left,top:i.top}},_openAnimation:function(){var e=L(!0,{},this.options.animation.open);return e.effects=s.parseEffects(e.effects,this.flipped),e},_hideDirClass:function(){var t=e(this.options.anchor),n=((t.attr("class")||"").match(z)||["","down"])[1],i=O+"-"+n;t.removeClass(i).children(A).removeClass(F).removeClass(i),this.element.removeClass(O+"-"+s.directions[n].reverse)},_showDirClass:function(t){var n=t.effects.slideIn?t.effects.slideIn.direction:"down",i=O+"-"+n;e(this.options.anchor).addClass(i).children(A).addClass(F).addClass(i),this.element.addClass(O+"-"+s.directions[n].reverse)},position:function(){this.visible()&&(this.flipped=this._position())},toggle:function(){var e=this;e[e.visible()?m:g]()},visible:function(){return this.element.is(":"+E)},close:function(n){var i,r,o,a,l=this,c=l.options;if(l.visible()){if(i=l.wrapper[0]?l.wrapper:s.wrap(l.element).hide(),l._toggleResize(!1),l._closing||l._trigger(m))return l._toggleResize(!0),t;l.element.find(".k-popup").each(function(){var t=e(this),i=t.data("kendoPopup");i&&i.close(n)}),H.off(l.downEvent,l._mousedownProxy),n?r={hide:!0,effects:{}}:(r=L(!0,{},c.animation.close),o=l.element.data(P),a=r.effects,!a&&!s.size(a)&&o&&s.size(o)&&(r.effects=o,r.reverse=!0),l._closing=!0),l.element.kendoStop(!0).attr("aria-hidden",!0),i.css({overflow:T}).attr("aria-hidden",!0),l.element.kendoAnimate(r),n&&l._animationClose()}},_trigger:function(e){return this.trigger(e,{type:e})},_resize:function(e){var t=this;d.resize.indexOf(e.type)!==-1?(clearTimeout(t._resizeTimeout),t._resizeTimeout=setTimeout(function(){t._position(),t._resizeTimeout=null},50)):(!t._hovered||t._activated&&t.element.hasClass("k-list-container"))&&t.close()},_toggleResize:function(e){var t=e?"on":"off",n=d.resize;d.mobileOS.ios||d.mobileOS.android||(n+=" "+N),e&&!this.scrollableParents&&(this.scrollableParents=this._scrollableParents()),this.scrollableParents&&this.scrollableParents.length&&this.scrollableParents[t](N,this._resizeProxy),R[t](n,this._resizeProxy)},_mousedown:function(t){var i=this,r=i.element[0],o=i.options,a=e(o.anchor)[0],l=o.toggleTarget,c=s.eventTarget(t),u=e(c).closest(".k-popup"),d=u.parent().parent(".km-shim").length;u=u[0],!d&&u&&u!==i.element[0]||"popover"!==e(t.target).closest("a").data("rel")&&(n(r,c)||n(a,c)||l&&n(e(l)[0],c)||i.close())},_fit:function(e,t,n){var i=0;return e+t>n&&(i=n-(e+t)),e<0&&(i=-e),i},_flip:function(e,t,n,i,r,o,a){var s=0;return a=a||t,o!==r&&o!==y&&r!==y&&(e+a>i&&(s+=-(n+t)),e+s<0&&(s+=n+t)),s},_scrollableParents:function(){return e(this.options.anchor).parentsUntil("body").filter(function(e,t){return s.isScrollable(t)})},_position:function(t){var n,i,r,o,a,l,c,u,g,m,v,_,y,b,w,k,x,T=this,C=T.element,E=T.wrapper,P=T.options,F=e(P.viewport),O=d.zoomLevel(),z=!!(F[0]==window&&window.innerWidth&&O<=1.02),A=e(P.anchor),I=P.origin.toLowerCase().split(" "),H=P.position.toLowerCase().split(" "),V=T.collisions,R=10002,N=0,B=document.documentElement;if(a=P.viewport===window?{top:window.pageYOffset||document.documentElement.scrollTop||0,left:window.pageXOffset||document.documentElement.scrollLeft||0}:F.offset(),z?(l=window.innerWidth,c=window.innerHeight):(l=F.width(),c=F.height()),z&&B.scrollHeight-B.clientHeight>0&&(u=P.isRtl?-1:1,l-=u*s.support.scrollbar()),n=A.parents().filter(E.siblings()),n[0])if(r=Math.max(+n.css("zIndex"),0))R=r+10;else for(i=A.parentsUntil(n),o=i.length;N<o;N++)r=+e(i[N]).css("zIndex"),r&&R<r&&(R=r+10);return E.css("zIndex",R),E.css(t&&t.isFixed?{left:t.x,top:t.y}:T._align(I,H)),g=h(E,D,A[0]===E.offsetParent()[0]),m=h(E),v=A.offsetParent().parent(".k-animation-container,.k-popup,.k-group"),v.length&&(g=h(E,D,!0),m=h(E)),m.top-=a.top,m.left-=a.left,T.wrapper.data(M)||E.data(M,L({},g)),_=L({},m),y=L({},g),b=P.adjustSize,"fit"===V[0]&&(y.top+=T._fit(_.top,p(E)+b.height,c/O)),"fit"===V[1]&&(y.left+=T._fit(_.left,f(E)+b.width,l/O)),w=L({},y),k=p(C),x=p(E),!E.height()&&k&&(x+=k),"flip"===V[0]&&(y.top+=T._flip(_.top,k,p(A),c/O,I[0],H[0],x)),"flip"===V[1]&&(y.left+=T._flip(_.left,f(C),f(A),l/O,I[1],H[1],f(E))),C.css(D,S),E.css(y),y.left!=w.left||y.top!=w.top},_align:function(t,n){var i,r=this,o=r.wrapper,a=e(r.options.anchor),s=t[0],l=t[1],c=n[0],u=n[1],d=h(a),g=e(r.options.appendTo),m=f(o),v=p(o)||p(o.children().first()),_=f(a),b=p(a),k=d.top,S=d.left,T=Math.round;return g[0]!=document.body&&(i=h(g),k-=i.top,S-=i.left),s===x&&(k+=b),s===y&&(k+=T(b/2)),c===x&&(k-=v),c===y&&(k-=T(v/2)),l===w&&(S+=_),l===y&&(S+=T(_/2)),u===w&&(S-=m),u===y&&(S-=T(m/2)),{top:k,left:S}}});l.plugin(q),i=s.support.stableSort,r="kendoTabKeyTrap",o="a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, object, embed, [tabindex], *[contenteditable]",a=u.extend({init:function(t){this.element=e(t),this.element.autoApplyNS(r)},trap:function(){this.element.on("keydown",V(this._keepInTrap,this))},removeTrap:function(){this.element.kendoDestroy(r)},destroy:function(){this.element.kendoDestroy(r),this.element=t},shouldTrap:function(){return!0},_keepInTrap:function(e){var t,n,i;9===e.which&&this.shouldTrap()&&!e.isDefaultPrevented()&&(t=this._focusableElements(),n=this._sortFocusableElements(t),i=this._nextFocusable(e,n),this._focus(i),e.preventDefault())},_focusableElements:function(){var t=this.element.find(o).filter(function(t,n){return n.tabIndex>=0&&e(n).is(":visible")&&!e(n).is("[disabled]")});return this.element.is("[tabindex]")&&t.push(this.element[0]),t},_sortFocusableElements:function(e){var t,n;return i?t=e.sort(function(e,t){return e.tabIndex-t.tabIndex}):(n="__k_index",e.each(function(e,t){t.setAttribute(n,e)}),t=e.sort(function(e,t){return e.tabIndex===t.tabIndex?parseInt(e.getAttribute(n),10)-parseInt(t.getAttribute(n),10):e.tabIndex-t.tabIndex}),e.removeAttr(n)),t},_nextFocusable:function(e,t){var n=t.length,i=t.index(e.target);return t.get((i+(e.shiftKey?-1:1))%n)},_focus:function(e){return"IFRAME"==e.nodeName?(e.contentWindow.document.body.focus(),t):(e.focus(),"INPUT"==e.nodeName&&e.setSelectionRange&&this._haveSelectionRange(e)&&e.setSelectionRange(0,e.value.length),t)},_haveSelectionRange:function(e){var t=e.type.toLowerCase();return"text"===t||"search"===t||"url"===t||"tel"===t||"password"===t}}),l.Popup.TabKeyTrap=a}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.touch.min",["kendo.core.min","kendo.userevents.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.ui.Widget,r=e.proxy,o=Math.abs,a=20,s=i.extend({init:function(e,t){function o(e){return function(t){s._triggerTouch(e,t)}}function a(e){return function(t){s.trigger(e,{touches:t.touches,distance:t.distance,center:t.center,event:t.event})}}var s=this;i.fn.init.call(s,e,t),t=s.options,e=s.element,s.wrapper=e,s.events=new n.UserEvents(e,{filter:t.filter,surface:t.surface,minHold:t.minHold,multiTouch:t.multiTouch,allowSelection:!0,fastTap:t.fastTap,press:o("touchstart"),hold:o("hold"),tap:r(s,"_tap"),gesturestart:a("gesturestart"),gesturechange:a("gesturechange"),gestureend:a("gestureend")}),t.enableSwipe?(s.events.bind("start",r(s,"_swipestart")),s.events.bind("move",r(s,"_swipemove"))):(s.events.bind("start",r(s,"_dragstart")),s.events.bind("move",o("drag")),s.events.bind("end",o("dragend"))),n.notify(s)},events:["touchstart","dragstart","drag","dragend","tap","doubletap","hold","swipe","gesturestart","gesturechange","gestureend"],options:{name:"Touch",surface:null,global:!1,fastTap:!1,filter:null,multiTouch:!1,enableSwipe:!1,minXDelta:30,maxYDelta:20,maxDuration:1e3,minHold:800,doubleTapTimeout:800},cancel:function(){this.events.cancel()},destroy:function(){i.fn.destroy.call(this),this.events.destroy()},_triggerTouch:function(e,t){this.trigger(e,{touch:t.touch,event:t.event})&&t.preventDefault()},_tap:function(e){var t=this,i=t.lastTap,r=e.touch;i&&r.endTime-i.endTime<t.options.doubleTapTimeout&&n.touchDelta(r,i).distance<a?(t._triggerTouch("doubletap",e),t.lastTap=null):(t._triggerTouch("tap",e),t.lastTap=r)},_dragstart:function(e){this._triggerTouch("dragstart",e)},_swipestart:function(e){2*o(e.x.velocity)>=o(e.y.velocity)&&e.sender.capture()},_swipemove:function(e){var t=this,n=t.options,i=e.touch,r=e.event.timeStamp-i.startTime,a=i.x.initialDelta>0?"right":"left";o(i.x.initialDelta)>=n.minXDelta&&o(i.y.initialDelta)<n.maxYDelta&&r<n.maxDuration&&(t.trigger("swipe",{direction:a,touch:e.touch}),i.cancel())}});n.ui.plugin(s)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.scroller.min",["kendo.fx.min","kendo.draganddrop.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.mobile,r=n.effects,o=i.ui,a=e.proxy,s=e.extend,l=o.Widget,c=n.Class,u=n.ui.Movable,d=n.ui.Pane,h=n.ui.PaneDimensions,f=r.Transition,p=r.Animation,g=Math.abs,m=500,v=.7,_=.96,y=10,b=55,w=.5,k=5,x="km-scroller-release",S="km-scroller-refresh",T="pull",C="change",M="resize",D="scroll",E=2,P=p.extend({init:function(e){var t=this;p.fn.init.call(t),s(t,e),t.userEvents.bind("gestureend",a(t.start,t)),t.tapCapture.bind("press",a(t.cancel,t))},enabled:function(){return this.movable.scale<this.dimensions.minScale},done:function(){return this.dimensions.minScale-this.movable.scale<.01},tick:function(){var e=this.movable;e.scaleWith(1.1),this.dimensions.rescale(e.scale)},onEnd:function(){var e=this.movable;e.scaleTo(this.dimensions.minScale),this.dimensions.rescale(e.scale)}}),F=p.extend({init:function(e){var t=this;p.fn.init.call(t),s(t,e,{transition:new f({axis:e.axis,movable:e.movable,onEnd:function(){t._end()}})}),t.tapCapture.bind("press",function(){t.cancel()}),t.userEvents.bind("end",a(t.start,t)),t.userEvents.bind("gestureend",a(t.start,t)),t.userEvents.bind("tap",a(t.onEnd,t))},onCancel:function(){this.transition.cancel()},freeze:function(e){var t=this;t.cancel(),t._moveTo(e)},onEnd:function(){var e=this;e.paneAxis.outOfBounds()?e._snapBack():e._end()},done:function(){return g(this.velocity)<1},start:function(e){var t,n=this;n.dimension.enabled&&(n.paneAxis.outOfBounds()?n._snapBack():(t=e.touch.id===E?0:e.touch[n.axis].velocity,n.velocity=Math.max(Math.min(t*n.velocityMultiplier,b),-b),n.tapCapture.captureNext(),p.fn.start.call(n)))},tick:function(){var e=this,t=e.dimension,n=e.paneAxis.outOfBounds()?w:e.friction,i=e.velocity*=n,r=e.movable[e.axis]+i;!e.elastic&&t.outOfBounds(r)&&(r=Math.max(Math.min(r,t.max),t.min),e.velocity=0),e.movable.moveAxis(e.axis,r)},_end:function(){this.tapCapture.cancelCapture(),this.end()},_snapBack:function(){var e=this,t=e.dimension,n=e.movable[e.axis]>t.max?t.max:t.min;e._moveTo(n)},_moveTo:function(e){this.transition.moveTo({location:e,duration:m,ease:f.easeOutExpo})}}),O=p.extend({init:function(e){var t=this;n.effects.Animation.fn.init.call(this),s(t,e,{origin:{},destination:{},offset:{}})},tick:function(){this._updateCoordinates(),this.moveTo(this.origin)},done:function(){return g(this.offset.y)<k&&g(this.offset.x)<k},onEnd:function(){this.moveTo(this.destination),this.callback&&this.callback.call()},setCoordinates:function(e,t){this.offset={},this.origin=e,this.destination=t},setCallback:function(e){e&&n.isFunction(e)?this.callback=e:e=t},_updateCoordinates:function(){this.offset={x:(this.destination.x-this.origin.x)/4,y:(this.destination.y-this.origin.y)/4},this.origin={y:this.origin.y+this.offset.y,x:this.origin.x+this.offset.x}}}),z=c.extend({init:function(t){var n=this,i="x"===t.axis,r=e('<div class="km-touch-scrollbar km-'+(i?"horizontal":"vertical")+'-scrollbar" />');s(n,t,{element:r,elementSize:0,movable:new u(r),scrollMovable:t.movable,alwaysVisible:t.alwaysVisible,size:i?"width":"height"}),n.scrollMovable.bind(C,a(n.refresh,n)),n.container.append(r),t.alwaysVisible&&n.show()},refresh:function(){var e=this,t=e.axis,n=e.dimension,i=n.size,r=e.scrollMovable,o=i/n.total,a=Math.round(-r[t]*o),s=Math.round(i*o);o>=1?this.element.css("display","none"):this.element.css("display",""),a+s>i?s=i-a:a<0&&(s+=a,a=0),e.elementSize!=s&&(e.element.css(e.size,s+"px"),e.elementSize=s),e.movable.moveAxis(t,a)},
show:function(){this.element.css({opacity:v,visibility:"visible"})},hide:function(){this.alwaysVisible||this.element.css({opacity:0})}}),A=l.extend({init:function(i,r){var o,c,f,p,m,v,_,y,b,w=this;return l.fn.init.call(w,i,r),i=w.element,(w._native=w.options.useNative&&n.support.hasNativeScrolling)?(i.addClass("km-native-scroller").prepend('<div class="km-scroll-header"/>'),s(w,{scrollElement:i,fixedContainer:i.children().first()}),t):(i.css("overflow","hidden").addClass("km-scroll-wrapper").wrapInner('<div class="km-scroll-container"/>').prepend('<div class="km-scroll-header"/>'),o=i.children().eq(1),c=new n.TapCapture(i),f=new u(o),p=new h({element:o,container:i,forcedEnabled:w.options.zoom}),m=this.options.avoidScrolling,v=new n.UserEvents(i,{touchAction:"pan-y",fastTap:!0,allowSelection:!0,preventDragEvent:!0,captureUpIfMoved:!0,multiTouch:w.options.zoom,supportDoubleTap:w.options.supportDoubleTap,start:function(t){p.refresh();var n=g(t.x.velocity),i=g(t.y.velocity),r=2*n>=i,o=e.contains(w.fixedContainer[0],t.event.target),a=2*i>=n;!o&&!m(t)&&w.enabled&&(p.x.enabled&&r||p.y.enabled&&a)?v.capture():v.cancel()}}),_=new d({movable:f,dimensions:p,userEvents:v,elastic:w.options.elastic}),y=new P({movable:f,dimensions:p,userEvents:v,tapCapture:c}),b=new O({moveTo:function(e){w.scrollTo(e.x,e.y)}}),f.bind(C,function(){w.scrollTop=-f.y,w.scrollLeft=-f.x,w.trigger(D,{scrollTop:w.scrollTop,scrollLeft:w.scrollLeft})}),w.options.mousewheelScrolling&&i.on("DOMMouseScroll mousewheel",a(this,"_wheelScroll")),s(w,{movable:f,dimensions:p,zoomSnapBack:y,animatedScroller:b,userEvents:v,pane:_,tapCapture:c,pulled:!1,enabled:!0,scrollElement:o,scrollTop:0,scrollLeft:0,fixedContainer:i.children().first()}),w._initAxis("x"),w._initAxis("y"),w._wheelEnd=function(){w._wheel=!1,w.userEvents.end(0,w._wheelY)},p.refresh(),w.options.pullToRefresh&&w._initPullToRefresh(),t)},_wheelScroll:function(e){this._wheel||(this._wheel=!0,this._wheelY=0,this.userEvents.press(0,this._wheelY)),clearTimeout(this._wheelTimeout),this._wheelTimeout=setTimeout(this._wheelEnd,50);var t=n.wheelDeltaY(e);t&&(this._wheelY+=t,this.userEvents.move(0,this._wheelY)),e.preventDefault()},makeVirtual:function(){this.dimensions.y.makeVirtual()},virtualSize:function(e,t){this.dimensions.y.virtualSize(e,t)},height:function(){return this.dimensions.y.size},scrollHeight:function(){return this.scrollElement[0].scrollHeight},scrollWidth:function(){return this.scrollElement[0].scrollWidth},options:{name:"Scroller",zoom:!1,pullOffset:140,visibleScrollHints:!1,elastic:!0,useNative:!1,mousewheelScrolling:!0,avoidScrolling:function(){return!1},pullToRefresh:!1,messages:{pullTemplate:"Pull to refresh",releaseTemplate:"Release to refresh",refreshTemplate:"Refreshing"}},events:[T,D,M],_resize:function(){this._native||this.contentResized()},setOptions:function(e){var t=this;l.fn.setOptions.call(t,e),e.pullToRefresh&&t._initPullToRefresh()},reset:function(){this._native?this.scrollElement.scrollTop(0):(this.movable.moveTo({x:0,y:0}),this._scale(1))},contentResized:function(){this.dimensions.refresh(),this.pane.x.outOfBounds()&&this.movable.moveAxis("x",this.dimensions.x.min),this.pane.y.outOfBounds()&&this.movable.moveAxis("y",this.dimensions.y.min)},zoomOut:function(){var e=this.dimensions;e.refresh(),this._scale(e.fitScale),this.movable.moveTo(e.centerCoordinates())},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},scrollTo:function(e,t){this._native?(this.scrollElement.scrollLeft(g(e)),this.scrollElement.scrollTop(g(t))):(this.dimensions.refresh(),this.movable.moveTo({x:e,y:t}))},animatedScrollTo:function(e,t,n){var i,r;this._native?this.scrollTo(e,t):(i={x:this.movable.x,y:this.movable.y},r={x:e,y:t},this.animatedScroller.setCoordinates(i,r),this.animatedScroller.setCallback(n),this.animatedScroller.start())},pullHandled:function(){var e=this;e.refreshHint.removeClass(S),e.hintContainer.html(e.pullTemplate({})),e.yinertia.onEnd(),e.xinertia.onEnd(),e.userEvents.cancel()},destroy:function(){l.fn.destroy.call(this),this.userEvents&&this.userEvents.destroy()},_scale:function(e){this.dimensions.rescale(e),this.movable.scaleTo(e)},_initPullToRefresh:function(){var e=this;e.dimensions.y.forceEnabled(),e.pullTemplate=n.template(e.options.messages.pullTemplate),e.releaseTemplate=n.template(e.options.messages.releaseTemplate),e.refreshTemplate=n.template(e.options.messages.refreshTemplate),e.scrollElement.prepend('<span class="km-scroller-pull"><span class="km-icon"></span><span class="km-loading-left"></span><span class="km-loading-right"></span><span class="km-template">'+e.pullTemplate({})+"</span></span>"),e.refreshHint=e.scrollElement.children().first(),e.hintContainer=e.refreshHint.children(".km-template"),e.pane.y.bind("change",a(e._paneChange,e)),e.userEvents.bind("end",a(e._dragEnd,e))},_dragEnd:function(){var e=this;e.pulled&&(e.pulled=!1,e.refreshHint.removeClass(x).addClass(S),e.hintContainer.html(e.refreshTemplate({})),e.yinertia.freeze(e.options.pullOffset/2),e.trigger("pull"))},_paneChange:function(){var e=this;e.movable.y/w>e.options.pullOffset?e.pulled||(e.pulled=!0,e.refreshHint.removeClass(S).addClass(x),e.hintContainer.html(e.releaseTemplate({}))):e.pulled&&(e.pulled=!1,e.refreshHint.removeClass(x),e.hintContainer.html(e.pullTemplate({})))},_initAxis:function(e){var t=this,n=t.movable,i=t.dimensions[e],r=t.tapCapture,o=t.pane[e],a=new z({axis:e,movable:n,dimension:i,container:t.element,alwaysVisible:t.options.visibleScrollHints});i.bind(C,function(){a.refresh()}),o.bind(C,function(){a.show()}),t[e+"inertia"]=new F({axis:e,paneAxis:o,movable:n,tapCapture:r,userEvents:t.userEvents,dimension:i,elastic:t.options.elastic,friction:t.options.friction||_,velocityMultiplier:t.options.velocityMultiplier||y,end:function(){a.hide(),t.trigger("scrollEnd",{axis:e,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft})}})}});o.plugin(A)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.view.min",["kendo.core.min","kendo.fx.min","kendo.mobile.scroller.min","kendo.view.min"],e)}(function(){return function(e,t){function n(e){var t,n,i=e.find(k("popover")),r=s.roles;for(t=0,n=i.length;t<n;t++)o.initWidget(i[t],{},r)}function i(e){o.triggeredByInput(e)||e.preventDefault()}function r(t){t.each(function(){o.initWidget(e(this),{},s.roles)})}var o=window.kendo,a=o.mobile,s=a.ui,l=o.attr,c=s.Widget,u=o.ViewClone,d="init",h='<div style="height: 100%; width: 100%; position: absolute; top: 0; left: 0; z-index: 20000; display: none" />',f="beforeShow",p="show",g="afterShow",m="beforeHide",v="transitionEnd",_="transitionStart",y="hide",b="destroy",w=o.attrValue,k=o.roleSelector,x=o.directiveSelector,S=o.compileMobileDirective,T=c.extend({init:function(t,n){c.fn.init.call(this,t,n),this.params={},e.extend(this,n),this.transition=this.transition||this.defaultTransition,this._id(),this.options.$angular?this._overlay():(this._layout(),this._overlay(),this._scroller(),this._model())},events:[d,f,p,g,m,y,b,_,v],options:{name:"View",title:"",layout:null,getLayout:e.noop,reload:!1,transition:"",defaultTransition:"",useNativeScrolling:!1,stretch:!1,zoom:!1,model:null,modelScope:window,scroller:{},initWidgets:!0},enable:function(e){t===e&&(e=!0),e?this.overlay.hide():this.overlay.show()},destroy:function(){this.layout&&this.layout.detach(this),this.trigger(b),c.fn.destroy.call(this),this.scroller&&this.scroller.destroy(),this.options.$angular&&this.element.scope().$destroy(),o.destroy(this.element)},purge:function(){this.destroy(),this.element.remove()},triggerBeforeShow:function(){return!this.trigger(f,{view:this})},triggerBeforeHide:function(){return!this.trigger(m,{view:this})},showStart:function(){var e=this.element;e.css("display",""),this.inited?this._invokeNgController():(this.inited=!0,this.trigger(d,{view:this})),this.layout&&this.layout.attach(this),this._padIfNativeScrolling(),this.trigger(p,{view:this}),o.resize(e)},showEnd:function(){this.trigger(g,{view:this}),this._padIfNativeScrolling()},hideEnd:function(){var e=this;e.element.hide(),e.trigger(y,{view:e}),e.layout&&e.layout.trigger(y,{view:e,layout:e.layout})},beforeTransition:function(e){this.trigger(_,{type:e})},afterTransition:function(e){this.trigger(v,{type:e})},_padIfNativeScrolling:function(){if(a.appLevelNativeScrolling()){var e=o.support.mobileOS&&o.support.mobileOS.android,t=a.application.skin()||"",n=a.application.os.android||t.indexOf("android")>-1,i="flat"===t||t.indexOf("material")>-1,r=!e&&!n||i?"header":"footer",s=!e&&!n||i?"footer":"header";this.content.css({paddingTop:this[r].height(),paddingBottom:this[s].height()})}},contentElement:function(){var e=this;return e.options.stretch?e.content:e.scrollerContent},clone:function(){return new u(this)},_scroller:function(){var t=this;a.appLevelNativeScrolling()||(t.options.stretch?t.content.addClass("km-stretched-view"):(t.content.kendoMobileScroller(e.extend(t.options.scroller,{zoom:t.options.zoom,useNative:t.options.useNativeScrolling})),t.scroller=t.content.data("kendoMobileScroller"),t.scrollerContent=t.scroller.scrollElement),o.support.kineticScrollNeeded&&(e(t.element).on("touchmove",".km-header",i),t.options.useNativeScrolling||t.options.stretch||e(t.element).on("touchmove",".km-content",i)))},_model:function(){var e=this,t=e.element,i=e.options.model;"string"==typeof i&&(i=o.getter(i)(e.options.modelScope)),e.model=i,n(t),e.element.css("display",""),e.options.initWidgets&&(i?o.bind(t,i,s,o.ui,o.dataviz.ui):a.init(t.children())),e.element.css("display","none")},_id:function(){var e=this.element,t=e.attr("id")||"";this.id=w(e,"url")||"#"+t,"#"==this.id&&(this.id=o.guid(),e.attr("id",this.id))},_layout:function(){var e=k("content"),t=this.element;t.addClass("km-view"),this.header=t.children(k("header")).addClass("km-header"),this.footer=t.children(k("footer")).addClass("km-footer"),t.children(e)[0]||t.wrapInner("<div "+l("role")+'="content"></div>'),this.content=t.children(k("content")).addClass("km-content"),this.element.prepend(this.header).append(this.footer),this.layout=this.options.getLayout(this.layout),this.layout&&this.layout.setup(this)},_overlay:function(){this.overlay=e(h).appendTo(this.element)},_invokeNgController:function(){var t,n,i;this.options.$angular&&(t=this.element.controller(),n=this.options.$angular[0],t&&(i=e.proxy(this,"_callController",t,n),/^\$(digest|apply)$/.test(n.$$phase)?i():n.$apply(i)))},_callController:function(e,t){this.element.injector().invoke(e.constructor,e,{$scope:t})}}),C=c.extend({init:function(e,t){c.fn.init.call(this,e,t),e=this.element,this.header=e.children(this._locate("header")).addClass("km-header"),this.footer=e.children(this._locate("footer")).addClass("km-footer"),this.elements=this.header.add(this.footer),n(e),this.options.$angular||o.mobile.init(this.element.children()),this.element.detach(),this.trigger(d,{layout:this})},_locate:function(e){return this.options.$angular?x(e):k(e)},options:{name:"Layout",id:null,platform:null},events:[d,p,y],setup:function(e){e.header[0]||(e.header=this.header),e.footer[0]||(e.footer=this.footer)},detach:function(e){var t=this;e.header===t.header&&t.header[0]&&e.element.prepend(t.header.detach()[0].cloneNode(!0)),e.footer===t.footer&&t.footer.length&&e.element.append(t.footer.detach()[0].cloneNode(!0))},attach:function(e){var t=this,n=t.currentView;n&&t.detach(n),e.header===t.header&&(t.header.detach(),e.element.children(k("header")).remove(),e.element.prepend(t.header)),e.footer===t.footer&&(t.footer.detach(),e.element.children(k("footer")).remove(),e.element.append(t.footer)),t.trigger(p,{layout:t,view:e}),t.currentView=e}}),M=o.Observable,D=/<body[^>]*>(([\u000a\u000d\u2028\u2029]|.)*)<\/body>/i,E="loadStart",P="loadComplete",F="showStart",O="sameViewRequested",z="viewShow",A="viewTypeDetermined",I="after",H=M.extend({init:function(t){var n,i,a,s,l=this;if(M.fn.init.call(l),e.extend(l,t),l.sandbox=e("<div />"),a=l.container,n=l._hideViews(a),l.rootView=n.first(),!l.rootView[0]&&t.rootNeeded)throw i=a[0]==o.mobile.application.element[0]?'Your kendo mobile application element does not contain any direct child elements with data-role="view" attribute set. Make sure that you instantiate the mobile application using the correct container.':'Your pane element does not contain any direct child elements with data-role="view" attribute set.',Error(i);l.layouts={},l.viewContainer=new o.ViewContainer(l.container),l.viewContainer.bind("accepted",function(e){e.view.params=l.params}),l.viewContainer.bind("complete",function(e){l.trigger(z,{view:e.view})}),l.viewContainer.bind(I,function(){l.trigger(I)}),this.getLayoutProxy=e.proxy(this,"_getLayout"),l._setupLayouts(a),s=a.children(l._locate("modalview drawer")),l.$angular?(l.$angular[0].viewOptions={defaultTransition:l.transition,loader:l.loader,container:l.container,getLayout:l.getLayoutProxy},s.each(function(n,i){S(e(i),t.$angular[0])})):r(s),this.bind(this.events,t)},events:[F,I,z,E,P,O,A],destroy:function(){o.destroy(this.container);for(var e in this.layouts)this.layouts[e].destroy()},view:function(){return this.viewContainer.view},showView:function(e,t,n){if(e=e.replace(RegExp("^"+this.remoteViewURLPrefix),""),""===e&&this.remoteViewURLPrefix&&(e="/"),e.replace(/^#/,"")===this.url)return this.trigger(O),!1;this.trigger(F);var i=this,r=function(n){return i.viewContainer.show(n,t,e)},a=i._findViewElement(e),s=o.widgetInstance(a);return i.url=e.replace(/^#/,""),i.params=n,s&&s.reload&&(s.purge(),a=[]),this.trigger(A,{remote:0===a.length,url:e}),a[0]?(s||(s=i._createView(a)),r(s)):(this.serverNavigation?location.href=e:i._loadView(e,r),!0)},append:function(e,t){var n,i,o,a=this.sandbox,s=(t||"").split("?")[0],c=this.container;return D.test(e)&&(e=RegExp.$1),a[0].innerHTML=e,c.append(a.children("script, style")),n=this._hideViews(a),o=n.first(),o.length||(n=o=a.wrapInner("<div data-role=view />").children()),s&&o.hide().attr(l("url"),s),this._setupLayouts(a),i=a.children(this._locate("modalview drawer")),c.append(a.children(this._locate("layout modalview drawer")).add(n)),r(i),this._createView(o)},_locate:function(e){return this.$angular?x(e):k(e)},_findViewElement:function(e){var t,n=e.split("?")[0];return n?(t=this.container.children("["+l("url")+"='"+n+"']"),t[0]||n.indexOf("/")!==-1||(t=this.container.children("#"===n.charAt(0)?n:"#"+n)),t):this.rootView},_createView:function(e){return this.$angular?S(e,this.$angular[0]):o.initWidget(e,{defaultTransition:this.transition,loader:this.loader,container:this.container,getLayout:this.getLayoutProxy,modelScope:this.modelScope,reload:w(e,"reload")},s.roles)},_getLayout:function(e){return""===e?null:e?this.layouts[e]:this.layouts[this.layout]},_loadView:function(t,n){this._xhr&&this._xhr.abort(),this.trigger(E),this._xhr=e.get(o.absoluteURL(t,this.remoteViewURLPrefix),"html").always(e.proxy(this,"_xhrComplete",n,t))},_xhrComplete:function(e,t,n){var i=!0;if("object"==typeof n&&0===n.status){if(!(n.responseText&&n.responseText.length>0))return;i=!0,n=n.responseText}this.trigger(P),i&&e(this.append(n,t))},_hideViews:function(e){return e.children(this._locate("view splitview")).hide()},_setupLayouts:function(t){var n,i=this;t.children(i._locate("layout")).each(function(){n=i.$angular?S(e(this),i.$angular[0]):o.initWidget(e(this),{},s.roles);var t=n.options.platform;t&&t!==a.application.os.name?n.destroy():i.layouts[n.options.id]=n})}});o.mobile.ViewEngine=H,s.plugin(T),s.plugin(C)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.loader.min",["kendo.core.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.mobile.ui,r=i.Widget,o=e.map(n.eventMap,function(e){return e}).join(" ").split(" "),a=r.extend({init:function(t,n){var i=this,o=e('<div class="km-loader"><span class="km-loading km-spin"></span><span class="km-loading-left"></span><span class="km-loading-right"></span></div>');r.fn.init.call(i,o,n),i.container=t,i.captureEvents=!1,i._attachCapture(),o.append(i.options.loading).hide().appendTo(t)},options:{name:"Loader",loading:"<h1>Loading...</h1>",timeout:100},show:function(){var e=this;clearTimeout(e._loading),e.options.loading!==!1&&(e.captureEvents=!0,e._loading=setTimeout(function(){e.element.show()},e.options.timeout))},hide:function(){this.captureEvents=!1,clearTimeout(this._loading),this.element.hide()},changeMessage:function(e){this.options.loading=e,this.element.find(">h1").html(e)},transition:function(){this.captureEvents=!0,this.container.css("pointer-events","none")},transitionDone:function(){this.captureEvents=!1,this.container.css("pointer-events","")},_attachCapture:function(){function e(e){n.captureEvents&&e.preventDefault()}var t,n=this;for(n.captureEvents=!1,t=0;t<o.length;t++)n.container[0].addEventListener(o[t],e,!0)}});i.plugin(a)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.pane.min",["kendo.mobile.view.min","kendo.mobile.loader.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.mobile,r=n.roleSelector,o=i.ui,a=o.Widget,s=i.ViewEngine,l=o.View,c=i.ui.Loader,u="external",d="href",h="#!",f="navigate",p="viewShow",g="sameViewRequested",m=n.support.mobileOS,v=m.ios&&!m.appMode&&m.flatVersion>=700,_=/popover|actionsheet|modalview|drawer/,y="#:back",b=n.attrValue,w=a.extend({init:function(e,t){var i=this;a.fn.init.call(i,e,t),t=i.options,e=i.element,e.addClass("km-pane"),i.options.collapsible&&e.addClass("km-collapsible-pane"),this.history=[],this.historyCallback=function(e,t,n){var r=i.transition;return i.transition=null,v&&n&&(r="none"),i.viewEngine.showView(e,r,t)},this._historyNavigate=function(e){if(e===y){if(1===i.history.length)return;i.history.pop(),e=i.history[i.history.length-1]}else i.history.push(e);i.historyCallback(e,n.parseQueryStringParams(e))},this._historyReplace=function(e){var t=n.parseQueryStringParams(e);i.history[i.history.length-1]=e,i.historyCallback(e,t)},i.loader=new c(e,{loading:i.options.loading}),i.viewEngine=new s({container:e,transition:t.transition,modelScope:t.modelScope,rootNeeded:!t.initial,serverNavigation:t.serverNavigation,remoteViewURLPrefix:t.root||"",layout:t.layout,$angular:t.$angular,loader:i.loader,showStart:function(){i.loader.transition(),i.closeActiveDialogs()},after:function(){i.loader.transitionDone()},viewShow:function(e){i.trigger(p,e)},loadStart:function(){i.loader.show()},loadComplete:function(){i.loader.hide()},sameViewRequested:function(){i.trigger(g)},viewTypeDetermined:function(e){e.remote&&i.options.serverNavigation||i.trigger(f,{url:e.url})}}),this._setPortraitWidth(),n.onResize(function(){i._setPortraitWidth()}),i._setupAppLinks()},closeActiveDialogs:function(){var t=this.element.find(r("actionsheet popover modalview")).filter(":visible");t.each(function(){n.widgetInstance(e(this),o).close()})},navigateToInitial:function(){var e=this.options.initial;return e&&this.navigate(e),e},options:{name:"Pane",portraitWidth:"",transition:"",layout:"",collapsible:!1,initial:null,modelScope:window,loading:"<h1>Loading...</h1>"},events:[f,p,g],append:function(e){return this.viewEngine.append(e)},destroy:function(){a.fn.destroy.call(this),this.viewEngine.destroy(),this.userEvents.destroy()},navigate:function(e,t){e instanceof l&&(e=e.id),this.transition=t,this._historyNavigate(e)},replace:function(e,t){e instanceof l&&(e=e.id),this.transition=t,this._historyReplace(e)},bindToRouter:function(e){var t=this,i=this.history,r=this.viewEngine;e.bind("init",function(t){var o,a=t.url,s=e.pushState?a:"/";r.rootView.attr(n.attr("url"),s),o=i.length,"/"===a&&o&&(e.navigate(i[o-1],!0),t.preventDefault())}),e.bind("routeMissing",function(e){t.historyCallback(e.url,e.params,e.backButtonPressed)||e.preventDefault()}),e.bind("same",function(){t.trigger(g)}),t._historyNavigate=function(t){e.navigate(t)},t._historyReplace=function(t){e.replace(t)}},hideLoading:function(){this.loader.hide()},showLoading:function(){this.loader.show()},changeLoadingMessage:function(e){this.loader.changeMessage(e)},view:function(){return this.viewEngine.view()},_setPortraitWidth:function(){var e,t=this.options.portraitWidth;t&&(e=n.mobile.application.element.is(".km-vertical")?t:"auto",this.element.css("width",e))},_setupAppLinks:function(){var t=this,i="tab",o="[data-"+n.ns+"navigate-on-press]",a=e.map(["button","backbutton","detailbutton","listview-link"],function(e){return r(e)+":not("+o+")"}).join(",");this.element.handler(this).on("down",r(i)+","+o,"_mouseup").on("click",r(i)+","+a+","+o,"_appLinkClick"),this.userEvents=new n.UserEvents(this.element,{fastTap:!0,filter:a,tap:function(e){e.event.currentTarget=e.touch.currentTarget,t._mouseup(e.event)}}),this.element.css("-ms-touch-action","")},_appLinkClick:function(t){var n=e(t.currentTarget).attr("href"),i=n&&"#"!==n[0]&&this.options.serverNavigation;i||b(e(t.currentTarget),"rel")==u||t.preventDefault()},_mouseup:function(r){if(!(r.which>1||r.isDefaultPrevented())){var a=this,s=e(r.currentTarget),l=b(s,"transition"),c=b(s,"rel")||"",f=b(s,"target"),p=s.attr(d),g=v&&0===s[0].offsetHeight,m=p&&"#"!==p[0]&&this.options.serverNavigation;g||m||c===u||t===p||p===h||(s.attr(d,h),setTimeout(function(){s.attr(d,p)}),c.match(_)?(n.widgetInstance(e(p),o).openFor(s),"actionsheet"!==c&&"drawer"!==c||r.stopPropagation()):("_top"===f?a=i.application.pane:f&&(a=e("#"+f).data("kendoMobilePane")),a.navigate(p,l)),r.preventDefault())}}});w.wrap=function(e){e.is(r("view"))||(e=e.wrap("<div data-"+n.ns+'role="view" data-stretch="true"></div>').parent());var t=e.wrap('<div class="km-pane-wrapper"><div></div></div>').parent(),i=new w(t);return i.navigate(""),i},o.plugin(w)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.popover.min",["kendo.popup.min","kendo.mobile.pane.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.mobile,r=i.ui,o="hide",a="open",s="close",l='<div class="km-popup-wrapper" />',c='<div class="km-popup-arrow" />',u='<div class="km-popup-overlay" />',d="km-up km-down km-left km-right",h=r.Widget,f={down:{origin:"bottom center",position:"top center"},up:{origin:"top center",position:"bottom center"},left:{origin:"center left",position:"center right",collision:"fit flip"},right:{origin:"center right",position:"center left",collision:"fit flip"}},p={animation:{open:{effects:"fade:in",duration:0},close:{effects:"fade:out",duration:400}}},g={horizontal:{offset:"top",size:"height"},vertical:{offset:"left",size:"width"}},m={up:"down",down:"up",left:"right",right:"left"},v=h.extend({init:function(t,i){var r,a,s=this,d=t.closest(".km-modalview-wrapper"),m=t.closest(".km-root").children(".km-pane").first(),v=d[0]?d:m;i.viewport?m=i.viewport:m[0]||(m=window),i.container?v=i.container:v[0]||(v=document.body),r={viewport:m,copyAnchorStyles:!1,autosize:!0,open:function(){s.overlay.show()},activate:e.proxy(s._activate,s),deactivate:function(){s.overlay.hide(),s._apiCall||s.trigger(o),s._apiCall=!1}},h.fn.init.call(s,t,i),t=s.element,i=s.options,t.wrap(l).addClass("km-popup").show(),a=s.options.direction.match(/left|right/)?"horizontal":"vertical",s.dimensions=g[a],s.wrapper=t.parent().css({width:i.width,height:i.height}).addClass("km-popup-wrapper km-"+i.direction).hide(),s.arrow=e(c).prependTo(s.wrapper).hide(),s.overlay=e(u).appendTo(v).hide(),r.appendTo=s.overlay,i.className&&s.overlay.addClass(i.className),s.popup=new n.ui.Popup(s.wrapper,e.extend(!0,r,p,f[i.direction]))},options:{name:"Popup",width:240,height:"",direction:"down",container:null,viewport:null},events:[o],show:function(t){this.popup.options.anchor=e(t),this.popup.open()},hide:function(){this._apiCall=!0,this.popup.close()},destroy:function(){h.fn.destroy.call(this),this.popup.destroy(),this.overlay.remove()},target:function(){return this.popup.options.anchor},_activate:function(){var t=this,n=t.options.direction,i=t.dimensions,r=i.offset,o=t.popup,a=o.options.anchor,s=e(a).offset(),l=e(o.element).offset(),c=o.flipped?m[n]:n,u=2*t.arrow[i.size](),h=t.element[i.size]()-t.arrow[i.size](),f=e(a)[i.size](),p=s[r]-l[r]+f/2;p<u&&(p=u),p>h&&(p=h),t.wrapper.removeClass(d).addClass("km-"+c),t.arrow.css(r,p).show()}}),_=h.extend({init:function(t,i){var o,a=this;a.initialOpen=!1,h.fn.init.call(a,t,i),o=e.extend({className:"km-popover-root",hide:function(){a.trigger(s)}},this.options.popup),a.popup=new v(a.element,o),a.popup.overlay.on("move",function(e){e.target==a.popup.overlay[0]&&e.preventDefault()}),a.pane=new r.Pane(a.element,e.extend(this.options.pane,{$angular:this.options.$angular})),n.notify(a,r)},options:{name:"PopOver",popup:{},pane:{}},events:[a,s],open:function(e){this.popup.show(e),this.initialOpen?this.pane.view()._invokeNgController():(this.pane.navigateToInitial()||this.pane.navigate(""),this.popup.popup._position(),this.initialOpen=!0)},openFor:function(e){this.open(e),this.trigger(a,{target:this.popup.target()})},close:function(){this.popup.hide()},destroy:function(){h.fn.destroy.call(this),this.pane.destroy(),this.popup.destroy(),n.destroy(this.element)}});r.plugin(v),r.plugin(_)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.shim.min",["kendo.popup.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.mobile.ui,r=n.ui.Popup,o='<div class="km-shim"/>',a="hide",s=i.Widget,l=s.extend({init:function(t,i){var l=this,c=n.mobile.application,u=n.support.mobileOS,d=c?c.os.name:u?u.name:"ios",h="ios"===d||"wp"===d||!!c&&c.os.skin,f="blackberry"===d,p=i.align||(h?"bottom center":f?"center right":"center center"),g=i.position||(h?"bottom center":f?"center right":"center center"),m=i.effect||(h?"slideIn:up":f?"slideIn:left":"fade:in"),v=e(o).handler(l).hide();s.fn.init.call(l,t,i),l.shim=v,t=l.element,i=l.options,i.className&&l.shim.addClass(i.className),i.modal||l.shim.on("down","_hide"),(c?c.element:e(document.body)).append(v),l.popup=new r(l.element,{anchor:v,modal:!0,appendTo:v,origin:p,position:g,animation:{open:{effects:m,duration:i.duration},close:{duration:i.duration}},close:function(e){var t=!1;l._apiCall||(t=l.trigger(a)),t&&e.preventDefault(),l._apiCall=!1},deactivate:function(){v.hide()},open:function(){v.show()}}),n.notify(l)},events:[a],options:{name:"Shim",modal:!1,align:t,position:t,effect:t,duration:200},show:function(){this.popup.open()},hide:function(){this._apiCall=!0,this.popup.close()},destroy:function(){s.fn.destroy.call(this),this.shim.kendoDestroy(),this.popup.destroy(),this.shim.remove()},_hide:function(t){t&&e.contains(this.shim.children().children(".k-popup")[0],t.target)||this.popup.close()}});i.plugin(l)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.modalview.min",["kendo.mobile.shim.min","kendo.mobile.view.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.mobile.ui,r=i.Shim,o=i.Widget,a="beforeOpen",s="open",l="close",c="init",u='<div class="km-modalview-wrapper" />',d=i.View.extend({init:function(e,t){var n=this;o.fn.init.call(n,e,t),n._id(),n._wrap(),n._shim(),this.options.$angular||(n._layout(),n._scroller(),n._model()),n.element.css("display",""),n.trigger(c)},events:[c,a,s,l],options:{name:"ModalView",modal:!0,width:null,height:null},destroy:function(){o.fn.destroy.call(this),this.shim.destroy()},open:function(t){var n=this;n.target=e(t),n.shim.show(),n._invokeNgController(),n.trigger("show",{view:n})},openFor:function(e){this.trigger(a,{target:e})||(this.open(e),this.trigger(s,{target:e}))},close:function(){this.element.is(":visible")&&!this.trigger(l)&&this.shim.hide()},_wrap:function(){var e,t,n=this,i=n.element,r=n.options;e=i[0].style.width||"auto",t=i[0].style.height||"auto",i.addClass("km-modalview").wrap(u),n.wrapper=i.parent().css({width:r.width||e||300,height:r.height||t||300}).addClass("auto"==t?" km-auto-height":""),i.css({width:"",height:""})},_shim:function(){var e=this;e.shim=new r(e.wrapper,{modal:e.options.modal,position:"center center",align:"center center",effect:"fade:in",className:"km-modalview-root",hide:function(t){e.trigger(l)&&t.preventDefault()}})}});i.plugin(d)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.drawer.min",["kendo.mobile.view.min","kendo.userevents.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.mobile,r=n.support.mobileOS,o=n.effects.Transition,a=n.roleSelector,s="x",l=i.ui,c=!(r.ios&&7==r.majorVersion&&!r.appMode),u="beforeShow",d="init",h="show",f="hide",p="afterHide",g={enable:e.noop},m=l.View.extend({init:function(t,r){var o,s,l,u,h;if(e(t).parent().prepend(t),i.ui.Widget.fn.init.call(this,t,r),this.options.$angular||(this._layout(),this._scroller()),this._model(),o=this.element.closest(a("pane")).data("kendoMobilePane"))this.pane=o,this.pane.bind("viewShow",function(e){u._viewShow(e)}),this.pane.bind("sameViewRequested",function(){u.hide()}),s=this.userEvents=new n.UserEvents(o.element,{fastTap:!0,filter:a("view splitview"),allowSelection:!0});else{if(this.currentView=g,l=e(this.options.container),!l)throw Error("The drawer needs a container configuration option set.");s=this.userEvents=new n.UserEvents(l,{fastTap:!0,allowSelection:!0}),this._attachTransition(l)}u=this,h=function(e){u.visible&&(u.hide(),e.preventDefault())},this.options.swipeToOpen&&c?(s.bind("press",function(){u.transition.cancel()}),s.bind("start",function(e){u._start(e)}),s.bind("move",function(e){u._update(e)}),s.bind("end",function(e){u._end(e)}),s.bind("tap",h)):s.bind("press",h),this.leftPositioned="left"===this.options.position,this.visible=!1,this.element.hide().addClass("km-drawer").addClass(this.leftPositioned?"km-left-drawer":"km-right-drawer"),this.trigger(d)},options:{name:"Drawer",position:"left",views:[],swipeToOpenViews:[],swipeToOpen:!0,title:"",container:null},events:[u,f,p,d,h],show:function(){this._activate()&&this._show()},hide:function(){this.currentView&&(this.currentView.enable(),m.current=null,this._moveViewTo(0),this.trigger(f,{view:this}))},openFor:function(){this.visible?this.hide():this.show()},destroy:function(){l.View.fn.destroy.call(this),this.userEvents.destroy()},_activate:function(){if(this.visible)return!0;var e=this._currentViewIncludedIn(this.options.views);return!(!e||this.trigger(u,{view:this}))&&(this._setAsCurrent(),this.element.show(),this.trigger(h,{view:this}),this._invokeNgController(),!0)},_currentViewIncludedIn:function(t){if(!this.pane||!t.length)return!0;var n=this.pane.view();return e.inArray(n.id.replace("#",""),t)>-1||e.inArray(n.element.attr("id"),t)>-1},_show:function(){this.currentView.enable(!1),this.visible=!0;var e=this.element.width();this.leftPositioned||(e=-e),this._moveViewTo(e)},_setAsCurrent:function(){m.last!==this&&(m.last&&m.last.element.hide(),this.element.show()),m.last=this,m.current=this},_moveViewTo:function(e){this.userEvents.cancel(),this.transition.moveTo({location:e,duration:400,ease:o.easeOutExpo})},_viewShow:function(e){return this.currentView&&this.currentView.enable(),this.currentView===e.view?(this.hide(),t):(this.currentView=e.view,this._attachTransition(e.view.element),t)},_attachTransition:function(e){var t=this,i=this.movable,r=i&&i.x;this.transition&&(this.transition.cancel(),this.movable.moveAxis("x",0)),i=this.movable=new n.ui.Movable(e),this.transition=new o({axis:s,movable:this.movable,onEnd:function(){0===i[s]&&(e[0].style.cssText="",t.element.hide(),t.trigger(p),t.visible=!1)}}),r&&(e.addClass("k-fx-hidden"),n.animationFrame(function(){e.removeClass("k-fx-hidden"),t.movable.moveAxis(s,r),t.hide()}))},_start:function(e){var i,r,o,a,s,l=e.sender;return Math.abs(e.x.velocity)<Math.abs(e.y.velocity)||n.triggeredByInput(e.event)||!this._currentViewIncludedIn(this.options.swipeToOpenViews)?(l.cancel(),t):(i=this.leftPositioned,
r=this.visible,o=i&&r||!i&&!m.current,a=!i&&r||i&&!m.current,s=e.x.velocity<0,(o&&s||a&&!s)&&this._activate()?(l.capture(),t):(l.cancel(),t))},_update:function(e){var t,n=this.movable,i=n.x+e.x.delta;t=this.leftPositioned?Math.min(Math.max(0,i),this.element.width()):Math.max(Math.min(0,i),-this.element.width()),this.movable.moveAxis(s,t),e.event.preventDefault(),e.event.stopPropagation()},_end:function(e){var t,n=e.x.velocity,i=Math.abs(this.movable.x)>this.element.width()/2,r=.8;t=this.leftPositioned?n>-r&&(n>r||i):n<r&&(n<-r||i),t?this._show():this.hide()}});l.plugin(m)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.splitview.min",["kendo.mobile.pane.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.mobile.ui,r=i.Widget,o="<div class='km-expanded-pane-shim' />",a=i.View,s=a.extend({init:function(t,a){var s,l,c=this;r.fn.init.call(c,t,a),t=c.element,e.extend(c,a),c._id(),c.options.$angular?c._overlay():(c._layout(),c._overlay()),c._style(),l=t.children(c._locate("modalview")),c.options.$angular?l.each(function(t,i){n.compileMobileDirective(e(i),a.$angular[0])}):n.mobile.init(l),c.panes=[],c._paramsHistory=[],c.options.$angular?(c.element.children(n.directiveSelector("pane")).each(function(){s=n.compileMobileDirective(e(this),a.$angular[0]),c.panes.push(s)}),c.element.children(n.directiveSelector("header footer")).each(function(){n.compileMobileDirective(e(this),a.$angular[0])})):c.content.children(n.roleSelector("pane")).each(function(){s=n.initWidget(this,{},i.roles),c.panes.push(s)}),c.expandedPaneShim=e(o).appendTo(c.element),c._shimUserEvents=new n.UserEvents(c.expandedPaneShim,{fastTap:!0,tap:function(){c.collapsePanes()}})},_locate:function(e){return this.options.$angular?n.directiveSelector(e):n.roleSelector(e)},options:{name:"SplitView",style:"horizontal"},expandPanes:function(){this.element.addClass("km-expanded-splitview")},collapsePanes:function(){this.element.removeClass("km-expanded-splitview")},_layout:function(){var e=this,t=e.element;e.transition=n.attrValue(t,"transition"),n.mobile.ui.View.prototype._layout.call(this),n.mobile.init(this.header.add(this.footer)),e.element.addClass("km-splitview"),e.content.addClass("km-split-content")},_style:function(){var t,n=this.options.style,i=this.element;n&&(t=n.split(" "),e.each(t,function(){i.addClass("km-split-"+this)}))},showStart:function(){var t=this;t.element.css("display",""),t.inited?this._invokeNgController():(t.inited=!0,e.each(t.panes,function(){this.options.initial?this.navigateToInitial():this.navigate("")}),t.trigger("init",{view:t})),t.trigger("show",{view:t})}});i.plugin(s)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.application.min",["kendo.mobile.pane.min","kendo.router.min"],e)}(function(){return function(e,t){function n(e,t){var n=[];return p&&n.push("km-on-"+p.name),n.push(e.skin?"km-"+e.skin:"ios"==e.name&&e.majorVersion>6?"km-ios7":"km-"+e.name),("ios"==e.name&&e.majorVersion<7||"ios"!=e.name)&&n.push("km-"+e.name+e.majorVersion),n.push("km-"+e.majorVersion),n.push("km-m"+(e.minorVersion?e.minorVersion[0]:0)),e.variant&&(e.skin&&e.skin===e.name||!e.skin||e.setDefaultPlatform===!1)&&n.push("km-"+(e.skin?e.skin:e.name)+"-"+e.variant),e.cordova&&n.push("km-cordova"),n.push(e.appMode?"km-app":"km-web"),t&&t.statusBarStyle&&n.push("km-"+t.statusBarStyle+"-status-bar"),n.join(" ")}function i(t){return"km-wp-"+(t.noVariantSet?0===parseInt(e("<div style='background: Background' />").css("background-color").split(",")[1],10)?"dark":"light":t.variant+" km-wp-"+t.variant+"-force")}function r(e){return p.wp?"-kendo-landscape"==e.css("animation-name"):Math.abs(window.orientation)/90==1}function o(e){return r(e)?w:v}function a(e){e.parent().addBack().css("min-height",window.innerHeight)}function s(){e("meta[name=viewport]").remove(),I.append(x({height:", width=device-width"+(r()?", height="+window.innerHeight+"px":u.mobileOS.flatVersion>=600&&u.mobileOS.flatVersion<700?", height="+window.innerWidth+"px":", height=device-height")}))}var l=window.kendo,c=l.mobile,u=l.support,d=c.ui.Widget,h=c.ui.Pane,f="ios7",p=u.mobileOS,g="blackberry"==p.device&&p.flatVersion>=600&&p.flatVersion<1e3&&p.appMode,m=.93,v="km-vertical",_="chrome"===p.browser,y=p.ios&&p.flatVersion>=700&&p.flatVersion<800&&(p.appMode||_),b=Math.abs(window.orientation)/90==1,w="km-horizontal",k={ios7:{ios:!0,browser:"default",device:"iphone",flatVersion:"700",majorVersion:"7",minorVersion:"0.0",name:"ios",tablet:!1},ios:{ios:!0,browser:"default",device:"iphone",flatVersion:"612",majorVersion:"6",minorVersion:"1.2",name:"ios",tablet:!1},android:{android:!0,browser:"default",device:"android",flatVersion:"442",majorVersion:"4",minorVersion:"4.2",name:"android",tablet:!1},blackberry:{blackberry:!0,browser:"default",device:"blackberry",flatVersion:"710",majorVersion:"7",minorVersion:"1.0",name:"blackberry",tablet:!1},meego:{meego:!0,browser:"default",device:"meego",flatVersion:"850",majorVersion:"8",minorVersion:"5.0",name:"meego",tablet:!1},wp:{wp:!0,browser:"default",device:"wp",flatVersion:"800",majorVersion:"8",minorVersion:"0.0",name:"wp",tablet:!1}},x=l.template('<meta content="initial-scale=#: data.scale #, maximum-scale=#: data.scale #, user-scalable=no#=data.height#" name="viewport" />',{usedWithBlock:!1}),S=l.template('<meta name="apple-mobile-web-app-capable" content="#= data.webAppCapable === false ? \'no\' : \'yes\' #" /> <meta name="apple-mobile-web-app-status-bar-style" content="#=data.statusBarStyle#" /> <meta name="msapplication-tap-highlight" content="no" /> ',{usedWithBlock:!1}),T=l.template("<style>.km-view { clip: rect(0 #= data.width #px #= data.height #px 0); }</style>",{usedWithBlock:!1}),C=p.android&&"chrome"!=p.browser||p.blackberry,M=l.template('<link rel="apple-touch-icon'+(p.android?"-precomposed":"")+'" # if(data.size) { # sizes="#=data.size#" #}# href="#=data.icon#" />',{usedWithBlock:!1}),D=("iphone"==p.device||"ipod"==p.device)&&p.majorVersion<7,E=("iphone"==p.device||"ipod"==p.device)&&p.majorVersion>=7,P=E?"none":null,F="mobilesafari"==p.browser?60:0,O=20,z=e(window),A=window.screen,I=e("head"),H="init",V=e.proxy,R=d.extend({init:function(t,n){c.application=this,e(e.proxy(this,"bootstrap",t,n))},bootstrap:function(t,n){var i,r,o;t=e(t),t[0]||(t=e(document.body)),d.fn.init.call(this,t,n),this.element.removeAttr("data-"+l.ns+"role"),this._setupPlatform(),this._attachMeta(),this._setupElementClass(),this._attachHideBarHandlers(),i=e.extend({},this.options),delete i.name,r=this,o=function(){r.pane=new h(r.element,i),r.pane.navigateToInitial(),r.options.updateDocumentTitle&&r._setupDocumentTitle(),r._startHistory(),r.trigger(H)},this.options.$angular?setTimeout(o):o()},options:{name:"Application",hideAddressBar:!0,browserHistory:!0,historyTransition:P,modelScope:window,statusBarStyle:"black",transition:"",retina:!1,platform:null,skin:null,updateDocumentTitle:!0,useNativeScrolling:!1},events:[H],navigate:function(e,t){this.pane.navigate(e,t)},replace:function(e,t){this.pane.replace(e,t)},scroller:function(){return this.view().scroller},hideLoading:function(){if(!this.pane)throw Error("The mobile application instance is not fully instantiated. Please consider activating loading in the application init event handler.");this.pane.hideLoading()},showLoading:function(){if(!this.pane)throw Error("The mobile application instance is not fully instantiated. Please consider activating loading in the application init event handler.");this.pane.showLoading()},changeLoadingMessage:function(e){if(!this.pane)throw Error("The mobile application instance is not fully instantiated. Please consider changing the message in the application init event handler.");this.pane.changeLoadingMessage(e)},view:function(){return this.pane.view()},skin:function(e){var t=this;return arguments.length?(t.options.skin=e||"",t.element[0].className="km-pane",t._setupPlatform(),t._setupElementClass(),t.options.skin):t.options.skin},destroy:function(){d.fn.destroy.call(this),this.pane.destroy(),this.options.browserHistory&&this.router.destroy()},_setupPlatform:function(){var t=this,r=t.options.platform,o=t.options.skin,a=[],s=p||k[f];r&&(s.setDefaultPlatform=!0,"string"==typeof r?(a=r.split("-"),s=e.extend({variant:a[1]},s,k[a[0]])):s=r),o&&(a=o.split("-"),p||(s.setDefaultPlatform=!1),s=e.extend({},s,{skin:a[0],variant:a[1]})),s.variant||(s.noVariantSet=!0,s.variant="dark"),t.os=s,t.osCssClass=n(t.os,t.options),"wp"==s.name&&(t.refreshBackgroundColorProxy||(t.refreshBackgroundColorProxy=e.proxy(function(){(t.os.variant&&t.os.skin&&t.os.skin===t.os.name||!t.os.skin)&&t.element.removeClass("km-wp-dark km-wp-light km-wp-dark-force km-wp-light-force").addClass(i(t.os))},t)),e(document).off("visibilitychange",t.refreshBackgroundColorProxy),e(document).off("resume",t.refreshBackgroundColorProxy),s.skin||(t.element.parent().css("overflow","hidden"),e(document).on("visibilitychange",t.refreshBackgroundColorProxy),e(document).on("resume",t.refreshBackgroundColorProxy),t.refreshBackgroundColorProxy()))},_startHistory:function(){this.options.browserHistory?(this.router=new l.Router({pushState:this.options.pushState,root:this.options.root,hashBang:this.options.hashBang}),this.pane.bindToRouter(this.router),this.router.start()):this.options.initial||this.pane.navigate("")},_resizeToScreenHeight:function(){var t,n=e("meta[name=apple-mobile-web-app-status-bar-style]").attr("content").match(/black-translucent|hidden/),i=this.element;t=_?window.innerHeight:r(i)?n?b?A.availWidth+O:A.availWidth:b?A.availWidth:A.availWidth-O:n?b?A.availHeight:A.availHeight+O:b?A.availHeight-O:A.availHeight,i.height(t)},_setupElementClass:function(){var t,n=this,i=n.element;i.parent().addClass("km-root km-"+(n.os.tablet?"tablet":"phone")),i.addClass(n.osCssClass+" "+o(i)),this.options.useNativeScrolling&&i.parent().addClass("km-native-scrolling"),_&&i.addClass("km-ios-chrome"),u.wpDevicePixelRatio&&i.parent().css("font-size",u.wpDevicePixelRatio+"em"),this.options.retina&&(i.parent().addClass("km-retina"),i.parent().css("font-size",u.devicePixelRatio*m+"em")),g&&s(),n.options.useNativeScrolling?i.parent().addClass("km-native-scrolling"):C&&(t=(screen.availWidth>screen.availHeight?screen.availWidth:screen.availHeight)+200,e(T({width:t,height:t})).appendTo(I)),y&&n._resizeToScreenHeight(),l.onResize(function(){i.removeClass("km-horizontal km-vertical").addClass(o(i)),n.options.useNativeScrolling&&a(i),y&&n._resizeToScreenHeight(),g&&s(),l.resize(i)})},_clearExistingMeta:function(){I.find("meta").filter("[name|='apple-mobile-web-app'],[name|='msapplication-tap'],[name='viewport']").remove()},_attachMeta:function(){var e,t=this.options,n=t.icon;if(this._clearExistingMeta(),g||I.prepend(x({height:"",scale:this.options.retina?1/u.devicePixelRatio:"1.0"})),I.prepend(S(t)),n){"string"==typeof n&&(n={"":n});for(e in n)I.prepend(M({icon:n[e],size:e}))}t.useNativeScrolling&&a(this.element)},_attachHideBarHandlers:function(){var e=this,t=V(e,"_hideBar");!u.mobileOS.appMode&&e.options.hideAddressBar&&D&&!e.options.useNativeScrolling&&(e._initialHeight={},z.on("load",t),l.onResize(function(){setTimeout(window.scrollTo,0,0,1)}))},_setupDocumentTitle:function(){var e=this,n=document.title;e.pane.bind("viewShow",function(e){var i=e.view.title;document.title=i!==t?i:n})},_hideBar:function(){var t=this,n=t.element;n.height(l.support.transforms.css+"calc(100% + "+F+"px)"),e(window).trigger(l.support.resize)}});l.mobile.Application=R,l.ui.plugin(R,l.mobile,"Mobile")}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.actionsheet.min",["kendo.mobile.popover.min","kendo.mobile.shim.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.support,r=n.mobile.ui,o=r.Shim,a=r.Popup,s=r.Widget,l="open",c="close",u="command",d="li>a",h="actionsheetContext",f='<div class="km-actionsheet-wrapper" />',p=n.template('<li class="km-actionsheet-cancel"><a href="\\#">#:cancel#</a></li>'),g=s.extend({init:function(t,l){var c,u,h,g=this,m=i.mobileOS;s.fn.init.call(g,t,l),l=g.options,h=l.type,t=g.element,u="auto"===h?m&&m.tablet:"tablet"===h,c=u?a:o,l.cancelTemplate&&(p=n.template(l.cancelTemplate)),t.addClass("km-actionsheet").append(p({cancel:g.options.cancel})).wrap(f).on("up",d,"_click").on("click",d,n.preventDefault),g.view().bind("destroy",function(){g.destroy()}),g.wrapper=t.parent().addClass(h?" km-actionsheet-"+h:""),g.shim=new c(g.wrapper,e.extend({modal:m.ios&&m.majorVersion<7,className:"km-actionsheet-root"},g.options.popup)),g._closeProxy=e.proxy(g,"_close"),g._shimHideProxy=e.proxy(g,"_shimHide"),g.shim.bind("hide",g._shimHideProxy),u&&n.onResize(g._closeProxy),n.notify(g,r)},events:[l,c,u],options:{name:"ActionSheet",cancel:"Cancel",type:"auto",popup:{height:"auto"}},open:function(t,n){var i=this;i.target=e(t),i.context=n,i.shim.show(t)},close:function(){this.context=this.target=null,this.shim.hide()},openFor:function(e){var t=this,n=e.data(h);t.open(e,n),t.trigger(l,{target:e,context:n})},destroy:function(){s.fn.destroy.call(this),n.unbindResize(this._closeProxy),this.shim.destroy()},_click:function(t){var i,r,o,a;t.isDefaultPrevented()||(i=e(t.currentTarget),r=i.data("action"),r&&(o={target:this.target,context:this.context},a=this.options.$angular,a?this.element.injector().get("$parse")(r)(a[0])(o):n.getter(r)(window)(o)),this.trigger(u,{target:this.target,context:this.context,currentTarget:i}),t.preventDefault(),this._close())},_shimHide:function(e){this.trigger(c)?e.preventDefault():this.context=this.target=null},_close:function(e){this.trigger(c)?e.preventDefault():this.close()}});r.plugin(g)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.button.min",["kendo.userevents.min"],e)}(function(){return function(e,t){function n(t,n,i){e(n.target).closest(".km-button,.km-detail").toggleClass("km-state-active",i),u&&t.deactivateTimeoutID&&(clearTimeout(t.deactivateTimeoutID),t.deactivateTimeoutID=0)}function i(t){return e('<span class="km-badge">'+t+"</span>")}var r=window.kendo,o=r.mobile,a=o.ui,s=a.Widget,l=r.support,c=l.mobileOS,u=c.android&&c.flatVersion>=300,d="click",h="disabled",f="km-state-disabled",p=s.extend({init:function(e,t){var i,o=this;s.fn.init.call(o,e,t),i="up"===o.options.clickOn,o._wrap(),o._style(),i||o.element.attr("data-navigate-on-press",!0),o.options.enable=o.options.enable&&!o.element.attr(h),o.enable(o.options.enable),o._userEvents=new r.UserEvents(o.element,{allowSelection:!i,fastTap:!0,press:function(e){o._activate(e)},release:function(e){n(o,e,!1),i||e.event.stopPropagation()}}),o._userEvents.bind(i?"tap":"press",function(e){o._release(e)}),u&&o.element.on("move",function(e){o._timeoutDeactivate(e)})},destroy:function(){s.fn.destroy.call(this),this._userEvents.destroy()},events:[d],options:{name:"Button",icon:"",style:"",badge:"",clickOn:"up",enable:!0},badge:function(e){var t=this.badgeElement=this.badgeElement||i(e).appendTo(this.element);return e||0===e?(t.html(e),this):e===!1?(t.empty().remove(),this.badgeElement=!1,this):t.html()},enable:function(e){var n=this.element;t===e&&(e=!0),this.options.enable=e,e?n.removeAttr(h):n.attr(h,h),n.toggleClass(f,!e)},_timeoutDeactivate:function(e){this.deactivateTimeoutID||(this.deactivateTimeoutID=setTimeout(n,500,this,e,!1))},_activate:function(e){var t=document.activeElement,i=t?t.nodeName:"";this.options.enable&&(n(this,e,!0),"INPUT"!=i&&"TEXTAREA"!=i||t.blur())},_release:function(n){var i=this;if(!(n.which>1))return i.options.enable?(i.trigger(d,{target:e(n.target),button:i.element})&&n.preventDefault(),t):(n.preventDefault(),t)},_style:function(){var t,n=this.options.style,i=this.element;n&&(t=n.split(" "),e.each(t,function(){i.addClass("km-"+this)}))},_wrap:function(){var t=this,n=t.options.icon,r=t.options.badge,o='<span class="km-icon km-'+n,a=t.element.addClass("km-button"),s=a.children("span:not(.km-icon)").addClass("km-text"),l=a.find("img").addClass("km-image");!s[0]&&a.html()&&(s=a.wrapInner('<span class="km-text" />').children("span.km-text")),!l[0]&&n&&(s[0]||(o+=" km-notext"),t.iconElement=a.prepend(e(o+'" />'))),(r||0===r)&&(t.badgeElement=i(r).appendTo(a))}}),g=p.extend({options:{name:"BackButton",style:"back"},init:function(e,n){var i=this;p.fn.init.call(i,e,n),t===i.element.attr("href")&&i.element.attr("href","#:back")}}),m=p.extend({options:{name:"DetailButton",style:""},init:function(e,t){p.fn.init.call(this,e,t)},_style:function(){var t,n=this.options.style+" detail",i=this.element;n&&(t=n.split(" "),e.each(t,function(){i.addClass("km-"+this)}))},_wrap:function(){var t=this,n=t.options.icon,i='<span class="km-icon km-'+n,r=t.element,o=r.children("span"),a=r.find("img").addClass("km-image");!a[0]&&n&&(o[0]||(i+=" km-notext"),r.prepend(e(i+'" />')))}});a.plugin(p),a.plugin(g),a.plugin(m)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.buttongroup.min",["kendo.core.min"],e)}(function(){return function(e,t){function n(e){return"k-"+e+" km-"+e}function i(t){return e('<span class="'+n("badge")+'">'+t+"</span>")}var r=window.kendo,o=r.mobile.ui,a=o.Widget,s="state-active",l="state-disabled",c="select",u="li:not(.km-"+s+")",d=a.extend({init:function(e,t){var i=this;a.fn.init.call(i,e,t),i.element.addClass("km-buttongroup k-widget k-button-group").find("li").each(i._button),i.element.on(i.options.selectOn,u,"_select"),i._enable=!0,i.select(i.options.index),i.options.enable||(i._enable=!1,i.wrapper.addClass(n(l)))},events:[c],options:{name:"ButtonGroup",selectOn:"down",index:-1,enable:!0},current:function(){return this.element.find(".km-"+s)},select:function(i){var r=this,o=-1;i!==t&&i!==-1&&r._enable&&!e(i).is(".km-"+l)&&(r.current().removeClass(n(s)),"number"==typeof i?(o=i,i=e(r.element[0].children[i])):i.nodeType&&(i=e(i),o=i.index()),i.addClass(n(s)),r.selectedIndex=o)},badge:function(t,n){var r,o=this.element;return isNaN(t)||(t=o.children().get(t)),t=o.find(t),r=e(t.children(".km-badge")[0]||i(n).appendTo(t)),n||0===n?(r.html(n),this):n===!1?(r.empty().remove(),this):r.html()},enable:function(e){t===e&&(e=!0),this.wrapper.toggleClass(n(l),!e),this._enable=this.options.enable=e},_button:function(){var t=e(this).addClass(n("button")),o=r.attrValue(t,"icon"),a=r.attrValue(t,"badge"),s=t.children("span"),l=t.find("img").addClass(n("image"));s[0]||(s=t.wrapInner("<span/>").children("span")),s.addClass(n("text")),!l[0]&&o&&t.prepend(e('<span class="'+n("icon")+" "+n(o)+'"/>')),(a||0===a)&&i(a).appendTo(t)},_select:function(e){e.which>1||e.isDefaultPrevented()||!this._enable||(this.select(e.currentTarget),this.trigger(c,{index:this.selectedIndex}))}});o.plugin(d)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.collapsible.min",["kendo.core.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.mobile.ui,r=i.Widget,o="km-collapsible",a="km-collapsible-header",s="km-collapsible-content",l="km-collapsibleinset",c="<div data-role='collapsible-header' class='"+a+"'></div>",u="<div data-role='collapsible-content' class='"+s+"'></div>",d="km-collapsed",h="km-expanded",f="km-animated",p="left",g="expand",m="collapse",v=r.extend({init:function(t,i){var a=this,s=e(t);r.fn.init.call(a,s,i),s.addClass(o),a._buildHeader(),a.content=s.children().not(a.header).wrapAll(u).parent(),a._userEvents=new n.UserEvents(a.header,{fastTap:!0,tap:function(){a.toggle()}}),s.addClass(a.options.collapsed?d:h),a.options.inset&&s.addClass(l),a.options.animation?(a.content.addClass(f),a.content.height(0),a.options.collapsed&&a.content.hide()):a.options.collapsed&&a.content.hide()},events:[g,m],options:{name:"Collapsible",collapsed:!0,collapseIcon:"arrow-n",expandIcon:"arrow-s",iconPosition:p,animation:!0,inset:!1},destroy:function(){r.fn.destroy.call(this),this._userEvents.destroy()},expand:function(e){var t=this.options.collapseIcon,i=this.content,r=n.support.mobileOS.ios;this.trigger(g)||(t&&this.header.find(".km-icon").removeClass().addClass("km-icon km-"+t),this.element.removeClass(d).addClass(h),this.options.animation&&!e?(i.off("transitionend"),i.show(),r&&i.removeClass(f),i.height(this._getContentHeight()),r&&i.addClass(f),n.resize(i)):i.show())},collapse:function(e){var t=this.options.expandIcon,n=this.content;this.trigger(m)||(t&&this.header.find(".km-icon").removeClass().addClass("km-icon km-"+t),this.element.removeClass(h).addClass(d),this.options.animation&&!e?(n.one("transitionend",function(){n.hide()}),n.height(0)):n.hide())},toggle:function(e){this.isCollapsed()?this.expand(e):this.collapse(e)},isCollapsed:function(){return this.element.hasClass(d)},resize:function(){!this.isCollapsed()&&this.options.animation&&this.content.height(this._getContentHeight())},_buildHeader:function(){var t=this.element.children(":header").wrapAll(c),n=e('<span class="km-icon"/>'),i=this.options.collapsed?this.options.expandIcon:this.options.collapseIcon,r=this.options.iconPosition;i&&(t.prepend(n),n.addClass("km-"+i)),this.header=t.parent(),this.header.addClass("km-icon-"+r)},_getContentHeight:function(){var e,t=this.content.attr("style");return this.content.css({position:"absolute",visibility:"hidden",height:"auto"}),e=this.content.height(),this.content.attr("style",t?t:""),e}});i.plugin(v)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.listview.min",["kendo.data.min","kendo.userevents.min","kendo.mobile.button.min"],e)}(function(){return function(e,t){function n(){return this.nodeType===v.TEXT_NODE&&this.nodeValue.match(j)}function i(e,t){t&&!e[0].querySelector(".km-icon")&&e.prepend('<span class="km-icon km-'+t+'"/>')}function r(e){i(e,M(e,"icon")),i(e,M(e.children(T),"icon"))}function o(e){var t=e.parent(),r=e.add(t.children(m.roleSelector("detailbutton"))),o=t.contents().not(r).not(n);o.length||(e.addClass("km-listview-link").attr(m.attr("role"),"listview-link"),i(e,M(t,"icon")),i(e,M(e,"icon")))}function a(e){if(e[0].querySelector("input[type=checkbox],input[type=radio]")){var t=e.parent();t.contents().not(e).not(function(){return 3==this.nodeType})[0]||(e.addClass("km-listview-label"),e.children("[type=checkbox],[type=radio]").addClass("km-widget km-icon km-check"))}}function s(t,n){e(t).css("transform","translate3d(0px, "+n+"px, 0px)")}var l,c,u,d,h,f,p,g,m=window.kendo,v=window.Node,_=m.mobile,y=_.ui,b=m._outerHeight,w=m.data.DataSource,k=y.DataBoundWidget,x=".km-list > li, > li:not(.km-group-container)",S=".km-listview-link, .km-listview-label",T="["+m.attr("icon")+"]",C=e.proxy,M=m.attrValue,D="km-group-title",E="km-state-active",P='<div class="'+D+'"><div class="km-text"></div></div>',F=m.template('<li><div class="'+D+'"><div class="km-text">#= this.headerTemplate(data) #</div></div><ul>#= kendo.render(this.template, data.items)#</ul></li>'),O='<div class="km-listview-wrapper" />',z=m.template('<form class="km-filter-form"><div class="km-filter-wrap"><input type="search" placeholder="#=placeholder#"/><a href="\\#" class="km-filter-reset" title="Clear"><span class="km-icon km-clear"></span><span class="km-text">Clear</span></a></div></form>'),A=".kendoMobileListView",I="styled",H="dataBound",V="dataBinding",R="itemChange",N="click",B="change",$="progress",L="function",j=/^\s+$/,W=/button/,q=m.Class.extend({init:function(e){var t,n,i=e.scroller();i&&(this.options=e.options,this.element=e.element,this.scroller=e.scroller(),this._shouldFixHeaders(),t=this,n=function(){t._cacheHeaders()},e.bind("resize",n),e.bind(I,n),e.bind(H,n),this._scrollHandler=function(e){t._fixHeader(e)},i.bind("scroll",this._scrollHandler))},destroy:function(){var e=this;e.scroller&&e.scroller.unbind("scroll",e._scrollHandler)},_fixHeader:function(t){if(this.fixedHeaders){var n,i,r,o=0,a=this.scroller,s=this.headers,l=t.scrollTop;do{if(n=s[o++],!n){r=e("<div />");break}i=n.offset,r=n.header}while(i+1>l);this.currentHeader!=o&&(a.fixedContainer.html(r.clone()),this.currentHeader=o)}},_shouldFixHeaders:function(){this.fixedHeaders="group"===this.options.type&&this.options.fixedHeaders},_cacheHeaders:function(){if(this._shouldFixHeaders(),this.fixedHeaders){var t=[],n=this.scroller.scrollTop;this.element.find("."+D).each(function(i,r){r=e(r),t.unshift({offset:r.position().top+n,header:r})}),this.headers=t,this._fixHeader({scrollTop:n})}}}),U=function(){return{page:1}},G=m.Class.extend({init:function(e){var t=this,n=e.options,i=e.scroller(),r=n.pullParameters||U;this.listView=e,this.scroller=i,e.bind("_dataSource",function(e){t.setDataSource(e.dataSource)}),i.setOptions({pullToRefresh:!0,pull:function(){t._pulled||(t._pulled=!0,t.dataSource.read(r.call(e,t._first)))},messages:{pullTemplate:n.messages.pullTemplate,releaseTemplate:n.messages.releaseTemplate,refreshTemplate:n.messages.refreshTemplate}})},setDataSource:function(e){var t=this;this._first=e.view()[0],this.dataSource=e,e.bind("change",function(){t._change()}),e.bind("error",function(){t._change()})},_change:function(){var e,t=this.scroller,n=this.dataSource;this._pulled&&t.pullHandled(),!this._pulled&&this._first||(e=n.view(),e[0]&&(this._first=e[0])),this._pulled=!1}}),Q=m.Observable.extend({init:function(e){var t=this;m.Observable.fn.init.call(t),t.buffer=e.buffer,t.height=e.height,t.item=e.item,t.items=[],t.footer=e.footer,t.buffer.bind("reset",function(){t.refresh()})},refresh:function(){for(var e,t,n,i,r=this.buffer,o=this.items,a=!1;o.length;)o.pop().destroy();for(this.offset=r.offset,e=this.item,i=0;i<r.viewSize;i++){if(i===r.total()){a=!0;break}n=e(this.content(this.offset+o.length)),n.below(t),t=n,o.push(n)}this.itemCount=o.length,this.trigger("reset"),this._resize(),a&&this.trigger("endReached")},totalHeight:function(){if(!this.items[0])return 0;var e=this,t=e.items,n=t[0].top,i=t[t.length-1].bottom,r=(i-n)/e.itemCount,o=e.buffer.length-e.offset-e.itemCount;return(this.footer?this.footer.height:0)+i+o*r},batchUpdate:function(e){var t,n,i=this.height(),r=this.items,o=this.offset;if(r[0]){if(this.lastDirection)for(;r[r.length-1].bottom>e+2*i&&0!==this.offset;)this.offset--,t=r.pop(),t.update(this.content(this.offset)),t.above(r[0]),r.unshift(t);else for(;r[0].top<e-i;){if(n=this.offset+this.itemCount,n===this.buffer.total()){this.trigger("endReached");break}if(n===this.buffer.length)break;t=r.shift(),t.update(this.content(this.offset+this.itemCount)),t.below(r[r.length-1]),r.push(t),this.offset++}o!==this.offset&&this._resize()}},update:function(e){var t,n,i,r,o=this,a=this.items,s=this.height(),l=this.itemCount,c=s/2,u=(this.lastTop||0)>e,d=e-c,h=e+s+c;a[0]&&(this.lastTop=e,this.lastDirection=u,u?a[0].top>d&&a[a.length-1].bottom>h+c&&this.offset>0&&(this.offset--,t=a.pop(),n=a[0],t.update(this.content(this.offset)),a.unshift(t),t.above(n),o._resize()):a[a.length-1].bottom<h&&a[0].top<d-c&&(r=this.offset+l,r===this.buffer.total()?this.trigger("endReached"):r!==this.buffer.length&&(t=a.shift(),i=a[a.length-1],a.push(t),t.update(this.content(this.offset+this.itemCount)),o.offset++,t.below(i),o._resize())))},content:function(e){return this.buffer.at(e)},destroy:function(){this.unbind()},_resize:function(){var e=this.items,t=0,n=0,i=e[0],r=e[e.length-1];i&&(t=i.top,n=r.bottom),this.trigger("resize",{top:t,bottom:n}),this.footer&&this.footer.below(r)}});m.mobile.ui.VirtualList=Q,l=m.Class.extend({init:function(t,n){var i=t.append([n],!0)[0],r=i.offsetHeight;e.extend(this,{top:0,element:i,listView:t,height:r,bottom:r})},update:function(e){this.element=this.listView.setDataItem(this.element,e)},above:function(e){e&&(this.height=this.element.offsetHeight,this.top=e.top-this.height,this.bottom=e.top,s(this.element,this.top))},below:function(e){e&&(this.height=this.element.offsetHeight,this.top=e.bottom,this.bottom=this.top+this.height,s(this.element,this.top))},destroy:function(){m.destroy(this.element),e(this.element).remove()}}),c='<div><span class="km-icon"></span><span class="km-loading-left"></span><span class="km-loading-right"></span></div>',u=m.Class.extend({init:function(t){this.element=e('<li class="km-load-more km-scroller-refresh" style="display: none"></li>').appendTo(t.element),this._loadIcon=e(c).appendTo(this.element)},enable:function(){this.element.show(),this.height=b(this.element,!0)},disable:function(){this.element.hide(),this.height=0},below:function(e){e&&(this.top=e.bottom,this.bottom=this.height+this.top,s(this.element,this.top))}}),d=u.extend({init:function(t,n){this._loadIcon=e(c).hide(),this._loadButton=e('<a class="km-load">'+t.options.messages.loadMoreText+"</a>").hide(),this.element=e('<li class="km-load-more" style="display: none"></li>').append(this._loadIcon).append(this._loadButton).appendTo(t.element);var i=this;this._loadButton.kendoMobileButton().data("kendoMobileButton").bind("click",function(){i._hideShowButton(),n.next()}),n.bind("resize",function(){i._showLoadButton()}),this.height=b(this.element,!0),this.disable()},_hideShowButton:function(){this._loadButton.hide(),this.element.addClass("km-scroller-refresh"),this._loadIcon.css("display","block")},_showLoadButton:function(){this._loadButton.show(),this.element.removeClass("km-scroller-refresh"),this._loadIcon.hide()}}),h=m.Class.extend({init:function(e){var t=this;this.chromeHeight=b(e.wrapper.children().not(e.element)),this.listView=e,this.scroller=e.scroller(),this.options=e.options,e.bind("_dataSource",function(e){t.setDataSource(e.dataSource,e.empty)}),e.bind("resize",function(){t.list.items.length&&(t.scroller.reset(),t.buffer.range(0),t.list.refresh())}),this.scroller.makeVirtual(),this._scroll=function(e){t.list.update(e.scrollTop)},this.scroller.bind("scroll",this._scroll),this._scrollEnd=function(e){t.list.batchUpdate(e.scrollTop)},this.scroller.bind("scrollEnd",this._scrollEnd)},destroy:function(){this.list.unbind(),this.buffer.unbind(),this.scroller.unbind("scroll",this._scroll),this.scroller.unbind("scrollEnd",this._scrollEnd)},setDataSource:function(t,n){var i,r,o,a,s=this,c=this.options,h=this.listView,f=h.scroller(),p=c.loadMore;if(this.dataSource=t,i=t.pageSize()||c.virtualViewSize,!i&&!n)throw Error("the DataSource does not have page size configured. Page Size setting is mandatory for the mobile listview virtual scrolling to work as expected.");this.buffer&&this.buffer.destroy(),r=new m.data.Buffer(t,Math.floor(i/2),p),o=p?new d(h,r):new u(h),this.list&&this.list.destroy(),a=new Q({buffer:r,footer:o,item:function(e){return new l(h,e)},height:function(){return f.height()}}),a.bind("resize",function(){s.updateScrollerSize(),h.updateSize()}),a.bind("reset",function(){s.footer.enable()}),a.bind("endReached",function(){o.disable(),s.updateScrollerSize()}),r.bind("expand",function(){a.lastDirection=!1,a.batchUpdate(f.scrollTop)}),e.extend(this,{buffer:r,scroller:f,list:a,footer:o})},updateScrollerSize:function(){this.scroller.virtualSize(0,this.list.totalHeight()+this.chromeHeight)},refresh:function(){this.list.refresh()},reset:function(){this.buffer.range(0),this.list.refresh()}}),f=m.Class.extend({init:function(e){var t,n=this;this.listView=e,this.options=e.options,t=this,this._refreshHandler=function(e){t.refresh(e)},this._progressHandler=function(){e.showLoading()},e.bind("_dataSource",function(e){n.setDataSource(e.dataSource)})},destroy:function(){this._unbindDataSource()},reset:function(){},refresh:function(e){var n,i,r,o,a,s,l,c=e&&e.action,u=e&&e.items,d=this.listView,h=this.dataSource,f=this.options.appendOnRefresh,p=h.view(),g=h.group(),m=g&&g[0];return"itemchange"===c?(d._hasBindingTarget()||(n=d.findByDataItem(u)[0],n&&d.setDataItem(n,u[0])),t):(a="add"===c&&!m||f&&!d._filter,s="remove"===c&&!m,a?i=[]:s&&(i=d.findByDataItem(u)),d.trigger(V,{action:c||"rebind",items:u,removedItems:i,index:e&&e.index
})?(this._shouldShowLoading()&&d.hideLoading(),t):("add"!==c||m?"remove"!==c||m?m?d.replaceGrouped(p):f&&!d._filter?(r=d.prepend(p),o=p):d.replace(p):(r=[],d.remove(u)):(l=p.indexOf(u[0]),l>-1&&(r=d.insertAt(u,l),o=u)),this._shouldShowLoading()&&d.hideLoading(),d.trigger(H,{ns:y,addedItems:r,addedDataItems:o}),t))},setDataSource:function(e){this.dataSource&&this._unbindDataSource(),this.dataSource=e,e.bind(B,this._refreshHandler),this._shouldShowLoading()&&this.dataSource.bind($,this._progressHandler)},_unbindDataSource:function(){this.dataSource.unbind(B,this._refreshHandler).unbind($,this._progressHandler)},_shouldShowLoading:function(){var e=this.options;return!e.pullToRefresh&&!e.loadMore&&!e.endlessScroll}}),p=m.Class.extend({init:function(t){var n=this,i=t.options.filterable,r="change paste",o=this;this.listView=t,this.options=i,t.element.before(z({placeholder:i.placeholder||"Search..."})),i.autoFilter!==!1&&(r+=" keyup"),this.element=t.wrapper.find(".km-search-form"),this.searchInput=t.wrapper.find("input[type=search]").closest("form").on("submit"+A,function(e){e.preventDefault()}).end().on("focus"+A,function(){n._oldFilter=n.searchInput.val()}).on(r.split(" ").join(A+" ")+A,C(this._filterChange,this)),this.clearButton=t.wrapper.find(".km-filter-reset").on(N,C(this,"_clearFilter")).hide(),this._dataSourceChange=e.proxy(this._refreshInput,this),t.bind("_dataSource",function(e){e.dataSource.bind("change",o._dataSourceChange)})},_refreshInput:function(){var e=this.listView.dataSource.filter(),t=this.listView._filter.searchInput;t.val(e&&e.filters[0].field===this.listView.options.filterable.field?e.filters[0].value:"")},_search:function(e){this._filter=!0,this.clearButton[e?"show":"hide"](),this.listView.dataSource.filter(e)},_filterChange:function(e){var t=this;"paste"==e.type&&this.options.autoFilter!==!1?setTimeout(function(){t._applyFilter()},1):this._applyFilter()},_applyFilter:function(){var e=this.options,t=this.searchInput.val(),n=t.length?{field:e.field,operator:e.operator||"startswith",ignoreCase:e.ignoreCase,value:t}:null;t!==this._oldFilter&&(this._oldFilter=t,this._search(n))},_clearFilter:function(e){this.searchInput.val(""),this._search(null),e.preventDefault()}}),g=k.extend({init:function(t,n){var i=this;k.fn.init.call(this,t,n),t=this.element,n=this.options,n.scrollTreshold&&(n.scrollThreshold=n.scrollTreshold),t.on("down",S,"_highlight").on("move up cancel",S,"_dim"),this._userEvents=new m.UserEvents(t,{fastTap:!0,filter:x,allowSelection:!0,tap:function(e){i._click(e)}}),t.css("-ms-touch-action","auto"),t.wrap(O),this.wrapper=this.element.parent(),this._headerFixer=new q(this),this._itemsCache={},this._templates(),this.virtual=n.endlessScroll||n.loadMore,this._style(),this.options.$angular&&(this.virtual||this.options.pullToRefresh)?setTimeout(e.proxy(this,"_start")):this._start()},_start:function(){var e=this.options;this.options.filterable&&(this._filter=new p(this)),this._itemBinder=this.virtual?new h(this):new f(this),this.options.pullToRefresh&&(this._pullToRefreshHandler=new G(this)),this.setDataSource(e.dataSource),this._enhanceItems(this.items()),m.notify(this,y)},events:[N,V,H,R],options:{name:"ListView",style:"",type:"flat",autoBind:!0,fixedHeaders:!1,template:"#:data#",headerTemplate:'<span class="km-text">#:value#</span>',appendOnRefresh:!1,loadMore:!1,endlessScroll:!1,scrollThreshold:30,pullToRefresh:!1,messages:{loadMoreText:"Press to load more",pullTemplate:"Pull to refresh",releaseTemplate:"Release to refresh",refreshTemplate:"Refreshing"},pullOffset:140,filterable:!1,virtualViewSize:null},refresh:function(){this._itemBinder.refresh()},reset:function(){this._itemBinder.reset()},setDataSource:function(e){var t=!e;this.dataSource=w.create(e),this.trigger("_dataSource",{dataSource:this.dataSource,empty:t}),this.options.autoBind&&!t&&(this.items().remove(),this.dataSource.fetch())},destroy:function(){k.fn.destroy.call(this),m.destroy(this.element),this._userEvents.destroy(),this._itemBinder&&this._itemBinder.destroy(),this._headerFixer&&this._headerFixer.destroy(),this.element.unwrap(),delete this.element,delete this.wrapper,delete this._userEvents},items:function(){return"group"===this.options.type?this.element.find(".km-list").children():this.element.children().not(".km-load-more")},scroller:function(){return this._scrollerInstance||(this._scrollerInstance=this.element.closest(".km-scroll-wrapper").data("kendoMobileScroller")),this._scrollerInstance},showLoading:function(){var e=this.view();e&&e.loader&&e.loader.show()},hideLoading:function(){var e=this.view();e&&e.loader&&e.loader.hide()},insertAt:function(e,t,n){var i=this;return i._renderItems(e,function(r){if(0===t?i.element.prepend(r):t===-1?i.element.append(r):i.items().eq(t-1).after(r),n)for(var o=0;o<r.length;o++)i.trigger(R,{item:r.eq(o),data:e[o],ns:y})})},append:function(e,t){return this.insertAt(e,-1,t)},prepend:function(e,t){return this.insertAt(e,0,t)},replace:function(e){return this.options.type="flat",this._angularItems("cleanup"),m.destroy(this.element.children()),this.element.empty(),this._userEvents.cancel(),this._style(),this.insertAt(e,0)},replaceGrouped:function(t){this.options.type="group",this._angularItems("cleanup"),this.element.empty();var n=e(m.render(this.groupTemplate,t));this._enhanceItems(n.children("ul").children("li")),this.element.append(n),_.init(n),this._style(),this._angularItems("compile")},remove:function(e){var t=this.findByDataItem(e);this.angular("cleanup",function(){return{elements:t}}),m.destroy(t),t.remove()},findByDataItem:function(e){var t,n,i=[];for(t=0,n=e.length;t<n;t++)i[t]="[data-"+m.ns+"uid="+e[t].uid+"]";return this.element.find(i.join(","))},setDataItem:function(t,n){var i=this,r=function(r){var o=e(r[0]);m.destroy(t),i.angular("cleanup",function(){return{elements:[e(t)]}}),e(t).replaceWith(o),i.trigger(R,{item:o,data:n,ns:y})};return this._renderItems([n],r)[0]},updateSize:function(){this._size=this.getSize()},_renderItems:function(t,n){var i=e(m.render(this.template,t));return n(i),this.angular("compile",function(){return{elements:i,data:t.map(function(e){return{dataItem:e}})}}),_.init(i),this._enhanceItems(i),i},_dim:function(e){this._toggle(e,!1)},_highlight:function(e){this._toggle(e,!0)},_toggle:function(t,n){if(!(t.which>1)){var i=e(t.currentTarget),r=i.parent(),o=M(i,"role")||"",a=!o.match(W),s=t.isDefaultPrevented();a&&r.toggleClass(E,n&&!s)}},_templates:function(){var e=this.options.template,t=this.options.headerTemplate,n=' data-uid="#=arguments[0].uid || ""#"',i={},r={};typeof e===L&&(i.template=e,e="#=this.template(data)#"),this.template=C(m.template("<li"+n+">"+e+"</li>"),i),r.template=this.template,typeof t===L&&(r._headerTemplate=t,t="#=this._headerTemplate(data)#"),r.headerTemplate=m.template(t),this.groupTemplate=C(F,r)},_click:function(t){if(!(t.event.which>1||t.event.isDefaultPrevented())){var n,i=t.target,r=e(t.event.target),o=r.closest(m.roleSelector("button","detailbutton","backbutton")),a=m.widgetInstance(o,y),s=i.attr(m.attr("uid"));s&&(n=this.dataSource.getByUid(s)),this.trigger(N,{target:r,item:i,dataItem:n,button:a})&&t.preventDefault()}},_styleGroups:function(){var t=this.element.children();t.children("ul").addClass("km-list"),t.each(function(){var t=e(this),n=t.contents().first();t.addClass("km-group-container"),n.is("ul")||n.is("div."+D)||n.wrap(P)})},_style:function(){var e=this.options,t="group"===e.type,n=this.element,i="inset"===e.style;n.addClass("km-listview").toggleClass("km-list",!t).toggleClass("km-virtual-list",this.virtual).toggleClass("km-listinset",!t&&i).toggleClass("km-listgroup",t&&!i).toggleClass("km-listgroupinset",t&&i),n.parents(".km-listview")[0]||n.closest(".km-content").toggleClass("km-insetcontent",i),t&&this._styleGroups(),this.trigger(I)},_enhanceItems:function(t){t.each(function(){var t,n=e(this),i=!1;n.children().each(function(){t=e(this),t.is("a")?(o(t),i=!0):t.is("label")&&(a(t),i=!0)}),i||r(n)})}}),y.plugin(g)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.navbar.min",["kendo.core.min"],e)}(function(){return function(e,t){function n(t,n){var i=n.find("["+r.attr("align")+"="+t+"]");if(i[0])return e('<div class="km-'+t+'item" />').append(i).prependTo(n)}function i(t){var n=t.siblings(),i=!!t.children("ul")[0],o=!!n[0]&&""===e.trim(t.text()),a=!(!r.mobile.application||!r.mobile.application.element.is(".km-android"));t.prevAll().toggleClass("km-absolute",i),t.toggleClass("km-show-title",o),t.toggleClass("km-fill-title",o&&!e.trim(t.html())),t.toggleClass("km-no-title",i),t.toggleClass("km-hide-title",a&&!n.children().is(":visible"))}var r=window.kendo,o=r.mobile,a=o.ui,s=a.Widget,l=s.extend({init:function(t,i){var r=this;s.fn.init.call(r,t,i),t=r.element,r.container().bind("show",e.proxy(this,"refresh")),t.addClass("km-navbar").wrapInner(e('<div class="km-view-title km-show-title" />')),r.leftElement=n("left",t),r.rightElement=n("right",t),r.centerElement=t.find(".km-view-title")},options:{name:"NavBar"},title:function(e){this.element.find(r.roleSelector("view-title")).text(e),i(this.centerElement)},refresh:function(e){var t=e.view;this.title(t.options.title)},destroy:function(){s.fn.destroy.call(this),r.destroy(this.element)}});a.plugin(l)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.scrollview.min",["kendo.fx.min","kendo.data.min","kendo.draganddrop.min"],e)}(function(){return function(e,t){function n(e){return"k-"+e+" km-"+e}var i,r,o,a,s,l,c,u,d=window.kendo,h=d.mobile,f=h.ui,p=e.proxy,g=d.effects.Transition,m=d.ui.Pane,v=d.ui.PaneDimensions,_=f.DataBoundWidget,y=d.data.DataSource,b=d.data.Buffer,w=d.data.BatchBuffer,k=Math,x=k.abs,S=k.ceil,T=k.round,C=k.max,M=k.min,D=k.floor,E="change",P="changing",F="refresh",O="current-page",z="virtual-page",A="function",I="itemChange",H="cleanup",V=3,R=-1,N=0,B=1,$=-1,L=0,j=1,W=d.Class.extend({init:function(t){var i=this,r=e("<ol class='"+n("pages")+"'/>");t.element.append(r),this._changeProxy=p(i,"_change"),this._refreshProxy=p(i,"_refresh"),t.bind(E,this._changeProxy),t.bind(F,this._refreshProxy),e.extend(i,{element:r,scrollView:t})},items:function(){return this.element.children()},_refresh:function(e){var t,i="";for(t=0;t<e.pageCount;t++)i+="<li/>";this.element.html(i),this.items().eq(e.page).addClass(n(O))},_change:function(e){this.items().removeClass(n(O)).eq(e.page).addClass(n(O))},destroy:function(){this.scrollView.unbind(E,this._changeProxy),this.scrollView.unbind(F,this._refreshProxy),this.element.remove()}});d.mobile.ui.ScrollViewPager=W,i="transitionEnd",r="dragStart",o="dragEnd",a=d.Observable.extend({init:function(t,n){var a,s,l,c,u,h,f=this;d.Observable.fn.init.call(this),this.element=t,this.container=t.parent(),a=new d.ui.Movable(f.element),s=new g({axis:"x",movable:a,onEnd:function(){f.trigger(i)}}),l=new d.UserEvents(t,{fastTap:!0,start:function(e){2*x(e.x.velocity)>=x(e.y.velocity)?l.capture():l.cancel(),f.trigger(r,e),s.cancel()},allowSelection:!0,end:function(e){f.trigger(o,e)}}),c=new v({element:f.element,container:f.container}),u=c.x,u.bind(E,function(){f.trigger(E)}),h=new m({dimensions:c,userEvents:l,movable:a,elastic:!0}),e.extend(f,{duration:n&&n.duration||1,movable:a,transition:s,userEvents:l,dimensions:c,dimension:u,pane:h}),this.bind([i,r,o,E],n)},size:function(){return{width:this.dimensions.x.getSize(),height:this.dimensions.y.getSize()}},total:function(){return this.dimension.getTotal()},offset:function(){return-this.movable.x},updateDimension:function(){this.dimension.update(!0)},refresh:function(){this.dimensions.refresh()},moveTo:function(e){this.movable.moveAxis("x",-e)},transitionTo:function(e,t,n){n?this.moveTo(-e):this.transition.moveTo({location:e,duration:this.duration,ease:t})}}),d.mobile.ui.ScrollViewElasticPane=a,s=d.Observable.extend({init:function(e,t,n){var i=this;d.Observable.fn.init.call(this),i.element=e,i.pane=t,i._getPages(),this.page=0,this.pageSize=n.pageSize||1,this.contentHeight=n.contentHeight,this.enablePager=n.enablePager,this.pagerOverlay=n.pagerOverlay},scrollTo:function(e,t){this.page=e,this.pane.transitionTo(-e*this.pane.size().width,g.easeOutExpo,t)},paneMoved:function(e,t,n,i){var r,o,a=this,s=a.pane,l=s.size().width*a.pageSize,c=T,u=t?g.easeOutBack:g.easeOutExpo;e===$?c=S:e===j&&(c=D),o=c(s.offset()/l),r=C(a.minSnap,M(-o*l,a.maxSnap)),o!=a.page&&n&&n({currentPage:a.page,nextPage:o})&&(r=-a.page*s.size().width),s.transitionTo(r,u,i)},updatePage:function(){var e=this.pane,t=T(e.offset()/e.size().width);return t!=this.page&&(this.page=t,!0)},forcePageUpdate:function(){return this.updatePage()},resizeTo:function(e){var t,n,i=this.pane,r=e.width;this.pageElements.width(r),"100%"===this.contentHeight&&(t=this.element.parent().height(),this.enablePager===!0&&(n=this.element.parent().find("ol.km-pages"),!this.pagerOverlay&&n.length&&(t-=d._outerHeight(n,!0))),this.element.css("height",t),this.pageElements.css("height",t)),i.updateDimension(),this._paged||(this.page=D(i.offset()/r)),this.scrollTo(this.page,!0),this.pageCount=S(i.total()/r),this.minSnap=-(this.pageCount-1)*r,this.maxSnap=0},_getPages:function(){this.pageElements=this.element.find(d.roleSelector("page")),this._paged=this.pageElements.length>0}}),d.mobile.ui.ScrollViewContent=s,l=d.Observable.extend({init:function(e,t,n){var i=this;d.Observable.fn.init.call(this),i.element=e,i.pane=t,i.options=n,i._templates(),i.page=n.page||0,i.pages=[],i._initPages(),i.resizeTo(i.pane.size()),i.pane.dimension.forceEnabled()},setDataSource:function(e){this.dataSource=y.create(e),this._buffer(),this._pendingPageRefresh=!1,this._pendingWidgetRefresh=!1},_viewShow:function(){var e=this;e._pendingWidgetRefresh&&(setTimeout(function(){e._resetPages()},0),e._pendingWidgetRefresh=!1)},_buffer:function(){var e=this.options.itemsPerPage;this.buffer&&this.buffer.destroy(),this.buffer=e>1?new w(this.dataSource,e):new b(this.dataSource,3*e),this._resizeProxy=p(this,"_onResize"),this._resetProxy=p(this,"_onReset"),this._endReachedProxy=p(this,"_onEndReached"),this.buffer.bind({resize:this._resizeProxy,reset:this._resetProxy,endreached:this._endReachedProxy})},_templates:function(){var e=this.options.template,t=this.options.emptyTemplate,n={},i={};typeof e===A&&(n.template=e,e="#=this.template(data)#"),this.template=p(d.template(e),n),typeof t===A&&(i.emptyTemplate=t,t="#=this.emptyTemplate(data)#"),this.emptyTemplate=p(d.template(t),i)},_initPages:function(){var e,t,n=this.pages,i=this.element;for(t=0;t<V;t++)e=new c(i),n.push(e);this.pane.updateDimension()},resizeTo:function(e){var t,n,i,r=this.pages,o=this.pane;for(t=0;t<r.length;t++)r[t].setWidth(e.width);"auto"===this.options.contentHeight?this.element.css("height",this.pages[1].element.height()):"100%"===this.options.contentHeight&&(n=this.element.parent().height(),this.options.enablePager===!0&&(i=this.element.parent().find("ol.km-pages"),!this.options.pagerOverlay&&i.length&&(n-=d._outerHeight(i,!0))),this.element.css("height",n),r[0].element.css("height",n),r[1].element.css("height",n),r[2].element.css("height",n)),o.updateDimension(),this._repositionPages(),this.width=e.width},scrollTo:function(e){var t,n=this.buffer;n.syncDataSource(),t=n.at(e),t&&(this._updatePagesContent(e),this.page=e)},paneMoved:function(e,t,n,i){var r,o=this,a=o.pane,s=a.size().width,l=a.offset(),c=Math.abs(l)>=s/3,u=t?d.effects.Transition.easeOutBack:d.effects.Transition.easeOutExpo,h=o.page+2>o.buffer.total(),f=0;e===j?0!==o.page&&(f=-1):e!==$||h?l>0&&c&&!h?f=1:l<0&&c&&0!==o.page&&(f=-1):f=1,r=o.page,f&&(r=f>0?r+1:r-1),n&&n({currentPage:o.page,nextPage:r})&&(f=0),0===f?o._cancelMove(u,i):f===-1?o._moveBackward(i):1===f&&o._moveForward(i)},updatePage:function(){var e=this.pages;return 0!==this.pane.offset()&&(this.pane.offset()>0?(e.push(this.pages.shift()),this.page++,this.setPageContent(e[2],this.page+1)):(e.unshift(this.pages.pop()),this.page--,this.setPageContent(e[0],this.page-1)),this._repositionPages(),this._resetMovable(),!0)},forcePageUpdate:function(){var e=this.pane.offset(),t=3*this.pane.size().width/4;return x(e)>t&&this.updatePage()},_resetMovable:function(){this.pane.moveTo(0)},_moveForward:function(e){this.pane.transitionTo(-this.width,d.effects.Transition.easeOutExpo,e)},_moveBackward:function(e){this.pane.transitionTo(this.width,d.effects.Transition.easeOutExpo,e)},_cancelMove:function(e,t){this.pane.transitionTo(0,e,t)},_resetPages:function(){this.page=this.options.page||0,this._updatePagesContent(this.page),this._repositionPages(),this.trigger("reset")},_onResize:function(){this.pageCount=S(this.dataSource.total()/this.options.itemsPerPage),this._pendingPageRefresh&&(this._updatePagesContent(this.page),this._pendingPageRefresh=!1),this.trigger("resize")},_onReset:function(){this.pageCount=S(this.dataSource.total()/this.options.itemsPerPage),this._resetPages()},_onEndReached:function(){this._pendingPageRefresh=!0},_repositionPages:function(){var e=this.pages;e[0].position(R),e[1].position(N),e[2].position(B)},_updatePagesContent:function(e){var t=this.pages,n=e||0;this.setPageContent(t[0],n-1),this.setPageContent(t[1],n),this.setPageContent(t[2],n+1)},setPageContent:function(t,n){var i=this.buffer,r=this.template,o=this.emptyTemplate,a=null;n>=0&&(a=i.at(n),e.isArray(a)&&!a.length&&(a=null)),this.trigger(H,{item:t.element}),t.content(null!==a?r(a):o({})),d.mobile.init(t.element),this.trigger(I,{item:t.element,data:a,ns:d.mobile.ui})}}),d.mobile.ui.VirtualScrollViewContent=l,c=d.Class.extend({init:function(t){this.element=e("<div class='"+n(z)+"'></div>"),this.width=t.width(),this.element.width(this.width),t.append(this.element)},content:function(e){this.element.html(e)},position:function(e){this.element.css("transform","translate3d("+this.width*e+"px, 0, 0)")},setWidth:function(e){this.width=e,this.element.width(e)}}),d.mobile.ui.VirtualPage=c,u=_.extend({init:function(e,t){var i,r,o,c=this;_.fn.init.call(c,e,t),t=c.options,e=c.element,d.stripWhitespace(e[0]),e.wrapInner("<div/>").addClass("k-widget "+n("scrollview")),this.options.enablePager&&(this.pager=new W(this),this.options.pagerOverlay&&e.addClass(n("scrollview-overlay"))),c.inner=e.children().first(),c.page=0,c.inner.css("height",t.contentHeight),c.pane=new a(c.inner,{duration:this.options.duration,transitionEnd:p(this,"_transitionEnd"),dragStart:p(this,"_dragStart"),dragEnd:p(this,"_dragEnd"),change:p(this,F)}),c.bind("resize",function(){c.pane.refresh()}),c.page=t.page,i=0===this.inner.children().length,r=i?new l(c.inner,c.pane,t):new s(c.inner,c.pane,t),r.page=c.page,r.bind("reset",function(){this._pendingPageRefresh=!1,c._syncWithContent(),c.trigger(F,{pageCount:r.pageCount,page:r.page})}),r.bind("resize",function(){c.trigger(F,{pageCount:r.pageCount,page:r.page})}),r.bind(I,function(e){c.trigger(I,e),c.angular("compile",function(){return{elements:e.item,data:[{dataItem:e.data}]}})}),r.bind(H,function(e){c.angular("cleanup",function(){return{elements:e.item}})}),c._content=r,c.setDataSource(t.dataSource),o=c.container(),o.nullObject?(c.viewInit(),c.viewShow()):o.bind("show",p(this,"viewShow")).bind("init",p(this,"viewInit"))},options:{name:"ScrollView",page:0,duration:400,velocityThreshold:.8,contentHeight:"auto",pageSize:1,itemsPerPage:1,bounceVelocityThreshold:1.6,enablePager:!0,pagerOverlay:!1,autoBind:!0,template:"",emptyTemplate:""},events:[P,E,F],destroy:function(){_.fn.destroy.call(this),d.destroy(this.element)},viewInit:function(){this.options.autoBind&&this._content.scrollTo(this._content.page,!0)},viewShow:function(){this.pane.refresh()},refresh:function(){var e=this._content;e.resizeTo(this.pane.size()),this.page=e.page,this.trigger(F,{pageCount:e.pageCount,page:e.page})},content:function(e){this.element.children().first().html(e),this._content._getPages(),this.pane.refresh()},value:function(e){var n=this.dataSource;return e?(this.scrollTo(n.indexOf(e),!0),t):n.at(this.page)},scrollTo:function(e,t){this._content.scrollTo(e,t),this._syncWithContent()},prev:function(){var e=this,n=e.page-1;e._content instanceof l?e._content.paneMoved(j,t,function(t){return e.trigger(P,t)}):n>-1&&e.scrollTo(n)},next:function(){var e=this,n=e.page+1;e._content instanceof l?e._content.paneMoved($,t,function(t){return e.trigger(P,t)}):n<e._content.pageCount&&e.scrollTo(n)},setDataSource:function(e){if(this._content instanceof l){var t=!e;this.dataSource=y.create(e),this._content.setDataSource(this.dataSource),this.options.autoBind&&!t&&this.dataSource.fetch()}},items:function(){return this.element.find(".km-"+z)},_syncWithContent:function(){var e,n,i=this._content.pages,r=this._content.buffer;this.page=this._content.page,e=r?r.at(this.page):t,e instanceof Array||(e=[e]),n=i?i[1].element:t,this.trigger(E,{page:this.page,element:n,data:e})},_dragStart:function(){this._content.forcePageUpdate()&&this._syncWithContent()},_dragEnd:function(e){var t=this,n=e.x.velocity,i=this.options.velocityThreshold,r=L,o=x(n)>this.options.bounceVelocityThreshold;n>i?r=j:n<-i&&(r=$),this._content.paneMoved(r,o,function(e){return t.trigger(P,e)})},_transitionEnd:function(){this._content.updatePage()&&this._syncWithContent()}}),f.plugin(u)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.switch.min",["kendo.fx.min","kendo.userevents.min"],e)}(function(){return function(e,t){function n(e){return"km-"+e}function i(e,t,n){return Math.max(t,Math.min(n,e))}var r=window.kendo,o=r.mobile.ui,a=r._outerWidth,s=o.Widget,l=r.support,c="change",u="switch-on",d="switch-off",h="margin-left",f="state-active",p="state-disabled",g="disabled",m=l.transitions.css===t?"":l.transitions.css,v=m+"transform",_=e.proxy,y='<span class="'+n("switch")+" "+n("widget")+'">        <span class="'+n("switch-wrapper")+'">            <span class="'+n("switch-background")+'"></span>        </span>         <span class="'+n("switch-container")+'">            <span class="'+n("switch-handle")+'">                 <span class="'+n("switch-label-on")+'">{0}</span>                 <span class="'+n("switch-label-off")+'">{1}</span>             </span>         </span>    </span>',b=s.extend({init:function(t,n){var i,o=this;s.fn.init.call(o,t,n),n=o.options,o.wrapper=e(r.format(y,n.onLabel,n.offLabel)),o.handle=o.wrapper.find(".km-switch-handle"),o.background=o.wrapper.find(".km-switch-background"),o.wrapper.insertBefore(o.element).prepend(o.element),o._drag(),o.origin=parseInt(o.background.css(h),10),o.constrain=0,o.snapPoint=0,t=o.element[0],t.type="checkbox",o._animateBackground=!0,i=o.options.checked,null===i&&(i=t.checked),o.check(i),o.options.enable=o.options.enable&&!o.element.attr(g),o.enable(o.options.enable),o.refresh(),r.notify(o,r.mobile.ui)},refresh:function(){var e=this,t=a(e.handle,!0);e.width=e.wrapper.width(),e.constrain=e.width-t,e.snapPoint=e.constrain/2,"number"!=typeof e.origin&&(e.origin=parseInt(e.background.css(h),10)),e.background.data("origin",e.origin),e.check(e.element[0].checked)},events:[c],options:{name:"Switch",onLabel:"on",offLabel:"off",checked:null,enable:!0},check:function(e){var i=this,r=i.element[0];return e===t?r.checked:(i._position(e?i.constrain:0),r.checked=e,i.wrapper.toggleClass(n(u),e).toggleClass(n(d),!e),t)},value:function(){return this.check.apply(this,arguments)},destroy:function(){s.fn.destroy.call(this),this.userEvents.destroy()},toggle:function(){var e=this;e.check(!e.element[0].checked)},enable:function(e){var i=this.element,r=this.wrapper;t===e&&(e=!0),this.options.enable=e,e?i.removeAttr(g):i.attr(g,g),r.toggleClass(n(p),!e)},_resize:function(){this.refresh()},_move:function(e){var t=this;e.preventDefault(),t._position(i(t.position+e.x.delta,0,t.width-a(t.handle,!0)))},_position:function(e){var t=this;t.position=e,t.handle.css(v,"translatex("+e+"px)"),t._animateBackground&&t.background.css(h,t.origin+e)},_start:function(){this.options.enable?(this.userEvents.capture(),this.handle.addClass(n(f))):this.userEvents.cancel()},_stop:function(){var e=this;e.handle.removeClass(n(f)),e._toggle(e.position>e.snapPoint)},_toggle:function(e){var t,i=this,o=i.handle,a=i.element[0],s=a.checked,l=r.mobile.application&&r.mobile.application.os.wp?100:200;i.wrapper.toggleClass(n(u),e).toggleClass(n(d),!e),i.position=t=e*i.constrain,i._animateBackground&&i.background.kendoStop(!0,!0).kendoAnimate({effects:"slideMargin",offset:t,reset:!0,reverse:!e,axis:"left",duration:l}),o.kendoStop(!0,!0).kendoAnimate({effects:"slideTo",duration:l,offset:t+"px,0",reset:!0,complete:function(){s!==e&&(a.checked=e,i.trigger(c,{checked:e}))}})},_drag:function(){var e=this;e.userEvents=new r.UserEvents(e.wrapper,{fastTap:!0,tap:function(){e.options.enable&&e._toggle(!e.element[0].checked)},start:_(e._start,e),move:_(e._move,e),end:_(e._stop,e)})}});o.plugin(b)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.tabstrip.min",["kendo.core.min"],e)}(function(){return function(e,t){function n(t){return e('<span class="km-badge">'+t+"</span>")}var i=window.kendo,r=i.mobile.ui,o=r.Widget,a="km-state-active",s="select",l=o.extend({init:function(t,n){var i=this;o.fn.init.call(i,t,n),i.container().bind("show",e.proxy(this,"refresh")),i.element.addClass("km-tabstrip").find("a").each(i._buildButton).eq(i.options.selectedIndex).addClass(a),i.element.on("down","a","_release")},events:[s],switchTo:function(t){var n,i,r=this.element.find("a"),o=0,a=r.length;if(!isNaN(t))return this._setActiveItem(r.eq(t)),!0;for(;o<a;o++)if(n=r[o],i=n.href.replace(/(\#.+)(\?.+)$/,"$1"),i.indexOf(t,i.length-t.length)!==-1)return this._setActiveItem(e(n)),!0;return!1},switchByFullUrl:function(e){var t;t=this.element.find("a[href$='"+e+"']"),this._setActiveItem(t)},clear:function(){this.currentItem().removeClass(a)},currentItem:function(){return this.element.children("."+a)},badge:function(t,i){var r,o=this.element;return isNaN(t)||(t=o.children().get(t)),t=o.find(t),r=e(t.find(".km-badge")[0]||n(i).insertAfter(t.children(".km-icon"))),i||0===i?(r.html(i),this):i===!1?(r.empty().remove(),this):r.html()},_release:function(t){if(!(t.which>1)){var n=this,i=e(t.currentTarget);i[0]!==n.currentItem()[0]&&(n.trigger(s,{item:i})?t.preventDefault():n._setActiveItem(i))}},_setActiveItem:function(e){e[0]&&(this.clear(),e.addClass(a))},_buildButton:function(){var t=e(this),r=i.attrValue(t,"icon"),o=i.attrValue(t,"badge"),a=t.find("img"),s=e('<span class="km-icon"/>');t.addClass("km-button").attr(i.attr("role"),"tab").contents().not(a).wrapAll('<span class="km-text"/>'),a[0]?a.addClass("km-image").prependTo(t):(t.prepend(s),r&&(s.addClass("km-"+r),(o||0===o)&&n(o).insertAfter(s)))},refresh:function(e){var t=e.view.id;t&&!this.switchTo(e.view.id)&&this.switchTo(t)},options:{name:"TabStrip",selectedIndex:0,enable:!0}});r.plugin(l)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.angular.min",["kendo.core.min"],e)}(function(){return function(e,t,n){"use strict";function i(e){var t=C;try{return C=function(e){return e()},e()}finally{C=t}}function r(t,i,r,c,u,m){function v(){var n,m,v,_,y,x,T;return r.kRebind&&(n=e(e(i)[0].cloneNode(!0))),S=o(t,i,r,c,k).options,i.is("select")&&!function(t){var n,i;if(t.length>0)for(n=e(t[0]),!/\S/.test(n.text())&&/^\?/.test(n.val())&&n.remove(),i=0;i<t.length;i++)e(t[i]).off("$destroy")}(i[0].options),m=k.call(i,E=S).data(c),l(m,t,r,c,u),t.$emit("kendoWidgetCreated",m),v=f(t,m),r.kRebind&&g(m,t,i,n,r.kRebind,v,r),r.kNgDisabled&&(_=r.kNgDisabled,y=t.$eval(_),y&&m.enable(!y),a(m,t,i,_)),r.kNgReadonly&&(x=r.kNgReadonly,T=t.$eval(x),T&&m.readonly(T),s(m,t,i,x)),r.kNgModel&&h(m,t,r.kNgModel),b&&d(m,t,i,b,w),m&&p(m,i),m}var _,y,b,w,k,x,S,T,M,D,P,F,O,z;if(!(i instanceof jQuery))throw Error("The Kendo UI directives require jQuery to be available before AngularJS. Please include jquery before angular in the document.");if(_=r.kNgDelay,y=t.$eval(_),m=m||[],b=m[0],w=m[1],k=e(i)[c],!k)return window.console.error("Could not find: "+c),null;if(x=o(t,i,r,c,k),S=x.options,x.unresolved.length){for(T=[],M=0,D=x.unresolved.length;M<D;M++)P=x.unresolved[M],F=e.Deferred(function(e){var i=t.$watch(P.path,function(t){t!==n&&(i(),e.resolve())})}).promise(),T.push(F);return e.when.apply(null,T).then(v),n}return _&&!y?(O=t.$root||t,z=function(){var e=t.$watch(_,function(t){t!==n&&(e(),i.removeAttr(r.$attr.kNgDelay),_=null,C(v))})},/^\$(digest|apply)$/.test(O.$$phase)?z():t.$apply(z),n):v()}function o(i,r,o,a,s){function l(e,r){var o=t.copy(i.$eval(r));o===n?p.push({option:e,path:r}):c[e]=o}var c,u,d,h,f=a.replace(/^kendo/,""),p=[],g=o.kOptions||o.options,m=i.$eval(g);return g&&m===n&&p.push({option:"options",path:g}),c=t.extend({},o.defaultOptions,m),u=s.widget.prototype.options,d=s.widget.prototype.events,e.each(o,function(e,t){var n,i,r,o;"source"!==e&&"kDataSource"!==e&&"kScopeField"!==e&&"scopeField"!==e&&(n="data"+e.charAt(0).toUpperCase()+e.slice(1),0===e.indexOf("on")&&(i=e.replace(/^on./,function(e){return e.charAt(2).toLowerCase()}),d.indexOf(i)>-1&&(c[i]=t)),u.hasOwnProperty(n)?l(n,t):u.hasOwnProperty(e)&&!O[e]?l(e,t):F[e]||(r=e.match(/^k(On)?([A-Z].*)/),r&&(o=r[2].charAt(0).toLowerCase()+r[2].slice(1),r[1]&&"kOnLabel"!=e?c[o]=t:("kOnLabel"==e&&(o="onLabel"),l(o,t)))))}),h=o.kDataSource||o.source,h&&(c.dataSource=P(i,r,f,h)),c.$angular=[i],{options:c,unresolved:p}}function a(e,t,i,r){return kendo.ui.PanelBar&&e instanceof kendo.ui.PanelBar||kendo.ui.Menu&&e instanceof kendo.ui.Menu?(D.warn("k-ng-disabled specified on a widget that does not have the enable() method: "+e.options.name),n):(t.$watch(r,function(t,n){t!=n&&e.enable(!t)}),n)}function s(e,t,i,r){return"function"!=typeof e.readonly?(D.warn("k-ng-readonly specified on a widget that does not have the readonly() method: "+e.options.name),n):(t.$watch(r,function(t,n){t!=n&&e.readonly(t)}),n)}function l(e,t,n,i,r){if(n[r]){var o=T(n[r]).assign;if(!o)throw Error(r+" attribute used but expression in it is not assignable: "+n[i]);o(t,e)}}function c(e){return/checkbox|radio/i.test(e.attr("type"))?e.prop("checked"):e.val()}function u(e){return z.test(e[0].tagName)}function d(e,t,i,r,o){var a,s,l,d,h;e.value&&(s=!1,a=u(i)?function(){return c(i)}:function(){return e.value()},l=function(){var i=r.$viewValue;i===n&&(i=r.$modelValue),i===n&&(i=null),s=!0,setTimeout(function(){if(s=!1,e){var n=t[e.element.attr("k-ng-model")];n&&(i=n),e.options.autoBind!==!1||e.listView.bound()?e.value(i):i&&e.value(i)}},0)},r.$render=l,setTimeout(function(){r.$render!==l&&(r.$render=l)()}),u(i)&&i.on("change",function(){s=!0}),d=function(e){return function(){var n;s&&!i.is("select")||(e&&o&&(n=o.$pristine),r.$setViewValue(a()),e&&(r.$setPristine(),n&&o.$setPristine()),b(t))}},e.first("change",d(!1)),e.first("spin",d(!1)),kendo.ui.AutoComplete&&e instanceof kendo.ui.AutoComplete||e.first("dataBound",d(!0)),h=a(),isNaN(r.$viewValue)||h==r.$viewValue||(r.$isEmpty(r.$viewValue)?null!=h&&""!==h&&h!=r.$viewValue&&r.$setViewValue(h):e.value(r.$viewValue)),r.$setPristine())}function h(t,i,r){var o,a,s,l,c,u,d,f,p,g,m,v,_;return kendo.ui.DateRangePicker&&t instanceof kendo.ui.DateRangePicker?(o=r.split(","),a=o[0].trim(),h(t._startDateInput,i,a),o[1]?(s=o[1].trim(),h(t._endDateInput,i,s),t.range({start:i[a],end:i[s]})):t.range({start:i[a],end:null}),n):"function"!=typeof t.value?(D.warn("k-ng-model specified on a widget that does not have the value() method: "+t.options.name),n):(l=e(t.element).parents("ng-form, form").first(),c=kendo.getter(l.attr("name"),!0)(i),u=T(r),d=u.assign,f=!1,p=kendo.ui.MultiSelect&&t instanceof kendo.ui.MultiSelect||kendo.ui.RangeSlider&&t instanceof kendo.ui.RangeSlider,g=function(e){return e&&p?e.length:0},m=g(u(i)),t.$angular_setLogicValue(u(i)),v=function(e,i){e===n&&(e=null),f||e==i&&g(e)==m||(m=g(e),t.$angular_setLogicValue(e))},p?i.$watchCollection(r,v):i.$watch(r,v),
_=function(){f=!0,c&&c.$pristine&&c.$setDirty(),b(i,function(){d(i,t.$angular_getLogicValue()),m=g(u(i))}),f=!1},t.first("change",_),t.first("spin",_),n)}function f(e,t){var n=e.$on("$destroy",function(){n(),t&&(kendo.destroy(t.element),t=null)});return n}function p(t,n){function i(){a.disconnect()}function r(){a.observe(e(n)[0],{attributes:!0})}var o,a;window.MutationObserver&&t.wrapper&&(o=[].slice.call(e(n)[0].classList),a=new MutationObserver(function(n){i(),t&&(n.forEach(function(n){var i,r=e(t.wrapper)[0];switch(n.attributeName){case"class":i=[].slice.call(n.target.classList),i.forEach(function(e){o.indexOf(e)<0&&(r.classList.add(e),kendo.ui.ComboBox&&t instanceof kendo.ui.ComboBox&&t.input[0].classList.add(e))}),o.forEach(function(e){i.indexOf(e)<0&&(r.classList.remove(e),kendo.ui.ComboBox&&t instanceof kendo.ui.ComboBox&&t.input[0].classList.remove(e))}),o=i;break;case"disabled":"function"!=typeof t.enable||t.element.attr("readonly")||t.enable(!e(n.target).attr("disabled"));break;case"readonly":"function"!=typeof t.readonly||t.element.attr("disabled")||t.readonly(!!e(n.target).attr("readonly"))}}),r())}),r(),t.first("destroy",i))}function g(t,n,i,r,o,a,s){var l=n.$watch(o,function(o,c){var u,d,h,f,p;t._muteRebind||o===c||(l(),s._cleanUp&&s._cleanUp(),u=j[t.options.name],u&&u.forEach(function(t){var i=n.$eval(s["k"+t]);i&&r.append(e(i).attr(kendo.toHyphens("k"+t),""))}),d=e(t.wrapper)[0],h=e(t.element)[0],f="Upload"===t.options.name,f&&(i=e(h)),p=i.injector().get("$compile"),t._destroy(),a&&a(),t=null,h&&(d&&d.parentNode.replaceChild(h,d),e(i).replaceWith(r)),p(r)(n))},!0);b(n)}function m(e,t){return function(n,i){return e.call(t,n,i)}}function v(e,t){this[e]=kendo.stringify(t)}function _(e,n){function i(e,t){x.directive(e,["directiveFactory",function(n){return n.create(t,e)}])}var r,o,a,s,l=n?"Mobile":"";l+=e.fn.options.name,r=l,o="kendo"+l.charAt(0)+l.substr(1).toLowerCase(),l="kendo"+l,a=l.replace(/([A-Z])/g,"-$1"),I.indexOf(l.replace("kendo",""))==-1&&(s=l===o?[l]:[l,o],t.forEach(s,function(e){x.directive(e,function(){return{restrict:"E",replace:!0,template:function(e,t){var n=A[r]||"div",i=t.kScopeField||t.scopeField;return"<"+n+" "+a+(i?'="'+i+'"':"")+">"+e.html()+"</"+n+">"}}})})),H.indexOf(l.replace("kendo",""))>-1||(i(l,l),o!=l&&i(o,l))}function y(t){return t=e(t),kendo.widgetInstance(t,kendo.ui)||kendo.widgetInstance(t,kendo.mobile.ui)||kendo.widgetInstance(t,kendo.dataviz.ui)}function b(e,t){var n=e.$root||e,i=/^\$(digest|apply)$/.test(n.$$phase);t?i?t():n.$apply(t):i||n.$digest()}function w(t,n){t.$destroy(),n&&e(n).removeData("$scope").removeData("$$kendoScope").removeData("$isolateScope").removeData("$isolateScopeNoTemplate").removeClass("ng-scope")}function k(n,i,r){var o,a,s;if(e.isArray(n))return t.forEach(n,function(e){k(e,i,r)});if("string"==typeof n){for(o=n.split("."),a=kendo;a&&o.length>0;)a=a[o.shift()];if(!a)return L.push([n,i,r]),!1;n=a.prototype}return s=n[i],n[i]=function(){var e=this,t=arguments;return r.apply({self:e,next:function(){return s.apply(e,arguments.length>0?arguments:t)}},t)},!0}var x,S,T,C,M,D,E,P,F,O,z,A,I,H,V,R,N,B,$,L,j;t&&t.injector&&(x=t.module("kendo.directives",[]),S=t.injector(["ng"]),T=S.get("$parse"),C=S.get("$timeout"),D=S.get("$log"),P=function(){var e={TreeList:"TreeListDataSource",TreeView:"HierarchicalDataSource",Scheduler:"SchedulerDataSource",PivotGrid:"PivotDataSource",PivotConfigurator:"PivotDataSource",PanelBar:"HierarchicalDataSource",Menu:"$PLAIN",ContextMenu:"$PLAIN"},t=function(e,t){return"$PLAIN"==t?e:kendo.data[t].create(e)};return function(n,i,r,o){var a=e[r]||"DataSource",s=n.$eval(o),l=t(s,a);return n.$watch(o,function(e){var n,r=y(i);r&&"function"==typeof r.setDataSource&&e!==s&&e!==r.dataSource&&(n=t(e,a),r.setDataSource(n),s=e)}),l}}(),F={kDataSource:!0,kOptions:!0,kRebind:!0,kNgModel:!0,kNgDelay:!0},O={name:!0,title:!0,style:!0},z=/^(input|select|textarea)$/i,x.factory("directiveFactory",["$compile",function(t){var n,i,o=!1;return M=t,i=function(t,i){return{restrict:"AC",require:["?ngModel","^?form"],scope:!1,controller:["$scope","$attrs","$element",function(e,t){this.template=m(v,t),t._cleanUp=m(function(){this.template=null,t._cleanUp=null},this)}],link:function(a,s,l,c){var u,d=e(s),h=t.replace(/([A-Z])/g,"-$1");d.attr(h,d.attr("data-"+h)),d[0].removeAttribute("data-"+h),u=r(a,s,l,t,i,c),u&&(n&&clearTimeout(n),n=setTimeout(function(){a.$emit("kendoRendered"),o||(o=!0,e("form").each(function(){var t=e(this).controller("form");t&&t.$setPristine()}))}))}}},{create:i}}]),A={Editor:"textarea",NumericTextBox:"input",DatePicker:"input",DateTimePicker:"input",TimePicker:"input",AutoComplete:"input",ColorPicker:"input",MaskedTextBox:"input",MultiSelect:"input",Upload:"input",Validator:"form",Button:"button",MobileButton:"a",MobileBackButton:"a",MobileDetailButton:"a",ListView:"ul",MobileListView:"ul",ScrollView:"div",PanelBar:"ul",TreeView:"ul",Menu:"ul",ContextMenu:"ul",ActionSheet:"ul",Switch:"input"},I=["MobileView","MobileDrawer","MobileLayout","MobileSplitView","MobilePane","MobileModalView"],H=["MobileApplication","MobileView","MobileModalView","MobileLayout","MobileActionSheet","MobileDrawer","MobileSplitView","MobilePane","MobileScrollView","MobilePopOver"],t.forEach(["MobileNavBar","MobileButton","MobileBackButton","MobileDetailButton","MobileTabStrip","MobileScrollView","MobileScroller"],function(e){H.push(e),e="kendo"+e,x.directive(e,function(){return{restrict:"A",link:function(t,n,i){r(t,n,i,e,e)}}})}),V=kendo.htmlEncode,R=/{{/g,N=/}}/g,B="{&#8203;{",$="}&#8203;}",kendo.htmlEncode=function(e){return V(e).replace(R,B).replace(N,$)},L=[],kendo.onWidgetRegistered(function(t){L=e.grep(L,function(e){return!k.apply(null,e)}),_(t.widget,"Mobile"==t.prefix)}),k(["ui.Widget","mobile.ui.Widget"],"angular",function(r,o){var a,s=this.self;return"init"==r?(!o&&E&&(o=E),E=null,o&&o.$angular&&(s.$angular_scope=o.$angular[0],s.$angular_init(s.element,o)),n):(a=s.$angular_scope,a&&i(function(){var i,l,c=o(),u=c.elements,d=c.data;if(u.length>0)switch(r){case"cleanup":t.forEach(u,function(t){var n=e(t).data("$$kendoScope");n&&n!==a&&n.$$kendoScope&&w(n,t)});break;case"compile":i=s.element.injector(),l=i?i.get("$compile"):M,t.forEach(u,function(t,i){var r,o;c.scopeFrom?r=c.scopeFrom:(o=d&&d[i],o!==n?(r=e.extend(a.$new(),o),r.$$kendoScope=!0):r=a),e(t).data("$$kendoScope",r),l(t)(r)}),b(a)}}),n)}),k("ui.Widget","$angular_getLogicValue",function(){return this.self.value()}),k("ui.Widget","$angular_setLogicValue",function(e){this.self.value(e)}),k("ui.Select","$angular_getLogicValue",function(){var e=this.self.dataItem(),t=this.self.options.dataValueField;return e?this.self.options.valuePrimitive?t?e[t]:e:e.toJSON():null}),k("ui.Select","$angular_setLogicValue",function(e){var t=this.self,i=t.options,r=i.dataValueField,o=i.text||"";e===n&&(e=""),r&&!i.valuePrimitive&&e&&(o=e[i.dataTextField]||"",e=e[r||i.dataTextField]),t.options.autoBind!==!1||t.listView.bound()?t.value(e):!o&&e&&i.valuePrimitive?t.value(e):t._preselect(e,o)}),k("ui.MultiSelect","$angular_getLogicValue",function(){var t=this.self.dataItems().slice(0),n=this.self.options.dataValueField;return n&&this.self.options.valuePrimitive&&(t=e.map(t,function(e){return e[n]})),t}),k("ui.MultiSelect","$angular_setLogicValue",function(t){var n,i,r,o;null==t&&(t=[]),n=this.self,i=n.options,r=i.dataValueField,o=t,r&&!i.valuePrimitive&&(t=e.map(t,function(e){return e[r]})),i.autoBind!==!1||i.valuePrimitive||n.listView.bound()?n.value(t):n._preselect(o,t)}),k("ui.Widget","$angular_init",function(t,n){var i,r,o,a,s=this.self;if(n&&!e.isArray(n))for(i=s.$angular_scope,r=s.events.length;--r>=0;)o=s.events[r],a=n[o],a&&"string"==typeof a&&(n[o]=s.$angular_makeEventHandler(o,i,a))}),k("ui.Widget","$angular_makeEventHandler",function(e,t,n){return n=T(n),function(e){b(t,function(){n(t,{kendoEvent:e})})}}),k(["ui.Grid","ui.ListView","ui.TreeView","ui.PanelBar"],"$angular_makeEventHandler",function(e,n,i){return"change"!=e?this.next():(i=T(i),function(e){var r,o,a,s,l,c,u,d,h,f=e.sender,p=f.options,g={kendoEvent:e};for(t.isString(p.selectable)&&(r=p.selectable.indexOf("cell")!==-1,o=p.selectable.indexOf("multiple")!==-1),f._checkBoxSelection&&(o=!0),a=g.selected=this.select(),s=g.data=[],l=g.columns=[],u=0;u<a.length;u++)d=r?a[u].parentNode:a[u],h=f.dataItem(d),r?(t.element.inArray(h,s)<0&&s.push(h),c=t.element(a[u]).index(),t.element.inArray(c,l)<0&&l.push(c)):s.push(h);o||(g.dataItem=g.data=s[0],g.angularDataItem=kendo.proxyModelSetters(g.dataItem),g.selected=a[0]),b(n,function(){i(n,g)})})}),k("ui.Grid","$angular_init",function(i,r){if(this.next(),r.columns){var o=e.extend({},kendo.Template,r.templateSettings);t.forEach(r.columns,function(e){!e.field||e.template||e.format||e.values||e.encoded!==n&&!e.encoded||(e.template="<span ng-bind='"+kendo.expr(e.field,"dataItem")+"'>#: "+kendo.expr(e.field,o.paramName)+"#</span>")})}}),k("mobile.ui.ButtonGroup","value",function(e){var t=this.self;return null!=e&&(t.select(t.element.children("li.km-button").eq(e)),t.trigger("change"),t.trigger("select",{index:t.selectedIndex})),t.selectedIndex}),k("mobile.ui.ButtonGroup","_select",function(){this.next(),this.self.trigger("change")}),x.directive("kendoMobileApplication",function(){return{terminal:!0,link:function(e,t,n){r(e,t,n,"kendoMobileApplication","kendoMobileApplication")}}}).directive("kendoMobileView",function(){return{scope:!0,link:{pre:function(e,t,n){n.defaultOptions=e.viewOptions,n._instance=r(e,t,n,"kendoMobileView","kendoMobileView")},post:function(e,t,n){n._instance._layout(),n._instance._scroller()}}}}).directive("kendoMobileDrawer",function(){return{scope:!0,link:{pre:function(e,t,n){n.defaultOptions=e.viewOptions,n._instance=r(e,t,n,"kendoMobileDrawer","kendoMobileDrawer")},post:function(e,t,n){n._instance._layout(),n._instance._scroller()}}}}).directive("kendoMobileModalView",function(){return{scope:!0,link:{pre:function(e,t,n){n.defaultOptions=e.viewOptions,n._instance=r(e,t,n,"kendoMobileModalView","kendoMobileModalView")},post:function(e,t,n){n._instance._layout(),n._instance._scroller()}}}}).directive("kendoMobileSplitView",function(){return{terminal:!0,link:{pre:function(e,t,n){n.defaultOptions=e.viewOptions,n._instance=r(e,t,n,"kendoMobileSplitView","kendoMobileSplitView")},post:function(e,t,n){n._instance._layout()}}}}).directive("kendoMobilePane",function(){return{terminal:!0,link:{pre:function(e,t,n){n.defaultOptions=e.viewOptions,r(e,t,n,"kendoMobilePane","kendoMobilePane")}}}}).directive("kendoMobileLayout",function(){return{link:{pre:function(e,t,n){r(e,t,n,"kendoMobileLayout","kendoMobileLayout")}}}}).directive("kendoMobileActionSheet",function(){return{restrict:"A",link:function(t,n,i){n.find("a[k-action]").each(function(){e(this).attr("data-"+kendo.ns+"action",e(this).attr("k-action"))}),r(t,n,i,"kendoMobileActionSheet","kendoMobileActionSheet")}}}).directive("kendoMobilePopOver",function(){return{terminal:!0,link:{pre:function(e,t,n){n.defaultOptions=e.viewOptions,r(e,t,n,"kendoMobilePopOver","kendoMobilePopOver")}}}}).directive("kendoViewTitle",function(){return{restrict:"E",replace:!0,template:function(e){return"<span data-"+kendo.ns+"role='view-title'>"+e.html()+"</span>"}}}).directive("kendoMobileHeader",function(){return{restrict:"E",link:function(e,t){t.addClass("km-header").attr("data-role","header")}}}).directive("kendoMobileFooter",function(){return{restrict:"E",link:function(e,t){t.addClass("km-footer").attr("data-role","footer")}}}).directive("kendoMobileScrollViewPage",function(){return{restrict:"E",replace:!0,template:function(e){return"<div data-"+kendo.ns+"role='page'>"+e.html()+"</div>"}}}),t.forEach(["align","icon","rel","transition","actionsheetContext"],function(e){var t="k"+e.slice(0,1).toUpperCase()+e.slice(1);x.directive(t,function(){return{restrict:"A",priority:2,link:function(n,i,r){i.attr(kendo.attr(kendo.toHyphens(e)),n.$eval(r[t]))}}})}),j={TreeMap:["Template"],MobileListView:["HeaderTemplate","Template"],MobileScrollView:["EmptyTemplate","Template"],Grid:["AltRowTemplate","DetailTemplate","RowTemplate"],ListView:["EditTemplate","Template","AltTemplate"],Pager:["SelectTemplate","LinkTemplate"],PivotGrid:["ColumnHeaderTemplate","DataCellTemplate","RowHeaderTemplate"],Scheduler:["AllDayEventTemplate","DateHeaderTemplate","EventTemplate","MajorTimeHeaderTemplate","MinorTimeHeaderTemplate"],ScrollView:["Template"],PanelBar:["Template"],TreeView:["Template"],Validator:["ErrorTemplate"]},function(){var e={};t.forEach(j,function(n,i){t.forEach(n,function(t){e[t]||(e[t]=[]),e[t].push("?^^kendo"+i)})}),t.forEach(e,function(e,t){var n="k"+t,i=kendo.toHyphens(n);x.directive(n,function(){return{restrict:"A",require:e,terminal:!0,compile:function(t,r){if(""===r[n]){t.removeAttr(i);var o=t[0].outerHTML;return function(t,r,a,s){for(var l;!l&&s.length;)l=s.shift();l?(l.template(n,o),r.remove()):D.warn(i+" without a matching parent widget found. It can be one of the following: "+e.join(", "))}}}}})})}())}(window.kendo.jQuery,window.angular),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.mobile.min",["kendo.core.min","kendo.fx.min","kendo.data.odata.min","kendo.data.xml.min","kendo.data.min","kendo.data.signalr.min","kendo.binder.min","kendo.validator.min","kendo.router.min","kendo.view.min","kendo.userevents.min","kendo.draganddrop.min","kendo.popup.min","kendo.touch.min","kendo.mobile.popover.min","kendo.mobile.loader.min","kendo.mobile.scroller.min","kendo.mobile.shim.min","kendo.mobile.view.min","kendo.mobile.modalview.min","kendo.mobile.drawer.min","kendo.mobile.splitview.min","kendo.mobile.pane.min","kendo.mobile.application.min","kendo.mobile.actionsheet.min","kendo.mobile.button.min","kendo.mobile.buttongroup.min","kendo.mobile.collapsible.min","kendo.mobile.listview.min","kendo.mobile.navbar.min","kendo.mobile.scrollview.min","kendo.mobile.switch.min","kendo.mobile.tabstrip.min","kendo.angular.min"],e)}(function(){"bundle all";return window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.mobile.min.js.map
