/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.draganddrop.min",["kendo.core.min","kendo.userevents.min"],t)}(function(){return function(t,e){function n(e,n){try{return t.contains(e,n)||e==n}catch(r){return!1}}function r(t,e){return parseInt(t.css(e),10)||0}function i(t,e){return Math.min(Math.max(t,e.min),e.max)}function o(t,e){var n=D(t),i=_._outerWidth,o=_._outerHeight,a=n.left+r(t,"borderLeftWidth")+r(t,"paddingLeft"),s=n.top+r(t,"borderTopWidth")+r(t,"paddingTop"),l=a+t.width()-i(e,!0),c=s+t.height()-o(e,!0);return{x:{min:a,max:l},y:{min:s,max:c}}}function a(n,r,i){for(var o,a,s=0,l=r&&r.length,c=i&&i.length;n&&n.parentNode;){for(s=0;s<l;s++)if(o=r[s],o.element[0]===n)return{target:o,targetElement:n};for(s=0;s<c;s++)if(a=i[s],t.contains(a.element[0],n)&&x.matchesSelector.call(n,a.options.filter))return{target:a,targetElement:n};n=n.parentNode}return e}function s(t,e){var n,r=e.options.group,i=t[r];if(T.fn.destroy.call(e),i.length>1){for(n=0;n<i.length;n++)if(i[n]==e){i.splice(n,1);break}}else i.length=0,delete t[r]}function l(t){var e,n,r,i=c()[0];return t[0]===i?(n=i.scrollTop,r=i.scrollLeft,{top:n,left:r,bottom:n+b.height(),right:r+b.width()}):(e=t.offset(),e.bottom=e.top+t.height(),e.right=e.left+t.width(),e)}function c(){return t(_.support.browser.edge||_.support.browser.safari?y.body:y.documentElement)}function u(e){var n,r=c();if(!e||e===y.body||e===y.documentElement)return r;for(n=t(e)[0];n&&!_.isScrollable(n)&&n!==y.body;)n=n.parentNode;return n===y.body?r:t(n)}function h(t,e,n){var r={x:0,y:0},i=50;return t-n.left<i?r.x=-(i-(t-n.left)):n.right-t<i&&(r.x=i-(n.right-t)),e-n.top<i?r.y=-(i-(e-n.top)):n.bottom-e<i&&(r.y=i-(n.bottom-e)),r}var d,f,p,g,v,m,_=window.kendo,x=_.support,y=window.document,b=t(window),E=_.Class,T=_.ui.Widget,S=_.Observable,w=_.UserEvents,M=t.proxy,C=t.extend,D=_.getOffset,O={},k={},I={},H=_.elementUnderCursor,W="keyup",z="change",P="dragstart",U="hold",L="drag",A="dragend",N="dragcancel",V="hintDestroyed",B="dragenter",$="dragleave",F="drop",j=S.extend({init:function(e,n){var r=this,i=e[0];r.capture=!1,i.addEventListener?(t.each(_.eventMap.down.split(" "),function(){i.addEventListener(this,M(r._press,r),!0)}),t.each(_.eventMap.up.split(" "),function(){i.addEventListener(this,M(r._release,r),!0)})):(t.each(_.eventMap.down.split(" "),function(){i.attachEvent(this,M(r._press,r))}),t.each(_.eventMap.up.split(" "),function(){i.attachEvent(this,M(r._release,r))})),S.fn.init.call(r),r.bind(["press","release"],n||{})},captureNext:function(){this.capture=!0},cancelCapture:function(){this.capture=!1},_press:function(t){var e=this;e.trigger("press"),e.capture&&t.preventDefault()},_release:function(t){var e=this;e.trigger("release"),e.capture&&(t.preventDefault(),e.cancelCapture())}}),G=S.extend({init:function(e){var n=this;S.fn.init.call(n),n.forcedEnabled=!1,t.extend(n,e),n.scale=1,n.horizontal?(n.measure="offsetWidth",n.scrollSize="scrollWidth",n.axis="x"):(n.measure="offsetHeight",n.scrollSize="scrollHeight",n.axis="y")},makeVirtual:function(){t.extend(this,{virtual:!0,forcedEnabled:!0,_virtualMin:0,_virtualMax:0})},virtualSize:function(t,e){this._virtualMin===t&&this._virtualMax===e||(this._virtualMin=t,this._virtualMax=e,this.update())},outOfBounds:function(t){return t>this.max||t<this.min},forceEnabled:function(){this.forcedEnabled=!0},getSize:function(){return this.container[0][this.measure]},getTotal:function(){return this.element[0][this.scrollSize]},rescale:function(t){this.scale=t},update:function(t){var e=this,n=e.virtual?e._virtualMax:e.getTotal(),r=n*e.scale,i=e.getSize();(0!==n||e.forcedEnabled)&&(e.max=e.virtual?-e._virtualMin:0,e.size=i,e.total=r,e.min=Math.min(e.max,i-r),e.minScale=i/n,e.centerOffset=(r-i)/2,e.enabled=e.forcedEnabled||r>i,t||e.trigger(z,e))}}),Q=S.extend({init:function(t){var e=this;S.fn.init.call(e),e.x=new G(C({horizontal:!0},t)),e.y=new G(C({horizontal:!1},t)),e.container=t.container,e.forcedMinScale=t.minScale,e.maxScale=t.maxScale||100,e.bind(z,t)},rescale:function(t){this.x.rescale(t),this.y.rescale(t),this.refresh()},centerCoordinates:function(){return{x:Math.min(0,-this.x.centerOffset),y:Math.min(0,-this.y.centerOffset)}},refresh:function(){var t=this;t.x.update(),t.y.update(),t.enabled=t.x.enabled||t.y.enabled,t.minScale=t.forcedMinScale||Math.min(t.x.minScale,t.y.minScale),t.fitScale=Math.max(t.x.minScale,t.y.minScale),t.trigger(z)}}),q=S.extend({init:function(t){var e=this;C(e,t),S.fn.init.call(e)},outOfBounds:function(){return this.dimension.outOfBounds(this.movable[this.axis])},dragMove:function(t){var e=this,n=e.dimension,r=e.axis,i=e.movable,o=i[r]+t;n.enabled&&((o<n.min&&t<0||o>n.max&&t>0)&&(t*=e.resistance),i.translateAxis(r,t),e.trigger(z,e))}}),J=E.extend({init:function(e){var n,r,i,o,a=this;C(a,{elastic:!0},e),i=a.elastic?.5:0,o=a.movable,a.x=n=new q({axis:"x",dimension:a.dimensions.x,resistance:i,movable:o}),a.y=r=new q({axis:"y",dimension:a.dimensions.y,resistance:i,movable:o}),a.userEvents.bind(["press","move","end","gesturestart","gesturechange"],{gesturestart:function(t){a.gesture=t,a.offset=a.dimensions.container.offset()},press:function(e){t(e.event.target).closest("a").is("[data-navigate-on-press=true]")&&e.sender.cancel()},gesturechange:function(t){var e,i,s,l=a.gesture,c=l.center,u=t.center,h=t.distance/l.distance,d=a.dimensions.minScale,f=a.dimensions.maxScale;o.scale<=d&&h<1&&(h+=.8*(1-h)),o.scale*h>=f&&(h=f/o.scale),i=o.x+a.offset.left,s=o.y+a.offset.top,e={x:(i-c.x)*h+u.x-i,y:(s-c.y)*h+u.y-s},o.scaleWith(h),n.dragMove(e.x),r.dragMove(e.y),a.dimensions.rescale(o.scale),a.gesture=t,t.preventDefault()},move:function(t){t.event.target.tagName.match(/textarea|input/i)||(n.dimension.enabled||r.dimension.enabled?(n.dragMove(t.x.delta),r.dragMove(t.y.delta),t.preventDefault()):t.touch.skip())},end:function(t){t.preventDefault()}})}}),K=x.transitions.prefix+"Transform";f=x.hasHW3D?function(t,e,n){return"translate3d("+t+"px,"+e+"px,0) scale("+n+")"}:function(t,e,n){return"translate("+t+"px,"+e+"px) scale("+n+")"},p=S.extend({init:function(e){var n=this;S.fn.init.call(n),n.element=t(e),n.element[0].style.webkitTransformOrigin="left top",n.x=0,n.y=0,n.scale=1,n._saveCoordinates(f(n.x,n.y,n.scale))},translateAxis:function(t,e){this[t]+=e,this.refresh()},scaleTo:function(t){this.scale=t,this.refresh()},scaleWith:function(t){this.scale*=t,this.refresh()},translate:function(t){this.x+=t.x,this.y+=t.y,this.refresh()},moveAxis:function(t,e){this[t]=e,this.refresh()},moveTo:function(t){C(this,t),this.refresh()},refresh:function(){var t,e=this,n=e.x,r=e.y;e.round&&(n=Math.round(n),r=Math.round(r)),t=f(n,r,e.scale),t!=e.coordinates&&(_.support.browser.msie&&_.support.browser.version<10?(e.element[0].style.position="absolute",e.element[0].style.left=e.x+"px",e.element[0].style.top=e.y+"px"):e.element[0].style[K]=t,e._saveCoordinates(t),e.trigger(z))},_saveCoordinates:function(t){this.coordinates=t}}),g=T.extend({init:function(t,e){var n,r=this;T.fn.init.call(r,t,e),n=r.options.group,n in k?k[n].push(r):k[n]=[r]},events:[B,$,F],options:{name:"DropTarget",group:"default"},destroy:function(){s(k,this)},_trigger:function(t,e){var n=this,r=O[n.options.group];if(r)return n.trigger(t,C({},e.event,{draggable:r,dropTarget:e.dropTarget}))},_over:function(t){this._trigger(B,t)},_out:function(t){this._trigger($,t)},_drop:function(t){var e=this,n=O[e.options.group];n&&(n.dropped=!e._trigger(F,t))}}),g.destroyGroup=function(t){var e,n=k[t]||I[t];if(n){for(e=0;e<n.length;e++)T.fn.destroy.call(n[e]);n.length=0,delete k[t],delete I[t]}},g._cache=k,v=g.extend({init:function(t,e){var n,r=this;T.fn.init.call(r,t,e),n=r.options.group,n in I?I[n].push(r):I[n]=[r]},destroy:function(){s(I,this)},options:{name:"DropTargetArea",group:"default",filter:null}}),m=T.extend({init:function(t,e){var n=this;T.fn.init.call(n,t,e),n._activated=!1,n.userEvents=new w(n.element,{global:!0,allowSelection:!0,filter:n.options.filter,threshold:n.options.distance,start:M(n._start,n),hold:M(n._hold,n),move:M(n._drag,n),end:M(n._end,n),cancel:M(n._cancel,n),select:M(n._select,n)}),n._afterEndHandler=M(n._afterEnd,n),n._captureEscape=M(n._captureEscape,n)},events:[U,P,L,A,N,V],options:{name:"Draggable",distance:_.support.touch?0:5,group:"default",cursorOffset:null,axis:null,container:null,filter:null,ignore:null,holdToDrag:!1,autoScroll:!1,dropped:!1},cancelHold:function(){this._activated=!1},_captureEscape:function(t){var e=this;t.keyCode===_.keys.ESC&&(e._trigger(N,{event:t}),e.userEvents.cancel())},_updateHint:function(e){var n,r=this,o=r.options,a=r.boundaries,s=o.axis,l=r.options.cursorOffset;l?n={left:e.x.location+l.left,top:e.y.location+l.top}:(r.hintOffset.left+=e.x.delta,r.hintOffset.top+=e.y.delta,n=t.extend({},r.hintOffset)),a&&(n.top=i(n.top,a.y),n.left=i(n.left,a.x)),"x"===s?delete n.top:"y"===s&&delete n.left,r.hint.css(n)},_shouldIgnoreTarget:function(e){var n=this.options.ignore;return n&&t(e).is(n)},_select:function(t){this._shouldIgnoreTarget(t.event.target)||t.preventDefault()},_start:function(n){var r,i=this,a=i.options,s=a.container?t(a.container):null,l=a.hint;return this._shouldIgnoreTarget(n.touch.initialTouch)||a.holdToDrag&&!i._activated?(i.userEvents.cancel(),e):(i.currentTarget=n.target,i.currentTargetOffset=D(i.currentTarget),l&&(i.hint&&i.hint.stop(!0,!0).remove(),i.hint=_.isFunction(l)?t(l.call(i,i.currentTarget)):l,r=D(i.currentTarget),i.hintOffset=r,i.hint.css({position:"absolute",zIndex:2e4,left:r.left,top:r.top}).appendTo(y.body),i.angular("compile",function(){i.hint.removeAttr("ng-repeat");for(var e=t(n.target);!e.data("$$kendoScope")&&e.length;)e=e.parent();return{elements:i.hint.get(),scopeFrom:e.data("$$kendoScope")}})),O[a.group]=i,i.dropped=!1,s&&(i.boundaries=o(s,i.hint)),t(y).on(W,i._captureEscape),i._trigger(P,n)&&(i.userEvents.cancel(),i._afterEnd()),i.userEvents.capture(),e)},_hold:function(t){this.currentTarget=t.target,this.options.holdToDrag&&this._trigger(U,t)?this.userEvents.cancel():this._activated=!0},_drag:function(e){var n,r;e.preventDefault(),n=this._elementUnderCursor(e),this.options.autoScroll&&this._cursorElement!==n&&(this._scrollableParent=u(n),this._cursorElement=n),this._lastEvent=e,this._processMovement(e,n),this.options.autoScroll&&this._scrollableParent[0]&&(r=h(e.x.location,e.y.location,l(this._scrollableParent)),this._scrollCompenstation=t.extend({},this.hintOffset),this._scrollVelocity=r,0===r.y&&0===r.x?(clearInterval(this._scrollInterval),this._scrollInterval=null):this._scrollInterval||(this._scrollInterval=setInterval(t.proxy(this,"_autoScroll"),50))),this.hint&&this._updateHint(e)},_processMovement:function(n,r){this._withDropTarget(r,function(r,i){if(!r)return d&&(d._trigger($,C(n,{dropTarget:t(d.targetElement)})),d=null),e;if(d){if(i===d.targetElement)return;d._trigger($,C(n,{dropTarget:t(d.targetElement)}))}r._trigger(B,C(n,{dropTarget:t(i)})),d=C(r,{targetElement:i})}),this._trigger(L,C(n,{dropTarget:d,elementUnderCursor:r}))},_autoScroll:function(){var t,e,n,r,i,o,a,s,l=this._scrollableParent[0],u=this._scrollVelocity,h=this._scrollCompenstation;l&&(t=this._elementUnderCursor(this._lastEvent),this._processMovement(this._lastEvent,t),r=l===c()[0],r?(e=y.body.scrollHeight>b.height(),n=y.body.scrollWidth>b.width()):(e=l.offsetHeight<=l.scrollHeight,n=l.offsetWidth<=l.scrollWidth),i=l.scrollTop+u.y,o=e&&i>0&&i<l.scrollHeight,a=l.scrollLeft+u.x,s=n&&a>0&&a<l.scrollWidth,o&&(l.scrollTop+=u.y),s&&(l.scrollLeft+=u.x),this.hint&&r&&(s||o)&&(o&&(h.top+=u.y),s&&(h.left+=u.x),this.hint.css(h)))},_end:function(e){this._withDropTarget(this._elementUnderCursor(e),function(n,r){n&&(n._drop(C({},e,{dropTarget:t(r)})),d=null)}),this._cancel(this._trigger(A,e))},_cancel:function(t){var e=this;e._scrollableParent=null,this._cursorElement=null,clearInterval(this._scrollInterval),e._activated=!1,e.hint&&!e.dropped?setTimeout(function(){e.hint.stop(!0,!0),t?e._afterEndHandler():e.hint.animate(e.currentTargetOffset,"fast",e._afterEndHandler)},0):e._afterEnd()},_trigger:function(t,e){var n=this;return n.trigger(t,C({},e.event,{x:e.x,y:e.y,currentTarget:n.currentTarget,initialTarget:e.touch?e.touch.initialTouch:null,dropTarget:e.dropTarget,elementUnderCursor:e.elementUnderCursor}))},_elementUnderCursor:function(t){var e=H(t),r=this.hint;return r&&n(r[0],e)&&(r.hide(),e=H(t),e||(e=H(t)),r.show()),e},_withDropTarget:function(t,e){var n,r=this.options.group,i=k[r],o=I[r];(i&&i.length||o&&o.length)&&(n=a(t,i,o),n?e(n.target,n.targetElement):e())},destroy:function(){var t=this;T.fn.destroy.call(t),t._afterEnd(),t.userEvents.destroy(),this._scrollableParent=null,this._cursorElement=null,clearInterval(this._scrollInterval),t.currentTarget=null},_afterEnd:function(){var e=this;e.hint&&e.hint.remove(),delete O[e.options.group],e.trigger("destroy"),e.trigger(V),t(y).off(W,e._captureEscape)}}),_.ui.plugin(g),_.ui.plugin(v),_.ui.plugin(m),_.TapCapture=j,_.containerBoundaries=o,C(_.ui,{Pane:J,PaneDimensions:Q,Movable:p}),_.ui.Draggable.utils={autoScrollVelocity:h,scrollableViewPort:l,findScrollableParent:u}}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()});
//# sourceMappingURL=kendo.draganddrop.min.js.map
