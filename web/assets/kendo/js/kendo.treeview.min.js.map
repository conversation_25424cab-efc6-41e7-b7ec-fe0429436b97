{"version": 3, "sources": ["kendo.treeview.js"], "names": ["f", "define", "$", "undefined", "contentChild", "filter", "node", "result", "children", "length", "templateNoWith", "code", "kendo", "template", "useWithBlock", "checkboxes", "find", "insertAction", "indexOffset", "nodeData", "referenceNode", "closest", "NODE", "parentNode", "group", "parent", "is", "this", "_dataSourceMove", "dataSource", "model", "referenceItem", "dataItem", "referenceNodeIndex", "indexOf", "index", "_insert", "data", "moveContents", "container", "tmp", "nodeName", "toLowerCase", "nextS<PERSON>ling", "nodeType", "nodeValue", "trim", "spriteRe", "test", "className", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "updateNodeHtml", "wrapper", "to<PERSON><PERSON><PERSON><PERSON>", "checkbox", "innerWrapper", "hasClass", "prependTo", "remove", "appendTo", "append", "eq", "addClass", "TreeView", "subGroup", "nodeContents", "nodeIcon", "window", "ui", "extend", "isArray", "Widget", "HierarchicalDataSource", "proxy", "keys", "NS", "TEMP_NS", "SELECT", "CHECK", "NAVIGATE", "EXPAND", "CHANGE", "ERROR", "CHECKED", "INDETERMINATE", "COLLAPSE", "DRAGSTART", "DRAG", "DROP", "DRAGEND", "DATABOUND", "CLICK", "UNDEFINED", "KSTATEHOVER", "KTREEVIEW", "VISIBLE", "STRING", "ARIACHECKED", "ARIASELECTED", "ARIADISABLED", "DISABLED", "bindings", "text", "url", "spriteCssClass", "imageUrl", "isJQueryInstance", "obj", "j<PERSON><PERSON><PERSON>", "isDomElement", "o", "HTMLElement", "DataBoundWidget", "init", "element", "options", "list", "that", "inferred", "hasDataSource", "loadOnDemand", "prototype", "call", "_dataSourceUids", "_animation", "_accessors", "_templates", "root", "_wrapper", "_group", "_tabindex", "attr", "_dataSource", "_attachEvents", "_dragging", "_syncHtmlAndDataSource", "autoBind", "_progress", "fetch", "check<PERSON><PERSON><PERSON><PERSON>", "updateIndeterminate", "id", "_ariaId", "format", "notify", "clickableItems", "MOUSEENTER", "on", "e", "preventDefault", "removeClass", "_click", "_toggleButtonClick", "_keydown", "_keypress", "_focus", "_blur", "_mousedown", "_checkboxChange", "_checkboxLabelClick", "_retryRequest", "target", "focus", "previousSibling", "prop", "trigger", "i", "item", "uid", "itemCheckbox", "view", "uidAttr", "expandedAttr", "checkboxesEnabled", "items", "expanded", "checked", "next", "animationOptions", "animation", "hasCollapseAnimation", "collapse", "expand", "reverse", "effects", "hide", "widget", "enabled", "dragAndDrop", "dragging", "HierarchicalDragAndDrop", "reorderable", "$angular", "autoScroll", "allowedContainers", "itemSelector", "hintText", "_hintText", "contains", "source", "destination", "dropHintContainer", "itemFromTarget", "content", "first", "last", "dropPositionFrom", "dropHint", "prevAll", "dragstart", "sourceNode", "drag", "originalEvent", "drop<PERSON>ar<PERSON>", "pageY", "pageX", "statusClass", "status", "setStatusClass", "setStatus", "drop", "navigationTarget", "_tempPreventNavigation", "destinationNode", "valid", "<PERSON><PERSON><PERSON><PERSON>", "state", "dropPosition", "position", "dragend", "triggerDragEnd", "insertAfter", "destroy", "ev", "off", "templates", "dragClue", "treeview", "fieldAccessor", "_fieldAccessor", "_checkboxes", "setAttributes", "attributes", "hasOwnProperty", "wrapperCssClass", "firstLevel", "cssClass", "groupLength", "textClass", "isLink", "selected", "toggleButtonClass", "groupAttributes", "groupCssClass", "itemContent", "itemElement", "loading", "retry", "setDataSource", "one", "_bindDataSource", "_refresh<PERSON><PERSON><PERSON>", "refresh", "_error<PERSON><PERSON><PERSON>", "_error", "bind", "_unbindDataSource", "unbind", "silentRead", "recursiveRead", "_initC<PERSON><PERSON>n", "fields", "field", "create", "events", "name", "duration", "messages", "requestFailed", "dataTextField", "textField", "fieldName", "fieldB<PERSON>ings", "count", "map", "x", "expr", "join", "setOptions", "fn", "_trigger", "eventName", "_setChecked", "datasource", "value", "isFunction", "nodes", "_setCheckedValue", "_setIndeterminate", "siblings", "all", "indeterminate", "subnodes", "_bubbleIndeterminate", "skipDownward", "_skip", "set", "isChecked", "_preventChange", "currentTarget", "toggle", "browser", "support", "msie", "edge", "_clickTarget", "current", "_focusable", "select", "clickTarget", "touch", "_nextVisible", "scrollContainer", "containers", "offsets", "documentElement", "document", "scrollHeight", "clientHeight", "push", "scrollTop", "focusElement", "_enabled", "skipSelf", "wrapperRe", "itemRe", "nextParent", "_expanded", "_previousVisible", "<PERSON><PERSON><PERSON><PERSON>", "prev", "key", "keyCode", "focused", "rtl", "isRtl", "RIGHT", "LEFT", "DOWN", "UP", "HOME", "END", "ENTER", "SPACEBAR", "matchToFocus", "delay", "focusedNode", "get", "isPrintable", "_match", "clearTimeout", "_matchTimer", "setTimeout", "_matchNextByText", "Array", "startIndex", "textNodes", "shouldNavigate", "contents", "href", "wrapperClasses", "wrap", "_getSelectedNode", "groupElement", "css", "_nodes", "groupData", "each", "_updateNodeClasses", "defaultTemplate", "textWrap", "_processNodes", "callback", "getByUid", "_dataItem", "_insertNode", "insertCallback", "collapsed", "childrenData", "updatedGroupLength", "nodeHtml", "_renderItem", "angular", "elements", "_renderGroup", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_updateNodes", "setCheckedState", "nodeWrapper", "isCollapsed", "context", "render", "findByUid", "toggleClass", "end", "html", "_toggle", "removeAttr", "ns", "_appendItems", "dataItems", "viewItems", "rootItems", "_refresh<PERSON><PERSON><PERSON>n", "child", "empty", "_refreshRoot", "groupHtml", "_angularItems", "bubble", "action", "level", "_remove", "_loaded", "load", "retryHtml", "loaded", "enable", "arguments", "_current", "direction", "height", "kendoStop", "kendoAnimate", "reset", "complete", "force", "showProgress", "loadingText", "Math", "min", "_objectOrSelf", "referenceDataItem", "destTreeview", "destDataSource", "loadPromise", "Deferred", "resolve", "promise", "_toObservableData", "ObservableArray", "toJSON", "splice", "apply", "concat", "success", "loadModel", "add", "max", "inserted", "done", "noop", "keepData", "prevSibling", "detach", "findByText", "getAttribute", "expandPath", "path", "proceed", "nodeIds", "shift", "then", "slice", "_parentIds", "parents", "unshift", "expandTo", "Node", "r", "renderItems", "len", "plugin", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,kBACH,aACA,8BACDD,IACL,WA0lDE,MA5kDC,UAAUE,EAAGC,GAWV,QAASC,GAAaC,GAClB,MAAO,UAAUC,GACb,GAAIC,GAASD,EAAKE,SAAS,yBAI3B,OAHKD,GAAOE,SACRF,EAASD,GAENC,EAAOC,SAASH,IAG/B,QAASK,GAAeC,GACpB,MAAOC,GAAMC,SAASF,GAAQG,cAAc,IAOhD,QAASC,GAAWT,GAChB,MAAOA,GAAKU,KAAK,kDAErB,QAASC,GAAaC,GAClB,MAAO,UAAUC,EAAUC,GACvBA,EAAgBA,EAAcC,QAAQC,EACtC,IAAoCC,GAAhCC,EAAQJ,EAAcK,QAI1B,OAHID,GAAMC,SAASC,GAAG,QAClBH,EAAaC,EAAMC,UAEhBE,KAAKC,gBAAgBT,EAAUK,EAAOD,EAAY,SAAUM,EAAYC,GAAtB,GACjDC,GAAgBJ,KAAKK,SAASZ,GAC9Ba,EAAqBF,EAAgBA,EAAcN,SAASS,QAAQH,GAAiBX,EAAce,OACvG,OAAOR,MAAKS,QAAQP,EAAWQ,OAAQP,EAAOG,EAAqBf,MAK/E,QAASoB,GAAahC,EAAMiC,GAExB,IADA,GAAIC,GACGlC,GAAuC,MAA/BA,EAAKmC,SAASC,eACzBF,EAAMlC,EACNA,EAAOA,EAAKqC,YACQ,GAAhBH,EAAII,WACJJ,EAAIK,UAAY3C,EAAE4C,KAAKN,EAAIK,YAE3BE,EAASC,KAAKR,EAAIS,WAClBV,EAAUW,aAAaV,EAAKD,EAAUY,YAEtCZ,EAAUa,YAAYZ,GAIlC,QAASa,GAAe/C,GACpB,GAAIgD,GAAUhD,EAAKE,SAAS,OAAQgB,EAAQlB,EAAKE,SAAS,MAAO+C,EAAeD,EAAQ9C,SAAS,WAAYgD,EAAWlD,EAAKE,SAAS,wBAAyBiD,EAAeH,EAAQ9C,SAAS,QAC3LF,GAAKoD,SAAS,gBAGbJ,EAAQ7C,SACT6C,EAAUpD,EAAE,WAAWyD,UAAUrD,KAEhCiD,EAAa9C,QAAUe,EAAMf,OAC9B8C,EAAerD,EAAE,2BAA6ByD,UAAUL,GAChD9B,EAAMf,QAAWe,EAAMhB,WAAWC,SAC1C8C,EAAaK,SACbpC,EAAMoC,UAENJ,EAAS/C,QACTP,EAAE,uCAAyC2D,SAASP,GAASQ,OAAON,GAEnEC,EAAahD,SACdgD,EAAenD,EAAKE,SAAS,KAAKuD,GAAG,GAAGC,SAAS,eAC5CP,EAAahD,SACdgD,EAAevD,EAAE,0BAErBuD,EAAaI,SAASP,GAClBA,EAAQ7C,QACR6B,EAAagB,EAAQ,GAAGX,YAAac,EAAa,MApF9D,GAAm2BQ,GAAUC,EAAUC,EAAcC,EAAUrB,EAA34BnC,EAAQyD,OAAOzD,MAAO0D,EAAK1D,EAAM0D,GAAIjC,EAAOzB,EAAMyB,KAAMkC,EAASrE,EAAEqE,OAAQ1D,EAAWD,EAAMC,SAAU2D,EAAUtE,EAAEsE,QAASC,EAASH,EAAGG,OAAQC,EAAyBrC,EAAKqC,uBAAwBC,EAAQzE,EAAEyE,MAAOC,EAAOhE,EAAMgE,KAAMC,EAAK,iBAAkBC,EAAU,qBAAsBC,EAAS,SAAUC,EAAQ,QAASC,EAAW,WAAYC,EAAS,SAAUC,EAAS,SAAUC,EAAQ,QAASC,EAAU,UAAWC,EAAgB,gBAAiBC,EAAW,WAAYC,EAAY,YAAaC,EAAO,OAAQC,EAAO,OAAQC,EAAU,UAAWC,EAAY,YAAaC,EAAQ,QAASC,EAAY,YAAaC,EAAc,gBAAiBC,EAAY,aAAcC,EAAU,WAAY3E,EAAO,UAAW4E,EAAS,SAAUC,EAAc,eAAgBC,EAAe,gBAAiBC,EAAe,gBAAiBC,EAAW,mBAA0EC,GACj5BC,KAAM,gBACNC,IAAK,eACLC,eAAgB,0BAChBC,SAAU,qBACXC,EAAmB,SAAUC,GAC5B,MAAOA,aAAejG,GAAMkG,QAAUzC,OAAOyC,QAAUD,YAAexC,QAAOyC,QAC9EC,EAAe,SAAUC,GACxB,MAA8B,gBAAhBC,aAA2BD,YAAaC,aAAcD,GAAkB,gBAANA,IAAiC,IAAfA,EAAEpE,gBAAyBoE,GAAEvE,WAAayD,EAcpJhC,GAAW9D,EAAa,YACxB+D,EAAe/D,EAAa,uBAC5BgE,EAAW,SAAU9D,GACjB,MAAOA,GAAKE,SAAS,OAAOA,SAAS,YAmBzCuC,EAAW,WA4CXkB,EAAWrD,EAAM0D,GAAG4C,gBAAgB3C,QAChC4C,KAAM,SAAUC,EAASC,GACrB,GAAoFC,GAAhFC,EAAO5F,KAAM6F,GAAW,EAAOC,EAAgBJ,KAAaA,EAAQxF,UACpE2C,GAAQ6C,KACRA,GAAYxF,WAAYwF,IAExBA,SAAkBA,GAAQK,cAAgB5B,GAAatB,EAAQ6C,EAAQxF,cACvEwF,EAAQK,cAAe,GAE3BjD,EAAOkD,UAAUR,KAAKS,KAAKL,EAAMH,EAASC,GAC1CD,EAAUG,EAAKH,QACfC,EAAUE,EAAKF,QACfE,EAAKM,mBACLP,EAAOF,EAAQ1F,GAAG,OAAS0F,GAAWA,EAAQ1D,SAASsC,IAAcoB,EAAQ5G,SAAS,MACtFgH,GAAYC,GAAiBH,EAAK7G,OAC9B+G,IACAH,EAAQxF,WAAWyF,KAAOA,GAE9BC,EAAKO,aACLP,EAAKQ,aACLR,EAAKS,aACAZ,EAAQ1D,SAASsC,IAOlBuB,EAAKjE,QAAU8D,EACfG,EAAKU,KAAOb,EAAQ5G,SAAS,MAAMuD,GAAG,KAPtCwD,EAAKW,WACDZ,IACAC,EAAKU,KAAOb,EACZG,EAAKY,OAAOZ,EAAKjE,WAMzBiE,EAAKa,YACLb,EAAKjE,QAAQ+E,KAAK,OAAQ,QAC1Bd,EAAKe,YAAYd,GACjBD,EAAKgB,gBACLhB,EAAKiB,YACAhB,EAMDD,EAAKkB,yBALDpB,EAAQqB,WACRnB,EAAKoB,WAAU,GACfpB,EAAK1F,WAAW+G,SAKpBvB,EAAQtG,YAAcsG,EAAQtG,WAAW8H,eACzCtB,EAAKuB,sBAELvB,EAAKH,QAAQ,GAAG2B,KAChBxB,EAAKyB,QAAUpI,EAAMqI,OAAO,gBAAiB1B,EAAKH,QAAQ,GAAG2B,KAEjEnI,EAAMsI,OAAO3B,IAEjBgB,cAAe,WACX,GAAIhB,GAAO5F,KAAMwH,EAAiB,iDAAkDC,EAAa,YACjG7B,GAAKjE,QAAQ+F,GAAGD,EAAavE,EAAI,yBAA0B,SAAUyE,GACjEA,EAAEC,mBACHF,GAAGD,EAAavE,EAAIsE,EAAgB,WACnCjJ,EAAEyB,MAAMqC,SAAS+B,KAClBsD,GAAG,aAAexE,EAAIsE,EAAgB,WACrCjJ,EAAEyB,MAAM6H,YAAYzD,KACrBsD,GAAGxD,EAAQhB,EAAIsE,EAAgBxE,EAAM4C,EAAKkC,OAAQlC,IAAO8B,GAAG,WAAaxE,EAAI,+BAAgCF,EAAM4C,EAAKmC,mBAAoBnC,IAAO8B,GAAGxD,EAAQhB,EAAI,4BAA6BF,EAAM4C,EAAKmC,mBAAoBnC,IAAO8B,GAAG,UAAYxE,EAAIF,EAAM4C,EAAKoC,SAAUpC,IAAO8B,GAAG,WAAaxE,EAAIF,EAAM4C,EAAKqC,UAAWrC,IAAO8B,GAAG,QAAUxE,EAAIF,EAAM4C,EAAKsC,OAAQtC,IAAO8B,GAAG,OAASxE,EAAIF,EAAM4C,EAAKuC,MAAOvC,IAAO8B,GAAG,YAAcxE,EAAI,gEAAiEF,EAAM4C,EAAKwC,WAAYxC,IAAO8B,GAAG,SAAWxE,EAAI,gCAAiCF,EAAM4C,EAAKyC,gBAAiBzC,IAAO8B,GAAG,QAAUxE,EAAI,iBAAkBF,EAAM4C,EAAK0C,oBAAqB1C,IAAO8B,GAAG,QAAUxE,EAAI,mBAAoBF,EAAM4C,EAAK2C,cAAe3C,IAAO8B,GAAG,QAAUxE,EAAI,2BAA4B,SAAUyE,GACzyBA,EAAEC,mBACHF,GAAG,QAAUxE,EAAI,SAAUyE,GAC1B,GAAIa,GAASjK,EAAEoJ,EAAEa,OACZA,GAAOzI,GAAG,oBAAuByI,EAAOnJ,KAAK,uCAAuCU,GAAG,oBACxF6F,EAAK6C,WAIjBH,oBAAqB,SAAUX,GAC3B,GAAI9F,GAAWtD,EAAEoJ,EAAEa,OAAOE,gBACtB7G,GAAS9B,GAAG,gBAGhB8B,EAAS8G,KAAK,WAAY9G,EAAS8G,KAAK,YACxC9G,EAAS+G,QAAQ,YAErB9B,uBAAwB,SAAUR,EAAMpG,GACpCoG,EAAOA,GAAQtG,KAAKsG,KACpBpG,EAAaA,GAAcF,KAAKE,UAChC,IAA4K2I,GAAGC,EAAMzI,EAAU0I,EAAKC,EAAhMtI,EAAOR,EAAW+I,OAAQC,EAAUjK,EAAMyH,KAAK,OAAQyC,EAAelK,EAAMyH,KAAK,YAAa0C,EAAoBpJ,KAAK0F,QAAQtG,WAAYiK,EAAQ/C,EAAKzH,SAAS,KACrK,KAAKgK,EAAI,EAAGA,EAAIQ,EAAMvK,OAAQ+J,IAC1BxI,EAAWK,EAAKmI,GAChBE,EAAM1I,EAAS0I,IACfD,EAAOO,EAAMjH,GAAGyG,GAChBC,EAAKpC,KAAK,OAAQ,YAAYA,KAAKwC,EAASH,GAAKrC,KAAKjC,EAAcqE,EAAK/G,SAAS,qBAClF1B,EAASiJ,SAAuC,SAA5BR,EAAKpC,KAAKyC,GAC1BC,IACAJ,EAAe5J,EAAW0J,GAC1BzI,EAASkJ,QAAUP,EAAaL,KAAKjF,GACrCsF,EAAatC,KAAK,KAAM,IAAMqC,GAC9BC,EAAaQ,KAAK,qBAAqB9C,KAAK,MAAO,IAAMqC,IAE7D/I,KAAK8G,uBAAuBgC,EAAKjK,SAAS,MAAOwB,EAASxB,WAGlEsH,WAAY,WACR,GAAIT,GAAU1F,KAAK0F,QAAS+D,EAAmB/D,EAAQgE,UAAWC,EAAuBF,EAAiBG,UAAY,WAAaH,GAAiBG,SAAUA,EAAWhH,KAAW6G,EAAiBI,OAAQJ,EAAiBG,SACzND,KACDC,EAAWhH,EAAOgH,GAAYE,SAAS,KAEvCL,KAAqB,IACrBA,GACII,QAAUE,YACVH,UACII,MAAM,EACND,cAIZN,EAAiBG,SAAWhH,EAAOgH,GAAYI,MAAM,IACrDtE,EAAQgE,UAAYD,GAExB5C,UAAW,WAAA,GAICoD,GAHJC,EAAUlK,KAAK0F,QAAQyE,YACvBC,EAAWpK,KAAKoK,QAChBF,KAAYE,GACRH,EAASjK,KACbA,KAAKoK,SAAW,GAAIzH,GAAG0H,wBAAwBrK,KAAKyF,SAChD6E,aAAa,EACbC,SAAUvK,KAAK0F,QAAQ6E,SACvBC,WAAYxK,KAAK0F,QAAQ8E,WACzB9L,OAAQ,mCACR+L,kBAAmB,cACnBC,aAAc,sBACdC,SAAU3H,EAAMhD,KAAK4K,UAAW5K,MAChC6K,SAAU,SAAUC,EAAQC,GACxB,MAAOxM,GAAEsM,SAASC,EAAQC,IAE9BC,kBAAmB,SAAUlC,GACzB,MAAOA,IAEXmC,eAAgB,SAAUzC,GACtB,GAAIM,GAAON,EAAO9I,QAAQ,uBAC1B,QACIoJ,KAAMA,EACNoC,QAAS1C,EAAO9I,QAAQ,SACxByL,MAAOrC,EAAK/G,SAAS,SACrBqJ,KAAMtC,EAAK/G,SAAS,WAG5BsJ,iBAAkB,SAAUC,GACxB,MAAOA,GAASC,QAAQ,SAASzM,OAAS,EAAI,QAAU,UAE5D0M,UAAW,SAAUV,GACjB,MAAOb,GAAOrB,QAAQ/E,GAAa4H,WAAYX,EAAO,MAE1DY,KAAM,SAAUhG,GACZuE,EAAOrB,QAAQ9E,GACX6H,cAAejG,EAAQiG,cACvBF,WAAY/F,EAAQoF,OAAO,GAC3Bc,WAAYlG,EAAQ8C,OAAO,GAC3BqD,MAAOnG,EAAQmG,MACfC,MAAOpG,EAAQoG,MACfC,YAAarG,EAAQsG,OACrBC,eAAgBvG,EAAQwG,aAGhCC,KAAM,SAAUzG,GAAV,GACEkG,GAAarN,EAAEmH,EAAQkG,YACvBQ,EAAmBR,EAAWlM,QAAQ,IAI1C,OAHI0M,IAAoBA,EAAiB1F,KAAK,SAC1CuD,EAAOoC,uBAAuBD,GAE3BnC,EAAOrB,QAAQ7E,GAClB4H,cAAejG,EAAQiG,cACvBF,WAAY/F,EAAQoF,OACpBwB,gBAAiB5G,EAAQqF,YACzBwB,MAAO7G,EAAQ6G,MACfC,SAAU,SAAUC,GAChBzM,KAAKuM,MAAQE,EACb/G,EAAQ8G,SAASC,IAErBb,WAAYlG,EAAQkG,WACpBc,aAAchH,EAAQiH,YAG9BC,QAAS,SAAUlH,GAIf,QAASmH,GAAe/B,GAChBb,EAAOvE,QAAQtG,YAAc6K,EAAOvE,QAAQtG,WAAW8H,eACvD+C,EAAO9C,sBAEX8C,EAAOrB,QAAQ5E,GACX2H,cAAejG,EAAQiG,cACvBF,WAAYX,GAAUA,EAAO,GAC7BwB,gBAAiBvB,EAAY,GAC7B2B,aAAcC,IAZjB,GACD7B,GAASpF,EAAQoF,OACjBC,EAAcrF,EAAQqF,YACtB4B,EAAWjH,EAAQiH,QAYP,SAAZA,EACA1C,EAAO9H,OAAO2I,EAAQC,EAAa8B,IAEnB,UAAZF,EACA7B,EAASb,EAAO1I,aAAauJ,EAAQC,GAClB,SAAZ4B,IACP7B,EAASb,EAAO6C,YAAYhC,EAAQC,IAExC8B,EAAe/B,SAInBZ,GAAWE,IACnBA,EAAS2C,UACT/M,KAAKoK,SAAW,OAGxBiC,uBAAwB,SAAU1N,GAC9BA,EAAK+I,GAAGxD,EAAQhB,EAAKC,EAAS,SAAU6J,GACpCA,EAAGpF,iBACHjJ,EAAKsO,IAAI/I,EAAQhB,EAAKC,MAG9ByH,UAAW,SAAUjM,GACjB,MAAOqB,MAAKkN,UAAUC,UAClBrE,KAAM9I,KAAKK,SAAS1B,GACpByO,SAAUpN,KAAK0F,WAGvBW,WAAY,WACR,GAAIT,GAAO5F,KAAM0F,EAAUE,EAAKF,QAAS2H,EAAgBrK,EAAM4C,EAAK0H,eAAgB1H,EAChFF,GAAQxG,gBAAmBwG,GAAQxG,UAAYqF,EAC/CmB,EAAQxG,SAAWA,EAASwG,EAAQxG,UAC5BwG,EAAQxG,WAChBwG,EAAQxG,SAAWH,EAAe,gBAAkBsO,EAAc,QAAU,sIAEhFzH,EAAK2H,cACL3H,EAAKsH,WACDM,cAAe,SAAU1E,GAAV,GAGFpC,GAFL9H,EAAS,GACT6O,EAAa3E,EAAKpC,QACtB,KAASA,IAAQ+G,GACTA,EAAWC,eAAehH,IAAkB,UAATA,IACnC9H,GAAU8H,EAAO,KAAO+G,EAAW/G,GAAQ,KAGnD,OAAO9H,IAEX+O,gBAAiB,SAAU9N,EAAOiJ,GAC9B,GAAIlK,GAAS,SAAU4B,EAAQsI,EAAKtI,KAOpC,OANIX,GAAM+N,YAAwB,IAAVpN,IACpB5B,GAAU,YAEV4B,GAASX,EAAMf,OAAS,IACxBF,GAAU,WAEPA,GAEXiP,SAAU,SAAUhO,EAAOiJ,GACvB,GAAIlK,GAAS,GAAI4B,EAAQsI,EAAKtI,MAAOsN,EAAcjO,EAAMf,OAAS,CAWlE,OAVIe,GAAM+N,YAAwB,IAAVpN,IACpB5B,GAAU,UAGVA,GADU,IAAV4B,GAAeA,GAASsN,EACd,QACHtN,GAASsN,EACN,QAEA,SAIlBC,UAAW,SAAUjF,EAAMkF,GACvB,GAAIpP,GAAS,MAUb,OATIoP,KACApP,GAAU,WAEVkK,EAAKoB,WAAY,IACjBtL,GAAU,qBAEVkK,EAAKmF,YAAa,IAClBrP,GAAU,qBAEPA,GAEXsP,kBAAmB,SAAUpF,GACzB,GAAIlK,GAAS,QAMb,OAJIA,IADAkK,EAAKQ,YAAa,EACR,cAEA,iBAIlB6E,gBAAiB,SAAUtO,GACvB,GAAI4N,GAAa,EAIjB,OAHK5N,GAAM+N,aACPH,EAAa,gBAEVA,GAAc5N,EAAMyJ,YAAa,EAAO,wBAA4B,KAE/E8E,cAAe,SAAUvO,GACrB,GAAIgO,GAAW,SAIf,OAHIhO,GAAM+N,aACNC,GAAY,qBAETA,GAEXV,SAAUpO,EAAe,qCACzBc,MAAOd,EAAe,6HACtBsP,YAAatP,EAAe,oBAAsBsO,EAAc,YAAc,wCAA+CA,EAAc,kBAAoB,sMAC/JiB,YAAavP,EAAe,oDAA2DsO,EAAc,OAAS,qdAC9GvE,KAAM/J,EAAe,4GAAuHE,EAAMyH,KAAK,OAAS,+aAChK6H,QAASxP,EAAe,iEACxByP,MAAOzP,EAAe,iHAG9BsK,MAAO,WACH,MAAOrJ,MAAKyF,QAAQpG,KAAK,8BAE7BoP,cAAe,SAAUvO,GACrB,GAAIwF,GAAU1F,KAAK0F,OACnBA,GAAQxF,WAAaA,EACrBF,KAAKkG,mBACLlG,KAAK2G,cACDjB,EAAQtG,YAAcsG,EAAQtG,WAAW8H,eACzClH,KAAKE,WAAWwO,IAAI,SAAUnQ,EAAEyE,MAAMhD,KAAKmH,oBAAqBnH,KAAM,OAEtEA,KAAK0F,QAAQqB,UACb/G,KAAKE,WAAW+G,SAGxB0H,gBAAiB,WACb3O,KAAK4O,gBAAkB5L,EAAMhD,KAAK6O,QAAS7O,MAC3CA,KAAK8O,cAAgB9L,EAAMhD,KAAK+O,OAAQ/O,MACxCA,KAAKE,WAAW8O,KAAKxL,EAAQxD,KAAK4O,iBAClC5O,KAAKE,WAAW8O,KAAKvL,EAAOzD,KAAK8O,gBAErCG,kBAAmB,WACf,GAAI/O,GAAaF,KAAKE,UAClBA,KACAA,EAAWgP,OAAO1L,EAAQxD,KAAK4O,iBAC/B1O,EAAWgP,OAAOzL,EAAOzD,KAAK8O,iBAGtCnI,YAAa,SAAUwI,GAEnB,QAASC,GAAc1O,GACnB,IAAK,GAAImI,GAAI,EAAGA,EAAInI,EAAK5B,OAAQ+J,IAC7BnI,EAAKmI,GAAGwG,gBACR3O,EAAKmI,GAAGhK,SAASoI,QACjBmI,EAAc1O,EAAKmI,GAAGhK,SAASoK,QALvC,GAAIrD,GAAO5F,KAAM0F,EAAUE,EAAKF,QAASxF,EAAawF,EAAQxF,UAQ9DA,GAAa2C,EAAQ3C,IAAgBQ,KAAMR,GAAeA,EAC1D0F,EAAKqJ,oBACA/O,EAAWoP,SACZpP,EAAWoP,SACLC,MAAO,SACPA,MAAO,QACPA,MAAO,mBACPA,MAAO,cAGjB3J,EAAK1F,WAAaA,EAAa6C,EAAuByM,OAAOtP,GACzDiP,IACAjP,EAAW+G,QACXmI,EAAclP,EAAW+I,SAE7BrD,EAAK+I,mBAETc,QACI5L,EACAC,EACAC,EACAC,EACAC,EACAV,EACAK,EACAR,EACAI,EACAF,EACAD,GAEJqC,SACIgK,KAAM,WACNxP,cACAwJ,WACIG,QACIE,QAAS,kBACT4F,SAAU,KAEd/F,UAAY+F,SAAU,MAE1BC,UACIrB,QAAS,aACTsB,cAAe,kBACfrB,MAAO,SAEXrE,aAAa,EACb/K,YAAY,EACZ2H,UAAU,EACVyD,YAAY,EACZzE,cAAc,EACd7G,SAAU,GACV4Q,cAAe,MAEnB1J,WAAY,WACR,GAAyCyC,GAAG0G,EAAOQ,EAA/CnK,EAAO5F,KAAM0F,EAAUE,EAAKF,QAA8BD,EAAUG,EAAKH,OAC7E,KAAKoD,IAAKjE,GACN2K,EAAQ7J,EAAQd,EAASiE,IACzBkH,EAAYtK,EAAQiB,KAAKzH,EAAMyH,KAAKmC,EAAI,YACnC0G,GAASQ,IACVR,EAAQQ,GAEPR,IACDA,EAAQ1G,GAEPhG,EAAQ0M,KACTA,GAASA,IAEb7J,EAAQd,EAASiE,IAAM0G,GAG/BjC,eAAgB,SAAU0C,GACtB,GAAIC,GAAgBjQ,KAAK0F,QAAQd,EAASoL,IAAaE,EAAQD,EAAcnR,OAAQF,EAAS,mBAU9F,OATc,KAAVsR,EACAtR,GAAU,gBAAmBoR,EAAY,OAEzCpR,GAAU,iBAAmBL,EAAE4R,IAAIF,EAAe,SAAUG,GACxD,MAAO,uBAAyBnR,EAAMoR,KAAKD,GAAK,MACjDE,KAAK,KAAO,KACf1R,GAAU,wCAA0CsR,EAAQ,cAEhEtR,GAAU,MAGd2R,WAAY,SAAU7K,GAClB5C,EAAO0N,GAAGD,WAAWtK,KAAKjG,KAAM0F,GAChC1F,KAAKmG,aACLnG,KAAK6G,YACL7G,KAAKqG,cAEToK,SAAU,SAAUC,EAAW/R,GAC3B,MAAOqB,MAAK4I,QAAQ8H,GAAa/R,KAAMA,EAAKe,QAAQC,GAAM,MAE9DgR,YAAa,SAAUC,EAAYC,GAC/B,GAAKD,GAAerS,EAAEuS,WAAWF,EAAW3H,MAG5C,IAAK,GAAIJ,GAAI,EAAGkI,EAAQH,EAAW3H,OAAQJ,EAAIkI,EAAMjS,OAAQ+J,IACrDkI,EAAMlI,GAAGqB,WAAY,GACrBlK,KAAKgR,iBAAiBD,EAAMlI,GAAIgI,GAEhCE,EAAMlI,GAAGhK,UACTmB,KAAK2Q,YAAYI,EAAMlI,GAAGhK,SAAUgS,IAIhDG,iBAAkB,SAAUrS,EAAMkS,GAC9BlS,EAAK+E,GAAWmN,GAEpBI,kBAAmB,SAAUtS,GACzB,GAA4BuS,GAAUpS,EAAoB+J,EAAtDhJ,EAAQ0C,EAAS5D,GAAyBwS,GAAM,CACpD,IAAKtR,EAAMf,SAGXoS,EAAW9R,EAAWS,EAAMhB,YAC5BC,EAASoS,EAASpS,QAClB,CAEO,GAAIA,EAAS,GAChB,IAAK+J,EAAI,EAAGA,EAAI/J,EAAQ+J,IACpB,GAAIqI,EAASrI,GAAGU,SAAW2H,EAASrI,EAAI,GAAGU,SAAW2H,EAASrI,GAAGuI,eAAiBF,EAASrI,EAAI,GAAGuI,cAAe,CAC9GD,GAAM,CACN,YAIRA,IAAOD,EAAS,GAAGE,aAGvB,OADAzS,GAAK+H,KAAKlC,EAAa2M,EAAMD,EAAS,GAAG3H,QAAU,SAC5CnK,EAAWT,GAAM+B,KAAKiD,GAAgBwN,GAAKxI,KAAKhF,GAAgBwN,GAAKxI,KAAKjF,EAASyN,GAAOD,EAAS,GAAG3H,WAEjHpC,oBAAqB,SAAUxI,GAAV,GAEb0S,GACAxI,EACAhH,EACAxB,CACJ,IALA1B,EAAOA,GAAQqB,KAAK2B,QAChB0P,EAAW9O,EAAS5D,GAAME,WAI1BwS,EAASvS,OAAQ,CACjB,IAAK+J,EAAI,EAAGA,EAAIwI,EAASvS,OAAQ+J,IAC7B7I,KAAKmH,oBAAoBkK,EAASjP,GAAGyG,GAEzC,IAAIlK,EAAKoB,GAAG,eACR,MAEJ8B,GAAW7B,KAAKiR,kBAAkBtS,GAClC0B,EAAWL,KAAKK,SAAS1B,GACrBkD,GAAYA,EAAS8G,KAAKjF,GAC1BrD,EAASkJ,SAAU,EAEflJ,SACOA,GAASkJ,UAKhC+H,qBAAsB,SAAU3S,EAAM4S,GAClC,GAAK5S,EAAKG,OAAV,CAGKyS,GACDvR,KAAKmH,oBAAoBxI,EAE7B,IAAoCkD,GAAhCjC,EAAaI,KAAKF,OAAOnB,EACzBiB,GAAWd,SACXkB,KAAKiR,kBAAkBrR,GACvBiC,EAAWjC,EAAWf,SAAS,OAAOQ,KAAK,4CAC3CW,KAAKwR,OAAQ,EACT3P,EAAS8G,KAAKhF,MAAmB,EACjC3D,KAAKK,SAAST,GAAY6R,IAAI/N,EAAS7B,EAAS8G,KAAKjF,IAErD1D,KAAKK,SAAST,GAAY6R,IAAI/N,GAAS,GAE3C1D,KAAKwR,OAAQ,EACbxR,KAAKsR,qBAAqB1R,GAAY,MAG9CyI,gBAAiB,SAAUV,GAAV,GACT9F,GAAWtD,EAAEoJ,EAAEa,QACfkJ,EAAY7P,EAAS8G,KAAKjF,GAC1B/E,EAAOkD,EAASnC,QAAQC,GACxBU,EAAWL,KAAKK,SAAS1B,EACzBqB,MAAK2R,gBAGLtR,EAASkJ,SAAWmI,IACpBrR,EAASoR,IAAI/N,EAASgO,GACtB/S,EAAK+H,KAAKlC,EAAakN,GACvB1R,KAAKyQ,SAASpN,EAAO1E,KAG7BoJ,mBAAoB,SAAUJ,GAC1B,GAAIhJ,GAAOJ,EAAEoJ,EAAEiK,eAAelS,QAAQC,EAClChB,GAAKoB,GAAG,2BAGZC,KAAK6R,OAAOlT,IAEhByJ,WAAY,SAAUT,GAAV,GACJ/B,GAAO5F,KACP4R,EAAgBrT,EAAEoJ,EAAEiK,eACpBjT,EAAOJ,EAAEoJ,EAAEiK,eAAelS,QAAQC,GAClCmS,EAAU7S,EAAM8S,QAAQD,OACxBnT,GAAKoB,GAAG,6BAGP+R,EAAQE,MAAQF,EAAQG,OAASL,EAAc7R,GAAG,eAC/C6R,EAAcjJ,KAAKhF,IACnBiC,EAAK+L,gBAAiB,EACtBC,EAAcjJ,KAAKjF,GAAUkO,EAAcjJ,KAAKjF,IAChDkO,EAAchJ,QAAQpF,GACtBoO,EAAclK,GAAGxD,EAAQhB,EAAI,SAAUyE,GACnCA,EAAEC,mBAENhC,EAAK+L,gBAAiB,IAEtBC,EAAc3E,IAAI/I,EAAQhB,GAC1B0C,EAAK+L,gBAAiB,IAG9B/L,EAAKsM,aAAevT,EACpBiH,EAAKuM,QAAQxT,KAEjByT,WAAY,SAAUzT,GAClB,MAAOA,IAAQA,EAAKG,QAAUH,EAAKoB,GAAG,cAAgBpB,EAAKU,KAAK,eAAe0C,SAAS4C,IAE5FuD,OAAQ,WACJ,GAAIiK,GAAUnS,KAAKqS,SAAUC,EAActS,KAAKkS,YAC5CjT,GAAM8S,QAAQQ,QAGdD,GAAeA,EAAYxT,SAC3BqT,EAAUG,GAETtS,KAAKoS,WAAWD,KACjBA,EAAUnS,KAAKmS,WAEdnS,KAAKoS,WAAWD,KACjBA,EAAUnS,KAAKwS,aAAajU,MAEhCyB,KAAKmS,QAAQA,KAEjB1J,MAAO,WACH,GAAqII,GAAjIlH,EAAU3B,KAAK2B,QAAS8Q,EAAkB9Q,EAAQ,GAAI+Q,KAAiBC,KAAcC,EAAkBC,SAASD,eACpH,GACIH,GAAkBA,EAAgB7S,WAC9B6S,EAAgBK,aAAeL,EAAgBM,eAC/CL,EAAWM,KAAKP,GAChBE,EAAQK,KAAKP,EAAgBQ,kBAE5BR,GAAmBG,EAE5B,KADA3T,EAAMiU,aAAavR,GACdkH,EAAI,EAAGA,EAAI6J,EAAW5T,OAAQ+J,IAC/B6J,EAAW7J,GAAGoK,UAAYN,EAAQ9J,IAG1CV,MAAO,WACHnI,KAAKmS,UAAU9S,KAAK,eAAewI,YAAY,oBAEnDsL,SAAU,SAAUxU,GAChB,OAAQA,EAAKE,SAAS,OAAOA,SAAS,SAASkD,SAAS4C,IAE5D7E,OAAQ,SAAUnB,GACd,GAAyDC,GAAQwU,EAA7DC,EAAY,iBAAkBC,EAAS,mBAChC3U,IAAQ4F,IACf5F,EAAOqB,KAAKyF,QAAQpG,KAAKV,IAExByG,EAAazG,KACdA,EAAOA,EAAK,IAEhByU,EAAWE,EAAOjS,KAAK1C,EAAK2C,UAC5B,GACI3C,GAAOA,EAAKiB,WACR0T,EAAOjS,KAAK1C,EAAK2C,aACb8R,EACAxU,EAASD,EAETyU,GAAW,UAGbC,EAAUhS,KAAK1C,EAAK2C,aAAe1C,EAC7C,OAAOL,GAAEK,IAEb4T,aAAc,SAAU7T,GAEpB,QAAS4U,GAAW5U,GAChB,KAAOA,EAAKG,SAAWH,EAAK6K,OAAO1K,QAC/BH,EAAOiH,EAAK9F,OAAOnB,EAEvB,OAAIA,GAAK6K,OAAO1K,OACLH,EAAK6K,OAEL7K,EARf,GAAkDC,GAA9CgH,EAAO5F,KAAMsJ,EAAW1D,EAAK4N,UAAU7U,EAqB3C,OAVKA,GAAKG,QAAWH,EAAKoB,GAAG,YAElBuJ,GACP1K,EAAS2D,EAAS5D,GAAME,WAAWsM,QAC9BvM,EAAOE,SACRF,EAAS2U,EAAW5U,KAGxBC,EAAS2U,EAAW5U,GAPpBC,EAASgH,EAAKU,KAAKzH,WAAWuD,GAAG,GAS9BxD,GAEX6U,iBAAkB,SAAU9U,GACxB,GAAiB+U,GAAW9U,EAAxBgH,EAAO5F,IACX,KAAKrB,EAAKG,QAAUH,EAAKgV,OAAO7U,OAM5B,IAJIF,EADAD,EAAKG,OACIH,EAAKgV,OAEL/N,EAAKU,KAAKzH,WAAWuM,OAE3BxF,EAAK4N,UAAU5U,KAClB8U,EAAYnR,EAAS3D,GAAQC,WAAWuM,OACnCsI,EAAU5U,SAGfF,EAAS8U,MAGb9U,GAASgH,EAAK9F,OAAOnB,IAASA,CAElC,OAAOC,IAEXoJ,SAAU,SAAUL,GAChB,GAAkCa,GAA9B5C,EAAO5F,KAAM4T,EAAMjM,EAAEkM,QAAiBC,EAAUlO,EAAKuM,UAAW7I,EAAW1D,EAAK4N,UAAUM,GAAUjS,EAAWiS,EAAQzU,KAAK,uCAAwC0U,EAAM9U,EAAM8S,QAAQiC,MAAMpO,EAAKH,QACnMkC,GAAEa,QAAUb,EAAEiK,iBAGbmC,GAAOH,GAAO3Q,EAAKgR,OAASF,GAAOH,GAAO3Q,EAAKiR,KAC5C5K,EACAd,EAAS5C,EAAK4M,aAAasB,GACnBA,EAAQzU,KAAK,eAAe0C,SAAS4C,IAC7CiB,EAAKiE,OAAOiK,IAERC,GAAOH,GAAO3Q,EAAKiR,MAAQH,GAAOH,GAAO3Q,EAAKgR,MAClD3K,IAAawK,EAAQzU,KAAK,eAAe0C,SAAS4C,GAClDiB,EAAKgE,SAASkK,IAEdtL,EAAS5C,EAAK9F,OAAOgU,GAChBlO,EAAKuN,SAAS3K,KACfA,EAAShK,IAGVoV,GAAO3Q,EAAKkR,KACnB3L,EAAS5C,EAAK4M,aAAasB,GACpBF,GAAO3Q,EAAKmR,GACnB5L,EAAS5C,EAAK6N,iBAAiBK,GACxBF,GAAO3Q,EAAKoR,KACnB7L,EAAS5C,EAAK4M,aAAajU,KACpBqV,GAAO3Q,EAAKqR,IACnB9L,EAAS5C,EAAK6N,iBAAiBlV,KACxBqV,GAAO3Q,EAAKsR,OAAUT,EAAQzU,KAAK,eAAe0C,SAAS4C,GAM3DiP,GAAO3Q,EAAKuR,UAAY3S,EAAS/C,SACnCgV,EAAQzU,KAAK,eAAe0C,SAAS4C,KACtC9C,EAAS8G,KAAKjF,GAAU7B,EAAS8G,KAAKjF,IAAUhD,KAAKiD,GAAe,GAAOgF,KAAKhF,GAAe,GAC/FiC,EAAKyC,iBAAkBG,OAAQ3G,KAEnC2G,EAASsL,GAVJA,EAAQzU,KAAK,eAAe0C,SAAS,qBACjC6D,EAAK6K,SAASrN,EAAQ0Q,IACvBlO,EAAKyM,OAAOyB,GAUpBtL,IACAb,EAAEC,iBACEkM,EAAQ,IAAMtL,EAAO,KACrB5C,EAAK6K,SAASnN,EAAUkF,GACxB5C,EAAKuM,QAAQ3J,OAIzBP,UAAW,SAAUN,GAAV,GAIH8M,GAHA7O,EAAO5F,KACP0U,EAAQ,IACRC,EAAc/O,EAAKuM,UAAUyC,IAAI,GAEjChB,EAAMjM,EAAEiM,IACRiB,EAA6B,IAAfjB,EAAI9U,MACjB+V,KAGAjP,EAAKkP,SACNlP,EAAKkP,OAAS,IAElBlP,EAAKkP,QAAUlB,EACfmB,aAAanP,EAAKoP,aAClBpP,EAAKoP,YAAcC,WAAW,WAC1BrP,EAAKkP,OAAS,IACfJ,GACHD,EAAeE,GAAe/O,EAAKsP,iBAAiBC,MAAMnP,UAAUzF,QAAQ0F,KAAKL,EAAKH,QAAQpG,KAAK,WAAYsV,GAAc/O,EAAKkP,QAC7HL,EAAa3V,SACd2V,EAAe7O,EAAKsP,oBAAqBtP,EAAKkP,SAE9CL,EAAaG,IAAI,IAAMH,EAAaG,IAAI,KAAOD,IAC/C/O,EAAK6K,SAASnN,EAAUmR,GACxB7O,EAAKuM,QAAQsC,MAGrBS,iBAAkB,SAAUE,EAAYvQ,GAAtB,GACVY,GAAUzF,KAAKyF,QACf4P,EAAY5P,EAAQpG,KAAK,SAASX,OAAO,SAAUmK,EAAGpD,GACtD,MAAOoD,GAAIuM,GAAc7W,EAAEkH,GAAS1F,GAAG,aAAiE,IAAlDxB,EAAEkH,GAASZ,OAAO9D,cAAcR,QAAQsE,IAElG,OAAOwQ,GAAUjT,GAAG,GAAG1C,QAAQC,IAEnCmI,OAAQ,SAAUH,GACd,GAAmH2N,GAA/G1P,EAAO5F,KAAMrB,EAAOJ,EAAEoJ,EAAEiK,eAAgB2D,EAAW/S,EAAa7D,EAAKe,QAAQC,IAAQ6V,EAAO7W,EAAK+H,KAAK,OAEtG4O,GADAE,EACyB,KAARA,GAAeA,EAAKjV,QAAQ,IAAMP,KAAKyF,QAAQ2B,GAAK,MAAQ,EAE5DmO,EAASzW,SAAWyW,EAAS1W,WAAWC,OAEzDwW,GACA3N,EAAEC,iBAEDjJ,EAAKoD,SAAS,sBAAyB6D,EAAK6K,SAASrN,EAAQzE,IAC9DiH,EAAKyM,OAAO1T,IAGpB4H,SAAU,WACN,GAAyC5E,GAAS2E,EAA9CV,EAAO5F,KAAMyF,EAAUG,EAAKH,QAAwBgQ,EAAiB,qBACrEhQ,GAAQ1F,GAAG,OACX4B,EAAU8D,EAAQiQ,KAAK,WAAW5V,SAClCwG,EAAOb,IAEP9D,EAAU8D,EACVa,EAAO3E,EAAQ9C,SAAS,MAAMuD,GAAG,IAErCwD,EAAKjE,QAAUA,EAAQU,SAASoT,GAChC7P,EAAKU,KAAOA,GAEhBqP,iBAAkB,WACd,MAAO3V,MAAKyF,QAAQpG,KAAK,qBAAqBK,QAAQC,IAE1D6G,OAAQ,SAAUsC,GACd,GAAIlD,GAAO5F,KAAM4N,EAAa9E,EAAK/G,SAASsC,GAAYxE,GAChD+N,WAAYA,EACZtE,SAAUsE,GAAchI,EAAK4N,UAAU1K,IACxC8M,EAAe9M,EAAKjK,SAAS,KACpC+W,GAAavT,SAASuD,EAAKsH,UAAUkB,cAAcvO,IAAQgW,IAAI,UAAWhW,EAAMyJ,SAAW,GAAK,QAChG1D,EAAKkQ,OAAOF,EAAc/V,IAE9BiW,OAAQ,SAAUF,EAAcG,GAC5B,GAAsDvW,GAAlDoG,EAAO5F,KAAM+Q,EAAQ6E,EAAa/W,SAAS,KAC/CkX,GAAYnT,GAAS9D,OAAQiS,EAAMjS,QAAUiX,GAC7ChF,EAAMiF,KAAK,SAAUnN,EAAGlK,GACpBA,EAAOJ,EAAEI,GACTa,GACIgB,MAAOqI,EACPS,SAAU1D,EAAK4N,UAAU7U,IAE7B+C,EAAe/C,GACfiH,EAAKqQ,mBAAmBtX,EAAMoX,EAAWvW,GACzCoG,EAAKY,OAAO7H,MAGpB4O,YAAa,WAAA,GAGL2I,GAFAxQ,EAAU1F,KAAK0F,QACftG,EAAasG,EAAQtG,UAErBA,KACA8W,EAAkB,0HACd9W,EAAWsQ,OACXwG,GAAmB,UAAa9W,EAAWsQ,KAAO,KAEtDwG,GAAmB,iGACnB9W,EAAawD,GAAS1D,SAAUgX,GAAmBxQ,EAAQtG,kBAChDA,GAAWF,UAAYqF,IAC9BnF,EAAWF,SAAWA,EAASE,EAAWF,WAE9CwG,EAAQtG,WAAaA,IAG7B6W,mBAAoB,SAAUtX,EAAMoX,EAAWvW,GAA3B,GAcZ2W,GACAnI,EAdArM,EAAUhD,EAAKE,SAAS,OAAQgB,EAAQlB,EAAKE,SAAS,MAAOqO,EAAYlN,KAAKkN,SAC9EvO,GAAKoD,SAAS,gBAGlBvC,EAAWA,MACXA,EAAS8J,eAAkB9J,GAAS8J,UAAYnF,EAAY3E,EAAS8J,SAAWtJ,KAAKwT,UAAU7U,GAC/Fa,EAASgB,YAAehB,GAASgB,OAAS2D,EAAY3E,EAASgB,MAAQ7B,EAAK6B,QAC5EhB,EAAS0K,cAAiB1K,GAAS0K,SAAW/F,EAAY3E,EAAS0K,SAAWvI,EAAQ9C,SAAS,SAASkD,SAAS,oBACjHgU,EAAYA,MACZA,EAAUnI,iBAAoBmI,GAAUnI,YAAczJ,EAAY4R,EAAUnI,WAAajP,EAAKmB,SAASA,SAASiC,SAASsC,GACzH0R,EAAUjX,aAAgBiX,GAAUjX,QAAUqF,EAAY4R,EAAUjX,OAASH,EAAKmB,SAASjB,WAAWC,OACtGH,EAAKkJ,YAAY,kBAAkBxF,SAAS6K,EAAUS,gBAAgBoI,EAAWvW,IACjFmC,EAAQkG,YAAY,qBAAqBxF,SAAS6K,EAAUW,SAASkI,EAAWvW,IAC5E2W,EAAWxU,EAAQ9C,SAAS,SAC5BmP,EAASmI,EAAS,IAA4C,KAAtCA,EAAS,GAAGrV,SAASC,cACjDoV,EAAStO,YAAY,gDAAgDxF,SAAS6K,EAAUa,UAAUvO,EAAUwO,KACxGnO,EAAMf,QAA2C,QAAjCH,EAAK+H,KAAK,uBAC1B/E,EAAQ9C,SAAS,WAAWgJ,YAAY,2BAA2BxF,SAAS6K,EAAUgB,kBAAkB1O,IACxGK,EAAMwC,SAAS,cAGvB+T,cAAe,SAAUrF,EAAOsF,GAAjB,GAGFxN,GAFLjD,EAAO5F,KACPqJ,EAAQzD,EAAKH,QAAQpG,KAAK0R,EAC9B,KAASlI,EAAI,EAAGA,EAAIQ,EAAMvK,OAAQ+J,IAC9BwN,EAASpQ,KAAKL,EAAMiD,EAAGtK,EAAE8K,EAAMR,IAAInJ,QAAQC,KAGnDU,SAAU,SAAU1B,GAChB,GAAIoK,GAAMxK,EAAEI,GAAMe,QAAQC,GAAM+G,KAAKzH,EAAMyH,KAAK,QAASxG,EAAaF,KAAKE,UAC3E,OAAOA,IAAcA,EAAWoW,SAASvN,IAE7CwN,UAAW,SAAU5X,GACjB,GAAIoK,GAAMxK,EAAEI,GAAMe,QAAQC,GAAM+G,KAAKzH,EAAMyH,KAAK,QAASxG,EAAaF,KAAKE,UAC3E,OAAOA,IAAcF,KAAKkG,gBAAgB6C,IAE9CyN,YAAa,SAAUhX,EAAUgB,EAAOZ,EAAY6W,EAAgBC,GAChE,GAAiGC,GAI1FhY,EAAMkK,EAAGC,EAAqBtH,EAAYkS,EAJ7C9N,EAAO5F,KAAMH,EAAQ0C,EAAS3C,GAAagX,EAAqB/W,EAAMhB,WAAWC,OAAS,EAAiBiX,GACvGnI,WAAYhO,EAAWmC,SAASsC,GAChCiF,UAAWoN,EACX5X,OAAQ8X,GACMC,EAAW,GAA2B1U,EAAS,SAAU2G,EAAMjJ,GAC7EiJ,EAAK5G,SAASrC,GAEtB,KAAKgJ,EAAI,EAAGA,EAAIrJ,EAASV,OAAQ+J,IAC7BC,EAAOtJ,EAASqJ,GAChBC,EAAKtI,MAAQA,EAAQqI,EACrBgO,GAAYjR,EAAKkR,aACbjX,MAAOkW,EACPjN,KAAMA,GAId,IADAnK,EAAOJ,EAAEsY,GACJlY,EAAKG,OAAV,CAuBA,IApBA8G,EAAKmR,QAAQ,UAAW,WACpB,OACIC,SAAUrY,EAAKiW,MACflU,KAAMlB,EAAS2Q,IAAI,SAAUrH,GACzB,OAASzI,SAAUyI,QAI1BjJ,EAAMf,SACPe,EAAQtB,EAAEqH,EAAKqR,cAAepX,MAAOkW,KAAc7T,SAAStC,IAEhE6W,EAAe9X,EAAMkB,GACjBD,EAAWmC,SAAS,YACpBL,EAAe9B,GACfgG,EAAKqQ,mBAAmBrW,EAAYmW,GAAazM,UAAWoN,KAEhElV,EAAa7C,EAAKgV,OAAOxI,QACzBuI,EAAY/U,EAAK6K,OAAO4B,OACxBxF,EAAKqQ,mBAAmBzU,MAAkB8H,SAAqD,QAA3C9H,EAAWkF,KAAKzH,EAAMyH,KAAK,eAC/Ed,EAAKqQ,mBAAmBvC,MAAiBpK,SAAoD,QAA1CoK,EAAUhN,KAAKzH,EAAMyH,KAAK,eACxEmC,EAAI,EAAGA,EAAIrJ,EAASV,OAAQ+J,IAC7BC,EAAOtJ,EAASqJ,GACZC,EAAKoO,cACLP,EAAe7N,EAAKjK,SAAS6B,OACzBiW,EAAa7X,QACb8G,EAAK4Q,YAAYG,EAAc7N,EAAKtI,MAAO7B,EAAKyD,GAAGyG,GAAI1G,GAAS2G,EAAKQ,UAIjF,OAAO3K,KAEXwY,aAAc,SAAU9N,EAAOkG,GAQ3B,QAAS6H,GAAgB9Q,EAAMmG,GACvBnG,EAAKvG,GAAG,aACRuG,EAAKjH,KAAK,gCAAgCqH,KAAKlC,EAAaiI,GAEhEnG,EAAKjH,KAAK,4DAA4DsJ,KAAKjF,EAAS+I,GAAO/L,KAAKiD,GAAe,GAAOgF,KAAKhF,GAAe,GAZpI,GAENkF,GAAGlK,EAAM0Y,EAAavO,EAAM4I,EAAW4F,EAoBnCN,EArBJpR,EAAO5F,KAEPuX,GACAnK,SAAUxH,EAAKF,QACfoD,KAAMA,GAEN0O,EAAkB,YAATjI,GAAgC,WAATA,CAOpC,IAAa,YAATA,EACAzG,EAAOO,EAAM,GACb1K,EAAOiH,EAAK6R,UAAU3O,EAAKC,KAAK1J,KAAK,eAAewI,YAAY,iBAAiB6P,YAAY,mBAAoB5O,EAAKyG,IAAQoI,MAC1H7O,EAAKyG,IACL3J,EAAKuM,QAAQxT,GAEjBA,EAAK+H,KAAKjC,IAAgBqE,EAAKyG,QAC5B,CASH,IARIyH,EAAWzY,EAAE4R,IAAI9G,EAAO,SAAUP,GAClC,MAAOlD,GAAK6R,UAAU3O,EAAKC,KAAKlK,SAAS,SAEzC2Y,GACA5R,EAAKmR,QAAQ,UAAW,WACpB,OAASC,SAAUA,KAGtBnO,EAAI,EAAGA,EAAIQ,EAAMvK,OAAQ+J,IAC1B0O,EAAQzO,KAAOA,EAAOO,EAAMR,GAC5BwO,EAAcL,EAASnO,GACvBlK,EAAO0Y,EAAYvX,SACf0X,GACAH,EAAYxY,SAAS,SAAS+Y,KAAKhS,EAAKsH,UAAUmB,YAAYkJ,IAE9DhI,GAAS7L,GACTgO,EAAY5I,EAAKyG,GACjB6H,EAAgBC,EAAa3F,GAC7B/S,EAAK+H,KAAKlC,EAAakN,GACnB9L,EAAKF,QAAQtG,WAAW8H,gBACxBkQ,EAAgBzY,EAAKE,SAAS,YAAa6S,GAC3C9L,EAAK+K,YAAY7H,EAAKjK,SAAU6S,GAChC9L,EAAK0L,qBAAqB3S,KAEd,YAAT4Q,EACP3J,EAAKiS,QAAQlZ,EAAMmK,EAAMA,EAAKyG,IACd,WAATA,IACP5Q,EAAKU,KAAK,4CAA4CsJ,KAAK,YAAaG,EAAKyG,IAC7E+H,GAAe9U,EAAa7D,GAAMoB,GAAGuE,GACrC3F,EAAKmZ,WAAWpT,GACXoE,EAAKyG,KACFzG,EAAKmF,UACLnF,EAAK2I,IAAI,YAAY,GAErB3I,EAAKQ,UACLR,EAAK2I,IAAI,YAAY,GAEzB6F,GAAc,EACd3Y,EAAK+H,KAAKjC,GAAc,GAAOiC,KAAKhC,GAAc,IAEtDkB,EAAKqQ,mBAAmBtX,MACpBuL,QAASpB,EAAKyG,GACdjG,UAAWgO,KAGfD,EAAYvY,QACZkB,KAAK4I,QAAQ,cACTE,KAAMuO,EACN3W,KAAMoI,EACNiP,GAAIpV,GAIZ6U,IACA5R,EAAKmR,QAAQ,UAAW,WACpB,OACIC,SAAUA,EACVtW,KAAMnC,EAAE4R,IAAI9G,EAAO,SAAUP,GACzB,QAAUzI,SAAUyI,WAO5CkP,aAAc,SAAUxX,EAAO6I,EAAOzJ,GAAxB,GAKFqY,GACAC,EACAC,EANJtY,EAAQ0C,EAAS3C,GACjBf,EAAWgB,EAAMhB,WACjB6X,GAAa1W,KAAKwT,UAAU5T,EAC5BI,MAAKyF,UAAY7F,GACbqY,EAAYjY,KAAKE,WAAWQ,OAC5BwX,EAAYlY,KAAKE,WAAW+I,OAC5BkP,EAAYD,EAAUpZ,OAASmZ,EAAUnZ,OAASoZ,EAAYD,EAClEzX,EAAQ2X,EAAU5X,QAAQ8I,EAAM,KACzBA,EAAMvK,SACb0B,EAAQ6I,EAAM,GAAGvJ,SAASS,QAAQ8I,EAAM,WAEjC7I,IAAS2D,IAChB3D,EAAQ3B,EAASC,QAErBkB,KAAKwW,YAAYnN,EAAO7I,EAAOZ,EAAY,SAAUkJ,EAAMjJ,GACnDW,GAAS3B,EAASC,OAClBgK,EAAK5G,SAASrC,GAEdiJ,EAAKvH,aAAa1C,EAASuD,GAAG5B,KAEnCkW,GACEA,IACD1W,KAAKiW,mBAAmBrW,MAAkB0J,UAAWoN,IACrDnU,EAAS3C,GAAYiW,IAAI,UAAW,WAG5CuC,iBAAkB,SAAUxY,EAAYyJ,EAAO7I,GAA7B,GACVqI,GAAGhK,EAAUwZ,EACb3S,EAAU1F,KAAK0F,QACfK,EAAeL,EAAQK,aACvBmB,EAAgBxB,EAAQtG,YAAcsG,EAAQtG,WAAW8H,aAE7D,IADA3E,EAAS3C,GAAY0Y,QAChBjP,EAAMvK,OAQP,IALAkB,KAAKgY,aAAaxX,EAAO6I,EAAOzJ,GAChCf,EAAW0D,EAAS3C,GAAYf,WAC5BkH,GAAgBmB,GAChBlH,KAAKsR,qBAAqBzS,EAASuM,QAElCvC,EAAI,EAAGA,EAAIhK,EAASC,OAAQ+J,IAC7BwP,EAAQxZ,EAASuD,GAAGyG,GACpB7I,KAAK4I,QAAQ,cACTE,KAAMuP,EAAMxZ,SAAS,OACrB6B,KAAM2I,EAAMR,GACZkP,GAAIpV,QAZZjB,GAAe9B,IAiBvB2Y,aAAc,SAAUlP,GAAV,GAUFxJ,GAKJmX,EACKnO,EAfL2P,EAAYxY,KAAKiX,cACjB5N,MAAOA,EACPxJ,OACI+N,YAAY,EACZtE,UAAU,IAWlB,KARItJ,KAAKsG,KAAKxH,QACVkB,KAAKyY,cAAc,WACf5Y,EAAQtB,EAAEia,GACdxY,KAAKsG,KAAKI,KAAK,QAAS7G,EAAM6G,KAAK,UAAUkR,KAAK/X,EAAM+X,SAExD5X,KAAKsG,KAAOtG,KAAK2B,QAAQiW,KAAKY,GAAW3Z,SAAS,MAElDmY,EAAWhX,KAAKsG,KAAKzH,SAAS,WACzBgK,EAAI,EAAGA,EAAIQ,EAAMvK,OAAQ+J,IAC9B7I,KAAK4I,QAAQ,cACTE,KAAMkO,EAAS5U,GAAGyG,GAClBnI,KAAM2I,EAAMR,GACZkP,GAAIpV,GAGZ3C,MAAKyY,cAAc,YAEvB5J,QAAS,SAAUlH,GAAV,GAQDkB,GAkBI6P,EAzBJ/Z,EAAOgJ,EAAEhJ,KACTga,EAAShR,EAAEgR,OACXtP,EAAQ1B,EAAE0B,MACVzJ,EAAaI,KAAK2B,QAClB+D,EAAU1F,KAAK0F,QACfK,EAAeL,EAAQK,aACvBmB,EAAgBxB,EAAQtG,YAAcsG,EAAQtG,WAAW8H,aAE7D,KAAIlH,KAAKwR,MAAT,CAGA,IAAK3I,EAAI,EAAGA,EAAIQ,EAAMvK,OAAQ+J,IAC1B7I,KAAKkG,gBAAgBmD,EAAMR,GAAGE,KAAOM,EAAMR,EAE/C,IAAIlB,EAAE4H,MAAO,CACT,IAAKlG,EAAM,KAAOA,EAAM,GAAGuP,MACvB,MAEJ,OAAO5Y,MAAKmX,aAAa9N,EAAO1B,EAAE4H,OAMtC,GAJI5Q,IACAiB,EAAaI,KAAKyX,UAAU9Y,EAAKoK,KACjC/I,KAAKgH,UAAUpH,GAAY,IAE3BsH,GAA2B,UAAVyR,EAAoB,CAErC,IADID,GAAS,EACR7P,EAAI,EAAGA,EAAIQ,EAAMvK,OAAQ+J,IAC1B,GAAI,WAAaQ,GAAMR,GAAI,CACvB6P,GAAS,CACT,OAGR,IAAKA,GAAU/Z,GAAQA,EAAK4K,QACxB,IAAKV,EAAI,EAAGA,EAAIQ,EAAMvK,OAAQ+J,IAC1BQ,EAAMR,GAAGU,SAAU,EAe/B,GAXc,OAAVoP,EACA3Y,KAAKgY,aAAarQ,EAAEnH,MAAO6I,EAAOzJ,GACjB,UAAV+Y,EACP3Y,KAAK6Y,QAAQ7Y,KAAKyX,UAAUpO,EAAM,GAAGN,MAAM,GAC1B,cAAV4P,EACP3Y,KAAKmX,aAAa9N,GACD,cAAVsP,EACP3Y,KAAKoY,iBAAiBxY,EAAYyJ,EAAO1B,EAAEnH,OAE3CR,KAAKuY,aAAalP,GAER,UAAVsP,EACA,IAAK9P,EAAI,EAAGA,EAAIQ,EAAMvK,OAAQ+J,MACrB9C,GAAgBsD,EAAMR,GAAGS,UAAYD,EAAMR,GAAGiQ,UAC/CzP,EAAMR,GAAGkQ,MAIrB/Y,MAAK4I,QAAQ3E,GAAatF,KAAMA,EAAOiB,EAAapB,IAChDwB,KAAKE,WAAWxB,UAAYsB,KAAK0F,QAAQtG,WAAW8H,eACpDlH,KAAKmH,oBAAoBvH,KAGjCmP,OAAQ,SAAUpH,GAAV,GACAhJ,GAAOgJ,EAAEhJ,MAAQqB,KAAKyX,UAAU9P,EAAEhJ,KAAKoK,KACvCiQ,EAAYhZ,KAAKkN,UAAUsB,OAAQoB,SAAU5P,KAAK0F,QAAQkK,UAC1DjR,IACAqB,KAAKgH,UAAUrI,GAAM,GACrBqB,KAAKwT,UAAU7U,GAAM,GACrB8D,EAAS9D,GAAM0D,SAAS,cACxBsF,EAAEhJ,KAAKsa,QAAO,KAEdjZ,KAAKgH,WAAU,GACfhH,KAAKyF,QAAQmS,KAAKoB,KAG1BzQ,cAAe,SAAUZ,GACrBA,EAAEC,iBACF5H,KAAKE,WAAW+G,SAEpB4C,OAAQ,SAAUkH,GACd/Q,KAAKoW,cAAcrF,EAAO,SAAUvQ,EAAOsI,GACvC9I,KAAK6R,OAAO/I,GAAM,MAG1Bc,SAAU,SAAUmH,GAChB/Q,KAAKoW,cAAcrF,EAAO,SAAUvQ,EAAOsI,GACvC9I,KAAK6R,OAAO/I,GAAM,MAG1BoQ,OAAQ,SAAUnI,EAAOmI,GACA,iBAAVnI,IACPmI,EAASnI,EACTA,EAAQ/Q,KAAKqJ,SAEb6P,EAA6B,GAApBC,UAAUra,UAAgBoa,EAEvClZ,KAAKoW,cAAcrF,EAAO,SAAUvQ,EAAOsI,GACvC9I,KAAKK,SAASyI,GAAM2I,IAAI,UAAWyH,MAG3C/G,QAAS,SAAUxT,GACf,GAAIiH,GAAO5F,KAAMmS,EAAUvM,EAAKwT,SAAU3T,EAAUG,EAAKH,QAAS2B,EAAKxB,EAAKyB,OAC5E,OAAI8R,WAAUra,OAAS,GAAKH,GAAQA,EAAKG,QACjCqT,IACIA,EAAQ,GAAG/K,KAAOA,GAClB+K,EAAQ2F,WAAW,MAEvB3F,EAAQ9S,KAAK,eAAewI,YAAY,oBAE5CsK,EAAUvM,EAAKwT,SAAW7a,EAAEI,EAAM8G,GAAS/F,QAAQC,GACnDwS,EAAQ9S,KAAK,eAAegD,SAAS,mBACrC+E,EAAK+K,EAAQ,GAAG/K,IAAMA,EAClBA,IACAxB,EAAKjE,QAAQmW,WAAW,yBACxB3F,EAAQzL,KAAK,KAAMU,GACnBxB,EAAKjE,QAAQ+E,KAAK,wBAAyBU,IAE/C,IAEC+K,IACDA,EAAUvM,EAAK4M,aAAajU,MAEzB4T,IAEXE,OAAQ,SAAU1T,GACd,GAAIiH,GAAO5F,KAAMyF,EAAUG,EAAKH,OAChC,OAAK0T,WAAUra,QAGfH,EAAOJ,EAAEI,EAAM8G,GAAS/F,QAAQC,GAChC8F,EAAQpG,KAAK,qBAAqB2W,KAAK,WACnC,GAAI3V,GAAWuF,EAAKvF,SAASL,KACzBK,IACAA,EAASoR,IAAI,YAAY,SAClBpR,GAAS4N,UAEhB1P,EAAEyB,MAAM6H,YAAY,sBAGxBlJ,EAAKG,SACL8G,EAAKvF,SAAS1B,GAAM8S,IAAI,YAAY,GACpC7L,EAAKsM,aAAevT,GAExBiH,EAAKgD,QAAQpF,GAdb7E,GAFW8G,EAAQpG,KAAK,qBAAqBK,QAAQC,IAkBzDkY,QAAS,SAAUlZ,EAAM0B,EAAUwJ,GAA1B,GAIDoP,GAHAvT,EAAU1F,KAAK0F,QACf6P,EAAW/S,EAAa7D,GACxB0a,EAAYxP,EAAS,SAAW,UAEhC0L,GAAS7U,KAAK,eAGlBuY,EAAS5Y,GAAYA,EAAS4Y,SAC1BpP,IAAWoP,GACPvT,EAAQK,cACR/F,KAAKgH,UAAUrI,GAAM,GAEzB4W,EAAStT,SACT5B,EAAS0Y,SAET/Y,KAAKiW,mBAAmBtX,MAAY2K,SAAUO,IACzCA,GACD0L,EAASM,IAAI,SAAUN,EAAS+D,UAAUzD,IAAI,UAElDN,EAASgE,WAAU,GAAM,GAAMC,aAAa5W,GAAS6W,OAAO,GAAQ/T,EAAQgE,UAAU2P,IAClFK,SAAU,WACF7P,GACA0L,EAASM,IAAI,SAAU,WAM3ChE,OAAQ,SAAUlT,EAAMkL,GACpBlL,EAAOJ,EAAEI,GACJ8D,EAAS9D,GAAMoB,GAAG,gCAGC,GAApBoZ,UAAUra,SACV+K,GAAU7J,KAAKwT,UAAU7U,IAE7BqB,KAAKwT,UAAU7U,EAAMkL,KAEzBkD,QAAS,WACL,GAAInH,GAAO5F,IACX8C,GAAO0N,GAAGzD,QAAQ9G,KAAKL,GACvBA,EAAKjE,QAAQsL,IAAI/J,GACjB0C,EAAKjE,QAAQtC,KAAK,iCAAiC4N,IAAI/J,GACvD0C,EAAKqJ,oBACDrJ,EAAKwE,UACLxE,EAAKwE,SAAS2C,UAElBnH,EAAKM,mBACLjH,EAAM8N,QAAQnH,EAAKH,SACnBG,EAAKU,KAAOV,EAAKjE,QAAUiE,EAAKH,QAAU,MAE9C+N,UAAW,SAAU7U,EAAMkS,EAAO8I,GAAvB,GAEHtZ,GADA8I,EAAelK,EAAMyH,KAAK,YAE1B4C,EAAWuH,EACXwI,EAAY/P,EAAW,SAAW,UACtC,OAAwB,IAApB6P,UAAUra,QACVuB,EAAWL,KAAKuW,UAAU5X,GACS,SAA5BA,EAAK+H,KAAKyC,IAA4B9I,GAAYA,EAASiJ,WAEtEjJ,EAAWL,KAAKK,SAAS1B,GACrB6D,EAAa7D,GAAM+B,KAAK,eAGxBiZ,GAAU3Z,KAAKyQ,SAAS4I,EAAW1a,KAC/B2K,GACA3K,EAAK+H,KAAKyC,EAAc,QACxBxK,EAAK+H,KAAK,gBAAiB,UAE3B/H,EAAKmZ,WAAW3O,GAChBxK,EAAK+H,KAAK,gBAAiB,UAE3BrG,IACAA,EAASoR,IAAI,WAAYnI,GACzBA,EAAWjJ,EAASiJ,WAd5BjJ,IAkBJ2G,UAAW,SAAUrI,EAAMib,GAAhB,GACHnU,GAAUzF,KAAKyF,QACfoU,EAAc7Z,KAAKkN,UAAUqB,SAAUqB,SAAU5P,KAAK0F,QAAQkK,UAC1C,IAApBuJ,UAAUra,QACV8a,EAAejb,EACXib,EACAnU,EAAQmS,KAAKiC,GAEbpU,EAAQ6S,SAGZ7V,EAAS9D,GAAM+Y,YAAY,cAAekC,GAAc/R,YAAY,eAG5EhD,KAAM,SAAUlG,EAAMkG,GAClB,GAAIxE,GAAWL,KAAKK,SAAS1B,GAAOsR,EAAgBjQ,KAAK0F,QAAQd,EAASC,MAAO+T,EAAQvY,EAASuY,QAAS9Z,EAASmR,EAAcnR,OAAQyQ,EAAQU,EAAc6J,KAAKC,IAAInB,EAAO9Z,EAAS,GACzL,OAAI+F,IACAxE,EAASoR,IAAIlC,EAAO1K,GAApBxE,GAEOA,EAASkP,IAGxByK,cAAe,SAAUrb,GACrB,MAAOJ,GAAEI,GAAMe,QAAQ,wBAAwBgB,KAAK,kBAAoBV,MAE5EC,gBAAiB,SAAUT,EAAUK,EAAOD,EAAYyW,GAAvC,GACT4D,GAAmBC,EAAela,KAAKga,cAAcpa,GAAcC,GAAQsa,EAAiBD,EAAaha,WACzGka,EAAc7b,EAAE8b,WAAWC,UAAUC,SAiBzC,OAhBI3a,IAAcA,EAAW,IAAMsa,EAAazU,QAAQ,KACpDwU,EAAoBC,EAAa7Z,SAAST,GACrCqa,EAAkBhB,WACnBiB,EAAalT,UAAUpH,GAAY,GACnCwa,EAAcH,EAAkBlB,QAEhCnZ,GAAcI,KAAKsG,OACnB6T,EAAiBF,EAAkBpb,SAC9Bsb,GAAoBA,YAA0BpX,KAC/CkX,EAAkB5K,gBAClB4K,EAAkBhB,QAAO,GACzBkB,EAAiBF,EAAkBpb,YAI/CW,EAAWQ,KAAKwa,kBAAkBhb,GAC3B6W,EAASpQ,KAAKiU,EAAcC,EAAgB3a,EAAU4a,IAEjEI,kBAAmB,SAAU7b,GACzB,GAAqBuB,GAAY6I,EAA7B1I,EAAW1B,CASf,QARIsG,EAAiBtG,IAASyG,EAAazG,MACvCuB,EAAaF,KAAKga,cAAcrb,GAAMuB,WACtC6I,EAAMxK,EAAEI,GAAM+H,KAAKzH,EAAMyH,KAAK,QAC9BrG,EAAWH,EAAWoW,SAASvN,GAC3B1I,IACAA,EAAWH,EAAW+B,OAAO5B,KAG9BA,GAEXI,QAAS,SAAUC,EAAMP,EAAOK,GACtBL,YAAiBlB,GAAMyB,KAAK+Z,gBAK9Bta,EAAQA,EAAMua,SAJT7X,EAAQ1C,KACTA,GAASA,GAKjB,IAAIP,GAAac,EAAKZ,QAStB,OARIF,IAAcA,EAAWyP,gBACzBzP,EAAWsX,aAAc,EACzBtX,EAAWyP,iBAEf3O,EAAKia,OAAOC,MAAMla,GACdF,EACA,GACFqa,OAAO1a,IACFH,KAAKyX,UAAU/W,EAAKF,GAAOuI,MAEtC+D,YAAaxN,EAAa,GAC1BiC,aAAcjC,EAAa,GAC3B6C,OAAQ,SAAU3C,EAAUI,EAAYkb,GACpC,GAAIjb,GAAQG,KAAKsG,IACjB,MAAI1G,GAAcJ,YAAoB2F,SAAUvF,EAAW,KAAOJ,EAAS,IAO3E,MAJAI,GAAaA,GAAcA,EAAWd,OAASc,EAAa,KACxDA,IACAC,EAAQ0C,EAAS3C,IAEdI,KAAKC,gBAAgBT,EAAUK,EAAOD,EAAY,SAAUM,EAAYC,EAAO4a,GAGlF,QAASC,KACDpb,GACAgG,EAAK4N,UAAU5T,GAAY,GAAM,EAErC,IAAIc,GAAOR,EAAWQ,OAAQF,EAAQsZ,KAAKmB,IAAIva,EAAK5B,OAAQ,EAC5D,OAAO8G,GAAKnF,QAAQC,EAAMP,EAAOK,GARgB,GACjD0a,GACAtV,EAAO5F,IAaX,OALA+a,GAAUI,KAAK,WACXD,EAAWF,KACXF,EAAUA,GAAWvc,EAAE6c,MACfF,KAELA,GAAY,QAG3BrC,QAAS,SAAUla,EAAM0c,GACrB,GAAiBzb,GAAY0b,EAAata,EAAtC4E,EAAO5F,IAeX,OAdArB,GAAOJ,EAAEI,EAAMiH,EAAKH,SACpBzF,KAAK+W,QAAQ,UAAW,WACpB,OAASC,SAAUrY,EAAKiW,SAE5BhV,EAAajB,EAAKmB,SAASA,SAC3Bwb,EAAc3c,EAAKgV,OACnB3S,EAAcrC,EAAK6K,OACnB7K,EAAK0c,EAAW,SAAW,YACvBzb,EAAWmC,SAAS,YACpBL,EAAe9B,GACfgG,EAAKqQ,mBAAmBrW,IAE5BgG,EAAKqQ,mBAAmBqF,GACxB1V,EAAKqQ,mBAAmBjV,GACjBrC,GAEXsD,OAAQ,SAAUtD,GACd,GAAI0B,GAAWL,KAAKK,SAAS1B,EACzB0B,IACAL,KAAKE,WAAW+B,OAAO5B,IAG/Bkb,OAAQ,SAAU5c,GACd,MAAOqB,MAAK6Y,QAAQla,GAAM,IAE9B6c,WAAY,SAAU3W,GAClB,MAAOtG,GAAEyB,KAAKyF,SAASpG,KAAK,SAASX,OAAO,SAAUmK,EAAGpD,GACrD,MAAOlH,GAAEkH,GAASZ,QAAUA,IAC7BnF,QAAQC,IAEf8X,UAAW,SAAU1O,GAAV,GAGHnK,GACKiK,EAHLQ,EAAQrJ,KAAKyF,QAAQpG,KAAK,WAC1B6J,EAAUjK,EAAMyH,KAAK,MAEzB,KAASmC,EAAI,EAAGA,EAAIQ,EAAMvK,OAAQ+J,IAC9B,GAAIQ,EAAMR,GAAG4S,aAAavS,IAAYH,EAAK,CACvCnK,EAASyK,EAAMR,EACf,OAGR,MAAOtK,GAAEK,IAEb8c,WAAY,SAAUC,EAAMjC,GAIxB,QAASkC,KACLC,EAAQC,QACJD,EAAQ/c,OACR+K,EAAOgS,EAAQ,IAAIE,KAAKH,GAExBvF,EAASpQ,KAAKmH,GAGtB,QAASvD,GAAOzC,GAAhB,GACQxI,GAASL,EAAE8b,WACX1b,EAAOyO,EAASlN,WAAW0U,IAAIxN,EAenC,OAdIzI,GACIA,EAAKsa,UACLta,EAAK8S,IAAI,YAAY,GACrB7S,EAAO0b,YAEPlN,EAASpG,UAAUoG,EAASqK,UAAU9Y,EAAKoK,MAAM,GACjDpK,EAAKoa,OAAOgD,KAAK,WACbpd,EAAK8S,IAAI,YAAY,GACrB7S,EAAO0b,aAIf1b,EAAO0b,UAEJ1b,EAAO2b,UA7BV,GACJnN,GAAWpN,KACX6b,EAAUF,EAAKK,MAAM,GACrB3F,EAAWqD,GAAYnb,EAAE6c,IA4B7BvR,GAAOgS,EAAQ,IAAIE,KAAKH,IAE5BK,WAAY,SAAUtd,GAGlB,IAHQ,GACJmB,GAASnB,GAAQA,EAAKiB,aACtBsc,KACGpc,GAAUA,EAAOF,YACpBsc,EAAQC,QAAQrc,EAAOsH,IACvBtH,EAASA,EAAOF,YAEpB,OAAOsc,IAEXE,SAAU,SAAUzd,GACVA,YAAgBM,GAAMyB,KAAK2b,OAC7B1d,EAAOqB,KAAKE,WAAW0U,IAAIjW,GAE/B,IAAIud,GAAUlc,KAAKic,WAAWtd,EAC9BqB,MAAK0b,WAAWQ,IAEpBpF,YAAa,SAAUpR,GAMnB,MALKA,GAAQ7F,QACT6F,EAAQ7F,UAEZ6F,EAAQ0H,SAAWpN,KAAK0F,QACxBA,EAAQ4W,EAAItc,KAAKkN,UACVlN,KAAKkN,UAAUpE,KAAKpD,IAE/BuR,aAAc,SAAUvR,GACpB,GAAIE,GAAO5F,IAaX,OAZA0F,GAAQ6W,YAAc,SAAU7W,GAC5B,GAAIkS,GAAO,GAAI/O,EAAI,EAAGQ,EAAQ3D,EAAQ2D,MAAOmT,EAAMnT,EAAQA,EAAMvK,OAAS,EAAGe,EAAQ6F,EAAQ7F,KAE7F,KADAA,EAAMf,OAAS0d,EACR3T,EAAI2T,EAAK3T,IACZnD,EAAQ7F,MAAQA,EAChB6F,EAAQoD,KAAOO,EAAMR,GACrBnD,EAAQoD,KAAKtI,MAAQqI,EACrB+O,GAAQhS,EAAKkR,YAAYpR,EAE7B,OAAOkS,IAEXlS,EAAQ4W,EAAI1W,EAAKsH,UACVtH,EAAKsH,UAAUrN,MAAM6F,MAGpC/C,EAAG8Z,OAAOna,IACZI,OAAOzD,MAAMkG,QACRzC,OAAOzD,OACE,kBAAVX,SAAwBA,OAAOoe,IAAMpe,OAAS,SAAUqe,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.treeview.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.treeview', [\n        'kendo.data',\n        'kendo.treeview.draganddrop'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'treeview',\n        name: 'TreeView',\n        category: 'web',\n        description: 'The TreeView widget displays hierarchical data in a traditional tree structure,with support for interactive drag-and-drop operations.',\n        depends: ['data'],\n        features: [{\n                id: 'treeview-dragging',\n                name: 'Drag & Drop',\n                description: 'Support for drag & drop',\n                depends: ['treeview.draganddrop']\n            }]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, data = kendo.data, extend = $.extend, template = kendo.template, isArray = $.isArray, Widget = ui.Widget, HierarchicalDataSource = data.HierarchicalDataSource, proxy = $.proxy, keys = kendo.keys, NS = '.kendoTreeView', TEMP_NS = '.kendoTreeViewTemp', SELECT = 'select', CHECK = 'check', NAVIGATE = 'navigate', EXPAND = 'expand', CHANGE = 'change', ERROR = 'error', CHECKED = 'checked', INDETERMINATE = 'indeterminate', COLLAPSE = 'collapse', DRAGSTART = 'dragstart', DRAG = 'drag', DROP = 'drop', DRAGEND = 'dragend', DATABOUND = 'dataBound', CLICK = 'click', UNDEFINED = 'undefined', KSTATEHOVER = 'k-state-hover', KTREEVIEW = 'k-treeview', VISIBLE = ':visible', NODE = '.k-item', STRING = 'string', ARIACHECKED = 'aria-checked', ARIASELECTED = 'aria-selected', ARIADISABLED = 'aria-disabled', DISABLED = 'k-state-disabled', TreeView, subGroup, nodeContents, nodeIcon, spriteRe, bindings = {\n                text: 'dataTextField',\n                url: 'dataUrlField',\n                spriteCssClass: 'dataSpriteCssClassField',\n                imageUrl: 'dataImageUrlField'\n            }, isJQueryInstance = function (obj) {\n                return obj instanceof kendo.jQuery || window.jQuery && obj instanceof window.jQuery;\n            }, isDomElement = function (o) {\n                return typeof HTMLElement === 'object' ? o instanceof HTMLElement : o && typeof o === 'object' && o.nodeType === 1 && typeof o.nodeName === STRING;\n            };\n        function contentChild(filter) {\n            return function (node) {\n                var result = node.children('.k-animation-container');\n                if (!result.length) {\n                    result = node;\n                }\n                return result.children(filter);\n            };\n        }\n        function templateNoWith(code) {\n            return kendo.template(code, { useWithBlock: false });\n        }\n        subGroup = contentChild('.k-group');\n        nodeContents = contentChild('.k-group,.k-content');\n        nodeIcon = function (node) {\n            return node.children('div').children('.k-icon');\n        };\n        function checkboxes(node) {\n            return node.find('.k-checkbox-wrapper:first input[type=checkbox]');\n        }\n        function insertAction(indexOffset) {\n            return function (nodeData, referenceNode) {\n                referenceNode = referenceNode.closest(NODE);\n                var group = referenceNode.parent(), parentNode;\n                if (group.parent().is('li')) {\n                    parentNode = group.parent();\n                }\n                return this._dataSourceMove(nodeData, group, parentNode, function (dataSource, model) {\n                    var referenceItem = this.dataItem(referenceNode);\n                    var referenceNodeIndex = referenceItem ? referenceItem.parent().indexOf(referenceItem) : referenceNode.index();\n                    return this._insert(dataSource.data(), model, referenceNodeIndex + indexOffset);\n                });\n            };\n        }\n        spriteRe = /k-sprite/;\n        function moveContents(node, container) {\n            var tmp;\n            while (node && node.nodeName.toLowerCase() != 'ul') {\n                tmp = node;\n                node = node.nextSibling;\n                if (tmp.nodeType == 3) {\n                    tmp.nodeValue = $.trim(tmp.nodeValue);\n                }\n                if (spriteRe.test(tmp.className)) {\n                    container.insertBefore(tmp, container.firstChild);\n                } else {\n                    container.appendChild(tmp);\n                }\n            }\n        }\n        function updateNodeHtml(node) {\n            var wrapper = node.children('div'), group = node.children('ul'), toggleButton = wrapper.children('.k-icon'), checkbox = node.children('input[type=checkbox]'), innerWrapper = wrapper.children('.k-in');\n            if (node.hasClass('k-treeview')) {\n                return;\n            }\n            if (!wrapper.length) {\n                wrapper = $('<div />').prependTo(node);\n            }\n            if (!toggleButton.length && group.length) {\n                toggleButton = $('<span class=\\'k-icon\\' />').prependTo(wrapper);\n            } else if (!group.length || !group.children().length) {\n                toggleButton.remove();\n                group.remove();\n            }\n            if (checkbox.length) {\n                $('<span class=\\'k-checkbox-wrapper\\' />').appendTo(wrapper).append(checkbox);\n            }\n            if (!innerWrapper.length) {\n                innerWrapper = node.children('a').eq(0).addClass('k-in k-link');\n                if (!innerWrapper.length) {\n                    innerWrapper = $('<span class=\\'k-in\\' />');\n                }\n                innerWrapper.appendTo(wrapper);\n                if (wrapper.length) {\n                    moveContents(wrapper[0].nextSibling, innerWrapper[0]);\n                }\n            }\n        }\n        TreeView = kendo.ui.DataBoundWidget.extend({\n            init: function (element, options) {\n                var that = this, inferred = false, hasDataSource = options && !!options.dataSource, list;\n                if (isArray(options)) {\n                    options = { dataSource: options };\n                }\n                if (options && typeof options.loadOnDemand == UNDEFINED && isArray(options.dataSource)) {\n                    options.loadOnDemand = false;\n                }\n                Widget.prototype.init.call(that, element, options);\n                element = that.element;\n                options = that.options;\n                that._dataSourceUids = {};\n                list = element.is('ul') && element || element.hasClass(KTREEVIEW) && element.children('ul');\n                inferred = !hasDataSource && list.length;\n                if (inferred) {\n                    options.dataSource.list = list;\n                }\n                that._animation();\n                that._accessors();\n                that._templates();\n                if (!element.hasClass(KTREEVIEW)) {\n                    that._wrapper();\n                    if (list) {\n                        that.root = element;\n                        that._group(that.wrapper);\n                    }\n                } else {\n                    that.wrapper = element;\n                    that.root = element.children('ul').eq(0);\n                }\n                that._tabindex();\n                that.wrapper.attr('role', 'tree');\n                that._dataSource(inferred);\n                that._attachEvents();\n                that._dragging();\n                if (!inferred) {\n                    if (options.autoBind) {\n                        that._progress(true);\n                        that.dataSource.fetch();\n                    }\n                } else {\n                    that._syncHtmlAndDataSource();\n                }\n                if (options.checkboxes && options.checkboxes.checkChildren) {\n                    that.updateIndeterminate();\n                }\n                if (that.element[0].id) {\n                    that._ariaId = kendo.format('{0}_tv_active', that.element[0].id);\n                }\n                kendo.notify(that);\n            },\n            _attachEvents: function () {\n                var that = this, clickableItems = '.k-in:not(.k-state-selected,.k-state-disabled)', MOUSEENTER = 'mouseenter';\n                that.wrapper.on(MOUSEENTER + NS, '.k-in.k-state-selected', function (e) {\n                    e.preventDefault();\n                }).on(MOUSEENTER + NS, clickableItems, function () {\n                    $(this).addClass(KSTATEHOVER);\n                }).on('mouseleave' + NS, clickableItems, function () {\n                    $(this).removeClass(KSTATEHOVER);\n                }).on(CLICK + NS, clickableItems, proxy(that._click, that)).on('dblclick' + NS, '.k-in:not(.k-state-disabled)', proxy(that._toggleButtonClick, that)).on(CLICK + NS, '.k-i-expand,.k-i-collapse', proxy(that._toggleButtonClick, that)).on('keydown' + NS, proxy(that._keydown, that)).on('keypress' + NS, proxy(that._keypress, that)).on('focus' + NS, proxy(that._focus, that)).on('blur' + NS, proxy(that._blur, that)).on('mousedown' + NS, '.k-in,.k-checkbox-wrapper :checkbox,.k-i-expand,.k-i-collapse', proxy(that._mousedown, that)).on('change' + NS, '.k-checkbox-wrapper :checkbox', proxy(that._checkboxChange, that)).on('click' + NS, '.checkbox-span', proxy(that._checkboxLabelClick, that)).on('click' + NS, '.k-request-retry', proxy(that._retryRequest, that)).on('click' + NS, '.k-link.k-state-disabled', function (e) {\n                    e.preventDefault();\n                }).on('click' + NS, function (e) {\n                    var target = $(e.target);\n                    if (!target.is(':kendoFocusable') && !target.find('input,select,textarea,button,object').is(':kendoFocusable')) {\n                        that.focus();\n                    }\n                });\n            },\n            _checkboxLabelClick: function (e) {\n                var checkbox = $(e.target.previousSibling);\n                if (checkbox.is('[disabled]')) {\n                    return;\n                }\n                checkbox.prop('checked', !checkbox.prop('checked'));\n                checkbox.trigger('change');\n            },\n            _syncHtmlAndDataSource: function (root, dataSource) {\n                root = root || this.root;\n                dataSource = dataSource || this.dataSource;\n                var data = dataSource.view(), uidAttr = kendo.attr('uid'), expandedAttr = kendo.attr('expanded'), checkboxesEnabled = this.options.checkboxes, items = root.children('li'), i, item, dataItem, uid, itemCheckbox;\n                for (i = 0; i < items.length; i++) {\n                    dataItem = data[i];\n                    uid = dataItem.uid;\n                    item = items.eq(i);\n                    item.attr('role', 'treeitem').attr(uidAttr, uid).attr(ARIASELECTED, item.hasClass('k-state-selected'));\n                    dataItem.expanded = item.attr(expandedAttr) === 'true';\n                    if (checkboxesEnabled) {\n                        itemCheckbox = checkboxes(item);\n                        dataItem.checked = itemCheckbox.prop(CHECKED);\n                        itemCheckbox.attr('id', '_' + uid);\n                        itemCheckbox.next('.k-checkbox-label').attr('for', '_' + uid);\n                    }\n                    this._syncHtmlAndDataSource(item.children('ul'), dataItem.children);\n                }\n            },\n            _animation: function () {\n                var options = this.options, animationOptions = options.animation, hasCollapseAnimation = animationOptions.collapse && 'effects' in animationOptions.collapse, collapse = extend({}, animationOptions.expand, animationOptions.collapse);\n                if (!hasCollapseAnimation) {\n                    collapse = extend(collapse, { reverse: true });\n                }\n                if (animationOptions === false) {\n                    animationOptions = {\n                        expand: { effects: {} },\n                        collapse: {\n                            hide: true,\n                            effects: {}\n                        }\n                    };\n                }\n                animationOptions.collapse = extend(collapse, { hide: true });\n                options.animation = animationOptions;\n            },\n            _dragging: function () {\n                var enabled = this.options.dragAndDrop;\n                var dragging = this.dragging;\n                if (enabled && !dragging) {\n                    var widget = this;\n                    this.dragging = new ui.HierarchicalDragAndDrop(this.element, {\n                        reorderable: true,\n                        $angular: this.options.$angular,\n                        autoScroll: this.options.autoScroll,\n                        filter: 'div:not(.k-state-disabled) .k-in',\n                        allowedContainers: '.k-treeview',\n                        itemSelector: '.k-treeview .k-item',\n                        hintText: proxy(this._hintText, this),\n                        contains: function (source, destination) {\n                            return $.contains(source, destination);\n                        },\n                        dropHintContainer: function (item) {\n                            return item;\n                        },\n                        itemFromTarget: function (target) {\n                            var item = target.closest('.k-top,.k-mid,.k-bot');\n                            return {\n                                item: item,\n                                content: target.closest('.k-in'),\n                                first: item.hasClass('k-top'),\n                                last: item.hasClass('k-bot')\n                            };\n                        },\n                        dropPositionFrom: function (dropHint) {\n                            return dropHint.prevAll('.k-in').length > 0 ? 'after' : 'before';\n                        },\n                        dragstart: function (source) {\n                            return widget.trigger(DRAGSTART, { sourceNode: source[0] });\n                        },\n                        drag: function (options) {\n                            widget.trigger(DRAG, {\n                                originalEvent: options.originalEvent,\n                                sourceNode: options.source[0],\n                                dropTarget: options.target[0],\n                                pageY: options.pageY,\n                                pageX: options.pageX,\n                                statusClass: options.status,\n                                setStatusClass: options.setStatus\n                            });\n                        },\n                        drop: function (options) {\n                            var dropTarget = $(options.dropTarget);\n                            var navigationTarget = dropTarget.closest('a');\n                            if (navigationTarget && navigationTarget.attr('href')) {\n                                widget._tempPreventNavigation(navigationTarget);\n                            }\n                            return widget.trigger(DROP, {\n                                originalEvent: options.originalEvent,\n                                sourceNode: options.source,\n                                destinationNode: options.destination,\n                                valid: options.valid,\n                                setValid: function (state) {\n                                    this.valid = state;\n                                    options.setValid(state);\n                                },\n                                dropTarget: options.dropTarget,\n                                dropPosition: options.position\n                            });\n                        },\n                        dragend: function (options) {\n                            var source = options.source;\n                            var destination = options.destination;\n                            var position = options.position;\n                            function triggerDragEnd(source) {\n                                if (widget.options.checkboxes && widget.options.checkboxes.checkChildren) {\n                                    widget.updateIndeterminate();\n                                }\n                                widget.trigger(DRAGEND, {\n                                    originalEvent: options.originalEvent,\n                                    sourceNode: source && source[0],\n                                    destinationNode: destination[0],\n                                    dropPosition: position\n                                });\n                            }\n                            if (position == 'over') {\n                                widget.append(source, destination, triggerDragEnd);\n                            } else {\n                                if (position == 'before') {\n                                    source = widget.insertBefore(source, destination);\n                                } else if (position == 'after') {\n                                    source = widget.insertAfter(source, destination);\n                                }\n                                triggerDragEnd(source);\n                            }\n                        }\n                    });\n                } else if (!enabled && dragging) {\n                    dragging.destroy();\n                    this.dragging = null;\n                }\n            },\n            _tempPreventNavigation: function (node) {\n                node.on(CLICK + NS + TEMP_NS, function (ev) {\n                    ev.preventDefault();\n                    node.off(CLICK + NS + TEMP_NS);\n                });\n            },\n            _hintText: function (node) {\n                return this.templates.dragClue({\n                    item: this.dataItem(node),\n                    treeview: this.options\n                });\n            },\n            _templates: function () {\n                var that = this, options = that.options, fieldAccessor = proxy(that._fieldAccessor, that);\n                if (options.template && typeof options.template == STRING) {\n                    options.template = template(options.template);\n                } else if (!options.template) {\n                    options.template = templateNoWith('# var text = ' + fieldAccessor('text') + '(data.item); #' + '# if (typeof data.item.encoded != \\'undefined\\' && data.item.encoded === false) {#' + '#= text #' + '# } else { #' + '#: text #' + '# } #');\n                }\n                that._checkboxes();\n                that.templates = {\n                    setAttributes: function (item) {\n                        var result = '';\n                        var attributes = item.attr || {};\n                        for (var attr in attributes) {\n                            if (attributes.hasOwnProperty(attr) && attr !== 'class') {\n                                result += attr + '=\"' + attributes[attr] + '\" ';\n                            }\n                        }\n                        return result;\n                    },\n                    wrapperCssClass: function (group, item) {\n                        var result = 'k-item', index = item.index;\n                        if (group.firstLevel && index === 0) {\n                            result += ' k-first';\n                        }\n                        if (index == group.length - 1) {\n                            result += ' k-last';\n                        }\n                        return result;\n                    },\n                    cssClass: function (group, item) {\n                        var result = '', index = item.index, groupLength = group.length - 1;\n                        if (group.firstLevel && index === 0) {\n                            result += 'k-top ';\n                        }\n                        if (index === 0 && index != groupLength) {\n                            result += 'k-top';\n                        } else if (index == groupLength) {\n                            result += 'k-bot';\n                        } else {\n                            result += 'k-mid';\n                        }\n                        return result;\n                    },\n                    textClass: function (item, isLink) {\n                        var result = 'k-in';\n                        if (isLink) {\n                            result += ' k-link';\n                        }\n                        if (item.enabled === false) {\n                            result += ' k-state-disabled';\n                        }\n                        if (item.selected === true) {\n                            result += ' k-state-selected';\n                        }\n                        return result;\n                    },\n                    toggleButtonClass: function (item) {\n                        var result = 'k-icon';\n                        if (item.expanded !== true) {\n                            result += ' k-i-expand';\n                        } else {\n                            result += ' k-i-collapse';\n                        }\n                        return result;\n                    },\n                    groupAttributes: function (group) {\n                        var attributes = '';\n                        if (!group.firstLevel) {\n                            attributes = 'role=\\'group\\'';\n                        }\n                        return attributes + (group.expanded !== true ? ' style=\\'display:none\\'' : '');\n                    },\n                    groupCssClass: function (group) {\n                        var cssClass = 'k-group';\n                        if (group.firstLevel) {\n                            cssClass += ' k-treeview-lines';\n                        }\n                        return cssClass;\n                    },\n                    dragClue: templateNoWith('#= data.treeview.template(data) #'),\n                    group: templateNoWith('<ul class=\\'#= data.r.groupCssClass(data.group) #\\'#= data.r.groupAttributes(data.group) #>' + '#= data.renderItems(data) #' + '</ul>'),\n                    itemContent: templateNoWith('# var imageUrl = ' + fieldAccessor('imageUrl') + '(data.item); #' + '# var spriteCssClass = ' + fieldAccessor('spriteCssClass') + '(data.item); #' + '# if (imageUrl) { #' + '<img class=\\'k-image\\' alt=\\'\\' src=\\'#= imageUrl #\\'>' + '# } #' + '# if (spriteCssClass) { #' + '<span class=\\'k-sprite #= spriteCssClass #\\' />' + '# } #' + '#= data.treeview.template(data) #'),\n                    itemElement: templateNoWith('# var item = data.item, r = data.r; #' + '# var url = ' + fieldAccessor('url') + '(item); #' + '<div class=\\'#= r.cssClass(data.group, item) #\\'>' + '# if (item.hasChildren) { #' + '<span class=\\'#= r.toggleButtonClass(item) #\\'/>' + '# } #' + '# if (data.treeview.checkboxes) { #' + '<span class=\\'k-checkbox-wrapper\\' role=\\'presentation\\'>' + '#= data.treeview.checkboxes.template(data) #' + '</span>' + '# } #' + '# var tag = url ? \\'a\\' : \\'span\\'; #' + '# var textAttr = url ? \\' href=\\\\\\'\\' + url + \\'\\\\\\'\\' : \\'\\'; #' + '<#=tag# class=\\'#= r.textClass(item, !!url) #\\'#= textAttr #>' + '#= r.itemContent(data) #' + '</#=tag#>' + '</div>'),\n                    item: templateNoWith('# var item = data.item, r = data.r; #' + '<li role=\\'treeitem\\' class=\\'#= r.wrapperCssClass(data.group, item) #\\'' + kendo.attr('uid') + '=\\'#= item.uid #\\' ' + '#= r.setAttributes(item.toJSON ? item.toJSON() : item) # ' + '# if (data.treeview.checkboxes) { #' + 'aria-checked=\\'#= item.checked ? \"true\" : \"false\" #\\' ' + '# } #' + 'aria-selected=\\'#= item.selected ? \"true\" : \"false\" #\\' ' + '#=item.enabled === false ? \"aria-disabled=\\'true\\'\" : \\'\\'#' + 'aria-expanded=\\'#= item.expanded ? \"true\" : \"false\" #\\' ' + 'data-expanded=\\'#= item.expanded ? \"true\" : \"false\" #\\' ' + '>' + '#= r.itemElement(data) #' + '</li>'),\n                    loading: templateNoWith('<div class=\\'k-icon k-i-loading\\' /> #: data.messages.loading #'),\n                    retry: templateNoWith('#: data.messages.requestFailed # ' + '<button class=\\'k-button k-request-retry\\'>#: data.messages.retry #</button>')\n                };\n            },\n            items: function () {\n                return this.element.find('.k-item > div:first-child');\n            },\n            setDataSource: function (dataSource) {\n                var options = this.options;\n                options.dataSource = dataSource;\n                this._dataSourceUids = {};\n                this._dataSource();\n                if (options.checkboxes && options.checkboxes.checkChildren) {\n                    this.dataSource.one('change', $.proxy(this.updateIndeterminate, this, null));\n                }\n                if (this.options.autoBind) {\n                    this.dataSource.fetch();\n                }\n            },\n            _bindDataSource: function () {\n                this._refreshHandler = proxy(this.refresh, this);\n                this._errorHandler = proxy(this._error, this);\n                this.dataSource.bind(CHANGE, this._refreshHandler);\n                this.dataSource.bind(ERROR, this._errorHandler);\n            },\n            _unbindDataSource: function () {\n                var dataSource = this.dataSource;\n                if (dataSource) {\n                    dataSource.unbind(CHANGE, this._refreshHandler);\n                    dataSource.unbind(ERROR, this._errorHandler);\n                }\n            },\n            _dataSource: function (silentRead) {\n                var that = this, options = that.options, dataSource = options.dataSource;\n                function recursiveRead(data) {\n                    for (var i = 0; i < data.length; i++) {\n                        data[i]._initChildren();\n                        data[i].children.fetch();\n                        recursiveRead(data[i].children.view());\n                    }\n                }\n                dataSource = isArray(dataSource) ? { data: dataSource } : dataSource;\n                that._unbindDataSource();\n                if (!dataSource.fields) {\n                    dataSource.fields = [\n                        { field: 'text' },\n                        { field: 'url' },\n                        { field: 'spriteCssClass' },\n                        { field: 'imageUrl' }\n                    ];\n                }\n                that.dataSource = dataSource = HierarchicalDataSource.create(dataSource);\n                if (silentRead) {\n                    dataSource.fetch();\n                    recursiveRead(dataSource.view());\n                }\n                that._bindDataSource();\n            },\n            events: [\n                DRAGSTART,\n                DRAG,\n                DROP,\n                DRAGEND,\n                DATABOUND,\n                EXPAND,\n                COLLAPSE,\n                SELECT,\n                CHANGE,\n                NAVIGATE,\n                CHECK\n            ],\n            options: {\n                name: 'TreeView',\n                dataSource: {},\n                animation: {\n                    expand: {\n                        effects: 'expand:vertical',\n                        duration: 200\n                    },\n                    collapse: { duration: 100 }\n                },\n                messages: {\n                    loading: 'Loading...',\n                    requestFailed: 'Request failed.',\n                    retry: 'Retry'\n                },\n                dragAndDrop: false,\n                checkboxes: false,\n                autoBind: true,\n                autoScroll: false,\n                loadOnDemand: true,\n                template: '',\n                dataTextField: null\n            },\n            _accessors: function () {\n                var that = this, options = that.options, i, field, textField, element = that.element;\n                for (i in bindings) {\n                    field = options[bindings[i]];\n                    textField = element.attr(kendo.attr(i + '-field'));\n                    if (!field && textField) {\n                        field = textField;\n                    }\n                    if (!field) {\n                        field = i;\n                    }\n                    if (!isArray(field)) {\n                        field = [field];\n                    }\n                    options[bindings[i]] = field;\n                }\n            },\n            _fieldAccessor: function (fieldName) {\n                var fieldBindings = this.options[bindings[fieldName]], count = fieldBindings.length, result = '(function(item) {';\n                if (count === 0) {\n                    result += 'return item[\\'' + fieldName + '\\'];';\n                } else {\n                    result += 'var levels = [' + $.map(fieldBindings, function (x) {\n                        return 'function(d){ return ' + kendo.expr(x) + '}';\n                    }).join(',') + '];';\n                    result += 'return levels[Math.min(item.level(), ' + count + '-1)](item)';\n                }\n                result += '})';\n                return result;\n            },\n            setOptions: function (options) {\n                Widget.fn.setOptions.call(this, options);\n                this._animation();\n                this._dragging();\n                this._templates();\n            },\n            _trigger: function (eventName, node) {\n                return this.trigger(eventName, { node: node.closest(NODE)[0] });\n            },\n            _setChecked: function (datasource, value) {\n                if (!datasource || !$.isFunction(datasource.view)) {\n                    return;\n                }\n                for (var i = 0, nodes = datasource.view(); i < nodes.length; i++) {\n                    if (nodes[i].enabled !== false) {\n                        this._setCheckedValue(nodes[i], value);\n                    }\n                    if (nodes[i].children) {\n                        this._setChecked(nodes[i].children, value);\n                    }\n                }\n            },\n            _setCheckedValue: function (node, value) {\n                node[CHECKED] = value;\n            },\n            _setIndeterminate: function (node) {\n                var group = subGroup(node), siblings, length, all = true, i;\n                if (!group.length) {\n                    return;\n                }\n                siblings = checkboxes(group.children());\n                length = siblings.length;\n                if (!length) {\n                    return;\n                } else if (length > 1) {\n                    for (i = 1; i < length; i++) {\n                        if (siblings[i].checked != siblings[i - 1].checked || siblings[i].indeterminate || siblings[i - 1].indeterminate) {\n                            all = false;\n                            break;\n                        }\n                    }\n                } else {\n                    all = !siblings[0].indeterminate;\n                }\n                node.attr(ARIACHECKED, all ? siblings[0].checked : 'mixed');\n                return checkboxes(node).data(INDETERMINATE, !all).prop(INDETERMINATE, !all).prop(CHECKED, all && siblings[0].checked);\n            },\n            updateIndeterminate: function (node) {\n                node = node || this.wrapper;\n                var subnodes = subGroup(node).children();\n                var i;\n                var checkbox;\n                var dataItem;\n                if (subnodes.length) {\n                    for (i = 0; i < subnodes.length; i++) {\n                        this.updateIndeterminate(subnodes.eq(i));\n                    }\n                    if (node.is('.k-treeview')) {\n                        return;\n                    }\n                    checkbox = this._setIndeterminate(node);\n                    dataItem = this.dataItem(node);\n                    if (checkbox && checkbox.prop(CHECKED)) {\n                        dataItem.checked = true;\n                    } else {\n                        if (dataItem) {\n                            delete dataItem.checked;\n                        }\n                    }\n                }\n            },\n            _bubbleIndeterminate: function (node, skipDownward) {\n                if (!node.length) {\n                    return;\n                }\n                if (!skipDownward) {\n                    this.updateIndeterminate(node);\n                }\n                var parentNode = this.parent(node), checkbox;\n                if (parentNode.length) {\n                    this._setIndeterminate(parentNode);\n                    checkbox = parentNode.children('div').find('.k-checkbox-wrapper input[type=checkbox]');\n                    this._skip = true;\n                    if (checkbox.prop(INDETERMINATE) === false) {\n                        this.dataItem(parentNode).set(CHECKED, checkbox.prop(CHECKED));\n                    } else {\n                        this.dataItem(parentNode).set(CHECKED, false);\n                    }\n                    this._skip = false;\n                    this._bubbleIndeterminate(parentNode, true);\n                }\n            },\n            _checkboxChange: function (e) {\n                var checkbox = $(e.target);\n                var isChecked = checkbox.prop(CHECKED);\n                var node = checkbox.closest(NODE);\n                var dataItem = this.dataItem(node);\n                if (this._preventChange) {\n                    return;\n                }\n                if (dataItem.checked != isChecked) {\n                    dataItem.set(CHECKED, isChecked);\n                    node.attr(ARIACHECKED, isChecked);\n                    this._trigger(CHECK, node);\n                }\n            },\n            _toggleButtonClick: function (e) {\n                var node = $(e.currentTarget).closest(NODE);\n                if (node.is('[aria-disabled=\\'true\\']')) {\n                    return;\n                }\n                this.toggle(node);\n            },\n            _mousedown: function (e) {\n                var that = this;\n                var currentTarget = $(e.currentTarget);\n                var node = $(e.currentTarget).closest(NODE);\n                var browser = kendo.support.browser;\n                if (node.is('[aria-disabled=\\'true\\']')) {\n                    return;\n                }\n                if ((browser.msie || browser.edge) && currentTarget.is(':checkbox')) {\n                    if (currentTarget.prop(INDETERMINATE)) {\n                        that._preventChange = false;\n                        currentTarget.prop(CHECKED, !currentTarget.prop(CHECKED));\n                        currentTarget.trigger(CHANGE);\n                        currentTarget.on(CLICK + NS, function (e) {\n                            e.preventDefault();\n                        });\n                        that._preventChange = true;\n                    } else {\n                        currentTarget.off(CLICK + NS);\n                        that._preventChange = false;\n                    }\n                }\n                that._clickTarget = node;\n                that.current(node);\n            },\n            _focusable: function (node) {\n                return node && node.length && node.is(':visible') && !node.find('.k-in:first').hasClass(DISABLED);\n            },\n            _focus: function () {\n                var current = this.select(), clickTarget = this._clickTarget;\n                if (kendo.support.touch) {\n                    return;\n                }\n                if (clickTarget && clickTarget.length) {\n                    current = clickTarget;\n                }\n                if (!this._focusable(current)) {\n                    current = this.current();\n                }\n                if (!this._focusable(current)) {\n                    current = this._nextVisible($());\n                }\n                this.current(current);\n            },\n            focus: function () {\n                var wrapper = this.wrapper, scrollContainer = wrapper[0], containers = [], offsets = [], documentElement = document.documentElement, i;\n                do {\n                    scrollContainer = scrollContainer.parentNode;\n                    if (scrollContainer.scrollHeight > scrollContainer.clientHeight) {\n                        containers.push(scrollContainer);\n                        offsets.push(scrollContainer.scrollTop);\n                    }\n                } while (scrollContainer != documentElement);\n                kendo.focusElement(wrapper);\n                for (i = 0; i < containers.length; i++) {\n                    containers[i].scrollTop = offsets[i];\n                }\n            },\n            _blur: function () {\n                this.current().find('.k-in:first').removeClass('k-state-focused');\n            },\n            _enabled: function (node) {\n                return !node.children('div').children('.k-in').hasClass(DISABLED);\n            },\n            parent: function (node) {\n                var wrapperRe = /\\bk-treeview\\b/, itemRe = /\\bk-item\\b/, result, skipSelf;\n                if (typeof node == STRING) {\n                    node = this.element.find(node);\n                }\n                if (!isDomElement(node)) {\n                    node = node[0];\n                }\n                skipSelf = itemRe.test(node.className);\n                do {\n                    node = node.parentNode;\n                    if (itemRe.test(node.className)) {\n                        if (skipSelf) {\n                            result = node;\n                        } else {\n                            skipSelf = true;\n                        }\n                    }\n                } while (!wrapperRe.test(node.className) && !result);\n                return $(result);\n            },\n            _nextVisible: function (node) {\n                var that = this, expanded = that._expanded(node), result;\n                function nextParent(node) {\n                    while (node.length && !node.next().length) {\n                        node = that.parent(node);\n                    }\n                    if (node.next().length) {\n                        return node.next();\n                    } else {\n                        return node;\n                    }\n                }\n                if (!node.length || !node.is(':visible')) {\n                    result = that.root.children().eq(0);\n                } else if (expanded) {\n                    result = subGroup(node).children().first();\n                    if (!result.length) {\n                        result = nextParent(node);\n                    }\n                } else {\n                    result = nextParent(node);\n                }\n                return result;\n            },\n            _previousVisible: function (node) {\n                var that = this, lastChild, result;\n                if (!node.length || node.prev().length) {\n                    if (node.length) {\n                        result = node.prev();\n                    } else {\n                        result = that.root.children().last();\n                    }\n                    while (that._expanded(result)) {\n                        lastChild = subGroup(result).children().last();\n                        if (!lastChild.length) {\n                            break;\n                        }\n                        result = lastChild;\n                    }\n                } else {\n                    result = that.parent(node) || node;\n                }\n                return result;\n            },\n            _keydown: function (e) {\n                var that = this, key = e.keyCode, target, focused = that.current(), expanded = that._expanded(focused), checkbox = focused.find('.k-checkbox-wrapper:first :checkbox'), rtl = kendo.support.isRtl(that.element);\n                if (e.target != e.currentTarget) {\n                    return;\n                }\n                if (!rtl && key == keys.RIGHT || rtl && key == keys.LEFT) {\n                    if (expanded) {\n                        target = that._nextVisible(focused);\n                    } else if (!focused.find('.k-in:first').hasClass(DISABLED)) {\n                        that.expand(focused);\n                    }\n                } else if (!rtl && key == keys.LEFT || rtl && key == keys.RIGHT) {\n                    if (expanded && !focused.find('.k-in:first').hasClass(DISABLED)) {\n                        that.collapse(focused);\n                    } else {\n                        target = that.parent(focused);\n                        if (!that._enabled(target)) {\n                            target = undefined;\n                        }\n                    }\n                } else if (key == keys.DOWN) {\n                    target = that._nextVisible(focused);\n                } else if (key == keys.UP) {\n                    target = that._previousVisible(focused);\n                } else if (key == keys.HOME) {\n                    target = that._nextVisible($());\n                } else if (key == keys.END) {\n                    target = that._previousVisible($());\n                } else if (key == keys.ENTER && !focused.find('.k-in:first').hasClass(DISABLED)) {\n                    if (!focused.find('.k-in:first').hasClass('k-state-selected')) {\n                        if (!that._trigger(SELECT, focused)) {\n                            that.select(focused);\n                        }\n                    }\n                } else if (key == keys.SPACEBAR && checkbox.length) {\n                    if (!focused.find('.k-in:first').hasClass(DISABLED)) {\n                        checkbox.prop(CHECKED, !checkbox.prop(CHECKED)).data(INDETERMINATE, false).prop(INDETERMINATE, false);\n                        that._checkboxChange({ target: checkbox });\n                    }\n                    target = focused;\n                }\n                if (target) {\n                    e.preventDefault();\n                    if (focused[0] != target[0]) {\n                        that._trigger(NAVIGATE, target);\n                        that.current(target);\n                    }\n                }\n            },\n            _keypress: function (e) {\n                var that = this;\n                var delay = 300;\n                var focusedNode = that.current().get(0);\n                var matchToFocus;\n                var key = e.key;\n                var isPrintable = key.length === 1;\n                if (!isPrintable) {\n                    return;\n                }\n                if (!that._match) {\n                    that._match = '';\n                }\n                that._match += key;\n                clearTimeout(that._matchTimer);\n                that._matchTimer = setTimeout(function () {\n                    that._match = '';\n                }, delay);\n                matchToFocus = focusedNode && that._matchNextByText(Array.prototype.indexOf.call(that.element.find('.k-item'), focusedNode), that._match);\n                if (!matchToFocus.length) {\n                    matchToFocus = that._matchNextByText(-1, that._match);\n                }\n                if (matchToFocus.get(0) && matchToFocus.get(0) !== focusedNode) {\n                    that._trigger(NAVIGATE, matchToFocus);\n                    that.current(matchToFocus);\n                }\n            },\n            _matchNextByText: function (startIndex, text) {\n                var element = this.element;\n                var textNodes = element.find('.k-in').filter(function (i, element) {\n                    return i > startIndex && $(element).is(':visible') && $(element).text().toLowerCase().indexOf(text) === 0;\n                });\n                return textNodes.eq(0).closest(NODE);\n            },\n            _click: function (e) {\n                var that = this, node = $(e.currentTarget), contents = nodeContents(node.closest(NODE)), href = node.attr('href'), shouldNavigate;\n                if (href) {\n                    shouldNavigate = href == '#' || href.indexOf('#' + this.element.id + '-') >= 0;\n                } else {\n                    shouldNavigate = contents.length && !contents.children().length;\n                }\n                if (shouldNavigate) {\n                    e.preventDefault();\n                }\n                if (!node.hasClass('.k-state-selected') && !that._trigger(SELECT, node)) {\n                    that.select(node);\n                }\n            },\n            _wrapper: function () {\n                var that = this, element = that.element, wrapper, root, wrapperClasses = 'k-widget k-treeview';\n                if (element.is('ul')) {\n                    wrapper = element.wrap('<div />').parent();\n                    root = element;\n                } else {\n                    wrapper = element;\n                    root = wrapper.children('ul').eq(0);\n                }\n                that.wrapper = wrapper.addClass(wrapperClasses);\n                that.root = root;\n            },\n            _getSelectedNode: function () {\n                return this.element.find('.k-state-selected').closest(NODE);\n            },\n            _group: function (item) {\n                var that = this, firstLevel = item.hasClass(KTREEVIEW), group = {\n                        firstLevel: firstLevel,\n                        expanded: firstLevel || that._expanded(item)\n                    }, groupElement = item.children('ul');\n                groupElement.addClass(that.templates.groupCssClass(group)).css('display', group.expanded ? '' : 'none');\n                that._nodes(groupElement, group);\n            },\n            _nodes: function (groupElement, groupData) {\n                var that = this, nodes = groupElement.children('li'), nodeData;\n                groupData = extend({ length: nodes.length }, groupData);\n                nodes.each(function (i, node) {\n                    node = $(node);\n                    nodeData = {\n                        index: i,\n                        expanded: that._expanded(node)\n                    };\n                    updateNodeHtml(node);\n                    that._updateNodeClasses(node, groupData, nodeData);\n                    that._group(node);\n                });\n            },\n            _checkboxes: function () {\n                var options = this.options;\n                var checkboxes = options.checkboxes;\n                var defaultTemplate;\n                if (checkboxes) {\n                    defaultTemplate = '<input type=\\'checkbox\\' tabindex=\\'-1\\' #= (item.enabled === false) ? \\'disabled\\' : \\'\\' # #= item.checked ? \\'checked\\' : \\'\\' #';\n                    if (checkboxes.name) {\n                        defaultTemplate += ' name=\\'' + checkboxes.name + '\\'';\n                    }\n                    defaultTemplate += ' id=\\'_#= item.uid #\\' class=\\'k-checkbox\\' /><span class=\\'k-checkbox-label checkbox-span\\'></span>';\n                    checkboxes = extend({ template: defaultTemplate }, options.checkboxes);\n                    if (typeof checkboxes.template == STRING) {\n                        checkboxes.template = template(checkboxes.template);\n                    }\n                    options.checkboxes = checkboxes;\n                }\n            },\n            _updateNodeClasses: function (node, groupData, nodeData) {\n                var wrapper = node.children('div'), group = node.children('ul'), templates = this.templates;\n                if (node.hasClass('k-treeview')) {\n                    return;\n                }\n                nodeData = nodeData || {};\n                nodeData.expanded = typeof nodeData.expanded != UNDEFINED ? nodeData.expanded : this._expanded(node);\n                nodeData.index = typeof nodeData.index != UNDEFINED ? nodeData.index : node.index();\n                nodeData.enabled = typeof nodeData.enabled != UNDEFINED ? nodeData.enabled : !wrapper.children('.k-in').hasClass('k-state-disabled');\n                groupData = groupData || {};\n                groupData.firstLevel = typeof groupData.firstLevel != UNDEFINED ? groupData.firstLevel : node.parent().parent().hasClass(KTREEVIEW);\n                groupData.length = typeof groupData.length != UNDEFINED ? groupData.length : node.parent().children().length;\n                node.removeClass('k-first k-last').addClass(templates.wrapperCssClass(groupData, nodeData));\n                wrapper.removeClass('k-top k-mid k-bot').addClass(templates.cssClass(groupData, nodeData));\n                var textWrap = wrapper.children('.k-in');\n                var isLink = textWrap[0] && textWrap[0].nodeName.toLowerCase() == 'a';\n                textWrap.removeClass('k-in k-link k-state-default k-state-disabled').addClass(templates.textClass(nodeData, isLink));\n                if (group.length || node.attr('data-hasChildren') == 'true') {\n                    wrapper.children('.k-icon').removeClass('k-i-expand k-i-collapse').addClass(templates.toggleButtonClass(nodeData));\n                    group.addClass('k-group');\n                }\n            },\n            _processNodes: function (nodes, callback) {\n                var that = this;\n                var items = that.element.find(nodes);\n                for (var i = 0; i < items.length; i++) {\n                    callback.call(that, i, $(items[i]).closest(NODE));\n                }\n            },\n            dataItem: function (node) {\n                var uid = $(node).closest(NODE).attr(kendo.attr('uid')), dataSource = this.dataSource;\n                return dataSource && dataSource.getByUid(uid);\n            },\n            _dataItem: function (node) {\n                var uid = $(node).closest(NODE).attr(kendo.attr('uid')), dataSource = this.dataSource;\n                return dataSource && this._dataSourceUids[uid];\n            },\n            _insertNode: function (nodeData, index, parentNode, insertCallback, collapsed) {\n                var that = this, group = subGroup(parentNode), updatedGroupLength = group.children().length + 1, childrenData, groupData = {\n                        firstLevel: parentNode.hasClass(KTREEVIEW),\n                        expanded: !collapsed,\n                        length: updatedGroupLength\n                    }, node, i, item, nodeHtml = '', firstChild, lastChild, append = function (item, group) {\n                        item.appendTo(group);\n                    };\n                for (i = 0; i < nodeData.length; i++) {\n                    item = nodeData[i];\n                    item.index = index + i;\n                    nodeHtml += that._renderItem({\n                        group: groupData,\n                        item: item\n                    });\n                }\n                node = $(nodeHtml);\n                if (!node.length) {\n                    return;\n                }\n                that.angular('compile', function () {\n                    return {\n                        elements: node.get(),\n                        data: nodeData.map(function (item) {\n                            return { dataItem: item };\n                        })\n                    };\n                });\n                if (!group.length) {\n                    group = $(that._renderGroup({ group: groupData })).appendTo(parentNode);\n                }\n                insertCallback(node, group);\n                if (parentNode.hasClass('k-item')) {\n                    updateNodeHtml(parentNode);\n                    that._updateNodeClasses(parentNode, groupData, { expanded: !collapsed });\n                }\n                firstChild = node.prev().first();\n                lastChild = node.next().last();\n                that._updateNodeClasses(firstChild, {}, { expanded: firstChild.attr(kendo.attr('expanded')) == 'true' });\n                that._updateNodeClasses(lastChild, {}, { expanded: lastChild.attr(kendo.attr('expanded')) == 'true' });\n                for (i = 0; i < nodeData.length; i++) {\n                    item = nodeData[i];\n                    if (item.hasChildren) {\n                        childrenData = item.children.data();\n                        if (childrenData.length) {\n                            that._insertNode(childrenData, item.index, node.eq(i), append, !item.expanded);\n                        }\n                    }\n                }\n                return node;\n            },\n            _updateNodes: function (items, field) {\n                var that = this;\n                var i, node, nodeWrapper, item, isChecked, isCollapsed;\n                var context = {\n                    treeview: that.options,\n                    item: item\n                };\n                var render = field != 'expanded' && field != 'checked';\n                function setCheckedState(root, state) {\n                    if (root.is('.k-group')) {\n                        root.find('.k-item:not([aria-disabled])').attr(ARIACHECKED, state);\n                    }\n                    root.find('.k-checkbox-wrapper input[type=checkbox]:not([disabled])').prop(CHECKED, state).data(INDETERMINATE, false).prop(INDETERMINATE, false);\n                }\n                if (field == 'selected') {\n                    item = items[0];\n                    node = that.findByUid(item.uid).find('.k-in:first').removeClass('k-state-hover').toggleClass('k-state-selected', item[field]).end();\n                    if (item[field]) {\n                        that.current(node);\n                    }\n                    node.attr(ARIASELECTED, !!item[field]);\n                } else {\n                    var elements = $.map(items, function (item) {\n                        return that.findByUid(item.uid).children('div');\n                    });\n                    if (render) {\n                        that.angular('cleanup', function () {\n                            return { elements: elements };\n                        });\n                    }\n                    for (i = 0; i < items.length; i++) {\n                        context.item = item = items[i];\n                        nodeWrapper = elements[i];\n                        node = nodeWrapper.parent();\n                        if (render) {\n                            nodeWrapper.children('.k-in').html(that.templates.itemContent(context));\n                        }\n                        if (field == CHECKED) {\n                            isChecked = item[field];\n                            setCheckedState(nodeWrapper, isChecked);\n                            node.attr(ARIACHECKED, isChecked);\n                            if (that.options.checkboxes.checkChildren) {\n                                setCheckedState(node.children('.k-group'), isChecked);\n                                that._setChecked(item.children, isChecked);\n                                that._bubbleIndeterminate(node);\n                            }\n                        } else if (field == 'expanded') {\n                            that._toggle(node, item, item[field]);\n                        } else if (field == 'enabled') {\n                            node.find('.k-checkbox-wrapper input[type=checkbox]').prop('disabled', !item[field]);\n                            isCollapsed = !nodeContents(node).is(VISIBLE);\n                            node.removeAttr(ARIADISABLED);\n                            if (!item[field]) {\n                                if (item.selected) {\n                                    item.set('selected', false);\n                                }\n                                if (item.expanded) {\n                                    item.set('expanded', false);\n                                }\n                                isCollapsed = true;\n                                node.attr(ARIASELECTED, false).attr(ARIADISABLED, true);\n                            }\n                            that._updateNodeClasses(node, {}, {\n                                enabled: item[field],\n                                expanded: !isCollapsed\n                            });\n                        }\n                        if (nodeWrapper.length) {\n                            this.trigger('itemChange', {\n                                item: nodeWrapper,\n                                data: item,\n                                ns: ui\n                            });\n                        }\n                    }\n                    if (render) {\n                        that.angular('compile', function () {\n                            return {\n                                elements: elements,\n                                data: $.map(items, function (item) {\n                                    return [{ dataItem: item }];\n                                })\n                            };\n                        });\n                    }\n                }\n            },\n            _appendItems: function (index, items, parentNode) {\n                var group = subGroup(parentNode);\n                var children = group.children();\n                var collapsed = !this._expanded(parentNode);\n                if (this.element === parentNode) {\n                    var dataItems = this.dataSource.data();\n                    var viewItems = this.dataSource.view();\n                    var rootItems = viewItems.length < dataItems.length ? viewItems : dataItems;\n                    index = rootItems.indexOf(items[0]);\n                } else if (items.length) {\n                    index = items[0].parent().indexOf(items[0]);\n                }\n                if (typeof index == UNDEFINED) {\n                    index = children.length;\n                }\n                this._insertNode(items, index, parentNode, function (item, group) {\n                    if (index >= children.length) {\n                        item.appendTo(group);\n                    } else {\n                        item.insertBefore(children.eq(index));\n                    }\n                }, collapsed);\n                if (!collapsed) {\n                    this._updateNodeClasses(parentNode, {}, { expanded: !collapsed });\n                    subGroup(parentNode).css('display', 'block');\n                }\n            },\n            _refreshChildren: function (parentNode, items, index) {\n                var i, children, child;\n                var options = this.options;\n                var loadOnDemand = options.loadOnDemand;\n                var checkChildren = options.checkboxes && options.checkboxes.checkChildren;\n                subGroup(parentNode).empty();\n                if (!items.length) {\n                    updateNodeHtml(parentNode);\n                } else {\n                    this._appendItems(index, items, parentNode);\n                    children = subGroup(parentNode).children();\n                    if (loadOnDemand && checkChildren) {\n                        this._bubbleIndeterminate(children.last());\n                    }\n                    for (i = 0; i < children.length; i++) {\n                        child = children.eq(i);\n                        this.trigger('itemChange', {\n                            item: child.children('div'),\n                            data: items[i],\n                            ns: ui\n                        });\n                    }\n                }\n            },\n            _refreshRoot: function (items) {\n                var groupHtml = this._renderGroup({\n                    items: items,\n                    group: {\n                        firstLevel: true,\n                        expanded: true\n                    }\n                });\n                if (this.root.length) {\n                    this._angularItems('cleanup');\n                    var group = $(groupHtml);\n                    this.root.attr('class', group.attr('class')).html(group.html());\n                } else {\n                    this.root = this.wrapper.html(groupHtml).children('ul');\n                }\n                var elements = this.root.children('.k-item');\n                for (var i = 0; i < items.length; i++) {\n                    this.trigger('itemChange', {\n                        item: elements.eq(i),\n                        data: items[i],\n                        ns: ui\n                    });\n                }\n                this._angularItems('compile');\n            },\n            refresh: function (e) {\n                var node = e.node;\n                var action = e.action;\n                var items = e.items;\n                var parentNode = this.wrapper;\n                var options = this.options;\n                var loadOnDemand = options.loadOnDemand;\n                var checkChildren = options.checkboxes && options.checkboxes.checkChildren;\n                var i;\n                if (this._skip) {\n                    return;\n                }\n                for (i = 0; i < items.length; i++) {\n                    this._dataSourceUids[items[i].uid] = items[i];\n                }\n                if (e.field) {\n                    if (!items[0] || !items[0].level) {\n                        return;\n                    }\n                    return this._updateNodes(items, e.field);\n                }\n                if (node) {\n                    parentNode = this.findByUid(node.uid);\n                    this._progress(parentNode, false);\n                }\n                if (checkChildren && action != 'remove') {\n                    var bubble = false;\n                    for (i = 0; i < items.length; i++) {\n                        if ('checked' in items[i]) {\n                            bubble = true;\n                            break;\n                        }\n                    }\n                    if (!bubble && node && node.checked) {\n                        for (i = 0; i < items.length; i++) {\n                            items[i].checked = true;\n                        }\n                    }\n                }\n                if (action == 'add') {\n                    this._appendItems(e.index, items, parentNode);\n                } else if (action == 'remove') {\n                    this._remove(this.findByUid(items[0].uid), false);\n                } else if (action == 'itemchange') {\n                    this._updateNodes(items);\n                } else if (action == 'itemloaded') {\n                    this._refreshChildren(parentNode, items, e.index);\n                } else {\n                    this._refreshRoot(items);\n                }\n                if (action != 'remove') {\n                    for (i = 0; i < items.length; i++) {\n                        if (!loadOnDemand || items[i].expanded || items[i]._loaded) {\n                            items[i].load();\n                        }\n                    }\n                }\n                this.trigger(DATABOUND, { node: node ? parentNode : undefined });\n                if (this.dataSource.filter() && this.options.checkboxes.checkChildren) {\n                    this.updateIndeterminate(parentNode);\n                }\n            },\n            _error: function (e) {\n                var node = e.node && this.findByUid(e.node.uid);\n                var retryHtml = this.templates.retry({ messages: this.options.messages });\n                if (node) {\n                    this._progress(node, false);\n                    this._expanded(node, false);\n                    nodeIcon(node).addClass('k-i-reload');\n                    e.node.loaded(false);\n                } else {\n                    this._progress(false);\n                    this.element.html(retryHtml);\n                }\n            },\n            _retryRequest: function (e) {\n                e.preventDefault();\n                this.dataSource.fetch();\n            },\n            expand: function (nodes) {\n                this._processNodes(nodes, function (index, item) {\n                    this.toggle(item, true);\n                });\n            },\n            collapse: function (nodes) {\n                this._processNodes(nodes, function (index, item) {\n                    this.toggle(item, false);\n                });\n            },\n            enable: function (nodes, enable) {\n                if (typeof nodes === 'boolean') {\n                    enable = nodes;\n                    nodes = this.items();\n                } else {\n                    enable = arguments.length == 2 ? !!enable : true;\n                }\n                this._processNodes(nodes, function (index, item) {\n                    this.dataItem(item).set('enabled', enable);\n                });\n            },\n            current: function (node) {\n                var that = this, current = that._current, element = that.element, id = that._ariaId;\n                if (arguments.length > 0 && node && node.length) {\n                    if (current) {\n                        if (current[0].id === id) {\n                            current.removeAttr('id');\n                        }\n                        current.find('.k-in:first').removeClass('k-state-focused');\n                    }\n                    current = that._current = $(node, element).closest(NODE);\n                    current.find('.k-in:first').addClass('k-state-focused');\n                    id = current[0].id || id;\n                    if (id) {\n                        that.wrapper.removeAttr('aria-activedescendant');\n                        current.attr('id', id);\n                        that.wrapper.attr('aria-activedescendant', id);\n                    }\n                    return;\n                }\n                if (!current) {\n                    current = that._nextVisible($());\n                }\n                return current;\n            },\n            select: function (node) {\n                var that = this, element = that.element;\n                if (!arguments.length) {\n                    return element.find('.k-state-selected').closest(NODE);\n                }\n                node = $(node, element).closest(NODE);\n                element.find('.k-state-selected').each(function () {\n                    var dataItem = that.dataItem(this);\n                    if (dataItem) {\n                        dataItem.set('selected', false);\n                        delete dataItem.selected;\n                    } else {\n                        $(this).removeClass('k-state-selected');\n                    }\n                });\n                if (node.length) {\n                    that.dataItem(node).set('selected', true);\n                    that._clickTarget = node;\n                }\n                that.trigger(CHANGE);\n            },\n            _toggle: function (node, dataItem, expand) {\n                var options = this.options;\n                var contents = nodeContents(node);\n                var direction = expand ? 'expand' : 'collapse';\n                var loaded;\n                if (contents.data('animating')) {\n                    return;\n                }\n                loaded = dataItem && dataItem.loaded();\n                if (expand && !loaded) {\n                    if (options.loadOnDemand) {\n                        this._progress(node, true);\n                    }\n                    contents.remove();\n                    dataItem.load();\n                } else {\n                    this._updateNodeClasses(node, {}, { expanded: expand });\n                    if (!expand) {\n                        contents.css('height', contents.height()).css('height');\n                    }\n                    contents.kendoStop(true, true).kendoAnimate(extend({ reset: true }, options.animation[direction], {\n                        complete: function () {\n                            if (expand) {\n                                contents.css('height', '');\n                            }\n                        }\n                    }));\n                }\n            },\n            toggle: function (node, expand) {\n                node = $(node);\n                if (!nodeIcon(node).is('.k-i-expand, .k-i-collapse')) {\n                    return;\n                }\n                if (arguments.length == 1) {\n                    expand = !this._expanded(node);\n                }\n                this._expanded(node, expand);\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that.wrapper.off(NS);\n                that.wrapper.find('.k-checkbox-wrapper :checkbox').off(NS);\n                that._unbindDataSource();\n                if (that.dragging) {\n                    that.dragging.destroy();\n                }\n                that._dataSourceUids = {};\n                kendo.destroy(that.element);\n                that.root = that.wrapper = that.element = null;\n            },\n            _expanded: function (node, value, force) {\n                var expandedAttr = kendo.attr('expanded');\n                var dataItem;\n                var expanded = value;\n                var direction = expanded ? 'expand' : 'collapse';\n                if (arguments.length == 1) {\n                    dataItem = this._dataItem(node);\n                    return node.attr(expandedAttr) === 'true' || dataItem && dataItem.expanded;\n                }\n                dataItem = this.dataItem(node);\n                if (nodeContents(node).data('animating')) {\n                    return;\n                }\n                if (force || !this._trigger(direction, node)) {\n                    if (expanded) {\n                        node.attr(expandedAttr, 'true');\n                        node.attr('aria-expanded', 'true');\n                    } else {\n                        node.removeAttr(expandedAttr);\n                        node.attr('aria-expanded', 'false');\n                    }\n                    if (dataItem) {\n                        dataItem.set('expanded', expanded);\n                        expanded = dataItem.expanded;\n                    }\n                }\n            },\n            _progress: function (node, showProgress) {\n                var element = this.element;\n                var loadingText = this.templates.loading({ messages: this.options.messages });\n                if (arguments.length == 1) {\n                    showProgress = node;\n                    if (showProgress) {\n                        element.html(loadingText);\n                    } else {\n                        element.empty();\n                    }\n                } else {\n                    nodeIcon(node).toggleClass('k-i-loading', showProgress).removeClass('k-i-reload');\n                }\n            },\n            text: function (node, text) {\n                var dataItem = this.dataItem(node), fieldBindings = this.options[bindings.text], level = dataItem.level(), length = fieldBindings.length, field = fieldBindings[Math.min(level, length - 1)];\n                if (text) {\n                    dataItem.set(field, text);\n                } else {\n                    return dataItem[field];\n                }\n            },\n            _objectOrSelf: function (node) {\n                return $(node).closest('[data-role=treeview]').data('kendoTreeView') || this;\n            },\n            _dataSourceMove: function (nodeData, group, parentNode, callback) {\n                var referenceDataItem, destTreeview = this._objectOrSelf(parentNode || group), destDataSource = destTreeview.dataSource;\n                var loadPromise = $.Deferred().resolve().promise();\n                if (parentNode && parentNode[0] != destTreeview.element[0]) {\n                    referenceDataItem = destTreeview.dataItem(parentNode);\n                    if (!referenceDataItem.loaded()) {\n                        destTreeview._progress(parentNode, true);\n                        loadPromise = referenceDataItem.load();\n                    }\n                    if (parentNode != this.root) {\n                        destDataSource = referenceDataItem.children;\n                        if (!destDataSource || !(destDataSource instanceof HierarchicalDataSource)) {\n                            referenceDataItem._initChildren();\n                            referenceDataItem.loaded(true);\n                            destDataSource = referenceDataItem.children;\n                        }\n                    }\n                }\n                nodeData = this._toObservableData(nodeData);\n                return callback.call(destTreeview, destDataSource, nodeData, loadPromise);\n            },\n            _toObservableData: function (node) {\n                var dataItem = node, dataSource, uid;\n                if (isJQueryInstance(node) || isDomElement(node)) {\n                    dataSource = this._objectOrSelf(node).dataSource;\n                    uid = $(node).attr(kendo.attr('uid'));\n                    dataItem = dataSource.getByUid(uid);\n                    if (dataItem) {\n                        dataItem = dataSource.remove(dataItem);\n                    }\n                }\n                return dataItem;\n            },\n            _insert: function (data, model, index) {\n                if (!(model instanceof kendo.data.ObservableArray)) {\n                    if (!isArray(model)) {\n                        model = [model];\n                    }\n                } else {\n                    model = model.toJSON();\n                }\n                var parentNode = data.parent();\n                if (parentNode && parentNode._initChildren) {\n                    parentNode.hasChildren = true;\n                    parentNode._initChildren();\n                }\n                data.splice.apply(data, [\n                    index,\n                    0\n                ].concat(model));\n                return this.findByUid(data[index].uid);\n            },\n            insertAfter: insertAction(1),\n            insertBefore: insertAction(0),\n            append: function (nodeData, parentNode, success) {\n                var group = this.root;\n                if (parentNode && nodeData instanceof jQuery && parentNode[0] === nodeData[0]) {\n                    return;\n                }\n                parentNode = parentNode && parentNode.length ? parentNode : null;\n                if (parentNode) {\n                    group = subGroup(parentNode);\n                }\n                return this._dataSourceMove(nodeData, group, parentNode, function (dataSource, model, loadModel) {\n                    var inserted;\n                    var that = this;\n                    function add() {\n                        if (parentNode) {\n                            that._expanded(parentNode, true, true);\n                        }\n                        var data = dataSource.data(), index = Math.max(data.length, 0);\n                        return that._insert(data, model, index);\n                    }\n                    loadModel.done(function () {\n                        inserted = add();\n                        success = success || $.noop;\n                        success(inserted);\n                    });\n                    return inserted || null;\n                });\n            },\n            _remove: function (node, keepData) {\n                var that = this, parentNode, prevSibling, nextSibling;\n                node = $(node, that.element);\n                this.angular('cleanup', function () {\n                    return { elements: node.get() };\n                });\n                parentNode = node.parent().parent();\n                prevSibling = node.prev();\n                nextSibling = node.next();\n                node[keepData ? 'detach' : 'remove']();\n                if (parentNode.hasClass('k-item')) {\n                    updateNodeHtml(parentNode);\n                    that._updateNodeClasses(parentNode);\n                }\n                that._updateNodeClasses(prevSibling);\n                that._updateNodeClasses(nextSibling);\n                return node;\n            },\n            remove: function (node) {\n                var dataItem = this.dataItem(node);\n                if (dataItem) {\n                    this.dataSource.remove(dataItem);\n                }\n            },\n            detach: function (node) {\n                return this._remove(node, true);\n            },\n            findByText: function (text) {\n                return $(this.element).find('.k-in').filter(function (i, element) {\n                    return $(element).text() == text;\n                }).closest(NODE);\n            },\n            findByUid: function (uid) {\n                var items = this.element.find('.k-item');\n                var uidAttr = kendo.attr('uid');\n                var result;\n                for (var i = 0; i < items.length; i++) {\n                    if (items[i].getAttribute(uidAttr) == uid) {\n                        result = items[i];\n                        break;\n                    }\n                }\n                return $(result);\n            },\n            expandPath: function (path, complete) {\n                var treeview = this;\n                var nodeIds = path.slice(0);\n                var callback = complete || $.noop;\n                function proceed() {\n                    nodeIds.shift();\n                    if (nodeIds.length) {\n                        expand(nodeIds[0]).then(proceed);\n                    } else {\n                        callback.call(treeview);\n                    }\n                }\n                function expand(id) {\n                    var result = $.Deferred();\n                    var node = treeview.dataSource.get(id);\n                    if (node) {\n                        if (node.loaded()) {\n                            node.set('expanded', true);\n                            result.resolve();\n                        } else {\n                            treeview._progress(treeview.findByUid(node.uid), true);\n                            node.load().then(function () {\n                                node.set('expanded', true);\n                                result.resolve();\n                            });\n                        }\n                    } else {\n                        result.resolve();\n                    }\n                    return result.promise();\n                }\n                expand(nodeIds[0]).then(proceed);\n            },\n            _parentIds: function (node) {\n                var parent = node && node.parentNode();\n                var parents = [];\n                while (parent && parent.parentNode) {\n                    parents.unshift(parent.id);\n                    parent = parent.parentNode();\n                }\n                return parents;\n            },\n            expandTo: function (node) {\n                if (!(node instanceof kendo.data.Node)) {\n                    node = this.dataSource.get(node);\n                }\n                var parents = this._parentIds(node);\n                this.expandPath(parents);\n            },\n            _renderItem: function (options) {\n                if (!options.group) {\n                    options.group = {};\n                }\n                options.treeview = this.options;\n                options.r = this.templates;\n                return this.templates.item(options);\n            },\n            _renderGroup: function (options) {\n                var that = this;\n                options.renderItems = function (options) {\n                    var html = '', i = 0, items = options.items, len = items ? items.length : 0, group = options.group;\n                    group.length = len;\n                    for (; i < len; i++) {\n                        options.group = group;\n                        options.item = items[i];\n                        options.item.index = i;\n                        html += that._renderItem(options);\n                    }\n                    return html;\n                };\n                options.r = that.templates;\n                return that.templates.group(options);\n            }\n        });\n        ui.plugin(TreeView);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}