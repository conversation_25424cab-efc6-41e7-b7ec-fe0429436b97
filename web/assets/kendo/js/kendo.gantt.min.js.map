{"version": 3, "sources": ["kendo.gantt.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "undefined", "selector", "uid", "attr", "trimOptions", "name", "prefix", "remove", "edit", "add", "navigate", "dateCompareValidator", "input", "field", "picker", "dates", "container", "editable", "model", "filter", "widgetInstance", "ui", "parent", "data", "start", "end", "parseDate", "val", "focusTable", "table", "direct", "wrapper", "parents", "scrollPositions", "scrollableParents", "TABINDEX", "each", "index", "scrollTop", "setActive", "e", "focus", "element", "parentsUntil", "computedStyle", "getComputedStyles", "overflow", "defaultCommands", "GanttTask", "GanttDataSource", "editors", "Editor", "PopupEditor", "ResourceEditor", "<PERSON><PERSON><PERSON>", "keys", "F10", "supportsMedia", "browser", "support", "mobileOS", "Observable", "Widget", "DataSource", "ObservableObject", "ObservableArray", "Query", "isArray", "inArray", "isFunction", "proxy", "isPlainObject", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "defaultIndicatorWidth", "NS", "PERCENTAGE_FORMAT", "CLICK", "WIDTH", "STRING", "DIRECTIONS", "down", "origin", "position", "up", "ARIA_DESCENDANT", "ARIA_LABEL", "ACTIVE_CELL", "ACTIVE_OPTION", "DOT", "TASK_DELETE_CONFIRM", "DEPENDENCY_DELETE_CONFIRM", "TOGGLE_BUTTON_TEMPLATE", "template", "BUTTON_TEMPLATE", "COMMAND_BUTTON_TEMPLATE", "VIEWBUTTONTEMPLATE", "HEADER_VIEWS_TEMPLATE", "TASK_DROPDOWN_TEMPLATE", "DATERANGEEDITOR", "title", "validationRules", "fields", "validation", "message", "appendTo", "hide", "RESOURCESEDITOR", "styles", "button", "messages", "assignButton", "click", "ganttStyles", "rowHeight", "listWrapper", "list", "timelineWrapper", "timeline", "splitBarWrapper", "splitBar", "splitBarHover", "popupWrapper", "popupList", "resizeHandle", "icon", "item", "line", "buttonDelete", "buttonCancel", "buttonSave", "buttonToggle", "primary", "hovered", "selected", "focused", "gridHeader", "gridHeaderWrap", "gridContent", "tasks", "popup", "form", "editForm", "formContainer", "resourcesFormContainer", "buttonsContainer", "edit<PERSON>ield", "edit<PERSON><PERSON><PERSON>", "resourcesField", "toolbar", "headerWrapper", "footerWrapper", "expanded", "views", "viewsWrapper", "actions", "iconPlus", "iconPdf", "iconToggle", "viewButtonDefault", "viewButton", "current<PERSON>iew", "link", "pdfButton", "append<PERSON><PERSON>on", "TaskDropDown", "fn", "call", "_popup", "direction", "navigatable", "_current", "method", "find", "sibling", "removeClass", "removeAttr", "addClass", "that", "itemSelector", "appendButtonSelector", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "insertAfter", "append", "Popup", "anchor", "open", "_adjustListWidth", "animation", "on", "target", "action", "preventDefault", "trigger", "type", "aria-activedescendant", "hover", "close", "bind", "keyCode", "UP", "DOWN", "ENTER", "ESC", "computedWidth", "listOuterWidth", "getComputedStyle", "parseFloat", "mozilla", "msie", "paddingLeft", "paddingRight", "borderLeftWidth", "borderRightWidth", "css", "fontFamily", "destroy", "clearTimeout", "_focusTimeout", "off", "unbind", "createDataSource", "dataSource", "Error", "GanttDependency", "Model", "id", "predecessorId", "successorId", "GanttDependencyDataSource", "schema", "modelBase", "successors", "_dependencies", "predecessors", "dependencies", "apply", "view", "operator", "toArray", "create", "duration", "isMilestone", "_offset", "newValue", "Date", "getTime", "set", "parentId", "defaultValue", "required", "orderId", "dateCompare", "percentComplete", "min", "max", "step", "summary", "task", "children", "taskAll<PERSON><PERSON><PERSON><PERSON>", "_removeItems", "_childRemoved", "_toGanttTask", "insert", "taskSiblings", "_reorderSiblings", "_resolve<PERSON><PERSON><PERSON><PERSON><PERSON>ields", "taskParent", "task<PERSON><PERSON><PERSON><PERSON>", "taskId", "order", "_sort", "dir", "callback", "taskLevel", "level", "taskTree", "l", "update", "taskInfo", "oldValue", "offsetChildren", "parentTask", "offset", "modelChangeHandler", "sender", "_resolveSummaryStart", "_resolveSummaryEnd", "_resolveSummaryPercentComplete", "_updateSummary", "getSummaryStart", "currentMin", "_updateSummaryRecursive", "getSummaryEnd", "currentMax", "getSummaryPercentComplete", "aggregate", "average", "oldOrderId", "startIndex", "endIndex", "newIndex", "siblings", "Math", "childCount", "_createNewModel", "accept", "desktop", "date<PERSON><PERSON><PERSON>", "resources", "createButton", "editor", "format", "_buildEditTemplate", "editable<PERSON><PERSON>s", "tmpl", "settings", "Template", "templateSettings", "paramName", "popupStyles", "html", "unescape", "expr", "editTask", "_createPopupEditor", "is", "showDialog", "buttons", "eq", "buttonIndex", "currentTarget", "kendoWindow", "modal", "autoFocus", "resizable", "draggable", "visible", "deactivate", "getKendoWindow", "center", "editableWidget", "save", "className", "cancel", "editor<PERSON><PERSON><PERSON>", "userTriggered", "kendoEditable", "clearContainer", "validateOnBlur", "cycleForm", "stopPropagation", "updateInfo", "_initContainer", "_attachHandlers", "events", "_dettachHandlers", "grid", "<PERSON><PERSON><PERSON><PERSON>", "_cancelProxy", "_cancel", "_saveProxy", "_save", "row", "closest", "getByUid", "_updateModel", "dom", "resourcesEditorTitle", "resize", "_resourceGrid", "_createButtons", "Grid", "columns", "resourcesHeader", "unitsHeader", "dataItem", "valueFormat", "toString", "sortable", "filterable", "from", "unitsValidation", "values", "prop", "iconClass", "pdf", "_events", "_wrapper", "_resources", "_timeline", "_toolbar", "_footer", "_adjustDimensions", "_preventRefresh", "_selected<PERSON>iew<PERSON>ame", "_dataSource", "_assignments", "_dropDowns", "_list", "_resizable", "_scrollable", "_dataBind", "_attachEvents", "_createEditor", "notify", "autoBind", "selectable", "columnResizeHandleWidth", "assignments", "taskTemplate", "deleteTaskConfirmation", "deleteDependencyConfirmation", "deleteTaskWindowTitle", "deleteDependencyWindowTitle", "day", "week", "month", "year", "showWorkHours", "showWorkDays", "workDayStart", "workDayEnd", "workWeekStart", "workWeekEnd", "hourSpan", "snap", "listWidth", "select", "clearSelection", "_refresh<PERSON><PERSON><PERSON>", "_progress<PERSON><PERSON><PERSON>", "_error<PERSON><PERSON><PERSON>", "_dependencyRefreshHandler", "_dependencyError<PERSON>andler", "footerDropDown", "headerDropDown", "_editor", "_resizeDraggable", "_mediaQuery", "removeListener", "_mediaQuery<PERSON><PERSON>ler", "_resizeHandler", "footer", "setOptions", "<PERSON><PERSON><PERSON><PERSON>", "newOptions", "isSettings", "empty", "_setEvents", "splitBarHandleClassName", "to<PERSON><PERSON><PERSON><PERSON>", "viewsSelector", "pdfSelector", "toggleSelector", "contentSelector", "treelist", "hoveredClassName", "actionsWrap", "handler", "matches", "display", "max-width", "refresh", "_resize", "_actions", "ns", "prepend", "matchMedia", "addListener", "toggleClass", "focusedViewIndex", "_<PERSON><PERSON>iew", "RIGHT", "LEFT", "hasClass", "SPACEBAR", "altKey", "blur", "_viewByIndex", "toLowerCase", "saveAsPDF", "relatedTarget", "_createButton", "command", "commandName", "split", "listSelector", "timelineSelector", "splitBarSelector", "toolbarHeight", "footerHeight", "totalHeight", "totalWidth", "splitBarWidth", "treeListWidth", "_scrollTo", "scrollTarget", "scrollIntoView", "content", "actionsSelector", "actionMessages", "firstSlot", "_timeSlots", "_createTask", "effects", "column", "toggleButtons", "restoreFocus", "_cachedCurrent", "_createResourceEditor", "GanttList", "_navigatable", "cell", "_updateTask", "selection", "oldWidth", "newWidth", "currentViewSelector", "GanttTimeline", "viewName", "resizeStart", "dependency", "predecessor", "successor", "_createDependency", "removeTask", "removeDependency", "_requestStart", "_error", "refreshDependencies", "dataTextField", "dataColorField", "dataFormatField", "_assignments<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataTaskIdField", "dataResourceIdField", "dataValueField", "_createPopupButton", "cancelTask", "saveTask", "_onDialogClose", "assignmentsModel", "Units", "_resourceEditor", "_wrapResourceData", "_updateAssignments", "range", "_render", "_renderDependencies", "date", "_scrollToDate", "_modelFromElement", "setDataSource", "_setDataSource", "fetch", "setDependenciesDataSource", "items", "assignment", "resource", "j", "<PERSON><PERSON><PERSON><PERSON>", "resourceId", "hasMatch", "_updateAssignment", "splice", "_removeAssignment", "shift", "_createAssignment", "sync", "taskCopy", "toJSON", "_editTask", "_syncDataSource", "resourceValueField", "_taskConfirm", "_removeTask", "_scrollToUid", "_preventDependencyRefresh", "resourceValue", "_dependencyConfirm", "_removeDependency", "_removeTaskDependencies", "_removeTaskAssignments", "_confirm", "confirmation", "scrollToUid", "cachedUid", "cachedIndex", "_progress", "_assignResources", "groupAssigments", "group", "assigments", "applyTaskResource", "setter", "wrapTask", "resourcedId", "formatedValue", "color", "result", "taskAssignments", "valuePerResource", "toggle", "progress", "timelineWidth", "timelineScroll", "treeListWrapper", "kendoResizable", "orientation", "handle", "scrollLeft", "delta", "x", "initialDelta", "isRtl", "_renderCurrentTime", "headerSelector", "<PERSON><PERSON><PERSON>er", "timelineContent", "treeList<PERSON><PERSON>er", "treeListContent", "wheelDeltaY", "one", "cellIndex", "headerTable", "header", "contentTable", "tables", "expandState", "collapse", "expand", "scroll", "reverse", "scrollVertical", "_rowHeight", "moveVertical", "subling", "moveHorizontal", "toggleExpandedState", "deleteAction", "selectedTask", "isInput", "ctrl<PERSON>ey", "setTimeout", "itemToFocus", "focusableItems", "_getToolbarItems", "idx", "shift<PERSON>ey", "TAB", "DELETE", "isCell", "_startEditHandler", "tagName", "concat", "activeElement", "_activeElement", "promises", "when", "done", "_adjustHeight", "PDFMixin", "_drawPDF", "listClass", "clone", "_drawPDFShadow", "avoidLinks", "plugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,eACH,aACA,cACA,eACA,kBACA,mBACA,uBACA,aACA,aACDD,IACL,WAmwFE,MAnvFC,UAAUE,EAAGkF,GA+HV,QAASC,GAASC,GACd,MAAO,IAAMlD,EAAMmD,KAAK,QAAUD,EAAM,KAAQA,EAAM,KAAQ,KAElE,QAASE,GAAY7B,GAOjB,aANOA,GAAQ8B,WACR9B,GAAQ+B,aACR/B,GAAQgC,aACRhC,GAAQiC,WACRjC,GAAQkC,UACRlC,GAAQmC,SACRnC,EAEX,QAASoC,GAAqBC,GAA9B,GAEYC,GACAC,EACAC,EACAC,EACAC,EACAC,CANR,IAAIN,EAAMO,OAAO,4BAA4BnF,OAAQ,CAOjD,IANI6E,EAAQD,EAAMT,KAAK,QACnBW,EAAS9D,EAAMoE,eAAeR,EAAO5D,EAAMqE,IAC3CN,KACAC,EAAYJ,EAGTI,IAAcjE,SAAWkE,GAC5BD,EAAYA,EAAUM,SACtBL,EAAWD,EAAUO,KAAK,gBAG9B,SADAL,EAAQD,EAAWA,EAAS1C,QAAQ2C,MAAQ,QAI5CH,EAAMS,MAAQN,EAAMM,MACpBT,EAAMU,IAAMP,EAAMO,IAClBV,EAAMF,GAASC,EAASA,EAAOnD,QAAUX,EAAM0E,UAAUd,EAAMe,OACxDZ,EAAMS,OAAST,EAAMU,KAEhC,OAAO,EAEX,QAASG,GAAWC,EAAOC,GAA3B,GACQC,GAAUF,EAAMG,QAAQ,IAAMhF,EAAMmD,KAAK,QAAU,aACnD8B,KACAD,EAAUE,EAAkBH,EAChCF,GAAM1B,KAAKgC,EAAU,GACjBL,GACAE,EAAQI,KAAK,SAAUC,EAAOf,GAC1BW,EAAgBI,GAASvH,EAAEwG,GAAQgB,aAG3C,KACIT,EAAM,GAAGU,YACX,MAAOC,GACLX,EAAM,GAAGY,QAETX,GACAE,EAAQI,KAAK,SAAUC,EAAOf,GAC1BxG,EAAEwG,GAAQgB,UAAUL,EAAgBI,MAIhD,QAASH,GAAkBQ,GACvB,MAAO5H,GAAE4H,GAASC,aAAa,QAAQxB,OAAO,SAAUkB,EAAOK,GAC3D,GAAIE,GAAgB5F,EAAM6F,kBAAkBH,GAAU,YACtD,OAAiC,WAA1BE,EAAcE,WACtBrC,IAAI1D,QA3Ld,GA6LOgG,GAoMAC,EAgEAC,EA0QAC,EAMAC,EA+FAC,EA8JAC,EAqKAC,EAlnCAtG,EAAQD,OAAOC,MACfuG,EAAOzI,EAAEqC,QAASqG,IAAK,KAAOxG,EAAMuG,MACpCE,EAAgB,cAAgB1G,QAChC2G,EAAU1G,EAAM2G,QAAQD,QACxBE,EAAW5G,EAAM2G,QAAQC,SACzBC,EAAa7G,EAAM6G,WACnBC,EAAS9G,EAAMqE,GAAGyC,OAClBC,EAAa/G,EAAMuE,KAAKwC,WACxBC,EAAmBhH,EAAMuE,KAAKyC,iBAC9BC,EAAkBjH,EAAMuE,KAAK0C,gBAC7BC,EAAQlH,EAAMuE,KAAK2C,MACnBC,EAAUrJ,EAAEqJ,QACZC,EAAUtJ,EAAEsJ,QACZC,EAAarH,EAAMqH,WACnBC,EAAQxJ,EAAEwJ,MACVnH,EAASrC,EAAEqC,OACXoH,EAAgBzJ,EAAEyJ,cAClB3G,EAAM9C,EAAE8C,IACR4G,EAAaxH,EAAMyH,YACnBC,EAAc1H,EAAM2H,aACpBC,EAAwB,EACxBC,EAAK,cACLC,EAAoB,KACpB3C,EAAW,WACX4C,EAAQ,QACRC,EAAQ,QACRC,EAAS,SACTC,GACAC,MACIC,OAAQ,cACRC,SAAU,YAEdC,IACIF,OAAQ,WACRC,SAAU,gBAGdE,EAAkB,wBAClBC,EAAa,aACbC,EAAc,oBACdC,EAAgB,wBAChBC,EAAM,IACNC,EAAsB,6CACtBC,EAA4B,mDAC5BC,EAAyB9I,EAAM+I,SAAS,wDAA0DP,EAAa,iEAC/GQ,EAAkB,wKAClBC,EAA0B,yDAC1BC,EAAqBlJ,EAAM+I,SAAS,sHACpCI,GAAwBnJ,EAAM+I,SAAS,wPACvCK,GAAyBpJ,EAAM+I,SAAS,yPACxCM,GAAkB,SAAUrF,EAAWzC,GAArB,GACd4B,IACAE,KAAM9B,EAAQsC,MACdyF,MAAO/H,EAAQ+H,OAEfC,EAAkBhI,EAAQ2C,MAAMsF,OAAOjI,EAAQsC,OAAO4F,UACtDF,IAAmBhC,EAAcgC,IAAoBA,EAAgBG,UACrEvG,EAAKnD,EAAMmD,KAAK,oBAAsBoG,EAAgBG,SAE1D5L,EAAE,+BAAiCkC,EAAMmD,KAAK,QAAU,WAAanD,EAAMmD,KAAK,QAAU,qBAAuBnD,EAAMmD,KAAK,QAAU,WAAa5B,EAAQsC,MAAQ,KAAO7D,EAAMmD,KAAK,YAAc,cAAgBA,KAAKA,GAAMwG,SAAS3F,GACvOlG,EAAE,SAAWkC,EAAMmD,KAAK,OAAS,KAAO5B,EAAQsC,MAAQ,6BAA6B+F,OAAOD,SAAS3F,IAErG6F,GAAkB,SAAU7F,EAAWzC,GACvCzD,EAAE,sBAAwByD,EAAQuI,OAAOC,OAAS,KAAOxI,EAAQyI,SAASC,aAAe,QAAQC,MAAM3I,EAAQ2I,OAAOP,SAAS3F,IAE/HmG,IACApF,QAAS,mBACTqF,UAAW,oBACXC,YAAa,kCACbC,KAAM,mBACNC,gBAAiB,kCACjBC,SAAU,mBACVC,gBAAiB,kGACjBC,SAAU,aACVC,cAAe,8BACfC,aAAc,mBACdC,UAAW,iBACXC,aAAc,kBACdC,KAAM,SACNC,KAAM,SACNC,KAAM,SACNC,aAAc,iBACdC,aAAc,iBACdC,WAAY,iBACZC,aAAc,iBACdC,QAAS,YACTC,QAAS,gBACTC,SAAU,mBACVC,QAAS,kBACTC,WAAY,gBACZC,eAAgB,qBAChBC,YAAa,iBACbC,MAAO,gBACPC,OACIC,KAAM,oBACNC,SAAU,oBACVC,cAAe,wBACfC,uBAAwB,6BACxBxC,QAAS,kBACTyC,iBAAkB,iCAClBpC,OAAQ,WACRqC,UAAW,eACXC,UAAW,eACXC,eAAgB,qBAEpBC,SACIC,cAAe,uCACfC,cAAe,uCACfF,QAAS,kBACTG,SAAU,mBACVC,MAAO,gBACPC,aAAc,iCACdC,QAAS,kBACT9C,OAAQ,6BACRsB,aAAc,wCACdyB,SAAU,kBACVC,QAAS,sBACTC,WAAY,2BACZC,kBAAmB,kBACnBC,WAAY,SACZC,YAAa,iBACbC,KAAM,SACNC,UAAW,cACXC,aAAc,mBAkElBC,GAAe1G,EAAW1G,QAC1BC,KAAM,SAAUsF,EAASnE,GACrBsF,EAAW2G,GAAGpN,KAAKqN,KAAKnN,MACxBA,KAAKoF,QAAUA,EACfpF,KAAKiB,QAAUpB,GAAO,KAAUG,KAAKiB,QAASA,GAC9CjB,KAAKoN,UAETnM,SACIoM,UAAW,OACXC,aAAa,GAEjBC,SAAU,SAAUC,GAAV,GACF3D,GAAc7D,EAAMwD,OACpBpK,EAAUY,KAAKgK,KAAKyD,KAAKpF,EAAMwB,EAAYsB,SAC3CuC,EAAUtO,EAAQoO,IAClBE,GAAQhP,SACRU,EAAQuO,YAAY9D,EAAYsB,SAASyC,WAAW,MACpDF,EAAQG,SAAShE,EAAYsB,SAAStI,KAAK,KAAMuF,GACjDpI,KAAKgK,KAAKyD,KAAK,MAAMG,WAAW3F,GAAiBpF,KAAKoF,EAAiBG,KAG/EgF,OAAQ,WAAA,GACAU,GAAO9N,KACP6J,EAAc7D,EAAMwD,OACpBuE,EAAe,KAAO1F,EAAMwB,EAAYa,KACxCsD,EAAuB3F,EAAMwB,EAAYoC,QAAQe,aACjDT,EAAUvM,KAAKiB,QAAQyI,SAAS6C,QAChCe,EAActN,KAAKiB,QAAQqM,WAC/BtN,MAAKgK,KAAOxM,EAAEsL,IACVU,OAAQK,EACR0C,UAEQtI,KAAM,MACNvG,KAAM6O,EAAQ0B,WAGdhK,KAAM,gBACNvG,KAAM6O,EAAQ2B,eAGdjK,KAAM,eACNvG,KAAM6O,EAAQ4B,iBAI1BnO,KAAKoF,QAAQgJ,OAAOpO,KAAKgK,MACzBhK,KAAKwL,MAAQ,GAAI9L,GAAMqE,GAAGsK,MAAMrO,KAAKgK,KAAMnK,GACvCyO,OAAQtO,KAAKoF,QAAQqI,KAAKO,GAC1BO,KAAM,WACFT,EAAKU,oBAETC,UAAWzO,KAAKiB,QAAQwN,WACzB7G,EAAW5H,KAAKiB,QAAQoM,aAC3BrN,KAAKoF,QAAQsJ,GAAGjH,EAAQF,EAAIyG,EAAsB,SAAU9I,GAAV,GAC1CyJ,GAASnR,EAAEwC,MACX4O,EAASD,EAAO9L,KAAKnD,EAAMmD,KAAK,UACpCqC,GAAE2J,iBACED,EACAd,EAAKgB,QAAQ,WAAaC,KAAMH,KAEhCd,EAAKtC,MAAM+C,OACPjB,GACAQ,EAAK9D,KAAKyD,KAAK,YAAYI,SAAShE,EAAYsB,SAAStI,KAAK,KAAMuF,GAAejE,MAAMsJ,KAAK,MAAM5K,MAChGgC,SAAU,EACVmK,wBAAyB5G,IAC1BjD,WAIfnF,KAAKgK,KAAKyD,KAAKM,GAAckB,MAAM,WAC/BzR,EAAEwC,MAAM6N,SAAShE,EAAYoB,UAC9B,WACCzN,EAAEwC,MAAM2N,YAAY9D,EAAYoB,WACjC9G,MAAMuK,GAAGjH,EAAQF,EAAIwG,EAAc,WAClCD,EAAKgB,QAAQ,WAAaC,KAAMvR,EAAEwC,MAAM6C,KAAKnD,EAAMmD,KAAK,aACxDiL,EAAKtC,MAAM0D,UAEX5B,IACAtN,KAAKwL,MAAM2D,KAAK,QAAS,WACrBrB,EAAK9D,KAAKyD,KAAKM,GAAcJ,YAAY9D,EAAYsB,SAAShH,MAAMsJ,KAAK,MAAM5K,KAAKgC,EAAU,GAC9FiJ,EAAK1I,QAAQV,QAAQ,IAAMhF,EAAMmD,KAAK,QAAU,aAAa4K,KAAKpF,EAAMwB,EAAYyB,YAAc,kBAAkBnG,UAExHnF,KAAKgK,KAAKyD,KAAK,MAAMiB,GAAG,UAAYnH,EAAI,SAAUrC,GAC9C,GAAIjH,GAAMiH,EAAEkK,OACZ,QAAQnR,GACR,IAAKgI,GAAKoJ,GACNnK,EAAE2J,iBACFf,EAAKP,SAAS,OACd,MACJ,KAAKtH,GAAKqJ,KACNpK,EAAE2J,iBACFf,EAAKP,SAAS,OACd,MACJ,KAAKtH,GAAKsJ,MACNzB,EAAK9D,KAAKyD,KAAKpF,EAAMwB,EAAYsB,SAASvB,OAC1C,MACJ,KAAK3D,GAAKuJ,IACNtK,EAAE2J,iBACFf,EAAKtC,MAAM0D,aAM3BV,iBAAkB,WAAA,GAMVlJ,GACAmK,EANAzF,EAAOhK,KAAKgK,KACZH,EAAc7D,EAAMwD,OACpB3K,EAAQmL,EAAK,GAAG/K,MAAMJ,MACtB4F,EAAUzE,KAAKoF,QAAQqI,KAAKpF,EAAMwB,EAAYoC,QAAQe,cACtD0C,EAAiBxI,EAAW8C,IAG3BA,EAAK/F,KAAKyD,IAAU7I,IAGzByG,EAAgB7F,OAAOkQ,iBAAmBlQ,OAAOkQ,iBAAiBlL,EAAQ,GAAI,MAAQ,EACtFgL,EAAgBnK,EAAgBsK,WAAWtK,EAAczG,OAASqI,EAAWzC,GACzEa,IAAkBc,EAAQyJ,SAAWzJ,EAAQ0J,QAC7CL,GAAiBG,WAAWtK,EAAcyK,aAAeH,WAAWtK,EAAc0K,cAAgBJ,WAAWtK,EAAc2K,iBAAmBL,WAAWtK,EAAc4K,mBAGvKrR,EAD2B,eAA3BmL,EAAKmG,IAAI,cACDV,GAAiBvI,EAAW8C,GAAQA,EAAKnL,SAEzC4Q,EAERC,EAAiB7Q,IACjBA,EAAQ6Q,GAEZ1F,EAAKmG,KACDC,WAAY3L,EAAQ0L,IAAI,eACxBtR,MAAOA,IACRoF,KAAKyD,EAAO7I,KAEnBwR,QAAS,WACLC,aAAatQ,KAAKuQ,eAClBvQ,KAAKwL,MAAM6E,UACXrQ,KAAKoF,QAAQoL,IAAIjJ,GACjBvH,KAAKgK,KAAKwG,IAAIjJ,GACdvH,KAAKyQ,YAGTC,GAAmB,SAAU3B,EAAMhM,GACnC,MAAO,UAAU9B,GAAV,GAEC0P,GACA1M,CAEJ,IAJAhD,EAAU4F,EAAQ5F,IAAagD,KAAMhD,GAAYA,EAC7C0P,EAAa1P,MACbgD,EAAO0M,EAAW1M,KACtB0M,EAAW1M,KAAOA,IACZ0M,YAAsB5B,KAAS4B,YAAsBlK,GACvD,KAAUmK,OAAM,mCAAqC7N,EAAO,2BAEhE,OAAO4N,aAAsB5B,GAAO4B,EAAa,GAAI5B,GAAK4B,KAG9DE,GAAkBnR,EAAMuE,KAAK6M,MAAMvT,QACnCwT,GAAI,KACJ7H,QACI6H,IAAMhC,KAAM,UACZiC,eAAiBjC,KAAM,UACvBkC,aAAelC,KAAM,UACrBA,MAAQA,KAAM,aAGlBmC,GAA4BzK,EAAW5G,QACvCC,KAAM,SAAUmB,GACZwF,EAAWyG,GAAGpN,KAAKqN,KAAKnN,KAAMH,GAAO,MACjCsR,QACIC,UAAWP,GACXjN,MAAOiN,KAEZ5P,KAEPoQ,WAAY,SAAUN,GAClB,MAAO/Q,MAAKsR,cAAc,gBAAiBP,IAE/CQ,aAAc,SAAUR,GACpB,MAAO/Q,MAAKsR,cAAc,cAAeP,IAE7CS,aAAc,SAAUT,GAAV,GACNQ,GAAevR,KAAKuR,aAAaR,GACjCM,EAAarR,KAAKqR,WAAWN,EAEjC,OADAQ,GAAapT,KAAKsT,MAAMF,EAAcF,GAC/BE,GAEXD,cAAe,SAAU/N,EAAOwN,GAAjB,GACP9M,GAAOjE,KAAK0R,OACZ7N,GACAN,MAAOA,EACPoO,SAAU,KACVtR,MAAO0Q,EAGX,OADA9M,GAAO,GAAI2C,GAAM3C,GAAMJ,OAAOA,GAAQ+N,YAI9CV,IAA0BW,OAASnB,GAAiBQ,GAA2B,6BAC3ExL,EAAYhG,EAAMuE,KAAK6M,MAAMvT,QAC7BuU,SAAU,WAAA,GACF3N,GAAMnE,KAAKmE,IACXD,EAAQlE,KAAKkE,KACjB,OAAOC,GAAMD,GAEjB6N,YAAa,WACT,MAA2B,KAApB/R,KAAK8R,YAEhBE,QAAS,SAAU3R,GAAV,GAKD4R,GACKzT,EALL+E,GACA,QACA,MAGJ,KAAS/E,EAAI,EAAGA,EAAI+E,EAAM7E,OAAQF,IAC9ByT,EAAW,GAAIC,MAAKlS,KAAKY,IAAI2C,EAAM/E,IAAI2T,UAAY9R,GACnDL,KAAKoS,IAAI7O,EAAM/E,GAAIyT,IAG3BlB,GAAI,KACJ7H,QACI6H,IAAMhC,KAAM,UACZsD,UACItD,KAAM,SACNuD,aAAc,KACdnJ,YAAcoJ,UAAU,IAE5BC,SACIzD,KAAM,SACN5F,YAAcoJ,UAAU,IAE5BvJ,OACI+F,KAAM,SACNuD,aAAc,YAElBpO,OACI6K,KAAM,OACN5F,YAAcoJ,UAAU,IAE5BpO,KACI4K,KAAM,OACN5F,YACIoJ,UAAU,EACVE,YAAapP,EACb+F,QAAS,wDAGjBsJ,iBACI3D,KAAM,SACN5F,YACIoJ,UAAU,EACVI,IAAK,EACLC,IAAK,EACLC,KAAM,MAGdC,SAAW/D,KAAM,WACjB3C,UACI2C,KAAM,UACNuD,cAAc,MAItB3M,EAAkBc,EAAW5G,QAC7BC,KAAM,SAAUmB,GACZwF,EAAWyG,GAAGpN,KAAKqN,KAAKnN,KAAMH,GAAO,MACjCsR,QACIC,UAAW1L,EACX9B,MAAO8B,IAEZzE,KAEPgC,OAAQ,SAAU8P,GAAV,GACAV,GAAWU,EAAKnS,IAAI,YACpBoS,EAAWhT,KAAKiT,gBAAgBF,EAIpC,OAHA/S,MAAKkT,aAAaF,GAClBD,EAAOtM,EAAWyG,GAAGjK,OAAOkK,KAAKnN,KAAM+S,GACvC/S,KAAKmT,cAAcd,EAAUU,EAAKnS,IAAI,YAC/BmS,GAEX5P,IAAK,SAAU4P,GACX,GAAKA,EAIL,MADAA,GAAO/S,KAAKoT,aAAaL,GAClB/S,KAAKqT,OAAOrT,KAAKsT,aAAaP,GAAMrU,OAAQqU,IAEvDM,OAAQ,SAAUtO,EAAOgO,GACrB,GAAKA,EAQL,MALAA,GAAO/S,KAAKoT,aAAaL,GACzBA,EAAKX,IAAI,UAAWrN,GACpBgO,EAAOtM,EAAWyG,GAAGmG,OAAOlG,KAAKnN,KAAM+E,EAAOgO,GAC9C/S,KAAKuT,iBAAiBR,EAAM/S,KAAKsT,aAAaP,GAAMrU,OAAS,GAC7DsB,KAAKwT,sBAAsBxT,KAAKyT,WAAWV,IACpCA,GAEXW,aAAc,SAAUX,GAAV,GAWNY,GAVA1P,EAAOjE,KAAK0R,OACZ7N,GACAN,MAAO,WACPoO,SAAU,KACVtR,MAAO,MAEPuT,EAAQ5T,KAAK6T,OAAS7T,KAAK6T,MAAMnV,OAASsB,KAAK6T,OAC/CtQ,MAAO,UACPuQ,IAAK,MAGT,IAAMf,EAAM,CAER,GADAY,EAASZ,EAAKnS,IAAI,MACd+S,IAAWjR,GAAwB,OAAXiR,GAA8B,KAAXA,EAC3C,QAEJ9P,GAAOxD,MAAQsT,EAGnB,MADA1P,GAAO,GAAI2C,GAAM3C,GAAMJ,OAAOA,GAAQzF,KAAKwV,GAAOhC,WAGtDqB,gBAAiB,SAAUF,GAAV,GACT9O,MACA6J,EAAO9N,KACP+T,EAAW,SAAUhB,GACrB,GAAIxH,GAAQuC,EAAK4F,aAAaX,EAC9B9O,GAAK9F,KAAKsT,MAAMxN,EAAMsH,GACtBjL,EAAIiL,EAAOwI,GAOf,OALMhB,GACFgB,EAAShB,GAET9O,EAAOjE,KAAK0R,OAETzN,GAEXqP,aAAc,SAAUP,GACpB,IAAKA,EACD,MAAO,KAEX,IAAI/O,GAAShE,KAAKyT,WAAWV,EAC7B,OAAO/S,MAAK0T,aAAa1P,IAE7ByP,WAAY,SAAUV,GAClB,MAAKA,IAAiC,OAAzBA,EAAKnS,IAAI,YAGfZ,KAAKY,IAAImS,EAAKV,UAFV,MAIf2B,UAAW,SAAUjB,GAGjB,IAHO,GACHkB,GAAQ,EACRjQ,EAAShE,KAAKyT,WAAWV,GACX,OAAX/O,GACHiQ,GAAS,EACTjQ,EAAShE,KAAKyT,WAAWzP,EAE7B,OAAOiQ,IAEXC,SAAU,SAAUnB,GAAV,GAEF3T,GAEKZ,EAAO2V,EAIJnB,EAPR/O,KAEAsH,EAAQvL,KAAK0T,aAAaX,EAC9B,KAASvU,EAAI,EAAG2V,EAAI5I,EAAM7M,OAAQF,EAAI2V,EAAG3V,IACrCY,EAAUmM,EAAM/M,GAChByF,EAAK9F,KAAKiB,GACNA,EAAQwB,IAAI,cACRoS,EAAWhT,KAAKkU,SAAS9U,GAC7B6E,EAAK9F,KAAKsT,MAAMxN,EAAM+O,GAG9B,OAAO/O,IAEXmQ,OAAQ,SAAUrB,EAAMsB,GAAhB,GAEAC,GAqCK/Q,EAtCLuK,EAAO9N,KAEPuU,EAAiB,SAAUC,EAAYC,GAAtB,GAERjW,GAAO2V,EADZnB,EAAWlF,EAAKmF,gBAAgBuB,EACpC,KAAShW,EAAI,EAAG2V,EAAInB,EAAStU,OAAQF,EAAI2V,EAAG3V,IACxCwU,EAASxU,GAAGwT,QAAQyC,IAGxBC,EAAqB,SAAUxP,GAAV,GACjB3B,GAAQ2B,EAAE3B,MACVK,EAAQsB,EAAEyP,MACd,QAAQpR,GACR,IAAK,QACDuK,EAAK8G,qBAAqB9G,EAAK2F,WAAW7P,IAC1C2Q,EAAe3Q,EAAOA,EAAMhD,IAAI2C,GAAO4O,UAAYmC,EAASnC,UAC5D,MACJ,KAAK,MACDrE,EAAK+G,mBAAmB/G,EAAK2F,WAAW7P,GACxC,MACJ,KAAK,kBACDkK,EAAKgH,+BAA+BhH,EAAK2F,WAAW7P,GACpD,MACJ,KAAK,UACDkK,EAAKyF,iBAAiB3P,EAAO0Q,IAIjCD,GAAShC,WAAa3P,IACtB4R,EAAWvB,EAAKnS,IAAI,YAChB0T,IAAaD,EAAShC,WACtBU,EAAKX,IAAI,WAAYiC,EAAShC,UAC9BvE,EAAKqF,cAAcmB,EAAUvB,EAAKnS,IAAI,YACtCmS,EAAKX,IAAI,UAAWtE,EAAKwF,aAAaP,GAAMrU,OAAS,GACrDoP,EAAK0F,sBAAsB1F,EAAK2F,WAAWV,WAExCsB,GAAShC,UAEpBU,EAAK5D,KAAK,SAAUuF,EACpB,KAASnR,IAAS8Q,GACdC,EAAWvB,EAAKnS,IAAI2C,GACpBwP,EAAKX,IAAI7O,EAAO8Q,EAAS9Q,GAE7BwP,GAAKtC,OAAO,SAAUiE,IAE1BlB,sBAAuB,SAAUV,GACxBA,IAGL9S,KAAK+U,eAAejC,GACf9S,KAAK0T,aAAaZ,GAASpU,SAGhCsB,KAAK4U,qBAAqB9B,GAC1B9S,KAAK6U,mBAAmB/B,GACxB9S,KAAK8U,+BAA+BhC,MAExC8B,qBAAsB,SAAU9B,GAAV,GACdhF,GAAO9N,KACPgV,EAAkB,SAAUR,GAAV,GAGdS,GACKzW,EAAO2V,EAHZnB,EAAWlF,EAAK4F,aAAac,GAC7B7B,EAAMK,EAAS,GAAG9O,MAAMiO,SAE5B,KAAS3T,EAAI,EAAG2V,EAAInB,EAAStU,OAAQF,EAAI2V,EAAG3V,IACxCyW,EAAajC,EAASxU,GAAG0F,MAAMiO,UAC3B8C,EAAatC,IACbA,EAAMsC,EAGd,OAAO,IAAI/C,MAAKS,GAEpB3S,MAAKkV,wBAAwBpC,EAAS,QAASkC,IAEnDH,mBAAoB,SAAU/B,GAAV,GACZhF,GAAO9N,KACPmV,EAAgB,SAAUX,GAAV,GAGZY,GACK5W,EAAO2V,EAHZnB,EAAWlF,EAAK4F,aAAac,GAC7B5B,EAAMI,EAAS,GAAG7O,IAAIgO,SAE1B,KAAS3T,EAAI,EAAG2V,EAAInB,EAAStU,OAAQF,EAAI2V,EAAG3V,IACxC4W,EAAapC,EAASxU,GAAG2F,IAAIgO,UACzBiD,EAAaxC,IACbA,EAAMwC,EAGd,OAAO,IAAIlD,MAAKU,GAEpB5S,MAAKkV,wBAAwBpC,EAAS,MAAOqC,IAEjDL,+BAAgC,SAAUhC,GAAV,GACxBhF,GAAO9N,KACPqV,EAA4B,SAAUb,GAAV,GACxBxB,GAAWlF,EAAK4F,aAAac,GAC7B9B,EAAkB,GAAI9L,GAAMoM,GAAUsC,YAClC/R,MAAO,kBACP+R,UAAW,YAEnB,OAAO5C,GAAgBA,gBAAgB6C,QAE3CvV,MAAKkV,wBAAwBpC,EAAS,kBAAmBuC,IAE7DH,wBAAyB,SAAUpC,EAASvP,EAAOwQ,GAA1B,GAIjB1T,GAEA2D,CALC8O,KAGDzS,EAAQ0T,EAASjB,GACrBA,EAAQV,IAAI7O,EAAOlD,GACf2D,EAAShE,KAAKyT,WAAWX,GACzB9O,GACAhE,KAAKkV,wBAAwBlR,EAAQT,EAAOwQ,KAGpDZ,cAAe,SAAUd,EAAUtN,GAApB,GAGFvG,GAAW2V,EAFhBnQ,EAAsB,OAAbqO,EAAoB,KAAOrS,KAAKY,IAAIyR,GAC7CW,EAAWhT,KAAK0T,aAAa1P,EACjC,KAASxF,EAAIuG,EAAOoP,EAAInB,EAAStU,OAAQF,EAAI2V,EAAG3V,IAC5CwU,EAASxU,GAAG4T,IAAI,UAAW5T,EAE/BwB,MAAKwT,sBAAsBxP,IAE/BuP,iBAAkB,SAAUR,EAAMyC,GAAhB,GAQLhX,GAPLgU,EAAUO,EAAKnS,IAAI,WACnByM,EAAYmF,EAAUgD,EACtBC,EAAapI,EAAYmI,EAAahD,EACtCkD,EAAWrI,EAAYmF,EAAUgD,EACjCG,EAAWtI,EAAYoI,EAAaA,EAAa,EACjDG,EAAW5V,KAAKsT,aAAaP,EAEjC,KADA2C,EAAWG,KAAKlD,IAAI+C,EAAUE,EAASlX,OAAS,GACvCF,EAAIiX,EAAYjX,GAAKkX,EAAUlX,IAChCoX,EAASpX,KAAOuU,IAGpB6C,EAASpX,GAAG4T,IAAI,UAAWuD,GAC3BA,GAAY,IAGpBZ,eAAgB,SAAUhC,GACtB,GAAa,OAATA,EAAe,CACf,GAAI+C,GAAa9V,KAAK0T,aAAaX,GAAMrU,MACzCqU,GAAKX,IAAI,UAAW0D,EAAa,KAGzC1C,aAAc,SAAUL,GACpB,KAAMA,YAAgBrN,IAAY,CAC9B,GAAI2O,GAAWtB,CACfA,GAAO/S,KAAK+V,kBACZhD,EAAKiD,OAAO3B,GAEhB,MAAOtB,MAGfpN,EAAgBkM,OAASnB,GAAiB/K,EAAiB,mBAC3D9F,GAAO,EAAMH,EAAMuE,MACf0B,gBAAiBA,EACjBD,UAAWA,EACXwL,0BAA2BA,GAC3BL,gBAAiBA,KAEjBjL,GACAqQ,SACIC,UAAWnN,GACXoN,UAAW5M,KAGf1D,EAASnG,EAAM6G,WAAW1G,QAC1BC,KAAM,SAAUsF,EAASnE,GACrBvB,EAAM6G,WAAW2G,GAAGpN,KAAKqN,KAAKnN,MAC9BA,KAAKoF,QAAUA,EACfpF,KAAKiB,QAAUpB,GAAO,KAAUG,KAAKiB,QAASA,GAC9CjB,KAAKoW,aAAepW,KAAKiB,QAAQmV,cAErClN,OAAQ,SAAUtD,EAAShC,GAAnB,GAKAsF,GAJA4E,EAAO9N,KACPiB,EAAUjB,KAAKiB,QACfyI,EAAWzI,EAAQyI,SAAS2M,OAC5BF,EAAYlV,EAAQkV,UAEpBvM,EAAQ,SAAU1E,GAClBA,EAAE2J,iBACFsH,EAAUE,OAAOvI,EAAKpK,UAAU+J,KAAKpF,EAAMrC,EAAMwD,OAAOgC,MAAMQ,gBAAiBpI,GAuCnF,OArCI3C,GAAQ0C,SAAS8E,SACjBS,EAAS1L,EAAE8C,IAAIsD,EAAMsF,OAAQ,SAAU7I,EAAOpC,GAC1C,OAASsF,MAAOtF,MAGpBiL,IAEQ3F,MAAO,QACPyF,MAAOU,EAASV,QAGhBzF,MAAO,QACPyF,MAAOU,EAASxF,MAChBmS,OAAQzQ,EAAQsQ,YAGhB3S,MAAO,MACPyF,MAAOU,EAASvF,IAChBkS,OAAQzQ,EAAQsQ,YAGhB3S,MAAO,kBACPyF,MAAOU,EAASgJ,gBAChB4D,OAAQ9O,IAGZ5D,EAAMhD,IAAIuV,EAAU5S,QACpB2F,EAAO/K,MACHoF,MAAO4S,EAAU5S,MACjByF,MAAOU,EAASyM,UAChBzM,SAAUA,EACV2M,OAAQzQ,EAAQuQ,UAChBvM,MAAOA,EACPJ,OAAQxD,EAAMwD,OAAOgC,SAI1BtC,GAEXqN,mBAAoB,SAAU3S,EAAOsF,EAAQsN,GAAzB,GAaHhY,GAAOE,EACR6E,EASIkT,EAtBZN,EAAYnW,KAAKiB,QAAQkV,UACzB1N,EAAWzI,KAAKiB,QAAQ0C,SAAS8E,SACjCiO,EAAW7W,KAAWH,EAAMiX,SAAU3W,KAAKiB,QAAQ2V,kBACnDC,EAAYH,EAASG,UACrBC,EAAc9Q,EAAMwD,OAAOgC,MAC3BuL,EAAO,EACX,IAAItO,QACWA,KAAad,IACpBc,EAAWhJ,OAAOuX,SAASvO,IAE/BsO,GAAQrX,EAAM+I,SAASA,EAAUiO,GAAU9S,OAE3C,KAASpF,EAAI,EAAGE,EAASwK,EAAOxK,OAAQF,EAAIE,EAAQF,IAC5C+E,EAAQ2F,EAAO1K,GACnBuY,GAAQ,eAAiBD,EAAY/K,UAAY,iBAAmBxI,EAAMA,MAAQ,MAAQA,EAAMyF,OAASzF,EAAMA,OAAS,IAAM,iBAC1HA,EAAMA,QAAU4S,EAAU5S,QAC1BwT,GAAQ,eAAiBD,EAAY9K,eAAiB,kCAErDpI,EAAMD,UAAYC,EAAMD,SAASJ,EAAMA,QACxCiT,EAAerY,KAAKoF,GACpBwT,GAAQ,QAAUrX,EAAMmD,KAAK,iBAAmB,KAAOU,EAAMA,MAAQ,YAAcuT,EAAYhL,UAAY,aAEvG2K,EAAO,KACPlT,EAAMA,OACNA,EAAQ7D,EAAMuX,KAAK1T,EAAMA,MAAOsT,GAChCJ,GAAQlT,EAAQ,aAAiBA,GAEjCkT,GAAQ,KAEZA,GAAQ,IACRA,EAAO/W,EAAM+I,SAASgO,EAAMC,GAC5BK,GAAQ,eAAiBD,EAAYhL,UAAY,KAAO2K,EAAK7S,GAAS,SAIlF,OAAOmT,MAGXjR,EAAcD,EAAOhG,QACrBwQ,QAAS,WACLrQ,KAAKkP,QACLlP,KAAKyQ,UAETyG,SAAU,SAAUnE,GAChB/S,KAAK2D,SAAW3D,KAAKmX,mBAAmBpE,IAE5C7D,MAAO,WAAA,GACCpB,GAAO9N,KACPqQ,EAAU,WACNvC,EAAKnK,WACLmK,EAAKnK,SAAS0M,UACdvC,EAAKnK,SAAW,KAChBmK,EAAKpK,UAAY,MAEjBoK,EAAKtC,QACLsC,EAAKtC,MAAM6E,UACXvC,EAAKtC,MAAQ,MAGjBxL,MAAK2D,UAAY3D,KAAK0D,UAAU0T,GAAG,aACnCtJ,EAAKgB,QAAQ,SAAWrP,OAAQqO,EAAKpK,YACrC1D,KAAK0D,UAAUO,KAAK,eAAekL,KAAK,aAAckB,GAASnB,SAE/DmB,KAGRgH,WAAY,SAAUpW,GAAV,GAICzC,GAAOE,EAIZ+F,EAIA+G,EAXA8L,EAAUrW,EAAQqW,QAClBR,EAAc9Q,EAAMwD,OAAOgC,MAC3BuL,EAAOrX,EAAM4W,OAAO,4EAA6EQ,EAAYrL,KAAMqL,EAAYnL,cAAemL,EAAY1N,QAASnI,EAAQvD,KAAMoZ,EAAYjL,iBACjM,KAASrN,EAAI,EAAGE,EAAS4Y,EAAQ5Y,OAAQF,EAAIE,EAAQF,IACjDuY,GAAQ/W,KAAKoW,aAAakB,EAAQ9Y,GAEtCuY,IAAQ,qBACJtS,EAAUzE,KAAKoF,QACfpF,KAAKwL,OACLxL,KAAKwL,MAAM6E,UAEX7E,EAAQxL,KAAKwL,MAAQhO,EAAEuZ,GAAM1N,SAAS5E,GAAS8S,GAAG,GAAG7I,GAAG,QAASrG,EAAMyO,EAAYrN,OAAQ,SAAUvE,GACrGA,EAAE2J,iBACFrD,EAAM0D,OACN,IAAIsI,GAAcha,EAAE0H,EAAEuS,eAAe1S,OACrCuS,GAAQE,GAAa5N,UACtB8N,aACCC,OAAO,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW,EACX9O,MAAO/H,EAAQ+H,MACf+O,SAAS,EACTC,WAAY,WACRhY,KAAKqQ,UACL5L,EAAQU,WAEb8S,iBACHzM,EAAM0M,SAAS3J,OACf/C,EAAMpG,QAAQqI,KAAK,cAActI,SAErCgS,mBAAoB,SAAUpE,GAAV,GA2BZrP,GAiBAyU,EA3CArK,EAAO9N,KACPiB,KACAyI,EAAW1J,KAAKiB,QAAQyI,SACxBG,EAAc7D,EAAMwD,OACpBsN,EAAcjN,EAAY2B,MAC1BuL,EAAOrX,EAAM4W,OAAO,mDAAoD5W,EAAMmD,KAAK,OAAQkQ,EAAKnQ,IAAKkU,EAAYrL,KAAMqL,EAAYpL,SAAUoL,EAAYnL,eACzJzC,EAASlJ,KAAKkJ,OAAOtD,EAAQqQ,QAASlD,GACtCyD,IAuFJ,OAtFAO,IAAQ/W,KAAKuW,mBAAmBxD,EAAM7J,EAAQsN,GAC9CO,GAAQ,eAAiBD,EAAYjL,iBAAmB,KACxDkL,GAAQ/W,KAAKoW,cACTrT,KAAM,SACNrF,KAAMgM,EAAS0O,KACfC,UAAWrS,EAAMwD,OAAOwB,UAE5B+L,GAAQ/W,KAAKoW,cACTrT,KAAM,SACNrF,KAAMgM,EAAS4O,SAEfxK,EAAK7M,QAAQ0C,SAAS0M,WAAY,IAClC0G,GAAQ/W,KAAKoW,cACTrT,KAAM,SACNrF,KAAMgM,EAAS2G,WAGvB0G,GAAQ,qBACJrT,EAAY1D,KAAK0D,UAAYlG,EAAEuZ,GAAM1N,SAASrJ,KAAKoF,SAASmS,GAAG,GAAGG,YAAY7X,GAC9E8X,OAAO,EACPE,WAAW,EACXC,WAAW,EACX9O,MAAOU,EAAS2M,OAAOkC,YACvBR,SAAS,EACT7I,MAAO,SAAUhK,GACTA,EAAEsT,eACE1K,EAAKgB,QAAQ,UACTpL,UAAWA,EACXE,MAAOmP,KAEX7N,EAAE2J,mBAIf5N,IACCkX,EAAiBzU,EAAU+U,eAC3BvP,OAAQsN,EACR5S,MAAOmP,EACP2F,gBAAgB,EAChBC,gBAAgB,EAChBhK,OAAQb,EAAK7M,QAAQ0N,SACtB1K,KAAK,iBACRvE,EAAMkZ,UAAUlV,GACX1D,KAAK8O,QAAQ,QACVpL,UAAWA,EACXE,MAAOmP,IAoCXjF,EAAKgB,QAAQ,UACTpL,UAAWA,EACXE,MAAOmP,KApCXrP,EAAUO,KAAK,eAAeiU,SAAS3J,OACvC7K,EAAUgL,GAAGjH,EAAQF,EAAIc,EAAMwB,EAAYgB,aAAc,SAAU3F,GAC/DA,EAAE2J,iBACF3J,EAAE2T,kBACF/K,EAAKgB,QAAQ,UACTpL,UAAWA,EACXE,MAAOmP,MAGfrP,EAAUgL,GAAGjH,EAAQF,EAAIc,EAAMwB,EAAYiB,WAAY,SAAU5F,GAAV,GAG/CgE,GACA4P,EACAvV,EACK/E,EAAOE,CAAhB,KALAwG,EAAE2J,iBACF3J,EAAE2T,kBACE3P,EAAS4E,EAAK5E,OAAOtD,EAAQqQ,QAASlD,GACtC+F,KAEKta,EAAI,EAAGE,EAASwK,EAAOxK,OAAQF,EAAIE,EAAQF,IAChD+E,EAAQ2F,EAAO1K,GAAG+E,MAClBuV,EAAWvV,GAASwP,EAAKnS,IAAI2C,EAEjCuK,GAAKgB,QAAQ,QACTpL,UAAWA,EACXE,MAAOmP,EACP+F,WAAYA,MAGpBpV,EAAUgL,GAAGjH,EAAQF,EAAIc,EAAMwB,EAAYe,aAAc,SAAU1F,GAC/DA,EAAE2J,iBACF3J,EAAE2T,kBACF/K,EAAKgB,QAAQ,UACTpL,UAAWA,EACXE,MAAOmP,OASZoF,KAGXpS,EAAiBS,EAAO3G,QACxBC,KAAM,SAAUsF,EAASnE,GACrBuF,EAAO0G,GAAGpN,KAAKqN,KAAKnN,KAAMoF,EAASnE,GACnCjB,KAAKyE,QAAUzE,KAAKoF,QACpBpF,KAAK4D,MAAQ5D,KAAKiB,QAAQ2C,MAC1B5D,KAAKgM,eAAiBhM,KAAKiB,QAAQ+K,eACnChM,KAAKoW,aAAepW,KAAKiB,QAAQmV,aACjCpW,KAAK+Y,iBACL/Y,KAAKgZ,mBAETC,QAAS,QACT1K,KAAM,WACFvO,KAAKP,OAAOyY,SAAS3J,QAEzBW,MAAO,WACHlP,KAAKP,OAAO0P,KAAK,aAAcnI,EAAMhH,KAAKqQ,QAASrQ,OAAOkP,SAE9DmB,QAAS,WACLrQ,KAAKkZ,mBACLlZ,KAAKmZ,KAAK9I,UACVrQ,KAAKmZ,KAAO,KACZnZ,KAAKP,OAAO4Q,UACZrQ,KAAKP,OAAS,KACd+G,EAAO0G,GAAGmD,QAAQlD,KAAKnN,MACvBN,EAAM2Q,QAAQrQ,KAAKyE,SACnBzE,KAAKoF,QAAUpF,KAAKyE,QAAU,MAElCuU,gBAAiB,WAAA,GACTnP,GAAc7D,EAAMwD,OACpB2P,EAAOnZ,KAAKmZ,KACZC,EAAepZ,KAAKqZ,aAAerS,EAAMhH,KAAKsZ,QAAStZ,KAC3DA,MAAK0D,UAAUgL,GAAGjH,EAAQF,EAAIc,EAAMwB,EAAYgB,aAAc7K,KAAKqZ,cACnErZ,KAAKuZ,WAAavS,EAAMhH,KAAKwZ,MAAOxZ,MACpCA,KAAK0D,UAAUgL,GAAGjH,EAAQF,EAAIc,EAAMwB,EAAYiB,WAAY9K,KAAKuZ,YACjEvZ,KAAKP,OAAO0P,KAAK,QAAS,SAAUjK,GAC5BA,EAAEsT,eACFY,EAAalU,KAGrBiU,EAAK1U,QAAQiK,GAAGjH,EAAQF,EAAI,yBAA4B,WAAA,GAChDnC,GAAU5H,EAAEwC,MACZyZ,EAAMjc,EAAE4H,GAASsU,QAAQ,MACzB9V,EAAQuV,EAAKxI,WAAWgJ,SAASF,EAAI5W,KAAKnD,EAAMmD,KAAK,SACrDxC,EAAQ7C,EAAE4H,GAASgS,GAAG,YAAc,EAAI,EAC5CxT,GAAMwO,IAAI,QAAS/R,MAG3B6Y,iBAAkB,WACdlZ,KAAKqZ,aAAe,KACpBrZ,KAAKuZ,WAAa,KAClBvZ,KAAK0D,UAAU8M,IAAIjJ,GACnBvH,KAAKmZ,KAAK1U,QAAQ+L,OAEtB8I,QAAS,SAAUpU,GACfA,EAAE2J,iBACF7O,KAAKkP,SAETsK,MAAO,SAAUtU,GACbA,EAAE2J,iBACF7O,KAAK4Z,eACA5Z,KAAKyE,QAAQ2S,GAAG/O,EAAMrC,EAAMwD,OAAOgC,MAAMQ,iBAC1ChM,KAAK8O,QAAQ,QACTpL,UAAW1D,KAAKyE,QAChBb,MAAO5D,KAAK4D,QAGpB5D,KAAKkP,SAET6J,eAAgB,WAAA,GACRjL,GAAO9N,KACP8W,EAAc9Q,EAAMwD,OAAOgC,MAC3BqO,EAAMna,EAAM4W,OAAO,qDAAsDQ,EAAYrL,KAAMqL,EAAYpL,SAAUoL,EAAYnL,cAAemL,EAAYlL,uBAC5JiO,GAAMrc,EAAEqc,GACR7Z,KAAK0D,UAAYmW,EAAIpM,KAAKpF,EAAMyO,EAAYlL,wBAC5C5L,KAAKP,OAASoa,EAAInC,aACdC,OAAO,EACPE,WAAW,EACXC,WAAW,EACXC,SAAS,EACT/O,MAAOhJ,KAAKiB,QAAQyI,SAASoQ,qBAC7BvL,KAAM,WACFT,EAAKqL,KAAKY,QAAO,MAEtB9V,KAAK,eACRjE,KAAKga,gBACLha,KAAKia,kBAETD,cAAe,WAAA,GACPlM,GAAO9N,KACP0J,EAAW1J,KAAKiB,QAAQyI,SACxBtE,EAAU5H,EAAE,8BAA8B6L,SAASrJ,KAAK0D,UAC5D1D,MAAKmZ,KAAO,GAAIzZ,GAAMqE,GAAGmW,KAAK9U,GAC1B+U,UAEQ5W,MAAO,OACPyF,MAAOU,EAAS0Q,gBAChB3R,SAAU,6HAGVlF,MAAO,QACPyF,MAAOU,EAAS2Q,YAChB5R,SAAU,SAAU6R,GAAV,GACFC,GAAcD,EAAShE,OACvBjW,EAA2B,OAAnBia,EAASja,MAAiBia,EAASja,MAAQ,EACvD,OAAOka,GAAc7a,EAAM8a,SAASna,EAAOka,GAAela,KAItEvB,OAAQ,IACR2b,UAAU,EACV9W,UAAU,EACV+W,YAAY,EACZ/J,YACI1M,KAAM6J,EAAK7M,QAAQgD,KACnBkN,QACIvN,OACImN,GAAI,KACJ7H,QACI6H,IAAM4J,KAAM,MACZ5X,MACI4X,KAAM,OACN5L,KAAM,SACNpL,UAAU,GAEdtD,OACIsa,KAAM,QACN5L,KAAM,SACN5F,WAAYnJ,KAAKiB,QAAQ2Z,iBAE7BtE,QACIqE,KAAM,SACN5L,KAAM,cAM1BqJ,KAAM,SAAUlT,GACZ,GAAI7E,KAAU6E,EAAE2V,OAAOxa,KACvB6E,GAAExB,UAAUM,SAASyJ,KAAK,0BAA4BqN,KAAK,UAAWza,OAIlF4Z,eAAgB,WAAA,GAGHzb,GAAOE,EAFZ4Y,EAAUtX,KAAKiB,QAAQqW,QACvBP,EAAO,eAAiB/Q,EAAMwD,OAAOgC,MAAMK,iBAAmB,IAClE,KAASrN,EAAI,EAAGE,EAAS4Y,EAAQ5Y,OAAQF,EAAIE,EAAQF,IACjDuY,GAAQ/W,KAAKoW,aAAakB,EAAQ9Y,GAEtCuY,IAAQ,SACR/W,KAAK0D,UAAU0K,OAAO2I,IAE1B6C,aAAc,WAAA,GAENvZ,GAEK7B,EAAOE,EAHZyX,KAEAlS,EAAOjE,KAAKmZ,KAAKxI,WAAW1M,MAChC,KAASzF,EAAI,EAAGE,EAASuF,EAAKvF,OAAQF,EAAIE,EAAQF,IAC9C6B,EAAQ4D,EAAKzF,GAAGoC,IAAI,SACN,OAAVP,GAAkBA,EAAQ,GAC1B8V,EAAUhY,KAAK8F,EAAKzF,GAG5BwB,MAAK4D,MAAM5D,KAAKgM,gBAAkBmK,KAGtCnQ,EAAQQ,EAAO3G,QACfC,KAAM,SAAUsF,EAASnE,EAASgY,GAC1BpS,EAAQ5F,KACRA,GAAY0P,WAAY1P,IAE5BwE,GACI2I,QACI1Q,KAAM,WACNkR,OAAQ,MACRyJ,UAAWrS,EAAMwD,OAAOyC,QAAQe,aAChC+N,UAAW/U,EAAMwD,OAAOyC,QAAQO,UAEpCwO,KACItd,KAAM,gBACN2a,UAAWrS,EAAMwD,OAAOyC,QAAQc,UAChCgO,UAAW/U,EAAMwD,OAAOyC,QAAQQ,UAGxCjG,EAAO0G,GAAGpN,KAAKqN,KAAKnN,KAAMoF,EAASnE,GAC/BgY,IACAjZ,KAAKib,QAAUhC,GAEnBjZ,KAAKkb,WACLlb,KAAKmb,aACAnb,KAAKiB,QAAQoL,OAAUrM,KAAKiB,QAAQoL,MAAM3N,SAC3CsB,KAAKiB,QAAQoL,OACT,MACA,OACA,UAGRrM,KAAKob,YACLpb,KAAKqb,WACLrb,KAAKsb,UACLtb,KAAKub,oBACLvb,KAAKwb,iBAAkB,EACvBxb,KAAK0R,KAAK1R,KAAKkK,SAASuR,mBACxBzb,KAAKwb,iBAAkB,EACvBxb,KAAK0b,cACL1b,KAAK2b,eACL3b,KAAK4b,aACL5b,KAAK6b,QACL7b,KAAKsR,gBACLtR,KAAK8b,aACL9b,KAAK+b,cACL/b,KAAKgc,YACLhc,KAAKic,gBACLjc,KAAKkc,gBACLxc,EAAMyc,OAAOnc,OAEjBiZ,QACI,cACA,YACA,MACA,OACA,SACA,SACA,OACA,SACA,WACA,YACA,OACA,UACA,cACA,SACA,YACA,gBAEJhY,SACI8B,KAAM,QACNqZ,UAAU,EACV9O,aAAa,EACb+O,YAAY,EACZ1Y,UAAU,EACVkU,WAAW,EACXyE,wBAAyBhV,EACzB6S,WACA9N,SACAsE,cACAa,gBACA2E,aACAoG,eACAC,aAAc,KACd9S,UACI0O,KAAM,OACNE,OAAQ,SACRjI,QAAS,SACToM,uBAAwBnU,EACxBoU,6BAA8BnU,EAC9BoU,sBAAuB,cACvBC,4BAA6B,oBAC7BvQ,OACIwQ,IAAK,MACLC,KAAM,OACNC,MAAO,QACPC,KAAM,OACN9Y,MAAO,QACPC,IAAK,OAEToI,SACI6B,OAAQ,WACRH,SAAU,YACVC,aAAc,YACdC,YAAa,YACb6M,IAAK,iBAET3E,QACIkC,YAAa,OACbuB,qBAAsB,YACtB9Q,MAAO,QACP9E,MAAO,QACPC,IAAK,MACLuO,gBAAiB,WACjByD,UAAW,YACXxM,aAAc,SACdyQ,gBAAiB,YACjBC,YAAa,UAGrB4C,eAAe,EACfC,cAAc,EACdjR,QAAS,KACTkR,aAAc,GAAIjL,MAAK,KAAM,EAAG,EAAG,EAAG,EAAG,GACzCkL,WAAY,GAAIlL,MAAK,KAAM,EAAG,EAAG,GAAI,EAAG,GACxCmL,cAAe,EACfC,YAAa,EACbC,SAAU,EACVC,MAAM,EACN1e,OAAQ,IACR2e,UAAW,MACX3T,UAAW,MAEf4T,OAAQ,SAAUrd,GACd,GAAI2J,GAAOhK,KAAKgK,IAChB,OAAK3J,IAGL2J,EAAK0T,OAAOrd,GACZL,KAAKgK,KAAK5E,QAAQqI,KAAK,wBAAwBtI,QAD/C6E,GAFWA,EAAK0T,UAMpBC,eAAgB,WACZ3d,KAAKgK,KAAK2T,kBAEdtN,QAAS,WACL7J,EAAO0G,GAAGmD,QAAQlD,KAAKnN,MACnBA,KAAK2Q,aACL3Q,KAAK2Q,WAAWF,OAAO,SAAUzQ,KAAK4d,iBACtC5d,KAAK2Q,WAAWF,OAAO,WAAYzQ,KAAK6d,kBACxC7d,KAAK2Q,WAAWF,OAAO,QAASzQ,KAAK8d,gBAErC9d,KAAKwR,eACLxR,KAAKwR,aAAaf,OAAO,SAAUzQ,KAAK+d,2BACxC/d,KAAKwR,aAAaf,OAAO,QAASzQ,KAAKge,0BAEvChe,KAAKkK,WACLlK,KAAKkK,SAASuG,SACdzQ,KAAKkK,SAASmG,WAEdrQ,KAAKgK,OACLhK,KAAKgK,KAAKyG,SACVzQ,KAAKgK,KAAKqG,WAEVrQ,KAAKie,gBACLje,KAAKie,eAAe5N,UAEpBrQ,KAAKke,gBACLle,KAAKke,eAAe7N,UAEpBrQ,KAAKme,SACLne,KAAKme,QAAQ9N,UAEbrQ,KAAKoe,kBACLpe,KAAKoe,iBAAiB/N,UAE1BrQ,KAAKiM,QAAQuE,IAAIjJ,GACbpB,IACAnG,KAAKqe,YAAYC,eAAete,KAAKue,oBACrCve,KAAKqe,YAAc,MAEvB7gB,EAAEiC,QAAQ+Q,IAAI,SAAWjJ,EAAIvH,KAAKwe,gBAClChhB,EAAEwC,KAAKyE,SAAS+L,IAAIjJ,GACpBvH,KAAKiM,QAAU,KACfjM,KAAKye,OAAS,MAElBC,WAAY,SAAUzd,GAAV,GAIA0d,GAHJC,EAAalf,EAAM0C,cAAepC,KAAKiB,QAASA,GAChDgY,EAASjZ,KAAKib,OACbha,GAAQoL,QACLsS,EAAe3e,KAAK0R,OAAO3O,KAC/B6b,EAAWvS,MAAQ7O,EAAE8C,IAAIN,KAAKiB,QAAQoL,MAAO,SAAUqF,GAAV,GACrCmN,GAAa5X,EAAcyK,GAC3B3O,EAAO8b,EAAkC,gBAAdnN,GAAK3C,KAAoB2C,EAAK1I,MAAQ0I,EAAK3C,KAAO2C,CAajF,OAZIiN,KAAiB5b,EACb8b,EACAnN,EAAKxG,UAAW,EAEhBwG,GACI3C,KAAMhM,EACNmI,UAAU,GAGX2T,IACPnN,EAAKxG,UAAW,GAEbwG,KAGVzQ,EAAQ0P,aACTiO,EAAWjO,WAAa3Q,KAAK2Q,YAE5B1P,EAAQuQ,eACToN,EAAWpN,aAAexR,KAAKwR,cAE9BvQ,EAAQkV,YACTyI,EAAWzI,UAAYnW,KAAKmW,WAE3BlV,EAAQsb,cACTqC,EAAWrC,YAAcvc,KAAKuc,aAElCvc,KAAKqQ,UACLrQ,KAAKoF,QAAQ0Z,QACb9e,KAAKiB,QAAU,KACfjB,KAAKF,KAAKE,KAAKoF,QAASwZ,EAAY3F,GACpCzS,EAAO0G,GAAG6R,WAAW5R,KAAKnN,KAAM4e,IAEpC3C,cAAe,WACXjc,KAAKwe,eAAiBxX,EAAMhH,KAAK+Z,OAAQ/Z,MAAM,GAC/CxC,EAAEiC,QAAQiP,GAAG,SAAWnH,EAAIvH,KAAKwe,iBAErCtD,SAAU,WAAA,GACFrR,GAAc7D,EAAMwD,OACpBwV,GACAnV,EAAYY,KACZZ,EAAYW,cACdnM,KAAK,KACH4C,EAAUjB,KAAKiB,QACfnC,EAASmC,EAAQnC,OACjBD,EAAQoC,EAAQpC,KACpBmB,MAAKyE,QAAUzE,KAAKoF,QAAQyI,SAAShE,EAAYpF,SAAS2J,OAAO,eAAkBvE,EAAYE,YAAc,uBAAwBqE,OAAO,eAAkBvE,EAAYM,gBAAkB,iBAAqB6U,EAA0B,kBAAmB5Q,OAAO,eAAkBvE,EAAYI,gBAAkB,uBACrTjK,KAAKyE,QAAQgJ,KAAKpF,EAAMwB,EAAYG,MAAMnL,MAAMoC,EAAQwc,WACpD3e,GACAkB,KAAKyE,QAAQ3F,OAAOA,GAEpBD,GACAmB,KAAKyE,QAAQ5F,MAAMA,GAEnBoC,EAAQ6I,WACR9J,KAAKyE,QAAQoJ,SAAShE,EAAYC,YAG1CuR,SAAU,WAAA,GAYFpP,GACAI,EACA4S,EAbAnR,EAAO9N,KACP6J,EAAc7D,EAAMwD,OACpB0V,EAAgB7W,EAAMwB,EAAYoC,QAAQI,MAAQ,QAClD8S,EAAc9W,EAAMwB,EAAYoC,QAAQc,UACxCqS,EAAiB/W,EAAMwB,EAAYkB,aACnCsU,EAAkBhX,EAAMwB,EAAYyB,YACpCgU,EAAW9hB,EAAE6K,EAAMwB,EAAYG,MAC/BE,EAAW1M,EAAE6K,EAAMwB,EAAYK,UAC/BqV,EAAmB1V,EAAYoB,QAC/BsB,EAAUvM,KAAKiB,QAAQgL,QACvBuT,EAAchiB,EAAE,eAAkBqM,EAAYoC,QAAQM,QAAU,MAIhEkT,EAAU,SAAUva,GAChBA,EAAEwa,QACFJ,EAASnP,KACLwP,QAAW,OACXC,YAAa,KAGjBN,EAASnP,KACLwP,QAAW,eACX9gB,MAAS,MACT+gB,YAAa,SAEjB1V,EAASiG,IAAI,UAAW,gBACxBrC,EAAK+R,UACL3V,EAASuD,KAAK4R,GAAiBra,UAAU8I,EAAK9I,YAElD8I,EAAKgS,UAEJ/Y,GAAWwF,KACZA,QAAiBA,KAAY5E,EAAS4E,EAAUvM,KAAK+f,SAASxT,GAC9DA,EAAUvF,EAAMtH,EAAM+I,SAAS8D,GAAUvM,OAE7Cif,EAAezhB,EAAEgL,GAAyBgB,OAAQK,EAAYoC,WAC9DI,EAAQ7O,EAAEqL,IACNmX,GAAItgB,EAAMsgB,GACV3T,MAAOrM,KAAKkK,SAASmC,MACrB7C,OAAQK,EAAYoC,WAExBuT,EAAYpR,OAAO7B,OACnBN,EAAUzO,EAAE,eAAkBqM,EAAYoC,QAAQC,cAAgB,MAAOkC,OAAO6Q,GAAc7Q,OAAO/B,GAAO+B,OAAOoR,GAC/GnT,EAAMoB,KAAK,MAAM/O,OAAS,GAC1B2N,EAAM4T,QAAQrX,GAAqBY,OAAQK,EAAYoC,WAE3DjM,KAAKyE,QAAQwb,QAAQhU,GACrBjM,KAAKiM,QAAUA,EACX9F,IACAnG,KAAKue,mBAAqBvX,EAAMyY,EAASzf,MACzCA,KAAKqe,YAAc5e,OAAOygB,WAAW,sBACrClgB,KAAKqe,YAAY8B,YAAYngB,KAAKue,qBAEtCtS,EAAQyC,GAAGjH,EAAQF,EAAI2X,EAAe,SAAUha,GAAV,GAE9B8E,GACAjH,EACA8J,CAHJ3H,GAAE2J,iBACE7E,EAAO8D,EAAK9D,KACZjH,EAAOvF,EAAEwC,MAAM6C,KAAKnD,EAAMmD,KAAK,SAC/BgK,EAAcR,EAAMoB,KAAKpF,EAAMwB,EAAYoC,QAAQY,aACnDA,EAAYuK,GAAG,aACfvK,EAAY7I,SAASoc,YAAYvW,EAAYoC,QAAQG,UAErDpC,EAAKrG,UAAYqG,EAAKrG,SAASmL,QAAQ,cAGtChB,EAAKgB,QAAQ,YAAc4C,KAAM3O,KAClC+K,EAAK4D,KAAK3O,GAEd+K,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYsB,SAASwC,YAAY9D,EAAYsB,YACtEuD,GAAG,UAAYnH,EAAI2X,EAAe,SAAUha,GAAV,GAC7BmH,GAAQ7O,EAAE6K,EAAMwB,EAAYoC,QAAQI,OAAO2G,SAAS,yBACpDqN,EAAmBhU,EAAMtH,MAAM+I,EAAKwS,cAAgBxS,EAAKwS,aAAa,IAAMjU,EAAMqN,QAAQrR,EAAMwB,EAAYqB,UAAU,GACtHhG,GAAEkK,UAAYnJ,EAAKsa,OACnB/iB,EAAEsQ,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYsB,UAAUwC,YAAY9D,EAAYsB,SACxE2C,EAAKwS,aAAuD9iB,EAAxC6iB,EAAmB,IAAMhU,EAAM3N,OAAW2N,EAAM,GAAQA,EAAMgU,EAAmB,IACrGvS,EAAKwS,aAAanb,QAAQ0I,SAAShE,EAAYsB,SAC/CjG,EAAE2J,kBACK3J,EAAEkK,UAAYnJ,EAAKua,MAC1BhjB,EAAEsQ,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYsB,UAAUwC,YAAY9D,EAAYsB,SACxE2C,EAAKwS,aAAwC9iB,EAAJ,IAArB6iB,EAA2BhU,EAAMA,EAAM3N,OAAS,GAAQ2N,EAAMgU,EAAmB,IACrGvS,EAAKwS,aAAanb,QAAQ0I,SAAShE,EAAYsB,SAC/CjG,EAAE2J,kBACK3J,EAAEkK,UAAYnJ,EAAKqJ,MAAQxB,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYoC,QAAQY,aAAa7I,SAASyc,SAAS5W,EAAYoC,QAAQG,WACjI5O,EAAEsQ,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYsB,UAAUwC,YAAY9D,EAAYsB,SACxE2C,EAAKwS,aAAuD9iB,EAAxC6iB,EAAmB,IAAMhU,EAAM3N,OAAW2N,EAAM,GAAQA,EAAMgU,EAAmB,IACrGvS,EAAKwS,aAAanb,QAAQ0I,SAAShE,EAAYsB,SAC/CjG,EAAE2J,kBACK3J,EAAEkK,UAAYnJ,EAAKoJ,IAAMvB,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYoC,QAAQY,aAAa7I,SAASyc,SAAS5W,EAAYoC,QAAQG,WAC/H5O,EAAEsQ,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYsB,UAAUwC,YAAY9D,EAAYsB,SACxE2C,EAAKwS,aAAwC9iB,EAAJ,IAArB6iB,EAA2BhU,EAAMA,EAAM3N,OAAS,GAAQ2N,EAAMgU,EAAmB,IACrGvS,EAAKwS,aAAanb,QAAQ0I,SAAShE,EAAYsB,SAC/CjG,EAAE2J,kBACM3J,EAAEkK,UAAYnJ,EAAKsJ,OAASrK,EAAEkK,UAAYnJ,EAAKya,WAAa5S,EAAKwS,cAGjEpb,EAAEkK,UAAYnJ,EAAKya,UAAYxb,EAAEkK,UAAYnJ,EAAKsJ,OAASrK,EAAEkK,UAAYnJ,EAAKqJ,MAAQpK,EAAEyb,SAAW7S,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYoC,QAAQY,YAAc,QAAQ4T,SAAS5W,EAAYsB,UAClM2C,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYoC,QAAQY,aAAa7I,SAASoc,YAAYvW,EAAYoC,QAAQG,UAClGlH,EAAE2J,kBACK3J,EAAEkK,UAAYnJ,EAAKuJ,KAAO1B,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYoC,QAAQY,aAAa7I,SAASyc,SAAS5W,EAAYoC,QAAQG,WAChI0B,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYoC,QAAQY,aAAa7I,SAASoc,YAAYvW,EAAYoC,QAAQG,UAAUwU,OAC5G9S,EAAKwS,aAAe,KACpBxS,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYoC,QAAQY,YAAc,QAAQgB,SAAShE,EAAYsB,SAAShG,QAChGD,EAAE2J,kBACK3J,EAAEkK,SAAW,IAAMlK,EAAEkK,SAAW,IACvCtB,EAAK4D,KAAK5D,EAAK5D,SAAS2W,aAAa3b,EAAEkK,QAAU,MAXjDtB,EAAK4D,KAAK5D,EAAKwS,aAAa5iB,OAAOojB,eACnC5b,EAAE2J,oBAYPH,GAAGjH,EAAQF,EAAI4X,EAAa,SAAUja,GACrCA,EAAE2J,iBACFf,EAAKiT,cACNrS,GAAGjH,EAAQF,EAAI6X,EAAgB,SAAUla,GACxCA,EAAE2J,iBACEyQ,EAASlI,GAAG,aACZkI,EAASnP,KACLwP,QAAW,OACX9gB,MAAS,MAEbqL,EAASiG,KACLwP,QAAW,eACX9gB,MAAS,SAEbiP,EAAK+R,UACL3V,EAASuD,KAAK4R,GAAiBra,UAAU8I,EAAK9I,aAE9CkF,EAASiG,KACLwP,QAAW,OACX9gB,MAAS,IAEbygB,EAASnP,KACLwP,QAAW,eACX9gB,MAAS,OACT+gB,YAAa,SACdnS,KAAK4R,GAAiBra,UAAU8I,EAAK9I,YAE5C8I,EAAKgS,YAET9f,KAAKyE,QAAQiK,GAAG,WAAanH,EAAI,SAAUrC,GAClC1H,EAAE0H,EAAE8b,eAAetH,QAAQrR,EAAMwB,EAAYoC,QAAQA,SAASvN,QAC/DoP,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYsB,SAASwC,YAAY9D,EAAYsB,SAEpE3N,EAAE0H,EAAE8b,eAAetH,QAAQrR,EAAMwB,EAAYoC,QAAQI,OAAO3N,QAC7DoP,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYoC,QAAQI,OAAOsB,YAAY9D,EAAYoC,QAAQG,YAExFqB,KAAKpF,EAAMwB,EAAYoC,QAAQA,QAAU,OAAOgD,MAAM,WACrDzR,EAAEwC,MAAM6N,SAAS0R,IAClB,WACC/hB,EAAEwC,MAAM2N,YAAY4R,MAG5BQ,SAAU,WAAA,GAYGvhB,GAAOE,EAXZuC,EAAUjB,KAAKiB,QACf0C,EAAW1C,EAAQ0C,SACnB4I,EAAUtL,EAAQgL,QAClB8K,EAAO,EACX,KAAKlQ,EAAQ0F,GAAU,CACnB,IAAI5I,GAAYA,EAASkO,UAAW,EAGhC,MAAOkF,EAFPxK,IAAW,UAKnB,IAAS/N,EAAI,EAAGE,EAAS6N,EAAQ7N,OAAQF,EAAIE,EAAQF,IACjDuY,GAAQ/W,KAAKihB,cAAc1U,EAAQ/N,GAEvC,OAAOuY,IAEXuE,QAAS,WAAA,GAKDzR,GACAH,EACAD,EACA+V,EACAf,EARA9a,EAAW3D,KAAKiB,QAAQ0C,QACvBA,IAAYA,EAASkO,UAAW,IAGjChI,EAAc7D,EAAMwD,OAAOyC,QAC3BvC,EAAW1J,KAAKiB,QAAQyI,SAAS6C,QACjC9C,EAASjM,EAAEkC,EAAM+I,SAASC,GAAiB7I,GAAO,GAAQ2J,OAAQK,GAAepE,EAAgB2I,QAAU1Q,KAAMgM,EAAS0E,WAC1HoR,EAAchiB,EAAE,eAAkBqM,EAAY0C,QAAU,MAAO6B,OAAO3E,GACtEgV,EAASjhB,EAAE,eAAkBqM,EAAYsC,cAAgB,MAAOiC,OAAOoR,GAC3Exf,KAAKyE,QAAQ2J,OAAOqQ,GACpBze,KAAKye,OAASA,IAElBwC,cAAe,SAAUC,GAAV,GACPzY,GAAWyY,EAAQzY,UAAYC,EAC/BgB,EAAW1J,KAAKiB,QAAQyI,SAAS6C,QACjC4U,QAAqBD,KAAYvZ,EAASuZ,EAAUA,EAAQne,MAAQme,EAAQxjB,KAC5E2a,EAAY5S,EAAgB0b,GAAe1b,EAAgB0b,GAAa9I,UAAY,YAAc8I,GAAe,IAAIvjB,QAAQ,MAAO,IACpIqD,GACA8Z,UAAW,GACXnM,OAAQ,GACRlR,KAAMyjB,EACN9I,UAAWA,EACX7O,OAAQxD,EAAMwD,OAAOyC,QAEzB,MAAKkV,GAAiBla,EAAcia,IAAYA,EAAQzY,UACpD,KAAUmI,OAAM;AASpB,MAPA3P,GAAUpB,GAAO,EAAMoB,EAASwE,EAAgB0b,IAAgBzjB,KAAMgM,EAASyX,KAC3Ela,EAAcia,KACVA,EAAQ7I,WAAavR,EAAQ7F,EAAQoX,UAAW6I,EAAQ7I,UAAU+I,MAAM,MAAQ,IAChFF,EAAQ7I,WAAa,IAAMpX,EAAQoX,WAEvCpX,EAAUpB,GAAO,EAAMoB,EAASigB,IAE7BxhB,EAAM+I,SAASA,GAAUxH,IAEpCsa,kBAAmB,WAAA,GACXnW,GAAUpF,KAAKoF,QACfyE,EAAc7D,EAAMwD,OACpB6X,EAAehZ,EAAMwB,EAAYG,KACjCsX,EAAmBjZ,EAAMwB,EAAYK,SACrCqX,EAAmBlZ,EAAMwB,EAAYO,SACrCoX,EAAgBpa,EAAYpH,KAAKiM,SACjCwV,EAAezhB,KAAKye,OAASrX,EAAYpH,KAAKye,QAAU,EACxDiD,EAActc,EAAQtG,SACtB6iB,EAAavc,EAAQvG,QACrB+iB,EAAgB1a,EAAW9B,EAAQqI,KAAK8T,IACxCM,EAAgB3a,EAAW9B,EAAQqI,KAAK4T,GAC5Cjc,GAAQ4N,UACJqO,EACAC,EACAC,GACFljB,KAAK,MAAMS,OAAO4iB,GAAeF,EAAgBC,IAAetd,MAAM6O,SAASsO,GAAkBziB,MAAM8iB,GAAcC,EAAgBC,IACnIF,EAAaE,EAAgBD,GAC7Bxc,EAAQqI,KAAK4T,GAAcxiB,MAAM8iB,EAAaC,IAGtDE,UAAW,SAAUzhB,GAAV,GAKHuO,GACAmT,EALArQ,EAAO1R,KAAKkK,SAASwH,OACrB1H,EAAOhK,KAAKgK,KACZnH,EAAOnD,EAAMmD,KAAK,OAClBkO,EAAsB,gBAAV1Q,GAAqBA,EAAQA,EAAMqZ,QAAQ,KAAO/W,KAAYE,KAAKA,GAG/Emf,EAAiB,WACW,IAAxBD,EAAarjB,QACbkQ,IAGJ8C,GAAKuQ,QAAQ7K,GAAG,aAChB2K,EAAerQ,EAAKuQ,QAAQxU,KAAK9K,EAASoO,IAC1CnC,EAAS,WACL8C,EAAKoQ,UAAUC,MAGnBA,EAAe/X,EAAKiY,QAAQxU,KAAK9K,EAASoO,IAC1CnC,EAAS,WACLmT,EAAanhB,IAAI,GAAGohB,mBAG5BA,KAEJpG,WAAY,WAAA,GACJ9N,GAAO9N,KACPkiB,EAAkB7Z,EAAMrC,EAAMwD,OAAOyC,QAAQM,QAC7C4V,EAAiBniB,KAAKiB,QAAQyI,SAAS6C,QACvCrC,EAAWlK,KAAKkK,SAChBvG,EAAW3D,KAAKiB,QAAQ0C,SACxB8b,EAAU,SAAUva,GAAV,GAENsN,GADAzD,EAAO7J,EAAE6J,KAET4B,EAAa7C,EAAK6C,WAClBoC,EAAOpC,EAAWoF,kBAClB7K,EAAW4C,EAAKwM,SAASxM,EAAK4P,UAC9B1Z,EAAS2M,EAAW8C,WAAWvI,GAC/BkX,EAAYlY,EAASwH,OAAO2Q,aAAa,GACzC1T,EAAkB,QAATI,EAAiB7D,EAAWlH,EACrCL,EAAWmK,EAAK9D,KAAKrG,QACrBA,IAAYA,EAASmL,QAAQ,cAGjCiE,EAAKX,IAAI,QAAS,YACdzD,GACAoE,EAAKX,IAAI,WAAYzD,EAAO/N,IAAI,OAChCmS,EAAKX,IAAI,QAASzD,EAAO/N,IAAI,UAC7BmS,EAAKX,IAAI,MAAOzD,EAAO/N,IAAI,UAE3BmS,EAAKX,IAAI,QAASgQ,EAAUle,OAC5B6O,EAAKX,IAAI,MAAOgQ,EAAUje,MAEjB,QAAT4K,IACAyD,EAAUtH,EAAStK,IAAI,WACvB4R,EAAmB,kBAATzD,EAA2ByD,EAAUA,EAAU,GAE7D1E,EAAKwU,YAAYvP,EAAMP,IAEtB7O,IAAYA,EAASkO,UAAW,IAGrC7R,KAAKie,eAAiB,GAAIhR,IAAajN,KAAKye,OAAOzL,SAASkP,GAAiB3K,GAAG,IAC5E7N,UAAY6C,QAAS4V,GACrB9U,UAAW,KACXoB,WAAaF,MAAQgU,QAAS,eAC9BjV,YAAaQ,EAAK7M,QAAQqM,cAE9BtN,KAAKke,eAAiB,GAAIjR,IAAajN,KAAKiM,QAAQ+G,SAASkP,GAAiB3K,GAAG,IAC7E7N,UAAY6C,QAAS4V,GACrB7U,YAAaQ,EAAK7M,QAAQqM,cAE9BtN,KAAKie,eAAe9O,KAAK,UAAWsQ,GACpCzf,KAAKke,eAAe/O,KAAK,UAAWsQ,KAExC5D,MAAO,WAAA,GAmBC2G,GAQKhkB,EA1BLsP,EAAO9N,KACPsN,EAAcQ,EAAK7M,QAAQqM,YAC3BzD,EAAc7D,EAAMwD,OACpBO,EAAc/J,KAAKyE,QAAQgJ,KAAKpF,EAAMwB,EAAYG,MAClD5E,EAAU2E,EAAY0D,KAAK,SAC3BgV,EAAgBziB,KAAKyE,QAAQgJ,KAAKpF,EAAMwB,EAAYoC,QAAQM,QAAU,aACtEtL,GACAkZ,QAASna,KAAKiB,QAAQkZ,YACtBxJ,WAAY3Q,KAAK2Q,WACjB0L,WAAYrc,KAAKiB,QAAQob,WACzB1Y,SAAU3D,KAAKiB,QAAQ0C,SACvBkU,UAAW7X,KAAKiB,QAAQ4W,UACxByE,wBAAyBtc,KAAKiB,QAAQqb,wBACtCmB,UAAWvW,EAAW6C,GACtBiC,eAAgBhM,KAAKmW,UAAU5S,MAC/BuG,UAAW9J,KAAKiB,QAAQ6I,WAExBqQ,EAAUlZ,EAAQkZ,QAElBuI,EAAe,WACXpV,IACAQ,EAAKP,SAASO,EAAK6U,gBACnBre,EAAWwJ,EAAK9D,KAAKiY,QAAQxU,KAAK,UAAU,UAEzCK,GAAK6U,eAEhB,KAASnkB,EAAI,EAAGA,EAAI2b,EAAQzb,OAAQF,IAChCgkB,EAASrI,EAAQ3b,GACbgkB,EAAOjf,QAAUvD,KAAKmW,UAAU5S,OAAkC,kBAAlBif,GAAOnM,SACvDmM,EAAOnM,OAASrP,EAAMhH,KAAK4iB,sBAAuB5iB,MAG1DA,MAAKgK,KAAO,GAAItK,GAAMqE,GAAG8e,UAAUzd,EAASnE,GAC5CjB,KAAKgK,KAAKmF,KAAK,SAAU,WACrBrB,EAAKgV,iBACN,GAAM3T,KAAK,OAAQ,SAAUjK,GAC5B4I,EAAK6U,eAAiBzd,EAAE6d,KACpBjV,EAAKgB,QAAQ,QACTiE,KAAM7N,EAAEtB,MACRF,UAAWwB,EAAE6d,QAEjB7d,EAAE2J,mBAEPM,KAAK,SAAU,SAAUjK,GACpB4I,EAAKgB,QAAQ,UACTiE,KAAM7N,EAAEtB,MACRF,UAAWwB,EAAE6d,QAEjB7d,EAAE2J,iBAEN6T,MACDvT,KAAK,SAAU,SAAUjK,GACxB4I,EAAKkV,YAAY9d,EAAE6N,KAAM7N,EAAE4T,YAC3B4J,MACDvT,KAAK,SAAU,WACdrB,EAAKgB,QAAQ,SACb,IAAImU,GAAYnV,EAAK9D,KAAK0T,QACtBuF,GAAUvkB,QACV+jB,EAAc7U,WAAW,cAAe,OACxCE,EAAK5D,SAASwT,OAAO,cAAiBuF,EAAUpgB,KAAK,YAAc,QAEnE4f,EAAc5f,KAAK,cAAe,OAClCiL,EAAK5D,SAASyT,oBAEnBxO,KAAK,eAAgB,SAAUjK,GAC9B4I,EAAKgB,QAAQ,gBACT0T,OAAQtd,EAAEsd,OACVU,SAAUhe,EAAEge,SACZC,SAAUje,EAAEie,cAIxB/H,UAAW,WAAA,GACHtN,GAAO9N,KACP6J,EAAc7D,EAAMwD,OACpBvI,EAAU6B,EAAYjD,GAAO,GAAQmM,eAAgBhM,KAAKmW,UAAU5S,OAASvD,KAAKiB,UAClFmE,EAAUpF,KAAKyE,QAAQgJ,KAAKpF,EAAMwB,EAAYK,SAAW,UACzDkZ,EAAsB/a,EAAMwB,EAAYoC,QAAQY,YAAc,MAAQxE,EAAMwB,EAAYoC,QAAQa,IACpG9M,MAAKkK,SAAW,GAAIxK,GAAMqE,GAAGsf,cAAcje,EAASnE,GACpDjB,KAAKkK,SAASiF,KAAK,WAAY,SAAUjK,GAAV,GACvBoe,GAAWpe,EAAEwM,KAAK9T,QAAQ,MAAO,OAAOkjB,cACxCpjB,EAAOoQ,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYoC,QAAQI,MAAQ,SAASsB,YAAY9D,EAAYqB,UAAU/G,MAAMsJ,KAAKpF,EAAMwB,EAAYoC,QAAQW,WAAa,IAAM0W,GAAUzV,SAAShE,EAAYqB,UAAUuC,KAAKpF,EAAMwB,EAAYoC,QAAQa,MAAMpP,MAChPoQ,GAAK7B,QAAQwB,KAAK2V,GAAqB1lB,KAAKA,GAC5CoQ,EAAK+R,YACN1Q,KAAK,YAAa,SAAUjK,GAC3B,GAAIvB,GAAWmK,EAAK9D,KAAKrG,QACzB,OAAIA,IAAYA,EAASmL,QAAQ,aAC7B5J,EAAE2J,iBACF,IAEAf,EAAKgB,QAAQ,aAAeiE,KAAM7N,EAAE6N,QACpC7N,EAAE2J,iBADN,KAGDM,KAAK,OAAQ,SAAUjK,GAAV,GACR6N,GAAO7N,EAAE6N,KACT7O,EAAQgB,EAAEhB,MACVC,EAAM,GAAI+N,MAAKhO,EAAMiO,UAAYY,EAAKjB,WACtChE,GAAKgB,QAAQ,QACTiE,KAAMA,EACN7O,MAAOA,EACPC,IAAKA,KAETe,EAAE2J,mBAEPM,KAAK,UAAW,SAAUjK,GAAV,GACX6N,GAAO7N,EAAE6N,KACT7O,EAAQgB,EAAEhB,MACVC,EAAM,GAAI+N,MAAKhO,EAAMiO,UAAYY,EAAKjB,WACrChE,GAAKgB,QAAQ,WACViE,KAAMA,EACN7O,MAAOA,EACPC,IAAKA,KAET2J,EAAKkV,YAAYlV,EAAK6C,WAAWgJ,SAAS5G,EAAKnQ,MAC3CsB,MAAOA,EACPC,IAAKA,MAGdgL,KAAK,cAAe,SAAUjK,GAC7B,GAAIvB,GAAWmK,EAAK9D,KAAKrG,QACzB,OAAIA,IAAYA,EAASmL,QAAQ,aAC7B5J,EAAE2J,iBACF,IAEAf,EAAKgB,QAAQ,eAAiBiE,KAAM7N,EAAE6N,QACtC7N,EAAE2J,iBADN,KAGDM,KAAK,SAAU,SAAUjK,GACpB4I,EAAKgB,QAAQ,UACTiE,KAAM7N,EAAE6N,KACR7O,MAAOgB,EAAEhB,MACTC,IAAKe,EAAEf,OAEXe,EAAE2J,mBAEPM,KAAK,YAAa,SAAUjK,GAAV,GACb6N,GAAO7N,EAAE6N,KACT+F,IACA5T,GAAEqe,YACFzK,EAAW5U,MAAQgB,EAAEhB,MAErB4U,EAAW3U,IAAMe,EAAEf,IAElB2J,EAAKgB,QAAQ,aACViE,KAAMA,EACN7O,MAAOgB,EAAEhB,MACTC,IAAKe,EAAEf,OAEX2J,EAAKkV,YAAYlV,EAAK6C,WAAWgJ,SAAS5G,EAAKnQ,KAAMkW,KAE1D3J,KAAK,qBAAsB,SAAUjK,GACpC,GAAIvB,GAAWmK,EAAK9D,KAAKrG,QACrBA,IAAYA,EAASmL,QAAQ,aAC7B5J,EAAE2J,mBAEPM,KAAK,mBAAoB,SAAUjK,GAClC4I,EAAKkV,YAAYlV,EAAK6C,WAAWgJ,SAASzU,EAAE6N,KAAKnQ,MAAQ8P,gBAAiBxN,EAAEwN,oBAC7EvD,KAAK,sBAAuB,SAAUjK,GACrC,GAAIvB,GAAWmK,EAAK9D,KAAKrG,QACrBA,IAAYA,EAASmL,QAAQ,aAC7B5J,EAAE2J,mBAEPM,KAAK,oBAAqB,SAAUjK,GACnC,GAAIse,GAAa1V,EAAK0D,aAAauE,iBAC/BhH,KAAM7J,EAAE6J,KACRiC,cAAe9L,EAAEue,YAAY1S,GAC7BE,YAAa/L,EAAEwe,UAAU3S,IAE7BjD,GAAK6V,kBAAkBH,KACxBrU,KAAK,SAAU,SAAUjK,GACxB,GAAIvB,GAAWmK,EAAK9D,KAAKrG,QACrBA,IACAA,EAASmL,QAAQ,YAErBhB,EAAK4P,OAAO,cAAiBxY,EAAEtC,IAAM,QACtCuM,KAAK,WAAY,SAAUjK,GAC1B,GAAIvB,GAAWmK,EAAK9D,KAAKrG,QACrBA,IAAYA,EAASmL,QAAQ,aAGjChB,EAAKoJ,SAAShS,EAAEtC,OACjBuM,KAAK,QAAS,WACbrB,EAAK6P,mBACNxO,KAAK,aAAc,SAAUjK,GAC5B,GAAIvB,GAAWmK,EAAK9D,KAAKrG,QACrBA,IAAYA,EAASmL,QAAQ,aAGjChB,EAAK8V,WAAW9V,EAAK6C,WAAWgJ,SAASzU,EAAEtC,QAC5CuM,KAAK,mBAAoB,SAAUjK,GAClC,GAAIvB,GAAWmK,EAAK9D,KAAKrG,QACrBA,IAAYA,EAASmL,QAAQ,aAGjChB,EAAK+V,iBAAiB/V,EAAK0D,aAAamI,SAASzU,EAAEtC,SAG3D8Y,YAAa,WAAA,GACLza,GAAUjB,KAAKiB,QACf0P,EAAa1P,EAAQ0P,UACzBA,GAAa9J,EAAQ8J,IAAgB1M,KAAM0M,GAAeA,EACtD3Q,KAAK2Q,YAAc3Q,KAAK4d,gBACxB5d,KAAK2Q,WAAWF,OAAO,SAAUzQ,KAAK4d,iBAAiBnN,OAAO,WAAYzQ,KAAK6d,kBAAkBpN,OAAO,QAASzQ,KAAK8d,gBAEtH9d,KAAK4d,gBAAkB5W,EAAMhH,KAAK6f,QAAS7f,MAC3CA,KAAK6d,iBAAmB7W,EAAMhH,KAAK8jB,cAAe9jB,MAClDA,KAAK8d,cAAgB9W,EAAMhH,KAAK+jB,OAAQ/jB,OAE5CA,KAAK2Q,WAAajR,EAAMuE,KAAK0B,gBAAgBkM,OAAOlB,GAAYxB,KAAK,SAAUnP,KAAK4d,iBAAiBzO,KAAK,WAAYnP,KAAK6d,kBAAkB1O,KAAK,QAASnP,KAAK8d,gBAEpKxM,cAAe,WAAA,GACPE,GAAexR,KAAKiB,QAAQuQ,iBAC5Bb,EAAa9J,EAAQ2K,IAAkBvN,KAAMuN,GAAiBA,CAC9DxR,MAAKwR,cAAgBxR,KAAK+d,0BAC1B/d,KAAKwR,aAAaf,OAAO,SAAUzQ,KAAK+d,2BAA2BtN,OAAO,QAASzQ,KAAKge,0BAExFhe,KAAK+d,0BAA4B/W,EAAMhH,KAAKgkB,oBAAqBhkB,MACjEA,KAAKge,wBAA0BhX,EAAMhH,KAAK+jB,OAAQ/jB,OAEtDA,KAAKwR,aAAe9R,EAAMuE,KAAKiN,0BAA0BW,OAAOlB,GAAYxB,KAAK,SAAUnP,KAAK+d,2BAA2B5O,KAAK,QAASnP,KAAKge,0BAElJ7C,WAAY,WAAA,GACJhF,GAAYnW,KAAKiB,QAAQkV,UACzBxF,EAAawF,EAAUxF,cAC3B3Q,MAAKmW,WACD5S,MAAO,YACP0gB,cAAe,OACfC,eAAgB,QAChBC,gBAAiB,UAErBtkB,EAAOG,KAAKmW,UAAWA,GACvBnW,KAAKmW,UAAUxF,WAAajR,EAAMuE,KAAKwC,WAAWoL,OAAOlB,IAE7DgL,aAAc,WAAA,GACNY,GAAcvc,KAAKiB,QAAQsb,YAC3B5L,EAAa4L,EAAY5L,cACzB3Q,MAAKuc,YACLvc,KAAKuc,YAAY5L,WAAWF,OAAO,SAAUzQ,KAAKokB,4BAElDpkB,KAAKokB,2BAA6Bpd,EAAMhH,KAAK6f,QAAS7f,MAE1DA,KAAKuc,aACD8H,gBAAiB,SACjBC,oBAAqB,aACrBC,eAAgB,SAEpB1kB,EAAOG,KAAKuc,YAAaA,GACzBvc,KAAKuc,YAAY5L,WAAajR,EAAMuE,KAAKwC,WAAWoL,OAAOlB,GAC3D3Q,KAAKuc,YAAY5L,WAAWxB,KAAK,SAAUnP,KAAKokB,6BAEpDlI,cAAe,WAAA,GACPpO,GAAO9N,KACPqW,EAASrW,KAAKme,QAAU,GAAIrY,GAAY9F,KAAKyE,QAAS5E,KAAWG,KAAKiB,SACtE0N,OAAQ3O,KACRmW,WACI5S,MAAOvD,KAAKmW,UAAU5S,MACtB8S,OAAQrP,EAAMhH,KAAK4iB,sBAAuB5iB,OAE9CoW,aAAcpP,EAAMhH,KAAKwkB,mBAAoBxkB,QAEjDqW,GAAOlH,KAAK,SAAU,SAAUjK,GAC5B,GAAI6N,GAAOjF,EAAK6C,WAAWgJ,SAASzU,EAAEtB,MAAMhB,IAC5C,OAAIkL,GAAKgB,QAAQ,UACTpL,UAAWwB,EAAExB,UACbqP,KAAMA,KAEV7N,EAAE2J,iBACF,IAEJf,EAAK2W,aAAL3W,KACDqB,KAAK,OAAQ,SAAUjK,GACtB,GAAI6N,GAAOjF,EAAK6C,WAAWgJ,SAASzU,EAAEtB,MAAMhB,IACxCkL,GAAKgB,QAAQ,QACTpL,UAAWwB,EAAExB,UACbqP,KAAMA,KAEV7N,EAAE2J,mBAEPM,KAAK,OAAQ,SAAUjK,GACtB,GAAI6N,GAAOjF,EAAK6C,WAAWgJ,SAASzU,EAAEtB,MAAMhB,IAC5CkL,GAAK4W,SAAS3R,EAAM7N,EAAE4T,cACvB3J,KAAK,SAAU,SAAUjK,GACxB4I,EAAK8V,WAAW1e,EAAEtB,MAAMhB,OACzBuM,KAAK,QAASrB,EAAK6W,iBAE1BA,eAAgB,aAEhB/B,sBAAuB,SAAUlf,EAAWzC,GAArB,GAWfoV,GAVAvI,EAAO9N,KACP4D,EAAQ3C,YAAmByF,GAAmBzF,EAAUA,EAAQ2C,MAChEmN,EAAKnN,EAAMhD,IAAI,MACf8I,EAAW1J,KAAKiB,QAAQyI,SACxBsC,EAAiBhM,KAAKmW,UAAU5S,MAChCqX,GAAoB/H,KAAM,KAC1B+R,EAAmB5kB,KAAKuc,YAAY5L,WAAW1P,QAAQkQ,OAAOvN,KAC9DghB,IAAoBA,EAAiB1b,OAAO2b,OAASD,EAAiB1b,OAAO2b,MAAM1b,YACnFtJ,GAAO,EAAM+a,EAAiBgK,EAAiB1b,OAAO2b,MAAM1b,YAE5DkN,EAASrW,KAAK8kB,gBAAkB,GAAI/e,GAAerC,GACnDsI,eAAgBA,EAChB4O,gBAAiBA,EACjB3W,KAAMjE,KAAK+kB,kBAAkBhU,GAC7BnN,MAAOA,EACP8F,SAAU7J,KAAW6J,EAAS2M,QAC9BiB,UAEQvU,KAAM,SACNrF,KAAMgM,EAAS0O,KACfC,UAAWrS,EAAMwD,OAAOwB,UAGxBjI,KAAM,SACNrF,KAAMgM,EAAS4O,SAGvBlC,aAAcpP,EAAMhH,KAAKwkB,mBAAoBxkB,MAC7CoY,KAAM,SAAUlT,GACZ4I,EAAKkX,mBAAmB9f,EAAEtB,MAAMhD,IAAI,MAAOsE,EAAEtB,MAAMhD,IAAIoL,OAG/DqK,EAAO9H,QAEXiW,mBAAoB,SAAUtD,GAAV,GACZC,GAAcD,EAAQne,MAAQme,EAAQxjB,KACtCuD,GACAoX,UAAWrS,EAAMwD,OAAOgC,MAAM/B,OAAS,aAAe0X,GAAe,IAAIvjB,QAAQ,MAAO,IACxFF,KAAMyjB,EACNte,KAAM,GAEV,MAAKse,GAAiBla,EAAcia,IAAYA,EAAQzY,UACpD,KAAUmI,OAAM,6CAQpB,OANI3J,GAAcia,KACVA,EAAQ7I,YACR6I,EAAQ7I,WAAa,IAAMpX,EAAQoX,WAEvCpX,EAAUpB,GAAO,EAAMoB,EAASigB,IAE7BxhB,EAAM+I,SAASE,GAAyB1H,IAEnDyQ,KAAM,SAAU3C,GACZ,MAAO/O,MAAKkK,SAASwH,KAAK3C,IAE9BkW,MAAO,SAAUA,GAAV,GACCtU,GAAa3Q,KAAK2Q,WAClBe,EAAO1R,KAAK0R,OACZxH,EAAWlK,KAAKkK,QASpB,OARI+a,KACAvT,EAAKzQ,QAAQgkB,OACT/gB,MAAO+gB,EAAM/gB,MACbC,IAAK8gB,EAAM9gB,KAEf+F,EAASgb,QAAQvU,EAAWuD,YAC5BhK,EAASib,oBAAoBnlB,KAAKwR,aAAaE,UAG/CxN,MAAOwN,EAAKxN,MACZC,IAAKuN,EAAKvN,MAGlBihB,KAAM,SAAUA,GACZ,GAAI1T,GAAO1R,KAAK0R,MAKhB,OAJI0T,KACA1T,EAAKzQ,QAAQmkB,KAAOA,EACpB1T,EAAK2T,cAAcD,IAEhB1T,EAAKzQ,QAAQmkB,MAExB9K,SAAU,SAAUja,GAAV,GAIF2J,GACA5E,CAJJ,OAAK/E,IAGD2J,EAAOhK,KAAKgK,KACZ5E,EAAU4E,EAAKiY,QAAQxU,KAAKpN,GACzB2J,EAAKsb,kBAAkBlgB,IAJnB,MAMfmgB,cAAe,SAAU5U,GACrB3Q,KAAKiB,QAAQ0P,WAAaA,EAC1B3Q,KAAK0b,cACL1b,KAAKgK,KAAKwb,eAAexlB,KAAK2Q,YAC1B3Q,KAAKiB,QAAQmb,UACbzL,EAAW8U,SAGnBC,0BAA2B,SAAUlU,GACjCxR,KAAKiB,QAAQuQ,aAAeA,EAC5BxR,KAAKsR,gBACDtR,KAAKiB,QAAQmb,UACb5K,EAAaiU,SAGrBE,MAAO,WACH,MAAO3lB,MAAKyE,QAAQuO,SAAS,YAEjCgS,mBAAoB,SAAUjU,EAAIoF,GAa9B,IAbgB,GAUZyP,GACAC,EACAxlB,EAGS7B,EAAOE,EAgBXonB,EAAOC,EA9BZpV,EAAa3Q,KAAKuc,YAAY5L,WAC9BgD,EAAS3T,KAAKuc,YAAY8H,gBAC1B2B,EAAahmB,KAAKuc,YAAY+H,oBAC9B2B,GAAW,EACX1J,EAAc,GAAI3V,GAAM+J,EAAWe,QAAQ7N,QAC3CN,MAAOoQ,EACPhC,SAAU,KACVtR,MAAO0Q,IACRa,UAII2K,EAAY7d,QAAQ,CAEvB,IADAknB,EAAarJ,EAAY,GAChB/d,EAAI,EAAGE,EAASyX,EAAUzX,OAAQF,EAAIE,EAAQF,IAEnD,GADAqnB,EAAW1P,EAAU3X,GACjBonB,EAAWhlB,IAAIolB,KAAgBH,EAASjlB,IAAI,MAAO,CACnDP,EAAQ8V,EAAU3X,GAAGoC,IAAI,SACzBZ,KAAKkmB,kBAAkBN,EAAYvlB,GACnC8V,EAAUgQ,OAAO3nB,EAAG,GACpBynB,GAAW,CACX,OAGHA,GACDjmB,KAAKomB,kBAAkBR,GAE3BK,GAAW,EACX1J,EAAY8J,QAEhB,IAASP,EAAI,EAAGC,EAAY5P,EAAUzX,OAAQonB,EAAIC,EAAWD,IACzDD,EAAW1P,EAAU2P,GACrB9lB,KAAKsmB,kBAAkBT,EAAU9U,EAErCJ,GAAW4V,QAEf9B,WAAY,WAAA,GACJpO,GAASrW,KAAKme,QACdza,EAAY2S,EAAO3S,SACnBA,IACA2S,EAAOnH,SAGfgI,SAAU,SAAUtU,GAAV,GAKF4jB,GAJAzT,EAAsB,gBAARnQ,GAAmB5C,KAAK2Q,WAAWgJ,SAAS/W,GAAOA,CAChEmQ,KAGDyT,EAAWxmB,KAAK2Q,WAAWoF,gBAAgBhD,EAAK0T,UACpDD,EAAS5jB,IAAMmQ,EAAKnQ,IACpB5C,KAAKykB,aACLzkB,KAAK0mB,UAAUF,KAEnBE,UAAW,SAAU3T,GACjB/S,KAAKme,QAAQjH,SAASnE,IAE1B2R,SAAU,SAAU3R,EAAM+F,GAAhB,GACFzC,GAASrW,KAAKme,QACdza,EAAY2S,EAAO3S,UACnBC,EAAW0S,EAAO1S,QAClBD,IAAaC,GAAYA,EAASQ,OAClCnE,KAAKgjB,YAAYjQ,EAAM+F,IAG/BkK,YAAa,SAAUjQ,EAAM+F,GACzB,GAAI9M,GAAiBhM,KAAKmW,UAAU5S,KAC/BvD,MAAK8O,QAAQ,QACViE,KAAMA,EACN8H,OAAQ/B,MAEZ9Y,KAAKwb,iBAAkB,EACvBxb,KAAK2Q,WAAWyD,OAAOrB,EAAM+F,GACzBA,EAAW9M,IACXhM,KAAKglB,mBAAmBjS,EAAKnS,IAAI,MAAOkY,EAAW9M,IAEvDhM,KAAK2mB,oBAGbT,kBAAmB,SAAUN,EAAYvlB,GACrC,GAAIumB,GAAqB5mB,KAAKuc,YAAYgI,cAC1CqB,GAAWxT,IAAIwU,EAAoBvmB,IAEvCujB,WAAY,SAAUhhB,GAAV,GACJkL,GAAO9N,KACP+S,EAAsB,gBAARnQ,GAAmB5C,KAAK2Q,WAAWgJ,SAAS/W,GAAOA,CAChEmQ,IAGL/S,KAAK6mB,aAAa,SAAUvO,GACnBA,GACDxK,EAAKgZ,YAAY/T,IAEtBA,IAEPuP,YAAa,SAAUvP,EAAMhO,GACzB,IAAK/E,KAAK8O,QAAQ,OACViE,KAAMA,EACNyQ,WAAY,OACZ,CACJ,GAAI7S,GAAa3Q,KAAK2Q,UACtB3Q,MAAKwb,iBAAkB,EACnBzW,IAAUrC,EACViO,EAAWxN,IAAI4P,GAEfpC,EAAW0C,OAAOtO,EAAOgO,GAE7B/S,KAAK+mB,aAAehU,EAAKnQ,IACzB5C,KAAK2mB,oBAGbhD,kBAAmB,SAAUH,GACpBxjB,KAAK8O,QAAQ,OACViE,KAAM,KACNyQ,WAAYA,MAEhBxjB,KAAKgnB,2BAA4B,EACjChnB,KAAKwR,aAAarO,IAAIqgB,GACtBxjB,KAAKgnB,2BAA4B,EACjChnB,KAAKwR,aAAa+U,SAG1BD,kBAAmB,SAAUT,EAAU9U,GAApB,GACXwL,GAAcvc,KAAKuc,YACnB5L,EAAa4L,EAAY5L,WACzBgD,EAAS4I,EAAY8H,gBACrB2B,EAAazJ,EAAY+H,oBACzB2C,EAAgB1K,EAAYgI,eAC5BqB,EAAajV,EAAWoF,iBAC5B6P,GAAWjS,GAAU5C,EACrB6U,EAAWI,GAAcH,EAASjlB,IAAI,MACtCglB,EAAWqB,GAAiBpB,EAASjlB,IAAI,SACzC+P,EAAWxN,IAAIyiB,IAEnB/B,iBAAkB,SAAUjhB,GAAV,GACVkL,GAAO9N,KACPwjB,EAA4B,gBAAR5gB,GAAmB5C,KAAKwR,aAAamI,SAAS/W,GAAOA,CACxE4gB,IAGLxjB,KAAKknB,mBAAmB,SAAU5O,GACzBA,GACDxK,EAAKqZ,kBAAkB3D,IAE5BA,IAEP4D,wBAAyB,SAAUrU,EAAMvB,GACrCxR,KAAKgnB,2BAA4B,CACjC,KAAK,GAAIxoB,GAAI,EAAGE,EAAS8S,EAAa9S,OAAQF,EAAIE,EAAQF,IACtDwB,KAAKwR,aAAavO,OAAOuO,EAAahT,GAE1CwB,MAAKgnB,2BAA4B,EACjChnB,KAAKwR,aAAa+U,QAEtBc,uBAAwB,SAAUtU,GAAV,GAUXvU,GAAOE,EATZiS,EAAa3Q,KAAKuc,YAAY5L,WAC9B4L,EAAc5L,EAAWe,OACzB7N,GACAN,MAAOvD,KAAKuc,YAAY8H,gBACxB1S,SAAU,KACVtR,MAAO0S,EAAKnS,IAAI,MAIpB,KAFA2b,EAAc,GAAI3V,GAAM2V,GAAa1Y,OAAOA,GAAQ+N,UACpD5R,KAAKwb,iBAAkB,EACdhd,EAAI,EAAGE,EAAS6d,EAAY7d,OAAQF,EAAIE,EAAQF,IACrDmS,EAAW1N,OAAOsZ,EAAY/d,GAElCwB,MAAKwb,iBAAkB,EACvB7K,EAAW4V,QAEfO,YAAa,SAAU/T,GACnB,GAAIvB,GAAexR,KAAKwR,aAAaA,aAAauB,EAAKhC,GAClD/Q,MAAK8O,QAAQ,UACViE,KAAMA,EACNvB,aAAcA,MAElBxR,KAAKonB,wBAAwBrU,EAAMvB,GACnCxR,KAAKqnB,uBAAuBtU,GAC5B/S,KAAKwb,iBAAkB,EACnBxb,KAAK2Q,WAAW1N,OAAO8P,IACvB/S,KAAK2mB,kBAET3mB,KAAKwb,iBAAkB,IAG/B2L,kBAAmB,SAAU3D,GACpBxjB,KAAK8O,QAAQ,UACViE,KAAM,KACNvB,cAAegS,MAEfxjB,KAAKwR,aAAavO,OAAOugB,IACzBxjB,KAAKwR,aAAa+U,QAI9BH,kBAAmB,SAAUR,GACzB5lB,KAAKuc,YAAY5L,WAAW1N,OAAO2iB,IAEvCiB,aAAc,SAAU9S,EAAUhB,GAC9B,GAAIrJ,GAAW1J,KAAKiB,QAAQyI,QAC5B1J,MAAKsnB,SAASvT,GACVnQ,MAAOmP,EACPrV,KAAMgM,EAAS+S,uBACfzT,MAAOU,EAASiT,yBAGxBuK,mBAAoB,SAAUnT,EAAUyP,GACpC,GAAI9Z,GAAW1J,KAAKiB,QAAQyI,QAC5B1J,MAAKsnB,SAASvT,GACVnQ,MAAO4f,EACP9lB,KAAMgM,EAASgT,6BACf1T,MAAOU,EAASkT,+BAGxB0K,SAAU,SAAUvT,EAAU9S,GAApB,GAEFyI,GACA4N,EAFA3T,EAAW3D,KAAKiB,QAAQ0C,QAGxBA,MAAa,GAAQA,EAAS4jB,gBAAiB,GAC/C7d,EAAW1J,KAAKiB,QAAQyI,SACxB4N,IAEQvU,KAAM,SACNrF,KAAMgM,EAAS2G,QACfgI,UAAWrS,EAAMwD,OAAOwB,QACxBpB,MAAO,WACHmK,OAIJhR,KAAM,SACNrF,KAAMgM,EAAS4O,OACf1O,MAAO,WACHmK,GAAS,MAIrB/T,KAAKqX,WAAWxX,GAAO,KAAUoB,GAAWqW,QAASA,MAErDvD,KAGRsD,WAAY,SAAUpW,GAClBjB,KAAKme,QAAQ9G,WAAWpW,IAE5B4e,QAAS,WAAA,GAKDlP,GACAuD,EACAsT,EACApoB,EACAqoB,EACAC,CATA1nB,MAAKwb,iBAAmBxb,KAAKgK,KAAKrG,WAGtC3D,KAAK2nB,WAAU,GACXhX,EAAa3Q,KAAK2Q,WAClBuD,EAAWvD,EAAWuD,WACtBsT,EAAcxnB,KAAK+mB,aAGnBW,KACA1nB,KAAKZ,UACLqoB,EAAYznB,KAAKZ,QAAQsa,QAAQ,MAAM7W,KAAKnD,EAAMmD,KAAK,QACvD6kB,EAAc1nB,KAAKZ,QAAQ2F,SAE3B/E,KAAK8O,QAAQ,iBAG+B,IAA5C9O,KAAKmW,UAAUxF,WAAW1M,OAAOvF,QACjCsB,KAAK4nB,iBAAiB1T,GAEtBlU,KAAKme,SACLne,KAAKme,QAAQjP,QAEjBlP,KAAK2d,iBACL3d,KAAKgK,KAAKkb,QAAQhR,GAClBlU,KAAKkK,SAASgb,QAAQhR,GACtBlU,KAAKkK,SAASib,oBAAoBnlB,KAAKwR,aAAaE,QAChD8V,IACAxnB,KAAK8hB,UAAU0F,GACfxnB,KAAK0d,OAAO/a,EAAS6kB,MAEpBA,GAAeC,IAAcC,GAAe,IAC7CtoB,EAAUY,KAAKgK,KAAKiY,QAAQxU,KAAK,KAAO9K,EAAS6kB,GAAeC,GAAa,YAAcC,EAAc,KACzG1nB,KAAKuN,SAASnO,IAElBY,KAAK+mB,aAAe,KACpB/mB,KAAK8O,QAAQ,gBAEjBkV,oBAAqB,WACbhkB,KAAKgnB,2BAGLhnB,KAAK8O,QAAQ,iBAGjB9O,KAAKkK,SAASib,oBAAoBnlB,KAAKwR,aAAaE,QACpD1R,KAAK8O,QAAQ,eAEjB8Y,iBAAkB,SAAU1T,GAAV,GAoCL1V,GAAOE,EAnCZyX,EAAYnW,KAAKmW,UACjBoG,EAAcvc,KAAKuc,YACnBsL,EAAkB,WAAA,GACd5jB,GAAOsY,EAAY5L,WAAWe,OAC9BoW,GAAUvkB,MAAOgZ,EAAY8H,gBAEjC,OADApgB,GAAO,GAAI2C,GAAM3C,GAAM6jB,MAAMA,GAAOlW,WAGpCmW,EAAaF,IACbG,EAAoB,SAAUjV,EAAMnE,GAAhB,GAGXpQ,GAAOE,EAFZiV,EAASZ,EAAKnS,IAAI,KAEtB,KADAlB,EAAMuoB,OAAO9R,EAAU5S,OAAOwP,EAAM,GAAIpM,QAC/BnI,EAAI,EAAGE,EAASqpB,EAAWrpB,OAAQF,EAAIE,EAAQF,IAChDupB,EAAWvpB,GAAG6B,QAAUsT,GACxB/E,EAAOmE,EAAMgV,EAAWvpB,GAAGmnB,QAInCuC,EAAW,SAAUnV,EAAM4S,GAAhB,GACFG,GAAOpnB,EACRgM,EACAmb,EACAoB,EACAkB,EACA5N,EACA6N,CANR,KAAStC,EAAI,EAAGpnB,EAASinB,EAAMjnB,OAAQonB,EAAIpnB,EAAQonB,IAC3Cpb,EAAOib,EAAMG,GACbD,EAAW1P,EAAUxF,WAAW/P,IAAI8J,EAAK9J,IAAI2b,EAAY+H,sBACzD2C,EAAgBvc,EAAK9J,IAAI2b,EAAYgI,gBACrC4D,EAAczd,EAAK9J,IAAI2b,EAAY+H,qBACnC/J,EAAcsL,EAASjlB,IAAIuV,EAAUgO,kBAAoB3c,EACzD4gB,EAAgB1oB,EAAM8a,SAASyM,EAAe1M,GAClDxH,EAAKoD,EAAU5S,OAAOpF,KAAK,GAAIuI,IAC3BqK,GAAIoX,EACJplB,KAAM8iB,EAASjlB,IAAIuV,EAAU8N,eAC7BoE,MAAOxC,EAASjlB,IAAIuV,EAAU+N,gBAC9B7jB,MAAO4mB,EACPmB,cAAeA,KAI3B,KAAS5pB,EAAI,EAAGE,EAASwV,EAASxV,OAAQF,EAAIE,EAAQF,IAClDwpB,EAAkB9T,EAAS1V,GAAI0pB,IAGvCnD,kBAAmB,SAAUhU,GAAV,GAGX8U,GAmBKrnB,EAAOE,EArBZoP,EAAO9N,KACPsoB,KAEAnS,EAAYnW,KAAKmW,UAAUxF,WAAWe,OACtC6K,EAAcvc,KAAKuc,YAAY5L,WAAWe,OAC1C6W,EAAkB,GAAI3hB,GAAM2V,GAAa1Y,QACzCN,MAAOuK,EAAKyO,YAAY8H,gBACxB1S,SAAU,KACVtR,MAAO0Q,IACRa,UACC4W,EAAmB,SAAUzX,GAC7B,GAAIkW,GAAgB,IAQpB,OAPA,IAAIrgB,GAAM2hB,GAAiB1kB,QACvBN,MAAOuK,EAAKyO,YAAY+H,oBACxB3S,SAAU,KACVtR,MAAO0Q,IACR2M,OAAO,SAAUkI,GAChBqB,GAAiBrB,EAAWhlB,IAAIkN,EAAKyO,YAAYgI,kBAE9C0C,EAEX,KAASzoB,EAAI,EAAGE,EAASyX,EAAUzX,OAAQF,EAAIE,EAAQF,IACnDqnB,EAAW1P,EAAU3X,GACrB8pB,EAAOnqB,MACH4S,GAAI8U,EAASjlB,IAAI,MACjBmC,KAAM8iB,EAASjlB,IAAIkN,EAAKqI,UAAU8N,eAClC3N,OAAQuP,EAASjlB,IAAIkN,EAAKqI,UAAUgO,kBAAoB3c,EACxDnH,MAAOmoB,EAAiB3C,EAAS9U,KAGzC,OAAOuX,IAEX3B,gBAAiB,WACb3mB,KAAKwb,iBAAkB,EACvBxb,KAAK8jB,gBACL9jB,KAAK2Q,WAAW4V,QAEpBzC,cAAe,WACX9jB,KAAK2nB,WAAU,IAEnB5D,OAAQ,WACJ/jB,KAAK2nB,WAAU,IAEnBA,UAAW,SAAUc,GACjB/oB,EAAMqE,GAAG2kB,SAAS1oB,KAAKoF,QAASqjB,IAEpC3M,WAAY,WAAA,GAOJ+F,GACA8G,EACAC,EARA9a,EAAO9N,KACPyE,EAAUzE,KAAKyE,QACfoF,EAAc7D,EAAMwD,OACpB6V,EAAkBhX,EAAMwB,EAAYyB,YACpCud,EAAkBpkB,EAAQgJ,KAAKpF,EAAMwB,EAAYG,MACjDC,EAAkBxF,EAAQgJ,KAAKpF,EAAMwB,EAAYK,SAIrDlK,MAAKoe,iBAAmB3Z,EAAQgJ,KAAKpF,EAAMwB,EAAYO,UAAUtL,OAAO+pB,EAAgB/pB,UAAUmQ,MAAM,WACpGzR,EAAEwC,MAAM6N,SAAShE,EAAYQ,gBAC9B,WACC7M,EAAEwC,MAAM2N,YAAY9D,EAAYQ,iBACjClG,MAAM2kB,gBACLC,YAAa,aACbC,OAAQ3gB,EAAMwB,EAAYO,SAC1BlG,MAAS,WACL2d,EAAgBgH,EAAgBhqB,QAChC8pB,EAAgB1e,EAAgBpL,QAChC+pB,EAAiB3e,EAAgBwD,KAAK4R,GAAiB4J,cAE3DlP,OAAU,SAAU7U,GAChB,GAAIgkB,GAAQhkB,EAAEikB,EAAEC,YACZ1pB,GAAM2G,QAAQgjB,MAAM5kB,KACpBykB,OAEArH,EAAgBqH,EAAQ,GAAKP,EAAgBO,EAAQ,IAGzDL,EAAgBhqB,MAAMgjB,EAAgBqH,GACtCjf,EAAgBpL,MAAM8pB,EAAgBO,GACtCjf,EAAgBwD,KAAK4R,GAAiB4J,WAAWL,EAAiBM,GAClEpb,EAAK5D,SAASwH,OAAO4X,yBAE1BrlB,KAAK,mBAEZ8X,YAAa,WAAA,GACLjO,GAAO9N,KACP6J,EAAc7D,EAAMwD,OACpB6V,EAAkBhX,EAAMwB,EAAYyB,YACpCie,EAAiBlhB,EAAMwB,EAAYwB,eACnCme,EAAiBxpB,KAAKkK,SAAS9E,QAAQqI,KAAK8b,GAC5CE,EAAkBzpB,KAAKkK,SAAS9E,QAAQqI,KAAK4R,GAC7CqK,EAAiB1pB,KAAKgK,KAAK5E,QAAQqI,KAAK8b,GACxCI,EAAkB3pB,KAAKgK,KAAK5E,QAAQqI,KAAK4R,EACzC/Y,IACAqjB,EAAgBxZ,IAAI,aAAc,QAEtCsZ,EAAgB/a,GAAG,SAAU,WACzBZ,EAAK9I,UAAYhF,KAAKgF,UACtBwkB,EAAeP,WAAWjpB,KAAKipB,YAC/BU,EAAgB3kB,UAAUhF,KAAKgF,aAEnC2kB,EAAgBjb,GAAG,SAAU,WACzBgb,EAAeT,WAAWjpB,KAAKipB,cAChCva,GAAG,iBAAmBnH,EAAK,cAAgBA,EAAI,SAAUrC,GAAV,GAC1CF,GAAYykB,EAAgBzkB,YAC5BkkB,EAAQxpB,EAAMkqB,YAAY1kB,EAC1BgkB,KACAhkB,EAAE2J,iBACFrR,EAAE0H,EAAEuS,eAAeoS,IAAI,QAAUtiB,GAAI,GACrCkiB,EAAgBzkB,UAAUA,GAAakkB,OAInDpG,aAAc,WAAA,GAWNgH,GAVAhc,EAAO9N,KACPsN,EAActN,KAAKiB,QAAQqM,YAC3B3J,EAAW3D,KAAKiB,QAAQ0C,SACxBomB,EAAc/pB,KAAKgK,KAAKggB,OAAOvc,KAAK,SACpCwc,EAAejqB,KAAKgK,KAAKiY,QAAQxU,KAAK,SACtC5D,EAAc7D,EAAMwD,OACpB6f,EAAQ3pB,EAAM2G,QAAQgjB,MAAMrpB,KAAKyE,SACjCglB,EAAkBzpB,KAAKkK,SAAS9E,QAAQqI,KAAKpF,EAAMwB,EAAYyB,aAC/D4e,EAASH,EAAY5mB,IAAI8mB,GACzBpnB,EAAOF,IAEPwnB,GACAC,UAAU,EACVC,QAAQ,GAERC,EAAS,SAAUC,GACnB,GAAI1rB,GAAQiP,EAAK5D,SAASwH,OAAO2Q,aAAa,GAAGvgB,WACjD2nB,GAAgBR,WAAWQ,EAAgBR,cAAgBsB,GAAW1rB,EAAQA,KAE9E2rB,EAAiB,SAAUD,GAC3B,GAAIzrB,GAASgP,EAAK5D,SAASwH,OAAO+Y,UAClChB,GAAgBzkB,UAAUykB,EAAgBzkB,aAAeulB,GAAWzrB,EAASA,KAE7E4rB,EAAe,SAAUld,GAAV,GACXxJ,GAAS8J,EAAK1O,QAAQ4E,OAAO,KAAOrB,KACpCoC,EAAQ+I,EAAK1O,QAAQ2F,QACrB4lB,EAAU3mB,EAAOwJ,IACQ,KAAzBM,EAAK4P,SAAShf,QACdoP,EAAK6P,iBAEc,IAAnBgN,EAAQjsB,QACRoP,EAAKP,SAASod,EAAQ3X,SAAS,SAAWjO,EAAQ,MAClD+I,EAAKgU,UAAUhU,EAAK1O,UAEhB0O,EAAK1O,QAAQgY,GAAG,OAAmB,QAAV5J,EACzBlJ,EAAWylB,GACJjc,EAAK1O,QAAQgY,GAAG,OAAmB,QAAV5J,GAChClJ,EAAW2lB,IAInBW,EAAiB,SAAUpd,GAC3B,GAAImd,GAAU7c,EAAK1O,QAAQoO,IACJ,KAAnBmd,EAAQjsB,SACRoP,EAAKP,SAASod,GACdb,EAAYhc,EAAK1O,QAAQ2F,UAG7B8lB,EAAsB,SAAUxqB,GAChC,GAAIuD,GAAQkK,EAAKwM,SAASxM,EAAK1O,QAC3BwE,GAAMkP,SAAWlP,EAAMwI,WAAa/L,GACpCuD,EAAMwO,IAAI,WAAY/R,IAG1ByqB,EAAe,WAAA,GAKXC,GACAnoB,EALAe,EAAWmK,EAAK7M,QAAQ0C,QACvBA,IAAYA,EAAS0M,WAAY,IAASvC,EAAK9D,KAAKrG,WAGrDonB,EAAejd,EAAK4P,SACpB9a,EAAMlD,EAAMmD,KAAK,OACjBkoB,EAAarsB,QACboP,EAAK8V,WAAWmH,EAAaloB,KAAKD,KAkD1C,OA/CApF,GAAEwC,KAAKyE,SAASiK,GAAG,YAAcnH,EAAI,KAAO1E,EAAO,QAAUA,EAAO,QAAUwF,EAAMwB,EAAYc,KAAO,IAAK,SAAUzF,GAAV,GAGpG9F,GAFAqY,EAAgBja,EAAE0H,EAAEuS,eACpBuT,EAAUxtB,EAAE0H,EAAEyJ,QAAQyI,GAAG,2FAEzBlS,GAAE+lB,UAGF3d,IAEIlO,EADAqY,EAAcL,GAAG,MACP5Z,EAAE0H,EAAEyJ,QAAQ+K,QAAQ,MAEpB5L,EAAK9D,KAAKiY,QAAQxU,KAAK,KAAO9K,EAAS8U,EAAc5U,KAAKnD,EAAMmD,KAAK,SAAW,eAE9FiL,EAAKP,SAASnO,KAEbkO,IAAe3J,GAAcqnB,IAC9Bld,EAAKyC,cAAgB2a,WAAW,WAC5B5mB,EAAWwJ,EAAK9D,KAAKiY,QAAQxU,KAAK,UAAU,IAC7C,OAERiB,GAAG,UAAYnH,EAAI,SAAUrC,GAAV,GAQdimB,GAPAltB,EAAMiH,EAAEkK,QACRtB,EAAO9N,KACPorB,EAAiB5tB,EAAEsQ,EAAKud,oBACxBC,EAAMF,EAAermB,MAAM+I,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYsB,SAAS,GAK5E,IAJImgB,QAAc9tB,EAAE0H,EAAEyJ,QAAQ+K,QAAQrR,EAAMwB,EAAYoC,QAAQI,OAAO3N,SACnE4sB,EAAMF,EAAermB,MAAM+I,EAAK7B,QAAQwB,KAAK,+EAA+E,KAE5H0d,EAAcjmB,EAAEqmB,SAAWH,EAAeE,EAAM,GAAKF,EAAeE,EAAM,GAC1ErtB,IAAQgI,EAAKC,IACb4H,EAAK7B,QAAQwB,KAAK,2BAA2BI,SAAShE,EAAYsB,SAAShG,QAC3ED,EAAE2J,qBACC,IAAI5Q,GAAOgI,EAAKulB,KAAOhuB,EAAE0H,EAAEyJ,QAAQ+K,QAAQrR,EAAMwB,EAAYoC,QAAQA,SAASvN,OAAQ,CAEzF,GADAoP,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYsB,SAASwC,YAAY9D,EAAYsB,SAASyV,OAC1EuK,EAGA,MAFA3tB,GAAE2tB,GAAatd,SAAShE,EAAYsB,SAAShG,QAC7CD,EAAE2J,iBACF,CAEA7O,MAAKgK,KAAK5E,QAAQgS,GAAG,YACrBpX,KAAKgK,KAAK5E,QAAQqI,KAAK,wBAAwBtI,QAE/CnF,KAAKoF,QAAQqI,KAAKpF,EAAMwB,EAAY0B,OAAO,GAAGpG,QAElDD,EAAE2J,mBAERM,KAAKnP,OACHsN,KAAgB,GAChB2c,EAAavb,GAAG,UAAYnH,EAAI,SAAUrC,GAClCA,EAAEkK,SAAWnJ,EAAKwlB,QAClBX,MAGR,IAEJZ,EAAOxb,GAAG,QAAUnH,EAAI,WAAA,GAChB5E,GAAW3C,OAASiqB,EAAarpB,IAAI,GAAK,KAAO,KACjDqiB,EAAYnV,EAAK4P,SACjBte,EAAU0O,EAAK1O,SAAW5B,EAAEylB,EAAUvkB,OAASukB,EAAYjjB,MAAMyN,KAAK9K,EAAW,QAAUmnB,GAAa,GAAK,IACjHhc,GAAKP,SAASnO,GACd5B,EAAEsQ,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYsB,UAAUwC,YAAY9D,EAAYsB,SACxE3N,EAAEsQ,EAAK7B,QAAQwB,KAAKpF,EAAMwB,EAAYoC,QAAQY,cAAc7I,SAAS2J,YAAY9D,EAAYoC,QAAQG,YACtGsC,GAAG,OAASnH,EAAI,WACfuG,EAAKP,WACDvN,MAAQ+pB,GACRvsB,EAAEwC,MAAM6C,KAAKgC,QAElB6J,GAAG,UAAYnH,EAAI,SAAUrC,GAAV,GAEdwmB,GADAztB,EAAMiH,EAAEkK,OAEZ,IAAKtB,EAAK1O,QAIV,OADAssB,EAAS5d,EAAK1O,QAAQgY,GAAG,MACjBnZ,GACR,IAAKgI,GAAKsa,MACNrb,EAAE2J,iBACE3J,EAAEyb,OACF2J,IACOplB,EAAE+lB,QACTJ,EAAoBxB,EAAQc,EAAYC,SAAWD,EAAYE,QAE/DO,EAAevB,EAAQ,OAAS,OAEpC,MACJ,KAAKpjB,GAAKua,KACNtb,EAAE2J,iBACE3J,EAAEyb,OACF2J,GAAO,GACAplB,EAAE+lB,QACTJ,EAAoBxB,EAAQc,EAAYE,OAASF,EAAYC,UAE7DQ,EAAevB,EAAQ,OAAS,OAEpC,MACJ,KAAKpjB,GAAKoJ,GACNnK,EAAE2J,iBACE3J,EAAEyb,OACF6J,GAAe,GAEfE,EAAa,OAEjB,MACJ,KAAKzkB,GAAKqJ,KACNpK,EAAE2J,iBACE3J,EAAEyb,OACF6J,IAEAE,EAAa,OAEjB,MACJ,KAAKzkB,GAAKya,SACNxb,EAAE2J,iBACE6c,GACA5d,EAAK4P,OAAO5P,EAAK1O,QAAQsa,QAAQ,MAErC,MACJ,KAAKzT,GAAKsJ,MACNrK,EAAE2J,iBACE6c,EACI5d,EAAK7M,QAAQ0C,UAAYmK,EAAK7M,QAAQ0C,SAASyQ,UAAW,IAC1DtG,EAAK6U,eAAiB7U,EAAK1O,QAC3B0O,EAAK9D,KAAK2hB,kBAAkB7d,EAAK1O,SACjC5B,EAAEwC,MAAM6pB,IAAI,QAAS,SAAU3kB,GAC3BA,EAAE2T,qBAIV/K,EAAK1O,QAAQ4T,SAAS,YAAYpJ,OAEtC,MACJ,KAAK3D,GAAKuJ,IACN,KACJ,KAAKvJ,GAAKwlB,OACFC,GACAZ,GAEJ,MACJ,SACQ7sB,GAAO,IAAMA,GAAO,IAAyC,UAAnCiH,EAAEyJ,OAAOid,QAAQ9K,eAC3ChT,EAAK4D,KAAK5D,EAAK5D,SAAS2W,aAAa5iB,EAAM,QArFvDisB,IA2FJmB,iBAAkB,WACd,MAAOrrB,MAAKiM,QAAQwB,KAAK,2BAA2BmE,UAAUia,OAAO7rB,KAAKiM,QAAQwB,KAAK,wCAAwCmE,UAAW5R,KAAKiM,QAAQwB,KAAK,+EAA+EmE,YAE/OrE,SAAU,SAAUnI,GAAV,GAEF0mB,GADAjiB,EAAc7D,EAAMwD,MAEpBxJ,MAAKZ,SAAWY,KAAKZ,QAAQV,QAC7BsB,KAAKZ,QAAQuO,YAAY9D,EAAYsB,SAASyC,WAAW,MAEzDxI,GAAWA,EAAQ1G,QACnBsB,KAAKZ,QAAUgG,EAAQyI,SAAShE,EAAYsB,SAAStI,KAAK,KAAMsF,GAChE2jB,EAAgBtuB,EAAEkC,EAAMqsB,kBACpBD,EAAc1U,GAAG,UAAYpX,KAAKyE,QAAQgJ,KAAKqe,GAAeptB,OAAS,GACvEotB,EAAcle,WAAW3F,GAAiBpF,KAAKoF,EAAiBE,IAGpEnI,KAAKZ,QAAU,MAGvB4c,UAAW,WAAA,GAKCgQ,GAJJle,EAAO9N,IACP8N,GAAK7M,QAAQmb,WACbpc,KAAKwb,iBAAkB,EACvBxb,KAAKgnB,2BAA4B,EAC7BgF,EAAWxuB,EAAE8C,KACbN,KAAK2Q,WACL3Q,KAAKwR,aACLxR,KAAKmW,UAAUxF,WACf3Q,KAAKuc,YAAY5L,YAClB,SAAUA,GACT,MAAOA,GAAW8U,UAEtBjoB,EAAEyuB,KAAKxa,MAAM,KAAMua,GAAUE,KAAK,WAC9Bpe,EAAK0N,iBAAkB,EACvB1N,EAAKkZ,2BAA4B,EACjClZ,EAAK+R,cAIjBC,QAAS,WACL9f,KAAKub,oBACLvb,KAAKkK,SAASwH,OAAOya,gBACrBnsB,KAAKkK,SAASwH,OAAO4X,qBACrBtpB,KAAKgK,KAAKmiB,mBAGdzsB,EAAM0sB,WACN1sB,EAAM0sB,SAASvsB,OAAOmG,EAAMkH,IAC5BlH,EAAMkH,GAAGmf,SAAW,WAAA,GACZxiB,GAAc7D,EAAMwD,OACpB8iB,EAAY,IAAMziB,EAAYG,KAC9ByT,EAAYzd,KAAKyE,QAAQgJ,KAAK6e,GAAWztB,QACzCojB,EAAUjiB,KAAKyE,QAAQ8nB,OAE3B,OADAtK,GAAQxU,KAAK6e,GAAWnc,IAAI,QAASsN,GAC9Bzd,KAAKwsB,gBAAiBvK,QAASA,IAAawK,WAAYzsB,KAAKiB,QAAQ+Z,IAAIyR,eAGxF/sB,EAAMqE,GAAG2oB,OAAO1mB,GAChBnG,GAAO,EAAMmG,GAASwD,OAAQK,MAChCpK,OAAOC,MAAM2C,QACR5C,OAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.gantt.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.gantt', [\n        'kendo.data',\n        'kendo.popup',\n        'kendo.window',\n        'kendo.resizable',\n        'kendo.gantt.list',\n        'kendo.gantt.timeline',\n        'kendo.grid',\n        'kendo.pdf'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'gantt',\n        name: 'Gantt',\n        category: 'web',\n        description: 'The Gantt component.',\n        depends: [\n            'data',\n            'popup',\n            'resizable',\n            'window',\n            'gantt.list',\n            'gantt.timeline',\n            'grid'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo;\n        var keys = $.extend({ F10: 121 }, kendo.keys);\n        var supportsMedia = 'matchMedia' in window;\n        var browser = kendo.support.browser;\n        var mobileOS = kendo.support.mobileOS;\n        var Observable = kendo.Observable;\n        var Widget = kendo.ui.Widget;\n        var DataSource = kendo.data.DataSource;\n        var ObservableObject = kendo.data.ObservableObject;\n        var ObservableArray = kendo.data.ObservableArray;\n        var Query = kendo.data.Query;\n        var isArray = $.isArray;\n        var inArray = $.inArray;\n        var isFunction = kendo.isFunction;\n        var proxy = $.proxy;\n        var extend = $.extend;\n        var isPlainObject = $.isPlainObject;\n        var map = $.map;\n        var outerWidth = kendo._outerWidth;\n        var outerHeight = kendo._outerHeight;\n        var defaultIndicatorWidth = 3;\n        var NS = '.kendoGantt';\n        var PERCENTAGE_FORMAT = 'p0';\n        var TABINDEX = 'tabIndex';\n        var CLICK = 'click';\n        var WIDTH = 'width';\n        var STRING = 'string';\n        var DIRECTIONS = {\n            'down': {\n                origin: 'bottom left',\n                position: 'top left'\n            },\n            'up': {\n                origin: 'top left',\n                position: 'bottom left'\n            }\n        };\n        var ARIA_DESCENDANT = 'aria-activedescendant';\n        var ARIA_LABEL = 'aria-label';\n        var ACTIVE_CELL = 'gantt_active_cell';\n        var ACTIVE_OPTION = 'action-option-focused';\n        var DOT = '.';\n        var TASK_DELETE_CONFIRM = 'Are you sure you want to delete this task?';\n        var DEPENDENCY_DELETE_CONFIRM = 'Are you sure you want to delete this dependency?';\n        var TOGGLE_BUTTON_TEMPLATE = kendo.template('<button class=\"#=styles.buttonToggle#\" type=\"button\" ' + ARIA_LABEL + '=\"Toggle\"><span class=\"#=styles.iconToggle#\"></span></button>');\n        var BUTTON_TEMPLATE = '<button class=\"#=styles.button# #=className#\" type=\"button\" ' + '#if (action) {#' + 'data-action=\"#=action#\"' + '#}#' + '><span class=\"#=iconClass#\"></span><span>#=text#</span></button>';\n        var COMMAND_BUTTON_TEMPLATE = '<a class=\"#=className#\" #=attr# href=\"\\\\#\">#=text#</a>';\n        var VIEWBUTTONTEMPLATE = kendo.template('<li class=\"#=styles.currentView# #=styles.viewButtonDefault#\"><a href=\"\\\\#\" class=\"#=styles.link#\">&nbps;</a></li>');\n        var HEADER_VIEWS_TEMPLATE = kendo.template('<ul class=\"#=styles.viewsWrapper#\">' + '#for(var view in views){#' + '<li class=\"#=styles.viewButtonDefault# #=styles.viewButton#-#= view.toLowerCase() #\" data-#=ns#name=\"#=view#\"><a href=\"\\\\#\" class=\"#=styles.link#\">#=views[view].title#</a></li>' + '#}#' + '</ul>');\n        var TASK_DROPDOWN_TEMPLATE = kendo.template('<div class=\"#=styles.popupWrapper#\">' + '<ul class=\"#=styles.popupList#\" role=\"listbox\">' + '#for(var i = 0, l = actions.length; i < l; i++){#' + '<li class=\"#=styles.item#\" data-action=\"#=actions[i].data#\" role=\"option\">#=actions[i].text#</span>' + '#}#' + '</ul>' + '</div>');\n        var DATERANGEEDITOR = function (container, options) {\n            var attr = {\n                name: options.field,\n                title: options.title\n            };\n            var validationRules = options.model.fields[options.field].validation;\n            if (validationRules && isPlainObject(validationRules) && validationRules.message) {\n                attr[kendo.attr('dateCompare-msg')] = validationRules.message;\n            }\n            $('<input type=\"text\" required ' + kendo.attr('type') + '=\"date\" ' + kendo.attr('role') + '=\"datetimepicker\" ' + kendo.attr('bind') + '=\"value:' + options.field + '\" ' + kendo.attr('validate') + '=\\'true\\' />').attr(attr).appendTo(container);\n            $('<span ' + kendo.attr('for') + '=\"' + options.field + '\" class=\"k-invalid-msg\"/>').hide().appendTo(container);\n        };\n        var RESOURCESEDITOR = function (container, options) {\n            $('<a href=\"#\" class=\"' + options.styles.button + '\">' + options.messages.assignButton + '</a>').click(options.click).appendTo(container);\n        };\n        var ganttStyles = {\n            wrapper: 'k-widget k-gantt',\n            rowHeight: 'k-gantt-rowheight',\n            listWrapper: 'k-gantt-layout k-gantt-treelist',\n            list: 'k-gantt-treelist',\n            timelineWrapper: 'k-gantt-layout k-gantt-timeline',\n            timeline: 'k-gantt-timeline',\n            splitBarWrapper: 'k-splitbar k-state-default k-splitbar-horizontal k-splitbar-draggable-horizontal k-gantt-layout',\n            splitBar: 'k-splitbar',\n            splitBarHover: 'k-splitbar-horizontal-hover',\n            popupWrapper: 'k-list-container',\n            popupList: 'k-list k-reset',\n            resizeHandle: 'k-resize-handle',\n            icon: 'k-icon',\n            item: 'k-item',\n            line: 'k-line',\n            buttonDelete: 'k-gantt-delete',\n            buttonCancel: 'k-gantt-cancel',\n            buttonSave: 'k-gantt-update',\n            buttonToggle: 'k-gantt-toggle',\n            primary: 'k-primary',\n            hovered: 'k-state-hover',\n            selected: 'k-state-selected',\n            focused: 'k-state-focused',\n            gridHeader: 'k-grid-header',\n            gridHeaderWrap: 'k-grid-header-wrap',\n            gridContent: 'k-grid-content',\n            tasks: 'k-gantt-tasks',\n            popup: {\n                form: 'k-popup-edit-form',\n                editForm: 'k-gantt-edit-form',\n                formContainer: 'k-edit-form-container',\n                resourcesFormContainer: 'k-resources-form-container',\n                message: 'k-popup-message',\n                buttonsContainer: 'k-edit-buttons k-state-default',\n                button: 'k-button',\n                editField: 'k-edit-field',\n                editLabel: 'k-edit-label',\n                resourcesField: 'k-gantt-resources'\n            },\n            toolbar: {\n                headerWrapper: 'k-floatwrap k-header k-gantt-toolbar',\n                footerWrapper: 'k-floatwrap k-header k-gantt-toolbar',\n                toolbar: 'k-gantt-toolbar',\n                expanded: 'k-state-expanded',\n                views: 'k-gantt-views',\n                viewsWrapper: 'k-reset k-header k-gantt-views',\n                actions: 'k-gantt-actions',\n                button: 'k-button k-button-icontext',\n                buttonToggle: 'k-button k-button-icon k-gantt-toggle',\n                iconPlus: 'k-icon k-i-plus',\n                iconPdf: 'k-icon k-i-file-pdf',\n                iconToggle: 'k-icon k-i-layout-1-by-4',\n                viewButtonDefault: 'k-state-default',\n                viewButton: 'k-view',\n                currentView: 'k-current-view',\n                link: 'k-link',\n                pdfButton: 'k-gantt-pdf',\n                appendButton: 'k-gantt-create'\n            }\n        };\n        function selector(uid) {\n            return '[' + kendo.attr('uid') + (uid ? '=\\'' + uid + '\\']' : ']');\n        }\n        function trimOptions(options) {\n            delete options.name;\n            delete options.prefix;\n            delete options.remove;\n            delete options.edit;\n            delete options.add;\n            delete options.navigate;\n            return options;\n        }\n        function dateCompareValidator(input) {\n            if (input.filter('[name=end], [name=start]').length) {\n                var field = input.attr('name');\n                var picker = kendo.widgetInstance(input, kendo.ui);\n                var dates = {};\n                var container = input;\n                var editable;\n                var model;\n                while (container !== window && !editable) {\n                    container = container.parent();\n                    editable = container.data('kendoEditable');\n                }\n                model = editable ? editable.options.model : null;\n                if (!model) {\n                    return true;\n                }\n                dates.start = model.start;\n                dates.end = model.end;\n                dates[field] = picker ? picker.value() : kendo.parseDate(input.val());\n                return dates.start <= dates.end;\n            }\n            return true;\n        }\n        function focusTable(table, direct) {\n            var wrapper = table.parents('[' + kendo.attr('role') + '=\"gantt\"]');\n            var scrollPositions = [];\n            var parents = scrollableParents(wrapper);\n            table.attr(TABINDEX, 0);\n            if (direct) {\n                parents.each(function (index, parent) {\n                    scrollPositions[index] = $(parent).scrollTop();\n                });\n            }\n            try {\n                table[0].setActive();\n            } catch (e) {\n                table[0].focus();\n            }\n            if (direct) {\n                parents.each(function (index, parent) {\n                    $(parent).scrollTop(scrollPositions[index]);\n                });\n            }\n        }\n        function scrollableParents(element) {\n            return $(element).parentsUntil('body').filter(function (index, element) {\n                var computedStyle = kendo.getComputedStyles(element, ['overflow']);\n                return computedStyle.overflow != 'visible';\n            }).add(window);\n        }\n        var defaultCommands;\n        var TaskDropDown = Observable.extend({\n            init: function (element, options) {\n                Observable.fn.init.call(this);\n                this.element = element;\n                this.options = extend(true, {}, this.options, options);\n                this._popup();\n            },\n            options: {\n                direction: 'down',\n                navigatable: false\n            },\n            _current: function (method) {\n                var ganttStyles = Gantt.styles;\n                var current = this.list.find(DOT + ganttStyles.focused);\n                var sibling = current[method]();\n                if (sibling.length) {\n                    current.removeClass(ganttStyles.focused).removeAttr('id');\n                    sibling.addClass(ganttStyles.focused).attr('id', ACTIVE_OPTION);\n                    this.list.find('ul').removeAttr(ARIA_DESCENDANT).attr(ARIA_DESCENDANT, ACTIVE_OPTION);\n                }\n            },\n            _popup: function () {\n                var that = this;\n                var ganttStyles = Gantt.styles;\n                var itemSelector = 'li' + DOT + ganttStyles.item;\n                var appendButtonSelector = DOT + ganttStyles.toolbar.appendButton;\n                var actions = this.options.messages.actions;\n                var navigatable = this.options.navigatable;\n                this.list = $(TASK_DROPDOWN_TEMPLATE({\n                    styles: ganttStyles,\n                    actions: [\n                        {\n                            data: 'add',\n                            text: actions.addChild\n                        },\n                        {\n                            data: 'insert-before',\n                            text: actions.insertBefore\n                        },\n                        {\n                            data: 'insert-after',\n                            text: actions.insertAfter\n                        }\n                    ]\n                }));\n                this.element.append(this.list);\n                this.popup = new kendo.ui.Popup(this.list, extend({\n                    anchor: this.element.find(appendButtonSelector),\n                    open: function () {\n                        that._adjustListWidth();\n                    },\n                    animation: this.options.animation\n                }, DIRECTIONS[this.options.direction]));\n                this.element.on(CLICK + NS, appendButtonSelector, function (e) {\n                    var target = $(this);\n                    var action = target.attr(kendo.attr('action'));\n                    e.preventDefault();\n                    if (action) {\n                        that.trigger('command', { type: action });\n                    } else {\n                        that.popup.open();\n                        if (navigatable) {\n                            that.list.find('li:first').addClass(ganttStyles.focused).attr('id', ACTIVE_OPTION).end().find('ul').attr({\n                                TABINDEX: 0,\n                                'aria-activedescendant': ACTIVE_OPTION\n                            }).focus();\n                        }\n                    }\n                });\n                this.list.find(itemSelector).hover(function () {\n                    $(this).addClass(ganttStyles.hovered);\n                }, function () {\n                    $(this).removeClass(ganttStyles.hovered);\n                }).end().on(CLICK + NS, itemSelector, function () {\n                    that.trigger('command', { type: $(this).attr(kendo.attr('action')) });\n                    that.popup.close();\n                });\n                if (navigatable) {\n                    this.popup.bind('close', function () {\n                        that.list.find(itemSelector).removeClass(ganttStyles.focused).end().find('ul').attr(TABINDEX, 0);\n                        that.element.parents('[' + kendo.attr('role') + '=\"gantt\"]').find(DOT + ganttStyles.gridContent + ' > table:first').focus();\n                    });\n                    this.list.find('ul').on('keydown' + NS, function (e) {\n                        var key = e.keyCode;\n                        switch (key) {\n                        case keys.UP:\n                            e.preventDefault();\n                            that._current('prev');\n                            break;\n                        case keys.DOWN:\n                            e.preventDefault();\n                            that._current('next');\n                            break;\n                        case keys.ENTER:\n                            that.list.find(DOT + ganttStyles.focused).click();\n                            break;\n                        case keys.ESC:\n                            e.preventDefault();\n                            that.popup.close();\n                            break;\n                        }\n                    });\n                }\n            },\n            _adjustListWidth: function () {\n                var list = this.list;\n                var ganttStyles = Gantt.styles;\n                var width = list[0].style.width;\n                var wrapper = this.element.find(DOT + ganttStyles.toolbar.appendButton);\n                var listOuterWidth = outerWidth(list);\n                var computedStyle;\n                var computedWidth;\n                if (!list.data(WIDTH) && width) {\n                    return;\n                }\n                computedStyle = window.getComputedStyle ? window.getComputedStyle(wrapper[0], null) : 0;\n                computedWidth = computedStyle ? parseFloat(computedStyle.width) : outerWidth(wrapper);\n                if (computedStyle && (browser.mozilla || browser.msie)) {\n                    computedWidth += parseFloat(computedStyle.paddingLeft) + parseFloat(computedStyle.paddingRight) + parseFloat(computedStyle.borderLeftWidth) + parseFloat(computedStyle.borderRightWidth);\n                }\n                if (list.css('box-sizing') !== 'border-box') {\n                    width = computedWidth - (outerWidth(list) - list.width());\n                } else {\n                    width = computedWidth;\n                }\n                if (listOuterWidth > width) {\n                    width = listOuterWidth;\n                }\n                list.css({\n                    fontFamily: wrapper.css('font-family'),\n                    width: width\n                }).data(WIDTH, width);\n            },\n            destroy: function () {\n                clearTimeout(this._focusTimeout);\n                this.popup.destroy();\n                this.element.off(NS);\n                this.list.off(NS);\n                this.unbind();\n            }\n        });\n        var createDataSource = function (type, name) {\n            return function (options) {\n                options = isArray(options) ? { data: options } : options;\n                var dataSource = options || {};\n                var data = dataSource.data;\n                dataSource.data = data;\n                if (!(dataSource instanceof type) && dataSource instanceof DataSource) {\n                    throw new Error('Incorrect DataSource type. Only ' + name + ' instances are supported');\n                }\n                return dataSource instanceof type ? dataSource : new type(dataSource);\n            };\n        };\n        var GanttDependency = kendo.data.Model.define({\n            id: 'id',\n            fields: {\n                id: { type: 'number' },\n                predecessorId: { type: 'number' },\n                successorId: { type: 'number' },\n                type: { type: 'number' }\n            }\n        });\n        var GanttDependencyDataSource = DataSource.extend({\n            init: function (options) {\n                DataSource.fn.init.call(this, extend(true, {}, {\n                    schema: {\n                        modelBase: GanttDependency,\n                        model: GanttDependency\n                    }\n                }, options));\n            },\n            successors: function (id) {\n                return this._dependencies('predecessorId', id);\n            },\n            predecessors: function (id) {\n                return this._dependencies('successorId', id);\n            },\n            dependencies: function (id) {\n                var predecessors = this.predecessors(id);\n                var successors = this.successors(id);\n                predecessors.push.apply(predecessors, successors);\n                return predecessors;\n            },\n            _dependencies: function (field, id) {\n                var data = this.view();\n                var filter = {\n                    field: field,\n                    operator: 'eq',\n                    value: id\n                };\n                data = new Query(data).filter(filter).toArray();\n                return data;\n            }\n        });\n        GanttDependencyDataSource.create = createDataSource(GanttDependencyDataSource, 'GanttDependencyDataSource');\n        var GanttTask = kendo.data.Model.define({\n            duration: function () {\n                var end = this.end;\n                var start = this.start;\n                return end - start;\n            },\n            isMilestone: function () {\n                return this.duration() === 0;\n            },\n            _offset: function (value) {\n                var field = [\n                    'start',\n                    'end'\n                ];\n                var newValue;\n                for (var i = 0; i < field.length; i++) {\n                    newValue = new Date(this.get(field[i]).getTime() + value);\n                    this.set(field[i], newValue);\n                }\n            },\n            id: 'id',\n            fields: {\n                id: { type: 'number' },\n                parentId: {\n                    type: 'number',\n                    defaultValue: null,\n                    validation: { required: true }\n                },\n                orderId: {\n                    type: 'number',\n                    validation: { required: true }\n                },\n                title: {\n                    type: 'string',\n                    defaultValue: 'New task'\n                },\n                start: {\n                    type: 'date',\n                    validation: { required: true }\n                },\n                end: {\n                    type: 'date',\n                    validation: {\n                        required: true,\n                        dateCompare: dateCompareValidator,\n                        message: 'End date should be after or equal to the start date'\n                    }\n                },\n                percentComplete: {\n                    type: 'number',\n                    validation: {\n                        required: true,\n                        min: 0,\n                        max: 1,\n                        step: 0.01\n                    }\n                },\n                summary: { type: 'boolean' },\n                expanded: {\n                    type: 'boolean',\n                    defaultValue: true\n                }\n            }\n        });\n        var GanttDataSource = DataSource.extend({\n            init: function (options) {\n                DataSource.fn.init.call(this, extend(true, {}, {\n                    schema: {\n                        modelBase: GanttTask,\n                        model: GanttTask\n                    }\n                }, options));\n            },\n            remove: function (task) {\n                var parentId = task.get('parentId');\n                var children = this.taskAllChildren(task);\n                this._removeItems(children);\n                task = DataSource.fn.remove.call(this, task);\n                this._childRemoved(parentId, task.get('orderId'));\n                return task;\n            },\n            add: function (task) {\n                if (!task) {\n                    return;\n                }\n                task = this._toGanttTask(task);\n                return this.insert(this.taskSiblings(task).length, task);\n            },\n            insert: function (index, task) {\n                if (!task) {\n                    return;\n                }\n                task = this._toGanttTask(task);\n                task.set('orderId', index);\n                task = DataSource.fn.insert.call(this, index, task);\n                this._reorderSiblings(task, this.taskSiblings(task).length - 1);\n                this._resolveSummaryFields(this.taskParent(task));\n                return task;\n            },\n            taskChildren: function (task) {\n                var data = this.view();\n                var filter = {\n                    field: 'parentId',\n                    operator: 'eq',\n                    value: null\n                };\n                var order = this._sort && this._sort.length ? this._sort : {\n                    field: 'orderId',\n                    dir: 'asc'\n                };\n                var taskId;\n                if (!!task) {\n                    taskId = task.get('id');\n                    if (taskId === undefined || taskId === null || taskId === '') {\n                        return [];\n                    }\n                    filter.value = taskId;\n                }\n                data = new Query(data).filter(filter).sort(order).toArray();\n                return data;\n            },\n            taskAllChildren: function (task) {\n                var data = [];\n                var that = this;\n                var callback = function (task) {\n                    var tasks = that.taskChildren(task);\n                    data.push.apply(data, tasks);\n                    map(tasks, callback);\n                };\n                if (!!task) {\n                    callback(task);\n                } else {\n                    data = this.view();\n                }\n                return data;\n            },\n            taskSiblings: function (task) {\n                if (!task) {\n                    return null;\n                }\n                var parent = this.taskParent(task);\n                return this.taskChildren(parent);\n            },\n            taskParent: function (task) {\n                if (!task || task.get('parentId') === null) {\n                    return null;\n                }\n                return this.get(task.parentId);\n            },\n            taskLevel: function (task) {\n                var level = 0;\n                var parent = this.taskParent(task);\n                while (parent !== null) {\n                    level += 1;\n                    parent = this.taskParent(parent);\n                }\n                return level;\n            },\n            taskTree: function (task) {\n                var data = [];\n                var current;\n                var tasks = this.taskChildren(task);\n                for (var i = 0, l = tasks.length; i < l; i++) {\n                    current = tasks[i];\n                    data.push(current);\n                    if (current.get('expanded')) {\n                        var children = this.taskTree(current);\n                        data.push.apply(data, children);\n                    }\n                }\n                return data;\n            },\n            update: function (task, taskInfo) {\n                var that = this;\n                var oldValue;\n                var offsetChildren = function (parentTask, offset) {\n                    var children = that.taskAllChildren(parentTask);\n                    for (var i = 0, l = children.length; i < l; i++) {\n                        children[i]._offset(offset);\n                    }\n                };\n                var modelChangeHandler = function (e) {\n                    var field = e.field;\n                    var model = e.sender;\n                    switch (field) {\n                    case 'start':\n                        that._resolveSummaryStart(that.taskParent(model));\n                        offsetChildren(model, model.get(field).getTime() - oldValue.getTime());\n                        break;\n                    case 'end':\n                        that._resolveSummaryEnd(that.taskParent(model));\n                        break;\n                    case 'percentComplete':\n                        that._resolveSummaryPercentComplete(that.taskParent(model));\n                        break;\n                    case 'orderId':\n                        that._reorderSiblings(model, oldValue);\n                        break;\n                    }\n                };\n                if (taskInfo.parentId !== undefined) {\n                    oldValue = task.get('parentId');\n                    if (oldValue !== taskInfo.parentId) {\n                        task.set('parentId', taskInfo.parentId);\n                        that._childRemoved(oldValue, task.get('orderId'));\n                        task.set('orderId', that.taskSiblings(task).length - 1);\n                        that._resolveSummaryFields(that.taskParent(task));\n                    }\n                    delete taskInfo.parentId;\n                }\n                task.bind('change', modelChangeHandler);\n                for (var field in taskInfo) {\n                    oldValue = task.get(field);\n                    task.set(field, taskInfo[field]);\n                }\n                task.unbind('change', modelChangeHandler);\n            },\n            _resolveSummaryFields: function (summary) {\n                if (!summary) {\n                    return;\n                }\n                this._updateSummary(summary);\n                if (!this.taskChildren(summary).length) {\n                    return;\n                }\n                this._resolveSummaryStart(summary);\n                this._resolveSummaryEnd(summary);\n                this._resolveSummaryPercentComplete(summary);\n            },\n            _resolveSummaryStart: function (summary) {\n                var that = this;\n                var getSummaryStart = function (parentTask) {\n                    var children = that.taskChildren(parentTask);\n                    var min = children[0].start.getTime();\n                    var currentMin;\n                    for (var i = 1, l = children.length; i < l; i++) {\n                        currentMin = children[i].start.getTime();\n                        if (currentMin < min) {\n                            min = currentMin;\n                        }\n                    }\n                    return new Date(min);\n                };\n                this._updateSummaryRecursive(summary, 'start', getSummaryStart);\n            },\n            _resolveSummaryEnd: function (summary) {\n                var that = this;\n                var getSummaryEnd = function (parentTask) {\n                    var children = that.taskChildren(parentTask);\n                    var max = children[0].end.getTime();\n                    var currentMax;\n                    for (var i = 1, l = children.length; i < l; i++) {\n                        currentMax = children[i].end.getTime();\n                        if (currentMax > max) {\n                            max = currentMax;\n                        }\n                    }\n                    return new Date(max);\n                };\n                this._updateSummaryRecursive(summary, 'end', getSummaryEnd);\n            },\n            _resolveSummaryPercentComplete: function (summary) {\n                var that = this;\n                var getSummaryPercentComplete = function (parentTask) {\n                    var children = that.taskChildren(parentTask);\n                    var percentComplete = new Query(children).aggregate([{\n                            field: 'percentComplete',\n                            aggregate: 'average'\n                        }]);\n                    return percentComplete.percentComplete.average;\n                };\n                this._updateSummaryRecursive(summary, 'percentComplete', getSummaryPercentComplete);\n            },\n            _updateSummaryRecursive: function (summary, field, callback) {\n                if (!summary) {\n                    return;\n                }\n                var value = callback(summary);\n                summary.set(field, value);\n                var parent = this.taskParent(summary);\n                if (parent) {\n                    this._updateSummaryRecursive(parent, field, callback);\n                }\n            },\n            _childRemoved: function (parentId, index) {\n                var parent = parentId === null ? null : this.get(parentId);\n                var children = this.taskChildren(parent);\n                for (var i = index, l = children.length; i < l; i++) {\n                    children[i].set('orderId', i);\n                }\n                this._resolveSummaryFields(parent);\n            },\n            _reorderSiblings: function (task, oldOrderId) {\n                var orderId = task.get('orderId');\n                var direction = orderId > oldOrderId;\n                var startIndex = direction ? oldOrderId : orderId;\n                var endIndex = direction ? orderId : oldOrderId;\n                var newIndex = direction ? startIndex : startIndex + 1;\n                var siblings = this.taskSiblings(task);\n                endIndex = Math.min(endIndex, siblings.length - 1);\n                for (var i = startIndex; i <= endIndex; i++) {\n                    if (siblings[i] === task) {\n                        continue;\n                    }\n                    siblings[i].set('orderId', newIndex);\n                    newIndex += 1;\n                }\n            },\n            _updateSummary: function (task) {\n                if (task !== null) {\n                    var childCount = this.taskChildren(task).length;\n                    task.set('summary', childCount > 0);\n                }\n            },\n            _toGanttTask: function (task) {\n                if (!(task instanceof GanttTask)) {\n                    var taskInfo = task;\n                    task = this._createNewModel();\n                    task.accept(taskInfo);\n                }\n                return task;\n            }\n        });\n        GanttDataSource.create = createDataSource(GanttDataSource, 'GanttDataSource');\n        extend(true, kendo.data, {\n            GanttDataSource: GanttDataSource,\n            GanttTask: GanttTask,\n            GanttDependencyDataSource: GanttDependencyDataSource,\n            GanttDependency: GanttDependency\n        });\n        var editors = {\n            desktop: {\n                dateRange: DATERANGEEDITOR,\n                resources: RESOURCESEDITOR\n            }\n        };\n        var Editor = kendo.Observable.extend({\n            init: function (element, options) {\n                kendo.Observable.fn.init.call(this);\n                this.element = element;\n                this.options = extend(true, {}, this.options, options);\n                this.createButton = this.options.createButton;\n            },\n            fields: function (editors, model) {\n                var that = this;\n                var options = this.options;\n                var messages = options.messages.editor;\n                var resources = options.resources;\n                var fields;\n                var click = function (e) {\n                    e.preventDefault();\n                    resources.editor(that.container.find(DOT + Gantt.styles.popup.resourcesField), model);\n                };\n                if (options.editable.template) {\n                    fields = $.map(model.fields, function (value, key) {\n                        return { field: key };\n                    });\n                } else {\n                    fields = [\n                        {\n                            field: 'title',\n                            title: messages.title\n                        },\n                        {\n                            field: 'start',\n                            title: messages.start,\n                            editor: editors.dateRange\n                        },\n                        {\n                            field: 'end',\n                            title: messages.end,\n                            editor: editors.dateRange\n                        },\n                        {\n                            field: 'percentComplete',\n                            title: messages.percentComplete,\n                            format: PERCENTAGE_FORMAT\n                        }\n                    ];\n                    if (model.get(resources.field)) {\n                        fields.push({\n                            field: resources.field,\n                            title: messages.resources,\n                            messages: messages,\n                            editor: editors.resources,\n                            click: click,\n                            styles: Gantt.styles.popup\n                        });\n                    }\n                }\n                return fields;\n            },\n            _buildEditTemplate: function (model, fields, editableFields) {\n                var resources = this.options.resources;\n                var template = this.options.editable.template;\n                var settings = extend({}, kendo.Template, this.options.templateSettings);\n                var paramName = settings.paramName;\n                var popupStyles = Gantt.styles.popup;\n                var html = '';\n                if (template) {\n                    if (typeof template === STRING) {\n                        template = window.unescape(template);\n                    }\n                    html += kendo.template(template, settings)(model);\n                } else {\n                    for (var i = 0, length = fields.length; i < length; i++) {\n                        var field = fields[i];\n                        html += '<div class=\"' + popupStyles.editLabel + '\"><label for=\"' + field.field + '\">' + (field.title || field.field || '') + '</label></div>';\n                        if (field.field === resources.field) {\n                            html += '<div class=\"' + popupStyles.resourcesField + '\" style=\"display:none\"></div>';\n                        }\n                        if (!model.editable || model.editable(field.field)) {\n                            editableFields.push(field);\n                            html += '<div ' + kendo.attr('container-for') + '=\"' + field.field + '\" class=\"' + popupStyles.editField + '\"></div>';\n                        } else {\n                            var tmpl = '#:';\n                            if (field.field) {\n                                field = kendo.expr(field.field, paramName);\n                                tmpl += field + '==null?\\'\\':' + field;\n                            } else {\n                                tmpl += '\\'\\'';\n                            }\n                            tmpl += '#';\n                            tmpl = kendo.template(tmpl, settings);\n                            html += '<div class=\"' + popupStyles.editField + '\">' + tmpl(model) + '</div>';\n                        }\n                    }\n                }\n                return html;\n            }\n        });\n        var PopupEditor = Editor.extend({\n            destroy: function () {\n                this.close();\n                this.unbind();\n            },\n            editTask: function (task) {\n                this.editable = this._createPopupEditor(task);\n            },\n            close: function () {\n                var that = this;\n                var destroy = function () {\n                    if (that.editable) {\n                        that.editable.destroy();\n                        that.editable = null;\n                        that.container = null;\n                    }\n                    if (that.popup) {\n                        that.popup.destroy();\n                        that.popup = null;\n                    }\n                };\n                if (this.editable && this.container.is(':visible')) {\n                    that.trigger('close', { window: that.container });\n                    this.container.data('kendoWindow').bind('deactivate', destroy).close();\n                } else {\n                    destroy();\n                }\n            },\n            showDialog: function (options) {\n                var buttons = options.buttons;\n                var popupStyles = Gantt.styles.popup;\n                var html = kendo.format('<div class=\"{0}\"><div class=\"{1}\"><p class=\"{2}\">{3}</p><div class=\"{4}\">', popupStyles.form, popupStyles.formContainer, popupStyles.message, options.text, popupStyles.buttonsContainer);\n                for (var i = 0, length = buttons.length; i < length; i++) {\n                    html += this.createButton(buttons[i]);\n                }\n                html += '</div></div></div>';\n                var wrapper = this.element;\n                if (this.popup) {\n                    this.popup.destroy();\n                }\n                var popup = this.popup = $(html).appendTo(wrapper).eq(0).on('click', DOT + popupStyles.button, function (e) {\n                    e.preventDefault();\n                    popup.close();\n                    var buttonIndex = $(e.currentTarget).index();\n                    buttons[buttonIndex].click();\n                }).kendoWindow({\n                    modal: true,\n                    autoFocus: false,\n                    resizable: false,\n                    draggable: false,\n                    title: options.title,\n                    visible: false,\n                    deactivate: function () {\n                        this.destroy();\n                        wrapper.focus();\n                    }\n                }).getKendoWindow();\n                popup.center().open();\n                popup.element.find('.k-primary').focus();\n            },\n            _createPopupEditor: function (task) {\n                var that = this;\n                var options = {};\n                var messages = this.options.messages;\n                var ganttStyles = Gantt.styles;\n                var popupStyles = ganttStyles.popup;\n                var html = kendo.format('<div {0}=\"{1}\" class=\"{2} {3}\"><div class=\"{4}\">', kendo.attr('uid'), task.uid, popupStyles.form, popupStyles.editForm, popupStyles.formContainer);\n                var fields = this.fields(editors.desktop, task);\n                var editableFields = [];\n                html += this._buildEditTemplate(task, fields, editableFields);\n                html += '<div class=\"' + popupStyles.buttonsContainer + '\">';\n                html += this.createButton({\n                    name: 'update',\n                    text: messages.save,\n                    className: Gantt.styles.primary\n                });\n                html += this.createButton({\n                    name: 'cancel',\n                    text: messages.cancel\n                });\n                if (that.options.editable.destroy !== false) {\n                    html += this.createButton({\n                        name: 'delete',\n                        text: messages.destroy\n                    });\n                }\n                html += '</div></div></div>';\n                var container = this.container = $(html).appendTo(this.element).eq(0).kendoWindow(extend({\n                    modal: true,\n                    resizable: false,\n                    draggable: true,\n                    title: messages.editor.editorTitle,\n                    visible: false,\n                    close: function (e) {\n                        if (e.userTriggered) {\n                            if (that.trigger('cancel', {\n                                    container: container,\n                                    model: task\n                                })) {\n                                e.preventDefault();\n                            }\n                        }\n                    }\n                }, options));\n                var editableWidget = container.kendoEditable({\n                    fields: editableFields,\n                    model: task,\n                    clearContainer: false,\n                    validateOnBlur: true,\n                    target: that.options.target\n                }).data('kendoEditable');\n                kendo.cycleForm(container);\n                if (!this.trigger('edit', {\n                        container: container,\n                        model: task\n                    })) {\n                    container.data('kendoWindow').center().open();\n                    container.on(CLICK + NS, DOT + ganttStyles.buttonCancel, function (e) {\n                        e.preventDefault();\n                        e.stopPropagation();\n                        that.trigger('cancel', {\n                            container: container,\n                            model: task\n                        });\n                    });\n                    container.on(CLICK + NS, DOT + ganttStyles.buttonSave, function (e) {\n                        e.preventDefault();\n                        e.stopPropagation();\n                        var fields = that.fields(editors.desktop, task);\n                        var updateInfo = {};\n                        var field;\n                        for (var i = 0, length = fields.length; i < length; i++) {\n                            field = fields[i].field;\n                            updateInfo[field] = task.get(field);\n                        }\n                        that.trigger('save', {\n                            container: container,\n                            model: task,\n                            updateInfo: updateInfo\n                        });\n                    });\n                    container.on(CLICK + NS, DOT + ganttStyles.buttonDelete, function (e) {\n                        e.preventDefault();\n                        e.stopPropagation();\n                        that.trigger('remove', {\n                            container: container,\n                            model: task\n                        });\n                    });\n                } else {\n                    that.trigger('cancel', {\n                        container: container,\n                        model: task\n                    });\n                }\n                return editableWidget;\n            }\n        });\n        var ResourceEditor = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this.wrapper = this.element;\n                this.model = this.options.model;\n                this.resourcesField = this.options.resourcesField;\n                this.createButton = this.options.createButton;\n                this._initContainer();\n                this._attachHandlers();\n            },\n            events: ['save'],\n            open: function () {\n                this.window.center().open();\n            },\n            close: function () {\n                this.window.bind('deactivate', proxy(this.destroy, this)).close();\n            },\n            destroy: function () {\n                this._dettachHandlers();\n                this.grid.destroy();\n                this.grid = null;\n                this.window.destroy();\n                this.window = null;\n                Widget.fn.destroy.call(this);\n                kendo.destroy(this.wrapper);\n                this.element = this.wrapper = null;\n            },\n            _attachHandlers: function () {\n                var ganttStyles = Gantt.styles;\n                var grid = this.grid;\n                var closeHandler = this._cancelProxy = proxy(this._cancel, this);\n                this.container.on(CLICK + NS, DOT + ganttStyles.buttonCancel, this._cancelProxy);\n                this._saveProxy = proxy(this._save, this);\n                this.container.on(CLICK + NS, DOT + ganttStyles.buttonSave, this._saveProxy);\n                this.window.bind('close', function (e) {\n                    if (e.userTriggered) {\n                        closeHandler(e);\n                    }\n                });\n                grid.wrapper.on(CLICK + NS, 'input[type=\\'checkbox\\']', function () {\n                    var element = $(this);\n                    var row = $(element).closest('tr');\n                    var model = grid.dataSource.getByUid(row.attr(kendo.attr('uid')));\n                    var value = $(element).is(':checked') ? 1 : '';\n                    model.set('value', value);\n                });\n            },\n            _dettachHandlers: function () {\n                this._cancelProxy = null;\n                this._saveProxy = null;\n                this.container.off(NS);\n                this.grid.wrapper.off();\n            },\n            _cancel: function (e) {\n                e.preventDefault();\n                this.close();\n            },\n            _save: function (e) {\n                e.preventDefault();\n                this._updateModel();\n                if (!this.wrapper.is(DOT + Gantt.styles.popup.resourcesField)) {\n                    this.trigger('save', {\n                        container: this.wrapper,\n                        model: this.model\n                    });\n                }\n                this.close();\n            },\n            _initContainer: function () {\n                var that = this;\n                var popupStyles = Gantt.styles.popup;\n                var dom = kendo.format('<div class=\"{0} {1}\"><div class=\"{2} {3}\"/></div>\"', popupStyles.form, popupStyles.editForm, popupStyles.formContainer, popupStyles.resourcesFormContainer);\n                dom = $(dom);\n                this.container = dom.find(DOT + popupStyles.resourcesFormContainer);\n                this.window = dom.kendoWindow({\n                    modal: true,\n                    resizable: false,\n                    draggable: true,\n                    visible: false,\n                    title: this.options.messages.resourcesEditorTitle,\n                    open: function () {\n                        that.grid.resize(true);\n                    }\n                }).data('kendoWindow');\n                this._resourceGrid();\n                this._createButtons();\n            },\n            _resourceGrid: function () {\n                var that = this;\n                var messages = this.options.messages;\n                var element = $('<div id=\"resources-grid\"/>').appendTo(this.container);\n                this.grid = new kendo.ui.Grid(element, {\n                    columns: [\n                        {\n                            field: 'name',\n                            title: messages.resourcesHeader,\n                            template: '<label><input type=\\'checkbox\\' value=\\'#=name#\\'' + '# if (value > 0 && value !== null) {#' + 'checked=\\'checked\\'' + '# } #' + '/>#=name#</labe>'\n                        },\n                        {\n                            field: 'value',\n                            title: messages.unitsHeader,\n                            template: function (dataItem) {\n                                var valueFormat = dataItem.format;\n                                var value = dataItem.value !== null ? dataItem.value : '';\n                                return valueFormat ? kendo.toString(value, valueFormat) : value;\n                            }\n                        }\n                    ],\n                    height: 280,\n                    sortable: true,\n                    editable: true,\n                    filterable: true,\n                    dataSource: {\n                        data: that.options.data,\n                        schema: {\n                            model: {\n                                id: 'id',\n                                fields: {\n                                    id: { from: 'id' },\n                                    name: {\n                                        from: 'name',\n                                        type: 'string',\n                                        editable: false\n                                    },\n                                    value: {\n                                        from: 'value',\n                                        type: 'number',\n                                        validation: this.options.unitsValidation\n                                    },\n                                    format: {\n                                        from: 'format',\n                                        type: 'string'\n                                    }\n                                }\n                            }\n                        }\n                    },\n                    save: function (e) {\n                        var value = !!e.values.value;\n                        e.container.parent().find('input[type=\\'checkbox\\']').prop('checked', value);\n                    }\n                });\n            },\n            _createButtons: function () {\n                var buttons = this.options.buttons;\n                var html = '<div class=\"' + Gantt.styles.popup.buttonsContainer + '\">';\n                for (var i = 0, length = buttons.length; i < length; i++) {\n                    html += this.createButton(buttons[i]);\n                }\n                html += '</div>';\n                this.container.append(html);\n            },\n            _updateModel: function () {\n                var resources = [];\n                var value;\n                var data = this.grid.dataSource.data();\n                for (var i = 0, length = data.length; i < length; i++) {\n                    value = data[i].get('value');\n                    if (value !== null && value > 0) {\n                        resources.push(data[i]);\n                    }\n                }\n                this.model[this.resourcesField] = resources;\n            }\n        });\n        var Gantt = Widget.extend({\n            init: function (element, options, events) {\n                if (isArray(options)) {\n                    options = { dataSource: options };\n                }\n                defaultCommands = {\n                    append: {\n                        text: 'Add Task',\n                        action: 'add',\n                        className: Gantt.styles.toolbar.appendButton,\n                        iconClass: Gantt.styles.toolbar.iconPlus\n                    },\n                    pdf: {\n                        text: 'Export to PDF',\n                        className: Gantt.styles.toolbar.pdfButton,\n                        iconClass: Gantt.styles.toolbar.iconPdf\n                    }\n                };\n                Widget.fn.init.call(this, element, options);\n                if (events) {\n                    this._events = events;\n                }\n                this._wrapper();\n                this._resources();\n                if (!this.options.views || !this.options.views.length) {\n                    this.options.views = [\n                        'day',\n                        'week',\n                        'month'\n                    ];\n                }\n                this._timeline();\n                this._toolbar();\n                this._footer();\n                this._adjustDimensions();\n                this._preventRefresh = true;\n                this.view(this.timeline._selectedViewName);\n                this._preventRefresh = false;\n                this._dataSource();\n                this._assignments();\n                this._dropDowns();\n                this._list();\n                this._dependencies();\n                this._resizable();\n                this._scrollable();\n                this._dataBind();\n                this._attachEvents();\n                this._createEditor();\n                kendo.notify(this);\n            },\n            events: [\n                'dataBinding',\n                'dataBound',\n                'add',\n                'edit',\n                'remove',\n                'cancel',\n                'save',\n                'change',\n                'navigate',\n                'moveStart',\n                'move',\n                'moveEnd',\n                'resizeStart',\n                'resize',\n                'resizeEnd',\n                'columnResize'\n            ],\n            options: {\n                name: 'Gantt',\n                autoBind: true,\n                navigatable: false,\n                selectable: true,\n                editable: true,\n                resizable: false,\n                columnResizeHandleWidth: defaultIndicatorWidth,\n                columns: [],\n                views: [],\n                dataSource: {},\n                dependencies: {},\n                resources: {},\n                assignments: {},\n                taskTemplate: null,\n                messages: {\n                    save: 'Save',\n                    cancel: 'Cancel',\n                    destroy: 'Delete',\n                    deleteTaskConfirmation: TASK_DELETE_CONFIRM,\n                    deleteDependencyConfirmation: DEPENDENCY_DELETE_CONFIRM,\n                    deleteTaskWindowTitle: 'Delete task',\n                    deleteDependencyWindowTitle: 'Delete dependency',\n                    views: {\n                        day: 'Day',\n                        week: 'Week',\n                        month: 'Month',\n                        year: 'Year',\n                        start: 'Start',\n                        end: 'End'\n                    },\n                    actions: {\n                        append: 'Add Task',\n                        addChild: 'Add Child',\n                        insertBefore: 'Add Above',\n                        insertAfter: 'Add Below',\n                        pdf: 'Export to PDF'\n                    },\n                    editor: {\n                        editorTitle: 'Task',\n                        resourcesEditorTitle: 'Resources',\n                        title: 'Title',\n                        start: 'Start',\n                        end: 'End',\n                        percentComplete: 'Complete',\n                        resources: 'Resources',\n                        assignButton: 'Assign',\n                        resourcesHeader: 'Resources',\n                        unitsHeader: 'Units'\n                    }\n                },\n                showWorkHours: true,\n                showWorkDays: true,\n                toolbar: null,\n                workDayStart: new Date(1980, 1, 1, 8, 0, 0),\n                workDayEnd: new Date(1980, 1, 1, 17, 0, 0),\n                workWeekStart: 1,\n                workWeekEnd: 5,\n                hourSpan: 1,\n                snap: true,\n                height: 600,\n                listWidth: '30%',\n                rowHeight: null\n            },\n            select: function (value) {\n                var list = this.list;\n                if (!value) {\n                    return list.select();\n                }\n                list.select(value);\n                this.list.element.find('table[role=treegrid]').focus();\n                return;\n            },\n            clearSelection: function () {\n                this.list.clearSelection();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                if (this.dataSource) {\n                    this.dataSource.unbind('change', this._refreshHandler);\n                    this.dataSource.unbind('progress', this._progressHandler);\n                    this.dataSource.unbind('error', this._errorHandler);\n                }\n                if (this.dependencies) {\n                    this.dependencies.unbind('change', this._dependencyRefreshHandler);\n                    this.dependencies.unbind('error', this._dependencyErrorHandler);\n                }\n                if (this.timeline) {\n                    this.timeline.unbind();\n                    this.timeline.destroy();\n                }\n                if (this.list) {\n                    this.list.unbind();\n                    this.list.destroy();\n                }\n                if (this.footerDropDown) {\n                    this.footerDropDown.destroy();\n                }\n                if (this.headerDropDown) {\n                    this.headerDropDown.destroy();\n                }\n                if (this._editor) {\n                    this._editor.destroy();\n                }\n                if (this._resizeDraggable) {\n                    this._resizeDraggable.destroy();\n                }\n                this.toolbar.off(NS);\n                if (supportsMedia) {\n                    this._mediaQuery.removeListener(this._mediaQueryHandler);\n                    this._mediaQuery = null;\n                }\n                $(window).off('resize' + NS, this._resizeHandler);\n                $(this.wrapper).off(NS);\n                this.toolbar = null;\n                this.footer = null;\n            },\n            setOptions: function (options) {\n                var newOptions = kendo.deepExtend({}, this.options, options);\n                var events = this._events;\n                if (!options.views) {\n                    var selectedView = this.view().name;\n                    newOptions.views = $.map(this.options.views, function (view) {\n                        var isSettings = isPlainObject(view);\n                        var name = isSettings ? typeof view.type !== 'string' ? view.title : view.type : view;\n                        if (selectedView === name) {\n                            if (isSettings) {\n                                view.selected = true;\n                            } else {\n                                view = {\n                                    type: name,\n                                    selected: true\n                                };\n                            }\n                        } else if (isSettings) {\n                            view.selected = false;\n                        }\n                        return view;\n                    });\n                }\n                if (!options.dataSource) {\n                    newOptions.dataSource = this.dataSource;\n                }\n                if (!options.dependencies) {\n                    newOptions.dependencies = this.dependencies;\n                }\n                if (!options.resources) {\n                    newOptions.resources = this.resources;\n                }\n                if (!options.assignments) {\n                    newOptions.assignments = this.assignments;\n                }\n                this.destroy();\n                this.element.empty();\n                this.options = null;\n                this.init(this.element, newOptions, events);\n                Widget.fn._setEvents.call(this, newOptions);\n            },\n            _attachEvents: function () {\n                this._resizeHandler = proxy(this.resize, this, false);\n                $(window).on('resize' + NS, this._resizeHandler);\n            },\n            _wrapper: function () {\n                var ganttStyles = Gantt.styles;\n                var splitBarHandleClassName = [\n                    ganttStyles.icon,\n                    ganttStyles.resizeHandle\n                ].join(' ');\n                var options = this.options;\n                var height = options.height;\n                var width = options.width;\n                this.wrapper = this.element.addClass(ganttStyles.wrapper).append('<div class=\\'' + ganttStyles.listWrapper + '\\'><div></div></div>').append('<div class=\\'' + ganttStyles.splitBarWrapper + '\\'><div class=\\'' + splitBarHandleClassName + '\\'></div></div>').append('<div class=\\'' + ganttStyles.timelineWrapper + '\\'><div></div></div>');\n                this.wrapper.find(DOT + ganttStyles.list).width(options.listWidth);\n                if (height) {\n                    this.wrapper.height(height);\n                }\n                if (width) {\n                    this.wrapper.width(width);\n                }\n                if (options.rowHeight) {\n                    this.wrapper.addClass(ganttStyles.rowHeight);\n                }\n            },\n            _toolbar: function () {\n                var that = this;\n                var ganttStyles = Gantt.styles;\n                var viewsSelector = DOT + ganttStyles.toolbar.views + ' > li';\n                var pdfSelector = DOT + ganttStyles.toolbar.pdfButton;\n                var toggleSelector = DOT + ganttStyles.buttonToggle;\n                var contentSelector = DOT + ganttStyles.gridContent;\n                var treelist = $(DOT + ganttStyles.list);\n                var timeline = $(DOT + ganttStyles.timeline);\n                var hoveredClassName = ganttStyles.hovered;\n                var actions = this.options.toolbar;\n                var actionsWrap = $('<div class=\\'' + ganttStyles.toolbar.actions + '\\'>');\n                var toolbar;\n                var views;\n                var toggleButton;\n                var handler = function (e) {\n                    if (e.matches) {\n                        treelist.css({\n                            'display': 'none',\n                            'max-width': 0\n                        });\n                    } else {\n                        treelist.css({\n                            'display': 'inline-block',\n                            'width': '30%',\n                            'max-width': 'none'\n                        });\n                        timeline.css('display', 'inline-block');\n                        that.refresh();\n                        timeline.find(contentSelector).scrollTop(that.scrollTop);\n                    }\n                    that._resize();\n                };\n                if (!isFunction(actions)) {\n                    actions = typeof actions === STRING ? actions : this._actions(actions);\n                    actions = proxy(kendo.template(actions), this);\n                }\n                toggleButton = $(TOGGLE_BUTTON_TEMPLATE({ styles: ganttStyles.toolbar }));\n                views = $(HEADER_VIEWS_TEMPLATE({\n                    ns: kendo.ns,\n                    views: this.timeline.views,\n                    styles: ganttStyles.toolbar\n                }));\n                actionsWrap.append(actions({}));\n                toolbar = $('<div class=\\'' + ganttStyles.toolbar.headerWrapper + '\\'>').append(toggleButton).append(views).append(actionsWrap);\n                if (views.find('li').length > 1) {\n                    views.prepend(VIEWBUTTONTEMPLATE({ styles: ganttStyles.toolbar }));\n                }\n                this.wrapper.prepend(toolbar);\n                this.toolbar = toolbar;\n                if (supportsMedia) {\n                    this._mediaQueryHandler = proxy(handler, this);\n                    this._mediaQuery = window.matchMedia('(max-width: 480px)');\n                    this._mediaQuery.addListener(this._mediaQueryHandler);\n                }\n                toolbar.on(CLICK + NS, viewsSelector, function (e) {\n                    e.preventDefault();\n                    var list = that.list;\n                    var name = $(this).attr(kendo.attr('name'));\n                    var currentView = views.find(DOT + ganttStyles.toolbar.currentView);\n                    if (currentView.is(':visible')) {\n                        currentView.parent().toggleClass(ganttStyles.toolbar.expanded);\n                    }\n                    if (list.editable && list.editable.trigger('validate')) {\n                        return;\n                    }\n                    if (!that.trigger('navigate', { view: name })) {\n                        that.view(name);\n                    }\n                    that.toolbar.find(DOT + ganttStyles.focused).removeClass(ganttStyles.focused);\n                }).on('keydown' + NS, viewsSelector, function (e) {\n                    var views = $(DOT + ganttStyles.toolbar.views).children(':not(.k-current-view)');\n                    var focusedViewIndex = views.index(that._focusedView && that._focusedView[0] || views.closest(DOT + ganttStyles.selected)[0]);\n                    if (e.keyCode === keys.RIGHT) {\n                        $(that.toolbar.find(DOT + ganttStyles.focused)).removeClass(ganttStyles.focused);\n                        that._focusedView = focusedViewIndex + 1 === views.length ? $(views[0]) : $(views[focusedViewIndex + 1]);\n                        that._focusedView.focus().addClass(ganttStyles.focused);\n                        e.preventDefault();\n                    } else if (e.keyCode === keys.LEFT) {\n                        $(that.toolbar.find(DOT + ganttStyles.focused)).removeClass(ganttStyles.focused);\n                        that._focusedView = focusedViewIndex === 0 ? $(views[views.length - 1]) : $(views[focusedViewIndex - 1]);\n                        that._focusedView.focus().addClass(ganttStyles.focused);\n                        e.preventDefault();\n                    } else if (e.keyCode === keys.DOWN && that.toolbar.find(DOT + ganttStyles.toolbar.currentView).parent().hasClass(ganttStyles.toolbar.expanded)) {\n                        $(that.toolbar.find(DOT + ganttStyles.focused)).removeClass(ganttStyles.focused);\n                        that._focusedView = focusedViewIndex + 1 === views.length ? $(views[0]) : $(views[focusedViewIndex + 1]);\n                        that._focusedView.focus().addClass(ganttStyles.focused);\n                        e.preventDefault();\n                    } else if (e.keyCode === keys.UP && that.toolbar.find(DOT + ganttStyles.toolbar.currentView).parent().hasClass(ganttStyles.toolbar.expanded)) {\n                        $(that.toolbar.find(DOT + ganttStyles.focused)).removeClass(ganttStyles.focused);\n                        that._focusedView = focusedViewIndex === 0 ? $(views[views.length - 1]) : $(views[focusedViewIndex - 1]);\n                        that._focusedView.focus().addClass(ganttStyles.focused);\n                        e.preventDefault();\n                    } else if ((e.keyCode === keys.ENTER || e.keyCode === keys.SPACEBAR) && that._focusedView) {\n                        that.view(that._focusedView.text().toLowerCase());\n                        e.preventDefault();\n                    } else if ((e.keyCode === keys.SPACEBAR || e.keyCode === keys.ENTER || e.keyCode === keys.DOWN && e.altKey) && that.toolbar.find(DOT + ganttStyles.toolbar.currentView + ' > a').hasClass(ganttStyles.focused)) {\n                        that.toolbar.find(DOT + ganttStyles.toolbar.currentView).parent().toggleClass(ganttStyles.toolbar.expanded);\n                        e.preventDefault();\n                    } else if (e.keyCode === keys.ESC && that.toolbar.find(DOT + ganttStyles.toolbar.currentView).parent().hasClass(ganttStyles.toolbar.expanded)) {\n                        that.toolbar.find(DOT + ganttStyles.toolbar.currentView).parent().toggleClass(ganttStyles.toolbar.expanded).blur();\n                        that._focusedView = null;\n                        that.toolbar.find(DOT + ganttStyles.toolbar.currentView + ' > a').addClass(ganttStyles.focused).focus();\n                        e.preventDefault();\n                    } else if (e.keyCode >= 49 && e.keyCode <= 57) {\n                        that.view(that.timeline._viewByIndex(e.keyCode - 49));\n                    }\n                }).on(CLICK + NS, pdfSelector, function (e) {\n                    e.preventDefault();\n                    that.saveAsPDF();\n                }).on(CLICK + NS, toggleSelector, function (e) {\n                    e.preventDefault();\n                    if (treelist.is(':visible')) {\n                        treelist.css({\n                            'display': 'none',\n                            'width': '0'\n                        });\n                        timeline.css({\n                            'display': 'inline-block',\n                            'width': '100%'\n                        });\n                        that.refresh();\n                        timeline.find(contentSelector).scrollTop(that.scrollTop);\n                    } else {\n                        timeline.css({\n                            'display': 'none',\n                            'width': 0\n                        });\n                        treelist.css({\n                            'display': 'inline-block',\n                            'width': '100%',\n                            'max-width': 'none'\n                        }).find(contentSelector).scrollTop(that.scrollTop);\n                    }\n                    that._resize();\n                });\n                this.wrapper.on('focusout' + NS, function (e) {\n                    if (!$(e.relatedTarget).closest(DOT + ganttStyles.toolbar.toolbar).length) {\n                        that.toolbar.find(DOT + ganttStyles.focused).removeClass(ganttStyles.focused);\n                    }\n                    if (!$(e.relatedTarget).closest(DOT + ganttStyles.toolbar.views).length) {\n                        that.toolbar.find(DOT + ganttStyles.toolbar.views).removeClass(ganttStyles.toolbar.expanded);\n                    }\n                }).find(DOT + ganttStyles.toolbar.toolbar + ' li').hover(function () {\n                    $(this).addClass(hoveredClassName);\n                }, function () {\n                    $(this).removeClass(hoveredClassName);\n                });\n            },\n            _actions: function () {\n                var options = this.options;\n                var editable = options.editable;\n                var actions = options.toolbar;\n                var html = '';\n                if (!isArray(actions)) {\n                    if (editable && editable.create !== false) {\n                        actions = ['append'];\n                    } else {\n                        return html;\n                    }\n                }\n                for (var i = 0, length = actions.length; i < length; i++) {\n                    html += this._createButton(actions[i]);\n                }\n                return html;\n            },\n            _footer: function () {\n                var editable = this.options.editable;\n                if (!editable || editable.create === false) {\n                    return;\n                }\n                var ganttStyles = Gantt.styles.toolbar;\n                var messages = this.options.messages.actions;\n                var button = $(kendo.template(BUTTON_TEMPLATE)(extend(true, { styles: ganttStyles }, defaultCommands.append, { text: messages.append })));\n                var actionsWrap = $('<div class=\\'' + ganttStyles.actions + '\\'>').append(button);\n                var footer = $('<div class=\\'' + ganttStyles.footerWrapper + '\\'>').append(actionsWrap);\n                this.wrapper.append(footer);\n                this.footer = footer;\n            },\n            _createButton: function (command) {\n                var template = command.template || BUTTON_TEMPLATE;\n                var messages = this.options.messages.actions;\n                var commandName = typeof command === STRING ? command : command.name || command.text;\n                var className = defaultCommands[commandName] ? defaultCommands[commandName].className : 'k-gantt-' + (commandName || '').replace(/\\s/g, '');\n                var options = {\n                    iconClass: '',\n                    action: '',\n                    text: commandName,\n                    className: className,\n                    styles: Gantt.styles.toolbar\n                };\n                if (!commandName && !(isPlainObject(command) && command.template)) {\n                    throw new Error('Custom commands should have name specified');\n                }\n                options = extend(true, options, defaultCommands[commandName], { text: messages[commandName] });\n                if (isPlainObject(command)) {\n                    if (command.className && inArray(options.className, command.className.split(' ')) < 0) {\n                        command.className += ' ' + options.className;\n                    }\n                    options = extend(true, options, command);\n                }\n                return kendo.template(template)(options);\n            },\n            _adjustDimensions: function () {\n                var element = this.element;\n                var ganttStyles = Gantt.styles;\n                var listSelector = DOT + ganttStyles.list;\n                var timelineSelector = DOT + ganttStyles.timeline;\n                var splitBarSelector = DOT + ganttStyles.splitBar;\n                var toolbarHeight = outerHeight(this.toolbar);\n                var footerHeight = this.footer ? outerHeight(this.footer) : 0;\n                var totalHeight = element.height();\n                var totalWidth = element.width();\n                var splitBarWidth = outerWidth(element.find(splitBarSelector));\n                var treeListWidth = outerWidth(element.find(listSelector));\n                element.children([\n                    listSelector,\n                    timelineSelector,\n                    splitBarSelector\n                ].join(',')).height(totalHeight - (toolbarHeight + footerHeight)).end().children(timelineSelector).width(totalWidth - (splitBarWidth + treeListWidth));\n                if (totalWidth < treeListWidth + splitBarWidth) {\n                    element.find(listSelector).width(totalWidth - splitBarWidth);\n                }\n            },\n            _scrollTo: function (value) {\n                var view = this.timeline.view();\n                var list = this.list;\n                var attr = kendo.attr('uid');\n                var id = typeof value === 'string' ? value : value.closest('tr' + selector()).attr(attr);\n                var action;\n                var scrollTarget;\n                var scrollIntoView = function () {\n                    if (scrollTarget.length !== 0) {\n                        action();\n                    }\n                };\n                if (view.content.is(':visible')) {\n                    scrollTarget = view.content.find(selector(id));\n                    action = function () {\n                        view._scrollTo(scrollTarget);\n                    };\n                } else {\n                    scrollTarget = list.content.find(selector(id));\n                    action = function () {\n                        scrollTarget.get(0).scrollIntoView();\n                    };\n                }\n                scrollIntoView();\n            },\n            _dropDowns: function () {\n                var that = this;\n                var actionsSelector = DOT + Gantt.styles.toolbar.actions;\n                var actionMessages = this.options.messages.actions;\n                var timeline = this.timeline;\n                var editable = this.options.editable;\n                var handler = function (e) {\n                    var type = e.type;\n                    var orderId;\n                    var dataSource = that.dataSource;\n                    var task = dataSource._createNewModel();\n                    var selected = that.dataItem(that.select());\n                    var parent = dataSource.taskParent(selected);\n                    var firstSlot = timeline.view()._timeSlots()[0];\n                    var target = type === 'add' ? selected : parent;\n                    var editable = that.list.editable;\n                    if (editable && editable.trigger('validate')) {\n                        return;\n                    }\n                    task.set('title', 'New task');\n                    if (target) {\n                        task.set('parentId', target.get('id'));\n                        task.set('start', target.get('start'));\n                        task.set('end', target.get('end'));\n                    } else {\n                        task.set('start', firstSlot.start);\n                        task.set('end', firstSlot.end);\n                    }\n                    if (type !== 'add') {\n                        orderId = selected.get('orderId');\n                        orderId = type === 'insert-before' ? orderId : orderId + 1;\n                    }\n                    that._createTask(task, orderId);\n                };\n                if (!editable || editable.create === false) {\n                    return;\n                }\n                this.footerDropDown = new TaskDropDown(this.footer.children(actionsSelector).eq(0), {\n                    messages: { actions: actionMessages },\n                    direction: 'up',\n                    animation: { open: { effects: 'slideIn:up' } },\n                    navigatable: that.options.navigatable\n                });\n                this.headerDropDown = new TaskDropDown(this.toolbar.children(actionsSelector).eq(0), {\n                    messages: { actions: actionMessages },\n                    navigatable: that.options.navigatable\n                });\n                this.footerDropDown.bind('command', handler);\n                this.headerDropDown.bind('command', handler);\n            },\n            _list: function () {\n                var that = this;\n                var navigatable = that.options.navigatable;\n                var ganttStyles = Gantt.styles;\n                var listWrapper = this.wrapper.find(DOT + ganttStyles.list);\n                var element = listWrapper.find('> div');\n                var toggleButtons = this.wrapper.find(DOT + ganttStyles.toolbar.actions + ' > button');\n                var options = {\n                    columns: this.options.columns || [],\n                    dataSource: this.dataSource,\n                    selectable: this.options.selectable,\n                    editable: this.options.editable,\n                    resizable: this.options.resizable,\n                    columnResizeHandleWidth: this.options.columnResizeHandleWidth,\n                    listWidth: outerWidth(listWrapper),\n                    resourcesField: this.resources.field,\n                    rowHeight: this.options.rowHeight\n                };\n                var columns = options.columns;\n                var column;\n                var restoreFocus = function () {\n                    if (navigatable) {\n                        that._current(that._cachedCurrent);\n                        focusTable(that.list.content.find('table'), true);\n                    }\n                    delete that._cachedCurrent;\n                };\n                for (var i = 0; i < columns.length; i++) {\n                    column = columns[i];\n                    if (column.field === this.resources.field && typeof column.editor !== 'function') {\n                        column.editor = proxy(this._createResourceEditor, this);\n                    }\n                }\n                this.list = new kendo.ui.GanttList(element, options);\n                this.list.bind('render', function () {\n                    that._navigatable();\n                }, true).bind('edit', function (e) {\n                    that._cachedCurrent = e.cell;\n                    if (that.trigger('edit', {\n                            task: e.model,\n                            container: e.cell\n                        })) {\n                        e.preventDefault();\n                    }\n                }).bind('cancel', function (e) {\n                    if (that.trigger('cancel', {\n                            task: e.model,\n                            container: e.cell\n                        })) {\n                        e.preventDefault();\n                    }\n                    restoreFocus();\n                }).bind('update', function (e) {\n                    that._updateTask(e.task, e.updateInfo);\n                    restoreFocus();\n                }).bind('change', function () {\n                    that.trigger('change');\n                    var selection = that.list.select();\n                    if (selection.length) {\n                        toggleButtons.removeAttr('data-action', 'add');\n                        that.timeline.select('[data-uid=\\'' + selection.attr('data-uid') + '\\']');\n                    } else {\n                        toggleButtons.attr('data-action', 'add');\n                        that.timeline.clearSelection();\n                    }\n                }).bind('columnResize', function (e) {\n                    that.trigger('columnResize', {\n                        column: e.column,\n                        oldWidth: e.oldWidth,\n                        newWidth: e.newWidth\n                    });\n                });\n            },\n            _timeline: function () {\n                var that = this;\n                var ganttStyles = Gantt.styles;\n                var options = trimOptions(extend(true, { resourcesField: this.resources.field }, this.options));\n                var element = this.wrapper.find(DOT + ganttStyles.timeline + ' > div');\n                var currentViewSelector = DOT + ganttStyles.toolbar.currentView + ' > ' + DOT + ganttStyles.toolbar.link;\n                this.timeline = new kendo.ui.GanttTimeline(element, options);\n                this.timeline.bind('navigate', function (e) {\n                    var viewName = e.view.replace(/\\./g, '\\\\.').toLowerCase();\n                    var text = that.toolbar.find(DOT + ganttStyles.toolbar.views + ' > li').removeClass(ganttStyles.selected).end().find(DOT + ganttStyles.toolbar.viewButton + '-' + viewName).addClass(ganttStyles.selected).find(DOT + ganttStyles.toolbar.link).text();\n                    that.toolbar.find(currentViewSelector).text(text);\n                    that.refresh();\n                }).bind('moveStart', function (e) {\n                    var editable = that.list.editable;\n                    if (editable && editable.trigger('validate')) {\n                        e.preventDefault();\n                        return;\n                    }\n                    if (that.trigger('moveStart', { task: e.task })) {\n                        e.preventDefault();\n                    }\n                }).bind('move', function (e) {\n                    var task = e.task;\n                    var start = e.start;\n                    var end = new Date(start.getTime() + task.duration());\n                    if (that.trigger('move', {\n                            task: task,\n                            start: start,\n                            end: end\n                        })) {\n                        e.preventDefault();\n                    }\n                }).bind('moveEnd', function (e) {\n                    var task = e.task;\n                    var start = e.start;\n                    var end = new Date(start.getTime() + task.duration());\n                    if (!that.trigger('moveEnd', {\n                            task: task,\n                            start: start,\n                            end: end\n                        })) {\n                        that._updateTask(that.dataSource.getByUid(task.uid), {\n                            start: start,\n                            end: end\n                        });\n                    }\n                }).bind('resizeStart', function (e) {\n                    var editable = that.list.editable;\n                    if (editable && editable.trigger('validate')) {\n                        e.preventDefault();\n                        return;\n                    }\n                    if (that.trigger('resizeStart', { task: e.task })) {\n                        e.preventDefault();\n                    }\n                }).bind('resize', function (e) {\n                    if (that.trigger('resize', {\n                            task: e.task,\n                            start: e.start,\n                            end: e.end\n                        })) {\n                        e.preventDefault();\n                    }\n                }).bind('resizeEnd', function (e) {\n                    var task = e.task;\n                    var updateInfo = {};\n                    if (e.resizeStart) {\n                        updateInfo.start = e.start;\n                    } else {\n                        updateInfo.end = e.end;\n                    }\n                    if (!that.trigger('resizeEnd', {\n                            task: task,\n                            start: e.start,\n                            end: e.end\n                        })) {\n                        that._updateTask(that.dataSource.getByUid(task.uid), updateInfo);\n                    }\n                }).bind('percentResizeStart', function (e) {\n                    var editable = that.list.editable;\n                    if (editable && editable.trigger('validate')) {\n                        e.preventDefault();\n                    }\n                }).bind('percentResizeEnd', function (e) {\n                    that._updateTask(that.dataSource.getByUid(e.task.uid), { percentComplete: e.percentComplete });\n                }).bind('dependencyDragStart', function (e) {\n                    var editable = that.list.editable;\n                    if (editable && editable.trigger('validate')) {\n                        e.preventDefault();\n                    }\n                }).bind('dependencyDragEnd', function (e) {\n                    var dependency = that.dependencies._createNewModel({\n                        type: e.type,\n                        predecessorId: e.predecessor.id,\n                        successorId: e.successor.id\n                    });\n                    that._createDependency(dependency);\n                }).bind('select', function (e) {\n                    var editable = that.list.editable;\n                    if (editable) {\n                        editable.trigger('validate');\n                    }\n                    that.select('[data-uid=\\'' + e.uid + '\\']');\n                }).bind('editTask', function (e) {\n                    var editable = that.list.editable;\n                    if (editable && editable.trigger('validate')) {\n                        return;\n                    }\n                    that.editTask(e.uid);\n                }).bind('clear', function () {\n                    that.clearSelection();\n                }).bind('removeTask', function (e) {\n                    var editable = that.list.editable;\n                    if (editable && editable.trigger('validate')) {\n                        return;\n                    }\n                    that.removeTask(that.dataSource.getByUid(e.uid));\n                }).bind('removeDependency', function (e) {\n                    var editable = that.list.editable;\n                    if (editable && editable.trigger('validate')) {\n                        return;\n                    }\n                    that.removeDependency(that.dependencies.getByUid(e.uid));\n                });\n            },\n            _dataSource: function () {\n                var options = this.options;\n                var dataSource = options.dataSource;\n                dataSource = isArray(dataSource) ? { data: dataSource } : dataSource;\n                if (this.dataSource && this._refreshHandler) {\n                    this.dataSource.unbind('change', this._refreshHandler).unbind('progress', this._progressHandler).unbind('error', this._errorHandler);\n                } else {\n                    this._refreshHandler = proxy(this.refresh, this);\n                    this._progressHandler = proxy(this._requestStart, this);\n                    this._errorHandler = proxy(this._error, this);\n                }\n                this.dataSource = kendo.data.GanttDataSource.create(dataSource).bind('change', this._refreshHandler).bind('progress', this._progressHandler).bind('error', this._errorHandler);\n            },\n            _dependencies: function () {\n                var dependencies = this.options.dependencies || {};\n                var dataSource = isArray(dependencies) ? { data: dependencies } : dependencies;\n                if (this.dependencies && this._dependencyRefreshHandler) {\n                    this.dependencies.unbind('change', this._dependencyRefreshHandler).unbind('error', this._dependencyErrorHandler);\n                } else {\n                    this._dependencyRefreshHandler = proxy(this.refreshDependencies, this);\n                    this._dependencyErrorHandler = proxy(this._error, this);\n                }\n                this.dependencies = kendo.data.GanttDependencyDataSource.create(dataSource).bind('change', this._dependencyRefreshHandler).bind('error', this._dependencyErrorHandler);\n            },\n            _resources: function () {\n                var resources = this.options.resources;\n                var dataSource = resources.dataSource || {};\n                this.resources = {\n                    field: 'resources',\n                    dataTextField: 'name',\n                    dataColorField: 'color',\n                    dataFormatField: 'format'\n                };\n                extend(this.resources, resources);\n                this.resources.dataSource = kendo.data.DataSource.create(dataSource);\n            },\n            _assignments: function () {\n                var assignments = this.options.assignments;\n                var dataSource = assignments.dataSource || {};\n                if (this.assignments) {\n                    this.assignments.dataSource.unbind('change', this._assignmentsRefreshHandler);\n                } else {\n                    this._assignmentsRefreshHandler = proxy(this.refresh, this);\n                }\n                this.assignments = {\n                    dataTaskIdField: 'taskId',\n                    dataResourceIdField: 'resourceId',\n                    dataValueField: 'value'\n                };\n                extend(this.assignments, assignments);\n                this.assignments.dataSource = kendo.data.DataSource.create(dataSource);\n                this.assignments.dataSource.bind('change', this._assignmentsRefreshHandler);\n            },\n            _createEditor: function () {\n                var that = this;\n                var editor = this._editor = new PopupEditor(this.wrapper, extend({}, this.options, {\n                    target: this,\n                    resources: {\n                        field: this.resources.field,\n                        editor: proxy(this._createResourceEditor, this)\n                    },\n                    createButton: proxy(this._createPopupButton, this)\n                }));\n                editor.bind('cancel', function (e) {\n                    var task = that.dataSource.getByUid(e.model.uid);\n                    if (that.trigger('cancel', {\n                            container: e.container,\n                            task: task\n                        })) {\n                        e.preventDefault();\n                        return;\n                    }\n                    that.cancelTask();\n                }).bind('edit', function (e) {\n                    var task = that.dataSource.getByUid(e.model.uid);\n                    if (that.trigger('edit', {\n                            container: e.container,\n                            task: task\n                        })) {\n                        e.preventDefault();\n                    }\n                }).bind('save', function (e) {\n                    var task = that.dataSource.getByUid(e.model.uid);\n                    that.saveTask(task, e.updateInfo);\n                }).bind('remove', function (e) {\n                    that.removeTask(e.model.uid);\n                }).bind('close', that._onDialogClose);\n            },\n            _onDialogClose: function () {\n            },\n            _createResourceEditor: function (container, options) {\n                var that = this;\n                var model = options instanceof ObservableObject ? options : options.model;\n                var id = model.get('id');\n                var messages = this.options.messages;\n                var resourcesField = this.resources.field;\n                var unitsValidation = { step: 0.01 };\n                var assignmentsModel = this.assignments.dataSource.options.schema.model;\n                if (assignmentsModel && assignmentsModel.fields.Units && assignmentsModel.fields.Units.validation) {\n                    extend(true, unitsValidation, assignmentsModel.fields.Units.validation);\n                }\n                var editor = this._resourceEditor = new ResourceEditor(container, {\n                    resourcesField: resourcesField,\n                    unitsValidation: unitsValidation,\n                    data: this._wrapResourceData(id),\n                    model: model,\n                    messages: extend({}, messages.editor),\n                    buttons: [\n                        {\n                            name: 'update',\n                            text: messages.save,\n                            className: Gantt.styles.primary\n                        },\n                        {\n                            name: 'cancel',\n                            text: messages.cancel\n                        }\n                    ],\n                    createButton: proxy(this._createPopupButton, this),\n                    save: function (e) {\n                        that._updateAssignments(e.model.get('id'), e.model.get(resourcesField));\n                    }\n                });\n                editor.open();\n            },\n            _createPopupButton: function (command) {\n                var commandName = command.name || command.text;\n                var options = {\n                    className: Gantt.styles.popup.button + ' k-gantt-' + (commandName || '').replace(/\\s/g, ''),\n                    text: commandName,\n                    attr: ''\n                };\n                if (!commandName && !(isPlainObject(command) && command.template)) {\n                    throw new Error('Custom commands should have name specified');\n                }\n                if (isPlainObject(command)) {\n                    if (command.className) {\n                        command.className += ' ' + options.className;\n                    }\n                    options = extend(true, options, command);\n                }\n                return kendo.template(COMMAND_BUTTON_TEMPLATE)(options);\n            },\n            view: function (type) {\n                return this.timeline.view(type);\n            },\n            range: function (range) {\n                var dataSource = this.dataSource;\n                var view = this.view();\n                var timeline = this.timeline;\n                if (range) {\n                    view.options.range = {\n                        start: range.start,\n                        end: range.end\n                    };\n                    timeline._render(dataSource.taskTree());\n                    timeline._renderDependencies(this.dependencies.view());\n                }\n                return {\n                    start: view.start,\n                    end: view.end\n                };\n            },\n            date: function (date) {\n                var view = this.view();\n                if (date) {\n                    view.options.date = date;\n                    view._scrollToDate(date);\n                }\n                return view.options.date;\n            },\n            dataItem: function (value) {\n                if (!value) {\n                    return null;\n                }\n                var list = this.list;\n                var element = list.content.find(value);\n                return list._modelFromElement(element);\n            },\n            setDataSource: function (dataSource) {\n                this.options.dataSource = dataSource;\n                this._dataSource();\n                this.list._setDataSource(this.dataSource);\n                if (this.options.autoBind) {\n                    dataSource.fetch();\n                }\n            },\n            setDependenciesDataSource: function (dependencies) {\n                this.options.dependencies = dependencies;\n                this._dependencies();\n                if (this.options.autoBind) {\n                    dependencies.fetch();\n                }\n            },\n            items: function () {\n                return this.wrapper.children('.k-task');\n            },\n            _updateAssignments: function (id, resources) {\n                var dataSource = this.assignments.dataSource;\n                var taskId = this.assignments.dataTaskIdField;\n                var resourceId = this.assignments.dataResourceIdField;\n                var hasMatch = false;\n                var assignments = new Query(dataSource.view()).filter({\n                    field: taskId,\n                    operator: 'eq',\n                    value: id\n                }).toArray();\n                var assignment;\n                var resource;\n                var value;\n                while (assignments.length) {\n                    assignment = assignments[0];\n                    for (var i = 0, length = resources.length; i < length; i++) {\n                        resource = resources[i];\n                        if (assignment.get(resourceId) === resource.get('id')) {\n                            value = resources[i].get('value');\n                            this._updateAssignment(assignment, value);\n                            resources.splice(i, 1);\n                            hasMatch = true;\n                            break;\n                        }\n                    }\n                    if (!hasMatch) {\n                        this._removeAssignment(assignment);\n                    }\n                    hasMatch = false;\n                    assignments.shift();\n                }\n                for (var j = 0, newLength = resources.length; j < newLength; j++) {\n                    resource = resources[j];\n                    this._createAssignment(resource, id);\n                }\n                dataSource.sync();\n            },\n            cancelTask: function () {\n                var editor = this._editor;\n                var container = editor.container;\n                if (container) {\n                    editor.close();\n                }\n            },\n            editTask: function (uid) {\n                var task = typeof uid === 'string' ? this.dataSource.getByUid(uid) : uid;\n                if (!task) {\n                    return;\n                }\n                var taskCopy = this.dataSource._createNewModel(task.toJSON());\n                taskCopy.uid = task.uid;\n                this.cancelTask();\n                this._editTask(taskCopy);\n            },\n            _editTask: function (task) {\n                this._editor.editTask(task);\n            },\n            saveTask: function (task, updateInfo) {\n                var editor = this._editor;\n                var container = editor.container;\n                var editable = editor.editable;\n                if (container && editable && editable.end()) {\n                    this._updateTask(task, updateInfo);\n                }\n            },\n            _updateTask: function (task, updateInfo) {\n                var resourcesField = this.resources.field;\n                if (!this.trigger('save', {\n                        task: task,\n                        values: updateInfo\n                    })) {\n                    this._preventRefresh = true;\n                    this.dataSource.update(task, updateInfo);\n                    if (updateInfo[resourcesField]) {\n                        this._updateAssignments(task.get('id'), updateInfo[resourcesField]);\n                    }\n                    this._syncDataSource();\n                }\n            },\n            _updateAssignment: function (assignment, value) {\n                var resourceValueField = this.assignments.dataValueField;\n                assignment.set(resourceValueField, value);\n            },\n            removeTask: function (uid) {\n                var that = this;\n                var task = typeof uid === 'string' ? this.dataSource.getByUid(uid) : uid;\n                if (!task) {\n                    return;\n                }\n                this._taskConfirm(function (cancel) {\n                    if (!cancel) {\n                        that._removeTask(task);\n                    }\n                }, task);\n            },\n            _createTask: function (task, index) {\n                if (!this.trigger('add', {\n                        task: task,\n                        dependency: null\n                    })) {\n                    var dataSource = this.dataSource;\n                    this._preventRefresh = true;\n                    if (index === undefined) {\n                        dataSource.add(task);\n                    } else {\n                        dataSource.insert(index, task);\n                    }\n                    this._scrollToUid = task.uid;\n                    this._syncDataSource();\n                }\n            },\n            _createDependency: function (dependency) {\n                if (!this.trigger('add', {\n                        task: null,\n                        dependency: dependency\n                    })) {\n                    this._preventDependencyRefresh = true;\n                    this.dependencies.add(dependency);\n                    this._preventDependencyRefresh = false;\n                    this.dependencies.sync();\n                }\n            },\n            _createAssignment: function (resource, id) {\n                var assignments = this.assignments;\n                var dataSource = assignments.dataSource;\n                var taskId = assignments.dataTaskIdField;\n                var resourceId = assignments.dataResourceIdField;\n                var resourceValue = assignments.dataValueField;\n                var assignment = dataSource._createNewModel();\n                assignment[taskId] = id;\n                assignment[resourceId] = resource.get('id');\n                assignment[resourceValue] = resource.get('value');\n                dataSource.add(assignment);\n            },\n            removeDependency: function (uid) {\n                var that = this;\n                var dependency = typeof uid === 'string' ? this.dependencies.getByUid(uid) : uid;\n                if (!dependency) {\n                    return;\n                }\n                this._dependencyConfirm(function (cancel) {\n                    if (!cancel) {\n                        that._removeDependency(dependency);\n                    }\n                }, dependency);\n            },\n            _removeTaskDependencies: function (task, dependencies) {\n                this._preventDependencyRefresh = true;\n                for (var i = 0, length = dependencies.length; i < length; i++) {\n                    this.dependencies.remove(dependencies[i]);\n                }\n                this._preventDependencyRefresh = false;\n                this.dependencies.sync();\n            },\n            _removeTaskAssignments: function (task) {\n                var dataSource = this.assignments.dataSource;\n                var assignments = dataSource.view();\n                var filter = {\n                    field: this.assignments.dataTaskIdField,\n                    operator: 'eq',\n                    value: task.get('id')\n                };\n                assignments = new Query(assignments).filter(filter).toArray();\n                this._preventRefresh = true;\n                for (var i = 0, length = assignments.length; i < length; i++) {\n                    dataSource.remove(assignments[i]);\n                }\n                this._preventRefresh = false;\n                dataSource.sync();\n            },\n            _removeTask: function (task) {\n                var dependencies = this.dependencies.dependencies(task.id);\n                if (!this.trigger('remove', {\n                        task: task,\n                        dependencies: dependencies\n                    })) {\n                    this._removeTaskDependencies(task, dependencies);\n                    this._removeTaskAssignments(task);\n                    this._preventRefresh = true;\n                    if (this.dataSource.remove(task)) {\n                        this._syncDataSource();\n                    }\n                    this._preventRefresh = false;\n                }\n            },\n            _removeDependency: function (dependency) {\n                if (!this.trigger('remove', {\n                        task: null,\n                        dependencies: [dependency]\n                    })) {\n                    if (this.dependencies.remove(dependency)) {\n                        this.dependencies.sync();\n                    }\n                }\n            },\n            _removeAssignment: function (assignment) {\n                this.assignments.dataSource.remove(assignment);\n            },\n            _taskConfirm: function (callback, task) {\n                var messages = this.options.messages;\n                this._confirm(callback, {\n                    model: task,\n                    text: messages.deleteTaskConfirmation,\n                    title: messages.deleteTaskWindowTitle\n                });\n            },\n            _dependencyConfirm: function (callback, dependency) {\n                var messages = this.options.messages;\n                this._confirm(callback, {\n                    model: dependency,\n                    text: messages.deleteDependencyConfirmation,\n                    title: messages.deleteDependencyWindowTitle\n                });\n            },\n            _confirm: function (callback, options) {\n                var editable = this.options.editable;\n                var messages;\n                var buttons;\n                if (editable === true || editable.confirmation !== false) {\n                    messages = this.options.messages;\n                    buttons = [\n                        {\n                            name: 'delete',\n                            text: messages.destroy,\n                            className: Gantt.styles.primary,\n                            click: function () {\n                                callback();\n                            }\n                        },\n                        {\n                            name: 'cancel',\n                            text: messages.cancel,\n                            click: function () {\n                                callback(true);\n                            }\n                        }\n                    ];\n                    this.showDialog(extend(true, {}, options, { buttons: buttons }));\n                } else {\n                    callback();\n                }\n            },\n            showDialog: function (options) {\n                this._editor.showDialog(options);\n            },\n            refresh: function () {\n                if (this._preventRefresh || this.list.editable) {\n                    return;\n                }\n                this._progress(false);\n                var dataSource = this.dataSource;\n                var taskTree = dataSource.taskTree();\n                var scrollToUid = this._scrollToUid;\n                var current;\n                var cachedUid;\n                var cachedIndex = -1;\n                if (this.current) {\n                    cachedUid = this.current.closest('tr').attr(kendo.attr('uid'));\n                    cachedIndex = this.current.index();\n                }\n                if (this.trigger('dataBinding')) {\n                    return;\n                }\n                if (this.resources.dataSource.data().length !== 0) {\n                    this._assignResources(taskTree);\n                }\n                if (this._editor) {\n                    this._editor.close();\n                }\n                this.clearSelection();\n                this.list._render(taskTree);\n                this.timeline._render(taskTree);\n                this.timeline._renderDependencies(this.dependencies.view());\n                if (scrollToUid) {\n                    this._scrollTo(scrollToUid);\n                    this.select(selector(scrollToUid));\n                }\n                if ((scrollToUid || cachedUid) && cachedIndex >= 0) {\n                    current = this.list.content.find('tr' + selector(scrollToUid || cachedUid) + ' > td:eq(' + cachedIndex + ')');\n                    this._current(current);\n                }\n                this._scrollToUid = null;\n                this.trigger('dataBound');\n            },\n            refreshDependencies: function () {\n                if (this._preventDependencyRefresh) {\n                    return;\n                }\n                if (this.trigger('dataBinding')) {\n                    return;\n                }\n                this.timeline._renderDependencies(this.dependencies.view());\n                this.trigger('dataBound');\n            },\n            _assignResources: function (taskTree) {\n                var resources = this.resources;\n                var assignments = this.assignments;\n                var groupAssigments = function () {\n                    var data = assignments.dataSource.view();\n                    var group = { field: assignments.dataTaskIdField };\n                    data = new Query(data).group(group).toArray();\n                    return data;\n                };\n                var assigments = groupAssigments();\n                var applyTaskResource = function (task, action) {\n                    var taskId = task.get('id');\n                    kendo.setter(resources.field)(task, new ObservableArray([]));\n                    for (var i = 0, length = assigments.length; i < length; i++) {\n                        if (assigments[i].value === taskId) {\n                            action(task, assigments[i].items);\n                        }\n                    }\n                };\n                var wrapTask = function (task, items) {\n                    for (var j = 0, length = items.length; j < length; j++) {\n                        var item = items[j];\n                        var resource = resources.dataSource.get(item.get(assignments.dataResourceIdField));\n                        var resourceValue = item.get(assignments.dataValueField);\n                        var resourcedId = item.get(assignments.dataResourceIdField);\n                        var valueFormat = resource.get(resources.dataFormatField) || PERCENTAGE_FORMAT;\n                        var formatedValue = kendo.toString(resourceValue, valueFormat);\n                        task[resources.field].push(new ObservableObject({\n                            id: resourcedId,\n                            name: resource.get(resources.dataTextField),\n                            color: resource.get(resources.dataColorField),\n                            value: resourceValue,\n                            formatedValue: formatedValue\n                        }));\n                    }\n                };\n                for (var i = 0, length = taskTree.length; i < length; i++) {\n                    applyTaskResource(taskTree[i], wrapTask);\n                }\n            },\n            _wrapResourceData: function (id) {\n                var that = this;\n                var result = [];\n                var resource;\n                var resources = this.resources.dataSource.view();\n                var assignments = this.assignments.dataSource.view();\n                var taskAssignments = new Query(assignments).filter({\n                    field: that.assignments.dataTaskIdField,\n                    operator: 'eq',\n                    value: id\n                }).toArray();\n                var valuePerResource = function (id) {\n                    var resourceValue = null;\n                    new Query(taskAssignments).filter({\n                        field: that.assignments.dataResourceIdField,\n                        operator: 'eq',\n                        value: id\n                    }).select(function (assignment) {\n                        resourceValue += assignment.get(that.assignments.dataValueField);\n                    });\n                    return resourceValue;\n                };\n                for (var i = 0, length = resources.length; i < length; i++) {\n                    resource = resources[i];\n                    result.push({\n                        id: resource.get('id'),\n                        name: resource.get(that.resources.dataTextField),\n                        format: resource.get(that.resources.dataFormatField) || PERCENTAGE_FORMAT,\n                        value: valuePerResource(resource.id)\n                    });\n                }\n                return result;\n            },\n            _syncDataSource: function () {\n                this._preventRefresh = false;\n                this._requestStart();\n                this.dataSource.sync();\n            },\n            _requestStart: function () {\n                this._progress(true);\n            },\n            _error: function () {\n                this._progress(false);\n            },\n            _progress: function (toggle) {\n                kendo.ui.progress(this.element, toggle);\n            },\n            _resizable: function () {\n                var that = this;\n                var wrapper = this.wrapper;\n                var ganttStyles = Gantt.styles;\n                var contentSelector = DOT + ganttStyles.gridContent;\n                var treeListWrapper = wrapper.find(DOT + ganttStyles.list);\n                var timelineWrapper = wrapper.find(DOT + ganttStyles.timeline);\n                var treeListWidth;\n                var timelineWidth;\n                var timelineScroll;\n                this._resizeDraggable = wrapper.find(DOT + ganttStyles.splitBar).height(treeListWrapper.height()).hover(function () {\n                    $(this).addClass(ganttStyles.splitBarHover);\n                }, function () {\n                    $(this).removeClass(ganttStyles.splitBarHover);\n                }).end().kendoResizable({\n                    orientation: 'horizontal',\n                    handle: DOT + ganttStyles.splitBar,\n                    'start': function () {\n                        treeListWidth = treeListWrapper.width();\n                        timelineWidth = timelineWrapper.width();\n                        timelineScroll = timelineWrapper.find(contentSelector).scrollLeft();\n                    },\n                    'resize': function (e) {\n                        var delta = e.x.initialDelta;\n                        if (kendo.support.isRtl(wrapper)) {\n                            delta *= -1;\n                        }\n                        if (treeListWidth + delta < 0 || timelineWidth - delta < 0) {\n                            return;\n                        }\n                        treeListWrapper.width(treeListWidth + delta);\n                        timelineWrapper.width(timelineWidth - delta);\n                        timelineWrapper.find(contentSelector).scrollLeft(timelineScroll + delta);\n                        that.timeline.view()._renderCurrentTime();\n                    }\n                }).data('kendoResizable');\n            },\n            _scrollable: function () {\n                var that = this;\n                var ganttStyles = Gantt.styles;\n                var contentSelector = DOT + ganttStyles.gridContent;\n                var headerSelector = DOT + ganttStyles.gridHeaderWrap;\n                var timelineHeader = this.timeline.element.find(headerSelector);\n                var timelineContent = this.timeline.element.find(contentSelector);\n                var treeListHeader = this.list.element.find(headerSelector);\n                var treeListContent = this.list.element.find(contentSelector);\n                if (mobileOS) {\n                    treeListContent.css('overflow-y', 'auto');\n                }\n                timelineContent.on('scroll', function () {\n                    that.scrollTop = this.scrollTop;\n                    timelineHeader.scrollLeft(this.scrollLeft);\n                    treeListContent.scrollTop(this.scrollTop);\n                });\n                treeListContent.on('scroll', function () {\n                    treeListHeader.scrollLeft(this.scrollLeft);\n                }).on('DOMMouseScroll' + NS + ' mousewheel' + NS, function (e) {\n                    var scrollTop = timelineContent.scrollTop();\n                    var delta = kendo.wheelDeltaY(e);\n                    if (delta) {\n                        e.preventDefault();\n                        $(e.currentTarget).one('wheel' + NS, false);\n                        timelineContent.scrollTop(scrollTop + -delta);\n                    }\n                });\n            },\n            _navigatable: function () {\n                var that = this;\n                var navigatable = this.options.navigatable;\n                var editable = this.options.editable;\n                var headerTable = this.list.header.find('table');\n                var contentTable = this.list.content.find('table');\n                var ganttStyles = Gantt.styles;\n                var isRtl = kendo.support.isRtl(this.wrapper);\n                var timelineContent = this.timeline.element.find(DOT + ganttStyles.gridContent);\n                var tables = headerTable.add(contentTable);\n                var attr = selector();\n                var cellIndex;\n                var expandState = {\n                    collapse: false,\n                    expand: true\n                };\n                var scroll = function (reverse) {\n                    var width = that.timeline.view()._timeSlots()[0].offsetWidth;\n                    timelineContent.scrollLeft(timelineContent.scrollLeft() + (reverse ? -width : width));\n                };\n                var scrollVertical = function (reverse) {\n                    var height = that.timeline.view()._rowHeight;\n                    timelineContent.scrollTop(timelineContent.scrollTop() + (reverse ? -height : height));\n                };\n                var moveVertical = function (method) {\n                    var parent = that.current.parent('tr' + selector());\n                    var index = that.current.index();\n                    var subling = parent[method]();\n                    if (that.select().length !== 0) {\n                        that.clearSelection();\n                    }\n                    if (subling.length !== 0) {\n                        that._current(subling.children('td:eq(' + index + ')'));\n                        that._scrollTo(that.current);\n                    } else {\n                        if (that.current.is('td') && method == 'prev') {\n                            focusTable(headerTable);\n                        } else if (that.current.is('th') && method == 'next') {\n                            focusTable(contentTable);\n                        }\n                    }\n                };\n                var moveHorizontal = function (method) {\n                    var subling = that.current[method]();\n                    if (subling.length !== 0) {\n                        that._current(subling);\n                        cellIndex = that.current.index();\n                    }\n                };\n                var toggleExpandedState = function (value) {\n                    var model = that.dataItem(that.current);\n                    if (model.summary && model.expanded !== value) {\n                        model.set('expanded', value);\n                    }\n                };\n                var deleteAction = function () {\n                    var editable = that.options.editable;\n                    if (!editable || editable.destroy === false || that.list.editable) {\n                        return;\n                    }\n                    var selectedTask = that.select();\n                    var uid = kendo.attr('uid');\n                    if (selectedTask.length) {\n                        that.removeTask(selectedTask.attr(uid));\n                    }\n                };\n                $(this.wrapper).on('mousedown' + NS, 'tr' + attr + ', div' + attr + ':not(' + DOT + ganttStyles.line + ')', function (e) {\n                    var currentTarget = $(e.currentTarget);\n                    var isInput = $(e.target).is(':button,a,:input,a>.k-icon,textarea,span.k-icon,span.k-link,.k-input,.k-multiselect-wrap');\n                    var current;\n                    if (e.ctrlKey) {\n                        return;\n                    }\n                    if (navigatable) {\n                        if (currentTarget.is('tr')) {\n                            current = $(e.target).closest('td');\n                        } else {\n                            current = that.list.content.find('tr' + selector(currentTarget.attr(kendo.attr('uid'))) + ' > td:first');\n                        }\n                        that._current(current);\n                    }\n                    if ((navigatable || editable) && !isInput) {\n                        that._focusTimeout = setTimeout(function () {\n                            focusTable(that.list.content.find('table'), true);\n                        }, 2);\n                    }\n                }).on('keydown' + NS, function (e) {\n                    var key = e.keyCode;\n                    var that = this;\n                    var focusableItems = $(that._getToolbarItems());\n                    var idx = focusableItems.index(that.toolbar.find(DOT + ganttStyles.focused)[0]);\n                    if (idx === -1 && $(e.target).closest(DOT + ganttStyles.toolbar.views).length) {\n                        idx = focusableItems.index(that.toolbar.find('.k-gantt-views > .k-state-selected:visible > a, .k-current-view:visible > a')[0]);\n                    }\n                    var itemToFocus = e.shiftKey ? focusableItems[idx - 1] : focusableItems[idx + 1];\n                    if (key === keys.F10) {\n                        that.toolbar.find('.k-button:visible:first').addClass(ganttStyles.focused).focus();\n                        e.preventDefault();\n                    } else if (key == keys.TAB && $(e.target).closest(DOT + ganttStyles.toolbar.toolbar).length) {\n                        that.toolbar.find(DOT + ganttStyles.focused).removeClass(ganttStyles.focused).blur();\n                        if (itemToFocus) {\n                            $(itemToFocus).addClass(ganttStyles.focused).focus();\n                            e.preventDefault();\n                            return;\n                        }\n                        if (this.list.element.is(':visible')) {\n                            this.list.element.find('table[role=treegrid]').focus();\n                        } else {\n                            this.element.find(DOT + ganttStyles.tasks)[0].focus();\n                        }\n                        e.preventDefault();\n                    }\n                }.bind(this));\n                if (navigatable !== true) {\n                    contentTable.on('keydown' + NS, function (e) {\n                        if (e.keyCode == keys.DELETE) {\n                            deleteAction();\n                        }\n                    });\n                    return;\n                }\n                tables.on('focus' + NS, function () {\n                    var selector = this === contentTable.get(0) ? 'td' : 'th';\n                    var selection = that.select();\n                    var current = that.current || $(selection.length ? selection : this).find(selector + ':eq(' + (cellIndex || 0) + ')');\n                    that._current(current);\n                    $(that.toolbar.find(DOT + ganttStyles.focused)).removeClass(ganttStyles.focused);\n                    $(that.toolbar.find(DOT + ganttStyles.toolbar.currentView)).parent().removeClass(ganttStyles.toolbar.expanded);\n                }).on('blur' + NS, function () {\n                    that._current();\n                    if (this == headerTable) {\n                        $(this).attr(TABINDEX, -1);\n                    }\n                }).on('keydown' + NS, function (e) {\n                    var key = e.keyCode;\n                    var isCell;\n                    if (!that.current) {\n                        return;\n                    }\n                    isCell = that.current.is('td');\n                    switch (key) {\n                    case keys.RIGHT:\n                        e.preventDefault();\n                        if (e.altKey) {\n                            scroll();\n                        } else if (e.ctrlKey) {\n                            toggleExpandedState(isRtl ? expandState.collapse : expandState.expand);\n                        } else {\n                            moveHorizontal(isRtl ? 'prev' : 'next');\n                        }\n                        break;\n                    case keys.LEFT:\n                        e.preventDefault();\n                        if (e.altKey) {\n                            scroll(true);\n                        } else if (e.ctrlKey) {\n                            toggleExpandedState(isRtl ? expandState.expand : expandState.collapse);\n                        } else {\n                            moveHorizontal(isRtl ? 'next' : 'prev');\n                        }\n                        break;\n                    case keys.UP:\n                        e.preventDefault();\n                        if (e.altKey) {\n                            scrollVertical(true);\n                        } else {\n                            moveVertical('prev');\n                        }\n                        break;\n                    case keys.DOWN:\n                        e.preventDefault();\n                        if (e.altKey) {\n                            scrollVertical();\n                        } else {\n                            moveVertical('next');\n                        }\n                        break;\n                    case keys.SPACEBAR:\n                        e.preventDefault();\n                        if (isCell) {\n                            that.select(that.current.closest('tr'));\n                        }\n                        break;\n                    case keys.ENTER:\n                        e.preventDefault();\n                        if (isCell) {\n                            if (that.options.editable && that.options.editable.update !== false) {\n                                that._cachedCurrent = that.current;\n                                that.list._startEditHandler(that.current);\n                                $(this).one('keyup', function (e) {\n                                    e.stopPropagation();\n                                });\n                            }\n                        } else {\n                            that.current.children('a.k-link').click();\n                        }\n                        break;\n                    case keys.ESC:\n                        break;\n                    case keys.DELETE:\n                        if (isCell) {\n                            deleteAction();\n                        }\n                        break;\n                    default:\n                        if (key >= 49 && key <= 57 && e.target.tagName.toLowerCase() !== 'input') {\n                            that.view(that.timeline._viewByIndex(key - 49));\n                        }\n                        break;\n                    }\n                });\n            },\n            _getToolbarItems: function () {\n                return this.toolbar.find('.k-gantt-toggle:visible').toArray().concat(this.toolbar.find('.k-gantt-actions > .k-button:visible').toArray(), this.toolbar.find('.k-gantt-views > .k-state-selected:visible > a, .k-current-view:visible > a').toArray());\n            },\n            _current: function (element) {\n                var ganttStyles = Gantt.styles;\n                var activeElement;\n                if (this.current && this.current.length) {\n                    this.current.removeClass(ganttStyles.focused).removeAttr('id');\n                }\n                if (element && element.length) {\n                    this.current = element.addClass(ganttStyles.focused).attr('id', ACTIVE_CELL);\n                    activeElement = $(kendo._activeElement());\n                    if (activeElement.is('table') && this.wrapper.find(activeElement).length > 0) {\n                        activeElement.removeAttr(ARIA_DESCENDANT).attr(ARIA_DESCENDANT, ACTIVE_CELL);\n                    }\n                } else {\n                    this.current = null;\n                }\n            },\n            _dataBind: function () {\n                var that = this;\n                if (that.options.autoBind) {\n                    this._preventRefresh = true;\n                    this._preventDependencyRefresh = true;\n                    var promises = $.map([\n                        this.dataSource,\n                        this.dependencies,\n                        this.resources.dataSource,\n                        this.assignments.dataSource\n                    ], function (dataSource) {\n                        return dataSource.fetch();\n                    });\n                    $.when.apply(null, promises).done(function () {\n                        that._preventRefresh = false;\n                        that._preventDependencyRefresh = false;\n                        that.refresh();\n                    });\n                }\n            },\n            _resize: function () {\n                this._adjustDimensions();\n                this.timeline.view()._adjustHeight();\n                this.timeline.view()._renderCurrentTime();\n                this.list._adjustHeight();\n            }\n        });\n        if (kendo.PDFMixin) {\n            kendo.PDFMixin.extend(Gantt.fn);\n            Gantt.fn._drawPDF = function () {\n                var ganttStyles = Gantt.styles;\n                var listClass = '.' + ganttStyles.list;\n                var listWidth = this.wrapper.find(listClass).width();\n                var content = this.wrapper.clone();\n                content.find(listClass).css('width', listWidth);\n                return this._drawPDFShadow({ content: content }, { avoidLinks: this.options.pdf.avoidLinks });\n            };\n        }\n        kendo.ui.plugin(Gantt);\n        extend(true, Gantt, { styles: ganttStyles });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}