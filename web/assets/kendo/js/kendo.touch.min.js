/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.touch.min",["kendo.core.min","kendo.userevents.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.ui.Widget,a=e.proxy,o=Math.abs,r=20,s=i.extend({init:function(e,t){function o(e){return function(t){s._triggerTouch(e,t)}}function r(e){return function(t){s.trigger(e,{touches:t.touches,distance:t.distance,center:t.center,event:t.event})}}var s=this;i.fn.init.call(s,e,t),t=s.options,e=s.element,s.wrapper=e,s.events=new n.UserEvents(e,{filter:t.filter,surface:t.surface,minHold:t.minHold,multiTouch:t.multiTouch,allowSelection:!0,fastTap:t.fastTap,press:o("touchstart"),hold:o("hold"),tap:a(s,"_tap"),gesturestart:r("gesturestart"),gesturechange:r("gesturechange"),gestureend:r("gestureend")}),t.enableSwipe?(s.events.bind("start",a(s,"_swipestart")),s.events.bind("move",a(s,"_swipemove"))):(s.events.bind("start",a(s,"_dragstart")),s.events.bind("move",o("drag")),s.events.bind("end",o("dragend"))),n.notify(s)},events:["touchstart","dragstart","drag","dragend","tap","doubletap","hold","swipe","gesturestart","gesturechange","gestureend"],options:{name:"Touch",surface:null,global:!1,fastTap:!1,filter:null,multiTouch:!1,enableSwipe:!1,minXDelta:30,maxYDelta:20,maxDuration:1e3,minHold:800,doubleTapTimeout:800},cancel:function(){this.events.cancel()},destroy:function(){i.fn.destroy.call(this),this.events.destroy()},_triggerTouch:function(e,t){this.trigger(e,{touch:t.touch,event:t.event})&&t.preventDefault()},_tap:function(e){var t=this,i=t.lastTap,a=e.touch;i&&a.endTime-i.endTime<t.options.doubleTapTimeout&&n.touchDelta(a,i).distance<r?(t._triggerTouch("doubletap",e),t.lastTap=null):(t._triggerTouch("tap",e),t.lastTap=a)},_dragstart:function(e){this._triggerTouch("dragstart",e)},_swipestart:function(e){2*o(e.x.velocity)>=o(e.y.velocity)&&e.sender.capture()},_swipemove:function(e){var t=this,n=t.options,i=e.touch,a=e.event.timeStamp-i.startTime,r=i.x.initialDelta>0?"right":"left";o(i.x.initialDelta)>=n.minXDelta&&o(i.y.initialDelta)<n.maxYDelta&&a<n.maxDuration&&(t.trigger("swipe",{direction:r,touch:e.touch}),i.cancel())}});n.ui.plugin(s)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.touch.min.js.map
