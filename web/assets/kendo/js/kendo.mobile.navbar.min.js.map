{"version": 3, "sources": ["kendo.mobile.navbar.js"], "names": ["f", "define", "$", "undefined", "createContainer", "align", "element", "items", "find", "kendo", "attr", "append", "prependTo", "toggleTitle", "centerElement", "siblings", "noTitle", "children", "showTitle", "trim", "text", "android", "mobile", "application", "is", "prevAll", "toggleClass", "html", "window", "ui", "Widget", "NavBar", "extend", "init", "options", "that", "this", "fn", "call", "container", "bind", "proxy", "addClass", "wrapInner", "leftElement", "rightElement", "name", "title", "value", "roleSelector", "refresh", "e", "view", "destroy", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,uBAAwB,cAAeD,IAChD,WAmDE,MA3CC,UAAUE,EAAGC,GAEV,QAASC,GAAgBC,EAAOC,GAC5B,GAAIC,GAAQD,EAAQE,KAAK,IAAMC,EAAMC,KAAK,SAAW,IAAML,EAAQ,IACnE,IAAIE,EAAM,GACN,MAAOL,GAAE,kBAAoBG,EAAQ,YAAYM,OAAOJ,GAAOK,UAAUN,GAGjF,QAASO,GAAYC,GACjB,GAAIC,GAAWD,EAAcC,WAAYC,IAAYF,EAAcG,SAAS,MAAM,GAAIC,IAAcH,EAAS,IAAuC,KAAjCb,EAAEiB,KAAKL,EAAcM,QAAgBC,KAAaZ,EAAMa,OAAOC,cAAed,EAAMa,OAAOC,YAAYjB,QAAQkB,GAAG,eACrOV,GAAcW,UAAUC,YAAY,cAAeV,GACnDF,EAAcY,YAAY,gBAAiBR,GAC3CJ,EAAcY,YAAY,gBAAiBR,IAAchB,EAAEiB,KAAKL,EAAca,SAC9Eb,EAAcY,YAAY,cAAeV,GACzCF,EAAcY,YAAY,gBAAiBL,IAAYN,EAASE,WAAWO,GAAG,aAdrF,GACOf,GAAQmB,OAAOnB,MAAOa,EAASb,EAAMa,OAAQO,EAAKP,EAAOO,GAAIC,EAASD,EAAGC,OAezEC,EAASD,EAAOE,QAChBC,KAAM,SAAU3B,EAAS4B,GACrB,GAAIC,GAAOC,IACXN,GAAOO,GAAGJ,KAAKK,KAAKH,EAAM7B,EAAS4B,GACnC5B,EAAU6B,EAAK7B,QACf6B,EAAKI,YAAYC,KAAK,OAAQtC,EAAEuC,MAAML,KAAM,YAC5C9B,EAAQoC,SAAS,aAAaC,UAAUzC,EAAE,gDAC1CiC,EAAKS,YAAcxC,EAAgB,OAAQE,GAC3C6B,EAAKU,aAAezC,EAAgB,QAASE,GAC7C6B,EAAKrB,cAAgBR,EAAQE,KAAK,mBAEtC0B,SAAWY,KAAM,UACjBC,MAAO,SAAUC,GACbZ,KAAK9B,QAAQE,KAAKC,EAAMwC,aAAa,eAAe7B,KAAK4B,GACzDnC,EAAYuB,KAAKtB,gBAErBoC,QAAS,SAAUC,GACf,GAAIC,GAAOD,EAAEC,IACbhB,MAAKW,MAAMK,EAAKlB,QAAQa,QAE5BM,QAAS,WACLvB,EAAOO,GAAGgB,QAAQf,KAAKF,MACvB3B,EAAM4C,QAAQjB,KAAK9B,WAG3BuB,GAAGyB,OAAOvB,IACZH,OAAOnB,MAAM8C,QACR3B,OAAOnB,OACE,kBAAVR,SAAwBA,OAAOuD,IAAMvD,OAAS,SAAUwD,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.navbar.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.navbar', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.navbar',\n        name: 'NavBar',\n        category: 'mobile',\n        description: 'The Kendo mobile NavBar widget is used inside a mobile View or Layout Header element to display an application navigation bar.',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, mobile = kendo.mobile, ui = mobile.ui, Widget = ui.Widget;\n        function createContainer(align, element) {\n            var items = element.find('[' + kendo.attr('align') + '=' + align + ']');\n            if (items[0]) {\n                return $('<div class=\"km-' + align + 'item\" />').append(items).prependTo(element);\n            }\n        }\n        function toggleTitle(centerElement) {\n            var siblings = centerElement.siblings(), noTitle = !!centerElement.children('ul')[0], showTitle = !!siblings[0] && $.trim(centerElement.text()) === '', android = !!(kendo.mobile.application && kendo.mobile.application.element.is('.km-android'));\n            centerElement.prevAll().toggleClass('km-absolute', noTitle);\n            centerElement.toggleClass('km-show-title', showTitle);\n            centerElement.toggleClass('km-fill-title', showTitle && !$.trim(centerElement.html()));\n            centerElement.toggleClass('km-no-title', noTitle);\n            centerElement.toggleClass('km-hide-title', android && !siblings.children().is(':visible'));\n        }\n        var NavBar = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                element = that.element;\n                that.container().bind('show', $.proxy(this, 'refresh'));\n                element.addClass('km-navbar').wrapInner($('<div class=\"km-view-title km-show-title\" />'));\n                that.leftElement = createContainer('left', element);\n                that.rightElement = createContainer('right', element);\n                that.centerElement = element.find('.km-view-title');\n            },\n            options: { name: 'NavBar' },\n            title: function (value) {\n                this.element.find(kendo.roleSelector('view-title')).text(value);\n                toggleTitle(this.centerElement);\n            },\n            refresh: function (e) {\n                var view = e.view;\n                this.title(view.options.title);\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                kendo.destroy(this.element);\n            }\n        });\n        ui.plugin(NavBar);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}