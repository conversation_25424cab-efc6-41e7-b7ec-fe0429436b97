{"version": 3, "sources": ["kendo.mobile.modalview.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "ui", "mobile", "<PERSON><PERSON>", "Widget", "BEFORE_OPEN", "OPEN", "CLOSE", "INIT", "WRAP", "<PERSON><PERSON><PERSON>iew", "View", "extend", "init", "element", "options", "that", "this", "fn", "call", "_id", "_wrap", "_shim", "$angular", "_layout", "_scroller", "_model", "css", "trigger", "events", "name", "modal", "width", "height", "destroy", "shim", "open", "target", "show", "_invokeNgController", "view", "openFor", "close", "is", "hide", "style", "addClass", "wrap", "wrapper", "parent", "position", "align", "effect", "className", "e", "preventDefault", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,0BACH,oBACA,qBACDD,IACL,WA8FE,MAnFC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAMG,OAAOD,GAAIE,EAAOF,EAAGE,KAAMC,EAASH,EAAGG,OAAQC,EAAc,aAAcC,EAAO,OAAQC,EAAQ,QAASC,EAAO,OAAQC,EAAO,uCAClKC,EAAYT,EAAGU,KAAKC,QACpBC,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOC,IACXb,GAAOc,GAAGL,KAAKM,KAAKH,EAAMF,EAASC,GACnCC,EAAKI,MACLJ,EAAKK,QACLL,EAAKM,QACAL,KAAKF,QAAQQ,WACdP,EAAKQ,UACLR,EAAKS,YACLT,EAAKU,UAETV,EAAKF,QAAQa,IAAI,UAAW,IAC5BX,EAAKY,QAAQpB,IAEjBqB,QACIrB,EACAH,EACAC,EACAC,GAEJQ,SACIe,KAAM,YACNC,OAAO,EACPC,MAAO,KACPC,OAAQ,MAEZC,QAAS,WACL9B,EAAOc,GAAGgB,QAAQf,KAAKF,MACvBA,KAAKkB,KAAKD,WAEdE,KAAM,SAAUC,GACZ,GAAIrB,GAAOC,IACXD,GAAKqB,OAASxC,EAAEwC,GAChBrB,EAAKmB,KAAKG,OACVtB,EAAKuB,sBACLvB,EAAKY,QAAQ,QAAUY,KAAMxB,KAEjCyB,QAAS,SAAUJ,GACVpB,KAAKW,QAAQvB,GAAegC,OAAQA,MACrCpB,KAAKmB,KAAKC,GACVpB,KAAKW,QAAQtB,GAAQ+B,OAAQA,MAGrCK,MAAO,WACCzB,KAAKH,QAAQ6B,GAAG,cAAgB1B,KAAKW,QAAQrB,IAC7CU,KAAKkB,KAAKS,QAGlBvB,MAAO,WACH,GAAiEW,GAAOC,EAApEjB,EAAOC,KAAMH,EAAUE,EAAKF,QAASC,EAAUC,EAAKD,OACxDiB,GAAQlB,EAAQ,GAAG+B,MAAMb,OAAS,OAClCC,EAASnB,EAAQ,GAAG+B,MAAMZ,QAAU,OACpCnB,EAAQgC,SAAS,gBAAgBC,KAAKtC,GACtCO,EAAKgC,QAAUlC,EAAQmC,SAAStB,KAC5BK,MAAOjB,EAAQiB,OAASA,GAAS,IACjCC,OAAQlB,EAAQkB,QAAUA,GAAU,MACrCa,SAAmB,QAAVb,EAAmB,kBAAoB,IACnDnB,EAAQa,KACJK,MAAO,GACPC,OAAQ,MAGhBX,MAAO,WACH,GAAIN,GAAOC,IACXD,GAAKmB,KAAO,GAAIhC,GAAKa,EAAKgC,SACtBjB,MAAOf,EAAKD,QAAQgB,MACpBmB,SAAU,gBACVC,MAAO,gBACPC,OAAQ,UACRC,UAAW,oBACXT,KAAM,SAAUU,GACRtC,EAAKY,QAAQrB,IACb+C,EAAEC,sBAMtBtD,GAAGuD,OAAO9C,IACZV,OAAOD,MAAM0D,QACRzD,OAAOD,OACE,kBAAVH,SAAwBA,OAAO8D,IAAM9D,OAAS,SAAU+D,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.modalview.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.modalview', [\n        'kendo.mobile.shim',\n        'kendo.mobile.view'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.modalview',\n        name: 'ModalView',\n        category: 'mobile',\n        description: 'The Kendo ModalView is used to present self-contained functionality in the context of the current task.',\n        depends: [\n            'mobile.shim',\n            'mobile.view'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.mobile.ui, Shim = ui.Shim, Widget = ui.Widget, BEFORE_OPEN = 'beforeOpen', OPEN = 'open', CLOSE = 'close', INIT = 'init', WRAP = '<div class=\"km-modalview-wrapper\" />';\n        var ModalView = ui.View.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                that._id();\n                that._wrap();\n                that._shim();\n                if (!this.options.$angular) {\n                    that._layout();\n                    that._scroller();\n                    that._model();\n                }\n                that.element.css('display', '');\n                that.trigger(INIT);\n            },\n            events: [\n                INIT,\n                BEFORE_OPEN,\n                OPEN,\n                CLOSE\n            ],\n            options: {\n                name: 'ModalView',\n                modal: true,\n                width: null,\n                height: null\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.shim.destroy();\n            },\n            open: function (target) {\n                var that = this;\n                that.target = $(target);\n                that.shim.show();\n                that._invokeNgController();\n                that.trigger('show', { view: that });\n            },\n            openFor: function (target) {\n                if (!this.trigger(BEFORE_OPEN, { target: target })) {\n                    this.open(target);\n                    this.trigger(OPEN, { target: target });\n                }\n            },\n            close: function () {\n                if (this.element.is(':visible') && !this.trigger(CLOSE)) {\n                    this.shim.hide();\n                }\n            },\n            _wrap: function () {\n                var that = this, element = that.element, options = that.options, width, height;\n                width = element[0].style.width || 'auto';\n                height = element[0].style.height || 'auto';\n                element.addClass('km-modalview').wrap(WRAP);\n                that.wrapper = element.parent().css({\n                    width: options.width || width || 300,\n                    height: options.height || height || 300\n                }).addClass(height == 'auto' ? ' km-auto-height' : '');\n                element.css({\n                    width: '',\n                    height: ''\n                });\n            },\n            _shim: function () {\n                var that = this;\n                that.shim = new Shim(that.wrapper, {\n                    modal: that.options.modal,\n                    position: 'center center',\n                    align: 'center center',\n                    effect: 'fade:in',\n                    className: 'km-modalview-root',\n                    hide: function (e) {\n                        if (that.trigger(CLOSE)) {\n                            e.preventDefault();\n                        }\n                    }\n                });\n            }\n        });\n        ui.plugin(ModalView);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}