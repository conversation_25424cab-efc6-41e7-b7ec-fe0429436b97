{"version": 3, "sources": ["kendo.reorderable.js"], "names": ["f", "define", "$", "undefined", "toggleHintClass", "hint", "denied", "find", "removeClass", "addClass", "kendo", "window", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "getOffset", "Widget", "ui", "CHANGE", "KREORDERABLE", "Reorderable", "extend", "init", "element", "options", "draggable", "that", "this", "group", "guid", "fn", "call", "Draggable", "autoScroll", "filter", "reorderDropCue", "kendoDropTarget", "dragenter", "e", "drop<PERSON>ar<PERSON>", "offset", "left", "_draggable", "_dropTargetAllowed", "_isLastDraggable", "inSameContainer", "source", "target", "sourceIndex", "_index", "targetIndex", "_dropTarget", "css", "height", "top", "appendTo", "document", "body", "dragleave", "remove", "drop", "trigger", "oldIndex", "newIndex", "position", "bind", "dragcancel", "_elements", "dragend", "dragstart", "currentTarget", "drag", "dropStartOffset", "width", "hasClass", "pageX", "name", "events", "item", "elements", "get", "found", "length", "pop", "dragOverContainers", "index", "destroy", "each", "data", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBACH,aACA,qBACDD,IACL,WAsLE,MA3KC,UAAUE,EAAGC,GAEV,QAASC,GAAgBC,EAAMC,GAC3BD,EAAOH,EAAEG,GACLC,EACAD,EAAKE,KAAK,kBAAkBC,YAAY,YAAYC,SAAS,cAE7DJ,EAAKE,KAAK,kBAAkBC,YAAY,cAAcC,SAAS,YAP1E,GACOC,GAAQC,OAAOD,MAAOE,EAAaF,EAAMG,YAAaC,EAAcJ,EAAMK,aAAcC,EAAYN,EAAMM,UAAWC,EAASP,EAAMQ,GAAGD,OAAQE,EAAS,SAAUC,EAAe,gBASjLC,EAAcJ,EAAOK,QACrBC,KAAM,SAAUC,EAASC,GACrB,GAAiBC,GAAbC,EAAOC,KAAiBC,EAAQnB,EAAMoB,OAAS,cACnDb,GAAOc,GAAGR,KAAKS,KAAKL,EAAMH,EAASC,GACnCD,EAAUG,EAAKH,QAAQf,SAASW,GAChCK,EAAUE,EAAKF,QACfE,EAAKD,UAAYA,EAAYD,EAAQC,WAAa,GAAIhB,GAAMQ,GAAGe,UAAUT,GACrEK,MAAOA,EACPK,YAAY,EACZC,OAAQV,EAAQU,OAChB9B,KAAMoB,EAAQpB,OAElBsB,EAAKS,eAAiBlC,EAAE,2CACxBsB,EAAQjB,KAAKmB,EAAUD,QAAQU,QAAQE,iBACnCR,MAAOH,EAAUD,QAAQI,MACzBS,UAAW,SAAUC,GAAV,GAIHC,GAA2BC,EAC3BnC,EAIIoC,CARHf,GAAKgB,aAGNH,EAAaZ,KAAKJ,QAClBlB,GAAUqB,EAAKiB,mBAAmBJ,IAAeb,EAAKkB,mBAC1DzC,EAAgBmC,EAAEb,UAAUrB,KAAMC,GAC7BA,IACDmC,EAASzB,EAAUwB,GACfE,EAAOD,EAAOC,KACdjB,EAAQqB,kBAAoBrB,EAAQqB,iBAChCC,OAAQP,EACRQ,OAAQrB,EAAKgB,WACbM,YAAatB,EAAKuB,OAAOV,GACzBW,YAAaxB,EAAKuB,OAAOvB,EAAKgB,cAElChB,EAAKyB,YAAcZ,EAEfb,EAAKuB,OAAOV,GAAcb,EAAKuB,OAAOvB,EAAKgB,cAC3CD,GAAQ9B,EAAW4B,IAG3Bb,EAAKS,eAAeiB,KAChBC,OAAQxC,EAAY0B,GACpBe,IAAKd,EAAOc,IACZb,KAAMA,IACPc,SAASC,SAASC,SAG7BC,UAAW,SAAUpB,GACjBnC,EAAgBmC,EAAEb,UAAUrB,MAAM,GAClCsB,EAAKS,eAAewB,SACpBjC,EAAKyB,YAAc,MAEvBS,KAAM,WAAA,GAKErB,GACAd,CALJC,GAAKyB,YAAc,KACdzB,EAAKgB,aAGNH,EAAaZ,KAAKJ,QAClBE,EAAYC,EAAKgB,WACjBhB,EAAKiB,mBAAmBJ,KAAgBb,EAAKkB,oBAC7ClB,EAAKmC,QAAQ3C,GACTK,QAASG,EAAKgB,WACdK,OAAQR,EACRuB,SAAUpC,EAAKuB,OAAOxB,GACtBsC,SAAUrC,EAAKuB,OAAOV,GACtByB,SAAUjD,EAAUW,EAAKS,gBAAgBM,KAAO1B,EAAUwB,GAAYE,KAAO,QAAU,eAKvGhB,EAAUwC,MACN,aACA,UACA,YACA,SAEAC,WAAY,WACRxC,EAAKS,eAAewB,SACpBjC,EAAKgB,WAAa,KAClBhB,EAAKyC,UAAY,MAErBC,QAAS,WACL1C,EAAKS,eAAewB,SACpBjC,EAAKgB,WAAa,KAClBhB,EAAKyC,UAAY,MAErBE,UAAW,SAAU/B,GACjBZ,EAAKgB,WAAaJ,EAAEgC,cACpB5C,EAAKyC,UAAYzC,EAAKH,QAAQjB,KAAKoB,EAAKD,UAAUD,QAAQU,SAE9DqC,KAAM,SAAUjC,GAAV,GAIEkC,GACAC,CAJC/C,GAAKyB,cAAexB,KAAKvB,KAAKE,KAAK,kBAAkBoE,SAAS,gBAG/DF,EAAkBzD,EAAUW,EAAKyB,aAAaV,KAC9CgC,EAAQ9D,EAAWe,EAAKyB,aAExBzB,EAAKS,eAAeiB,IADpBd,EAAEqC,MAAQH,EAAkBC,EAAQ,GACVhC,KAAM+B,EAAkBC,IAExBhC,KAAM+B,SAKhDhD,SACIoD,KAAM,cACN1C,OAAQ,KAEZ2C,QAAS3D,GACT0B,iBAAkB,WACd,GAAoIkC,GAAhIjC,EAAkBlB,KAAKH,QAAQqB,gBAAiBpB,EAAYE,KAAKe,WAAW,GAAIqC,EAAWpD,KAAKwC,UAAUa,MAAOC,GAAQ,CAC7H,KAAKpC,EACD,OAAO,CAEX,OAAQoC,GAASF,EAASG,OAAS,GAC/BJ,EAAOC,EAASI,MAChBF,EAAQxD,IAAcqD,GAAQjC,GAC1BC,OAAQrB,EACRsB,OAAQ+B,EACR9B,YAAarB,KAAKsB,OAAOxB,GACzByB,YAAavB,KAAKsB,OAAO6B,IAGjC,QAAQG,GAEZtC,mBAAoB,SAAUJ,GAC1B,GAAIM,GAAkBlB,KAAKH,QAAQqB,gBAAiBuC,EAAqBzD,KAAKH,QAAQ4D,mBAAoB3D,EAAYE,KAAKe,UAC3H,OAAIjB,GAAU,KAAOc,EAAW,MAG3BM,IAAoBuC,MAGrBvC,GACIC,OAAQrB,EACRsB,OAAQR,EACRS,YAAarB,KAAKsB,OAAOxB,GACzByB,YAAavB,KAAKsB,OAAOV,MAI1B6C,EAAmBzD,KAAKsB,OAAOxB,GAAYE,KAAKsB,OAAOV,OAElEU,OAAQ,SAAU1B,GACd,MAAOI,MAAKwC,UAAUkB,MAAM9D,IAEhC+D,QAAS,WACL,GAAI5D,GAAOC,IACXX,GAAOc,GAAGwD,QAAQvD,KAAKL,GACvBA,EAAKH,QAAQjB,KAAKoB,EAAKD,UAAUD,QAAQU,QAAQqD,KAAK,WAClD,GAAIT,GAAO7E,EAAE0B,KACTmD,GAAKU,KAAK,oBACVV,EAAKU,KAAK,mBAAmBF,YAGjC5D,EAAKD,YACLC,EAAKD,UAAU6D,UACf5D,EAAKD,UAAUF,QAAUG,EAAKD,UAAY,MAE9CC,EAAKqD,SAAWrD,EAAKS,eAAiBT,EAAKyC,UAAYzC,EAAKgB,WAAa,OAGjFjC,GAAMQ,GAAGwE,OAAOrE,IAClBV,OAAOD,MAAMiF,QACRhF,OAAOD,OACE,kBAAVT,SAAwBA,OAAO2F,IAAM3F,OAAS,SAAU4F,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.reorderable.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.reorderable', [\n        'kendo.core',\n        'kendo.draganddrop'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'reorderable',\n        name: 'Reorderable',\n        category: 'framework',\n        depends: [\n            'core',\n            'draganddrop'\n        ],\n        advanced: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, getOffset = kendo.getOffset, Widget = kendo.ui.Widget, CHANGE = 'change', KREORDERABLE = 'k-reorderable';\n        function toggleHintClass(hint, denied) {\n            hint = $(hint);\n            if (denied) {\n                hint.find('.k-drag-status').removeClass('k-i-plus').addClass('k-i-cancel');\n            } else {\n                hint.find('.k-drag-status').removeClass('k-i-cancel').addClass('k-i-plus');\n            }\n        }\n        var Reorderable = Widget.extend({\n            init: function (element, options) {\n                var that = this, draggable, group = kendo.guid() + '-reorderable';\n                Widget.fn.init.call(that, element, options);\n                element = that.element.addClass(KREORDERABLE);\n                options = that.options;\n                that.draggable = draggable = options.draggable || new kendo.ui.Draggable(element, {\n                    group: group,\n                    autoScroll: true,\n                    filter: options.filter,\n                    hint: options.hint\n                });\n                that.reorderDropCue = $('<div class=\"k-reorder-cue\"></div></div>');\n                element.find(draggable.options.filter).kendoDropTarget({\n                    group: draggable.options.group,\n                    dragenter: function (e) {\n                        if (!that._draggable) {\n                            return;\n                        }\n                        var dropTarget = this.element, offset;\n                        var denied = !that._dropTargetAllowed(dropTarget) || that._isLastDraggable();\n                        toggleHintClass(e.draggable.hint, denied);\n                        if (!denied) {\n                            offset = getOffset(dropTarget);\n                            var left = offset.left;\n                            if (options.inSameContainer && !options.inSameContainer({\n                                    source: dropTarget,\n                                    target: that._draggable,\n                                    sourceIndex: that._index(dropTarget),\n                                    targetIndex: that._index(that._draggable)\n                                })) {\n                                that._dropTarget = dropTarget;\n                            } else {\n                                if (that._index(dropTarget) > that._index(that._draggable)) {\n                                    left += outerWidth(dropTarget);\n                                }\n                            }\n                            that.reorderDropCue.css({\n                                height: outerHeight(dropTarget),\n                                top: offset.top,\n                                left: left\n                            }).appendTo(document.body);\n                        }\n                    },\n                    dragleave: function (e) {\n                        toggleHintClass(e.draggable.hint, true);\n                        that.reorderDropCue.remove();\n                        that._dropTarget = null;\n                    },\n                    drop: function () {\n                        that._dropTarget = null;\n                        if (!that._draggable) {\n                            return;\n                        }\n                        var dropTarget = this.element;\n                        var draggable = that._draggable;\n                        if (that._dropTargetAllowed(dropTarget) && !that._isLastDraggable()) {\n                            that.trigger(CHANGE, {\n                                element: that._draggable,\n                                target: dropTarget,\n                                oldIndex: that._index(draggable),\n                                newIndex: that._index(dropTarget),\n                                position: getOffset(that.reorderDropCue).left > getOffset(dropTarget).left ? 'after' : 'before'\n                            });\n                        }\n                    }\n                });\n                draggable.bind([\n                    'dragcancel',\n                    'dragend',\n                    'dragstart',\n                    'drag'\n                ], {\n                    dragcancel: function () {\n                        that.reorderDropCue.remove();\n                        that._draggable = null;\n                        that._elements = null;\n                    },\n                    dragend: function () {\n                        that.reorderDropCue.remove();\n                        that._draggable = null;\n                        that._elements = null;\n                    },\n                    dragstart: function (e) {\n                        that._draggable = e.currentTarget;\n                        that._elements = that.element.find(that.draggable.options.filter);\n                    },\n                    drag: function (e) {\n                        if (!that._dropTarget || this.hint.find('.k-drag-status').hasClass('k-i-cancel')) {\n                            return;\n                        }\n                        var dropStartOffset = getOffset(that._dropTarget).left;\n                        var width = outerWidth(that._dropTarget);\n                        if (e.pageX > dropStartOffset + width / 2) {\n                            that.reorderDropCue.css({ left: dropStartOffset + width });\n                        } else {\n                            that.reorderDropCue.css({ left: dropStartOffset });\n                        }\n                    }\n                });\n            },\n            options: {\n                name: 'Reorderable',\n                filter: '*'\n            },\n            events: [CHANGE],\n            _isLastDraggable: function () {\n                var inSameContainer = this.options.inSameContainer, draggable = this._draggable[0], elements = this._elements.get(), found = false, item;\n                if (!inSameContainer) {\n                    return false;\n                }\n                while (!found && elements.length > 0) {\n                    item = elements.pop();\n                    found = draggable !== item && inSameContainer({\n                        source: draggable,\n                        target: item,\n                        sourceIndex: this._index(draggable),\n                        targetIndex: this._index(item)\n                    });\n                }\n                return !found;\n            },\n            _dropTargetAllowed: function (dropTarget) {\n                var inSameContainer = this.options.inSameContainer, dragOverContainers = this.options.dragOverContainers, draggable = this._draggable;\n                if (draggable[0] === dropTarget[0]) {\n                    return false;\n                }\n                if (!inSameContainer || !dragOverContainers) {\n                    return true;\n                }\n                if (inSameContainer({\n                        source: draggable,\n                        target: dropTarget,\n                        sourceIndex: this._index(draggable),\n                        targetIndex: this._index(dropTarget)\n                    })) {\n                    return true;\n                }\n                return dragOverContainers(this._index(draggable), this._index(dropTarget));\n            },\n            _index: function (element) {\n                return this._elements.index(element);\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that.element.find(that.draggable.options.filter).each(function () {\n                    var item = $(this);\n                    if (item.data('kendoDropTarget')) {\n                        item.data('kendoDropTarget').destroy();\n                    }\n                });\n                if (that.draggable) {\n                    that.draggable.destroy();\n                    that.draggable.element = that.draggable = null;\n                }\n                that.elements = that.reorderDropCue = that._elements = that._draggable = null;\n            }\n        });\n        kendo.ui.plugin(Reorderable);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}