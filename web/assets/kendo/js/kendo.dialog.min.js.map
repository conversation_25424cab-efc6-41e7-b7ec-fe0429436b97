{"version": 3, "sources": ["kendo.dialog.js"], "names": ["f", "define", "$", "undefined", "defined", "x", "constrain", "value", "low", "high", "Math", "max", "min", "parseInt", "Infinity", "button<PERSON>eyTrigger", "e", "keyCode", "keys", "ENTER", "SPACEBAR", "templates", "PopupBox", "<PERSON><PERSON>", "kendo<PERSON><PERSON>t", "Confirm", "kendoConfirm", "Prompt", "kendoPrompt", "kendo", "window", "Widget", "ui", "TabKeyTrap", "Popup", "proxy", "template", "isFunction", "NS", "KDIALOG", "KWINDOW", "KICONCLOSE", "KCONTENTCLASS", "KCONTENTSELECTOR", "KCONTENT", "KSCROLL", "KTITLELESS", "KDIALOGTITLE", "KDIALOGTITLEBAR", "KBUTTONGROUP", "KBUTTON", "KALERT", "KCONFIRM", "KPROMPT", "KTEXTBOX", "KOVERLAY", "VISIBLE", "ZINDEX", "BODY", "INITOPEN", "TOUCHSTART", "TOUCHMOVE", "OPEN", "CLOSE", "SHOW", "HIDE", "WIDTH", "SIZE", "small", "medium", "large", "HIDDEN", "OVERFLOW", "DATADOCOVERFLOWRULE", "DATAHTMLTAPYRULE", "HUNDREDPERCENT", "CSSFLEXBOX", "support", "cssFlexbox", "messages", "okText", "cancel", "promptInput", "ceil", "overlaySelector", "DialogBase", "extend", "init", "element", "options", "that", "this", "fn", "call", "_init", "notify", "wrapper", "_center<PERSON><PERSON><PERSON>", "_center", "appendTo", "visible", "is", "wrapperTemplate", "_createDialog", "closest", "_defaultFocus", "_tabindex", "_dimensions", "_tabKeyTrap", "_triggerOpen", "hide", "setOptions", "sizeClass", "size", "title", "content", "destroy", "children", "html", "actions", "remove", "_createActionbar", "show", "_closable", "removeClass", "modal", "_enableDocumentScrolling", "_overlay", "i", "width", "height", "dimensions", "length", "css", "_setElementMaxHeight", "indexOf", "outerWidth", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "outerHeight", "minHeight", "maxHeight", "_setElementHeight", "addClass", "paddingBox", "elementMaxHeight", "_paddingBox", "parseFloat", "_uiHeight", "vertical", "paddingTop", "paddingLeft", "paddingBottom", "paddingRight", "horizontal", "elementHeight", "_applyScrollClassName", "hasScroll", "get", "scrollHeight", "actionbar", "actionbarHeight", "offsetHeight", "titlebar", "titlebarHeight", "overlay", "insertBefore", "toggle", "_waiAriaOverlay", "_removeWaiAriaOverlay", "preventScroll", "_stopDocumentScrolling", "node", "_overlayedNodes", "prevAll", "add", "nextAll", "each", "jthis", "data", "attr", "hiddenValue", "removeAttr", "_closeClick", "preventDefault", "close", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ESC", "_keydown", "_closing", "closable", "isRtl", "titleId", "id", "guid", "toggleClass", "append", "titlebarActions", "find", "closeAction", "prepend", "autoApplyNS", "on", "isStretchedLayout", "buttonLayout", "_addButtons", "_normalizeButtonSize", "action", "text", "btn", "o", "actionClick", "_actionClick", "actionKeyHandler", "_actionKeyHandler", "buttonSize", "round", "_mergeTextWithOptions", "lastButton", "currentSize", "style", "difference", "target", "tabIndex", "closeBtn", "actionButtons", "_runActionBtn", "currentTarget", "preventClose", "sender", "toFront", "_triggerInitOpen", "trigger", "opacity", "_focusDialog", "open", "otherModalsVisible", "overlayFx", "showOptions", "_animationOptions", "kendoStop", "_modals", "duration", "effects", "Fade", "fx", "fadeIn", "endValue", "play", "kendoAnimate", "complete", "_openAnimationEnd", "animation", "basicAnimation", "_initOpenTriggered", "zIndex", "originalZIndex", "center", "windowObject", "zIndexNew", "isNaN", "systemTriggered", "arguments", "_close", "_stopCenterOnResize", "hideOptions", "userTriggered", "_removeOverlay", "reverse", "_closeAnimationEnd", "_centerOnResize", "documentWindow", "scrollTop", "scrollLeft", "newLeft", "newTop", "left", "top", "_trackResize", "onResize", "unbindResize", "modals", "hideOverlay", "_object", "last", "$html", "$body", "_storeOverflowRule", "mobileOS", "ios", "addEventListener", "_touchStart", "passive", "_touchMove", "changedTouches", "pageY", "$target", "upScroll", "preventYScroll", "clientHeight", "document", "body", "_restoreOverflowRule", "removeData", "removeEventListener", "$element", "_isOverflowStored", "overflowRule", "overflow", "lastModal", "zStack", "filter", "dom", "object", "sort", "a", "b", "widget", "widgetInstance", "_destroy", "ns", "off", "encodedHtml", "htmlEncode", "prependTo", "angular", "elements", "push", "dataItem", "_focus", "trap", "focus", "events", "Dialog", "name", "plugin", "alertWrapper", "_ensureContentId", "bind", "_ariaDescribedBy", "_initFocus", "_chooseEntryFocus", "location", "host", "alert", "result", "Deferred", "primary", "resolve", "reject", "confirmDialog", "confirm", "_createPrompt", "prompt<PERSON><PERSON><PERSON>", "promptInputContainer", "insertAfter", "val", "promptDialog", "prompt", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,gBACH,aACA,eACDD,IACL,WA4yBE,MAjyBC,UAAUE,EAAGC,GAUV,QAASC,GAAQC,GACb,MAAmB,KAALA,EAElB,QAASC,GAAUC,EAAOC,EAAKC,GAC3B,MAAOC,MAAKC,IAAID,KAAKE,IAAIC,SAASN,EAAO,IAAKE,IAASK,EAAAA,EAAWL,EAAOI,SAASJ,EAAM,KAAMI,SAASL,EAAK,KAEhH,QAASO,GAAiBC,GACtB,MAAOA,GAAEC,SAAWC,EAAKC,OAASH,EAAEC,SAAWC,EAAKE,SAjB3D,GAS4BC,GAioBrBC,EAoCAC,EAaAC,EAGAC,EA4BAC,EAIAC,EA2CAC,EAxwBAC,EAAQC,OAAOD,MAAOE,EAASF,EAAMG,GAAGD,OAAQE,EAAaJ,EAAMG,GAAGE,MAAMD,WAAYE,EAAQjC,EAAEiC,MAAOC,EAAWP,EAAMO,SAAUlB,EAAOW,EAAMX,KAAMmB,EAAanC,EAAEmC,WAAYC,EAAK,cAAeC,EAAU,YAAaC,EAAU,YAAaC,EAAa,kBAAmBC,EAAgB,8CAA+CC,EAAmB,oBAAqBC,EAAW,aAAcC,EAAU,WAAYC,EAAa,qBAAsBC,EAAe,kBAAmBC,EAAkBD,EAAe,MAAOE,EAAe,wBAAyBC,EAAU,YAAaC,EAAS,UAAWC,EAAW,YAAaC,EAAU,WAAYC,EAAW,aAAcC,EAAW,aAAcC,EAAU,WAAYC,EAAS,SAAUC,EAAO,OAAQC,EAAW,WAAYC,EAAa,aAAcC,EAAY,YAAaC,EAAO,OAAQC,EAAQ,QAASC,EAAO,OAAQC,EAAO,OAAQC,EAAQ,QAASC,GACx5BC,MAAO,cACPC,OAAQ,cACRC,MAAO,eACRC,EAAS,SAAUC,EAAW,WAAYC,EAAsB,yBAA0BC,EAAmB,QAASC,GAAiB,IAAKC,GAAa/C,EAAMgD,QAAQC,WAAYC,IAClLC,OAAQ,KACRC,OAAQ,SACRC,YAAa,SACdC,GAAOzE,KAAKyE,KAAiBC,GAAkB,+BAUlDC,GAAatD,EAAOuD,QACpBC,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOC,IACX5D,GAAO6D,GAAGL,KAAKM,KAAKH,EAAMF,EAASC,GACnCC,EAAKI,MAAMJ,EAAKF,QAASE,EAAKD,SAC9B5D,EAAMkE,OAAOL,IAEjBI,MAAO,SAAUN,EAASC,GACtB,GAAiBO,GAAbN,EAAOC,IACXD,GAAKO,gBAAkB9D,EAAMuD,EAAKQ,QAASR,GAC3CA,EAAKS,SAAWjG,EAAEwD,GACbtD,EAAQqF,EAAQW,UAAgC,OAApBX,EAAQW,UACrCX,EAAQW,QAAUZ,EAAQa,GAAG7C,IAE7BkC,EAAKY,kBAAoBnG,IACzBuF,EAAKY,gBAAkBjF,EAAU2E,SAErCN,EAAKa,gBACLP,EAAUN,EAAKM,QAAUR,EAAQgB,QAAQjE,GACrCkD,EAAQgB,gBAAkBtG,IAC1BuF,EAAKe,cAAgBjB,EAAQ,IAEjCE,EAAKgB,UAAUlB,GACfE,EAAKiB,cACLhB,KAAKiB,YAAc,GAAI3E,GAAW+D,GAC7BN,EAAKD,QAAQW,QAGdV,EAAKmB,eAFLnB,EAAKM,QAAQc,QAKrBC,WAAY,SAAUtB,GAAV,GA0BAW,GAzBJV,EAAOC,KACPqB,EAAYtB,EAAKD,QAAQwB,IAC7BxB,GAAUvF,EAAEoF,OAAOI,EAAKD,QAASA,GACjC1D,EAAO6D,GAAGmB,WAAWlB,KAAKH,EAAMD,GAC5BA,EAAQyB,QAAU/G,GAClBuF,EAAKwB,MAAMzB,EAAQyB,OAEnBzB,EAAQ0B,UACRtF,EAAMuF,QAAQ1B,EAAKF,QAAQ6B,YAC3B3B,EAAKF,QAAQ8B,KAAK7B,EAAQ0B,UAE1B1B,EAAQ8B,UACR7B,EAAKM,QAAQqB,SAASpE,GAAcuE,SACpC9B,EAAK+B,iBAAiB/B,EAAKM,UAE/BN,EAAKM,QAAQ0B,OACbhC,EAAKiC,UAAUjC,EAAKM,SACpBN,EAAKM,QAAQ4B,YAAYzD,EAAK6C,IAC9BtB,EAAKiB,cACAlB,EAAQW,QAGTV,EAAKmB,eAFLnB,EAAKM,QAAQc,OAIY,IAAlBrB,EAAQoC,QACXzB,EAAUV,EAAKD,QAAQW,WAAY,EACvCV,EAAKoC,2BACLpC,EAAKqC,SAAStC,EAAQoC,OAASzB,KAGvCO,YAAa,WAAA,GAOAqB,GACDzH,EAPJmF,EAAOC,KAAMK,EAAUN,EAAKM,QAASP,EAAUC,EAAKD,QAASwC,EAAQxC,EAAQwC,MAAOC,EAASzC,EAAQyC,OAAQlB,EAAYvB,EAAQwB,KAAMkB,GACnI,WACA,YACA,WACA,YAER,KAASH,EAAI,EAAGA,EAAIG,EAAWC,OAAQJ,IAC/BzH,EAAQkF,EAAQ0C,EAAWH,IAC3BzH,GAASA,GAASO,EAAAA,GAClBkF,EAAQqC,IAAIF,EAAWH,GAAIzH,EAGnCoF,MAAK2C,uBACDL,KACIA,GAAAA,GAAiBM,QAAQ,KAAO,EAChCvC,EAAQiC,MAAMA,GAEdjC,EAAQwC,WAAWlI,EAAU2H,EAAOxC,EAAQgD,SAAUhD,EAAQiD,YAGlER,KACIA,GAAAA,GAAkBK,QAAQ,KAAO,EACjCvC,EAAQkC,OAAOA,GAEflC,EAAQ2C,YAAYrI,EAAU4H,EAAQzC,EAAQmD,UAAWnD,EAAQoD,YAErElD,KAAKmD,qBAEL9B,GAAa7C,EAAK6C,IAClBhB,EAAQ+C,SAAS5E,EAAK6C,KAG9BsB,qBAAsB,WAClB,GAA6EU,GAAYC,EAArFvD,EAAOC,KAAMH,EAAUE,EAAKF,QAASqD,EAAYnD,EAAKD,QAAQoD,SAC9DA,IAAa/H,EAAAA,IACbkI,EAAatD,EAAKwD,YAAY1D,GAC9ByD,EAAmBE,WAAWN,EAAW,IAAMnD,EAAK0D,YAAcJ,EAAWK,SACzEJ,EAAmB,GACnBzD,EAAQ6C,KAAMQ,UAAW1D,GAAK8D,GAAoB,SAI9DC,YAAa,SAAU1D,GACnB,GAAI8D,GAAaH,WAAW3D,EAAQ6C,IAAI,eAAgB,IAAKkB,EAAcJ,WAAW3D,EAAQ6C,IAAI,gBAAiB,IAAKmB,EAAgBL,WAAW3D,EAAQ6C,IAAI,kBAAmB,IAAKoB,EAAeN,WAAW3D,EAAQ6C,IAAI,iBAAkB,GAC/O,QACIgB,SAAUC,EAAaE,EACvBE,WAAYH,EAAcE,IAGlCX,kBAAmB,WACf,GAAIpD,GAAOC,KAAMH,EAAUE,EAAKF,QAAS0C,EAASxC,EAAKD,QAAQyC,OAAQc,EAAatD,EAAKwD,YAAY1D,GAAUmE,EAAgBR,WAAWjB,EAAQ,IAAMxC,EAAK0D,YAAcJ,EAAWK,QAClLM,GAAgB,IAChBA,EAAgB,GAEpBnE,EAAQ6C,KAAMH,OAAQ/C,GAAKwE,GAAiB,OAC5ChE,KAAKiE,sBAAsBpE,IAE/BoE,sBAAuB,SAAUpE,GAC7B,GAAIqE,GAAYrE,EAAQsE,IAAI,GAAGC,aAAevE,EAAQmD,aAClDkB,GACArE,EAAQuD,SAASlG,GAEjB2C,EAAQoC,YAAY/E,IAG5BuG,UAAW,WACP,GAAI1D,GAAOC,KAAMK,EAAUN,EAAKM,QAASgE,EAAYhE,EAAQqB,SAASpE,GAAegH,EAAkBD,EAAU,IAAMA,EAAU,GAAGE,cAAgB,EAAGC,EAAWnE,EAAQqB,SAASrE,GAAkBoH,EAAiBD,EAAS,IAAMA,EAAS,GAAGD,cAAgB,CACjQ,OAAOD,GAAkBG,GAE7BrC,SAAU,SAAU3B,GAChB,GAAIiE,GAAU1E,KAAKQ,SAASkB,SAAS9D,GAAWyC,EAAUL,KAAKK,OAa/D,OAZKqE,GAAQjC,SACTiC,EAAUnK,EAAEmB,EAAUgJ,UAE1BA,EAAQC,aAAatE,EAAQ,IAAIuE,OAAOnE,GAASiC,IAAI5E,EAAQ5C,SAASmF,EAAQqC,IAAI5E,GAAS,IAAM,GAC7F2C,EACAT,KAAK6E,kBAEL7E,KAAK8E,wBAEL9E,KAAKF,QAAQoC,MAAM6C,eACnB/E,KAAKgF,yBAEFN,GAEXG,gBAAiB,WACb,GAAII,GAAOjF,KAAKK,OAChBL,MAAKkF,gBAAkBD,EAAKE,QAAQ1F,IAAiB2F,IAAIH,EAAKI,QAAQ5F,KAAkB6F,KAAK,WACzF,GAAIC,GAAQhL,EAAEyF,KACduF,GAAMC,KAAK,aAAcD,EAAME,KAAK,gBACpCF,EAAME,KAAK,cAAe,WAGlCX,sBAAuB,WACnB,MAAO9E,MAAKkF,iBAAmBlF,KAAKkF,gBAAgBI,KAAK,WAAA,GACjDL,GAAO1K,EAAEyF,MACT0F,EAAcT,EAAKO,KAAK,aACxBE,GACAT,EAAKQ,KAAK,cAAeC,GAEzBT,EAAKU,WAAW,kBAI5BC,YAAa,SAAUvK,GACnBA,EAAEwK,iBACF7F,KAAK8F,OAAM,IAEfC,iBAAkB,SAAU1K,IACpBD,EAAiBC,IAAMA,EAAEC,SAAWC,EAAKyK,MACzChG,KAAK8F,OAAM,IAGnBG,SAAU,SAAU5K,GAChB,GAAI0E,GAAOC,KAAMF,EAAUC,EAAKD,QAASxE,EAAUD,EAAEC,OACjDA,IAAWC,EAAKyK,MAAQjG,EAAKmG,UAAYpG,EAAQqG,UACjDpG,EAAK+F,OAAM,IAGnBlF,cAAe,WACX,GAAIb,GAAOC,KAAMwB,EAAUzB,EAAKF,QAASC,EAAUC,EAAKD,QAASsG,EAAQlK,EAAMgD,QAAQkH,MAAM5E,GAAUgD,EAAWjK,EAAEmB,EAAU8I,SAAS1E,IAAWuG,GAAW7E,EAAQ8E,IAAMpK,EAAMqK,QAAU,SAAUlG,EAAU9F,EAAEwF,EAAKY,gBAAgBb,GACtOO,GAAQmG,YAAY,QAASJ,GAC7B5E,EAAQ4B,SAASrG,GACjBgD,EAAKS,SAASiG,OAAOpG,GACjBP,EAAQyB,SAAU,GAClBlB,EAAQoG,OAAOjC,GACfA,EAASiB,KAAK,KAAMY,GACpBhG,EAAQoF,KAAK,kBAAmBY,IAEhChG,EAAQ+C,SAASjG,GAErB4C,EAAKiC,UAAU3B,GACfA,EAAQoG,OAAOjF,GACX1B,EAAQ0B,UACRtF,EAAMuF,QAAQD,EAAQE,YACtBF,EAAQG,KAAK7B,EAAQ0B,UAErB1B,EAAQ8B,QAAQa,QAChB1C,EAAK+B,iBAAiBzB,IAG9B2B,UAAW,SAAU3B,GAAV,GACHN,GAAOC,KACPF,EAAUC,EAAKD,QACf0E,EAAWnE,EAAQqB,SAASrE,GAC5BqJ,EAAkBlC,EAASmC,KAAK,qBAChCC,EAAcF,EAAgBjE,OAASiE,EAAgBC,KAAK,mBAAqBtG,EAAQsG,KAAK,kBAClGC,GAAY/E,SACR/B,EAAQqG,YAAa,IACjBrG,EAAQyB,SAAU,GAASmF,EAAgBjE,OAC3CiE,EAAgBD,OAAO/K,EAAUoK,MAAMhG,IAEvCO,EAAQwG,QAAQnL,EAAUoK,MAAMhG,IAEpCO,EAAQyG,YAAYnK,GACpBoD,EAAKF,QAAQiH,YAAYnK,GACzB0D,EAAQsG,KAAK7J,GAAYiK,GAAG,QAASvK,EAAMuD,EAAK6F,YAAa7F,IAAOgH,GAAG,UAAWvK,EAAMuD,EAAKgG,iBAAkBhG,IAC/GA,EAAKF,QAAQkH,GAAG,UAAWvK,EAAMuD,EAAKkG,SAAUlG,MAGxD+B,iBAAkB,SAAUzB,GAAV,GACV2G,GAAkD,cAA9BhH,KAAKF,QAAQmH,aACjCA,EAAeD,EAAoB,YAAc,SACjD3C,EAAY9J,EAAEmB,EAAU2I,WAAY4C,aAAcA,IACtDjH,MAAKkH,YAAY7C,GACb2C,IAAsB/H,IACtBe,KAAKmH,qBAAqB9C,GAE9BhE,EAAQoG,OAAOpC,IAEnB6C,YAAa,SAAU7C,GAAV,GAC2O+C,GAAQC,EACnPhF,EAGDiF,EAJJvH,EAAOC,KAAMuH,EAAIxH,EAAKD,QAAS0H,EAAchL,EAAMuD,EAAK0H,aAAc1H,GAAO2H,EAAmBlL,EAAMuD,EAAK4H,kBAAmB5H,GAAO6B,EAAU7B,EAAKD,QAAQ8B,QAASa,EAASb,EAAQa,OAAQmF,EAAa7M,KAAK8M,MAAM7I,GAAiByD,EAC3O,KAASJ,EAAI,EAAGA,EAAII,EAAQJ,IACxB+E,EAASxF,EAAQS,GACjBgF,EAAOtH,EAAK+H,sBAAsBV,GAC9BE,EAAM/M,EAAEmB,EAAU0L,OAAOA,IAASN,YAAYnK,GAAIgF,KAAK0F,GAAM7G,SAAS6D,GAAWmB,KAAK,SAAU4B,EAAOA,QAAQL,GAAG,QAASS,GAAaT,GAAG,UAAWW,GACnI,cAAnBH,EAAEN,cAAiChI,KAC/BoD,GAAKI,EAAS,IACdmF,EAAa5I,GAAiBqD,EAAIuF,GAEtCN,EAAI5E,IAAInE,EAAOqJ,EAAa,OAIxCE,sBAAuB,SAAUV,GAC7B,GAAIC,GAAOD,EAAOC,IAClB,OAAOA,GAAO5K,EAAS4K,GAAMrH,KAAKF,SAAW,IAEjDqH,qBAAsB,SAAU9C,GAC5B,GAAItE,GAAOC,KAAMF,EAAUC,EAAKD,QAASiI,EAAa1D,EAAU3C,SAASnE,EAAU,SAAUyK,EAAcxE,WAAWuE,EAAW,GAAKA,EAAW,GAAGE,MAAM1J,GAAS,GAAI2J,EAAalJ,GAAiBc,EAAQ8B,QAAQa,OAASuF,CAC1NE,GAAa,GACbH,EAAWrF,IAAInE,EAAOyJ,EAAcE,EAAa,MAGzDnH,UAAW,SAAUoH,GAAV,GAMHC,GALArI,EAAOC,KACPK,EAAUN,EAAKM,QACfgI,EAAWhI,EAAQsG,KAAK7J,GACxBwL,EAAgBjI,EAAQsG,KAAKrJ,EAAe,IAAMC,EACtDnB,GAAO6D,GAAGc,UAAUb,KAAKF,KAAMmI,GAC3BC,EAAWD,EAAO1C,KAAK,YAC3B4C,EAAS5C,KAAK,WAAY2C,GAC1BE,EAAc7C,KAAK,WAAY2C,IAEnCX,aAAc,SAAUpM,GAChB2E,KAAKK,QAAQK,GAAG7C,IAChBmC,KAAKuI,cAAclN,EAAEmN,gBAG7Bb,kBAAmB,SAAUtM,GACrBD,EAAiBC,GACjB2E,KAAKuI,cAAclN,EAAEmN,eACdnN,EAAEC,SAAWC,EAAKyK,KACzBhG,KAAK8F,OAAM,IAGnByC,cAAe,SAAUJ,GAAV,GAKPf,GAAmCqB,EAJnC1I,EAAOC,IACPD,GAAKmG,WAGLkB,EAAS7M,EAAE4N,GAAQ3C,KAAK,UAAWiD,EAAe/L,EAAW0K,IAAWA,GAASsB,OAAQ3I,OAAY,EACpG0I,GACD1I,EAAK+F,OAAM,KAGnB5E,aAAc,WAAA,GACNnB,GAAOC,KACPF,EAAUC,EAAKD,QACfO,EAAUN,EAAKM,OACnBN,GAAK4I,UACL5I,EAAK6I,mBACL7I,EAAK8I,QAAQ1K,GACT2B,EAAQoC,QACRnC,EAAKqC,SAAS/B,EAAQK,GAAG7C,IAAU6E,KAAMoG,QAAS,KAClD/I,EAAKgJ,iBAGbC,KAAM,WAAA,GAC2GtE,GAASuE,EActGC,EAdZnJ,EAAOC,KAAMK,EAAUN,EAAKM,QAAS8I,EAAcnJ,KAAKoJ,kBAAkBjL,GAAO2B,EAAUC,EAAKD,OA8BpG,OA7BAE,MAAK4I,mBACA7I,EAAK8I,QAAQ1K,KACV4B,EAAKmG,UACL7F,EAAQgJ,WAAU,GAAM,GAE5BtJ,EAAKmG,UAAW,EAChBnG,EAAK4I,UACL7I,EAAQW,SAAU,EACdX,EAAQoC,QACR+G,IAAuBlJ,EAAKuJ,UAAU7G,OACtCiC,EAAU3E,EAAKqC,SAAS6G,GACxBvE,EAAQ2E,WAAU,GAAM,GACpBF,EAAYI,UAAYrN,EAAMsN,QAAQC,OAASR,GAC3CC,EAAYhN,EAAMwN,GAAGhF,GAASiF,SAClCT,EAAUK,SAASJ,EAAYI,UAAY,GAC3CL,EAAUU,SAAS,IACnBV,EAAUW,QAEVnF,EAAQhC,IAAI,UAAW,IAE3BgC,EAAQ3C,QAEZ1B,EAAQ0B,OAAOsH,YAAYS,cACvBN,QAASL,EAAYK,QACrBD,SAAUJ,EAAYI,SACtBQ,SAAUvN,EAAMuD,EAAKiK,kBAAmBjK,KAE5CM,EAAQ0B,QAELhC,GAEXqJ,kBAAmB,SAAU9C,GAAV,GACX2D,GAAYjK,KAAKF,QAAQmK,UACzBC,GACAlB,MAAQQ,YACR1D,OACI3E,MAAM,EACNqI,YAGR,OAAOS,IAAaA,EAAU3D,IAAO4D,EAAe5D,IAExD0D,kBAAmB,WACXhK,KAAKF,QAAQoC,OACblC,KAAK+I,eAET/I,KAAK6I,QAAQxK,IAEjBuK,iBAAkB,WACTnO,EAAQuF,KAAKmK,sBACdnK,KAAKmK,oBAAqB,EAC1BnK,KAAK6I,QAAQ7K,KAGrB2K,QAAS,WACL,GAAI5I,GAAOC,KAAMK,EAAUN,EAAKM,QAAS+J,GAAU/J,EAAQqC,IAAI5E,GAASuM,EAAiBD,CAazF,OAZArK,GAAKuK,SACL/P,EAAEsC,GAASyI,KAAK,SAAUjD,EAAGxC,GACzB,GAAI0K,GAAehQ,EAAEsF,GAAU2K,EAAYD,EAAa7H,IAAI5E,EACvD2M,OAAMD,KACPJ,EAASrP,KAAKC,KAAKwP,EAAWJ,QAGjC/J,EAAQ,GAAG4H,MAAMmC,QAAUC,EAAiBD,IAC7C/J,EAAQqC,IAAI5E,EAAQsM,EAAS,GAEjCrK,EAAKF,QAAQ8G,KAAK,gBAAgB9E,SAClCxB,EAAU,KACHN,GAEX+F,MAAO,SAAU4E,GAMb,MALKC,WAAUlI,SACXiI,GAAkB,GAEtB1K,KAAK4K,OAAOF,GACZ1K,KAAK6K,sBACE7K,MAEX4K,OAAQ,SAAUF,GACd,GAAI3K,GAAOC,KAAMK,EAAUN,EAAKM,QAASP,EAAUC,EAAKD,QAASqJ,EAAcnJ,KAAKoJ,kBAAkB,QAAS0B,EAAc9K,KAAKoJ,kBAAkB,QACpJ,IAAI/I,EAAQK,GAAG7C,KAAakC,EAAK8I,QAAQzK,GAAS2M,eAAgBL,IAAoB,CAClF,GAAI3K,EAAKmG,SACL,MAEJnG,GAAKmG,UAAW,EAChBpG,EAAQW,SAAU,EAClBT,KAAKgL,iBACL3K,EAAQgJ,YAAYS,cAChBN,QAASsB,EAAYtB,SAAWL,EAAYK,QAC5CyB,QAASH,EAAYG,WAAY,EACjC1B,SAAUuB,EAAYvB,SACtBQ,SAAUvN,EAAMwD,KAAKkL,mBAAoBlL,QAGjD,MAAOD,IAEXuK,OAAQ,WACJtK,KAAKO,UACLP,KAAKmL,mBAET5K,QAAS,WACL,GAAIR,GAAOC,KAAMK,EAAUN,EAAKM,QAAS+K,EAAiB7Q,EAAE4B,QAASkP,EAAY,EAAGC,EAAa,EAAGC,EAAUD,EAAavQ,KAAKC,IAAI,GAAIoQ,EAAe9I,QAAUjC,EAAQiC,SAAW,GAAIkJ,EAASH,EAAYtQ,KAAKC,IAAI,GAAIoQ,EAAe7I,SAAWlC,EAAQkC,SAAWrH,SAASmF,EAAQqC,IAAI,cAAe,KAAO,EAKlT,OAJArC,GAAQqC,KACJ+I,KAAMF,EACNG,IAAKF,IAEFzL,GAEXoL,gBAAiB,WACTnL,KAAK2L,eAGTzP,EAAM0P,SAAS5L,KAAKM,iBACpBN,KAAK2L,cAAe,IAExBd,oBAAqB,WACjB3O,EAAM2P,aAAa7L,KAAKM,iBACxBN,KAAK2L,cAAe,GAExBX,eAAgB,WAAA,GACRc,GAAS9L,KAAKsJ,UACdxJ,EAAUE,KAAKF,QACfiM,EAAcjM,EAAQoC,QAAU4J,EAAOrJ,MACvCsJ,IACA/L,KAAKoC,UAAS,GAAOP,SACjB/B,EAAQoC,MAAM6C,eACd/E,KAAKmC,4BAEF2J,EAAOrJ,SACdzC,KAAKgM,QAAQF,EAAOG,QAAQ7J,UAAS,GACjCtC,EAAQoC,MAAM6C,eACd/E,KAAKgF,2BAIjBA,uBAAwB,WAAA,GAKhBkH,GACAvK,EALA5B,EAAOC,KACPmM,EAAQ5R,EAAE,OACdwF,GAAKqM,mBAAmBD,GACxBA,EAAMzJ,IAAI7D,EAAUD,GAChBsN,EAAQ3R,EAAE,QACVoH,EAAOuK,EAAM,GACjBnM,EAAKqM,mBAAmBF,GACxBA,EAAMxJ,IAAI7D,EAAUD,GAChB1C,EAAMgD,QAAQmN,SAASC,MACvB3K,EAAK4K,iBAAiBtO,EAAY8B,EAAKyM,aAAeC,SAAS,IAC/D9K,EAAK4K,iBAAiBrO,EAAW6B,EAAK2M,YAAcD,SAAS,MAGrED,YAAa,SAAUnR,GACnBd,EAAEyF,MAAMwF,KAAKzG,EAAkB1D,EAAEsR,eAAe,GAAGC,QAEvDF,WAAY,SAAUrR,GAAV,GACJ8M,GAAS9M,EAAE8M,OACX0E,EAAUtS,EAAEc,EAAE8M,QACd2E,EAAWzR,EAAEsR,eAAe,GAAGC,MAAQrS,EAAEyF,MAAMwF,KAAKzG,GAAoB,EACxEgO,EAAiBF,EAAQnM,GAAG1D,IAAsB8P,GAAoC,IAAxBD,EAAQxB,cAAuByB,GAAYD,EAAQxB,cAAgBlD,EAAO/D,aAAe+D,EAAO6E,YAC7JH,GAAQnM,GAAG1D,KAAqB+P,GACjC1R,EAAEwK,kBAGV1D,yBAA0B,WAAA,GAClBpC,GAAOC,KACPmM,EAAQ5R,EAAE0S,SAASC,MACnBhB,EAAQ3R,EAAE,QACVoH,EAAOuK,EAAM,EACjBnM,GAAKoN,qBAAqBhB,GAC1BpM,EAAKoN,qBAAqBjB,GACtBhQ,EAAMgD,QAAQmN,SAASC,MACvBJ,EAAMkB,WAAWrO,GACjB4C,EAAK0L,oBAAoBpP,EAAY8B,EAAKyM,aAAeC,SAAS,IAClE9K,EAAK0L,oBAAoBnP,EAAW6B,EAAK2M,YAAcD,SAAS,MAGxEL,mBAAoB,SAAUkB,GAC1B,IAAItN,KAAKuN,kBAAkBD,GAA3B,CAGA,GAAIE,GAAeF,EAASnJ,IAAI,GAAG8D,MAAMwF,QACb,iBAAjBD,IACPF,EAAS9H,KAAK1G,EAAqB0O,KAG3CD,kBAAmB,SAAUD,GACzB,MAAqD,gBAAvCA,GAAS9H,KAAK1G,IAEhCqO,qBAAsB,SAAUG,GAC5B,GAAIE,GAAeF,EAAS9H,KAAK1G,EACZ,QAAjB0O,GAAyBA,IAAiBhT,GAC1C8S,EAAS5K,IAAI7D,EAAU2O,GACvBF,EAASF,WAAWtO,IAEpBwO,EAAS5K,IAAI7D,EAAU,KAG/BqM,mBAAoB,WAAA,GAMRwC,GALJ3N,EAAOC,IACXD,GAAKmG,UAAW,EAChBnG,EAAKM,QAAQc,OAAOuB,IAAI,UAAW,IACnC3C,EAAK8I,QAAQvK,GACTyB,EAAKD,QAAQoC,QACTwL,EAAY3N,EAAKiM,QAAQjM,EAAKuJ,UAAU2C,QACxCyB,GACAA,EAAU/E,YAItBW,QAAS,WAAA,GACDvJ,GAAOC,KACP2N,EAASpT,EAAEsC,GAAS+Q,OAAO,WAAA,GACvBC,GAAMtT,EAAEyF,MACR8N,EAAS/N,EAAKiM,QAAQ6B,GACtB/N,EAAUgO,GAAUA,EAAOhO,OAC/B,OAAOA,IAAWA,EAAQoC,OAASnC,EAAKD,QAAQU,UAAYV,EAAQU,UAAYV,EAAQW,SAAWoN,EAAInN,GAAG7C,KAC3GkQ,KAAK,SAAUC,EAAGC,GACjB,OAAQ1T,EAAEyT,GAAGtL,IAAI,WAAanI,EAAE0T,GAAGvL,IAAI,WAG3C,OADA3C,GAAO,KACA4N,GAEX3B,QAAS,SAAUnM,GAAV,GACD2B,GAAU3B,EAAQ6B,SAASzE,GAC3BiR,EAAShS,EAAMiS,eAAe3M,EAClC,OAAI0M,GACOA,EAEJ1T,GAEXiH,QAAS,WACL,GAAI1B,GAAOC,IACXD,GAAKqO,WACLhS,EAAO6D,GAAGwB,QAAQvB,KAAKH,GACvBA,EAAKM,QAAQwB,SACb9B,EAAKM,QAAUN,EAAKF,QAAUtF,KAElC6T,SAAU,WAAA,GACFrO,GAAOC,KACPqO,EAAK,IAAM1R,CACfoD,GAAKM,QAAQiO,IAAID,GACjBtO,EAAKF,QAAQyO,IAAID,GACjBtO,EAAKM,QAAQsG,KAAK7J,EAAa,IAAMQ,EAAe,MAAQC,GAAS+Q,IAAID,GACzEtO,EAAK8K,uBAETtJ,MAAO,SAAUI,GACb,GAAI5B,GAAOC,KAAMK,EAAUN,EAAKM,QAASP,EAAUC,EAAKD,QAAS0E,EAAWnE,EAAQqB,SAASrE,GAAkBkE,EAAQiD,EAAS9C,SAAStE,GAAemR,EAAcrS,EAAMsS,WAAW7M,EACvL,OAAKgJ,WAAUlI,QAGXd,KAAS,GACT6C,EAAS3C,SACTxB,EAAQ+C,SAASjG,KAEZqH,EAAS/B,SACV+B,EAAWjK,EAAEmB,EAAU8I,SAAS1E,IAAU2O,UAAUpO,GACpDkB,EAAQiD,EAAS9C,SAAStE,GAC1BiD,EAAQ4B,YAAY9E,IAExBoE,EAAMI,KAAK4M,IAEfxO,EAAKD,QAAQyB,MAAQgN,EACdxO,GAdIwB,EAAMI,QAgBrBH,QAAS,SAAUG,EAAM6D,GACrB,GAAIzF,GAAOC,KAAMwB,EAAUzB,EAAKM,QAAQqB,SAASzE,EACjD,OAAKxC,GAAQkH,IAGb3B,KAAK0O,QAAQ,UAAW,WACpB,OAASC,SAAUnN,EAAQE,cAE/BxF,EAAMuF,QAAQD,EAAQE,YACtBF,EAAQG,KAAKA,GACb3B,KAAK0O,QAAQ,UAAW,WAAA,GAEXrM,GADL2L,IACJ,KAAS3L,EAAIb,EAAQiB,SAAUJ,GAAK,GAChC2L,EAAEY,MAAOC,SAAUrJ,GAEvB,QACImJ,SAAUnN,EAAQE,WAClB8D,KAAMwI,KAGdjO,EAAKD,QAAQ0B,QAAUG,EAChB5B,GAlBIyB,EAAQG,QAoBvBoH,aAAc,WACN/I,KAAKc,eACLd,KAAK8O,OAAO9O,KAAKc,eAErBd,KAAKiB,YAAY8N,QAErBD,OAAQ,SAAU7J,GACVA,GACAA,EAAK+J,SAGbC,QACIjR,EACAG,EACAC,EACAC,EACAC,GAEJwB,SACIyB,MAAO,GACP0F,aAAc,YACdrF,WACAM,OAAO,EACPZ,KAAM,OACNgB,MAAO,KACPC,OAAQ,KACRO,SAAU,EACVG,UAAW,EACXF,SAAU5H,EAAAA,EACV+H,UAAW/H,EAAAA,EACXqG,QAAS,KACTf,QAAS,KACTD,SAAUzC,EACVoI,UAAU,KAGd+I,GAASxP,GAAWC,QACpBG,SACIqP,KAAM,SACN/P,UAAY0G,MAAO,WAG3B5J,GAAMG,GAAG+S,OAAOF,IACZvT,EAAW+D,GAAWC,QACtBQ,MAAO,SAAUN,EAASC,GACtB,GAAIC,GAAOC,IACXD,GAAKY,gBAAkBjF,EAAU2T,aACjCvP,EAAQgB,cAAgB,KACxBf,EAAKuP,iBAAiBzP,GACtBH,GAAWO,GAAGE,MAAMD,KAAKH,EAAMF,EAASC,GACxCC,EAAKwP,KAAKjR,EAAM9B,EAAMuD,EAAK0B,QAAS1B,IACpCA,EAAKyP,mBACLzP,EAAK0P,cAETH,iBAAkB,SAAUzP,GACxB,GAAIoF,GAAO1K,EAAEsF,EACRoF,GAAKQ,KAAK,OACXR,EAAKQ,KAAK,KAAMvJ,EAAMqK,OAAS,aAGvCiJ,iBAAkB,WACdxP,KAAKK,QAAQoF,KAAK,mBAAoBzF,KAAKH,QAAQ4F,KAAK,QAE5DgK,WAAY,WACR,GAAIlI,GAAIvH,KAAKF,OACbE,MAAKc,cAAgBd,KAAK0P,oBACtB1P,KAAKc,eAAiByG,EAAE9G,SAAW8G,EAAErF,OACrClC,KAAK+I,gBAGb2G,kBAAmB,WACf,MAAO1P,MAAKK,QAAQsG,KAAKrJ,EAAe,MAAQC,GAAS,IAE7DuC,SACIyB,MAAOpF,OAAOwT,SAASC,KACvBzJ,UAAU,EACV/G,SAAUA,MAGdxD,EAAQD,EAASgE,QACjBQ,MAAO,SAAUN,EAASC,GACtB,GAAIC,GAAOC,IACXrE,GAASsE,GAAGE,MAAMD,KAAKH,EAAMF,EAASC,GACtCC,EAAKM,QAAQ+C,SAAS5F,IAE1BsC,SACIqP,KAAM,QACNjN,OAAO,EACPN,UAAYyF,KAAM,4BAG1BnL,EAAMG,GAAG+S,OAAOxT,GACZC,EAAa,SAAUwL,GACvB,MAAO9M,GAAEmB,EAAUmU,OAAOhU,YAAa2F,QAAS6F,IAAQ7B,KAAK,cAAcwD,QAE3ElN,EAAUH,EAASgE,QACnBQ,MAAO,SAAUN,EAASC,GACtB,GAAIC,GAAOC,IACXrE,GAASsE,GAAGE,MAAMD,KAAKH,EAAMF,EAASC,GACtCC,EAAKM,QAAQ+C,SAAS3F,GACtBsC,EAAK+P,OAASvV,EAAEwV,YAEpBjQ,SACIqP,KAAM,UACNjN,OAAO,EACPN,UAEQyF,KAAM,uBACN2I,SAAS,EACT5I,OAAQ,SAAU/L,GACdA,EAAEqN,OAAOoH,OAAOG,aAIpB5I,KAAM,uBACND,OAAQ,SAAU/L,GACdA,EAAEqN,OAAOoH,OAAOI,eAMpChU,EAAMG,GAAG+S,OAAOtT,GACZC,EAAe,SAAUsL,GACzB,GAAI8I,GAAgB5V,EAAEmB,EAAU0U,SAASrU,cAAeyF,QAAS6F,IAAQ7B,KAAK,gBAAgBwD,MAC9F,OAAOmH,GAAcL,QAErB9T,EAASL,EAASgE,QAClBQ,MAAO,SAAUN,EAASC,GACtB,GAAIC,GAAOC,IACXrE,GAASsE,GAAGE,MAAMD,KAAKH,EAAMF,EAASC,GACtCC,EAAKM,QAAQ+C,SAAS1F,GACtBqC,EAAKsQ,gBACLtQ,EAAK+P,OAASvV,EAAEwV,YAEpBM,cAAe,WACX,GAAIzV,GAAQoF,KAAKF,QAAQlF,MAAO0V,EAAkB/V,EAAEmB,EAAU6U,qBAAqBvQ,KAAKF,UAAU0Q,YAAYxQ,KAAKH,QAC/GjF,IACA0V,EAAgB5O,SAAS/D,GAAU8S,IAAI7V,GAE3CoF,KAAKc,cAAgBd,KAAK0P,oBAC1B1P,KAAK+I,gBAET2G,kBAAmB,WACf,MAAO1P,MAAKK,QAAQsG,KAAKhJ,GAAU,IAEvCmC,SACIqP,KAAM,SACNjN,OAAO,EACPtH,MAAO,GACPgH,UAEQyF,KAAM,uBACN2I,SAAS,EACT5I,OAAQ,SAAU/L,GACd,GAAIqN,GAASrN,EAAEqN,OAAQ9N,EAAQ8N,EAAOrI,QAAQsG,KAAKhJ,GAAU8S,KAC7D/H,GAAOoH,OAAOG,QAAQrV,MAI1ByM,KAAM,uBACND,OAAQ,SAAU/L,GACd,GAAIqN,GAASrN,EAAEqN,OAAQ9N,EAAQ8N,EAAOrI,QAAQsG,KAAKhJ,GAAU8S,KAC7DpV,GAAEqN,OAAOoH,OAAOI,OAAOtV,SAM3CsB,EAAMG,GAAG+S,OAAOpT,GACZC,EAAc,SAAUoL,EAAMzM,GAC9B,GAAI8V,GAAenW,EAAEmB,EAAUiV,QAAQ1U,aACnCuF,QAAS6F,EACTzM,MAAOA,IACR4K,KAAK,eAAewD,MACvB,OAAO0H,GAAaZ,QAExBpU,GACI2E,QAAS5D,EAAS,4DAClB2K,OAAQ3K,EAAS,wGACjB+H,SAAU/H,EAAS,kLACnBqJ,MAAOrJ,EAAS,0OAChB4H,UAAW5H,EAAS,gGACpBiI,QAAS,4BACT2K,aAAc5S,EAAS,iEACvBoT,MAAO,UACPO,QAAS,UACTO,OAAQ,UACRJ,qBAAsB9T,EAAS,2JAEnCP,EAAM2T,MAAQhU,EACdK,EAAMkU,QAAUrU,EAChBG,EAAMyU,OAAS1U,GACjBE,OAAOD,MAAM0U,QACRzU,OAAOD,OACE,kBAAV5B,SAAwBA,OAAOuW,IAAMvW,OAAS,SAAUwW,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dialog.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.dialog', [\n        'kendo.core',\n        'kendo.popup'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dialog',\n        name: 'Dialog',\n        category: 'web',\n        description: 'The dialog widget is a modal popup that brings information to the user.',\n        depends: [\n            'core',\n            'popup'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, TabKeyTrap = kendo.ui.Popup.TabKeyTrap, proxy = $.proxy, template = kendo.template, keys = kendo.keys, isFunction = $.isFunction, NS = 'kendoWindow', KDIALOG = '.k-dialog', KWINDOW = '.k-window', KICONCLOSE = '.k-dialog-close', KCONTENTCLASS = 'k-content k-window-content k-dialog-content', KCONTENTSELECTOR = '.k-window-content', KCONTENT = '.k-content', KSCROLL = 'k-scroll', KTITLELESS = 'k-dialog-titleless', KDIALOGTITLE = '.k-dialog-title', KDIALOGTITLEBAR = KDIALOGTITLE + 'bar', KBUTTONGROUP = '.k-dialog-buttongroup', KBUTTON = '.k-button', KALERT = 'k-alert', KCONFIRM = 'k-confirm', KPROMPT = 'k-prompt', KTEXTBOX = '.k-textbox', KOVERLAY = '.k-overlay', VISIBLE = ':visible', ZINDEX = 'zIndex', BODY = 'body', INITOPEN = 'initOpen', TOUCHSTART = 'touchstart', TOUCHMOVE = 'touchmove', OPEN = 'open', CLOSE = 'close', SHOW = 'show', HIDE = 'hide', WIDTH = 'width', SIZE = {\n                small: 'k-window-sm',\n                medium: 'k-window-md',\n                large: 'k-window-lg'\n            }, HIDDEN = 'hidden', OVERFLOW = 'overflow', DATADOCOVERFLOWRULE = 'original-overflow-rule', DATAHTMLTAPYRULE = 'tap-y', HUNDREDPERCENT = 100, CSSFLEXBOX = kendo.support.cssFlexbox, messages = {\n                okText: 'OK',\n                cancel: 'Cancel',\n                promptInput: 'Input'\n            }, ceil = Math.ceil, templates, overlaySelector = ':not(link,meta,script,style)';\n        function defined(x) {\n            return typeof x != 'undefined';\n        }\n        function constrain(value, low, high) {\n            return Math.max(Math.min(parseInt(value, 10), high === Infinity ? high : parseInt(high, 10)), parseInt(low, 10));\n        }\n        function buttonKeyTrigger(e) {\n            return e.keyCode == keys.ENTER || e.keyCode == keys.SPACEBAR;\n        }\n        var DialogBase = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                that._init(that.element, that.options);\n                kendo.notify(that);\n            },\n            _init: function (element, options) {\n                var that = this, wrapper;\n                that._centerCallback = proxy(that._center, that);\n                that.appendTo = $(BODY);\n                if (!defined(options.visible) || options.visible === null) {\n                    options.visible = element.is(VISIBLE);\n                }\n                if (that.wrapperTemplate === undefined) {\n                    that.wrapperTemplate = templates.wrapper;\n                }\n                that._createDialog();\n                wrapper = that.wrapper = element.closest(KDIALOG);\n                if (options._defaultFocus === undefined) {\n                    that._defaultFocus = element[0];\n                }\n                that._tabindex(element);\n                that._dimensions();\n                this._tabKeyTrap = new TabKeyTrap(wrapper);\n                if (!that.options.visible) {\n                    that.wrapper.hide();\n                } else {\n                    that._triggerOpen();\n                }\n            },\n            setOptions: function (options) {\n                var that = this;\n                var sizeClass = that.options.size;\n                options = $.extend(that.options, options);\n                Widget.fn.setOptions.call(that, options);\n                if (options.title !== undefined) {\n                    that.title(options.title);\n                }\n                if (options.content) {\n                    kendo.destroy(that.element.children());\n                    that.element.html(options.content);\n                }\n                if (options.actions) {\n                    that.wrapper.children(KBUTTONGROUP).remove();\n                    that._createActionbar(that.wrapper);\n                }\n                that.wrapper.show();\n                that._closable(that.wrapper);\n                that.wrapper.removeClass(SIZE[sizeClass]);\n                that._dimensions();\n                if (!options.visible) {\n                    that.wrapper.hide();\n                } else {\n                    that._triggerOpen();\n                }\n                if (typeof options.modal !== 'undefined') {\n                    var visible = that.options.visible !== false;\n                    that._enableDocumentScrolling();\n                    that._overlay(options.modal && visible);\n                }\n            },\n            _dimensions: function () {\n                var that = this, wrapper = that.wrapper, options = that.options, width = options.width, height = options.height, sizeClass = options.size, dimensions = [\n                        'minWidth',\n                        'minHeight',\n                        'maxWidth',\n                        'maxHeight'\n                    ];\n                for (var i = 0; i < dimensions.length; i++) {\n                    var value = options[dimensions[i]];\n                    if (value && value != Infinity) {\n                        wrapper.css(dimensions[i], value);\n                    }\n                }\n                this._setElementMaxHeight();\n                if (width) {\n                    if (width.toString().indexOf('%') > 0) {\n                        wrapper.width(width);\n                    } else {\n                        wrapper.outerWidth(constrain(width, options.minWidth, options.maxWidth));\n                    }\n                }\n                if (height) {\n                    if (height.toString().indexOf('%') > 0) {\n                        wrapper.height(height);\n                    } else {\n                        wrapper.outerHeight(constrain(height, options.minHeight, options.maxHeight));\n                    }\n                    this._setElementHeight();\n                }\n                if (sizeClass && SIZE[sizeClass]) {\n                    wrapper.addClass(SIZE[sizeClass]);\n                }\n            },\n            _setElementMaxHeight: function () {\n                var that = this, element = that.element, maxHeight = that.options.maxHeight, paddingBox, elementMaxHeight;\n                if (maxHeight != Infinity) {\n                    paddingBox = that._paddingBox(element);\n                    elementMaxHeight = parseFloat(maxHeight, 10) - that._uiHeight() - paddingBox.vertical;\n                    if (elementMaxHeight > 0) {\n                        element.css({ maxHeight: ceil(elementMaxHeight) + 'px' });\n                    }\n                }\n            },\n            _paddingBox: function (element) {\n                var paddingTop = parseFloat(element.css('padding-top'), 10), paddingLeft = parseFloat(element.css('padding-left'), 10), paddingBottom = parseFloat(element.css('padding-bottom'), 10), paddingRight = parseFloat(element.css('padding-right'), 10);\n                return {\n                    vertical: paddingTop + paddingBottom,\n                    horizontal: paddingLeft + paddingRight\n                };\n            },\n            _setElementHeight: function () {\n                var that = this, element = that.element, height = that.options.height, paddingBox = that._paddingBox(element), elementHeight = parseFloat(height, 10) - that._uiHeight() - paddingBox.vertical;\n                if (elementHeight < 0) {\n                    elementHeight = 0;\n                }\n                element.css({ height: ceil(elementHeight) + 'px' });\n                this._applyScrollClassName(element);\n            },\n            _applyScrollClassName: function (element) {\n                var hasScroll = element.get(0).scrollHeight > element.outerHeight();\n                if (hasScroll) {\n                    element.addClass(KSCROLL);\n                } else {\n                    element.removeClass(KSCROLL);\n                }\n            },\n            _uiHeight: function () {\n                var that = this, wrapper = that.wrapper, actionbar = wrapper.children(KBUTTONGROUP), actionbarHeight = actionbar[0] && actionbar[0].offsetHeight || 0, titlebar = wrapper.children(KDIALOGTITLEBAR), titlebarHeight = titlebar[0] && titlebar[0].offsetHeight || 0;\n                return actionbarHeight + titlebarHeight;\n            },\n            _overlay: function (visible) {\n                var overlay = this.appendTo.children(KOVERLAY), wrapper = this.wrapper;\n                if (!overlay.length) {\n                    overlay = $(templates.overlay);\n                }\n                overlay.insertBefore(wrapper[0]).toggle(visible).css(ZINDEX, parseInt(wrapper.css(ZINDEX), 10) - 1);\n                if (visible) {\n                    this._waiAriaOverlay();\n                } else {\n                    this._removeWaiAriaOverlay();\n                }\n                if (this.options.modal.preventScroll) {\n                    this._stopDocumentScrolling();\n                }\n                return overlay;\n            },\n            _waiAriaOverlay: function () {\n                var node = this.wrapper;\n                this._overlayedNodes = node.prevAll(overlaySelector).add(node.nextAll(overlaySelector)).each(function () {\n                    var jthis = $(this);\n                    jthis.data('ariaHidden', jthis.attr('aria-hidden'));\n                    jthis.attr('aria-hidden', 'true');\n                });\n            },\n            _removeWaiAriaOverlay: function () {\n                return this._overlayedNodes && this._overlayedNodes.each(function () {\n                    var node = $(this);\n                    var hiddenValue = node.data('ariaHidden');\n                    if (hiddenValue) {\n                        node.attr('aria-hidden', hiddenValue);\n                    } else {\n                        node.removeAttr('aria-hidden');\n                    }\n                });\n            },\n            _closeClick: function (e) {\n                e.preventDefault();\n                this.close(false);\n            },\n            _closeKeyHandler: function (e) {\n                if (buttonKeyTrigger(e) || e.keyCode == keys.ESC) {\n                    this.close(false);\n                }\n            },\n            _keydown: function (e) {\n                var that = this, options = that.options, keyCode = e.keyCode;\n                if (keyCode == keys.ESC && !that._closing && options.closable) {\n                    that.close(false);\n                }\n            },\n            _createDialog: function () {\n                var that = this, content = that.element, options = that.options, isRtl = kendo.support.isRtl(content), titlebar = $(templates.titlebar(options)), titleId = (content.id || kendo.guid()) + '_title', wrapper = $(that.wrapperTemplate(options));\n                wrapper.toggleClass('k-rtl', isRtl);\n                content.addClass(KCONTENTCLASS);\n                that.appendTo.append(wrapper);\n                if (options.title !== false) {\n                    wrapper.append(titlebar);\n                    titlebar.attr('id', titleId);\n                    wrapper.attr('aria-labelledby', titleId);\n                } else {\n                    wrapper.addClass(KTITLELESS);\n                }\n                that._closable(wrapper);\n                wrapper.append(content);\n                if (options.content) {\n                    kendo.destroy(content.children());\n                    content.html(options.content);\n                }\n                if (options.actions.length) {\n                    that._createActionbar(wrapper);\n                }\n            },\n            _closable: function (wrapper) {\n                var that = this;\n                var options = that.options;\n                var titlebar = wrapper.children(KDIALOGTITLEBAR);\n                var titlebarActions = titlebar.find('.k-window-actions');\n                var closeAction = titlebarActions.length ? titlebarActions.find('.k-dialog-close') : wrapper.find('.k-dialog-close');\n                closeAction.remove();\n                if (options.closable !== false) {\n                    if (options.title !== false && titlebarActions.length) {\n                        titlebarActions.append(templates.close(options));\n                    } else {\n                        wrapper.prepend(templates.close(options));\n                    }\n                    wrapper.autoApplyNS(NS);\n                    that.element.autoApplyNS(NS);\n                    wrapper.find(KICONCLOSE).on('click', proxy(that._closeClick, that)).on('keydown', proxy(that._closeKeyHandler, that));\n                    that.element.on('keydown', proxy(that._keydown, that));\n                }\n            },\n            _createActionbar: function (wrapper) {\n                var isStretchedLayout = this.options.buttonLayout === 'stretched';\n                var buttonLayout = isStretchedLayout ? 'stretched' : 'normal';\n                var actionbar = $(templates.actionbar({ buttonLayout: buttonLayout }));\n                this._addButtons(actionbar);\n                if (isStretchedLayout && !CSSFLEXBOX) {\n                    this._normalizeButtonSize(actionbar);\n                }\n                wrapper.append(actionbar);\n            },\n            _addButtons: function (actionbar) {\n                var that = this, o = that.options, actionClick = proxy(that._actionClick, that), actionKeyHandler = proxy(that._actionKeyHandler, that), actions = that.options.actions, length = actions.length, buttonSize = Math.round(HUNDREDPERCENT / length), action, text;\n                for (var i = 0; i < length; i++) {\n                    action = actions[i];\n                    text = that._mergeTextWithOptions(action);\n                    var btn = $(templates.action(action)).autoApplyNS(NS).html(text).appendTo(actionbar).data('action', action.action).on('click', actionClick).on('keydown', actionKeyHandler);\n                    if (o.buttonLayout === 'stretched' && !CSSFLEXBOX) {\n                        if (i == length - 1) {\n                            buttonSize = HUNDREDPERCENT - i * buttonSize;\n                        }\n                        btn.css(WIDTH, buttonSize + '%');\n                    }\n                }\n            },\n            _mergeTextWithOptions: function (action) {\n                var text = action.text;\n                return text ? template(text)(this.options) : '';\n            },\n            _normalizeButtonSize: function (actionbar) {\n                var that = this, options = that.options, lastButton = actionbar.children(KBUTTON + ':last'), currentSize = parseFloat(lastButton[0] ? lastButton[0].style[WIDTH] : 0), difference = HUNDREDPERCENT - options.actions.length * currentSize;\n                if (difference > 0) {\n                    lastButton.css(WIDTH, currentSize + difference + '%');\n                }\n            },\n            _tabindex: function (target) {\n                var that = this;\n                var wrapper = that.wrapper;\n                var closeBtn = wrapper.find(KICONCLOSE);\n                var actionButtons = wrapper.find(KBUTTONGROUP + ' ' + KBUTTON);\n                Widget.fn._tabindex.call(this, target);\n                var tabIndex = target.attr('tabindex');\n                closeBtn.attr('tabIndex', tabIndex);\n                actionButtons.attr('tabIndex', tabIndex);\n            },\n            _actionClick: function (e) {\n                if (this.wrapper.is(VISIBLE)) {\n                    this._runActionBtn(e.currentTarget);\n                }\n            },\n            _actionKeyHandler: function (e) {\n                if (buttonKeyTrigger(e)) {\n                    this._runActionBtn(e.currentTarget);\n                } else if (e.keyCode == keys.ESC) {\n                    this.close(false);\n                }\n            },\n            _runActionBtn: function (target) {\n                var that = this;\n                if (that._closing) {\n                    return;\n                }\n                var action = $(target).data('action'), preventClose = isFunction(action) && action({ sender: that }) === false;\n                if (!preventClose) {\n                    that.close(false);\n                }\n            },\n            _triggerOpen: function () {\n                var that = this;\n                var options = that.options;\n                var wrapper = that.wrapper;\n                that.toFront();\n                that._triggerInitOpen();\n                that.trigger(OPEN);\n                if (options.modal) {\n                    that._overlay(wrapper.is(VISIBLE)).css({ opacity: 0.5 });\n                    that._focusDialog();\n                }\n            },\n            open: function () {\n                var that = this, wrapper = that.wrapper, showOptions = this._animationOptions(OPEN), options = that.options, overlay, otherModalsVisible;\n                this._triggerInitOpen();\n                if (!that.trigger(OPEN)) {\n                    if (that._closing) {\n                        wrapper.kendoStop(true, true);\n                    }\n                    that._closing = false;\n                    that.toFront();\n                    options.visible = true;\n                    if (options.modal) {\n                        otherModalsVisible = !!that._modals().length;\n                        overlay = that._overlay(otherModalsVisible);\n                        overlay.kendoStop(true, true);\n                        if (showOptions.duration && kendo.effects.Fade && !otherModalsVisible) {\n                            var overlayFx = kendo.fx(overlay).fadeIn();\n                            overlayFx.duration(showOptions.duration || 0);\n                            overlayFx.endValue(0.5);\n                            overlayFx.play();\n                        } else {\n                            overlay.css('opacity', 0.5);\n                        }\n                        overlay.show();\n                    }\n                    wrapper.show().kendoStop().kendoAnimate({\n                        effects: showOptions.effects,\n                        duration: showOptions.duration,\n                        complete: proxy(that._openAnimationEnd, that)\n                    });\n                    wrapper.show();\n                }\n                return that;\n            },\n            _animationOptions: function (id) {\n                var animation = this.options.animation;\n                var basicAnimation = {\n                    open: { effects: {} },\n                    close: {\n                        hide: true,\n                        effects: {}\n                    }\n                };\n                return animation && animation[id] || basicAnimation[id];\n            },\n            _openAnimationEnd: function () {\n                if (this.options.modal) {\n                    this._focusDialog();\n                }\n                this.trigger(SHOW);\n            },\n            _triggerInitOpen: function () {\n                if (!defined(this._initOpenTriggered)) {\n                    this._initOpenTriggered = true;\n                    this.trigger(INITOPEN);\n                }\n            },\n            toFront: function () {\n                var that = this, wrapper = that.wrapper, zIndex = +wrapper.css(ZINDEX), originalZIndex = zIndex;\n                that.center();\n                $(KWINDOW).each(function (i, element) {\n                    var windowObject = $(element), zIndexNew = windowObject.css(ZINDEX);\n                    if (!isNaN(zIndexNew)) {\n                        zIndex = Math.max(+zIndexNew, zIndex);\n                    }\n                });\n                if (!wrapper[0].style.zIndex || originalZIndex < zIndex) {\n                    wrapper.css(ZINDEX, zIndex + 2);\n                }\n                that.element.find('> .k-overlay').remove();\n                wrapper = null;\n                return that;\n            },\n            close: function (systemTriggered) {\n                if (!arguments.length) {\n                    systemTriggered = true;\n                }\n                this._close(systemTriggered);\n                this._stopCenterOnResize();\n                return this;\n            },\n            _close: function (systemTriggered) {\n                var that = this, wrapper = that.wrapper, options = that.options, showOptions = this._animationOptions('open'), hideOptions = this._animationOptions('close');\n                if (wrapper.is(VISIBLE) && !that.trigger(CLOSE, { userTriggered: !systemTriggered })) {\n                    if (that._closing) {\n                        return;\n                    }\n                    that._closing = true;\n                    options.visible = false;\n                    this._removeOverlay();\n                    wrapper.kendoStop().kendoAnimate({\n                        effects: hideOptions.effects || showOptions.effects,\n                        reverse: hideOptions.reverse === true,\n                        duration: hideOptions.duration,\n                        complete: proxy(this._closeAnimationEnd, this)\n                    });\n                }\n                return that;\n            },\n            center: function () {\n                this._center();\n                this._centerOnResize();\n            },\n            _center: function () {\n                var that = this, wrapper = that.wrapper, documentWindow = $(window), scrollTop = 0, scrollLeft = 0, newLeft = scrollLeft + Math.max(0, (documentWindow.width() - wrapper.width()) / 2), newTop = scrollTop + Math.max(0, (documentWindow.height() - wrapper.height() - parseInt(wrapper.css('paddingTop'), 10)) / 2);\n                wrapper.css({\n                    left: newLeft,\n                    top: newTop\n                });\n                return that;\n            },\n            _centerOnResize: function () {\n                if (this._trackResize) {\n                    return;\n                }\n                kendo.onResize(this._centerCallback);\n                this._trackResize = true;\n            },\n            _stopCenterOnResize: function () {\n                kendo.unbindResize(this._centerCallback);\n                this._trackResize = false;\n            },\n            _removeOverlay: function () {\n                var modals = this._modals();\n                var options = this.options;\n                var hideOverlay = options.modal && !modals.length;\n                if (hideOverlay) {\n                    this._overlay(false).remove();\n                    if (options.modal.preventScroll) {\n                        this._enableDocumentScrolling();\n                    }\n                } else if (modals.length) {\n                    this._object(modals.last())._overlay(true);\n                    if (options.modal.preventScroll) {\n                        this._stopDocumentScrolling();\n                    }\n                }\n            },\n            _stopDocumentScrolling: function () {\n                var that = this;\n                var $body = $('body');\n                that._storeOverflowRule($body);\n                $body.css(OVERFLOW, HIDDEN);\n                var $html = $('html');\n                var html = $html[0];\n                that._storeOverflowRule($html);\n                $html.css(OVERFLOW, HIDDEN);\n                if (kendo.support.mobileOS.ios) {\n                    html.addEventListener(TOUCHSTART, that._touchStart, { passive: false });\n                    html.addEventListener(TOUCHMOVE, that._touchMove, { passive: false });\n                }\n            },\n            _touchStart: function (e) {\n                $(this).data(DATAHTMLTAPYRULE, e.changedTouches[0].pageY);\n            },\n            _touchMove: function (e) {\n                var target = e.target;\n                var $target = $(e.target);\n                var upScroll = e.changedTouches[0].pageY - $(this).data(DATAHTMLTAPYRULE) > 0;\n                var preventYScroll = $target.is(KCONTENTSELECTOR) && (upScroll && $target.scrollTop() === 0) || !upScroll && $target.scrollTop() === target.scrollHeight - target.clientHeight;\n                if (!$target.is(KCONTENTSELECTOR) || preventYScroll) {\n                    e.preventDefault();\n                }\n            },\n            _enableDocumentScrolling: function () {\n                var that = this;\n                var $body = $(document.body);\n                var $html = $('html');\n                var html = $html[0];\n                that._restoreOverflowRule($body);\n                that._restoreOverflowRule($html);\n                if (kendo.support.mobileOS.ios) {\n                    $html.removeData(DATAHTMLTAPYRULE);\n                    html.removeEventListener(TOUCHSTART, that._touchStart, { passive: false });\n                    html.removeEventListener(TOUCHMOVE, that._touchMove, { passive: false });\n                }\n            },\n            _storeOverflowRule: function ($element) {\n                if (this._isOverflowStored($element)) {\n                    return;\n                }\n                var overflowRule = $element.get(0).style.overflow;\n                if (typeof overflowRule === 'string') {\n                    $element.data(DATADOCOVERFLOWRULE, overflowRule);\n                }\n            },\n            _isOverflowStored: function ($element) {\n                return typeof $element.data(DATADOCOVERFLOWRULE) === 'string';\n            },\n            _restoreOverflowRule: function ($element) {\n                var overflowRule = $element.data(DATADOCOVERFLOWRULE);\n                if (overflowRule !== null && overflowRule !== undefined) {\n                    $element.css(OVERFLOW, overflowRule);\n                    $element.removeData(DATADOCOVERFLOWRULE);\n                } else {\n                    $element.css(OVERFLOW, '');\n                }\n            },\n            _closeAnimationEnd: function () {\n                var that = this;\n                that._closing = false;\n                that.wrapper.hide().css('opacity', '');\n                that.trigger(HIDE);\n                if (that.options.modal) {\n                    var lastModal = that._object(that._modals().last());\n                    if (lastModal) {\n                        lastModal.toFront();\n                    }\n                }\n            },\n            _modals: function () {\n                var that = this;\n                var zStack = $(KWINDOW).filter(function () {\n                    var dom = $(this);\n                    var object = that._object(dom);\n                    var options = object && object.options;\n                    return options && options.modal && that.options.appendTo == options.appendTo && options.visible && dom.is(VISIBLE);\n                }).sort(function (a, b) {\n                    return +$(a).css('zIndex') - +$(b).css('zIndex');\n                });\n                that = null;\n                return zStack;\n            },\n            _object: function (element) {\n                var content = element.children(KCONTENT);\n                var widget = kendo.widgetInstance(content);\n                if (widget) {\n                    return widget;\n                }\n                return undefined;\n            },\n            destroy: function () {\n                var that = this;\n                that._destroy();\n                Widget.fn.destroy.call(that);\n                that.wrapper.remove();\n                that.wrapper = that.element = $();\n            },\n            _destroy: function () {\n                var that = this;\n                var ns = '.' + NS;\n                that.wrapper.off(ns);\n                that.element.off(ns);\n                that.wrapper.find(KICONCLOSE + ',' + KBUTTONGROUP + ' > ' + KBUTTON).off(ns);\n                that._stopCenterOnResize();\n            },\n            title: function (html) {\n                var that = this, wrapper = that.wrapper, options = that.options, titlebar = wrapper.children(KDIALOGTITLEBAR), title = titlebar.children(KDIALOGTITLE), encodedHtml = kendo.htmlEncode(html);\n                if (!arguments.length) {\n                    return title.html();\n                }\n                if (html === false) {\n                    titlebar.remove();\n                    wrapper.addClass(KTITLELESS);\n                } else {\n                    if (!titlebar.length) {\n                        titlebar = $(templates.titlebar(options)).prependTo(wrapper);\n                        title = titlebar.children(KDIALOGTITLE);\n                        wrapper.removeClass(KTITLELESS);\n                    }\n                    title.html(encodedHtml);\n                }\n                that.options.title = encodedHtml;\n                return that;\n            },\n            content: function (html, data) {\n                var that = this, content = that.wrapper.children(KCONTENT);\n                if (!defined(html)) {\n                    return content.html();\n                }\n                this.angular('cleanup', function () {\n                    return { elements: content.children() };\n                });\n                kendo.destroy(content.children());\n                content.html(html);\n                this.angular('compile', function () {\n                    var a = [];\n                    for (var i = content.length; --i >= 0;) {\n                        a.push({ dataItem: data });\n                    }\n                    return {\n                        elements: content.children(),\n                        data: a\n                    };\n                });\n                that.options.content = html;\n                return that;\n            },\n            _focusDialog: function () {\n                if (this._defaultFocus) {\n                    this._focus(this._defaultFocus);\n                }\n                this._tabKeyTrap.trap();\n            },\n            _focus: function (node) {\n                if (node) {\n                    node.focus();\n                }\n            },\n            events: [\n                INITOPEN,\n                OPEN,\n                CLOSE,\n                SHOW,\n                HIDE\n            ],\n            options: {\n                title: '',\n                buttonLayout: 'stretched',\n                actions: [],\n                modal: true,\n                size: 'auto',\n                width: null,\n                height: null,\n                minWidth: 0,\n                minHeight: 0,\n                maxWidth: Infinity,\n                maxHeight: Infinity,\n                content: null,\n                visible: null,\n                appendTo: BODY,\n                closable: true\n            }\n        });\n        var Dialog = DialogBase.extend({\n            options: {\n                name: 'Dialog',\n                messages: { close: 'Close' }\n            }\n        });\n        kendo.ui.plugin(Dialog);\n        var PopupBox = DialogBase.extend({\n            _init: function (element, options) {\n                var that = this;\n                that.wrapperTemplate = templates.alertWrapper;\n                options._defaultFocus = null;\n                that._ensureContentId(element);\n                DialogBase.fn._init.call(that, element, options);\n                that.bind(HIDE, proxy(that.destroy, that));\n                that._ariaDescribedBy();\n                that._initFocus();\n            },\n            _ensureContentId: function (element) {\n                var node = $(element);\n                if (!node.attr('id')) {\n                    node.attr('id', kendo.guid() + '_k-popup');\n                }\n            },\n            _ariaDescribedBy: function () {\n                this.wrapper.attr('aria-describedby', this.element.attr('id'));\n            },\n            _initFocus: function () {\n                var o = this.options;\n                this._defaultFocus = this._chooseEntryFocus();\n                if (this._defaultFocus && o.visible && o.modal) {\n                    this._focusDialog();\n                }\n            },\n            _chooseEntryFocus: function () {\n                return this.wrapper.find(KBUTTONGROUP + ' > ' + KBUTTON)[0];\n            },\n            options: {\n                title: window.location.host,\n                closable: false,\n                messages: messages\n            }\n        });\n        var Alert = PopupBox.extend({\n            _init: function (element, options) {\n                var that = this;\n                PopupBox.fn._init.call(that, element, options);\n                that.wrapper.addClass(KALERT);\n            },\n            options: {\n                name: 'Alert',\n                modal: true,\n                actions: [{ text: '#: messages.okText #' }]\n            }\n        });\n        kendo.ui.plugin(Alert);\n        var kendoAlert = function (text) {\n            return $(templates.alert).kendoAlert({ content: text }).data('kendoAlert').open();\n        };\n        var Confirm = PopupBox.extend({\n            _init: function (element, options) {\n                var that = this;\n                PopupBox.fn._init.call(that, element, options);\n                that.wrapper.addClass(KCONFIRM);\n                that.result = $.Deferred();\n            },\n            options: {\n                name: 'Confirm',\n                modal: true,\n                actions: [\n                    {\n                        text: '#: messages.okText #',\n                        primary: true,\n                        action: function (e) {\n                            e.sender.result.resolve();\n                        }\n                    },\n                    {\n                        text: '#: messages.cancel #',\n                        action: function (e) {\n                            e.sender.result.reject();\n                        }\n                    }\n                ]\n            }\n        });\n        kendo.ui.plugin(Confirm);\n        var kendoConfirm = function (text) {\n            var confirmDialog = $(templates.confirm).kendoConfirm({ content: text }).data('kendoConfirm').open();\n            return confirmDialog.result;\n        };\n        var Prompt = PopupBox.extend({\n            _init: function (element, options) {\n                var that = this;\n                PopupBox.fn._init.call(that, element, options);\n                that.wrapper.addClass(KPROMPT);\n                that._createPrompt();\n                that.result = $.Deferred();\n            },\n            _createPrompt: function () {\n                var value = this.options.value, promptContainer = $(templates.promptInputContainer(this.options)).insertAfter(this.element);\n                if (value) {\n                    promptContainer.children(KTEXTBOX).val(value);\n                }\n                this._defaultFocus = this._chooseEntryFocus();\n                this._focusDialog();\n            },\n            _chooseEntryFocus: function () {\n                return this.wrapper.find(KTEXTBOX)[0];\n            },\n            options: {\n                name: 'Prompt',\n                modal: true,\n                value: '',\n                actions: [\n                    {\n                        text: '#: messages.okText #',\n                        primary: true,\n                        action: function (e) {\n                            var sender = e.sender, value = sender.wrapper.find(KTEXTBOX).val();\n                            sender.result.resolve(value);\n                        }\n                    },\n                    {\n                        text: '#: messages.cancel #',\n                        action: function (e) {\n                            var sender = e.sender, value = sender.wrapper.find(KTEXTBOX).val();\n                            e.sender.result.reject(value);\n                        }\n                    }\n                ]\n            }\n        });\n        kendo.ui.plugin(Prompt);\n        var kendoPrompt = function (text, value) {\n            var promptDialog = $(templates.prompt).kendoPrompt({\n                content: text,\n                value: value\n            }).data('kendoPrompt').open();\n            return promptDialog.result;\n        };\n        templates = {\n            wrapper: template('<div class=\\'k-widget k-window k-dialog\\' role=\\'dialog\\' />'),\n            action: template('<button type=\\'button\\' class=\\'k-button# if (data.primary) { # k-primary# } role=\\'button\\' #\\'></button>'),\n            titlebar: template('<div class=\\'k-window-titlebar k-dialog-titlebar k-header\\'>' + '<span class=\\'k-window-title k-dialog-title\\'>#: title #</span>' + '<div class=\\'k-window-actions k-dialog-actions\\' />' + '</div>'),\n            close: template('<a role=\\'button\\' href=\\'\\\\#\\' class=\\'k-button k-bare k-button-icon k-window-action k-dialog-action k-dialog-close\\' title=\\'#: messages.close #\\' aria-label=\\'#: messages.close #\\' tabindex=\\'-1\\'><span class=\\'k-icon k-i-close\\'></span></a>'),\n            actionbar: template('<div class=\\'k-dialog-buttongroup k-dialog-button-layout-#: buttonLayout #\\' role=\\'toolbar\\' />'),\n            overlay: '<div class=\\'k-overlay\\' />',\n            alertWrapper: template('<div class=\\'k-widget k-window k-dialog\\' role=\\'alertdialog\\' />'),\n            alert: '<div />',\n            confirm: '<div />',\n            prompt: '<div />',\n            promptInputContainer: template('<div class=\\'k-prompt-container\\'><input type=\\'text\\' class=\\'k-textbox\\' title=\\'#: messages.promptInput #\\' aria-label=\\'#: messages.promptInput #\\' /></div>')\n        };\n        kendo.alert = kendoAlert;\n        kendo.confirm = kendoConfirm;\n        kendo.prompt = kendoPrompt;\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}