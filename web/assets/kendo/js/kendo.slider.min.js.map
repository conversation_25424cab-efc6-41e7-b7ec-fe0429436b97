{"version": 3, "sources": ["kendo.slider.js"], "names": ["f", "define", "$", "undefined", "createWrapper", "options", "element", "isHorizontal", "orientationCssClass", "style", "attr", "cssClasses", "tickPlacementCssClass", "tickPlacement", "showButtons", "createButton", "type", "isRtl", "buttonCssClass", "createSliderItems", "distance", "i", "result", "count", "math", "floor", "round", "smallStep", "createTrack", "dragHandleCount", "is", "firstDragHandleTitle", "leftDragHandleTitle", "dragHandleTitle", "min", "max", "selectionStart", "value", "rightDragHandleTitle", "selectionEnd", "step", "<PERSON><PERSON><PERSON><PERSON>", "setValue", "formatValue", "replace", "kendo", "cultures", "current", "numberFormat", "calculatePrecision", "number", "precision", "split", "length", "power", "parseFloat", "pow", "parseAttr", "name", "parse", "getAttribute", "defined", "UNDEFINED", "removeFraction", "RangeSlider", "window", "Widget", "ui", "Draggable", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "extend", "format", "proxy", "isArray", "Math", "support", "pointers", "msPointers", "CHANGE", "SLIDE", "NS", "MOUSE_DOWN", "TRACK_MOUSE_DOWN", "MOUSE_UP", "TRACK_MOUSE_UP", "MOVE_SELECTION", "KEY_DOWN", "CLICK", "MOUSE_OVER", "FOCUS", "BLUR", "DRAG_HANDLE", "TRACK_SELECTOR", "TICK_SELECTOR", "STATE_SELECTED", "STATE_FOCUSED", "STATE_DEFAULT", "STATE_DISABLED", "DISABLED", "TABINDEX", "getTouches", "SliderBase", "init", "rtlDirectionSign", "that", "this", "fn", "call", "_isHorizontal", "orientation", "_isRtl", "_position", "_sizeFn", "_outerSize", "tooltip", "enabled", "Error", "_createHtml", "wrapper", "closest", "_trackDiv", "find", "_setTrackDivWidth", "_maxSelection", "_sliderItemsInit", "_reset", "_tabindex", "_keyMap", "37", "40", "39", "38", "35", "36", "33", "largeStep", "34", "notify", "events", "_distance", "_resize", "remove", "_refresh", "enable", "sizeBetweenTicks", "pixelWidths", "_calculateItemsWidth", "parent", "before", "_setItemsWidth", "_setItemsTitle", "_calculateSteps", "_setItemsLargeTick", "getSize", "dimensions", "trackDivPosition", "css", "first", "last", "items", "paddingTop", "bordersWidth", "selection", "addClass", "titleNumber", "limit", "increment", "item", "valueWithoutFraction", "makeArray", "reverse", "_values", "html", "itemsCount", "itemWidth", "trackDivSize", "pixelStep", "_roundWidths", "pixelWidthsArray", "balance", "_addAdditionalSize", "additionalSize", "parseInt", "lastItem", "val", "ceil", "splice", "pop", "_pixelSteps", "_getValueFromPosition", "mousePosition", "dragableArea", "position", "halfStep", "startPoint", "abs", "_getFormattedValue", "drag", "tooltipTemplate", "template", "_getDraggableArea", "offset", "getOffset", "left", "top", "endPoint", "inputs", "eq", "prop", "wrap", "hide", "_focus", "e", "target", "_drag", "_firstHandleDrag", "_activeHandle", "_lastHandleDrag", "_activeHandleDrag", "_updateTooltip", "_focusWithMouse", "idx", "index", "setTimeout", "focus", "_setTooltipTimeout", "_blur", "removeClass", "_removeTooltip", "_tooltipTimeout", "_clearTooltipTimeout", "clearTimeout", "tooltipDiv", "stop", "formId", "form", "_form", "on", "_formResetHandler", "setOptions", "destroy", "off", "Slide<PERSON>", "dragHandle", "_selection", "Selection", "Drag", "increaseButtonTitle", "decreaseButtonTitle", "clickHandler", "move", "mouseDownHandler", "disable", "removeAttr", "touch", "location", "pageX", "pageY", "hasClass", "_update", "dragstart", "preventDefault", "end", "document", "documentElement", "one", "_end", "sign", "newVal", "_nextValueByIndex", "_valueIndex", "_setValueInRange", "which", "timeout", "timer", "setInterval", "_clearTimer", "currentTarget", "click", "_keydown", "change", "trigger", "isNaN", "_refreshAriaAttr", "formattedValue", "_tooltipDiv", "text", "clearInterval", "keyCode", "draggable", "moveSelection", "selectionValue", "selectionDiv", "halfDragHanndle", "rtlCorrection", "bind", "sender", "owner", "_dragstart", "dragend", "dragcancel", "prototype", "_activeDragHandle", "userEvents", "cancel", "_setZIndex", "oldVal", "_createTooltip", "colloutCssClass", "wnd", "appendTo", "body", "tooltipInnerDiv", "_scrollOffset", "scrollTop", "scrollLeft", "moveTooltip", "slideParams", "x", "y", "constrainValue", "values", "_disposeAll", "noAnimation", "fadeOut", "<PERSON><PERSON><PERSON><PERSON>", "sdhOffset", "diff", "anchorSize", "margin", "viewport", "callout", "width", "height", "_flip", "_fit", "size", "viewPortEnd", "output", "maxOverflow", "plugin", "firstInput", "secondInput", "console", "warn", "from", "to", "handle", "dragSelectionStart", "dragSelectionEnd", "activeHandleDrag", "selectionStartValue", "selectionEndValue", "_value", "start", "each", "selectionStartIndex", "selectionEndIndex", "halfH<PERSON>le", "makeSelection", "selectionPosition", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,gBAAiB,qBAAsBD,IAChD,WAwqCE,MAhqCC,UAAUE,EAAGC,GAoWV,QAASC,GAAcC,EAASC,EAASC,GACrC,GAAIC,GAAsBD,EAAe,uBAAyB,qBAAsBE,EAAQJ,EAAQI,MAAQJ,EAAQI,MAAQH,EAAQI,KAAK,SAAUC,EAAaL,EAAQI,KAAK,SAAW,IAAMJ,EAAQI,KAAK,SAAW,GAAIE,EAAwB,EAOtP,OAN6B,eAAzBP,EAAQQ,cACRD,EAAwB,wBACQ,WAAzBP,EAAQQ,gBACfD,EAAwB,qBAE5BH,EAAQA,EAAQ,WAAcA,EAAQ,IAAO,GACtC,gCAAmCD,EAAsBG,EAAa,IAAOF,EAAQ,8BAAsCJ,EAAQS,YAAc,oBAAsB,IAAMF,EAAwB,iBAEhN,QAASG,GAAaV,EAASW,EAAMT,EAAcU,GAC/C,GAAIC,GAAiB,EAcrB,OAXQA,GAFJX,GACKU,GAAiB,YAARD,GAAsBC,GAAiB,YAARD,EACxB,qBAEA,oBAGT,YAARA,EACiB,kBAEA,oBAGlB,+BAAkCA,EAAO,YAAqBX,EAAQW,EAAO,eAAiB,iBAA0BX,EAAQW,EAAO,eAAiB,yBAAkCE,EAAiB,gBAEtN,QAASC,GAAkBd,EAASe,GAChC,GAAmHC,GAA/GC,EAAS,sCAAyCC,EAAQC,EAAKC,MAAMC,EAAMN,EAAWf,EAAQsB,YAAc,CAChH,KAAKN,EAAI,EAAGA,EAAIE,EAAOF,IACnBC,GAAU,oDAGd,OADAA,IAAU,QAGd,QAASM,GAAYvB,EAASC,GAC1B,GAAIuB,GAAkBvB,EAAQwB,GAAG,SAAW,EAAI,EAAGC,EAA0C,GAAnBF,EAAuBxB,EAAQ2B,oBAAsB3B,EAAQ4B,eACvI,OAAO,qHAAqIF,EAAuB,kCAAwC1B,EAAQ6B,IAAM,oBAAwB7B,EAAQ8B,IAAM,qBAAyBN,EAAkB,EAAIxB,EAAQ+B,gBAAkB/B,EAAQ6B,IAAM7B,EAAQgC,OAAShC,EAAQ6B,KAAO,cAAiBL,EAAkB,EAAI,2CAAkDxB,EAAQiC,qBAAuB,iCAAuCjC,EAAQ6B,IAAM,oBAAwB7B,EAAQ8B,IAAM,qBAAyB9B,EAAQkC,cAAgBlC,EAAQ8B,KAAO,aAAgB,IAAM,SAEnqB,QAASK,GAAKC,GACV,MAAO,UAAUJ,GACb,MAAOA,GAAQI,GAGvB,QAASC,GAASL,GACd,MAAO,YACH,MAAOA,IAGf,QAASM,GAAYN,GACjB,OAAQA,EAAQ,IAAIO,QAAQ,IAAKC,EAAMC,SAASC,QAAQC,aAAa,MAEzE,QAASC,GAAmBZ,GAA5B,GACQa,GAASb,GAAAA,EACTc,EAAY,CAMhB,OALAD,GAASA,EAAOE,MAAM,KAClBF,EAAO,KACPC,EAAYD,EAAO,GAAGG,QAE1BF,EAAYA,EAAY,GAAK,GAAKA,EAGtC,QAASzB,GAAMW,GACX,GAAIc,GAAWG,CAIf,OAHAjB,GAAQkB,WAAWlB,EAAO,IAC1Bc,EAAYF,EAAmBZ,GAC/BiB,EAAQ9B,EAAKgC,IAAI,GAAIL,GAAa,GAC3B3B,EAAKE,MAAMW,EAAQiB,GAASA,EAEvC,QAASG,GAAUnD,EAASoD,GACxB,GAAIrB,GAAQsB,EAAMrD,EAAQsD,aAAaF,GAIvC,OAHc,QAAVrB,IACAA,EAAQlC,GAELkC,EAEX,QAASwB,GAAQxB,GACb,aAAcA,KAAUyB,EAE5B,QAASC,GAAe1B,GACpB,MAAe,KAARA,EApbd,GAk4BO2B,GAj4BAnB,EAAQoB,OAAOpB,MAAOqB,EAASrB,EAAMsB,GAAGD,OAAQE,EAAYvB,EAAMsB,GAAGC,UAAWC,EAAaxB,EAAMyB,YAAaC,EAAc1B,EAAM2B,aAAcC,EAASvE,EAAEuE,OAAQC,EAAS7B,EAAM6B,OAAQf,EAAQd,EAAMU,WAAYoB,EAAQzE,EAAEyE,MAAOC,EAAU1E,EAAE0E,QAASpD,EAAOqD,KAAMC,EAAUjC,EAAMiC,QAASC,EAAWD,EAAQC,SAAUC,EAAaF,EAAQE,WAAYC,EAAS,SAAUC,EAAQ,QAASC,EAAK,UAAWC,EAAa,aAAeD,EAAK,aAAeA,EAAIE,EAAmBN,EAAW,cAAgBI,EAAKH,EAAa,gBAAkBG,EAAKC,EAAYE,EAAW,WAAaH,EAAK,WAAaA,EAAII,EAAiBR,EAAW,YAAcC,EAAa,cAAgBG,EAAKG,EAAUE,EAAiB,gBAAiBC,EAAW,UAAYN,EAAIO,EAAQ,QAAUP,EAAIQ,EAAa,YAAcR,EAAIS,EAAQ,QAAUT,EAAIU,EAAO,OAASV,EAAIW,EAAc,gBAAiBC,EAAiB,kBAAmBC,EAAgB,UAAWC,EAAiB,mBAAoBC,EAAgB,kBAAmBC,EAAgB,kBAAmBC,EAAiB,mBAAoBC,EAAW,WAAYvC,EAAY,YAAawC,EAAW,WAAYC,EAAa1D,EAAM0D,WACnpCC,EAAatC,EAAOO,QACpBgC,KAAM,SAAUnG,EAASD,GAAnB,GAsBEqG,GArBAC,EAAOC,IASX,IARA1C,EAAO2C,GAAGJ,KAAKK,KAAKH,EAAMrG,EAASD,GACnCA,EAAUsG,EAAKtG,QACfsG,EAAKI,cAAuC,cAAvB1G,EAAQ2G,YAC7BL,EAAKM,OAASN,EAAKI,eAAiBlE,EAAMiC,QAAQ7D,MAAMX,GACxDqG,EAAKO,UAAYP,EAAKI,cAAgB,OAAS,SAC/CJ,EAAKQ,QAAUR,EAAKI,cAAgB,QAAU,SAC9CJ,EAAKS,WAAaT,EAAKI,cAAgB1C,EAAaE,EACpDlE,EAAQgH,QAAQ3C,OAASrE,EAAQgH,QAAQC,QAAUjH,EAAQgH,QAAQ3C,QAAU,MAAQ,MACjFrE,EAAQsB,WAAa,EACrB,KAAU4F,OAAM,uDAEpBZ,GAAKa,cACLb,EAAKc,QAAUd,EAAKrG,QAAQoH,QAAQ,aACpCf,EAAKgB,UAAYhB,EAAKc,QAAQG,KAAK7B,GACnCY,EAAKkB,oBACLlB,EAAKmB,cAAgBnB,EAAKgB,UAAUhB,EAAKQ,WACzCR,EAAKoB,mBACLpB,EAAKqB,SACLrB,EAAKsB,UAAUtB,EAAKc,QAAQG,KAAK9B,IACjCa,EAAKtG,EAAQiH,QAAU,SAAW,aAC9BZ,EAAmB7D,EAAMiC,QAAQ7D,MAAM0F,EAAKc,YAAgB,EAChEd,EAAKuB,SACDC,GAAI3F,KAAUkE,EAAmBrG,EAAQsB,WACzCyG,GAAI5F,GAAMnC,EAAQsB,WAClB0G,GAAI7F,EAAK,EAAKkE,EAAmBrG,EAAQsB,WACzC2G,GAAI9F,GAAMnC,EAAQsB,WAClB4G,GAAI7F,EAASrC,EAAQ8B,KACrBqG,GAAI9F,EAASrC,EAAQ6B,KACrBuG,GAAIjG,GAAMnC,EAAQqI,WAClBC,GAAInG,GAAMnC,EAAQqI,YAEtB7F,EAAM+F,OAAOjC,IAEjBkC,QACI5D,EACAC,GAEJ7E,SACIiH,SAAS,EACTpF,IAAK,EACLC,IAAK,GACLR,UAAW,EACX+G,UAAW,EACX1B,YAAa,aACbnG,cAAe,OACfwG,SACIC,SAAS,EACT5C,OAAQ,QAGhBoE,UAAW,WACP,MAAOpH,GAAMkF,KAAKvG,QAAQ8B,IAAMyE,KAAKvG,QAAQ6B,MAEjD6G,QAAS,WACLnC,KAAKiB,oBACLjB,KAAKa,QAAQG,KAAK,mBAAmBoB,SACrCpC,KAAKkB,cAAgBlB,KAAKe,UAAUf,KAAKO,WACzCP,KAAKmB,mBACLnB,KAAKqC,WACDrC,KAAKvG,QAAQiH,SACbV,KAAKsC,QAAO,IAGpBnB,iBAAkB,WAAA,GACVpB,GAAOC,KAAMvG,EAAUsG,EAAKtG,QAC5B8I,EAAmBxC,EAAKmB,gBAAkBzH,EAAQ8B,IAAM9B,EAAQ6B,KAAO7B,EAAQsB,WAC/EyH,EAAczC,EAAK0C,qBAAqB7H,EAAKC,MAAMkF,EAAKmC,YAAczI,EAAQsB,WACrD,SAAzBtB,EAAQQ,eAA2BsI,GAAoB,IACvDjJ,EAAE0G,KAAKtG,SAASgJ,SAAS1B,KAAK,mBAAmBoB,SACjDrC,EAAKgB,UAAU4B,OAAOpI,EAAkBd,EAASsG,EAAKmC,cACtDnC,EAAK6C,eAAeJ,GACpBzC,EAAK8C,kBAET9C,EAAK+C,gBAAgBN,GACQ,QAAzB/I,EAAQQ,eAA2BsI,GAAoB,GAAK9I,EAAQqI,WAAarI,EAAQsB,WACzFgF,EAAKgD,sBAGbC,QAAS,WACL,MAAO/G,GAAMgH,WAAWjD,KAAKa,UAEjCI,kBAAmB,WACf,GAAIlB,GAAOC,KAAMkD,EAAgG,EAA7EvG,WAAWoD,EAAKgB,UAAUoC,IAAIpD,EAAKM,OAAS,QAAUN,EAAKO,WAAY,GAC3GP,GAAKgB,UAAUhB,EAAKQ,SAASR,EAAKc,QAAQd,EAAKQ,WAAa,EAAI2C,IAEpEN,eAAgB,SAAUJ,GACtB,GAA6H/H,GAAzHsF,EAAOC,KAAMvG,EAAUsG,EAAKtG,QAAS2J,EAAQ,EAAGC,EAAOb,EAAY/F,OAAS,EAAG6G,EAAQvD,EAAKc,QAAQG,KAAK5B,GAAmBmE,EAAa,EAAGC,EAAe,EAAG7I,EAAQ2I,EAAM7G,OAAQgH,EAAY,CACpM,KAAKhJ,EAAI,EAAGA,EAAIE,EAAQ,EAAGF,IACvBnB,EAAEgK,EAAM7I,EAAI,IAAIsF,EAAKQ,SAASiC,EAAY/H,GAS9C,IAPIsF,EAAKI,eACL7G,EAAEgK,EAAMF,IAAQM,SAAS,WAAW3D,EAAKQ,SAASiC,EAAYa,EAAO,IACrE/J,EAAEgK,EAAMD,IAAOK,SAAS,UAAU3D,EAAKQ,SAASiC,EAAYa,MAE5D/J,EAAEgK,EAAMD,IAAOK,SAAS,WAAW3D,EAAKQ,SAASiC,EAAYa,IAC7D/J,EAAEgK,EAAMF,IAAQM,SAAS,UAAU3D,EAAKQ,SAASiC,EAAYa,EAAO,KAEpEtD,EAAKmC,YAAczI,EAAQsB,YAAc,IAAMgF,EAAKI,cAAe,CACnE,IAAK1F,EAAI,EAAGA,EAAI+H,EAAY/F,OAAQhC,IAChCgJ,GAAajB,EAAY/H,EAE7B8I,GAAaxD,EAAKmB,cAAgBuC,EAClCF,GAAc5G,WAAWoD,EAAKgB,UAAUoC,IAAIpD,EAAKO,WAAY,IAAMkD,EACnEzD,EAAKc,QAAQG,KAAK,mBAAmBmC,IAAI,cAAeI,KAGhEV,eAAgB,WAEZ,IADA,GAAI9C,GAAOC,KAAMvG,EAAUsG,EAAKtG,QAAS6J,EAAQvD,EAAKc,QAAQG,KAAK5B,GAAgBuE,EAAclK,EAAQ6B,IAAKX,EAAQ2I,EAAM7G,OAAQhC,EAAIsF,EAAKI,gBAAkBJ,EAAKM,OAAS,EAAI1F,EAAQ,EAAGiJ,EAAQ7D,EAAKI,gBAAkBJ,EAAKM,OAAS1F,KAAYkJ,EAAY9D,EAAKI,gBAAkBJ,EAAKM,OAAS,KAC/R5F,EAAImJ,IAAU,EAAGnJ,GAAKoJ,EACzBvK,EAAEgK,EAAM7I,IAAIX,KAAK,QAASgE,EAAOrE,EAAQgH,QAAQ3C,OAAQhD,EAAM6I,KAC/DA,GAAelK,EAAQsB,WAG/BgI,mBAAoB,WAAA,GAC0Ee,GAAMrI,EAQpFsI,EARRhE,EAAOC,KAAMvG,EAAUsG,EAAKtG,QAAS6J,EAAQvD,EAAKc,QAAQG,KAAK5B,GAAgB3E,EAAI,CACvF,IAAI0C,EAAe1D,EAAQqI,WAAa3E,EAAe1D,EAAQsB,aAAe,GAAKgF,EAAKmC,YAAczI,EAAQqI,WAAa,EAIvH,IAHK/B,EAAKI,eAAkBJ,EAAKM,SAC7BiD,EAAQhK,EAAE0K,UAAUV,GAAOW,WAE1BxJ,EAAI,EAAGA,EAAI6I,EAAM7G,OAAQhC,IAC1BqJ,EAAOxK,EAAEgK,EAAM7I,IACfgB,EAAQsE,EAAKmE,QAAQzJ,GACjBsJ,EAAuBjJ,EAAMqC,EAAe1B,EAAQuE,KAAKvG,QAAQ6B,MACjEyI,EAAuB5G,EAAe1D,EAAQsB,aAAe,GAAKgJ,EAAuB5G,EAAe1D,EAAQqI,aAAe,IAC/HgC,EAAKJ,SAAS,gBAAgBS,KAAK,yBAA6BL,EAAKhK,KAAK,SAAW,WAC3E,IAANW,GAAWA,IAAM6I,EAAM7G,OAAS,GAChCqH,EAAKX,IAAI,cAAeW,EAAK/D,EAAKQ,WAAa,QAMnEkC,qBAAsB,SAAU2B,GAC5B,GAA4KC,GAAW7B,EAAa/H,EAAhMsF,EAAOC,KAAMvG,EAAUsG,EAAKtG,QAAS6K,EAAe3H,WAAWoD,EAAKgB,UAAUoC,IAAIpD,EAAKQ,UAAY,EAAG/F,EAAWuF,EAAKmC,YAAaqC,EAAYD,EAAe9J,CAMlK,KALIA,EAAWf,EAAQsB,UAAYH,EAAKC,MAAML,EAAWf,EAAQsB,WAAa,IAC1EuJ,GAAgB9J,EAAWf,EAAQsB,UAAYwJ,GAEnDF,EAAYC,EAAeF,EAC3B5B,KACK/H,EAAI,EAAGA,EAAI2J,EAAa,EAAG3J,IAC5B+H,EAAY/H,GAAK4J,CAGrB,OADA7B,GAAY4B,EAAa,GAAK5B,EAAY4B,GAAcC,EAAY,EAC7DtE,EAAKyE,aAAahC,IAE7BgC,aAAc,SAAUC,GACpB,GAAkDhK,GAA9CiK,EAAU,EAAG/J,EAAQ8J,EAAiBhI,MAC1C,KAAKhC,EAAI,EAAGA,EAAIE,EAAOF,IACnBiK,GAAWD,EAAiBhK,GAAKG,EAAKC,MAAM4J,EAAiBhK,IAC7DgK,EAAiBhK,GAAKG,EAAKC,MAAM4J,EAAiBhK,GAGtD,OADAiK,GAAU9J,EAAKE,MAAM4J,GACd1E,KAAK2E,mBAAmBD,EAASD,IAE5CE,mBAAoB,SAAUC,EAAgBH,GAC1C,GAAuB,IAAnBG,EACA,MAAOH,EAEX,IAA4HhK,GAAxHmB,EAAOe,WAAW8H,EAAiBhI,OAAS,GAAKE,WAA6B,GAAlBiI,EAAsBA,EAAiBA,EAAiB,EACxH,KAAKnK,EAAI,EAAGA,EAAImK,EAAgBnK,IAC5BgK,EAAiBI,SAASjK,EAAKE,MAAMc,EAAOnB,GAAI,MAAQ,CAE5D,OAAOgK,IAEX3B,gBAAiB,SAAUN,GACvB,GAAqKsC,GAAjK/E,EAAOC,KAAMvG,EAAUsG,EAAKtG,QAASsL,EAAMtL,EAAQ6B,IAAKmI,EAAY,EAAGjJ,EAAWuF,EAAKmC,YAAakC,EAAaxJ,EAAKoK,KAAKxK,EAAWf,EAAQsB,WAAYN,EAAI,CAMlK,IALA2J,GAAc5J,EAAWf,EAAQsB,UAAY,IAAM,EAAI,EAAI,EAC3DyH,EAAYyC,OAAO,EAAG,EAAiC,EAA9BzC,EAAY4B,EAAa,IAClD5B,EAAYyC,OAAOb,EAAa,EAAG,EAAuB,EAApB5B,EAAY0C,OAClDnF,EAAKoF,aAAe1B,GACpB1D,EAAKmE,SAAWa,GACG,IAAfX,EAAJ,CAGA,KAAO3J,EAAI2J,GACPX,IAAcjB,EAAY/H,EAAI,GAAK+H,EAAY/H,IAAM,EACrDsF,EAAKoF,YAAY1K,GAAKgJ,EACtBsB,GAAOtL,EAAQsB,UACfgF,EAAKmE,QAAQzJ,GAAKK,EAAMiK,GACxBtK,GAEJqK,GAAWtK,EAAWf,EAAQsB,YAAc,EAAIqJ,EAAa,EAAIA,EACjErE,EAAKoF,YAAYL,GAAY/E,EAAKmB,cAClCnB,EAAKmE,QAAQY,GAAYrL,EAAQ8B,IAC7BwE,EAAKM,SACLN,EAAKoF,YAAYlB,UACjBlE,EAAKmE,QAAQD,aAGrBmB,sBAAuB,SAAUC,EAAeC,GAC5C,GAA6J7K,GAAzJsF,EAAOC,KAAMvG,EAAUsG,EAAKtG,QAASmC,EAAOhB,EAAKW,IAAI9B,EAAQsB,WAAagF,EAAKmB,cAAgBnB,EAAKmC,aAAc,GAAIqD,EAAW,EAAGC,EAAW5J,EAAO,CAS1J,IARImE,EAAKI,eACLoF,EAAWF,EAAgBC,EAAaG,WACpC1F,EAAKM,SACLkF,EAAWxF,EAAKmB,cAAgBqE,IAGpCA,EAAWD,EAAaG,WAAaJ,EAErCtF,EAAKmB,eAAiB2D,SAAS9E,EAAKmB,cAAgBtF,EAAM,IAAM,GAAK,EAAI2J,EACzE,MAAO9L,GAAQ8B,GAEnB,KAAKd,EAAI,EAAGA,EAAIsF,EAAKoF,YAAY1I,OAAQhC,IACrC,GAAIG,EAAK8K,IAAI3F,EAAKoF,YAAY1K,GAAK8K,GAAY,GAAKC,EAChD,MAAO1K,GAAMiF,EAAKmE,QAAQzJ,KAItCkL,mBAAoB,SAAUZ,EAAKa,GAC/B,GAA4DC,GAAiBrK,EAAgBG,EAAzFoE,EAAOC,KAAMmE,EAAO,GAAI1D,EAAUV,EAAKtG,QAAQgH,OAmCnD,OAlCIzC,GAAQ+G,IACRvJ,EAAiBuJ,EAAI,GACrBpJ,EAAeoJ,EAAI,IACZa,GAAQA,EAAKxL,OACpBoB,EAAiBoK,EAAKpK,eACtBG,EAAeiK,EAAKjK,cAEpBiK,IACAC,EAAkBD,EAAKC,kBAEtBA,GAAmBpF,EAAQqF,WAC5BD,EAAkB5J,EAAM6J,SAASrF,EAAQqF,WAEzC9H,EAAQ+G,IAAQa,GAAQA,EAAKxL,KACzByL,EACA1B,EAAO0B,GACHrK,eAAgBA,EAChBG,aAAcA,KAGlBH,EAAiBsC,EAAO2C,EAAQ3C,OAAQtC,GACxCG,EAAemC,EAAO2C,EAAQ3C,OAAQnC,GACtCwI,EAAO3I,EAAiB,MAAQG,IAGhCiK,IACAA,EAAKb,IAAMA,GAGXZ,EADA0B,EACOA,GAAkBpK,MAAOsJ,IAEzBjH,EAAO2C,EAAQ3C,OAAQiH,IAG/BZ,GAEX4B,kBAAmB,WACf,GAAIhG,GAAOC,KAAMgG,EAAS/J,EAAMgK,UAAUlG,EAAKgB,UAC/C,QACI0E,WAAY1F,EAAKI,cAAgB6F,EAAOE,KAAOF,EAAOG,IAAMpG,EAAKmB,cACjEkF,SAAUrG,EAAKI,cAAgB6F,EAAOE,KAAOnG,EAAKmB,cAAgB8E,EAAOG,MAGjFvF,YAAa,WACT,GAAIb,GAAOC,KAAMtG,EAAUqG,EAAKrG,QAASD,EAAUsG,EAAKtG,QAAS4M,EAAS3M,EAAQsH,KAAK,QAClE,IAAjBqF,EAAO5J,QACP4J,EAAOC,GAAG,GAAGC,KAAK,QAASxK,EAAYtC,EAAQ+B,iBAC/C6K,EAAOC,GAAG,GAAGC,KAAK,QAASxK,EAAYtC,EAAQkC,gBAE/CjC,EAAQ6M,KAAK,QAASxK,EAAYtC,EAAQgC,QAE9C/B,EAAQ8M,KAAKhN,EAAcC,EAASC,EAASqG,EAAKI,gBAAgBsG,OAC9DhN,EAAQS,aACRR,EAAQiJ,OAAOxI,EAAaV,EAAS,WAAYsG,EAAKI,cAAeJ,EAAKM,SAASsC,OAAOxI,EAAaV,EAAS,WAAYsG,EAAKI,cAAeJ,EAAKM,SAEzJ3G,EAAQiJ,OAAO3H,EAAYvB,EAASC,KAExCgN,OAAQ,SAAUC,GACd,GAAI5G,GAAOC,KAAM4G,EAASD,EAAEC,OAAQ7B,EAAMhF,EAAKtE,QAASmK,EAAO7F,EAAK8G,KAC/DjB,KACGgB,GAAU7G,EAAKc,QAAQG,KAAK9B,GAAaoH,GAAG,GAAG,IAC/CV,EAAO7F,EAAK+G,iBACZ/G,EAAKgH,cAAgB,IAErBnB,EAAO7F,EAAKiH,gBACZjH,EAAKgH,cAAgB,GAEzBhC,EAAMA,EAAIhF,EAAKgH,gBAEnBzN,EAAEsN,GAAQlD,SAASpE,EAAgB,IAAMD,GACrCuG,IACA7F,EAAKkH,kBAAoBrB,EACzBA,EAAKpK,eAAiBuE,EAAKtG,QAAQ+B,eACnCoK,EAAKjK,aAAeoE,EAAKtG,QAAQkC,aACjCiK,EAAKsB,eAAenC,KAG5BoC,gBAAiB,SAAUP,GACvBA,EAAStN,EAAEsN,EACX,IAAI7G,GAAOC,KAAMoH,EAAMR,EAAO1L,GAAGgE,GAAe0H,EAAOS,QAAU,CACjEhK,QAAOiK,WAAW,WACdvH,EAAKc,QAAQG,KAAK9B,GAAoB,GAAPkI,EAAW,EAAI,GAAGG,SAClD,GACHxH,EAAKyH,sBAETC,MAAO,SAAUd,GACb,GAAI5G,GAAOC,KAAM4F,EAAO7F,EAAKkH,iBAC7B3N,GAAEqN,EAAEC,QAAQc,YAAYpI,EAAgB,IAAMD,GAC1CuG,IACAA,EAAK+B,uBACE5H,GAAKkH,wBACLlH,GAAKgH,gBAGpBS,mBAAoB,WAChB,GAAIzH,GAAOC,IACXD,GAAK6H,gBAAkBvK,OAAOiK,WAAW,WACrC,GAAI1B,GAAO7F,EAAK8G,OAAS9G,EAAKkH,iBAC1BrB,IACAA,EAAK+B,kBAEV,MAEPE,qBAAsB,WAAA,GAGdjC,GAFA7F,EAAOC,IACX3C,QAAOyK,aAAa9H,KAAK4H,iBACrBhC,EAAO7F,EAAK8G,OAAS9G,EAAKkH,kBAC1BrB,GAAQA,EAAKmC,YACbnC,EAAKmC,WAAWC,MAAK,GAAM,GAAO7E,IAAI,UAAW,IAGzD/B,OAAQ,WACJ,GAAIrB,GAAOC,KAAMtG,EAAUqG,EAAKrG,QAASuO,EAASvO,EAAQI,KAAK,QAASoO,EAAOD,EAAS3O,EAAE,IAAM2O,GAAUvO,EAAQoH,QAAQ,OACtHoH,GAAK,KACLnI,EAAKoI,MAAQD,EAAKE,GAAG,QAASrK,EAAMgC,EAAKsI,kBAAmBtI,MAGpEzE,IAAK,SAAUG,GACX,MAAKA,IAGLuE,KAAKsI,YAAahN,IAAOG,IAAzBuE,GAFWA,KAAKvG,QAAQ6B,KAI5BC,IAAK,SAAUE,GACX,MAAKA,IAGLuE,KAAKsI,YAAa/M,IAAOE,IAAzBuE,GAFWA,KAAKvG,QAAQ8B,KAI5B+M,WAAY,SAAU7O,GAClB6D,EAAO2C,GAAGqI,WAAWpI,KAAKF,KAAMvG,GAChCuG,KAAKmB,mBACLnB,KAAKqC,YAETkG,QAAS,WACDvI,KAAKmI,OACLnI,KAAKmI,MAAMK,IAAI,QAASxI,KAAKqI,mBAEjC/K,EAAO2C,GAAGsI,QAAQrI,KAAKF,SAqF3ByI,GAAS7I,EAAW/B,QACpBgC,KAAM,SAAUnG,EAASD,GACrB,GAAiBiP,GAAb3I,EAAOC,IACXtG,GAAQU,KAAO,OACfX,EAAUoE,MACNpC,MAAOoB,EAAUnD,EAAS,SAC1B4B,IAAKuB,EAAUnD,EAAS,OACxB6B,IAAKsB,EAAUnD,EAAS,OACxBqB,UAAW8B,EAAUnD,EAAS,SAC/BD,GACHC,EAAUJ,EAAEI,GACRD,GAAWA,EAAQiH,UAAYnH,IAC/BE,EAAQiH,SAAWhH,EAAQwB,GAAG,eAElC0E,EAAWK,GAAGJ,KAAKK,KAAKH,EAAMrG,EAASD,GACvCA,EAAUsG,EAAKtG,QACVwD,EAAQxD,EAAQgC,QAA4B,OAAlBhC,EAAQgC,QACnChC,EAAQgC,MAAQhC,EAAQ6B,IACxB5B,EAAQ6M,KAAK,QAASxK,EAAYtC,EAAQ6B,OAE9C7B,EAAQgC,MAAQb,EAAKW,IAAIX,EAAKU,IAAI7B,EAAQgC,MAAOhC,EAAQ8B,KAAM9B,EAAQ6B,KACvEoN,EAAa3I,EAAKc,QAAQG,KAAK9B,GAC/Bc,KAAK2I,WAAa,GAAIF,IAAOG,UAAUF,EAAY3I,EAAMtG,GACzDsG,EAAK8G,MAAQ,GAAI4B,IAAOI,KAAKH,EAAY,GAAI3I,EAAMtG,IAEvDA,SACIqD,KAAM,SACN5C,aAAa,EACb4O,oBAAqB,WACrBC,oBAAqB,WACrB1N,gBAAiB,OACjBoF,SAAW3C,OAAQ,cACnBrC,MAAO,MAEX6G,OAAQ,SAAUA,GAAV,GACqC0G,GAAcC,EAuC/CC,EAvCJnJ,EAAOC,KAAMvG,EAAUsG,EAAKtG,OAChCsG,GAAKoJ,UACD7G,KAAW,IAGfvC,EAAKc,QAAQ6G,YAAYlI,GAAgBkE,SAASnE,GAClDQ,EAAKc,QAAQG,KAAK,SAASoI,WAAW3J,GACtCuJ,EAAe,SAAUrC,GAAV,GAKPtB,GAAkFC,EAAyCsB,EAJ3HyC,EAAQ1J,EAAWgH,GAAG,EAC1B,IAAK0C,EAAL,CAIA,GADIhE,EAAgBtF,EAAKI,cAAgBkJ,EAAMC,SAASC,MAAQF,EAAMC,SAASE,MAAOlE,EAAevF,EAAKgG,oBAAqBa,EAAStN,EAAEqN,EAAEC,QACxIA,EAAO6C,SAAS,gBAEhB,MADA7C,GAAOlD,SAASpE,EAAgB,IAAMD,GACtC,CAEJU,GAAK2J,QAAQ3J,EAAKqF,sBAAsBC,EAAeC,IACvDvF,EAAKoH,gBAAgBR,EAAEC,QACvB7G,EAAK8G,MAAM8C,UAAUhD,GACrBA,EAAEiD,mBAEN7J,EAAKc,QAAQG,KAAK5B,EAAgB,KAAOD,GAAgBiJ,GAAG3J,EAAkBuK,GAAca,MAAMzB,GAAG3J,EAAkB,WACnHnF,EAAEwQ,SAASC,iBAAiBC,IAAI,cAAe/N,EAAM2N,kBACtDxB,GAAGzJ,EAAgB,WAClBoB,EAAK8G,MAAMoD,SAEflK,EAAKc,QAAQG,KAAK9B,GAAapF,KAAK4F,EAAU,GAAG0I,GAAG1J,EAAU,WAC1DqB,EAAKyH,uBACNY,GAAGtJ,EAAO,SAAU6H,GACnB5G,EAAKoH,gBAAgBR,EAAEC,QACvBD,EAAEiD,mBACHxB,GAAGpJ,EAAOjB,EAAMgC,EAAK2G,OAAQ3G,IAAOqI,GAAGnJ,EAAMlB,EAAMgC,EAAK0H,MAAO1H,IAClEkJ,EAAOlL,EAAM,SAAUmM,GACnB,GAAIC,GAASpK,EAAKqK,kBAAkBrK,EAAKsK,YAAqB,EAAPH,EACvDnK,GAAKuK,iBAAiBH,GACtBpK,EAAK8G,MAAMK,eAAeiD,IAC3BpK,GACCtG,EAAQS,cACJgP,EAAmBnL,EAAM,SAAU4I,EAAGuD,GACtClK,KAAK6H,wBACW,IAAZlB,EAAE4D,OAAerM,EAAQmL,OAAqB,IAAZ1C,EAAE4D,SACpCtB,EAAKiB,GACLlK,KAAKwK,QAAUlD,WAAWvJ,EAAM,WAC5BiC,KAAKyK,MAAQC,YAAY,WACrBzB,EAAKiB,IACN,KACJlK,MAAO,OAEfD,GACHA,EAAKc,QAAQG,KAAK,aAAaoH,GAAG1J,EAAUX,EAAM,SAAU4I,GACxD3G,KAAK2K,cACL5K,EAAKoH,gBAAgBR,EAAEC,SACxB7G,IAAOqI,GAAGrJ,EAAY,SAAU4H,GAC/BrN,EAAEqN,EAAEiE,eAAelH,SAAS,mBAC7B0E,GAAG,WAAa7J,EAAIR,EAAM,SAAU4I,GACnCrN,EAAEqN,EAAEiE,eAAelD,YAAY,iBAC/B1H,KAAK2K,eACN5K,IAAOuG,GAAG,GAAG8B,GAAG5J,EAAYT,EAAM,SAAU4I,GAC3CuC,EAAiBvC,EAAG,IACrB5G,IAAO8K,OAAM,GAAOhB,MAAMvD,GAAG,GAAG8B,GAAG5J,EAAYT,EAAM,SAAU4I,GAC9DuC,EAAiBvC,OAClB5G,IAAO8K,MAAM5O,EAAM2N,iBAE1B7J,EAAKc,QAAQG,KAAK9B,GAAasJ,IAAI3J,GAAU,GAAOuJ,GAAGvJ,EAAUd,EAAMiC,KAAK8K,SAAU/K,IACtFtG,EAAQiH,SAAU,IAEtByI,QAAS,WACL,GAAIpJ,GAAOC,IACXD,GAAKc,QAAQ6G,YAAYnI,GAAemE,SAASlE,GACjDlG,EAAEyG,EAAKrG,SAAS6M,KAAK9G,EAAUA,GAC/BM,EAAKc,QAAQG,KAAK,aAAawH,IAAIhK,GAAY4J,GAAG5J,EAAY,SAAUmI,GACpEA,EAAEiD,iBACFtQ,EAAE0G,MAAM0D,SAAS,oBAClB8E,IAAI9J,GAAU0J,GAAG1J,EAAU,SAAUiI,GACpCA,EAAEiD,iBACFtQ,EAAE0G,MAAM0H,YAAY,oBACrBc,IAAI,aAAejK,GAAI6J,GAAG,aAAe7J,EAAItC,EAAM2N,gBAAgBpB,IAAIzJ,GAAYqJ,GAAGrJ,EAAY9C,EAAM2N,gBAC3G7J,EAAKc,QAAQG,KAAK5B,EAAgB,KAAOD,GAAgBqJ,IAAI/J,GAAkB+J,IAAI7J,GACnFoB,EAAKc,QAAQG,KAAK9B,GAAapF,KAAK4F,MAAc8I,IAAI9J,GAAU8J,IAAI3J,GAAU2J,IAAI1J,GAAO0J,IAAIxJ,GAAOwJ,IAAIvJ,GACxGc,EAAKtG,QAAQiH,SAAU,GAE3BgJ,QAAS,SAAU3E,GACf,GAAIhF,GAAOC,KAAM+K,EAAShL,EAAKtE,SAAWsJ,CAC1ChF,GAAKtE,MAAMsJ,GACPgG,GACAhL,EAAKiL,QAAQ3M,GAAU5C,MAAOsE,EAAKtG,QAAQgC,SAGnDA,MAAO,SAAUA,GACb,GAAIsE,GAAOC,KAAMvG,EAAUsG,EAAKtG,OAEhC,OADAgC,GAAQX,EAAMW,GACVwP,MAAMxP,GACChC,EAAQgC,OAEfA,GAAShC,EAAQ6B,KAAOG,GAAShC,EAAQ8B,KACrC9B,EAAQgC,OAASA,IACjBsE,EAAKrG,QAAQ6M,KAAK,QAASxK,EAAYN,IACvChC,EAAQgC,MAAQA,EAChBsE,EAAKmL,iBAAiBzP,GACtBsE,EAAKsC,YALb,IASJA,SAAU,WACNrC,KAAKgL,QAAQpM,GAAkBnD,MAAOuE,KAAKvG,QAAQgC,SAEvDyP,iBAAkB,SAAUzP,GACxB,GAAoC0P,GAAhCpL,EAAOC,KAAM4F,EAAO7F,EAAK8G,KAEzBsE,GADAvF,GAAQA,EAAKwF,YACIxF,EAAKwF,YAAYC,OAEjBtL,EAAK4F,mBAAmBlK,EAAO,MAEpDuE,KAAKa,QAAQG,KAAK9B,GAAapF,KAAK,gBAAiB2B,GAAO3B,KAAK,iBAAkBqR,IAEvFR,YAAa,WACT7C,aAAa9H,KAAKwK,SAClBc,cAActL,KAAKyK,QAEvBK,SAAU,SAAUnE,GAChB,GAAI5G,GAAOC,IACP2G,GAAE4E,UAAWxL,GAAKuB,UAClBvB,EAAK8H,uBACL9H,EAAKuK,iBAAiBvK,EAAKuB,QAAQqF,EAAE4E,SAASxL,EAAKtG,QAAQgC,QAC3DsE,EAAK8G,MAAMK,eAAenH,EAAKtE,SAC/BkL,EAAEiD,mBAGVU,iBAAkB,SAAUvF,GACxB,GAAIhF,GAAOC,KAAMvG,EAAUsG,EAAKtG,OAEhC,OADAsL,GAAMjK,EAAMiK,GACRkG,MAAMlG,IACNhF,EAAK2J,QAAQjQ,EAAQ6B,KACrB,IAEJyJ,EAAMnK,EAAKW,IAAIX,EAAKU,IAAIyJ,EAAKtL,EAAQ8B,KAAM9B,EAAQ6B,KACnDyE,EAAK2J,QAAQ3E,GADbA,IAGJqF,kBAAmB,SAAU/C,GACzB,GAAI1M,GAAQqF,KAAKkE,QAAQzH,MAIzB,OAHIuD,MAAKK,SACLgH,EAAQ1M,EAAQ,EAAI0M,GAEjBrH,KAAKkE,QAAQtJ,EAAKW,IAAI,EAAGX,EAAKU,IAAI+L,EAAO1M,EAAQ,MAE5D0N,kBAAmB,WACf,GAAItI,GAAOC,KAAM1E,EAAMyE,EAAKtG,QAAQ6B,GACpCgM,YAAW,WACP,GAAI7L,GAAQsE,EAAKrG,QAAQ,GAAG+B,KAC5BsE,GAAKtE,MAAgB,KAAVA,GAAgBwP,MAAMxP,GAASH,EAAMG,MAGxD8M,QAAS,WACL,GAAIxI,GAAOC,IACXJ,GAAWK,GAAGsI,QAAQrI,KAAKH,GAC3BA,EAAKc,QAAQ2H,IAAIjK,GAAIyC,KAAK,aAAawH,IAAIjK,GAAIsL,MAAM7I,KAAK9B,GAAasJ,IAAIjK,GAAIsL,MAAM7I,KAAK5B,EAAgB,KAAOD,GAAgBqJ,IAAIjK,GAAIsL,MACzI9J,EAAK8G,MAAM2E,UAAUjD,UACrBxI,EAAK8G,MAAMc,gBAAe,KAGlCc,IAAOG,UAAY,SAAUF,EAAY3I,EAAMtG,GAC3C,QAASgS,GAAc1G,GACnB,GAAI2G,GAAiB3G,EAAMtL,EAAQ6B,IAAK+L,EAAQtH,EAAKsK,YAAczP,EAAKoK,KAAKlK,EAAM4Q,EAAiBjS,EAAQsB,YAAa0I,EAAYoB,SAAS9E,EAAKoF,YAAYkC,GAAQ,IAAKsE,EAAe5L,EAAKgB,UAAUC,KAAK,uBAAwB4K,EAAkB/G,SAAS9E,EAAKS,WAAWkI,GAAc,EAAG,IAAKmD,EAAgB9L,EAAKM,OAAS,EAAI,CAC1UsL,GAAa5L,EAAKQ,SAASR,EAAKM,OAASN,EAAKmB,cAAgBuC,EAAYA,GAC1EiF,EAAWvF,IAAIpD,EAAKO,UAAWmD,EAAYmI,EAAkBC,GAEjEJ,EAAchS,EAAQgC,OACtBsE,EAAK+L,MACDxN,EACAM,GACD,SAAU+H,GACT8E,EAAc9O,WAAWgK,EAAElL,MAAO,OAEtCsE,EAAK+L,KAAKzN,EAAQ,SAAUsI,GACxB8E,EAAc9O,WAAWgK,EAAEoF,OAAOtQ,QAAS,QAGnDgN,GAAOI,KAAO,SAAUnP,EAASU,EAAM4R,EAAOvS,GAC1C,GAAIsG,GAAOC,IACXD,GAAKiM,MAAQA,EACbjM,EAAKtG,QAAUA,EACfsG,EAAKrG,QAAUA,EACfqG,EAAK3F,KAAOA,EACZ2F,EAAKyL,UAAY,GAAIhO,GAAU9D,GAC3Bc,SAAU,EACVmP,UAAW5L,EAAMgC,EAAKkM,WAAYlM,GAClC6F,KAAM7H,EAAMgC,EAAK6F,KAAM7F,GACvBmM,QAASnO,EAAMgC,EAAKmM,QAASnM,GAC7BoM,WAAYpO,EAAMgC,EAAKoM,WAAYpM,KAEvCrG,EAAQmR,OAAM,GACdnR,EAAQ0O,GAAG,YAAa,SAAUzB,GAC9BA,EAAEiD,oBAGVnB,GAAOI,KAAKuD,WACRzC,UAAW,SAAUhD,GACjB3G,KAAKgM,MAAMK,kBAAoBrM,KAC/BA,KAAKwL,UAAUc,WAAWC,SAC1BvM,KAAKiM,WAAWtF,GAChB3G,KAAKkM,WAETD,WAAY,SAAUtF,GAClB,GAAI5G,GAAOC,KAAMgM,EAAQjM,EAAKiM,MAAOvS,EAAUsG,EAAKtG,OACpD,OAAKA,GAAQiH,SAIbV,KAAKgM,MAAMK,kBAAoBrM,KAC/BgM,EAAMtS,QAAQ8O,IAAIzJ,GAClBiN,EAAMnL,QAAQG,KAAK,IAAM1B,GAAeoI,YAAYpI,EAAgB,IAAMD,GAC1EU,EAAKrG,QAAQgK,SAASpE,EAAgB,IAAMD,GAC5C/F,EAAEwQ,SAASC,iBAAiB5G,IAAI,SAAU,WAC1CpD,EAAKuF,aAAe0G,EAAMjG,oBAC1BhG,EAAKnE,KAAOhB,EAAKW,IAAI9B,EAAQsB,WAAaiR,EAAM9K,cAAgB8K,EAAM9J,aAAc,GAChFnC,EAAK3F,MACL2F,EAAKvE,eAAiB/B,EAAQ+B,eAC9BuE,EAAKpE,aAAelC,EAAQkC,aAC5BqQ,EAAMQ,WAAWzM,EAAK3F,OAEtB2F,EAAK0M,OAAS1M,EAAKgF,IAAMtL,EAAQgC,MAErCsE,EAAK4H,gBAAe,GACpB5H,EAAK2M,iBAfL1M,IAHI2G,EAAEiD,iBACF,IAmBR8C,eAAgB,WACZ,GAAiG7G,GAAiB8G,EAA9G5M,EAAOC,KAAMgM,EAAQjM,EAAKiM,MAAOvL,EAAUV,EAAKtG,QAAQgH,QAAS0D,EAAO,GAAIyI,EAAMtT,EAAE+D,OACnFoD,GAAQC,UAGTD,EAAQqF,WACRD,EAAkB9F,EAAK8F,gBAAkB5J,EAAM6J,SAASrF,EAAQqF,WAEpExM,EAAE,qBAAqB8I,SACvBrC,EAAKgI,WAAazO,EAAE,mEAAqEuT,SAAS/C,SAASgD,MAC3G3I,EAAO6H,EAAMrG,mBAAmB5F,EAAKgF,KAAOiH,EAAMvQ,QAASsE,GACtDA,EAAK3F,OACNuS,EAAkB,cAAgBX,EAAM7L,cAAgB,IAAM,KAC9DJ,EAAKgN,gBAAkB,yBAA4BJ,EAAkB,mBACrExI,GAAQpE,EAAKgN,iBAEjBhN,EAAKgI,WAAW5D,KAAKA,GACrBpE,EAAKiN,eACD7G,IAAKyG,EAAIK,YACT/G,KAAM0G,EAAIM,cAEdnN,EAAKoN,gBAETvH,KAAM,SAAUe,GACZ,GAA2JyG,GAAvJrN,EAAOC,KAAMgM,EAAQjM,EAAKiM,MAAOqB,EAAI1G,EAAE0G,EAAE/D,SAAUgE,EAAI3G,EAAE2G,EAAEhE,SAAU7D,EAAa1F,EAAKuF,aAAaG,WAAYW,EAAWrG,EAAKuF,aAAac,QACjJO,GAAEiD,iBAGM7J,EAAKgF,IAFTiH,EAAM7L,cACF6L,EAAM3L,OACKN,EAAKwN,eAAeF,EAAG5H,EAAYW,EAAUiH,EAAIjH,GAEjDrG,EAAKwN,eAAeF,EAAG5H,EAAYW,EAAUiH,GAAKjH,GAGtDrG,EAAKwN,eAAeD,EAAGlH,EAAUX,EAAY6H,GAAKlH,GAE7DrG,EAAK0M,QAAU1M,EAAKgF,MACpBhF,EAAK0M,OAAS1M,EAAKgF,IACfhF,EAAK3F,MACY,eAAb2F,EAAK3F,KAED2F,EAAKvE,eADLuE,EAAKgF,IAAMhF,EAAKpE,aACMoE,EAAKgF,IAELhF,EAAKpE,aAAeoE,EAAKgF,IAG/ChF,EAAKgF,IAAMhF,EAAKvE,eAChBuE,EAAKpE,aAAeoE,EAAKgF,IAEzBhF,EAAKvE,eAAiBuE,EAAKpE,aAAeoE,EAAKgF,IAGvDqI,GACII,QACIzN,EAAKvE,eACLuE,EAAKpE,cAETF,OACIsE,EAAKvE,eACLuE,EAAKpE,gBAIbyR,GAAgB3R,MAAOsE,EAAKgF,KAEhCiH,EAAMhB,QAAQ1M,EAAO8O,IAEzBrN,EAAKmH,eAAenH,EAAKgF,MAE7BmC,eAAgB,SAAUnC,GACtB,GAAIhF,GAAOC,KAAMvG,EAAUsG,EAAKtG,QAASgH,EAAUhH,EAAQgH,QAAS0D,EAAO,EACtE1D,GAAQC,UAGRX,EAAKgI,YACNhI,EAAK2M,iBAETvI,EAAOpE,EAAKiM,MAAMrG,mBAAmB7K,EAAMiK,GAAMhF,GAC5CA,EAAK3F,OACN+J,GAAQpE,EAAKgN,iBAEjBhN,EAAKgI,WAAW5D,KAAKA,GACrBpE,EAAKoN,gBAEThB,WAAY,WAGR,MAFAnM,MAAKgM,MAAM3J,WACX/I,EAAEwQ,SAASC,iBAAiB5G,IAAI,SAAU,IACnCnD,KAAKiK,QAEhBiC,QAAS,WACL,GAAInM,GAAOC,KAAMgM,EAAQjM,EAAKiM,KAS9B,OARA1S,GAAEwQ,SAASC,iBAAiB5G,IAAI,SAAU,IACtCpD,EAAK3F,KACL4R,EAAMtC,QAAQ3J,EAAKvE,eAAgBuE,EAAKpE,eAExCqQ,EAAMtC,QAAQ3J,EAAKgF,KACnBhF,EAAKyL,UAAUc,WAAWmB,eAE9B1N,EAAKyL,UAAUc,WAAWC,SACnBxM,EAAKkK,QAEhBA,KAAM,WACF,GAAIlK,GAAOC,KAAMgM,EAAQjM,EAAKiM,KAG9B,OAFAA,GAAM7E,gBAAgBpH,EAAKrG,SAC3BsS,EAAMtS,QAAQ0O,GAAGrJ,IACV,GAEX4I,eAAgB,SAAU+F,GACtB,GAAI3N,GAAOC,KAAMgM,EAAQjM,EAAKiM,KAC1BjM,GAAKgI,YAAciE,EAAMvS,QAAQgH,QAAQC,SAAWsL,EAAMvS,QAAQiH,UAC9DgN,GACA3N,EAAKgI,WAAW3F,SAChBrC,EAAKgI,WAAa,MAElBhI,EAAKgI,WAAW4F,QAAQ,OAAQ,WAC5BrU,EAAE0G,MAAMoC,SACRrC,EAAKgI,WAAa,SAKlCoF,YAAa,WACT,GAA+QS,GAAaC,EAAWC,EAAMC,EAAzShO,EAAOC,KAAMgM,EAAQjM,EAAKiM,MAAO7F,EAAM,EAAGD,EAAO,EAAGxM,EAAUqG,EAAKrG,QAASsM,EAAS/J,EAAMgK,UAAUvM,GAAUsU,EAAS,EAAGC,EAAW3U,EAAE+D,QAAS6Q,EAAUnO,EAAKgI,WAAW/G,KAAK,cAAemN,EAAQ1Q,EAAWsC,EAAKgI,YAAaqG,EAASzQ,EAAYoC,EAAKgI,WAC9PhI,GAAK3F,MACLwT,EAAc5B,EAAMnL,QAAQG,KAAK9B,GACjC8G,EAAS/J,EAAMgK,UAAU2H,EAAYtH,GAAG,IACxCuH,EAAY5R,EAAMgK,UAAU2H,EAAYtH,GAAG,IACvC0F,EAAM7L,eACNgG,EAAM0H,EAAU1H,IAChBD,EAAOF,EAAOE,MAAQ2H,EAAU3H,KAAOF,EAAOE,MAAQ,IAEtDC,EAAMH,EAAOG,KAAO0H,EAAU1H,IAAMH,EAAOG,KAAO,EAClDD,EAAO2H,EAAU3H,MAErB6H,EAAatQ,EAAWmQ,EAAYtH,GAAG,IAAM,EAAI0H,IAEjD7H,EAAMH,EAAOG,IACbD,EAAOF,EAAOE,KACd6H,EAAatQ,EAAW/D,GAAW,EAAIsU,GAEvChC,EAAM7L,eACN+F,GAAQrB,UAAUsJ,EAAQnC,EAAMxL,WAAW9G,IAAY,EAAG,IAC1DyM,GAAOiI,EAASJ,GAAUE,EAAQzR,OAASyR,EAAQE,SAAW,KAE9DjI,GAAOtB,UAAUuJ,EAASpC,EAAMxL,WAAW9G,IAAY,EAAG,IAC1DwM,GAAQiI,EAAQH,GAAUE,EAAQzR,OAASyR,EAAQC,QAAU,IAE7DnC,EAAM7L,eACN2N,EAAO/N,EAAKsO,MAAMlI,EAAKiI,EAAQL,EAAYpQ,EAAYsQ,GAAYlO,EAAKiN,cAAc7G,KACtFA,GAAO2H,EACP5H,GAAQnG,EAAKuO,KAAKpI,EAAMiI,EAAO1Q,EAAWwQ,GAAYlO,EAAKiN,cAAc9G,QAEzE4H,EAAO/N,EAAKsO,MAAMnI,EAAMiI,EAAOJ,EAAYtQ,EAAWwQ,GAAYlO,EAAKiN,cAAc9G,MACrFC,GAAOpG,EAAKuO,KAAKnI,EAAKiI,EAAQzQ,EAAYsQ,GAAYlO,EAAKiN,cAAc7G,KACzED,GAAQ4H,GAERA,EAAO,GAAKI,IACZA,EAAQxG,cACRwG,EAAQxK,SAAS,wBAA0BsI,EAAM7L,cAAgB,IAAM,OAE3EJ,EAAKgI,WAAW5E,KACZgD,IAAKA,EACLD,KAAMA,KAGdoI,KAAM,SAAU/I,EAAUgJ,EAAMC,GAC5B,GAAIC,GAAS,CAOb,OANIlJ,GAAWgJ,EAAOC,IAClBC,EAASD,GAAejJ,EAAWgJ,IAEnChJ,EAAW,IACXkJ,GAAUlJ,GAEPkJ,GAEXJ,MAAO,SAAUrI,EAAQuI,EAAMR,EAAYS,GACvC,GAAIC,GAAS,CAOb,OANIzI,GAASuI,EAAOC,IAChBC,KAAYV,EAAaQ,IAEzBvI,EAASyI,EAAS,IAClBA,GAAUV,EAAaQ,GAEpBE,GAEXlB,eAAgB,SAAUhI,EAAUjK,EAAKC,EAAKmT,GAC1C,GAAI3O,GAAOC,KAAM+E,EAAM,CAUvB,OARIA,GADAzJ,EAAMiK,GAAYA,EAAWhK,EACvBwE,EAAKiM,MAAM5G,sBAAsBG,EAAUxF,EAAKuF,cAElDoJ,EACM3O,EAAKtG,QAAQ8B,IAEbwE,EAAKtG,QAAQ6B,MAMnCW,EAAMsB,GAAGoR,OAAOlG,IACZrL,EAAcwC,EAAW/B,QACzBgC,KAAM,SAAUnG,EAASD,GAAnB,GAkCEmU,GAjCA7N,EAAOC,KAAMqG,EAAS/M,EAAEI,GAASsH,KAAK,SAAU4N,EAAavI,EAAOC,GAAG,GAAG,GAAIuI,EAAcxI,EAAOC,GAAG,GAAG,EAC7GsI,GAAWxU,KAAO,OAClByU,EAAYzU,KAAO,OACfX,GAAWA,EAAQS,cACfmD,OAAOyR,SACPzR,OAAOyR,QAAQC,KAAK,sEAExBtV,EAAQS,aAAc,GAE1BT,EAAUoE,MACNrC,eAAgBqB,EAAU+R,EAAY,SACtCtT,IAAKuB,EAAU+R,EAAY,OAC3BrT,IAAKsB,EAAU+R,EAAY,OAC3B7T,UAAW8B,EAAU+R,EAAY,UAEjCjT,aAAckB,EAAUgS,EAAa,SACrCvT,IAAKuB,EAAUgS,EAAa,OAC5BtT,IAAKsB,EAAUgS,EAAa,OAC5B9T,UAAW8B,EAAUgS,EAAa,SACnCpV,GACCA,GAAWA,EAAQiH,UAAYnH,IAC/BE,EAAQiH,SAAW2F,EAAOnL,GAAG,eAEjC0E,EAAWK,GAAGJ,KAAKK,KAAKH,EAAMrG,EAASD,GACvCA,EAAUsG,EAAKtG,QACVwD,EAAQxD,EAAQ+B,iBAA8C,OAA3B/B,EAAQ+B,iBAC5C/B,EAAQ+B,eAAiB/B,EAAQ6B,IACjC+K,EAAOC,GAAG,GAAGC,KAAK,QAASxK,EAAYtC,EAAQ6B,OAE9C2B,EAAQxD,EAAQkC,eAA0C,OAAzBlC,EAAQkC,eAC1ClC,EAAQkC,aAAelC,EAAQ8B,IAC/B8K,EAAOC,GAAG,GAAGC,KAAK,QAASxK,EAAYtC,EAAQ8B,OAE/CqS,EAAc7N,EAAKc,QAAQG,KAAK9B,GACpCc,KAAK2I,WAAa,GAAIvL,GAAYwL,UAAUgF,EAAa7N,EAAMtG,GAC/DsG,EAAK+G,iBAAmB,GAAI2B,IAAOI,KAAK+E,EAAYtH,GAAG,GAAI,cAAevG,EAAMtG,GAChFsG,EAAKiH,gBAAkB,GAAIyB,IAAOI,KAAK+E,EAAYtH,GAAG,GAAI,aAAcvG,EAAMtG,IAElFA,SACIqD,KAAM,cACN1B,oBAAqB,OACrBM,qBAAsB,OACtB+E,SAAW3C,OAAQ,cACnBtC,eAAgB,KAChBG,aAAc,MAElB2G,OAAQ,SAAUA,GACd,GAAyC0G,GAArCjJ,EAAOC,KAAMvG,EAAUsG,EAAKtG,OAChCsG,GAAKoJ,UACD7G,KAAW,IAGfvC,EAAKc,QAAQ6G,YAAYlI,GAAgBkE,SAASnE,GAClDQ,EAAKc,QAAQG,KAAK,SAASoI,WAAW3J,GACtCuJ,EAAe,SAAUrC,GAAV,GAKPtB,GAAkFC,EAAyCP,EAA+D6B,EAAsBoI,EAAMC,EAAIrJ,EAJ1NyD,EAAQ1J,EAAWgH,GAAG,EAC1B,IAAK0C,EAAL,CAIA,GADIhE,EAAgBtF,EAAKI,cAAgBkJ,EAAMC,SAASC,MAAQF,EAAMC,SAASE,MAAOlE,EAAevF,EAAKgG,oBAAqBhB,EAAMhF,EAAKqF,sBAAsBC,EAAeC,GAAesB,EAAStN,EAAEqN,EAAEC,QACvMA,EAAO6C,SAAS,gBAGhB,MAFA1J,GAAKc,QAAQG,KAAK,IAAM1B,GAAeoI,YAAYpI,EAAgB,IAAMD,GACzEuH,EAAOlD,SAASpE,EAAgB,IAAMD,GACtC,CAEA0F,GAAMtL,EAAQ+B,gBACdwT,EAAOjK,EACPkK,EAAKxV,EAAQkC,aACbiK,EAAO7F,EAAK+G,kBACL/B,EAAMhF,EAAKpE,cAClBqT,EAAOvV,EAAQ+B,eACfyT,EAAKlK,EACLa,EAAO7F,EAAKiH,iBAERjC,EAAMtL,EAAQ+B,gBAAkB/B,EAAQkC,aAAeoJ,GACvDiK,EAAOjK,EACPkK,EAAKxV,EAAQkC,aACbiK,EAAO7F,EAAK+G,mBAEZkI,EAAOvV,EAAQ+B,eACfyT,EAAKlK,EACLa,EAAO7F,EAAKiH,iBAGpBpB,EAAK+D,UAAUhD,GACf5G,EAAKuK,iBAAiB0E,EAAMC,GAC5BlP,EAAKoH,gBAAgBvB,EAAKlM,WAE9BqG,EAAKc,QAAQG,KAAK5B,EAAgB,KAAOD,GAAgBiJ,GAAG3J,EAAkBuK,GAAca,MAAMzB,GAAG3J,EAAkB,WACnHnF,EAAEwQ,SAASC,iBAAiBC,IAAI,cAAe/N,EAAM2N,kBACtDxB,GAAGzJ,EAAgB,WACdoB,EAAKsM,mBACLtM,EAAKsM,kBAAkBpC,SAG/BlK,EAAKc,QAAQG,KAAK9B,GAAapF,KAAK4F,EAAU,GAAG0I,GAAG1J,EAAU,WAC1DqB,EAAKyH,uBACNY,GAAGtJ,EAAO,SAAU6H,GACnB5G,EAAKoH,gBAAgBR,EAAEC,QACvBD,EAAEiD,mBACHxB,GAAGpJ,EAAOjB,EAAMgC,EAAK2G,OAAQ3G,IAAOqI,GAAGnJ,EAAMlB,EAAMgC,EAAK0H,MAAO1H,IAClEA,EAAKc,QAAQG,KAAK9B,GAAasJ,IAAI3J,EAAU5C,EAAM2N,gBAAgBtD,GAAG,GAAG8B,GAAGvJ,EAAUd,EAAM,SAAU4I,GAClG3G,KAAK8K,SAASnE,EAAG,gBAClB5G,IAAO8J,MAAMvD,GAAG,GAAG8B,GAAGvJ,EAAUd,EAAM,SAAU4I,GAC/C3G,KAAK8K,SAASnE,EAAG,eAClB5G,IACHA,EAAKtG,QAAQiH,SAAU,IAE3ByI,QAAS,WACL,GAAIpJ,GAAOC,IACXD,GAAKc,QAAQ6G,YAAYnI,GAAemE,SAASlE,GACjDO,EAAKc,QAAQG,KAAK,SAASuF,KAAK9G,EAAUA,GAC1CM,EAAKc,QAAQG,KAAK5B,EAAgB,KAAOD,GAAgBqJ,IAAI/J,GAAkB+J,IAAI7J,GACnFoB,EAAKc,QAAQG,KAAK9B,GAAapF,KAAK4F,MAAc8I,IAAI9J,GAAU8J,IAAI3J,GAAU2J,IAAI1J,GAAO0J,IAAIxJ,GAAOwJ,IAAIvJ,GACxGc,EAAKtG,QAAQiH,SAAU,GAE3BoK,SAAU,SAAUnE,EAAGuI,GACnB,GAAmHC,GAAoBC,EAAkBC,EAArJtP,EAAOC,KAAMsP,EAAsBvP,EAAKtG,QAAQ+B,eAAgB+T,EAAoBxP,EAAKtG,QAAQkC,YACjGgL,GAAE4E,UAAWxL,GAAKuB,UAClBvB,EAAK8H,uBACS,eAAVqH,GACAG,EAAmBtP,EAAKkH,kBAAoBlH,EAAK+G,iBACjDwI,EAAsBvP,EAAKuB,QAAQqF,EAAE4E,SAAS+D,GAC1CA,EAAsBC,IACtBA,EAAoBD,KAGxBD,EAAmBtP,EAAKkH,kBAAoBlH,EAAKiH,gBACjDuI,EAAoBxP,EAAKuB,QAAQqF,EAAE4E,SAASgE,GACxCD,EAAsBC,IACtBD,EAAsBC,IAG9BxP,EAAKuK,iBAAiBxP,EAAMwU,GAAsBxU,EAAMyU,IACxDJ,EAAqBlR,KAAK1C,IAAI+T,EAAqBvP,EAAKtG,QAAQ+B,gBAChE4T,EAAmBnR,KAAK3C,IAAIiU,EAAmBxP,EAAKtG,QAAQkC,cAC5D0T,EAAiB1T,aAAesC,KAAK1C,IAAI6T,EAAkBrP,EAAKtG,QAAQ+B,gBACxE6T,EAAiB7T,eAAiByC,KAAK3C,IAAI6T,EAAoBpP,EAAKtG,QAAQkC,cAC5E0T,EAAiBnI,eAAenH,EAAKtE,QAAQsE,EAAKgH,gBAClDJ,EAAEiD,mBAGVF,QAAS,SAAUlO,EAAgBG,GAA1B,GACDoE,GAAOC,KAAMwN,EAASzN,EAAKtE,QAC3BsP,EAASyC,EAAO,IAAMhS,GAAkBgS,EAAO,IAAM7R,CACzDoE,GAAKtE,OACDD,EACAG,IAEAoP,GACAhL,EAAKiL,QAAQ3M,GACTmP,QACIhS,EACAG,GAEJF,OACID,EACAG,MAKhBF,MAAO,SAAUA,GACb,MAAIA,IAASA,EAAMgB,OACRuD,KAAKwP,OAAO/T,EAAM,GAAIA,EAAM,IAE5BuE,KAAKwP,UAGpBA,OAAQ,SAAUC,EAAO5F,GACrB,GAAI9J,GAAOC,KAAMvG,EAAUsG,EAAKtG,QAAS+B,EAAiB/B,EAAQ+B,eAAgBG,EAAelC,EAAQkC,YACzG,OAAIsP,OAAMwE,IAAUxE,MAAMpB,IAElBrO,EACAG,IAGJ8T,EAAQ3U,EAAM2U,GACd5F,EAAM/O,EAAM+O,GAEZ4F,GAAShW,EAAQ6B,KAAOmU,GAAShW,EAAQ8B,KAAOsO,GAAOpQ,EAAQ6B,KAAOuO,GAAOpQ,EAAQ8B,KAAOkU,GAAS5F,IACjGrO,GAAkBiU,GAAS9T,GAAgBkO,IAC3C9J,EAAKrG,QAAQsH,KAAK,SAASsF,GAAG,GAAGC,KAAK,QAASxK,EAAY0T,IAAQ5F,MAAMvD,GAAG,GAAGC,KAAK,QAASxK,EAAY8N,IACzGpQ,EAAQ+B,eAAiBiU,EACzBhW,EAAQkC,aAAekO,EACvB9J,EAAKsC,WACLtC,EAAKmL,iBAAiBuE,EAAO5F,KATjC4F,IAaRjC,OAAQ,SAAUiC,EAAO5F,GACrB,MAAI7L,GAAQyR,GACDzP,KAAKwP,OAAOC,EAAM,GAAIA,EAAM,IAE5BzP,KAAKwP,OAAOC,EAAO5F,IAGlCxH,SAAU,WACN,GAAItC,GAAOC,KAAMvG,EAAUsG,EAAKtG,OAChCsG,GAAKiL,QAAQpM,GACT4O,QACI/T,EAAQ+B,eACR/B,EAAQkC,cAEZF,OACIhC,EAAQ+B,eACR/B,EAAQkC,gBAGZlC,EAAQ+B,gBAAkB/B,EAAQ8B,KAAO9B,EAAQkC,cAAgBlC,EAAQ8B,KACzEwE,EAAKyM,WAAW,gBAGxBtB,iBAAkB,SAAUuE,EAAO5F,GAC/B,GAA8FsB,GAA1FpL,EAAOC,KAAM4N,EAAc7N,EAAKc,QAAQG,KAAK9B,GAAc0G,EAAO7F,EAAKkH,iBAC3EkE,GAAiBpL,EAAK4F,oBAClB8J,EACA5F,GACDjE,GACHgI,EAAYtH,GAAG,GAAGxM,KAAK,gBAAiB2V,GACxC7B,EAAYtH,GAAG,GAAGxM,KAAK,gBAAiB+P,GACxC+D,EAAY9T,KAAK,iBAAkBqR,IAEvCb,iBAAkB,SAAU9O,EAAgBG,GACxC,GAAIlC,GAAUuG,KAAKvG,OACnB+B,GAAiBZ,EAAKW,IAAIX,EAAKU,IAAIE,EAAgB/B,EAAQ8B,KAAM9B,EAAQ6B,KACzEK,EAAef,EAAKW,IAAIX,EAAKU,IAAIK,EAAclC,EAAQ8B,KAAM9B,EAAQ6B,KACjEE,GAAkB/B,EAAQ8B,KAAOI,GAAgBlC,EAAQ8B,KACzDyE,KAAKwM,WAAW,eAEpBxM,KAAK0J,QAAQ9O,EAAKU,IAAIE,EAAgBG,GAAef,EAAKW,IAAIC,EAAgBG,KAElF6Q,WAAY,SAAUpS,GAClB4F,KAAKa,QAAQG,KAAK9B,GAAawQ,KAAK,SAAUrI,GAC1C/N,EAAE0G,MAAMmD,IAAI,UAAmB,eAAR/I,EAAwB,EAAIiN,EAAQA,MAGnEgB,kBAAmB,WACf,GAAItI,GAAOC,KAAMvG,EAAUsG,EAAKtG,OAChC6N,YAAW,WAAA,GACHjB,GAAStG,EAAKrG,QAAQsH,KAAK,SAC3ByO,EAAQpJ,EAAO,GAAG5K,MAClBoO,EAAMxD,EAAO,GAAG5K,KACpBsE,GAAKyN,OAAiB,KAAViC,GAAgBxE,MAAMwE,GAAShW,EAAQ6B,IAAMmU,EAAe,KAAR5F,GAAcoB,MAAMpB,GAAOpQ,EAAQ8B,IAAMsO,MAGjHtB,QAAS,WACL,GAAIxI,GAAOC,IACXJ,GAAWK,GAAGsI,QAAQrI,KAAKH,GAC3BA,EAAKc,QAAQ2H,IAAIjK,GAAIyC,KAAK5B,EAAgB,KAAOD,GAAgBqJ,IAAIjK,GAAIsL,MAAM7I,KAAK9B,GAAasJ,IAAIjK,GACrGwB,EAAK+G,iBAAiB0E,UAAUjD,UAChCxI,EAAKiH,gBAAgBwE,UAAUjD,aAGvCnL,EAAYwL,UAAY,SAAUgF,EAAa7N,EAAMtG,GACjD,QAASgS,GAAchQ,GACnBA,EAAQA,KACR,IAAI6T,GAAsB7T,EAAM,GAAKhC,EAAQ6B,IAAKiU,EAAoB9T,EAAM,GAAKhC,EAAQ6B,IAAKqU,EAAsB/U,EAAKoK,KAAKlK,EAAMwU,EAAsB7V,EAAQsB,YAAa6U,EAAoBhV,EAAKoK,KAAKlK,EAAMyU,EAAoB9V,EAAQsB,YAAaS,EAAiBuE,EAAKoF,YAAYwK,GAAsBhU,EAAeoE,EAAKoF,YAAYyK,GAAoBC,EAAahL,SAAS9E,EAAKS,WAAWoN,EAAYtH,GAAG,IAAM,EAAG,IAAKuF,EAAgB9L,EAAKM,OAAS,EAAI,CAC7cuN,GAAYtH,GAAG,GAAGnD,IAAIpD,EAAKO,UAAW9E,EAAiBqU,EAAahE,GAAehC,MAAMvD,GAAG,GAAGnD,IAAIpD,EAAKO,UAAW3E,EAAekU,EAAahE,GAC/IiE,EAActU,EAAgBG,GAElC,QAASmU,GAActU,EAAgBG,GACnC,GAAI8H,GAAWsM,EAAmBpE,EAAe5L,EAAKgB,UAAUC,KAAK,sBACrEyC,GAAY7I,EAAK8K,IAAIlK,EAAiBG,GACtCgQ,EAAa5L,EAAKQ,SAASkD,GACvB1D,EAAKM,QACL0P,EAAoBnV,EAAKW,IAAIC,EAAgBG,GAC7CgQ,EAAaxI,IAAI,QAASpD,EAAKmB,cAAgB6O,EAAoB,KAEnEA,EAAoBnV,EAAKU,IAAIE,EAAgBG,GAC7CgQ,EAAaxI,IAAIpD,EAAKO,UAAWyP,EAAoB,IAG7DtE,EAAc1L,EAAKtE,SACnBsE,EAAK+L,MACDzN,EACAC,EACAM,GACD,SAAU+H,GACT8E,EAAc9E,EAAE6G,WAGxBvR,EAAMsB,GAAGoR,OAAOvR,IAClBC,OAAOpB,MAAM+T,QACR3S,OAAOpB,OACE,kBAAV5C,SAAwBA,OAAO4W,IAAM5W,OAAS,SAAU6W,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.slider.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.slider', ['kendo.draganddrop'], f);\n}(function () {\n    var __meta__ = {\n        id: 'slider',\n        name: 'Slider',\n        category: 'web',\n        description: 'The Slider widget provides a rich input for selecting values or ranges of values.',\n        depends: ['draganddrop']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, Draggable = kendo.ui.Draggable, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, extend = $.extend, format = kendo.format, parse = kendo.parseFloat, proxy = $.proxy, isArray = $.isArray, math = Math, support = kendo.support, pointers = support.pointers, msPointers = support.msPointers, CHANGE = 'change', SLIDE = 'slide', NS = '.slider', MOUSE_DOWN = 'touchstart' + NS + ' mousedown' + NS, TRACK_MOUSE_DOWN = pointers ? 'pointerdown' + NS : msPointers ? 'MSPointerDown' + NS : MOUSE_DOWN, MOUSE_UP = 'touchend' + NS + ' mouseup' + NS, TRACK_MOUSE_UP = pointers ? 'pointerup' : msPointers ? 'MSPointerUp' + NS : MOUSE_UP, MOVE_SELECTION = 'moveSelection', KEY_DOWN = 'keydown' + NS, CLICK = 'click' + NS, MOUSE_OVER = 'mouseover' + NS, FOCUS = 'focus' + NS, BLUR = 'blur' + NS, DRAG_HANDLE = '.k-draghandle', TRACK_SELECTOR = '.k-slider-track', TICK_SELECTOR = '.k-tick', STATE_SELECTED = 'k-state-selected', STATE_FOCUSED = 'k-state-focused', STATE_DEFAULT = 'k-state-default', STATE_DISABLED = 'k-state-disabled', DISABLED = 'disabled', UNDEFINED = 'undefined', TABINDEX = 'tabindex', getTouches = kendo.getTouches;\n        var SliderBase = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                that._isHorizontal = options.orientation == 'horizontal';\n                that._isRtl = that._isHorizontal && kendo.support.isRtl(element);\n                that._position = that._isHorizontal ? 'left' : 'bottom';\n                that._sizeFn = that._isHorizontal ? 'width' : 'height';\n                that._outerSize = that._isHorizontal ? outerWidth : outerHeight;\n                options.tooltip.format = options.tooltip.enabled ? options.tooltip.format || '{0}' : '{0}';\n                if (options.smallStep <= 0) {\n                    throw new Error('Kendo UI Slider smallStep must be a positive number.');\n                }\n                that._createHtml();\n                that.wrapper = that.element.closest('.k-slider');\n                that._trackDiv = that.wrapper.find(TRACK_SELECTOR);\n                that._setTrackDivWidth();\n                that._maxSelection = that._trackDiv[that._sizeFn]();\n                that._sliderItemsInit();\n                that._reset();\n                that._tabindex(that.wrapper.find(DRAG_HANDLE));\n                that[options.enabled ? 'enable' : 'disable']();\n                var rtlDirectionSign = kendo.support.isRtl(that.wrapper) ? -1 : 1;\n                that._keyMap = {\n                    37: step(-1 * rtlDirectionSign * options.smallStep),\n                    40: step(-options.smallStep),\n                    39: step(+1 * rtlDirectionSign * options.smallStep),\n                    38: step(+options.smallStep),\n                    35: setValue(options.max),\n                    36: setValue(options.min),\n                    33: step(+options.largeStep),\n                    34: step(-options.largeStep)\n                };\n                kendo.notify(that);\n            },\n            events: [\n                CHANGE,\n                SLIDE\n            ],\n            options: {\n                enabled: true,\n                min: 0,\n                max: 10,\n                smallStep: 1,\n                largeStep: 5,\n                orientation: 'horizontal',\n                tickPlacement: 'both',\n                tooltip: {\n                    enabled: true,\n                    format: '{0}'\n                }\n            },\n            _distance: function () {\n                return round(this.options.max - this.options.min);\n            },\n            _resize: function () {\n                this._setTrackDivWidth();\n                this.wrapper.find('.k-slider-items').remove();\n                this._maxSelection = this._trackDiv[this._sizeFn]();\n                this._sliderItemsInit();\n                this._refresh();\n                if (this.options.enabled) {\n                    this.enable(true);\n                }\n            },\n            _sliderItemsInit: function () {\n                var that = this, options = that.options;\n                var sizeBetweenTicks = that._maxSelection / ((options.max - options.min) / options.smallStep);\n                var pixelWidths = that._calculateItemsWidth(math.floor(that._distance() / options.smallStep));\n                if (options.tickPlacement != 'none' && sizeBetweenTicks >= 2) {\n                    $(this.element).parent().find('.k-slider-items').remove();\n                    that._trackDiv.before(createSliderItems(options, that._distance()));\n                    that._setItemsWidth(pixelWidths);\n                    that._setItemsTitle();\n                }\n                that._calculateSteps(pixelWidths);\n                if (options.tickPlacement != 'none' && sizeBetweenTicks >= 2 && options.largeStep >= options.smallStep) {\n                    that._setItemsLargeTick();\n                }\n            },\n            getSize: function () {\n                return kendo.dimensions(this.wrapper);\n            },\n            _setTrackDivWidth: function () {\n                var that = this, trackDivPosition = parseFloat(that._trackDiv.css(that._isRtl ? 'right' : that._position), 10) * 2;\n                that._trackDiv[that._sizeFn](that.wrapper[that._sizeFn]() - 2 - trackDivPosition);\n            },\n            _setItemsWidth: function (pixelWidths) {\n                var that = this, options = that.options, first = 0, last = pixelWidths.length - 1, items = that.wrapper.find(TICK_SELECTOR), i, paddingTop = 0, bordersWidth = 2, count = items.length, selection = 0;\n                for (i = 0; i < count - 2; i++) {\n                    $(items[i + 1])[that._sizeFn](pixelWidths[i]);\n                }\n                if (that._isHorizontal) {\n                    $(items[first]).addClass('k-first')[that._sizeFn](pixelWidths[last - 1]);\n                    $(items[last]).addClass('k-last')[that._sizeFn](pixelWidths[last]);\n                } else {\n                    $(items[last]).addClass('k-first')[that._sizeFn](pixelWidths[last]);\n                    $(items[first]).addClass('k-last')[that._sizeFn](pixelWidths[last - 1]);\n                }\n                if (that._distance() % options.smallStep !== 0 && !that._isHorizontal) {\n                    for (i = 0; i < pixelWidths.length; i++) {\n                        selection += pixelWidths[i];\n                    }\n                    paddingTop = that._maxSelection - selection;\n                    paddingTop += parseFloat(that._trackDiv.css(that._position), 10) + bordersWidth;\n                    that.wrapper.find('.k-slider-items').css('padding-top', paddingTop);\n                }\n            },\n            _setItemsTitle: function () {\n                var that = this, options = that.options, items = that.wrapper.find(TICK_SELECTOR), titleNumber = options.min, count = items.length, i = that._isHorizontal && !that._isRtl ? 0 : count - 1, limit = that._isHorizontal && !that._isRtl ? count : -1, increment = that._isHorizontal && !that._isRtl ? 1 : -1;\n                for (; i - limit !== 0; i += increment) {\n                    $(items[i]).attr('title', format(options.tooltip.format, round(titleNumber)));\n                    titleNumber += options.smallStep;\n                }\n            },\n            _setItemsLargeTick: function () {\n                var that = this, options = that.options, items = that.wrapper.find(TICK_SELECTOR), i = 0, item, value;\n                if (removeFraction(options.largeStep) % removeFraction(options.smallStep) === 0 || that._distance() / options.largeStep >= 3) {\n                    if (!that._isHorizontal && !that._isRtl) {\n                        items = $.makeArray(items).reverse();\n                    }\n                    for (i = 0; i < items.length; i++) {\n                        item = $(items[i]);\n                        value = that._values[i];\n                        var valueWithoutFraction = round(removeFraction(value - this.options.min));\n                        if (valueWithoutFraction % removeFraction(options.smallStep) === 0 && valueWithoutFraction % removeFraction(options.largeStep) === 0) {\n                            item.addClass('k-tick-large').html('<span class=\\'k-label\\'>' + item.attr('title') + '</span>');\n                            if (i !== 0 && i !== items.length - 1) {\n                                item.css('line-height', item[that._sizeFn]() + 'px');\n                            }\n                        }\n                    }\n                }\n            },\n            _calculateItemsWidth: function (itemsCount) {\n                var that = this, options = that.options, trackDivSize = parseFloat(that._trackDiv.css(that._sizeFn)) + 1, distance = that._distance(), pixelStep = trackDivSize / distance, itemWidth, pixelWidths, i;\n                if (distance / options.smallStep - math.floor(distance / options.smallStep) > 0) {\n                    trackDivSize -= distance % options.smallStep * pixelStep;\n                }\n                itemWidth = trackDivSize / itemsCount;\n                pixelWidths = [];\n                for (i = 0; i < itemsCount - 1; i++) {\n                    pixelWidths[i] = itemWidth;\n                }\n                pixelWidths[itemsCount - 1] = pixelWidths[itemsCount] = itemWidth / 2;\n                return that._roundWidths(pixelWidths);\n            },\n            _roundWidths: function (pixelWidthsArray) {\n                var balance = 0, count = pixelWidthsArray.length, i;\n                for (i = 0; i < count; i++) {\n                    balance += pixelWidthsArray[i] - math.floor(pixelWidthsArray[i]);\n                    pixelWidthsArray[i] = math.floor(pixelWidthsArray[i]);\n                }\n                balance = math.round(balance);\n                return this._addAdditionalSize(balance, pixelWidthsArray);\n            },\n            _addAdditionalSize: function (additionalSize, pixelWidthsArray) {\n                if (additionalSize === 0) {\n                    return pixelWidthsArray;\n                }\n                var step = parseFloat(pixelWidthsArray.length - 1) / parseFloat(additionalSize == 1 ? additionalSize : additionalSize - 1), i;\n                for (i = 0; i < additionalSize; i++) {\n                    pixelWidthsArray[parseInt(math.round(step * i), 10)] += 1;\n                }\n                return pixelWidthsArray;\n            },\n            _calculateSteps: function (pixelWidths) {\n                var that = this, options = that.options, val = options.min, selection = 0, distance = that._distance(), itemsCount = math.ceil(distance / options.smallStep), i = 1, lastItem;\n                itemsCount += distance / options.smallStep % 1 === 0 ? 1 : 0;\n                pixelWidths.splice(0, 0, pixelWidths[itemsCount - 2] * 2);\n                pixelWidths.splice(itemsCount - 1, 1, pixelWidths.pop() * 2);\n                that._pixelSteps = [selection];\n                that._values = [val];\n                if (itemsCount === 0) {\n                    return;\n                }\n                while (i < itemsCount) {\n                    selection += (pixelWidths[i - 1] + pixelWidths[i]) / 2;\n                    that._pixelSteps[i] = selection;\n                    val += options.smallStep;\n                    that._values[i] = round(val);\n                    i++;\n                }\n                lastItem = distance % options.smallStep === 0 ? itemsCount - 1 : itemsCount;\n                that._pixelSteps[lastItem] = that._maxSelection;\n                that._values[lastItem] = options.max;\n                if (that._isRtl) {\n                    that._pixelSteps.reverse();\n                    that._values.reverse();\n                }\n            },\n            _getValueFromPosition: function (mousePosition, dragableArea) {\n                var that = this, options = that.options, step = math.max(options.smallStep * (that._maxSelection / that._distance()), 0), position = 0, halfStep = step / 2, i;\n                if (that._isHorizontal) {\n                    position = mousePosition - dragableArea.startPoint;\n                    if (that._isRtl) {\n                        position = that._maxSelection - position;\n                    }\n                } else {\n                    position = dragableArea.startPoint - mousePosition;\n                }\n                if (that._maxSelection - (parseInt(that._maxSelection % step, 10) - 3) / 2 < position) {\n                    return options.max;\n                }\n                for (i = 0; i < that._pixelSteps.length; i++) {\n                    if (math.abs(that._pixelSteps[i] - position) - 1 <= halfStep) {\n                        return round(that._values[i]);\n                    }\n                }\n            },\n            _getFormattedValue: function (val, drag) {\n                var that = this, html = '', tooltip = that.options.tooltip, tooltipTemplate, selectionStart, selectionEnd;\n                if (isArray(val)) {\n                    selectionStart = val[0];\n                    selectionEnd = val[1];\n                } else if (drag && drag.type) {\n                    selectionStart = drag.selectionStart;\n                    selectionEnd = drag.selectionEnd;\n                }\n                if (drag) {\n                    tooltipTemplate = drag.tooltipTemplate;\n                }\n                if (!tooltipTemplate && tooltip.template) {\n                    tooltipTemplate = kendo.template(tooltip.template);\n                }\n                if (isArray(val) || drag && drag.type) {\n                    if (tooltipTemplate) {\n                        html = tooltipTemplate({\n                            selectionStart: selectionStart,\n                            selectionEnd: selectionEnd\n                        });\n                    } else {\n                        selectionStart = format(tooltip.format, selectionStart);\n                        selectionEnd = format(tooltip.format, selectionEnd);\n                        html = selectionStart + ' - ' + selectionEnd;\n                    }\n                } else {\n                    if (drag) {\n                        drag.val = val;\n                    }\n                    if (tooltipTemplate) {\n                        html = tooltipTemplate({ value: val });\n                    } else {\n                        html = format(tooltip.format, val);\n                    }\n                }\n                return html;\n            },\n            _getDraggableArea: function () {\n                var that = this, offset = kendo.getOffset(that._trackDiv);\n                return {\n                    startPoint: that._isHorizontal ? offset.left : offset.top + that._maxSelection,\n                    endPoint: that._isHorizontal ? offset.left + that._maxSelection : offset.top\n                };\n            },\n            _createHtml: function () {\n                var that = this, element = that.element, options = that.options, inputs = element.find('input');\n                if (inputs.length == 2) {\n                    inputs.eq(0).prop('value', formatValue(options.selectionStart));\n                    inputs.eq(1).prop('value', formatValue(options.selectionEnd));\n                } else {\n                    element.prop('value', formatValue(options.value));\n                }\n                element.wrap(createWrapper(options, element, that._isHorizontal)).hide();\n                if (options.showButtons) {\n                    element.before(createButton(options, 'increase', that._isHorizontal, that._isRtl)).before(createButton(options, 'decrease', that._isHorizontal, that._isRtl));\n                }\n                element.before(createTrack(options, element));\n            },\n            _focus: function (e) {\n                var that = this, target = e.target, val = that.value(), drag = that._drag;\n                if (!drag) {\n                    if (target == that.wrapper.find(DRAG_HANDLE).eq(0)[0]) {\n                        drag = that._firstHandleDrag;\n                        that._activeHandle = 0;\n                    } else {\n                        drag = that._lastHandleDrag;\n                        that._activeHandle = 1;\n                    }\n                    val = val[that._activeHandle];\n                }\n                $(target).addClass(STATE_FOCUSED + ' ' + STATE_SELECTED);\n                if (drag) {\n                    that._activeHandleDrag = drag;\n                    drag.selectionStart = that.options.selectionStart;\n                    drag.selectionEnd = that.options.selectionEnd;\n                    drag._updateTooltip(val);\n                }\n            },\n            _focusWithMouse: function (target) {\n                target = $(target);\n                var that = this, idx = target.is(DRAG_HANDLE) ? target.index() : 0;\n                window.setTimeout(function () {\n                    that.wrapper.find(DRAG_HANDLE)[idx == 2 ? 1 : 0].focus();\n                }, 1);\n                that._setTooltipTimeout();\n            },\n            _blur: function (e) {\n                var that = this, drag = that._activeHandleDrag;\n                $(e.target).removeClass(STATE_FOCUSED + ' ' + STATE_SELECTED);\n                if (drag) {\n                    drag._removeTooltip();\n                    delete that._activeHandleDrag;\n                    delete that._activeHandle;\n                }\n            },\n            _setTooltipTimeout: function () {\n                var that = this;\n                that._tooltipTimeout = window.setTimeout(function () {\n                    var drag = that._drag || that._activeHandleDrag;\n                    if (drag) {\n                        drag._removeTooltip();\n                    }\n                }, 300);\n            },\n            _clearTooltipTimeout: function () {\n                var that = this;\n                window.clearTimeout(this._tooltipTimeout);\n                var drag = that._drag || that._activeHandleDrag;\n                if (drag && drag.tooltipDiv) {\n                    drag.tooltipDiv.stop(true, false).css('opacity', 1);\n                }\n            },\n            _reset: function () {\n                var that = this, element = that.element, formId = element.attr('form'), form = formId ? $('#' + formId) : element.closest('form');\n                if (form[0]) {\n                    that._form = form.on('reset', proxy(that._formResetHandler, that));\n                }\n            },\n            min: function (value) {\n                if (!value) {\n                    return this.options.min;\n                }\n                this.setOptions({ 'min': value });\n            },\n            max: function (value) {\n                if (!value) {\n                    return this.options.max;\n                }\n                this.setOptions({ 'max': value });\n            },\n            setOptions: function (options) {\n                Widget.fn.setOptions.call(this, options);\n                this._sliderItemsInit();\n                this._refresh();\n            },\n            destroy: function () {\n                if (this._form) {\n                    this._form.off('reset', this._formResetHandler);\n                }\n                Widget.fn.destroy.call(this);\n            }\n        });\n        function createWrapper(options, element, isHorizontal) {\n            var orientationCssClass = isHorizontal ? ' k-slider-horizontal' : ' k-slider-vertical', style = options.style ? options.style : element.attr('style'), cssClasses = element.attr('class') ? ' ' + element.attr('class') : '', tickPlacementCssClass = '';\n            if (options.tickPlacement == 'bottomRight') {\n                tickPlacementCssClass = ' k-slider-bottomright';\n            } else if (options.tickPlacement == 'topLeft') {\n                tickPlacementCssClass = ' k-slider-topleft';\n            }\n            style = style ? ' style=\\'' + style + '\\'' : '';\n            return '<div class=\\'k-widget k-slider' + orientationCssClass + cssClasses + '\\'' + style + '>' + '<div class=\\'k-slider-wrap' + (options.showButtons ? ' k-slider-buttons' : '') + tickPlacementCssClass + '\\'></div></div>';\n        }\n        function createButton(options, type, isHorizontal, isRtl) {\n            var buttonCssClass = '';\n            if (isHorizontal) {\n                if (!isRtl && type == 'increase' || isRtl && type != 'increase') {\n                    buttonCssClass = 'k-i-arrow-60-right';\n                } else {\n                    buttonCssClass = 'k-i-arrow-60-left';\n                }\n            } else {\n                if (type == 'increase') {\n                    buttonCssClass = 'k-i-arrow-60-up';\n                } else {\n                    buttonCssClass = 'k-i-arrow-60-down';\n                }\n            }\n            return '<a class=\\'k-button k-button-' + type + '\\' ' + 'title=\\'' + options[type + 'ButtonTitle'] + '\\' ' + 'aria-label=\\'' + options[type + 'ButtonTitle'] + '\\'>' + '<span class=\\'k-icon ' + buttonCssClass + '\\'></span></a>';\n        }\n        function createSliderItems(options, distance) {\n            var result = '<ul class=\\'k-reset k-slider-items\\'>', count = math.floor(round(distance / options.smallStep)) + 1, i;\n            for (i = 0; i < count; i++) {\n                result += '<li class=\\'k-tick\\' role=\\'presentation\\'>&nbsp;</li>';\n            }\n            result += '</ul>';\n            return result;\n        }\n        function createTrack(options, element) {\n            var dragHandleCount = element.is('input') ? 1 : 2, firstDragHandleTitle = dragHandleCount == 2 ? options.leftDragHandleTitle : options.dragHandleTitle;\n            return '<div class=\\'k-slider-track\\'><div class=\\'k-slider-selection\\'><!-- --></div>' + '<a href=\\'#\\' class=\\'k-draghandle\\' title=\\'' + firstDragHandleTitle + '\\' role=\\'slider\\' aria-valuemin=\\'' + options.min + '\\' aria-valuemax=\\'' + options.max + '\\' aria-valuenow=\\'' + (dragHandleCount > 1 ? options.selectionStart || options.min : options.value || options.min) + '\\'>Drag</a>' + (dragHandleCount > 1 ? '<a href=\\'#\\' class=\\'k-draghandle\\' title=\\'' + options.rightDragHandleTitle + '\\'role=\\'slider\\' aria-valuemin=\\'' + options.min + '\\' aria-valuemax=\\'' + options.max + '\\' aria-valuenow=\\'' + (options.selectionEnd || options.max) + '\\'>Drag</a>' : '') + '</div>';\n        }\n        function step(stepValue) {\n            return function (value) {\n                return value + stepValue;\n            };\n        }\n        function setValue(value) {\n            return function () {\n                return value;\n            };\n        }\n        function formatValue(value) {\n            return (value + '').replace('.', kendo.cultures.current.numberFormat['.']);\n        }\n        function calculatePrecision(value) {\n            var number = value.toString();\n            var precision = 0;\n            number = number.split('.');\n            if (number[1]) {\n                precision = number[1].length;\n            }\n            precision = precision > 10 ? 10 : precision;\n            return precision;\n        }\n        function round(value) {\n            var precision, power;\n            value = parseFloat(value, 10);\n            precision = calculatePrecision(value);\n            power = math.pow(10, precision || 0);\n            return math.round(value * power) / power;\n        }\n        function parseAttr(element, name) {\n            var value = parse(element.getAttribute(name));\n            if (value === null) {\n                value = undefined;\n            }\n            return value;\n        }\n        function defined(value) {\n            return typeof value !== UNDEFINED;\n        }\n        function removeFraction(value) {\n            return value * 10000;\n        }\n        var Slider = SliderBase.extend({\n            init: function (element, options) {\n                var that = this, dragHandle;\n                element.type = 'text';\n                options = extend({}, {\n                    value: parseAttr(element, 'value'),\n                    min: parseAttr(element, 'min'),\n                    max: parseAttr(element, 'max'),\n                    smallStep: parseAttr(element, 'step')\n                }, options);\n                element = $(element);\n                if (options && options.enabled === undefined) {\n                    options.enabled = !element.is('[disabled]');\n                }\n                SliderBase.fn.init.call(that, element, options);\n                options = that.options;\n                if (!defined(options.value) || options.value === null) {\n                    options.value = options.min;\n                    element.prop('value', formatValue(options.min));\n                }\n                options.value = math.max(math.min(options.value, options.max), options.min);\n                dragHandle = that.wrapper.find(DRAG_HANDLE);\n                this._selection = new Slider.Selection(dragHandle, that, options);\n                that._drag = new Slider.Drag(dragHandle, '', that, options);\n            },\n            options: {\n                name: 'Slider',\n                showButtons: true,\n                increaseButtonTitle: 'Increase',\n                decreaseButtonTitle: 'Decrease',\n                dragHandleTitle: 'drag',\n                tooltip: { format: '{0:#,#.##}' },\n                value: null\n            },\n            enable: function (enable) {\n                var that = this, options = that.options, clickHandler, move;\n                that.disable();\n                if (enable === false) {\n                    return;\n                }\n                that.wrapper.removeClass(STATE_DISABLED).addClass(STATE_DEFAULT);\n                that.wrapper.find('input').removeAttr(DISABLED);\n                clickHandler = function (e) {\n                    var touch = getTouches(e)[0];\n                    if (!touch) {\n                        return;\n                    }\n                    var mousePosition = that._isHorizontal ? touch.location.pageX : touch.location.pageY, dragableArea = that._getDraggableArea(), target = $(e.target);\n                    if (target.hasClass('k-draghandle')) {\n                        target.addClass(STATE_FOCUSED + ' ' + STATE_SELECTED);\n                        return;\n                    }\n                    that._update(that._getValueFromPosition(mousePosition, dragableArea));\n                    that._focusWithMouse(e.target);\n                    that._drag.dragstart(e);\n                    e.preventDefault();\n                };\n                that.wrapper.find(TICK_SELECTOR + ', ' + TRACK_SELECTOR).on(TRACK_MOUSE_DOWN, clickHandler).end().on(TRACK_MOUSE_DOWN, function () {\n                    $(document.documentElement).one('selectstart', kendo.preventDefault);\n                }).on(TRACK_MOUSE_UP, function () {\n                    that._drag._end();\n                });\n                that.wrapper.find(DRAG_HANDLE).attr(TABINDEX, 0).on(MOUSE_UP, function () {\n                    that._setTooltipTimeout();\n                }).on(CLICK, function (e) {\n                    that._focusWithMouse(e.target);\n                    e.preventDefault();\n                }).on(FOCUS, proxy(that._focus, that)).on(BLUR, proxy(that._blur, that));\n                move = proxy(function (sign) {\n                    var newVal = that._nextValueByIndex(that._valueIndex + sign * 1);\n                    that._setValueInRange(newVal);\n                    that._drag._updateTooltip(newVal);\n                }, that);\n                if (options.showButtons) {\n                    var mouseDownHandler = proxy(function (e, sign) {\n                        this._clearTooltipTimeout();\n                        if (e.which === 1 || support.touch && e.which === 0) {\n                            move(sign);\n                            this.timeout = setTimeout(proxy(function () {\n                                this.timer = setInterval(function () {\n                                    move(sign);\n                                }, 60);\n                            }, this), 200);\n                        }\n                    }, that);\n                    that.wrapper.find('.k-button').on(MOUSE_UP, proxy(function (e) {\n                        this._clearTimer();\n                        that._focusWithMouse(e.target);\n                    }, that)).on(MOUSE_OVER, function (e) {\n                        $(e.currentTarget).addClass('k-state-hover');\n                    }).on('mouseout' + NS, proxy(function (e) {\n                        $(e.currentTarget).removeClass('k-state-hover');\n                        this._clearTimer();\n                    }, that)).eq(0).on(MOUSE_DOWN, proxy(function (e) {\n                        mouseDownHandler(e, 1);\n                    }, that)).click(false).end().eq(1).on(MOUSE_DOWN, proxy(function (e) {\n                        mouseDownHandler(e, -1);\n                    }, that)).click(kendo.preventDefault);\n                }\n                that.wrapper.find(DRAG_HANDLE).off(KEY_DOWN, false).on(KEY_DOWN, proxy(this._keydown, that));\n                options.enabled = true;\n            },\n            disable: function () {\n                var that = this;\n                that.wrapper.removeClass(STATE_DEFAULT).addClass(STATE_DISABLED);\n                $(that.element).prop(DISABLED, DISABLED);\n                that.wrapper.find('.k-button').off(MOUSE_DOWN).on(MOUSE_DOWN, function (e) {\n                    e.preventDefault();\n                    $(this).addClass('k-state-active');\n                }).off(MOUSE_UP).on(MOUSE_UP, function (e) {\n                    e.preventDefault();\n                    $(this).removeClass('k-state-active');\n                }).off('mouseleave' + NS).on('mouseleave' + NS, kendo.preventDefault).off(MOUSE_OVER).on(MOUSE_OVER, kendo.preventDefault);\n                that.wrapper.find(TICK_SELECTOR + ', ' + TRACK_SELECTOR).off(TRACK_MOUSE_DOWN).off(TRACK_MOUSE_UP);\n                that.wrapper.find(DRAG_HANDLE).attr(TABINDEX, -1).off(MOUSE_UP).off(KEY_DOWN).off(CLICK).off(FOCUS).off(BLUR);\n                that.options.enabled = false;\n            },\n            _update: function (val) {\n                var that = this, change = that.value() != val;\n                that.value(val);\n                if (change) {\n                    that.trigger(CHANGE, { value: that.options.value });\n                }\n            },\n            value: function (value) {\n                var that = this, options = that.options;\n                value = round(value);\n                if (isNaN(value)) {\n                    return options.value;\n                }\n                if (value >= options.min && value <= options.max) {\n                    if (options.value != value) {\n                        that.element.prop('value', formatValue(value));\n                        options.value = value;\n                        that._refreshAriaAttr(value);\n                        that._refresh();\n                    }\n                }\n            },\n            _refresh: function () {\n                this.trigger(MOVE_SELECTION, { value: this.options.value });\n            },\n            _refreshAriaAttr: function (value) {\n                var that = this, drag = that._drag, formattedValue;\n                if (drag && drag._tooltipDiv) {\n                    formattedValue = drag._tooltipDiv.text();\n                } else {\n                    formattedValue = that._getFormattedValue(value, null);\n                }\n                this.wrapper.find(DRAG_HANDLE).attr('aria-valuenow', value).attr('aria-valuetext', formattedValue);\n            },\n            _clearTimer: function () {\n                clearTimeout(this.timeout);\n                clearInterval(this.timer);\n            },\n            _keydown: function (e) {\n                var that = this;\n                if (e.keyCode in that._keyMap) {\n                    that._clearTooltipTimeout();\n                    that._setValueInRange(that._keyMap[e.keyCode](that.options.value));\n                    that._drag._updateTooltip(that.value());\n                    e.preventDefault();\n                }\n            },\n            _setValueInRange: function (val) {\n                var that = this, options = that.options;\n                val = round(val);\n                if (isNaN(val)) {\n                    that._update(options.min);\n                    return;\n                }\n                val = math.max(math.min(val, options.max), options.min);\n                that._update(val);\n            },\n            _nextValueByIndex: function (index) {\n                var count = this._values.length;\n                if (this._isRtl) {\n                    index = count - 1 - index;\n                }\n                return this._values[math.max(0, math.min(index, count - 1))];\n            },\n            _formResetHandler: function () {\n                var that = this, min = that.options.min;\n                setTimeout(function () {\n                    var value = that.element[0].value;\n                    that.value(value === '' || isNaN(value) ? min : value);\n                });\n            },\n            destroy: function () {\n                var that = this;\n                SliderBase.fn.destroy.call(that);\n                that.wrapper.off(NS).find('.k-button').off(NS).end().find(DRAG_HANDLE).off(NS).end().find(TICK_SELECTOR + ', ' + TRACK_SELECTOR).off(NS).end();\n                that._drag.draggable.destroy();\n                that._drag._removeTooltip(true);\n            }\n        });\n        Slider.Selection = function (dragHandle, that, options) {\n            function moveSelection(val) {\n                var selectionValue = val - options.min, index = that._valueIndex = math.ceil(round(selectionValue / options.smallStep)), selection = parseInt(that._pixelSteps[index], 10), selectionDiv = that._trackDiv.find('.k-slider-selection'), halfDragHanndle = parseInt(that._outerSize(dragHandle) / 2, 10), rtlCorrection = that._isRtl ? 2 : 0;\n                selectionDiv[that._sizeFn](that._isRtl ? that._maxSelection - selection : selection);\n                dragHandle.css(that._position, selection - halfDragHanndle - rtlCorrection);\n            }\n            moveSelection(options.value);\n            that.bind([\n                SLIDE,\n                MOVE_SELECTION\n            ], function (e) {\n                moveSelection(parseFloat(e.value, 10));\n            });\n            that.bind(CHANGE, function (e) {\n                moveSelection(parseFloat(e.sender.value(), 10));\n            });\n        };\n        Slider.Drag = function (element, type, owner, options) {\n            var that = this;\n            that.owner = owner;\n            that.options = options;\n            that.element = element;\n            that.type = type;\n            that.draggable = new Draggable(element, {\n                distance: 0,\n                dragstart: proxy(that._dragstart, that),\n                drag: proxy(that.drag, that),\n                dragend: proxy(that.dragend, that),\n                dragcancel: proxy(that.dragcancel, that)\n            });\n            element.click(false);\n            element.on('dragstart', function (e) {\n                e.preventDefault();\n            });\n        };\n        Slider.Drag.prototype = {\n            dragstart: function (e) {\n                this.owner._activeDragHandle = this;\n                this.draggable.userEvents.cancel();\n                this._dragstart(e);\n                this.dragend();\n            },\n            _dragstart: function (e) {\n                var that = this, owner = that.owner, options = that.options;\n                if (!options.enabled) {\n                    e.preventDefault();\n                    return;\n                }\n                this.owner._activeDragHandle = this;\n                owner.element.off(MOUSE_OVER);\n                owner.wrapper.find('.' + STATE_FOCUSED).removeClass(STATE_FOCUSED + ' ' + STATE_SELECTED);\n                that.element.addClass(STATE_FOCUSED + ' ' + STATE_SELECTED);\n                $(document.documentElement).css('cursor', 'pointer');\n                that.dragableArea = owner._getDraggableArea();\n                that.step = math.max(options.smallStep * (owner._maxSelection / owner._distance()), 0);\n                if (that.type) {\n                    that.selectionStart = options.selectionStart;\n                    that.selectionEnd = options.selectionEnd;\n                    owner._setZIndex(that.type);\n                } else {\n                    that.oldVal = that.val = options.value;\n                }\n                that._removeTooltip(true);\n                that._createTooltip();\n            },\n            _createTooltip: function () {\n                var that = this, owner = that.owner, tooltip = that.options.tooltip, html = '', wnd = $(window), tooltipTemplate, colloutCssClass;\n                if (!tooltip.enabled) {\n                    return;\n                }\n                if (tooltip.template) {\n                    tooltipTemplate = that.tooltipTemplate = kendo.template(tooltip.template);\n                }\n                $('.k-slider-tooltip').remove();\n                that.tooltipDiv = $('<div class=\\'k-widget k-tooltip k-slider-tooltip\\'><!-- --></div>').appendTo(document.body);\n                html = owner._getFormattedValue(that.val || owner.value(), that);\n                if (!that.type) {\n                    colloutCssClass = 'k-callout-' + (owner._isHorizontal ? 's' : 'e');\n                    that.tooltipInnerDiv = '<div class=\\'k-callout ' + colloutCssClass + '\\'><!-- --></div>';\n                    html += that.tooltipInnerDiv;\n                }\n                that.tooltipDiv.html(html);\n                that._scrollOffset = {\n                    top: wnd.scrollTop(),\n                    left: wnd.scrollLeft()\n                };\n                that.moveTooltip();\n            },\n            drag: function (e) {\n                var that = this, owner = that.owner, x = e.x.location, y = e.y.location, startPoint = that.dragableArea.startPoint, endPoint = that.dragableArea.endPoint, slideParams;\n                e.preventDefault();\n                if (owner._isHorizontal) {\n                    if (owner._isRtl) {\n                        that.val = that.constrainValue(x, startPoint, endPoint, x < endPoint);\n                    } else {\n                        that.val = that.constrainValue(x, startPoint, endPoint, x >= endPoint);\n                    }\n                } else {\n                    that.val = that.constrainValue(y, endPoint, startPoint, y <= endPoint);\n                }\n                if (that.oldVal != that.val) {\n                    that.oldVal = that.val;\n                    if (that.type) {\n                        if (that.type == 'firstHandle') {\n                            if (that.val < that.selectionEnd) {\n                                that.selectionStart = that.val;\n                            } else {\n                                that.selectionStart = that.selectionEnd = that.val;\n                            }\n                        } else {\n                            if (that.val > that.selectionStart) {\n                                that.selectionEnd = that.val;\n                            } else {\n                                that.selectionStart = that.selectionEnd = that.val;\n                            }\n                        }\n                        slideParams = {\n                            values: [\n                                that.selectionStart,\n                                that.selectionEnd\n                            ],\n                            value: [\n                                that.selectionStart,\n                                that.selectionEnd\n                            ]\n                        };\n                    } else {\n                        slideParams = { value: that.val };\n                    }\n                    owner.trigger(SLIDE, slideParams);\n                }\n                that._updateTooltip(that.val);\n            },\n            _updateTooltip: function (val) {\n                var that = this, options = that.options, tooltip = options.tooltip, html = '';\n                if (!tooltip.enabled) {\n                    return;\n                }\n                if (!that.tooltipDiv) {\n                    that._createTooltip();\n                }\n                html = that.owner._getFormattedValue(round(val), that);\n                if (!that.type) {\n                    html += that.tooltipInnerDiv;\n                }\n                that.tooltipDiv.html(html);\n                that.moveTooltip();\n            },\n            dragcancel: function () {\n                this.owner._refresh();\n                $(document.documentElement).css('cursor', '');\n                return this._end();\n            },\n            dragend: function () {\n                var that = this, owner = that.owner;\n                $(document.documentElement).css('cursor', '');\n                if (that.type) {\n                    owner._update(that.selectionStart, that.selectionEnd);\n                } else {\n                    owner._update(that.val);\n                    that.draggable.userEvents._disposeAll();\n                }\n                that.draggable.userEvents.cancel();\n                return that._end();\n            },\n            _end: function () {\n                var that = this, owner = that.owner;\n                owner._focusWithMouse(that.element);\n                owner.element.on(MOUSE_OVER);\n                return false;\n            },\n            _removeTooltip: function (noAnimation) {\n                var that = this, owner = that.owner;\n                if (that.tooltipDiv && owner.options.tooltip.enabled && owner.options.enabled) {\n                    if (noAnimation) {\n                        that.tooltipDiv.remove();\n                        that.tooltipDiv = null;\n                    } else {\n                        that.tooltipDiv.fadeOut('slow', function () {\n                            $(this).remove();\n                            that.tooltipDiv = null;\n                        });\n                    }\n                }\n            },\n            moveTooltip: function () {\n                var that = this, owner = that.owner, top = 0, left = 0, element = that.element, offset = kendo.getOffset(element), margin = 8, viewport = $(window), callout = that.tooltipDiv.find('.k-callout'), width = outerWidth(that.tooltipDiv), height = outerHeight(that.tooltipDiv), dragHandles, sdhOffset, diff, anchorSize;\n                if (that.type) {\n                    dragHandles = owner.wrapper.find(DRAG_HANDLE);\n                    offset = kendo.getOffset(dragHandles.eq(0));\n                    sdhOffset = kendo.getOffset(dragHandles.eq(1));\n                    if (owner._isHorizontal) {\n                        top = sdhOffset.top;\n                        left = offset.left + (sdhOffset.left - offset.left) / 2;\n                    } else {\n                        top = offset.top + (sdhOffset.top - offset.top) / 2;\n                        left = sdhOffset.left;\n                    }\n                    anchorSize = outerWidth(dragHandles.eq(0)) + 2 * margin;\n                } else {\n                    top = offset.top;\n                    left = offset.left;\n                    anchorSize = outerWidth(element) + 2 * margin;\n                }\n                if (owner._isHorizontal) {\n                    left -= parseInt((width - owner._outerSize(element)) / 2, 10);\n                    top -= height + margin + (callout.length ? callout.height() : 0);\n                } else {\n                    top -= parseInt((height - owner._outerSize(element)) / 2, 10);\n                    left -= width + margin + (callout.length ? callout.width() : 0);\n                }\n                if (owner._isHorizontal) {\n                    diff = that._flip(top, height, anchorSize, outerHeight(viewport) + that._scrollOffset.top);\n                    top += diff;\n                    left += that._fit(left, width, outerWidth(viewport) + that._scrollOffset.left);\n                } else {\n                    diff = that._flip(left, width, anchorSize, outerWidth(viewport) + that._scrollOffset.left);\n                    top += that._fit(top, height, outerHeight(viewport) + that._scrollOffset.top);\n                    left += diff;\n                }\n                if (diff > 0 && callout) {\n                    callout.removeClass();\n                    callout.addClass('k-callout k-callout-' + (owner._isHorizontal ? 'n' : 'w'));\n                }\n                that.tooltipDiv.css({\n                    top: top,\n                    left: left\n                });\n            },\n            _fit: function (position, size, viewPortEnd) {\n                var output = 0;\n                if (position + size > viewPortEnd) {\n                    output = viewPortEnd - (position + size);\n                }\n                if (position < 0) {\n                    output = -position;\n                }\n                return output;\n            },\n            _flip: function (offset, size, anchorSize, viewPortEnd) {\n                var output = 0;\n                if (offset + size > viewPortEnd) {\n                    output += -(anchorSize + size);\n                }\n                if (offset + output < 0) {\n                    output += anchorSize + size;\n                }\n                return output;\n            },\n            constrainValue: function (position, min, max, maxOverflow) {\n                var that = this, val = 0;\n                if (min < position && position < max) {\n                    val = that.owner._getValueFromPosition(position, that.dragableArea);\n                } else {\n                    if (maxOverflow) {\n                        val = that.options.max;\n                    } else {\n                        val = that.options.min;\n                    }\n                }\n                return val;\n            }\n        };\n        kendo.ui.plugin(Slider);\n        var RangeSlider = SliderBase.extend({\n            init: function (element, options) {\n                var that = this, inputs = $(element).find('input'), firstInput = inputs.eq(0)[0], secondInput = inputs.eq(1)[0];\n                firstInput.type = 'text';\n                secondInput.type = 'text';\n                if (options && options.showButtons) {\n                    if (window.console) {\n                        window.console.warn('showbuttons option is not supported for the range slider, ignoring');\n                    }\n                    options.showButtons = false;\n                }\n                options = extend({}, {\n                    selectionStart: parseAttr(firstInput, 'value'),\n                    min: parseAttr(firstInput, 'min'),\n                    max: parseAttr(firstInput, 'max'),\n                    smallStep: parseAttr(firstInput, 'step')\n                }, {\n                    selectionEnd: parseAttr(secondInput, 'value'),\n                    min: parseAttr(secondInput, 'min'),\n                    max: parseAttr(secondInput, 'max'),\n                    smallStep: parseAttr(secondInput, 'step')\n                }, options);\n                if (options && options.enabled === undefined) {\n                    options.enabled = !inputs.is('[disabled]');\n                }\n                SliderBase.fn.init.call(that, element, options);\n                options = that.options;\n                if (!defined(options.selectionStart) || options.selectionStart === null) {\n                    options.selectionStart = options.min;\n                    inputs.eq(0).prop('value', formatValue(options.min));\n                }\n                if (!defined(options.selectionEnd) || options.selectionEnd === null) {\n                    options.selectionEnd = options.max;\n                    inputs.eq(1).prop('value', formatValue(options.max));\n                }\n                var dragHandles = that.wrapper.find(DRAG_HANDLE);\n                this._selection = new RangeSlider.Selection(dragHandles, that, options);\n                that._firstHandleDrag = new Slider.Drag(dragHandles.eq(0), 'firstHandle', that, options);\n                that._lastHandleDrag = new Slider.Drag(dragHandles.eq(1), 'lastHandle', that, options);\n            },\n            options: {\n                name: 'RangeSlider',\n                leftDragHandleTitle: 'drag',\n                rightDragHandleTitle: 'drag',\n                tooltip: { format: '{0:#,#.##}' },\n                selectionStart: null,\n                selectionEnd: null\n            },\n            enable: function (enable) {\n                var that = this, options = that.options, clickHandler;\n                that.disable();\n                if (enable === false) {\n                    return;\n                }\n                that.wrapper.removeClass(STATE_DISABLED).addClass(STATE_DEFAULT);\n                that.wrapper.find('input').removeAttr(DISABLED);\n                clickHandler = function (e) {\n                    var touch = getTouches(e)[0];\n                    if (!touch) {\n                        return;\n                    }\n                    var mousePosition = that._isHorizontal ? touch.location.pageX : touch.location.pageY, dragableArea = that._getDraggableArea(), val = that._getValueFromPosition(mousePosition, dragableArea), target = $(e.target), from, to, drag;\n                    if (target.hasClass('k-draghandle')) {\n                        that.wrapper.find('.' + STATE_FOCUSED).removeClass(STATE_FOCUSED + ' ' + STATE_SELECTED);\n                        target.addClass(STATE_FOCUSED + ' ' + STATE_SELECTED);\n                        return;\n                    }\n                    if (val < options.selectionStart) {\n                        from = val;\n                        to = options.selectionEnd;\n                        drag = that._firstHandleDrag;\n                    } else if (val > that.selectionEnd) {\n                        from = options.selectionStart;\n                        to = val;\n                        drag = that._lastHandleDrag;\n                    } else {\n                        if (val - options.selectionStart <= options.selectionEnd - val) {\n                            from = val;\n                            to = options.selectionEnd;\n                            drag = that._firstHandleDrag;\n                        } else {\n                            from = options.selectionStart;\n                            to = val;\n                            drag = that._lastHandleDrag;\n                        }\n                    }\n                    drag.dragstart(e);\n                    that._setValueInRange(from, to);\n                    that._focusWithMouse(drag.element);\n                };\n                that.wrapper.find(TICK_SELECTOR + ', ' + TRACK_SELECTOR).on(TRACK_MOUSE_DOWN, clickHandler).end().on(TRACK_MOUSE_DOWN, function () {\n                    $(document.documentElement).one('selectstart', kendo.preventDefault);\n                }).on(TRACK_MOUSE_UP, function () {\n                    if (that._activeDragHandle) {\n                        that._activeDragHandle._end();\n                    }\n                });\n                that.wrapper.find(DRAG_HANDLE).attr(TABINDEX, 0).on(MOUSE_UP, function () {\n                    that._setTooltipTimeout();\n                }).on(CLICK, function (e) {\n                    that._focusWithMouse(e.target);\n                    e.preventDefault();\n                }).on(FOCUS, proxy(that._focus, that)).on(BLUR, proxy(that._blur, that));\n                that.wrapper.find(DRAG_HANDLE).off(KEY_DOWN, kendo.preventDefault).eq(0).on(KEY_DOWN, proxy(function (e) {\n                    this._keydown(e, 'firstHandle');\n                }, that)).end().eq(1).on(KEY_DOWN, proxy(function (e) {\n                    this._keydown(e, 'lastHandle');\n                }, that));\n                that.options.enabled = true;\n            },\n            disable: function () {\n                var that = this;\n                that.wrapper.removeClass(STATE_DEFAULT).addClass(STATE_DISABLED);\n                that.wrapper.find('input').prop(DISABLED, DISABLED);\n                that.wrapper.find(TICK_SELECTOR + ', ' + TRACK_SELECTOR).off(TRACK_MOUSE_DOWN).off(TRACK_MOUSE_UP);\n                that.wrapper.find(DRAG_HANDLE).attr(TABINDEX, -1).off(MOUSE_UP).off(KEY_DOWN).off(CLICK).off(FOCUS).off(BLUR);\n                that.options.enabled = false;\n            },\n            _keydown: function (e, handle) {\n                var that = this, selectionStartValue = that.options.selectionStart, selectionEndValue = that.options.selectionEnd, dragSelectionStart, dragSelectionEnd, activeHandleDrag;\n                if (e.keyCode in that._keyMap) {\n                    that._clearTooltipTimeout();\n                    if (handle == 'firstHandle') {\n                        activeHandleDrag = that._activeHandleDrag = that._firstHandleDrag;\n                        selectionStartValue = that._keyMap[e.keyCode](selectionStartValue);\n                        if (selectionStartValue > selectionEndValue) {\n                            selectionEndValue = selectionStartValue;\n                        }\n                    } else {\n                        activeHandleDrag = that._activeHandleDrag = that._lastHandleDrag;\n                        selectionEndValue = that._keyMap[e.keyCode](selectionEndValue);\n                        if (selectionStartValue > selectionEndValue) {\n                            selectionStartValue = selectionEndValue;\n                        }\n                    }\n                    that._setValueInRange(round(selectionStartValue), round(selectionEndValue));\n                    dragSelectionStart = Math.max(selectionStartValue, that.options.selectionStart);\n                    dragSelectionEnd = Math.min(selectionEndValue, that.options.selectionEnd);\n                    activeHandleDrag.selectionEnd = Math.max(dragSelectionEnd, that.options.selectionStart);\n                    activeHandleDrag.selectionStart = Math.min(dragSelectionStart, that.options.selectionEnd);\n                    activeHandleDrag._updateTooltip(that.value()[that._activeHandle]);\n                    e.preventDefault();\n                }\n            },\n            _update: function (selectionStart, selectionEnd) {\n                var that = this, values = that.value();\n                var change = values[0] != selectionStart || values[1] != selectionEnd;\n                that.value([\n                    selectionStart,\n                    selectionEnd\n                ]);\n                if (change) {\n                    that.trigger(CHANGE, {\n                        values: [\n                            selectionStart,\n                            selectionEnd\n                        ],\n                        value: [\n                            selectionStart,\n                            selectionEnd\n                        ]\n                    });\n                }\n            },\n            value: function (value) {\n                if (value && value.length) {\n                    return this._value(value[0], value[1]);\n                } else {\n                    return this._value();\n                }\n            },\n            _value: function (start, end) {\n                var that = this, options = that.options, selectionStart = options.selectionStart, selectionEnd = options.selectionEnd;\n                if (isNaN(start) && isNaN(end)) {\n                    return [\n                        selectionStart,\n                        selectionEnd\n                    ];\n                } else {\n                    start = round(start);\n                    end = round(end);\n                }\n                if (start >= options.min && start <= options.max && end >= options.min && end <= options.max && start <= end) {\n                    if (selectionStart != start || selectionEnd != end) {\n                        that.element.find('input').eq(0).prop('value', formatValue(start)).end().eq(1).prop('value', formatValue(end));\n                        options.selectionStart = start;\n                        options.selectionEnd = end;\n                        that._refresh();\n                        that._refreshAriaAttr(start, end);\n                    }\n                }\n            },\n            values: function (start, end) {\n                if (isArray(start)) {\n                    return this._value(start[0], start[1]);\n                } else {\n                    return this._value(start, end);\n                }\n            },\n            _refresh: function () {\n                var that = this, options = that.options;\n                that.trigger(MOVE_SELECTION, {\n                    values: [\n                        options.selectionStart,\n                        options.selectionEnd\n                    ],\n                    value: [\n                        options.selectionStart,\n                        options.selectionEnd\n                    ]\n                });\n                if (options.selectionStart == options.max && options.selectionEnd == options.max) {\n                    that._setZIndex('firstHandle');\n                }\n            },\n            _refreshAriaAttr: function (start, end) {\n                var that = this, dragHandles = that.wrapper.find(DRAG_HANDLE), drag = that._activeHandleDrag, formattedValue;\n                formattedValue = that._getFormattedValue([\n                    start,\n                    end\n                ], drag);\n                dragHandles.eq(0).attr('aria-valuenow', start);\n                dragHandles.eq(1).attr('aria-valuenow', end);\n                dragHandles.attr('aria-valuetext', formattedValue);\n            },\n            _setValueInRange: function (selectionStart, selectionEnd) {\n                var options = this.options;\n                selectionStart = math.max(math.min(selectionStart, options.max), options.min);\n                selectionEnd = math.max(math.min(selectionEnd, options.max), options.min);\n                if (selectionStart == options.max && selectionEnd == options.max) {\n                    this._setZIndex('firstHandle');\n                }\n                this._update(math.min(selectionStart, selectionEnd), math.max(selectionStart, selectionEnd));\n            },\n            _setZIndex: function (type) {\n                this.wrapper.find(DRAG_HANDLE).each(function (index) {\n                    $(this).css('z-index', type == 'firstHandle' ? 1 - index : index);\n                });\n            },\n            _formResetHandler: function () {\n                var that = this, options = that.options;\n                setTimeout(function () {\n                    var inputs = that.element.find('input');\n                    var start = inputs[0].value;\n                    var end = inputs[1].value;\n                    that.values(start === '' || isNaN(start) ? options.min : start, end === '' || isNaN(end) ? options.max : end);\n                });\n            },\n            destroy: function () {\n                var that = this;\n                SliderBase.fn.destroy.call(that);\n                that.wrapper.off(NS).find(TICK_SELECTOR + ', ' + TRACK_SELECTOR).off(NS).end().find(DRAG_HANDLE).off(NS);\n                that._firstHandleDrag.draggable.destroy();\n                that._lastHandleDrag.draggable.destroy();\n            }\n        });\n        RangeSlider.Selection = function (dragHandles, that, options) {\n            function moveSelection(value) {\n                value = value || [];\n                var selectionStartValue = value[0] - options.min, selectionEndValue = value[1] - options.min, selectionStartIndex = math.ceil(round(selectionStartValue / options.smallStep)), selectionEndIndex = math.ceil(round(selectionEndValue / options.smallStep)), selectionStart = that._pixelSteps[selectionStartIndex], selectionEnd = that._pixelSteps[selectionEndIndex], halfHandle = parseInt(that._outerSize(dragHandles.eq(0)) / 2, 10), rtlCorrection = that._isRtl ? 2 : 0;\n                dragHandles.eq(0).css(that._position, selectionStart - halfHandle - rtlCorrection).end().eq(1).css(that._position, selectionEnd - halfHandle - rtlCorrection);\n                makeSelection(selectionStart, selectionEnd);\n            }\n            function makeSelection(selectionStart, selectionEnd) {\n                var selection, selectionPosition, selectionDiv = that._trackDiv.find('.k-slider-selection');\n                selection = math.abs(selectionStart - selectionEnd);\n                selectionDiv[that._sizeFn](selection);\n                if (that._isRtl) {\n                    selectionPosition = math.max(selectionStart, selectionEnd);\n                    selectionDiv.css('right', that._maxSelection - selectionPosition - 1);\n                } else {\n                    selectionPosition = math.min(selectionStart, selectionEnd);\n                    selectionDiv.css(that._position, selectionPosition - 1);\n                }\n            }\n            moveSelection(that.value());\n            that.bind([\n                CHANGE,\n                SLIDE,\n                MOVE_SELECTION\n            ], function (e) {\n                moveSelection(e.values);\n            });\n        };\n        kendo.ui.plugin(RangeSlider);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}