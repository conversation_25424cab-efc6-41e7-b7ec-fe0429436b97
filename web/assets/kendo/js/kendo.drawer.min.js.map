{"version": 3, "sources": ["kendo.drawer.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "ui", "Widget", "SHOW", "HIDE", "ITEMCLICK", "PUSH", "OVERLAY", "LEFT", "RIGHT", "Drawer", "extend", "init", "element", "options", "userEvents", "tap", "that", "this", "fn", "call", "_element", "_wrapper", "position", "_mode", "mini", "_miniMode", "_initDrawerItems", "mode", "_setBodyOffset", "UserEvents", "document", "body", "outerWrapper", "fastTap", "allowSelection", "e", "contains", "drawerItemsWrapper", "event", "target", "_itemClick", "visible", "trigger", "sender", "hide", "preventDefault", "swipeToOpen", "bind", "_start", "_update", "_end", "minHeight", "css", "contentElement", "children", "first", "drawerElement", "template", "addClass", "wrap", "parent", "drawerContainer", "prepend", "append", "overlayMiniOffset", "outerWidth", "leftPositioned", "drawerItems", "find", "separatorItems", "_selectedItemIndex", "removeClass", "eq", "overlayContainer", "miniWidth", "width", "miniTemplate", "_miniTemplate", "html", "min<PERSON><PERSON><PERSON>", "show", "isExpanded", "hasClass", "_selectItem", "value", "Math", "abs", "x", "velocity", "y", "triggeredByInput", "cancel", "drawerMini", "_overlay", "_push", "shouldShow", "elementWidth", "pastHalf", "velocityThreshold", "moveEventArgs", "updatedPosition", "delta", "limitedPosition", "min", "max", "stopPropagation", "item", "selectedItemIndex", "index", "length", "closest", "destroy", "name", "events", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,gBAAiB,oBAAqBD,IAC/C,WAsWE,MA9VC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAASD,EAAGC,OAAQC,EAAO,OAAQC,EAAO,OAAQC,EAAY,YAAaC,EAAO,OAAQC,EAAU,UAAWC,EAAO,OAAQC,EAAQ,QAC3KC,EAASX,EAAME,GAAGC,OAAOS,QACzBC,KAAM,SAAUC,EAASC,GAAnB,GAEEC,GAkBAC,EAnBAC,EAAOC,IAEXhB,GAAOiB,GAAGP,KAAKQ,KAAKF,KAAML,EAASC,GACnCA,EAAUG,EAAKH,QACfG,EAAKI,SAASR,GACdI,EAAKK,SAAST,GACdI,EAAKM,WACLN,EAAKO,QACDV,EAAQW,MACRR,EAAKS,YAETT,EAAKU,mBACDb,EAAQW,MAAwB,WAAhBX,EAAQc,MACxBX,EAAKY,iBAETd,EAAaG,KAAKH,WAAa,GAAIhB,GAAM+B,WAAWhB,EAAQc,MAAQrB,EAAUV,EAAEkC,SAASC,MAAQd,KAAKe,cAClGC,SAAS,EACTC,gBAAgB,IAEhBnB,EAAM,SAAUoB,GACZvC,EAAEwC,SAASpB,EAAKqB,mBAAmB,GAAIF,EAAEG,MAAMC,SAC/CvB,EAAKwB,WAAWL,GAEhBnB,EAAKyB,UAAYzB,EAAK0B,QAAQ,QAAUC,OAAQ1B,SAChDD,EAAK4B,OACLT,EAAEU,mBAGN5B,KAAKJ,QAAQiC,aACbhC,EAAWiC,KAAK,QAAS,SAAUZ,GAC/BnB,EAAKgC,OAAOb,KAEhBrB,EAAWiC,KAAK,OAAQ,SAAUZ,GAC9BnB,EAAKiC,QAAQd,KAEjBrB,EAAWiC,KAAK,MAAO,SAAUZ,GAC7BnB,EAAKkC,KAAKf,KAEdrB,EAAWiC,KAAK,MAAOhC,IAEvBD,EAAWiC,KAAK,QAAShC,GAEzBF,EAAQsC,WACRnC,EAAKgB,aAAaoB,IAAI,aAAcvC,EAAQsC,YAGpD/B,SAAU,WAAA,GACFJ,GAAOC,KACPL,EAAUI,EAAKJ,QACfC,EAAUG,EAAKH,QACfwC,EAAiBrC,EAAKqC,eAAiBzC,EAAQ0C,WAAWC,OAC9DvC,GAAKwC,cAAgB5D,EAAEiB,EAAQ4C,UAC/BJ,EAAeK,SAAS,oBACxB9C,EAAQ8C,SAAS,sBAErBrC,SAAU,WAAA,GACFR,GAAUI,KAAKJ,QACf2C,EAAgBvC,KAAKuC,cACrB5C,EAAUK,KAAKL,QACfyC,EAAiBpC,KAAKoC,eACtBhB,EAAqBpB,KAAKoB,mBAAqBmB,EAAcG,KAAK,sCAAwCC,SAC1GC,EAAkB5C,KAAK4C,gBAAkBxB,EAAmBsB,KAAK,0CAA4CC,SAC7G5B,EAAef,KAAKe,aAAe6B,EAAgBF,KAAK,wCAA0CC,QAClG/C,GAAQc,MAAQrB,EAChBV,EAAEkC,SAASC,MAAM+B,QAAQ9B,IAEzBA,EAAa+B,OAAOV,GACpBzC,EAAQkD,QAAQ9B,KAGxBJ,eAAgB,WACZ,GAAIoC,GAAoB/C,KAAK4C,gBAAgBI,YACzChD,MAAKiD,eACLtE,EAAEkC,SAASC,MAAMqB,IAAI,eAAgBY,GAErCpE,EAAEkC,SAASC,MAAMqB,IAAI,gBAAiBY,IAG9CtC,iBAAkB,WAAA,GACVW,GAAqBpB,KAAKoB,mBAC1B8B,EAAc9B,EAAmB+B,KAAK,6BACtCC,EAAiBhC,EAAmB+B,KAAK,iCAC7CD,GAAYT,SAAS,iBACrBW,EAAeX,SAAS,oCACpBzC,KAAKqD,oBAAsB,IAC3BH,EAAYI,YAAY,oBACxBJ,EAAYK,GAAGvD,KAAKqD,oBAAoBZ,SAAS,sBAGzDnC,MAAO,WAAA,GAGCkD,GAFA5D,EAAUI,KAAKJ,QACfmB,EAAef,KAAKe,YAEpBnB,GAAQc,MAAQtB,EAChB2B,EAAa0B,SAAS,YAAcrD,IAEpC2B,EAAa0B,SAAS,YAAcpD,GACpCmE,EAAmBxD,KAAKwD,iBAAmB7E,EAAE,iCAC7CoC,EAAa8B,QAAQW,KAG7BhD,UAAW,WAAA,GACHZ,GAAUI,KAAKJ,QACfmB,EAAef,KAAKe,aACpB0C,EAAY7D,EAAQW,KAAKmD,MACzBC,EAAe3D,KAAK4D,cAAgBhE,EAAQW,KAAKiC,UAAY7D,EAAEiB,EAAQW,KAAKiC,UAC5EpB,EAAqBpB,KAAKoB,kBAC9BL,GAAa0B,SAAS,sBAClBkB,GACAvC,EAAmByC,KAAKF,GAExBF,GACArC,EAAmBsC,MAAMD,GAE7BzD,KAAK8D,SAAWlE,EAAQW,KAAKmD,OAAS1D,KAAK4C,gBAAgBc,SAE/DK,KAAM,WAAA,GACEnB,GAAkB5C,KAAK4C,gBACvBhD,EAAUI,KAAKJ,QACfoE,EAAapB,EAAgBqB,SAAS,qBACtCN,EAAe3D,KAAK4D,cACpBrB,EAAgBvC,KAAKuC,cACrBnB,EAAqBpB,KAAKoB,kBACzB4C,KACDpB,EAAgBH,SAAS,qBACzBzC,KAAKwB,SAAU,GAEfmC,IACAvC,EAAmByC,KAAKtB,GACxBvC,KAAKS,mBACLT,KAAKkE,eAELtE,EAAQW,KACRa,EAAmBsC,MAAM9D,EAAQ8D,OAEjCd,EAAgBc,MAAM9D,EAAQ8D,OAEd,WAAhB9D,EAAQc,OACRV,KAAKwD,iBAAiBO,OACtB/D,KAAKwB,SAAU,IAGvBG,KAAM,WAAA,GACE5B,GAAOC,KACP4C,EAAkB7C,EAAK6C,gBACvBhD,EAAUI,KAAKJ,QACfwB,EAAqBpB,KAAKoB,mBAC1BuC,EAAe3D,KAAK4D,cACpBH,EAAY7D,EAAQW,MAAQX,EAAQW,KAAKmD,KACzC1D,MAAK4D,gBACLxC,EAAmByC,KAAKF,GACxB5D,EAAKU,mBACLT,KAAKkE,eAELtE,EAAQW,KAEJa,EAAmBsC,MADnBD,EACyBA,EAEA,IAG7Bb,EAAgBc,MAAM,IAEtB1D,KAAKwB,UACLoB,EAAgBU,YAAY,qBAC5BtD,KAAKwB,SAAU,GAEC,WAAhB5B,EAAQc,MACRV,KAAKwD,iBAAiB7B,QAG9BtB,SAAU,SAAU8D,GAAV,GACFvE,GAAUI,KAAKJ,QACfmB,EAAef,KAAKe,aACpBV,EAAW8D,GAASvE,EAAQS,QAC5BA,IAAYd,GACZwB,EAAauC,YAAY,YAAchE,GACvCyB,EAAa0B,SAAS,YAAclD,KAEpCwB,EAAauC,YAAY,YAAc/D,GACvCwB,EAAa0B,SAAS,YAAcnD,IAExCU,KAAKiD,eAAiB5C,IAAaf,GAEvCyC,OAAQ,SAAUb,GAAV,GACAnB,GAAOC,KACPJ,EAAUI,KAAKJ,QACfgD,EAAkB5C,KAAK4C,gBACvBxB,EAAqBpB,KAAKoB,mBAC1BvB,EAAaqB,EAAEQ,MACnB,OAAI0C,MAAKC,IAAInD,EAAEoD,EAAEC,UAAYH,KAAKC,IAAInD,EAAEsD,EAAED,WAAa1F,EAAM4F,iBAAiBvD,EAAEG,QAC5ExB,EAAW6E,SACX,IAEA1E,KAAK2E,YACLvD,EAAmByC,KAAK9D,EAAKwC,eAE7B3C,EAAQW,KACRa,EAAmBe,IAAI,aAAc,QAErCS,EAAgBT,IAAI,aAAc,QAElB,WAAhBvC,EAAQc,MACRV,KAAKwD,iBAAiBO,OAT1B,IAYJ/B,QAAS,SAAUd,GAAV,GACDtB,GAAUI,KAAKJ,QACfc,EAAOd,EAAQc,IACP,YAARA,EACAV,KAAK4E,SAAS1D,GAEdlB,KAAK6E,MAAM3D,IAGnBe,KAAM,SAAUf,GAAV,GAQE4D,GAPAP,EAAWrD,EAAEoD,EAAEC,SACf3E,EAAUI,KAAKJ,QACfgD,EAAkB5C,KAAK4C,gBACvBxB,EAAqBpB,KAAKoB,mBAC1B2D,EAAe3D,EAAmBsC,QAClCsB,EAAWD,EAAenF,EAAQ8D,MAAQ,EAC1CuB,EAAoB,EAEpBrF,GAAQW,KACRa,EAAmBe,IAAI,aAAc,oBAErCS,EAAgBT,IAAI,aAAc,oBAGlC2C,EADA9E,KAAKiD,eACQsB,GAAYU,IAAsBV,EAAWU,GAAqBD,GAElET,EAAWU,IAAsBV,GAAYU,GAAqBD,GAE/EF,EACI9E,KAAKyB,QAAQ,QAAUC,OAAQ1B,QAC/BkB,EAAEU,iBACF5B,KAAK2B,QAEL3B,KAAK+D,OAGL/D,KAAKyB,QAAQ,QAAUC,OAAQ1B,QAC/BkB,EAAEU,iBACF5B,KAAK+D,QAEL/D,KAAK2B,QAIjBiD,SAAU,SAAUM,GAAV,GACFtC,GAAkB5C,KAAK4C,gBACvBxB,EAAqBpB,KAAKoB,mBAC1B2D,EAAe3D,EAAmBsC,QAClC9D,EAAUI,KAAKJ,QACfkE,EAAWlE,EAAQW,MAAQX,EAAQW,KAAKmD,OAAS1D,KAAK8D,UAAY,EAElEqB,EACcJ,GAAgB/E,KAAKiD,eAAiBiC,EAAcZ,EAAEc,OAASF,EAAcZ,EAAEc,OAF7FC,EAGcjB,KAAKkB,IAAIlB,KAAKmB,IAAIJ,EAAiBrB,GAAWlE,EAAQ8D,MACxEwB,GAAc7D,MAAMO,iBACpBsD,EAAc7D,MAAMmE,kBAChB5F,EAAQW,KACRa,EAAmBsC,MAAM2B,GAEzBzC,EAAgBc,MAAM2B,IAG9BR,MAAO,SAAUK,GAAV,GACCtC,GAAkB5C,KAAK4C,gBACvBxB,EAAqBpB,KAAKoB,mBAC1B2D,EAAe3D,EAAmBsC,QAClC9D,EAAUI,KAAKJ,QACfkE,EAAWlE,EAAQW,MAAQX,EAAQW,KAAKmD,OAAS1D,KAAK8D,UAAY,EAElEqB,EACcJ,GAAgB/E,KAAKiD,eAAiBiC,EAAcZ,EAAEc,OAASF,EAAcZ,EAAEc,OAF7FC,EAGcjB,KAAKkB,IAAIlB,KAAKmB,IAAIJ,EAAiBrB,GAAWlE,EAAQ8D,MACxEwB,GAAc7D,MAAMO,iBACpBsD,EAAc7D,MAAMmE,kBAChB5F,EAAQW,KACRa,EAAmBsC,MAAM2B,GAEzBzC,EAAgBc,MAAM2B,IAG9BnB,YAAa,SAAUuB,GACnB,GAAIC,EACJ,OAAID,IACAA,EAAKhD,SAAS,oBACdzC,KAAKyB,QAAQ,aACTgE,KAAMA,EACN/D,OAAQ1B,OAEZA,KAAKqD,mBAAqBoC,EAAKE,QAC/B,IAEJD,EAAoB1F,KAAKqD,mBACrBqC,GACA1F,KAAKoB,mBAAmB+B,KAAK,6BAA+BI,GAAGmC,GAAmBjD,SAAS,oBAF/FiD,IAKJnE,WAAY,SAAUL,GAAV,GAEJuE,GADA1F,EAAOC,IAEPrB,GAAEuC,EAAEG,MAAMC,QAAQ6B,KAAK,kBAAkByC,OAAS,EAClDH,EAAO9G,EAAEuC,EAAEG,MAAMC,QAAQ6B,KAAK,kBACvBxE,EAAEuC,EAAEG,MAAMC,QAAQuE,QAAQ,kBAAkBD,OAAS,EAC5DH,EAAO9G,EAAEuC,EAAEG,MAAMC,QAAQuE,QAAQ,kBAC1BlH,EAAEuC,EAAEG,MAAMC,QAAQ2C,SAAS,oBAClCwB,EAAO9G,EAAEuC,EAAEG,MAAMC,SAErBvB,EAAKqB,mBAAmB+B,KAAK,kBAAkBG,YAAY,oBAC3DvD,EAAKmE,YAAYuB,IAErBK,QAAS,WACL,GAAIlG,GAAUI,KAAKJ,OACC,YAAhBA,EAAQc,OACJV,KAAKiD,eACLtE,EAAEkC,SAASC,MAAMqB,IAAI,eAAgB,GAErCxD,EAAEkC,SAASC,MAAMqB,IAAI,gBAAiB,IAG9CnD,EAAOiB,GAAG6F,QAAQ5F,KAAKF,MACvBA,KAAKH,WAAWiG,UAChBjH,EAAMiH,QAAQ9F,KAAKL,SACnBK,KAAKL,QAAUK,KAAK4C,gBAAkB5C,KAAKuC,cAAgBvC,KAAKoB,mBAAqBpB,KAAK4D,cAAgB,MAE9GhE,SACImG,KAAM,SACN1F,SAAUf,EACVoB,KAAM,UACNmB,aAAa,EACb6B,MAAO,IACPnD,MAAM,EACNiC,SAAU,IAEdwD,QACI9G,EACAD,EACAE,IAGRN,GAAME,GAAGkH,OAAOzG,IAClBV,OAAOD,MAAMqH,QACRpH,OAAOD,OACE,kBAAVH,SAAwBA,OAAOyH,IAAMzH,OAAS,SAAU0H,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.drawer.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.drawer', ['kendo.userevents'], f);\n}(function () {\n    var __meta__ = {\n        id: 'drawer',\n        name: 'Drawer',\n        category: 'web',\n        description: 'The Kendo Drawer widget provides slide to reveal sidebar',\n        depends: ['userevents']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, Widget = ui.Widget, SHOW = 'show', HIDE = 'hide', ITEMCLICK = 'itemClick', PUSH = 'push', OVERLAY = 'overlay', LEFT = 'left', RIGHT = 'right';\n        var Drawer = kendo.ui.Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                var userEvents;\n                Widget.fn.init.call(this, element, options);\n                options = that.options;\n                that._element(element);\n                that._wrapper(element);\n                that.position();\n                that._mode();\n                if (options.mini) {\n                    that._miniMode();\n                }\n                that._initDrawerItems();\n                if (options.mini && options.mode == 'overlay') {\n                    that._setBodyOffset();\n                }\n                userEvents = this.userEvents = new kendo.UserEvents(options.mode == OVERLAY ? $(document.body) : this.outerWrapper, {\n                    fastTap: true,\n                    allowSelection: true\n                });\n                var tap = function (e) {\n                    if ($.contains(that.drawerItemsWrapper[0], e.event.target)) {\n                        that._itemClick(e);\n                    }\n                    if (that.visible && !that.trigger('hide', { sender: this })) {\n                        that.hide();\n                        e.preventDefault();\n                    }\n                };\n                if (this.options.swipeToOpen) {\n                    userEvents.bind('start', function (e) {\n                        that._start(e);\n                    });\n                    userEvents.bind('move', function (e) {\n                        that._update(e);\n                    });\n                    userEvents.bind('end', function (e) {\n                        that._end(e);\n                    });\n                    userEvents.bind('tap', tap);\n                } else {\n                    userEvents.bind('press', tap);\n                }\n                if (options.minHeight) {\n                    that.outerWrapper.css('min-height', options.minHeight);\n                }\n            },\n            _element: function () {\n                var that = this;\n                var element = that.element;\n                var options = that.options;\n                var contentElement = that.contentElement = element.children().first();\n                that.drawerElement = $(options.template);\n                contentElement.addClass('k-drawer-content');\n                element.addClass('k-drawer k-widget');\n            },\n            _wrapper: function () {\n                var options = this.options;\n                var drawerElement = this.drawerElement;\n                var element = this.element;\n                var contentElement = this.contentElement;\n                var drawerItemsWrapper = this.drawerItemsWrapper = drawerElement.wrap('<div class=\\'k-drawer-items\\'></div>').parent();\n                var drawerContainer = this.drawerContainer = drawerItemsWrapper.wrap('<div class=\\'k-drawer-container\\'></div>').parent();\n                var outerWrapper = this.outerWrapper = drawerContainer.wrap('<div class=\\'k-drawer-wrapper\\'></div>').parent();\n                if (options.mode == OVERLAY) {\n                    $(document.body).prepend(outerWrapper);\n                } else {\n                    outerWrapper.append(contentElement);\n                    element.prepend(outerWrapper);\n                }\n            },\n            _setBodyOffset: function () {\n                var overlayMiniOffset = this.drawerContainer.outerWidth();\n                if (this.leftPositioned) {\n                    $(document.body).css('padding-left', overlayMiniOffset);\n                } else {\n                    $(document.body).css('padding-right', overlayMiniOffset);\n                }\n            },\n            _initDrawerItems: function () {\n                var drawerItemsWrapper = this.drawerItemsWrapper;\n                var drawerItems = drawerItemsWrapper.find('[data-role=\\'drawer-item\\']');\n                var separatorItems = drawerItemsWrapper.find('[data-role=\\'drawer-separator\\']');\n                drawerItems.addClass('k-drawer-item');\n                separatorItems.addClass('k-drawer-item k-drawer-separator');\n                if (this._selectedItemIndex >= 0) {\n                    drawerItems.removeClass('k-state-selected');\n                    drawerItems.eq(this._selectedItemIndex).addClass('k-state-selected');\n                }\n            },\n            _mode: function () {\n                var options = this.options;\n                var outerWrapper = this.outerWrapper;\n                var overlayContainer;\n                if (options.mode == PUSH) {\n                    outerWrapper.addClass('k-drawer-' + PUSH);\n                } else {\n                    outerWrapper.addClass('k-drawer-' + OVERLAY);\n                    overlayContainer = this.overlayContainer = $('<div class=\"k-overlay\"></div>');\n                    outerWrapper.prepend(overlayContainer);\n                }\n            },\n            _miniMode: function () {\n                var options = this.options;\n                var outerWrapper = this.outerWrapper;\n                var miniWidth = options.mini.width;\n                var miniTemplate = this._miniTemplate = options.mini.template && $(options.mini.template);\n                var drawerItemsWrapper = this.drawerItemsWrapper;\n                outerWrapper.addClass('k-drawer-mini-mode');\n                if (miniTemplate) {\n                    drawerItemsWrapper.html(miniTemplate);\n                }\n                if (miniWidth) {\n                    drawerItemsWrapper.width(miniWidth);\n                }\n                this.minWidth = options.mini.width || this.drawerContainer.width();\n            },\n            show: function () {\n                var drawerContainer = this.drawerContainer;\n                var options = this.options;\n                var isExpanded = drawerContainer.hasClass('k-drawer-expanded');\n                var miniTemplate = this._miniTemplate;\n                var drawerElement = this.drawerElement;\n                var drawerItemsWrapper = this.drawerItemsWrapper;\n                if (!isExpanded) {\n                    drawerContainer.addClass('k-drawer-expanded');\n                    this.visible = true;\n                }\n                if (miniTemplate) {\n                    drawerItemsWrapper.html(drawerElement);\n                    this._initDrawerItems();\n                    this._selectItem();\n                }\n                if (options.mini) {\n                    drawerItemsWrapper.width(options.width);\n                } else {\n                    drawerContainer.width(options.width);\n                }\n                if (options.mode == 'overlay') {\n                    this.overlayContainer.show();\n                    this.visible = true;\n                }\n            },\n            hide: function () {\n                var that = this;\n                var drawerContainer = that.drawerContainer;\n                var options = this.options;\n                var drawerItemsWrapper = this.drawerItemsWrapper;\n                var miniTemplate = this._miniTemplate;\n                var miniWidth = options.mini && options.mini.width;\n                if (this._miniTemplate) {\n                    drawerItemsWrapper.html(miniTemplate);\n                    that._initDrawerItems();\n                    this._selectItem();\n                }\n                if (options.mini) {\n                    if (miniWidth) {\n                        drawerItemsWrapper.width(miniWidth);\n                    } else {\n                        drawerItemsWrapper.width('');\n                    }\n                } else {\n                    drawerContainer.width('');\n                }\n                if (this.visible) {\n                    drawerContainer.removeClass('k-drawer-expanded');\n                    this.visible = false;\n                }\n                if (options.mode == 'overlay') {\n                    this.overlayContainer.hide();\n                }\n            },\n            position: function (value) {\n                var options = this.options;\n                var outerWrapper = this.outerWrapper;\n                var position = value || options.position;\n                if (position == RIGHT) {\n                    outerWrapper.removeClass('k-drawer-' + LEFT);\n                    outerWrapper.addClass('k-drawer-' + RIGHT);\n                } else {\n                    outerWrapper.removeClass('k-drawer-' + RIGHT);\n                    outerWrapper.addClass('k-drawer-' + LEFT);\n                }\n                this.leftPositioned = position === LEFT;\n            },\n            _start: function (e) {\n                var that = this;\n                var options = this.options;\n                var drawerContainer = this.drawerContainer;\n                var drawerItemsWrapper = this.drawerItemsWrapper;\n                var userEvents = e.sender;\n                if (Math.abs(e.x.velocity) < Math.abs(e.y.velocity) || kendo.triggeredByInput(e.event)) {\n                    userEvents.cancel();\n                    return;\n                }\n                if (this.drawerMini) {\n                    drawerItemsWrapper.html(that.drawerElement);\n                }\n                if (options.mini) {\n                    drawerItemsWrapper.css('transition', 'none');\n                } else {\n                    drawerContainer.css('transition', 'none');\n                }\n                if (options.mode == 'overlay') {\n                    this.overlayContainer.show();\n                }\n            },\n            _update: function (e) {\n                var options = this.options;\n                var mode = options.mode;\n                if (mode == 'overlay') {\n                    this._overlay(e);\n                } else {\n                    this._push(e);\n                }\n            },\n            _end: function (e) {\n                var velocity = e.x.velocity;\n                var options = this.options;\n                var drawerContainer = this.drawerContainer;\n                var drawerItemsWrapper = this.drawerItemsWrapper;\n                var elementWidth = drawerItemsWrapper.width();\n                var pastHalf = elementWidth > options.width / 2;\n                var velocityThreshold = 0.8;\n                var shouldShow;\n                if (options.mini) {\n                    drawerItemsWrapper.css('transition', 'all .3s ease-out');\n                } else {\n                    drawerContainer.css('transition', 'all .3s ease-out');\n                }\n                if (this.leftPositioned) {\n                    shouldShow = velocity > -velocityThreshold && (velocity > velocityThreshold || pastHalf);\n                } else {\n                    shouldShow = velocity < velocityThreshold && (velocity < -velocityThreshold || pastHalf);\n                }\n                if (shouldShow) {\n                    if (this.trigger('show', { sender: this })) {\n                        e.preventDefault();\n                        this.hide();\n                    } else {\n                        this.show();\n                    }\n                } else {\n                    if (this.trigger('hide', { sender: this })) {\n                        e.preventDefault();\n                        this.show();\n                    } else {\n                        this.hide();\n                    }\n                }\n            },\n            _overlay: function (moveEventArgs) {\n                var drawerContainer = this.drawerContainer;\n                var drawerItemsWrapper = this.drawerItemsWrapper;\n                var elementWidth = drawerItemsWrapper.width();\n                var options = this.options;\n                var minWidth = options.mini && options.mini.width || this.minWidth || 0;\n                var limitedPosition;\n                var updatedPosition;\n                updatedPosition = elementWidth + (this.leftPositioned ? moveEventArgs.x.delta : -moveEventArgs.x.delta);\n                limitedPosition = Math.min(Math.max(updatedPosition, minWidth), options.width);\n                moveEventArgs.event.preventDefault();\n                moveEventArgs.event.stopPropagation();\n                if (options.mini) {\n                    drawerItemsWrapper.width(limitedPosition);\n                } else {\n                    drawerContainer.width(limitedPosition);\n                }\n            },\n            _push: function (moveEventArgs) {\n                var drawerContainer = this.drawerContainer;\n                var drawerItemsWrapper = this.drawerItemsWrapper;\n                var elementWidth = drawerItemsWrapper.width();\n                var options = this.options;\n                var minWidth = options.mini && options.mini.width || this.minWidth || 0;\n                var limitedPosition;\n                var updatedPosition;\n                updatedPosition = elementWidth + (this.leftPositioned ? moveEventArgs.x.delta : -moveEventArgs.x.delta);\n                limitedPosition = Math.min(Math.max(updatedPosition, minWidth), options.width);\n                moveEventArgs.event.preventDefault();\n                moveEventArgs.event.stopPropagation();\n                if (options.mini) {\n                    drawerItemsWrapper.width(limitedPosition);\n                } else {\n                    drawerContainer.width(limitedPosition);\n                }\n            },\n            _selectItem: function (item) {\n                var selectedItemIndex;\n                if (item) {\n                    item.addClass('k-state-selected');\n                    this.trigger('itemClick', {\n                        item: item,\n                        sender: this\n                    });\n                    this._selectedItemIndex = item.index();\n                    return;\n                }\n                selectedItemIndex = this._selectedItemIndex;\n                if (selectedItemIndex) {\n                    this.drawerItemsWrapper.find('[data-role=\\'drawer-item\\']').eq(selectedItemIndex).addClass('k-state-selected');\n                }\n            },\n            _itemClick: function (e) {\n                var that = this;\n                var item;\n                if ($(e.event.target).find('.k-drawer-item').length > 0) {\n                    item = $(e.event.target).find('.k-drawer-item');\n                } else if ($(e.event.target).closest('.k-drawer-item').length > 0) {\n                    item = $(e.event.target).closest('.k-drawer-item');\n                } else if ($(e.event.target).hasClass('.k-drawer-item')) {\n                    item = $(e.event.target);\n                }\n                that.drawerItemsWrapper.find('.k-drawer-item').removeClass('k-state-selected');\n                that._selectItem(item);\n            },\n            destroy: function () {\n                var options = this.options;\n                if (options.mode == 'overlay') {\n                    if (this.leftPositioned) {\n                        $(document.body).css('padding-left', 0);\n                    } else {\n                        $(document.body).css('padding-right', 0);\n                    }\n                }\n                Widget.fn.destroy.call(this);\n                this.userEvents.destroy();\n                kendo.destroy(this.element);\n                this.element = this.drawerContainer = this.drawerElement = this.drawerItemsWrapper = this._miniTemplate = null;\n            },\n            options: {\n                name: 'Drawer',\n                position: LEFT,\n                mode: 'overlay',\n                swipeToOpen: true,\n                width: 280,\n                mini: false,\n                template: ''\n            },\n            events: [\n                HIDE,\n                SHOW,\n                ITEMCLICK\n            ]\n        });\n        kendo.ui.plugin(Drawer);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}