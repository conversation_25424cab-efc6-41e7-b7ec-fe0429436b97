{"version": 3, "sources": ["kendo.data.odata.js"], "names": ["f", "define", "$", "undefined", "toOdataFilter", "filter", "useOdataFour", "idx", "length", "field", "type", "format", "operator", "value", "ignoreCase", "result", "logic", "filters", "replace", "odataFilters", "odataFiltersVersionFour", "kendo", "timezone", "apply", "push", "join", "stripMetadata", "obj", "name", "indexOf", "hex16", "Math", "floor", "random", "toString", "substr", "createBoundary", "prefix", "createDelimeter", "boundary", "close", "NEWLINE", "createCommand", "transport", "item", "httpVerb", "command", "transportUrl", "options", "url", "commandPrefix", "isFunction", "getOperationHeader", "changeset", "changeId", "header", "getOperationContent", "content", "DOUBLELINE", "stringify", "getOperations", "collection", "<PERSON><PERSON><PERSON><PERSON>", "i", "requestBody", "processCollection", "colection", "getBoundary", "createBatchRequest", "colections", "batchURL", "batch", "headers", "Content-Type", "updated", "destroyed", "created", "data", "parseBatchResponse", "responseText", "changeBody", "status", "code", "marker", "jsonModel", "responseMarkers", "match", "markerIndex", "collections", "models", "passed", "lastIndexOf", "substring", "pop", "parseFloat", "JSON", "parse", "window", "extend", "eq", "neq", "gt", "gte", "lt", "lte", "contains", "doesnotcontain", "endswith", "startswith", "isnull", "isnotnull", "isnullorempty", "isnotnullorempty", "isempty", "isnotempty", "mappers", "pageSize", "noop", "page", "params", "useVersionFour", "$filter", "sort", "orderby", "expr", "map", "order", "dir", "$orderby", "skip", "$skip", "take", "$top", "defaultDataType", "read", "dataType", "schemas", "odata", "d", "results", "total", "transports", "cache", "jsonp", "update", "contentType", "create", "destroy", "parameterMap", "option", "this", "$inlinecount", "$format", "Error", "odata-v4", "isArray", "$count", "submit", "e", "that", "ajax", "success", "response", "current", "responses", "index", "error", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,oBAAqB,cAAeD,IAC7C,WAwbE,MAhbC,UAAUE,EAAGC,GAoDV,QAASC,GAAcC,EAAQC,GAC3B,GAAgDC,GAAKC,EAAQC,EAAOC,EAAMC,EAAQC,EAAUC,EAAOC,EAA/FC,KAAaC,EAAQX,EAAOW,OAAS,MAAsEC,EAAUZ,EAAOY,OAChI,KAAKV,EAAM,EAAGC,EAASS,EAAQT,OAAQD,EAAMC,EAAQD,IACjDF,EAASY,EAAQV,GACjBE,EAAQJ,EAAOI,MACfI,EAAQR,EAAOQ,MACfD,EAAWP,EAAOO,SACdP,EAAOY,QACPZ,EAASD,EAAcC,EAAQC,IAE/BQ,EAAaT,EAAOS,WACpBL,EAAQA,EAAMS,QAAQ,MAAO,KAC7Bb,EAASc,EAAaP,GAClBN,IACAD,EAASe,EAAwBR,IAEpB,kBAAbA,EACAP,EAASgB,EAAMV,OAAO,6BAAgCF,EAAOJ,GACzC,qBAAbO,EACPP,EAASgB,EAAMV,OAAO,8BAAiCF,EAAOJ,GAC1C,WAAbO,GAAsC,cAAbA,EAChCP,EAASgB,EAAMV,OAAO,eAAgBF,EAAOJ,GACzB,YAAbO,GAAuC,eAAbA,EACjCP,EAASgB,EAAMV,OAAO,aAAgBF,EAAOJ,GACtCA,GAAUQ,IAAUV,IAC3BO,EAAOR,EAAEQ,KAAKG,GACD,WAATH,GACAC,EAAS,QACTE,EAAQA,EAAMK,QAAQ,KAAM,MACxBJ,KAAe,IACfL,EAAQ,WAAaA,EAAQ,MAEjB,SAATC,EACHJ,GACAK,EAAS,gCACTE,EAAQQ,EAAMC,SAASC,MAAMV,EAAO,YAEpCF,EAAS,oCAGbA,EAAS,MAETN,EAAOG,OAAS,EACD,gBAAXH,EACAM,EAAS,WAAaA,EAAS,KAE/BA,EAAS,OAASA,EAAS,QACV,mBAAbC,IACIN,GACAK,EAAS,uBACTN,EAAS,WAETM,GAAU,cAKtBA,EAAS,WAAaA,EAE1BN,EAASgB,EAAMV,OAAOA,EAAQN,EAAQQ,EAAOJ,KAGrDM,EAAOS,KAAKnB,EAMhB,OAJAA,GAASU,EAAOU,KAAK,IAAMT,EAAQ,KAC/BD,EAAOP,OAAS,IAChBH,EAAS,IAAMA,EAAS,KAErBA,EAEX,QAASqB,GAAcC,GACnB,IAAK,GAAIC,KAAQD,GACkB,IAA3BC,EAAKC,QAAQ,iBACNF,GAAIC,GAIvB,QAASE,KACL,MAAOC,MAAKC,MAA4B,OAArB,EAAID,KAAKE,WAAmBC,SAAS,IAAIC,OAAO,GAEvE,QAASC,GAAeC,GACpB,MAAOA,GAASP,IAAU,IAAMA,IAAU,IAAMA,IAEpD,QAASQ,GAAgBC,EAAUC,GAC/B,GAAIzB,GAAS0B,EAAU,KAAOF,CAI9B,OAHIC,KACAzB,GAAU,MAEPA,EAEX,QAAS2B,GAAcC,EAAWC,EAAMC,EAAUC,GAAlD,GACQC,GAAeJ,EAAUK,QAAQF,GAASG,IAC1CC,EAAgB7B,EAAMV,OAAO,OAAQkC,EACzC,OAAIM,GAAWJ,GACJG,EAAgBH,EAAaH,GAE7BM,EAAgBH,EAG/B,QAASK,GAAmBC,EAAWC,GACnC,GAAIC,GAAS,EAKb,OAJAA,IAAUjB,EAAgBe,GAAW,GACrCE,GAAUd,EAAU,iCACpBc,GAAUd,EAAU,oCACpBc,GAAUd,EAAU,eAAiBa,EAGzC,QAASE,GAAoBZ,GACzB,GAAIa,GAAU,EAId,OAHAA,IAAWhB,EAAU,uDACrBgB,GAAWhB,EAAU,gCACrBgB,GAAWC,EAAarC,EAAMsC,UAAUf,GAG5C,QAASgB,GAAcC,EAAYR,EAAWC,EAAUR,EAASH,EAAWmB,GAA5E,GAEaC,GADLC,EAAc,EAClB,KAASD,EAAI,EAAGA,EAAIF,EAAWrD,OAAQuD,IACnCC,GAAeZ,EAAmBC,EAAWC,GAC7CU,GAAeN,EAAahB,EAAcC,EAAWkB,EAAWE,GAAIpB,EAAUK,QAAQF,GAASpC,KAAMoC,GAAW,YAC3GgB,IACDE,GAAeR,EAAoBK,EAAWE,KAElDC,GAAevB,EACfa,GAEJ,OAAOU,GAEX,QAASC,GAAkBC,EAAW3B,EAAUc,EAAWC,EAAUX,EAAWG,EAASgB,GACrF,GAAIE,GAAc,EAKlB,OAJAA,IAAeG,EAAY5B,EAAUc,GACrCW,GAAeJ,EAAcM,EAAWb,EAAWC,EAAUR,EAASH,EAAWmB,GACjFE,GAAe1B,EAAgBe,GAAW,GAC1CW,GAAevB,EAGnB,QAAS0B,GAAY5B,EAAUc,GAC3B,GAAIW,GAAc,EAGlB,OAFAA,IAAe,KAAOzB,EAAWE,EACjCuB,GAAe,2CAA6CX,EAAYZ,EAG5E,QAAS2B,GAAmBzB,EAAW0B,GAAvC,GACQrB,MACAT,EAAWH,EAAe,aAC1B4B,EAAc,GACdV,EAAW,EACXgB,EAAW3B,EAAUK,QAAQuB,MAAMtB,IACnCI,EAAYjB,EAAe,gBAmB/B,OAlBAY,GAAQtC,KAAOiC,EAAUK,QAAQuB,MAAM7D,KACvCsC,EAAQC,IAAME,EAAWmB,GAAYA,IAAaA,EAClDtB,EAAQwB,SAAYC,eAAgB,6BAA+BlC,GAC/D8B,EAAWK,QAAQlE,SACnBwD,GAAeC,EAAkBI,EAAWK,QAASnC,EAAUc,EAAWC,EAAUX,EAAW,UAAU,GACzGW,GAAYe,EAAWK,QAAQlE,OAC/B6C,EAAYjB,EAAe,kBAE3BiC,EAAWM,UAAUnE,SACrBwD,GAAeC,EAAkBI,EAAWM,UAAWpC,EAAUc,EAAWC,EAAUX,EAAW,WAAW,GAC5GW,GAAYe,EAAWM,UAAUnE,OACjC6C,EAAYjB,EAAe,kBAE3BiC,EAAWO,QAAQpE,SACnBwD,GAAeC,EAAkBI,EAAWO,QAASrC,EAAUc,EAAWC,EAAUX,EAAW,UAAU,IAE7GqB,GAAe1B,EAAgBC,GAAU,GACzCS,EAAQ6B,KAAOb,EACRhB,EAEX,QAAS8B,GAAmBC,GAA5B,GAIQC,GACAC,EACAC,EACAC,EACAC,EAKKrB,EAZLsB,EAAkBN,EAAaO,MAAM,qCACrCC,EAAc,EACdC,IAUJ,KAJAA,EAAYhE,MACRiE,UACAC,QAAQ,IAEH3B,EAAI,EAAGA,EAAIsB,EAAgB7E,OAAQuD,IACxCoB,EAASE,EAAgBtB,GACrBoB,EAAOQ,YAAY,KAAMR,EAAO3E,OAAS,GACrCuD,EAAIsB,EAAgB7E,OAAS,GAC7BgF,EAAYhE,MACRiE,UACAC,QAAQ,KAQhBH,EAHCA,EAGaR,EAAalD,QAAQsD,EAAQI,EAAcJ,EAAO3E,QAFlDuE,EAAalD,QAAQsD,GAIvCH,EAAaD,EAAaa,UAAUL,EAAaR,EAAalD,QAAQ,KAAM0D,EAAc,IAC1FN,EAASD,EAAWM,MAAM,gCAAgCO,MAC1DX,EAAO7D,EAAMyE,WAAWb,EAAOK,MAAM,UAAUO,OAC3CX,GAAQ,KAAOA,GAAQ,KACvBE,EAAYJ,EAAWM,MAAM,YACzBF,GACAI,EAAYA,EAAYhF,OAAS,GAAGiF,OAAOjE,KAAKuE,KAAKC,MAAMZ,EAAU,MAGzEI,EAAYA,EAAYhF,OAAS,GAAGkF,QAAS,EAGrD,OAAOF,GApQX,GAAInE,GAAQ4E,OAAO5E,MAAO6E,EAAShG,EAAEgG,OAAQzD,EAAU,OAAQiB,EAAa,WAAYP,EAAa9B,EAAM8B,WAAYhC,GAC/GgF,GAAI,KACJC,IAAK,KACLC,GAAI,KACJC,IAAK,KACLC,GAAI,KACJC,IAAK,KACLC,SAAU,cACVC,eAAgB,cAChBC,SAAU,WACVC,WAAY,aACZC,OAAQ,KACRC,UAAW,KACXC,cAAe,KACfC,iBAAkB,KAClBC,QAAS,KACTC,WAAY,MACb9F,EAA0B8E,KAAW/E,GAAgBsF,SAAU,aAAeU,GAC7EC,SAAUlH,EAAEmH,KACZC,KAAMpH,EAAEmH,KACRhH,OAAQ,SAAUkH,EAAQlH,EAAQmH,GAC1BnH,IACAA,EAASD,EAAcC,EAAQmH,GAC3BnH,IACAkH,EAAOE,QAAUpH,KAI7BqH,KAAM,SAAUH,EAAQI,GACpB,GAAIC,GAAO1H,EAAE2H,IAAIF,EAAS,SAAU9G,GAChC,GAAIiH,GAAQjH,EAAMJ,MAAMS,QAAQ,MAAO,IAIvC,OAHkB,SAAdL,EAAMkH,MACND,GAAS,SAENA,IACRrG,KAAK,IACJmG,KACAL,EAAOS,SAAWJ,IAG1BK,KAAM,SAAUV,EAAQU,GAChBA,IACAV,EAAOW,MAAQD,IAGvBE,KAAM,SAAUZ,EAAQY,GAChBA,IACAZ,EAAOa,KAAOD,KAGvBE,GAAoBC,MAAQC,SAAU,SAoN7CrC,IAAO,EAAM7E,EAAMwD,MACf2D,SACIC,OACI/H,KAAM,OACNmE,KAAM,SAAUA,GACZ,MAAOA,GAAK6D,EAAEC,UAAY9D,EAAK6D,IAEnCE,MAAO,cAGfC,YACIJ,OACIH,MACIQ,OAAO,EACPP,SAAU,QACVQ,MAAO,aAEXC,QACIF,OAAO,EACPP,SAAU,OACVU,YAAa,mBACbvI,KAAM,OAEVwI,QACIJ,OAAO,EACPP,SAAU,OACVU,YAAa,mBACbvI,KAAM,QAEVyI,SACIL,OAAO,EACPP,SAAU,OACV7H,KAAM,UAEV0I,aAAc,SAAUpG,EAAStC,EAAM8G,GACnC,GAAID,GAAQ1G,EAAOwI,EAAQd,CAK3B,IAJAvF,EAAUA,MACVtC,EAAOA,GAAQ,OACf6H,GAAYe,KAAKtG,SAAWqF,GAAiB3H,GAC7C6H,EAAWA,EAAWA,EAASA,SAAW,OAC7B,SAAT7H,EAAiB,CACjB6G,GAAWgC,aAAc,YACT,QAAZhB,IACAhB,EAAOiC,QAAU,OAErB,KAAKH,IAAUrG,GACPmE,EAAQkC,GACRlC,EAAQkC,GAAQ9B,EAAQvE,EAAQqG,GAAS7B,GAEzCD,EAAO8B,GAAUrG,EAAQqG,OAG9B,CACH,GAAiB,SAAbd,EACA,KAAUkB,OAAM,sCAAwC/I,EAAO,cAEnE,IAAa,YAATA,EAAoB,CACpB,IAAK2I,IAAUrG,GACXnC,EAAQmC,EAAQqG,GACK,gBAAVxI,KACPmC,EAAQqG,GAAUxI,EAAQ,GAGlC0G,GAASlG,EAAMsC,UAAUX,IAGjC,MAAOuE,QAKvBrB,GAAO,EAAM7E,EAAMwD,MACf2D,SACIkB,YACIhJ,KAAM,OACNmE,KAAM,SAAUA,GACZ,GAAI3E,EAAEyJ,QAAQ9E,GAAO,CACjB,IAAK,GAAId,GAAI,EAAGA,EAAIc,EAAKrE,OAAQuD,IAC7BrC,EAAcmD,EAAKd,GAEvB,OAAOc,GAIP,MAFAA,GAAO3E,EAAEgG,UAAWrB,GACpBnD,EAAcmD,GACVA,EAAKhE,MACEgE,EAAKhE,OAERgE,IAGhB+D,MAAO,SAAU/D,GACb,MAAOA,GAAK,mBAIxBgE,YACIa,YACInF,OAAS7D,KAAM,QACf4H,MACIQ,OAAO,EACPP,SAAU,QAEdS,QACIF,OAAO,EACPP,SAAU,OACVU,YAAa,0CACbvI,KAAM,OAEVwI,QACIJ,OAAO,EACPP,SAAU,OACVU,YAAa,0CACbvI,KAAM,QAEVyI,SACIL,OAAO,EACPP,SAAU,OACV7H,KAAM,UAEV0I,aAAc,SAAUpG,EAAStC,GAC7B,GAAIK,GAASM,EAAMwD,KAAKgE,WAAWJ,MAAMW,aAAapG,EAAStC,GAAM,EAKrE,OAJY,QAARA,IACAK,EAAO6I,QAAS,QACT7I,GAAOwI,cAEXxI,GAEX8I,OAAQ,SAAUC,GAAV,GACAC,GAAOT,KACPtG,EAAUoB,EAAmB2F,EAAMD,EAAEjF,MACrCW,EAAcsE,EAAEjF,MACfW,EAAYd,QAAQlE,QAAWgF,EAAYb,UAAUnE,QAAWgF,EAAYZ,QAAQpE,SAGzFN,EAAE8J,KAAK9D,GAAO,MACV+D,QAAS,SAAUC,GAAV,GAGDC,GAFAC,EAAYtF,EAAmBoF,GAC/BG,EAAQ,CAER7E,GAAYd,QAAQlE,SACpB2J,EAAUC,EAAUC,GAChBF,EAAQzE,QACRoE,EAAEG,QAAQE,EAAQ1E,OAAOjF,OAAS2J,EAAQ1E,UAAa,UAE3D4E,KAEA7E,EAAYb,UAAUnE,SACtB2J,EAAUC,EAAUC,GAChBF,EAAQzE,QACRoE,EAAEG,WAAY,WAElBI,KAEA7E,EAAYZ,QAAQpE,SACpB2J,EAAUC,EAAUC,GAChBF,EAAQzE,QACRoE,EAAEG,QAAQE,EAAQ1E,OAAQ,YAItC6E,MAAO,SAAUJ,EAAUjF,EAAQqF,GAC/BR,EAAEQ,MAAMJ,EAAUjF,EAAQqF,KAE/BtH,UAKrBiD,OAAO5E,MAAMkJ,QACRtE,OAAO5E,OACE,kBAAVpB,SAAwBA,OAAOuK,IAAMvK,OAAS,SAAUwK,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.data.odata.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.data.odata', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'data.odata',\n        name: 'OData',\n        category: 'framework',\n        depends: ['core'],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, extend = $.extend, NEWLINE = '\\r\\n', DOUBLELINE = '\\r\\n\\r\\n', isFunction = kendo.isFunction, odataFilters = {\n                eq: 'eq',\n                neq: 'ne',\n                gt: 'gt',\n                gte: 'ge',\n                lt: 'lt',\n                lte: 'le',\n                contains: 'substringof',\n                doesnotcontain: 'substringof',\n                endswith: 'endswith',\n                startswith: 'startswith',\n                isnull: 'eq',\n                isnotnull: 'ne',\n                isnullorempty: 'eq',\n                isnotnullorempty: 'ne',\n                isempty: 'eq',\n                isnotempty: 'ne'\n            }, odataFiltersVersionFour = extend({}, odataFilters, { contains: 'contains' }), mappers = {\n                pageSize: $.noop,\n                page: $.noop,\n                filter: function (params, filter, useVersionFour) {\n                    if (filter) {\n                        filter = toOdataFilter(filter, useVersionFour);\n                        if (filter) {\n                            params.$filter = filter;\n                        }\n                    }\n                },\n                sort: function (params, orderby) {\n                    var expr = $.map(orderby, function (value) {\n                        var order = value.field.replace(/\\./g, '/');\n                        if (value.dir === 'desc') {\n                            order += ' desc';\n                        }\n                        return order;\n                    }).join(',');\n                    if (expr) {\n                        params.$orderby = expr;\n                    }\n                },\n                skip: function (params, skip) {\n                    if (skip) {\n                        params.$skip = skip;\n                    }\n                },\n                take: function (params, take) {\n                    if (take) {\n                        params.$top = take;\n                    }\n                }\n            }, defaultDataType = { read: { dataType: 'jsonp' } };\n        function toOdataFilter(filter, useOdataFour) {\n            var result = [], logic = filter.logic || 'and', idx, length, field, type, format, operator, value, ignoreCase, filters = filter.filters;\n            for (idx = 0, length = filters.length; idx < length; idx++) {\n                filter = filters[idx];\n                field = filter.field;\n                value = filter.value;\n                operator = filter.operator;\n                if (filter.filters) {\n                    filter = toOdataFilter(filter, useOdataFour);\n                } else {\n                    ignoreCase = filter.ignoreCase;\n                    field = field.replace(/\\./g, '/');\n                    filter = odataFilters[operator];\n                    if (useOdataFour) {\n                        filter = odataFiltersVersionFour[operator];\n                    }\n                    if (operator === 'isnullorempty') {\n                        filter = kendo.format('{0} {1} null or {0} {1} \\'\\'', field, filter);\n                    } else if (operator === 'isnotnullorempty') {\n                        filter = kendo.format('{0} {1} null and {0} {1} \\'\\'', field, filter);\n                    } else if (operator === 'isnull' || operator === 'isnotnull') {\n                        filter = kendo.format('{0} {1} null', field, filter);\n                    } else if (operator === 'isempty' || operator === 'isnotempty') {\n                        filter = kendo.format('{0} {1} \\'\\'', field, filter);\n                    } else if (filter && value !== undefined) {\n                        type = $.type(value);\n                        if (type === 'string') {\n                            format = '\\'{1}\\'';\n                            value = value.replace(/'/g, '\\'\\'');\n                            if (ignoreCase === true) {\n                                field = 'tolower(' + field + ')';\n                            }\n                        } else if (type === 'date') {\n                            if (useOdataFour) {\n                                format = '{1:yyyy-MM-ddTHH:mm:ss+00:00}';\n                                value = kendo.timezone.apply(value, 'Etc/UTC');\n                            } else {\n                                format = 'datetime\\'{1:yyyy-MM-ddTHH:mm:ss}\\'';\n                            }\n                        } else {\n                            format = '{1}';\n                        }\n                        if (filter.length > 3) {\n                            if (filter !== 'substringof') {\n                                format = '{0}({2},' + format + ')';\n                            } else {\n                                format = '{0}(' + format + ',{2})';\n                                if (operator === 'doesnotcontain') {\n                                    if (useOdataFour) {\n                                        format = '{0}({2},\\'{1}\\') eq -1';\n                                        filter = 'indexof';\n                                    } else {\n                                        format += ' eq false';\n                                    }\n                                }\n                            }\n                        } else {\n                            format = '{2} {0} ' + format;\n                        }\n                        filter = kendo.format(format, filter, value, field);\n                    }\n                }\n                result.push(filter);\n            }\n            filter = result.join(' ' + logic + ' ');\n            if (result.length > 1) {\n                filter = '(' + filter + ')';\n            }\n            return filter;\n        }\n        function stripMetadata(obj) {\n            for (var name in obj) {\n                if (name.indexOf('@odata') === 0) {\n                    delete obj[name];\n                }\n            }\n        }\n        function hex16() {\n            return Math.floor((1 + Math.random()) * 65536).toString(16).substr(1);\n        }\n        function createBoundary(prefix) {\n            return prefix + hex16() + '-' + hex16() + '-' + hex16();\n        }\n        function createDelimeter(boundary, close) {\n            var result = NEWLINE + '--' + boundary;\n            if (close) {\n                result += '--';\n            }\n            return result;\n        }\n        function createCommand(transport, item, httpVerb, command) {\n            var transportUrl = transport.options[command].url;\n            var commandPrefix = kendo.format('{0} ', httpVerb);\n            if (isFunction(transportUrl)) {\n                return commandPrefix + transportUrl(item);\n            } else {\n                return commandPrefix + transportUrl;\n            }\n        }\n        function getOperationHeader(changeset, changeId) {\n            var header = '';\n            header += createDelimeter(changeset, false);\n            header += NEWLINE + 'Content-Type: application/http';\n            header += NEWLINE + 'Content-Transfer-Encoding: binary';\n            header += NEWLINE + 'Content-ID: ' + changeId;\n            return header;\n        }\n        function getOperationContent(item) {\n            var content = '';\n            content += NEWLINE + 'Content-Type: application/json;odata=minimalmetadata';\n            content += NEWLINE + 'Prefer: return=representation';\n            content += DOUBLELINE + kendo.stringify(item);\n            return content;\n        }\n        function getOperations(collection, changeset, changeId, command, transport, skipContent) {\n            var requestBody = '';\n            for (var i = 0; i < collection.length; i++) {\n                requestBody += getOperationHeader(changeset, changeId);\n                requestBody += DOUBLELINE + createCommand(transport, collection[i], transport.options[command].type, command) + ' HTTP/1.1';\n                if (!skipContent) {\n                    requestBody += getOperationContent(collection[i]);\n                }\n                requestBody += NEWLINE;\n                changeId++;\n            }\n            return requestBody;\n        }\n        function processCollection(colection, boundary, changeset, changeId, transport, command, skipContent) {\n            var requestBody = '';\n            requestBody += getBoundary(boundary, changeset);\n            requestBody += getOperations(colection, changeset, changeId, command, transport, skipContent);\n            requestBody += createDelimeter(changeset, true);\n            requestBody += NEWLINE;\n            return requestBody;\n        }\n        function getBoundary(boundary, changeset) {\n            var requestBody = '';\n            requestBody += '--' + boundary + NEWLINE;\n            requestBody += 'Content-Type: multipart/mixed; boundary=' + changeset + NEWLINE;\n            return requestBody;\n        }\n        function createBatchRequest(transport, colections) {\n            var options = {};\n            var boundary = createBoundary('sf_batch_');\n            var requestBody = '';\n            var changeId = 0;\n            var batchURL = transport.options.batch.url;\n            var changeset = createBoundary('sf_changeset_');\n            options.type = transport.options.batch.type;\n            options.url = isFunction(batchURL) ? batchURL() : batchURL;\n            options.headers = { 'Content-Type': 'multipart/mixed; boundary=' + boundary };\n            if (colections.updated.length) {\n                requestBody += processCollection(colections.updated, boundary, changeset, changeId, transport, 'update', false);\n                changeId += colections.updated.length;\n                changeset = createBoundary('sf_changeset_');\n            }\n            if (colections.destroyed.length) {\n                requestBody += processCollection(colections.destroyed, boundary, changeset, changeId, transport, 'destroy', true);\n                changeId += colections.destroyed.length;\n                changeset = createBoundary('sf_changeset_');\n            }\n            if (colections.created.length) {\n                requestBody += processCollection(colections.created, boundary, changeset, changeId, transport, 'create', false);\n            }\n            requestBody += createDelimeter(boundary, true);\n            options.data = requestBody;\n            return options;\n        }\n        function parseBatchResponse(responseText) {\n            var responseMarkers = responseText.match(/--changesetresponse_[a-z0-9-]+$/gm);\n            var markerIndex = 0;\n            var collections = [];\n            var changeBody;\n            var status;\n            var code;\n            var marker;\n            var jsonModel;\n            collections.push({\n                models: [],\n                passed: true\n            });\n            for (var i = 0; i < responseMarkers.length; i++) {\n                marker = responseMarkers[i];\n                if (marker.lastIndexOf('--', marker.length - 1)) {\n                    if (i < responseMarkers.length - 1) {\n                        collections.push({\n                            models: [],\n                            passed: true\n                        });\n                    }\n                    continue;\n                }\n                if (!markerIndex) {\n                    markerIndex = responseText.indexOf(marker);\n                } else {\n                    markerIndex = responseText.indexOf(marker, markerIndex + marker.length);\n                }\n                changeBody = responseText.substring(markerIndex, responseText.indexOf('--', markerIndex + 1));\n                status = changeBody.match(/^HTTP\\/1\\.\\d (\\d{3}) (.*)$/gm).pop();\n                code = kendo.parseFloat(status.match(/\\d{3}/g).pop());\n                if (code >= 200 && code <= 299) {\n                    jsonModel = changeBody.match(/\\{.*\\}/gm);\n                    if (jsonModel) {\n                        collections[collections.length - 1].models.push(JSON.parse(jsonModel[0]));\n                    }\n                } else {\n                    collections[collections.length - 1].passed = false;\n                }\n            }\n            return collections;\n        }\n        extend(true, kendo.data, {\n            schemas: {\n                odata: {\n                    type: 'json',\n                    data: function (data) {\n                        return data.d.results || [data.d];\n                    },\n                    total: 'd.__count'\n                }\n            },\n            transports: {\n                odata: {\n                    read: {\n                        cache: true,\n                        dataType: 'jsonp',\n                        jsonp: '$callback'\n                    },\n                    update: {\n                        cache: true,\n                        dataType: 'json',\n                        contentType: 'application/json',\n                        type: 'PUT'\n                    },\n                    create: {\n                        cache: true,\n                        dataType: 'json',\n                        contentType: 'application/json',\n                        type: 'POST'\n                    },\n                    destroy: {\n                        cache: true,\n                        dataType: 'json',\n                        type: 'DELETE'\n                    },\n                    parameterMap: function (options, type, useVersionFour) {\n                        var params, value, option, dataType;\n                        options = options || {};\n                        type = type || 'read';\n                        dataType = (this.options || defaultDataType)[type];\n                        dataType = dataType ? dataType.dataType : 'json';\n                        if (type === 'read') {\n                            params = { $inlinecount: 'allpages' };\n                            if (dataType != 'json') {\n                                params.$format = 'json';\n                            }\n                            for (option in options) {\n                                if (mappers[option]) {\n                                    mappers[option](params, options[option], useVersionFour);\n                                } else {\n                                    params[option] = options[option];\n                                }\n                            }\n                        } else {\n                            if (dataType !== 'json') {\n                                throw new Error('Only json dataType can be used for ' + type + ' operation.');\n                            }\n                            if (type !== 'destroy') {\n                                for (option in options) {\n                                    value = options[option];\n                                    if (typeof value === 'number') {\n                                        options[option] = value + '';\n                                    }\n                                }\n                                params = kendo.stringify(options);\n                            }\n                        }\n                        return params;\n                    }\n                }\n            }\n        });\n        extend(true, kendo.data, {\n            schemas: {\n                'odata-v4': {\n                    type: 'json',\n                    data: function (data) {\n                        if ($.isArray(data)) {\n                            for (var i = 0; i < data.length; i++) {\n                                stripMetadata(data[i]);\n                            }\n                            return data;\n                        } else {\n                            data = $.extend({}, data);\n                            stripMetadata(data);\n                            if (data.value) {\n                                return data.value;\n                            }\n                            return [data];\n                        }\n                    },\n                    total: function (data) {\n                        return data['@odata.count'];\n                    }\n                }\n            },\n            transports: {\n                'odata-v4': {\n                    batch: { type: 'POST' },\n                    read: {\n                        cache: true,\n                        dataType: 'json'\n                    },\n                    update: {\n                        cache: true,\n                        dataType: 'json',\n                        contentType: 'application/json;IEEE754Compatible=true',\n                        type: 'PUT'\n                    },\n                    create: {\n                        cache: true,\n                        dataType: 'json',\n                        contentType: 'application/json;IEEE754Compatible=true',\n                        type: 'POST'\n                    },\n                    destroy: {\n                        cache: true,\n                        dataType: 'json',\n                        type: 'DELETE'\n                    },\n                    parameterMap: function (options, type) {\n                        var result = kendo.data.transports.odata.parameterMap(options, type, true);\n                        if (type == 'read') {\n                            result.$count = true;\n                            delete result.$inlinecount;\n                        }\n                        return result;\n                    },\n                    submit: function (e) {\n                        var that = this;\n                        var options = createBatchRequest(that, e.data);\n                        var collections = e.data;\n                        if (!collections.updated.length && !collections.destroyed.length && !collections.created.length) {\n                            return;\n                        }\n                        $.ajax(extend(true, {}, {\n                            success: function (response) {\n                                var responses = parseBatchResponse(response);\n                                var index = 0;\n                                var current;\n                                if (collections.updated.length) {\n                                    current = responses[index];\n                                    if (current.passed) {\n                                        e.success(current.models.length ? current.models : [], 'update');\n                                    }\n                                    index++;\n                                }\n                                if (collections.destroyed.length) {\n                                    current = responses[index];\n                                    if (current.passed) {\n                                        e.success([], 'destroy');\n                                    }\n                                    index++;\n                                }\n                                if (collections.created.length) {\n                                    current = responses[index];\n                                    if (current.passed) {\n                                        e.success(current.models, 'create');\n                                    }\n                                }\n                            },\n                            error: function (response, status, error) {\n                                e.error(response, status, error);\n                            }\n                        }, options));\n                    }\n                }\n            }\n        });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}