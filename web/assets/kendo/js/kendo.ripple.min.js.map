{"version": 3, "sources": ["kendo.ripple.js"], "names": ["f", "define", "closest", "createRipple", "once", "activate", "finishAnimation", "release", "deactivate", "register", "this", "kendo", "util", "window", "ripple", "element", "selector", "matches", "node", "Element", "prototype", "el", "sel", "msMatchesSelector", "parentElement", "doc", "blob", "createElement", "className", "append<PERSON><PERSON><PERSON>", "eventName", "fn", "listener", "removeEventListener", "remove", "addEventListener", "containerSelector", "options", "e", "container", "doubleFocus", "_a", "state", "eventType", "rect", "left", "top", "xMax", "yMax", "dx", "dy", "size", "duration", "target", "document", "ownerDocument", "test", "type", "classList", "contains", "add", "animated", "released", "focusin", "keydown", "mousedown", "pointerdown", "touchdown", "currentTarget", "getComputedStyle", "getPropertyValue", "getBoundingClientRect", "clientX", "clientY", "width", "height", "Math", "sqrt", "style", "offsetWidth", "Error", "cssText", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "transition", "opacity", "root", "elements", "flatten", "arr", "concat", "apply", "handlers", "map", "item", "defaultOptions", "events", "global", "activator", "body", "for<PERSON>ach", "evt", "removeListener", "deepExtend", "amd", "a1", "a2", "a3", "$", "undefined", "ui", "Widget", "extend", "R<PERSON>pleContainer", "init", "that", "call", "wrapper", "registerListeners", "name", "nextElement<PERSON><PERSON>ling", "removeListeners", "callback", "destroy", "plugin", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,eAAgB,cAAeD,IACxC,YACG,WAAA,GAGOE,GAiBAC,EAWAC,EAWAC,EAgEAC,EAIAC,EAIAC,EAoBAC,CArIJC,MAAKC,MAAMC,KAAOF,KAAKC,MAAMC,SAC7BC,OAAOF,MAAMC,KAAKE,OAASD,OAAOF,MAAMC,KAAKE,WACzCZ,EAAU,SAAUa,EAASC,GAAnB,GAINC,GAKAC,CARJ,IAAIH,EAAQb,QACR,MAAOa,GAAQb,QAAQc,EAQ3B,KANIC,EAAUE,QAAQC,UAAUH,QAAU,SAAUI,EAAIC,GACpD,MAAOD,GAAGJ,QAAQK,IAClB,SAAUD,EAAIC,GACd,MAAOD,GAAGE,kBAAkBD,IAE5BJ,EAAOH,EACJG,GAAM,CACT,GAAID,EAAQC,EAAMF,GACd,MAAOE,EAEXA,GAAOA,EAAKM,gBAGhBrB,EAAe,SAAUsB,GAAV,GAGXC,GAFAZ,EAASW,EAAIE,cAAc,MAK/B,OAJAb,GAAOc,UAAY,WACfF,EAAOD,EAAIE,cAAc,OAC7BD,EAAKE,UAAY,gBACjBd,EAAOe,YAAYH,IAEfZ,EACAY,IAGJtB,EAAO,SAAUW,EAASe,EAAWC,GAA9B,GACHC,GAAW,WACXD,IACAhB,EAAQkB,oBAAoBH,EAAWE,GAAU,IAEjDE,EAAS,WACT,MAAOnB,GAAQoB,iBAAiBL,EAAWE,GAAU,GAGzD,OADAE,MACSA,OAAQA,IAEjB7B,EAAW,SAAU+B,EAAmBC,GACxC,MAAO,UAAUC,GAAV,GAGCC,GASAC,EAKAC,EAAwB3B,EAAgBY,EACxCgB,EAOAC,EAYAC,EACAC,EACAC,EAQAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAnDAC,EAASf,EAAEe,OACX5B,EAAM4B,EAAOC,UAAYD,EAAOE,aAOpC,IAJIhB,EADAF,EAAQE,UACIF,EAAQE,UAAUc,GAElBnD,EAAQmD,EAAQjB,GAE3BG,IAGDC,EAAc,SAASgB,KAAKlB,EAAEmB,OAASlB,EAAUmB,UAAUC,SAAS,oBACpEnB,GAAJ,CAyCA,GAtCAD,EAAUmB,UAAUE,IAAI,mBACpBnB,EAAKtC,EAAasB,GAAMX,EAAS2B,EAAG,GAAIf,EAAOe,EAAG,GAClDC,GACAmB,UAAU,EACVC,UAAU,EACVpC,KAAMA,EACNa,UAAWA,EACXzB,OAAQA,GAER6B,GACAoB,QAAW,WACXC,QAAW,QACXC,UAAa,UACbC,YAAe,YACfC,UAAa,WACf7B,EAAEmB,MACJrD,EAAKkC,EAAE8B,cAAezB,EAAW,WAC7B,MAAOpC,GAAQmC,KAEnBH,EAAUV,YAAYf,GACtBD,OAAOwD,iBAAiBvD,GAAQwD,iBAAiB,WAC7C1B,EAAOL,EAAUgC,wBACjB1B,EAAO,EACPC,EAAM,EACN,sBAAsBU,KAAKlB,EAAEmB,OAC7BZ,EAAOP,EAAEkC,QAAU5B,EAAKC,KACxBC,EAAMR,EAAEmC,QAAU7B,EAAKE,MAEvBD,EAAOD,EAAK8B,MAAQ,EACpB5B,EAAMF,EAAK+B,OAAS,GAEpB5B,EAAOF,EAAOD,EAAK8B,MAAQ,EAAI9B,EAAK8B,MAAQ,EAC5C1B,EAAOF,EAAMF,EAAK+B,OAAS,EAAI/B,EAAK+B,OAAS,EAC7C1B,EAAKJ,EAAOE,EACZG,EAAKJ,EAAME,EACXG,EAAO,EAAIyB,KAAKC,KAAK5B,EAAKA,EAAKC,EAAKA,GACpCE,EAAW,IACf1B,EAAKoD,MAAMJ,MAAQhD,EAAKoD,MAAMH,OAASxB,EAAO,KAC1CzB,EAAKqD,YAAc,EACnB,KAAUC,OAAM,iBAEpBtD,GAAKoD,MAAMG,QAAU,gBAAkB9B,EAAO,oBAAsBA,EAAO,kEAAoEN,EAAO,iBAAmBC,EAAM,UAC/KoC,WAAW,WACP,MAAO5E,GAAgBoC,IACxBU,MAGP9C,EAAkB,SAAUoC,GAC5BA,EAAMmB,UAAW,EACjBrD,EAAWkC,IAEXnC,EAAU,SAAUmC,GACpBA,EAAMoB,UAAW,EACjBtD,EAAWkC,IAEXlC,EAAa,SAAUkC,GACvB,GAAKA,EAAMoB,UAAapB,EAAMmB,SAA9B,CAGA,GAAInC,GAAOgB,EAAMhB,KAAMZ,EAAS4B,EAAM5B,OAAQyB,EAAYG,EAAMH,SAC5DA,IACAnC,EAAKmC,EAAW,OAAQ,WACpB,MAAOA,GAAUmB,UAAUxB,OAAO,qBAGtCR,IACAtB,EAAKsB,EAAM,gBAAiB,WACpBZ,GAAUA,EAAOqE,YACjBrE,EAAOqE,WAAWC,YAAYtE,KAGtCY,EAAKoD,MAAMO,WAAa,uBACxB3D,EAAKoD,MAAMQ,QAAU,OAGzB7E,EAAW,SAAU8E,EAAMC,GAAhB,GACPC,GAAU,SAAUC,GACpB,SAAUC,OAAOC,SAAUF,IAE3BG,EAAWJ,EAAQD,EAASM,IAAI,SAAUC,GAAV,GAC5BC,IACAC,QACI,YACA,aAEJC,QAAQ,GAERlF,EAAW+E,EAAK/E,SAAUyB,EAAKsD,EAAK1D,QAASA,EAAiB,SAAPI,EAAgBuD,EAAiBvD,EACxF0D,EAAY9F,EAASW,EAAUqB,GAC/B4D,EAAS5D,EAAQ4D,QAAUD,EAAeC,OAC1C1D,EAAYF,EAAQ6D,OAAS5C,SAAS8C,KAAOb,CAIjD,OAHAU,GAAOI,QAAQ,SAAUC,GACrB,MAAO/D,GAAUJ,iBAAiBmE,EAAKH,GAAW,MAGlDF,OAAQA,EACR5D,QAASA,EACT8D,UAAWA,KAGnB,OAAO,YACH,GAAKZ,EAAL,CAGA,GAAIgB,GAAiB,SAAU9D,GAAV,GACbwD,GAASxD,EAAGwD,OAAQ5D,EAAUI,EAAGJ,QAAS8D,EAAY1D,EAAG0D,UACzD5D,EAAYF,EAAQ6D,OAAS5C,SAAS8C,KAAOb,CACjDU,GAAOI,QAAQ,SAAUC,GACrB,MAAO/D,GAAUN,oBAAoBqE,EAAKH,GAAW,KAG7DN,GAASQ,QAAQE,GACjBhB,EAAO,QAGf5E,MAAM6F,WAAW7F,MAAMC,KAAKE,QAAUL,SAAUA,QAEpC,kBAAVR,SAAwBA,OAAOwG,IAAMxG,OAAS,SAAUyG,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAU3G,EAAGC,QACVA,OAAO,gBAAiB,eAAgBD,IAC1C,WAyDE,MAlDC,UAAU6G,EAAGC,GAAb,GACOnG,GAAQE,OAAOF,MAAOoG,EAAKpG,EAAMoG,GAAIC,EAASD,EAAGC,OAAQC,EAASJ,EAAEI,OAAQnG,EAASH,EAAMC,KAAKE,OAChGoG,EAAkBF,EAAOC,QACzBE,KAAM,SAAUpG,EAASsB,GACrB,GAAI+E,GAAO1G,IACXsG,GAAOjF,GAAGoF,KAAKE,KAAKD,EAAMrG,GAC1BA,EAAUqG,EAAKE,QAAUF,EAAKrG,QAC9BqG,EAAK/E,QAAU4E,KAAWG,EAAK/E,QAASA,GACxC+E,EAAKG,qBAETlF,SACImF,KAAM,kBACNhC,WACMxE,SAAU,sBAERA,SAAU,oBACVqB,SAAW6D,QAAQ,KAErBlF,SAAU,sCAERA,SAAU,wBACVqB,SACI4D,QAAS,WACT1D,UAAW,SAAUlB,GACjB,GAAI,2BAA2BmC,KAAKnC,EAAGO,WACnC,MAAOP,GAAGoG,wBAOlCC,gBAAiB,aAEjBH,kBAAmB,WAAA,GAKXI,GAJAP,EAAO1G,KACP6E,EAAO6B,EAAKrG,QAAQ,GACpByE,EAAW4B,EAAK/E,QAAQmD,QAC5B4B,GAAKM,kBACDC,EAAW7G,EAAOL,SAAS8E,EAAMC,GACrC4B,EAAKM,gBAAkBC,GAE3BC,QAAS,WACL,GAAIR,GAAO1G,IACXsG,GAAOjF,GAAG6F,QAAQP,KAAKD,GACvBA,EAAKM,oBAGbX,GAAGc,OAAOX,IACZrG,OAAOF,MAAMmH,QACRjH,OAAOF,OACE,kBAAVV,SAAwBA,OAAOwG,IAAMxG,OAAS,SAAUyG,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.ripple.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/ripple', ['kendo.core'], f);\n}(function () {\n    (function () {\n        this.kendo.util = this.kendo.util || {};\n        window.kendo.util.ripple = window.kendo.util.ripple || {};\n        var closest = function (element, selector) {\n            if (element.closest) {\n                return element.closest(selector);\n            }\n            var matches = Element.prototype.matches ? function (el, sel) {\n                return el.matches(sel);\n            } : function (el, sel) {\n                return el.msMatchesSelector(sel);\n            };\n            var node = element;\n            while (node) {\n                if (matches(node, selector)) {\n                    return node;\n                }\n                node = node.parentElement;\n            }\n        };\n        var createRipple = function (doc) {\n            var ripple = doc.createElement('div');\n            ripple.className = 'k-ripple';\n            var blob = doc.createElement('div');\n            blob.className = 'k-ripple-blob';\n            ripple.appendChild(blob);\n            return [\n                ripple,\n                blob\n            ];\n        };\n        var once = function (element, eventName, fn) {\n            var listener = function () {\n                fn();\n                element.removeEventListener(eventName, listener, false);\n            };\n            var remove = function () {\n                return element.addEventListener(eventName, listener, false);\n            };\n            remove();\n            return { remove: remove };\n        };\n        var activate = function (containerSelector, options) {\n            return function (e) {\n                var target = e.target;\n                var doc = target.document || target.ownerDocument;\n                var container;\n                if (options.container) {\n                    container = options.container(target);\n                } else {\n                    container = closest(target, containerSelector);\n                }\n                if (!container) {\n                    return;\n                }\n                var doubleFocus = /focus/i.test(e.type) && container.classList.contains('k-ripple-target');\n                if (doubleFocus) {\n                    return;\n                }\n                container.classList.add('k-ripple-target');\n                var _a = createRipple(doc), ripple = _a[0], blob = _a[1];\n                var state = {\n                    animated: false,\n                    released: false,\n                    blob: blob,\n                    container: container,\n                    ripple: ripple\n                };\n                var eventType = {\n                    'focusin': 'focusout',\n                    'keydown': 'keyup',\n                    'mousedown': 'mouseup',\n                    'pointerdown': 'pointerup',\n                    'touchdown': 'touchup'\n                }[e.type];\n                once(e.currentTarget, eventType, function () {\n                    return release(state);\n                });\n                container.appendChild(ripple);\n                window.getComputedStyle(ripple).getPropertyValue('opacity');\n                var rect = container.getBoundingClientRect();\n                var left = 0;\n                var top = 0;\n                if (/mouse|pointer|touch/.test(e.type)) {\n                    left = e.clientX - rect.left;\n                    top = e.clientY - rect.top;\n                } else {\n                    left = rect.width / 2;\n                    top = rect.height / 2;\n                }\n                var xMax = left < rect.width / 2 ? rect.width : 0;\n                var yMax = top < rect.height / 2 ? rect.height : 0;\n                var dx = left - xMax;\n                var dy = top - yMax;\n                var size = 2 * Math.sqrt(dx * dx + dy * dy);\n                var duration = 500;\n                blob.style.width = blob.style.height = size + 'px';\n                if (blob.offsetWidth < 0) {\n                    throw new Error('Inconceivable!');\n                }\n                blob.style.cssText = '\\n    width: ' + size + 'px;\\n    height: ' + size + 'px;\\n    transform: translate(-50%, -50%) scale(1);\\n    left: ' + left + 'px;\\n    top: ' + top + 'px;\\n  ';\n                setTimeout(function () {\n                    return finishAnimation(state);\n                }, duration);\n            };\n        };\n        var finishAnimation = function (state) {\n            state.animated = true;\n            deactivate(state);\n        };\n        var release = function (state) {\n            state.released = true;\n            deactivate(state);\n        };\n        var deactivate = function (state) {\n            if (!state.released || !state.animated) {\n                return;\n            }\n            var blob = state.blob, ripple = state.ripple, container = state.container;\n            if (container) {\n                once(container, 'blur', function () {\n                    return container.classList.remove('k-ripple-target');\n                });\n            }\n            if (blob) {\n                once(blob, 'transitionend', function () {\n                    if (ripple && ripple.parentNode) {\n                        ripple.parentNode.removeChild(ripple);\n                    }\n                });\n                blob.style.transition = 'opacity 200ms linear';\n                blob.style.opacity = '0';\n            }\n        };\n        var register = function (root, elements) {\n            var flatten = function (arr) {\n                return [].concat.apply([], arr);\n            };\n            var handlers = flatten(elements.map(function (item) {\n                var defaultOptions = {\n                    events: [\n                        'mousedown',\n                        'touchdown'\n                    ],\n                    global: false\n                };\n                var selector = item.selector, _a = item.options, options = _a === void 0 ? defaultOptions : _a;\n                var activator = activate(selector, options);\n                var events = options.events || defaultOptions.events;\n                var container = options.global ? document.body : root;\n                events.forEach(function (evt) {\n                    return container.addEventListener(evt, activator, false);\n                });\n                return {\n                    events: events,\n                    options: options,\n                    activator: activator\n                };\n            }));\n            return function () {\n                if (!root) {\n                    return;\n                }\n                var removeListener = function (_a) {\n                    var events = _a.events, options = _a.options, activator = _a.activator;\n                    var container = options.global ? document.body : root;\n                    events.forEach(function (evt) {\n                        return container.removeEventListener(evt, activator, false);\n                    });\n                };\n                handlers.forEach(removeListener);\n                root = null;\n            };\n        };\n        kendo.deepExtend(kendo.util.ripple, { register: register });\n    }());\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.ripple', ['util/ripple'], f);\n}(function () {\n    var __meta__ = {\n        id: 'ripplecontainer',\n        name: 'RippleContainer',\n        category: 'web',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, Widget = ui.Widget, extend = $.extend, ripple = kendo.util.ripple;\n        var RippleContainer = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element);\n                element = that.wrapper = that.element;\n                that.options = extend({}, that.options, options);\n                that.registerListeners();\n            },\n            options: {\n                name: 'RippleContainer',\n                elements: [\n                    { selector: '.k-button:not(li)' },\n                    {\n                        selector: '.k-list > .k-item',\n                        options: { global: true }\n                    },\n                    { selector: '.k-checkbox-label, .k-radio-label' },\n                    {\n                        selector: '.k-checkbox, .k-radio',\n                        options: {\n                            events: ['focusin'],\n                            container: function (el) {\n                                if (/\\b(k-checkbox|k-radio)\\b/.test(el.className)) {\n                                    return el.nextElementSibling;\n                                }\n                            }\n                        }\n                    }\n                ]\n            },\n            removeListeners: function () {\n            },\n            registerListeners: function () {\n                var that = this;\n                var root = that.element[0];\n                var elements = that.options.elements;\n                that.removeListeners();\n                var callback = ripple.register(root, elements);\n                that.removeListeners = callback;\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that.removeListeners();\n            }\n        });\n        ui.plugin(RippleContainer);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}