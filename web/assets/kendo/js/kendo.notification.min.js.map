{"version": 3, "sources": ["kendo.notification.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "Widget", "ui", "proxy", "extend", "setTimeout", "CLICK", "SHOW", "HIDE", "KNOTIFICATION", "KICLOSE", "KHIDING", "INFO", "SUCCESS", "WARNING", "ERROR", "TOP", "LEFT", "BOTTOM", "RIGHT", "UP", "NS", "WRAPPER", "TEMPLATE", "SAFE_TEMPLATE", "replace", "Notification", "init", "element", "options", "that", "this", "fn", "call", "appendTo", "is", "hide", "_compileTemplates", "templates", "_guid", "guid", "_isRtl", "support", "isRtl", "_compileStacking", "stacking", "position", "top", "left", "notify", "events", "name", "pinned", "bottom", "right", "hideOnClick", "button", "allowHideAfter", "autoHideAfter", "width", "height", "animation", "open", "effects", "duration", "close", "kendoTemplate", "template", "_compiled", "each", "key", "value", "type", "templateId", "html", "_defaultCompiled", "_safeCompiled", "_getCompiled", "safe", "defaultCompiled", "origin", "paddings", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "horizontalAlignment", "_popup<PERSON><PERSON>in", "_popupPosition", "_popupPaddings", "_attachPopupEvents", "popup", "attachClick", "target", "on", "_hidePopup", "closeIcon", "attach<PERSON>elay", "isNaN", "bind", "find", "_showPopup", "wrapper", "openPopup", "x", "y", "last", "Popup", "anchor", "document", "body", "copyAnchorStyles", "modal", "collision", "_triggerHide", "deactivate", "e", "sender", "off", "destroy", "removeClass", "outerWidth", "outerHeight", "addClass", "css", "margin", "zIndex", "_togglePin", "pin", "win", "sign", "parseInt", "scrollTop", "scrollLeft", "_attachStaticEvents", "_hideStatic", "_showStatic", "initializedNotifications", "insertionMethod", "kendoAnimate", "getNotifications", "idx", "complete", "remove", "trigger", "angular", "elements", "show", "content", "args", "defaultArgs", "isFunction", "typeIcon", "isPlainObject", "toggleClass", "attr", "append", "data", "dataItem", "showText", "info", "success", "warning", "error", "openedNotifications", "guidElements", "children", "setOptions", "newOptions", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,sBACH,aACA,eACDD,IACL,WAyWE,MAxVC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAASF,EAAMG,GAAGD,OAAQE,EAAQN,EAAEM,MAAOC,EAASP,EAAEO,OAAQC,EAAaL,OAAOK,WAAYC,EAAQ,QAASC,EAAO,OAAQC,EAAO,OAAQC,EAAgB,iBAAkBC,EAAU,kCAAmCC,EAAU,WAAYC,EAAO,OAAQC,EAAU,UAAWC,EAAU,UAAWC,EAAQ,QAASC,EAAM,MAAOC,EAAO,OAAQC,EAAS,SAAUC,EAAQ,QAASC,EAAK,KAAMC,EAAK,qBAAsBC,EAAU,sDAAuDC,EAAW,iNAAsOC,EAAgBD,EAASE,QAAQ,aAAc,cACzxBC,EAAezB,EAAOG,QACtBuB,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOC,IACX9B,GAAO+B,GAAGL,KAAKM,KAAKH,EAAMF,EAASC,GACnCA,EAAUC,EAAKD,QACVA,EAAQK,UAAarC,EAAEgC,EAAQK,UAAUC,GAAGP,IAC7CE,EAAKF,QAAQQ,OAEjBN,EAAKO,kBAAkBR,EAAQS,WAC/BR,EAAKS,MAAQ,IAAMxC,EAAMyC,OACzBV,EAAKW,OAAS1C,EAAM2C,QAAQC,MAAMf,GAClCE,EAAKc,iBAAiBf,EAAQgB,SAAUhB,EAAQiB,SAASC,IAAKlB,EAAQiB,SAASE,MAC/EjD,EAAMkD,OAAOnB,IAEjBoB,QACI3C,EACAC,GAEJqB,SACIsB,KAAM,eACNL,UACIM,QAAQ,EACRL,IAAK,KACLC,KAAM,KACNK,OAAQ,GACRC,MAAO,IAEXT,SAAU,UACVU,aAAa,EACbC,QAAQ,EACRC,eAAgB,EAChBC,cAAe,IACfxB,SAAU,KACVyB,MAAO,KACPC,OAAQ,KACRtB,aACAuB,WACIC,MACIC,QAAS,UACTC,SAAU,KAEdC,OACIF,QAAS,WACTC,SAAU,IACV5B,MAAM,KAIlBC,kBAAmB,SAAUC,GAAV,GACXR,GAAOC,KACPmC,EAAgBnE,EAAMoE,QAC1BrC,GAAKsC,aACLvE,EAAEwE,KAAK/B,EAAW,SAAUgC,EAAKC,GAC7BzC,EAAKsC,UAAUG,EAAMC,MAAQN,EAAcK,EAAMJ,UAAYtE,EAAE,IAAM0E,EAAME,YAAYC,UAE3F5C,EAAK6C,iBAAmBT,EAAc3C,GACtCO,EAAK8C,cAAgBV,EAAc1C,IAEvCqD,aAAc,SAAUL,EAAMM,GAC1B,GAAIC,GAAkBD,EAAO/C,KAAK6C,cAAgB7C,KAAK4C,gBACvD,OAAOH,GAAOzC,KAAKqC,UAAUI,IAASO,EAAkBA,GAE5DnC,iBAAkB,SAAUC,EAAUE,EAAKC,GACvC,GAK2DgC,GAAQlC,EAL/DhB,EAAOC,KAAMkD,GACTC,WAAY,EACZC,aAAc,EACdC,cAAe,EACfC,YAAa,GACdC,EAA+B,OAATtC,EAAgB/B,EAAOE,CACpD,QAAQ0B,GACR,IAAK,OACDmC,EAAS9D,EAAS,IAAMoE,EACxBxC,EAAW9B,EAAM,IAAMsE,QAChBL,GAASG,aAChB,MACJ,KAAKjE,GACD6D,EAAShE,EAAM,IAAMG,EACrB2B,EAAW9B,EAAM,IAAMC,QAChBgE,GAASE,YAChB,MACJ,KAAKlE,GACD+D,EAAShE,EAAM,IAAMC,EACrB6B,EAAW9B,EAAM,IAAMG,QAChB8D,GAASI,WAChB,MACJ,KAAKjE,GACD4D,EAAShE,EAAM,IAAMsE,EACrBxC,EAAW5B,EAAS,IAAMoE,QACnBL,GAASC,UAChB,MACJ,SACgB,OAARnC,GACAiC,EAAS9D,EAAS,IAAMoE,EACxBxC,EAAW9B,EAAM,IAAMsE,QAChBL,GAASG,gBAEhBJ,EAAShE,EAAM,IAAMsE,EACrBxC,EAAW5B,EAAS,IAAMoE,QACnBL,GAASC,YAIxBpD,EAAKyD,aAAeP,EACpBlD,EAAK0D,eAAiB1C,EACtBhB,EAAK2D,eAAiBR,GAE1BS,mBAAoB,SAAU7D,EAAS8D,GAEnC,QAASC,GAAYC,GACjBA,EAAOC,GAAGxF,EAAQe,EAAI,WAClBS,EAAKiE,WAAWJ,KAHxB,GAAsHK,GAAlHlE,EAAOC,KAAM0B,EAAiB5B,EAAQ4B,eAAgBwC,GAAeC,MAAMzC,IAAmBA,EAAiB,CAM/G5B,GAAQ0B,YACRoC,EAAMQ,KAAK,WAAY,WACfF,EACA5F,EAAW,WACPuF,EAAYD,EAAM/D,UACnB6B,GAEHmC,EAAYD,EAAM/D,WAGnBC,EAAQ2B,SACfwC,EAAYL,EAAM/D,QAAQwE,KAAK1F,GAC3BuF,EACA5F,EAAW,WACPuF,EAAYI,IACbvC,GAEHmC,EAAYI,KAIxBK,WAAY,SAAUC,EAASzE,GAC3B,GAA6G8D,GAAOY,EAAhHzE,EAAOC,KAAM2B,EAAgB7B,EAAQ6B,cAAe8C,EAAI3E,EAAQiB,SAASE,KAAMyD,EAAI5E,EAAQiB,SAASC,GACxGwD,GAAY1G,EAAE,IAAMiC,EAAKS,MAAQ,SAAW5B,EAAU,KAAK+F,OAC3Df,EAAQ,GAAI5F,GAAMG,GAAGyG,MAAML,GACvBM,OAAQL,EAAU,GAAKA,EAAYM,SAASC,KAC5C9B,OAAQlD,EAAKyD,aACbzC,SAAUhB,EAAK0D,eACf3B,UAAWhC,EAAQgC,UACnBkD,kBAAkB,EAClBC,OAAO,EACPC,UAAW,GACXtE,MAAOb,EAAKW,OACZwB,MAAO,WACHnC,EAAKoF,aAAanF,KAAKH,UAE3BuF,WAAY,SAAUC,GAClBA,EAAEC,OAAOzF,QAAQ0F,IAAIjG,GACrB+F,EAAEC,OAAOzF,QAAQwE,KAAK1F,GAAS4G,IAAIjG,GACnC+F,EAAEC,OAAOE,aAGjBzF,EAAK4D,mBAAmB7D,EAAS8D,GACjCW,EAAQkB,YAAY,mBAChBjB,EAAU,GACVZ,EAAM7B,QAEI,OAAN0C,IACAA,EAAI3G,EAAEG,QAAQ2D,QAAU2C,EAAQmB,aAAe5F,EAAQiB,SAASQ,OAE1D,OAANmD,IACAA,EAAI5G,EAAEG,QAAQ4D,SAAW0C,EAAQoB,cAAgB7F,EAAQiB,SAASO,QAEtEsC,EAAM7B,KAAK0C,EAAGC,IAElBd,EAAMW,QAAQqB,SAAS7F,EAAKS,OAAOqF,IAAIxH,GACnCyH,OAAQ,EACRC,OAAQ,OACThG,EAAK2D,iBACJ5D,EAAQiB,SAASM,QACjBuC,EAAMW,QAAQsB,IAAI,WAAY,SAC1BrB,EAAU,IACVzE,EAAKiG,WAAWpC,EAAMW,SAAS,IAE3BC,EAAU,IAClBzE,EAAKiG,WAAWpC,EAAMW,SAAS,GAE/B5C,EAAgB,GAChBrD,EAAW,WACPyB,EAAKiE,WAAWJ,IACjBjC,IAGXqC,WAAY,SAAUJ,GAClBA,EAAMW,QAAQqB,SAAShH,GACvBgF,EAAM1B,SAEV8D,WAAY,SAAUzB,EAAS0B,GAC3B,GAAIC,GAAMpI,EAAEG,QAASkI,EAAOF,KAAW,CACvC1B,GAAQsB,KACJ7E,IAAKoF,SAAS7B,EAAQsB,IAAI5G,GAAM,IAAMkH,EAAOD,EAAIG,YACjDpF,KAAMmF,SAAS7B,EAAQsB,IAAI3G,GAAO,IAAMiH,EAAOD,EAAII,gBAG3DC,oBAAqB,SAAUzG,EAASyE,GAEpC,QAASV,GAAYC,GACjBA,EAAOC,GAAGxF,EAAQe,EAAIlB,EAAM2B,EAAKyG,YAAazG,EAAMwE,IAFxD,GAAIxE,GAAOC,KAAM0B,EAAiB5B,EAAQ4B,eAAgBwC,GAAeC,MAAMzC,IAAmBA,EAAiB,CAI/G5B,GAAQ0B,YACJ0C,EACA5F,EAAW,WACPuF,EAAYU,IACb7C,GAEHmC,EAAYU,GAETzE,EAAQ2B,SACXyC,EACA5F,EAAW,WACPuF,EAAYU,EAAQF,KAAK1F,KAC1B+C,GAEHmC,EAAYU,EAAQF,KAAK1F,MAIrC8H,YAAa,SAAUlC,EAASzE,GAC5B,GAAwL4G,GAApL3G,EAAOC,KAAM2B,EAAgB7B,EAAQ6B,cAAeG,EAAYhC,EAAQgC,UAAW6E,EAAkB7G,EAAQgB,UAAYzB,GAAMS,EAAQgB,UAAY5B,EAAO,YAAc,UAC5KqF,GAAQkB,YAAY,WAAWG,SAAS7F,EAAKS,OAAOmG,GAAiB7G,EAAQK,UAAUE,OAAOuG,aAAa9E,EAAUC,OAAQ,GAC7H2E,EAA2B3G,EAAK8G,mBAChCH,EAAyBpE,KAAK,SAAUwE,EAAKjH,GACzCE,EAAKwG,oBAAoBzG,EAAShC,EAAE+B,IAChC8B,EAAgB,GAChBrD,EAAW,WACPyB,EAAKyG,YAAY1I,EAAE+B,KACpB8B,MAIf6E,YAAa,SAAUjC,GACnBA,EAAQqC,aAAavI,EAAO2B,KAAKF,QAAQgC,UAAUI,QAAS,GACxD6E,SAAU,WACNxC,EAAQgB,IAAIjG,GAAI+E,KAAK1F,GAAS4G,IAAIjG,GAClCiF,EAAQyC,aAGhBhH,KAAKmF,aAAaZ,IAEtBY,aAAc,SAAUtF,GACpBG,KAAKiH,QAAQxI,GAAQoB,QAASA,IAC9BG,KAAKkH,QAAQ,UAAW,WACpB,OAASC,SAAUtH,MAG3BuH,KAAM,SAAUC,EAAS5E,EAAMM,GAC3B,GAA+DuE,GAAMC,EAAjExH,EAAOC,KAAMF,EAAUC,EAAKD,QAASyE,EAAUzG,EAAEyB,EAkCrD,OAjCKkD,KACDA,EAAO5D,GAEK,OAAZwI,GAAoBA,IAAYtJ,GAAyB,KAAZsJ,IACzCrJ,EAAMwJ,WAAWH,KACjBA,EAAUA,KAEdE,GACIE,SAAUhF,EACV4E,QAAS,IAGTC,EADAxJ,EAAE4J,cAAcL,GACThJ,EAAOkJ,EAAaF,GAEpBhJ,EAAOkJ,GAAeF,QAASA,IAE1C9C,EAAQqB,SAASlH,EAAgB,IAAM+D,GAAMkF,YAAYjJ,EAAgB,UAAWoB,EAAQ2B,QAAQkG,YAAYjJ,EAAgB,YAAaoB,EAAQ2B,QAAQmG,KAAK,YAAa,SAAS/B,KACpLjE,MAAO9B,EAAQ8B,MACfC,OAAQ/B,EAAQ+B,SACjBgG,OAAO9H,EAAK+C,aAAaL,EAAMM,GAAMuE,IACxCvH,EAAKmH,QAAQ,UAAW,WACpB,OACIC,SAAU5C,EACVuD,OAASC,SAAUT,OAGvBxJ,EAAEgC,EAAQK,UAAU,GACpBJ,EAAK0G,YAAYlC,EAASzE,GAE1BC,EAAKuE,WAAWC,EAASzE,GAE7BC,EAAKkH,QAAQzI,GAAQqB,QAAS0E,KAE3BxE,GAEXiI,SAAU,SAAUX,EAAS5E,GACzBzC,KAAKoH,KAAKC,EAAS5E,GAAM,IAE7BwF,KAAM,SAAUZ,GACZ,MAAOrH,MAAKoH,KAAKC,EAASxI,IAE9BqJ,QAAS,SAAUb,GACf,MAAOrH,MAAKoH,KAAKC,EAASvI,IAE9BqJ,QAAS,SAAUd,GACf,MAAOrH,MAAKoH,KAAKC,EAAStI,IAE9BqJ,MAAO,SAAUf,GACb,MAAOrH,MAAKoH,KAAKC,EAASrI,IAE9BqB,KAAM,WACF,GAAIN,GAAOC,KAAMqI,EAAsBtI,EAAK8G,kBAa5C,OAXIwB,GAAoB/F,KADpBvC,EAAKD,QAAQK,SACY,SAAU2G,EAAKjH,GACpCE,EAAKyG,YAAY1I,EAAE+B,KAGE,SAAUiH,EAAKjH,GACpC,GAAI+D,GAAQ9F,EAAE+B,GAASiI,KAAK,aACxBlE,IACA7D,EAAKiE,WAAWJ,KAIrB7D,GAEX8G,iBAAkB,WACd,GAAI9G,GAAOC,KAAMsI,EAAexK,EAAE,IAAMiC,EAAKS,MAAQ,SAAW5B,EAAU,IAC1E,OAAImB,GAAKD,QAAQK,SACNmI,EAEAA,EAAaC,SAAS,IAAM7J,IAG3C8J,WAAY,SAAUC,GAClB,GAAiB3I,GAAbC,EAAOC,IACX9B,GAAO+B,GAAGuI,WAAWtI,KAAKH,EAAM0I,GAChC3I,EAAUC,EAAKD,QACX2I,EAAWlI,YAAcxC,GACzBgC,EAAKO,kBAAkBR,EAAQS,WAE/BkI,EAAW3H,WAAa/C,GAAa0K,EAAW1H,WAAahD,GAC7DgC,EAAKc,iBAAiBf,EAAQgB,SAAUhB,EAAQiB,SAASC,IAAKlB,EAAQiB,SAASE,OAGvFuE,QAAS,WACLtH,EAAO+B,GAAGuF,QAAQtF,KAAKF,MACvBA,KAAK6G,mBAAmBtB,IAAIjG,GAAI+E,KAAK1F,GAAS4G,IAAIjG,KAG1DtB,GAAMG,GAAGuK,OAAO/I,IAClB1B,OAAOD,MAAM2K,QACR1K,OAAOD,OACE,kBAAVH,SAAwBA,OAAO+K,IAAM/K,OAAS,SAAUgL,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.notification.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.notification', [\n        'kendo.core',\n        'kendo.popup'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'notification',\n        name: 'Notification',\n        category: 'web',\n        description: 'The Notification widget displays user alerts.',\n        depends: [\n            'core',\n            'popup'\n        ],\n        features: [{\n                id: 'notification-fx',\n                name: 'Animation',\n                description: 'Support for animation',\n                depends: ['fx']\n            }]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, proxy = $.proxy, extend = $.extend, setTimeout = window.setTimeout, CLICK = 'click', SHOW = 'show', HIDE = 'hide', KNOTIFICATION = 'k-notification', KICLOSE = '.k-notification-wrap .k-i-close', KHIDING = 'k-hiding', INFO = 'info', SUCCESS = 'success', WARNING = 'warning', ERROR = 'error', TOP = 'top', LEFT = 'left', BOTTOM = 'bottom', RIGHT = 'right', UP = 'up', NS = '.kendoNotification', WRAPPER = '<div class=\"k-widget k-popup k-notification\"></div>', TEMPLATE = '<div class=\"k-notification-wrap\">' + '<span class=\"k-icon k-i-#=typeIcon#\" title=\"#=typeIcon#\"></span>' + '<div class=\"k-notification-content\">#=content#</div>' + '<span class=\"k-icon k-i-close\" title=\"Hide\"></span>' + '</div>', SAFE_TEMPLATE = TEMPLATE.replace('#=content#', '#:content#');\n        var Notification = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                if (!options.appendTo || !$(options.appendTo).is(element)) {\n                    that.element.hide();\n                }\n                that._compileTemplates(options.templates);\n                that._guid = '_' + kendo.guid();\n                that._isRtl = kendo.support.isRtl(element);\n                that._compileStacking(options.stacking, options.position.top, options.position.left);\n                kendo.notify(that);\n            },\n            events: [\n                SHOW,\n                HIDE\n            ],\n            options: {\n                name: 'Notification',\n                position: {\n                    pinned: true,\n                    top: null,\n                    left: null,\n                    bottom: 20,\n                    right: 20\n                },\n                stacking: 'default',\n                hideOnClick: true,\n                button: false,\n                allowHideAfter: 0,\n                autoHideAfter: 5000,\n                appendTo: null,\n                width: null,\n                height: null,\n                templates: [],\n                animation: {\n                    open: {\n                        effects: 'fade:in',\n                        duration: 300\n                    },\n                    close: {\n                        effects: 'fade:out',\n                        duration: 600,\n                        hide: true\n                    }\n                }\n            },\n            _compileTemplates: function (templates) {\n                var that = this;\n                var kendoTemplate = kendo.template;\n                that._compiled = {};\n                $.each(templates, function (key, value) {\n                    that._compiled[value.type] = kendoTemplate(value.template || $('#' + value.templateId).html());\n                });\n                that._defaultCompiled = kendoTemplate(TEMPLATE);\n                that._safeCompiled = kendoTemplate(SAFE_TEMPLATE);\n            },\n            _getCompiled: function (type, safe) {\n                var defaultCompiled = safe ? this._safeCompiled : this._defaultCompiled;\n                return type ? this._compiled[type] || defaultCompiled : defaultCompiled;\n            },\n            _compileStacking: function (stacking, top, left) {\n                var that = this, paddings = {\n                        paddingTop: 0,\n                        paddingRight: 0,\n                        paddingBottom: 0,\n                        paddingLeft: 0\n                    }, horizontalAlignment = left !== null ? LEFT : RIGHT, origin, position;\n                switch (stacking) {\n                case 'down':\n                    origin = BOTTOM + ' ' + horizontalAlignment;\n                    position = TOP + ' ' + horizontalAlignment;\n                    delete paddings.paddingBottom;\n                    break;\n                case RIGHT:\n                    origin = TOP + ' ' + RIGHT;\n                    position = TOP + ' ' + LEFT;\n                    delete paddings.paddingRight;\n                    break;\n                case LEFT:\n                    origin = TOP + ' ' + LEFT;\n                    position = TOP + ' ' + RIGHT;\n                    delete paddings.paddingLeft;\n                    break;\n                case UP:\n                    origin = TOP + ' ' + horizontalAlignment;\n                    position = BOTTOM + ' ' + horizontalAlignment;\n                    delete paddings.paddingTop;\n                    break;\n                default:\n                    if (top !== null) {\n                        origin = BOTTOM + ' ' + horizontalAlignment;\n                        position = TOP + ' ' + horizontalAlignment;\n                        delete paddings.paddingBottom;\n                    } else {\n                        origin = TOP + ' ' + horizontalAlignment;\n                        position = BOTTOM + ' ' + horizontalAlignment;\n                        delete paddings.paddingTop;\n                    }\n                    break;\n                }\n                that._popupOrigin = origin;\n                that._popupPosition = position;\n                that._popupPaddings = paddings;\n            },\n            _attachPopupEvents: function (options, popup) {\n                var that = this, allowHideAfter = options.allowHideAfter, attachDelay = !isNaN(allowHideAfter) && allowHideAfter > 0, closeIcon;\n                function attachClick(target) {\n                    target.on(CLICK + NS, function () {\n                        that._hidePopup(popup);\n                    });\n                }\n                if (options.hideOnClick) {\n                    popup.bind('activate', function () {\n                        if (attachDelay) {\n                            setTimeout(function () {\n                                attachClick(popup.element);\n                            }, allowHideAfter);\n                        } else {\n                            attachClick(popup.element);\n                        }\n                    });\n                } else if (options.button) {\n                    closeIcon = popup.element.find(KICLOSE);\n                    if (attachDelay) {\n                        setTimeout(function () {\n                            attachClick(closeIcon);\n                        }, allowHideAfter);\n                    } else {\n                        attachClick(closeIcon);\n                    }\n                }\n            },\n            _showPopup: function (wrapper, options) {\n                var that = this, autoHideAfter = options.autoHideAfter, x = options.position.left, y = options.position.top, popup, openPopup;\n                openPopup = $('.' + that._guid + ':not(.' + KHIDING + ')').last();\n                popup = new kendo.ui.Popup(wrapper, {\n                    anchor: openPopup[0] ? openPopup : document.body,\n                    origin: that._popupOrigin,\n                    position: that._popupPosition,\n                    animation: options.animation,\n                    copyAnchorStyles: false,\n                    modal: true,\n                    collision: '',\n                    isRtl: that._isRtl,\n                    close: function () {\n                        that._triggerHide(this.element);\n                    },\n                    deactivate: function (e) {\n                        e.sender.element.off(NS);\n                        e.sender.element.find(KICLOSE).off(NS);\n                        e.sender.destroy();\n                    }\n                });\n                that._attachPopupEvents(options, popup);\n                wrapper.removeClass('k-group k-reset');\n                if (openPopup[0]) {\n                    popup.open();\n                } else {\n                    if (x === null) {\n                        x = $(window).width() - wrapper.outerWidth() - options.position.right;\n                    }\n                    if (y === null) {\n                        y = $(window).height() - wrapper.outerHeight() - options.position.bottom;\n                    }\n                    popup.open(x, y);\n                }\n                popup.wrapper.addClass(that._guid).css(extend({\n                    margin: 0,\n                    zIndex: 10050\n                }, that._popupPaddings));\n                if (options.position.pinned) {\n                    popup.wrapper.css('position', 'fixed');\n                    if (openPopup[0]) {\n                        that._togglePin(popup.wrapper, true);\n                    }\n                } else if (!openPopup[0]) {\n                    that._togglePin(popup.wrapper, false);\n                }\n                if (autoHideAfter > 0) {\n                    setTimeout(function () {\n                        that._hidePopup(popup);\n                    }, autoHideAfter);\n                }\n            },\n            _hidePopup: function (popup) {\n                popup.wrapper.addClass(KHIDING);\n                popup.close();\n            },\n            _togglePin: function (wrapper, pin) {\n                var win = $(window), sign = pin ? -1 : 1;\n                wrapper.css({\n                    top: parseInt(wrapper.css(TOP), 10) + sign * win.scrollTop(),\n                    left: parseInt(wrapper.css(LEFT), 10) + sign * win.scrollLeft()\n                });\n            },\n            _attachStaticEvents: function (options, wrapper) {\n                var that = this, allowHideAfter = options.allowHideAfter, attachDelay = !isNaN(allowHideAfter) && allowHideAfter > 0;\n                function attachClick(target) {\n                    target.on(CLICK + NS, proxy(that._hideStatic, that, wrapper));\n                }\n                if (options.hideOnClick) {\n                    if (attachDelay) {\n                        setTimeout(function () {\n                            attachClick(wrapper);\n                        }, allowHideAfter);\n                    } else {\n                        attachClick(wrapper);\n                    }\n                } else if (options.button) {\n                    if (attachDelay) {\n                        setTimeout(function () {\n                            attachClick(wrapper.find(KICLOSE));\n                        }, allowHideAfter);\n                    } else {\n                        attachClick(wrapper.find(KICLOSE));\n                    }\n                }\n            },\n            _showStatic: function (wrapper, options) {\n                var that = this, autoHideAfter = options.autoHideAfter, animation = options.animation, insertionMethod = options.stacking == UP || options.stacking == LEFT ? 'prependTo' : 'appendTo', initializedNotifications;\n                wrapper.removeClass('k-popup').addClass(that._guid)[insertionMethod](options.appendTo).hide().kendoAnimate(animation.open || false);\n                initializedNotifications = that.getNotifications();\n                initializedNotifications.each(function (idx, element) {\n                    that._attachStaticEvents(options, $(element));\n                    if (autoHideAfter > 0) {\n                        setTimeout(function () {\n                            that._hideStatic($(element));\n                        }, autoHideAfter);\n                    }\n                });\n            },\n            _hideStatic: function (wrapper) {\n                wrapper.kendoAnimate(extend(this.options.animation.close || false, {\n                    complete: function () {\n                        wrapper.off(NS).find(KICLOSE).off(NS);\n                        wrapper.remove();\n                    }\n                }));\n                this._triggerHide(wrapper);\n            },\n            _triggerHide: function (element) {\n                this.trigger(HIDE, { element: element });\n                this.angular('cleanup', function () {\n                    return { elements: element };\n                });\n            },\n            show: function (content, type, safe) {\n                var that = this, options = that.options, wrapper = $(WRAPPER), args, defaultArgs;\n                if (!type) {\n                    type = INFO;\n                }\n                if (content !== null && content !== undefined && content !== '') {\n                    if (kendo.isFunction(content)) {\n                        content = content();\n                    }\n                    defaultArgs = {\n                        typeIcon: type,\n                        content: ''\n                    };\n                    if ($.isPlainObject(content)) {\n                        args = extend(defaultArgs, content);\n                    } else {\n                        args = extend(defaultArgs, { content: content });\n                    }\n                    wrapper.addClass(KNOTIFICATION + '-' + type).toggleClass(KNOTIFICATION + '-button', options.button).toggleClass(KNOTIFICATION + '-closable', options.button).attr('data-role', 'alert').css({\n                        width: options.width,\n                        height: options.height\n                    }).append(that._getCompiled(type, safe)(args));\n                    that.angular('compile', function () {\n                        return {\n                            elements: wrapper,\n                            data: [{ dataItem: args }]\n                        };\n                    });\n                    if ($(options.appendTo)[0]) {\n                        that._showStatic(wrapper, options);\n                    } else {\n                        that._showPopup(wrapper, options);\n                    }\n                    that.trigger(SHOW, { element: wrapper });\n                }\n                return that;\n            },\n            showText: function (content, type) {\n                this.show(content, type, true);\n            },\n            info: function (content) {\n                return this.show(content, INFO);\n            },\n            success: function (content) {\n                return this.show(content, SUCCESS);\n            },\n            warning: function (content) {\n                return this.show(content, WARNING);\n            },\n            error: function (content) {\n                return this.show(content, ERROR);\n            },\n            hide: function () {\n                var that = this, openedNotifications = that.getNotifications();\n                if (that.options.appendTo) {\n                    openedNotifications.each(function (idx, element) {\n                        that._hideStatic($(element));\n                    });\n                } else {\n                    openedNotifications.each(function (idx, element) {\n                        var popup = $(element).data('kendoPopup');\n                        if (popup) {\n                            that._hidePopup(popup);\n                        }\n                    });\n                }\n                return that;\n            },\n            getNotifications: function () {\n                var that = this, guidElements = $('.' + that._guid + ':not(.' + KHIDING + ')');\n                if (that.options.appendTo) {\n                    return guidElements;\n                } else {\n                    return guidElements.children('.' + KNOTIFICATION);\n                }\n            },\n            setOptions: function (newOptions) {\n                var that = this, options;\n                Widget.fn.setOptions.call(that, newOptions);\n                options = that.options;\n                if (newOptions.templates !== undefined) {\n                    that._compileTemplates(options.templates);\n                }\n                if (newOptions.stacking !== undefined || newOptions.position !== undefined) {\n                    that._compileStacking(options.stacking, options.position.top, options.position.left);\n                }\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.getNotifications().off(NS).find(KICLOSE).off(NS);\n            }\n        });\n        kendo.ui.plugin(Notification);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}