/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.pivot.fieldmenu.min",["kendo.pivotgrid.min","kendo.menu.min","kendo.window.min","kendo.treeview.min","kendo.dropdownlist.min"],e)}(function(){return function(e,t){function i(e,t){var i,s,n=[];for(i=0,s=e.length;i<s;i++)e[i].field!==t&&n.push(e[i]);return n}function s(e,t,i){var s,n,r,a;if(!e)return[];for(e=e.filters,s=0,n=[],r=e.length;s<r;s++)a=e[s].operator,(i||"in"===a)&&a!==i||e[s].field!==t||n.push(e[s]);return n}function n(t,i,n){var r,a=0,l=n.length;if(t=s(t,i,"in")[0])for(r=t.value.split(",");a<l;a++)n[a].checked=e.inArray(n[a].uniqueName,r)>=0;else for(;a<l;a++)n[a].checked=!0}function r(e,t){var i,s=e.length;for(i=0;i<s;i++)e[i].checked&&0!==e[i].level()&&t.push(e[i].uniqueName),e[i].hasChildren&&r(e[i].children.view(),t)}var a=window.kendo,l=a.ui,o="kendoContextMenu",c=e.proxy,u=".kendoPivotFieldMenu",d=l.Widget,f="k-filter-item",h="aria-label",p=d.extend({init:function(e,t){d.fn.init.call(this,e,t),this._dataSource(),this._layout(),a.notify(this)},events:[],options:{name:"PivotFieldMenu",filter:null,filterable:!0,sortable:!0,messages:{info:"Show items with value that:",sortAscending:"Sort Ascending",sortDescending:"Sort Descending",filterFields:"Fields Filter",filter:"Filter",include:"Include Fields...",title:"Fields to include",clear:"Clear",ok:"OK",cancel:"Cancel",operators:{contains:"Contains",doesnotcontain:"Does not contain",startswith:"Starts with",endswith:"Ends with",eq:"Is equal to",neq:"Is not equal to"}}},_layout:function(){var t=this.options;this.wrapper=e(a.template(k)({ns:a.ns,filterable:t.filterable,sortable:t.sortable,messages:t.messages})),this.menu=this.wrapper[o]({filter:t.filter,target:this.element,orientation:"vertical",showOn:"click",closeOnClick:!1,open:c(this._menuOpen,this),select:c(this._select,this),copyAnchorStyles:!1}).data(o),this._createWindow(),t.filterable&&this._initFilterForm()},_initFilterForm:function(){var e=this.menu.element.find("."+f),t=c(this._filter,this);this._filterOperator=new a.ui.DropDownList(e.find("select")),this._filterValue=e.find(".k-textbox"),this._updateFilterAriaLabel(),e.on("submit"+u,t).on("click"+u,".k-button-filter",t).on("click"+u,".k-button-clear",c(this._reset,this))},_setFilterForm:function(e){var t=this._filterOperator,i="",s="";e&&(i=e.operator,s=e.value),t.value(i),t.value()||t.select(0),this._filterValue.val(s)},_clearFilters:function(e){var t,i,n=this.dataSource.filter()||{},r=0;for(n.filters=n.filters||[],t=s(n,e),i=t.length;r<i;r++)n.filters.splice(n.filters.indexOf(t[r]),1);return n},_convert:function(t){var i=this.dataSource.options.schema,s=((i.model||{}).fields||{})[this.currentMember];return s&&("number"===s.type?t=parseFloat(t):"boolean"===s.type&&(t=!!e.parseJSON(t))),t},_filter:function(e){var i,s,n=this,r=n._convert(n._filterValue.val());return e.preventDefault(),""===r?(n.menu.close(),t):(i={field:n.currentMember,operator:n._filterOperator.value(),value:r},s=n._clearFilters(n.currentMember),s.filters.push(i),n.dataSource.filter(s),n.menu.close(),t)},_updateFilterAriaLabel:function(){var e=this.menu.element.find("."+f),t=this._filterOperator.value(),i=this.options.messages.operators[t];e.find("select").attr(h,i)},_reset:function(e){var t=this,i=t._clearFilters(t.currentMember);e.preventDefault(),i.filters[0]||(i={}),t.dataSource.filter(i),t._setFilterForm(null),t.menu.close()},_sort:function(e){var t=this.currentMember,s=this.dataSource.sort()||[];s=i(s,t),s.push({field:t,dir:e}),this.dataSource.sort(s),this.menu.close()},setDataSource:function(e){this.options.dataSource=e,this._dataSource()},_dataSource:function(){this.dataSource=a.data.PivotDataSource.create(this.options.dataSource)},_createWindow:function(){var t=this.options.messages;this.includeWindow=e(a.template(v)({messages:t})).on("click"+u,".k-button-ok",c(this._applyIncludes,this)).on("click"+u,".k-button-cancel",c(this._closeWindow,this)),this.includeWindow=new l.Window(this.includeWindow,{title:t.title,visible:!1,resizable:!1,open:c(this._windowOpen,this)})},_applyIncludes:function(e){var t,i=[],n=this.treeView.dataSource.view(),a=n[0].checked,l=this.dataSource.filter(),o=s(l,this.currentMember,"in")[0];r(n,i),o&&(a?(l.filters.splice(l.filters.indexOf(o),1),l.filters.length||(l={})):o.value=i.join(","),t=l),i.length&&(t||a||(t={field:this.currentMember,operator:"in",value:i.join(",")},l&&(l.filters.push(t),t=l))),t&&this.dataSource.filter(t),this._closeWindow(e)},_closeWindow:function(e){e.preventDefault(),this.includeWindow.close()},_treeViewDataSource:function(){var e=this;return a.data.HierarchicalDataSource.create({schema:{model:{id:"uniqueName",hasChildren:function(e){return parseInt(e.childrenCardinality,10)>0}}},transport:{read:function(t){var i={},s=e.treeView.dataSource.get(t.data.uniqueName),r=t.data.uniqueName;r?(i.memberUniqueName=s.uniqueName.replace(/\&/g,"&amp;"),i.treeOp=1):i.levelUniqueName=e.currentMember+".[(ALL)]",e.dataSource.schemaMembers(i).done(function(i){n(e.dataSource.filter(),e.currentMember,i),t.success(i)}).fail(t.error)}}})},_createTreeView:function(e){var t=this;t.treeView=new l.TreeView(e,{autoBind:!1,dataSource:t._treeViewDataSource(),dataTextField:"caption",template:"#: data.item.caption || data.item.name #",checkboxes:{checkChildren:!0},dataBound:function(){l.progress(t.includeWindow.element,!1)}})},_menuOpen:function(t){if(t.event){var i=a.attr("name");this.currentMember=e(t.event.target).closest("["+i+"]").attr(i),this.options.filterable&&this._setFilterForm(s(this.dataSource.filter(),this.currentMember)[0])}},_select:function(t){var i=e(t.item);e(".k-pivot-filter-window").not(this.includeWindow.element).kendoWindow("close"),i.hasClass("k-include-item")?this.includeWindow.center().open():i.hasClass("k-sort-asc")?this._sort("asc"):i.hasClass("k-sort-desc")?this._sort("desc"):i.hasClass(f)&&this._updateFilterAriaLabel()},_windowOpen:function(){this.treeView||this._createTreeView(this.includeWindow.element.find(".k-treeview")),l.progress(this.includeWindow.element,!0),this.treeView.dataSource.read()},destroy:function(){d.fn.destroy.call(this),this.menu&&(this.menu.destroy(),this.menu=null),this.treeView&&(this.treeView.destroy(),this.treeView=null),this.includeWindow&&(this.includeWindow.destroy(),this.includeWindow=null),this.wrapper=null,this.element=null}}),m='<div class="k-filterable k-content" tabindex="-1" data-role="fieldmenu"><form class="k-filter-menu"><div><div class="k-filter-help-text">#=messages.info#</div><select>#for(var op in messages.operators){#<option value="#=op#">#=messages.operators[op]#</option>#}#</select><input class="k-textbox" type="text" '+h+'="#=messages.filter#" /><div><a class="k-button k-primary k-button-filter" href="\\#">#=messages.filter#</a><a class="k-button k-button-clear" href="\\#">#=messages.clear#</a></div></div></form></div>',k='<ul class="k-pivot-fieldmenu"># if (sortable) {#<li class="k-item k-sort-asc"><span class="k-link"><span class="k-icon k-i-sort-asc-sm"></span>${messages.sortAscending}</span></li><li class="k-item k-sort-desc"><span class="k-link"><span class="k-icon k-i-sort-desc-sm"></span>${messages.sortDescending}</span></li># if (filterable) {#<li class="k-separator"></li># } ## } ## if (filterable) {#<li class="k-item k-include-item"><span class="k-link"><span class="k-icon k-i-filter"></span>${messages.include}</span></li><li class="k-separator"></li><li class="k-item '+f+'"><span class="k-link"><span class="k-icon k-i-filter"></span>${messages.filterFields}</span><ul><li>'+m+"</li></ul></li># } #</ul>",v='<div class="k-popup-edit-form k-pivot-filter-window"><div class="k-edit-form-container"><div class="k-treeview"></div><div class="k-edit-buttons k-state-default"><a class="k-button k-primary k-button-ok" href="\\#">${messages.ok}</a><a class="k-button k-button-cancel" href="\\#">${messages.cancel}</a></div></div>';l.plugin(p)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()});
//# sourceMappingURL=kendo.pivot.fieldmenu.min.js.map
