/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.scheduler.dayview.min",["kendo.scheduler.view.min"],e)}(function(){return function(e,t){function a(e){var t=new Date(1980,1,1,0,0,0);return h(t,w(e)),t}function s(e,t,a){return e>=t&&e<=a}function i(e,t,a,s){return s=s?e<=a:e<a,e>t&&s}function n(e,t,a,s){var i,n=e._continuousEvents,o=n[n.length-1],l=g(t.start.startDate()).getTime();if(s&&o&&g(o.start.startDate()).getTime()==l){for(i=n.length-1;i>-1&&!(n[i].isAllDay||g(n[i].start.startDate()).getTime()<l);i--);n.splice(i+1,0,{element:a,isAllDay:!0,uid:a.attr(r.attr("uid")),start:t.start,end:t.end})}else n.push({element:a,isAllDay:s,uid:a.attr(r.attr("uid")),start:t.start,end:t.end})}function o(e){var t=[],a=e.workWeekStart%7,s=Math.abs(e.workWeekEnd%7);for(t.push(a);s!=a;)a>6?a-=7:a++,t.push(a);return t}var r=window.kendo,l=r.ui,d=r.support.browser,h=r.date.setTime,c=l.SchedulerView,u=r._outerWidth,p=r._outerHeight,f=e.extend,m=e.proxy,g=r.date.getDate,_=r.date.MS_PER_MINUTE,v=r.date.MS_PER_DAY,y="k-current-time",D="k-current-time-arrow",k="k-event-inverse",T=.8666,w=r.date.getMilliseconds,S=".kendoMultiDayView",C=r.template('<div title="(#=kendo.format("{0:t} - {1:t}", start, end)#): #=title.replace(/"/g,"&\\#34;")#"><div class="k-event-template k-event-time">#:kendo.format("{0:t} - {1:t}", start, end)#</div><div class="k-event-template">${title}</div></div>'),b=r.template('<div title="(#=kendo.format("{0:t}", start)#): #=title.replace(/"/g,"&\\#34;")#"><div class="k-event-template">${title}</div></div>'),x=r.template("#var dateString = isMobile ? kendo.toString(date,'ddd')[0] : kendo.toString(date,'ddd M/dd'); #<span class='k-link k-nav-day'>#=dateString#</span>"),E='<div role="gridcell" aria-selected="false" data-#=ns#uid="#=uid#"#if (resources[0]) { #style="background-color:#=resources[0].color#; border-color: #=resources[0].color#"class="k-event"#} else {#class="k-event"#}#><span class="k-event-actions"># if(data.tail || data.middle) {#<span class="k-icon k-i-arrow-60-left"></span>#}## if(data.isException()) {#<span class="k-icon k-i-non-recurrence"></span># } else if(data.isRecurring()) {#<span class="k-icon k-i-reload"></span># } #</span>{0}<span class="k-event-actions">#if (showDelete) {#<a href="\\#" class="k-link k-event-delete" title="${data.messages.destroy}" aria-label="${data.messages.destroy}"><span class="k-icon k-i-close"></span></a>#}## if(data.head || data.middle) {#<span class="k-icon k-i-arrow-60-right"></span>#}#</span>#if(resizable && !singleDay && !data.tail && !data.middle){#<span class="k-resize-handle k-resize-w"></span>#}##if(resizable && !singleDay && !data.head && !data.middle){#<span class="k-resize-handle k-resize-e"></span>#}#</div>',H='<div role="gridcell" aria-selected="false" data-#=ns#uid="#=uid#" #if (resources[0]) { #style="background-color:#=resources[0].color #; border-color: #=resources[0].color#"class="k-event"#} else {#class="k-event"#}#><span class="k-event-actions"># if(data.isException()) {#<span class="k-icon k-i-non-recurrence"></span># } else if(data.isRecurring()) {#<span class="k-icon k-i-reload"></span># } #</span>{0}<span class="k-event-actions">#if (showDelete) {#<a href="\\#" class="k-link k-event-delete" title="${data.messages.destroy}" aria-label="${data.messages.destroy}"><span class="k-icon k-i-close"></span></a>#}#</span><span class="k-event-top-actions"># if(data.tail || data.middle) {#<span class="k-icon k-i-arrow-60-up"></span># } #</span><span class="k-event-bottom-actions"># if(data.head || data.middle) {#<span class="k-icon k-i-arrow-60-down"></span># } #</span># if(resizable && !data.tail && !data.middle) {#<span class="k-resize-handle k-resize-n"></span># } ## if(resizable && !data.head && !data.middle) {#<span class="k-resize-handle k-resize-s"></span># } #</div>',M=c.extend({init:function(e,t){var a=this;c.fn.init.call(a,e,t),a.title=a.options.title||a.options.name,a._workDays=o(a.options),a._templates(),a._editable(),a.calculateDateRange(),a._groups(),a._currentTime(!0)},_currentTimeMarkerUpdater:function(){this._updateCurrentTimeMarker(new Date)},_updateCurrentTimeMarker:function(t){var a,s,i,n,o,l,h,c,f,m,g,_,v,k,w,S,C=this.options;for(C.currentTimeMarker.useLocalTimezone===!1&&(a=C.dataSource.options.schema.timezone,C.dataSource&&a&&(s=r.timezone.offset(t,a),t=r.timezone.convert(t,t.getTimezoneOffset(),s))),this.times.find("."+y).remove(),this.content.find("."+y).remove(),i=C.group&&"horizontal"!=C.group.orientation?this.groups.length:1,n=this.times.find("tr:first th:first"),o=this.times.find("tr:first th:last"),l=0;l<i;l++){if(h=this.groups[l],!h)return;if(c=r.date.toUtcTime(t),f=h.timeSlotRanges(c,c+1),0===f.length)return;m=f[0].collection,g=m.slotByStartDate(t),g&&(_="<div class='"+y+"'></div>",v=e(_).prependTo(this.times),k=Math.round(f[0].innerRect(t,new Date(t.getTime()+1),!1).top),w={},S=this.content[0].scrollWidth,(d.msie||d.edge)&&(S-=1),this._isRtl?(w.right=n.position().left+p(n)-p(o),v.addClass(D+"-left")):(w.left=o.position().left,v.addClass(D+"-right")),w.top=k-u(v)*T/2,v.css(w),e(_).prependTo(this.content).css({top:k,height:"1px",right:0,width:S,left:0}))}},_currentTime:function(e){var a=this,s=a.options.currentTimeMarker;s!==!1&&s.updateInterval!==t&&(a._currentTimeMarkerUpdater(),e&&(a._currentTimeUpdateTimer=setInterval(m(this._currentTimeMarkerUpdater,a),s.updateInterval)))},_updateResizeHint:function(e,t,a,s){var i,n,o,l,d,h,u,p,f,m,g,_,v=e.isMultiDay(),y=this.groups[t],D=y.ranges(a,s,v,e.isAllDay);for(this._removeResizeHint(),d=0;d<D.length;d++)if(h=D[d],u=h.startSlot(),this._isGroupedByDate()&&v)for(p=u.index;p<=h.end.index;p++)f=h.collection._slots[p],i=f.offsetWidth,n=f.clientHeight,o=f.offsetTop,l=c.fn._createResizeHint.call(this,f.offsetLeft,o,i,n),this._resizeHint=this._resizeHint.add(l);else i=u.offsetWidth,n=u.clientHeight,o=u.offsetTop,v?i=h.innerWidth():(m=h.outerRect(a,s,this.options.snap),o=m.top,n=m.bottom-m.top),l=c.fn._createResizeHint.call(this,u.offsetLeft,o,i,n),this._resizeHint=this._resizeHint.add(l);g="t",_=this.content,v&&(g="M/dd",_=this.element.find(".k-scheduler-header-wrap:has(.k-scheduler-header-all-day) > div"),_.length||(_=this.content)),this._resizeHint.appendTo(_),this._resizeHint.find(".k-label-top,.k-label-bottom").text(""),this._resizeHint.first().addClass("k-first").find(".k-label-top").text(r.toString(r.timezone.toLocalDate(a),g)),this._resizeHint.last().addClass("k-last").find(".k-label-bottom").text(r.toString(r.timezone.toLocalDate(s),g))},_updateMoveHint:function(t,a,s){var i,n,o,l,d,h,c,u,p,f,m=t.isMultiDay(),g=this.groups[a],_=r.date.toUtcTime(t.start)+s,v=_+t.duration(),y=g.ranges(_,v,m,t.isAllDay);for(_=r.timezone.toLocalDate(_),v=r.timezone.toLocalDate(v),this._removeMoveHint(t.uid),!m&&(0===w(v)||w(v)<w(this.startTime()))&&y.length>1&&y.pop(),i=e(),n=0;n<y.length;n++)if(o=y[n],l=o.start,h={left:l.offsetLeft+2,top:l.offsetTop},this._isGroupedByDate()&&m)for(c=l.index;c<=o.end.index;c++)u=o.collection._slots[c],h.left=this._isRtl?.1*u.clientWidth+u.offsetLeft+2:u.offsetLeft+2,h.height=u.offsetHeight,h.width=.9*u.clientWidth-4,d=this._createEventElement(t.clone({start:_,end:v}),!m),t.inverseColor&&d.addClass(k),this._appendMoveHint(d,h),i=i.add(d);else this._isRtl&&(h.left=.1*l.clientWidth+l.offsetLeft+2),m?h.width=o.innerWidth()-4:(p=o.outerRect(_,v,this.options.snap),h.top=p.top,h.height=p.bottom-p.top,h.width=.9*l.clientWidth-4),d=this._createEventElement(t.clone({start:_,end:v}),!m),t.inverseColor&&d.addClass(k),this._appendMoveHint(d,h),i=i.add(d);f=this.content,m&&(f=this.element.find(".k-scheduler-header-wrap:has(.k-scheduler-header-all-day) > div"),f.length||(f=this.content)),i.appendTo(f)},_appendMoveHint:function(e,t){e.addClass("k-event-drag-hint"),e.css(t),this._moveHint=this._moveHint.add(e)},_slotByPosition:function(e,t){var a,s,i,n;for(this._isVerticallyGrouped()?(s=this.content.offset(),t+=this.content[0].scrollTop,e+=this.content[0].scrollLeft):s=this.element.find(".k-scheduler-header-wrap:has(.k-scheduler-header-all-day)").find(">div").offset(),s&&(e-=s.left,t-=s.top),e=Math.ceil(e),t=Math.ceil(t),n=0;n<this.groups.length;n++)if(i=this.groups[n],a=i.daySlotByPosition(e,t,this._isGroupedByDate()))return a;for(s&&(e+=s.left,t+=s.top),s=this.content.offset(),e-=s.left,t-=s.top,this._isVerticallyGrouped()||(t+=this.content[0].scrollTop,e+=this.content[0].scrollLeft),e=Math.ceil(e),t=Math.ceil(t),n=0;n<this.groups.length;n++)if(i=this.groups[n],a=i.timeSlotByPosition(e,t))return a;return null},_groupCount:function(){var e=this.groupedResources,t=this._isGroupedByDate();return e.length?"vertical"===this._groupOrientation()?t?this._columnCountForLevel(e.length-1):this._rowCountForLevel(e.length-1):t?this._columnCountForLevel(e.length)/this._columnCountForLevel(0):this._columnCountForLevel(e.length)/this._columnOffsetForResource(e.length):1},_columnCountInResourceView:function(){var e=this.groupedResources,t=this._isGroupedByDate();return!e.length||this._isVerticallyGrouped()?t?this._rowCountForLevel(0):this._columnCountForLevel(0):t?this._columnCountForLevel(0):this._columnOffsetForResource(e.length)},_timeSlotGroups:function(e,t){var a,s,i,n,o,r,l,d,h,c,u=this._timeSlotInterval(),p=e,f=this._isGroupedByDate(),m=this.content.find("tr:not(.k-scheduler-header-all-day)");for(m.attr("role","row"),o=m.length,this._isVerticallyGrouped()&&(f&&(p=t),o=Math.floor(o/p)),r=0;r<p;r++)for(l=0,d=0,this._isVerticallyGrouped()?l=r:d=r,i=l*o;i<(l+1)*o;){if(h=m[i].children,i%o===0&&(s=w(new Date((+this.startTime())))),c=0,f)if(this._isVerticallyGrouped())for(n=0;n<e;n++)a=this.groups[n],this._addTimeSlotGroup(a,h,n,s,u,r);else for(a=this.groups[r],n=d;n<e*t;n+=e)this._addTimeSlotGroup(a,h,n,s,u,c),c++;else for(a=this.groups[r],n=d*t;n<(d+1)*t;n++)this._addTimeSlotGroup(a,h,n,s,u,c),c++;s+=u,i++}},_addTimeSlotGroup:function(e,t,a,s,i,n){var o,r,l,d=t[a],h=e.getTimeSlotCollection(n),c=this._dates[n];c&&(o=Date.UTC(c.getFullYear(),c.getMonth(),c.getDate()),r=o+s,l=r+i,d.setAttribute("role","gridcell"),d.setAttribute("aria-selected",!1),h.addTimeSlot(d,r,l))},_addDaySlotGroup:function(e,t,a,s,i){var n,o=t[a],l=this._dates[i];l&&(n=Date.UTC(l.getFullYear(),l.getMonth(),l.getDate()),o.setAttribute("role","gridcell"),o.setAttribute("aria-selected",!1),e.addDaySlot(o,n,n+r.date.MS_PER_DAY))},_daySlotGroups:function(e,t){var a,s,i,n,o,r,l,d,h,c=e,u=this._isGroupedByDate();for(this._isVerticallyGrouped()?(u&&(c=t),a=this.element.find(".k-scheduler-header-all-day")):a=this.element.find(".k-scheduler-header-all-day tr"),a.attr("role","row"),i=0;i<c;i++)if(n=0,this._isVerticallyGrouped()&&(n=i),l=a[n].children,d=0,this._isVerticallyGrouped()||(d=i),h=0,u)if(this._isVerticallyGrouped())for(s=0;s<e;s++)o=this.groups[s],r=o.getDaySlotCollection(0),this._addDaySlotGroup(r,l,s,t,i);else for(o=this.groups[i],r=o.getDaySlotCollection(0),s=d;s<e*t;s+=e)this._addDaySlotGroup(r,l,s,t,h),h++;else for(o=this.groups[i],r=o.getDaySlotCollection(0),s=d*t;s<(d+1)*t;s++)this._addDaySlotGroup(r,l,s,t,h),h++},_groups:function(){var e,t,a,s=this._groupCount(),i=this._columnCountInResourceView();for(this.groups=[],e=0;e<s;e++){for(t=this._addResourceView(e),a=0;a<i;a++)this._dates[a]&&t.addTimeSlotCollection(this._dates[a],r.date.addDays(this._dates[a],1));this.options.allDaySlot&&t.addDaySlotCollection(this._dates[0],r.date.addDays(this._dates[this._dates.length-1],1))}this._timeSlotGroups(s,i),this.options.allDaySlot&&this._daySlotGroups(s,i)},options:{name:"MultiDayView",selectedDateFormat:"{0:D}",selectedShortDateFormat:"{0:d}",selectedMobileDateFormat:"{0:MMM} {0:dd} - {1:dd}",allDaySlot:!0,showWorkHours:!1,title:"",startTime:r.date.today(),endTime:r.date.today(),minorTickCount:2,majorTick:60,majorTimeHeaderTemplate:"<span class='k-time-text'>#=kendo.toString(date, 'h:mm')#</span> <span class='k-time-period'>#=kendo.toString(date, 'tt')#</span>",minorTimeHeaderTemplate:"&\\#8203;",groupHeaderTemplate:"#=text#",slotTemplate:"&nbsp;",allDaySlotTemplate:"&nbsp;",eventTemplate:C,allDayEventTemplate:b,dateHeaderTemplate:x,editable:!0,workDayStart:new Date(1980,1,1,8,0,0),workDayEnd:new Date(1980,1,1,17,0,0),workWeekStart:1,workWeekEnd:5,footer:{command:"workDay"},messages:{allDay:"all day",showFullDay:"Show full day",showWorkDay:"Show business hours"},currentTimeMarker:{updateInterval:1e4,useLocalTimezone:!0}},events:["remove","add","edit"],_templates:function(){var e=this.options,t=f({},r.Template,e.templateSettings);this.eventTemplate=this._eventTmpl(e.eventTemplate,H),this.allDayEventTemplate=this._eventTmpl(e.allDayEventTemplate,E),this.majorTimeHeaderTemplate=r.template(e.majorTimeHeaderTemplate,t),this.minorTimeHeaderTemplate=r.template(e.minorTimeHeaderTemplate,t),this.dateHeaderTemplate=r.template(e.dateHeaderTemplate,t),this.slotTemplate=r.template(e.slotTemplate,t),this.allDaySlotTemplate=r.template(e.allDaySlotTemplate,t),this.groupHeaderTemplate=r.template(e.groupHeaderTemplate,t)},_editable:function(){this.options.editable&&(this._isMobile()?this._touchEditable():this._mouseEditable())},_mouseEditable:function(){var t=this;t.element.on("click"+S,".k-event a:has(.k-i-close)",function(a){t.trigger("remove",{uid:e(this).closest(".k-event").attr(r.attr("uid"))}),a.preventDefault()}),t.options.editable.create!==!1&&t.element.on("dblclick"+S,".k-scheduler-content td",function(a){var s,i;e(this).parent().hasClass("k-scheduler-header-all-day")||(s=t._slotByPosition(a.pageX,a.pageY),s&&(i=t._resourceBySlot(s),t.trigger("add",{eventInfo:f({start:s.startDate(),end:s.endDate()},i)})),a.preventDefault())}).on("dblclick"+S,".k-scheduler-header-all-day td",function(e){var a,s=t._slotByPosition(e.pageX,e.pageY);s&&(a=t._resourceBySlot(s),t.trigger("add",{eventInfo:f({},{isAllDay:!0,start:r.date.getDate(s.startDate()),end:r.date.getDate(s.startDate())},a)})),e.preventDefault()}),t.options.editable.update!==!1&&t.element.on("dblclick"+S,".k-event",function(a){t.trigger("edit",{uid:e(this).closest(".k-event").attr(r.attr("uid"))}),a.preventDefault()})},_touchEditable:function(){var a=this,s=0;r.support.mobileOS.android&&(s=5),a.options.editable.create!==!1&&(a._addUserEvents=new r.UserEvents(a.element,{threshold:s,filter:".k-scheduler-content td",useClickAsTap:!r.support.browser.edge,tap:function(s){var i,n,o,r;a._scrolling||e(s.target).parent().hasClass("k-scheduler-header-all-day")||(i=s.x.location!==t?s.x.location:s.x,n=s.y.location!==t?s.y.location:s.y,o=a._slotByPosition(i,n),o&&(r=a._resourceBySlot(o),a.trigger("add",{eventInfo:f({start:o.startDate(),end:o.endDate()},r)})),s.preventDefault())}}),a._allDayUserEvents=new r.UserEvents(a.element,{threshold:s,useClickAsTap:!r.support.browser.edge,filter:".k-scheduler-header-all-day td",tap:function(e){var s,i,n,o;a._scrolling||(s=e.x.location!==t?e.x.location:e.x,i=e.y.location!==t?e.y.location:e.y,n=a._slotByPosition(s,i),n&&(o=a._resourceBySlot(n),a.trigger("add",{eventInfo:f({},{isAllDay:!0,start:r.date.getDate(n.startDate()),end:r.date.getDate(n.startDate())},o)})),e.preventDefault())}})),a.options.editable.update!==!1&&(a._editUserEvents=new r.UserEvents(a.element,{threshold:s,useClickAsTap:!r.support.browser.edge,filter:".k-event",tap:function(t){var s,i;a._scrolling||(s=e(t.target).closest(".k-event"),i=e(t.touch.initialTouch),i.hasClass("k-i-close")?a.trigger("remove",{uid:s.attr(r.attr("uid"))}):s.hasClass("k-event-active")||a.trigger("edit",{uid:s.attr(r.attr("uid"))}),t.preventDefault())}}))},_layout:function(e){var t,a,s,i=[],n=[],o=this.options,l=this,d=l._isGroupedByDate();for(t=0;t<e.length;t++)a={},a.text=l.dateHeaderTemplate({date:e[t],isMobile:l._isMobile()}),r.date.isToday(e[t])&&(a.className="k-today"),i.push(a);return s=this.groupedResources,o.allDaySlot&&n.push({text:o.messages.allDay,allDay:!0,cellContent:function(t){var a=t;return t=s.length&&"vertical"!==l._groupOrientation()?t%e.length:t,l.allDaySlotTemplate({date:e[t],resources:function(){return l._resourceBySlot({groupIndex:a})}})}}),this._forTimeRange(this.startTime(),this.endTime(),function(e,t,a,s){var i=t?l.majorTimeHeaderTemplate:l.minorTimeHeaderTemplate,o={text:i({date:e}),className:s?"k-slot-cell":""};n.push(o)}),s.length&&("vertical"===this._groupOrientation()?d?(n=this._createDateLayout(i,n),i=this._createColumnsLayout(s,null,this.groupHeaderTemplate)):n=this._createRowsLayout(s,n,this.groupHeaderTemplate):i=d?this._createColumnsLayout(s,i,this.groupHeaderTemplate,i):this._createColumnsLayout(s,i,this.groupHeaderTemplate)),{columns:i,rows:n}},_footer:function(){var t,a,s,i=this.options;i.footer!==!1&&(t='<div class="k-header k-scheduler-footer">',a=i.footer.command,this._isMobile()&&(t+='<span class="k-state-default k-scheduler-today"><a href="#" class="k-link">',t+=i.messages.today+"</a></span>"),a&&"workDay"===a?this._isMobile()?(t+='<span class="k-state-default k-scheduler-fullday"><a href="#" class="k-link">',t+=(i.showWorkHours?i.messages.showFullDay:i.messages.showWorkDay)+"</a></span>"):(t+='<ul class="k-reset k-header">',t+='<li class="k-state-default k-scheduler-fullday"><a href="#" class="k-link"><span class="k-icon k-i-clock"></span>',t+=(i.showWorkHours?i.messages.showFullDay:i.messages.showWorkDay)+"</a></li>",t+="</ul>"):t+="&nbsp;",t+="</div>",this.footer=e(t).appendTo(this.element),s=this,this.footer.on("click"+S,".k-scheduler-fullday",function(e){e.preventDefault(),s.trigger("navigate",{view:s.name||i.name,date:i.date,isWorkDay:!i.showWorkHours})}),this.footer.on("click"+S,".k-scheduler-today",function(e){var t,a,n,o,l;e.preventDefault(),t=s.options.timezone,a="today",n=new Date,t?(l=r.timezone.offset(n,t),o=r.timezone.convert(n,n.getTimezoneOffset(),l)):o=n,s.trigger("navigate",{view:s.name||i.name,action:a,date:o})}))},_forTimeRange:function(e,t,s,i){var n,o,r,l,d,c,u,p,f,m,g,y,D,k,T,S;for(e=a(e),t=a(t),n=this,o=w(e),r=w(t),l=n.options.minorTickCount,d=n.options.majorTick*_,c=d/l||1,u=new Date((+e)),p=u.getDate(),m=0,y="",g=v/c,o!=r&&(o>r&&(r+=v),g=(r-o)/c),g=Math.round(g);m<g;m++)D=m%(d/c),k=0===D,T=D<l-1,S=D===l-1,y+=s(u,k,T,S),h(u,c,!1);return r&&(f=w(u),p<u.getDate()&&(f+=v),f>r&&(u=new Date((+t)))),i&&(y+=i(u)),y},_content:function(e){var t,a,s,i=this,n=i.options,o=i.startTime(),r=this.endTime(),l=1,d=1,h=e.length,c="",u=this.groupedResources,p=this.allDaySlotTemplate,f=!1,m=i._isGroupedByDate(),g=0;for(u.length&&(f="vertical"===i._groupOrientation(),f?(d=this._rowCountForLevel(this.rowLevels.length-2),m&&(l=this._columnCountForLevel(this.columnLevels.length-1)),n.allDaySlot&&(t=function(t){var a,s='<tr class="k-scheduler-header-all-day">',n=m?0:t,o=function(){return i._resourceBySlot({groupIndex:n})};if(m)for(;n<l;n++)s+="<td>"+p({date:e[g],resources:o})+"</td>";else for(a=0;a<e.length;a++)s+="<td>"+p({date:e[a],resources:o})+"</td>";return s+"</tr>"})):l=m?this._columnCountForLevel(this.columnLevels.length-1)/this._columnCountForLevel(0):this._columnCountForLevel(this.columnLevels.length-2)),c+="<tbody>",a=function(t,a,n){var o,r,d,c="",u=0;if(c="<tr"+(n?' class="k-middle-row"':"")+">",m)for(o=0,r=h;o<r;o++){for(u=0;u<l;u++)d=o,f&&(d=g),c=i._addCellsToContent(c,e,t,d,u,s);if(f)break}else for(;u<l;u++)for(o=0,r=h;o<r;o++)c=i._addCellsToContent(c,e,t,o,u,s);return c+="</tr>"},s=0;s<d;s++)c+=t?t(s):"",c+=this._forTimeRange(o,r,a),f&&g++;c+="</tbody>",this.content.find("table").append(c)},_addCellsToContent:function(e,t,a,s,i,n){var o,l=this,d="",h=this.slotTemplate,c="vertical"===this._groupOrientation(),u=function(e){return function(){return l._resourceBySlot({groupIndex:e})}};return r.date.isToday(t[s])&&(d+="k-today"),(r.date.getMilliseconds(a)<r.date.getMilliseconds(this.options.workDayStart)||r.date.getMilliseconds(a)>=r.date.getMilliseconds(this.options.workDayEnd)||!this._isWorkDay(t[s]))&&(d+=" k-nonwork-hour"),e+="<td"+(""!==d?' class="'+d+'"':"")+">",o=r.date.getDate(t[s]),r.date.setTime(o,r.date.getMilliseconds(a)),e+=h({date:o,resources:u(c&&!l._isGroupedByDate()?n:i)}),e+="</td>"},_isWorkDay:function(e){var t,a=e.getDay(),s=this._workDays;for(t=0;t<s.length;t++)if(s[t]===a)return!0;return!1},_render:function(t){var a,s=this;t=t||[],this._dates=t,this._startDate=t[0],this._endDate=t[t.length-1||0],this.createLayout(this._layout(t)),this._content(t),this._footer(),this.refreshLayout(),a=this.element.find(".k-scheduler-header-all-day td"),a.length&&(this._allDayHeaderHeight=a.first()[0].clientHeight),s.element.on("click"+S,".k-nav-day",function(t){var a,i=e(t.currentTarget).closest("th"),n=i.offset(),o=0,r=p(i);s._isGroupedByDate()&&(s._isVerticallyGrouped()?(o=u(s.times),r=0):r=p(s.datesHeader)),a=s._slotByPosition(n.left+o,n.top+r),s.trigger("navigate",{view:"day",date:a.startDate()})})},startTime:function(){var e=this.options;return e.showWorkHours?e.workDayStart:e.startTime},endTime:function(){var e=this.options;return e.showWorkHours?e.workDayEnd:e.endTime},startDate:function(){return this._startDate},endDate:function(){return this._endDate},_end:function(e){var t=w(this.endTime())||v;return e&&(t=0),new Date(this._endDate.getTime()+t)},nextDate:function(){return r.date.nextDay(this.endDate())},previousDate:function(){return r.date.previousDay(this.startDate())},calculateDateRange:function(){this._render([this.options.date])},destroy:function(){var e=this;e._currentTimeUpdateTimer&&clearInterval(e._currentTimeUpdateTimer),e.datesHeader&&e.datesHeader.off(S),e.element&&e.element.off(S),e.footer&&e.footer.remove(),c.fn.destroy.call(this),this._isMobile()&&e.options.editable&&(e.options.editable.create!==!1&&(e._addUserEvents.destroy(),e._allDayUserEvents.destroy()),e.options.editable.update!==!1&&e._editUserEvents.destroy())},inRange:function(e){var t,a,s,i,n=c.fn.inRange.call(this,e);return e.isAllDay?n:(t=w(this.startTime()),a=w(this.endTime())||r.date.MS_PER_DAY,s=w(e.start),i=w(e.end)||r.date.MS_PER_DAY,n&&t<=s&&i<=a)},selectionByElement:function(e){var t=e.offset();return this._slotByPosition(t.left,t.top)},_timeSlotInterval:function(){var e=this.options;return e.majorTick/e.minorTickCount*_},_timeSlotIndex:function(e){var t=this.options,a=w(e),s=w(this.startTime()),i=t.majorTick/t.minorTickCount*_;return(a-s)/i},_slotIndex:function(e,t){return t?this._dateSlotIndex(e):this._timeSlotIndex(e)},_dateSlotIndex:function(e,t){var a,i,n,o,l=this._dates||[],d=1;for(a=0,i=l.length;a<i;a++)if(n=r.date.getDate(l[a]),o=new Date(r.date.getDate(l[a]).getTime()+v-(t?0:1)),s(e,n,o))return a*d;return-1},_positionAllDayEvent:function(t,a){var s,i,n,o,r,l,d,h=a.innerWidth(),u=a.start.index,p=a.end.index,f=c.collidingEvents(a.events(),u,p),m=this._headerColumnCount||0,g=2,_=u!==p?5:4,v=this._allDayHeaderHeight,y=a.startSlot();for(t.css({left:y.offsetLeft+g,width:h-_}),a.addEvent({slotIndex:u,start:u,end:p,element:t}),f.push({slotIndex:u,start:u,end:p,element:t}),s=c.createRows(f),s.length&&s.length>m&&(this._headerColumnCount=s.length),i=a.start.offsetTop,n=0,o=s.length;n<o;n++)for(r=s[n].events,l=0,d=r.length;l<d;l++)e(r[l].element).css({top:i+n*v})},_arrangeColumns:function(e,t,a,s){var i,n,o,r,l,d,h,u,p,f,m,g,_=s.start;for(e={element:e,slotIndex:_.index,start:t,end:t+a},n=_.clientWidth,o=.1*n,l=s.events(),d=c.collidingEvents(l,e.start,e.end),s.addEvent(e),d.push(e),i=c.createColumns(d),h=(n-o)/i.length,u=0,p=i.length;u<p;u++)for(r=i[u].events,f=0,m=r.length;f<m;f++)g=h-4,r[f].element[0].style.width=(g>0?g:h)+"px",r[f].element[0].style.left=(this._isRtl?o:0)+_.offsetLeft+u*h+2+"px"},_positionEvent:function(e,t,a){var s=e._startTime||e.start,i=e._endTime||e.end,n=a.innerRect(s,i,!1),o=n.bottom-n.top-2;o<0&&(o=0),t.css({top:n.top,height:o}),this._arrangeColumns(t,n.top,t[0].clientHeight,a)},_createEventElement:function(t,a,i,n){var o,l,d,h,c,u,p=a?this.eventTemplate:this.allDayEventTemplate,m=this.options,_=m.editable,y=this._isMobile(),D=_&&_.destroy!==!1&&!y,k=_&&_.resize!==!1,T=g(this.startDate()),S=g(this.endDate()),C=w(this.startTime()),b=w(this.endTime()),x=t._time("start"),E=t._time("end");return C>=b&&(b=w(new Date(this.endTime().getTime()+v-1))),a||t.isAllDay||(S=new Date(S.getTime()+v)),l=t.start,d=t.end,t.isAllDay&&(d=g(t.end)),!s(g(l),T,S)&&!s(d,T,S)||a&&x<C&&E>b?o=!0:g(l)<T||a&&x<C?n=!0:(d>S&&!a||a&&E>b)&&(i=!0),h=this.eventResources(t),t._startTime&&x!==r.date.getMilliseconds(t.start)&&(l=new Date(x),l=r.timezone.apply(l,"Etc/UTC")),t._endTime&&E!==r.date.getMilliseconds(t.end)&&(d=new Date(E),d=r.timezone.apply(d,"Etc/UTC")),c=f({},{ns:r.ns,resizable:k,showDelete:D,middle:o,head:i,tail:n,singleDay:1==this._dates.length,resources:h,inverseColor:!1,messages:m.messages},t,{start:l,end:d}),u=e(p(c)),this.angular("compile",function(){return{elements:u,data:[{dataItem:c}]}}),u},_isInTimeSlot:function(e){var t,a=this.startTime(),s=this.endTime(),n=e._startTime||e.start,o=e._endTime||e.end;return w(s)===w(r.date.getDate(s))&&(s=r.date.getDate(s),h(s,v-1)),e._date("end")>e._date("start")&&(o=+e._date("end")+(v-1)),o=e._endTime?o-e._date("end"):w(new Date(o)),n=e._startTime?n-e._date("start"):w(new Date(n)),s=w(s),a=w(a),a===n&&n===o||(t=n!==s,i(n,a,s,t)||i(o,a,s,t)||i(a,n,o)||i(s,n,o))},_isInDateSlot:function(e){var t=this.groups[0],a=t.firstSlot().start,i=t.lastSlot().end-1,n=r.date.toUtcTime(e.start),o=r.date.toUtcTime(e.end);return(s(n,a,i)||s(o,a,i)||s(a,n,o)||s(i,n,o))&&(!s(o,a,a)||s(o,n,n)||e.isAllDay)},_updateAllDayHeaderHeight:function(e){var t,a;if(this._height!==e&&(this._height=e,t=this.element.find(".k-scheduler-header-all-day td"),t.length))for(t.parent().add(this.element.find(".k-scheduler-times-all-day").parent()).height(e),a=0;a<this.groups.length;a++)this.groups[a].refresh()},_renderEvents:function(e,t){var a,s,i,o,r,l,d,h,c,u,p,f,m,g,_,y,D,k,T,w,S,C=this.datesHeader.find(".k-scheduler-header-wrap > div"),b=this._isGroupedByDate();for(s=0,i=e.length;s<i;s++)if(a=e[s],this._isInDateSlot(a))if(o=a.isAllDay||a.duration()>=v,r=o&&!this._isVerticallyGrouped()?C:this.content,o){if(this.options.allDaySlot&&(p=this.groups[t],p._continuousEvents||(p._continuousEvents=[]),d=p.slotRanges(a),d.length))if(h=d[0],y=h.start.index,D=h.end.index,b&&y!==D)for(c=h.start.start,u=h.end.end,k=new Date(c),T=new Date(c),w=h.start.index;w<=h.end.index;w++)l=this._createEventElement(a,!o,w!==D,w!==y),S=p.daySlotRanges(k,T,!0)[0],T.setDate(T.getDate()+1),k.setDate(k.getDate()+1),this._positionAllDayEvent(l,S),n(p,S,l,!0),l.appendTo(r),this._inverseEventColor(l);else l=this._createEventElement(a,!o),this._positionAllDayEvent(l,d[0]),n(p,d[0],l,!0),l.appendTo(r),this._inverseEventColor(l)}else if(this._isInTimeSlot(a))for(p=this.groups[t],p._continuousEvents||(p._continuousEvents=[]),d=p.slotRanges(a),f=d.length,m=0;m<f;m++)h=d[m],c=a.start,u=a.end,f>1&&(0===m?u=h.end.endDate():m==f-1?c=h.start.startDate():(c=h.start.startDate(),u=h.end.endDate())),g=a.clone({start:c,end:u,_startTime:a._startTime,_endTime:a.endTime}),this._isInTimeSlot(g)&&(_=h.head,l=this._createEventElement(a,!o,_,h.tail),l.appendTo(r),this._inverseEventColor(l),this._positionEvent(g,l,h),n(p,h,l,!1))},render:function(t){var a,i,n,o;for(this._headerColumnCount=0,this._groups(),this.element.find(".k-event").remove(),t=new r.data.Query(t).sort([{field:"start",dir:"asc"},{field:"end",dir:"desc"}]).toArray(),a=[],this._eventsByResource(t,this.groupedResources,a),i=e.map(this._dates,function(t){return Math.max.apply(null,e.map(a,function(a){return e.grep(a,function(e){return e.isMultiDay()&&s(t,g(e.start),g(e.end))}).length}))}),n=Math.max.apply(null,i),this._updateAllDayHeaderHeight((n+1)*this._allDayHeaderHeight),o=0;o<a.length;o++)this._renderEvents(a[o],o);this.refreshLayout(),this._currentTime(!1),this.trigger("activate")},_eventsByResource:function(e,t,a){var s,i,n,o,l=t[0];if(l)for(s=l.dataSource.view(),i=0;i<s.length;i++)n=this._resourceValue(l,s[i]),o=new r.data.Query(e).filter({field:l.field,operator:c.groupEqFilter(n)}).toArray(),t.length>1?this._eventsByResource(o,t.slice(1),a):a.push(o);else a.push(e)},_columnOffsetForResource:function(e){return this._columnCountForLevel(e)/this._columnCountForLevel(e-1)},_columnCountForLevel:function(e){var t=this.columnLevels[e];return t?t.length:0},_rowCountForLevel:function(e){var t=this.rowLevels[e];return t?t.length:0},clearSelection:function(){this.content.add(this.datesHeader).find(".k-state-selected").removeAttr("id").attr("aria-selected",!1).removeClass("k-state-selected")},_updateDirection:function(e,t,a,s,i){var n=e.isAllDay,o=t[0].start,r=t[t.length-1].end;a&&(i?n||o.index!==r.index||o.collectionIndex!==r.collectionIndex||(e.backward=s):(n&&o.index===r.index||!n&&o.collectionIndex===r.collectionIndex)&&(e.backward=s))},_changeViewPeriod:function(e,t,a){var s,i,n,o,r,l,d,c,u,p,f,m,g;if(!a)return s=t?this.previousDate():this.nextDate(),i=e.start,n=e.end,o=this._isGroupedByDate()&&this._isVerticallyGrouped(),r=this.groups[e.groupIndex],l=t?r._timeSlotCollections:r._getCollections(r.daySlotCollectionCount()),d=l[l.length-1]._slots,c=t||r.daySlotCollectionCount()?d.length-1:0,p=new Date(s),f=new Date(s),!this._isInRange(p,f)&&(e.start=p,e.end=f,o?(m=new Date(d[c].startDate()),g=new Date(d[c].endDate()),u=w(g)?w(g):v,h(e.start,w(m)),h(e.end,u),r.daySlotCollectionCount()&&(e.isAllDay=!e.isAllDay)):(u=e.isAllDay||!w(n)?v:w(n),h(e.start,w(i)),h(e.end,u)),this._isVerticallyGrouped()||(e.groupIndex=t?this.groups.length-1:0),e.events=[],!0)}});f(!0,l,{MultiDayView:M,DayView:M.extend({options:{name:"DayView",title:"Day",selectedMobileDateFormat:"{0:MMM d}"},name:"day"}),WeekView:M.extend({options:{name:"WeekView",title:"Week",selectedDateFormat:"{0:D} - {1:D}",selectedShortDateFormat:"{0:d} - {1:d}"},name:"week",calculateDateRange:function(){var e,t,a=this.options.date,s=r.date.dayOfWeek(a,this.calendarInfo().firstDay,-1),i=[];for(e=0,t=7;e<t;e++)i.push(s),s=r.date.nextDay(s);this._render(i)}}),WorkWeekView:M.extend({options:{name:"WorkWeekView",title:"Work Week",selectedDateFormat:"{0:D} - {1:D}",selectedShortDateFormat:"{0:d} - {1:d}"},name:"workWeek",nextDate:function(){var e=r.date.dayOfWeek(r.date.nextDay(this.startDate()),this.calendarInfo().firstDay,1);return r.date.addDays(e,this._workDays[0])},previousDate:function(){var e=r.date.dayOfWeek(this.startDate(),this.calendarInfo().firstDay,-1),t=this._workDays;return r.date.addDays(e,t[t.length-1]-7)},calculateDateRange:function(){for(var e=this.options.date,t=r.date.dayOfWeek,a=t(e,this.calendarInfo().firstDay,-1),s=t(a,this.options.workWeekStart,1),i=t(s,this.options.workWeekEnd,1),n=[];s<=i;)n.push(s),s=r.date.nextDay(s);this._render(n)}})})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,a){(a||t)()});
//# sourceMappingURL=kendo.scheduler.dayview.min.js.map
