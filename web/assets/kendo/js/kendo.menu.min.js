/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.menu.min",["kendo.popup.min","kendo.data.min"],e)}(function(){return function(e,t){function n(e,t){return e=e.split(" ")[!t+0]||e,e.replace("top","up").replace("bottom","down")}function o(e,t,n){e=e.split(" ")[!t+0]||e;var o={origin:["bottom",n?"right":"left"],position:["top",n?"right":"left"]},i=/left|right/.test(e);return i?(o.origin=["top",e],o.position[1]=O.directions[e].reverse):(o.origin[0]=e,o.position[0]=O.directions[e].reverse),o.origin=o.origin.join(" "),o.position=o.position.join(" "),o}function i(t,n){try{return e.contains(t,n)}catch(o){return!1}}function r(t){t=e(t),t.addClass("k-item").children(G).addClass(Q),t.children("a").addClass(K).children(G).addClass(Q),t.filter(":not([disabled])").addClass(ye),t.filter(".k-separator").empty().append("&nbsp;"),t.filter("li[disabled]").addClass(Te).removeAttr("disabled").attr("aria-disabled",!0),t.filter("[role]").length||t.attr("role","menuitem"),t.children(X).length||t.contents().filter(function(){return!(this.nodeName.match(L)||3==this.nodeType&&!e.trim(this.nodeValue))}).wrapAll("<span class='"+K+"'/>"),a(t),l(t)}function a(t){t=e(t),t.find("> .k-link > [class*=k-i-arrow-60]:not(.k-sprite)").remove(),t.filter(":has(.k-menu-group)").children(".k-link:not(:has([class*=k-i-arrow]:not(.k-sprite)))").each(function(){var t=e(this),n=s(t);t.append("<span class='k-icon"+n+" k-menu-expand-arrow'/>")})}function s(e){var t,n=e.parent().parent(),o=O.support.isRtl(n);return t=n.hasClass(J+"-horizontal")?" k-i-arrow-60-down":o?" k-i-arrow-60-left":" k-i-arrow-60-right"}function l(t){t=e(t),t.filter(".k-first:not(:first-child)").removeClass($),t.filter(".k-last:not(:last-child)").removeClass(q),t.filter(":first-child").addClass($),t.filter(":last-child").addClass(q)}function p(e){var t,n;if(e&&e.length)for(t in e)n=e.eq(t),n.find("ul").length?n.attr("aria-haspopup",!0):n.removeAttr("aria-haspopup")}function c(e){if(!e.hasClass(J))return e.parentsUntil("."+J,"li")}function u(t,n){var o=f(n);o&&d(t,o),n.items&&e(t).children("ul").children("li").each(function(e){u(this,n.items[e])})}function d(t,n){e(t).children(".k-link").data({selectHandler:n})}function f(e){var t=e.select,n=O.isFunction;return t&&n(t)?t:null}function m(e){return e?"li[data-groupparent='"+e+"']":"li[data-groupparent]"}function h(e){return e?"ul[data-group='"+e+"']":"ul[data-group]"}function v(t,n){var o=t.find(m()),i=[];return o.each(function(o,r){var a,s;for(r=e(r),a=r.data(we),s=t;a;)s=n.find(h(a)+":visible"),s.length&&i.push(s),r=s.find(m()),a=r.data(we)}),i}function g(t,n){var o=t.data(ke);return o?n.find(m(o)):e([])}function _(t,n){var o=t.data(we);return o?n.children(Se).children(h(o)):e([])}function k(t,n){var o,i,r=[],a=function(e){for(;e.parentNode&&!n.is(e.parentNode);)r.push(e.parentNode),e=e.parentNode},s=t[0]||t;for(a(s),o=r[r.length-1];e(o).is(Se)&&(i=e(o).children("ul"),s=g(i,n)[0]);)r.push(s),a(s),o=r[r.length-1];return r}function w(e){var t=0;return e.wheelDelta&&(t=-e.wheelDelta/120,t=t>0?Math.ceil(t):Math.floor(t)),e.detail&&(t=Math.round(e.detail/3)),t}function C(e,t){for(var n=0,o=e.parentNode;o&&!isNaN(o[t]);)n+=o[t],o=o.parentNode;return n}function b(e){return se&&e.originalEvent&&e.originalEvent.pointerType in Fe}function y(e){var t=e.originalEvent;return x&&/touch/i.test(t.type||"")}function P(e){e.contents().filter(function(){return"LI"!=this.nodeName}).remove()}var O=window.kendo,T=O.ui,H=O._activeElement,x=O.support.touch&&O.support.mobileOS,I=e.isArray,S=O.data.HierarchicalDataSource,W="mousedown",E="click",A=30,D=50,M=e.extend,B=e.proxy,N=e.each,R=O.template,U=O.keys,z=T.Widget,L=/^(ul|a|div)$/i,F=".kendoMenu",G="img",j="open",J="k-menu",K="k-link k-menu-link",X=".k-link",Y=".k-icon",q="k-last",V="close",Z="timer",$="k-first",Q="k-image",ee="select",te="zIndex",ne="activate",oe="deactivate",ie="touchstart"+F+" MSPointerDown"+F+" pointerdown"+F,re=O.support.pointers,ae=O.support.msPointers,se=ae||re,le="change",pe="error",ce=O.support.touch?"touchstart":"",ue=re?"pointerover":ae?"MSPointerOver":"mouseenter",de=re?"pointerout":ae?"MSPointerOut":"mouseleave",fe="DOMMouseScroll"+F+" mousewheel"+F,me=O.support.resize+F,he="scrollWidth",ve="scrollHeight",ge="offsetWidth",_e="offsetHeight",ke="group",we="groupparent",Ce=e(document.documentElement),be="kendoPopup",ye="k-state-default",Pe="k-state-hover",Oe="k-state-focused",Te="k-state-disabled",He="k-state-selected",xe=".k-menu",Ie=".k-menu-group",Se=".k-animation-container",We=Ie+","+Se,Ee=":not(.k-list) > .k-item",Ae=".k-item.k-state-disabled",De=".k-item",Me=".k-item:not(.k-state-disabled)",Be=".k-item:not(.k-state-disabled) > .k-link",Ne=":not(.k-item.k-separator)",Re=De+Ne+":eq(0)",Ue=De+Ne+":last",ze="div:not(.k-animation-container,.k-list-container)",Le=".k-menu-scroll-button",Fe={2:1,touch:1},Ge="string",je="dataBound",Je={text:"dataTextField",url:"dataUrlField",spriteCssClass:"dataSpriteCssClassField",imageUrl:"dataImageUrlField",imageAttr:"dataImageAttrField",content:"dataContentField"},Ke={wrapperCssClass:function(e,t){var n="k-item",o=t.index;return n+=t.enabled===!1?" k-state-disabled":" k-state-default",e.firstLevel&&0===o&&(n+=" k-first"),o==e.length-1&&(n+=" k-last"),t.cssClass&&(n+=" "+t.cssClass),t.attr&&t.attr.hasOwnProperty("class")&&(n+=" "+t.attr["class"]),t.selected&&(n+=" "+He),n},itemCssAttributes:function(e){var t,n="",o=e.attr||{};for(t in o)o.hasOwnProperty(t)&&"class"!==t&&(n+=t+'="'+o[t]+'" ');return n},imageCssAttributes:function(e){var t,n="",o=e&&e.toJSON?e.toJSON():{};o["class"]?o["class"]+=" "+Q:o["class"]=Q;for(t in o)o.hasOwnProperty(t)&&(n+=t+'="'+o[t]+'" ');return n},contentCssAttributes:function(e){var t,n="",o=e.contentAttr||{},i="k-content k-group k-menu-group";o["class"]?o["class"]+=" "+i:o["class"]=i;for(t in o)o.hasOwnProperty(t)&&(n+=t+'="'+o[t]+'" ');return n},textClass:function(){return K},arrowClass:function(e,t){var n="k-icon";return n+=t.horizontal?" k-i-arrow-60-down":" k-i-arrow-60-right"},groupAttributes:function(e){return e.expanded!==!0?" style='display:none'":""},groupCssClass:function(){return"k-group k-menu-group"},content:function(e){return e.content?e.content:"&nbsp;"}},Xe=O.ui.DataBoundWidget.extend({init:function(e,t){var n=this;z.fn.init.call(n,e,t),e=n.wrapper=n.element,t=n.options,n._accessors(),n._templates(),n._dataSource(),n._updateClasses(),n._animations(t),n.nextItemZIndex=100,n._tabindex(),n._initOverflow(t),n._attachMenuEventsHandlers(),t.openOnClick&&(n.clicked=!1),e.attr("role","menubar"),e[0].id&&(n._ariaId=O.format("{0}_mn_active",e[0].id)),O.notify(n)},events:[j,V,ne,oe,ee,je],options:{name:"Menu",animation:{open:{duration:200},close:{duration:100}},orientation:"horizontal",direction:"default",openOnClick:!1,closeOnClick:!0,hoverDelay:100,scrollable:!1,popupCollision:t},_initData:function(){var e=this;e.dataSource&&(e.angular("cleanup",function(){return{elements:e.element.children()}}),e.element.empty(),e.append(e.dataSource.view(),e.element),e.angular("compile",function(){return{elements:e.element.children()}}))},_attachMenuEventsHandlers:function(){var t=this,n=t.element,o=t.options,i=t._overflowWrapper();(i||n).on(ie,De,B(t._focusHandler,t)).on(E+F,Ae,!1).on(E+F,De,B(t._click,t)).on(ie+" "+W+F,".k-content",B(t._preventClose,t)).on(ue+F,Me,B(t._mouseenter,t)).on(de+F,Me,B(t._mouseleave,t)).on(W+F,Me,B(t._mousedown,t)).on(ce+F+" "+ue+F+" "+de+F+" "+W+F+" "+E+F,Be,B(t._toggleHover,t)),n.on("keydown"+F,B(t._keydown,t)).on("focus"+F,B(t._focus,t)).on("focus"+F,".k-content",B(t._focus,t)).on("blur"+F,B(t._removeHoverItem,t)).on("blur"+F,"[tabindex]",B(t._checkActiveElement,t)),i&&i.on(de+F,We,B(t._mouseleavePopup,t)).on(ue+F,We,B(t._mouseenterPopup,t)),o.openOnClick&&(t._documentClickHandler=B(t._documentClick,t),e(document).click(t._documentClickHandler))},_detachMenuEventsHandlers:function(){var t=this,n=t._overflowWrapper();n&&n.off(F),t.element.off(F),t._documentClickHandler&&e(document).unbind("click",t._documentClickHandler)},_initOverflow:function(t){var n,o,i,r,a=this,s="horizontal"==t.orientation;t.scrollable&&(a._openedPopups={},a._scrollWrapper=a.element.wrap("<div class='k-menu-scroll-wrapper "+t.orientation+"'></div>").parent(),s&&P(a.element),n=e(a.templates.scrollButton({direction:s?"left":"up"})),o=e(a.templates.scrollButton({direction:s?"right":"down"})),n.add(o).appendTo(a._scrollWrapper),a._initScrolling(a.element,n,o,s),i=a.element.outerWidth(),r=a.element[0].style.width,r="auto"===r?"":r,s&&e(window).on(me,O.throttle(function(){a._setOverflowWrapperWidth(i,r),a._toggleScrollButtons(a.element,n,o,s)},100)),a._setOverflowWrapperWidth(i,r),a._toggleScrollButtons(a.element,n,o,s))},_overflowWrapper:function(){return this._scrollWrapper||this._popupsWrapper},_setOverflowWrapperWidth:function(e,t){var n,o,i,r,a=this,s=a._scrollWrapper.css("width");a._scrollWrapper.css({width:""}),n=a._scrollWrapper.outerWidth(),a._scrollWrapper.css({width:s}),o=a.element.outerWidth(),i=a.element[0].offsetWidth-a.element[0].clientWidth,o!=n&&n>0&&(r=t?Math.min(e,n):n,a.element.width(r-i),a._scrollWrapper.width(r))},_reinitOverflow:function(e){var t=this,n=e.scrollable&&!t.options.scrollable||!e.scrollable&&t.options.scrollable||e.scrollable&&t.options.scrollable&&e.scrollable.distance!=t.options.scrollable.distance||e.orientation!=t.options.orientation;n&&(t._detachMenuEventsHandlers(),t._destroyOverflow(),t._initOverflow(e),t._attachMenuEventsHandlers())},_destroyOverflow:function(){var n=this,o=n._overflowWrapper();o&&(o.off(F),o.find(Le).off(F).remove(),o.children(Se).each(function(t,n){var i,r=e(n).children(Ie);r.off(fe),i=g(r,o),i.length&&i.append(n)}),o.find(m()).removeAttr("data-groupparent"),o.find(h()).removeAttr("data-group"),n.element.off(fe),e(window).off(me),o.contents().unwrap(),n._scrollWrapper=n._popupsWrapper=n._openedPopups=t)},_initScrolling:function(t,n,o,i){var r=this,a=r.options.scrollable,s=e.isNumeric(a.distance)?a.distance:D,l=s/2,p="-="+s,c="+="+s,u="-="+2*s,d="+="+2*s,f=!1,m=!1,h=function(e){var a=i?{scrollLeft:e}:{scrollTop:e};t.finish().animate(a,"fast","linear",function(){f&&h(e)}),r._toggleScrollButtons(t,n,o,i)},v=function(e){f||m||(h(e.data.direction),f=!0)},g=function(a){var s=i?{scrollLeft:a.data.direction}:{scrollTop:a.data.direction};m=y(a)||b(a),t.stop().animate(s,"fast","linear",function(){m?(r._toggleScrollButtons(t,n,o,i),f=!0):e(a.currentTarget).trigger(ue)}),f=!1,a.stopPropagation(),a.preventDefault()};n.on(ue+F,{direction:p},v).on(O.eventMap.down+F,{direction:u},g),o.on(ue+F,{direction:c},v).on(O.eventMap.down+F,{direction:d},g),n.add(o).on(de+F,function(){t.stop(),f=!1,r._toggleScrollButtons(t,n,o,i)}),t.on(fe,function(e){var a,s,p,c;e.ctrlKey||e.shiftKey||e.altKey||(a=w(e.originalEvent),s=Math.abs(a)*l,p=(a>0?"+=":"-=")+s,c=i?{scrollLeft:p}:{scrollTop:p},r._closeChildPopups(t),t.finish().animate(c,"fast","linear",function(){r._toggleScrollButtons(t,n,o,i)}),e.preventDefault())})},_toggleScrollButtons:function(e,t,n,o){var i=o?e.scrollLeft():e.scrollTop(),r=o?he:ve,a=o?ge:_e;t.toggle(0!==i),n.toggle(i<e[0][r]-e[0][a]-1)},setOptions:function(e){var t=this.options.animation;this._animations(e),e.animation=M(!0,t,e.animation),"dataSource"in e&&this._dataSource(e),this._updateClasses(),this._reinitOverflow(e),z.fn.setOptions.call(this,e)},destroy:function(){var e=this;z.fn.destroy.call(e),e._detachMenuEventsHandlers(),e._destroyOverflow(),O.destroy(e.element)},enable:function(e,t){return this._toggleDisabled(e,t!==!1),this},disable:function(e){return this._toggleDisabled(e,!1),this},attemptGetItem:function(t){var n,o;return t=t||this.element,n=this.element.find(t),o=this._overflowWrapper(),n.length||t===this.element?n:o?o.find(t):e()},append:function(e,t){t=this.attemptGetItem(t);var n=this._insert(e,t,t.length?t.find("> .k-menu-group, > .k-animation-container > .k-menu-group"):null);return N(n.items,function(t){n.group.append(this),a(this),u(this,e[t]||e)}),a(t),l(n.group.find(".k-first, .k-last").add(n.items)),p(c(n.group)),this},insertBefore:function(e,t){t=this.attemptGetItem(t);var n=this._insert(e,t,t.parent());return N(n.items,function(n){t.before(this),a(this),l(this),u(this,e[n]||e)}),l(t),this},insertAfter:function(e,t){t=this.attemptGetItem(t);var n=this._insert(e,t,t.parent());return N(n.items,function(n){t.after(this),a(this),l(this),u(this,e[n]||e)}),l(t),this},_insert:function(t,n,o){var i,a,s,l,p=this;return n&&n.length||(o=p.element),s=e.isPlainObject(t)||t instanceof O.data.ObservableObject,l={firstLevel:o.hasClass(J),horizontal:o.hasClass(J+"-horizontal"),expanded:!0,length:o.children().length},n&&!o.length&&(o=e(p.renderGroup({group:l,options:p.options})).appendTo(n)),s||I(t)||t instanceof O.data.ObservableArray?i=e(e.map(s?[t]:t,function(t,n){return"string"==typeof t?e(t).get():e(p.renderItem({group:l,item:M(t,{index:n})})).get()})):(i="string"==typeof t&&"<"!=t.charAt(0)?p.element.find(t):e(t),a=i.find("> ul").addClass("k-menu-group").attr("role","menu"),i=i.filter("li"),i.add(a.find("> li")).each(function(){r(this)})),{items:i,group:o}},remove:function(e){var t,n,o,i,r;return e=this.attemptGetItem(e),t=this,n=e.parentsUntil(t.element,Ee),o=e.parent("ul:not(.k-menu)"),e.remove(),o&&!o.children(Ee).length&&(i=c(o),r=o.parent(Se),r.length?r.remove():o.remove(),p(i)),n.length&&(n=n.eq(0),a(n),l(n)),t},_openAfterLoad:function(e,t){var n=this;t.loaded()?(n.open(e),n._loading=!1):t.one(le,function(){e.find(Y).removeClass("k-i-loading"),n._loading&&(n.open(e),n._loading=!1)})},open:function(i){var r,a,s,l=this,p=l.options,c="horizontal"==p.orientation,u=p.direction,d=O.support.isRtl(l.wrapper),f=l._overflowWrapper();return i=(f||l.element).find(i),r=l.dataSource&&l.dataSource.getByUid(i.data("uid")),r&&r.hasChildren&&!r.loaded()&&!l._loading?(l._loading=!0,i.find(Y).addClass("k-i-loading"),r.load(),l._openAfterLoad(i,r),t):(/^(top|bottom|default)$/.test(u)&&(u=d?c?(u+" left").replace("default","bottom"):"left":c?(u+" right").replace("default","bottom"):"right"),a=">.k-popup:visible,>.k-animation-container>.k-popup:visible",s=function(){var t=e(this).data(be);t&&l.close(e(this).closest("li.k-item"),!0)},i.siblings().find(a).each(s),f&&i.find(a).each(s),l.options.openOnClick&&(l.clicked=!0),i.each(function(){var i=e(this);clearTimeout(i.data(Z)),i.data(Z,setTimeout(function(){var r,a,s,h,v,g,_,k,w,C,b=i.find(".k-menu-group:first:hidden");!b[0]&&f&&(a=l._getPopup(i),b=a&&a.element),b.is(":visible")||b[0]&&l._triggerEvent({item:i[0],type:j})===!1&&(!b.find(".k-menu-group")[0]&&b.children(".k-item").length>1?(s=e(window).height(),h=function(){b.css({maxHeight:s-(O._outerHeight(b)-b.height())-O.getShadows(b).bottom,overflow:"auto"})},O.support.browser.msie&&O.support.browser.version<=7?setTimeout(h,0):h()):b.css({maxHeight:"",overflow:""}),i.data(te,i.css(te)),v=l.nextItemZIndex++,i.css(te,v),l.options.scrollable&&i.parent().siblings(Le).css({zIndex:++v}),r=b.data(be),g=i.parent().hasClass(J),_=g&&c,k=o(u,g,d),w=p.animation.open.effects,C=w!==t?w:"slideIn:"+n(u,g),r?(r=b.data(be),r.options.origin=k.origin,r.options.position=k.position,r.options.animation.open.effects=C):r=b.kendoPopup({activate:function(){l._triggerEvent({item:this.wrapper.parent(),type:ne})},deactivate:function(e){l._closing=!1,e.sender.element.removeData("targetTransform").css({opacity:""}),l._triggerEvent({item:this.wrapper.parent(),type:oe})},origin:k.origin,position:k.position,collision:p.popupCollision!==t?p.popupCollision:_?"fit":"fit flip",anchor:i,appendTo:f||i,animation:{open:M(!0,{effects:C},p.animation.open),close:p.animation.close},open:B(l._popupOpen,l),close:function(e){var t,n;l._closing=!0,t=e.sender.wrapper.parent(),f&&(n=e.sender.element.data(ke),n&&(t=(f||l.element).find(m(n))),e.sender.wrapper.children(Le).hide()),l._triggerEvent({item:t[0],type:V})?e.preventDefault():(t.css(te,t.data(te)),t.removeData(te),l.options.scrollable&&t.parent().siblings(Le).css({zIndex:""}),(x||se||O.support.mouseAndTouchPresent)&&(t.removeClass(Pe),l._removeHoverItem()))}}).data(be),b.removeAttr("aria-hidden"),l._configurePopupOverflow(r,i),r._hovered=!0,r.open(),l._initPopupScrolling(r))},l.options.hoverDelay))}),l)},_configurePopupOverflow:function(e,t){var n,o=this;o.options.scrollable&&(o._wrapPopupElement(e),t.attr("data-groupparent")||(n=(new Date).getTime(),t.attr("data-groupparent",n),e.element.attr("data-group",n)))},_wrapPopupElement:function(e){e.element.parent().is(Se)||(e.wrapper=O.wrap(e.element,e.options.autosize).css({overflow:"hidden",display:"block",position:"absolute"}))},_initPopupScrolling:function(e,t,n){var o=this;o.options.scrollable&&e.element[0].scrollHeight>e.element[0].offsetHeight&&o._initPopupScrollButtons(e,t,n)},_initPopupScrollButtons:function(t,n,o){var i=this,r=t.wrapper.children(Le),a=i.options.animation,s=(a&&a.open&&a.open.duration||0)+A;setTimeout(function(){var a,s;r.length||(a=e(i.templates.scrollButton({direction:n?"left":"up"})),s=e(i.templates.scrollButton({direction:n?"right":"down"})),r=a.add(s).appendTo(t.wrapper),i._initScrolling(t.element,a,s,n),o||r.on(ue+F,function(){var n=i._overflowWrapper();e(v(t.element,n)).each(function(e,t){var o=n.find(m(t.data(ke)));i.close(o)})}).on(de+F,function(){setTimeout(function(){e.isEmptyObject(i._openedPopups)&&i._closeParentPopups(t.element)},A)})),i._toggleScrollButtons(t.element,r.first(),r.last(),n)},s)},_popupOpen:function(e){this._keyTriggered||e.sender.element.children("."+Oe).removeClass(Oe),this.options.scrollable&&this._setPopupHeight(e.sender)},_setPopupHeight:function(t,n){var o,i,r,a,s,l,p,c,u,d=t.element,f=d.add(d.parent(Se));f.height(d.hasClass(J)&&this._initialHeight||""),o=t._location(n),i=e(window).height(),r=o.height,a=n?0:Math.max(o.top,0),s=n?0:C(this._overflowWrapper()[0],"scrollTop"),l=window.innerHeight-i,p=i-O.getShadows(d).bottom+l,c=p+s>r+a,c||(u=Math.min(p,p-a+s),f.css({overflow:"hidden",height:u+"px"}))},close:function(t,n){var o,i,r=this,a=r._overflowWrapper(),s=a||r.element;return t=s.find(t),t.length||(t=s.find(">.k-item")),o=function(t){var n=!1;return e.isEmptyObject(r._openedPopups)?n:(e(v(t,a)).each(function(e,t){return n=!!r._openedPopups[""+t.data(ke)],!n}),n)},i=function(e){var t=e.data(we);return!a||!t||!r._openedPopups[""+t]},t.each(function(){var s=e(this);!n&&r._isRootItem(s)&&(r.clicked=!1),clearTimeout(s.data(Z)),s.data(Z,setTimeout(function(){var e=r._getPopup(s);if(e&&(i(s)||r._forceClose)){if(!r._forceClose&&o(e.element))return;e.close(),e.element.attr("aria-hidden",!0),a&&r._forceClose&&t.last().is(s[0])&&delete r._forceClose}},r.options.hoverDelay))}),r},_getPopup:function(e){var t,n,o=this,i=e.find(".k-menu-group:not(.k-list-container):not(.k-calendar-container):first:visible").data(be),r=o._overflowWrapper();return!i&&r&&(t=e.data(we),t&&(n=r.find(h(t)),i=n.data(be))),i},_toggleDisabled:function(t,n){this.element.find(t).each(function(){e(this).toggleClass(ye,n).toggleClass(Te,!n).attr("aria-disabled",!n)})},_toggleHover:function(t){var n=e(O.eventTarget(t)||t.target).closest(Ee),o=t.type==ue||W.indexOf(t.type)!==-1;n.siblings().removeClass(Pe),n.parents("li."+Te).length||n.toggleClass(Pe,o||"mousedown"==t.type||"pointerover"==t.type||t.type==ce),this._removeHoverItem()},_preventClose:function(){this.options.closeOnClick||(this._closurePrevented=!0)},_checkActiveElement:function(t){var n=this,o=e(t?t.currentTarget:this._hoverItem()),r=n._findRootParent(o)[0];this._closurePrevented||setTimeout(function(){document.hasFocus()&&(i(r,O._activeElement())||!t||i(r,t.currentTarget))||n.close(r)},0),this._closurePrevented=!1},_removeHoverItem:function(){var e=this._hoverItem();e&&e.hasClass(Oe)&&(e.removeClass(Oe),this._oldHoverItem=null)},_updateClasses:function(){var e,t=this.element,n=".k-menu-init div ul";t.removeClass("k-menu-horizontal k-menu-vertical"),t.addClass("k-widget k-reset k-header k-menu-init "+J).addClass(J+"-"+this.options.orientation),t.find("li > ul").filter(function(){return!O.support.matchesSelector.call(this,n)}).addClass("k-group k-menu-group").attr("role","menu").attr("aria-hidden",t.is(":visible")).parent("li").attr("aria-haspopup","true").end().find("li > div").addClass("k-content").attr("tabindex","-1"),e=t.find("> li,.k-menu-group > li"),t.removeClass("k-menu-init"),e.each(function(){r(this)})},_mouseenter:function(t){var n=this,o=e(t.currentTarget),r=n._itemHasChildren(o),a=o.data(we)||o.parent().data(ke),s=b(t);a&&(n._openedPopups[""+a]=!0),n._closing||t.delegateTarget!=o.parents(xe)[0]&&t.delegateTarget!=o.parents(".k-menu-scroll-wrapper,.k-popups-wrapper")[0]||(n._keyTriggered=!1,n.options.openOnClick.rootMenuItems&&n._isRootItem(o.closest(Ee))||n.options.openOnClick.subMenuItems&&!n._isRootItem(o.closest(Ee))||(!(n.options.openOnClick===!1||n.options.openOnClick.rootMenuItems===!1&&n._isRootItem(o.closest(Ee))||n.options.openOnClick.subMenuItems===!1&&!n._isRootItem(o.closest(Ee))||n.clicked)||x||s&&n._isRootItem(o.closest(Ee))||!i(t.currentTarget,t.relatedTarget)&&r&&n.open(o),(n.options.openOnClick===!0&&n.clicked||x)&&o.siblings().each(B(function(e,t){n.close(t,!0)},n))))},_mousedown:function(t){var n=this,o=e(t.currentTarget);(n.options.openOnClick.subMenuItems&&!n._isRootItem(o)||x)&&o.siblings().each(B(function(e,t){n.close(t,!0)},n))},_mouseleave:function(n){var o=this,r=e(n.currentTarget),a=r.data(we),s=r.children(Se).length||r.children(Ie).length||a,l=e(window);return a&&delete o._openedPopups[""+a],r.parentsUntil(Se,".k-list-container,.k-calendar-container")[0]?(n.stopImmediatePropagation(),t):o.options.openOnClick!==!1&&(o.options.openOnClick.rootMenuItems||!o._isRootItem(r))&&(o.options.openOnClick.subMenuItems||o._isRootItem(r))||x||b(n)||i(n.currentTarget,n.relatedTarget||n.target)||!s||i(n.currentTarget,O._activeElement())?((O.support.browser.msie&&!n.toElement&&!n.relatedTarget&&!b(n)||n.clientX<0||n.clientY<0||n.clientY>l.height()||n.clientX>l.width())&&o.close(r),t):(o.close(r,!0),o._loading=!1,t)},_mouseenterPopup:function(t){var n,o=this,i=e(t.currentTarget);i.parent().is(Se)||(i=i.children("ul"),n=i.data(ke),n&&(o._openedPopups[""+n]=!0))},_mouseleavePopup:function(t){var n=this,o=e(t.currentTarget);!b(t)&&o.is(Se)&&n._closePopups(o.children("ul"))},_closePopups:function(t){var n,o=this,i=o._overflowWrapper(),r=t.data(ke);r&&(delete o._openedPopups[""+r],n=i.find(m(r)),setTimeout(function(){if(o.options.openOnClick)o._closeChildPopups(t);else if(e.isEmptyObject(o._openedPopups)){var i=o._innerPopup(t);o._closeParentPopups(i)}else o.close(n,!0)},0))},_closeChildPopups:function(t){var n=this,o=n._overflowWrapper();e(v(t,o)).each(function(){var e=o.find(m(this.data(ke)));n.close(e,!0)})},_innerPopup:function(e){var t=this._overflowWrapper(),n=v(e,t);return n[n.length-1]||e},_closeParentPopups:function(e){var t=this,n=t._overflowWrapper(),o=e.data(ke),i=n.find(m(o));for(o=i.parent().data(ke),t.close(i,!0);o&&!t._openedPopups[o]&&!i.parent().is(xe);)i=n.find(m(o)),t.close(i,!0),o=i.parent().data(ke)},_click:function(n){for(var o,i,r,a,s,l,p=this,c=p.options,u=e(O.eventTarget(n)),d=u[0],f=u[0]?u[0].nodeName.toUpperCase():"",m="INPUT"==f||"SELECT"==f||"BUTTON"==f||"LABEL"==f,v=u.closest(X),g=u.closest(Ee),_=g[0],k=v.attr("href"),w=u.attr("href"),C=e("<a href='#' />").attr("href"),y=!!k&&k!==C,P=y&&!!k.match(/^#/),T=!!w&&w!==C,H=p._overflowWrapper();d&&d.parentNode!=_;)d=d.parentNode;if(!e(d).is(ze)){if(g.hasClass(Te))return n.preventDefault(),t;if(n.handled||!p._triggerSelect(u,_)||m||n.preventDefault(),n.handled=!0,i=g.children(We),H&&(s=g.data(we),s&&(i=H.find(h(s)))),r=i.is(":visible"),a=c.openOnClick&&r&&p._isRootItem(g),c.closeOnClick&&(!y||P)&&(!i.length||a))return g.removeClass(Pe).css("height"),p._oldHoverItem=p._findRootParent(g),l=p._parentsUntil(v,p.element,Ee),p._forceClose=!!H,p.close(l),p.clicked=!1,"MSPointerUp".indexOf(n.type)!=-1&&n.preventDefault(),t;y&&n.enterKey&&v[0].click(),(p._isRootItem(g)&&c.openOnClick!==!1||c.openOnClick.subMenuItems||O.support.touch||b(n)&&p._isRootItem(g.closest(Ee)))&&(y||m||T||n.preventDefault(),p.clicked=!0,o=i.is(":visible")?V:j,(c.closeOnClick||o!=V)&&p[o](g))}},_parentsUntil:function(n,o,i){var r,a,s=this._overflowWrapper();return s?(r=k(n,s),a=[],e(r).each(function(){var n=e(this);return!n.is(o)&&(n.is(i)&&a.push(this),t)}),e(a)):n.parentsUntil(o,i)},_triggerSelect:function(e,t){var n,o,i,r;return e=e.is(".k-link")?e:e.closest(".k-link"),n=e.data("selectHandler"),n&&(o=this._getEventData(e),n.call(this,o)),i=o&&o.isDefaultPrevented(),r=this._triggerEvent({item:t,type:ee}),i||r},_getEventData:function(e){var t={sender:this,target:e,_defaultPrevented:!1,preventDefault:function(){this._defaultPrevented=!0},isDefaultPrevented:function(){return this._defaultPrevented}};return t},_documentClick:function(e){var t=this;i((t._overflowWrapper()||t.element)[0],e.target)||(t.clicked=!1)},_focus:function(n){var o=this,i=n.target,r=o._hoverItem(),a=H();return i==o.wrapper[0]||e(i).is(":kendoFocusable")?(a===n.currentTarget&&(r.length?o._moveHover([],r):o._oldHoverItem||o._moveHover([],o.wrapper.children().first())),t):(n.stopPropagation(),e(i).closest(".k-content").closest(".k-menu-group").closest(".k-item").addClass(Oe),o.wrapper.focus(),t)},_keydown:function(e){var n,o,i,r=this,a=e.keyCode,s=r._oldHoverItem,l=O.support.isRtl(r.wrapper);if(e.target==e.currentTarget||a==U.ESC){if(s||(s=r._oldHoverItem=r._hoverItem()),o=r._itemBelongsToVertival(s),i=r._itemHasChildren(s),r._keyTriggered=!0,a==U.RIGHT)n=r[l?"_itemLeft":"_itemRight"](s,o,i);else if(a==U.LEFT)n=r[l?"_itemRight":"_itemLeft"](s,o,i);else if(a==U.DOWN)n=r._itemDown(s,o,i);else if(a==U.UP)n=r._itemUp(s,o,i);else if(a==U.HOME)r._moveHover(s,s.parent().children().first()),e.preventDefault();else if(a==U.END)r._moveHover(s,s.parent().children().last()),e.preventDefault();else if(a==U.ESC)n=r._itemEsc(s,o);else if(a==U.ENTER||a==U.SPACEBAR)n=s.children(".k-link"),n.length>0&&(r._click({target:n[0],preventDefault:function(){},enterKey:!0}),i&&!s.hasClass(Te)?(r.open(s),r._moveHover(s,r._childPopupElement(s).children().first())):r._moveHover(s,r._findRootParent(s)));else if(a==U.TAB)return n=r._findRootParent(s),r._moveHover(s,n),r._checkActiveElement(),t;n&&n[0]&&(e.preventDefault(),e.stopPropagation())}},_hoverItem:function(){return this.wrapper.find(".k-item.k-state-hover,.k-item.k-state-focused").filter(":visible")},_itemBelongsToVertival:function(e){var t=this.wrapper.hasClass("k-menu-vertical");return e.length?e.parent().hasClass("k-menu-group")||t:t},_itemHasChildren:function(e){return!!(e&&e.length&&e[0].nodeType)&&(e.children(".k-menu-group, div.k-animation-container").length>0||!!e.data(we)&&!!this._overflowWrapper().children(h(e.data(we))))},_moveHover:function(t,n){var o=this,i=o._ariaId;t.length&&n.length&&t.removeClass(Oe),n.length&&(n[0].id&&(i=n[0].id),n.addClass(Oe),o._oldHoverItem=n,i&&(o.element.removeAttr("aria-activedescendant"),e("#"+i).removeAttr("id"),n.attr("id",i),o.element.attr("aria-activedescendant",i)),o._scrollToItem(n))},_findRootParent:function(e){return this._isRootItem(e)?e:this._parentsUntil(e,xe,"li.k-item").last()},_isRootItem:function(e){return e.parent().hasClass(J)},_itemRight:function(e,t,n){var o,i,r,a,s=this;return t?n&&!e.hasClass(Te)?(s.open(e),o=s._childPopupElement(e).children().first()):"horizontal"==s.options.orientation&&(i=s._findRootParent(e),r=s._overflowWrapper(),r&&(a=_(i,r),s._closeChildPopups(a)),s.close(i),o=i.nextAll(Re)):(o=e.nextAll(Re),o.length||(o=e.prevAll(Ue)),s.close(e)),o&&!o.length?o=s.wrapper.children(".k-item").first():o||(o=[]),s._moveHover(e,o),o},_itemLeft:function(e,t){var n,o,i=this;return t?(n=e.parent().closest(".k-item"),o=i._overflowWrapper(),!n.length&&o&&(n=g(e.parent(),o)),i.close(n),i._isRootItem(n)&&"horizontal"==i.options.orientation&&(n=n.prevAll(Re))):(n=e.prevAll(Re),n.length||(n=e.nextAll(Ue)),i.close(e)),n.length||(n=i.wrapper.children(".k-item").last()),i._moveHover(e,n),n},_itemDown:function(e,t,n){var o,i=this;if(t)o=e.nextAll(Re);else{if(!n||e.hasClass(Te))return;i.open(e),o=i._childPopupElement(e).children().first()}return!o.length&&e.length?o=e.parent().children().first():e.length||(o=i.wrapper.children(".k-item").first()),i._moveHover(e,o),o},_itemUp:function(e,t){var n,o=this;if(t)return n=e.prevAll(Re),!n.length&&e.length?n=e.parent().children().last():e.length||(n=o.wrapper.children(".k-item").last()),o._moveHover(e,n),n},_scrollToItem:function(e){var t,n,o,i,r,a,s,l,p,c,u,d,f=this;f.options.scrollable&&e&&e.length&&(t=e.parent(),n=!!t.hasClass(J)&&"horizontal"==f.options.orientation,o=n?"scrollLeft":"scrollTop",i=n?O._outerWidth:O._outerHeight,r=t[o](),a=i(e),s=e[0][n?"offsetLeft":"offsetTop"],l=i(t),p=t.siblings(Le),c=p.length?i(p.first()):0,r+l<s+a+c?u=s+a-l+c:r>s-c&&(u=s-c),isNaN(u)||(d={},d[o]=u,t.finish().animate(d,"fast","linear",function(){f._toggleScrollButtons(t,p.first(),p.last(),n)})))},_itemEsc:function(e,t){var n,o=this;return t?(n=e.parent().closest(".k-item"),o.close(n),o._moveHover(e,n),n):e},_childPopupElement:function(e){var t=e.find(".k-menu-group"),n=this._overflowWrapper();return!t.length&&n&&(t=_(e,n)),t},_triggerEvent:function(e){var t=this;return t.trigger(e.type,{type:e.type,item:e.item})},_focusHandler:function(t){var n=this,o=e(O.eventTarget(t)).closest(Ee);o.hasClass(Te)||setTimeout(function(){n._moveHover([],o),o.children(".k-content")[0]&&o.parent().closest(".k-item").removeClass(Oe)},200)},_animations:function(e){e&&"animation"in e&&!e.animation&&(e.animation={open:{effects:{}},close:{hide:!0,effects:{}}})},_dataSource:function(e){var t=this,n=e?e.dataSource:t.options.dataSource;n&&(n=I(n)?{data:n}:n,t._unbindDataSource(),n.fields||(n.fields=[{field:"uid"},{field:"text"},{field:"url"},{field:"cssClass"},{field:"spriteCssClass"},{field:"imageUrl"},{field:"imageAttr"},{field:"attr"},{field:"contentAttr"},{field:"content"},{field:"encoded"},{field:"items"},{field:"select"}]),t.dataSource=S.create(n),t._bindDataSource(),t.dataSource.fetch())},_bindDataSource:function(){this._refreshHandler=B(this.refresh,this),this._errorHandler=B(this._error,this),this.dataSource.bind(le,this._refreshHandler),this.dataSource.bind(pe,this._errorHandler)},_unbindDataSource:function(){var e=this.dataSource;e&&(e.unbind(le,this._refreshHandler),e.unbind(pe,this._errorHandler))},_error:function(){},findByUid:function(e){var t=this._overflowWrapper()||this.element;return t.find("[data-uid="+e+"]")},refresh:function(t){var n=this,o=t.node,i=t.action,r=o?n.findByUid(o.uid):n.element,a=t.items,s=t.index,l=e.proxy(n._updateItem,n),p=e.proxy(n._removeItem,n);"add"==i?n._appendItems(a,s,r):"remove"==i?a.forEach(p):"itemchange"==i?a.forEach(l):"itemloaded"===i?n.append(t.items,r):this._initData(),this.trigger(je,{item:r,dataItem:o})},_appendItems:function(e,t,n){var o=this,i=n.find(De).eq(t);i.length?o.insertBefore(e,i):o.append(e,n)},_removeItem:function(e){var t=this,n=t.findByUid(e.uid);t.remove(n)},_updateItem:function(e){var t=this,n=t.findByUid(e.uid),o=n.next(),i=e.parentNode();t.remove(n),o.length?t.insertBefore(e,o):t.append(e,i&&t.findByUid(i.uid))},_accessors:function(){var e,t,n,o=this,i=o.options,r=o.element;for(e in Je)t=i[Je[e]],n=r.attr(O.attr(e+"-field")),!t&&n&&(t=n),t||(t=e),I(t)||(t=[t]),i[Je[e]]=t},_fieldAccessor:function(t){var n=this.options[Je[t]]||[],o=n.length,i="(function(item) {";return 0===o?i+="return item['"+t+"'];":(i+="var levels = ["+e.map(n,function(e){return"function(d){ return "+O.expr(e)+"}"}).join(",")+"];",i+="if(item.level){return levels[Math.min(item.level(), "+o+"-1)](item);}else",i+="{return levels["+o+"-1](item)}"),i+="})"},_templates:function(){var e=this,t=e.options,n=B(e._fieldAccessor,e);t.template&&typeof t.template==Ge?t.template=R(t.template):t.template||(t.template=R("# var text = "+n("text")+"(data.item); ## if (typeof data.item.encoded != 'undefined' && data.item.encoded === false) {##= text ## } else { ##: text ## } #")),e.templates={content:R("#var contentHtml = "+n("content")+"(item);#<div #= contentCssAttributes(item.toJSON ? item.toJSON() : item) # tabindex='-1'>#= contentHtml || '' #</div>"),
group:R("<ul class='#= groupCssClass(group) #'#= groupAttributes(group) # role='menu' aria-hidden='true'>#= renderItems(data) #</ul>"),itemWrapper:R("# var url = "+n("url")+"(item); ## var imageUrl = "+n("imageUrl")+"(item); ## var imgAttributes = "+n("imageAttr")+"(item);## var tag = url ? 'a' : 'span' #<#= tag # class='#= textClass(item) #' #if(url){#href='#= url #'#}#># if (imageUrl) { #<img #= imageCssAttributes(imgAttributes) #  alt='' src='#= imageUrl #' /># } ##= sprite(item) ##= data.menu.options.template(data) ##= arrow(data) #</#= tag #>"),item:R("#var contentHtml = "+n("content")+"(item);#<li class='#= wrapperCssClass(group, item) #' #= itemCssAttributes(item.toJSON ? item.toJSON() : item) # role='menuitem'  #=item.items ? \"aria-haspopup='true'\": \"\"##=item.enabled === false ? \"aria-disabled='true'\" : ''#"+O.attr("uid")+"='#= item.uid #' >#= itemWrapper(data) ##if (item.hasChildren || item.items) { ##= subGroup({ items: item.items, menu: menu, group: { expanded: item.expanded } }) ## } else if (item.content || item.contentUrl || contentHtml) { ##= renderContent(data) ## } #</li>"),scrollButton:R("<span class='k-button k-button-icon k-menu-scroll-button k-scroll-#= direction #' unselectable='on'><span class='k-icon k-i-arrow-60-#= direction #'></span></span>"),arrow:R("<span class='#= arrowClass(item, group) #'></span>"),sprite:R("# var spriteCssClass = "+n("spriteCssClass")+"(data); if(spriteCssClass) {#<span class='k-sprite #= spriteCssClass #'></span>#}#"),empty:R("")}},renderItem:function(e){var t,n,o=this;return e=M({menu:o,group:{}},e),t=o.templates.empty,n=e.item,o.templates.item(M(e,{sprite:o.templates.sprite,itemWrapper:o.templates.itemWrapper,renderContent:o.renderContent,arrow:n.items||n.content||n[o.options.dataContentField[0]]?o.templates.arrow:t,subGroup:o.renderGroup},Ke))},renderGroup:function(e){var t=this,n=t.templates||e.menu.templates;return n.group(M({renderItems:function(e){for(var t="",n=0,o=e.items,i=o?o.length:0,r=M({length:i},e.group);n<i;n++)t+=e.menu.renderItem(M(e,{group:r,item:M({index:n},o[n])}));return t}},e,Ke))},renderContent:function(e){return e.menu.templates.content(M(e,Ke))}}),Ye=Xe.extend({init:function(t,n){var o=this;Xe.fn.init.call(o,t,n),o._marker=O.guid().substring(0,8),o.target=e(o.options.target),o._popup(),o._wire()},_initOverflow:function(e){var t=this;e.scrollable&&!t._overflowWrapper()&&(t._openedPopups={},t._popupsWrapper=(t.element.parent().is(Se)?t.element.parent():t.element).wrap("<div class='k-popups-wrapper "+e.orientation+"'></div>").parent(),"horizontal"==t.options.orientation&&P(t.element),e.appendTo&&e.appendTo.append(t._popupsWrapper),t._initialHeight=t.element[0].style.height,t._initialWidth=t.element[0].style.width)},options:{name:"ContextMenu",filter:null,showOn:"contextmenu",orientation:"vertical",alignToAnchor:!1,target:"body"},events:[j,V,ne,oe,ee],setOptions:function(t){var n=this;Xe.fn.setOptions.call(n,t),n.target.off(n.showOn+F+n._marker,n._showProxy),n.userEvents&&n.userEvents.destroy(),n.target=e(n.options.target),t.orientation&&n.popup.wrapper[0]&&n.popup.element.unwrap(),n._wire(),Xe.fn.setOptions.call(this,t)},destroy:function(){var e=this;e.target.off(e.options.showOn+F+e._marker),Ce.off(O.support.mousedown+F+e._marker,e._closeProxy),e.userEvents&&e.userEvents.destroy(),Xe.fn.destroy.call(e)},open:function(n,o){var r,a,s=this;return n=e(n)[0],i(s.element[0],e(n)[0])||s._itemHasChildren(e(n))?Xe.fn.open.call(s,n):s._triggerEvent({item:s.element,type:j})===!1&&(s.popup.visible()&&s.options.filter&&(s.popup.close(!0),s.popup.element.kendoStop(!0)),o!==t?(r=s._overflowWrapper(),r&&(a=r.offset(),n-=a.left,o-=a.top),s.popup.wrapper.hide(),s._configurePopupScrolling(n,o),s.popup.open(n,o)):(s.popup.options.anchor=(n?n:s.popup.anchor)||s.target,s.popup.element.kendoStop(!0),s._configurePopupScrolling(),s.popup.open()),Ce.off(s.popup.downEvent,s.popup._mousedownProxy),Ce.on(O.support.mousedown+F+s._marker,s._closeProxy)),s},_configurePopupScrolling:function(e,n){var o=this,i=o.popup,r="horizontal"==o.options.orientation;o.options.scrollable&&(o._wrapPopupElement(i),i.element.parent().css({position:"",height:""}),i.element.css({visibility:"hidden",display:"",position:""}),r?o._setPopupWidth(i,isNaN(e)?t:{isFixed:!0,x:e,y:n}):o._setPopupHeight(i,isNaN(e)?t:{isFixed:!0,x:e,y:n}),i.element.css({visibility:"",display:"none",position:"absolute"}),o._initPopupScrollButtons(i,r,!0),i.element.siblings(Le).hide())},_setPopupWidth:function(t,n){var o,i,r,a,s,l,p,c,u=t.element,d=u.add(u.parent(Se));d.width(this._initialWidth||""),o=t._location(n),i=e(window).width(),r=o.width,a=Math.max(o.left,0),s=n?0:C(this._overflowWrapper()[0],"scrollLeft"),l=O.getShadows(u),p=i-l.left-l.right,c=p+s>r+a,c||d.css({overflow:"hidden",width:p-a+s+"px"})},close:function(){var t=this;i(t.element[0],e(arguments[0])[0])||t._itemHasChildren(arguments[0])?Xe.fn.close.call(t,arguments[0]):t.popup.visible()&&t._triggerEvent({item:t.element,type:V})===!1&&(t.popup.close(),Ce.off(O.support.mousedown+F+t._marker,t._closeProxy),t.unbind(ee,t._closeTimeoutProxy))},_showHandler:function(t){var n,o=t,r=this,a=r.options,s=e(O.support.mobileOS?o.target:o.currentTarget);t.event&&(o=t.event,o.pageX=t.x.location,o.pageY=t.y.location),i(r.element[0],t.relatedTarget||t.target)||(r._eventOrigin=o,o.preventDefault(),o.stopImmediatePropagation(),r.element.find("."+Oe).removeClass(Oe),(a.filter&&s.is(a.filter)||!a.filter)&&(a.alignToAnchor?(r.popup.options.anchor=o.currentTarget,r.open(o.currentTarget)):(r.popup.options.anchor=o.currentTarget,r._targetChild?(n=r.target.offset(),r.open(o.pageX-n.left,o.pageY-n.top)):r.open(o.pageX,o.pageY))))},_closeHandler:function(t){var n,o=this,r=e(t.relatedTarget||t.target),a=r.closest(o.target.selector)[0]==o.target[0],s=r.closest(De),l=o._itemHasChildren(s),p=o._overflowWrapper(),c=i(o.element[0],r[0])||p&&i(p[0],r[0]);o._eventOrigin=t,n=3!==t.which,o.popup.visible()&&(n&&a||!a)&&(o.options.closeOnClick&&!l&&c||!c)&&(c?(this.unbind(ee,this._closeTimeoutProxy),o.bind(ee,o._closeTimeoutProxy)):o.close())},_wire:function(){var e=this,t=e.options,n=e.target;e._showProxy=B(e._showHandler,e),e._closeProxy=B(e._closeHandler,e),e._closeTimeoutProxy=B(e.close,e),n[0]&&(O.support.mobileOS&&"contextmenu"==t.showOn?(e.userEvents=new O.UserEvents(n,{filter:t.filter,allowSelection:!1}),n.on(t.showOn+F+e._marker,!1),e.userEvents.bind("hold",e._showProxy)):t.filter?n.on(t.showOn+F+e._marker,t.filter,e._showProxy):n.on(t.showOn+F+e._marker,e._showProxy))},_triggerEvent:function(n){var o=this,i=e(o.popup.options.anchor)[0],r=o._eventOrigin;return o._eventOrigin=t,o.trigger(n.type,M({type:n.type,item:n.item||this.element[0],target:i},r?{event:r}:{}))},_popup:function(){var t=this,n=t._overflowWrapper();t._triggerProxy=B(t._triggerEvent,t),t.popup=t.element.addClass("k-context-menu").kendoPopup({autosize:"horizontal"===t.options.orientation,anchor:t.target||"body",copyAnchorStyles:t.options.copyAnchorStyles,collision:t.options.popupCollision||"fit",animation:t.options.animation,activate:t._triggerProxy,deactivate:t._triggerProxy,appendTo:n||t.options.appendTo,close:n?function(t){e(v(t.sender.element,n)).each(function(e,t){var n=t.data(be);n&&n.close(!0)})}:e.noop}).data(be),t._targetChild=i(t.target[0],t.popup.element[0])}});T.plugin(Xe),T.plugin(Ye)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.menu.min.js.map
