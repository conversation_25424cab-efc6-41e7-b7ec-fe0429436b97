/*  跳转到某页
 * obj 跳转到页数的div
 * str 要调去的是哪一类数据，‘staff’调去员工的数据
 */
function getListByShowPage(item , cur , json){
    switch( item ){
        case "generalComplaint": // 公司总览 - 投诉
            getList(cur);
            break;
        case "login":
            getAgencyList(cur);
            break;
        // <<<========================  调查管理  ========================>>>
        case "invest1" : //  问卷库 主列表
            json = JSON.parse(json) ;
            getList(json.status, cur ,json.month)
            break ;
        case "investSearch" : // 问卷库 主列表，搜索列表
            json = JSON.parse(json) ;
            getListSearch(json.keyword, cur , json.month)
            break ;
        case "investQR" : //  调查管理 主列表
            json = JSON.parse(json) ;
            getList(json.status, cur ,json.month)
            break ;
        case "investQRSearch" : //  调查管理 搜索列表
            json = JSON.parse(json) ;
            getListBySearch(json.keyword ,cur )
            break ;
        case "completeSubject" : // 调查管理 查看已收回试卷
            json = JSON.parse(json) ;
            getCompleteSubjectList(json.id , cur, json.typeStr)
            break ;
        case "getgetQAnsysListByType" : // 调查管理 查看已收回试卷
            json = JSON.parse(json) ;
            getgetQAnsysListByType(json.publish, json.type, cur)
            break ;
        case "getgetQAnsysListById" : // 调查管理  答案详情
            json = JSON.parse(json) ;
            getQAnsList(cur);
            break ;
        case "getYearAreaDetails" : // 调查管理  答案详情2
            json = JSON.parse(json) ;
            getYearAreaDetails(cur);
            break ;

        // <<<========================  权限管理  ========================>>>
        case "updateLog" : // 权限设置  -  修改记录
            getList(cur , 20 ) ;
            break ;
        // <<<========================  总务管理  ========================>>>
        case "capitalAsserts":   		// 总务管理 - 固定资产
            json = eval('('+ json +')');
            var useState = json["useState"];
            var rank = json["rank"];
            getCapitalAssertsList( cur, 15, useState, rank);
            break ;
        case "employee":   				// 总务管理 - 固定资产
            json = eval('('+ json +')');
            var isDuty = json["isDuty"];
            defineGetList( cur, 20, isDuty);
            break ;
        case 'records':					// 总务管理 - 登录记录 - 本日本月本年
            json = JSON.parse(json) ;
            var flag = json["flag"];
            var sort = json["sort"];
            getLoginRecordList(cur,20,flag,sort);
            break;
        case 'definedRecords':			// 总务管理 - 登录记录 - 自定义查询
            json = JSON.parse(json) ;
            var type00 = json["type"];
            var beginTime = json["beginTime"];
            var endTime = json["endTime"];
            getDefinedLoginRecordList(cur, 20, type00, beginTime, endTime);
            break;
        case 'recordsDetail':			// 总务管理 - 登录记录 - 本日本月本年（登录查询）
            seeLoginRecordListDetail(cur,20,json);
            break;
        case 'definedRecordsDetail':	// 总务管理 - 登录记录 - 自定义查询（登录查询）
            seeDefinedLoginRecordListDetail(cur,20,json);
            break;
        case 'recruit':	// 总务管理 - 招聘管理
            json = JSON.parse(json) ;
            var offerStatus = json["offerStatus"];
            var timeType = json["timeType"];
            getOfferList(cur,20,offerStatus,timeType) ;
            break;
        case 'businessNameEditLog':	// 总务管理 - 企业信息
            updateNameLog(cur,20) ;
            break;
        case 'updateSiteLog':	// 总务管理 - 企业信息
            updateSiteLog(cur,20) ;
            break;
        case 'opertionDayLog':	// 总务管理 - 跨机构管理
            orgsOptionDayHistories(cur,20) ;
            break;


        // <<<========================  企业需求调查  ========================>>>
        case 'surveyList':
            getList(cur);
            break;

        // <<<========================  财务管理  ========================>>>

        case "checkManage_2":  			// 财务管理 - 支票管理 - 现金支票
            cash( cur , 20 );
            break ;
        case "checkManage_1":  			// 财务管理 - 支票管理 - 转账支票
            transfer( cur , 20 ) ;
            break ;
        case "updateHistory":  			// 财务管理 - 修改记录
            json = eval('('+ json +')');
            var num = json.num;
            getList(cur, 10 , num);
            break ;
        case "financeUpdateHistory":   	// 财务管理 - 修改记录
            json = eval('('+ json +')');
            var type = json["type"];
            var pageNum = json["pageNum"];
            getList( type , cur , pageNum );
            break ;
        case 'expenseReceive': 			// 财务管理 - 报销受理
            getReceiveMes() ;
            break;
        case 'expenseReceiveCateroy': 	// 财务管理 - 类别设置
            getCateroyMes() ;
            break;

        // <<<========================  活跃指数  ========================>>>

        case 'activeIndexlist1':	// 活跃指数1 - 首页列表
            getactiveable(cur,20,json.keyword);
            break;
        case 'activeIndexlist2':	// 活跃指数2 - 首页列表
            getactfiltable(cur,20);
            break;
        case 'activeIndexlist3':	// 活跃指数3 - 首页列表
            gettactfiatble(cur,20,json.keyword);
            break;
        case 'currentPageNo1'://点击确定后活跃指数1列表
			makesure(cur,20,json.keyword);
            break;
        case 'currentPageNo2'://点击确定后活跃指数2列表
			makesure2(cur,20,json.keyword);
            break;
        case 'currentPageNo3'://点击确定后活跃指数3列表
			makesure3(cur,20,json.keyword);
            break;
        // <<<========================  销售管理  ========================>>>

        case 'serviceSetMealList': 		// 销售管理 - 服务套餐
            json = JSON.parse(json);
            var key = json["key"];
            getList(cur ,key) ;
            break;
        case 'serviceSetMealListStop': 		// 销售管理 - 服务套餐 停用的
            json = JSON.parse(json);
            var key = json["key"];
            getStopList(cur ,key) ;
            break;
        case 'orderProductionSet': 		// 销售管理 - 要货计划处理列表
            getOrdList(cur ,10) ;
            break;
        case 'orderManage': 			// 销售管理 - 订单管理-未完结订单
            getOrderList(cur, 10);
            break;
        case 'orderManage2': 			// 销售管理 - 订单管理-已终止订单
            getOrdEndList(cur, 10);
            break;
        case 'salesDivision': 			// 销售管理 - 订单管理-已终止订单
            getDivisionList(cur, 20);
            break;
        case 'commodity':   			// 销售管理 - 通用型商品
            json = eval('(' + json + ')');
            var key = json.param;
            var category = json.category;
            var categoryName = json.categoryName;
            getCommodityList(cur, 20, category, categoryName, key);
            break;
        case 'inSuspendTY':   			// 销售管理 - 通用型商品
            json = eval('(' + json + ')');
            var key = json.param;
            getSuspendList(cur, 20, key);
            break;
        case 'noIssued':   			// 销售管理 - 送达管理
            getListData(cur);
            break;
        case 'noSigned':   			// 销售管理 - 送达管理
            getSignList(cur);
            break;
        case 'signedRecord':   			// 销售管理 - 送达管理
            getRecordList(cur);
            break;
        case 'serviceProject':   			// 销售管理 - 服务项目
            json = eval('(' + json + ')');
            getServiceList(json.name, json.enabled, cur, 20);
            break;
        case 'serviceTemplateSet':   			// 销售管理 - 服务合同
            json = eval('(' + json + ')');
            getTemplate(cur, json.source);
            break;
        case 'serviceContract':   			// 销售管理 - 服务合同
            getContractList(cur);
            break;

        // <<<========================  生产管理  ========================>>>

        case 'productReview': 			// 生产管理 - 要货计划评审
            getList(cur , 10 ) ;
            break;
        case "productionScan":   		// 生产管理 - 要货计划查看
            json = eval('('+ json +')');
            var pageNum = json["pageNum"];
            getList(cur , 10) ;
            break ;
        case "mtStorkIndex":  			// 生产管理 - 入库申请
            json = eval('('+ json +')');
            var state = json.state;
            getStorageApplyList( cur,15, state);
            break;
        case "concessionApply":  		// 生产管理 - 让步申请
            json = eval('('+ json +')');
            var state = json.state;
            getConcessionApplyList( cur,15, state);
            break;

        // <<<========================  商品管理  ========================>>>

        case 'goodsprocess':   			// 商品管理 - 工序信息
            json = eval('('+ json +')');
            var innerSn = json.innerSn;
            getProcess(innerSn ,cur , 10 );
            break;

        case 'composition': 			// 商品管理 - 商品构成
            json = eval('('+ json +')');
            var innerSn = json.innerSn;
            getComposionList(cur , 10 , innerSn);
            break;
        case 'backpmessage': 			// 商品管理 - 包装信息
            getbpMessage(cur,10);
            break;
        case 'customerManage': 			// 商品管理 - 客户管理
            json = eval('('+ json +')');
            var keyWord = json.keyWord;
            getCustomerMes(cur , 20, keyWord) ;
            break;
        case 'suspendCustomer': 		// 商品管理 - 已暂停合作的客户
            getSuspendCustomer(cur , 20 ) ;
            break;

        // <<<========================  物料管理  ========================>>>

        case 'material': 				// 物料管理 - 物料信息
            json = eval('('+ json +')');
            var pid = json.pid;
            getMaterialByKindid( pid , cur , 10 );
            break;

        // <<<========================  项目管理  ========================>>>

        case "proList2":   	// 项目管理 - 综合管理、立案 （通用）
            getList2( cur ) ;
            break ;

        // <<<========================  资源中心  ========================>>>

        case "fileMessage":  			// 资源中心 - 文件管理
            json = eval('('+ json +')');
            var categoryId = json.categoryId;
            getFile( cur,20, categoryId);
            break;
        case "changeRecord":  			// 资源中心 - 文件管理--换版记录
            json = eval('('+ json +')');
            var fileId = json.fileId;
            getRecord( cur,20, fileId);
            break;
        case "issue":  					// 资源中心 - 发布管理
            getList( cur ) ;
            break;
        case "changeVersion":  			// 资源中心 - 换版管理
            json = eval('('+ json +')');
            var fileId = json.fileId;
            getList( cur ) ;
            break;
        case "generalFile":
            getGeneralFile(cur,20);
            break;
        case "advancedFindFile":
            advancedSearch_file(cur,20);
            break;
        case "findFolder":
            advancedSearch_folder(cur,20);
            break;
        case "setRightLog": // 权限修改记录查看
            getAclLogList(cur, 20);
            break;
        case "setRightLog_accurate": // 权限修改记录查看- 精确查找
            getAclLogList(cur, 20, true);
            break;
        case "sheetList":  			// 表格管理 - 列表
            json = eval('('+ json +')');
            getSheetList( cur, 20, json.type, json.name ) ;
            break;
        case "sheetHandleRecord":  			// 表格管理 - 操作记录
            getSheetHandleRecord( cur, 20) ;
            break;
        case "chooseRelate":  			// 在已关联的表格中选择（待关联、被废止）
            json = eval('('+ json +')');
            getSheetList( cur, 20, json.type, json.name) ;
            break;
        // <<<========================  公共信息  ========================>>>
        case "publicFile":  			// 信息  - 文件列表
            json = eval('('+ json +')');
            getFileList( cur, 20, json.categoryId) ;
            break;
        case "publicFileRecord":  			// 信息  - 文件换版记录列表
            getFileRecord( cur, 20) ;
            break;
        // <<<========================  质量管理  ========================>>>

        case "storageTest":  			// 质量管理 - 入库检验
            json = eval('('+ json +')');
            var state = json.state;
            getStorageTestList( cur,15, state);
            break;

        // <<<========================  技术管理  ========================>>>

        case "concessionAccept":  		// 技术管理 - 让步受理
            json = eval('('+ json +')');
            var state = json.state;
            getConcessionAcceptList( cur,15, state);
            break;
        case 'InProduct':   			// 技术管理 - 产品档案
            json = eval('(' + json + ')');
            json.currPage = cur;
            getPdBaseListData(json);
            break;
        case 'InSuspendPd':   			// 技术管理 - 产品档案
            json = eval('(' + json + ')');
            json.currPage = cur;
            getSuspendPdList(json);
            break;
        case 'InProductPart':   			// 技术管理 - 零组件档案
            json = eval('(' + json + ')');
            json.currPage = cur;
            getPartsList(json);
            break;
        case 'partsTemporary':   			// 技术管理 - 零组件档案-暂存
            json = eval('(' + json + ')');
            json.currPage = cur;
            getTempPartsList(json);
            break;
        case 'InSuspendPt':   			// 技术管理 - 零组件档案-停产
            json = eval('(' + json + ')');
            json.currPage = cur;
            getSuspendPtList(json);
            break;
        case 'waitRelative':   			// 技术管理 - 有待关联的商品
            getWaitRelativeProList(cur, 20);
            break;
        case 'relatived':   			// 技术管理 - 已关联的商品
            json = eval('(' + json + ')');
            var key = json.param;
            getRelativedProList(cur, 20, key);
            break;
        case 'byProductSearch':   			// 技术管理 - 关联管理 - 按产品展示关联情况
            json = eval('(' + json + ')');
            var type = json.type;
            getListByProduct(type, cur);
            break;
        case 'waiteRl':   			// 技术管理 - 去选择产品
            json = eval('(' + json + ')');
            var key = json.param;
            getWaitProductList(cur, 20, key);
            break;
        case 'techMt':   			// 技术管理-材料 - 去选择产品
            json = eval('(' + json + ')');
            var key = json.keyword;
            getList(cur, key)
            break;
        case 'techMtBase':   			// 技术管理-材料  - 去试试
            getMtBaseList(cur)
            break;
        case 'techPd':   			// 技术管理-材料  - 获取待选择产品列表
            getPdMtBaseList(cur)
            break;
        case 'BindingPdList':   			// 技术管理-材料  - 根据材料获取已绑产品列表
            getBindingPdList(cur)
            break;
        case 'StopMtBaseList':   			// 技术管理-材料  -获取获取已停用材料列表
            getStopMtBaseList(cur)
            break;
        case 'BeforeBindingPdList':   			// 技术管理-材料  - 根据已停用材料获取曾经绑定的产品
            getBeforeBindingPdList(cur)
            break;
        case 'MtListByPdBase':   			// 技术管理-材料  - 获取材料变更记录
            getMtListByPdBase(cur)
            break;

        // <<<========================  仓库管理 - 成品库 ========================>>>

        case "accept":  				// 仓库管理 - 入库受理
            json = eval('('+ json +')');
            jumpTo(json, cur,20);
            break;

        case 'goodsDelivery':
            json = JSON.parse(json);
            var state = json['state'];
            getDeliveryList(cur, 20, state);
            break
        // <<<========================  采购 ========================>>>

        case "materialEntery":  	  			// 材料录入
            json = JSON.parse(json);
            var category = json['category'];
            var state = json['state'];
            var keyword = json['keyword'];
            getListByPage(category, state, keyword, cur, 20);
            break;

        case "materialManage":  	  			// 材料管理
            json = JSON.parse(json);
            var category = json['category'];
            var state = json['state'];
            var keyword = json['keyword'];
            getListByPage(category, state, keyword, cur, 20);
            break;
        case "orderManagePur":  	  			// 采购订单
            json = JSON.parse(json);
            var keyword = json['keyword'];
            getPurOrdersList(keyword, cur);
            break;


        // <<<========================  仓库查询 - 原辅材料库查询 ========================>>>
        case "rawSearch":
            json = JSON.parse(json);
            getMainList(cur);
            break;
        case "acceptStorageSearch":
            json = JSON.parse(json);
            getList(cur, json.param);
            break;
        // <<<========================  仓库管理 - 原辅材料库  ========================>>>

        case "resetLocationList":  	  			// 重新选择库位
            // json = eval('('+ json +')');
            // var flag = json.param;
            // jumpTo( flag, cur,20);
            resetLocationList(cur, 20);

        case "getPdByCategorySn":  	  			// 按类别查看原辅材料
            json = JSON.parse(json);
            var category = json['category'];
            var state = json['state'];
            var keyword = json['keyword'];
            getPdByCategoryByPage(category, state, keyword, cur, 20);
            break;
        case "keywordSearch":  	  			// 搜索
            json = JSON.parse(json);
            var keyword = json['keyword'];
            keywordSearch(keyword,cur,20);
            break;

        case "getWaitingForLocationList":  	  			// 待录入初始库存列表
            getWaitingForLocationList(cur,20)  ;
            break;

        case "pickOrInList":  	  			// 出入库记录
            json = JSON.parse(json);
            var index = json['index'];
            getLogList(cur,20,index);
            break;

        case "stockChangeRecord":  	  			// 库位变动记录
            json = JSON.parse(json);
            json.pageNum = cur
            stockChangeRecord(dataInfo)(json);
            break;

        // <<<========================  物流管理  ========================>>>
        case "outStorage":
            json = JSON.parse(json);
            var state = json['state'];
            getDeliveryList(cur, 20, state)
            break;
        case "signature":
            getSignedList(cur, 20)
            break;
        case "unSignature":
            getUnsignedList(cur, 20)
            break;
        // <<<========================  培训管理  ========================>>>
        case "questionBankList":  	// 试题库列表
            var jsonObj = JSON.parse(json);
            getList(cur, 20, jsonObj);
            break;
        case "questionBankSourceList":  	// 素材列表
            var jsonObj = JSON.parse(json);
            getSourceList(cur, 20, jsonObj);
            break;
        case "examList":
            var jsonObj = JSON.parse(json);
            getList(cur,jsonObj);
            break;
        case "finishedExamList":
            var jsonObj = JSON.parse(json);
            getFinishList(cur,jsonObj);
            break;

        // <<<========================  持续改进  ========================>>>

        case "improve":					// 持续改进 - 结案通过
            json = eval('('+ json +')');
            var buttonType = json.buttonType;
            getApplicationList( cur , 20, buttonType );
            break ;

        // <<<========================  个人中心  ========================>>>

        case "myApply":					// 个人中心 - 报销申请
            json = eval('('+ json +')');
            var num = json.num;
            getApplicationList( num , cur , 15);
            break ;
        case "myApply_leave":			// 个人中心 - 请求处理 - 请假请求
            json = JSON.parse(json) ;
            var type = json["type"];
            getApplyList( type , cur , 15 ) ;
            break ;
        case "myApply_overtime":		// 个人中心 - 请求处理 - 请假请求
            json = JSON.parse(json) ;
            var type = json["type"];
            getApplyList( type , cur , 15 ) ;
            break ;
        case "chargeApply_leave":		// 个人中心 - 处理请求 - 处理请假请求
            json = JSON.parse(json) ;
            var type = json["type"];
            getList( type , cur , 15 ) ;
            break ;
        case "chargeApply_overtime":	// 个人中心 - 处理请求 - 处理加班请求
            json = JSON.parse(json) ;
            var type = json["type"];
            getList( type , cur , 15 ) ;
            break ;
        case "chargeSubmitSale": 		// 个人中心 - 报销请求处理
            json = eval('('+ json +')');
            var num = json.num;
            pending( num , cur , 15);
            break ;
        case "personInfoRecord": 		// 个人信息 - 基本信息修改记录
            json = JSON.parse(json) ;
            var name = json.name;
            var id = json.id;
            personUpdateRecord(name, id, cur, 10);
            break ;
        case "employeeRecord": 		// 个人信息 - 教育经历、工作经历修改记录
            json = JSON.parse(json) ;
            var name = json.name;
            var id = json.id;
            employeeBaseRecord(name, id, cur, 10);
            break ;
        case "handleRecord": 		// 档案管理修改记录
            handleRecord(cur, 10);
            break ;
        // <<<========================  高管管理  ========================>>>
        case "manageRecord": 		// 个人信息 - 教育经历、工作经历修改记录
            json = JSON.parse(json) ;
            var obj = json.obj;
            var flag = json.flag;
            updateRecord(obj, flag, cur, 10);
            break ;
        // <<<========================  消息管理  ========================>>>

        case 'message':					// 消息管理
            json = JSON.parse(json) ;
            var num = json["num"];
            setMessageList(cur,20,num);
            break;

        // <<<========================  讨论区  ========================>>>
        case 'discussion': // 讨论区主题的审批
            json = JSON.parse(json);
            var approveStatus = json["approveStatus"];
            getForumMethod(approveStatus, cur, 20);
            break;
        case 'discussionIndex': // 讨论区主页
            json = JSON.parse(json);
            var title = json["title"];
            var enabled = json["enabled"];
            getPostsList(cur, 20, title, enabled);
            break;
        case 'discuss': // 意见列表
            json = JSON.parse(json);
            var title = json["title"];
            getPostsList(cur, 9, title);
            break;
        case 'reportOption': // 意见列表
            getPostMessage(cur,20);
            break;
        case 'searchReportOption': // 搜索的意见列表
            json = JSON.parse(json);
            var sta = json["sta"];
            search(sta, cur);
            break;
        case 'archiveFile': // 附件归档获取图片或者非图片
            json = JSON.parse(json);
            var attType = json["attType"];
            getFileList(cur, 20, attType);
            break;
        case 'bubbleInHistoryRecord': // 讨论区内部文件换版记录
            getBubbleChangeVersionRecordSysIn(cur, 20)
            break
        case 'bubbleOutHistoryRecord': // 讨论区外部文件换版记录
            getBubbleChangeVersionRecordSysOut(cur, 20)
            break
        // <<<========================  考勤管理  ========================>>>
        case 'updateAttendant':
            getAttendanceDetail(cur,20);
            break;
        case 'attendanceCount':
            getAttendanceCount(cur,20);
            break;
        case 'attendantInput':
            getAttendentList(cur,20);
            break;
        case 'clockRecord':
            getClockRecord(cur);
            break;
        // <<<========================  工作记录  ========================>>>
        case 'attendanceDays':
            getAttendanceDays(cur,20);
            break;
        // <<<========================  常规借款  ========================>>>
        case 'ordLoan':
            json = JSON.parse(json) ;
            var state = json["state"];
            var index = json["index"];
            getList(state, cur, 15, index);
            break;
        case 'advancePaymentPayLoan':
            json = JSON.parse(json) ;
            var state = json["state"];
            var index = json["index"];
            var yearMonth = json["time"];
            advancePaymentPayLoan(state, yearMonth, cur, index)
            break;
        case 'getMostDebitLoan':
            json = JSON.parse(json) ;
            var state = json["state"];
            var index = json["index"];
            var yearMonth = json["time"];
            getMostDebitLoan(state, yearMonth, cur,index)
            break;
        // <<<========================  备忘日程  ========================>>>
        case 'repeatSchedule':
            getMainEventList(cur,20);
            break;
        case 'eventMemo':
            getMemoMattersList(cur,20);
            break;
        case 'eventHistory':
            getHistoryList(cur,20);
            break;
        case 'keyWord':
            getKeyWordList(cur,20);
            break;
        case 'memoKeyWord':
            json = JSON.parse(json) ;
            getMemoKeyWordList(cur,20, json.keyword);
            break;
        case 'hisKeyWord':
            getHisKeyWordList(cur,20);
            break;
        case 'editRecord':
            json = JSON.parse(json) ;
            updateRecord(json.id,cur, 10, json.type)
            break
        case 'share':
            json = JSON.parse(json) ;
            getShareList(json.type, cur, 20);
            break
        case 'shareManage':
            getShareManage(cur, 20)
            break

        // <<<========================  供应商  ========================>>>
        case 'currentPageNo':
            json = JSON.parse(json);
            getContractMes(cur,20);
            break;
        // <<<========================  单位管理  ========================>>>
        case 'unitManage':
            getList(cur, 20);
            break ;
        // <<<========================  客户管理  ========================>>>
        case 'interviewList':
            getInterviewListData(cur, 20);
            break ;
        case 'outOfOrg': // 内容管理 - 我的审批
            getOutOfOrgList(cur, 20);
            break;
        // <<<========================  配方管理  ========================>>>
        case 'forulaList':
            json = JSON.parse(json) ;
            getForulaList(json.keyword, cur, 20);
            break ;
        case 'goSelectTry':
            getFormulaList( cur);
            break ;
        case 'stopForulaList':
            getStopForulaList( cur);
            break ;
        case 'fmBindingPdList':
            getFmBindingPdList( cur);
            break ;
        case 'fmUpdateRecord':
            getChangeRecord( cur);
            break ;
        case 'goHandle':
            getPdMtBaseList( cur);
            break ;
        case 'fmBeforeBindingPdList':
            getFmBeforeBindingPdList( cur);
            break ;
        case 'mtForFormula':
            json = JSON.parse(json) ;
            getMtListForFormula( cur,json.keyword);
            break ;
        case 'mtBdFormula':
            getBdFormulaList( cur);
            break ;
        case 'stopMtForFormula':
            getStopMtForFormula( cur);
            break ;
        case 'mtBeforeBdFormula':
            getBeforeBdFormulaList( cur);
            break ;
        // <<<========================  访谈记录  ========================>>>
        case 'getInterviewList':
            getList(cur , 20);
            break ;
        // <<<========================  配方管理  ========================>>>
        case 'replenishIndex':
            json = JSON.parse(json) ;
            getReplenishList(json.category, json.keyword, cur, 20);
            break ;
        // <<<========================  冻结账号  ========================>>>
        case 'frozenMain':
            getMainUserList(cur, 20);
            break ;
        case 'frozenRecord':
            getFrozenRecord(cur, 20);
            break ;
        // <<<========================  讨论组  ========================>>>
        case 'discussionBorrow':
            json = JSON.parse(json) ;
            getThemeList(cur, 20, json.title);
            break ;
        // <<<========================  关于  ========================>>>
        case 'txtList':
            json = JSON.parse(json) ;
            getTxtList(cur, 20, json.categoryId)
            break ;
            break ;
        // <<<========================  小程序  ========================>>>
        case 'minappLog':
            json = JSON.parse(json) ;
            getLogList(cur, json.fileID)
            break ;
        // <<<========================  用户展示  ========================>>>
        case 'authAcc':
            getAuthViewList(cur, 20)
            break ;
        // <<<========================  购销统筹  ========================>>>
        case 'planSlOrders':
            json = JSON.parse(json) ;
            getOrderList(json.num, json.obj, cur)
            break ;
        // <<<========================  会计管理  ========================>>>
        case 'subChangeContent':
            json = JSON.parse(json) ;
            getSubChangeRecord(cur, json.type)
            break ;
        // <<<========================  中枢管控  ========================>>>
        case 'branchNameEditLog':
            updateBranchNameLog(cur, 20)
            break ;
        case 'orgManage':
            getIndexInteraGency(cur, 20)
            break ;
        // <<<========================  装备器具  ========================>>>
        case 'equipmentList':
            json = JSON.parse(json) ;
            getEqList(cur,json.category, json.batchClassType)
            break ;
        default:
            break ;
        // <<<========================  工序管理  ========================>>>
        case 'processview':     //工序查看
            getprceview(cur,20)
            break;
        case 'overpaymentPayLoan':     //收款时多收来的款
            json = JSON.parse(json) ;
            getLoanOverpaymentList(json.state, json.time,cur,json.index)
            break;
        // <<<========================  初始化（采购）  ========================>>>
        case 'materialEntry':     //获取材料列表
            json = JSON.parse(json) ;
            getMtList(cur, 20, json.state, json.category)
            break;
        case 'supplierList':
            json = JSON.parse(json) ;
            getContractMes(cur, 20, json.keyword)
            break
        case 'pointedRAAMt':
            searchPointedRAAMt(cur, 20)
            break
        // <<<========================  初始化（原辅材料库）  ========================>>>
        case 'initialInventoryPending': //初始库存有待确定的原辅材料
            getWaitingForLocationList(cur, 20)
            break;
        case 'initialInventoryHandled': // 初始库存已确定的原辅材料
            getHandledLocationList(cur, 20,)
            break
    }
}

/* 
 *  分页的页数设置
 *  obj,分页插入的对象
 *  cur, 当前页数
 *  countall， 总页数
 */
function setPage(obj , cur , countall , Typestr , json ){
    var all = Number(countall);
    cur = Number(cur);
    if( all == 0){ all = 1 ; }
    if( cur == 0){ cur = 1 ; }
    var div_obj = obj;
    div_obj.html("");
    var str = "<div class='yeCon'>";
    if(all > 8){
        var star = cur - 3; // 开始页
        var n = 0;
        var end = parseInt(cur) + 3;
        if( star < 1 ){ star = 1; n = 3 - cur; end += n ;  }
        if( end > all ){ end = all ; }
        if( end - star < 5 ){ star = end - 5 ; }
        if( star < 1 ){ star = 1 ; }

        if( cur != 1 ){ str += "<div class='ye' onclick='showFirstPage($(this))'> 首页 </div>"; }
        if( star > 1 ){ str = str + "<span style='padding:5px; ' ></span>....<span style='padding:5px; ' ></span>"; }
        for (var i = star; i <= end; i++) {
            if( i == cur ){ str += "<div class='yecur'>"+i+"</div>"; }
            else {
                str += "<div class='ye' onclick='showPage($(this))'>" + i + "</div>";
            }
        }
        if( end < all ){ str = str + "<span style='padding:5px; ' ></span>....<span style='padding:5px; ' ></span>"; }
        if( cur != all ){ str += "<div class='ye' onclick='showLastPage($(this) ,"+ all +")'> 尾页 </div>"; }

    }else{
        for (var i = 1; i <= all; i++) {
            if(i==cur){
                str=str+"<div class='yecur'>"+i+"</div>";
            }else{
                str=str+"<div class='ye' onclick='showPage($(this))'>"+i+"</div>";
            }
        }
    }
    json = json || "{}";
    str=str+"<div class='yeC'>共"+all+"页</div>"+
        "<div class='yeC'>当前第"+cur+"页</div></div>"+
        "<div class='hd type'>"+ Typestr +"</div>"+
        "<div class='hd json'>"+ json +"</div>";
    div_obj.append(str);
}
/*  跳转到某页
 * obj 跳转到页数的div
 * str 要调去的是哪一类数据，‘staff’调去员工的数据
 */
function showPage(obj){
    var cur = obj.html();
    var item = obj.parent().siblings(".type").html();
    var jsonStr = obj.parent().siblings(".json").html();
    jsonStr = jsonStr || "{}";
    getListByShowPage(item, cur, jsonStr);
}
function showFirstPage(obj){
    var cur = 1;
    var item = obj.parent().siblings(".type").html();
    var json = obj.parent().siblings(".json").html();
    json = json || "{}";
    getListByShowPage(item , cur , json) ;
}
function showLastPage(obj , num){
    var cur = num;
    var item = obj.parent().siblings(".type").html();
    var json = obj.parent().siblings(".json").html();
    json = json || "{}";
    getListByShowPage(item , cur , json) ;
}
