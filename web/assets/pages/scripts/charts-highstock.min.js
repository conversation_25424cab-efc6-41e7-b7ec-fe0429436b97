jQuery(document).ready(function(){var t=[],e=0,a=["MSFT","AAPL","GOOG"],i=function(){$("#highstock_1").highcharts("StockChart",{chart:{style:{fontFamily:"Open Sans"}},rangeSelector:{selected:4},yAxis:{labels:{formatter:function(){return(this.value>0?" + ":"")+this.value+"%"}},plotLines:[{value:0,width:2,color:"silver"}]},plotOptions:{series:{compare:"percent"}},tooltip:{pointFormat:'<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b> ({point.change}%)<br/>',valueDecimals:2},series:t})};$.each(a,function(s,l){$.getJSON("http://www.highcharts.com/samples/data/jsonp.php?filename="+l.toLowerCase()+"-c.json&callback=?",function(o){t[s]={name:l,data:o},e+=1,e===a.length&&i()})}),$.getJSON("http://www.highcharts.com/samples/data/jsonp.php?filename=aapl-ohlcv.json&callback=?",function(t){var e=[],a=[],i=t.length,s=[["week",[1]],["month",[1,2,3,4,6]]],l=0;for(l;i>l;l+=1)e.push([t[l][0],t[l][1],t[l][2],t[l][3],t[l][4]]),a.push([t[l][0],t[l][5]]);$("#highstock_2").highcharts("StockChart",{chart:{style:{fontFamily:"Open Sans"}},rangeSelector:{selected:1},title:{text:"AAPL Historical"},yAxis:[{labels:{align:"right",x:-3},title:{text:"OHLC"},height:"60%",lineWidth:2},{labels:{align:"right",x:-3},title:{text:"Volume"},top:"65%",height:"35%",offset:0,lineWidth:2}],series:[{type:"candlestick",name:"AAPL",data:e,dataGrouping:{units:s}},{type:"column",name:"Volume",data:a,yAxis:1,dataGrouping:{units:s}}]})}),$.getJSON("http://www.highcharts.com/samples/data/jsonp.php?filename=aapl-ohlc.json&callback=?",function(t){$("#highstock_3").highcharts("StockChart",{chart:{style:{fontFamily:"Open Sans"}},rangeSelector:{selected:2},title:{text:"AAPL Stock Price"},series:[{type:"ohlc",name:"AAPL Stock Price",data:t,dataGrouping:{units:[["week",[1]],["month",[1,2,3,4,6]]]}}]})}),$.getJSON("http://www.highcharts.com/samples/data/jsonp.php?filename=usdeur.json&callback=?",function(t){var e=new Date(t[t.length-1][0]).getFullYear();$("#highstock_4").highcharts("StockChart",{chart:{style:{fontFamily:"Open Sans"}},rangeSelector:{selected:1},title:{text:"USD to EUR exchange rate"},yAxis:{title:{text:"Exchange rate"}},series:[{name:"USD to EUR",data:t,id:"dataseries",tooltip:{valueDecimals:4}},{type:"flags",data:[{x:Date.UTC(e,1,22),title:"A",text:'Shape: "squarepin"'},{x:Date.UTC(e,3,28),title:"A",text:'Shape: "squarepin"'}],onSeries:"dataseries",shape:"squarepin",width:16},{type:"flags",data:[{x:Date.UTC(e,2,1),title:"B",text:'Shape: "circlepin"'},{x:Date.UTC(e,3,1),title:"B",text:'Shape: "circlepin"'}],shape:"circlepin",width:16},{type:"flags",data:[{x:Date.UTC(e,2,10),title:"C",text:'Shape: "flag"'},{x:Date.UTC(e,3,11),title:"C",text:'Shape: "flag"'}],color:Highcharts.getOptions().colors[0],fillColor:Highcharts.getOptions().colors[0],onSeries:"dataseries",width:16,style:{color:"white"},states:{hover:{fillColor:"#395C84"}}}]})})});