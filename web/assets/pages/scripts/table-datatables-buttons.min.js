var TableDatatablesButtons = function () {
    var e = function () {
        var e = $("#sample_1");
        e.dataTable({
            language: {
                aria: {
                    sortAscending: ": activate to sort column ascending",
                    sortDescending: ": activate to sort column descending"
                },
                emptyTable: "No data available in table",
                info: "",
                infoEmpty: "无数据",
                infoFiltered: "(filtered1 from _MAX_ total entries)",
                lengthMenu: "_MENU_ ",
                search: "search:",
                zeroRecords: "未查出数据"
            },
            buttons: [  {
                extend: "excel",
                className: "btn yellow btn-outline "
            }, {extend: "csv", className: "btn purple btn-outline "}, {
                extend: "colvis",
                className: "btn dark btn-outline",
                text: "Columns"
            }],
            responsive: !0,
            order: [[0, "desc"]],
            lengthMenu: [[5, 10, 15, 20, -1], [5, 10, 15, 20, "全部"]],
            pageLength: 15,
            dom: "<'row' <'col-md-12'B>><'row'<'col-md-6 col-sm-12'l><'col-md-6 col-sm-12'f>r><'table-scrollable't><'row'<'col-md-5 col-sm-12'i><'col-md-7 col-sm-12'p>>"
        })
    }, t = function () {
        var e = $("#sample_2");
        e.dataTable({
            language: {
                aria: {
                    sortAscending: ": activate to sort column ascending",
                    sortDescending: ": activate to sort column descending"
                },
                emptyTable: "No data available in table",
                info: "",
                infoEmpty: "无数据",
                infoFiltered: "(filtered1 from _MAX_ total entries)",
                lengthMenu: "_MENU_ ",
                search: "search:",
                zeroRecords: "未查出数据"
            },
            buttons: [  {
                extend: "excel",
                className: "btn yellow btn-outline "
            }, {extend: "csv", className: "btn purple btn-outline "}, {
                extend: "colvis",
                className: "btn dark btn-outline",
                text: "Columns"
            }],
            order: [[0, "asc"]],
            lengthMenu: [[5, 10, 15, 20, -1], [5, 10, 15, 20, "All"]],
            pageLength: 10,
            dom: "<'row' <'col-md-12'B>><'row'<'col-md-6 col-sm-12'l><'col-md-6 col-sm-12'f>r><'table-scrollable't><'row'<'col-md-5 col-sm-12'i><'col-md-7 col-sm-12'p>>"
        })
    }, a = function () {
        var e = $("#sample_3"), t = e.dataTable({
            language: {
                aria: {
                    sortAscending: ": activate to sort column ascending",
                    sortDescending: ": activate to sort column descending"
                },
                emptyTable: "No data available in table",
                info: "",
                infoEmpty: "无数据",
                infoFiltered: "(filtered1 from _MAX_ total entries)",
                lengthMenu: "_MENU_ ",
                search: "search:",
                zeroRecords: "未查出数据"
            },
            buttons: [{extend: "print", className: "btn dark btn-outline"}, {
                extend: "copy",
                className: "btn red btn-outline"
            }, {extend: "pdf", className: "btn green btn-outline"}, {
                extend: "excel",
                className: "btn yellow btn-outline "
            }, {extend: "csv", className: "btn purple btn-outline "}, {
                extend: "colvis",
                className: "btn dark btn-outline",
                text: "Columns"
            }],
            responsive: !0,
            order: [[0, "asc"]],
            lengthMenu: [[5, 10, 15, 20, -1], [5, 10, 15, 20, "All"]],
            pageLength: 10
        });
        $("#sample_3_tools > li > a.tool-action").on("click", function () {
            var e = $(this).attr("data-action");
            t.DataTable().button(e).trigger()
        })
    }, n = function () {
        $(".date-picker").datepicker({rtl: App.isRTL(), autoclose: !0});
        var e = new Datatable;
        e.init({
            src: $("#datatable_ajax"),
            onSuccess: function (e, t) {
            },
            onError: function (e) {
            },
            onDataLoad: function (e) {
            },
            loadingMessage: "Loading...",
            dataTable: {
                bStateSave: !0,
                lengthMenu: [[10, 20, 50, 100, 150, -1], [10, 20, 50, 100, 150, "All"]],
                pageLength: 10,
                ajax: {url: "../demo/table_ajax.php"},
                order: [[1, "asc"]],
                buttons: [{
                    extend: "excel",
                    className: "btn default"
                }, {extend: "csv", className: "btn default"}, {
                    text: "Reload",
                    className: "btn default",
                    action: function (e, t, a, n) {
                        t.ajax.reload(), alert("Datatable reloaded!")
                    }
                }]
            }
        }), e.getTableWrapper().on("click", ".table-group-action-submit", function (t) {
            t.preventDefault();
            var a = $(".table-group-action-input", e.getTableWrapper());
            "" != a.val() && e.getSelectedRowsCount() > 0 ? (e.setAjaxParam("customActionType", "group_action"), e.setAjaxParam("customActionName", a.val()), e.setAjaxParam("id", e.getSelectedRows()), e.getDataTable().ajax.reload(), e.clearAjaxParams()) : "" == a.val() ? App.alert({
                type: "danger",
                icon: "warning",
                message: "Please select an action",
                container: e.getTableWrapper(),
                place: "prepend"
            }) : 0 === e.getSelectedRowsCount() && App.alert({
                type: "danger",
                icon: "warning",
                message: "No record selected",
                container: e.getTableWrapper(),
                place: "prepend"
            })
        }), $("#datatable_ajax_tools > li > a.tool-action").on("click", function () {
            var t = $(this).attr("data-action");
            e.getDataTable().button(t).trigger()
        })
    };
    return {
        init: function () {
            jQuery().dataTable && (e(), t(), a(), n())
        }
    }
}();
jQuery(document).ready(function () {
    TableDatatablesButtons.init()
});