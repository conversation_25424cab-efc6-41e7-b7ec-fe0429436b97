var BootstrapTable=function(){var e=function(){var e=$("#table-transform");$("#transform").click(function(){e.bootstrapTable()}),$("#destroy").click(function(){e.bootstrapTable("destroy")})},t=function(){$("#table-style");$("#hover, #striped, #condensed").click(function(){var e="table";$("#hover").prop("checked")&&(e+=" table-hover"),$("#condensed").prop("checked")&&(e+=" table-condensed"),$("#table-style").bootstrapTable("destroy").bootstrapTable({classes:e,striped:$("#striped").prop("checked")})})};return{init:function(){e(),t()}}}();jQuery(document).ready(function(){BootstrapTable.init()});