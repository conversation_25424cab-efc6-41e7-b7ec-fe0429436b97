var ChartsFlotcharts=function(){return{init:function(){App.addResizeHandler(function(){Charts.initPieCharts()})},initCharts:function(){function e(){for(s.length>0&&(s=s.slice(1));s.length<n;){var e=s.length>0?s[s.length-1]:50,t=e+10*Math.random()-5;0>t&&(t=0),t>100&&(t=100),s.push(t)}for(var i=[],r=0;r<s.length;++r)i.push([r,s[r]]);return i}function t(){if(1==$("#chart_1").size()){for(var e=[],t=0;t<2*Math.PI;t+=.25)e.push([t,Math.sin(t)]);for(var i=[],t=0;t<2*Math.PI;t+=.25)i.push([t,Math.cos(t)]);for(var r=[],t=0;t<2*Math.PI;t+=.1)r.push([t,Math.tan(t)]);$.plot($("#chart_1"),[{label:"sin(x)",data:e,lines:{lineWidth:1},shadowSize:0},{label:"cos(x)",data:i,lines:{lineWidth:1},shadowSize:0},{label:"tan(x)",data:r,lines:{lineWidth:1},shadowSize:0}],{series:{lines:{show:!0},points:{show:!0,fill:!0,radius:3,lineWidth:1}},xaxis:{tickColor:"#eee",ticks:[0,[Math.PI/2,"π/2"],[Math.PI,"π"],[3*Math.PI/2,"3π/2"],[2*Math.PI,"2π"]]},yaxis:{tickColor:"#eee",ticks:10,min:-2,max:2},grid:{borderColor:"#eee",borderWidth:1}})}}function i(){function e(){return Math.floor(21*Math.random())+20}function t(e,t,i){$('<div id="tooltip">'+i+"</div>").css({position:"absolute",display:"none",top:t+5,left:e+15,border:"1px solid #333",padding:"4px",color:"#fff","border-radius":"3px","background-color":"#333",opacity:.8}).appendTo("body").fadeIn(200)}if(1==$("#chart_2").size()){var i=[[1,e()],[2,e()],[3,2+e()],[4,3+e()],[5,5+e()],[6,10+e()],[7,15+e()],[8,20+e()],[9,25+e()],[10,30+e()],[11,35+e()],[12,25+e()],[13,15+e()],[14,20+e()],[15,45+e()],[16,50+e()],[17,65+e()],[18,70+e()],[19,85+e()],[20,80+e()],[21,75+e()],[22,80+e()],[23,75+e()],[24,70+e()],[25,65+e()],[26,75+e()],[27,80+e()],[28,85+e()],[29,90+e()],[30,95+e()]],r=[[1,e()-5],[2,e()-5],[3,e()-5],[4,6+e()],[5,5+e()],[6,20+e()],[7,25+e()],[8,36+e()],[9,26+e()],[10,38+e()],[11,39+e()],[12,50+e()],[13,51+e()],[14,12+e()],[15,13+e()],[16,14+e()],[17,15+e()],[18,15+e()],[19,16+e()],[20,17+e()],[21,18+e()],[22,19+e()],[23,20+e()],[24,21+e()],[25,14+e()],[26,24+e()],[27,25+e()],[28,26+e()],[29,27+e()],[30,31+e()]],o=($.plot($("#chart_2"),[{data:i,label:"Unique Visits",lines:{lineWidth:1},shadowSize:0},{data:r,label:"Page Views",lines:{lineWidth:1},shadowSize:0}],{series:{lines:{show:!0,lineWidth:2,fill:!0,fillColor:{colors:[{opacity:.05},{opacity:.01}]}},points:{show:!0,radius:3,lineWidth:1},shadowSize:2},grid:{hoverable:!0,clickable:!0,tickColor:"#eee",borderColor:"#eee",borderWidth:1},colors:["#d12610","#37b7f3","#52e136"],xaxis:{ticks:11,tickDecimals:0,tickColor:"#eee"},yaxis:{ticks:11,tickDecimals:0,tickColor:"#eee"}}),null);$("#chart_2").bind("plothover",function(e,i,r){if($("#x").text(i.x.toFixed(2)),$("#y").text(i.y.toFixed(2)),r){if(o!=r.dataIndex){o=r.dataIndex,$("#tooltip").remove();var a=r.datapoint[0].toFixed(2),s=r.datapoint[1].toFixed(2);t(r.pageX,r.pageY,r.series.label+" of "+a+" = "+s)}}else $("#tooltip").remove(),o=null})}}function r(){function e(){a=null;var e=s,t=plot.getAxes();if(!(e.x<t.xaxis.min||e.x>t.xaxis.max||e.y<t.yaxis.min||e.y>t.yaxis.max)){var i,r,n=plot.getData();for(i=0;i<n.length;++i){var l=n[i];for(r=0;r<l.data.length&&!(l.data[r][0]>e.x);++r);var h,d=l.data[r-1],c=l.data[r];h=null==d?c[1]:null==c?d[1]:d[1]+(c[1]-d[1])*(e.x-d[0])/(c[0]-d[0]),o.eq(i).text(l.label.replace(/=.*/,"= "+h.toFixed(2)))}}}if(1==$("#chart_3").size()){for(var t=[],i=[],r=0;14>r;r+=.1)t.push([r,Math.sin(r)]),i.push([r,Math.cos(r)]);plot=$.plot($("#chart_3"),[{data:t,label:"sin(x) = -0.00",lines:{lineWidth:1},shadowSize:0},{data:i,label:"cos(x) = -0.00",lines:{lineWidth:1},shadowSize:0}],{series:{lines:{show:!0}},crosshair:{mode:"x"},grid:{hoverable:!0,autoHighlight:!1,tickColor:"#eee",borderColor:"#eee",borderWidth:1},yaxis:{min:-1.2,max:1.2}});var o=$("#chart_3 .legendLabel");o.each(function(){$(this).css("width",$(this).width())});var a=null,s=null;$("#chart_3").bind("plothover",function(t,i,r){s=i,a||(a=setTimeout(e,50))})}}function o(){function t(){o.setData([e()]),o.draw(),setTimeout(t,r)}if(1==$("#chart_4").size()){var i={series:{shadowSize:1},lines:{show:!0,lineWidth:.5,fill:!0,fillColor:{colors:[{opacity:.1},{opacity:1}]}},yaxis:{min:0,max:100,tickColor:"#eee",tickFormatter:function(e){return e+"%"}},xaxis:{show:!1},colors:["#6ef146"],grid:{tickColor:"#eee",borderWidth:0}},r=30,o=$.plot($("#chart_4"),[e()],i);t()}}function a(){function e(){$.plot($("#chart_5"),[{label:"sales",data:t,lines:{lineWidth:1},shadowSize:0},{label:"tax",data:r,lines:{lineWidth:1},shadowSize:0},{label:"profit",data:o,lines:{lineWidth:1},shadowSize:0}],{series:{stack:a,lines:{show:n,fill:!0,steps:l,lineWidth:0},bars:{show:s,barWidth:.5,lineWidth:0,shadowSize:0,align:"center"}},grid:{tickColor:"#eee",borderColor:"#eee",borderWidth:1}})}if(1==$("#chart_5").size()){for(var t=[],i=0;10>=i;i+=1)t.push([i,parseInt(30*Math.random())]);for(var r=[],i=0;10>=i;i+=1)r.push([i,parseInt(30*Math.random())]);for(var o=[],i=0;10>=i;i+=1)o.push([i,parseInt(30*Math.random())]);var a=0,s=!0,n=!1,l=!1;$(".stackControls input").click(function(t){t.preventDefault(),a="With stacking"==$(this).val()?!0:null,e()}),$(".graphControls input").click(function(t){t.preventDefault(),s=-1!=$(this).val().indexOf("Bars"),n=-1!=$(this).val().indexOf("Lines"),l=-1!=$(this).val().indexOf("steps"),e()}),e()}}if(jQuery.plot){var s=[],n=250;t(),i(),r(),o(),a()}},initBarCharts:function(){function e(e){var t=[],r=100+e,o=200+e;for(i=1;i<=20;i++){var a=Math.floor(Math.random()*(o-r+1)+r);t.push([i,a]),r++,o++}return t}var t=e(0),r={series:{bars:{show:!0}},bars:{barWidth:.8,lineWidth:0,shadowSize:0,align:"left"},grid:{tickColor:"#eee",borderColor:"#eee",borderWidth:1}};0!==$("#chart_1_1").size()&&$.plot($("#chart_1_1"),[{data:t,lines:{lineWidth:1},shadowSize:0}],r);var o=[[10,10],[20,20],[30,30],[40,40],[50,50]],r={series:{bars:{show:!0}},bars:{horizontal:!0,barWidth:6,lineWidth:0,shadowSize:0,align:"left"},grid:{tickColor:"#eee",borderColor:"#eee",borderWidth:1}};0!==$("#chart_1_2").size()&&$.plot($("#chart_1_2"),[o],r)},initPieCharts:function(){function e(e,t,i){i&&(percent=parseFloat(i.series.percent).toFixed(2),$("#hover").html('<span style="font-weight: bold; color: '+i.series.color+'">'+i.series.label+" ("+percent+"%)</span>"))}function t(e,t,i){i&&(percent=parseFloat(i.series.percent).toFixed(2),alert(""+i.series.label+": "+percent+"%"))}var i=[],r=Math.floor(10*Math.random())+1;r=5>r?5:r;for(var o=0;r>o;o++)i[o]={label:"Series"+(o+1),data:Math.floor(100*Math.random())+1};0!==$("#pie_chart").size()&&$.plot($("#pie_chart"),i,{series:{pie:{show:!0}}}),0!==$("#pie_chart_1").size()&&$.plot($("#pie_chart_1"),i,{series:{pie:{show:!0}},legend:{show:!1}}),0!==$("#pie_chart_2").size()&&$.plot($("#pie_chart_2"),i,{series:{pie:{show:!0,radius:1,label:{show:!0,radius:1,formatter:function(e,t){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(t.percent)+"%</div>"},background:{opacity:.8}}}},legend:{show:!1}}),0!==$("#pie_chart_3").size()&&$.plot($("#pie_chart_3"),i,{series:{pie:{show:!0,radius:1,label:{show:!0,radius:.75,formatter:function(e,t){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(t.percent)+"%</div>"},background:{opacity:.5}}}},legend:{show:!1}}),0!==$("#pie_chart_4").size()&&$.plot($("#pie_chart_4"),i,{series:{pie:{show:!0,radius:1,label:{show:!0,radius:.75,formatter:function(e,t){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(t.percent)+"%</div>"},background:{opacity:.5,color:"#000"}}}},legend:{show:!1}}),0!==$("#pie_chart_5").size()&&$.plot($("#pie_chart_5"),i,{series:{pie:{show:!0,radius:.75,label:{show:!0,radius:.75,formatter:function(e,t){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(t.percent)+"%</div>"},background:{opacity:.5,color:"#000"}}}},legend:{show:!1}}),0!==$("#pie_chart_6").size()&&$.plot($("#pie_chart_6"),i,{series:{pie:{show:!0,radius:1,label:{show:!0,radius:2/3,formatter:function(e,t){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(t.percent)+"%</div>"},threshold:.1}}},legend:{show:!1}}),0!==$("#pie_chart_7").size()&&$.plot($("#pie_chart_7"),i,{series:{pie:{show:!0,combine:{color:"#999",threshold:.1}}},legend:{show:!1}}),0!==$("#pie_chart_8").size()&&$.plot($("#pie_chart_8"),i,{series:{pie:{show:!0,radius:300,label:{show:!0,formatter:function(e,t){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(t.percent)+"%</div>"},threshold:.1}}},legend:{show:!1}}),0!==$("#pie_chart_9").size()&&$.plot($("#pie_chart_9"),i,{series:{pie:{show:!0,radius:1,tilt:.5,label:{show:!0,radius:1,formatter:function(e,t){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(t.percent)+"%</div>"},background:{opacity:.8}},combine:{color:"#999",threshold:.1}}},legend:{show:!1}}),0!==$("#donut").size()&&$.plot($("#donut"),i,{series:{pie:{innerRadius:.5,show:!0}}}),0!==$("#interactive").size()&&($.plot($("#interactive"),i,{series:{pie:{show:!0}},grid:{hoverable:!0,clickable:!0}}),$("#interactive").bind("plothover",e),$("#interactive").bind("plotclick",t))}}}();jQuery(document).ready(function(){ChartsFlotcharts.init(),ChartsFlotcharts.initCharts(),ChartsFlotcharts.initPieCharts(),ChartsFlotcharts.initBarCharts()});