!function(e,l,i,t){"use strict";e("#js-grid-lightbox-gallery").cubeportfolio({filters:"#js-filters-lightbox-gallery1, #js-filters-lightbox-gallery2",loadMore:"#js-loadMore-lightbox-gallery",loadMoreAction:"click",layoutMode:"grid",mediaQueries:[{width:1500,cols:5},{width:1100,cols:4},{width:800,cols:3},{width:480,cols:2},{width:320,cols:1}],defaultFilter:"*",animationType:"rotateSides",gapHorizontal:10,gapVertical:10,gridAdjustment:"responsive",caption:"zoom",displayType:"sequentially",displayTypeSpeed:100,lightboxDelegate:".cbp-lightbox",lightboxGallery:!0,lightboxTitleSrc:"data-title",lightboxCounter:'<div class="cbp-popup-lightbox-counter">{{current}} of {{total}}</div>',singlePageInlineDelegate:".cbp-singlePageInline",singlePageInlinePosition:"below",singlePageInlineInFocus:!0,singlePageInlineCallback:function(l,i){var t=this;e.ajax({url:l,type:"GET",dataType:"html",timeout:1e4}).done(function(e){t.updateSinglePageInline(e)}).fail(function(){t.updateSinglePageInline("AJAX Error! Please refresh the page!")})}})}(jQuery,window,document);