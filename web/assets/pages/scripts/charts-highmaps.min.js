jQuery(document).ready(function(){$.getJSON("http://www.highcharts.com/samples/data/jsonp.php?filename=world-population.json&callback=?",function(t){var a=Highcharts.geojson(Highcharts.maps["custom/world"]);$.each(t,function(){"UK"===this.code&&(this.code="GB")}),$("#highmaps_1").highcharts("Map",{chart:{style:{fontFamily:"Open Sans"}},title:{text:"World population 2013 by country"},subtitle:{text:"Demo of Highcharts map with bubbles"},legend:{enabled:!1},mapNavigation:{enabled:!0,buttonOptions:{verticalAlign:"bottom"}},series:[{name:"Countries",mapData:a,color:"#E0E0E0",enableMouseTracking:!1},{type:"mapbubble",mapData:a,name:"Population 2013",joinBy:["iso-a2","code"],data:t,minSize:4,maxSize:"12%",tooltip:{pointFormat:"{point.code}: {point.z} thousands"}}]})}),$("#highmaps_2").highcharts({data:{csv:document.getElementById("csv").innerHTML},chart:{type:"heatmap",inverted:!0,style:{fontFamily:"Open Sans"}},title:{text:"Highcharts heat map",align:"left"},subtitle:{text:"Temperature variation by day and hour through May 2015",align:"left"},xAxis:{tickPixelInterval:50,min:Date.UTC(2015,4,1),max:Date.UTC(2015,4,30)},yAxis:{title:{text:null},labels:{format:"{value}:00"},minPadding:0,maxPadding:0,startOnTick:!1,endOnTick:!1,tickPositions:[0,6,12,18,24],tickWidth:1,min:0,max:23},colorAxis:{stops:[[0,"#3060cf"],[.5,"#fffbbc"],[.9,"#c4463a"]],min:-5},series:[{borderWidth:0,colsize:864e5,tooltip:{headerFormat:"Temperature<br/>",pointFormat:"{point.x:%e %b, %Y} {point.y}:00: <b>{point.value} ℃</b>"}}]}),$("#highmaps_3").highcharts("Map",{chart:{spacingBottom:20,style:{fontFamily:"Open Sans"}},title:{text:"Europe time zones"},legend:{enabled:!0},plotOptions:{map:{allAreas:!1,joinBy:["iso-a2","code"],dataLabels:{enabled:!0,color:"white",formatter:function(){return this.point.properties&&this.point.properties.labelrank.toString()<5?this.point.properties["iso-a2"]:void 0},format:null,style:{fontWeight:"bold"}},mapData:Highcharts.maps["custom/europe"],tooltip:{headerFormat:"",pointFormat:"{point.name}: <b>{series.name}</b>"}}},series:[{name:"UTC",data:$.map(["IE","IS","GB","PT"],function(t){return{code:t}})},{name:"UTC + 1",data:$.map(["NO","SE","DK","DE","NL","BE","LU","ES","FR","PL","CZ","AT","CH","LI","SK","HU","SI","IT","SM","HR","BA","YF","ME","AL","MK"],function(t){return{code:t}})},{name:"UTC + 2",data:$.map(["FI","EE","LV","LT","BY","UA","MD","RO","BG","GR","TR","CY"],function(t){return{code:t}})},{name:"UTC + 3",data:$.map(["RU"],function(t){return{code:t}})}]})});