var ComponentsContextMenu=function(){var n=function(){$("#main").contextmenu({target:"#context-menu2",before:function(n){return n.preventDefault(),"SPAN"==n.target.tagName?(n.preventDefault(),this.closemenu(),!1):!0}})},e=function(){$("#context2").contextmenu({target:"#context-menu2",onItem:function(n,e){alert($(e.target).text())}}),$("#context-menu2").on("show.bs.context",function(n){console.log("before show event")}),$("#context-menu2").on("shown.bs.context",function(n){console.log("after show event")}),$("#context-menu2").on("hide.bs.context",function(n){console.log("before hide event")}),$("#context-menu2").on("hidden.bs.context",function(n){console.log("after hide event")})};return{init:function(){n(),e()}}}();jQuery(document).ready(function(){ComponentsContextMenu.init()});