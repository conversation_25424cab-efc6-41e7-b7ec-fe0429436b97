var TableDatatablesEditable=function(){
    var e=function(){function e(e,t){
        for(var n=e.fnGetData(t),a=$(">td",t),l=0,r=a.length;r>l;l++)e.fnUpdate(n[l],t,l,!1);e.fnDraw()}
        function t(e,t){
            var n=e.fnGetData(t),a=$(">td",t);
            a[0].innerHTML='<input type="text" disabled name="createDate" class="form-control input-small" value="'+n[0]+'">',
            a[1].innerHTML='<input type="text" onclick="WdatePicker()" name="createDate" class="form-control input-small" value="'+n[1]+'">',
                a[2].innerHTML='<input type="text" name="credit" onblur="changeDis($(this).val(),1);" class="form-control input-small credit" value="'+n[2]+'">',
                a[3].innerHTML='<input type="text" name="debit" onblur="changeDis($(this).val(),2);" class="form-control input-small debit " value="'+n[3]+'">',
                a[4].innerHTML='<input type="text" disabled name="createName" class="form-control input-small" value="'+n[4]+'">',
                a[5].innerHTML='<input type="text" name="applyMemo" class="form-control input-small" value="'+n[5]+'">',
                a[6].innerHTML='<a class="edit" href="">Save</a>',
                a[7].innerHTML='<a class="cancel" href="">Cancel</a>'}function n(e,t){
            var n=$("input",t);e.fnUpdate(n[0].value,t,0,!1),
                e.fnUpdate(n[1].value,t,1,!1),e.fnUpdate(n[2].value,t,2,!1),e.fnUpdate(n[3].value,t,3,!1),e.fnUpdate(n[4].value,t,4,!1),e.fnUpdate(n[5].value,t,5,!1),
                e.fnUpdate('<a class="edit" href="">edit</a>',t,6,!1),
                e.fnUpdate('<a class="delete" href="">delete</a>',t,7,!1),
                e.fnDraw()}var a=$("#sample_editable_1"),
            l=a.dataTable({lengthMenu:[[5,15,20,-1],[5,15,20,"ȫ��"]],
                pageLength:10,language:{lengthMenu:" _MENU_ records"},
                columnDefs:[{orderable:!0,targets:[0]},
                    {searchable:!0,targets:[0]}],order:[[0,"asc"]]}),
            r=($("#sample_editable_1_wrapper"),null),o=!1;$("#sample_editable_1_new")
            .click(function(e){if(e.preventDefault(),o&&r){if(!confirm("Previose row not saved. Do you want to save it ?"))
                return l.fnDeleteRow(r),r=null,void(o=!1);n(l,r),$(r).find("td:first").html("Untitled"),r=null,o=!1}
                var a=l.fnAddData(["","","","","","","",""]),i=l.fnGetNodes(a[0]);t(l,i),r=i,o=!0}),
            a.on("click",".delete",function(e){if(e.preventDefault(),0!=confirm("Are you sure to delete this row ?")){
                var t=$(this).parents("tr")[0];l.fnDeleteRow(t),
                    $.ajax({
                        url:"../finance/deleteAccountDetail.do?id="+ t.cells[0].innerText
                    })
            }}),
            a.on("click",".cancel",function(t){t.preventDefault(),o?(l.fnDeleteRow(r),r=null,o=!1):(e(l,r),r=null)}),
            a.on("click",".edit",function(a){a.preventDefault();
                var o=$(this).parents("tr")[0];
                var pid=$("#pid").val();
                null!==r&&r!=o?(e(l,r),t(l,o),r=o):r==o&&"Save"==this.innerHTML?(n(l,r),r=null,
                $.ajax({
                            method:"Post",
                            url:"../finance/saveOrUpdateAccountDetail.do?id="+o.cells[0].innerText+"&d="+o.cells[1].innerText+"&debit="+o.cells[3].innerText+"&credit="+o.cells[2].innerText+"&memo="+o.cells[5].innerText+"&pid="+pid,
                            success:function(msg){
                                if(msg=="error"){
                                    alert("����ʧ�ܣ��˻��������¼���������ѱ����ɣ����ʵ��")
                                }
                            }
                            //url:"../finance/saveOrUpdateAccountDetail.do?id="+o.cells[0].innerText+"&d="+o.cells[1].innerText
                        })
                ):(t(l,o),r=o)})};return{init:function(){e()}}}();
jQuery(document).ready(function(){TableDatatablesEditable.init()});