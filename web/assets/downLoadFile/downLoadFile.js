/* creator：张旭博，2017-05-08 16:30:58，文件预览 */
var customVersion = ['doc','docx','xls','xlsx','zip','rar','apk','ipa','ppt','pptx','txt','pdf','png','jpg','wps','et','md']
function seeOnline(obj) {
    console.log(obj)
    if(!(obj instanceof jQuery)){
        obj = $(obj)
    }
    var path = obj.attr("path");
    var pathArr = path.split(".");

    var type = pathArr[pathArr.length - 1].toLowerCase() ;
    switch (type){
        case 'doc':case 'docx':
        case 'xls':case 'xlsx':
        case 'ppt':case 'pptx':
            obj.attr('target','_blank').attr('href','https://view.officeapps.live.com/op/view.aspx?src='+$.fileUrl+path);
            break;
        case 'png':case 'jpg':case 'jpeg':case 'gif':
        case "pdf":case 'txt':
            obj.attr('target','_blank').attr('href',$.fileUrl+path);
            break;
        case "md":
            obj.attr('target','_blank').attr('href',$.webRoot+'/assets/md/index.html?src='+$.fileUrl+path);
            break;
        default:
            // case "rar":case "zip":
            // case "pdf": // wyu：ow365有文件大小(5M)限制，改为直接用浏览器预览
            // case 'et':
            // case "txt":
            obj.attr('target','_blank').attr('href',$.ow365url+path);
    }
}

function seePicture(path) {
    if (path) {
        console.log(path)
        // var ind = path.indexOf("upload") + 7;
        // path = path.substr(ind,path.length);
        path = path.replace("^/upload/","/").replace("^upload/","");
        var url = $.fileUrl + path
        console.log(url)
        window.open(url)
    }
}

// creator：侯杏哲，2017-05-08 16:30:58，文件下载 ， 兼容FF/IE 下载图片直接打开，文件名不一致问题
function getDownLoad(_this) {
    console.log('getDownLoad')
    if(!(_this instanceof jQuery)){
        _this = $(_this)
    }
    var path = _this.attr("path");
    console.log(path)
    var pathArr = path.split(".");
    var url = $.uploadUrl + path;
    var filename = _this.attr('download');
    var type = pathArr[pathArr.length - 1];
    if ($.inArray(type,['png', 'jpg', 'jpeg', 'gif']) != -1) {
        var broweer = myBrowser();
        if (broweer == 'IE'){
            downloadIEFile(filename, url);
            return false;
        }
    }
    _this.attr('target','_blank').attr('href',url);
    return true ;
}

// /* creator : 侯杏哲 2018-03-13 处理下载图片的兼容性问题 , 下载文件的时候不做处理  */
// function getDownLoad(_this) {
//     var path = _this.attr("path");
//     var pathArr = path.split(".");
//     var url = $.uploadUrl + path;
//     var filename = _this.attr('download');
//     var type = pathArr[pathArr.length - 1];
//     switch (type) {
//         case 'png':
//         case 'jpg':
//         case 'jpeg':
//         case 'gif':
//             downloadFile(url, filename, type);
//             return false;
//         default:
//             _this.attr('target','_blank').attr('href',url).attr("download" , filename) ;
//             return true ;
//     }
// }
// function downloadFile(url, filename, type){
//     var broweer = myBrowser();
//     console.log(broweer);
//     switch(broweer){
//         case 'IE':
//             downloadIEFile(filename, url);
//             break ;
//         case 'FF':
//             downloadFFFile(filename, url, type);
//             break ;
//         default:
//             downloadEdgeFile(filename, url);
//     }
// }
//判断浏览器类型 wuyu： 20180416 代码移动到 common.js longin.js
// function myBrowser() {
//     var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
//     var isOpera = userAgent.indexOf("OPR") > -1; if (isOpera) { return "Opera" }; //判断是否Opera浏览器 OPR/43.0.2442.991
//     //if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) { return "IE"; }; //判断是否IE浏览器
//     if (userAgent.indexOf("Firefox") > -1) { return "FF"; } //判断是否Firefox浏览器  Firefox/51.0
//     if (userAgent.indexOf("Trident") > -1 || userAgent.indexOf("MSIE") > -1) { return "IE"; } //判断是否IE浏览器  Trident/7.0; rv:11.0
//     if (userAgent.indexOf("Edge") > -1) { return "Edge"; } //判断是否Edge浏览器  Edge/14.14393
//     if (userAgent.indexOf("Chrome") > -1) { return "Chrome"; }// Chrome/56.0.2924.87
//     if (userAgent.indexOf("Safari") > -1) { return "Safari"; } //判断是否Safari浏览器 AppleWebKit/534.57.2 Version/5.1.7 Safari/534.57.2
// }
// function downloadFFFile(fileName, content, contentType) {
// //    console.log('downloadFFFile start');
//     var image = new Image();
//     image.crossOrigin = 'anonymous';
//     image.src = content;
//     image.onload = function () { //wyu:图像可以跨域访问
//         var canvas = document.createElement('canvas');
//         canvas.width = image.width;
//         canvas.height = image.height;
//         context = canvas.getContext('2d');
//         context.drawImage(image, 0, 0);
//         var blob = base64Img2Blob(canvas.toDataURL(contentType));
//         var url = URL.createObjectURL(blob);
//         var a = document.createElement('a');
//         a.href = url;
//         a.download = fileName;
//         document.body.appendChild(a);
//         a.click();
//         URL.revokeObjectURL(url);
//         document.body.removeChild(a);
//     }
// //    console.log('downloadFFFile end');
// }
// function downloadEdgeFile(fileName, url) {
// //    var blob = new Blob([content]);
//     var a = document.createElement('a');
// //    var url = URL.createObjectURL(blob);
//     a.href = url;
//     a.download = fileName;
//     a.click();
// //    URL.revokeObjectURL(url);
// }
//支持IE11
function downloadIEFile(fileName, url, contentType) {
    console.log('downloadIEFile start');
    getDataUrlBySrc(url).then(function (b64) {
        console.log(b64);
        var blob = base64Img2Blob(b64);
        window.navigator.msSaveOrOpenBlob(blob, fileName);
        console.log('downloadIEFile finished');
    });
    function getDataUrlBySrc(src) {
        var def = $.Deferred();
        const xmlHTTP = new XMLHttpRequest();
        xmlHTTP.open("GET", src, true);
        xmlHTTP.timeout = 20000;
        xmlHTTP.crossDomain = true;
        // 以 ArrayBuffer 的形式返回数据
        xmlHTTP.responseType = "arraybuffer";
        xmlHTTP.onload = function (e) {
            // 1. 将返回的数据存储在一个 8 位无符号整数值的类型化数组里面
            const arr = new Uint8Array(xmlHTTP.response);
            // 2. 转为 charCode 字符串
            const raw = Array.prototype.map
                .call(arr, function (charCode) {
                    return String.fromCharCode(charCode);
                })
                .join("");
            // 3. 将二进制字符串转为 base64 编码的字符串
            const b64 = btoa(raw);
            const dataURL = "data:image/jpeg;base64," + b64;
            def.resolve(dataURL);
        };
        xmlHTTP.onerror = function (err) {
            console.log(err);
        };
        xmlHTTP.send();
        return def.promise(); //就在这里调用
    }
    console.log('downloadIEFile end');
}
function base64Img2Blob(code){
    var parts = code.split(';base64,');
    var contentType = parts[0].split(':')[1];
    var raw = window.atob(parts[1]);
    var rawLength = raw.length;
    var uInt8Array = new Uint8Array(rawLength);
    for (var i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
    }
    return new Blob([uInt8Array], {type: contentType});
}

// creator: 张旭博, 2020-07-27 18:11:07, 适合小窗预览和下载（示例可见discussDetail.vue)
function previewOrDownload(obj, type) {
    if(!(obj instanceof jQuery)){
        obj = $(obj)
    }
    var path = obj.attr("path");
    var filename = obj.attr('download');

    var pathArr = path.split(".");

    var url = $.uploadUrl + path;
    var suffix = pathArr[pathArr.length - 1] ;

    var eleLink = document.createElement('a');
    eleLink.style.display = 'none';
    eleLink.target = '_blank';

    if (type === 1) {
        switch (suffix){
            case 'doc':case 'docx':
            case 'xls':case 'xlsx':
            case 'ppt':
                url = 'https://view.officeapps.live.com/op/view.aspx?src='+$.fileUrl+path
                break;
            case 'png':case 'jpg':case 'jpeg':case 'gif':
            case "pdf":
            case "txt":
                url = $.fileUrl+path
                break;
            case "md":
                url = $.webRoot+'/assets/md/index.html?src=/upload/'+path
                break;
            default:
                // case "rar":case "zip":
                // case "pdf": // wyu：ow365有文件大小(5M)限制，改为直接用浏览器预览
                // case 'et':
                url = $.ow365url+path
        }
    } else {
        eleLink.download = filename;
        if ($.inArray(suffix,['png', 'jpg', 'jpeg', 'gif'])) {
            var broweer = myBrowser();
            if (broweer == 'IE'){
                downloadIEFile(filename, url);
                return false;
            }
        }
    }
    eleLink.href = url;
    document.body.appendChild(eleLink);
    eleLink.click();
    document.body.removeChild(eleLink);
}