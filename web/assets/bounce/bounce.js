$.fn.extend({
	dragging:function(data){
		var $this = $(this);
		var xPage;
		var yPage;
		var X;
		var Y;
		var xRand = 0;
		var yRand = 0;
		var father = $this.parent();
		var defaults = {
			move : 'both',
			randomPosition : true ,
			hander:1
		}
		var opt = $.extend({},defaults,data);
		var movePosition = opt.move;
		var random = opt.randomPosition;
		var hander = opt.hander;
		if(hander == 1){
			hander = $this;
		}else{
			hander = $this.find(opt.hander);
		}
		father.css({ "overflow": "hidden", "left": "0px", "right": "0px", "top": "0px", "bottom": "0px","margin":"0px","width":"100%" });

		//---初始化
		var faWidth = father.width();
		var faHeight = father.height();
		var thisWidth = $this.width();
		var thisHeight = $this.height();
		var mDown = false;
		var positionX;
		var positionY;
		var moveX ;
		var moveY ;
		// father.css({"position":"absolute","overflow":"hidden"});
		// father.css({ "overflow": "hidden", "left": "0px", "right": "0px", "top": "0px", "bottom": "0px","margin":"0px","width":"100%" }); // 移动到上面去了（解决拖动问题）
		$this.css({"position":"fixed","left":(faWidth-thisWidth)/2+'px'});
		// $this.offset({left:(faWidth-thisWidth)/2,top:200});
		hander.css({"cursor":"move"});
		/* updater：张旭博，2017-08-17 10:49:20，注释掉41-43行,解决select插件问题，如出现问题找我 */
		// $this.find('*').not('img,span').mousedown(function(e) {
		// 	e.stopPropagation();//阻止当前事件在DOMs树上冒泡
		// });
		if(random){
			$thisRandom();
		}
		function $thisRandom(){ //随机函数
			$this.each(function(index){
				var randY = parseInt(Math.random()*(faHeight-thisHeight));
				var randX = parseInt(Math.random()*(faWidth-thisWidth));
				if(movePosition.toLowerCase() == 'both'){
					$(this).css({
								top:randY,
								left:randX
							});
				}
			});
		}
		hander.mousedown(function(e){
			father.children().css({"zIndex":"0"});
			$this.css({"zIndex":"1"});
			mDown = true;
			X = e.pageX;
			Y = e.pageY;
			positionX = $this.position().left;
			positionY = $this.position().top;
			return false;
		});
		$(document).mouseup(function(e){
			mDown = false;
		});
		$(document.body).mousemove(function(e){
			xPage = e.pageX;
			moveX = positionX+xPage-X;
			yPage = e.pageY;
			moveY = positionY+yPage-Y;
			function thisAllMove(){ //全部移动
				if(mDown == true){
					$this.css({"left":moveX,"top":moveY});
				}else{
					return;
				}
				if(moveX < 0){
					$this.css({"left":"0"});
				}
				// if(moveY < 0){
				// 	$this.css({"top":"0"});
				// }
				if(moveX > (faWidth-thisWidth)){
					$this.css({"left":faWidth-thisWidth});
				}
				 if(moveY>=(faHeight-thisHeight)){
				 	$this.css({"top":faHeight-thisHeight});
				 }
			}
			if(movePosition.toLowerCase() == 'both'){
					thisAllMove();
				}
		});
	}
});
// 弹框控件 ， 动作类
function Bounce(className) {
	this.klass = className ;
	 // 弹框对象 
    this.cancel = function(){
        $(this.klass).hide().children().hide();
        $(this.klass).stopTime ();
    };
    this.show = function(obj){
        if( obj && typeof(obj) == typeof( new Object()) ){
            $(this.klass).show().children().hide();
            obj.show();
            obj.dragging({
                move: 'both',
                randomPosition: false,
                hander:".bonceHead,.acHead "
            })
            obj.css({"top":($(window).height()-obj.height())/2-200});
            obj.css({"left":($(window).width()-obj.width())/2});
        }
    };
    this.resize = function (obj) {
        obj.css({"top":($(window).height()-obj.height())/2-200});
    };
    // 设置everyTime，依赖于jquery.times-1.2.js的everyTime: function(interval, label, fn, times)函数。
    this.everyTime = function(interval, label, fn, times){
    	$(this.klass).everyTime(interval, label, fn, times);
    }
}

var bounce = new Bounce(".bounce");	// 普通弹框
var bounce_Fixed = new Bounce(".bounce_Fixed"); // fixed 弹框,
// var bounceFloating = new Bounce(".bounceFloating"); // 悬浮框,
// var pageContainer = new Bounce(".pageContainer"); // 页面内的弹框
var common_pop = new Bounce(".common_pop");

$(function () {
    // 设置 bounce 的高度
	setInterval(function(){setBounceHeight()} , 500);
});

function setBounceHeight(){
	var height = $(document).height() ;
	$(".bounce").height(height);
}