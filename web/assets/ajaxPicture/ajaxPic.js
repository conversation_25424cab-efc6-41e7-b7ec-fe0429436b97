/**
 * Created by 侯杏哲 on 2016/10/13.
 */
/* Creator : 侯杏哲 处理页面加载完成页面登录的卡顿问题   */
var ajaxArr = [] ;  // 标记页面刚加载完成时，需要加载的异步请求，
//  ajaxArr[0] 菜单的异步
//  ajaxArr[1] 获取页面列表的异步
//  ajaxArr[2] 获取备用信息的异步
//  目前只记录前三个接口的异步情况，全部完成，取消异步显示

// loading 标识的开关
var loading = {
    open : function(){
        $(".zhe_AjaxPic").show();
    },
    close : function(){
        $(".zhe_AjaxPic").hide();
    }
} ; 
var chargeClose = function( n ){
    ajaxArr[n] = 0 ;
    var k = true ;
    for(var i = 0 ; i < ajaxArr.length ; i++ ){
        if(ajaxArr[i] == 1 ){ k = false ; break ;  }
    }
    if(k){  loading.close() ;     }
} ; 





















