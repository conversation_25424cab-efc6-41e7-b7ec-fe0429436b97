+function(t){"use strict";var n=!1,o=function(o,e){var i=this;this.init("confirmation",o,e),t(o).on("show.bs.confirmation",function(n){i.options.onShow(n,this),t(this).addClass("open");var o=i.options,e=o.all_selector;o.singleton&&t(e).not(i.$element).each(function(){t(this).hasClass("open")&&t(this).confirmation("hide")})}),t(o).on("hide.bs.confirmation",function(n){i.options.onHide(n,this),t(this).removeClass("open")}),t(o).on("shown.bs.confirmation",function(){{var o=i.options;o.all_selector}i.isPopout()&&(n||(n=t("body").on("click",function(o){i.$element.is(o.target)||i.$element.has(o.target).length||t(".popover").has(o.target).length||(i.hide(),i.inState.click=!1,t("body").unbind(o),n=!1)})))}),t(o).on("click",function(t){t.preventDefault()})};if(!t.fn.popover||!t.fn.tooltip)throw new Error("Confirmation requires popover.js and tooltip.js");o.VERSION="1.0.2",o.DEFAULTS=t.extend({},t.fn.popover.Constructor.DEFAULTS,{placement:"right",title:"Are you sure?",btnOkClass:"btn btn-sm btn-danger",btnOkLabel:"Delete",btnOkIcon:"glyphicon glyphicon-ok",btnCancelClass:"btn btn-sm btn-default",btnCancelLabel:"Cancel",btnCancelIcon:"glyphicon glyphicon-remove",href:"#",target:"_self",singleton:!0,popout:!0,onShow:function(){},onHide:function(){},onConfirm:function(){},onCancel:function(){},template:'<div class="popover"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"><a data-apply="confirmation">Yes</a><a data-dismiss="confirmation">No</a></div></div>'}),o.prototype=t.extend({},t.fn.popover.Constructor.prototype),o.prototype.constructor=o,o.prototype.getDefaults=function(){return o.DEFAULTS},o.prototype.setContent=function(){var n=this,o=this.tip(),e=this.getTitle(),i=o.find('[data-apply="confirmation"]'),a=o.find('[data-dismiss="confirmation"]'),s=this.options;i.addClass(this.getBtnOkClass()).html(this.getBtnOkLabel()).prepend(t("<i></i>").addClass(this.getBtnOkIcon())," ").attr("href",this.getHref()).attr("target",this.getTarget()).off("click").on("click",function(t){s.onConfirm(t,n.$element),n.hide(),n.inState.click=!1}),a.addClass(this.getBtnCancelClass()).html(this.getBtnCancelLabel()).prepend(t("<i></i>").addClass(this.getBtnCancelIcon())," ").off("click").on("click",function(t){s.onCancel(t,n.$element),n.hide(),n.inState.click=!1}),o.find(".popover-title")[this.options.html?"html":"text"](e),o.removeClass("fade top bottom left right in"),o.find(".popover-title").html()||o.find(".popover-title").hide()},o.prototype.getBtnOkClass=function(){var t=this.$element,n=this.options;return t.attr("data-btnOkClass")||("function"==typeof n.btnOkClass?n.btnOkClass.call(t[0]):n.btnOkClass)},o.prototype.getBtnOkLabel=function(){var t=this.$element,n=this.options;return t.attr("data-btnOkLabel")||("function"==typeof n.btnOkLabel?n.btnOkLabel.call(t[0]):n.btnOkLabel)},o.prototype.getBtnOkIcon=function(){var t=this.$element,n=this.options;return t.attr("data-btnOkIcon")||("function"==typeof n.btnOkIcon?n.btnOkIcon.call(t[0]):n.btnOkIcon)},o.prototype.getBtnCancelClass=function(){var t=this.$element,n=this.options;return t.attr("data-btnCancelClass")||("function"==typeof n.btnCancelClass?n.btnCancelClass.call(t[0]):n.btnCancelClass)},o.prototype.getBtnCancelLabel=function(){var t=this.$element,n=this.options;return t.attr("data-btnCancelLabel")||("function"==typeof n.btnCancelLabel?n.btnCancelLabel.call(t[0]):n.btnCancelLabel)},o.prototype.getBtnCancelIcon=function(){var t=this.$element,n=this.options;return t.attr("data-btnCancelIcon")||("function"==typeof n.btnCancelIcon?n.btnCancelIcon.call(t[0]):n.btnCancelIcon)},o.prototype.getHref=function(){var t=this.$element,n=this.options;return t.attr("data-href")||("function"==typeof n.href?n.href.call(t[0]):n.href)},o.prototype.getTarget=function(){var t=this.$element,n=this.options;return t.attr("data-target")||("function"==typeof n.target?n.target.call(t[0]):n.target)},o.prototype.isPopout=function(){var t,n=this.$element,o=this.options;return t=n.attr("data-popout")||("function"==typeof o.popout?o.popout.call(n[0]):o.popout),"false"==t&&(t=!1),t};var e=t.fn.confirmation;t.fn.confirmation=function(n){var e=this;return this.each(function(){var i=t(this),a=i.data("bs.confirmation"),s="object"==typeof n&&n;s=s||{},s.all_selector=e.selector,(a||"destroy"!=n)&&(a||i.data("bs.confirmation",a=new o(this,s)),"string"==typeof n&&a[n]())})},t.fn.confirmation.Constructor=o,t.fn.confirmation.noConflict=function(){return t.fn.confirmation=e,this}}(jQuery);