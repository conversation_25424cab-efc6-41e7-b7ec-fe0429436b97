/*!
* jquery.inputmask.bundle.js
* http://github.com/RobinHerbots/jquery.inputmask
* Copyright (c) 2010 - 2015 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.2.1-29
*/
!function($){function Inputmask(e){this.el=void 0,this.opts=$.extend(!0,{},this.defaults,e),this.noMasksCache=e&&void 0!==e.definitions,this.userOptions=e||{},resolveAlias(this.opts.alias,e,this.opts)}function isInputEventSupported(e){var t=document.createElement("input"),i="on"+e,a=i in t;return a||(t.setAttribute(i,"return;"),a="function"==typeof t[i]),t=null,a}function isInputTypeSupported(e){var t="text"===e||"tel"===e||"password"===e;if(!t){var i=document.createElement("input");i.setAttribute("type",e),t="text"===i.type,i=null}return t}function resolveAlias(e,t,i){var a=i.aliases[e];return a?(a.alias&&resolveAlias(a.alias,void 0,i),$.extend(!0,i,a),$.extend(!0,i,t),!0):(null===i.mask&&(i.mask=e),!1)}function importAttributeOptions(npt,opts,userOptions){function importOption(option){var optionData=npt.getAttribute("data-inputmask-"+option.toLowerCase());null!==optionData&&(optionData="boolean"==typeof optionData?optionData:optionData.toString(),"string"==typeof optionData&&0===option.indexOf("on")&&(optionData=eval("("+optionData+")")),"mask"===option&&0===optionData.indexOf("[")?(userOptions[option]=optionData.replace(/[\s[\]]/g,"").split(","),userOptions[option][0]=userOptions[option][0].replace("'",""),userOptions[option][userOptions[option].length-1]=userOptions[option][userOptions[option].length-1].replace("'","")):userOptions[option]=optionData)}var attrOptions=npt.getAttribute("data-inputmask");if(attrOptions&&""!==attrOptions)try{attrOptions=attrOptions.replace(new RegExp("'","g"),'"');var dataoptions=$.parseJSON("{"+attrOptions+"}");$.extend(!0,userOptions,dataoptions)}catch(ex){}for(var option in opts)importOption(option);if(userOptions.alias){resolveAlias(userOptions.alias,userOptions,opts);for(option in opts)importOption(option)}return $.extend(!0,opts,userOptions),opts}function generateMaskSet(e,t){function i(t){function i(e,t,i,a){this.matches=[],this.isGroup=e||!1,this.isOptional=t||!1,this.isQuantifier=i||!1,this.isAlternator=a||!1,this.quantifier={min:1,max:1}}function a(t,i,a){var n=e.definitions[i];a=void 0!==a?a:t.matches.length;var r=t.matches[a-1];if(n&&!v){n.placeholder=$.isFunction(n.placeholder)?n.placeholder.call(this,e):n.placeholder;for(var o=n.prevalidator,s=o?o.length:0,l=1;l<n.cardinality;l++){var u=s>=l?o[l-1]:[],p=u.validator,c=u.cardinality;t.matches.splice(a++,0,{fn:p?"string"==typeof p?new RegExp(p):new function(){this.test=p}:new RegExp("."),cardinality:c?c:1,optionality:t.isOptional,newBlockMarker:void 0===r||r.def!==(n.definitionSymbol||i),casing:n.casing,def:n.definitionSymbol||i,placeholder:n.placeholder,mask:i}),r=t.matches[a-1]}t.matches.splice(a++,0,{fn:n.validator?"string"==typeof n.validator?new RegExp(n.validator):new function(){this.test=n.validator}:new RegExp("."),cardinality:n.cardinality,optionality:t.isOptional,newBlockMarker:void 0===r||r.def!==(n.definitionSymbol||i),casing:n.casing,def:n.definitionSymbol||i,placeholder:n.placeholder,mask:i})}else t.matches.splice(a++,0,{fn:null,cardinality:0,optionality:t.isOptional,newBlockMarker:void 0===r||r.def!==i,casing:null,def:i,placeholder:void 0,mask:i}),v=!1}function n(t,i){t.isGroup&&(t.isGroup=!1,a(t,e.groupmarker.start,0),i!==!0&&a(t,e.groupmarker.end))}function r(e,t,i,r){t.matches.length>0&&(void 0===r||r)&&(i=t.matches[t.matches.length-1],n(i)),a(t,e)}function o(){if(k.length>0){if(c=k[k.length-1],r(u,c,d,!c.isAlternator),c.isAlternator){f=k.pop();for(var e=0;e<f.matches.length;e++)f.matches[e].isGroup=!1;k.length>0?(c=k[k.length-1],c.matches.push(f)):g.matches.push(f)}}else r(u,g,d)}function s(t){function i(t){return t===e.optionalmarker.start?t=e.optionalmarker.end:t===e.optionalmarker.end?t=e.optionalmarker.start:t===e.groupmarker.start?t=e.groupmarker.end:t===e.groupmarker.end&&(t=e.groupmarker.start),t}t.matches=t.matches.reverse();for(var a in t.matches){var n=parseInt(a);if(t.matches[a].isQuantifier&&t.matches[n+1]&&t.matches[n+1].isGroup){var r=t.matches[a];t.matches.splice(a,1),t.matches.splice(n+1,0,r)}t.matches[a]=void 0!==t.matches[a].matches?s(t.matches[a]):i(t.matches[a])}return t}for(var l,u,p,c,f,d,m,h=/(?:[?*+]|\{[0-9\+\*]+(?:,[0-9\+\*]*)?\})|[^.?*+^${[]()|\\]+|./g,v=!1,g=new i,k=[],y=[];l=h.exec(t);)if(u=l[0],v)o();else switch(u.charAt(0)){case e.escapeChar:v=!0;break;case e.optionalmarker.end:case e.groupmarker.end:if(p=k.pop(),void 0!==p)if(k.length>0){if(c=k[k.length-1],c.matches.push(p),c.isAlternator){f=k.pop();for(var x=0;x<f.matches.length;x++)f.matches[x].isGroup=!1;k.length>0?(c=k[k.length-1],c.matches.push(f)):g.matches.push(f)}}else g.matches.push(p);else o();break;case e.optionalmarker.start:k.push(new i(!1,!0));break;case e.groupmarker.start:k.push(new i(!0));break;case e.quantifiermarker.start:var b=new i(!1,!1,!0);u=u.replace(/[{}]/g,"");var P=u.split(","),S=isNaN(P[0])?P[0]:parseInt(P[0]),w=1===P.length?S:isNaN(P[1])?P[1]:parseInt(P[1]);if(("*"===w||"+"===w)&&(S="*"===w?0:1),b.quantifier={min:S,max:w},k.length>0){var A=k[k.length-1].matches;l=A.pop(),l.isGroup||(m=new i(!0),m.matches.push(l),l=m),A.push(l),A.push(b)}else l=g.matches.pop(),l.isGroup||(m=new i(!0),m.matches.push(l),l=m),g.matches.push(l),g.matches.push(b);break;case e.alternatormarker:k.length>0?(c=k[k.length-1],d=c.matches.pop()):d=g.matches.pop(),d.isAlternator?k.push(d):(f=new i(!1,!1,!1,!0),f.matches.push(d),k.push(f));break;default:o()}for(;k.length>0;)p=k.pop(),n(p,!0),g.matches.push(p);return g.matches.length>0&&(d=g.matches[g.matches.length-1],n(d),y.push(g)),e.numericInput&&s(y[0]),y}function a(a,n){if(null===a||""===a)return void 0;if(1===a.length&&e.greedy===!1&&0!==e.repeat&&(e.placeholder=""),e.repeat>0||"*"===e.repeat||"+"===e.repeat){var r="*"===e.repeat?0:"+"===e.repeat?1:e.repeat;a=e.groupmarker.start+a+e.groupmarker.end+e.quantifiermarker.start+r+","+e.repeat+e.quantifiermarker.end}var o;return void 0===Inputmask.prototype.masksCache[a]||t===!0?(o={mask:a,maskToken:i(a),validPositions:{},_buffer:void 0,buffer:void 0,tests:{},metadata:n},t!==!0&&(Inputmask.prototype.masksCache[e.numericInput?a.split("").reverse().join(""):a]=o)):o=$.extend(!0,{},Inputmask.prototype.masksCache[a]),o}function n(e){return e=e.toString()}var r;if($.isFunction(e.mask)&&(e.mask=e.mask.call(this,e)),$.isArray(e.mask)){if(e.mask.length>1){e.keepStatic=null===e.keepStatic?!0:e.keepStatic;var o="(";return $.each(e.numericInput?e.mask.reverse():e.mask,function(e,t){o.length>1&&(o+=")|("),o+=n(void 0===t.mask||$.isFunction(t.mask)?t:t.mask)}),o+=")",a(o,e.mask)}e.mask=e.mask.pop()}return e.mask&&(r=void 0===e.mask.mask||$.isFunction(e.mask.mask)?a(n(e.mask),e.mask):a(n(e.mask.mask),e.mask)),r}function maskScope(e,t,i){function a(e,t,i){t=t||0;var a,r,o,s=[],l=0;do{if(e===!0&&n().validPositions[l]){var p=n().validPositions[l];r=p.match,a=p.locator.slice(),s.push(i===!0?p.input:A(l,r))}else o=u(l,a,l-1),r=o.match,a=o.locator.slice(),s.push(A(l,r));l++}while((void 0===nt||nt>l-1)&&null!==r.fn||null===r.fn&&""!==r.def||t>=l);return s.pop(),s}function n(){return t}function r(e){var t=n();t.buffer=void 0,t.tests={},e!==!0&&(t._buffer=void 0,t.validPositions={},t.p=0)}function o(e,t){var i=n(),a=-1,r=i.validPositions;void 0===e&&(e=-1);var o=a,s=a;for(var l in r){var u=parseInt(l);r[u]&&(t||null!==r[u].match.fn)&&(e>=u&&(o=u),u>=e&&(s=u))}return a=-1!==o&&e-o>1||e>s?o:s}function s(e,t,a){if(i.insertMode&&void 0!==n().validPositions[e]&&void 0===a){var r,s=$.extend(!0,{},n().validPositions),l=o();for(r=e;l>=r;r++)delete n().validPositions[r];n().validPositions[e]=t;var u,p=!0,d=n().validPositions;for(r=u=e;l>=r;r++){var m=s[r];if(void 0!==m)for(var h=u,v=-1;h<x()&&(null==m.match.fn&&d[r]&&(d[r].match.optionalQuantifier===!0||d[r].match.optionality===!0)||null!=m.match.fn);){if(null===m.match.fn||!i.keepStatic&&d[r]&&(void 0!==d[r+1]&&f(r+1,d[r].locator.slice(),r).length>1||void 0!==d[r].alternation)?h++:h=b(u),c(h,m.match.def)){p=k(h,m.input,!0,!0)!==!1,u=h;break}if(p=null==m.match.fn,v===h)break;v=h}if(!p)break}if(!p)return n().validPositions=$.extend(!0,{},s),!1}else n().validPositions[e]=t;return!0}function l(e,t,a,s){var l,u=e;for(n().p=e,l=u;t>l;l++)void 0!==n().validPositions[l]&&(a===!0||i.canClearPosition(n(),l,o(),s,i)!==!1)&&delete n().validPositions[l];for(r(!0),l=u+1;l<=o();){for(;void 0!==n().validPositions[u];)u++;var p=n().validPositions[u];u>l&&(l=u+1);var f=n().validPositions[l];void 0!==f&&y(l)&&void 0===p?(c(u,f.match.def)&&k(u,f.input,!0)!==!1&&(delete n().validPositions[l],l++),u++):l++}var d=o(),m=x();for(s!==!0&&a!==!0&&void 0!==n().validPositions[d]&&n().validPositions[d].input===i.radixPoint&&delete n().validPositions[d],l=d+1;m>=l;l++)n().validPositions[l]&&delete n().validPositions[l];r(!0)}function u(e,t,a){var r=n().validPositions[e];if(void 0===r)for(var s=f(e,t,a),l=o(),u=n().validPositions[l]||f(0)[0],p=void 0!==u.alternation?u.locator[u.alternation].toString().split(","):[],c=0;c<s.length&&(r=s[c],!(r.match&&(i.greedy&&r.match.optionalQuantifier!==!0||(r.match.optionality===!1||r.match.newBlockMarker===!1)&&r.match.optionalQuantifier!==!0)&&(void 0===u.alternation||u.alternation!==r.alternation||void 0!==r.locator[u.alternation]&&g(r.locator[u.alternation].toString().split(","),p))));c++);return r}function p(e){return n().validPositions[e]?n().validPositions[e].match:f(e)[0].match}function c(e,t){for(var i=!1,a=f(e),n=0;n<a.length;n++)if(a[n].match&&a[n].match.def===t){i=!0;break}return i}function f(e,t,i,a){function r(t,i,a,l){function p(a,l,d){if(u>1e4)throw"Inputmask: There is probably an error in your mask definition or in the code. Create an issue on github with an example of the mask you are using. "+n().mask;if(u===e&&void 0===a.matches)return c.push({match:a,locator:l.reverse()}),!0;if(void 0!==a.matches){if(a.isGroup&&d!==a){if(a=p(t.matches[$.inArray(a,t.matches)+1],l))return!0}else if(a.isOptional){var m=a;if(a=r(a,i,l,d)){if(o=c[c.length-1].match,s=0===$.inArray(o,m.matches),!s)return!0;f=!0,u=e}}else if(a.isAlternator){var h,v=a,g=[],k=c.slice(),y=l.length,x=i.length>0?i.shift():-1;if(-1===x||"string"==typeof x){var b=u,P=i.slice(),S=[];"string"==typeof x&&(S=x.split(","));for(var w=0;w<v.matches.length;w++){if(c=[],a=p(v.matches[w],[w].concat(l),d)||a,a!==!0&&void 0!==a&&S[S.length-1]<v.matches.length){var A=t.matches.indexOf(a)+1;t.matches.length>A&&(a=p(t.matches[A],[A].concat(l.slice(1,l.length)),d),a&&(S.push(A.toString()),$.each(c,function(e,t){t.alternation=l.length-1})))}h=c.slice(),u=b,c=[];for(var E=0;E<P.length;E++)i[E]=P[E];for(var I=0;I<h.length;I++){var R=h[I];R.alternation=R.alternation||y;for(var O=0;O<g.length;O++){var _=g[O];if(R.match.mask===_.match.mask&&("string"!=typeof x||-1!==$.inArray(R.locator[R.alternation].toString(),S))){h.splice(I,1),I--,_.locator[R.alternation]=_.locator[R.alternation]+","+R.locator[R.alternation],_.alternation=R.alternation;break}}}g=g.concat(h)}"string"==typeof x&&(g=$.map(g,function(e,t){if(isFinite(t)){var i,a=e.alternation,n=e.locator[a].toString().split(",");e.locator[a]=void 0,e.alternation=void 0;for(var r=0;r<n.length;r++)i=-1!==$.inArray(n[r],S),i&&(void 0!==e.locator[a]?(e.locator[a]+=",",e.locator[a]+=n[r]):e.locator[a]=parseInt(n[r]),e.alternation=a);if(void 0!==e.locator[a])return e}})),c=k.concat(g),u=e,f=c.length>0}else a=v.matches[x]?p(v.matches[x],[x].concat(l),d):!1;if(a)return!0}else if(a.isQuantifier&&d!==t.matches[$.inArray(a,t.matches)-1])for(var C=a,j=i.length>0?i.shift():0;j<(isNaN(C.quantifier.max)?j+1:C.quantifier.max)&&e>=u;j++){var M=t.matches[$.inArray(C,t.matches)-1];if(a=p(M,[j].concat(l),M)){if(o=c[c.length-1].match,o.optionalQuantifier=j>C.quantifier.min-1,s=0===$.inArray(o,M.matches)){if(j>C.quantifier.min-1){f=!0,u=e;break}return!0}return!0}}else if(a=r(a,i,l,d))return!0}else u++}for(var d=i.length>0?i.shift():0;d<t.matches.length;d++)if(t.matches[d].isQuantifier!==!0){var m=p(t.matches[d],[d].concat(a),l);if(m&&u===e)return m;if(u>e)break}}var o,s,l=n().maskToken,u=t?i:0,p=t||[0],c=[],f=!1;if(a===!0&&n().tests[e])return n().tests[e];if(void 0===t){for(var d,m=e-1;void 0===(d=n().validPositions[m])&&m>-1&&(!n().tests[m]||void 0===(d=n().tests[m][0]));)m--;void 0!==d&&m>-1&&(u=m,p=d.locator.slice())}for(var h=p.shift();h<l.length;h++){var v=r(l[h],p,[h]);if(v&&u===e||u>e)break}return(0===c.length||f)&&c.push({match:{fn:null,cardinality:0,optionality:!0,casing:null,def:""},locator:[]}),n().tests[e]=$.extend(!0,[],c),n().tests[e]}function d(){return void 0===n()._buffer&&(n()._buffer=a(!1,1)),n()._buffer}function m(){return void 0===n().buffer&&(n().buffer=a(!0,o(),!0)),n().buffer}function h(e,t,a){var o;if(a=a||m().slice(),e===!0)r(),e=0,t=a.length;else for(o=e;t>o;o++)delete n().validPositions[o],delete n().tests[o];for(o=e;t>o;o++)r(!0),a[o]!==i.skipOptionalPartCharacter&&k(o,a[o],!0,!0)}function v(e,t){switch(t.casing){case"upper":e=e.toUpperCase();break;case"lower":e=e.toLowerCase()}return e}function g(e,t){for(var a=i.greedy?t:t.slice(0,1),n=!1,r=0;r<e.length;r++)if(-1!==$.inArray(e[r],a)){n=!0;break}return n}function k(e,t,a,p){function c(e,t,a,u){var p=!1;return $.each(f(e),function(c,f){for(var d=f.match,g=t?1:0,y="",x=d.cardinality;x>g;x--)y+=S(e-(x-1));if(t&&(y+=t),p=null!=d.fn?d.fn.test(y,n(),e,a,i):t!==d.def&&t!==i.skipOptionalPartCharacter||""===d.def?!1:{c:d.def,pos:e},p!==!1){var b=void 0!==p.c?p.c:t;b=b===i.skipOptionalPartCharacter&&null===d.fn?d.def:b;var P=e,w=m();if(void 0!==p.remove&&($.isArray(p.remove)||(p.remove=[p.remove]),$.each(p.remove.sort(function(e,t){return t-e}),function(e,t){l(t,t+1,!0)})),void 0!==p.insert&&($.isArray(p.insert)||(p.insert=[p.insert]),$.each(p.insert.sort(function(e,t){return e-t}),function(e,t){k(t.pos,t.c,!0)})),p.refreshFromBuffer){var A=p.refreshFromBuffer;if(a=!0,h(A===!0?A:A.start,A.end,w),void 0===p.pos&&void 0===p.c)return p.pos=o(),!1;if(P=void 0!==p.pos?p.pos:e,P!==e)return p=$.extend(p,k(P,b,!0)),!1}else if(p!==!0&&void 0!==p.pos&&p.pos!==e&&(P=p.pos,h(e,P),P!==e))return p=$.extend(p,k(P,b,!0)),!1;return p!==!0&&void 0===p.pos&&void 0===p.c?!1:(c>0&&r(!0),s(P,$.extend({},f,{input:v(b,d)}),u)||(p=!1),!1)}}),p}function d(e,t,a,s){for(var l,p,c,f,d,m,h=$.extend(!0,{},n().validPositions),v=o();v>=0&&(f=n().validPositions[v],!f||void 0===f.alternation||(l=v,p=n().validPositions[l].alternation,u(l).locator[f.alternation]===f.locator[f.alternation]));v--);if(void 0!==p){l=parseInt(l);for(var g in n().validPositions)if(g=parseInt(g),f=n().validPositions[g],g>=l&&void 0!==f.alternation){var y=n().validPositions[l].locator[p].toString().split(","),x=f.locator[p]||y[0];x.length>0&&(x=x.split(",")[0]);for(var b=0;b<y.length;b++)if(x<y[b]){for(var P,S,w=g;w>=0;w--)if(P=n().validPositions[w],void 0!==P){S=P.locator[p],P.locator[p]=parseInt(y[b]);break}if(x!==P.locator[p]){var A=[],E=0;for(d=g+1;d<o()+1;d++)m=n().validPositions[d],m&&(null!=m.match.fn?A.push(m.input):e>d&&E++),delete n().validPositions[d],delete n().tests[d];for(r(!0),i.keepStatic=!i.keepStatic,c=!0;A.length>0;){var I=A.shift();if(I!==i.skipOptionalPartCharacter&&!(c=k(o()+1,I,!1,!0)))break}if(P.alternation=p,P.locator[p]=S,c){var R=o(e)+1,O=0;for(d=g+1;d<o()+1;d++)m=n().validPositions[d],m&&null==m.match.fn&&e>d&&O++;e+=O-E,c=k(e>R?R:e,t,a,s)}if(i.keepStatic=!i.keepStatic,c)return c;r(),n().validPositions=$.extend(!0,{},h)}}break}}return!1}function P(e,t){for(var i=n().validPositions[t],a=i.locator,r=a.length,o=e;t>o;o++)if(!y(o)){var l=f(o),u=l[0],p=-1;$.each(l,function(e,t){for(var i=0;r>i;i++)t.locator[i]&&g(t.locator[i].toString().split(","),a[i].toString().split(","))&&i>p&&(p=i,u=t)}),s(o,$.extend({},u,{input:u.match.def}),!0)}}a=a===!0;for(var w=m(),E=e-1;E>-1&&!n().validPositions[E];E--);for(E++;e>E;E++)void 0===n().validPositions[E]&&((!y(E)||w[E]!==A(E))&&f(E).length>1||w[E]===i.radixPoint||"0"===w[E]&&$.inArray(i.radixPoint,w)<E)&&c(E,w[E],!0);var I=e,R=!1,O=$.extend(!0,{},n().validPositions);if(I<x()&&(m(),R=c(I,t,a,p),(!a||p)&&R===!1)){var _=n().validPositions[I];if(!_||null!==_.match.fn||_.match.def!==t&&t!==i.skipOptionalPartCharacter){if((i.insertMode||void 0===n().validPositions[b(I)])&&!y(I))for(var j=I+1,M=b(I);M>=j;j++)if(R=c(j,t,a,p),R!==!1){P(I,j),I=j;break}}else R={caret:b(I)}}if(R===!1&&i.keepStatic&&C(w)&&(R=d(e,t,a,p)),R===!0&&(R={pos:I}),$.isFunction(i.postValidation)&&R!==!1&&!a){r(!0);var F=i.postValidation(m(),i);if(F){if(F.refreshFromBuffer){var D=F.refreshFromBuffer;h(D===!0?D:D.start,D.end,F.buffer),r(!0),R=F}}else r(!0),n().validPositions=$.extend(!0,{},O),R=!1}return R}function y(e){var t=p(e);if(null!=t.fn)return t.fn;if(!i.keepStatic&&void 0===n().validPositions[e]){for(var a=f(e),r=!0,o=0;o<a.length;o++)if(""!==a[o].match.def&&(void 0===a[o].alternation||a[o].locator[a[o].alternation].length>1)){r=!1;break}return r}return!1}function x(){var e;nt=at.prop("maxLength"),-1===nt&&(nt=void 0);var t,i=o(),a=n().validPositions[i],r=void 0!==a?a.locator.slice():void 0;for(t=i+1;void 0===a||null!==a.match.fn||null===a.match.fn&&""!==a.match.def;t++)a=u(t,r,t-1),r=a.locator.slice();var s=p(t-1);return e=""!==s.def?t:t-1,void 0===nt||nt>e?e:nt}function b(e,t){var a=x();if(e>=a)return a;for(var n=e;++n<a&&(t===!0&&(p(n).newBlockMarker!==!0||!y(n))||t!==!0&&!y(n)&&(i.nojumps!==!0||i.nojumpsThreshold>n)););return n}function P(e,t){var i=e;if(0>=i)return 0;for(;--i>0&&(t===!0&&p(i).newBlockMarker!==!0||t!==!0&&!y(i)););return i}function S(e){return void 0===n().validPositions[e]?A(e):n().validPositions[e].input}function w(e,t,a,n,o){if(n&&$.isFunction(i.onBeforeWrite)){var s=i.onBeforeWrite.call(e,n,t,a,i);if(s){if(s.refreshFromBuffer){var l=s.refreshFromBuffer;h(l===!0?l:l.start,l.end,s.buffer||t),r(!0),t=m()}a=void 0!==s.caret?s.caret:a}}e.inputmask._valueSet(t.join("")),void 0===a||void 0!==n&&"blur"===n.type||R(e,a),o===!0&&(lt=!0,$(e).trigger("input"))}function A(e,t){if(t=t||p(e),void 0!==t.placeholder)return t.placeholder;if(null===t.fn){if(!i.keepStatic&&void 0===n().validPositions[e]){for(var a,r=f(e),o=!1,s=0;s<r.length;s++){if(a&&""!==r[s].match.def&&r[s].match.def!==a.match.def&&(void 0===r[s].alternation||r[s].alternation===a.alternation)){o=!0;break}r[s].match.optionality!==!0&&r[s].match.optionalQuantifier!==!0&&(a=r[s])}if(o)return i.placeholder.charAt(e%i.placeholder.length)}return t.def}return i.placeholder.charAt(e%i.placeholder.length)}function E(e,t,a,s){function l(){var e=!1,t=d().slice(f,b(f)).join("").indexOf(c);if(-1!==t&&!y(f)){e=!0;for(var i=d().slice(f,f+t),a=0;a<i.length;a++)if(" "!==i[a]){e=!1;break}}return e}var p=void 0!==s?s.slice():e.inputmask._valueGet().split(""),c="",f=0;if(r(),n().p=b(-1),t&&e.inputmask._valueSet(""),!a)if(i.autoUnmask!==!0){var h=d().slice(0,b(-1)).join(""),v=p.join("").match(new RegExp("^"+Inputmask.escapeRegex(h),"g"));v&&v.length>0&&(p.splice(0,v.length*h.length),f=b(f))}else f=b(f);$.each(p,function(t,r){var s=$.Event("keypress");s.which=r.charCodeAt(0),c+=r;var p=o(void 0,!0),d=n().validPositions[p],m=u(p+1,d?d.locator.slice():void 0,p);if(!l()||a||i.autoUnmask){var h=a?t:null==m.match.fn&&m.match.optionality&&p+1<n().p?p+1:n().p;G.call(e,s,!0,!1,a,h),f=h+1,c=""}else G.call(e,s,!0,!1,!0,p+1)}),t&&w(e,m(),document.activeElement===e?b(o(0)):void 0,$.Event("checkval"))}function I(e){if(e[0].inputmask&&!e.hasClass("hasDatepicker")){var t=[],a=n().validPositions;for(var r in a)a[r].match&&null!=a[r].match.fn&&t.push(a[r].input);var o=0===t.length?null:(ot?t.reverse():t).join("");if(null!==o){var s=(ot?m().slice().reverse():m()).join("");$.isFunction(i.onUnMask)&&(o=i.onUnMask.call(e,s,o,i)||o)}return o}return e[0].inputmask._valueGet()}function R(e,t,a){function n(e){if(ot&&"number"==typeof e&&(!i.greedy||""!==i.placeholder)){var t=m().join("").length;e=t-e}return e}var r;if("number"!=typeof t)return e.setSelectionRange?(t=e.selectionStart,a=e.selectionEnd):window.getSelection?(r=window.getSelection().getRangeAt(0),(r.commonAncestorContainer.parentNode===e||r.commonAncestorContainer===e)&&(t=r.startOffset,a=r.endOffset)):document.selection&&document.selection.createRange&&(r=document.selection.createRange(),t=0-r.duplicate().moveStart("character",-1e5),a=t+r.text.length),{begin:n(t),end:n(a)};if(t=n(t),a=n(a),a="number"==typeof a?a:t,$(e).is(":visible")){var o=e.style.fontSize.replace("px","")*a;if(e.scrollLeft=o>e.scrollWidth?o:0,androidchrome||i.insertMode!==!1||t!==a||a++,e.setSelectionRange)e.selectionStart=t,e.selectionEnd=a;else if(window.getSelection){if(r=document.createRange(),void 0===e.firstChild){var s=document.createTextNode("");e.appendChild(s)}r.setStart(e.firstChild,t<e.inputmask._valueGet().length?t:e.inputmask._valueGet().length),r.setEnd(e.firstChild,a<e.inputmask._valueGet().length?a:e.inputmask._valueGet().length),r.collapse(!0);var l=window.getSelection();l.removeAllRanges(),l.addRange(r)}else e.createTextRange&&(r=e.createTextRange(),r.collapse(!0),r.moveEnd("character",a),r.moveStart("character",t),r.select())}}function O(e){var t,i,a=m(),r=a.length,s=o(),l={},p=n().validPositions[s],c=void 0!==p?p.locator.slice():void 0;for(t=s+1;t<a.length;t++)i=u(t,c,t-1),c=i.locator.slice(),l[t]=$.extend(!0,{},i);var d=p&&void 0!==p.alternation?p.locator[p.alternation]:void 0;for(t=r-1;t>s&&(i=l[t],(i.match.optionality||i.match.optionalQuantifier||d&&(d!==l[t].locator[p.alternation]&&null!=i.match.fn||null===i.match.fn&&i.locator[p.alternation]&&g(i.locator[p.alternation].toString().split(","),d.toString().split(","))&&""!==f(t)[0].def))&&a[t]===A(t,i.match));t--)r--;return e?{l:r,def:l[r]?l[r].match:void 0}:r}function _(e){for(var t=O(),i=e.length-1;i>t&&!y(i);i--);return e.splice(t,i+1-t),e}function C(e){if($.isFunction(i.isComplete))return i.isComplete.call(at,e,i);if("*"===i.repeat)return void 0;var t=!1,a=O(!0),r=P(a.l);if(void 0===a.def||a.def.newBlockMarker||a.def.optionality||a.def.optionalQuantifier){t=!0;for(var o=0;r>=o;o++){var s=u(o).match;if(null!==s.fn&&void 0===n().validPositions[o]&&s.optionality!==!0&&s.optionalQuantifier!==!0||null===s.fn&&e[o]!==A(o,s)){t=!1;break}}}return t}function j(e,t){return ot?e-t>1||e-t===1&&i.insertMode:t-e>1||t-e===1&&i.insertMode}function M(e){var t=$._data(e).events,a=!1;$.each(t,function(e,t){$.each(t,function(e,t){if("inputmask"===t.namespace){var n=t.handler;t.handler=function(e){if(void 0===this.inputmask){var t=$.data(this,"_inputmask_opts");t?new Inputmask(t).mask(this):$(this).unbind(".inputmask")}else{if("setvalue"===e.type||!(this.disabled||this.readOnly&&!("keydown"===e.type&&e.ctrlKey&&67===e.keyCode||i.tabThrough===!1&&e.keyCode===Inputmask.keyCode.TAB))){switch(e.type){case"input":if(lt===!0||a===!0)return lt=!1,e.preventDefault();break;case"keydown":st=!1,a=!1;break;case"keypress":if(st===!0)return e.preventDefault();st=!0;break;case"compositionstart":a=!0;break;case"compositionupdate":lt=!0;break;case"compositionend":a=!1}return n.apply(this,arguments)}e.preventDefault()}}}})})}function F(e){function t(e){if($.valHooks&&void 0===$.valHooks[e]||$.valHooks[e].inputmaskpatch!==!0){var t=$.valHooks[e]&&$.valHooks[e].get?$.valHooks[e].get:function(e){return e.value},i=$.valHooks[e]&&$.valHooks[e].set?$.valHooks[e].set:function(e,t){return e.value=t,e};$.valHooks[e]={get:function(e){if(e.inputmask){if(e.inputmask.opts.autoUnmask)return e.inputmask.unmaskedvalue();var i=t(e),a=e.inputmask.maskset,n=a._buffer;return n=n?n.join(""):"",i!==n?i:""}return t(e)},set:function(e,t){var a,n=$(e);return a=i(e,t),e.inputmask&&n.triggerHandler("setvalue.inputmask"),a},inputmaskpatch:!0}}}function i(){return this.inputmask?this.inputmask.opts.autoUnmask?this.inputmask.unmaskedvalue():r.call(this)!==d().join("")?r.call(this):"":r.call(this)}function a(e){o.call(this,e),this.inputmask&&$(this).triggerHandler("setvalue.inputmask")}function n(e){$(e).bind("mouseenter.inputmask",function(){var e=$(this),t=this,i=t.inputmask._valueGet();""!==i&&i!==m().join("")&&e.triggerHandler("setvalue.inputmask")});var t=$._data(e).events,i=t.mouseover;if(i){for(var a=i[i.length-1],n=i.length-1;n>0;n--)i[n]=i[n-1];i[0]=a}}var r,o;e.inputmask.__valueGet||(Object.getOwnPropertyDescriptor&&void 0===e.value?(r=function(){return this.textContent},o=function(e){this.textContent=e},Object.defineProperty(e,"value",{get:i,set:a})):document.__lookupGetter__&&e.__lookupGetter__("value")?(r=e.__lookupGetter__("value"),o=e.__lookupSetter__("value"),e.__defineGetter__("value",i),e.__defineSetter__("value",a)):(r=function(){return e.value},o=function(t){e.value=t},t(e.type),n(e)),e.inputmask.__valueGet=r,e.inputmask._valueGet=function(e){return ot&&e!==!0?r.call(this.el).split("").reverse().join(""):r.call(this.el)},e.inputmask.__valueSet=o,e.inputmask._valueSet=function(e,t){o.call(this.el,t!==!0&&ot?e.split("").reverse().join(""):e)})}function D(e,t,a,s){function p(){if(i.keepStatic){r(!0);var t,a=[],s=$.extend(!0,{},n().validPositions);for(t=o();t>=0;t--){var l=n().validPositions[t];if(l&&(null!=l.match.fn&&a.push(l.input),delete n().validPositions[t],void 0!==l.alternation&&l.locator[l.alternation]===u(t).locator[l.alternation]))break}if(t>-1)for(;a.length>0;){n().p=b(o());var p=$.Event("keypress");p.which=a.pop().charCodeAt(0),G.call(e,p,!0,!1,!1,n().p)}else n().validPositions=$.extend(!0,{},s)}}if((i.numericInput||ot)&&(t===Inputmask.keyCode.BACKSPACE?t=Inputmask.keyCode.DELETE:t===Inputmask.keyCode.DELETE&&(t=Inputmask.keyCode.BACKSPACE),ot)){var c=a.end;a.end=a.begin,a.begin=c}t===Inputmask.keyCode.BACKSPACE&&(a.end-a.begin<1||i.insertMode===!1)?(a.begin=P(a.begin),void 0===n().validPositions[a.begin]||n().validPositions[a.begin].input!==i.groupSeparator&&n().validPositions[a.begin].input!==i.radixPoint||a.begin--):t===Inputmask.keyCode.DELETE&&a.begin===a.end&&(a.end=y(a.end)?a.end+1:b(a.end)+1,void 0===n().validPositions[a.begin]||n().validPositions[a.begin].input!==i.groupSeparator&&n().validPositions[a.begin].input!==i.radixPoint||a.end++),l(a.begin,a.end,!1,s),s!==!0&&p();var f=o(a.begin);f<a.begin?(-1===f&&r(),n().p=b(f)):s!==!0&&(n().p=a.begin)}function T(e){var t=this,a=$(t),r=e.keyCode,s=R(t);r===Inputmask.keyCode.BACKSPACE||r===Inputmask.keyCode.DELETE||iphone&&127===r||e.ctrlKey&&88===r&&!isInputEventSupported("cut")?(e.preventDefault(),88===r&&(X=m().join("")),D(t,r,s),w(t,m(),n().p,e,X!==m().join("")),t.inputmask._valueGet()===d().join("")?a.trigger("cleared"):C(m())===!0&&a.trigger("complete"),i.showTooltip&&a.prop("title",n().mask)):r===Inputmask.keyCode.END||r===Inputmask.keyCode.PAGE_DOWN?setTimeout(function(){var a=b(o());i.insertMode||a!==x()||e.shiftKey||a--,R(t,e.shiftKey?s.begin:a,a)},0):r===Inputmask.keyCode.HOME&&!e.shiftKey||r===Inputmask.keyCode.PAGE_UP?R(t,0,e.shiftKey?s.begin:0):(i.undoOnEscape&&r===Inputmask.keyCode.ESCAPE||90===r&&e.ctrlKey)&&e.altKey!==!0?(E(t,!0,!1,X.split("")),a.click()):r!==Inputmask.keyCode.INSERT||e.shiftKey||e.ctrlKey?i.tabThrough===!0&&r===Inputmask.keyCode.TAB?(e.shiftKey===!0?(null===p(s.begin).fn&&(s.begin=b(s.begin)),s.end=P(s.begin,!0),s.begin=P(s.end,!0)):(s.begin=b(s.begin,!0),s.end=b(s.begin,!0),s.end<x()&&s.end--),s.begin<x()&&(e.preventDefault(),R(t,s.begin,s.end))):i.insertMode!==!1||e.shiftKey||(r===Inputmask.keyCode.RIGHT?setTimeout(function(){var e=R(t);R(t,e.begin)},0):r===Inputmask.keyCode.LEFT&&setTimeout(function(){var e=R(t);R(t,ot?e.begin+1:e.begin-1)},0)):(i.insertMode=!i.insertMode,R(t,i.insertMode||s.begin!==x()?s.begin:s.begin-1)),i.onKeyDown.call(this,e,m(),R(t).begin,i),ut=-1!==$.inArray(r,i.ignorables)}function G(e,t,a,o,l){var u=this,p=$(u),c=e.which||e.charCode||e.keyCode;if(!(t===!0||e.ctrlKey&&e.altKey)&&(e.ctrlKey||e.metaKey||ut))return c===Inputmask.keyCode.ENTER&&X!==m().join("")&&setTimeout(function(){p.change(),X=m().join("")},0),!0;if(c){46===c&&e.shiftKey===!1&&","===i.radixPoint&&(c=44);var d,v=t?{begin:l,end:l}:R(u),g=String.fromCharCode(c),y=j(v.begin,v.end);y&&(n().undoPositions=$.extend(!0,{},n().validPositions),D(u,Inputmask.keyCode.DELETE,v,!0),v.begin=n().p,i.insertMode||(i.insertMode=!i.insertMode,s(v.begin,o),i.insertMode=!i.insertMode),y=!i.multi),n().writeOutBuffer=!0;var x=ot&&!y?v.end:v.begin,S=k(x,g,o);if(S!==!1){if(S!==!0&&(x=void 0!==S.pos?S.pos:x,g=void 0!==S.c?S.c:g),r(!0),void 0!==S.caret)d=S.caret;else{var A=n().validPositions;d=!i.keepStatic&&(void 0!==A[x+1]&&f(x+1,A[x].locator.slice(),x).length>1||void 0!==A[x].alternation)?x+1:b(x)}n().p=d}if(a!==!1){var E=this;if(setTimeout(function(){i.onKeyValidation.call(E,S,i)},0),n().writeOutBuffer&&S!==!1){var I=m();w(u,I,t?void 0:i.numericInput?P(d):d,e,t!==!0),t!==!0&&setTimeout(function(){C(I)===!0&&p.trigger("complete")},0)}else y&&(n().buffer=void 0,n().validPositions=n().undoPositions)}else y&&(n().buffer=void 0,n().validPositions=n().undoPositions);if(i.showTooltip&&p.prop("title",n().mask),t&&$.isFunction(i.onBeforeWrite)){var O=i.onBeforeWrite.call(this,e,m(),d,i);if(O&&O.refreshFromBuffer){var _=O.refreshFromBuffer;h(_===!0?_:_.start,_.end,O.buffer),r(!0),O.caret&&(n().p=O.caret)}}if(e.preventDefault(),t)return S}}function N(e){var t=this,a=$(t),n=t.inputmask._valueGet(!0),r=R(t);if("propertychange"===e.type&&t.inputmask._valueGet().length<=x())return!0;if("paste"===e.type){var o=n.substr(0,r.begin),s=n.substr(r.end,n.length);o===d().slice(0,r.begin).join("")&&(o=""),s===d().slice(r.end).join("")&&(s=""),window.clipboardData&&window.clipboardData.getData?n=o+window.clipboardData.getData("Text")+s:e.originalEvent&&e.originalEvent.clipboardData&&e.originalEvent.clipboardData.getData&&(n=o+e.originalEvent.clipboardData.getData("text/plain")+s)}var l=n;if($.isFunction(i.onBeforePaste)){if(l=i.onBeforePaste.call(t,n,i),l===!1)return e.preventDefault(),!1;l||(l=n)}return E(t,!1,!1,ot?l.split("").reverse():l.toString().split("")),w(t,m(),void 0,e,!0),a.click(),C(m())===!0&&a.trigger("complete"),!1}function B(e){var t=this;E(t,!0,!1),C(m())===!0&&$(t).trigger("complete"),e.preventDefault()}function H(e){var t=this;X=m().join(""),(""===tt||0!==e.originalEvent.data.indexOf(tt))&&(et=R(t))}function K(e){var t=this,a=R(t);0===e.originalEvent.data.indexOf(tt)&&(r(),a=et);var o=e.originalEvent.data;R(t,a.begin,a.end);for(var s=0;s<o.length;s++){var l=$.Event("keypress");l.which=o.charCodeAt(s),st=!1,ut=!1,G.call(t,l)}setTimeout(function(){var e=n().p;w(t,m(),i.numericInput?P(e):e)},0),tt=e.originalEvent.data}function L(){}function U(){var e=this,t=e.inputmask._valueGet();e.inputmask._valueSet($.isFunction(i.onBeforeMask)?i.onBeforeMask.call(e,t,i)||t:t),E(e,!0,!1),X=m().join(""),(i.clearMaskOnLostFocus||i.clearIncomplete)&&e.inputmask._valueGet()===d().join("")&&e.inputmask._valueSet("")}function q(){var e=this,t=e.inputmask._valueGet();i.showMaskOnFocus&&(!i.showMaskOnHover||i.showMaskOnHover&&""===t)?e.inputmask._valueGet()!==m().join("")&&w(e,m(),b(o())):pt===!1&&R(e,b(o())),i.positionCaretOnTab===!0&&setTimeout(function(){R(e,b(o()))},0),X=m().join("")}function Q(){var e=$(this),t=this;if(pt=!1,i.clearMaskOnLostFocus){var a=m().slice(),n=t.inputmask._valueGet();document.activeElement!==t&&n!==e.attr("placeholder")&&""!==n&&(-1===o()&&n===d().join("")?a=[]:_(a),w(t,a))}}function z(){function e(e){if(i.radixFocus&&""!==i.radixPoint){var t=n().validPositions;if(void 0===t[e]||t[e].input===A(e)){if(e<b(-1))return!0;var a=$.inArray(i.radixPoint,m());if(-1!==a){for(var r in t)if(r>a&&t[r].input!==A(r))return!1;return!0}}}return!1}var t=this;if(document.activeElement===t){var a=R(t);if(a.begin===a.end)if(e(a.begin))R(t,$.inArray(i.radixPoint,m()));else{var r=a.begin,s=b(o(r));s>r?R(t,y(r)||y(r-1)?r:b(r)):R(t,i.numericInput?0:s)}}}function W(){var e=this;setTimeout(function(){R(e,0,b(o()))},0)}function V(e){lt=!0;var t=this,a=$(t),r=R(t);if(ot){var o=window.clipboardData||e.originalEvent.clipboardData,s=o.getData("text").split("").reverse().join("");o.setData("text",s)}D(t,Inputmask.keyCode.DELETE,r),w(t,m(),n().p,e,X!==m().join("")),t.inputmask._valueGet()===d().join("")&&a.trigger("cleared"),i.showTooltip&&(t.title=n().mask)}function Y(e){var t=$(this),a=this;if(a.inputmask){var n=a.inputmask._valueGet(),s=m().slice();X!==s.join("")&&setTimeout(function(){t.change(),X=s.join("")},0),""!==n&&(i.clearMaskOnLostFocus&&(-1===o()&&n===d().join("")?s=[]:_(s)),C(s)===!1&&(setTimeout(function(){t.trigger("incomplete")
},0),i.clearIncomplete&&(r(),s=i.clearMaskOnLostFocus?[]:d().slice())),w(a,s,void 0,e))}}function Z(){var e=this;pt=!0,document.activeElement!==e&&i.showMaskOnHover&&e.inputmask._valueGet()!==m().join("")&&w(e,m())}function J(e){at=$(e),i.showTooltip&&at.prop("title",n().mask),("rtl"===e.dir||i.rightAlign)&&(e.style.textAlign="right"),("rtl"===e.dir||i.numericInput)&&(e.dir="ltr",e.removeAttribute("dir"),e.inputmask.isRTL=!0,ot=!0),at.unbind(".inputmask"),("INPUT"===e.tagName&&isInputTypeSupported(e.getAttribute("type"))||e.isContentEditable)&&(at.closest("form").bind("submit",function(){X!==m().join("")&&at.change(),i.clearMaskOnLostFocus&&-1===o()&&e.inputmask._valueGet&&e.inputmask._valueGet()===d().join("")&&e.inputmask._valueSet(""),i.removeMaskOnSubmit&&(e.inputmask._valueSet(e.inputmask.unmaskedvalue(),!0),setTimeout(function(){w(e,m())},0))}).bind("reset",function(){setTimeout(function(){at.triggerHandler("setvalue.inputmask")},0)}),at.bind("mouseenter.inputmask",Z).bind("blur.inputmask",Y).bind("focus.inputmask",q).bind("mouseleave.inputmask",Q).bind("click.inputmask",z).bind("dblclick.inputmask",W).bind(PasteEventType+".inputmask dragdrop.inputmask drop.inputmask",N).bind("cut.inputmask",V).bind("complete.inputmask",i.oncomplete).bind("incomplete.inputmask",i.onincomplete).bind("cleared.inputmask",i.oncleared).bind("keydown.inputmask",T).bind("keypress.inputmask",G),androidfirefox||at.bind("compositionstart.inputmask",H).bind("compositionupdate.inputmask",K).bind("compositionend.inputmask",L),"paste"===PasteEventType&&at.bind("input.inputmask",B)),at.bind("setvalue.inputmask",U),F(e);var t=$.isFunction(i.onBeforeMask)?i.onBeforeMask.call(e,e.inputmask._valueGet(),i)||e.inputmask._valueGet():e.inputmask._valueGet();E(e,!0,!1,t.split(""));var a=m().slice();X=a.join("");var s;try{s=document.activeElement}catch(l){}C(a)===!1&&i.clearIncomplete&&r(),i.clearMaskOnLostFocus&&(a.join("")===d().join("")?a=[]:_(a)),w(e,a),s===e&&R(e,b(o())),M(e)}var X,et,tt,it,at,nt,rt,ot=!1,st=!1,lt=!1,ut=!1,pt=!0;if(void 0!==e)switch(e.action){case"isComplete":return it=e.el,at=$(it),t=it.inputmask.maskset,i=it.inputmask.opts,C(e.buffer);case"unmaskedvalue":return it=e.el,void 0===it?(at=$({}),it=at[0],it.inputmask=new Inputmask,it.inputmask.opts=i,it.inputmask.el=it,it.inputmask.maskset=t,it.inputmask.isRTL=i.numericInput,i.numericInput&&(ot=!0),rt=($.isFunction(i.onBeforeMask)?i.onBeforeMask.call(at,e.value,i)||e.value:e.value).split(""),E(at,!1,!1,ot?rt.reverse():rt),$.isFunction(i.onBeforeWrite)&&i.onBeforeWrite.call(this,void 0,m(),0,i)):at=$(it),t=it.inputmask.maskset,i=it.inputmask.opts,ot=it.inputmask.isRTL,I(at);case"mask":X=m().join(""),J(e.el);break;case"format":return at=$({}),at[0].inputmask=new Inputmask,at[0].inputmask.opts=i,at[0].inputmask.el=at[0],at[0].inputmask.maskset=t,at[0].inputmask.isRTL=i.numericInput,i.numericInput&&(ot=!0),rt=($.isFunction(i.onBeforeMask)?i.onBeforeMask.call(at,e.value,i)||e.value:e.value).split(""),E(at,!1,!1,ot?rt.reverse():rt),$.isFunction(i.onBeforeWrite)&&i.onBeforeWrite.call(this,void 0,m(),0,i),e.metadata?{value:ot?m().slice().reverse().join(""):m().join(""),metadata:at.inputmask("getmetadata")}:ot?m().slice().reverse().join(""):m().join("");case"isValid":at=$({}),at[0].inputmask=new Inputmask,at[0].inputmask.opts=i,at[0].inputmask.el=at[0],at[0].inputmask.maskset=t,at[0].inputmask.isRTL=i.numericInput,i.numericInput&&(ot=!0),rt=e.value.split(""),E(at,!1,!0,ot?rt.reverse():rt);for(var ct=m(),ft=O(),dt=ct.length-1;dt>ft&&!y(dt);dt--);return ct.splice(ft,dt+1-ft),C(ct)&&e.value===ct.join("");case"getemptymask":return it=e.el,at=$(it),t=it.inputmask.maskset,i=it.inputmask.opts,d();case"remove":it=e.el,at=$(it),t=it.inputmask.maskset,i=it.inputmask.opts,it.inputmask._valueSet(I(at)),at.unbind(".inputmask");var mt;Object.getOwnPropertyDescriptor&&(mt=Object.getOwnPropertyDescriptor(it,"value")),mt&&mt.get?it.inputmask.__valueGet&&Object.defineProperty(it,"value",{get:it.inputmask.__valueGet,set:it.inputmask.__valueSet}):document.__lookupGetter__&&it.__lookupGetter__("value")&&it.inputmask.__valueGet&&(it.__defineGetter__("value",it.inputmask.__valueGet),it.__defineSetter__("value",it.inputmask.__valueSet)),it.inputmask=void 0;break;case"getmetadata":if(it=e.el,at=$(it),t=it.inputmask.maskset,i=it.inputmask.opts,$.isArray(t.metadata)){for(var ht,vt=o(),gt=vt;gt>=0;gt--)if(n().validPositions[gt]&&void 0!==n().validPositions[gt].alternation){ht=n().validPositions[gt].alternation;break}return void 0!==ht?t.metadata[n().validPositions[vt].locator[ht]]:t.metadata[0]}return t.metadata}}Inputmask.prototype={defaults:{placeholder:"_",optionalmarker:{start:"[",end:"]"},quantifiermarker:{start:"{",end:"}"},groupmarker:{start:"(",end:")"},alternatormarker:"|",escapeChar:"\\",mask:null,oncomplete:$.noop,onincomplete:$.noop,oncleared:$.noop,repeat:0,greedy:!0,autoUnmask:!1,removeMaskOnSubmit:!1,clearMaskOnLostFocus:!0,insertMode:!0,clearIncomplete:!1,aliases:{},alias:null,onKeyDown:$.noop,onBeforeMask:null,onBeforePaste:null,onBeforeWrite:null,onUnMask:null,showMaskOnFocus:!0,showMaskOnHover:!0,onKeyValidation:$.noop,skipOptionalPartCharacter:" ",showTooltip:!1,numericInput:!1,rightAlign:!1,undoOnEscape:!0,radixPoint:"",groupSeparator:"",radixFocus:!1,nojumps:!1,nojumpsThreshold:0,keepStatic:null,positionCaretOnTab:!1,tabThrough:!1,supportsInputType:[],definitions:{9:{validator:"[0-9]",cardinality:1,definitionSymbol:"*"},a:{validator:"[A-Za-zА-яЁёÀ-ÿµ]",cardinality:1,definitionSymbol:"*"},"*":{validator:"[0-9A-Za-zА-яЁёÀ-ÿµ]",cardinality:1}},ignorables:[8,9,13,19,27,33,34,35,36,37,38,39,40,45,46,93,112,113,114,115,116,117,118,119,120,121,122,123],isComplete:null,canClearPosition:$.noop,postValidation:null},masksCache:{},mask:function(e){var t=$.extend(!0,{},this.opts);importAttributeOptions(e,t,$.extend(!0,{},this.userOptions));var i=generateMaskSet(t,this.noMasksCache);return void 0!==i&&(e.inputmask=e.inputmask||new Inputmask,e.inputmask.opts=t,e.inputmask.noMasksCache=this.noMasksCache,e.inputmask.userOptions=$.extend(!0,{},this.userOptions),e.inputmask.el=e,e.inputmask.maskset=i,e.inputmask.isRTL=!1,$.data(e,"_inputmask_opts",t),maskScope({action:"mask",el:e},i,e.inputmask.opts)),e},option:function(e){return"string"==typeof e?this.opts[e]:"object"==typeof e?($.extend(this.opts,e),$.extend(this.userOptions,e),this):void 0},unmaskedvalue:function(){return this.el?maskScope({action:"unmaskedvalue",el:this.el}):void 0},remove:function(){return this.el?(maskScope({action:"remove",el:this.el}),this.el.inputmask=void 0,this.el):void 0},getemptymask:function(){return this.el?maskScope({action:"getemptymask",el:this.el}):void 0},hasMaskedValue:function(){return!this.opts.autoUnmask},isComplete:function(){return this.el?maskScope({action:"isComplete",buffer:this.el.inputmask._valueGet().split(""),el:this.el}):void 0},getmetadata:function(){return this.el?maskScope({action:"getmetadata",el:this.el}):void 0}},Inputmask.extendDefaults=function(e){$.extend(Inputmask.prototype.defaults,e)},Inputmask.extendDefinitions=function(e){$.extend(Inputmask.prototype.defaults.definitions,e)},Inputmask.extendAliases=function(e){$.extend(Inputmask.prototype.defaults.aliases,e)},Inputmask.format=function(e,t,i){var a=$.extend(!0,{},Inputmask.prototype.defaults,t);return resolveAlias(a.alias,t,a),maskScope({action:"format",value:e,metadata:i},generateMaskSet(a,t&&void 0!==t.definitions),a)},Inputmask.unmask=function(e,t){var i=$.extend(!0,{},Inputmask.prototype.defaults,t);return resolveAlias(i.alias,t,i),maskScope({action:"unmaskedvalue",value:e},generateMaskSet(i,t&&void 0!==t.definitions),i)},Inputmask.isValid=function(e,t){var i=$.extend(!0,{},Inputmask.prototype.defaults,t);return resolveAlias(i.alias,t,i),maskScope({action:"isValid",value:e},generateMaskSet(i,t&&void 0!==t.definitions),i)},Inputmask.escapeRegex=function(e){var t=["/",".","*","+","?","|","(",")","[","]","{","}","\\","$","^"];return e.replace(new RegExp("(\\"+t.join("|\\")+")","gim"),"\\$1")},Inputmask.keyCode={ALT:18,BACKSPACE:8,CAPS_LOCK:20,COMMA:188,COMMAND:91,COMMAND_LEFT:91,COMMAND_RIGHT:93,CONTROL:17,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,INSERT:45,LEFT:37,MENU:93,NUMPAD_ADD:107,NUMPAD_DECIMAL:110,NUMPAD_DIVIDE:111,NUMPAD_ENTER:108,NUMPAD_MULTIPLY:106,NUMPAD_SUBTRACT:109,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SHIFT:16,SPACE:32,TAB:9,UP:38,WINDOWS:91};var ua=navigator.userAgent,iphone=null!==ua.match(new RegExp("iphone","i")),androidchrome=null!==ua.match(new RegExp("android.*chrome.*","i")),androidfirefox=null!==ua.match(new RegExp("android.*firefox.*","i")),PasteEventType=isInputEventSupported("paste")?"paste":isInputEventSupported("input")?"input":"propertychange";return window.Inputmask=Inputmask,Inputmask}(jQuery),function(e,t){return void 0===e.fn.inputmask&&(e.fn.inputmask=function(i,a){var n,r;if(a=a||{},"string"==typeof i)switch(i){case"mask":return n=new t(a),this.each(function(){n.mask(this)});case"unmaskedvalue":return r=this.jquery&&this.length>0?this[0]:this,r.inputmask?r.inputmask.unmaskedvalue():e(r).val();case"remove":return this.each(function(){this.inputmask&&this.inputmask.remove()});case"getemptymask":return r=this.jquery&&this.length>0?this[0]:this,r.inputmask?r.inputmask.getemptymask():"";case"hasMaskedValue":return r=this.jquery&&this.length>0?this[0]:this,r.inputmask?r.inputmask.hasMaskedValue():!1;case"isComplete":return r=this.jquery&&this.length>0?this[0]:this,r.inputmask?r.inputmask.isComplete():!0;case"getmetadata":return r=this.jquery&&this.length>0?this[0]:this,r.inputmask?r.inputmask.getmetadata():void 0;case"setvalue":r=this.jquery&&this.length>0?this[0]:this,e(r).val(a),void 0!==r.inputmask&&e(r).triggerHandler("setvalue.inputmask");break;case"option":if("string"!=typeof a)return this.each(function(){return void 0!==this.inputmask?this.inputmask.option(a):void 0});if(r=this.jquery&&this.length>0?this[0]:this,void 0!==r.inputmask)return r.inputmask.option(a);break;default:return a.alias=i,n=new t(a),this.each(function(){n.mask(this)})}else{if("object"==typeof i)return n=new t(i),this.each(void 0===i.mask&&void 0===i.alias?function(){return void 0!==this.inputmask?this.inputmask.option(i):void n.mask(this)}:function(){n.mask(this)});if(void 0===i)return this.each(function(){n=new t(a),n.mask(this)})}}),e.fn.inputmask}(jQuery,Inputmask),function(e,t){return t.extendDefinitions({h:{validator:"[01][0-9]|2[0-3]",cardinality:2,prevalidator:[{validator:"[0-2]",cardinality:1}]},s:{validator:"[0-5][0-9]",cardinality:2,prevalidator:[{validator:"[0-5]",cardinality:1}]},d:{validator:"0[1-9]|[12][0-9]|3[01]",cardinality:2,prevalidator:[{validator:"[0-3]",cardinality:1}]},m:{validator:"0[1-9]|1[012]",cardinality:2,prevalidator:[{validator:"[01]",cardinality:1}]},y:{validator:"(19|20)\\d{2}",cardinality:4,prevalidator:[{validator:"[12]",cardinality:1},{validator:"(19|20)",cardinality:2},{validator:"(19|20)\\d",cardinality:3}]}}),t.extendAliases({"dd/mm/yyyy":{mask:"1/2/y",placeholder:"dd/mm/yyyy",regex:{val1pre:new RegExp("[0-3]"),val1:new RegExp("0[1-9]|[12][0-9]|3[01]"),val2pre:function(e){var i=t.escapeRegex.call(this,e);return new RegExp("((0[1-9]|[12][0-9]|3[01])"+i+"[01])")},val2:function(e){var i=t.escapeRegex.call(this,e);return new RegExp("((0[1-9]|[12][0-9])"+i+"(0[1-9]|1[012]))|(30"+i+"(0[13-9]|1[012]))|(31"+i+"(0[13578]|1[02]))")}},leapday:"29/02/",separator:"/",yearrange:{minyear:1900,maxyear:2099},isInYearRange:function(e,t,i){if(isNaN(e))return!1;var a=parseInt(e.concat(t.toString().slice(e.length))),n=parseInt(e.concat(i.toString().slice(e.length)));return(isNaN(a)?!1:a>=t&&i>=a)||(isNaN(n)?!1:n>=t&&i>=n)},determinebaseyear:function(e,t,i){var a=(new Date).getFullYear();if(e>a)return e;if(a>t){for(var n=t.toString().slice(0,2),r=t.toString().slice(2,4);n+i>t;)n--;var o=n+r;return e>o?e:o}return a},onKeyDown:function(i){var a=e(this);if(i.ctrlKey&&i.keyCode===t.keyCode.RIGHT){var n=new Date;a.val(n.getDate().toString()+(n.getMonth()+1).toString()+n.getFullYear().toString()),a.triggerHandler("setvalue.inputmask")}},getFrontValue:function(e,t,i){for(var a=0,n=0,r=0;r<e.length&&"2"!==e.charAt(r);r++){var o=i.definitions[e.charAt(r)];o?(a+=n,n=o.cardinality):n++}return t.join("").substr(a,n)},definitions:{1:{validator:function(e,t,i,a,n){var r=n.regex.val1.test(e);return a||r||e.charAt(1)!==n.separator&&-1==="-./".indexOf(e.charAt(1))||!(r=n.regex.val1.test("0"+e.charAt(0)))?r:(t.buffer[i-1]="0",{refreshFromBuffer:{start:i-1,end:i},pos:i,c:e.charAt(0)})},cardinality:2,prevalidator:[{validator:function(e,t,i,a,n){var r=e;isNaN(t.buffer[i+1])||(r+=t.buffer[i+1]);var o=1===r.length?n.regex.val1pre.test(r):n.regex.val1.test(r);if(!a&&!o){if(o=n.regex.val1.test(e+"0"))return t.buffer[i]=e,t.buffer[++i]="0",{pos:i,c:"0"};if(o=n.regex.val1.test("0"+e))return t.buffer[i]="0",i++,{pos:i}}return o},cardinality:1}]},2:{validator:function(e,t,i,a,n){var r=n.getFrontValue(t.mask,t.buffer,n);-1!==r.indexOf(n.placeholder[0])&&(r="01"+n.separator);var o=n.regex.val2(n.separator).test(r+e);if(!a&&!o&&(e.charAt(1)===n.separator||-1!=="-./".indexOf(e.charAt(1)))&&(o=n.regex.val2(n.separator).test(r+"0"+e.charAt(0))))return t.buffer[i-1]="0",{refreshFromBuffer:{start:i-1,end:i},pos:i,c:e.charAt(0)};if(n.mask.indexOf("2")===n.mask.length-1&&o){var s=t.buffer.join("").substr(4,4)+e;if(s!==n.leapday)return!0;var l=parseInt(t.buffer.join("").substr(0,4),10);return l%4===0?l%100===0?l%400===0?!0:!1:!0:!1}return o},cardinality:2,prevalidator:[{validator:function(e,t,i,a,n){isNaN(t.buffer[i+1])||(e+=t.buffer[i+1]);var r=n.getFrontValue(t.mask,t.buffer,n);-1!==r.indexOf(n.placeholder[0])&&(r="01"+n.separator);var o=1===e.length?n.regex.val2pre(n.separator).test(r+e):n.regex.val2(n.separator).test(r+e);return a||o||!(o=n.regex.val2(n.separator).test(r+"0"+e))?o:(t.buffer[i]="0",i++,{pos:i})},cardinality:1}]},y:{validator:function(e,t,i,a,n){if(n.isInYearRange(e,n.yearrange.minyear,n.yearrange.maxyear)){var r=t.buffer.join("").substr(0,6);if(r!==n.leapday)return!0;var o=parseInt(e,10);return o%4===0?o%100===0?o%400===0?!0:!1:!0:!1}return!1},cardinality:4,prevalidator:[{validator:function(e,t,i,a,n){var r=n.isInYearRange(e,n.yearrange.minyear,n.yearrange.maxyear);if(!a&&!r){var o=n.determinebaseyear(n.yearrange.minyear,n.yearrange.maxyear,e+"0").toString().slice(0,1);if(r=n.isInYearRange(o+e,n.yearrange.minyear,n.yearrange.maxyear))return t.buffer[i++]=o.charAt(0),{pos:i};if(o=n.determinebaseyear(n.yearrange.minyear,n.yearrange.maxyear,e+"0").toString().slice(0,2),r=n.isInYearRange(o+e,n.yearrange.minyear,n.yearrange.maxyear))return t.buffer[i++]=o.charAt(0),t.buffer[i++]=o.charAt(1),{pos:i}}return r},cardinality:1},{validator:function(e,t,i,a,n){var r=n.isInYearRange(e,n.yearrange.minyear,n.yearrange.maxyear);if(!a&&!r){var o=n.determinebaseyear(n.yearrange.minyear,n.yearrange.maxyear,e).toString().slice(0,2);if(r=n.isInYearRange(e[0]+o[1]+e[1],n.yearrange.minyear,n.yearrange.maxyear))return t.buffer[i++]=o.charAt(1),{pos:i};if(o=n.determinebaseyear(n.yearrange.minyear,n.yearrange.maxyear,e).toString().slice(0,2),n.isInYearRange(o+e,n.yearrange.minyear,n.yearrange.maxyear)){var s=t.buffer.join("").substr(0,6);if(s!==n.leapday)r=!0;else{var l=parseInt(e,10);r=l%4===0?l%100===0?l%400===0?!0:!1:!0:!1}}else r=!1;if(r)return t.buffer[i-1]=o.charAt(0),t.buffer[i++]=o.charAt(1),t.buffer[i++]=e.charAt(0),{refreshFromBuffer:{start:i-3,end:i},pos:i}}return r},cardinality:2},{validator:function(e,t,i,a,n){return n.isInYearRange(e,n.yearrange.minyear,n.yearrange.maxyear)},cardinality:3}]}},insertMode:!1,autoUnmask:!1},"mm/dd/yyyy":{placeholder:"mm/dd/yyyy",alias:"dd/mm/yyyy",regex:{val2pre:function(e){var i=t.escapeRegex.call(this,e);return new RegExp("((0[13-9]|1[012])"+i+"[0-3])|(02"+i+"[0-2])")},val2:function(e){var i=t.escapeRegex.call(this,e);return new RegExp("((0[1-9]|1[012])"+i+"(0[1-9]|[12][0-9]))|((0[13-9]|1[012])"+i+"30)|((0[13578]|1[02])"+i+"31)")},val1pre:new RegExp("[01]"),val1:new RegExp("0[1-9]|1[012]")},leapday:"02/29/",onKeyDown:function(i){var a=e(this);if(i.ctrlKey&&i.keyCode===t.keyCode.RIGHT){var n=new Date;a.val((n.getMonth()+1).toString()+n.getDate().toString()+n.getFullYear().toString()),a.triggerHandler("setvalue.inputmask")}}},"yyyy/mm/dd":{mask:"y/1/2",placeholder:"yyyy/mm/dd",alias:"mm/dd/yyyy",leapday:"/02/29",onKeyDown:function(i){var a=e(this);if(i.ctrlKey&&i.keyCode===t.keyCode.RIGHT){var n=new Date;a.val(n.getFullYear().toString()+(n.getMonth()+1).toString()+n.getDate().toString()),a.triggerHandler("setvalue.inputmask")}}},"dd.mm.yyyy":{mask:"1.2.y",placeholder:"dd.mm.yyyy",leapday:"29.02.",separator:".",alias:"dd/mm/yyyy"},"dd-mm-yyyy":{mask:"1-2-y",placeholder:"dd-mm-yyyy",leapday:"29-02-",separator:"-",alias:"dd/mm/yyyy"},"mm.dd.yyyy":{mask:"1.2.y",placeholder:"mm.dd.yyyy",leapday:"02.29.",separator:".",alias:"mm/dd/yyyy"},"mm-dd-yyyy":{mask:"1-2-y",placeholder:"mm-dd-yyyy",leapday:"02-29-",separator:"-",alias:"mm/dd/yyyy"},"yyyy.mm.dd":{mask:"y.1.2",placeholder:"yyyy.mm.dd",leapday:".02.29",separator:".",alias:"yyyy/mm/dd"},"yyyy-mm-dd":{mask:"y-1-2",placeholder:"yyyy-mm-dd",leapday:"-02-29",separator:"-",alias:"yyyy/mm/dd"},datetime:{mask:"1/2/y h:s",placeholder:"dd/mm/yyyy hh:mm",alias:"dd/mm/yyyy",regex:{hrspre:new RegExp("[012]"),hrs24:new RegExp("2[0-4]|1[3-9]"),hrs:new RegExp("[01][0-9]|2[0-4]"),ampm:new RegExp("^[a|p|A|P][m|M]"),mspre:new RegExp("[0-5]"),ms:new RegExp("[0-5][0-9]")},timeseparator:":",hourFormat:"24",definitions:{h:{validator:function(e,t,i,a,n){if("24"===n.hourFormat&&24===parseInt(e,10))return t.buffer[i-1]="0",t.buffer[i]="0",{refreshFromBuffer:{start:i-1,end:i},c:"0"};var r=n.regex.hrs.test(e);if(!a&&!r&&(e.charAt(1)===n.timeseparator||-1!=="-.:".indexOf(e.charAt(1)))&&(r=n.regex.hrs.test("0"+e.charAt(0))))return t.buffer[i-1]="0",t.buffer[i]=e.charAt(0),i++,{refreshFromBuffer:{start:i-2,end:i},pos:i,c:n.timeseparator};if(r&&"24"!==n.hourFormat&&n.regex.hrs24.test(e)){var o=parseInt(e,10);return 24===o?(t.buffer[i+5]="a",t.buffer[i+6]="m"):(t.buffer[i+5]="p",t.buffer[i+6]="m"),o-=12,10>o?(t.buffer[i]=o.toString(),t.buffer[i-1]="0"):(t.buffer[i]=o.toString().charAt(1),t.buffer[i-1]=o.toString().charAt(0)),{refreshFromBuffer:{start:i-1,end:i+6},c:t.buffer[i]}}return r},cardinality:2,prevalidator:[{validator:function(e,t,i,a,n){var r=n.regex.hrspre.test(e);return a||r||!(r=n.regex.hrs.test("0"+e))?r:(t.buffer[i]="0",i++,{pos:i})},cardinality:1}]},s:{validator:"[0-5][0-9]",cardinality:2,prevalidator:[{validator:function(e,t,i,a,n){var r=n.regex.mspre.test(e);return a||r||!(r=n.regex.ms.test("0"+e))?r:(t.buffer[i]="0",i++,{pos:i})},cardinality:1}]},t:{validator:function(e,t,i,a,n){return n.regex.ampm.test(e+"m")},casing:"lower",cardinality:1}},insertMode:!1,autoUnmask:!1},datetime12:{mask:"1/2/y h:s t\\m",placeholder:"dd/mm/yyyy hh:mm xm",alias:"datetime",hourFormat:"12"},"mm/dd/yyyy hh:mm xm":{mask:"1/2/y h:s t\\m",placeholder:"mm/dd/yyyy hh:mm xm",alias:"datetime12",regex:{val2pre:function(e){var i=t.escapeRegex.call(this,e);return new RegExp("((0[13-9]|1[012])"+i+"[0-3])|(02"+i+"[0-2])")},val2:function(e){var i=t.escapeRegex.call(this,e);return new RegExp("((0[1-9]|1[012])"+i+"(0[1-9]|[12][0-9]))|((0[13-9]|1[012])"+i+"30)|((0[13578]|1[02])"+i+"31)")},val1pre:new RegExp("[01]"),val1:new RegExp("0[1-9]|1[012]")},leapday:"02/29/",onKeyDown:function(i){var a=e(this);if(i.ctrlKey&&i.keyCode===t.keyCode.RIGHT){var n=new Date;a.val((n.getMonth()+1).toString()+n.getDate().toString()+n.getFullYear().toString()),a.triggerHandler("setvalue.inputmask")}}},"hh:mm t":{mask:"h:s t\\m",placeholder:"hh:mm xm",alias:"datetime",hourFormat:"12"},"h:s t":{mask:"h:s t\\m",placeholder:"hh:mm xm",alias:"datetime",hourFormat:"12"},"hh:mm:ss":{mask:"h:s:s",placeholder:"hh:mm:ss",alias:"datetime",autoUnmask:!1},"hh:mm":{mask:"h:s",placeholder:"hh:mm",alias:"datetime",autoUnmask:!1},date:{alias:"dd/mm/yyyy"},"mm/yyyy":{mask:"1/y",placeholder:"mm/yyyy",leapday:"donotuse",separator:"/",alias:"mm/dd/yyyy"},shamsi:{regex:{val2pre:function(e){var i=t.escapeRegex.call(this,e);return new RegExp("((0[1-9]|1[012])"+i+"[0-3])")},val2:function(e){var i=t.escapeRegex.call(this,e);return new RegExp("((0[1-9]|1[012])"+i+"(0[1-9]|[12][0-9]))|((0[1-9]|1[012])"+i+"30)|((0[1-6])"+i+"31)")},val1pre:new RegExp("[01]"),val1:new RegExp("0[1-9]|1[012]")},yearrange:{minyear:1300,maxyear:1499},mask:"y/1/2",leapday:"/12/30",placeholder:"yyyy/mm/dd",alias:"mm/dd/yyyy",clearIncomplete:!0}}),t}(jQuery,Inputmask),function(e,t){return t.extendDefinitions({A:{validator:"[A-Za-zА-яЁёÀ-ÿµ]",cardinality:1,casing:"upper"},"&":{validator:"[0-9A-Za-zА-яЁёÀ-ÿµ]",cardinality:1,casing:"upper"},"#":{validator:"[0-9A-Fa-f]",cardinality:1,casing:"upper"}}),t.extendAliases({url:{mask:"ir",placeholder:"",separator:"",defaultPrefix:"http://",regex:{urlpre1:new RegExp("[fh]"),urlpre2:new RegExp("(ft|ht)"),urlpre3:new RegExp("(ftp|htt)"),urlpre4:new RegExp("(ftp:|http|ftps)"),urlpre5:new RegExp("(ftp:/|ftps:|http:|https)"),urlpre6:new RegExp("(ftp://|ftps:/|http:/|https:)"),urlpre7:new RegExp("(ftp://|ftps://|http://|https:/)"),urlpre8:new RegExp("(ftp://|ftps://|http://|https://)")},definitions:{i:{validator:function(){return!0},cardinality:8,prevalidator:function(){for(var e=[],t=8,i=0;t>i;i++)e[i]=function(){var e=i;return{validator:function(t,i,a,n,r){if(r.regex["urlpre"+(e+1)]){var o,s=t;e+1-t.length>0&&(s=i.buffer.join("").substring(0,e+1-t.length)+""+s);var l=r.regex["urlpre"+(e+1)].test(s);if(!n&&!l){for(a-=e,o=0;o<r.defaultPrefix.length;o++)i.buffer[a]=r.defaultPrefix[o],a++;for(o=0;o<s.length-1;o++)i.buffer[a]=s[o],a++;return{pos:a}}return l}return!1},cardinality:e}}();return e}()},r:{validator:".",cardinality:50}},insertMode:!1,autoUnmask:!1},ip:{mask:"i[i[i]].i[i[i]].i[i[i]].i[i[i]]",definitions:{i:{validator:function(e,t,i){return i-1>-1&&"."!==t.buffer[i-1]?(e=t.buffer[i-1]+e,e=i-2>-1&&"."!==t.buffer[i-2]?t.buffer[i-2]+e:"0"+e):e="00"+e,new RegExp("25[0-5]|2[0-4][0-9]|[01][0-9][0-9]").test(e)},cardinality:1}}},email:{mask:"*{1,64}[.*{1,64}][.*{1,64}][.*{1,64}]@*{1,64}[.*{2,64}][.*{2,6}][.*{1,2}]",greedy:!1,onBeforePaste:function(e){return e=e.toLowerCase(),e.replace("mailto:","")},definitions:{"*":{validator:"[0-9A-Za-z!#$%&'*+/=?^_`{|}~-]",cardinality:1,casing:"lower"}}},mac:{mask:"##:##:##:##:##:##"}}),t}(jQuery,Inputmask),function(e,t){return t.extendAliases({numeric:{mask:function(e){function t(t){for(var i="",a=0;a<t.length;a++)i+=e.definitions[t.charAt(a)]?"\\"+t.charAt(a):t.charAt(a);return i}if(0!==e.repeat&&isNaN(e.integerDigits)&&(e.integerDigits=e.repeat),e.repeat=0,e.groupSeparator===e.radixPoint&&(e.groupSeparator="."===e.radixPoint?",":","===e.radixPoint?".":"")," "===e.groupSeparator&&(e.skipOptionalPartCharacter=void 0),e.autoGroup=e.autoGroup&&""!==e.groupSeparator,e.autoGroup&&("string"==typeof e.groupSize&&isFinite(e.groupSize)&&(e.groupSize=parseInt(e.groupSize)),isFinite(e.integerDigits))){var i=Math.floor(e.integerDigits/e.groupSize),a=e.integerDigits%e.groupSize;e.integerDigits=parseInt(e.integerDigits)+(0===a?i-1:i),e.integerDigits<1&&(e.integerDigits="*")}e.placeholder.length>1&&(e.placeholder=e.placeholder.charAt(0)),e.radixFocus=e.radixFocus&&""!==e.placeholder&&e.integerOptional===!0,e.definitions[";"]=e.definitions["~"],e.definitions[";"].definitionSymbol="~",e.numericInput===!0&&(e.radixFocus=!1,e.digitsOptional=!1,isNaN(e.digits)&&(e.digits=2),e.decimalProtect=!1);var n=t(e.prefix);return n+="[+]",n+=e.integerOptional===!0?"~{1,"+e.integerDigits+"}":"~{"+e.integerDigits+"}",void 0!==e.digits&&(isNaN(e.digits)||parseInt(e.digits)>0)&&(n+=e.digitsOptional?"["+(e.decimalProtect?":":e.radixPoint)+";{1,"+e.digits+"}]":(e.decimalProtect?":":e.radixPoint)+";{"+e.digits+"}"),""!==e.negationSymbol.back&&(n+="[-]"),n+=t(e.suffix),e.greedy=!1,n},placeholder:"",greedy:!1,digits:"*",digitsOptional:!0,radixPoint:".",radixFocus:!0,groupSize:3,groupSeparator:"",autoGroup:!1,allowPlus:!0,allowMinus:!0,negationSymbol:{front:"-",back:""},integerDigits:"+",integerOptional:!0,prefix:"",suffix:"",rightAlign:!0,decimalProtect:!0,min:null,max:null,step:1,insertMode:!0,autoUnmask:!1,unmaskAsNumber:!1,postFormat:function(i,a,n,r){r.numericInput===!0&&(i=i.reverse(),isFinite(a)&&(a=i.join("").length-a-1));var o,s,l=!1;i.length>=r.suffix.length&&i.join("").indexOf(r.suffix)===i.length-r.suffix.length&&(i.length=i.length-r.suffix.length,l=!0),a=a>=i.length?i.length-1:a<r.prefix.length?r.prefix.length:a;var u=!1,p=i[a];if(""===r.groupSeparator||r.numericInput!==!0&&-1!==e.inArray(r.radixPoint,i)&&a>e.inArray(r.radixPoint,i)||new RegExp("["+t.escapeRegex(r.negationSymbol.front)+"+]").test(p)){if(l)for(o=0,s=r.suffix.length;s>o;o++)i.push(r.suffix.charAt(o));return{pos:a}}var c=i.slice();p===r.groupSeparator&&(c.splice(a--,1),p=c[a]),n?p!==r.radixPoint&&(c[a]="?"):c.splice(a,0,"?");var f=c.join(""),d=f;if(f.length>0&&r.autoGroup||n&&-1!==f.indexOf(r.groupSeparator)){var m=t.escapeRegex(r.groupSeparator);u=0===f.indexOf(r.groupSeparator),f=f.replace(new RegExp(m,"g"),"");var h=f.split(r.radixPoint);if(f=""===r.radixPoint?f:h[0],f!==r.prefix+"?0"&&f.length>=r.groupSize+r.prefix.length)for(var v=new RegExp("([-+]?[\\d?]+)([\\d?]{"+r.groupSize+"})");v.test(f);)f=f.replace(v,"$1"+r.groupSeparator+"$2"),f=f.replace(r.groupSeparator+r.groupSeparator,r.groupSeparator);""!==r.radixPoint&&h.length>1&&(f+=r.radixPoint+h[1])}for(u=d!==f,i.length=f.length,o=0,s=f.length;s>o;o++)i[o]=f.charAt(o);var g=e.inArray("?",i);if(-1===g&&p===r.radixPoint&&(g=e.inArray(r.radixPoint,i)),n?i[g]=p:i.splice(g,1),!u&&l)for(o=0,s=r.suffix.length;s>o;o++)i.push(r.suffix.charAt(o));return g=r.numericInput&&isFinite(a)?i.join("").length-g-1:g,r.numericInput&&(i=i.reverse(),e.inArray(r.radixPoint,i)<g&&i.join("").length-r.suffix.length!==g&&(g-=1)),{pos:g,refreshFromBuffer:u,buffer:i}},onBeforeWrite:function(i,a,n,r){if(i&&("blur"===i.type||"checkval"===i.type)){var o=a.join(""),s=o.replace(r.prefix,"");if(s=s.replace(r.suffix,""),s=s.replace(new RegExp(t.escapeRegex(r.groupSeparator),"g"),""),","===r.radixPoint&&(s=s.replace(t.escapeRegex(r.radixPoint),".")),isFinite(s)&&isFinite(r.min)&&parseFloat(s)<parseFloat(r.min))return e.extend(!0,{refreshFromBuffer:!0,buffer:(r.prefix+r.min).split("")},r.postFormat((r.prefix+r.min).split(""),0,!0,r));if(r.numericInput!==!0){var l=""!==r.radixPoint?a.join("").split(r.radixPoint):[a.join("")],u=l[0].match(r.regex.integerPart(r)),p=2===l.length?l[1].match(r.regex.integerNPart(r)):void 0;if(u){u[0]!==r.negationSymbol.front+"0"&&u[0]!==r.negationSymbol.front&&"+"!==u[0]||void 0!==p&&!p[0].match(/^0+$/)||a.splice(u.index,1);var c=e.inArray(r.radixPoint,a);if(-1!==c){if(isFinite(r.digits)&&!r.digitsOptional){for(var f=1;f<=r.digits;f++)(void 0===a[c+f]||a[c+f]===r.placeholder.charAt(0))&&(a[c+f]="0");return{refreshFromBuffer:o!==a.join(""),buffer:a}}if(c===a.length-r.suffix.length-1)return a.splice(c,1),{refreshFromBuffer:!0,buffer:a}}}}}if(r.autoGroup){var d=r.postFormat(a,r.numericInput?n:n-1,!0,r);return d.caret=n<=r.prefix.length?d.pos:d.pos+1,d}},regex:{integerPart:function(e){return new RegExp("["+t.escapeRegex(e.negationSymbol.front)+"+]?\\d+")},integerNPart:function(e){return new RegExp("[\\d"+t.escapeRegex(e.groupSeparator)+"]+")}},signHandler:function(e,t,i,a,n){if(!a&&n.allowMinus&&"-"===e||n.allowPlus&&"+"===e){var r=t.buffer.join("").match(n.regex.integerPart(n));if(r&&r[0].length>0)return t.buffer[r.index]===("-"===e?"+":n.negationSymbol.front)?"-"===e?""!==n.negationSymbol.back?{pos:r.index,c:n.negationSymbol.front,remove:r.index,caret:i,insert:{pos:t.buffer.length-n.suffix.length-1,c:n.negationSymbol.back}}:{pos:r.index,c:n.negationSymbol.front,remove:r.index,caret:i}:""!==n.negationSymbol.back?{pos:r.index,c:"+",remove:[r.index,t.buffer.length-n.suffix.length-1],caret:i}:{pos:r.index,c:"+",remove:r.index,caret:i}:t.buffer[r.index]===("-"===e?n.negationSymbol.front:"+")?"-"===e&&""!==n.negationSymbol.back?{remove:[r.index,t.buffer.length-n.suffix.length-1],caret:i-1}:{remove:r.index,caret:i-1}:"-"===e?""!==n.negationSymbol.back?{pos:r.index,c:n.negationSymbol.front,caret:i+1,insert:{pos:t.buffer.length-n.suffix.length,c:n.negationSymbol.back}}:{pos:r.index,c:n.negationSymbol.front,caret:i+1}:{pos:r.index,c:e,caret:i+1}}return!1},radixHandler:function(t,i,a,n,r){if(!n&&(-1!==e.inArray(t,[",","."])&&(t=r.radixPoint),t===r.radixPoint&&void 0!==r.digits&&(isNaN(r.digits)||parseInt(r.digits)>0))){var o=e.inArray(r.radixPoint,i.buffer),s=i.buffer.join("").match(r.regex.integerPart(r));if(-1!==o&&i.validPositions[o])return i.validPositions[o-1]?{caret:o+1}:{pos:s.index,c:s[0],caret:o+1};if(!s||"0"===s[0]&&s.index+1!==a)return i.buffer[s?s.index:a]="0",{pos:(s?s.index:a)+1,c:r.radixPoint}}return!1},leadingZeroHandler:function(t,i,a,n,r){if(r.numericInput===!0){if("0"===i.buffer[i.buffer.length-r.prefix.length-1])return{pos:a,remove:i.buffer.length-r.prefix.length-1}}else{var o=i.buffer.join("").match(r.regex.integerNPart(r)),s=e.inArray(r.radixPoint,i.buffer);if(o&&!n&&(-1===s||s>=a))if(0===o[0].indexOf("0")){a<r.prefix.length&&(a=o.index);var l=e.inArray(r.radixPoint,i._buffer),u=i._buffer&&i.buffer.slice(s).join("")===i._buffer.slice(l).join("")||0===parseInt(i.buffer.slice(s+1).join("")),p=i._buffer&&i.buffer.slice(o.index,s).join("")===i._buffer.slice(r.prefix.length,l).join("")||"0"===i.buffer.slice(o.index,s).join("");if(-1===s||u&&p)return i.buffer.splice(o.index,1),a=a>o.index?a-1:o.index,{pos:a,remove:o.index};if(o.index+1===a||"0"===t)return i.buffer.splice(o.index,1),a=o.index,{pos:a,remove:o.index}}else if("0"===t&&a<=o.index&&o[0]!==r.groupSeparator)return!1}return!0},postValidation:function(i,a){var n=!0,r=i.join(""),o=r.replace(a.prefix,"");return o=o.replace(a.suffix,""),o=o.replace(new RegExp(t.escapeRegex(a.groupSeparator),"g"),""),","===a.radixPoint&&(o=o.replace(t.escapeRegex(a.radixPoint),".")),o=o.replace(new RegExp("^"+t.escapeRegex(a.negationSymbol.front)),"-"),o=o.replace(new RegExp(t.escapeRegex(a.negationSymbol.back)+"$"),""),o=o===a.negationSymbol.front?o+"0":o,isFinite(o)&&(null!==a.max&&isFinite(a.max)&&(n=parseFloat(o)<=parseFloat(a.max)),n&&null!==a.min&&isFinite(a.min)&&(0>=o||o.toString().length>=a.min.toString().length)&&(n=parseFloat(o)>=parseFloat(a.min),n||(n=e.extend(!0,{refreshFromBuffer:!0,buffer:(a.prefix+a.min).split("")},a.postFormat((a.prefix+a.min).split(""),0,!0,a)),n.refreshFromBuffer=!0))),n},definitions:{"~":{validator:function(i,a,n,r,o){var s=o.signHandler(i,a,n,r,o);if(!s&&(s=o.radixHandler(i,a,n,r,o),!s&&(s=r?new RegExp("[0-9"+t.escapeRegex(o.groupSeparator)+"]").test(i):new RegExp("[0-9]").test(i),s===!0&&(s=o.leadingZeroHandler(i,a,n,r,o),s===!0)))){var l=e.inArray(o.radixPoint,a.buffer);s=-1!==l&&o.digitsOptional===!1&&o.numericInput!==!0&&n>l&&!r?{pos:n,remove:n}:{pos:n}}return s},cardinality:1,prevalidator:null},"+":{validator:function(e,t,i,a,n){var r=n.signHandler(e,t,i,a,n);return!r&&(a&&n.allowMinus&&e===n.negationSymbol.front||n.allowMinus&&"-"===e||n.allowPlus&&"+"===e)&&(r="-"===e?""!==n.negationSymbol.back?{pos:i,c:"-"===e?n.negationSymbol.front:"+",caret:i+1,insert:{pos:t.buffer.length,c:n.negationSymbol.back}}:{pos:i,c:"-"===e?n.negationSymbol.front:"+",caret:i+1}:!0),r},cardinality:1,prevalidator:null,placeholder:""},"-":{validator:function(e,t,i,a,n){var r=n.signHandler(e,t,i,a,n);return!r&&a&&n.allowMinus&&e===n.negationSymbol.back&&(r=!0),r},cardinality:1,prevalidator:null,placeholder:""},":":{validator:function(e,i,a,n,r){var o=r.signHandler(e,i,a,n,r);if(!o){var s="["+t.escapeRegex(r.radixPoint)+",\\.]";o=new RegExp(s).test(e),o&&i.validPositions[a]&&i.validPositions[a].match.placeholder===r.radixPoint&&(o={caret:a+1})}return o?{c:r.radixPoint}:o},cardinality:1,prevalidator:null,placeholder:function(e){return e.radixPoint}}},onUnMask:function(e,i,a){var n=e.replace(a.prefix,"");return n=n.replace(a.suffix,""),n=n.replace(new RegExp(t.escapeRegex(a.groupSeparator),"g"),""),a.unmaskAsNumber?(n=n.replace(t.escapeRegex.call(this,a.radixPoint),"."),Number(n)):n
},isComplete:function(e,i){var a=e.join(""),n=e.slice();if(i.postFormat(n,0,!0,i),n.join("")!==a)return!1;var r=a.replace(i.prefix,"");return r=r.replace(i.suffix,""),r=r.replace(new RegExp(t.escapeRegex(i.groupSeparator),"g"),""),","===i.radixPoint&&(r=r.replace(t.escapeRegex(i.radixPoint),".")),isFinite(r)},onBeforeMask:function(e,i){if(""!==i.radixPoint&&isFinite(e))e=e.toString().replace(".",i.radixPoint);else{var a=e.match(/,/g),n=e.match(/\./g);n&&a?n.length>a.length?(e=e.replace(/\./g,""),e=e.replace(",",i.radixPoint)):a.length>n.length?(e=e.replace(/,/g,""),e=e.replace(".",i.radixPoint)):e=e.indexOf(".")<e.indexOf(",")?e.replace(/\./g,""):e=e.replace(/,/g,""):e=e.replace(new RegExp(t.escapeRegex(i.groupSeparator),"g"),"")}if(0===i.digits&&(-1!==e.indexOf(".")?e=e.substring(0,e.indexOf(".")):-1!==e.indexOf(",")&&(e=e.substring(0,e.indexOf(",")))),""!==i.radixPoint&&isFinite(i.digits)&&-1!==e.indexOf(i.radixPoint)){var r=e.split(i.radixPoint),o=r[1].match(new RegExp("\\d*"))[0];if(parseInt(i.digits)<o.toString().length){var s=Math.pow(10,parseInt(i.digits));e=e.replace(t.escapeRegex(i.radixPoint),"."),e=Math.round(parseFloat(e)*s)/s,e=e.toString().replace(".",i.radixPoint)}}return e.toString()},onBeforePaste:function(e,t){return t.onBeforeMask(e,t)},canClearPosition:function(i,a,n,r,o){var s=i.validPositions[a].input,l=s!==o.radixPoint||null!==i.validPositions[a].match.fn&&o.decimalProtect===!1||isFinite(s)||a===n||s===o.groupSeparator||s===o.negationSymbol.front||s===o.negationSymbol.back;if(l&&isFinite(s)){var u,p=e.inArray(o.radixPoint,i.buffer),c=!1;if(void 0===i.validPositions[p]&&(i.validPositions[p]={input:o.radixPoint},c=!0),!r&&i.buffer){u=i.buffer.join("").substr(0,a).match(o.regex.integerNPart(o));var f=a+1,d=null==u||0===parseInt(u[0].replace(new RegExp(t.escapeRegex(o.groupSeparator),"g"),""));if(d)for(;i.validPositions[f]&&(i.validPositions[f].input===o.groupSeparator||"0"===i.validPositions[f].input);)delete i.validPositions[f],f++}var m=[];for(var h in i.validPositions)void 0!==i.validPositions[h].input&&m.push(i.validPositions[h].input);if(c&&delete i.validPositions[p],p>0){var v=m.join("");if(u=v.match(o.regex.integerNPart(o)),u&&p>=a)if(0===u[0].indexOf("0"))l=u.index!==a||"0"===o.placeholder;else{var g=parseInt(u[0].replace(new RegExp(t.escapeRegex(o.groupSeparator),"g"),"")),k=parseInt(v.split(o.radixPoint)[1]);10>g&&i.validPositions[a]&&("0"!==o.placeholder||k>0)&&(i.validPositions[a].input="0",i.p=o.prefix.length+1,l=!1)}}}return l},onKeyDown:function(i,a,n,r){var o=e(this);if(i.ctrlKey)switch(i.keyCode){case t.keyCode.UP:o.val(parseFloat(this.inputmask.unmaskedvalue())+parseInt(r.step)),o.triggerHandler("setvalue.inputmask");break;case t.keyCode.DOWN:o.val(parseFloat(this.inputmask.unmaskedvalue())-parseInt(r.step)),o.triggerHandler("setvalue.inputmask")}}},currency:{prefix:"$ ",groupSeparator:",",alias:"numeric",placeholder:"0",autoGroup:!0,digits:2,digitsOptional:!1,clearMaskOnLostFocus:!1},decimal:{alias:"numeric"},integer:{alias:"numeric",digits:0,radixPoint:""},percentage:{alias:"numeric",digits:2,radixPoint:".",placeholder:"0",autoGroup:!1,min:0,max:100,suffix:" %",allowPlus:!1,allowMinus:!1}}),t}(jQuery,Inputmask),function(e,t){return t.extendAliases({phone:{url:"phone-codes/phone-codes.js",countrycode:"",mask:function(t){t.definitions["#"]=t.definitions[9];var i=[];return e.ajax({url:t.url,async:!1,dataType:"json",success:function(e){i=e},error:function(e,i,a){alert(a+" - "+t.url)}}),i=i.sort(function(e,t){return(e.mask||e)<(t.mask||t)?-1:1})},keepStatic:!1,nojumps:!0,nojumpsThreshold:1,onBeforeMask:function(e,t){var i=e.replace(/^0/g,"");return(i.indexOf(t.countrycode)>1||-1===i.indexOf(t.countrycode))&&(i="+"+t.countrycode+i),i}},phonebe:{alias:"phone",url:"phone-codes/phone-be.js",countrycode:"32",nojumpsThreshold:4}}),t}(jQuery,Inputmask),function(e,t){return t.extendAliases({Regex:{mask:"r",greedy:!1,repeat:"*",regex:null,regexTokens:null,tokenizer:/\[\^?]?(?:[^\\\]]+|\\[\S\s]?)*]?|\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9][0-9]*|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|c[A-Za-z]|[\S\s]?)|\((?:\?[:=!]?)?|(?:[?*+]|\{[0-9]+(?:,[0-9]*)?\})\??|[^.?*+^${[()|\\]+|./g,quantifierFilter:/[0-9]+[^,]/,isComplete:function(e,t){return new RegExp(t.regex).test(e.join(""))},definitions:{r:{validator:function(t,i,a,n,r){function o(e,t){this.matches=[],this.isGroup=e||!1,this.isQuantifier=t||!1,this.quantifier={min:1,max:1},this.repeaterPart=void 0}function s(){var e,t,i=new o,a=[];for(r.regexTokens=[];e=r.tokenizer.exec(r.regex);)switch(t=e[0],t.charAt(0)){case"(":a.push(new o(!0));break;case")":u=a.pop(),a.length>0?a[a.length-1].matches.push(u):i.matches.push(u);break;case"{":case"+":case"*":var n=new o(!1,!0);t=t.replace(/[{}]/g,"");var s=t.split(","),l=isNaN(s[0])?s[0]:parseInt(s[0]),p=1===s.length?l:isNaN(s[1])?s[1]:parseInt(s[1]);if(n.quantifier={min:l,max:p},a.length>0){var c=a[a.length-1].matches;e=c.pop(),e.isGroup||(u=new o(!0),u.matches.push(e),e=u),c.push(e),c.push(n)}else e=i.matches.pop(),e.isGroup||(u=new o(!0),u.matches.push(e),e=u),i.matches.push(e),i.matches.push(n);break;default:a.length>0?a[a.length-1].matches.push(t):i.matches.push(t)}i.matches.length>0&&r.regexTokens.push(i)}function l(t,i){var a=!1;i&&(c+="(",d++);for(var n=0;n<t.matches.length;n++){var r=t.matches[n];if(r.isGroup===!0)a=l(r,!0);else if(r.isQuantifier===!0){var o=e.inArray(r,t.matches),s=t.matches[o-1],u=c;if(isNaN(r.quantifier.max)){for(;r.repeaterPart&&r.repeaterPart!==c&&r.repeaterPart.length>c.length&&!(a=l(s,!0)););a=a||l(s,!0),a&&(r.repeaterPart=c),c=u+r.quantifier.max}else{for(var p=0,f=r.quantifier.max-1;f>p&&!(a=l(s,!0));p++);c=u+"{"+r.quantifier.min+","+r.quantifier.max+"}"}}else if(void 0!==r.matches)for(var h=0;h<r.length&&!(a=l(r[h],i));h++);else{var v;if("["==r.charAt(0)){v=c,v+=r;for(var g=0;d>g;g++)v+=")";var k=new RegExp("^("+v+")$");a=k.test(m)}else for(var y=0,x=r.length;x>y;y++)if("\\"!==r.charAt(y)){v=c,v+=r.substr(0,y+1),v=v.replace(/\|$/,"");for(var g=0;d>g;g++)v+=")";var k=new RegExp("^("+v+")$");if(a=k.test(m))break}c+=r}if(a)break}return i&&(c+=")",d--),a}var u,p=i.buffer.slice(),c="",f=!1,d=0;null===r.regexTokens&&s(),p.splice(a,0,t);for(var m=p.join(""),h=0;h<r.regexTokens.length;h++){var v=r.regexTokens[h];if(f=l(v,v.isGroup))break}return f},cardinality:1}}}}),t}(jQuery,Inputmask);