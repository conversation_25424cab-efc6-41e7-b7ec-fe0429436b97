AmCharts.themes.patterns = {

	themeName:"patterns",

	AmChart: {
		color: "#000000", backgroundColor: "#FFFFFF"
	},

	AmCoordinateChart: {
		colors:["#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000"],
		patterns:[
		{"url":"patterns/black/pattern1.png", "width":4, "height":4},
		{"url":"patterns/black/pattern2.png", "width":4, "height":4},
		{"url":"patterns/black/pattern3.png", "width":4, "height":4},
		{"url":"patterns/black/pattern4.png", "width":4, "height":4},
		{"url":"patterns/black/pattern5.png", "width":4, "height":4},
		{"url":"patterns/black/pattern6.png", "width":4, "height":4},
		{"url":"patterns/black/pattern7.png", "width":4, "height":4},
		{"url":"patterns/black/pattern8.png", "width":4, "height":4},
		{"url":"patterns/black/pattern9.png", "width":4, "height":4},
		{"url":"patterns/black/pattern10.png", "width":4, "height":4},
		{"url":"patterns/black/pattern11.png", "width":4, "height":4},
		{"url":"patterns/black/pattern12.png", "width":4, "height":4},
		{"url":"patterns/black/pattern13.png", "width":4, "height":4},
		{"url":"patterns/black/pattern14.png", "width":4, "height":4},
		{"url":"patterns/black/pattern15.png", "width":4, "height":4},
		{"url":"patterns/black/pattern16.png", "width":4, "height":4},
		{"url":"patterns/black/pattern17.png", "width":4, "height":4},
		{"url":"patterns/black/pattern18.png", "width":4, "height":4},
		{"url":"patterns/black/pattern19.png", "width":4, "height":4},
		{"url":"patterns/black/pattern20.png", "width":4, "height":4},
		{"url":"patterns/black/pattern21.png", "width":4, "height":4}]
	},


	AmStockChart: {
		colors:["#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000"]
	},

	AmPieChart: {
		depth3D:0,
		angle:0,
		labelRadius:10
	},

	AmSlicedChart: {
		outlineAlpha: 0.3,
		colors:["#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000","#000000"],
		outlineThickness: 1,
		outlineColor:"#000000",
		labelTickColor: "#000000",
		labelTickAlpha: 0.3,
		patterns:[
		{"url":"patterns/black/pattern1.png", "width":4, "height":4},
		{"url":"patterns/black/pattern2.png", "width":4, "height":4},
		{"url":"patterns/black/pattern3.png", "width":4, "height":4},
		{"url":"patterns/black/pattern4.png", "width":4, "height":4},
		{"url":"patterns/black/pattern5.png", "width":4, "height":4},
		{"url":"patterns/black/pattern6.png", "width":4, "height":4},
		{"url":"patterns/black/pattern7.png", "width":4, "height":4},
		{"url":"patterns/black/pattern8.png", "width":4, "height":4},
		{"url":"patterns/black/pattern9.png", "width":4, "height":4},
		{"url":"patterns/black/pattern10.png", "width":4, "height":4},
		{"url":"patterns/black/pattern11.png", "width":4, "height":4},
		{"url":"patterns/black/pattern12.png", "width":4, "height":4},
		{"url":"patterns/black/pattern13.png", "width":4, "height":4},
		{"url":"patterns/black/pattern14.png", "width":4, "height":4},
		{"url":"patterns/black/pattern15.png", "width":4, "height":4},
		{"url":"patterns/black/pattern16.png", "width":4, "height":4},
		{"url":"patterns/black/pattern17.png", "width":4, "height":4},
		{"url":"patterns/black/pattern18.png", "width":4, "height":4},
		{"url":"patterns/black/pattern19.png", "width":4, "height":4},
		{"url":"patterns/black/pattern20.png", "width":4, "height":4},
		{"url":"patterns/black/pattern21.png", "width":4, "height":4}]
	},

	AmRectangularChart: {
		zoomOutButtonColor: '#000000',
		zoomOutButtonRollOverAlpha: 0.15,
		zoomOutButtonImage: "lens.png"
	},



	AxisBase: {
		axisColor: "#000000",
		axisAlpha: 0.3,
		gridAlpha: 0.05,
		gridColor: "#000000"
	},

	ChartScrollbar: {
		backgroundColor: "#000000",
		backgroundAlpha: 0.13,
		graphFillAlpha: 0.4,
		selectedGraphFillAlpha: 0.7,
		graphLineAlpha: 0,
		selectedBackgroundColor: "#FFFFFF",
		selectedBackgroundAlpha: 0.9,
		gridAlpha: 0.15
	},

	ChartCursor: {
		cursorColor: "#000000",
		color: "#FFFFFF",
		cursorAlpha: 0.5
	},

	AmLegend: {
		color: "#000000",
		markerBorderAlpha:0.1,
		markerSize:20,
		switchColor:"#000000"
	},

	AmGraph: {
		lineAlpha: 0.4,
		fillAlphas:0.5
	},

	AmAngularGauge:{
		faceAlpha:0.5,
		facePattern:{"url":"patterns/black/pattern1.png", "width":4, "height":4}
	},


	GaugeArrow: {
		color: "#000000",
		alpha: 1,
		nailAlpha: 1,
		innerRadius: "0%",
		nailRadius: 15,
		startWidth: 15,
		borderAlpha: 1,
		radius:"70%",
		nailBorderAlpha: 1
	},

	GaugeAxis: {
		tickColor: "#000000",
		tickAlpha: 1,
		tickLength: 15,
		minorTickLength: 8,
		axisThickness: 1,
		axisColor: '#000000',
		axisAlpha: 1,
		bandAlpha: 1
	},

	TrendLine: {
		lineColor: "#c03246",
		lineAlpha: 0.8
	},

	// ammap
	AreasSettings: {
		alpha: 0.8,
		color: "#000000",
		colorSolid: "#000000",
		unlistedAreasAlpha: 0.4,
		unlistedAreasColor: "#000000",
		outlineColor: "#FFFFFF",
		outlineAlpha: 0.5,
		outlineThickness: 0.5,
		rollOverColor: "#3c5bdc",
		rollOverOutlineColor: "#FFFFFF",
		selectedOutlineColor: "#FFFFFF",
		selectedColor: "#f15135",
		unlistedAreasOutlineColor: "#FFFFFF",
		unlistedAreasOutlineAlpha: 0.5
	},

	LinesSettings: {
		color: "#000000",
		alpha: 0.8
	},

	ImagesSettings: {
		alpha: 0.8,
		labelColor: "#000000",
		color: "#000000",
		labelRollOverColor: "#3c5bdc"
	},

	ZoomControl: {
		buttonRollOverColor: "#3c5bdc",
		buttonFillColor: "#f15135",
		buttonFillAlpha: 0.8
	},

	SmallMap: {
		mapColor: "#000000",
		rectangleColor: "#FFFFFF",
		backgroundColor: "#FFFFFF",
		backgroundAlpha: 0.7,
		borderThickness: 1,
		borderAlpha: 0.8
	},

	// the defaults below are set using CSS syntax, you can use any existing css property
	// if you don't use Stock chart, you can delete lines below
	PeriodSelector: {
		color: "#000000"
	},

	PeriodButton: {
		color: "#000000",
		background: "transparent",
		opacity: 0.7,
		border: "1px solid rgba(0, 0, 0, .3)",
		MozBorderRadius: "5px",
		borderRadius: "5px",
		margin: "1px",
		outline: "none",
		boxSizing: "border-box"
	},

	PeriodButtonSelected: {
		color: "#000000",
		backgroundColor: "rgba(0, 0, 0, 0.1)",
		border: "1px solid rgba(0, 0, 0, .3)",
		MozBorderRadius: "5px",
		borderRadius: "5px",
		margin: "1px",
		outline: "none",
		opacity: 1,
		boxSizing: "border-box"
	},

	PeriodInputField: {
		color: "#000000",
		background: "transparent",
		border: "1px solid rgba(0, 0, 0, .3)",
		outline: "none"
	},

	DataSetSelector: {
		color: "#000000",
		selectedBackgroundColor: "rgba(0, 0, 0, .25)",
		rollOverBackgroundColor: "rgba(0, 0, 0, .15)"
	},

	DataSetCompareList: {
		color: "#000000",
		lineHeight: "100%",
		boxSizing: "initial",
		webkitBoxSizing: "initial",
		border: "1px solid rgba(0, 0, 0, .3)"
	},

	DataSetSelect: {
		border: "1px solid rgba(0, 0, 0, .3)",
		outline: "none"
	}

};