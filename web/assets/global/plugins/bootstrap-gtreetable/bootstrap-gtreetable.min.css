/* ========================================================= 
 * bootstrap-gtreetable v2.2.1-alpha
 * https://github.com/gilek/bootstrap-gtreetable
 * ========================================================= 
 * Copyright 2014 Macie<PERSON>
 * Licensed under MIT (https://github.com/gilek/bootstrap-gtreetable/blob/master/LICENSE)
 * ========================================================= */

.gtreetable .icon{position:relative;top:1px;display:inline-block;font-family:'Glyphicons Halflings';font-style:normal;font-weight:400;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.gtreetable .node-name{line-height:28px;cursor:pointer}.gtreetable .node-loading{background:url(data:image/gif;base64,R0lGODlhEAAQAIQAACQmJJyanMzOzOzq7GRiZNze3LS2tPT29ERCRNza3OTm5MTCxPz+/CwuLJyenNTS1Ozu7IyKjOTi5Pz6/FRWVMTGxP///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQJBgAWACwAAAAAEAAQAAAFf6AlWpP0KA4RQGMrLXARAUDjtGSyGItMAwiWSFI5JAQKBsRAQUgSEhKsyGgxBrrFoQATVHEMQewBU+BGiq6kUJicRRO2QsF2v+MFBXlhfg+6XAteZ2ExEzALE18iDBNZBxYFRUcDExMDApMFjI6IbIhFOC8xgT1vW0gSAgWQIyEAIfkECQYAFwAsAAAAABAAEACEJCYknJqczM7M7OrsZGJktLa03N7c9Pb0VFZUpKak3NrchIKExMLE5Obk/P78LC4snJ6c1NLU7O7s5OLk/Pr8lJKUxMbE////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABXzgJV7UFDXmdIzsxLyG8TITSypMwcSzRY0Ty0EhaDgoDYtwASG9hA7WcQF4SGQMQdTmQAAAgcirYRsVvoRJ7Fe+SCqVRGPdJhkmpnF98BJgtWUOAjAUMxRbIkc4DCsGQkQDFBQDAo8GiRSLO1gMQjYuMJyXZQcGRRN+KyMhACH5BAkGABcALAAAAAAQABAAhCQmJJyanMzOzOzq7GRiZLS2tNze3PT29FRWVKSmpNza3ISChMTCxOTm5Pz+/CwuLJyenNTS1Ozu7OTi5Pz6/JSSlMTGxP///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAV+4CVe1BQ15nSM7MS8hvEyE0sqTMHEs0WNE8tBIWg4KA2LUFGjvIQO1hHHOMgYgqjNIYBFXg3bqPESTGI/MSnWaKDVF0mlkvgywuoCAEC4ZsUOCHsBTi8UWiJHCwAPAxcGSwIDFBQDAkILAYkUVDtXhjYuMDI6NWJWRRNFaSIhACH5BAkGABYALAAAAAAQABAAAAV8oCVak/QopnSMrLS8RfEuEksmi7HEczWNksohIVAwJoqKMFGbvIQM1hG3OMgWgqiNIYA9XgrbSPESSGI/MSmmUKDV64LiuwirB+VrVsyFOV8TWiIMA1QrBUsCAxMQBhQIEgkFgxNUOxEAmQh2QDMFmAANDmpWRQ4EAQMsIQAh+QQJBgAWACwAAAAAEAAQAIQkJiScmpzMzszs6uy0trTc3txUVlT09vSsrqzc2tzEwsTk5uSEgoT8/vwsLiycnpzU0tTs7uzk4uRkYmT8+vzExsT///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFfKAlWpQELaZ0jKykvEXxKhJLJgqhxHNFjZLKISFYNCiLijBRo7yEDdYRpzjIFIKorSGAQV4L22jxEkhiPzEptlig1evC4qsIqwfla1bMhTlfFFoiUy8rBUIMBgQRFAMCQhAFgxQMAJYBVAo+Ng8OlzI6NWIDARMIEkVpIiEAIfkECQYAFwAsAAAAABAAEACEJCYknJqczM7M7OrsZGJktLa03N7c9Pb0REJEpKak3NrcxMLE5Obk/P78LC4snJ6c1NLU7O7slJKU5OLk/Pr8VFZUxMbE////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABX/gJV7UBDHmdIzstLyG8S4TSypLscSzRY0Ty0EhYDQoDItQUaO8hA3WEbc4yBaCqK0hgEFeDNuI8RJMYj8xKcZISCQR9drAIAAABfmgHLhXtFJdOxEOAAgDgBdTLysPCBNEAxQUAwJCEAYiDQNUO1cLPjYuMDI6NWJWRRNFaSIhACH5BAkGABcALAAAAAAQABAAhCQmJJyanMzOzOzq7GRiZLS2tNze3PT29ERCRKSmpNza3MTCxOTm5Pz+/CwuLJyenNTS1Ozu7JSSlOTi5Pz6/FRWVMTGxP///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAV/4CVe1AQx5nSM7LS8hvEuE3tFylsw8mtRowdiohAwGgeGxXJQ1CIOAOLIalByi0MAAKg0bJeGAEbgFsCiwUuQkEgiaJLCgDJMgOhDjAF5MeJqCwIugl82YjAHMxSGIlZYKwZMRQMUFAMCkwaOVzMxM0w2hAufMHkGRhMCBisjIQAh+QQJBgAWACwAAAAAEAAQAIQkJiScmpzMzszs6uy0trTc3txUVlT09vSsqqzc2tzEwsTk5uSEgoT8/vwsLiycnpzU0tTs7uzk4uRkYmT8+vzExsT///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFfaAlWlEwIRIkHWP7OAAQFEottRbFxDJdV5SRpHJgGAgRyqJCTNwov0Nw1KAkagefoIGzNAS1AqS26IoWNYGkUJh2KezFgu3GwQuLsaJsHqS1XDhfYVA1FIEiVVcKLAVNAgMUFAMCTQWJVjUKbJpEOBKanGFmWQILEgIFLCMhADs=) no-repeat;padding-left:22px}.gtreetable .node-icon-selected{margin-right:6px;display:none}.gtreetable .node-icon-selected:before{content:"\e013"}.gtreetable .node-icon-type{margin-right:12px;opacity:.2;filter:alpha(opacity=20);display:none}.gtreetable .node-icon-ce{margin-right:6px;opacity:.2;filter:alpha(opacity=20);cursor:pointer}.gtreetable .node-icon-ce:before{content:"\e080"}.gtreetable .node-expanded .node-icon-ce{-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);-o-transform:rotate(90deg);transform:rotate(90deg)}.gtreetable .node-hovered{background-color:#f5f5f5}.gtreetable .node-hovered .node-icon-ce,.gtreetable .node-hovered .node-icon-type{opacity:1;filter:alpha(opacity=100)}.gtreetable .node-icon-handle{padding-right:12px;cursor:url(data:image/x-icon;base64,AAACAAEAICACAAcABQAwAQAAFgAAACgAAAAgAAAAQAAAAAEAAQAAAAAAAAEAAAAAAAAAAAAAAgAAAAAAAAAAAAAA////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD8AAAA/AAAAfwAAAP+AAAH/gAAB/8AAA//AAAd/wAAGf+AAAH9gAADbYAAA2yAAAZsAAAGbAAAAGAAAAAAAAA//////////////////////////////////////////////////////////////////////////////////////gH///4B///8Af//+AD///AA///wAH//4AB//8AAf//AAD//5AA///gAP//4AD//8AF///AB///5A////5///8=),move;background:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAECAMAAABx7QVyAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAZQTFRFzMzM////040VdgAAAAJ0Uk5T/wDltzBKAAAAFklEQVR42mJgYGBkAAEQyQgCEBIgwAAAeAAN4Rn1SQAAAABJRU5ErkJggg==) repeat-y;visibility:hidden}.gtreetable .node-draggable.node-hovered .node-icon-handle{visibility:visible}.gtreetable .node-buttons{visibility:hidden}.gtreetable.gtreetable-fullAccess .node-hovered.node-saved .node-buttons,.gtreetable.gtreetable-fullAccess .node-selected.node-saved .node-buttons{visibility:visible}.gtreetable .node-selected .node-icon-selected{display:inline-block}.gtreetable input{display:inline;margin-right:6px;height:28px;padding:3px 6px}.gtreetable .node-draggable-helper{cursor:url(data:image/x-icon;base64,AAACAAEAICACAAcABQAwAQAAFgAAACgAAAAgAAAAQAAAAAEAAQAAAAAAAAEAAAAAAAAAAAAAAgAAAAAAAAAAAAAA////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD8AAAA/AAAAfwAAAP+AAAH/gAAB/8AAAH/AAAB/wAAA/0AAANsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//////////////////////////////////////////////////////////////////////////////////////gH///4B///8Af//+AD///AA///wAH//+AB///wAf//4AH//+AD///yT/////////////////////////////8=),pointer}.gtreetable .node-draggable-pointer{position:absolute;visibility:hidden;display:inline-block;width:0;height:0;margin-left:2px;vertical-align:middle;border-top:6px solid;border-right:6px solid transparent;border-left:6px solid transparent;transform:rotate(-90deg)}.node-draggable-container{background-color:#f5f5f5}.node-draggable-container .node-draggable-pointer{visibility:visible}