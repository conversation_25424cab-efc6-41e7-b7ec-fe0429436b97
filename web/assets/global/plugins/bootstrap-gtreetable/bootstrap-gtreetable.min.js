/* ========================================================= 
 * bootstrap-gtreetable v2.2.1-alpha
 * https://github.com/gilek/bootstrap-gtreetable
 * ========================================================= 
 * Copyright 2014 <PERSON><PERSON><PERSON>
 * Licensed under MIT (https://github.com/gilek/bootstrap-gtreetable/blob/master/LICENSE)
 * ========================================================= */

!function(a){function b(b,c){this.options=c,this.$tree=a(b),this.language=void 0===this.options.languages[this.options.language]?this.options.languages["en-US"]:a.extend({},this.options.languages["en-US"],this.options.languages[this.options.language]),this._isNodeDragging=!1,this._lastId=0,this.actions=[],null!==this.options.defaultActions&&(this.actions=this.options.defaultActions),void 0!==this.options.actions&&this.actions.push.apply(this.actions,this.options.actions),this.options.cache>0&&(this.cacheManager=new d(this));var e=this.language;if(this.template=void 0!==this.options.template?this.options.template:'<table class="table gtreetable"><tr class="'+this.options.classes.node+" "+this.options.classes.collapsed+'"><td><span>${draggableIcon}${indent}${ecIcon}${selectedIcon}${typeIcon}${name}</span><span class="hide '+this.options.classes.action+'">${input}${saveButton} ${cancelButton}</span><div class="btn-group pull-right '+this.options.classes.buttons+'">${actionsButton}${actionsList}</div></td></tr></table>',this.templateParts=void 0!==this.options.templateParts?this.options.templateParts:{draggableIcon:this.options.draggable===!0?'<span class="'+this.options.classes.handleIcon+'">&zwnj;</span><span class="'+this.options.classes.draggablePointer+'">&zwnj;</span>':"",indent:'<span class="'+this.options.classes.indent+'">&zwnj;</span>',ecIcon:'<span class="'+this.options.classes.ceIcon+' icon"></span>',selectedIcon:'<span class="'+this.options.classes.selectedIcon+' icon"></span>',typeIcon:'<span class="'+this.options.classes.typeIcon+'"></span>',name:'<span class="'+this.options.classes.name+'"></span>',input:'<input type="text" name="name" value="" style="width: '+this.options.inputWidth+'" class="form-control" />',saveButton:'<button type="button" class="btn btn-sm btn-success '+this.options.classes.saveButton+'">'+e.save+"</button>",cancelButton:'<button type="button" class="btn btn-sm '+this.options.classes.cancelButton+'">'+e.cancel+"</button>",actionsButton:'<button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">'+e.action+' <span class="caret"></span></button>',actionsList:""},this.actions.length>0){var f='<ul class="dropdown-menu" role="menu"><li role="presentation" class="dropdown-header">'+e.action+"</li>";a.each(this.actions,function(a,b){if(b.divider===!0)f+='<li class="divider"></li>';else{var c=b.name.match(/\$\{([\w\W]+)\}/),d=null!==c&&void 0!==c[1]&&void 0!==e.actions[c[1]]?e.actions[c[1]]:b.name;f+='<li role="presentation"><a href="#notarget" class="node-action-'+a+'" tabindex="-1">'+d+"</a></li>"}}),f+="</ul>",this.templateParts.actionsList=f}var g=this.template;a.each(this.templateParts,function(a,b){g=g.replace("${"+a+"}",b)}),this.options.template=g,0===this.$tree.find("tbody").length&&this.$tree.append("<tbody></tbody>"),this.options.readonly||this.$tree.addClass("gtreetable-fullAccess"),this.$nodeTemplate=a(void 0!==this.options.templateSelector?this.options.templateSelector:this.options.template).find("."+this.options.classes.node),this.options.draggable===!0&&this.isNodeDragging(!1),this.init()}function c(a,b){this.manager=b,this.level=parseInt(a.level),this.parent=a.parent,this.name=a.name,this.type=a.type,this.id=a.id,this.insertPosition=void 0,this.movePosition=void 0,this.relatedNodeId=void 0,this._isExpanded=!1,this._isLoading=!1,this._isSaved=void 0===a.id?!1:!0,this._isSelected=!1,this._isHovered=!1,this._isEditable=!1,this.init()}function d(a){this._cached={},this.manager=a}function e(c,d){var e=null;return this.each(function(){var f=a(this),g=f.data("bs.gtreetable"),h=a.extend({},a.fn.gtreetable.defaults,f.data(),"object"==typeof c&&c);g||(g=new b(this,h),f.data("bs.gtreetable",g)),"string"==typeof c&&(e=g[c](d))}),e||(e=this),e}b.prototype={getNode:function(a){return a.data("bs.gtreetable.gtreetablenode")},getNodeById:function(a){return this.getNode(this.$tree.find("."+this.options.classes.node+"[data-id='"+a+"']"))},getSelectedNodes:function(){var b=[],c=this;return a.each(this.$tree.find("."+this.options.classes.selected),function(){b.push(c.getNode(a(this)))}),b},getSourceNodes:function(b,c){var d=this,e=this.getNodeById(b),f=b>0&&this.options.cache>0;if(f&&c!==!0){var g=this.cacheManager.get(e);if(void 0!==g){var h={};return h[d.options.nodesWrapper]=g,h}}var i=this.options.source(b),j={beforeSend:function(){b>0&&e.isLoading(!0)},success:function(a){if(void 0!==a[d.options.nodesWrapper]){g=a[d.options.nodesWrapper];for(var c=0;c<g.length;c+=1)g[c].parent=b;"function"==typeof d.options.sort&&g.sort(d.options.sort),f&&d.cacheManager.set(e,g)}},error:function(a){alert(a.status+": "+a.responseText)},complete:function(){b>0&&e.isLoading(!1)}};return a.ajax(a.extend({},j,i))},init:function(){var a=this;this.getSourceNodes(0).done(function(b){var d=b[a.options.nodesWrapper];for(var e in d){var f=new c(d[e],a);f.insertIntegral(f)}})},isNodeDragging:function(a){return void 0===a?this._isNodeDragging:void(a===!0?(this._isNodeDragging=!0,this.$tree.disableSelection()):(this._isNodeDragging=!1,this.$tree.enableSelection()))},generateNewId:function(){return this._lastId+=1,"g"+this._lastId}},c.prototype={getPath:function(){var b=this,c=[b.name],d=b.parent;return b.$node.prevAll("."+this.manager.options.classes.node).each(function(){var e=b.manager.getNode(a(this));e.id===d&&(d=e.parent,c[c.length]=e.name)}),c},getParents:function(){for(var a=[],b=this.parent;;){if(0===b)break;var c=this.manager.getNodeById(b);a.push(c),b=c.parent}return a},getIP:function(){var b=this,c="0",d=b.getParents();return d.reverse(),a.each(d,function(){c+="."+this.id}),c+="."+b.id},getSiblings:function(){for(var b=this,c=[],d="."+b.manager.options.classes.node+"[data-parent='"+b.parent+"']",e=b.$node.prevAll(d),f=e.length-1;f>=0;--f)c.push(b.manager.getNode(a(e[f])));return c.push(b),b.$node.nextAll(d).each(function(){c.push(b.manager.getNode(a(this)))}),c},getDescendants:function(b){var c=this,d=a.extend({},{depth:1,includeNotSaved:!1,index:void 0},b),e="."+c.manager.options.classes.node,f=-1!==d.depth||isNaN(d.depth)?d.depth:1/0,g=[];if(d.includeNotSaved===!1&&(e+="."+c.manager.options.classes.saved),f>1?c.$node.nextAll(e).each(function(){var b=c.manager.getNode(a(this));return(b.level<=c.level||b.level===c.level&&b.parent===c.parent)&&(d.includeNotSaved!==!0||b.isSaved())?!1:void g.push(b)}):c.$node.nextAll(e+"[data-parent='"+c.id+"'][data-level='"+(c.level+1)+"']").each(function(){g.push(c.manager.getNode(a(this)))}),!isNaN(d.index)){var h=d.index>=0?d.index-1:g.length+d.index;return g[h]}return g},getMovePosition:function(){return this.movePosition},setMovePosition:function(a,b){this.$node.removeClass(this.manager.options.classes.draggableContainer),void 0!==a&&(this.$node.addClass(this.manager.options.classes.draggableContainer),this.movePosition=a,this.$pointer.css("top",b.top+"px"),this.$pointer.css("left",b.left+"px"))},getId:function(){return this.id},getName:function(){return this.isEditable()?this.$input.val():this.name},getParent:function(){return this.parent},getInsertPosition:function(){return this.insertPosition},getRelatedNodeId:function(){return this.relatedNodeId},init:function(){this.$node=this.manager.$nodeTemplate.clone(!1),this.$name=this.$node.find("."+this.manager.options.classes.name),this.$ceIcon=this.$node.find("."+this.manager.options.classes.ceIcon),this.$typeIcon=this.$node.find("."+this.manager.options.classes.typeIcon),this.$icon=this.$node.find("."+this.manager.options.classes.icon),this.$action=this.$node.find("."+this.manager.options.classes.action),this.$indent=this.$node.find("."+this.manager.options.classes.indent),this.$saveButton=this.$node.find("."+this.manager.options.classes.saveButton),this.$cancelButton=this.$node.find("."+this.manager.options.classes.cancelButton),this.$input=this.$node.find("input"),this.$pointer=this.$node.find("."+this.manager.options.classes.draggablePointer),this.render(),this.attachEvents(),this.$node.data("bs.gtreetable.gtreetablenode",this)},render:function(){this.$name.html(this.name),void 0!==this.id&&(this.$node.attr("data-id",this.id),this.isSaved(!0),this.manager.options.draggable===!0&&this.$node.addClass(this.manager.options.classes.draggable)),this.$node.attr("data-parent",this.parent),this.$node.attr("data-level",this.level),this.$indent.css("marginLeft",(parseInt(this.level)-this.manager.options.rootLevel)*this.manager.options.nodeIndent+"px").html("&zwnj;"),void 0!==this.type&&this.manager.options.types&&void 0!==this.manager.options.types[this.type]&&this.$typeIcon.addClass(this.manager.options.types[this.type]).show()},attachEvents:function(){var b=this,c=parseInt(this.manager.options.selectLimit);if(this.$node.mouseover(function(){(b.manager.options.draggable!==!0||b.manager.isNodeDragging()!==!0)&&(b.$node.addClass(b.manager.options.classes.hovered),b.isHovered(!0))}),this.$node.mouseleave(function(){b.$node.removeClass(b.manager.options.classes.hovered),b.isHovered(!1)}),this.$name.click(isNaN(c)===!1&&(c>0||-1===c)?function(d){if(b.isSelected())a.isFunction(b.manager.options.onUnselect)&&b.manager.options.onUnselect(b),b.isSelected(!1);else{var e=b.manager.getSelectedNodes();1===c&&1===e.length?(e[0].isSelected(!1),e=[]):e.length===c&&(a.isFunction(b.manager.options.onSelectOverflow)&&b.options.onSelectOverflow(b),d.preventDefault()),(e.length<c||-1===c)&&b.isSelected(!0),a.isFunction(b.manager.options.onSelect)&&b.manager.options.onSelect(b)}}:function(){b.$ceIcon.click()}),this.$ceIcon.click(function(a){b.isExpanded()?b.collapse():b.expand({isAltPressed:a.altKey})}),b.manager.options.dragCanExpand===!0&&this.$ceIcon.mouseover(function(){b.manager.options.draggable===!0&&b.manager.isNodeDragging()===!0&&(b.isExpanded()||b.expand())}),a.each(this.manager.actions,function(a,c){b.$node.find("."+b.manager.options.classes.action+"-"+a).click(function(){c.event(b,b.manager)})}),this.$saveButton.click(function(){b.save()}),this.$cancelButton.click(function(){b.saveCancel()}),b.manager.options.draggable===!0){var d=function(a,c){var d,e=a.offset.top-c.offset().top,f=c.offset().top,g=c.outerHeight(),h=g-Math.round(a.helper.outerHeight()/2),i={left:b.manager.$tree.offset().left+5};return.3*h>=e?(d="before",i.top=f+3):.7*h>=e?(d="lastChild",i.top=f+h/2):(d="after",i.top=f+h),i.top+=2,{position:d,pointerOffset:i}};this.$node.draggable({scroll:!0,refreshPositions:b.manager.options.dragCanExpand,helper:function(){var c=b.manager.getNode(a(this));return'<mark class="'+b.manager.options.classes.draggableHelper+'">'+c.name+"</mark>"},cursorAt:{top:0,left:0},handle:"."+b.manager.options.classes.handleIcon,start:function(){a.browser.webkit||a(this).data("bs.gtreetable.gtreetablenode.scrollTop",a(window).scrollTop())},stop:function(){b.manager.isNodeDragging(!1)},drag:function(c,e){if(!a.browser.webkit){var f=a(window).scrollTop(),g=a(this).data("bs.gtreetable.gtreetablenode.scrollTop")-f;e.position.top-=f+g,a(this).data("bs.gtreetable.gtreetablenode.startingScrollTop",f)}var h=a(this).data("bs.gtreetable.gtreetablenode.currentDroppable");if(h){var i=d(e,h);b.manager.getNode(h).setMovePosition(i.position,i.pointerOffset)}}}).droppable({accept:"."+b.manager.options.classes.node,over:function(c,e){var f=a(this),g=d(e,f);b.manager.getNode(f).setMovePosition(g.position,g.pointerOffset),e.draggable.data("bs.gtreetable.gtreetablenode.currentDroppable",f)},out:function(c,d){d.draggable.removeData("bs.gtreetable.gtreetablenode.currentDroppable"),b.manager.getNode(a(this)).setMovePosition()},drop:function(c,d){var e=a(this),f=b.manager.getNode(e),g=f.getMovePosition();d.draggable.removeData("bs.gtreetable.gtreetablenode.currentDroppable"),f.setMovePosition(),b.manager.getNode(d.draggable).move(f,g)}})}},makeEditable:function(){this.showForm(!0)},save:function(){var b=this;a.isFunction(b.manager.options.onSave)?a.when(a.ajax(b.manager.options.onSave(b))).done(function(a){b._save(a)}):b._save({name:b.getName(),id:b.manager.generateNewId()})},_save:function(b){var c=this;c.id=b.id,c.name=b.name,a.isFunction(c.manager.options.sort)&&c.sort(),this.manager.options.cache>0&&this.manager.cacheManager.synchronize(c.isSaved()?"edit":"add",c),c.render(),c.showForm(!1),c.isHovered(!1)},saveCancel:function(){this.showForm(!1),this.isSaved()||this._remove()},expand:function(b){var d=this,e=d,f=a.extend({},{isAltPressed:!1,onAfterFill:function(a,b){a.isExpanded(!0),0===b.length&&(a.manager.options.showExpandIconOnEmpty===!0?a.isExpanded(!1):a.showCeIcon(!1))}},b);a.when(this.manager.getSourceNodes(d.id,f.isAltPressed)).done(function(a){var b=a[d.manager.options.nodesWrapper];for(var g in b){var h=new c(b[g],d.manager);d.insertIntegral(h,e),e=h}f&&f.onAfterFill(d,b)})},collapse:function(){this.isExpanded(!1),a.each(this.getDescendants({depth:-1,includeNotSaved:!0}),function(){this.$node.remove()})},_canAdd:function(a){var b={result:!(0===a.parent&&this.manager.options.manyroots===!1)};return b.result||(b.message=this.manager.language.messages.onNewRootNotAllowed),b},add:function(a,b){function d(){f&&(e.isExpanded(!0),e.showCeIcon(!0)),g.insert(a,e),g.insertPosition=a,g.relatedNodeId=e.id,g.showForm(!0)}var e=this,f="lastChild"===a||"firstChild"===a,g=new c({level:e.level+(f?1:0),parent:e.level!==this.manager.options.rootLevel||f?f?e.id:e.parent:0,type:b},this.manager),h=this._canAdd(g);return h.result?void(f&&!e.isExpanded()?e.expand({onAfterFill:function(){d()}}):d()):(alert(h.message),!1)},insert:function(a,b){var c,d,e=this;if("before"===a)b.$node.before(e.$node);else if("after"===a)d=b,b.isExpanded()&&(c=b.getDescendants({depth:1,index:-1,includeNotSaved:!0}),d=void 0===c?d:c),d.$node.after(e.$node);else if("firstChild"===a)this.manager.getNodeById(b.id).$node.after(e.$node);else{if("lastChild"!==a)throw"Wrong position.";c=b.getDescendants({depth:1,index:-1,includeNotSaved:!0}),d=void 0===c?b:c,d.$node.after(e.$node)}},insertIntegral:function(a,b){void 0===b?this.manager.$tree.append(a.$node):b.$node.after(a.$node)},remove:function(){var b=this;b.isSaved()&&a.isFunction(b.manager.options.onDelete)?a.when(a.ajax(b.manager.options.onDelete(b))).done(function(){b._remove()}):this._remove()},_remove:function(){if(this.isExpanded()===!0&&this.collapse(),this.$node.remove(),this.parent>0){var a=this.manager.getNodeById(this.parent);0===a.getDescendants({depth:1,includeNotSaved:!0}).length&&a.collapse()}this.manager.options.cache>0&&this.manager.cacheManager.synchronize("delete",this)},_canMove:function(b,c){var d=this,e={result:!0};return 0===b.parent&&this.manager.options.manyroots===!1&&"lastChild"!==c?(e.result=!1,e.message=this.manager.language.messages.onMoveAsRoot):a.each(b.getParents(),function(){return this.id===d.id?(e.result=!1,e.message=this.manager.language.messages.onMoveInDescendant,!1):void 0}),e},move:function(b,c){var d=this,e=this._canMove(b,c);return e.result===!1?(alert(e.message),!1):void(a.isFunction(d.manager.options.onMove)?a.when(a.ajax(d.manager.options.onMove(d,b,c))).done(function(){d._move(b,c)}):d._move(b,c))},_move:function(b,c){var d=this,e=d.getDescendants({depth:-1,includeNotSaved:!0}),f=a.extend({},d),g=d.getIP(),h=b.level-d.level;if(d.parent="lastChild"===c?b.id:b.parent,d.level=b.level,"lastChild"!==c||b.isExpanded()){if("lastChild"===c&&(d.level+=1,b.showCeIcon(!0)),d.render(),d.insert(c,b),e.length>0){var i=d.$node;"lastChild"===c&&(h+=1),a.each(e,function(){var a=this;a.level+=h,a.render(),i.after(a.$node),i=a.$node})}}else d.$node.remove(),a.each(e,function(){this.$node.remove()});var j=d.manager.getNodeById(f.parent);void 0!==j&&0===j.getDescendants({depth:1,includeNotSaved:!0}).length&&j.isExpanded(!1),a.isFunction(d.manager.options.sort)&&d.sort(),this.manager.options.cache>0&&this.manager.cacheManager.synchronize("move",d,{oOldNode:f,oldIP:g})},sort:function(){var b=this,c=b.getSiblings();if(c.length>0){var d,e=b.isExpanded()?b.getDescendants({depth:-1,includeNotSaved:!0}):[];a.each(c,function(){return-1===b.manager.options.sort(b,this)?(d=this,!1):void 0}),void 0===d?(d=c[c.length-1],d.isExpanded()&&(d=b.manager.getNodeById(b.parent).getDescendants({depth:-1,index:-1,includeNotSaved:!0})),d.$node.after(b.$node)):d.$node.before(b.$node);var f=b.$node;a.each(e,function(){var a=this;f.after(a.$node),f=a.$node})}},isLoading:function(a){return void 0===a?this._isLoading:void(a?(this.$name.addClass(this.manager.options.classes.loading),this._isLoading=!0):(this.$name.removeClass(this.manager.options.classes.loading),this._isLoading=!1))},isSaved:function(a){return void 0===a?this._isSaved:void(a?(this.$node.addClass(this.manager.options.classes.saved),this._isSaved=!0):(this.$node.removeClass(this.manager.options.classes.saved),this._isSaved=!1))},isSelected:function(a){return void 0===a?this._isSelected:void(a?(this.$node.addClass(this.manager.options.classes.selected),this._isSelected=!0):(this.$node.removeClass(this.manager.options.classes.selected),this._isSelected=!1))},isExpanded:function(a){return void 0===a?this._isExpanded:void(a?(this.$node.addClass(this.manager.options.classes.expanded).removeClass(this.manager.options.classes.collapsed),this._isExpanded=!0):(this.$node.addClass(this.manager.options.classes.collapsed).removeClass(this.manager.options.classes.expanded),this._isExpanded=!1))},isHovered:function(a){return void 0===a?this._isHovered:void(a?(this.$node.addClass(this.manager.options.classes.hovered),this._isHovered=!0):(this.$node.removeClass(this.manager.options.classes.hovered),this.$node.find(".btn-group").removeClass("open"),this._isHovered=!1))},isEditable:function(a){return void 0===a?this._isEditable:void(this._isEditable=a)},showCeIcon:function(a){this.$ceIcon.css("visibility",a?"visible":"hidden")},showForm:function(a){a===!0?(this.isEditable(!0),this.$input.val(this.name),this.$name.addClass("hide"),this.$action.removeClass("hide"),this.$input.focus()):(this.isEditable(!1),this.$name.removeClass("hide"),this.$action.addClass("hide"))}},d.prototype={_getIP:function(a){return"string"==typeof a?a:a.getIP()},get:function(a){return this._cached[this._getIP(a)]},set:function(a,b){this._cached[this._getIP(a)]=b},remove:function(a){this._cached[this._getIP(a)]=void 0},synchronize:function(a,b,c){if(b.parent>0)switch(a){case"add":this._synchronizeAdd(b);break;case"edit":this._synchronizeEdit(b);break;case"delete":this._synchronizeDelete(b);break;case"move":this._synchronizeMove(b,c);break;default:throw"Wrong method."}},_synchronizeAdd:function(a){var b=this.manager.getNodeById(a.parent);if(this.manager.options.cache>1){var c=this.get(b);void 0!==c&&(c.push({id:a.id,name:a.getName(),level:a.level,type:a.type,parent:a.parent}),this.set(b,this.isSortDefined()?this.sort(c):c))}else this.remove(b)},_synchronizeEdit:function(b){var c=this.manager.getNodeById(b.parent);if(this.manager.options.cache>1){var d=this.get(c);a.each(d,function(){return this.id===b.id?(this.name=b.getName(),!1):void 0}),this.set(c,this.isSortDefined()?this.sort(d):d)}else this.remove(c)},_synchronizeDelete:function(b){var c=this.manager.getNodeById(b.parent);if(this.manager.options.cache>1){var d,e=this.get(c);a.each(e,function(a){return this.id===b.id?(d=a,!1):void 0}),void 0!==d&&(e.splice(d,1),this.set(c,e))}else this.remove(c)},_synchronizeMove:function(b,c){var d=this,e=b.getIP(),f=b.level-c.oOldNode.level;a.each(this._cached,function(b){if(b===c.oldIP||0===b.indexOf(c.oldIP+".")){if(d.manager.options.cache>1){var g=[],h=b!==c.oldIP?e+b.substr(c.oldIP.length):e;a(d.get(b)).each(function(){this.level+=f,g.push(this)}),d.set(h,g)}d.remove(b)}}),this.synchronize("delete",c.oOldNode),this.synchronize("add",b)},isSortDefined:function(){return a.isFunction(this.manager.options.sort)},sort:function(a){return a.sort(this.manager.options.sort)}};var f=a.fn.gtreetable;a.fn.gtreetable=e,a.fn.gtreetable.Constructor=b,a.fn.gtreetable.defaults={nodesWrapper:"nodes",nodeIndent:16,language:"en",inputWidth:"60%",cache:2,readonly:!1,selectLimit:1,rootLevel:0,manyroots:!1,draggable:!1,dragCanExpand:!1,showExpandIconOnEmpty:!1,languages:{"en-US":{save:"Save",cancel:"Cancel",action:"Action",actions:{createBefore:"Create before",createAfter:"Create after",createFirstChild:"Create first child",createLastChild:"Create last child",update:"Update","delete":"Delete"},messages:{onDelete:"Are you sure?",onNewRootNotAllowed:"Adding the now node as root is not allowed.",onMoveInDescendant:"The target node should not be descendant.",onMoveAsRoot:"The target node should not be root."}}},defaultActions:[{name:"${createBefore}",event:function(a){a.add("before","default")}},{name:"${createAfter}",event:function(a){a.add("after","default")}},{name:"${createFirstChild}",event:function(a){a.add("firstChild","default")}},{name:"${createLastChild}",event:function(a){a.add("lastChild","default")}},{divider:!0},{name:"${update}",event:function(a){a.makeEditable()}},{name:"${delete}",event:function(a,b){confirm(b.language.messages.onDelete)&&a.remove()}}],classes:{node:"node",loading:"node-loading",selected:"node-selected",hovered:"node-hovered",expanded:"node-expanded",collapsed:"node-collapsed",draggable:"node-draggable",draggableHelper:"node-draggable-helper",draggablePointer:"node-draggable-pointer",draggableContainer:"node-draggable-container",saved:"node-saved",name:"node-name",icon:"node-icon",selectedIcon:"node-icon-selected",ceIcon:"node-icon-ce",typeIcon:"node-icon-type",handleIcon:"node-icon-handle",action:"node-action",indent:"node-indent",saveButton:"node-save",cancelButton:"node-cancel",buttons:"node-buttons"}},a.fn.gtreetable.noConflict=function(){return a.fn.gtreetable=f,this}}(jQuery);