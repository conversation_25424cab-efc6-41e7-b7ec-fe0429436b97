/* Ion.RangeSlider, Nice Skin
// css version 1.5.11
// by <PERSON> | ionden.com
// ===================================================================================================================*/

/* =====================================================================================================================
// Skin details */

.irs-line-mid,
.irs-line-left,
.irs-line-right,
.irs-diapason,
.irs-slider {
    background:#E5E5E5;
}

.irs-diapason {
    background: #ffb848;
}

.irs {
    height: 40px;
}
.irs-with-grid {
    height: 60px;
}
.irs-line {
    height: 8px; top: 25px;
}
    .irs-line-left {
        height: 8px;
    }
    .irs-line-mid {
        height: 8px;
    }
    .irs-line-right {
        height: 8px;
    }

.irs-diapason {
    height: 8px; top: 25px;
}

.irs-slider {
    width: 13px;
    height: 24px;
    top: 17px;
    border:1px solid #fefefe;
    background:#777;

    border-radius: 3px;
}
#irs-active-slider, .irs-slider:hover {
    
}

.irs-min, .irs-max {
   color: #999;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    top: 0; padding: 1px 3px;
    background: rgba(0,0,0,0.1);
    border-radius: 3px;
}
.lt-ie9 .irs-min, .lt-ie9 .irs-max {
    background: #ccc;
}

.irs-from, .irs-to, .irs-single {
    color: #fff;
    font-size: 10px; line-height: 1.333;
    text-shadow: none;
    padding: 1px 5px;
    background: rgba(0,0,0,0.3);
    border-radius: 3px;
}
.lt-ie9 .irs-from, .lt-ie9 .irs-to, .lt-ie9 .irs-single {
    background: #999;
}

.irs-grid-pol {
    background: #99a4ac;
}
.irs-grid-text {
    color: #99a4ac;
}