<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-3.0.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
		http://www.springframework.org/schema/aop https://www.springframework.org/schema/aop/spring-aop.xsd">

       <aop:aspectj-autoproxy />
       <context:component-scan base-package="cn.sphd.miners.modules.*.controller"/>

       <mvc:interceptors>
              <bean id="authInterceptor" class="cn.sphd.miners.common.initializer.AuthInterceptor"/>
       </mvc:interceptors>
       <mvc:annotation-driven>
              <mvc:argument-resolvers>
                     <bean id="authMethodArgumentResolver" class="cn.sphd.miners.common.initializer.AuthMethodArgumentResolver" lazy-init="false"/>
              </mvc:argument-resolvers>
              <!--  可不设置，使用默认的超时时间 -->
              <mvc:async-support default-timeout="15000"/>
              <!-- 处理请求时返回json字符串的中文乱码问题 -->
              <mvc:message-converters>
                     <bean class="org.springframework.http.converter.StringHttpMessageConverter">
                            <property name="supportedMediaTypes">
                                   <list>
                                          <value>application/json;charset=UTF-8</value>
                                   </list>
                            </property>
                     </bean>
              </mvc:message-converters>
       </mvc:annotation-driven>
       <!-- prefix+viewName+suffix= /WEB-INF/jsp/index.jsp -->
       <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
              <property name="prefix" value="/WEB-INF/pages/modules/"/>
              <property name="suffix" value=".jsp"/>
       </bean>

       <bean id="multipartResolver" class="cn.sphd.miners.common.initializer.MinersMultipartResolver">
              <property name="maxUploadSize" value="104857600"/>
              <property name="maxInMemorySize" value="4096"/>
       </bean>
       <bean id="exceptionResolver" class="cn.sphd.miners.common.errors.MinersExceptionResolver">
              <!--<property name="exceptionMappings">-->
                     <!--<props>-->
                            <!--<prop key="java.lang.Exception">error</prop>-->
                     <!--</props>-->
              <!--</property>-->
              <!-- 设置日志输出级别，不定义则默认不输出警告等错误日志信息 -->
              <property name="warnLogCategory" value="WARN"></property>
              <!-- 默认错误页面，当找不到上面mappings中指定的异常对应视图时，使用本默认配置 -->
              <!--<property name="defaultErrorView" value="error"></property>-->
              <!-- 默认HTTP状态码 -->
              <property name="defaultStatusCode" value="500"></property>
       </bean>
</beans>