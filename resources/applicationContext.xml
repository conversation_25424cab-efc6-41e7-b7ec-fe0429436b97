<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:redis="http://www.springframework.org/schema/redis"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-4.3.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop-4.3.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx-4.3.xsd
		http://www.springframework.org/schema/cache
		http://www.springframework.org/schema/cache/spring-cache-4.3.xsd
		http://www.springframework.org/schema/redis
		http://www.springframework.org/schema/redis/spring-redis.xsd
		http://www.springframework.org/schema/util
		http://www.springframework.org/schema/util/spring-util-4.3.xsd
        http://www.springframework.org/schema/task
        http://www.springframework.org/schema/task/spring-task-4.3.xsd">

       <context:component-scan base-package="cn.sphd.miners"/>
       <tx:annotation-driven transaction-manager="txManager"/>
       <!-- 扫描注解 -->
       <task:annotation-driven />

       <!-- redis连接配置，依次为主机ip，端口，是否使用池，(usePool=true时)redis的池配置 -->
       <bean id="connectionFactory" class="org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory"
             p:host-name="**************" p:port="6379" p:password="ACJH8FA#A^W%P^oEd" p:database="1" p:validateConnection="true" p:timeout="60000">
       </bean>
       <!-- redis template definition -->
       <!--    删除SessionListener -->
       <!-- 不再使用session删除监听器
       <bean id="httpSessionMonitorListener" class="cn.sphd.miners.common.initializer.SessionListener"></bean>
              //move from Redis session redisHttpSessionConfiguration property
              <property name="httpSessionListeners">
                     <list>
                            <ref bean="httpSessionMonitorListener"/>
                     </list>
              </property>
        -->
       <bean id="redisTemplate" class="org.springframework.data.redis.core.RedisTemplate" p:connection-factory-ref="connectionFactory"/>
       <!-- session过期时间15日 -->
       <!-- 让Spring Session不再执行config命令 -->
       <!-- Begin of Redis session
       <bean id="redisHttpSessionConfiguration" class="org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration">
              <property name="maxInactiveIntervalInSeconds" value="1296000"></property>
        </bean>
       End of Redis session -->

       <bean id="messageListener" class="cn.sphd.miners.modules.socket.listener.DefaultMessageListener"></bean>
       <!--注册listener-->
       <redis:listener-container connection-factory="connectionFactory">
              <redis:listener ref="messageListener" method="handleMessage" topic="cluster:*"></redis:listener>
       </redis:listener-container>

       <!-- spring thread pool executor -->
       <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
              <!-- 线程池维护线程的最少数量 -->
              <property name="corePoolSize" value="8" />
              <!-- 允许的空闲时间 -->
              <property name="keepAliveSeconds" value="300" />
              <!-- 线程池维护线程的最大数量 -->
              <property name="maxPoolSize" value="128" />
              <!-- 缓存队列 -->
              <property name="queueCapacity" value="1024" />
              <!-- 对拒绝task的处理策略 -->
              <property name="rejectedExecutionHandler">
                     <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy" />
              </property>
       </bean>

       <!-- Datasource -->
<!--       <bean id="dataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource" destroy-method="close">-->
<!--              <property name="driverClass" value="com.mysql.cj.jdbc.Driver" />-->
<!--              <property name="jdbcUrl" value="*****************************************************************************************************************************************************************"/>-->
<!--              <property name="user" value="miners" />-->
<!--              <property name="password" value="sphd" />-->
<!--              &lt;!&ndash;连接池中保留的最小连接数。&ndash;&gt;-->
<!--              <property name="minPoolSize" value="3" />-->
<!--              &lt;!&ndash;连接池中保留的最大连接数。Default: 15 &ndash;&gt;-->
<!--              <property name="maxPoolSize" value="60" />-->
<!--              &lt;!&ndash;初始化时获取的连接数，取值应在minPoolSize与maxPoolSize之间。Default: 3 &ndash;&gt;-->
<!--              <property name="initialPoolSize" value="3"/>-->
<!--              &lt;!&ndash;最大空闲时间,60秒内未使用则连接被丢弃。若为0则永不丢弃。Default: 0 &ndash;&gt;-->
<!--              <property name="maxIdleTime" value="600"/>-->
<!--              &lt;!&ndash;当连接池中的连接耗尽的时候c3p0一次同时获取的连接数。Default: 3 &ndash;&gt;-->
<!--              <property name="acquireIncrement" value="3" />-->
<!--              &lt;!&ndash;JDBC的标准参数，用以控制数据源内加载的PreparedStatements数量。但由于预缓存的statements-->
<!--                  属于单个connection而不是整个连接池。所以设置这个参数需要考虑到多方面的因素。-->
<!--                  如果maxStatements与maxStatementsPerConnection均为0，则缓存被关闭。Default: 0&ndash;&gt;-->
<!--              <property name="maxStatements" value="0" />-->
<!--              <property name="maxStatementsPerConnection" value="0" />-->
<!--              &lt;!&ndash; 当连接池用完时客户端调用getConnection()后等待获取新连接的时间，超时后将抛出 SQLException，如设为0则无限期等待。单位毫秒，默认为0 &ndash;&gt;-->
<!--              <property name="checkoutTimeout" value="60000" />-->
<!--              &lt;!&ndash;每60秒检查所有连接池中的空闲连接。Default: 0 &ndash;&gt;-->
<!--              <property name="idleConnectionTestPeriod" value="300" />-->
<!--              &lt;!&ndash;定义在从数据库获取新连接失败后重复尝试的次数。Default: 30 &ndash;&gt;-->
<!--              <property name="acquireRetryAttempts" value="30" />-->
<!--              &lt;!&ndash;获取连接失败将会引起所有等待连接池来获取连接的线程抛出异常。但是数据源仍有效-->
<!--                  保留，并在下次调用getConnection()的时候继续尝试获取连接。如果设为true，那么在尝试-->
<!--                  获取连接失败后该数据源将申明已断开并永久关闭。Default: false&ndash;&gt;-->
<!--              <property name="breakAfterAcquireFailure" value="true" />-->
<!--       </bean>-->
       <bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
              <property name="driverClassName" value="com.mysql.cj.jdbc.Driver" />
              <property name="jdbcUrl" value="**************************************************************************************************************************"/>
              <property name="username" value="miners" />
              <property name="password" value="sphd" />
              <!-- 连接只读数据库时配置为true， 保证安全 -->
              <property name="readOnly" value="false" />
              <!-- 等待连接池分配连接的最大时长（毫秒），超过这个时长还没可用的连接则发生SQLException， 缺省:30秒 -->
              <property name="connectionTimeout" value="30000" />
              <!-- 一个连接idle状态的最大时长（毫秒），超时则被释放（retired），缺省:10分钟 -->
              <property name="idleTimeout" value="30000" />
              <!-- 一个连接的生命时长（毫秒），超时而且没被使用则被释放（retired），缺省:30分钟，建议设置比数据库超时时长少30秒，参考MySQL wait_timeout参数（show variables like '%timeout%';） -->
              <property name="maxLifetime" value="30000" />
<!--              <property name="maxLifetime" value="1770000" />-->
<!--              <property name="minimumIdle" value="10" />-->
              <!-- 连接池中允许的最大连接数。缺省值：10；推荐的公式：((core_count * 2) + effective_spindle_count) -->
              <property name="maximumPoolSize" value="4" />
<!--              &lt;!&ndash; 您未在定义的时间限制内关闭连接时，通知潜在的泄漏。 &ndash;&gt;-->
<!--              <property name="leakDetectionThreshold" value="60000"/>-->
       </bean>

       <!-- hibernate  -->

       <bean id="sessionFactory" class="org.springframework.orm.hibernate5.LocalSessionFactoryBean">
              <property name="dataSource" ref="dataSource"/>
              <property name="packagesToScan">
                     <list>
                            <!-- 可以加多个包 -->
                            <value>cn.sphd.miners.modules.*</value>
                     </list>
              </property>

              <property name="hibernateProperties">
                     <props>
                            <prop key="hibernate.dialect">cn.sphd.miners.common.utils.MySQL5LocalDialect</prop>
                            <!--
                            <prop key="hibernate.show_sql">true</prop>
                            -->
<!--                         <prop key="hibernate.connection.url">*****************************************************************************************************************************************************************</prop>-->
                         <prop key="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver</prop>
                     </props>
              </property>
       </bean>
       <bean id="txManager" class="org.springframework.orm.hibernate5.HibernateTransactionManager">
              <property name="sessionFactory" ref="sessionFactory"/>
       </bean>

       <tx:advice id="txAdvice" transaction-manager="txManager">
              <tx:attributes>
                     <tx:method name="save*" propagation="REQUIRED" />
                     <tx:method name="select*" propagation="REQUIRED" />
                     <tx:method name="delete*" propagation="REQUIRED" />
                     <tx:method name="update*" propagation="REQUIRED" />
                     <tx:method name="*" read-only="true" rollback-for="Exception"/>
              </tx:attributes>
       </tx:advice>


       <aop:aspectj-autoproxy proxy-target-class="true" />
       <aop:config>
              <aop:advisor advice-ref="txAdvice" pointcut="execution(* cn.sphd.miners.common.service..*.*(..))"/>
              <aop:advisor advice-ref="txAdvice" pointcut="execution(* cn.sphd.miners.modules..service..*.*(..))"/>
       </aop:config>


       <!--hibernate-->
       <!-- Spring定时器 -->
       <!--微信获取商户服务，启动时触发，微信抖音小程序accessToken定时任务 1小时触发一次 -->
       <bean id="tpThirdPlatformTask" class="cn.sphd.miners.modules.thirdPlatform.task.TpThirdPlatformTask"/>
       <bean id="tpMerchantJob" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="tpThirdPlatformTask"/>
              <property name="targetMethod" value="tpMerchant"/>
       </bean>
       <bean id="tpMerchantStartTrigger" class="org.springframework.scheduling.quartz.SimpleTriggerFactoryBean">
              <property name="jobDetail" ref="tpMerchantJob" />
              <property name="startDelay" value="10000" />
              <property name="repeatInterval" value="0" />
              <property name="repeatCount" value="0" />
       </bean>
       <bean id="tpAccessTokenTaskJob" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="tpThirdPlatformTask"/>
              <property name="targetMethod" value="tpAccessToken"/>
       </bean>
       <bean id="tpAccessTokenStartTrigger" class="org.springframework.scheduling.quartz.SimpleTriggerFactoryBean">
              <property name="jobDetail" ref="tpAccessTokenTaskJob" />
              <property name="startDelay" value="11000" />
              <property name="repeatInterval" value="0" />
              <property name="repeatCount" value="0" />
       </bean>
       <bean id="tpAccessTokenTriggerHour"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="tpAccessTokenTaskJob"/>
              <property name="cronExpression" value="0 47 * * * ?" />
       </bean>
       <!--考勤扫描定时任务,启动触发一次，之后每逢每15分钟的第2分43秒，这样可以规避每天早晨00:01开始的部门修改定时任务-->
       <bean id="attendanceTask" class="cn.sphd.miners.modules.generalAffairs.task.AttendanceTask"/>
       <bean id="runAttendanceTask" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="attendanceTask"/>
              <property name="targetMethod" value="runAttendanceTask"/>
       </bean>
       <bean id="runAttendanceTaskTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="runAttendanceTask"/>
              <property name="cronExpression" value="43 2/15 * * * ?" />
       </bean>
       <bean id="runAttendanceTaskStartTrigger" class="org.springframework.scheduling.quartz.SimpleTriggerFactoryBean">
              <property name="jobDetail" ref="runAttendanceTask" />
              <property name="startDelay" value="12000" />
              <property name="repeatInterval" value="0" />
              <property name="repeatCount" value="0" />
       </bean>

       <bean id="financeTask" class="cn.sphd.miners.modules.finance.task.FinanceTask"/>
       <bean id="daySettle" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="financeTask"/>
              <property name="targetMethod" value="settleDay"/>
       </bean>

       <bean id="dayTrigger"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="daySettle"/>
              <property name="cronExpression" value="0 0 0 * * ?" />
       </bean>

       <!--wp和acc隶属数据初始化定时任务-->
       <bean id="authTask" class="cn.sphd.miners.modules.auth.task.AuthTask"/>
       <bean id="authDisable" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="authTask"/>
              <property name="targetMethod" value="authDisable"/>
       </bean>
       <bean id="authDisableTrigger"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="authDisable"/>
              <property name="cronExpression" value="30 0 0 * * ?" />
       </bean>

       <!--部门修改每日定时任务-->
       <bean id="postTaskDay" class="cn.sphd.miners.modules.generalAffairs.task.PostTask"/>
       <bean id="postSettleDay" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="postTaskDay"/>
              <property name="targetMethod" value="postSettleDay"/>
       </bean>
       <bean id="postTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="postSettleDay"/>
              <property name="cronExpression" value="0 1 0 * * ?" />
       </bean>

<!--       &lt;!&ndash;考勤设置每日定时任务&ndash;&gt;-->
<!--       <bean id="attendanceSettleDay" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--              <property name="targetObject" ref="attendanceTask"/>-->
<!--              <property name="targetMethod" value="attendanceSettleDay"/>-->
<!--       </bean>-->
<!--       <bean id="attendanceTriggerDay"-->
<!--             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--              <property name="jobDetail" ref="attendanceSettleDay"/>-->
<!--              <property name="cronExpression" value="10 1 0 * * ?" />-->
<!--       </bean>-->

       <!--文件与资料每日定时任务-->
       <bean id="resTrashFileTaskDay" class="cn.sphd.miners.modules.resourceAuthority.task.ResTrashFileTask"/>
       <bean id="delResTrashFileTaskDay" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="resTrashFileTaskDay"/>
              <property name="targetMethod" value="delResTrashFileTaskDay"/>
       </bean>
       <bean id="resTrashFileTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="delResTrashFileTaskDay"/>
              <property name="cronExpression" value="30 1 0 * * ?" />
       </bean>

       <!--计算过期合同每日执行-->
       <bean id="contactBaseTaskDay" class="cn.sphd.miners.modules.sales.task.ContactBaseTask"/>
       <bean id="contactBaseTask" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="contactBaseTaskDay"/>
              <property name="targetMethod" value="contactBaseTask"/>
       </bean>
       <bean id="contactBaseTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="contactBaseTask"/>
              <property name="cronExpression" value="0 2 0 * * ?" />
       </bean>
       <!--培训每日定时任务 -->
       <bean id="trainTaskDay" class="cn.sphd.miners.modules.trainManage.task.TrainTask"/>
       <bean id="trainDay" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="trainTaskDay"/>
              <property name="targetMethod" value="trainDay"/>
       </bean>
       <bean id="trainTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="trainDay"/>
              <property name="cronExpression" value="30 2 0 * * ?" />
       </bean>

       <!--各机构统计每日定时任务-->
       <bean id="dailyOrgIndexTaskDay" class="cn.sphd.miners.modules.dailyIndex.task.DailyOrgIndexTask"/>
       <bean id="dailyOrgIndexDayTask" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="dailyOrgIndexTaskDay"/>
              <property name="targetMethod" value="dailyOrgIndexDay"/>
       </bean>
       <bean id="dailyOrgIndexTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="dailyOrgIndexDayTask"/>
              <property name="cronExpression" value="0 3 0 * * ?" />
       </bean>

       <!--讨论区每日定时任务-->
       <bean id="forumStopTaskDay" class="cn.sphd.miners.modules.forumArea.task.ForumStopTask"/>
       <bean id="stopUsingForumTaskDay" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="forumStopTaskDay"/>
              <property name="targetMethod" value="stopUsingForumTaskDay"/>
       </bean>
       <bean id="forumTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="stopUsingForumTaskDay"/>
              <property name="cronExpression" value="0 5 0 * * ?" />
       </bean>

       <!--阅览室每日定时任务-->
       <bean id="readRoomTaskDay" class="cn.sphd.miners.modules.resourceAuthority.task.ReadRoomTask"/>
       <bean id="stopUsingReadRoomTaskDay" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="readRoomTaskDay"/>
              <property name="targetMethod" value="stopUsingReadRoomTaskDay"/>
       </bean>
       <bean id="readRoomTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="stopUsingReadRoomTaskDay"/>
              <property name="cronExpression" value="0 9 0 * * ?" />
       </bean>


       <!--机构日程每日定时任务-->
       <bean id="scheduleTaskDay" class="cn.sphd.miners.modules.schedule.task.ScheduleTask"/>
       <bean id="scheduleSettleDay" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="scheduleTaskDay"/>
              <property name="targetMethod" value="scheduleSettleDay"/>
       </bean>
       <bean id="scheduleTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="scheduleSettleDay"/>
              <property name="cronExpression" value="0 10 0 * * ?" />
       </bean>

       <!--领地日程每日定时任务-->
       <bean id="dmmScheduleTaskDay" class="cn.sphd.miners.modules.dmmSchedule.task.DmmScheduleTask"/>
       <bean id="dmmScheduleSettleDay" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="dmmScheduleTaskDay"/>
              <property name="targetMethod" value="dmmScheduleSettleDay"/>
       </bean>
       <bean id="dmmScheduleTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="dmmScheduleSettleDay"/>
              <property name="cronExpression" value="0 14 0 * * ?" />
       </bean>

       <!--上传文件每日定时任务-->
       <bean id="uploadTask" class="cn.sphd.miners.modules.uploads.task.UploadTask"/>
       <bean id="checkUpload" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="uploadTask"/>
              <property name="targetMethod" value="checkUpload"/>
       </bean>
       <bean id="uploadTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="checkUpload"/>
              <property name="cronExpression" value="0 15 0 * * ?" />
       </bean>

       <!--移动删除每日定时任务-->
       <bean id="removeInlvidDataTask" class="cn.sphd.miners.modules.socket.task.RemoveInlvidDataTask"/>
       <bean id="dailyRemove" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="removeInlvidDataTask"/>
              <property name="targetMethod" value="dailyRemove"/>
       </bean>
       <bean id="removeInlvidDataTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="dailyRemove"/>
              <property name="cronExpression" value="0 16 1 * * ?" />
       </bean>

       <!--角标每日定时任务-->
       <bean id="badgeNumberTask" class="cn.sphd.miners.modules.system.task.BadgeNumberTask"/>
       <bean id="updateAllBadgeNumber" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="badgeNumberTask"/>
              <property name="targetMethod" value="updateAllBadgeNumber"/>
       </bean>
       <bean id="badgeNumbeTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="updateAllBadgeNumber"/>
              <property name="cronExpression" value="0 17 1 * * ?" />
       </bean>

       <!--劳动合同每日定时任务-->
       <bean id="personnelContractTask" class="cn.sphd.miners.modules.personal.task.PersonnelContractTask"/>
       <bean id="personnelContractTaskDay" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="personnelContractTask"/>
              <property name="targetMethod" value="personnelContractTaskDay"/>
       </bean>
       <bean id="personnelContracTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="personnelContractTaskDay"/>
              <property name="cronExpression" value="0 50 1 * * ?" />
       </bean>

       <!--空间与流量每日定时任务-->
       <bean id="spaceTrafficTaskDay" class="cn.sphd.miners.modules.spaceTraffic.task.SpaceTrafficDayTask"/>
       <bean id="spaceTrafficDayTask" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="spaceTrafficTaskDay"/>
              <property name="targetMethod" value="spaceTrafficDayTask"/>
       </bean>
       <bean id="spaceTrafficTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="spaceTrafficDayTask"/>
              <property name="cronExpression" value="0 0 2 * * ?" />
       </bean>

       <!--会计报税计划任务-->
       <bean id="accountantTask" class="cn.sphd.miners.modules.accountant.task.AccountantTask"/>
       <bean id="accountTaxJob"
             class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="accountantTask"/>
              <property name="targetMethod" value="doScheduled"/>
       </bean>
       <bean id="accountTaxTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="accountTaxJob"/>
              <property name="cronExpression" value="0 20 10 * * ?"/>
       </bean>

       <!--汽车定时任务-->
       <bean id="carTask" class="cn.sphd.miners.modules.car.task.CarTask"/>
       <bean id="carTaskJob" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
              <property name="targetObject" ref="carTask"/>
              <property name="targetMethod" value="checkInsurance"/>
       </bean>
       <bean id="carTriggerDay"
             class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
              <property name="jobDetail" ref="carTaskJob"/>
              <property name="cronExpression" value="0 0 14 * * ?" />
       </bean>

       <!-- 总调度用于启动Spring定时器 -->
       <bean class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
              <property name="triggers">
                     <list>
                            <!-- 启动延时任务 -->
                            <ref bean="tpMerchantStartTrigger" />
                            <ref bean="tpAccessTokenStartTrigger" />
                            <ref bean="runAttendanceTaskStartTrigger" />
                            <!-- 小于日任务 -->
                            <ref bean="runAttendanceTaskTrigger" />
                            <ref bean="tpAccessTokenTriggerHour" />
                            <!-- 日任务 按照开始时间排 -->
                            <ref bean="dayTrigger" />
                            <ref bean="authDisableTrigger" />
                            <ref bean="postTriggerDay" />
<!--                            <ref bean="attendanceTriggerDay" />-->
                            <ref bean="resTrashFileTriggerDay" />
                            <ref bean="personnelContracTriggerDay" />
                            <ref bean="spaceTrafficTriggerDay" />
                            <ref bean="trainTriggerDay" />
                            <ref bean="contactBaseTriggerDay" />
                            <ref bean="dailyOrgIndexTriggerDay" />
                            <ref bean="forumTriggerDay" />
                            <ref bean="readRoomTriggerDay" />
                            <ref bean="scheduleTriggerDay" />
                            <ref bean="dmmScheduleTriggerDay" />
                            <ref bean="uploadTriggerDay" />
                            <ref bean="removeInlvidDataTriggerDay" />
                            <ref bean="badgeNumbeTriggerDay" />
                            <ref bean="accountTaxTrigger" />
                            <ref bean="carTriggerDay" />
                            <!-- 周任务 -->
                            <!-- 月任务 -->
                            <!-- 年任务 -->
                     </list>
              </property>
       </bean>

       <!--mybatise-->

       <bean id ="sqlSessionFactory" class= "cn.sphd.miners.common.utils.PackagesSqlSessionFactoryBean" >
              <property name ="dataSource" ref="dataSource"/>
              <property name="typeAliasesPackage" value="cn.sphd.miners.modules.*.entity"/>
              <property name="mapperLocations" value="classpath:cn/sphd/miners/modules/**/mapper/xml/*.xml" />
              <property name="configLocation" value="classpath:mybatis-config.xml"/>
              <!-- 插件配置 -->
              <property name="plugins">
                     <array>
                            <bean id="pagePlugin" class="cn.sphd.miners.common.utils.PagePlugin">
                                   <!-- 指定数据库方言 -->
                                   <property name="dialect" value="mysql"/>
                                   <property name="pageSqlId" value=".*listPage.*"/>
                            </bean>
                     </array>
              </property>
       </bean >


       <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
              <property name="basePackage" value="cn.sphd.miners.modules.*.mapper" />
       </bean>

       <bean id="sqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
              <constructor-arg ref="sqlSessionFactory" />
       </bean>

       <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
              <property name="dataSource" ref="dataSource" />
       </bean>

       <!--缓存-->
       <cache:annotation-driven />
       <!-- generic cache manager -->
       <!-- Begin of Redis cache
       <bean id="redisCacheWriter" class="org.springframework.data.redis.cache.RedisCacheWriter" factory-method="nonLockingRedisCacheWriter">
              <constructor-arg ref="connectionFactory"/>
       </bean>
       <bean id="redisCacheConfiguration" class="org.springframework.data.redis.cache.RedisCacheConfiguration" factory-method="defaultCacheConfig" />
       <bean id="cacheManager" class="org.springframework.data.redis.cache.RedisCacheManager">
              <constructor-arg index="0" ref="redisCacheWriter"/>
              <constructor-arg index="1" ref="redisCacheConfiguration"/>
       </bean>
       End of Redis cache -->
       <!-- Begin of Simple cache -->
       <bean id="cacheManager" class="org.springframework.cache.support.SimpleCacheManager">
              <property name="caches">
                     <set>
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="default" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="allPopedomSearch" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="popedomList" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="orgWithUserTree" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="ipAddress" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="changePasswordTime" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="personnelAttendanceMonthlyId" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="popedomTaskGetAll" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="userPopedoms" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="userPopedomTasks" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="userBadgeNumbers" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="getUserByRoleCode" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="verifyToken" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="getPersonnelAttendanceConfigsOpenDates" />
                            <bean class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean" p:name="getPersonnelAttendanceConfigsByExactOpenDate" />
                     </set>
              </property>
       </bean>
       <!-- End of Simple cache -->
</beans>