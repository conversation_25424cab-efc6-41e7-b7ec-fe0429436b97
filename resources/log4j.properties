### set log levels ###
log4j.rootLogger = INFO, stdout, D
log4j.logger.cn.sphd = INFO
log4j.logger.System.out.println = INFO
log4j.logger.org.hibernate.engine.jdbc.spi.SqlStatementLogger = INFO

log4j.appender.stdout = org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target = System.out
log4j.appender.stdout.Threshold = INFO
log4j.appender.stdout.layout = org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern = %-d{yyyy-MM-dd HH:mm:ss.SSS} [%p] [%c{6}] %m%n

log4j.appender.D = org.apache.log4j.DailyRollingFileAppender
log4j.appender.D.File = ${catalina.home}/logs/log.log
log4j.appender.D.Append = true
log4j.appender.D.Threshold = INFO
log4j.appender.D.layout = org.apache.log4j.PatternLayout
log4j.appender.D.layout.ConversionPattern = %-d{yyyy-MM-dd'T'HH:mm:ss.SSSXXX} [%p] [%c] %m%n

log4j.logger.org.mybatis=DEBUG
log4j.logger.org.apache.ibatis=DEBUG

log4j.appender.A1=org.apache.log4j.RollingFileAppender  
log4j.appender.A1.Encoding=UTF-8  
log4j.appender.A1.File=all.log  