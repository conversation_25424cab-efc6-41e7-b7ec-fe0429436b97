{"outPath": "./web/smart-doc", "serverUrl": "https://dvm01.btransmission.com", "serverEnv": "https://{{server}}", "isStrict": false, "allInOne": true, "coverOld": true, "sortByTitle": true, "showAuthor": true, "style": "atom-one-dark", "createDebugPage": true, "md5EncryptedHtmlName": false, "packageFilters": "cn.sphd.miners.modules.*.controller.*", "packageExcludeFilters": "", "framework": "spring", "projectName": "通用框架", "sourceFolder": "src/cn/sphd", "appToken": "f6c64bc9a403451da4d04f726f5f1e8e", "openUrl": "https://torna.btransmission.com/api", "debugEnvName": "主干通用框架服务器", "replace": true, "ignoreRequestParams": ["cn.sphd.miners.modules.auth.dto.AuthInfoDto", "cn.sphd.miners.modules.auth.entity.AuthAcc", "cn.sphd.miners.modules.auth.entity.AuthWp", "cn.sphd.miners.modules.iot.entity.IotTerminal", "cn.sphd.miners.modules.system.entity.Organization", "cn.sphd.miners.modules.system.entity.User", "cn.sphd.miners.modules.thirdPlatform.entity.TpMember"], "groups": [{"name": "账号.登录、注册、激活、微信、领地", "apis": "cn.sphd.miners.modules.auth.controller.AccQuestionController"}, {"name": "账号.记录", "apis": "cn.sphd.miners.modules.auth.controller.*"}, {"name": "其他", "apis": "cn.sphd.miners.modules.*.controller.*"}]}