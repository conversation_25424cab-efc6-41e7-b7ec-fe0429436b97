<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.sphd</groupId>
    <artifactId>miners</artifactId>
    <packaging>war</packaging>
    <version>V3.9.20241120</version>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- https://mvnrepository.com/artifact/com.auth0/java-jwt -->
        <version.jwt>4.4.0</version.jwt>
        <!-- https://mvnrepository.com/artifact/com.thetransactioncompany/cors-filter -->
        <version.cors-filter>2.10</version.cors-filter>
        <!-- http://mvnrepository.com/artifact/commons-fileupload/commons-fileupload -->
        <version.commons-fileupload>1.5</version.commons-fileupload>
        <!-- http://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
        <version.commons-lang3>3.17.0</version.commons-lang3>
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-text -->
        <version.commons-text>1.12.0</version.commons-text>
        <!-- https://mvnrepository.com/artifact/org.aspectj/aspectjtools -->
        <version.aspectj>1.9.22.1</version.aspectj>
        <!-- http://mvnrepository.com/artifact/org.springframework/spring-core -->
        <!-- http://projects.spring.io/spring-framework/ -->
        <version.spring>5.3.39</version.spring>
        <!-- http://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
        <!-- http://hc.apache.org/downloads.cgi -->
        <version.httpclient>4.5.14</version.httpclient>
        <!-- http://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-core -->
        <version.jackson>2.18.1</version.jackson>
        <!-- http://mvnrepository.com/artifact/org.hibernate/hibernate-core -->
        <!-- http://hibernate.org/orm/downloads/ -->
        <version.hibernate>5.6.15.Final</version.hibernate>
        <!-- https://mvnrepository.com/artifact/org.hibernate.common/hibernate-commons-annotations -->
        <version.hibernate-commons-annotations>5.1.2.Final</version.hibernate-commons-annotations>
        <!-- https://mvnrepository.com/artifact/org.hibernate.validator/hibernate-validator -->
        <!-- http://hibernate.org/validator/downloads/ -->
        <version.hibernateValidator>6.2.5.Final</version.hibernateValidator>
        <!-- http://mvnrepository.com/artifact/org.mybatis/mybatis -->
        <version.mybatis>3.5.16</version.mybatis>
        <!-- http://mvnrepository.com/artifact/org.mybatis/mybatis-spring -->
        <version.mybatis-spring>2.1.2</version.mybatis-spring>
        <!-- https://mvnrepository.com/artifact/com.mysql/mysql-connector-j -->
        <version.mysql>9.3.0</version.mysql>
        <!-- http://mvnrepository.com/artifact/org.quartz-scheduler/quartz -->
        <version.quartz>2.3.2</version.quartz>
        <!-- https://mvnrepository.com/artifact/com.zaxxer/HikariCP -->
        <version.hikari>6.1.0</version.hikari>
<!--        &lt;!&ndash; http://mvnrepository.com/artifact/com.mchange/c3p0 &ndash;&gt;-->
<!--        <version.c3p0>0.9.5.5</version.c3p0>-->
        <!-- https://mvnrepository.com/artifact/net.sf.json-lib/json-lib -->
        <version.json-lib>2.4</version.json-lib>
        <!-- https://mvnrepository.com/artifact/org.json/json -->
        <version.json>20240303</version.json>
        <!-- https://mvnrepository.com/artifact/com.alibaba.fastjson2/fastjson2 -->
        <!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
        <version.fastjson>2.0.53</version.fastjson>
        <!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpcore -->
<!--        <version.http>4.4.16</version.http>-->
        <!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
        <version.commons-io>2.17.0</version.commons-io>
        <!-- https://mvnrepository.com/artifact/io.lettuce/lettuce-core -->
        <version.lettuce>6.5.0.RELEASE</version.lettuce>
        <!-- https://mvnrepository.com/artifact/org.springframework.session/spring-session-data-redis -->
        <version.spring-session-data-redis>3.3.3</version.spring-session-data-redis>
<!--        &lt;!&ndash; https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils &ndash;&gt;-->
<!--        <version.commons-beanutils>1.9.4</version.commons-beanutils>-->
<!--        &lt;!&ndash; https://mvnrepository.com/artifact/com.google.guava/guava &ndash;&gt;-->
<!--        <version.guava>31.0.1-jre</version.guava>-->
        <!-- https://mvnrepository.com/artifact/com.google.zxing/core -->
        <version.zxing>3.5.3</version.zxing>
        <!-- https://mvnrepository.com/artifact/org.apache.pdfbox/pdfbox -->
        <version.pdfbox>3.0.3</version.pdfbox>
        <!-- https://mvnrepository.com/artifact/org.apache.pdfbox/jbig2-imageio -->
        <version.jbig2-imageio>3.0.4</version.jbig2-imageio>
        <!-- https://mvnrepository.com/artifact/com.mob.push.sdk/mobpush-websdkv3-java -->
        <version.mobpush>2.0.3</version.mobpush>
        <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j -->
        <version.log4j>2.24.1</version.log4j>
        <!-- https://mvnrepository.com/artifact/org.slf4j/slf4j-api -->
        <!-- https://mvnrepository.com/artifact/org.slf4j/slf4j-log4j12 -->
        <!-- https://mvnrepository.com/artifact/org.slf4j/slf4j-reload4j -->
        <version.slf4j>2.0.16</version.slf4j>
        <!-- http://mvnrepository.com/artifact/eu.bitwalker/UserAgentUtils -->
        <version.UserAgentUtils>1.21</version.UserAgentUtils>
        <!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
        <version.poi>5.3.0</version.poi>
        <!-- https://mvnrepository.com/artifact/net.sourceforge.jexcelapi/jxl -->
        <version.jxl>2.6.12</version.jxl>
        <!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
        <version.dom4j>2.1.4</version.dom4j>
        <!-- https://mvnrepository.com/artifact/javax.servlet.jsp.jstl/jstl -->
        <version.jstl>1.2</version.jstl>
        <!-- https://mvnrepository.com/artifact/org.apache.taglibs/taglibs-standard-impl -->
        <version.taglibsStandard>1.2.5</version.taglibsStandard>
        <!-- https://mvnrepository.com/artifact/javax.servlet/javax.servlet-api -->
        <version.servlet-api>4.0.1</version.servlet-api>
<!--        &lt;!&ndash; https://mvnrepository.com/artifact/org.elasticsearch.client/elasticsearch-rest-high-level-client &ndash;&gt;-->
<!--        <version.elasticsearch>7.17.5</version.elasticsearch>-->
        <!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk18on -->
        <version.bcprov-jdk18on>1.79</version.bcprov-jdk18on>
        <!-- https://mvnrepository.com/artifact/fr.noop/subtitle -->
        <version.subtitle>0.9.0</version.subtitle>
        <!-- https://mvnrepository.com/artifact/com.github.jiangxincode/cpdetector -->
        <version.cpdetector>1.0.10</version.cpdetector>
        <!-- https://mvnrepository.com/artifact/com.github.wechatpay-apiv3/wechatpay-java -->
        <version.wechatpay>0.2.14</version.wechatpay>
        <!-- https://mvnrepository.com/artifact/com.github.vip-zpf/jave -->
        <version.jave>1.1.4</version.jave>
    </properties>
    <dependencies>
        <!-- https://mvnrepository.com/artifact/com.auth0/java-jwt -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>${version.jwt}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.thetransactioncompany/cors-filter -->
        <dependency>
            <groupId>com.thetransactioncompany</groupId>
            <artifactId>cors-filter</artifactId>
            <version>${version.cors-filter}</version>
        </dependency>
        <!-- http://mvnrepository.com/artifact/commons-io/commons-io -->
<!--        <dependency>-->
<!--            <groupId>commons-io</groupId>-->
<!--            <artifactId>commons-io</artifactId>-->
<!--            <version>2.5</version>-->
<!--        </dependency>-->
        <!-- http://mvnrepository.com/artifact/commons-fileupload/commons-fileupload -->
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>${version.commons-fileupload}</version>
        </dependency>
<!--        &lt;!&ndash; http://mvnrepository.com/artifact/commons-codec/commons-codec &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>commons-codec</groupId>-->
<!--            <artifactId>commons-codec</artifactId>-->
<!--            <version>1.10</version>-->
<!--        </dependency>-->
        <!-- http://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${version.commons-lang3}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-text -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>${version.commons-text}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.aspectj/aspectjtools -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjtools</artifactId>
            <version>${version.aspectj}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>${version.spring}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
            <version>${version.spring}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${version.spring}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework/spring-websocket -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-websocket</artifactId>
            <version>${version.spring}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework/spring-messaging -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
            <version>${version.spring}</version>
        </dependency>
        <!-- http://mvnrepository.com/artifact/org.mybatis/mybatis -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>${version.mybatis}</version>
        </dependency>
        <!-- http://mvnrepository.com/artifact/org.mybatis/mybatis-spring -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>${version.mybatis-spring}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.mysql/mysql-connector-j -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${version.mysql}</version>
        </dependency>
<!--        &lt;!&ndash; https://mvnrepository.com/artifact/com.mchange/c3p0 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.mchange</groupId>-->
<!--            <artifactId>c3p0</artifactId>-->
<!--            <version>${version.c3p0}</version>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/com.zaxxer/HikariCP -->
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>${version.hikari}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${version.jackson}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${version.jackson}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${version.jackson}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.module/jackson-module-jaxb-annotations -->
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jaxb-annotations</artifactId>
            <version>${version.jackson}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${version.httpclient}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>${version.httpclient}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient-cache</artifactId>
            <version>${version.httpclient}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-core -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${version.log4j}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.slf4j/slf4j-api -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${version.slf4j}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.slf4j/slf4j-reload4j -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-reload4j</artifactId>
            <version>${version.slf4j}</version>
        </dependency>
        <!-- http://mvnrepository.com/artifact/javax.transaction/jta -->
<!--        <dependency>-->
<!--            <groupId>javax.transaction</groupId>-->
<!--            <artifactId>jta</artifactId>-->
<!--            <version>1.1</version>-->
<!--        </dependency>-->
        <!-- http://mvnrepository.com/artifact/javax.mail/mail -->
<!--        <dependency>-->
<!--            <groupId>javax.mail</groupId>-->
<!--            <artifactId>mail</artifactId>-->
<!--            <version>1.4.7</version>-->
<!--        </dependency>-->
        <!-- http://mvnrepository.com/artifact/eu.bitwalker/UserAgentUtils -->
        <dependency>
            <groupId>eu.bitwalker</groupId>
            <artifactId>UserAgentUtils</artifactId>
            <version>${version.UserAgentUtils}</version>
        </dependency>
        <!-- http://mvnrepository.com/artifact/net.coobird/thumbnailator -->
<!--        <dependency>-->
<!--            <groupId>net.coobird</groupId>-->
<!--            <artifactId>thumbnailator</artifactId>-->
<!--            <version>0.4.8</version>-->
<!--        </dependency>-->
        <!-- http://mvnrepository.com/artifact/org.apache.ant/ant -->
<!--        <dependency>-->
<!--            <groupId>org.apache.ant</groupId>-->
<!--            <artifactId>ant</artifactId>-->
<!--            <version>1.9.9</version>-->
<!--        </dependency>-->
        <!-- http://mvnrepository.com/artifact/javax.servlet/javax.servlet-api -->
<!--        <dependency>-->
<!--            <groupId>javax.servlet</groupId>-->
<!--            <artifactId>javax.servlet-api</artifactId>-->
<!--            <version>3.1.0</version>-->
<!--        </dependency>-->
        <!-- http://mvnrepository.com/artifact/junit/junit -->
<!--        <dependency>-->
<!--            <groupId>junit</groupId>-->
<!--            <artifactId>junit</artifactId>-->
<!--            <version>4.12</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${version.spring}</version>
        </dependency>
        <!-- http://mvnrepository.com/artifact/org.mybatis.generator/mybatis-generator-core -->
<!--        <dependency>-->
<!--            <groupId>org.mybatis.generator</groupId>-->
<!--            <artifactId>mybatis-generator-core</artifactId>-->
<!--            <version>1.3.5</version>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/net.sf.json-lib/json-lib -->
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>${version.json-lib}</version>
            <classifier>jdk15</classifier>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.json/json -->
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>${version.json}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.alibaba.fastjson2/fastjson2 -->
        <!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${version.fastjson}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpcore -->
<!--        <dependency>-->
<!--            <groupId>org.apache.httpcomponents</groupId>-->
<!--            <artifactId>httpcore</artifactId>-->
<!--            <version>${version.http}</version>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/org.quartz-scheduler/quartz -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>${version.quartz}</version>
        </dependency>
        <!-- http://mvnrepository.com/artifact/org.hibernate/hibernate-core -->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>${version.hibernate}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.hibernate.common/hibernate-commons-annotations -->
        <dependency>
            <groupId>org.hibernate.common</groupId>
            <artifactId>hibernate-commons-annotations</artifactId>
            <version>${version.hibernate-commons-annotations}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.hibernate.validator/hibernate-validator -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${version.hibernateValidator}</version>
        </dependency>
<!--        &lt;!&ndash; https://mvnrepository.com/artifact/org.hibernate.javax.persistence/hibernate-jpa-2.1-api &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.hibernate.javax.persistence</groupId>-->
<!--            <artifactId>hibernate-jpa-2.1-api</artifactId>-->
<!--            <version>1.0.2.Final</version>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${version.commons-io}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/io.lettuce/lettuce-core -->
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
            <version>${version.lettuce}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.session/spring-session-data-redis -->
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
            <version>${version.spring-session-data-redis}</version>
        </dependency>
<!--        &lt;!&ndash; https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>commons-beanutils</groupId>-->
<!--            <artifactId>commons-beanutils</artifactId>-->
<!--            <version>${version.commons-beanutils}</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; https://mvnrepository.com/artifact/com.google.guava/guava &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.google.guava</groupId>-->
<!--            <artifactId>guava</artifactId>-->
<!--            <version>${version.guava}</version>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/com.google.zxing/core -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${version.zxing}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.google.zxing/javase -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>${version.zxing}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.pdfbox/pdfbox -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>${version.pdfbox}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox-tools</artifactId>
            <version>${version.pdfbox}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.pdfbox/jbig2-imageio -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>jbig2-imageio</artifactId>
            <version>${version.jbig2-imageio}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.mob.push.sdk/mobpush-websdkv3-java -->
        <dependency>
            <groupId>com.mob.push.sdk</groupId>
            <artifactId>mobpush-websdkv3-java</artifactId>
            <version>${version.mobpush}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${version.poi}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/net.sourceforge.jexcelapi/jxl -->
        <dependency>
            <groupId>net.sourceforge.jexcelapi</groupId>
            <artifactId>jxl</artifactId>
            <version>${version.jxl}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>${version.dom4j}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.taglibs/taglibs-standard-impl -->
        <dependency>
            <groupId>org.apache.taglibs</groupId>
            <artifactId>taglibs-standard-impl</artifactId>
            <version>${version.taglibsStandard}</version>
            <scope>runtime</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.taglibs/taglibs-standard-spec -->
        <dependency>
            <groupId>org.apache.taglibs</groupId>
            <artifactId>taglibs-standard-spec</artifactId>
            <version>${version.taglibsStandard}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/javax.servlet/javax.servlet-api -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>${version.servlet-api}</version>
            <scope>provided</scope>
        </dependency>

<!--        &lt;!&ndash; https://mvnrepository.com/artifact/org.elasticsearch/elasticsearch &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.elasticsearch</groupId>-->
<!--            <artifactId>elasticsearch</artifactId>-->
<!--            <version>${version.elasticsearch}</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; https://mvnrepository.com/artifact/org.elasticsearch.client/elasticsearch-rest-client &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.elasticsearch.client</groupId>-->
<!--            <artifactId>elasticsearch-rest-client</artifactId>-->
<!--            <version>${version.elasticsearch}</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; https://mvnrepository.com/artifact/org.elasticsearch.client/elasticsearch-rest-high-level-client &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.elasticsearch.client</groupId>-->
<!--            <artifactId>elasticsearch-rest-high-level-client</artifactId>-->
<!--            <version>${version.elasticsearch}</version>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk18on -->
        <!-- 解决默认jdk不支持PKCS7Padding的问题 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>${version.bcprov-jdk18on}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/fr.noop/subtitle -->
        <dependency>
            <groupId>fr.noop</groupId>
            <artifactId>subtitle</artifactId>
            <version>${version.subtitle}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.jiangxincode/cpdetector -->
        <dependency>
            <groupId>com.github.jiangxincode</groupId>
            <artifactId>cpdetector</artifactId>
            <version>${version.cpdetector}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.wechatpay-apiv3/wechatpay-java -->
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java</artifactId>
            <version>${version.wechatpay}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.vip-zpf/jave -->
        <!-- https://github.com/vip-zpf/jave.git -->
        <dependency>
            <groupId>com.github.vip-zpf</groupId>
            <artifactId>jave</artifactId>
            <version>${version.jave}</version>
        </dependency>
        <dependency>
            <groupId>com.icbc</groupId>
            <artifactId>hsm-software-share</artifactId>
            <version>1.0.5</version>
            <scope>system</scope>
            <systemPath>${basedir}/web/WEB-INF/lib/hsm-software-share-1.0.5.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.icbc</groupId>
            <artifactId>icbc-api-sdk-cop</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/web/WEB-INF/lib/icbc-api-sdk-cop.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.icbc</groupId>
            <artifactId>icbc-api-sdk-cop-io</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/web/WEB-INF/lib/icbc-api-sdk-cop-io.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>cn.com.infosec</groupId>
            <artifactId>icbc-ca</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/web/WEB-INF/lib/icbc-ca.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>cn.com.infosec</groupId>
            <artifactId>InfosecCrypto_Java1_02_JDK14</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/web/WEB-INF/lib/InfosecCrypto_Java1_02_JDK14+.jar</systemPath>
        </dependency>
    </dependencies>
<!--    <repositories>-->
<!--        &lt;!&ndash; https://github.com/xingePush/xinge-api-java.git &ndash;&gt;-->
<!--        &lt;!&ndash; https://mvnrepository.com/artifact/com.github.xingePush/xinge &ndash;&gt;-->
<!--&lt;!&ndash;        <version.xinge>1.2.1</version.xinge>&ndash;&gt;-->
<!--        &lt;!&ndash; https://github.com/xingePush/xinge-api-java.git &ndash;&gt;-->
<!--        &lt;!&ndash; https://mvnrepository.com/artifact/com.github.xingePush/xinge &ndash;&gt;-->
<!--&lt;!&ndash;        <dependency>&ndash;&gt;-->
<!--&lt;!&ndash;            <groupId>com.github.xingePush</groupId>&ndash;&gt;-->
<!--&lt;!&ndash;            <artifactId>xinge</artifactId>&ndash;&gt;-->
<!--&lt;!&ndash;            <version>${version.xinge}</version>&ndash;&gt;-->
<!--&lt;!&ndash;        </dependency>&ndash;&gt;-->
<!--        <repository>-->
<!--            <id>jcenter</id>-->
<!--&lt;!&ndash;            <url>http://jcenter.bintray.com/</url>&ndash;&gt;-->
<!--            <url>https://maven.aliyun.com/repository/jcenter</url>-->
<!--&lt;!&ndash;            <url>http://maven.aliyun.com/nexus/content/groups/public</url>&ndash;&gt;-->
<!--        </repository>-->
<!--        <repository>-->
<!--            <id>xingePush</id>-->
<!--            <url>https://raw.githubusercontent.com/xingePush/maven-repository/release/</url>-->
<!--        </repository>-->
<!--    </repositories>-->
    <pluginRepositories>
        <pluginRepository>
            <id>jcenter</id>
            <url>https://maven.aliyun.com/repository/jcenter</url>
<!--            <url>http://jcenter.bintray.com/</url>-->
        </pluginRepository>
    </pluginRepositories>
    <build>
        <defaultGoal>clean install</defaultGoal>
        <finalName>${project.artifactId}</finalName>
        <sourceDirectory>${basedir}/src/main/java</sourceDirectory>
        <outputDirectory>${basedir}/out/production/${project.artifactId}</outputDirectory>
        <directory>${basedir}/out/${project.artifactId}</directory>
        <resources>
            <resource>
                <directory>${basedir}/resources</directory>
            </resource>
            <resource>
                <directory>${basedir}/src/main/java</directory>
                <includes>
                    <include>cn/sphd/miners/modules/**/mapper/xml/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <!-- http://mvnrepository.com/artifact/org.apache.maven.plugins/maven-compiler-plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
<!--                        <arg>-verbose</arg>-->
                        <arg>-Xlint:unchecked</arg>
                        <arg>-Xlint:deprecation </arg>
<!--                        <arg>-bootclasspath</arg>-->
<!--                        <arg>${env.JAVA_HOME}/jre/lib/rt.jar:${env.JAVA_HOME}/jre/lib/jce.jar</arg>-->
<!--                        <arg>-extdirs</arg>-->
<!--                        <arg>${basedir}/web/WEB-INF/lib/</arg>-->
                    </compilerArgs>
                </configuration>
            </plugin>
            <!-- https://mvnrepository.com/artifact/org.apache.maven.plugins/maven-war-plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <webappDirectory>${project.build.directory}/${project.build.finalName}</webappDirectory>
                    <warSourceDirectory>web</warSourceDirectory>
                    <warSourceExcludes>**/node_modules/**</warSourceExcludes>
                    <outputDirectory>
                        ${project.build.directory}
                    </outputDirectory>
                </configuration>
            </plugin>
            <!-- https://mvnrepository.com/artifact/org.apache.maven.plugins/maven-eclipse-plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
                <version>2.10</version>
                <configuration>
                    <wtpversion>2.0</wtpversion>
                </configuration>
            </plugin>
            <!-- https://mvnrepository.com/artifact/org.apache.maven.plugins/maven-idea-plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-idea-plugin</artifactId>
                <version>2.2.1</version>
            </plugin>
            <plugin>
                <!-- https://mvnrepository.com/artifact/com.ly.smart-doc/smart-doc -->
                <groupId>com.ly.smart-doc</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <configFile>./resources/smart-doc.json</configFile>
                    <projectName>${project.description}</projectName>
                    <includes>
                        <!-- 使用了mybatis-plus的Page分页需要include所使用的源码包 -->
<!--                        <include>cn.sphd.miners.common.utils.PagePlugin</include>-->
                        <!-- 使用了mybatis-plus的IPage分页需要include mybatis-plus-core-->
<!--                        <include>com.mybatis:mybatis</include>-->
                        <!-- 使用了jpa的分页需要include所使用的源码包 -->
<!--                        <include>org.springframework.data:spring-data-commons</include>-->
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <!--如果不需要在执行编译时启动smart-doc，则将phase注释掉-->
                        <phase>compile</phase>
                        <goals>
                            <!--smart-doc提供了html、openapi、markdown等goal，可按需配置-->
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>